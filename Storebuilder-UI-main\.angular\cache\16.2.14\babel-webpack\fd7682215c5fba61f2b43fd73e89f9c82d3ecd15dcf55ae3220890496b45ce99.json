{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from \"@angular/forms\";\nimport { environment } from \"@environments/environment\";\nimport { CountryISO, PhoneNumberFormat, SearchCountryField } from 'ngx-intl-tel-input-gg';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/dialog\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/checkbox\";\nimport * as i8 from \"ngx-intl-tel-input-gg\";\nfunction NotifyModalComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"span\", 6);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"notifyMeDetails.notifyMeVia\"));\n  }\n}\nfunction NotifyModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NotifyModalComponent_ng_template_1_div_0_Template, 4, 3, \"div\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayModal);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction NotifyModalComponent_div_2_ngx_intl_tel_input_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-intl-tel-input\", 18);\n    i0.ɵɵlistener(\"countryChange\", function NotifyModalComponent_div_2_ngx_intl_tel_input_12_Template_ngx_intl_tel_input_countryChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.countryChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"customPlaceholder\", ctx_r4.phoneNumberlabel);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r4.phoneLength)(\"numberFormat\", ctx_r4.PhoneNumberFormat.International)(\"phoneValidation\", false)(\"searchCountryField\", i0.ɵɵpureFunction2(13, _c0, ctx_r4.SearchCountryField.Iso2, ctx_r4.SearchCountryField.Name))(\"searchCountryFlag\", false)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r4.CustomCountryISO)(\"separateDialCode\", true)(\"onlyCountries\", ctx_r4.predefinedCountries);\n  }\n}\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction NotifyModalComponent_div_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 9)(2, \"div\", 10)(3, \"p-checkbox\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function NotifyModalComponent_div_2_div_13_Template_p_checkbox_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.isEmailChecked = $event);\n    })(\"ngModelChange\", function NotifyModalComponent_div_2_div_13_Template_p_checkbox_ngModelChange_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.isChangeOption(\"email\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"label\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", ctx_r5.isEmailChecked)(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 4, \"notifyMeDetails.byEmail\"));\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    opacity: a0\n  };\n};\nfunction NotifyModalComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"p-checkbox\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function NotifyModalComponent_div_2_Template_p_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.isNumberChecked = $event);\n    })(\"ngModelChange\", function NotifyModalComponent_div_2_Template_p_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.isChangeOption(\"number\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"label\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 13)(10, \"div\", 14)(11, \"form\", 15);\n    i0.ɵɵtemplate(12, NotifyModalComponent_div_2_ngx_intl_tel_input_12_Template, 1, 16, \"ngx-intl-tel-input\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, NotifyModalComponent_div_2_div_13_Template, 8, 7, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"binary\", true)(\"ngModel\", ctx_r1.isNumberChecked)(\"ngModelOptions\", i0.ɵɵpureFunction0(10, _c1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 8, \"notifyMeDetails.byNumber\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.notifyForm)(\"ngStyle\", i0.ɵɵpureFunction1(11, _c2, !ctx_r1.isNumberChecked ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.predefinedCountries.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEmailExist);\n  }\n}\nfunction NotifyModalComponent_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NotifyModalComponent_ng_template_3_div_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onClick());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.isNumberChecked && !ctx_r14.numberValidator() || !ctx_r14.isNumberChecked && !ctx_r14.isEmailChecked)(\"label\", ctx_r14.sumbitText);\n  }\n}\nfunction NotifyModalComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, NotifyModalComponent_ng_template_3_div_0_Template, 2, 2, \"div\", 20);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NotifyModalComponent_ng_template_3_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onClose());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.displayModal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.closeText);\n  }\n}\nconst _c3 = function () {\n  return {\n    width: \"30rem\"\n  };\n};\nconst _c4 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class NotifyModalComponent {\n  constructor(translateService, fb, cd) {\n    this.translateService = translateService;\n    this.fb = fb;\n    this.cd = cd;\n    this.displayModal = false;\n    this.message = '';\n    this.type = '';\n    this.submit = new EventEmitter();\n    this.close = new EventEmitter();\n    this.sumbitText = this.translateService.instant('notifyMeDetails.notifyAvaliable');\n    this.closeText = this.translateService.instant('notifyMeDetails.close');\n    this.predefinedCountries = [];\n    this.PhoneNumberFormat = PhoneNumberFormat;\n    this.phoneNumberlabel = 'X';\n    this.SearchCountryField = SearchCountryField;\n    this.isNumberChecked = false;\n    this.isEmailChecked = false;\n    this.isEmailExist = false;\n    this.phoneLength = 14;\n    this.createForm();\n    this.intializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes) {\n      if (!this.displayModal) {\n        this.onClose();\n      }\n    }\n  }\n  ngOnInit() {}\n  onClick() {\n    this.submit.emit(this.notifyForm.getRawValue());\n  }\n  onClose() {\n    this.isNumberChecked = false;\n    this.isEmailChecked = false;\n    let tenantId = JSON.parse(localStorage.getItem('tenantId') ?? '');\n    if (tenantId) {\n      this.selectedTenant = tenantId;\n    }\n    this.notifyForm.reset();\n    this.close.emit(true);\n  }\n  isChangeOption(type) {\n    if (type === 'number') {\n      if (!this.isNumberChecked) {\n        this.notifyForm.controls['phone'].disable();\n        this.notifyForm.controls['phone'].setValidators([]);\n      } else {\n        this.notifyForm.controls['phone'].enable();\n        this.notifyForm.controls['phone'].setValidators([Validators.required]);\n        this.notifyForm.controls['phone'].updateValueAndValidity();\n      }\n    } else {\n      if (this.isEmailChecked) {\n        const profile = JSON.parse(localStorage.getItem('profile') ?? '');\n        if (profile?.email) {\n          this.notifyForm.controls['email'].setValue(profile.email);\n          this.isEmailExist = true;\n        }\n      } else {\n        this.notifyForm.controls['email'].setValue('');\n        //this.isEmailExist = false;\n      }\n    }\n  }\n\n  numberValidator() {\n    let number = this.notifyForm.controls['phone'].value?.number;\n    if (this.selectedTenant && number) {\n      number = number.replace(/\\s/g, '');\n      number = number.replace(/[^0-9.]/g, '');\n      this.notifyForm.controls['phone'].value.number = number;\n      // this.notifyForm.controls['phone'].setValue(number);\n      if (number.length == this.phoneLength) {\n        return true;\n        // if (this.selectedTenant == 1) {\n        //   let allowedUgandaNumber: string[]  = ['77', '73', '75', '78', '70','76'];\n        //   let firstDigit = number.substring(0,2)\n        //   return allowedUgandaNumber.includes(firstDigit)\n        // }else if (this.selectedTenant == 4) {\n        //   return number.charAt(0) === '0'\n        // }else if (this.selectedTenant == 2) {\n        //   return true;\n        // }\n      }\n    }\n\n    return false;\n  }\n  countryChange(data) {\n    this.selectedTenant = 3;\n    if (data.iso2 === 'ug') {\n      this.selectedTenant = 1;\n      this.phoneLength = 9;\n    } else if (data.iso2 === 'gh') {\n      this.selectedTenant = 2;\n      this.phoneLength = 9;\n    } else if (data.iso2 === 'ci') {\n      this.selectedTenant = 4;\n      this.phoneLength = 10;\n    }\n    this.numberValidator();\n  }\n  createForm() {\n    this.notifyForm = this.fb.group({\n      phone: new FormControl(null),\n      email: new FormControl('')\n    });\n  }\n  intializeData() {\n    if (!this.isNumberChecked) {\n      this.notifyForm.controls['phone'].disable();\n    }\n    if (environment.isStoreCloud) {\n      this.predefinedCountries = [{\n        name: 'Egypt',\n        alpha2Code: 'EG',\n        alpha3Code: '',\n        numericCode: '818',\n        callingCode: '+20',\n        maxLength: 10\n      }];\n      this.selectedCountry = {\n        name: 'Egypt',\n        alpha2Code: 'EG',\n        itemCode: 'eg',\n        alpha3Code: '',\n        numericCode: '818',\n        callingCode: '+20',\n        maxLength: 10\n      };\n    } else {\n      this.predefinedCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n      let tenantId = JSON.parse(localStorage.getItem('tenantId') ?? '');\n      if (tenantId) {\n        this.selectedTenant = tenantId;\n        if (tenantId === 1) {\n          this.selectedCountry = this.predefinedCountries[0];\n        } else if (tenantId === 2) {\n          this.selectedCountry = this.predefinedCountries[1];\n        } else if (tenantId === 4) {\n          this.selectedCountry = this.predefinedCountries[2];\n        }\n        let length = JSON.parse(localStorage.getItem('PhoneLength') ?? '');\n        if (length) {\n          this.phoneLength = length;\n          for (var i = 1; i < parseInt(this.phoneLength, 0); i++) {\n            this.phoneNumberlabel = this.phoneNumberlabel + 'X';\n          }\n        }\n        this.CustomCountryISO = this.selectedCountry;\n      }\n    }\n  }\n  ngOnDestroy() {}\n  static #_ = this.ɵfac = function NotifyModalComponent_Factory(t) {\n    return new (t || NotifyModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotifyModalComponent,\n    selectors: [[\"app-notify-modal\"]],\n    inputs: {\n      displayModal: \"displayModal\",\n      message: \"message\",\n      type: \"type\",\n      isEmailExist: \"isEmailExist\"\n    },\n    outputs: {\n      submit: \"submit\",\n      close: \"close\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 12,\n    consts: [[1, \"notifyModal\", 3, \"visible\", \"breakpoints\", \"closable\", \"blockScroll\", \"showHeader\", \"draggable\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"header\"], [\"class\", \"notifyModal__body-container\", 4, \"ngIf\"], [\"pTemplate\", \"footer\"], [\"class\", \"inline-flex align-items-center justify-content-center gap-2\", 4, \"ngIf\"], [1, \"inline-flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\"], [1, \"notifyModal__title\", \"font-bold\", \"white-space-nowrap\"], [1, \"notifyModal__body-container\"], [1, \"d-flex\", \"align-items-center\"], [1, \"d-inline-flex\", \"notifyModal__body-container__checkbox-container\"], [1, \"custom-checkbox\"], [\"inputId\", \"binary\", 1, \"notifyModal__body-container__checkbox-container__checkbox\", 3, \"binary\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"d-inline-flex\", \"notifyModal__body-container__primary-text\", 2, \"width\", \"90%\"], [1, \"d-flex\", \"align-items-center\", \"mt-2\"], [1, \"d-inline-flex\", \"w-100\"], [1, \"w-100\", 3, \"formGroup\", \"ngStyle\"], [\"formControlName\", \"phone\", \"name\", \"phone\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"maxLength\", \"numberFormat\", \"phoneValidation\", \"searchCountryField\", \"searchCountryFlag\", \"selectFirstCountry\", \"selectedCountryISO\", \"separateDialCode\", \"onlyCountries\", \"customPlaceholder\", \"countryChange\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center mt-4 mb-5\", 4, \"ngIf\"], [\"formControlName\", \"phone\", \"name\", \"phone\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"maxLength\", \"numberFormat\", \"phoneValidation\", \"searchCountryField\", \"searchCountryFlag\", \"selectFirstCountry\", \"selectedCountryISO\", \"separateDialCode\", \"onlyCountries\", \"customPlaceholder\", \"countryChange\"], [1, \"d-flex\", \"align-items-center\", \"mt-4\", \"mb-5\"], [\"class\", \"d-flex\", 4, \"ngIf\"], [1, \"d-flex\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-2\", \"width-100\", \"notifyModal__action-btn\", \"notifyModal__action-btn__close-btn\", 3, \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-2\", \"mt-3\", \"width-100\", \"notifyModal__action-btn\", \"notifyModal__action-btn__notify-btn\", 3, \"disabled\", \"label\", \"click\"]],\n    template: function NotifyModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function NotifyModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵtemplate(1, NotifyModalComponent_ng_template_1_Template, 1, 1, \"ng-template\", 1);\n        i0.ɵɵtemplate(2, NotifyModalComponent_div_2_Template, 14, 13, \"div\", 2);\n        i0.ɵɵtemplate(3, NotifyModalComponent_ng_template_3_Template, 3, 2, \"ng-template\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c3));\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(11, _c4))(\"closable\", false)(\"blockScroll\", true)(\"showHeader\", true)(\"draggable\", false)(\"modal\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.displayModal);\n      }\n    },\n    dependencies: [i3.PrimeTemplate, i4.Dialog, i5.ButtonDirective, i6.NgIf, i6.NgStyle, i7.Checkbox, i8.NgxIntlTelInputComponent, i8.NativeElementInjectorDirective, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName, i1.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.notifyModal__title[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: var(--medium-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n}\\n.notifyModal__body-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1.5rem 0rem 1.5rem;\\n}\\n.notifyModal__body-container__checkbox-container[_ngcontent-%COMP%] {\\n  width: 6%;\\n}\\n@media only screen and (max-width: 767px) {\\n  .notifyModal__body-container__checkbox-container[_ngcontent-%COMP%] {\\n    width: 8%;\\n  }\\n}\\n.notifyModal__body-container__checkbox-container__checkbox[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n}\\n.notifyModal__body-container__primary-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  text-align: center;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n}\\n.notifyModal__action-btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 30px; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n}\\n.notifyModal__action-btn__notify-btn[_ngcontent-%COMP%] {\\n  background: var(--colors-Main-Color, #204E6E) !important;\\n  border: none !important;\\n  color: var(--Gray-00, var(--colors-fff, #FFF)) !important;\\n}\\n.notifyModal__action-btn__close-btn[_ngcontent-%COMP%] {\\n  background: transparent !important;\\n  border: 1px solid var(--Secondary-100, #D5EDFD) !important;\\n  color: var(--colors-Main-Color, #204E6E) !important;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .iti--allow-dropdown .iti__flag-container {\\n  pointer-events: none !important;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .p-dialog .p-dialog-header {\\n  padding: 1.5rem 1.5rem 0rem 1.5rem;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .p-dialog .p-dialog-footer button {\\n  width: 100%;\\n  margin: 0;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .p-dialog-content {\\n  border-bottom: none !important;\\n  border-radius: 0px !important;\\n  padding: 0;\\n  overflow-y: visible;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .model img {\\n  width: 50px;\\n  height: 50px;\\n  margin-bottom: 30px;\\n  margin-top: 70px;\\n}\\n.notifyModal[_ngcontent-%COMP%]     .model p {\\n  color: #000;\\n  font-size: 18px;\\n  font-family: main-medium, sans-serif;\\n  margin-bottom: 114px;\\n  text-align: center;\\n  line-height: 25px;\\n  padding-right: 28px;\\n  padding-left: 28px;\\n}\\n\\n.custom-checkbox[_ngcontent-%COMP%]   .p-checkbox[_ngcontent-%COMP%]   .p-checkbox-box[_ngcontent-%COMP%] {\\n  border: 2px solid black !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .notifyModal[_ngcontent-%COMP%]  .p-dialog {\\n    top: 90px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FormControl", "Validators", "environment", "CountryISO", "PhoneNumberFormat", "SearchCountryField", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtemplate", "NotifyModalComponent_ng_template_1_div_0_Template", "ɵɵproperty", "ctx_r0", "displayModal", "ɵɵlistener", "NotifyModalComponent_div_2_ngx_intl_tel_input_12_Template_ngx_intl_tel_input_countryChange_0_listener", "$event", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "countryChange", "ɵɵpropertyInterpolate", "ctx_r4", "phoneNumberlabel", "phoneLength", "International", "ɵɵpureFunction2", "_c0", "Iso2", "Name", "CustomCountryISO", "predefinedCountries", "NotifyModalComponent_div_2_div_13_Template_p_checkbox_ngModelChange_3_listener", "_r9", "ctx_r8", "isEmailChecked", "ctx_r10", "isChangeOption", "ctx_r5", "ɵɵpureFunction0", "_c1", "NotifyModalComponent_div_2_Template_p_checkbox_ngModelChange_4_listener", "_r12", "ctx_r11", "isNumberChecked", "ctx_r13", "NotifyModalComponent_div_2_ngx_intl_tel_input_12_Template", "NotifyModalComponent_div_2_div_13_Template", "ctx_r1", "notifyForm", "ɵɵpureFunction1", "_c2", "length", "isEmailExist", "NotifyModalComponent_ng_template_3_div_0_Template_button_click_1_listener", "_r16", "ctx_r15", "onClick", "ctx_r14", "numberValidator", "sumbitText", "NotifyModalComponent_ng_template_3_div_0_Template", "NotifyModalComponent_ng_template_3_Template_button_click_2_listener", "_r18", "ctx_r17", "onClose", "ctx_r2", "closeText", "NotifyModalComponent", "constructor", "translateService", "fb", "cd", "message", "type", "submit", "close", "instant", "createForm", "intializeData", "ngOnChanges", "changes", "ngOnInit", "emit", "getRawValue", "tenantId", "JSON", "parse", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "reset", "controls", "disable", "setValidators", "enable", "required", "updateValueAndValidity", "profile", "email", "setValue", "number", "value", "replace", "data", "iso2", "group", "phone", "isStoreCloud", "name", "alpha2Code", "alpha3Code", "numericCode", "callingCode", "max<PERSON><PERSON><PERSON>", "selectedCountry", "itemCode", "Uganda", "Ghana", "CôteDIvoire", "i", "parseInt", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "FormBuilder", "ChangeDetectorRef", "_2", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NotifyModalComponent_Template", "rf", "ctx", "NotifyModalComponent_Template_p_dialog_visibleChange_0_listener", "NotifyModalComponent_ng_template_1_Template", "NotifyModalComponent_div_2_Template", "NotifyModalComponent_ng_template_3_Template", "ɵɵstyleMap", "_c3", "_c4"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\notify-modal\\notify-modal.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\notify-modal\\notify-modal.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectorRef,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnDestroy,\r\n  OnInit,\r\n  Output,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport {TranslateService} from \"@ngx-translate/core\";\r\nimport {FormBuilder, FormControl, FormGroup, Validators} from \"@angular/forms\";\r\nimport {environment} from \"@environments/environment\";\r\nimport {\r\n  CountryISO,\r\n  PhoneNumberFormat,\r\n  SearchCountryField,\r\n} from 'ngx-intl-tel-input-gg';\r\nimport {ConfigurationKeys} from \"@core/enums\";\r\n@Component({\r\n  selector: 'app-notify-modal',\r\n  templateUrl: './notify-modal.component.html',\r\n  styleUrls: ['./notify-modal.component.scss']\r\n})\r\nexport class NotifyModalComponent implements OnInit, OnDestroy {\r\n  @Input() displayModal: boolean = false\r\n  @Input() message: string = ''\r\n  @Input() type : string = ''\r\n  @Output() submit = new EventEmitter<boolean>();\r\n  @Output() close = new EventEmitter<boolean>();\r\n  sumbitText = this.translateService.instant('notifyMeDetails.notifyAvaliable');\r\n  closeText = this.translateService.instant('notifyMeDetails.close')\r\n  notifyForm: FormGroup;\r\n  predefinedCountries: any[] = [];\r\n  selectedCountry: any;\r\n  selectedTenant : number ;\r\n  PhoneNumberFormat = PhoneNumberFormat;\r\n  phoneNumberlabel: string = 'X';\r\n  SearchCountryField = SearchCountryField;\r\n  CustomCountryISO: any;\r\n  isNumberChecked : boolean = false;\r\n  isEmailChecked : boolean = false;\r\n  @Input() isEmailExist : boolean = false;\r\n  phoneLength: any = 14;\r\n  constructor(private translateService: TranslateService,private fb: FormBuilder, private cd : ChangeDetectorRef) {\r\n    this.createForm();\r\n    this.intializeData()\r\n\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n     if(changes){\r\n       if(!this.displayModal){\r\n         this.onClose()\r\n\r\n       }\r\n     }\r\n  }\r\n  ngOnInit(): void {\r\n\r\n  }\r\n  onClick() {\r\n    this.submit.emit(this.notifyForm.getRawValue())\r\n  }\r\n  onClose(){\r\n    this.isNumberChecked = false;\r\n    this.isEmailChecked = false;\r\n    let tenantId = JSON.parse(localStorage.getItem('tenantId') ?? '')\r\n    if(tenantId) {\r\n      this.selectedTenant = tenantId\r\n    }\r\n    this.notifyForm.reset()\r\n    this.close.emit(true);\r\n  }\r\n  isChangeOption(type : string){\r\n    if(type=== 'number'){\r\n      if(!this.isNumberChecked){\r\n        this.notifyForm.controls['phone'].disable()\r\n        this.notifyForm.controls['phone'].setValidators([]);\r\n      }else{\r\n        this.notifyForm.controls['phone'].enable()\r\n        this.notifyForm.controls['phone'].setValidators([Validators.required]);\r\n        this.notifyForm.controls['phone'].updateValueAndValidity()\r\n      }\r\n    }else{\r\n      if(this.isEmailChecked){\r\n        const profile = JSON.parse(localStorage.getItem('profile') ?? '')\r\n        if(profile?.email){\r\n          this.notifyForm.controls['email'].setValue(profile.email);\r\n          this.isEmailExist = true;\r\n\r\n        }\r\n      }else{\r\n        this.notifyForm.controls['email'].setValue('');\r\n        //this.isEmailExist = false;\r\n      }\r\n    }\r\n  }\r\n  numberValidator(){\r\n      let number = this.notifyForm.controls['phone'].value?.number\r\n      if(this.selectedTenant && number){\r\n        number = number.replace(/\\s/g, '')\r\n        number = number.replace(/[^0-9.]/g, '')\r\n        this.notifyForm.controls['phone'].value.number = number\r\n        // this.notifyForm.controls['phone'].setValue(number);\r\n         if(number.length == this.phoneLength){\r\n           return true;\r\n           // if (this.selectedTenant == 1) {\r\n           //   let allowedUgandaNumber: string[]  = ['77', '73', '75', '78', '70','76'];\r\n           //   let firstDigit = number.substring(0,2)\r\n           //   return allowedUgandaNumber.includes(firstDigit)\r\n           // }else if (this.selectedTenant == 4) {\r\n           //   return number.charAt(0) === '0'\r\n           // }else if (this.selectedTenant == 2) {\r\n           //   return true;\r\n           // }\r\n         }\r\n      }\r\n      return false;\r\n  }\r\n  countryChange(data : any){\r\n    this.selectedTenant = 3;\r\n    if (data.iso2 === 'ug') {\r\n      this.selectedTenant = 1;\r\n      this.phoneLength = 9;\r\n    } else if (data.iso2 === 'gh') {\r\n      this.selectedTenant = 2;\r\n      this.phoneLength = 9;\r\n    } else if (data.iso2 === 'ci') {\r\n      this.selectedTenant = 4;\r\n      this.phoneLength = 10;\r\n    }\r\n    this.numberValidator()\r\n  }\r\n  createForm(){\r\n    this.notifyForm = this.fb.group({\r\n      phone: new FormControl(null),\r\n      email: new FormControl(''),\r\n    });\r\n  }\r\n  intializeData(){\r\n    if(!this.isNumberChecked){\r\n      this.notifyForm.controls['phone'].disable()\r\n    }\r\n    if (environment.isStoreCloud) {\r\n      this.predefinedCountries = [\r\n        {\r\n          name: 'Egypt',\r\n          alpha2Code: 'EG',\r\n          alpha3Code: '',\r\n          numericCode: '818',\r\n          callingCode: '+20',\r\n          maxLength: 10,\r\n        },\r\n      ];\r\n      this.selectedCountry = {\r\n        name: 'Egypt',\r\n        alpha2Code: 'EG',\r\n        itemCode: 'eg',\r\n        alpha3Code: '',\r\n        numericCode: '818',\r\n        callingCode: '+20',\r\n        maxLength: 10,\r\n      };\r\n\r\n    } else {\r\n      this.predefinedCountries = [\r\n        CountryISO.Uganda,\r\n        CountryISO.Ghana,\r\n        CountryISO.CôteDIvoire,\r\n      ];\r\n      let tenantId = JSON.parse(localStorage.getItem('tenantId') ?? '')\r\n      if(tenantId){\r\n        this.selectedTenant = tenantId\r\n        if(tenantId === 1){\r\n          this.selectedCountry = this.predefinedCountries [0]\r\n        }else if(tenantId === 2){\r\n          this.selectedCountry = this.predefinedCountries [1]\r\n        }else if(tenantId === 4){\r\n          this.selectedCountry = this.predefinedCountries [2]\r\n        }\r\n        let length = JSON.parse(localStorage.getItem('PhoneLength')?? '')\r\n        if(length){\r\n          this.phoneLength = length;\r\n          for (var i = 1; i < parseInt(this.phoneLength, 0); i++) {\r\n            this.phoneNumberlabel = this.phoneNumberlabel + 'X';\r\n          }\r\n        }\r\n        this.CustomCountryISO = this.selectedCountry;\r\n      }\r\n    }\r\n  }\r\n  ngOnDestroy() {\r\n  }\r\n}\r\n", "<p-dialog [(visible)]=\"displayModal\" [style]=\"{ width: '30rem' }\" [breakpoints]=\"{ '1199px': '75vw', '575px': '90vw' }\" [closable]=\"false\"\r\n  [blockScroll]=\"true\" [showHeader]=\"true\" [draggable]=\"false\" [modal]=\"true\" class=\"notifyModal\">\r\n    <ng-template pTemplate=\"header\">\r\n      <div class=\"inline-flex align-items-center justify-content-center gap-2\" *ngIf=\"displayModal\">\r\n        <span class=\"notifyModal__title font-bold white-space-nowrap\">{{\"notifyMeDetails.notifyMeVia\" | translate}}</span>\r\n      </div>\r\n    </ng-template>\r\n    <div class=\"notifyModal__body-container\" *ngIf=\"displayModal\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <div class=\"d-inline-flex notifyModal__body-container__checkbox-container\">\r\n          <div class=\"custom-checkbox\">\r\n            <p-checkbox class=\"notifyModal__body-container__checkbox-container__checkbox\" [binary]=\"true\" inputId=\"binary\"  [(ngModel)]=\"isNumberChecked\" (ngModelChange)=\"isChangeOption('number')\"\r\n                        [ngModelOptions]=\"{standalone: true}\"></p-checkbox>\r\n          </div>\r\n        </div>\r\n        <div class=\"d-inline-flex notifyModal__body-container__primary-text\" style=\"width:90%\">\r\n          <label>{{\"notifyMeDetails.byNumber\" | translate}}</label>\r\n        </div>\r\n      </div>\r\n      <div class=\"d-flex align-items-center mt-2\">\r\n        <div class=\"d-inline-flex w-100\">\r\n          <form [formGroup]=\"notifyForm\" class=\"w-100\"\r\n                [ngStyle]=\"{opacity:!isNumberChecked? '0.5': '' }\"\r\n          >\r\n            <ngx-intl-tel-input\r\n              *ngIf=\"predefinedCountries.length > 0\"\r\n              [cssClass]=\"'custom contact-input-phone'\"\r\n              [enableAutoCountrySelect]=\"true\"\r\n              [enablePlaceholder]=\"true\"\r\n              [maxLength]=\"phoneLength\"\r\n              [numberFormat]=\"PhoneNumberFormat.International\"\r\n              [phoneValidation]=\"false\"\r\n              [searchCountryField]=\"[SearchCountryField.Iso2, SearchCountryField.Name]\"\r\n              [searchCountryFlag]=\"false\"\r\n              [selectFirstCountry]=\"false\"\r\n              [selectedCountryISO]=\"CustomCountryISO\"\r\n              [separateDialCode]=\"true\"\r\n              [onlyCountries]=\"predefinedCountries\"\r\n              customPlaceholder=\"{{ phoneNumberlabel}}\"\r\n              (countryChange)=\"countryChange($event)\"\r\n              formControlName=\"phone\" name=\"phone\"\r\n            ></ngx-intl-tel-input>\r\n          </form>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"d-flex align-items-center mt-4 mb-5\" *ngIf=\"isEmailExist\">\r\n        <div class=\"d-inline-flex notifyModal__body-container__checkbox-container\">\r\n          <div class=\"custom-checkbox\">\r\n            <p-checkbox class=\"notifyModal__body-container__checkbox-container__checkbox\" [binary]=\"true\" inputId=\"binary\"  [(ngModel)]=\"isEmailChecked\" (ngModelChange)=\"isChangeOption('email')\"\r\n                        [ngModelOptions]=\"{standalone: true}\"></p-checkbox>\r\n          </div>\r\n        </div>\r\n        <div class=\"d-inline-flex notifyModal__body-container__primary-text\" style=\"width:90%\">\r\n          <label>{{\"notifyMeDetails.byEmail\" | translate}}</label>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <ng-template pTemplate=\"footer\">\r\n      <div class=\"d-flex\" *ngIf=\"displayModal\">\r\n        <button (click)=\"onClick()\" class=\"p-field p-col-12 mb-2  mt-3 width-100 notifyModal__action-btn notifyModal__action-btn__notify-btn\"  pButton\r\n                [disabled]=\"(isNumberChecked  && !numberValidator())  ||\r\n               (!isNumberChecked && !isEmailChecked)\"\r\n                [label]=\"sumbitText\"\r\n                type=\"button\">\r\n\r\n        </button>\r\n      </div>\r\n      <div class=\"d-flex\">\r\n        <button (click)=\"onClose()\" class=\"p-field p-col-12 mb-2  width-100 notifyModal__action-btn notifyModal__action-btn__close-btn\"  pButton\r\n                [label]=\"closeText\"\r\n                type=\"button\">\r\n\r\n        </button>\r\n      </div>\r\n\r\n    </ng-template>\r\n</p-dialog>\r\n"], "mappings": "AAAA,SAGEA,YAAY,QAMP,eAAe;AAEtB,SAAqBC,WAAW,EAAaC,UAAU,QAAO,gBAAgB;AAC9E,SAAQC,WAAW,QAAO,2BAA2B;AACrD,SACEC,UAAU,EACVC,iBAAiB,EACjBC,kBAAkB,QACb,uBAAuB;;;;;;;;;;;;ICdxBC,EAAA,CAAAC,cAAA,aAA8F;IAC9BD,EAAA,CAAAE,MAAA,GAA6C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAApDH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,sCAA6C;;;;;IAD7GN,EAAA,CAAAO,UAAA,IAAAC,iDAAA,iBAEM;;;;IAFoER,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;;;;;IAqBtFX,EAAA,CAAAC,cAAA,6BAiBC;IAFCD,EAAA,CAAAY,UAAA,2BAAAC,sGAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAiBlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IAExCd,EAAA,CAAAG,YAAA,EAAqB;;;;IAHpBH,EAAA,CAAAqB,qBAAA,sBAAAC,MAAA,CAAAC,gBAAA,CAAyC;IAZzCvB,EAAA,CAAAS,UAAA,0CAAyC,0EAAAa,MAAA,CAAAE,WAAA,kBAAAF,MAAA,CAAAxB,iBAAA,CAAA2B,aAAA,kDAAAzB,EAAA,CAAA0B,eAAA,KAAAC,GAAA,EAAAL,MAAA,CAAAvB,kBAAA,CAAA6B,IAAA,EAAAN,MAAA,CAAAvB,kBAAA,CAAA8B,IAAA,kFAAAP,MAAA,CAAAQ,gBAAA,6CAAAR,MAAA,CAAAS,mBAAA;;;;;;;;;;;IAoBjD/B,EAAA,CAAAC,cAAA,cAAsE;IAGgDD,EAAA,CAAAY,UAAA,2BAAAoB,+EAAAlB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAC,MAAA,GAAAlC,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAe,MAAA,CAAAC,cAAA,GAAArB,MAAA;IAAA,EAA4B,2BAAAkB,+EAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAG,OAAA,GAAApC,EAAA,CAAAkB,aAAA;MAAA,OAAkBlB,EAAA,CAAAmB,WAAA,CAAAiB,OAAA,CAAAC,cAAA,CAAe,OAAO,CAAC;IAAA,EAAzC;IAC1FrC,EAAA,CAAAG,YAAA,EAAa;IAGnEH,EAAA,CAAAC,cAAA,cAAuF;IAC9ED,EAAA,CAAAE,MAAA,GAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IALwBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,UAAA,gBAAe,YAAA6B,MAAA,CAAAH,cAAA,oBAAAnC,EAAA,CAAAuC,eAAA,IAAAC,GAAA;IAKxFxC,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,kCAAyC;;;;;;;;;;;IA/CtDN,EAAA,CAAAC,cAAA,aAA8D;IAI0DD,EAAA,CAAAY,UAAA,2BAAA6B,wEAAA3B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAAwB,OAAA,CAAAC,eAAA,GAAA9B,MAAA;IAAA,EAA6B,2BAAA2B,wEAAA;MAAAzC,EAAA,CAAAe,aAAA,CAAA2B,IAAA;MAAA,MAAAG,OAAA,GAAA7C,EAAA,CAAAkB,aAAA;MAAA,OAAkBlB,EAAA,CAAAmB,WAAA,CAAA0B,OAAA,CAAAR,cAAA,CAAe,QAAQ,CAAC;IAAA,EAA1C;IAC3FrC,EAAA,CAAAG,YAAA,EAAa;IAGnEH,EAAA,CAAAC,cAAA,cAAuF;IAC9ED,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAG7DH,EAAA,CAAAC,cAAA,cAA4C;IAKtCD,EAAA,CAAAO,UAAA,KAAAuC,yDAAA,kCAiBsB;IACxB9C,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAO,UAAA,KAAAwC,0CAAA,kBAUM;IACR/C,EAAA,CAAAG,YAAA,EAAM;;;;IA9CgFH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,UAAA,gBAAe,YAAAuC,MAAA,CAAAJ,eAAA,oBAAA5C,EAAA,CAAAuC,eAAA,KAAAC,GAAA;IAKxFxC,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,mCAA0C;IAK3CN,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAS,UAAA,cAAAuC,MAAA,CAAAC,UAAA,CAAwB,YAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAAH,MAAA,CAAAJ,eAAA;IAIzB5C,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAS,UAAA,SAAAuC,MAAA,CAAAjB,mBAAA,CAAAqB,MAAA,KAAoC;IAqBKpD,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAS,UAAA,SAAAuC,MAAA,CAAAK,YAAA,CAAkB;;;;;;IAapErD,EAAA,CAAAC,cAAA,cAAyC;IAC/BD,EAAA,CAAAY,UAAA,mBAAA0C,0EAAA;MAAAtD,EAAA,CAAAe,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAqC,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAM3BzD,EAAA,CAAAG,YAAA,EAAS;;;;IALDH,EAAA,CAAAI,SAAA,GACqC;IADrCJ,EAAA,CAAAS,UAAA,aAAAiD,OAAA,CAAAd,eAAA,KAAAc,OAAA,CAAAC,eAAA,OAAAD,OAAA,CAAAd,eAAA,KAAAc,OAAA,CAAAvB,cAAA,CACqC,UAAAuB,OAAA,CAAAE,UAAA;;;;;;IAH/C5D,EAAA,CAAAO,UAAA,IAAAsD,iDAAA,kBAQM;IACN7D,EAAA,CAAAC,cAAA,cAAoB;IACVD,EAAA,CAAAY,UAAA,mBAAAkD,oEAAA;MAAA9D,EAAA,CAAAe,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAhE,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA6C,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAI3BjE,EAAA,CAAAG,YAAA,EAAS;;;;IAdUH,EAAA,CAAAS,UAAA,SAAAyD,MAAA,CAAAvD,YAAA,CAAkB;IAW7BX,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,UAAAyD,MAAA,CAAAC,SAAA,CAAmB;;;;;;;;;;;;;;AD9CnC,OAAM,MAAOC,oBAAoB;EAoB/BC,YAAoBC,gBAAkC,EAASC,EAAe,EAAUC,EAAsB;IAA1F,KAAAF,gBAAgB,GAAhBA,gBAAgB;IAA2B,KAAAC,EAAE,GAAFA,EAAE;IAAuB,KAAAC,EAAE,GAAFA,EAAE;IAnBjF,KAAA7D,YAAY,GAAY,KAAK;IAC7B,KAAA8D,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAY,EAAE;IACjB,KAAAC,MAAM,GAAG,IAAIlF,YAAY,EAAW;IACpC,KAAAmF,KAAK,GAAG,IAAInF,YAAY,EAAW;IAC7C,KAAAmE,UAAU,GAAG,IAAI,CAACU,gBAAgB,CAACO,OAAO,CAAC,iCAAiC,CAAC;IAC7E,KAAAV,SAAS,GAAG,IAAI,CAACG,gBAAgB,CAACO,OAAO,CAAC,uBAAuB,CAAC;IAElE,KAAA9C,mBAAmB,GAAU,EAAE;IAG/B,KAAAjC,iBAAiB,GAAGA,iBAAiB;IACrC,KAAAyB,gBAAgB,GAAW,GAAG;IAC9B,KAAAxB,kBAAkB,GAAGA,kBAAkB;IAEvC,KAAA6C,eAAe,GAAa,KAAK;IACjC,KAAAT,cAAc,GAAa,KAAK;IACvB,KAAAkB,YAAY,GAAa,KAAK;IACvC,KAAA7B,WAAW,GAAQ,EAAE;IAEnB,IAAI,CAACsD,UAAU,EAAE;IACjB,IAAI,CAACC,aAAa,EAAE;EAEtB;EACAC,WAAWA,CAACC,OAAsB;IAC/B,IAAGA,OAAO,EAAC;MACT,IAAG,CAAC,IAAI,CAACtE,YAAY,EAAC;QACpB,IAAI,CAACsD,OAAO,EAAE;;;EAIrB;EACAiB,QAAQA,CAAA,GAER;EACAzB,OAAOA,CAAA;IACL,IAAI,CAACkB,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAClC,UAAU,CAACmC,WAAW,EAAE,CAAC;EACjD;EACAnB,OAAOA,CAAA;IACL,IAAI,CAACrB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACT,cAAc,GAAG,KAAK;IAC3B,IAAIkD,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACjE,IAAGJ,QAAQ,EAAE;MACX,IAAI,CAACK,cAAc,GAAGL,QAAQ;;IAEhC,IAAI,CAACpC,UAAU,CAAC0C,KAAK,EAAE;IACvB,IAAI,CAACf,KAAK,CAACO,IAAI,CAAC,IAAI,CAAC;EACvB;EACA9C,cAAcA,CAACqC,IAAa;IAC1B,IAAGA,IAAI,KAAI,QAAQ,EAAC;MAClB,IAAG,CAAC,IAAI,CAAC9B,eAAe,EAAC;QACvB,IAAI,CAACK,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACC,OAAO,EAAE;QAC3C,IAAI,CAAC5C,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACE,aAAa,CAAC,EAAE,CAAC;OACpD,MAAI;QACH,IAAI,CAAC7C,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACG,MAAM,EAAE;QAC1C,IAAI,CAAC9C,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACE,aAAa,CAAC,CAACnG,UAAU,CAACqG,QAAQ,CAAC,CAAC;QACtE,IAAI,CAAC/C,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACK,sBAAsB,EAAE;;KAE7D,MAAI;MACH,IAAG,IAAI,CAAC9D,cAAc,EAAC;QACrB,MAAM+D,OAAO,GAAGZ,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACjE,IAAGS,OAAO,EAAEC,KAAK,EAAC;UAChB,IAAI,CAAClD,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACQ,QAAQ,CAACF,OAAO,CAACC,KAAK,CAAC;UACzD,IAAI,CAAC9C,YAAY,GAAG,IAAI;;OAG3B,MAAI;QACH,IAAI,CAACJ,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACQ,QAAQ,CAAC,EAAE,CAAC;QAC9C;;;EAGN;;EACAzC,eAAeA,CAAA;IACX,IAAI0C,MAAM,GAAG,IAAI,CAACpD,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACU,KAAK,EAAED,MAAM;IAC5D,IAAG,IAAI,CAACX,cAAc,IAAIW,MAAM,EAAC;MAC/BA,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAClCF,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACvC,IAAI,CAACtD,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACU,KAAK,CAACD,MAAM,GAAGA,MAAM;MACvD;MACC,IAAGA,MAAM,CAACjD,MAAM,IAAI,IAAI,CAAC5B,WAAW,EAAC;QACnC,OAAO,IAAI;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;;;IAGL,OAAO,KAAK;EAChB;EACAJ,aAAaA,CAACoF,IAAU;IACtB,IAAI,CAACd,cAAc,GAAG,CAAC;IACvB,IAAIc,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;MACtB,IAAI,CAACf,cAAc,GAAG,CAAC;MACvB,IAAI,CAAClE,WAAW,GAAG,CAAC;KACrB,MAAM,IAAIgF,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACf,cAAc,GAAG,CAAC;MACvB,IAAI,CAAClE,WAAW,GAAG,CAAC;KACrB,MAAM,IAAIgF,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACf,cAAc,GAAG,CAAC;MACvB,IAAI,CAAClE,WAAW,GAAG,EAAE;;IAEvB,IAAI,CAACmC,eAAe,EAAE;EACxB;EACAmB,UAAUA,CAAA;IACR,IAAI,CAAC7B,UAAU,GAAG,IAAI,CAACsB,EAAE,CAACmC,KAAK,CAAC;MAC9BC,KAAK,EAAE,IAAIjH,WAAW,CAAC,IAAI,CAAC;MAC5ByG,KAAK,EAAE,IAAIzG,WAAW,CAAC,EAAE;KAC1B,CAAC;EACJ;EACAqF,aAAaA,CAAA;IACX,IAAG,CAAC,IAAI,CAACnC,eAAe,EAAC;MACvB,IAAI,CAACK,UAAU,CAAC2C,QAAQ,CAAC,OAAO,CAAC,CAACC,OAAO,EAAE;;IAE7C,IAAIjG,WAAW,CAACgH,YAAY,EAAE;MAC5B,IAAI,CAAC7E,mBAAmB,GAAG,CACzB;QACE8E,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;OACZ,CACF;MACD,IAAI,CAACC,eAAe,GAAG;QACrBN,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,IAAI;QAChBM,QAAQ,EAAE,IAAI;QACdL,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE;OACZ;KAEF,MAAM;MACL,IAAI,CAACnF,mBAAmB,GAAG,CACzBlC,UAAU,CAACwH,MAAM,EACjBxH,UAAU,CAACyH,KAAK,EAChBzH,UAAU,CAAC0H,WAAW,CACvB;MACD,IAAIlC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;MACjE,IAAGJ,QAAQ,EAAC;QACV,IAAI,CAACK,cAAc,GAAGL,QAAQ;QAC9B,IAAGA,QAAQ,KAAK,CAAC,EAAC;UAChB,IAAI,CAAC8B,eAAe,GAAG,IAAI,CAACpF,mBAAmB,CAAE,CAAC,CAAC;SACpD,MAAK,IAAGsD,QAAQ,KAAK,CAAC,EAAC;UACtB,IAAI,CAAC8B,eAAe,GAAG,IAAI,CAACpF,mBAAmB,CAAE,CAAC,CAAC;SACpD,MAAK,IAAGsD,QAAQ,KAAK,CAAC,EAAC;UACtB,IAAI,CAAC8B,eAAe,GAAG,IAAI,CAACpF,mBAAmB,CAAE,CAAC,CAAC;;QAErD,IAAIqB,MAAM,GAAGkC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAG,EAAE,CAAC;QACjE,IAAGrC,MAAM,EAAC;UACR,IAAI,CAAC5B,WAAW,GAAG4B,MAAM;UACzB,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,QAAQ,CAAC,IAAI,CAACjG,WAAW,EAAE,CAAC,CAAC,EAAEgG,CAAC,EAAE,EAAE;YACtD,IAAI,CAACjG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,GAAG,GAAG;;;QAGvD,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAACqF,eAAe;;;EAGlD;EACAO,WAAWA,CAAA,GACX;EAAC,QAAAC,CAAA,G;qBAxKUvD,oBAAoB,EAAApE,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAA4H,iBAAA,CAAA5H,EAAA,CAAAiI,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB9D,oBAAoB;IAAA+D,SAAA;IAAAC,MAAA;MAAAzH,YAAA;MAAA8D,OAAA;MAAAC,IAAA;MAAArB,YAAA;IAAA;IAAAgF,OAAA;MAAA1D,MAAA;MAAAC,KAAA;IAAA;IAAA0D,QAAA,GAAAtI,EAAA,CAAAuI,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCxBjC7I,EAAA,CAAAC,cAAA,kBACkG;QADxFD,EAAA,CAAAY,UAAA,2BAAAmI,gEAAAjI,MAAA;UAAA,OAAAgI,GAAA,CAAAnI,YAAA,GAAAG,MAAA;QAAA,EAA0B;QAEhCd,EAAA,CAAAO,UAAA,IAAAyI,2CAAA,yBAIc;QACdhJ,EAAA,CAAAO,UAAA,IAAA0I,mCAAA,mBAkDM;QACNjJ,EAAA,CAAAO,UAAA,IAAA2I,2CAAA,yBAkBc;QAClBlJ,EAAA,CAAAG,YAAA,EAAW;;;QA7E0BH,EAAA,CAAAmJ,UAAA,CAAAnJ,EAAA,CAAAuC,eAAA,KAAA6G,GAAA,EAA4B;QAAvDpJ,EAAA,CAAAS,UAAA,YAAAqI,GAAA,CAAAnI,YAAA,CAA0B,gBAAAX,EAAA,CAAAuC,eAAA,KAAA8G,GAAA;QAOUrJ,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAS,UAAA,SAAAqI,GAAA,CAAAnI,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}