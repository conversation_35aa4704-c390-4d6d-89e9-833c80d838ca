<section class="category-products-page"
  [ngClass]="{
    'category-products-page': navbarData?.isActive,
    'hidden-navbar': !navbarData?.isActive,
    'mobile-layout': isMobileLayout
  }"
  *ngIf="!isBlank">
  
  <div class="breadcrumb m-0"
    [ngClass]="{'breadcrumb': navbarData?.isActive, 'hiddenNavbarBreadcrum': !navbarData?.isActive}">
    <p-breadcrumb [home]="home" [model]="breadItems"></p-breadcrumb>
  </div>

  <!-- Category Banner -->
  <div *ngIf="categoryBanner?.isBanner" class="category-banner">
    <img [src]="getBannerImages(categoryBanner?.desktopBanner || categoryBanner?.mobileBanner)" 
         class="category-banner__img"/>
  </div>

  <!-- Products list -->
  <div *ngIf="products && products.length">
    <div class="products-header">
      <div class="category-title font-size-22 bold-font">
        {{ category ? category.categoryName : categoryName }}
      </div>
    </div>

    <div *ngIf="products && products.length > 0" class="products-grid">
      <ng-container *ngFor="let product of products; let i = index">
        <a *ngIf="!product.isDisable" 
           class="product-card-link"
           [class.isLayoutTemplate]="!isLayoutTemplate">
          <app-mtn-product-card [currency]="currency" [product]="product" [categoryName]="categoryName"></app-mtn-product-card>
        </a>
      </ng-container>
    </div>
    
    <div class="spinner-product" *ngIf="showProductSpinner">
      <p-progressSpinner></p-progressSpinner>
    </div>
  </div>
</section>

<app-category-not-found *ngIf="!isBlank && products && products.length === 0"></app-category-not-found>
