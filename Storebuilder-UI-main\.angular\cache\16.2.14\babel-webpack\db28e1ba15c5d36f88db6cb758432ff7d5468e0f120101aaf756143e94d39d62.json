{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from \"@environments/environment\";\nimport { UserConsentType } from \"@core/enums/user\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/common\";\nfunction CookieModalComponent_div_0_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 10);\n  }\n}\nfunction CookieModalComponent_div_0_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 11);\n  }\n}\nfunction CookieModalComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 1)(2, \"div\", 2);\n    i0.ɵɵtemplate(3, <PERSON><PERSON>ModalComponent_div_0_img_3_Template, 1, 0, \"img\", 3);\n    i0.ɵɵtemplate(4, CookieModalComponent_div_0_img_4_Template, 1, 0, \"img\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 5)(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function CookieModalComponent_div_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.proceed());\n    });\n    i0.ɵɵtext(13, \" I accept \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CookieModalComponent_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.cancel());\n    });\n    i0.ɵɵtext(15, \" I do not accept \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConfig);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.cookieModalDetails.header);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.cookieModalDetails.message);\n  }\n}\nexport class CookieModalComponent {\n  bsModalRef;\n  userService;\n  cookieModalDetails;\n  scConfig = false;\n  submit = new EventEmitter();\n  constructor(bsModalRef, userService) {\n    this.bsModalRef = bsModalRef;\n    this.userService = userService;\n    this.scConfig = environment.isStoreCloud;\n  }\n  proceed() {\n    localStorage.setItem('save_cookie', 'true');\n    const data = {\n      consentType: UserConsentType.Cookie,\n      sessionId: localStorage.getItem('consumer-consent-sessionId') || '',\n      consent: true\n    };\n    this.userService.updateUserConsent(data).subscribe({\n      next: res => {}\n    });\n    this.bsModalRef.hide();\n    this.submit.emit(true);\n  }\n  cancel() {\n    localStorage.setItem('save_cookie', 'false');\n    const data = {\n      consentType: UserConsentType.Cookie,\n      sessionId: localStorage.getItem('consumer-consent-sessionId') || '',\n      consent: false\n    };\n    this.userService.updateUserConsent(data).subscribe({\n      next: res => {}\n    });\n    this.bsModalRef.hide();\n    this.submit.emit(false);\n  }\n  static ɵfac = function CookieModalComponent_Factory(t) {\n    return new (t || CookieModalComponent)(i0.ɵɵdirectiveInject(i1.BsModalRef), i0.ɵɵdirectiveInject(i2.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CookieModalComponent,\n    selectors: [[\"app-cookie-modal\"]],\n    inputs: {\n      cookieModalDetails: \"cookieModalDetails\"\n    },\n    outputs: {\n      submit: \"submit\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [1, \"modal-wrapper\", \"modal-body\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"cookie-popup\"], [1, \"cookie-popup__logo\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/momo-marketplace-footer-logo.png\", 4, \"ngIf\"], [\"class\", \"logo-red\", \"alt\", \"No Image\", \"src\", \"assets/icons/momo-marketplace-footer-logo-red.png\", 4, \"ngIf\"], [1, \"cookie-popup__header\"], [1, \"cookie-popup__message\"], [1, \"d-flex\", \"justify-content-center\", \"mt-4\"], [1, \"cookie-popup__buttons\", \"cookie-popup__buttons__accept-btn\", 3, \"click\"], [1, \"cookie-popup__buttons\", \"cookie-popup__buttons__reject-btn\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/momo-marketplace-footer-logo.png\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/momo-marketplace-footer-logo-red.png\", 1, \"logo-red\"]],\n    template: function CookieModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CookieModalComponent_div_0_Template, 16, 4, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.cookieModalDetails);\n      }\n    },\n    dependencies: [i3.NgIf],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .modal-header {\\n  border: 0;\\n}\\n  .modal-header mat-icon {\\n  color: #1D4C69;\\n}\\n\\n.modal-wrapper[_ngcontent-%COMP%] {\\n  padding: 45px;\\n  box-sizing: border-box;\\n}\\n\\n.cookie-popup[_ngcontent-%COMP%] {\\n  z-index: 99999;\\n}\\n.cookie-popup__logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.cookie-popup__logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n}\\n.cookie-popup__header[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  font-size: 24px;\\n  font-weight: 700;\\n  line-height: 29px;\\n  letter-spacing: 0;\\n  text-align: center;\\n  color: var(--main_bt_txtcolor);\\n}\\n.cookie-popup__message[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  font-size: 20px;\\n  font-weight: 400;\\n  line-height: 24px;\\n  letter-spacing: 0;\\n  text-align: center;\\n  color: #000000;\\n}\\n.cookie-popup__buttons[_ngcontent-%COMP%] {\\n  height: 43px;\\n  padding: 13px 24px;\\n  justify-content: center;\\n  align-items: center;\\n  width: 352px;\\n  gap: 8px;\\n  border-radius: 6px;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n  border: 2px solid var(--main_bt_txtcolor);\\n}\\n@media only screen and (max-width: 767px) {\\n  .cookie-popup__buttons[_ngcontent-%COMP%] {\\n    padding: 13px 0px;\\n  }\\n}\\n.cookie-popup__buttons__accept-btn[_ngcontent-%COMP%] {\\n  background: var(--main_bt_txtcolor);\\n  color: #FFF;\\n}\\n.cookie-popup__buttons__reject-btn[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  color: var(--main_bt_txtcolor);\\n  background: var(--main_bt_bgcolor);\\n}\\n\\n.logo-red[_ngcontent-%COMP%] {\\n  width: 120px !important;\\n  height: 56px !important;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}