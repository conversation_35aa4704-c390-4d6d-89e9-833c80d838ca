{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport { map } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ReviewsService {\n  http;\n  baseUrl;\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}`; //api/front-shop`\n  }\n\n  getProductReviews(id, params) {\n    return this.http.get(`${this.baseUrl}/Review/review/showReviews/${id}`, {\n      params\n    });\n  }\n  getProductRate(productsId) {\n    return this.http.post(`${this.baseUrl}/Review/Review/GetProductReviewRates`, productsId).pipe(map(review => {\n      return {\n        proReview: review.data\n      };\n    }));\n  }\n  getProductReviewRate(productId) {\n    return this.http.get(`${this.baseUrl}/Review/Review/GetProductDetailReviewRates/${productId}`).pipe(map(review => {\n      return {\n        proReview: review.data\n      };\n    }));\n  }\n  getProductReviewDetail(productId) {\n    return this.http.get(`${this.baseUrl}/Review/ReviewDetail/GetAllReviewByProduct/${productId}`).pipe(map(review => {\n      return {\n        proReview: review\n      };\n    }));\n  }\n  addProductReviews(model) {\n    return this.http.post(`${this.baseUrl}/Review/review/CreateReview`, model);\n  }\n  static ɵfac = function ReviewsService_Factory(t) {\n    return new (t || ReviewsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ReviewsService,\n    factory: ReviewsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}