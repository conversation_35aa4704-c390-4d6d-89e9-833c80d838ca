{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class LiveStreamService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}`;\n  }\n  getLiveMerchantData() {\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetLiveMerchants`);\n  }\n  getLiveStreamDetailById(shopId) {\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetStreamDetail/${shopId}`);\n  }\n}\nLiveStreamService.ɵfac = function LiveStreamService_Factory(t) {\n  return new (t || LiveStreamService)(i0.ɵɵinject(i1.HttpClient));\n};\nLiveStreamService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: LiveStreamService,\n  factory: LiveStreamService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}