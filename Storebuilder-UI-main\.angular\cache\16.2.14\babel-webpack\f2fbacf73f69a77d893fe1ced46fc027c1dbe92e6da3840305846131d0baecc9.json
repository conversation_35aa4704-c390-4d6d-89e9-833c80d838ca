{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { PaginatorModule } from \"primeng/paginator\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class OrdersModule {\n  static ɵfac = function OrdersModule_Factory(t) {\n    return new (t || OrdersModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: OrdersModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), TranslateModule, PaginatorModule, EmptyScreenComponent, BackButtonComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrdersModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule, PaginatorModule, EmptyScreenComponent, BackButtonComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IndexComponent", "RouterModule", "routes", "TranslateModule", "PaginatorModule", "EmptyScreenComponent", "BackButtonComponent", "OrdersModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\order\\orders\\orders.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { routes } from \"./routes\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {PaginatorModule} from \"primeng/paginator\";\r\nimport {EmptyScreenComponent} from \"@shared/components/empty-screen/empty-screen.component\";\r\nimport {BackButtonComponent} from \"@shared/components/back-button/back-button.component\";\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    TranslateModule,\r\n    PaginatorModule,\r\n    EmptyScreenComponent,\r\n    BackButtonComponent\r\n  ]\r\n})\r\nexport class OrdersModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,oBAAoB,QAAO,wDAAwD;AAC3F,SAAQC,mBAAmB,QAAO,sDAAsD;;;AAcxF,OAAM,MAAOC,YAAY;;qBAAZA,YAAY;EAAA;;UAAZA;EAAY;;cARrBR,YAAY,EACZE,YAAY,CAACO,QAAQ,CAACN,MAAM,CAAC,EAC7BC,eAAe,EACfC,eAAe,EACfC,oBAAoB,EACpBC,mBAAmB;EAAA;;;2EAGVC,YAAY;IAAAE,YAAA,GAXrBT,cAAc;IAAAU,OAAA,GAGdX,YAAY,EAAAY,EAAA,CAAAV,YAAA,EAEZE,eAAe,EACfC,eAAe,EACfC,oBAAoB,EACpBC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}