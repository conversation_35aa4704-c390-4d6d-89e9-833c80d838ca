{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class FooterService {\n  http = inject(HttpClient);\n  getAllFooterSectionsWithPages() {\n    return this.http.get(`${environment.apiEndPoint}/Tenant/ShopPages/GetSectionsWithPages`);\n  }\n  static ɵfac = function FooterService_Factory(t) {\n    return new (t || FooterService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: FooterService,\n    factory: FooterService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}