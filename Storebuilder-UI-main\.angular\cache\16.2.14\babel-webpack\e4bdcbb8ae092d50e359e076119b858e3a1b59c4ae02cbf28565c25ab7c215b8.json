{"ast": null, "code": "export class RefreshTokenViewModel {}", "map": {"version": 3, "names": ["RefreshTokenViewModel"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\auth.ts"], "sourcesContent": ["export class RefreshTokenViewModel {\r\n  AuthToken: string | undefined;\r\n  RefreshToken: string | undefined;\r\n\r\n\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}