{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/inputtext\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"ngx-intl-tel-input-gg\";\nimport * as i5 from \"primeng/dialog\";\nimport * as i6 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class AddressLabelComponent {\n  constructor() {\n    this.displayModal = false;\n    this.submit = new EventEmitter();\n    this.cancel = new EventEmitter();\n  }\n  closeModal() {\n    this.submit.emit(this.addAddress);\n  }\n  onCancel(event) {\n    this.cancel.emit(false);\n  }\n}\nAddressLabelComponent.ɵfac = function AddressLabelComponent_Factory(t) {\n  return new (t || AddressLabelComponent)();\n};\nAddressLabelComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddressLabelComponent,\n  selectors: [[\"app-address-label\"]],\n  inputs: {\n    displayModal: \"displayModal\"\n  },\n  outputs: {\n    submit: \"submit\",\n    cancel: \"cancel\"\n  },\n  decls: 18,\n  vars: 21,\n  consts: [[1, \"address-modal\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"onHide\", \"visibleChange\"], [1, \"address-label\"], [1, \"form-bg\"], [1, \"d-flex\"], [\"for\", \"label\"], [\"id\", \"label\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngModel\", \"ngModelChange\"], [1, \"grid\"], [1, \"col-12\", \"col-md-6\", \"mt-3\", \"address-label-btn\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"add-address-btn\", 3, \"label\", \"click\"], [1, \"col-12\", \"col-md-6\", \"mt-1\", \"button-address\", \"address-label-btn\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"defaultBtn\", \"w-100\", \"add-address-btn\", 3, \"label\", \"click\"]],\n  template: function AddressLabelComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p-dialog\", 0);\n      i0.ɵɵlistener(\"onHide\", function AddressLabelComponent_Template_p_dialog_onHide_0_listener() {\n        return ctx.closeModal();\n      })(\"visibleChange\", function AddressLabelComponent_Template_p_dialog_visibleChange_0_listener($event) {\n        return ctx.displayModal = $event;\n      });\n      i0.ɵɵelementStart(1, \"h2\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵpipe(3, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"label\", 4);\n      i0.ɵɵtext(7);\n      i0.ɵɵpipe(8, \"translate\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"div\", 3)(10, \"input\", 5);\n      i0.ɵɵlistener(\"ngModelChange\", function AddressLabelComponent_Template_input_ngModelChange_10_listener($event) {\n        return ctx.addAddress = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function AddressLabelComponent_Template_button_click_13_listener() {\n        return ctx.closeModal();\n      });\n      i0.ɵɵpipe(14, \"translate\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(15, \"div\", 9)(16, \"button\", 10);\n      i0.ɵɵlistener(\"click\", function AddressLabelComponent_Template_button_click_16_listener($event) {\n        return ctx.onCancel($event);\n      });\n      i0.ɵɵpipe(17, \"translate\");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(20, _c0))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 12, \"addressOtherModal.addressLabel\"), \" \");\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 14, \"addressOtherModal.labelName\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngModel\", ctx.addAddress);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(14, 16, \"addressOtherModal.add\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(17, 18, \"addressOtherModal.cancel\"));\n    }\n  },\n  dependencies: [i1.InputText, i2.DefaultValueAccessor, i2.NgControlStatus, i3.ButtonDirective, i2.NgModel, i4.NativeElementInjectorDirective, i5.Dialog, i6.TranslatePipe],\n  styles: [\".address-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 18px;\\n  color: #292D32;\\n  font-family: var(--medium-font);\\n  text-align: center;\\n}\\n\\n.address-modal[_ngcontent-%COMP%]     .p-dialog-content {\\n  border-radius: 8px;\\n  background: #F6F6F6;\\n  padding: 24px;\\n}\\n\\n.form-bg[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: white;\\n  width: 100%;\\n  padding: 16px;\\n  border-radius: 8px;\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%]:enabled:hover {\\n  border: none !important;\\n  width: 100%;\\n  box-shadow: none !important;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-weight: 300;\\n  color: #323232;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0px !important;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.defaultBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n  letter-spacing: 0px;\\n  color: var(--header_bgcolor);\\n  background: transparent;\\n  border: 1px solid var(--header_bgcolor);\\n  padding: 10px 20px;\\n  font-size: 15px;\\n  border-radius: 25px;\\n  width: 70%;\\n  outline: 0 none;\\n  font-family: var(--medium-font);\\n}\\n\\n.add-address-btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px !important;\\n  border-radius: 8px !important;\\n  text-transform: capitalize !important;\\n}\\n\\n.address-label-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.p-button[_ngcontent-%COMP%]:enabled:hover {\\n  background: transparent;\\n  color: var(--header_bgcolor);\\n  border: 1px solid var(--header_bgcolor);\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}