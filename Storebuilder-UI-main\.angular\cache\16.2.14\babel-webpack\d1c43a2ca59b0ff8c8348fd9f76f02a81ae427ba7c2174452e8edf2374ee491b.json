{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { isPlatformBrowser } from '@angular/common';\nimport { CountryISO, PhoneNumberFormat, SearchCountryField } from \"ngx-intl-tel-input-gg\";\nimport { TenantRecords } from \"@core/interface\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"@shared/modals/success-modal/success-modal.component\";\nimport * as i11 from \"@angular-magic/ngx-gp-autocomplete\";\nimport * as i12 from \"@angular/google-maps\";\nimport * as i13 from \"primeng/dropdown\";\nimport * as i14 from \"ngx-intl-tel-input-gg\";\nimport * as i15 from \"../modals/address-label/address-label.component\";\nconst _c0 = [\"placesRef\"];\nconst _c1 = [\"search\"];\nfunction AddressComponent_ng_container_0_map_marker_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"map-marker\", 52);\n    i0.ɵɵlistener(\"mapDrag\", function AddressComponent_ng_container_0_map_marker_13_Template_map_marker_mapDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.markerDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marker_r16 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r5.markerOptions)(\"position\", marker_r16.position);\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r19.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r20.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r21.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const car_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", car_r22.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"ErrorMessages.phoneNumberIsUnvalid\"), \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r24.selectedAddressType === \"Home\" ? \"assets/icons/mobile-icons/home-address-white.svg\" : \"assets/icons/mobile-icons/home-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r25.selectedAddressType === \"Work\" ? \"assets/icons/mobile-icons/work-address-white.svg\" : \"assets/icons/mobile-icons/work-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_92_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const addressType_r23 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.selectAddressType(addressType_r23.name));\n    });\n    i0.ɵɵtemplate(1, AddressComponent_ng_container_0_div_92_img_1_Template, 1, 1, \"img\", 57);\n    i0.ɵɵtemplate(2, AddressComponent_ng_container_0_div_92_img_2_Template, 1, 1, \"img\", 58);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const addressType_r23 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r13.selectedAddressType === addressType_r23.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r23.name === \"Home\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r23.name === \"Work\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", addressType_r23.name, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_94_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onSubmit());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.confirmAddress\"));\n  }\n}\nfunction AddressComponent_ng_container_0_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_98_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.Update());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r15.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.updateAddress\"));\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"form-input-error\": a0\n  };\n};\nconst _c3 = function (a0) {\n  return {\n    \"border-bottom\": a0\n  };\n};\nconst _c4 = function () {\n  return {\n    width: \"100%\",\n    border: \"none\",\n    background: \"none\",\n    outline: \"none\",\n    \"box-shadow\": \"none\"\n  };\n};\nconst _c5 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction AddressComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 3);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"img\", 7);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onBack());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"google-map\", 11);\n    i0.ɵɵlistener(\"mapClick\", function AddressComponent_ng_container_0_Template_google_map_mapClick_12_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.mapClicked($event));\n    })(\"mapInitialized\", function AddressComponent_ng_container_0_Template_google_map_mapInitialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.mapInitialize($event));\n    });\n    i0.ɵɵtemplate(13, AddressComponent_ng_container_0_map_marker_13_Template, 1, 2, \"map-marker\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"form\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"label\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 15);\n    i0.ɵɵelement(21, \"input\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19)(24, \"div\", 20);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelement(27, \"br\");\n    i0.ɵɵelementStart(28, \"p-dropdown\", 21, 22);\n    i0.ɵɵlistener(\"onChange\", function AddressComponent_ng_container_0_Template_p_dropdown_onChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.filterCitiesByRegion($event.value));\n    });\n    i0.ɵɵtemplate(30, AddressComponent_ng_container_0_ng_template_30_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(31, AddressComponent_ng_container_0_ng_template_31_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 18)(33, \"div\", 19)(34, \"div\", 25);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelement(37, \"br\");\n    i0.ɵɵelementStart(38, \"p-dropdown\", 26, 27);\n    i0.ɵɵtemplate(40, AddressComponent_ng_container_0_ng_template_40_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(41, AddressComponent_ng_container_0_ng_template_41_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 14)(43, \"div\", 15)(44, \"label\", 28);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 15)(48, \"input\", 29);\n    i0.ɵɵlistener(\"input\", function AddressComponent_ng_container_0_Template_input_input_48_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.getCoordinates());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 14)(50, \"div\", 15)(51, \"label\", 28);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 15);\n    i0.ɵɵelement(55, \"input\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 14)(57, \"div\", 15)(58, \"label\", 31);\n    i0.ɵɵtext(59);\n    i0.ɵɵpipe(60, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 15);\n    i0.ɵɵelement(62, \"input\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 14)(64, \"div\", 15)(65, \"label\", 33);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 15);\n    i0.ɵɵelement(69, \"input\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 14)(71, \"div\", 15)(72, \"label\", 35);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 36)(76, \"ngx-intl-tel-input\", 37);\n    i0.ɵɵlistener(\"countryChange\", function AddressComponent_ng_container_0_Template_ngx_intl_tel_input_countryChange_76_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onCountryChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(77, AddressComponent_ng_container_0_span_77_Template, 3, 3, \"span\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 14)(79, \"div\", 15)(80, \"label\", 39);\n    i0.ɵɵtext(81);\n    i0.ɵɵpipe(82, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 15);\n    i0.ɵɵelement(84, \"input\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 41)(86, \"div\", 42)(87, \"div\", 43)(88, \"div\", 44);\n    i0.ɵɵtext(89);\n    i0.ɵɵpipe(90, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 45);\n    i0.ɵɵtemplate(92, AddressComponent_ng_container_0_div_92_Template, 4, 5, \"div\", 46);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(93, \"div\", 47);\n    i0.ɵɵtemplate(94, AddressComponent_ng_container_0_div_94_Template, 3, 4, \"div\", 48);\n    i0.ɵɵelementStart(95, \"div\", 49)(96, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_Template_button_click_96_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.setAsDefault());\n    });\n    i0.ɵɵpipe(97, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(98, AddressComponent_ng_container_0_div_98_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_14_0;\n    let tmp_20_0;\n    let tmp_25_0;\n    let tmp_40_0;\n    let tmp_41_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 51, \"multipleAddress.completeAddress\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"center\", ctx_r0.center)(\"zoom\", ctx_r0.zoom)(\"options\", ctx_r0.mapOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.addressForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(19, 53, \"addingAddress.country\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c2, ((tmp_7_0 = ctx_r0.addressForm.get(\"country\")) == null ? null : tmp_7_0.hasError(\"required\")) && ((tmp_7_0 = ctx_r0.addressForm.get(\"country\")) == null ? null : tmp_7_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(77, _c3, ((tmp_8_0 = ctx_r0.addressForm.get(\"region.regionId\")) == null ? null : tmp_8_0.hasError(\"required\")) && ((tmp_8_0 = ctx_r0.addressForm.get(\"region.regionId\")) == null ? null : tmp_8_0.touched) ? \"1px solid red\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.region);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 55, \"addingAddress.region\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(79, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r0.allRegionList)(\"filter\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(80, _c3, ((tmp_14_0 = ctx_r0.addressForm.get(\"city\")) == null ? null : tmp_14_0.hasError(\"required\")) && ((tmp_14_0 = ctx_r0.addressForm.get(\"city\")) == null ? null : tmp_14_0.touched) ? \"1px solid red\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 57, \"addingAddress.city\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(82, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r0.filteredCities)(\"filter\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(46, 59, \"addingAddress.addressDetails\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(83, _c2, (tmp_20_0 = ctx_r0.addressForm.get(\"streetAddress\")) == null ? null : tmp_20_0.hasError(\"required\")));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(53, 61, \"addingAddress.otherAddressSecond\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(60, 63, \"addingAddress.post-code\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r0.addressForm.value.postcode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(67, 65, \"addingAddress.landMark\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(85, _c2, ((tmp_25_0 = ctx_r0.addressForm.get(\"landMark\")) == null ? null : tmp_25_0.hasError(\"required\")) && ((tmp_25_0 = ctx_r0.addressForm.get(\"landMark\")) == null ? null : tmp_25_0.touched)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(74, 67, \"addingAddress.phone\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r0.phoneInputLength)(\"numberFormat\", ctx_r0.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r0.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(87, _c5, ctx_r0.SearchCountryField.Iso2, ctx_r0.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r0.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r0.customPlaceHolder)(\"ngClass\", i0.ɵɵpureFunction1(90, _c2, ((tmp_40_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.hasError(\"required\")) || ((tmp_40_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.hasError(\"validatePhoneNumber\"))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_41_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.hasError(\"validatePhoneNumber\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(82, 69, \"addingAddress.instructions\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(90, 71, \"addingAddress.addressLabel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.addressLabelList);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.addressService.chosenAddress === null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.addressService.chosenAddress === null || ctx_r0.addressService.chosenAddress.isDefault === true)(\"label\", i0.ɵɵpipeBind1(97, 73, \"addingAddress.defaultAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id && ctx_r0.id !== \"add-address\");\n  }\n}\nfunction AddressComponent_ng_template_1_h2_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"addingAddress.addAddress\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_h2_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"addingAddress.updateAddressTitle\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_map_marker_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"map-marker\", 52);\n    i0.ɵɵlistener(\"mapDrag\", function AddressComponent_ng_template_1_map_marker_30_Template_map_marker_mapDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.markerDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marker_r55 = ctx.$implicit;\n    const ctx_r44 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r44.markerOptions)(\"position\", marker_r55.position);\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r58 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r58.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r59 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r59.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r60 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r60.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const car_r61 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", car_r61.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"ErrorMessages.phoneNumberIsUnvalid\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_div_105_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r63.selectedAddressType === \"Home\" ? \"assets/icons/mobile-icons/home-address-white.svg\" : \"assets/icons/mobile-icons/home-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_template_1_div_105_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r64.selectedAddressType === \"Work\" ? \"assets/icons/mobile-icons/work-address-white.svg\" : \"assets/icons/mobile-icons/work-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_template_1_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_105_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r66);\n      const addressType_r62 = restoredCtx.$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.selectAddressType(addressType_r62.name));\n    });\n    i0.ɵɵtemplate(1, AddressComponent_ng_template_1_div_105_img_1_Template, 1, 1, \"img\", 57);\n    i0.ɵɵtemplate(2, AddressComponent_ng_template_1_div_105_img_2_Template, 1, 1, \"img\", 58);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const addressType_r62 = ctx.$implicit;\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r52.selectedAddressType === addressType_r62.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r62.name === \"Home\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r62.name === \"Work\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", addressType_r62.name, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_110_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.onSubmit());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r53.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.addAddress\"));\n  }\n}\nfunction AddressComponent_ng_template_1_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_111_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.Update());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r54.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.updateAddress\"));\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"hidden-navbar\": a0\n  };\n};\nfunction AddressComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"em\", 67)(3, \"em\", 68);\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"em\", 68);\n    i0.ɵɵelementStart(8, \"span\", 69);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 70);\n    i0.ɵɵtemplate(12, AddressComponent_ng_template_1_h2_12_Template, 3, 3, \"h2\", 71);\n    i0.ɵɵtemplate(13, AddressComponent_ng_template_1_h2_13_Template, 3, 3, \"h2\", 71);\n    i0.ɵɵelementStart(14, \"div\", 47)(15, \"div\", 72)(16, \"div\", 73)(17, \"div\", 47)(18, \"div\", 10)(19, \"div\", 74)(20, \"span\", 75);\n    i0.ɵɵelement(21, \"em\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 77, 78);\n    i0.ɵɵlistener(\"onAddressChange\", function AddressComponent_ng_template_1_Template_input_onAddressChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.handleAddressChange($event));\n    });\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 79);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_Template_span_click_26_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.clear());\n    });\n    i0.ɵɵelement(27, \"em\", 80);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 10)(29, \"google-map\", 81);\n    i0.ɵɵlistener(\"mapClick\", function AddressComponent_ng_template_1_Template_google_map_mapClick_29_listener($event) {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.mapClicked($event));\n    })(\"mapInitialized\", function AddressComponent_ng_template_1_Template_google_map_mapInitialized_29_listener($event) {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.mapInitialize($event));\n    });\n    i0.ɵɵtemplate(30, AddressComponent_ng_template_1_map_marker_30_Template, 1, 2, \"map-marker\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"form\", 13)(32, \"div\", 61)(33, \"div\", 82)(34, \"div\", 83);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelement(37, \"br\");\n    i0.ɵɵelementStart(38, \"p-dropdown\", 21, 22);\n    i0.ɵɵlistener(\"onChange\", function AddressComponent_ng_template_1_Template_p_dropdown_onChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.filterCitiesByRegion($event.value));\n    });\n    i0.ɵɵtemplate(40, AddressComponent_ng_template_1_ng_template_40_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(41, AddressComponent_ng_template_1_ng_template_41_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 61)(43, \"div\", 82)(44, \"div\", 84);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelement(47, \"br\");\n    i0.ɵɵelementStart(48, \"p-dropdown\", 26, 27);\n    i0.ɵɵtemplate(50, AddressComponent_ng_template_1_ng_template_50_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(51, AddressComponent_ng_template_1_ng_template_51_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"div\", 61)(53, \"div\", 42);\n    i0.ɵɵelement(54, \"input\", 32);\n    i0.ɵɵelementStart(55, \"label\", 31);\n    i0.ɵɵtext(56);\n    i0.ɵɵpipe(57, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 61)(59, \"div\", 85)(60, \"ngx-intl-tel-input\", 37);\n    i0.ɵɵlistener(\"countryChange\", function AddressComponent_ng_template_1_Template_ngx_intl_tel_input_countryChange_60_listener($event) {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.onCountryChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, AddressComponent_ng_template_1_span_61_Template, 3, 3, \"span\", 38);\n    i0.ɵɵelementStart(62, \"label\", 35);\n    i0.ɵɵtext(63);\n    i0.ɵɵpipe(64, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"span\", 86);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 61)(69, \"div\", 42);\n    i0.ɵɵelement(70, \"input\", 87);\n    i0.ɵɵelementStart(71, \"label\", 16);\n    i0.ɵɵtext(72);\n    i0.ɵɵpipe(73, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 61)(75, \"div\", 42)(76, \"input\", 29);\n    i0.ɵɵlistener(\"input\", function AddressComponent_ng_template_1_Template_input_input_76_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.getCoordinates());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"label\", 28);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 61)(81, \"div\", 42);\n    i0.ɵɵelement(82, \"input\", 30);\n    i0.ɵɵelementStart(83, \"label\", 28);\n    i0.ɵɵtext(84);\n    i0.ɵɵpipe(85, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(86, \"div\", 61)(87, \"div\", 42);\n    i0.ɵɵelement(88, \"input\", 34);\n    i0.ɵɵelementStart(89, \"label\", 33);\n    i0.ɵɵtext(90);\n    i0.ɵɵpipe(91, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(92, \"div\", 61)(93, \"div\", 42);\n    i0.ɵɵelement(94, \"input\", 40);\n    i0.ɵɵelementStart(95, \"label\", 39);\n    i0.ɵɵtext(96);\n    i0.ɵɵpipe(97, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(98, \"div\", 88)(99, \"div\", 42)(100, \"div\", 89)(101, \"div\", 44);\n    i0.ɵɵtext(102);\n    i0.ɵɵpipe(103, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"div\", 45);\n    i0.ɵɵtemplate(105, AddressComponent_ng_template_1_div_105_Template, 4, 5, \"div\", 46);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(106, \"div\", 47)(107, \"div\", 63)(108, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_Template_button_click_108_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.setAsDefault());\n    });\n    i0.ɵɵpipe(109, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(110, AddressComponent_ng_template_1_div_110_Template, 3, 4, \"div\", 48);\n    i0.ɵɵtemplate(111, AddressComponent_ng_template_1_div_111_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_15_0;\n    let tmp_21_0;\n    let tmp_41_0;\n    let tmp_42_0;\n    let tmp_45_0;\n    let tmp_47_0;\n    let tmp_50_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(91, _c6, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 61, \"sideMenu.yourAccount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 63, \"multipleAddress.myAddresses\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id === \"add-address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id !== \"add-address\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(25, 65, \"addingAddress.search\"));\n    i0.ɵɵproperty(\"options\", ctx_r2.mapOptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"center\", ctx_r2.center)(\"zoom\", ctx_r2.zoom)(\"options\", ctx_r2.mapOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.addressForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(93, _c3, (((tmp_15_0 = ctx_r2.addressForm.get(\"region.id\")) == null ? null : tmp_15_0.hasError(\"required\")) && ((tmp_15_0 = ctx_r2.addressForm.get(\"region.id\")) == null ? null : tmp_15_0.touched) ? \"1px solid red\" : \"\") || (!((tmp_15_0 = ctx_r2.addressForm.get(\"region\")) == null ? null : tmp_15_0.valid) ? \"1px solid red\" : \"\")));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.region);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 67, \"addingAddress.region\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(95, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r2.allRegionList)(\"filter\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(96, _c3, (((tmp_21_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_21_0.hasError(\"required\")) && ((tmp_21_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_21_0.touched) ? \"1px solid red\" : \"\") || (!((tmp_21_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_21_0.valid) ? \"1px solid red\" : \"\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(46, 69, \"addingAddress.city\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(98, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r2.filteredCities)(\"filter\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r2.addressForm.value.postcode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(57, 71, \"addingAddress.post-code\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone region-label\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r2.phoneInputLength)(\"numberFormat\", ctx_r2.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r2.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(99, _c5, ctx_r2.SearchCountryField.Iso2, ctx_r2.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r2.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r2.customPlaceHolder)(\"ngClass\", i0.ɵɵpureFunction1(102, _c2, ((tmp_41_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.hasError(\"required\")) && ((tmp_41_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_42_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_42_0.hasError(\"validatePhoneNumber\")) && ((tmp_42_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_42_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(64, 73, \"addingAddress.RecipientContactNumber\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"*\", i0.ɵɵpipeBind1(67, 75, \"addingAddress.phoneNote\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(104, _c2, ((tmp_45_0 = ctx_r2.addressForm.get(\"country\")) == null ? null : tmp_45_0.hasError(\"required\")) && ((tmp_45_0 = ctx_r2.addressForm.get(\"country\")) == null ? null : tmp_45_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(73, 77, \"addingAddress.country\"), \"*\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(106, _c2, (tmp_47_0 = ctx_r2.addressForm.get(\"streetAddress\")) == null ? null : tmp_47_0.hasError(\"required\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(79, 79, \"addingAddress.addressDetails\"), \"*\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(85, 81, \"addingAddress.otherAddressSecond\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(108, _c2, ((tmp_50_0 = ctx_r2.addressForm.get(\"landMark\")) == null ? null : tmp_50_0.hasError(\"required\")) && ((tmp_50_0 = ctx_r2.addressForm.get(\"landMark\")) == null ? null : tmp_50_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(91, 83, \"addingAddress.landMark\"), \"*\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(97, 85, \"addingAddress.instructions\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(103, 87, \"addingAddress.addressLabel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.addressLabelList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.addressService.chosenAddress === null || ctx_r2.addressService.chosenAddress.isDefault === true)(\"label\", i0.ɵɵpipeBind1(109, 89, \"addingAddress.defaultAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id === \"add-address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id !== \"add-address\");\n  }\n}\nfunction AddressComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-mtn-success-modal\", 93);\n    i0.ɵɵlistener(\"submit\", function AddressComponent_ng_container_3_Template_app_mtn_success_modal_submit_1_listener() {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.onConfrim());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r3.isDisplaySuccessModal)(\"message\", ctx_r3.message);\n  }\n}\nfunction AddressComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-address-label\", 94);\n    i0.ɵɵlistener(\"submit\", function AddressComponent_ng_container_4_Template_app_address_label_submit_1_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.addAddress($event));\n    })(\"cancel\", function AddressComponent_ng_container_4_Template_app_address_label_cancel_1_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.onAddressDialogCancel($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r4.isDisplayOtherModal);\n  }\n}\nexport class AddressComponent {\n  ngZone;\n  addressService;\n  messageService;\n  router;\n  store;\n  route;\n  translate;\n  mainDataService;\n  loaderService;\n  _location;\n  cd;\n  permissionService;\n  appDataService;\n  $gtmService;\n  platformId;\n  routeToCheckOut;\n  markerOptions = {\n    draggable: true,\n    icon: 'assets/images/map-pin.svg'\n  };\n  latitude;\n  longitude;\n  phoneLength = 13;\n  position;\n  landMarkAddressRequired = false;\n  isMobileTemplate = false;\n  isDisplaySuccessModal = false;\n  isDisplayOtherModal = false;\n  message = '';\n  addressDetailCity = '';\n  placesRef;\n  addressForm = new UntypedFormGroup({\n    addressLabel: new UntypedFormControl('Home'),\n    receiverFirstName: new UntypedFormControl(''),\n    receiverLastName: new UntypedFormControl(''),\n    streetAddress: new UntypedFormControl('', Validators.required),\n    country: new UntypedFormControl('', Validators.required),\n    city: new UntypedFormControl('', Validators.required),\n    landMark: new UntypedFormControl('', Validators.required),\n    deliveryInstructions: new UntypedFormControl(''),\n    buldingNumber: new UntypedFormControl(''),\n    postcode: new UntypedFormControl(''),\n    receiverPhoneNumber: new UntypedFormControl('', Validators.required),\n    geo_location: new UntypedFormControl(''),\n    Lat: new UntypedFormControl(''),\n    Lng: new UntypedFormControl(''),\n    Id: new UntypedFormControl(''),\n    additionalAddress: new UntypedFormControl(''),\n    region: new UntypedFormGroup({\n      id: new UntypedFormControl(''),\n      regionName: new UntypedFormControl('')\n    }, Validators.required)\n  });\n  search = '';\n  Lat;\n  Lng;\n  zoom;\n  address;\n  myplaceHolder;\n  source;\n  isDefault = false;\n  navbarData;\n  center = {\n    lat: 0.3,\n    lng: 32.5\n  };\n  mapOptions = {\n    fullscreenControl: false,\n    disableDefaultUI: true,\n    componentRestrictions: {\n      country: 'UG'\n    }\n  };\n  allCities = [];\n  isDifferentCity = false;\n  addressLabelList = [{\n    'name': 'Home',\n    'id': 1\n  }, {\n    'name': 'Work',\n    'id': 2\n  }, {\n    'name': 'Other',\n    'id': 3\n  }];\n  selectedAddressType;\n  searchElementRef;\n  id = '';\n  routeSub;\n  geoCoder = new google.maps.Geocoder();\n  redirectUrl;\n  map;\n  borderBottomStyle = '2px solid red !important';\n  phoneInputLength = 12;\n  preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n  CustomCountryISO;\n  customPlaceHolder = '';\n  PhoneNumberFormat = PhoneNumberFormat;\n  SearchCountryField = SearchCountryField;\n  allRegionList = [];\n  filteredCities = [];\n  screenWidth = window.innerWidth;\n  lat;\n  lng;\n  selectedCitiesValue = [];\n  // Country coordinates mapping\n  countryCoordinates = {\n    'UG': {\n      lat: 0.3136,\n      lng: 32.5811,\n      country: 'UG'\n    },\n    'GH': {\n      lat: 5.595465,\n      lng: -0.242603,\n      country: 'GH'\n    },\n    'CI': {\n      lat: 7.5399,\n      lng: -5.5471,\n      country: 'CI'\n    } // Côte d'Ivoire\n  };\n\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(ngZone, addressService, messageService, router, store, route, translate, mainDataService, loaderService, _location, cd, permissionService, appDataService, $gtmService, platformId) {\n    this.ngZone = ngZone;\n    this.addressService = addressService;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.route = route;\n    this.translate = translate;\n    this.mainDataService = mainDataService;\n    this.loaderService = loaderService;\n    this._location = _location;\n    this.cd = cd;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.$gtmService = $gtmService;\n    this.platformId = platformId;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.source = this.router.getCurrentNavigation()?.extras?.state;\n    let tenantId = localStorage.getItem('tenantId');\n    if (tenantId && tenantId !== '') {\n      if (tenantId == '1') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '2') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '3') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '4') {\n        this.customPlaceHolder = 'XXXXXXXXXX';\n      }\n    }\n    // Set initial map center based on isoCode\n    this.setMapCenterFromIsoCode();\n  }\n  /**\r\n   * Set map center and component restrictions based on isoCode from localStorage\r\n   */\n  setMapCenterFromIsoCode() {\n    const isoCode = localStorage.getItem('isoCode');\n    if (isoCode && this.countryCoordinates[isoCode]) {\n      const countryData = this.countryCoordinates[isoCode];\n      this.center = {\n        lat: countryData.lat,\n        lng: countryData.lng\n      };\n      // Update map options with the correct country restriction\n      this.mapOptions = {\n        ...this.mapOptions,\n        componentRestrictions: {\n          country: countryData.country\n        }\n      };\n    } else {\n      // Default to Uganda if isoCode is not found or not supported\n      const defaultCountry = this.countryCoordinates['UG'];\n      this.center = {\n        lat: defaultCountry.lat,\n        lng: defaultCountry.lng\n      };\n      this.mapOptions = {\n        ...this.mapOptions,\n        componentRestrictions: {\n          country: defaultCountry.country\n        }\n      };\n    }\n  }\n  onBack() {\n    this._location.back();\n  }\n  ngOnInit() {\n    var _this = this;\n    this.route.queryParamMap.subscribe(queryParams => {\n      this.routeToCheckOut = queryParams.get(\"checkout\");\n    });\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.lat = history.state.lat;\n    this.lng = history.state.lng;\n    this.route.queryParams.subscribe(params => {\n      this.redirectUrl = params.returnUrl;\n    });\n    let userDetails = this.store.get('profile');\n    this.routeSub = this.route.params.subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (params) {\n        _this.id = params['id'];\n        if (_this.id != 'add-address') {\n          _this.getCustomerAddress();\n          _this.$gtmService.pushPageView('Your Addresses', 'Update Address');\n        } else {\n          _this.$gtmService.pushPageView('Your Addresses', 'Add Address');\n          const defaultPhoneNumber = localStorage.getItem('userPhone');\n          const countryPhone = userDetails.isdCode ? userDetails.isdCode?.replace('+', '') : localStorage.getItem('CountryPhone')?.replace('+', '') || '';\n          const phoneNumberWithoutCode = defaultPhoneNumber?.replace(countryPhone, '');\n          _this.addressForm.get('receiverPhoneNumber')?.setValue(phoneNumberWithoutCode);\n          _this.addressForm.get('receiverPhoneNumber')?.markAllAsTouched();\n          const regionList = yield _this.getAllRegion();\n          const selectedRegion = regionList.find(item => item.isDefault);\n          if (selectedRegion) {\n            _this.filteredCities = selectedRegion.cities;\n            _this.addressForm.patchValue({\n              region: selectedRegion\n            });\n          }\n          if (_this.lat && _this.lng) {\n            _this.setCurrentLocation({\n              lat: _this.lat,\n              lng: _this.lng\n            });\n          } else {\n            _this.setCurrentLocation();\n          }\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    let phoneLength = localStorage.getItem('PhoneLength')?.toString();\n    let landMarkAddress = localStorage.getItem('customerAddressLandmarkRequired')?.toString();\n    if (landMarkAddress && landMarkAddress == 'True') {\n      this.landMarkAddressRequired = true;\n      this.addressForm.controls['landMark'].setValidators([Validators.required]);\n      this.addressForm.controls['landMark'].updateValueAndValidity();\n    }\n    if (phoneLength) {\n      this.phoneLength = parseInt(phoneLength) - 2;\n    }\n    this.mainDataService.setUserData(userDetails);\n    let name = userDetails?.name?.split(' ');\n    this.addressForm.patchValue({\n      receiverFirstName: name[0] ? name[0] : '',\n      receiverLastName: name[1] ? name[1] : ''\n      // receiverPhoneNumber: userDetails.mobileNumber,\n    });\n\n    if (!localStorage.getItem(\"isoCode\")) {\n      const tenants = this.appDataService.tenants;\n      if (tenants.records != undefined) {\n        let tenantId = localStorage.getItem('tenantId');\n        let data = tenants.records;\n        let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n        localStorage.setItem('isoCode', arr?.isoCode);\n        this.store.set('allCountryTenants', tenants.records);\n      }\n    } else {\n      this.CustomCountryISO = userDetails.country ? userDetails.country : localStorage.getItem(\"isoCode\");\n    }\n    let isoCode = userDetails.country ? userDetails.country : localStorage.getItem(\"isoCode\");\n    if (this.appDataService.configuration && isoCode == 'UG' || isoCode == \"GH\" || isoCode == 'CI') {\n      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n    } else {\n      this.phoneInputLength = 20;\n    }\n    this.filteredCities = this.allCities;\n    this.region.valueChanges.subscribe(value => {\n      const selectedRegion = this.allRegionList?.find(r => r.id == value.id);\n      this.addressForm.patchValue({\n        region: {\n          id: selectedRegion.id,\n          regionName: selectedRegion.regionName\n        }\n      }, {\n        emitEvent: false\n      });\n    });\n  }\n  onCountryChange(event) {\n    console.log(event);\n    if (event.iso2.toUpperCase() !== 'UG' && event.iso2.toUpperCase() !== \"GH\" && event.iso2.toUpperCase() !== 'CI') {\n      this.phoneInputLength = 20;\n    } else {\n      if (this.appDataService.configuration) {\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n        if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n      }\n    }\n  }\n  mapInitialize(map) {\n    this.map = map;\n  }\n  handleAddressChange(place) {\n    this.Lat = place.geometry.location.lat();\n    this.Lng = place.geometry.location.lng();\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.zoom = 12;\n    this.center = this.position[0].position;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  ngOnDestroy() {\n    this.routeSub.unsubscribe();\n  }\n  getCustomerAddress() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loaderService.show();\n      try {\n        const res = yield _this2.addressService.getAddressById(_this2.id).toPromise();\n        _this2.addressService.loadedAddress = true;\n        if (res.data) {\n          _this2.isDefault = res.data.isDefault;\n          _this2.addressService.chosenAddress = res.data;\n          _this2.CustomCountryISO = res.data.countryISO;\n          const trimmedPhoneNumber = _this2.trimPhoneNumber(res.data.isdCode, res.data.receiverPhoneNumber);\n          yield _this2.getAllRegion();\n          const selectedRegion = _this2.selectedRegion(res.data.region);\n          const selectedCity = _this2.selectedCity(res.data.city);\n          _this2.addressForm.patchValue({\n            addressLabel: res.data.addressLabel,\n            receiverFirstName: res.data.receiverFirstName,\n            receiverLastName: res.data.receiverLastName,\n            postcode: res.data.postCode,\n            receiverPhoneNumber: trimmedPhoneNumber,\n            landMark: res.data.landMark,\n            deliveryInstructions: res.data.deliveryInstructions,\n            buldingNumber: res.data.buldingNumber,\n            additionalAddress: res.data.additionalAddress,\n            city: selectedCity.cityName,\n            region: selectedRegion\n          });\n          if (res.data.addressLabel != 'Home' && res.data.addressLabel != 'Work') {\n            _this2.addressLabelList[2].name = res.data.addressLabel;\n          }\n          _this2.selectedAddressType = res.data.addressLabel;\n          _this2.Lat = parseFloat(res.data.lat);\n          _this2.Lng = parseFloat(res.data.lng);\n          const streetAddress = res.data.streetAddress;\n          if (_this2.searchElementRef) {\n            _this2.searchElementRef.nativeElement.value = streetAddress;\n          }\n          _this2.position = [{\n            position: {\n              lat: _this2.Lat,\n              lng: _this2.Lng\n            }\n          }];\n          _this2.zoom = 8;\n          _this2.center = _this2.position[0].position;\n          if (res.data.streetAddress || res.data.country) {\n            _this2.addressForm.patchValue({\n              streetAddress: res.data.streetAddress,\n              country: res.data.country\n            });\n          } else {\n            _this2.getAddress(_this2.Lat, _this2.Lng);\n          }\n          _this2.createLocationButton();\n        } else {\n          _this2.setCurrentLocation();\n          _this2.createLocationButton();\n        }\n        _this2.loaderService.hide();\n      } catch (err) {\n        _this2.addressService.loadedAddress = true;\n        _this2.setCurrentLocation();\n        _this2.messageService.add({\n          severity: 'error',\n          summary: _this2.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      } finally {\n        _this2.loaderService.hide();\n      }\n    })();\n  }\n  trimPhoneNumber(isdCode, phoneNumber) {\n    let userDetails = this.store.get('profile');\n    return phoneNumber.length > 3 ? phoneNumber.substring(isdCode ? isdCode.replace('+', '').length : userDetails?.isdCode?.replace('+', '').length) : phoneNumber;\n  }\n  selectedRegion(regionName) {\n    const region = this.allRegionList.find(r => r.regionName === regionName);\n    if (region) {\n      this.filterCitiesByRegion(region.id);\n      this.selectedCitiesValue = region.cities;\n      return {\n        id: region.id,\n        regionName: region.regionName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"regionName\": null\n    };\n  }\n  selectedCity(cityName) {\n    const city = this.selectedCitiesValue.find(r => r.cityName === cityName);\n    if (city) {\n      return {\n        id: city.id,\n        cityName: city.cityName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"cityName\": null\n    };\n  }\n  getAddress(latitude, longitude) {\n    this.geoCoder.geocode({\n      location: {\n        lat: latitude,\n        lng: longitude\n      }\n    }, (results, status) => {\n      if (status === 'OK') {\n        if (results[0]) {\n          const countryComponent = results[0].address_components.find(component => component.types.includes('country'));\n          console.log(countryComponent);\n          const isoCode = localStorage.getItem('isoCode');\n          if (countryComponent && countryComponent.short_name === isoCode) {\n            this.position = [{\n              position: {\n                lat: latitude,\n                lng: longitude\n              }\n            }];\n            this.center = this.position[0].position;\n            this.zoom = 12;\n            this.address = results[0].formatted_address;\n            if (results[0]?.address_components.length) {\n              const city = results[0].address_components.find(item => item.types.includes('locality'));\n              this.addressDetailCity = city.long_name;\n            }\n            this.addressForm.patchValue({\n              streetAddress: this.address,\n              country: results[results.length - 1].formatted_address\n              // city: results[results.length - 3].formatted_address,\n            });\n\n            this.validate();\n            this.getCoordinates();\n            this.cd.detectChanges();\n          } else {\n            this.setMapCenterFromIsoCode();\n            this.position = [{}];\n            this.cd.detectChanges();\n          }\n        } else {\n          if (isPlatformBrowser(this.platformId)) {\n            window.alert('No results found');\n          }\n        }\n      } else {\n        if (isPlatformBrowser(this.platformId)) {\n          window.alert('Geocoder failed due to: ' + status);\n        }\n      }\n    });\n  }\n  clear() {\n    this.searchElementRef.nativeElement.value = '';\n  }\n  onSubmit() {\n    this.addressForm.patchValue({\n      Lat: this.Lat ? this.Lat.toString() : '',\n      Lng: this.Lng ? this.Lng.toString() : ''\n      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\n    });\n\n    this.loaderService.show();\n    if (this.addressForm.value.postcode == '') this.addressForm.value.postcode = 0;\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      if (this.addressForm.value.postcode === \"\") delete this.addressForm.value.postcode;\n      this.addressService.addAddress(formValue).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.loaderService.hide();\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully');\n          } else {\n            this.loaderService.hide();\n            this.messageService.add({\n              severity: 'error',\n              summary: res?.message\n            });\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  onConfrim() {\n    this.isDisplaySuccessModal = false;\n    if (this.routeToCheckOut) {\n      this.router.navigateByUrl(\"/checkout/selectAddress\");\n    } else {\n      this.router.navigate(['/account/address']);\n    }\n  }\n  Update() {\n    if (this.Lat?.toString() === \"\" || this.Lng?.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.addressIsRequired')\n      });\n      return;\n    }\n    this.addressForm.patchValue({\n      Lat: this.Lat?.toString(),\n      Lng: this.Lng?.toString(),\n      Id: this.addressService.chosenAddress.id\n    });\n    this.loaderService.show();\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      this.addressService.updateAddress(formValue).subscribe({\n        next: res => {\n          this.loaderService.hide();\n          if (!res.success) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.address'),\n              detail: this.translate.instant(res.message)\n            });\n          } else {\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  OnlyNumeric(val) {\n    if (!Number(val.value)) {\n      this.addressForm.value.postcode = '';\n      this.addressForm.value.phone = '';\n    }\n    return false;\n  }\n  checkPlaceHolder() {\n    if (this.myplaceHolder) {\n      this.myplaceHolder = '';\n    } else {\n      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\n      if (this.myplaceHolder) this.myplaceHolder = this.myplaceHolder + ' 000 000 000';else this.myplaceHolder = '256 000 000 000';\n    }\n  }\n  validate() {\n    if (!this.addressForm.valid) return true;\n  }\n  setAsDefault() {\n    this.addressService.setDefault(this.id).subscribe({\n      next: res => {\n        this.messageService.add({\n          severity: 'success',\n          summary: this.translate.instant('ResponseMessages.address'),\n          detail: this.translate.instant('ResponseMessages.defaultAddressSuccessfully')\n        });\n        if (this.redirectUrl && this.redirectUrl !== '') {\n          this.router.navigate([this.redirectUrl]);\n        } else {\n          this.router.navigate(['/account/address']);\n        }\n      },\n      error: err => {\n        this.loaderService.hide();\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err\n        });\n      }\n    });\n  }\n  setCurrentLocation(selectedPosition) {\n    this.addressService.chosenAddress = null;\n    if ('geolocation' in navigator) {\n      if (!selectedPosition) {\n        navigator.geolocation.getCurrentPosition(position => {\n          this.Lat = position.coords.latitude;\n          this.Lng = position.coords.longitude;\n          this.position = [{\n            position: {\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.getAddress(this.Lat, this.Lng);\n          this.createLocationButton();\n        });\n      } else {\n        this.Lat = parseFloat(selectedPosition.lat);\n        this.Lng = parseFloat(selectedPosition.lng);\n        this.position = [{\n          position: {\n            lat: this.Lat,\n            lng: this.Lng\n          }\n        }];\n        this.getAddress(this.Lat, this.Lng);\n        this.createLocationButton();\n      }\n    }\n  }\n  mapClicked(event) {\n    let latLng = JSON.parse(JSON.stringify(event.latLng));\n    this.Lat = latLng.lat;\n    this.Lng = latLng.lng;\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.center = this.position[0].position;\n    this.zoom = 12;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  createLocationButton() {\n    if (isPlatformBrowser(this.platformId)) {\n      const controlDiv = document.createElement('div');\n      controlDiv.index = 100;\n      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\n    }\n  }\n  markerDragEnd(event) {\n    if (event.latLng != null) {\n      const latLng = event.latLng.toJSON();\n      this.getAddress(latLng.lat, latLng.lng);\n    }\n  }\n  getCities() {\n    const reqObj = {\n      currentPage: 1,\n      pageSize: 5,\n      ignorePagination: true\n    };\n    this.addressService.getAllCities(reqObj).subscribe(res => {\n      if (res.success) {\n        this.allCities = res.data.records;\n      }\n    });\n  }\n  getCoordinates() {\n    var geocoder = new google.maps.Geocoder();\n    this.isDifferentCity = true;\n    geocoder.geocode({\n      'address': this.addressForm.controls['streetAddress'].value\n    }, (results, status) => {\n      if (status == google.maps.GeocoderStatus.OK) {\n        if (results[0].address_components.length) {\n          const city = results[0].address_components.find(item => item.types.includes('locality'));\n          if (city.long_name === this.addressDetailCity) {\n            this.isDifferentCity = false;\n            this.cd.detectChanges();\n          }\n        }\n      }\n    });\n  }\n  getAllRegion() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      let reqObj = {\n        currentPage: 1,\n        pageSize: 5,\n        ignorePagination: true\n      };\n      try {\n        const res = yield _this3.addressService.getAllRegions(reqObj).toPromise();\n        if (res.success) {\n          _this3.allRegionList = res.data.records;\n          _this3.filteredCities = res.data.records[0].cities;\n          return res.data.records;\n        } else {\n          return [];\n        }\n      } catch (error) {\n        _this3.messageService.add({\n          severity: 'error',\n          summary: _this3.translate.instant('ErrorMessages.fetchError')\n        });\n        return [];\n      }\n    })();\n  }\n  filterCitiesByRegion(regionId) {\n    const selectedRegion = this.allRegionList.find(region => region.id === regionId);\n    if (selectedRegion) {\n      this.filteredCities = selectedRegion?.cities;\n    }\n  }\n  selectAddressType(type) {\n    this.selectedAddressType = type;\n    this.addressForm.patchValue({\n      addressLabel: type\n    });\n    if (this.selectedAddressType == 'Other') {\n      this.isDisplayOtherModal = true;\n    }\n  }\n  addAddress(event) {\n    if (event) {\n      this.addressForm.patchValue({\n        addressLabel: event\n      });\n      this.addressLabelList[2].name = event;\n    }\n    this.isDisplayOtherModal = false;\n  }\n  onAddressDialogCancel(event) {\n    this.isDisplayOtherModal = event;\n  }\n  get region() {\n    return this.addressForm.get('region');\n  }\n  static ɵfac = function AddressComponent_Factory(t) {\n    return new (t || AddressComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddressComponent,\n    selectors: [[\"app-address\"]],\n    viewQuery: function AddressComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.placesRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchElementRef = _t.first);\n      }\n    },\n    hostBindings: function AddressComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function AddressComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 5,\n    vars: 4,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [4, \"ngIf\"], [1, \"account-page-mobile\"], [1, \"d-flex\", \"cart-mobile-new__address-layout\", \"flex-row\"], [1, \"d-inline-flex\", \"cart-mobile-new__address-layout__address-items-section\"], [1, \"header-container\"], [\"src\", \"assets/icons/mobile-icons/back-icon.svg\", \"alt\", \"back-icon\", 3, \"click\"], [1, \"header-container__header-detail\"], [1, \"complete-address\", \"mb-5\"], [1, \"col-12\"], [\"height\", \"111px\", \"width\", \"100%\", 3, \"center\", \"zoom\", \"options\", \"mapClick\", \"mapInitialized\"], [3, \"options\", \"position\", \"mapDrag\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", 3, \"formGroup\"], [1, \"form-bg\"], [1, \"d-flex\"], [\"for\", \"country\"], [\"formControlName\", \"country\", \"id\", \"country\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", \"input-address\", 3, \"ngClass\"], [1, \"col-12\", \"col-md-6\", \"mt-3\", \"region-dropdown\"], [1, \"search-container\", \"select-dropdown-merchant\", \"city-dropdown\", \"region-address\", 3, \"ngStyle\"], [1, \"product-label\", \"region-label\", 3, \"formGroup\"], [\"formControlName\", \"id\", \"optionValue\", \"id\", \"optionLabel\", \"regionName\", \"filterBy\", \"regionName\", 1, \"custom-dropdown\", 3, \"options\", \"filter\", \"onChange\"], [\"ddRegion\", \"\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [1, \"product-label\", \"region-label\"], [\"formControlName\", \"city\", \"optionValue\", \"cityName\", \"optionLabel\", \"cityName\", \"filterBy\", \"cityName\", 1, \"custom-dropdown\", 3, \"options\", \"filter\"], [\"ddCity\", \"\"], [\"for\", \"address-type\"], [\"formControlName\", \"streetAddress\", \"id\", \"address-type\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\", \"input\"], [\"formControlName\", \"additionalAddress\", \"id\", \"other-address-second\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\"], [\"for\", \"city\"], [\"formControlName\", \"postcode\", \"id\", \"postcode\", \"pInputText\", \"\", \"type\", \"number\", 1, \"form-input\", 3, \"value\"], [\"for\", \"landMark\"], [\"formControlName\", \"landMark\", \"id\", \"landMark\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\"], [\"for\", \"receiverPhoneNumber\"], [1, \"d-flex\", \"contact-address\", \"flex-column\"], [\"formControlName\", \"receiverPhoneNumber\", \"name\", \"receiverPhoneNumber\", \"id\", \"receiverPhoneNumber\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"maxLength\", \"numberFormat\", \"phoneValidation\", \"preferredCountries\", \"searchCountryField\", \"searchCountryFlag\", \"selectFirstCountry\", \"selectedCountryISO\", \"separateDialCode\", \"customPlaceholder\", \"ngClass\", \"countryChange\"], [\"class\", \"error-msg\", 4, \"ngIf\"], [\"for\", \"instructions\"], [\"formControlName\", \"deliveryInstructions\", \"id\", \"instructions\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\"], [1, \"col-12\", \"col-md-12\", \"mt-3\", \"form-bg\"], [1, \"p-float-label\", \"w-full\"], [1, \"search-container\", \"select-dropdown-merchant\", \"bg-address\"], [1, \"product-label\", \"bg-address\"], [1, \"options-address-list\", \"d-flex\"], [\"class\", \"custom-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\"], [\"class\", \"col-12 col-md-6 mt-3\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-6\", \"button-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"defaultBtn\", \"w-100\", \"add-address-btn\", 3, \"disabled\", \"label\", \"click\"], [\"class\", \"col-12 col-md-6 mt-3 button-address\", 4, \"ngIf\"], [3, \"options\", \"position\", \"mapDrag\"], [1, \"\"], [1, \"search-name\"], [1, \"error-msg\"], [1, \"custom-option\", 3, \"click\"], [\"alt\", \"Home Icon\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Work Icon\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Home Icon\", 3, \"src\"], [\"alt\", \"Work Icon\", 3, \"src\"], [1, \"col-12\", \"col-md-6\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"add-address-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"col-12\", \"col-md-6\", \"mt-3\", \"button-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"confirmBtn\", 3, \"disabled\", \"label\", \"click\"], [1, \"address\", 3, \"ngClass\"], [1, \"breadcrumb-address\", \"d-flex\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-home\", \"cursor-pointer\", 3, \"routerLink\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-angle-left\"], [3, \"routerLink\"], [1, \"content-container\", \"mt-5\", 2, \"padding-top\", \"1px\", \"max-width\", \"1057px\"], [\"class\", \"add-address\", \"style\", \"font-family: var(--medium-font)\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-12\", \"mb-4\"], [1, \"address-card\", \"shadow-1\", \"p-3\", \"mb-4\"], [1, \"p-inputgroup\", \"search-group\"], [1, \"p-inputgroup-addon\", \"icon-bg\"], [1, \"pi\", \"pi-search\"], [\"ngx-gp-autocomplete\", \"\", 1, \"map-search\", 3, \"options\", \"placeholder\", \"onAddressChange\"], [\"placesRef\", \"ngx-places\", \"search\", \"\"], [1, \"p-inputgroup-addon\", \"icon-bg\", 3, \"click\"], [1, \"pi\", \"pi-times-circle\"], [\"height\", \"224px\", \"width\", \"100%\", 3, \"center\", \"zoom\", \"options\", \"mapClick\", \"mapInitialized\"], [1, \"search-container\", \"select-dropdown-merchant\", \"city-dropdown\", 3, \"ngStyle\"], [1, \"product-label\", 3, \"formGroup\"], [1, \"product-label\"], [1, \"p-float-label\", \"w-full\", \"contact-input-phone-container\"], [1, \"note\"], [\"formControlName\", \"country\", \"id\", \"country\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\"], [1, \"col-12\", \"col-md-12\", \"mt-0\", \"form-bg\"], [1, \"select-dropdown-merchant\", \"bg-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"defaultBtn\", \"w-100\", 3, \"disabled\", \"label\", \"click\"], [1, \"add-address\", 2, \"font-family\", \"var(--medium-font)\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [3, \"displayModal\", \"message\", \"submit\"], [3, \"displayModal\", \"submit\", \"cancel\"]],\n    template: function AddressComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AddressComponent_ng_container_0_Template, 99, 92, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, AddressComponent_ng_template_1_Template, 112, 110, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(3, AddressComponent_ng_container_3_Template, 2, 2, \"ng-container\", 2);\n        i0.ɵɵtemplate(4, AddressComponent_ng_container_4_Template, 2, 1, \"ng-container\", 2);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isDisplaySuccessModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isDisplayOtherModal);\n      }\n    },\n    dependencies: [i2.PrimeTemplate, i7.InputText, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.FormGroupDirective, i8.FormControlName, i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i9.ButtonDirective, i3.RouterLink, i10.SuccessModalComponent, i11.NgxGpAutocompleteDirective, i12.GoogleMap, i12.MapMarker, i13.Dropdown, i14.NgxIntlTelInputComponent, i14.NativeElementInjectorDirective, i15.AddressLabelComponent, i4.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.address[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n}\\n.address[_ngcontent-%COMP%]   .address-card[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n  width: 100%;\\n  background-color: #ffffff;\\n  border-radius: 5px;\\n}\\n.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .map-search[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: #f5f5f5 !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  padding: 13px 5px;\\n  font-size: 15px;\\n  color: black;\\n  font-family: var(--regular-font);\\n  border-top: 1px solid #ced4da !important;\\n  border-bottom: 1px solid #ced4da !important;\\n  border-left: unset;\\n  border-right: unset;\\n}\\n.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .icon-bg[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  cursor: pointer;\\n}\\n.address[_ngcontent-%COMP%]   .map[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 224px;\\n  border: 1px solid #ced4da;\\n  border-radius: 5px;\\n}\\n.address[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background-color: #f5f5f5 !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  padding: 22px 10px 13px;\\n  font-size: 16px;\\n  color: black;\\n  border-bottom: 1.5px solid #aeaeae;\\n  font-family: var(--regular-font);\\n}\\n.address[_ngcontent-%COMP%]   agm-map[_ngcontent-%COMP%] {\\n  height: 300px;\\n}\\n\\n[_nghost-%COMP%]     .address .p-float-label label {\\n  top: 30px;\\n  font-size: 11px;\\n  color: #323232;\\n  font-weight: 500;\\n  font-family: var(--medium-font);\\n}\\n[_nghost-%COMP%]     .address .p-float-label input ~ label {\\n  top: 15px !important;\\n}\\n[_nghost-%COMP%]     .address .p-float-label.contact-input-phone-container label {\\n  top: 20px;\\n}\\n\\n.button-address[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n.button-address[_ngcontent-%COMP%]   .defaultBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n  letter-spacing: 0px;\\n  color: var(--header_bgcolor);\\n  text-transform: uppercase;\\n  background: transparent;\\n  border: 1px solid var(--header_bgcolor);\\n  padding: 10px 20px;\\n  font-size: 15px;\\n  border-radius: 25px;\\n  width: 70%;\\n  outline: 0 none;\\n  font-family: var(--medium-font);\\n}\\n.button-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: var(--header_bgcolor);\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  border: 1px solid var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n  outline: 0 none;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  border-radius: 1px;\\n  padding: 8px 16px;\\n  height: 60px;\\n  border-bottom: 1.5px solid #aeaeae;\\n}\\n@media only screen and (max-width: 767px) {\\n  .search-container[_ngcontent-%COMP%] {\\n    border-bottom: none !important;\\n  }\\n}\\n\\n.select-dropdown-merchant[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-filter-container input {\\n  border: 1px solid #ced4da !important;\\n}\\n.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-items-wrapper ul {\\n  padding-left: 0;\\n}\\n\\n.product-label[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #323232;\\n  font-family: var(--medium-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n@media only screen and (max-width: 767px) {\\n  .product-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    font-weight: 300;\\n    font-family: var(--regular-font);\\n  }\\n}\\n\\n.custom-dropdown[_ngcontent-%COMP%]     .p-dropdown .p-dropdown-label {\\n  background: transparent;\\n  border: 0 none;\\n  padding: 0;\\n  font-family: var(--medium-font);\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 14px;\\n  line-height: 16px;\\n  \\n\\n  color: #323232;\\n}\\n\\n@media screen and (max-width: 720px) {\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    padding-top: 0rem !important;\\n  }\\n  .address[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n}\\n.breadcrumb-address[_ngcontent-%COMP%] {\\n  background-color: #efeded;\\n  padding: 1rem 2rem;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 15px;\\n}\\n\\n.add-address[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {\\n  color: #495057;\\n  background: none !important;\\n  width: 100%;\\n}\\n\\n.search-name[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #323232;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dropdown-items-wrapper {\\n  background-color: #f5f5f5;\\n}\\n@media only screen and (max-width: 767px) {\\n    .p-dropdown-items-wrapper {\\n    margin-top: 0px !important;\\n  }\\n}\\n\\n.select-dropdown-merchant[_ngcontent-%COMP%]     .p-overlay.p-component {\\n  min-width: 784px !important;\\n  transform-origin: center top;\\n  top: 17px !important;\\n  left: 0px;\\n  margin-left: -15px;\\n}\\n\\n.city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component {\\n  min-width: 380px !important;\\n  transform-origin: center top;\\n  top: 17px !important;\\n  left: 0px;\\n  margin-left: -15px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component {\\n    min-width: 100% !important;\\n    margin-left: 0px !important;\\n  }\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-transform: uppercase;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n  .custom-map-control-button {\\n  cursor: pointer !important;\\n}\\n\\n.account-page-mobile[_ngcontent-%COMP%] {\\n  margin-top: 75px !important;\\n}\\n\\n.cart-mobile-new__address-layout__address-items-section[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  border: 1px solid #e4e7e9;\\n  border-radius: 4px;\\n  flex-direction: column;\\n  background: #f6f6f6;\\n}\\n\\n.header-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #2d2d2d;\\n}\\n\\n.complete-address[_ngcontent-%COMP%] {\\n  max-width: 1057px;\\n  padding: 16px;\\n  padding-top: 0px;\\n}\\n\\n.form-bg[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: white;\\n  width: 100%;\\n  padding: 16px;\\n  border-radius: 8px;\\n}\\n\\n.input-address[_ngcontent-%COMP%] {\\n  padding: 0px !important;\\n}\\n\\n.region-address[_ngcontent-%COMP%] {\\n  padding: 12px !important;\\n  background: white;\\n  align-content: center;\\n  padding-left: 13px !important;\\n  border: none;\\n}\\n\\n.region-label[_ngcontent-%COMP%] {\\n  background: white !important;\\n}\\n\\n.region-dropdown[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%]:enabled:hover {\\n  border: none !important;\\n  width: 100%;\\n  box-shadow: none !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  input[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 0px !important;\\n    font-family: var(--regular-font);\\n  }\\n  label[_ngcontent-%COMP%] {\\n    font-family: var(--regular-font);\\n    font-size: 10px;\\n    font-weight: 300;\\n  }\\n}\\n.custom-option[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 4px;\\n  border: 1px solid var(--primary, #204e6e);\\n  padding: 4px 6px;\\n  min-width: 75px;\\n  text-align: center;\\n  width: auto;\\n}\\n\\n.custom-option.selected[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  background: var(--primary, #204e6e);\\n  color: white;\\n}\\n\\n.bg-address[_ngcontent-%COMP%] {\\n  background: #fff;\\n  padding-left: 0px;\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-weight: 300;\\n}\\n\\n.options-address-list[_ngcontent-%COMP%] {\\n  gap: 10px;\\n  margin-top: 10px;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .contact-address[_ngcontent-%COMP%]     .contact-input-phone {\\n    border-bottom: none !important;\\n    background-color: white !important;\\n    height: 40px !important;\\n  }\\n    .map-container {\\n    border-radius: 12px;\\n    box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.1);\\n    border: 1px solid #fff;\\n  }\\n}\\n.add-address-btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px !important;\\n  border-radius: 8px !important;\\n  text-transform: capitalize !important;\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  font-size: small;\\n  color: #45494c;\\n  font-family: var(--medium-font) !important;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}