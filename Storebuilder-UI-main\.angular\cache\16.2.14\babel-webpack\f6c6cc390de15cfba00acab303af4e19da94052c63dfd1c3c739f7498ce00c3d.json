{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../shared/services/token.service\";\nimport * as i3 from \"ngx-cookie-service\";\nexport class AuthGuard {\n  constructor(router, injector, authTokenService, cookieService) {\n    this.router = router;\n    this.injector = injector;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.token = '';\n  }\n  canActivate(route, state) {\n    let localToken = null;\n    this.authTokenService.authTokenData.subscribe(message => localToken = message);\n    const cookieToken = this.cookieService.get('authToken');\n    if (localToken) {\n      this.token = localToken;\n      return true;\n    } else if (cookieToken && cookieToken !== '') {\n      this.token = cookieToken;\n      return true;\n    }\n    // console.log(this.token);\n    // not logged in so redirect to login page with the return url\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  static #_ = this.ɵfac = function AuthGuard_Factory(t) {\n    return new (t || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.AuthTokenService), i0.ɵɵinject(i3.CookieService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "injector", "authTokenService", "cookieService", "token", "canActivate", "route", "state", "localToken", "authTokenData", "subscribe", "message", "cookieToken", "get", "navigate", "queryParams", "returnUrl", "url", "_", "i0", "ɵɵinject", "i1", "Router", "Injector", "i2", "AuthTokenService", "i3", "CookieService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\guard\\auth.guard.ts"], "sourcesContent": ["import { Injectable, Injector } from '@angular/core';\r\nimport { Router, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\r\nimport { CookieService } from 'ngx-cookie-service';\r\n\r\nimport { StoreService } from '../shared/services/store.service';\r\nimport { AuthTokenService } from '../shared/services/token.service';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AuthGuard implements CanActivate {\r\n  token: any = '';\r\n  constructor(\r\n    private router: Router,\r\n    private injector: Injector,\r\n\r\n    private authTokenService: AuthTokenService, private cookieService: CookieService\r\n\r\n  ) {\r\n  }\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {\r\n    let localToken = null\r\n    this.authTokenService.authTokenData.subscribe(message => localToken = message);\r\n    const cookieToken = this.cookieService.get('authToken')\r\n    if (localToken) {\r\n      this.token = localToken\r\n      return true;\r\n    } else if(cookieToken && cookieToken !== '') {\r\n      this.token = cookieToken\r\n      return true\r\n    }\r\n    // console.log(this.token);\r\n    // not logged in so redirect to login page with the return url\r\n    this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });\r\n    return false;\r\n  }\r\n}\r\n"], "mappings": ";;;;AAQA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,MAAc,EACdC,QAAkB,EAElBC,gBAAkC,EAAUC,aAA4B;IAHxE,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAER,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAA4B,KAAAC,aAAa,GAAbA,aAAa;IALnE,KAAAC,KAAK,GAAQ,EAAE;EAQf;EAEAC,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAI,CAACN,gBAAgB,CAACO,aAAa,CAACC,SAAS,CAACC,OAAO,IAAIH,UAAU,GAAGG,OAAO,CAAC;IAC9E,MAAMC,WAAW,GAAG,IAAI,CAACT,aAAa,CAACU,GAAG,CAAC,WAAW,CAAC;IACvD,IAAIL,UAAU,EAAE;MACd,IAAI,CAACJ,KAAK,GAAGI,UAAU;MACvB,OAAO,IAAI;KACZ,MAAM,IAAGI,WAAW,IAAIA,WAAW,KAAK,EAAE,EAAE;MAC3C,IAAI,CAACR,KAAK,GAAGQ,WAAW;MACxB,OAAO,IAAI;;IAEb;IACA;IACA,IAAI,CAACZ,MAAM,CAACc,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAAEC,WAAW,EAAE;QAAEC,SAAS,EAAET,KAAK,CAACU;MAAG;IAAE,CAAE,CAAC;IAC3E,OAAO,KAAK;EACd;EAAC,QAAAC,CAAA,G;qBA1BUpB,SAAS,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAI,QAAA,GAAAJ,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAAN,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAT9B,SAAS;IAAA+B,OAAA,EAAT/B,SAAS,CAAAgC,IAAA;IAAAC,UAAA,EADI;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}