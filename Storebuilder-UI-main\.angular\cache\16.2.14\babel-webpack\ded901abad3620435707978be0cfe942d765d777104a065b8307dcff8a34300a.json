{"ast": null, "code": "import { environment } from \"../../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class HomeService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}`;\n  }\n  getSectionMapping() {\n    return this.http.get(`${this.baseUrl}/Tenant/ShowRoomConfiguration/GetTenantShowRoomConfiguration`);\n  }\n  getMainSliders(params) {\n    return this.http.get(`${this.baseUrl}/Tenant/MainBanner/GetAllMainBanner`, {\n      params\n    });\n  }\n  getBanners(id) {\n    return this.http.get(`${this.baseUrl}/Tenant/Banner/GetBannerById/${id}`);\n  }\n  static #_ = this.ɵfac = function HomeService_Factory(t) {\n    return new (t || HomeService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HomeService,\n    factory: HomeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "HomeService", "constructor", "http", "baseUrl", "apiEndPoint", "getSectionMapping", "get", "getMainSliders", "params", "getBanners", "id", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\services\\home.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from \"../../../../environments/environment\";\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {Observable} from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class HomeService {\r\n  baseUrl: string;\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}`;\r\n  }\r\n\r\n  getSectionMapping(): Observable<any> {\r\n    return this.http.get(`${this.baseUrl}/Tenant/ShowRoomConfiguration/GetTenantShowRoomConfiguration`);\r\n  }\r\n\r\n  getMainSliders(params : any): Observable<any> {\r\n    return this.http.get<any>(`${this.baseUrl}/Tenant/MainBanner/GetAllMainBanner`, { params });\r\n  }\r\n  getBanners(id : any): Observable<any> {\r\n    return this.http.get<any>(`${this.baseUrl}/Tenant/Banner/GetBannerById/${id}`);\r\n  }\r\n  \r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,sCAAsC;;;AAOhE,OAAM,MAAOC,WAAW;EAGtBC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAEZ,IAAI,CAACC,OAAO,GAAG,GAAGJ,WAAW,CAACK,WAAW,EAAE;EAC7C;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,GAAG,IAAI,CAACH,OAAO,8DAA8D,CAAC;EACrG;EAEAI,cAAcA,CAACC,MAAY;IACzB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,qCAAqC,EAAE;MAAEK;IAAM,CAAE,CAAC;EAC7F;EACAC,UAAUA,CAACC,EAAQ;IACjB,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,gCAAgCO,EAAE,EAAE,CAAC;EAChF;EAAC,QAAAC,CAAA,G;qBAlBUX,WAAW,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXhB,WAAW;IAAAiB,OAAA,EAAXjB,WAAW,CAAAkB,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}