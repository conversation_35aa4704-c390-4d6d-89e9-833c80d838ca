import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { PermissionService, StoreService } from '@core/services';
import UtilityFunctions from '@core/utilities/functions';
import { environment } from '@environments/environment';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleAnalyticsService } from 'ngx-google-analytics';
import { AccordionModule } from 'primeng/accordion';
import { ListboxModule } from 'primeng/listbox';
@Component({
  selector: 'app-category-listing-menu',
  standalone: true,
  imports: [AccordionModule,ListboxModule,FormsModule ,CommonModule,RouterModule,TranslateModule],
  templateUrl: './category-listing-menu.component.html',
  styleUrls: ['./category-listing-menu.component.scss']
})
export class CategoryListingMenuComponent implements OnInit{

  categories: any;
  baseUrl: string = environment.apiEndPoint;
  selectedTab: any ;
  activeIndex: number|number[]|null|undefined;
  selectedCountry!: any;
  childCategoryItems: any;
  categoryName: any;
  listingHolder: any;
  isGoogleAnalytics:boolean = false
  constructor(private store:StoreService,
  private $gaService: GoogleAnalyticsService,
  private permissionService:PermissionService){

  }

  ngOnInit(): void {
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');

   this.callCategories();
  }
  callCategories(){
    this.store.subscription('categories')
    .subscribe({
      next: (res: any) => {

          this.categories = res;
          this.categories = this.categories.filter((item: any)=> !item.hide);
          this.categories?.forEach((cat: any) => {
            cat['path'] = cat.categoryName;
            cat['catIds'] = cat.id;
            cat['disabled'] = true;

          });
          this.listingHolder = this.categories;
       
      },
      error: (err: any) => {
        console.error(err);
      }
    });
  }
  imageUrlProvider(){
    let imageObj = this.listingHolder.find((item: any) => {
      return item.categoryName === this.categoryName;
    });
    return imageObj.image ;

  }
  selectedTabControl(_tabData: any) {

    this.activeIndex = -1;
    if (_tabData?.path) {
      this.categoryName = _tabData.path.split("/")[0];
    }
    if(!this.selectedTab){
      this.categories = _tabData.categories.filter((cat:any)=> !cat.hide)
      this.selectedTab = _tabData
    }
    if(this.selectedTab !==_tabData){
      this.selectedTab = _tabData;
      this.childCategoryItems = this.selectedTab?.categories?.map((cat:any) => {
        return {
          name: cat.categoryName,
          code: cat.image,
          id : cat.id,
          hidden:cat.hide,
          totalProductC: cat.totalProductCount
        };
      }) ;
      this.childCategoryItems = this.childCategoryItems.filter((cat:any)=> !cat.hidden)
    }
  }
  triggerGoogleAnalytics(subCategoryName?:string){
    if(this.isGoogleAnalytics){

    this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true, {
      categorySelected: subCategoryName ? subCategoryName : this.categoryName,
    });
  }
  }
  backButton(){
    this.callCategories();
    this.selectedTab = null;
    this.activeIndex = -1;
  }
  errorHandler(event: any) {
    if (environment.isStoreCloud) {
      event.target.src = "assets/images/placeholder.png";

    } else {
      event.target.src = "assets/images/mtn-alt.png";
    }
  }
  getImagesUrl(product: any) {
   return UtilityFunctions.verifyImageURL(product, environment.apiEndPoint);
  }
}
