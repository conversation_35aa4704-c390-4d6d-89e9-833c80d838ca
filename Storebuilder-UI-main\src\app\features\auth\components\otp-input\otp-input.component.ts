import { Component, EventEmitter, Input, Output, OnInit, OnDestroy, ChangeDetectorRef, ElementRef, ViewChildren, QueryList, AfterViewInit, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-otp-input',
  standalone: true,
  imports: [CommonModule, FormsModule, TranslateModule],
  templateUrl: './otp-input.component.html',
  styleUrls: ['./otp-input.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => OtpInputComponent),
      multi: true
    }
  ]
})
export class OtpInputComponent implements OnInit, OnDestroy, AfterViewInit, ControlValueAccessor {
  @Input() length: number = 4;
  @Output() otpComplete = new EventEmitter<string>();
  @Output() resendOtp = new EventEmitter<void>();
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef>;

  value: string = '';
  digits: string[] = ['', '', '', ''];
  activeIndex: number = 0;
  
  timeLeft: number = 60;
  timeLeftDisplay: string = '60';
  interval: any;

  private onChange = (value: string) => {};
  private onTouched = () => {};

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.digits = new Array(this.length).fill('');
    this.startTimer();
  }

  ngAfterViewInit(): void {
    // Focus the hidden input for auto-fill
    this.focusHiddenInput();
  }

  ngOnDestroy() {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }

  startTimer() {
    this.timeLeft = 60;
    this.timeLeftDisplay = '60';
    
    if (this.interval) {
      clearInterval(this.interval);
    }
    
    this.interval = setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
        this.timeLeftDisplay = this.timeLeft < 10 ? `0${this.timeLeft}` : this.timeLeft.toString();
      } else {
        clearInterval(this.interval);
        this.timeLeftDisplay = '00';
      }
      this.cd.detectChanges();
    }, 1000);
  }

  onDigitInput(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    let inputValue = target.value.replace(/\D/g, ''); // Remove non-digits
    
    // Only allow single digit
    if (inputValue.length > 1) {
      inputValue = inputValue.slice(-1);
    }

    this.digits[index] = inputValue;
    this.updateValue();
    
    // Update the hidden input value
    this.updateHiddenInput();
    
    this.onChange(this.value);
    this.onTouched();

    // Move to next input if digit entered
    if (inputValue && index < this.length - 1) {
      this.focusNextInput(index + 1);
    }

    // Check if OTP is complete
    if (this.value.length === this.length) {
      this.otpComplete.emit(this.value);
    }
  }

  onKeyDown(index: number, event: KeyboardEvent) {
    const target = event.target as HTMLInputElement;
    
    // Handle backspace
    if (event.key === 'Backspace') {
      if (this.digits[index]) {
        this.digits[index] = '';
      } else if (index > 0) {
        // Move to previous input and clear it
        this.focusPrevInput(index - 1);
      }
      this.updateValue();
      this.updateHiddenInput();
      this.onChange(this.value);
    }
    // Handle arrow keys
    else if (event.key === 'ArrowLeft' && index > 0) {
      this.focusPrevInput(index - 1);
    }
    else if (event.key === 'ArrowRight' && index < this.length - 1) {
      this.focusNextInput(index + 1);
    }
    // Handle digit input
    else if (/^\d$/.test(event.key)) {
      this.digits[index] = event.key;
      this.updateValue();
      this.updateHiddenInput();
      this.onChange(this.value);
      this.onTouched();

      // Move to next input if not the last one
      if (index < this.length - 1) {
        this.focusNextInput(index + 1);
      }

      // Check if OTP is complete
      if (this.value.length === this.length) {
        this.otpComplete.emit(this.value);
      }
    }
    // Block non-numeric characters except navigation
    else if (!['Tab', 'Enter', 'Escape', 'Delete'].includes(event.key)) {
      event.preventDefault();
    }
  }

  onPaste(event: ClipboardEvent) {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    const numericData = pastedData.replace(/\D/g, '');
    
    // Fill the inputs with pasted data
    for (let i = 0; i < Math.min(numericData.length, this.length); i++) {
      this.digits[i] = numericData[i];
    }
    
    this.updateValue();
    this.updateHiddenInput();
    this.onChange(this.value);
    this.onTouched();

    if (this.value.length === this.length) {
      this.otpComplete.emit(this.value);
    }
  }

  onFocus(index: number) {
    this.activeIndex = index;
    // When focusing on any visible input, also focus the hidden input for auto-fill
    this.focusHiddenInput();
  }

  onAutoFillInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const inputValue = target.value.replace(/\D/g, '');
    
    // Distribute the auto-filled value to individual inputs
    for (let i = 0; i < this.length; i++) {
      this.digits[i] = inputValue[i] || '';
    }
    
    this.updateValue();
    this.onChange(this.value);
    this.onTouched();

    // Emit when OTP is complete
    if (this.value.length === this.length) {
      this.otpComplete.emit(this.value);
    }
  }

  onResendClick() {
    // Clear all inputs
    this.digits = new Array(this.length).fill('');
    this.updateValue();
    this.updateHiddenInput();
    
    // Force change detection
    this.cd.detectChanges();
    
    this.resendOtp.emit();
    this.startTimer();
    
    // Focus first input
    this.focusNextInput(0);
  }

  get isComplete(): boolean {
    return this.value.length === this.length;
  }

  get isTimerExpired(): boolean {
    return this.timeLeft === 0;
  }

  private focusHiddenInput() {
    const hiddenInput = document.querySelector('.hidden-otp-input') as HTMLInputElement;
    if (hiddenInput) {
      hiddenInput.focus();
    }
  }

  private updateValue() {
    this.value = this.digits.join('');
  }

  private updateHiddenInput() {
    const hiddenInput = document.querySelector('.hidden-otp-input') as HTMLInputElement;
    if (hiddenInput) {
      hiddenInput.value = this.value;
    }
  }

  private focusNextInput(index: number) {
    setTimeout(() => {
      const inputs = this.otpInputs.toArray();
      if (inputs[index]) {
        inputs[index].nativeElement.focus();
      }
    }, 0);
  }

  private focusPrevInput(index: number) {
    setTimeout(() => {
      const inputs = this.otpInputs.toArray();
      if (inputs[index]) {
        inputs[index].nativeElement.focus();
        inputs[index].nativeElement.select();
      }
    }, 0);
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.value = value || '';
    // Update digits array from the value
    for (let i = 0; i < this.length; i++) {
      this.digits[i] = this.value[i] || '';
    }
    // Update hidden input
    this.updateHiddenInput();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    // Implementation for disabled state if needed
  }
}