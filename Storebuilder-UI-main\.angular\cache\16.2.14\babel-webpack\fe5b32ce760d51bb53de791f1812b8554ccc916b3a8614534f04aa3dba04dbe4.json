{"ast": null, "code": "import { catchError, map, of } from 'rxjs';\nimport { v4 as uuidv4 } from \"uuid\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nexport let ProductLogicService = /*#__PURE__*/(() => {\n  class ProductLogicService {\n    store;\n    messageService;\n    productService;\n    cartService;\n    router;\n    translate;\n    authTokenService;\n    mainDataService;\n    cartProductList;\n    favouritesProductList;\n    compareProductList;\n    lang;\n    isAuthUser;\n    sessionId = \"\";\n    addedProducts = Array();\n    constructor(store, messageService, productService, cartService, router, translate, authTokenService, mainDataService) {\n      this.store = store;\n      this.messageService = messageService;\n      this.productService = productService;\n      this.cartService = cartService;\n      this.router = router;\n      this.translate = translate;\n      this.authTokenService = authTokenService;\n      this.mainDataService = mainDataService;\n      this.cartProductList = [];\n      this.favouritesProductList = [];\n      this.compareProductList = [];\n      this.lang = this.store.get('lang');\n      this.authTokenService.authTokenData.subscribe(message => this.isAuthUser = message);\n    }\n    modifyCart(product, type, newProducts) {\n      this.getStoreData();\n      return this.cartModificationApi(product, type, newProducts);\n    }\n    emptyCart(cartId) {\n      this.cartService.emptyCart(cartId).subscribe({\n        next: res => {\n          this.cartProductList = res.data?.cartItems;\n          // if (res.data.cartItems !== undefined && res.data.cartItems !== null && res.data.cartItems.length === 0) {\n          //   localStorage.removeItem('sessionId');\n          // }\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n          this.setCartToStore(this.cartProductList, \"\", true);\n          this.messageService.add({\n            severity: 'success',\n            summary: this.translate.instant('ResponseMessages.cart'),\n            detail: this.translate.instant('ResponseMessages.successfullyUpdatedFromCart')\n          });\n          return true;\n        },\n        error: err => {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n          return false;\n        }\n      });\n      return of(false);\n    }\n    getStoreData() {\n      this.authTokenService.authTokenData.subscribe(message => this.isAuthUser = message);\n      this.lang = this.store.get('lang');\n      this.cartProductList = this.store.get('cartProducts');\n      this.favouritesProductList = this.store.get('favouritesProducts');\n      this.compareProductList = this.store.get('compareProducts');\n    }\n    userCart(product, type, newProducts) {\n      return this.cartModificationApi(product, type, newProducts);\n    }\n    guestCart(product, type, newProducts) {\n      return this.cartModificationApi(product, type, newProducts);\n    }\n    cartModificationApi(product, type, newProducts) {\n      const sessionId = this.getSessionId();\n      switch (type) {\n        case 'add':\n          return this.addToCart(product, sessionId, newProducts);\n        case 'delete':\n          return this.removeFromCart(product, newProducts);\n        case 'update':\n          return this.updateCart(product, sessionId, newProducts);\n        default:\n          return of(false);\n      }\n    }\n    getSessionId() {\n      let sessionId = localStorage.getItem('sessionId') ?? '';\n      if (!sessionId) {\n        sessionId = GuidGenerator.newGuid();\n        localStorage.setItem('sessionId', sessionId);\n      }\n      return sessionId;\n    }\n    addToCart(product, sessionId, newProducts) {\n      product.sessionId = sessionId;\n      return this.cartService.addToCart(product).pipe(map(res => {\n        if (res?.success) {\n          this.handleCartModificationSuccess(res, product, newProducts);\n          return true;\n        } else {\n          this.handleCartModificationError(res);\n          return false;\n        }\n      }), catchError(err => this.handleCartModificationError(err)));\n    }\n    removeFromCart(product, newProducts) {\n      return this.cartService.removeItemFromCart(product).pipe(map(res => {\n        this.handleCartModificationSuccess(res, product, newProducts);\n        return true;\n      }), catchError(err => this.handleCartModificationError(err)));\n    }\n    updateCart(product, sessionId, newProducts) {\n      product.sessionId = sessionId;\n      return this.cartService.updateCart(product).pipe(map(res => {\n        if (!res.success) {\n          this.handleCartModificationError(res);\n          return false;\n        }\n        this.handleCartModificationSuccess(res, product, newProducts);\n        return true;\n      }), catchError(err => this.handleCartModificationError(err)));\n    }\n    handleCartModificationSuccess(res, product, newProducts) {\n      this.cartProductList = res.data?.cartItems;\n      this.setCartToStore(this.cartProductList, product);\n      this.mainDataService.setCartLenghtData(res.data.cartItems.length);\n      if (newProducts === 'buynow') {\n        this.router.navigate(['/cart']);\n      }\n      this.messageService.add({\n        severity: 'success',\n        summary: this.translate.instant('ResponseMessages.cart'),\n        detail: this.translate.instant('ResponseMessages.successfullyAddedToCart')\n      });\n    }\n    handleCartModificationError(err) {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: err.message\n      });\n      return of(false);\n    }\n    setCartToStore(cartList, product, removed) {\n      this.addedProducts = Array();\n      this.store.set('cartProducts', cartList);\n      this.store.set('cartProductSuccess', product);\n      this.addedProducts = cartList;\n      localStorage.setItem('addedProducts', JSON.stringify(this.addedProducts));\n    }\n    static ɵfac = function ProductLogicService_Factory(t) {\n      return new (t || ProductLogicService)(i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i1.ProductService), i0.ɵɵinject(i1.CartService), i0.ɵɵinject(i3.Router), i0.ɵɵinject(i4.TranslateService), i0.ɵɵinject(i1.AuthTokenService), i0.ɵɵinject(i1.MainDataService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductLogicService,\n      factory: ProductLogicService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ProductLogicService;\n})();\nclass GuidGenerator {\n  static newGuid() {\n    return uuidv4();\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}