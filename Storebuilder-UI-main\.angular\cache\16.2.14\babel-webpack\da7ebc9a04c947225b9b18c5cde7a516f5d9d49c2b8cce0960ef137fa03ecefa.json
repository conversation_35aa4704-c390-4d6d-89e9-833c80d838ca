{"ast": null, "code": "import { ElementRef, PLATFORM_ID } from '@angular/core';\nimport { NavigationEnd } from \"@angular/router\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { ConfigurationKeys } from \"@core/enums\";\nimport { TenantRecords } from \"@core/interface\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-cookie-service\";\nimport * as i7 from \"@core/services/device-detection.service\";\nimport * as i8 from \"primeng/toast\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"../../../../shared/components/loader/loader.component\";\nconst _c0 = [\"scroll\"];\nfunction MainLandingComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass GuidGenerator {\n  static newGuid() {\n    return uuidv4();\n  }\n}\nexport class MainLandingComponent {\n  constructor(store, cartService, meta, messageService, mainDataService, translate, router, cookieService, authService, authTokenService, cd, tenantService, appDataService, languageService, platformId, commonService, deviceDetectionService) {\n    this.store = store;\n    this.cartService = cartService;\n    this.meta = meta;\n    this.messageService = messageService;\n    this.mainDataService = mainDataService;\n    this.translate = translate;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authService = authService;\n    this.authTokenService = authTokenService;\n    this.cd = cd;\n    this.tenantService = tenantService;\n    this.appDataService = appDataService;\n    this.languageService = languageService;\n    this.platformId = platformId;\n    this.commonService = commonService;\n    this.deviceDetectionService = deviceDetectionService;\n    this.lang = 'en';\n    this.storeNames = [];\n    this.display = true;\n    this.loading = false;\n    this.seoTitle = '';\n    this.seoDesc = '';\n    this.isShop = false;\n    this.isRefreshTokenCalled = false;\n    this.isRenderApplication = false;\n    this.cartListCount = 0;\n    this.cartListData = [];\n    this.country = '';\n    translate.addLangs(['en', 'ar']);\n    this.themeApplied = false;\n    translate.setDefaultLang('en');\n  }\n  ngOnInit() {\n    const sessionId = window.localStorage.getItem('sessionId');\n    if (!sessionId) {\n      localStorage.setItem('sessionId', GuidGenerator.newGuid());\n    }\n    this.triggerInitialCalls();\n    this.getUserIpAddress();\n    this.getDeviceInfo();\n  }\n  getUserIpAddress() {\n    this.authService.getUserIPAddress().subscribe({\n      next: res => {\n        if (res?.data?.ipAddress) {\n          const userIp = res?.data?.ipAddress.replace('::ffff:', '') || '';\n          this.store.set('userIP', userIp);\n        }\n      },\n      error: err => {\n        console.warn('Error in Fetching User IP Address', err);\n      }\n    });\n  }\n  getDeviceInfo() {\n    const device_info = {\n      deviceType: this.deviceDetectionService.getDeviceType(),\n      deviceId: this.deviceDetectionService.getDeviceId()\n    };\n    this.store.set('deviceInfo', device_info);\n  }\n  triggerInitialCalls() {\n    this.routeToTop();\n    this.createStores();\n    // this.getCategories()\n    // this.lang = this.store.get('lang') || 'en';\n    // remove extra language call\n    // this.setLangSettings();\n    this.getMainData();\n  }\n  routeToTop() {\n    this.router.events.subscribe(event => {\n      if (!(event instanceof NavigationEnd)) {\n        return;\n      }\n      window.scrollTo(0, 0);\n    });\n  }\n  createStores() {\n    this.storeNames = [{\n      name: 'timeInterval',\n      data: null,\n      localStore: true\n    }, {\n      name: 'orderData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'transactionData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'userPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'mainData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'allCountryTenants',\n      data: null,\n      localStore: true\n    }, {\n      name: 'tenantId',\n      data: null,\n      localStore: true\n    }, {\n      name: 'shipmentCost',\n      data: null,\n      localStore: true\n    }, {\n      name: 'isShop',\n      data: null,\n      localStorage: true\n    }, {\n      name: 'currency',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryCode',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'profile',\n      data: null,\n      localStore: true\n    }, {\n      name: 'categories',\n      data: [],\n      localStore: true\n    }, {\n      name: 'notifications',\n      data: null,\n      localStore: false\n    }, {\n      name: 'cartProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'favouritesProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'compareProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'cartProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'favouritesProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'compareProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'allowedFeature',\n      data: null,\n      localStore: false\n    }, {\n      name: 'userIP',\n      data: null,\n      localStore: false\n    }, {\n      name: 'deviceInfo',\n      data: null,\n      localStore: false\n    }, {\n      name: 'checkoutData',\n      data: {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      },\n      localStore: true\n    }, {\n      name: 'search',\n      data: '',\n      localStore: false\n    }, {\n      name: 'loading',\n      data: false,\n      localStore: true\n    }, {\n      name: 'verificationCode',\n      data: '',\n      localStore: true\n    }, {\n      name: 'sessionId',\n      data: '',\n      localStore: true\n    }];\n    /*Create Dynamic BehaviorSubject at Store*/\n    this.storeNames.forEach(item => {\n      this.store.createNewStore(item.name, item.data, item.localStore);\n    });\n  }\n  getConfigurationDecimal() {\n    const configuration = this.appDataService.configuration;\n    this.isRenderApplication = true;\n    const findAndSetLocalStorage = key => {\n      const record = configuration.records?.find(x => x.key === key);\n      if (record) localStorage.setItem(key, record.value);\n    };\n    findAndSetLocalStorage(ConfigurationKeys.CurrencyDecimal);\n    findAndSetLocalStorage(ConfigurationKeys.CountryPhone);\n    findAndSetLocalStorage(ConfigurationKeys.PhoneLength);\n    findAndSetLocalStorage(ConfigurationKeys.PhoneNumberMask);\n    findAndSetLocalStorage(ConfigurationKeys.Currency);\n    findAndSetLocalStorage(ConfigurationKeys.EmailRequired);\n    findAndSetLocalStorage(ConfigurationKeys.DisableCents);\n    findAndSetLocalStorage(ConfigurationKeys.CustomerAddressLandmarkRequired);\n    localStorage.setItem('emailRequired', 'false');\n    // localStorage.setItem('disableCent', 'false');\n    // Commented out extra API calls\n    // this.getCart();\n    this.getAllCountryTenants();\n    this.getCategories();\n    this.isRenderApplication = true;\n  }\n  getCart() {\n    let cartData = {\n      sessionId: localStorage.getItem('sessionId') ?? ''\n    };\n    let applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo != '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getCart(cartData).subscribe({\n      next: res => {\n        this.cartListCount = 0;\n        this.cartListData = [];\n        if (res?.data?.records?.length) {\n          this.cartListCount = 0;\n          if (res.data.records[0].cartDetails.length) {\n            this.cartListCount = res.data.records[0].cartDetails.length;\n            this.cartListData = res.data.records[0].cartDetails;\n          }\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n          }\n          this.mainDataService.setCartLenghtData(this.cartListCount);\n          this.mainDataService.setCartItemsData(this.cartListData);\n        } else {\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n        }\n      },\n      error: err => {},\n      complete: () => {}\n    });\n  }\n  getMainData() {\n    this.getInitialDataConfig();\n  }\n  getInitialDataConfig() {\n    const initialData = this.appDataService.initialData;\n    this.getConfigurationDecimal();\n    this.handleFeatureByTenant(initialData.featureByTenantRes);\n    this.setMainData(initialData.result.records);\n    this.setShopDetails(initialData.isShop, initialData.shopProductSetting);\n    this.tenantService.setHeader(true);\n  }\n  handleFeatureByTenant(featureByTenantRes) {\n    if (featureByTenantRes.length) {\n      localStorage.setItem('Allowed-feature', JSON.stringify(featureByTenantRes));\n      const featureList = JSON.parse(localStorage.getItem('Allowed-feature') ?? '[]');\n      const isSellerHub = featureList.find(item => item.portalName === 'Seller-Hub');\n      localStorage.setItem('isSellerHub', isSellerHub ? 'seller-hub' : 'not-seller-hub');\n    } else {\n      localStorage.setItem('isSellerHub', 'not-seller-hub');\n    }\n    this.store.set('allowedFeature', featureByTenantRes);\n  }\n  setMainData(resultRecords) {\n    if (resultRecords !== undefined) {\n      this.store.set('mainData', resultRecords);\n      Object.entries(resultRecords).forEach(([subkey, value]) => {\n        const key = parseInt(subkey);\n        const record = resultRecords[key];\n        if (record.key === 'AppTheme') {\n          const dynamicStyle = record.displayName;\n          if (dynamicStyle != null && dynamicStyle !== '') {\n            const dynamicStyleObj = JSON.parse(dynamicStyle);\n            this.applyStyle(dynamicStyleObj);\n          }\n        } else {\n          this.themeApplied = true;\n          this.cd.detectChanges();\n        }\n      });\n    }\n  }\n  setShopDetails(isShop, shopProductSetting) {\n    this.isShop = isShop;\n    if (this.isShop) {\n      this.seoTitle = shopProductSetting.seoTitle;\n      this.seoDesc = shopProductSetting.seoDescription;\n      this.meta.updateTag({\n        name: this.seoTitle,\n        content: this.seoDesc\n      });\n    } else {\n      this.meta.updateTag({\n        name: 'Description',\n        content: 'MarketPlace, Buy, products'\n      });\n    }\n  }\n  getCategories() {\n    const categories = this.appDataService.categories;\n    if (categories.records != undefined) {\n      this.store.set('categories', categories.records);\n      localStorage.setItem('allCategories', JSON.stringify(categories.records));\n    }\n  }\n  getAllCountryTenants() {\n    const tenants = this.appDataService.tenants;\n    if (tenants.records != undefined) {\n      let tenantId = localStorage.getItem('tenantId');\n      let data = tenants.records;\n      let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n      localStorage.setItem('isoCode', arr?.isoCode);\n      this.store.set('allCountryTenants', tenants.records);\n    }\n  }\n  appendCssPropertyValue(key, value, dynamicStyleObj, sub) {\n    if (value) {\n      Object.entries(value).forEach(([subKey, subSubValue]) => {\n        if (value[subKey]) {\n          if (isPlatformBrowser(this.platformId)) {\n            document.documentElement.style.setProperty('--' + subKey, value[subKey]);\n          }\n        } else if (subKey && dynamicStyleObj && dynamicStyleObj[key]) {\n          this.appendCssPropertyValue(subKey, dynamicStyleObj[key][subKey], dynamicStyleObj, true);\n        }\n      });\n    }\n  }\n  applyStyle(dynamicStyleObj) {\n    Object.entries(dynamicStyleObj).forEach(([key, value]) => {\n      if (value && dynamicStyleObj[key] && (dynamicStyleObj[key]?.length > 2 || Object.entries(dynamicStyleObj[key])?.length >= 1)) {\n        if (dynamicStyleObj[key] != null && (dynamicStyleObj[key][0]?.length == 1 || Number(dynamicStyleObj[key][0]))) {\n          if (isPlatformBrowser(this.platformId)) {\n            document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n          }\n        } else {\n          this.appendCssPropertyValue(key, dynamicStyleObj[key], dynamicStyleObj, false);\n        }\n      } else {\n        if (isPlatformBrowser(this.platformId)) {\n          document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n        }\n      }\n    });\n    this.themeApplied = true;\n    this.cd.detectChanges();\n  }\n  scrollToTop() {\n    this.scroll.nativeElement.scrollTop = 0;\n  }\n  signOut() {\n    this.commonService.logOut();\n    this.router.navigate(['/login']);\n    this.getMainData();\n  }\n  static #_ = this.ɵfac = function MainLandingComponent_Factory(t) {\n    return new (t || MainLandingComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.Meta), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.CookieService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.TenantService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i7.DeviceDetectionService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MainLandingComponent,\n    selectors: [[\"app-main-landing\"]],\n    viewQuery: function MainLandingComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroll = _t.first);\n      }\n    },\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"main-content\"], [\"position\", \"top-right\", \"sticky\", \"true\", 3, \"autoZIndex\"], [4, \"ngIf\"]],\n    template: function MainLandingComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-mtn-loader\")(2, \"p-toast\", 1);\n        i0.ɵɵtemplate(3, MainLandingComponent_ng_container_3_Template, 2, 0, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"autoZIndex\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isRenderApplication);\n      }\n    },\n    dependencies: [i8.Toast, i9.NgIf, i5.RouterOutlet, i10.LoaderComponent],\n    styles: [\".container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n}\\n\\n.page[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  min-height: 100vh;\\n}\\n\\n  .p-toast-top-right {\\n  top: 7rem !important;\\n  z-index: 10000 !important;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n    .p-toast-top-right {\\n    top: 0.8rem !important;\\n    right: 0px !important;\\n    left: 0px !important;\\n    z-index: 10000 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    min-height: 55vh;\\n  }\\n}\\n.main-content[_ngcontent-%COMP%]:has(div.not-found) {\\n  min-height: 40vh !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGFuZGluZy9jb21wb25lbnRzL21haW4tbGFuZGluZy9tYWluLWxhbmRpbmcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLDhCQUFBO0VBQ0EsaUJBQUE7QUFDRjs7QUFFQTtFQUNFLG9CQUFBO0VBQ0EseUJBQUE7QUFDRjs7QUFFQTtFQUVJO0lBQ0Usc0JBQUE7SUFDQSxxQkFBQTtJQUNBLG9CQUFBO0lBQ0EseUJBQUE7RUFBSjtFQUlBO0lBQ0UsZ0JBQUE7RUFGRjtBQUNGO0FBTUE7RUFDRSwyQkFBQTtBQUpGIiwic291cmNlc0NvbnRlbnQiOlsiLmNvbnRhaW5lciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIG92ZXJmbG93OiBhdXRvO1xyXG59XHJcblxyXG4ucGFnZSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICBtaW4taGVpZ2h0OiAxMDB2aDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5wLXRvYXN0LXRvcC1yaWdodCB7XHJcbiAgdG9wOiA3cmVtICFpbXBvcnRhbnQ7XHJcbiAgei1pbmRleDogMTAwMDAgIWltcG9ydGFudDtcclxufVxyXG5cclxuQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiA3ODZweCkge1xyXG4gIDo6bmctZGVlcCB7XHJcbiAgICAucC10b2FzdC10b3AtcmlnaHQge1xyXG4gICAgICB0b3A6IDAuOHJlbSAhaW1wb3J0YW50O1xyXG4gICAgICByaWdodDogMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgIGxlZnQ6IDBweCAhaW1wb3J0YW50O1xyXG4gICAgICB6LWluZGV4OiAxMDAwMCAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1haW4tY29udGVudCB7XHJcbiAgICBtaW4taGVpZ2h0OiA1NXZoO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbi5tYWluLWNvbnRlbnQ6aGFzKGRpdi5ub3QtZm91bmQpIHtcclxuICBtaW4taGVpZ2h0OiA0MHZoICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["ElementRef", "PLATFORM_ID", "NavigationEnd", "v4", "uuidv4", "Configuration<PERSON>eys", "TenantRecords", "isPlatformBrowser", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "GuidGenerator", "newGuid", "MainLandingComponent", "constructor", "store", "cartService", "meta", "messageService", "mainDataService", "translate", "router", "cookieService", "authService", "authTokenService", "cd", "tenantService", "appDataService", "languageService", "platformId", "commonService", "deviceDetectionService", "lang", "storeNames", "display", "loading", "seo<PERSON><PERSON>le", "seoDesc", "isShop", "isRefreshTokenCalled", "isRenderApplication", "cartListCount", "cartListData", "country", "addLangs", "themeApplied", "setDefaultLang", "ngOnInit", "sessionId", "window", "localStorage", "getItem", "setItem", "triggerInitialCalls", "getUserIpAddress", "getDeviceInfo", "getUserIPAddress", "subscribe", "next", "res", "data", "ip<PERSON><PERSON><PERSON>", "userIp", "replace", "set", "error", "err", "console", "warn", "device_info", "deviceType", "getDeviceType", "deviceId", "getDeviceId", "routeToTop", "createStores", "getMainData", "events", "event", "scrollTo", "name", "localStore", "shipping", "payment", "promo", "steps", "profile", "orderId", "for<PERSON>ach", "item", "createNewStore", "getConfigurationDecimal", "configuration", "findAndSetLocalStorage", "key", "record", "records", "find", "x", "value", "CurrencyDecimal", "CountryPhone", "PhoneLength", "PhoneNumberMask", "<PERSON><PERSON><PERSON><PERSON>", "EmailRequired", "DisableCents", "CustomerAddressLandmarkRequired", "getAllCountryTenants", "getCategories", "getCart", "cartData", "applyTo", "length", "cartDetails", "cartDetailsDPay", "concat", "setCartLenghtData", "setCartItemsData", "complete", "getInitialDataConfig", "initialData", "handleFeatureByTenant", "featureByTenantRes", "setMainData", "result", "setShopDetails", "shopProductSetting", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "featureList", "parse", "isSellerHub", "portalName", "resultRecords", "undefined", "Object", "entries", "subkey", "parseInt", "dynamicStyle", "displayName", "dynamicStyleObj", "applyStyle", "detectChanges", "seoDescription", "updateTag", "content", "categories", "tenants", "tenantId", "arr", "element", "isoCode", "appendCssPropertyV<PERSON>ue", "sub", "subKey", "subSubValue", "document", "documentElement", "style", "setProperty", "Number", "scrollToTop", "scroll", "nativeElement", "scrollTop", "signOut", "logOut", "navigate", "_", "ɵɵdirectiveInject", "i1", "StoreService", "CartService", "i2", "Meta", "i3", "MessageService", "MainDataService", "i4", "TranslateService", "i5", "Router", "i6", "CookieService", "AuthService", "AuthTokenService", "ChangeDetectorRef", "TenantService", "AppDataService", "LanguageService", "CommonService", "i7", "DeviceDetectionService", "_2", "selectors", "viewQuery", "MainLandingComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "MainLandingComponent_ng_container_3_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\landing\\components\\main-landing\\main-landing.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\landing\\components\\main-landing\\main-landing.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, ElementRef, Inject, OnInit, PLATFORM_ID, ViewChild} from '@angular/core';\r\nimport {Meta} from \"@angular/platform-browser\";\r\nimport {MessageService} from \"primeng/api\";\r\nimport {TranslateService} from \"@ngx-translate/core\";\r\nimport {NavigationEnd, Router} from \"@angular/router\";\r\nimport {CookieService} from \"ngx-cookie-service\";\r\nimport {v4 as uuidv4} from \"uuid\";\r\n\r\nimport {ConfigurationKeys} from \"@core/enums\";\r\nimport {Categories, ConfigurationResponse, InitialData, TenantRecords} from \"@core/interface\";\r\nimport {\r\n  MainDataService,\r\n  LanguageService,\r\n  AppDataService,\r\n  AuthService,\r\n  TenantService,\r\n  AuthTokenService,\r\n  CartService,\r\n  StoreService, CommonService\r\n} from \"@core/services\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport { DeviceDetectionService } from '@core/services/device-detection.service';\r\n\r\n\r\nclass GuidGenerator {\r\n  static newGuid() {\r\n   return  uuidv4()\r\n  }\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'app-main-landing',\r\n  templateUrl: './main-landing.component.html',\r\n  styleUrls: ['./main-landing.component.scss']\r\n})\r\nexport class MainLandingComponent implements OnInit {\r\n  @ViewChild('scroll', {read: ElementRef}) public scroll!: ElementRef<any>;\r\n  lang: string = 'en';\r\n  storeNames: Array<any> = [];\r\n  display: boolean = true;\r\n  loading: boolean = false;\r\n  themeApplied: boolean;\r\n  seoTitle: string = '';\r\n  seoDesc: string = '';\r\n  isShop: boolean = false;\r\n  isRefreshTokenCalled: boolean = false;\r\n  isRenderApplication: boolean = false;\r\n  bg_color: any;\r\n  cartListCount: any = 0;\r\n  cartListData: any = [];\r\n  public country: string = '';\r\n\r\n  constructor(private store: StoreService,\r\n              private cartService: CartService,\r\n              private meta: Meta,\r\n              private messageService: MessageService,\r\n              private mainDataService: MainDataService,\r\n              private translate: TranslateService,\r\n              private router: Router,\r\n              private cookieService: CookieService,\r\n              private authService: AuthService,\r\n              private authTokenService: AuthTokenService,\r\n              private cd: ChangeDetectorRef,\r\n              private tenantService: TenantService,\r\n              private appDataService: AppDataService,\r\n              private languageService:LanguageService,\r\n              @Inject(PLATFORM_ID) private platformId: any,\r\n              private commonService: CommonService,\r\n              private deviceDetectionService:DeviceDetectionService) {\r\n    translate.addLangs(['en', 'ar']);\r\n    this.themeApplied = false;\r\n\r\n    translate.setDefaultLang('en');\r\n\r\n\r\n  }\r\n\r\n  ngOnInit(): void {\r\n\r\n    const sessionId = window.localStorage.getItem('sessionId');\r\n    if (!sessionId) {\r\n      localStorage.setItem('sessionId', GuidGenerator.newGuid());\r\n    }\r\n\r\n      this.triggerInitialCalls();\r\n      this.getUserIpAddress();\r\n      this.getDeviceInfo();\r\n\r\n  }\r\n  getUserIpAddress(){\r\n    this.authService.getUserIPAddress().subscribe({\r\n      next:(res:any)=>{\r\n        if(res?.data?.ipAddress){\r\n          const userIp = res?.data?.ipAddress.replace('::ffff:','') || ''\r\n         this.store.set('userIP',userIp)\r\n        }\r\n\r\n      },error:(err)=>{\r\n        console.warn('Error in Fetching User IP Address',err)\r\n\r\n      }\r\n    })\r\n  }\r\n  getDeviceInfo(){\r\n    const device_info = {\r\n      deviceType:this.deviceDetectionService.getDeviceType(),\r\n      deviceId:this.deviceDetectionService.getDeviceId()\r\n    }\r\n    this.store.set('deviceInfo',device_info)\r\n  }\r\n  triggerInitialCalls() {\r\n    this.routeToTop();\r\n    this.createStores();\r\n    // this.getCategories()\r\n    // this.lang = this.store.get('lang') || 'en';\r\n    // remove extra language call\r\n    // this.setLangSettings();\r\n    this.getMainData();\r\n\r\n  }\r\n\r\n  routeToTop(): void {\r\n    this.router.events.subscribe((event) => {\r\n      if (!(event instanceof NavigationEnd)) {\r\n        return;\r\n      }\r\n      window.scrollTo(0, 0);\r\n    });\r\n  }\r\n\r\n  createStores(): void {\r\n\r\n    this.storeNames = [\r\n\r\n\r\n      {name: 'timeInterval', data: null, localStore: true},\r\n      {name: 'orderData', data: null, localStore: true},\r\n      {name: 'transactionData', data: null, localStore: true},\r\n      {name: 'userPhone', data: null, localStore: true},\r\n      {name: 'mainData', data: null, localStore: true},\r\n      {name: 'allCountryTenants', data: null, localStore: true},\r\n      {name: 'tenantId', data: null, localStore: true},\r\n      {name: 'shipmentCost', data: null, localStore: true},\r\n      {name: 'isShop', data: null, localStorage: true},\r\n      {name: 'currency', data: null, localStore: true},\r\n      {name: 'countryCode', data: null, localStore: true},\r\n      {name: 'countryPhone', data: null, localStore: true},\r\n      {name: 'profile', data: null, localStore: true},\r\n      {name: 'categories', data: [], localStore: true},\r\n      {name: 'notifications', data: null, localStore: false},\r\n      {name: 'cartProducts', data: [], localStore: true},\r\n      {name: 'favouritesProducts', data: [], localStore: true},\r\n      {name: 'compareProducts', data: [], localStore: true},\r\n      {name: 'cartProductSuccess', data: null, localStore: false},\r\n      {name: 'favouritesProductSuccess', data: null, localStore: false},\r\n      {name: 'compareProductSuccess', data: null, localStore: false},\r\n      {name: 'allowedFeature', data: null, localStore: false},\r\n      {name: 'userIP', data: null, localStore: false},\r\n      {name: 'deviceInfo', data: null, localStore: false},\r\n      {\r\n        name: 'checkoutData',\r\n        data: {\r\n          shipping: null,\r\n          payment: null,\r\n          promo: null,\r\n          steps: null,\r\n          profile: null,\r\n          orderId: null,\r\n        },\r\n        localStore: true,\r\n      },\r\n      {name: 'search', data: '', localStore: false},\r\n      {name: 'loading', data: false, localStore: true},\r\n      {name: 'verificationCode', data: '', localStore: true},\r\n      {name: 'sessionId', data: '', localStore: true},\r\n    ];\r\n\r\n    /*Create Dynamic BehaviorSubject at Store*/\r\n    this.storeNames.forEach((item) => {\r\n      this.store.createNewStore(item.name, item.data, item.localStore);\r\n    });\r\n  }\r\n\r\n  getConfigurationDecimal() {\r\n\r\n    const configuration: ConfigurationResponse = this.appDataService.configuration;\r\n    this.isRenderApplication = true;\r\n\r\n    const findAndSetLocalStorage = (key: string): void => {\r\n      const record = configuration.records?.find((x: any) => x.key === key);\r\n      if (record) localStorage.setItem(key, record.value);\r\n    };\r\n\r\n    findAndSetLocalStorage(ConfigurationKeys.CurrencyDecimal);\r\n    findAndSetLocalStorage(ConfigurationKeys.CountryPhone);\r\n    findAndSetLocalStorage(ConfigurationKeys.PhoneLength);\r\n    findAndSetLocalStorage(ConfigurationKeys.PhoneNumberMask);\r\n    findAndSetLocalStorage(ConfigurationKeys.Currency);\r\n    findAndSetLocalStorage(ConfigurationKeys.EmailRequired);\r\n    findAndSetLocalStorage(ConfigurationKeys.DisableCents);\r\n    findAndSetLocalStorage(ConfigurationKeys.CustomerAddressLandmarkRequired);\r\n\r\n    localStorage.setItem('emailRequired', 'false');\r\n    // localStorage.setItem('disableCent', 'false');\r\n\r\n    // Commented out extra API calls\r\n    // this.getCart();\r\n    this.getAllCountryTenants();\r\n    this.getCategories();\r\n    this.isRenderApplication = true;\r\n  }\r\n\r\n\r\n  getCart(): void {\r\n    let cartData : any = {\r\n      sessionId: localStorage.getItem('sessionId') ?? '',\r\n    };\r\n    let applyTo  = localStorage.getItem('apply-to');\r\n    if(applyTo && applyTo != ''){\r\n      cartData['applyTo'] = applyTo\r\n    }\r\n    this.cartService.getCart(cartData).subscribe({\r\n      next: (res: any) => {\r\n        this.cartListCount = 0;\r\n        this.cartListData = [];\r\n        if (res?.data?.records?.length) {\r\n          this.cartListCount = 0;\r\n          if (res.data.records[0].cartDetails.length) {\r\n            this.cartListCount = res.data.records[0].cartDetails.length;\r\n            this.cartListData = res.data.records[0].cartDetails;\r\n\r\n          }\r\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\r\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\r\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay)\r\n          }\r\n          this.mainDataService.setCartLenghtData(this.cartListCount);\r\n          this.mainDataService.setCartItemsData(this.cartListData);\r\n\r\n        } else {\r\n          this.mainDataService.setCartLenghtData(0);\r\n          this.mainDataService.setCartItemsData([]);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n      },\r\n      complete: () => {\r\n      }\r\n    });\r\n  }\r\n\r\n  getMainData(): void {\r\n    this.getInitialDataConfig()\r\n  }\r\n\r\n  getInitialDataConfig() {\r\n    const initialData: InitialData = this.appDataService.initialData;\r\n    this.getConfigurationDecimal();\r\n    this.handleFeatureByTenant(initialData.featureByTenantRes);\r\n    this.setMainData(initialData.result.records);\r\n    this.setShopDetails(initialData.isShop, initialData.shopProductSetting);\r\n\r\n    this.tenantService.setHeader(true);\r\n  }\r\n\r\n  private handleFeatureByTenant(featureByTenantRes: any[]): void {\r\n    if (featureByTenantRes.length) {\r\n      localStorage.setItem('Allowed-feature', JSON.stringify(featureByTenantRes));\r\n      const featureList: any = JSON.parse(localStorage.getItem('Allowed-feature') ?? '[]');\r\n      const isSellerHub = featureList.find((item: any) => item.portalName === 'Seller-Hub');\r\n\r\n      localStorage.setItem('isSellerHub', isSellerHub ? 'seller-hub' : 'not-seller-hub');\r\n    } else {\r\n      localStorage.setItem('isSellerHub', 'not-seller-hub');\r\n    }\r\n\r\n    this.store.set('allowedFeature', featureByTenantRes);\r\n  }\r\n\r\n  private setMainData(resultRecords: any): void {\r\n    if (resultRecords !== undefined) {\r\n      this.store.set('mainData', resultRecords);\r\n\r\n      Object.entries(resultRecords).forEach(([subkey, value]) => {\r\n        const key = parseInt(subkey);\r\n        const record = resultRecords[key];\r\n\r\n        if (record.key === 'AppTheme') {\r\n          const dynamicStyle = record.displayName;\r\n          if (dynamicStyle != null && dynamicStyle !== '') {\r\n            const dynamicStyleObj = JSON.parse(dynamicStyle);\r\n            this.applyStyle(dynamicStyleObj);\r\n          }\r\n        } else {\r\n          this.themeApplied = true;\r\n          this.cd.detectChanges();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private setShopDetails(isShop: boolean, shopProductSetting: any): void {\r\n    this.isShop = isShop;\r\n\r\n    if (this.isShop) {\r\n      this.seoTitle = shopProductSetting.seoTitle;\r\n      this.seoDesc = shopProductSetting.seoDescription;\r\n      this.meta.updateTag({ name: this.seoTitle, content: this.seoDesc });\r\n    } else {\r\n      this.meta.updateTag({\r\n        name: 'Description',\r\n        content: 'MarketPlace, Buy, products',\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getCategories(): void {\r\n    const categories: Categories = this.appDataService.categories;\r\n    if (categories.records != undefined) {\r\n      this.store.set('categories', categories.records);\r\n      localStorage.setItem('allCategories', JSON.stringify(categories.records));\r\n    }\r\n  }\r\n\r\n  getAllCountryTenants(): void {\r\n    const tenants = this.appDataService.tenants\r\n    if (tenants.records != undefined) {\r\n      let tenantId = localStorage.getItem('tenantId');\r\n      let data = tenants.records;\r\n      let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();\r\n      localStorage.setItem('isoCode', arr?.isoCode);\r\n      this.store.set('allCountryTenants', tenants.records);\r\n    }\r\n  }\r\n\r\n  appendCssPropertyValue(\r\n    key: any,\r\n    value: any,\r\n    dynamicStyleObj: any,\r\n    sub: boolean\r\n  ) {\r\n    if (value) {\r\n      Object.entries(value).forEach(([subKey, subSubValue]) => {\r\n        if (value[subKey]) {\r\n          if (isPlatformBrowser(this.platformId)) {\r\n            document.documentElement.style.setProperty(\r\n              '--' + subKey,\r\n              value[subKey]\r\n            );\r\n          }\r\n        } else if (subKey && dynamicStyleObj && dynamicStyleObj[key]) {\r\n          this.appendCssPropertyValue(\r\n            subKey,\r\n            dynamicStyleObj[key][subKey],\r\n            dynamicStyleObj,\r\n            true\r\n          );\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  applyStyle(dynamicStyleObj: any) {\r\n\r\n    Object.entries(dynamicStyleObj).forEach(([key, value]) => {\r\n      if (\r\n        value &&\r\n        dynamicStyleObj[key] &&\r\n        (dynamicStyleObj[key]?.length > 2 ||\r\n          Object.entries(dynamicStyleObj[key])?.length >= 1)\r\n      ) {\r\n        if (\r\n          dynamicStyleObj[key] != null &&\r\n          (dynamicStyleObj[key][0]?.length == 1 ||\r\n            Number(dynamicStyleObj[key][0]))\r\n        ) {\r\n          if (isPlatformBrowser(this.platformId)) {\r\n            document.documentElement.style.setProperty(\r\n              '--' + key,\r\n              dynamicStyleObj[key]\r\n            );\r\n          }\r\n\r\n        } else {\r\n          this.appendCssPropertyValue(\r\n            key,\r\n            dynamicStyleObj[key],\r\n            dynamicStyleObj,\r\n            false\r\n          );\r\n        }\r\n      } else {\r\n        if (isPlatformBrowser(this.platformId)) {\r\n          document.documentElement.style.setProperty(\r\n            '--' + key,\r\n            dynamicStyleObj[key]\r\n          );\r\n        }\r\n\r\n      }\r\n    });\r\n    this.themeApplied = true;\r\n    this.cd.detectChanges();\r\n\r\n  }\r\n\r\n  scrollToTop() {\r\n    this.scroll.nativeElement.scrollTop = 0;\r\n  }\r\n\r\n  signOut(): void {\r\n    this.commonService.logOut();\r\n    this.router.navigate(['/login']);\r\n    this.getMainData();\r\n  }\r\n}\r\n", "<div class=\"main-content\">\r\n  <app-mtn-loader></app-mtn-loader>\r\n  <p-toast position=\"top-right\" [autoZIndex]=\"true\"  sticky=\"true\"></p-toast>\r\n  <ng-container *ngIf=\"isRenderApplication\">\r\n    <router-outlet></router-outlet>\r\n  </ng-container>\r\n</div>\r\n\r\n"], "mappings": "AAAA,SAAsCA,UAAU,EAAkBC,WAAW,QAAkB,eAAe;AAI9G,SAAQC,aAAa,QAAe,iBAAiB;AAErD,SAAQC,EAAE,IAAIC,MAAM,QAAO,MAAM;AAEjC,SAAQC,iBAAiB,QAAO,aAAa;AAC7C,SAAwDC,aAAa,QAAO,iBAAiB;AAW7F,SAAQC,iBAAiB,QAAO,iBAAiB;;;;;;;;;;;;;;;ICjB/CC,EAAA,CAAAC,uBAAA,GAA0C;IACxCD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,qBAAA,EAAe;;;ADmBjB,MAAMC,aAAa;EACjB,OAAOC,OAAOA,CAAA;IACb,OAAQT,MAAM,EAAE;EACjB;;AASF,OAAM,MAAOU,oBAAoB;EAiB/BC,YAAoBC,KAAmB,EACnBC,WAAwB,EACxBC,IAAU,EACVC,cAA8B,EAC9BC,eAAgC,EAChCC,SAA2B,EAC3BC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB,EACxBC,gBAAkC,EAClCC,EAAqB,EACrBC,aAA4B,EAC5BC,cAA8B,EAC9BC,eAA+B,EACVC,UAAe,EACpCC,aAA4B,EAC5BC,sBAA6C;IAhB7C,KAAAhB,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACM,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,sBAAsB,GAAtBA,sBAAsB;IA/B1C,KAAAC,IAAI,GAAW,IAAI;IACnB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,OAAO,GAAY,KAAK;IAExB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,MAAM,GAAY,KAAK;IACvB,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAC,mBAAmB,GAAY,KAAK;IAEpC,KAAAC,aAAa,GAAQ,CAAC;IACtB,KAAAC,YAAY,GAAQ,EAAE;IACf,KAAAC,OAAO,GAAW,EAAE;IAmBzBvB,SAAS,CAACwB,QAAQ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,KAAK;IAEzBzB,SAAS,CAAC0B,cAAc,CAAC,IAAI,CAAC;EAGhC;EAEAC,QAAQA,CAAA;IAEN,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC1D,IAAI,CAACH,SAAS,EAAE;MACdE,YAAY,CAACE,OAAO,CAAC,WAAW,EAAEzC,aAAa,CAACC,OAAO,EAAE,CAAC;;IAG1D,IAAI,CAACyC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,aAAa,EAAE;EAExB;EACAD,gBAAgBA,CAAA;IACd,IAAI,CAAC/B,WAAW,CAACiC,gBAAgB,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAEC,GAAO,IAAG;QACd,IAAGA,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAC;UACtB,MAAMC,MAAM,GAAGH,GAAG,EAAEC,IAAI,EAAEC,SAAS,CAACE,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC,IAAI,EAAE;UAChE,IAAI,CAAChD,KAAK,CAACiD,GAAG,CAAC,QAAQ,EAACF,MAAM,CAAC;;MAGlC,CAAC;MAACG,KAAK,EAAEC,GAAG,IAAG;QACbC,OAAO,CAACC,IAAI,CAAC,mCAAmC,EAACF,GAAG,CAAC;MAEvD;KACD,CAAC;EACJ;EACAX,aAAaA,CAAA;IACX,MAAMc,WAAW,GAAG;MAClBC,UAAU,EAAC,IAAI,CAACvC,sBAAsB,CAACwC,aAAa,EAAE;MACtDC,QAAQ,EAAC,IAAI,CAACzC,sBAAsB,CAAC0C,WAAW;KACjD;IACD,IAAI,CAAC1D,KAAK,CAACiD,GAAG,CAAC,YAAY,EAACK,WAAW,CAAC;EAC1C;EACAhB,mBAAmBA,CAAA;IACjB,IAAI,CAACqB,UAAU,EAAE;IACjB,IAAI,CAACC,YAAY,EAAE;IACnB;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,EAAE;EAEpB;EAEAF,UAAUA,CAAA;IACR,IAAI,CAACrD,MAAM,CAACwD,MAAM,CAACpB,SAAS,CAAEqB,KAAK,IAAI;MACrC,IAAI,EAAEA,KAAK,YAAY7E,aAAa,CAAC,EAAE;QACrC;;MAEFgD,MAAM,CAAC8B,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ;EAEAJ,YAAYA,CAAA;IAEV,IAAI,CAAC1C,UAAU,GAAG,CAGhB;MAAC+C,IAAI,EAAE,cAAc;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACpD;MAACD,IAAI,EAAE,WAAW;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACjD;MAACD,IAAI,EAAE,iBAAiB;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACvD;MAACD,IAAI,EAAE,WAAW;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACjD;MAACD,IAAI,EAAE,UAAU;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAChD;MAACD,IAAI,EAAE,mBAAmB;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACzD;MAACD,IAAI,EAAE,UAAU;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAChD;MAACD,IAAI,EAAE,cAAc;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACpD;MAACD,IAAI,EAAE,QAAQ;MAAEpB,IAAI,EAAE,IAAI;MAAEV,YAAY,EAAE;IAAI,CAAC,EAChD;MAAC8B,IAAI,EAAE,UAAU;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAChD;MAACD,IAAI,EAAE,aAAa;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACnD;MAACD,IAAI,EAAE,cAAc;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACpD;MAACD,IAAI,EAAE,SAAS;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAC/C;MAACD,IAAI,EAAE,YAAY;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAChD;MAACD,IAAI,EAAE,eAAe;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EACtD;MAACD,IAAI,EAAE,cAAc;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAClD;MAACD,IAAI,EAAE,oBAAoB;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACxD;MAACD,IAAI,EAAE,iBAAiB;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACrD;MAACD,IAAI,EAAE,oBAAoB;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EAC3D;MAACD,IAAI,EAAE,0BAA0B;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EACjE;MAACD,IAAI,EAAE,uBAAuB;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EAC9D;MAACD,IAAI,EAAE,gBAAgB;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EACvD;MAACD,IAAI,EAAE,QAAQ;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EAC/C;MAACD,IAAI,EAAE,YAAY;MAAEpB,IAAI,EAAE,IAAI;MAAEqB,UAAU,EAAE;IAAK,CAAC,EACnD;MACED,IAAI,EAAE,cAAc;MACpBpB,IAAI,EAAE;QACJsB,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV;MACDN,UAAU,EAAE;KACb,EACD;MAACD,IAAI,EAAE,QAAQ;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAK,CAAC,EAC7C;MAACD,IAAI,EAAE,SAAS;MAAEpB,IAAI,EAAE,KAAK;MAAEqB,UAAU,EAAE;IAAI,CAAC,EAChD;MAACD,IAAI,EAAE,kBAAkB;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,EACtD;MAACD,IAAI,EAAE,WAAW;MAAEpB,IAAI,EAAE,EAAE;MAAEqB,UAAU,EAAE;IAAI,CAAC,CAChD;IAED;IACA,IAAI,CAAChD,UAAU,CAACuD,OAAO,CAAEC,IAAI,IAAI;MAC/B,IAAI,CAAC1E,KAAK,CAAC2E,cAAc,CAACD,IAAI,CAACT,IAAI,EAAES,IAAI,CAAC7B,IAAI,EAAE6B,IAAI,CAACR,UAAU,CAAC;IAClE,CAAC,CAAC;EACJ;EAEAU,uBAAuBA,CAAA;IAErB,MAAMC,aAAa,GAA0B,IAAI,CAACjE,cAAc,CAACiE,aAAa;IAC9E,IAAI,CAACpD,mBAAmB,GAAG,IAAI;IAE/B,MAAMqD,sBAAsB,GAAIC,GAAW,IAAU;MACnD,MAAMC,MAAM,GAAGH,aAAa,CAACI,OAAO,EAAEC,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACJ,GAAG,KAAKA,GAAG,CAAC;MACrE,IAAIC,MAAM,EAAE7C,YAAY,CAACE,OAAO,CAAC0C,GAAG,EAAEC,MAAM,CAACI,KAAK,CAAC;IACrD,CAAC;IAEDN,sBAAsB,CAACzF,iBAAiB,CAACgG,eAAe,CAAC;IACzDP,sBAAsB,CAACzF,iBAAiB,CAACiG,YAAY,CAAC;IACtDR,sBAAsB,CAACzF,iBAAiB,CAACkG,WAAW,CAAC;IACrDT,sBAAsB,CAACzF,iBAAiB,CAACmG,eAAe,CAAC;IACzDV,sBAAsB,CAACzF,iBAAiB,CAACoG,QAAQ,CAAC;IAClDX,sBAAsB,CAACzF,iBAAiB,CAACqG,aAAa,CAAC;IACvDZ,sBAAsB,CAACzF,iBAAiB,CAACsG,YAAY,CAAC;IACtDb,sBAAsB,CAACzF,iBAAiB,CAACuG,+BAA+B,CAAC;IAEzEzD,YAAY,CAACE,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC;IAC9C;IAEA;IACA;IACA,IAAI,CAACwD,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACrE,mBAAmB,GAAG,IAAI;EACjC;EAGAsE,OAAOA,CAAA;IACL,IAAIC,QAAQ,GAAS;MACnB/D,SAAS,EAAEE,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI;KACjD;IACD,IAAI6D,OAAO,GAAI9D,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC/C,IAAG6D,OAAO,IAAIA,OAAO,IAAI,EAAE,EAAC;MAC1BD,QAAQ,CAAC,SAAS,CAAC,GAAGC,OAAO;;IAE/B,IAAI,CAAChG,WAAW,CAAC8F,OAAO,CAACC,QAAQ,CAAC,CAACtD,SAAS,CAAC;MAC3CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAClB,aAAa,GAAG,CAAC;QACtB,IAAI,CAACC,YAAY,GAAG,EAAE;QACtB,IAAIiB,GAAG,EAAEC,IAAI,EAAEoC,OAAO,EAAEiB,MAAM,EAAE;UAC9B,IAAI,CAACxE,aAAa,GAAG,CAAC;UACtB,IAAIkB,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACkB,WAAW,CAACD,MAAM,EAAE;YAC1C,IAAI,CAACxE,aAAa,GAAGkB,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACkB,WAAW,CAACD,MAAM;YAC3D,IAAI,CAACvE,YAAY,GAAGiB,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACkB,WAAW;;UAGrD,IAAIvD,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACmB,eAAe,IAAIxD,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACmB,eAAe,CAACF,MAAM,EAAE;YACrF,IAAI,CAACxE,aAAa,IAAIkB,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACmB,eAAe,CAACF,MAAM;YAChE,IAAI,CAACvE,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC0E,MAAM,CAACzD,GAAG,CAACC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACmB,eAAe,CAAC;;UAEnF,IAAI,CAAChG,eAAe,CAACkG,iBAAiB,CAAC,IAAI,CAAC5E,aAAa,CAAC;UAC1D,IAAI,CAACtB,eAAe,CAACmG,gBAAgB,CAAC,IAAI,CAAC5E,YAAY,CAAC;SAEzD,MAAM;UACL,IAAI,CAACvB,eAAe,CAACkG,iBAAiB,CAAC,CAAC,CAAC;UACzC,IAAI,CAAClG,eAAe,CAACmG,gBAAgB,CAAC,EAAE,CAAC;;MAE7C,CAAC;MACDrD,KAAK,EAAGC,GAAQ,IAAI,CACpB,CAAC;MACDqD,QAAQ,EAAEA,CAAA,KAAK,CACf;KACD,CAAC;EACJ;EAEA3C,WAAWA,CAAA;IACT,IAAI,CAAC4C,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,MAAMC,WAAW,GAAgB,IAAI,CAAC9F,cAAc,CAAC8F,WAAW;IAChE,IAAI,CAAC9B,uBAAuB,EAAE;IAC9B,IAAI,CAAC+B,qBAAqB,CAACD,WAAW,CAACE,kBAAkB,CAAC;IAC1D,IAAI,CAACC,WAAW,CAACH,WAAW,CAACI,MAAM,CAAC7B,OAAO,CAAC;IAC5C,IAAI,CAAC8B,cAAc,CAACL,WAAW,CAACnF,MAAM,EAAEmF,WAAW,CAACM,kBAAkB,CAAC;IAEvE,IAAI,CAACrG,aAAa,CAACsG,SAAS,CAAC,IAAI,CAAC;EACpC;EAEQN,qBAAqBA,CAACC,kBAAyB;IACrD,IAAIA,kBAAkB,CAACV,MAAM,EAAE;MAC7B/D,YAAY,CAACE,OAAO,CAAC,iBAAiB,EAAE6E,IAAI,CAACC,SAAS,CAACP,kBAAkB,CAAC,CAAC;MAC3E,MAAMQ,WAAW,GAAQF,IAAI,CAACG,KAAK,CAAClF,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;MACpF,MAAMkF,WAAW,GAAGF,WAAW,CAAClC,IAAI,CAAER,IAAS,IAAKA,IAAI,CAAC6C,UAAU,KAAK,YAAY,CAAC;MAErFpF,YAAY,CAACE,OAAO,CAAC,aAAa,EAAEiF,WAAW,GAAG,YAAY,GAAG,gBAAgB,CAAC;KACnF,MAAM;MACLnF,YAAY,CAACE,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC;;IAGvD,IAAI,CAACrC,KAAK,CAACiD,GAAG,CAAC,gBAAgB,EAAE2D,kBAAkB,CAAC;EACtD;EAEQC,WAAWA,CAACW,aAAkB;IACpC,IAAIA,aAAa,KAAKC,SAAS,EAAE;MAC/B,IAAI,CAACzH,KAAK,CAACiD,GAAG,CAAC,UAAU,EAAEuE,aAAa,CAAC;MAEzCE,MAAM,CAACC,OAAO,CAACH,aAAa,CAAC,CAAC/C,OAAO,CAAC,CAAC,CAACmD,MAAM,EAAExC,KAAK,CAAC,KAAI;QACxD,MAAML,GAAG,GAAG8C,QAAQ,CAACD,MAAM,CAAC;QAC5B,MAAM5C,MAAM,GAAGwC,aAAa,CAACzC,GAAG,CAAC;QAEjC,IAAIC,MAAM,CAACD,GAAG,KAAK,UAAU,EAAE;UAC7B,MAAM+C,YAAY,GAAG9C,MAAM,CAAC+C,WAAW;UACvC,IAAID,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;YAC/C,MAAME,eAAe,GAAGd,IAAI,CAACG,KAAK,CAACS,YAAY,CAAC;YAChD,IAAI,CAACG,UAAU,CAACD,eAAe,CAAC;;SAEnC,MAAM;UACL,IAAI,CAAClG,YAAY,GAAG,IAAI;UACxB,IAAI,CAACpB,EAAE,CAACwH,aAAa,EAAE;;MAE3B,CAAC,CAAC;;EAEN;EAEQnB,cAAcA,CAACxF,MAAe,EAAEyF,kBAAuB;IAC7D,IAAI,CAACzF,MAAM,GAAGA,MAAM;IAEpB,IAAI,IAAI,CAACA,MAAM,EAAE;MACf,IAAI,CAACF,QAAQ,GAAG2F,kBAAkB,CAAC3F,QAAQ;MAC3C,IAAI,CAACC,OAAO,GAAG0F,kBAAkB,CAACmB,cAAc;MAChD,IAAI,CAACjI,IAAI,CAACkI,SAAS,CAAC;QAAEnE,IAAI,EAAE,IAAI,CAAC5C,QAAQ;QAAEgH,OAAO,EAAE,IAAI,CAAC/G;MAAO,CAAE,CAAC;KACpE,MAAM;MACL,IAAI,CAACpB,IAAI,CAACkI,SAAS,CAAC;QAClBnE,IAAI,EAAE,aAAa;QACnBoE,OAAO,EAAE;OACV,CAAC;;EAEN;EAGAvC,aAAaA,CAAA;IACX,MAAMwC,UAAU,GAAe,IAAI,CAAC1H,cAAc,CAAC0H,UAAU;IAC7D,IAAIA,UAAU,CAACrD,OAAO,IAAIwC,SAAS,EAAE;MACnC,IAAI,CAACzH,KAAK,CAACiD,GAAG,CAAC,YAAY,EAAEqF,UAAU,CAACrD,OAAO,CAAC;MAChD9C,YAAY,CAACE,OAAO,CAAC,eAAe,EAAE6E,IAAI,CAACC,SAAS,CAACmB,UAAU,CAACrD,OAAO,CAAC,CAAC;;EAE7E;EAEAY,oBAAoBA,CAAA;IAClB,MAAM0C,OAAO,GAAG,IAAI,CAAC3H,cAAc,CAAC2H,OAAO;IAC3C,IAAIA,OAAO,CAACtD,OAAO,IAAIwC,SAAS,EAAE;MAChC,IAAIe,QAAQ,GAAGrG,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAC/C,IAAIS,IAAI,GAAG0F,OAAO,CAACtD,OAAO;MAC1B,IAAIwD,GAAG,GAAkB5F,IAAI,CAACqC,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACF,QAAQ,IAAIA,QAAQ,CAAC,IAAI,IAAIlJ,aAAa,EAAE;MACzG6C,YAAY,CAACE,OAAO,CAAC,SAAS,EAAEoG,GAAG,EAAEE,OAAO,CAAC;MAC7C,IAAI,CAAC3I,KAAK,CAACiD,GAAG,CAAC,mBAAmB,EAAEsF,OAAO,CAACtD,OAAO,CAAC;;EAExD;EAEA2D,sBAAsBA,CACpB7D,GAAQ,EACRK,KAAU,EACV4C,eAAoB,EACpBa,GAAY;IAEZ,IAAIzD,KAAK,EAAE;MACTsC,MAAM,CAACC,OAAO,CAACvC,KAAK,CAAC,CAACX,OAAO,CAAC,CAAC,CAACqE,MAAM,EAAEC,WAAW,CAAC,KAAI;QACtD,IAAI3D,KAAK,CAAC0D,MAAM,CAAC,EAAE;UACjB,IAAIvJ,iBAAiB,CAAC,IAAI,CAACuB,UAAU,CAAC,EAAE;YACtCkI,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGL,MAAM,EACb1D,KAAK,CAAC0D,MAAM,CAAC,CACd;;SAEJ,MAAM,IAAIA,MAAM,IAAId,eAAe,IAAIA,eAAe,CAACjD,GAAG,CAAC,EAAE;UAC5D,IAAI,CAAC6D,sBAAsB,CACzBE,MAAM,EACNd,eAAe,CAACjD,GAAG,CAAC,CAAC+D,MAAM,CAAC,EAC5Bd,eAAe,EACf,IAAI,CACL;;MAEL,CAAC,CAAC;;EAEN;EAEAC,UAAUA,CAACD,eAAoB;IAE7BN,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACvD,OAAO,CAAC,CAAC,CAACM,GAAG,EAAEK,KAAK,CAAC,KAAI;MACvD,IACEA,KAAK,IACL4C,eAAe,CAACjD,GAAG,CAAC,KACnBiD,eAAe,CAACjD,GAAG,CAAC,EAAEmB,MAAM,GAAG,CAAC,IAC/BwB,MAAM,CAACC,OAAO,CAACK,eAAe,CAACjD,GAAG,CAAC,CAAC,EAAEmB,MAAM,IAAI,CAAC,CAAC,EACpD;QACA,IACE8B,eAAe,CAACjD,GAAG,CAAC,IAAI,IAAI,KAC3BiD,eAAe,CAACjD,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEmB,MAAM,IAAI,CAAC,IACnCkD,MAAM,CAACpB,eAAe,CAACjD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAClC;UACA,IAAIxF,iBAAiB,CAAC,IAAI,CAACuB,UAAU,CAAC,EAAE;YACtCkI,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGpE,GAAG,EACViD,eAAe,CAACjD,GAAG,CAAC,CACrB;;SAGJ,MAAM;UACL,IAAI,CAAC6D,sBAAsB,CACzB7D,GAAG,EACHiD,eAAe,CAACjD,GAAG,CAAC,EACpBiD,eAAe,EACf,KAAK,CACN;;OAEJ,MAAM;QACL,IAAIzI,iBAAiB,CAAC,IAAI,CAACuB,UAAU,CAAC,EAAE;UACtCkI,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGpE,GAAG,EACViD,eAAe,CAACjD,GAAG,CAAC,CACrB;;;IAIP,CAAC,CAAC;IACF,IAAI,CAACjD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACpB,EAAE,CAACwH,aAAa,EAAE;EAEzB;EAEAmB,WAAWA,CAAA;IACT,IAAI,CAACC,MAAM,CAACC,aAAa,CAACC,SAAS,GAAG,CAAC;EACzC;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC1I,aAAa,CAAC2I,MAAM,EAAE;IAC3B,IAAI,CAACpJ,MAAM,CAACqJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC9F,WAAW,EAAE;EACpB;EAAC,QAAA+F,CAAA,G;qBA5XU9J,oBAAoB,EAAAN,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAvK,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAE,WAAA,GAAAxK,EAAA,CAAAqK,iBAAA,CAAAI,EAAA,CAAAC,IAAA,GAAA1K,EAAA,CAAAqK,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA5K,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAO,eAAA,GAAA7K,EAAA,CAAAqK,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA/K,EAAA,CAAAqK,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAjL,EAAA,CAAAqK,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAnL,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAc,WAAA,GAAApL,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAe,gBAAA,GAAArL,EAAA,CAAAqK,iBAAA,CAAArK,EAAA,CAAAsL,iBAAA,GAAAtL,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAiB,aAAA,GAAAvL,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAkB,cAAA,GAAAxL,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAmB,eAAA,GAAAzL,EAAA,CAAAqK,iBAAA,CA+BX5K,WAAW,GAAAO,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAoB,aAAA,GAAA1L,EAAA,CAAAqK,iBAAA,CAAAsB,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UA/BpBvL,oBAAoB;IAAAwL,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BACHzM,UAAU;;;;;;;;;;;;QCrCxCQ,EAAA,CAAAmM,cAAA,aAA0B;QACxBnM,EAAA,CAAAE,SAAA,qBAAiC;QAEjCF,EAAA,CAAAoM,UAAA,IAAAC,4CAAA,0BAEe;QACjBrM,EAAA,CAAAsM,YAAA,EAAM;;;QAJ0BtM,EAAA,CAAAuM,SAAA,GAAmB;QAAnBvM,EAAA,CAAAwM,UAAA,oBAAmB;QAClCxM,EAAA,CAAAuM,SAAA,GAAyB;QAAzBvM,EAAA,CAAAwM,UAAA,SAAAN,GAAA,CAAAjK,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}