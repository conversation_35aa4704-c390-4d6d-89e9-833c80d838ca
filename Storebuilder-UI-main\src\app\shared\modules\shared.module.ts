import { NgModule } from '@angular/core';
import {CommonModule} from '@angular/common';
import {CommentPipe} from "../pipes/santiaze.pipe";
import {DeliveryDateFormatPipe} from "../pipes/delivery-date-format.pipe";

// Modules
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {RouterModule} from "@angular/router";
import {ButtonModule} from "primeng/button";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {DialogModule} from "primeng/dialog";

// Components
import {SizeGuideModalComponent} from "@shared/components/size-guide-modal/size-guide-modal.component";
import {DeleteCartModalComponent} from "@shared/modals/delete-cart-modal/delete-cart-modal.component";
import {ConfirmationModalComponent} from "@shared/modals/confirmation-modal/confirmation-modal.component";
import {MessageModalComponent} from "@shared/modals/message-modal/message-modal.component";
import {SuccessModalComponent} from "@shared/modals/success-modal/success-modal.component";
import {PaymentFailedModalComponent} from "@shared/modals/payment-failed-modal/payment-failed-modal.component";
import {MobileModalComponent} from "@shared/modals/mobile-modal/mobile-modal.component";
import {AddressModalComponent} from "@shared/modals/address-modal/address-modal.component";
import {StrictNumberOnlyDirective} from "@core/directives";
import {PaginatorModule} from "primeng/paginator";
import {CheckboxModule} from "primeng/checkbox";
import { MobileCartModalComponent } from '@shared/modals/mobile-cart-modal/mobile-cart-modal.component';
import {LoaderDotsComponent} from "@shared/components/loader-dots/loader-dots.component";
import { ConfirmationDeleteDialogComponent } from "../modals/confirmation-delete-dialog/confirmation-delete-dialog.component";
import { AgeRestrictionComponent } from "@shared/components/age-restriction/age-restriction.component";
import { CustomSwiperComponent } from '@shared/components/custom-swiper/custom-swiper.component';

const APP_COMPONENTS = [
  SizeGuideModalComponent,
  DeleteCartModalComponent,
  ConfirmationModalComponent,
  MessageModalComponent,
  SuccessModalComponent,
  PaymentFailedModalComponent,
  MobileModalComponent,
  AddressModalComponent,
  StrictNumberOnlyDirective,
  CommentPipe,
  DeliveryDateFormatPipe,
  MobileCartModalComponent,
  LoaderDotsComponent,
  AgeRestrictionComponent,
  CustomSwiperComponent
];

const APP_MODULES = [
  ButtonModule,
  BreadcrumbModule,
  DialogModule,
  PaginatorModule,
  CommonModule,
  TranslateModule,
  RouterModule,
  FormsModule,
  ReactiveFormsModule,
];
@NgModule({
  declarations: [
    APP_COMPONENTS
  ],
  imports: [
    APP_MODULES,
    ConfirmationDeleteDialogComponent],
  exports: [
    ButtonModule,
    RouterModule,
    BreadcrumbModule,
    FormsModule,
    ReactiveFormsModule,
    APP_COMPONENTS,
    CheckboxModule
  ],
  providers:[ CommentPipe, DeliveryDateFormatPipe]
})
export class SharedModule { }
