{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { CommonModule } from '@angular/common';\nimport { routes } from \"./routes\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let MerchantsModule = /*#__PURE__*/(() => {\n  class MerchantsModule {\n    static ɵfac = function MerchantsModule_Factory(t) {\n      return new (t || MerchantsModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MerchantsModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), InitialModule, TranslateModule]\n    });\n  }\n  return MerchantsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}