{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"primeng/dialog\";\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport let PaymentFailedModalComponent = /*#__PURE__*/(() => {\n  class PaymentFailedModalComponent {\n    translate;\n    displayModal = false;\n    submit = new EventEmitter();\n    buttonText;\n    addressName = '';\n    isStoreCloud = environment.isStoreCloud;\n    constructor(translate) {\n      this.translate = translate;\n      this.buttonText = this.translate.instant('settings.address.addAddress');\n    }\n    ngOnInit() {\n      /**/\n    }\n    static ɵfac = function PaymentFailedModalComponent_Factory(t) {\n      return new (t || PaymentFailedModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentFailedModalComponent,\n      selectors: [[\"app-mtn-payment-failed-modal\"]],\n      inputs: {\n        displayModal: \"displayModal\"\n      },\n      outputs: {\n        submit: \"submit\"\n      },\n      decls: 12,\n      vars: 7,\n      consts: [[3, \"visible\", \"breakpoints\", \"resizable\", \"closable\", \"modal\", \"showHeader\", \"visibleChange\"], [1, \"d-flex\", \"justify-content-center\", \"mb-4\", \"mt-4\"], [1, \"icon-cross-bg\"], [1, \"fa-solid\", \"fa-xmark\", \"cross-icon\"], [1, \"your-address\", \"text-center\"], [1, \"payment-fail\", \"mb-8\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"px-7\", \"bg-white\", \"border-round\", \"pt-3\"], [\"type\", \"button\", \"ng-reflect-label\", \"Proceed to Payment\", 1, \"p-element\", \"my-2\", \"second-btn\", \"p-button\", \"p-component\", \"width-100\"], [1, \"p-button-label\", \"try-size\"]],\n      template: function PaymentFailedModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dialog\", 0);\n          i0.ɵɵlistener(\"visibleChange\", function PaymentFailedModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            return ctx.displayModal = $event;\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"span\", 2);\n          i0.ɵɵelement(3, \"em\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"h2\", 4);\n          i0.ɵɵtext(5, \"Payment failed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \" Your payment was not successfully processed. Please try again \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"Try again\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(6, _c0))(\"resizable\", false)(\"closable\", false)(\"modal\", true)(\"showHeader\", false);\n        }\n      },\n      dependencies: [i2.Dialog],\n      styles: [\"*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box;list-style:none;text-decoration:none}.your-address[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;color:#000;font-size:28px;font-weight:700}  .p-dialog-content{border-bottom:none!important}.second-btn[_ngcontent-%COMP%]{font-family:var(--medium-font);padding:10px 20px;font-size:15px;font-weight:500;text-transform:uppercase}.payment-fail[_ngcontent-%COMP%]{font-size:15px;font-weight:300;color:#a3a3a3;font-family:var(--regular-font)!important;width:341px;text-align:center}.cross-icon[_ngcontent-%COMP%]{margin-top:10px}.icon-cross-bg[_ngcontent-%COMP%]{text-align:center;font-size:30px;margin-bottom:18px;background:var(--header_bgcolor);width:50px;display:flex;height:50px;color:#fff;border-radius:30px;justify-content:center}@media only screen and (max-width: 786px){.try-size[_ngcontent-%COMP%]{font-size:14px}.payment-fail[_ngcontent-%COMP%]{width:100%}}\"]\n    });\n  }\n  return PaymentFailedModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}