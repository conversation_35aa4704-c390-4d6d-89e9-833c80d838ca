"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[621],{6621:(Vn,W,u)=>{u.r(W),u.d(W,{AccountModule:()=>Gn});var m=u(6814),A=u(6075),e=u(5879),xe=u(553),D=u(9566),g=u(864),J=u(459),I=u(5662),U=u(9147),T=u(5219),b=u(6663),l=u(6223),be=u(8057),x=u(7152);function ve(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",2)(2,"h3",3),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",4),e.O4$(),e.TgZ(6,"svg",5),e._UZ(7,"path",6)(8,"path",7),e.qZA(),e.kcU(),e.TgZ(9,"span"),e._uU(10),e.ALo(11,"translate"),e.qZA(),e.TgZ(12,"div",8),e._UZ(13,"img",9),e.qZA()(),e.TgZ(14,"div",10),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.showAddress())}),e.O4$(),e.TgZ(15,"svg",5),e._UZ(16,"path",11)(17,"path",12),e.qZA(),e.kcU(),e.TgZ(18,"span"),e._uU(19),e.ALo(20,"translate"),e.qZA(),e.TgZ(21,"div",8),e._UZ(22,"img",9),e.qZA()(),e.TgZ(23,"div",13),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.triggerGoogleAnalytics())}),e.O4$(),e.TgZ(24,"svg",5),e._UZ(25,"path",14)(26,"path",15),e.qZA(),e.kcU(),e.TgZ(27,"span"),e._uU(28),e.ALo(29,"translate"),e.qZA(),e.TgZ(30,"div",8),e._UZ(31,"img",9),e.qZA()(),e.TgZ(32,"div",16),e.O4$(),e.TgZ(33,"svg",5),e._UZ(34,"path",17)(35,"path",18)(36,"path",19)(37,"path",20)(38,"path",21)(39,"path",22)(40,"path",23)(41,"path",24)(42,"path",25),e.qZA(),e.kcU(),e.TgZ(43,"span"),e._uU(44),e.ALo(45,"translate"),e.qZA(),e.TgZ(46,"span",26),e._uU(47),e.ALo(48,"translate"),e.qZA(),e.TgZ(49,"div",8),e._UZ(50,"img",9),e.qZA()(),e.TgZ(51,"div",16),e.O4$(),e.TgZ(52,"svg",5),e._UZ(53,"path",27)(54,"path",28)(55,"path",29)(56,"path",30),e.qZA(),e.kcU(),e.TgZ(57,"span"),e._uU(58),e.ALo(59,"translate"),e.qZA(),e.TgZ(60,"div",31),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.modalpopUp())}),e._UZ(61,"img",32),e.TgZ(62,"span",33),e._uU(63),e.qZA(),e._UZ(64,"img",9),e.qZA()(),e.TgZ(65,"div",34),e.O4$(),e.TgZ(66,"svg",35),e._UZ(67,"circle",36)(68,"path",37)(69,"path",38),e.qZA(),e.kcU(),e.TgZ(70,"span"),e._uU(71),e.ALo(72,"translate"),e.qZA()(),e.TgZ(73,"div",39)(74,"p-checkbox",40),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.isChecked=i)})("onChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.updateSubscription(i))}),e.qZA(),e.TgZ(75,"span"),e._uU(76),e.ALo(77,"translate"),e.qZA()(),e.TgZ(78,"div",10),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.logOut())}),e.O4$(),e.TgZ(79,"svg",5),e._UZ(80,"path",41)(81,"path",42),e.qZA(),e.kcU(),e.TgZ(82,"span"),e._uU(83),e.ALo(84,"translate"),e.qZA()()(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(3),e.hij("",e.lcZ(4,14,"account.index.profile")," "),e.xp6(7),e.hij(" ",e.lcZ(11,16,"account.index.yourOrders")," "),e.xp6(9),e.hij(" ",e.lcZ(20,18,"account.index.myAddresses"),""),e.xp6(9),e.hij("",e.lcZ(29,20,"account.index.yourDetails")," "),e.xp6(16),e.Oqu(e.lcZ(45,22,"account.index.lang")),e.xp6(3),e.Oqu(e.lcZ(48,24,"account.index.language")),e.xp6(11),e.Oqu(e.lcZ(59,26,"account.index.country")),e.xp6(3),e.Q6J("src",t.getImage(null==t.selectedCountry?null:t.selectedCountry.flag),e.LSH),e.xp6(2),e.Oqu(null==t.selectedCountry?null:t.selectedCountry.name),e.xp6(8),e.hij(" ",e.lcZ(72,28,"footer.help"),""),e.xp6(3),e.Q6J("ngModel",t.isChecked)("binary",!0),e.xp6(2),e.Oqu(e.lcZ(77,30,"auth.optInCheckBox")),e.xp6(7),e.hij(" ",e.lcZ(84,32,"account.index.logout")," ")}}const ye=function(o){return{"hidden-navbar":o}};function Ae(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"section",43)(1,"div",44),e._UZ(2,"em",45)(3,"em",46),e.TgZ(4,"span",47),e._uU(5),e.ALo(6,"translate"),e.qZA()(),e.TgZ(7,"div",48)(8,"div",49)(9,"div",50),e._uU(10),e.ALo(11,"translate"),e.qZA()(),e.TgZ(12,"div",51)(13,"a",52)(14,"div",53),e._UZ(15,"img",54),e._uU(16),e.ALo(17,"translate"),e.qZA(),e._UZ(18,"em",55),e.qZA(),e.TgZ(19,"a",56),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.showAddress())}),e.TgZ(20,"div",53),e._UZ(21,"img",57),e._uU(22),e.ALo(23,"translate"),e.qZA(),e._UZ(24,"em",55),e.qZA(),e.TgZ(25,"a",58)(26,"div",53),e._UZ(27,"img",59),e._uU(28),e.ALo(29,"translate"),e.qZA(),e._UZ(30,"em",55),e.qZA()(),e.TgZ(31,"div",39)(32,"p-checkbox",40),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.isChecked=i)})("onChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.updateSubscription(i))}),e.qZA(),e.TgZ(33,"span"),e._uU(34),e.ALo(35,"translate"),e.qZA()(),e.TgZ(36,"a",60),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.logOut())}),e._uU(37),e.ALo(38,"translate"),e.qZA()()()}if(2&o){const t=e.oxw();e.Q6J("ngClass",e.VKq(26,ye,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(2),e.Q6J("routerLink","/"),e.xp6(2),e.Q6J("routerLink","/account"),e.xp6(1),e.Oqu(e.lcZ(6,12,"sideMenu.yourAccount")),e.xp6(5),e.hij(" ",e.lcZ(11,14,"account.index.myAccount")," "),e.xp6(6),e.hij(" ",e.lcZ(17,16,"account.index.yourOrders")," "),e.xp6(6),e.hij(" ",e.lcZ(23,18,"account.index.myAddresses")," "),e.xp6(6),e.hij(" ",e.lcZ(29,20,"account.index.profile")," "),e.xp6(4),e.Q6J("ngModel",t.isChecked)("binary",!0),e.xp6(2),e.Oqu(e.lcZ(35,22,"auth.optInCheckBox")),e.xp6(3),e.hij(" ",e.lcZ(38,24,"account.index.logout")," ")}}let Me=(()=>{class o{store;router;cookieService;authTokenService;mainDataService;appDataService;dialogService;platformId;permissionService;$gaService;$gtmService;messageService;translate;navbarData;screenWidth;baseUrl;selectedCountry;isMobileLayout=!1;isGoogleAnalytics=!1;isChecked=!1;constructor(t,n,i,r,a,c,d,p,h,_,f,y,C){this.store=t,this.router=n,this.cookieService=i,this.authTokenService=r,this.mainDataService=a,this.appDataService=c,this.dialogService=d,this.platformId=p,this.permissionService=h,this.$gaService=_,this.$gtmService=f,this.messageService=y,this.translate=C,(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth),this.baseUrl=`${xe.N.apiEndPoint}`}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.$gtmService.pushPageView("account"),this.navbarData=this.appDataService.layoutTemplate.find(r=>"navbar"===r.type);let t=localStorage.getItem("allCountryTenants")??"",n=localStorage.getItem("tenantId"),i=JSON.parse(t);this.selectedCountry=i.find(r=>r.tenantId==n),this.getSubscriptionStatus()}logOut(){sessionStorage.clear(),this.authTokenService.authTokenSet(""),this.cookieService.delete("authToken","/"),this.store.set("profile",""),this.mainDataService.setCartLenghtData(null),this.mainDataService.setUserData(null),localStorage.setItem("secondaryDefault","false"),localStorage.setItem("sessionId",""),localStorage.setItem("addedProducts",""),localStorage.setItem("cartId",""),this.store.set("cartProducts",[]),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth_enc"),localStorage.setItem("isGuest","true"),this.router.navigate(["/"])}getImage(t){return t?`${this.baseUrl}/Images/${t}`:""}modalpopUp(){this.dialogService.showDialog()}showAddress(){this.isGoogleAnalytics&&this.$gaService.event(D.s.CLICK_ON_MY_ADDRESSES,"","VIEW_ADDRESS ",1,!0),this.router.navigate(["/account/address"])}triggerGoogleAnalytics(){this.isGoogleAnalytics&&this.$gaService.event(D.s.CLICK_ON_ACCOUNT_DETAILS,"","ACCOUNT_DETAILS",1,!0)}getSubscriptionStatus(){this.mainDataService.getCustomerSubscriptionValue().subscribe(t=>{this.isChecked=t??!1},t=>{console.error("Error fetching subscription status:",t)})}updateSubscription(t){this.isChecked=t.checked,this.mainDataService.updateCustomerSubscriptionValue({isSubscribed:this.isChecked}).subscribe(()=>{this.messageService.add(this.isChecked?{severity:"success",summary:this.translate.instant("account.optInMessage")}:{severity:"error",summary:this.translate.instant("account.optOutMessage")})},n=>{console.error("Error updating subscription:",n),this.isChecked=!this.isChecked})}static \u0275fac=function(n){return new(n||o)(e.Y36(g.d6),e.Y36(A.F0),e.Y36(J.N),e.Y36(g.Lz),e.Y36(g.iI),e.Y36(g.UW),e.Y36(g.UW),e.Y36(e.Lbi),e.Y36(g.$A),e.Y36(I.$r),e.Y36(U.J),e.Y36(T.ez),e.Y36(b.sK))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-index"]],decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["oldContainer",""],[1,"profile-container"],[1,"d-flex","profile-heading"],["routerLink","/orders",1,"profile-item"],["xmlns","http://www.w3.org/2000/svg","width","20","height","20","viewBox","0 0 20 20","fill","none"],["d","M16.0337 4.65046H15.7003L12.8837 1.83379C12.6587 1.60879 12.292 1.60879 12.0587 1.83379C11.8337 2.05879 11.8337 2.42546 12.0587 2.65879L14.0503 4.65046H5.95033L7.94199 2.65879C8.16699 2.43379 8.16699 2.06712 7.94199 1.83379C7.71699 1.60879 7.35033 1.60879 7.11699 1.83379L4.30866 4.65046H3.97533C3.22533 4.65046 1.66699 4.65046 1.66699 6.78379C1.66699 7.59212 1.83366 8.12546 2.18366 8.47546C2.38366 8.68379 2.62533 8.79212 2.88366 8.85046C3.12533 8.90879 3.38366 8.91712 3.63366 8.91712H16.367C16.6253 8.91712 16.867 8.90046 17.1003 8.85046C17.8003 8.68379 18.3337 8.18379 18.3337 6.78379C18.3337 4.65046 16.7753 4.65046 16.0337 4.65046Z","fill","#204E6E"],["d","M15.9087 10H4.09207C3.57541 10 3.18374 10.4583 3.26707 10.9667L3.96707 15.25C4.20041 16.6833 4.82541 18.3333 7.60041 18.3333H12.2754C15.0837 18.3333 15.5837 16.925 15.8837 15.35L16.7254 10.9917C16.8254 10.475 16.4337 10 15.9087 10ZM12.4004 13.375L9.69207 15.875C9.57541 15.9833 9.42541 16.0417 9.26707 16.0417C9.10874 16.0417 8.95041 15.9833 8.82541 15.8583L7.57541 14.6083C7.33374 14.3667 7.33374 13.9667 7.57541 13.725C7.82541 13.4833 8.21707 13.4833 8.46707 13.725L9.29207 14.55L11.5587 12.4583C11.8087 12.225 12.2087 12.2417 12.4421 12.4917C12.6754 12.75 12.6587 13.1417 12.4004 13.375Z","fill","#204E6E"],[1,"arrow"],["alt","","src","assets/icons/arrow.svg",2,"width","20px","height","20px"],[1,"profile-item",3,"click"],["d","M9.99989 1.66712C10.891 1.66165 11.7742 1.83593 12.5964 2.17952C13.4187 2.52311 14.1633 3.02897 14.7856 3.66685C15.4079 4.30474 15.8952 5.06155 16.2184 5.89206C16.5416 6.72256 16.694 7.6097 16.6666 8.50045C16.6666 11.2671 14.4443 14.5449 9.99989 18.3338C5.55823 14.5421 3.336 11.2643 3.33323 8.50045C3.30576 7.6097 3.45818 6.72256 3.78137 5.89206C4.10456 5.06155 4.59188 4.30474 5.21421 3.66685C5.83653 3.02897 6.58109 2.52311 7.40336 2.17952C8.22563 1.83593 9.10874 1.66165 9.99989 1.66712Z","fill","#204E6E"],["d","M9.99968 10.0003C9.67004 10.0003 9.34781 9.90258 9.07372 9.71944C8.79964 9.53631 8.58602 9.27601 8.45988 8.97147C8.33373 8.66692 8.30072 8.33181 8.36503 8.00851C8.42934 7.68521 8.58808 7.38824 8.82116 7.15515C9.05425 6.92206 9.35122 6.76333 9.67452 6.69902C9.99783 6.63471 10.3329 6.66771 10.6375 6.79386C10.942 6.92001 11.2023 7.13363 11.3855 7.40771C11.5686 7.68179 11.6663 8.00402 11.6663 8.33366C11.665 8.77528 11.489 9.19844 11.1767 9.51071C10.8645 9.82299 10.4413 9.99901 9.99968 10.0003Z","fill","#F5F5F5"],["routerLink","/account/details",1,"profile-item",3,"click"],["d","M18.3337 10.0003C18.3337 5.40866 14.592 1.66699 10.0003 1.66699C5.40866 1.66699 1.66699 5.40866 1.66699 10.0003C1.66699 12.417 2.70866 14.592 4.35866 16.117C4.35866 16.1253 4.35866 16.1253 4.35033 16.1337C4.43366 16.217 4.53366 16.2837 4.61699 16.3587C4.66699 16.4003 4.70866 16.442 4.75866 16.4753C4.90866 16.6003 5.07533 16.717 5.23366 16.8337C5.29199 16.8753 5.34199 16.9087 5.40033 16.9503C5.55866 17.0587 5.72533 17.1587 5.90033 17.2503C5.95866 17.2837 6.02533 17.3253 6.08366 17.3587C6.25033 17.4503 6.42533 17.5337 6.60866 17.6087C6.67533 17.642 6.74199 17.6753 6.80866 17.7003C6.99199 17.7753 7.17533 17.842 7.35866 17.9003C7.42533 17.9253 7.49199 17.9503 7.55866 17.967C7.75866 18.0253 7.95866 18.0753 8.15866 18.1253C8.21699 18.142 8.27533 18.1587 8.34199 18.167C8.57533 18.217 8.80866 18.2503 9.05033 18.2753C9.08366 18.2753 9.11699 18.2837 9.15033 18.292C9.43366 18.317 9.71699 18.3337 10.0003 18.3337C10.2837 18.3337 10.567 18.317 10.842 18.292C10.8753 18.292 10.9087 18.2837 10.942 18.2753C11.1837 18.2503 11.417 18.217 11.6503 18.167C11.7087 18.1587 11.767 18.1337 11.8337 18.1253C12.0337 18.0753 12.242 18.0337 12.4337 17.967C12.5003 17.942 12.567 17.917 12.6337 17.9003C12.817 17.8337 13.0087 17.7753 13.1837 17.7003C13.2503 17.6753 13.317 17.642 13.3837 17.6087C13.5587 17.5337 13.7337 17.4503 13.9087 17.3587C13.9753 17.3253 14.0337 17.2837 14.092 17.2503C14.2587 17.1503 14.4253 17.0587 14.592 16.9503C14.6503 16.917 14.7003 16.8753 14.7587 16.8337C14.9253 16.717 15.0837 16.6003 15.2337 16.4753C15.2837 16.4337 15.3253 16.392 15.3753 16.3587C15.467 16.2837 15.5587 16.2087 15.642 16.1337C15.642 16.1253 15.642 16.1253 15.6337 16.117C17.292 14.592 18.3337 12.417 18.3337 10.0003ZM14.117 14.142C11.8587 12.6253 8.15866 12.6253 5.88366 14.142C5.51699 14.3837 5.21699 14.667 4.96699 14.9753C3.70033 13.692 2.91699 11.9337 2.91699 10.0003C2.91699 6.09199 6.09199 2.91699 10.0003 2.91699C13.9087 2.91699 17.0837 6.09199 17.0837 10.0003C17.0837 11.9337 16.3003 13.692 15.0337 14.9753C14.792 14.667 14.4837 14.3837 14.117 14.142Z","fill","#204E6E"],["d","M10 5.77539C8.275 5.77539 6.875 7.17539 6.875 8.90039C6.875 10.5921 8.2 11.9671 9.95833 12.0171C9.98333 12.0171 10.0167 12.0171 10.0333 12.0171C10.05 12.0171 10.075 12.0171 10.0917 12.0171C10.1 12.0171 10.1083 12.0171 10.1083 12.0171C11.7917 11.9587 13.1167 10.5921 13.125 8.90039C13.125 7.17539 11.725 5.77539 10 5.77539Z","fill","#204E6E"],[1,"profile-item"],["d","M6.3748 17.4245C6.3498 17.4245 6.31647 17.4412 6.29147 17.4412C4.6748 16.6412 3.35814 15.3162 2.5498 13.6995C2.5498 13.6745 2.56647 13.6412 2.56647 13.6162C3.58314 13.9162 4.63314 14.1412 5.6748 14.3162C5.85814 15.3662 6.0748 16.4079 6.3748 17.4245Z","fill","#204E6E"],["d","M17.4502 13.7079C16.6252 15.3662 15.2502 16.7079 13.5752 17.5162C13.8919 16.4579 14.1585 15.3912 14.3335 14.3162C15.3835 14.1412 16.4169 13.9162 17.4335 13.6162C17.4252 13.6495 17.4502 13.6829 17.4502 13.7079Z","fill","#204E6E"],["d","M17.5169 6.42507C16.4669 6.1084 15.4085 5.85007 14.3335 5.66673C14.1585 4.59173 13.9002 3.52507 13.5752 2.4834C15.3002 3.3084 16.6919 4.70007 17.5169 6.42507Z","fill","#204E6E"],["d","M6.37507 2.5748C6.07507 3.59147 5.8584 4.6248 5.6834 5.6748C4.6084 5.84147 3.54173 6.10814 2.4834 6.4248C3.29173 4.7498 4.6334 3.3748 6.29173 2.5498C6.31673 2.5498 6.35007 2.5748 6.37507 2.5748Z","fill","#204E6E"],["d","M12.9085 5.49199C10.9751 5.27533 9.02513 5.27533 7.0918 5.49199C7.30013 4.35033 7.5668 3.20866 7.9418 2.10866C7.95846 2.04199 7.95013 1.99199 7.95846 1.92533C8.6168 1.76699 9.2918 1.66699 10.0001 1.66699C10.7001 1.66699 11.3835 1.76699 12.0335 1.92533C12.0418 1.99199 12.0418 2.04199 12.0585 2.10866C12.4335 3.21699 12.7001 4.35033 12.9085 5.49199Z","fill","#204E6E"],["d","M5.49199 12.9085C4.34199 12.7001 3.20866 12.4335 2.10866 12.0585C2.04199 12.0418 1.99199 12.0501 1.92533 12.0418C1.76699 11.3835 1.66699 10.7085 1.66699 10.0001C1.66699 9.30013 1.76699 8.6168 1.92533 7.9668C1.99199 7.95846 2.04199 7.95846 2.10866 7.9418C3.21699 7.57513 4.34199 7.30013 5.49199 7.0918C5.28366 9.02513 5.28366 10.9751 5.49199 12.9085Z","fill","#204E6E"],["d","M18.3338 10.0001C18.3338 10.7085 18.2338 11.3835 18.0755 12.0418C18.0088 12.0501 17.9588 12.0418 17.8921 12.0585C16.7838 12.4251 15.6505 12.7001 14.5088 12.9085C14.7255 10.9751 14.7255 9.02513 14.5088 7.0918C15.6505 7.30013 16.7921 7.5668 17.8921 7.9418C17.9588 7.95846 18.0088 7.9668 18.0755 7.9668C18.2338 8.62513 18.3338 9.30013 18.3338 10.0001Z","fill","#204E6E"],["d","M12.9085 14.5088C12.7001 15.6588 12.4335 16.7921 12.0585 17.8921C12.0418 17.9588 12.0418 18.0088 12.0335 18.0755C11.3835 18.2338 10.7001 18.3338 10.0001 18.3338C9.2918 18.3338 8.6168 18.2338 7.95846 18.0755C7.95013 18.0088 7.95846 17.9588 7.9418 17.8921C7.57513 16.7838 7.30013 15.6588 7.0918 14.5088C8.05846 14.6171 9.02513 14.6921 10.0001 14.6921C10.9751 14.6921 11.9501 14.6171 12.9085 14.5088Z","fill","#204E6E"],["d","M13.1364 13.1364C11.0522 13.3994 8.94846 13.3994 6.86422 13.1364C6.60125 11.0522 6.60125 8.94846 6.86422 6.86422C8.94846 6.60125 11.0522 6.60125 13.1364 6.86422C13.3994 8.94846 13.3994 11.0522 13.1364 13.1364Z","fill","#204E6E"],[1,"right-text"],["d","M7.62533 6.24121C7.15866 6.24121 6.79199 6.61621 6.79199 7.07454C6.79199 7.53288 7.16699 7.90788 7.62533 7.90788C8.08366 7.90788 8.45866 7.53288 8.45866 7.07454C8.45866 6.61621 8.08366 6.24121 7.62533 6.24121Z","fill","#204E6E"],["d","M17.8837 4.20033C17.1837 2.57533 15.642 1.66699 13.492 1.66699H6.50866C3.83366 1.66699 1.66699 3.83366 1.66699 6.50866V13.492C1.66699 15.642 2.57533 17.1837 4.20033 17.8837C4.35866 17.9503 4.54199 17.9087 4.65866 17.792L17.792 4.65866C17.917 4.53366 17.9587 4.35033 17.8837 4.20033ZM8.77533 10.2003C8.45033 10.517 8.02533 10.667 7.60033 10.667C7.17533 10.667 6.75033 10.5087 6.42533 10.2003C5.57533 9.40033 4.64199 8.12533 5.00033 6.60866C5.31699 5.23366 6.53366 4.61699 7.60033 4.61699C8.66699 4.61699 9.88366 5.23366 10.2003 6.61699C10.5503 8.12533 9.61699 9.40033 8.77533 10.2003Z","fill","#204E6E"],["d","M16.2251 17.1079C16.4084 17.2913 16.3834 17.5913 16.1584 17.7163C15.4251 18.1246 14.5334 18.3329 13.4917 18.3329H6.50841C6.26674 18.3329 6.16674 18.0496 6.33341 17.8829L11.3667 12.8496C11.5334 12.6829 11.7917 12.6829 11.9584 12.8496L16.2251 17.1079Z","fill","#204E6E"],["d","M18.3335 6.50841V13.4917C18.3335 14.5334 18.1251 15.4334 17.7168 16.1584C17.5918 16.3834 17.2918 16.4001 17.1085 16.2251L12.8418 11.9584C12.6751 11.7917 12.6751 11.5334 12.8418 11.3667L17.8751 6.33341C18.0501 6.16674 18.3335 6.26674 18.3335 6.50841Z","fill","#204E6E"],[1,"arrow",3,"click"],[1,"flar-header-img",3,"src"],[1,"flag-name"],["routerLink","/account/help",1,"profile-item"],["width","20","height","20","viewBox","0 0 20 20","fill","none","xmlns","http://www.w3.org/2000/svg"],["cx","10","cy","10","r","10","fill","#204E6E"],["d","M9.68985 13.4688C9.06899 13.4688 8.56641 13.9861 8.56641 14.6069C8.56641 15.213 9.05421 15.7452 9.68985 15.7452C10.3255 15.7452 10.828 15.213 10.828 14.6069C10.828 13.9861 10.3107 13.4688 9.68985 13.4688Z","fill","white"],["d","M9.88083 4.84766C7.88522 4.84766 6.96875 6.03027 6.96875 6.82848C6.96875 7.40498 7.45655 7.67108 7.85566 7.67108C8.65391 7.67108 8.32872 6.53282 9.83648 6.53282C10.5756 6.53282 11.1669 6.85805 11.1669 7.53803C11.1669 8.33624 10.3391 8.79448 9.85126 9.20836C9.42255 9.57786 8.86087 10.184 8.86087 11.4552C8.86087 12.2239 9.06784 12.4456 9.67387 12.4456C10.3982 12.4456 10.546 12.1204 10.546 11.8395C10.546 11.0709 10.5608 10.6274 11.3738 9.99179C11.7729 9.68138 13.0294 8.67617 13.0294 7.28667C13.0294 5.89718 11.7729 4.84766 9.88083 4.84766Z","fill","white"],["id","opt-in",1,"opt-in"],[3,"ngModel","binary","ngModelChange","onChange"],["d","M14 1.66699H11.8333C9.16667 1.66699 7.5 3.33366 7.5 6.00033V9.37533H12.7083C13.05 9.37533 13.3333 9.65866 13.3333 10.0003C13.3333 10.342 13.05 10.6253 12.7083 10.6253H7.5V14.0003C7.5 16.667 9.16667 18.3337 11.8333 18.3337H13.9917C16.6583 18.3337 18.325 16.667 18.325 14.0003V6.00033C18.3333 3.33366 16.6667 1.66699 14 1.66699Z","fill","#204E6E"],["d","M3.8002 9.37454L5.5252 7.64954C5.6502 7.52454 5.70853 7.36621 5.70853 7.20788C5.70853 7.04954 5.6502 6.88288 5.5252 6.76621C5.28353 6.52454 4.88353 6.52454 4.64186 6.76621L1.8502 9.55788C1.60853 9.79954 1.60853 10.1995 1.8502 10.4412L4.64186 13.2329C4.88353 13.4745 5.28353 13.4745 5.5252 13.2329C5.76686 12.9912 5.76686 12.5912 5.5252 12.3495L3.8002 10.6245H7.5002V9.37454H3.8002V9.37454Z","fill","#204E6E"],[1,"account-page",3,"ngClass"],[1,"breadcrumb-address","d-flex"],["aria-hidden","true",1,"pi","pi-home","cursor-pointer",3,"routerLink"],["aria-hidden","true",1,"pi","pi-angle-left"],[3,"routerLink"],[1,"content-container","mobile-top",2,"margin-top","40px"],[1,"col-12","col-md-6","flex","md:justify-content-start","mobile-left"],[1,"font-size-22","bold-font","account-heading"],[1,"mt-4","flex","flex-column","justify-content-start","flex-wrap","account-links"],["routerLink","/orders",1,"flex","justify-content-between","py-3","px-3","surface-100","mb-2","no-underline","text-black-alpha-90","border-round"],[1,"align-items-center","d-flex"],["alt","No Image","src","assets/icons/book.svg",2,"margin-right","11px"],[1,"pi","pi-angle-right","text-blue-800"],[1,"flex","justify-content-between","py-3","px-3","surface-100","mb-2","no-underline","text-black-alpha-90","border-round",3,"click"],["alt","No Image","src","assets/icons/pin.svg",1,"img"],["routerLink","/account/details",1,"flex","justify-content-between","py-3","px-3","surface-100","mb-2","no-underline","text-black-alpha-90","border-round"],["alt","No Image","src","assets/icons/person.svg",1,"img"],[1,"mt-4","no-underline","text-blue-800","cursor-pointer","logout-color",3,"click"]],template:function(n,i){if(1&n&&(e.YNc(0,ve,85,34,"ng-container",0),e.YNc(1,Ae,39,28,"ng-template",null,1,e.W1O)),2&n){const r=e.MAs(2);e.Q6J("ngIf",i.isMobileLayout&&i.screenWidth<=768)("ngIfElse",r)}},dependencies:[l.JJ,m.mk,m.O5,A.rH,l.On,be.XZ,x.mh,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.account-links[_ngcontent-%COMP%]{font-family:var(--medium-font)!important}.account-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:330px}.img[_ngcontent-%COMP%]{margin-left:-5px;margin-right:10px}.account-page[_ngcontent-%COMP%]{margin-bottom:250px!important}.breadcrumb-address[_ngcontent-%COMP%]{background-color:#efeded;padding:1rem}.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{padding:0 6px;margin:auto 0}.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px}.opt-in[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:flex-start;width:47%;margin:20px 0}.opt-in[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--gray-700);font:normal 400 12px/20px var(--regular-font);letter-spacing:.4px;text-transform:capitalize}@media screen and (max-width: 768px){.account-page[_ngcontent-%COMP%]{margin-top:200px!important}.mobile-top[_ngcontent-%COMP%]{margin-top:10px!important}.mobile-left[_ngcontent-%COMP%]{padding-left:0!important}.account-links[_ngcontent-%COMP%]{margin-top:10px!important}.account-heading[_ngcontent-%COMP%]{font-size:20px!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:150px!important}.opt-in[_ngcontent-%COMP%]{width:100%}}.logout-color[_ngcontent-%COMP%]{color:var(--main_bt_txtcolor)!important;font-family:var(--medium-font)!important}.pi[_ngcontent-%COMP%]{color:var(--main_bt_txtcolor)!important}.profile-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:#fff;padding:16px;margin-top:72px}@media only screen and (max-width: 767px){.profile-container[_ngcontent-%COMP%]{height:800px;overflow:auto}}@media only screen and (min-width: 768px) and (max-width: 1200px){.profile-container[_ngcontent-%COMP%]{height:800px;overflow:auto}}.profile-container[_ngcontent-%COMP%]   .profile-heading[_ngcontent-%COMP%]{display:flex;align-self:stretch;color:#292d32;font-family:main-medium;font-size:16px;font-style:normal;font-weight:500;line-height:normal;margin-bottom:0;padding:16px 16px 16px 0}.profile-item[_ngcontent-%COMP%]{border:1px solid #E4E7E9;cursor:pointer;display:flex;padding:24px 16px;border-radius:8px;margin-bottom:16px;font-family:main-medium;align-items:center;position:relative}.profile-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:16px}.profile-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#323232;font-family:main-medium;font-size:14px;font-style:normal;font-weight:400;line-height:100%;align-self:center}.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]{right:10px;position:absolute}.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flar-header-img[_ngcontent-%COMP%]{width:24px;border-radius:4px;height:18px;margin-right:5px}.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flag-name[_ngcontent-%COMP%]{color:#a3a3a3;font-family:main-regular;font-size:12px;font-style:normal;font-weight:400;line-height:100%;text-transform:capitalize}.profile-item[_ngcontent-%COMP%]   .right-text[_ngcontent-%COMP%]{margin-left:auto;color:#aaa;position:absolute;right:37px;text-transform:capitalize;font-size:12px}.profile-item[_ngcontent-%COMP%]   .country-flag[_ngcontent-%COMP%]{margin-left:auto;width:24px;height:16px}"]})}return o})();var G=u(5861),q=u(2284),B=u(3714),S=u(707),ee=u(5006);function Ze(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}"function"==typeof SuppressedError&&SuppressedError;var Se=Ze(function o(s,t){if(s===t)return!0;if(s&&t&&"object"==typeof s&&"object"==typeof t){if(s.constructor!==t.constructor)return!1;var n,i,r;if(Array.isArray(s)){if((n=s.length)!=t.length)return!1;for(i=n;0!=i--;)if(!o(s[i],t[i]))return!1;return!0}if(s.constructor===RegExp)return s.source===t.source&&s.flags===t.flags;if(s.valueOf!==Object.prototype.valueOf)return s.valueOf()===t.valueOf();if(s.toString!==Object.prototype.toString)return s.toString()===t.toString();if((n=(r=Object.keys(s)).length)!==Object.keys(t).length)return!1;for(i=n;0!=i--;)if(!Object.prototype.hasOwnProperty.call(t,r[i]))return!1;for(i=n;0!=i--;){var a=r[i];if(!o(s[a],t[a]))return!1}return!0}return s!=s&&t!=t});const te="__googleMapsScriptId";var E=function(o){return o[o.INITIALIZED=0]="INITIALIZED",o[o.LOADING=1]="LOADING",o[o.SUCCESS=2]="SUCCESS",o[o.FAILURE=3]="FAILURE",o}(E||{});class P{constructor({apiKey:s,authReferrerPolicy:t,channel:n,client:i,id:r=te,language:a,libraries:c=[],mapIds:d,nonce:p,region:h,retries:_=3,url:f="https://maps.googleapis.com/maps/api/js",version:y}){if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=s,this.authReferrerPolicy=t,this.channel=n,this.client=i,this.id=r||te,this.language=a,this.libraries=c,this.mapIds=d,this.nonce=p,this.region=h,this.retries=_,this.url=f,this.version=y,P.instance){if(!Se(this.options,P.instance.options))throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(P.instance.options)}`);return P.instance}P.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?E.FAILURE:this.done?E.SUCCESS:this.loading?E.LOADING:E.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){let s=this.url;return s+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(s+=`&key=${this.apiKey}`),this.channel&&(s+=`&channel=${this.channel}`),this.client&&(s+=`&client=${this.client}`),this.libraries.length>0&&(s+=`&libraries=${this.libraries.join(",")}`),this.language&&(s+=`&language=${this.language}`),this.region&&(s+=`&region=${this.region}`),this.version&&(s+=`&v=${this.version}`),this.mapIds&&(s+=`&map_ids=${this.mapIds.join(",")}`),this.authReferrerPolicy&&(s+=`&auth_referrer_policy=${this.authReferrerPolicy}`),s}deleteScript(){const s=document.getElementById(this.id);s&&s.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((s,t)=>{this.loadCallback(n=>{n?t(n.error):s(window.google)})})}importLibrary(s){return this.execute(),google.maps.importLibrary(s)}loadCallback(s){this.callbacks.push(s),this.execute()}setScript(){var s,t;if(document.getElementById(this.id))return void this.callback();const n={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(n).forEach(r=>!n[r]&&delete n[r]),null!==(t=null===(s=window?.google)||void 0===s?void 0:s.maps)&&void 0!==t&&t.importLibrary||(r=>{let a,c,d,p="The Google Maps JavaScript API",h="google",_="importLibrary",f="__ib__",y=document,C=window;C=C[h]||(C[h]={});const v=C.maps||(C.maps={}),N=new Set,w=new URLSearchParams,Bn=()=>a||(a=new Promise((Y,X)=>function we(o,s,t,n){return new(t||(t=Promise))(function(r,a){function c(h){try{p(n.next(h))}catch(_){a(_)}}function d(h){try{p(n.throw(h))}catch(_){a(_)}}function p(h){h.done?r(h.value):function i(r){return r instanceof t?r:new t(function(a){a(r)})}(h.value).then(c,d)}p((n=n.apply(o,s||[])).next())})}(this,void 0,void 0,function*(){var $;for(d in yield c=y.createElement("script"),c.id=this.id,w.set("libraries",[...N]+""),r)w.set(d.replace(/[A-Z]/g,Rn=>"_"+Rn[0].toLowerCase()),r[d]);w.set("callback",h+".maps."+f),c.src=this.url+"?"+w,v[f]=Y,c.onerror=()=>a=X(Error(p+" could not load.")),c.nonce=this.nonce||(null===($=y.querySelector("script[nonce]"))||void 0===$?void 0:$.nonce)||"",y.head.append(c)})));v[_]?console.warn(p+" only loads once. Ignoring:",r):v[_]=(Y,...X)=>N.add(Y)&&Bn().then(()=>v[_](Y,...X))})(n);const i=this.libraries.map(r=>this.importLibrary(r));i.length||i.push(this.importLibrary("core")),Promise.all(i).then(()=>this.callback(),r=>{const a=new ErrorEvent("error",{error:r});this.loadErrorCallback(a)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(s){if(this.errors.push(s),this.errors.length<=this.retries){const t=this.errors.length*Math.pow(2,this.errors.length);console.error(`Failed to load Google Maps script, retrying in ${t} ms.`),setTimeout(()=>{this.deleteScript(),this.setScript()},t)}else this.onerrorEvent=s,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(s=>{s(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version)return console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),void this.callback();this.loading=!0,this.setScript()}}}let Oe=(()=>{class o{constructor(){this.defaultOptions={}}setOptions(t){this.defaultOptions={...this.defaultOptions,...t}}getOptions(){return this.defaultOptions}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=e.Yz7({token:o,factory:o.\u0275fac,providedIn:"root"}),o})();class ne{}let oe=(()=>{class o{constructor(t,n,i,r){this.el=t,this.ngxGpAutocompleteService=n,this.config=i,this.ngZone=r,this.onAddressChange=new e.vpe}ngAfterViewInit(){this.options||(this.options=this.ngxGpAutocompleteService.getOptions()),new P(this.config.loaderOptions).load().then(()=>{this.initialize()})}isGoogleLibExists(){return!(!google||!google.maps||!google.maps.places)}initialize(){if(!this.isGoogleLibExists())throw new Error("Google maps library can not be found");if(this.autocomplete=new google.maps.places.Autocomplete(this.el.nativeElement,this.options),!this.autocomplete)throw new Error("Autocomplete is not initialized");null!=!this.autocomplete.addListener&&(this.eventListener=this.autocomplete.addListener("place_changed",()=>{this.handleChangeEvent()})),this.el.nativeElement.addEventListener("keydown",t=>{t.key&&"enter"==t.key.toLowerCase()&&t.target===this.el.nativeElement&&(t.preventDefault(),t.stopPropagation())}),window&&window.navigator&&window.navigator.userAgent&&navigator.userAgent.match(/(iPad|iPhone|iPod)/g)&&setTimeout(()=>{let t=document.getElementsByClassName("pac-container");if(t){let n=Array.from(t);if(n)for(let i of n)i&&i.addEventListener("touchend",r=>{r.stopImmediatePropagation()})}},500)}reset(){this.autocomplete.setComponentRestrictions(this.options.componentRestrictions),this.autocomplete.setTypes(this.options.types)}handleChangeEvent(){this.ngZone.run(()=>{this.place=this.autocomplete.getPlace(),this.place&&this.onAddressChange.emit(this.place)})}}return o.\u0275fac=function(t){return new(t||o)(e.Y36(e.SBq),e.Y36(Oe),e.Y36(ne),e.Y36(e.R0b))},o.\u0275dir=e.lG2({type:o,selectors:[["","ngx-gp-autocomplete",""]],inputs:{options:"options"},outputs:{onAddressChange:"onAddressChange"},exportAs:["ngx-places"]}),o})(),ke=(()=>{class o{static forRoot(t){return{ngModule:o,providers:[{provide:ne,useValue:t}]}}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.oAB({type:o}),o.\u0275inj=e.cJS({}),o})();var F=u(9025),ie=u(3965),re=u(1312);const Pe=function(){return{"960px":"75vw","640px":"90vw"}};let Le=(()=>{class o{displayModal=!1;submit=new e.vpe;cancel=new e.vpe;addAddress;closeModal(){this.submit.emit(this.addAddress)}onCancel(t){this.cancel.emit(!1)}static \u0275fac=function(n){return new(n||o)};static \u0275cmp=e.Xpm({type:o,selectors:[["app-address-label"]],inputs:{displayModal:"displayModal"},outputs:{submit:"submit",cancel:"cancel"},decls:18,vars:21,consts:[[1,"address-modal",3,"visible","breakpoints","dismissableMask","draggable","modal","resizable","showHeader","onHide","visibleChange"],[1,"address-label"],[1,"form-bg"],[1,"d-flex"],["for","label"],["id","label","pInputText","","type","text",1,"form-input",3,"ngModel","ngModelChange"],[1,"grid"],[1,"col-12","col-md-6","mt-3","address-label-btn"],["pButton","","type","button",1,"w-full","second-btn","add-address-btn",3,"label","click"],[1,"col-12","col-md-6","mt-1","button-address","address-label-btn"],["pButton","","type","button",1,"defaultBtn","w-100","add-address-btn",3,"label","click"]],template:function(n,i){1&n&&(e.TgZ(0,"p-dialog",0),e.NdJ("onHide",function(){return i.closeModal()})("visibleChange",function(a){return i.displayModal=a}),e.TgZ(1,"h2",1),e._uU(2),e.ALo(3,"translate"),e.qZA(),e.TgZ(4,"div",2)(5,"div",3)(6,"label",4),e._uU(7),e.ALo(8,"translate"),e.qZA()(),e.TgZ(9,"div",3)(10,"input",5),e.NdJ("ngModelChange",function(a){return i.addAddress=a}),e.qZA()()(),e.TgZ(11,"div",6)(12,"div",7)(13,"button",8),e.NdJ("click",function(){return i.closeModal()}),e.ALo(14,"translate"),e.qZA()(),e.TgZ(15,"div",9)(16,"button",10),e.NdJ("click",function(a){return i.onCancel(a)}),e.ALo(17,"translate"),e.qZA()()()()),2&n&&(e.Q6J("visible",i.displayModal)("breakpoints",e.DdM(20,Pe))("dismissableMask",!0)("draggable",!1)("modal",!0)("resizable",!1)("showHeader",!1),e.xp6(2),e.hij(" ",e.lcZ(3,12,"addressOtherModal.addressLabel")," "),e.xp6(5),e.Oqu(e.lcZ(8,14,"addressOtherModal.labelName")),e.xp6(3),e.Q6J("ngModel",i.addAddress),e.xp6(3),e.Q6J("label",e.lcZ(14,16,"addressOtherModal.add")),e.xp6(3),e.Q6J("label",e.lcZ(17,18,"addressOtherModal.cancel")))},dependencies:[B.o,l.Fj,l.JJ,S.Hq,l.On,x.mh,re.V,b.X$],styles:[".address-label[_ngcontent-%COMP%]{font-weight:500;font-size:18px;color:#292d32;font-family:var(--medium-font);text-align:center}.address-modal[_ngcontent-%COMP%]     .p-dialog-content{border-radius:8px;background:#F6F6F6;padding:24px}.form-bg[_ngcontent-%COMP%]{margin-top:20px;background:white;width:100%;padding:16px;border-radius:8px}.p-inputtext[_ngcontent-%COMP%]:enabled:hover{border:none!important;width:100%;box-shadow:none!important}label[_ngcontent-%COMP%]{font-family:var(--regular-font);font-size:10px;font-weight:300;color:#323232}input[_ngcontent-%COMP%]{width:100%;padding:0!important;font-family:var(--regular-font);font-size:14px;font-weight:500}.defaultBtn[_ngcontent-%COMP%]{text-align:center;letter-spacing:0px;color:var(--header_bgcolor);background:transparent;border:1px solid var(--header_bgcolor);padding:10px 20px;font-size:15px;border-radius:25px;width:70%;outline:0 none;font-family:var(--medium-font)}.add-address-btn[_ngcontent-%COMP%]{font-weight:500;font-size:14px!important;border-radius:8px!important;text-transform:capitalize!important}.address-label-btn[_ngcontent-%COMP%]{width:100%}.p-button[_ngcontent-%COMP%]:enabled:hover{background:transparent;color:var(--header_bgcolor);border:1px solid var(--header_bgcolor)}"]})}return o})();const Ie=["placesRef"],Ne=["search"];function De(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"map-marker",52),e.NdJ("mapDrag",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.markerDragEnd(i))}),e.qZA()}if(2&o){const t=s.$implicit,n=e.oxw(2);e.Q6J("options",n.markerOptions)("position",t.position)}}function Ue(o,s){1&o&&e._uU(0),2&o&&e.hij(" ",s.$implicit.regionName," ")}function Ee(o,s){if(1&o&&(e.TgZ(0,"div",53),e._uU(1),e.qZA()),2&o){const t=s.$implicit;e.xp6(1),e.hij(" ",t.regionName," ")}}function Fe(o,s){1&o&&e._uU(0),2&o&&e.hij(" ",s.$implicit.cityName," ")}function Je(o,s){if(1&o&&(e.TgZ(0,"div",54),e._uU(1),e.qZA()),2&o){const t=s.$implicit;e.xp6(1),e.hij(" ",t.cityName," ")}}function qe(o,s){1&o&&(e.TgZ(0,"span",55),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij("",e.lcZ(2,1,"ErrorMessages.phoneNumberIsUnvalid")," "))}function He(o,s){if(1&o&&e._UZ(0,"img",59),2&o){const t=e.oxw(3);e.Q6J("src","Home"===t.selectedAddressType?"assets/icons/mobile-icons/home-address-white.svg":"assets/icons/mobile-icons/home-address.svg",e.LSH)}}function ze(o,s){if(1&o&&e._UZ(0,"img",60),2&o){const t=e.oxw(3);e.Q6J("src","Work"===t.selectedAddressType?"assets/icons/mobile-icons/work-address-white.svg":"assets/icons/mobile-icons/work-address.svg",e.LSH)}}function Qe(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",56),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.selectAddressType(r.name))}),e.YNc(1,He,1,1,"img",57),e.YNc(2,ze,1,1,"img",58),e._uU(3),e.qZA()}if(2&o){const t=s.$implicit,n=e.oxw(2);e.ekj("selected",n.selectedAddressType===t.name),e.xp6(1),e.Q6J("ngIf","Home"===t.name),e.xp6(1),e.Q6J("ngIf","Work"===t.name),e.xp6(1),e.hij(" ",t.name," ")}}function Ye(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",61)(1,"button",62),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.onSubmit())}),e.ALo(2,"translate"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("disabled",t.validate())("label",e.lcZ(2,2,"addingAddress.confirmAddress"))}}function Ge(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",63)(1,"button",64),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.Update())}),e.ALo(2,"translate"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("disabled",t.validate())("label",e.lcZ(2,2,"addingAddress.updateAddress"))}}const O=function(o){return{"form-input-error":o}},H=function(o){return{"border-bottom":o}},z=function(){return{width:"100%",border:"none",background:"none",outline:"none","box-shadow":"none"}},se=function(o,s){return[o,s]};function Be(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"section",3),e.ynx(2),e.TgZ(3,"div",4)(4,"div",5)(5,"div",6)(6,"img",7),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBack())}),e.qZA(),e.TgZ(7,"span",8),e._uU(8),e.ALo(9,"translate"),e.qZA()(),e.TgZ(10,"div",9)(11,"div",10)(12,"google-map",11),e.NdJ("mapClick",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.mapClicked(i))})("mapInitialized",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.mapInitialize(i))}),e.YNc(13,De,1,2,"map-marker",12),e.qZA(),e.TgZ(14,"form",13)(15,"div",14)(16,"div",15)(17,"label",16),e._uU(18),e.ALo(19,"translate"),e.qZA()(),e.TgZ(20,"div",15),e._UZ(21,"input",17),e.qZA()(),e.TgZ(22,"div",18)(23,"div",19)(24,"div",20),e._uU(25),e.ALo(26,"translate"),e._UZ(27,"br"),e.TgZ(28,"p-dropdown",21,22),e.NdJ("onChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.filterCitiesByRegion(i.value))}),e.YNc(30,Ue,1,1,"ng-template",23),e.YNc(31,Ee,2,1,"ng-template",24),e.qZA()()()(),e.TgZ(32,"div",18)(33,"div",19)(34,"div",25),e._uU(35),e.ALo(36,"translate"),e._UZ(37,"br"),e.TgZ(38,"p-dropdown",26,27),e.YNc(40,Fe,1,1,"ng-template",23),e.YNc(41,Je,2,1,"ng-template",24),e.qZA()()()(),e.TgZ(42,"div",14)(43,"div",15)(44,"label",28),e._uU(45),e.ALo(46,"translate"),e.qZA()(),e.TgZ(47,"div",15)(48,"input",29),e.NdJ("input",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.getCoordinates())}),e.qZA()()(),e.TgZ(49,"div",14)(50,"div",15)(51,"label",28),e._uU(52),e.ALo(53,"translate"),e.qZA()(),e.TgZ(54,"div",15),e._UZ(55,"input",30),e.qZA()(),e.TgZ(56,"div",14)(57,"div",15)(58,"label",31),e._uU(59),e.ALo(60,"translate"),e.qZA()(),e.TgZ(61,"div",15),e._UZ(62,"input",32),e.qZA()(),e.TgZ(63,"div",14)(64,"div",15)(65,"label",33),e._uU(66),e.ALo(67,"translate"),e.qZA()(),e.TgZ(68,"div",15),e._UZ(69,"input",34),e.qZA()(),e.TgZ(70,"div",14)(71,"div",15)(72,"label",35),e._uU(73),e.ALo(74,"translate"),e.qZA()(),e.TgZ(75,"div",36)(76,"ngx-intl-tel-input",37),e.NdJ("countryChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onCountryChange(i))}),e.qZA(),e.YNc(77,qe,3,3,"span",38),e.qZA()(),e.TgZ(78,"div",14)(79,"div",15)(80,"label",39),e._uU(81),e.ALo(82,"translate"),e.qZA()(),e.TgZ(83,"div",15),e._UZ(84,"input",40),e.qZA()(),e.TgZ(85,"div",41)(86,"div",42)(87,"div",43)(88,"div",44),e._uU(89),e.ALo(90,"translate"),e.qZA(),e.TgZ(91,"div",45),e.YNc(92,Qe,4,5,"div",46),e.qZA()()()()(),e.TgZ(93,"div",47),e.YNc(94,Ye,3,4,"div",48),e.TgZ(95,"div",49)(96,"button",50),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.setAsDefault())}),e.ALo(97,"translate"),e.qZA()(),e.YNc(98,Ge,3,4,"div",51),e.qZA()()()()(),e.BQk(),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();let n,i,r,a,c,d,p;e.xp6(8),e.hij(" ",e.lcZ(9,51,"multipleAddress.completeAddress")," "),e.xp6(4),e.Q6J("center",t.center)("zoom",t.zoom)("options",t.mapOptions),e.xp6(1),e.Q6J("ngForOf",t.position),e.xp6(1),e.Q6J("formGroup",t.addressForm),e.xp6(4),e.hij("",e.lcZ(19,53,"addingAddress.country"),"*"),e.xp6(3),e.Q6J("ngClass",e.VKq(75,O,(null==(n=t.addressForm.get("country"))?null:n.hasError("required"))&&(null==(n=t.addressForm.get("country"))?null:n.touched))),e.xp6(2),e.Q6J("ngStyle",e.VKq(77,H,null!=(i=t.addressForm.get("region.regionId"))&&i.hasError("required")&&null!=(i=t.addressForm.get("region.regionId"))&&i.touched?"1px solid red":"")),e.xp6(1),e.Q6J("formGroup",t.region),e.xp6(1),e.hij(" ",e.lcZ(26,55,"addingAddress.region"),"* "),e.xp6(3),e.Akn(e.DdM(79,z)),e.Q6J("options",t.allRegionList)("filter",!0),e.xp6(5),e.Q6J("ngStyle",e.VKq(80,H,null!=(r=t.addressForm.get("city"))&&r.hasError("required")&&null!=(r=t.addressForm.get("city"))&&r.touched?"1px solid red":"")),e.xp6(2),e.hij(" ",e.lcZ(36,57,"addingAddress.city"),"* "),e.xp6(3),e.Akn(e.DdM(82,z)),e.Q6J("options",t.filteredCities)("filter",!0),e.xp6(7),e.hij("",e.lcZ(46,59,"addingAddress.addressDetails"),"*"),e.xp6(3),e.Q6J("ngClass",e.VKq(83,O,null==(a=t.addressForm.get("streetAddress"))?null:a.hasError("required"))),e.xp6(4),e.Oqu(e.lcZ(53,61,"addingAddress.otherAddressSecond")),e.xp6(7),e.Oqu(e.lcZ(60,63,"addingAddress.post-code")),e.xp6(3),e.Q6J("value",t.addressForm.value.postcode),e.xp6(4),e.hij("",e.lcZ(67,65,"addingAddress.landMark"),"*"),e.xp6(3),e.Q6J("ngClass",e.VKq(85,O,(null==(c=t.addressForm.get("landMark"))?null:c.hasError("required"))&&(null==(c=t.addressForm.get("landMark"))?null:c.touched))),e.xp6(4),e.hij("",e.lcZ(74,67,"addingAddress.phone"),"*"),e.xp6(3),e.Q6J("cssClass","custom contact-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",t.phoneInputLength)("numberFormat",t.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",t.preferredCountries)("searchCountryField",e.WLB(87,se,t.SearchCountryField.Iso2,t.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",t.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",t.customPlaceHolder)("ngClass",e.VKq(90,O,(null==(d=t.addressForm.get("receiverPhoneNumber"))?null:d.hasError("required"))||(null==(d=t.addressForm.get("receiverPhoneNumber"))?null:d.hasError("validatePhoneNumber")))),e.xp6(1),e.Q6J("ngIf",null==(p=t.addressForm.get("receiverPhoneNumber"))?null:p.hasError("validatePhoneNumber")),e.xp6(4),e.Oqu(e.lcZ(82,69,"addingAddress.instructions")),e.xp6(8),e.hij(" ",e.lcZ(90,71,"addingAddress.addressLabel")," "),e.xp6(3),e.Q6J("ngForOf",t.addressLabelList),e.xp6(2),e.Q6J("ngIf",null===t.addressService.chosenAddress),e.xp6(2),e.Q6J("disabled",null===t.addressService.chosenAddress||!0===t.addressService.chosenAddress.isDefault)("label",e.lcZ(97,73,"addingAddress.defaultAddress")),e.xp6(2),e.Q6J("ngIf",t.id&&"add-address"!==t.id)}}function Re(o,s){1&o&&(e.TgZ(0,"h2",91),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"addingAddress.addAddress")," "))}function Ve(o,s){1&o&&(e.TgZ(0,"h2",91),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"addingAddress.updateAddressTitle")," "))}function Ke(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"map-marker",52),e.NdJ("mapDrag",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.markerDragEnd(i))}),e.qZA()}if(2&o){const t=s.$implicit,n=e.oxw(2);e.Q6J("options",n.markerOptions)("position",t.position)}}function je(o,s){1&o&&e._uU(0),2&o&&e.hij(" ",s.$implicit.regionName," ")}function Xe(o,s){if(1&o&&(e.TgZ(0,"div",54),e._uU(1),e.qZA()),2&o){const t=s.$implicit;e.xp6(1),e.hij(" ",t.regionName," ")}}function $e(o,s){1&o&&e._uU(0),2&o&&e.hij(" ",s.$implicit.cityName," ")}function We(o,s){if(1&o&&(e.TgZ(0,"div",54),e._uU(1),e.qZA()),2&o){const t=s.$implicit;e.xp6(1),e.hij(" ",t.cityName," ")}}function et(o,s){1&o&&(e.TgZ(0,"span",55),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij("",e.lcZ(2,1,"ErrorMessages.phoneNumberIsUnvalid")," "))}function tt(o,s){if(1&o&&e._UZ(0,"img",59),2&o){const t=e.oxw(3);e.Q6J("src","Home"===t.selectedAddressType?"assets/icons/mobile-icons/home-address-white.svg":"assets/icons/mobile-icons/home-address.svg",e.LSH)}}function nt(o,s){if(1&o&&e._UZ(0,"img",60),2&o){const t=e.oxw(3);e.Q6J("src","Work"===t.selectedAddressType?"assets/icons/mobile-icons/work-address-white.svg":"assets/icons/mobile-icons/work-address.svg",e.LSH)}}function ot(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",56),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(2);return e.KtG(a.selectAddressType(r.name))}),e.YNc(1,tt,1,1,"img",57),e.YNc(2,nt,1,1,"img",58),e._uU(3),e.qZA()}if(2&o){const t=s.$implicit,n=e.oxw(2);e.ekj("selected",n.selectedAddressType===t.name),e.xp6(1),e.Q6J("ngIf","Home"===t.name),e.xp6(1),e.Q6J("ngIf","Work"===t.name),e.xp6(1),e.hij(" ",t.name," ")}}function it(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",61)(1,"button",92),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.onSubmit())}),e.ALo(2,"translate"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("disabled",t.validate())("label",e.lcZ(2,2,"addingAddress.addAddress"))}}function rt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",63)(1,"button",64),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.Update())}),e.ALo(2,"translate"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("disabled",t.validate())("label",e.lcZ(2,2,"addingAddress.updateAddress"))}}const st=function(o){return{"hidden-navbar":o}};function at(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"section",65)(1,"div",66),e._UZ(2,"em",67)(3,"em",68),e.TgZ(4,"span",69),e._uU(5),e.ALo(6,"translate"),e.qZA(),e._UZ(7,"em",68),e.TgZ(8,"span",69),e._uU(9),e.ALo(10,"translate"),e.qZA()(),e.TgZ(11,"div",70),e.YNc(12,Re,3,3,"h2",71),e.YNc(13,Ve,3,3,"h2",71),e.TgZ(14,"div",47)(15,"div",72)(16,"div",73)(17,"div",47)(18,"div",10)(19,"div",74)(20,"span",75),e._UZ(21,"em",76),e.qZA(),e.TgZ(22,"input",77,78),e.NdJ("onAddressChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.handleAddressChange(i))}),e.ALo(25,"translate"),e.qZA(),e.TgZ(26,"span",79),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.clear())}),e._UZ(27,"em",80),e.qZA()()(),e.TgZ(28,"div",10)(29,"google-map",81),e.NdJ("mapClick",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.mapClicked(i))})("mapInitialized",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.mapInitialize(i))}),e.YNc(30,Ke,1,2,"map-marker",12),e.qZA()(),e.TgZ(31,"form",13)(32,"div",61)(33,"div",82)(34,"div",83),e._uU(35),e.ALo(36,"translate"),e._UZ(37,"br"),e.TgZ(38,"p-dropdown",21,22),e.NdJ("onChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.filterCitiesByRegion(i.value))}),e.YNc(40,je,1,1,"ng-template",23),e.YNc(41,Xe,2,1,"ng-template",24),e.qZA()()()(),e.TgZ(42,"div",61)(43,"div",82)(44,"div",84),e._uU(45),e.ALo(46,"translate"),e._UZ(47,"br"),e.TgZ(48,"p-dropdown",26,27),e.YNc(50,$e,1,1,"ng-template",23),e.YNc(51,We,2,1,"ng-template",24),e.qZA()()()(),e.TgZ(52,"div",61)(53,"div",42),e._UZ(54,"input",32),e.TgZ(55,"label",31),e._uU(56),e.ALo(57,"translate"),e.qZA()()(),e.TgZ(58,"div",61)(59,"div",85)(60,"ngx-intl-tel-input",37),e.NdJ("countryChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onCountryChange(i))}),e.qZA(),e.YNc(61,et,3,3,"span",38),e.TgZ(62,"label",35),e._uU(63),e.ALo(64,"translate"),e.qZA()(),e.TgZ(65,"span",86),e._uU(66),e.ALo(67,"translate"),e.qZA()(),e.TgZ(68,"div",61)(69,"div",42),e._UZ(70,"input",87),e.TgZ(71,"label",16),e._uU(72),e.ALo(73,"translate"),e.qZA()()(),e.TgZ(74,"div",61)(75,"div",42)(76,"input",29),e.NdJ("input",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.getCoordinates())}),e.qZA(),e.TgZ(77,"label",28),e._uU(78),e.ALo(79,"translate"),e.qZA()()(),e.TgZ(80,"div",61)(81,"div",42),e._UZ(82,"input",30),e.TgZ(83,"label",28),e._uU(84),e.ALo(85,"translate"),e.qZA()()(),e.TgZ(86,"div",61)(87,"div",42),e._UZ(88,"input",34),e.TgZ(89,"label",33),e._uU(90),e.ALo(91,"translate"),e.qZA()()(),e.TgZ(92,"div",61)(93,"div",42),e._UZ(94,"input",40),e.TgZ(95,"label",39),e._uU(96),e.ALo(97,"translate"),e.qZA()()(),e.TgZ(98,"div",88)(99,"div",42)(100,"div",89)(101,"div",44),e._uU(102),e.ALo(103,"translate"),e.qZA(),e.TgZ(104,"div",45),e.YNc(105,ot,4,5,"div",46),e.qZA()()()()()(),e.TgZ(106,"div",47)(107,"div",63)(108,"button",90),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.setAsDefault())}),e.ALo(109,"translate"),e.qZA()(),e.YNc(110,it,3,4,"div",48),e.YNc(111,rt,3,4,"div",51),e.qZA()()()()()()}if(2&o){const t=e.oxw();let n,i,r,a,c,d,p;e.Q6J("ngClass",e.VKq(91,st,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(2),e.Q6J("routerLink","/"),e.xp6(2),e.Q6J("routerLink","/account"),e.xp6(1),e.Oqu(e.lcZ(6,61,"sideMenu.yourAccount")),e.xp6(3),e.Q6J("routerLink","/account/address"),e.xp6(1),e.Oqu(e.lcZ(10,63,"multipleAddress.myAddresses")),e.xp6(3),e.Q6J("ngIf",t.id&&"add-address"===t.id),e.xp6(1),e.Q6J("ngIf",t.id&&"add-address"!==t.id),e.xp6(9),e.s9C("placeholder",e.lcZ(25,65,"addingAddress.search")),e.Q6J("options",t.mapOptions),e.xp6(7),e.Q6J("center",t.center)("zoom",t.zoom)("options",t.mapOptions),e.xp6(1),e.Q6J("ngForOf",t.position),e.xp6(1),e.Q6J("formGroup",t.addressForm),e.xp6(2),e.Q6J("ngStyle",e.VKq(93,H,(null!=(n=t.addressForm.get("region.id"))&&n.hasError("required")&&null!=(n=t.addressForm.get("region.id"))&&n.touched?"1px solid red":"")||(null!=(n=t.addressForm.get("region"))&&n.valid?"":"1px solid red"))),e.xp6(1),e.Q6J("formGroup",t.region),e.xp6(1),e.hij(" ",e.lcZ(36,67,"addingAddress.region"),"* "),e.xp6(3),e.Akn(e.DdM(95,z)),e.Q6J("options",t.allRegionList)("filter",!0),e.xp6(5),e.Q6J("ngStyle",e.VKq(96,H,(null!=(i=t.addressForm.get("city"))&&i.hasError("required")&&null!=(i=t.addressForm.get("city"))&&i.touched?"1px solid red":"")||(null!=(i=t.addressForm.get("city"))&&i.valid?"":"1px solid red"))),e.xp6(2),e.hij(" ",e.lcZ(46,69,"addingAddress.city"),"* "),e.xp6(3),e.Akn(e.DdM(98,z)),e.Q6J("options",t.filteredCities)("filter",!0),e.xp6(6),e.Q6J("value",t.addressForm.value.postcode),e.xp6(2),e.Oqu(e.lcZ(57,71,"addingAddress.post-code")),e.xp6(4),e.Q6J("cssClass","custom contact-input-phone region-label")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",t.phoneInputLength)("numberFormat",t.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",t.preferredCountries)("searchCountryField",e.WLB(99,se,t.SearchCountryField.Iso2,t.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",t.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",t.customPlaceHolder)("ngClass",e.VKq(102,O,(null==(r=t.addressForm.get("receiverPhoneNumber"))?null:r.hasError("required"))&&(null==(r=t.addressForm.get("receiverPhoneNumber"))?null:r.touched))),e.xp6(1),e.Q6J("ngIf",(null==(a=t.addressForm.get("receiverPhoneNumber"))?null:a.hasError("validatePhoneNumber"))&&(null==(a=t.addressForm.get("receiverPhoneNumber"))?null:a.touched)),e.xp6(2),e.hij("",e.lcZ(64,73,"addingAddress.RecipientContactNumber"),"*"),e.xp6(3),e.hij("*",e.lcZ(67,75,"addingAddress.phoneNote"),""),e.xp6(4),e.Q6J("ngClass",e.VKq(104,O,(null==(c=t.addressForm.get("country"))?null:c.hasError("required"))&&(null==(c=t.addressForm.get("country"))?null:c.touched))),e.xp6(2),e.hij("",e.lcZ(73,77,"addingAddress.country"),"*"),e.xp6(4),e.Q6J("ngClass",e.VKq(106,O,null==(d=t.addressForm.get("streetAddress"))?null:d.hasError("required"))),e.xp6(2),e.hij("",e.lcZ(79,79,"addingAddress.addressDetails"),"*"),e.xp6(6),e.Oqu(e.lcZ(85,81,"addingAddress.otherAddressSecond")),e.xp6(4),e.Q6J("ngClass",e.VKq(108,O,(null==(p=t.addressForm.get("landMark"))?null:p.hasError("required"))&&(null==(p=t.addressForm.get("landMark"))?null:p.touched))),e.xp6(2),e.hij("",e.lcZ(91,83,"addingAddress.landMark"),"*"),e.xp6(6),e.Oqu(e.lcZ(97,85,"addingAddress.instructions")),e.xp6(6),e.hij(" ",e.lcZ(103,87,"addingAddress.addressLabel")," "),e.xp6(3),e.Q6J("ngForOf",t.addressLabelList),e.xp6(3),e.Q6J("disabled",null===t.addressService.chosenAddress||!0===t.addressService.chosenAddress.isDefault)("label",e.lcZ(109,89,"addingAddress.defaultAddress")),e.xp6(2),e.Q6J("ngIf",t.id&&"add-address"===t.id),e.xp6(1),e.Q6J("ngIf",t.id&&"add-address"!==t.id)}}function lt(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"app-mtn-success-modal",93),e.NdJ("submit",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onConfrim())}),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("displayModal",t.isDisplaySuccessModal)("message",t.message)}}function dt(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"app-address-label",94),e.NdJ("submit",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.addAddress(i))})("cancel",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onAddressDialogCancel(i))}),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("displayModal",t.isDisplayOtherModal)}}let ae=(()=>{class o{ngZone;addressService;messageService;router;store;route;translate;mainDataService;loaderService;_location;cd;permissionService;appDataService;$gtmService;platformId;routeToCheckOut;markerOptions={draggable:!0,icon:"assets/images/map-pin.svg"};latitude;longitude;phoneLength=13;position;landMarkAddressRequired=!1;isMobileTemplate=!1;isDisplaySuccessModal=!1;isDisplayOtherModal=!1;message="";addressDetailCity="";placesRef;addressForm=new l.nJ({addressLabel:new l.p4("Home"),receiverFirstName:new l.p4(""),receiverLastName:new l.p4(""),streetAddress:new l.p4("",l.kI.required),country:new l.p4("",l.kI.required),city:new l.p4("",l.kI.required),landMark:new l.p4("",l.kI.required),deliveryInstructions:new l.p4(""),buldingNumber:new l.p4(""),postcode:new l.p4(""),receiverPhoneNumber:new l.p4("",l.kI.required),geo_location:new l.p4(""),Lat:new l.p4(""),Lng:new l.p4(""),Id:new l.p4(""),additionalAddress:new l.p4(""),region:new l.nJ({id:new l.p4(""),regionName:new l.p4("")},l.kI.required)});search="";Lat;Lng;zoom;address;myplaceHolder;source;isDefault=!1;navbarData;center={lat:.3,lng:32.5};mapOptions={fullscreenControl:!1,disableDefaultUI:!0,componentRestrictions:{country:"UG"}};allCities=[];isDifferentCity=!1;addressLabelList=[{name:"Home",id:1},{name:"Work",id:2},{name:"Other",id:3}];selectedAddressType;searchElementRef;id="";routeSub;geoCoder=new google.maps.Geocoder;redirectUrl;map;borderBottomStyle="2px solid red !important";phoneInputLength=12;preferredCountries=[x.HT.Uganda,x.HT.Ghana,x.HT.C\u00f4teDIvoire];CustomCountryISO;customPlaceHolder="";PhoneNumberFormat=x.M9;SearchCountryField=x.wX;allRegionList=[];filteredCities=[];screenWidth=window.innerWidth;lat;lng;selectedCitiesValue=[];countryCoordinates={UG:{lat:.3136,lng:32.5811,country:"UG"},GH:{lat:5.595465,lng:-.242603,country:"GH"},CI:{lat:7.5399,lng:-5.5471,country:"CI"}};onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_,f,y,C,v,N){this.ngZone=t,this.addressService=n,this.messageService=i,this.router=r,this.store=a,this.route=c,this.translate=d,this.mainDataService=p,this.loaderService=h,this._location=_,this.cd=f,this.permissionService=y,this.appDataService=C,this.$gtmService=v,this.platformId=N,this.isMobileTemplate=this.permissionService.hasPermission("Mobile-Layout"),this.source=this.router.getCurrentNavigation()?.extras?.state;let w=localStorage.getItem("tenantId");w&&""!==w&&("1"==w||"2"==w||"3"==w?this.customPlaceHolder="XXXXXXXXX":"4"==w&&(this.customPlaceHolder="XXXXXXXXXX")),this.setMapCenterFromIsoCode()}setMapCenterFromIsoCode(){const t=localStorage.getItem("isoCode");if(t&&this.countryCoordinates[t]){const n=this.countryCoordinates[t];this.center={lat:n.lat,lng:n.lng},this.mapOptions={...this.mapOptions,componentRestrictions:{country:n.country}}}else{const n=this.countryCoordinates.UG;this.center={lat:n.lat,lng:n.lng},this.mapOptions={...this.mapOptions,componentRestrictions:{country:n.country}}}}onBack(){this._location.back()}ngOnInit(){var t=this;this.route.queryParamMap.subscribe(d=>{this.routeToCheckOut=d.get("checkout")}),this.navbarData=this.appDataService.layoutTemplate.find(d=>"navbar"===d.type),this.lat=history.state.lat,this.lng=history.state.lng,this.route.queryParams.subscribe(d=>{this.redirectUrl=d.returnUrl});let n=this.store.get("profile");this.routeSub=this.route.params.subscribe(function(){var d=(0,G.Z)(function*(p){if(t.id=p.id,"add-address"!=t.id)t.getCustomerAddress(),t.$gtmService.pushPageView("Your Addresses","Update Address");else{t.$gtmService.pushPageView("Your Addresses","Add Address");const h=localStorage.getItem("userPhone"),_=n.isdCode?n.isdCode?.replace("+",""):localStorage.getItem("CountryPhone")?.replace("+","")||"",f=h?.replace(_,"");t.addressForm.get("receiverPhoneNumber")?.setValue(f),t.addressForm.get("receiverPhoneNumber")?.markAllAsTouched();const C=(yield t.getAllRegion()).find(v=>v.isDefault);C&&(t.filteredCities=C.cities,t.addressForm.patchValue({region:C})),t.lat&&t.lng?t.setCurrentLocation({lat:t.lat,lng:t.lng}):t.setCurrentLocation()}});return function(p){return d.apply(this,arguments)}}());let i=localStorage.getItem("PhoneLength")?.toString(),r=localStorage.getItem("customerAddressLandmarkRequired")?.toString();r&&"True"==r&&(this.landMarkAddressRequired=!0,this.addressForm.controls.landMark.setValidators([l.kI.required]),this.addressForm.controls.landMark.updateValueAndValidity()),i&&(this.phoneLength=parseInt(i)-2),this.mainDataService.setUserData(n);let a=n?.name?.split(" ");if(this.addressForm.patchValue({receiverFirstName:a[0]?a[0]:"",receiverLastName:a[1]?a[1]:""}),localStorage.getItem("isoCode"))this.CustomCountryISO=n.country?n.country:localStorage.getItem("isoCode");else{const d=this.appDataService.tenants;if(null!=d.records){let p=localStorage.getItem("tenantId"),_=d.records.find(f=>f.tenantId==p)??new q.Sb;localStorage.setItem("isoCode",_?.isoCode),this.store.set("allCountryTenants",d.records)}}let c=n.country?n.country:localStorage.getItem("isoCode");if(this.appDataService.configuration&&"UG"==c||"GH"==c||"CI"==c){const d=this.appDataService.configuration.records.find(p=>"PhoneLength"===p.key);d&&(this.phoneInputLength=parseInt(d.value))}else this.phoneInputLength=20;this.filteredCities=this.allCities,this.region.valueChanges.subscribe(d=>{const p=this.allRegionList?.find(h=>h.id==d.id);this.addressForm.patchValue({region:{id:p.id,regionName:p.regionName}},{emitEvent:!1})})}onCountryChange(t){if(console.log(t),"UG"!==t.iso2.toUpperCase()&&"GH"!==t.iso2.toUpperCase()&&"CI"!==t.iso2.toUpperCase())this.phoneInputLength=20;else if(this.appDataService.configuration){const n=this.appDataService.configuration.records.find(i=>"PhoneLength"===i.key);n&&(this.phoneInputLength=parseInt(n.value))}}mapInitialize(t){this.map=t}handleAddressChange(t){this.Lat=t.geometry.location.lat(),this.Lng=t.geometry.location.lng(),this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.zoom=12,this.center=this.position[0].position,this.getAddress(this.Lat,this.Lng)}ngOnDestroy(){this.routeSub.unsubscribe()}getCustomerAddress(){var t=this;return(0,G.Z)(function*(){t.loaderService.show();try{const n=yield t.addressService.getAddressById(t.id).toPromise();if(t.addressService.loadedAddress=!0,n.data){if(t.isDefault=n.data.isDefault,t.addressService.chosenAddress=n.data,t.CustomCountryISO=n.data.countryISO,t.appDataService.configuration&&"UG"==t.CustomCountryISO||"GH"==t.CustomCountryISO||"CI"==t.CustomCountryISO){const d=t.appDataService.configuration.records.find(p=>"PhoneLength"===p.key);d&&(t.phoneInputLength=parseInt(d.value))}else t.phoneInputLength=20;const i=t.trimPhoneNumber(n.data.isdCode,n.data.receiverPhoneNumber);yield t.getAllRegion();const r=t.selectedRegion(n.data.region),a=t.selectedCity(n.data.city);t.addressForm.patchValue({addressLabel:n.data.addressLabel,receiverFirstName:n.data.receiverFirstName,receiverLastName:n.data.receiverLastName,postcode:n.data.postCode,receiverPhoneNumber:i,landMark:n.data.landMark,deliveryInstructions:n.data.deliveryInstructions,buldingNumber:n.data.buldingNumber,additionalAddress:n.data.additionalAddress,city:a.cityName,region:r}),"Home"!=n.data.addressLabel&&"Work"!=n.data.addressLabel&&(t.addressLabelList[2].name=n.data.addressLabel),t.selectedAddressType=n.data.addressLabel,t.Lat=parseFloat(n.data.lat),t.Lng=parseFloat(n.data.lng),t.searchElementRef&&(t.searchElementRef.nativeElement.value=n.data.streetAddress),t.position=[{position:{lat:t.Lat,lng:t.Lng}}],t.zoom=8,t.center=t.position[0].position,n.data.streetAddress||n.data.country?t.addressForm.patchValue({streetAddress:n.data.streetAddress,country:n.data.country}):t.getAddress(t.Lat,t.Lng),t.createLocationButton()}else t.setCurrentLocation(),t.createLocationButton();t.loaderService.hide()}catch(n){t.addressService.loadedAddress=!0,t.setCurrentLocation(),t.messageService.add({severity:"error",summary:t.translate.instant("ErrorMessages.fetchError"),detail:n.message})}finally{t.loaderService.hide()}})()}trimPhoneNumber(t,n){let i=this.store.get("profile");return n.length>3?n.substring(t?t.replace("+","").length:i?.isdCode?.replace("+","").length):n}selectedRegion(t){const n=this.allRegionList.find(i=>i.regionName===t);return n?(this.filterCitiesByRegion(n.id),this.selectedCitiesValue=n.cities,{id:n.id,regionName:n.regionName}):{id:-1,regionName:null}}selectedCity(t){const n=this.selectedCitiesValue.find(i=>i.cityName===t);return n?{id:n.id,cityName:n.cityName}:{id:-1,cityName:null}}getAddress(t,n){this.geoCoder.geocode({location:{lat:t,lng:n}},(i,r)=>{if("OK"===r)if(i[0]){const a=i[0].address_components.find(d=>d.types.includes("country"));console.log(a);const c=localStorage.getItem("isoCode");if(a&&a.short_name===c){if(this.position=[{position:{lat:t,lng:n}}],this.center=this.position[0].position,this.zoom=12,this.address=i[0].formatted_address,i[0]?.address_components.length){const d=i[0].address_components.find(p=>p.types.includes("locality"));this.addressDetailCity=d.long_name}this.addressForm.patchValue({streetAddress:this.address,country:i[i.length-1].formatted_address}),this.validate(),this.getCoordinates(),this.cd.detectChanges()}else this.setMapCenterFromIsoCode(),this.position=[{}],this.cd.detectChanges()}else(0,m.NF)(this.platformId)&&window.alert("No results found");else(0,m.NF)(this.platformId)&&window.alert("Geocoder failed due to: "+r)})}clear(){this.searchElementRef.nativeElement.value=""}onSubmit(){if(this.addressForm.patchValue({Lat:this.Lat?this.Lat.toString():"",Lng:this.Lng?this.Lng.toString():""}),this.loaderService.show(),""==this.addressForm.value.postcode&&(this.addressForm.value.postcode=0),this.addressForm.valid){const t={...this.addressForm.value,region:this.addressForm.value.region.regionName,receiverPhoneNumber:this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)};""===this.addressForm.value.postcode&&delete this.addressForm.value.postcode,this.addressService.addAddress(t).subscribe({next:n=>{n?.success?(this.loaderService.hide(),this.isDisplaySuccessModal=!0,this.message=this.translate.instant("ResponseMessages.addressAddedSuccessfully")):(this.loaderService.hide(),this.messageService.add({severity:"error",summary:n?.message}))},error:n=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n})}})}}onConfrim(){this.isDisplaySuccessModal=!1,this.routeToCheckOut?this.router.navigateByUrl("/checkout/selectAddress"):this.router.navigate(["/account/address"])}Update(){if(""!==this.Lat?.toString()&&""!==this.Lng?.toString()&&""!==this.addressForm.controls.streetAddress.value){if(this.addressForm.patchValue({Lat:this.Lat?.toString(),Lng:this.Lng?.toString(),Id:this.addressService.chosenAddress.id}),this.loaderService.show(),this.addressForm.valid){const t={...this.addressForm.value,region:this.addressForm.value.region.regionName,receiverPhoneNumber:this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)};this.addressService.updateAddress(t).subscribe({next:n=>{this.loaderService.hide(),n.success?(this.isDisplaySuccessModal=!0,this.message=this.translate.instant("ResponseMessages.addressUpdatedSuccessfully")):this.messageService.add({severity:"error",summary:this.translate.instant("ResponseMessages.address"),detail:this.translate.instant(n.message)})},error:n=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n})}})}}else this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.addressIsRequired")})}OnlyNumeric(t){return Number(t.value)||(this.addressForm.value.postcode="",this.addressForm.value.phone=""),!1}checkPlaceHolder(){this.myplaceHolder?this.myplaceHolder="":(this.myplaceHolder=localStorage.getItem("countryPhone")?.toString(),this.myplaceHolder=this.myplaceHolder?this.myplaceHolder+" 000 000 000":"256 000 000 000")}validate(){if(!this.addressForm.valid)return!0}setAsDefault(){this.addressService.setDefault(this.id).subscribe({next:t=>{this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.address"),detail:this.translate.instant("ResponseMessages.defaultAddressSuccessfully")}),this.router.navigate(this.redirectUrl&&""!==this.redirectUrl?[this.redirectUrl]:["/account/address"])},error:t=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t})}})}setCurrentLocation(t){this.addressService.chosenAddress=null,"geolocation"in navigator&&(t?(this.Lat=parseFloat(t.lat),this.Lng=parseFloat(t.lng),this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.getAddress(this.Lat,this.Lng),this.createLocationButton()):navigator.geolocation.getCurrentPosition(n=>{this.Lat=n.coords.latitude,this.Lng=n.coords.longitude,this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.center=this.position[0].position,this.zoom=12,this.getAddress(this.Lat,this.Lng),this.createLocationButton()}))}mapClicked(t){let n=JSON.parse(JSON.stringify(t.latLng));this.Lat=n.lat,this.Lng=n.lng,this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.center=this.position[0].position,this.zoom=12,this.getAddress(this.Lat,this.Lng)}createLocationButton(){if((0,m.NF)(this.platformId)){const t=document.createElement("div");t.index=100,this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(t)}}markerDragEnd(t){if(null!=t.latLng){const n=t.latLng.toJSON();this.getAddress(n.lat,n.lng)}}getCities(){this.addressService.getAllCities({currentPage:1,pageSize:5,ignorePagination:!0}).subscribe(n=>{n.success&&(this.allCities=n.data.records)})}getCoordinates(){var t=new google.maps.Geocoder;this.isDifferentCity=!0,t.geocode({address:this.addressForm.controls.streetAddress.value},(n,i)=>{i==google.maps.GeocoderStatus.OK&&n[0].address_components.length&&n[0].address_components.find(a=>a.types.includes("locality")).long_name===this.addressDetailCity&&(this.isDifferentCity=!1,this.cd.detectChanges())})}getAllRegion(){var t=this;return(0,G.Z)(function*(){let n={currentPage:1,pageSize:5,ignorePagination:!0};try{const i=yield t.addressService.getAllRegions(n).toPromise();return i.success?(t.allRegionList=i.data.records,t.filteredCities=i.data.records[0].cities,i.data.records):[]}catch{return t.messageService.add({severity:"error",summary:t.translate.instant("ErrorMessages.fetchError")}),[]}})()}filterCitiesByRegion(t){const n=this.allRegionList.find(i=>i.id===t);n&&(this.filteredCities=n?.cities)}selectAddressType(t){this.selectedAddressType=t,this.addressForm.patchValue({addressLabel:t}),"Other"==this.selectedAddressType&&(this.isDisplayOtherModal=!0)}addAddress(t){t&&(this.addressForm.patchValue({addressLabel:t}),this.addressLabelList[2].name=t),this.isDisplayOtherModal=!1}onAddressDialogCancel(t){this.isDisplayOtherModal=t}get region(){return this.addressForm.get("region")}static \u0275fac=function(n){return new(n||o)(e.Y36(e.R0b),e.Y36(g.DM),e.Y36(T.ez),e.Y36(A.F0),e.Y36(g.d6),e.Y36(A.gz),e.Y36(b.sK),e.Y36(g.iI),e.Y36(g.D1),e.Y36(m.Ye),e.Y36(e.sBO),e.Y36(g.$A),e.Y36(g.UW),e.Y36(U.J),e.Y36(e.Lbi))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-address"]],viewQuery:function(n,i){if(1&n&&(e.Gf(Ie,5),e.Gf(Ne,5)),2&n){let r;e.iGM(r=e.CRH())&&(i.placesRef=r.first),e.iGM(r=e.CRH())&&(i.searchElementRef=r.first)}},hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:5,vars:4,consts:[[4,"ngIf","ngIfElse"],["oldContainer",""],[4,"ngIf"],[1,"account-page-mobile"],[1,"d-flex","cart-mobile-new__address-layout","flex-row"],[1,"d-inline-flex","cart-mobile-new__address-layout__address-items-section"],[1,"header-container"],["src","assets/icons/mobile-icons/back-icon.svg","alt","back-icon",3,"click"],[1,"header-container__header-detail"],[1,"complete-address","mb-5"],[1,"col-12"],["height","111px","width","100%",3,"center","zoom","options","mapClick","mapInitialized"],[3,"options","position","mapDrag",4,"ngFor","ngForOf"],[1,"grid",3,"formGroup"],[1,"form-bg"],[1,"d-flex"],["for","country"],["formControlName","country","id","country","pInputText","","type","text",1,"form-input","input-address",3,"ngClass"],[1,"col-12","col-md-6","mt-3","region-dropdown"],[1,"search-container","select-dropdown-merchant","city-dropdown","region-address",3,"ngStyle"],[1,"product-label","region-label",3,"formGroup"],["formControlName","id","optionValue","id","optionLabel","regionName","filterBy","regionName",1,"custom-dropdown",3,"options","filter","onChange"],["ddRegion",""],["pTemplate","selectedItem"],["pTemplate","item"],[1,"product-label","region-label"],["formControlName","city","optionValue","cityName","optionLabel","cityName","filterBy","cityName",1,"custom-dropdown",3,"options","filter"],["ddCity",""],["for","address-type"],["formControlName","streetAddress","id","address-type","pInputText","","type","text",1,"form-input",3,"ngClass","input"],["formControlName","additionalAddress","id","other-address-second","pInputText","","type","text",1,"form-input"],["for","city"],["formControlName","postcode","id","postcode","pInputText","","type","number",1,"form-input",3,"value"],["for","landMark"],["formControlName","landMark","id","landMark","pInputText","","type","text",1,"form-input",3,"ngClass"],["for","receiverPhoneNumber"],[1,"d-flex","contact-address","flex-column"],["formControlName","receiverPhoneNumber","name","receiverPhoneNumber","id","receiverPhoneNumber",3,"cssClass","enableAutoCountrySelect","enablePlaceholder","maxLength","numberFormat","phoneValidation","preferredCountries","searchCountryField","searchCountryFlag","selectFirstCountry","selectedCountryISO","separateDialCode","customPlaceholder","ngClass","countryChange"],["class","error-msg",4,"ngIf"],["for","instructions"],["formControlName","deliveryInstructions","id","instructions","pInputText","","type","text",1,"form-input"],[1,"col-12","col-md-12","mt-3","form-bg"],[1,"p-float-label","w-full"],[1,"search-container","select-dropdown-merchant","bg-address"],[1,"product-label","bg-address"],[1,"options-address-list","d-flex"],["class","custom-option",3,"selected","click",4,"ngFor","ngForOf"],[1,"grid"],["class","col-12 col-md-6 mt-3",4,"ngIf"],[1,"col-12","col-md-6","button-address"],["pButton","","type","button",1,"defaultBtn","w-100","add-address-btn",3,"disabled","label","click"],["class","col-12 col-md-6 mt-3 button-address",4,"ngIf"],[3,"options","position","mapDrag"],[1,""],[1,"search-name"],[1,"error-msg"],[1,"custom-option",3,"click"],["alt","Home Icon",3,"src",4,"ngIf"],["alt","Work Icon",3,"src",4,"ngIf"],["alt","Home Icon",3,"src"],["alt","Work Icon",3,"src"],[1,"col-12","col-md-6","mt-3"],["pButton","","type","button",1,"w-full","second-btn","add-address-btn",3,"disabled","label","click"],[1,"col-12","col-md-6","mt-3","button-address"],["pButton","","type","button",1,"w-full","confirmBtn",3,"disabled","label","click"],[1,"address",3,"ngClass"],[1,"breadcrumb-address","d-flex"],["aria-hidden","true",1,"pi","pi-home","cursor-pointer",3,"routerLink"],["aria-hidden","true",1,"pi","pi-angle-left"],[3,"routerLink"],[1,"content-container","mt-5",2,"padding-top","1px","max-width","1057px"],["class","add-address","style","font-family: var(--medium-font)",4,"ngIf"],[1,"col-12","col-md-12","mb-4"],[1,"address-card","shadow-1","p-3","mb-4"],[1,"p-inputgroup","search-group"],[1,"p-inputgroup-addon","icon-bg"],[1,"pi","pi-search"],["ngx-gp-autocomplete","",1,"map-search",3,"options","placeholder","onAddressChange"],["placesRef","ngx-places","search",""],[1,"p-inputgroup-addon","icon-bg",3,"click"],[1,"pi","pi-times-circle"],["height","224px","width","100%",3,"center","zoom","options","mapClick","mapInitialized"],[1,"search-container","select-dropdown-merchant","city-dropdown",3,"ngStyle"],[1,"product-label",3,"formGroup"],[1,"product-label"],[1,"p-float-label","w-full","contact-input-phone-container"],[1,"note"],["formControlName","country","id","country","pInputText","","type","text",1,"form-input",3,"ngClass"],[1,"col-12","col-md-12","mt-0","form-bg"],[1,"select-dropdown-merchant","bg-address"],["pButton","","type","button",1,"defaultBtn","w-100",3,"disabled","label","click"],[1,"add-address",2,"font-family","var(--medium-font)"],["pButton","","type","button",1,"w-full","second-btn",3,"disabled","label","click"],[3,"displayModal","message","submit"],[3,"displayModal","submit","cancel"]],template:function(n,i){if(1&n&&(e.YNc(0,Be,99,92,"ng-container",0),e.YNc(1,at,112,110,"ng-template",null,1,e.W1O),e.YNc(3,lt,2,2,"ng-container",2),e.YNc(4,dt,2,1,"ng-container",2)),2&n){const r=e.MAs(2);e.Q6J("ngIf",i.isMobileTemplate&&i.screenWidth<=768)("ngIfElse",r),e.xp6(3),e.Q6J("ngIf",i.isDisplaySuccessModal),e.xp6(1),e.Q6J("ngIf",i.isDisplayOtherModal)}},dependencies:[T.jx,B.o,l._Y,l.Fj,l.wV,l.JJ,l.JL,l.sg,l.u,m.mk,m.sg,m.O5,m.PC,S.Hq,A.rH,ee.l,oe,F.b6,F.O_,ie.Lt,x.FV,x.mh,Le,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.address[_ngcontent-%COMP%]{min-height:100%}.address[_ngcontent-%COMP%]   .address-card[_ngcontent-%COMP%]{min-height:50vh;width:100%;background-color:#fff;border-radius:5px}.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .map-search[_ngcontent-%COMP%]{width:100%;background-color:#f5f5f5!important;box-shadow:0 2px 4px #00000003;padding:13px 5px;font-size:15px;color:#000;font-family:var(--regular-font);border-top:1px solid #ced4da!important;border-bottom:1px solid #ced4da!important;border-left:unset;border-right:unset}.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .icon-bg[_ngcontent-%COMP%]{background-color:#f5f5f5!important;cursor:pointer}.address[_ngcontent-%COMP%]   .map[_ngcontent-%COMP%]{width:100%;height:224px;border:1px solid #ced4da;border-radius:5px}.address[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]{position:relative;width:100%;background-color:#f5f5f5!important;box-shadow:0 2px 4px #00000003;padding:22px 10px 13px;font-size:16px;color:#000;border-bottom:1.5px solid #aeaeae;font-family:var(--regular-font)}.address[_ngcontent-%COMP%]   agm-map[_ngcontent-%COMP%]{height:300px}[_nghost-%COMP%]     .address .p-float-label label{top:30px;font-size:11px;color:#323232;font-weight:500;font-family:var(--medium-font)}[_nghost-%COMP%]     .address .p-float-label input~label{top:15px!important}[_nghost-%COMP%]     .address .p-float-label.contact-input-phone-container label{top:20px}.button-address[_ngcontent-%COMP%]{font-size:15px}.button-address[_ngcontent-%COMP%]   .defaultBtn[_ngcontent-%COMP%]{text-align:center;letter-spacing:0px;color:var(--header_bgcolor);text-transform:uppercase;background:transparent;border:1px solid var(--header_bgcolor);padding:10px 20px;font-size:15px;border-radius:25px;width:70%;outline:0 none;font-family:var(--medium-font)}.button-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%]{color:#fff;background-color:var(--header_bgcolor);padding:10px 20px;border-radius:25px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none}.search-container[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:1px;padding:8px 16px;height:60px;border-bottom:1.5px solid #aeaeae}@media only screen and (max-width: 767px){.search-container[_ngcontent-%COMP%]{border-bottom:none!important}}.select-dropdown-merchant[_ngcontent-%COMP%]{width:100%}.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-filter-container input{border:1px solid #ced4da!important}.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-items-wrapper ul{padding-left:0}.product-label[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#323232;font-family:var(--medium-font);font-size:12px;font-style:normal;font-weight:400;line-height:normal}@media only screen and (max-width: 767px){.product-label[_ngcontent-%COMP%]{font-size:10px;font-weight:300;font-family:var(--regular-font)}}.custom-dropdown[_ngcontent-%COMP%]     .p-dropdown .p-dropdown-label{background:transparent;border:0 none;padding:0;font-family:var(--medium-font);font-style:normal;font-weight:400;font-size:14px;line-height:16px;color:#323232}@media screen and (max-width: 720px){.content-container.mt-5[_ngcontent-%COMP%]{padding-top:0rem!important}.address[_ngcontent-%COMP%]{margin-top:200px!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:150px!important}}.breadcrumb-address[_ngcontent-%COMP%]{background-color:#efeded;padding:1rem 2rem}.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{padding:0 6px;margin:auto 0}.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px}.add-address[_ngcontent-%COMP%]{font-size:28px;font-weight:700;font-family:var(--medium-font)!important}  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight{color:#495057;background:none!important;width:100%}.search-name[_ngcontent-%COMP%]{padding:5px;font-size:14px;font-weight:500;color:#323232;font-family:var(--medium-font)!important}  .p-dropdown-items-wrapper{background-color:#f5f5f5}@media only screen and (max-width: 767px){  .p-dropdown-items-wrapper{margin-top:0!important}}.select-dropdown-merchant[_ngcontent-%COMP%]     .p-overlay.p-component{min-width:784px!important;transform-origin:center top;top:17px!important;left:0;margin-left:-15px}.city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component{min-width:380px!important;transform-origin:center top;top:17px!important;left:0;margin-left:-15px}@media only screen and (max-width: 767px){.city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component{min-width:100%!important;margin-left:0!important}}.second-btn[_ngcontent-%COMP%]{font-weight:500;font-size:14px;text-transform:uppercase}button[_ngcontent-%COMP%]{font-weight:500}  .custom-map-control-button{cursor:pointer!important}.account-page-mobile[_ngcontent-%COMP%]{margin-top:75px!important}.cart-mobile-new__address-layout__address-items-section[_ngcontent-%COMP%]{max-width:100%;width:100%;border:1px solid #e4e7e9;border-radius:4px;flex-direction:column;background:#f6f6f6}.header-container[_ngcontent-%COMP%]{padding:16px;font-size:16px;font-weight:500;color:#2d2d2d}.complete-address[_ngcontent-%COMP%]{max-width:1057px;padding:0 16px 16px}.form-bg[_ngcontent-%COMP%]{margin-top:20px;background:white;width:100%;padding:16px;border-radius:8px}.input-address[_ngcontent-%COMP%]{padding:0!important}.region-address[_ngcontent-%COMP%]{background:white;align-content:center;padding:12px 12px 12px 13px!important;border:none}.region-label[_ngcontent-%COMP%]{background:white!important}.region-dropdown[_ngcontent-%COMP%]{background:white;border-radius:8px}.p-inputtext[_ngcontent-%COMP%]:enabled:hover{border:none!important;width:100%;box-shadow:none!important}@media only screen and (max-width: 767px){input[_ngcontent-%COMP%]{width:100%;padding:0!important;font-family:var(--regular-font)}label[_ngcontent-%COMP%]{font-family:var(--regular-font);font-size:10px;font-weight:300}}.custom-option[_ngcontent-%COMP%]{cursor:pointer;border-radius:4px;border:1px solid var(--primary, #204e6e);padding:4px 6px;min-width:75px;text-align:center;width:auto}.custom-option.selected[_ngcontent-%COMP%]{border-radius:4px;background:var(--primary, #204e6e);color:#fff}.bg-address[_ngcontent-%COMP%]{background:#fff;padding-left:0;font-family:var(--regular-font);font-size:10px;font-weight:300}.options-address-list[_ngcontent-%COMP%]{gap:10px;margin-top:10px}@media only screen and (max-width: 767px){.contact-address[_ngcontent-%COMP%]     .contact-input-phone{border-bottom:none!important;background-color:#fff!important;height:40px!important}  .map-container{border-radius:12px;box-shadow:2px 2px 4px #0000001a;border:1px solid #fff}}.add-address-btn[_ngcontent-%COMP%]{font-weight:500;font-size:14px!important;border-radius:8px!important;text-transform:capitalize!important}.note[_ngcontent-%COMP%]{font-size:small;color:#45494c;font-family:var(--medium-font)!important}"]})}return o})();var R=u(8180),V=u(8610),le=u(2051),de=u(1865),ce=u(617),pe=u(7680),K=u(6385);function ct(o,s){1&o&&(e.TgZ(0,"div",9),e._UZ(1,"p-progressSpinner"),e.qZA())}function pt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"p-radioButton",29),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw(3);return e.KtG(r.radioPrimary=i)}),e.qZA()}if(2&o){const t=e.oxw(3);e.Q6J("value",!0)("ngModel",t.radioPrimary)("disabled",!0)}}function gt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"p-radioButton",34),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw().index,a=e.oxw(3);return e.KtG(a.radioSecondary[r]=i)})("onClick",function(){e.CHM(t);const i=e.oxw(),r=i.$implicit,a=i.index,c=e.oxw(3);return e.KtG(c.onDefaultChange("secondary",r.id,a))}),e.qZA()}if(2&o){const t=e.oxw().index,n=e.oxw(3);e.Q6J("ngModel",n.radioSecondary[t])("value",!0)}}function mt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",18)(1,"div",19),e.YNc(2,gt,1,2,"p-radioButton",30),e.qZA(),e.TgZ(3,"div",21)(4,"div",16)(5,"label"),e._uU(6),e.ALo(7,"translate"),e.qZA()(),e.TgZ(8,"div",16),e._UZ(9,"input",17),e.qZA()(),e.TgZ(10,"div",31)(11,"div",32),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.showConfirmationModal(r.id))}),e.TgZ(12,"mat-icon",33),e._uU(13,"delete"),e.qZA()()()()}if(2&o){const t=s.$implicit,n=e.oxw(3);e.xp6(2),e.Q6J("ngIf",(null==n.allPhones?null:n.allPhones.length)>1),e.xp6(4),e.hij("",e.lcZ(7,3,"account.details.secondaryPhoneNumber")," "),e.xp6(3),e.s9C("value",t.phoneNumber)}}function ut(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"button",35),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(3);return e.KtG(i.addPhone(!1))}),e._UZ(1,"img",36),e._uU(2),e.ALo(3,"translate"),e.qZA()}2&o&&(e.xp6(2),e.hij(" ",e.lcZ(3,1,"account.details.addPhoneNumber")," "))}function ht(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",10)(2,"div",11)(3,"div",12)(4,"div",13)(5,"div",14)(6,"div",15)(7,"div",16)(8,"label"),e._uU(9),e.ALo(10,"translate"),e.qZA()(),e.TgZ(11,"div",16),e._UZ(12,"input",17),e.qZA()(),e.TgZ(13,"div",15)(14,"div",16)(15,"label"),e._uU(16),e.ALo(17,"translate"),e.qZA()(),e.TgZ(18,"div",16),e._UZ(19,"input",17),e.qZA()(),e.TgZ(20,"div",15)(21,"div",16)(22,"label"),e._uU(23),e.ALo(24,"translate"),e.qZA()(),e.TgZ(25,"div",16),e._UZ(26,"input",17),e.qZA()(),e.TgZ(27,"div",18)(28,"div",19),e.YNc(29,pt,1,3,"p-radioButton",20),e.qZA(),e.TgZ(30,"div",21)(31,"div",16)(32,"label"),e._uU(33),e.ALo(34,"translate"),e.qZA()(),e.TgZ(35,"div",16),e._UZ(36,"input",17),e.qZA()(),e.TgZ(37,"div",22)(38,"div",23)(39,"div",24),e._uU(40),e.ALo(41,"translate"),e.qZA()(),e.TgZ(42,"div",25)(43,"div",26),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.addPhone(!0))}),e._uU(44),e.ALo(45,"translate"),e.qZA()()()(),e.YNc(46,mt,14,5,"div",27),e.YNc(47,ut,4,3,"button",28),e.qZA()()()()(),e.BQk()}if(2&o){const t=e.oxw(2);e.xp6(9),e.hij("",e.lcZ(10,13,"account.details.firstName")," * "),e.xp6(3),e.s9C("value",t.userDetails.userName[0]),e.xp6(4),e.hij("",e.lcZ(17,15,"account.details.lastName")," * "),e.xp6(3),e.s9C("value",t.userDetails.userName[1]),e.xp6(4),e.hij("",e.lcZ(24,17,"account.details.email")," "),e.xp6(3),e.s9C("value",t.userDetails.email),e.xp6(3),e.Q6J("ngIf",(null==t.allPhones?null:t.allPhones.length)>1),e.xp6(4),e.hij("",e.lcZ(34,19,"account.details.phoneNumber")," * "),e.xp6(3),e.s9C("value",null==t.primaryPhone?null:t.primaryPhone.phoneNumber),e.xp6(4),e.hij(" ",e.lcZ(41,21,"account.details.default")," "),e.xp6(4),e.hij(" ",e.lcZ(45,23,"account.details.change")," "),e.xp6(2),e.Q6J("ngForOf",t.secondaryPhones),e.xp6(1),e.Q6J("ngIf",t.secondaryPhones.length<1)}}const _t=function(o){return{marginTop:o}};function ft(o,s){if(1&o&&(e.ynx(0),e.TgZ(1,"section",3)(2,"div",4)(3,"span",5),e._UZ(4,"img",6),e.qZA(),e.TgZ(5,"span",7),e._uU(6),e.ALo(7,"translate"),e.qZA()(),e.YNc(8,ct,2,0,"div",8),e.YNc(9,ht,48,25,"ng-container",2),e.qZA(),e.BQk()),2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngStyle",e.VKq(7,_t,t.screenWidth<=768?t.isMobileLayout?"5rem":"220px":"")),e.xp6(2),e.Q6J("routerLink","/account"),e.xp6(3),e.hij(" \xa0",e.lcZ(7,5,"yourDetailsbread.yourDetails"),""),e.xp6(2),e.Q6J("ngIf",t.loading),e.xp6(1),e.Q6J("ngIf",!t.loading)}}function Ct(o,s){1&o&&(e.TgZ(0,"div",9),e._UZ(1,"p-progressSpinner"),e.qZA())}function xt(o,s){1&o&&(e.TgZ(0,"p",50),e._uU(1,"Default"),e.qZA())}function bt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"p-radioButton",51),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw(3);return e.KtG(r.radioPrimary=i)}),e.qZA()}if(2&o){const t=e.oxw(3);e.Q6J("value",!0)("ngModel",t.radioPrimary)("disabled",!0)}}function vt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"p-radioButton",54),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw().index,a=e.oxw(3);return e.KtG(a.radioSecondary[r]=i)})("onClick",function(){e.CHM(t);const i=e.oxw(),r=i.$implicit,a=i.index,c=e.oxw(3);return e.KtG(c.onDefaultChange("secondary",r.id,a))}),e.qZA()}if(2&o){const t=e.oxw().index,n=e.oxw(3);e.Q6J("ngModel",n.radioSecondary[t])("value",!0)}}function yt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",18),e.YNc(1,vt,1,2,"p-radioButton",52),e._UZ(2,"input",17),e.TgZ(3,"label"),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",31)(7,"div",45)(8,"mat-icon",53),e.NdJ("click",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.showConfirmationModal(r.id))}),e._uU(9,"delete"),e.qZA()()()()}if(2&o){const t=s.$implicit,n=e.oxw(3);e.xp6(1),e.Q6J("ngIf",(null==n.allPhones?null:n.allPhones.length)>1),e.xp6(1),e.s9C("value",t.phoneNumber),e.xp6(2),e.hij("",e.lcZ(5,3,"account.details.secondaryPhoneNumber")," ")}}function At(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"span",55),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(3);return e.KtG(i.addPhone(!1))}),e._UZ(1,"em",56),e._uU(2),e.ALo(3,"translate"),e.qZA()}2&o&&(e.xp6(2),e.hij(" ",e.lcZ(3,1,"account.details.newSecondaryPhone")," "))}const Mt=function(o){return{"mt-3":o}};function wt(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",10)(2,"div",11)(3,"p",41),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",12)(7,"div",13)(8,"div",14)(9,"div",42),e._UZ(10,"input",17),e.TgZ(11,"label"),e._uU(12),e.ALo(13,"translate"),e.qZA()(),e.TgZ(14,"div",42),e._UZ(15,"input",17),e.TgZ(16,"label"),e._uU(17),e.ALo(18,"translate"),e.qZA()(),e.TgZ(19,"div",18),e.YNc(20,xt,2,0,"p",43),e.YNc(21,bt,1,3,"p-radioButton",44),e._UZ(22,"input",17),e.TgZ(23,"label"),e._uU(24),e.ALo(25,"translate"),e.qZA(),e.TgZ(26,"div",22)(27,"div",45)(28,"div",46),e._uU(29),e.ALo(30,"translate"),e.qZA()(),e.TgZ(31,"div",45)(32,"div",47),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.addPhone(!0))}),e._uU(33),e.ALo(34,"translate"),e.qZA()()()(),e.YNc(35,yt,10,5,"div",27),e.YNc(36,At,4,3,"span",48),e.TgZ(37,"div",49),e._UZ(38,"input",17),e.TgZ(39,"label"),e._uU(40),e.ALo(41,"translate"),e.qZA()()()()()()(),e.BQk()}if(2&o){const t=e.oxw(2);e.xp6(4),e.hij(" ",e.lcZ(5,16,"account.details.yourDetails")," "),e.xp6(6),e.s9C("value",t.userDetails.userName[0]),e.xp6(2),e.hij("",e.lcZ(13,18,"account.details.firstName")," * "),e.xp6(3),e.s9C("value",t.userDetails.userName[1]),e.xp6(2),e.hij("",e.lcZ(18,20,"account.details.lastName")," * "),e.xp6(3),e.Q6J("ngIf",(null==t.allPhones?null:t.allPhones.length)>1),e.xp6(1),e.Q6J("ngIf",(null==t.allPhones?null:t.allPhones.length)>1),e.xp6(1),e.s9C("value",null==t.primaryPhone?null:t.primaryPhone.phoneNumber),e.xp6(2),e.hij("",e.lcZ(25,22,"account.details.phoneNumber")," * "),e.xp6(5),e.hij(" ",e.lcZ(30,24,"account.details.default")," "),e.xp6(4),e.hij(" ",e.lcZ(34,26,"account.details.change")," "),e.xp6(2),e.Q6J("ngForOf",t.secondaryPhones),e.xp6(1),e.Q6J("ngIf",t.secondaryPhones.length<1),e.xp6(1),e.Q6J("ngClass",e.VKq(30,Mt,1===t.secondaryPhones.length)),e.xp6(1),e.s9C("value",t.userDetails.email),e.xp6(2),e.hij("",e.lcZ(41,28,"account.details.email")," ")}}const Zt=function(o){return{"hidden-navbar":o}};function Tt(o,s){if(1&o&&(e.TgZ(0,"section",37)(1,"div",38),e._UZ(2,"em",39)(3,"em",40),e.TgZ(4,"span",5),e._uU(5),e.ALo(6,"translate"),e.qZA(),e._UZ(7,"em",40),e.TgZ(8,"span",5),e._uU(9),e.ALo(10,"translate"),e.qZA()(),e.YNc(11,Ct,2,0,"div",8),e.YNc(12,wt,42,32,"ng-container",2),e.qZA()),2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngClass",e.VKq(12,Zt,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(1),e.Q6J("routerLink","/"),e.xp6(2),e.Q6J("routerLink","/account"),e.xp6(1),e.Oqu(e.lcZ(6,8,"sideMenu.yourAccount")),e.xp6(3),e.Q6J("routerLink","/account/details"),e.xp6(1),e.Oqu(e.lcZ(10,10,"yourDetailsbread.yourDetails")),e.xp6(2),e.Q6J("ngIf",t.loading),e.xp6(1),e.Q6J("ngIf",!t.loading)}}function St(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"app-confirmation-delete-dialog",57),e.NdJ("update",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.deletePhone(i))}),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("showDialog",t.displayModal)("mobile",1)}}function Ot(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"app-mtn-success-modal",58),e.NdJ("submit",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onConfrim())}),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("displayModal",t.isDisplaySuccessModal)("message",t.message)("caution",t.caution)}}let kt=(()=>{class o{store;authService;router;ref;messageService;translate;modalService;cookieService;authTokenService;mainDataService;appDataService;permissionService;$gaService;platformId;$gtmService;loading=!1;confirmPassword="";emailAddress="";lastName="";firstName="";mobileNumber="";otpCode="";hasUpperChar=!1;hasLowerChar=!1;hasMinimum8Chars=!1;hasSpecialChars=!1;passwordIsValid=!1;userDetails;password="Abcde@12_s";displayApprovedModal=!1;primaryPhone="";radioPrimary=!0;radioSecondary=[];secondaryPhones=[];displayModal=!1;selectedId="";allPhones;isDisplaySuccessModal=!1;message;caution;navbarData;tagName=I.Ir;isGoogleAnalytics=!1;screenWidth=window.innerWidth;isMobileLayout=!1;onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_,f,y,C,v,N){this.store=t,this.authService=n,this.router=i,this.ref=r,this.messageService=a,this.translate=c,this.modalService=d,this.cookieService=p,this.authTokenService=h,this.mainDataService=_,this.appDataService=f,this.permissionService=y,this.$gaService=C,this.platformId=v,this.$gtmService=N}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.$gtmService.pushPageView("account","your details"),this.navbarData=this.appDataService.layoutTemplate.find(t=>"navbar"===t.type),this.userDetails=this.store.get("profile"),this.userDetails&&(this.userDetails.userName=this.userDetails?.name?.split(" ")),this.getPhoneNumbers(),this.triggerAnalytics()}approveModal(){this.displayApprovedModal=!0}checkPasswordPattern(t){return this.hasUpperChar=/[A-Z]+/.test(t),this.hasLowerChar=/[a-z]+/.test(t),this.hasMinimum8Chars=/.{8,}/.test(t),this.hasSpecialChars=/[!@#$%^&*(),.?":{}|<>]/.test(t),this.passwordIsValid=this.hasMinimum8Chars&&this.hasUpperChar&&this.hasLowerChar&&this.hasSpecialChars,this.hasMinimum8Chars&&this.hasUpperChar&&this.hasLowerChar&&this.hasSpecialChars}getPhoneNumbers(){this.authService.getPhoneNumbers().subscribe({next:t=>{this.allPhones=t.data.records;const n=this.allPhones.filter(i=>i.isPrimary);this.primaryPhone=n.length?n[0]:"",this.secondaryPhones=this.allPhones.filter(i=>!i.isPrimary),this.secondaryPhones.forEach(i=>{this.radioSecondary.push(!1)})}})}addPhone(t){this.isGoogleAnalytics&&this.$gaService.event(D.s.CLICK_ON_ADD_NEW_NUMBER_UNDER_DETAILS,"","ACCOUNT_DETAILS_ADD_NEW_NUMBER",1,!0),t?(0,m.NF)(this.platformId)&&this.modalService.show(V.Y,{initialState:{confirmationModalDetails:{header:"Changing default number",message:"Going forward, this number will be used in:",point1:"Sign in activities",point2:"Receiving important notifications"}}}).content.submit.pipe((0,R.q)(1)).subscribe(r=>{r&&(localStorage.setItem("isPrimary","true"),this.router.navigate(["/account/verify-user"]))}):(localStorage.setItem("isPrimary","false"),this.router.navigate(["/account/verify-user"]))}deletePhone(t){this.displayModal=!1,"delete"==t&&this.authService.deletePhoneNumber(this.selectedId).subscribe({next:n=>{n.success&&(this.messageService.add({severity:"success",summary:"",detail:this.translate.instant("ResponseMessages.newSecondaryDeleteSuccessMessage")}),this.getPhoneNumbers())}})}showConfirmationModal(t){this.selectedId=t,this.displayModal=!0}onDefaultChange(t,n,i){this.radioPrimary=!1,(0,m.NF)(this.platformId)&&this.modalService.show(V.Y,{initialState:{confirmationModalDetails:{header:"Changing default number",message:"Going forward, this number will be used in:",point1:"Sign in activities",point2:"Receiving important notifications"}}}).content.submit.pipe((0,R.q)(1)).subscribe(c=>{c?this.authService.SetPrimaryPhoneNumber({id:n}).subscribe({next:d=>{d.success?(this.radioSecondary[i]=!1,this.radioPrimary=!0,this.isDisplaySuccessModal=!0,this.message="Default number chnaged successfully",this.caution="please, sign in again"):this.messageService.add({severity:"error",summary:"error",detail:d.message})},error:d=>{console.error(d)}}):(this.radioSecondary[i]=!1,this.radioPrimary=!0),this.ref.markForCheck(),this.ref.detectChanges()})}onConfrim(){this.isDisplaySuccessModal=!1,sessionStorage.clear(),this.authTokenService.authTokenSet(""),this.cookieService.delete("authToken","/"),this.store.set("profile",""),this.mainDataService.setCartLenghtData(null),this.mainDataService.setUserData(null),localStorage.setItem("secondaryDefault","false"),localStorage.setItem("sessionId",""),localStorage.setItem("addedProducts",""),localStorage.setItem("cartId",""),this.store.set("cartProducts",[]),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth_enc"),this.router.navigate(["/login"])}triggerAnalytics(){this.isGoogleAnalytics&&this.permissionService.getTagFeature("VIEW_ITEM_LIST")&&(this.$gaService.pageView("/account/details","Account Details"),this.$gaService.event(this.tagName.SEARCH,"","VIEW_ITEM_LIST",1,!0,{user_ID:this.userDetails?this.userDetails.mobileNumber:"Un_Authenticated"}))}static \u0275fac=function(n){return new(n||o)(e.Y36(g.d6),e.Y36(g.e8),e.Y36(A.F0),e.Y36(e.sBO),e.Y36(T.ez),e.Y36(b.sK),e.Y36(le.tT),e.Y36(J.N),e.Y36(g.Lz),e.Y36(g.iI),e.Y36(g.UW),e.Y36(g.$A),e.Y36(I.$r),e.Y36(e.Lbi),e.Y36(U.J))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-details"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:5,vars:4,consts:[[4,"ngIf","ngIfElse"],["desktopView",""],[4,"ngIf"],[1,"main-container",3,"ngStyle"],[1,"header-container"],[3,"routerLink"],["src","assets/icons/mobile-icons/back-icon.svg","alt","back-icon"],[1,"header-container__header-detail"],["class","spinner",4,"ngIf"],[1,"spinner"],[1,"content-container","my-3"],[1,"grid","justify-content-start"],[1,"col-12","col-md-8","col-lg-6","border-round"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12","mb-8"],[1,"p-field","p-col-12","content-container__input-form"],[1,"d-flex"],["type","text","disabled","",3,"value"],[1,"p-field","p-col-12","mt-3","flex","flex-row","justify-content-between","mb-0"],[1,"content-container__radio-btn-container","align-self-center"],["class","content-container__radio-btn-container__radio-default","name","default",3,"value","ngModel","disabled","ngModelChange",4,"ngIf"],[1,"content-container__input-form",2,"width","85% !important"],[1,"default-address-btn"],[1,"flex","flex-row","align-items-center","text-center"],[1,"default-btn","w-full"],[1,"flex","flex-row","align-items-center","text-right"],[1,"change-btn","cursor-pointer","w-full","px-0",3,"click"],["class","p-field p-col-12 mt-3 flex flex-row justify-content-between mb-0",4,"ngFor","ngForOf"],["class","w-full second-btn content-container__add-btn mt-4 gap-2","pButton","","type","button",3,"click",4,"ngIf"],["name","default",1,"content-container__radio-btn-container__radio-default",3,"value","ngModel","disabled","ngModelChange"],["class","content-container__radio-btn-container__radio-default","name","default",3,"ngModel","value","ngModelChange","onClick",4,"ngIf"],[1,"delete-icon"],[1,"flex","flex-row","align-items-center",3,"click"],[2,"color","var(--main_bt_txtcolor)","cursor","pointer"],["name","default",1,"content-container__radio-btn-container__radio-default",3,"ngModel","value","ngModelChange","onClick"],["pButton","","type","button",1,"w-full","second-btn","content-container__add-btn","mt-4","gap-2",3,"click"],["src","assets/icons/mobile-icons/add-circle.svg","alt","No Image"],[1,"main-container"],[1,"breadcrumb-address","d-flex",3,"ngClass"],["aria-hidden","true",1,"pi","pi-home","cursor-pointer",3,"routerLink"],["aria-hidden","true",1,"pi","pi-angle-left"],[1,"col-12","bold-font","font-size-28","m-0","py-0","mb-4","details-heading","mt-4"],[1,"p-field","p-col-12"],["class","default-p",4,"ngIf"],["class","radio-default","name","default",3,"value","ngModel","disabled","ngModelChange",4,"ngIf"],[1,"flex","flex-row","align-items-center"],[1,"default-btn"],[1,"change-btn","cursor-pointer",3,"click"],["class","new-number d-flex cursor-pointer",3,"click",4,"ngIf"],[1,"p-field","p-col-12",3,"ngClass"],[1,"default-p"],["name","default",1,"radio-default",3,"value","ngModel","disabled","ngModelChange"],["class","radio-default","name","default",3,"ngModel","value","ngModelChange","onClick",4,"ngIf"],[2,"color","var(--main_bt_txtcolor)","cursor","pointer",3,"click"],["name","default",1,"radio-default",3,"ngModel","value","ngModelChange","onClick"],[1,"new-number","d-flex","cursor-pointer",3,"click"],["aria-hidden","true",1,"pi","pi-plus-circle","mr-2"],[3,"showDialog","mobile","update"],[3,"displayModal","message","caution","submit"]],template:function(n,i){if(1&n&&(e.YNc(0,ft,10,9,"ng-container",0),e.YNc(1,Tt,13,14,"ng-template",null,1,e.W1O),e.YNc(3,St,2,2,"ng-container",2),e.YNc(4,Ot,2,3,"ng-container",2)),2&n){const r=e.MAs(2);e.Q6J("ngIf",i.screenWidth<768)("ngIfElse",r),e.xp6(3),e.Q6J("ngIf",i.displayModal),e.xp6(1),e.Q6J("ngIf",i.isDisplaySuccessModal)}},dependencies:[l.JJ,m.mk,m.sg,m.O5,m.PC,S.Hq,A.rH,l.On,ee.l,de.EU,x.mh,ce.Hw,pe.G,K.$,b.X$],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}[_ngcontent-%COMP%]:root{--main_bgcolor: #fafafa;--main_bt_bgcolor: #fafafa;--main_bt_txtcolor: #1d4c69;--hover_bt_bgcolor: #ffcc00;--hover_bt_txtcolor: #000000;--bt_border_color: #ffcc00;--main_hover_bt_boarder_color: #fafafa;--main_bt_border_color: #1d4c69;--main_hover_bt_bgcolor: #1d4c69;--main_hover_bt_txtcolor: #fafafa;--bt_bgcolor: #ffcc00;--bt_txtcolor: #000000;--header_bgcolor: #1a445e;--navbar_bgcolor: #1a445e;--navbar_txtcolor: #fafafa;--navbar_hover_bgcolor: #ffcc00;--navbar_hover_txtcolor: #fafafa;--footer_back_bgcolor: #345e78;--footer_back_txtcolor: #fafafa;--footer_content_bgcolor: #1d4c69;--footer_content_txtcolor: #fafafa;--footer_copy_bgcolor: #1a445e;--footer_copy_txtcolor: #fafafa;--header_txtcolor: #fafafa;--icon_bgcolor: #fafafa;--icon_txtcolor: #000000;--link_bgcolor: #0082e4;--hover_link_color: #0082e4;--input_bgcolor: #fafafa;--input_txtcolor: #000000;--input_boardercolor: #000000;--Layout-Padding-Screen: 32px;--Layout-Width-Screen: 1440px;--breadcrum_bgcolor: #1d4c69;--breadcrum_txtcolor: #fafafa;--breadcrum_iconcolor: #000000;--main-color: #1d4c69;--second-color: #1a445e;--third-color: #345e78;--fourth-color: #ffcc00;--white-color: #ffffff;--body-color: #fafafa;--flag-color: #ffcc00;--flag-font-color: #000000;--in-stock-color: #01b467;--rating-color: #fa8232;--text-color-1: #1d4c69;--text-color-2: #fafafa;--text-color-3: #000000;--light-font: "main-light";--regular-font: "main-regular";--medium-font: "main-medium";--bold-font: "main-bold";--main_border_radius: 5px;--border_color: #000000;--light-grey:#AFAFAF;--brighter-green:#3FBF6F;--warning:#EE5858;--light-blue:#E8EFFD;--gray-100:#E4E7E9;--gray-900:#191C1F;--gray-700: #475156;--gray-50: #F2F4F5;--gray-200: #C9CFD2;--dark-gray: #323232}.p-field[_ngcontent-%COMP%]{position:relative;margin-bottom:1rem}input[_ngcontent-%COMP%]{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:2px solid #b9b9b9!important;padding-left:10px;padding-right:10px;padding-top:17px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px}label[_ngcontent-%COMP%]{position:absolute;pointer-events:none;top:30%;margin-top:-.9rem;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px;font-weight:500;color:#323232;font-family:var(--medium-font)!important}.new-number[_ngcontent-%COMP%]{color:var(--main_bt_txtcolor);font-size:16px;font-weight:500;padding:14px 10px 13px 0;font-family:var(--medium-font)!important}.default-btn[_ngcontent-%COMP%]{font-size:11px;font-family:var(--medium-font);background:#FFCB05;border-radius:28px;padding-left:13px;padding-right:13px}.change-btn[_ngcontent-%COMP%]{font-size:11px;font-family:var(--medium-font);padding-left:13px;padding-right:13px;color:var(--primary, #204E6E);text-decoration:underline}.radio-default[_ngcontent-%COMP%]{position:absolute;left:-35px;top:25px}.default-p[_ngcontent-%COMP%]{position:absolute;top:-15px;left:-50px;color:#323232;font-family:var(--bold-font);font-size:11px;font-style:normal;font-weight:500;line-height:normal}@media screen and (max-width: 768px){.details-heading[_ngcontent-%COMP%]{font-size:20px!important}}.delete-icon[_ngcontent-%COMP%]{margin:auto 0;position:absolute;right:15px;top:15px}.default-address-btn[_ngcontent-%COMP%]{position:absolute;right:10px;top:10px}.margin-x-100[_ngcontent-%COMP%]{margin-top:122px!important;font-size:15px!important;margin-bottom:40px!important}.breadcrumb-address[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem 2rem;gap:10px}i.fa.fa-angle-left[_ngcontent-%COMP%], em.fa.fa-angle-left[_ngcontent-%COMP%]{padding:0 10px;margin:auto 0}.details-heading[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:700;font-size:28px;color:#000}@media only screen and (min-width: 768px) and (max-width: 1200px){.margin-x-100[_ngcontent-%COMP%]{margin-top:194px!important}}@media only screen and (max-width: 767px){.main-container[_ngcontent-%COMP%]{margin-top:93px;padding:1rem 1.5rem}.header-container__header-detail[_ngcontent-%COMP%]{color:#004f71;font-family:main-medium;font-size:16px;font-style:normal;font-weight:500;line-height:normal}.content-container[_ngcontent-%COMP%]{padding:0!important}.content-container__input-form[_ngcontent-%COMP%]{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border-bottom:none!important;padding:8px 20px;background-color:#f5f5f5!important}.content-container__input-form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{position:inherit;pointer-events:none;top:0!important;margin-top:0;left:0;padding:0;color:#323232b3;font-family:main-medium;font-size:12px;font-style:normal;font-weight:500;line-height:normal}.content-container__input-form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:auto!important;width:100%;border-radius:none!important;opacity:none;border-bottom:none!important;padding-left:0!important;padding-right:0!important;padding-top:0!important}.content-container__radio-btn-container[_ngcontent-%COMP%]{width:10%}.content-container__radio-btn-container__default-p[_ngcontent-%COMP%]{position:absolute;top:20px;left:25px;color:#323232;font-family:var(--bold-font);font-size:11px;font-style:normal;font-weight:500;line-height:normal}.content-container__radio-btn-container__radio-default[_ngcontent-%COMP%]{top:0!important;left:0!important}.content-container__add-btn[_ngcontent-%COMP%]{background:transparent!important;border-radius:8px!important;color:var(--primary, #204E6E);font-family:main-medium;font-size:14px;font-style:normal;font-weight:500;line-height:26px;letter-spacing:.168px;text-transform:uppercase;place-content:center}.content-container[_ngcontent-%COMP%]   .default-btn[_ngcontent-%COMP%]{background:#DCE6FD;padding:8px 6px;border-radius:4px;color:#022c61;font-family:main-medium;font-size:10px;font-style:normal;font-weight:400;line-height:100%}.content-container[_ngcontent-%COMP%]   .change-btn[_ngcontent-%COMP%]{color:var(--csk-204-e-6-e-400, #61A2D1);text-align:right;font-family:main-medium;font-size:11px;font-style:normal;font-weight:500;line-height:normal;text-decoration:none}.margin-x-100[_ngcontent-%COMP%]{margin-top:202px!important}}']})}return o})();function Pt(o,s){1&o&&(e.TgZ(0,"button",29),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"multipleAddress.default")," "))}function Lt(o,s){1&o&&(e.TgZ(0,"button",30),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"multipleAddress.other")," "))}function It(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"img",31),e.NdJ("click",function(){e.CHM(t);const i=e.oxw().$implicit,r=e.oxw(2);return e.KtG(r.showConfirmationModal(i.id))}),e.qZA()}}function Nt(o,s){if(1&o&&(e.TgZ(0,"span",32),e._uU(1),e.qZA()),2&o){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.addressLabel," ")}}function Dt(o,s){if(1&o&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o){const t=e.oxw().index;e.xp6(1),e.AsE("",e.lcZ(2,2,"multipleAddress.address")," ",t+1,"")}}function Ut(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",13)(2,"div",14)(3,"a",15)(4,"div",16)(5,"div",17)(6,"div",18)(7,"div",19),e.YNc(8,Pt,3,3,"button",20),e.YNc(9,Lt,3,3,"button",21),e.TgZ(10,"div",22)(11,"img",23),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.triggerAddressAnalytics("CLICK_ON_EDIT_ADDRESS","EDIT_ADDRESS"))}),e.qZA(),e.YNc(12,It,1,0,"img",24),e.qZA()()(),e.TgZ(13,"div",25),e.YNc(14,Nt,2,1,"span",26),e.YNc(15,Dt,3,4,"span",27),e.qZA(),e.TgZ(16,"span",28),e._uU(17),e.qZA()()()()()(),e.BQk()}if(2&o){const t=s.$implicit;e.xp6(8),e.Q6J("ngIf",t.isDefault),e.xp6(1),e.Q6J("ngIf",!t.isDefault&&"Home"!==t.addressLabel&&"Work"!==t.addressLabel),e.xp6(2),e.Q6J("routerLink","/account/address/"+t.id),e.xp6(1),e.Q6J("ngIf",!t.isDefault),e.xp6(2),e.Q6J("ngIf",null==t?null:t.addressLabel),e.xp6(1),e.Q6J("ngIf",!(null!=t&&t.addressLabel)),e.xp6(1),e.s9C("title",t.streetAddress),e.xp6(1),e.Oqu(t.streetAddress)}}function Et(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"section",2)(2,"div",3)(3,"div",4)(4,"div",5),e._UZ(5,"img",6),e.TgZ(6,"span",7),e._uU(7),e.ALo(8,"translate"),e.qZA()(),e.TgZ(9,"div",8),e.YNc(10,Ut,18,8,"ng-container",9),e.TgZ(11,"div",10)(12,"button",11),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.addAddressMobile())}),e.ALo(13,"translate"),e.qZA()(),e.TgZ(14,"app-confirmation-delete-dialog",12),e.NdJ("update",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDelete(i))}),e.qZA()()()()(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(5),e.Q6J("routerLink","/account"),e.xp6(2),e.hij(" ",e.lcZ(8,6,"multipleAddress.myAddresses")," "),e.xp6(3),e.Q6J("ngForOf",t.address),e.xp6(2),e.Q6J("label",e.lcZ(13,8,"addingAddress.addNewAddress")),e.xp6(2),e.Q6J("addresses",1)("showDialog",t.displayModal)}}function Ft(o,s){1&o&&(e.TgZ(0,"button",29),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"multipleAddress.default")," "))}function Jt(o,s){1&o&&(e.TgZ(0,"button",30),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"multipleAddress.other")," "))}function qt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"img",31),e.NdJ("click",function(){e.CHM(t);const i=e.oxw().$implicit,r=e.oxw(3);return e.KtG(r.showConfirmationModal(i.id))}),e.qZA()}}function Ht(o,s){if(1&o&&(e.TgZ(0,"span",32),e._uU(1),e.qZA()),2&o){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.addressLabel," ")}}function zt(o,s){if(1&o&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o){const t=e.oxw().index;e.xp6(1),e.AsE("",e.lcZ(2,2,"multipleAddress.address")," ",t+1,"")}}function Qt(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div")(2,"div",14)(3,"input",47),e.NdJ("change",function(){const r=e.CHM(t).$implicit,a=e.oxw(3);return e.KtG(a.changeDefaultAddress(r))})("ngModelChange",function(i){e.CHM(t);const r=e.oxw(3);return e.KtG(r.selectedAddress=i)}),e.qZA(),e.TgZ(4,"a",48)(5,"div",16)(6,"div",17)(7,"div",18)(8,"div",19),e.YNc(9,Ft,3,3,"button",20),e.YNc(10,Jt,3,3,"button",21),e.TgZ(11,"div",22)(12,"img",23),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(3);return e.KtG(i.triggerAddressAnalytics("CLICK_ON_EDIT_ADDRESS","EDIT_ADDRESS"))}),e.qZA(),e.YNc(13,qt,1,0,"img",49),e.qZA()()(),e.TgZ(14,"div",25),e.YNc(15,Ht,2,1,"span",26),e.YNc(16,zt,3,4,"span",27),e.qZA(),e.TgZ(17,"span",28),e._uU(18),e.qZA()()()()()(),e.BQk()}if(2&o){const t=s.$implicit,n=e.oxw(3);e.xp6(3),e.Q6J("ngModel",n.selectedAddress)("disabled",1===n.address.length)("value",t),e.xp6(6),e.Q6J("ngIf",t.isDefault),e.xp6(1),e.Q6J("ngIf",!t.isDefault&&"Home"!==t.addressLabel&&"Work"!==t.addressLabel),e.xp6(2),e.Q6J("routerLink","/account/address/"+t.id),e.xp6(1),e.Q6J("ngIf",!t.isDefault),e.xp6(2),e.Q6J("ngIf",null==t?null:t.addressLabel),e.xp6(1),e.Q6J("ngIf",!(null!=t&&t.addressLabel)),e.xp6(1),e.s9C("title",t.streetAddress),e.xp6(1),e.Oqu(t.streetAddress)}}function Yt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div")(1,"div",40)(2,"div",41)(3,"div",42)(4,"div",43),e._uU(5),e.ALo(6,"translate"),e.qZA()()(),e.TgZ(7,"div",44),e.YNc(8,Qt,19,11,"ng-container",9),e.qZA(),e.TgZ(9,"div",45)(10,"button",46),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.addAddress())}),e.ALo(11,"translate"),e.qZA()(),e.TgZ(12,"app-confirmation-delete-dialog",12),e.NdJ("update",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.onDelete(i))}),e.qZA()()()}if(2&o){const t=e.oxw(2);e.xp6(5),e.hij(" ",e.lcZ(6,5,"multipleAddress.myAddresses")," "),e.xp6(3),e.Q6J("ngForOf",t.address),e.xp6(2),e.Q6J("label",e.lcZ(11,7,"addingAddress.addAddress")),e.xp6(2),e.Q6J("addresses",1)("showDialog",t.displayModal)}}function Gt(o,s){if(1&o&&(e.TgZ(0,"span",32),e._uU(1),e.qZA()),2&o){const t=e.oxw().$implicit;e.xp6(1),e.Oqu(null==t?null:t.addressLabel)}}function Bt(o,s){if(1&o&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o){const t=e.oxw().index;e.xp6(1),e.AsE("",e.lcZ(2,2,"multipleAddress.address")," ",t+1,"")}}function Rt(o,s){1&o&&(e.TgZ(0,"button",61),e._uU(1,"Default"),e.qZA())}function Vt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"em",62),e.NdJ("click",function(){e.CHM(t);const i=e.oxw().$implicit,r=e.oxw(3);return e.KtG(r.showConfirmationModal(i.id))}),e.qZA()}}function Kt(o,s){if(1&o&&(e.ynx(0),e.TgZ(1,"div",51)(2,"div")(3,"a",52)(4,"div",53),e._UZ(5,"img",54),e.TgZ(6,"div",55)(7,"div",25),e.YNc(8,Gt,2,1,"span",26),e.YNc(9,Bt,3,4,"span",27),e.YNc(10,Rt,2,0,"button",56),e.qZA(),e.TgZ(11,"span",28),e._uU(12),e.qZA()()(),e.TgZ(13,"a",57),e._UZ(14,"em",58),e.qZA()()(),e.TgZ(15,"div",59),e.YNc(16,Vt,1,0,"em",60),e.qZA()(),e.BQk()),2&o){const t=s.$implicit;e.xp6(3),e.Q6J("routerLink","/account/address/"+t.id),e.xp6(5),e.Q6J("ngIf",null==t?null:t.addressLabel),e.xp6(1),e.Q6J("ngIf",!(null!=t&&t.addressLabel)),e.xp6(1),e.Q6J("ngIf",t.isDefault),e.xp6(1),e.s9C("title",t.streetAddress),e.xp6(1),e.Oqu(t.streetAddress),e.xp6(1),e.Q6J("routerLink","/account/address/"+t.id),e.xp6(3),e.Q6J("ngIf",!t.isDefault)}}function jt(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div")(1,"div",40)(2,"div",41)(3,"div",42)(4,"div",43),e._uU(5),e.ALo(6,"translate"),e.qZA()()(),e.TgZ(7,"div",50),e.YNc(8,Kt,17,8,"ng-container",9),e.TgZ(9,"div",45)(10,"button",46),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.addAddress())}),e.ALo(11,"translate"),e.qZA()(),e.TgZ(12,"app-confirmation-delete-dialog",12),e.NdJ("update",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.onDelete(i))}),e.qZA()()()()}if(2&o){const t=e.oxw(2);e.xp6(5),e.hij(" ",e.lcZ(6,5,"multipleAddress.myAddresses")," "),e.xp6(3),e.Q6J("ngForOf",t.address),e.xp6(2),e.Q6J("label",e.lcZ(11,7,"addingAddress.addAddress")),e.xp6(2),e.Q6J("addresses",1)("showDialog",t.displayModal)}}const Xt=function(o){return{"hidden-navbar":o}};function $t(o,s){if(1&o&&(e.TgZ(0,"section",34)(1,"div",35),e._UZ(2,"em",36)(3,"em",37),e.TgZ(4,"span",38),e._uU(5),e.ALo(6,"translate"),e.qZA(),e._UZ(7,"em",37),e.TgZ(8,"span",38),e._uU(9),e.ALo(10,"translate"),e.qZA()(),e.YNc(11,Yt,13,9,"div",39),e.YNc(12,jt,13,9,"div",39),e.qZA()),2&o){const t=e.oxw();e.Q6J("ngClass",e.VKq(12,Xt,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(2),e.Q6J("routerLink","/"),e.xp6(2),e.Q6J("routerLink","/account"),e.xp6(1),e.Oqu(e.lcZ(6,8,"sideMenu.yourAccount")),e.xp6(3),e.Q6J("routerLink","/account/address"),e.xp6(1),e.Oqu(e.lcZ(10,10,"multipleAddress.myAddresses")),e.xp6(2),e.Q6J("ngIf",t.isLayoutTemplate),e.xp6(1),e.Q6J("ngIf",!t.isLayoutTemplate)}}let Wt=(()=>{class o{addressService;translate;platformId;messageService;router;route;appDataService;permissionService;$gaService;$gtmService;store;address=[];displayModal=!1;selectedId="";redirectUrl;selectedAddress;isMobileTemplate=!1;navbarData;isLayoutTemplate=!1;tagName=I.Ir;tagNameLocal=D.s;userDetails;isGoogleAnalytics=!1;screenWidth=window.innerWidth;onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_,f){this.addressService=t,this.translate=n,this.platformId=i,this.messageService=r,this.router=a,this.route=c,this.appDataService=d,this.permissionService=p,this.$gaService=h,this.$gtmService=_,this.store=f,this.isLayoutTemplate=this.permissionService.hasPermission("Layout-Template"),this.isMobileTemplate=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics")}ngOnInit(){this.navbarData=this.appDataService.layoutTemplate.find(t=>"navbar"===t.type),this.$gtmService.pushPageView("account","addresses"),this.route.queryParams.subscribe(t=>{this.redirectUrl=t.returnUrl}),this.getCustomerAddress()}triggerAddressAnalytics(t,n){this.isGoogleAnalytics&&this.$gaService.event(this.tagNameLocal[t],"",n,1,!0)}getCustomerAddress(){this.addressService.getAddress().subscribe({next:t=>{this.userDetails=this.store.get("profile"),this.triggerAnalytics(),this.address=t.data.records,this.selectedAddress=this.address[0]}})}onDelete(t){this.displayModal=!1,"delete"==t&&(this.triggerAddressAnalytics("CLICK_ON_DELETE_ADDRESS","DELETE_ADDRESS"),this.addressService.deleteAddress(this.selectedId).subscribe({next:n=>{this.messageService.add({severity:"success",summary:"",detail:"Address deleted successfully"}),this.getCustomerAddress()}}))}showConfirmationModal(t){this.selectedId=t,this.displayModal=!0}addAddressMobile(){this.triggerAddressAnalytics("CLICK_ON_ADD_ADDRESS","ADD_ADDRESS"),this.router.navigate(["/account/verify-address"])}addAddress(){this.triggerAddressAnalytics("CLICK_ON_ADD_ADDRESS","ADD_ADDRESS"),this.redirectUrl&&""!==this.redirectUrl?(this.router.navigate(["/account/address/add-address"],{queryParams:{returnUrl:this.redirectUrl}}),this.redirectUrl=""):this.router.navigate(["/account/address/add-address"])}addressDetails(t){this.router.navigate([`/account/address/${t}`])}changeDefaultAddress(t){this.addressService.setDefault(t.id).subscribe({next:n=>{this.getCustomerAddress()},error:n=>{console.error(n)}})}triggerAnalytics(){this.isGoogleAnalytics&&this.permissionService.getTagFeature("VIEW_ITEM_LIST")&&(this.$gaService.pageView("/account/address","All Addresses"),this.$gaService.event(this.tagName.SEARCH,"","VIEW_ITEM_LIST",1,!0,{user_ID:this.userDetails?this.userDetails.mobileNumber:"Un_Authenticated"}))}static \u0275fac=function(n){return new(n||o)(e.Y36(g.DM),e.Y36(b.sK),e.Y36(e.Lbi),e.Y36(T.ez),e.Y36(A.F0),e.Y36(A.gz),e.Y36(g.UW),e.Y36(g.$A),e.Y36(I.$r),e.Y36(U.J),e.Y36(g.d6))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-address-list"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["oldContainer",""],[1,"account-page-mobile"],[1,"d-flex","cart-mobile-new__address-layout","flex-row"],[1,"d-inline-flex","cart-mobile-new__address-layout__address-items-section"],[1,"header-container"],["alt","back-icon","src","assets/icons/mobile-icons/back-icon.svg",3,"routerLink"],[1,"header-container__header-detail"],[1,"mt-5","flex","flex-column","justify-content-start","flex-wrap","account-links","mobile-address","mobile-addressbar"],[4,"ngFor","ngForOf"],[1,"mt-3","add-new-address"],["pButton","","type","button",1,"w-full","add-new-address-btn",3,"label","click"],[3,"addresses","showDialog","update"],[1,"address-bg"],[1,"radio-button-container"],[1,"justify-content-between","px-3","mb-2","no-underline","text-black-alpha-90","border-round","align-items-center"],[1,"align-items-center"],[1,"address-item-list"],[1,"address-container"],[1,"address-actions"],["class","default-btn",4,"ngIf"],["class","other-btn",4,"ngIf"],[1,"icons-container"],["alt","No Image","src","assets/icons/edit-address.svg",1,"edit-icon",3,"routerLink","click"],["alt","No Image","class","delete-icon","src","assets/icons/delete-address.svg",3,"click",4,"ngIf"],[1,"d-flex","justify-content-between"],["class","address-tag","style","font-weight: bolder; text-transform: capitalize",4,"ngIf"],["class","address-tag","style","font-weight: bolder",4,"ngIf"],[1,"street-address",3,"title"],[1,"default-btn"],[1,"other-btn"],["alt","No Image","src","assets/icons/delete-address.svg",1,"delete-icon",3,"click"],[1,"address-tag",2,"font-weight","bolder","text-transform","capitalize"],[1,"address-tag",2,"font-weight","bolder"],[1,"account-page",3,"ngClass"],[1,"breadcrumb-address","d-flex"],["aria-hidden","true",1,"pi","pi-home","cursor-pointer",3,"routerLink"],["aria-hidden","true",1,"pi","pi-angle-left"],[3,"routerLink"],[4,"ngIf"],[1,"content-container","mobile-top",2,"margin-top","40px"],[1,"grid"],[1,"col-12","col-md-6","flex","md:justify-content-start"],[1,"font-size-28","bold-font","your-addres"],[1,"mt-3","flex","flex-column","justify-content-start","flex-wrap","account-links","mobile-address","mobile-addressbar"],[1,"mt-3","button-address"],["pButton","","type","button",1,"w-full","confirmBtn",3,"label","click"],["type","radio",1,"radio-default",3,"ngModel","disabled","value","change","ngModelChange"],[1,"justify-content-between","p-3","mb-2","no-underline","text-black-alpha-90","border-round","align-items-center","surface-100"],["alt","No Image","src","assets/icons/delete-address.svg","class","delete-icon",3,"click",4,"ngIf"],[1,"mt-5","flex","flex-column","justify-content-start","flex-wrap","account-links","mobile-address"],[1,"d-inline-flex"],[1,"flex","justify-content-between","py-3","px-3","surface-100","mb-2","no-underline","text-black-alpha-90","border-round","align-items-center",3,"routerLink"],[1,"align-items-center","d-flex"],["alt","No Image","src","assets/icons/pin.svg",1,"img"],[1,"address-item"],["class","default",4,"ngIf"],[1,"d-flex","my-auto",3,"routerLink"],[1,"pi","pi-angle-right",2,"cursor","pointer"],[1,"surface-100",2,"align-items","center","display","flex","height","64px","width","22px"],["class","fas fa-trash delete-color cursor-pointer",3,"click",4,"ngIf"],[1,"default"],[1,"fas","fa-trash","delete-color","cursor-pointer",3,"click"]],template:function(n,i){if(1&n&&(e.YNc(0,Et,15,10,"ng-container",0),e.YNc(1,$t,13,14,"ng-template",null,1,e.W1O)),2&n){const r=e.MAs(2);e.Q6J("ngIf",i.isMobileTemplate&&i.screenWidth<=768)("ngIfElse",r)}},dependencies:[l.Fj,l._,l.JJ,m.mk,m.sg,m.O5,S.Hq,A.rH,l.On,x.mh,K.$,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.account-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{max-width:366px;width:100%}.account-page[_ngcontent-%COMP%]{margin-bottom:250px!important}@media screen and (max-width: 768px){.account-page[_ngcontent-%COMP%]{margin-top:200px!important}.mobile-address[_ngcontent-%COMP%]{margin-top:10px!important}.your-addres[_ngcontent-%COMP%]{font-size:20px!important}.street-address[_ngcontent-%COMP%]{font-size:11px;font-weight:500;color:#323232;font-family:var(--medium-font)!important;overflow:hidden;text-overflow:ellipsis;-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box}.mobile-top[_ngcontent-%COMP%]{margin-top:20px!important}.radio-default[_ngcontent-%COMP%]{left:8px!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:150px!important}}.pi[_ngcontent-%COMP%]{color:#000}.img[_ngcontent-%COMP%]{margin-left:-5px;margin-right:10px}.delete-color[_ngcontent-%COMP%]{color:var(--main_bt_txtcolor);font-size:16px}.pi-angle-right[_ngcontent-%COMP%]{color:var(--main_bt_txtcolor)}.address-tag[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-size:16px;font-weight:500!important}.street-address[_ngcontent-%COMP%]{font-size:11px;font-family:var(--medium-font)!important;font-weight:500!important}.address-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:266px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.button-address[_ngcontent-%COMP%]{font-size:15px;max-width:355px}.button-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%]{color:#fff;background-color:var(--header_bgcolor);padding:10px 20px;border-radius:25px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none;margin-bottom:20px;text-transform:uppercase}.default[_ngcontent-%COMP%]{width:73px;height:19px;background:#FFCB05 0% 0% no-repeat padding-box;border-radius:50px;border:none;letter-spacing:-.15px;color:#323232;font-size:11px;font-family:var(--medium-font)!important}.breadcrumb-address[_ngcontent-%COMP%]{background-color:#efeded;padding:1rem 2rem}.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{padding:0 6px;margin:auto 0}.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px}.radio-default[_ngcontent-%COMP%]{align-self:center;display:inline-flex;margin-right:10px}.radio-button-container[_ngcontent-%COMP%]{display:flex}.account-page-mobile[_ngcontent-%COMP%]{margin-top:75px!important}.cart-mobile-new__address-layout__address-items-section[_ngcontent-%COMP%]{max-width:100%;width:100%;border:1px solid #E4E7E9;border-radius:4px;flex-direction:column;background:#F6F6F6;margin-bottom:69px}.add-new-address[_ngcontent-%COMP%]{padding:16px}.add-new-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%]{color:#fff;background-color:var(--header_bgcolor);padding:10px 20px;border-radius:25px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none;margin-bottom:20px;text-transform:uppercase}.address-bg[_ngcontent-%COMP%]{background:#fff;border-radius:16px;border-bottom:1px solid #E4E7E9;padding:16px 8px 16px 16px;margin-bottom:10px}.mobile-addressbar[_ngcontent-%COMP%]{padding:0 13px 13px}.address-item-list[_ngcontent-%COMP%]{display:flex;flex-direction:column}.header-container[_ngcontent-%COMP%]{padding:16px;font-size:16px;font-weight:500;color:#2d2d2d}.address-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.address-actions[_ngcontent-%COMP%]{display:flex;width:100%}.default-btn[_ngcontent-%COMP%]{border-radius:4px;background:#DCE6FD;color:#022c61;border:none;font-weight:400;font-size:10px;font-family:var(--medium-font)!important}.icons-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;gap:16px;margin-left:auto}.icons-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:12px;height:12px}.edit-icon[_ngcontent-%COMP%], .delete-icon[_ngcontent-%COMP%]{margin-left:10px}.right-section[_ngcontent-%COMP%]{display:flex;align-items:center}.right-section[_ngcontent-%COMP%]   .default[_ngcontent-%COMP%]{margin-right:10px}.add-new-address-btn[_ngcontent-%COMP%]{background-color:#fff;color:var(--header_bgcolor);padding:10px 20px;border-radius:6px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none;margin-bottom:20px}.add-new-address-btn[_ngcontent-%COMP%]:active{border:1px solid var(--header_bgcolor)!important;background-color:#fff!important}.other-btn[_ngcontent-%COMP%]{color:#856600;font-weight:400;font-size:10px;border-radius:4px;background:#FFE992;border:none;font-family:var(--medium-font)!important;width:50px}"]})}return o})();var en=u(4825),tn=u(9949);function nn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"button",18),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.resendOtp())}),e._uU(1),e.ALo(2,"translate"),e.qZA()}if(2&o){const t=e.oxw(2);e.Q6J("disabled","00"!==t.timeLeft),e.xp6(1),e.hij(" ",e.lcZ(2,2,"auth.otp.resend")," ")}}function on(o,s){if(1&o&&(e.TgZ(0,"span",19),e._uU(1),e.ALo(2,"translate"),e.TgZ(3,"b"),e._uU(4),e.qZA()()),2&o){const t=e.oxw(2);e.xp6(1),e.hij("",e.lcZ(2,2,"auth.otp.resendIn")," "),e.xp6(3),e.hij("0:",t.timeLeft,"")}}const rn=function(o){return{marginTop:o}},k=function(){return{standalone:!0}};function sn(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",4)(2,"div",5)(3,"p",6),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",7),e._uU(7),e.ALo(8,"translate"),e.qZA(),e.TgZ(9,"div",8)(10,"div",9)(11,"div",10)(12,"form",11)(13,"input",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value1=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(14,"input",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value2=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(15,"input",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value3=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(16,"input",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value4=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA()()(),e.TgZ(17,"div",13),e.YNc(18,nn,3,4,"button",14),e.YNc(19,on,5,4,"span",15),e.qZA(),e.TgZ(20,"button",16),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.validateOtp())}),e._uU(21),e.ALo(22,"translate"),e.qZA(),e.TgZ(23,"button",17),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBack())}),e._uU(24),e.ALo(25,"translate"),e.qZA()()()()(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngStyle",e.VKq(24,rn,t.screenWidth<=768?t.isMobileLayout?"5rem":"220px":"")),e.xp6(3),e.hij(" ",e.lcZ(5,16,"otp.enterOTP")," "),e.xp6(3),e.hij(" ",e.lcZ(8,18,"otp.verificationCodeSent")," "),e.xp6(6),e.Q6J("ngModel",t.value1)("ngModelOptions",e.DdM(26,k)),e.xp6(1),e.Q6J("ngModel",t.value2)("ngModelOptions",e.DdM(27,k)),e.xp6(1),e.Q6J("ngModel",t.value3)("ngModelOptions",e.DdM(28,k)),e.xp6(1),e.Q6J("ngModel",t.value4)("ngModelOptions",e.DdM(29,k)),e.xp6(2),e.Q6J("ngIf",0==t.timeLeft),e.xp6(1),e.Q6J("ngIf",t.timeLeft>0),e.xp6(1),e.Q6J("disabled",!(t.value1&&t.value2&&t.value3&&t.value4)),e.xp6(1),e.hij(" ",e.lcZ(22,20,"auth.otp.next")," "),e.xp6(3),e.hij(" ",e.lcZ(25,22,"newUser.verifyUser.cancel")," ")}}function an(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"button",18),e.NdJ("click",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.resendOtp())}),e._uU(1),e.ALo(2,"translate"),e.qZA()}if(2&o){const t=e.oxw(2);e.Q6J("disabled","00"!==t.timeLeft),e.xp6(1),e.hij(" ",e.lcZ(2,2,"auth.otp.resend")," ")}}function ln(o,s){if(1&o&&(e.TgZ(0,"span",19),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o){const t=e.oxw(2);e.xp6(1),e.AsE("",e.lcZ(2,2,"auth.otp.resendIn")," 0:",t.timeLeft,"")}}function dn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",20)(1,"div",21)(2,"p",22),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",23),e._uU(6),e.ALo(7,"translate"),e.qZA(),e.TgZ(8,"div",24)(9,"div",9)(10,"div",25)(11,"form",11)(12,"input",26),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value1=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(13,"input",26),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value2=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(14,"input",26),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value3=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA(),e.TgZ(15,"input",26),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.value4=i)})("keyup",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onDigitInput(i))}),e.qZA()()(),e.TgZ(16,"div",13),e.YNc(17,an,3,4,"button",14),e.YNc(18,ln,3,4,"span",15),e.qZA(),e.TgZ(19,"button",27),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.validateOtp())}),e.ALo(20,"translate"),e.qZA()()()()()}if(2&o){const t=e.oxw();e.xp6(3),e.hij(" ",e.lcZ(4,14,"otp.enterOTP")," "),e.xp6(3),e.hij(" ",e.lcZ(7,16,"otp.verificationCodeSent")," "),e.xp6(6),e.Q6J("ngModel",t.value1)("ngModelOptions",e.DdM(20,k)),e.xp6(1),e.Q6J("ngModel",t.value2)("ngModelOptions",e.DdM(21,k)),e.xp6(1),e.Q6J("ngModel",t.value3)("ngModelOptions",e.DdM(22,k)),e.xp6(1),e.Q6J("ngModel",t.value4)("ngModelOptions",e.DdM(23,k)),e.xp6(2),e.Q6J("ngIf",0==t.timeLeft),e.xp6(1),e.Q6J("ngIf",t.timeLeft>0),e.xp6(1),e.Q6J("label",e.lcZ(20,18,"auth.otp.next"))("disabled",!(t.value1&&t.value2&&t.value3&&t.value4))}}function cn(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"app-mtn-message-modal",28),e.NdJ("submit",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.onSubmit(i))}),e.qZA(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("displayModal",t.displayModal)("message",t.message)("type",t.isPrimary||t.otpService.toggleBoolean?"primary":"secondary")}}let pn=(()=>{class o{authTokenService;cookieService;mainDataService;messageService;auth;cd;loaderService;router;otpService;translate;store;platformId;user;permissionService;displayModal=!1;message="";mobileNumber="";size=50;value1;value2;value3;value4;otpCode="";countDown;counter=60;timeLeft=60;tick=1e3;interval;isPrimary=!1;decoded;cookieValue;screenWidth=window.innerWidth;isMobileLayout=!1;onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_,f,y,C,v){this.authTokenService=t,this.cookieService=n,this.mainDataService=i,this.messageService=r,this.auth=a,this.cd=c,this.loaderService=d,this.router=p,this.otpService=h,this.translate=_,this.store=f,this.platformId=y,this.user=C,this.permissionService=v}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.countDown=(0,en.H)(0,this.tick).subscribe(()=>--this.counter),this.startTimer()}ngOnDestroy(){this.countDown=null}onDigitInput(t){let n;"Backspace"!==t.code&&"Tab"!==t.code&&(n=t.srcElement.nextElementSibling),"Backspace"===t.code&&(n=t.srcElement.previousElementSibling),n?.focus()}validateOtp(){this.store.set("loading",!0),this.isOtpComplete()?this.handleOtpCompletion():this.AddMessage("ErrorMessages.fetchError","ErrorMessages.mobileRequired")}isOtpComplete(){return null!=this.value1&&null!=this.value2&&null!=this.value3&&null!=this.value4}handleOtpCompletion(){if(this.otpCode=this.concatenateOtpValues(),history?.state?.password)this.login();else{const t=this.createOtpData();this.makeApiCall(t)}}concatenateOtpValues(){return this.value1.toString()+this.value2.toString()+this.value3.toString()+this.value4.toString()}createOtpData(){const t=localStorage.getItem("primaryPhone"),n=localStorage.getItem("isPrimary")??"false";return"true"===n?{phoneNumber:t,userId:localStorage.getItem("userId"),isPrimary:"true",otpCode:this.otpCode}:{phoneNumber:t,isPrimary:n,otpCode:this.otpCode}}makeApiCall(t){this.auth.addPrimaryOrSecondaryPhone(t).subscribe({next:n=>{this.handleApiSuccess(n)},error:n=>{this.handleApiError()}})}handleApiSuccess(t){this.store.set("loading",!1),t?.success?(localStorage.setItem("secondaryDefault","false"),this.displayModal=!0,this.isPrimary="true"===localStorage.getItem("isPrimary"),this.message=this.translate.instant(this.isPrimary?"ResponseMessages.newPrimarySuccessMessage":"ResponseMessages.newSecondarySuccessMessage")):this.handleApiFailure()}handleApiError(){this.store.set("loading",!1),this.AddMessage("ErrorMessages.fetchError","ErrorMessages.mobileRequired")}handleApiFailure(){this.messageService.add({severity:"error",detail:this.translate.instant("ErrorMessages.invalidOtp")})}AddMessage(t,n){this.messageService.add({severity:"error",summary:this.translate.instant(t),detail:this.translate.instant(n)})}onSubmit(t){t&&(this.isPrimary?this.logout():this.router.navigate(["/account/details"]),this.displayModal=!1,this.message="")}logout(){sessionStorage.clear(),this.authTokenService.authTokenSet(""),this.cookieService.delete("authToken","/"),this.store.set("profile",""),this.mainDataService.setCartLenghtData(null),this.mainDataService.setUserData(null),localStorage.setItem("secondaryDefault","false"),localStorage.setItem("sessionId",""),localStorage.setItem("addedProducts",""),localStorage.setItem("cartId",""),this.store.set("cartProducts",[]),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth_enc"),this.router.navigate(["/login"])}login(){this.store.set("loading",!0),this.auth.login({username:history.state.Username,password:history.state.password,RequestId:history.state.verificationCode,otp:this.otpCode}).subscribe({next:t=>{t?.success?(this.mainDataService.setUserData(t.data),this.store.set("profile",t.data),this.store.set("userPhone",t.data.mobileNumber),this.store.set("timeInterval",(new Date).getTime()),this.store.set("refreshToken",t.data.refreshToken.replace("bearer","")),this.store.set("loading",!1),t.data.isPasswodExpired?(this.router.navigateByUrl("/change-password"),this.messageService.add({severity:"info",summary:this.translate.instant("ResponseMessages.changePassword"),detail:this.translate.instant("ResponseMessages.passwordExpirationChange")})):(this.router.navigate(["/"]),this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.login"),detail:this.translate.instant("ResponseMessages.loggedInSuccessfully")}))):(this.store.set("profile",""),this.store.set("loading",!1),this.messageService.add({severity:"error",summary:t?.Message??this.translate.instant("ErrorMessages.invalidUserNameOrPassword")}),localStorage.removeItem("isPrimary"))},error:t=>{this.store.set("profile",""),this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}})}resendOtp(){this.store.set("loading",!0),this.otpService.checkMobileNumber({UserName:localStorage.getItem("primaryPhone")??this.otpService.username,CountryId:"1448983B-0C38-450A-BD71-9204D181B925"}).subscribe({next:t=>{this.loaderService.hide(),this.startTimer()},error:t=>{this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}})}startTimer(){this.interval=setInterval(()=>{this.timeLeft>0?this.timeLeft--:this.timeLeft=60,0===this.timeLeft&&clearInterval(this.interval),this.timeLeft<10&&(this.timeLeft="0"+this.timeLeft),this.cd.detectChanges()},1e3)}onBack(){this.router.navigate(["account/verify-mobile"])}static \u0275fac=function(n){return new(n||o)(e.Y36(g.Lz),e.Y36(J.N),e.Y36(g.iI),e.Y36(T.ez),e.Y36(g.e8),e.Y36(e.sBO),e.Y36(g.D1),e.Y36(A.F0),e.Y36(g.aO),e.Y36(b.sK),e.Y36(g.d6),e.Y36(e.Lbi),e.Y36(g.KD),e.Y36(g.$A))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-verify-otp"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:5,vars:3,consts:[[1,"otp"],[4,"ngIf","ngIfElse"],["desktopView",""],[4,"ngIf"],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center"],[1,"col-12","text-center","content-container__header-title"],["href","/",1,"col-12","text-center","content-container__header-sub-title"],[1,"col-12","col-md-8","col-lg-6"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12","mb-4"],["action","",1,"mt-5"],["type","text","maxlength","1","placeholder","-","oninput","this.value=this.value.replace(/[^0-9]/g,'');",3,"ngModel","ngModelOptions","ngModelChange","keyup"],[1,"count"],["style","background: white; border: none; cursor: pointer","class","resend",3,"disabled","click",4,"ngIf"],["class","time-left",4,"ngIf"],["pButton","","type","button",1,"w-full","second-btn","content-container__primary-btn","mt-4","gap-2",3,"disabled","click"],["pButton","","type","button",1,"w-full","second-btn","content-container__secondary-btn","mt-3","gap-2",3,"click"],[1,"resend",2,"background","white","border","none","cursor","pointer",3,"disabled","click"],[1,"time-left"],[1,"content-container","my-3"],[1,"grid","justify-content-center","margin-x-100"],[1,"col-12","bold-font","text-center","font-size-28","m-0","py-0","mx-4","mb-2"],["href","/",1,"col-12","text-center","no-underline","font-size-14","pt-0","m-0","mb-3","otp-des"],[1,"col-12","col-md-8","col-lg-6","border-round","bg-white","shadow-1","pt-6"],[1,"p-field","p-col-12","mb-8"],["type","text","maxlength","1","oninput","this.value=this.value.replace(/[^0-9]/g,'');",3,"ngModel","ngModelOptions","ngModelChange","keyup"],["pButton","","type","button","pButton","","type","button",1,"p-field","p-col-12","mb-5","mt-3","width-100","second-btn",3,"label","disabled","click"],[3,"displayModal","message","type","submit"]],template:function(n,i){if(1&n&&(e.TgZ(0,"section",0),e.YNc(1,sn,26,30,"ng-container",1),e.YNc(2,dn,21,24,"ng-template",null,2,e.W1O),e.qZA(),e.YNc(4,cn,2,3,"ng-container",3)),2&n){const r=e.MAs(3);e.xp6(1),e.Q6J("ngIf",i.screenWidth<768)("ngIfElse",r),e.xp6(3),e.Q6J("ngIf",i.displayModal)}},dependencies:[l._Y,l.Fj,l.JJ,l.JL,l.nD,m.O5,m.PC,S.Hq,l.On,l.F,tn.e,x.mh,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}form[_ngcontent-%COMP%]{text-align:center}form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{display:inline-block;width:60px;height:60px;text-align:center;margin:0 .8rem;background:#F5F5F5!important;border-bottom:1px solid #AEAEAE!important;font-size:20px;font-weight:500;font-family:var(--medium-font)!important}.count[_ngcontent-%COMP%]{text-align:center;font-size:small}.otp[_ngcontent-%COMP%]{margin-top:90px}@media screen and (max-width: 768px){  .main-content{min-height:auto!important}}  .p-dialog .p-dialog-footer{text-align:center}  .p-dialog .p-dialog-header{padding:.5rem}  .p-dialog .p-dialog-footer button{width:100%}  .model img{width:50px;height:50px;margin-bottom:30px;margin-top:70px}.time-left[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-size:13px;font-weight:400;color:#a3a3a3}.otp-des[_ngcontent-%COMP%]{font-family:var(--regular-font)!important}.resend[_ngcontent-%COMP%]{font-size:14px;font-weight:800;font-family:var(--medium-font)!important}@media only screen and (max-width: 767px){.otp[_ngcontent-%COMP%]{margin-top:122px!important}.content-container__header-title[_ngcontent-%COMP%]{color:#000;font-family:main-medium;font-size:20px;font-style:normal;font-weight:700;line-height:normal;margin-bottom:0!important}.content-container__header-sub-title[_ngcontent-%COMP%]{color:#6e6e6e;font-family:main-medium;font-size:14px;font-style:normal;font-weight:400;line-height:normal}.content-container[_ngcontent-%COMP%]   .time-left[_ngcontent-%COMP%]{color:#a3a3a3;text-align:center;font-family:main-regular;font-size:13px;font-style:normal;font-weight:400;line-height:normal}.content-container__primary-btn[_ngcontent-%COMP%]{background:var(--primary, #204E6E);border-radius:8px!important;color:var(--Gray-00, #FFF);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;text-transform:uppercase;place-content:center;padding:12px 24px;height:48px}.content-container__secondary-btn[_ngcontent-%COMP%]{background:transparent!important;border-radius:8px!important;color:var(--primary, #204E6E);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;text-transform:uppercase;place-content:center;padding:12px 24px;height:48px}form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin:0 .4rem!important;border-radius:8px!important;border-bottom:none!important}}"]})}return o})();function gn(o,s){if(1&o&&(e.TgZ(0,"span",7),e._uU(1),e.qZA()),2&o){const t=e.oxw(2);e.Q6J("ngStyle",t.labelLeftStyle),e.xp6(1),e.hij(" ",t.labelChecked," ")}}function mn(o,s){if(1&o&&(e.TgZ(0,"span",8),e._uU(1),e.qZA()),2&o){const t=e.oxw(2);e.Q6J("ngStyle",t.labelRightStyle),e.xp6(1),e.hij(" ",t.labelUnchecked," ")}}function un(o,s){if(1&o&&(e.ynx(0),e.YNc(1,gn,2,2,"span",5),e.YNc(2,mn,2,2,"span",6),e.BQk()),2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",t.toggled),e.xp6(1),e.Q6J("ngIf",!t.toggled)}}const hn=function(o){return{"ng-toggle-focused":o}};let j=(()=>{class o{}return o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=e.Yz7({token:o,factory:o.\u0275fac}),o})();const ge="#0099CC",L="#fff";let vn=0,yn=(()=>{class o{constructor(t,n){this.config=t,this._elementRef=n,this.value=this.config.value||!0,this.name=this.config.name||"",this.disabled=this.config.disabled||!1,this.height=this.config.height||25,this.width=this.config.width||45,this.margin=this.config.margin||2,this.fontSize=this.config.fontSize||void 0,this.speed=this.config.speed||300,this.color=this.config.color,this.switchColor=this.config.switchColor,this.labels=this.config.labels||!0,this.fontColor=this.config.fontColor||void 0,this.values=this.config.values||{checked:!0,unchecked:!1},this.textAlign=this.config.textAlign||{checked:"left",unchecked:"right"},this.id="",this.ariaLabel=null,this.ariaLabelledby=null,this.cssColors=!1,this.change=new e.vpe,this.valueChange=new e.vpe,this.onChange=i=>{},this.onTouch=()=>{},this._uniqueId="ng-toggle-"+ ++vn,this.id=this.id||this._uniqueId,this.ariaLabel=this.ariaLabel||this.name||this.id}ngOnInit(){this.setToogle()}onInput(t){this.value=t,this.onTouch(),this.onChange(this.value)}writeValue(t){this.value=t,this.setToogle()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouch=t}setDisabledState(t){this.disabled=t}setToogle(){const t=this.value;let n=Object.values(this.values).findIndex(i=>i==t);n>-1&&(this.toggled="checked"==Object.keys(this.values)[n])}ngOnChanges(t){for(const n in t)"value"==n&&this.writeValue(t[n].currentValue)}get coreStyle(){return{width:Z(this.width),height:Z(this.height),transition:`all ${this.speed}ms`,backgroundColor:this.cssColors?null:this.disabled?this.colorDisabled:this.colorCurrent,borderRadius:Z(Math.round(this.height/2))}}get buttonRadius(){const t=this.height-2*this.margin;return t>0?t:0}get distance(){return Z(this.width-this.height+this.margin)}get buttonStyle(){const t=`all ${this.speed}ms`,n=Z(this.margin),i=me(this.toggled?this.distance:n,n);let r=this.switchColor?this.switchColorCurrent:null;return r=this.disabled?this.switchColorDisabled:r,{width:Z(this.buttonRadius),height:Z(this.buttonRadius),transition:t,transform:i,background:r}}get labelStyle(){return{lineHeight:Z(this.height),fontSize:this.fontSize?Z(this.fontSize):null,color:this.fontColor?this.fontColorCurrent:null,width:Z(this.width-this.buttonRadius-this.margin)}}get labelLeftStyle(){return{...this.labelStyle,textAlign:this.textAlign.checked||this.textAlign}}get labelRightStyle(){return{...this.labelStyle,textAlign:this.textAlign.unchecked||this.textAlign}}get colorChecked(){let{color:t}=this;return Q(t)?M(t,"checked",ge):t||ge}get colorUnchecked(){return M(this.color,"unchecked","#e0e0e0")}get colorDisabled(){return M(this.color,"disabled","#dbdbdb")}get colorCurrent(){return this.toggled?this.colorChecked:this.colorUnchecked}get labelChecked(){return M(this.labels,"checked","")}get labelUnchecked(){return M(this.labels,"unchecked","")}get switchColorChecked(){return M(this.switchColor,"checked",L)}get switchColorUnchecked(){return M(this.switchColor,"unchecked",L)}get switchColorDisabled(){return M(this.switchColor,"disabled","silver")}get switchColorCurrent(){return Q(this.switchColor)?this.toggled?this.switchColorChecked:this.switchColorUnchecked:this.switchColor||L}get fontColorChecked(){return M(this.fontColor,"checked",L)}get fontColorUnchecked(){return M(this.fontColor,"unchecked",L)}get fontColorDisabled(){return M(this.fontColor,"disabled",L)}get fontColorCurrent(){return Q(this.fontColor)?this.disabled?this.fontColorDisabled:this.toggled?this.fontColorChecked:this.fontColorUnchecked:this.fontColor||L}get label(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:`${this._uniqueId}-label`}toggle(t){const n=!this.toggled;this.toggled=n,this.value=this.getValue(n),this.onTouch(),this.onChange(this.value),this.valueChange.emit(this.value)}getValue(t){return!0===t?this.values.checked:this.values.unchecked}onFocus(t){!this.focused&&t.relatedTarget&&(this.focused=!0)}onFocusout(t){this._elementRef.nativeElement.contains(t.relatedTarget)||(this.focused=!1,this.onTouch())}}return o.\u0275fac=function(t){return new(t||o)(e.Y36(j),e.Y36(e.SBq))},o.\u0275cmp=e.Xpm({type:o,selectors:[["ng-toggle"]],inputs:{value:"value",name:"name",disabled:"disabled",height:"height",width:"width",margin:"margin",fontSize:"fontSize",speed:"speed",color:"color",switchColor:"switchColor",labels:"labels",fontColor:"fontColor",values:"values",textAlign:"textAlign",id:"id",ariaLabel:["aria-label","ariaLabel"],ariaLabelledby:["aria-labelledby","ariaLabelledby"],ariaDescribedby:["aria-describedby","ariaDescribedby"]},outputs:{change:"change",valueChange:"valueChange"},features:[e._Bn([{provide:l.JU,useExisting:(0,e.Gpc)(()=>o),multi:!0}]),e.TTD],decls:5,vars:16,consts:[[1,"ng-toggle-switch",3,"for"],["type","checkbox","role","checkbox",1,"ng-toggle-switch-input",3,"checked","disabled","change","focusin","focusout"],[1,"ng-toggle-switch-core",3,"ngClass","ngStyle"],[1,"ng-toggle-switch-button",3,"ngStyle"],[4,"ngIf"],["class","ng-toggle-switch-label ng-toggle-left",3,"ngStyle",4,"ngIf"],["class","ng-toggle-switch-label ng-toggle-right",3,"ngStyle",4,"ngIf"],[1,"ng-toggle-switch-label","ng-toggle-left",3,"ngStyle"],[1,"ng-toggle-switch-label","ng-toggle-right",3,"ngStyle"]],template:function(t,n){1&t&&(e.TgZ(0,"label",0)(1,"input",1),e.NdJ("change",function(r){return n.toggle(r)})("focusin",function(r){return n.onFocus(r)})("focusout",function(r){return n.onFocusout(r)}),e.qZA(),e.TgZ(2,"div",2),e._UZ(3,"div",3),e.qZA(),e.YNc(4,un,3,2,"ng-container",4),e.qZA()),2&t&&(e.Q6J("for",n.id),e.uIk("id",n.label),e.xp6(1),e.Q6J("checked",n.value)("disabled",n.disabled),e.uIk("id",n.id)("name",n.name)("aria-label",n.ariaLabel)("aria-labelledby",n.label)("aria-describedby",n.ariaDescribedby)("aria-checked",n.toggled),e.xp6(1),e.Q6J("ngClass",e.VKq(14,hn,n.focused))("ngStyle",n.coreStyle),e.xp6(1),e.Q6J("ngStyle",n.buttonStyle),e.xp6(1),e.Q6J("ngIf",n.labels))},dependencies:[m.mk,m.O5,m.PC],styles:["label[_ngcontent-%COMP%]{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.ng-toggle-switch[_ngcontent-%COMP%]{display:inline-block;position:relative;vertical-align:middle;-webkit-user-select:none;user-select:none;font-size:10px;cursor:pointer}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-input[_ngcontent-%COMP%]{opacity:0;position:absolute;width:1px;height:1px}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label[_ngcontent-%COMP%]{position:absolute;top:0;font-weight:600;color:#fff;z-index:1;padding:0 10px;box-sizing:border-box}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label.ng-toggle-left[_ngcontent-%COMP%]{left:0}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label.ng-toggle-right[_ngcontent-%COMP%]{right:0}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-core[_ngcontent-%COMP%]{display:block;position:relative;box-sizing:border-box;outline:0;margin:0;transition:border-color .3s,background-color .3s;-webkit-user-select:none;user-select:none}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-core[_ngcontent-%COMP%]   .ng-toggle-switch-button[_ngcontent-%COMP%]{display:block;position:absolute;overflow:hidden;top:0;left:0;border-radius:100%;background-color:#fff;z-index:2}.ng-toggle-switch.disabled[_ngcontent-%COMP%]{pointer-events:none;opacity:.6}.ng-toggle-focused[_ngcontent-%COMP%]{box-shadow:0 0 4px 3px #999}"]}),o})();const Q=o=>"object"==typeof o,M=(o,s,t)=>((o,s)=>Q(o)&&o.hasOwnProperty(s))(o,s)?o[s]:t,Z=o=>`${o}px`,me=(o,s)=>`translate(${o}, ${s})`;let Mn=(()=>{class o{static forRoot(t={}){return{ngModule:o,providers:[{provide:j,useValue:t}]}}}return o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.oAB({type:o}),o.\u0275inj=e.cJS({providers:[j],imports:[m.ez]}),o})();const ue=function(){return{unchecked:"#00000087",checked:"rgba(25, 118, 210, 0.20)"}},he=function(o,s){return{"toggle-checked":o,"toggle-unchecked":s}};function wn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",17)(1,"ng-toggle",18),e.NdJ("change",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.onToggleChange(i))}),e.qZA(),e.TgZ(2,"span",19),e._uU(3,"Set as default"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("color",e.DdM(5,ue))("height",14)("ngClass",e.WLB(6,he,t.toggleValue,!t.toggleValue))("value",!1)("width",43)}}const Zn=function(o){return{marginTop:o}},_e=function(o){return{border:o}},fe=function(o,s){return[o,s]};function Tn(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",3)(2,"div",4)(3,"p",5),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",6),e._uU(7),e.ALo(8,"translate"),e.qZA(),e.TgZ(9,"div",7)(10,"div",8)(11,"div",9)(12,"form",10)(13,"div",11),e._UZ(14,"ngx-intl-tel-input",12),e.TgZ(15,"label",13),e._uU(16),e.ALo(17,"translate"),e.qZA()(),e.YNc(18,wn,4,9,"div",14),e.qZA()(),e.TgZ(19,"button",15),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkMobileExist())}),e._uU(20),e.ALo(21,"translate"),e.qZA(),e.TgZ(22,"button",16),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBack())}),e._uU(23),e.ALo(24,"translate"),e.qZA()()()()(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngStyle",e.VKq(33,Zn,t.screenWidth<=768?t.isMobileLayout?"5rem":"220px":"")),e.xp6(3),e.hij(" ",e.lcZ(5,23,t.title)," "),e.xp6(3),e.hij(" ",e.lcZ(8,25,"register.authenticateYourself")," "),e.xp6(5),e.Q6J("formGroup",t.verifyForm),e.xp6(1),e.Q6J("ngStyle",e.VKq(35,_e,(null==t.verifyForm.controls.phoneNumber.value||null==t.verifyForm.controls.phoneNumber.value.e164Number?null:t.verifyForm.controls.phoneNumber.value.e164Number.length)>0&&!t.verifyForm.controls.phoneNumber.valid?"1px solid red":"0px solid transparent")),e.xp6(1),e.Q6J("cssClass","custom contact-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",t.phoneInputLength)("numberFormat",t.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",t.preferredCountries)("searchCountryField",e.WLB(37,fe,t.SearchCountryField.Iso2,t.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",t.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",t.customPlaceHolder),e.xp6(2),e.hij("",e.lcZ(17,27,"contactUs.mobileNumber"),"*"),e.xp6(2),e.Q6J("ngIf",!t.isPrimary),e.xp6(1),e.Q6J("disabled",!t.verifyForm.controls.phoneNumber.valid),e.xp6(1),e.hij(" ",e.lcZ(21,29,"newUser.verifyUser.next")," "),e.xp6(3),e.hij(" ",e.lcZ(24,31,"newUser.verifyUser.cancel")," ")}}function Sn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",17)(1,"ng-toggle",18),e.NdJ("change",function(i){e.CHM(t);const r=e.oxw(2);return e.KtG(r.onToggleChange(i))}),e.qZA(),e.TgZ(2,"span",19),e._uU(3,"Set as default"),e.qZA()()}if(2&o){const t=e.oxw(2);e.xp6(1),e.Q6J("color",e.DdM(5,ue))("height",14)("ngClass",e.WLB(6,he,t.toggleValue,!t.toggleValue))("value",!1)("width",43)}}function On(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",20)(1,"div",21)(2,"p",22),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",23),e._uU(6),e.ALo(7,"translate"),e.qZA(),e.TgZ(8,"div",24)(9,"div",8)(10,"div",9)(11,"form",10)(12,"div",11),e._UZ(13,"ngx-intl-tel-input",12),e.TgZ(14,"label",13),e._uU(15),e.ALo(16,"translate"),e.qZA()(),e.YNc(17,Sn,4,9,"div",14),e.qZA()(),e.TgZ(18,"button",25),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkMobileExist())}),e.ALo(19,"translate"),e.qZA()()()()()}if(2&o){const t=e.oxw();e.xp6(3),e.hij(" ",e.lcZ(4,21,t.title)," "),e.xp6(3),e.hij(" ",e.lcZ(7,23,"register.authenticateYourself")," "),e.xp6(5),e.Q6J("formGroup",t.verifyForm),e.xp6(1),e.Q6J("ngStyle",e.VKq(29,_e,(null==t.verifyForm.controls.phoneNumber.value||null==t.verifyForm.controls.phoneNumber.value.e164Number?null:t.verifyForm.controls.phoneNumber.value.e164Number.length)>0&&!t.verifyForm.controls.phoneNumber.valid?"1px solid red":"0px solid transparent")),e.xp6(1),e.Q6J("cssClass","custom contact-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",t.phoneInputLength)("numberFormat",t.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",t.preferredCountries)("searchCountryField",e.WLB(31,fe,t.SearchCountryField.Iso2,t.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",t.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",t.customPlaceHolder),e.xp6(2),e.hij("",e.lcZ(16,25,"contactUs.mobileNumber"),"*"),e.xp6(2),e.Q6J("ngIf",!t.isPrimary),e.xp6(1),e.Q6J("disabled",!t.verifyForm.controls.phoneNumber.valid)("label",e.lcZ(19,27,"register.next"))}}let kn=(()=>{class o{otpService;platformId;translate;messageService;router;store;fb;modalService;appDataService;permissionService;toggleValue=!1;phoneNumber="";countryPhoneNumber="";countryPhoneCode="";phoneLength=12;phoneInputLength=12;CustomCountryISO;PhoneNumberFormat=x.M9;preferredCountries=[x.HT.Uganda,x.HT.Ghana,x.HT.C\u00f4teDIvoire];SearchCountryField=x.wX;verifyForm;customPlaceHolder="";isPrimary;title="";screenWidth=window.innerWidth;isMobileLayout=!1;onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_){this.otpService=t,this.platformId=n,this.translate=i,this.messageService=r,this.router=a,this.store=c,this.fb=d,this.modalService=p,this.appDataService=h,this.permissionService=_;let f=localStorage.getItem("tenantId");f&&""!==f&&("1"==f||"2"==f||"3"==f?this.customPlaceHolder="XXXXXXXXX":"4"==f&&(this.customPlaceHolder="XXXXXXXXXX"))}ngOnInit(){if(this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isPrimary=JSON.parse(localStorage.getItem("isPrimary")??""),this.title=this.translate.instant(this.isPrimary?"phoneNumber.verifyMobile.changeDefaultNumber":"phoneNumber.verifyMobile.addNew"),this.CustomCountryISO=localStorage.getItem("isoCode"),this.verifyForm=this.fb.group({phoneNumber:["",l.kI.required]}),this.appDataService.configuration){const t=this.appDataService.configuration.records.find(n=>"PhoneLength"===n.key);t&&(this.phoneInputLength=parseInt(t.value))}}checkMobileExist(){this.store.set("loading",!0),this.otpService.toggleBoolean=this.toggleValue,this.verifyForm.valid&&this.toggleValue?this.modalService.show(V.Y,{initialState:{confirmationModalDetails:{header:"Changing default number",message:"Going forward, this number will be used in:",point1:"Sign in activities",point2:"Receiving important notifications"}}}).content.submit.pipe((0,R.q)(1)).subscribe(i=>{i&&this.primarySecondaryMobileServiceFunction()}):this.verifyForm.valid&&!this.toggleValue&&(this.verifyForm.valid?this.primarySecondaryMobileServiceFunction():this.handleInvalidForm())}primarySecondaryMobileServiceFunction(){this.phoneNumber=this.verifyForm.controls.phoneNumber.value.e164Number.slice(1),this.otpService.username=this.phoneNumber,this.otpService.countryId="1448983B-0C38-450A-BD71-9204D181B925";const t=localStorage.getItem("isPrimary");t&&""!=t&&"true"===t?this.otpService.checkMobileNumber({UserName:this.phoneNumber,CountryId:"1448983B-0C38-450A-BD71-9204D181B925",UserRole:q.g8.consumer}).subscribe({next:n=>this.handleResponse(n),error:n=>this.handleError(n)}):this.otpService.checkSecondaryMobileNumber({userName:this.phoneNumber,userRole:q.g8.consumer}).subscribe({next:n=>this.handleResponse(n),error:n=>this.handleError(n)})}handleInvalidForm(){this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:this.translate.instant("ErrorMessages.mobileRequired")}),this.store.set("loading",!1)}handleResponse(t){this.store.set("loading",!1),this.verifyResponse(t)}handleError(t){this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}verifyResponse(t){t.success?t.data?.isRegistered?this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.mobileNumberAlreadyRegisteredBefore"),detail:t.message}):(this.store.set("primaryPhone",this.phoneNumber),localStorage.setItem("primaryPhone",this.phoneNumber),localStorage.setItem("secondaryDefault","true"),this.router.navigate(["/account/verify-otp"])):(this.messageService.add({severity:"error",summary:this.translate.instant("InvalidMobileNumberFormat"==t.message?"ErrorMessages.phoneNumberIsUnvalid":"ErrorMessages.fetchError"),detail:t.Message}),this.store.set("loading",!1))}omit_special_char(t){let n;return n=t.charCode,n>47&&n<58}onToggleChange(t){this.toggleValue=t.target.checked,localStorage.setItem("isPrimary",JSON.stringify(this.toggleValue))}onBack(){this.router.navigate(["account/verify-user"])}static \u0275fac=function(n){return new(n||o)(e.Y36(g.aO),e.Y36(e.Lbi),e.Y36(b.sK),e.Y36(T.ez),e.Y36(A.F0),e.Y36(g.d6),e.Y36(l.qu),e.Y36(le.tT),e.Y36(g.UW),e.Y36(g.$A))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-verify-mobile"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:4,vars:2,consts:[[1,"register"],[4,"ngIf","ngIfElse"],["desktopView",""],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center"],[1,"content-container__header-title","m-0","text-center","mb-2"],[1,"col-12","text-center","mb-3","content-container__header-sub-title",2,"color","#a3a3a3"],[1,"col-12","col-md-8","col-lg-6"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12"],["autocomplete","new-password",3,"formGroup"],[1,"p-float-label","w-full",3,"ngStyle"],["autocomplete","new-phoneNumber","formControlName","phoneNumber","name","phoneNumber",3,"cssClass","enableAutoCountrySelect","enablePlaceholder","maxLength","numberFormat","phoneValidation","preferredCountries","searchCountryField","searchCountryFlag","selectFirstCountry","selectedCountryISO","separateDialCode","customPlaceholder"],["for","mobileNumber",1,"mobile-label"],["class","mt-4",4,"ngIf"],["pButton","","type","button",1,"w-full","second-btn","content-container__primary-btn","mt-4","gap-2",3,"disabled","click"],["pButton","","type","button",1,"w-full","second-btn","content-container__secondary-btn","mt-3","gap-2",3,"click"],[1,"mt-4"],[3,"color","height","ngClass","value","width","change"],[1,"ml-4","set-default"],[1,"content-container","my-3"],[1,"grid","justify-content-center","margin-x-100"],[1,"add-new","m-0","text-center","mb-2"],[1,"col-12","text-center","no-underline","font-size-14","mb-3","otp-desc",2,"color","#a3a3a3"],[1,"col-12","col-md-8","col-lg-6","border-round","bg-white","shadow-1","px-5","pt-6"],["pButton","","type","button",1,"p-field","p-col-12","mb-5","mt-7","width-100","font-size-14","second-btn",3,"disabled","label","click"]],template:function(n,i){if(1&n&&(e.TgZ(0,"section",0),e.YNc(1,Tn,25,40,"ng-container",1),e.YNc(2,On,20,34,"ng-template",null,2,e.W1O),e.qZA()),2&n){const r=e.MAs(3);e.xp6(1),e.Q6J("ngIf",i.screenWidth<768)("ngIfElse",r)}},dependencies:[l._Y,l.JJ,l.JL,l.sg,l.u,m.mk,m.O5,m.PC,S.Hq,yn,x.FV,x.mh,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}#phone-number[_ngcontent-%COMP%]{padding:.9rem;width:100%}.add-new[_ngcontent-%COMP%]{font-size:28px;font-weight:700;font-family:var(--medium-font)!important}.mobile-label[_ngcontent-%COMP%]{position:absolute;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;color:#323232;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}@media screen and (max-width: 768px){  .main-content{min-height:auto!important}.second-btn[_ngcontent-%COMP%]{font-size:14px!important}.add-new[_ngcontent-%COMP%]{text-align:left!important}}.iti__flag-container[_ngcontent-%COMP%]{pointer-events:none!important}.otp-desc[_ngcontent-%COMP%]{font-family:var(--regular-font)!important}.margin-x-100[_ngcontent-%COMP%]{margin-top:200px!important}  .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}  .toggle-checked .ng-toggle-switch-button{width:20px!important;height:20px!important;box-shadow:0 1px 3px!important;transition:all .3s ease 0s;transform:translate(22px,-3px)!important;background-color:#204e6e!important}  .toggle-unchecked .ng-toggle-switch-button{width:20px!important;height:20px!important;box-shadow:0 1px 3px!important;transition:all .3s ease 0s;transform:translateY(-3px)!important}.set-default[_ngcontent-%COMP%]{color:#000;font-family:var(--medium-font);font-size:12px;font-style:normal;font-weight:500}@media only screen and (max-width: 767px){.register[_ngcontent-%COMP%]{margin-top:122px}.content-container[_ngcontent-%COMP%]{font-family:main-medium;font-style:normal;line-height:normal}.content-container__header-title[_ngcontent-%COMP%]{color:#000;font-size:20px;font-weight:700}.content-container__header-sub-title[_ngcontent-%COMP%]{color:#6e6e6e;font-size:14px;font-weight:400}.content-container[_ngcontent-%COMP%]   .contact-input-phone[_ngcontent-%COMP%]{border-bottom:none!important}.content-container__primary-btn[_ngcontent-%COMP%]{background:var(--primary, #204E6E);border-radius:8px!important;color:var(--Gray-00, #FFF);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;text-transform:uppercase;place-content:center;padding:12px 24px;height:48px}.content-container__secondary-btn[_ngcontent-%COMP%]{background:transparent!important;border-radius:8px!important;color:var(--primary, #204E6E);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;text-transform:uppercase;place-content:center;padding:12px 24px;height:48px}}"]})}return o})();var Pn=u(6726),Ln=u(7874),Ce=u(1423);const In=function(o){return{marginTop:o}};function Nn(o,s){if(1&o){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",3)(2,"div",4)(3,"p",5),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"div",6),e._uU(7),e.ALo(8,"translate"),e.ALo(9,"translate"),e.qZA(),e.TgZ(10,"div",7)(11,"div",8)(12,"div",9)(13,"span",10)(14,"label",11),e._uU(15),e.ALo(16,"translate"),e.qZA(),e.TgZ(17,"p-password",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.password=i)}),e.qZA()()(),e.TgZ(18,"button",13),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkMobileExist())}),e._uU(19),e.ALo(20,"translate"),e.qZA(),e.TgZ(21,"button",14),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBack())}),e._uU(22),e.ALo(23,"translate"),e.qZA()()()()(),e.BQk()}if(2&o){const t=e.oxw();e.xp6(1),e.Q6J("ngStyle",e.VKq(23,In,t.screenWidth<=768?t.isMobileLayout?"5rem":"220px":"")),e.xp6(3),e.hij(" ",e.lcZ(5,11,"newUser.verifyUser.password")," "),e.xp6(3),e.AsE(" ",e.lcZ(8,13,"newUser.verifyUser.passwordConfirm"),"\xa0",e.lcZ(9,15,t.title)," "),e.xp6(8),e.hij("",e.lcZ(16,17,"signIn.password")," *"),e.xp6(2),e.Q6J("toggleMask",!0)("feedback",!1)("ngModel",t.password),e.xp6(1),e.Q6J("disabled",0===t.password.length),e.xp6(1),e.hij(" ",e.lcZ(20,19,"newUser.verifyUser.continue")," "),e.xp6(3),e.hij(" ",e.lcZ(23,21,"newUser.verifyUser.cancel")," ")}}function Dn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"div",15)(1,"div",4)(2,"p",16),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",17),e._uU(6),e.ALo(7,"translate"),e.ALo(8,"translate"),e.qZA(),e.TgZ(9,"div",18)(10,"div",8)(11,"div",9)(12,"span",10)(13,"label",11),e._uU(14),e.ALo(15,"translate"),e.qZA(),e.TgZ(16,"p-password",12),e.NdJ("ngModelChange",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.password=i)}),e.qZA()()(),e.TgZ(17,"button",19),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkMobileExist())}),e.ALo(18,"translate"),e.qZA()()()()()}if(2&o){const t=e.oxw();e.xp6(3),e.hij(" ",e.lcZ(4,9,"newUser.verifyUser.password")," "),e.xp6(3),e.AsE(" ",e.lcZ(7,11,"newUser.verifyUser.passwordConfirm"),"\xa0",e.lcZ(8,13,t.title)," "),e.xp6(8),e.hij("",e.lcZ(15,15,"signIn.password")," *"),e.xp6(2),e.Q6J("toggleMask",!0)("feedback",!1)("ngModel",t.password),e.xp6(1),e.Q6J("label",e.lcZ(18,17,"newUser.verifyUser.continue"))("disabled",0===t.password.length)}}let Un=(()=>{class o{otpService;translate;messageService;router;store;auth;authTokenService;permissionService;platformId;$gaService;cookieService;loaderService;phoneNumber;countryPhoneNumber="";countryPhoneCode="";phoneLength=12;phoneInputLength=12;tagName=D.s;isGoogleAnalytics=!1;password="";cookieValue;decoded;isPrimary;title="";screenWidth=window.innerWidth;isMobileLayout=!1;isEmail=!1;userEmail="";onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}constructor(t,n,i,r,a,c,d,p,h,_,f,y){this.otpService=t,this.translate=n,this.messageService=i,this.router=r,this.store=a,this.auth=c,this.authTokenService=d,this.permissionService=p,this.platformId=h,this.$gaService=_,this.cookieService=f,this.loaderService=y;let C=localStorage.getItem("profile")??"{}";C=JSON.parse(C),C?.mobileNumber&&(this.phoneNumber=C.mobileNumber),C?.isEmail&&(this.isEmail=C.isEmail,this.userEmail=C.email)}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isPrimary=JSON.parse(localStorage.getItem("isPrimary")??""),this.title=this.translate.instant(this.isPrimary?"newUser.verifyUser.confirmChangeDefaultNumber":"newUser.verifyUser.addNewNumber")}onBack(){this.router.navigate(["account/details"])}triggerEnterPasswordAnalytics(t=""){this.$gaService.event(this.tagName.CLICK_ON_ENTER_PASSWORD,"","CONFIRM_PASSWORD_TO_ADD_NEW_NUMBER",1,!0,{submission_outcome:t})}checkMobileExist(){this.store.set("loading",!0),this.otpService.userPassword=this.password,null!=this.phoneNumber&&""!=this.phoneNumber&&this.password&&""!==this.password?this.auth.login({username:this.isEmail?this.userEmail:this.phoneNumber.replace("-",""),password:this.password}).subscribe({next:t=>{if(t?.success){this.store.set("profile",t.data),this.store.set("userPhone",t.data.mobileNumber),this.store.set("timeInterval",(new Date).getTime());let n=t.data.authToken.replace("bearer ","");this.triggerEnterPasswordAnalytics("Pass"),this.decoded=(0,Pn.Z)(n);let i=(this.decoded.exp/864e5).toFixed(0);const r=new Date;r.setDate(r.getDate()+parseInt(i));let a=Ln.AES.encrypt(n,"paysky").toString();localStorage.setItem("auth_enc",a),this.cookieService.set("authToken",n,{expires:r,path:"/",sameSite:"Strict"}),this.cookieValue=this.cookieService.get("authToken"),this.authTokenService.authTokenSet(this.cookieValue),localStorage.removeItem("isGuest"),this.loaderService.hide(),t?.data?.currency&&this.store.set("currency",t.data.currency),localStorage.setItem("refreshToken",t.data.refreshToken),this.store.set("refreshToken",t.data.refreshToken),this.router.navigate(["/account/verify-mobile"])}else this.triggerEnterPasswordAnalytics("failed"),this.messageService.add({severity:"error",summary:"",detail:t.message}),localStorage.setItem("isGuest","true")}}):(this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:this.translate.instant("ErrorMessages.mobileRequired")}),this.store.set("loading",!1))}omit_special_char(t){let n;return n=t.charCode,n>47&&n<58}static \u0275fac=function(n){return new(n||o)(e.Y36(g.aO),e.Y36(b.sK),e.Y36(T.ez),e.Y36(A.F0),e.Y36(g.d6),e.Y36(g.e8),e.Y36(g.Lz),e.Y36(g.$A),e.Y36(e.Lbi),e.Y36(I.$r),e.Y36(J.N),e.Y36(g.D1))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-verify-user"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.isEmail(a)},!1,e.Jf7)},decls:4,vars:2,consts:[[1,"register"],[4,"ngIf","ngIfElse"],["desktopView",""],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center","margin-x-100"],[1,"col-12","content-container__heading-title","text-center","m-0","py-0","mx-4"],[1,"col-12","content-container__heading-sub-title","mb-3"],[1,"col-12","col-md-8","col-lg-6"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12"],[1,""],[1,"pass-label"],["autocomplete","off",1,"customClass",3,"toggleMask","feedback","ngModel","ngModelChange"],["pButton","","type","button",1,"w-full","second-btn","content-container__primary-btn","mt-4","gap-2",3,"disabled","click"],["pButton","","type","button",1,"w-full","second-btn","content-container__secondary-btn","mt-3","gap-2",3,"click"],[1,"content-container","my-3"],[1,"col-12","bold-font","text-center","font-size-28","m-0","py-0","mx-4",2,"line-height","1.5"],[1,"col-12","text-center","no-underline","font-size-16","mb-3","default-number"],[1,"col-12","col-md-8","col-lg-6","border-round","bg-white","shadow-1","px-5","pt-6"],["pButton","","type","button",1,"p-field","p-col-12","mb-5","mt-7","width-100","font-size-14","second-btn",3,"label","disabled","click"]],template:function(n,i){if(1&n&&(e.TgZ(0,"section",0),e.YNc(1,Nn,24,25,"ng-container",1),e.YNc(2,Dn,19,19,"ng-template",null,2,e.W1O),e.qZA()),2&n){const r=e.MAs(3);e.xp6(1),e.Q6J("ngIf",i.screenWidth<768)("ngIfElse",r)}},dependencies:[Ce.ro,l.JJ,m.O5,m.PC,S.Hq,l.On,x.mh,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}#phone-number[_ngcontent-%COMP%]{padding:.9rem;width:100%}.register[_ngcontent-%COMP%]{margin-top:90px}.pass-label[_ngcontent-%COMP%]{color:#323232;position:absolute;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;z-index:3;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important}  .p-password-input{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;padding-top:20px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px}.default-number[_ngcontent-%COMP%]{font-family:var(--regular-font)!important;color:#a3a3a3}@media only screen and (max-width: 767px){.register[_ngcontent-%COMP%]{margin-top:122px!important}.content-container__heading-title[_ngcontent-%COMP%]{color:#000;font-family:main-medium;font-size:20px;font-style:normal;font-weight:700;line-height:normal}.content-container__heading-sub-title[_ngcontent-%COMP%]{color:#6e6e6e;text-align:center;font-family:main-medium;font-size:14px;font-style:normal;font-weight:400;line-height:normal}.content-container[_ngcontent-%COMP%]   .pass-label[_ngcontent-%COMP%]{color:#323232;font-family:main-medium;font-size:12px;font-style:normal;font-weight:500;line-height:normal}.content-container__primary-btn[_ngcontent-%COMP%]{background:var(--primary, #204E6E);border-radius:8px!important;color:var(--Gray-00, #FFF);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;place-content:center;padding:12px 24px;height:48px}.content-container__secondary-btn[_ngcontent-%COMP%]{background:transparent!important;border-radius:8px!important;color:var(--primary, #204E6E);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;place-content:center;padding:12px 24px;height:48px}  .p-password-input{border-bottom:none!important;border-radius:8px!important}}"]})}return o})();const En=["placesRef"],Fn=["search"];function Jn(o,s){if(1&o){const t=e.EpF();e.TgZ(0,"map-marker",12),e.NdJ("mapDrag",function(i){e.CHM(t);const r=e.oxw();return e.KtG(r.markerDragEnd(i))}),e.qZA()}if(2&o){const t=s.$implicit,n=e.oxw();e.Q6J("options",n.markerOptions)("position",t.position)}}let qn=(()=>{class o{ngZone;addressService;messageService;router;store;route;translate;mainDataService;loaderService;_location;cd;appDataService;platformId;routeToCheckOut;markerOptions={draggable:!0,icon:"assets/images/map-pin.svg"};latitude;longitude;phoneLength=13;position;landMarkAddressRequired=!1;isDisplaySuccessModal=!1;message="";addressDetailCity="";placesRef;addressForm=new l.nJ({addressLabel:new l.p4("Home"),receiverFirstName:new l.p4(""),receiverLastName:new l.p4(""),streetAddress:new l.p4("",l.kI.required),country:new l.p4("",l.kI.required),city:new l.p4("",l.kI.required),state:new l.p4(""),landMark:new l.p4("",l.kI.required),deliveryInstructions:new l.p4(""),buldingNumber:new l.p4(""),postcode:new l.p4(""),receiverPhoneNumber:new l.p4("",l.kI.required),geo_location:new l.p4(""),Lat:new l.p4(""),Lng:new l.p4(""),Id:new l.p4(""),additionalAddress:new l.p4(""),region:new l.nJ({regionId:new l.p4("",l.kI.required),regionName:new l.p4("",l.kI.required)})});search="";Lat;Lng;zoom;address;myplaceHolder;source;isDefault=!1;navbarData;center={lat:.3,lng:32.5};mapOptions={fullscreenControl:!1,disableDefaultUI:!0,componentRestrictions:{country:"UG"}};allCities=[];isDifferentCity=!1;addressLabelList=[{name:"Home",id:1},{name:"Work",id:2},{name:"Other",id:3}];selectedAddressType;searchElementRef;id="";routeSub;geoCoder=new google.maps.Geocoder;redirectUrl;map;borderBottomStyle="2px solid red !important";phoneInputLength=12;preferredCountries=[x.HT.Uganda,x.HT.Ghana,x.HT.C\u00f4teDIvoire];CustomCountryISO;customPlaceHolder="";allRegionList=[];filteredCities=[];countryCoordinates={UG:{lat:.3136,lng:32.5811,country:"UG"},GH:{lat:5.595465,lng:-.242603,country:"GH"},CI:{lat:7.5399,lng:-5.5471,country:"CI"}};constructor(t,n,i,r,a,c,d,p,h,_,f,y,C){this.ngZone=t,this.addressService=n,this.messageService=i,this.router=r,this.store=a,this.route=c,this.translate=d,this.mainDataService=p,this.loaderService=h,this._location=_,this.cd=f,this.appDataService=y,this.platformId=C,this.source=this.router.getCurrentNavigation()?.extras?.state;let v=localStorage.getItem("tenantId");v&&""!==v&&("1"==v||"2"==v||"3"==v?this.customPlaceHolder="XXXXXXXXX":"4"==v&&(this.customPlaceHolder="XXXXXXXXXX")),this.setMapCenterFromIsoCode()}setMapCenterFromIsoCode(){const t=localStorage.getItem("isoCode");if(t&&this.countryCoordinates[t]){const n=this.countryCoordinates[t];this.center={lat:n.lat,lng:n.lng},this.mapOptions={...this.mapOptions,componentRestrictions:{country:n.country}}}else{const n=this.countryCoordinates.UG;this.center={lat:n.lat,lng:n.lng},this.mapOptions={...this.mapOptions,componentRestrictions:{country:n.country}}}}onBack(){this.router.navigateByUrl(this.routeToCheckOut?"/checkout/selectAddress":"/account/address")}ngOnInit(){this.route.queryParamMap.subscribe(a=>{this.routeToCheckOut=a.get("checkout")}),this.navbarData=this.appDataService.layoutTemplate.find(a=>"navbar"===a.type),this.route.queryParams.subscribe(a=>{this.redirectUrl=a.returnUrl}),this.routeSub=this.route.params.subscribe(a=>{this.getCities(),this.setCurrentLocation(),this.getAllRegion()});let t=localStorage.getItem("PhoneLength")?.toString(),n=localStorage.getItem("customerAddressLandmarkRequired")?.toString();n&&"True"==n&&(this.landMarkAddressRequired=!0,this.addressForm.controls.landMark.setValidators([l.kI.required]),this.addressForm.controls.landMark.updateValueAndValidity()),t&&(this.phoneLength=parseInt(t)-2);let i=this.store.get("profile");this.mainDataService.setUserData(i);let r=i?.name?.split(" ");if(this.addressForm.patchValue({receiverFirstName:r[0]?r[0]:"",receiverLastName:r[1]?r[1]:""}),localStorage.getItem("isoCode"))this.CustomCountryISO=localStorage.getItem("isoCode");else{const a=this.appDataService.tenants;if(null!=a.records){let c=localStorage.getItem("tenantId"),p=a.records.find(h=>h.tenantId==c)??new q.Sb;localStorage.setItem("isoCode",p?.isoCode),this.store.set("allCountryTenants",a.records)}}if(this.appDataService.configuration){const a=this.appDataService.configuration.records.find(c=>"PhoneLength"===c.key);a&&(this.phoneInputLength=parseInt(a.value))}this.filteredCities=this.allCities}mapInitialize(t){this.map=t}handleAddressChange(t){this.Lat=t.geometry.location.lat(),this.Lng=t.geometry.location.lng(),this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.zoom=12,this.center=this.position[0].position,this.getAddress(this.Lat,this.Lng)}ngOnDestroy(){this.routeSub.unsubscribe()}getAddress(t,n){this.geoCoder.geocode({location:{lat:t,lng:n}},(i,r)=>{if("OK"===r)if(i[0]){const a=i[0].address_components.find(d=>d.types.includes("country"));console.log(a);const c=localStorage.getItem("isoCode");if(a&&a.short_name===c){if(this.position=[{position:{lat:t,lng:n}}],this.center=this.position[0].position,this.zoom=12,this.address=i[0].formatted_address,i[0]?.address_components.length){const d=i[0].address_components.find(p=>p.types.includes("locality"));this.addressDetailCity=d.long_name}this.addressForm.patchValue({streetAddress:this.address,country:i[i.length-1].formatted_address}),this.validate(),this.getCoordinates(),this.cd.detectChanges()}else this.setMapCenterFromIsoCode(),this.position=[{}],this.cd.detectChanges()}else(0,m.NF)(this.platformId)&&window.alert("No results found");else(0,m.NF)(this.platformId)&&window.alert("Geocoder failed due to: "+r)})}clear(){this.searchElementRef.nativeElement.value=""}onSubmit(){if(this.addressForm.patchValue({Lat:this.Lat?this.Lat.toString():"",Lng:this.Lng?this.Lng.toString():""}),this.loaderService.show(),""==this.addressForm.value.postcode&&(this.addressForm.value.postcode=0),this.addressForm.valid){const t={...this.addressForm.value,region:this.addressForm.value.region.regionName,receiverPhoneNumber:this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)};(""===t.postcode||null===t.postcode)&&delete t.postcode,this.addressService.addAddress(t).subscribe({next:n=>{n?.success?(this.loaderService.hide(),this.isDisplaySuccessModal=!0,this.message=this.translate.instant("ResponseMessages.addressAddedSuccessfully")):(this.loaderService.hide(),this.messageService.add({severity:"error",summary:n?.message}))},error:n=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n})}})}}onConfrim(){this.isDisplaySuccessModal=!1,this.redirectUrl&&""!==this.redirectUrl?this.router.navigate([this.redirectUrl]):this._location.back()}Update(){if(""!==this.Lat.toString()&&""!==this.Lng.toString()&&""!==this.addressForm.controls.streetAddress.value){if(this.addressForm.patchValue({Lat:this.Lat.toString(),Lng:this.Lng.toString(),Id:this.addressService.chosenAddress.id}),this.loaderService.show(),this.addressForm.valid){const t={...this.addressForm.value,region:this.addressForm.value.region.regionName,receiverPhoneNumber:this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)};(""===t.postcode||null===t.postcode)&&delete t.postcode,this.addressService.updateAddress(t).subscribe({next:n=>{this.loaderService.hide(),n.success?(this.isDisplaySuccessModal=!0,this.message=this.translate.instant("ResponseMessages.addressUpdatedSuccessfully")):this.messageService.add({severity:"error",summary:this.translate.instant("ResponseMessages.address"),detail:this.translate.instant(n.message)})},error:n=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n})}})}}else this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.addressIsRequired")})}OnlyNumeric(t){return Number(t.value)||(this.addressForm.value.postcode="",this.addressForm.value.phone=""),!1}checkPlaceHolder(){this.myplaceHolder?this.myplaceHolder="":(this.myplaceHolder=localStorage.getItem("countryPhone")?.toString(),this.myplaceHolder=this.myplaceHolder?this.myplaceHolder+" 000 000 000":"256 000 000 000")}validate(){if(!this.addressForm.valid)return!0}setAsDefault(){this.addressService.setDefault(this.id).subscribe({next:t=>{this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.address"),detail:this.translate.instant("ResponseMessages.defaultAddressSuccessfully")}),this.router.navigate(this.redirectUrl&&""!==this.redirectUrl?[this.redirectUrl]:["/account/address"])},error:t=>{this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t})}})}setCurrentLocation(){this.addressService.chosenAddress=null,"geolocation"in navigator&&navigator.geolocation.getCurrentPosition(t=>{this.Lat=t.coords.latitude,this.Lng=t.coords.longitude,this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.center=this.position[0].position,this.zoom=12,this.getAddress(this.Lat,this.Lng),this.createLocationButton()})}mapClicked(t){let n=JSON.parse(JSON.stringify(t.latLng));this.Lat=n.lat,this.Lng=n.lng,this.position=[{position:{lat:this.Lat,lng:this.Lng}}],this.center=this.position[0].position,this.zoom=12,this.getAddress(this.Lat,this.Lng)}createLocationButton(){if((0,m.NF)(this.platformId)){const t=document.createElement("div");t.index=100,this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(t)}}markerDragEnd(t){if(null!=t.latLng){const n=t.latLng.toJSON();this.getAddress(n.lat,n.lng)}}getCities(){this.addressService.getAllCities({currentPage:1,pageSize:5,ignorePagination:!0}).subscribe(n=>{n.success&&(this.allCities=n.data.records,this.allCities.unshift({id:-1,cityName:null,regionId:-1,isActive:!0}))})}getCoordinates(){var t=new google.maps.Geocoder;this.isDifferentCity=!0,t.geocode({address:this.addressForm.controls.streetAddress.value},(n,i)=>{i==google.maps.GeocoderStatus.OK&&n[0].address_components.length&&n[0].address_components.find(a=>a.types.includes("locality"))?.long_name===this.addressDetailCity&&(this.isDifferentCity=!1,this.cd.detectChanges())})}getAllRegion(){this.addressService.getAllRegions({currentPage:1,pageSize:5,ignorePagination:!0}).subscribe(n=>{n.success&&(this.allRegionList=n.data.records,this.allRegionList.unshift({id:-1,regionName:null,regionId:-1,isActive:!0}))})}filterCitiesByRegion(t){const n=this.allRegionList.find(i=>i.id===t);this.addressForm.patchValue({region:{regionId:n.id,regionName:n.regionName}}),this.filteredCities=this.allCities.filter(i=>i.regionId===t)}addMoreAddress(){this.routeToCheckOut?this.redirectUrl&&""!==this.redirectUrl?(this.router.navigate(["/account/address/add-address"],{state:{returnUrl:this.redirectUrl,lat:this.Lat,lng:this.Lng},queryParams:{checkout:"checkout"}}),this.redirectUrl=""):this.router.navigate(["/account/address/add-address"],{state:{returnUrl:this.redirectUrl,lat:this.Lat,lng:this.Lng},queryParams:{checkout:"checkout"}}):this.redirectUrl&&""!==this.redirectUrl?(this.router.navigate(["/account/address/add-address"],{state:{returnUrl:this.redirectUrl,lat:this.Lat,lng:this.Lng}}),this.redirectUrl=""):this.router.navigate(["/account/address/add-address"],{state:{returnUrl:this.redirectUrl,lat:this.Lat,lng:this.Lng}})}static \u0275fac=function(n){return new(n||o)(e.Y36(e.R0b),e.Y36(g.DM),e.Y36(T.ez),e.Y36(A.F0),e.Y36(g.d6),e.Y36(A.gz),e.Y36(b.sK),e.Y36(g.iI),e.Y36(g.D1),e.Y36(m.Ye),e.Y36(e.sBO),e.Y36(g.UW),e.Y36(e.Lbi))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-mobile-address-map"]],viewQuery:function(n,i){if(1&n&&(e.Gf(En,5),e.Gf(Fn,5)),2&n){let r;e.iGM(r=e.CRH())&&(i.placesRef=r.first),e.iGM(r=e.CRH())&&(i.searchElementRef=r.first)}},decls:18,vars:14,consts:[[1,"map"],[1,"map-header-container"],["src","assets/icons/mobile-icons/back-icon.svg","alt","back-icon",3,"click"],[1,"map-header-detail"],[1,"col-12","search-bar"],[1,"p-inputgroup","search-group","search-top"],["ngx-gp-autocomplete","",1,"map-search","search-input",3,"options","placeholder","onAddressChange"],["placesRef","ngx-places","search",""],["height","700px","width","100%",3,"center","zoom","options","mapClick","mapInitialized"],[3,"options","position","mapDrag",4,"ngFor","ngForOf"],[1,"confirm-address","col-12"],["pButton","","type","button",1,"w-full","confirm-address-Btn",3,"label","click"],[3,"options","position","mapDrag"]],template:function(n,i){1&n&&(e.TgZ(0,"div",0)(1,"div",1)(2,"img",2),e.NdJ("click",function(){return i.onBack()}),e.qZA(),e.TgZ(3,"span",3),e._uU(4),e.ALo(5,"translate"),e.qZA()(),e.TgZ(6,"div")(7,"div",4)(8,"div",5)(9,"input",6,7),e.NdJ("onAddressChange",function(a){return i.handleAddressChange(a)}),e.ALo(12,"translate"),e.qZA()()(),e.TgZ(13,"google-map",8),e.NdJ("mapClick",function(a){return i.mapClicked(a)})("mapInitialized",function(a){return i.mapInitialize(a)}),e.YNc(14,Jn,1,2,"map-marker",9),e.qZA()()(),e.TgZ(15,"div",10)(16,"button",11),e.NdJ("click",function(){return i.addMoreAddress()}),e.ALo(17,"translate"),e.qZA()()),2&n&&(e.xp6(4),e.hij(" ",e.lcZ(5,8,"multipleAddress.selectAddress")," "),e.xp6(5),e.s9C("placeholder",e.lcZ(12,10,"addingAddress.search")),e.Q6J("options",i.mapOptions),e.xp6(4),e.Q6J("center",i.center)("zoom",i.zoom)("options",i.mapOptions),e.xp6(1),e.Q6J("ngForOf",i.position),e.xp6(2),e.Q6J("label",e.lcZ(17,12,"multipleAddress.confirmAddress")))},dependencies:[m.sg,S.Hq,oe,F.b6,F.O_,b.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.map[_ngcontent-%COMP%]{margin-top:74px}.search-bar[_ngcontent-%COMP%]{position:absolute;z-index:3;width:100%}.search-top[_ngcontent-%COMP%]{top:30px;position:relative;box-shadow:-2px 0 5px #33333330}.search-input[_ngcontent-%COMP%]{width:100%;padding:16px;border-radius:8px}.search-input[_ngcontent-%COMP%]::placeholder{color:#1a445e!important}.map-header-container[_ngcontent-%COMP%]{background:#F6F6F6;padding:16px}.map-header-detail[_ngcontent-%COMP%]{color:#2d2d2d;font-size:16px;font-weight:500;font-family:var(--medium-font)}.confirm-address-Btn[_ngcontent-%COMP%]{bottom:110px;color:#fff;background-color:var(--header_bgcolor);padding:16px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none}@media only screen and (max-width: 767px){.confirm-address-Btn[_ngcontent-%COMP%]{position:fixed;bottom:69px;left:0}}@media only screen and (min-width: 768px) and (max-width: 1200px){.confirm-address-Btn[_ngcontent-%COMP%]{position:fixed;bottom:69px;left:0}}.confirm-address[_ngcontent-%COMP%]{position:relative}"]})}return o})();function Hn(o,s){1&o&&(e.TgZ(0,"main",1)(1,"header",2),e._UZ(2,"img",3),e.TgZ(3,"span",4),e._uU(4),e.ALo(5,"translate"),e.qZA()(),e.TgZ(6,"div",5),e.O4$(),e.TgZ(7,"svg",6),e._UZ(8,"circle",7)(9,"path",8),e.qZA(),e.kcU(),e.TgZ(10,"span"),e._uU(11),e.ALo(12,"translate"),e.qZA(),e.TgZ(13,"div",9),e._UZ(14,"img",10),e.qZA()()()),2&o&&(e.xp6(4),e.hij(" ",e.lcZ(5,2,"footer.help")," "),e.xp6(7),e.hij(" ",e.lcZ(12,4,"footer.contactUs"),""))}const zn=[{path:"",component:Me},{path:"address",component:Wt},{path:"address/:id",component:ae},{path:"add-address",component:ae},{path:"details",component:kt},{path:"verify-otp",component:pn},{path:"verify-mobile",component:kn},{path:"verify-user",component:Un},{path:"help",component:(()=>{class o{permissionService;platformId;$gtmService;constructor(t,n,i){this.permissionService=t,this.platformId=n,this.$gtmService=i,this.isMobileTemplate=this.permissionService.hasPermission("Mobile-Layout"),this.$gtmService.pushPageView("account","help")}isMobileTemplate=!1;screenWidth=window.innerWidth;onResize(t){(0,m.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}static \u0275fac=function(n){return new(n||o)(e.Y36(g.$A),e.Y36(e.Lbi),e.Y36(U.J))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-help"]],hostBindings:function(n,i){1&n&&e.NdJ("resize",function(a){return i.onResize(a)},!1,e.Jf7)},decls:1,vars:1,consts:[["class","help-page-mobile",4,"ngIf"],[1,"help-page-mobile"],[1,"header-container"],["src","assets/icons/mobile-icons/back-icon.svg","alt","back-icon","routerLink","/account"],[1,""],["routerLink","/contact-us",1,"help-item"],["width","20","height","20","viewBox","0 0 20 20","fill","none","xmlns","http://www.w3.org/2000/svg"],["cx","10","cy","10","r","10","fill","#FFCB05"],["d","M14.0637 12.1532L12.757 11.2402C12.5862 11.121 12.3864 11.0582 12.1793 11.0582C11.85 11.0582 11.5409 11.2197 11.3526 11.49L11.0492 11.9247C10.5408 11.5837 9.97358 11.1077 9.43281 10.567C8.89214 10.0263 8.41616 9.45904 8.07531 8.95067L8.50982 8.64734C8.73131 8.49311 8.87923 8.26207 8.92621 7.99695C8.97309 7.73203 8.914 7.46435 8.75958 7.24276L7.84683 5.93609C7.65597 5.66309 7.34801 5.5 7.02292 5.5C6.91025 5.5 6.79975 5.51989 6.69457 5.5587C6.57511 5.60282 6.46383 5.65698 6.35471 5.72553L6.17428 5.85257C6.12918 5.88763 6.08723 5.92604 6.04704 5.96622C5.82693 6.18623 5.67074 6.46474 5.5826 6.79407C5.20649 8.20396 6.13824 10.3359 7.9011 12.0988C9.3815 13.5792 11.16 14.4988 12.5424 14.499H12.5425C12.7793 14.499 13.0024 14.4715 13.2059 14.4171C13.5352 14.3291 13.8138 14.1729 14.034 13.9526C14.0739 13.9127 14.1122 13.8707 14.1531 13.8177L14.2803 13.6363C14.3424 13.537 14.3965 13.4258 14.4412 13.3054C14.5949 12.8898 14.4397 12.4159 14.0637 12.1532Z","fill","#004F71"],[1,"arrow"],["alt","","src","assets/icons/arrow.svg",2,"width","20px","height","20px"]],template:function(n,i){1&n&&e.YNc(0,Hn,15,6,"main",0),2&n&&e.Q6J("ngIf",i.isMobileTemplate&&i.screenWidth<=768)},dependencies:[m.O5,A.rH,b.X$],styles:[".help-page-mobile[_ngcontent-%COMP%]{margin-top:80px!important;padding:15px}.header-container[_ngcontent-%COMP%]{padding:16px 10px;font-size:16px;font-weight:500;color:#2d2d2d}.help-item[_ngcontent-%COMP%]{border:1px solid #E4E7E9;cursor:pointer;display:flex;padding:24px 16px;border-radius:8px;margin-bottom:16px;font-family:main-medium;align-items:center;position:relative}.help-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:16px}.help-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#323232;font-family:main-medium;font-size:14px;font-style:normal;font-weight:400;line-height:100%;align-self:center}.help-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]{right:10px;position:absolute}.help-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flar-header-img[_ngcontent-%COMP%]{width:24px;border-radius:4px;height:18px;margin-right:5px}.help-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flag-name[_ngcontent-%COMP%]{color:#a3a3a3;font-family:main-regular;font-size:12px;font-style:normal;font-weight:400;line-height:100%;text-transform:capitalize}.help-item[_ngcontent-%COMP%]   .right-text[_ngcontent-%COMP%]{margin-left:auto;color:#aaa;position:absolute;right:37px;text-transform:capitalize;font-size:12px}.help-item[_ngcontent-%COMP%]   .country-flag[_ngcontent-%COMP%]{margin-left:auto;width:24px;height:16px}.arrow[_ngcontent-%COMP%]{right:10px;position:absolute}.arrow[_ngcontent-%COMP%]   .flar-header-img[_ngcontent-%COMP%]{width:24px;border-radius:4px;height:18px;margin-right:5px}.arrow[_ngcontent-%COMP%]   .flag-name[_ngcontent-%COMP%]{color:#a3a3a3;font-family:main-regular;font-size:12px;font-style:normal;font-weight:400;line-height:100%;text-transform:capitalize}"]})}return o})()},{path:"verify-address",component:qn}];var Qn=u(258),Yn=u(9663);let Gn=(()=>{class o{static \u0275fac=function(n){return new(n||o)};static \u0275mod=e.oAB({type:o});static \u0275inj=e.cJS({imports:[Ce.gz,B.j,Yn.zz,l.UX,m.ez,Qn.m,l.u5,l.UX,ke.forRoot({loaderOptions:{apiKey:localStorage.getItem("mapKey")??"",libraries:["places"]}}),A.Bz.forChild(zn),F.Y4,l.UX,b.aw,ie.kW,de.cc,Mn,x.J7,ce.Ps,pe.L,re.S,K.$]})}return o})()}}]);