@import '../../../../assets/scss/common.scss';

// Base styles
.category-listing {
  margin: 78px 0 72px;
  padding-top: 16px;
  background-color: #F5F7FC;
  min-height: 55vh;
  
  .main-heading-category-mobile {
    padding: 0 16px;
    margin-bottom: 16px;
  }
}

.main-header-category {
  font: normal 600 16px/100% var(--regular-font);
}

.category-Name-Mobile {
  color: #204E6E;
  font-family: var(--regular-font);
}

.child-categories-setting {
  margin-left: 28px;
}

.child-category-text {
  font: 400 14px var(--regular-font);
}

// PrimeNG overrides
::ng-deep .disabled-handling .p-accordion-tab .p-accordion-header {
  opacity: 1 !important;
  font: normal 400 14px/100% var(--light-font);
}

::ng-deep .accordion-holder {
  .p-component {
    padding: 0 8px;
  }
  
  .p-accordion {
    .p-accordion-header {
      .p-accordion-header-link {
        padding: 24px 8px !important;
        background: #fff !important;
        border: 0.1px solid #e4e7e957 !important;
        color: #323232 !important;
      }
      
      &:not(.p-disabled) .p-accordion-header-link:focus {
        box-shadow: none !important;
      }
    }
    
    .p-accordion-content {
      border: none !important;
      padding: 0 !important;
    }
    
    .p-accordion-tab-active .p-accordion-header .p-accordion-header-link {
      border-bottom: 0 !important;
      
      .p-accordion-toggle-icon-end {
        transform: rotate(-180deg) !important;
      }
    }
    
    p-accordiontab:first-child .p-accordion-header .p-accordion-header-link {
      border-radius: 4px 4px 0 0;
    }
  }
  
  &.apply-styles .p-accordion {
    p-accordiontab:first-child .p-accordion-header .p-accordion-header-link chevronrighticon {
      display: none;
    }
    
    .p-accordion-tab .p-accordion-header .p-accordion-header-link[aria-expanded="false"] chevronrighticon {
      transform: rotate(90deg);
    }
  }
}

::ng-deep .list-box-child .p-listbox {
  border: none !important;
  
  .p-listbox-list-wrapper {
    .p-listbox-list {
      .p-listbox-empty-message {
        text-align: center;
      }
    }
  }
}
