{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { InputMaskModule } from 'primeng/inputmask';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { ConfirmationDeleteDialogComponent } from '../../shared/modals/confirmation-delete-dialog/confirmation-delete-dialog.component';\nimport { GoogleMapsModule } from \"@angular/google-maps\";\nimport { NgxGpAutocompleteModule } from \"@angular-magic/ngx-gp-autocomplete\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { RadioButtonModule } from \"primeng/radiobutton\";\nimport { NgToggleModule } from \"ng-toggle-button\";\nimport { NgxIntlTelInputModule } from \"ngx-intl-tel-input-gg\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { ProgressSpinnerModule } from \"primeng/progressspinner\";\nimport { DialogModule } from \"primeng/dialog\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular-magic/ngx-gp-autocomplete\";\nimport * as i2 from \"@angular/router\";\nexport let AccountModule = /*#__PURE__*/(() => {\n  class AccountModule {\n    static ɵfac = function AccountModule_Factory(t) {\n      return new (t || AccountModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AccountModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [PasswordModule, InputTextModule, InputMaskModule, ReactiveFormsModule, CommonModule, SharedModule, FormsModule, ReactiveFormsModule, NgxGpAutocompleteModule.forRoot({\n        loaderOptions: {\n          apiKey: localStorage.getItem('mapKey') ?? '',\n          libraries: ['places']\n        }\n      }), RouterModule.forChild(routes), GoogleMapsModule, ReactiveFormsModule, TranslateModule, DropdownModule, RadioButtonModule, NgToggleModule, NgxIntlTelInputModule, MatIconModule, ProgressSpinnerModule, DialogModule, ConfirmationDeleteDialogComponent]\n    });\n  }\n  return AccountModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}