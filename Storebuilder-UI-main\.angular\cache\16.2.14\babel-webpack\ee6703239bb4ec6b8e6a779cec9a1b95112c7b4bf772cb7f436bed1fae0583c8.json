{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LoaderService {\n  constructor() {\n    this.isLoading = new Subject();\n  }\n  show() {\n    this.isLoading.next(true);\n  }\n  hide() {\n    this.isLoading.next(false);\n  }\n  static #_ = this.ɵfac = function LoaderService_Factory(t) {\n    return new (t || LoaderService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoaderService,\n    factory: LoaderService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Subject", "LoaderService", "constructor", "isLoading", "show", "next", "hide", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\services\\loader.service.ts"], "sourcesContent": ["\r\nimport { Injectable } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LoaderService {\r\n\r\n  isLoading = new Subject<boolean>();\r\n\r\n\r\n  constructor() {\r\n  }\r\n\r\n  show() {\r\n     this.isLoading.next(true);\r\n  }\r\n\r\n  hide() {\r\n\r\n     this.isLoading.next(false);\r\n  }\r\n}"], "mappings": "AAEA,SAASA,OAAO,QAAQ,MAAM;;AAK9B,OAAM,MAAOC,aAAa;EAKxBC,YAAA;IAHA,KAAAC,SAAS,GAAG,IAAIH,OAAO,EAAW;EAIlC;EAEAI,IAAIA,CAAA;IACD,IAAI,CAACD,SAAS,CAACE,IAAI,CAAC,IAAI,CAAC;EAC5B;EAEAC,IAAIA,CAAA;IAED,IAAI,CAACH,SAAS,CAACE,IAAI,CAAC,KAAK,CAAC;EAC7B;EAAC,QAAAE,CAAA,G;qBAfUN,aAAa;EAAA;EAAA,QAAAO,EAAA,G;WAAbP,aAAa;IAAAQ,OAAA,EAAbR,aAAa,CAAAS,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}