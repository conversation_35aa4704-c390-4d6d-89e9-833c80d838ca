{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"primeng/dialog\";\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"100vw\"\n  };\n};\nexport let SizeGuideModalComponent = /*#__PURE__*/(() => {\n  class SizeGuideModalComponent {\n    translate;\n    displayModal = false;\n    sizeGuidImage;\n    submit = new EventEmitter();\n    cancel = new EventEmitter();\n    buttonText;\n    addressName = '';\n    isStoreCloud = environment.isStoreCloud;\n    constructor(translate) {\n      this.translate = translate;\n      this.buttonText = this.translate.instant('settings.address.addAddress');\n    }\n    ngOnInit() {\n      /**/\n    }\n    closeModal() {\n      this.cancel.emit(true);\n    }\n    static ɵfac = function SizeGuideModalComponent_Factory(t) {\n      return new (t || SizeGuideModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SizeGuideModalComponent,\n      selectors: [[\"app-mtn-size-guide-modal\"]],\n      inputs: {\n        displayModal: \"displayModal\",\n        sizeGuidImage: \"sizeGuidImage\"\n      },\n      outputs: {\n        submit: \"submit\",\n        cancel: \"cancel\"\n      },\n      decls: 2,\n      vars: 9,\n      consts: [[3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"onHide\", \"visibleChange\"], [\"alt\", \"No Image\", 1, \"size-guid\", 3, \"src\"]],\n      template: function SizeGuideModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dialog\", 0);\n          i0.ɵɵlistener(\"onHide\", function SizeGuideModalComponent_Template_p_dialog_onHide_0_listener() {\n            return ctx.closeModal();\n          })(\"visibleChange\", function SizeGuideModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            return ctx.displayModal = $event;\n          });\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(8, _c0))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"src\", ctx.sizeGuidImage, i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i2.Dialog],\n      styles: [\"*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box;list-style:none;text-decoration:none}  .p-dialog-content{border-bottom:none}.size-guid[_ngcontent-%COMP%]{width:100%;height:500px}\"]\n    });\n  }\n  return SizeGuideModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}