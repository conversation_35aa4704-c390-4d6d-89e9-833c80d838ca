<section class="login">
  <app-back-button></app-back-button>
  <!-- if Desktop  -->
  <ng-container *ngIf="isMobileLayout ? screenWidth > 767  : true">
    <div class="content-container my-3">
      <div class="grid justify-content-between mobile-top ">
        <div class="shadow-signin">
          <div class="col-12 image" >
            <img src="assets/images/new-signin.svg" alt="" srcset="">
          </div>
          <div class="col-12 col-md-8 col-lg-6 bg-white px-5 pt-6 header-body" style="line-height: 1.5">
            <p class="signin-heading">
              {{ "signIn.signIn" | translate }}
            </p>
            <p class="signIn-content">
              {{ "signIn.content" | translate }}
            </p>
            <form [formGroup]="signInForm" autocomplete=new-password>
              <div class="p-fluid p-grid">
               <!-- Phone Number Input -->
               <div class="p-field p-col-12">
                <div [ngStyle]="{
                    border:
                      signInForm.controls.phoneNumber.value?.e164Number?.length > 0 &&
                      !signInForm.controls.phoneNumber.valid
                        ? '1px solid red'
                        : '0px solid transparent'
                  }" class="custom-input">
                  <form #f="ngForm" [formGroup]="signInForm">
                    <label class="contact-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}</label>
                    <ngx-intl-tel-input [cssClass]="'custom contact-input-phone mobile-input-phone'" [enableAutoCountrySelect]="true"
                      [enablePlaceholder]="true" [maxLength]="phoneInputLength"
                      [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
                      [preferredCountries]="preferredCountries"
                      [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                      [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
                      [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" autocomplete="new-phoneNumber"
                      formControlName="phoneNumber" name="phoneNumber"></ngx-intl-tel-input>
                  </form>
                </div>
              </div>
              <!-- Password Input -->
              <div class="p-field p-col-12">
                <label  class="contact-label">{{ "signIn.password" | translate }} </label>
                <span class="p-float-label ">
                  <p-password (ngModelChange)="modelChanged($event)" [(ngModel)]="password" [feedback]="false"
                    [ngModelOptions]="{ standalone: true }" [toggleMask]="true" autocomplete="off"
                    class="customClass" id="custom-password-input"></p-password>
                </span>
              </div>
                <div class="flex justify-content-end flex-wrap resetPassword cursor-pointer">
                  <a class="no-underline font-size-12  mb-4" (click)="resetPassword()">{{
                    "signIn.forgotPassword" | translate }}</a>
                </div>
  
                <button (click)="login()" [disabled]="
                    (!signInForm.controls.phoneNumber.valid || !password) === true
                      ? true
                      : false
                  " [label]="'signIn.continue' | translate"
                  class="p-field p-col-12 my-2 width-100 font-size-14 second-btn" pButton type="button"></button>
                  <p class="signin-agreement"> {{ "signIn.AgreeTermsOne" | translate }} <a (click)="reloadCurrentPage(171, 'Terms and Conditions')"> {{ "signIn.AgreeTermsTwo" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsThree" | translate }} <a (click)="reloadCurrentPage(3, 'Privacy policy')"> {{ "signIn.AgreeTermsFour" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsFive" | translate }}.</p>
                  <div class="new-customer-container">
                    <p> {{ "signIn.newCustomer" | translate }} </p>
                    <a class="register-now" [routerLink]="['/register']" (click)="onSignupCtaClick('signin_page')"> {{ "signIn.Register" | translate }} </a>
                  </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- if mobile  -->
  <ng-container *ngIf="isMobileLayout && screenWidth <= 767">
    <!-- <app-back-button></app-back-button> -->
    <div class="content-container my-3">
      <div class="grid justify-content-center mobile-top">
        <div class="col-12 col-md-8 col-lg-6 bg-white mt-0 mobile-container">
          <sign-in-up-header *ngIf="screenWidth < 768" [title]="'signIn.signIn'" [img]="'assets/images/new-signin.svg'"></sign-in-up-header>
          <form [formGroup]="signInForm" autocomplete=new-password>
            <div class="p-fluid p-grid">
              <!-- Phone Number Input -->
              <div class="p-field p-col-12">

                <div [ngStyle]="{
                    border:
                      signInForm.controls.phoneNumber.value?.e164Number?.length > 0 &&
                      !signInForm.controls.phoneNumber.valid
                        ? '1px solid red'
                        : '0px solid transparent'
                  }" class="custom-input">
                  <form #f="ngForm" [formGroup]="signInForm">
                    <label class="contact-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}*</label>
                    <ngx-intl-tel-input [cssClass]="'custom contact-input-phone mobile-input-phone'" [enableAutoCountrySelect]="true"
                      [enablePlaceholder]="true" [maxLength]="phoneInputLength"
                      [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
                      [preferredCountries]="preferredCountries"
                      [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                      [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
                      [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" autocomplete="new-phoneNumber"
                      formControlName="phoneNumber" name="phoneNumber"></ngx-intl-tel-input>
                  </form>

                </div>
              </div>
              <!-- Password Input -->
              <div class="p-field p-col-12">
                <span class="p-float-label mt-3">
                  <p-password (ngModelChange)="modelChanged($event)" [(ngModel)]="password" [feedback]="false"
                    [ngModelOptions]="{ standalone: true }" [toggleMask]="true" autocomplete="off"
                    class="customClass" id="custom-password-input"></p-password>
                  <label>{{ "signIn.password" | translate }} *</label>
                </span>
              </div>
              <!-- Reset Password -->
              <div class="flex justify-content-end flex-wrap">
                <a class="no-underline main-color font-size-12 bold-font mb-4 mt-4" (click)="resetPassword()">{{
                  "signIn.forgotPassword" | translate }}</a>
              </div>

              <button (click)="login()" [disabled]="
                  (!signInForm.controls.phoneNumber.valid || !password) === true
                    ? true
                    : false
                " [label]="'signIn.signIn' | translate"
                class="primary-btn second-btn mb-3" pButton type="button"></button>

                <p class="signin-agreement"> {{ "signIn.AgreeTermsOne" | translate }} <a (click)="reloadCurrentPage(171, 'Terms and Conditions')"> {{ "signIn.AgreeTermsTwo" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsThree" | translate }} <a (click)="reloadCurrentPage(3, 'Privacy policy')"> {{ "signIn.AgreeTermsFour" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsFive" | translate }}.</p>
              <div class="new-customer-container">
                <p> {{ "signIn.newCustomer" | translate }} </p>
                <a class="register-now" [routerLink]="['/register']" (click)="onSignupCtaClick('signin_page_mobile')"> {{ "signIn.Register" | translate }} </a>
              </div>
            </div>
          </form>
        </div>
      </div>
      </div>
    </ng-container>
</section>
