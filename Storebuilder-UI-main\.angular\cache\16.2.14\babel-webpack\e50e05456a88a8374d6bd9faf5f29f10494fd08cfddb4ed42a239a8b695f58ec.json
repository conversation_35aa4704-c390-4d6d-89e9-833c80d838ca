{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, SkipSelf, Optional, ViewChild, NgModule, Injectable } from '@angular/core';\nimport { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i4 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/api';\nimport { SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { Subject } from 'rxjs';\nconst _c0 = [\"mask\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"titlebar\"];\nfunction DynamicDialogComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"mousedown\", function DynamicDialogComponent_div_2_div_2_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.initResize($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DynamicDialogComponent_div_2_div_3_button_5_WindowMaximizeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMaximizeIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nfunction DynamicDialogComponent_div_2_div_3_button_5_WindowMinimizeIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMinimizeIcon\", 21);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nconst _c3 = function () {\n  return {\n    \"p-dialog-header-icon p-dialog-header-maximize p-link\": true\n  };\n};\nfunction DynamicDialogComponent_div_2_div_3_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DynamicDialogComponent_div_2_div_3_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.maximize());\n    })(\"keydown.enter\", function DynamicDialogComponent_div_2_div_3_button_5_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.maximize());\n    });\n    i0.ɵɵelement(1, \"span\", 19);\n    i0.ɵɵtemplate(2, DynamicDialogComponent_div_2_div_3_button_5_WindowMaximizeIcon_2_Template, 1, 1, \"WindowMaximizeIcon\", 20);\n    i0.ɵɵtemplate(3, DynamicDialogComponent_div_2_div_3_button_5_WindowMinimizeIcon_3_Template, 1, 1, \"WindowMinimizeIcon\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(4, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r11.maximized ? ctx_r11.minimizeIcon : ctx_r11.maximizeIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.maximized && !ctx_r11.maximizeIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.maximized && !ctx_r11.minimizeIcon);\n  }\n}\nfunction DynamicDialogComponent_div_2_div_3_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function DynamicDialogComponent_div_2_div_3_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.hide());\n    })(\"keydown.enter\", function DynamicDialogComponent_div_2_div_3_button_6_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.hide());\n    });\n    i0.ɵɵelement(1, \"TimesIcon\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-dialog-header-icon p-dialog-header-maximize p-link\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-close-icon\");\n  }\n}\nfunction DynamicDialogComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 13);\n    i0.ɵɵlistener(\"mousedown\", function DynamicDialogComponent_div_2_div_3_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.initDrag($event));\n    });\n    i0.ɵɵelementStart(2, \"span\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 15);\n    i0.ɵɵtemplate(5, DynamicDialogComponent_div_2_div_3_button_5_Template, 4, 5, \"button\", 16);\n    i0.ɵɵtemplate(6, DynamicDialogComponent_div_2_div_3_button_6_Template, 2, 2, \"button\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.config.header);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.config.maximizable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.config.closable !== false);\n  }\n}\nfunction DynamicDialogComponent_div_2_ng_template_6_Template(rf, ctx) {}\nfunction DynamicDialogComponent_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.config.footer, \" \");\n  }\n}\nconst _c4 = function (a1, a2, a3, a4) {\n  return {\n    \"p-dialog p-dynamic-dialog p-component\": true,\n    \"p-dialog-rtl\": a1,\n    \"p-dialog-resizable\": a2,\n    \"p-dialog-draggable\": a3,\n    \"p-dialog-maximized\": a4\n  };\n};\nconst _c5 = function (a0, a1) {\n  return {\n    transform: a0,\n    transition: a1\n  };\n};\nconst _c6 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nfunction DynamicDialogComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 4);\n    i0.ɵɵlistener(\"@animation.start\", function DynamicDialogComponent_div_2_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onAnimationStart($event));\n    })(\"@animation.done\", function DynamicDialogComponent_div_2_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, DynamicDialogComponent_div_2_div_2_Template, 1, 0, \"div\", 5);\n    i0.ɵɵtemplate(3, DynamicDialogComponent_div_2_div_3_Template, 7, 3, \"div\", 6);\n    i0.ɵɵelementStart(4, \"div\", 7, 8);\n    i0.ɵɵtemplate(6, DynamicDialogComponent_div_2_ng_template_6_Template, 0, 0, \"ng-template\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, DynamicDialogComponent_div_2_div_7_Template, 2, 1, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.config.styleClass);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.config.width)(\"height\", ctx_r1.config.height);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(13, _c4, ctx_r1.config.rtl, ctx_r1.config.resizable, ctx_r1.config.draggable, ctx_r1.maximized))(\"ngStyle\", ctx_r1.config.style)(\"@animation\", i0.ɵɵpureFunction1(21, _c6, i0.ɵɵpureFunction2(18, _c5, ctx_r1.transformOptions, ctx_r1.config.transitionOptions || \"150ms cubic-bezier(0, 0, 0.2, 1)\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.resizable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showHeader === false ? false : true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.config.contentStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.footer);\n  }\n}\nconst _c7 = function (a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n  return {\n    \"p-dialog-mask\": true,\n    \"p-component-overlay p-component-overlay-enter p-dialog-mask-scrollblocker\": a1,\n    \"p-dialog-left\": a2,\n    \"p-dialog-right\": a3,\n    \"p-dialog-top\": a4,\n    \"p-dialog-bottom\": a5,\n    \"p-dialog-top-left\": a6,\n    \"p-dialog-top-right\": a7,\n    \"p-dialog-bottom-left\": a8,\n    \"p-dialog-bottom-right\": a9\n  };\n};\nclass DynamicDialogContent {\n  viewContainerRef;\n  constructor(viewContainerRef) {\n    this.viewContainerRef = viewContainerRef;\n  }\n  static ɵfac = function DynamicDialogContent_Factory(t) {\n    return new (t || DynamicDialogContent)(i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DynamicDialogContent,\n    selectors: [[\"\", \"pDynamicDialogContent\", \"\"]],\n    hostAttrs: [1, \"p-element\"]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DynamicDialogContent, [{\n    type: Directive,\n    args: [{\n      selector: '[pDynamicDialogContent]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }];\n  }, null);\n})();\n\n/**\n * Dialogs can be created dynamically with any component as the content using a DialogService.\n * @group Interface\n */\nclass DynamicDialogConfig {\n  /**\n   * An object to pass to the component loaded inside the Dialog.\n   */\n  data;\n  /**\n   * Header text of the dialog.\n   */\n  header;\n  /**\n   * Identifies the element (or elements) that labels the element it is applied to.\n   */\n  ariaLabelledBy;\n  /**\n   * Footer text of the dialog.\n   */\n  footer;\n  /**\n   * Width of the dialog.\n   */\n  width;\n  /**\n   * Height of the dialog.\n   */\n  height;\n  /**\n   * Specifies if pressing escape key should hide the dialog.\n   */\n  closeOnEscape;\n  /**\n   * Base zIndex value to use in layering.\n   */\n  baseZIndex;\n  /**\n   * Whether to automatically manage layering.\n   */\n  autoZIndex;\n  /**\n   * Specifies if clicking the modal background should hide the dialog.\n   */\n  dismissableMask;\n  /**\n   * Inline style of the component.\n   */\n  rtl;\n  /**\n   * Inline style of the comopnent.\n   */\n  style;\n  /**\n   * Inline style of the content.\n   */\n  contentStyle;\n  /**\n   * Style class of the component.\n   */\n  styleClass;\n  /**\n   * Transition options of the animation.\n   */\n  transitionOptions;\n  /**\n   * Adds a close icon to the header to hide the dialog.\n   */\n  closable;\n  /**\n   * Whether to show the header or not.\n   */\n  showHeader;\n  /**\n   * Defines if background should be blocked when dialog is displayed.\n   */\n  modal;\n  /**\n   * Style class of the mask.\n   */\n  maskStyleClass;\n  /**\n   * Enables resizing of the content.\n   */\n  resizable;\n  /**\n   * Enables dragging to change the position using header.\n   */\n  draggable;\n  /**\n   * Keeps dialog in the viewport.\n   */\n  keepInViewport;\n  /**\n   * Minimum value for the left coordinate of dialog in dragging.\n   */\n  minX;\n  /**\n   * Minimum value for the top coordinate of dialog in dragging.\n   */\n  minY;\n  /**\n   * Whether the dialog can be displayed full screen.\n   */\n  maximizable;\n  /**\n   * Name of the maximize icon.\n   */\n  maximizeIcon;\n  /**\n   * Name of the minimize icon.\n   */\n  minimizeIcon;\n  /**\n   * Position of the dialog, options are \"center\", \"top\", \"bottom\", \"left\", \"right\", \"top-left\", \"top-right\", \"bottom-left\" or \"bottom-right\".\n   */\n  position;\n}\n\n/**\n * Dynamic Dialog instance.\n * @group Components\n */\nclass DynamicDialogRef {\n  constructor() {}\n  /**\n   * Closes dialog.\n   * @group Method\n   */\n  close(result) {\n    this._onClose.next(result);\n  }\n  /**\n   * Destroys the dialog instance.\n   * @group Method\n   */\n  destroy() {\n    this._onDestroy.next(null);\n  }\n  /**\n   * Callback to invoke on drag start.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Method\n   */\n  dragStart(event) {\n    this._onDragStart.next(event);\n  }\n  /**\n   * Callback to invoke on drag end.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Method\n   */\n  dragEnd(event) {\n    this._onDragEnd.next(event);\n  }\n  /**\n   * Callback to invoke on resize start.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Method\n   */\n  resizeInit(event) {\n    this._onResizeInit.next(event);\n  }\n  /**\n   * Callback to invoke on resize start.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Method\n   */\n  resizeEnd(event) {\n    this._onResizeEnd.next(event);\n  }\n  /**\n   * Callback to invoke on dialog is maximized.\n   * @param {*} value - Size value.\n   * @group Method\n   */\n  maximize(value) {\n    this._onMaximize.next(value);\n  }\n  _onClose = new Subject();\n  /**\n   * Event triggered on dialog is closed.\n   * @group Events\n   */\n  onClose = this._onClose.asObservable();\n  _onDestroy = new Subject();\n  /**\n   * Event triggered on dialog instance is destroyed.\n   * @group Events\n   */\n  onDestroy = this._onDestroy.asObservable();\n  _onDragStart = new Subject();\n  /**\n   * Event triggered on drag start.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Events\n   */\n  onDragStart = this._onDragStart.asObservable();\n  _onDragEnd = new Subject();\n  /**\n   * Event triggered on drag end.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Events\n   */\n  onDragEnd = this._onDragEnd.asObservable();\n  _onResizeInit = new Subject();\n  /**\n   * Event triggered on resize start.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Events\n   */\n  onResizeInit = this._onResizeInit.asObservable();\n  _onResizeEnd = new Subject();\n  /**\n   * Event triggered on resize end.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Events\n   */\n  onResizeEnd = this._onResizeEnd.asObservable();\n  _onMaximize = new Subject();\n  /**\n   * Event triggered on resize end.\n   * @param {*} value - Size value.\n   * @group Events\n   */\n  onMaximize = this._onMaximize.asObservable();\n}\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}', style({\n  transform: 'none',\n  opacity: 1\n}))]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\nclass DynamicDialogComponent {\n  document;\n  platformId;\n  componentFactoryResolver;\n  cd;\n  renderer;\n  config;\n  dialogRef;\n  zone;\n  primeNGConfig;\n  parentDialog;\n  visible = true;\n  componentRef;\n  mask;\n  resizing;\n  dragging;\n  maximized;\n  _style = {};\n  originalStyle;\n  lastPageX;\n  lastPageY;\n  insertionPoint;\n  maskViewChild;\n  contentViewChild;\n  headerViewChild;\n  childComponentType;\n  container;\n  wrapper;\n  documentKeydownListener;\n  documentEscapeListener;\n  maskClickListener;\n  transformOptions = 'scale(0.7)';\n  documentResizeListener;\n  documentResizeEndListener;\n  documentDragListener;\n  documentDragEndListener;\n  get minX() {\n    return this.config.minX ? this.config.minX : 0;\n  }\n  get minY() {\n    return this.config.minY ? this.config.minY : 0;\n  }\n  get keepInViewport() {\n    return this.config.keepInViewport;\n  }\n  get maximizable() {\n    return this.config.maximizable;\n  }\n  get maximizeIcon() {\n    return this.config.maximizeIcon;\n  }\n  get minimizeIcon() {\n    return this.config.minimizeIcon;\n  }\n  get style() {\n    return this._style;\n  }\n  get position() {\n    return this.config.position;\n  }\n  set style(value) {\n    if (value) {\n      this._style = {\n        ...value\n      };\n      this.originalStyle = value;\n    }\n  }\n  get parent() {\n    const domElements = Array.from(this.document.getElementsByClassName('p-dialog'));\n    if (domElements.length > 1) {\n      return domElements.pop();\n    }\n  }\n  constructor(document, platformId, componentFactoryResolver, cd, renderer, config, dialogRef, zone, primeNGConfig, parentDialog) {\n    this.document = document;\n    this.platformId = platformId;\n    this.componentFactoryResolver = componentFactoryResolver;\n    this.cd = cd;\n    this.renderer = renderer;\n    this.config = config;\n    this.dialogRef = dialogRef;\n    this.zone = zone;\n    this.primeNGConfig = primeNGConfig;\n    this.parentDialog = parentDialog;\n  }\n  ngAfterViewInit() {\n    this.loadChildComponent(this.childComponentType);\n    this.cd.detectChanges();\n  }\n  loadChildComponent(componentType) {\n    let componentFactory = this.componentFactoryResolver.resolveComponentFactory(componentType);\n    let viewContainerRef = this.insertionPoint?.viewContainerRef;\n    viewContainerRef?.clear();\n    this.componentRef = viewContainerRef?.createComponent(componentFactory);\n  }\n  moveOnTop() {\n    if (this.config.autoZIndex !== false) {\n      ZIndexUtils.set('modal', this.container, (this.config.baseZIndex || 0) + this.primeNGConfig.zIndex.modal);\n      this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n    }\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.wrapper = this.container.parentElement;\n        this.moveOnTop();\n        if (this.parent) {\n          this.unbindGlobalListeners();\n        }\n        this.bindGlobalListeners();\n        if (this.config.modal !== false) {\n          this.enableModality();\n        }\n        this.focus();\n        break;\n      case 'void':\n        if (this.wrapper && this.config.modal !== false) {\n          DomHandler.addClass(this.wrapper, 'p-component-overlay-leave');\n        }\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    if (event.toState === 'void') {\n      this.onContainerDestroy();\n      this.dialogRef.destroy();\n    }\n  }\n  onContainerDestroy() {\n    this.unbindGlobalListeners();\n    if (this.container && this.config.autoZIndex !== false) {\n      ZIndexUtils.clear(this.container);\n    }\n    if (this.config.modal !== false) {\n      this.disableModality();\n    }\n    this.container = null;\n  }\n  close() {\n    this.visible = false;\n    this.cd.markForCheck();\n  }\n  hide() {\n    if (this.dialogRef) {\n      this.dialogRef.close();\n    }\n  }\n  enableModality() {\n    if (this.config.closable !== false && this.config.dismissableMask) {\n      this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', event => {\n        if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n          this.hide();\n        }\n      });\n    }\n    if (this.config.modal !== false) {\n      DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n    }\n  }\n  disableModality() {\n    if (this.wrapper) {\n      if (this.config.dismissableMask) {\n        this.unbindMaskClickListener();\n      }\n      if (this.config.modal !== false) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (!this.cd.destroyed) {\n        this.cd.detectChanges();\n      }\n    }\n  }\n  onKeydown(event) {\n    // tab\n    if (event.which === 9) {\n      event.preventDefault();\n      let focusableElements = DomHandler.getFocusableElements(this.container);\n      if (focusableElements && focusableElements.length > 0) {\n        if (!focusableElements[0].ownerDocument.activeElement) {\n          focusableElements[0].focus();\n        } else {\n          let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n          if (event.shiftKey) {\n            if (focusedIndex == -1 || focusedIndex === 0) focusableElements[focusableElements.length - 1].focus();else focusableElements[focusedIndex - 1].focus();\n          } else {\n            if (focusedIndex == -1 || focusedIndex === focusableElements.length - 1) focusableElements[0].focus();else focusableElements[focusedIndex + 1].focus();\n          }\n        }\n      }\n    }\n  }\n  focus() {\n    const focusable = DomHandler.getFocusableElements(this.container);\n    if (focusable && focusable.length > 0) {\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => focusable[0].focus(), 5);\n      });\n    }\n  }\n  maximize() {\n    this.maximized = !this.maximized;\n    if (this.maximized) {\n      DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n    } else {\n      DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n    }\n    this.dialogRef.maximize({\n      maximized: this.maximized\n    });\n  }\n  initResize(event) {\n    if (this.config.resizable) {\n      if (!this.documentResizeListener) {\n        this.bindDocumentResizeListeners();\n      }\n      this.resizing = true;\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n      DomHandler.addClass(this.document.body, 'p-unselectable-text');\n      this.dialogRef.resizeInit(event);\n    }\n  }\n  onResize(event) {\n    if (this.resizing) {\n      let deltaX = event.pageX - this.lastPageX;\n      let deltaY = event.pageY - this.lastPageY;\n      let containerWidth = DomHandler.getOuterWidth(this.container);\n      let containerHeight = DomHandler.getOuterHeight(this.container);\n      let contentHeight = DomHandler.getOuterHeight(this.contentViewChild.nativeElement);\n      let newWidth = containerWidth + deltaX;\n      let newHeight = containerHeight + deltaY;\n      let minWidth = this.container.style.minWidth;\n      let minHeight = this.container.style.minHeight;\n      let offset = this.container.getBoundingClientRect();\n      let viewport = DomHandler.getViewport();\n      let hasBeenDragged = !parseInt(this.container.style.top) || !parseInt(this.container.style.left);\n      if (hasBeenDragged) {\n        newWidth += deltaX;\n        newHeight += deltaY;\n      }\n      if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n        this._style.width = newWidth + 'px';\n        this.container.style.width = this._style.width;\n      }\n      if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n        this.contentViewChild.nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n        if (this._style.height) {\n          this._style.height = newHeight + 'px';\n          this.container.style.height = this._style.height;\n        }\n      }\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n    }\n  }\n  resizeEnd(event) {\n    if (this.resizing) {\n      this.resizing = false;\n      DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n      this.dialogRef.resizeEnd(event);\n    }\n  }\n  initDrag(event) {\n    if (DomHandler.hasClass(event.target, 'p-dialog-header-icon') || DomHandler.hasClass(event.target.parentElement, 'p-dialog-header-icon')) {\n      return;\n    }\n    if (this.config.draggable) {\n      this.dragging = true;\n      this.lastPageX = event.pageX;\n      this.lastPageY = event.pageY;\n      this.container.style.margin = '0';\n      DomHandler.addClass(this.document.body, 'p-unselectable-text');\n      this.dialogRef.dragStart(event);\n    }\n  }\n  onDrag(event) {\n    if (this.dragging) {\n      let containerWidth = DomHandler.getOuterWidth(this.container);\n      let containerHeight = DomHandler.getOuterHeight(this.container);\n      let deltaX = event.pageX - this.lastPageX;\n      let deltaY = event.pageY - this.lastPageY;\n      let offset = this.container.getBoundingClientRect();\n      let leftPos = offset.left + deltaX;\n      let topPos = offset.top + deltaY;\n      let viewport = DomHandler.getViewport();\n      this.container.style.position = 'fixed';\n      if (this.keepInViewport) {\n        if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n          this._style.left = leftPos + 'px';\n          this.lastPageX = event.pageX;\n          this.container.style.left = leftPos + 'px';\n        }\n        if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n          this._style.top = topPos + 'px';\n          this.lastPageY = event.pageY;\n          this.container.style.top = topPos + 'px';\n        }\n      } else {\n        this.lastPageX = event.pageX;\n        this.container.style.left = leftPos + 'px';\n        this.lastPageY = event.pageY;\n        this.container.style.top = topPos + 'px';\n      }\n    }\n  }\n  endDrag(event) {\n    if (this.dragging) {\n      this.dragging = false;\n      DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n      this.dialogRef.dragEnd(event);\n      this.cd.detectChanges();\n    }\n  }\n  resetPosition() {\n    this.container.style.position = '';\n    this.container.style.left = '';\n    this.container.style.top = '';\n    this.container.style.margin = '';\n  }\n  bindDocumentDragListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        this.documentDragListener = this.renderer.listen(this.document, 'mousemove', this.onDrag.bind(this));\n      });\n    }\n  }\n  bindDocumentDragEndListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        this.documentDragEndListener = this.renderer.listen(this.document, 'mouseup', this.endDrag.bind(this));\n      });\n    }\n  }\n  unbindDocumentDragEndListener() {\n    if (this.documentDragEndListener) {\n      this.documentDragEndListener();\n      this.documentDragListener = null;\n    }\n  }\n  unbindDocumentDragListener() {\n    if (this.documentDragListener) {\n      this.documentDragListener();\n      this.documentDragListener = null;\n    }\n  }\n  bindDocumentResizeListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.zone.runOutsideAngular(() => {\n        this.documentResizeListener = this.renderer.listen(this.document, 'mousemove', this.onResize.bind(this));\n        this.documentResizeEndListener = this.renderer.listen(this.document, 'mouseup', this.resizeEnd.bind(this));\n      });\n    }\n  }\n  unbindDocumentResizeListeners() {\n    if (this.documentResizeListener && this.documentResizeEndListener) {\n      this.documentResizeListener();\n      this.documentResizeEndListener();\n      this.documentResizeListener = null;\n      this.documentResizeEndListener = null;\n    }\n  }\n  bindGlobalListeners() {\n    if (this.parentDialog) {\n      this.parentDialog.unbindDocumentKeydownListener();\n    }\n    this.bindDocumentKeydownListener();\n    if (this.config.closeOnEscape !== false && this.config.closable !== false) {\n      this.bindDocumentEscapeListener();\n    }\n    if (this.config.resizable) {\n      this.bindDocumentResizeListeners();\n    }\n    if (this.config.draggable) {\n      this.bindDocumentDragListener();\n      this.bindDocumentDragEndListener();\n    }\n  }\n  unbindGlobalListeners() {\n    this.unbindDocumentKeydownListener();\n    this.unbindDocumentEscapeListener();\n    this.unbindDocumentResizeListeners();\n    this.unbindDocumentDragListener();\n    this.unbindDocumentDragEndListener();\n    if (this.parentDialog) {\n      this.parentDialog.bindDocumentKeydownListener();\n    }\n  }\n  bindDocumentKeydownListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.documentKeydownListener) {\n        return;\n      } else {\n        this.zone.runOutsideAngular(() => {\n          this.documentKeydownListener = this.renderer.listen(this.document, 'keydown', this.onKeydown.bind(this));\n        });\n      }\n    }\n  }\n  unbindDocumentKeydownListener() {\n    if (this.documentKeydownListener) {\n      this.documentKeydownListener();\n      this.documentKeydownListener = null;\n    }\n  }\n  bindDocumentEscapeListener() {\n    const documentTarget = this.maskViewChild ? this.maskViewChild.nativeElement.ownerDocument : 'document';\n    this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n      if (event.which == 27) {\n        if (parseInt(this.container.style.zIndex) == ZIndexUtils.getCurrent()) {\n          this.hide();\n        }\n      }\n    });\n  }\n  unbindDocumentEscapeListener() {\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n      this.documentEscapeListener = null;\n    }\n  }\n  unbindMaskClickListener() {\n    if (this.maskClickListener) {\n      this.maskClickListener();\n      this.maskClickListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.onContainerDestroy();\n    if (this.componentRef) {\n      this.componentRef.destroy();\n    }\n  }\n  static ɵfac = function DynamicDialogComponent_Factory(t) {\n    return new (t || DynamicDialogComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DynamicDialogConfig), i0.ɵɵdirectiveInject(DynamicDialogRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.PrimeNGConfig), i0.ɵɵdirectiveInject(DynamicDialogComponent, 12));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: DynamicDialogComponent,\n    selectors: [[\"p-dynamicDialog\"]],\n    viewQuery: function DynamicDialogComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DynamicDialogContent, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.insertionPoint = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.maskViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    decls: 3,\n    vars: 14,\n    consts: [[3, \"ngClass\"], [\"mask\", \"\"], [\"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"class\", \"width\", \"height\", 4, \"ngIf\"], [\"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [\"class\", \"p-resizable-handle\", \"style\", \"z-index: 90;\", 3, \"mousedown\", 4, \"ngIf\"], [\"class\", \"p-dialog-header\", 3, \"mousedown\", 4, \"ngIf\"], [1, \"p-dialog-content\", 3, \"ngStyle\"], [\"content\", \"\"], [\"pDynamicDialogContent\", \"\"], [\"class\", \"p-dialog-footer\", 4, \"ngIf\"], [1, \"p-resizable-handle\", 2, \"z-index\", \"90\", 3, \"mousedown\"], [1, \"p-dialog-header\", 3, \"mousedown\"], [\"titlebar\", \"\"], [1, \"p-dialog-title\"], [1, \"p-dialog-header-icons\"], [\"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\"], [1, \"p-dialog-header-maximize-icon\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [\"type\", \"button\", 3, \"ngClass\", \"click\", \"keydown.enter\"], [1, \"p-dialog-footer\"]],\n    template: function DynamicDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵtemplate(2, DynamicDialogComponent_div_2_Template, 8, 23, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.config.maskStyleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunctionV(4, _c7, [ctx.config.modal !== false, ctx.position === \"left\", ctx.position === \"right\", ctx.position === \"top\", ctx.position === \"bottom\", ctx.position === \"topleft\" || ctx.position === \"top-left\", ctx.position === \"topright\" || ctx.position === \"top-right\", ctx.position === \"bottomleft\" || ctx.position === \"bottom-left\", ctx.position === \"bottomright\" || ctx.position === \"bottom-right\"]));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.visible);\n      }\n    },\n    dependencies: function () {\n      return [i4.NgClass, i4.NgIf, i4.NgStyle, WindowMaximizeIcon, WindowMinimizeIcon, TimesIcon, DynamicDialogContent];\n    },\n    styles: [\".p-dialog-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;pointer-events:none}.p-dialog-mask.p-component-overlay{pointer-events:auto}.p-dialog{display:flex;flex-direction:column;pointer-events:auto;max-height:90%;transform:scale(1);position:relative}.p-dialog-content{overflow-y:auto;flex-grow:1}.p-dialog-header{display:flex;align-items:center;justify-content:space-between;flex-shrink:0}.p-dialog-draggable .p-dialog-header{cursor:move}.p-dialog-footer{flex-shrink:0}.p-dialog .p-dialog-header-icons{display:flex;align-items:center}.p-dialog .p-dialog-header-icon{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-fluid .p-dialog-footer .p-button{width:auto}.p-dialog-top .p-dialog,.p-dialog-bottom .p-dialog,.p-dialog-left .p-dialog,.p-dialog-right .p-dialog,.p-dialog-top-left .p-dialog,.p-dialog-top-right .p-dialog,.p-dialog-bottom-left .p-dialog,.p-dialog-bottom-right .p-dialog{margin:.75rem;transform:translateZ(0)}.p-dialog-maximized{transition:none;transform:none;width:100vw!important;height:100vh!important;top:0!important;left:0!important;max-height:100%;height:100%}.p-dialog-maximized .p-dialog-content{flex-grow:1}.p-dialog-left{justify-content:flex-start}.p-dialog-right{justify-content:flex-end}.p-dialog-top{align-items:flex-start}.p-dialog-top-left{justify-content:flex-start;align-items:flex-start}.p-dialog-top-right{justify-content:flex-end;align-items:flex-start}.p-dialog-bottom{align-items:flex-end}.p-dialog-bottom-left{justify-content:flex-start;align-items:flex-end}.p-dialog-bottom-right{justify-content:flex-end;align-items:flex-end}.p-dialog .p-resizable-handle{position:absolute;font-size:.1px;display:block;cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.p-confirm-dialog .p-dialog-content{display:flex;align-items:center}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n    }\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DynamicDialogComponent, [{\n    type: Component,\n    args: [{\n      selector: 'p-dynamicDialog',\n      template: `\n        <div\n            #mask\n            [ngClass]=\"{\n                'p-dialog-mask': true,\n                'p-component-overlay p-component-overlay-enter p-dialog-mask-scrollblocker': config.modal !== false,\n                'p-dialog-left': position === 'left',\n                'p-dialog-right': position === 'right',\n                'p-dialog-top': position === 'top',\n                'p-dialog-bottom': position === 'bottom',\n                'p-dialog-top-left': position === 'topleft' || position === 'top-left',\n                'p-dialog-top-right': position === 'topright' || position === 'top-right',\n                'p-dialog-bottom-left': position === 'bottomleft' || position === 'bottom-left',\n                'p-dialog-bottom-right': position === 'bottomright' || position === 'bottom-right'\n            }\"\n            [class]=\"config.maskStyleClass\"\n        >\n            <div\n                #container\n                [ngClass]=\"{ 'p-dialog p-dynamic-dialog p-component': true, 'p-dialog-rtl': config.rtl, 'p-dialog-resizable': config.resizable, 'p-dialog-draggable': config.draggable, 'p-dialog-maximized': maximized }\"\n                [ngStyle]=\"config.style\"\n                [class]=\"config.styleClass\"\n                [@animation]=\"{ value: 'visible', params: { transform: transformOptions, transition: config.transitionOptions || '150ms cubic-bezier(0, 0, 0.2, 1)' } }\"\n                (@animation.start)=\"onAnimationStart($event)\"\n                (@animation.done)=\"onAnimationEnd($event)\"\n                role=\"dialog\"\n                *ngIf=\"visible\"\n                [style.width]=\"config.width\"\n                [style.height]=\"config.height\"\n            >\n                <div *ngIf=\"config.resizable\" class=\"p-resizable-handle\" style=\"z-index: 90;\" (mousedown)=\"initResize($event)\"></div>\n                <div #titlebar class=\"p-dialog-header\" (mousedown)=\"initDrag($event)\" *ngIf=\"config.showHeader === false ? false : true\">\n                    <span class=\"p-dialog-title\">{{ config.header }}</span>\n                    <div class=\"p-dialog-header-icons\">\n                        <button *ngIf=\"config.maximizable\" type=\"button\" [ngClass]=\"{ 'p-dialog-header-icon p-dialog-header-maximize p-link': true }\" (click)=\"maximize()\" (keydown.enter)=\"maximize()\" tabindex=\"-1\" pRipple>\n                            <span class=\"p-dialog-header-maximize-icon\" [ngClass]=\"maximized ? minimizeIcon : maximizeIcon\"></span>\n                            <WindowMaximizeIcon *ngIf=\"!maximized && !maximizeIcon\" [styleClass]=\"'p-dialog-header-maximize-icon'\" />\n                            <WindowMinimizeIcon *ngIf=\"maximized && !minimizeIcon\" [styleClass]=\"'p-dialog-header-maximize-icon'\" />\n                        </button>\n                        <button [ngClass]=\"'p-dialog-header-icon p-dialog-header-maximize p-link'\" type=\"button\" (click)=\"hide()\" (keydown.enter)=\"hide()\" *ngIf=\"config.closable !== false\">\n                            <TimesIcon [styleClass]=\"'p-dialog-header-close-icon'\" />\n                        </button>\n                    </div>\n                </div>\n                <div #content class=\"p-dialog-content\" [ngStyle]=\"config.contentStyle\">\n                    <ng-template pDynamicDialogContent></ng-template>\n                </div>\n                <div class=\"p-dialog-footer\" *ngIf=\"config.footer\">\n                    {{ config.footer }}\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-dialog-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;pointer-events:none}.p-dialog-mask.p-component-overlay{pointer-events:auto}.p-dialog{display:flex;flex-direction:column;pointer-events:auto;max-height:90%;transform:scale(1);position:relative}.p-dialog-content{overflow-y:auto;flex-grow:1}.p-dialog-header{display:flex;align-items:center;justify-content:space-between;flex-shrink:0}.p-dialog-draggable .p-dialog-header{cursor:move}.p-dialog-footer{flex-shrink:0}.p-dialog .p-dialog-header-icons{display:flex;align-items:center}.p-dialog .p-dialog-header-icon{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-fluid .p-dialog-footer .p-button{width:auto}.p-dialog-top .p-dialog,.p-dialog-bottom .p-dialog,.p-dialog-left .p-dialog,.p-dialog-right .p-dialog,.p-dialog-top-left .p-dialog,.p-dialog-top-right .p-dialog,.p-dialog-bottom-left .p-dialog,.p-dialog-bottom-right .p-dialog{margin:.75rem;transform:translateZ(0)}.p-dialog-maximized{transition:none;transform:none;width:100vw!important;height:100vh!important;top:0!important;left:0!important;max-height:100%;height:100%}.p-dialog-maximized .p-dialog-content{flex-grow:1}.p-dialog-left{justify-content:flex-start}.p-dialog-right{justify-content:flex-end}.p-dialog-top{align-items:flex-start}.p-dialog-top-left{justify-content:flex-start;align-items:flex-start}.p-dialog-top-right{justify-content:flex-end;align-items:flex-start}.p-dialog-bottom{align-items:flex-end}.p-dialog-bottom-left{justify-content:flex-start;align-items:flex-end}.p-dialog-bottom-right{justify-content:flex-end;align-items:flex-end}.p-dialog .p-resizable-handle{position:absolute;font-size:.1px;display:block;cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.p-confirm-dialog .p-dialog-content{display:flex;align-items:center}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DynamicDialogConfig\n    }, {\n      type: DynamicDialogRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.PrimeNGConfig\n    }, {\n      type: DynamicDialogComponent,\n      decorators: [{\n        type: SkipSelf\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    insertionPoint: [{\n      type: ViewChild,\n      args: [DynamicDialogContent]\n    }],\n    maskViewChild: [{\n      type: ViewChild,\n      args: ['mask']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    headerViewChild: [{\n      type: ViewChild,\n      args: ['titlebar']\n    }]\n  });\n})();\nclass DynamicDialogModule {\n  static ɵfac = function DynamicDialogModule_Factory(t) {\n    return new (t || DynamicDialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DynamicDialogModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, WindowMaximizeIcon, WindowMinimizeIcon, TimesIcon, SharedModule, SharedModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DynamicDialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, WindowMaximizeIcon, WindowMinimizeIcon, TimesIcon, SharedModule],\n      declarations: [DynamicDialogComponent, DynamicDialogContent],\n      exports: [SharedModule]\n    }]\n  }], null, null);\n})();\nclass DynamicDialogInjector {\n  _parentInjector;\n  _additionalTokens;\n  constructor(_parentInjector, _additionalTokens) {\n    this._parentInjector = _parentInjector;\n    this._additionalTokens = _additionalTokens;\n  }\n  get(token, notFoundValue, flags) {\n    const value = this._additionalTokens.get(token);\n    if (value) return value;\n    return this._parentInjector.get(token, notFoundValue);\n  }\n}\n\n/**\n * Dynamic Dialog component methods.\n * @group Service\n */\nclass DialogService {\n  componentFactoryResolver;\n  appRef;\n  injector;\n  document;\n  dialogComponentRefMap = new Map();\n  constructor(componentFactoryResolver, appRef, injector, document) {\n    this.componentFactoryResolver = componentFactoryResolver;\n    this.appRef = appRef;\n    this.injector = injector;\n    this.document = document;\n  }\n  /**\n   * Displays the dialog using the dynamic dialog object options.\n   * @param {*} componentType - Dynamic component for content template.\n   * @param {DynamicDialogConfig} config - DynamicDialog object.\n   * @returns {DynamicDialogRef} DynamicDialog instance.\n   * @group Method\n   */\n  open(componentType, config) {\n    const dialogRef = this.appendDialogComponentToBody(config);\n    this.dialogComponentRefMap.get(dialogRef).instance.childComponentType = componentType;\n    return dialogRef;\n  }\n  appendDialogComponentToBody(config) {\n    const map = new WeakMap();\n    map.set(DynamicDialogConfig, config);\n    const dialogRef = new DynamicDialogRef();\n    map.set(DynamicDialogRef, dialogRef);\n    const sub = dialogRef.onClose.subscribe(() => {\n      this.dialogComponentRefMap.get(dialogRef).instance.close();\n    });\n    const destroySub = dialogRef.onDestroy.subscribe(() => {\n      this.removeDialogComponentFromBody(dialogRef);\n      destroySub.unsubscribe();\n      sub.unsubscribe();\n    });\n    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(DynamicDialogComponent);\n    const componentRef = componentFactory.create(new DynamicDialogInjector(this.injector, map));\n    this.appRef.attachView(componentRef.hostView);\n    const domElem = componentRef.hostView.rootNodes[0];\n    this.document.body.appendChild(domElem);\n    this.dialogComponentRefMap.set(dialogRef, componentRef);\n    return dialogRef;\n  }\n  removeDialogComponentFromBody(dialogRef) {\n    if (!dialogRef || !this.dialogComponentRefMap.has(dialogRef)) {\n      return;\n    }\n    const dialogComponentRef = this.dialogComponentRefMap.get(dialogRef);\n    this.appRef.detachView(dialogComponentRef.hostView);\n    dialogComponentRef.destroy();\n    this.dialogComponentRefMap.delete(dialogRef);\n  }\n  static ɵfac = function DialogService_Factory(t) {\n    return new (t || DialogService)(i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DialogService,\n    factory: DialogService.ɵfac\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: i0.Injector\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DialogService, DynamicDialogComponent, DynamicDialogConfig, DynamicDialogInjector, DynamicDialogModule, DynamicDialogRef };\n//# sourceMappingURL=primeng-dynamicdialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}