{"ast": null, "code": "import { CommonModule, DecimalPipe, NgOptimizedImage } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { CartProductDetailsComponent } from './components/cart-product-details/cart-product-details.component';\nimport { CheckoutCardComponent } from './components/checkout-card/checkout-card.component';\nimport { EmptyCartComponent } from './components/empty-cart/empty-cart.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { OptOutModalComponent } from \"@pages/cart/components/modals/opt-out-modal/opt-out-modal.component\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport { GAImpressionDirective } from '@core/directives/ga-impression.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class CartModule {\n  static #_ = this.ɵfac = function CartModule_Factory(t) {\n    return new (t || CartModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CartModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), RippleModule, TranslateModule, ButtonModule, SharedModule, EmptyScreenComponent, BackButtonComponent, DialogModule, DropdownModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CartModule, {\n    declarations: [IndexComponent, CartProductDetailsComponent, CheckoutCardComponent, EmptyCartComponent, OptOutModalComponent],\n    imports: [CommonModule, i1.RouterModule, RippleModule, TranslateModule, ButtonModule, SharedModule, DecimalPipe, EmptyScreenComponent, BackButtonComponent, GAImpressionDirective, DialogModule, DropdownModule, NgOptimizedImage]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DecimalPipe", "NgOptimizedImage", "IndexComponent", "RouterModule", "routes", "RippleModule", "CartProductDetailsComponent", "CheckoutCardComponent", "EmptyCartComponent", "TranslateModule", "ButtonModule", "SharedModule", "OptOutModalComponent", "DialogModule", "DropdownModule", "EmptyScreenComponent", "BackButtonComponent", "GAImpressionDirective", "CartModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\cart\\cart.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\r\nimport {CommonModule, DecimalPipe, NgOptimizedImage} from '@angular/common';\r\nimport {IndexComponent} from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {RippleModule} from \"primeng/ripple\";\r\nimport {CartProductDetailsComponent} from './components/cart-product-details/cart-product-details.component';\r\nimport {CheckoutCardComponent} from './components/checkout-card/checkout-card.component';\r\nimport {EmptyCartComponent} from './components/empty-cart/empty-cart.component';\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {ButtonModule} from \"primeng/button\";\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\nimport {OptOutModalComponent} from \"@pages/cart/components/modals/opt-out-modal/opt-out-modal.component\";\r\nimport {DialogModule} from \"primeng/dialog\";\r\nimport {DropdownModule} from \"primeng/dropdown\";\r\nimport {EmptyScreenComponent} from \"@shared/components/empty-screen/empty-screen.component\";\r\nimport {BackButtonComponent} from \"@shared/components/back-button/back-button.component\";\r\nimport { GAImpressionDirective } from '@core/directives/ga-impression.directive';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    CartProductDetailsComponent,\r\n    CheckoutCardComponent,\r\n    EmptyCartComponent,\r\n    OptOutModalComponent\r\n  ],\r\n    imports: [\r\n        CommonModule,\r\n        RouterModule.forChild(routes),\r\n        RippleModule,\r\n        TranslateModule,\r\n        ButtonModule,\r\n        SharedModule,\r\n        DecimalPipe,\r\n        EmptyScreenComponent,\r\n        BackButtonComponent,\r\n        GAImpressionDirective,\r\n        DialogModule,\r\n        DropdownModule,\r\n        NgOptimizedImage,\r\n    ]\r\n})\r\nexport class CartModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,QAAO,iBAAiB;AAC3E,SAAQC,cAAc,QAAO,oCAAoC;AACjE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,2BAA2B,QAAO,kEAAkE;AAC5G,SAAQC,qBAAqB,QAAO,oDAAoD;AACxF,SAAQC,kBAAkB,QAAO,8CAA8C;AAC/E,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAAQC,oBAAoB,QAAO,qEAAqE;AACxG,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,oBAAoB,QAAO,wDAAwD;AAC3F,SAAQC,mBAAmB,QAAO,sDAAsD;AACxF,SAASC,qBAAqB,QAAQ,0CAA0C;;;AA4BhF,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAfftB,YAAY,EACZI,YAAY,CAACmB,QAAQ,CAAClB,MAAM,CAAC,EAC7BC,YAAY,EACZI,eAAe,EACfC,YAAY,EACZC,YAAY,EAEZI,oBAAoB,EACpBC,mBAAmB,EAEnBH,YAAY,EACZC,cAAc;EAAA;;;2EAITI,UAAU;IAAAK,YAAA,GAtBnBrB,cAAc,EACdI,2BAA2B,EAC3BC,qBAAqB,EACrBC,kBAAkB,EAClBI,oBAAoB;IAAAY,OAAA,GAGhBzB,YAAY,EAAA0B,EAAA,CAAAtB,YAAA,EAEZE,YAAY,EACZI,eAAe,EACfC,YAAY,EACZC,YAAY,EACZX,WAAW,EACXe,oBAAoB,EACpBC,mBAAmB,EACnBC,qBAAqB,EACrBJ,YAAY,EACZC,cAAc,EACdb,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}