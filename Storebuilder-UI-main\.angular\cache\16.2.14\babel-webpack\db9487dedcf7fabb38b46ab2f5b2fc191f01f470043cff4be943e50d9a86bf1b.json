{"ast": null, "code": "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n  websocket: WS,\n  webtransport: WT,\n  polling: Polling\n};", "map": {"version": 3, "names": ["Polling", "WS", "WT", "transports", "websocket", "webtransport", "polling"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/engine.io-client/build/esm/transports/index.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,EAAE,QAAQ,gBAAgB;AACnC,SAASC,EAAE,QAAQ,mBAAmB;AACtC,OAAO,MAAMC,UAAU,GAAG;EACtBC,SAAS,EAAEH,EAAE;EACbI,YAAY,EAAEH,EAAE;EAChBI,OAAO,EAAEN;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}