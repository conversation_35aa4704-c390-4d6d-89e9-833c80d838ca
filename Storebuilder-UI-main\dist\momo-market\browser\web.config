<?xml version="1.0" encoding="utf-8"?>
<configuration>

<system.webServer>
  <rewrite>
    <rules>
      <rule name="Angular Routes" stopProcessing="true">
        <match url=".*" />
        <conditions logicalGrouping="MatchAll">
          <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
        </conditions>
        <action type="Rewrite" url="./index.html" />
      </rule>
	  <rule name="HTTPS Redirect" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions>
                        <add input="{HTTPS}" pattern="^OFF$" />
                    </conditions>
               <action type="Redirect" url="https://{HTTP_HOST}{REQUEST_URI}" appendQueryString="false" />
	  </rule>
    </rules>
  </rewrite>


       <security>
      <requestFiltering removeServerHeader ="true" />
    </security>
    <httpProtocol>
      <customHeaders>
    <remove name="X-Powered-By" />
    <remove name="ETag" />
		    <add name="Strict-Transport-Security" value="max-age=31536000"/>
        <add name="referrer-policy" value="strict-origin-when-cross-origin"/>
        <add name="x-content-type-options" value="nosniff"/>
        <add name="x-frame-options" value="DENY"/>
        <add name="X-Permitted-Cross-Domain-Policies" value="none"/>
        <add name="x-xss-protection" value="1; mode=block"/>
        <add name="Expect-CT" value="max-age=0, enforce"/>
        <add name="Cache-Control" value="no-cache"/>

		<add name="Permissions-Policy" value="geolocation=(self), microphone=()"/>
        <add name="Feature-Policy" value="accelerometer 'self';ambient-light-sensor 'self';autoplay 'self';battery 'self';camera 'self'; display-capture 'self';document-domain 'self';encrypted-media 'self';execution-while-not-rendered 'self';execution-while-out-of-viewport 'self';gyroscope 'self';magnetometer 'self';microphone 'none';geolocation  'none';midi 'self';navigation-override 'self';payment 'self';picture-in-picture 'self';publickey-credentials-get 'self';sync-xhr 'self';usb 'self';wake-lock 'self';xr-spatial-tracking 'self';"/>
		      <add name="Public-Key-Pins" value="pin-sha256=&quot;base64+primary==&quot;; pin-sha256=&quot;base64+backup==&quot;; max-age=5184000; includeSubDomains" />

       <add name="Content-Security-Policy" value="base-uri 'self';block-all-mixed-content;font-src 'self' fonts.gstatic.com;form-action 'self';frame-ancestors 'self';manifest-src 'self';media-src 'self';object-src 'self';upgrade-insecure-requests;worker-src 'self';"/>
      </customHeaders>
    </httpProtocol>

</system.webServer>
</configuration>
