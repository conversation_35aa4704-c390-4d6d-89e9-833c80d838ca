
.p-tree{
  border: none;
  .p-treenode-label{
    color: var(--character-primary-85, rgba(0, 0, 0, 0.85));
    font-family: var(--regular-font);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    letter-spacing: 0.1px;
  }
  .p-treenode-children{
    color: var(--character-primary-85, rgba(0, 0, 0, 0.85));
    font-family: var(--regular-font);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    letter-spacing: 0.4px;
  }
  .p-tree-toggler:enabled:hover{
    background: transparent !important;
  }
  .p-tree-toggler:focus{
    box-shadow: none !important;
  }
  //.custom-tree .p-treenode-content .p-treenode-toggler {
  //  background: none; /* Remove default background */
  //}
  //
  //.custom-tree .p-treenode-content .p-treenode-toggler .pi-chevron-down::before,
  //.custom-tree .p-treenode-content .p-treenode-toggler .pi-chevron-right::before {
  //  content: "\f054"; /* Your custom icon's unicode or path */
  //  font-family: 'FontAwesome'; /* Replace with your custom font-family if needed */
  //  font-size: 18px; /* Adjust as needed */
  //  color: red; /* Customize the color */
  //}
  //
  ///* Additional styling for the icon itself, if necessary */
  //.custom-tree .p-treenode-content .p-treenode-toggler i {
  //  display: none; /* Hide the default icon */
  //}
}
