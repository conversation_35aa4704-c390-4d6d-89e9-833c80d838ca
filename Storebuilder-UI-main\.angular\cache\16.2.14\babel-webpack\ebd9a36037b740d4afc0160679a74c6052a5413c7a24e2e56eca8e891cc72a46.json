{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ngx-translate/core\";\nfunction SectionComponent_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function SectionComponent_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToCategories());\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelement(2, \"img\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(1, 1, \"buttons.viewAll\"));\n  }\n}\nconst _c0 = [\"*\"];\nexport class SectionComponent {\n  constructor(router, translate) {\n    this.router = router;\n    this.translate = translate;\n    this.title = '';\n  }\n  ngOnInit() {}\n  ngOnChanges(change) {}\n  goToCategories() {\n    if (this.featureProduct) {\n      this.router.navigate([`/category/${this.featureProduct}&${this.topNumber}&${this.title}`]);\n    } else {\n      this.router.navigate([`/category/${this.categoryID}`]);\n    }\n  }\n  translated(name) {\n    let transData;\n    name = name.replace(/\\s/g, '');\n    this.translate.get('featureType').subscribe(data => {\n      transData = data;\n      // return data[name.toLowerCase()] ? data[name.toLowerCase()] : '';\n    });\n\n    return transData[name.toLowerCase()] ? transData[name.toLowerCase()] : name;\n  }\n  static #_ = this.ɵfac = function SectionComponent_Factory(t) {\n    return new (t || SectionComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SectionComponent,\n    selectors: [[\"app-section\"]],\n    inputs: {\n      title: \"title\",\n      categoryID: \"categoryID\",\n      featureProduct: \"featureProduct\",\n      topNumber: \"topNumber\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 7,\n    vars: 2,\n    consts: [[1, \"section-block\"], [1, \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"my-0\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-outlined view-all-btn\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"view-all-btn\", 3, \"label\", \"click\"], [\"src\", \"assets/images/payment-icons/a-r.svg\", 1, \"arw\"]],\n    template: function SectionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, SectionComponent_button_4_Template, 3, 3, \"button\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵprojection(6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"\", ctx.translated(ctx.title), \" \");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.categoryID !== 0);\n      }\n    },\n    styles: [\".section-block[_ngcontent-%COMP%] {\\n  min-height: 261px;\\n  height: auto;\\n  width: 100%;\\n  padding: 20px;\\n  background: #fafafa 0% 0% no-repeat padding-box;\\n  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1607843137);\\n  border-radius: 10px;\\n  opacity: 1;\\n}\\n\\n.arw[_ngcontent-%COMP%] {\\n  display: flex;\\n  position: absolute;\\n  left: 100px;\\n  width: 24px;\\n  height: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvc2VjdGlvbi9zZWN0aW9uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7RUFDQSwrQ0FBQTtFQUNBLG1EQUFBO0VBQ0EsbUJBQUE7RUFDQSxVQUFBO0FBQ0Y7O0FBQ0E7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFFRiIsInNvdXJjZXNDb250ZW50IjpbIi5zZWN0aW9uLWJsb2NrIHtcclxuICBtaW4taGVpZ2h0OiAyNjFweDtcclxuICBoZWlnaHQ6IGF1dG87XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBiYWNrZ3JvdW5kOiAjZmFmYWZhIDAlIDAlIG5vLXJlcGVhdCBwYWRkaW5nLWJveDtcclxuICBib3gtc2hhZG93OiAwcHggMHB4IDVweCAjMDAwMDAwMjk7XHJcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICBvcGFjaXR5OiAxO1xyXG59XHJcbi5hcncge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIGxlZnQ6IDEwMHB4O1xyXG4gIHdpZHRoOiAyNHB4O1xyXG4gIGhlaWdodDogMTZweDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "SectionComponent_button_4_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goToCategories", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpipeBind1", "SectionComponent", "constructor", "router", "translate", "title", "ngOnInit", "ngOnChanges", "change", "featureProduct", "navigate", "topNumber", "categoryID", "translated", "name", "transData", "replace", "get", "subscribe", "data", "toLowerCase", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "TranslateService", "_2", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "SectionComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵtemplate", "SectionComponent_button_4_Template", "ɵɵprojection", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\section\\section.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\section\\section.component.html"], "sourcesContent": ["import { Component, Input, OnInit, SimpleChanges } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n@Component({\r\n  selector: 'app-section',\r\n  templateUrl: './section.component.html',\r\n  styleUrls: ['./section.component.scss'],\r\n})\r\nexport class SectionComponent implements OnInit {\r\n  constructor(private router: Router, private translate: TranslateService) {}\r\n  @Input() title: string = '';\r\n  @Input() categoryID: number | undefined;\r\n  @Input() featureProduct: number | undefined;\r\n  @Input() topNumber: number | undefined;\r\n\r\n  ngOnInit(): void {}\r\n\r\n  ngOnChanges(change: SimpleChanges) {}\r\n\r\n  goToCategories() {\r\n    if (this.featureProduct) {\r\n      this.router.navigate([\r\n        `/category/${this.featureProduct}&${this.topNumber}&${this.title}`,\r\n      ]);\r\n    } else {\r\n      this.router.navigate([`/category/${this.categoryID}`]);\r\n    }\r\n  }\r\n  translated(name: string) {\r\n    let transData: any;\r\n    name = name.replace(/\\s/g, '');\r\n\r\n    this.translate.get('featureType').subscribe((data: any) => {\r\n      transData = data;\r\n      // return data[name.toLowerCase()] ? data[name.toLowerCase()] : '';\r\n    });\r\n    return transData[name.toLowerCase()] ? transData[name.toLowerCase()] : name;\r\n  }\r\n}\r\n", "<section class=\"section-block\">\r\n  <div class=\"flex flex-row justify-content-between\">\r\n    <h2 class=\"my-0\">{{translated(title) }} </h2>\r\n    <button *ngIf=\"categoryID !== 0\" pButton type=\"button\" [label]=\"'buttons.viewAll' | translate\"\r\n      class=\"p-button-outlined view-all-btn\" (click)=\"goToCategories()\">\r\n      <img class=\"arw\" src=\"assets/images/payment-icons/a-r.svg\" />\r\n    </button>\r\n  </div>\r\n  <div class=\"mt-3\">\r\n    <ng-content></ng-content>\r\n  </div>\r\n</section>\r\n"], "mappings": ";;;;;;ICGIA,EAAA,CAAAC,cAAA,gBACoE;IAA3BD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;;IACjET,EAAA,CAAAU,SAAA,aAA6D;IAC/DV,EAAA,CAAAW,YAAA,EAAS;;;IAH8CX,EAAA,CAAAY,UAAA,UAAAZ,EAAA,CAAAa,WAAA,0BAAuC;;;;ADMlG,OAAM,MAAOC,gBAAgB;EAC3BC,YAAoBC,MAAc,EAAUC,SAA2B;IAAnD,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,SAAS,GAATA,SAAS;IAC5C,KAAAC,KAAK,GAAW,EAAE;EAD+C;EAM1EC,QAAQA,CAAA,GAAU;EAElBC,WAAWA,CAACC,MAAqB,GAAG;EAEpCZ,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACa,cAAc,EAAE;MACvB,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CACnB,aAAa,IAAI,CAACD,cAAc,IAAI,IAAI,CAACE,SAAS,IAAI,IAAI,CAACN,KAAK,EAAE,CACnE,CAAC;KACH,MAAM;MACL,IAAI,CAACF,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,IAAI,CAACE,UAAU,EAAE,CAAC,CAAC;;EAE1D;EACAC,UAAUA,CAACC,IAAY;IACrB,IAAIC,SAAc;IAClBD,IAAI,GAAGA,IAAI,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAE9B,IAAI,CAACZ,SAAS,CAACa,GAAG,CAAC,aAAa,CAAC,CAACC,SAAS,CAAEC,IAAS,IAAI;MACxDJ,SAAS,GAAGI,IAAI;MAChB;IACF,CAAC,CAAC;;IACF,OAAOJ,SAAS,CAACD,IAAI,CAACM,WAAW,EAAE,CAAC,GAAGL,SAAS,CAACD,IAAI,CAACM,WAAW,EAAE,CAAC,GAAGN,IAAI;EAC7E;EAAC,QAAAO,CAAA,G;qBA7BUpB,gBAAgB,EAAAd,EAAA,CAAAmC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArC,EAAA,CAAAmC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB1B,gBAAgB;IAAA2B,SAAA;IAAAC,MAAA;MAAAxB,KAAA;MAAAO,UAAA;MAAAH,cAAA;MAAAE,SAAA;IAAA;IAAAmB,QAAA,GAAA3C,EAAA,CAAA4C,oBAAA;IAAAC,kBAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCT7BpD,EAAA,CAAAC,cAAA,iBAA+B;QAEVD,EAAA,CAAAsD,MAAA,GAAuB;QAAAtD,EAAA,CAAAW,YAAA,EAAK;QAC7CX,EAAA,CAAAuD,UAAA,IAAAC,kCAAA,oBAGS;QACXxD,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,aAAkB;QAChBD,EAAA,CAAAyD,YAAA,GAAyB;QAC3BzD,EAAA,CAAAW,YAAA,EAAM;;;QARaX,EAAA,CAAA0D,SAAA,GAAuB;QAAvB1D,EAAA,CAAA2D,kBAAA,KAAAN,GAAA,CAAA3B,UAAA,CAAA2B,GAAA,CAAAnC,KAAA,OAAuB;QAC/BlB,EAAA,CAAA0D,SAAA,GAAsB;QAAtB1D,EAAA,CAAAY,UAAA,SAAAyC,GAAA,CAAA5B,UAAA,OAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}