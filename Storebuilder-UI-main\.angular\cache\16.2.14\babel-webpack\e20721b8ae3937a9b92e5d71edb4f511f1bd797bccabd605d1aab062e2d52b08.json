{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nexport class LoaderDotsComponent {\n  constructor(loaderService, ref) {\n    this.loaderService = loaderService;\n    this.ref = ref;\n    this.loaderService.isLoading.subscribe(res => {\n      this.isLoading = res;\n      this.ref.markForCheck();\n      this.ref.detectChanges();\n    });\n  }\n  ngOnInit() {\n    /**/\n  }\n  static #_ = this.ɵfac = function LoaderDotsComponent_Factory(t) {\n    return new (t || LoaderDotsComponent)(i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoaderDotsComponent,\n    selectors: [[\"app-loader-dots\"]],\n    decls: 1,\n    vars: 0,\n    consts: [[1, \"dot-floating\"]],\n    template: function LoaderDotsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n    },\n    styles: [\".dot-floating[_ngcontent-%COMP%] {\\n  width: 8px;\\n  aspect-ratio: 1;\\n  border-radius: 50%;\\n  clip-path: inset(-45px);\\n  box-shadow: -90px 15px #1a445e, -60px 15px #1a445e, -30px 15px #1a445e, 0px 15px #1a445e, 30px 15px #1a445e, 60px 15px #1a445e;\\n  transform: translateY(-15px);\\n  animation: _ngcontent-%COMP%_l19 1s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_l19 {\\n  0% {\\n    box-shadow: -90px 15px #1a445e, -60px 15px #1a445e, -30px 15px #1a445e, 0px 15px #1a445e, 30px 15px #1a445e, 60px 15px #1a445e;\\n  }\\n  16.67% {\\n    box-shadow: -60px 15px #1a445e, -30px 15px #1a445e, 0px 15px #1a445e, 30px 15px #1a445e, 60px 15px #1a445e, 90px 15px #1a445e;\\n  }\\n  33.33% {\\n    box-shadow: -30px 15px #1a445e, 0px 15px #1a445e, 30px 15px #1a445e, 60px 15px #1a445e, 90px 15px #1a445e, 120px 15px #1a445e;\\n  }\\n  50% {\\n    box-shadow: 0px 15px #1a445e, 30px 15px #1a445e, 60px 15px #1a445e, 90px 15px #1a445e, 120px 15px #1a445e, 150px 15px #1a445e;\\n  }\\n  66.67% {\\n    box-shadow: 30px 15px #1a445e, 60px 15px #1a445e, 90px 15px #1a445e, 120px 15px #1a445e, 150px 15px #1a445e, 180px 15px #1a445e;\\n  }\\n  83.33% {\\n    box-shadow: 60px 15px #1a445e, 90px 15px #1a445e, 120px 15px #1a445e, 150px 15px #1a445e, 180px 15px #1a445e, 210px 15px #1a445e;\\n  }\\n  100% {\\n    box-shadow: 90px 15px #1a445e, 120px 15px #1a445e, 150px 15px #1a445e, 180px 15px #1a445e, 210px 15px #1a445e, 240px 15px #1a445e;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["LoaderDotsComponent", "constructor", "loaderService", "ref", "isLoading", "subscribe", "res", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "ngOnInit", "_", "i0", "ɵɵdirectiveInject", "i1", "LoaderService", "ChangeDetectorRef", "_2", "selectors", "decls", "vars", "consts", "template", "LoaderDotsComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\loader-dots\\loader-dots.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\loader-dots\\loader-dots.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, OnInit} from '@angular/core';\r\nimport {LoaderService} from '@core/services';\r\n\r\n@Component({\r\n  selector: 'app-loader-dots',\r\n  templateUrl: './loader-dots.component.html',\r\n  styleUrls: ['./loader-dots.component.scss']\r\n})\r\nexport class LoaderDotsComponent implements OnInit {\r\n  isLoading: any;\r\n\r\n  constructor(private loaderService: LoaderService, private ref: ChangeDetectorRef,) {\r\n    this.loaderService.isLoading.subscribe((res => {\r\n\r\n      this.isLoading = res;\r\n      this.ref.markForCheck();\r\n      this.ref.detectChanges();\r\n    }));\r\n\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n}\r\n", "<div class=\"dot-floating\"></div>\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,mBAAmB;EAG9BC,YAAoBC,aAA4B,EAAUC,GAAsB;IAA5D,KAAAD,aAAa,GAAbA,aAAa;IAAyB,KAAAC,GAAG,GAAHA,GAAG;IAC3D,IAAI,CAACD,aAAa,CAACE,SAAS,CAACC,SAAS,CAAEC,GAAG,IAAG;MAE5C,IAAI,CAACF,SAAS,GAAGE,GAAG;MACpB,IAAI,CAACH,GAAG,CAACI,YAAY,EAAE;MACvB,IAAI,CAACJ,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAE,CAAC;EAEL;EAEAC,QAAQA,CAAA;IACN;EAAA;EACD,QAAAC,CAAA,G;qBAfUV,mBAAmB,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBhB,mBAAmB;IAAAiB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRhCZ,EAAA,CAAAc,SAAA,aAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}