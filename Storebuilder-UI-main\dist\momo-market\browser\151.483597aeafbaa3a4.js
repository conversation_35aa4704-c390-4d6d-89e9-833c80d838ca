"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[151],{6151:(lt,m,c)=>{c.r(m),c.d(m,{CategoryProductsModule:()=>K});var u=c(6814),p=c(6075),e=c(5879),f=c(553),y=c(5662),_=c(906),l=c(864),v=c(5219),h=c(6663),I=c(459),b=c(9147),S=c(7875),C=c(7680),x=c(2655),P=c(707);const T=function(){return["/"]};let N=(()=>{class a{static \u0275fac=function(i){return new(i||a)};static \u0275cmp=e.Xpm({type:a,selectors:[["app-category-not-found"]],decls:8,vars:8,consts:[[1,"empty-cart","align-items-center"],[1,"center","justify-content-center"],[1,"d-flex","justify-content-center","m-0","mx-4","cartText"],[1,"d-flex","align-items-center","justify-content-center"],["pButton","","type","button",1,"margin-x-30","width-100","second-btn",3,"routerLink","label"]],template:function(i,r){1&i&&(e.TgZ(0,"section",0)(1,"div",1)(2,"p",2),e._uU(3),e.ALo(4,"translate"),e.qZA(),e.TgZ(5,"div",3),e._UZ(6,"button",4),e.ALo(7,"translate"),e.qZA()()()),2&i&&(e.xp6(3),e.hij(" ",e.lcZ(4,3,"categories.productNotFound")," "),e.xp6(3),e.s9C("label",e.lcZ(7,5,"categories.goToHomePage")),e.Q6J("routerLink",e.DdM(7,T)))},dependencies:[p.rH,P.Hq,h.X$],styles:[".empty-cart[_ngcontent-%COMP%]{height:640px;position:relative}.center[_ngcontent-%COMP%]{margin:0;position:absolute;top:50%;left:50%;transform:translate(-50%,-25%)}.cartText[_ngcontent-%COMP%]{width:302px;font-size:28px;font-weight:700;margin-bottom:15px!important;margin-top:25px!important;font-family:var(--medium-font)!important}.second-btn[_ngcontent-%COMP%]{text-transform:uppercase;font-family:var(--medium-font)!important;font-weight:500!important;width:293px!important;font-size:14px}.cart-wait[_ngcontent-%COMP%]{font-size:15px;font-weight:300;color:#a3a3a3!important;font-family:var(--regular-font)!important}@media screen and (max-width: 768px){.empty-cart[_ngcontent-%COMP%]{height:300px}}"]})}return a})();function w(a,n){if(1&a&&(e.TgZ(0,"div",8),e._UZ(1,"img",9),e.qZA()),2&a){const t=e.oxw(3);e.xp6(1),e.Q6J("src",t.getBannerImages(null==t.categoryBanner?null:t.categoryBanner.desktopBanner),e.LSH)}}function L(a,n){if(1&a&&(e.TgZ(0,"a",18),e._UZ(1,"app-mtn-product-card",19),e.qZA()),2&a){const t=e.oxw().$implicit,i=e.oxw(5);e.ekj("isLayoutTemplate",!i.isLayoutTemplate),e.xp6(1),e.Q6J("currency",i.currency)("product",t)("categoryName",i.categoryName)}}function k(a,n){if(1&a&&(e.ynx(0),e.YNc(1,L,2,5,"a",17),e.BQk()),2&a){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",!t.isDisable)}}function B(a,n){if(1&a&&(e.TgZ(0,"div",15),e.YNc(1,k,2,1,"ng-container",16),e.qZA()),2&a){const t=e.oxw(4);e.xp6(1),e.Q6J("ngForOf",t.products)}}function D(a,n){1&a&&(e.TgZ(0,"div",20),e._UZ(1,"p-progressSpinner"),e.qZA())}function O(a,n){if(1&a&&(e.TgZ(0,"div")(1,"div",10)(2,"div",11)(3,"div",12),e._uU(4),e.qZA()(),e.YNc(5,B,2,1,"div",13),e.YNc(6,D,2,0,"div",14),e.qZA()()),2&a){const t=e.oxw(3);e.xp6(4),e.hij(" ",t.category?t.category.categoryName:t.categoryName," "),e.xp6(1),e.Q6J("ngIf",t.products&&t.products.length>0),e.xp6(1),e.Q6J("ngIf",t.showProductSpinner)}}const A=function(a,n){return{"category-products-page":a,"hidden-navbar":n}},M=function(a,n){return{breadcrumb:a,hiddenNavbarBreadcrum:n}};function Z(a,n){if(1&a&&(e.TgZ(0,"section",4)(1,"div",5),e._UZ(2,"p-breadcrumb",6),e.qZA(),e.YNc(3,w,2,1,"div",7),e.YNc(4,O,7,3,"div",3),e.qZA()),2&a){const t=e.oxw(2);e.Q6J("ngClass",e.WLB(6,A,null==t.navbarData?null:t.navbarData.isActive,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(1),e.Q6J("ngClass",e.WLB(9,M,null==t.navbarData?null:t.navbarData.isActive,!(null!=t.navbarData&&t.navbarData.isActive))),e.xp6(1),e.Q6J("home",t.home)("model",t.breadItems),e.xp6(1),e.Q6J("ngIf",(null==t.categoryBanner?null:t.categoryBanner.isBanner)&&(null==t.categoryBanner?null:t.categoryBanner.desktopBanner)),e.xp6(1),e.Q6J("ngIf",t.products&&t.products.length)}}function F(a,n){1&a&&e._UZ(0,"app-category-not-found")}function V(a,n){if(1&a&&(e.ynx(0),e.YNc(1,Z,5,12,"section",2),e.YNc(2,F,1,0,"app-category-not-found",3),e.BQk()),2&a){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",!t.isBlank),e.xp6(1),e.Q6J("ngIf",!t.isBlank&&t.products&&0===t.products.length)}}function Y(a,n){if(1&a&&(e.TgZ(0,"div",8),e._UZ(1,"img",9),e.qZA()),2&a){const t=e.oxw(3);e.xp6(1),e.Q6J("src",t.getBannerImages(null==t.categoryBanner?null:t.categoryBanner.mobileBanner),e.LSH)}}function z(a,n){if(1&a&&(e.TgZ(0,"a",32),e._UZ(1,"app-mtn-product-card",19),e.qZA()),2&a){const t=e.oxw().$implicit,i=e.oxw(5);e.ekj("isLayoutTemplate",!i.isLayoutTemplate),e.xp6(1),e.Q6J("currency",i.currency)("product",t)("categoryName",i.categoryName)}}function J(a,n){if(1&a&&(e.ynx(0),e.YNc(1,z,2,5,"a",31),e.BQk()),2&a){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",!t.isDisable)}}function U(a,n){if(1&a&&(e.TgZ(0,"div",30),e.YNc(1,J,2,1,"ng-container",16),e.qZA()),2&a){const t=e.oxw(4);e.xp6(1),e.Q6J("ngForOf",t.products)}}function E(a,n){1&a&&(e.TgZ(0,"div",20),e._UZ(1,"p-progressSpinner"),e.qZA())}function Q(a,n){if(1&a&&(e.TgZ(0,"div")(1,"div",10)(2,"div",23)(3,"div",24)(4,"div",25)(5,"div",26),e._uU(6),e.qZA()(),e.TgZ(7,"div",27),e._UZ(8,"div",28),e.qZA()()(),e.YNc(9,U,2,1,"div",29),e.YNc(10,E,2,0,"div",14),e.qZA()()),2&a){const t=e.oxw(3);e.xp6(6),e.hij(" ",t.category?t.category.categoryName:t.categoryName," "),e.xp6(3),e.Q6J("ngIf",t.products&&t.products.length>0),e.xp6(1),e.Q6J("ngIf",t.showProductSpinner)}}const H=function(a){return{"margin-top":a}};function $(a,n){if(1&a&&(e.TgZ(0,"section",22),e.YNc(1,Y,2,1,"div",7),e.YNc(2,Q,11,3,"div",3),e.qZA()),2&a){const t=e.oxw(2);e.Q6J("ngStyle",e.VKq(3,H,t.isMobileLayout?"65px":"210px")),e.xp6(1),e.Q6J("ngIf",(null==t.categoryBanner?null:t.categoryBanner.isBanner)&&(null==t.categoryBanner?null:t.categoryBanner.mobileBanner)),e.xp6(1),e.Q6J("ngIf",t.products&&t.products.length)}}function G(a,n){1&a&&e._UZ(0,"app-category-not-found")}function W(a,n){if(1&a&&(e.YNc(0,$,3,5,"section",21),e.YNc(1,G,1,0,"app-category-not-found",3)),2&a){const t=e.oxw();e.Q6J("ngIf",!t.isBlank),e.xp6(1),e.Q6J("ngIf",!t.isBlank&&t.products&&0===t.products.length)}}const j=[{path:"",component:(()=>{class a{activatedRoute;productService;store;reviewsService;messageService;translate;ref;loaderService;tenantService;authTokenService;cookieService;mainDataService;router;appDataService;permissionService;$gaService;platformId;$gtmService;categoryId;topNumber;category;categoryName;items=[];breadItems=[];home={icon:"pi pi-home",routerLink:"/"};currency={};baseUrl=f.N.apiEndPoint+"/";emptyMsg="Your Category Is Empty";subPath;subId;catIds="";catPaths="";products;isError=!1;isBlank=!0;reviews;badgesList=[];rawCategories=[];newCategory=null;token;pageSize=50;currentPageSize=50;triggerProductsCall=!1;showProductSpinner=!1;loadDataType="";currentPageNumber=1;total=0;ignorePagination=!1;shouldCallNextFeatureProduct=!0;shouldCallNextCategoryProduct=!0;navbarData;isLayoutTemplate=!1;promotionId;promotionName;screenWidth=window.innerWidth;isMobileView=this.screenWidth<=786;isGoogleAnalytics=!1;userDetails;tagName=y.Ir;isMobileLayout=!1;categoryBanner;onScroll(t){if((0,u.NF)(this.platformId)){const i=window.scrollY||document.documentElement.scrollTop,r=document.documentElement.scrollHeight;i+window.innerHeight>=r-1&&!this.triggerProductsCall&&this.total>=this.pageSize&&this.loadOnScrollData()}}onResize(t){this.screenWidth=t.target.innerWidth,this.isMobileView=this.screenWidth<=768}loadOnScrollData(){"category"===this.loadDataType?this.shouldCallNextCategoryProduct&&this.loadPaginatedProducts():"feature"===this.loadDataType?this.shouldCallNextFeatureProduct&&this.loadPaginatedFeatureProducts():"promotion"===this.loadDataType&&this.shouldCallNextFeatureProduct&&this.loadPaginatedPromotionProducts()}constructor(t,i,r,o,s,d,g,X,tt,et,it,at,rt,ot,nt,st,ct,dt){this.activatedRoute=t,this.productService=i,this.store=r,this.reviewsService=o,this.messageService=s,this.translate=d,this.ref=g,this.loaderService=X,this.tenantService=tt,this.authTokenService=et,this.cookieService=it,this.mainDataService=at,this.router=rt,this.appDataService=ot,this.permissionService=nt,this.$gaService=st,this.platformId=ct,this.$gtmService=dt,this.isLayoutTemplate=this.permissionService.hasPermission("Layout-Template"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.scrollToTop()}ngOnInit(){this.navbarData=this.appDataService.layoutTemplate.find(t=>"navbar"===t.type),this.paramfunc(),this.scrollToTop()}paramfunc(){let t;this.activatedRoute.paramMap.subscribe(i=>{this.products=[],t=i.get("id"),t=t?.split("&"),this.userDetails=this.store.get("profile"),this.router.url.includes("promotion")?1==t?.length&&(this.promotionId=t[0],this.loadDataType="promotion",this.triggerAnalytics("promotion",this.promotionId),this.loadPromotionData()):1==t?.length?(this.categoryId=t[0],this.loadDataType="category",this.triggerAnalytics("category",this.categoryId),this.loadData()):3==t?.length&&(this.categoryId=t[0],this.topNumber=t[1],this.categoryName=t[2],this.loadDataType="feature",this.triggerAnalytics("feature",this.categoryId),this.loadSectionData(),this.$gtmService.pushPageView("feature",this.categoryName))})}loadData(){if(this.loaderService.show(),this.productService.getCategoryProducts(this.categoryId,this.currentPageSize,!0,!0).subscribe({next:t=>{this.products=[],this.isBlank=!1,t.success&&(this.isError=!1,this.total=t.data.productsList.records.length,this.categoryBanner=t.data.categoryBanner,t.data?.productsList?.records.forEach(i=>{this.addProductFromLoadData(i),this.badgesList=i.badgesList[i]?.desktopImage||[]}),this.getAllCategories()),this.loaderService.hide()},error:t=>{this.handleError(t)}}),setTimeout(()=>{this.store.subscription("currency").subscribe({next:t=>this.currency=t})},10),this.activatedRoute.queryParams.subscribe(t=>{this.subPath=t?.path?.split("//"),this.subId=t?.id?.split("//")}),this.subPath){let t="Products";this.translate.get("categoryCard.products").subscribe(i=>{t=i}),this.categoryName=this.subPath[this.subPath.length-1],this.items=[],this.subPath.forEach((i,r)=>{this.items.push({label:i,id:this.subId[r]})}),this.items.forEach((i,r)=>{this.catPaths=0==r?i.label:this.catPaths+"//"+i.label,this.catIds=0==r?i.id:this.catIds+"//"+String(i.id),i.routerLink=`/category/${i.id}`,i.queryParams={path:this.catPaths,id:this.catIds}}),this.items.push({label:t}),this.home={icon:"pi pi-home",routerLink:"/"}}else{this.store.subscription("categories").subscribe({next:i=>{i.forEach(r=>{r.id==this.categoryId&&(this.category=r)}),this.activatedRoute.queryParamMap.subscribe(r=>{this.categoryName=r.get("categoryName")})},error:i=>{console.error(i)}});let t="Products";this.translate.get("categoryCard.products").subscribe(i=>{t=i}),this.items=[{label:this.category?.categoryName},{label:t}],this.home={icon:"pi pi-home",routerLink:"/"}}}loadPromotionData(){this.loaderService.show(),this.productService.GetAllProductsByPrmotion(this.promotionId,this.currentPageSize,!1,this.currentPageNumber).subscribe({next:t=>{this.products=[],this.isBlank=!1,this.categoryName=t.data.promotionName,this.breadItems=[{label:this.categoryName}],t?.data?.records?.length>0&&(this.total=t?.data?.records?.length,t.data?.records.forEach(i=>{this.addProductFromLoadSectionData(i)}),this.loaderService.hide())},error:t=>{console.error(t),this.isBlank=!1,this.loaderService.hide()},complete:()=>{this.loaderService.hide()}}),setTimeout(()=>{this.store.subscription("currency").subscribe({next:t=>this.currency=t})},10),this.home={icon:"pi pi-home",routerLink:"/"},this.ref.detectChanges(),this.ref.markForCheck()}handleError(t){this.isBlank=!1,this.isError=!0,this.loaderService.hide()}addProductFromLoadData(t){let i,r=t?.productVariances?.find(d=>d.isDefault);if(r)i=r;else{let d=t?.productVariances?.find(g=>g.soldOut);i=d||t?.productVariances[0]}let o=[];i?.productFeaturesList&&(o=i?.productFeaturesList[0]?.featureList);let s={badges:t.badgesList,productId:t?.id,productName:t?.name,isLiked:t?.isLiked,priceValue:i?.price,salePriceValue:i?.salePrice,priceId:i?.priceId,currencyCode:t?.currencyCode,masterImageUrl:t?.masterImageUrl??(i.images?i.images[0]:null),thumbnailImages:i?.thumbnailImages,soldOut:i?.soldOut,rate:i?.rate,count:i?.count??0,specProductId:i.specProductId,channelId:t.channelId??"1",salePercent:i?.salePrice?100-i?.salePrice/i?.price*100:0,shopId:t.shopId,isHot:o?.includes(1),isNew:o?.includes(2),isBest:o?.includes(3),quantity:i.quantity,proSchedulingId:i.proSchedulingId,stockPerSKU:i.stockPerSKU,stockStatus:i.stockStatus,sku:i?.sku,skuAutoGenerated:i.skuAutoGenerated,isDisable:t.isDisable,badgesList:t?.badgesList};this.products.push(s)}loadSectionData(){this.loaderService.show(),this.productService.GetAllProductsByFeature(this.categoryId,this.currentPageSize,!1,this.currentPageNumber,50,!1,null,this.ignorePagination).subscribe({next:t=>{this.products=[],this.isBlank=!1,t?.data?.records?.length>0&&(this.total=t?.data?.records?.length,t.data?.records.forEach(i=>{this.addProductFromLoadSectionData(i)}),this.loaderService.hide())},error:t=>{console.error(t),this.isBlank=!1,this.loaderService.hide()},complete:()=>{this.loaderService.hide()}}),setTimeout(()=>{this.store.subscription("currency").subscribe({next:t=>this.currency=t})},10),this.breadItems=[{label:this.categoryName}],this.home={icon:"pi pi-home",routerLink:"/"},this.ref.detectChanges(),this.ref.markForCheck()}addProductFromLoadSectionData(t){let i,r=t?.productVariances?.find(o=>o.isDefault);if(r)i=r;else{let o=t?.productVariances?.find(s=>s.soldOut);i=o||t?.productVariances[0]}if(i){let o=[];i?.productFeaturesList&&(o=i?.productFeaturesList[0]?.featureList);let s={badges:t.badgesList,productId:t?.id,productName:t?.name,isLiked:t?.isLiked,priceValue:i?.price,priceId:i?.priceId,salePriceValue:i?.salePrice,currencyCode:t?.currencyCode,masterImageUrl:t?.masterImageUrl??i.images[0],thumbnailImages:i?.thumbnailImages,soldOut:i?.soldOut,rate:i?.rate,count:i?.count??0,salePercent:i?.salePrice?100-i?.salePrice/i?.price*100:0,shopId:t.shopId,specProductId:i.specProductId,channelId:t.channelId??"1",isHot:o?.includes(1),isNew:o?.includes(2),isBest:o?.includes(3),quantity:i.quantity,proSchedulingId:i.proSchedulingId,stockPerSKU:i.stockPerSKU,stockStatus:i.stockStatus,sku:i?.sku,skuAutoGenerated:i.skuAutoGenerated,badgesList:t?.badgesList};s.salePriceValue&&(s.salePercent=100-s.salePriceValue/s.priceValue*100),this.products.push(s)}}fetchCategories(t,i){if(0!=t.length)for(const r of t)r.id==this.categoryId&&this.assignBreadCrumbsData(r),this.fetchCategories(r.categories,r)}assignBreadCrumbsData(t){let i=t?.categoryIds?.split("->")?.map(Number),r=t?.categoryPath?.split("->")?.map(String),o=[];i.length===r.length&&(i?.map((s,d)=>{o.push({routerLink:"/category/"+s.toString(),label:r[d]})}),this.breadItems=o,this.ref.detectChanges(),this.ref.markForCheck()),this.$gtmService.pushPageView("category",t?.categoryPath)}logOut(){sessionStorage.clear(),this.authTokenService.authTokenSet(""),this.cookieService.delete("authToken","/"),this.store.set("profile",""),this.mainDataService.setCartLenghtData(null),this.mainDataService.setUserData(null),localStorage.setItem("sessionId",""),localStorage.setItem("addedProducts",""),localStorage.setItem("cartId",""),this.store.set("cartProducts",[]),localStorage.removeItem("refreshToken"),localStorage.removeItem("auth_enc")}loadPaginatedProducts(){this.triggerProductsCall=!0,this.showProductSpinner=!0,this.currentPageSize+=this.pageSize,this.currentPageNumber+=1,this.ref.detectChanges(),this.productService.getCategoryProducts(this.categoryId,this.currentPageSize,!1,!1,!0,this.currentPageNumber).subscribe({next:t=>{this.shouldCallNextCategoryProduct=t.data?.productsList.records.length>0;const i=t.data.productsList.records;this.total=i.length,i.forEach(r=>{this.addProductFromLoadData(r)}),this.triggerProductsCall=!t.data.productsList.records.length,this.showProductSpinner=!1,this.ref.markForCheck()}})}addProduct(t){let i,r=t?.productVariances?.find(d=>d.isDefault);if(r)i=r;else{let d=t?.productVariances?.find(g=>g.soldOut);i=d||t?.productVariances[0]}let o=[];i?.productFeaturesList&&(o=i?.productFeaturesList[0]?.featureList);let s={productId:t?.id,productName:t?.name,priceValue:i?.price,salePriceValue:i?.salePrice,currencyCode:t?.currencyCode,masterImageUrl:t?.masterImageUrl??i.images[0],thumbnailImages:i?.thumbnailImages,soldOut:i?.soldOut,rate:i?.rate,count:i?.count??0,salePercent:i?.salePrice?100-i?.salePrice/i?.price*100:0,channelId:t?.channelId,isHot:o?.includes(1),isNew:o?.includes(2),isBest:o?.includes(3),quantity:i.quantity,proSchedulingId:i.proSchedulingId,stockPerSKU:i.stockPerSKU,stockStatus:i.stockStatus,sku:i?.sku,skuAutoGenerated:i.skuAutoGenerated};this.products.push(s)}loadPaginatedFeatureProducts(){this.triggerProductsCall=!0,this.showProductSpinner=!0,this.currentPageSize+=this.pageSize,this.currentPageNumber+=1,this.ref.detectChanges(),this.productService.GetAllProductsByFeature(this.categoryId,this.currentPageSize,!0,this.currentPageNumber).subscribe({next:t=>{0==t.data?.records.length&&(this.shouldCallNextFeatureProduct=!1);const i=t.data?.records.slice(-this.pageSize);this.total=t.data?.records?.length,i.forEach(r=>{this.addProductFromLoadSectionData(r)}),this.triggerProductsCall=!t.data?.records.length,this.showProductSpinner=!1,this.ref.markForCheck()}})}loadPaginatedPromotionProducts(){this.triggerProductsCall=!0,this.showProductSpinner=!0,this.currentPageSize+=this.pageSize,this.currentPageNumber+=1,this.ref.detectChanges(),this.productService.GetAllProductsByFeature(this.categoryId,this.currentPageSize,!0,this.currentPageNumber).subscribe({next:t=>{0==t.data?.records.length&&(this.shouldCallNextFeatureProduct=!1);const i=t.data?.records.slice(-this.pageSize);this.total=t.data?.records?.length,i.forEach(r=>{this.addProduct(r)}),this.triggerProductsCall=!t.data?.records.length,this.showProductSpinner=!1,this.ref.markForCheck()}})}getAllCategories(){let t=localStorage.getItem("allCategories");t=JSON.parse(t),this.rawCategories=t,this.rawCategories?.forEach(i=>{i.id==this.categoryId&&this.assignBreadCrumbsData(i),i.path=i.categoryName,i.catIds=i.id,this.fetchCategories(i.categories,i)})}scrollToTop(){(0,u.NF)(this.platformId)&&window.scrollTo({top:0,behavior:"smooth"})}triggerAnalytics(t,i){this.isGoogleAnalytics&&this.permissionService.getTagFeature("promotion"===t?"VIEW_PROMOTION":"VIEW_ITEM_LIST")&&("promotion"===t?this.$gaService.pageView("/promotion","Promotion ID: "+i):"category"===t?this.$gaService.pageView("/category","Category ID: "+i):"feature"===t&&this.$gaService.pageView("/category","Feature ID: "+i),this.$gaService.event(this.tagName.SEARCH,i,"promotion"===t?"VIEW_PROMOTION":"VIEW_ITEM_LIST",1,!0,{id:i,user_ID:this.userDetails?this.userDetails.mobileNumber:"Un_Authenticated"}))}getBannerImages(t){return _.Z.verifyImageURL(t,f.N.apiEndPoint)}static \u0275fac=function(i){return new(i||a)(e.Y36(p.gz),e.Y36(l.M5),e.Y36(l.d6),e.Y36(l.Y0),e.Y36(v.ez),e.Y36(h.sK),e.Y36(e.sBO),e.Y36(l.D1),e.Y36(l.aB),e.Y36(l.Lz),e.Y36(I.N),e.Y36(l.iI),e.Y36(p.F0),e.Y36(l.UW),e.Y36(l.$A),e.Y36(y.$r),e.Y36(e.Lbi),e.Y36(b.J))};static \u0275cmp=e.Xpm({type:a,selectors:[["app-category-products"]],hostBindings:function(i,r){1&i&&e.NdJ("scroll",function(s){return r.onScroll(s)},!1,e.Jf7)("resize",function(s){return r.onResize(s)},!1,e.Jf7)},inputs:{products:"products"},decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["mobileView",""],["class","category-products-page",3,"ngClass",4,"ngIf"],[4,"ngIf"],[1,"category-products-page",3,"ngClass"],[1,"breadcrumb","m-0",3,"ngClass"],[3,"home","model"],["class","category-banner",4,"ngIf"],[1,"category-banner"],[1,"category-banner__img",3,"src"],[1,""],[1,"col-12","col-md-6","flex"],[1,"font-size-22","bold-font"],["class","title-category flex flex-row flex-wrap ",4,"ngIf"],["class","spinner-product",4,"ngIf"],[1,"title-category","flex","flex-row","flex-wrap"],[4,"ngFor","ngForOf"],["class","slide mt-2 mx-1 md:mx-md-2 lg:mx-lg-2 products-margin",3,"isLayoutTemplate",4,"ngIf"],[1,"slide","mt-2","mx-1","md:mx-md-2","lg:mx-lg-2","products-margin"],[3,"currency","product","categoryName"],[1,"spinner-product"],["class","category-products-page",3,"ngStyle",4,"ngIf"],[1,"category-products-page",3,"ngStyle"],[1,"container",2,"padding-top","16px"],[1,"row",2,"padding","0px 15px"],[1,"col-9",2,"padding-left","0px !important"],[1,"font-size-22","font-bold"],[1,"col-3",2,"padding-right","0px !important"],[2,"float","inline-end"],["class","row mobile-card-div",4,"ngIf"],[1,"row","mobile-card-div"],["class","slide mt-2 col-sm-6","style","padding: 0;",3,"isLayoutTemplate",4,"ngIf"],[1,"slide","mt-2","col-sm-6",2,"padding","0"]],template:function(i,r){if(1&i&&(e.YNc(0,V,3,2,"ng-container",0),e.YNc(1,W,2,2,"ng-template",null,1,e.W1O)),2&i){const o=e.MAs(2);e.Q6J("ngIf",!r.isMobileView)("ngIfElse",o)}},dependencies:[u.mk,u.sg,u.O5,u.PC,S.Y,C.G,x.a,N],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}a[_ngcontent-%COMP%]{text-decoration:none;width:var(--product-card-width)}@media only screen and (max-width: 767px){a[_ngcontent-%COMP%]{--product-card-width: 43%;margin:8px}a[class*=isLayoutTemplate][_ngcontent-%COMP%]{--product-card-width: 36% !important}}@media only screen and (min-width: 1701px){a[_ngcontent-%COMP%]{--product-card-width: 18%}a[class*=isLayoutTemplate][_ngcontent-%COMP%]{--product-card-width: 11% !important}}@media only screen and (min-width: 1201px) and (max-width: 1700px){a[_ngcontent-%COMP%]{width:18%}a[class*=isLayoutTemplate][_ngcontent-%COMP%]{--product-card-width: 11% !important}}.showProducts[_ngcontent-%COMP%]{max-width:1000px}.font-bold[_ngcontent-%COMP%]{font-family:main-regular;font-size:18px;font-style:normal;font-weight:500;line-height:24px;text-transform:uppercase}.mobile-card-div[_ngcontent-%COMP%]{justify-content:left;margin:0 15px;width:100%}@media screen and (min-width: 769px){.content-container.mt-5[_ngcontent-%COMP%]{padding-left:11rem!important;padding-right:8rem!important;margin-top:0!important}}@media screen and (max-width: 768px){.breadcrumb[_ngcontent-%COMP%]{margin-top:200px!important}.col-12.md\\:col-6.flex.justify-content-center.md\\:justify-content-start[_ngcontent-%COMP%]{justify-content:left!important}.content-container.mt-5[_ngcontent-%COMP%]{margin-top:0!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:150px!important}}.products-margin[_ngcontent-%COMP%]{padding:0 0 16px!important}.title-category[_ngcontent-%COMP%]{margin-top:5px!important;margin-bottom:1rem!important}@media screen and (min-width: 768px){.category-banner[_ngcontent-%COMP%]{margin-bottom:24px}}.category-banner__img[_ngcontent-%COMP%]{width:100%}"]})}return a})()}];var q=c(4480),R=c(6574);let K=(()=>{class a{static \u0275fac=function(i){return new(i||a)};static \u0275mod=e.oAB({type:a});static \u0275inj=e.cJS({imports:[u.ez,p.Bz.forChild(j),q.T,h.aw,R.p,x.w,P.hJ]})}return a})()}}]);