{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { OtpInputComponent } from '../otp-input/otp-input.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@core/services\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/progressspinner\";\nfunction RegisterOtpComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"mobile-content-container\": a0\n  };\n};\nfunction RegisterOtpComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6);\n    i0.ɵɵelement(4, \"img\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelement(7, \"img\", 10);\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"h2\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 13);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 15)(17, \"h2\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 16)(24, \"div\", 17)(25, \"app-otp-input\", 18, 19);\n    i0.ɵɵlistener(\"otpComplete\", function RegisterOtpComponent_ng_container_2_Template_app_otp_input_otpComplete_25_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onOtpComplete($event));\n    })(\"resendOtp\", function RegisterOtpComponent_ng_container_2_Template_app_otp_input_resendOtp_25_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.resendOtp());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_2_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.validateOtp());\n    });\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 21)(30, \"div\", 16)(31, \"div\", 17)(32, \"app-otp-input\", 18, 22);\n    i0.ɵɵlistener(\"otpComplete\", function RegisterOtpComponent_ng_container_2_Template_app_otp_input_otpComplete_32_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onOtpComplete($event));\n    })(\"resendOtp\", function RegisterOtpComponent_ng_container_2_Template_app_otp_input_resendOtp_32_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.resendOtp());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_2_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.validateOtp());\n    });\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(26);\n    const _r3 = i0.ɵɵreference(33);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.isMobileLayout && ctx_r1.screenWidth <= 767));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, \"auth.otp.title\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 11, \"auth.otp.subTitle\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 13, \"auth.otp.title\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 15, \"auth.otp.subTitle\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(28, 17, \"auth.otp.next\"))(\"disabled\", !_r2.isComplete);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(35, 19, \"auth.otp.next\"))(\"disabled\", !_r3.isComplete);\n  }\n}\nexport class RegisterOtpComponent {\n  constructor(messageService, translate, router, otpService, store, permissionService, loaderService, $gaService, $gtmService) {\n    this.messageService = messageService;\n    this.translate = translate;\n    this.router = router;\n    this.otpService = otpService;\n    this.store = store;\n    this.permissionService = permissionService;\n    this.loaderService = loaderService;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.loading = false;\n    this.mobileNumber = \"\";\n    this.otpCode = \"\";\n    this.isMobileLayout = false;\n    this.screenWidth = window.innerWidth;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.$gtmService.pushPageView('signUp', 'OTP');\n    if (history.state.mobile == null) {\n      this.router.navigate(['/register']);\n    } else {\n      this.mobileNumber = history.state.mobile;\n    }\n  }\n  ngOnDestroy() {}\n  onOtpComplete(otpCode) {\n    this.otpCode = otpCode;\n  }\n  validateOtp() {\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_OTP_VERIFY, '', 'OTP_VERIFICATION', 1, true);\n    if (this.otpCode && this.otpCode.length === 4) {\n      this.loading = true;\n      this.otpService.checkOTP({\n        UserName: this.mobileNumber,\n        OTPCode: this.otpCode,\n        CountryId: \"1448983B-0C38-450A-BD71-9204D181B925\"\n      }).subscribe({\n        next: res => {\n          this.loading = false;\n          if (res.success === 12) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.invalidOtp')\n            });\n          } else if (res.success === 87) {\n            // Navigate to registration details instead of reset password\n            this.router.navigateByUrl('/register/register-details', {\n              state: {\n                mobile: this.mobileNumber,\n                otp: this.otpCode,\n                isVerified: true\n              }\n            });\n          }\n        },\n        error: err => {\n          this.loading = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        }\n      });\n    } else {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\n      });\n    }\n  }\n  resendOtp() {\n    this.loaderService.show();\n    this.otpService.checkMobileNumber({\n      UserName: this.otpService.username,\n      CountryId: this.otpService.countryId\n    }).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        // Timer restart is handled in OTP component\n      },\n\n      error: err => {\n        this.store.set(\"loading\", false);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n}\nRegisterOtpComponent.ɵfac = function RegisterOtpComponent_Factory(t) {\n  return new (t || RegisterOtpComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.RegisterService), i0.ɵɵdirectiveInject(i4.StoreService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i4.LoaderService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService));\n};\nRegisterOtpComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: RegisterOtpComponent,\n  selectors: [[\"app-register-otp\"]],\n  standalone: true,\n  features: [i0.ɵɵStandaloneFeature],\n  decls: 3,\n  vars: 2,\n  consts: [[1, \"update-password-page\"], [\"class\", \"spinner\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"spinner\"], [1, \"content-container\", 3, \"ngClass\"], [1, \"grid\", \"justify-content-center\", \"shadow-signin\"], [1, \"image\", \"col-12\", \"desktop-only\"], [\"src\", \"assets/images/registerLogo.svg\", \"alt\", \"\", \"srcset\", \"\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"content-body\"], [1, \"mobile-header\", \"mobile-only\"], [\"src\", \"assets/images/register.svg\"], [1, \"head-desc\"], [1, \"signup-heading\"], [1, \"signup-desc\"], [1, \"desktop-layout\", \"desktop-only\"], [1, \"otp-heading\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\"], [3, \"otpComplete\", \"resendOtp\"], [\"desktopOtpInput\", \"\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"validate-btn\", 3, \"label\", \"disabled\", \"click\"], [1, \"mobile-layout\", \"mobile-only\"], [\"mobileOtpInput\", \"\"]],\n  template: function RegisterOtpComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"section\", 0);\n      i0.ɵɵtemplate(1, RegisterOtpComponent_div_1_Template, 2, 0, \"div\", 1);\n      i0.ɵɵtemplate(2, RegisterOtpComponent_ng_container_2_Template, 36, 23, \"ng-container\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n    }\n  },\n  dependencies: [CommonModule, i7.NgClass, i7.NgIf, TranslateModule, i2.TranslatePipe, ButtonModule, i8.ButtonDirective, ProgressSpinnerModule, i9.ProgressSpinner, OtpInputComponent],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n[_nghost-%COMP%]     button.back-btn {\\n  position: relative !important;\\n}\\n\\n@media screen and (min-width: 768px) {\\n  [_nghost-%COMP%]     button.back-btn {\\n    top: -22px !important;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .update-password-page[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n\\n.content-container.mobile-content-container[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.shadow-signin[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n  padding-top: 16px;\\n  margin: 30px;\\n}\\n@media screen and (max-width: 767px) {\\n  .shadow-signin[_ngcontent-%COMP%] {\\n    padding: 0 !important;\\n    margin: 0;\\n    border: none;\\n    background-color: white;\\n  }\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: end;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  width: 300px;\\n  border-radius: 281.739px;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%] {\\n  padding-top: 1.5rem;\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n@media screen and (max-width: 767px) {\\n  .shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%] {\\n    padding-top: 0.75rem;\\n  }\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  padding: 0.75rem;\\n}\\n.mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%] {\\n  margin: 0 !important;\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #212121;\\n  font-family: var(--medium-font) !important;\\n}\\n.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 400;\\n  color: #443F3F;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.validate-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 4px;\\n  padding: 16px 24px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  text-transform: uppercase;\\n  background-color: #1d4c69;\\n  color: white;\\n  border: none;\\n  margin-bottom: 1.25rem;\\n  margin-top: 0.75rem;\\n}\\n.validate-btn[_ngcontent-%COMP%]:disabled {\\n  background-color: #1d4c69;\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n@media screen and (max-width: 767px) {\\n  .validate-btn[_ngcontent-%COMP%] {\\n    margin: 0.75rem;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-button.validate-btn:enabled:hover {\\n  background-color: #1d4c69 !important;\\n}\\n\\n.mobile-only[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media screen and (max-width: 767px) {\\n  .mobile-only[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n\\n.desktop-only[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n@media screen and (max-width: 767px) {\\n  .desktop-only[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media screen and (max-width: 767px) {\\n  .mobile-content-container[_ngcontent-%COMP%] {\\n    margin-top: 7rem !important;\\n  }\\n  .mobile-content-container[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%] {\\n    margin-bottom: 25px;\\n    padding-left: 14px !important;\\n    padding-right: 24px !important;\\n    border: 1px solid rgba(151, 151, 151, 0.17);\\n    border-radius: 7px;\\n    padding-top: 16px;\\n    margin: 30px;\\n  }\\n  .mobile-content-container[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .mobile-content-container[_ngcontent-%COMP%]   .p-fluid[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    margin-bottom: 0.75rem;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .margin-x-100[_ngcontent-%COMP%] {\\n    margin-top: 160px !important;\\n  }\\n}\\n.desktop-layout[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 500;\\n  color: #212121;\\n  font-family: var(--medium-font) !important;\\n  margin-bottom: 0.5rem;\\n}\\n.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #443F3F;\\n  font-family: var(--regular-font) !important;\\n  margin: 0;\\n}\\n\\n.mobile-layout[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}