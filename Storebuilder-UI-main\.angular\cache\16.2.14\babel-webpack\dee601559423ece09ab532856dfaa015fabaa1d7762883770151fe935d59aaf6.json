{"ast": null, "code": "import { HttpClient, HttpParams } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { BehaviorSubject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nexport class CustomPageService {\n  http = inject(HttpClient);\n  backgroundSubject = new BehaviorSubject({});\n  backgroundStyle$ = this.backgroundSubject.asObservable();\n  changePageTitle = new BehaviorSubject('');\n  changePageTitle$ = this.changePageTitle.asObservable();\n  setBackgroundStyle(style) {\n    this.backgroundSubject.next(style);\n  }\n  onChangePageTitle(title) {\n    this.changePageTitle.next(title);\n  }\n  getCustomPageDetails(pageURL) {\n    return this.http.post(`${environment.apiEndPoint}/Product/CustomPage/GetCustomPageDetails`, {\n      pageURL\n    });\n  }\n  getProducts(currentPage, pageSize, sectionId) {\n    const params = new HttpParams().set('sectionid', sectionId.toString()).set('pagesize', pageSize.toString()).set('PageNumber', currentPage.toString());\n    return this.http.get(`${environment.apiEndPoint}/Product/CustomPage/GetSectionDetailsWithProducts`, {\n      params\n    });\n  }\n  static ɵfac = function CustomPageService_Factory(t) {\n    return new (t || CustomPageService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomPageService,\n    factory: CustomPageService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}