
export interface Category {
  id: number;
  categoryName: string;
  categoryNameAr: string;
  description: string;
  image: string;
  totalProductCount: number;
  parentChildCount: number;
  path:string;
  catIds:number;
  slug: string;
  url_path: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  company_id: number;
  position: number;
  status: number;
  parentId: number;
  created_at: string;
  updated_at: string;
  display_mode: string;
  category_icon_path: string;
  additional: any;
  category_product_in_pwa: number;
  count_product: number;
  translations: Array<CategoryTranslation>;
  categories: Array<CategoryChildren>;
  _lft: string;
  _rgt: string;
  isVirtual:boolean;
  hide: boolean;
}

export interface CategoryChildren {
  id: number;
  categoryName: string;
  categoryNameAr: string;
  description: string;
  image: string;
  totalProductCount: number;
  parentChildCount: number;
  path:string;
  catIds:number;
  slug: string;
  url_path: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  company_id: number;
  position: number;
  status: number;
  parentId: number;
  created_at: string;
  updated_at: string;
  display_mode: string;
  category_icon_path: string;
  additional: any;
  category_product_in_pwa: number;
  count_product: number;
  translations: Array<CategoryTranslation>;
  categories: Array<CategoryChildren>;
  _lft: string;
  _rgt: string;
  isVirtual:boolean;
}

export interface CategoryTranslation {
  id: number;
  name: string;
  slug: string;
  description: string;
  url_path: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
  category_id: number;
  locale: string;
  company_id: number;
  locale_id: number;
}
export interface CategoryList {
  id: number;
  parentId: number;
  order: number;
  categoryName: string;
  categoryNameAr: string;
  categoryPath: string;
  categoryIds: string;
  categoryStatus: boolean;
  image: string;
  shopId: number | null;
  isVirtual: boolean;
  isRefundable: boolean;
  totalProductCount: number;
  hide: boolean;
  templateId: number;
  categories: CategoryList[]; 
  path:string;
  catIds:number;
  disabled:boolean;
}

export interface  ChildCategoryItem {
  name: string;
  code: string;
  id: number;
  hidden: boolean;
  totalProductC: number;
};