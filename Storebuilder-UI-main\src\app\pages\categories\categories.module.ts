import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IndexComponent } from './components/index/index.component';
import { RouterModule } from "@angular/router";
import { routes } from "./routes";
import { RippleModule } from "primeng/ripple";
import {InitialModule} from "@shared/modules/initial.module";
import {TranslateModule} from "@ngx-translate/core";
import {BreadcrumbModule} from "primeng/breadcrumb";

@NgModule({
  declarations: [
    IndexComponent
  ],
  imports: [
    CommonModule,
    InitialModule,
    RouterModule.forChild(routes),
    RippleModule,
    TranslateModule,
    BreadcrumbModule
  ]
})
export class CategoriesModule { }
