{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { DynamicDialogModule } from 'primeng/dynamicdialog';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { InputMaskModule } from \"primeng/inputmask\";\nimport { CheckboxModule } from \"primeng/checkbox\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { routes } from \"./routes\";\n// Components\nimport { IndexComponent } from './components/index/index.component';\nimport { OrderSummaryCartComponent } from './components/order-summary-cart/order-summary-cart.component';\nimport { PaymentCartComponent } from './components/payment-cart/payment-cart.component';\nimport { DeliveryMethodCartComponent } from './components/delivery-method-cart/delivery-method-cart.component';\nimport { PaymentDialogComponent } from './components/payment-dialog/payment-dialog.component';\nimport { OrderPlacedComponent } from './components/order-placed/order-placed.component';\nimport { PaymentErrorDialogComponent } from './components/payment-error-dialog/payment-error-dialog.component';\nimport { PaymentWaitingDialogComponent } from './components/payment-waiting-dialog/payment-waiting-dialog.component';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { LightboxLoaderModalComponent } from \"@pages/checkout/modals/lightbox-loader-modal/lightbox-loader-modal.component\";\nimport { SelectAddressMobiluiComponent } from './components/select-address-mobilui/select-address-mobilui.component';\nimport { PromoCodeComponent } from './components/promo-code/promo-code.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class CheckoutModule {}\nCheckoutModule.ɵfac = function CheckoutModule_Factory(t) {\n  return new (t || CheckoutModule)();\n};\nCheckoutModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: CheckoutModule\n});\nCheckoutModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule.forChild(routes), RippleModule, RadioButtonModule, DropdownModule, DynamicDialogModule, SelectButtonModule, InputMaskModule, CheckboxModule, FormsModule, ReactiveFormsModule, TranslateModule, ButtonModule, SharedModule, DialogModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CheckoutModule, {\n    declarations: [IndexComponent, PaymentCartComponent, DeliveryMethodCartComponent, OrderSummaryCartComponent, PaymentDialogComponent, OrderPlacedComponent, PaymentErrorDialogComponent, PaymentWaitingDialogComponent, LightboxLoaderModalComponent, SelectAddressMobiluiComponent, PromoCodeComponent],\n    imports: [CommonModule, i1.RouterModule, RippleModule, RadioButtonModule, DropdownModule, DynamicDialogModule, SelectButtonModule, InputMaskModule, CheckboxModule, FormsModule, ReactiveFormsModule, TranslateModule, ButtonModule, SharedModule, DialogModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}