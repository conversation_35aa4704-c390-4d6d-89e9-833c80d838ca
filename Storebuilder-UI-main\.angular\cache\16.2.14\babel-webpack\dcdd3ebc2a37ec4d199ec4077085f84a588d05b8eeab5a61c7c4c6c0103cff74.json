{"ast": null, "code": "import { AboutUsComponentComponent } from \"./components/about-us/AboutUsComponent/AboutUsComponent.component\";\nexport const routes = [{\n  path: '',\n  component: AboutUsComponentComponent\n}];", "map": {"version": 3, "names": ["AboutUsComponentComponent", "routes", "path", "component"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\about-us\\routes.ts"], "sourcesContent": ["import {Routes} from \"@angular/router\";\r\nimport { AboutUsComponentComponent } from \"./components/about-us/AboutUsComponent/AboutUsComponent.component\";\r\n\r\nexport const routes: Routes = [\r\n  { path: '', component: AboutUsComponentComponent },\r\n];\r\n"], "mappings": "AACA,SAASA,yBAAyB,QAAQ,mEAAmE;AAE7G,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAyB,CAAE,CACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}