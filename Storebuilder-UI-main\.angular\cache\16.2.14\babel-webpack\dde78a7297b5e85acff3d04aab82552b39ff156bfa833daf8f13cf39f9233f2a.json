{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from '@environments/environment';\nimport jwt_decode from 'jwt-decode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-cookie-service\";\nimport * as i3 from \"./main-data.service\";\nexport class InitService {\n  http;\n  cookieService;\n  mainDataService;\n  // private initializationComplete = new BehaviorSubject<boolean>(false);\n  constructor(http, cookieService, mainDataService) {\n    this.http = http;\n    this.cookieService = cookieService;\n    this.mainDataService = mainDataService;\n  }\n  getFullSecurityApiKey() {\n    return _asyncToGenerator(function* () {\n      const data = `grant_type=client_credentials&client_id=${environment.client_id}&client_secret=${environment.client_secret}&scope=${environment.scope}`;\n      const response = yield fetch(environment.securityApiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: data\n      });\n      if (!response.ok) {\n        throw new Error('Network response was not ok' + response.statusText);\n      }\n      const responseData = yield response.json();\n      let decodedToken = jwt_decode(responseData.access_token);\n      const expires = new Date(decodedToken.exp * 1000);\n      expires.setTime(expires.getTime() + 5 * 60 * 60 * 1000);\n      // this.cookieService.set('accessToken', decodedToken.access_token, {\n      //   expires: expires,\n      //   path: '/',\n      //   sameSite: 'Strict',\n      // });\n      return yield {\n        access_token: responseData.access_token,\n        expires: expires\n      };\n    })();\n  }\n  getPublicApis() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const token = _this.cookieService.get('accessToken') ?? '';\n      const response = yield fetch('https://uat-apps.paysky.io/sc-authenticator' + '/TenantConfiguration/GetPublicApis', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'x-access-token': token\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Network response was not ok ' + response.statusText);\n      }\n      const responseData = yield response.json();\n      return responseData;\n    })();\n  }\n  loadScript(apiKey) {\n    return new Promise((resolve, reject) => {\n      const url = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;\n      const node = document.createElement('script');\n      node.src = url;\n      node.type = 'text/javascript';\n      node.async = true;\n      document.getElementsByTagName('head')[0].appendChild(node);\n      node.onload = () => resolve();\n      node.onerror = () => reject(new Error('Failed to load script'));\n    });\n  }\n  shouldGetGoogleKey(mapKeyExpiry) {\n    const date1 = new Date(mapKeyExpiry);\n    const date2 = new Date();\n    const time1 = date1.getTime();\n    const time2 = date2.getTime();\n    const differenceInMinutes = (time2 - time1) / (1000 * 60);\n    return differenceInMinutes >= 30;\n  }\n  fetchGoogleKey(tenantId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const response = yield fetch(environment.apiEndPoint + '/Auth/TenantConfiguration/GetGoogleKey', {\n        method: 'GET',\n        headers: {\n          tenantId: tenantId && tenantId !== '' ? tenantId : environment.defaultTenant\n        }\n      });\n      const resp = yield response.json();\n      const apiKey = atob(resp.data.key);\n      localStorage.setItem('mapKey', apiKey);\n      localStorage.setItem('mapKeyExpiry', new Date().getTime().toString());\n      yield _this2.loadScript(apiKey);\n    })();\n  }\n  doesCookieExist(cookieName) {\n    return this.cookieService.check(cookieName);\n  }\n  isTokenExpired() {\n    let token = this.cookieService.get('accessToken') ?? '';\n    if (token == '') {\n      return false;\n    }\n    const decodedToken = jwt_decode(token);\n    const expiryTime = decodedToken.exp * 1000;\n    const currentTime = new Date().getTime();\n    return expiryTime - currentTime < 5 * 60 * 1000; // Refresh if token expires in less than 5 minutes\n  }\n\n  fetchGoogleTId() {\n    this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\n      next: res => {\n        localStorage.setItem('GATrackingId', res.data ? res.data[0].value : '');\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  initialize() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const tenantId = localStorage.getItem('tenantId') || environment.defaultTenant;\n      if (!tenantId || tenantId === '') localStorage.setItem('tenantId', tenantId);\n      if (environment.client_id) {\n        if (!_this3.doesCookieExist('accessToken')) {\n          let result = yield _this3.getFullSecurityApiKey();\n          _this3.cookieService.set('accessToken', result.access_token, {\n            expires: result.expires,\n            path: '/',\n            sameSite: 'Strict'\n          });\n          let openApis = yield _this3.getPublicApis();\n          localStorage.setItem('publicApis', JSON.stringify(openApis));\n        }\n      }\n      _this3.fetchGoogleTId();\n      const mapKeyExpiry = localStorage.getItem('mapKeyExpiry');\n      if (mapKeyExpiry && _this3.shouldGetGoogleKey(parseInt(mapKeyExpiry))) {\n        yield _this3.fetchGoogleKey(tenantId);\n      } else {\n        const mapKey = localStorage.getItem('mapKey');\n        if (mapKey) {\n          yield _this3.loadScript(mapKey);\n        } else {\n          yield _this3.fetchGoogleKey(tenantId);\n        }\n      }\n      // this.initializationComplete.next(true);\n    })();\n  }\n\n  static ɵfac = function InitService_Factory(t) {\n    return new (t || InitService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.CookieService), i0.ɵɵinject(i3.MainDataService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: InitService,\n    factory: InitService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "jwt_decode", "InitService", "http", "cookieService", "mainDataService", "constructor", "getFullSecurityApiKey", "_asyncToGenerator", "data", "client_id", "client_secret", "scope", "response", "fetch", "securityApiUrl", "method", "headers", "body", "ok", "Error", "statusText", "responseData", "json", "decodedToken", "access_token", "expires", "Date", "exp", "setTime", "getTime", "getPublicApis", "_this", "token", "get", "loadScript", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "url", "node", "document", "createElement", "src", "type", "async", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "onload", "onerror", "shouldGetGoogleKey", "mapKeyExpiry", "date1", "date2", "time1", "time2", "differenceInMinutes", "fetch<PERSON><PERSON><PERSON><PERSON>ey", "tenantId", "_this2", "apiEndPoint", "defaultTenant", "resp", "atob", "key", "localStorage", "setItem", "toString", "doesCookieExist", "cookieName", "check", "isTokenExpired", "expiryTime", "currentTime", "fetchGoogleTId", "getGoogleAnalyticsTrackingId", "subscribe", "next", "res", "value", "error", "err", "console", "initialize", "_this3", "getItem", "result", "set", "path", "sameSite", "openApis", "JSON", "stringify", "parseInt", "mapKey", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "CookieService", "i3", "MainDataService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\init.service.ts"], "sourcesContent": ["// src/app/services/init.service.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '@environments/environment';\r\nimport { BehaviorSubject, firstValueFrom } from 'rxjs';\r\nimport jwt_decode from 'jwt-decode';\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { MainDataService } from './main-data.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class InitService {\r\n    // private initializationComplete = new BehaviorSubject<boolean>(false);\r\n\r\n  constructor(private http: HttpClient, private cookieService: CookieService,private mainDataService:MainDataService) {}\r\n\r\n  async getFullSecurityApiKey(): Promise<any> {\r\n    const data =\r\n    `grant_type=client_credentials&client_id=${environment.client_id}&client_secret=${environment.client_secret}&scope=${environment.scope}`;\r\n    const response = await fetch(environment.securityApiUrl, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/x-www-form-urlencoded',\r\n      },\r\n      body: data,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Network response was not ok' + response.statusText);\r\n    }\r\n    const responseData = await response.json();\r\n    let decodedToken: any = jwt_decode(responseData.access_token);\r\n    const expires = new Date(decodedToken.exp * 1000);\r\n    expires.setTime(expires.getTime() + 5 * 60 * 60 * 1000);\r\n    // this.cookieService.set('accessToken', decodedToken.access_token, {\r\n    //   expires: expires,\r\n    //   path: '/',\r\n    //   sameSite: 'Strict',\r\n    // });\r\n    return await {access_token:responseData.access_token,expires:expires};\r\n  }\r\n\r\n  async getPublicApis() {\r\n    const token = this.cookieService.get('accessToken')?? '';\r\n    const response = await fetch(\r\n      'https://uat-apps.paysky.io/sc-authenticator' + '/TenantConfiguration/GetPublicApis',\r\n      {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'x-access-token': token,\r\n        },\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Network response was not ok ' + response.statusText);\r\n    }\r\n\r\n    const responseData = await response.json();\r\n    return responseData;\r\n  }\r\n\r\n  loadScript(apiKey: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const url = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;\r\n      const node = document.createElement('script');\r\n      node.src = url;\r\n      node.type = 'text/javascript';\r\n      node.async = true;\r\n      document.getElementsByTagName('head')[0].appendChild(node);\r\n      node.onload = () => resolve();\r\n      node.onerror = () => reject(new Error('Failed to load script'));\r\n    });\r\n  }\r\n\r\n  shouldGetGoogleKey(mapKeyExpiry: number): boolean {\r\n    const date1 = new Date(mapKeyExpiry);\r\n    const date2 = new Date();\r\n    const time1 = date1.getTime();\r\n    const time2 = date2.getTime();\r\n    const differenceInMinutes = (time2 - time1) / (1000 * 60);\r\n    return differenceInMinutes >= 30;\r\n  }\r\n\r\n  async fetchGoogleKey(tenantId: string) {\r\n    const response = await fetch(\r\n      environment.apiEndPoint + '/Auth/TenantConfiguration/GetGoogleKey',\r\n      {\r\n        method: 'GET',\r\n        headers: {\r\n          tenantId:\r\n            tenantId && tenantId !== '' ? tenantId : environment.defaultTenant,\r\n        },\r\n      }\r\n    );\r\n    const resp = await response.json();\r\n    const apiKey = atob(resp.data.key);\r\n    localStorage.setItem('mapKey', apiKey);\r\n    localStorage.setItem('mapKeyExpiry', new Date().getTime().toString());\r\n    await this.loadScript(apiKey);\r\n  }\r\n  doesCookieExist(cookieName: string): boolean {\r\n    return this.cookieService.check(cookieName);\r\n  }\r\n  isTokenExpired(): boolean {\r\n    let token = this.cookieService.get('accessToken')?? '';\r\n    if (token=='') {\r\n      return false;\r\n    }\r\n    const decodedToken: any = jwt_decode(token);\r\n    const expiryTime = decodedToken.exp * 1000;\r\n    const currentTime = new Date().getTime();\r\n    return expiryTime - currentTime < 5 * 60 * 1000; // Refresh if token expires in less than 5 minutes\r\n  }\r\n  fetchGoogleTId() {\r\n    this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\r\n      next: (res: any) => {\r\n        localStorage.setItem('GATrackingId', res.data?res.data[0].value:'');\r\n      }, error: (err: any) => {\r\n        console.error(err)\r\n      }\r\n    })\r\n  }\r\n  async initialize(): Promise<void> {\r\n    const tenantId = localStorage.getItem('tenantId') || environment.defaultTenant;\r\n    if(!tenantId || tenantId === '') localStorage.setItem('tenantId', tenantId)\r\n    if(environment.client_id) {\r\n      if (!this.doesCookieExist('accessToken')) {\r\n        let result = await this.getFullSecurityApiKey();\r\n        this.cookieService.set('accessToken', result.access_token, {\r\n          expires: result.expires,\r\n          path: '/',\r\n          sameSite: 'Strict',\r\n        });\r\n        let openApis =await this.getPublicApis();\r\n        localStorage.setItem('publicApis',JSON.stringify(openApis))\r\n      }\r\n    }\r\n    this.fetchGoogleTId();\r\n\r\n\r\n    const mapKeyExpiry = localStorage.getItem('mapKeyExpiry');\r\n\r\n    if (mapKeyExpiry && this.shouldGetGoogleKey(parseInt(mapKeyExpiry))) {\r\n      await this.fetchGoogleKey(tenantId);\r\n    } else {\r\n      const mapKey = localStorage.getItem('mapKey');\r\n      if (mapKey) {\r\n        await this.loadScript(mapKey);\r\n      } else {\r\n        await this.fetchGoogleKey(tenantId);\r\n      }\r\n    }\r\n    // this.initializationComplete.next(true);\r\n\r\n  }\r\n//   getInitializationComplete$() {\r\n//     return this.initializationComplete.asObservable();\r\n//   }\r\n}\r\n"], "mappings": ";AAGA,SAASA,WAAW,QAAQ,2BAA2B;AAEvD,OAAOC,UAAU,MAAM,YAAY;;;;;AAOnC,OAAM,MAAOC,WAAW;EAGFC,IAAA;EAA0BC,aAAA;EAAqCC,eAAA;EAFjF;EAEFC,YAAoBH,IAAgB,EAAUC,aAA4B,EAASC,eAA+B;IAA9F,KAAAF,IAAI,GAAJA,IAAI;IAAsB,KAAAC,aAAa,GAAbA,aAAa;IAAwB,KAAAC,eAAe,GAAfA,eAAe;EAAmB;EAE/GE,qBAAqBA,CAAA;IAAA,OAAAC,iBAAA;MACzB,MAAMC,IAAI,GACV,2CAA2CT,WAAW,CAACU,SAAS,kBAAkBV,WAAW,CAACW,aAAa,UAAUX,WAAW,CAACY,KAAK,EAAE;MACxI,MAAMC,QAAQ,SAASC,KAAK,CAACd,WAAW,CAACe,cAAc,EAAE;QACvDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;SACjB;QACDC,IAAI,EAAET;OACP,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,GAAGP,QAAQ,CAACQ,UAAU,CAAC;;MAEtE,MAAMC,YAAY,SAAST,QAAQ,CAACU,IAAI,EAAE;MAC1C,IAAIC,YAAY,GAAQvB,UAAU,CAACqB,YAAY,CAACG,YAAY,CAAC;MAC7D,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACH,YAAY,CAACI,GAAG,GAAG,IAAI,CAAC;MACjDF,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACvD;MACA;MACA;MACA;MACA;MACA,aAAa;QAACL,YAAY,EAACH,YAAY,CAACG,YAAY;QAACC,OAAO,EAACA;MAAO,CAAC;IAAC;EACxE;EAEMK,aAAaA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAxB,iBAAA;MACjB,MAAMyB,KAAK,GAAGD,KAAI,CAAC5B,aAAa,CAAC8B,GAAG,CAAC,aAAa,CAAC,IAAG,EAAE;MACxD,MAAMrB,QAAQ,SAASC,KAAK,CAC1B,6CAA6C,GAAG,oCAAoC,EACpF;QACEE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,gBAAgB,EAAEgB;;OAErB,CACF;MAED,IAAI,CAACpB,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,GAAGP,QAAQ,CAACQ,UAAU,CAAC;;MAGvE,MAAMC,YAAY,SAAST,QAAQ,CAACU,IAAI,EAAE;MAC1C,OAAOD,YAAY;IAAC;EACtB;EAEAa,UAAUA,CAACC,MAAc;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,GAAG,GAAG,+CAA+CJ,MAAM,mBAAmB;MACpF,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC7CF,IAAI,CAACG,GAAG,GAAGJ,GAAG;MACdC,IAAI,CAACI,IAAI,GAAG,iBAAiB;MAC7BJ,IAAI,CAACK,KAAK,GAAG,IAAI;MACjBJ,QAAQ,CAACK,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACP,IAAI,CAAC;MAC1DA,IAAI,CAACQ,MAAM,GAAG,MAAMX,OAAO,EAAE;MAC7BG,IAAI,CAACS,OAAO,GAAG,MAAMX,MAAM,CAAC,IAAInB,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACjE,CAAC,CAAC;EACJ;EAEA+B,kBAAkBA,CAACC,YAAoB;IACrC,MAAMC,KAAK,GAAG,IAAI1B,IAAI,CAACyB,YAAY,CAAC;IACpC,MAAME,KAAK,GAAG,IAAI3B,IAAI,EAAE;IACxB,MAAM4B,KAAK,GAAGF,KAAK,CAACvB,OAAO,EAAE;IAC7B,MAAM0B,KAAK,GAAGF,KAAK,CAACxB,OAAO,EAAE;IAC7B,MAAM2B,mBAAmB,GAAG,CAACD,KAAK,GAAGD,KAAK,KAAK,IAAI,GAAG,EAAE,CAAC;IACzD,OAAOE,mBAAmB,IAAI,EAAE;EAClC;EAEMC,cAAcA,CAACC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAApD,iBAAA;MACnC,MAAMK,QAAQ,SAASC,KAAK,CAC1Bd,WAAW,CAAC6D,WAAW,GAAG,wCAAwC,EAClE;QACE7C,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP0C,QAAQ,EACNA,QAAQ,IAAIA,QAAQ,KAAK,EAAE,GAAGA,QAAQ,GAAG3D,WAAW,CAAC8D;;OAE1D,CACF;MACD,MAAMC,IAAI,SAASlD,QAAQ,CAACU,IAAI,EAAE;MAClC,MAAMa,MAAM,GAAG4B,IAAI,CAACD,IAAI,CAACtD,IAAI,CAACwD,GAAG,CAAC;MAClCC,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE/B,MAAM,CAAC;MACtC8B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,IAAIxC,IAAI,EAAE,CAACG,OAAO,EAAE,CAACsC,QAAQ,EAAE,CAAC;MACrE,MAAMR,MAAI,CAACzB,UAAU,CAACC,MAAM,CAAC;IAAC;EAChC;EACAiC,eAAeA,CAACC,UAAkB;IAChC,OAAO,IAAI,CAAClE,aAAa,CAACmE,KAAK,CAACD,UAAU,CAAC;EAC7C;EACAE,cAAcA,CAAA;IACZ,IAAIvC,KAAK,GAAG,IAAI,CAAC7B,aAAa,CAAC8B,GAAG,CAAC,aAAa,CAAC,IAAG,EAAE;IACtD,IAAID,KAAK,IAAE,EAAE,EAAE;MACb,OAAO,KAAK;;IAEd,MAAMT,YAAY,GAAQvB,UAAU,CAACgC,KAAK,CAAC;IAC3C,MAAMwC,UAAU,GAAGjD,YAAY,CAACI,GAAG,GAAG,IAAI;IAC1C,MAAM8C,WAAW,GAAG,IAAI/C,IAAI,EAAE,CAACG,OAAO,EAAE;IACxC,OAAO2C,UAAU,GAAGC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EACnD;;EACAC,cAAcA,CAAA;IACZ,IAAI,CAACtE,eAAe,CAACuE,4BAA4B,EAAE,CAACC,SAAS,CAAC;MAC5DC,IAAI,EAAGC,GAAQ,IAAI;QACjBb,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEY,GAAG,CAACtE,IAAI,GAACsE,GAAG,CAACtE,IAAI,CAAC,CAAC,CAAC,CAACuE,KAAK,GAAC,EAAE,CAAC;MACrE,CAAC;MAAEC,KAAK,EAAGC,GAAQ,IAAI;QACrBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EACME,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7E,iBAAA;MACd,MAAMmD,QAAQ,GAAGO,YAAY,CAACoB,OAAO,CAAC,UAAU,CAAC,IAAItF,WAAW,CAAC8D,aAAa;MAC9E,IAAG,CAACH,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAEO,YAAY,CAACC,OAAO,CAAC,UAAU,EAAER,QAAQ,CAAC;MAC3E,IAAG3D,WAAW,CAACU,SAAS,EAAE;QACxB,IAAI,CAAC2E,MAAI,CAAChB,eAAe,CAAC,aAAa,CAAC,EAAE;UACxC,IAAIkB,MAAM,SAASF,MAAI,CAAC9E,qBAAqB,EAAE;UAC/C8E,MAAI,CAACjF,aAAa,CAACoF,GAAG,CAAC,aAAa,EAAED,MAAM,CAAC9D,YAAY,EAAE;YACzDC,OAAO,EAAE6D,MAAM,CAAC7D,OAAO;YACvB+D,IAAI,EAAE,GAAG;YACTC,QAAQ,EAAE;WACX,CAAC;UACF,IAAIC,QAAQ,SAAQN,MAAI,CAACtD,aAAa,EAAE;UACxCmC,YAAY,CAACC,OAAO,CAAC,YAAY,EAACyB,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAC;;;MAG/DN,MAAI,CAACV,cAAc,EAAE;MAGrB,MAAMvB,YAAY,GAAGc,YAAY,CAACoB,OAAO,CAAC,cAAc,CAAC;MAEzD,IAAIlC,YAAY,IAAIiC,MAAI,CAAClC,kBAAkB,CAAC2C,QAAQ,CAAC1C,YAAY,CAAC,CAAC,EAAE;QACnE,MAAMiC,MAAI,CAAC3B,cAAc,CAACC,QAAQ,CAAC;OACpC,MAAM;QACL,MAAMoC,MAAM,GAAG7B,YAAY,CAACoB,OAAO,CAAC,QAAQ,CAAC;QAC7C,IAAIS,MAAM,EAAE;UACV,MAAMV,MAAI,CAAClD,UAAU,CAAC4D,MAAM,CAAC;SAC9B,MAAM;UACL,MAAMV,MAAI,CAAC3B,cAAc,CAACC,QAAQ,CAAC;;;MAGvC;IAAA;EAEF;;;qBAjJWzD,WAAW,EAAA8F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;EAAA;;WAAXrG,WAAW;IAAAsG,OAAA,EAAXtG,WAAW,CAAAuG,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}