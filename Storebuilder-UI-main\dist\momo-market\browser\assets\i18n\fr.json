{"header": {"cart": "<PERSON><PERSON>", "deliverTo": "<PERSON><PERSON> à", "hi": "Y'ello", "login": "Se connecter", "logout": " Se Déconnecter", "signIn": "Se connecter", "signup": "S'inscrire", "wishList": "Liste de favoris", "yourAddresses": "<PERSON><PERSON>", "yourDetails": "<PERSON><PERSON>", "yourOrders": "Mes Commandes", "myAddresses": "<PERSON><PERSON>"}, "navbar": {"all": "<PERSON>ut", "home": "Accueil", "Wishlist": "Liste de favoris", "Categories": "Catégories", "Cart": "<PERSON><PERSON>", "Profile": "Profil"}, "buttons": {"addreview": "Ajouter un commentaire", "addToCart": "A<PERSON>ter au panier", "close": "<PERSON><PERSON><PERSON>", "readLess": "Voir moins", "readMore": "Voir plus", "shopNow": "Acheter maintenant", "viewAll": "Voir tout", "back": "p<PERSON><PERSON><PERSON>"}, "sideMenu": {"allMerchants": "Tous les marchands", "browseAllCategories": "Parcourir toutes les catégories", "categories": "Catégories", "customerService": "Customer Service", "help": "Aider", "home": "Accueil", "merchants": "<PERSON><PERSON><PERSON>", "signIn": "Se connecter", "signOut": "Se Déconnecter", "yourAccount": "<PERSON><PERSON>", "yourAddresses": "<PERSON><PERSON>"}, "account": {"address": {"addAddress": "Ajoutez l'adresse", "addAddressMessage": "L'adresse a été ajoutée avec succès", "building": "<PERSON><PERSON><PERSON><PERSON> ", "city": "Ville", "country": "Pays", "defaultAddress": "Définir comme par défaut", "first-name": "Prénom", "instructions": "Instructions de livraison (facultatif)", "landMark": "Près d'un point de repère", "last-name": "Nom de famille ", "optional": "(Facultatif)", "phone": "Numéro de téléphone ", "post-code": "Code Postal", "search": "Tapez votre adresse", "state": "État", "street": "Rue/Point de repère le plus proche", "updateAddress": "Mettre à jour l'adresse"}, "details": {"change": "<PERSON>z", "default": "Défaut", "delivery": "<PERSON><PERSON><PERSON>", "DeliveryOption": "Option de livraison", "email": "E-mail", "firstName": "Prénom", "lastName": "Nom de famille", "newSecondaryPhone": "Nouveau numéro de portable", "phoneNumber": "Numéro de téléphone", "secondaryPhoneNumber": "Numéro de téléphone secondaire", "secretCode": "Code Secret", "shippingDetails": "<PERSON><PERSON><PERSON> livraison", "yourDetails": "<PERSON><PERSON>", "requestReturn": "<PERSON><PERSON><PERSON> de retour", "addPhoneNumber": "Ajouter un numéro de portable"}, "index": {"logout": "Se Déconnecter", "yourAccount": "<PERSON><PERSON>", "yourAddresses": "<PERSON><PERSON>", "yourOrders": "Mes Commandes", "yourDetails": "Paramètres du Profil", "profile": "Mon Profil", "lang": "<PERSON><PERSON>", "country": "Pays", "language": "<PERSON><PERSON><PERSON><PERSON>", "myAddresses": "<PERSON><PERSON>", "myAccount": "Mon Compte"}}, "addingAddress": {"RecipientContactNumber": "Numéro de contact du destinataire", "phoneNote": "Il s’agit du numéro que le livreur utilisera pour contacter le destinataire au moment de la livraison.", "addAddress": "Ajouter une adresse", "addNewAddress": "Ajouter une nouvelle adresse", "confirmAddress": "Confirmer l'adresse", "addressLabel": "Étiquette d'adresse", "building": "Apt, Suite, Unité, bâtiment (facultatif)", "city": "Ville", "country": "Pays", "defaultAddress": "DÉFINIR PAR DÉFAUT", "first-name": "Prénom", "instructions": "Instructions de livraison", "landMark": "Point de repère le plus proche", "last-name": "Nom", "optional": "(facultatif)", "otherAddress": "Ligne d'adresse 1", "otherAddressSecond": "Ligne d'adresse 2", "phone": "Numéro de téléphone", "post-code": "Code postal", "search": "<PERSON><PERSON><PERSON>", "state": "État", "street": "Adresse postale ou case postale", "updateAddress": "CONFIRMER L’ADRESSE", "updateAddressTitle": "Mettre à jour l’adresse", "region": "Region", "addressDetails": "<PERSON><PERSON><PERSON> de l'adresse"}, "addressModal": {"addAddress": "AJOUTER UNE NOUVELLE ADRESSE", "default": "<PERSON><PERSON> <PERSON><PERSON>", "yourAddresses": "Vos adresses", "updateAddresses": "Mettre à jour l'adresse", "shippingAddress": "<PERSON><PERSON><PERSON> adresse de liv<PERSON>", "confirmAddress": "Confirmer l'adresse"}, "addressOtherModal": {"addressLabel": "Étiquette d'adresse", "labelName": "Nom de l'étiquette", "add": "Ajouter", "cancel": "Annuler"}, "auth": {"otp": {"next": "Suivant", "resend": "<PERSON><PERSON><PERSON>", "resendIn": "<PERSON><PERSON><PERSON> dans", "subTitle": "Nous venons d'envoyer le code de vérification", "title": "Saisissez le code OTP"}, "registerPassword": {"referralBy": "Réf<PERSON><PERSON><PERSON>", "referralByErrors": "La valeur maximale doit être de 50 caractères", "agreeText": "je suis d'accord avec le", "confirmPassword": "Confirmez le mot de passe", "continue": "<PERSON><PERSON><PERSON><PERSON>", "signUp": "Inscription", "email": "e-mail", "firstName": "prénom", "lastName": "nom de famille", "password": "le mot de passe", "phoneNumber": "numéro de téléphone", "subTitle": "Trouvez tout ce que vous aimez sur la place de marché Mo<PERSON>, la plus grande place de marché en ligne au monde.", "termsAndConditions": "Te<PERSON><PERSON> et conditions", "title": "S'inscrire", "validation1": "Au moins 10 caractères de longueur", "validation2": "Lettre minuscule (a-z)", "validation3": "Lettre majuscule (A-Z)", "validation4": "Au moins un numéro (0-9)", "validation5": "Au moins un caractère spécial (!@#$%^&*()-_=+[]{};:,./?)", "validation6": "L'e-mail doit être une adresse e-mail valide", "validation7": "Confirmer le mot de passe doit correspondre au mot de passe", "validation8": "Mot de passe incorrect! Veuillez choisir un mot de passe plus fort", "signup": "S'inscrire", "signin": "Se connecter", "desc": "Trouvez tout ce que vous aimez sur la place de Marché par <PERSON>, la plus grande place de marché en ligne au monde", "haveAccount": "Vous avez déjà un compte ?", "registerSuccessMsg": "Votre compte a été créé avec succès", "okButton": "Commencer vos achats !", "termsAndConds": "Conditions d’utilisation et avis de confidentialité de MoMo.", "agreementText": "En continuant, vous acceptez", "backToLogin": "Retour à la connexion"}, "optInCheckBox": "Recevez des offres exclusives, des nouveautés et des conseils d'initiés directement dans votre boîte mail ! Abonnez-vous à notre newsletter et ne manquez aucune offre exceptionnelle."}, "cart": {"cartDetail": {"cantDeliverLocationMessage": "*Impossible de livrer à votre emplacement", "cantDeliverMessage": "*Impossible de livrer ce produit à votre adresse", "delete": "<PERSON><PERSON><PERSON><PERSON>", "moveWishList": "<PERSON><PERSON><PERSON><PERSON> vers la liste de souhaits", "notAvailable": "NON DISPONIBLE", "outOfStock": "This product is out of stock", "Qty": "Quantité", "color": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "size2": "Taille2"}, "emptyCart": {"cartEmpty": "Votre panier est vide", "cartEmptyMessage": "Ajoutez des articles pour commencer", "startShopping": "Allez! On commence les courses!", "checkSpelling": "- Vérifiez l'orthographe pour les erreurs de frappe", "emptyCategories": "Votre catégorie est vide", "noResultFound": "Aucun résultat trouvé pour", "searchGeneralTerms": "- Essayez de rechercher des termes plus généraux - vous pouvez ensuite filtrer les résultats de la recherche", "shortWords": "- Essayez de rechercher avec des mots-clés courts et simples", "whatAreYouWaitingFor": "<PERSON><PERSON><PERSON><PERSON>ez-vous", "goToHome": "Aller à la page d'accueil", "addItems": "Ajoutez des articles pour commencer"}, "index": {"cartWarning": "Le fait de placer un produit dans le panier ne réserve pas ce produit ni son prix. Nous ne réservons le produit qu'une fois le paiement reçu.", "continueShopping": "Continuer vos achats", "price": "PRIX", "products": "PRODUITS", "quantity": "QUANTITÉ", "v2": {"cart": "<PERSON><PERSON>"}, "yourCart": "<PERSON><PERSON><PERSON> panier"}}, "categories": {"allCategories": "Toutes nos catégories", "categoryNotFound": "La catégorie n'est pas trouvée.", "productNotFound": "Aucun produit trouvé", "goToHomePage": "Go to Home Page"}, "categoryCard": {"items": "éléments", "products": "Produits"}, "changePassword": {"changePassword": "Changer le mot de passe", "confirmPassword": "Confirmez le mot de passe", "newPassword": "nouveau mot de passe", "oldPassword": "ancien mot de passe", "phoneNumber": "Numéro de téléphone", "securityPolicy": "Pour la politique de sécurité, vous ne changez pas votre mot de passe à partir de 90 jours, veuille<PERSON> le mettre à jour"}, "checkout": {"noItemsInCart": "Aucun article dans le panier", "checkoutLabel": "Caisse", "ShippingAddress": "adresse de l<PERSON>", "DeliveryOption": "option de livraison", "deliveryMethod": {"area": "Zone", "change": "Changer", "checkout": "commander", "city": "Ville", "default": "<PERSON><PERSON> <PERSON><PERSON>", "deliverOption": "Option de livraison", "deliveryMethod": "Veuillez sélectionner votre mode de livraison préféré", "fromTo": " De 2 à 3 jours", "location": "Emplacement", "mobileNumber": "Numéro de téléphone", "nextDay": "Le lendemain", "sameDay": "Le jour même", "shipping": "Livraison à domicle", "shippingAddress": "<PERSON><PERSON><PERSON>", "contactNo": "Numéro de contact", "infoMessage": "Ce numéro sera utilisé par le transporteur pour vous contacter", "paymentMethod": "Option de paiement", "paymentOption": "Option de paiement", "paymentOptionDesktop": "Option de paiement", "momoPayLabel": "MoMo Pay", "cardLabel": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>"}, "index": {"deliveryMethod": "Mode de livraison", "orderSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commande"}, "item": "article", "items": "article(s)", "orderPlaced": {"deliveryoption": "Option de livraison", "item": "Article", "myOrders": "<PERSON><PERSON> commandes", "orderPlaced": " Commande passée", "paymentMethod": "Mode de paiement", "paymentTotal": "Montant Total", "shipping": "Frais de livraison", "thanksForOrdering": "<PERSON>rci d'avoir fait vos achats sur MoMoMarket, voici le récapitulatif de votre commande", "thanksForOrderingMomo": "Merci d'avoir effectué vos achats sur MoMoMarket, voici le récapitulatif de votre numéro de commande", "totalItemCost": "<PERSON><PERSON> <PERSON> commande", "vat": "T.V.A.", "mobilHeader": "Votre commande a été passée avec succès", "mobilP": "Merci d'avoir effectué vos achats sur MoMoMarket, voici le récapitulatif de votre numéro de commande", "total": "Sous-total", "shippingFee": "Frais de livraison", "discount": "<PERSON><PERSON><PERSON>", "totalMoney": "Totale", "payment": "Mode de paiement", "delivery": "Option de livraison"}, "orderSummary": {"delivery": "<PERSON><PERSON><PERSON>", "deliveryOption": "Option de livraison", "item": "Prix ​​total des articles", "subTotal": "Sous-total", "shippingFees": "Frais de livraison", "orderPlaced": "Commande passée", "paymentMethod": "Méthode de paiement", "paymentTotal": "Total du paiement", "shipping": "Expédition", "thanksForOrdering": "Merci d'avoir fait vos achats sur MoMoMarket. Voici le résumé de votre commande", "total": "Total", "totalItemCost": "Coût total des articles", "usuallyDeliveredWithin": "Généralement livré sous 2 jours ouvrés", "vat": "TVA", "discount": "Réduction"}, "paymentCart": {"arrives": "Date de livraison estimée", "proceed": "Procéder au paiement", "PayNow": "Payer maintenant", "loadingMessage": "<PERSON><PERSON><PERSON>z patienter, votre transaction est en cours de traitement"}, "paymentDialog": {"123": "123", "12345678": "12345678", "afriElec": "AfriElec", "allInformationsSafe": "vos informations sont sûres et sécurisées", "cardholderName": "Nom du titulaire", "cardNumber": "Numéro de carte", "dontHaveMomo": "Je n'ai pas encore MoMo", "howItWorks": "Comment ça fonctionne", "merchantName": "Nom du commerçant", "merchantRef": "<PERSON><PERSON><PERSON> marchand", "mm/yy": "MM/YY", "paymentTotal": "Total du paiement", "payWith": "Payer avec", "phoneNumber": "Numéro de téléphone", "saveCardForLater": "Enregistrer la carte pour plus tard", "sign-upNow": "S'inscrire maintenant", "youNeedToHaveMomo": "Vous devez avoir un compte MoMo pour continuer. Nous vous enverrons une demande via l'application mobile et USSD."}, "proceedToCheckout": {"itemsCost": "<PERSON><PERSON>", "multipleItems": "Articles", "orderSummary": "Recapitula<PERSON><PERSON> de votre commande", "proceedTo": "Commander", "singleItem": "Article", "vat": "T.V.A."}}, "contactUs": {"contactDetails": "<PERSON><PERSON><PERSON> du contact", "contactUs": "Nous contacter", "contactUsTitle": "Besoin d’aide", "contactUsMobileTemplate": "Contactez notre équipe", "emailAddress": "Adresse électronique", "email": "Email", "firstName": "Prénom", "getInTouchWith": "Entrez en contact", "lastName": "Nom de famille", "message": "votre message", "mobileNumber": "Numéro de téléphone", "sendMessage": "So<PERSON><PERSON><PERSON>", "getInTouchWithUs": "Prenez contact avec nous"}, "deleteCart": {"cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "sureMesg": "Êtes-vous sûr de vouloir supprimer cet article ?"}, "optOutModal": {"close": "<PERSON><PERSON><PERSON>", "message": "Certains articles dans votre panier ne bénéficient plus des prix promotionnels", "cartMessage": "les articles dans votre panier ne bénéficient plus des prix promotionnels"}, "cpl": {"first": "Vous pouvez acheter jusqu'à", "second": "articles au prix réduit.", "third": "Les articles supplémentaires seront au prix normal.", "fourth": "Veuillez vérifier votre panier avant de continuer."}, "mobileCartModal": {"cancel": "Annuler", "move": "<PERSON><PERSON><PERSON><PERSON> vers la liste de souhaits", "removeFromCart": "<PERSON><PERSON><PERSON> du panier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "suremovewishlist": "Êtes-vous sûr de vouloir déplacer cet article vers la liste de souhaits ?", "sureRemoveCart": "Êtes-vous sûr de vouloir supprimer cet article ?"}, "deleteItemPopupComponent": {"?": "?", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteAddress": "Êtes-vous sûr de vouloir supprimer cette adresse", "deleteNumber": "Êtes-vous sûr de vouloir supprimer ce numéro de mobile", "deleteSelected": "Voulez-vous supprimer la sélection", "yesDelete": "<PERSON><PERSON>, supprimer"}, "ErrorMessages": {"fetchError": "Erreur de récupération", "loginExpired": "Connexion expirée", "pleaseLoginAgain": "Login Expired,Please login again", "unauthrizedAction": "Action non autorisée", "pleaseLoginFirst.": "Action non autorisée, veuillez d'abord vous connecter.", "pleaseContactCallCenter": "Votre commande ne peut pas être soumise, ve<PERSON><PERSON><PERSON> contacter le centre d'appels", "mustSelectCancelationReason": "<PERSON><PERSON> sélectionner le motif d'annulation", "mustEnterPassword": "Doit entrer le mot de passe", "orderCancellationFailed": "Order Cancellation Failed", "errorWhileCancelYourOrder": "erreur lors de l'annulation de votre commande, veuil<PERSON><PERSON> réessayer.", "orderRefundRequestFailed": "La demande de remboursement de la commande a échoué", "errorWhileTheRefundRequestProcess": "erreur lors du processus de demande de remboursement, veuillez réessayer.", "pleaseEnterValidReview": "Veuillez saisir un avis valide", "invalidOtp": "Non valide OTP", "mobileRequired": "Mobile requis", "emailRequired": "Email requis", "invalidUserNameOrPassword": "Nom d'utilisateur ou mot de passe invalide", "mobileNumberAlreadyRegisteredBefore": "Le numéro de portable est déjà enregistré", "pleaseEnterValidEmailAddress": "Veuillez saisir une adresse e-mail valide", "phoneNumberIsUnvalid": "Le numéro de téléphone n'est pas valide", "outOfDeliveryArea": "hors zone de livraison", "InsuficientQuantity": "Quantité insuffisante pour le produit", "exceedOperations": "Dépasser le nombre d'opérations par heure", "noProducts": "Il n'y a pas de produit", "contactUsErrorMessage": "Veuillez réessayer plus tard.", "errorReadingImage": "Erreur de lecture du fichier image", "mustSelectReturnReason": "<PERSON><PERSON> de<PERSON> sélectionner le motif du retour", "mustUploadItemImage": "V<PERSON> devez télécharger une image de l'article", "mustUploadItemImageSize": "La taille maximale de l'image/vidéo a été atteinte", "errorWhileReturnYourOrder": "<PERSON><PERSON>ur lors du retour de votre commande, veuil<PERSON><PERSON> réessayer.", "orderReturnedFailed": "Échec du retour de commande", "itemPerCustomerError": "Impossible d'ajouter des produits à plus de ", "itemPerCustomerErrorNext": " produits", "uploadItemFailed": "Échec du téléchargement de l'élément", "itemUploadAllowed": "<PERSON><PERSON>s les fichiers PNG, JPG ou MP4 sont autorisés."}, "featureType": {"bestseller": "Best-seller", "categories": "Catégories", "hotdeals": "Bonnes affaires", "newarrival": "Nouvelle arrivee"}, "floationPanel": {"addToCart": "A<PERSON>ter au panier", "buyNow": "Acheter maintenant", "notifyMe": "M'INFORMER"}, "footer": {"copyRightsMobile": "Tous droits réservés à Group FinCommerce Inc. en République d'Afrique du Sud", "aboutPaysky": "À propos de Yalla", "aboutUs": "À propos de nous", "account": "<PERSON><PERSON><PERSON>", "backToTop": "En haut", "career": "Carrières", "company": "Entreprise", "conditionsOfUse": "Conditions d'utilisation", "contactUs": "Contactez-nous", "getSellerMobileApp": "Obtenir l'application mobile vendeur", "help": "Aide", "myAddress": "<PERSON>", "myDetails": "Mes <PERSON>", "myOrders": "Mes Commandes", "privacyPolicy": "Politique de confidentialité", "rightsReserved": "© 2023, march<PERSON>. tous droits réservés", "sellerHub": "Espace vendeur", "sellOnMarketplace": "Vendre sur la place de marché", "sellOnMoMoButton": "Vendre sur MoMo Market", "sellOnMomoMarket": "Vendre sur MomoMarket", "shippingAndReturn": "Expédition et retours", "termsAndConditions": "Te<PERSON><PERSON> et conditions", "termsandConditions": "Te<PERSON><PERSON> et conditions", "weAccept": "Nous acceptons", "careers": "Carrières", "becomeSeller": "<PERSON><PERSON><PERSON> vendeur", "contactWithUs": "Contactez-nous", "reachOut": "<PERSON><PERSON><PERSON>", "returnAndRefund": "Retour et Remboursement", "disputeResolution": "Résolution des Litiges", "warrantyPolicy": "Politique de Garantie", "faq": "Questions Fréquemment Po<PERSON>ées", "NewsletterPreferences ": "Préférences de la newsletter"}, "landing": {"BestSeller": "<PERSON><PERSON><PERSON>", "browseAllProduct": "Parcourir tous les produit", "HotDeals": "Offres Spéciales", "NewArrival": "Nouveautés", "seeMore": "Voir plus", "seeAll": "Voir tout"}, "landingNavbar": {"liveMerchants": "Commerçants en direct", "sellOnMarketplace": "Vendre sur la place de marché", "sellOnYallaMall": "Vendre sur Yalla Mall"}, "landingPage": {"clickOnCountry": "Cliquez sur votre pours accéder au MoMo Market", "searchCountry": "Recherchez un pays", "selectCountry": "Sélectionnez un pays", "noCountryFound": "Aucun pays trouvé"}, "landingPageSearch": {"brands": "Marques", "clear": "<PERSON><PERSON><PERSON><PERSON>", "colors": "Couleurs", "price": "Prix", "refineBy": "Affiner par", "searchFilter": "<PERSON><PERSON><PERSON>", "searchFor": "<PERSON><PERSON><PERSON> ", "shortBy": "Trier par :"}, "language": {"English": "English", "French": "Français"}, "merchant": {"merchants": "<PERSON><PERSON><PERSON>", "searchMerchant": "Rechercher un marchand"}, "messageModal": {"pleaseSignInAgain": "<PERSON><PERSON><PERSON><PERSON> vous connecter à nouveau"}, "mobileModal": {"addNumber": "Ajouter un numéro", "default": "<PERSON><PERSON> <PERSON><PERSON>", "yourMobileNumber": "Vos numéros de portable"}, "multipleAddress": {"address": "<PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON>-vous supprimer cette adresse", "default": "<PERSON><PERSON> <PERSON><PERSON>", "other": "<PERSON><PERSON>", "yourAddresses": "Vos adresses", "myAddresses": "<PERSON><PERSON>", "Addresses": "Adresses", "selectAddress": "<PERSON><PERSON><PERSON><PERSON>ner une adresse", "confirmAddress": "Confirmer l'adresse", "completeAddress": "Compléter les détails de l'adresse"}, "newUser": {"verifyUser": {"addNewNumber": "Ajouter un nouveau numéro", "confirmChangeDefaultNumber": "Confirmer le changement du numéro par défaut", "continue": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "proceed": "<PERSON><PERSON><PERSON>", "next": "Suivant", "enterPassword": "Entrer le mot de passe", "password": "le mot de passe", "passwordConfirm": "Entrer votre mot de passe pour confirmer le changement de numéro par défaut", "OK": "<PERSON><PERSON><PERSON>"}}, "notFound": {"pageNotFound": "Page non trouvée"}, "order": {"At": "À", "allOrders": "Toutes les commandes", "Cancelled": "<PERSON><PERSON><PERSON>", "Delivered": "Livrée", "InTransit": "En transit", "orderItems": "articles", "orderItemsCount": "La commande a", "orderNo": "<PERSON><PERSON><PERSON><PERSON> de commande", "OrderPlaced": "Commande passée", "orderStatus": "Statut de la commande", "Pending": "En attente", "Processing": "En cours de traitement", "refresh": "Actualiser", "Refunded": "Remboursée", "RefundRequested": "Remboursement demandé", "Successful": "<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "Voir les détails", "yourOrders": "Mes Commandes", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "yourAccount": "<PERSON><PERSON>", "Succeeded": "<PERSON><PERSON><PERSON><PERSON>", "orderDate": "Date de commande", "RequestReturn": "<PERSON><PERSON><PERSON> de retour", "Returned": "<PERSON><PERSON><PERSON><PERSON>", "noOrder": "Aucune commande trouvée", "noOrderMessage": "Ajoutez des articles pour commencer", "UnderProcessing": "En cours de traitement", "ReturnRejected": "Retour rejeté", "ReturnInProgress": "Retour en cours"}, "orderDetails": {"orderDetailsBox": "Reçu de paiement de commande", "invalidOrderReceipt": "<PERSON><PERSON><PERSON> de commande invalide", "cannotDownloadReceipt": "Impossible de télécharger le reçu. Le reçu de commande est invalide ou non disponible.", "addReview": "Ajouter un commentaire", "amount": "<PERSON><PERSON>", "amountDetails": "<PERSON><PERSON><PERSON> du montant", "cancelItem": "Annuler l'article", "cancelOrder": "Annuler la commande", "cancelOrderConfirmQuestions": "Êtes-vous sûr(e) de vouloir annuler cette commande?", "cancelOrderReason": "<PERSON><PERSON><PERSON> d'annulation", "cancelReason": "Veuillez indiquer le motif de l'annulation", "cancelSingleOrderConfirmQuestions": "Êtes-vous sûr(e) de vouloir annuler cet article?", "cancelThisItem": "Annuler cet article", "explainMore": "Expliquez da<PERSON>", "itemCancelled": "Article annulé", "itemsAmount": "<PERSON><PERSON> de votre commande", "notificationSent": " Nous avons informé le marchand et vous recontacterons une fois qu'il aura accepté ou refusé la demande", "ok": "<PERSON><PERSON><PERSON>", "orderCancelled": "Commande annulée", "mustSelectCancelationReason": "<PERSON><PERSON> sélectionner le motif d'annulation", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "orderNo": "N ° de commande", "password": "Mot de passe", "paymentDetails": "Détails de paiement", "paymentTotal": "Montant total", "proceed": "<PERSON><PERSON><PERSON>", "rateAndReview": "Notez et évaluez", "reasonForCancellation": "Raison pour l'annulation", "reasonForRefund": "Raison du remboursement", "refundAmount": "Montant du remboursement", "refundOrder": "<PERSON><PERSON><PERSON> de rembo<PERSON>", "refundReasonIsRequired": "Le motif du remboursement est requis", "refundRequestSent": "<PERSON><PERSON><PERSON> de remboursement envoyée", "requestRefund": "<PERSON><PERSON><PERSON> de rembo<PERSON>", "reviewRequestSent": "Demande d'examen envoyée// Demande de remboursement envoyée", "selectTheProduct": "Sélectionnez le(s) produit(s) dont vous souhaitez demander le remboursement", "shipmentStatus": "Expédition statut", "shipmentTracking": "Suivi des expéditions", "shipping": "Frais de livraison", "shippingDetails": "Les détails d'expédition", "sku": "SKU", "takeTheTime": "Prenez le temps de nous dire ce que vous pensez du temps que vous avez reçu", "transactionID": "identifiant de transaction", "transactionPhone": "Numéro de portable", "transactionStatus": "État de la transaction", "type": "Mode de paiement", "typeCard": "<PERSON><PERSON>", "typeMomo": "MoMo Pay", "vat": "T.V.A.", "weAreProcessingTheRefund": "Nous traitons le remboursement, cela peut prendre jusqu'à 72 heures", "yourReview": "<PERSON><PERSON><PERSON> avis", "requestReturn": "<PERSON><PERSON><PERSON> de retour", "returnReason": "<PERSON><PERSON><PERSON> du retour", "confirmReason": "Confirm<PERSON> le retour", "orderRequestReturn": "<PERSON><PERSON><PERSON> de demande de retour de commande", "selectReturnReason": "Sélectionner le motif de retour", "upload": "Télécharger", "uploadImageVideo": "Télécharger une image ou une vidéo", "areYouSureWantReturnOrder": "Êtes-vous sûr de vouloir retourner cette commande ?", "areYouSureWantReturnItem": "Êtes-vous sûr de vouloir retourner cet article ?", "returnRequestSent": "<PERSON><PERSON><PERSON> de retour envoyée", "notifiedMerchantReturnedRequest": "Nous avons informé le marchand et vous reviendrons une fois qu'il aura accepté ou décliné la demande.", "returnItem": "Retourner l'article", "refundSuccessfullyProcessed": "Re<PERSON><PERSON><PERSON>", "pickUpPlace": "<PERSON><PERSON> <PERSON>", "totalAmount": "montant total", "uploadImage": "Télécharger une image ou une vidéo", "requestRefundOrder": "De<PERSON><PERSON> le remboursement de la commande", "requestRefundMessage": "Êtes-vous sûr de vouloir rembourser cette commande?", "Back": "dos", "next": "suivante", "return": "Retour", "numberOfCancelledItem": "Nombre d'articles annulés", "numberOfReturnedItem": "Nombre d'articles retournés", "returnFromPickedUpDeliveredAddress": "Le retour sera récupéré à l'adresse de livraison", "back": "Retour", "discount": "<PERSON><PERSON><PERSON>"}, "otp": {"enterOTP": "Entrer OTP", "resend": "<PERSON><PERSON><PERSON>", "resendIn": "<PERSON><PERSON><PERSON> dans", "verificationCodeSent": "Nous venons d'envoyer le code de vérification"}, "phoneNumber": {"verifyMobile": {"addNew": "Ajouter un nouveau numéro de mobile", "changeDefaultNumber": "Change Default Number", "mobileNumber": "Numéro de portable"}}, "primeng": {"medium": "<PERSON><PERSON><PERSON>", "passwordPrompt": "Entrer un mot de passe", "strong": "Forte", "weak": "Faible"}, "productCard": {"instock": "En stock", "soldOut": "Rupture de stock"}, "productDetails": {"details": {"aboutThisProduct": "À propos de ce produit", "addToCart": "A<PERSON>ter au panier", "basedOn": "Basé sur", "bestSeller": "MEILLEURE VENTE", "bestseller": "<PERSON><PERSON><PERSON> vente", "buyNow": "Acheter maintenant", "colors": "Couleurs", "customerRating": "Évaluation des clients", "customerReviews": "Avis des clients", "description": "La description", "dimension": "Dimension", "dx": "L x", "female": "<PERSON>mme", "gender": "Genre", "h": "H", "height": "<PERSON><PERSON>", "hot": "CHAUD", "inStock": "en stock", "length": "<PERSON><PERSON><PERSON>", "linkIsCopiedSuccessfully": "Le lien a été copié avec succès", "male": "<PERSON><PERSON><PERSON>", "merchantName": "Nom du commerçant", "multiColor": "Multicolore", "newArrivals": "NOUVEAUTÉS", "off": "de réduction", "orCopyLink": "Ou copiez le lien", "outOfStock": "<PERSON><PERSON><PERSON><PERSON>", "overallRating": "Note globale", "productDetails": "Détails du produit", "ProductShareWithYourFriend": "Si vous aimez ce produit, partagez-le avec vos amis.", "ratings": "notes", "review": "<PERSON><PERSON>", "sale": "Bon plan", "shareThisProduct": "Partager ce produit", "sizeGuide": "Guide des tailles", "sku": "Merchant SKU", "specification": "Spécification", "uniSex": "Unisex", "verifiedPurchase": "Achat vé<PERSON>", "weight": "Poids", "width": "<PERSON><PERSON>", "wx": "l x", "notifyMe": "M'INFORMER", "leftInStock": "reste en stock", "leftInStockMultiple": "restent en stock", "brand": "Marque", "only": "Seulement", "itemsLeft": "éléments restants", "skuAutoGenerated": "SKU"}}, "register": {"authenticateYourself": "Nous vous enverrons un OTP pour vous authentifier", "next": "Suivant", "phoneNumber": "Numéro de téléphone", "register": "S'inscrire", "login": "Se connecter", "mobileNumber": "Mobile number", "continuer": "<PERSON><PERSON><PERSON>", "alreadyHaveAccount": "Vous avez déjà un compte ?", "signUp": "Inscription", "content": "Trouvez tout ce que vous aimez sur la place de marché Mo<PERSON>, la plus grande place de marché en ligne au monde "}, "resetPassword": {"authenticateYourself": "Nous vous enverrons un OTP pour vous authentifier", "next": "Suivant", "phoneNumber": "Numéro de téléphone", "resetPassword": "Réinitialiser le mot de passe"}, "ResponseMessages": {"orderIsInValid": "La commande contient des produits non valides.", "address": "Addresse", "addressAddedSuccessfully": "<PERSON><PERSON><PERSON> a<PERSON> avec succès", "addressUpdatedSuccessfully": "<PERSON>resse mise à jour avec succès", "cart": "<PERSON><PERSON>", "changePassword": "changer le mot de passe", "contactUsSuccessMessage": "Merci de rester en contact! Nous apprécions que vous nous contactiez.", "defaultAddressSuccessfully": "Adresse par défaut ajou<PERSON>e", "invalidCityAddress": "La ville n’est définie dans aucune région. Veuillez mettre à jour votre adresse.", "loggedInSuccessfully": "Connecté avec succès", "login": "Connexion", "newPrimarySuccessMessage": "Changement du numéro par dé<PERSON><PERSON> r<PERSON>i", "newSecondaryDeleteSuccessMessage": "Numéro de téléphone secondaire supprimé.", "newSecondarySuccessMessage": "Nouveau numéro de mobile ajouté avec succès", "okButtonText": "OK", "passwordExpirationChange": "Modification de l'expiration du mot de passe", "phoneNumberIsValid": "Le numéro de téléphone est valide", "pleaseProvideYourAddress": "Veuillez fournir votre adresse", "register": "Enregistrement", "registerSuccess": "Utilisateur c<PERSON>é avec succès", "successfullyAddedToCart": "Ajouté au panier avec succès", "successfullyAddedToWishList": "Ajouté avec succès à liste de souhaits", "successfullyDeletedFromCart": "Supprimé du panier avec succès", "successfullyRemovedToWishList": "Ajouté avec succès à liste de souhaits", "successfullyUpdatedFromCart": "Mise à jour réussie depuis le panier", "successfullyRemovedFromCart": "<PERSON><PERSON> mis à jour avec succès", "wishList": "Liste de souhaits"}, "search": {"in": "dans", "searchOnMoMoMarket": "Rechercher sur le marché par MoMo", "recentSearches": "Recherches récentes"}, "searchNotFound": "Aucun résultat trouvé!", "sellerInfo": {"delivery": "<PERSON><PERSON><PERSON>", "details": "Détails", "moreFromThisSeller": " Plus de ce vendeur", "returnPolicy": "Politique de retour", "daysReturnable": "Cet article est éligible à des retours gratuits sous", "days": "jours", "sellerInformation": "Informations sur le vendeur", "totalReviews": "<PERSON><PERSON>", "viewAll": "Voir tout", "warranty": "<PERSON><PERSON><PERSON>"}, "signIn": {"continue": "<PERSON><PERSON><PERSON>", "createone": "<PERSON><PERSON><PERSON> votre compte ici", "forgotPassword": "Mot de passe oublié?", "noAccountYet": "Noveau chez MoMo Marketplace? ", "password": "Mot de passe", "phoneNumber": "Numéro de téléphone", "signIn": "Se connecter", "signInText": "Trouvez tout ce que vous aimez sur Market by Mo<PERSON><PERSON>, le plus grand marché en ligne au monde", "tooltip": "Veuillez allonger ce texte à 12 caractères ou plus", "AgreeTermsOne": "En continuant, vous acceptez les", "AgreeTermsTwo": "termes et conditions", "AgreeTermsThree": "et", "AgreeTermsFour": "la politique de confidentialité", "AgreeTermsFive": "Market by MoMo", "newCustomer": "Nouveau client ?", "Register": "<PERSON><PERSON><PERSON> votre compte maintenant", "content": "Trouvez tout ce que vous aimez sur la place de marché Mo<PERSON>, la plus grande place de marché en ligne au monde."}, "updatePassword": {"backToLogin": "Retour connexion", "confirmPassword": "Confirmez le mot de passe", "newPassword": "nouveau mot de passe", "passwordReset": "Réinitialisation du mot de passe", "phoneNumber": "Numéro de téléphone", "resetPassword": "réinitialiser le mot de passe", "setUpSecurePassword": "<PERSON><PERSON><PERSON> de vous inscrire avec les critères ci-dessous"}, "wishlist": {"addToCart": "A<PERSON>ter au panier", "continueShopping": "Continuer vos achats", "delete": "<PERSON><PERSON><PERSON><PERSON>", "description": "Placer un produit sur la liste de souhaits ne réserve pas ce produit ou ce prix. Nous ne réservons le produit qu'une fois le paiement effectué", "MoveCart": "<PERSON>é<PERSON><PERSON> dans le panier", "notAvailable": "NON DISPONIBLE", "price": "PRIX", "products": "PRODUITS", "title": "Liste de souhaits", "was": "Était", "wishListEmpty": "Votre liste de favoris est vide", "wishListShopping": "Commencer vos achats", "wishListWaiting": "Qu'attendez-vous?", "items": "articles", "movetoCart": "<PERSON><PERSON><PERSON><PERSON> au panier", "notifyMe": "Notifier", "yourWishlist": "Votre liste de souhaits"}, "yourDetailsbread": {"details": "dé<PERSON>", "yourDetails": "<PERSON><PERSON>"}, "notifyMeDetails": {"notifyMeVia": "Me notifier via :", "byNumber": "Par numéro de téléphone mobile", "byEmail": "Par adresse e-mail", "notifyAvaliable": "Notifier moi quand disponible", "close": "FERMER", "thanksForInterest": "Merci pour votre intérêt!", "notifyProductIsAvaialble": "<PERSON>h bien, vous recevrez une notification si le produit est disponible."}, "flashSaleModal": {"hours": " <PERSON><PERSON>", "min": "Min", "sec": "Sec", "visit": "Visiter", "close": "<PERSON><PERSON><PERSON>"}, "promo": {"header": "Ajouter un code promotionnel", "placeholder": "Entrez le code promo", "apply": "Appliquer", "discountApplied": "Remise appliquée", "couponCodeInvalid": "Code promotionnel invalide", "couponAlreadyUsed": "Coupon déjà utilisé"}, "countries": {"Uganda": "<PERSON><PERSON><PERSON>", "Ghana": "Ghana", "CotedIvoire": "Côte d’Ivoire"}, "ageConsentModal": {"ageVerification": "Vérification de l'âge", "PleaseVerify": "Veuillez vérifier votre âge pour entrer.", "Day": "Jour", "Month": "<PERSON><PERSON>", "Year": "<PERSON><PERSON>", "IAgreeAll": "J'accepte tout", "btnCancel": "Annuler", "btnProceed": "<PERSON><PERSON><PERSON>", "validationMsg": "Veuillez saisir toutes les données requises", "errorLabel": "Les boissons alcoolisées et/ou certains produits de vapotage doivent être reçus et signés par une personne âgée de *AGE* ans ou plus. La personne recevant la livraison peut être invitée à présenter sa pièce d'identité, son passeport ou son permis de conduire pour vérifier son âge."}}