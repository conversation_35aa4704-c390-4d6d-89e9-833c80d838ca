{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject } from \"rxjs\";\nimport { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./\";\nexport class TenantService {\n  constructor(mainDataService, appDataService, homeService) {\n    this.mainDataService = mainDataService;\n    this.appDataService = appDataService;\n    this.homeService = homeService;\n    this.tenant = new Subject();\n    this.header = new Subject();\n    this.isAllowedCached = {\n      isLandingPage: false,\n      isHotDeals: false,\n      isNewDeals: false,\n      isNewArrivals: false,\n      isGlobal: false\n    };\n  }\n  setTenant(tenantId) {\n    this.appDataService.clearAppData();\n    this.tenant.next(tenantId);\n  }\n  getTenant() {\n    return this.tenant;\n  }\n  setHeader(tenantId) {\n    this.header.next(tenantId);\n  }\n  getHeader() {\n    return this.header;\n  }\n  getAppConfigurationData(isMobile = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!(yield _this.shouldGetAppDataCache())) {\n        if (_this.isAllowedCached.isLandingPage) {\n          const referer = environment.referer;\n          const allAppConfiguration = _this.mainDataService.getAllAppConfigurations(referer).toPromise();\n          let res = yield Promise.all([allAppConfiguration]);\n          // below condition for initial data is because we are expecting an object but getting array from backend\n          _this.appDataService.initialData = Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData;\n          _this.appDataService.configuration = res[0].data.allConfiguration;\n          _this.appDataService.tenants = res[0].data.allCountryTenants;\n          _this.appDataService.categories = res[0].data.allCategory;\n          _this.appDataService.showRoomConfiguration = res[0].data.tenantShowRoomConfiguration;\n          const appDataCache = {\n            initialData: Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData,\n            configuration: res[0].data.allConfiguration,\n            tenants: res[0].data.allCountryTenants,\n            categories: res[0].data.allCategory,\n            showRoomConfiguration: res[0].data.tenantShowRoomConfiguration\n          };\n          // localStorage.setItem('appData_expiry', (new Date).getTime().toString())\n          localStorage.setItem('appData_cache', JSON.stringify(appDataCache));\n        } else {\n          let appDataCache = localStorage.getItem('appData_cache') ?? '';\n          if (!appDataCache || appDataCache === '') {\n            localStorage.removeItem('apiDataVersion');\n            localStorage.removeItem('appData_cache');\n            yield _this.getAppConfigurationData();\n          } else {\n            appDataCache = JSON.parse(appDataCache);\n            _this.appDataService.initialData = appDataCache.initialData;\n            _this.appDataService.configuration = appDataCache.configuration;\n            _this.appDataService.tenants = appDataCache.tenants;\n            _this.appDataService.categories = appDataCache.categories;\n            _this.appDataService.showRoomConfiguration = appDataCache.showRoomConfiguration;\n          }\n        }\n      }\n    })();\n  }\n  getConfigurationWithoutCache(isMobile) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const layoutTemplatePromise = _this2.mainDataService.getLayoutTemplate(isMobile).toPromise();\n      let res = yield Promise.all([layoutTemplatePromise]);\n      _this2.appDataService.layoutTemplate = res[0].data;\n    })();\n  }\n  shouldGetAppDataCache() {\n    return new Promise((resolve, reject) => {\n      const tenantId = localStorage.getItem('tenantId') ?? '';\n      this.homeService.getAppDataVersion(tenantId).subscribe({\n        next: version => {\n          let apiDataVersion = localStorage.getItem('apiDataVersion');\n          if (!apiDataVersion || apiDataVersion === 'null' || apiDataVersion[0] === 'v') {\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\n            this.isAllowedCached.isLandingPage = true;\n            this.isAllowedCached.isBestSeller = true;\n            this.isAllowedCached.isNewArrivals = true;\n            this.isAllowedCached.isHotDeals = true;\n            this.isAllowedCached.isGlobal = true;\n            resolve(false);\n            return false;\n          } else {\n            let versionList = JSON.parse(localStorage.getItem('apiDataVersion') ?? '');\n            if (version.find(item => item.key === 'ApiDataVersion')?.value != versionList.find(item => item.key === 'ApiDataVersion')?.value) {\n              this.isAllowedCached.isLandingPage = true;\n            }\n            if (!versionList.find(item => item.key === 'BestSeller') || version.find(item => item.key === 'BestSeller')?.value != versionList.find(item => item.key === 'BestSeller')?.value) {\n              this.isAllowedCached.isBestSeller = true;\n            }\n            if (!versionList.find(item => item.key === 'NewArrivals') || version.find(item => item.key === 'NewArrivals')?.value != versionList.find(item => item.key === 'NewArrivals')?.value) {\n              this.isAllowedCached.isNewArrivals = true;\n            }\n            if (!versionList.find(item => item.key === 'HotDeals') || version.find(item => item.key === 'HotDeals')?.value != versionList.find(item => item.key === 'HotDeals')?.value) {\n              this.isAllowedCached.isHotDeals = true;\n            }\n            if (!versionList.find(item => item.key === 'Global') || version.find(item => item.key === 'Global')?.value != versionList.find(item => item.key === 'HotDeals')?.value) {\n              this.isAllowedCached.isGlobal = true;\n            }\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\n            resolve(false);\n            return false;\n          }\n        },\n        error: () => {\n          resolve(false);\n          return false;\n        }\n      });\n    });\n  }\n  static #_ = this.ɵfac = function TenantService_Factory(t) {\n    return new (t || TenantService)(i0.ɵɵinject(i1.MainDataService), i0.ɵɵinject(i1.AppDataService), i0.ɵɵinject(i1.HomeService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TenantService,\n    factory: TenantService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Subject", "environment", "TenantService", "constructor", "mainDataService", "appDataService", "homeService", "tenant", "header", "isAllowedCached", "isLandingPage", "isHotDeals", "isNewDeals", "isNewArrivals", "isGlobal", "<PERSON><PERSON><PERSON><PERSON>", "tenantId", "clearAppData", "next", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getAppConfigurationData", "isMobile", "_this", "_asyncToGenerator", "shouldGetAppDataCache", "referer", "allAppConfiguration", "getAllAppConfigurations", "to<PERSON>romise", "res", "Promise", "all", "initialData", "Array", "isArray", "data", "configuration", "allConfiguration", "tenants", "allCountryTenants", "categories", "allCategory", "showRoomConfiguration", "tenantShowRoomConfiguration", "appDataCache", "localStorage", "setItem", "JSON", "stringify", "getItem", "removeItem", "parse", "getConfigurationWithoutCache", "_this2", "layoutTemplatePromise", "getLayoutTemplate", "layoutTemplate", "resolve", "reject", "getAppDataVersion", "subscribe", "version", "apiDataVersion", "isBestSeller", "versionList", "find", "item", "key", "value", "error", "_", "i0", "ɵɵinject", "i1", "MainDataService", "AppDataService", "HomeService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\tenant.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {Subject} from \"rxjs\";\r\nimport {MainDataService, AppDataService, HomeService} from \"./\";\r\nimport {environment} from \"@environments/environment\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TenantService {\r\n  public tenant = new Subject();\r\n  public header = new Subject();\r\n  public isAllowedCached : any = {\r\n    isLandingPage: false,\r\n    isHotDeals: false,\r\n    isNewDeals: false,\r\n    isNewArrivals: false,\r\n    isGlobal:false\r\n  }\r\n  constructor(private mainDataService: MainDataService,\r\n              private appDataService: AppDataService,\r\n              private homeService: HomeService) {\r\n  }\r\n\r\n  setTenant(tenantId: any) {\r\n    this.appDataService.clearAppData();\r\n    this.tenant.next(tenantId);\r\n  }\r\n\r\n  getTenant() {\r\n    return this.tenant;\r\n  }\r\n\r\n  setHeader(tenantId: any) {\r\n    this.header.next(tenantId);\r\n  }\r\n\r\n  getHeader() {\r\n    return this.header;\r\n  }\r\n\r\n  async getAppConfigurationData(isMobile = false): Promise<any> {\r\n\r\n    if (!await this.shouldGetAppDataCache()) {\r\n      if(this.isAllowedCached.isLandingPage){\r\n        const referer: any = environment.referer\r\n        const allAppConfiguration = this.mainDataService.getAllAppConfigurations(referer).toPromise();\r\n        let res: any = await Promise.all([\r\n          allAppConfiguration\r\n        ]);\r\n        // below condition for initial data is because we are expecting an object but getting array from backend\r\n        this.appDataService.initialData = Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData\r\n        this.appDataService.configuration = res[0].data.allConfiguration\r\n        this.appDataService.tenants = res[0].data.allCountryTenants\r\n        this.appDataService.categories = res[0].data.allCategory\r\n        this.appDataService.showRoomConfiguration = res[0].data.tenantShowRoomConfiguration\r\n\r\n        const appDataCache = {\r\n          initialData: Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData,\r\n          configuration: res[0].data.allConfiguration,\r\n          tenants: res[0].data.allCountryTenants,\r\n          categories: res[0].data.allCategory,\r\n          showRoomConfiguration: res[0].data.tenantShowRoomConfiguration,\r\n        }\r\n        // localStorage.setItem('appData_expiry', (new Date).getTime().toString())\r\n        localStorage.setItem('appData_cache', JSON.stringify(appDataCache))\r\n      }else {\r\n        let appDataCache: any = localStorage.getItem('appData_cache') ?? ''\r\n        if (!appDataCache || appDataCache === '') {\r\n          localStorage.removeItem('apiDataVersion')\r\n          localStorage.removeItem('appData_cache')\r\n          await this.getAppConfigurationData()\r\n        } else {\r\n          appDataCache = JSON.parse(appDataCache)\r\n\r\n          this.appDataService.initialData = appDataCache.initialData\r\n          this.appDataService.configuration = appDataCache.configuration\r\n          this.appDataService.tenants = appDataCache.tenants\r\n          this.appDataService.categories = appDataCache.categories\r\n          this.appDataService.showRoomConfiguration = appDataCache.showRoomConfiguration\r\n        }\r\n      }\r\n\r\n\r\n    }\r\n\r\n\r\n  }\r\n\r\n  async getConfigurationWithoutCache(isMobile: boolean) {\r\n    const layoutTemplatePromise = this.mainDataService.getLayoutTemplate(isMobile).toPromise();\r\n    let res: any = await Promise.all([\r\n      layoutTemplatePromise\r\n    ]);\r\n    this.appDataService.layoutTemplate = res[0].data\r\n\r\n  }\r\n\r\n  shouldGetAppDataCache(): Promise<any> {\r\n    return new Promise((resolve, reject) => {\r\n      const tenantId: string = localStorage.getItem('tenantId') ?? '';\r\n      this.homeService.getAppDataVersion(tenantId).subscribe({\r\n        next: (version: any) => {\r\n          let apiDataVersion : any = localStorage.getItem('apiDataVersion');\r\n          if(!apiDataVersion || apiDataVersion === 'null' || apiDataVersion[0] === 'v'){\r\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\r\n            this.isAllowedCached.isLandingPage = true;\r\n            this.isAllowedCached.isBestSeller = true;\r\n            this.isAllowedCached.isNewArrivals = true;\r\n            this.isAllowedCached.isHotDeals  = true;\r\n            this.isAllowedCached.isGlobal = true;\r\n            resolve(false)\r\n            return false\r\n          }else{\r\n            let versionList = JSON.parse(localStorage.getItem('apiDataVersion') ?? '');\r\n            if(version.find((item: any) => item.key === 'ApiDataVersion')?.value != versionList.find((item: any) => item.key === 'ApiDataVersion')?.value){\r\n              this.isAllowedCached.isLandingPage = true;\r\n            }\r\n            if(!(versionList.find((item: any) => item.key === 'BestSeller')) || (version.find((item: any) => item.key === 'BestSeller')?.value != versionList.find((item: any) => item.key === 'BestSeller')?.value)){\r\n              this.isAllowedCached.isBestSeller = true;\r\n            }\r\n            if(!(versionList.find((item: any) => item.key === 'NewArrivals')) || (version.find((item: any) => item.key === 'NewArrivals')?.value != versionList.find((item: any) => item.key === 'NewArrivals')?.value))\r\n            {\r\n              this.isAllowedCached.isNewArrivals = true;\r\n            }\r\n            if(!(versionList.find((item: any) => item.key === 'HotDeals')) || (version.find((item: any) => item.key === 'HotDeals')?.value != versionList.find((item: any) => item.key === 'HotDeals')?.value)){\r\n              this.isAllowedCached.isHotDeals = true;\r\n            }\r\n            if(!(versionList.find((item: any) => item.key === 'Global')) || (version.find((item: any) => item.key === 'Global')?.value != versionList.find((item: any) => item.key === 'HotDeals')?.value)){\r\n              this.isAllowedCached.isGlobal = true;\r\n            }\r\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\r\n            resolve(false)\r\n            return false\r\n          }\r\n        },\r\n        error: () => {\r\n          resolve(false)\r\n          return false;\r\n        }\r\n      })\r\n    })\r\n\r\n  }\r\n}\r\n"], "mappings": ";AACA,SAAQA,OAAO,QAAO,MAAM;AAE5B,SAAQC,WAAW,QAAO,2BAA2B;;;AAKrD,OAAM,MAAOC,aAAa;EAUxBC,YAAoBC,eAAgC,EAChCC,cAA8B,EAC9BC,WAAwB;IAFxB,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IAXxB,KAAAC,MAAM,GAAG,IAAIP,OAAO,EAAE;IACtB,KAAAQ,MAAM,GAAG,IAAIR,OAAO,EAAE;IACtB,KAAAS,eAAe,GAAS;MAC7BC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAC;KACV;EAID;EAEAC,SAASA,CAACC,QAAa;IACrB,IAAI,CAACX,cAAc,CAACY,YAAY,EAAE;IAClC,IAAI,CAACV,MAAM,CAACW,IAAI,CAACF,QAAQ,CAAC;EAC5B;EAEAG,SAASA,CAAA;IACP,OAAO,IAAI,CAACZ,MAAM;EACpB;EAEAa,SAASA,CAACJ,QAAa;IACrB,IAAI,CAACR,MAAM,CAACU,IAAI,CAACF,QAAQ,CAAC;EAC5B;EAEAK,SAASA,CAAA;IACP,OAAO,IAAI,CAACb,MAAM;EACpB;EAEMc,uBAAuBA,CAACC,QAAQ,GAAG,KAAK;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAE5C,IAAI,QAAOD,KAAI,CAACE,qBAAqB,EAAE,GAAE;QACvC,IAAGF,KAAI,CAACf,eAAe,CAACC,aAAa,EAAC;UACpC,MAAMiB,OAAO,GAAQ1B,WAAW,CAAC0B,OAAO;UACxC,MAAMC,mBAAmB,GAAGJ,KAAI,CAACpB,eAAe,CAACyB,uBAAuB,CAACF,OAAO,CAAC,CAACG,SAAS,EAAE;UAC7F,IAAIC,GAAG,SAAcC,OAAO,CAACC,GAAG,CAAC,CAC/BL,mBAAmB,CACpB,CAAC;UACF;UACAJ,KAAI,CAACnB,cAAc,CAAC6B,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW;UAC/HV,KAAI,CAACnB,cAAc,CAACiC,aAAa,GAAGP,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACE,gBAAgB;UAChEf,KAAI,CAACnB,cAAc,CAACmC,OAAO,GAAGT,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACI,iBAAiB;UAC3DjB,KAAI,CAACnB,cAAc,CAACqC,UAAU,GAAGX,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACM,WAAW;UACxDnB,KAAI,CAACnB,cAAc,CAACuC,qBAAqB,GAAGb,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACQ,2BAA2B;UAEnF,MAAMC,YAAY,GAAG;YACnBZ,WAAW,EAAEC,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACH,WAAW;YAC1GI,aAAa,EAAEP,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACE,gBAAgB;YAC3CC,OAAO,EAAET,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACI,iBAAiB;YACtCC,UAAU,EAAEX,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACM,WAAW;YACnCC,qBAAqB,EAAEb,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI,CAACQ;WACpC;UACD;UACAE,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACJ,YAAY,CAAC,CAAC;SACpE,MAAK;UACJ,IAAIA,YAAY,GAAQC,YAAY,CAACI,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE;UACnE,IAAI,CAACL,YAAY,IAAIA,YAAY,KAAK,EAAE,EAAE;YACxCC,YAAY,CAACK,UAAU,CAAC,gBAAgB,CAAC;YACzCL,YAAY,CAACK,UAAU,CAAC,eAAe,CAAC;YACxC,MAAM5B,KAAI,CAACF,uBAAuB,EAAE;WACrC,MAAM;YACLwB,YAAY,GAAGG,IAAI,CAACI,KAAK,CAACP,YAAY,CAAC;YAEvCtB,KAAI,CAACnB,cAAc,CAAC6B,WAAW,GAAGY,YAAY,CAACZ,WAAW;YAC1DV,KAAI,CAACnB,cAAc,CAACiC,aAAa,GAAGQ,YAAY,CAACR,aAAa;YAC9Dd,KAAI,CAACnB,cAAc,CAACmC,OAAO,GAAGM,YAAY,CAACN,OAAO;YAClDhB,KAAI,CAACnB,cAAc,CAACqC,UAAU,GAAGI,YAAY,CAACJ,UAAU;YACxDlB,KAAI,CAACnB,cAAc,CAACuC,qBAAqB,GAAGE,YAAY,CAACF,qBAAqB;;;;IAKnF;EAGH;EAEMU,4BAA4BA,CAAC/B,QAAiB;IAAA,IAAAgC,MAAA;IAAA,OAAA9B,iBAAA;MAClD,MAAM+B,qBAAqB,GAAGD,MAAI,CAACnD,eAAe,CAACqD,iBAAiB,CAAClC,QAAQ,CAAC,CAACO,SAAS,EAAE;MAC1F,IAAIC,GAAG,SAAcC,OAAO,CAACC,GAAG,CAAC,CAC/BuB,qBAAqB,CACtB,CAAC;MACFD,MAAI,CAAClD,cAAc,CAACqD,cAAc,GAAG3B,GAAG,CAAC,CAAC,CAAC,CAACM,IAAI;IAAA;EAElD;EAEAX,qBAAqBA,CAAA;IACnB,OAAO,IAAIM,OAAO,CAAC,CAAC2B,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAM5C,QAAQ,GAAW+B,YAAY,CAACI,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;MAC/D,IAAI,CAAC7C,WAAW,CAACuD,iBAAiB,CAAC7C,QAAQ,CAAC,CAAC8C,SAAS,CAAC;QACrD5C,IAAI,EAAG6C,OAAY,IAAI;UACrB,IAAIC,cAAc,GAASjB,YAAY,CAACI,OAAO,CAAC,gBAAgB,CAAC;UACjE,IAAG,CAACa,cAAc,IAAIA,cAAc,KAAK,MAAM,IAAIA,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,EAAC;YAC3EjB,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAACa,OAAO,CAAC,CAAC;YAC/D,IAAI,CAACtD,eAAe,CAACC,aAAa,GAAG,IAAI;YACzC,IAAI,CAACD,eAAe,CAACwD,YAAY,GAAG,IAAI;YACxC,IAAI,CAACxD,eAAe,CAACI,aAAa,GAAG,IAAI;YACzC,IAAI,CAACJ,eAAe,CAACE,UAAU,GAAI,IAAI;YACvC,IAAI,CAACF,eAAe,CAACK,QAAQ,GAAG,IAAI;YACpC6C,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,KAAK;WACb,MAAI;YACH,IAAIO,WAAW,GAAGjB,IAAI,CAACI,KAAK,CAACN,YAAY,CAACI,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC1E,IAAGY,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,gBAAgB,CAAC,EAAEC,KAAK,IAAIJ,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,gBAAgB,CAAC,EAAEC,KAAK,EAAC;cAC5I,IAAI,CAAC7D,eAAe,CAACC,aAAa,GAAG,IAAI;;YAE3C,IAAG,CAAEwD,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,YAAY,CAAE,IAAKN,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,YAAY,CAAC,EAAEC,KAAK,IAAIJ,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,YAAY,CAAC,EAAEC,KAAM,EAAC;cACvM,IAAI,CAAC7D,eAAe,CAACwD,YAAY,GAAG,IAAI;;YAE1C,IAAG,CAAEC,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,aAAa,CAAE,IAAKN,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,aAAa,CAAC,EAAEC,KAAK,IAAIJ,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,aAAa,CAAC,EAAEC,KAAM,EAC3M;cACE,IAAI,CAAC7D,eAAe,CAACI,aAAa,GAAG,IAAI;;YAE3C,IAAG,CAAEqD,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,UAAU,CAAE,IAAKN,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,UAAU,CAAC,EAAEC,KAAK,IAAIJ,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,UAAU,CAAC,EAAEC,KAAM,EAAC;cACjM,IAAI,CAAC7D,eAAe,CAACE,UAAU,GAAG,IAAI;;YAExC,IAAG,CAAEuD,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,QAAQ,CAAE,IAAKN,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,QAAQ,CAAC,EAAEC,KAAK,IAAIJ,WAAW,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,GAAG,KAAK,UAAU,CAAC,EAAEC,KAAM,EAAC;cAC7L,IAAI,CAAC7D,eAAe,CAACK,QAAQ,GAAG,IAAI;;YAEtCiC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEC,IAAI,CAACC,SAAS,CAACa,OAAO,CAAC,CAAC;YAC/DJ,OAAO,CAAC,KAAK,CAAC;YACd,OAAO,KAAK;;QAEhB,CAAC;QACDY,KAAK,EAAEA,CAAA,KAAK;UACVZ,OAAO,CAAC,KAAK,CAAC;UACd,OAAO,KAAK;QACd;OACD,CAAC;IACJ,CAAC,CAAC;EAEJ;EAAC,QAAAa,CAAA,G;qBAtIUtE,aAAa,EAAAuE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAG,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb7E,aAAa;IAAA8E,OAAA,EAAb9E,aAAa,CAAA+E,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}