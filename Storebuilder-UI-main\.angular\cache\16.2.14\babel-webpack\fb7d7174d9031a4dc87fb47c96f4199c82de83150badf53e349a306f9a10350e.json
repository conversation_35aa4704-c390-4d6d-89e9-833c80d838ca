{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { ButtonModule } from \"primeng/button\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CategoryProductsModule = /*#__PURE__*/(() => {\n  class CategoryProductsModule {\n    static ɵfac = function CategoryProductsModule_Factory(t) {\n      return new (t || CategoryProductsModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CategoryProductsModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), RippleModule, TranslateModule, InitialModule, BreadcrumbModule, ButtonModule]\n    });\n  }\n  return CategoryProductsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}