{"ast": null, "code": "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\nexport function createPacketEncoderStream() {\n  return new TransformStream({\n    transform(packet, controller) {\n      encodePacketToBinary(packet, encodedPacket => {\n        const payloadLength = encodedPacket.length;\n        let header;\n        // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n        if (payloadLength < 126) {\n          header = new Uint8Array(1);\n          new DataView(header.buffer).setUint8(0, payloadLength);\n        } else if (payloadLength < 65536) {\n          header = new Uint8Array(3);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 126);\n          view.setUint16(1, payloadLength);\n        } else {\n          header = new Uint8Array(9);\n          const view = new DataView(header.buffer);\n          view.setUint8(0, 127);\n          view.setBigUint64(1, BigInt(payloadLength));\n        }\n        // first bit indicates whether the payload is plain text (0) or binary (1)\n        if (packet.data && typeof packet.data !== \"string\") {\n          header[0] |= 0x80;\n        }\n        controller.enqueue(header);\n        controller.enqueue(encodedPacket);\n      });\n    }\n  });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n  return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n  if (chunks[0].length === size) {\n    return chunks.shift();\n  }\n  const buffer = new Uint8Array(size);\n  let j = 0;\n  for (let i = 0; i < size; i++) {\n    buffer[i] = chunks[0][j++];\n    if (j === chunks[0].length) {\n      chunks.shift();\n      j = 0;\n    }\n  }\n  if (chunks.length && j < chunks[0].length) {\n    chunks[0] = chunks[0].slice(j);\n  }\n  return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n  if (!TEXT_DECODER) {\n    TEXT_DECODER = new TextDecoder();\n  }\n  const chunks = [];\n  let state = 0 /* State.READ_HEADER */;\n  let expectedLength = -1;\n  let isBinary = false;\n  return new TransformStream({\n    transform(chunk, controller) {\n      chunks.push(chunk);\n      while (true) {\n        if (state === 0 /* State.READ_HEADER */) {\n          if (totalLength(chunks) < 1) {\n            break;\n          }\n          const header = concatChunks(chunks, 1);\n          isBinary = (header[0] & 0x80) === 0x80;\n          expectedLength = header[0] & 0x7f;\n          if (expectedLength < 126) {\n            state = 3 /* State.READ_PAYLOAD */;\n          } else if (expectedLength === 126) {\n            state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n          } else {\n            state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n          }\n        } else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n          if (totalLength(chunks) < 2) {\n            break;\n          }\n          const headerArray = concatChunks(chunks, 2);\n          expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n          state = 3 /* State.READ_PAYLOAD */;\n        } else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n          if (totalLength(chunks) < 8) {\n            break;\n          }\n          const headerArray = concatChunks(chunks, 8);\n          const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n          const n = view.getUint32(0);\n          if (n > Math.pow(2, 53 - 32) - 1) {\n            // the maximum safe integer in JavaScript is 2^53 - 1\n            controller.enqueue(ERROR_PACKET);\n            break;\n          }\n          expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n          state = 3 /* State.READ_PAYLOAD */;\n        } else {\n          if (totalLength(chunks) < expectedLength) {\n            break;\n          }\n          const data = concatChunks(chunks, expectedLength);\n          controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n          state = 0 /* State.READ_HEADER */;\n        }\n\n        if (expectedLength === 0 || expectedLength > maxPayload) {\n          controller.enqueue(ERROR_PACKET);\n          break;\n        }\n      }\n    }\n  });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}