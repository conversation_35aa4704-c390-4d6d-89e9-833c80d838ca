{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class NotFoundComponent {\n  ngOnInit() {\n    /**/\n  }\n  static #_ = this.ɵfac = function NotFoundComponent_Factory(t) {\n    return new (t || NotFoundComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotFoundComponent,\n    selectors: [[\"app-not-found\"]],\n    decls: 4,\n    vars: 3,\n    template: function NotFoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\")(1, \"h1\");\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"notFound.pageNotFound\"));\n      }\n    },\n    dependencies: [i1.TranslatePipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["NotFoundComponent", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "template", "NotFoundComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\not-found\\components\\not-found.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\not-found\\components\\not-found.component.html"], "sourcesContent": ["import {Component, OnInit} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-not-found',\r\n  templateUrl: './not-found.component.html',\r\n  styleUrls: ['./not-found.component.scss']\r\n})\r\nexport class NotFoundComponent implements OnInit {\r\n\r\n\r\n  ngOnInit() {\r\n    /**/\r\n  }\r\n\r\n}\r\n", "<div>\r\n  <h1>{{\"notFound.pageNotFound\" | translate}}</h1>\r\n</div>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,iBAAiB;EAG5BC,QAAQA,CAAA;IACN;EAAA;EACD,QAAAC,CAAA,G;qBALUF,iBAAiB;EAAA;EAAA,QAAAG,EAAA,G;UAAjBH,iBAAiB;IAAAI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP9BE,EAAA,CAAAC,cAAA,UAAK;QACCD,EAAA,CAAAE,MAAA,GAAuC;;QAAAF,EAAA,CAAAG,YAAA,EAAK;;;QAA5CH,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,gCAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}