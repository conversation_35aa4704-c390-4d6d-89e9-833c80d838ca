{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { get } from 'scriptjs';\nimport { environment } from '@environments/environment';\nimport { RequestDelivery } from '@core/interface';\nimport { forkJoin, take } from \"rxjs\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport { PaymentMethodEnum } from '@core/enums/payment-method-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/custom-GA.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"@pages/checkout/modals/lightbox-loader-modal/lightbox-loader-modal.component\";\nconst _c0 = function (a0) {\n  return {\n    \"opacity\": a0\n  };\n};\nfunction PaymentCartComponent_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"section\", 10)(3, \"div\", 11)(4, \"div\", 12);\n    i0.ɵɵelement(5, \"p\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_ng_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.show());\n    });\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"span\", 16);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 17);\n    i0.ɵɵtext(12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 18);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 19);\n    i0.ɵɵelement(19, \"path\", 20)(20, \"path\", 21);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.isProceccedCheckOut)(\"ngStyle\", i0.ɵɵpureFunction1(14, _c0, !ctx_r3.isProceccedCheckOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.orderDetails.productDetails.length, \" \", i0.ɵɵpipeBind1(10, 7, ctx_r3.orderDetails.productDetails.length > 1 ? \"checkout.items\" : \"checkout.item\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.disableCent === \"false\" ? i0.ɵɵpipeBind2(14, 9, ctx_r3.orderDetails.orderAmount + ctx_r3.shipmentService.shipmentCost - ctx_r3.orderDiscount, \"1.\" + ctx_r3.decimalValue + \"-\" + ctx_r3.decimalValue) : ctx_r3.orderDetails.orderAmount + ctx_r3.shipmentService.shipmentCost - ctx_r3.orderDiscount, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 12, \"checkout.paymentCart.PayNow\"), \" \");\n  }\n}\nfunction PaymentCartComponent_div_0_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"checkout.paymentCart.arrives\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.timeString);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    opacity: a0\n  };\n};\nfunction PaymentCartComponent_div_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 22);\n    i0.ɵɵtemplate(1, PaymentCartComponent_div_0_ng_template_2_div_1_Template, 5, 4, \"div\", 23);\n    i0.ɵɵelementStart(2, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_ng_template_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.show());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.timeString && ctx_r5.isShipmentFeeExist);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.isProceccedCheckOut)(\"ngStyle\", i0.ɵɵpureFunction1(6, _c1, !ctx_r5.isProceccedCheckOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 4, \"checkout.paymentCart.proceed\"), \" \");\n  }\n}\nfunction PaymentCartComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, PaymentCartComponent_div_0_ng_container_1_Template, 21, 16, \"ng-container\", 4);\n    i0.ɵɵtemplate(2, PaymentCartComponent_div_0_ng_template_2_Template, 5, 8, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"section\", 6)(5, \"div\", 7)(6, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.show());\n    });\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isMobileTemplate && ctx_r0.screenWidth <= 768)(\"ngIfElse\", _r4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(7, 3, \"checkout.paymentCart.proceed\"));\n  }\n}\nfunction PaymentCartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"section\", 22)(2, \"div\", 7)(3, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.show());\n    });\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(4, 1, \"checkout.paymentCart.proceed\"));\n  }\n}\nfunction PaymentCartComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-lightbox-loader-modal\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r2.displayLoaderModal);\n  }\n}\nexport class PaymentCartComponent {\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(dialogService, platformId, store, paymentService, router, messageService, shopService, transactionService, transactionDetailsService, orderService, translate, addressService, productLogicService, loaderService, authService, mainDataService, permissionService, $gaService, shipmentService, renderer, cd, _GACustomEvent) {\n    this.dialogService = dialogService;\n    this.platformId = platformId;\n    this.store = store;\n    this.paymentService = paymentService;\n    this.router = router;\n    this.messageService = messageService;\n    this.shopService = shopService;\n    this.transactionService = transactionService;\n    this.transactionDetailsService = transactionDetailsService;\n    this.orderService = orderService;\n    this.translate = translate;\n    this.addressService = addressService;\n    this.productLogicService = productLogicService;\n    this.loaderService = loaderService;\n    this.authService = authService;\n    this.mainDataService = mainDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.shipmentService = shipmentService;\n    this.renderer = renderer;\n    this.cd = cd;\n    this._GACustomEvent = _GACustomEvent;\n    this.lightBoxData = null;\n    this.shopsPayments = new Array();\n    this.totalLogisticsFee = 0;\n    this.subOrdersCommission = new Array();\n    this.shipmentFee = 0;\n    this.totalDiscount = 0;\n    this.allowedpaymentMethod = 0;\n    this.showPayButton = false;\n    this.shipmentCost = -1;\n    this.AmountTrxn = 0;\n    this.requestData = new RequestDelivery();\n    this.minDate = new Date().toDateString();\n    this.maxDate = new Date().toDateString();\n    this.multipleTransactionDetails = new Array();\n    this.PaymentCalled = false;\n    this.shopIds = [];\n    this.merchantId = '';\n    this.terminalId = '';\n    this.isMobileTemplate = false;\n    this.OrderId = '';\n    this.OrderShopId = null;\n    this.minTimeDelivery = Number.MAX_SAFE_INTEGER;\n    this.maxTimeDelivery = -1;\n    this.MomoPayCommissionAmount = 0;\n    this.cartId = 0;\n    this.arrivalDate = '';\n    this.timeString = '';\n    this.isLightboxLoaded = false;\n    this.isFetchOrderPaymentConfig = false;\n    this.isProceccedCheckOut = false;\n    this.isLayoutTemplate = false;\n    this.isShipmentFeeExist = false;\n    this.isGoogleAnalytics = false;\n    this.itemsTotalPrices = 0;\n    this.currencyCode = '';\n    this.decimalValue = 0;\n    this.displayLoaderModal = false;\n    this.screenWidth = window.innerWidth;\n    this.orderDiscount = 0;\n    let value = localStorage.getItem('CurrencyDecimal');\n    if (value) this.decimalValue = parseInt(value);\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.disableCent = localStorage.getItem('DisableCents');\n    this.isFetchOrderPaymentConfig = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isLayoutTemplate = _this.permissionService.hasPermission('Layout-Template');\n      _this.isGoogleAnalytics = _this.permissionService.hasPermission('Google Analytics');\n      _this.isShipmentFeeExist = _this.permissionService.hasPermission('Shipment-Fee');\n      _this.getOrderData();\n      _this.getCartId();\n      _this.addressSubscription = _this.addressService.getCustomAddress().subscribe(res => {\n        if (res) {\n          _this.getFullOrderData();\n        }\n      });\n      _this.mobileSubscription = _this.addressService.getCustomMobile().subscribe(res => {\n        if (res) {\n          _this.UpdateOrder();\n        }\n      });\n      // this.addressService.addresseData.subscribe((res: any) => {\n      //   if (res) {\n      //     this.getFullOrderData();\n      //   }\n      // })\n      // this.addressService.mobileData.subscribe((res: any) => {\n      //   if (res) {\n      //     this.UpdateOrder();\n      //   }\n      // })\n      _this.showPayButton = false;\n      _this.getCurrentCartId();\n      _this.store.subscription('mainData').subscribe({\n        next: res => {\n          let data = res.find(obj => obj.key.toLocaleLowerCase() === 'LightBoxURL'.toLocaleLowerCase());\n          if (data) _this.lightBoxURL = data.lightBoxURL;\n          data = res.find(obj => obj.key.toLocaleLowerCase() === 'currency'.toLocaleLowerCase());\n          if (data) _this.currency = data.displayName;\n          data = res.find(obj => obj.key.toLocaleLowerCase() === 'countryCode'.toLocaleLowerCase());\n          if (data) _this.requestData.countryCode = data.displayName;\n          data = res.find(obj => obj.key.toLocaleLowerCase() === 'countryphone'.toLocaleLowerCase());\n          if (data) {\n            _this.requestData.dropOffContactInfo.countryCode = data.displayName;\n            _this.requestData.pickupContactInfo.countryCode = data.displayName;\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n      _this.sessionId = localStorage.getItem('sessionId');\n      _this.userDetails = _this.store.get('profile');\n      _this.refreshSubscription = _this.refreshSummary.subscribe(() => {\n        _this.getDiscountValue();\n        _this.getFullOrderData();\n      });\n    })();\n  }\n  getOrderData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.store.subscription('orderData').subscribe({\n        next: res => {\n          if (res) {\n            _this2.orderDetails = res;\n            if (_this2.orderDetails.productDetails.length > 0) {\n              _this2.currencyCode = _this2.orderDetails.productDetails[0].currencyCode;\n            }\n            _this2.getCustomerAddress();\n            _this2.getCustomerPhone();\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    })();\n  }\n  getCustomerAddress() {\n    this.addressService.getAddress().subscribe({\n      next: res => {\n        if (res.data.records.length > 0) {\n          this.addressService.chosenAddress = res.data.records[0];\n          if (!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\" || !this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\") {\n            this.messageService.add({\n              severity: 'info',\n              summary: this.translate.instant('ResponseMessages.address'),\n              detail: this.translate.instant('ResponseMessages.invalidCityAddress')\n            });\n            this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], {\n              queryParams: {\n                returnUrl: '/checkout'\n              }\n            });\n            return;\n          }\n          if (!this.deliveryOptionDetails && !this.permissionService.hasPermission('Shipment-Fee')) {\n            this.getFullOrderData();\n          }\n        } else {\n          this.messageService.add({\n            severity: 'info',\n            summary: this.translate.instant('ResponseMessages.address'),\n            detail: this.translate.instant('ResponseMessages.pleaseProvideYourAddress')\n          });\n          this.router.navigate(['/account/address'], {\n            queryParams: {\n              returnUrl: '/checkout'\n            }\n          });\n          return;\n        }\n        this.addressService.loadedAddress = true;\n      },\n      error: err => {\n        this.loaderService.hide();\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  ngOnChanges() {\n    this.deliveryOptionDetails = this.deliveryOptionDetails?.deliveryOption ? this.deliveryOptionDetails?.deliveryOption : this.deliveryOptionDetails;\n    if (this.deliveryOptionDetails && this.permissionService.hasPermission('Shipment-Fee')) {\n      this.getFullOrderData();\n    }\n  }\n  getFullOrderData() {\n    if (this.deliveryOptionDetails?.id) {\n      let data = {\n        OrderId: this.orderDetails?.orderId,\n        AddressId: this.addressService.chosenAddress.id\n      };\n      data['deliveryOptionId'] = this.deliveryOptionDetails?.id || data['deliveryOptionId'];\n      this.orderService.GetOrderWithPaymentsConfigurations(data).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.isProceccedCheckOut = true;\n            this.isFetchOrderPaymentConfig = true;\n            this.orderDetailsWithConfig = res.data;\n            this.lightBoxURL = res.data.lightBoxURL;\n            this.merchantId = res.data.merchantId;\n            this.terminalId = res.data.terminalId;\n            this.subOrderDetails = res.data.shopsDetails;\n            this.shopsPayments = res.data.shopsPayments;\n            this.totalLogisticsFee = res.data.totalLogisticsFee;\n            this.subOrdersCommission = res.data.SubOrdersCommission;\n            this.AmountTrxn = res.data.amountTrxn;\n            this.OrderId = this.orderDetails.orderId;\n            this.OrderShopId = this.orderDetails.shopId;\n            this.MomoPayCommissionAmount = res.data.momoPayCommissionAmount;\n            this.shipmentService.shipmentCost = res.data.totalDeliveryCost;\n            this.shipmentService.actualShipmentFee = res.data.calculateShipmentFeeRes ? res.data.calculateShipmentFeeRes.adjustedShipmentFee : 0;\n            this.shipmentFee = 0;\n            this.totalDiscount = res.data.totalDiscount;\n            this.allowedpaymentMethod = res.data?.allowedpaymentMethod;\n            if (res.data?.shopsDetails.length) {\n              res.data?.shopsDetails.forEach(item => {\n                if (item.shipmentFee) {\n                  this.shipmentFee = this.shipmentFee + item.shipmentFee;\n                }\n              });\n            }\n            this.maxTimeDelivery = res.data.maxTimeDelivery;\n            this.minTimeDelivery = res.data.minTimeDelivery;\n            this.timeString = res.data.timeString;\n            this.shipmentService.currentShipment = {\n              totalDeliveryCost: Number(this.shipmentService.shipmentCost),\n              shipmentDetails: this.shipmentDetails,\n              maxTime: this.maxTimeDelivery,\n              minTime: this.minTimeDelivery\n            };\n            this.showPayButton = true;\n            this.store.set('shipmentCost', {\n              totalDeliveryCost: res.data.totalDeliveryCost,\n              shipmentDetails: this.shipmentDetails,\n              deliveryOption: this.deliveryOptionDetails\n            });\n            this.UpdateOrder();\n            this.CallLightBox();\n          } else {\n            this.isProceccedCheckOut = false;\n            if (res?.message != null) {\n              if (res.message === 'City is not defined in any region. Please update your address.' || !this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\" || !this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\") {\n                this.messageService.add({\n                  severity: 'info',\n                  summary: this.translate.instant('ResponseMessages.address'),\n                  detail: this.translate.instant('ResponseMessages.invalidCityAddress')\n                });\n                this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], {\n                  queryParams: {\n                    returnUrl: '/checkout'\n                  }\n                });\n                return;\n              } else {\n                this.messageService.add({\n                  severity: 'error',\n                  summary: this.translate.instant('ErrorMessages.fetchError'),\n                  detail: res.message\n                });\n              }\n            }\n          }\n        },\n        error: err => {\n          this.isProceccedCheckOut = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: this.translate.instant('ErrorMessages.pleaseContactCallCenter')\n          });\n          this.router.navigate(['/cart']);\n        }\n      });\n    }\n  }\n  /**\r\n  * This function handles the display of the Lightbox for the checkout process,\r\n  * logs analytics events if enabled, and performs a series of API calls to validate\r\n  * promotional stock and retrieve phone numbers. Based on the API responses,\r\n  * it determines whether to proceed with the checkout flow or redirect the user back to the cart.\r\n  */\n  show() {\n    if (this.isGoogleAnalytics) {\n      // Log an analytics event for proceeding to payment\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_PROCEED_TO_PAYMENT,\n      // Action enum\n      'checkout',\n      // Category\n      'PROCEED_TO_PAYMENT',\n      // Label\n      1,\n      // Value\n      true,\n      // Non-interaction flag\n      {\n        \"order_amount\": this.shipmentService.currentOrderTotal + this.shipmentService.shipmentCost,\n        \"order_ID\": this.OrderId,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        \"session_ID\": this.sessionId,\n        \"ip_Address\": this.store.get('userIP'),\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n        \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n        \"order_commission\": this.MomoPayCommissionAmount,\n        \"merchant_ID\": this.merchantId,\n        \"shipping_fee\": this.shipmentFee,\n        \"order_totalItems\": this.orderDetails?.productDetails?.length,\n        \"payment_method\": this.paymentMethodDetails,\n        \"suborder_commission\": this.subOrdersCommission,\n        \"delivery_option\": this.deliveryOptionDetails.name\n      });\n    }\n    forkJoin([this.authService.getPhoneNumbers(), this.authService.PromotionStockCheck(this.orderDetails.orderId), this.orderService.verifyOrderProductsVisibilityBeforeCheckout({\n      OrderId: this.orderDetails.orderId\n    })]).subscribe({\n      next: ([phoneNumberRes, promotionStockCheck, visibilityCheck]) => {\n        let promotionStockStatus = promotionStockCheck.data.promotionalStockAvailable;\n        let isValidOrder = visibilityCheck?.success;\n        if (!promotionStockStatus) {\n          this.router.navigateByUrl('/cart');\n        }\n        if (!isValidOrder) {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ResponseMessages.orderIsInValid')\n          });\n          this.router.navigateByUrl('/cart');\n        } else if (phoneNumberRes.success && promotionStockStatus && isValidOrder) {\n          this.displayLoaderModal = true;\n          this.showPayButton = false;\n          if (this.isProceccedCheckOut) {\n            Lightbox.Checkout.showLightbox();\n          }\n        }\n      },\n      error: err => {\n        console.error('Error in API calls', err);\n      },\n      complete: () => {\n        console.log('All API calls completed.');\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Set up a MutationObserver to watch for changes in the DOM\n    this.observer = new MutationObserver(mutations => {\n      for (let mutation of mutations) {\n        if (mutation.type === 'childList') {\n          Array.from(mutation.addedNodes).forEach(node => {\n            if (node.nodeName === 'IFRAME') {\n              const iframe = node;\n              this.addIframeLoadListener(iframe);\n            }\n          });\n        }\n      }\n    });\n    // Start observing the body for added nodes\n    this.observer.observe(document.body, {\n      childList: true,\n      subtree: true\n    });\n  }\n  addIframeLoadListener(iframe) {\n    this.displayLoaderModal = true;\n    this.iframeLoadListener = this.renderer.listen(iframe, 'load', () => {\n      this.displayLoaderModal = false;\n      this.cd.detectChanges();\n      this.accessIframeElements(iframe);\n    });\n  }\n  accessIframeElements(iframe) {\n    try {\n      const iframeDocument = iframe.contentDocument || iframe?.contentWindow?.document;\n      const iframeBody = iframeDocument?.body;\n      // Example: Check for a specific element or value inside the iframe\n      const element = iframeDocument?.querySelector('#elementId');\n      if (element) {\n        console.log('Element inside iframe:', element);\n      } else {\n        console.log('Element not found inside iframe');\n      }\n    } catch (error) {\n      console.error('Error accessing iframe content:', error);\n    }\n  }\n  // ngAfterViewInit() {\n  //   this.iframeInterval = setInterval(() => {\n  //     const iframe = document.querySelector('iframe'); // Adjust the selector to match your iframe\n  //     if (iframe && iframe.contentWindow && iframe.contentDocument) {\n  //       this.loaderService.show();\n  //       this.accessIframeElements(iframe as HTMLIFrameElement);\n  //       clearInterval(this.iframeInterval);\n  //     }\n  //   }, 100);\n  // }\n  // accessIframeElements(iframe: HTMLIFrameElement) {\n  //\n  //   try {\n  //     const iframeDocument = iframe?.contentWindow?.document || iframe.contentDocument;\n  //     const iframeBody = iframeDocument?.body;\n  //\n  //     // Example: Accessing an element inside the iframe\n  //     const element = iframeDocument?.querySelector('#DivInsidemyModal');\n  //     if (element) {\n  //       console.log('Element inside iframe:', element);\n  //     } else {\n  //       console.log('Element not found inside iframe');\n  //     }\n  //   } catch (error) {\n  //     console.error('Error accessing iframe content:', error);\n  //   }\n  // }\n  CallLightBox() {\n    let that = this;\n    let storedLang = localStorage.getItem('lang');\n    get(this.lightBoxURL, () => {\n      if (environment.isStoreCloud) {\n        this.getData(that, storedLang);\n      } else {\n        Lightbox.Checkout.configure = {\n          defaultPaymentMethod: this.paymentMethodDetails?.toLocaleLowerCase()?.includes('card') ? PaymentMethodEnum['Card'] : PaymentMethodEnum['Wallet'],\n          momoPaySubMerchantsDataStr: JSON.stringify(this.shopsPayments),\n          MomoPayCommissionAmount: this.MomoPayCommissionAmount,\n          MID: this.merchantId,\n          TID: this.terminalId,\n          lang: storedLang,\n          MerchantReference: this.OrderId ?? '',\n          AmountTrxn: this.AmountTrxn,\n          AdditionalCustomerData: {\n            CustomerMobile: this.userPhoneNumber\n          },\n          MomoPayLogisticFees: this.totalLogisticsFee,\n          MomoPayDiscounts: -this.totalDiscount,\n          paymentMethodFromLightBox: this.totalDiscount ? this.allowedpaymentMethod : PaymentMethodEnum['Both'],\n          completeCallback: function (data) {\n            that.loaderService.hide();\n            let transactionData = {\n              TransactionId: data.SystemReference,\n              CardNumber: data.PayerAccount,\n              PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\n              UpdateProductQuantityList: null,\n              OrderId: this.OrderId,\n              OrderShopId: this.OrderShopId\n            };\n            that.PaymentSuccess(transactionData);\n            that.onDeleteCart();\n            Lightbox.Checkout.closeLightbox();\n            if (data) {\n              that.orderService.updateOrderStatus(that.orderDetails.orderId).pipe(take(1)).subscribe(res => {\n                that.showPayButton = true;\n              });\n            }\n          },\n          errorCallback: function () {\n            that.loaderService.hide();\n            that.showPayButton = true;\n            Lightbox.Checkout.closeLightbox();\n          },\n          cancelCallback: function () {\n            that.loaderService.hide();\n            that.showPayButton = true;\n            Lightbox.Checkout.closeLightbox();\n          }\n        };\n      }\n    });\n  }\n  PaymentSuccess(transactionData) {\n    this.store.set('transactionData', transactionData);\n    this._GACustomEvent.purchaseEvent(this.cartItems, transactionData.TransactionId, this.shipmentFee, this.currencyCode);\n    this.router.navigate(['/checkout/success']);\n  }\n  getProductShopLatLng(transactionId, shopId, orderId, productId, cost) {\n    this.shopService.getShopById(shopId).subscribe({\n      next: res => {\n        if (res.data === null) {\n          return;\n        }\n        let location = [res.data.lat, res.data.lng];\n        this.requestData.pickupContactInfo.addressLatLng = '[' + location.toString() + ']';\n        this.requestData.dropOffContactInfo.addressLatLng = '[' + [this.addressService.chosenAddress.lat, this.addressService.chosenAddress.lng].toString() + ']';\n      },\n      error: err => {}\n    });\n  }\n  UpdateOrder() {\n    let applyTo = null;\n    let deliveryOption = null;\n    if (this.deliveryOptionDetails) {\n      if (this.deliveryOptionDetails.applyTo) {\n        applyTo = this.deliveryOptionDetails.applyTo;\n      }\n      if (this.deliveryOptionDetails.applyTo) {\n        deliveryOption = this.deliveryOptionDetails.id;\n      }\n    }\n    this.orderService.updateOrder({\n      id: this.orderDetails.orderId,\n      TransferReferenceId: this.paymentResult?.SystemReference,\n      totalDeliveryCost: this.shipmentService.shipmentCost,\n      shipmentFee: this.shipmentFee,\n      applyTo: applyTo,\n      deliveryOption: deliveryOption,\n      total: this.orderDetails.orderAmount + Number(this.shipmentService.shipmentCost) - this.orderDiscount,\n      addressId: this.addressService.chosenAddress.id,\n      subOrderDetails: this.subOrderDetails,\n      StreetAddress: this.addressService.chosenAddress.streetAddress,\n      State: this.addressService.chosenAddress.state,\n      Floor: this.addressService.chosenAddress.floor,\n      BuldingNumber: this.addressService.chosenAddress.buldingNumber,\n      CountryName: this.addressService.chosenAddress.countryName,\n      City: this.addressService.chosenAddress.city,\n      LandMark: this.addressService.chosenAddress.landMark\n    }).subscribe({\n      next: res => {},\n      error: err => {}\n    });\n  }\n  onDeleteCart() {\n    let cartId = localStorage.getItem('cartId');\n    if (!cartId || cartId == '') {\n      cartId = this.cartId;\n    }\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature('delete_cart')) {\n      this.$gaService.event('delete_cart', 'cart', cartId);\n    }\n    this.mainDataService.setCartLenghtData(0);\n    this.mainDataService.setCartItemsData([]);\n    this.productLogicService.emptyCart(cartId);\n  }\n  getCurrentCartId() {\n    this.store.subscription('cartProducts').subscribe({\n      next: res => {\n        this.cartId = res[0]?.cartId;\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  getData(that, storedLang) {\n    Lightbox.Checkout.configure = {\n      MID: this.merchantId,\n      TID: this.terminalId,\n      lang: storedLang,\n      MerchantReference: this.OrderId ?? '',\n      AmountTrxn: 120,\n      AdditionalCustomerData: {\n        CustomerMobile: this.userPhoneNumber\n      },\n      completeCallback: function (data) {\n        that.loaderService.hide();\n        let transactionData = {\n          TransactionId: data.SystemReference,\n          CardNumber: data.PayerAccount,\n          PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\n          UpdateProductQuantityList: null,\n          OrderId: this.OrderId,\n          OrderShopId: this.OrderShopId\n        };\n        that.PaymentSuccess(transactionData);\n        that.onDeleteCart();\n        Lightbox.Checkout.closeLightbox();\n        if (data) {\n          that.orderService.updateOrderStatus(that.orderDetails.orderId).subscribe(res => {\n            that.showPayButton = true;\n          });\n        }\n      },\n      errorCallback: function () {\n        that.loaderService.hide();\n        that.showPayButton = true;\n        Lightbox.Checkout.closeLightbox();\n      },\n      cancelCallback: function () {\n        that.loaderService.hide();\n        that.showPayButton = true;\n        Lightbox.Checkout.closeLightbox();\n      }\n    };\n  }\n  getCustomerPhone() {\n    this.authService.getPhoneNumbers().subscribe({\n      next: res => {\n        if (res.data && res.data.records && res.data.records.length) {\n          // let mobileData = res.data.records.find((obj: any) => obj.isPrimary === true);\n          // if (!mobileData) {\n          //   mobileData =  res.data.records[0];\n          // }\n          this.userPhoneNumber = res.data.records.map(obj => obj.phoneNumber);\n          this.requestData.dropOffContactInfo.phoneNumber = res.data.records.map(obj => obj.phoneNumber);\n        } else {\n          this.userPhoneNumber = localStorage.getItem('phoneNumberArray');\n        }\n      }\n    });\n  }\n  getDiscountValue() {\n    this.orderService.getOrderDiscount(this.orderDetails.orderId).subscribe({\n      next: res => {\n        if (res.success) {\n          this.orderDiscount = res.data >= this.orderDetails.orderAmount ? this.orderDetails.orderAmount : res.data;\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.addressSubscription.unsubscribe();\n    this.mobileSubscription.unsubscribe();\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n    if (this.iframeLoadListener) {\n      this.iframeLoadListener(); // Remove the event listener\n    }\n    // this.addressService.addresses.unsubscribe()\n    if (this.refreshSubscription) {\n      this.refreshSubscription.unsubscribe();\n    }\n  }\n  getCartId() {}\n  static #_ = this.ɵfac = function PaymentCartComponent_Factory(t) {\n    return new (t || PaymentCartComponent)(i0.ɵɵdirectiveInject(i1.DialogService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.PaymentService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i2.ShopService), i0.ɵɵdirectiveInject(i2.TransactionService), i0.ɵɵdirectiveInject(i2.TransactionDetailsService), i0.ɵɵdirectiveInject(i2.OrderService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i2.AddressService), i0.ɵɵdirectiveInject(i2.ProductLogicService), i0.ɵɵdirectiveInject(i2.LoaderService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.ShipmentService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.CustomGAService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PaymentCartComponent,\n    selectors: [[\"app-payment-cart\"]],\n    hostBindings: function PaymentCartComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function PaymentCartComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      deliveryOptionDetails: \"deliveryOptionDetails\",\n      refreshSummary: \"refreshSummary\",\n      cartItems: \"cartItems\",\n      paymentMethodDetails: \"paymentMethodDetails\",\n      isFetchOrderPaymentConfig: \"isFetchOrderPaymentConfig\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService]), i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 3,\n    consts: [[\"class\", \"new-payment-cart\", 4, \"ngIf\"], [\"class\", \"old-payment-cart\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"new-payment-cart\"], [4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [1, \"payment-card\", \"d-none\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"px-7\", \"bg-white\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"my-2\", \"second-btn\", 3, \"label\", \"click\"], [1, \"new-checkout-card\"], [1, \"checkout-card\"], [1, \"row\"], [1, \"col-md-12\", \"error-container\"], [1, \"error-msg\"], [1, \"button-container-mobile\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"button-content\"], [1, \"items\"], [1, \"price\"], [1, \"checkout-button\"], [\"fill\", \"none\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"width\", \"20\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M3.125 10L16.875 10\", \"stroke\", \"#F5F7FC\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\"], [\"d\", \"M11.25 15.625L16.875 10L11.25 4.375\", \"stroke\", \"#F5F7FC\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\"], [1, \"payment-card\"], [\"class\", \"payment-card__arrives\", 4, \"ngIf\"], [1, \"payment-card__checkout\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"payment-card__arrives\"], [1, \"ckeckout-count\"], [1, \"old-payment-cart\"], [3, \"displayModal\"]],\n    template: function PaymentCartComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, PaymentCartComponent_div_0_Template, 8, 5, \"div\", 0);\n        i0.ɵɵtemplate(1, PaymentCartComponent_div_1_Template, 5, 3, \"div\", 1);\n        i0.ɵɵtemplate(2, PaymentCartComponent_ng_container_2_Template, 2, 1, \"ng-container\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.displayLoaderModal);\n      }\n    },\n    dependencies: [i8.NgIf, i8.NgStyle, i9.ButtonDirective, i10.LightboxLoaderModalComponent, i8.DecimalPipe, i5.TranslatePipe],\n    styles: [\".new-payment-cart[_ngcontent-%COMP%]   .payment-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 0px 24px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 16px;\\n  align-self: stretch;\\n  margin-bottom: 85px;\\n}\\n.new-payment-cart[_ngcontent-%COMP%]   .payment-card__arrives[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n.new-payment-cart[_ngcontent-%COMP%]   .payment-card__checkout[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 56px;\\n  padding: 0px 24px;\\n  justify-content: center;\\n  align-self: stretch;\\n  border-radius: 6px;\\n  background: var(--main_bt_txtcolor);\\n  color: #FFF;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 56px; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n  border: none;\\n}\\n\\n.old-payment-cart[_ngcontent-%COMP%]   .arrives-tag[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n.old-payment-cart[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n}\\n@media only screen and (max-width: 786px) {\\n  .old-payment-cart[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    font-family: var(--medium-font) !important;\\n  }\\n}\\n\\n.button-container-mobile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 56px;\\n  width: 100%;\\n  padding: 0px 12px;\\n  border-radius: 6px;\\n  background: var(--main_bt_txtcolor);\\n  color: #FFF;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 56px;\\n  letter-spacing: 0.168px;\\n  border: none;\\n  justify-content: space-between;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  line-height: 1.7;\\n  text-align: start;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-family: \\\"main-regular\\\";\\n  text-transform: lowercase;\\n  font-weight: 400;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-family: \\\"main-regular\\\";\\n  font-weight: 700;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .checkout-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: none;\\n  color: #fff;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 700;\\n  font-family: \\\"main-regular\\\";\\n  display: inline-flex;\\n  width: 60%;\\n  justify-content: space-between;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "DialogService", "get", "environment", "RequestDelivery", "fork<PERSON><PERSON>n", "take", "isPlatformBrowser", "GaLocalActionEnum", "PaymentMethodEnum", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "PaymentCartComponent_div_0_ng_container_1_Template_button_click_6_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "show", "ɵɵtext", "ɵɵnamespaceSVG", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "isProceccedCheckOut", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate2", "orderDetails", "productDetails", "length", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "currencyCode", "disableCent", "ɵɵpipeBind2", "orderAmount", "shipmentService", "shipmentCost", "orderDiscount", "decimalValue", "ɵɵtextInterpolate", "ctx_r8", "timeString", "ɵɵtemplate", "PaymentCartComponent_div_0_ng_template_2_div_1_Template", "PaymentCartComponent_div_0_ng_template_2_Template_button_click_2_listener", "_r10", "ctx_r9", "ctx_r5", "isShipmentFeeExist", "_c1", "PaymentCartComponent_div_0_ng_container_1_Template", "PaymentCartComponent_div_0_ng_template_2_Template", "ɵɵtemplateRefExtractor", "PaymentCartComponent_div_0_Template_button_click_6_listener", "_r12", "ctx_r11", "ctx_r0", "isMobileTemplate", "screenWidth", "_r4", "ɵɵpropertyInterpolate", "PaymentCartComponent_div_1_Template_button_click_3_listener", "_r14", "ctx_r13", "ctx_r2", "displayLoaderModal", "PaymentCartComponent", "onResize", "event", "platformId", "window", "innerWidth", "constructor", "dialogService", "store", "paymentService", "router", "messageService", "shopService", "transactionService", "transactionDetailsService", "orderService", "translate", "addressService", "productLogicService", "loaderService", "authService", "mainDataService", "permissionService", "$gaService", "renderer", "cd", "_GACustomEvent", "lightBoxData", "shopsPayments", "Array", "totalLogisticsFee", "subOrdersCommission", "shipmentFee", "totalDiscount", "allowedpaymentMethod", "showPayButton", "AmountTrxn", "requestData", "minDate", "Date", "toDateString", "maxDate", "multipleTransactionDetails", "PaymentCalled", "shopIds", "merchantId", "terminalId", "OrderId", "OrderShopId", "minTimeDelivery", "Number", "MAX_SAFE_INTEGER", "maxTimeDelivery", "MomoPayCommissionAmount", "cartId", "arrivalDate", "isLightboxLoaded", "isFetchOrderPaymentConfig", "isLayoutTemplate", "isGoogleAnalytics", "itemsTotalPrices", "value", "localStorage", "getItem", "parseInt", "hasPermission", "ngOnInit", "_this", "_asyncToGenerator", "getOrderData", "getCartId", "addressSubscription", "getCustomAddress", "subscribe", "res", "getFullOrderData", "mobileSubscription", "getCustomMobile", "UpdateOrder", "getCurrentCartId", "subscription", "next", "data", "find", "obj", "key", "toLocaleLowerCase", "lightBoxURL", "currency", "displayName", "countryCode", "dropOffContactInfo", "pickupContactInfo", "error", "err", "console", "sessionId", "userDetails", "refreshSubscription", "refreshSummary", "getDiscountValue", "_this2", "getCustomerAddress", "getCustomerPhone", "get<PERSON><PERSON><PERSON>", "records", "<PERSON><PERSON><PERSON><PERSON>", "region", "city", "add", "severity", "summary", "instant", "detail", "navigate", "id", "queryParams", "returnUrl", "deliveryOptionDetails", "loadedAddress", "hide", "message", "ngOnChanges", "deliveryOption", "orderId", "AddressId", "GetOrderWithPaymentsConfigurations", "success", "orderDetailsWithConfig", "subOrderDetails", "shopsDetails", "SubOrdersCommission", "amountTrxn", "shopId", "momoPayCommissionAmount", "totalDeliveryCost", "actualShipmentFee", "calculateShipmentFeeRes", "adjustedShipmentFee", "for<PERSON>ach", "item", "currentShipment", "shipmentDetails", "maxTime", "minTime", "set", "CallLightBox", "CLICK_ON_PROCEED_TO_PAYMENT", "currentOrderTotal", "mobileNumber", "deviceType", "deviceId", "paymentMethodDetails", "name", "getPhoneNumbers", "PromotionStockCheck", "verifyOrderProductsVisibilityBeforeCheckout", "phoneNumberRes", "promotionStockCheck", "visibilityCheck", "promotionStockStatus", "promotionalStockAvailable", "isValidOrder", "navigateByUrl", "Lightbox", "Checkout", "showLightbox", "complete", "log", "ngAfterViewInit", "observer", "MutationObserver", "mutations", "mutation", "type", "from", "addedNodes", "node", "nodeName", "iframe", "addIframeLoadListener", "observe", "document", "body", "childList", "subtree", "iframeLoadListener", "listen", "detectChanges", "accessIframeElements", "iframeDocument", "contentDocument", "contentWindow", "iframeBody", "element", "querySelector", "that", "storedLang", "isStoreCloud", "getData", "configure", "defaultPaymentMethod", "includes", "momoPaySubMerchantsDataStr", "JSON", "stringify", "MID", "TID", "lang", "MerchantReference", "AdditionalCustomerData", "CustomerMobile", "userPhoneNumber", "MomoPayLogisticFees", "MomoPayDiscounts", "paymentMethodFromLightBox", "completeCallback", "transactionData", "TransactionId", "SystemReference", "CardNumber", "PayerAccount", "PaymentMethod", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UpdateProductQuantityList", "PaymentSuccess", "onDeleteCart", "closeLightbox", "updateOrderStatus", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "cancelCallback", "purchaseEvent", "cartItems", "getProductShopLatLng", "transactionId", "productId", "cost", "getShopById", "location", "lat", "lng", "addressLatLng", "toString", "applyTo", "updateOrder", "TransferReferenceId", "paymentResult", "total", "addressId", "StreetAddress", "streetAddress", "State", "state", "Floor", "floor", "BuldingNumber", "buldingNumber", "CountryName", "countryName", "City", "LandMark", "landMark", "getTagFeature", "setCartLenghtData", "setCartItemsData", "emptyCart", "map", "phoneNumber", "getOrderDiscount", "ngOnDestroy", "unsubscribe", "disconnect", "_", "ɵɵdirectiveInject", "i1", "i2", "StoreService", "PaymentService", "i3", "Router", "i4", "MessageService", "ShopService", "TransactionService", "TransactionDetailsService", "OrderService", "i5", "TranslateService", "AddressService", "ProductLogicService", "LoaderService", "AuthService", "MainDataService", "PermissionService", "i6", "GoogleAnalyticsService", "ShipmentService", "Renderer2", "ChangeDetectorRef", "i7", "CustomGAService", "_2", "selectors", "hostBindings", "PaymentCartComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PaymentCartComponent_Template", "PaymentCartComponent_div_0_Template", "PaymentCartComponent_div_1_Template", "PaymentCartComponent_ng_container_2_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\components\\payment-cart\\payment-cart.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\components\\payment-cart\\payment-cart.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  HostListener,\r\n  Inject,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  PLATFORM_ID,\r\n  Renderer2,\r\n  SimpleChanges,\r\n  ChangeDetectorRef\r\n} from '@angular/core';\r\nimport { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { MessageService } from 'primeng/api';\r\nimport { get } from 'scriptjs';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { environment } from '@environments/environment';\r\n\r\nimport { RequestDelivery } from '@core/interface';\r\n\r\nimport {\r\n  AddressService,\r\n  LoaderService,\r\n  OrderService,\r\n  ShopService,\r\n  ShipmentService,\r\n  TransactionDetailsService,\r\n  TransactionService,\r\n  StoreService,\r\n  PaymentService,\r\n  ProductLogicService, AuthService, MainDataService, PermissionService\r\n} from '@core/services';\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport {Observable, Subject, Subscription, forkJoin, switchMap, take} from \"rxjs\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport { PaymentMethodEnum } from '@core/enums/payment-method-enum';\r\nimport { CustomGAService } from '@core/services/custom-GA.service';\r\n\r\ndeclare const Lightbox: any;\r\ndeclare const $: any;\r\n\r\n@Component({\r\n  selector: 'app-payment-cart',\r\n  templateUrl: './payment-cart.component.html',\r\n  styleUrls: ['./payment-cart.component.scss'],\r\n  providers: [DialogService],\r\n})\r\nexport class PaymentCartComponent implements OnInit, OnChanges {\r\n  @Input() deliveryOptionDetails: any;\r\n  @Input() refreshSummary: Subject<void>;\r\n  @Input() cartItems: any[];\r\n  @Input() paymentMethodDetails:any;\r\n  private refreshSubscription: Subscription;\r\n  ref: DynamicDialogRef | undefined;\r\n  lightBoxData: any = null;\r\n  shopsPayments = new Array<any>();\r\n  totalLogisticsFee = 0;\r\n  subOrdersCommission = new Array<any>();\r\n  lightBoxURL: any;\r\n  arrayIframe!: any[];\r\n  shipmentFee: number = 0;\r\n  totalDiscount: number = 0;\r\n  allowedpaymentMethod: number = 0;\r\n  userPhoneNumber: any;\r\n  showPayButton: boolean = false;\r\n  orderDetails: any;\r\n  orderDetailsWithConfig: any;\r\n  paymentResult: any;\r\n  currency: any;\r\n  countryCode: any;\r\n  countryPhone: any;\r\n  shipmentCost: any = -1;\r\n  AmountTrxn = 0;\r\n  requestData = new RequestDelivery();\r\n  shipmentDetails: any;\r\n  subOrderDetails: any;\r\n  minDate: any = new Date().toDateString();\r\n  maxDate: any = new Date().toDateString();\r\n  multipleTransactionDetails = new Array<any>();\r\n  PaymentCalled: boolean = false;\r\n  shopIds = [];\r\n  merchantId: string = '';\r\n  terminalId: string = '';\r\n  isMobileTemplate: boolean = false;\r\n  OrderId: string = '';\r\n  OrderShopId: number | null = null;\r\n  minTimeDelivery: number = Number.MAX_SAFE_INTEGER;\r\n  maxTimeDelivery: number = -1;\r\n  MomoPayCommissionAmount = 0;\r\n  cartId: number = 0;\r\n  arrivalDate: string = '';\r\n  timeString: string = '';\r\n  isLightboxLoaded: boolean = false;\r\n  @Input() isFetchOrderPaymentConfig: boolean = false;\r\n  isProceccedCheckOut: boolean = false;\r\n  isLayoutTemplate: boolean = false;\r\n  isShipmentFeeExist : boolean = false;\r\n  isGoogleAnalytics: boolean = false;\r\n  itemsTotalPrices: number = 0;\r\n  currencyCode: string = '';\r\n  decimalValue: number = 0;\r\n  disableCent: any;\r\n  displayLoaderModal= false;\r\n  private observer: MutationObserver;\r\n  private iframeLoadListener: () => void;\r\n  addressSubscription: Subscription;\r\n  mobileSubscription: Subscription;\r\n  screenWidth:any=window.innerWidth;\r\n  sessionId: string | null;\r\n  userDetails: any;\r\n  orderDiscount: number = 0;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n  constructor(\r\n    public dialogService: DialogService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n    public store: StoreService,\r\n    public paymentService: PaymentService,\r\n    private router: Router,\r\n    public messageService: MessageService,\r\n    private shopService: ShopService,\r\n    private transactionService: TransactionService,\r\n    private transactionDetailsService: TransactionDetailsService,\r\n    private orderService: OrderService,\r\n    private translate: TranslateService,\r\n    private addressService: AddressService,\r\n    private productLogicService: ProductLogicService,\r\n    private loaderService: LoaderService,\r\n    private authService: AuthService,\r\n    private mainDataService: MainDataService,\r\n    private permissionService: PermissionService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    public shipmentService: ShipmentService,\r\n    private renderer: Renderer2,\r\n    private cd : ChangeDetectorRef,\r\n    private _GACustomEvent:CustomGAService\r\n  ) {\r\n\r\n    let value = localStorage.getItem('CurrencyDecimal');\r\n    if (value)\r\n      this.decimalValue = parseInt(value);\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')\r\n    this.disableCent = localStorage.getItem('DisableCents');\r\n    this.isFetchOrderPaymentConfig = false;\r\n  }\r\n\r\n\r\n\r\n\r\n  async ngOnInit() {\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n    this.isShipmentFeeExist = this.permissionService.hasPermission('Shipment-Fee')\r\n    this.getOrderData();\r\n    this.getCartId();\r\n\r\n    this.addressSubscription = this.addressService.getCustomAddress().subscribe((res: any) => {\r\n      if (res) {\r\n        this.getFullOrderData();\r\n      }\r\n    })\r\n    this.mobileSubscription = this.addressService.getCustomMobile().subscribe((res: any) => {\r\n      if (res) {\r\n        this.UpdateOrder();\r\n      }\r\n    })\r\n    // this.addressService.addresseData.subscribe((res: any) => {\r\n    //   if (res) {\r\n    //     this.getFullOrderData();\r\n    //   }\r\n    // })\r\n    // this.addressService.mobileData.subscribe((res: any) => {\r\n    //   if (res) {\r\n    //     this.UpdateOrder();\r\n    //   }\r\n    // })\r\n    this.showPayButton = false;\r\n    this.getCurrentCartId();\r\n    this.store.subscription('mainData').subscribe({\r\n      next: (res: any) => {\r\n\r\n        let data = res.find(\r\n          (obj: any) =>\r\n            obj.key.toLocaleLowerCase() === 'LightBoxURL'.toLocaleLowerCase()\r\n        );\r\n        if (data) this.lightBoxURL = data.lightBoxURL;\r\n        data = res.find(\r\n          (obj: any) =>\r\n            obj.key.toLocaleLowerCase() === 'currency'.toLocaleLowerCase()\r\n        );\r\n        if (data) this.currency = data.displayName;\r\n        data = res.find(\r\n          (obj: any) =>\r\n            obj.key.toLocaleLowerCase() === 'countryCode'.toLocaleLowerCase()\r\n        );\r\n        if (data) this.requestData.countryCode = data.displayName;\r\n        data = res.find(\r\n          (obj: any) =>\r\n            obj.key.toLocaleLowerCase() === 'countryphone'.toLocaleLowerCase()\r\n        );\r\n        if (data) {\r\n          this.requestData.dropOffContactInfo.countryCode = data.displayName;\r\n          this.requestData.pickupContactInfo.countryCode = data.displayName;\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n    this.sessionId = localStorage.getItem('sessionId');\r\n    this.userDetails = this.store.get('profile');\r\n\r\n    this.refreshSubscription = this.refreshSummary.subscribe(()=>{\r\n      this.getDiscountValue();\r\n      this.getFullOrderData()\r\n    })\r\n  }\r\n\r\n  async getOrderData() {\r\n    this.store.subscription('orderData').subscribe({\r\n      next: (res: any) => {\r\n        if (res) {\r\n          this.orderDetails = res;\r\n          if(this.orderDetails.productDetails.length>0){\r\n            this.currencyCode = this.orderDetails.productDetails[0].currencyCode;\r\n          }\r\n          this.getCustomerAddress();\r\n          this.getCustomerPhone();\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n\r\n  }\r\n\r\n  getCustomerAddress() {\r\n    this.addressService.getAddress().subscribe({\r\n      next: (res: any) => {\r\n\r\n        if (res.data.records.length > 0) {\r\n          this.addressService.chosenAddress = res.data.records[0];\r\n          if((!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\") || (!this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\")) {\r\n            this.messageService.add({\r\n              severity: 'info',\r\n              summary: this.translate.instant('ResponseMessages.address'),\r\n              detail: this.translate.instant(\r\n                'ResponseMessages.invalidCityAddress'\r\n              ),\r\n            });\r\n\r\n            this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], { queryParams: { returnUrl: '/checkout' } });\r\n            return;\r\n          }\r\n          if (!this.deliveryOptionDetails && !this.permissionService.hasPermission('Shipment-Fee')) {\r\n            this.getFullOrderData();\r\n\r\n          }\r\n        } else {\r\n          this.messageService.add({\r\n            severity: 'info',\r\n            summary: this.translate.instant('ResponseMessages.address'),\r\n            detail: this.translate.instant(\r\n              'ResponseMessages.pleaseProvideYourAddress'\r\n            ),\r\n          });\r\n\r\n          this.router.navigate(['/account/address'], { queryParams: { returnUrl: '/checkout' } });\r\n          return;\r\n        }\r\n\r\n        this.addressService.loadedAddress = true;\r\n      },\r\n      error: (err: any) => {\r\n\r\n        this.loaderService.hide();\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          detail: err.message,\r\n        });\r\n      },\r\n    });\r\n  }\r\n  ngOnChanges() {\r\n\r\n this.deliveryOptionDetails = this.deliveryOptionDetails?.deliveryOption ? this.deliveryOptionDetails?.deliveryOption : this.deliveryOptionDetails\r\n\r\n    if (this.deliveryOptionDetails && this.permissionService.hasPermission('Shipment-Fee')) {\r\n      this.getFullOrderData()\r\n    }\r\n  }\r\n  getFullOrderData() {\r\n\r\n    if(this.deliveryOptionDetails?.id ){\r\n    let data: any = {\r\n      OrderId: this.orderDetails?.orderId,\r\n      AddressId: this.addressService.chosenAddress.id,\r\n\r\n    };\r\n\r\n    data['deliveryOptionId'] = this.deliveryOptionDetails?.id || data['deliveryOptionId'];\r\n\r\n    this.orderService.GetOrderWithPaymentsConfigurations(data).subscribe({\r\n      next: (res: any) => {\r\n        if (res?.success) {\r\n          this.isProceccedCheckOut = true;\r\n          this.isFetchOrderPaymentConfig = true\r\n\r\n          this.orderDetailsWithConfig = res.data;\r\n          this.lightBoxURL = res.data.lightBoxURL;\r\n          this.merchantId = res.data.merchantId;\r\n          this.terminalId = res.data.terminalId;\r\n          this.subOrderDetails = res.data.shopsDetails;\r\n          this.shopsPayments = res.data.shopsPayments;\r\n          this.totalLogisticsFee = res.data.totalLogisticsFee;\r\n          this.subOrdersCommission = res.data.SubOrdersCommission;\r\n          this.AmountTrxn = res.data.amountTrxn;\r\n          this.OrderId = this.orderDetails.orderId;\r\n          this.OrderShopId = this.orderDetails.shopId;\r\n          this.MomoPayCommissionAmount = res.data.momoPayCommissionAmount;\r\n          this.shipmentService.shipmentCost = res.data.totalDeliveryCost;\r\n          this.shipmentService.actualShipmentFee = res.data.calculateShipmentFeeRes ? res.data.calculateShipmentFeeRes.adjustedShipmentFee : 0\r\n          this.shipmentFee = 0;\r\n          this.totalDiscount = res.data.totalDiscount;\r\n          this.allowedpaymentMethod = res.data?.allowedpaymentMethod;\r\n          if (res.data?.shopsDetails.length) {\r\n            res.data?.shopsDetails.forEach((item: any) => {\r\n              if (item.shipmentFee) {\r\n                this.shipmentFee = this.shipmentFee + item.shipmentFee\r\n              }\r\n            })\r\n          }\r\n          this.maxTimeDelivery = res.data.maxTimeDelivery;\r\n          this.minTimeDelivery = res.data.minTimeDelivery;\r\n\r\n\r\n          this.timeString = res.data.timeString;\r\n          this.shipmentService.currentShipment = {\r\n            totalDeliveryCost: Number(this.shipmentService.shipmentCost),\r\n            shipmentDetails: this.shipmentDetails,\r\n            maxTime: this.maxTimeDelivery,\r\n            minTime: this.minTimeDelivery,\r\n          };\r\n          this.showPayButton = true;\r\n          this.store.set('shipmentCost', {\r\n            totalDeliveryCost: res.data.totalDeliveryCost,\r\n            shipmentDetails: this.shipmentDetails,\r\n            deliveryOption: this.deliveryOptionDetails\r\n\r\n          });\r\n\r\n          this.UpdateOrder();\r\n          this.CallLightBox();\r\n        } else {\r\n          this.isProceccedCheckOut = false;\r\n          if (res?.message != null) {\r\n            if(res.message === 'City is not defined in any region. Please update your address.' ||\r\n              (((!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\") ||\r\n                (!this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\")))) {\r\n              this.messageService.add({\r\n                severity: 'info',\r\n                summary: this.translate.instant('ResponseMessages.address'),\r\n                detail: this.translate.instant(\r\n                  'ResponseMessages.invalidCityAddress'\r\n                ),\r\n              });\r\n\r\n              this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], { queryParams: { returnUrl: '/checkout' } });\r\n              return;\r\n            } else {\r\n              this.messageService.add({\r\n                severity: 'error',\r\n                summary: this.translate.instant('ErrorMessages.fetchError'),\r\n                detail: res.message\r\n              });\r\n            }\r\n          }\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.isProceccedCheckOut = false;\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          detail: this.translate.instant(\r\n            'ErrorMessages.pleaseContactCallCenter'\r\n          ),\r\n        });\r\n        this.router.navigate(['/cart']);\r\n      },\r\n    });\r\n  }\r\n  }\r\n\r\n /**\r\n * This function handles the display of the Lightbox for the checkout process, \r\n * logs analytics events if enabled, and performs a series of API calls to validate \r\n * promotional stock and retrieve phone numbers. Based on the API responses, \r\n * it determines whether to proceed with the checkout flow or redirect the user back to the cart.\r\n */\r\n show() {\r\n  \r\n  if (this.isGoogleAnalytics) {\r\n      // Log an analytics event for proceeding to payment\r\n      this.$gaService.event(\r\n          GaLocalActionEnum.CLICK_ON_PROCEED_TO_PAYMENT, // Action enum\r\n          'checkout',                                   // Category\r\n          'PROCEED_TO_PAYMENT',                        // Label\r\n          1,                                           // Value\r\n          true,                                        // Non-interaction flag\r\n          {\r\n              \"order_amount\": this.shipmentService.currentOrderTotal + this.shipmentService.shipmentCost,\r\n              \"order_ID\": this.OrderId,\r\n              \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\r\n              \"session_ID\": this.sessionId,\r\n              \"ip_Address\": this.store.get('userIP'),\r\n              \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n              \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n              \"order_commission\": this.MomoPayCommissionAmount,\r\n              \"merchant_ID\": this.merchantId,\r\n              \"shipping_fee\": this.shipmentFee,\r\n              \"order_totalItems\": this.orderDetails?.productDetails?.length,\r\n              \"payment_method\": this.paymentMethodDetails,\r\n              \"suborder_commission\": this.subOrdersCommission,\r\n              \"delivery_option\": this.deliveryOptionDetails.name\r\n          }\r\n      );\r\n  }\r\n\r\n  forkJoin([\r\n      this.authService.getPhoneNumbers(),                             \r\n      this.authService.PromotionStockCheck(this.orderDetails.orderId),\r\n      this.orderService.verifyOrderProductsVisibilityBeforeCheckout(  \r\n          {OrderId:this.orderDetails.orderId}\r\n      )\r\n  ]).subscribe({\r\n      next: ([phoneNumberRes, promotionStockCheck, visibilityCheck]: [any, any, any]) => {\r\n          let promotionStockStatus = promotionStockCheck.data.promotionalStockAvailable;\r\n          let isValidOrder = visibilityCheck?.success;\r\n\r\n          if(!promotionStockStatus ){\r\n            this.router.navigateByUrl('/cart');\r\n          }\r\n\r\n          if (!isValidOrder ) {              \r\n              this.messageService.add({\r\n                severity: 'error',\r\n                summary: this.translate.instant('ResponseMessages.orderIsInValid'),\r\n              });\r\n              this.router.navigateByUrl('/cart');\r\n          } \r\n\r\n          else if (phoneNumberRes.success && promotionStockStatus && isValidOrder) {\r\n              this.displayLoaderModal = true; \r\n              this.showPayButton = false;    \r\n\r\n              if (this.isProceccedCheckOut) {\r\n                  Lightbox.Checkout.showLightbox();\r\n              }\r\n          }\r\n      },\r\n      error: (err) => {\r\n          console.error('Error in API calls', err);\r\n      },\r\n      complete: () => {\r\n          console.log('All API calls completed.');\r\n      }\r\n  });\r\n}\r\n\r\n\r\n  ngAfterViewInit() {\r\n    // Set up a MutationObserver to watch for changes in the DOM\r\n    this.observer = new MutationObserver((mutations) => {\r\n      for (let mutation of mutations) {\r\n        if (mutation.type === 'childList') {\r\n          Array.from(mutation.addedNodes).forEach((node: Node) => {\r\n            if (node.nodeName === 'IFRAME') {\r\n              const iframe = node as HTMLIFrameElement;\r\n              this.addIframeLoadListener(iframe);\r\n            }\r\n          });\r\n        }\r\n      }\r\n    });\r\n\r\n    // Start observing the body for added nodes\r\n    this.observer.observe(document.body, { childList: true, subtree: true });\r\n  }\r\n\r\n  private addIframeLoadListener(iframe: HTMLIFrameElement) {\r\n    this.displayLoaderModal = true;\r\n    this.iframeLoadListener = this.renderer.listen(iframe, 'load', () => {\r\n     this.displayLoaderModal = false;\r\n     this.cd.detectChanges()\r\n      this.accessIframeElements(iframe);\r\n    });\r\n  }\r\n\r\n  private accessIframeElements(iframe: HTMLIFrameElement) {\r\n    try {\r\n      const iframeDocument = iframe.contentDocument || iframe?.contentWindow?.document;\r\n      const iframeBody = iframeDocument?.body;\r\n\r\n      // Example: Check for a specific element or value inside the iframe\r\n      const element = iframeDocument?.querySelector('#elementId');\r\n      if (element) {\r\n        console.log('Element inside iframe:', element);\r\n      } else {\r\n        console.log('Element not found inside iframe');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error accessing iframe content:', error);\r\n    }\r\n  }\r\n  // ngAfterViewInit() {\r\n  //   this.iframeInterval = setInterval(() => {\r\n  //     const iframe = document.querySelector('iframe'); // Adjust the selector to match your iframe\r\n  //     if (iframe && iframe.contentWindow && iframe.contentDocument) {\r\n  //       this.loaderService.show();\r\n  //       this.accessIframeElements(iframe as HTMLIFrameElement);\r\n  //       clearInterval(this.iframeInterval);\r\n  //     }\r\n  //   }, 100);\r\n  // }\r\n  // accessIframeElements(iframe: HTMLIFrameElement) {\r\n  //\r\n  //   try {\r\n  //     const iframeDocument = iframe?.contentWindow?.document || iframe.contentDocument;\r\n  //     const iframeBody = iframeDocument?.body;\r\n  //\r\n  //     // Example: Accessing an element inside the iframe\r\n  //     const element = iframeDocument?.querySelector('#DivInsidemyModal');\r\n  //     if (element) {\r\n  //       console.log('Element inside iframe:', element);\r\n  //     } else {\r\n  //       console.log('Element not found inside iframe');\r\n  //     }\r\n  //   } catch (error) {\r\n  //     console.error('Error accessing iframe content:', error);\r\n  //   }\r\n  // }\r\n  CallLightBox() {\r\n    let that = this;\r\n    let storedLang = localStorage.getItem('lang');\r\n\r\n    get(this.lightBoxURL, () => {\r\n      if (environment.isStoreCloud) {\r\n    this.getData(that,storedLang);\r\n      } else {\r\n        Lightbox.Checkout.configure = {\r\n          defaultPaymentMethod: this.paymentMethodDetails?.toLocaleLowerCase()?.includes('card') ? PaymentMethodEnum['Card'] : PaymentMethodEnum['Wallet'] ,\r\n          momoPaySubMerchantsDataStr: JSON.stringify(this.shopsPayments),\r\n          MomoPayCommissionAmount: this.MomoPayCommissionAmount,\r\n          MID: this.merchantId,\r\n          TID: this.terminalId,\r\n          lang: storedLang,\r\n          MerchantReference: this.OrderId ?? '',\r\n          AmountTrxn: this.AmountTrxn,\r\n          AdditionalCustomerData: {\r\n            CustomerMobile: this.userPhoneNumber\r\n          },\r\n          MomoPayLogisticFees: this.totalLogisticsFee,\r\n          MomoPayDiscounts: -this.totalDiscount,\r\n          paymentMethodFromLightBox: this.totalDiscount ? this.allowedpaymentMethod : PaymentMethodEnum['Both'],  // relative to cube  enum for  payment methods \r\n\r\n\r\n\r\n          completeCallback: function (data: any) {\r\n\r\n\r\n            that.loaderService.hide();\r\n            let transactionData = {\r\n              TransactionId: data.SystemReference,\r\n              CardNumber: data.PayerAccount,\r\n              PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\r\n              UpdateProductQuantityList: null, // updateInventoryQuantity,\r\n              OrderId: this.OrderId,\r\n              OrderShopId: this.OrderShopId,\r\n            };\r\n\r\n            that.PaymentSuccess(transactionData);\r\n            that.onDeleteCart();\r\n            Lightbox.Checkout.closeLightbox();\r\n            if (data) {\r\n              that.orderService.updateOrderStatus(that.orderDetails.orderId).pipe(\r\n                take(1),\r\n              ).subscribe(res=>{\r\n                that.showPayButton = true;\r\n              } )\r\n            \r\n            }\r\n          },\r\n          \r\n          errorCallback: function () {\r\n            that.loaderService.hide();\r\n            that.showPayButton = true;\r\n            Lightbox.Checkout.closeLightbox();\r\n          },\r\n          cancelCallback: function () {\r\n            that.loaderService.hide();\r\n            that.showPayButton = true;\r\n\r\n            Lightbox.Checkout.closeLightbox();\r\n          },\r\n        };\r\n      }\r\n\r\n    });\r\n  }\r\n\r\n  PaymentSuccess(transactionData: any) {\r\n    this.store.set('transactionData', transactionData);\r\n    this._GACustomEvent.purchaseEvent(this.cartItems,transactionData.TransactionId,this.shipmentFee,this.currencyCode)\r\n    this.router.navigate(['/checkout/success']);\r\n  }\r\n\r\n  getProductShopLatLng(\r\n    transactionId: any,\r\n    shopId: any,\r\n    orderId: any,\r\n    productId: any,\r\n    cost: any\r\n  ) {\r\n    this.shopService.getShopById(shopId).subscribe({\r\n      next: (res: any) => {\r\n        if (res.data === null) {\r\n          return;\r\n        }\r\n\r\n        let location = [res.data.lat, res.data.lng];\r\n        this.requestData.pickupContactInfo.addressLatLng =\r\n          '[' + location.toString() + ']';\r\n        this.requestData.dropOffContactInfo.addressLatLng =\r\n          '[' +\r\n          [\r\n            this.addressService.chosenAddress.lat,\r\n            this.addressService.chosenAddress.lng,\r\n          ].toString() +\r\n          ']';\r\n\r\n      },\r\n      error: (err: any) => {\r\n      },\r\n    });\r\n  }\r\n\r\n  UpdateOrder() {\r\n    let applyTo: any = null\r\n    let deliveryOption: any = null\r\n    if (this.deliveryOptionDetails) {\r\n      if (this.deliveryOptionDetails.applyTo) {\r\n        applyTo = this.deliveryOptionDetails.applyTo\r\n      }\r\n      if (this.deliveryOptionDetails.applyTo) {\r\n        deliveryOption = this.deliveryOptionDetails.id\r\n      }\r\n    }\r\n    this.orderService\r\n      .updateOrder({\r\n        id: this.orderDetails.orderId,\r\n        TransferReferenceId: this.paymentResult?.SystemReference,\r\n        totalDeliveryCost: this.shipmentService.shipmentCost,\r\n        shipmentFee: this.shipmentFee,\r\n        applyTo: applyTo,\r\n        deliveryOption: deliveryOption,\r\n        total:\r\n          this.orderDetails.orderAmount +\r\n          Number(this.shipmentService.shipmentCost) - this.orderDiscount,\r\n        addressId: this.addressService.chosenAddress.id,\r\n        subOrderDetails: this.subOrderDetails,\r\n        StreetAddress: this.addressService.chosenAddress.streetAddress,\r\n        State: this.addressService.chosenAddress.state,\r\n        Floor: this.addressService.chosenAddress.floor,\r\n        BuldingNumber: this.addressService.chosenAddress.buldingNumber,\r\n        CountryName: this.addressService.chosenAddress.countryName,\r\n        City: this.addressService.chosenAddress.city,\r\n        LandMark: this.addressService.chosenAddress.landMark,\r\n      })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n\r\n\r\n        },\r\n        error: (err: any) => {\r\n        },\r\n      });\r\n  }\r\n\r\n  onDeleteCart() {\r\n    let cartId: any = localStorage.getItem('cartId')\r\n    if (!cartId || cartId == '') {\r\n      cartId = this.cartId\r\n    }\r\n    if (this.isGoogleAnalytics &&  this.permissionService.getTagFeature('delete_cart')) {\r\n      this.$gaService.event('delete_cart', 'cart', cartId);\r\n    }\r\n    this.mainDataService.setCartLenghtData(0);\r\n    this.mainDataService.setCartItemsData([]);\r\n    this.productLogicService.emptyCart(cartId);\r\n  }\r\n\r\n  getCurrentCartId() {\r\n    this.store.subscription('cartProducts').subscribe({\r\n      next: (res: any) => {\r\n\r\n        this.cartId = res[0]?.cartId;\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n  }\r\n\r\n  private getData(that:any,storedLang:any ) {\r\n    Lightbox.Checkout.configure = {\r\n\r\n      MID: this.merchantId,\r\n      TID: this.terminalId,\r\n      lang: storedLang,\r\n      MerchantReference: this.OrderId ?? '',\r\n      AmountTrxn: 120,\r\n      AdditionalCustomerData: {\r\n        CustomerMobile: this.userPhoneNumber\r\n      },\r\n\r\n\r\n      completeCallback: function (data: any) {\r\n\r\n\r\n        that.loaderService.hide();\r\n        let transactionData = {\r\n          TransactionId: data.SystemReference,\r\n          CardNumber: data.PayerAccount,\r\n          PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\r\n          UpdateProductQuantityList: null, // updateInventoryQuantity,\r\n          OrderId: this.OrderId,\r\n          OrderShopId: this.OrderShopId,\r\n        };\r\n\r\n        that.PaymentSuccess(transactionData);\r\n        that.onDeleteCart();\r\n        Lightbox.Checkout.closeLightbox();\r\n        if (data) {\r\n          that.orderService\r\n            .updateOrderStatus(that.orderDetails.orderId)\r\n            .subscribe((res:any) => {\r\n              that.showPayButton = true;\r\n            });\r\n        }\r\n      },\r\n      errorCallback: function () {\r\n\r\n\r\n        that.loaderService.hide();\r\n\r\n        that.showPayButton = true;\r\n        Lightbox.Checkout.closeLightbox();\r\n      },\r\n      cancelCallback: function () {\r\n        that.loaderService.hide();\r\n        that.showPayButton = true;\r\n\r\n        Lightbox.Checkout.closeLightbox();\r\n      },\r\n    };\r\n  }\r\n\r\n  getCustomerPhone() {\r\n    this.authService.getPhoneNumbers().subscribe({\r\n      next: (res: any) => {\r\n        if(res.data && res.data.records && res.data.records.length) {\r\n          // let mobileData = res.data.records.find((obj: any) => obj.isPrimary === true);\r\n          // if (!mobileData) {\r\n          //   mobileData =  res.data.records[0];\r\n          // }\r\n          this.userPhoneNumber = res.data.records.map((obj: any) => obj.phoneNumber)\r\n          this.requestData.dropOffContactInfo.phoneNumber = res.data.records.map((obj: any) => obj.phoneNumber)\r\n        } else {\r\n          this.userPhoneNumber = localStorage.getItem('phoneNumberArray')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  getDiscountValue() {\r\n    this.orderService.getOrderDiscount(this.orderDetails.orderId).subscribe({\r\n      next: (res) => {\r\n        if (res.success) {\r\n          this.orderDiscount = res.data >= this.orderDetails.orderAmount ? this.orderDetails.orderAmount : res.data ;\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.addressSubscription.unsubscribe()\r\n    this.mobileSubscription.unsubscribe()\r\n    if (this.observer) {\r\n      this.observer.disconnect();\r\n    }\r\n    if (this.iframeLoadListener) {\r\n      this.iframeLoadListener(); // Remove the event listener\r\n    }\r\n    // this.addressService.addresses.unsubscribe()\r\n\r\n    if (this.refreshSubscription) {\r\n      this.refreshSubscription.unsubscribe();\r\n    }\r\n  }\r\n  getCartId() {\r\n\r\n  }\r\n}\r\n", "<div *ngIf=\"isLayoutTemplate\" class=\"new-payment-cart\">\r\n\r\n  <ng-container *ngIf=\"isMobileTemplate && screenWidth <= 768; else oldContainer\">\r\n    <div class=\"new-checkout-card\">\r\n      <section class=\"checkout-card\">\r\n\r\n        <div class=\"row\">\r\n          <div class=\"col-md-12 error-container\">\r\n            <p class=\"error-msg\"></p>\r\n          </div>\r\n        </div>\r\n\r\n        <button (click)=\"show()\" [disabled]=\"!isProceccedCheckOut\" [ngStyle]=\"{'opacity':   !isProceccedCheckOut? '0.5': ''}\"\r\n                class=\"button-container-mobile\">\r\n          <div class=\"button-content\">\r\n              <span class=\"items\">{{\r\n                  orderDetails.productDetails\r\n                    .length\r\n                }} {{\r\n                  (orderDetails.productDetails.length > 1 ? 'checkout.items' : 'checkout.item') | translate\r\n                }}</span>\r\n            <span class=\"price\">\r\n                 {{ currencyCode }}\r\n              <!--            {{orderDetails.orderAmount | number : \"1.\" + decimalValue + \"-\" + decimalValue}}-->\r\n              {{\r\n                disableCent === \"false\" ? (orderDetails.orderAmount + shipmentService.shipmentCost - orderDiscount | number: \"1.\" + decimalValue + \"-\" + decimalValue)\r\n                  :\r\n                  (orderDetails.orderAmount + shipmentService.shipmentCost - orderDiscount)\r\n              }}\r\n              </span>\r\n\r\n          </div>\r\n          <div class=\"checkout-button\">\r\n            {{ 'checkout.paymentCart.PayNow' | translate }}\r\n\r\n\r\n            <svg fill=\"none\" height=\"20\" viewBox=\"0 0 20 20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path d=\"M3.125 10L16.875 10\" stroke=\"#F5F7FC\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                    stroke-width=\"1.5\"/>\r\n              <path d=\"M11.25 15.625L16.875 10L11.25 4.375\" stroke=\"#F5F7FC\" stroke-linecap=\"round\" stroke-linejoin=\"round\"\r\n                    stroke-width=\"1.5\"/>\r\n            </svg>\r\n          </div>\r\n\r\n        </button>\r\n      </section>\r\n    </div>\r\n  </ng-container>\r\n  <ng-template #oldContainer>\r\n      <section class=\"payment-card\">\r\n        <div *ngIf=\"timeString && isShipmentFeeExist\" class=\"payment-card__arrives\">\r\n          {{ \"checkout.paymentCart.arrives\" | translate }}:\r\n          <span class=\"ckeckout-count\">{{ timeString }}</span>\r\n        </div>\r\n        <button (click)=\"show()\" [disabled]=\"!isProceccedCheckOut\" [ngStyle]=\"{\r\n            opacity: !isProceccedCheckOut? '0.5' : ''\r\n          }\" class=\"payment-card__checkout\">\r\n          {{ 'checkout.paymentCart.proceed' | translate }}\r\n        </button>\r\n      </section>\r\n  </ng-template>\r\n\r\n  <!--old implementation-->\r\n  <section class=\"payment-card d-none\">\r\n    <div class=\"grid align-items-center justify-content-center px-7 bg-white border-round\">\r\n      <button (click)=\"show()\" class=\"my-2 second-btn\" label=\"{{ 'checkout.paymentCart.proceed' | translate }}\" pButton\r\n              type=\"button\"></button>\r\n\r\n    </div>\r\n  </section>\r\n</div>\r\n<div *ngIf=\"!isLayoutTemplate\" class=\"old-payment-cart\">\r\n  <section class=\"payment-card\">\r\n    <div\r\n      class=\"grid align-items-center justify-content-center px-7 bg-white border-round\"\r\n    >\r\n      <button\r\n        (click)=\"show()\"\r\n        class=\"my-2 second-btn\"\r\n        label=\"{{ 'checkout.paymentCart.proceed' | translate }}\"\r\n        pButton\r\n        type=\"button\"\r\n      ></button>\r\n\r\n    </div>\r\n  </section>\r\n\r\n\r\n</div>\r\n\r\n<ng-container *ngIf=\"displayLoaderModal\">\r\n  <app-lightbox-loader-modal [displayModal]=\"displayLoaderModal\"></app-lightbox-loader-modal>\r\n</ng-container>\r\n\r\n"], "mappings": ";AAAA,SAOEA,WAAW,QAIN,eAAe;AACtB,SAASC,aAAa,QAA0B,uBAAuB;AAEvE,SAASC,GAAG,QAAQ,UAAU;AAI9B,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,SAASC,eAAe,QAAQ,iBAAiB;AAejD,SAA2CC,QAAQ,EAAaC,IAAI,QAAO,MAAM;AACjF,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,iBAAiB,QAAO,kCAAkC;AAClE,SAASC,iBAAiB,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;ICpCjEC,EAAA,CAAAC,uBAAA,GAAgF;IAC9ED,EAAA,CAAAE,cAAA,aAA+B;IAKvBF,EAAA,CAAAG,SAAA,YAAyB;IAC3BH,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAE,cAAA,iBACwC;IADhCF,EAAA,CAAAK,UAAA,mBAAAC,2EAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,IAAA,EAAM;IAAA,EAAC;IAEtBZ,EAAA,CAAAE,cAAA,cAA4B;IACJF,EAAA,CAAAa,MAAA,GAKhB;;IAAAb,EAAA,CAAAI,YAAA,EAAO;IACbJ,EAAA,CAAAE,cAAA,gBAAoB;IACfF,EAAA,CAAAa,MAAA,IACH;IACAb,EAAA,CAAAa,MAAA,IAKA;;IAAAb,EAAA,CAAAI,YAAA,EAAO;IAGXJ,EAAA,CAAAE,cAAA,eAA6B;IAC3BF,EAAA,CAAAa,MAAA,IAGA;;IAAAb,EAAA,CAAAc,cAAA,EAA+F;IAA/Fd,EAAA,CAAAE,cAAA,eAA+F;IAC7FF,EAAA,CAAAG,SAAA,gBAC0B;IAG5BH,EAAA,CAAAI,YAAA,EAAM;IAMhBJ,EAAA,CAAAe,qBAAA,EAAe;;;;IAnCgBf,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAiB,UAAA,cAAAC,MAAA,CAAAC,mBAAA,CAAiC,YAAAnB,EAAA,CAAAoB,eAAA,KAAAC,GAAA,GAAAH,MAAA,CAAAC,mBAAA;IAGhCnB,EAAA,CAAAgB,SAAA,GAKhB;IALgBhB,EAAA,CAAAsB,kBAAA,KAAAJ,MAAA,CAAAK,YAAA,CAAAC,cAAA,CAAAC,MAAA,OAAAzB,EAAA,CAAA0B,WAAA,QAAAR,MAAA,CAAAK,YAAA,CAAAC,cAAA,CAAAC,MAAA,+CAKhB;IAEDzB,EAAA,CAAAgB,SAAA,GACH;IADGhB,EAAA,CAAA2B,kBAAA,MAAAT,MAAA,CAAAU,YAAA,MACH;IACA5B,EAAA,CAAAgB,SAAA,GAKA;IALAhB,EAAA,CAAA2B,kBAAA,MAAAT,MAAA,CAAAW,WAAA,eAAA7B,EAAA,CAAA8B,WAAA,QAAAZ,MAAA,CAAAK,YAAA,CAAAQ,WAAA,GAAAb,MAAA,CAAAc,eAAA,CAAAC,YAAA,GAAAf,MAAA,CAAAgB,aAAA,SAAAhB,MAAA,CAAAiB,YAAA,SAAAjB,MAAA,CAAAiB,YAAA,IAAAjB,MAAA,CAAAK,YAAA,CAAAQ,WAAA,GAAAb,MAAA,CAAAc,eAAA,CAAAC,YAAA,GAAAf,MAAA,CAAAgB,aAAA,MAKA;IAIFlC,EAAA,CAAAgB,SAAA,GAGA;IAHAhB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA0B,WAAA,6CAGA;;;;;IAcJ1B,EAAA,CAAAE,cAAA,cAA4E;IAC1EF,EAAA,CAAAa,MAAA,GACA;;IAAAb,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAI,YAAA,EAAO;;;;IADpDJ,EAAA,CAAAgB,SAAA,GACA;IADAhB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA0B,WAAA,6CACA;IAA6B1B,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAoC,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAgB;;;;;;;;;;;IAHjDtC,EAAA,CAAAE,cAAA,kBAA8B;IAC5BF,EAAA,CAAAuC,UAAA,IAAAC,uDAAA,kBAGM;IACNxC,EAAA,CAAAE,cAAA,iBAEoC;IAF5BF,EAAA,CAAAK,UAAA,mBAAAoC,0EAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAAmC,IAAA;MAAA,MAAAC,MAAA,GAAA3C,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgC,MAAA,CAAA/B,IAAA,EAAM;IAAA,EAAC;IAGtBZ,EAAA,CAAAa,MAAA,GACF;;IAAAb,EAAA,CAAAI,YAAA,EAAS;;;;IARHJ,EAAA,CAAAgB,SAAA,GAAsC;IAAtChB,EAAA,CAAAiB,UAAA,SAAA2B,MAAA,CAAAN,UAAA,IAAAM,MAAA,CAAAC,kBAAA,CAAsC;IAInB7C,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAiB,UAAA,cAAA2B,MAAA,CAAAzB,mBAAA,CAAiC,YAAAnB,EAAA,CAAAoB,eAAA,IAAA0B,GAAA,GAAAF,MAAA,CAAAzB,mBAAA;IAGxDnB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAA0B,WAAA,4CACF;;;;;;IA1DR1B,EAAA,CAAAE,cAAA,aAAuD;IAErDF,EAAA,CAAAuC,UAAA,IAAAQ,kDAAA,4BA6Ce;IACf/C,EAAA,CAAAuC,UAAA,IAAAS,iDAAA,gCAAAhD,EAAA,CAAAiD,sBAAA,CAYc;IAGdjD,EAAA,CAAAE,cAAA,iBAAqC;IAEzBF,EAAA,CAAAK,UAAA,mBAAA6C,4DAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAyC,OAAA,CAAAxC,IAAA,EAAM;IAAA,EAAC;;IACFZ,EAAA,CAAAI,YAAA,EAAS;;;;;IAhEpBJ,EAAA,CAAAgB,SAAA,GAA8C;IAA9ChB,EAAA,CAAAiB,UAAA,SAAAoC,MAAA,CAAAC,gBAAA,IAAAD,MAAA,CAAAE,WAAA,QAA8C,aAAAC,GAAA;IA+DRxD,EAAA,CAAAgB,SAAA,GAAwD;IAAxDhB,EAAA,CAAAyD,qBAAA,UAAAzD,EAAA,CAAA0B,WAAA,uCAAwD;;;;;;IAM/G1B,EAAA,CAAAE,cAAA,cAAwD;IAMhDF,EAAA,CAAAK,UAAA,mBAAAqD,4DAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAAoD,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAiD,OAAA,CAAAhD,IAAA,EAAM;IAAA,EAAC;;IAKjBZ,EAAA,CAAAI,YAAA,EAAS;;;IAHRJ,EAAA,CAAAgB,SAAA,GAAwD;IAAxDhB,EAAA,CAAAyD,qBAAA,UAAAzD,EAAA,CAAA0B,WAAA,uCAAwD;;;;;IAWhE1B,EAAA,CAAAC,uBAAA,GAAyC;IACvCD,EAAA,CAAAG,SAAA,oCAA2F;IAC7FH,EAAA,CAAAe,qBAAA,EAAe;;;;IADcf,EAAA,CAAAgB,SAAA,GAAmC;IAAnChB,EAAA,CAAAiB,UAAA,iBAAA4C,MAAA,CAAAC,kBAAA,CAAmC;;;ADzChE,OAAM,MAAOC,oBAAoB;EAkE/BC,QAAQA,CAACC,KAAW;IAClB,IAAIpE,iBAAiB,CAAC,IAAI,CAACqE,UAAU,CAAC,EAAE;MACtC,IAAI,CAACX,WAAW,GAAGY,MAAM,CAACC,UAAU;;EAExC;EACAC,YACSC,aAA4B,EACNJ,UAAe,EACrCK,KAAmB,EACnBC,cAA8B,EAC7BC,MAAc,EACfC,cAA8B,EAC7BC,WAAwB,EACxBC,kBAAsC,EACtCC,yBAAoD,EACpDC,YAA0B,EAC1BC,SAA2B,EAC3BC,cAA8B,EAC9BC,mBAAwC,EACxCC,aAA4B,EAC5BC,WAAwB,EACxBC,eAAgC,EAChCC,iBAAoC,EACpCC,UAAkC,EACnCtD,eAAgC,EAC/BuD,QAAmB,EACnBC,EAAsB,EACtBC,cAA8B;IArB/B,KAAAnB,aAAa,GAAbA,aAAa;IACS,KAAAJ,UAAU,GAAVA,UAAU;IAChC,KAAAK,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACX,KAAAtD,eAAe,GAAfA,eAAe;IACd,KAAAuD,QAAQ,GAARA,QAAQ;IACR,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IAtFxB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,aAAa,GAAG,IAAIC,KAAK,EAAO;IAChC,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,mBAAmB,GAAG,IAAIF,KAAK,EAAO;IAGtC,KAAAG,WAAW,GAAW,CAAC;IACvB,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,oBAAoB,GAAW,CAAC;IAEhC,KAAAC,aAAa,GAAY,KAAK;IAO9B,KAAAjE,YAAY,GAAQ,CAAC,CAAC;IACtB,KAAAkE,UAAU,GAAG,CAAC;IACd,KAAAC,WAAW,GAAG,IAAI1G,eAAe,EAAE;IAGnC,KAAA2G,OAAO,GAAQ,IAAIC,IAAI,EAAE,CAACC,YAAY,EAAE;IACxC,KAAAC,OAAO,GAAQ,IAAIF,IAAI,EAAE,CAACC,YAAY,EAAE;IACxC,KAAAE,0BAA0B,GAAG,IAAIb,KAAK,EAAO;IAC7C,KAAAc,aAAa,GAAY,KAAK;IAC9B,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAvD,gBAAgB,GAAY,KAAK;IACjC,KAAAwD,OAAO,GAAW,EAAE;IACpB,KAAAC,WAAW,GAAkB,IAAI;IACjC,KAAAC,eAAe,GAAWC,MAAM,CAACC,gBAAgB;IACjD,KAAAC,eAAe,GAAW,CAAC,CAAC;IAC5B,KAAAC,uBAAuB,GAAG,CAAC;IAC3B,KAAAC,MAAM,GAAW,CAAC;IAClB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAhF,UAAU,GAAW,EAAE;IACvB,KAAAiF,gBAAgB,GAAY,KAAK;IACxB,KAAAC,yBAAyB,GAAY,KAAK;IACnD,KAAArG,mBAAmB,GAAY,KAAK;IACpC,KAAAsG,gBAAgB,GAAY,KAAK;IACjC,KAAA5E,kBAAkB,GAAa,KAAK;IACpC,KAAA6E,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAA/F,YAAY,GAAW,EAAE;IACzB,KAAAO,YAAY,GAAW,CAAC;IAExB,KAAA2B,kBAAkB,GAAE,KAAK;IAKzB,KAAAP,WAAW,GAAKY,MAAM,CAACC,UAAU;IAGjC,KAAAlC,aAAa,GAAW,CAAC;IAiCvB,IAAI0F,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IACnD,IAAIF,KAAK,EACP,IAAI,CAACzF,YAAY,GAAG4F,QAAQ,CAACH,KAAK,CAAC;IACrC,IAAI,CAACtE,gBAAgB,GAAG,IAAI,CAAC+B,iBAAiB,CAAC2C,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACP,gBAAgB,GAAG,IAAI,CAACpC,iBAAiB,CAAC2C,aAAa,CAAC,iBAAiB,CAAC;IAC/E,IAAI,CAACnG,WAAW,GAAGgG,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,IAAI,CAACN,yBAAyB,GAAG,KAAK;EACxC;EAKMS,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACT,gBAAgB,GAAGS,KAAI,CAAC7C,iBAAiB,CAAC2C,aAAa,CAAC,iBAAiB,CAAC;MAC/EE,KAAI,CAACR,iBAAiB,GAAGQ,KAAI,CAAC7C,iBAAiB,CAAC2C,aAAa,CAAC,kBAAkB,CAAC;MACjFE,KAAI,CAACrF,kBAAkB,GAAGqF,KAAI,CAAC7C,iBAAiB,CAAC2C,aAAa,CAAC,cAAc,CAAC;MAC9EE,KAAI,CAACE,YAAY,EAAE;MACnBF,KAAI,CAACG,SAAS,EAAE;MAEhBH,KAAI,CAACI,mBAAmB,GAAGJ,KAAI,CAAClD,cAAc,CAACuD,gBAAgB,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;QACvF,IAAIA,GAAG,EAAE;UACPP,KAAI,CAACQ,gBAAgB,EAAE;;MAE3B,CAAC,CAAC;MACFR,KAAI,CAACS,kBAAkB,GAAGT,KAAI,CAAClD,cAAc,CAAC4D,eAAe,EAAE,CAACJ,SAAS,CAAEC,GAAQ,IAAI;QACrF,IAAIA,GAAG,EAAE;UACPP,KAAI,CAACW,WAAW,EAAE;;MAEtB,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAX,KAAI,CAAChC,aAAa,GAAG,KAAK;MAC1BgC,KAAI,CAACY,gBAAgB,EAAE;MACvBZ,KAAI,CAAC3D,KAAK,CAACwE,YAAY,CAAC,UAAU,CAAC,CAACP,SAAS,CAAC;QAC5CQ,IAAI,EAAGP,GAAQ,IAAI;UAEjB,IAAIQ,IAAI,GAAGR,GAAG,CAACS,IAAI,CAChBC,GAAQ,IACPA,GAAG,CAACC,GAAG,CAACC,iBAAiB,EAAE,KAAK,aAAa,CAACA,iBAAiB,EAAE,CACpE;UACD,IAAIJ,IAAI,EAAEf,KAAI,CAACoB,WAAW,GAAGL,IAAI,CAACK,WAAW;UAC7CL,IAAI,GAAGR,GAAG,CAACS,IAAI,CACZC,GAAQ,IACPA,GAAG,CAACC,GAAG,CAACC,iBAAiB,EAAE,KAAK,UAAU,CAACA,iBAAiB,EAAE,CACjE;UACD,IAAIJ,IAAI,EAAEf,KAAI,CAACqB,QAAQ,GAAGN,IAAI,CAACO,WAAW;UAC1CP,IAAI,GAAGR,GAAG,CAACS,IAAI,CACZC,GAAQ,IACPA,GAAG,CAACC,GAAG,CAACC,iBAAiB,EAAE,KAAK,aAAa,CAACA,iBAAiB,EAAE,CACpE;UACD,IAAIJ,IAAI,EAAEf,KAAI,CAAC9B,WAAW,CAACqD,WAAW,GAAGR,IAAI,CAACO,WAAW;UACzDP,IAAI,GAAGR,GAAG,CAACS,IAAI,CACZC,GAAQ,IACPA,GAAG,CAACC,GAAG,CAACC,iBAAiB,EAAE,KAAK,cAAc,CAACA,iBAAiB,EAAE,CACrE;UACD,IAAIJ,IAAI,EAAE;YACRf,KAAI,CAAC9B,WAAW,CAACsD,kBAAkB,CAACD,WAAW,GAAGR,IAAI,CAACO,WAAW;YAClEtB,KAAI,CAAC9B,WAAW,CAACuD,iBAAiB,CAACF,WAAW,GAAGR,IAAI,CAACO,WAAW;;QAErE,CAAC;QACDI,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;QACpB;OACD,CAAC;MACF3B,KAAI,CAAC6B,SAAS,GAAGlC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MAClDI,KAAI,CAAC8B,WAAW,GAAG9B,KAAI,CAAC3D,KAAK,CAAC/E,GAAG,CAAC,SAAS,CAAC;MAE5C0I,KAAI,CAAC+B,mBAAmB,GAAG/B,KAAI,CAACgC,cAAc,CAAC1B,SAAS,CAAC,MAAI;QAC3DN,KAAI,CAACiC,gBAAgB,EAAE;QACvBjC,KAAI,CAACQ,gBAAgB,EAAE;MACzB,CAAC,CAAC;IAAA;EACJ;EAEMN,YAAYA,CAAA;IAAA,IAAAgC,MAAA;IAAA,OAAAjC,iBAAA;MAChBiC,MAAI,CAAC7F,KAAK,CAACwE,YAAY,CAAC,WAAW,CAAC,CAACP,SAAS,CAAC;QAC7CQ,IAAI,EAAGP,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAE;YACP2B,MAAI,CAAC7I,YAAY,GAAGkH,GAAG;YACvB,IAAG2B,MAAI,CAAC7I,YAAY,CAACC,cAAc,CAACC,MAAM,GAAC,CAAC,EAAC;cAC3C2I,MAAI,CAACxI,YAAY,GAAGwI,MAAI,CAAC7I,YAAY,CAACC,cAAc,CAAC,CAAC,CAAC,CAACI,YAAY;;YAEtEwI,MAAI,CAACC,kBAAkB,EAAE;YACzBD,MAAI,CAACE,gBAAgB,EAAE;;QAE3B,CAAC;QACDV,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;QACpB;OACD,CAAC;IAAC;EAEL;EAEAQ,kBAAkBA,CAAA;IAChB,IAAI,CAACrF,cAAc,CAACuF,UAAU,EAAE,CAAC/B,SAAS,CAAC;MACzCQ,IAAI,EAAGP,GAAQ,IAAI;QAEjB,IAAIA,GAAG,CAACQ,IAAI,CAACuB,OAAO,CAAC/I,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACuD,cAAc,CAACyF,aAAa,GAAGhC,GAAG,CAACQ,IAAI,CAACuB,OAAO,CAAC,CAAC,CAAC;UACvD,IAAI,CAAC,IAAI,CAACxF,cAAc,CAACyF,aAAa,CAACC,MAAM,IAAI,IAAI,CAAC1F,cAAc,CAACyF,aAAa,CAACC,MAAM,KAAK,EAAE,IAAM,CAAC,IAAI,CAAC1F,cAAc,CAACyF,aAAa,CAACE,IAAI,IAAI,IAAI,CAAC3F,cAAc,CAACyF,aAAa,CAACE,IAAI,KAAK,EAAG,EAAE;YAC/L,IAAI,CAACjG,cAAc,CAACkG,GAAG,CAAC;cACtBC,QAAQ,EAAE,MAAM;cAChBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;cAC3DC,MAAM,EAAE,IAAI,CAACjG,SAAS,CAACgG,OAAO,CAC5B,qCAAqC;aAExC,CAAC;YAEF,IAAI,CAACtG,MAAM,CAACwG,QAAQ,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAACjG,cAAc,CAACyF,aAAa,CAACS,EAAE,CAAC,EAAE;cAAEC,WAAW,EAAE;gBAAEC,SAAS,EAAE;cAAW;YAAE,CAAE,CAAC;YAC/H;;UAEF,IAAI,CAAC,IAAI,CAACC,qBAAqB,IAAI,CAAC,IAAI,CAAChG,iBAAiB,CAAC2C,aAAa,CAAC,cAAc,CAAC,EAAE;YACxF,IAAI,CAACU,gBAAgB,EAAE;;SAG1B,MAAM;UACL,IAAI,CAAChE,cAAc,CAACkG,GAAG,CAAC;YACtBC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAE,IAAI,CAACjG,SAAS,CAACgG,OAAO,CAC5B,2CAA2C;WAE9C,CAAC;UAEF,IAAI,CAACtG,MAAM,CAACwG,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAAEE,WAAW,EAAE;cAAEC,SAAS,EAAE;YAAW;UAAE,CAAE,CAAC;UACvF;;QAGF,IAAI,CAACpG,cAAc,CAACsG,aAAa,GAAG,IAAI;MAC1C,CAAC;MACD1B,KAAK,EAAGC,GAAQ,IAAI;QAElB,IAAI,CAAC3E,aAAa,CAACqG,IAAI,EAAE;QACzB,IAAI,CAAC7G,cAAc,CAACkG,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEnB,GAAG,CAAC2B;SACb,CAAC;MACJ;KACD,CAAC;EACJ;EACAC,WAAWA,CAAA;IAEZ,IAAI,CAACJ,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,EAAEK,cAAc,GAAG,IAAI,CAACL,qBAAqB,EAAEK,cAAc,GAAG,IAAI,CAACL,qBAAqB;IAE9I,IAAI,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAAChG,iBAAiB,CAAC2C,aAAa,CAAC,cAAc,CAAC,EAAE;MACtF,IAAI,CAACU,gBAAgB,EAAE;;EAE3B;EACAA,gBAAgBA,CAAA;IAEd,IAAG,IAAI,CAAC2C,qBAAqB,EAAEH,EAAE,EAAE;MACnC,IAAIjC,IAAI,GAAQ;QACdnC,OAAO,EAAE,IAAI,CAACvF,YAAY,EAAEoK,OAAO;QACnCC,SAAS,EAAE,IAAI,CAAC5G,cAAc,CAACyF,aAAa,CAACS;OAE9C;MAEDjC,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACoC,qBAAqB,EAAEH,EAAE,IAAIjC,IAAI,CAAC,kBAAkB,CAAC;MAErF,IAAI,CAACnE,YAAY,CAAC+G,kCAAkC,CAAC5C,IAAI,CAAC,CAACT,SAAS,CAAC;QACnEQ,IAAI,EAAGP,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAEqD,OAAO,EAAE;YAChB,IAAI,CAAC3K,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACqG,yBAAyB,GAAG,IAAI;YAErC,IAAI,CAACuE,sBAAsB,GAAGtD,GAAG,CAACQ,IAAI;YACtC,IAAI,CAACK,WAAW,GAAGb,GAAG,CAACQ,IAAI,CAACK,WAAW;YACvC,IAAI,CAAC1C,UAAU,GAAG6B,GAAG,CAACQ,IAAI,CAACrC,UAAU;YACrC,IAAI,CAACC,UAAU,GAAG4B,GAAG,CAACQ,IAAI,CAACpC,UAAU;YACrC,IAAI,CAACmF,eAAe,GAAGvD,GAAG,CAACQ,IAAI,CAACgD,YAAY;YAC5C,IAAI,CAACtG,aAAa,GAAG8C,GAAG,CAACQ,IAAI,CAACtD,aAAa;YAC3C,IAAI,CAACE,iBAAiB,GAAG4C,GAAG,CAACQ,IAAI,CAACpD,iBAAiB;YACnD,IAAI,CAACC,mBAAmB,GAAG2C,GAAG,CAACQ,IAAI,CAACiD,mBAAmB;YACvD,IAAI,CAAC/F,UAAU,GAAGsC,GAAG,CAACQ,IAAI,CAACkD,UAAU;YACrC,IAAI,CAACrF,OAAO,GAAG,IAAI,CAACvF,YAAY,CAACoK,OAAO;YACxC,IAAI,CAAC5E,WAAW,GAAG,IAAI,CAACxF,YAAY,CAAC6K,MAAM;YAC3C,IAAI,CAAChF,uBAAuB,GAAGqB,GAAG,CAACQ,IAAI,CAACoD,uBAAuB;YAC/D,IAAI,CAACrK,eAAe,CAACC,YAAY,GAAGwG,GAAG,CAACQ,IAAI,CAACqD,iBAAiB;YAC9D,IAAI,CAACtK,eAAe,CAACuK,iBAAiB,GAAG9D,GAAG,CAACQ,IAAI,CAACuD,uBAAuB,GAAG/D,GAAG,CAACQ,IAAI,CAACuD,uBAAuB,CAACC,mBAAmB,GAAG,CAAC;YACpI,IAAI,CAAC1G,WAAW,GAAG,CAAC;YACpB,IAAI,CAACC,aAAa,GAAGyC,GAAG,CAACQ,IAAI,CAACjD,aAAa;YAC3C,IAAI,CAACC,oBAAoB,GAAGwC,GAAG,CAACQ,IAAI,EAAEhD,oBAAoB;YAC1D,IAAIwC,GAAG,CAACQ,IAAI,EAAEgD,YAAY,CAACxK,MAAM,EAAE;cACjCgH,GAAG,CAACQ,IAAI,EAAEgD,YAAY,CAACS,OAAO,CAAEC,IAAS,IAAI;gBAC3C,IAAIA,IAAI,CAAC5G,WAAW,EAAE;kBACpB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG4G,IAAI,CAAC5G,WAAW;;cAE1D,CAAC,CAAC;;YAEJ,IAAI,CAACoB,eAAe,GAAGsB,GAAG,CAACQ,IAAI,CAAC9B,eAAe;YAC/C,IAAI,CAACH,eAAe,GAAGyB,GAAG,CAACQ,IAAI,CAACjC,eAAe;YAG/C,IAAI,CAAC1E,UAAU,GAAGmG,GAAG,CAACQ,IAAI,CAAC3G,UAAU;YACrC,IAAI,CAACN,eAAe,CAAC4K,eAAe,GAAG;cACrCN,iBAAiB,EAAErF,MAAM,CAAC,IAAI,CAACjF,eAAe,CAACC,YAAY,CAAC;cAC5D4K,eAAe,EAAE,IAAI,CAACA,eAAe;cACrCC,OAAO,EAAE,IAAI,CAAC3F,eAAe;cAC7B4F,OAAO,EAAE,IAAI,CAAC/F;aACf;YACD,IAAI,CAACd,aAAa,GAAG,IAAI;YACzB,IAAI,CAAC3B,KAAK,CAACyI,GAAG,CAAC,cAAc,EAAE;cAC7BV,iBAAiB,EAAE7D,GAAG,CAACQ,IAAI,CAACqD,iBAAiB;cAC7CO,eAAe,EAAE,IAAI,CAACA,eAAe;cACrCnB,cAAc,EAAE,IAAI,CAACL;aAEtB,CAAC;YAEF,IAAI,CAACxC,WAAW,EAAE;YAClB,IAAI,CAACoE,YAAY,EAAE;WACpB,MAAM;YACL,IAAI,CAAC9L,mBAAmB,GAAG,KAAK;YAChC,IAAIsH,GAAG,EAAE+C,OAAO,IAAI,IAAI,EAAE;cACxB,IAAG/C,GAAG,CAAC+C,OAAO,KAAK,gEAAgE,IAC9E,CAAC,IAAI,CAACxG,cAAc,CAACyF,aAAa,CAACC,MAAM,IAAI,IAAI,CAAC1F,cAAc,CAACyF,aAAa,CAACC,MAAM,KAAK,EAAE,IAC5F,CAAC,IAAI,CAAC1F,cAAc,CAACyF,aAAa,CAACE,IAAI,IAAI,IAAI,CAAC3F,cAAc,CAACyF,aAAa,CAACE,IAAI,KAAK,EAAK,EAAE;gBAChG,IAAI,CAACjG,cAAc,CAACkG,GAAG,CAAC;kBACtBC,QAAQ,EAAE,MAAM;kBAChBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;kBAC3DC,MAAM,EAAE,IAAI,CAACjG,SAAS,CAACgG,OAAO,CAC5B,qCAAqC;iBAExC,CAAC;gBAEF,IAAI,CAACtG,MAAM,CAACwG,QAAQ,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAACjG,cAAc,CAACyF,aAAa,CAACS,EAAE,CAAC,EAAE;kBAAEC,WAAW,EAAE;oBAAEC,SAAS,EAAE;kBAAW;gBAAE,CAAE,CAAC;gBAC/H;eACD,MAAM;gBACL,IAAI,CAAC1G,cAAc,CAACkG,GAAG,CAAC;kBACtBC,QAAQ,EAAE,OAAO;kBACjBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;kBAC3DC,MAAM,EAAEvC,GAAG,CAAC+C;iBACb,CAAC;;;;QAIV,CAAC;QACD5B,KAAK,EAAGC,GAAQ,IAAI;UAClB,IAAI,CAAC1I,mBAAmB,GAAG,KAAK;UAChC,IAAI,CAACuD,cAAc,CAACkG,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAE,IAAI,CAACjG,SAAS,CAACgG,OAAO,CAC5B,uCAAuC;WAE1C,CAAC;UACF,IAAI,CAACtG,MAAM,CAACwG,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QACjC;OACD,CAAC;;EAEJ;EAED;;;;;;EAMArK,IAAIA,CAAA;IAEH,IAAI,IAAI,CAAC8G,iBAAiB,EAAE;MACxB;MACA,IAAI,CAACpC,UAAU,CAACrB,KAAK,CACjBnE,iBAAiB,CAACoN,2BAA2B;MAAE;MAC/C,UAAU;MAAoC;MAC9C,oBAAoB;MAAyB;MAC7C,CAAC;MAA4C;MAC7C,IAAI;MAAyC;MAC7C;QACI,cAAc,EAAE,IAAI,CAAClL,eAAe,CAACmL,iBAAiB,GAAG,IAAI,CAACnL,eAAe,CAACC,YAAY;QAC1F,UAAU,EAAE,IAAI,CAAC6E,OAAO;QACxB,SAAS,EAAE,IAAI,CAACkD,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoD,YAAY,GAAG,kBAAkB;QAChF,YAAY,EAAE,IAAI,CAACrD,SAAS;QAC5B,YAAY,EAAE,IAAI,CAACxF,KAAK,CAAC/E,GAAG,CAAC,QAAQ,CAAC;QACtC,aAAa,EAAE,IAAI,CAAC+E,KAAK,CAAC/E,GAAG,CAAC,YAAY,CAAC,EAAE6N,UAAU;QACvD,WAAW,EAAE,IAAI,CAAC9I,KAAK,CAAC/E,GAAG,CAAC,YAAY,CAAC,EAAE8N,QAAQ;QACnD,kBAAkB,EAAE,IAAI,CAAClG,uBAAuB;QAChD,aAAa,EAAE,IAAI,CAACR,UAAU;QAC9B,cAAc,EAAE,IAAI,CAACb,WAAW;QAChC,kBAAkB,EAAE,IAAI,CAACxE,YAAY,EAAEC,cAAc,EAAEC,MAAM;QAC7D,gBAAgB,EAAE,IAAI,CAAC8L,oBAAoB;QAC3C,qBAAqB,EAAE,IAAI,CAACzH,mBAAmB;QAC/C,iBAAiB,EAAE,IAAI,CAACuF,qBAAqB,CAACmC;OACjD,CACJ;;IAGL7N,QAAQ,CAAC,CACL,IAAI,CAACwF,WAAW,CAACsI,eAAe,EAAE,EAClC,IAAI,CAACtI,WAAW,CAACuI,mBAAmB,CAAC,IAAI,CAACnM,YAAY,CAACoK,OAAO,CAAC,EAC/D,IAAI,CAAC7G,YAAY,CAAC6I,2CAA2C,CACzD;MAAC7G,OAAO,EAAC,IAAI,CAACvF,YAAY,CAACoK;IAAO,CAAC,CACtC,CACJ,CAAC,CAACnD,SAAS,CAAC;MACTQ,IAAI,EAAEA,CAAC,CAAC4E,cAAc,EAAEC,mBAAmB,EAAEC,eAAe,CAAkB,KAAI;QAC9E,IAAIC,oBAAoB,GAAGF,mBAAmB,CAAC5E,IAAI,CAAC+E,yBAAyB;QAC7E,IAAIC,YAAY,GAAGH,eAAe,EAAEhC,OAAO;QAE3C,IAAG,CAACiC,oBAAoB,EAAE;UACxB,IAAI,CAACtJ,MAAM,CAACyJ,aAAa,CAAC,OAAO,CAAC;;QAGpC,IAAI,CAACD,YAAY,EAAG;UAChB,IAAI,CAACvJ,cAAc,CAACkG,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAAC/F,SAAS,CAACgG,OAAO,CAAC,iCAAiC;WAClE,CAAC;UACF,IAAI,CAACtG,MAAM,CAACyJ,aAAa,CAAC,OAAO,CAAC;SACrC,MAEI,IAAIN,cAAc,CAAC9B,OAAO,IAAIiC,oBAAoB,IAAIE,YAAY,EAAE;UACrE,IAAI,CAACnK,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAACoC,aAAa,GAAG,KAAK;UAE1B,IAAI,IAAI,CAAC/E,mBAAmB,EAAE;YAC1BgN,QAAQ,CAACC,QAAQ,CAACC,YAAY,EAAE;;;MAG5C,CAAC;MACDzE,KAAK,EAAGC,GAAG,IAAI;QACXC,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEC,GAAG,CAAC;MAC5C,CAAC;MACDyE,QAAQ,EAAEA,CAAA,KAAK;QACXxE,OAAO,CAACyE,GAAG,CAAC,0BAA0B,CAAC;MAC3C;KACH,CAAC;EACJ;EAGEC,eAAeA,CAAA;IACb;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAI;MACjD,KAAK,IAAIC,QAAQ,IAAID,SAAS,EAAE;QAC9B,IAAIC,QAAQ,CAACC,IAAI,KAAK,WAAW,EAAE;UACjCjJ,KAAK,CAACkJ,IAAI,CAACF,QAAQ,CAACG,UAAU,CAAC,CAACrC,OAAO,CAAEsC,IAAU,IAAI;YACrD,IAAIA,IAAI,CAACC,QAAQ,KAAK,QAAQ,EAAE;cAC9B,MAAMC,MAAM,GAAGF,IAAyB;cACxC,IAAI,CAACG,qBAAqB,CAACD,MAAM,CAAC;;UAEtC,CAAC,CAAC;;;IAGR,CAAC,CAAC;IAEF;IACA,IAAI,CAACT,QAAQ,CAACW,OAAO,CAACC,QAAQ,CAACC,IAAI,EAAE;MAAEC,SAAS,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAI,CAAE,CAAC;EAC1E;EAEQL,qBAAqBA,CAACD,MAAyB;IACrD,IAAI,CAACpL,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC2L,kBAAkB,GAAG,IAAI,CAAClK,QAAQ,CAACmK,MAAM,CAACR,MAAM,EAAE,MAAM,EAAE,MAAK;MACnE,IAAI,CAACpL,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAAC0B,EAAE,CAACmK,aAAa,EAAE;MACtB,IAAI,CAACC,oBAAoB,CAACV,MAAM,CAAC;IACnC,CAAC,CAAC;EACJ;EAEQU,oBAAoBA,CAACV,MAAyB;IACpD,IAAI;MACF,MAAMW,cAAc,GAAGX,MAAM,CAACY,eAAe,IAAIZ,MAAM,EAAEa,aAAa,EAAEV,QAAQ;MAChF,MAAMW,UAAU,GAAGH,cAAc,EAAEP,IAAI;MAEvC;MACA,MAAMW,OAAO,GAAGJ,cAAc,EAAEK,aAAa,CAAC,YAAY,CAAC;MAC3D,IAAID,OAAO,EAAE;QACXnG,OAAO,CAACyE,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;OAC/C,MAAM;QACLnG,OAAO,CAACyE,GAAG,CAAC,iCAAiC,CAAC;;KAEjD,CAAC,OAAO3E,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;;EAE3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAqD,YAAYA,CAAA;IACV,IAAIkD,IAAI,GAAG,IAAI;IACf,IAAIC,UAAU,GAAGvI,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE7CtI,GAAG,CAAC,IAAI,CAAC8J,WAAW,EAAE,MAAK;MACzB,IAAI7J,WAAW,CAAC4Q,YAAY,EAAE;QAChC,IAAI,CAACC,OAAO,CAACH,IAAI,EAACC,UAAU,CAAC;OAC1B,MAAM;QACLjC,QAAQ,CAACC,QAAQ,CAACmC,SAAS,GAAG;UAC5BC,oBAAoB,EAAE,IAAI,CAACjD,oBAAoB,EAAElE,iBAAiB,EAAE,EAAEoH,QAAQ,CAAC,MAAM,CAAC,GAAG1Q,iBAAiB,CAAC,MAAM,CAAC,GAAGA,iBAAiB,CAAC,QAAQ,CAAC;UAChJ2Q,0BAA0B,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjL,aAAa,CAAC;UAC9DyB,uBAAuB,EAAE,IAAI,CAACA,uBAAuB;UACrDyJ,GAAG,EAAE,IAAI,CAACjK,UAAU;UACpBkK,GAAG,EAAE,IAAI,CAACjK,UAAU;UACpBkK,IAAI,EAAEX,UAAU;UAChBY,iBAAiB,EAAE,IAAI,CAAClK,OAAO,IAAI,EAAE;UACrCX,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3B8K,sBAAsB,EAAE;YACtBC,cAAc,EAAE,IAAI,CAACC;WACtB;UACDC,mBAAmB,EAAE,IAAI,CAACvL,iBAAiB;UAC3CwL,gBAAgB,EAAE,CAAC,IAAI,CAACrL,aAAa;UACrCsL,yBAAyB,EAAE,IAAI,CAACtL,aAAa,GAAG,IAAI,CAACC,oBAAoB,GAAGlG,iBAAiB,CAAC,MAAM,CAAC;UAIrGwR,gBAAgB,EAAE,SAAAA,CAAUtI,IAAS;YAGnCkH,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;YACzB,IAAIiG,eAAe,GAAG;cACpBC,aAAa,EAAExI,IAAI,CAACyI,eAAe;cACnCC,UAAU,EAAE1I,IAAI,CAAC2I,YAAY;cAC7BC,aAAa,EAAE5I,IAAI,CAAC6I,WAAW,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC;cACjDC,yBAAyB,EAAE,IAAI;cAC/BjL,OAAO,EAAE,IAAI,CAACA,OAAO;cACrBC,WAAW,EAAE,IAAI,CAACA;aACnB;YAEDoJ,IAAI,CAAC6B,cAAc,CAACR,eAAe,CAAC;YACpCrB,IAAI,CAAC8B,YAAY,EAAE;YACnB9D,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;YACjC,IAAIjJ,IAAI,EAAE;cACRkH,IAAI,CAACrL,YAAY,CAACqN,iBAAiB,CAAChC,IAAI,CAAC5O,YAAY,CAACoK,OAAO,CAAC,CAACyG,IAAI,CACjExS,IAAI,CAAC,CAAC,CAAC,CACR,CAAC4I,SAAS,CAACC,GAAG,IAAE;gBACf0H,IAAI,CAACjK,aAAa,GAAG,IAAI;cAC3B,CAAC,CAAE;;UAGP,CAAC;UAEDmM,aAAa,EAAE,SAAAA,CAAA;YACblC,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;YACzB4E,IAAI,CAACjK,aAAa,GAAG,IAAI;YACzBiI,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;UACnC,CAAC;UACDI,cAAc,EAAE,SAAAA,CAAA;YACdnC,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;YACzB4E,IAAI,CAACjK,aAAa,GAAG,IAAI;YAEzBiI,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;UACnC;SACD;;IAGL,CAAC,CAAC;EACJ;EAEAF,cAAcA,CAACR,eAAoB;IACjC,IAAI,CAACjN,KAAK,CAACyI,GAAG,CAAC,iBAAiB,EAAEwE,eAAe,CAAC;IAClD,IAAI,CAAC/L,cAAc,CAAC8M,aAAa,CAAC,IAAI,CAACC,SAAS,EAAChB,eAAe,CAACC,aAAa,EAAC,IAAI,CAAC1L,WAAW,EAAC,IAAI,CAACnE,YAAY,CAAC;IAClH,IAAI,CAAC6C,MAAM,CAACwG,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEAwH,oBAAoBA,CAClBC,aAAkB,EAClBtG,MAAW,EACXT,OAAY,EACZgH,SAAc,EACdC,IAAS;IAET,IAAI,CAACjO,WAAW,CAACkO,WAAW,CAACzG,MAAM,CAAC,CAAC5D,SAAS,CAAC;MAC7CQ,IAAI,EAAGP,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACQ,IAAI,KAAK,IAAI,EAAE;UACrB;;QAGF,IAAI6J,QAAQ,GAAG,CAACrK,GAAG,CAACQ,IAAI,CAAC8J,GAAG,EAAEtK,GAAG,CAACQ,IAAI,CAAC+J,GAAG,CAAC;QAC3C,IAAI,CAAC5M,WAAW,CAACuD,iBAAiB,CAACsJ,aAAa,GAC9C,GAAG,GAAGH,QAAQ,CAACI,QAAQ,EAAE,GAAG,GAAG;QACjC,IAAI,CAAC9M,WAAW,CAACsD,kBAAkB,CAACuJ,aAAa,GAC/C,GAAG,GACH,CACE,IAAI,CAACjO,cAAc,CAACyF,aAAa,CAACsI,GAAG,EACrC,IAAI,CAAC/N,cAAc,CAACyF,aAAa,CAACuI,GAAG,CACtC,CAACE,QAAQ,EAAE,GACZ,GAAG;MAEP,CAAC;MACDtJ,KAAK,EAAGC,GAAQ,IAAI,CACpB;KACD,CAAC;EACJ;EAEAhB,WAAWA,CAAA;IACT,IAAIsK,OAAO,GAAQ,IAAI;IACvB,IAAIzH,cAAc,GAAQ,IAAI;IAC9B,IAAI,IAAI,CAACL,qBAAqB,EAAE;MAC9B,IAAI,IAAI,CAACA,qBAAqB,CAAC8H,OAAO,EAAE;QACtCA,OAAO,GAAG,IAAI,CAAC9H,qBAAqB,CAAC8H,OAAO;;MAE9C,IAAI,IAAI,CAAC9H,qBAAqB,CAAC8H,OAAO,EAAE;QACtCzH,cAAc,GAAG,IAAI,CAACL,qBAAqB,CAACH,EAAE;;;IAGlD,IAAI,CAACpG,YAAY,CACdsO,WAAW,CAAC;MACXlI,EAAE,EAAE,IAAI,CAAC3J,YAAY,CAACoK,OAAO;MAC7B0H,mBAAmB,EAAE,IAAI,CAACC,aAAa,EAAE5B,eAAe;MACxDpF,iBAAiB,EAAE,IAAI,CAACtK,eAAe,CAACC,YAAY;MACpD8D,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BoN,OAAO,EAAEA,OAAO;MAChBzH,cAAc,EAAEA,cAAc;MAC9B6H,KAAK,EACH,IAAI,CAAChS,YAAY,CAACQ,WAAW,GAC7BkF,MAAM,CAAC,IAAI,CAACjF,eAAe,CAACC,YAAY,CAAC,GAAG,IAAI,CAACC,aAAa;MAChEsR,SAAS,EAAE,IAAI,CAACxO,cAAc,CAACyF,aAAa,CAACS,EAAE;MAC/Cc,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCyH,aAAa,EAAE,IAAI,CAACzO,cAAc,CAACyF,aAAa,CAACiJ,aAAa;MAC9DC,KAAK,EAAE,IAAI,CAAC3O,cAAc,CAACyF,aAAa,CAACmJ,KAAK;MAC9CC,KAAK,EAAE,IAAI,CAAC7O,cAAc,CAACyF,aAAa,CAACqJ,KAAK;MAC9CC,aAAa,EAAE,IAAI,CAAC/O,cAAc,CAACyF,aAAa,CAACuJ,aAAa;MAC9DC,WAAW,EAAE,IAAI,CAACjP,cAAc,CAACyF,aAAa,CAACyJ,WAAW;MAC1DC,IAAI,EAAE,IAAI,CAACnP,cAAc,CAACyF,aAAa,CAACE,IAAI;MAC5CyJ,QAAQ,EAAE,IAAI,CAACpP,cAAc,CAACyF,aAAa,CAAC4J;KAC7C,CAAC,CACD7L,SAAS,CAAC;MACTQ,IAAI,EAAGP,GAAQ,IAAI,CAGnB,CAAC;MACDmB,KAAK,EAAGC,GAAQ,IAAI,CACpB;KACD,CAAC;EACN;EAEAoI,YAAYA,CAAA;IACV,IAAI5K,MAAM,GAAQQ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAChD,IAAI,CAACT,MAAM,IAAIA,MAAM,IAAI,EAAE,EAAE;MAC3BA,MAAM,GAAG,IAAI,CAACA,MAAM;;IAEtB,IAAI,IAAI,CAACK,iBAAiB,IAAK,IAAI,CAACrC,iBAAiB,CAACiP,aAAa,CAAC,aAAa,CAAC,EAAE;MAClF,IAAI,CAAChP,UAAU,CAACrB,KAAK,CAAC,aAAa,EAAE,MAAM,EAAEoD,MAAM,CAAC;;IAEtD,IAAI,CAACjC,eAAe,CAACmP,iBAAiB,CAAC,CAAC,CAAC;IACzC,IAAI,CAACnP,eAAe,CAACoP,gBAAgB,CAAC,EAAE,CAAC;IACzC,IAAI,CAACvP,mBAAmB,CAACwP,SAAS,CAACpN,MAAM,CAAC;EAC5C;EAEAyB,gBAAgBA,CAAA;IACd,IAAI,CAACvE,KAAK,CAACwE,YAAY,CAAC,cAAc,CAAC,CAACP,SAAS,CAAC;MAChDQ,IAAI,EAAGP,GAAQ,IAAI;QAEjB,IAAI,CAACpB,MAAM,GAAGoB,GAAG,CAAC,CAAC,CAAC,EAAEpB,MAAM;MAC9B,CAAC;MACDuC,KAAK,EAAGC,GAAQ,IAAI;QAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEQyG,OAAOA,CAACH,IAAQ,EAACC,UAAc;IACrCjC,QAAQ,CAACC,QAAQ,CAACmC,SAAS,GAAG;MAE5BM,GAAG,EAAE,IAAI,CAACjK,UAAU;MACpBkK,GAAG,EAAE,IAAI,CAACjK,UAAU;MACpBkK,IAAI,EAAEX,UAAU;MAChBY,iBAAiB,EAAE,IAAI,CAAClK,OAAO,IAAI,EAAE;MACrCX,UAAU,EAAE,GAAG;MACf8K,sBAAsB,EAAE;QACtBC,cAAc,EAAE,IAAI,CAACC;OACtB;MAGDI,gBAAgB,EAAE,SAAAA,CAAUtI,IAAS;QAGnCkH,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;QACzB,IAAIiG,eAAe,GAAG;UACpBC,aAAa,EAAExI,IAAI,CAACyI,eAAe;UACnCC,UAAU,EAAE1I,IAAI,CAAC2I,YAAY;UAC7BC,aAAa,EAAE5I,IAAI,CAAC6I,WAAW,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC;UACjDC,yBAAyB,EAAE,IAAI;UAC/BjL,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBC,WAAW,EAAE,IAAI,CAACA;SACnB;QAEDoJ,IAAI,CAAC6B,cAAc,CAACR,eAAe,CAAC;QACpCrB,IAAI,CAAC8B,YAAY,EAAE;QACnB9D,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;QACjC,IAAIjJ,IAAI,EAAE;UACRkH,IAAI,CAACrL,YAAY,CACdqN,iBAAiB,CAAChC,IAAI,CAAC5O,YAAY,CAACoK,OAAO,CAAC,CAC5CnD,SAAS,CAAEC,GAAO,IAAI;YACrB0H,IAAI,CAACjK,aAAa,GAAG,IAAI;UAC3B,CAAC,CAAC;;MAER,CAAC;MACDmM,aAAa,EAAE,SAAAA,CAAA;QAGblC,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;QAEzB4E,IAAI,CAACjK,aAAa,GAAG,IAAI;QACzBiI,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;MACnC,CAAC;MACDI,cAAc,EAAE,SAAAA,CAAA;QACdnC,IAAI,CAACjL,aAAa,CAACqG,IAAI,EAAE;QACzB4E,IAAI,CAACjK,aAAa,GAAG,IAAI;QAEzBiI,QAAQ,CAACC,QAAQ,CAAC8D,aAAa,EAAE;MACnC;KACD;EACH;EAEA5H,gBAAgBA,CAAA;IACd,IAAI,CAACnF,WAAW,CAACsI,eAAe,EAAE,CAACjF,SAAS,CAAC;MAC3CQ,IAAI,EAAGP,GAAQ,IAAI;QACjB,IAAGA,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACQ,IAAI,CAACuB,OAAO,IAAI/B,GAAG,CAACQ,IAAI,CAACuB,OAAO,CAAC/I,MAAM,EAAE;UAC1D;UACA;UACA;UACA;UACA,IAAI,CAAC0P,eAAe,GAAG1I,GAAG,CAACQ,IAAI,CAACuB,OAAO,CAACkK,GAAG,CAAEvL,GAAQ,IAAKA,GAAG,CAACwL,WAAW,CAAC;UAC1E,IAAI,CAACvO,WAAW,CAACsD,kBAAkB,CAACiL,WAAW,GAAGlM,GAAG,CAACQ,IAAI,CAACuB,OAAO,CAACkK,GAAG,CAAEvL,GAAQ,IAAKA,GAAG,CAACwL,WAAW,CAAC;SACtG,MAAM;UACL,IAAI,CAACxD,eAAe,GAAGtJ,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;;MAEnE;KACD,CAAC;EACJ;EAEAqC,gBAAgBA,CAAA;IACd,IAAI,CAACrF,YAAY,CAAC8P,gBAAgB,CAAC,IAAI,CAACrT,YAAY,CAACoK,OAAO,CAAC,CAACnD,SAAS,CAAC;MACtEQ,IAAI,EAAGP,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACqD,OAAO,EAAE;UACf,IAAI,CAAC5J,aAAa,GAAGuG,GAAG,CAACQ,IAAI,IAAI,IAAI,CAAC1H,YAAY,CAACQ,WAAW,GAAG,IAAI,CAACR,YAAY,CAACQ,WAAW,GAAG0G,GAAG,CAACQ,IAAI;;MAE7G,CAAC;MACDW,KAAK,EAAGC,GAAQ,IAAI;QAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAgL,WAAWA,CAAA;IACT,IAAI,CAACvM,mBAAmB,CAACwM,WAAW,EAAE;IACtC,IAAI,CAACnM,kBAAkB,CAACmM,WAAW,EAAE;IACrC,IAAI,IAAI,CAACrG,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACsG,UAAU,EAAE;;IAE5B,IAAI,IAAI,CAACtF,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,EAAE,CAAC,CAAC;;IAE7B;IAEA,IAAI,IAAI,CAACxF,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAAC6K,WAAW,EAAE;;EAE1C;EACAzM,SAASA,CAAA,GAET;EAAC,QAAA2M,CAAA,G;qBAxwBUjR,oBAAoB,EAAA/D,EAAA,CAAAiV,iBAAA,CAAAC,EAAA,CAAA3V,aAAA,GAAAS,EAAA,CAAAiV,iBAAA,CAyErB3V,WAAW,GAAAU,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAC,YAAA,GAAApV,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAE,cAAA,GAAArV,EAAA,CAAAiV,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAvV,EAAA,CAAAiV,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzV,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAO,WAAA,GAAA1V,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAQ,kBAAA,GAAA3V,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAS,yBAAA,GAAA5V,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAU,YAAA,GAAA7V,EAAA,CAAAiV,iBAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAA/V,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAa,cAAA,GAAAhW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAc,mBAAA,GAAAjW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAe,aAAA,GAAAlW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAgB,WAAA,GAAAnW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAiB,eAAA,GAAApW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAkB,iBAAA,GAAArW,EAAA,CAAAiV,iBAAA,CAAAqB,EAAA,CAAAC,sBAAA,GAAAvW,EAAA,CAAAiV,iBAAA,CAAAE,EAAA,CAAAqB,eAAA,GAAAxW,EAAA,CAAAiV,iBAAA,CAAAjV,EAAA,CAAAyW,SAAA,GAAAzW,EAAA,CAAAiV,iBAAA,CAAAjV,EAAA,CAAA0W,iBAAA,GAAA1W,EAAA,CAAAiV,iBAAA,CAAA0B,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAzEV9S,oBAAoB;IAAA+S,SAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAAlT,QAAA,CAAAmT,MAAA,CAAgB;QAAA,UAAAnX,EAAA,CAAAoX,eAAA;;;;;;;;;;qCAFhB,CAAC7X,aAAa,CAAC,GAAAS,EAAA,CAAAqX,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChD5BjX,EAAA,CAAAuC,UAAA,IAAAoV,mCAAA,iBAsEM;QACN3X,EAAA,CAAAuC,UAAA,IAAAqV,mCAAA,iBAiBM;QAEN5X,EAAA,CAAAuC,UAAA,IAAAsV,4CAAA,0BAEe;;;QA5FT7X,EAAA,CAAAiB,UAAA,SAAAiW,GAAA,CAAAzP,gBAAA,CAAsB;QAuEtBzH,EAAA,CAAAgB,SAAA,GAAuB;QAAvBhB,EAAA,CAAAiB,UAAA,UAAAiW,GAAA,CAAAzP,gBAAA,CAAuB;QAmBdzH,EAAA,CAAAgB,SAAA,GAAwB;QAAxBhB,EAAA,CAAAiB,UAAA,SAAAiW,GAAA,CAAApT,kBAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}