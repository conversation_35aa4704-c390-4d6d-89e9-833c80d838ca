<section class="register">
  <ng-container *ngIf="screenWidth <768; else desktopView">
    <div class="content-container " [ngStyle]="{marginTop: (screenWidth <= 768 ? (isMobileLayout ? '5rem' : '220px') : '') }">
      <div class="grid justify-content-center">
        <p class="content-container__header-title m-0 text-center mb-2">
          {{title | translate}}
        </p>
        <div class="col-12 text-center  mb-3 content-container__header-sub-title" style="color: #a3a3a3">
          {{ "register.authenticateYourself" | translate }}
        </div>
        <div class="col-12 col-md-8 col-lg-6 ">
          <div class="p-fluid p-grid">

            <div class="p-field p-col-12">
              <form [formGroup]="verifyForm" autocomplete="new-password">
                <div [ngStyle]="{
                    border:
                      verifyForm.controls.phoneNumber.value?.e164Number
                        ?.length > 0 && !verifyForm.controls.phoneNumber.valid
                        ? '1px solid red'
                        : '0px solid transparent'
                  }" class="p-float-label w-full">
                  <ngx-intl-tel-input [cssClass]="'custom contact-input-phone'" [enableAutoCountrySelect]="true"
                                      [enablePlaceholder]="true" [maxLength]="phoneInputLength"
                                      [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
                                      [preferredCountries]="preferredCountries" [searchCountryField]="[
                      SearchCountryField.Iso2,
                      SearchCountryField.Name
                    ]" [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
                                      [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" autocomplete="new-phoneNumber"
                                      formControlName="phoneNumber" name="phoneNumber">
                  </ngx-intl-tel-input>
                  <label class="mobile-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}*</label>
                </div>
                <div class="mt-4" *ngIf="!isPrimary">
                  <ng-toggle (change)="onToggleChange($event)" [color]="{
                        unchecked: '#00000087',
                        checked: 'rgba(25, 118, 210, 0.20)'
  }" [height]="14" [ngClass]="{'toggle-checked':toggleValue,'toggle-unchecked':!toggleValue}" [value]="false"
                             [width]="43"></ng-toggle>
                  <span class="ml-4 set-default">Set as default</span>
                </div>


              </form>
            </div>

            <button (click)="checkMobileExist()" [disabled]="!verifyForm.controls.phoneNumber.valid"
                    class="w-full second-btn content-container__primary-btn mt-4 gap-2" pButton type="button">
              {{"newUser.verifyUser.next" | translate}}
            </button>

            <button (click)="onBack()"
                    class="w-full second-btn content-container__secondary-btn mt-3 gap-2" pButton type="button">
              {{"newUser.verifyUser.cancel" | translate}}
            </button>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-template #desktopView>
    <div class="content-container my-3">
      <div class="grid justify-content-center margin-x-100">
        <p class="add-new m-0 text-center mb-2">
          {{title | translate}}
        </p>
        <div class="col-12 text-center no-underline font-size-14 mb-3 otp-desc" style="color: #a3a3a3">
          {{ "register.authenticateYourself" | translate }}
        </div>
        <div class="col-12 col-md-8 col-lg-6 border-round bg-white shadow-1 px-5 pt-6">
          <div class="p-fluid p-grid">

            <div class="p-field p-col-12">
              <form [formGroup]="verifyForm" autocomplete="new-password">
                <div [ngStyle]="{
                    border:
                      verifyForm.controls.phoneNumber.value?.e164Number
                        ?.length > 0 && !verifyForm.controls.phoneNumber.valid
                        ? '1px solid red'
                        : '0px solid transparent'
                  }" class="p-float-label w-full">
                  <ngx-intl-tel-input  #phoneInput3 [cssClass]="'custom contact-input-phone'" [enableAutoCountrySelect]="true"
                    [enablePlaceholder]="true" [maxLength]="phoneInputLength"
                    [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
                    [preferredCountries]="preferredCountries" [searchCountryField]="[
                      SearchCountryField.Iso2,
                      SearchCountryField.Name
                    ]" [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
                    [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" autocomplete="new-phoneNumber"
                    formControlName="phoneNumber" name="phoneNumber">
                  </ngx-intl-tel-input>
                  <label class="mobile-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}*</label>
                </div>
                <div class="mt-4" *ngIf="!isPrimary">
                  <ng-toggle (change)="onToggleChange($event)" [color]="{
                        unchecked: '#00000087',
                        checked: 'rgba(25, 118, 210, 0.20)'
  }" [height]="14" [ngClass]="{'toggle-checked':toggleValue,'toggle-unchecked':!toggleValue}" [value]="false"
                    [width]="43"></ng-toggle>
                  <span class="ml-4 set-default">Set as default</span>
                </div>


              </form>
            </div>
            <button (click)="checkMobileExist()" [disabled]="!verifyForm.controls.phoneNumber.valid"
              [label]="'register.next' | translate" class="p-field p-col-12 mb-5 mt-7 width-100 font-size-14 second-btn"
              pButton type="button"></button>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</section>
