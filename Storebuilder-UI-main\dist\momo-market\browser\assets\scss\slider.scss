@import "common";

.mtn-main-slider {
  .p-carousel-content {
    position: relative;
  }

  .p-carousel-items-content {
    @include responsive(mobile) {
      border-radius: 5px;
    }
  }

  .p-carousel-container {
    position: relative;
  }

  .p-carousel-item {
    height: 330px;

    img {
      height: 100%;
      margin-top: 10px;

      @include responsive(mobile) {
        border-radius: 5px;
      }
    }

    @include responsive(mobile) {
      height: 220px;
      border-radius: 5px;
    }
  }

  .p-carousel-prev {
    color: #ffffff !important;
    background: rgba(255,255,255,0.7) !important;
    position: absolute;
    z-index: 9;
    left: 25px;
    padding: 6px;
    content: url("../../assets/icons/arrow.svg");
    background-size: 10px 10px;
    transform: rotateY(180deg);
    border:1px solid var(--main-color) !important;

    @include responsive(mobile) {
      display: none;
    }
  }

  .p-carousel-next {
    color: #ffffff !important;
    background: rgba(255,255,255,0.7) !important;
    position: absolute;
    z-index: 9;
    right: 25px;
    content: url("../../assets/icons/arrow.svg");
    background-size: 10px 10px;
    padding: 6px;
    border:1px solid var(--main-color) !important;

    @include responsive(mobile) {
      display: none;
    }
  }

  .p-icon-wrapper {
    font-size: 10px;
    width: 10px;
  }

  .p-carousel-indicators {
    display: none;

    @include responsive(mobile) {
      position: absolute;
      bottom: 0;
      left: 6vh;
      padding-bottom: 0;
      display: flex;

      //.p-carousel-indicator.p-highlight button
      .p-highlight {
        button {
          background-color: #262525 !important;
        }
      }

      .p-carousel-indicator {
        button {
          width: 10px;
          border-radius: 50%;
          height: 10px;
        }
      }
    }
  }
}

@include responsive(mobile) {
  .template-one__slider {

    div {
      height: inherit;

      app-mtn-main-slider {
        height: inherit;

        .p-element {
          height: inherit;

          .p-carousel-items-content {
            height: inherit !important;
          }
        }

        .p-carousel-item {
          height: inherit !important;
        }
      }

    }
  }

  .template-one__promotion,
  .template-one__promotion-medium {
    div {
      height: inherit;

      .p-element {
        height: inherit;

        .p-carousel-items-content {
          height: inherit !important;
        }
      }
    }

    .p-carousel-item {
      padding: 0 4px !important;
    }
  }
}
.banner-image {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  cursor: pointer;
}
.banner-image-rounded{
  border-radius: 6px;
}
