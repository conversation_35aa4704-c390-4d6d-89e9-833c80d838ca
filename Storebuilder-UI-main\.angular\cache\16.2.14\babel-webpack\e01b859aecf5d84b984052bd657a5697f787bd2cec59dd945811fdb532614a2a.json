{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from '@environments/environment';\nimport jwt_decode from 'jwt-decode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-cookie-service\";\nimport * as i3 from \"./main-data.service\";\nexport let InitService = /*#__PURE__*/(() => {\n  class InitService {\n    http;\n    cookieService;\n    mainDataService;\n    // private initializationComplete = new BehaviorSubject<boolean>(false);\n    constructor(http, cookieService, mainDataService) {\n      this.http = http;\n      this.cookieService = cookieService;\n      this.mainDataService = mainDataService;\n    }\n    // async getFullSecurityApiKey(): Promise<any> {\n    //   const data =\n    //   `grant_type=client_credentials&client_id=${environment.client_id}&client_secret=${environment.client_secret}&scope=${environment.scope}`;\n    //   const response = await fetch(environment.securityApiUrl, {\n    //     method: 'POST',\n    //     headers: {\n    //       'Content-Type': 'application/x-www-form-urlencoded',\n    //     },\n    //     body: data,\n    //   });\n    //   if (!response.ok) {\n    //     throw new Error('Network response was not ok' + response.statusText);\n    //   }\n    //   const responseData = await response.json();\n    //   let decodedToken: any = jwt_decode(responseData.access_token);\n    //   const expires = new Date(decodedToken.exp * 1000);\n    //   expires.setTime(expires.getTime() + 5 * 60 * 60 * 1000);\n    //   // this.cookieService.set('accessToken', decodedToken.access_token, {\n    //   //   expires: expires,\n    //   //   path: '/',\n    //   //   sameSite: 'Strict',\n    //   // });\n    //   return await {access_token:responseData.access_token,expires:expires};\n    // }\n    getPublicApis() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const token = _this.cookieService.get('accessToken') ?? '';\n        const response = yield fetch('https://uat-apps.paysky.io/sc-authenticator' + '/TenantConfiguration/GetPublicApis', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'x-access-token': token\n          }\n        });\n        if (!response.ok) {\n          throw new Error('Network response was not ok ' + response.statusText);\n        }\n        const responseData = yield response.json();\n        return responseData;\n      })();\n    }\n    loadScript(apiKey) {\n      return new Promise((resolve, reject) => {\n        const url = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;\n        const node = document.createElement('script');\n        node.src = url;\n        node.type = 'text/javascript';\n        node.async = true;\n        document.getElementsByTagName('head')[0].appendChild(node);\n        node.onload = () => resolve();\n        node.onerror = () => reject(new Error('Failed to load script'));\n      });\n    }\n    shouldGetGoogleKey(mapKeyExpiry) {\n      const date1 = new Date(mapKeyExpiry);\n      const date2 = new Date();\n      const time1 = date1.getTime();\n      const time2 = date2.getTime();\n      const differenceInMinutes = (time2 - time1) / (1000 * 60);\n      return differenceInMinutes >= 30;\n    }\n    fetchGoogleKey(tenantId) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const response = yield fetch(environment.apiEndPoint + '/Auth/TenantConfiguration/GetGoogleKey', {\n          method: 'GET',\n          headers: {\n            tenantId: tenantId && tenantId !== '' ? tenantId : environment.defaultTenant\n          }\n        });\n        const resp = yield response.json();\n        const apiKey = atob(resp.data.key);\n        localStorage.setItem('mapKey', apiKey);\n        localStorage.setItem('mapKeyExpiry', new Date().getTime().toString());\n        yield _this2.loadScript(apiKey);\n      })();\n    }\n    doesCookieExist(cookieName) {\n      return this.cookieService.check(cookieName);\n    }\n    isTokenExpired() {\n      let token = this.cookieService.get('accessToken') ?? '';\n      if (token == '') {\n        return false;\n      }\n      const decodedToken = jwt_decode(token);\n      const expiryTime = decodedToken.exp * 1000;\n      const currentTime = new Date().getTime();\n      return expiryTime - currentTime < 5 * 60 * 1000; // Refresh if token expires in less than 5 minutes\n    }\n\n    fetchGoogleTId() {\n      this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\n        next: res => {\n          localStorage.setItem('GATrackingId', res.data ? res.data[0].value : '');\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }\n    initialize() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const tenantId = localStorage.getItem('tenantId') || environment.defaultTenant;\n        if (!tenantId || tenantId === '') localStorage.setItem('tenantId', tenantId);\n        // if(environment.client_id) {\n        //   if (!this.doesCookieExist('accessToken')) {\n        //     let result = await this.getFullSecurityApiKey();\n        //     this.cookieService.set('accessToken', result.access_token, {\n        //       expires: result.expires,\n        //       path: '/',\n        //       sameSite: 'Strict',\n        //     });\n        //     let openApis =await this.getPublicApis();\n        //     localStorage.setItem('publicApis',JSON.stringify(openApis))\n        //   }\n        // }\n        _this3.fetchGoogleTId();\n        const mapKeyExpiry = localStorage.getItem('mapKeyExpiry');\n        if (mapKeyExpiry && _this3.shouldGetGoogleKey(parseInt(mapKeyExpiry))) {\n          yield _this3.fetchGoogleKey(tenantId);\n        } else {\n          const mapKey = localStorage.getItem('mapKey');\n          if (mapKey) {\n            yield _this3.loadScript(mapKey);\n          } else {\n            yield _this3.fetchGoogleKey(tenantId);\n          }\n        }\n        // this.initializationComplete.next(true);\n      })();\n    }\n\n    static ɵfac = function InitService_Factory(t) {\n      return new (t || InitService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.CookieService), i0.ɵɵinject(i3.MainDataService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: InitService,\n      factory: InitService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return InitService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}