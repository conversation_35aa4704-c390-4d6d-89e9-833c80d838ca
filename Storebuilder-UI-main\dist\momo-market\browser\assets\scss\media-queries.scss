@media screen and (min-width: 576px) {

  .sm\:px-9 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }

  .sm\:px-10 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }

  .sm\:px-11 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .sm\:px-12 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }

  .floating-panel {
    bottom: 0 !important;
  }

}

@media screen and (max-width: 1306px) {
  .main-btn {
    font-size: 11px;
  }

  .second-btn {
    font-size: 11px;
  }
}


@media screen and (min-width: 768px) {

  .content-container {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }

  .md\:px-9 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }

  .md\:px-10 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }

  .md\:px-11 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .md\:px-12 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}


@media screen and (min-width: 992px) {

  .content-container {
    display: flow-root;

    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .lg\:px-9 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }

  .lg\:px-10 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }

  .lg\:px-11 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .lg\:px-12 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}


@media screen and (min-width: 1200px) {

  .content-container {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .xl\:px-9 {
    padding-left: 6rem !important;
    padding-right: 6rem !important;
  }

  .xl\:px-10 {
    padding-left: 7rem !important;
    padding-right: 7rem !important;
  }

  .xl\:px-11 {
    padding-left: 8rem !important;
    padding-right: 8rem !important;
  }

  .xl\:px-12 {
    padding-left: 9rem !important;
    padding-right: 9rem !important;
  }
}


@media (min-width: 576px) {
  .page {
    margin-top: 2rem;
  }


}


@media (min-width: 768px) {
  .page {
    margin-top: 5rem;
  }


}


@media (min-width: 992px) {
  .page {
    margin-top: 0rem;
  }


}


@media (min-width: 1200px) {
  .page {
    margin-top: 0rem;
  }


}