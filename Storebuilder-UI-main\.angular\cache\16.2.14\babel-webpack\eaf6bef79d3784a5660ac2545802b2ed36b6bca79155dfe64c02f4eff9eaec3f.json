{"ast": null, "code": "import { APP_INITIALIZER, LOCALE_ID } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';\nimport { TranslateLoader, TranslateModule } from '@ngx-translate/core';\nimport { TranslateHttpLoader } from '@ngx-translate/http-loader';\nimport { BrowserAnimationsModule } from \"@angular/platform-browser/animations\";\nimport { BsModalService } from 'ngx-bootstrap/modal';\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { GoogleMapsModule } from '@angular/google-maps';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoaderInterceptor, AppInterceptor } from '@core/interceptors';\nimport { MessageService } from \"primeng/api\";\nimport { ToastModule } from \"primeng/toast\";\nimport { NgxGoogleAnalyticsModule } from \"ngx-google-analytics\";\nimport { MaintenanceComponent } from \"@pages/maintenance/maintenance.component\";\nimport { RouteInterceptor } from \"@core/interceptors/route.interceptor\";\nimport customFrLocale from './core/directives/french-fs';\nimport { LanguageService } from '@core/services';\nimport { registerLocaleData } from '@angular/common';\nimport { InitService } from '@core/services/init.service';\nimport { GTMService } from '@core/services/gtm.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"ngx-google-analytics\";\nexport function HttpLoaderFactory(http) {\n  return new TranslateHttpLoader(http, './assets/i18n/', '.json');\n}\nexport function getAnalyticsTrackingId() {\n  return localStorage.getItem('GATrackingId') || '';\n}\nexport function initializeApp(initService) {\n  return () => initService.initialize();\n}\nexport class AppModule {\n  constructor() {}\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [BsModalService, GTMService, {\n      provide: APP_INITIALIZER,\n      useFactory: initializeApp,\n      deps: [InitService],\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AppInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: LoaderInterceptor,\n      multi: true\n    }, {\n      provide: HTTP_INTERCEPTORS,\n      useClass: RouteInterceptor,\n      multi: true\n    }, MessageService, {\n      provide: LOCALE_ID,\n      useFactory: translate => {\n        switch (translate.currentLang) {\n          case \"en\":\n          case \"fr\":\n            registerLocaleData(customFrLocale);\n            break;\n        }\n        return translate.currentLang;\n      },\n      deps: [LanguageService]\n    }],\n    imports: [BrowserModule.withServerTransition({\n      appId: 'serverApp'\n    }), BrowserAnimationsModule, AppRoutingModule, HttpClientModule, TranslateModule.forRoot({\n      loader: {\n        provide: TranslateLoader,\n        useFactory: HttpLoaderFactory,\n        deps: [HttpClient]\n      },\n      defaultLanguage: 'en'\n    }), GoogleMapsModule,\n    // SharedModule,\n    FormsModule, ReactiveFormsModule,\n    // LayoutModule,\n    InitialModule, ToastModule, NgxGoogleAnalyticsModule.forRoot(getAnalyticsTrackingId())]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, MaintenanceComponent],\n    imports: [i1.BrowserModule, BrowserAnimationsModule, AppRoutingModule, HttpClientModule, i2.TranslateModule, GoogleMapsModule,\n    // SharedModule,\n    FormsModule, ReactiveFormsModule,\n    // LayoutModule,\n    InitialModule, ToastModule, i3.NgxGoogleAnalyticsModule]\n  });\n})();", "map": {"version": 3, "names": ["APP_INITIALIZER", "LOCALE_ID", "BrowserModule", "HTTP_INTERCEPTORS", "HttpClient", "HttpClientModule", "Translate<PERSON><PERSON><PERSON>", "TranslateModule", "TranslateHttpLoader", "BrowserAnimationsModule", "BsModalService", "FormsModule", "ReactiveFormsModule", "GoogleMapsModule", "InitialModule", "AppRoutingModule", "AppComponent", "LoaderInterceptor", "AppInterceptor", "MessageService", "ToastModule", "NgxGoogleAnalyticsModule", "MaintenanceComponent", "RouteInterceptor", "customFrLocale", "LanguageService", "registerLocaleData", "InitService", "GTMService", "HttpLoaderFactory", "http", "getAnalyticsTrackingId", "localStorage", "getItem", "initializeApp", "initService", "initialize", "AppModule", "constructor", "_", "_2", "bootstrap", "_3", "provide", "useFactory", "deps", "multi", "useClass", "translate", "currentLang", "imports", "withServerTransition", "appId", "forRoot", "loader", "defaultLanguage", "declarations", "i1", "i2", "i3"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\app.module.ts"], "sourcesContent": ["import {APP_INITIALIZER, LOCALE_ID, NgModule} from '@angular/core';\r\nimport {BrowserModule} from '@angular/platform-browser';\r\nimport {HTTP_INTERCEPTORS, HttpClient, HttpClientModule} from '@angular/common/http';\r\nimport {TranslateLoader, TranslateModule} from '@ngx-translate/core';\r\nimport {TranslateHttpLoader} from '@ngx-translate/http-loader';\r\nimport {BrowserAnimationsModule} from \"@angular/platform-browser/animations\";\r\nimport {BsModalService} from 'ngx-bootstrap/modal';\r\nimport {FormsModule, ReactiveFormsModule} from \"@angular/forms\";\r\nimport {GoogleMapsModule} from '@angular/google-maps'\r\n\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nimport {AppRoutingModule} from './app-routing.module';\r\nimport {AppComponent} from './app.component';\r\nimport {LoaderInterceptor, AppInterceptor} from '@core/interceptors';\r\nimport {MessageService} from \"primeng/api\";\r\nimport {ToastModule} from \"primeng/toast\";\r\nimport {NgxGoogleAnalyticsModule, NgxGoogleAnalyticsRouterModule} from \"ngx-google-analytics\";\r\nimport {GoogleTagManagerModule} from \"angular-google-tag-manager\";\r\nimport {MaintenanceComponent} from \"@pages/maintenance/maintenance.component\";\r\nimport {RouteInterceptor} from \"@core/interceptors/route.interceptor\";\r\nimport customFrLocale   from './core/directives/french-fs'\r\nimport { LanguageService } from '@core/services';\r\nimport { registerLocaleData } from '@angular/common';\r\nimport { InitService } from '@core/services/init.service';\r\nimport { GTMService } from '@core/services/gtm.service';\r\nexport function HttpLoaderFactory(http: HttpClient) {\r\n  return new TranslateHttpLoader(http, './assets/i18n/', '.json');\r\n}\r\n\r\nexport function getAnalyticsTrackingId():string {\r\n  return localStorage.getItem('GATrackingId')||'';\r\n}\r\nexport function initializeApp(initService: InitService): () => Promise<void> {\r\n  return () => initService.initialize();\r\n}\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    MaintenanceComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule.withServerTransition({appId: 'serverApp'}),\r\n    BrowserAnimationsModule,\r\n    AppRoutingModule,\r\n    HttpClientModule,\r\n    TranslateModule.forRoot({\r\n      loader: {\r\n        provide: TranslateLoader,\r\n        useFactory: HttpLoaderFactory,\r\n        deps: [HttpClient],\r\n      },\r\n      defaultLanguage: 'en'\r\n    }),\r\n    GoogleMapsModule,\r\n    // SharedModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    // LayoutModule,\r\n    InitialModule,\r\n    ToastModule,\r\n    NgxGoogleAnalyticsModule.forRoot(getAnalyticsTrackingId()),\r\n    // NgxGoogleAnalyticsRouterModule\r\n\r\n  ],\r\n  providers: [\r\n    BsModalService,\r\n    GTMService,\r\n    {\r\n      provide: APP_INITIALIZER,\r\n      useFactory: initializeApp,\r\n      deps: [InitService],\r\n      multi: true\r\n    },\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: AppInterceptor,\r\n      multi: true\r\n    },\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: LoaderInterceptor,\r\n      multi: true,\r\n    },\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: RouteInterceptor,\r\n      multi: true,\r\n    },\r\n    MessageService,\r\n    {\r\n      provide: LOCALE_ID,\r\n      useFactory: (translate: LanguageService) => {\r\n          switch (translate.currentLang) {\r\n              case \"en\":\r\n              case \"fr\":\r\n                  registerLocaleData(customFrLocale)\r\n                  break;\r\n\r\n          }\r\n          return translate.currentLang;\r\n      },\r\n      deps: [LanguageService]\r\n  },\r\n  ],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule {\r\n\r\n  constructor() {\r\n\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAQA,eAAe,EAAEC,SAAS,QAAiB,eAAe;AAClE,SAAQC,aAAa,QAAO,2BAA2B;AACvD,SAAQC,iBAAiB,EAAEC,UAAU,EAAEC,gBAAgB,QAAO,sBAAsB;AACpF,SAAQC,eAAe,EAAEC,eAAe,QAAO,qBAAqB;AACpE,SAAQC,mBAAmB,QAAO,4BAA4B;AAC9D,SAAQC,uBAAuB,QAAO,sCAAsC;AAC5E,SAAQC,cAAc,QAAO,qBAAqB;AAClD,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,gBAAgB,QAAO,sBAAsB;AAErD,SAAQC,aAAa,QAAO,gCAAgC;AAC5D,SAAQC,gBAAgB,QAAO,sBAAsB;AACrD,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,iBAAiB,EAAEC,cAAc,QAAO,oBAAoB;AACpE,SAAQC,cAAc,QAAO,aAAa;AAC1C,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,wBAAwB,QAAuC,sBAAsB;AAE7F,SAAQC,oBAAoB,QAAO,0CAA0C;AAC7E,SAAQC,gBAAgB,QAAO,sCAAsC;AACrE,OAAOC,cAAc,MAAQ,6BAA6B;AAC1D,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,UAAU,QAAQ,4BAA4B;;;;;AACvD,OAAM,SAAUC,iBAAiBA,CAACC,IAAgB;EAChD,OAAO,IAAItB,mBAAmB,CAACsB,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC;AACjE;AAEA,OAAM,SAAUC,sBAAsBA,CAAA;EACpC,OAAOC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAE,EAAE;AACjD;AACA,OAAM,SAAUC,aAAaA,CAACC,WAAwB;EACpD,OAAO,MAAMA,WAAW,CAACC,UAAU,EAAE;AACvC;AAwEA,OAAM,MAAOC,SAAS;EAEpBC,YAAA,GAEA;EAAC,QAAAC,CAAA,G;qBAJUF,SAAS;EAAA;EAAA,QAAAG,EAAA,G;UAATH,SAAS;IAAAI,SAAA,GAFRzB,YAAY;EAAA;EAAA,QAAA0B,EAAA,G;eAxCb,CACThC,cAAc,EACdkB,UAAU,EACV;MACEe,OAAO,EAAE3C,eAAe;MACxB4C,UAAU,EAAEV,aAAa;MACzBW,IAAI,EAAE,CAAClB,WAAW,CAAC;MACnBmB,KAAK,EAAE;KACR,EACD;MACEH,OAAO,EAAExC,iBAAiB;MAC1B4C,QAAQ,EAAE7B,cAAc;MACxB4B,KAAK,EAAE;KACR,EACD;MACEH,OAAO,EAAExC,iBAAiB;MAC1B4C,QAAQ,EAAE9B,iBAAiB;MAC3B6B,KAAK,EAAE;KACR,EACD;MACEH,OAAO,EAAExC,iBAAiB;MAC1B4C,QAAQ,EAAExB,gBAAgB;MAC1BuB,KAAK,EAAE;KACR,EACD3B,cAAc,EACd;MACEwB,OAAO,EAAE1C,SAAS;MAClB2C,UAAU,EAAGI,SAA0B,IAAI;QACvC,QAAQA,SAAS,CAACC,WAAW;UACzB,KAAK,IAAI;UACT,KAAK,IAAI;YACLvB,kBAAkB,CAACF,cAAc,CAAC;YAClC;;QAGR,OAAOwB,SAAS,CAACC,WAAW;MAChC,CAAC;MACDJ,IAAI,EAAE,CAACpB,eAAe;KACzB,CACA;IAAAyB,OAAA,GA9DChD,aAAa,CAACiD,oBAAoB,CAAC;MAACC,KAAK,EAAE;IAAW,CAAC,CAAC,EACxD3C,uBAAuB,EACvBM,gBAAgB,EAChBV,gBAAgB,EAChBE,eAAe,CAAC8C,OAAO,CAAC;MACtBC,MAAM,EAAE;QACNX,OAAO,EAAErC,eAAe;QACxBsC,UAAU,EAAEf,iBAAiB;QAC7BgB,IAAI,EAAE,CAACzC,UAAU;OAClB;MACDmD,eAAe,EAAE;KAClB,CAAC,EACF1C,gBAAgB;IAChB;IACAF,WAAW,EACXC,mBAAmB;IACnB;IACAE,aAAa,EACbM,WAAW,EACXC,wBAAwB,CAACgC,OAAO,CAACtB,sBAAsB,EAAE,CAAC;EAAA;;;2EA8CjDM,SAAS;IAAAmB,YAAA,GArElBxC,YAAY,EACZM,oBAAoB;IAAA4B,OAAA,GAAAO,EAAA,CAAAvD,aAAA,EAIpBO,uBAAuB,EACvBM,gBAAgB,EAChBV,gBAAgB,EAAAqD,EAAA,CAAAnD,eAAA,EAShBM,gBAAgB;IAChB;IACAF,WAAW,EACXC,mBAAmB;IACnB;IACAE,aAAa,EACbM,WAAW,EAAAuC,EAAA,CAAAtC,wBAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}