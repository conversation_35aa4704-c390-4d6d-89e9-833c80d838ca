import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { APMPaymentSDK, APMPaymentSDKConfig, InitiateApmWidgetResponse } from '@pages/checkout/modals/APM';

interface Window {
  APMPaymentSDK: new (config: APMPaymentSDKConfig) => APMPaymentSDK;
}
declare const window: Window;

@Component({
  selector: 'app-apm-payment',
  templateUrl: './apm-payment.component.html',
  styleUrls: ['./apm-payment.component.scss']
})
export class ApmPaymentComponent implements OnInit{
  @Input() config: InitiateApmWidgetResponse;

  @Output() onComplete = new EventEmitter<any>();
  @Output() onCancel = new EventEmitter<void>();
  @Output() onError = new EventEmitter<void>();
  isLoading: boolean = false;



  startPayment(): void {
    this.initWidget();
  }

  ngOnInit() {
    if (this.config) {
      this.initWidget();
    }
  }

  initWidget(): void {
    this.isLoading = true;
    if (typeof window.APMPaymentSDK === 'function') {

      const APM_WIDGET = new window.APMPaymentSDK({
        container: 'apm-widget',
        token: this.config.Token,
        amount: +this.config.Amount,
        reference: this.config.Reference,
        merchantId: this.config.MerchantId,
        timestamp: this.config.Timestamp,
        signature: this.config.Signature,
        currency: this.config.Currency,
        env: this.config.env,
        onPaymentUpdate: (status: string) => {
          if (status === 'Success') this.onComplete.emit(status);
          else this.onError.emit();
        },
        onError: (err: any) => {
          console.warn("failed",err)
          this.onError.emit(err)
        },
        onFormValidation: (errors: any[]) => console.error('Validation:', errors)
      });
      APM_WIDGET.initialize();
    }
    else {
      console.error('APMPaymentSDK not loaded');
      this.isLoading = false;
    }
  }
}
