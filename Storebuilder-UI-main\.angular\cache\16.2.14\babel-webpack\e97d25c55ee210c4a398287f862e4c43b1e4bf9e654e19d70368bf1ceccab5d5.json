{"ast": null, "code": "import { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class WishlistService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiEndPoint;\n  }\n  deleteCustomerWishList(data) {\n    return this.http.post(`${this.baseUrl}/product/CustomerWishList/DeleteCustomerWishList`, data);\n  }\n  getAllCustomerWishList() {\n    return this.http.get(`${this.baseUrl}/Product/CustomerWishList/GetAllCustomerWishList?TopNumber=100`);\n  }\n}\nWishlistService.ɵfac = function WishlistService_Factory(t) {\n  return new (t || WishlistService)(i0.ɵɵinject(i1.HttpClient));\n};\nWishlistService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: WishlistService,\n  factory: WishlistService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}