@import"./common.scss";

.view-all-btn {
  border: none !important;
  color: black !important;

  width: 100%;
  background-color: var(--main_bt_bgcolor) !important;


  font-size: 14px;
  font-family: var(--medium-font);
}

.main-btn {
  font-size: 14px;
  color: var(--main_bt_txtcolor) !important;
  background-color: var(--main_bt_bgcolor);
  border: 1px solid;
  border-radius: 25px;
  font-family: var(--medium-font);
  padding: 10px 20px;

  &:hover {
    color: var(--main_hover_bt_txtcolor) !important;
    background-color: var(--main_hover_bt_bgcolor) !important;
    border: 1px solid var(--main_hover_bt_boarder_color) !important;
  }
}

.second-btn {
  font-size: 14px;
  color: #ffffff;
  background-color: #1d4c69;
  border-radius: 25px;
  border: 1px solid #1d4c69;
  font-family: var(--medium-font);
  padding: 10px 20px;

  &:hover {
    color: black !important;

    background-color: var(--hover_bt_bgcolor) !important;

  }
}

.primary-btn{
  @include responsive(mobile) {
    .p-disabled, .p-component:disabled{
      opacity: 0.4;
    }
    border-radius: 8px;
  }
}