import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CustomPageService } from './custom-page.service';
import { LoaderService } from '@core/services';
import { GTMService } from '@core/services/gtm.service';
import { Meta } from '@angular/platform-browser';
import { Product } from '@core/interface';

@Component({
  selector: 'app-custom-page',
  templateUrl: './custom-page.component.html',
  styleUrls: ['./custom-page.component.scss'],
})
export class CustomPageComponent implements OnInit {
  router: Router = inject(Router);
  customPageService: CustomPageService = inject(CustomPageService);
  loaderService: LoaderService = inject(LoaderService);
  $gtmService: GTMService = inject(GTMService);
  meta = inject(Meta);
  sectionsData: any = [];
  mobileBackground: string = '';
  desktopBackground: string = '';

  ngOnInit(): void {
    this.fetchCustomPageData();
  }

  fetchCustomPageData() {
    this.loaderService.show();
    const urlWithoutParams = this.router.url.split('?')[0];
    this.customPageService
      .getCustomPageDetails(urlWithoutParams)
      .subscribe((res: any) => {

        const { success, data } = res;
        if (!success || data.status === 'Draft' || data.hidePage) {
          this.loaderService.hide();
          this.router.navigateByUrl('page-not-found');
        } else {
          this.desktopBackground = data.desktopBackground;
          this.mobileBackground = data.mobileBackground;

          this.customPageService.setBackgroundStyle(this.backgroundStyle);
          this.customPageService.onChangePageTitle(data.title);
          this.meta.updateTag({
            name: 'description',
            content: data.metaDescription,
          });

          this.sectionsData = data.sections.map(
            ({
              sectionName,
              sectionId,
              products,
              banners,
              productsLimit,
              productsTotal,
            }: any) => ({
              sectionName,
              sectionId,
              banners,

              productsLimit,
              productsTotal,
              products: products.map(({ productDetails }: any) => ({
                ...this.handleProductFromFetchedData(productDetails),
                sectionName,
              })),
            })
          );

          this.loaderService.hide();
        }

        this.$gtmService.pushPageView('Custom page', data.title);
      });
  }

  get backgroundStyle() {
    const bg =
      window.innerWidth <= 768 ? this.mobileBackground : this.desktopBackground;

    if (!bg) {
      return {};
    }

    if (bg.startsWith('http') || bg.startsWith('url(')) {
      return { 'background-image': `url(${bg})` };
    }

    return { background: bg };
  }

  handleProductFromFetchedData(record: any) {
    let selectedVariance;
    let features = [];
    let defaultVariant = record?.productVariances?.find(
      (variant: any) => variant.isDefault
    );

    if (defaultVariant) {
      selectedVariance = defaultVariant;
    } else {
      let approvedVariant = record?.productVariances?.find(
        (variant: any) => variant.soldOut
      );

      if (approvedVariant) {
        selectedVariance = approvedVariant;
      } else {
        selectedVariance = record?.productVariances[0];
      }
    }

    if (selectedVariance?.productFeaturesList) {
      features = selectedVariance?.productFeaturesList[0]?.featureList;
    }

    let product: any = {
      productId: record?.id,
      productName: record?.name,
      isLiked: record?.isLiked,
      priceValue: selectedVariance?.price,
      salePriceValue: selectedVariance?.salePrice,
      priceId: selectedVariance?.priceId,
      currencyCode: record?.currencyCode,
      masterImageUrl:
        record?.masterImageUrl ??
        (selectedVariance.images ? selectedVariance.images[0] : null),
      thumbnailImages: selectedVariance?.thumbnailImages,
      soldOut: selectedVariance?.soldOut,
      rate: selectedVariance?.rate,
      count: selectedVariance?.count ?? 0,
      specProductId: selectedVariance.specProductId,
      channelId: record.channelId ?? '1',
      salePercent: selectedVariance?.salePrice
        ? 100 - (selectedVariance?.salePrice / selectedVariance?.price) * 100
        : 0,
      shopId: record.shopId,
      isHot: features?.includes(1),
      isNew: features?.includes(2),
      isBest: features?.includes(3),
      quantity: selectedVariance.quantity,
      proSchedulingId: selectedVariance.proSchedulingId,
      stockPerSKU: selectedVariance.stockPerSKU,
      stockStatus: selectedVariance.stockStatus,
      sku: selectedVariance?.sku,
      skuAutoGenerated: selectedVariance.skuAutoGenerated,
      badgesList: record?.badgesList,
    };

    return product;
  }

  formatURL(url: string): string {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return 'https://' + url;
    }
    return url;
  }

  redirectFeaturedProducts(sectionName: string, SectionId: number) {
    this.router.navigate([`/${this.router.url}/${sectionName}/${SectionId}`]);
  }

  getLimitedProducts(section: any): Product[] {
    const limit = section.productsLimit ?? section.products.length;
    return section.products.slice(0, limit);
  }
  goToNewLink(url: string) {
    window.open(url, '_blank');
  }
}
