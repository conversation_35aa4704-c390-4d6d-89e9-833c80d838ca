{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MainLandingComponent } from \"@pages/landing/components/main-landing/main-landing.component\";\nimport { AuthGuard } from \"@core/guards/auth.guard\";\nimport { PagenotfoundComponent } from \"@shared/components/pagenotfound/pagenotfound.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MainLandingComponent,\n  children: [{\n    path: '',\n    loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeModule)\n  }, {\n    path: 'product/:id/:channelId',\n    children: [{\n      path: '',\n      loadChildren: () => import('./pages/product-details/product-details.module').then(m => m.ProductDetailsModule)\n    }]\n  }, {\n    path: 'promotion/:id',\n    children: [{\n      path: '',\n      loadChildren: () => import('./pages/category-products/category-products.module').then(m => m.CategoryProductsModule)\n    }]\n  }, {\n    path: 'category/:id',\n    children: [{\n      path: '',\n      loadChildren: () => import('./pages/category-products/category-products.module').then(m => m.CategoryProductsModule)\n    }]\n  }, {\n    path: 'categories',\n    children: [{\n      path: '',\n      loadChildren: () => import('./pages/categories/categories.module').then(m => m.CategoriesModule)\n    }]\n  }, {\n    path: 'search',\n    children: [{\n      path: '',\n      loadChildren: () => import('./pages/search/search.module').then(m => m.SearchModule)\n    }]\n  }, {\n    path: 'cart',\n    loadChildren: () => import('./pages/cart/cart.module').then(m => m.CartModule)\n  }, {\n    path: 'account',\n    loadChildren: () => import('./pages/account/account.module').then(m => m.AccountModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'merchants',\n    loadChildren: () => import('./pages/merchants/merchants.module').then(m => m.MerchantsModule)\n  }, {\n    path: 'orders',\n    loadChildren: () => import('./pages/order/orders/orders.module').then(m => m.OrdersModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'order/:id',\n    loadChildren: () => import('./pages/order/order-details/order-details.module').then(m => m.OrderDetailsModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'order-refund',\n    loadChildren: () => import('./pages/order/order-refund/order-refund.module').then(m => m.OrderRefundModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'login',\n    loadComponent: () => import('./features/auth/components/sign-in/sign-in.component').then(m => m.SignInComponent),\n    canActivate: [AuthGuard]\n  },\n  // {\n  //   path: 'login',\n  //   loadChildren: () => import('./pages/auth/sign-in/sign-in.module').then((m) => m.SignInModule),\n  //   canActivate: [AuthGuard]\n  // },\n  {\n    path: 'contact-us',\n    loadChildren: () => import('./pages/footer-pages/contact-us/contact-us.module').then(m => m.ContactUsModule)\n  }, {\n    path: 'about-us',\n    loadChildren: () => import('./pages/footer-pages/about-us/about-us.module').then(m => m.AboutUsModule)\n  },\n  // {\n  //   path: 'reset-password',\n  //   loadChildren: () => import('./pages/auth/reset-password/reset-password.module').then((m) => m.ResetPasswordModule),\n  // },\n  {\n    path: 'reset-password',\n    loadComponent: () => import('./features/auth/components/reset-password/reset-password.component').then(m => m.ResetPasswordComponent),\n    canActivate: [AuthGuard]\n  },\n  // {\n  //   path: 'otp',\n  //   loadChildren: () => import('./pages/auth/otp/otp.module').then((m) => m.OtpModule),\n  // },\n  {\n    path: 'otp',\n    loadComponent: () => import('./features/auth/components/reset-password-otp/reset-password-otp.component').then(m => m.ResetPasswordOtpComponent),\n    canActivate: [AuthGuard]\n  },\n  // {\n  //   path: 'update-password',\n  //   loadChildren: () => import('./pages/auth/update-password/update-password.module').then((m) => m.UpdatePasswordModule),\n  // },\n  {\n    path: 'update-password',\n    loadComponent: () => import('./features/auth/components/reset-password-confirmation/reset-password-confirmation.component').then(m => m.ResetPasswordConfirmationComponent),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'change-password',\n    loadChildren: () => import('./pages/auth/change-password/change-password.module').then(m => m.ChangePasswordModule)\n  }, {\n    path: 'checkout',\n    loadChildren: () => import('./pages/checkout/checkout.module').then(m => m.CheckoutModule),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'register',\n    loadComponent: () => import('./features/auth/components/register/register.component').then(m => m.RegisterComponent),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'register/register-otp',\n    loadComponent: () => import('./features/auth/components/register-otp/register-otp.component').then(m => m.RegisterOtpComponent),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'register/register-details',\n    loadComponent: () => import('./features/auth/components/register-details/register-details.component').then(m => m.RegisterDetailsComponent),\n    canActivate: [AuthGuard]\n  }, {\n    path: 'not-found',\n    loadChildren: () => import('./pages/not-found/not-found.module').then(m => m.NotFoundModule)\n  }, {\n    path: 'wishlist',\n    loadChildren: () => import('./pages/wishlist/wishlist.module').then(m => m.WishlistModule)\n  }, {\n    path: 'merchants-livestream',\n    loadChildren: () => import('./pages/merchant-livestream/merchant-livestream.module').then(m => m.MerchantLivestreamModule)\n  }, {\n    path: 'categories-list',\n    loadChildren: () => import('./pages/category-list-mobile/category-list.module').then(m => m.CategoryListModule)\n  }, {\n    path: ':tenant/:title',\n    loadChildren: () => import('./pages/custom-page/custom-page.module').then(m => m.CustomPageModule)\n  }, {\n    path: 'page-not-found',\n    component: PagenotfoundComponent\n  }, {\n    path: '**',\n    pathMatch: 'full',\n    component: PagenotfoundComponent\n  }]\n}];\nexport class AppRoutingModule {\n  static ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes, {\n      initialNavigation: 'enabledBlocking',\n      anchorScrolling: 'enabled',\n      scrollPositionRestoration: 'enabled',\n      scrollOffset: [0, -20]\n    }), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}