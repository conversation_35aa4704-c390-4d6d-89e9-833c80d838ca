 <div class="p-field p-col-12">
      <div [ngStyle]="{
          border:
            signInForm.controls.phoneNumber.value?.e164Number?.length > 0 &&
            !signInForm.controls.phoneNumber.valid
              ? '1px solid red'
              : '0px solid transparent'
        }" class="custom-input">
        <form #f="ngForm" [formGroup]="signInForm">
          <label class="contact-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}*</label>
          <ngx-intl-tel-input
            #phoneInput
            [cssClass]="'custom contact-input-phone mobile-input-phone'"
            [enableAutoCountrySelect]="true"
            [enablePlaceholder]="true"
            [maxLength]="maxLength"
            [numberFormat]="PhoneNumberFormat.National"
            [phoneValidation]="false"
            [preferredCountries]="preferredCountries"
            [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
            [searchCountryFlag]="true"
            [selectFirstCountry]="false"
            [selectedCountryISO]="selectedCountryISO"
            [separateDialCode]="true"
            [customPlaceholder]="placeholder"
            autocomplete="new-phoneNumber"
            formControlName="phoneNumber"
            name="phoneNumber">
          </ngx-intl-tel-input>
        </form>
      </div>
    </div>
