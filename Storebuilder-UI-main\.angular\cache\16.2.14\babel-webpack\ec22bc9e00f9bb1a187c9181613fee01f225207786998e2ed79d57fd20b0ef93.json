{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class PagenotfoundComponent {\n  static ɵfac = function PagenotfoundComponent_Factory(t) {\n    return new (t || PagenotfoundComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PagenotfoundComponent,\n    selectors: [[\"app-mtn-pagenotfound\"]],\n    decls: 17,\n    vars: 3,\n    consts: [[1, \"not-found\", \"custom-css\", \"m-top\"], [1, \"container\", \"d-flex\", \"justify-content-around\"], [1, \"flex-column\"], [1, \"flex-row\", \"text-wrapper-5\", \"text-center\"], [1, \"flex-row\", \"text-wrapper-6\", \"text-center\"], [1, \"flex-row\"], [1, \"this-page-doesn-t\"], [\"routerLink\", \"/\", \"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-2\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"label\"], [\"src\", \"https://c.animaapp.com/RLaOyNW3/img/frame.svg\", \"alt\", \"Page not found image\", 1, \"img\", \"not-found-img\"]],\n    template: function PagenotfoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \"Oops....\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵtext(6, \"Page not found\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"p\", 6);\n        i0.ɵɵtext(9, \" This Page doesn't exist or was removed!\");\n        i0.ɵɵelement(10, \"br\");\n        i0.ɵɵtext(11, \"We suggest you\\u00A0\\u00A0back to home. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 5);\n        i0.ɵɵelement(13, \"button\", 7);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 2);\n        i0.ɵɵelement(16, \"img\", 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(14, 1, \"notFound.goBack\"));\n      }\n    },\n    dependencies: [i1.ButtonDirective, i2.RouterLink, i3.TranslatePipe],\n    styles: [\".header-spacing {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar {\\n  margin-top: 122px;\\n}\\n\\n.discount-price {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.text-wrapper-5 {\\n  font-family: var(--medium-font) !important;\\n  font-size: 40px;\\n  line-height: 52px;\\n  font-weight: 700;\\n}\\n\\n.text-wrapper-6 {\\n  font-family: var(--medium-font) !important;\\n  font-size: 32px;\\n  line-height: 41.6px;\\n  font-weight: 400;\\n}\\n\\n.this-page-doesn-t {\\n  font-family: var(--regular-font) !important;\\n  font-size: 16px;\\n  font-weight: 400;\\n  line-height: 24px;\\n  letter-spacing: 0.04em;\\n  text-align: center;\\n}\\n\\n.m-top {\\n  margin-top: 12rem !important;\\n}\\n\\n.not-found-img {\\n  width: 320px;\\n  height: 320px;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n  .m-top {\\n    margin-top: 1rem !important;\\n  }\\n  .not-found-img {\\n    width: 200px;\\n    height: 200px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .m-top {\\n    margin-top: 250px !important;\\n  }\\n}\"],\n    encapsulation: 2\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}