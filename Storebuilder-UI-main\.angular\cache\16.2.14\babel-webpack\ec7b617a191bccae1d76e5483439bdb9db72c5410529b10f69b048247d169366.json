{"ast": null, "code": "import { IndexComponent } from \"./components/index/index.component\";\nimport { AddressComponent } from \"./components/address/address.component\";\nimport { DetailsComponent } from \"./components/details/details.component\";\nexport const routes = [{\n  path: '',\n  component: IndexComponent\n}, {\n  path: 'address',\n  component: AddressComponent\n}, {\n  path: 'details',\n  component: DetailsComponent\n}];", "map": {"version": 3, "names": ["IndexComponent", "AddressComponent", "DetailsComponent", "routes", "path", "component"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\routes.ts"], "sourcesContent": ["import {Routes} from \"@angular/router\";\r\nimport {IndexComponent} from \"./components/index/index.component\";\r\nimport {AddressComponent} from \"./components/address/address.component\";\r\nimport { DetailsComponent } from \"./components/details/details.component\";\r\nexport const routes: Routes = [\r\n  {path: '', component: IndexComponent},\r\n  {path: 'address', component: AddressComponent},\r\n  {path: 'details', component: DetailsComponent},\r\n];\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,oCAAoC;AACjE,SAAQC,gBAAgB,QAAO,wCAAwC;AACvE,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAACC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEL;AAAc,CAAC,EACrC;EAACI,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEJ;AAAgB,CAAC,EAC9C;EAACG,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEH;AAAgB,CAAC,CAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}