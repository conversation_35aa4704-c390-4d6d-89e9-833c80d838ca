import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {RouterModule} from "@angular/router";
import {RippleModule} from "primeng/ripple";
import {RadioButtonModule} from 'primeng/radiobutton';
import {DropdownModule} from 'primeng/dropdown';
import {DynamicDialogModule} from 'primeng/dynamicdialog';
import {SelectButtonModule} from 'primeng/selectbutton';
import {InputMaskModule} from "primeng/inputmask";
import {CheckboxModule} from "primeng/checkbox";
import {DialogModule} from "primeng/dialog";

import {routes} from "./routes";

// Components
import {IndexComponent} from './components/index/index.component';
import {OrderSummaryCartComponent} from './components/order-summary-cart/order-summary-cart.component';
import {PaymentCartComponent} from './components/payment-cart/payment-cart.component';
import {DeliveryMethodCartComponent} from './components/delivery-method-cart/delivery-method-cart.component';
import {PaymentDialogComponent} from './components/payment-dialog/payment-dialog.component';
import {OrderPlacedComponent} from './components/order-placed/order-placed.component';
import {PaymentErrorDialogComponent} from './components/payment-error-dialog/payment-error-dialog.component';
import {PaymentWaitingDialogComponent} from './components/payment-waiting-dialog/payment-waiting-dialog.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {TranslateModule} from "@ngx-translate/core";
import {ButtonModule} from "primeng/button";
import {SharedModule} from "@shared/modules/shared.module";

import { LightboxLoaderModalComponent} from "@pages/checkout/modals/lightbox-loader-modal/lightbox-loader-modal.component";
import { SelectAddressMobiluiComponent } from './components/select-address-mobilui/select-address-mobilui.component';
import { PromoCodeComponent } from './components/promo-code/promo-code.component';
import { LightboxPaymentComponent } from './components/lightbox-payment/lightbox-payment.component';
import { PaymentMethodWrapperComponent } from './components/payment-method-wrapper/payment-method-wrapper.component';
import { ApmPaymentComponent } from './components/apm-payment/apm-payment.component';

@NgModule({
  declarations: [
    IndexComponent,
    PaymentCartComponent,
    DeliveryMethodCartComponent,
    OrderSummaryCartComponent,
    PaymentDialogComponent,
    OrderPlacedComponent,
    PaymentErrorDialogComponent,
    PaymentWaitingDialogComponent,
    LightboxLoaderModalComponent,
    SelectAddressMobiluiComponent,
    PromoCodeComponent,
    LightboxPaymentComponent,
    PaymentMethodWrapperComponent,
    ApmPaymentComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    RippleModule,
    RadioButtonModule,
    DropdownModule,
    DynamicDialogModule,
    SelectButtonModule,
    InputMaskModule,
    CheckboxModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    ButtonModule,
    SharedModule,
    DialogModule
]
})
export class CheckoutModule {
}


