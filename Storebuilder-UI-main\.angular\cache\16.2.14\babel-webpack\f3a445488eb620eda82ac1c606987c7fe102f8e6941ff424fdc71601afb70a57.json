{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { NavigationEnd } from \"@angular/router\";\nimport { filter } from 'rxjs';\nimport { GaActionEnum } from 'ngx-google-analytics';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../../shared/components/product-slider/product-slider.component\";\nimport * as i9 from \"../../../../shared/components/section/section.component\";\nimport * as i10 from \"../../../../shared/components/back-button/back-button.component\";\nimport * as i11 from \"../details/details.component\";\nimport * as i12 from \"../image-zoom/image-zoom.component\";\nimport * as i13 from \"../floating-panel/floating-panel.component\";\nimport * as i14 from \"../product-images/product-images.component\";\nfunction IndexRevampComponent_div_0_section_1_app_back_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-back-button\");\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_2_p_breadcrumb_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-breadcrumb\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"home\", ctx_r9.home)(\"model\", ctx_r9.items);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"breadcrumb\": a0,\n    \"hiddenNavbarBreadcrum\": a1\n  };\n};\nfunction IndexRevampComponent_div_0_section_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, IndexRevampComponent_div_0_section_1_div_2_p_breadcrumb_1_Template, 1, 2, \"p-breadcrumb\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r4.navbarData == null ? null : ctx_r4.navbarData.isActive, !(ctx_r4.navbarData == null ? null : ctx_r4.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.screenWidth > 768);\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.productDetails == null ? null : ctx_r5.productDetails.sellerName, \" \");\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r6.productDetails == null ? null : ctx_r6.productDetails.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.productDetails == null ? null : ctx_r6.productDetails.name, \" \");\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r10.selectedVariance == null ? null : ctx_r10.selectedVariance.salePercent == null ? null : ctx_r10.selectedVariance.salePercent.toFixed(0), \"% \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.off\"), \" \");\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.outOfStock\"), \" \");\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, IndexRevampComponent_div_0_section_1_div_5_div_1_Template, 3, 4, \"div\", 16);\n    i0.ɵɵtemplate(2, IndexRevampComponent_div_0_section_1_div_5_div_2_Template, 3, 3, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedVariance.salePercent > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.selectedVariance == null ? null : ctx_r7.selectedVariance.soldOut);\n  }\n}\nfunction IndexRevampComponent_div_0_section_1_ng_container_7_app_floating_panel_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-floating-panel\", 26);\n    i0.ɵɵlistener(\"onItemLike\", function IndexRevampComponent_div_0_section_1_ng_container_7_app_floating_panel_6_Template_app_floating_panel_onItemLike_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r13.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"currency\", ctx_r12.currency)(\"product\", ctx_r12.productDetails)(\"variant\", ctx_r12.selectedVariance);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"flex-column\": a0\n  };\n};\nfunction IndexRevampComponent_div_0_section_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵelement(3, \"app-product-images\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"app-details\", 24);\n    i0.ɵɵlistener(\"onChangeVariant\", function IndexRevampComponent_div_0_section_1_ng_container_7_Template_app_details_onChangeVariant_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.onvVarinaceChange($event));\n    })(\"onItemLike\", function IndexRevampComponent_div_0_section_1_ng_container_7_Template_app_details_onItemLike_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, IndexRevampComponent_div_0_section_1_ng_container_7_app_floating_panel_6_Template, 1, 3, \"app-floating-panel\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, ctx_r8.screenWidth <= 1200));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"product\", ctx_r8.productDetails)(\"variant\", ctx_r8.selectedVariance)(\"channelId\", ctx_r8.channelId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"currency\", ctx_r8.currency)(\"product\", ctx_r8.productDetails)(\"selectedColor\", ctx_r8.selectedColor)(\"selectedSize\", ctx_r8.selectedSize)(\"selectedSize2\", ctx_r8.selectedSize2)(\"selectedVariant\", ctx_r8.selectedVariance)(\"cols\", ctx_r8.cols)(\"channelId\", ctx_r8.channelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.screenWidth > 1200);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"product-details__hidden-navbar\": a0\n  };\n};\nfunction IndexRevampComponent_div_0_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 4);\n    i0.ɵɵtemplate(1, IndexRevampComponent_div_0_section_1_app_back_button_1_Template, 1, 0, \"app-back-button\", 5);\n    i0.ɵɵtemplate(2, IndexRevampComponent_div_0_section_1_div_2_Template, 2, 5, \"div\", 6);\n    i0.ɵɵtemplate(3, IndexRevampComponent_div_0_section_1_div_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵtemplate(4, IndexRevampComponent_div_0_section_1_div_4_Template, 2, 2, \"div\", 8);\n    i0.ɵɵtemplate(5, IndexRevampComponent_div_0_section_1_div_5_Template, 3, 2, \"div\", 9);\n    i0.ɵɵelementStart(6, \"div\", 2);\n    i0.ɵɵtemplate(7, IndexRevampComponent_div_0_section_1_ng_container_7_Template, 7, 15, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c2, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.screenWidth < 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.screenWidth > 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.screenWidth < 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.screenWidth < 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.screenWidth < 768);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.productDetails == null ? null : ctx_r2.productDetails.id);\n  }\n}\nfunction IndexRevampComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, IndexRevampComponent_div_0_section_1_Template, 8, 9, \"section\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.productDetails == null ? null : ctx_r0.productDetails.id);\n  }\n}\nfunction IndexRevampComponent_div_1_ng_container_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"app-mtn-section\", 39);\n    i0.ɵɵelement(2, \"app-mtn-product-slider\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", \"Related Products\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"products\", ctx_r19.relatedProducts);\n  }\n}\nfunction IndexRevampComponent_div_1_ng_container_4_app_floating_panel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-floating-panel\", 26);\n    i0.ɵɵlistener(\"onItemLike\", function IndexRevampComponent_div_1_ng_container_4_app_floating_panel_9_Template_app_floating_panel_onItemLike_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"currency\", ctx_r20.currency)(\"product\", ctx_r20.productDetails)(\"variant\", ctx_r20.selectedVariance);\n  }\n}\nfunction IndexRevampComponent_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"div\", 32)(4, \"div\", 33);\n    i0.ɵɵelement(5, \"app-image-zoom\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"app-details\", 36);\n    i0.ɵɵlistener(\"onChangeVariant\", function IndexRevampComponent_div_1_ng_container_4_Template_app_details_onChangeVariant_7_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onvVarinaceChange($event));\n    })(\"onItemLike\", function IndexRevampComponent_div_1_ng_container_4_Template_app_details_onItemLike_7_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, IndexRevampComponent_div_1_ng_container_4_div_8_Template, 3, 2, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, IndexRevampComponent_div_1_ng_container_4_app_floating_panel_9_Template, 1, 3, \"app-floating-panel\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"product\", ctx_r18.productDetails)(\"variant\", ctx_r18.selectedVariance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"currency\", ctx_r18.currency)(\"product\", ctx_r18.productDetails)(\"selectedColor\", ctx_r18.selectedColor)(\"selectedSize\", ctx_r18.selectedSize)(\"selectedSize2\", ctx_r18.selectedSize2)(\"selectedVariant\", ctx_r18.selectedVariance)(\"cols\", ctx_r18.cols);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.relatedProducts.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.showPanel);\n  }\n}\nfunction IndexRevampComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"section\", 28)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"p-breadcrumb\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, IndexRevampComponent_div_1_ng_container_4_Template, 10, 11, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"home\", ctx_r1.home)(\"model\", ctx_r1.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.productDetails == null ? null : ctx_r1.productDetails.id);\n  }\n}\nexport class IndexRevampComponent {\n  onResize(event) {\n    this.screenWidth = window.innerWidth;\n    this.updateZoomClass();\n  }\n  constructor(store, activatedRoute, productService, messageService, router, translate, ref, loaderService, appDataService, $gaService, permissionService, platformId, $gtmService) {\n    this.store = store;\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.router = router;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.appDataService = appDataService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    this.$gtmService = $gtmService;\n    this.relatedProducts = [];\n    this.currency = {};\n    this.productDetails = {};\n    this.loading = false;\n    this.showPanel = false;\n    this.items = [];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.name = 'alam';\n    this.screenWidth = window.innerWidth;\n    this.decimalValue = 0;\n    this.zoomLevelClass = 'default-zoom';\n    this.isLayoutTemplate = false;\n    this.tagName = GaActionEnum;\n    this.cols = [];\n    this.isGoogleAnalytics = false;\n    this.isMobileLayout = false;\n    this.isMobileView = this.screenWidth <= 786;\n    this.onScrollEvent = _event => {\n      if (isPlatformBrowser(this.platformId)) {\n        const sectionDetails = document.querySelector('#details');\n        const relatedProductsSection = document.querySelector('#relatedProducts');\n        const fromTop = sectionDetails ? sectionDetails.getBoundingClientRect().top < -110 : false;\n        const fromBottom = relatedProductsSection ? relatedProductsSection.getBoundingClientRect().top < 550 : false;\n        this.showPanel = fromTop && !fromBottom;\n      }\n    };\n    this.initializeComponent();\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.specProductId = params.get('id');\n      this.channelId = params.get('channelId');\n    });\n  }\n  ngOnInit() {\n    this.initializeNavbar();\n    this.loadData();\n    if (isPlatformBrowser(this.platformId)) {\n      window.addEventListener('scroll', this.onScrollEvent, true);\n    }\n    this.scrollToTopOnNavigation();\n  }\n  ngAfterViewInit() {\n    this.subscribeToCurrency();\n    this.scrollToTop();\n  }\n  onItemLiked(event) {\n    this.loadData();\n  }\n  loadData() {\n    this.loading = true;\n    this.loaderService.show();\n    this.productService.getProductDetails(this.specProductId, this.channelId).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        this.productDetails = res.data;\n        this.channelId = res.data?.channelId;\n        this.processProductDetails();\n        this.$gtmService.pushPageView(this.productDetails?.categoryPath, this.productDetails.name);\n      },\n      error: err => {\n        this.handleError(err);\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    this.subscribeToCategories();\n  }\n  onvVarinaceChange(variance) {\n    this.selectedVariance = variance;\n  }\n  initializeComponent() {\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.disableCent = localStorage.getItem('disableCent');\n    const value = localStorage.getItem('decimalValue');\n    if (value) this.decimalValue = parseInt(value);\n    this.subscribeToRouteParams();\n    this.subscribeToRouterEvents();\n  }\n  initializeNavbar() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n  }\n  scrollToTopOnNavigation() {\n    this.router.events.subscribe(evt => {\n      if (evt instanceof NavigationEnd) {\n        window.scrollTo(0, 0);\n      }\n    });\n  }\n  subscribeToCurrency() {\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n  }\n  scrollToTop() {\n    if (isPlatformBrowser(this.platformId)) {\n      const top = document.getElementById('top');\n      if (top) {\n        top.scrollIntoView();\n      }\n    }\n  }\n  subscribeToRouteParams() {\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.specProductId = params.get('id');\n    });\n  }\n  subscribeToRouterEvents() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe();\n  }\n  processProductDetails() {\n    if (this.productDetails) {\n      this.userDetails = this.store.get('profile');\n      this.trackGoogleAnalytics();\n      this.initializeProductSpecs();\n      this.processProductVariances();\n      this.updateSelectedVarianceDetails();\n      if (this.channelId == '1') {\n        this.getTemplateFields(this.productDetails.templateId);\n      }\n      this.assignBreadCrumbsData();\n      this.ref.detectChanges();\n      this.ref.markForCheck();\n    }\n    this.loading = false;\n  }\n  trackGoogleAnalytics() {\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature('VIEW_ITEM')) {\n      this.$gaService.pageView('/product', `Product Detail: ${this.specProductId}`);\n      this.$gaService.event(this.tagName.VIEW_ITEM, this.productDetails.categoryName, this.productDetails.name, 1, true, {\n        product_ID: this.productDetails.id,\n        user_ID: this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n  }\n  initializeProductSpecs() {\n    this.productDetails['specs'] = {};\n    this.productDetails.productSpecs?.forEach(spec => {\n      this.productDetails.specs[spec.name] = spec;\n    });\n  }\n  processProductVariances() {\n    this.productDetails.productVariances.forEach(variance => {\n      this.calculateVarianceSalePercent(variance);\n      variance['specs'] = {};\n      variance.varianceSpecs?.forEach(spec => {\n        variance['specs'][spec.name] = spec;\n      });\n    });\n  }\n  calculateVarianceSalePercent(variance) {\n    if (variance.salePrice) {\n      variance.salePercent = 100 - variance.salePrice / variance.price * 100;\n    } else if (variance.salePriceValue) {\n      variance.salePercent = 100 - variance.salePriceValue / variance.price * 100;\n    }\n  }\n  updateSelectedVarianceDetails() {\n    this.selectedVariance = this.productDetails.productVariances.find(variant => variant.isDefault) || this.productDetails.productVariances.find(variant => !variant.soldOut) || this.productDetails.productVariances[0];\n    this.selectedColor = this.selectedVariance.color;\n    this.selectedVariance.varianceSpecs?.forEach(variantSpec => {\n      if (variantSpec.name === 'Size') {\n        this.selectedSize = variantSpec.value;\n      }\n      if (variantSpec.name === 'Size 2') {\n        this.selectedSize2 = variantSpec.value;\n      }\n    });\n  }\n  handleError(err) {\n    this.loaderService.hide();\n    console.error(err);\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: err.message\n    });\n    this.loading = false;\n  }\n  subscribeToCategories() {\n    this.store.subscription('categories').subscribe({\n      next: res => {\n        this.category = res.find(element => element.id === this.categoryId);\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  assignBreadCrumbsData() {\n    const idsArray = this.productDetails?.categoryIds?.split('->').map(Number).slice(0, 3);\n    const nameArray = this.productDetails?.categoryPath?.split('->').map(String).slice(0, 3);\n    this.items = idsArray?.map((id, index) => ({\n      routerLink: `/category/${id}`,\n      label: nameArray[index]\n    })) || [];\n  }\n  getTemplateFields(id) {\n    this.productService.getAllTemplateFields(id).subscribe(res => {\n      if (res.success) {\n        this.cols = res.data.records;\n      }\n    });\n  }\n  updateZoomClass() {\n    const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n    if (zoomLevel <= 91) {\n      this.zoomLevelClass = 'zoom-110';\n    } else if (zoomLevel <= 112) {\n      this.zoomLevelClass = 'zoom-90';\n    } else if (zoomLevel <= 125) {\n      this.zoomLevelClass = 'zoom-80';\n    } else if (zoomLevel <= 134) {\n      this.zoomLevelClass = 'zoom-75';\n    } else if (zoomLevel <= 150) {\n      this.zoomLevelClass = 'zoom-67';\n    } else if (zoomLevel <= 200) {\n      this.zoomLevelClass = 'zoom-50';\n    } else if (zoomLevel <= 300) {\n      this.zoomLevelClass = 'zoom-33';\n    } else if (zoomLevel <= 400) {\n      this.zoomLevelClass = 'zoom-25';\n    } else {\n      this.zoomLevelClass = 'default-zoom';\n    }\n  }\n  static #_ = this.ɵfac = function IndexRevampComponent_Factory(t) {\n    return new (t || IndexRevampComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i6.GTMService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexRevampComponent,\n    selectors: [[\"app-index-revamp\"]],\n    hostBindings: function IndexRevampComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function IndexRevampComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"new-product-details\", 4, \"ngIf\"], [\"class\", \"old-product-details\", 4, \"ngIf\"], [1, \"new-product-details\"], [\"class\", \"product-details\", \"id\", \"top\", 3, \"ngClass\", 4, \"ngIf\"], [\"id\", \"top\", 1, \"product-details\", 3, \"ngClass\"], [4, \"ngIf\"], [\"class\", \"breadcrumb\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"seller-info-name\", 4, \"ngIf\"], [\"class\", \"product-name\", \"data-placement\", \"top\", \"data-toggle\", \"tooltip\", 3, \"title\", 4, \"ngIf\"], [\"class\", \"d-flex product-details__tag-section\", 4, \"ngIf\"], [1, \"breadcrumb\", 3, \"ngClass\"], [\"class\", \"col-12\", 3, \"home\", \"model\", 4, \"ngIf\"], [1, \"col-12\", 3, \"home\", \"model\"], [1, \"seller-info-name\"], [\"data-placement\", \"top\", \"data-toggle\", \"tooltip\", 1, \"product-name\", 3, \"title\"], [1, \"d-flex\", \"product-details__tag-section\"], [\"class\", \"d-inline-flex product-details__tag-section__green-label\", 4, \"ngIf\"], [\"class\", \"d-inline-flex justify-content-normal product-details__tag-section__stock\", 4, \"ngIf\"], [1, \"d-inline-flex\", \"product-details__tag-section__green-label\"], [1, \"d-inline-flex\", \"justify-content-normal\", \"product-details__tag-section__stock\"], [1, \"d-flex\", \"flex-row\", 3, \"ngClass\"], [1, \"product-details__images-section\"], [3, \"product\", \"variant\", \"channelId\"], [1, \"product-details__details-section\"], [\"id\", \"details\", 3, \"currency\", \"product\", \"selectedColor\", \"selectedSize\", \"selectedSize2\", \"selectedVariant\", \"cols\", \"channelId\", \"onChangeVariant\", \"onItemLike\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\", 4, \"ngIf\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\"], [1, \"old-product-details\"], [\"id\", \"top\", 1, \"product-details\"], [1, \"breadcrumb\"], [1, \"\"], [1, \"grid\", \"pt-0\"], [1, \"col-12\", \"col-md-6\", \"col-lg-7\"], [1, \"images\"], [3, \"product\", \"variant\"], [1, \"col-12\", \"col-md-8\", \"col-lg-5\"], [\"id\", \"details\", 3, \"currency\", \"product\", \"selectedColor\", \"selectedSize\", \"selectedSize2\", \"selectedVariant\", \"cols\", \"onChangeVariant\", \"onItemLike\"], [\"class\", \"my-5\", 4, \"ngIf\"], [1, \"my-5\"], [\"id\", \"relatedProducts\", 3, \"title\"], [3, \"products\"]],\n    template: function IndexRevampComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IndexRevampComponent_div_0_Template, 2, 1, \"div\", 0);\n        i0.ɵɵtemplate(1, IndexRevampComponent_div_1_Template, 5, 3, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i8.ProductSliderComponent, i9.SectionComponent, i10.BackButtonComponent, i11.DetailsComponent, i12.ImageZoomComponent, i13.FloatingPanelComponent, i14.ProductImagesComponent, i4.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.new-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n@media only screen and (max-width: 767px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 56px;\\n  }\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details__images-section[_ngcontent-%COMP%] {\\n  max-width: 30%;\\n  width: 30%;\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details__images-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details__images-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details__details-section[_ngcontent-%COMP%] {\\n  max-width: 70%;\\n  width: 70%;\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details__details-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details__details-section[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    width: 100%;\\n  }\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details__tag-section[_ngcontent-%COMP%] {\\n  padding: 0px 17px 17px 17px;\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details__tag-section__green-label[_ngcontent-%COMP%] {\\n  background: #2DB224;\\n  align-items: flex-start;\\n  border-radius: 2px;\\n  color: #FFF;\\n  font-family: var(--regular-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 16px;\\n  \\n\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  padding: 5px 10px;\\n}\\n.new-product-details[_ngcontent-%COMP%]   .product-details__tag-section__stock[_ngcontent-%COMP%] {\\n  border-radius: 2px;\\n  background: var(--Gray-400, #929FA5);\\n  color: var(--Gray-00, var(--colors-fff, #FFF));\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 700;\\n  padding: 5px 10px;\\n  line-height: 16px;\\n  \\n\\n  font-family: var(--light-font);\\n  padding: 5px 10px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .new-product-details[_ngcontent-%COMP%]   .product-details__hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 56px;\\n  }\\n  .new-product-details[_ngcontent-%COMP%]   .seller-info-name[_ngcontent-%COMP%] {\\n    color: #5A5A5A;\\n    font-family: var(--regular-font);\\n    font-size: 12px;\\n    font-style: normal;\\n    font-weight: 400;\\n    line-height: 100%;\\n    padding: 0px 17px 12px 17px;\\n    text-decoration-line: underline;\\n    margin-top: 6em;\\n  }\\n  .new-product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    width: 100%;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    color: #204E6E;\\n    font-family: var(--regular-font);\\n    font-size: 16px;\\n    font-style: normal;\\n    font-weight: 700;\\n    margin-bottom: 10px;\\n    padding: 0px 17px;\\n  }\\n}\\n\\n.old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  position: relative;\\n  margin-top: 93px;\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  .old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n  }\\n}\\n.old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n}\\n@media screen and (max-width: 320px) {\\n  .old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 140px;\\n  }\\n  .old-product-details[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 0rem !important;\\n  }\\n  .old-product-details[_ngcontent-%COMP%]   .pt-6[_ngcontent-%COMP%] {\\n    padding-top: 0rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) and (min-width: 325px) {\\n  .old-product-details[_ngcontent-%COMP%]   .h-d-1[_ngcontent-%COMP%] {\\n    height: 300px !important;\\n  }\\n  .old-product-details[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 155px;\\n  }\\n  .old-product-details[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 1rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .old-product-details[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n    min-height: 100% !important;\\n  }\\n}\\n\\n[_nghost-%COMP%]   app-back-button[_ngcontent-%COMP%] {\\n  position: relative;\\n  top: -5em;\\n  display: inline-block;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "NavigationEnd", "filter", "GaActionEnum", "isPlatformBrowser", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r9", "home", "items", "ɵɵelementStart", "ɵɵtemplate", "IndexRevampComponent_div_0_section_1_div_2_p_breadcrumb_1_Template", "ɵɵelementEnd", "ɵɵpureFunction2", "_c0", "ctx_r4", "navbarData", "isActive", "ɵɵadvance", "screenWidth", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r5", "productDetails", "sellerName", "ɵɵpropertyInterpolate", "ctx_r6", "name", "ɵɵtextInterpolate2", "ctx_r10", "<PERSON><PERSON><PERSON><PERSON>", "salePercent", "toFixed", "ɵɵpipeBind1", "IndexRevampComponent_div_0_section_1_div_5_div_1_Template", "IndexRevampComponent_div_0_section_1_div_5_div_2_Template", "ctx_r7", "soldOut", "ɵɵlistener", "IndexRevampComponent_div_0_section_1_ng_container_7_app_floating_panel_6_Template_app_floating_panel_onItemLike_0_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "onItemLiked", "ctx_r12", "currency", "ɵɵelementContainerStart", "IndexRevampComponent_div_0_section_1_ng_container_7_Template_app_details_onChangeVariant_5_listener", "_r16", "ctx_r15", "onvVarinaceChange", "IndexRevampComponent_div_0_section_1_ng_container_7_Template_app_details_onItemLike_5_listener", "ctx_r17", "IndexRevampComponent_div_0_section_1_ng_container_7_app_floating_panel_6_Template", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c1", "ctx_r8", "channelId", "selectedColor", "selectedSize", "selectedSize2", "cols", "IndexRevampComponent_div_0_section_1_app_back_button_1_Template", "IndexRevampComponent_div_0_section_1_div_2_Template", "IndexRevampComponent_div_0_section_1_div_3_Template", "IndexRevampComponent_div_0_section_1_div_4_Template", "IndexRevampComponent_div_0_section_1_div_5_Template", "IndexRevampComponent_div_0_section_1_ng_container_7_Template", "_c2", "ctx_r2", "id", "IndexRevampComponent_div_0_section_1_Template", "ctx_r0", "ctx_r19", "relatedProducts", "IndexRevampComponent_div_1_ng_container_4_app_floating_panel_9_Template_app_floating_panel_onItemLike_0_listener", "_r22", "ctx_r21", "ctx_r20", "IndexRevampComponent_div_1_ng_container_4_Template_app_details_onChangeVariant_7_listener", "_r24", "ctx_r23", "IndexRevampComponent_div_1_ng_container_4_Template_app_details_onItemLike_7_listener", "ctx_r25", "IndexRevampComponent_div_1_ng_container_4_div_8_Template", "IndexRevampComponent_div_1_ng_container_4_app_floating_panel_9_Template", "ctx_r18", "length", "showPanel", "IndexRevampComponent_div_1_ng_container_4_Template", "ctx_r1", "IndexRevampComponent", "onResize", "event", "window", "innerWidth", "updateZoomClass", "constructor", "store", "activatedRoute", "productService", "messageService", "router", "translate", "ref", "loaderService", "appDataService", "$gaService", "permissionService", "platformId", "$gtmService", "loading", "icon", "routerLink", "decimalValue", "zoomLevelClass", "isLayoutTemplate", "tagName", "isGoogleAnalytics", "isMobileLayout", "isMobile<PERSON>iew", "onScrollEvent", "_event", "sectionDetails", "document", "querySelector", "relatedProductsSection", "fromTop", "getBoundingClientRect", "top", "fromBottom", "initializeComponent", "paramMap", "subscribe", "params", "specProductId", "get", "ngOnInit", "initializeNavbar", "loadData", "addEventListener", "scrollToTopOnNavigation", "ngAfterViewInit", "subscribeToCurrency", "scrollToTop", "show", "getProductDetails", "next", "res", "hide", "data", "processProductDetails", "pushPageView", "categoryPath", "error", "err", "handleError", "complete", "subscribeToCategories", "variance", "hasPermission", "disableCent", "localStorage", "getItem", "value", "parseInt", "subscribeToRouteParams", "subscribeToRouterEvents", "layoutTemplate", "find", "section", "type", "events", "evt", "scrollTo", "setTimeout", "subscription", "getElementById", "scrollIntoView", "pipe", "userDetails", "trackGoogleAnalytics", "initializeProductSpecs", "processProductVariances", "updateSelectedVarianceDetails", "getT<PERSON>plateFields", "templateId", "assignBreadCrumbsData", "detectChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTagFeature", "pageView", "VIEW_ITEM", "categoryName", "product_ID", "user_ID", "mobileNumber", "productSpecs", "for<PERSON>ach", "spec", "specs", "productVariances", "calculateVarianceSalePercent", "varianceSpecs", "salePrice", "price", "salePriceValue", "variant", "isDefault", "color", "variantSpec", "console", "add", "severity", "summary", "instant", "detail", "message", "category", "element", "categoryId", "idsArray", "categoryIds", "split", "map", "Number", "slice", "nameArray", "String", "index", "label", "getAllTemplateFields", "success", "records", "zoomLevel", "screen", "availWidth", "_", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "ActivatedRoute", "ProductService", "i3", "MessageService", "Router", "i4", "TranslateService", "ChangeDetectorRef", "LoaderService", "AppDataService", "i5", "GoogleAnalyticsService", "PermissionService", "i6", "GTMService", "_2", "selectors", "hostBindings", "IndexRevampComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "IndexRevampComponent_div_0_Template", "IndexRevampComponent_div_1_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\index-revamp\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\index-revamp\\index.component.html"], "sourcesContent": ["import {AfterViewInit, ChangeDetectorRef, Component, HostListener, Inject, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, Router } from \"@angular/router\";\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { filter } from 'rxjs';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Product, Currency, Category } from \"@core/interface\";\r\n\r\nimport {\r\n  AddressService,\r\n  AppDataService,\r\n  LoaderService, PermissionService,\r\n  ProductService,\r\n  StoreService,\r\n} from '@core/services';\r\nimport { GaActionEnum, GoogleAnalyticsService } from 'ngx-google-analytics';\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n@Component({\r\n  selector: 'app-index-revamp',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexRevampComponent implements OnInit, AfterViewInit {\r\n  relatedProducts: Array<Product> = [];\r\n  currency: Currency = {} as Currency;\r\n  productDetails: any = {};\r\n  specProductId: any;\r\n  loading: boolean = false;\r\n  showPanel: boolean = false;\r\n  items: MenuItem[] = [];\r\n  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };\r\n  categoryId: any;\r\n  category!: Category;\r\n  selectedColor: any;\r\n  selectedSize: any;\r\n  selectedSize2: any;\r\n  selectedVariance: any;\r\n  name = 'alam';\r\n  token: any;\r\n  screenWidth: number = window.innerWidth;\r\n  decimalValue: number = 0;\r\n  disableCent: any;\r\n  zoomLevelClass: string = 'default-zoom';\r\n  navbarData: any;\r\n  isLayoutTemplate: boolean = false;\r\n  tagName:any=GaActionEnum;\r\n  cols: any =[];\r\n  userDetails: any;\r\n  isGoogleAnalytics: boolean = false;\r\n  isMobileLayout: boolean = false;\r\n  isMobileView: boolean =this.screenWidth <= 786;\r\n  channelId: any;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any): void {\r\n    this.screenWidth = window.innerWidth;\r\n    this.updateZoomClass();\r\n  }\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private productService: ProductService,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private translate: TranslateService,\r\n    private ref: ChangeDetectorRef,\r\n    private loaderService: LoaderService,\r\n    private appDataService: AppDataService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    private permissionService: PermissionService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n    private $gtmService:GTMService\r\n  ) {\r\n    this.initializeComponent();\r\n    this.activatedRoute.paramMap.subscribe(params => {\r\n      this.specProductId = params.get('id');\r\n      this.channelId = params.get('channelId');\r\n\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeNavbar();\r\n    this.loadData();\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.addEventListener('scroll', this.onScrollEvent, true);\r\n    }\r\n    this.scrollToTopOnNavigation();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.subscribeToCurrency();\r\n    this.scrollToTop();\r\n  }\r\n\r\n  onItemLiked(event: boolean): void {\r\n    this.loadData();\r\n  }\r\n\r\n  loadData(): void {\r\n    this.loading = true;\r\n    this.loaderService.show();\r\n    this.productService.getProductDetails(this.specProductId,this.channelId).subscribe({\r\n      next: (res: any) => {\r\n        this.loaderService.hide();\r\n        this.productDetails = res.data;\r\n        this.channelId = res.data?.channelId;\r\n        this.processProductDetails();\r\n        this.$gtmService.pushPageView(this.productDetails?.categoryPath,this.productDetails.name)\r\n      },\r\n      error: (err: any) => {\r\n        this.handleError(err);\r\n      },\r\n      complete: () => {\r\n        this.loaderService.hide();\r\n      },\r\n    });\r\n\r\n    this.subscribeToCategories();\r\n  }\r\n\r\n  onvVarinaceChange(variance: any): void {\r\n    this.selectedVariance = variance;\r\n  }\r\n\r\n  private initializeComponent(): void {\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n\r\n    this.disableCent = localStorage.getItem('disableCent');\r\n    const value = localStorage.getItem('decimalValue');\r\n    if (value) this.decimalValue = parseInt(value);\r\n    this.subscribeToRouteParams();\r\n    this.subscribeToRouterEvents();\r\n  }\r\n\r\n  private initializeNavbar(): void {\r\n    this.navbarData = this.appDataService.layoutTemplate.find(\r\n      (section: any) => section.type === 'navbar'\r\n    );\r\n  }\r\n\r\n  private scrollToTopOnNavigation(): void {\r\n    this.router.events.subscribe((evt) => {\r\n      if (evt instanceof NavigationEnd) {\r\n        window.scrollTo(0, 0);\r\n      }\r\n    });\r\n  }\r\n\r\n  private subscribeToCurrency(): void {\r\n    setTimeout(() => {\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n  }\r\n\r\n  private scrollToTop(): void {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const top = document.getElementById('top');\r\n      if (top) {\r\n        top.scrollIntoView();\r\n      }\r\n    }\r\n  }\r\n\r\n  private subscribeToRouteParams(): void {\r\n    this.activatedRoute.paramMap.subscribe((params) => {\r\n      this.specProductId = params.get('id');\r\n    });\r\n  }\r\n\r\n  private subscribeToRouterEvents(): void {\r\n    this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationEnd))\r\n      .subscribe();\r\n  }\r\n\r\n  private processProductDetails(): void {\r\n    if (this.productDetails) {\r\n      this.userDetails = this.store.get('profile');\r\n      this.trackGoogleAnalytics();\r\n      this.initializeProductSpecs();\r\n      this.processProductVariances();\r\n      this.updateSelectedVarianceDetails();\r\n      if(this.channelId == '1'){\r\n        this.getTemplateFields(this.productDetails.templateId);\r\n      }\r\n      this.assignBreadCrumbsData();\r\n      this.ref.detectChanges();\r\n      this.ref.markForCheck();\r\n    }\r\n    this.loading = false;\r\n  }\r\n\r\n  private trackGoogleAnalytics(): void {\r\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature('VIEW_ITEM')) {\r\n      this.$gaService.pageView('/product', `Product Detail: ${this.specProductId}`);\r\n      this.$gaService.event(\r\n        this.tagName.VIEW_ITEM,\r\n        this.productDetails.categoryName,\r\n        this.productDetails.name,\r\n        1,\r\n        true,\r\n        {\r\n          product_ID: this.productDetails.id,\r\n          user_ID: this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\r\n        }\r\n      );\r\n    }\r\n  }\r\n\r\n  private initializeProductSpecs(): void {\r\n    this.productDetails['specs'] = {};\r\n    this.productDetails.productSpecs?.forEach((spec: any) => {\r\n      this.productDetails.specs[spec.name] = spec;\r\n    });\r\n  }\r\n\r\n  private processProductVariances(): void {\r\n    this.productDetails.productVariances.forEach((variance: any) => {\r\n      this.calculateVarianceSalePercent(variance);\r\n      variance['specs'] = {};\r\n      variance.varianceSpecs?.forEach((spec: any) => {\r\n        variance['specs'][spec.name] = spec;\r\n      });\r\n    });\r\n  }\r\n\r\n  private calculateVarianceSalePercent(variance: any): void {\r\n    if (variance.salePrice) {\r\n      variance.salePercent = 100 - (variance.salePrice / variance.price) * 100;\r\n    } else if (variance.salePriceValue) {\r\n      variance.salePercent = 100 - (variance.salePriceValue / variance.price) * 100;\r\n    }\r\n  }\r\n\r\n  private updateSelectedVarianceDetails(): void {\r\n    this.selectedVariance =\r\n      this.productDetails.productVariances.find((variant: any) => variant.isDefault) ||\r\n      this.productDetails.productVariances.find((variant: any) => !variant.soldOut) ||\r\n      this.productDetails.productVariances[0];\r\n\r\n    this.selectedColor = this.selectedVariance.color;\r\n    this.selectedVariance.varianceSpecs?.forEach((variantSpec: any) => {\r\n      if (variantSpec.name === 'Size') {\r\n        this.selectedSize = variantSpec.value;\r\n      }\r\n      if (variantSpec.name === 'Size 2') {\r\n        this.selectedSize2 = variantSpec.value;\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleError(err: any): void {\r\n    this.loaderService.hide();\r\n    console.error(err);\r\n    this.messageService.add({\r\n      severity: 'error',\r\n      summary: this.translate.instant('ErrorMessages.fetchError'),\r\n      detail: err.message,\r\n    });\r\n    this.loading = false;\r\n  }\r\n\r\n  private subscribeToCategories(): void {\r\n    this.store.subscription('categories').subscribe({\r\n      next: (res: any) => {\r\n        this.category = res.find((element: Category) => element.id === this.categoryId);\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n  }\r\n\r\n  private assignBreadCrumbsData(): void {\r\n    const idsArray = this.productDetails?.categoryIds?.split('->').map(Number).slice(0, 3);\r\n    const nameArray = this.productDetails?.categoryPath?.split('->').map(String).slice(0, 3);\r\n    this.items = idsArray?.map((id:any, index:any) => ({\r\n      routerLink: `/category/${id}`,\r\n      label: nameArray[index],\r\n    })) || [];\r\n  }\r\n\r\n  private getTemplateFields(id: any): void {\r\n    this.productService.getAllTemplateFields(id).subscribe((res: any) => {\r\n      if (res.success) {\r\n        this.cols = res.data.records;\r\n      }\r\n    });\r\n  }\r\n\r\n  private updateZoomClass(): void {\r\n    const zoomLevel = (window.innerWidth / window.screen.availWidth) * 100;\r\n    if (zoomLevel <= 91) {\r\n      this.zoomLevelClass = 'zoom-110';\r\n    } else if (zoomLevel <= 112) {\r\n      this.zoomLevelClass = 'zoom-90';\r\n    } else if (zoomLevel <= 125) {\r\n      this.zoomLevelClass = 'zoom-80';\r\n    } else if (zoomLevel <= 134) {\r\n      this.zoomLevelClass = 'zoom-75';\r\n    } else if (zoomLevel <= 150) {\r\n      this.zoomLevelClass = 'zoom-67';\r\n    } else if (zoomLevel <= 200) {\r\n      this.zoomLevelClass = 'zoom-50';\r\n    } else if (zoomLevel <= 300) {\r\n      this.zoomLevelClass = 'zoom-33';\r\n    } else if (zoomLevel <= 400) {\r\n      this.zoomLevelClass = 'zoom-25';\r\n    } else {\r\n      this.zoomLevelClass = 'default-zoom';\r\n    }\r\n  }\r\n\r\n  onScrollEvent = (_event: any): void => {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const sectionDetails = document.querySelector('#details');\r\n      const relatedProductsSection = document.querySelector('#relatedProducts');\r\n      const fromTop = sectionDetails ? sectionDetails.getBoundingClientRect().top < -110 : false;\r\n      const fromBottom = relatedProductsSection ? relatedProductsSection.getBoundingClientRect().top < 550 : false;\r\n      this.showPanel = fromTop && !fromBottom;\r\n    }\r\n  };\r\n}\r\n", "<div *ngIf=\"isLayoutTemplate\" class=\"new-product-details\">\r\n  <section *ngIf=\"productDetails?.id\" [ngClass]=\"{'product-details__hidden-navbar':!navbarData?.isActive}\" class=\"product-details\"\r\n           id=\"top\">\r\n\r\n    <app-back-button *ngIf='screenWidth < 768'></app-back-button>\r\n\r\n    <div *ngIf=\"screenWidth > 768\" [ngClass]=\"{'breadcrumb':navbarData?.isActive,'hiddenNavbarBreadcrum':!navbarData?.isActive}\"\r\n         class=\"breadcrumb\">\r\n      <!-- <p-breadcrumb *ngIf=\"screenWidth < 768\" [home]=\"home\" [model]=\"items\" class=\"col-12\">\r\n        <ng-template pTemplate=\"item\" let-item>\r\n          <a class=\"cursor-pointer\" [routerLink]=\"item.url\">\r\n            <i [class]=\"item.icon\"></i>\r\n          </a>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"separator\">\r\n          <img src=\"assets/icons/breadcrum-icon.svg\" alt=\"breadcrum-icon\"/>\r\n        </ng-template>\r\n\r\n\r\n      </p-breadcrumb> -->\r\n      <p-breadcrumb *ngIf=\"screenWidth > 768\" [home]=\"home\" [model]=\"items\" class=\"col-12\">\r\n      </p-breadcrumb>\r\n    </div>\r\n\r\n\r\n    <div *ngIf='screenWidth < 768' class=\"seller-info-name\">\r\n      {{productDetails?.sellerName}}\r\n    </div>\r\n    <div *ngIf='screenWidth < 768' class=\"product-name\" data-placement=\"top\" data-toggle=\"tooltip\"\r\n         title=\"{{productDetails?.name}}\">\r\n      {{productDetails?.name}}\r\n    </div>\r\n    <div class=\"d-flex product-details__tag-section\" *ngIf='screenWidth < 768'>\r\n      <div *ngIf=\"selectedVariance.salePercent>0\"\r\n           class=\"d-inline-flex product-details__tag-section__green-label\">\r\n        {{selectedVariance?.salePercent?.toFixed(0)}}% {{\"productDetails.details.off\" | translate}}\r\n      </div>\r\n      <div *ngIf=\"selectedVariance?.soldOut\" class=\"d-inline-flex justify-content-normal product-details__tag-section__stock\">\r\n        {{\"productDetails.details.outOfStock\" | translate}}\r\n      </div>\r\n    </div>\r\n    <div  class=\"new-product-details\">\r\n      <ng-container *ngIf=\"productDetails?.id\">\r\n        <div [ngClass]=\"{'flex-column':screenWidth<=1200}\" class=\"d-flex flex-row\">\r\n          <div class=\"product-details__images-section\">\r\n            <app-product-images [product]=\"productDetails\" [variant]=\"selectedVariance\" [channelId]=\"channelId\"/>\r\n          </div>\r\n          <div class=\"product-details__details-section\">\r\n            <app-details (onChangeVariant)=\"onvVarinaceChange($event)\" (onItemLike)=\"onItemLiked($event)\"\r\n                         [currency]=\"currency\" [product]=\"productDetails\" [selectedColor]=\"selectedColor\"\r\n                         [selectedSize]=\"selectedSize\" [selectedSize2]=\"selectedSize2\" [selectedVariant]=\"selectedVariance\"  [cols]=\"cols\" id=\"details\" [channelId]=\"channelId\"/>\r\n          </div>\r\n        </div>\r\n        <app-floating-panel (onItemLike)=\"onItemLiked($event)\" *ngIf=\"screenWidth>1200\" [currency]=\"currency\"\r\n                            [product]=\"productDetails\" [variant]=\"selectedVariance\"></app-floating-panel>\r\n      </ng-container>\r\n    </div>\r\n  </section>\r\n</div>\r\n\r\n<div *ngIf=\"!isLayoutTemplate\" class=\"old-product-details\">\r\n  <section class=\"product-details\" id=\"top\">\r\n\r\n    <div class=\"breadcrumb\">\r\n      <p-breadcrumb [home]=\"home\" [model]=\"items\" class=\"col-12\"></p-breadcrumb>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"productDetails?.id\">\r\n      <div class=\"\">\r\n        <div class=\"grid pt-0\">\r\n          <div class=\"col-12 col-md-6 col-lg-7\">\r\n            <div class=\"images\">\r\n              <app-image-zoom [product]=\"productDetails\" [variant]=\"selectedVariance\"></app-image-zoom>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-12 col-md-8 col-lg-5\">\r\n            <app-details (onChangeVariant)=\"onvVarinaceChange($event)\" (onItemLike)=\"onItemLiked($event)\"\r\n                         [currency]=\"currency\" [product]=\"productDetails\" [selectedColor]=\"selectedColor\"\r\n                         [selectedSize]=\"selectedSize\" [selectedSize2]=\"selectedSize2\" [selectedVariant]=\"selectedVariance\" id=\"details\" [cols]=\"cols\" ></app-details>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"relatedProducts.length\" class=\"my-5\">\r\n          <app-mtn-section [title]=\"'Related Products'\" id=\"relatedProducts\">\r\n            <app-mtn-product-slider [products]=\"relatedProducts\"></app-mtn-product-slider>\r\n          </app-mtn-section>\r\n        </div>\r\n      </div>\r\n\r\n      <app-floating-panel (onItemLike)=\"onItemLiked($event)\" *ngIf=\"showPanel\" [currency]=\"currency\"\r\n                          [product]=\"productDetails\" [variant]=\"selectedVariance\"></app-floating-panel>\r\n    </ng-container>\r\n  </section>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAAmFA,WAAW,QAAO,eAAe;AACpH,SAAyBC,aAAa,QAAgB,iBAAiB;AAEvE,SAASC,MAAM,QAAQ,MAAM;AAW7B,SAASC,YAAY,QAAgC,sBAAsB;AAC3E,SAAQC,iBAAiB,QAAO,iBAAiB;;;;;;;;;;;;;;;;;;ICX7CC,EAAA,CAAAC,SAAA,sBAA6D;;;;;IAgB3DD,EAAA,CAAAC,SAAA,uBACe;;;;IADyBD,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa,UAAAD,MAAA,CAAAE,KAAA;;;;;;;;;;;IAdvDL,EAAA,CAAAM,cAAA,cACwB;IAatBN,EAAA,CAAAO,UAAA,IAAAC,kEAAA,2BACe;IACjBR,EAAA,CAAAS,YAAA,EAAM;;;;IAhByBT,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAU,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,IAAAF,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,GAA6F;IAc3Gd,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAU,MAAA,CAAAI,WAAA,OAAuB;;;;;IAKxChB,EAAA,CAAAM,cAAA,cAAwD;IACtDN,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAkB,kBAAA,MAAAC,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAC,UAAA,MACF;;;;;IACArB,EAAA,CAAAM,cAAA,cACsC;IACpCN,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAS,YAAA,EAAM;;;;IAFDT,EAAA,CAAAsB,qBAAA,UAAAC,MAAA,CAAAH,cAAA,kBAAAG,MAAA,CAAAH,cAAA,CAAAI,IAAA,CAAgC;IACnCxB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAkB,kBAAA,MAAAK,MAAA,CAAAH,cAAA,kBAAAG,MAAA,CAAAH,cAAA,CAAAI,IAAA,MACF;;;;;IAEExB,EAAA,CAAAM,cAAA,cACqE;IACnEN,EAAA,CAAAiB,MAAA,GACF;;IAAAjB,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAyB,kBAAA,MAAAC,OAAA,CAAAC,gBAAA,kBAAAD,OAAA,CAAAC,gBAAA,CAAAC,WAAA,kBAAAF,OAAA,CAAAC,gBAAA,CAAAC,WAAA,CAAAC,OAAA,WAAA7B,EAAA,CAAA8B,WAAA,0CACF;;;;;IACA9B,EAAA,CAAAM,cAAA,cAAwH;IACtHN,EAAA,CAAAiB,MAAA,GACF;;IAAAjB,EAAA,CAAAS,YAAA,EAAM;;;IADJT,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA8B,WAAA,iDACF;;;;;IAPF9B,EAAA,CAAAM,cAAA,cAA2E;IACzEN,EAAA,CAAAO,UAAA,IAAAwB,yDAAA,kBAGM;IACN/B,EAAA,CAAAO,UAAA,IAAAyB,yDAAA,kBAEM;IACRhC,EAAA,CAAAS,YAAA,EAAM;;;;IAPET,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAE,UAAA,SAAA+B,MAAA,CAAAN,gBAAA,CAAAC,WAAA,KAAoC;IAIpC5B,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAE,UAAA,SAAA+B,MAAA,CAAAN,gBAAA,kBAAAM,MAAA,CAAAN,gBAAA,CAAAO,OAAA,CAA+B;;;;;;IAgBnClC,EAAA,CAAAM,cAAA,6BAC4E;IADxDN,EAAA,CAAAmC,UAAA,wBAAAC,2HAAAC,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAyC,aAAA;MAAA,OAAczC,EAAA,CAAA0C,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IACsBrC,EAAA,CAAAS,YAAA,EAAqB;;;;IADjBT,EAAA,CAAAE,UAAA,aAAA0C,OAAA,CAAAC,QAAA,CAAqB,YAAAD,OAAA,CAAAxB,cAAA,aAAAwB,OAAA,CAAAjB,gBAAA;;;;;;;;;;;IAXvG3B,EAAA,CAAA8C,uBAAA,GAAyC;IACvC9C,EAAA,CAAAM,cAAA,cAA2E;IAEvEN,EAAA,CAAAC,SAAA,6BAAqG;IACvGD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAAM,cAAA,cAA8C;IAC/BN,EAAA,CAAAmC,UAAA,6BAAAY,oGAAAV,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAU,IAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAAyC,aAAA;MAAA,OAAmBzC,EAAA,CAAA0C,WAAA,CAAAO,OAAA,CAAAC,iBAAA,CAAAb,MAAA,CAAyB;IAAA,EAAC,wBAAAc,+FAAAd,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAU,IAAA;MAAA,MAAAI,OAAA,GAAApD,EAAA,CAAAyC,aAAA;MAAA,OAAezC,EAAA,CAAA0C,WAAA,CAAAU,OAAA,CAAAT,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAlC;IAA1DrC,EAAA,CAAAS,YAAA,EAEqK;IAGzKT,EAAA,CAAAO,UAAA,IAAA8C,iFAAA,iCACiG;IACnGrD,EAAA,CAAAsD,qBAAA,EAAe;;;;IAZRtD,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAuD,eAAA,KAAAC,GAAA,EAAAC,MAAA,CAAAzC,WAAA,UAA6C;IAE1BhB,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAE,UAAA,YAAAuD,MAAA,CAAArC,cAAA,CAA0B,YAAAqC,MAAA,CAAA9B,gBAAA,eAAA8B,MAAA,CAAAC,SAAA;IAIjC1D,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAE,UAAA,aAAAuD,MAAA,CAAAZ,QAAA,CAAqB,YAAAY,MAAA,CAAArC,cAAA,mBAAAqC,MAAA,CAAAE,aAAA,kBAAAF,MAAA,CAAAG,YAAA,mBAAAH,MAAA,CAAAI,aAAA,qBAAAJ,MAAA,CAAA9B,gBAAA,UAAA8B,MAAA,CAAAK,IAAA,eAAAL,MAAA,CAAAC,SAAA;IAIkB1D,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAE,UAAA,SAAAuD,MAAA,CAAAzC,WAAA,QAAsB;;;;;;;;;;IApDpFhB,EAAA,CAAAM,cAAA,iBACkB;IAEhBN,EAAA,CAAAO,UAAA,IAAAwD,+DAAA,6BAA6D;IAE7D/D,EAAA,CAAAO,UAAA,IAAAyD,mDAAA,iBAgBM;IAGNhE,EAAA,CAAAO,UAAA,IAAA0D,mDAAA,iBAEM;IACNjE,EAAA,CAAAO,UAAA,IAAA2D,mDAAA,iBAGM;IACNlE,EAAA,CAAAO,UAAA,IAAA4D,mDAAA,iBAQM;IACNnE,EAAA,CAAAM,cAAA,aAAkC;IAChCN,EAAA,CAAAO,UAAA,IAAA6D,4DAAA,2BAae;IACjBpE,EAAA,CAAAS,YAAA,EAAM;;;;IAvD4BT,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAuD,eAAA,IAAAc,GAAA,IAAAC,MAAA,CAAAzD,UAAA,kBAAAyD,MAAA,CAAAzD,UAAA,CAAAC,QAAA,GAAoE;IAGpFd,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAtD,WAAA,OAAuB;IAEnChB,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAtD,WAAA,OAAuB;IAmBvBhB,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAtD,WAAA,OAAuB;IAGvBhB,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAtD,WAAA,OAAuB;IAIqBhB,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAtD,WAAA,OAAuB;IAUxDhB,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAE,UAAA,SAAAoE,MAAA,CAAAlD,cAAA,kBAAAkD,MAAA,CAAAlD,cAAA,CAAAmD,EAAA,CAAwB;;;;;IA1C7CvE,EAAA,CAAAM,cAAA,aAA0D;IACxDN,EAAA,CAAAO,UAAA,IAAAiE,6CAAA,qBAwDU;IACZxE,EAAA,CAAAS,YAAA,EAAM;;;;IAzDMT,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAE,UAAA,SAAAuE,MAAA,CAAArD,cAAA,kBAAAqD,MAAA,CAAArD,cAAA,CAAAmD,EAAA,CAAwB;;;;;IAgF5BvE,EAAA,CAAAM,cAAA,cAAiD;IAE7CN,EAAA,CAAAC,SAAA,iCAA8E;IAChFD,EAAA,CAAAS,YAAA,EAAkB;;;;IAFDT,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAE,UAAA,6BAA4B;IACnBF,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAE,UAAA,aAAAwE,OAAA,CAAAC,eAAA,CAA4B;;;;;;IAK1D3E,EAAA,CAAAM,cAAA,6BAC4E;IADxDN,EAAA,CAAAmC,UAAA,wBAAAyC,iHAAAvC,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAyC,aAAA;MAAA,OAAczC,EAAA,CAAA0C,WAAA,CAAAoC,OAAA,CAAAnC,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IACsBrC,EAAA,CAAAS,YAAA,EAAqB;;;;IADxBT,EAAA,CAAAE,UAAA,aAAA6E,OAAA,CAAAlC,QAAA,CAAqB,YAAAkC,OAAA,CAAA3D,cAAA,aAAA2D,OAAA,CAAApD,gBAAA;;;;;;IArBhG3B,EAAA,CAAA8C,uBAAA,GAAyC;IACvC9C,EAAA,CAAAM,cAAA,cAAc;IAINN,EAAA,CAAAC,SAAA,yBAAyF;IAC3FD,EAAA,CAAAS,YAAA,EAAM;IAERT,EAAA,CAAAM,cAAA,cAAsC;IACvBN,EAAA,CAAAmC,UAAA,6BAAA6C,0FAAA3C,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAA2C,IAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAAyC,aAAA;MAAA,OAAmBzC,EAAA,CAAA0C,WAAA,CAAAwC,OAAA,CAAAhC,iBAAA,CAAAb,MAAA,CAAyB;IAAA,EAAC,wBAAA8C,qFAAA9C,MAAA;MAAArC,EAAA,CAAAsC,aAAA,CAAA2C,IAAA;MAAA,MAAAG,OAAA,GAAApF,EAAA,CAAAyC,aAAA;MAAA,OAAezC,EAAA,CAAA0C,WAAA,CAAA0C,OAAA,CAAAzC,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAlC;IAEkFrC,EAAA,CAAAS,YAAA,EAAc;IAG9JT,EAAA,CAAAO,UAAA,IAAA8E,wDAAA,kBAIM;IACRrF,EAAA,CAAAS,YAAA,EAAM;IAENT,EAAA,CAAAO,UAAA,IAAA+E,uEAAA,iCACiG;IACnGtF,EAAA,CAAAsD,qBAAA,EAAe;;;;IAlBWtD,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAE,UAAA,YAAAqF,OAAA,CAAAnE,cAAA,CAA0B,YAAAmE,OAAA,CAAA5D,gBAAA;IAK/B3B,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAE,UAAA,aAAAqF,OAAA,CAAA1C,QAAA,CAAqB,YAAA0C,OAAA,CAAAnE,cAAA,mBAAAmE,OAAA,CAAA5B,aAAA,kBAAA4B,OAAA,CAAA3B,YAAA,mBAAA2B,OAAA,CAAA1B,aAAA,qBAAA0B,OAAA,CAAA5D,gBAAA,UAAA4D,OAAA,CAAAzB,IAAA;IAIhC9D,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAE,UAAA,SAAAqF,OAAA,CAAAZ,eAAA,CAAAa,MAAA,CAA4B;IAOoBxF,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAE,UAAA,SAAAqF,OAAA,CAAAE,SAAA,CAAe;;;;;IA5B7EzF,EAAA,CAAAM,cAAA,cAA2D;IAIrDN,EAAA,CAAAC,SAAA,uBAA0E;IAC5ED,EAAA,CAAAS,YAAA,EAAM;IAENT,EAAA,CAAAO,UAAA,IAAAmF,kDAAA,4BAuBe;IACjB1F,EAAA,CAAAS,YAAA,EAAU;;;;IA3BQT,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAE,UAAA,SAAAyF,MAAA,CAAAvF,IAAA,CAAa,UAAAuF,MAAA,CAAAtF,KAAA;IAGdL,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAE,UAAA,SAAAyF,MAAA,CAAAvE,cAAA,kBAAAuE,MAAA,CAAAvE,cAAA,CAAAmD,EAAA,CAAwB;;;AD5C3C,OAAM,MAAOqB,oBAAoB;EAgC/BC,QAAQA,CAACC,KAAW;IAClB,IAAI,CAAC9E,WAAW,GAAG+E,MAAM,CAACC,UAAU;IACpC,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,YACUC,KAAmB,EACnBC,cAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B,EAC9BC,MAAc,EACdC,SAA2B,EAC3BC,GAAsB,EACtBC,aAA4B,EAC5BC,cAA8B,EAC9BC,UAAkC,EAClCC,iBAAoC,EACfC,UAAe,EACpCC,WAAsB;IAZtB,KAAAZ,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACI,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,WAAW,GAAXA,WAAW;IAjDrB,KAAApC,eAAe,GAAmB,EAAE;IACpC,KAAA9B,QAAQ,GAAa,EAAc;IACnC,KAAAzB,cAAc,GAAQ,EAAE;IAExB,KAAA4F,OAAO,GAAY,KAAK;IACxB,KAAAvB,SAAS,GAAY,KAAK;IAC1B,KAAApF,KAAK,GAAe,EAAE;IACtB,KAAAD,IAAI,GAAa;MAAE6G,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAOxD,KAAA1F,IAAI,GAAG,MAAM;IAEb,KAAAR,WAAW,GAAW+E,MAAM,CAACC,UAAU;IACvC,KAAAmB,YAAY,GAAW,CAAC;IAExB,KAAAC,cAAc,GAAW,cAAc;IAEvC,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,OAAO,GAAKxH,YAAY;IACxB,KAAAgE,IAAI,GAAO,EAAE;IAEb,KAAAyD,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,YAAY,GAAW,IAAI,CAACzG,WAAW,IAAI,GAAG;IA6Q9C,KAAA0G,aAAa,GAAIC,MAAW,IAAU;MACpC,IAAI5H,iBAAiB,CAAC,IAAI,CAAC+G,UAAU,CAAC,EAAE;QACtC,MAAMc,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;QACzD,MAAMC,sBAAsB,GAAGF,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;QACzE,MAAME,OAAO,GAAGJ,cAAc,GAAGA,cAAc,CAACK,qBAAqB,EAAE,CAACC,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK;QAC1F,MAAMC,UAAU,GAAGJ,sBAAsB,GAAGA,sBAAsB,CAACE,qBAAqB,EAAE,CAACC,GAAG,GAAG,GAAG,GAAG,KAAK;QAC5G,IAAI,CAACzC,SAAS,GAAGuC,OAAO,IAAI,CAACG,UAAU;;IAE3C,CAAC;IA7PC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAChC,cAAc,CAACiC,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MAC9C,IAAI,CAACC,aAAa,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;MACrC,IAAI,CAAC/E,SAAS,GAAG6E,MAAM,CAACE,GAAG,CAAC,WAAW,CAAC;IAE1C,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI7I,iBAAiB,CAAC,IAAI,CAAC+G,UAAU,CAAC,EAAE;MACtCf,MAAM,CAAC8C,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACnB,aAAa,EAAE,IAAI,CAAC;;IAE7D,IAAI,CAACoB,uBAAuB,EAAE;EAChC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAtG,WAAWA,CAACmD,KAAc;IACxB,IAAI,CAAC8C,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IACN,IAAI,CAAC5B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACN,aAAa,CAACwC,IAAI,EAAE;IACzB,IAAI,CAAC7C,cAAc,CAAC8C,iBAAiB,CAAC,IAAI,CAACX,aAAa,EAAC,IAAI,CAAC9E,SAAS,CAAC,CAAC4E,SAAS,CAAC;MACjFc,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC3C,aAAa,CAAC4C,IAAI,EAAE;QACzB,IAAI,CAAClI,cAAc,GAAGiI,GAAG,CAACE,IAAI;QAC9B,IAAI,CAAC7F,SAAS,GAAG2F,GAAG,CAACE,IAAI,EAAE7F,SAAS;QACpC,IAAI,CAAC8F,qBAAqB,EAAE;QAC5B,IAAI,CAACzC,WAAW,CAAC0C,YAAY,CAAC,IAAI,CAACrI,cAAc,EAAEsI,YAAY,EAAC,IAAI,CAACtI,cAAc,CAACI,IAAI,CAAC;MAC3F,CAAC;MACDmI,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACC,WAAW,CAACD,GAAG,CAAC;MACvB,CAAC;MACDE,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACpD,aAAa,CAAC4C,IAAI,EAAE;MAC3B;KACD,CAAC;IAEF,IAAI,CAACS,qBAAqB,EAAE;EAC9B;EAEA7G,iBAAiBA,CAAC8G,QAAa;IAC7B,IAAI,CAACrI,gBAAgB,GAAGqI,QAAQ;EAClC;EAEQ5B,mBAAmBA,CAAA;IACzB,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACR,iBAAiB,CAACoD,aAAa,CAAC,iBAAiB,CAAC;IAC/E,IAAI,CAAC1C,iBAAiB,GAAG,IAAI,CAACV,iBAAiB,CAACoD,aAAa,CAAC,kBAAkB,CAAC;IACjF,IAAI,CAACzC,cAAc,GAAG,IAAI,CAACX,iBAAiB,CAACoD,aAAa,CAAC,eAAe,CAAC;IAE3E,IAAI,CAACC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACtD,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,IAAIC,KAAK,EAAE,IAAI,CAAClD,YAAY,GAAGmD,QAAQ,CAACD,KAAK,CAAC;IAC9C,IAAI,CAACE,sBAAsB,EAAE;IAC7B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEQ7B,gBAAgBA,CAAA;IACtB,IAAI,CAAC9H,UAAU,GAAG,IAAI,CAAC8F,cAAc,CAAC8D,cAAc,CAACC,IAAI,CACtDC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAC5C;EACH;EAEQ9B,uBAAuBA,CAAA;IAC7B,IAAI,CAACvC,MAAM,CAACsE,MAAM,CAACvC,SAAS,CAAEwC,GAAG,IAAI;MACnC,IAAIA,GAAG,YAAYlL,aAAa,EAAE;QAChCmG,MAAM,CAACgF,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEzB,CAAC,CAAC;EACJ;EAEQ/B,mBAAmBA,CAAA;IACzBgC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7E,KAAK,CAAC8E,YAAY,CAAC,UAAU,CAAC,CAAC3C,SAAS,CAAC;QAC5Cc,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACxG,QAAQ,GAAGwG;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;EACR;EAEQJ,WAAWA,CAAA;IACjB,IAAIlJ,iBAAiB,CAAC,IAAI,CAAC+G,UAAU,CAAC,EAAE;MACtC,MAAMoB,GAAG,GAAGL,QAAQ,CAACqD,cAAc,CAAC,KAAK,CAAC;MAC1C,IAAIhD,GAAG,EAAE;QACPA,GAAG,CAACiD,cAAc,EAAE;;;EAG1B;EAEQZ,sBAAsBA,CAAA;IAC5B,IAAI,CAACnE,cAAc,CAACiC,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MAChD,IAAI,CAACC,aAAa,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;IACvC,CAAC,CAAC;EACJ;EAEQ+B,uBAAuBA,CAAA;IAC7B,IAAI,CAACjE,MAAM,CAACsE,MAAM,CACfO,IAAI,CAACvL,MAAM,CAAEiG,KAAK,IAAKA,KAAK,YAAYlG,aAAa,CAAC,CAAC,CACvD0I,SAAS,EAAE;EAChB;EAEQkB,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACpI,cAAc,EAAE;MACvB,IAAI,CAACiK,WAAW,GAAG,IAAI,CAAClF,KAAK,CAACsC,GAAG,CAAC,SAAS,CAAC;MAC5C,IAAI,CAAC6C,oBAAoB,EAAE;MAC3B,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACC,uBAAuB,EAAE;MAC9B,IAAI,CAACC,6BAA6B,EAAE;MACpC,IAAG,IAAI,CAAC/H,SAAS,IAAI,GAAG,EAAC;QACvB,IAAI,CAACgI,iBAAiB,CAAC,IAAI,CAACtK,cAAc,CAACuK,UAAU,CAAC;;MAExD,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACnF,GAAG,CAACoF,aAAa,EAAE;MACxB,IAAI,CAACpF,GAAG,CAACqF,YAAY,EAAE;;IAEzB,IAAI,CAAC9E,OAAO,GAAG,KAAK;EACtB;EAEQsE,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAAC/D,iBAAiB,IAAI,IAAI,CAACV,iBAAiB,CAACkF,aAAa,CAAC,WAAW,CAAC,EAAE;MAC/E,IAAI,CAACnF,UAAU,CAACoF,QAAQ,CAAC,UAAU,EAAE,mBAAmB,IAAI,CAACxD,aAAa,EAAE,CAAC;MAC7E,IAAI,CAAC5B,UAAU,CAACd,KAAK,CACnB,IAAI,CAACwB,OAAO,CAAC2E,SAAS,EACtB,IAAI,CAAC7K,cAAc,CAAC8K,YAAY,EAChC,IAAI,CAAC9K,cAAc,CAACI,IAAI,EACxB,CAAC,EACD,IAAI,EACJ;QACE2K,UAAU,EAAE,IAAI,CAAC/K,cAAc,CAACmD,EAAE;QAClC6H,OAAO,EAAE,IAAI,CAACf,WAAW,GAAG,IAAI,CAACA,WAAW,CAACgB,YAAY,GAAG;OAC7D,CACF;;EAEL;EAEQd,sBAAsBA,CAAA;IAC5B,IAAI,CAACnK,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE;IACjC,IAAI,CAACA,cAAc,CAACkL,YAAY,EAAEC,OAAO,CAAEC,IAAS,IAAI;MACtD,IAAI,CAACpL,cAAc,CAACqL,KAAK,CAACD,IAAI,CAAChL,IAAI,CAAC,GAAGgL,IAAI;IAC7C,CAAC,CAAC;EACJ;EAEQhB,uBAAuBA,CAAA;IAC7B,IAAI,CAACpK,cAAc,CAACsL,gBAAgB,CAACH,OAAO,CAAEvC,QAAa,IAAI;MAC7D,IAAI,CAAC2C,4BAA4B,CAAC3C,QAAQ,CAAC;MAC3CA,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE;MACtBA,QAAQ,CAAC4C,aAAa,EAAEL,OAAO,CAAEC,IAAS,IAAI;QAC5CxC,QAAQ,CAAC,OAAO,CAAC,CAACwC,IAAI,CAAChL,IAAI,CAAC,GAAGgL,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQG,4BAA4BA,CAAC3C,QAAa;IAChD,IAAIA,QAAQ,CAAC6C,SAAS,EAAE;MACtB7C,QAAQ,CAACpI,WAAW,GAAG,GAAG,GAAIoI,QAAQ,CAAC6C,SAAS,GAAG7C,QAAQ,CAAC8C,KAAK,GAAI,GAAG;KACzE,MAAM,IAAI9C,QAAQ,CAAC+C,cAAc,EAAE;MAClC/C,QAAQ,CAACpI,WAAW,GAAG,GAAG,GAAIoI,QAAQ,CAAC+C,cAAc,GAAG/C,QAAQ,CAAC8C,KAAK,GAAI,GAAG;;EAEjF;EAEQrB,6BAA6BA,CAAA;IACnC,IAAI,CAAC9J,gBAAgB,GACnB,IAAI,CAACP,cAAc,CAACsL,gBAAgB,CAAChC,IAAI,CAAEsC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC,IAC9E,IAAI,CAAC7L,cAAc,CAACsL,gBAAgB,CAAChC,IAAI,CAAEsC,OAAY,IAAK,CAACA,OAAO,CAAC9K,OAAO,CAAC,IAC7E,IAAI,CAACd,cAAc,CAACsL,gBAAgB,CAAC,CAAC,CAAC;IAEzC,IAAI,CAAC/I,aAAa,GAAG,IAAI,CAAChC,gBAAgB,CAACuL,KAAK;IAChD,IAAI,CAACvL,gBAAgB,CAACiL,aAAa,EAAEL,OAAO,CAAEY,WAAgB,IAAI;MAChE,IAAIA,WAAW,CAAC3L,IAAI,KAAK,MAAM,EAAE;QAC/B,IAAI,CAACoC,YAAY,GAAGuJ,WAAW,CAAC9C,KAAK;;MAEvC,IAAI8C,WAAW,CAAC3L,IAAI,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACqC,aAAa,GAAGsJ,WAAW,CAAC9C,KAAK;;IAE1C,CAAC,CAAC;EACJ;EAEQR,WAAWA,CAACD,GAAQ;IAC1B,IAAI,CAAClD,aAAa,CAAC4C,IAAI,EAAE;IACzB8D,OAAO,CAACzD,KAAK,CAACC,GAAG,CAAC;IAClB,IAAI,CAACtD,cAAc,CAAC+G,GAAG,CAAC;MACtBC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,IAAI,CAAC/G,SAAS,CAACgH,OAAO,CAAC,0BAA0B,CAAC;MAC3DC,MAAM,EAAE7D,GAAG,CAAC8D;KACb,CAAC;IACF,IAAI,CAAC1G,OAAO,GAAG,KAAK;EACtB;EAEQ+C,qBAAqBA,CAAA;IAC3B,IAAI,CAAC5D,KAAK,CAAC8E,YAAY,CAAC,YAAY,CAAC,CAAC3C,SAAS,CAAC;MAC9Cc,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACsE,QAAQ,GAAGtE,GAAG,CAACqB,IAAI,CAAEkD,OAAiB,IAAKA,OAAO,CAACrJ,EAAE,KAAK,IAAI,CAACsJ,UAAU,CAAC;MACjF,CAAC;MACDlE,KAAK,EAAGC,GAAQ,IAAI;QAClBwD,OAAO,CAACzD,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEQgC,qBAAqBA,CAAA;IAC3B,MAAMkC,QAAQ,GAAG,IAAI,CAAC1M,cAAc,EAAE2M,WAAW,EAAEC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACtF,MAAMC,SAAS,GAAG,IAAI,CAAChN,cAAc,EAAEsI,YAAY,EAAEsE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACI,MAAM,CAAC,CAACF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACxF,IAAI,CAAC9N,KAAK,GAAGyN,QAAQ,EAAEG,GAAG,CAAC,CAAC1J,EAAM,EAAE+J,KAAS,MAAM;MACjDpH,UAAU,EAAE,aAAa3C,EAAE,EAAE;MAC7BgK,KAAK,EAAEH,SAAS,CAACE,KAAK;KACvB,CAAC,CAAC,IAAI,EAAE;EACX;EAEQ5C,iBAAiBA,CAACnH,EAAO;IAC/B,IAAI,CAAC8B,cAAc,CAACmI,oBAAoB,CAACjK,EAAE,CAAC,CAAC+D,SAAS,CAAEe,GAAQ,IAAI;MAClE,IAAIA,GAAG,CAACoF,OAAO,EAAE;QACf,IAAI,CAAC3K,IAAI,GAAGuF,GAAG,CAACE,IAAI,CAACmF,OAAO;;IAEhC,CAAC,CAAC;EACJ;EAEQzI,eAAeA,CAAA;IACrB,MAAM0I,SAAS,GAAI5I,MAAM,CAACC,UAAU,GAAGD,MAAM,CAAC6I,MAAM,CAACC,UAAU,GAAI,GAAG;IACtE,IAAIF,SAAS,IAAI,EAAE,EAAE;MACnB,IAAI,CAACvH,cAAc,GAAG,UAAU;KACjC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM,IAAIuH,SAAS,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACvH,cAAc,GAAG,SAAS;KAChC,MAAM;MACL,IAAI,CAACA,cAAc,GAAG,cAAc;;EAExC;EAAC,QAAA0H,CAAA,G;qBAvSUlJ,oBAAoB,EAAA5F,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAjP,EAAA,CAAA+O,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnP,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAApP,EAAA,CAAA+O,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAtP,EAAA,CAAA+O,iBAAA,CAAAG,EAAA,CAAAK,MAAA,GAAAvP,EAAA,CAAA+O,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAzP,EAAA,CAAA+O,iBAAA,CAAA/O,EAAA,CAAA0P,iBAAA,GAAA1P,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAW,aAAA,GAAA3P,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAY,cAAA,GAAA5P,EAAA,CAAA+O,iBAAA,CAAAc,EAAA,CAAAC,sBAAA,GAAA9P,EAAA,CAAA+O,iBAAA,CAAAC,EAAA,CAAAe,iBAAA,GAAA/P,EAAA,CAAA+O,iBAAA,CAiDrBpP,WAAW,GAAAK,EAAA,CAAA+O,iBAAA,CAAAiB,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAjDVtK,oBAAoB;IAAAuK,SAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAA1K,QAAA,CAAAxD,MAAA,CAAgB;QAAA,UAAArC,EAAA,CAAAwQ,eAAA;;;;;;;;QCvB7BxQ,EAAA,CAAAO,UAAA,IAAAkQ,mCAAA,iBA0DM;QAENzQ,EAAA,CAAAO,UAAA,IAAAmQ,mCAAA,iBAiCM;;;QA7FA1Q,EAAA,CAAAE,UAAA,SAAAqQ,GAAA,CAAAlJ,gBAAA,CAAsB;QA4DtBrH,EAAA,CAAAe,SAAA,GAAuB;QAAvBf,EAAA,CAAAE,UAAA,UAAAqQ,GAAA,CAAAlJ,gBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}