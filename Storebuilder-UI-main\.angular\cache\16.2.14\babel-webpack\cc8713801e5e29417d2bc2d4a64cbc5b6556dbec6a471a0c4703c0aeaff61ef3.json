{"ast": null, "code": "import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  constructor(_platform, _ngZone) {\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._monitoredElements = new Map();\n  }\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const result = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('animationstart', listener, listenerOptions);\n      element.classList.add('cdk-text-field-autofill-monitored');\n    });\n    this._monitoredElements.set(element, {\n      subject: result,\n      unlisten: () => {\n        element.removeEventListener('animationstart', listener, listenerOptions);\n      }\n    });\n    return result;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  static #_ = this.ɵfac = function AutofillMonitor_Factory(t) {\n    return new (t || AutofillMonitor)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutofillMonitor,\n    factory: AutofillMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  constructor(_elementRef, _autofillMonitor) {\n    this._elementRef = _elementRef;\n    this._autofillMonitor = _autofillMonitor;\n    /** Emits when the autofill state of the element changes. */\n    this.cdkAutofill = new EventEmitter();\n  }\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n  static #_ = this.ɵfac = function CdkAutofill_Factory(t) {\n    return new (t || CdkAutofill)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(AutofillMonitor));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAutofill,\n    selectors: [[\"\", \"cdkAutofill\", \"\"]],\n    outputs: {\n      cdkAutofill: \"cdkAutofill\"\n    }\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: AutofillMonitor\n    }];\n  }, {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    value = coerceBooleanProperty(value);\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  constructor(_elementRef, _platform, _ngZone, /** @breaking-change 11.0.0 make document required */\n  document) {\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._destroyed = new Subject();\n    this._enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    this._previousMinRows = -1;\n    this._isViewInited = false;\n    /** Handles `focus` and `blur` events. */\n    this._handleFocusEvent = event => {\n      this._hasFocus = event.type === 'focus';\n    };\n    this._document = document;\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        fromEvent(window, 'resize').pipe(auditTime(16), takeUntil(this._destroyed)).subscribe(() => this.resizeToFitContent(true));\n        this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n    this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    let textareaClone = this._textareaElement.cloneNode(false);\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n  static #_ = this.ɵfac = function CdkTextareaAutosize_Factory(t) {\n    return new (t || CdkTextareaAutosize)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTextareaAutosize,\n    selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n    hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n    hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n          return ctx._noopInputHandler();\n        });\n      }\n    },\n    inputs: {\n      minRows: [\"cdkAutosizeMinRows\", \"minRows\"],\n      maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"],\n      enabled: [\"cdkTextareaAutosize\", \"enabled\"],\n      placeholder: \"placeholder\"\n    },\n    exportAs: [\"cdkTextareaAutosize\"]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: ['cdkTextareaAutosize']\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {\n  static #_ = this.ɵfac = function TextFieldModule_Factory(t) {\n    return new (t || TextFieldModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TextFieldModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };", "map": {"version": 3, "names": ["i1", "normalizePassiveListenerOptions", "i0", "Injectable", "EventEmitter", "Directive", "Output", "Optional", "Inject", "Input", "NgModule", "coerceElement", "coerceNumberProperty", "coerceBooleanProperty", "EMPTY", "Subject", "fromEvent", "auditTime", "takeUntil", "DOCUMENT", "listenerOptions", "passive", "AutofillMonitor", "constructor", "_platform", "_ngZone", "_monitoredElements", "Map", "monitor", "elementOrRef", "<PERSON><PERSON><PERSON><PERSON>", "element", "info", "get", "subject", "result", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "run", "next", "target", "isAutofilled", "remove", "runOutsideAngular", "addEventListener", "set", "unlisten", "removeEventListener", "stopMonitoring", "complete", "delete", "ngOnDestroy", "for<PERSON>ach", "_info", "_", "ɵfac", "AutofillMonitor_Factory", "t", "ɵɵinject", "Platform", "NgZone", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "CdkAutofill", "_elementRef", "_autofillMonitor", "cdkAutofill", "ngOnInit", "subscribe", "emit", "CdkAutofill_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "selector", "CdkTextareaAutosize", "minRows", "_minRows", "value", "_setMinHeight", "maxRows", "_maxRows", "_setMaxHeight", "enabled", "_enabled", "resizeToFitContent", "reset", "placeholder", "_textareaElement", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "document", "_destroyed", "_previousMinRows", "_isViewInited", "_handleFocusEvent", "_hasFocus", "_document", "nativeElement", "minHeight", "_cachedLineHeight", "style", "maxHeight", "ngAfterViewInit", "_initialHeight", "height", "window", "_getWindow", "pipe", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "rows", "position", "visibility", "border", "padding", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "textarea", "_previousValue", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "_getDocument", "doc", "defaultView", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "CdkTextareaAutosize_Factory", "hostAttrs", "hostBindings", "CdkTextareaAutosize_HostBindings", "rf", "ctx", "ɵɵlistener", "CdkTextareaAutosize_input_HostBindingHandler", "inputs", "exportAs", "host", "decorators", "TextFieldModule", "TextFieldModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@angular/cdk/fesm2022/text-field.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    constructor(_platform, _ngZone) {\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._monitoredElements = new Map();\n    }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const result = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = ((event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: false }));\n            }\n        });\n        this._ngZone.runOutsideAngular(() => {\n            element.addEventListener('animationstart', listener, listenerOptions);\n            element.classList.add('cdk-text-field-autofill-monitored');\n        });\n        this._monitoredElements.set(element, {\n            subject: result,\n            unlisten: () => {\n                element.removeEventListener('animationstart', listener, listenerOptions);\n            },\n        });\n        return result;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AutofillMonitor, deps: [{ token: i1.Platform }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }]; } });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    constructor(_elementRef, _autofillMonitor) {\n        this._elementRef = _elementRef;\n        this._autofillMonitor = _autofillMonitor;\n        /** Emits when the autofill state of the element changes. */\n        this.cdkAutofill = new EventEmitter();\n    }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAutofill, deps: [{ token: i0.ElementRef }, { token: AutofillMonitor }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkAutofill, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: AutofillMonitor }]; }, propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        value = coerceBooleanProperty(value);\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    constructor(_elementRef, _platform, _ngZone, \n    /** @breaking-change 11.0.0 make document required */\n    document) {\n        this._elementRef = _elementRef;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._destroyed = new Subject();\n        this._enabled = true;\n        /**\n         * Value of minRows as of last resize. If the minRows has decreased, the\n         * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n         * does not have the same problem because it does not affect the textarea's scrollHeight.\n         */\n        this._previousMinRows = -1;\n        this._isViewInited = false;\n        /** Handles `focus` and `blur` events. */\n        this._handleFocusEvent = (event) => {\n            this._hasFocus = event.type === 'focus';\n        };\n        this._document = document;\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                fromEvent(window, 'resize')\n                    .pipe(auditTime(16), takeUntil(this._destroyed))\n                    .subscribe(() => this.resizeToFitContent(true));\n                this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n                this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        let textareaClone = this._textareaElement.cloneNode(false);\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        textareaClone.style.position = 'absolute';\n        textareaClone.style.visibility = 'hidden';\n        textareaClone.style.border = 'none';\n        textareaClone.style.padding = '0';\n        textareaClone.style.height = '';\n        textareaClone.style.minHeight = '';\n        textareaClone.style.maxHeight = '';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        textareaClone.style.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTextareaAutosize, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkTextareaAutosize, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\"], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: ['cdkTextareaAutosize']\n            }], placeholder: [{\n                type: Input\n            }] } });\n\nclass TextFieldModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: TextFieldModule, declarations: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: TextFieldModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,uBAAuB;AAC3C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9G,SAASC,aAAa,EAAEC,oBAAoB,EAAEC,qBAAqB,QAAQ,uBAAuB;AAClG,SAASC,KAAK,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAChD,SAASC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACrD,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA,MAAMC,eAAe,GAAGnB,+BAA+B,CAAC;EAAEoB,OAAO,EAAE;AAAK,CAAC,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC5B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvC;EACAC,OAAOA,CAACC,YAAY,EAAE;IAClB,IAAI,CAAC,IAAI,CAACL,SAAS,CAACM,SAAS,EAAE;MAC3B,OAAOhB,KAAK;IAChB;IACA,MAAMiB,OAAO,GAAGpB,aAAa,CAACkB,YAAY,CAAC;IAC3C,MAAMG,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACO,GAAG,CAACF,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACN,OAAOA,IAAI,CAACE,OAAO;IACvB;IACA,MAAMC,MAAM,GAAG,IAAIpB,OAAO,CAAC,CAAC;IAC5B,MAAMqB,QAAQ,GAAG,2BAA2B;IAC5C,MAAMC,QAAQ,GAAKC,KAAK,IAAK;MACzB;MACA;MACA;MACA,IAAIA,KAAK,CAACC,aAAa,KAAK,+BAA+B,IACvD,CAACR,OAAO,CAACS,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACvCL,OAAO,CAACS,SAAS,CAACE,GAAG,CAACN,QAAQ,CAAC;QAC/B,IAAI,CAACX,OAAO,CAACkB,GAAG,CAAC,MAAMR,MAAM,CAACS,IAAI,CAAC;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;UAAEC,YAAY,EAAE;QAAK,CAAC,CAAC,CAAC;MACrF,CAAC,MACI,IAAIR,KAAK,CAACC,aAAa,KAAK,6BAA6B,IAC1DR,OAAO,CAACS,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACtCL,OAAO,CAACS,SAAS,CAACO,MAAM,CAACX,QAAQ,CAAC;QAClC,IAAI,CAACX,OAAO,CAACkB,GAAG,CAAC,MAAMR,MAAM,CAACS,IAAI,CAAC;UAAEC,MAAM,EAAEP,KAAK,CAACO,MAAM;UAAEC,YAAY,EAAE;QAAM,CAAC,CAAC,CAAC;MACtF;IACJ,CAAE;IACF,IAAI,CAACrB,OAAO,CAACuB,iBAAiB,CAAC,MAAM;MACjCjB,OAAO,CAACkB,gBAAgB,CAAC,gBAAgB,EAAEZ,QAAQ,EAAEjB,eAAe,CAAC;MACrEW,OAAO,CAACS,SAAS,CAACE,GAAG,CAAC,mCAAmC,CAAC;IAC9D,CAAC,CAAC;IACF,IAAI,CAAChB,kBAAkB,CAACwB,GAAG,CAACnB,OAAO,EAAE;MACjCG,OAAO,EAAEC,MAAM;MACfgB,QAAQ,EAAEA,CAAA,KAAM;QACZpB,OAAO,CAACqB,mBAAmB,CAAC,gBAAgB,EAAEf,QAAQ,EAAEjB,eAAe,CAAC;MAC5E;IACJ,CAAC,CAAC;IACF,OAAOe,MAAM;EACjB;EACAkB,cAAcA,CAACxB,YAAY,EAAE;IACzB,MAAME,OAAO,GAAGpB,aAAa,CAACkB,YAAY,CAAC;IAC3C,MAAMG,IAAI,GAAG,IAAI,CAACN,kBAAkB,CAACO,GAAG,CAACF,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACmB,QAAQ,CAAC,CAAC;MACfnB,IAAI,CAACE,OAAO,CAACoB,QAAQ,CAAC,CAAC;MACvBvB,OAAO,CAACS,SAAS,CAACO,MAAM,CAAC,mCAAmC,CAAC;MAC7DhB,OAAO,CAACS,SAAS,CAACO,MAAM,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACrB,kBAAkB,CAAC6B,MAAM,CAACxB,OAAO,CAAC;IAC3C;EACJ;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,kBAAkB,CAAC+B,OAAO,CAAC,CAACC,KAAK,EAAE3B,OAAO,KAAK,IAAI,CAACsB,cAAc,CAACtB,OAAO,CAAC,CAAC;EACrF;EAAC,QAAA4B,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFxC,eAAe,EAAzBpB,EAAE,CAAA6D,QAAA,CAAyC/D,EAAE,CAACgE,QAAQ,GAAtD9D,EAAE,CAAA6D,QAAA,CAAiE7D,EAAE,CAAC+D,MAAM;EAAA,CAA6C;EAAA,QAAAC,EAAA,GAChN,IAAI,CAACC,KAAK,kBAD6EjE,EAAE,CAAAkE,kBAAA;IAAAC,KAAA,EACY/C,eAAe;IAAAgD,OAAA,EAAfhD,eAAe,CAAAsC,IAAA;IAAAW,UAAA,EAAc;EAAM,EAAG;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGtE,EAAE,CAAAuE,iBAAA,CAGXnD,eAAe,EAAc,CAAC;IAC7GoD,IAAI,EAAEvE,UAAU;IAChBwE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE1E,EAAE,CAACgE;IAAS,CAAC,EAAE;MAAEU,IAAI,EAAExE,EAAE,CAAC+D;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAChG;AACA,MAAMW,WAAW,CAAC;EACdrD,WAAWA,CAACsD,WAAW,EAAEC,gBAAgB,EAAE;IACvC,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACC,WAAW,GAAG,IAAI3E,YAAY,CAAC,CAAC;EACzC;EACA4E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,gBAAgB,CAChBlD,OAAO,CAAC,IAAI,CAACiD,WAAW,CAAC,CACzBI,SAAS,CAAC3C,KAAK,IAAI,IAAI,CAACyC,WAAW,CAACG,IAAI,CAAC5C,KAAK,CAAC,CAAC;EACzD;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsB,gBAAgB,CAACzB,cAAc,CAAC,IAAI,CAACwB,WAAW,CAAC;EAC1D;EAAC,QAAAlB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAuB,oBAAArB,CAAA;IAAA,YAAAA,CAAA,IAAwFc,WAAW,EAvBrB1E,EAAE,CAAAkF,iBAAA,CAuBqClF,EAAE,CAACmF,UAAU,GAvBpDnF,EAAE,CAAAkF,iBAAA,CAuB+D9D,eAAe;EAAA,CAA4C;EAAA,QAAA4C,EAAA,GACnN,IAAI,CAACoB,IAAI,kBAxB8EpF,EAAE,CAAAqF,iBAAA;IAAAb,IAAA,EAwBJE,WAAW;IAAAY,SAAA;IAAAC,OAAA;MAAAV,WAAA;IAAA;EAAA,EAAqF;AAClM;AACA;EAAA,QAAAP,SAAA,oBAAAA,SAAA,KA1BoGtE,EAAE,CAAAuE,iBAAA,CA0BXG,WAAW,EAAc,CAAC;IACzGF,IAAI,EAAErE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCe,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhB,IAAI,EAAExE,EAAE,CAACmF;IAAW,CAAC,EAAE;MAAEX,IAAI,EAAEpD;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyD,WAAW,EAAE,CAAC;MAC1HL,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMqF,mBAAmB,CAAC;EACtB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGjF,oBAAoB,CAACkF,KAAK,CAAC;IAC3C,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAACG,QAAQ,GAAGrF,oBAAoB,CAACkF,KAAK,CAAC;IAC3C,IAAI,CAACI,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACL,KAAK,EAAE;IACfA,KAAK,GAAGjF,qBAAqB,CAACiF,KAAK,CAAC;IACpC;IACA;IACA,IAAI,IAAI,CAACM,QAAQ,KAAKN,KAAK,EAAE;MACzB,CAAC,IAAI,CAACM,QAAQ,GAAGN,KAAK,IAAI,IAAI,CAACO,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,gBAAgB,CAACD,WAAW;EAC5C;EACA,IAAIA,WAAWA,CAACT,KAAK,EAAE;IACnB,IAAI,CAACW,wBAAwB,GAAGC,SAAS;IACzC,IAAIZ,KAAK,EAAE;MACP,IAAI,CAACU,gBAAgB,CAACG,YAAY,CAAC,aAAa,EAAEb,KAAK,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACU,gBAAgB,CAACI,eAAe,CAAC,aAAa,CAAC;IACxD;IACA,IAAI,CAACC,+BAA+B,CAAC,CAAC;EAC1C;EACAtF,WAAWA,CAACsD,WAAW,EAAErD,SAAS,EAAEC,OAAO,EAC3C;EACAqF,QAAQ,EAAE;IACN,IAAI,CAACjC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACrD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACsF,UAAU,GAAG,IAAIhG,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACqF,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACY,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACC,iBAAiB,GAAI5E,KAAK,IAAK;MAChC,IAAI,CAAC6E,SAAS,GAAG7E,KAAK,CAACoC,IAAI,KAAK,OAAO;IAC3C,CAAC;IACD,IAAI,CAAC0C,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACN,gBAAgB,GAAG,IAAI,CAAC3B,WAAW,CAACwC,aAAa;EAC1D;EACA;EACAtB,aAAaA,CAAA,EAAG;IACZ,MAAMuB,SAAS,GAAG,IAAI,CAAC1B,OAAO,IAAI,IAAI,CAAC2B,iBAAiB,GAAI,GAAE,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAAC2B,iBAAkB,IAAG,GAAG,IAAI;IAC9G,IAAID,SAAS,EAAE;MACX,IAAI,CAACd,gBAAgB,CAACgB,KAAK,CAACF,SAAS,GAAGA,SAAS;IACrD;EACJ;EACA;EACApB,aAAaA,CAAA,EAAG;IACZ,MAAMuB,SAAS,GAAG,IAAI,CAACzB,OAAO,IAAI,IAAI,CAACuB,iBAAiB,GAAI,GAAE,IAAI,CAACvB,OAAO,GAAG,IAAI,CAACuB,iBAAkB,IAAG,GAAG,IAAI;IAC9G,IAAIE,SAAS,EAAE;MACX,IAAI,CAACjB,gBAAgB,CAACgB,KAAK,CAACC,SAAS,GAAGA,SAAS;IACrD;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAClG,SAAS,CAACM,SAAS,EAAE;MAC1B;MACA,IAAI,CAAC6F,cAAc,GAAG,IAAI,CAACnB,gBAAgB,CAACgB,KAAK,CAACI,MAAM;MACxD,IAAI,CAACvB,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC5E,OAAO,CAACuB,iBAAiB,CAAC,MAAM;QACjC,MAAM6E,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAChC9G,SAAS,CAAC6G,MAAM,EAAE,QAAQ,CAAC,CACtBE,IAAI,CAAC9G,SAAS,CAAC,EAAE,CAAC,EAAEC,SAAS,CAAC,IAAI,CAAC6F,UAAU,CAAC,CAAC,CAC/C9B,SAAS,CAAC,MAAM,IAAI,CAACoB,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAACG,gBAAgB,CAACvD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACiE,iBAAiB,CAAC;QACvE,IAAI,CAACV,gBAAgB,CAACvD,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACiE,iBAAiB,CAAC;MAC1E,CAAC,CAAC;MACF,IAAI,CAACD,aAAa,GAAG,IAAI;MACzB,IAAI,CAACZ,kBAAkB,CAAC,IAAI,CAAC;IACjC;EACJ;EACA7C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgD,gBAAgB,CAACpD,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC8D,iBAAiB,CAAC;IAC1E,IAAI,CAACV,gBAAgB,CAACpD,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC8D,iBAAiB,CAAC;IACzE,IAAI,CAACH,UAAU,CAACnE,IAAI,CAAC,CAAC;IACtB,IAAI,CAACmE,UAAU,CAACzD,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0E,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACT,iBAAiB,EAAE;MACxB;IACJ;IACA;IACA,IAAIU,aAAa,GAAG,IAAI,CAACzB,gBAAgB,CAAC0B,SAAS,CAAC,KAAK,CAAC;IAC1DD,aAAa,CAACE,IAAI,GAAG,CAAC;IACtB;IACA;IACA;IACAF,aAAa,CAACT,KAAK,CAACY,QAAQ,GAAG,UAAU;IACzCH,aAAa,CAACT,KAAK,CAACa,UAAU,GAAG,QAAQ;IACzCJ,aAAa,CAACT,KAAK,CAACc,MAAM,GAAG,MAAM;IACnCL,aAAa,CAACT,KAAK,CAACe,OAAO,GAAG,GAAG;IACjCN,aAAa,CAACT,KAAK,CAACI,MAAM,GAAG,EAAE;IAC/BK,aAAa,CAACT,KAAK,CAACF,SAAS,GAAG,EAAE;IAClCW,aAAa,CAACT,KAAK,CAACC,SAAS,GAAG,EAAE;IAClC;IACA;IACA;IACA;IACA;IACAQ,aAAa,CAACT,KAAK,CAACgB,QAAQ,GAAG,QAAQ;IACvC,IAAI,CAAChC,gBAAgB,CAACiC,UAAU,CAACC,WAAW,CAACT,aAAa,CAAC;IAC3D,IAAI,CAACV,iBAAiB,GAAGU,aAAa,CAACU,YAAY;IACnDV,aAAa,CAAClF,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACgD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACG,aAAa,CAAC,CAAC;EACxB;EACA0C,oBAAoBA,CAAA,EAAG;IACnB,MAAM7G,OAAO,GAAG,IAAI,CAACyE,gBAAgB;IACrC,MAAMqC,cAAc,GAAG9G,OAAO,CAACyF,KAAK,CAACsB,YAAY,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAG,IAAI,CAACvH,SAAS,CAACwH,OAAO;IACxC,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,IAAI,CAAC5B,SAAS;IACrD,MAAM+B,cAAc,GAAGH,SAAS,GAC1B,yCAAyC,GACzC,iCAAiC;IACvC;IACA;IACA;IACA,IAAIE,iBAAiB,EAAE;MACnBlH,OAAO,CAACyF,KAAK,CAACsB,YAAY,GAAI,GAAE/G,OAAO,CAAC4G,YAAa,IAAG;IAC5D;IACA;IACA;IACA5G,OAAO,CAACS,SAAS,CAACE,GAAG,CAACwG,cAAc,CAAC;IACrC;IACA;IACA,MAAMC,YAAY,GAAGpH,OAAO,CAACoH,YAAY,GAAG,CAAC;IAC7CpH,OAAO,CAACS,SAAS,CAACO,MAAM,CAACmG,cAAc,CAAC;IACxC,IAAID,iBAAiB,EAAE;MACnBlH,OAAO,CAACyF,KAAK,CAACsB,YAAY,GAAGD,cAAc;IAC/C;IACA,OAAOM,YAAY;EACvB;EACAtC,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACI,aAAa,IAAI,IAAI,CAACR,wBAAwB,IAAIC,SAAS,EAAE;MACnE;IACJ;IACA,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MACnB,IAAI,CAACE,wBAAwB,GAAG,CAAC;MACjC;IACJ;IACA,MAAMX,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACV,KAAK;IACzC,IAAI,CAACU,gBAAgB,CAACV,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACD,WAAW;IAC/D,IAAI,CAACE,wBAAwB,GAAG,IAAI,CAACmC,oBAAoB,CAAC,CAAC;IAC3D,IAAI,CAACpC,gBAAgB,CAACV,KAAK,GAAGA,KAAK;EACvC;EACAsD,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC5H,SAAS,CAACM,SAAS,EAAE;MAC1B,IAAI,CAACuE,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,kBAAkBA,CAACgD,KAAK,GAAG,KAAK,EAAE;IAC9B;IACA,IAAI,CAAC,IAAI,CAACjD,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAAC4B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACnB,+BAA+B,CAAC,CAAC;IACtC;IACA;IACA,IAAI,CAAC,IAAI,CAACU,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAM+B,QAAQ,GAAG,IAAI,CAACzE,WAAW,CAACwC,aAAa;IAC/C,MAAMvB,KAAK,GAAGwD,QAAQ,CAACxD,KAAK;IAC5B;IACA,IAAI,CAACuD,KAAK,IAAI,IAAI,CAACxD,QAAQ,KAAK,IAAI,CAACmB,gBAAgB,IAAIlB,KAAK,KAAK,IAAI,CAACyD,cAAc,EAAE;MACpF;IACJ;IACA,MAAMJ,YAAY,GAAG,IAAI,CAACP,oBAAoB,CAAC,CAAC;IAChD,MAAMhB,MAAM,GAAG4B,IAAI,CAACC,GAAG,CAACN,YAAY,EAAE,IAAI,CAAC1C,wBAAwB,IAAI,CAAC,CAAC;IACzE;IACA6C,QAAQ,CAAC9B,KAAK,CAACI,MAAM,GAAI,GAAEA,MAAO,IAAG;IACrC,IAAI,CAACnG,OAAO,CAACuB,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAO0G,qBAAqB,KAAK,WAAW,EAAE;QAC9CA,qBAAqB,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAACL,QAAQ,CAAC,CAAC;MACtE,CAAC,MACI;QACDM,UAAU,CAAC,MAAM,IAAI,CAACD,sBAAsB,CAACL,QAAQ,CAAC,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,GAAGzD,KAAK;IAC3B,IAAI,CAACkB,gBAAgB,GAAG,IAAI,CAACnB,QAAQ;EACzC;EACA;AACJ;AACA;EACIS,KAAKA,CAAA,EAAG;IACJ;IACA;IACA,IAAI,IAAI,CAACqB,cAAc,KAAKjB,SAAS,EAAE;MACnC,IAAI,CAACF,gBAAgB,CAACgB,KAAK,CAACI,MAAM,GAAG,IAAI,CAACD,cAAc;IAC5D;EACJ;EACAkC,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1C,SAAS,IAAIN,QAAQ;EACrC;EACA;EACAgB,UAAUA,CAAA,EAAG;IACT,MAAMiC,GAAG,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IAC/B,OAAOC,GAAG,CAACC,WAAW,IAAInC,MAAM;EACpC;EACA;AACJ;AACA;AACA;AACA;EACI8B,sBAAsBA,CAACL,QAAQ,EAAE;IAC7B,MAAM;MAAEW,cAAc;MAAEC;IAAa,CAAC,GAAGZ,QAAQ;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvC,UAAU,CAACoD,SAAS,IAAI,IAAI,CAAChD,SAAS,EAAE;MAC9CmC,QAAQ,CAACc,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;IAC5D;EACJ;EAAC,QAAAvG,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAyG,4BAAAvG,CAAA;IAAA,YAAAA,CAAA,IAAwF6B,mBAAmB,EAvS7BzF,EAAE,CAAAkF,iBAAA,CAuS6ClF,EAAE,CAACmF,UAAU,GAvS5DnF,EAAE,CAAAkF,iBAAA,CAuSuEpF,EAAE,CAACgE,QAAQ,GAvSpF9D,EAAE,CAAAkF,iBAAA,CAuS+FlF,EAAE,CAAC+D,MAAM,GAvS1G/D,EAAE,CAAAkF,iBAAA,CAuSqHjE,QAAQ;EAAA,CAA4D;EAAA,QAAA+C,EAAA,GAClR,IAAI,CAACoB,IAAI,kBAxS8EpF,EAAE,CAAAqF,iBAAA;IAAAb,IAAA,EAwSJiB,mBAAmB;IAAAH,SAAA;IAAA8E,SAAA,WAAwP,GAAG;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAxS5QvK,EAAE,CAAAyK,UAAA,mBAAAC,6CAAA;UAAA,OAwSJF,GAAA,CAAAb,iBAAA,CAAkB,CAAC;QAAA;MAAA;IAAA;IAAAgB,MAAA;MAAAjF,OAAA;MAAAI,OAAA;MAAAG,OAAA;MAAAI,WAAA;IAAA;IAAAuE,QAAA;EAAA,EAA2Y;AAChgB;AACA;EAAA,QAAAtG,SAAA,oBAAAA,SAAA,KA1SoGtE,EAAE,CAAAuE,iBAAA,CA0SXkB,mBAAmB,EAAc,CAAC;IACjHjB,IAAI,EAAErE,SAAS;IACfsE,IAAI,EAAE,CAAC;MACCe,QAAQ,EAAE,+BAA+B;MACzCoF,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA,MAAM,EAAE,GAAG;QACX,SAAS,EAAE;MACf;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErG,IAAI,EAAExE,EAAE,CAACmF;IAAW,CAAC,EAAE;MAAEX,IAAI,EAAE1E,EAAE,CAACgE;IAAS,CAAC,EAAE;MAAEU,IAAI,EAAExE,EAAE,CAAC+D;IAAO,CAAC,EAAE;MAAES,IAAI,EAAEgC,SAAS;MAAEsE,UAAU,EAAE,CAAC;QACnItG,IAAI,EAAEnE;MACV,CAAC,EAAE;QACCmE,IAAI,EAAElE,MAAM;QACZmE,IAAI,EAAE,CAACxD,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyE,OAAO,EAAE,CAAC;MACtClB,IAAI,EAAEjE,KAAK;MACXkE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEqB,OAAO,EAAE,CAAC;MACVtB,IAAI,EAAEjE,KAAK;MACXkE,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEwB,OAAO,EAAE,CAAC;MACVzB,IAAI,EAAEjE,KAAK;MACXkE,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE4B,WAAW,EAAE,CAAC;MACd7B,IAAI,EAAEjE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwK,eAAe,CAAC;EAAA,QAAAtH,CAAA,GACT,IAAI,CAACC,IAAI,YAAAsH,wBAAApH,CAAA;IAAA,YAAAA,CAAA,IAAwFmH,eAAe;EAAA,CAAkD;EAAA,QAAA/G,EAAA,GAClK,IAAI,CAACiH,IAAI,kBA3U8EjL,EAAE,CAAAkL,gBAAA;IAAA1G,IAAA,EA2USuG;EAAe,EAAkG;EAAA,QAAAI,EAAA,GACnN,IAAI,CAACC,IAAI,kBA5U8EpL,EAAE,CAAAqL,gBAAA,IA4U2B;AACjI;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KA9UoGtE,EAAE,CAAAuE,iBAAA,CA8UXwG,eAAe,EAAc,CAAC;IAC7GvG,IAAI,EAAEhE,QAAQ;IACdiE,IAAI,EAAE,CAAC;MACC6G,YAAY,EAAE,CAAC5G,WAAW,EAAEe,mBAAmB,CAAC;MAChD8F,OAAO,EAAE,CAAC7G,WAAW,EAAEe,mBAAmB;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASrE,eAAe,EAAEsD,WAAW,EAAEe,mBAAmB,EAAEsF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}