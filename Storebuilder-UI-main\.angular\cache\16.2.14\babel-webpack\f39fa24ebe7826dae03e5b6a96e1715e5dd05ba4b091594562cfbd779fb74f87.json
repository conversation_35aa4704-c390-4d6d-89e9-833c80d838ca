{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport { InitialModule } from \"../../shared/modules/initial.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CartModule = /*#__PURE__*/(() => {\n  class CartModule {\n    static ɵfac = function CartModule_Factory(t) {\n      return new (t || CartModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CartModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), RippleModule, TranslateModule, ButtonModule, SharedModule, EmptyScreenComponent, BackButtonComponent, DialogModule, DropdownModule, InitialModule]\n    });\n  }\n  return CartModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}