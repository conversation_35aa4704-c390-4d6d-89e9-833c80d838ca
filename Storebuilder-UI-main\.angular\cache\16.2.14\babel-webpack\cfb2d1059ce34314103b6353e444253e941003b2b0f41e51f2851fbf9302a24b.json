{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"primeng/inputtext\";\nconst _c0 = function (a0) {\n  return {\n    color: a0\n  };\n};\nfunction EmailInputComponent_div_0_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r2.firstNameFlag && ctx_r2.email.length === 0 || (ctx_r2.emailForm.controls.emailAddress.errors == null ? null : ctx_r2.emailForm.controls.emailAddress.errors.email) ? \"red\" : ctx_r2.labelColor));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.email\"), \" \");\n  }\n}\nfunction EmailInputComponent_div_0_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c0, ctx_r3.firstNameFlag && ctx_r3.email.length === 0 || (ctx_r3.emailForm.controls.emailAddress.errors == null ? null : ctx_r3.emailForm.controls.emailAddress.errors.email) ? \"red\" : ctx_r3.labelColor))(\"for\", ctx_r3.floatingLabel ? \"custom-float-input\" : \"float-input\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"auth.registerPassword.email\"), \" * \");\n  }\n}\nfunction EmailInputComponent_div_0_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9)(1, \"small\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"auth.registerPassword.validation6\"));\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"p-float-label\": a0,\n    \"mt-3\": a1\n  };\n};\nconst _c2 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction EmailInputComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, EmailInputComponent_div_0_label_1_Template, 3, 6, \"label\", 2);\n    i0.ɵɵelementStart(2, \"span\", 3)(3, \"input\", 4);\n    i0.ɵɵlistener(\"click\", function EmailInputComponent_div_0_Template_input_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onBlur());\n    })(\"input\", function EmailInputComponent_div_0_Template_input_input_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onInputChange($event));\n    })(\"ngModelChange\", function EmailInputComponent_div_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.email = $event);\n    })(\"ngModelChange\", function EmailInputComponent_div_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onEmailChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, EmailInputComponent_div_0_label_4_Template, 3, 7, \"label\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, EmailInputComponent_div_0_p_5_Template, 4, 3, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.floatingLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, ctx_r0.floatingLabel, ctx_r0.floatingLabel));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.email)(\"ngModelOptions\", i0.ɵɵpureFunction0(10, _c2))(\"id\", ctx_r0.floatingLabel ? \"custom-float-input\" : \"float-input\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.floatingLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.emailForm.controls == null ? null : ctx_r0.emailForm.controls.emailAddress == null ? null : ctx_r0.emailForm.controls.emailAddress.errors == null ? null : ctx_r0.emailForm.controls.emailAddress.errors.email);\n  }\n}\nfunction EmailInputComponent_div_1_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r10.labelColor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"auth.registerPassword.email\"), \" \");\n  }\n}\nfunction EmailInputComponent_div_1_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r11.labelColor);\n    i0.ɵɵproperty(\"for\", ctx_r11.floatingLabel ? \"custom-float-input\" : \"float-input\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 4, \"auth.registerPassword.email\"), \" \");\n  }\n}\nfunction EmailInputComponent_div_1_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9)(1, \"small\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"auth.registerPassword.validation6\"));\n  }\n}\nfunction EmailInputComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, EmailInputComponent_div_1_label_1_Template, 3, 5, \"label\", 11);\n    i0.ɵɵelementStart(2, \"span\", 3)(3, \"input\", 4);\n    i0.ɵɵlistener(\"click\", function EmailInputComponent_div_1_Template_input_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onBlur());\n    })(\"input\", function EmailInputComponent_div_1_Template_input_input_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onInputChange($event));\n    })(\"ngModelChange\", function EmailInputComponent_div_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.email = $event);\n    })(\"ngModelChange\", function EmailInputComponent_div_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onEmailChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, EmailInputComponent_div_1_label_4_Template, 3, 6, \"label\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, EmailInputComponent_div_1_p_5_Template, 4, 3, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.floatingLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, ctx_r1.floatingLabel, ctx_r1.floatingLabel));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.email)(\"ngModelOptions\", i0.ɵɵpureFunction0(10, _c2))(\"id\", ctx_r1.floatingLabel ? \"custom-float-input\" : \"float-input\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.floatingLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.emailForm.controls == null ? null : ctx_r1.emailForm.controls.emailAddress == null ? null : ctx_r1.emailForm.controls.emailAddress.errors == null ? null : ctx_r1.emailForm.controls.emailAddress.errors.email);\n  }\n}\nexport class EmailInputComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.emailRequired = 'false';\n    this.floatingLabel = false;\n    this.labelColor = 'white';\n    this.placeholder = '';\n    this.emailLabel = '';\n    this.emailChange = new EventEmitter();\n    this.validationChange = new EventEmitter();\n    this.blur = new EventEmitter();\n    this.email = '';\n    this.firstNameFlag = false;\n    this.emailForm = this.fb.group({\n      emailAddress: ['', [Validators.email]]\n    });\n  }\n  ngOnInit() {\n    // Update validators based on emailRequired\n    if (this.emailRequired === 'true') {\n      this.emailForm.controls.emailAddress.setValidators([Validators.required, Validators.email]);\n    } else {\n      this.emailForm.controls.emailAddress.setValidators([Validators.email]);\n    }\n    this.emailForm.controls.emailAddress.updateValueAndValidity();\n    // Listen to form changes\n    this.emailForm.valueChanges.subscribe(() => {\n      this.validationChange.emit(this.isValid());\n    });\n  }\n  onEmailChange(value) {\n    this.emailForm.patchValue({\n      emailAddress: value\n    });\n    if (value && value.trim() !== '') {\n      const isValidEmail = this.isValidEmailAddress(value);\n      if (isValidEmail) {\n        this.emailChange.emit(value.trim());\n      } else {\n        this.emailChange.emit(null);\n      }\n    } else {\n      this.emailChange.emit(null);\n    }\n  }\n  onBlur() {\n    this.blur.emit();\n    if (this.emailRequired === 'true' && this.email.length === 0) {\n      this.firstNameFlag = true;\n    }\n  }\n  onInputChange(event) {\n    if (event.target.value) {\n      const firstSpaceIndex = event.target.value.indexOf(' ');\n      if (firstSpaceIndex == 0) {\n        event.target.value = event.target.value.substring(0, firstSpaceIndex) + event.target.value.substring(firstSpaceIndex + 1);\n      }\n    }\n    this.onEmailChange(event.target.value);\n  }\n  isValidEmailAddress(email) {\n    const regex = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/;\n    return regex.test(email);\n  }\n  isValid() {\n    return this.emailForm.valid && (this.emailRequired === 'false' || this.email.length > 0);\n  }\n}\nEmailInputComponent.ɵfac = function EmailInputComponent_Factory(t) {\n  return new (t || EmailInputComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n};\nEmailInputComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: EmailInputComponent,\n  selectors: [[\"app-email-input\"]],\n  inputs: {\n    emailRequired: \"emailRequired\",\n    floatingLabel: \"floatingLabel\",\n    labelColor: \"labelColor\",\n    placeholder: \"placeholder\",\n    emailLabel: \"emailLabel\"\n  },\n  outputs: {\n    emailChange: \"emailChange\",\n    validationChange: \"validationChange\",\n    blur: \"blur\"\n  },\n  standalone: true,\n  features: [i0.ɵɵStandaloneFeature],\n  decls: 2,\n  vars: 2,\n  consts: [[\"class\", \"p-field p-col-12\", 4, \"ngIf\"], [1, \"p-field\", \"p-col-12\"], [\"class\", \"contact-label\", 3, \"ngStyle\", 4, \"ngIf\"], [3, \"ngClass\"], [\"autocomplete\", \"new-password\", \"name\", \"emailAddress\", \"pInputText\", \"\", \"type\", \"email\", 3, \"ngModel\", \"ngModelOptions\", \"id\", \"click\", \"input\", \"ngModelChange\"], [3, \"ngStyle\", \"for\", 4, \"ngIf\"], [\"class\", \"field-error\", 4, \"ngIf\"], [1, \"contact-label\", 3, \"ngStyle\"], [3, \"ngStyle\", \"for\"], [1, \"field-error\"], [2, \"color\", \"red\"], [\"class\", \"contact-label\", 3, \"color\", 4, \"ngIf\"], [3, \"for\", \"color\", 4, \"ngIf\"], [1, \"contact-label\"], [3, \"for\"]],\n  template: function EmailInputComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, EmailInputComponent_div_0_Template, 6, 11, \"div\", 0);\n      i0.ɵɵtemplate(1, EmailInputComponent_div_1_Template, 6, 11, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.emailRequired === \"true\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.emailRequired === \"false\");\n    }\n  },\n  dependencies: [CommonModule, i2.NgClass, i2.NgIf, i2.NgStyle, FormsModule, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgModel, ReactiveFormsModule, TranslateModule, i3.TranslatePipe, InputTextModule, i4.InputText],\n  styles: [\".p-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\nspan[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 0.25rem;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\\n\\ninput#float-input[_ngcontent-%COMP%], input#custom-float-input[_ngcontent-%COMP%] {\\n  height: 50px !important;\\n  width: 100%;\\n  border-radius: 2px;\\n  opacity: 1;\\n  border: none !important;\\n  border: 1px solid #E4E7E9 !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  font-size: 14px;\\n  font-weight: 400;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.field-error[_ngcontent-%COMP%] {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0;\\n}\\n\\n.p-float-label[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: none;\\n  top: 50%;\\n  margin-top: -0.5rem;\\n  transition: all 0.2s;\\n  color: #6c757d !important;\\n}\\n\\n.p-float-label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus    ~ label[_ngcontent-%COMP%], .p-float-label[_ngcontent-%COMP%]   input.p-filled[_ngcontent-%COMP%]    ~ label[_ngcontent-%COMP%], .p-float-label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:-webkit-autofill    ~ label[_ngcontent-%COMP%] {\\n  top: -0.5rem;\\n  font-size: 12px;\\n  background-color: transparent;\\n  padding: 0 0.25rem;\\n  color: #333 !important;\\n}\\n\\n.contact-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-bottom: 0.25rem;\\n  margin-top: 0 !important;\\n  color: #333 !important;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}