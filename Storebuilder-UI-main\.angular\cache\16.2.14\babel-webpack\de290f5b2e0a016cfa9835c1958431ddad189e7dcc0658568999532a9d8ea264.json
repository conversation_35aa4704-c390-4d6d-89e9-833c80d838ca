{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"primeng/dialog\";\nimport * as i3 from \"@angular/common\";\nfunction SuccessInfoModalComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"img\", 5);\n    i0.ɵɵelementStart(2, \"span\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.titleMessage);\n  }\n}\nfunction SuccessInfoModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SuccessInfoModalComponent_ng_template_1_div_0_Template, 4, 1, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayModal);\n  }\n}\nfunction SuccessInfoModalComponent_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.bodyMessage);\n  }\n}\nfunction SuccessInfoModalComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SuccessInfoModalComponent_ng_template_2_div_0_Template, 5, 1, \"div\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.displayModal && ctx_r1.bodyMessage);\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class SuccessInfoModalComponent {\n  constructor() {\n    this.displayModal = false;\n    this.titleMessage = '';\n    this.bodyMessage = '';\n    this.caution = '';\n    this.cancel = new EventEmitter();\n  }\n  ngOnInit() {\n    /**/\n  }\n  onHide() {\n    this.cancel.emit(true);\n  }\n}\nSuccessInfoModalComponent.ɵfac = function SuccessInfoModalComponent_Factory(t) {\n  return new (t || SuccessInfoModalComponent)();\n};\nSuccessInfoModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SuccessInfoModalComponent,\n  selectors: [[\"app-success-info-modal\"]],\n  inputs: {\n    displayModal: \"displayModal\",\n    titleMessage: \"titleMessage\",\n    bodyMessage: \"bodyMessage\",\n    caution: \"caution\"\n  },\n  outputs: {\n    cancel: \"cancel\"\n  },\n  decls: 3,\n  vars: 8,\n  consts: [[1, \"share-success-modal\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"showHeader\", \"modal\", \"resizable\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [\"class\", \"inline-flex align-items-center justify-content-center gap-2\", 4, \"ngIf\"], [1, \"inline-flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/success-icon.svg\"], [1, \"share-success-modal__title\", \"font-bold\", \"white-space-nowrap\"], [\"class\", \"share-success-modal__body-container\", 4, \"ngIf\"], [1, \"share-success-modal__body-container\"], [1, \"d-flex\", \"align-items-center\", \"share-success-modal__body-container__primary-text\"], [1, \"d-inline-flex\"]],\n  template: function SuccessInfoModalComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p-dialog\", 0);\n      i0.ɵɵlistener(\"visibleChange\", function SuccessInfoModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n        return ctx.displayModal = $event;\n      })(\"onHide\", function SuccessInfoModalComponent_Template_p_dialog_onHide_0_listener() {\n        return ctx.onHide();\n      });\n      i0.ɵɵtemplate(1, SuccessInfoModalComponent_ng_template_1_Template, 1, 1, \"ng-template\", 1);\n      i0.ɵɵtemplate(2, SuccessInfoModalComponent_ng_template_2_Template, 1, 1, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(7, _c0))(\"dismissableMask\", true)(\"draggable\", false)(\"showHeader\", true)(\"modal\", true)(\"resizable\", false);\n    }\n  },\n  dependencies: [i1.PrimeTemplate, i2.Dialog, i3.NgIf],\n  styles: [\".share-success-modal__title[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: var(--medium-font);\\n  font-size: 17px;\\n  font-style: normal;\\n  font-weight: 400 !important;\\n  line-height: normal;\\n}\\n.share-success-modal__body-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem 0.5rem 0rem 0.5rem;\\n}\\n.share-success-modal__body-container__checkbox[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n}\\n.share-success-modal__body-container__primary-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  text-align: center;\\n  font-family: var(--medium-font);\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n.share-success-modal__action-btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 30px; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n}\\n.share-success-modal__action-btn__notify-btn[_ngcontent-%COMP%] {\\n  background: var(--colors-Main-Color, #204E6E) !important;\\n  border: none !important;\\n  color: var(--Gray-00, var(--colors-fff, #FFF)) !important;\\n}\\n.share-success-modal__action-btn__close-btn[_ngcontent-%COMP%] {\\n  background: transparent !important;\\n  border: 1px solid var(--Secondary-100, #D5EDFD) !important;\\n  color: var(--colors-Main-Color, #204E6E) !important;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .p-dialog .p-dialog-header-icons {\\n  display: none;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .p-dialog .p-dialog-header {\\n  padding: 0.5rem 0.5rem 0rem 0.5rem;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .p-dialog .p-dialog-footer button {\\n  width: 100%;\\n  margin: 0;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .p-dialog-content {\\n  border-bottom: none !important;\\n  border-radius: 0px !important;\\n  padding: 0;\\n  overflow-y: visible;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .model img {\\n  width: 50px;\\n  height: 50px;\\n  margin-bottom: 30px;\\n  margin-top: 70px;\\n}\\n.share-success-modal[_ngcontent-%COMP%]     .model p {\\n  color: #000;\\n  font-size: 18px;\\n  font-family: main-medium, sans-serif;\\n  margin-bottom: 114px;\\n  text-align: center;\\n  line-height: 25px;\\n  padding-right: 28px;\\n  padding-left: 28px;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}