{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { HttpParams } from \"@angular/common/http\";\nimport { BehaviorSubject, map } from \"rxjs\";\nimport { castToHttpParams, EndPointsConfig } from '@core/utilities';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class OrderService {\n  http;\n  orderId = '';\n  baseUrl;\n  discountResetSubject = new BehaviorSubject(undefined);\n  discountReset$ = this.discountResetSubject.asObservable();\n  resetDiscount() {\n    this.discountResetSubject.next();\n  }\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Order`;\n  }\n  getAllOrders() {\n    return this.http.get(`${this.baseUrl}/Order/GetAllOrder`);\n  }\n  getOrder(id) {\n    return this.http.get(`${this.baseUrl}/Order/GetOrderById?OrderId=${id}`);\n  }\n  getAllOrderDetail(id) {\n    let params = new HttpParams().set('id', id);\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.OrderDetails.Controller}/${EndPointsConfig.OrderDetails.EndPoints.GetAllOrderDetail}`, {\n      params: params\n    });\n  }\n  GetOrderWithPaymentsConfigurations(data) {\n    return this.http.post(`${this.baseUrl}/Order/GetOrderWithPaymentsConfigurations`, data);\n  }\n  createOrder(data) {\n    return this.http.post(`${this.baseUrl}/Order/CreateOrder`, data);\n  }\n  updateOrder(data) {\n    return this.http.post(`${this.baseUrl}/Order/UpdateOrder`, data);\n  }\n  checkAddressCityRegion(data) {\n    return this.http.post(`${this.baseUrl}/Tenant/Address/CheckAddressCityRegion`, data);\n  }\n  getCustomerOrders(model) {\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.Order.Controller}/${EndPointsConfig.Order.EndPoints.GetCustomerOrder}`, {\n      params: castToHttpParams(model)\n    }).pipe(map(res => {\n      return {\n        orders: res?.data?.records ? res?.data?.records : [],\n        count: res?.data?.total ? res?.data?.total : 0\n      };\n    }));\n  }\n  cancelOrder(data) {\n    return this.http.post(`${this.baseUrl}/Order/CancelOrder`, data);\n  }\n  refundOrder(model) {\n    return this.http.post(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.Order.Controller}/${EndPointsConfig.Order.EndPoints.RefundOrder}`, model).pipe(map(res => {\n      return {\n        success: res?.success\n      };\n    }));\n  }\n  requestOrderRefund(model) {\n    return this.http.post(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.Order.Controller}/${EndPointsConfig.Order.EndPoints.RequestOrderRefund}`, model).pipe(map(res => {\n      return {\n        success: res?.success\n      };\n    }));\n  }\n  getRefundReasons() {\n    let params = new HttpParams().set('PageSize', 20);\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.RefundReason.Controller}/${EndPointsConfig.RefundReason.EndPoints.GetAllRefundReason}`, {\n      params: params\n    }).pipe(map(res => {\n      return {\n        reasons: res?.data?.records ? res?.data?.records : []\n      };\n    }));\n  }\n  getAllRefundReasons() {\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.RefundReason.Controller}/${EndPointsConfig.RefundReason.EndPoints.GetRefundReasonList}`);\n  }\n  updateOrderStatus(orderId) {\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.Order.Controller}/${EndPointsConfig.Order.EndPoints.UpdateOrderStatus}/${orderId}`).pipe(map(res => {\n      return {\n        success: res?.success\n      };\n    }));\n  }\n  returnOrder(data) {\n    return this.http.post(`${this.baseUrl}/Order/ConsumerRequestRefund`, data);\n  }\n  applyPromoCode(data) {\n    return this.http.post(`${this.baseUrl}/Order/ApplyPromoCode`, data);\n  }\n  getOrderDiscount(id) {\n    let params = new HttpParams().set('orderId', id);\n    return this.http.get(`${this.baseUrl}/Order/GetOrderDiscountValue`, {\n      params: params\n    });\n  }\n  removePromoCode(data) {\n    return this.http.post(`${this.baseUrl}/Order/RemovePromoCode`, data);\n  }\n  rejectAllOrder(data) {\n    return this.http.post(`${this.baseUrl}/Order/CancelOrder`, data);\n  }\n  verifyOrderProductsVisibilityBeforeCheckout({\n    OrderId\n  }) {\n    return this.http.post(`${this.baseUrl}/Order/VerifyOrderProductsVisibilityBeforeCheckout`, {\n      OrderId\n    });\n  }\n  ReturnLastItemAmountWithShipmentFees(data) {\n    return this.http.post(`${this.baseUrl}/Order/ReturnLastItemAmountWithShipmentFees`, data);\n  }\n  downloadOrderReceipt(orderId) {\n    return this.http.get(`${this.baseUrl}/OrderReceipt/DownloadConsumerOrderReceipt?orderId=${orderId}`);\n  }\n  static ɵfac = function OrderService_Factory(t) {\n    return new (t || OrderService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: OrderService,\n    factory: OrderService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}