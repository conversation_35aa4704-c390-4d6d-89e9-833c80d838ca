{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../../shared/components/category-card/category-card.component\";\nimport * as i6 from \"primeng/breadcrumb\";\nfunction IndexComponent_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 9);\n    i0.ɵɵelement(1, \"app-mtn-category-card\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"routerLink\", \"/category/\", category_r1.id, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"category\", category_r1)(\"showAllMobile\", ctx_r0.screenWidth < 768);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"content-container\": a0\n  };\n};\nexport let IndexComponent = /*#__PURE__*/(() => {\n  class IndexComponent {\n    activatedRoute;\n    store;\n    productService;\n    translateService;\n    cdr;\n    platformId;\n    items = [];\n    categories = [];\n    home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    categoryId;\n    products = [];\n    screenWidth;\n    constructor(activatedRoute, store, productService, translateService, cdr, platformId) {\n      this.activatedRoute = activatedRoute;\n      this.store = store;\n      this.productService = productService;\n      this.translateService = translateService;\n      this.cdr = cdr;\n      this.platformId = platformId;\n      this.categoryId = this.activatedRoute.snapshot.params['id'];\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n    }\n    onResize(event) {\n      this.screenWidth = event.target.innerWidth;\n    }\n    ngOnInit() {\n      this.items = [{\n        label: this.translateService.instant('categories.allCategories')\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        let categories = localStorage.getItem('allCategories');\n        if (categories && categories !== '') {\n          this.categories = JSON.parse(categories);\n          this.cdr.detectChanges();\n        }\n      }, 100);\n    }\n    static ɵfac = function IndexComponent_Factory(t) {\n      return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndexComponent,\n      selectors: [[\"app-index\"]],\n      hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 11,\n      vars: 9,\n      consts: [[1, \"categories-page\"], [1, \"breadcrumb\", 2, \"margin-top\", \"93px\"], [3, \"home\", \"model\"], [1, \"mt-2\", \"main_font\", 3, \"ngClass\"], [1, \"grid\"], [1, \"col-12\", \"col-md-12\", \"flex\", \"md:justify-content-start\", \"all-categ\"], [1, \"all-category\", \"bold-font\"], [1, \"my-4\", \"flex\", \"flex-row\", \"flex-wrap\", \"card-category\"], [\"class\", \"mt-2 mx-2 mb-5 card-spaces\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"mx-2\", \"mb-5\", \"card-spaces\", 3, \"routerLink\"], [3, \"category\", \"showAllMobile\"]],\n      template: function IndexComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵpipe(8, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7);\n          i0.ɵɵtemplate(10, IndexComponent_a_10_Template, 2, 3, \"a\", 8);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"home\", ctx.home)(\"model\", ctx.items);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.screenWidth >= 768));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 5, \"categories.allCategories\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i5.CategoryCardComponent, i1.RouterLink, i6.Breadcrumb, i3.TranslatePipe],\n      styles: [\"a[_ngcontent-%COMP%]{text-decoration:none}.all-category[_ngcontent-%COMP%]{font-size:24px}@media screen and (max-width: 768px){.breadcrumb[_ngcontent-%COMP%]{margin-top:0!important}.all-categ[_ngcontent-%COMP%]{justify-content:left!important;margin-left:17px}.main_font[_ngcontent-%COMP%]{margin-top:0!important}.breadcrumb[_ngcontent-%COMP%]   p-breadcrumb.p-element[_ngcontent-%COMP%]{margin-bottom:15px!important}.card-category[_ngcontent-%COMP%]{margin-top:1rem!important}.card-spaces[_ngcontent-%COMP%]{margin-left:1.4rem!important;margin-right:0rem!important}}\"]\n    });\n  }\n  return IndexComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}