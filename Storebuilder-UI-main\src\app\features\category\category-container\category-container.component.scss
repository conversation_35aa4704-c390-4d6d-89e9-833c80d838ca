@import '../../../../assets/scss/common.scss';

// Base styles
a {
  text-decoration: none;
}

.all-category {
  font-size: 24px;
}

.categories-page {
  padding: 20px;
  min-height: 100vh;
}

.breadcrumb {
  margin-bottom: 20px;
}

.breadcrumb-spacing {
  margin-top: 93px;
}

.content-container {
  // Only apply max-width on desktop
  @media (min-width: 768px) {
    max-width: 1200px;
    margin: 0 auto;
  }
}

.grid {
  display: grid;
  gap: 20px;
}

.card-category {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.card-spaces {
  flex: 0 0 auto;
  margin-bottom: 20px;
}

.selected-category-products {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.product-item {
  padding: 15px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// ========================================
// MEDIA QUERIES
// ========================================

@media (max-width: 768px) {
  .breadcrumb {
    display: none;
  }
  
  .all-categ {
    justify-content: left !important;
    margin-left: 17px;
  }
  
  .main_font {
    margin-top: 0 !important;
  }
  
  .card-category {
    margin-top: 1rem !important;
  }
  
  .card-spaces {
    margin: 0 0 20px 1.4rem !important;
  }
  
  .categories-page {
    padding: 15px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}
