{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport { debounceTime, distinctUntilChanged, fromEvent, map, switchMap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/custom-GA.service\";\nimport * as i7 from \"primeng/divider\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-intl-tel-input-gg\";\nimport * as i10 from \"@angular/forms\";\nconst _c0 = [\"inputField\"];\nfunction SearchComponent_ng_container_0_ng_container_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_ng_container_9_div_3_Template_div_mousedown_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const option_r7 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.NavigateToProduct(option_r7.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"p-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", option_r7.currencyCode, \"\\u00A0\", (option_r7.specsProducts[0] == null ? null : option_r7.specsProducts[0].salePrice) ? option_r7.specsProducts[0] == null ? null : option_r7.specsProducts[0].salePrice : option_r7.specsProducts[0] == null ? null : option_r7.specsProducts[0].price, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"search.in\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", option_r7.categoryName, \" \");\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_9_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"searchNotFound\"), \" \");\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵtemplate(3, SearchComponent_ng_container_0_ng_container_9_div_3_Template, 13, 7, \"div\", 8);\n    i0.ɵɵtemplate(4, SearchComponent_ng_container_0_ng_container_9_p_4_Template, 3, 3, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.searchResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.searchResult && ctx_r3.searchResult.length === 0);\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function SearchComponent_ng_container_0_ng_container_10_div_8_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const historyItem_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.selectFromHistory(historyItem_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵelement(2, \"em\", 26);\n    i0.ɵɵelementStart(3, \"span\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 28);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_ng_container_10_div_8_Template_button_mousedown_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const historyItem_r12 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.removeFromHistory(historyItem_r12, $event));\n    });\n    i0.ɵɵelement(6, \"em\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const historyItem_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(historyItem_r12);\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 19)(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 21);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_ng_container_10_Template_button_mousedown_6_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.clearSearchHistory($event));\n    });\n    i0.ɵɵelement(7, \"em\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SearchComponent_ng_container_0_ng_container_10_div_8_Template, 7, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"search.recentSearches\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.searchHistory);\n  }\n}\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction SearchComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"form\", 2)(3, \"input\", 3, 4);\n    i0.ɵɵlistener(\"focus\", function SearchComponent_ng_container_0_Template_input_focus_3_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onInputFocus());\n    })(\"focusout\", function SearchComponent_ng_container_0_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.closeDropDown());\n    })(\"keyup.enter\", function SearchComponent_ng_container_0_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.searchProduct($event, \"\"));\n    })(\"ngModelChange\", function SearchComponent_ng_container_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.searchChange($event));\n    })(\"ngModelChange\", function SearchComponent_ng_container_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.search = $event);\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\")(8, \"em\", 5);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_Template_em_mousedown_8_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.searchProduct($event, ctx_r24.search));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, SearchComponent_ng_container_0_ng_container_9_Template, 5, 2, \"ng-container\", 0);\n    i0.ɵɵtemplate(10, SearchComponent_ng_container_0_ng_container_10_Template, 9, 4, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.scConfig ? i0.ɵɵpipeBind1(5, 5, \"search.scSearchOnMoMoMarket\") : i0.ɵɵpipeBind1(6, 7, \"search.searchOnMoMoMarket\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.search)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayResult && !ctx_r0.showHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showHistory && ctx_r0.searchHistory.length > 0);\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_ng_container_9_div_3_Template_div_mousedown_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r33);\n      const option_r30 = restoredCtx.$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r32.NavigateToProduct(option_r30.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"p-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r30 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r30.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", option_r30.currencyCode, \"\\u00A0\", (option_r30.specsProducts[0] == null ? null : option_r30.specsProducts[0].salePrice) ? option_r30.specsProducts[0] == null ? null : option_r30.specsProducts[0].salePrice : option_r30.specsProducts[0] == null ? null : option_r30.specsProducts[0].price, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"search.in\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", option_r30.categoryName, \" \");\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"searchNotFound\"), \" \");\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵtemplate(3, SearchComponent_ng_container_1_ng_container_9_div_3_Template, 13, 7, \"div\", 8);\n    i0.ɵɵtemplate(4, SearchComponent_ng_container_1_ng_container_9_p_4_Template, 3, 3, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.searchResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.searchResult && ctx_r26.searchResult.length === 0);\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function SearchComponent_ng_container_1_ng_container_10_div_8_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const historyItem_r35 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.selectFromHistory(historyItem_r35));\n    });\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵelement(2, \"em\", 26);\n    i0.ɵɵelementStart(3, \"span\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 28);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_ng_container_10_div_8_Template_button_mousedown_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const historyItem_r35 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.removeFromHistory(historyItem_r35, $event));\n    });\n    i0.ɵɵelement(6, \"em\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const historyItem_r35 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(historyItem_r35);\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"div\", 19)(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 21);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_ng_container_10_Template_button_mousedown_6_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.clearSearchHistory($event));\n    });\n    i0.ɵɵelement(7, \"em\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, SearchComponent_ng_container_1_ng_container_10_div_8_Template, 7, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"search.recentSearches\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.searchHistory);\n  }\n}\nfunction SearchComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"form\", 2)(3, \"input\", 3, 4);\n    i0.ɵɵlistener(\"focus\", function SearchComponent_ng_container_1_Template_input_focus_3_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onInputFocus());\n    })(\"focusout\", function SearchComponent_ng_container_1_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.closeDropDown());\n    })(\"keyup.enter\", function SearchComponent_ng_container_1_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.searchProduct($event, \"\"));\n    })(\"ngModelChange\", function SearchComponent_ng_container_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.searchChange($event));\n    })(\"ngModelChange\", function SearchComponent_ng_container_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.search = $event);\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\")(8, \"em\", 5);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_Template_em_mousedown_8_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.searchProduct($event, ctx_r47.search));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, SearchComponent_ng_container_1_ng_container_9_Template, 5, 2, \"ng-container\", 0);\n    i0.ɵɵtemplate(10, SearchComponent_ng_container_1_ng_container_10_Template, 9, 4, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r1.scConfig ? i0.ɵɵpipeBind1(5, 5, \"search.scSearchOnMoMoMarket\") : i0.ɵɵpipeBind1(6, 7, \"search.searchOnMoMoMarket\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.search)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.displayResult && !ctx_r1.showHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showHistory && ctx_r1.searchHistory.length > 0);\n  }\n}\nexport let SearchComponent = /*#__PURE__*/(() => {\n  class SearchComponent {\n    store;\n    productService;\n    messageService;\n    cd;\n    elementRef;\n    permissionService;\n    translate;\n    router;\n    $gaService;\n    customGAService;\n    search = '';\n    displayResult = false;\n    searchResult = [];\n    searchHistory = [];\n    showHistory = false;\n    productId = 0;\n    scConfig = false;\n    isMobileTemplate = false;\n    isGoogleAnalytics = false;\n    onResult = new EventEmitter();\n    inputField;\n    onDocumentClick(event) {\n      const clickedInside = this.elementRef.nativeElement.contains(event.target);\n      if (!clickedInside) {\n        this.showResultPopUp(false);\n        this.showHistory = false;\n      }\n    }\n    constructor(store, productService, messageService, cd, elementRef, permissionService, translate, router, $gaService, customGAService) {\n      this.store = store;\n      this.productService = productService;\n      this.messageService = messageService;\n      this.cd = cd;\n      this.elementRef = elementRef;\n      this.permissionService = permissionService;\n      this.translate = translate;\n      this.router = router;\n      this.$gaService = $gaService;\n      this.customGAService = customGAService;\n      this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      router.events.subscribe(event => {\n        if (event.navigationTrigger === 'popstate' || event.navigationTrigger === 'imperative' && !event.url?.includes('search')) {\n          this.search = null;\n        }\n      });\n      if (environment.isStoreCloud) {\n        this.scConfig = true;\n      } else {\n        this.scConfig = false;\n      }\n      this.router.routeReuseStrategy.shouldReuseRoute = function () {\n        return false;\n      };\n    }\n    ngOnInit() {\n      this.loadSearchHistory();\n    }\n    ngAfterViewInit() {\n      this.keyupSearch();\n      this.store.subscription('search').subscribe(res => {\n        this.search = res;\n      });\n    }\n    // Load search history from localStorage\n    loadSearchHistory() {\n      const history = localStorage.getItem('searchHistory');\n      if (history) {\n        this.searchHistory = JSON.parse(history);\n      }\n    }\n    // Save search history to localStorage\n    saveSearchHistory() {\n      localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));\n    }\n    // Add search term to history\n    addToSearchHistory(searchTerm) {\n      if (!searchTerm || searchTerm.trim() === '') return;\n      const trimmedTerm = searchTerm.trim();\n      // Remove if already exists to avoid duplicates\n      this.searchHistory = this.searchHistory.filter(term => term !== trimmedTerm);\n      // Add to beginning of array\n      this.searchHistory.unshift(trimmedTerm);\n      // Keep only last 10 searches\n      if (this.searchHistory.length > 10) {\n        this.searchHistory = this.searchHistory.slice(0, 10);\n      }\n      this.saveSearchHistory();\n    }\n    // Handle input focus to show history\n    onInputFocus() {\n      if (!this.search || this.search.trim() === '') {\n        this.showHistory = true;\n        this.displayResult = false;\n      }\n    }\n    // Handle selecting a search from history\n    selectFromHistory(searchTerm) {\n      this.search = searchTerm;\n      this.showHistory = false;\n      this.displayResult = false;\n      this.cd.detectChanges();\n      // Trigger search\n      const event = {\n        target: {\n          value: searchTerm\n        }\n      };\n      this.searchProduct(event);\n    }\n    // Clear search history\n    clearSearchHistory(event) {\n      if (event) {\n        event.stopPropagation();\n        event.preventDefault();\n      }\n      this.searchHistory = [];\n      this.saveSearchHistory();\n      this.showHistory = false;\n    }\n    // Remove specific item from history\n    removeFromHistory(searchTerm, event) {\n      event.stopPropagation();\n      this.searchHistory = this.searchHistory.filter(term => term !== searchTerm);\n      this.saveSearchHistory();\n      // If no history left, hide the dropdown\n      if (this.searchHistory.length === 0) {\n        this.showHistory = false;\n      }\n    }\n    searchProduct(event, searchKay) {\n      if (event?.target?.value) {\n        this.closeDropDown();\n        this.showHistory = false;\n        // Add to search history\n        this.addToSearchHistory(event.target.value);\n        this.router.navigate(['/search'], {\n          queryParams: {\n            q: event?.target?.value\n          }\n        });\n      }\n    }\n    showResultPopUp(show) {\n      this.displayResult = show;\n    }\n    NavigateToProduct(productId) {\n      const selectedProduct = this.searchResult.find(item => item.id === productId);\n      if (selectedProduct && this.isGoogleAnalytics) {\n        const item_id = selectedProduct.id;\n        const item_name = selectedProduct.name;\n        const search_term = this.search;\n        this.customGAService.selectItemEvent(item_id, item_name, search_term);\n      }\n      this.router.navigate(['product/' + productId + '/' + '1'], {\n        queryParams: {\n          tenantId: localStorage.getItem(\"tenantId\"),\n          lang: localStorage.getItem(\"lang\")\n        },\n        queryParamsHandling: 'merge'\n      });\n      this.showResultPopUp(false);\n    }\n    closeDropDown() {\n      this.showResultPopUp(false);\n      // this.showHistory = false;\n    }\n\n    searchChange(event) {\n      const regex = /^[^a-zA-Z0-9]+/;\n      this.search = event.replace(regex, '');\n      this.cd.detectChanges();\n      if (this.search === '') {\n        this.search = null;\n        this.showHistory = true;\n        this.displayResult = false;\n      } else {\n        this.showHistory = false;\n      }\n    }\n    keyupSearch() {\n      fromEvent(this.inputField?.nativeElement, 'input').pipe(debounceTime(500), map(event => event), distinctUntilChanged(), switchMap(event => {\n        if (event.target.value && event.target.value?.length > 2 && event.code != 'Enter') {\n          const query = event.target.value;\n          this.showHistory = false;\n          if (this.isGoogleAnalytics) {\n            this.$gaService.event(GaLocalActionEnum.CLICK_ON_SEARCH_BAR, 'search', 'SEARCH', 1, true, {\n              \"search_term\": query\n            });\n          }\n          return this.productService.FilterWithSearchProductName(query);\n        } else {\n          return Promise.resolve([]);\n        }\n      })).subscribe({\n        next: res => {\n          this.searchResult = res.data.products.records;\n          const SEARCHCOUNT = res.data.products.total;\n          this.showResultPopUp(true);\n          if (this.searchResult.length > 0 && this.isGoogleAnalytics) {\n            const searchOrigin = window.location.pathname;\n            this.customGAService.searchSubmittedEvent(this.search, SEARCHCOUNT, searchOrigin);\n          }\n          if (this.searchResult.length === 0 && this.isGoogleAnalytics) {\n            this.customGAService.searchNoResultEvent(this.search);\n          }\n        },\n        error: err => {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        },\n        complete: () => {}\n      });\n    }\n    static ɵfac = function SearchComponent_Factory(t) {\n      return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchComponent,\n      selectors: [[\"app-mtn-search\"]],\n      viewQuery: function SearchComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputField = _t.first);\n        }\n      },\n      hostBindings: function SearchComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SearchComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      outputs: {\n        onResult: \"onResult\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [1, \"product-search\", \"p-inputgroup\"], [\"autocomplete\", \"off\", 2, \"display\", \"contents\"], [\"autocapitalize\", \"off\", \"autocomplete\", \"new-password\", \"autocorrect\", \"off\", \"pInputText\", \"\", \"spellcheck\", \"false\", \"type\", \"text\", 3, \"ngModel\", \"ngModelOptions\", \"placeholder\", \"focus\", \"focusout\", \"keyup.enter\", \"ngModelChange\"], [\"inputField\", \"\"], [1, \"pi\", \"pi-search\", 3, \"mousedown\"], [1, \"search-results\"], [1, \"my-3\"], [\"class\", \"px-2 padding-y-5\", \"style\", \"cursor: pointer\", 3, \"mousedown\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ml-3\", 4, \"ngIf\"], [1, \"px-2\", \"padding-y-5\", 2, \"cursor\", \"pointer\", 3, \"mousedown\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"medium-font\", \"font-size-14\", \"large-name\"], [1, \"medium-font\", \"font-size-14\"], [1, \"d-flex\"], [1, \"prop-color\"], [1, \"main-color\", \"font-size-14\", \"mx-1\", 2, \"color\", \"#004f71\", \"font-weight\", \"bold\"], [1, \"ml-3\"], [1, \"search-results\", \"search-history\"], [1, \"history-header\", \"px-2\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"font-size-14\", \"font-weight-bold\"], [\"type\", \"button\", 1, \"clear-history-btn\", 3, \"mousedown\"], [1, \"pi\", \"pi-trash\", 2, \"color\", \"lightslategrey\"], [\"class\", \"history-item px-2 py-2 d-flex justify-content-between align-items-center\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"history-item\", \"px-2\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [1, \"pi\", \"pi-clock\", \"mr-2\", \"history-icon\"], [1, \"font-size-14\"], [\"type\", \"button\", 1, \"remove-item-btn\", 3, \"mousedown\"], [1, \"pi\", \"pi-times\", 2, \"color\", \"lightslategrey\"], [1, \"product-search-mobile\", \"p-inputgroup\"]],\n      template: function SearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SearchComponent_ng_container_0_Template, 11, 10, \"ng-container\", 0);\n          i0.ɵɵtemplate(1, SearchComponent_ng_container_1_Template, 11, 10, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isMobileTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate);\n        }\n      },\n      dependencies: [i7.Divider, i8.NgForOf, i8.NgIf, i9.NativeElementInjectorDirective, i10.ɵNgNoValidate, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgControlStatusGroup, i10.NgModel, i10.NgForm, i3.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.product-search[_ngcontent-%COMP%]{position:relative;width:100%;background:#f5f5f5 0 0 no-repeat padding-box!important;box-shadow:0 2px 4px #00000003!important;border:unset;padding:10px 28px;color:#989898!important;font-size:12px;font-style:normal;font-weight:500;line-height:normal;z-index:2;border-radius:8px;font-family:var(--regular-font)}.product-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{display:flex;width:100%;flex-direction:column;align-items:flex-start;gap:4px;background:#f5f5f5 0 0 no-repeat padding-box!important}.product-search[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::placeholder{color:#989898!important;font-size:10px;font-weight:700;opacity:1}.product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]{background-color:#f5f5f5}.product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{color:#000}.product-search[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]{display:block;position:absolute;width:100%;height:auto;top:39px;left:0;max-height:50vh;overflow-y:auto;background-color:#fff;border:1px solid rgba(151,151,151,.168627451);border-radius:5px;z-index:4}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:1px solid #e9ecef}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]   .clear-history-btn[_ngcontent-%COMP%]{background:none;border:none;color:#6c757d;cursor:pointer;padding:2px 4px;border-radius:4px;font-size:12px;transition:all .2s ease}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]   .clear-history-btn[_ngcontent-%COMP%]:hover{background-color:#e9ecef;color:#495057}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]{cursor:pointer;transition:background-color .2s ease;border-bottom:1px solid #f1f3f4}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .history-icon[_ngcontent-%COMP%]{color:#6c757d;font-size:12px}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .remove-item-btn[_ngcontent-%COMP%]{background:none;border:none;color:#6c757d;cursor:pointer;padding:2px 4px;border-radius:3px;font-size:10px;opacity:0;transition:all .2s ease}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .remove-item-btn[_ngcontent-%COMP%]:hover{background-color:#e9ecef;color:#dc3545}.product-search[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]:hover   .remove-item-btn[_ngcontent-%COMP%]{opacity:1}.product-search[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{position:fixed;width:100%;height:100%;top:0;left:0;background-color:#00000080;opacity:.4}.product-search[_ngcontent-%COMP%]   .prop-color[_ngcontent-%COMP%]{color:#a3a3a3}.product-search-mobile[_ngcontent-%COMP%]{position:relative;width:100%;background:#f5f5f5 0 0 no-repeat padding-box!important;box-shadow:0 2px 4px #00000003!important;border:unset;padding:10px 28px;color:#989898!important;font-size:12px;font-style:normal;font-weight:500;line-height:normal;z-index:2;border-radius:8px;font-family:var(--regular-font)}.product-search-mobile[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{display:flex;width:100%;flex-direction:column;align-items:flex-start;gap:4px;background:#f5f5f5 0 0 no-repeat padding-box!important}.product-search-mobile[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::placeholder{color:#989898!important;font-size:10px;font-weight:700;opacity:1}.product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]{background-color:#f5f5f5}.product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{color:#000}.product-search-mobile[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]{display:block;position:absolute;width:100%;height:auto;top:39px;left:0;max-height:50vh;overflow-y:auto;background-color:#fff;border:1px solid rgba(151,151,151,.168627451);border-radius:5px;z-index:4}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]{background-color:#f8f9fa}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]   .clear-history-btn[_ngcontent-%COMP%]{background:none;border:none;color:#6c757d;cursor:pointer;padding:2px 4px;border-radius:4px;font-size:12px;transition:all .2s ease}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]   .clear-history-btn[_ngcontent-%COMP%]:hover{background-color:#e9ecef;color:#495057}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]{cursor:pointer;transition:background-color .2s ease;border-bottom:1px solid #f1f3f4}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .history-icon[_ngcontent-%COMP%]{color:#6c757d;font-size:12px}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .remove-item-btn[_ngcontent-%COMP%]{background-color:transparent;border:none;cursor:pointer;padding:2px 4px;border-radius:3px;font-size:9px}.product-search-mobile[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{position:fixed;width:100%;height:100%;top:0;left:0;background-color:#00000080;opacity:.4}.product-search-mobile[_ngcontent-%COMP%]   .prop-color[_ngcontent-%COMP%]{color:#a3a3a3}.large-name[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:76%}@media only screen and (max-width: 767px){.product-search-mobile[_ngcontent-%COMP%]{z-index:auto!important;height:52px;width:100%!important;border-radius:25px}.product-search-mobile[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]{top:51px!important}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]{padding:8px 12px}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-header[_ngcontent-%COMP%]   .clear-history-btn[_ngcontent-%COMP%]{padding:6px 10px;font-size:14px}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]{padding:12px}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .history-icon[_ngcontent-%COMP%]{font-size:14px}.product-search-mobile[_ngcontent-%COMP%]   .search-history[_ngcontent-%COMP%]   .history-item[_ngcontent-%COMP%]   .remove-item-btn[_ngcontent-%COMP%]{padding:6px 8px;font-size:12px}}\"]\n    });\n  }\n  return SearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}