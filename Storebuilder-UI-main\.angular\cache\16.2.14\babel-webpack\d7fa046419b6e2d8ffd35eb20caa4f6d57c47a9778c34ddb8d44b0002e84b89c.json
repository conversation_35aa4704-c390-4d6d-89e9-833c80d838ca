{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class LiveStreamService {\n  http;\n  baseUrl;\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}`;\n  }\n  getLiveMerchantData() {\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetLiveMerchants`);\n  }\n  getLiveStreamDetailById(shopId) {\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetStreamDetail/${shopId}`);\n  }\n  static ɵfac = function LiveStreamService_Factory(t) {\n    return new (t || LiveStreamService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LiveStreamService,\n    factory: LiveStreamService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "LiveStreamService", "http", "baseUrl", "constructor", "apiEndPoint", "getLiveMerchantData", "get", "getLiveStreamDetailById", "shopId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\livestream.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { environment } from '@environments/environment';\r\nimport { Observable} from 'rxjs';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class LiveStreamService {\r\n  baseUrl: string;\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}`;\r\n  }\r\n\r\n  getLiveMerchantData(): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetLiveMerchants`);\r\n  }\r\n  \r\n  getLiveStreamDetailById(shopId:any): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetStreamDetail/${shopId}`);\r\n  }\r\n\r\n}"], "mappings": "AAEA,SAASA,WAAW,QAAQ,2BAA2B;;;AAOvD,OAAM,MAAOC,iBAAiB;EAGlBC,IAAA;EAFVC,OAAO;EACPC,YACUF,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAEZ,IAAI,CAACC,OAAO,GAAG,GAAGH,WAAW,CAACK,WAAW,EAAE;EAC7C;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,oCAAoC,CAAC;EAC3E;EAEAK,uBAAuBA,CAACC,MAAU;IAChC,OAAO,IAAI,CAACP,IAAI,CAACK,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,qCAAqCM,MAAM,EAAE,CAAC;EACpF;;qBAdWR,iBAAiB,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;WAAjBZ,iBAAiB;IAAAa,OAAA,EAAjBb,iBAAiB,CAAAc,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}