import { Routes } from '@angular/router';
import { CategoryContainerComponent } from './category-container/category-container.component';
import { CategoryListingMenuComponent } from './category-listing-menu/category-listing-menu.component';
import { CategoryProductsComponent } from './category-products/category-products.component';

export const categoryRoutes: Routes = [
  { path: 'categories', component: CategoryContainerComponent },
  
  { path: 'products/:id', component: CategoryProductsComponent },
  
  { path: 'feature/:id/:topNumber/:categoryName', component: CategoryProductsComponent },
  
  { path: 'promotion/:id', component: CategoryProductsComponent },
  
  { path: 'list', component: CategoryListingMenuComponent },
  
  { path: 'browse', component: CategoryListingMenuComponent },
  
  { path: ':id', component: CategoryProductsComponent },
  
  { path: '', component: CategoryProductsComponent },
  
  { path: '**', redirectTo: '' }
];
