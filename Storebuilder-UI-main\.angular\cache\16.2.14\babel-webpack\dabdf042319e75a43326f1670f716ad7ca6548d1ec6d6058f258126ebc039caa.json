{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { register } from 'swiper/element/bundle';\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { CarouselModule } from \"ngx-owl-carousel-o\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nregister();\nexport let HomeModule = /*#__PURE__*/(() => {\n  class HomeModule {\n    static ɵfac = function HomeModule_Factory(t) {\n      return new (t || HomeModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, TranslateModule, RouterModule.forChild(routes), CarouselModule, InitialModule]\n    });\n  }\n  return HomeModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}