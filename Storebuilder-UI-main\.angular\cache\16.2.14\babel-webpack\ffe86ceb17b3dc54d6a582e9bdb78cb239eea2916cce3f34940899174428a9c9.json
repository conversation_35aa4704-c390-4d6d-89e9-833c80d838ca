{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-google-analytics\";\nimport * as i3 from \"@core/services\";\nexport class RouteInterceptor {\n  constructor(router, route, $gaService, store, permissionService) {\n    this.router = router;\n    this.route = route;\n    this.$gaService = $gaService;\n    this.store = store;\n    this.permissionService = permissionService;\n    this.isGoogleAnalytics = false;\n    this.userDetails = this.store.get('profile');\n    this.sessionId = localStorage.getItem('sessionId');\n    this.fullUrl = window.location.href;\n    this.route.queryParams.subscribe(params => {\n      this.tenantId = params.tenantId;\n    });\n    this.route.params.subscribe(params => {\n      this.productId = params.id;\n    });\n  }\n  intercept(request, next) {\n    // Intercept request here\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature('page_visited')) {\n      this.$gaService.event(\"page_visited\", '', '', 1, true, {\n        \"product_ID\": this.productId ? this.productId : null,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        \"session_ID\": this.sessionId,\n        \"tenant_ID\": this.tenantId,\n        \"visitedURL\": this.fullUrl\n      });\n    }\n    // Pass the request to the next handler\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function RouteInterceptor_Factory(t) {\n    return new (t || RouteInterceptor)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i1.ActivatedRoute), i0.ɵɵinject(i2.GoogleAnalyticsService), i0.ɵɵinject(i3.StoreService), i0.ɵɵinject(i3.PermissionService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RouteInterceptor,\n    factory: RouteInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["RouteInterceptor", "constructor", "router", "route", "$gaService", "store", "permissionService", "isGoogleAnalytics", "userDetails", "get", "sessionId", "localStorage", "getItem", "fullUrl", "window", "location", "href", "queryParams", "subscribe", "params", "tenantId", "productId", "id", "intercept", "request", "next", "hasPermission", "getTagFeature", "event", "mobileNumber", "handle", "_", "i0", "ɵɵinject", "i1", "Router", "ActivatedRoute", "i2", "GoogleAnalyticsService", "i3", "StoreService", "PermissionService", "_2", "factory", "ɵfac"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interceptors\\route.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n  HttpHandler,\r\n  HttpRequest,\r\n} from '@angular/common/http';\r\nimport {filter, Observable} from 'rxjs';\r\nimport {ActivatedRoute, NavigationEnd, Router} from \"@angular/router\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport {PermissionService, StoreService} from \"@core/services\";\r\n@Injectable()\r\nexport class RouteInterceptor implements HttpInterceptor {\r\n  userDetails: any;\r\n  sessionId: string | null;\r\n  fullUrl: any;\r\n  tenantId: any;\r\n  productId: any;\r\n  isGoogleAnalytics: boolean = false;\r\n\r\n  constructor(private router: Router,private route:ActivatedRoute,private $gaService: GoogleAnalyticsService,private store: StoreService,private permissionService: PermissionService) {\r\n    this.userDetails = this.store.get('profile');\r\n    this.sessionId = localStorage.getItem('sessionId');\r\n    this.fullUrl = window.location.href;\r\n    this.route.queryParams.subscribe((params: any) => {\r\n      this.tenantId = params.tenantId;\r\n    });\r\n    this.route.params.subscribe(params => {\r\n      this.productId = params.id;\r\n    });\r\n\r\n  }\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    // Intercept request here\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    if(this.isGoogleAnalytics && this.permissionService.getTagFeature('page_visited')){\r\n      this.$gaService.event(\"page_visited\", '','',1,true,{\"product_ID\":this.productId?this.productId:null,\"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated',\"session_ID\":this.sessionId,\"tenant_ID\":this.tenantId,\"visitedURL\":this.fullUrl});\r\n    }\r\n\r\n\r\n    // Pass the request to the next handler\r\n    return next.handle(request);\r\n  }\r\n}\r\n"], "mappings": ";;;;AAYA,OAAM,MAAOA,gBAAgB;EAQ3BC,YAAoBC,MAAc,EAASC,KAAoB,EAASC,UAAkC,EAASC,KAAmB,EAASC,iBAAoC;IAA/J,KAAAJ,MAAM,GAANA,MAAM;IAAiB,KAAAC,KAAK,GAALA,KAAK;IAAwB,KAAAC,UAAU,GAAVA,UAAU;IAAiC,KAAAC,KAAK,GAALA,KAAK;IAAuB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAFhK,KAAAC,iBAAiB,GAAY,KAAK;IAGhC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,KAAK,CAACI,GAAG,CAAC,SAAS,CAAC;IAC5C,IAAI,CAACC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAClD,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IACnC,IAAI,CAACb,KAAK,CAACc,WAAW,CAACC,SAAS,CAAEC,MAAW,IAAI;MAC/C,IAAI,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IACjC,CAAC,CAAC;IACF,IAAI,CAACjB,KAAK,CAACgB,MAAM,CAACD,SAAS,CAACC,MAAM,IAAG;MACnC,IAAI,CAACE,SAAS,GAAGF,MAAM,CAACG,EAAE;IAC5B,CAAC,CAAC;EAEJ;EACAC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjB;IACA,IAAI,CAAClB,iBAAiB,GAAG,IAAI,CAACD,iBAAiB,CAACoB,aAAa,CAAC,kBAAkB,CAAC;IAEjF,IAAG,IAAI,CAACnB,iBAAiB,IAAI,IAAI,CAACD,iBAAiB,CAACqB,aAAa,CAAC,cAAc,CAAC,EAAC;MAChF,IAAI,CAACvB,UAAU,CAACwB,KAAK,CAAC,cAAc,EAAE,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,IAAI,EAAC;QAAC,YAAY,EAAC,IAAI,CAACP,SAAS,GAAC,IAAI,CAACA,SAAS,GAAC,IAAI;QAAC,SAAS,EAAC,IAAI,CAACb,WAAW,GAAC,IAAI,CAACA,WAAW,CAACqB,YAAY,GAAC,kBAAkB;QAAC,YAAY,EAAC,IAAI,CAACnB,SAAS;QAAC,WAAW,EAAC,IAAI,CAACU,QAAQ;QAAC,YAAY,EAAC,IAAI,CAACP;MAAO,CAAC,CAAC;;IAInQ;IACA,OAAOY,IAAI,CAACK,MAAM,CAACN,OAAO,CAAC;EAC7B;EAAC,QAAAO,CAAA,G;qBAlCU/B,gBAAgB,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAJ,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAAC,sBAAA,GAAAN,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAR,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAE,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhB1C,gBAAgB;IAAA2C,OAAA,EAAhB3C,gBAAgB,CAAA4C;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}