
import {Component, Inject, Input, OnInit, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import {MainSlider} from "@core/interface";
import UtilityFunctions from "@core/utilities/functions";
import {environment} from "@environments/environment";
import {isPlatformBrowser} from "@angular/common";
import { Carousel } from 'primeng/carousel';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { GoogleAnalyticsService } from 'ngx-google-analytics';

@Component({
  selector: 'app-mtn-main-slider',
  templateUrl: './mtn-main-slider.component.html',
  styleUrls: ['./mtn-main-slider.component.scss']
})
export class MtnMainSliderComponent implements OnInit{
  innerWidth?: number;
  mobileScreen: boolean = false;
  @Input() sliders: Array<MainSlider> = [];
  sliderDots: any = [];

  constructor(private router: Router, @Inject(PLATFORM_ID) private platformId: any,
  private $gaService:GoogleAnalyticsService) {
    Carousel.prototype.onTouchMove = () => { };
 
  }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.innerWidth = window.innerWidth;
      if (this.innerWidth < 768) {
        this.mobileScreen = true;
      } else {
        this.mobileScreen = false;
      }
    }
    this.sliders.map((x: any, index: number) => {
      this.sliderDots.push({
        index,
        selected: index === 0
      })
    })
  }
  routeToCTA(banner: any, url: string = '') {
    if (!url) return;

    if (isPlatformBrowser(this.platformId)) {
      this.$gaService.event(
        GaLocalActionEnum.CLICK_ON_BANNERS,
        '',
        'BANNERS_ON_HOMEPAGE',
        1,
        true,
        {
          bannerId: banner.promotionId || url.split('/').pop(),
          redirectPage: url
        }
      );

      let finalUrl = url.trim();

      if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
        if (finalUrl.startsWith('www.')) {
          finalUrl = 'https://' + finalUrl;
        } else if (finalUrl.startsWith('/')) {
          // internal route
          finalUrl = window.location.origin + finalUrl;
        } else {
          finalUrl = 'https://' + finalUrl;
        }
      }

      // ✅ If promotion
      if (banner.promotionId) {
        if (banner.CTALink) {
          const cta = banner.CTALink.replace(/promotions\//g, 'promotion/');
          if (!cta.startsWith('http://') && !cta.startsWith('https://')) {
            window.location.href = 'https://' + cta;
          } else {
            window.location.href = cta;
          }
        } else {
          const tempurl =
            'https://' +
            environment.marketPlaceHostName +
            '/promotion/' +
            banner.promotionId;
          window.location.href = tempurl;
        }
      } else {
        window.location.href = finalUrl;
      }
    }
  }

  getBannerImages(url: string) {
    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);
  }
  changeSliderDot(event: any) {
    this.sliderDots.forEach((obj: any) => {
      obj.selected = obj.index == event.page;
    });
  }

}

