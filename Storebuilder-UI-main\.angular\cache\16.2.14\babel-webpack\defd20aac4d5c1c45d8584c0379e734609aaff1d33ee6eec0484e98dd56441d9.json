{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { SharedModule } from \"../../shared/modules/shared.module\";\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputMaskModule } from \"primeng/inputmask\";\nlet ResetPasswordModule = class ResetPasswordModule {};\nResetPasswordModule = __decorate([NgModule({\n  declarations: [IndexComponent],\n  imports: [CommonModule, SharedModule, PasswordModule, InputTextModule, InputMaskModule, RouterModule.forChild(routes)]\n})], ResetPasswordModule);\nexport { ResetPasswordModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IndexComponent", "RouterModule", "routes", "SharedModule", "PasswordModule", "InputTextModule", "InputMaskModule", "ResetPasswordModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\reset-password\\reset-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {SharedModule} from \"../../shared/modules/shared.module\";\r\nimport {PasswordModule} from 'primeng/password';\r\nimport {InputTextModule} from 'primeng/inputtext';\r\nimport {InputMaskModule} from \"primeng/inputmask\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    PasswordModule,\r\n    InputTextModule,\r\n    InputMaskModule,\r\n    RouterModule.forChild(routes)\r\n  ]\r\n})\r\nexport class ResetPasswordModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,YAAY,QAAO,oCAAoC;AAC/D,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,eAAe,QAAO,mBAAmB;AAe1C,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB,GAAI;AAAvBA,mBAAmB,GAAAC,UAAA,EAb/BV,QAAQ,CAAC;EACRW,YAAY,EAAE,CACZT,cAAc,CACf;EACDU,OAAO,EAAE,CACPX,YAAY,EACZI,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfL,YAAY,CAACU,QAAQ,CAACT,MAAM,CAAC;CAEhC,CAAC,C,EACWK,mBAAmB,CAAI;SAAvBA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}