{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { PanelModule } from \"primeng/panel\";\nimport { TreeModule } from \"primeng/tree\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SearchModule {\n  static ɵfac = function SearchModule_Factory(t) {\n    return new (t || SearchModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SearchModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, InitialModule, RouterModule.forChild(routes), RippleModule, TranslateModule, PanelModule, TreeModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SearchModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, InitialModule, i1.RouterModule, RippleModule, TranslateModule, PanelModule, TreeModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IndexComponent", "RouterModule", "routes", "RippleModule", "TranslateModule", "InitialModule", "PanelModule", "TreeModule", "SearchModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\search\\search.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { routes } from \"./routes\";\r\nimport { RippleModule } from \"primeng/ripple\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nimport {PanelModule} from \"primeng/panel\";\r\nimport {TreeModule} from \"primeng/tree\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    InitialModule,\r\n    RouterModule.forChild(routes),\r\n    RippleModule,\r\n    TranslateModule,\r\n    PanelModule,\r\n    TreeModule\r\n  ]\r\n})\r\nexport class SearchModule {\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,aAAa,QAAO,gCAAgC;AAC5D,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,UAAU,QAAO,cAAc;;;AAgBvC,OAAM,MAAOC,YAAY;;qBAAZA,YAAY;EAAA;;UAAZA;EAAY;;cATrBT,YAAY,EACZM,aAAa,EACbJ,YAAY,CAACQ,QAAQ,CAACP,MAAM,CAAC,EAC7BC,YAAY,EACZC,eAAe,EACfE,WAAW,EACXC,UAAU;EAAA;;;2EAGDC,YAAY;IAAAE,YAAA,GAZrBV,cAAc;IAAAW,OAAA,GAGdZ,YAAY,EACZM,aAAa,EAAAO,EAAA,CAAAX,YAAA,EAEbE,YAAY,EACZC,eAAe,EACfE,WAAW,EACXC,UAAU;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}