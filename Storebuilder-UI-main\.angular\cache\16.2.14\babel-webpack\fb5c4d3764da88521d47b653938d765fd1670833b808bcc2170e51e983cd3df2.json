{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AgeRestrictionComponent {\n  constructor() {\n    this.restrictionMessage = '';\n  }\n  static #_ = this.ɵfac = function AgeRestrictionComponent_Factory(t) {\n    return new (t || AgeRestrictionComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AgeRestrictionComponent,\n    selectors: [[\"app-age-restriction\"]],\n    inputs: {\n      restrictionMessage: \"restrictionMessage\"\n    },\n    decls: 7,\n    vars: 1,\n    consts: [[1, \"app-age-restriction\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", 1, \"age-restriction-icon\"], [\"d\", \"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\", \"stroke\", \"#191C1F\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M11.25 11.25H12V16.5H12.75\", \"stroke\", \"#191C1F\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M11.8125 9C12.4338 9 12.9375 8.49632 12.9375 7.875C12.9375 7.25368 12.4338 6.75 11.8125 6.75C11.1912 6.75 10.6875 7.25368 10.6875 7.875C10.6875 8.49632 11.1912 9 11.8125 9Z\", \"fill\", \"#191C1F\"], [1, \"age-restriction-text\"]],\n    template: function AgeRestrictionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(1, \"svg\", 1);\n        i0.ɵɵelement(2, \"path\", 2)(3, \"path\", 3)(4, \"path\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(5, \"p\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", ctx.restrictionMessage, \" \");\n      }\n    },\n    styles: [\"\\n\\n.app-age-restriction[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 10px;\\n  padding: 10px;\\n  border-radius: 5px;\\n  background-color: #fff;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\\n  \\n\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.age-restriction-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  flex-shrink: 0;\\n  fill: none;\\n  stroke: #191c1f;\\n  stroke-width: 1.5;\\n  stroke-linecap: round;\\n  stroke-linejoin: round;\\n}\\n\\n\\n\\n.age-restriction-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #191c1f;\\n  line-height: 1.5;\\n  margin: 0;\\n  \\n\\n}\\n@media (max-width: 768px) {\\n  .age-restriction-text[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvYWdlLXJlc3RyaWN0aW9uL2FnZS1yZXN0cmljdGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw2Q0FBQTtBQUNBO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0Esd0NBQUE7RUFFQSx3REFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtBQUFKOztBQUdFLHlCQUFBO0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxVQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0EscUJBQUE7RUFDQSxzQkFBQTtBQUFKOztBQUdFLHlCQUFBO0FBQ0E7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUVBLDBDQUFBO0FBREo7QUFFSTtFQVBGO0lBUUksZUFBQTtFQUNKO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBEaXJlY3RseSB0YXJnZXQgdGhlIHJvb3QgY29tcG9uZW50IGNsYXNzICovXHJcbi5hcHAtYWdlLXJlc3RyaWN0aW9uIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgIGdhcDogMTBweDtcclxuICAgIHBhZGRpbmc6IDEwcHg7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1cHg7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICBcclxuICAgIC8qIEVuc3VyZSB0aGUgY29tcG9uZW50IGFkYXB0cyB0byB0aGUgcGFyZW50IGNvbnRhaW5lciAqL1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gIH1cclxuICBcclxuICAvKiBTdHlsaW5nIGZvciB0aGUgaWNvbiAqL1xyXG4gIC5hZ2UtcmVzdHJpY3Rpb24taWNvbiB7XHJcbiAgICB3aWR0aDogMjRweDtcclxuICAgIGhlaWdodDogMjRweDtcclxuICAgIGZsZXgtc2hyaW5rOiAwO1xyXG4gICAgZmlsbDogbm9uZTtcclxuICAgIHN0cm9rZTogIzE5MWMxZjtcclxuICAgIHN0cm9rZS13aWR0aDogMS41O1xyXG4gICAgc3Ryb2tlLWxpbmVjYXA6IHJvdW5kO1xyXG4gICAgc3Ryb2tlLWxpbmVqb2luOiByb3VuZDtcclxuICB9XHJcbiAgXHJcbiAgLyogU3R5bGluZyBmb3IgdGhlIHRleHQgKi9cclxuICAuYWdlLXJlc3RyaWN0aW9uLXRleHQge1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgY29sb3I6ICMxOTFjMWY7XHJcbiAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gIFxyXG4gICAgLyogUmVzcG9uc2l2ZSBkZXNpZ24gZm9yIHNtYWxsZXIgc2NyZWVucyAqL1xyXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgIH1cclxuICB9XHJcbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["AgeRestrictionComponent", "constructor", "restrictionMessage", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "AgeRestrictionComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\age-restriction\\age-restriction.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\age-restriction\\age-restriction.component.html"], "sourcesContent": ["import { Component,Input } from '@angular/core';\n\n@Component({\n  selector: 'app-age-restriction',\n  templateUrl: './age-restriction.component.html',\n  styleUrls: ['./age-restriction.component.scss']\n})\nexport class AgeRestrictionComponent {\n  @Input() restrictionMessage: string = '';\n}\n", "<div class=\"app-age-restriction\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" class=\"age-restriction-icon\">\n      <path d=\"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\" stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n      <path d=\"M11.25 11.25H12V16.5H12.75\" stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n      <path d=\"M11.8125 9C12.4338 9 12.9375 8.49632 12.9375 7.875C12.9375 7.25368 12.4338 6.75 11.8125 6.75C11.1912 6.75 10.6875 7.25368 10.6875 7.875C10.6875 8.49632 11.1912 9 11.8125 9Z\" fill=\"#191C1F\" />\n    </svg>\n    <p class=\"age-restriction-text\">\n        {{ restrictionMessage }}\n    </p>\n  </div>\n"], "mappings": ";AAOA,OAAM,MAAOA,uBAAuB;EALpCC,YAAA;IAMW,KAAAC,kBAAkB,GAAW,EAAE;;EACzC,QAAAC,CAAA,G;qBAFYH,uBAAuB;EAAA;EAAA,QAAAI,EAAA,G;UAAvBJ,uBAAuB;IAAAK,SAAA;IAAAC,MAAA;MAAAJ,kBAAA;IAAA;IAAAK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPpCE,EAAA,CAAAC,cAAA,aAAiC;QAC7BD,EAAA,CAAAE,cAAA,EAA4H;QAA5HF,EAAA,CAAAC,cAAA,aAA4H;QAC1HD,EAAA,CAAAG,SAAA,cAAiN;QAGnNH,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAAK,eAAA,EAAgC;QAAhCL,EAAA,CAAAC,cAAA,WAAgC;QAC5BD,EAAA,CAAAM,MAAA,GACJ;QAAAN,EAAA,CAAAI,YAAA,EAAI;;;QADAJ,EAAA,CAAAO,SAAA,GACJ;QADIP,EAAA,CAAAQ,kBAAA,MAAAT,GAAA,CAAAX,kBAAA,MACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}