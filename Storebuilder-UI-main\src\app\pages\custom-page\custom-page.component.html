<main>
  <ng-container *ngFor="let eachSection of sectionsData">
    <section class="main-container" *ngIf="eachSection.products.length > 0">
      <!-- Section header on top -->
      <div class="d-flex justify-content-between align-items-center">
        <h2>{{ eachSection.sectionName }}</h2>
        <div
          *ngIf="
            eachSection.productsLimit &&
            eachSection.productsLimit < eachSection.productsTotal
          "
          (click)="
            redirectFeaturedProducts(
              eachSection.sectionName,
              eachSection.sectionId
            )
          "
          class="see-all"
        >
          {{ "landing.seeMore" | translate }}
        </div>
      </div>

      <!-- Products section - keeping original structure -->
      <div class="each-product">
        <app-mtn-product-card
          class="product-card"
          *ngFor="let product of getLimitedProducts(eachSection)"
          [product]="product"
        />
      </div>
    </section>

   <section
  class="main-container banner-container"
  [ngClass]="'banners-' + eachSection.banners.length"
  *ngIf="eachSection.banners?.length"
>
  <div
    class="single-banner"
    *ngFor="let banner of eachSection.banners"
    (click)="goToNewLink(formatURL(banner.redirectionURL))"
  >
    <img [src]="banner.desktopBanner" alt="Desktop Banner" class="banner-desktop" />
    <img [src]="banner.mobileBanner" alt="Mobile Banner" class="banner-mobile" />
  </div>
</section>

  </ng-container>
</main>
