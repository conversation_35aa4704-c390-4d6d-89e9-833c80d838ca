{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core/services/gtm.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/carousel\";\nimport * as i7 from \"@shared/components/landing-templates/promotion-banner/promotion-banner.component\";\nimport * as i8 from \"@shared/components/landing-templates/promotion-vertical/promotion-vertical.component\";\nimport * as i9 from \"@shared/components/landing-templates/feature-products/feature-products.component\";\nimport * as i10 from \"@shared/components/mtn-main-slider/mtn-main-slider.component\";\nimport * as i11 from \"@shared/modals/flash-sale-modal/flash-sale-modal.component\";\nfunction TemplateOneComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"app-mtn-main-slider\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r0.mainBanner);\n  }\n}\nfunction TemplateOneComponent_section_2_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-promotion-banner\", 24);\n  }\n  if (rf & 2) {\n    const banner_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"banner\", banner_r14);\n  }\n}\nfunction TemplateOneComponent_section_2_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 22);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_2_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"responsiveOptions\", ctx_r11.rowOnePromotionResponsiveConfig)(\"value\", ctx_r11.rowOnePromotions)(\"circular\", false)(\"autoplayInterval\", 0)(\"showIndicators\", false)(\"showNavigators\", false);\n  }\n}\nfunction TemplateOneComponent_section_2_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-promotion-banner\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const banner_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banner\", banner_r16);\n  }\n}\nfunction TemplateOneComponent_section_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_2_ng_container_3_div_1_Template, 2, 1, \"div\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.rowOnePromotions);\n  }\n}\nfunction TemplateOneComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 18)(1, \"div\", 19);\n    i0.ɵɵtemplate(2, TemplateOneComponent_section_2_p_carousel_2_Template, 2, 6, \"p-carousel\", 20);\n    i0.ɵɵtemplate(3, TemplateOneComponent_section_2_ng_container_3_Template, 2, 1, \"ng-container\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth <= 767);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth > 767);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    minHeight: a0\n  };\n};\nfunction TemplateOneComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"app-promotion-vertical\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c0, ctx_r2.isMobileLayout && ctx_r2.isMobileView ? \"600px\" : \"inherit\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banners\", ctx_r2.rowTwoPromotions);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"template-one__feature__feature-products-max\": a0\n  };\n};\nfunction TemplateOneComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"app-feature-products\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx_r3.rowTwoPromotions.length == 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"featureId\", ctx_r3.firstFeaturedProducts.id)(\"isLeftVerticalBanner\", ctx_r3.rowTwoPromotions.length == 0 ? true : false)(\"products\", ctx_r3.firstFeaturedProducts.data)(\"showLoader\", ctx_r3.firstFeaturedProducts.showLoader)(\"title\", ctx_r3.firstFeaturedProducts.title);\n  }\n}\nfunction TemplateOneComponent_section_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 31)(1, \"div\", 4)(2, \"div\", 32);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r4.bestSellerMobileBanner);\n  }\n}\nfunction TemplateOneComponent_section_8_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-promotion-banner\", 24);\n  }\n  if (rf & 2) {\n    const banner_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"banner\", banner_r20);\n  }\n}\nfunction TemplateOneComponent_section_8_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 22);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_8_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"responsiveOptions\", ctx_r17.rowThreePromotionResponsiveConfig)(\"value\", ctx_r17.rowThreePromotions)(\"circular\", false)(\"autoplayInterval\", 0)(\"showIndicators\", false)(\"showNavigators\", false);\n  }\n}\nfunction TemplateOneComponent_section_8_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"app-promotion-banner\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const banner_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banner\", banner_r22);\n  }\n}\nfunction TemplateOneComponent_section_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_8_ng_container_3_div_1_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.rowThreePromotions);\n  }\n}\nfunction TemplateOneComponent_section_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 34)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, TemplateOneComponent_section_8_p_carousel_2_Template, 2, 6, \"p-carousel\", 20);\n    i0.ɵɵtemplate(3, TemplateOneComponent_section_8_ng_container_3_Template, 2, 1, \"ng-container\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.screenWidth <= 767);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.screenWidth > 767);\n  }\n}\nfunction TemplateOneComponent_app_feature_products_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-feature-products\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"featureId\", ctx_r6.secondFeaturedProducts.id)(\"isRightVerticalBanner\", ctx_r6.rowFourPromotions.length == 0 ? true : false)(\"products\", ctx_r6.secondFeaturedProducts.data)(\"showLoader\", ctx_r6.firstFeaturedProducts.showLoader)(\"title\", ctx_r6.secondFeaturedProducts.title);\n  }\n}\nfunction TemplateOneComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"app-promotion-vertical\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banners\", ctx_r7.rowFourPromotions);\n  }\n}\nfunction TemplateOneComponent_section_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 31)(1, \"div\", 4)(2, \"div\", 32);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r8.bestOfferMobileBanner);\n  }\n}\nfunction TemplateOneComponent_section_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 31)(1, \"div\", 4)(2, \"div\", 32);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r9.rowFivePromotions);\n  }\n}\nfunction TemplateOneComponent_section_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 40);\n    i0.ɵɵelement(1, \"app-feature-products\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"featureId\", ctx_r10.thirdFeaturedProducts.id)(\"fullRowProducts\", true)(\"isNewArrival\", true)(\"products\", ctx_r10.thirdFeaturedProducts.data)(\"showLoader\", ctx_r10.firstFeaturedProducts.showLoader)(\"title\", ctx_r10.thirdFeaturedProducts.title);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"template-one__feature-2__feature-products-max\": a0\n  };\n};\nexport class TemplateOneComponent {\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n      this.isMobileView = this.screenWidth <= 768;\n    }\n  }\n  constructor(appDataService, productService, tenantService, router, permissionService, $gtmService, platformId) {\n    this.appDataService = appDataService;\n    this.productService = productService;\n    this.tenantService = tenantService;\n    this.router = router;\n    this.permissionService = permissionService;\n    this.$gtmService = $gtmService;\n    this.platformId = platformId;\n    this.topNumber = 8;\n    // Main Banner\n    this.mainBanner = [];\n    // Featured Products\n    this.firstFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    this.secondFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    this.thirdFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    this.fourthFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    // Promotions\n    this.rowOnePromotions = [];\n    this.rowTwoPromotions = [];\n    this.rowThreePromotions = [];\n    this.rowFourPromotions = [];\n    this.displayFlashSaleModal = false;\n    this.isMobileView = window.innerWidth <= 768;\n    this.isMobileLayout = false;\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n    this.rowOnePromotionResponsiveConfig = [{\n      breakpoint: '2400px',\n      numVisible: 3,\n      numScroll: 0\n    }, {\n      breakpoint: '991px',\n      numVisible: 2,\n      numScroll: 1\n    }, {\n      breakpoint: '767px',\n      numVisible: 2,\n      numScroll: 1\n    }];\n    this.rowThreePromotionResponsiveConfig = [{\n      breakpoint: '1199px',\n      numVisible: 3,\n      numScroll: 0\n    }, {\n      breakpoint: '991px',\n      numVisible: 2,\n      numScroll: 1\n    }, {\n      breakpoint: '767px',\n      numVisible: 1.5,\n      numScroll: 1\n    }];\n  }\n  ngOnInit() {\n    this.$gtmService.pushPageView('Home');\n    this.fetchFeaturedProducts();\n    this.fetchPromotions();\n    this.fetchMainBanner();\n    this.fetchFlashSale();\n  }\n  fetchFeaturedProducts() {\n    const allFeatureProducts = this.appDataService.layoutTemplate.filter(section => section.type === 'feature_product');\n    if (allFeatureProducts.length) {\n      allFeatureProducts.forEach(item => {\n        if (item.isActive) {\n          switch (item.data) {\n            // Hot Deals Section\n            case '1':\n              if (this.tenantService.isAllowedCached.isHotDeals) {\n                this.getFeatureProducts(parseInt(item.data), \"first\");\n              } else {\n                this.firstFeaturedProducts = localStorage.getItem('firstFeaturedProducts') ? JSON.parse(localStorage.getItem('firstFeaturedProducts') ?? '') : [];\n              }\n              break;\n            // New Arrivals Section\n            case '2':\n              if (this.tenantService.isAllowedCached.isNewArrivals) {\n                this.getFeatureProducts(parseInt(item.data), \"second\");\n              } else {\n                this.secondFeaturedProducts = localStorage.getItem('secondFeaturedProducts') ? JSON.parse(localStorage.getItem('secondFeaturedProducts') ?? '') : [];\n              }\n              break;\n            // Bestsellers Section\n            case '3':\n              if (this.tenantService.isAllowedCached.isBestSeller) {\n                this.getFeatureProducts(parseInt(item.data), \"third\");\n              } else {\n                this.thirdFeaturedProducts = localStorage.getItem('thirdFeaturedProducts') ? JSON.parse(localStorage.getItem('thirdFeaturedProducts') ?? '') : [];\n              }\n              break;\n          }\n        } else {\n          this.firstFeaturedProducts.showLoader = false;\n          this.secondFeaturedProducts.showLoader = false;\n          this.thirdFeaturedProducts.showLoader = false;\n        }\n      });\n    }\n  }\n  // fetchPromotions() {\n  //   const allPromotions = this.appDataService.layoutTemplate.filter((section: any) => section.type === 'promotion')\n  //   allPromotions.forEach((promotion: any) => {\n  //     if (promotion.name === 'Promotion Card Left' || promotion.name === 'Promotion Card Middle' || promotion.name === 'Promotion Card Right') {\n  //       const data: any = JSON.parse(promotion.data)\n  //       data.ctaLink = data.CTALink\n  //       data.isActive = promotion.isActive\n  //       if (data.isActive) {\n  //         this.rowOnePromotions.push(data)\n  //       }\n  //     }\n  //     if (promotion.name === 'Discount Banner Left') {\n  //       const data: any = JSON.parse(promotion.data)\n  //       data.ctaLink = data.CTALink\n  //       data.isActive = promotion.isActive\n  //       if (data.isActive) {\n  //         this.rowTwoPromotions.push(data)\n  //       }\n  //\n  //     }\n  //     if (promotion.name === 'Discount Banner Middle Left' || promotion.name === 'Discount Banner Middle Right') {\n  //       const data: any = JSON.parse(promotion.data)\n  //       data.ctaLink = data.CTALink\n  //       data.isActive = promotion.isActive\n  //       if (data.isActive) {\n  //         this.rowThreePromotions.push(data)\n  //       }\n  //\n  //     }\n  //     if (promotion.name === 'Discount Banner Right Top' || promotion.name === 'Discount Banner Right Bottom') {\n  //       const data: any = JSON.parse(promotion.data)\n  //       data.ctaLink = data.CTALink\n  //       data.isActive = promotion.isActive\n  //       if (data.isActive) {\n  //         this.rowFourPromotions.push(data)\n  //       }\n  //\n  //     }\n  //     if (promotion.name === 'Discount Banner Bottom') {\n  //       const data: any = JSON.parse(promotion.data)\n  //       data.ctaLink = data.CTALink\n  //       data.isActive = promotion.isActive\n  //       if (data.isActive) {\n  //         this.rowFivePromotions = data;\n  //       }\n  //\n  //     }\n  //   })\n  //\n  // }\n  fetchPromotions() {\n    const allPromotions = this.appDataService.layoutTemplate.filter(section => section.type === 'promotion');\n    allPromotions.forEach(promotion => {\n      const data = JSON.parse(promotion.data);\n      data.ctaLink = data.CTALink;\n      data.isActive = promotion.isActive;\n      if (data.isActive) {\n        data['promotionId'] = promotion.promotionId;\n        switch (promotion.name) {\n          case 'Promotion Card Left':\n          case 'Promotion Card Middle':\n          case 'Promotion Card Right':\n            this.rowOnePromotions.push(data);\n            break;\n          case 'Discount Banner Left':\n            this.rowTwoPromotions.push(data);\n            break;\n          case 'Discount Banner Middle Left':\n          case 'Discount Banner Middle Right':\n            this.rowThreePromotions.push(data);\n            break;\n          case 'Discount Banner Right Top':\n          case 'Discount Banner Right Bottom':\n            this.rowFourPromotions.push(data);\n            break;\n          case 'Discount Banner Bottom':\n            this.rowFivePromotions = data;\n            break;\n        }\n      }\n    });\n    const bestSellerMobileBanner = this.appDataService.layoutTemplate.find(section => section.type === 'best_seller_banner');\n    if (bestSellerMobileBanner) {\n      const data = JSON.parse(bestSellerMobileBanner.data);\n      data.ctaLink = data.CTALink;\n      data.isActive = bestSellerMobileBanner.isActive;\n      this.bestSellerMobileBanner = data;\n    }\n    const bestOfferMobileBanner = this.appDataService.layoutTemplate.find(section => section.type === 'best_offers_banner');\n    if (bestOfferMobileBanner) {\n      const data = JSON.parse(bestOfferMobileBanner.data);\n      data.ctaLink = data.CTALink;\n      data.isActive = bestOfferMobileBanner.isActive;\n      this.bestOfferMobileBanner = data;\n    }\n  }\n  fetchMainBanner() {\n    const allBanners = this.appDataService.layoutTemplate.find(section => section.type === 'main_banner');\n    if (allBanners) {\n      if (allBanners?.isActive) this.mainBanner = JSON.parse(allBanners.data);\n      if (this.mainBanner.length > 0) {\n        this.mainBanner = this.mainBanner.filter(item => !item.isMerchantbanner);\n      }\n      this.mainBanner = this.filterInactivebanners(this.mainBanner);\n    }\n  }\n  filterInactivebanners(tempData) {\n    let currentDate = Date.now();\n    let data = tempData.filter(banner => {\n      let endDate = new Date(banner.endDate).getTime();\n      let startDate = new Date(banner.startDate).getTime();\n      if (!banner.promotionId) {\n        if (!endDate) {\n          return banner;\n        } else {\n          if (endDate && endDate >= currentDate && startDate && startDate <= currentDate) {\n            return banner;\n          }\n        }\n      }\n      if (banner.promotionId && endDate && endDate >= currentDate && startDate && startDate <= currentDate) {\n        return banner;\n      }\n    });\n    return data;\n  }\n  getFeatureProducts(featureId, featureSequence) {\n    let featureTopNumber = 8;\n    let featureShowOutOfStock = false;\n    const showRoomRecords = this.appDataService.showRoomConfiguration.records;\n    if (showRoomRecords.length) {\n      const featureRecord = showRoomRecords.find(res => res.showRoomTypeId == 2 && res.featureProduct == featureId);\n      if (featureRecord && featureRecord?.topNumber) {\n        featureTopNumber = featureRecord.topNumber;\n      }\n      if (featureRecord && featureRecord?.showOutOfStock) {\n        featureShowOutOfStock = featureRecord.showOutOfStock;\n      }\n    }\n    let pageSize = featureTopNumber;\n    this.productService.GetAllProductsByFeature(featureId, featureTopNumber, false, 1, pageSize, false, featureShowOutOfStock, true).subscribe({\n      next: res => {\n        if (res?.data?.records?.length) {\n          const tempProducts = [];\n          res.data?.records.forEach(record => {\n            this.addTempProduct(res, tempProducts, record, featureSequence, featureId, featureTopNumber);\n          });\n        } else {\n          this.setFeatureProducts(featureSequence, featureId, [], res?.data?.featureName, featureTopNumber);\n        }\n      }\n    });\n  }\n  fetchFlashSale() {\n    this.flashSale = this.appDataService.layoutTemplate.find(section => section.description === 'Flash Sale');\n    const visited = localStorage.getItem('visited');\n    if (this.flashSale?.isActive && this.flashSale?.promotionId && !visited && this.flashSale?.data) {\n      this.flashSaleData = JSON.parse(this.flashSale.data);\n      this.displayFlashSaleModal = true;\n    }\n  }\n  addTempProduct(res, tempProducts, record, featureSequence, featureId, featureTopNumber) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => !variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    if (selectedVariance) {\n      let features = selectedVariance?.productFeaturesList[0]?.featureList;\n      let product = {\n        productId: record?.id,\n        productName: record?.name,\n        isLiked: record?.isLiked,\n        priceValue: selectedVariance?.price,\n        priceId: selectedVariance?.priceId,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: record?.currencyCode,\n        masterImageUrl: selectedVariance.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : ''),\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: record.shopId,\n        specProductId: selectedVariance.specProductId,\n        channelId: record.channelId ?? '1',\n        isHot: !!features?.includes(1),\n        isNew: !!features?.includes(2),\n        isBest: !!features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        stockStatus: selectedVariance.stockStatus,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated\n      };\n      if (product.salePriceValue) {\n        product.salePercent = 100 - product.salePriceValue / product.priceValue * 100;\n      }\n      tempProducts.push(product);\n    }\n    this.setFeatureProducts(featureSequence, featureId, tempProducts, res?.data?.featureName, featureTopNumber);\n  }\n  setFeatureProducts(featureSequence, featureId, tempProducts, featureName, topNumberLimit = 8) {\n    // todo: this slicing needs to be discussed may be this one is may be handled from styling\n    if (featureSequence === 'first') {\n      let firstProductsCount = this.rowTwoPromotions.length > 0 ? topNumberLimit : 10;\n      this.firstFeaturedProducts = {\n        id: featureId,\n        data: tempProducts.slice(0, firstProductsCount),\n        title: featureName,\n        showLoader: false,\n        isActive: true\n      };\n      localStorage.setItem('firstFeaturedProducts', JSON.stringify(this.firstFeaturedProducts));\n    } else if (featureSequence === 'second') {\n      let secondProductsCount = this.rowFourPromotions.length > 0 ? topNumberLimit : 10;\n      this.secondFeaturedProducts = {\n        id: featureId,\n        data: tempProducts.slice(0, secondProductsCount),\n        title: featureName,\n        showLoader: false,\n        isActive: true\n      };\n      localStorage.setItem('secondFeaturedProducts', JSON.stringify(this.secondFeaturedProducts));\n    } else if (featureSequence === 'third') {\n      this.thirdFeaturedProducts = {\n        id: featureId,\n        data: tempProducts.slice(0, topNumberLimit),\n        title: featureName,\n        showLoader: false,\n        isActive: true\n      };\n      localStorage.setItem('thirdFeaturedProducts', JSON.stringify(this.thirdFeaturedProducts));\n    } else if (featureSequence === 'fourth') {\n      this.fourthFeaturedProducts = {\n        id: featureId,\n        data: tempProducts.slice(0, topNumberLimit),\n        title: featureName,\n        showLoader: false,\n        isActive: true\n      };\n      localStorage.setItem('fourthFeaturedProducts', JSON.stringify(this.fourthFeaturedProducts));\n    }\n  }\n  onFlashCancel() {\n    this.displayFlashSaleModal = false;\n  }\n  routeToCTA() {\n    this.displayFlashSaleModal = false;\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.flashSale.promotionId) {\n        const data = JSON.parse(this.flashSale.data);\n        let tempurl;\n        // if(data.promotionName) {\n        //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + data.promotionName;\n        // } else{\n        //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + this.flashSale.promotionId;\n        // }\n        // window.open(tempurl, '_blank');\n        this.router.navigate([`/promotion/${data.promotionName}`]);\n      } else {\n        window.open(this.flashSaleData.CTALink, '_blank');\n      }\n    }\n  }\n  static #_ = this.ɵfac = function TemplateOneComponent_Factory(t) {\n    return new (t || TemplateOneComponent)(i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i1.TenantService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TemplateOneComponent,\n    selectors: [[\"app-landing-template-one\"]],\n    hostBindings: function TemplateOneComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function TemplateOneComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 19,\n    vars: 19,\n    consts: [[1, \"template-one\"], [\"class\", \"template-one__slider\", 4, \"ngIf\"], [\"class\", \"template-one__promotion\", 4, \"ngIf\"], [1, \"template-one__feature\", 3, \"ngStyle\"], [1, \"d-flex\", \"justify-content-space-between\"], [\"class\", \"template-one__feature__promotion-vertical d-inline-flex\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"template-one__feature__feature-products d-inline-flex pl-3\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"template-one__promotion-large\", 4, \"ngIf\"], [\"class\", \"template-one__promotion-medium\", 4, \"ngIf\"], [1, \"template-one__feature-2\"], [1, \"template-one__feature-2__feature-products\", \"d-inline-flex\", \"pr-3\", 3, \"ngClass\"], [3, \"featureId\", \"isRightVerticalBanner\", \"products\", \"showLoader\", \"title\", 4, \"ngIf\"], [\"class\", \"template-one__feature-2__promotion-vertical d-inline-flex\", 4, \"ngIf\"], [\"class\", \"template-one__feature-single\", 4, \"ngIf\"], [3, \"data\", \"displayModal\", \"submit\", \"cancel\"], [1, \"template-one__slider\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"sliders\"], [1, \"template-one__promotion\"], [1, \"d-flex\", \"justify-content-around\", \"mobile-second-banner\"], [3, \"responsiveOptions\", \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"showNavigators\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"responsiveOptions\", \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"showNavigators\"], [\"pTemplate\", \"item\"], [1, \"w-100\", \"promotion-banner-small\", 3, \"banner\"], [\"class\", \"template-one__promotion__promotion-content d-inline-flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-one__promotion__promotion-content\", \"d-inline-flex\"], [1, \"template-one__feature__promotion-vertical\", \"d-inline-flex\", 3, \"ngStyle\"], [3, \"banners\"], [1, \"template-one__feature__feature-products\", \"d-inline-flex\", \"pl-3\", 3, \"ngClass\"], [3, \"featureId\", \"isLeftVerticalBanner\", \"products\", \"showLoader\", \"title\"], [1, \"template-one__promotion-large\"], [1, \"template-one__promotion-large__promotion-content\", \"d-inline-flex\"], [1, \"w-100\", 3, \"banner\"], [1, \"template-one__promotion-medium\"], [1, \"d-flex\", \"justify-content-around\"], [\"class\", \"template-one__promotion-medium__promotion-content d-inline-flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-one__promotion-medium__promotion-content\", \"d-inline-flex\"], [3, \"featureId\", \"isRightVerticalBanner\", \"products\", \"showLoader\", \"title\"], [1, \"template-one__feature-2__promotion-vertical\", \"d-inline-flex\"], [1, \"template-one__feature-single\"], [3, \"featureId\", \"fullRowProducts\", \"isNewArrival\", \"products\", \"showLoader\", \"title\"]],\n    template: function TemplateOneComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0, 0);\n        i0.ɵɵtemplate(1, TemplateOneComponent_section_1_Template, 3, 1, \"section\", 1);\n        i0.ɵɵtemplate(2, TemplateOneComponent_section_2_Template, 4, 2, \"section\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, TemplateOneComponent_div_5_Template, 2, 4, \"div\", 5);\n        i0.ɵɵtemplate(6, TemplateOneComponent_div_6_Template, 2, 8, \"div\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, TemplateOneComponent_section_7_Template, 4, 1, \"section\", 7);\n        i0.ɵɵtemplate(8, TemplateOneComponent_section_8_Template, 4, 2, \"section\", 8);\n        i0.ɵɵelementStart(9, \"section\", 9)(10, \"div\", 4)(11, \"div\", 10);\n        i0.ɵɵtemplate(12, TemplateOneComponent_app_feature_products_12_Template, 1, 5, \"app-feature-products\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, TemplateOneComponent_div_13_Template, 2, 1, \"div\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(14, TemplateOneComponent_section_14_Template, 4, 1, \"section\", 7);\n        i0.ɵɵtemplate(15, TemplateOneComponent_section_15_Template, 4, 1, \"section\", 7);\n        i0.ɵɵtemplate(16, TemplateOneComponent_section_16_Template, 2, 6, \"section\", 13);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(17);\n        i0.ɵɵelementStart(18, \"app-flash-sale-modal\", 14);\n        i0.ɵɵlistener(\"submit\", function TemplateOneComponent_Template_app_flash_sale_modal_submit_18_listener() {\n          return ctx.routeToCTA();\n        })(\"cancel\", function TemplateOneComponent_Template_app_flash_sale_modal_cancel_18_listener() {\n          return ctx.onFlashCancel();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mainBanner.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.rowOnePromotions.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(15, _c0, ctx.isMobileLayout && ctx.isMobileView ? \"600px\" : \"inherit\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.screenWidth > 1200 && ctx.rowTwoPromotions.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.firstFeaturedProducts.isActive);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.bestSellerMobileBanner == null ? null : ctx.bestSellerMobileBanner.isActive);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.rowThreePromotions.length > 0);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c2, ctx.rowFourPromotions.length == 0));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.secondFeaturedProducts.isActive);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.screenWidth > 1200 && ctx.rowFourPromotions.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.bestOfferMobileBanner == null ? null : ctx.bestOfferMobileBanner.isActive);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.rowFivePromotions);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.thirdFeaturedProducts.isActive);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.flashSaleData)(\"displayModal\", ctx.displayFlashSaleModal);\n      }\n    },\n    dependencies: [i4.PrimeTemplate, i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i6.Carousel, i7.PromotionBannerComponent, i8.PromotionVerticalComponent, i9.FeatureProductsComponent, i10.MtnMainSliderComponent, i11.FlashSaleModalComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.template-one__slider[_ngcontent-%COMP%] {\\n  height: 330px;\\n}\\n.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  height: inherit;\\n}\\n.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__slider[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n}\\n.template-one__promotion[_ngcontent-%COMP%] {\\n  height: 250px;\\n  margin-top: 30px;\\n}\\n.template-one__promotion__promotion-content[_ngcontent-%COMP%] {\\n  width: 444px;\\n  margin-right: 15px;\\n}\\n.template-one__promotion__promotion-content[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n.template-one__promotion__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n  height: 250px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion[_ngcontent-%COMP%] {\\n    height: 120px;\\n    margin-top: 37px;\\n  }\\n}\\n.template-one__promotion-medium[_ngcontent-%COMP%] {\\n  height: 350px;\\n  margin-top: 30px;\\n}\\n.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%] {\\n  width: 688px;\\n  margin-right: 15px;\\n}\\n.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n  height: 350px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n    height: 115px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion-medium[_ngcontent-%COMP%] {\\n    height: 115px;\\n  }\\n}\\n.template-one__promotion-large[_ngcontent-%COMP%] {\\n  height: 400px;\\n  margin-top: 30px;\\n}\\n.template-one__promotion-large__promotion-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-right: 15px;\\n}\\n.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n  height: 400px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion-large__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%] {\\n    height: 165px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__promotion-large[_ngcontent-%COMP%] {\\n    height: 165px;\\n  }\\n}\\n.template-one__feature[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  height: auto;\\n}\\n.template-one__feature__promotion-vertical[_ngcontent-%COMP%] {\\n  width: 15%;\\n  height: inherit;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature__promotion-vertical[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature__promotion-vertical[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n.template-one__feature__promotion-vertical[_ngcontent-%COMP%]   app-promotion-vertical[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.template-one__feature__feature-products[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n  width: 85%;\\n}\\n.template-one__feature__feature-products[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature__feature-products[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature__feature-products[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.template-one__feature__feature-products-max[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n.template-one__feature-2[_ngcontent-%COMP%] {\\n  min-height: 600px;\\n  height: auto;\\n  margin-top: 30px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature-2[_ngcontent-%COMP%] {\\n    min-height: 300px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature-2[_ngcontent-%COMP%] {\\n    min-height: 300px;\\n  }\\n}\\n.template-one__feature-2__promotion-vertical[_ngcontent-%COMP%] {\\n  width: 15%;\\n  height: 600px;\\n}\\n.template-one__feature-2__promotion-vertical[_ngcontent-%COMP%]   app-promotion-vertical[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.template-one__feature-2__feature-products[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n  width: 85%;\\n}\\n.template-one__feature-2__feature-products[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature-2__feature-products[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature-2__feature-products[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.template-one__feature-2__feature-products-max[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature-2[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature-2[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n.template-one__feature-single[_ngcontent-%COMP%] {\\n  min-height: 530px;\\n  height: auto;\\n  margin-top: 30px;\\n}\\n.template-one__feature-single[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature-single[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature-single[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n  }\\n  .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n  .template-one__feature[_ngcontent-%COMP%] {\\n    margin-top: 13px !important;\\n  }\\n  .template-one__feature-2[_ngcontent-%COMP%] {\\n    margin-top: 13px !important;\\n  }\\n  .template-one__feature-single[_ngcontent-%COMP%] {\\n    margin-top: 13px !important;\\n  }\\n  .mobile-second-banner[_ngcontent-%COMP%] {\\n    display: block !important;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n  .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n  .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  .template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n  .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    padding-left: 0px !important;\\n    margin-right: 0px !important;\\n    padding-right: 0px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "isPlatformBrowser", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "mainBanner", "banner_r14", "ɵɵtemplate", "TemplateOneComponent_section_2_p_carousel_2_ng_template_1_Template", "ctx_r11", "rowOnePromotionResponsiveConfig", "rowOnePromotions", "banner_r16", "ɵɵelementContainerStart", "TemplateOneComponent_section_2_ng_container_3_div_1_Template", "ɵɵelementContainerEnd", "ctx_r12", "TemplateOneComponent_section_2_p_carousel_2_Template", "TemplateOneComponent_section_2_ng_container_3_Template", "ctx_r1", "screenWidth", "ɵɵpureFunction1", "_c0", "ctx_r2", "isMobileLayout", "isMobile<PERSON>iew", "rowTwoPromotions", "_c1", "ctx_r3", "length", "firstFeaturedProducts", "id", "data", "<PERSON><PERSON><PERSON><PERSON>", "title", "ctx_r4", "bestSellerMobileBanner", "banner_r20", "TemplateOneComponent_section_8_p_carousel_2_ng_template_1_Template", "ctx_r17", "rowThreePromotionResponsiveConfig", "rowThreePromotions", "banner_r22", "TemplateOneComponent_section_8_ng_container_3_div_1_Template", "ctx_r18", "TemplateOneComponent_section_8_p_carousel_2_Template", "TemplateOneComponent_section_8_ng_container_3_Template", "ctx_r5", "ctx_r6", "secondFeaturedProducts", "rowFourPromotions", "ctx_r7", "ctx_r8", "bestOfferMobileBanner", "ctx_r9", "rowFivePromotions", "ctx_r10", "thirdFeaturedProducts", "TemplateOneComponent", "onResize", "event", "platformId", "window", "innerWidth", "constructor", "appDataService", "productService", "tenantService", "router", "permissionService", "$gtmService", "topNumber", "isActive", "fourthFeaturedProducts", "displayFlashSaleModal", "hasPermission", "breakpoint", "numVisible", "numScroll", "ngOnInit", "pushPageView", "fetchFeaturedProducts", "fetchPromotions", "fetchMainBanner", "fetchFlashSale", "allFeatureProducts", "layoutTemplate", "filter", "section", "type", "for<PERSON>ach", "item", "isAllowedCached", "isHotDeals", "getFeatureProducts", "parseInt", "localStorage", "getItem", "JSON", "parse", "isNewArrivals", "isBestSeller", "allPromotions", "promotion", "ctaLink", "CTALink", "promotionId", "name", "push", "find", "allBanners", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterInactivebanners", "tempData", "currentDate", "Date", "now", "banner", "endDate", "getTime", "startDate", "featureId", "featureSequence", "featureTopNumber", "featureShowOutOfStock", "showRoomRecords", "showRoomConfiguration", "records", "featureRecord", "res", "showRoomTypeId", "featureProduct", "showOutOfStock", "pageSize", "GetAllProductsByFeature", "subscribe", "next", "tempProducts", "record", "addTempProduct", "setFeatureProducts", "featureName", "flashSale", "description", "visited", "flashSaleData", "<PERSON><PERSON><PERSON><PERSON>", "defaultVariant", "productVariances", "variant", "isDefault", "approvedVariant", "soldOut", "features", "productFeaturesList", "featureList", "product", "productId", "productName", "isLiked", "priceValue", "price", "priceId", "salePriceValue", "salePrice", "currencyCode", "masterImageUrl", "images", "thumbnailImages", "rate", "count", "salePercent", "shopId", "specProductId", "channelId", "isHot", "includes", "isNew", "isBest", "quantity", "proSchedulingId", "stockPerSKU", "stockStatus", "sku", "skuAutoGenerated", "topNumberLimit", "firstProductsCount", "slice", "setItem", "stringify", "secondProductsCount", "onFlashCancel", "routeToCTA", "tempurl", "navigate", "promotionName", "open", "_", "ɵɵdirectiveInject", "i1", "AppDataService", "ProductService", "TenantService", "i2", "Router", "PermissionService", "i3", "GTMService", "_2", "selectors", "hostBindings", "TemplateOneComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "TemplateOneComponent_section_1_Template", "TemplateOneComponent_section_2_Template", "TemplateOneComponent_div_5_Template", "TemplateOneComponent_div_6_Template", "TemplateOneComponent_section_7_Template", "TemplateOneComponent_section_8_Template", "TemplateOneComponent_app_feature_products_12_Template", "TemplateOneComponent_div_13_Template", "TemplateOneComponent_section_14_Template", "TemplateOneComponent_section_15_Template", "TemplateOneComponent_section_16_Template", "ɵɵlistener", "TemplateOneComponent_Template_app_flash_sale_modal_submit_18_listener", "TemplateOneComponent_Template_app_flash_sale_modal_cancel_18_listener", "_c2"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\landing-templates\\template-one\\template-one.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\landing-templates\\template-one\\template-one.component.html"], "sourcesContent": ["import {Component, HostListener, Inject, PLATFORM_ID} from '@angular/core';\r\nimport { Promotion } from \"@core/interface\";\r\nimport {AppDataService, PermissionService, ProductService, TenantService} from \"@core/services\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {environment} from \"@environments/environment\";\r\nimport {Router} from \"@angular/router\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n@Component({\r\n  selector: 'app-landing-template-one',\r\n  templateUrl: './template-one.component.html',\r\n  styleUrls: ['./template-one.component.scss']\r\n})\r\nexport class TemplateOneComponent {\r\n  screenWidth: number;\r\n  topNumber: number = 8;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n\r\n  // Main Banner\r\n  mainBanner: any = [];\r\n\r\n  // Featured Products\r\n  firstFeaturedProducts: any = { id: 0, data: [], title: '',isActive:false, showLoader: true };\r\n  secondFeaturedProducts: any = { id: 0, data: [], title: '',isActive:false, showLoader: true };\r\n  thirdFeaturedProducts: any = { id: 0, data: [], title: '',isActive:false, showLoader: true };\r\n  fourthFeaturedProducts: any = { id: 0, data: [], title: '',isActive:false, showLoader: true };\r\n\r\n  // Promotions\r\n  rowOnePromotions: Promotion[] = [];\r\n  rowTwoPromotions: Promotion[] = [];\r\n  rowThreePromotions: Promotion[] = [];\r\n  rowFourPromotions: Promotion[] = [];\r\n  rowFivePromotions: Promotion;\r\n  bestSellerMobileBanner: Promotion;\r\n  bestOfferMobileBanner: Promotion;\r\n\r\n  rowOnePromotionResponsiveConfig: any[];\r\n  rowThreePromotionResponsiveConfig: any[];\r\n  displayFlashSaleModal:boolean=false;\r\n  flashSaleData:any;\r\n  flashSale:any;\r\n  isMobileView = window.innerWidth <= 768;\r\n  isMobileLayout: boolean = false;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth;\r\n      this.isMobileView = this.screenWidth <= 768;\r\n    }\r\n  }\r\n\r\n  constructor(private appDataService: AppDataService,\r\n    private productService: ProductService, public tenantService: TenantService, private router: Router,\r\n              private permissionService: PermissionService,\r\n              private $gtmService:GTMService,\r\n              @Inject(PLATFORM_ID) private platformId: any) {\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n    this.rowOnePromotionResponsiveConfig = [\r\n      {\r\n        breakpoint: '2400px',\r\n        numVisible: 3,\r\n        numScroll: 0\r\n      },\r\n      {\r\n        breakpoint: '991px',\r\n        numVisible: 2,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '767px',\r\n        numVisible: 2,\r\n        numScroll: 1\r\n      }\r\n    ];\r\n    this.rowThreePromotionResponsiveConfig = [\r\n      {\r\n        breakpoint: '1199px',\r\n        numVisible: 3,\r\n        numScroll: 0\r\n      },\r\n      {\r\n        breakpoint: '991px',\r\n        numVisible: 2,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '767px',\r\n        numVisible: 1.5,\r\n        numScroll: 1\r\n      }\r\n    ];\r\n  }\r\n\r\n\r\n  ngOnInit(): any {\r\n    this.$gtmService.pushPageView('Home')\r\n    this.fetchFeaturedProducts()\r\n    this.fetchPromotions()\r\n    this.fetchMainBanner()\r\n    this.fetchFlashSale()\r\n\r\n  }\r\n\r\n\r\n  fetchFeaturedProducts(){\r\n    const allFeatureProducts = this.appDataService.layoutTemplate.filter((section: any) => section.type === 'feature_product')\r\n\r\n    if(allFeatureProducts.length) {\r\n      allFeatureProducts.forEach((item: any) => {\r\n        if(item.isActive){\r\n          switch(item.data) {\r\n            // Hot Deals Section\r\n            case '1':\r\n              if(this.tenantService.isAllowedCached.isHotDeals) {\r\n                this.getFeatureProducts(parseInt(item.data), \"first\")\r\n              } else {\r\n                this.firstFeaturedProducts = localStorage.getItem('firstFeaturedProducts') ?\r\n                JSON.parse(localStorage.getItem('firstFeaturedProducts') ?? '') : [];\r\n              }\r\n              break;\r\n            // New Arrivals Section\r\n            case '2':\r\n              if(this.tenantService.isAllowedCached.isNewArrivals){\r\n                this.getFeatureProducts(parseInt(item.data), \"second\")\r\n        \r\n              } else {\r\n                this.secondFeaturedProducts = localStorage.getItem('secondFeaturedProducts') ?\r\n                JSON.parse(localStorage.getItem('secondFeaturedProducts') ?? '') : [];\r\n              }\r\n              break;\r\n            // Bestsellers Section\r\n            case '3':\r\n              if(this.tenantService.isAllowedCached.isBestSeller){\r\n                this.getFeatureProducts(parseInt(item.data), \"third\")\r\n              } else {\r\n                this.thirdFeaturedProducts = localStorage.getItem('thirdFeaturedProducts') ?\r\n                JSON.parse(localStorage.getItem('thirdFeaturedProducts') ?? '') : [];\r\n              }\r\n              break;\r\n          }\r\n        } else {\r\n          this.firstFeaturedProducts.showLoader = false;\r\n          this.secondFeaturedProducts.showLoader = false;\r\n          this.thirdFeaturedProducts.showLoader = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // fetchPromotions() {\r\n  //   const allPromotions = this.appDataService.layoutTemplate.filter((section: any) => section.type === 'promotion')\r\n  //   allPromotions.forEach((promotion: any) => {\r\n  //     if (promotion.name === 'Promotion Card Left' || promotion.name === 'Promotion Card Middle' || promotion.name === 'Promotion Card Right') {\r\n  //       const data: any = JSON.parse(promotion.data)\r\n  //       data.ctaLink = data.CTALink\r\n  //       data.isActive = promotion.isActive\r\n  //       if (data.isActive) {\r\n  //         this.rowOnePromotions.push(data)\r\n  //       }\r\n  //     }\r\n  //     if (promotion.name === 'Discount Banner Left') {\r\n  //       const data: any = JSON.parse(promotion.data)\r\n  //       data.ctaLink = data.CTALink\r\n  //       data.isActive = promotion.isActive\r\n  //       if (data.isActive) {\r\n  //         this.rowTwoPromotions.push(data)\r\n  //       }\r\n  //\r\n  //     }\r\n  //     if (promotion.name === 'Discount Banner Middle Left' || promotion.name === 'Discount Banner Middle Right') {\r\n  //       const data: any = JSON.parse(promotion.data)\r\n  //       data.ctaLink = data.CTALink\r\n  //       data.isActive = promotion.isActive\r\n  //       if (data.isActive) {\r\n  //         this.rowThreePromotions.push(data)\r\n  //       }\r\n  //\r\n  //     }\r\n  //     if (promotion.name === 'Discount Banner Right Top' || promotion.name === 'Discount Banner Right Bottom') {\r\n  //       const data: any = JSON.parse(promotion.data)\r\n  //       data.ctaLink = data.CTALink\r\n  //       data.isActive = promotion.isActive\r\n  //       if (data.isActive) {\r\n  //         this.rowFourPromotions.push(data)\r\n  //       }\r\n  //\r\n  //     }\r\n  //     if (promotion.name === 'Discount Banner Bottom') {\r\n  //       const data: any = JSON.parse(promotion.data)\r\n  //       data.ctaLink = data.CTALink\r\n  //       data.isActive = promotion.isActive\r\n  //       if (data.isActive) {\r\n  //         this.rowFivePromotions = data;\r\n  //       }\r\n  //\r\n  //     }\r\n  //   })\r\n  //\r\n  // }\r\n\r\n\r\n  fetchPromotions() {\r\n    const allPromotions = this.appDataService.layoutTemplate.filter((section: any) => section.type === 'promotion');\r\n    allPromotions.forEach((promotion: any) => {\r\n      const data: any = JSON.parse(promotion.data);\r\n      data.ctaLink = data.CTALink;\r\n      data.isActive = promotion.isActive;\r\n\r\n      if (data.isActive) {\r\n        data['promotionId']=promotion.promotionId;\r\n        switch (promotion.name) {\r\n          case 'Promotion Card Left':\r\n          case 'Promotion Card Middle':\r\n          case 'Promotion Card Right':\r\n            this.rowOnePromotions.push(data);\r\n            break;\r\n\r\n          case 'Discount Banner Left':\r\n            this.rowTwoPromotions.push(data);\r\n            break;\r\n\r\n          case 'Discount Banner Middle Left':\r\n          case 'Discount Banner Middle Right':\r\n            this.rowThreePromotions.push(data);\r\n            break;\r\n\r\n          case 'Discount Banner Right Top':\r\n          case 'Discount Banner Right Bottom':\r\n            this.rowFourPromotions.push(data);\r\n            break;\r\n\r\n          case 'Discount Banner Bottom':\r\n            this.rowFivePromotions = data;\r\n            break;\r\n        }\r\n      }\r\n    });\r\n\r\n    const bestSellerMobileBanner = this.appDataService.layoutTemplate.find((section: any) => section.type === 'best_seller_banner');\r\n    if(bestSellerMobileBanner) {\r\n      const data: any = JSON.parse(bestSellerMobileBanner.data);\r\n      data.ctaLink = data.CTALink;\r\n      data.isActive = bestSellerMobileBanner.isActive;\r\n      this.bestSellerMobileBanner = data;\r\n    }\r\n\r\n    const bestOfferMobileBanner = this.appDataService.layoutTemplate.find((section: any) => section.type === 'best_offers_banner');\r\n    if(bestOfferMobileBanner) {\r\n      const data: any = JSON.parse(bestOfferMobileBanner.data);\r\n      data.ctaLink = data.CTALink;\r\n      data.isActive = bestOfferMobileBanner.isActive;\r\n      this.bestOfferMobileBanner = data;\r\n    }\r\n\r\n  }\r\n\r\n\r\n  fetchMainBanner() {\r\n\r\n    const allBanners = this.appDataService.layoutTemplate.find((section: any) => section.type === 'main_banner')\r\n    if (allBanners) {\r\n      if (allBanners?.isActive)\r\n        this.mainBanner = JSON.parse(allBanners.data)\r\n      if (this.mainBanner.length > 0) {\r\n        this.mainBanner = this.mainBanner.filter((item: any) => !item.isMerchantbanner);\r\n      }\r\n      this.mainBanner=this.filterInactivebanners(this.mainBanner);\r\n    }\r\n  }\r\n  filterInactivebanners(tempData:any){\r\n    let currentDate:any=Date.now();\r\n    let data=tempData.filter((banner:any)=> {\r\n\r\n      let endDate=new Date(banner.endDate).getTime()\r\n      let startDate=new Date(banner.startDate).getTime()\r\n      if(!banner.promotionId){\r\n        if(!endDate){\r\n          return banner;\r\n          }\r\n          else{\r\n          if(endDate && endDate>=currentDate && startDate && startDate<=currentDate){\r\n          return banner;\r\n          }\r\n        }\r\n      }\r\n      if(banner.promotionId && endDate && endDate>=currentDate && startDate && startDate<=currentDate){\r\n        return  banner;\r\n      }\r\n    })\r\n    return data\r\n\r\n  }\r\n  getFeatureProducts(featureId: number, featureSequence: string) {\r\n\r\n    let featureTopNumber = 8\r\n    let featureShowOutOfStock = false;\r\n\r\n    const showRoomRecords = this.appDataService.showRoomConfiguration.records\r\n    if(showRoomRecords.length) {\r\n      const featureRecord = showRoomRecords.find((res:any) => res.showRoomTypeId == 2 && res.featureProduct == featureId)\r\n      if(featureRecord && featureRecord?.topNumber){\r\n        featureTopNumber = featureRecord.topNumber\r\n      }\r\n      if(featureRecord && featureRecord?.showOutOfStock) {\r\n        featureShowOutOfStock = featureRecord.showOutOfStock\r\n      }\r\n    }\r\n\r\n    let pageSize=featureTopNumber;\r\n\r\n\r\n\r\n    this.productService\r\n      .GetAllProductsByFeature(featureId, featureTopNumber, false, 1, pageSize, false, featureShowOutOfStock,true)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          if (res?.data?.records?.length) {\r\n            const tempProducts: any = [];\r\n            res.data?.records.forEach((record: any) => {\r\n             this.addTempProduct(res,tempProducts,record,featureSequence,featureId, featureTopNumber);\r\n            })\r\n          } else {\r\n            this.setFeatureProducts(featureSequence, featureId, [], res?.data?.featureName, featureTopNumber)\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchFlashSale(){\r\n     this.flashSale = this.appDataService.layoutTemplate.find((section: any) => section.description === 'Flash Sale')\r\n    const visited:any=localStorage.getItem('visited');\r\n    if(this.flashSale?.isActive && this.flashSale?.promotionId && !visited && this.flashSale?.data){\r\n        this.flashSaleData=JSON.parse(this.flashSale.data);\r\n        this.displayFlashSaleModal=true;\r\n    }\r\n\r\n  }\r\n\r\n  addTempProduct(res:any,tempProducts:any,record: any,featureSequence:any,featureId:any, featureTopNumber: number) {\r\n    let selectedVariance;\r\n    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n    if (defaultVariant) {\r\n      selectedVariance = defaultVariant;\r\n    } else {\r\n      let approvedVariant = record?.productVariances?.find((variant: any) => !variant.soldOut);\r\n      if (approvedVariant) {\r\n        selectedVariance = approvedVariant;\r\n\r\n      } else {\r\n        selectedVariance = record?.productVariances[0];\r\n      }\r\n\r\n    }\r\n    if (selectedVariance) {\r\n      let features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n      let product = {\r\n        productId: record?.id,\r\n        productName: record?.name,\r\n        isLiked: record?.isLiked,\r\n        priceValue: selectedVariance?.price,\r\n        priceId: selectedVariance?.priceId,\r\n        salePriceValue: selectedVariance?.salePrice,\r\n        currencyCode: record?.currencyCode,\r\n        masterImageUrl: selectedVariance.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : ''),\r\n        thumbnailImages: selectedVariance?.thumbnailImages,\r\n        soldOut: selectedVariance?.soldOut,\r\n        rate: selectedVariance?.rate,\r\n        count: selectedVariance?.count ?? 0,\r\n        salePercent: selectedVariance?.salePrice ? 100 - (selectedVariance?.salePrice / selectedVariance?.price * 100) : 0,\r\n        shopId: record.shopId,\r\n        specProductId: selectedVariance.specProductId,\r\n        channelId: record.channelId ?? '1',\r\n        isHot:!!features?.includes(1),\r\n        isNew:!!features?.includes(2),\r\n        isBest:!!features?.includes(3),\r\n        quantity:selectedVariance.quantity,\r\n        proSchedulingId:selectedVariance.proSchedulingId,\r\n        stockPerSKU:selectedVariance.stockPerSKU,\r\n        stockStatus: selectedVariance.stockStatus,\r\n        sku:selectedVariance?.sku,\r\n        skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n\r\n      }\r\n      if (product.salePriceValue) {\r\n        product.salePercent = 100 - (product.salePriceValue / product.priceValue * 100);\r\n      }\r\n\r\n      tempProducts.push(product)\r\n    }\r\n\r\n    this.setFeatureProducts(featureSequence, featureId,tempProducts,res?.data?.featureName, featureTopNumber);\r\n    }\r\n\r\n  setFeatureProducts(featureSequence: any, featureId: any, tempProducts: any,featureName:any, topNumberLimit: number = 8) {\r\n    // todo: this slicing needs to be discussed may be this one is may be handled from styling\r\n    if (featureSequence === 'first') {\r\n\r\n      let firstProductsCount=this.rowTwoPromotions.length>0 ? topNumberLimit : 10;\r\n      this.firstFeaturedProducts = {\r\n        id: featureId,\r\n        data: tempProducts.slice(0, firstProductsCount),\r\n        title: featureName,\r\n        showLoader: false,\r\n        isActive:true\r\n      };\r\n      localStorage.setItem('firstFeaturedProducts', JSON.stringify(this.firstFeaturedProducts));\r\n    } else if (featureSequence === 'second') {\r\n      let secondProductsCount=this.rowFourPromotions.length>0 ? topNumberLimit : 10;\r\n      this.secondFeaturedProducts = {\r\n        id: featureId,\r\n        data: tempProducts.slice(0, secondProductsCount),\r\n        title: featureName,\r\n        showLoader: false,\r\n        isActive:true\r\n      };\r\n      localStorage.setItem('secondFeaturedProducts', JSON.stringify(this.secondFeaturedProducts));\r\n    } else if (featureSequence === 'third') {\r\n      this.thirdFeaturedProducts = {\r\n        id: featureId,\r\n        data: tempProducts.slice(0, topNumberLimit),\r\n        title: featureName,\r\n        showLoader: false,\r\n        isActive:true\r\n      };\r\n      localStorage.setItem('thirdFeaturedProducts', JSON.stringify(this.thirdFeaturedProducts));\r\n\r\n    }else if (featureSequence === 'fourth') {\r\n      this.fourthFeaturedProducts = {\r\n        id: featureId,\r\n        data: tempProducts.slice(0, topNumberLimit),\r\n        title: featureName,\r\n        showLoader: false,\r\n        isActive:true\r\n      };\r\n      localStorage.setItem('fourthFeaturedProducts', JSON.stringify(this.fourthFeaturedProducts));\r\n\r\n    }\r\n\r\n    }\r\n  onFlashCancel() {\r\n\r\n    this.displayFlashSaleModal = false;\r\n  }\r\n  routeToCTA() {\r\n    this.displayFlashSaleModal = false;\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      if(this.flashSale.promotionId) {\r\n        const data = JSON.parse(this.flashSale.data)\r\n        let tempurl;\r\n        // if(data.promotionName) {\r\n        //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + data.promotionName;\r\n        // } else{\r\n        //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + this.flashSale.promotionId;\r\n        // }\r\n        // window.open(tempurl, '_blank');\r\n        this.router.navigate([`/promotion/${data.promotionName}`])\r\n      }\r\n      else{\r\n        window.open(this.flashSaleData.CTALink, '_blank');\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n}\r\n", "<ng-container class=\"template-one\">\r\n  <section *ngIf=\"mainBanner.length>0\" class=\"template-one__slider\">\r\n    <div class=\"d-flex justify-content-center\">\r\n      <app-mtn-main-slider [sliders]=\"mainBanner\"></app-mtn-main-slider>\r\n    </div>\r\n  </section>\r\n\r\n  <section *ngIf=\"rowOnePromotions.length>0\" class=\"template-one__promotion\">\r\n    <div class=\" d-flex justify-content-around mobile-second-banner\">\r\n\r\n      <p-carousel *ngIf=\"screenWidth<=767\" [responsiveOptions]=\"rowOnePromotionResponsiveConfig\"\r\n                  [value]=\"rowOnePromotions\" [circular]=\"false\"  [autoplayInterval]=\"0\" [showIndicators]=\"false\" [showNavigators]=\"false\">\r\n        <ng-template let-banner pTemplate=\"item\">\r\n          <app-promotion-banner [banner]=\"banner\" class=\"w-100 promotion-banner-small\"/>\r\n        </ng-template>\r\n      </p-carousel>\r\n      <ng-container *ngIf=\"screenWidth>767\">\r\n        <div class=\"template-one__promotion__promotion-content d-inline-flex\" *ngFor=\"let banner of rowOnePromotions\">\r\n          <app-promotion-banner [banner]=\"banner\" class=\"w-100 promotion-banner-small\" />\r\n        </div>\r\n      </ng-container>\r\n\r\n    </div>\r\n  </section>\r\n<!--  commented code-->\r\n<!--*ngIf=\"rowTwoPromotions.length>0 || firstFeaturedProducts.data.length>0\"-->\r\n  <section  class=\"template-one__feature\" [ngStyle]=\"{minHeight: (isMobileLayout && isMobileView) ? '600px' : 'inherit'}\">\r\n    <div class=\"d-flex justify-content-space-between\">\r\n      <div *ngIf=\"screenWidth>1200 && rowTwoPromotions.length>0\"\r\n           class=\"template-one__feature__promotion-vertical d-inline-flex\"\r\n           [ngStyle]=\"{minHeight: (isMobileLayout && isMobileView) ? '600px' : 'inherit'}\">\r\n        <app-promotion-vertical [banners]=\"rowTwoPromotions\"/>\r\n      </div>\r\n      <div *ngIf=\"firstFeaturedProducts.isActive\" [ngClass]=\"{'template-one__feature__feature-products-max':rowTwoPromotions.length==0}\"\r\n           class=\"template-one__feature__feature-products d-inline-flex pl-3\"\r\n      >\r\n        <app-feature-products [featureId]=\"firstFeaturedProducts.id\"\r\n                              [isLeftVerticalBanner]=\"rowTwoPromotions.length==0 ? true : false\" [products]=\"firstFeaturedProducts.data\"\r\n                              [showLoader]=\"firstFeaturedProducts.showLoader\" [title]=\"firstFeaturedProducts.title\"/>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n<!--  Best seller mobile banner-->\r\n  <section *ngIf=\"bestSellerMobileBanner?.isActive\" class=\"template-one__promotion-large\">\r\n    <div class=\" d-flex justify-content-space-between\">\r\n      <div class=\"template-one__promotion-large__promotion-content d-inline-flex\">\r\n        <app-promotion-banner [banner]=\"bestSellerMobileBanner\" class=\"w-100\"/>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <section *ngIf=\"rowThreePromotions.length>0\" class=\"template-one__promotion-medium\">\r\n    <div class=\" d-flex justify-content-around\">\r\n      <p-carousel *ngIf=\"screenWidth<=767\" [responsiveOptions]=\"rowThreePromotionResponsiveConfig\"\r\n                  [value]=\"rowThreePromotions\" [circular]=\"false\"  [autoplayInterval]=\"0\" [showIndicators]=\"false\" [showNavigators]=\"false\">\r\n        <ng-template let-banner pTemplate=\"item\">\r\n          <app-promotion-banner [banner]=\"banner\" class=\"w-100 promotion-banner-small\"/>\r\n        </ng-template>\r\n      </p-carousel>\r\n      <ng-container *ngIf=\"screenWidth>767\">\r\n        <div *ngFor=\"let banner of rowThreePromotions\"\r\n             class=\"template-one__promotion-medium__promotion-content d-inline-flex\">\r\n          <app-promotion-banner [banner]=\"banner\" class=\"w-100\"/>\r\n        </div>\r\n      </ng-container>\r\n\r\n    </div>\r\n  </section>\r\n  <!--  commented code-->\r\n  <!--   *ngIf=\"rowFourPromotions.length>0 || secondFeaturedProducts.data.length>0\"-->\r\n\r\n  <section class=\"template-one__feature-2\">\r\n    <div class=\"d-flex justify-content-space-between\">\r\n      <div [ngClass]=\"{'template-one__feature-2__feature-products-max':rowFourPromotions.length==0}\"\r\n           class=\"template-one__feature-2__feature-products d-inline-flex pr-3\">\r\n<!--        *ngIf=\"secondFeaturedProducts.data.length>0\"-->\r\n        <app-feature-products *ngIf=\"secondFeaturedProducts.isActive\"\r\n                              [featureId]=\"secondFeaturedProducts.id\" [isRightVerticalBanner]=\"rowFourPromotions.length==0 ? true : false\"\r\n                              [products]=\"secondFeaturedProducts.data\" [showLoader]=\"firstFeaturedProducts.showLoader\"\r\n                              [title]=\"secondFeaturedProducts.title\"/>\r\n      </div>\r\n      <div *ngIf=\"screenWidth>1200 && rowFourPromotions.length>0\"\r\n           class=\"template-one__feature-2__promotion-vertical d-inline-flex\">\r\n        <app-promotion-vertical [banners]=\"rowFourPromotions\"/>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!--  Best offer mobile banner-->\r\n  <section *ngIf=\"bestOfferMobileBanner?.isActive\" class=\"template-one__promotion-large\">\r\n    <div class=\" d-flex justify-content-space-between\">\r\n      <div class=\"template-one__promotion-large__promotion-content d-inline-flex\">\r\n        <app-promotion-banner [banner]=\"bestOfferMobileBanner\" class=\"w-100\"/>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <section *ngIf=\"rowFivePromotions\" class=\"template-one__promotion-large\">\r\n    <div class=\" d-flex justify-content-space-between\">\r\n      <div class=\"template-one__promotion-large__promotion-content d-inline-flex\">\r\n        <app-promotion-banner [banner]=\"rowFivePromotions\" class=\"w-100\"/>\r\n      </div>\r\n    </div>\r\n  </section>\r\n<!--*ngIf=\"thirdFeaturedProducts.data.length>0\"-->\r\n  <section  class=\"template-one__feature-single\" *ngIf=\"thirdFeaturedProducts.isActive\">\r\n    <app-feature-products [featureId]=\"thirdFeaturedProducts.id\" [fullRowProducts]=\"true\"\r\n                          [isNewArrival]=\"true\" [products]=\"thirdFeaturedProducts.data\"\r\n                          [showLoader]=\"firstFeaturedProducts.showLoader\"\r\n                          [title]=\"thirdFeaturedProducts.title\"/>\r\n  </section>\r\n</ng-container>\r\n<ng-container>\r\n  <app-flash-sale-modal\r\n    [data]=\"flashSaleData\"\r\n    (submit)=\"routeToCTA()\"\r\n    (cancel)=\"onFlashCancel()\"\r\n    [displayModal]=\"displayFlashSaleModal\"\r\n\r\n  >\r\n  </app-flash-sale-modal>\r\n</ng-container>\r\n"], "mappings": "AAAA,SAAyCA,WAAW,QAAO,eAAe;AAG1E,SAAQC,iBAAiB,QAAO,iBAAiB;;;;;;;;;;;;;;;ICF/CC,EAAA,CAAAC,cAAA,kBAAkE;IAE9DD,EAAA,CAAAE,SAAA,8BAAkE;IACpEF,EAAA,CAAAG,YAAA,EAAM;;;;IADiBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,YAAAC,MAAA,CAAAC,UAAA,CAAsB;;;;;IAUvCP,EAAA,CAAAE,SAAA,+BAA8E;;;;IAAxDF,EAAA,CAAAK,UAAA,WAAAG,UAAA,CAAiB;;;;;IAH3CR,EAAA,CAAAC,cAAA,qBACoI;IAClID,EAAA,CAAAS,UAAA,IAAAC,kEAAA,0BAEc;IAChBV,EAAA,CAAAG,YAAA,EAAa;;;;IALwBH,EAAA,CAAAK,UAAA,sBAAAM,OAAA,CAAAC,+BAAA,CAAqD,UAAAD,OAAA,CAAAE,gBAAA;;;;;IAOxFb,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,+BAA+E;IACjFF,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,UAAA,WAAAS,UAAA,CAAiB;;;;;IAF3Cd,EAAA,CAAAe,uBAAA,GAAsC;IACpCf,EAAA,CAAAS,UAAA,IAAAO,4DAAA,kBAEM;IACRhB,EAAA,CAAAiB,qBAAA,EAAe;;;;IAH4EjB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,YAAAa,OAAA,CAAAL,gBAAA,CAAmB;;;;;IAVlHb,EAAA,CAAAC,cAAA,kBAA2E;IAGvED,EAAA,CAAAS,UAAA,IAAAU,oDAAA,yBAKa;IACbnB,EAAA,CAAAS,UAAA,IAAAW,sDAAA,2BAIe;IAEjBpB,EAAA,CAAAG,YAAA,EAAM;;;;IAZSH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAC,WAAA,QAAsB;IAMpBtB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAC,WAAA,OAAqB;;;;;;;;;;IAYpCtB,EAAA,CAAAC,cAAA,cAEqF;IACnFD,EAAA,CAAAE,SAAA,iCAAsD;IACxDF,EAAA,CAAAG,YAAA,EAAM;;;;IAFDH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,YAAA,wBAA+E;IAC1D3B,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,YAAAoB,MAAA,CAAAG,gBAAA,CAA4B;;;;;;;;;;IAEtD5B,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,+BAE6G;IAC/GF,EAAA,CAAAG,YAAA,EAAM;;;;IANsCH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,IAAAM,GAAA,EAAAC,MAAA,CAAAF,gBAAA,CAAAG,MAAA,OAAsF;IAG1G/B,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,UAAA,cAAAyB,MAAA,CAAAE,qBAAA,CAAAC,EAAA,CAAsC,yBAAAH,MAAA,CAAAF,gBAAA,CAAAG,MAAA,kCAAAD,MAAA,CAAAE,qBAAA,CAAAE,IAAA,gBAAAJ,MAAA,CAAAE,qBAAA,CAAAG,UAAA,WAAAL,MAAA,CAAAE,qBAAA,CAAAI,KAAA;;;;;IAQlEpC,EAAA,CAAAC,cAAA,kBAAwF;IAGlFD,EAAA,CAAAE,SAAA,+BAAuE;IACzEF,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,WAAAgC,MAAA,CAAAC,sBAAA,CAAiC;;;;;IAUrDtC,EAAA,CAAAE,SAAA,+BAA8E;;;;IAAxDF,EAAA,CAAAK,UAAA,WAAAkC,UAAA,CAAiB;;;;;IAH3CvC,EAAA,CAAAC,cAAA,qBACsI;IACpID,EAAA,CAAAS,UAAA,IAAA+B,kEAAA,0BAEc;IAChBxC,EAAA,CAAAG,YAAA,EAAa;;;;IALwBH,EAAA,CAAAK,UAAA,sBAAAoC,OAAA,CAAAC,iCAAA,CAAuD,UAAAD,OAAA,CAAAE,kBAAA;;;;;IAO1F3C,EAAA,CAAAC,cAAA,cAC6E;IAC3ED,EAAA,CAAAE,SAAA,+BAAuD;IACzDF,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,UAAA,WAAAuC,UAAA,CAAiB;;;;;IAH3C5C,EAAA,CAAAe,uBAAA,GAAsC;IACpCf,EAAA,CAAAS,UAAA,IAAAoC,4DAAA,kBAGM;IACR7C,EAAA,CAAAiB,qBAAA,EAAe;;;;IAJWjB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,YAAAyC,OAAA,CAAAH,kBAAA,CAAqB;;;;;IATnD3C,EAAA,CAAAC,cAAA,kBAAoF;IAEhFD,EAAA,CAAAS,UAAA,IAAAsC,oDAAA,yBAKa;IACb/C,EAAA,CAAAS,UAAA,IAAAuC,sDAAA,2BAKe;IAEjBhD,EAAA,CAAAG,YAAA,EAAM;;;;IAbSH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,SAAA4C,MAAA,CAAA3B,WAAA,QAAsB;IAMpBtB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,SAAA4C,MAAA,CAAA3B,WAAA,OAAqB;;;;;IAiBlCtB,EAAA,CAAAE,SAAA,+BAG8D;;;;IAFxCF,EAAA,CAAAK,UAAA,cAAA6C,MAAA,CAAAC,sBAAA,CAAAlB,EAAA,CAAuC,0BAAAiB,MAAA,CAAAE,iBAAA,CAAArB,MAAA,kCAAAmB,MAAA,CAAAC,sBAAA,CAAAjB,IAAA,gBAAAgB,MAAA,CAAAlB,qBAAA,CAAAG,UAAA,WAAAe,MAAA,CAAAC,sBAAA,CAAAf,KAAA;;;;;IAI/DpC,EAAA,CAAAC,cAAA,cACuE;IACrED,EAAA,CAAAE,SAAA,iCAAuD;IACzDF,EAAA,CAAAG,YAAA,EAAM;;;;IADoBH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,UAAA,YAAAgD,MAAA,CAAAD,iBAAA,CAA6B;;;;;IAM3DpD,EAAA,CAAAC,cAAA,kBAAuF;IAGjFD,EAAA,CAAAE,SAAA,+BAAsE;IACxEF,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,UAAA,WAAAiD,MAAA,CAAAC,qBAAA,CAAgC;;;;;IAK5DvD,EAAA,CAAAC,cAAA,kBAAyE;IAGnED,EAAA,CAAAE,SAAA,+BAAkE;IACpEF,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,WAAAmD,MAAA,CAAAC,iBAAA,CAA4B;;;;;IAKxDzD,EAAA,CAAAC,cAAA,kBAAsF;IACpFD,EAAA,CAAAE,SAAA,+BAG6D;IAC/DF,EAAA,CAAAG,YAAA,EAAU;;;;IAJcH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,UAAA,cAAAqD,OAAA,CAAAC,qBAAA,CAAA1B,EAAA,CAAsC,4DAAAyB,OAAA,CAAAC,qBAAA,CAAAzB,IAAA,gBAAAwB,OAAA,CAAA1B,qBAAA,CAAAG,UAAA,WAAAuB,OAAA,CAAAC,qBAAA,CAAAvB,KAAA;;;;;;;;AD9FhE,OAAM,MAAOwB,oBAAoB;EAiC/BC,QAAQA,CAACC,KAAW;IAClB,IAAI/D,iBAAiB,CAAC,IAAI,CAACgE,UAAU,CAAC,EAAE;MACtC,IAAI,CAACzC,WAAW,GAAG0C,MAAM,CAACC,UAAU;MACpC,IAAI,CAACtC,YAAY,GAAG,IAAI,CAACL,WAAW,IAAI,GAAG;;EAE/C;EAEA4C,YAAoBC,cAA8B,EACxCC,cAA8B,EAASC,aAA4B,EAAUC,MAAc,EACjFC,iBAAoC,EACpCC,WAAsB,EACDT,UAAe;IAJpC,KAAAI,cAAc,GAAdA,cAAc;IACxB,KAAAC,cAAc,GAAdA,cAAc;IAAyB,KAAAC,aAAa,GAAbA,aAAa;IAAyB,KAAAC,MAAM,GAANA,MAAM;IACzE,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACU,KAAAT,UAAU,GAAVA,UAAU;IA1CnD,KAAAU,SAAS,GAAW,CAAC;IAIrB;IACA,KAAAlE,UAAU,GAAQ,EAAE;IAEpB;IACA,KAAAyB,qBAAqB,GAAQ;MAAEC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAACsC,QAAQ,EAAC,KAAK;MAAEvC,UAAU,EAAE;IAAI,CAAE;IAC5F,KAAAgB,sBAAsB,GAAQ;MAAElB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAACsC,QAAQ,EAAC,KAAK;MAAEvC,UAAU,EAAE;IAAI,CAAE;IAC7F,KAAAwB,qBAAqB,GAAQ;MAAE1B,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAACsC,QAAQ,EAAC,KAAK;MAAEvC,UAAU,EAAE;IAAI,CAAE;IAC5F,KAAAwC,sBAAsB,GAAQ;MAAE1C,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAACsC,QAAQ,EAAC,KAAK;MAAEvC,UAAU,EAAE;IAAI,CAAE;IAE7F;IACA,KAAAtB,gBAAgB,GAAgB,EAAE;IAClC,KAAAe,gBAAgB,GAAgB,EAAE;IAClC,KAAAe,kBAAkB,GAAgB,EAAE;IACpC,KAAAS,iBAAiB,GAAgB,EAAE;IAOnC,KAAAwB,qBAAqB,GAAS,KAAK;IAGnC,KAAAjD,YAAY,GAAGqC,MAAM,CAACC,UAAU,IAAI,GAAG;IACvC,KAAAvC,cAAc,GAAY,KAAK;IAe7B,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC6C,iBAAiB,CAACM,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI9E,iBAAiB,CAAC,IAAI,CAACgE,UAAU,CAAC,EAAE;MACtC,IAAI,CAACzC,WAAW,GAAG0C,MAAM,CAACC,UAAU;;IAEtC,IAAI,CAACrD,+BAA+B,GAAG,CACrC;MACEkE,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,CACF;IACD,IAAI,CAACtC,iCAAiC,GAAG,CACvC;MACEoC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,GAAG;MACfC,SAAS,EAAE;KACZ,CACF;EACH;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAAC,MAAM,CAAC;IACrC,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,cAAc,EAAE;EAEvB;EAGAH,qBAAqBA,CAAA;IACnB,MAAMI,kBAAkB,GAAG,IAAI,CAACpB,cAAc,CAACqB,cAAc,CAACC,MAAM,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,iBAAiB,CAAC;IAE1H,IAAGJ,kBAAkB,CAACxD,MAAM,EAAE;MAC5BwD,kBAAkB,CAACK,OAAO,CAAEC,IAAS,IAAI;QACvC,IAAGA,IAAI,CAACnB,QAAQ,EAAC;UACf,QAAOmB,IAAI,CAAC3D,IAAI;YACd;YACA,KAAK,GAAG;cACN,IAAG,IAAI,CAACmC,aAAa,CAACyB,eAAe,CAACC,UAAU,EAAE;gBAChD,IAAI,CAACC,kBAAkB,CAACC,QAAQ,CAACJ,IAAI,CAAC3D,IAAI,CAAC,EAAE,OAAO,CAAC;eACtD,MAAM;gBACL,IAAI,CAACF,qBAAqB,GAAGkE,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,GAC1EC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;;cAEtE;YACF;YACA,KAAK,GAAG;cACN,IAAG,IAAI,CAAC9B,aAAa,CAACyB,eAAe,CAACQ,aAAa,EAAC;gBAClD,IAAI,CAACN,kBAAkB,CAACC,QAAQ,CAACJ,IAAI,CAAC3D,IAAI,CAAC,EAAE,QAAQ,CAAC;eAEvD,MAAM;gBACL,IAAI,CAACiB,sBAAsB,GAAG+C,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,GAC5EC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;;cAEvE;YACF;YACA,KAAK,GAAG;cACN,IAAG,IAAI,CAAC9B,aAAa,CAACyB,eAAe,CAACS,YAAY,EAAC;gBACjD,IAAI,CAACP,kBAAkB,CAACC,QAAQ,CAACJ,IAAI,CAAC3D,IAAI,CAAC,EAAE,OAAO,CAAC;eACtD,MAAM;gBACL,IAAI,CAACyB,qBAAqB,GAAGuC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,GAC1EC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;;cAEtE;;SAEL,MAAM;UACL,IAAI,CAACnE,qBAAqB,CAACG,UAAU,GAAG,KAAK;UAC7C,IAAI,CAACgB,sBAAsB,CAAChB,UAAU,GAAG,KAAK;UAC9C,IAAI,CAACwB,qBAAqB,CAACxB,UAAU,GAAG,KAAK;;MAEjD,CAAC,CAAC;;EAEN;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGAiD,eAAeA,CAAA;IACb,MAAMoB,aAAa,GAAG,IAAI,CAACrC,cAAc,CAACqB,cAAc,CAACC,MAAM,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,WAAW,CAAC;IAC/Ga,aAAa,CAACZ,OAAO,CAAEa,SAAc,IAAI;MACvC,MAAMvE,IAAI,GAAQkE,IAAI,CAACC,KAAK,CAACI,SAAS,CAACvE,IAAI,CAAC;MAC5CA,IAAI,CAACwE,OAAO,GAAGxE,IAAI,CAACyE,OAAO;MAC3BzE,IAAI,CAACwC,QAAQ,GAAG+B,SAAS,CAAC/B,QAAQ;MAElC,IAAIxC,IAAI,CAACwC,QAAQ,EAAE;QACjBxC,IAAI,CAAC,aAAa,CAAC,GAACuE,SAAS,CAACG,WAAW;QACzC,QAAQH,SAAS,CAACI,IAAI;UACpB,KAAK,qBAAqB;UAC1B,KAAK,uBAAuB;UAC5B,KAAK,sBAAsB;YACzB,IAAI,CAAChG,gBAAgB,CAACiG,IAAI,CAAC5E,IAAI,CAAC;YAChC;UAEF,KAAK,sBAAsB;YACzB,IAAI,CAACN,gBAAgB,CAACkF,IAAI,CAAC5E,IAAI,CAAC;YAChC;UAEF,KAAK,6BAA6B;UAClC,KAAK,8BAA8B;YACjC,IAAI,CAACS,kBAAkB,CAACmE,IAAI,CAAC5E,IAAI,CAAC;YAClC;UAEF,KAAK,2BAA2B;UAChC,KAAK,8BAA8B;YACjC,IAAI,CAACkB,iBAAiB,CAAC0D,IAAI,CAAC5E,IAAI,CAAC;YACjC;UAEF,KAAK,wBAAwB;YAC3B,IAAI,CAACuB,iBAAiB,GAAGvB,IAAI;YAC7B;;;IAGR,CAAC,CAAC;IAEF,MAAMI,sBAAsB,GAAG,IAAI,CAAC6B,cAAc,CAACqB,cAAc,CAACuB,IAAI,CAAErB,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,oBAAoB,CAAC;IAC/H,IAAGrD,sBAAsB,EAAE;MACzB,MAAMJ,IAAI,GAAQkE,IAAI,CAACC,KAAK,CAAC/D,sBAAsB,CAACJ,IAAI,CAAC;MACzDA,IAAI,CAACwE,OAAO,GAAGxE,IAAI,CAACyE,OAAO;MAC3BzE,IAAI,CAACwC,QAAQ,GAAGpC,sBAAsB,CAACoC,QAAQ;MAC/C,IAAI,CAACpC,sBAAsB,GAAGJ,IAAI;;IAGpC,MAAMqB,qBAAqB,GAAG,IAAI,CAACY,cAAc,CAACqB,cAAc,CAACuB,IAAI,CAAErB,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,oBAAoB,CAAC;IAC9H,IAAGpC,qBAAqB,EAAE;MACxB,MAAMrB,IAAI,GAAQkE,IAAI,CAACC,KAAK,CAAC9C,qBAAqB,CAACrB,IAAI,CAAC;MACxDA,IAAI,CAACwE,OAAO,GAAGxE,IAAI,CAACyE,OAAO;MAC3BzE,IAAI,CAACwC,QAAQ,GAAGnB,qBAAqB,CAACmB,QAAQ;MAC9C,IAAI,CAACnB,qBAAqB,GAAGrB,IAAI;;EAGrC;EAGAmD,eAAeA,CAAA;IAEb,MAAM2B,UAAU,GAAG,IAAI,CAAC7C,cAAc,CAACqB,cAAc,CAACuB,IAAI,CAAErB,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,aAAa,CAAC;IAC5G,IAAIqB,UAAU,EAAE;MACd,IAAIA,UAAU,EAAEtC,QAAQ,EACtB,IAAI,CAACnE,UAAU,GAAG6F,IAAI,CAACC,KAAK,CAACW,UAAU,CAAC9E,IAAI,CAAC;MAC/C,IAAI,IAAI,CAAC3B,UAAU,CAACwB,MAAM,GAAG,CAAC,EAAE;QAC9B,IAAI,CAACxB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACkF,MAAM,CAAEI,IAAS,IAAK,CAACA,IAAI,CAACoB,gBAAgB,CAAC;;MAEjF,IAAI,CAAC1G,UAAU,GAAC,IAAI,CAAC2G,qBAAqB,CAAC,IAAI,CAAC3G,UAAU,CAAC;;EAE/D;EACA2G,qBAAqBA,CAACC,QAAY;IAChC,IAAIC,WAAW,GAAKC,IAAI,CAACC,GAAG,EAAE;IAC9B,IAAIpF,IAAI,GAACiF,QAAQ,CAAC1B,MAAM,CAAE8B,MAAU,IAAG;MAErC,IAAIC,OAAO,GAAC,IAAIH,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,CAACC,OAAO,EAAE;MAC9C,IAAIC,SAAS,GAAC,IAAIL,IAAI,CAACE,MAAM,CAACG,SAAS,CAAC,CAACD,OAAO,EAAE;MAClD,IAAG,CAACF,MAAM,CAACX,WAAW,EAAC;QACrB,IAAG,CAACY,OAAO,EAAC;UACV,OAAOD,MAAM;SACZ,MACG;UACJ,IAAGC,OAAO,IAAIA,OAAO,IAAEJ,WAAW,IAAIM,SAAS,IAAIA,SAAS,IAAEN,WAAW,EAAC;YAC1E,OAAOG,MAAM;;;;MAIjB,IAAGA,MAAM,CAACX,WAAW,IAAIY,OAAO,IAAIA,OAAO,IAAEJ,WAAW,IAAIM,SAAS,IAAIA,SAAS,IAAEN,WAAW,EAAC;QAC9F,OAAQG,MAAM;;IAElB,CAAC,CAAC;IACF,OAAOrF,IAAI;EAEb;EACA8D,kBAAkBA,CAAC2B,SAAiB,EAAEC,eAAuB;IAE3D,IAAIC,gBAAgB,GAAG,CAAC;IACxB,IAAIC,qBAAqB,GAAG,KAAK;IAEjC,MAAMC,eAAe,GAAG,IAAI,CAAC5D,cAAc,CAAC6D,qBAAqB,CAACC,OAAO;IACzE,IAAGF,eAAe,CAAChG,MAAM,EAAE;MACzB,MAAMmG,aAAa,GAAGH,eAAe,CAAChB,IAAI,CAAEoB,GAAO,IAAKA,GAAG,CAACC,cAAc,IAAI,CAAC,IAAID,GAAG,CAACE,cAAc,IAAIV,SAAS,CAAC;MACnH,IAAGO,aAAa,IAAIA,aAAa,EAAEzD,SAAS,EAAC;QAC3CoD,gBAAgB,GAAGK,aAAa,CAACzD,SAAS;;MAE5C,IAAGyD,aAAa,IAAIA,aAAa,EAAEI,cAAc,EAAE;QACjDR,qBAAqB,GAAGI,aAAa,CAACI,cAAc;;;IAIxD,IAAIC,QAAQ,GAACV,gBAAgB;IAI7B,IAAI,CAACzD,cAAc,CAChBoE,uBAAuB,CAACb,SAAS,EAAEE,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAEU,QAAQ,EAAE,KAAK,EAAET,qBAAqB,EAAC,IAAI,CAAC,CAC3GW,SAAS,CAAC;MACTC,IAAI,EAAGP,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAEjG,IAAI,EAAE+F,OAAO,EAAElG,MAAM,EAAE;UAC9B,MAAM4G,YAAY,GAAQ,EAAE;UAC5BR,GAAG,CAACjG,IAAI,EAAE+F,OAAO,CAACrC,OAAO,CAAEgD,MAAW,IAAI;YACzC,IAAI,CAACC,cAAc,CAACV,GAAG,EAACQ,YAAY,EAACC,MAAM,EAAChB,eAAe,EAACD,SAAS,EAAEE,gBAAgB,CAAC;UACzF,CAAC,CAAC;SACH,MAAM;UACL,IAAI,CAACiB,kBAAkB,CAAClB,eAAe,EAAED,SAAS,EAAE,EAAE,EAAEQ,GAAG,EAAEjG,IAAI,EAAE6G,WAAW,EAAElB,gBAAgB,CAAC;;MAErG;KACD,CAAC;EACN;EAEAvC,cAAcA,CAAA;IACX,IAAI,CAAC0D,SAAS,GAAG,IAAI,CAAC7E,cAAc,CAACqB,cAAc,CAACuB,IAAI,CAAErB,OAAY,IAAKA,OAAO,CAACuD,WAAW,KAAK,YAAY,CAAC;IACjH,MAAMC,OAAO,GAAKhD,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IACjD,IAAG,IAAI,CAAC6C,SAAS,EAAEtE,QAAQ,IAAI,IAAI,CAACsE,SAAS,EAAEpC,WAAW,IAAI,CAACsC,OAAO,IAAI,IAAI,CAACF,SAAS,EAAE9G,IAAI,EAAC;MAC3F,IAAI,CAACiH,aAAa,GAAC/C,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC2C,SAAS,CAAC9G,IAAI,CAAC;MAClD,IAAI,CAAC0C,qBAAqB,GAAC,IAAI;;EAGrC;EAEAiE,cAAcA,CAACV,GAAO,EAACQ,YAAgB,EAACC,MAAW,EAAChB,eAAmB,EAACD,SAAa,EAAEE,gBAAwB;IAC7G,IAAIuB,gBAAgB;IACpB,IAAIC,cAAc,GAAGT,MAAM,EAAEU,gBAAgB,EAAEvC,IAAI,CAAEwC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACL,IAAII,eAAe,GAAGb,MAAM,EAAEU,gBAAgB,EAAEvC,IAAI,CAAEwC,OAAY,IAAK,CAACA,OAAO,CAACG,OAAO,CAAC;MACxF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OAEnC,MAAM;QACLL,gBAAgB,GAAGR,MAAM,EAAEU,gBAAgB,CAAC,CAAC,CAAC;;;IAIlD,IAAIF,gBAAgB,EAAE;MACpB,IAAIO,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;MAClE,IAAIC,OAAO,GAAG;QACZC,SAAS,EAAEnB,MAAM,EAAE3G,EAAE;QACrB+H,WAAW,EAAEpB,MAAM,EAAE/B,IAAI;QACzBoD,OAAO,EAAErB,MAAM,EAAEqB,OAAO;QACxBC,UAAU,EAAEd,gBAAgB,EAAEe,KAAK;QACnCC,OAAO,EAAEhB,gBAAgB,EAAEgB,OAAO;QAClCC,cAAc,EAAEjB,gBAAgB,EAAEkB,SAAS;QAC3CC,YAAY,EAAE3B,MAAM,EAAE2B,YAAY;QAClCC,cAAc,EAAEpB,gBAAgB,CAACoB,cAAc,KAAKpB,gBAAgB,CAACqB,MAAM,GAAGrB,gBAAgB,CAACqB,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9GC,eAAe,EAAEtB,gBAAgB,EAAEsB,eAAe;QAClDhB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;QAClCiB,IAAI,EAAEvB,gBAAgB,EAAEuB,IAAI;QAC5BC,KAAK,EAAExB,gBAAgB,EAAEwB,KAAK,IAAI,CAAC;QACnCC,WAAW,EAAEzB,gBAAgB,EAAEkB,SAAS,GAAG,GAAG,GAAIlB,gBAAgB,EAAEkB,SAAS,GAAGlB,gBAAgB,EAAEe,KAAK,GAAG,GAAI,GAAG,CAAC;QAClHW,MAAM,EAAElC,MAAM,CAACkC,MAAM;QACrBC,aAAa,EAAE3B,gBAAgB,CAAC2B,aAAa;QAC7CC,SAAS,EAAEpC,MAAM,CAACoC,SAAS,IAAI,GAAG;QAClCC,KAAK,EAAC,CAAC,CAACtB,QAAQ,EAAEuB,QAAQ,CAAC,CAAC,CAAC;QAC7BC,KAAK,EAAC,CAAC,CAACxB,QAAQ,EAAEuB,QAAQ,CAAC,CAAC,CAAC;QAC7BE,MAAM,EAAC,CAAC,CAACzB,QAAQ,EAAEuB,QAAQ,CAAC,CAAC,CAAC;QAC9BG,QAAQ,EAACjC,gBAAgB,CAACiC,QAAQ;QAClCC,eAAe,EAAClC,gBAAgB,CAACkC,eAAe;QAChDC,WAAW,EAACnC,gBAAgB,CAACmC,WAAW;QACxCC,WAAW,EAAEpC,gBAAgB,CAACoC,WAAW;QACzCC,GAAG,EAACrC,gBAAgB,EAAEqC,GAAG;QACzBC,gBAAgB,EAAGtC,gBAAgB,CAACsC;OAErC;MACD,IAAI5B,OAAO,CAACO,cAAc,EAAE;QAC1BP,OAAO,CAACe,WAAW,GAAG,GAAG,GAAIf,OAAO,CAACO,cAAc,GAAGP,OAAO,CAACI,UAAU,GAAG,GAAI;;MAGjFvB,YAAY,CAAC7B,IAAI,CAACgD,OAAO,CAAC;;IAG5B,IAAI,CAAChB,kBAAkB,CAAClB,eAAe,EAAED,SAAS,EAACgB,YAAY,EAACR,GAAG,EAAEjG,IAAI,EAAE6G,WAAW,EAAElB,gBAAgB,CAAC;EACzG;EAEFiB,kBAAkBA,CAAClB,eAAoB,EAAED,SAAc,EAAEgB,YAAiB,EAACI,WAAe,EAAE4C,cAAA,GAAyB,CAAC;IACpH;IACA,IAAI/D,eAAe,KAAK,OAAO,EAAE;MAE/B,IAAIgE,kBAAkB,GAAC,IAAI,CAAChK,gBAAgB,CAACG,MAAM,GAAC,CAAC,GAAG4J,cAAc,GAAG,EAAE;MAC3E,IAAI,CAAC3J,qBAAqB,GAAG;QAC3BC,EAAE,EAAE0F,SAAS;QACbzF,IAAI,EAAEyG,YAAY,CAACkD,KAAK,CAAC,CAAC,EAAED,kBAAkB,CAAC;QAC/CxJ,KAAK,EAAE2G,WAAW;QAClB5G,UAAU,EAAE,KAAK;QACjBuC,QAAQ,EAAC;OACV;MACDwB,YAAY,CAAC4F,OAAO,CAAC,uBAAuB,EAAE1F,IAAI,CAAC2F,SAAS,CAAC,IAAI,CAAC/J,qBAAqB,CAAC,CAAC;KAC1F,MAAM,IAAI4F,eAAe,KAAK,QAAQ,EAAE;MACvC,IAAIoE,mBAAmB,GAAC,IAAI,CAAC5I,iBAAiB,CAACrB,MAAM,GAAC,CAAC,GAAG4J,cAAc,GAAG,EAAE;MAC7E,IAAI,CAACxI,sBAAsB,GAAG;QAC5BlB,EAAE,EAAE0F,SAAS;QACbzF,IAAI,EAAEyG,YAAY,CAACkD,KAAK,CAAC,CAAC,EAAEG,mBAAmB,CAAC;QAChD5J,KAAK,EAAE2G,WAAW;QAClB5G,UAAU,EAAE,KAAK;QACjBuC,QAAQ,EAAC;OACV;MACDwB,YAAY,CAAC4F,OAAO,CAAC,wBAAwB,EAAE1F,IAAI,CAAC2F,SAAS,CAAC,IAAI,CAAC5I,sBAAsB,CAAC,CAAC;KAC5F,MAAM,IAAIyE,eAAe,KAAK,OAAO,EAAE;MACtC,IAAI,CAACjE,qBAAqB,GAAG;QAC3B1B,EAAE,EAAE0F,SAAS;QACbzF,IAAI,EAAEyG,YAAY,CAACkD,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;QAC3CvJ,KAAK,EAAE2G,WAAW;QAClB5G,UAAU,EAAE,KAAK;QACjBuC,QAAQ,EAAC;OACV;MACDwB,YAAY,CAAC4F,OAAO,CAAC,uBAAuB,EAAE1F,IAAI,CAAC2F,SAAS,CAAC,IAAI,CAACpI,qBAAqB,CAAC,CAAC;KAE1F,MAAK,IAAIiE,eAAe,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACjD,sBAAsB,GAAG;QAC5B1C,EAAE,EAAE0F,SAAS;QACbzF,IAAI,EAAEyG,YAAY,CAACkD,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;QAC3CvJ,KAAK,EAAE2G,WAAW;QAClB5G,UAAU,EAAE,KAAK;QACjBuC,QAAQ,EAAC;OACV;MACDwB,YAAY,CAAC4F,OAAO,CAAC,wBAAwB,EAAE1F,IAAI,CAAC2F,SAAS,CAAC,IAAI,CAACpH,sBAAsB,CAAC,CAAC;;EAI7F;EACFsH,aAAaA,CAAA;IAEX,IAAI,CAACrH,qBAAqB,GAAG,KAAK;EACpC;EACAsH,UAAUA,CAAA;IACR,IAAI,CAACtH,qBAAqB,GAAG,KAAK;IAClC,IAAI7E,iBAAiB,CAAC,IAAI,CAACgE,UAAU,CAAC,EAAE;MACtC,IAAG,IAAI,CAACiF,SAAS,CAACpC,WAAW,EAAE;QAC7B,MAAM1E,IAAI,GAAGkE,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC2C,SAAS,CAAC9G,IAAI,CAAC;QAC5C,IAAIiK,OAAO;QACX;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC7H,MAAM,CAAC8H,QAAQ,CAAC,CAAC,cAAclK,IAAI,CAACmK,aAAa,EAAE,CAAC,CAAC;OAC3D,MACG;QACFrI,MAAM,CAACsI,IAAI,CAAC,IAAI,CAACnD,aAAa,CAACxC,OAAO,EAAE,QAAQ,CAAC;;;EAGvD;EAAC,QAAA4F,CAAA,G;qBArcU3I,oBAAoB,EAAA5D,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1M,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA3M,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAG,aAAA,GAAA5M,EAAA,CAAAwM,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA9M,EAAA,CAAAwM,iBAAA,CAAAC,EAAA,CAAAM,iBAAA,GAAA/M,EAAA,CAAAwM,iBAAA,CAAAQ,EAAA,CAAAC,UAAA,GAAAjN,EAAA,CAAAwM,iBAAA,CA4CX1M,WAAW;EAAA;EAAA,QAAAoN,EAAA,G;UA5CpBtJ,oBAAoB;IAAAuJ,SAAA;IAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAApBC,GAAA,CAAA1J,QAAA,CAAA2J,MAAA,CAAgB;QAAA,UAAAxN,EAAA,CAAAyN,eAAA;;;;;;;;QCb7BzN,EAAA,CAAAe,uBAAA,MAAmC;QACjCf,EAAA,CAAAS,UAAA,IAAAiN,uCAAA,qBAIU;QAEV1N,EAAA,CAAAS,UAAA,IAAAkN,uCAAA,qBAgBU;QAGV3N,EAAA,CAAAC,cAAA,iBAAwH;QAEpHD,EAAA,CAAAS,UAAA,IAAAmN,mCAAA,iBAIM;QACN5N,EAAA,CAAAS,UAAA,IAAAoN,mCAAA,iBAMM;QACR7N,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAS,UAAA,IAAAqN,uCAAA,qBAMU;QAEV9N,EAAA,CAAAS,UAAA,IAAAsN,uCAAA,qBAgBU;QAIV/N,EAAA,CAAAC,cAAA,iBAAyC;QAKnCD,EAAA,CAAAS,UAAA,KAAAuN,qDAAA,mCAG8D;QAChEhO,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAS,UAAA,KAAAwN,oCAAA,kBAGM;QACRjO,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAS,UAAA,KAAAyN,wCAAA,qBAMU;QAEVlO,EAAA,CAAAS,UAAA,KAAA0N,wCAAA,qBAMU;QAEVnO,EAAA,CAAAS,UAAA,KAAA2N,wCAAA,sBAKU;QACZpO,EAAA,CAAAiB,qBAAA,EAAe;QACfjB,EAAA,CAAAe,uBAAA,IAAc;QACZf,EAAA,CAAAC,cAAA,gCAMC;QAJCD,EAAA,CAAAqO,UAAA,oBAAAC,sEAAA;UAAA,OAAUf,GAAA,CAAArB,UAAA,EAAY;QAAA,EAAC,oBAAAqC,sEAAA;UAAA,OACbhB,GAAA,CAAAtB,aAAA,EAAe;QAAA,EADF;QAKzBjM,EAAA,CAAAG,YAAA,EAAuB;QACzBH,EAAA,CAAAiB,qBAAA,EAAe;;;QAzHHjB,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAhN,UAAA,CAAAwB,MAAA,KAAyB;QAMzB/B,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAA1M,gBAAA,CAAAkB,MAAA,KAA+B;QAmBD/B,EAAA,CAAAI,SAAA,GAA+E;QAA/EJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,KAAAC,GAAA,EAAA+L,GAAA,CAAA7L,cAAA,IAAA6L,GAAA,CAAA5L,YAAA,wBAA+E;QAE7G3B,EAAA,CAAAI,SAAA,GAAmD;QAAnDJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAjM,WAAA,WAAAiM,GAAA,CAAA3L,gBAAA,CAAAG,MAAA,KAAmD;QAKnD/B,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAvL,qBAAA,CAAA0C,QAAA,CAAoC;QAWpC1E,EAAA,CAAAI,SAAA,GAAsC;QAAtCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAjL,sBAAA,kBAAAiL,GAAA,CAAAjL,sBAAA,CAAAoC,QAAA,CAAsC;QAQtC1E,EAAA,CAAAI,SAAA,GAAiC;QAAjCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAA5K,kBAAA,CAAAZ,MAAA,KAAiC;QAsBlC/B,EAAA,CAAAI,SAAA,GAAyF;QAAzFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,KAAAiN,GAAA,EAAAjB,GAAA,CAAAnK,iBAAA,CAAArB,MAAA,OAAyF;QAGrE/B,EAAA,CAAAI,SAAA,GAAqC;QAArCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAApK,sBAAA,CAAAuB,QAAA,CAAqC;QAKxD1E,EAAA,CAAAI,SAAA,GAAoD;QAApDJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAjM,WAAA,WAAAiM,GAAA,CAAAnK,iBAAA,CAAArB,MAAA,KAAoD;QAQpD/B,EAAA,CAAAI,SAAA,GAAqC;QAArCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAAhK,qBAAA,kBAAAgK,GAAA,CAAAhK,qBAAA,CAAAmB,QAAA,CAAqC;QAQrC1E,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAA9J,iBAAA,CAAuB;QAQezD,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAA5J,qBAAA,CAAAe,QAAA,CAAoC;QASlF1E,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,UAAA,SAAAkN,GAAA,CAAApE,aAAA,CAAsB,iBAAAoE,GAAA,CAAA3I,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}