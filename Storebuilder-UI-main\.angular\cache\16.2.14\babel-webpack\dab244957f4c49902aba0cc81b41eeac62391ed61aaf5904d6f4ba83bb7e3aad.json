{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class PaymentFailedModalComponent {\n  constructor(translate) {\n    this.translate = translate;\n    this.displayModal = false;\n    this.submit = new EventEmitter();\n    this.buttonText = this.translate.instant('settings.address.addAddress');\n    this.addressName = '';\n    this.isStoreCloud = environment.isStoreCloud;\n  }\n  ngOnInit() {\n    /**/\n  }\n  static #_ = this.ɵfac = function PaymentFailedModalComponent_Factory(t) {\n    return new (t || PaymentFailedModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PaymentFailedModalComponent,\n    selectors: [[\"app-mtn-payment-failed-modal\"]],\n    inputs: {\n      displayModal: \"displayModal\"\n    },\n    outputs: {\n      submit: \"submit\"\n    },\n    decls: 12,\n    vars: 7,\n    consts: [[3, \"visible\", \"breakpoints\", \"resizable\", \"closable\", \"modal\", \"showHeader\", \"visibleChange\"], [1, \"d-flex\", \"justify-content-center\", \"mb-4\", \"mt-4\"], [1, \"icon-cross-bg\"], [1, \"fa-solid\", \"fa-xmark\", \"cross-icon\"], [1, \"your-address\", \"text-center\"], [1, \"payment-fail\", \"mb-8\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"px-7\", \"bg-white\", \"border-round\", \"pt-3\"], [\"type\", \"button\", \"ng-reflect-label\", \"Proceed to Payment\", 1, \"p-element\", \"my-2\", \"second-btn\", \"p-button\", \"p-component\", \"width-100\"], [1, \"p-button-label\", \"try-size\"]],\n    template: function PaymentFailedModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function PaymentFailedModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"span\", 2);\n        i0.ɵɵelement(3, \"em\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"h2\", 4);\n        i0.ɵɵtext(5, \"Payment failed\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"p\", 5);\n        i0.ɵɵtext(7, \" Your payment was not successfully processed. Please try again \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"button\", 7)(10, \"span\", 8);\n        i0.ɵɵtext(11, \"Try again\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(6, _c0))(\"resizable\", false)(\"closable\", false)(\"modal\", true)(\"showHeader\", false);\n      }\n    },\n    styles: [\"*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  list-style: none;\\n  text-decoration: none;\\n}\\n\\n.your-address[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  color: #000;\\n  font-size: 28px;\\n  font-weight: 700;\\n}\\n\\n  .p-dialog-content {\\n  border-bottom: none !important;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font);\\n  padding: 10px 20px;\\n  font-size: 15px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n}\\n\\n.payment-fail[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 300;\\n  color: #A3A3A3;\\n  font-family: var(--regular-font) !important;\\n  width: 341px;\\n  text-align: center;\\n}\\n\\n.cross-icon[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.icon-cross-bg[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 30px;\\n  margin-bottom: 18px;\\n  background: var(--header_bgcolor);\\n  width: 50px;\\n  display: flex;\\n  height: 50px;\\n  color: white;\\n  border-radius: 30px;\\n  justify-content: center;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n  .try-size[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .payment-fail[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "environment", "PaymentFailedModalComponent", "constructor", "translate", "displayModal", "submit", "buttonText", "instant", "addressName", "isStoreCloud", "ngOnInit", "_", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PaymentFailedModalComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "PaymentFailedModalComponent_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\payment-failed-modal\\payment-failed-modal.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\payment-failed-modal\\payment-failed-modal.component.html"], "sourcesContent": ["import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {environment} from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-mtn-payment-failed-modal',\r\n  templateUrl: './payment-failed-modal.component.html',\r\n  styleUrls: ['./payment-failed-modal.component.scss']\r\n})\r\nexport class PaymentFailedModalComponent implements OnInit {\r\n  @Input() displayModal: boolean = false\r\n  @Output() submit = new EventEmitter<boolean>();\r\n  buttonText: string = this.translate.instant('settings.address.addAddress');\r\n  addressName: string = ''\r\n  isStoreCloud: boolean = environment.isStoreCloud\r\n\r\n  constructor(private translate: TranslateService) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n}\r\n", "<p-dialog\r\n  [(visible)]=\"displayModal\"\r\n  [breakpoints]=\"{ '960px': '75vw', '640px': '90vw' }\"\r\n  [resizable]=\"false\"\r\n  [closable]=\"false\"\r\n  [modal]=\"true\"\r\n  [showHeader]=\"false\"\r\n>\r\n  <div class=\"d-flex justify-content-center mb-4 mt-4\">\r\n    <span class=\"icon-cross-bg\"\r\n      ><em class=\"fa-solid fa-xmark cross-icon\"></em\r\n    ></span>\r\n  </div>\r\n  <h2 class=\"your-address text-center\">Payment failed</h2>\r\n  <p class=\"payment-fail mb-8\">\r\n    Your payment was not successfully processed. Please try again\r\n  </p>\r\n  <div\r\n    class=\"grid align-items-center justify-content-center px-7 bg-white border-round pt-3\"\r\n  >\r\n    <button\r\n      type=\"button\"\r\n      class=\"p-element my-2 second-btn p-button p-component width-100\"\r\n      ng-reflect-label=\"Proceed to Payment\"\r\n    >\r\n      <span class=\"p-button-label try-size\">Try again</span>\r\n    </button>\r\n  </div>\r\n</p-dialog>\r\n"], "mappings": "AAAA,SAAmBA,YAAY,QAA8B,eAAe;AAE5E,SAAQC,WAAW,QAAO,8BAA8B;;;;;;;;;AAOxD,OAAM,MAAOC,2BAA2B;EAOtCC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;IANpB,KAAAC,YAAY,GAAY,KAAK;IAC5B,KAAAC,MAAM,GAAG,IAAIN,YAAY,EAAW;IAC9C,KAAAO,UAAU,GAAW,IAAI,CAACH,SAAS,CAACI,OAAO,CAAC,6BAA6B,CAAC;IAC1E,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAYT,WAAW,CAACS,YAAY;EAGhD;EAEAC,QAAQA,CAAA;IACN;EAAA;EACD,QAAAC,CAAA,G;qBAZUV,2BAA2B,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3Bf,2BAA2B;IAAAgB,SAAA;IAAAC,MAAA;MAAAd,YAAA;IAAA;IAAAe,OAAA;MAAAd,MAAA;IAAA;IAAAe,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxCb,EAAA,CAAAe,cAAA,kBAOC;QANCf,EAAA,CAAAgB,UAAA,2BAAAC,uEAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAAtB,YAAA,GAAA0B,MAAA;QAAA,EAA0B;QAO1BlB,EAAA,CAAAe,cAAA,aAAqD;QAEhDf,EAAA,CAAAmB,SAAA,YACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAEVpB,EAAA,CAAAe,cAAA,YAAqC;QAAAf,EAAA,CAAAqB,MAAA,qBAAc;QAAArB,EAAA,CAAAoB,YAAA,EAAK;QACxDpB,EAAA,CAAAe,cAAA,WAA6B;QAC3Bf,EAAA,CAAAqB,MAAA,sEACF;QAAArB,EAAA,CAAAoB,YAAA,EAAI;QACJpB,EAAA,CAAAe,cAAA,aAEC;QAMyCf,EAAA,CAAAqB,MAAA,iBAAS;QAAArB,EAAA,CAAAoB,YAAA,EAAO;;;QAxB1DpB,EAAA,CAAAsB,UAAA,YAAAR,GAAA,CAAAtB,YAAA,CAA0B,gBAAAQ,EAAA,CAAAuB,eAAA,IAAAC,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}