{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PasswordModule } from 'primeng/password';\nimport { FormsModule } from '@angular/forms';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/password\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction PasswordInputComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"signIn.password\"), \" *\");\n  }\n}\nfunction PasswordInputComponent_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"signIn.password\"), \" *\");\n  }\n}\nfunction PasswordInputComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"a\", 8);\n    i0.ɵɵlistener(\"click\", function PasswordInputComponent_div_5_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onForgotPasswordClick());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.forgotPasswordClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"signIn.forgotPassword\"), \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"mt-3\": a0\n  };\n};\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nexport class PasswordInputComponent {\n  showForgotPassword = true;\n  forgotPasswordClass = '';\n  floatingLabel = false;\n  feedback = true;\n  passwordChange = new EventEmitter();\n  validationChange = new EventEmitter();\n  forgotPasswordClick = new EventEmitter();\n  password = '';\n  modelChanged(password) {\n    this.password = password;\n    this.passwordChange.emit(password);\n    this.validationChange.emit(password && password.length > 0);\n  }\n  onForgotPasswordClick() {\n    this.forgotPasswordClick.emit();\n  }\n  static ɵfac = function PasswordInputComponent_Factory(t) {\n    return new (t || PasswordInputComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PasswordInputComponent,\n    selectors: [[\"app-password-input\"]],\n    inputs: {\n      showForgotPassword: \"showForgotPassword\",\n      forgotPasswordClass: \"forgotPasswordClass\",\n      floatingLabel: \"floatingLabel\",\n      feedback: \"feedback\"\n    },\n    outputs: {\n      passwordChange: \"passwordChange\",\n      validationChange: \"validationChange\",\n      forgotPasswordClick: \"forgotPasswordClick\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 6,\n    vars: 11,\n    consts: [[1, \"p-field\", \"p-col-12\"], [\"class\", \"contact-label\", 4, \"ngIf\"], [1, \"p-float-label\", 3, \"ngClass\"], [\"autocomplete\", \"off\", \"id\", \"custom-password-input\", 1, \"customClass\", 3, \"ngModel\", \"feedback\", \"ngModelOptions\", \"toggleMask\", \"ngModelChange\"], [4, \"ngIf\"], [\"class\", \"flex justify-content-end flex-wrap resetPassword cursor-pointer\", 4, \"ngIf\"], [1, \"contact-label\"], [1, \"flex\", \"justify-content-end\", \"flex-wrap\", \"resetPassword\", \"cursor-pointer\"], [1, \"no-underline\", 3, \"ngClass\", \"click\"]],\n    template: function PasswordInputComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, PasswordInputComponent_label_1_Template, 3, 3, \"label\", 1);\n        i0.ɵɵelementStart(2, \"span\", 2)(3, \"p-password\", 3);\n        i0.ɵɵlistener(\"ngModelChange\", function PasswordInputComponent_Template_p_password_ngModelChange_3_listener($event) {\n          return ctx.modelChanged($event);\n        })(\"ngModelChange\", function PasswordInputComponent_Template_p_password_ngModelChange_3_listener($event) {\n          return ctx.password = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, PasswordInputComponent_label_4_Template, 3, 3, \"label\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, PasswordInputComponent_div_5_Template, 4, 4, \"div\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.floatingLabel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx.floatingLabel));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.password)(\"feedback\", ctx.feedback)(\"ngModelOptions\", i0.ɵɵpureFunction0(10, _c1))(\"toggleMask\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.floatingLabel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showForgotPassword);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, FormsModule, i2.NgControlStatus, i2.NgModel, PasswordModule, i3.Password, TranslateModule, i4.TranslatePipe],\n    styles: [\".contact-label[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  padding: 10px;\\n  font-size: 12px !important;\\n  font-weight: 400;\\n  color: #2D2D2D;\\n  line-height: 20px;\\n}\\n\\n.resetPassword[_ngcontent-%COMP%] {\\n  gap: 8px;\\n  padding: 8px 0px;\\n}\\n.resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #272727;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #323232;\\n}\\n\\n  .customClass input {\\n  height: 60px !important;\\n  width: 100%;\\n  border-radius: 4px;\\n  opacity: 1;\\n  border: none !important;\\n  border: 1px solid #ccc !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  background-color: #fff !important;\\n  font-family: var(--regular-font) !important;\\n  font-size: 14px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  color: red;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}