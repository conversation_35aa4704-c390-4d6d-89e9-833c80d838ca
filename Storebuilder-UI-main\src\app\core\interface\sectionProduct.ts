export interface ProductVariance {
    specProductId: number;
    status: string;
    stockStatus: string;
    price: number;
    salePrice: any;
    quantity: number;
    sku: string;
    images: string[];
    masterImageUrl: string;
    salePriceValue: any;
    stockStatusId: number;
  }
  
  export interface ProductDetails {
    id: number;
    name: string;
    description: string;
    shopId: number;
    tenantId: number;
    sellerName: string;
    productStatus: string;
    productStatusId: number;
    returnPeriod: any;
    isRefundable: boolean;
    productVariances: ProductVariance[];
    channelName: string;
    channelId: number;
    isShopVisible: boolean;
    isVisible: boolean;
  }
  
  export interface Product {
    productId: number;
    productDetails: ProductDetails;
    badgesList: any[]; 
  }
  
  export interface Section {
    sectionId: number;
    sectionName: string;
    sectionTypeID: number;
    label: string;
    productsLimit: number;
    products: Product[]; 
  }
  
  export interface ProductResponse {
    success: boolean;
    data: {
      records: Section[];  
      total: number;
      hasNext: boolean;
    };
  }
  