{"ast": null, "code": "import { BehaviorSubject, map } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./main-data.service\";\nimport * as i3 from \"@ngx-translate/core\";\nexport let LanguageService = /*#__PURE__*/(() => {\n  class LanguageService {\n    http;\n    mainDataService;\n    translate;\n    language = new BehaviorSubject('en');\n    numLang = localStorage.getItem('lang') ?? 'en';\n    constructor(http, mainDataService, translate) {\n      this.http = http;\n      this.mainDataService = mainDataService;\n      this.translate = translate;\n    }\n    getLanguage() {\n      return this.http.get(`${environment.apiEndPoint}/Tenant/Language/GetAllLanguage`).pipe(map(res => {\n        let setLanguage;\n        const allLanguages = res.data.records;\n        res.data?.records.forEach(childObj => {\n          let lng = this.mainDataService.browserLanguge ? this.mainDataService.browserLanguge : localStorage.getItem(\"browserLanguage\");\n          if (lng == childObj.code) {\n            setLanguage = childObj.code;\n          }\n        });\n        let defaultLanguage = res.data?.records.filter(lang => lang.isDefault);\n        if (!defaultLanguage.length && res.data?.records.length) {\n          const nonDefaultLanguage = res.data?.records[0];\n          localStorage.setItem('lang', nonDefaultLanguage.code);\n          this.translate.setDefaultLang(nonDefaultLanguage.code);\n          this.translate.use(nonDefaultLanguage.code);\n        } else {\n          const lang = setLanguage ?? defaultLanguage[0]?.code;\n          localStorage.setItem('lang', lang);\n          this.translate.setDefaultLang(lang);\n          this.translate.use(lang);\n        }\n        return {\n          languages: res?.data?.records\n        };\n      }));\n    }\n    get currentLang() {\n      let numLang = localStorage.getItem('numLang');\n      return numLang ? numLang : this.numLang;\n    }\n    setCurrentLang(lang) {\n      localStorage.setItem('numLang', lang);\n    }\n    static ɵfac = function LanguageService_Factory(t) {\n      return new (t || LanguageService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.MainDataService), i0.ɵɵinject(i3.TranslateService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LanguageService,\n      factory: LanguageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LanguageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}