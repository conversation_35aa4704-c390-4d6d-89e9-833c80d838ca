{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RegisterService {\n  constructor(http) {\n    this.http = http;\n    this.username = \"\";\n    this.countryId = \"\";\n    this.otpBaseUrl = `${environment.apiEndPoint}/Auth/OtpUser`;\n    this.userBaseUrl = `${environment.apiEndPoint}/Auth/User`;\n  }\n  checkMobileNumber(data) {\n    return this.http.post(`${this.otpBaseUrl}/CheckUserNameAvailability`, data);\n  }\n  checkSecondaryMobileNumber(data) {\n    return this.http.post(`${this.userBaseUrl}/CheckUserNumberAvailabilityForSecondaryandPrimary`, data);\n  }\n  checkOTP(data) {\n    return this.http.post(`${this.userBaseUrl}/VerifyMobileNumber`, data);\n  }\n  registerUser(data) {\n    return this.http.post(`${this.userBaseUrl}/RegisterPortal`, data);\n  }\n  VerifyForgotPassword(data) {\n    return this.http.post(`${this.userBaseUrl}/VerifyForgotPassword`, data);\n  }\n  static #_ = this.ɵfac = function RegisterService_Factory(t) {\n    return new (t || RegisterService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RegisterService,\n    factory: RegisterService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "RegisterService", "constructor", "http", "username", "countryId", "otpBaseUrl", "apiEndPoint", "userBaseUrl", "checkMobileNumber", "data", "post", "checkSecondaryMobileNumber", "checkOTP", "registerUser", "VerifyForgotPassword", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\register.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from 'src/environments/environment';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {Observable} from \"rxjs\";\r\nimport {CheckMobile, CheckOtp, RegisterUser} from '../../core/interface';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RegisterService {\r\n  toggleBoolean :any;\r\n  otpBaseUrl: string;\r\n  userBaseUrl: string;\r\n  userPassword:any;\r\n  username: string = \"\";\r\n  countryId: string = \"\";\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.otpBaseUrl = `${environment.apiEndPoint}/Auth/OtpUser`;\r\n    this.userBaseUrl = `${environment.apiEndPoint}/Auth/User`;\r\n  }\r\n\r\n  checkMobileNumber(data: CheckMobile): Observable<object> {\r\n    return this.http.post(`${this.otpBaseUrl}/CheckUserNameAvailability`, data);\r\n  }\r\n\r\n  checkSecondaryMobileNumber(data: any): Observable<object> {\r\n    return this.http.post(`${this.userBaseUrl}/CheckUserNumberAvailabilityForSecondaryandPrimary`, data);\r\n  }\r\n\r\n  checkOTP(data: CheckOtp): Observable<object> {\r\n    return this.http.post(`${this.userBaseUrl}/VerifyMobileNumber`, data);\r\n  }\r\n\r\n  registerUser(data: RegisterUser): Observable<object> {\r\n    return this.http.post(`${this.userBaseUrl}/RegisterPortal`, data);\r\n  }\r\n\r\n  VerifyForgotPassword(data: any): Observable<object> {\r\n\r\n    return this.http.post(`${this.userBaseUrl}/VerifyForgotPassword`, data);\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,8BAA8B;;;AAQxD,OAAM,MAAOC,eAAe;EAQ1BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJd,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IAKpB,IAAI,CAACC,UAAU,GAAG,GAAGN,WAAW,CAACO,WAAW,eAAe;IAC3D,IAAI,CAACC,WAAW,GAAG,GAAGR,WAAW,CAACO,WAAW,YAAY;EAC3D;EAEAE,iBAAiBA,CAACC,IAAiB;IACjC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACL,UAAU,4BAA4B,EAAEI,IAAI,CAAC;EAC7E;EAEAE,0BAA0BA,CAACF,IAAS;IAClC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACH,WAAW,oDAAoD,EAAEE,IAAI,CAAC;EACtG;EAEAG,QAAQA,CAACH,IAAc;IACrB,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACH,WAAW,qBAAqB,EAAEE,IAAI,CAAC;EACvE;EAEAI,YAAYA,CAACJ,IAAkB;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACH,WAAW,iBAAiB,EAAEE,IAAI,CAAC;EACnE;EAEAK,oBAAoBA,CAACL,IAAS;IAE5B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACH,WAAW,uBAAuB,EAAEE,IAAI,CAAC;EACzE;EAAC,QAAAM,CAAA,G;qBAlCUf,eAAe,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfpB,eAAe;IAAAqB,OAAA,EAAfrB,eAAe,CAAAsB,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}