{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport let DeliveryDateFormatPipe = /*#__PURE__*/(() => {\n  class DeliveryDateFormatPipe {\n    translateService;\n    constructor(translateService) {\n      this.translateService = translateService;\n    }\n    transform(dateString) {\n      if (!dateString) return '';\n      try {\n        const parts = dateString.split('/');\n        if (parts.length !== 3) return dateString;\n        const day = parseInt(parts[0], 10);\n        const month = parseInt(parts[1], 10) - 1;\n        const year = parseInt(parts[2], 10);\n        const date = new Date(year, month, day);\n        if (isNaN(date.getTime())) return dateString;\n        // Get current language\n        const currentLang = this.translateService.currentLang || 'en';\n        console.log(currentLang);\n        // Day names based on language\n        const dayNames = currentLang === 'fr' ? ['<PERSON>man<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Mercredi', '<PERSON><PERSON>', 'Vendredi', '<PERSON>di'] : ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n        const dayOfWeek = dayNames[date.getDay()];\n        // Month names based on language\n        const monthNames = currentLang === 'fr' ? ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'] : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const monthName = monthNames[date.getMonth()];\n        const getOrdinalSuffix = day => {\n          if (day > 3 && day < 21) return 'ᵗʰ';\n          switch (day % 10) {\n            case 1:\n              return 'ˢᵗ';\n            case 2:\n              return 'ⁿᵈ';\n            case 3:\n              return 'ʳᵈ';\n            default:\n              return 'ᵗʰ';\n          }\n        };\n        const ordinalSuffix = getOrdinalSuffix(day);\n        return `${dayOfWeek} ${day}${ordinalSuffix} ${monthName} ${year}`;\n      } catch (error) {\n        console.error('Error formatting date:', error);\n        return dateString;\n      }\n    }\n    static ɵfac = function DeliveryDateFormatPipe_Factory(t) {\n      return new (t || DeliveryDateFormatPipe)(i0.ɵɵdirectiveInject(i1.TranslateService, 16));\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"deliveryDateFormat\",\n      type: DeliveryDateFormatPipe,\n      pure: true\n    });\n  }\n  return DeliveryDateFormatPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}