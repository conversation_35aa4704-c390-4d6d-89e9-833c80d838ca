.rtl {
  direction: rtl !important;
}

.deep-rtl {
  * {
    direction: rtl !important;
  }
}

[dir="rtl"] {
  owl-stage {
    float: left !important;
  }

  .p-inputgroup-addon {
    &:first-child {
      border-top-left-radius: unset !important;
      border-bottom-left-radius: unset !important;
      border-left: unset !important;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      border-right: 1px solid #ced4da;
    }

    &:last-child {
      border-top-right-radius: unset !important;
      border-bottom-right-radius: unset !important;
      border-right: unset !important;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      border-left: 1px solid #ced4da;
    }
  }

  .p-inputgroup {
    button {
      &:first-child {
        border-top-left-radius: unset !important;
        border-bottom-left-radius: unset !important;
        border-left: unset !important;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        border-right: 1px solid #ced4da;
      }

      &:last-child {
        border-top-right-radius: unset !important;
        border-bottom-right-radius: unset !important;
        border-right: unset !important;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-left: 1px solid #ced4da;
      }
    }

    input {
      &:first-child {
        border-top-left-radius: unset !important;
        border-bottom-left-radius: unset !important;
        border-left: unset !important;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        border-right: 1px solid #ced4da;
      }

      &:last-child {
        border-top-right-radius: unset !important;
        border-bottom-right-radius: unset !important;
        border-right: unset !important;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-left: 1px solid #ced4da;
      }
    }

    > .p-inputwrapper {
      &:first-child {
        > .p-component {
          border-top-left-radius: unset !important;
          border-bottom-left-radius: unset !important;
          border-left: unset !important;
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
          border-right: 1px solid #ced4da;

          > .p-inputtext {
            border-top-left-radius: unset !important;
            border-bottom-left-radius: unset !important;
            border-left: unset !important;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
            border-right: 1px solid #ced4da;
          }
        }
      }

      &:last-child {
        > .p-component {
          border-top-right-radius: unset !important;
          border-bottom-right-radius: unset !important;
          border-right: unset !important;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
          border-left: 1px solid #ced4da;

          > .p-inputtext {
            border-top-right-radius: unset !important;
            border-bottom-right-radius: unset !important;
            border-right: unset !important;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            border-left: 1px solid #ced4da;
          }
        }
      }
    }
  }

  .pi-bars:before {
    content: "\e91d";
  }

  .p-float-label {
    label {
      right: 0.5rem !important;
    }
  }


  .mr-0 {
    margin-left: 0 !important;
    margin-right: unset !important;
  }

  .mr-1 {
    margin-left: 0.25rem !important;
    margin-right: unset !important;
  }

  .mr-2 {
    margin-left: 0.5rem !important;
    margin-right: unset !important;
  }

  .mr-3 {
    margin-left: 1rem !important;
    margin-right: unset !important;
  }

  .mr-4 {
    margin-left: 1.5rem !important;
    margin-right: unset !important;
  }

  .mr-5 {
    margin-left: 2rem !important;
    margin-right: unset !important;
  }

  .mr-6 {
    margin-left: 3rem !important;
    margin-right: unset !important;
  }

  .mr-7 {
    margin-left: 4rem !important;
    margin-right: unset !important;
  }

  .mr-8 {
    margin-left: 5rem !important;
    margin-right: unset !important;
  }

  .-mr-1 {
    margin-left: -0.25rem !important;
    margin-right: unset !important;
  }

  .-mr-2 {
    margin-left: -0.5rem !important;
    margin-right: unset !important;
  }

  .-mr-3 {
    margin-left: -1rem !important;
    margin-right: unset !important;
  }

  .-mr-4 {
    margin-left: -1.5rem !important;
    margin-right: unset !important;
  }

  .-mr-5 {
    margin-left: -2rem !important;
    margin-right: unset !important;
  }

  .-mr-6 {
    margin-left: -3rem !important;
    margin-right: unset !important;
  }

  .-mr-7 {
    margin-left: -4rem !important;
    margin-right: unset !important;
  }

  .-mr-8 {
    margin-left: -5rem !important;
    margin-right: unset !important;
  }


  .ml-0 {
    margin-right: 0 !important;
    margin-left: unset !important;
  }

  .ml-1 {
    margin-right: 0.25rem !important;
    margin-left: unset !important;
  }

  .ml-2 {
    margin-right: 0.5rem !important;
    margin-left: unset !important;
  }

  .ml-3 {
    margin-right: 1rem !important;
    margin-left: unset !important;
  }

  .ml-4 {
    margin-right: 1.5rem !important;
    margin-left: unset !important;
  }

  .ml-5 {
    margin-right: 2rem !important;
    margin-left: unset !important;
  }

  .ml-6 {
    margin-right: 3rem !important;
    margin-left: unset !important;
  }

  .ml-7 {
    margin-right: 4rem !important;
    margin-left: unset !important;
  }

  .ml-8 {
    margin-right: 5rem !important;
    margin-left: unset !important;
  }

  .-ml-1 {
    margin-right: -0.25rem !important;
    margin-left: unset !important;
  }

  .-ml-2 {
    margin-right: -0.5rem !important;
    margin-left: unset !important;
  }

  .-ml-3 {
    margin-right: -1rem !important;
    margin-left: unset !important;
  }

  .-ml-4 {
    margin-right: -1.5rem !important;
    margin-left: unset !important;
  }

  .-ml-5 {
    margin-right: -2rem !important;
    margin-left: unset !important;
  }

  .-ml-6 {
    margin-right: -3rem !important;
    margin-left: unset !important;
  }

  .-ml-7 {
    margin-right: -4rem !important;
    margin-left: unset !important;
  }

  .-ml-8 {
    margin-right: -5rem !important;
    margin-left: unset !important;
  }


  .pr-0 {
    padding-left: 0 !important;
    padding-right: unset !important;
  }

  .pr-1 {
    padding-left: 0.25rem !important;
    padding-right: unset !important;
  }

  .pr-2 {
    padding-left: 0.5rem !important;
    padding-right: unset !important;
  }

  .pr-3 {
    padding-left: 1rem !important;
    padding-right: unset !important;
  }

  .pr-4 {
    padding-left: 1.5rem !important;
    padding-right: unset !important;
  }

  .pr-5 {
    padding-left: 2rem !important;
    padding-right: unset !important;
  }

  .pr-6 {
    padding-left: 3rem !important;
    padding-right: unset !important;
  }

  .pr-7 {
    padding-left: 4rem !important;
    padding-right: unset !important;
  }

  .pr-8 {
    padding-left: 5rem !important;
    padding-right: unset !important;
  }


  .pl-0 {
    padding-right: 0 !important;
    padding-left: unset !important;
  }

  .pl-1 {
    padding-right: 0.25rem !important;
    padding-left: unset !important;
  }

  .pl-2 {
    padding-right: 0.5rem !important;
    padding-left: unset !important;
  }

  .pl-3 {
    padding-right: 1rem !important;
    padding-left: unset !important;
  }

  .pl-4 {
    padding-right: 1.5rem !important;
    padding-left: unset !important;
  }

  .pl-5 {
    padding-right: 2rem !important;
    padding-left: unset !important;
  }

  .pl-6 {
    padding-right: 3rem !important;
    padding-left: unset !important;
  }

  .pl-7 {
    padding-right: 4rem !important;
    padding-left: unset !important;
  }

  .pl-8 {
    padding-right: 5rem !important;
    padding-left: unset !important;
  }

  .product-flag {
    border-radius: 10px 10px 0 8px;
  }
}
