"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[326],{3214:(ue,N,y)=>{y.d(N,{_:()=>D});var d=y(553),L=y(5879),w=y(9862);let D=(()=>{class x{http;baseUrl;constructor(R){this.http=R,this.baseUrl=`${d.N.apiEndPoint}`}getLiveMerchantData(){return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetLiveMerchants`)}getLiveStreamDetailById(R){return this.http.get(`${this.baseUrl}/Tenant/Streaming/GetStreamDetail/${R}`)}static \u0275fac=function(v){return new(v||x)(L.LFG(w.eN))};static \u0275prov=L.Yz7({token:x,factory:x.\u0275fac,providedIn:"root"})}return x})()},3326:(ue,N,y)=>{y.d(N,{x:()=>kt,B:()=>Le});var d={};y.r(d),y.d(d,{Decoder:()=>le,Encoder:()=>ut,PacketType:()=>u,protocol:()=>ft});var L=y(553);const w=Object.create(null);w.open="0",w.close="1",w.ping="2",w.pong="3",w.message="4",w.upgrade="5",w.noop="6";const D=Object.create(null);Object.keys(w).forEach(i=>{D[w[i]]=i});const x={type:"error",data:"parser error"},V="function"==typeof Blob||typeof Blob<"u"&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),R="function"==typeof ArrayBuffer,v=i=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(i):i&&i.buffer instanceof ArrayBuffer,ee=({type:i,data:t},e,n)=>V&&t instanceof Blob?e?n(t):o(t,n):R&&(t instanceof ArrayBuffer||v(t))?e?n(t):o(new Blob([t]),n):n(w[i]+(t||"")),o=(i,t)=>{const e=new FileReader;return e.onload=function(){const n=e.result.split(",")[1];t("b"+(n||""))},e.readAsDataURL(i)};function c(i){return i instanceof Uint8Array?i:i instanceof ArrayBuffer?new Uint8Array(i):new Uint8Array(i.buffer,i.byteOffset,i.byteLength)}let l;const _=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let i=0;i<64;i++)_["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(i)]=i;const I="function"==typeof ArrayBuffer,U=(i,t)=>{if("string"!=typeof i)return{type:"message",data:S(i,t)};const e=i.charAt(0);return"b"===e?{type:"message",data:Y(i.substring(1),t)}:D[e]?i.length>1?{type:D[e],data:i.substring(1)}:{type:D[e]}:x},Y=(i,t)=>{if(I){const e=(i=>{let n,a,h,f,m,t=.75*i.length,e=i.length,s=0;"="===i[i.length-1]&&(t--,"="===i[i.length-2]&&t--);const M=new ArrayBuffer(t),T=new Uint8Array(M);for(n=0;n<e;n+=4)a=_[i.charCodeAt(n)],h=_[i.charCodeAt(n+1)],f=_[i.charCodeAt(n+2)],m=_[i.charCodeAt(n+3)],T[s++]=a<<2|h>>4,T[s++]=(15&h)<<4|f>>2,T[s++]=(3&f)<<6|63&m;return M})(i);return S(e,t)}return{base64:!0,data:i}},S=(i,t)=>"blob"===t?i instanceof Blob?i:new Blob([i]):i instanceof ArrayBuffer?i:i.buffer,W=String.fromCharCode(30);let ne;function z(i){return i.reduce((t,e)=>t+e.length,0)}function J(i,t){if(i[0].length===t)return i.shift();const e=new Uint8Array(t);let n=0;for(let s=0;s<t;s++)e[s]=i[0][n++],n===i[0].length&&(i.shift(),n=0);return i.length&&n<i[0].length&&(i[0]=i[0].slice(n)),e}function b(i){if(i)return function Ie(i){for(var t in b.prototype)i[t]=b.prototype[t];return i}(i)}b.prototype.on=b.prototype.addEventListener=function(i,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+i]=this._callbacks["$"+i]||[]).push(t),this},b.prototype.once=function(i,t){function e(){this.off(i,e),t.apply(this,arguments)}return e.fn=t,this.on(i,e),this},b.prototype.off=b.prototype.removeListener=b.prototype.removeAllListeners=b.prototype.removeEventListener=function(i,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var e=this._callbacks["$"+i];if(!e)return this;if(1==arguments.length)return delete this._callbacks["$"+i],this;for(var n,s=0;s<e.length;s++)if((n=e[s])===t||n.fn===t){e.splice(s,1);break}return 0===e.length&&delete this._callbacks["$"+i],this},b.prototype.emitReserved=b.prototype.emit=function(i){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),e=this._callbacks["$"+i],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(e){n=0;for(var s=(e=e.slice(0)).length;n<s;++n)e[n].apply(this,t)}return this},b.prototype.listeners=function(i){return this._callbacks=this._callbacks||{},this._callbacks["$"+i]||[]},b.prototype.hasListeners=function(i){return!!this.listeners(i).length};const E=typeof self<"u"?self:typeof window<"u"?window:Function("return this")();function me(i,...t){return t.reduce((e,n)=>(i.hasOwnProperty(n)&&(e[n]=i[n]),e),{})}const Ue=E.setTimeout,We=E.clearTimeout;function $(i,t){t.useNativeTimers?(i.setTimeoutFn=Ue.bind(E),i.clearTimeoutFn=We.bind(E)):(i.setTimeoutFn=E.setTimeout.bind(E),i.clearTimeoutFn=E.clearTimeout.bind(E))}function He(i){return"string"==typeof i?function Fe(i){let t=0,e=0;for(let n=0,s=i.length;n<s;n++)t=i.charCodeAt(n),t<128?e+=1:t<2048?e+=2:t<55296||t>=57344?e+=3:(n++,e+=4);return e}(i):Math.ceil(1.33*(i.byteLength||i.size))}class Ze extends Error{constructor(t,e,n){super(t),this.description=e,this.context=n,this.type="TransportError"}}class ie extends b{constructor(t){super(),this.writable=!1,$(this,t),this.opts=t,this.query=t.query,this.socket=t.socket}onError(t,e,n){return super.emitReserved("error",new Ze(t,e,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const e=U(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){const t=this.opts.hostname;return-1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){const e=function Ve(i){let t="";for(let e in i)i.hasOwnProperty(e)&&(t.length&&(t+="&"),t+=encodeURIComponent(e)+"="+encodeURIComponent(i[e]));return t}(t);return e.length?"?"+e:""}}const ge="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),K=64,_e={};let be,ye=0,A=0;function ve(i){let t="";do{t=ge[i%K]+t,i=Math.floor(i/K)}while(i>0);return t}function we(){const i=ve(+new Date);return i!==be?(ye=0,be=i):i+"."+ve(ye++)}for(;A<K;A++)_e[ge[A]]=A;let xe=!1;try{xe=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const ze=xe;function Ce(i){const t=i.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||ze))return new XMLHttpRequest}catch{}if(!t)try{return new(E[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch{}}function Je(){}const $e=null!=new Ce({xdomain:!1}).responseType;let Q=(()=>{class i extends b{constructor(e,n){super(),$(this,n),this.opts=n,this.method=n.method||"GET",this.uri=e,this.data=void 0!==n.data?n.data:null,this.create()}create(){var e;const n=me(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this.opts.xd;const s=this.xhr=new Ce(n);try{s.open(this.method,this.uri,!0);try{if(this.opts.extraHeaders){s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0);for(let a in this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(a)&&s.setRequestHeader(a,this.opts.extraHeaders[a])}}catch{}if("POST"===this.method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{s.setRequestHeader("Accept","*/*")}catch{}null===(e=this.opts.cookieJar)||void 0===e||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(s.timeout=this.opts.requestTimeout),s.onreadystatechange=()=>{var a;3===s.readyState&&(null===(a=this.opts.cookieJar)||void 0===a||a.parseCookies(s)),4===s.readyState&&(200===s.status||1223===s.status?this.onLoad():this.setTimeoutFn(()=>{this.onError("number"==typeof s.status?s.status:0)},0))},s.send(this.data)}catch(a){return void this.setTimeoutFn(()=>{this.onError(a)},0)}typeof document<"u"&&(this.index=i.requestsCount++,i.requests[this.index]=this)}onError(e){this.emitReserved("error",e,this.xhr),this.cleanup(!0)}cleanup(e){if(!(typeof this.xhr>"u"||null===this.xhr)){if(this.xhr.onreadystatechange=Je,e)try{this.xhr.abort()}catch{}typeof document<"u"&&delete i.requests[this.index],this.xhr=null}}onLoad(){const e=this.xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this.cleanup())}abort(){this.cleanup()}}return i.requestsCount=0,i.requests={},i})();function Ee(){for(let i in Q.requests)Q.requests.hasOwnProperty(i)&&Q.requests[i].abort()}typeof document<"u"&&("function"==typeof attachEvent?attachEvent("onunload",Ee):"function"==typeof addEventListener&&addEventListener("onpagehide"in E?"pagehide":"unload",Ee,!1));const se="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),j=E.WebSocket||E.MozWebSocket,ke=typeof navigator<"u"&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),Ge={websocket:class je extends ie{constructor(t){super(t),this.supportsBinary=!t.forceBase64}get name(){return"websocket"}doOpen(){if(!this.check())return;const t=this.uri(),e=this.opts.protocols,n=ke?{}:me(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=ke?new j(t,e,n):e?new j(t,e):new j(t)}catch(s){return this.emitReserved("error",s)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){const s=e===t.length-1;ee(t[e],this.supportsBinary,a=>{try{this.ws.send(a)}catch{}s&&se(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=we()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}check(){return!!j}},webtransport:class Xe extends ie{get name(){return"webtransport"}doOpen(){"function"==typeof WebTransport&&(this.transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name]),this.transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this.transport.ready.then(()=>{this.transport.createBidirectionalStream().then(t=>{const e=function Ne(i,t){ne||(ne=new TextDecoder);const e=[];let n=0,s=-1,a=!1;return new TransformStream({transform(h,f){for(e.push(h);;){if(0===n){if(z(e)<1)break;const m=J(e,1);a=128==(128&m[0]),s=127&m[0],n=s<126?3:126===s?1:2}else if(1===n){if(z(e)<2)break;const m=J(e,2);s=new DataView(m.buffer,m.byteOffset,m.length).getUint16(0),n=3}else if(2===n){if(z(e)<8)break;const m=J(e,8),M=new DataView(m.buffer,m.byteOffset,m.length),T=M.getUint32(0);if(T>Math.pow(2,21)-1){f.enqueue(x);break}s=T*Math.pow(2,32)+M.getUint32(4),n=3}else{if(z(e)<s)break;const m=J(e,s);f.enqueue(U(a?m:ne.decode(m),t)),n=0}if(0===s||s>i){f.enqueue(x);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=t.readable.pipeThrough(e).getReader(),s=function Be(){return new TransformStream({transform(i,t){!function p(i,t){V&&i.data instanceof Blob?i.data.arrayBuffer().then(c).then(t):R&&(i.data instanceof ArrayBuffer||v(i.data))?t(c(i.data)):ee(i,!1,e=>{l||(l=new TextEncoder),t(l.encode(e))})}(i,e=>{const n=e.length;let s;if(n<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,n);else if(n<65536){s=new Uint8Array(3);const a=new DataView(s.buffer);a.setUint8(0,126),a.setUint16(1,n)}else{s=new Uint8Array(9);const a=new DataView(s.buffer);a.setUint8(0,127),a.setBigUint64(1,BigInt(n))}i.data&&"string"!=typeof i.data&&(s[0]|=128),t.enqueue(s),t.enqueue(e)})}})}();s.readable.pipeTo(t.writable),this.writer=s.writable.getWriter();const a=()=>{n.read().then(({done:f,value:m})=>{f||(this.onPacket(m),a())}).catch(f=>{})};a();const h={type:"open"};this.query.sid&&(h.data=`{"sid":"${this.query.sid}"}`),this.writer.write(h).then(()=>this.onOpen())})}))}write(t){this.writable=!1;for(let e=0;e<t.length;e++){const s=e===t.length-1;this.writer.write(t[e]).then(()=>{s&&se(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null===(t=this.transport)||void 0===t||t.close()}},polling:class Ke extends ie{constructor(t){if(super(t),this.polling=!1,typeof location<"u"){const n="https:"===location.protocol;let s=location.port;s||(s=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||s!==t.port}this.supportsBinary=$e&&!(t&&t.forceBase64),this.opts.withCredentials&&(this.cookieJar=void 0)}get name(){return"polling"}doOpen(){this.poll()}pause(t){this.readyState="pausing";const e=()=>{this.readyState="paused",t()};if(this.polling||!this.writable){let n=0;this.polling&&(n++,this.once("pollComplete",function(){--n||e()})),this.writable||(n++,this.once("drain",function(){--n||e()}))}else e()}poll(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){((i,t)=>{const e=i.split(W),n=[];for(let s=0;s<e.length;s++){const a=U(e[s],t);if(n.push(a),"error"===a.type)break}return n})(t,this.socket.binaryType).forEach(n=>{if("opening"===this.readyState&&"open"===n.type&&this.onOpen(),"close"===n.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(n)}),"closed"!==this.readyState&&(this.polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.poll())}doClose(){const t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,((i,t)=>{const e=i.length,n=new Array(e);let s=0;i.forEach((a,h)=>{ee(a,!1,f=>{n[h]=f,++s===e&&t(n.join(W))})})})(t,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=we()),!this.supportsBinary&&!e.sid&&(e.b64=1),this.createUri(t,e)}request(t={}){return Object.assign(t,{xd:this.xd,cookieJar:this.cookieJar},this.opts),new Q(this.uri(),t)}doWrite(t,e){const n=this.request({method:"POST",data:t});n.on("success",e),n.on("error",(s,a)=>{this.onError("xhr post error",s,a)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(e,n)=>{this.onError("xhr poll error",e,n)}),this.pollXhr=t}}},et=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,tt=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function oe(i){if(i.length>2e3)throw"URI too long";const t=i,e=i.indexOf("["),n=i.indexOf("]");-1!=e&&-1!=n&&(i=i.substring(0,e)+i.substring(e,n).replace(/:/g,";")+i.substring(n,i.length));let s=et.exec(i||""),a={},h=14;for(;h--;)a[tt[h]]=s[h]||"";return-1!=e&&-1!=n&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=function nt(i,t){const n=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&n.splice(0,1),"/"==t.slice(-1)&&n.splice(n.length-1,1),n}(0,a.path),a.queryKey=function it(i,t){const e={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,s,a){s&&(e[s]=a)}),e}(0,a.query),a}let Oe=(()=>{class i extends b{constructor(e,n={}){super(),this.binaryType="arraybuffer",this.writeBuffer=[],e&&"object"==typeof e&&(n=e,e=null),e?(e=oe(e),n.hostname=e.host,n.secure="https"===e.protocol||"wss"===e.protocol,n.port=e.port,e.query&&(n.query=e.query)):n.host&&(n.hostname=oe(n.host).host),$(this,n),this.secure=null!=n.secure?n.secure:typeof location<"u"&&"https:"===location.protocol,n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=n.transports||["polling","websocket","webtransport"],this.writeBuffer=[],this.prevBufferLen=0,this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function Ye(i){let t={},e=i.split("&");for(let n=0,s=e.length;n<s;n++){let a=e[n].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1])}return t}(this.opts.query)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingTimeoutTimer=null,"function"==typeof addEventListener&&(this.opts.closeOnBeforeunload&&(this.beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this.beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this.offlineEventListener=()=>{this.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",this.offlineEventListener,!1))),this.open()}createTransport(e){const n=Object.assign({},this.opts.query);n.EIO=4,n.transport=e,this.id&&(n.sid=this.id);const s=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new Ge[e](s)}open(){let e;if(this.opts.rememberUpgrade&&i.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))e="websocket";else{if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);e=this.transports[0]}this.readyState="opening";try{e=this.createTransport(e)}catch{return this.transports.shift(),void this.open()}e.open(),this.setTransport(e)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",n=>this.onClose("transport close",n))}probe(e){let n=this.createTransport(e),s=!1;i.priorWebsocketSuccess=!1;const a=()=>{s||(n.send([{type:"ping",data:"probe"}]),n.once("packet",B=>{if(!s)if("pong"===B.type&&"probe"===B.data){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;i.priorWebsocketSuccess="websocket"===n.name,this.transport.pause(()=>{s||"closed"!==this.readyState&&(Pe(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const F=new Error("probe error");F.transport=n.name,this.emitReserved("upgradeError",F)}}))};function h(){s||(s=!0,Pe(),n.close(),n=null)}const f=B=>{const F=new Error("probe error: "+B);F.transport=n.name,h(),this.emitReserved("upgradeError",F)};function m(){f("transport closed")}function M(){f("socket closed")}function T(B){n&&B.name!==n.name&&h()}const Pe=()=>{n.removeListener("open",a),n.removeListener("error",f),n.removeListener("close",m),this.off("close",M),this.off("upgrading",T)};n.once("open",a),n.once("error",f),n.once("close",m),this.once("close",M),this.once("upgrading",T),-1!==this.upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||n.open()},200):n.open()}onOpen(){if(this.readyState="open",i.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush(),"open"===this.readyState&&this.opts.upgrade){let e=0;const n=this.upgrades.length;for(;e<n;e++)this.probe(this.upgrades[e])}}onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),this.resetPingTimeout(),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":const n=new Error("server error");n.code=e.data,this.onError(n);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.resetPingTimeout()}resetPingTimeout(){this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn(()=>{this.onClose("ping timeout")},this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}onDrain(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this.getWritablePackets();this.transport.send(e),this.prevBufferLen=e.length,this.emitReserved("flush")}}getWritablePackets(){if(!(this.maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let s=0;s<this.writeBuffer.length;s++){const a=this.writeBuffer[s].data;if(a&&(n+=He(a)),s>0&&n>this.maxPayload)return this.writeBuffer.slice(0,s);n+=2}return this.writeBuffer}write(e,n,s){return this.sendPacket("message",e,n,s),this}send(e,n,s){return this.sendPacket("message",e,n,s),this}sendPacket(e,n,s,a){if("function"==typeof n&&(a=n,n=void 0),"function"==typeof s&&(a=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;const h={type:e,data:n,options:s};this.emitReserved("packetCreate",h),this.writeBuffer.push(h),a&&this.once("flush",a),this.flush()}close(){const e=()=>{this.onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),e()},s=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}onError(e){i.priorWebsocketSuccess=!1,this.emitReserved("error",e),this.onClose("transport error",e)}onClose(e,n){("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)&&(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),"function"==typeof removeEventListener&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",e,n),this.writeBuffer=[],this.prevBufferLen=0)}filterUpgrades(e){const n=[];let s=0;const a=e.length;for(;s<a;s++)~this.transports.indexOf(e[s])&&n.push(e[s]);return n}}return i.protocol=4,i})();const rt="function"==typeof ArrayBuffer,ot=i=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(i):i.buffer instanceof ArrayBuffer,Se=Object.prototype.toString,at="function"==typeof Blob||typeof Blob<"u"&&"[object BlobConstructor]"===Se.call(Blob),ct="function"==typeof File||typeof File<"u"&&"[object FileConstructor]"===Se.call(File);function ae(i){return rt&&(i instanceof ArrayBuffer||ot(i))||at&&i instanceof Blob||ct&&i instanceof File}function X(i,t){if(!i||"object"!=typeof i)return!1;if(Array.isArray(i)){for(let e=0,n=i.length;e<n;e++)if(X(i[e]))return!0;return!1}if(ae(i))return!0;if(i.toJSON&&"function"==typeof i.toJSON&&1===arguments.length)return X(i.toJSON(),!0);for(const e in i)if(Object.prototype.hasOwnProperty.call(i,e)&&X(i[e]))return!0;return!1}function ht(i){const t=[],n=i;return n.data=ce(i.data,t),n.attachments=t.length,{packet:n,buffers:t}}function ce(i,t){if(!i)return i;if(ae(i)){const e={_placeholder:!0,num:t.length};return t.push(i),e}if(Array.isArray(i)){const e=new Array(i.length);for(let n=0;n<i.length;n++)e[n]=ce(i[n],t);return e}if("object"==typeof i&&!(i instanceof Date)){const e={};for(const n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=ce(i[n],t));return e}return i}function lt(i,t){return i.data=he(i.data,t),delete i.attachments,i}function he(i,t){if(!i)return i;if(i&&!0===i._placeholder){if("number"==typeof i.num&&i.num>=0&&i.num<t.length)return t[i.num];throw new Error("illegal attachments")}if(Array.isArray(i))for(let e=0;e<i.length;e++)i[e]=he(i[e],t);else if("object"==typeof i)for(const e in i)Object.prototype.hasOwnProperty.call(i,e)&&(i[e]=he(i[e],t));return i}const dt=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ft=5;var u=function(i){return i[i.CONNECT=0]="CONNECT",i[i.DISCONNECT=1]="DISCONNECT",i[i.EVENT=2]="EVENT",i[i.ACK=3]="ACK",i[i.CONNECT_ERROR=4]="CONNECT_ERROR",i[i.BINARY_EVENT=5]="BINARY_EVENT",i[i.BINARY_ACK=6]="BINARY_ACK",i}(u||{});class ut{constructor(t){this.replacer=t}encode(t){return t.type!==u.EVENT&&t.type!==u.ACK||!X(t)?[this.encodeAsString(t)]:this.encodeAsBinary({type:t.type===u.EVENT?u.BINARY_EVENT:u.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id})}encodeAsString(t){let e=""+t.type;return(t.type===u.BINARY_EVENT||t.type===u.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){const e=ht(t),n=this.encodeAsString(e.packet),s=e.buffers;return s.unshift(n),s}}function Re(i){return"[object Object]"===Object.prototype.toString.call(i)}class le extends b{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");e=this.decodeString(t);const n=e.type===u.BINARY_EVENT;n||e.type===u.BINARY_ACK?(e.type=n?u.EVENT:u.ACK,this.reconstructor=new pt(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else{if(!ae(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");e=this.reconstructor.takeBinaryData(t),e&&(this.reconstructor=null,super.emitReserved("decoded",e))}}decodeString(t){let e=0;const n={type:Number(t.charAt(0))};if(void 0===u[n.type])throw new Error("unknown packet type "+n.type);if(n.type===u.BINARY_EVENT||n.type===u.BINARY_ACK){const a=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);const h=t.substring(a,e);if(h!=Number(h)||"-"!==t.charAt(e))throw new Error("Illegal attachments");n.attachments=Number(h)}if("/"===t.charAt(e+1)){const a=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);n.nsp=t.substring(a,e)}else n.nsp="/";const s=t.charAt(e+1);if(""!==s&&Number(s)==s){const a=e+1;for(;++e;){const h=t.charAt(e);if(null==h||Number(h)!=h){--e;break}if(e===t.length)break}n.id=Number(t.substring(a,e+1))}if(t.charAt(++e)){const a=this.tryParse(t.substr(e));if(!le.isPayloadValid(n.type,a))throw new Error("invalid payload");n.data=a}return n}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,e){switch(t){case u.CONNECT:return Re(e);case u.DISCONNECT:return void 0===e;case u.CONNECT_ERROR:return"string"==typeof e||Re(e);case u.EVENT:case u.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===dt.indexOf(e[0]));case u.ACK:case u.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class pt{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const e=lt(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function k(i,t,e){return i.on(t,e),function(){i.off(t,e)}}const mt=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Te extends b{constructor(t,e,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[k(t,"open",this.onopen.bind(this)),k(t,"packet",this.onpacket.bind(this)),k(t,"error",this.onerror.bind(this)),k(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){if(mt.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;const n={type:u.EVENT,data:e,options:{}};if(n.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){const h=this.ids++,f=e.pop();this._registerAckCallback(h,f),n.id=h}return this.flags.volatile&&(!(this.io.engine&&this.io.engine.transport&&this.io.engine.transport.writable)||!this.connected)||(this.connected?(this.notifyOutgoingListeners(n),this.packet(n)):this.sendBuffer.push(n)),this.flags={},this}_registerAckCallback(t,e){var n;const s=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===s)return void(this.acks[t]=e);const a=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let f=0;f<this.sendBuffer.length;f++)this.sendBuffer[f].id===t&&this.sendBuffer.splice(f,1);e.call(this,new Error("operation has timed out"))},s),h=(...f)=>{this.io.clearTimeoutFn(a),e.apply(this,f)};h.withError=!0,this.acks[t]=h}emitWithAck(t,...e){return new Promise((n,s)=>{const a=(h,f)=>h?s(h):n(f);a.withError=!0,e.push(a),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((s,...a)=>n!==this._queue[0]?void 0:(null!==s?n.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(s)):(this._queue.shift(),e&&e(null,...a)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;const e=this._queue[0];e.pending&&!t||(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:u.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(n=>String(n.id)===t)){const n=this.acks[t];delete this.acks[t],n.withError&&n.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case u.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case u.EVENT:case u.BINARY_EVENT:this.onevent(t);break;case u.ACK:case u.BINARY_ACK:this.onack(t);break;case u.DISCONNECT:this.ondisconnect();break;case u.CONNECT_ERROR:this.destroy();const n=new Error(t.data.message);n.data=t.data.data,this.emitReserved("connect_error",n)}}onevent(t){const e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const e=this._anyListeners.slice();for(const n of e)n.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){const e=this;let n=!1;return function(...s){n||(n=!0,e.packet({type:u.ACK,id:t,data:s}))}}onack(t){const e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:u.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const e=this._anyListeners;for(let n=0;n<e.length;n++)if(t===e[n])return e.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const e=this._anyOutgoingListeners;for(let n=0;n<e.length;n++)if(t===e[n])return e.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const e=this._anyOutgoingListeners.slice();for(const n of e)n.apply(this,t.data)}}}function P(i){this.ms=(i=i||{}).min||100,this.max=i.max||1e4,this.factor=i.factor||2,this.jitter=i.jitter>0&&i.jitter<=1?i.jitter:0,this.attempts=0}P.prototype.duration=function(){var i=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),e=Math.floor(t*this.jitter*i);i=1&Math.floor(10*t)?i+e:i-e}return 0|Math.min(i,this.max)},P.prototype.reset=function(){this.attempts=0},P.prototype.setMin=function(i){this.ms=i},P.prototype.setMax=function(i){this.max=i},P.prototype.setJitter=function(i){this.jitter=i};class de extends b{constructor(t,e){var n;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,$(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(n=e.randomizationFactor)&&void 0!==n?n:.5),this.backoff=new P({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;const s=e.parser||d;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null===(e=this.backoff)||void 0===e||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null===(e=this.backoff)||void 0===e||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null===(e=this.backoff)||void 0===e||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Oe(this.uri,this.opts);const e=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const s=k(e,"open",function(){n.onopen(),t&&t()}),a=f=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",f),t?t(f):this.maybeReconnectOnOpen()},h=k(e,"error",a);if(!1!==this._timeout){const m=this.setTimeoutFn(()=>{s(),a(new Error("timeout")),e.close()},this._timeout);this.opts.autoUnref&&m.unref(),this.subs.push(()=>{this.clearTimeoutFn(m)})}return this.subs.push(s),this.subs.push(h),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(k(t,"ping",this.onping.bind(this)),k(t,"data",this.ondata.bind(this)),k(t,"error",this.onerror.bind(this)),k(t,"close",this.onclose.bind(this)),k(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(e){this.onclose("parse error",e)}}ondecoded(t){se(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let n=this.nsps[t];return n?this._autoConnect&&!n.active&&n.connect():(n=new Te(this,t,e),this.nsps[t]=n),n}_destroy(t){const e=Object.keys(this.nsps);for(const n of e)if(this.nsps[n].active)return;this._close()}_packet(t){const e=this.encoder.encode(t);for(let n=0;n<e.length;n++)this.engine.write(e[n],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close"),this.engine&&this.engine.close()}disconnect(){return this._close()}onclose(t,e){this.cleanup(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const e=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(s=>{s?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",s)):t.onreconnect()}))},e);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const q={};function G(i,t){"object"==typeof i&&(t=i,i=void 0);const e=function st(i,t="",e){let n=i;e=e||typeof location<"u"&&location,null==i&&(i=e.protocol+"//"+e.host),"string"==typeof i&&("/"===i.charAt(0)&&(i="/"===i.charAt(1)?e.protocol+i:e.host+i),/^(https?|wss?):\/\//.test(i)||(i=typeof e<"u"?e.protocol+"//"+i:"https://"+i),n=oe(i)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";const a=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+a+":"+n.port+t,n.href=n.protocol+"://"+a+(e&&e.port===n.port?"":":"+n.port),n}(i,(t=t||{}).path||"/socket.io"),n=e.source,s=e.id;let m;return t.forceNew||t["force new connection"]||!1===t.multiplex||q[s]&&e.path in q[s].nsps?m=new de(n,t):(q[s]||(q[s]=new de(n,t)),m=q[s]),e.query&&!t.query&&(t.query=e.queryKey),m.socket(e.path,t)}Object.assign(G,{Manager:de,Socket:Te,io:G,connect:G});var gt=y(5619),r=y(5879),De=y(6075),_t=y(3214),H=y(6814),fe=y(6223),Ae=y(381),yt=y(617);const bt=["receiverVideo"];function vt(i,t){if(1&i){const e=r.EpF();r.TgZ(0,"span")(1,"img",30),r.NdJ("error",function(s){r.CHM(e);const a=r.oxw(2);return r.KtG(a.errorHandler(s))}),r.qZA()()}if(2&i){const e=r.oxw().$implicit,n=r.oxw();r.xp6(1),r.Q6J("src",n.cartListImage(e.specsProducts[0].thumbnailImage),r.LSH)}}function wt(i,t){if(1&i){const e=r.EpF();r.TgZ(0,"div",24)(1,"div",25),r.NdJ("click",function(){const a=r.CHM(e).$implicit,h=r.oxw();return r.KtG(h.navigateToProduct(a.id))}),r.YNc(2,vt,2,1,"span",26),r.TgZ(3,"div",27)(4,"h4",28),r._uU(5),r.qZA(),r.TgZ(6,"p",29),r._uU(7),r.qZA(),r.TgZ(8,"p"),r._uU(9),r.qZA()()()()}if(2&i){const e=t.$implicit;r.xp6(2),r.Q6J("ngIf",e.specsProducts.length>0),r.xp6(3),r.Oqu(e.name),r.xp6(2),r.hij(" ",null==e?null:e.description," "),r.xp6(2),r.Oqu(null==e?null:e.specsProducts[0].stockStatus)}}function xt(i,t){if(1&i&&(r.TgZ(0,"p",31),r._uU(1),r.qZA()),2&i){const e=r.oxw();r.xp6(1),r.hij("LIVE CHAT WITH - ",e.streamerName,"")}}function Ct(i,t){if(1&i&&(r.ynx(0),r.TgZ(1,"strong"),r._uU(2),r.qZA(),r._uU(3),r.BQk()),2&i){const e=r.oxw().$implicit;r.xp6(2),r.hij("",e.message.userName,":"),r.xp6(1),r.hij(" ",e.message.message," ")}}function Et(i,t){if(1&i&&(r.TgZ(0,"div",32),r.YNc(1,Ct,4,2,"ng-container",26),r.qZA()),2&i){const e=t.$implicit,n=r.oxw();r.xp6(1),r.Q6J("ngIf",e.message.room==n.streamId)}}const Me=function(i){return{"btn-disabled":i}};let kt=(()=>{class i{route;livestream;router;location;receiverVideo;editFlag=!1;productList=[];StreamDetail={};routeId;baseUrl=L.N.apiEndPoint;roomId;broadcastersList=[];config={iceServers:[{urls:["stun:stun.l.google.com:19302"]},{urls:"turn:coturn.paysky.io:3478",credential:"somepassword",username:"guest"}]};peerConnection=new RTCPeerConnection(this.config);socket;isConnected;navigateUrl;messagesSubject=new gt.X([]);messages$=this.messagesSubject.asObservable();messageInput="";streamId;isAvalable=!0;userName;streamerName;constructor(e,n,s,a){this.route=e,this.livestream=n,this.router=s,this.location=a,this.socket=G("https://dev-apps.paysky.io",{path:"/onfido-node/socket.io"}),this.route.queryParams.subscribe(h=>{this.streamId=h.streamId,this.roomId=h.roomId,this.getStreamDetail(this.streamId)}),this.userName="Guest-User"+Math.floor(100+900*Math.random()).toString()}ngOnInit(){this.socket.emit("request-broadcasters"),this.socket.on("broadcasters-list",e=>{this.broadcastersList=e,0===this.broadcastersList.filter(s=>s.includes(this.roomId)).length&&(this.isAvalable=!1,this.location.back())}),this.socket.on("isAvailable",e=>{this.isConnected=e}),this.socket.on("chat message",e=>{this.messagesSubject.next([...this.messagesSubject.value,e])}),this.socket.on("offer",(e,n)=>{this.peerConnection.setRemoteDescription(n).then(()=>this.peerConnection.createAnswer()).then(s=>this.peerConnection.setLocalDescription(s)).then(()=>{this.socket.emit("answer",e,this.peerConnection.localDescription)}),this.peerConnection.ontrack=s=>{this.receiverVideo.nativeElement.srcObject=s.streams[0],setTimeout(()=>{this.receiverVideo.nativeElement.play()},1e3)},this.peerConnection.onicecandidate=s=>{s.candidate&&this.socket.emit("candidate",e,s.candidate)}}),this.socket.on("candidate",(e,n)=>{this.peerConnection.addIceCandidate(new RTCIceCandidate(n)).catch(s=>console.error(s))}),this.socket.on("connect",()=>{this.socket.emit("watcher")}),this.socket.on("disconnect",()=>{}),setTimeout(()=>{this.socket.emit("watcher",this.roomId)},1e3)}unloadNotification(e){this.socket.close(),this.peerConnection.close()}getStreamDetail(e){this.livestream.getLiveStreamDetailById(e).subscribe({next:n=>{this.StreamDetail=n?.data,this.streamerName=this.StreamDetail.products[0].sellerName},error:n=>{console.error(n)}})}errorHandler(e){e.target.src="https://alcodesbase.blob.core.windows.net/generic/sections-default-image.png"}cartListImage(e){return Le(e,this.baseUrl)}navigateToProduct(e){this.navigateUrl=`product/${e}/1?tenantId=3&lang=en`;const n=this.location.prepareExternalUrl(this.navigateUrl);window.open(n,"_blank")}sendMessage(){""!==this.messageInput.trim()&&(this.socket.emit("chat message",{message:this.messageInput,room:this.streamId,userName:this.userName}),this.messageInput="")}static \u0275fac=function(n){return new(n||i)(r.Y36(De.gz),r.Y36(_t._),r.Y36(De.F0),r.Y36(H.Ye))};static \u0275cmp=r.Xpm({type:i,selectors:[["app-merchant-livestream-details"]],viewQuery:function(n,s){if(1&n&&r.Gf(bt,5),2&n){let a;r.iGM(a=r.CRH())&&(s.receiverVideo=a.first)}},hostBindings:function(n,s){1&n&&r.NdJ("beforeunload",function(h){return s.unloadNotification(h)},!1,r.Jf7)},decls:34,vars:17,consts:[[1,"cart","mt-8","cart-top"],[1,"content-container","my-3",2,"margin-top","180px !important"],[1,"content-container","my-3"],[1,"row",2,"justify-content","center"],[1,"streamView",2,"width","70%"],["width","100%","height","500","playsinline","","controls","","autoplay","",2,"object-fit","cover"],["remoteVideoone","","receiverVideo",""],[1,"streamDetails"],[1,"description-holder"],[1,"selected-product-card"],[3,"scroll-x-wheel-enabled"],["nav",""],["drag-scroll-item","",4,"ngFor","ngForOf"],[1,"streamView",2,"width","30%"],[1,"view-holder"],[1,"instruction-View-second"],[1,"p-2","headerMain",2,"height","62px !important"],[1,"checklist-title"],["class","checklist",4,"ngIf"],[1,"message-box"],["class","message-show",4,"ngFor","ngForOf"],[1,"message-send-section"],["placeholder","Type your message...",1,"message-textarea",3,"ngModel","disabled","ngClass","ngModelChange","keyup.enter"],["mat-raised-button","",1,"send-button","add-product-btn","send-button-setting",3,"disabled","ngClass","click"],["drag-scroll-item",""],[1,"card","shadow-sm",3,"click"],[4,"ngIf"],[1,"card-body"],[1,"card-title"],[1,"description-holder",2,"margin-bottom","0"],["alt","...",1,"card-img-top",3,"src","error"],[1,"checklist"],[1,"message-show"]],template:function(n,s){1&n&&(r.TgZ(0,"section",0),r.ynx(1),r._UZ(2,"div",1),r.BQk(),r.qZA(),r.TgZ(3,"div",2),r.ynx(4),r.TgZ(5,"div",3)(6,"div",4),r._UZ(7,"video",5,6),r.TgZ(10,"div",7)(11,"h1"),r._uU(12),r.qZA(),r.TgZ(13,"p",8),r._uU(14),r.qZA()(),r.TgZ(15,"div",9)(16,"drag-scroll",10,11),r.YNc(18,wt,10,4,"div",12),r.qZA()()(),r.TgZ(19,"div",13)(20,"div",14)(21,"div",15)(22,"div",16)(23,"div",17)(24,"strong"),r.YNc(25,xt,2,1,"p",18),r.qZA()()()(),r.TgZ(26,"div",19),r.YNc(27,Et,2,1,"div",20),r.ALo(28,"async"),r.qZA(),r.TgZ(29,"section",21)(30,"textarea",22),r.NdJ("ngModelChange",function(h){return s.messageInput=h})("keyup.enter",function(){return s.sendMessage()}),r.qZA(),r.TgZ(31,"button",23),r.NdJ("click",function(){return s.sendMessage()}),r.TgZ(32,"mat-icon"),r._uU(33,"send"),r.qZA()()()()()(),r.BQk(),r.qZA()),2&n&&(r.xp6(12),r.Oqu(null==s.StreamDetail?null:s.StreamDetail.title),r.xp6(2),r.Oqu(null==s.StreamDetail?null:s.StreamDetail.description),r.xp6(2),r.Q6J("scroll-x-wheel-enabled",!0),r.xp6(2),r.Q6J("ngForOf",s.StreamDetail.products),r.xp6(7),r.Q6J("ngIf",s.streamerName),r.xp6(2),r.Q6J("ngForOf",r.lcZ(28,11,s.messages$)),r.xp6(3),r.Q6J("ngModel",s.messageInput)("disabled",!s.isAvalable)("ngClass",r.VKq(13,Me,!s.isAvalable)),r.xp6(1),r.Q6J("disabled",!s.isAvalable)("ngClass",r.VKq(15,Me,!s.isAvalable)))},dependencies:[H.mk,H.sg,H.O5,fe.Fj,fe.JJ,fe.On,Ae.HU,Ae.$Z,yt.Hw,H.Ov],styles:['[_nghost-%COMP%]{width:100%}.wrapper[_ngcontent-%COMP%]{padding:44px 44px 44px 50px!important}.wrapper[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:35px;font-weight:700;font-family:var(--bold-font);margin-bottom:40px;padding-top:3px}.wrapper[_ngcontent-%COMP%]   .select-category[_ngcontent-%COMP%]{width:268px;font-family:main-medium,sans-serif;color:#323232;font-size:12px}.wrapper[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{border-bottom:1px solid #b9b9b9!important;border-radius:0!important;height:50px!important;background:#f5f5f5 0% 0% no-repeat padding-box;padding-left:11px;padding-top:8px;width:268px;margin-left:10px}.wrapper[_ngcontent-%COMP%]     .p-button.p-button-icon-only{width:3rem;padding:.75rem 0;bottom:24px;border:none;background:#f5f5f5 0% 0% no-repeat padding-box;color:#000}.wrapper[_ngcontent-%COMP%]     .p-button:enabled:hover{background:none;color:#000;border-color:#f5f5f5}.wrapper[_ngcontent-%COMP%]     input:focus-visible{outline:none;border-radius:0}.wrapper[_ngcontent-%COMP%]     .p-button:enabled:active{background:none;color:#000;border-color:#f5f5f5}.wrapper[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{color:#323232;font-size:12px;font-family:main-medium,sans-serif;font-style:normal;font-weight:400;line-height:normal}.wrapper[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%]{width:100%}.wrapper[_ngcontent-%COMP%]     .p-calendar{width:100%!important}.wrapper[_ngcontent-%COMP%]   .custom-calendar[_ngcontent-%COMP%]     .p-calendar .p-inputtext{width:100%;background:transparent!important;border:0 none!important;padding:0!important;font-family:main-medium,sans-serif;font-style:normal!important;font-weight:400!important;font-size:12px!important;line-height:16px!important;color:#323232;bottom:13px;position:inherit}.wrapper[_ngcontent-%COMP%]     .p-inputtext:enabled:focus{box-shadow:none}.wrapper[_ngcontent-%COMP%]     .p-button:focus{box-shadow:none!important}.wrapper[_ngcontent-%COMP%]     .catalog-filters .ng-select .ng-select-container{border-bottom:1px solid #b9b9b9!important}.streamView[_ngcontent-%COMP%]{background-color:#f8f8f8;border-color:#e7e7e7;border-radius:2px;box-shadow:0 1px 5px 2px #c2c2c259}.instructionView[_ngcontent-%COMP%]{background-color:#fff!important;background-color:#f8f8f8;border-color:#e7e7e7;border-radius:2px;box-shadow:0 1px 5px 2px #c2c2c259;margin:8px}.headerMain[_ngcontent-%COMP%]{background-color:#f8f8f8;border-color:#e7e7e7;border-radius:2px;box-shadow:0 1px 5px 2px #c2c2c259;display:flex;align-items:center;height:80px}.offline_pin[_ngcontent-%COMP%]{transform:scale(1.42);margin-right:4px}.headerStatus[_ngcontent-%COMP%]{font-size:30px;margin-top:16px}.statusHolder[_ngcontent-%COMP%]{display:inline-flex;align-items:center;width:180px;border-right:2px solid}.Description[_ngcontent-%COMP%]{text-align:center}.checklist[_ngcontent-%COMP%]{text-transform:uppercase;align-items:center;display:contents}.checklist-title[_ngcontent-%COMP%]{width:100%;display:contents}.listdiv[_ngcontent-%COMP%]   mat-list[_ngcontent-%COMP%]   mat-list-item[_ngcontent-%COMP%]{height:58px;font-size:16px;font-weight:500}.checked[_ngcontent-%COMP%]{content:"\\f058";transform:rotate(360deg);transition:transform .5s ease-in-out;-o-transition:transform .5s ease-in-out;color:#67cf9f;opacity:1}.unchecked[_ngcontent-%COMP%]{color:#bfc9c4}.hidden[_ngcontent-%COMP%]{opacity:0;transform:rotate(-360deg)}.checklist-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:5px}  .listdiv mat-list mat-list-item .mdc-list-item__primary-text{display:flex;align-items:center;justify-content:space-between}.selected-product-card[_ngcontent-%COMP%]{height:283px;padding-right:15px}.add-product-btn[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;min-width:150px;max-width:250px;height:40px;background:#004f71 0% 0% no-repeat padding-box;border-radius:28px;opacity:1;text-align:center;letter-spacing:0px;color:#fff;font-family:main-medium,sans-serif}.stepper-holder[_ngcontent-%COMP%]{min-height:581px;background:white;height:100%}  mat-stepper div div mat-step-header{background-color:transparent!important}.card[_ngcontent-%COMP%]{position:relative;height:14rem;width:12rem;margin-right:.5rem;margin-left:.5rem;margin-top:.5rem;border-radius:.75rem}.card[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{border-top-left-radius:.75rem;border-top-right-radius:.75rem}.card-title[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-family:Noto Sans SC,sans-serif}.description-holder[_ngcontent-%COMP%]{overflow:hidden;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.card-price[_ngcontent-%COMP%]{font-size:medium;margin-bottom:.5rem}.card-img-top[_ngcontent-%COMP%]{height:120px}drag-scroll[_ngcontent-%COMP%]{height:15rem;width:100%}.button-options[_ngcontent-%COMP%]{text-align:end;padding-right:17px}.example-margin[_ngcontent-%COMP%]{position:absolute}.apply-holder[_ngcontent-%COMP%]{padding:0 0 6px 9px}.streamDetails[_ngcontent-%COMP%]{padding:0 20px}.message-box[_ngcontent-%COMP%]{box-sizing:border-box;background-color:#dfdfdfb3;height:501px;overflow-y:scroll;display:flex;flex-direction:column;justify-content:flex-start;min-height:500px}textarea[_ngcontent-%COMP%]{width:100%;border:none;resize:none;outline:none}textarea[_ngcontent-%COMP%]::placeholder{font-size:16px;opacity:.7}.view-holder[_ngcontent-%COMP%]{box-shadow:0 1px 5px 2px #c2c2c259;margin:8px 8px 0}.send-button[_ngcontent-%COMP%]{position:absolute;top:14px;right:16px}.message-send-section[_ngcontent-%COMP%]{position:relative}.message-textarea[_ngcontent-%COMP%]{padding:12px}.message-show[_ngcontent-%COMP%]{padding:6px 10px 0}.send-button-setting[_ngcontent-%COMP%]{min-width:63px!important;height:30px!important}[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px}[_ngcontent-%COMP%]::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 4px rgba(117,116,116,.3);border-radius:8px;margin-top:10px;margin-bottom:10px}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:10px;background-color:#f8bcbc}@media (min-width: 768px){.adjustable[_ngcontent-%COMP%]{margin-top:-145px}}@media only screen and (max-width: 500px){.content-container[_ngcontent-%COMP%]{margin:0 0 273px!important}}']})}return i})();function Le(i,t){return i?(t.endsWith("/")||(t+="/"),i.startsWith("/")&&(i=i.substring(1)),"images"===i.substring(0,6).toLowerCase()?t+i:t+"Images/"+i):""}},381:(ue,N,y)=>{y.d(N,{$Z:()=>x,HU:()=>V,l1:()=>R});var d=y(5879),L=y(6814);const w=["contentRef"],D=["*"];let x=(()=>{class v{get dragDisabled(){return this._dragDisabled}set dragDisabled(o){this._dragDisabled=o}constructor(o){this.display="inline-block",this._dragDisabled=!1,this._elementRef=o}static#e=this.\u0275fac=function(c){return new(c||v)(d.Y36(d.SBq))};static#t=this.\u0275dir=d.lG2({type:v,selectors:[["","drag-scroll-item",""]],hostVars:2,hostBindings:function(c,l){2&c&&d.Udp("display",l.display)},inputs:{dragDisabled:["drag-disabled","dragDisabled"]}})}return v})(),V=(()=>{class v{get isDragging(){return this._isDragging}get currIndex(){return this._index}set currIndex(o){o!==this._index&&(this._index=o,this.indexChanged.emit(o))}get scrollbarHidden(){return this._scrollbarHidden}set scrollbarHidden(o){this._scrollbarHidden=o}get disabled(){return this._disabled}set disabled(o){this._disabled=o}get xDisabled(){return this._xDisabled}set xDisabled(o){this._xDisabled=o}get yDisabled(){return this._yDisabled}set yDisabled(o){this._yDisabled=o}get xWheelEnabled(){return this._xWheelEnabled}set xWheelEnabled(o){this._xWheelEnabled=o}get dragDisabled(){return this._dragDisabled}set dragDisabled(o){this._dragDisabled=o}get snapDisabled(){return this._snapDisabled}set snapDisabled(o){this._snapDisabled=o}get snapOffset(){return this._snapOffset}set snapOffset(o){this._snapOffset=o}get snapDuration(){return this._snapDuration}set snapDuration(o){this._snapDuration=o}constructor(o,c,l){this._elementRef=o,this._renderer=c,this._document=l,this._index=0,this._scrollbarHidden=!1,this._disabled=!1,this._xDisabled=!1,this._xWheelEnabled=!1,this._yDisabled=!1,this._dragDisabled=!1,this._snapDisabled=!1,this._snapOffset=0,this._snapDuration=500,this._isDragging=!1,this.isPressed=!1,this.isScrolling=!1,this.scrollTimer=-1,this.scrollToTimer=-1,this.downX=0,this.downY=0,this.displayType="block",this.elWidth=null,this.elHeight=null,this._pointerEvents="auto",this.scrollbarWidth=null,this.isAnimating=!1,this.prevChildrenLength=0,this.indexBound=0,this.rtl=!1,this.dsInitialized=new d.vpe,this.indexChanged=new d.vpe,this.reachesLeftBound=new d.vpe,this.reachesRightBound=new d.vpe,this.snapAnimationFinished=new d.vpe,this.dragStart=new d.vpe,this.dragEnd=new d.vpe,this.scrollbarWidth=`${this.getScrollbarWidth()}px`}ngOnChanges(){this.setScrollBar(),this.xDisabled||this.disabled||this._scrollbarHidden?this.disableScroll("x"):this.enableScroll("x"),this.yDisabled||this.disabled?this.disableScroll("y"):this.enableScroll("y")}ngAfterViewInit(){this._renderer.setAttribute(this._contentRef.nativeElement,"drag-scroll","true"),this.displayType=typeof window<"u"?window.getComputedStyle(this._elementRef.nativeElement).display:"block",this._renderer.setStyle(this._contentRef.nativeElement,"display",this.displayType),this._renderer.setStyle(this._contentRef.nativeElement,"whiteSpace","noWrap"),this.markElDimension(),this._renderer.setStyle(this._contentRef.nativeElement,"width",this.elWidth),this._renderer.setStyle(this._contentRef.nativeElement,"height",this.elHeight),this.wrapper&&this.checkScrollbar(),this._onMouseDownListener=this._renderer.listen(this._contentRef.nativeElement,"mousedown",this.onMouseDownHandler.bind(this)),this._onScrollListener=this._renderer.listen(this._contentRef.nativeElement,"scroll",this.onScrollHandler.bind(this)),this._onDragStartListener=this._renderer.listen(this._contentRef.nativeElement,"dragstart",o=>{o.preventDefault()}),this.checkNavStatus(),this.dsInitialized.emit(),this.adjustMarginToLastChild(),this.rtl="rtl"===getComputedStyle(this._contentRef.nativeElement).getPropertyValue("direction")}ngAfterViewChecked(){this._children.length!==this.prevChildrenLength&&(this.markElDimension(),this.checkScrollbar(),this.prevChildrenLength=this._children.length,this.checkNavStatus())}ngOnDestroy(){this._renderer.setAttribute(this._contentRef.nativeElement,"drag-scroll","false"),this._onMouseDownListener&&(this._onMouseDownListener=this._onMouseDownListener()),this._onScrollListener&&(this._onScrollListener=this._onScrollListener()),this._onDragStartListener&&(this._onDragStartListener=this._onDragStartListener())}onMouseMoveHandler(o){this.onMouseMove(o)}onMouseMove(o){if((o.clientX!==this.downX||o.clientY!==this.downY)&&this.isPressed&&!this.disabled){if(!o.buttons&&!o.which)return this.onMouseUpHandler(o);if(this._pointerEvents="none",this._setIsDragging(!0),!this.xDisabled&&!this.dragDisabled){const c=o.clientX;this._contentRef.nativeElement.scrollLeft=this._contentRef.nativeElement.scrollLeft-c+this.downX,this.downX=c}if(!this.yDisabled&&!this.dragDisabled){const c=o.clientY;this._contentRef.nativeElement.scrollTop=this._contentRef.nativeElement.scrollTop-c+this.downY,this.downY=c}}}onMouseDownHandler(o){const c=this.locateDragScrollItem(o.target);if(c&&c.dragDisabled)return;this._startGlobalListening("touchstart"===o.type),this.isPressed=!0;const p=o;this.downX=p.clientX,this.downY=p.clientY,clearTimeout(this.scrollToTimer)}onScrollHandler(){this.checkNavStatus(),this.isPressed||this.isAnimating||this.snapDisabled?this.locateCurrentIndex():(this.isScrolling=!0,clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout(()=>{this.isScrolling=!1,this.locateCurrentIndex(!0)},500))}onMouseUpHandler(o){this.isPressed&&(this.isPressed=!1,this._pointerEvents="auto",this._setIsDragging(!1),this.snapDisabled?this.locateCurrentIndex():this.locateCurrentIndex(!0),this._stopGlobalListening())}moveLeft(){(0!==this.currIndex||this.snapDisabled)&&(this.currIndex--,clearTimeout(this.scrollToTimer),this.scrollTo(this._contentRef.nativeElement,this.toChildrenLocation(),this.snapDuration))}moveRight(){const o=this.wrapper||this.parentNode,c=o?o.clientWidth:0;!this.isScrollReachesRightEnd()&&this.currIndex<this.maximumIndex(c,this._children.toArray())&&(this.currIndex++,clearTimeout(this.scrollToTimer),this.scrollTo(this._contentRef.nativeElement,this.toChildrenLocation(),this.snapDuration))}moveTo(o){const c=this.wrapper||this.parentNode,l=c?c.clientWidth:0;o>=0&&o!==this.currIndex&&this.currIndex<=this.maximumIndex(l,this._children.toArray())&&(this.currIndex=Math.min(o,this.maximumIndex(l,this._children.toArray())),clearTimeout(this.scrollToTimer),this.scrollTo(this._contentRef.nativeElement,this.toChildrenLocation(),this.snapDuration))}checkNavStatus(){setTimeout(()=>{this._children.length<=1||this._contentRef.nativeElement.scrollWidth<=this._contentRef.nativeElement.clientWidth?(this.reachesLeftBound.emit(!0),this.reachesRightBound.emit(!0)):this.isScrollReachesRightEnd()?(this.reachesLeftBound.emit(!1),this.reachesRightBound.emit(!0)):0===this._contentRef.nativeElement.scrollLeft&&this._contentRef.nativeElement.scrollWidth>this._contentRef.nativeElement.clientWidth?(this.reachesLeftBound.emit(!0),this.reachesRightBound.emit(!1)):(this.reachesLeftBound.emit(!1),this.reachesRightBound.emit(!1))},0)}onWheel(o){this._xWheelEnabled&&(o.preventDefault(),this._snapDisabled?this._contentRef.nativeElement.scrollBy(o.deltaY,0):o.deltaY<0?this.moveLeft():o.deltaY>0&&this.moveRight())}onWindowResize(){this.refreshWrapperDimensions(),this.checkNavStatus()}_setIsDragging(o){this._isDragging!==o&&(this._isDragging=o,o?this.dragStart.emit():this.dragEnd.emit())}_startGlobalListening(o){this._onMouseMoveListener||(this._onMouseMoveListener=this._renderer.listen("document",o?"touchmove":"mousemove",this.onMouseMoveHandler.bind(this))),this._onMouseUpListener||(this._onMouseUpListener=this._renderer.listen("document",o?"touchend":"mouseup",this.onMouseUpHandler.bind(this)))}_stopGlobalListening(){this._onMouseMoveListener&&(this._onMouseMoveListener=this._onMouseMoveListener()),this._onMouseUpListener&&(this._onMouseUpListener=this._onMouseUpListener())}disableScroll(o){this._renderer.setStyle(this._contentRef.nativeElement,`overflow-${o}`,"hidden")}enableScroll(o){this._renderer.setStyle(this._contentRef.nativeElement,`overflow-${o}`,"auto")}hideScrollbar(){"none"!==this._contentRef.nativeElement.style.display&&!this.wrapper&&(this.parentNode=this._contentRef.nativeElement.parentNode,this.wrapper=this._renderer.createElement("div"),this._renderer.setAttribute(this.wrapper,"class","drag-scroll-wrapper"),this._renderer.addClass(this.wrapper,"drag-scroll-container"),this.refreshWrapperDimensions(),this._renderer.setStyle(this.wrapper,"overflow","hidden"),this._renderer.setStyle(this._contentRef.nativeElement,"width",`calc(100% + ${this.scrollbarWidth})`),this._renderer.setStyle(this._contentRef.nativeElement,"height",`calc(100% + ${this.scrollbarWidth})`),this._renderer.appendChild(this._elementRef.nativeElement,this.wrapper),this._renderer.appendChild(this.wrapper,this._contentRef.nativeElement),this.adjustMarginToLastChild())}showScrollbar(){this.wrapper&&(this._renderer.setStyle(this._contentRef.nativeElement,"width","100%"),this._renderer.setStyle(this._contentRef.nativeElement,"height",this.wrapper.style.height),null!==this.parentNode&&(this.parentNode.removeChild(this.wrapper),this.parentNode.appendChild(this._contentRef.nativeElement)),this.wrapper=null,this.adjustMarginToLastChild())}checkScrollbar(){this._renderer.setStyle(this._contentRef.nativeElement,"height",this._contentRef.nativeElement.scrollWidth<=this._contentRef.nativeElement.clientWidth?"100%":`calc(100% + ${this.scrollbarWidth})`),this._renderer.setStyle(this._contentRef.nativeElement,"width",this._contentRef.nativeElement.scrollHeight<=this._contentRef.nativeElement.clientHeight?"100%":`calc(100% + ${this.scrollbarWidth})`)}setScrollBar(){this.scrollbarHidden?this.hideScrollbar():this.showScrollbar()}getScrollbarWidth(){const o=this._renderer.createElement("div");this._renderer.setStyle(o,"visibility","hidden"),this._renderer.setStyle(o,"width","100px"),this._renderer.setStyle(o,"msOverflowStyle","scrollbar"),this._renderer.appendChild(this._document.body,o);const c=o.offsetWidth;this._renderer.setStyle(o,"overflow","scroll");const l=this._renderer.createElement("div");this._renderer.setStyle(l,"width","100%"),this._renderer.appendChild(o,l);const p=l.offsetWidth;return this._renderer.removeChild(this._document.body,o),c-p||20}refreshWrapperDimensions(){this.wrapper&&(this._renderer.setStyle(this.wrapper,"width","100%"),this._renderer.setStyle(this.wrapper,"height",this._elementRef.nativeElement.style.height>0||this._elementRef.nativeElement.offsetHeight>0?this._elementRef.nativeElement.style.height||this._elementRef.nativeElement.offsetHeight+"px":"100%"))}scrollTo(o,c,l){const p=this;p.isAnimating=!0;const _=o.scrollLeft,O=(this.rtl?-1:1)*c-_-this.snapOffset;let I=0;const Y=function(){var S,W,Z;I+=20,o.scrollLeft=(S=I,W=_,Z=O,(S/=l/2)<1?Z/2*S*S+W:-Z/2*(--S*(S-2)-1)+W),I<l?p.scrollToTimer=setTimeout(Y,20):setTimeout(()=>{p.isAnimating=!1,p.snapAnimationFinished.emit(p.currIndex)},20)};Y()}locateCurrentIndex(o){const c=Math.abs(this._contentRef.nativeElement.scrollLeft);this.currentChildWidth((l,p,g,_,O)=>{c>=g&&c<=p?(p-c>l/2&&!this.isScrollReachesRightEnd()?(this.isAnimating||(this.currIndex=_),o&&this.scrollTo(this._contentRef.nativeElement,g,this.snapDuration)):0!==c&&(this.isAnimating||(this.currIndex=_+1),o&&this.scrollTo(this._contentRef.nativeElement,g+l,this.snapDuration)),O()):_+1===this._children.length-1&&(this.isAnimating||(this.currIndex=_+1),O())})}currentChildWidth(o){let c=0,l=!1;const p=function(){l=!0},g=this._children.toArray();for(let _=0;_<g.length&&_!==g.length-1&&!l;_++){const C=g[_]._elementRef.nativeElement.clientWidth;o(C,c+g[_+1]._elementRef.nativeElement.clientWidth,c,_,p),c+=C}}toChildrenLocation(){let o=0;const c=this._children.toArray();for(let l=0;l<this.currIndex;l++)o+=c[l]._elementRef.nativeElement.clientWidth;return o}locateDragScrollItem(o){let c=null;const l=this._children.toArray();for(let p=0;p<l.length;p++)o===l[p]._elementRef.nativeElement&&(c=l[p]);return c}markElDimension(){this.wrapper?(this.elWidth=this.wrapper.style.width,this.elHeight=this.wrapper.style.height):(this.elWidth=this._elementRef.nativeElement.style.width||this._elementRef.nativeElement.offsetWidth+"px",this.elHeight=this._elementRef.nativeElement.style.height||this._elementRef.nativeElement.offsetHeight+"px");const o=this.wrapper||this.parentNode;this._children.length>1&&(this.indexBound=this.maximumIndex(o?o.clientWidth:0,this._children.toArray()))}maximumIndex(o,c){let l=0,p=0;for(let g=0;g<=c.length;g++){const _=c[c.length-1-g];if(!_)break;{const O=_._elementRef.nativeElement;let C=O.clientWidth;if(0===C&&O.firstElementChild&&(C=_._elementRef.nativeElement.firstElementChild.clientWidth),p+=C,!(p<o))break;l++}}return c.length-l}isScrollReachesRightEnd(){return Math.abs(this._contentRef.nativeElement.scrollLeft)+this._contentRef.nativeElement.offsetWidth>=this._contentRef.nativeElement.scrollWidth}adjustMarginToLastChild(){if(this._children&&this._children.length>0&&this.hideScrollbar){const o=this._children.toArray();this._renderer.setStyle(o[o.length-1]._elementRef.nativeElement,"margin-right",this.wrapper&&o.length>1?this.scrollbarWidth:0)}}static#e=this.\u0275fac=function(c){return new(c||v)(d.Y36(d.SBq),d.Y36(d.Qsj),d.Y36(L.K0))};static#t=this.\u0275cmp=d.Xpm({type:v,selectors:[["drag-scroll"]],contentQueries:function(c,l,p){if(1&c&&d.Suo(p,x,5),2&c){let g;d.iGM(g=d.CRH())&&(l._children=g)}},viewQuery:function(c,l){if(1&c&&d.Gf(w,7),2&c){let p;d.iGM(p=d.CRH())&&(l._contentRef=p.first)}},hostVars:2,hostBindings:function(c,l){1&c&&d.NdJ("wheel",function(g){return l.onWheel(g)})("resize",function(){return l.onWindowResize()},!1,d.Jf7),2&c&&d.Udp("pointer-events",l._pointerEvents)},inputs:{scrollbarHidden:["scrollbar-hidden","scrollbarHidden"],disabled:["drag-scroll-disabled","disabled"],xDisabled:["drag-scroll-x-disabled","xDisabled"],yDisabled:["drag-scroll-y-disabled","yDisabled"],xWheelEnabled:["scroll-x-wheel-enabled","xWheelEnabled"],dragDisabled:["drag-disabled","dragDisabled"],snapDisabled:["snap-disabled","snapDisabled"],snapOffset:["snap-offset","snapOffset"],snapDuration:["snap-duration","snapDuration"]},outputs:{dsInitialized:"dsInitialized",indexChanged:"indexChanged",reachesLeftBound:"reachesLeftBound",reachesRightBound:"reachesRightBound",snapAnimationFinished:"snapAnimationFinished",dragStart:"dragStart",dragEnd:"dragEnd"},features:[d.TTD],ngContentSelectors:D,decls:3,vars:0,consts:[[1,"drag-scroll-content"],["contentRef",""]],template:function(c,l){1&c&&(d.F$t(),d.TgZ(0,"div",0,1),d.Hsn(2),d.qZA())},styles:["[_nghost-%COMP%]{overflow:hidden;display:block}.drag-scroll-content[_ngcontent-%COMP%]{height:100%;overflow:auto;white-space:nowrap}"]})}return v})(),R=(()=>{class v{static#e=this.\u0275fac=function(c){return new(c||v)};static#t=this.\u0275mod=d.oAB({type:v});static#n=this.\u0275inj=d.cJS({})}return v})()}}]);