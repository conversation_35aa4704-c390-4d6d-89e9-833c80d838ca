{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { SharedModule } from \"../../shared/modules/shared.module\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { CartProductDetailsComponent } from './components/cart-product-details/cart-product-details.component';\nimport { CheckoutCardComponent } from './components/checkout-card/checkout-card.component';\nimport { EmptyCartComponent } from './components/empty-cart/empty-cart.component';\nlet CartModule = class CartModule {};\nCartModule = __decorate([NgModule({\n  declarations: [IndexComponent, CartProductDetailsComponent, CheckoutCardComponent, EmptyCartComponent],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes), RippleModule]\n})], CartModule);\nexport { CartModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IndexComponent", "RouterModule", "routes", "SharedModule", "RippleModule", "CartProductDetailsComponent", "CheckoutCardComponent", "EmptyCartComponent", "CartModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\cart\\cart.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {SharedModule} from \"../../shared/modules/shared.module\";\r\nimport {RippleModule} from \"primeng/ripple\";\r\nimport { CartProductDetailsComponent } from './components/cart-product-details/cart-product-details.component';\r\nimport { CheckoutCardComponent } from './components/checkout-card/checkout-card.component';\r\nimport { EmptyCartComponent } from './components/empty-cart/empty-cart.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    CartProductDetailsComponent,\r\n    CheckoutCardComponent,\r\n    EmptyCartComponent\r\n  ],\r\n    imports: [\r\n        CommonModule,\r\n        SharedModule,\r\n        RouterModule.forChild(routes),\r\n        RippleModule\r\n    ]\r\n})\r\nexport class CartModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,YAAY,QAAO,oCAAoC;AAC/D,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,kBAAkB,QAAQ,8CAA8C;AAiB1E,IAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAI;AAAdA,UAAU,GAAAC,UAAA,EAdtBX,QAAQ,CAAC;EACRY,YAAY,EAAE,CACZV,cAAc,EACdK,2BAA2B,EAC3BC,qBAAqB,EACrBC,kBAAkB,CACnB;EACCI,OAAO,EAAE,CACLZ,YAAY,EACZI,YAAY,EACZF,YAAY,CAACW,QAAQ,CAACV,MAAM,CAAC,EAC7BE,YAAY;CAEnB,CAAC,C,EACWI,UAAU,CAAI;SAAdA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}