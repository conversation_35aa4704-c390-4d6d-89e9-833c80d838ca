{"ast": null, "code": "import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@core/services\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"ngx-intl-tel-input-gg\";\nimport * as i11 from \"primeng/progressspinner\";\nimport * as i12 from \"../../../../../shared/components/back-button/back-button.component\";\nfunction RegisterOtpComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterOtpComponent_ng_container_3_ng_container_2_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_3_ng_container_2_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.resendOtp());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.timeLeft !== \"00\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resend\"), \" \");\n  }\n}\nfunction RegisterOtpComponent_ng_container_3_ng_container_2_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resendIn\"), \" 0:\", ctx_r5.timeLeft, \"\");\n  }\n}\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction RegisterOtpComponent_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 9)(5, \"div\", 10)(6, \"div\", 11)(7, \"p\", 12);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 13);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"form\", 14)(14, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.value1 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.value2 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_15_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.value3 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_16_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.value4 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_17_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 16);\n    i0.ɵɵtemplate(19, RegisterOtpComponent_ng_container_3_ng_container_2_button_19_Template, 3, 4, \"button\", 17);\n    i0.ɵɵtemplate(20, RegisterOtpComponent_ng_container_3_ng_container_2_span_20_Template, 3, 4, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_3_ng_container_2_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.validateOtp());\n    });\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 14, \"auth.otp.title\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 16, \"auth.otp.subTitle\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.value1)(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.value2)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.value3)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.value4)(\"ngModelOptions\", i0.ɵɵpureFunction0(23, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.timeLeft == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.timeLeft > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(22, 18, \"auth.otp.next\"));\n    i0.ɵɵproperty(\"disabled\", !(ctx_r2.value1 && ctx_r2.value2 && ctx_r2.value3 && ctx_r2.value4));\n  }\n}\nfunction RegisterOtpComponent_ng_container_3_ng_container_3_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_3_ng_container_3_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.resendOtp());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.timeLeft !== \"00\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resend\"), \" \");\n  }\n}\nfunction RegisterOtpComponent_ng_container_3_ng_container_3_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resendIn\"), \" 0:\", ctx_r19.timeLeft, \"\");\n  }\n}\nfunction RegisterOtpComponent_ng_container_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"img\", 24);\n    i0.ɵɵelementStart(4, \"div\", 25)(5, \"h2\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 28)(12, \"form\", 29)(13, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.value1 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_13_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.value2 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_14_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.value3 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_15_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.value4 = $event);\n    })(\"keyup\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_16_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onDigitInput($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 31);\n    i0.ɵɵtemplate(18, RegisterOtpComponent_ng_container_3_ng_container_3_button_18_Template, 3, 4, \"button\", 17);\n    i0.ɵɵtemplate(19, RegisterOtpComponent_ng_container_3_ng_container_3_span_19_Template, 3, 4, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function RegisterOtpComponent_ng_container_3_ng_container_3_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.validateOtp());\n    });\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 14, \"auth.otp.title\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 16, \"auth.otp.subTitle\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.value1)(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.value2)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.value3)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.value4)(\"ngModelOptions\", i0.ɵɵpureFunction0(23, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.timeLeft == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.timeLeft > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(21, 18, \"auth.otp.next\"));\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.value1 && ctx_r3.value2 && ctx_r3.value3 && ctx_r3.value4));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nfunction RegisterOtpComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, RegisterOtpComponent_ng_container_3_ng_container_2_Template, 23, 24, \"ng-container\", 3);\n    i0.ɵɵtemplate(3, RegisterOtpComponent_ng_container_3_ng_container_3_Template, 22, 24, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isMobileLayout && ctx_r1.screenWidth <= 767 ? \"mobile-content-container\" : \"content-container\")(\"ngStyle\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.isMobileLayout ? \"1rem\" : \"220px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMobileLayout ? ctx_r1.screenWidth > 767 : true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMobileLayout && ctx_r1.screenWidth <= 767);\n  }\n}\nexport class RegisterOtpComponent {\n  constructor(messageService, translate, router, otpService, store, permissionService, cd, loaderService, $gaService,\n  // private permissionService:PermissionService,\n  $gtmService) {\n    this.messageService = messageService;\n    this.translate = translate;\n    this.router = router;\n    this.otpService = otpService;\n    this.store = store;\n    this.permissionService = permissionService;\n    this.cd = cd;\n    this.loaderService = loaderService;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.loading = false;\n    this.mobileNumber = \"\";\n    this.size = 50;\n    this.otpCode = \"\";\n    this.timeLeft = 60;\n    this.counter = 60;\n    this.tick = 1000;\n    this.isMobileLayout = false;\n    this.screenWidth = window.innerWidth;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.$gtmService.pushPageView('signUp', 'OTP');\n    this.startTimer();\n    if (history.state.mobile == null) {\n      this.router.navigate(['/register']);\n    } else {\n      this.mobileNumber = history.state.mobile;\n    }\n  }\n  ngOnDestroy() {\n    this.countDown = null;\n  }\n  startTimer() {\n    this.interval = setInterval(() => {\n      if (this.timeLeft > 0) {\n        this.timeLeft--;\n      } else {\n        this.timeLeft = 60;\n      }\n      if (this.timeLeft === 0) {\n        clearInterval(this.interval);\n      }\n      if (this.timeLeft < 10) {\n        this.timeLeft = '0' + this.timeLeft;\n      }\n      this.cd.detectChanges();\n    }, 1000);\n  }\n  onDigitInput(event) {\n    let element;\n    if (event.code !== 'Backspace' && event.code !== 'Tab') element = event.srcElement.nextElementSibling;\n    if (event.code === 'Backspace') element = event.srcElement.previousElementSibling;\n    if (event.code === 'Tab') {\n      return;\n    }\n    if (element == null) return;else element.focus();\n  }\n  validateOtp() {\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_OTP_VERIFY, '', 'OTP_VERIFICATION', 1, true);\n    if ((this.value1 != null || this.value1 != undefined) && (this.value2 != null || this.value2 != undefined) && (this.value3 != null || this.value3 != undefined) && (this.value4 != null || this.value4 != undefined)) {\n      this.otpCode = this.value1.toString() + this.value2.toString() + this.value3.toString() + this.value4.toString();\n      this.otpService.checkOTP({\n        UserName: this.mobileNumber,\n        OTPCode: this.otpCode,\n        CountryId: \"1448983B-0C38-450A-BD71-9204D181B925\"\n      }).subscribe({\n        next: res => {\n          this.loading = false;\n          if (res.success === 12) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.invalidOtp')\n            });\n          } else if (res.success === 87) {\n            this.router.navigateByUrl('/register/register-reset-password', {\n              state: {\n                mobile: this.mobileNumber,\n                otp: this.otpCode\n              }\n            });\n          }\n        },\n        error: err => {\n          this.loading = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        }\n      });\n    } else {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\n      });\n    }\n  }\n  resendOtp() {\n    this.loaderService.show();\n    this.otpService.checkMobileNumber({\n      UserName: this.otpService.username,\n      CountryId: this.otpService.countryId\n    }).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        this.startTimer();\n      },\n      error: err => {\n        this.store.set(\"loading\", false);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function RegisterOtpComponent_Factory(t) {\n    return new (t || RegisterOtpComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.RegisterService), i0.ɵɵdirectiveInject(i4.StoreService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.LoaderService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RegisterOtpComponent,\n    selectors: [[\"app-register-otp\"]],\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"update-password-page\"], [\"class\", \"spinner\", 4, \"ngIf\"], [3, \"navigationUrl\"], [4, \"ngIf\"], [1, \"spinner\"], [3, \"ngClass\", \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-75\", \"shadow-signin\"], [1, \"image\", \"col-12\"], [\"src\", \"assets/images/registerLogo.svg\", \"alt\", \"\", \"srcset\", \"\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"pt-6\", \"d-flex\", \"justify-content-flex-end\", \"align-items-center\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\"], [1, \"otp-heading\"], [1, \"sent-otp\"], [\"action\", \"\", 1, \"form-container\"], [\"placeholder\", \"_\", \"type\", \"text\", \"maxlength\", \"1\", \"oninput\", \"this.value=this.value.replace(/[^0-9]/g,'');\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"keyup\"], [1, \"count\"], [\"style\", \"background: white; border: none; cursor: pointer\", \"class\", \"resend\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"time-left\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-5\", \"mt-3\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"label\", \"disabled\", \"click\"], [1, \"resend\", 2, \"background\", \"white\", \"border\", \"none\", \"cursor\", \"pointer\", 3, \"disabled\", \"click\"], [1, \"time-left\"], [1, \"justify-content-center\", \"p-0\", \"shadow-signin\", \"bg-white\", \"mobile-contianer\"], [1, \"d-flex\", \"flex-row\", \"pt-3\"], [\"src\", \"assets/images/register.svg\"], [1, \"head-desc\"], [1, \"signup-heading\"], [1, \"signup-desc\"], [1, \"d-flex\", \"flex-column\", \"mb-3\"], [\"action\", \"\", 1, \"mt-5\"], [\"type\", \"text\", \"maxlength\", \"1\", \"placeholder\", \"_\", \"oninput\", \"this.value=this.value.replace(/[^0-9]/g,'');\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"keyup\"], [1, \"count\", \"p-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"primary-btn\", \"second-btn\", \"m-3\", 3, \"label\", \"disabled\", \"click\"]],\n    template: function RegisterOtpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, RegisterOtpComponent_div_1_Template, 2, 0, \"div\", 1);\n        i0.ɵɵelement(2, \"app-back-button\", 2);\n        i0.ɵɵtemplate(3, RegisterOtpComponent_ng_container_3_Template, 4, 6, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"navigationUrl\", \"/register\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i7.NgStyle, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.MaxLengthValidator, i8.NgModel, i8.NgForm, i9.ButtonDirective, i10.NativeElementInjectorDirective, i11.ProgressSpinner, i12.BackButtonComponent, i2.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\nform[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 60px;\\n  height: 60px;\\n  text-align: center;\\n  margin: 0 0.8rem;\\n  background: #F5F5F5 !important;\\n  font-size: 30px;\\n  font-weight: 400;\\n  font-family: var(--medium-font) !important;\\n  border-radius: 8px;\\n}\\n\\n.otp[_ngcontent-%COMP%] {\\n  margin-top: 90px;\\n}\\n\\n[_nghost-%COMP%]     button.back-btn {\\n  position: relative !important;\\n}\\n\\n@media screen and (min-width: 768px) {\\n  [_nghost-%COMP%]     button.back-btn {\\n    top: -22px !important;\\n  }\\n}\\n.shadow-signin[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n  padding-top: 16px;\\n  margin: 30px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .shadow-signin[_ngcontent-%COMP%] {\\n    padding: 0 !important;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .shadow-signin[_ngcontent-%COMP%] {\\n    padding: 0 !important;\\n  }\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: end;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  width: 300px;\\n  border-radius: 281.739px;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  color: #212121;\\n  line-height: 100%;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .sent-otp[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: #443F3F;\\n  font-family: var(--regular-font) !important;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: small;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .resend[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 800;\\n  font-family: var(--medium-font) !important;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .time-left[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  font-family: var(--regular-font) !important;\\n  color: #204E6E;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 2px;\\n  padding: 16px 24px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  text-transform: uppercase;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .otp[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n  form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    margin: 0 0.4rem !important;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .margin-x-100[_ngcontent-%COMP%] {\\n    margin-top: 160px !important;\\n  }\\n}\\n.mobile-contianer[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  margin-top: 140px;\\n  padding-left: 14px !important;\\n  padding-right: 24px !important;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%] {\\n  margin: 0 !important;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .time-left[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor);\\n  font-size: 12px;\\n  font-weight: 400;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .resend[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor);\\n  font-size: 12px;\\n  font-weight: 400;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .sent-otp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 400;\\n  color: #443F3F;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  margin-bottom: 0;\\n}\\n.mobile-contianer[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border-bottom: none !important;\\n  height: 50px;\\n  width: 50px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["GaLocalActionEnum", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "RegisterOtpComponent_ng_container_3_ng_container_2_button_19_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "resendOtp", "ɵɵtext", "ɵɵproperty", "ctx_r4", "timeLeft", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "ctx_r5", "ɵɵelementContainerStart", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_14_listener", "$event", "_r9", "ctx_r8", "value1", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_14_listener", "ctx_r10", "onDigitInput", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_15_listener", "ctx_r11", "value2", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_15_listener", "ctx_r12", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_16_listener", "ctx_r13", "value3", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_16_listener", "ctx_r14", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_ngModelChange_17_listener", "ctx_r15", "value4", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_input_keyup_17_listener", "ctx_r16", "ɵɵtemplate", "RegisterOtpComponent_ng_container_3_ng_container_2_button_19_Template", "RegisterOtpComponent_ng_container_3_ng_container_2_span_20_Template", "RegisterOtpComponent_ng_container_3_ng_container_2_Template_button_click_21_listener", "ctx_r17", "validateOtp", "ɵɵelementContainerEnd", "ctx_r2", "ɵɵpureFunction0", "_c0", "ɵɵpropertyInterpolate", "RegisterOtpComponent_ng_container_3_ng_container_3_button_18_Template_button_click_0_listener", "_r21", "ctx_r20", "ctx_r18", "ctx_r19", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_13_listener", "_r23", "ctx_r22", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_13_listener", "ctx_r24", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_14_listener", "ctx_r25", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_14_listener", "ctx_r26", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_15_listener", "ctx_r27", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_15_listener", "ctx_r28", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_ngModelChange_16_listener", "ctx_r29", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_input_keyup_16_listener", "ctx_r30", "RegisterOtpComponent_ng_container_3_ng_container_3_button_18_Template", "RegisterOtpComponent_ng_container_3_ng_container_3_span_19_Template", "RegisterOtpComponent_ng_container_3_ng_container_3_Template_button_click_20_listener", "ctx_r31", "ɵɵtextInterpolate", "ctx_r3", "RegisterOtpComponent_ng_container_3_ng_container_2_Template", "RegisterOtpComponent_ng_container_3_ng_container_3_Template", "ctx_r1", "isMobileLayout", "screenWidth", "ɵɵpureFunction1", "_c1", "RegisterOtpComponent", "constructor", "messageService", "translate", "router", "otpService", "store", "permissionService", "cd", "loaderService", "$gaService", "$gtmService", "loading", "mobileNumber", "size", "otpCode", "counter", "tick", "window", "innerWidth", "ngOnInit", "hasPermission", "pushPageView", "startTimer", "history", "state", "mobile", "navigate", "ngOnDestroy", "countDown", "interval", "setInterval", "clearInterval", "detectChanges", "event", "element", "code", "srcElement", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "focus", "CLICK_ON_OTP_VERIFY", "undefined", "toString", "checkOTP", "UserName", "OTPCode", "CountryId", "subscribe", "next", "res", "success", "add", "severity", "summary", "instant", "navigateByUrl", "otp", "error", "err", "detail", "message", "show", "checkMobileNumber", "username", "countryId", "hide", "set", "_", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "TranslateService", "i3", "Router", "i4", "RegisterService", "StoreService", "PermissionService", "ChangeDetectorRef", "LoaderService", "i5", "GoogleAnalyticsService", "i6", "GTMService", "_2", "selectors", "decls", "vars", "consts", "template", "RegisterOtpComponent_Template", "rf", "ctx", "RegisterOtpComponent_div_1_Template", "RegisterOtpComponent_ng_container_3_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\register\\components\\register-otp\\register-otp.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\register\\components\\register-otp\\register-otp.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';\r\nimport {Router} from '@angular/router';\r\nimport {MessageService} from \"primeng/api\";\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {Location } from '@angular/common';\r\n\r\nimport {LoaderService, StoreService, RegisterService, PermissionService} from \"@core/services\";\r\nimport { GoogleAnalyticsService } from 'ngx-google-analytics';\r\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n@Component({\r\n  selector: 'app-register-otp',\r\n  templateUrl: './register-otp.component.html',\r\n  styleUrls: ['./register-otp.component.scss']\r\n})\r\nexport class RegisterOtpComponent implements OnInit, OnDestroy {\r\n  loading: boolean = false;\r\n  mobileNumber: string = \"\";\r\n  size: number = 50;\r\n  value1: number | undefined;\r\n  value2: number | undefined;\r\n  value3: number | undefined;\r\n  value4: number | undefined;\r\n  otpCode: string = \"\";\r\n  timeLeft: any = 60;\r\n  interval: any;\r\n  countDown: any;\r\n  counter = 60;\r\n  tick = 1000;\r\n  isMobileLayout: boolean = false;\r\n  screenWidth:number = window.innerWidth;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private translate: TranslateService,\r\n    private router: Router,\r\n    private otpService: RegisterService,\r\n    private store: StoreService,\r\n    private permissionService: PermissionService,\r\n    private cd: ChangeDetectorRef, private loaderService: LoaderService,\r\n    private $gaService:GoogleAnalyticsService,\r\n    // private permissionService:PermissionService,\r\n    private $gtmService:GTMService) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.$gtmService.pushPageView('signUp','OTP')\r\n\r\n    this.startTimer();\r\n    if (history.state.mobile == null) {\r\n      this.router.navigate(['/register']);\r\n    } else {\r\n      this.mobileNumber = history.state.mobile;\r\n    }\r\n  }\r\n  ngOnDestroy() {\r\n    this.countDown = null;\r\n  }\r\n\r\n  startTimer() {\r\n\r\n    this.interval = setInterval(() => {\r\n      if (this.timeLeft > 0) {\r\n        this.timeLeft--;\r\n      } else {\r\n        this.timeLeft = 60;\r\n      }\r\n\r\n      if (this.timeLeft === 0) {\r\n        clearInterval(this.interval);\r\n      }\r\n      if (this.timeLeft < 10) {\r\n        this.timeLeft = '0' + this.timeLeft;\r\n      }\r\n      this.cd.detectChanges();\r\n    }, 1000);\r\n\r\n  }\r\n\r\n  onDigitInput(event: any) {\r\n\r\n    let element;\r\n    if (event.code !== 'Backspace' && event.code !== 'Tab')\r\n      element = event.srcElement.nextElementSibling;\r\n\r\n    if (event.code === 'Backspace')\r\n      element = event.srcElement.previousElementSibling;\r\n    if (event.code === 'Tab') {\r\n      return;\r\n    }\r\n    if (element == null)\r\n      return;\r\n    else\r\n      element.focus();\r\n  }\r\n\r\n  validateOtp() {\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_OTP_VERIFY, '', 'OTP_VERIFICATION', 1, true);\r\n\r\n    if ((this.value1 != null || this.value1 != undefined) && (this.value2 != null || this.value2 != undefined) &&\r\n      (this.value3 != null || this.value3 != undefined) && (this.value4 != null || this.value4 != undefined)) {\r\n      this.otpCode = this.value1.toString() + this.value2.toString() + this.value3.toString() + this.value4.toString();\r\n      this.otpService.checkOTP({\r\n        UserName: this.mobileNumber,\r\n        OTPCode: this.otpCode,\r\n        CountryId: \"1448983B-0C38-450A-BD71-9204D181B925\"\r\n      })\r\n        .subscribe({\r\n          next: (res: any) => {\r\n            this.loading = false;\r\n            if (res.success === 12) {\r\n              this.messageService.add({severity: 'error', summary: this.translate.instant('ErrorMessages.invalidOtp')});\r\n            } else if(res.success === 87){\r\n              this.router.navigateByUrl('/register/register-reset-password', {\r\n                state: {\r\n                  mobile: this.mobileNumber,\r\n                  otp: this.otpCode\r\n                }\r\n              });\r\n            }\r\n          },\r\n          error: (err: any) => {\r\n            this.loading = false;\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: this.translate.instant('ErrorMessages.fetchError'),\r\n              detail: err.message\r\n            });\r\n          }\r\n        });\r\n    } else {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\r\n      });\r\n    }\r\n  }\r\n\r\n  resendOtp() {\r\n    this.loaderService.show();\r\n    this.otpService.checkMobileNumber({\r\n      UserName: this.otpService.username,\r\n      CountryId: this.otpService.countryId\r\n    }).subscribe({\r\n      next: (res) => {\r\n        this.loaderService.hide();\r\n        this.startTimer();\r\n      },\r\n      error: (err) => {\r\n        this.store.set(\"loading\", false);\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          detail: err.message\r\n        });\r\n      }\r\n    });\r\n\r\n  }\r\n}\r\n", "<!-- <section class=\"otp\" [ngClass]=\"{'d-flex':isMobileLayout && screenWidth <= 767}\"> -->\r\n  <section class=\"update-password-page\">  \r\n    <div *ngIf=\"loading\" class=\"spinner\">\r\n      <p-progressSpinner></p-progressSpinner>\r\n    </div>\r\n    <app-back-button  [navigationUrl]=\"'/register'\"></app-back-button>\r\n  <ng-container *ngIf=\"!loading\">\r\n    <div [ngClass]=\"isMobileLayout  && screenWidth <= 767 ? 'mobile-content-container' : 'content-container'\"\r\n         [ngStyle]=\"{marginTop: isMobileLayout ? '1rem' : '220px' }\">\r\n         <!-- Desktop view -->\r\n         <ng-container *ngIf=\"isMobileLayout ? screenWidth > 767 : true\">\r\n      <div class=\"grid justify-content-center margin-x-75 shadow-signin\">\r\n         <div class=\"image col-12\" >\r\n          <img src=\"assets/images/registerLogo.svg\" alt=\"\" srcset=\"\">\r\n        </div>\r\n        <div class=\"col-12 col-md-8 col-lg-6  bg-white pt-6 d-flex justify-content-flex-end align-items-center  \">\r\n          <div class=\"p-fluid p-grid\">\r\n            <div class=\"p-field p-col-12\">\r\n              <p class=\" otp-heading\">\r\n                {{ \"auth.otp.title\" | translate }}\r\n              </p>\r\n              <div class=\"sent-otp\">\r\n                {{ \"auth.otp.subTitle\" | translate }}\r\n              </div>\r\n              <form action=\"\" class=\"form-container\">\r\n                <input placeholder=\"_\" type=\"text\" maxlength=\"1\" [(ngModel)]=\"value1\" [ngModelOptions]=\"{ standalone: true }\"\r\n                  oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n                <input  placeholder=\"_\" type=\"text\" maxlength=\"1\" [(ngModel)]=\"value2\" [ngModelOptions]=\"{ standalone: true }\"\r\n                  oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n                <input placeholder=\"_\" type=\"text\" maxlength=\"1\" [(ngModel)]=\"value3\" [ngModelOptions]=\"{ standalone: true }\"\r\n                  oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n                <input  placeholder=\"_\" type=\"text\" maxlength=\"1\" [(ngModel)]=\"value4\" [ngModelOptions]=\"{ standalone: true }\"\r\n                  oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n              </form>\r\n            </div>\r\n            <div class=\"count\">\r\n              <button style=\"background: white; border: none; cursor: pointer\" class=\"resend\" *ngIf=\"timeLeft==0\"\r\n                [disabled]=\"timeLeft !== '00'\" (click)=\"resendOtp()\">\r\n                {{ \"auth.otp.resend\" | translate }}\r\n              </button>\r\n              <span class=\"time-left\" *ngIf=\"timeLeft>0\"> {{ \"auth.otp.resendIn\" | translate }} 0:{{ timeLeft }}</span>\r\n            </div>\r\n            <button label=\"{{ 'auth.otp.next' | translate }}\"\r\n              class=\"p-field p-col-12 mb-5 mt-3 width-100 font-size-14 second-btn\" (click)=\"validateOtp()\" pButton\r\n              type=\"button\" [disabled]=\"!(value1 && value2 && value3 && value4)\"></button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n    \r\n    <!-- Mobile view -->\r\n    <ng-container *ngIf=\"isMobileLayout && screenWidth <= 767\">\r\n\r\n      <div class=\"justify-content-center p-0 shadow-signin bg-white mobile-contianer\">\r\n        <div class=\"d-flex flex-row pt-3\">\r\n          <img src=\"assets/images/register.svg\"/>\r\n          <div class=\"head-desc\">\r\n            <h2 class=\"signup-heading\"> {{ \"auth.otp.title\" | translate }}</h2>\r\n            <span class=\"signup-desc\">{{ \"auth.otp.subTitle\" | translate }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"d-flex flex-column mb-3\">\r\n          <form action=\"\" class=\"mt-5\">\r\n            <input type=\"text\" maxlength=\"1\" [(ngModel)]=\"value1\" [ngModelOptions]=\"{ standalone: true }\" placeholder=\"_\"\r\n              oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n            <input type=\"text\" maxlength=\"1\" [(ngModel)]=\"value2\" [ngModelOptions]=\"{ standalone: true }\" placeholder=\"_\"\r\n              oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n            <input type=\"text\" maxlength=\"1\" [(ngModel)]=\"value3\" [ngModelOptions]=\"{ standalone: true }\" placeholder=\"_\"\r\n              oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n            <input type=\"text\" maxlength=\"1\" [(ngModel)]=\"value4\" [ngModelOptions]=\"{ standalone: true }\" placeholder=\"_\"\r\n              oninput=\"this.value=this.value.replace(/[^0-9]/g,'');\" (keyup)=\"onDigitInput($event)\" />\r\n          </form>\r\n          <div class=\"count p-3\">\r\n            <button style=\"background: white; border: none; cursor: pointer\" class=\"resend\" *ngIf=\"timeLeft==0\"\r\n              [disabled]=\"timeLeft !== '00'\" (click)=\"resendOtp()\">\r\n              {{ \"auth.otp.resend\" | translate }}\r\n            </button>\r\n            <span class=\"time-left\" *ngIf=\"timeLeft>0\"> {{ \"auth.otp.resendIn\" | translate }} 0:{{ timeLeft }}</span>\r\n          </div>\r\n          <button label=\"{{ 'auth.otp.next' | translate }}\"\r\n            class=\"primary-btn second-btn m-3\" (click)=\"validateOtp()\" pButton\r\n            type=\"button\" [disabled]=\"!(value1 && value2 && value3 && value4)\"></button>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n    </div>\r\n  </ng-container>\r\n</section>\r\n\r\n"], "mappings": "AAQA,SAASA,iBAAiB,QAAQ,kCAAkC;;;;;;;;;;;;;;;;ICNhEC,EAAA,CAAAC,cAAA,aAAqC;IACnCD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAgCIH,EAAA,CAAAC,cAAA,iBACuD;IAAtBD,EAAA,CAAAI,UAAA,mBAAAC,8FAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACpDX,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAa,UAAA,aAAAC,MAAA,CAAAC,QAAA,UAA8B;IAC9Bf,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,+BACF;;;;;IACAlB,EAAA,CAAAC,cAAA,eAA2C;IAACD,EAAA,CAAAY,MAAA,GAAsD;;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;IAA7DH,EAAA,CAAAgB,SAAA,GAAsD;IAAtDhB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAkB,WAAA,oCAAAE,MAAA,CAAAL,QAAA,KAAsD;;;;;;;;;;;IA9BvGf,EAAA,CAAAqB,uBAAA,GAAgE;IACnErB,EAAA,CAAAC,cAAA,aAAmE;IAE/DD,EAAA,CAAAE,SAAA,aAA2D;IAC7DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0G;IAIlGD,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAY,MAAA,IACF;;IAAAZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuC;IACYD,EAAA,CAAAI,UAAA,2BAAAkB,4FAAAC,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAe,MAAA,CAAAC,MAAA,GAAAH,MAAA;IAAA,EAAoB,mBAAAI,oFAAAJ,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAI,OAAA,GAAA5B,EAAA,CAAAS,aAAA;MAAA,OACHT,EAAA,CAAAU,WAAA,CAAAkB,OAAA,CAAAC,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjB;IAArEvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADxCD,EAAA,CAAAI,UAAA,2BAAA0B,4FAAAP,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAO,OAAA,GAAA/B,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAqB,OAAA,CAAAC,MAAA,GAAAT,MAAA;IAAA,EAAoB,mBAAAU,oFAAAV,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAU,OAAA,GAAAlC,EAAA,CAAAS,aAAA;MAAA,OACJT,EAAA,CAAAU,WAAA,CAAAwB,OAAA,CAAAL,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADhB;IAAtEvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADzCD,EAAA,CAAAI,UAAA,2BAAA+B,4FAAAZ,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAY,OAAA,GAAApC,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAA0B,OAAA,CAAAC,MAAA,GAAAd,MAAA;IAAA,EAAoB,mBAAAe,oFAAAf,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAe,OAAA,GAAAvC,EAAA,CAAAS,aAAA;MAAA,OACHT,EAAA,CAAAU,WAAA,CAAA6B,OAAA,CAAAV,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjB;IAArEvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADxCD,EAAA,CAAAI,UAAA,2BAAAoC,4FAAAjB,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAiB,OAAA,GAAAzC,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAA+B,OAAA,CAAAC,MAAA,GAAAnB,MAAA;IAAA,EAAoB,mBAAAoB,oFAAApB,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAoB,OAAA,GAAA5C,EAAA,CAAAS,aAAA;MAAA,OACJT,EAAA,CAAAU,WAAA,CAAAkC,OAAA,CAAAf,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADhB;IAAtEvB,EAAA,CAAAG,YAAA,EAC0F;IAG9FH,EAAA,CAAAC,cAAA,eAAmB;IACjBD,EAAA,CAAA6C,UAAA,KAAAC,qEAAA,qBAGS;IACT9C,EAAA,CAAA6C,UAAA,KAAAE,mEAAA,mBAAyG;IAC3G/C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAEqE;IADED,EAAA,CAAAI,UAAA,mBAAA4C,qFAAA;MAAAhD,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAyB,OAAA,GAAAjD,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAuC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;;IACzBlD,EAAA,CAAAG,YAAA,EAAS;IAItFH,EAAA,CAAAmD,qBAAA,EAAe;;;;IA7BHnD,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,+BACF;IAEElB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,mCACF;IAEmDlB,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAuC,MAAA,CAAA1B,MAAA,CAAoB,mBAAA1B,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAEnBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAuC,MAAA,CAAApB,MAAA,CAAoB,mBAAAhC,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAErBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAuC,MAAA,CAAAf,MAAA,CAAoB,mBAAArC,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAEnBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAuC,MAAA,CAAAV,MAAA,CAAoB,mBAAA1C,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAKStD,EAAA,CAAAgB,SAAA,GAAiB;IAAjBhB,EAAA,CAAAa,UAAA,SAAAuC,MAAA,CAAArC,QAAA,MAAiB;IAIzEf,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAa,UAAA,SAAAuC,MAAA,CAAArC,QAAA,KAAgB;IAEnCf,EAAA,CAAAgB,SAAA,GAAyC;IAAzChB,EAAA,CAAAuD,qBAAA,UAAAvD,EAAA,CAAAkB,WAAA,0BAAyC;IAEjClB,EAAA,CAAAa,UAAA,eAAAuC,MAAA,CAAA1B,MAAA,IAAA0B,MAAA,CAAApB,MAAA,IAAAoB,MAAA,CAAAf,MAAA,IAAAe,MAAA,CAAAV,MAAA,EAAoD;;;;;;IA6BpE1C,EAAA,CAAAC,cAAA,iBACuD;IAAtBD,EAAA,CAAAI,UAAA,mBAAAoD,8FAAA;MAAAxD,EAAA,CAAAM,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAgD,OAAA,CAAA/C,SAAA,EAAW;IAAA,EAAC;IACpDX,EAAA,CAAAY,MAAA,GACF;;IAAAZ,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAa,UAAA,aAAA8C,OAAA,CAAA5C,QAAA,UAA8B;IAC9Bf,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,+BACF;;;;;IACAlB,EAAA,CAAAC,cAAA,eAA2C;IAACD,EAAA,CAAAY,MAAA,GAAsD;;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;IAA7DH,EAAA,CAAAgB,SAAA,GAAsD;IAAtDhB,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAkB,WAAA,oCAAA0C,OAAA,CAAA7C,QAAA,KAAsD;;;;;;IA1B1Gf,EAAA,CAAAqB,uBAAA,GAA2D;IAEzDrB,EAAA,CAAAC,cAAA,cAAgF;IAE5ED,EAAA,CAAAE,SAAA,cAAuC;IACvCF,EAAA,CAAAC,cAAA,cAAuB;IACOD,EAAA,CAAAY,MAAA,GAAkC;;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAqC;;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAG1EH,EAAA,CAAAC,cAAA,eAAqC;IAEAD,EAAA,CAAAI,UAAA,2BAAAyD,4FAAAtC,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAqD,OAAA,CAAArC,MAAA,GAAAH,MAAA;IAAA,EAAoB,mBAAAyC,oFAAAzC,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAG,OAAA,GAAAjE,EAAA,CAAAS,aAAA;MAAA,OACaT,EAAA,CAAAU,WAAA,CAAAuD,OAAA,CAAApC,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjC;IAArDvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADzDD,EAAA,CAAAI,UAAA,2BAAA8D,4FAAA3C,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAK,OAAA,GAAAnE,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAyD,OAAA,CAAAnC,MAAA,GAAAT,MAAA;IAAA,EAAoB,mBAAA6C,oFAAA7C,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAO,OAAA,GAAArE,EAAA,CAAAS,aAAA;MAAA,OACaT,EAAA,CAAAU,WAAA,CAAA2D,OAAA,CAAAxC,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjC;IAArDvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADzDD,EAAA,CAAAI,UAAA,2BAAAkE,4FAAA/C,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAS,OAAA,GAAAvE,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAA6D,OAAA,CAAAlC,MAAA,GAAAd,MAAA;IAAA,EAAoB,mBAAAiD,oFAAAjD,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAW,OAAA,GAAAzE,EAAA,CAAAS,aAAA;MAAA,OACaT,EAAA,CAAAU,WAAA,CAAA+D,OAAA,CAAA5C,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjC;IAArDvB,EAAA,CAAAG,YAAA,EAC0F;IAC1FH,EAAA,CAAAC,cAAA,iBAC0F;IADzDD,EAAA,CAAAI,UAAA,2BAAAsE,4FAAAnD,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAa,OAAA,GAAA3E,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAiE,OAAA,CAAAjC,MAAA,GAAAnB,MAAA;IAAA,EAAoB,mBAAAqD,oFAAArD,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAe,OAAA,GAAA7E,EAAA,CAAAS,aAAA;MAAA,OACaT,EAAA,CAAAU,WAAA,CAAAmE,OAAA,CAAAhD,YAAA,CAAAN,MAAA,CAAoB;IAAA,EADjC;IAArDvB,EAAA,CAAAG,YAAA,EAC0F;IAE5FH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA6C,UAAA,KAAAiC,qEAAA,qBAGS;IACT9E,EAAA,CAAA6C,UAAA,KAAAkC,mEAAA,mBAAyG;IAC3G/E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAEqE;IADhCD,EAAA,CAAAI,UAAA,mBAAA4E,qFAAA;MAAAhF,EAAA,CAAAM,aAAA,CAAAwD,IAAA;MAAA,MAAAmB,OAAA,GAAAjF,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAuE,OAAA,CAAA/B,WAAA,EAAa;IAAA,EAAC;;IACSlD,EAAA,CAAAG,YAAA,EAAS;IAGpFH,EAAA,CAAAmD,qBAAA,EAAe;;;;IA3BqBnD,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,8BAAkC;IACpClB,EAAA,CAAAgB,SAAA,GAAqC;IAArChB,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAAkB,WAAA,8BAAqC;IAK9BlB,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAsE,MAAA,CAAAzD,MAAA,CAAoB,mBAAA1B,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAEpBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAsE,MAAA,CAAAnD,MAAA,CAAoB,mBAAAhC,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAEpBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAsE,MAAA,CAAA9C,MAAA,CAAoB,mBAAArC,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAEpBtD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAa,UAAA,YAAAsE,MAAA,CAAAzC,MAAA,CAAoB,mBAAA1C,EAAA,CAAAqD,eAAA,KAAAC,GAAA;IAI4BtD,EAAA,CAAAgB,SAAA,GAAiB;IAAjBhB,EAAA,CAAAa,UAAA,SAAAsE,MAAA,CAAApE,QAAA,MAAiB;IAIzEf,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAa,UAAA,SAAAsE,MAAA,CAAApE,QAAA,KAAgB;IAEnCf,EAAA,CAAAgB,SAAA,GAAyC;IAAzChB,EAAA,CAAAuD,qBAAA,UAAAvD,EAAA,CAAAkB,WAAA,0BAAyC;IAEjClB,EAAA,CAAAa,UAAA,eAAAsE,MAAA,CAAAzD,MAAA,IAAAyD,MAAA,CAAAnD,MAAA,IAAAmD,MAAA,CAAA9C,MAAA,IAAA8C,MAAA,CAAAzC,MAAA,EAAoD;;;;;;;;;;IA3E5E1C,EAAA,CAAAqB,uBAAA,GAA+B;IAC7BrB,EAAA,CAAAC,cAAA,aACiE;IAE5DD,EAAA,CAAA6C,UAAA,IAAAuC,2DAAA,4BAsCU;IAGfpF,EAAA,CAAA6C,UAAA,IAAAwC,2DAAA,4BAiCe;IACfrF,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAmD,qBAAA,EAAe;;;;IA/ERnD,EAAA,CAAAgB,SAAA,GAAoG;IAApGhB,EAAA,CAAAa,UAAA,YAAAyE,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,WAAA,2DAAoG,YAAAxF,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAC,cAAA;IAGrFvF,EAAA,CAAAgB,SAAA,GAA+C;IAA/ChB,EAAA,CAAAa,UAAA,SAAAyE,MAAA,CAAAC,cAAA,GAAAD,MAAA,CAAAE,WAAA,cAA+C;IAyCpDxF,EAAA,CAAAgB,SAAA,GAA0C;IAA1ChB,EAAA,CAAAa,UAAA,SAAAyE,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,WAAA,QAA0C;;;ADnC7D,OAAM,MAAOG,oBAAoB;EAiB/BC,YACUC,cAA8B,EAC9BC,SAA2B,EAC3BC,MAAc,EACdC,UAA2B,EAC3BC,KAAmB,EACnBC,iBAAoC,EACpCC,EAAqB,EAAUC,aAA4B,EAC3DC,UAAiC;EACzC;EACQC,WAAsB;IATtB,KAAAT,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,EAAE,GAAFA,EAAE;IAA6B,KAAAC,aAAa,GAAbA,aAAa;IAC5C,KAAAC,UAAU,GAAVA,UAAU;IAEV,KAAAC,WAAW,GAAXA,WAAW;IA1BrB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,IAAI,GAAW,EAAE;IAKjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA3F,QAAQ,GAAQ,EAAE;IAGlB,KAAA4F,OAAO,GAAG,EAAE;IACZ,KAAAC,IAAI,GAAG,IAAI;IACX,KAAArB,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAUqB,MAAM,CAACC,UAAU;EAatC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACxB,cAAc,GAAG,IAAI,CAACW,iBAAiB,CAACc,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI,CAACV,WAAW,CAACW,YAAY,CAAC,QAAQ,EAAC,KAAK,CAAC;IAE7C,IAAI,CAACC,UAAU,EAAE;IACjB,IAAIC,OAAO,CAACC,KAAK,CAACC,MAAM,IAAI,IAAI,EAAE;MAChC,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;KACpC,MAAM;MACL,IAAI,CAACd,YAAY,GAAGW,OAAO,CAACC,KAAK,CAACC,MAAM;;EAE5C;EACAE,WAAWA,CAAA;IACT,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;EAEAN,UAAUA,CAAA;IAER,IAAI,CAACO,QAAQ,GAAGC,WAAW,CAAC,MAAK;MAC/B,IAAI,IAAI,CAAC3G,QAAQ,GAAG,CAAC,EAAE;QACrB,IAAI,CAACA,QAAQ,EAAE;OAChB,MAAM;QACL,IAAI,CAACA,QAAQ,GAAG,EAAE;;MAGpB,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;QACvB4G,aAAa,CAAC,IAAI,CAACF,QAAQ,CAAC;;MAE9B,IAAI,IAAI,CAAC1G,QAAQ,GAAG,EAAE,EAAE;QACtB,IAAI,CAACA,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACA,QAAQ;;MAErC,IAAI,CAACoF,EAAE,CAACyB,aAAa,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EAEV;EAEA/F,YAAYA,CAACgG,KAAU;IAErB,IAAIC,OAAO;IACX,IAAID,KAAK,CAACE,IAAI,KAAK,WAAW,IAAIF,KAAK,CAACE,IAAI,KAAK,KAAK,EACpDD,OAAO,GAAGD,KAAK,CAACG,UAAU,CAACC,kBAAkB;IAE/C,IAAIJ,KAAK,CAACE,IAAI,KAAK,WAAW,EAC5BD,OAAO,GAAGD,KAAK,CAACG,UAAU,CAACE,sBAAsB;IACnD,IAAIL,KAAK,CAACE,IAAI,KAAK,KAAK,EAAE;MACxB;;IAEF,IAAID,OAAO,IAAI,IAAI,EACjB,OAAO,KAEPA,OAAO,CAACK,KAAK,EAAE;EACnB;EAEAjF,WAAWA,CAAA;IACT,IAAI,CAACmD,UAAU,CAACwB,KAAK,CAAC9H,iBAAiB,CAACqI,mBAAmB,EAAE,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,IAAI,CAAC;IAE7F,IAAI,CAAC,IAAI,CAAC1G,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI2G,SAAS,MAAM,IAAI,CAACrG,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAIqG,SAAS,CAAC,KACvG,IAAI,CAAChG,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAIgG,SAAS,CAAC,KAAK,IAAI,CAAC3F,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI2F,SAAS,CAAC,EAAE;MACxG,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAAChF,MAAM,CAAC4G,QAAQ,EAAE,GAAG,IAAI,CAACtG,MAAM,CAACsG,QAAQ,EAAE,GAAG,IAAI,CAACjG,MAAM,CAACiG,QAAQ,EAAE,GAAG,IAAI,CAAC5F,MAAM,CAAC4F,QAAQ,EAAE;MAChH,IAAI,CAACtC,UAAU,CAACuC,QAAQ,CAAC;QACvBC,QAAQ,EAAE,IAAI,CAAChC,YAAY;QAC3BiC,OAAO,EAAE,IAAI,CAAC/B,OAAO;QACrBgC,SAAS,EAAE;OACZ,CAAC,CACCC,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAACtC,OAAO,GAAG,KAAK;UACpB,IAAIsC,GAAG,CAACC,OAAO,KAAK,EAAE,EAAE;YACtB,IAAI,CAACjD,cAAc,CAACkD,GAAG,CAAC;cAACC,QAAQ,EAAE,OAAO;cAAEC,OAAO,EAAE,IAAI,CAACnD,SAAS,CAACoD,OAAO,CAAC,0BAA0B;YAAC,CAAC,CAAC;WAC1G,MAAM,IAAGL,GAAG,CAACC,OAAO,KAAK,EAAE,EAAC;YAC3B,IAAI,CAAC/C,MAAM,CAACoD,aAAa,CAAC,mCAAmC,EAAE;cAC7D/B,KAAK,EAAE;gBACLC,MAAM,EAAE,IAAI,CAACb,YAAY;gBACzB4C,GAAG,EAAE,IAAI,CAAC1C;;aAEb,CAAC;;QAEN,CAAC;QACD2C,KAAK,EAAGC,GAAQ,IAAI;UAClB,IAAI,CAAC/C,OAAO,GAAG,KAAK;UACpB,IAAI,CAACV,cAAc,CAACkD,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAACnD,SAAS,CAACoD,OAAO,CAAC,0BAA0B,CAAC;YAC3DK,MAAM,EAAED,GAAG,CAACE;WACb,CAAC;QACJ;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAAC3D,cAAc,CAACkD,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,IAAI,CAACnD,SAAS,CAACoD,OAAO,CAAC,0BAA0B,CAAC;QAC3DK,MAAM,EAAE,IAAI,CAACzD,SAAS,CAACoD,OAAO,CAAC,8BAA8B;OAC9D,CAAC;;EAEN;EAEAvI,SAASA,CAAA;IACP,IAAI,CAACyF,aAAa,CAACqD,IAAI,EAAE;IACzB,IAAI,CAACzD,UAAU,CAAC0D,iBAAiB,CAAC;MAChClB,QAAQ,EAAE,IAAI,CAACxC,UAAU,CAAC2D,QAAQ;MAClCjB,SAAS,EAAE,IAAI,CAAC1C,UAAU,CAAC4D;KAC5B,CAAC,CAACjB,SAAS,CAAC;MACXC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACzC,aAAa,CAACyD,IAAI,EAAE;QACzB,IAAI,CAAC3C,UAAU,EAAE;MACnB,CAAC;MACDmC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAACrD,KAAK,CAAC6D,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;QAChC,IAAI,CAACjE,cAAc,CAACkD,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,IAAI,CAACnD,SAAS,CAACoD,OAAO,CAAC,0BAA0B,CAAC;UAC3DK,MAAM,EAAED,GAAG,CAACE;SACb,CAAC;MACJ;KACD,CAAC;EAEJ;EAAC,QAAAO,CAAA,G;qBAjJUpE,oBAAoB,EAAA3F,EAAA,CAAAgK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlK,EAAA,CAAAgK,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAApK,EAAA,CAAAgK,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtK,EAAA,CAAAgK,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAxK,EAAA,CAAAgK,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAAzK,EAAA,CAAAgK,iBAAA,CAAAO,EAAA,CAAAG,iBAAA,GAAA1K,EAAA,CAAAgK,iBAAA,CAAAhK,EAAA,CAAA2K,iBAAA,GAAA3K,EAAA,CAAAgK,iBAAA,CAAAO,EAAA,CAAAK,aAAA,GAAA5K,EAAA,CAAAgK,iBAAA,CAAAa,EAAA,CAAAC,sBAAA,GAAA9K,EAAA,CAAAgK,iBAAA,CAAAe,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBtF,oBAAoB;IAAAuF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCf/BxL,EAAA,CAAAC,cAAA,iBAAsC;QACpCD,EAAA,CAAA6C,UAAA,IAAA6I,mCAAA,iBAEM;QACN1L,EAAA,CAAAE,SAAA,yBAAkE;QACpEF,EAAA,CAAA6C,UAAA,IAAA8I,4CAAA,0BAgFe;QACjB3L,EAAA,CAAAG,YAAA,EAAU;;;QArFAH,EAAA,CAAAgB,SAAA,GAAa;QAAbhB,EAAA,CAAAa,UAAA,SAAA4K,GAAA,CAAAlF,OAAA,CAAa;QAGDvG,EAAA,CAAAgB,SAAA,GAA6B;QAA7BhB,EAAA,CAAAa,UAAA,8BAA6B;QAClCb,EAAA,CAAAgB,SAAA,GAAc;QAAdhB,EAAA,CAAAa,UAAA,UAAA4K,GAAA,CAAAlF,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}