module.exports = {
  // === CORE FORMATTING RULES ===

  // Semicolons - Always add semicolons at the end of statements
  semi: true,

  // Trailing commas - Add trailing commas in ES5-compatible locations
  trailingComma: 'es5',

  // Quotes - Use single quotes instead of double quotes for strings
  singleQuote: true,

  // Line width - Wrap lines that exceed 80 characters
  printWidth: 80,

  // Indentation - Use 2 spaces for indentation (no tabs)
  tabWidth: 2,
  useTabs: false,

  // === SPACING & BRACKETS ===

  // Add spaces inside object brackets: { foo: bar } instead of {foo: bar}
  bracketSpacing: true,

  // Put closing bracket on new line for multi-line elements
  bracketSameLine: false,

  // Arrow function parentheses - Omit when possible: x => x instead of (x) => x
  arrowParens: 'avoid',

  // === LINE ENDINGS & FORMATTING ===

  // Use Unix line endings (LF) for consistency across platforms
  endOfLine: 'lf',

  // Insert final newline at end of file
  insertFinalNewline: true,

  // Remove trailing whitespace
  trimTrailingWhitespace: true,

  // Quote properties in objects only when needed
  quoteProps: 'as-needed',

  // Use consistent spacing around embedded expressions in templates
  embeddedLanguageFormatting: 'auto',

  // === FILE-SPECIFIC OVERRIDES ===
  overrides: [
    {
      // Angular HTML templates
      files: ['*.html', '*.component.html'],
      options: {
        parser: 'angular',
        printWidth: 120, // Longer lines for HTML attributes
        bracketSameLine: false, // Keep closing brackets on new lines
        htmlWhitespaceSensitivity: 'css', // Respect CSS display values
      },
    },
    {
      // SCSS/CSS files
      files: ['*.scss', '*.css'],
      options: {
        parser: 'scss',
        singleQuote: false, // Use double quotes in CSS
        printWidth: 100, // Slightly longer for CSS properties
      },
    },
    {
      // JSON files
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none', // JSON doesn't support trailing commas
        printWidth: 120, // Allow longer lines for JSON
      },
    },
    {
      // TypeScript files
      files: ['*.ts', '*.tsx'],
      options: {
        parser: 'typescript',
        semi: true,
        singleQuote: true,
        trailingComma: 'es5',
      },
    },
    {
      // JavaScript files
      files: ['*.js', '*.jsx'],
      options: {
        parser: 'babel',
        semi: true,
        singleQuote: true,
        trailingComma: 'es5',
      },
    },
    {
      // Markdown files
      files: '*.md',
      options: {
        parser: 'markdown',
        printWidth: 80,
        proseWrap: 'always', // Wrap markdown text
        tabWidth: 2,
      },
    },
  ],
};
