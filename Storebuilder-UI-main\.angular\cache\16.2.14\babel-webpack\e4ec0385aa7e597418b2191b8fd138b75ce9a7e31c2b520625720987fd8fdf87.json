{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"ngx-cookie-service\";\nexport class AuthGuard {\n  router;\n  injector;\n  authTokenService;\n  cookieService;\n  token = '';\n  constructor(router, injector, authTokenService, cookieService) {\n    this.router = router;\n    this.injector = injector;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n  }\n  canActivate(route, state) {\n    let localToken = null;\n    this.authTokenService.authTokenData.subscribe(message => localToken = message);\n    const cookieToken = this.cookieService.get('authToken');\n    if (localToken) {\n      this.token = localToken;\n      return true;\n    } else if (cookieToken && cookieToken !== '') {\n      this.token = cookieToken;\n      return true;\n    }\n    this.router.navigate(['/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    return false;\n  }\n  static ɵfac = function AuthGuard_Factory(t) {\n    return new (t || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.AuthTokenService), i0.ɵɵinject(i3.CookieService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "router", "injector", "authTokenService", "cookieService", "token", "constructor", "canActivate", "route", "state", "localToken", "authTokenData", "subscribe", "message", "cookieToken", "get", "navigate", "queryParams", "returnUrl", "url", "i0", "ɵɵinject", "i1", "Router", "Injector", "i2", "AuthTokenService", "i3", "CookieService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import {Injectable, Injector} from '@angular/core';\r\nimport {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot} from '@angular/router';\r\nimport {CookieService} from 'ngx-cookie-service';\r\nimport {AuthTokenService} from '@core/services';\r\n\r\n@Injectable({providedIn: 'root'})\r\nexport class AuthGuard implements CanActivate {\r\n  token: any = '';\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private injector: Injector,\r\n    private authTokenService: AuthTokenService, private cookieService: CookieService\r\n  ) {\r\n  }\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {\r\n    let localToken = null;\r\n    this.authTokenService.authTokenData.subscribe((message: any) => localToken = message);\r\n    const cookieToken = this.cookieService.get('authToken');\r\n    if (localToken) {\r\n      this.token = localToken;\r\n      return true;\r\n    } else if (cookieToken && cookieToken !== '') {\r\n      this.token = cookieToken;\r\n      return true;\r\n    }\r\n\r\n\r\n    this.router.navigate(['/login'], {queryParams: {returnUrl: state.url}});\r\n    return false;\r\n  }\r\n}\r\n"], "mappings": ";;;;AAMA,OAAM,MAAOA,SAAS;EAIVC,MAAA;EACAC,QAAA;EACAC,gBAAA;EAA4CC,aAAA;EALtDC,KAAK,GAAQ,EAAE;EAEfC,YACUL,MAAc,EACdC,QAAkB,EAClBC,gBAAkC,EAAUC,aAA4B;IAFxE,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAA4B,KAAAC,aAAa,GAAbA,aAAa;EAEnE;EAEAG,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAI,CAACP,gBAAgB,CAACQ,aAAa,CAACC,SAAS,CAAEC,OAAY,IAAKH,UAAU,GAAGG,OAAO,CAAC;IACrF,MAAMC,WAAW,GAAG,IAAI,CAACV,aAAa,CAACW,GAAG,CAAC,WAAW,CAAC;IACvD,IAAIL,UAAU,EAAE;MACd,IAAI,CAACL,KAAK,GAAGK,UAAU;MACvB,OAAO,IAAI;KACZ,MAAM,IAAII,WAAW,IAAIA,WAAW,KAAK,EAAE,EAAE;MAC5C,IAAI,CAACT,KAAK,GAAGS,WAAW;MACxB,OAAO,IAAI;;IAIb,IAAI,CAACb,MAAM,CAACe,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAACC,WAAW,EAAE;QAACC,SAAS,EAAET,KAAK,CAACU;MAAG;IAAC,CAAC,CAAC;IACvE,OAAO,KAAK;EACd;;qBAzBWnB,SAAS,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAI,QAAA,GAAAJ,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAAN,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAC,aAAA;EAAA;;WAAT5B,SAAS;IAAA6B,OAAA,EAAT7B,SAAS,CAAA8B,IAAA;IAAAC,UAAA,EADG;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}