{"ast": null, "code": "import { v4 as uuidv4 } from 'uuid';\nimport * as i0 from \"@angular/core\";\nexport class DeviceDetectionService {\n  deviceType = '';\n  deviceIdKey = 'device_id';\n  constructor() {}\n  isMobile() {\n    const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n    return /android|iPhone|iPad|iPod/i.test(userAgent) && !window.MSStream;\n  }\n  isTablet() {\n    const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n    return /iPad|Android/i.test(userAgent) && !this.isMobile();\n  }\n  isDesktop() {\n    return !this.isMobile() && !this.isTablet();\n  }\n  getDeviceType() {\n    if (this.isMobile()) {\n      this.deviceType = 'mobile';\n    } else if (this.isTablet()) {\n      this.deviceType = 'tablet';\n    } else {\n      this.deviceType = 'desktop';\n    }\n    return this.deviceType;\n  }\n  getDeviceId() {\n    let deviceId = localStorage.getItem(this.deviceIdKey);\n    if (!deviceId) {\n      deviceId = uuidv4();\n      localStorage.setItem(this.deviceIdKey, deviceId);\n    }\n    return deviceId;\n  }\n  static ɵfac = function DeviceDetectionService_Factory(t) {\n    return new (t || DeviceDetectionService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DeviceDetectionService,\n    factory: DeviceDetectionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}