{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { CardModule } from 'primeng/card';\nimport { RippleModule } from 'primeng/ripple';\nimport { AboutUsComponentComponent } from './components/about-us/AboutUsComponent/AboutUsComponent.component';\nimport { routes } from './routes';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AboutUsModule {\n  static ɵfac = function AboutUsModule_Factory(t) {\n    return new (t || AboutUsModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AboutUsModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), RippleModule, CardModule, TranslateModule, SharedModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AboutUsModule, {\n    declarations: [AboutUsComponentComponent],\n    imports: [CommonModule, i1.RouterModule, RippleModule, CardModule, TranslateModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "CardModule", "RippleModule", "AboutUsComponentComponent", "routes", "TranslateModule", "SharedModule", "AboutUsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\footer-pages\\about-us\\about-us.module.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { CardModule } from 'primeng/card';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { AboutUsComponentComponent } from './components/about-us/AboutUsComponent/AboutUsComponent.component';\r\nimport { routes } from './routes';\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\n\r\n@NgModule({\r\n  declarations: [AboutUsComponentComponent],\r\n    imports: [\r\n        CommonModule,\r\n        RouterModule.forChild(routes),\r\n        RippleModule,\r\n        CardModule,\r\n        TranslateModule,\r\n        SharedModule\r\n    ],\r\n})\r\nexport class AboutUsModule {}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,MAAM,QAAQ,UAAU;AACjC,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,YAAY,QAAO,+BAA+B;;;AAa1D,OAAM,MAAOC,aAAa;;qBAAbA,aAAa;EAAA;;UAAbA;EAAa;;cARlBR,YAAY,EACZC,YAAY,CAACQ,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY,EACZD,UAAU,EACVI,eAAe,EACfC,YAAY;EAAA;;;2EAGPC,aAAa;IAAAE,YAAA,GAVTN,yBAAyB;IAAAO,OAAA,GAElCX,YAAY,EAAAY,EAAA,CAAAX,YAAA,EAEZE,YAAY,EACZD,UAAU,EACVI,eAAe,EACfC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}