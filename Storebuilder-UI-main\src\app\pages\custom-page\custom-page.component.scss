@import "../../../assets/scss/common.scss";

main {
  padding: 16px;

  .main-container {
    h2 {
      padding: 8px 0;
      margin: 5px 0 5px 9px;
      font-family: $font-main-medium;
      color: rgb(33, 37, 41);
      font-weight: 700;
      font-size: 22px;
      text-transform: capitalize;
    }

    .each-product {
      display: flex;
      flex-wrap: wrap !important;
      padding: 0 16px 0 0;
      gap: 7px;

      .product-card {
        width: 220px;
        display: flex;
        flex-direction: column;
      }
    }

    &.banner-container {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      margin-top: 24px;

      &:first-child {
        margin-top: 0;
      }

      .single-banner {
        flex: 1;
      }

      img {
        width: 100%;
        border-radius: 6px;
        background: linear-gradient(90deg, #204e6e 0%, #3e96d4 100%);
        box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
      }

      .banner-desktop {
        @media (max-width: 768px) {
          display: none;
        }
      }

      .banner-mobile {
        display: none;
        @media (max-width: 768px) {
          display: block;
        }
      }

      /* === MOBILE GRID LAYOUT === */
      @media (max-width: 768px) {
        display: grid;
        gap: 12px;
        margin-top: 16px;

        &.banners-1 {
          grid-template-columns: 1fr;
        }

        &.banners-2 {
          grid-template-columns: 1fr 1fr;
        }

        &.banners-3 {
          /* First banner full width, next two half-width */
          grid-template-columns: repeat(2, 1fr);

          .single-banner:first-child {
            grid-column: 1 / -1; // spans full width
          }
        }

        &.banners-4 {
          grid-template-columns: repeat(2, 1fr);
        }

        .single-banner {
          flex: none;
        }
      }
    }
  }
}

.see-all {
  color: #191c1f;
  font-family: var(--medium-font);
  cursor: pointer;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;

  &:hover {
    text-decoration: underline;
    color: #1a445e;
  }
}

@media (max-width: 1200px) {
  main {
    margin-top: 120px !important;
  }
}

@media (max-width: 768px) {
  main {
    margin-top: 0px !important;
    .main-container {
      h2 {
        font-size: 18px;
        text-transform: uppercase;
      }
      .each-product {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px; // Consistent gap for grid
        align-items: start; // Prevents grid items from stretching

        .product-card {
          margin: 0; // Remove auto margin in grid
          justify-self: center; // Center cards in grid cells
        }
      }
    }
  }
}

@media (max-width: 699px) {
  main {
    margin-top: 35px !important;
  }
}

@media (max-width: 575px) {
  main {
    margin-top: 60px !important;
  }
}

@media (max-width: 467px) {
  main {
    .main-container {
      .each-product {
        padding: 0 0 0 0;
        .product-card {
          width: 150px !important;
        }
      }
    }
  }
}

@media (max-width: 323px) {
  main {
    .main-container {
      padding: 0 0 0 0;
      .each-product {
        padding: 0 !important;
        .product-card {
          width: 130px !important;
        }
      }
    }
  }
}

@media (max-width: 280px) {
  main {
    .main-container {
      .each-product {
        grid-template-columns: 1fr !important;
        .product-card {
          width: auto !important;
        }
      }
    }
  }
}
