<app-back-button></app-back-button>
<section  [ngClass]="isMobileLayout && screenWidth <= 767 ? 'd-flex h-100 mt-0' :'register'">
  <!-- is Desktop  -->
  <ng-container *ngIf="isMobileLayout ? screenWidth > 767  : true">
    <div class="content-container my-3">
      <div class="grid justify-content-between  shadow-signin">
        <!-- Image -->
        <div class="image col-12" >
          <img src="assets/images/registerLogo.svg" alt="" srcset="">
        </div>
          <div class="col-12 col-md-8 col-lg-6  bg-white  pt-6 header-body">
            <p class="signUp-heading">
              {{ "register.signUp" | translate }}
            </p>
           <p class="signUp-content"> {{ "register.content" | translate }}</p>
            <form [formGroup]="registerForm" autocomplete="new-password">
            <div class="p-fluid p-grid">
              <div class="mt-3">
                <div [ngStyle]="{
                    border:
                      registerForm.controls.phoneNumber.value?.e164Number
                        ?.length > 0 && !registerForm.controls.phoneNumber.valid
                        ? '1px solid red'
                        : '0px solid transparent'
                  }" class="w-full">
                  <form #f="ngForm" [formGroup]="registerForm">
                    <label class="contact-label" for="mobileNumber">{{ "contactUs.mobileNumber" | translate }}</label>
                    <ngx-intl-tel-input [cssClass]="'custom contact-input-phone'" [enableAutoCountrySelect]="true"
                      [enablePlaceholder]="true" [maxLength]="phoneInputLength"
                      [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
                      [preferredCountries]="preferredCountries"
                      [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                      [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
                      [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" formControlName="phoneNumber"
                      name="phoneNumber" (input)="onPhoneNumberInput()"></ngx-intl-tel-input>
                  </form>
                </div>
              </div>
               <div class="d-flex flex-column mb-5 btn-container">
              <button (click)="checkMobileExist()" [disabled]="!registerForm.controls.phoneNumber.valid"
                [label]="'register.continue' | translate"
                class="p-field p-col-12  width-100 font-size-14 second-btn" pButton type="button">
              </button>

              <p class="signin-agreement"> {{ "signIn.AgreeTermsOne" | translate }} <a (click)="reloadCurrentPage(171, 'Terms and Conditions')"> {{ "signIn.AgreeTermsTwo" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsThree" | translate }} <a (click)="reloadCurrentPage(3, 'Privacy policy')"> {{ "signIn.AgreeTermsFour" | translate }}</a>&nbsp;{{ "signIn.AgreeTermsFive" | translate }}.</p>
              <div class="new-customer-container">
                <p> {{ "register.alreadyHaveAccount" | translate }} </p>
                <a routerLink="/login"
                [label]="'register.login' | translate"
                class="register-now p-field p-col-12 " pButton type="button"></a>
              </div>
              </div>

            </div>
          </form>
        </div>
      </div>
    </div>
  </ng-container>


<!-- is Mobile  -->
  <ng-container *ngIf="isMobileLayout && screenWidth <= 767">
<div class="mobile-content-container mobile-container">
  <div class="d-flex flex-row py-3">
    <img src="assets/images/signup.svg"/>
    <div class="d-flex flex-column justify-content-center">
      <h2 class="signup-heading"> {{ "auth.registerPassword.title" | translate }}</h2>
      <span class="signup-desc">{{ "auth.registerPassword.desc" | translate }}</span>
    </div>
  </div>

        <div class="custom-input">

          <form #f="ngForm" [formGroup]="registerForm">
            <label>{{'register.mobileNumber' | translate}}</label>
            <ngx-intl-tel-input [cssClass]="'custom contact-input-phone mobile-input-phone'" [enableAutoCountrySelect]="true"
              [enablePlaceholder]="true" [maxLength]="phoneInputLength"
              [numberFormat]="PhoneNumberFormat.National" [phoneValidation]="false"
              [preferredCountries]="preferredCountries"
              [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
              [searchCountryFlag]="true" [selectFirstCountry]="false" [selectedCountryISO]="CustomCountryISO"
              [separateDialCode]="true" [customPlaceholder]="customPlaceHolder" formControlName="phoneNumber"
              name="phoneNumber" (input)="onPhoneNumberInput()"></ngx-intl-tel-input>
          </form>

        </div>
        <div class="d-flex flex-column mt-4 mb-5">
          <button (click)="checkMobileExist()" [disabled]="!registerForm.controls.phoneNumber.valid"
            [label]="'register.next' | translate"
            class="primary-btn second-btn" pButton type="button"></button>
            </div>
        </div>
  </ng-container>
</section>

<ng-container *ngIf="isRegisterModal">
  <app-register-user-modal [displayModal]="isRegisterModal"></app-register-user-modal>
</ng-container>
