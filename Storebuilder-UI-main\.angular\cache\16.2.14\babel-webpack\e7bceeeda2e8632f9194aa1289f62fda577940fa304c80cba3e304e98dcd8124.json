{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { PaginatorModule } from \"primeng/paginator\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class OrdersModule {}\nOrdersModule.ɵfac = function OrdersModule_Factory(t) {\n  return new (t || OrdersModule)();\n};\nOrdersModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: OrdersModule\n});\nOrdersModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule.forChild(routes), TranslateModule, PaginatorModule, EmptyScreenComponent, BackButtonComponent]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrdersModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule, PaginatorModule, EmptyScreenComponent, BackButtonComponent]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}