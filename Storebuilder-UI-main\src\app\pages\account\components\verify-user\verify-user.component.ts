import {Component, HostListener, Inject, OnInit, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {MessageService} from 'primeng/api';
import jwt_decode from "jwt-decode";
import {CookieService} from "ngx-cookie-service";
import * as CryptoJS from 'crypto-js';
import {
  LoaderService,
  AuthService,
  RegisterService,
  AuthTokenService,
  StoreService,
  PermissionService
} from "@core/services";
import {isPlatformBrowser} from "@angular/common";
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import {GoogleAnalyticsService } from 'ngx-google-analytics';


@Component({
  selector: 'app-verify-user',
  templateUrl: './verify-user.component.html',
  styleUrls: ['./verify-user.component.scss']
})
export class VerifyUserComponent implements OnInit {

  phoneNumber: string | undefined;
  countryPhoneNumber: string = "";
  countryPhoneCode: string = "";
  phoneLength: number = 12;
  phoneInputLength: number = 12;
  tagName: any = GaLocalActionEnum;
  isGoogleAnalytics:boolean= false
  password: any = '';

  cookieValue: any;
  decoded: any;
  isPrimary: boolean;
  title: string = ''
  screenWidth:any=window.innerWidth;
  isMobileLayout: boolean = false;
  isEmail:boolean = false;
  userEmail:string = '';
  @HostListener('window:resize', ['$event'])

  onResize(event?: any) {
    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth
    }
  }
  constructor(private otpService: RegisterService,
              private translate: TranslateService,
              private messageService: MessageService,
              private router: Router,
              private store: StoreService,
              private auth: AuthService,
              private authTokenService: AuthTokenService,
              private permissionService: PermissionService,
              @Inject(PLATFORM_ID) private platformId: any,
              private $gaService: GoogleAnalyticsService,
              private cookieService: CookieService, private loaderService: LoaderService) {
    let userData: any = localStorage.getItem('profile') ?? '{}';
    userData = JSON.parse(userData);
    if (userData?.mobileNumber) this.phoneNumber = userData.mobileNumber;
    if (userData?.isEmail){
      this.isEmail = userData.isEmail
      this.userEmail = userData.email
    }
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')
    this.isPrimary = JSON.parse(localStorage.getItem("isPrimary") ?? '');
    if(this.isPrimary){
      this.title = this.translate.instant('newUser.verifyUser.confirmChangeDefaultNumber');
    }else{
      this.title = this.translate.instant('newUser.verifyUser.addNewNumber');
    }
  }
  onBack(){
    this.router.navigate(['account/details']);
  }

  triggerEnterPasswordAnalytics(isPasswordCorrect:string ='') {
      this.$gaService.event(
        this.tagName.CLICK_ON_ENTER_PASSWORD,
        '',
        'CONFIRM_PASSWORD_TO_ADD_NEW_NUMBER',
        1,
        true,
        {
          "submission_outcome": isPasswordCorrect
        }
      );
  }

  checkMobileExist() {
    this.store.set("loading", true);
    this.otpService.userPassword = this.password;

    if (this.phoneNumber != null && this.phoneNumber != "" && this.password && this.password !== '') {
      this.auth
        .login({
          username: this.isEmail ? this.userEmail :this.phoneNumber.replace('-', ''),
          password: this.password,
        }).subscribe({
        next: (res: any) => {
          if (res?.success) {
            this.store.set('profile', res.data);
            this.store.set('userPhone', res.data.mobileNumber);
            this.store.set('timeInterval', new Date().getTime());
            let token = res.data.authToken.replace('bearer ', '');
            this.triggerEnterPasswordAnalytics('Pass');
            this.decoded = jwt_decode(token);

            let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(
              0
            );

            const dateNow = new Date();
            dateNow.setDate(dateNow.getDate() + parseInt(days));
            let encryptedMessage = CryptoJS.AES.encrypt(
              token,
              'paysky'
            ).toString();
            localStorage.setItem('auth_enc', encryptedMessage);
            this.cookieService.set('authToken', token, {
              expires: dateNow,
              path: '/',
              sameSite: 'Strict',
            });
            this.cookieValue = this.cookieService.get('authToken');

            this.authTokenService.authTokenSet(this.cookieValue);
            localStorage.removeItem('isGuest');

            this.loaderService.hide();
            if (res?.data?.currency)
              this.store.set('currency', res.data.currency);

            localStorage.setItem('refreshToken', res.data.refreshToken);
            this.store.set('refreshToken', res.data.refreshToken);
            this.router.navigate(['/account/verify-mobile']);
          } else {
            this.triggerEnterPasswordAnalytics('failed');
            this.messageService.add({
              severity: 'error',
              summary: '',
              detail: res.message,
            });
            localStorage.setItem('isGuest', 'true');
          }
        }
      });


    } else {
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('ErrorMessages.fetchError'),
        detail: this.translate.instant('ErrorMessages.mobileRequired')
      });
      this.store.set("loading", false);
    }
  }

  omit_special_char(event: any) {
    let key;
    key = event.charCode;
    return (key > 47 && key < 58);
  }

}
