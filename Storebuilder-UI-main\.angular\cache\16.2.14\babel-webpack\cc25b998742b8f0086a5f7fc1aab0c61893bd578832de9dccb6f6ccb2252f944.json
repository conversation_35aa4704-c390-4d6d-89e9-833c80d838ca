{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-google-analytics\";\nexport class CustomGAService {\n  constructor($gaService) {\n    this.$gaService = $gaService;\n    this.dataSource = new BehaviorSubject(null);\n    this.currentData = this.dataSource.asObservable();\n  }\n  getCartItems(products) {\n    const cartProducts = products.reduce((acc, item, index) => {\n      let product = {\n        \"item_id\": \"\",\n        \"item_name\": \"\",\n        \"discount\": 0,\n        \"index\": 0,\n        \"item_brand\": \"\",\n        \"item_category\": \"\",\n        \"item_category2\": \"\",\n        \"item_category3\": \"\",\n        \"price\": 0,\n        \"quantity\": 0\n      };\n      product.price = item.specProductDetails.salePriceValue || item.price, product.item_id = item.specProductDetails?.skuAutoGenerated || item.specProductDetails.sku, product.index = index, product.item_name = item.productName, product.discount = item.specProductDetails.salePriceValue && item.price - item.specProductDetails.salePriceValue, product.quantity = item.quantity;\n      product.item_category = item.categoryName;\n      acc.cart.push(product);\n      acc.totalPrice += (item.specProductDetails.salePriceValue || product.price) * product.quantity;\n      return acc;\n    }, {\n      cart: [],\n      totalPrice: 0\n    });\n    return cartProducts;\n  }\n  productWithVarienceLog(selectedVariant, productDetails, event) {\n    const price = selectedVariant?.price || productDetails?.specProductDetails?.price;\n    const salePrice = selectedVariant?.salePrice || productDetails?.specProductDetails?.salePrice;\n    const discount = salePrice && price - salePrice;\n    this.$gaService.event(event, 'Products', event, 1, true, {\n      \"currency\": productDetails.currencyCode,\n      \"value\": price || salePrice,\n      \"items\": [{\n        \"item_id\": selectedVariant?.skuAutoGenerated || productDetails?.specProductDetails?.skuAutoGenerated || productDetails.specProductDetails.sku,\n        \"item_name\": productDetails.productName || productDetails.name,\n        \"discount\": discount,\n        \"index\": 0,\n        \"item_brand\": \"\",\n        \"item_category\": productDetails.categoryName,\n        \"item_category2\": \"\",\n        \"item_category3\": \"\",\n        \"price\": price || salePrice,\n        \"quantity\": 1\n      }]\n    });\n  }\n  addToCartEvent(cartProduct, productDetails) {\n    this.$gaService.event(\"add_to_cart\", 'Products', 'add_to_cart', 1, true, {\n      \"currency\": productDetails.currencyCode,\n      \"value\": cartProduct.specProductDetails?.salePrice || cartProduct.specProductDetails?.price || cartProduct.price,\n      \"items\": [{\n        \"item_id\": cartProduct.skuAutoGenerated || cartProduct.sku,\n        \"item_name\": productDetails.name || productDetails.productName,\n        \"discount\": cartProduct.salePrice && cartProduct.price - cartProduct.salePrice,\n        \"index\": 0,\n        \"item_brand\": \"\",\n        \"item_category\": productDetails.categoryName,\n        \"item_category2\": \"\",\n        \"item_category3\": \"\",\n        \"price\": cartProduct.specProductDetails?.salePrice || cartProduct.specProductDetails?.price || cartProduct.price,\n        \"quantity\": 1 // Quantity of items\n      }]\n    });\n  }\n\n  viewItemEvent(selectedVariant, productDetails) {\n    // const brand = productDetails.productAttributeValues?.find(item.)\n    this.productWithVarienceLog(selectedVariant, productDetails, \"view_item\");\n  }\n  addToWishlistEvent(productDetails, selectedVariant) {\n    this.productWithVarienceLog(selectedVariant, productDetails, \"add_to_wishlist\");\n  }\n  removeFromCartEvent(product) {\n    this.$gaService.event(\"remove_from_cart\", 'Products', 'remove_from_cart', 1, true, {\n      \"currency\": product.currencyCode,\n      \"value\": product.specProductDetails.salePrice || product.price,\n      \"items\": [{\n        \"item_id\": product?.specProductDetails?.skuAutoGenerated,\n        \"item_name\": product?.productName,\n        \"discount\": product.specProductDetails.salePriceValue && product.price - product.specProductDetails.salePriceValue,\n        \"index\": 0,\n        \"item_brand\": \"\",\n        \"item_category\": \"\",\n        \"item_category2\": \"\",\n        \"item_category3\": \"\",\n        \"price\": product.price,\n        \"quantity\": product.quantity\n      }]\n    });\n  }\n  viewCartEvent(products) {\n    const cartProducts = this.getCartItems(products);\n    this.$gaService.event(\"view_cart\", 'Products', 'view_cart', 1, true, {\n      \"currency\": cartProducts?.cart[0]?.currencyCode,\n      \"value\": cartProducts?.totalPrice,\n      \"items\": cartProducts?.cart\n    });\n  }\n  beginPurchaseEvent(products) {\n    const cartProducts = this.getCartItems(products);\n    this.$gaService.event(\"begin_checkout\", 'Checkout', 'begin_checkout', 1, true, {\n      \"currency\": cartProducts.cart[0].currencyCode,\n      \"value\": cartProducts.totalPrice,\n      \"items\": cartProducts.cart\n    });\n  }\n  addShippingInfoEvent(products, shippingTier) {\n    const cartProducts = this.getCartItems(products);\n    this.$gaService.event(\"add_shipping_info\", 'Checkout', 'add_shipping_info', 1, true, {\n      \"currency\": cartProducts.cart[0].currencyCode,\n      \"value\": cartProducts.totalPrice,\n      \"shipping_tier\": shippingTier,\n      \"items\": cartProducts.cart\n    });\n  }\n  addPaymentInfoEvent(products, paymentType) {\n    const cartProducts = this.getCartItems(products);\n    this.$gaService.event(\"add_payment_info\", 'Checkout', 'add_payment_info', 1, true, {\n      \"currency\": cartProducts.cart[0].currencyCode,\n      \"value\": cartProducts.totalPrice,\n      \"payment_type\": paymentType,\n      \"items\": cartProducts.cart\n    });\n  }\n  purchaseEvent(orderDetails, transactionId, shipmentFee, currencyCode) {\n    const cartProducts = this.getCartItems(orderDetails);\n    this.$gaService.event(\"purchase\", 'Checkout', 'purchase', 1, true, {\n      \"transaction_id\": transactionId,\n      \"value\": cartProducts.totalPrice,\n      \"shipping\": shipmentFee,\n      \"currency\": currencyCode,\n      // \"coupon\": \"SUMMER_SALE\", // Optional coupon code\n      \"items\": cartProducts.cart\n    });\n  }\n  viewItemListEvent(renderedProduct, itemListName) {\n    let product = {\n      \"item_id\": (renderedProduct?.specProductDetails?.skuAutoGenerated ? renderedProduct?.specProductDetails?.skuAutoGenerated : renderedProduct['skuAutoGenerated']) || renderedProduct.sku || renderedProduct.productId,\n      \"item_name\": renderedProduct.productName ? renderedProduct.productName : renderedProduct.name,\n      \"discount\": 0,\n      \"index\": 0,\n      \"item_brand\": \"\",\n      \"item_category\": renderedProduct.categoryName,\n      \"item_category2\": \"\",\n      \"item_category3\": \"\",\n      \"price\": renderedProduct.specProductDetails?.salePriceValue || renderedProduct.salePriceValue || renderedProduct.price || renderedProduct.priceValue\n    };\n    let products = [];\n    products.push(product);\n    this.$gaService.event(\"view_item_list\", 'Products', 'view_item_list', 1, true, {\n      item_list_id: itemListName,\n      item_list_name: itemListName,\n      items: products\n    });\n  }\n  static #_ = this.ɵfac = function CustomGAService_Factory(t) {\n    return new (t || CustomGAService)(i0.ɵɵinject(i1.GoogleAnalyticsService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomGAService,\n    factory: CustomGAService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "CustomGAService", "constructor", "$gaService", "dataSource", "currentData", "asObservable", "getCartItems", "products", "cartProducts", "reduce", "acc", "item", "index", "product", "price", "specProductDetails", "salePriceValue", "item_id", "skuAutoGenerated", "sku", "item_name", "productName", "discount", "quantity", "item_category", "categoryName", "cart", "push", "totalPrice", "productWithVarienceLog", "<PERSON><PERSON><PERSON><PERSON>", "productDetails", "event", "salePrice", "currencyCode", "name", "addToCartEvent", "cartProduct", "viewItemEvent", "addToWishlistEvent", "removeFromCartEvent", "viewCartEvent", "beginPurchaseEvent", "addShippingInfoEvent", "shippingTier", "addPaymentInfoEvent", "paymentType", "purchaseEvent", "orderDetails", "transactionId", "shipmentFee", "viewItemListEvent", "renderedProduct", "itemListName", "productId", "priceValue", "item_list_id", "item_list_name", "items", "_", "i0", "ɵɵinject", "i1", "GoogleAnalyticsService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\custom-GA.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { GoogleAnalyticsService } from 'ngx-google-analytics';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n\r\n@Injectable({\r\n providedIn: 'root'\r\n})\r\n\r\nexport class CustomGAService {\r\n  private dataSource = new BehaviorSubject<any>(null);\r\n  currentData = this.dataSource.asObservable();\r\n\r\n    constructor(private $gaService:GoogleAnalyticsService){\r\n    \r\n    }\r\n    getCartItems(products:any[]){\r\n      const cartProducts =  products.reduce((acc, item,index)=>{\r\n        let product = {\r\n          \"item_id\": \"\", \r\n          \"item_name\": \"\",\r\n          \"discount\": 0, \r\n          \"index\": 0, \r\n          \"item_brand\": \"\", \r\n          \"item_category\": \"\", \r\n          \"item_category2\": \"\", \r\n          \"item_category3\": \"\", \r\n          \"price\": 0,\r\n          \"quantity\":0\r\n        };\r\n        product.price = item.specProductDetails.salePriceValue || item.price,\r\n        product.item_id = item.specProductDetails?.skuAutoGenerated || item.specProductDetails.sku,\r\n        product.index = index,\r\n        product.item_name = item.productName,\r\n        product.discount = item.specProductDetails.salePriceValue && (item.price -  item.specProductDetails.salePriceValue),\r\n        product.quantity = item.quantity\r\n        product.item_category = item.categoryName\r\n        acc.cart.push(product)\r\n        acc.totalPrice += (item.specProductDetails.salePriceValue || product.price ) * product.quantity;\r\n        return acc\r\n      },{ cart: [], totalPrice: 0 })\r\n      return cartProducts\r\n    }\r\n   productWithVarienceLog(selectedVariant:any,productDetails:any,event:string){\r\n    const  price =  selectedVariant?.price || productDetails?.specProductDetails?.price \r\n    const  salePrice = selectedVariant?.salePrice || productDetails?.specProductDetails?.salePrice\r\n    const  discount = salePrice && (price - salePrice)\r\n      this.$gaService.event(event, 'Products',event,1,true,\r\n      {\r\n          \"currency\": productDetails.currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n          \"value\": price || salePrice, // Total item value after discounts - Mandatory\r\n          \"items\": [\r\n            {\r\n              \"item_id\": selectedVariant?.skuAutoGenerated || productDetails?.specProductDetails?.skuAutoGenerated || productDetails.specProductDetails.sku, // Product SKU - Mandatory\r\n              \"item_name\": productDetails.productName || productDetails.name, // Product Name - Mandatory (corrected spelling)\r\n              \"discount\": discount, // Discount value associated with the item - Optional\r\n              \"index\": 0, // The index/position of the item in a list - Optional\r\n              \"item_brand\": \"\", // Item brand - Optional\r\n              \"item_category\": productDetails.categoryName, // Item category level 1 - Corrected spelling\r\n              \"item_category2\": \"\", // Item category level 2 - Optional\r\n              \"item_category3\": \"\", // Item category level 3 - Optional\r\n              \"price\": price || salePrice, // Unit price of the item - Optional\r\n              \"quantity\":1\r\n            }\r\n          ]\r\n        })\r\n   }\r\n   \r\n  addToCartEvent(cartProduct:any,productDetails:any){ // when customer add product to card \r\n        this.$gaService.event(\"add_to_cart\", 'Products','add_to_cart',1,true,\r\n        {\r\n            \"currency\": productDetails.currencyCode, // Replace with the appropriate currency code\r\n            \"value\":cartProduct.specProductDetails?.salePrice || cartProduct.specProductDetails?.price || cartProduct.price, // Total price after sale * quantity\r\n            \"items\": [\r\n              {\r\n                \"item_id\": cartProduct.skuAutoGenerated || cartProduct.sku, // Product SKU\r\n                \"item_name\": productDetails.name || productDetails.productName, // Product name\r\n                \"discount\": cartProduct.salePrice && (cartProduct.price - cartProduct.salePrice), // Discount value (optional)\r\n                \"index\": 0, // Item position in list (optional)\r\n                \"item_brand\": \"\", // Brand name (optional)\r\n                \"item_category\": productDetails.categoryName, // Item category level 1 (corrected spelling)\r\n                \"item_category2\": \"\", // Item category level 2\r\n                \"item_category3\": \"\", // Item category level 3\r\n                \"price\": cartProduct.specProductDetails?.salePrice || cartProduct.specProductDetails?.price || cartProduct.price, // Unit price\r\n                \"quantity\": 1 // Quantity of items\r\n              }\r\n            ]\r\n          })  \r\n    } \r\n    viewItemEvent(selectedVariant:any,productDetails:any){ // when customer open the product details\r\n      // const brand = productDetails.productAttributeValues?.find(item.)\r\n      this.productWithVarienceLog(selectedVariant,productDetails,\"view_item\")\r\n        \r\n    }\r\n    addToWishlistEvent(productDetails:any,selectedVariant?:any){ // when customer add to wishlist\r\n      this.productWithVarienceLog(selectedVariant,productDetails,\"add_to_wishlist\")\r\n}\r\n\r\nremoveFromCartEvent(product:any){ // Remove from cart \r\n    this.$gaService.event(\"remove_from_cart\", 'Products','remove_from_cart',1,true,\r\n    {\r\n        \"currency\": product.currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n        \"value\": product.specProductDetails.salePrice || product.price, // Total item value after discounts - Mandatory\r\n        \"items\": [\r\n          {\r\n            \"item_id\": product?.specProductDetails?.skuAutoGenerated, // Product SKU - Mandatory\r\n            \"item_name\": product?.productName, // Product Name - Mandatory (corrected spelling)\r\n            \"discount\": product.specProductDetails.salePriceValue && (product.price -  product.specProductDetails.salePriceValue), // Discount value associated with the item - Optional\r\n            \"index\": 0, // The index/position of the item in a list - Optional\r\n            \"item_brand\": \"\", // Item brand - Optional\r\n            \"item_category\": \"\", // Item category level 1 - Corrected spelling\r\n            \"item_category2\": \"\", // Item category level 2 - Optional\r\n            \"item_category3\": \"\", // Item category level 3 - Optional\r\n            \"price\": product.price, // Unit price of the item - Optional\r\n            \"quantity\":product.quantity\r\n          }\r\n        ]\r\n      })\r\n}\r\nviewCartEvent(products:any[]){ // View Cart \r\n const cartProducts = this.getCartItems(products)\r\n    this.$gaService.event(\"view_cart\", 'Products','view_cart',1,true,\r\n    {\r\n        \"currency\":cartProducts?.cart[0]?.currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n        \"value\": cartProducts?.totalPrice, // Total item value after discounts - Mandatory\r\n        \"items\":cartProducts?.cart\r\n      })\r\n}\r\n\r\nbeginPurchaseEvent (products:any[]){ // Begin Purchase \r\n  const cartProducts = this.getCartItems(products)\r\n    this.$gaService.event(\"begin_checkout\", 'Checkout','begin_checkout',1,true,\r\n    {\r\n      \"currency\":cartProducts.cart[0].currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n      \"value\": cartProducts.totalPrice, // Total item value after discounts - Mandatory\r\n      \"items\":cartProducts.cart\r\n    })\r\n}\r\naddShippingInfoEvent (products:any[],shippingTier:string){ // Add shipping info  \r\n    const cartProducts = this.getCartItems(products)\r\n    this.$gaService.event(\"add_shipping_info\", 'Checkout','add_shipping_info',1,true,\r\n    {\r\n      \"currency\":cartProducts.cart[0].currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n      \"value\": cartProducts.totalPrice, // Total item value after discounts - Mandatory\r\n      \"shipping_tier\":shippingTier,\r\n      \"items\":cartProducts.cart\r\n    })\r\n}\r\naddPaymentInfoEvent (products:any[],paymentType:string){ // Add shipping info  \r\n  const cartProducts = this.getCartItems(products)\r\n  this.$gaService.event(\"add_payment_info\", 'Checkout','add_payment_info',1,true,\r\n  {\r\n    \"currency\":cartProducts.cart[0].currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n    \"value\": cartProducts.totalPrice, // Total item value after discounts - Mandatory\r\n    \"payment_type\":paymentType,\r\n    \"items\":cartProducts.cart\r\n  })\r\n}\r\npurchaseEvent (orderDetails:any,transactionId:string,shipmentFee:number,currencyCode:string){ // Purchase\r\n const cartProducts = this.getCartItems(orderDetails)\r\n\r\n    this.$gaService.event(\"purchase\", 'Checkout','purchase',1,true,\r\n    {\r\n        \"transaction_id\": transactionId, // Transaction ID - Mandatory\r\n        \"value\":cartProducts.totalPrice, // Sum of (price * quantity) for all items\r\n        \"shipping\": shipmentFee, // Shipping cost - Mandatory\r\n        \"currency\": currencyCode, // Currency code (ISO 4217 standard) - Mandatory\r\n        // \"coupon\": \"SUMMER_SALE\", // Optional coupon code\r\n        \"items\": cartProducts.cart\r\n      }\r\n      )\r\n}\r\n\r\nviewItemListEvent(renderedProduct:any,itemListName:string) { //View item list event: when customer finds any product list like home, search results, or categories’ products list. \r\n  let product = {\r\n    \"item_id\": (renderedProduct?.specProductDetails?.skuAutoGenerated ?  renderedProduct?.specProductDetails?.skuAutoGenerated: renderedProduct['skuAutoGenerated']) || renderedProduct.sku || renderedProduct.productId, \r\n    \"item_name\": renderedProduct.productName ? renderedProduct.productName : renderedProduct.name ,\r\n    \"discount\": 0, \r\n    \"index\": 0, \r\n    \"item_brand\": \"\", \r\n    \"item_category\": renderedProduct.categoryName, \r\n    \"item_category2\": \"\", \r\n    \"item_category3\": \"\", \r\n    \"price\": renderedProduct.specProductDetails?.salePriceValue || renderedProduct.salePriceValue || renderedProduct.price || renderedProduct.priceValue,\r\n  };\r\n  let products =[]\r\n  products.push(product)\r\n  this.$gaService.event(\"view_item_list\", 'Products','view_item_list',1,true,\r\n  {\r\n     item_list_id: itemListName, // The ID of the list in which the item was presented to the user. \r\n     item_list_name: itemListName, // The name of the list in which the item was presented to the user. \r\n     items: products\r\n}); \r\n\r\n \r\n}\r\n              \r\n}"], "mappings": "AAEA,SAASA,eAAe,QAAQ,MAAM;;;AAOtC,OAAM,MAAOC,eAAe;EAIxBC,YAAoBC,UAAiC;IAAjC,KAAAA,UAAU,GAAVA,UAAU;IAHxB,KAAAC,UAAU,GAAG,IAAIJ,eAAe,CAAM,IAAI,CAAC;IACnD,KAAAK,WAAW,GAAG,IAAI,CAACD,UAAU,CAACE,YAAY,EAAE;EAI1C;EACAC,YAAYA,CAACC,QAAc;IACzB,MAAMC,YAAY,GAAID,QAAQ,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,EAACC,KAAK,KAAG;MACvD,IAAIC,OAAO,GAAG;QACZ,SAAS,EAAE,EAAE;QACb,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,CAAC;QACb,OAAO,EAAE,CAAC;QACV,YAAY,EAAE,EAAE;QAChB,eAAe,EAAE,EAAE;QACnB,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,OAAO,EAAE,CAAC;QACV,UAAU,EAAC;OACZ;MACDA,OAAO,CAACC,KAAK,GAAGH,IAAI,CAACI,kBAAkB,CAACC,cAAc,IAAIL,IAAI,CAACG,KAAK,EACpED,OAAO,CAACI,OAAO,GAAGN,IAAI,CAACI,kBAAkB,EAAEG,gBAAgB,IAAIP,IAAI,CAACI,kBAAkB,CAACI,GAAG,EAC1FN,OAAO,CAACD,KAAK,GAAGA,KAAK,EACrBC,OAAO,CAACO,SAAS,GAAGT,IAAI,CAACU,WAAW,EACpCR,OAAO,CAACS,QAAQ,GAAGX,IAAI,CAACI,kBAAkB,CAACC,cAAc,IAAKL,IAAI,CAACG,KAAK,GAAIH,IAAI,CAACI,kBAAkB,CAACC,cAAe,EACnHH,OAAO,CAACU,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;MAChCV,OAAO,CAACW,aAAa,GAAGb,IAAI,CAACc,YAAY;MACzCf,GAAG,CAACgB,IAAI,CAACC,IAAI,CAACd,OAAO,CAAC;MACtBH,GAAG,CAACkB,UAAU,IAAI,CAACjB,IAAI,CAACI,kBAAkB,CAACC,cAAc,IAAIH,OAAO,CAACC,KAAK,IAAKD,OAAO,CAACU,QAAQ;MAC/F,OAAOb,GAAG;IACZ,CAAC,EAAC;MAAEgB,IAAI,EAAE,EAAE;MAAEE,UAAU,EAAE;IAAC,CAAE,CAAC;IAC9B,OAAOpB,YAAY;EACrB;EACDqB,sBAAsBA,CAACC,eAAmB,EAACC,cAAkB,EAACC,KAAY;IACzE,MAAOlB,KAAK,GAAIgB,eAAe,EAAEhB,KAAK,IAAIiB,cAAc,EAAEhB,kBAAkB,EAAED,KAAK;IACnF,MAAOmB,SAAS,GAAGH,eAAe,EAAEG,SAAS,IAAIF,cAAc,EAAEhB,kBAAkB,EAAEkB,SAAS;IAC9F,MAAOX,QAAQ,GAAGW,SAAS,IAAKnB,KAAK,GAAGmB,SAAU;IAChD,IAAI,CAAC/B,UAAU,CAAC8B,KAAK,CAACA,KAAK,EAAE,UAAU,EAACA,KAAK,EAAC,CAAC,EAAC,IAAI,EACpD;MACI,UAAU,EAAED,cAAc,CAACG,YAAY;MACvC,OAAO,EAAEpB,KAAK,IAAImB,SAAS;MAC3B,OAAO,EAAE,CACP;QACE,SAAS,EAAEH,eAAe,EAAEZ,gBAAgB,IAAIa,cAAc,EAAEhB,kBAAkB,EAAEG,gBAAgB,IAAIa,cAAc,CAAChB,kBAAkB,CAACI,GAAG;QAC7I,WAAW,EAAEY,cAAc,CAACV,WAAW,IAAIU,cAAc,CAACI,IAAI;QAC9D,UAAU,EAAEb,QAAQ;QACpB,OAAO,EAAE,CAAC;QACV,YAAY,EAAE,EAAE;QAChB,eAAe,EAAES,cAAc,CAACN,YAAY;QAC5C,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,OAAO,EAAEX,KAAK,IAAImB,SAAS;QAC3B,UAAU,EAAC;OACZ;KAEJ,CAAC;EACP;EAEDG,cAAcA,CAACC,WAAe,EAACN,cAAkB;IAC3C,IAAI,CAAC7B,UAAU,CAAC8B,KAAK,CAAC,aAAa,EAAE,UAAU,EAAC,aAAa,EAAC,CAAC,EAAC,IAAI,EACpE;MACI,UAAU,EAAED,cAAc,CAACG,YAAY;MACvC,OAAO,EAACG,WAAW,CAACtB,kBAAkB,EAAEkB,SAAS,IAAII,WAAW,CAACtB,kBAAkB,EAAED,KAAK,IAAIuB,WAAW,CAACvB,KAAK;MAC/G,OAAO,EAAE,CACP;QACE,SAAS,EAAEuB,WAAW,CAACnB,gBAAgB,IAAImB,WAAW,CAAClB,GAAG;QAC1D,WAAW,EAAEY,cAAc,CAACI,IAAI,IAAIJ,cAAc,CAACV,WAAW;QAC9D,UAAU,EAAEgB,WAAW,CAACJ,SAAS,IAAKI,WAAW,CAACvB,KAAK,GAAGuB,WAAW,CAACJ,SAAU;QAChF,OAAO,EAAE,CAAC;QACV,YAAY,EAAE,EAAE;QAChB,eAAe,EAAEF,cAAc,CAACN,YAAY;QAC5C,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,OAAO,EAAEY,WAAW,CAACtB,kBAAkB,EAAEkB,SAAS,IAAII,WAAW,CAACtB,kBAAkB,EAAED,KAAK,IAAIuB,WAAW,CAACvB,KAAK;QAChH,UAAU,EAAE,CAAC,CAAC;OACf;KAEJ,CAAC;EACR;;EACAwB,aAAaA,CAACR,eAAmB,EAACC,cAAkB;IAClD;IACA,IAAI,CAACF,sBAAsB,CAACC,eAAe,EAACC,cAAc,EAAC,WAAW,CAAC;EAEzE;EACAQ,kBAAkBA,CAACR,cAAkB,EAACD,eAAoB;IACxD,IAAI,CAACD,sBAAsB,CAACC,eAAe,EAACC,cAAc,EAAC,iBAAiB,CAAC;EACnF;EAEAS,mBAAmBA,CAAC3B,OAAW;IAC3B,IAAI,CAACX,UAAU,CAAC8B,KAAK,CAAC,kBAAkB,EAAE,UAAU,EAAC,kBAAkB,EAAC,CAAC,EAAC,IAAI,EAC9E;MACI,UAAU,EAAEnB,OAAO,CAACqB,YAAY;MAChC,OAAO,EAAErB,OAAO,CAACE,kBAAkB,CAACkB,SAAS,IAAIpB,OAAO,CAACC,KAAK;MAC9D,OAAO,EAAE,CACP;QACE,SAAS,EAAED,OAAO,EAAEE,kBAAkB,EAAEG,gBAAgB;QACxD,WAAW,EAAEL,OAAO,EAAEQ,WAAW;QACjC,UAAU,EAAER,OAAO,CAACE,kBAAkB,CAACC,cAAc,IAAKH,OAAO,CAACC,KAAK,GAAID,OAAO,CAACE,kBAAkB,CAACC,cAAe;QACrH,OAAO,EAAE,CAAC;QACV,YAAY,EAAE,EAAE;QAChB,eAAe,EAAE,EAAE;QACnB,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,OAAO,EAAEH,OAAO,CAACC,KAAK;QACtB,UAAU,EAACD,OAAO,CAACU;OACpB;KAEJ,CAAC;EACR;EACAkB,aAAaA,CAAClC,QAAc;IAC3B,MAAMC,YAAY,GAAG,IAAI,CAACF,YAAY,CAACC,QAAQ,CAAC;IAC7C,IAAI,CAACL,UAAU,CAAC8B,KAAK,CAAC,WAAW,EAAE,UAAU,EAAC,WAAW,EAAC,CAAC,EAAC,IAAI,EAChE;MACI,UAAU,EAACxB,YAAY,EAAEkB,IAAI,CAAC,CAAC,CAAC,EAAEQ,YAAY;MAC9C,OAAO,EAAE1B,YAAY,EAAEoB,UAAU;MACjC,OAAO,EAACpB,YAAY,EAAEkB;KACvB,CAAC;EACR;EAEAgB,kBAAkBA,CAAEnC,QAAc;IAChC,MAAMC,YAAY,GAAG,IAAI,CAACF,YAAY,CAACC,QAAQ,CAAC;IAC9C,IAAI,CAACL,UAAU,CAAC8B,KAAK,CAAC,gBAAgB,EAAE,UAAU,EAAC,gBAAgB,EAAC,CAAC,EAAC,IAAI,EAC1E;MACE,UAAU,EAACxB,YAAY,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACQ,YAAY;MAC5C,OAAO,EAAE1B,YAAY,CAACoB,UAAU;MAChC,OAAO,EAACpB,YAAY,CAACkB;KACtB,CAAC;EACN;EACAiB,oBAAoBA,CAAEpC,QAAc,EAACqC,YAAmB;IACpD,MAAMpC,YAAY,GAAG,IAAI,CAACF,YAAY,CAACC,QAAQ,CAAC;IAChD,IAAI,CAACL,UAAU,CAAC8B,KAAK,CAAC,mBAAmB,EAAE,UAAU,EAAC,mBAAmB,EAAC,CAAC,EAAC,IAAI,EAChF;MACE,UAAU,EAACxB,YAAY,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACQ,YAAY;MAC5C,OAAO,EAAE1B,YAAY,CAACoB,UAAU;MAChC,eAAe,EAACgB,YAAY;MAC5B,OAAO,EAACpC,YAAY,CAACkB;KACtB,CAAC;EACN;EACAmB,mBAAmBA,CAAEtC,QAAc,EAACuC,WAAkB;IACpD,MAAMtC,YAAY,GAAG,IAAI,CAACF,YAAY,CAACC,QAAQ,CAAC;IAChD,IAAI,CAACL,UAAU,CAAC8B,KAAK,CAAC,kBAAkB,EAAE,UAAU,EAAC,kBAAkB,EAAC,CAAC,EAAC,IAAI,EAC9E;MACE,UAAU,EAACxB,YAAY,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACQ,YAAY;MAC5C,OAAO,EAAE1B,YAAY,CAACoB,UAAU;MAChC,cAAc,EAACkB,WAAW;MAC1B,OAAO,EAACtC,YAAY,CAACkB;KACtB,CAAC;EACJ;EACAqB,aAAaA,CAAEC,YAAgB,EAACC,aAAoB,EAACC,WAAkB,EAAChB,YAAmB;IAC1F,MAAM1B,YAAY,GAAG,IAAI,CAACF,YAAY,CAAC0C,YAAY,CAAC;IAEjD,IAAI,CAAC9C,UAAU,CAAC8B,KAAK,CAAC,UAAU,EAAE,UAAU,EAAC,UAAU,EAAC,CAAC,EAAC,IAAI,EAC9D;MACI,gBAAgB,EAAEiB,aAAa;MAC/B,OAAO,EAACzC,YAAY,CAACoB,UAAU;MAC/B,UAAU,EAAEsB,WAAW;MACvB,UAAU,EAAEhB,YAAY;MACxB;MACA,OAAO,EAAE1B,YAAY,CAACkB;KACvB,CACA;EACP;EAEAyB,iBAAiBA,CAACC,eAAmB,EAACC,YAAmB;IACvD,IAAIxC,OAAO,GAAG;MACZ,SAAS,EAAE,CAACuC,eAAe,EAAErC,kBAAkB,EAAEG,gBAAgB,GAAIkC,eAAe,EAAErC,kBAAkB,EAAEG,gBAAgB,GAAEkC,eAAe,CAAC,kBAAkB,CAAC,KAAKA,eAAe,CAACjC,GAAG,IAAIiC,eAAe,CAACE,SAAS;MACpN,WAAW,EAAEF,eAAe,CAAC/B,WAAW,GAAG+B,eAAe,CAAC/B,WAAW,GAAG+B,eAAe,CAACjB,IAAI;MAC7F,UAAU,EAAE,CAAC;MACb,OAAO,EAAE,CAAC;MACV,YAAY,EAAE,EAAE;MAChB,eAAe,EAAEiB,eAAe,CAAC3B,YAAY;MAC7C,gBAAgB,EAAE,EAAE;MACpB,gBAAgB,EAAE,EAAE;MACpB,OAAO,EAAE2B,eAAe,CAACrC,kBAAkB,EAAEC,cAAc,IAAIoC,eAAe,CAACpC,cAAc,IAAIoC,eAAe,CAACtC,KAAK,IAAIsC,eAAe,CAACG;KAC3I;IACD,IAAIhD,QAAQ,GAAE,EAAE;IAChBA,QAAQ,CAACoB,IAAI,CAACd,OAAO,CAAC;IACtB,IAAI,CAACX,UAAU,CAAC8B,KAAK,CAAC,gBAAgB,EAAE,UAAU,EAAC,gBAAgB,EAAC,CAAC,EAAC,IAAI,EAC1E;MACGwB,YAAY,EAAEH,YAAY;MAC1BI,cAAc,EAAEJ,YAAY;MAC5BK,KAAK,EAAEnD;KACX,CAAC;EAGF;EAAC,QAAAoD,CAAA,G;qBA1LY3D,eAAe,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfhE,eAAe;IAAAiE,OAAA,EAAfjE,eAAe,CAAAkE,IAAA;IAAAC,UAAA,EAHf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}