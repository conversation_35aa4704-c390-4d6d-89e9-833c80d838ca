{"ast": null, "code": "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n  value = typeof XMLHttpRequest !== 'undefined' && 'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n}\nexport const hasCORS = value;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}