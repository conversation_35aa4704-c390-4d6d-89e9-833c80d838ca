import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { CountryISO } from '../../models/CountryISO.enum';
import { GaActionEnum, GoogleAnalyticsService } from 'ngx-google-analytics';
import { AppDataService, AuthService, AuthTokenService, CartService, LoaderService, MainDataService, PermissionService, StoreService, UserService, CustomGAService } from '@core/services';
import { MessageService } from 'primeng/api';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { GTMService } from '@core/services/gtm.service';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { TenantRecords, UserConsent } from '@core/interface';
import { UserConsentType } from '../../models/UserConsentType.enum';
import jwt_decode from 'jwt-decode';
import { GuidGenerator } from '../../utils/guid-generator';
import { BackButtonComponent } from "../../../../shared/components/back-button/back-button.component";
import { PhoneInputComponent } from "../phone-input/phone-input.component";
import { SignInUpHeaderComponent } from "../../../../shared/components/sign-in-up-header/sign-in-up-header.component";
import { PasswordInputComponent } from "../password-input/password-input.component";
import { ButtonModule } from 'primeng/button';
import * as CryptoJS from 'crypto-js';
import {EmailInputComponent} from "../email-input/email-input.component";

@Component({
  selector: 'app-sign-in',
  standalone: true,
  imports: [CommonModule, BackButtonComponent,
    RouterModule, ButtonModule,
    PhoneInputComponent, SignInUpHeaderComponent, PasswordInputComponent, TranslateModule, EmailInputComponent],
  templateUrl: './sign-in.component.html',
  styleUrls: ['./sign-in.component.scss']
})
export class SignInComponent {

  phoneNumber: any = null;
  password: string = '';
  isPhoneValid: boolean = false;
  isPasswordValid: boolean = false;

  phoneInputLength: number = 12;
  customCountryISO: any;
  customPlaceHolder: string = '';
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];

  submitted: boolean = false;
  redirctURL: any;
  cartListCount: any = 0;
  cartListData: any = [];
  isGoogleAnalytics: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth: number;
  tagName: any = GaActionEnum;
  products: Array<any> = [];
  activeTab: string = 'mobileNumber';
  email: string | null = null;
  isEmailValid: boolean = false;
  counter: any = 0;
  nationalNumber: string;

constructor(
  private store: StoreService,
    private auth: AuthService,
    private messageService: MessageService,
    private router: Router,
    private cartService: CartService,
    private translate: TranslateService,
    private cookieService: CookieService,
    private authTokenService: AuthTokenService,
    private route: ActivatedRoute,
    private mainDataService: MainDataService,
    private permissionService: PermissionService,
    private loaderService: LoaderService,
    private appDataService: AppDataService,
    private $gaService: GoogleAnalyticsService,
    private userService: UserService,
    private customGAService: CustomGAService,
    @Inject(PLATFORM_ID) private platformId: any,
    private $gtmService: GTMService,
) {
   this.initializeComponent();
}

  ngOnInit(): void {
    this.setupPermissions();
    this.setupCountryISO();
    this.setupRouteParams();
    this.setupPhoneInputLength();
    this.$gtmService.pushPageView('signIn');
  }

  private initializeComponent() {
    this.setupCustomPlaceholder();

    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth;
    }
  }

  private setupPermissions() {
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');
  }

  private setupCountryISO() {
    if (!localStorage.getItem("isoCode")) {
      const tenants = this.appDataService.tenants;
      if (tenants.records != undefined) {
        let tenantId = localStorage.getItem('tenantId');
        let data = tenants.records;
        let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();
        localStorage.setItem('isoCode', arr?.isoCode);
        this.store.set('allCountryTenants', tenants.records);
      }
    } else {
      this.customCountryISO = localStorage.getItem("isoCode");
    }
  }

  private setupRouteParams() {
    this.route.queryParams.subscribe((params) => {
      this.redirctURL = params.returnUrl;
    });
  }

  private setupPhoneInputLength() {
    if (this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
    }
  }

  onRegisterClick() {
    if (this.isGoogleAnalytics) {
      this.customGAService.signupCtaClickEvent('signin_page');
    }
    this.router.navigate(['/register']);
  }

  private setupCustomPlaceholder() {
    let tenantId = localStorage.getItem('tenantId');
    if (tenantId && tenantId !== '') {
      const placeholders: { [key: string]: string } = {
        '1': 'XXXXXXXXXX',
        '2': 'XXXXXXXXX',
        '3': 'XXXXXXXXX',
        '4': 'XXXXXXXXXX'
      };
      this.customPlaceHolder = placeholders[tenantId] || '';
    }
  }

  get isFormValid(): boolean {
    return (this.isPhoneValid || this.isEmailValid) && this.isPasswordValid && this.password.length > 0;
  }

  onPhoneNumberChange(phoneNumber: any) {
      let tenantId = localStorage.getItem('tenantId');
      let firstChar = phoneNumber?.number?.charAt(0)
      let countryCode = phoneNumber?.countryCode
      if( countryCode == 'GH'){
        // if(firstChar == '0'){
        //   this.counter++
        // }
        // if(phoneNumber?.number?.length == 1){
            // this.messageService.add({
            //   severity: 'info',
            //   summary: "Info",
            //   detail: 'Phone number must be 9 digits or start with 0 followed by 9 digits.',
            //   life:4000
            // });
        // }
      // if(firstChar == '0' || this.counter > 2 || this.counter > 5 || this.counter > 7 && tenantId == '2' ){
      //   this.phoneInputLength=10
      //       if(phoneNumber?.number?.length <2){
      //         this.counter = 0
      //       }
      // }else{
          if (this.appDataService.configuration) {
            const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
            if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
          }
      // }
    }else if(countryCode == 'UG'){
       if (this.appDataService.configuration) {
              const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
              if (phoneLength) {
                this.phoneInputLength = parseInt(phoneLength.value) - (this.nationalNumber[0] === '0' ? 0 : 1);
              }
            }
      
    }
    this.phoneNumber = phoneNumber;
    // if(countryCode == 'UG' && this.nationalNumber.length == 10 && this.nationalNumber[0] == '0'){
    //   this.phoneNumber.e164Number =this.addTrunkPrefixToE164(phoneNumber?.e164Number,phoneNumber?.dialCode)
    //   console.log( this.phoneNumber," this.phoneNumber");
      
    // }
  }
  //  addTrunkPrefixToE164(e164Number: string, dialCode: string) {
  //   const cleanE164 = e164Number.replace(/\s+/g, '');
  //   const cleanDialCode = dialCode.replace(/\s+/g, '');
    
  //   return cleanE164.startsWith(cleanDialCode)
  //     ? `${cleanDialCode}0${cleanE164.slice(cleanDialCode.length)}`
  //     : cleanE164;
  // };
   onRawPhoneNumberChange($event: string) {
    this.nationalNumber = $event
    }
  onPhoneValidationChange(isValid: boolean) {
    this.isPhoneValid = isValid;
  }

  onPasswordChange(password: string) {
    this.password = password;
  }

  onPasswordValidationChange(isValid: boolean) {
    this.isPasswordValid = isValid;
  }

  async login() {
    if (this.isGoogleAnalytics) {
      this.$gaService.event(GaLocalActionEnum.CLICK_ON_CONTINUE_FOR_SIGN_IN, '', 'SIGN_IN_STEP2', 1, true);
    }

    this.loaderService.show();
    this.submitted = true;

    this.auth.login({
      username: this.phoneNumber ? this.phoneNumber.e164Number.slice(1) : this.email,
      password: this.password,
    }).subscribe({
      next: async (res: any) => {
        await this.handleLoginSuccess(res);
      },
      error: (err: any) => {
        this.handleLoginError(err);
      },
    });
  }

  private async handleLoginSuccess(res: any) {
    if (res?.success && res.data.role == 'consumer') {
      if (this.isGoogleAnalytics && this.permissionService.getTagFeature('LOGIN')) {
        this.$gaService.event(this.tagName.LOGIN, '', '', 1, true, { "user_ID": res.data.mobileNumber });
      }

      await this.updateUserConsent(res.data.id);
      this.setUserData(res.data);
      this.setAuthToken(res.data.authToken);
      this.loaderService.hide();

      if (res?.data?.currency) {
        this.store.set('currency', res.data.currency);
      }

      localStorage.setItem('refreshToken', res.data.refreshToken);
      this.store.set('refreshToken', res.data.refreshToken);

      const cartData: any = { sessionId: localStorage.getItem('sessionId') };
      const cartId = localStorage.getItem('cartId');
      await this.checkCart(cartData, cartId);

      this.handlePostLoginNavigation(res.data);
    } else {
      this.handleLoginFailure(res?.message);
    }
  }

  private async updateUserConsent(userId: string) {
    const data: UserConsent = {
      consentType: UserConsentType.Cookie,
      sessionId: localStorage.getItem('consumer-consent-sessionId') || '',
      consent: true,
      userId: userId
    };
    this.userService.updateUserConsent(data).subscribe({
      next: (res: any) => { }
    });
  }

  private setUserData(userData: any) {
    this.mainDataService.setUserData(userData);
    this.store.set('profile', userData);
    this.store.set('userPhone', userData.mobileNumber);
    localStorage.setItem('userId', userData.id);
    this.store.set('timeInterval', new Date().getTime());
  }

  private setAuthToken(authToken: string) {
    let token = authToken.replace('bearer ', '');
    const decoded = jwt_decode(token);
    let days: any = ((decoded as any).exp / (60 * 60 * 24 * 1000)).toFixed(0);

    localStorage.removeItem('visited');
    const dateNow = new Date();
    dateNow.setDate(dateNow.getDate() + parseInt(days));

    let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();
    localStorage.setItem('auth_enc', encryptedMessage);

    this.cookieService.set('authToken', token, {
      expires: dateNow,
      path: '/',
      sameSite: 'Strict',
    });

    localStorage.removeItem('isGuest');
    this.authTokenService.authTokenSet(token);
  }

  private handlePostLoginNavigation(userData: any) {
    if (userData.isPasswodExpired) {
      this.router.navigateByUrl('/change-password');
      this.messageService.add({
        severity: 'info',
        summary: this.translate.instant('ResponseMessages.changePassword'),
        detail: this.translate.instant('ResponseMessages.passwordExpirationChange'),
      });
    } else {
      if (this.redirctURL) {
        this.router.navigate([this.redirctURL]);
        this.redirctURL = null;
      } else {
        this.router.navigate(['/']);
      }
      this.messageService.add({
        severity: 'success',
        summary: this.translate.instant('ResponseMessages.login'),
        detail: this.translate.instant('ResponseMessages.loggedInSuccessfully'),
      });
    }
  }

  private handleLoginError(err: any) {
    this.store.set('profile', '');
    this.loaderService.hide();
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: err.message,
    });
    localStorage.setItem('isGuest', 'true');
  }

  private handleLoginFailure(message?: string) {
    this.store.set('profile', '');
    this.loaderService.hide();
    this.messageService.add({
      severity: 'error',
      summary: message || this.translate.instant('ErrorMessages.invalidUserNameOrPassword'),
    });
    localStorage.setItem('isGuest', 'true');
  }

  resetPassword() {
    if (this.isGoogleAnalytics) {
      this.$gaService.event(GaLocalActionEnum.CLICK_ON_FORGOT_PASSWORD, '', 'FORGOT_PASSWORD', 1, true);
    }
    this.router.navigate(['/reset-password']);
  }

  reloadCurrentPage(pageId: number, title: string) {
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
      this.router.navigate(['/about-us/'], {
        queryParams: { pageId: pageId, title: title },
      })
    );
  }

getAllCart(data: any): void {
    this.products = [];
    let cartData: any = {
      sessionId: data.sessionId,
    };
    let applyTo = localStorage.getItem('apply-to');
    if (applyTo && applyTo != '') {
      cartData['applyTo'] = applyTo
    }
    this.cartService.getCart(cartData)
      .subscribe({
        next: (res: any) => {
          this.cartListCount = 0;
          this.cartListData = [];
          if (res.data?.records?.length) {
            this.cartListCount = 0;
            if (res.data.records[0].cartDetails.length) {
              this.cartListCount = res.data.records[0].cartDetails.length;
              this.cartListData = res.data.records[0].cartDetails;

            }
            if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {
              this.cartListCount += res.data.records[0].cartDetailsDPay.length;
              this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay)
            }
            this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData);
            this.mainDataService.setCartLenghtData(this.cartListCount);
            this.mainDataService.setCartItemsData(this.cartListData);

          } else {
            this.mainDataService.setCartLenghtData(0);
            this.mainDataService.setCartItemsData([]);
          }
        }
      });
  }

  public compareCartProducts(products: [], storeProducts: []) {
    if (products.length) {

      products.forEach((item: any) => {
        storeProducts.forEach((data: any) => {
          if (item.specsProductId === data.specsProductId) {
            this.products.push(item);
          }
        });
      });
    } else {
      this.products = storeProducts;
    }
    this.store.set('cartProducts', this.products);
    localStorage.setItem('addedProducts', JSON.stringify(this.products));
  }

  getShipmentMethodByTenantId(data: any) {
    if (this.permissionService.hasPermission('Shipment-Fee')) {
      this.cartService.getShipmentMethodByTenantId().subscribe((res: any) => {
        if (res.success && res.data.length) {
          // this.applyTo = res.data[0].applyTo
          localStorage.setItem('apply-to', res.data[0].applyTo);
          this.getAllCart(data);
        }
      })
    } else {
      localStorage.setItem('apply-to', '2');
      this.getAllCart(data);
    }

  }

  checkCart(cartData: any, cartId: any) {
    return new Promise<void>((resolve, reject) => {
      if (!cartData.sessionId) {
        localStorage.setItem('sessionId', GuidGenerator.newGuid());
        cartData.sessionId = localStorage.getItem('sessionId');
        this.getAllCart(cartData);
        resolve(); // Resolve the promise
      } else {
        if (cartId && cartId != '') {
          cartData.cartId = parseInt(cartId);
        } else {
          cartData.cartId = 0;
        }
        this.cartService.updateCart(cartData)
          .subscribe({
            next: (res: any) => {
              if (res?.data?.cartItems?.length) {
                this.cartListData = res.data.cartItems;
                this.cartListCount = res.data.cartItems.length;
              }
              this.mainDataService.setCartLenghtData(this.cartListCount);
              this.mainDataService.setCartItemsData(this.cartListData);
              this.getShipmentMethodByTenantId(cartData);
              resolve(); // Resolve the promise
            },
            error: (err: any) => {
              this.cartListCount = 0;
              this.cartListData = [];
              this.mainDataService.setCartLenghtData(this.cartListCount);
              this.mainDataService.setCartItemsData(this.cartListData);
              this.getShipmentMethodByTenantId(cartData);
              reject(err);
            }
          });
      }
    });
  }

  get forgotPasswordClass(): string {
    return 'font-size-12';
  }

  get floatingLabelEnabled(): boolean {
    return false;
  }

  get buttonLabel(): string {
    return 'signIn.continue';
    }


  switchTab(tab: string) {
    this.activeTab = tab;
    if (tab === 'mobileNumber') {
      this.email = null;
      this.isEmailValid = false
    }
    if (tab === 'email') {
      this.phoneNumber = null;
      this.isPhoneValid = false
    }
  }

  get labelColor(): string {
    return this.screenWidth <= 767 ? 'grey' : 'white';
  }

  onEmailChange(value: string | null) {
    this.email = value;
  }

  onEmailValidationChange(isValid: boolean) {
    this.isEmailValid = isValid;
  }

  // Track signup CTA click
  onSignupCtaClick(ctaLocation: string) {
    if (this.isGoogleAnalytics) {
      this.customGAService.signupCtaClickEvent(ctaLocation);
    }
  }
}
