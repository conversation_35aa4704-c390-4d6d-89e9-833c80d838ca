{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport { take } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static #_ = this.ɵfac = function FocusTrapManager_Factory(t) {\n    return new (t || FocusTrapManager)();\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  constructor(_platformId) {\n    this._platformId = _platformId;\n    // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n    /** Whether the Angular application is being rendered in the browser. */\n    this.isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n    /** Whether the current browser is Microsoft Edge. */\n    this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n    /** Whether the current rendering engine is Microsoft Trident. */\n    this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n    // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n    /** Whether the current rendering engine is Blink. */\n    this.BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n    // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n    /** Whether the current rendering engine is WebKit. */\n    this.WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n    /** Whether the current platform is Apple iOS. */\n    this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n    // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n    /** Whether the current browser is Firefox. */\n    this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n    this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n    // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n    /** Whether the current browser is Safari. */\n    this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  }\n  static #_ = this.ɵfac = function Platform_Factory(t) {\n    return new (t || Platform)(i0.ɵɵinject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static #_ = this.ɵfac = function InteractivityChecker_Factory(t) {\n    return new (t || InteractivityChecker)(i0.ɵɵinject(Platform));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Platform\n    }];\n  }, null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  // IE11 parses tabindex=\"\" as the value \"-32768\"\n  if (tabIndex == '-32768') {\n    return false;\n  }\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to misalign.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n    this._enabled = true;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      if (startAnchor.parentNode) {\n        startAnchor.parentNode.removeChild(startAnchor);\n      }\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      if (endAnchor.parentNode) {\n        endAnchor.parentNode.removeChild(endAnchor);\n      }\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfuly. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then either focuses the first element that the\n   * user specified, or the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement()));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady() {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    for (let i = 0; i < markers.length; i++) {\n      // @breaking-change 8.0.0\n      if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n      } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement() {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if (redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus();\n        return !!focusableChild;\n      }\n      redirectToElement.focus();\n      return true;\n    }\n    return this.focusFirstTabbableElement();\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement() {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement() {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus();\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n    // back to `childNodes` which includes text nodes, comments etc.\n    let children = root.children || root.childNodes;\n    for (let i = 0; i < children.length; i++) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    let children = root.children || root.childNodes;\n    for (let i = children.length - 1; i >= 0; i--) {\n      let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n  static #_ = this.ɵfac = function FocusTrapFactory_Factory(t) {\n    return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Directive for trapping focus within a region. */\nclass FocusTrapDirective {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap.enabled;\n  }\n  set enabled(value) {\n    this.focusTrap.enabled = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  get autoCapture() {\n    return this._autoCapture;\n  }\n  set autoCapture(value) {\n    this._autoCapture = coerceBooleanProperty(value);\n  }\n  constructor(_elementRef, _focusTrapFactory, _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    this._previouslyFocusedElement = null;\n    this._autoCapture = false;\n    this._document = _document;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n  ngOnDestroy() {\n    this.focusTrap.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (!this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = this._document.activeElement;\n    this.focusTrap.focusInitialElementWhenReady();\n  }\n  static #_ = this.ɵfac = function FocusTrapDirective_Factory(t) {\n    return new (t || FocusTrapDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FocusTrapDirective,\n    selectors: [[\"\", \"focusTrap\", \"\"]],\n    inputs: {\n      enabled: [\"cdkTrapFocus\", \"enabled\"],\n      autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"]\n    },\n    exportAs: [\"focusTrap\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[focusTrap]',\n      exportAs: 'focusTrap'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    enabled: [{\n      type: Input,\n      args: ['cdkTrapFocus']\n    }],\n    autoCapture: [{\n      type: Input,\n      args: ['cdkTrapFocusAutoCapture']\n    }]\n  });\n})();\nclass FocusTrapModule {\n  static forRoot() {\n    return {\n      ngModule: FocusTrapModule,\n      providers: [FocusTrapManager, Platform, InteractivityChecker]\n    };\n  }\n  static #_ = this.ɵfac = function FocusTrapModule_Factory(t) {\n    return new (t || FocusTrapModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FocusTrapModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [FocusTrapDirective],\n      exports: [FocusTrapDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapDirective, FocusTrapModule };", "map": {"version": 3, "names": ["i0", "Injectable", "PLATFORM_ID", "Inject", "Directive", "Input", "NgModule", "isPlatformBrowser", "DOCUMENT", "CommonModule", "take", "FocusTrapManager", "constructor", "_focusTrapStack", "register", "focusTrap", "filter", "ft", "stack", "length", "_disable", "push", "_enable", "deregister", "i", "indexOf", "splice", "_", "ɵfac", "FocusTrapManager_Factory", "t", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "hasV8BreakIterator", "Intl", "v8BreakIterator", "Platform", "_platformId", "<PERSON><PERSON><PERSON><PERSON>", "document", "EDGE", "test", "navigator", "userAgent", "TRIDENT", "BLINK", "window", "chrome", "CSS", "WEBKIT", "IOS", "FIREFOX", "ANDROID", "SAFARI", "Platform_Factory", "ɵɵinject", "Object", "decorators", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "_platform", "isDisabled", "element", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "visibility", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "isPotentiallyTabbableIOS", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "InteractivityChecker_Factory", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "undefined", "getAttribute", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "coerceBooleanProperty", "value", "FocusTrap", "enabled", "_enabled", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "_element", "_checker", "_ngZone", "_document", "deferAnchors", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "attachAnchors", "destroy", "startAnchor", "endAnchor", "removeEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "runOutsideAngular", "_createAnchor", "addEventListener", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "querySelectorAll", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "focus", "has<PERSON>tta<PERSON>", "root", "children", "childNodes", "tabbable<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "anchor", "createElement", "classList", "add", "setAttribute", "isEnabled", "removeAttribute", "toggleAnchors", "fn", "isStable", "onStable", "pipe", "subscribe", "FocusTrapFactory", "create", "deferCaptureElements", "FocusTrapFactory_Factory", "NgZone", "FocusTrapDirective", "autoCapture", "_autoCapture", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "nativeElement", "ngOnDestroy", "ngAfterContentInit", "_captureFocus", "ngDoCheck", "ngOnChanges", "changes", "autoCaptureChange", "firstChange", "activeElement", "FocusTrapDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "selector", "FocusTrapModule", "forRoot", "ngModule", "providers", "FocusTrapModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ngx-bootstrap/focus-trap/fesm2022/ngx-bootstrap-focus-trap.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport { take } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter((ft) => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = (typeof Intl !== 'undefined' && Intl.v8BreakIterator);\n}\ncatch {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    constructor(_platformId) {\n        this._platformId = _platformId;\n        // We want to use the Angular platform check because if the Document is shimmed\n        // without the navigator, the following checks will fail. This is preferred because\n        // sometimes the Document may be shimmed without the user's knowledge or intention\n        /** Whether the Angular application is being rendered in the browser. */\n        this.isBrowser = this._platformId ?\n            isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n        /** Whether the current browser is Microsoft Edge. */\n        this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n        /** Whether the current rendering engine is Microsoft Trident. */\n        this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n        // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n        /** Whether the current rendering engine is Blink. */\n        this.BLINK = this.isBrowser && (!!(window.chrome || hasV8BreakIterator) &&\n            typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT);\n        // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n        // ensure that Webkit runs standalone and is not used as another engine's base.\n        /** Whether the current rendering engine is WebKit. */\n        this.WEBKIT = this.isBrowser &&\n            /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n        /** Whether the current platform is Apple iOS. */\n        this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) &&\n            !('MSStream' in window);\n        // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n        // them self as Gecko-like browsers and modify the userAgent's according to that.\n        // Since we only cover one explicit Firefox case, we can simply check for Firefox\n        // instead of having an unstable check for Gecko.\n        /** Whether the current browser is Firefox. */\n        this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n        /** Whether the current platform is Android. */\n        // Trident on mobile adds the android platform to the userAgent to trick detections.\n        this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n        // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n        // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n        // Safari browser should also use Webkit as its layout engine.\n        /** Whether the current browser is Safari. */\n        this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: Platform, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: Platform, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return isPotentiallyFocusable(element) && !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: InteractivityChecker, deps: [{ token: Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Platform }]; } });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth || element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    // IE11 parses tabindex=\"\" as the value \"-32768\"\n    if (tabIndex == '-32768') {\n        return false;\n    }\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return inputType === 'text'\n        || inputType === 'password'\n        || nodeName === 'select'\n        || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/* eslint-disable */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to misalign.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\nclass FocusTrap {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            if (startAnchor.parentNode) {\n                startAnchor.parentNode.removeChild(startAnchor);\n            }\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            if (endAnchor.parentNode) {\n                endAnchor.parentNode.removeChild(endAnchor);\n            }\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfuly. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then either focuses the first element that the\n     * user specified, or the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady() {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement()));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady() {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement()));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady() {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement()));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        let markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` +\n            `[cdkFocusRegion${bound}], ` +\n            `[cdk-focus-${bound}]`);\n        for (let i = 0; i < markers.length; i++) {\n            // @breaking-change 8.0.0\n            if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                    `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                    `attribute will be removed in 8.0.0.`, markers[i]);\n            }\n            else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                    `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0.`, markers[i]);\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length ?\n            markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement() {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` +\n            `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if (redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus();\n                return !!focusableChild;\n            }\n            redirectToElement.focus();\n            return true;\n        }\n        return this.focusFirstTabbableElement();\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement() {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus();\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement() {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus();\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in DOM order. Note that IE doesn't have `children` for SVG so we fall\n        // back to `childNodes` which includes text nodes, comments etc.\n        let children = root.children || root.childNodes;\n        for (let i = 0; i < children.length; i++) {\n            let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n                this._getFirstTabbableElement(children[i]) :\n                null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        let children = root.children || root.childNodes;\n        for (let i = children.length - 1; i >= 0; i--) {\n            let tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ?\n                this._getLastTabbableElement(children[i]) :\n                null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        if (this._ngZone.isStable) {\n            fn();\n        }\n        else {\n            this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change for 11.0.0 Remove this class.\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Directive for trapping focus within a region. */\nclass FocusTrapDirective {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap.enabled;\n    }\n    set enabled(value) {\n        this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    get autoCapture() {\n        return this._autoCapture;\n    }\n    set autoCapture(value) {\n        this._autoCapture = coerceBooleanProperty(value);\n    }\n    constructor(_elementRef, _focusTrapFactory, _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        this._autoCapture = false;\n        this._document = _document;\n        this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    ngOnDestroy() {\n        this.focusTrap.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (!this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture &&\n            this.focusTrap.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = this._document.activeElement;\n        this.focusTrap.focusInitialElementWhenReady();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapDirective, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.4\", type: FocusTrapDirective, selector: \"[focusTrap]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\"], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"] }, exportAs: [\"focusTrap\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[focusTrap]',\n                    exportAs: 'focusTrap'\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { enabled: [{\n                type: Input,\n                args: ['cdkTrapFocus']\n            }], autoCapture: [{\n                type: Input,\n                args: ['cdkTrapFocusAutoCapture']\n            }] } });\n\nclass FocusTrapModule {\n    static forRoot() {\n        return {\n            ngModule: FocusTrapModule,\n            providers: [\n                FocusTrapManager,\n                Platform,\n                InteractivityChecker\n            ]\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapModule, declarations: [FocusTrapDirective], imports: [CommonModule], exports: [FocusTrapDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: FocusTrapModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [FocusTrapDirective],\n                    exports: [FocusTrapDirective]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapDirective, FocusTrapModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC3F,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,SAASC,IAAI,QAAQ,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,SAAS,EAAE;IAChB;IACA,IAAI,CAACF,eAAe,GAAG,IAAI,CAACA,eAAe,CAACG,MAAM,CAAEC,EAAE,IAAKA,EAAE,KAAKF,SAAS,CAAC;IAC5E,IAAIG,KAAK,GAAG,IAAI,CAACL,eAAe;IAChC,IAAIK,KAAK,CAACC,MAAM,EAAE;MACdD,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IACtC;IACAF,KAAK,CAACG,IAAI,CAACN,SAAS,CAAC;IACrBA,SAAS,CAACO,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIC,UAAUA,CAACR,SAAS,EAAE;IAClBA,SAAS,CAACK,QAAQ,CAAC,CAAC;IACpB,MAAMF,KAAK,GAAG,IAAI,CAACL,eAAe;IAClC,MAAMW,CAAC,GAAGN,KAAK,CAACO,OAAO,CAACV,SAAS,CAAC;IAClC,IAAIS,CAAC,KAAK,CAAC,CAAC,EAAE;MACVN,KAAK,CAACQ,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;MAClB,IAAIN,KAAK,CAACC,MAAM,EAAE;QACdD,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;EAAC,QAAAK,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFnB,gBAAgB;EAAA,CAAoD;EAAA,QAAAoB,EAAA,GACrK,IAAI,CAACC,KAAK,kBAD6EhC,EAAE,CAAAiC,kBAAA;IAAAC,KAAA,EACYvB,gBAAgB;IAAAwB,OAAA,EAAhBxB,gBAAgB,CAAAiB,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrC,EAAE,CAAAsC,iBAAA,CAGX3B,gBAAgB,EAAc,CAAC;IAC9G4B,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,kBAAkB;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI;EACAA,kBAAkB,GAAI,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,eAAgB;AAC9E,CAAC,CACD,MAAM;EACFF,kBAAkB,GAAG,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMG,QAAQ,CAAC;EACXhC,WAAWA,CAACiC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACD,WAAW,GAC7BtC,iBAAiB,CAAC,IAAI,CAACsC,WAAW,CAAC,GAAG,OAAOE,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ;IACpF;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACF,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IACjE;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACN,SAAS,IAAI,iBAAiB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAC5E;IACA;IACA,IAAI,CAACE,KAAK,GAAG,IAAI,CAACP,SAAS,IAAK,CAAC,EAAEQ,MAAM,CAACC,MAAM,IAAId,kBAAkB,CAAC,IACnE,OAAOe,GAAG,KAAK,WAAW,IAAI,CAAC,IAAI,CAACR,IAAI,IAAI,CAAC,IAAI,CAACI,OAAQ;IAC9D;IACA;IACA;IACA,IAAI,CAACK,MAAM,GAAG,IAAI,CAACX,SAAS,IACxB,cAAc,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAACE,KAAK,IAAI,CAAC,IAAI,CAACL,IAAI,IAAI,CAAC,IAAI,CAACI,OAAO;IAC1F;IACA,IAAI,CAACM,GAAG,GAAG,IAAI,CAACZ,SAAS,IAAI,kBAAkB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IACrE,EAAE,UAAU,IAAIG,MAAM,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA,IAAI,CAACK,OAAO,GAAG,IAAI,CAACb,SAAS,IAAI,sBAAsB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IACjF;IACA;IACA,IAAI,CAACS,OAAO,GAAG,IAAI,CAACd,SAAS,IAAI,UAAU,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAACC,OAAO;IACtF;IACA;IACA;IACA;IACA,IAAI,CAACS,MAAM,GAAG,IAAI,CAACf,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,IAAI,CAACM,MAAM;EACtF;EAAC,QAAA9B,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAkC,iBAAAhC,CAAA;IAAA,YAAAA,CAAA,IAAwFc,QAAQ,EA1ElB5C,EAAE,CAAA+D,QAAA,CA0EkC7D,WAAW;EAAA,CAA6C;EAAA,QAAA6B,EAAA,GACnL,IAAI,CAACC,KAAK,kBA3E6EhC,EAAE,CAAAiC,kBAAA;IAAAC,KAAA,EA2EYU,QAAQ;IAAAT,OAAA,EAARS,QAAQ,CAAAhB,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AACjJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7EoGrC,EAAE,CAAAsC,iBAAA,CA6EXM,QAAQ,EAAc,CAAC;IACtGL,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEyB,MAAM;MAAEC,UAAU,EAAE,CAAC;QAC3D1B,IAAI,EAAEpC,MAAM;QACZqC,IAAI,EAAE,CAACtC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgE,iBAAiB,CAAC;EACpBtD,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACuD,gBAAgB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBxD,WAAWA,CAACyD,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACC,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACF,OAAO,EAAE;IACf,OAAOG,WAAW,CAACH,OAAO,CAAC,IAAII,gBAAgB,CAACJ,OAAO,CAAC,CAACK,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACN,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAACF,SAAS,CAACvB,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMgC,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACT,OAAO,CAAC,CAAC;IACxD,IAAIO,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACL,SAAS,CAACK,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAGX,OAAO,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAACV,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOY,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAACb,SAAS,CAACZ,MAAM,IAAI,IAAI,CAACY,SAAS,CAACX,GAAG,IAAI,CAAC2B,wBAAwB,CAACd,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAIW,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAACX,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOY,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACf,SAAS,CAACV,OAAO,IAAIY,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAOD,OAAO,CAACe,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAChB,OAAO,EAAEiB,MAAM,EAAE;IACzB;IACA;IACA,OAAOC,sBAAsB,CAAClB,OAAO,CAAC,IAAI,CAAC,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC,KAC9DiB,MAAM,EAAErB,gBAAgB,IAAI,IAAI,CAACM,SAAS,CAACF,OAAO,CAAC,CAAC;EAC7D;EAAC,QAAA5C,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA8D,6BAAA5D,CAAA;IAAA,YAAAA,CAAA,IAAwFsC,oBAAoB,EA1N9BpE,EAAE,CAAA+D,QAAA,CA0N8CnB,QAAQ;EAAA,CAA6C;EAAA,QAAAb,EAAA,GAC5L,IAAI,CAACC,KAAK,kBA3N6EhC,EAAE,CAAAiC,kBAAA;IAAAC,KAAA,EA2NYkC,oBAAoB;IAAAjC,OAAA,EAApBiC,oBAAoB,CAAAxC,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AAC7J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7NoGrC,EAAE,CAAAsC,iBAAA,CA6NX8B,oBAAoB,EAAc,CAAC;IAClH7B,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEK;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;AACxE;AACA;AACA;AACA;AACA;AACA,SAASmC,eAAeA,CAACzB,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAACwB,YAAY;EAC9B,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASJ,WAAWA,CAACH,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACoB,WAAW,IAAIpB,OAAO,CAACqB,YAAY,IAChD,OAAOrB,OAAO,CAACsB,cAAc,KAAK,UAAU,IAAItB,OAAO,CAACsB,cAAc,CAAC,CAAC,CAAC1E,MAAO,CAAC;AAC1F;AACA;AACA,SAAS2E,mBAAmBA,CAACvB,OAAO,EAAE;EAClC,IAAIW,QAAQ,GAAGX,OAAO,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAOD,QAAQ,KAAK,OAAO,IACvBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAASa,aAAaA,CAACxB,OAAO,EAAE;EAC5B,OAAOyB,cAAc,CAACzB,OAAO,CAAC,IAAIA,OAAO,CAAChC,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAAS0D,gBAAgBA,CAAC1B,OAAO,EAAE;EAC/B,OAAO2B,eAAe,CAAC3B,OAAO,CAAC,IAAIA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAASwB,cAAcA,CAACzB,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAASe,eAAeA,CAAC3B,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASgB,gBAAgBA,CAAC5B,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,IAAID,OAAO,CAACe,QAAQ,KAAKc,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAId,QAAQ,GAAGf,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC;EAC/C;EACA,IAAIf,QAAQ,IAAI,QAAQ,EAAE;IACtB,OAAO,KAAK;EAChB;EACA,OAAO,CAAC,EAAEA,QAAQ,IAAI,CAACgB,KAAK,CAACC,QAAQ,CAACjB,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASL,gBAAgBA,CAACV,OAAO,EAAE;EAC/B,IAAI,CAAC4B,gBAAgB,CAAC5B,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAMe,QAAQ,GAAGiB,QAAQ,CAAChC,OAAO,CAAC8B,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOC,KAAK,CAAChB,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASD,wBAAwBA,CAACd,OAAO,EAAE;EACvC,IAAIW,QAAQ,GAAGX,OAAO,CAACW,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIqB,SAAS,GAAGtB,QAAQ,KAAK,OAAO,IAAIX,OAAO,CAAChC,IAAI;EACpD,OAAOiE,SAAS,KAAK,MAAM,IACpBA,SAAS,KAAK,UAAU,IACxBtB,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA,SAASO,sBAAsBA,CAAClB,OAAO,EAAE;EACrC;EACA,IAAIwB,aAAa,CAACxB,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAOuB,mBAAmB,CAACvB,OAAO,CAAC,IAC/B0B,gBAAgB,CAAC1B,OAAO,CAAC,IACzBA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,IACvC2B,gBAAgB,CAAC5B,OAAO,CAAC;AACjC;AACA;AACA,SAASS,SAASA,CAACyB,IAAI,EAAE;EACrB;EACA,OAAOA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAIrD,MAAM;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsD,qBAAqBA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAI,IAAI,IAAK,GAAEA,KAAM,EAAC,KAAK,OAAO;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAACG,QAAQ,GAAGH,KAAK;IACrB,IAAI,IAAI,CAACI,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACN,KAAK,EAAE,IAAI,CAACI,YAAY,CAAC;MACpD,IAAI,CAACE,qBAAqB,CAACN,KAAK,EAAE,IAAI,CAACK,UAAU,CAAC;IACtD;EACJ;EACAtG,WAAWA,CAACwG,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,GAAG,KAAK,EAAE;IACtE,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,MAAM,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAG,MAAM,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC/D,IAAI,CAACb,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACQ,YAAY,EAAE;MACf,IAAI,CAACM,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMC,WAAW,GAAG,IAAI,CAACf,YAAY;IACrC,MAAMgB,SAAS,GAAG,IAAI,CAACf,UAAU;IACjC,IAAIc,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACR,mBAAmB,CAAC;MAClE,IAAIM,WAAW,CAACG,UAAU,EAAE;QACxBH,WAAW,CAACG,UAAU,CAACC,WAAW,CAACJ,WAAW,CAAC;MACnD;IACJ;IACA,IAAIC,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACN,iBAAiB,CAAC;MAC9D,IAAIK,SAAS,CAACE,UAAU,EAAE;QACtBF,SAAS,CAACE,UAAU,CAACC,WAAW,CAACH,SAAS,CAAC;MAC/C;IACJ;IACA,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACO,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACH,OAAO,CAACe,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACqB,aAAa,CAAC,CAAC;QACxC,IAAI,CAACrB,YAAY,CAACsB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACb,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACR,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACoB,aAAa,CAAC,CAAC;QACtC,IAAI,CAACpB,UAAU,CAACqB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACX,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACR,QAAQ,CAACe,UAAU,EAAE;MAC1B,IAAI,CAACf,QAAQ,CAACe,UAAU,CAACK,YAAY,CAAC,IAAI,CAACvB,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAACe,UAAU,CAACK,YAAY,CAAC,IAAI,CAACtB,UAAU,EAAE,IAAI,CAACE,QAAQ,CAACqB,WAAW,CAAC;MACjF,IAAI,CAAChB,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kCAAkCA,CAAA,EAAG;IACjC,OAAO,IAAIJ,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACf,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACImB,iCAAiCA,CAAA,EAAG;IAChC,OAAO,IAAIL,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACjB,wBAAwB,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIsB,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,IAAIC,OAAO,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,gBAAgB,CAAE,qBAAoBF,KAAM,KAAI,GACvE,kBAAiBA,KAAM,KAAI,GAC3B,cAAaA,KAAM,GAAE,CAAC;IAC3B,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2H,OAAO,CAAChI,MAAM,EAAEK,CAAC,EAAE,EAAE;MACrC;MACA,IAAI2H,OAAO,CAAC3H,CAAC,CAAC,CAACgD,YAAY,CAAE,aAAY0E,KAAM,EAAC,CAAC,EAAE;QAC/CG,OAAO,CAACC,IAAI,CAAE,gDAA+CJ,KAAM,KAAI,GAClE,sBAAqBA,KAAM,4BAA2B,GACtD,qCAAoC,EAAEC,OAAO,CAAC3H,CAAC,CAAC,CAAC;MAC1D,CAAC,MACI,IAAI2H,OAAO,CAAC3H,CAAC,CAAC,CAACgD,YAAY,CAAE,oBAAmB0E,KAAM,EAAC,CAAC,EAAE;QAC3DG,OAAO,CAACC,IAAI,CAAE,uDAAsDJ,KAAM,KAAI,GACzE,sBAAqBA,KAAM,sCAAqC,GAChE,2BAA0B,EAAEC,OAAO,CAAC3H,CAAC,CAAC,CAAC;MAChD;IACJ;IACA,IAAI0H,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAAChI,MAAM,GAAGgI,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACI,wBAAwB,CAAC,IAAI,CAACnC,QAAQ,CAAC;IACrF;IACA,OAAO+B,OAAO,CAAChI,MAAM,GACjBgI,OAAO,CAACA,OAAO,CAAChI,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAACqI,uBAAuB,CAAC,IAAI,CAACpC,QAAQ,CAAC;EACjF;EACA;AACJ;AACA;AACA;EACI0B,mBAAmBA,CAAA,EAAG;IAClB;IACA,MAAMW,iBAAiB,GAAG,IAAI,CAACrC,QAAQ,CAACsC,aAAa,CAAE,uBAAsB,GACxE,mBAAkB,CAAC;IACxB,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAIA,iBAAiB,CAACjF,YAAY,CAAE,mBAAkB,CAAC,EAAE;QACrD6E,OAAO,CAACC,IAAI,CAAE,yDAAwD,GACjE,0DAAyD,GACzD,0BAAyB,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACpC,QAAQ,CAAC9B,WAAW,CAACkE,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,EAAEC,KAAK,CAAC,CAAC;QACvB,OAAO,CAAC,CAACD,cAAc;MAC3B;MACAF,iBAAiB,CAACG,KAAK,CAAC,CAAC;MACzB,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAC/B,yBAAyB,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIA,yBAAyBA,CAAA,EAAG;IACxB,MAAM4B,iBAAiB,GAAG,IAAI,CAACR,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIQ,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,CAAC,CAACH,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACI9B,wBAAwBA,CAAA,EAAG;IACvB,MAAM8B,iBAAiB,GAAG,IAAI,CAACR,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIQ,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,CAAC,CAACH,iBAAiB;EAC9B;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACpC,YAAY;EAC5B;EACA;EACA8B,wBAAwBA,CAACO,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACzC,QAAQ,CAAC9B,WAAW,CAACuE,IAAI,CAAC,IAAI,IAAI,CAACzC,QAAQ,CAACxC,UAAU,CAACiF,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA;IACA,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,UAAU;IAC/C,KAAK,IAAIxI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,QAAQ,CAAC5I,MAAM,EAAEK,CAAC,EAAE,EAAE;MACtC,IAAIyI,aAAa,GAAGF,QAAQ,CAACvI,CAAC,CAAC,CAAC0I,QAAQ,KAAK,IAAI,CAAC3C,SAAS,CAAC4C,YAAY,GACpE,IAAI,CAACZ,wBAAwB,CAACQ,QAAQ,CAACvI,CAAC,CAAC,CAAC,GAC1C,IAAI;MACR,IAAIyI,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAT,uBAAuBA,CAACM,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACzC,QAAQ,CAAC9B,WAAW,CAACuE,IAAI,CAAC,IAAI,IAAI,CAACzC,QAAQ,CAACxC,UAAU,CAACiF,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,UAAU;IAC/C,KAAK,IAAIxI,CAAC,GAAGuI,QAAQ,CAAC5I,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAIyI,aAAa,GAAGF,QAAQ,CAACvI,CAAC,CAAC,CAAC0I,QAAQ,KAAK,IAAI,CAAC3C,SAAS,CAAC4C,YAAY,GACpE,IAAI,CAACX,uBAAuB,CAACO,QAAQ,CAACvI,CAAC,CAAC,CAAC,GACzC,IAAI;MACR,IAAIyI,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA3B,aAAaA,CAAA,EAAG;IACZ,MAAM8B,MAAM,GAAG,IAAI,CAAC7C,SAAS,CAAC8C,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAAClD,qBAAqB,CAAC,IAAI,CAACH,QAAQ,EAAEoD,MAAM,CAAC;IACjDA,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CH,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CH,MAAM,CAACI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOJ,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIjD,qBAAqBA,CAACsD,SAAS,EAAEL,MAAM,EAAE;IACrC;IACA;IACAK,SAAS,GAAGL,MAAM,CAACI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGJ,MAAM,CAACM,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAAC5D,OAAO,EAAE;IACnB,IAAI,IAAI,CAACE,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACJ,OAAO,EAAE,IAAI,CAACE,YAAY,CAAC;MACtD,IAAI,CAACE,qBAAqB,CAACJ,OAAO,EAAE,IAAI,CAACG,UAAU,CAAC;IACxD;EACJ;EACA;EACA2B,gBAAgBA,CAAC+B,EAAE,EAAE;IACjB,IAAI,IAAI,CAACtD,OAAO,CAACuD,QAAQ,EAAE;MACvBD,EAAE,CAAC,CAAC;IACR,CAAC,MACI;MACD,IAAI,CAACtD,OAAO,CAACwD,QAAQ,CAACC,IAAI,CAACrK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsK,SAAS,CAACJ,EAAE,CAAC;IACrD;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,gBAAgB,CAAC;EACnBrK,WAAWA,CAACyG,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACtC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2D,MAAMA,CAAC3G,OAAO,EAAE4G,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIrE,SAAS,CAACvC,OAAO,EAAE,IAAI,CAAC8C,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,EAAE4D,oBAAoB,CAAC;EACpG;EAAC,QAAAxJ,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAwJ,yBAAAtJ,CAAA;IAAA,YAAAA,CAAA,IAAwFmJ,gBAAgB,EA7nB1BjL,EAAE,CAAA+D,QAAA,CA6nB0CK,oBAAoB,GA7nBhEpE,EAAE,CAAA+D,QAAA,CA6nB2E/D,EAAE,CAACqL,MAAM,GA7nBtFrL,EAAE,CAAA+D,QAAA,CA6nBiGvD,QAAQ;EAAA,CAA6C;EAAA,QAAAuB,EAAA,GAC/O,IAAI,CAACC,KAAK,kBA9nB6EhC,EAAE,CAAAiC,kBAAA;IAAAC,KAAA,EA8nBY+I,gBAAgB;IAAA9I,OAAA,EAAhB8I,gBAAgB,CAAArJ,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhoBoGrC,EAAE,CAAAsC,iBAAA,CAgoBX2I,gBAAgB,EAAc,CAAC;IAC9G1I,IAAI,EAAEtC,UAAU;IAChBuC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE6B;IAAqB,CAAC,EAAE;MAAE7B,IAAI,EAAEvC,EAAE,CAACqL;IAAO,CAAC,EAAE;MAAE9I,IAAI,EAAE6D,SAAS;MAAEnC,UAAU,EAAE,CAAC;QACnH1B,IAAI,EAAEpC,MAAM;QACZqC,IAAI,EAAE,CAAChC,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA,MAAM8K,kBAAkB,CAAC;EACrB;EACA,IAAIvE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChG,SAAS,CAACgG,OAAO;EACjC;EACA,IAAIA,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAAC9F,SAAS,CAACgG,OAAO,GAAGH,qBAAqB,CAACC,KAAK,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,IAAI0E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAC1E,KAAK,EAAE;IACnB,IAAI,CAAC2E,YAAY,GAAG5E,qBAAqB,CAACC,KAAK,CAAC;EACpD;EACAjG,WAAWA,CAAC6K,WAAW,EAAEC,iBAAiB,EAAEnE,SAAS,EAAE;IACnD,IAAI,CAACkE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACH,YAAY,GAAG,KAAK;IACzB,IAAI,CAACjE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACxG,SAAS,GAAG,IAAI,CAAC2K,iBAAiB,CAACR,MAAM,CAAC,IAAI,CAACO,WAAW,CAACG,aAAa,EAAE,IAAI,CAAC;EACxF;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9K,SAAS,CAACgH,OAAO,CAAC,CAAC;IACxB;IACA;IACA,IAAI,IAAI,CAAC4D,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAAC/B,KAAK,CAAC,CAAC;MACtC,IAAI,CAAC+B,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC/K,SAAS,CAAC+G,aAAa,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACyD,WAAW,EAAE;MAClB,IAAI,CAACQ,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACjL,SAAS,CAAC8I,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAAC9I,SAAS,CAAC+G,aAAa,CAAC,CAAC;IAClC;EACJ;EACAmE,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,iBAAiB,GAAGD,OAAO,CAAC,aAAa,CAAC;IAChD,IAAIC,iBAAiB,IAAI,CAACA,iBAAiB,CAACC,WAAW,IAAI,IAAI,CAACb,WAAW,IACvE,IAAI,CAACxK,SAAS,CAAC8I,WAAW,CAAC,CAAC,EAAE;MAC9B,IAAI,CAACkC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,yBAAyB,GAAG,IAAI,CAACpE,SAAS,CAAC8E,aAAa;IAC7D,IAAI,CAACtL,SAAS,CAAC2H,4BAA4B,CAAC,CAAC;EACjD;EAAC,QAAA/G,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA0K,2BAAAxK,CAAA;IAAA,YAAAA,CAAA,IAAwFwJ,kBAAkB,EAlsB5BtL,EAAE,CAAAuM,iBAAA,CAksB4CvM,EAAE,CAACwM,UAAU,GAlsB3DxM,EAAE,CAAAuM,iBAAA,CAksBsEtB,gBAAgB,GAlsBxFjL,EAAE,CAAAuM,iBAAA,CAksBmG/L,QAAQ;EAAA,CAA4C;EAAA,QAAAuB,EAAA,GAChP,IAAI,CAAC0K,IAAI,kBAnsB8EzM,EAAE,CAAA0M,iBAAA;IAAAnK,IAAA,EAmsBJ+I,kBAAkB;IAAAqB,SAAA;IAAAC,MAAA;MAAA7F,OAAA;MAAAwE,WAAA;IAAA;IAAAsB,QAAA;IAAAC,QAAA,GAnsBhB9M,EAAE,CAAA+M,oBAAA;EAAA,EAmsBiN;AACvT;AACA;EAAA,QAAA1K,SAAA,oBAAAA,SAAA,KArsBoGrC,EAAE,CAAAsC,iBAAA,CAqsBXgJ,kBAAkB,EAAc,CAAC;IAChH/I,IAAI,EAAEnC,SAAS;IACfoC,IAAI,EAAE,CAAC;MACCwK,QAAQ,EAAE,aAAa;MACvBH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtK,IAAI,EAAEvC,EAAE,CAACwM;IAAW,CAAC,EAAE;MAAEjK,IAAI,EAAE0I;IAAiB,CAAC,EAAE;MAAE1I,IAAI,EAAE6D,SAAS;MAAEnC,UAAU,EAAE,CAAC;QACnH1B,IAAI,EAAEpC,MAAM;QACZqC,IAAI,EAAE,CAAChC,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEuG,OAAO,EAAE,CAAC;MACtCxE,IAAI,EAAElC,KAAK;MACXmC,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE+I,WAAW,EAAE,CAAC;MACdhJ,IAAI,EAAElC,KAAK;MACXmC,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyK,eAAe,CAAC;EAClB,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,eAAe;MACzBG,SAAS,EAAE,CACPzM,gBAAgB,EAChBiC,QAAQ,EACRwB,oBAAoB;IAE5B,CAAC;EACL;EAAC,QAAAzC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAyL,wBAAAvL,CAAA;IAAA,YAAAA,CAAA,IAAwFmL,eAAe;EAAA,CAAkD;EAAA,QAAAlL,EAAA,GAClK,IAAI,CAACuL,IAAI,kBAluB8EtN,EAAE,CAAAuN,gBAAA;IAAAhL,IAAA,EAkuBS0K;EAAe,EAA+F;EAAA,QAAAO,EAAA,GAChN,IAAI,CAACC,IAAI,kBAnuB8EzN,EAAE,CAAA0N,gBAAA;IAAAC,OAAA,GAmuBoClN,YAAY;EAAA,EAAI;AAC1J;AACA;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KAruBoGrC,EAAE,CAAAsC,iBAAA,CAquBX2K,eAAe,EAAc,CAAC;IAC7G1K,IAAI,EAAEjC,QAAQ;IACdkC,IAAI,EAAE,CAAC;MACCmL,OAAO,EAAE,CAAClN,YAAY,CAAC;MACvBmN,YAAY,EAAE,CAACtC,kBAAkB,CAAC;MAClCuC,OAAO,EAAE,CAACvC,kBAAkB;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASxE,SAAS,EAAEwE,kBAAkB,EAAE2B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}