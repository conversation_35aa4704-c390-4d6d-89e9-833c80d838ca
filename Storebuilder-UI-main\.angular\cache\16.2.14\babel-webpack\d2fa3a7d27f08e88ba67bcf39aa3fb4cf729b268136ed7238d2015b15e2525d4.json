{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MerchantLivestreamDetailsComponent } from './merchant-livestream-details/merchant-livestream-details.component';\nimport { MerchantLivestreamComponent } from './merchant-livestream.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MerchantLivestreamComponent\n}, {\n  path: 'merchant-liveStream-details',\n  component: MerchantLivestreamDetailsComponent\n}];\nexport class MerchantLivestreamRoutingModule {\n  static #_ = this.ɵfac = function MerchantLivestreamRoutingModule_Factory(t) {\n    return new (t || MerchantLivestreamRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MerchantLivestreamRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MerchantLivestreamRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "MerchantLivestreamDetailsComponent", "MerchantLivestreamComponent", "routes", "path", "component", "MerchantLivestreamRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchant-livestream\\merchant-livestream-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { MerchantLivestreamDetailsComponent } from './merchant-livestream-details/merchant-livestream-details.component';\r\nimport { MerchantLivestreamComponent } from './merchant-livestream.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: MerchantLivestreamComponent },\r\n  { path: 'merchant-liveStream-details', component: MerchantLivestreamDetailsComponent },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class MerchantLivestreamRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kCAAkC,QAAQ,qEAAqE;AACxH,SAASC,2BAA2B,QAAQ,iCAAiC;;;AAE7E,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAA2B,CAAE,EACpD;EAAEE,IAAI,EAAE,6BAA6B;EAAEC,SAAS,EAAEJ;AAAkC,CAAE,CACvF;AAMD,OAAM,MAAOK,+BAA+B;EAAA,QAAAC,CAAA,G;qBAA/BD,+BAA+B;EAAA;EAAA,QAAAE,EAAA,G;UAA/BF;EAA+B;EAAA,QAAAG,EAAA,G;cAHhCT,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC,EAC7BH,YAAY;EAAA;;;2EAEXM,+BAA+B;IAAAK,OAAA,GAAAC,EAAA,CAAAZ,YAAA;IAAAa,OAAA,GAFhCb,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}