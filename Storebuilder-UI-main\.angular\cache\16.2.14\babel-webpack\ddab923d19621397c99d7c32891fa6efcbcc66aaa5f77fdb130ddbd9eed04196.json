{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { NavigationEnd } from \"@angular/router\";\nimport { filter } from 'rxjs';\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"../../../../shared/components/product-slider/product-slider.component\";\nimport * as i9 from \"../../../../shared/components/section/section.component\";\nimport * as i10 from \"../details/details.component\";\nimport * as i11 from \"../image-zoom/image-zoom.component\";\nimport * as i12 from \"../floating-panel/floating-panel.component\";\nfunction IndexComponent_ng_container_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"app-mtn-section\", 15);\n    i0.ɵɵelement(2, \"app-mtn-product-slider\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", \"Related Products\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"products\", ctx_r1.relatedProducts);\n  }\n}\nfunction IndexComponent_ng_container_4_app_floating_panel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-floating-panel\", 17);\n    i0.ɵɵlistener(\"onItemLike\", function IndexComponent_ng_container_4_app_floating_panel_9_Template_app_floating_panel_onItemLike_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"currency\", ctx_r2.currency)(\"product\", ctx_r2.productDetails)(\"variant\", ctx_r2.selectedVariance);\n  }\n}\nfunction IndexComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"app-image-zoom\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"app-details\", 11);\n    i0.ɵɵlistener(\"onChangeVariant\", function IndexComponent_ng_container_4_Template_app_details_onChangeVariant_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onvVarinaceChange($event));\n    })(\"onItemLike\", function IndexComponent_ng_container_4_Template_app_details_onItemLike_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, IndexComponent_ng_container_4_div_8_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, IndexComponent_ng_container_4_app_floating_panel_9_Template, 1, 3, \"app-floating-panel\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"product\", ctx_r0.productDetails)(\"variant\", ctx_r0.selectedVariance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"currency\", ctx_r0.currency)(\"product\", ctx_r0.productDetails)(\"selectedColor\", ctx_r0.selectedColor)(\"selectedSize\", ctx_r0.selectedSize)(\"selectedVariant\", ctx_r0.selectedVariance);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.relatedProducts.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showPanel);\n  }\n}\nexport class IndexComponent {\n  store;\n  activatedRoute;\n  productService;\n  messageService;\n  router;\n  translate;\n  ref;\n  loaderService;\n  $gaService;\n  permissionService;\n  platformId;\n  customGAService;\n  relatedProducts = [];\n  currency = {};\n  productDetails = {};\n  specProductId;\n  loading = false;\n  showPanel = false;\n  items = [];\n  home = {\n    icon: 'pi pi-home',\n    routerLink: '/'\n  };\n  categoryId;\n  category;\n  selectedColor;\n  selectedSize;\n  selectedVariance;\n  name = 'alam';\n  token;\n  channelId;\n  tagName = GaActionEnum;\n  userDetails;\n  isGoogleAnalytics = false;\n  constructor(store, activatedRoute, productService, messageService, router, translate, ref, loaderService, $gaService, permissionService, platformId, customGAService) {\n    this.store = store;\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.router = router;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    this.customGAService = customGAService;\n    router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {});\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.specProductId = params.get('id');\n      this.channelId = params.get('channelId');\n    });\n  }\n  ngOnInit() {\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.loadData();\n    if (isPlatformBrowser(this.platformId)) {\n      window.addEventListener('scroll', this.onScrollEvent, true);\n      this.router.events.subscribe(evt => {\n        if (!(evt instanceof NavigationEnd)) {\n          return;\n        }\n        window.scrollTo(0, 0);\n      });\n    }\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  onScrollEvent = _event => {\n    if (isPlatformBrowser(this.platformId)) {\n      const sectionDetails = document.querySelector('#details');\n      const relatedProductsSection = document.querySelector('#relatedProducts');\n      const fromTop = sectionDetails ? sectionDetails.getBoundingClientRect().top < -110 : false;\n      const fromBottom = relatedProductsSection ? relatedProductsSection.getBoundingClientRect().top < 550 : false;\n      this.showPanel = fromTop && !fromBottom;\n    }\n  };\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    if (isPlatformBrowser(this.platformId)) {\n      let top = document.getElementById('top');\n      if (top !== null) {\n        top.scrollIntoView();\n        top = null;\n      }\n    }\n  }\n  onItemLiked(event) {\n    this.loadData();\n  }\n  loadData() {\n    this.loaderService.show();\n    this.productService.getProductDetails(this.specProductId, this.channelId).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        this.productDetails = res.data;\n        if (this.productDetails) {\n          this.userDetails = this.store.get('profile');\n          this.productDetails['specs'] = {};\n        }\n        this.productDetails?.productSpecs.forEach(spec => {\n          this.productDetails.specs[spec.name] = spec;\n        });\n        this.productDetails.productVariances.forEach(variance => {\n          variance['specs'] = [];\n          variance.varianceSpecs.forEach(spec => {\n            variance['specs'][spec.name] = spec;\n          });\n          if (variance.salePriceValue) {\n            variance.salePercent = 100 - variance.salePriceValue / variance.priceValue * 100;\n          }\n        });\n        let defaultVariant = this.productDetails.productVariances.find(variant => variant.isDefault);\n        if (defaultVariant) {\n          this.selectedVariance = defaultVariant;\n        } else {\n          let approvedVariant = this.productDetails.productVariances.find(variant => !variant.soldOut);\n          if (approvedVariant) {\n            this.selectedVariance = approvedVariant;\n          } else {\n            this.selectedVariance = this.productDetails.productVariances[0];\n          }\n        }\n        // color picker implementation\n        this.selectedColor = this.selectedVariance.color;\n        this.selectedVariance.varianceSpecs.forEach(variantSpec => {\n          if (variantSpec.name == 'Size') {\n            this.selectedSize = variantSpec.value;\n          }\n        });\n        // Track analytics after selectedVariance is set\n        if (this.isGoogleAnalytics && this.permissionService.getTagFeature('VIEW_ITEM')) {\n          this.$gaService.pageView('/product', 'Product Detail: ' + this.specProductId);\n          this.customGAService.viewItemEvent(this.selectedVariance, this.productDetails);\n        }\n        this.assignBreadCrumbsData();\n        this.ref.detectChanges();\n        this.ref.markForCheck();\n        this.loading = false;\n      },\n      error: err => {\n        this.loaderService.hide();\n        console.error(err);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n        this.loading = false;\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    this.store.subscription('categories').subscribe({\n      next: res => {\n        res.forEach(element => {\n          if (element.id == this.categoryId) {\n            this.category = element;\n          }\n        });\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  onvVarinaceChange(variance) {\n    this.selectedVariance = variance;\n  }\n  assignBreadCrumbsData() {\n    let idsArray = this.productDetails?.categoryIds?.split(\"->\").map(Number);\n    let nameArray = this.productDetails?.categoryPath?.split(\"->\").map(String);\n    let breadCrumbs = [];\n    if (idsArray?.length === nameArray?.length) {\n      idsArray?.map((e, i) => {\n        breadCrumbs.push({\n          routerLink: '/category/' + e.toString(),\n          label: nameArray[i]\n        });\n      });\n      this.items = breadCrumbs;\n    }\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.CustomGAService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"old-product-index\"], [\"id\", \"top\", 1, \"product-details\"], [1, \"breadcrumb\"], [1, \"col-12\", 3, \"home\", \"model\"], [4, \"ngIf\"], [1, \"content-container\", \"my-3\"], [1, \"grid\", \"pt-0\"], [1, \"col-12\", \"col-md-6\", \"col-lg-7\"], [1, \"images\"], [3, \"product\", \"variant\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\"], [\"id\", \"details\", 3, \"currency\", \"product\", \"selectedColor\", \"selectedSize\", \"selectedVariant\", \"onChangeVariant\", \"onItemLike\"], [\"class\", \"my-5\", 4, \"ngIf\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\", 4, \"ngIf\"], [1, \"my-5\"], [\"id\", \"relatedProducts\", 3, \"title\"], [3, \"products\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, IndexComponent_ng_container_4_Template, 10, 9, \"ng-container\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"home\", ctx.home)(\"model\", ctx.items);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.productDetails == null ? null : ctx.productDetails.id);\n      }\n    },\n    dependencies: [i6.NgIf, i7.Breadcrumb, i8.ProductSliderComponent, i9.SectionComponent, i10.DetailsComponent, i11.ImageZoomComponent, i12.FloatingPanelComponent],\n    styles: [\".old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  position: relative;\\n  margin-top: 93px;\\n}\\n.old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n}\\n@media screen and (max-width: 320px) {\\n  .old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 140px;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 0rem !important;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .pt-6[_ngcontent-%COMP%] {\\n    padding-top: 0rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) and (min-width: 325px) {\\n  .old-product-index[_ngcontent-%COMP%]   .h-d-1[_ngcontent-%COMP%] {\\n    height: 300px !important;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 155px;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 1rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .old-product-index[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n    min-height: 100% !important;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}