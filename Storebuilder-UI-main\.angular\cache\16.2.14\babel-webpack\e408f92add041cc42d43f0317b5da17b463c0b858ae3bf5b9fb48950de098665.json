{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SitemapService {\n  constructor() {}\n  generateSitemap(routes) {\n    let sitemap = '<?xml version=\"1.0\" encoding=\"UTF-8\"?>';\n    sitemap += '<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">';\n    routes.forEach(route => {\n      sitemap += `<url><loc>https://momomarket.africa:1010/${route.path}</loc></url>`;\n    });\n    sitemap += '</urlset>';\n    return sitemap;\n  }\n  static ɵfac = function SitemapService_Factory(t) {\n    return new (t || SitemapService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SitemapService,\n    factory: SitemapService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["SitemapService", "constructor", "generateSitemap", "routes", "sitemap", "for<PERSON>ach", "route", "path", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\sitemap.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Routes } from '@angular/router';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SitemapService {\r\n\r\n  constructor() { }\r\n\r\n  generateSitemap(routes: Routes): string {\r\n    let sitemap = '<?xml version=\"1.0\" encoding=\"UTF-8\"?>';\r\n    sitemap += '<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">';\r\n\r\n    routes.forEach(route => {\r\n      sitemap += `<url><loc>https://momomarket.africa:1010/${route.path}</loc></url>`;\r\n    });\r\n\r\n    sitemap += '</urlset>';\r\n    return sitemap;\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,cAAc;EAEzBC,YAAA,GAAgB;EAEhBC,eAAeA,CAACC,MAAc;IAC5B,IAAIC,OAAO,GAAG,wCAAwC;IACtDA,OAAO,IAAI,8DAA8D;IAEzED,MAAM,CAACE,OAAO,CAACC,KAAK,IAAG;MACrBF,OAAO,IAAI,4CAA4CE,KAAK,CAACC,IAAI,cAAc;IACjF,CAAC,CAAC;IAEFH,OAAO,IAAI,WAAW;IACtB,OAAOA,OAAO;EAChB;;qBAdWJ,cAAc;EAAA;;WAAdA,cAAc;IAAAQ,OAAA,EAAdR,cAAc,CAAAS,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}