{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@core/services\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/carousel\";\nimport * as i7 from \"primeng/dialog\";\nconst _c0 = function (a0) {\n  return {\n    active: a0\n  };\n};\nfunction ImageZoomComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"img\", 11);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_0_div_5_Template_img_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const img_r4 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.selectImage(img_r4.image));\n    })(\"error\", function ImageZoomComponent_div_0_div_5_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.errorHandler($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const img_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"alt\", ctx_r2.product.productName)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, img_r4.id === ctx_r2.selectedImage.id))(\"src\", ctx_r2.getProductImages(img_r4.thumbnail), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ImageZoomComponent_div_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r3.getProductImages(ctx_r3.selectedImage.large_image_url), i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"40vw\"\n  };\n};\nconst _c2 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nfunction ImageZoomComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"img\", 5);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_0_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.showImageModal(ctx_r8.getProductImages(ctx_r8.selectedImage.large_image_url)));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, ImageZoomComponent_div_0_div_5_Template, 2, 5, \"div\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p-dialog\", 8);\n    i0.ɵɵlistener(\"visibleChange\", function ImageZoomComponent_div_0_Template_p_dialog_visibleChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.displayModal = $event);\n    });\n    i0.ɵɵtemplate(7, ImageZoomComponent_div_0_ng_template_7_Template, 2, 1, \"ng-template\", 9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.getProductImages(ctx_r0.selectedImage.large_image_url), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.images);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c1));\n    i0.ɵɵproperty(\"visible\", ctx_r0.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(11, _c2))(\"draggable\", false)(\"draggable\", false)(\"modal\", true)(\"resizable\", false);\n  }\n}\nfunction ImageZoomComponent_div_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.checkUsername(), \" \");\n  }\n}\nfunction ImageZoomComponent_div_1_div_13_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 35);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_1_div_13_ng_template_5_Template_img_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const img_r16 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.selectImage(img_r16.image));\n    })(\"error\", function ImageZoomComponent_div_1_div_13_ng_template_5_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const img_r16 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"alt\", ctx_r14.product.productName)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, img_r16.id === ctx_r14.selectedImage.id))(\"src\", ctx_r14.getProductImages(img_r16.thumbnail), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ImageZoomComponent_div_1_div_13_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r15.getProductImages(ctx_r15.selectedImage.large_image_url), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ImageZoomComponent_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"div\", 29)(4, \"p-carousel\", 30);\n    i0.ɵɵtemplate(5, ImageZoomComponent_div_1_div_13_ng_template_5_Template, 1, 5, \"ng-template\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 32)(7, \"img\", 33);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_1_div_13_Template_img_click_7_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.showImageModal(ctx_r20.getProductImages(ctx_r20.selectedImage.large_image_url)));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-dialog\", 34);\n    i0.ɵɵlistener(\"visibleChange\", function ImageZoomComponent_div_1_div_13_Template_p_dialog_visibleChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.displayModal = $event);\n    });\n    i0.ɵɵtemplate(9, ImageZoomComponent_div_1_div_13_ng_template_9_Template, 2, 1, \"ng-template\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoplayInterval\", 3500)(\"circular\", true)(\"numScroll\", 1)(\"numVisible\", 5)(\"orientation\", \"vertical\")(\"showIndicators\", true)(\"showNavigators\", true)(\"value\", ctx_r12.images)(\"verticalViewPortHeight\", \"600px\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r12.getProductImages(ctx_r12.selectedImage.large_image_url), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(18, _c1));\n    i0.ɵɵproperty(\"visible\", ctx_r12.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(19, _c2))(\"draggable\", false)(\"draggable\", false)(\"modal\", true)(\"resizable\", false);\n  }\n}\nfunction ImageZoomComponent_div_1_div_14_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 35);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_1_div_14_ng_template_4_Template_img_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const img_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.selectImage(img_r24.image));\n    })(\"error\", function ImageZoomComponent_div_1_div_14_ng_template_4_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const img_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"alt\", ctx_r23.product.productName)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, img_r24.id === ctx_r23.selectedImage.id))(\"src\", ctx_r23.getProductImages(img_r24.thumbnail), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ImageZoomComponent_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"img\", 38);\n    i0.ɵɵlistener(\"click\", function ImageZoomComponent_div_1_div_14_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.showImageModal(ctx_r28.getProductImages(ctx_r28.selectedImage.large_image_url)));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 39)(3, \"p-carousel\", 30);\n    i0.ɵɵtemplate(4, ImageZoomComponent_div_1_div_14_ng_template_4_Template, 1, 5, \"ng-template\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r13.getProductImages(ctx_r13.selectedImage.large_image_url), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoplayInterval\", 3500)(\"circular\", true)(\"numScroll\", 2)(\"numVisible\", 3)(\"orientation\", \"horizontal\")(\"showIndicators\", true)(\"showNavigators\", true)(\"value\", ctx_r13.images)(\"verticalViewPortHeight\", \"600px\");\n  }\n}\nfunction ImageZoomComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"p\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵelement(6, \"em\", 18);\n    i0.ɵɵelementStart(7, \"div\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, ImageZoomComponent_div_1_div_11_Template, 3, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"section\", 3);\n    i0.ɵɵtemplate(13, ImageZoomComponent_div_1_div_13_Template, 11, 20, \"div\", 22);\n    i0.ɵɵtemplate(14, ImageZoomComponent_div_1_div_14_Template, 5, 10, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.product.name, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.variant.rate ? ctx_r1.variant.rate : 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.variant.count ? ctx_r1.variant.count : 0, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.product.soldOut);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth > 720);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth < 720);\n  }\n}\nexport class ImageZoomComponent {\n  constructor(modalService, translate, productService, permissionService, platformId) {\n    this.modalService = modalService;\n    this.translate = translate;\n    this.productService = productService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    this.product = {};\n    this.variant = {};\n    this.selectedImage = {};\n    this.displayModal = false;\n    this.modalImage = '';\n    this.images = [];\n    this.isLayoutTemplate = false;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.baseUrl = environment.apiEndPoint + '/';\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n  }\n  ngOnInit() {\n    /**/\n  }\n  ngOnChanges() {\n    this.images = [];\n    this.variant?.thumbnailImages?.forEach((element, index) => {\n      let img = {\n        'thumbnail': element,\n        'image': this.variant.images[index]\n      };\n      this.images.push(img);\n    });\n    this.selectedImage = {\n      id: 0,\n      path: '',\n      url: '',\n      original_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\n      large_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\n      medium_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\n      small_image_url: this.variant?.images ? this.variant?.images[0] : \"\"\n    };\n    let current = this.variant.images;\n    this.variant.images = [];\n    for (let image of current) {\n      this.variant.images.push(image);\n    }\n  }\n  selectImage(imageObj) {\n    this.selectedImage.original_image_url = imageObj + '';\n    this.selectedImage.large_image_url = imageObj + '';\n    this.selectedImage.medium_image_url = imageObj + '';\n    this.selectedImage.small_image_url = imageObj + '';\n  }\n  getImage(imageLink) {\n    if (imageLink) {\n      if (imageLink[0] === '/') imageLink = imageLink.substring(1);\n      const sub = imageLink.substring(0, imageLink.indexOf('/'));\n      if (sub.toLowerCase().includes('images')) {\n        return `${this.baseUrl}${imageLink}`;\n      } else {\n        return `${this.baseUrl}Images/${imageLink}`;\n      }\n    } else {\n      return '';\n    }\n  }\n  showImageModal(img) {\n    this.displayModal = true;\n  }\n  errorHandler(event) {\n    if (environment.isStoreCloud) {\n      event.target.src = \"assets/images/placeholder.png\";\n    } else {\n      event.target.src = \"assets/images/mtn-alt.png\";\n    }\n  }\n  checkUsername() {\n    return this.translate.instant('productCard.instock');\n  }\n  getProductImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  static #_ = this.ɵfac = function ImageZoomComponent_Factory(t) {\n    return new (t || ImageZoomComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.ProductService), i0.ɵɵdirectiveInject(i3.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ImageZoomComponent,\n    selectors: [[\"app-image-zoom\"]],\n    hostBindings: function ImageZoomComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function ImageZoomComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      product: \"product\",\n      variant: \"variant\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"new-product-images\", 4, \"ngIf\"], [\"class\", \"old-product-images\", 4, \"ngIf\"], [1, \"new-product-images\"], [1, \"product-images\"], [1, \"product-images__selectedImage\"], [\"alt\", \"No Image\", \"alt\", \"No Image\", 3, \"src\", \"click\"], [1, \"d-flex\", \"flex-row\", \"justify-content-start\", \"product-images__images\"], [\"class\", \"product-images__images__variantImages\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-images__selectedImage__product-view\", 3, \"visible\", \"breakpoints\", \"draggable\", \"modal\", \"resizable\", \"visibleChange\"], [\"pTemplate\", \"content\"], [1, \"product-images__images__variantImages\"], [3, \"alt\", \"ngClass\", \"src\", \"click\", \"error\"], [\"alt\", \"show Image\", \"height\", \"100%\", \"width\", \"100%\", 1, \"product-images__selectedImage__product-image-modal\", 3, \"src\"], [1, \"old-product-images\"], [1, \"desktop-display-none\", \"mb-3\"], [1, \"product-name\", \"mb-0\", \"desktop-display-none\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"justify-content-between\", \"mt-2\"], [1, \"rating\", \"mt-1\", \"mx-2\", \"desktop-display-none\"], [1, \"star\", \"pi\", \"pi-star-fill\"], [1, \"rate\", \"mx-1\"], [1, \"rating-number\"], [\"class\", \"in-stock\", 4, \"ngIf\"], [\"class\", \"grid justify-content-center align-items-center\", 4, \"ngIf\"], [\"class\", \"d-flex flex-column mobile-product-image-section\", 4, \"ngIf\"], [1, \"in-stock\"], [1, \"dot-6\", \"bg-green-400\", \"green-dot\"], [1, \"grid\", \"justify-content-center\", \"align-items-center\"], [1, \"col-1\"], [1, \"justify-content-center\", \"align-items-center\", \"text-center\"], [1, \"images-container\"], [3, \"autoplayInterval\", \"circular\", \"numScroll\", \"numVisible\", \"orientation\", \"showIndicators\", \"showNavigators\", \"value\", \"verticalViewPortHeight\"], [\"pTemplate\", \"item\"], [1, \"col-6\", \"image-container\"], [\"alt\", \"No Image\", \"type\", \"button\", 1, \"main-image-box\", 3, \"src\", \"click\"], [1, \"prouct-view\", 3, \"visible\", \"breakpoints\", \"draggable\", \"modal\", \"resizable\", \"visibleChange\"], [1, \"image\", \"my-3\", 3, \"alt\", \"ngClass\", \"src\", \"click\", \"error\"], [\"alt\", \"show Image\", \"height\", \"100%\", \"width\", \"100%\", 1, \"product-image-modal\", 3, \"src\"], [1, \"d-flex\", \"flex-column\", \"mobile-product-image-section\"], [\"alt\", \"\", \"type\", \"button\", 1, \"mobile-main-image\", 3, \"src\", \"click\"], [1, \"mobile-image-slider\"]],\n    template: function ImageZoomComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ImageZoomComponent_div_0_Template, 8, 12, \"div\", 0);\n        i0.ɵɵtemplate(1, ImageZoomComponent_div_1_Template, 15, 6, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.PrimeTemplate, i6.Carousel, i7.Dialog],\n    styles: [\".new-product-images[_ngcontent-%COMP%]   .product-images[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 561px;\\n  padding: 0px 20px;\\n  justify-content: center;\\n  align-items: flex-start;\\n  align-content: flex-start;\\n  gap: 24px;\\n  flex-shrink: 0;\\n  flex-wrap: wrap;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images__selectedImage[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 460px;\\n  height: 374px;\\n  padding-top: 24px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 1px;\\n  flex-shrink: 0;\\n  border-radius: 4px;\\n  background: var(--colors-fff, #FFF);\\n  \\n\\n  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.12), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 1px 0px rgba(0, 0, 0, 0.2);\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images__selectedImage[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 278px;\\n  height: 243.6px;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images__images[_ngcontent-%COMP%] {\\n  gap: 8px;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images__images__variantImages[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  border: 1px solid #A3A3A3;\\n  background: #FFF;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images__images__variantImages[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100px;\\n  padding: 2px;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images_product-view[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  position: absolute !important;\\n}\\n.new-product-images[_ngcontent-%COMP%]   .product-images_product-view__product-image-modal[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 500px;\\n}\\n\\n.old-product-images[_ngcontent-%COMP%]   .product-images[_ngcontent-%COMP%]   .images-container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 50px;\\n  object-fit: contain;\\n  cursor: pointer;\\n}\\n.old-product-images[_ngcontent-%COMP%]   .main-image-box[_ngcontent-%COMP%] {\\n  width: 380px;\\n  object-fit: contain;\\n  position: inherit;\\n  margin-bottom: 17rem;\\n}\\n.old-product-images[_ngcontent-%COMP%]     .ngxImageZoomContainer {\\n  width: 100% !important;\\n  height: 100% !important;\\n}\\n.old-product-images[_ngcontent-%COMP%]     .ngxImageZoomThumbnail {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: contain;\\n}\\n.old-product-images[_ngcontent-%COMP%]     .p-carousel-vertical .p-carousel-items-container {\\n  flex-direction: column !important;\\n  height: 30% !important;\\n}\\n@media screen and (min-width: 769px) {\\n  .old-product-images[_ngcontent-%COMP%]   .desktop-display-none[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .old-product-images[_ngcontent-%COMP%]     .ngxImageZoomContainer {\\n    width: 100% !important;\\n    height: 50% !important;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]     .ngxImageZoomThumbnail {\\n    width: 100%;\\n    height: 50%;\\n    object-fit: contain;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .grid.justify-content-center.align-items-center[_ngcontent-%COMP%] {\\n    justify-content: left !important;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .main-image-box[_ngcontent-%COMP%] {\\n    width: 196px;\\n    margin-bottom: 0px !important;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    margin-bottom: 0rem !important;\\n    text-align: end;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .mobile-product-image-section[_ngcontent-%COMP%]   .mobile-main-image[_ngcontent-%COMP%] {\\n    height: 230px;\\n    width: 100%;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .mobile-product-image-section[_ngcontent-%COMP%]   .mobile-image-slider[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .mobile-product-image-section[_ngcontent-%COMP%]   .mobile-image-slider[_ngcontent-%COMP%]   .p-carousel-indicators[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .green-dot[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    display: inline-block;\\n    border-radius: 50%;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    font-weight: 400;\\n    color: #000000;\\n    font-family: var(--regular-font) !important;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-number[_ngcontent-%COMP%] {\\n    color: #a3a3a3;\\n    font-size: 15px;\\n    font-family: var(--light-font);\\n    font-weight: 300;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]   .in-stock[_ngcontent-%COMP%] {\\n    color: #01b467;\\n    font-size: 12px;\\n    font-family: var(--medium-font);\\n    font-weight: 500;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]     .breadcrumb {\\n    margin-top: 0rem !important;\\n    margin-bottom: 0px;\\n  }\\n  .old-product-images[_ngcontent-%COMP%]     .product-details {\\n    margin-top: 0px !important;\\n  }\\n}\\n.old-product-images[_ngcontent-%COMP%]     .prouct-view {\\n  z-index: 9999 !important;\\n  position: absolute !important;\\n}\\n.old-product-images[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  margin-bottom: 8rem;\\n  text-align: end;\\n}\\n.old-product-images[_ngcontent-%COMP%]     ul.p-carousel-indicators.p-reset.ng-star-inserted {\\n  display: none;\\n}\\n.old-product-images[_ngcontent-%COMP%]     button.p-ripple.p-element.p-carousel-prev.p-link.ng-star-inserted {\\n  display: none;\\n}\\n.old-product-images[_ngcontent-%COMP%]     button.p-ripple.p-element.p-carousel-next.p-link.ng-star-inserted {\\n  display: none;\\n}\\n.old-product-images[_ngcontent-%COMP%]     .p-dialog-content {\\n  border-bottom: none;\\n  border-radius: unset;\\n}\\n.old-product-images[_ngcontent-%COMP%]   .product-image-modal[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 500px;\\n}\\n.old-product-images[_ngcontent-%COMP%]     .p-dialog-header {\\n  padding-bottom: 0px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "environment", "isPlatformBrowser", "UtilityFunctions", "i0", "ɵɵelementStart", "ɵɵlistener", "ImageZoomComponent_div_0_div_5_Template_img_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r6", "img_r4", "$implicit", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "selectImage", "image", "ImageZoomComponent_div_0_div_5_Template_img_error_1_listener", "$event", "ctx_r7", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r2", "product", "productName", "ɵɵpureFunction1", "_c0", "id", "selectedImage", "getProductImages", "thumbnail", "ɵɵsanitizeUrl", "ɵɵelement", "ctx_r3", "large_image_url", "ImageZoomComponent_div_0_Template_img_click_3_listener", "_r9", "ctx_r8", "showImageModal", "ɵɵtemplate", "ImageZoomComponent_div_0_div_5_Template", "ImageZoomComponent_div_0_Template_p_dialog_visibleChange_6_listener", "ctx_r10", "displayModal", "ImageZoomComponent_div_0_ng_template_7_Template", "ctx_r0", "images", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "_c2", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r11", "checkUsername", "ImageZoomComponent_div_1_div_13_ng_template_5_Template_img_click_0_listener", "_r18", "img_r16", "ctx_r17", "ImageZoomComponent_div_1_div_13_ng_template_5_Template_img_error_0_listener", "ctx_r19", "ctx_r14", "ctx_r15", "ImageZoomComponent_div_1_div_13_ng_template_5_Template", "ImageZoomComponent_div_1_div_13_Template_img_click_7_listener", "_r21", "ctx_r20", "ImageZoomComponent_div_1_div_13_Template_p_dialog_visibleChange_8_listener", "ctx_r22", "ImageZoomComponent_div_1_div_13_ng_template_9_Template", "ctx_r12", "ImageZoomComponent_div_1_div_14_ng_template_4_Template_img_click_0_listener", "_r26", "img_r24", "ctx_r25", "ImageZoomComponent_div_1_div_14_ng_template_4_Template_img_error_0_listener", "ctx_r27", "ctx_r23", "ImageZoomComponent_div_1_div_14_Template_img_click_1_listener", "_r29", "ctx_r28", "ImageZoomComponent_div_1_div_14_ng_template_4_Template", "ctx_r13", "ImageZoomComponent_div_1_div_11_Template", "ImageZoomComponent_div_1_div_13_Template", "ImageZoomComponent_div_1_div_14_Template", "ctx_r1", "name", "ɵɵtextInterpolate", "variant", "rate", "count", "soldOut", "screenWidth", "ImageZoomComponent", "constructor", "modalService", "translate", "productService", "permissionService", "platformId", "modalImage", "isLayoutTemplate", "hasPermission", "baseUrl", "apiEndPoint", "window", "innerWidth", "onResize", "event", "target", "ngOnInit", "ngOnChanges", "thumbnailImages", "for<PERSON>ach", "element", "index", "img", "push", "path", "url", "original_image_url", "medium_image_url", "small_image_url", "current", "imageObj", "getImage", "imageLink", "substring", "sub", "indexOf", "toLowerCase", "includes", "isStoreCloud", "src", "instant", "verifyImageURL", "_", "ɵɵdirectiveInject", "i1", "BsModalService", "i2", "TranslateService", "i3", "ProductService", "PermissionService", "_2", "selectors", "hostBindings", "ImageZoomComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ImageZoomComponent_div_0_Template", "ImageZoomComponent_div_1_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\image-zoom\\image-zoom.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\image-zoom\\image-zoom.component.html"], "sourcesContent": ["import {Component, HostListener, Inject, Input, OnChanges, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport {environment} from '@environments/environment';\r\nimport {Product, ProductImage} from '@core/interface';\r\nimport {BsModalService} from 'ngx-bootstrap/modal';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {PermissionService,ProductService} from \"@core/services\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\n\r\n@Component({\r\n  selector: 'app-image-zoom',\r\n  templateUrl: './image-zoom.component.html',\r\n  styleUrls: ['./image-zoom.component.scss'],\r\n})\r\nexport class ImageZoomComponent implements OnInit, OnChanges {\r\n  @Input() product: Product = {} as Product;\r\n  @Input() variant: any = {};\r\n  selectedImage: ProductImage = {} as ProductImage;\r\n  baseUrl: string;\r\n  displayModal: boolean = false;\r\n  modalImage: string = '';\r\n  screenWidth: number;\r\n  images: any = [];\r\n  isLayoutTemplate: boolean = false;\r\n  constructor(private modalService: BsModalService, private translate: TranslateService, private productService: ProductService,\r\n              private permissionService: PermissionService,@Inject(PLATFORM_ID) private platformId: any,) {\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')\r\n    this.baseUrl = environment.apiEndPoint + '/';\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: any) {\r\n      this.screenWidth = event.target.innerWidth;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    this.images = [];\r\n    this.variant?.thumbnailImages?.forEach((element: any, index: any) => {\r\n      let img = {'thumbnail': element, 'image': this.variant.images[index]};\r\n      this.images.push(img);\r\n    });\r\n\r\n    this.selectedImage = {\r\n      id: 0,\r\n      path: '',\r\n      url: '',\r\n      original_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\r\n      large_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\r\n      medium_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\r\n      small_image_url: this.variant?.images ? this.variant?.images[0] : \"\",\r\n    };\r\n\r\n\r\n    let current = this.variant.images;\r\n\r\n    this.variant.images = [];\r\n\r\n    for (let image of current) {\r\n      this.variant.images.push(image);\r\n    }\r\n  }\r\n\r\n  selectImage(imageObj: any): void {\r\n\r\n\r\n    this.selectedImage.original_image_url = imageObj + '';\r\n    this.selectedImage.large_image_url = imageObj + '';\r\n    this.selectedImage.medium_image_url = imageObj + '';\r\n    this.selectedImage.small_image_url = imageObj + '';\r\n  }\r\n\r\n  getImage(imageLink: any) {\r\n    if (imageLink) {\r\n      if (imageLink[0] === '/') imageLink = imageLink.substring(1);\r\n      const sub = imageLink.substring(0, imageLink.indexOf('/'));\r\n      if (sub.toLowerCase().includes('images')) {\r\n\r\n        return `${this.baseUrl}${imageLink}`;\r\n      } else {\r\n\r\n\r\n        return `${this.baseUrl}Images/${imageLink}`;\r\n      }\r\n    } else {\r\n      return '';\r\n    }\r\n  }\r\n\r\n  showImageModal(img: string) {\r\n    this.displayModal = true;\r\n  }\r\n\r\n  errorHandler(event: any) {\r\n    if (environment.isStoreCloud) {\r\n      event.target.src = \"assets/images/placeholder.png\";\r\n\r\n    } else {\r\n      event.target.src = \"assets/images/mtn-alt.png\";\r\n\r\n    }\r\n  }\r\n\r\n  checkUsername() {\r\n\r\n\r\n    return this.translate.instant('productCard.instock');\r\n  }\r\n\r\n  getProductImages(url: string) {\r\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\r\n  }\r\n}\r\n", "<div class=\"new-product-images\" *ngIf=\"isLayoutTemplate\">\r\n<div class=\"product-images\">\r\n  <div class=\"product-images__selectedImage\">\r\n    <img\r\n          alt=\"No Image\"\r\n          (click)=\"showImageModal(getProductImages(selectedImage.large_image_url))\"\r\n          [src]=\"getProductImages(selectedImage.large_image_url)\" alt=\"No Image\"\r\n    >\r\n\r\n  </div>\r\n  <div class=\"d-flex flex-row justify-content-start product-images__images\">\r\n    <div class=\"product-images__images__variantImages\" *ngFor=\"let img of images\">\r\n\r\n        <img  (click)=\"selectImage(img.image)\"\r\n              (error)=\"errorHandler($event)\"\r\n              [alt]=\"product.productName\"\r\n              [ngClass]=\"{ active: img.id === selectedImage.id }\"\r\n              [src]=\"getProductImages(img.thumbnail)\">\r\n\r\n\r\n\r\n    </div>\r\n\r\n  </div>\r\n  <p-dialog\r\n    [(visible)]=\"displayModal\"\r\n    [breakpoints]=\"{ '960px': '75vw', '640px': '90vw' }\"\r\n    [draggable]=\"false\"\r\n    [draggable]=\"false\"\r\n    [modal]=\"true\"\r\n    [resizable]=\"false\"\r\n    [style]=\"{ width: '40vw' }\"\r\n    class=\"product-images__selectedImage__product-view\"\r\n  >\r\n    <ng-template pTemplate=\"content\">\r\n      <div>\r\n        <img\r\n          [src]=\"getProductImages(selectedImage.large_image_url)\"\r\n          alt=\"show Image\"\r\n          class=\"product-images__selectedImage__product-image-modal\"\r\n          height=\"100%\"\r\n          width=\"100%\"\r\n        />\r\n      </div>\r\n    </ng-template>\r\n  </p-dialog>\r\n\r\n</div>\r\n</div>\r\n<div class=\"old-product-images\" *ngIf=\"!isLayoutTemplate\">\r\n  <div class=\"desktop-display-none mb-3\">\r\n    <p class=\"product-name mb-0 desktop-display-none\">\r\n      {{ product.name }}\r\n    </p>\r\n    <div class=\"flex flex-row align-items-center justify-content-between mt-2\">\r\n      <div class=\"rating mt-1 mx-2 desktop-display-none\">\r\n        <em class=\"star pi pi-star-fill\"></em>\r\n        <div class=\"rate mx-1\">{{ variant.rate ? variant.rate : 0 }}</div>\r\n        <div class=\"rating-number\">({{ variant.count ? variant.count : 0 }})</div>\r\n      </div>\r\n      <div *ngIf=\"!product.soldOut\" class=\"in-stock\">\r\n\r\n        <div class=\"dot-6 bg-green-400 green-dot\"></div>\r\n        {{ checkUsername() }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <section class=\"product-images\">\r\n    <div\r\n      *ngIf=\"screenWidth > 720\"\r\n      class=\"grid justify-content-center align-items-center\"\r\n    >\r\n      <div class=\"col-1\"></div>\r\n      <div class=\"justify-content-center align-items-center text-center\">\r\n        <div class=\"images-container\">\r\n          <p-carousel\r\n            [autoplayInterval]=\"3500\"\r\n            [circular]=\"true\"\r\n            [numScroll]=\"1\"\r\n            [numVisible]=\"5\"\r\n            [orientation]=\"'vertical'\"\r\n            [showIndicators]=\"true\"\r\n            [showNavigators]=\"true\"\r\n            [value]=\"images\"\r\n            [verticalViewPortHeight]=\"'600px'\"\r\n          >\r\n            <ng-template let-img pTemplate=\"item\">\r\n              <img\r\n\r\n                (click)=\"selectImage(img.image)\"\r\n                (error)=\"errorHandler($event)\"\r\n                [alt]=\"product.productName\"\r\n                [ngClass]=\"{ active: img.id === selectedImage.id }\"\r\n                [src]=\"getProductImages(img.thumbnail)\"\r\n                class=\"image my-3\"\r\n              />\r\n            </ng-template>\r\n          </p-carousel>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-6 image-container\">\r\n        <img\r\n          (click)=\"\r\n          showImageModal(getProductImages(selectedImage.large_image_url))\r\n        \"\r\n          [src]=\"getProductImages(selectedImage.large_image_url)\"\r\n          alt=\"No Image\"\r\n          class=\"main-image-box\"\r\n          type=\"button\"\r\n        />\r\n\r\n\r\n        <p-dialog\r\n          [(visible)]=\"displayModal\"\r\n          [breakpoints]=\"{ '960px': '75vw', '640px': '90vw' }\"\r\n          [draggable]=\"false\"\r\n          [draggable]=\"false\"\r\n          [modal]=\"true\"\r\n          [resizable]=\"false\"\r\n          [style]=\"{ width: '40vw' }\"\r\n          class=\"prouct-view\"\r\n        >\r\n          <ng-template pTemplate=\"content\">\r\n            <div>\r\n              <img\r\n                [src]=\"getProductImages(selectedImage.large_image_url)\"\r\n                alt=\"show Image\"\r\n                class=\"product-image-modal\"\r\n                height=\"100%\"\r\n                width=\"100%\"\r\n              />\r\n            </div>\r\n          </ng-template>\r\n        </p-dialog>\r\n      </div>\r\n      <div class=\"col-1\"></div>\r\n    </div>\r\n\r\n    <div\r\n      *ngIf=\"screenWidth < 720\"\r\n      class=\"d-flex flex-column mobile-product-image-section\"\r\n    >\r\n      <img\r\n        (click)=\"showImageModal(getProductImages(selectedImage.large_image_url))\"\r\n        [src]=\"getProductImages(selectedImage.large_image_url)\"\r\n        alt=\"\"\r\n        class=\"mobile-main-image\"\r\n        type=\"button\"\r\n      />\r\n      <div class=\"mobile-image-slider\">\r\n\r\n        <p-carousel\r\n          [autoplayInterval]=\"3500\"\r\n          [circular]=\"true\"\r\n          [numScroll]=\"2\"\r\n          [numVisible]=\"3\"\r\n          [orientation]=\"'horizontal'\"\r\n          [showIndicators]=\"true\"\r\n          [showNavigators]=\"true\"\r\n          [value]=\"images\"\r\n          [verticalViewPortHeight]=\"'600px'\"\r\n        >\r\n          <ng-template let-img pTemplate=\"item\">\r\n            <img\r\n              (click)=\"selectImage(img.image)\"\r\n              (error)=\"errorHandler($event)\"\r\n              [alt]=\"product.productName\"\r\n              [ngClass]=\"{ active: img.id === selectedImage.id }\"\r\n              [src]=\"getProductImages(img.thumbnail)\"\r\n              class=\"image my-3\"\r\n            />\r\n          </ng-template>\r\n        </p-carousel>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</div>\r\n"], "mappings": "AAAA,SAAmEA,WAAW,QAAO,eAAe;AACpG,SAAQC,WAAW,QAAO,2BAA2B;AAKrD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,OAAOC,gBAAgB,MAAM,2BAA2B;;;;;;;;;;;;;;;;;ICIpDC,EAAA,CAAAC,cAAA,cAA8E;IAEpED,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,MAAA,CAAAM,KAAA,CAAsB;IAAA,EAAC,mBAAAC,6DAAAC,MAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAU,MAAA,GAAAhB,EAAA,CAAAU,aAAA;MAAA,OACvBV,EAAA,CAAAW,WAAA,CAAAK,MAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EADG;IAAtCf,EAAA,CAAAkB,YAAA,EAI8C;;;;;IAFxClB,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAAoB,UAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAC,WAAA,CAA2B,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAlB,MAAA,CAAAmB,EAAA,KAAAL,MAAA,CAAAM,aAAA,CAAAD,EAAA,UAAAL,MAAA,CAAAO,gBAAA,CAAArB,MAAA,CAAAsB,SAAA,GAAA7B,EAAA,CAAA8B,aAAA;;;;;IAoBnC9B,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAA+B,SAAA,cAME;IACJ/B,EAAA,CAAAkB,YAAA,EAAM;;;;IANFlB,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,UAAA,QAAAY,MAAA,CAAAJ,gBAAA,CAAAI,MAAA,CAAAL,aAAA,CAAAM,eAAA,GAAAjC,EAAA,CAAA8B,aAAA,CAAuD;;;;;;;;;;;;;;;;;IArCjE9B,EAAA,CAAAC,cAAA,aAAyD;IAK/CD,EAAA,CAAAE,UAAA,mBAAAgC,uDAAA;MAAAlC,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAAC,MAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAyB,MAAA,CAAAC,cAAA,CAAeD,MAAA,CAAAR,gBAAA,CAAAQ,MAAA,CAAAT,aAAA,CAAAM,eAAA,CAA+C,CAAC;IAAA,EAAC;IAF/EjC,EAAA,CAAAkB,YAAA,EAIC;IAGHlB,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAsC,UAAA,IAAAC,uCAAA,iBAUM;IAERvC,EAAA,CAAAkB,YAAA,EAAM;IACNlB,EAAA,CAAAC,cAAA,kBASC;IARCD,EAAA,CAAAE,UAAA,2BAAAsC,oEAAAzB,MAAA;MAAAf,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAAM,OAAA,GAAAzC,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA8B,OAAA,CAAAC,YAAA,GAAA3B,MAAA;IAAA,EAA0B;IAS1Bf,EAAA,CAAAsC,UAAA,IAAAK,+CAAA,yBAUc;IAChB3C,EAAA,CAAAkB,YAAA,EAAW;;;;IAvCHlB,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,UAAA,QAAAwB,MAAA,CAAAhB,gBAAA,CAAAgB,MAAA,CAAAjB,aAAA,CAAAM,eAAA,GAAAjC,EAAA,CAAA8B,aAAA,CAAuD;IAKM9B,EAAA,CAAAmB,SAAA,GAAS;IAATnB,EAAA,CAAAoB,UAAA,YAAAwB,MAAA,CAAAC,MAAA,CAAS;IAoB5E7C,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA8C,UAAA,CAAA9C,EAAA,CAAA+C,eAAA,KAAAC,GAAA,EAA2B;IAN3BhD,EAAA,CAAAoB,UAAA,YAAAwB,MAAA,CAAAF,YAAA,CAA0B,gBAAA1C,EAAA,CAAA+C,eAAA,KAAAE,GAAA;;;;;IAmCxBjD,EAAA,CAAAC,cAAA,cAA+C;IAE7CD,EAAA,CAAA+B,SAAA,cAAgD;IAChD/B,EAAA,CAAAkD,MAAA,GACF;IAAAlD,EAAA,CAAAkB,YAAA,EAAM;;;;IADJlB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAC,OAAA,CAAAC,aAAA,QACF;;;;;;IAuBQrD,EAAA,CAAAC,cAAA,cAQE;IANAD,EAAA,CAAAE,UAAA,mBAAAoD,4EAAA;MAAA,MAAAlD,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAApD,WAAA,CAAAI,SAAA;MAAA,MAAAiD,OAAA,GAAAzD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA8C,OAAA,CAAA7C,WAAA,CAAA4C,OAAA,CAAA3C,KAAA,CAAsB;IAAA,EAAC,mBAAA6C,4EAAA3C,MAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAkD,IAAA;MAAA,MAAAI,OAAA,GAAA3D,EAAA,CAAAU,aAAA;MAAA,OACvBV,EAAA,CAAAW,WAAA,CAAAgD,OAAA,CAAA1C,YAAA,CAAAF,MAAA,CAAoB;IAAA,EADG;IAFlCf,EAAA,CAAAkB,YAAA,EAQE;;;;;IAJAlB,EAAA,CAAAoB,UAAA,QAAAwC,OAAA,CAAAtC,OAAA,CAAAC,WAAA,CAA2B,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAA+B,OAAA,CAAA9B,EAAA,KAAAkC,OAAA,CAAAjC,aAAA,CAAAD,EAAA,UAAAkC,OAAA,CAAAhC,gBAAA,CAAA4B,OAAA,CAAA3B,SAAA,GAAA7B,EAAA,CAAA8B,aAAA;;;;;IAgC/B9B,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAA+B,SAAA,cAME;IACJ/B,EAAA,CAAAkB,YAAA,EAAM;;;;IANFlB,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,UAAA,QAAAyC,OAAA,CAAAjC,gBAAA,CAAAiC,OAAA,CAAAlC,aAAA,CAAAM,eAAA,GAAAjC,EAAA,CAAA8B,aAAA,CAAuD;;;;;;IAzDnE9B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA+B,SAAA,cAAyB;IACzB/B,EAAA,CAAAC,cAAA,cAAmE;IAa7DD,EAAA,CAAAsC,UAAA,IAAAwB,sDAAA,0BAUc;IAChB9D,EAAA,CAAAkB,YAAA,EAAa;IAGjBlB,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,UAAA,mBAAA6D,8DAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAU,aAAA;MAAA,OACWV,EAAA,CAAAW,WAAA,CAAAsD,OAAA,CAAA5B,cAAA,CAAe4B,OAAA,CAAArC,gBAAA,CAAAqC,OAAA,CAAAtC,aAAA,CAAAM,eAAA,CAC5B,CAAC;IAAA;IAHDjC,EAAA,CAAAkB,YAAA,EAQE;IAGFlB,EAAA,CAAAC,cAAA,mBASC;IARCD,EAAA,CAAAE,UAAA,2BAAAgE,2EAAAnD,MAAA;MAAAf,EAAA,CAAAK,aAAA,CAAA2D,IAAA;MAAA,MAAAG,OAAA,GAAAnE,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAwD,OAAA,CAAAzB,YAAA,GAAA3B,MAAA;IAAA,EAA0B;IAS1Bf,EAAA,CAAAsC,UAAA,IAAA8B,sDAAA,yBAUc;IAChBpE,EAAA,CAAAkB,YAAA,EAAW;IAEblB,EAAA,CAAA+B,SAAA,eAAyB;IAC3B/B,EAAA,CAAAkB,YAAA,EAAM;;;;IA5DElB,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAoB,UAAA,0BAAyB,wIAAAiD,OAAA,CAAAxB,MAAA;IA6B3B7C,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,UAAA,QAAAiD,OAAA,CAAAzC,gBAAA,CAAAyC,OAAA,CAAA1C,aAAA,CAAAM,eAAA,GAAAjC,EAAA,CAAA8B,aAAA,CAAuD;IAcvD9B,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA8C,UAAA,CAAA9C,EAAA,CAAA+C,eAAA,KAAAC,GAAA,EAA2B;IAN3BhD,EAAA,CAAAoB,UAAA,YAAAiD,OAAA,CAAA3B,YAAA,CAA0B,gBAAA1C,EAAA,CAAA+C,eAAA,KAAAE,GAAA;;;;;;IAkDxBjD,EAAA,CAAAC,cAAA,cAOE;IANAD,EAAA,CAAAE,UAAA,mBAAAoE,4EAAA;MAAA,MAAAlE,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAApE,WAAA,CAAAI,SAAA;MAAA,MAAAiE,OAAA,GAAAzE,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA8D,OAAA,CAAA7D,WAAA,CAAA4D,OAAA,CAAA3D,KAAA,CAAsB;IAAA,EAAC,mBAAA6D,4EAAA3D,MAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAkE,IAAA;MAAA,MAAAI,OAAA,GAAA3E,EAAA,CAAAU,aAAA;MAAA,OACvBV,EAAA,CAAAW,WAAA,CAAAgE,OAAA,CAAA1D,YAAA,CAAAF,MAAA,CAAoB;IAAA,EADG;IADlCf,EAAA,CAAAkB,YAAA,EAOE;;;;;IAJAlB,EAAA,CAAAoB,UAAA,QAAAwD,OAAA,CAAAtD,OAAA,CAAAC,WAAA,CAA2B,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAA+C,OAAA,CAAA9C,EAAA,KAAAkD,OAAA,CAAAjD,aAAA,CAAAD,EAAA,UAAAkD,OAAA,CAAAhD,gBAAA,CAAA4C,OAAA,CAAA3C,SAAA,GAAA7B,EAAA,CAAA8B,aAAA;;;;;;IA5BrC9B,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,UAAA,mBAAA2E,8DAAA;MAAA7E,EAAA,CAAAK,aAAA,CAAAyE,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAoE,OAAA,CAAA1C,cAAA,CAAe0C,OAAA,CAAAnD,gBAAA,CAAAmD,OAAA,CAAApD,aAAA,CAAAM,eAAA,CAA+C,CAAC;IAAA,EAAC;IAD3EjC,EAAA,CAAAkB,YAAA,EAME;IACFlB,EAAA,CAAAC,cAAA,cAAiC;IAa7BD,EAAA,CAAAsC,UAAA,IAAA0C,sDAAA,0BASc;IAChBhF,EAAA,CAAAkB,YAAA,EAAa;;;;IA5BblB,EAAA,CAAAmB,SAAA,GAAuD;IAAvDnB,EAAA,CAAAoB,UAAA,QAAA6D,OAAA,CAAArD,gBAAA,CAAAqD,OAAA,CAAAtD,aAAA,CAAAM,eAAA,GAAAjC,EAAA,CAAA8B,aAAA,CAAuD;IAQrD9B,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAoB,UAAA,0BAAyB,0IAAA6D,OAAA,CAAApC,MAAA;;;;;IAvGnC7C,EAAA,CAAAC,cAAA,cAA0D;IAGpDD,EAAA,CAAAkD,MAAA,GACF;IAAAlD,EAAA,CAAAkB,YAAA,EAAI;IACJlB,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAA+B,SAAA,aAAsC;IACtC/B,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAkD,MAAA,GAAqC;IAAAlD,EAAA,CAAAkB,YAAA,EAAM;IAClElB,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAkD,MAAA,IAAyC;IAAAlD,EAAA,CAAAkB,YAAA,EAAM;IAE5ElB,EAAA,CAAAsC,UAAA,KAAA4C,wCAAA,kBAIM;IACRlF,EAAA,CAAAkB,YAAA,EAAM;IAERlB,EAAA,CAAAC,cAAA,kBAAgC;IAC9BD,EAAA,CAAAsC,UAAA,KAAA6C,wCAAA,oBAoEM;IAENnF,EAAA,CAAAsC,UAAA,KAAA8C,wCAAA,mBAoCM;IACRpF,EAAA,CAAAkB,YAAA,EAAU;;;;IA3HNlB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAkC,MAAA,CAAA/D,OAAA,CAAAgE,IAAA,MACF;IAI2BtF,EAAA,CAAAmB,SAAA,GAAqC;IAArCnB,EAAA,CAAAuF,iBAAA,CAAAF,MAAA,CAAAG,OAAA,CAAAC,IAAA,GAAAJ,MAAA,CAAAG,OAAA,CAAAC,IAAA,KAAqC;IACjCzF,EAAA,CAAAmB,SAAA,GAAyC;IAAzCnB,EAAA,CAAAmD,kBAAA,MAAAkC,MAAA,CAAAG,OAAA,CAAAE,KAAA,GAAAL,MAAA,CAAAG,OAAA,CAAAE,KAAA,UAAyC;IAEhE1F,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAoB,UAAA,UAAAiE,MAAA,CAAA/D,OAAA,CAAAqE,OAAA,CAAsB;IAS3B3F,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAoB,UAAA,SAAAiE,MAAA,CAAAO,WAAA,OAAuB;IAsEvB5F,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAoB,UAAA,SAAAiE,MAAA,CAAAO,WAAA,OAAuB;;;AD7H9B,OAAM,MAAOC,kBAAkB;EAU7BC,YAAoBC,YAA4B,EAAUC,SAA2B,EAAUC,cAA8B,EACzGC,iBAAoC,EAA8BC,UAAe;IADjF,KAAAJ,YAAY,GAAZA,YAAY;IAA0B,KAAAC,SAAS,GAATA,SAAS;IAA4B,KAAAC,cAAc,GAAdA,cAAc;IACzF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAAiD,KAAAC,UAAU,GAAVA,UAAU;IAVvF,KAAA7E,OAAO,GAAY,EAAa;IAChC,KAAAkE,OAAO,GAAQ,EAAE;IAC1B,KAAA7D,aAAa,GAAiB,EAAkB;IAEhD,KAAAe,YAAY,GAAY,KAAK;IAC7B,KAAA0D,UAAU,GAAW,EAAE;IAEvB,KAAAvD,MAAM,GAAQ,EAAE;IAChB,KAAAwD,gBAAgB,GAAY,KAAK;IAG/B,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACH,iBAAiB,CAACI,aAAa,CAAC,iBAAiB,CAAC;IAC/E,IAAI,CAACC,OAAO,GAAG1G,WAAW,CAAC2G,WAAW,GAAG,GAAG;IAC5C,IAAI1G,iBAAiB,CAAC,IAAI,CAACqG,UAAU,CAAC,EAAE;MACtC,IAAI,CAACP,WAAW,GAAGa,MAAM,CAACC,UAAU;;EAExC;EAGAC,QAAQA,CAACC,KAAU;IACf,IAAI,CAAChB,WAAW,GAAGgB,KAAK,CAACC,MAAM,CAACH,UAAU;EAC9C;EAEAI,QAAQA,CAAA;IACN;EAAA;EAIFC,WAAWA,CAAA;IACT,IAAI,CAAClE,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC2C,OAAO,EAAEwB,eAAe,EAAEC,OAAO,CAAC,CAACC,OAAY,EAAEC,KAAU,KAAI;MAClE,IAAIC,GAAG,GAAG;QAAC,WAAW,EAAEF,OAAO;QAAE,OAAO,EAAE,IAAI,CAAC1B,OAAO,CAAC3C,MAAM,CAACsE,KAAK;MAAC,CAAC;MACrE,IAAI,CAACtE,MAAM,CAACwE,IAAI,CAACD,GAAG,CAAC;IACvB,CAAC,CAAC;IAEF,IAAI,CAACzF,aAAa,GAAG;MACnBD,EAAE,EAAE,CAAC;MACL4F,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,kBAAkB,EAAE,IAAI,CAAChC,OAAO,EAAE3C,MAAM,GAAG,IAAI,CAAC2C,OAAO,EAAE3C,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;MACvEZ,eAAe,EAAE,IAAI,CAACuD,OAAO,EAAE3C,MAAM,GAAG,IAAI,CAAC2C,OAAO,EAAE3C,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;MACpE4E,gBAAgB,EAAE,IAAI,CAACjC,OAAO,EAAE3C,MAAM,GAAG,IAAI,CAAC2C,OAAO,EAAE3C,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE;MACrE6E,eAAe,EAAE,IAAI,CAAClC,OAAO,EAAE3C,MAAM,GAAG,IAAI,CAAC2C,OAAO,EAAE3C,MAAM,CAAC,CAAC,CAAC,GAAG;KACnE;IAGD,IAAI8E,OAAO,GAAG,IAAI,CAACnC,OAAO,CAAC3C,MAAM;IAEjC,IAAI,CAAC2C,OAAO,CAAC3C,MAAM,GAAG,EAAE;IAExB,KAAK,IAAIhC,KAAK,IAAI8G,OAAO,EAAE;MACzB,IAAI,CAACnC,OAAO,CAAC3C,MAAM,CAACwE,IAAI,CAACxG,KAAK,CAAC;;EAEnC;EAEAD,WAAWA,CAACgH,QAAa;IAGvB,IAAI,CAACjG,aAAa,CAAC6F,kBAAkB,GAAGI,QAAQ,GAAG,EAAE;IACrD,IAAI,CAACjG,aAAa,CAACM,eAAe,GAAG2F,QAAQ,GAAG,EAAE;IAClD,IAAI,CAACjG,aAAa,CAAC8F,gBAAgB,GAAGG,QAAQ,GAAG,EAAE;IACnD,IAAI,CAACjG,aAAa,CAAC+F,eAAe,GAAGE,QAAQ,GAAG,EAAE;EACpD;EAEAC,QAAQA,CAACC,SAAc;IACrB,IAAIA,SAAS,EAAE;MACb,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,SAAS,GAAGA,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC;MAC5D,MAAMC,GAAG,GAAGF,SAAS,CAACC,SAAS,CAAC,CAAC,EAAED,SAAS,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;MAC1D,IAAID,GAAG,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAExC,OAAO,GAAG,IAAI,CAAC5B,OAAO,GAAGuB,SAAS,EAAE;OACrC,MAAM;QAGL,OAAO,GAAG,IAAI,CAACvB,OAAO,UAAUuB,SAAS,EAAE;;KAE9C,MAAM;MACL,OAAO,EAAE;;EAEb;EAEAzF,cAAcA,CAAC+E,GAAW;IACxB,IAAI,CAAC1E,YAAY,GAAG,IAAI;EAC1B;EAEAzB,YAAYA,CAAC2F,KAAU;IACrB,IAAI/G,WAAW,CAACuI,YAAY,EAAE;MAC5BxB,KAAK,CAACC,MAAM,CAACwB,GAAG,GAAG,+BAA+B;KAEnD,MAAM;MACLzB,KAAK,CAACC,MAAM,CAACwB,GAAG,GAAG,2BAA2B;;EAGlD;EAEAhF,aAAaA,CAAA;IAGX,OAAO,IAAI,CAAC2C,SAAS,CAACsC,OAAO,CAAC,qBAAqB,CAAC;EACtD;EAEA1G,gBAAgBA,CAAC2F,GAAW;IAC1B,OAAOxH,gBAAgB,CAACwI,cAAc,CAAChB,GAAG,EAAE1H,WAAW,CAAC2G,WAAW,CAAC;EACtE;EAAC,QAAAgC,CAAA,G;qBAxGU3C,kBAAkB,EAAA7F,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAE,iBAAA,GAAAhJ,EAAA,CAAAyI,iBAAA,CAWoC7I,WAAW;EAAA;EAAA,QAAAqJ,EAAA,G;UAXjEpD,kBAAkB;IAAAqD,SAAA;IAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAlBC,GAAA,CAAA3C,QAAA,CAAA5F,MAAA,CAAgB;QAAA,UAAAf,EAAA,CAAAuJ,eAAA;;;;;;;;;;;;;QCd7BvJ,EAAA,CAAAsC,UAAA,IAAAkH,iCAAA,kBAgDM;QACNxJ,EAAA,CAAAsC,UAAA,IAAAmH,iCAAA,kBA+HM;;;QAhL2BzJ,EAAA,CAAAoB,UAAA,SAAAkI,GAAA,CAAAjD,gBAAA,CAAsB;QAiDtBrG,EAAA,CAAAmB,SAAA,GAAuB;QAAvBnB,EAAA,CAAAoB,UAAA,UAAAkI,GAAA,CAAAjD,gBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}