import { PaymentMethodEnum } from "@core/enums/payment-method-enum";

export interface LightboxConfig {
  lightBoxURL: string;
  TrxDateTime: string;
  SecureHash: string;
  defaultPaymentMethod: PaymentMethodEnum;
  momoPaySubMerchantsDataStr: string;
  MomoPayCommissionAmount: number;
  MID: string;
  TID: string;
  lang: string | null;
  MerchantReference: string;
  AmountTrxn: number;
  AdditionalCustomerData: {
    CustomerMobile: string;
  };
  MomoPayLogisticFees: number;
  MomoPayDiscounts: number;
  paymentMethodFromLightBox: PaymentMethodEnum;
}
