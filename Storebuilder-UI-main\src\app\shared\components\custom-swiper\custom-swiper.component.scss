.carousel-wrapper {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
}

.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0,0,0,0.5);
  color: white;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 10px;
  z-index: 10;
}

.left {
  left: 10px;
}

.right {
  right: 10px;
}

.dots {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.dots span {
  width: 10px;
  height: 10px;
  margin: 0 5px;
  background-color: #ccc;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
}

.dots span.active {
  background-color: #333;
}
.carousel-wrapper {
  width: 100%;
  overflow: hidden; 
  position: relative;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease; 
}

.carousel-slide {
  flex-shrink: 0;  
  flex-grow: 0;  
  flex-basis: 100%; 
  height: 300px;  
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}
