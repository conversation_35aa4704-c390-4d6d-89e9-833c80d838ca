{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from '@shared/modules/shared.module';\nimport { RouterModule } from '@angular/router';\nimport { IndexComponent } from './components/index/index.component';\nimport { DetailsComponent } from './components/details/details.component';\nimport { routes } from './routes';\nimport { ImageZoomComponent } from './components/image-zoom/image-zoom.component';\nimport { NgxImageZoomModule } from \"ngx-image-zoom\";\nimport { CarouselModule } from \"primeng/carousel\";\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { TabViewModule } from 'primeng/tabview';\nimport { FloatingPanelComponent } from './components/floating-panel/floating-panel.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { DividerModule } from \"primeng/divider\";\nimport { RatingModule } from \"primeng/rating\";\nimport { ProgressBarModule } from \"primeng/progressbar\";\nimport { SellerInfoComponent } from './components/seller-info/seller-info.component';\nimport { IndexRevampComponent } from './components/index-revamp/index.component';\nimport { ProductImagesComponent } from './components/product-images/product-images.component';\nimport { ShareButtonsModule } from 'ngx-sharebuttons/buttons';\nimport { ShareIconsModule } from 'ngx-sharebuttons/icons';\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ProductDetailsModule {\n  static ɵfac = function ProductDetailsModule_Factory(t) {\n    return new (t || ProductDetailsModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProductDetailsModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, RouterModule.forChild(routes), NgxImageZoomModule, CarouselModule, TranslateModule, FormsModule, ReactiveFormsModule, DialogModule, InitialModule, DividerModule, RatingModule, ProgressBarModule, TabMenuModule, TabViewModule, ShareButtonsModule, ShareIconsModule,\n    // NgIf,\n    // NgForOf,\n    // NgForOf,\n    // NgIf,\n    BackButtonComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProductDetailsModule, {\n    declarations: [IndexComponent, DetailsComponent, ImageZoomComponent, FloatingPanelComponent, SellerInfoComponent, IndexRevampComponent, ProductImagesComponent],\n    imports: [CommonModule, SharedModule, i1.RouterModule, NgxImageZoomModule, CarouselModule, TranslateModule, FormsModule, ReactiveFormsModule, DialogModule, InitialModule, DividerModule, RatingModule, ProgressBarModule, TabMenuModule, TabViewModule, ShareButtonsModule, ShareIconsModule,\n    // NgIf,\n    // NgForOf,\n    // NgForOf,\n    // NgIf,\n    BackButtonComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SharedModule", "RouterModule", "IndexComponent", "DetailsComponent", "routes", "ImageZoomComponent", "NgxImageZoomModule", "CarouselModule", "TabMenuModule", "TabViewModule", "FloatingPanelComponent", "FormsModule", "ReactiveFormsModule", "DialogModule", "InitialModule", "TranslateModule", "DividerModule", "RatingModule", "ProgressBarModule", "SellerInfoComponent", "IndexRevampComponent", "ProductImagesComponent", "ShareButtonsModule", "ShareIconsModule", "BackButtonComponent", "ProductDetailsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\product-details.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\r\nimport {CommonModule} from '@angular/common';\r\nimport {SharedModule} from '@shared/modules/shared.module';\r\nimport {RouterModule} from '@angular/router';\r\nimport {IndexComponent} from './components/index/index.component';\r\nimport {DetailsComponent} from './components/details/details.component';\r\nimport {routes} from './routes';\r\nimport {ImageZoomComponent} from './components/image-zoom/image-zoom.component';\r\nimport {NgxImageZoomModule} from \"ngx-image-zoom\";\r\nimport {CarouselModule} from \"primeng/carousel\";\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport {FloatingPanelComponent} from './components/floating-panel/floating-panel.component';\r\nimport {FormsModule, ReactiveFormsModule} from '@angular/forms';\r\n\r\nimport {DialogModule} from 'primeng/dialog';\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {DividerModule} from \"primeng/divider\";\r\nimport {RatingModule} from \"primeng/rating\";\r\nimport {ProgressBarModule} from \"primeng/progressbar\";\r\nimport { SellerInfoComponent } from './components/seller-info/seller-info.component';\r\nimport { IndexRevampComponent } from './components/index-revamp/index.component';\r\nimport { ProductImagesComponent } from './components/product-images/product-images.component';\r\nimport { ShareButtonsModule } from 'ngx-sharebuttons/buttons';\r\nimport { ShareIconsModule } from 'ngx-sharebuttons/icons';\r\nimport {BackButtonComponent} from \"@shared/components/back-button/back-button.component\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    DetailsComponent,\r\n    ImageZoomComponent,\r\n    FloatingPanelComponent,\r\n    SellerInfoComponent,\r\n    IndexRevampComponent,\r\n    ProductImagesComponent,\r\n  ],\r\n    imports: [\r\n        CommonModule,\r\n        SharedModule,\r\n        RouterModule.forChild(routes),\r\n        NgxImageZoomModule,\r\n        CarouselModule,\r\n        TranslateModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        DialogModule,\r\n        InitialModule,\r\n        DividerModule,\r\n        RatingModule,\r\n        ProgressBarModule,\r\n        TabMenuModule,\r\n        TabViewModule,\r\n        ShareButtonsModule,\r\n        ShareIconsModule,\r\n        // NgIf,\r\n        // NgForOf,\r\n        // NgForOf,\r\n        // NgIf,\r\n        BackButtonComponent\r\n\r\n    ]\r\n})\r\nexport class ProductDetailsModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,cAAc,QAAO,oCAAoC;AACjE,SAAQC,gBAAgB,QAAO,wCAAwC;AACvE,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,kBAAkB,QAAO,8CAA8C;AAC/E,SAAQC,kBAAkB,QAAO,gBAAgB;AACjD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAAQC,sBAAsB,QAAO,sDAAsD;AAC3F,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAE/D,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,aAAa,QAAO,gCAAgC;AAC5D,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,aAAa,QAAO,iBAAiB;AAC7C,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAAQC,mBAAmB,QAAO,sDAAsD;;;AAsCxF,OAAM,MAAOC,oBAAoB;;qBAApBA,oBAAoB;EAAA;;UAApBA;EAAoB;;cAzBzB1B,YAAY,EACZC,YAAY,EACZC,YAAY,CAACyB,QAAQ,CAACtB,MAAM,CAAC,EAC7BE,kBAAkB,EAClBC,cAAc,EACdQ,eAAe,EACfJ,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbE,aAAa,EACbC,YAAY,EACZC,iBAAiB,EACjBV,aAAa,EACbC,aAAa,EACba,kBAAkB,EAClBC,gBAAgB;IAChB;IACA;IACA;IACA;IACAC,mBAAmB;EAAA;;;2EAIdC,oBAAoB;IAAAE,YAAA,GAlC7BzB,cAAc,EACdC,gBAAgB,EAChBE,kBAAkB,EAClBK,sBAAsB,EACtBS,mBAAmB,EACnBC,oBAAoB,EACpBC,sBAAsB;IAAAO,OAAA,GAGlB7B,YAAY,EACZC,YAAY,EAAA6B,EAAA,CAAA5B,YAAA,EAEZK,kBAAkB,EAClBC,cAAc,EACdQ,eAAe,EACfJ,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbE,aAAa,EACbC,YAAY,EACZC,iBAAiB,EACjBV,aAAa,EACbC,aAAa,EACba,kBAAkB,EAClBC,gBAAgB;IAChB;IACA;IACA;IACA;IACAC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}