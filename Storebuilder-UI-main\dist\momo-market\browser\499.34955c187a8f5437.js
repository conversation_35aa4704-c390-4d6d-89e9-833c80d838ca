"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[499],{8499:(Q,g,c)=>{c.r(g),c.d(g,{CustomPageModule:()=>U});var u=c(6814),t=c(5879),m=c(6075),h=c(9131),P=c(864),_=c(9147),O=c(6593),f=c(7875),C=c(6663);function M(a,r){if(1&a){const n=t.EpF();t.TgZ(0,"div",8),t.NdJ("click",function(){t.CHM(n);const o=t.oxw(2).$implicit,i=t.oxw();return t.KtG(i.redirectFeaturedProducts(o.sectionName,o.sectionId))}),t._uU(1),t.<PERSON><PERSON>(2,"translate"),t.qZA()}2&a&&(t.xp6(1),t.hij(" ",t.lcZ(2,1,"landing.seeMore")," "))}function x(a,r){1&a&&t._UZ(0,"app-mtn-product-card",9),2&a&&t.Q6J("product",r.$implicit)}function v(a,r){if(1&a&&(t.TgZ(0,"section",3)(1,"div",4)(2,"h2"),t._uU(3),t.qZA(),t.YNc(4,M,3,3,"div",5),t.qZA(),t.TgZ(5,"div",6),t.YNc(6,x,1,1,"app-mtn-product-card",7),t.qZA()()),2&a){const n=t.oxw().$implicit,e=t.oxw();t.xp6(3),t.Oqu(n.sectionName),t.xp6(1),t.Q6J("ngIf",n.productsLimit&&n.productsLimit<n.productsTotal),t.xp6(2),t.Q6J("ngForOf",e.getLimitedProducts(n))}}function b(a,r){if(1&a&&(t.TgZ(0,"section",10)(1,"a",11),t._UZ(2,"img",12)(3,"img",13),t.qZA()()),2&a){const n=t.oxw().$implicit,e=t.oxw();t.xp6(1),t.Q6J("href",e.formatURL(n.redirectionURL),t.LSH),t.xp6(1),t.Q6J("src",n.desktopBanner,t.LSH),t.xp6(1),t.Q6J("src",n.mobileBanner,t.LSH)}}function I(a,r){if(1&a&&(t.ynx(0),t.YNc(1,v,7,3,"section",1),t.YNc(2,b,4,3,"section",2),t._UZ(3,"br")(4,"br"),t.BQk()),2&a){const n=r.$implicit;t.xp6(1),t.Q6J("ngIf",n.products.length>0),t.xp6(1),t.Q6J("ngIf",n.redirectionURL)}}let S=(()=>{class a{router=(0,t.f3M)(m.F0);customPageService=(0,t.f3M)(h.$);loaderService=(0,t.f3M)(P.D1);$gtmService=(0,t.f3M)(_.J);meta=(0,t.f3M)(O.h_);sectionsData=[];mobileBackground="";desktopBackground="";ngOnInit(){this.fetchCustomPageData()}fetchCustomPageData(){this.loaderService.show();const n=this.router.url.split("?")[0];this.customPageService.getCustomPageDetails(n).subscribe(e=>{const{success:o,data:i}=e;!o||"Draft"===i.status||i.hidePage?(this.loaderService.hide(),this.router.navigateByUrl("page-not-found")):(this.desktopBackground=i.desktopBackground,this.mobileBackground=i.mobileBackground,this.customPageService.setBackgroundStyle(this.backgroundStyle),this.customPageService.onChangePageTitle(i.title),this.meta.updateTag({name:"description",content:i.metaDescription}),this.sectionsData=i.sections.map(({sectionName:s,sectionId:d,products:p,desktopBanner:l,mobileBanner:A,redirectionURL:J,productsLimit:V,productsTotal:$})=>({sectionName:s,sectionId:d,desktopBanner:l,mobileBanner:A,redirectionURL:J,productsLimit:V,productsTotal:$,products:p.map(({productDetails:z})=>({...this.handleProductFromFetchedData(z),sectionName:s}))})),this.loaderService.hide()),this.$gtmService.pushPageView("Custom page",i.title)})}get backgroundStyle(){const n=window.innerWidth<=768?this.mobileBackground:this.desktopBackground;return n?n.startsWith("http")||n.startsWith("url(")?{"background-image":`url(${n})`}:{background:n}:{}}handleProductFromFetchedData(n){let e,o=[],i=n?.productVariances?.find(d=>d.isDefault);if(i)e=i;else{let d=n?.productVariances?.find(p=>p.soldOut);e=d||n?.productVariances[0]}return e?.productFeaturesList&&(o=e?.productFeaturesList[0]?.featureList),{productId:n?.id,productName:n?.name,isLiked:n?.isLiked,priceValue:e?.price,salePriceValue:e?.salePrice,priceId:e?.priceId,currencyCode:n?.currencyCode,masterImageUrl:n?.masterImageUrl??(e.images?e.images[0]:null),thumbnailImages:e?.thumbnailImages,soldOut:e?.soldOut,rate:e?.rate,count:e?.count??0,specProductId:e.specProductId,channelId:n.channelId??"1",salePercent:e?.salePrice?100-e?.salePrice/e?.price*100:0,shopId:n.shopId,isHot:o?.includes(1),isNew:o?.includes(2),isBest:o?.includes(3),quantity:e.quantity,proSchedulingId:e.proSchedulingId,stockPerSKU:e.stockPerSKU,stockStatus:e.stockStatus,sku:e?.sku,skuAutoGenerated:e.skuAutoGenerated,badgesList:n?.badgesList}}formatURL(n){return n.startsWith("http://")||n.startsWith("https://")?n:"https://"+n}redirectFeaturedProducts(n,e){this.router.navigate([`/${this.router.url}/${n}/${e}`])}getLimitedProducts(n){return n.products.slice(0,n.productsLimit??n.products.length)}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=t.Xpm({type:a,selectors:[["app-custom-page"]],decls:2,vars:1,consts:[[4,"ngFor","ngForOf"],["class","main-container",4,"ngIf"],["class","main-container banner-container",4,"ngIf"],[1,"main-container"],[1,"d-flex","justify-content-between","align-items-center"],["class","see-all",3,"click",4,"ngIf"],[1,"each-product"],["class","product-card",3,"product",4,"ngFor","ngForOf"],[1,"see-all",3,"click"],[1,"product-card",3,"product"],[1,"main-container","banner-container"],["target","_blank","rel","noopener noreferrer",3,"href"],["alt","Desktop Banner",1,"banner-desktop",3,"src"],["alt","Mobile Banner",1,"banner-mobile",3,"src"]],template:function(e,o){1&e&&(t.TgZ(0,"main"),t.YNc(1,I,5,2,"ng-container",0),t.qZA()),2&e&&(t.xp6(1),t.Q6J("ngForOf",o.sectionsData))},dependencies:[u.sg,u.O5,f.Y,C.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}main[_ngcontent-%COMP%]{padding:16px}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{padding:8px 0;margin:5px 0 5px 9px;font-family:main-medium;color:#212529;font-weight:700;font-size:22px;text-transform:capitalize}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap!important;padding:0 16px 0 0;gap:7px}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:220px;display:flex;flex-direction:column}main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]:first-child{margin-top:26px}main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;min-width:80%;margin:auto}@media (max-width: 768px){main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-desktop[_ngcontent-%COMP%]{display:none}}main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-mobile[_ngcontent-%COMP%]{display:none}@media (max-width: 768px){main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-mobile[_ngcontent-%COMP%]{display:block}}.see-all[_ngcontent-%COMP%]{color:#191c1f;font-family:var(--medium-font);cursor:pointer;font-size:14px;font-style:normal;font-weight:500;line-height:20px}.see-all[_ngcontent-%COMP%]:hover{text-decoration:underline;color:#1a445e}@media (max-width: 1200px){main[_ngcontent-%COMP%]{margin-top:120px!important}}@media (max-width: 768px){main[_ngcontent-%COMP%]{margin-top:0!important}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;text-transform:uppercase}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:10px;align-items:start}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{margin:0;justify-self:center}}@media (max-width: 699px){main[_ngcontent-%COMP%]{margin-top:35px!important}}@media (max-width: 575px){main[_ngcontent-%COMP%]{margin-top:60px!important}}@media (max-width: 467px){main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]{padding:0}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:150px!important}}@media (max-width: 323px){main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]{padding:0}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]{padding:0!important}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:130px!important}}@media (max-width: 280px){main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]{grid-template-columns:1fr!important}main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:auto!important}}"]})}return a})();var y=c(2655),L=c(7680);let w=(()=>{class a{el;threshold=.1;scrolledToEnd=new t.vpe;observer=null;constructor(n){this.el=n}ngOnInit(){this.setupIntersectionObserver()}ngOnDestroy(){this.cleanupObserver()}setupIntersectionObserver(){!this.observer&&typeof IntersectionObserver<"u"&&(this.observer=new IntersectionObserver(e=>{const[o]=e;o.isIntersecting&&this.scrolledToEnd.emit()},{root:null,rootMargin:"0px",threshold:this.threshold}),this.observer.observe(this.el.nativeElement))}cleanupObserver(){this.observer&&(this.observer.disconnect(),this.observer=null)}static \u0275fac=function(e){return new(e||a)(t.Y36(t.SBq))};static \u0275dir=t.lG2({type:a,selectors:[["","appLazyLoad",""]],inputs:{threshold:"threshold"},outputs:{scrolledToEnd:"scrolledToEnd"},standalone:!0})}return a})();function k(a,r){if(1&a&&(t.TgZ(0,"div",10),t._UZ(1,"app-mtn-product-card",11),t.qZA()),2&a){const n=r.$implicit;t.xp6(1),t.Q6J("product",n)}}function D(a,r){1&a&&(t.TgZ(0,"div",12),t._UZ(1,"p-progressSpinner"),t.qZA())}function T(a,r){if(1&a){const n=t.EpF();t.TgZ(0,"div",6),t.YNc(1,k,2,1,"div",7),t.TgZ(2,"div",8),t.NdJ("scrolledToEnd",function(){t.CHM(n);const o=t.oxw();return t.KtG(o.onScrolledToEnd())}),t.YNc(3,D,2,0,"div",9),t.qZA()()}if(2&a){const n=t.oxw();t.xp6(1),t.Q6J("ngForOf",n.products),t.xp6(2),t.Q6J("ngIf",n.isLoading)}}const Z=function(a,r){return{breadcrumb:a,hiddenNavbarBreadcrum:r}},F=[{path:"",component:S},{path:":sectionName/:sectionId",component:(()=>{class a{router=(0,t.f3M)(m.F0);route=(0,t.f3M)(m.gz);appDataService=(0,t.f3M)(P.UW);customPageService=(0,t.f3M)(h.$);home={icon:"pi pi-home",routerLink:"/"};breadItems=[];navbarData;sectionName="";sectionId="0";title="";products=[];currentPage=1;pageSize=10;isLoading=!1;isLastPage=!1;hasMoreProducts=!0;ngOnInit(){this.setNavbarData(),this.initRouteData(),this.loadProducts()}setNavbarData(){this.navbarData=this.appDataService.layoutTemplate.find(n=>"navbar"===n.type)}initRouteData(){this.route.paramMap.subscribe(n=>{const e=n.get("tenant");this.title=n.get("title")||"",this.sectionId=n.get("sectionId")||"",this.updateBreadcrumb(e)})}updateBreadcrumb(n){this.breadItems=[{label:this.title,routerLink:[`/${n}/${this.title}`]},{label:this.sectionName}]}loadProducts(){this.isLoading=!0,this.customPageService.getProducts(this.currentPage,this.pageSize,this.sectionId).subscribe(n=>{if(!n.success)return void this.router.navigateByUrl("page-not-found");const{sectionName:e,records:o,hasNext:i}=n.data;this.sectionName=e,this.updateBreadcrumb(this.route.snapshot.paramMap.get("tenant")),this.hasMoreProducts=i&&o.length===this.pageSize,this.isLastPage=!this.hasMoreProducts,o.forEach(s=>this.addProductFromLoadData(s)),this.currentPage++,this.isLoading=!1},n=>{console.error("Error fetching products:",n),this.isLoading=!1,this.currentPage--})}addProductFromLoadData(n){let e;const o=n.productDetails;let i=n?.productDetails?.productVariances?.find(p=>p.isDefault);if(i)e=i;else{let p=n?.productDetails?.productVariances?.find(l=>l.soldOut);e=p||n?.productDetails?.productVariances[0]}let s=[];e?.productFeaturesList&&(s=e?.productFeaturesList[0]?.featureList);let d={badges:o.badgesList,productId:o?.id,productName:o?.name,isLiked:o?.isLiked,priceValue:e?.price,salePriceValue:e?.salePrice,priceId:e?.priceId,currencyCode:o?.currencyCode,masterImageUrl:o?.masterImageUrl??(e.images?e.images[0]:null),thumbnailImages:e?.thumbnailImages,soldOut:e?.soldOut,rate:e?.rate,count:e?.count??0,specProductId:e.specProductId,channelId:o.channelId??"1",salePercent:e?.salePrice?100-e?.salePrice/e?.price*100:0,shopId:o.shopId,isHot:s?.includes(1),isNew:s?.includes(2),isBest:s?.includes(3),quantity:e.quantity,proSchedulingId:e.proSchedulingId,stockPerSKU:e.stockPerSKU,stockStatus:e.stockStatus,sku:e?.sku,skuAutoGenerated:e.skuAutoGenerated};this.products.push(d)}onScrolledToEnd(){this.isLoading||!this.hasMoreProducts||(this.isLoading=!0,this.loadProducts())}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=t.Xpm({type:a,selectors:[["app-section-details"]],decls:9,vars:8,consts:[[1,"category-products-page"],[1,"breadcrumb","m-0",3,"ngClass"],[3,"home","model"],[1,"col-12","col-md-6","flex"],[1,"font-size-22","bold-font"],["class","each-product",4,"ngIf"],[1,"each-product"],["class","product-card",4,"ngFor","ngForOf"],["appLazyLoad","",1,"loading-indicator",3,"scrolledToEnd"],["class","spinner-product",4,"ngIf"],[1,"product-card"],[3,"product"],[1,"spinner-product"]],template:function(e,o){1&e&&(t.TgZ(0,"main")(1,"section",0)(2,"div",1),t._UZ(3,"p-breadcrumb",2),t.qZA(),t.TgZ(4,"div")(5,"div",3)(6,"div",4),t._uU(7),t.qZA()(),t.YNc(8,T,4,2,"div",5),t.qZA()()()),2&e&&(t.xp6(2),t.Q6J("ngClass",t.WLB(5,Z,null==o.navbarData?null:o.navbarData.isActive,!(null!=o.navbarData&&o.navbarData.isActive))),t.xp6(1),t.Q6J("home",o.home)("model",o.breadItems),t.xp6(4),t.hij(" ",o.sectionName," "),t.xp6(1),t.Q6J("ngIf",null==o.products?null:o.products.length))},dependencies:[u.mk,u.sg,u.O5,y.a,f.Y,L.G,w],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.hiddenNavbarBreadcrum[_ngcontent-%COMP%]{display:none}.products-margin[_ngcontent-%COMP%]{padding:0 0 16px!important}.loading-indicator[_ngcontent-%COMP%]{width:100%;visibility:visible}.each-product[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap!important;padding:0 16px 0 0;gap:7px}.each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:220px}@media (max-width: 1200px){main[_ngcontent-%COMP%]{margin-top:120px!important}}@media screen and (max-width: 768px){.each-product[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,auto)}.each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{margin:auto}}@media (max-width: 575px){main[_ngcontent-%COMP%]{margin-top:80px!important}}@media (max-width: 467px){.each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:150px!important}}@media (max-width: 323px){.each-product[_ngcontent-%COMP%]{padding:0!important}.each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:130px!important}}@media (max-width: 280px){.each-product[_ngcontent-%COMP%]{grid-template-columns:1fr!important}.each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{width:auto!important}}"]})}return a})()}];var B=c(258),N=c(6574);let U=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=t.oAB({type:a});static \u0275inj=t.cJS({imports:[u.ez,m.Bz.forChild(F),C.aw,B.m,N.p]})}return a})()}}]);