{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { FormsModule } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class OtpModule {\n  static #_ = this.ɵfac = function OtpModule_Factory(t) {\n    return new (t || OtpModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: OtpModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), TranslateModule, ButtonModule, FormsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OtpModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule, ButtonModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IndexComponent", "RouterModule", "routes", "TranslateModule", "ButtonModule", "FormsModule", "OtpModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\otp\\otp.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {ButtonModule} from \"primeng/button\";\r\nimport {FormsModule} from \"@angular/forms\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    TranslateModule,\r\n    ButtonModule,\r\n    FormsModule\r\n  ]\r\n})\r\nexport class OtpModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,WAAW,QAAO,gBAAgB;;;AAc1C,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qBAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF;EAAS;EAAA,QAAAG,EAAA,G;cAPlBV,YAAY,EACZE,YAAY,CAACS,QAAQ,CAACR,MAAM,CAAC,EAC7BC,eAAe,EACfC,YAAY,EACZC,WAAW;EAAA;;;2EAGFC,SAAS;IAAAK,YAAA,GAVlBX,cAAc;IAAAY,OAAA,GAGdb,YAAY,EAAAc,EAAA,CAAAZ,YAAA,EAEZE,eAAe,EACfC,YAAY,EACZC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}