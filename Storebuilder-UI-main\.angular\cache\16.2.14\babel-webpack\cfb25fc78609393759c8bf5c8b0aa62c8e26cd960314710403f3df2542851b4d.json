{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class LazyLoadDirective {\n  constructor(el) {\n    this.el = el;\n    this.threshold = 0.1;\n    this.scrolledToEnd = new EventEmitter();\n    this.observer = null;\n  }\n  ngOnInit() {\n    this.setupIntersectionObserver();\n  }\n  ngOnDestroy() {\n    this.cleanupObserver();\n  }\n  setupIntersectionObserver() {\n    if (!this.observer && typeof IntersectionObserver !== 'undefined') {\n      const options = {\n        root: null,\n        rootMargin: '0px',\n        threshold: this.threshold\n      };\n      this.observer = new IntersectionObserver(entries => {\n        const [entry] = entries;\n        if (entry.isIntersecting) {\n          this.scrolledToEnd.emit();\n        }\n      }, options);\n      this.observer.observe(this.el.nativeElement);\n    }\n  }\n  cleanupObserver() {\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = null;\n    }\n  }\n}\nLazyLoadDirective.ɵfac = function LazyLoadDirective_Factory(t) {\n  return new (t || LazyLoadDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nLazyLoadDirective.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n  type: LazyLoadDirective,\n  selectors: [[\"\", \"appLazyLoad\", \"\"]],\n  inputs: {\n    threshold: \"threshold\"\n  },\n  outputs: {\n    scrolledToEnd: \"scrolledToEnd\"\n  },\n  standalone: true\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}