{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MainSliderComponent } from './components/main-slider/main-slider.component';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { BannerComponent } from './components/banner/banner.component';\nimport { CategorySliderComponent } from './components/category-slider/category-slider.component';\nimport { SectionCategoryComponent } from './components/section-category/section.component';\nimport { routes } from \"./routes\";\nimport { register } from 'swiper/element/bundle';\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { CarouselModule } from \"ngx-owl-carousel-o\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nregister();\nlet HomeModule = class HomeModule {};\nHomeModule = __decorate([NgModule({\n  declarations: [MainSliderComponent, IndexComponent, BannerComponent, CategorySliderComponent, SectionCategoryComponent],\n  imports: [CommonModule, SharedModule, TranslateModule, RouterModule.forChild(routes), CarouselModule, InitialModule],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})], HomeModule);\nexport { HomeModule };", "map": {"version": 3, "names": ["NgModule", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "MainSliderComponent", "IndexComponent", "RouterModule", "BannerComponent", "CategorySliderComponent", "SectionCategoryComponent", "routes", "register", "SharedModule", "TranslateModule", "CarouselModule", "InitialModule", "HomeModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "schemas"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\home.module.ts"], "sourcesContent": ["import { NgModule,CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MainSliderComponent } from './components/main-slider/main-slider.component';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport { BannerComponent } from './components/banner/banner.component';\r\nimport {CategorySliderComponent} from './components/category-slider/category-slider.component';\r\nimport {SectionCategoryComponent} from './components/section-category/section.component';\r\nimport {routes} from \"./routes\";\r\nimport { register } from 'swiper/element/bundle';\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {CarouselModule} from \"ngx-owl-carousel-o\";\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nregister();\r\n\r\n@NgModule({\r\n  declarations: [\r\n    MainSliderComponent,\r\n    IndexComponent,\r\n    BannerComponent,\r\n    CategorySliderComponent,\r\n    SectionCategoryComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    TranslateModule,\r\n    RouterModule.forChild(routes),\r\n    CarouselModule,\r\n    InitialModule,\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\r\n\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,EAACC,sBAAsB,QAAQ,eAAe;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAAQC,uBAAuB,QAAO,wDAAwD;AAC9F,SAAQC,wBAAwB,QAAO,iDAAiD;AACxF,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,cAAc,QAAO,oBAAoB;AACjD,SAAQC,aAAa,QAAO,gCAAgC;AAC5DJ,QAAQ,EAAE;AAqBH,IAAMK,UAAU,GAAhB,MAAMA,UAAU,GAAI;AAAdA,UAAU,GAAAC,UAAA,EAnBtBhB,QAAQ,CAAC;EACRiB,YAAY,EAAE,CACZd,mBAAmB,EACnBC,cAAc,EACdE,eAAe,EACfC,uBAAuB,EACvBC,wBAAwB,CACzB;EACDU,OAAO,EAAE,CACPhB,YAAY,EACZS,YAAY,EACZC,eAAe,EACfP,YAAY,CAACc,QAAQ,CAACV,MAAM,CAAC,EAC7BI,cAAc,EACdC,aAAa,CACd;EACDM,OAAO,EAAE,CAACnB,sBAAsB;CAEjC,CAAC,C,EACWc,UAAU,CAAI;SAAdA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}