{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction MerchantBlockComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function MerchantBlockComponent_div_6_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const shop_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.ShopProduct(shop_r1.shopId, shop_r1.shopName));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const shop_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(shop_r1.shopName);\n  }\n}\nexport class MerchantBlockComponent {\n  constructor(route) {\n    this.route = route;\n    this.data = {};\n  }\n  ngOnInit() {}\n  ShopProduct(value, Name) {\n    this.route.navigate([`/merchants/merchant-product`], {\n      state: {\n        shopId: value,\n        shopName: Name\n      }\n    });\n  }\n  static #_ = this.ɵfac = function MerchantBlockComponent_Factory(t) {\n    return new (t || MerchantBlockComponent)(i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MerchantBlockComponent,\n    selectors: [[\"app-merchant-block\"]],\n    inputs: {\n      data: \"data\"\n    },\n    decls: 7,\n    vars: 3,\n    consts: [[1, \"grid\", \"mt-5\"], [1, \"col-12\", 3, \"id\"], [1, \"bold-font\", \"font-size-20\"], [1, \"col-12\"], [1, \"grid\"], [\"class\", \"col-12 md:col-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"md:col-4\"], [1, \"no-underline\", 2, \"color\", \"#004f71\", \"cursor\", \"pointer\", 3, \"click\"]],\n    template: function MerchantBlockComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n        i0.ɵɵtemplate(6, MerchantBlockComponent_div_6_Template, 3, 1, \"div\", 5);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵpropertyInterpolate(\"id\", ctx.data.key);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.key);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.data.shopList);\n      }\n    },\n    dependencies: [i2.NgForOf],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "MerchantBlockComponent_div_6_Template_a_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "shop_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "ShopProduct", "shopId", "shopName", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "MerchantBlockComponent", "constructor", "route", "data", "ngOnInit", "value", "Name", "navigate", "state", "_", "ɵɵdirectiveInject", "i1", "Router", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "MerchantBlockComponent_Template", "rf", "ctx", "ɵɵtemplate", "MerchantBlockComponent_div_6_Template", "ɵɵpropertyInterpolate", "key", "ɵɵproperty", "shopList"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchants\\components\\merchant-block\\merchant-block.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchants\\components\\merchant-block\\merchant-block.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Shops } from \"../../../../interfaces/merchant\";\r\n\r\n@Component({\r\n  selector: 'app-merchant-block',\r\n  templateUrl: './merchant-block.component.html',\r\n  styleUrls: ['./merchant-block.component.scss']\r\n})\r\nexport class MerchantBlockComponent implements OnInit {\r\n  @Input() data: Shops = {} as Shops;\r\n\r\n  constructor(private route: Router) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n  ShopProduct(value: any, Name: any) {\r\n    this.route.navigate([`/merchants/merchant-product`], { state: { shopId: value, shopName: Name } });\r\n\r\n\r\n  }\r\n}\r\n", "<div class=\"grid mt-5\">\r\n  <div class=\"col-12\" id=\"{{ data.key }}\">\r\n    <div class=\"bold-font font-size-20\">{{ data.key }}</div>\r\n  </div>\r\n  <div class=\"col-12\">\r\n    <div class=\"grid\">\r\n      <div *ngFor=\"let shop of data.shopList\" class=\"col-12 md:col-4\">\r\n        <a\r\n          class=\"font-size-15 main-color medium-font my-1\"\r\n          style=\"color: #004f71; cursor: pointer\"\r\n          class=\"no-underline\"\r\n          (click)=\"ShopProduct(shop.shopId, shop.shopName)\"\r\n          >{{ shop.shopName }}</a\r\n        >\r\n        <!-- (click)=\"ShopProduct(shop.shopId)\" -->\r\n        <!-- <div class=\"font-size-15 main-color medium-font my-1\">{{shop.shopName}}</div> -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;ICMMA,EAAA,CAAAC,cAAA,aAAgE;IAK5DD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,OAAA,CAAAM,MAAA,EAAAN,OAAA,CAAAO,QAAA,CAAuC;IAAA,EAAC;IAChDd,EAAA,CAAAe,MAAA,GAAmB;IAAAf,EAAA,CAAAgB,YAAA,EACrB;;;;IADEhB,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,iBAAA,CAAAX,OAAA,CAAAO,QAAA,CAAmB;;;ADH9B,OAAM,MAAOK,sBAAsB;EAGjCC,YAAoBC,KAAa;IAAb,KAAAA,KAAK,GAALA,KAAK;IAFhB,KAAAC,IAAI,GAAU,EAAW;EAGlC;EAEAC,QAAQA,CAAA,GACR;EACAX,WAAWA,CAACY,KAAU,EAAEC,IAAS;IAC/B,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAAC,CAAC,6BAA6B,CAAC,EAAE;MAAEC,KAAK,EAAE;QAAEd,MAAM,EAAEW,KAAK;QAAEV,QAAQ,EAAEW;MAAI;IAAE,CAAE,CAAC;EAGpG;EAAC,QAAAG,CAAA,G;qBAZUT,sBAAsB,EAAAnB,EAAA,CAAA6B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBb,sBAAsB;IAAAc,SAAA;IAAAC,MAAA;MAAAZ,IAAA;IAAA;IAAAa,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTnCxC,EAAA,CAAAC,cAAA,aAAuB;QAEiBD,EAAA,CAAAe,MAAA,GAAc;QAAAf,EAAA,CAAAgB,YAAA,EAAM;QAE1DhB,EAAA,CAAAC,cAAA,aAAoB;QAEhBD,EAAA,CAAA0C,UAAA,IAAAC,qCAAA,iBAUM;QACR3C,EAAA,CAAAgB,YAAA,EAAM;;;QAhBYhB,EAAA,CAAAiB,SAAA,GAAmB;QAAnBjB,EAAA,CAAA4C,qBAAA,OAAAH,GAAA,CAAAnB,IAAA,CAAAuB,GAAA,CAAmB;QACD7C,EAAA,CAAAiB,SAAA,GAAc;QAAdjB,EAAA,CAAAkB,iBAAA,CAAAuB,GAAA,CAAAnB,IAAA,CAAAuB,GAAA,CAAc;QAI1B7C,EAAA,CAAAiB,SAAA,GAAgB;QAAhBjB,EAAA,CAAA8C,UAAA,YAAAL,GAAA,CAAAnB,IAAA,CAAAyB,QAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}