import { Component, ContentChild, TemplateRef, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-custom-swiper',
  templateUrl: './custom-swiper.component.html',
  styleUrls: ['./custom-swiper.component.scss']
})
export class CustomSwiperComponent implements OnInit, OnDestroy {
  @ContentChild('carouselItem', { static: false }) carouselItemTemplate!: TemplateRef<any>;
  @Input() items: any[] = [];
  @Input() slidesPerView: number = 3;
  @Input() showArrows: boolean = true;
  @Input() showDots: boolean = true;
  @Input() autoPlay: boolean = true;
  @Input() autoPlayInterval: number = 3000;
  @Input() loop: boolean = true;
  @Output() slideChanged: EventEmitter<number> = new EventEmitter();

  activeIndex: number = 0;
  startTouch: number = 0;
  endTouch: number = 0;
  numberOfDots: number[] = [];
  autoplayIntervalId: any;

  get maxIndex(): number {
    return this.items.length - 1;
  }

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this.numberOfDots = Array(this.items.length).fill(0).map((_, i) => i);
    if (this.autoPlay) this.startAutoplay();
  }

  ngOnDestroy() {
    this.stopAutoplay();
  }

  startAutoplay() {
    this.autoplayIntervalId = setInterval(() => this.nextSlide(), this.autoPlayInterval);
  }

  stopAutoplay() {
    if (this.autoplayIntervalId) clearInterval(this.autoplayIntervalId);
  }

  prevSlide() {
    this.changeSlide('prev');
  }

  nextSlide() {
    this.changeSlide('next');
  }

  private changeSlide(direction: 'prev' | 'next') {
    if (this.autoPlay) this.stopAutoplay();

    if (direction === 'prev') {
      this.activeIndex = this.loop && this.activeIndex === 0 ? this.maxIndex : Math.max(0, this.activeIndex - 1);
    } else {
      this.activeIndex = this.loop && this.activeIndex === this.maxIndex ? 0 : Math.min(this.maxIndex, this.activeIndex + 1);
    }

    this.slideChanged.emit(this.activeIndex);

    if (this.autoPlay) this.startAutoplay();
    this.cdr.detectChanges();
  }

  goToSlide(index: number) {
    if (this.autoPlay) this.stopAutoplay();
    this.activeIndex = index;
    this.slideChanged.emit(this.activeIndex);
    if (this.autoPlay) this.startAutoplay();
  }

  trackByIndex(index: number) {
    return index;
  }

  onTouchStart(event: TouchEvent): void {
    if (this.autoPlay) this.stopAutoplay();
    this.startTouch = event.touches[0].clientX;
  }

  onTouchMove(event: TouchEvent): void {
    this.endTouch = event.touches[0].clientX;
  }

  onTouchEnd(): void {
    const swipeThreshold = 50;
    if (this.startTouch - this.endTouch > swipeThreshold) {
      this.nextSlide();
    } else if (this.endTouch - this.startTouch > swipeThreshold) {
      this.prevSlide();
    }

    if (this.autoPlay) this.startAutoplay();
  }
}
