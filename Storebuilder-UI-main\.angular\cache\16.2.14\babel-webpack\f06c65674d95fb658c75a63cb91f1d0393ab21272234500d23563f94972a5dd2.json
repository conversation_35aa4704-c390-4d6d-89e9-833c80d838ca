{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { WishlistRoutingModule } from './wishlist-routing.module';\nimport { ListComponent } from './components/list/list.component';\nimport { WishlistService } from \"@core/services\";\nimport { EmptyWishComponent } from './components/empty-wish/empty-wish.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ToastModule } from \"primeng/toast\";\nimport { RatingModule } from \"primeng/rating\";\nimport { FormsModule } from '@angular/forms';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport { GAImpressionDirective } from '@core/directives/ga-impression.directive';\nimport * as i0 from \"@angular/core\";\nexport class WishlistModule {\n  static ɵfac = function WishlistModule_Factory(t) {\n    return new (t || WishlistModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: WishlistModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [WishlistService],\n    imports: [CommonModule, WishlistRoutingModule, TranslateModule, ToastModule, RatingModule, FormsModule, InitialModule, EmptyScreenComponent, BackButtonComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WishlistModule, {\n    declarations: [ListComponent, EmptyWishComponent],\n    imports: [CommonModule, WishlistRoutingModule, TranslateModule, ToastModule, RatingModule, FormsModule, InitialModule, EmptyScreenComponent, BackButtonComponent, GAImpressionDirective]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}