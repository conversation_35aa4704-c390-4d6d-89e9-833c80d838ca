{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { MerchantBlockComponent } from './components/merchant-block/merchant-block.component';\nimport { routes } from \"./routes\";\nimport { MerchantFilterPipe } from './pipes/merchant-filter.pipe';\nimport { MerchantProductsComponent } from './components/merchant-products/merchant-products.component';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class MerchantsModule {\n  static #_ = this.ɵfac = function MerchantsModule_Factory(t) {\n    return new (t || MerchantsModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MerchantsModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), InitialModule, TranslateModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MerchantsModule, {\n    declarations: [IndexComponent, MerchantBlockComponent, MerchantFilterPipe, MerchantProductsComponent],\n    imports: [CommonModule, i1.RouterModule, InitialModule, TranslateModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CommonModule", "IndexComponent", "MerchantBlockComponent", "routes", "MerchantFilterPipe", "MerchantProductsComponent", "InitialModule", "TranslateModule", "MerchantsModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchants\\merchants.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {CommonModule} from '@angular/common';\r\nimport {IndexComponent} from './components/index/index.component';\r\nimport {MerchantBlockComponent} from './components/merchant-block/merchant-block.component';\r\nimport {routes} from \"./routes\";\r\nimport {MerchantFilterPipe} from './pipes/merchant-filter.pipe';\r\nimport {MerchantProductsComponent} from './components/merchant-products/merchant-products.component';\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    MerchantBlockComponent,\r\n    MerchantFilterPipe,\r\n    MerchantProductsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    InitialModule,\r\n    TranslateModule\r\n  ]\r\n})\r\nexport class MerchantsModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,cAAc,QAAO,oCAAoC;AACjE,SAAQC,sBAAsB,QAAO,sDAAsD;AAC3F,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,kBAAkB,QAAO,8BAA8B;AAC/D,SAAQC,yBAAyB,QAAO,4DAA4D;AACpG,SAAQC,aAAa,QAAO,gCAAgC;AAC5D,SAAQC,eAAe,QAAO,qBAAqB;;;AAiBnD,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cANxBX,YAAY,EACZD,YAAY,CAACa,QAAQ,CAACT,MAAM,CAAC,EAC7BG,aAAa,EACbC,eAAe;EAAA;;;2EAGNC,eAAe;IAAAK,YAAA,GAZxBZ,cAAc,EACdC,sBAAsB,EACtBE,kBAAkB,EAClBC,yBAAyB;IAAAS,OAAA,GAGzBd,YAAY,EAAAe,EAAA,CAAAhB,YAAA,EAEZO,aAAa,EACbC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}