{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport jwt_decode from \"jwt-decode\";\nimport * as CryptoJS from 'crypto-js';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"ngx-cookie-service\";\nimport * as i7 from \"primeng/password\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"ngx-intl-tel-input-gg\";\nconst _c0 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nfunction VerifyUserComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"p\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"span\", 10)(14, \"label\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-password\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyUserComponent_ng_container_1_Template_p_password_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.password = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_container_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.checkMobileExist());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_container_1_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onBack());\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(23, _c0, ctx_r0.screenWidth <= 768 ? ctx_r0.isMobileLayout ? \"5rem\" : \"220px\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 11, \"newUser.verifyUser.password\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(8, 13, \"newUser.verifyUser.passwordConfirm\"), \"\\u00A0\", i0.ɵɵpipeBind1(9, 15, ctx_r0.title), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(16, 17, \"signIn.password\"), \" *\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false)(\"ngModel\", ctx_r0.password);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.password.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 19, \"newUser.verifyUser.continue\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 21, \"newUser.verifyUser.cancel\"), \" \");\n  }\n}\nfunction VerifyUserComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 4)(2, \"p\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 10)(13, \"label\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p-password\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyUserComponent_ng_template_2_Template_p_password_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.password = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_template_2_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.checkMobileExist());\n    });\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 9, \"newUser.verifyUser.password\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(7, 11, \"newUser.verifyUser.passwordConfirm\"), \"\\u00A0\", i0.ɵɵpipeBind1(8, 13, ctx_r2.title), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 15, \"signIn.password\"), \" *\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false)(\"ngModel\", ctx_r2.password);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(18, 17, \"newUser.verifyUser.continue\"))(\"disabled\", ctx_r2.password.length === 0);\n  }\n}\nexport class VerifyUserComponent {\n  otpService;\n  translate;\n  messageService;\n  router;\n  store;\n  auth;\n  authTokenService;\n  permissionService;\n  platformId;\n  $gaService;\n  cookieService;\n  loaderService;\n  phoneNumber;\n  countryPhoneNumber = \"\";\n  countryPhoneCode = \"\";\n  phoneLength = 12;\n  phoneInputLength = 12;\n  tagName = GaLocalActionEnum;\n  isGoogleAnalytics = false;\n  password = '';\n  cookieValue;\n  decoded;\n  isPrimary;\n  title = '';\n  screenWidth = window.innerWidth;\n  isMobileLayout = false;\n  isEmail = false;\n  userEmail = '';\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(otpService, translate, messageService, router, store, auth, authTokenService, permissionService, platformId, $gaService, cookieService, loaderService) {\n    this.otpService = otpService;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.auth = auth;\n    this.authTokenService = authTokenService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    this.$gaService = $gaService;\n    this.cookieService = cookieService;\n    this.loaderService = loaderService;\n    let userData = localStorage.getItem('profile') ?? '{}';\n    userData = JSON.parse(userData);\n    if (userData?.mobileNumber) this.phoneNumber = userData.mobileNumber;\n    if (userData?.isEmail) {\n      this.isEmail = userData.isEmail;\n      this.userEmail = userData.email;\n    }\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.isPrimary = JSON.parse(localStorage.getItem(\"isPrimary\") ?? '');\n    if (this.isPrimary) {\n      this.title = this.translate.instant('newUser.verifyUser.confirmChangeDefaultNumber');\n    } else {\n      this.title = this.translate.instant('newUser.verifyUser.addNewNumber');\n    }\n  }\n  onBack() {\n    this.router.navigate(['account/details']);\n  }\n  triggerEnterPasswordAnalytics(isPasswordCorrect = '') {\n    this.$gaService.event(this.tagName.CLICK_ON_ENTER_PASSWORD, '', 'CONFIRM_PASSWORD_TO_ADD_NEW_NUMBER', 1, true, {\n      \"submission_outcome\": isPasswordCorrect\n    });\n  }\n  checkMobileExist() {\n    this.store.set(\"loading\", true);\n    this.otpService.userPassword = this.password;\n    if (this.phoneNumber != null && this.phoneNumber != \"\" && this.password && this.password !== '') {\n      this.auth.login({\n        username: this.isEmail ? this.userEmail : this.phoneNumber.replace('-', ''),\n        password: this.password\n      }).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.store.set('profile', res.data);\n            this.store.set('userPhone', res.data.mobileNumber);\n            this.store.set('timeInterval', new Date().getTime());\n            let token = res.data.authToken.replace('bearer ', '');\n            this.triggerEnterPasswordAnalytics('Pass');\n            this.decoded = jwt_decode(token);\n            let days = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n            const dateNow = new Date();\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\n            let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();\n            localStorage.setItem('auth_enc', encryptedMessage);\n            this.cookieService.set('authToken', token, {\n              expires: dateNow,\n              path: '/',\n              sameSite: 'Strict'\n            });\n            this.cookieValue = this.cookieService.get('authToken');\n            this.authTokenService.authTokenSet(this.cookieValue);\n            localStorage.removeItem('isGuest');\n            this.loaderService.hide();\n            if (res?.data?.currency) this.store.set('currency', res.data.currency);\n            localStorage.setItem('refreshToken', res.data.refreshToken);\n            this.store.set('refreshToken', res.data.refreshToken);\n            this.router.navigate(['/account/verify-mobile']);\n          } else {\n            this.triggerEnterPasswordAnalytics('failed');\n            this.messageService.add({\n              severity: 'error',\n              summary: '',\n              detail: res.message\n            });\n            localStorage.setItem('isGuest', 'true');\n          }\n        }\n      });\n    } else {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\n      });\n      this.store.set(\"loading\", false);\n    }\n  }\n  omit_special_char(event) {\n    let key;\n    key = event.charCode;\n    return key > 47 && key < 58;\n  }\n  static ɵfac = function VerifyUserComponent_Factory(t) {\n    return new (t || VerifyUserComponent)(i0.ɵɵdirectiveInject(i1.RegisterService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.CookieService), i0.ɵɵdirectiveInject(i1.LoaderService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: VerifyUserComponent,\n    selectors: [[\"app-verify-user\"]],\n    hostBindings: function VerifyUserComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function VerifyUserComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"register\"], [4, \"ngIf\", \"ngIfElse\"], [\"desktopView\", \"\"], [1, \"content-container\", 3, \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-100\"], [1, \"col-12\", \"content-container__heading-title\", \"text-center\", \"m-0\", \"py-0\", \"mx-4\"], [1, \"col-12\", \"content-container__heading-sub-title\", \"mb-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\"], [1, \"\"], [1, \"pass-label\"], [\"autocomplete\", \"off\", 1, \"customClass\", 3, \"toggleMask\", \"feedback\", \"ngModel\", \"ngModelChange\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"content-container__primary-btn\", \"mt-4\", \"gap-2\", 3, \"disabled\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"content-container__secondary-btn\", \"mt-3\", \"gap-2\", 3, \"click\"], [1, \"content-container\", \"my-3\"], [1, \"col-12\", \"bold-font\", \"text-center\", \"font-size-28\", \"m-0\", \"py-0\", \"mx-4\", 2, \"line-height\", \"1.5\"], [1, \"col-12\", \"text-center\", \"no-underline\", \"font-size-16\", \"mb-3\", \"default-number\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"border-round\", \"bg-white\", \"shadow-1\", \"px-5\", \"pt-6\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-5\", \"mt-7\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"label\", \"disabled\", \"click\"]],\n    template: function VerifyUserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, VerifyUserComponent_ng_container_1_Template, 24, 25, \"ng-container\", 1);\n        i0.ɵɵtemplate(2, VerifyUserComponent_ng_template_2_Template, 19, 19, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(3);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.screenWidth < 768)(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: [i7.Password, i8.NgControlStatus, i9.NgIf, i9.NgStyle, i10.ButtonDirective, i8.NgModel, i11.NativeElementInjectorDirective, i2.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n#phone-number[_ngcontent-%COMP%] {\\n  padding: 0.9rem;\\n  width: 100%;\\n}\\n\\n.register[_ngcontent-%COMP%] {\\n  margin-top: 90px;\\n}\\n\\n.pass-label[_ngcontent-%COMP%] {\\n  color: #323232;\\n  position: absolute;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  z-index: 3;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n}\\n\\n  .p-password-input {\\n  height: 60px !important;\\n  width: 100%;\\n  border-radius: 5px 5px 0px 0px;\\n  opacity: 1;\\n  border: none !important;\\n  border-bottom: 1px solid #b9b9b9 !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  padding-top: 20px;\\n  background-color: #f5f5f5 !important;\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n}\\n\\n.default-number[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font) !important;\\n  color: #a3a3a3;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .register[_ngcontent-%COMP%] {\\n    margin-top: 122px !important;\\n  }\\n  .content-container__heading-title[_ngcontent-%COMP%] {\\n    color: #000;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 20px;\\n    font-style: normal;\\n    font-weight: 700;\\n    line-height: normal;\\n  }\\n  .content-container__heading-sub-title[_ngcontent-%COMP%] {\\n    color: #6E6E6E;\\n    text-align: center;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 400;\\n    line-height: normal;\\n  }\\n  .content-container[_ngcontent-%COMP%]   .pass-label[_ngcontent-%COMP%] {\\n    color: #323232;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 12px;\\n    font-style: normal;\\n    font-weight: 500;\\n    line-height: normal;\\n  }\\n  .content-container__primary-btn[_ngcontent-%COMP%] {\\n    background: var(--primary, #204E6E);\\n    border-radius: 8px !important;\\n    color: var(--Gray-00, #FFF);\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    letter-spacing: 0.168px;\\n    place-content: center;\\n    padding: 12px 24px;\\n    height: 48px;\\n  }\\n  .content-container__secondary-btn[_ngcontent-%COMP%] {\\n    background: transparent !important;\\n    border-radius: 8px !important;\\n    color: var(--primary, #204E6E);\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    letter-spacing: 0.168px;\\n    place-content: center;\\n    padding: 12px 24px;\\n    height: 48px;\\n  }\\n    .p-password-input {\\n    border-bottom: none !important;\\n    border-radius: 8px !important;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}