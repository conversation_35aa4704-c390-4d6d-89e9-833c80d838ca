{"ast": null, "code": "export function passwordValidator() {\n  return control => {\n    const password = control.value.toLowerCase(); // Convert password to lowercase for case-insensitive checks\n    // const username = user.email; // Replace with actual username value\n    const applicationName = ['Yalla Mall', 'Momo Market']; // Replace with actual application name\n    const months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December', 'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE', 'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'];\n    const seasons = ['spring', 'summer', 'autumn', 'winter', 'Spring', 'Summer', 'Autumn', 'Winter', 'SPRING', 'SUMMER', 'AUTUMN', 'WINTER'];\n    const weakPasswords = [' ', '123456', '123456789', 'guest', 'qwerty', '12345678', '111111', '12345', 'col123456', '123123', '1234567', '1234', '1234567890', '000000', '555555', '666666', '123321', '654321', '7777777', '123', 'D1lakiss', '777777', '110110jp', '1111', '987654321', '121212', 'Gizli', 'abc123', '112233', 'azerty', '159753', '1q2w3e4r', '54321', 'pass@123', '222222', 'qwertyuiop', 'qwerty123', 'qazwsx', 'vip', 'asdasd', '123qwe', '123654', 'iloveyou', 'a1b2c3', '999999', 'Groupd2013', '1q2w3e', 'usr', 'Liman1000', '1111111', '333333', '123123123', '9136668099', '11111111', '1qaz2wsx', 'password1', 'mar20lt', '987654321', 'gfhjkm', '159357', 'abcd1234', '131313', '789456', 'luzit2000', 'aaaaaa', 'zxcvbnm', 'asdfghjkl', '1234qwer', '88888888', 'dragon', '987654', '888888', 'qwe123', 'football', '3601', 'asdfgh', 'master', 'samsung', '12345678910', 'killer', '1237895', '1234561', '12344321', 'daniel', '000000', '444444', '101010', 'fuckyou', 'qazwsxedc', '789456123', 'super123', 'qwer1234', '123456789a', '823477aA', '147258369', 'unknown', '98765', 'q1w2e3r4', '232323', '102030', '12341234', '147258', 'shadow', '123456a', '87654321', '10203', 'pokemon', 'princess', 'azertyuiop', 'thomas', 'baseball', 'monkey', 'jordan', 'michael', 'love', '1111111111', '11223344', '123456789', 'asdf1234', '147852', '252525', '11111', 'loulou', '111222', 'superman', 'qweasdzxc', 'soccer', 'qqqqqq', '123abc', 'computer', 'qweasd', 'zxcvbn', 'sunshine', '1234554321', 'asd123', 'marina', 'lol123', 'a123456', 'Password', '123789', 'jordan23', 'jessica', '212121', '7654321', 'googledummy', 'qwerty1', '123654789', 'naruto', 'Indya123', 'internet', 'doudou', 'anmol123', '55555', 'andrea', 'anthony', 'martin', 'basketball', 'nicole', 'xxxxxx', '1qazxsw2', 'charlie', '12345qwert', 'zzzzzz', 'q1w2e3', '147852369', 'hello', 'welcome', 'marseille', '456123', 'secret', 'matrix', 'zaq12wsx', 'password123', 'qwertyu', 'hunter', 'freedom', '999999999', 'eminem', 'junior', '696969', 'andrew', 'michelle', 'wow12345', 'juventus', 'batman', 'justin', '12qwaszx', 'Pass@123', 'passw0rd', 'soleil', 'nikita', 'Password1', 'qweqwe', 'nicolas', 'robert', 'starwars', 'liverpool', '5555555', 'bonjour', '124578']; // Add more weak passwords\n    // Check conditions and invalidate password if any condition is met\n    if (applicationName.some(applicationName => password.includes(applicationName)) || months.some(month => password.includes(month)) || seasons.some(season => password.includes(season)) || weakPasswords.includes(password)) {\n      return {\n        invalidPassword: {\n          value: control.value\n        }\n      };\n    }\n    return null;\n  };\n}", "map": {"version": 3, "names": ["passwordValidator", "control", "password", "value", "toLowerCase", "applicationName", "months", "seasons", "weakPasswords", "some", "includes", "month", "season", "invalidPassword"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\validators\\password.validator.ts"], "sourcesContent": ["import {AbstractControl, ValidatorFn} from '@angular/forms';\r\n\r\nexport function passwordValidator(): ValidatorFn {\r\n  return (control: AbstractControl): { [key: string]: any } | null => {\r\n    const password = control.value.toLowerCase(); // Convert password to lowercase for case-insensitive checks\r\n    // const username = user.email; // Replace with actual username value\r\n\r\n    const applicationName = ['Yalla Mall','Momo Market']; // Replace with actual application name\r\n    const months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December', 'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE', 'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'];\r\n    const seasons = ['spring', 'summer', 'autumn', 'winter', 'Spring', 'Summer', 'Autumn', 'Winter', 'SPRING', 'SUMMER', 'AUTUMN', 'WINTER'];\r\n    const weakPasswords = [' ','123456', '123456789', 'guest', 'qwerty', '12345678', '111111', '12345', 'col123456', '123123', '1234567', '1234', '1234567890', '000000', '555555', '666666', '123321', '654321', '7777777', '123', 'D1lakiss', '777777', '110110jp', '1111', '987654321', '121212', 'Gizli', 'abc123', '112233', 'azerty', '159753', '1q2w3e4r', '54321', 'pass@123', '222222', 'qwertyuiop', 'qwerty123', 'qazwsx', 'vip', 'asdasd', '123qwe', '123654', 'iloveyou', 'a1b2c3', '999999', 'Groupd2013', '1q2w3e', 'usr', 'Liman1000', '1111111', '333333', '123123123', '9136668099', '11111111', '1qaz2wsx', 'password1', 'mar20lt', '987654321', 'gfhjkm', '159357', 'abcd1234', '131313', '789456', 'luzit2000', 'aaaaaa', 'zxcvbnm', 'asdfghjkl', '1234qwer', '88888888', 'dragon', '987654', '888888', 'qwe123', 'football', '3601', 'asdfgh', 'master', 'samsung', '12345678910', 'killer', '1237895', '1234561', '12344321', 'daniel', '000000', '444444', '101010', 'fuckyou', 'qazwsxedc', '789456123', 'super123', 'qwer1234', '123456789a', '823477aA', '147258369', 'unknown', '98765', 'q1w2e3r4', '232323', '102030', '12341234', '147258', 'shadow', '123456a', '87654321', '10203', 'pokemon', 'princess', 'azertyuiop', 'thomas', 'baseball', 'monkey', 'jordan', 'michael', 'love', '1111111111', '11223344', '123456789', 'asdf1234', '147852', '252525', '11111', 'loulou', '111222', 'superman', 'qweasdzxc', 'soccer', 'qqqqqq', '123abc', 'computer', 'qweasd', 'zxcvbn', 'sunshine', '1234554321', 'asd123', 'marina', 'lol123', 'a123456', 'Password', '123789', 'jordan23', 'jessica', '212121', '7654321', 'googledummy', 'qwerty1', '123654789', 'naruto', 'Indya123', 'internet', 'doudou', 'anmol123', '55555', 'andrea', 'anthony', 'martin', 'basketball', 'nicole', 'xxxxxx', '1qazxsw2', 'charlie', '12345qwert', 'zzzzzz', 'q1w2e3', '147852369', 'hello', 'welcome', 'marseille', '456123', 'secret', 'matrix', 'zaq12wsx', 'password123', 'qwertyu', 'hunter', 'freedom', '999999999', 'eminem', 'junior', '696969', 'andrew', 'michelle', 'wow12345', 'juventus', 'batman', 'justin', '12qwaszx', 'Pass@123', 'passw0rd', 'soleil', 'nikita', 'Password1', 'qweqwe', 'nicolas', 'robert', 'starwars', 'liverpool', '5555555', 'bonjour', '124578']; // Add more weak passwords\r\n\r\n    // Check conditions and invalidate password if any condition is met\r\n    if ( applicationName.some(applicationName => password.includes(applicationName)) || months.some(month => password.includes(month)) || seasons.some(season => password.includes(season)) || weakPasswords.includes(password)) {\r\n      return {invalidPassword: {value: control.value}};\r\n    }\r\n\r\n    return null;\r\n  };\r\n}\r\n"], "mappings": "AAEA,OAAM,SAAUA,iBAAiBA,CAAA;EAC/B,OAAQC,OAAwB,IAAmC;IACjE,MAAMC,QAAQ,GAAGD,OAAO,CAACE,KAAK,CAACC,WAAW,EAAE,CAAC,CAAC;IAC9C;IAEA,MAAMC,eAAe,GAAG,CAAC,YAAY,EAAC,aAAa,CAAC,CAAC,CAAC;IACtD,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;IAC7X,MAAMC,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACxI,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEvqE;IACA,IAAKH,eAAe,CAACI,IAAI,CAACJ,eAAe,IAAIH,QAAQ,CAACQ,QAAQ,CAACL,eAAe,CAAC,CAAC,IAAIC,MAAM,CAACG,IAAI,CAACE,KAAK,IAAIT,QAAQ,CAACQ,QAAQ,CAACC,KAAK,CAAC,CAAC,IAAIJ,OAAO,CAACE,IAAI,CAACG,MAAM,IAAIV,QAAQ,CAACQ,QAAQ,CAACE,MAAM,CAAC,CAAC,IAAIJ,aAAa,CAACE,QAAQ,CAACR,QAAQ,CAAC,EAAE;MAC3N,OAAO;QAACW,eAAe,EAAE;UAACV,KAAK,EAAEF,OAAO,CAACE;QAAK;MAAC,CAAC;;IAGlD,OAAO,IAAI;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}