import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { InitiateApmWidgetRequest, InitiateApmWidgetResponse } from '@pages/checkout/modals/APM';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {

  readonly baseUrl: string;
  
  constructor(
    private http: HttpClient
  ) {
    this.baseUrl = `${environment.apiEndPoint}/Payment`;
  }

  getAllPayments(req: any): Observable<object> {

    return this.http.post(`${this.baseUrl}/Payment/GetAllPayment`, req);
  }

  getIsTenantCardPaymentEnabled() : Observable<boolean> {
    return this.http.get<boolean>(`${this.baseUrl}/Cube/IsTenantCardPaymentEnabled`, );
  }


  getApmConfig(payload: InitiateApmWidgetRequest): Observable<InitiateApmWidgetResponse> {
    const userId = localStorage.getItem('userId') ?? '';
    const headers = new HttpHeaders({ userId });
  
    return this.http.post<InitiateApmWidgetResponse>(
      `${this.baseUrl}/Apm/GetApmWidget`,
      payload,
      { headers }
    );
  }
  
}
