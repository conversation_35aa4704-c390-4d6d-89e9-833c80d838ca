{"ast": null, "code": "class TranslateHttpLoader {\n  constructor(http, prefix = \"/assets/i18n/\", suffix = \".json\") {\n    this.http = http;\n    this.prefix = prefix;\n    this.suffix = suffix;\n  }\n  /**\r\n   * Gets the translations from the server\r\n   */\n  getTranslation(lang) {\n    return this.http.get(`${this.prefix}${lang}${this.suffix}`);\n  }\n}\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { TranslateHttpLoader };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}