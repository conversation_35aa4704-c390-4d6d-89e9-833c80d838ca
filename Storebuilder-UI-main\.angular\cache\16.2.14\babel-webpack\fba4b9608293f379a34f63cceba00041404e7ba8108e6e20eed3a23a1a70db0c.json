{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PermissionService {\n  constructor() {\n    this.permissions = [];\n    this.tagFeatureFlag = [];\n  }\n  setPermissions(permissions) {\n    this.permissions = permissions;\n  }\n  hasPermission(permission) {\n    return this.permissions.includes(permission);\n  }\n  setTagFeature(permissions) {\n    this.tagFeatureFlag = permissions;\n  }\n  getTagFeature(permission) {\n    return this.tagFeatureFlag.includes(permission);\n  }\n  static #_ = this.ɵfac = function PermissionService_Factory(t) {\n    return new (t || PermissionService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PermissionService,\n    factory: PermissionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["PermissionService", "constructor", "permissions", "tagFeatureFlag", "setPermissions", "hasPermission", "permission", "includes", "setTagFeature", "getTagFeature", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\permission.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PermissionService {\r\n  private permissions: string[] = [];\r\n  private tagFeatureFlag: string[] = [];\r\n\r\n  setPermissions(permissions: string[]): void {\r\n    this.permissions = permissions;\r\n  }\r\n\r\n  hasPermission(permission: string): boolean {\r\n    return this.permissions.includes(permission);\r\n  }\r\n\r\n\r\n  setTagFeature(permissions: string[]): void {\r\n    this.tagFeatureFlag = permissions;\r\n  }\r\n  getTagFeature(permission: string): boolean {\r\n    return this.tagFeatureFlag.includes(permission);\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,iBAAiB;EAH9BC,YAAA;IAIU,KAAAC,WAAW,GAAa,EAAE;IAC1B,KAAAC,cAAc,GAAa,EAAE;;EAErCC,cAAcA,CAACF,WAAqB;IAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;EAChC;EAEAG,aAAaA,CAACC,UAAkB;IAC9B,OAAO,IAAI,CAACJ,WAAW,CAACK,QAAQ,CAACD,UAAU,CAAC;EAC9C;EAGAE,aAAaA,CAACN,WAAqB;IACjC,IAAI,CAACC,cAAc,GAAGD,WAAW;EACnC;EACAO,aAAaA,CAACH,UAAkB;IAC9B,OAAO,IAAI,CAACH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;EACjD;EAAC,QAAAI,CAAA,G;qBAlBUV,iBAAiB;EAAA;EAAA,QAAAW,EAAA,G;WAAjBX,iBAAiB;IAAAY,OAAA,EAAjBZ,iBAAiB,CAAAa,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}