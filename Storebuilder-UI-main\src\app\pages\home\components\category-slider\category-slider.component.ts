import {ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {Swiper} from "swiper";
import {Category} from "@core/interface";

@Component({
  selector: 'app-category-slider',
  templateUrl: './category-slider.component.html',
  styleUrls: ['./category-slider.component.scss']
})
export class CategorySliderComponent {
   @ViewChild('swiper') swiper: ElementRef | any;
  @Input() categories: Array<Category> = [];
constructor(private cd: ChangeDetectorRef,) {
  this.swiper  = new Swiper('swiper-container', {
    direction: 'horizontal',
    loop:true,
    navigation: true,
    spaceBetween: 10,
    autoplay: true,
  })
}

  ngOnInit(){
  let arr = this.categories.concat(this.categories);
  let arr1=arr.concat(this.categories);
  this.categories = arr1;
  this.cd.detectChanges();

}
  slideNext() {
    this.swiper.nativeElement.swiper.slideNext(1);
  }
  slidePrev() {
  this.swiper.nativeElement.swiper.slidePrev(100);
  }
}
