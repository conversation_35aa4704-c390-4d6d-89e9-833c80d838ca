{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class PagenotfoundComponent {\n  static #_ = this.ɵfac = function PagenotfoundComponent_Factory(t) {\n    return new (t || PagenotfoundComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PagenotfoundComponent,\n    selectors: [[\"app-mtn-pagenotfound\"]],\n    decls: 17,\n    vars: 3,\n    consts: [[1, \"not-found\", \"custom-css\", \"m-top\"], [1, \"container\", \"d-flex\", \"justify-content-around\"], [1, \"flex-column\"], [1, \"flex-row\", \"text-wrapper-5\", \"text-center\"], [1, \"flex-row\", \"text-wrapper-6\", \"text-center\"], [1, \"flex-row\"], [1, \"this-page-doesn-t\"], [\"routerLink\", \"/\", \"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-2\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"label\"], [\"src\", \"https://c.animaapp.com/RLaOyNW3/img/frame.svg\", \"alt\", \"Page not found image\", 1, \"img\", \"not-found-img\"]],\n    template: function PagenotfoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵtext(4, \"Oops....\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵtext(6, \"Page not found\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"p\", 6);\n        i0.ɵɵtext(9, \" This Page doesn't exist or was removed!\");\n        i0.ɵɵelement(10, \"br\");\n        i0.ɵɵtext(11, \"We suggest you\\u00A0\\u00A0back to home. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 5);\n        i0.ɵɵelement(13, \"button\", 7);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 2);\n        i0.ɵɵelement(16, \"img\", 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(14, 1, \"notFound.goBack\"));\n      }\n    },\n    dependencies: [i1.ButtonDirective, i2.RouterLink, i3.TranslatePipe],\n    styles: [\".header-spacing {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar {\\n  margin-top: 122px;\\n}\\n\\n.discount-price {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.text-wrapper-5 {\\n  font-family: var(--medium-font) !important;\\n  font-size: 40px;\\n  line-height: 52px;\\n  font-weight: 700;\\n}\\n\\n.text-wrapper-6 {\\n  font-family: var(--medium-font) !important;\\n  font-size: 32px;\\n  line-height: 41.6px;\\n  font-weight: 400;\\n}\\n\\n.this-page-doesn-t {\\n  font-family: var(--regular-font) !important;\\n  font-size: 16px;\\n  font-weight: 400;\\n  line-height: 24px;\\n  letter-spacing: 0.04em;\\n  text-align: center;\\n}\\n\\n.m-top {\\n  margin-top: 12rem !important;\\n}\\n\\n.not-found-img {\\n  width: 320px;\\n  height: 320px;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n  .m-top {\\n    margin-top: 1rem !important;\\n  }\\n  .not-found-img {\\n    width: 200px;\\n    height: 200px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .m-top {\\n    margin-top: 250px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvc2Nzcy9jb21tb24uc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvcGFnZW5vdGZvdW5kL3BhZ2Vub3Rmb3VuZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFpQ0E7RUFDRSxpQkF4QmM7QUNSaEI7O0FEbUNBO0VBQ0UsaUJBNUJjO0FDSmhCOztBRGtDQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsMENBQUE7QUMvQkY7O0FBWEE7RUFDRSwwQ0FBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBY0Y7O0FBWEE7RUFDRSwwQ0FBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtFQUVBLGdCQUFBO0FBYUY7O0FBVkE7RUFDRSwyQ0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtBQWFGOztBQVRBO0VBQ0UsNEJBQUE7QUFZRjs7QUFUQTtFQUNFLFlBQUE7RUFDQSxhQUFBO0FBWUY7O0FBVEE7RUFDRTtJQUNFLDJCQUFBO0VBWUY7RUFUQTtJQUNFLFlBQUE7SUFDQSxhQUFBO0VBV0Y7QUFDRjtBRGhDSTtFQ3lCRjtJQUNFLDRCQUFBO0VBVUY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIiR5ZWxsb3dCdXR0b25Db2xvcjogI0ZGQ0MwMDtcclxuJGJsdWVCdXR0b25Db2xvcjogIzAwNEY3MTtcclxuJGZvcm1GaWVsZE91dExpbmU6ICNGNUY1RjU7XHJcbiRoaW50TWVzc2FnZUNvbG9yOiAjQTNBM0EzO1xyXG4kd2hpdGVDb2xvcjogI2ZmZjtcclxuJGJsYWNrQ29sb3I6ICMwMDA7XHJcbiRmb250LW1haW4tYm9sZDogJ01UTkJyaWdodGVyU2Fucy1Cb2xkJztcclxuJGZvbnQtbWFpbi1tZWRpdW06ICdtYWluLW1lZGl1bSc7XHJcbiRmb250LW1haW4tcmVndWxhcjogJ21haW4tcmVndWxhcic7XHJcbiRmb250LW1haW4tbGlnaHQ6ICdtYWluLWxpZ2h0JztcclxuJGhlYWRlcl9oZWlnaHQ6IDEyMnB4O1xyXG4kaGVhZGVyX2hlaWdodF93aXRob3V0X25hdmJhcjogNzNweDtcclxuXHJcbiRicmVha3BvaW50czogKFxyXG4gIG1vYmlsZTogXCJvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzY3cHgpXCIsXHJcbiAgdGFibGV0OiBcIm9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiA3NjhweCkgYW5kIChtYXgtd2lkdGg6IDEyMDBweClcIixcclxuICBkZXNrdG9wOiBcIm9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAxMjAxcHgpIGFuZCAobWF4LXdpZHRoOiAxNzAwcHgpXCIsXHJcbiAgbGFyZ2UtZGVza3RvcDogXCJvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMTcwMXB4KVwiXHJcbik7XHJcblxyXG4vLyBEZWZpbmUgbWl4aW4gZm9yIG1lZGlhIHF1ZXJpZXNcclxuQG1peGluIHJlc3BvbnNpdmUoJGJyZWFrcG9pbnQpIHtcclxuICBAaWYgbWFwLWhhcy1rZXkoJGJyZWFrcG9pbnRzLCAkYnJlYWtwb2ludCkge1xyXG4gICAgQG1lZGlhICN7bWFwLWdldCgkYnJlYWtwb2ludHMsICRicmVha3BvaW50KX0ge1xyXG4gICAgICBAY29udGVudDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIEBlbHNlIHtcclxuICAgIEBlcnJvciBcIlVua25vd24gYnJlYWtwb2ludDogI3skYnJlYWtwb2ludH0uIFBsZWFzZSB1c2UgJ21vYmlsZScsICd0YWJsZXQnLCBvciAnZGVza3RvcCcuXCI7XHJcbiAgfVxyXG59XHJcblxyXG4uaGVhZGVyLXNwYWNpbmcge1xyXG4gIG1hcmdpbi10b3A6ICRoZWFkZXJfaGVpZ2h0O1xyXG59XHJcblxyXG4uaGVhZGVyLXNwYWNpbmctbm90LW5hdmJhciB7XHJcbiAgbWFyZ2luLXRvcDogJGhlYWRlcl9oZWlnaHQ7XHJcbn1cclxuLmRpc2NvdW50LXByaWNle1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBmb250LXdlaWdodDo1MDA7XHJcbiAgY29sb3I6I0UyMTgzNiAhaW1wb3J0YW50O1xyXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1tZWRpdW0tZm9udCkgIWltcG9ydGFudDtcclxufSIsIkBpbXBvcnRcIi4uLy4uLy4uLy4uL2Fzc2V0cy9zY3NzL2NvbW1vbi5zY3NzXCI7XHJcblxyXG4udGV4dC13cmFwcGVyLTUge1xyXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1tZWRpdW0tZm9udCkgIWltcG9ydGFudDtcclxuICBmb250LXNpemU6IDQwcHg7XHJcbiAgbGluZS1oZWlnaHQ6IDUycHg7XHJcbiAgZm9udC13ZWlnaHQ6IDcwMFxyXG59XHJcblxyXG4udGV4dC13cmFwcGVyLTYge1xyXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1tZWRpdW0tZm9udCkgIWltcG9ydGFudDtcclxuICBmb250LXNpemU6IDMycHg7XHJcbiAgbGluZS1oZWlnaHQ6IDQxLjZweDtcclxuXHJcbiAgZm9udC13ZWlnaHQ6IDQwMFxyXG59XHJcblxyXG4udGhpcy1wYWdlLWRvZXNuLXQge1xyXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1yZWd1bGFyLWZvbnQpICFpbXBvcnRhbnQ7XHJcbiAgZm9udC1zaXplOiAxNnB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgbGluZS1oZWlnaHQ6IDI0cHg7XHJcbiAgbGV0dGVyLXNwYWNpbmc6IDAuMDRlbTtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcblxyXG59XHJcblxyXG4ubS10b3Age1xyXG4gIG1hcmdpbi10b3A6IDEycmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5ub3QtZm91bmQtaW1nIHtcclxuICB3aWR0aDogMzIwcHg7XHJcbiAgaGVpZ2h0OiAzMjBweDtcclxufVxyXG5cclxuQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiA3ODZweCkge1xyXG4gIC5tLXRvcCB7XHJcbiAgICBtYXJnaW4tdG9wOiAxcmVtICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAubm90LWZvdW5kLWltZyB7XHJcbiAgICB3aWR0aDogMjAwcHg7XHJcbiAgICBoZWlnaHQ6IDIwMHB4O1xyXG4gIH1cclxufVxyXG5cclxuQGluY2x1ZGUgcmVzcG9uc2l2ZShtb2JpbGUpIHtcclxuICAubS10b3Age1xyXG4gICAgbWFyZ2luLXRvcDogMjUwcHggIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["PagenotfoundComponent", "_", "_2", "selectors", "decls", "vars", "consts", "template", "PagenotfoundComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpipeBind1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\pagenotfound\\pagenotfound.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\pagenotfound\\pagenotfound.component.html"], "sourcesContent": ["import {Component, ViewEncapsulation} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-mtn-pagenotfound',\r\n  templateUrl: './pagenotfound.component.html',\r\n  styleUrls: ['./pagenotfound.component.scss'],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class PagenotfoundComponent {\r\n\r\n}\r\n", "\r\n<div class=\"not-found custom-css m-top\" >\r\n  <div class=\"container d-flex justify-content-around\" >\r\n      <div class=\"flex-column\">\r\n\r\n        <div class=\"flex-row text-wrapper-5 text-center\">Oops....</div>\r\n        <div class=\"flex-row text-wrapper-6 text-center\">Page not found</div>\r\n        <div class=\"flex-row\">\r\n          <p class=\"this-page-doesn-t\">\r\n            This Page doesn't exist or was removed!<br/>We suggest you&nbsp;&nbsp;back to home.\r\n          </p>\r\n        </div>\r\n        <div class=\"flex-row\">\r\n          <button\r\n\r\n            routerLink=\"/\"\r\n            [label]=\"'notFound.goBack' | translate\"\r\n            class=\"p-field p-col-12 my-2 width-100 font-size-14 second-btn\"\r\n            pButton\r\n            type=\"button\"\r\n          ></button>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div class=\"flex-column\">\r\n        <img class=\"img not-found-img\" src=\"https://c.animaapp.com/RLaOyNW3/img/frame.svg\" alt=\"Page not found image\"\r\n            >\r\n      </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;AAQA,OAAM,MAAOA,qBAAqB;EAAA,QAAAC,CAAA,G;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA,G;UAArBF,qBAAqB;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPlCE,EAAA,CAAAC,cAAA,aAAyC;QAIgBD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC/DH,EAAA,CAAAC,cAAA,aAAiD;QAAAD,EAAA,CAAAE,MAAA,qBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACrEH,EAAA,CAAAC,cAAA,aAAsB;QAElBD,EAAA,CAAAE,MAAA,+CAAuC;QAAAF,EAAA,CAAAI,SAAA,UAAK;QAAAJ,EAAA,CAAAE,MAAA,gDAC9C;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACpBD,EAAA,CAAAI,SAAA,iBAOU;;QACZJ,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,cAAyB;QACvBD,EAAA,CAAAI,SAAA,cACK;QACPJ,EAAA,CAAAG,YAAA,EAAM;;;QAZAH,EAAA,CAAAK,SAAA,IAAuC;QAAvCL,EAAA,CAAAM,UAAA,UAAAN,EAAA,CAAAO,WAAA,2BAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}