{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate, __param } from \"tslib\";\nimport { Component, EventEmitter, Input, HostListener, Output, Inject, PLATFORM_ID } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { environment } from '@environments/environment';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { GuidGenerator } from \"@core/services\";\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { verifyImageURL } from '@pages/merchant-livestream/merchant-livestream-details/merchant-livestream-details.component';\nlet DetailsComponent = class DetailsComponent {\n  productLogicService;\n  messageService;\n  store;\n  fb;\n  router;\n  detailsService;\n  translate;\n  authTokenService;\n  cookieService;\n  userService;\n  modalService;\n  ref;\n  cartService;\n  mainDataService;\n  productService;\n  $gaService;\n  permissionService;\n  route;\n  addressService;\n  _GACustomEvents;\n  sanitizer;\n  platformId;\n  product = {};\n  channelId = '';\n  currency = {};\n  selectedVariant;\n  selectedSize;\n  selectedSize2;\n  selectedColor;\n  cols = [];\n  onItemLike = new EventEmitter();\n  onChangeVariant = new EventEmitter();\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  _BaseURL = environment.apiEndPoint;\n  Variant = {};\n  showMore = false;\n  reviewLimit = 0;\n  cartId = '0';\n  rate1;\n  rate2;\n  rate3;\n  scConfig = false;\n  rate4;\n  rate5;\n  rating;\n  rateCount;\n  reviews;\n  reviewrating;\n  reviewDes;\n  soldOut;\n  decimalValue = 0;\n  productForm;\n  textareaHeight = 0;\n  isDescription = false;\n  currencyCode = '';\n  reviewsLenght;\n  colorsValues = [];\n  colors = [];\n  sizes = [];\n  sizesValues = [];\n  variants = [];\n  variantIds = [];\n  displaySizes = [];\n  displayColors = [];\n  message = '';\n  displayModal = false;\n  displaySuccessModal = false;\n  displayShareModal = false;\n  CustomCountryISO;\n  genderSpeces;\n  isStoreCloud = environment.isStoreCloud;\n  disableCent;\n  authToken;\n  cartListCount = 0;\n  cartListData = [];\n  sizeGuidAttributeId;\n  sizeGuidImage = '';\n  productId;\n  screenWidth;\n  stockStatusId;\n  currentLink;\n  state;\n  isLayoutTemplate = false;\n  isMobileTemplate = false;\n  isShowNotifyFeature = false;\n  userDetails;\n  sessionId;\n  isEmailExist = false;\n  isGoogleAnalytics = false;\n  varianceSpec = [];\n  size1 = [];\n  size2 = [];\n  displayNotifyModal = false;\n  successTitleMessage = '';\n  successBodyMessage = '';\n  profile;\n  tagName = GaActionEnum;\n  tagNameLocal = GaLocalActionEnum;\n  selectedAddress;\n  displayAgeConsentModal = false;\n  displayEligableModal = false;\n  restrictionAge;\n  restrictedProductTobePurchased;\n  sanitizedDescription = '';\n  descriptionBase64 = false;\n  filteredProductSpecs = [];\n  filteredVarianceSpecs = [];\n  canShare = false;\n  // Constructor for the DetailsComponent\n  constructor(\n  // Injects a service to handle product-specific logic\n  productLogicService,\n  // Service to display messages (e.g., success, error, etc.)\n  messageService,\n  // Manages the application's state storage\n  store,\n  // FormBuilder for creating and managing forms\n  fb,\n  // Router service for navigation\n  router,\n  // Service to handle product details\n  detailsService,\n  // Handles translations for internationalization\n  translate,\n  // Service to manage authentication tokens\n  authTokenService,\n  // Service to handle browser cookies\n  cookieService,\n  // Service to manage user-related functionalities\n  userService,\n  // Service to manage modals\n  modalService,\n  // Change detection to force UI updates\n  ref,\n  // Service to manage shopping cart operations\n  cartService,\n  // Service to handle core data operations\n  mainDataService,\n  // Service to handle API requests for product-related operations\n  productService,\n  // Google Analytics service for tracking events\n  $gaService,\n  // Service to check user permissions\n  permissionService,\n  // Activated route to access route parameters and query strings\n  route,\n  // Service to manage user address data\n  addressService, _GACustomEvents, sanitizer,\n  // Injects the platform ID to differentiate between browser and server environments\n  platformId) {\n    this.productLogicService = productLogicService;\n    this.messageService = messageService;\n    this.store = store;\n    this.fb = fb;\n    this.router = router;\n    this.detailsService = detailsService;\n    this.translate = translate;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.userService = userService;\n    this.modalService = modalService;\n    this.ref = ref;\n    this.cartService = cartService;\n    this.mainDataService = mainDataService;\n    this.productService = productService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.route = route;\n    this.addressService = addressService;\n    this._GACustomEvents = _GACustomEvents;\n    this.sanitizer = sanitizer;\n    this.platformId = platformId;\n    // Checks if the environment is configured for store cloud\n    this.scConfig = environment.isStoreCloud;\n    // Checks if the user has permission for layout templates\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    // Checks if the user has permission for mobile layout templates\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    // Captures the current router state snapshot\n    this.state = router.routerState.snapshot;\n    // Retrieves a flag to disable displaying cents in prices\n    this.disableCent = localStorage.getItem('DisableCents');\n    // Retrieves and parses the decimal value for price formatting\n    let value = localStorage.getItem('CurrencyDecimal');\n    if (value) this.decimalValue = parseInt(value);\n    // Initializes the form group with a description field\n    this.productForm = this.fb.group({\n      description: new FormControl('')\n    });\n    // If running in a browser environment\n    if (isPlatformBrowser(this.platformId)) {\n      // Sets the initial screen width\n      this.screenWidth = window.innerWidth;\n      // Captures the current page URL\n      this.currentLink = window.location.href;\n    }\n    // Initialize gender specifications with translations\n    this.genderSpeces = {\n      1: this.translate.instant('productDetails.details.male'),\n      2: this.translate.instant('productDetails.details.female'),\n      3: this.translate.instant('productDetails.details.uniSex')\n    };\n  }\n  /**\r\n   * Initializes the component when it is first loaded.\r\n   * This method performs various setup tasks such as retrieving route parameters,\r\n   * setting up user profile data, fetching product details, and initializing UI states.\r\n   */\n  ngOnInit() {\n    // Retrieve the full URL of the current page\n    const fullUrl = window.location.href;\n    let productId;\n    let tenantId;\n    // Subscribe to query parameters to retrieve the tenant ID\n    this.route.queryParams.subscribe(params => {\n      tenantId = params.tenantId;\n    });\n    // Subscribe to route parameters to retrieve the product ID\n    this.route.params.subscribe(params => {\n      productId = params.id;\n    });\n    // Load user profile and session data from local storage\n    this.profile = localStorage.getItem('profile');\n    this.userDetails = this.store.get('profile');\n    this.sessionId = localStorage.getItem('sessionId');\n    // Check for specific permissions using the permission service\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isShowNotifyFeature = this.permissionService.hasPermission('Notify-Me');\n    this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n    // Update the scroll position via the user service\n    this.userService.updateScrollTop(true);\n    // If running in a browser environment, reset the scroll position to the top\n    if (isPlatformBrowser(this.platformId)) {\n      window.scroll({\n        top: 0,\n        left: 0,\n        behavior: 'smooth'\n      });\n      // Smooth scroll to the top\n      const scrollToTop = window.setInterval(() => {\n        let pos = window.scrollY ?? null;\n        if (pos > 0) {\n          window.scrollTo(0, pos - 50);\n        } else {\n          window.clearInterval(scrollToTop);\n        }\n      }, 10);\n    }\n    // Fetch customer address if the user profile exists\n    if (this.profile) {\n      this.getCustomerAddress();\n    }\n    this.getFilteredProductSpec();\n    this.getFilteredVarianceSpec();\n    // If the product has a description, initialize the description field\n    if (this.product.description) {\n      // Check if the description is Base64 encoded\n      this.descriptionBase64 = this.isBase64(this.product.description);\n      // Decode if Base64, otherwise keep the original\n      this.product.description = this.descriptionBase64 ? decodeURIComponent(escape(atob(this.product.description))) : this.product.description;\n      // Update the form control with a truncated version of the description\n      this.productForm.controls['description'].setValue(this.product.description.substring(0, 150) + '.....');\n      this.isDescription = true;\n      this.updateSanitizedDescription(this.product.description);\n    }\n    // Initialize product variants and ID\n    this.variants = this.product.productVariances;\n    this.productId = this.product.id;\n    // Fetch additional data and refresh the UI\n    this.setData();\n    this.ref.markForCheck();\n    this.ref.detectChanges();\n    // Retrieve the cart ID\n    this.getCartId();\n    // Set product rating and review data\n    this.rating = this.selectedVariant?.rate;\n    this.rateCount = this.selectedVariant?.count;\n    this.rate1 = this.selectedVariant?.rateOne;\n    this.rate2 = this.selectedVariant?.rateTwo;\n    this.rate3 = this.selectedVariant?.rateThree;\n    this.rate4 = this.selectedVariant?.rateFour;\n    this.rate5 = this.selectedVariant?.rateFive;\n    this.reviews = this.selectedVariant?.reviewDetails;\n    this.reviewsLenght = this.selectedVariant?.reviewDetails?.length;\n    // Set the initial height of the description textarea\n    this.textareaHeight = 70;\n    this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n    this.canShare = 'share' in navigator;\n  }\n  /**\r\n   * Computes the intersection of two arrays.\r\n   *\r\n   * @param arr1 - The first array of elements.\r\n   * @param arr2 - The second array of elements.\r\n   * @returns An array containing elements that are common to both input arrays.\r\n   */\n  performIntersection(arr1, arr2) {\n    // Convert the input arrays into sets for efficient lookup\n    const setA = new Set(arr1);\n    const setB = new Set(arr2);\n    // Initialize an array to store the intersection result\n    let intersectionResult = [];\n    // Iterate through elements in the second set\n    for (let i of setB) {\n      // If the element exists in the first set, add it to the result\n      if (setA.has(i)) {\n        intersectionResult.push(i);\n      }\n    }\n    // Return the array of intersecting elements\n    return intersectionResult;\n  }\n  /**\r\n   * Toggles the \"Read More\" functionality for the product description.\r\n   *\r\n   * When expanded, the full product description is displayed, and the textarea height is adjusted based on the number of lines.\r\n   * When collapsed, only a truncated version of the description is displayed with a fixed height.\r\n   */\n  readMore() {\n    if (!this.showMore) {\n      // Expand: Set the full product description and calculate the textarea height based on the number of lines.\n      this.productForm.controls['description'].setValue(this.product.description);\n      let lines = this.productForm.controls['description'].value.split(/\\s+/).length;\n      this.textareaHeight = (15 + lines) * 2; // Adjust the height dynamically.\n    } else {\n      // Collapse: Set a truncated version of the description and reset the height.\n      this.productForm.controls['description'].setValue(this.product.description.substring(0, 150) + '.....');\n      this.textareaHeight = 70; // Set to default height.\n    }\n    // Toggle the \"showMore\" flag to switch between expanded and collapsed states.\n    this.showMore = !this.showMore;\n  }\n  /**\r\n   * Adds a product to the cart and sends tracking data to Google Analytics if enabled.\r\n   *\r\n   * @param productItem - The specific product variant to be added to the cart.\r\n   * @param shopId - The ID of the shop from which the product is being added.\r\n   * @param productDetails - The general product details including name, category, and seller information.\r\n   */\n  addItem(productItem, shopId, productDetails) {\n    // Track the \"Add to Cart\" event in Google Analytics if enabled.\n    if (this.isGoogleAnalytics) {\n      this._GACustomEvents.addToCartEvent(productItem, productDetails);\n    }\n    // Do not proceed if the product is sold out.\n    if (this.product.soldOut) return;\n    // Assign the current cart ID to the product item.\n    productItem.cartId = this.cartId;\n    let isTrigger = true;\n    // Retrieve current cart data and handle adding the product.\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger) {\n        if (res && res.length > 0) {\n          // Check if the product already exists in the cart.\n          let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n          if (cartProduct) {\n            // Prevent exceeding item-per-customer limits if applicable.\n            if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n              });\n              isTrigger = false;\n              return;\n            }\n          }\n          // Prepare the request object for adding the product to the cart.\n          const reqObj = {\n            productId: this.product.id,\n            quantity: 1,\n            specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            cartId: this.cartId,\n            priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n            shopId: shopId,\n            sessionId: productItem.sessionId,\n            channelId: this.product?.channelId,\n            proSchedulingId: productItem.proSchedulingId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails);\n        } else {\n          // Add the product as a new item in the cart.\n          const reqObj = {\n            productId: this.product.id,\n            quantity: 1,\n            specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            cartId: this.cartId,\n            priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n            shopId: shopId,\n            sessionId: productItem.sessionId,\n            channelId: this.product?.channelId,\n            proSchedulingId: productItem.proSchedulingId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails);\n        }\n      }\n    });\n  }\n  /**\r\n   * Creates a cart entry for the given product and handles the response from the API.\r\n   *\r\n   * @param product - The product object containing details required for adding to the cart.\r\n   * @param productDetails - Additional details of the product, such as name and category.\r\n   * @param navigate - Optional flag to indicate whether to navigate after adding the product.\r\n   */\n  createCart(product, productDetails, navigate) {\n    // Ensure the product has a session ID; generate and store one if missing.\n    product.sessionId = localStorage.getItem('sessionId');\n    if (!product.sessionId) {\n      product.sessionId = GuidGenerator.newGuid();\n      localStorage.setItem('sessionId', product.sessionId);\n    }\n    // Call the cart service to add the product to the cart.\n    this.cartService.addToCart(product).subscribe({\n      next: res => {\n        // Handle successful addition to the cart.\n        if (!res.data.userFailedProductEligibility) {\n          if (res?.success) {\n            // Store the cart ID in localStorage.\n            localStorage.setItem(\"cartId\", res.data.cartItems[0].cartId);\n            // Show a success message.\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.cart'),\n              detail: this.translate.instant('ResponseMessages.successfullyAddedToCart')\n            });\n            // Fetch the updated cart items.\n            this.getAllCart(product.sessionId, navigate);\n          } else {\n            // Show an error message if the cart operation failed.\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.cart'),\n              detail: res.message\n            });\n          }\n        } else {\n          // Handle age restriction for the product.\n          this.restrictionAge = res.data.productAgeRestriction;\n          this.restrictedProductTobePurchased = {\n            product: product,\n            productDetails: productDetails,\n            navigate: navigate\n          };\n          this.displayAgeConsentModal = true;\n        }\n      },\n      error: err => {\n        // Show an error message for any API errors.\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  /**\r\n   * Handles the \"Buy Now\" action for a product, adding it to the cart and navigating to checkout.\r\n   *\r\n   * @param productItem - The specific product item being purchased.\r\n   * @param shopId - The ID of the shop where the product is sold.\r\n   * @param productDetails - Additional details about the product, such as name and category.\r\n   */\n  shopNow(productItem, shopId, productDetails) {\n    // Trigger Google Analytics event for \"Buy Now\" action, if enabled.\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(this.tagNameLocal.CLICK_ON_BUY_NOW, 'product', 'BUY_NOW', 1, true, {\n        product_ID: productDetails.id,\n        product_name: productDetails.name,\n        category_name: productDetails.categoryName,\n        user_ID: this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        session_ID: this.sessionId,\n        shop_ID: productDetails.shopId,\n        product_SKU: productItem?.skuAutoGenerated,\n        seller_name: productDetails?.sellerName,\n        ip_Address: this.store.get('userIP'),\n        device_Type: this.store.get('deviceInfo')?.deviceType,\n        device_Id: this.store.get('deviceInfo')?.deviceId,\n        product_tags: productItem?.bestSeller ? 'Best Seller' : productItem?.newArrival ? 'New Arrival' : productItem?.hotDeals ? 'Hot Deals' : 'None',\n        promotion: productItem?.promotionName ? productItem?.promotionName : 'None'\n      });\n    }\n    // Return if the product is sold out.\n    if (this.product.soldOut) return;\n    // Assign cart ID to the product item.\n    productItem.cartId = this.cartId;\n    let isTrigger = true;\n    // Fetch existing cart data to manage quantities and avoid duplicates.\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger) {\n        if (res && res.length > 0) {\n          // Check if the product already exists in the cart and manage customer quantity limits.\n          let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n          if (cartProduct) {\n            if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n              });\n              isTrigger = false;\n              return;\n            }\n          }\n          // Construct the request object and proceed to add the product to the cart.\n          const reqObj = {\n            productId: this.product.id,\n            quantity: 1,\n            specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            cartId: this.cartId,\n            priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n            shopId: shopId,\n            sessionId: productItem.sessionId,\n            channelId: this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails, true);\n        } else {\n          // If the cart is empty, create a new cart item.\n          const reqObj = {\n            productId: this.product.id,\n            quantity: 1,\n            specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            cartId: this.cartId,\n            priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n            shopId: shopId,\n            sessionId: productItem.sessionId,\n            channelId: this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails, true);\n        }\n      }\n    });\n  }\n  /**\r\n   * Retrieves the cart ID from the store's \"cartProducts\" subscription.\r\n   * Updates the `cartId` property if cart products exist in the store.\r\n   */\n  getCartId() {\n    // Subscribe to the \"cartProducts\" observable in the store.\n    this.store.subscription('cartProducts').subscribe({\n      // On successful retrieval of cart products.\n      next: res => {\n        // If cart products exist, update the cartId with the first product's cartId.\n        if (res.length > 0) {\n          this.cartId = res[0].cartId;\n        }\n      },\n      // Handle any errors encountered during the subscription.\n      error: err => {\n        console.error(err); // Log the error for debugging purposes.\n      }\n    });\n  }\n  /**\r\n   * Checks the stock status of the selected variant and returns an appropriate message.\r\n   *\r\n   * @returns {string} - Translated message indicating whether the product is in stock or sold out.\r\n   */\n  checkUsername() {\n    // If the selected variant is not sold out, return the \"in stock\" message.\n    if (!this.selectedVariant?.soldOut) {\n      return this.translate.instant('productCard.instock');\n    }\n    // Otherwise, return the \"sold out\" message.\n    else {\n      return this.translate.instant('productCard.soldOut');\n    }\n  }\n  /**\r\n   * Toggles the wishlist status for a product. Adds or removes the product from the wishlist based on the current state.\r\n   *\r\n   * @param {any} specsProductId - The ID of the specific product variant to add/remove.\r\n   * @param {any} flag - Indicates whether the product is currently in the wishlist (true = remove, false = add).\r\n   * @param {Product} productDetails - Details of the product to be added/removed from the wishlist.\r\n   */\n  addToWishlist(specsProductId, flag, productDetails) {\n    // Track \"add to wishlist\" event in Google Analytics if enabled.\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event('add_to_wishlist', 'product', specsProductId);\n    }\n    // Retrieve authentication token from the service or cookies.\n    this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n    if (!this.authToken) {\n      this.authToken = this.cookieService.get('authToken');\n    }\n    // If the user is not authenticated, redirect to the login page.\n    if (!this.authToken) {\n      this.router.navigate(['login'], {\n        queryParams: {\n          returnUrl: this.state.url\n        }\n      });\n      return;\n    }\n    // Prepare the request object for toggling the wishlist status.\n    const obj = {\n      specsProductId,\n      flag,\n      productId: this.product.id,\n      channelId: this.product.channelId\n    };\n    // Call the wishlist toggle service and handle the response.\n    this.detailsService.wishlistToggle(obj).subscribe({\n      next: res => {\n        if (res?.success) {\n          // Toggle the \"isLiked\" state for the selected variant.\n          this.selectedVariant.isLiked = !this.selectedVariant.isLiked;\n          if (!flag) {\n            // Handle successful addition to the wishlist.\n            if (this.isGoogleAnalytics) {\n              this._GACustomEvents.addToWishlistEvent(productDetails, this.selectedVariant);\n              this.$gaService.event(this.tagName.ADD_TO_WISHLIST, productDetails.categoryName, productDetails.name, 1, true, {\n                \"product_ID\": productDetails.id,\n                \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                \"session_ID\": this.sessionId,\n                \"shop_ID\": productDetails.shopId\n              });\n            }\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyAddedToWishList')\n            });\n          } else {\n            // Handle successful removal from the wishlist.\n            if (this.isGoogleAnalytics) {\n              this.$gaService.event(\"remove_from_wishlist\", productDetails.categoryName, 'DELETE_FROM_CART', 1, true, {\n                \"product_name\": productDetails.name,\n                \"product_ID\": productDetails.id,\n                \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                \"session_ID\": this.sessionId,\n                \"shop_ID\": productDetails.shopId\n              });\n            }\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyRemovedToWishList')\n            });\n          }\n        }\n      },\n      error: err => {\n        // Handle errors during the wishlist toggle operation.\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  /**\r\n   * Handles the selection of a size attribute for a product variant.\r\n   * Updates the selected size, selected variant, and emits the updated variant.\r\n   *\r\n   * @param {any} size - The size object that the user selects.\r\n   * @param {number} type - The type of size selection (0 for the first size group, 1 for the second).\r\n   */\n  onSizeChange(size, type) {\n    // Update the selected size or size2 based on the type\n    if (type === 0) {\n      this.selectedSize = size.value; // Update primary size\n    } else {\n      this.selectedSize2 = size.value; // Update secondary size\n    }\n    // Filter available variants dynamically based on selected attributes\n    const matchingVariants = this.product.productVariances.filter(variant => {\n      const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name === \"Size\" && spec.value === this.selectedSize) : true; // Match any variant if no size is selected\n      const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name === \"Size 2\" && spec.value === this.selectedSize2) : true; // Match any variant if no size 2 is selected\n      return sizeMatch && size2Match;\n    });\n    // If matching variants exist, select the first one and update attributes\n    if (matchingVariants.length > 0) {\n      this.selectedVariant = matchingVariants[0];\n      this.selectedColor = this.selectedVariant.color || this.selectedColor;\n      this.selectedSize = this.selectedVariant.varianceSpecs.find(spec => spec.name === \"Size\")?.value || this.selectedSize;\n      this.selectedSize2 = this.selectedVariant.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || this.selectedSize2;\n    } else {\n      // If no matching variants exist, reset the selected variant\n      this.selectedVariant = null;\n    }\n    // Emit the updated variant for other components\n    this.onChangeVariant.emit(this.selectedVariant);\n    // Trigger Angular change detection to update the UI\n    this.ref.detectChanges();\n    this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n  }\n  /**\r\n   * Handles the selection of a color attribute for a product variant.\r\n   * Updates the selected color, adjusts available sizes, determines the appropriate variant, and emits the updated variant.\r\n   *\r\n   * @param {any} color - The color object that the user selects.\r\n   */\n  onColorChange(color) {\n    // Update the selected color\n    this.selectedColor = color.value;\n    // Filter variants for the selected color\n    this.variants = this.product.productVariances.filter(variant => variant.color === this.selectedColor);\n    // Find a matching variant based on the current size and size2 selections\n    const matchingVariant = this.variants.find(variant => {\n      const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name === \"Size\" && spec.value === this.selectedSize) : true;\n      const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name === \"Size 2\" && spec.value === this.selectedSize2) : true;\n      return sizeMatch && size2Match;\n    });\n    // If a matching variant exists, update the selected attributes\n    if (matchingVariant) {\n      this.selectedVariant = matchingVariant;\n      this.selectedSize = matchingVariant.varianceSpecs.find(spec => spec.name === \"Size\")?.value || this.selectedSize;\n      this.selectedSize2 = matchingVariant.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || this.selectedSize2;\n    } else {\n      // If no matching variant exists, fallback to the first variant for the selected color\n      this.selectedVariant = this.variants[0] || null;\n      this.selectedSize = this.selectedVariant?.varianceSpecs.find(spec => spec.name === \"Size\")?.value || null;\n      this.selectedSize2 = this.selectedVariant?.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || null;\n    }\n    // Update the display sizes to reflect the selected color\n    this.displaySizes = this.variants.flatMap(variant => variant.varianceSpecs.filter(spec => spec.name === \"Size\").map(spec => spec.value));\n    // Emit the updated variant\n    this.onChangeVariant.emit(this.selectedVariant);\n    // Trigger change detection to update the UI\n    this.ref.detectChanges();\n    this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n  }\n  /**\r\n   *\r\n   * @param variant  - The variant object that the user selects.\r\n   * @returns  - A boolean value indicating whether the selected variant is available for the current color.\r\n   */\n  isVariantAvailable(variant) {\n    // Match selected color\n    const colorMatch = this.selectedColor ? variant.color === this.selectedColor : true;\n    // Match selected size (if exists)\n    const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name.toLowerCase().includes(\"size\") && spec.value === this.selectedSize) : true;\n    // Match selected size2 (if exists)\n    const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name.toLowerCase().includes(\"size 2\") && spec.value === this.selectedSize2) : true;\n    // Return true if the variant matches all selected attributes\n    return colorMatch && sizeMatch && size2Match;\n  }\n  productImage = '';\n  productPrice = '';\n  productLowStock = '';\n  onVariantChange(variant) {\n    if (this.isVariantAvailable(variant)) {\n      this.selectedVariant = variant;\n      // Dynamically resolve attribute names\n      const colorAttribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Color')?.attributeName || 'Color';\n      const sizeAttribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Size')?.attributeName || 'Size';\n      const size2Attribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Size 2')?.attributeName || 'Size 2';\n      // Dynamically update selected attributes\n      this.selectedColor = variant.varianceSpecs.find(spec => spec.name === colorAttribute)?.value || this.selectedColor;\n      this.selectedSize = variant.varianceSpecs.find(spec => spec.name === sizeAttribute)?.value || this.selectedSize;\n      this.selectedSize2 = variant.varianceSpecs.find(spec => spec.name === size2Attribute)?.value || this.selectedSize2;\n      // Emit the updated variant\n      this.onChangeVariant.emit(this.selectedVariant);\n      // Trigger change detection\n      this.ref.detectChanges();\n    }\n  }\n  /**\r\n   *\r\n   * @param color  - The color object that the user selects.\r\n   * @returns  - A boolean value indicating whether the selected color has available variants.\r\n   */\n  isColorAvailable(color) {\n    // Check if the color has available variants\n    return this.variants.some(variant => variant.color === color.value);\n  }\n  /**\r\n   * Checks whether a size is available for the selected color.\r\n   * Returns false for all sizes if no variants exist for the selected color.\r\n   *\r\n   * @param {any} size - The size object to check.\r\n   * @returns {boolean} - True if the size is available, false otherwise.\r\n   */\n  isSizeAvailable(size) {\n    // Check size availability based on variants\n    return this.variants.some(variant => variant.varianceSpecs.some(spec => spec.value === size.value));\n  }\n  /**\r\n   * Updates the selected sizes (Size and Size 2) based on the currently selected variant's specifications.\r\n   * This ensures that the selected size attributes reflect the active variant's data.\r\n   */\n  getVariantSelectedSize() {\n    // Check if the current variant has a specification named \"Size\" and update the selected size accordingly.\n    if (this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size\")) {\n      this.selectedSize = this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size\").value;\n    }\n    // Check if the current variant has a specification named \"Size 2\" and update the selected size2 accordingly.\n    if (this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size 2\")?.value) {\n      this.selectedSize2 = this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size 2\").value;\n    }\n    // Trigger Angular's change detection to update the UI with the new size selections.\n    this.ref.markForCheck();\n    this.ref.detectChanges();\n  }\n  /**\r\n   * Displays the size guide modal to the user.\r\n   * This method sets the `displayModal` flag to `true`, triggering the modal to appear.\r\n   */\n  showSizeModal() {\n    this.displayModal = true; // Set the flag to display the size guide modal.\n  }\n  /**\r\n   * Fetches the size guide image based on the provided size guide attribute ID.\r\n   * This method calls the `getSizeGuidDetails` service to retrieve the size guide details.\r\n   * If an image URL is returned, it verifies and assigns the URL to `sizeGuidImage`.\r\n   *\r\n   * @param sizeGuidAttributeId - The ID of the size guide attribute.\r\n   */\n  getSizeGuideImage(sizeGuidAttributeId) {\n    this.productService.getSizeGuidDetails(sizeGuidAttributeId).subscribe({\n      next: res => {\n        if (res?.data?.imageUrl) {\n          // Verify and set the size guide image URL.\n          this.sizeGuidImage = UtilityFunctions.verifyImageURL(res?.data?.imageUrl, this._BaseURL);\n        }\n      },\n      error: () => {\n        //TODO: Handle errors silently.\n        // Handle errors silently.\n      }\n    });\n  }\n  /**\r\n   * Handles the submission of a form or action.\r\n   * This method is typically triggered by a form submission event.\r\n   * It closes the modal by setting `displayModal` to `false`.\r\n   *\r\n   * @param event - The event object triggered on form submission.\r\n   */\n  onSubmit(event) {\n    this.displayModal = false;\n  }\n  /**\r\n   * Fetches all cart items associated with a specific session and optionally navigates to the cart page.\r\n   * Updates the cart list count and data based on the API response, and synchronizes this data with the application state.\r\n   *\r\n   * @param sessionId - The session ID to identify the user's cart.\r\n   * @param navigate - A boolean flag indicating whether to navigate to the cart page after fetching data.\r\n   */\n  getAllCart(sessionId, navigate) {\n    let cartData = {\n      sessionId: sessionId\n    };\n    // Include \"applyTo\" parameter if it exists in local storage\n    let applyTo = localStorage.getItem('apply-to');\n    if (applyTo) {\n      cartData['applyTo'] = applyTo;\n    }\n    // Fetch cart data using the CartService\n    this.cartService.getCart(cartData).subscribe({\n      next: res => {\n        this.cartListCount = 0;\n        this.cartListData = [];\n        if (res.data?.records?.length) {\n          this.cartListCount = 0;\n          // Update cart count and data from regular cart details\n          if (res.data.records[0].cartDetails.length) {\n            this.cartListCount = res.data.records[0].cartDetails.length;\n            this.cartListData = res.data.records[0].cartDetails;\n          }\n          // Update cart count and data from DPay cart details\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n          }\n          // Synchronize cart data with the application state\n          this.mainDataService.setCartLenghtData(this.cartListCount);\n          this.mainDataService.setCartItemsData(this.cartListData);\n        } else {\n          // Reset cart data if no records exist\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n        }\n        // Navigate to the cart page if the `navigate` flag is true\n        if (navigate) {\n          this.router.navigate(['/cart']);\n        }\n      },\n      error: () => {\n        // Handle any errors that occur during the API call\n      }\n    });\n  }\n  /**\r\n   * Displays the share modal to allow users to share product details.\r\n   * This method simply sets the `displayShareModal` flag to `true`,\r\n   * triggering the modal to appear in the UI.\r\n   */\n  showShareModal() {\n    if (!this.canShare) {\n      this.displayShareModal = true;\n    } else {\n      this.shareContent();\n    }\n  }\n  /**\r\n   * Copies the provided link to the user's clipboard.\r\n   *\r\n   * Steps:\r\n   * - Creates a temporary, invisible `textarea` element to hold the link.\r\n   * - Selects the link and copies it to the clipboard using the `navigator.clipboard.writeText` API.\r\n   * - Removes the temporary `textarea` element after copying.\r\n   * - Closes the share modal (`displayShareModal`) and shows a success modal with a message indicating\r\n   *   the link was copied successfully.\r\n   *\r\n   * @param val - The URL or string to be copied to the clipboard.\r\n   */\n  copyLink(val) {\n    if (isPlatformBrowser(this.platformId)) {\n      const selBox = document.createElement('textarea');\n      selBox.style.position = 'fixed';\n      selBox.style.left = '0';\n      selBox.style.top = '0';\n      selBox.style.opacity = '0';\n      selBox.value = val;\n      document.body.appendChild(selBox);\n      selBox.focus();\n      selBox.select();\n      navigator.clipboard.writeText(this.currentLink);\n      document.body.removeChild(selBox);\n    }\n    // Close the share modal and display success message\n    this.displayShareModal = false;\n    this.successTitleMessage = this.translate.instant(\"productDetails.details.linkIsCopiedSuccessfully\");\n    // this.displaySuccessModal = true;\n  }\n  /**\r\n   * Opens the \"Notify Me\" modal to allow the user to subscribe for notifications about a product.\r\n   *\r\n   * Steps:\r\n   * - Sets the `displayNotifyModal` flag to `true` to display the modal.\r\n   * - Checks the user's profile data from `localStorage` to determine if an email address exists.\r\n   * - If the user's profile contains an email, sets `isEmailExist` to `true`; otherwise, sets it to `false`.\r\n   */\n  notifyMe() {\n    this.displayNotifyModal = true;\n    // Retrieve and parse the user's profile data from localStorage\n    const profile = JSON.parse(localStorage.getItem('profile') ?? '');\n    // Check if the profile contains an email and update `isEmailExist` accordingly\n    if (profile?.email) {\n      this.isEmailExist = true;\n    } else {\n      this.isEmailExist = false;\n    }\n  }\n  /**\r\n   * Handles the submission of the \"Notify Me\" form.\r\n   *\r\n   * Steps:\r\n   * 1. Constructs a request object (`reqObj`) to store notification details such as email and phone number.\r\n   * 2. Checks if the product has variances and includes the `specProductId` in the request object.\r\n   * 3. Processes the user's phone input, formatting it to include the dial code and number without spaces.\r\n   * 4. Calls the `notifyMeProduct` service to submit the notification request.\r\n   * 5. On success:\r\n   *    - Sets success messages using translations.\r\n   *    - Displays the success modal and hides the notify modal.\r\n   *\r\n   * @param data - Form data containing user inputs such as email and phone number.\r\n   */\n  onSubmitNotify(data) {\n    let reqObj = {};\n    // Set the email in the request object, or leave it empty if not provided\n    reqObj['email'] = data.email ? data.email : '';\n    // Include the specProductId if the product has variances\n    if (this.product.productVariances.length) {\n      reqObj['specProductId'] = this.product.productVariances[0].specProductId;\n    }\n    // Format and include the phone number if provided, or set it to an empty string\n    if (data.phone) {\n      let dialCode = data.phone.dialCode.substring(1, 4);\n      reqObj['phoneNumber'] = dialCode + data.phone.number.replace(/\\s/g, '');\n    } else {\n      reqObj['phoneNumber'] = '';\n    }\n    // Call the service to submit the \"Notify Me\" request\n    this.productService.notifyMeProduct(reqObj).subscribe(res => {\n      if (res.success) {\n        // Display success messages and modal\n        this.successTitleMessage = this.translate.instant(\"notifyMeDetails.thanksForInterest\");\n        this.successBodyMessage = this.translate.instant(\"notifyMeDetails.notifyProductIsAvaialble\");\n        this.displaySuccessModal = true;\n        this.displayNotifyModal = false;\n      }\n    });\n  }\n  /**\r\n   * Handles the cancellation of modals.\r\n   *\r\n   * This function is used to close both the success modal and the \"Notify Me\" modal\r\n   * by setting their respective display flags to `false`.\r\n   */\n  onCancel() {\n    this.displaySuccessModal = false; // Close the success modal\n    this.displayNotifyModal = false; // Close the \"Notify Me\" modal\n  }\n  /**\r\n   * Processes product variant data to prepare for rendering and interaction.\r\n   *\r\n   * - This function extracts and organizes the `colors` and `sizes` data from the product's variants.\r\n   * - For each variant, it adds color and size information, ensuring unique entries in the respective arrays.\r\n   * - Links sizes to colors, and vice versa, to maintain associations between attributes.\r\n   * - Sets up size guide images if available for the first size variant.\r\n   */\n  setData() {\n    // Iterate through each product variant\n    this.product.productVariances.forEach((variant, index) => {\n      this.variantIds.push(variant.specProductId);\n      // Handle color attributes\n      if (variant.color) {\n        if (!this.colorsValues.includes(variant.color)) {\n          this.colors.push({\n            value: variant.color,\n            variants: [variant],\n            variantSpecIds: [variant.specProductId],\n            sizes: []\n          });\n          this.colorsValues.push(variant.color);\n        } else {\n          let index = this.colorsValues.indexOf(variant.color);\n          this.colors[index].variants.push(variant);\n          this.colors[index].variantSpecIds.push(variant.specProductId);\n        }\n      }\n      // Process additional specs for marketplace products\n      if (this.product.channelId == '1') {\n        variant.varianceSpecs.forEach(specs => {\n          // Handle size attributes (Size and Size 2)\n          if (specs.name === \"Size\" || specs.name === 'Size 2') {\n            if (index === 0) {\n              this.getSizeGuideImage(specs.attributeId);\n            }\n            if (!this.sizesValues.includes(specs.value)) {\n              this.sizes.push({\n                value: specs.value,\n                name: specs.name,\n                variants: [variant],\n                variantSpecIds: [variant.specProductId],\n                colors: [],\n                attributeValueId: specs.attributeValueId\n              });\n              this.sizesValues.push(specs.value);\n            } else {\n              let index = this.sizesValues.indexOf(specs.value);\n              this.sizes[index].variants.push(variant);\n              this.sizes[index].variantSpecIds.push(variant.specProductId);\n            }\n          }\n        });\n      }\n    });\n    // Associate sizes with colors and manage display sizes\n    this.colors.forEach(color => {\n      if (color.value === this.selectedColor) {\n        if (color.sizes.length) {\n          this.variants = this.performIntersection(this.variants, color.variants);\n        }\n        this.displaySizes = color.sizes;\n        this.size1 = this.sizes.filter(item => item.name === 'Size');\n        this.size2 = this.sizes.filter(item => item.name === 'Size 2');\n      }\n      this.sizes.forEach(size => {\n        let relateSize = this.performIntersection(size.variants, color.variants);\n        if (relateSize.length > 0) {\n          color.sizes.push(size);\n        }\n      });\n    });\n    // Finalize color-size associations\n    this.setColorsSize();\n  }\n  /**\r\n   * Associates colors and sizes based on their shared variants.\r\n   *\r\n   * - Iterates through the list of `sizes` to link associated `colors` based on variant intersections.\r\n   * - For each size:\r\n   *   - Filters and updates the list of associated colors.\r\n   *   - Narrows down the variants based on the selected size and its associated colors.\r\n   * - If no colors are available, defaults the display to all sizes and segregates `size1` and `size2` based on their names.\r\n   */\n  setColorsSize() {\n    // Loop through each size to associate related colors\n    this.sizes.forEach(size => {\n      if (size.value === this.selectedSize) {\n        // If the size has associated colors, filter the variants\n        if (size.colors.length) {\n          this.variants = this.performIntersection(this.variants, size.variants);\n        }\n        // Update the displayColors to match the colors associated with this size\n        this.displayColors = size.colors;\n      }\n      // Associate colors to the current size based on shared variants\n      this.colors.forEach(color => {\n        let relateColor = this.performIntersection(color.variants, size.variants);\n        if (relateColor.length > 0) {\n          size.colors.push(color);\n        }\n      });\n    });\n    // Default behavior when no colors are available it should display all sizes dimmed\n    if (!this.colors || this.colors.length === 0) {\n      this.displaySizes = this.sizes;\n      this.size1 = this.sizes.filter(item => item.name === 'Size');\n      this.size2 = this.sizes.filter(item => item.name === 'Size 2');\n    }\n  }\n  /**\r\n   * Checks if a label associated with a given key exists and processes variance specifications accordingly.\r\n   *\r\n   * @param {string} key - The field mapping key to search for in the `cols` array.\r\n   * @param {number} [type=0] - An optional parameter (default: 0) that can be used for additional logic if needed in the future.\r\n   * @returns {any[]} - An array of columns that match the given key.\r\n   *\r\n   * **Logic:**\r\n   * - If the `cols` array has elements:\r\n   *   1. Initialize `varianceSpec` as an empty array.\r\n   *   2. Filter `cols` to find items where the `templateDefaultFields.fieldMapping` matches the given key.\r\n   *   3. For each matching item:\r\n   *      - Iterate over the `varianceSpecs` of the `selectedVariant`.\r\n   *      - If the `attributeId` matches, add the spec to `varianceSpec`.\r\n   *   4. Remove duplicates from `varianceSpec`.\r\n   *   5. Return the filtered columns.\r\n   */\n  checkLabel(key, type = 0) {\n    if (this.cols.length) {\n      this.varianceSpec = [];\n      // Filter columns based on the key\n      const data = this.cols.filter(item => item.templateDefaultFields.fieldMapping === key);\n      // Map matching columns to variance specs\n      data.forEach(item => {\n        this.selectedVariant.varianceSpecs.forEach(spec => {\n          if (spec.attributeId === item.attributeId) {\n            this.varianceSpec.push(spec);\n          }\n        });\n      });\n      // Remove duplicate entries from varianceSpec\n      this.varianceSpec = this.removeDuplicates(this.varianceSpec);\n      // Return filtered columns that match the key\n      return this.cols.filter(item => item.templateDefaultFields.fieldMapping === key);\n    }\n  }\n  /**\r\n   * Removes duplicate objects from an array based on a unique `attributeValueId` property.\r\n   *\r\n   * @param {any[]} data - The array of objects to process.\r\n   * @returns {any[]} - A new array with duplicate entries removed.\r\n   *\r\n   * **Logic:**\r\n   * - Iterates over the input array and filters out duplicate entries.\r\n   * - Uses `findIndex` to check if the current item's `attributeValueId` already exists in the array.\r\n   * - Ensures that only the first occurrence of each unique `attributeValueId` is retained.\r\n   * ```\r\n   */\n  removeDuplicates(data) {\n    return data.filter((item, index, self) => index === self.findIndex(t => t.attributeValueId === item.attributeValueId));\n  }\n  /**\r\n   * Retrieves the customer's addresses and sets the default address if available.\r\n   *\r\n   * **Functionality:**\r\n   * - Calls the `getAddress` method from the `AddressService` to fetch the list of customer addresses.\r\n   * - Checks if there are any addresses in the response.\r\n   * - Finds and sets the `selectedAddress` to the address marked as default (`isDefault`).\r\n   *\r\n   * **API Response Handling:**\r\n   * - The `next` block processes the API response.\r\n   * - If the response contains a `records` array, it looks for the address with `isDefault` set to `true`.\r\n   * - No action is taken if no default address is found or if the `records` array is empty.\r\n   *\r\n   * **Example Use Case:**\r\n   * - Useful in applications where the user has multiple saved addresses, and a default address is pre-selected for convenience during checkout.\r\n   */\n  getCustomerAddress() {\n    this.addressService.getAddress().subscribe({\n      next: res => {\n        if (res.data.records.length) {\n          this.selectedAddress = res.data.records.find(item => item.isDefault);\n        }\n      }\n    });\n  }\n  /**\r\n   * Sends an event to Google Analytics for tracking purposes.\r\n   *\r\n   * @param tagNameKey - The key representing the analytics event name.\r\n   * @param label - A label describing the event for context.\r\n   *\r\n   * If Google Analytics is enabled (`isGoogleAnalytics`), this method logs the event using `$gaService.event`.\r\n   */\n  triggerAnalytics(tagNameKey, label) {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(this.tagNameLocal[tagNameKey], '', label, 1, true);\n    }\n  }\n  /**\r\n   * Handles the submission of the age consent modal.\r\n   *\r\n   * This method gathers user session and profile data, creates an age consent object (`AgeConsent`),\r\n   * and sends it to the backend via `userService.updateAgeConsent`. If the backend call is successful,\r\n   * the product is added to the cart. If an error occurs, it is handled gracefully.\r\n   */\n  onSubmitConsent() {\n    // Close the age consent modal\n    this.displayAgeConsentModal = false;\n    // Retrieve the user profile from local storage\n    const userProfile = localStorage.getItem(\"profile\") || '';\n    const userId = userProfile ? JSON.parse(userProfile)?.id : null;\n    // Create the AgeConsent object with required session and age restriction data\n    let data = {\n      sessionId: localStorage.getItem('sessionId') || '',\n      MinimumAgeForProductEligibility: this.restrictionAge\n    };\n    // Include the userId in the consent object if available\n    if (userId) {\n      data.userId = userId;\n    }\n    // Extract product and navigation-related information for cart creation\n    const product = this.restrictedProductTobePurchased.product;\n    const productDetails = this.restrictedProductTobePurchased.productDetails;\n    const navigate = this.restrictedProductTobePurchased.navigate;\n    // Send the age consent to the backend and handle the response\n    this.userService.updateAgeConsent(data).subscribe({\n      next: res => {\n        // Successfully updated consent, proceed to create the cart\n        this.createCart(product, productDetails, navigate);\n      },\n      error: err => {\n        // Handle any errors encountered during the backend call\n        this.handleError(err.message);\n      }\n    });\n  }\n  /**\r\n   * Closes the age consent modal and opens the eligibility modal.\r\n   *\r\n   * This method ensures that the `displayAgeConsentModal` is set to `false`\r\n   * to close the age consent modal and `displayEligableModal` is set to `true`\r\n   * to show the eligibility modal to the user.\r\n   */\n  closeConsentModal() {\n    this.displayEligableModal = true; // Open the eligibility modal\n    this.displayAgeConsentModal = false; // Close the age consent modal\n  }\n  /**\r\n   * Closes the eligibility modal.\r\n   *\r\n   * This method sets `displayEligableModal` to `false` to hide the eligibility modal from the user.\r\n   */\n  closeEligableModal() {\n    this.displayEligableModal = false; // Close the eligibility modal\n  }\n  /**\r\n   * Handles errors by displaying an error message to the user.\r\n   *\r\n   * This method uses the `MessageService` to display an error notification with a localized error summary\r\n   * and a detailed message passed as a parameter.\r\n   *\r\n   * @param {string} message - The error message to be displayed to the user.\r\n   */\n  handleError(message) {\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: message // Detailed error message\n    });\n  }\n\n  isBase64(str) {\n    const base64Regex = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/;\n    return base64Regex.test(str);\n  }\n  updateSanitizedDescription(description) {\n    this.sanitizedDescription = this.sanitizer.bypassSecurityTrustHtml(description);\n  }\n  getFilteredProductSpec() {\n    this.filteredProductSpecs = this.product.productSpecs?.filter(spec => spec.name !== 'Gender' && spec.name !== 'Weight' && spec.name !== 'Size' && spec.name !== 'Size 2') || [];\n  }\n  getFilteredVarianceSpec() {\n    this.filteredVarianceSpecs = this.selectedVariant.varianceSpecs?.filter(spec => spec.name !== 'Gender' && spec.name !== 'Weight' && spec.name !== 'Size' && spec.name !== 'Size 2') || [];\n  }\n  shareContent() {\n    return _asyncToGenerator(function* () {\n      const shareData = {\n        url: window.location.href\n      };\n      yield navigator.share(shareData);\n    })();\n  }\n  getImageUrl(imageLink) {\n    return verifyImageURL(imageLink, this._BaseURL);\n  }\n};\n__decorate([Input()], DetailsComponent.prototype, \"product\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"channelId\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"currency\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"selectedVariant\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"selectedSize\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"selectedSize2\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"selectedColor\", void 0);\n__decorate([Input()], DetailsComponent.prototype, \"cols\", void 0);\n__decorate([Output()], DetailsComponent.prototype, \"onItemLike\", void 0);\n__decorate([Output()], DetailsComponent.prototype, \"onChangeVariant\", void 0);\n__decorate([HostListener('window:resize', ['$event'])], DetailsComponent.prototype, \"onResize\", null);\nDetailsComponent = __decorate([Component({\n  selector: 'app-details',\n  templateUrl: './details.component.html',\n  styleUrls: ['./details.component.scss']\n}), __param(21, Inject(PLATFORM_ID))], DetailsComponent);\nexport { DetailsComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}