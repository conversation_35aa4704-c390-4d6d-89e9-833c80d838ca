{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction BackBtnComponent_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 1);\n    i0.ɵɵlistener(\"click\", function BackBtnComponent_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(1, \"img\", 2);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, ctx_r0.backText ? ctx_r0.backText : \"buttons.back\"));\n  }\n}\nexport class BackBtnComponent {\n  _location;\n  router;\n  navigationUrl = '';\n  backText = '';\n  allowedRoutes = ['/login', '/register', '/reset-password', '/reset-password-confirmation', '/reset-password-otp', '/otp', '/update-password', '/update-password-confirmation'];\n  constructor(_location, router) {\n    this._location = _location;\n    this.router = router;\n  }\n  shouldShowComponent() {\n    const currentUrl = this.router.url;\n    return this.allowedRoutes.some(route => currentUrl.includes(route));\n  }\n  goBack() {\n    if (this.navigationUrl) {\n      this.router.navigate([this.navigationUrl]);\n    } else {\n      this._location.back();\n    }\n  }\n  static ɵfac = function BackBtnComponent_Factory(t) {\n    return new (t || BackBtnComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BackBtnComponent,\n    selectors: [[\"app-back-btn\"]],\n    inputs: {\n      navigationUrl: \"navigationUrl\",\n      backText: \"backText\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"back-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"back-btn\", 3, \"click\"], [\"alt\", \"back-icon\", \"src\", \"assets/icons/mobile-icons/back-icon.svg\"]],\n    template: function BackBtnComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, BackBtnComponent_button_0_Template, 5, 3, \"button\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowComponent());\n      }\n    },\n    dependencies: [CommonModule, i1.NgIf, RouterModule, TranslateModule, i3.TranslatePipe],\n    styles: [\".back-btn[_ngcontent-%COMP%] {\\n  background-color: #F6F6F6;\\n  padding: 16px 25px;\\n  position: absolute;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: row;\\n  gap: 7px;\\n  outline: none;\\n  border: none;\\n  align-items: center;\\n  padding-left: 12.5rem;\\n}\\n.back-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 24px;\\n  font-family: var(--medium-font);\\n  color: #2F3036;\\n}\\n@media screen and (max-width: 768px) {\\n  .back-btn[_ngcontent-%COMP%] {\\n    top: 74px;\\n    padding-left: 33px !important;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}