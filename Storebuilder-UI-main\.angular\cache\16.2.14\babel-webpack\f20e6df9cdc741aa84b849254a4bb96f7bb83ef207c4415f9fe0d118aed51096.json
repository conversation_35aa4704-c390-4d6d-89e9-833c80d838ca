{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Zero padding strategy.\n   */\n  CryptoJS.pad.ZeroPadding = {\n    pad: function (data, blockSize) {\n      // Shortcut\n      var blockSizeBytes = blockSize * 4;\n\n      // Pad\n      data.clamp();\n      data.sigBytes += blockSizeBytes - (data.sigBytes % blockSizeBytes || blockSizeBytes);\n    },\n    unpad: function (data) {\n      // Shortcut\n      var dataWords = data.words;\n\n      // Unpad\n      var i = data.sigBytes - 1;\n      for (var i = data.sigBytes - 1; i >= 0; i--) {\n        if (dataWords[i >>> 2] >>> 24 - i % 4 * 8 & 0xff) {\n          data.sigBytes = i + 1;\n          break;\n        }\n      }\n    }\n  };\n  return CryptoJS.pad.ZeroPadding;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "ZeroPadding", "data", "blockSize", "blockSizeBytes", "clamp", "sigBytes", "unpad", "dataWords", "words", "i"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/crypto-js/pad-zeropadding.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Zero padding strategy.\n\t */\n\tCryptoJS.pad.ZeroPadding = {\n\t    pad: function (data, blockSize) {\n\t        // Shortcut\n\t        var blockSizeBytes = blockSize * 4;\n\n\t        // Pad\n\t        data.clamp();\n\t        data.sigBytes += blockSizeBytes - ((data.sigBytes % blockSizeBytes) || blockSizeBytes);\n\t    },\n\n\t    unpad: function (data) {\n\t        // Shortcut\n\t        var dataWords = data.words;\n\n\t        // Unpad\n\t        var i = data.sigBytes - 1;\n\t        for (var i = data.sigBytes - 1; i >= 0; i--) {\n\t            if (((dataWords[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff)) {\n\t                data.sigBytes = i + 1;\n\t                break;\n\t            }\n\t        }\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.ZeroPadding;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAG,CAACC,WAAW,GAAG;IACvBD,GAAG,EAAE,SAAAA,CAAUE,IAAI,EAAEC,SAAS,EAAE;MAC5B;MACA,IAAIC,cAAc,GAAGD,SAAS,GAAG,CAAC;;MAElC;MACAD,IAAI,CAACG,KAAK,CAAC,CAAC;MACZH,IAAI,CAACI,QAAQ,IAAIF,cAAc,IAAKF,IAAI,CAACI,QAAQ,GAAGF,cAAc,IAAKA,cAAc,CAAC;IAC1F,CAAC;IAEDG,KAAK,EAAE,SAAAA,CAAUL,IAAI,EAAE;MACnB;MACA,IAAIM,SAAS,GAAGN,IAAI,CAACO,KAAK;;MAE1B;MACA,IAAIC,CAAC,GAAGR,IAAI,CAACI,QAAQ,GAAG,CAAC;MACzB,KAAK,IAAII,CAAC,GAAGR,IAAI,CAACI,QAAQ,GAAG,CAAC,EAAEI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAMF,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,IAAI,EAAG;UACtDR,IAAI,CAACI,QAAQ,GAAGI,CAAC,GAAG,CAAC;UACrB;QACJ;MACJ;IACJ;EACJ,CAAC;EAGD,OAAOX,QAAQ,CAACC,GAAG,CAACC,WAAW;AAEhC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}