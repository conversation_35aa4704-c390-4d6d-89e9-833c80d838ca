{"ast": null, "code": "import { signal } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class AppDataService {\n  constructor() {\n    this.dialogVisibilitySignal = signal(false);\n  }\n  get dialogVisibility() {\n    return this.dialogVisibilitySignal.asReadonly();\n  }\n  showDialog() {\n    this.dialogVisibilitySignal.set(true);\n  }\n  hideDialog() {\n    this.dialogVisibilitySignal.set(false);\n  }\n  get initialData() {\n    return this._initialData;\n  }\n  set initialData(data) {\n    this._initialData = data;\n  }\n  get configuration() {\n    return this._configuration;\n  }\n  set configuration(data) {\n    this._configuration = data;\n  }\n  get tenants() {\n    return this._tenants;\n  }\n  set tenants(data) {\n    this._tenants = data;\n  }\n  get categories() {\n    return this._categories;\n  }\n  set categories(data) {\n    const sortedCategories = this.sortCategories(data.records);\n    data.records = sortedCategories;\n    this._categories = data;\n  }\n  get showRoomConfiguration() {\n    return this._showRoomConfiguration;\n  }\n  set showRoomConfiguration(data) {\n    this._showRoomConfiguration = data;\n  }\n  get layoutTemplate() {\n    return this._layoutTemplate;\n  }\n  set layoutTemplate(data) {\n    this._layoutTemplate = data;\n  }\n  get shopSettingDsata() {\n    return this._shopSettingData;\n  }\n  set shopSettingData(data) {\n    this._shopSettingData = data;\n  }\n  clearAppData() {\n    localStorage.removeItem('appData_expiry');\n    localStorage.removeItem('appData_cache');\n  }\n  sortCategories(categories) {\n    categories.sort((a, b) => a.order - b.order);\n    categories.forEach(category => {\n      if (category.categories) {\n        category.categories = this.sortCategories(category.categories);\n      }\n    });\n    return categories;\n  }\n  static #_ = this.ɵfac = function AppDataService_Factory(t) {\n    return new (t || AppDataService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AppDataService,\n    factory: AppDataService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["signal", "AppDataService", "constructor", "dialogVisibilitySignal", "dialogVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showDialog", "set", "hideDialog", "initialData", "_initialData", "data", "configuration", "_configuration", "tenants", "_tenants", "categories", "_categories", "sortedCategories", "sortCategories", "records", "showRoomConfiguration", "_showRoomConfiguration", "layoutTemplate", "_layoutTemplate", "shopSettingDsata", "_shopSettingData", "shopSettingData", "clearAppData", "localStorage", "removeItem", "sort", "a", "b", "order", "for<PERSON>ach", "category", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\app-data.service.ts"], "sourcesContent": ["import {Injectable, Signal, WritableSignal, signal} from '@angular/core';\r\nimport {\r\n  Categories,\r\n  ConfigurationResponse,\r\n  InitialData,\r\n  Tenant,\r\n  AllShowroomConf,\r\n  CategoryRecords\r\n} from \"@core/interface\";\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AppDataService {\r\n  private _initialData: InitialData;\r\n  private _showRoomConfiguration: AllShowroomConf;\r\n  private _layoutTemplate: any;\r\n  private _shopSettingData: Categories;\r\n  private _categories: Categories;\r\n  private _tenants: Tenant;\r\n  private _configuration: ConfigurationResponse;\r\n  private dialogVisibilitySignal: WritableSignal<boolean> = signal(false);\r\n\r\n  get dialogVisibility(): Signal<boolean> {\r\n    return this.dialogVisibilitySignal.asReadonly();\r\n  }\r\n\r\n  showDialog() {\r\n    this.dialogVisibilitySignal.set(true);\r\n  }\r\n  hideDialog() {\r\n    this.dialogVisibilitySignal.set(false);\r\n  }\r\n  get initialData(): InitialData {\r\n    return this._initialData\r\n  }\r\n\r\n  set initialData(data: InitialData) {\r\n    this._initialData = data;\r\n  }\r\n\r\n  get configuration(): ConfigurationResponse {\r\n    return this._configuration;\r\n  }\r\n\r\n  set configuration(data: ConfigurationResponse) {\r\n    this._configuration = data;\r\n  }\r\n\r\n  get tenants(): Tenant {\r\n    return this._tenants;\r\n  }\r\n\r\n  set tenants(data: Tenant) {\r\n    this._tenants = data;\r\n  }\r\n\r\n  get categories(): Categories {\r\n    return this._categories;\r\n  }\r\n\r\n  set categories(data: Categories) {\r\n    const sortedCategories = this.sortCategories(data.records);\r\n    data.records = sortedCategories\r\n    this._categories = data;\r\n  }\r\n\r\n  get showRoomConfiguration(): any {\r\n    return this._showRoomConfiguration;\r\n  }\r\n\r\n  set showRoomConfiguration(data: AllShowroomConf) {\r\n    this._showRoomConfiguration = data;\r\n  }\r\n\r\n  get layoutTemplate(): any {\r\n    return this._layoutTemplate;\r\n  }\r\n\r\n  set layoutTemplate(data: any) {\r\n    this._layoutTemplate = data;\r\n  }\r\n\r\n  get shopSettingDsata(): any {\r\n    return this._shopSettingData;\r\n  }\r\n\r\n  set shopSettingData(data: any) {\r\n    this._shopSettingData = data;\r\n  }\r\n\r\n  clearAppData(){\r\n    localStorage.removeItem('appData_expiry');\r\n    localStorage.removeItem('appData_cache');\r\n  }\r\n\r\n  sortCategories(categories: CategoryRecords[]): CategoryRecords[] {\r\n    categories.sort((a: any, b: any) => a.order - b.order);\r\n    categories.forEach((category : any) => {\r\n      if (category.categories) {\r\n        category.categories = this.sortCategories(category.categories);\r\n      }\r\n    });\r\n\r\n    return categories;\r\n  }\r\n}\r\n\r\n"], "mappings": "AAAA,SAA4CA,MAAM,QAAO,eAAe;;AAaxE,OAAM,MAAOC,cAAc;EAH3BC,YAAA;IAWU,KAAAC,sBAAsB,GAA4BH,MAAM,CAAC,KAAK,CAAC;;EAEvE,IAAII,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACD,sBAAsB,CAACE,UAAU,EAAE;EACjD;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACH,sBAAsB,CAACI,GAAG,CAAC,IAAI,CAAC;EACvC;EACAC,UAAUA,CAAA;IACR,IAAI,CAACL,sBAAsB,CAACI,GAAG,CAAC,KAAK,CAAC;EACxC;EACA,IAAIE,WAAWA,CAAA;IACb,OAAO,IAAI,CAACC,YAAY;EAC1B;EAEA,IAAID,WAAWA,CAACE,IAAiB;IAC/B,IAAI,CAACD,YAAY,GAAGC,IAAI;EAC1B;EAEA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACC,cAAc;EAC5B;EAEA,IAAID,aAAaA,CAACD,IAA2B;IAC3C,IAAI,CAACE,cAAc,GAAGF,IAAI;EAC5B;EAEA,IAAIG,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ;EACtB;EAEA,IAAID,OAAOA,CAACH,IAAY;IACtB,IAAI,CAACI,QAAQ,GAAGJ,IAAI;EACtB;EAEA,IAAIK,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACC,WAAW;EACzB;EAEA,IAAID,UAAUA,CAACL,IAAgB;IAC7B,MAAMO,gBAAgB,GAAG,IAAI,CAACC,cAAc,CAACR,IAAI,CAACS,OAAO,CAAC;IAC1DT,IAAI,CAACS,OAAO,GAAGF,gBAAgB;IAC/B,IAAI,CAACD,WAAW,GAAGN,IAAI;EACzB;EAEA,IAAIU,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACC,sBAAsB;EACpC;EAEA,IAAID,qBAAqBA,CAACV,IAAqB;IAC7C,IAAI,CAACW,sBAAsB,GAAGX,IAAI;EACpC;EAEA,IAAIY,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACC,eAAe;EAC7B;EAEA,IAAID,cAAcA,CAACZ,IAAS;IAC1B,IAAI,CAACa,eAAe,GAAGb,IAAI;EAC7B;EAEA,IAAIc,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAC9B;EAEA,IAAIC,eAAeA,CAAChB,IAAS;IAC3B,IAAI,CAACe,gBAAgB,GAAGf,IAAI;EAC9B;EAEAiB,YAAYA,CAAA;IACVC,YAAY,CAACC,UAAU,CAAC,gBAAgB,CAAC;IACzCD,YAAY,CAACC,UAAU,CAAC,eAAe,CAAC;EAC1C;EAEAX,cAAcA,CAACH,UAA6B;IAC1CA,UAAU,CAACe,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC;IACtDlB,UAAU,CAACmB,OAAO,CAAEC,QAAc,IAAI;MACpC,IAAIA,QAAQ,CAACpB,UAAU,EAAE;QACvBoB,QAAQ,CAACpB,UAAU,GAAG,IAAI,CAACG,cAAc,CAACiB,QAAQ,CAACpB,UAAU,CAAC;;IAElE,CAAC,CAAC;IAEF,OAAOA,UAAU;EACnB;EAAC,QAAAqB,CAAA,G;qBA5FUpC,cAAc;EAAA;EAAA,QAAAqC,EAAA,G;WAAdrC,cAAc;IAAAsC,OAAA,EAAdtC,cAAc,CAAAuC,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}