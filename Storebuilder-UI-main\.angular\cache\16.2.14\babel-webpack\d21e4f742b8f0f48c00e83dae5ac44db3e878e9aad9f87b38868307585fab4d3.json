{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function (undefined) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var X32WordArray = C_lib.WordArray;\n\n    /**\n     * x64 namespace.\n     */\n    var C_x64 = C.x64 = {};\n\n    /**\n     * A 64-bit word.\n     */\n    var X64Word = C_x64.Word = Base.extend({\n      /**\n       * Initializes a newly created 64-bit word.\n       *\n       * @param {number} high The high 32 bits.\n       * @param {number} low The low 32 bits.\n       *\n       * @example\n       *\n       *     var x64Word = CryptoJS.x64.Word.create(0x00010203, 0x04050607);\n       */\n      init: function (high, low) {\n        this.high = high;\n        this.low = low;\n      }\n\n      /**\n       * Bitwise NOTs this word.\n       *\n       * @return {X64Word} A new x64-Word object after negating.\n       *\n       * @example\n       *\n       *     var negated = x64Word.not();\n       */\n      // not: function () {\n      // var high = ~this.high;\n      // var low = ~this.low;\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Bitwise ANDs this word with the passed word.\n       *\n       * @param {X64Word} word The x64-Word to AND with this word.\n       *\n       * @return {X64Word} A new x64-Word object after ANDing.\n       *\n       * @example\n       *\n       *     var anded = x64Word.and(anotherX64Word);\n       */\n      // and: function (word) {\n      // var high = this.high & word.high;\n      // var low = this.low & word.low;\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Bitwise ORs this word with the passed word.\n       *\n       * @param {X64Word} word The x64-Word to OR with this word.\n       *\n       * @return {X64Word} A new x64-Word object after ORing.\n       *\n       * @example\n       *\n       *     var ored = x64Word.or(anotherX64Word);\n       */\n      // or: function (word) {\n      // var high = this.high | word.high;\n      // var low = this.low | word.low;\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Bitwise XORs this word with the passed word.\n       *\n       * @param {X64Word} word The x64-Word to XOR with this word.\n       *\n       * @return {X64Word} A new x64-Word object after XORing.\n       *\n       * @example\n       *\n       *     var xored = x64Word.xor(anotherX64Word);\n       */\n      // xor: function (word) {\n      // var high = this.high ^ word.high;\n      // var low = this.low ^ word.low;\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Shifts this word n bits to the left.\n       *\n       * @param {number} n The number of bits to shift.\n       *\n       * @return {X64Word} A new x64-Word object after shifting.\n       *\n       * @example\n       *\n       *     var shifted = x64Word.shiftL(25);\n       */\n      // shiftL: function (n) {\n      // if (n < 32) {\n      // var high = (this.high << n) | (this.low >>> (32 - n));\n      // var low = this.low << n;\n      // } else {\n      // var high = this.low << (n - 32);\n      // var low = 0;\n      // }\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Shifts this word n bits to the right.\n       *\n       * @param {number} n The number of bits to shift.\n       *\n       * @return {X64Word} A new x64-Word object after shifting.\n       *\n       * @example\n       *\n       *     var shifted = x64Word.shiftR(7);\n       */\n      // shiftR: function (n) {\n      // if (n < 32) {\n      // var low = (this.low >>> n) | (this.high << (32 - n));\n      // var high = this.high >>> n;\n      // } else {\n      // var low = this.high >>> (n - 32);\n      // var high = 0;\n      // }\n\n      // return X64Word.create(high, low);\n      // },\n\n      /**\n       * Rotates this word n bits to the left.\n       *\n       * @param {number} n The number of bits to rotate.\n       *\n       * @return {X64Word} A new x64-Word object after rotating.\n       *\n       * @example\n       *\n       *     var rotated = x64Word.rotL(25);\n       */\n      // rotL: function (n) {\n      // return this.shiftL(n).or(this.shiftR(64 - n));\n      // },\n\n      /**\n       * Rotates this word n bits to the right.\n       *\n       * @param {number} n The number of bits to rotate.\n       *\n       * @return {X64Word} A new x64-Word object after rotating.\n       *\n       * @example\n       *\n       *     var rotated = x64Word.rotR(7);\n       */\n      // rotR: function (n) {\n      // return this.shiftR(n).or(this.shiftL(64 - n));\n      // },\n\n      /**\n       * Adds this word with the passed word.\n       *\n       * @param {X64Word} word The x64-Word to add with this word.\n       *\n       * @return {X64Word} A new x64-Word object after adding.\n       *\n       * @example\n       *\n       *     var added = x64Word.add(anotherX64Word);\n       */\n      // add: function (word) {\n      // var low = (this.low + word.low) | 0;\n      // var carry = (low >>> 0) < (this.low >>> 0) ? 1 : 0;\n      // var high = (this.high + word.high + carry) | 0;\n\n      // return X64Word.create(high, low);\n      // }\n    });\n\n    /**\n     * An array of 64-bit words.\n     *\n     * @property {Array} words The array of CryptoJS.x64.Word objects.\n     * @property {number} sigBytes The number of significant bytes in this word array.\n     */\n    var X64WordArray = C_x64.WordArray = Base.extend({\n      /**\n       * Initializes a newly created word array.\n       *\n       * @param {Array} words (Optional) An array of CryptoJS.x64.Word objects.\n       * @param {number} sigBytes (Optional) The number of significant bytes in the words.\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.x64.WordArray.create();\n       *\n       *     var wordArray = CryptoJS.x64.WordArray.create([\n       *         CryptoJS.x64.Word.create(0x00010203, 0x04050607),\n       *         CryptoJS.x64.Word.create(0x18191a1b, 0x1c1d1e1f)\n       *     ]);\n       *\n       *     var wordArray = CryptoJS.x64.WordArray.create([\n       *         CryptoJS.x64.Word.create(0x00010203, 0x04050607),\n       *         CryptoJS.x64.Word.create(0x18191a1b, 0x1c1d1e1f)\n       *     ], 10);\n       */\n      init: function (words, sigBytes) {\n        words = this.words = words || [];\n        if (sigBytes != undefined) {\n          this.sigBytes = sigBytes;\n        } else {\n          this.sigBytes = words.length * 8;\n        }\n      },\n      /**\n       * Converts this 64-bit word array to a 32-bit word array.\n       *\n       * @return {CryptoJS.lib.WordArray} This word array's data as a 32-bit word array.\n       *\n       * @example\n       *\n       *     var x32WordArray = x64WordArray.toX32();\n       */\n      toX32: function () {\n        // Shortcuts\n        var x64Words = this.words;\n        var x64WordsLength = x64Words.length;\n\n        // Convert\n        var x32Words = [];\n        for (var i = 0; i < x64WordsLength; i++) {\n          var x64Word = x64Words[i];\n          x32Words.push(x64Word.high);\n          x32Words.push(x64Word.low);\n        }\n        return X32WordArray.create(x32Words, this.sigBytes);\n      },\n      /**\n       * Creates a copy of this word array.\n       *\n       * @return {X64WordArray} The clone.\n       *\n       * @example\n       *\n       *     var clone = x64WordArray.clone();\n       */\n      clone: function () {\n        var clone = Base.clone.call(this);\n\n        // Clone \"words\" array\n        var words = clone.words = this.words.slice(0);\n\n        // Clone each X64Word object\n        var wordsLength = words.length;\n        for (var i = 0; i < wordsLength; i++) {\n          words[i] = words[i].clone();\n        }\n        return clone;\n      }\n    });\n  })();\n  return CryptoJS;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}