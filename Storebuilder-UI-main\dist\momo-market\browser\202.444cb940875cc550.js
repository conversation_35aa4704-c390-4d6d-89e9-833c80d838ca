"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[202],{9202:(Mt,b,l)=>{l.r(b),l.d(b,{RegisterDetailsComponent:()=>ot});var M=l(5861),e=l(5879),m=l(6814),w=l(6075),u=l(6663),v=l(707),y=l(1312),P=l(8057),c=l(6223),N=l(5118),T=l(1423),x=l(3714),L=l(9397),U=l(1374),_=l(7152),O=l(5662),k=l(3918),J=l(7625),Q=l(9566);const I=function(n){return{color:n}},Z=function(){return{standalone:!0}};let Y=(()=>{class n{fb;labelColor="white";floatingLabel=!1;firstNameChange=new e.vpe;lastNameChange=new e.vpe;validationChange=new e.vpe;blur=new e.vpe;firstName="";lastName="";firstNameFlag=!1;nameForm;constructor(t){this.fb=t,this.nameForm=this.fb.group({firstName:["",c.kI.required],lastName:["",c.kI.required]})}ngOnInit(){this.nameForm.valueChanges.subscribe(()=>{this.validationChange.emit(this.nameForm.valid)})}onFirstNameChange(t){this.nameForm.patchValue({firstName:t}),this.firstNameChange.emit(t)}onLastNameChange(t){this.nameForm.patchValue({lastName:t}),this.lastNameChange.emit(t)}onBlur(){this.blur.emit(),this.isRequired("firstName")}isRequired(t){this.nameForm.controls[t]?.errors?.required&&(this.firstNameFlag=!0)}onInputChange(t,a){if(t.target.value){const i=t.target.value.indexOf(" ");0==i&&(t.target.value=t.target.value.substring(0,i)+t.target.value.substring(i+1))}"firstName"===a?this.onFirstNameChange(t.target.value):this.onLastNameChange(t.target.value)}static \u0275fac=function(a){return new(a||n)(e.Y36(c.qu))};static \u0275cmp=e.Xpm({type:n,selectors:[["app-name-input"]],inputs:{labelColor:"labelColor",floatingLabel:"floatingLabel"},outputs:{firstNameChange:"firstNameChange",lastNameChange:"lastNameChange",validationChange:"validationChange",blur:"blur"},standalone:!0,features:[e.jDz],decls:13,vars:18,consts:[[1,"name-container"],["for","float-input",3,"ngStyle"],["id","float-input","name","firstName","pInputText","","type","text",3,"ngModel","ngModelOptions","click","input","ngModelChange"],["id","float-input","name","lastName","pInputText","","type","text",3,"ngModel","ngModelOptions","click","input","ngModelChange"]],template:function(a,i){if(1&a&&(e.TgZ(0,"div",0)(1,"div")(2,"span")(3,"label",1),e._uU(4),e.ALo(5,"translate"),e.qZA(),e.TgZ(6,"input",2),e.NdJ("click",function(){return i.onBlur()})("input",function(r){return i.onInputChange(r,"firstName")})("ngModelChange",function(r){return i.firstName=r})("ngModelChange",function(r){return i.onFirstNameChange(r)}),e.qZA()()(),e.TgZ(7,"div")(8,"span")(9,"label",1),e._uU(10),e.ALo(11,"translate"),e.qZA(),e.TgZ(12,"input",3),e.NdJ("click",function(){return i.onBlur()})("input",function(r){return i.onInputChange(r,"lastName")})("ngModelChange",function(r){return i.lastName=r})("ngModelChange",function(r){return i.onLastNameChange(r)}),e.qZA()()()()),2&a){let o,r;e.xp6(3),e.Q6J("ngStyle",e.VKq(12,I,i.firstNameFlag&&0===(null==(o=i.nameForm.get("firstName"))?null:o.value.length)?"red":i.labelColor)),e.xp6(1),e.hij("",e.lcZ(5,8,"auth.registerPassword.firstName")," *"),e.xp6(2),e.Q6J("ngModel",i.firstName)("ngModelOptions",e.DdM(14,Z)),e.xp6(3),e.Q6J("ngStyle",e.VKq(15,I,i.firstNameFlag&&0===(null==(r=i.nameForm.get("lastName"))?null:r.value.length)?"red":i.labelColor)),e.xp6(1),e.hij("",e.lcZ(11,10,"auth.registerPassword.lastName")," *"),e.xp6(2),e.Q6J("ngModel",i.lastName)("ngModelOptions",e.DdM(17,Z))}},dependencies:[m.ez,m.PC,c.u5,c.Fj,c.JJ,c.On,c.UX,u.aw,u.X$,x.j,x.o],styles:[".name-container[_ngcontent-%COMP%]{display:flex;width:100%;gap:8px}.name-container[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{flex:1}span[_ngcontent-%COMP%]{display:block}label[_ngcontent-%COMP%]{color:#323232!important;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}input#float-input[_ngcontent-%COMP%]{height:50px!important;width:100%;border-radius:2px;opacity:1;border:none!important;border:1px solid #E4E7E9!important;padding-left:10px;padding-right:10px;font-size:14px;font-weight:400;font-family:var(--regular-font)!important}@media screen and (max-width: 768px){.name-container[_ngcontent-%COMP%]{gap:8px}}"]})}return n})();var z=l(5334),E=l(1064),D=l(7680),V=l(6726),B=l(7134),F=l(7874),q=l(8145),C=l(5219),p=l(864),G=l(6593),K=l(9147),X=l(459);function H(n,s){1&n&&(e.TgZ(0,"div",7),e._UZ(1,"p-progressSpinner"),e.qZA())}function j(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"app-phone-input",46),e.NdJ("phoneNumberChange",function(i){e.CHM(t);const o=e.oxw(2);return e.KtG(o.onPhoneNumberChange(i))})("validationChange",function(i){e.CHM(t);const o=e.oxw(2);return e.KtG(o.onPhoneValidationChange(i))}),e.qZA()}if(2&n){const t=e.oxw(2);e.Q6J("maxLength",20)("selectedCountryISO",t.CustomCountryISO)("preferredCountries",t.preferredCountries)("placeholder",t.customPlaceHolder)}}function $(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"app-email-input",47),e.NdJ("emailChange",function(i){e.CHM(t);const o=e.oxw(2);return e.KtG(o.onEmailChange(i))})("validationChange",function(i){e.CHM(t);const o=e.oxw(2);return e.KtG(o.onEmailValidationChange(i))})("blur",function(){e.CHM(t);const i=e.oxw(2);return e.KtG(i.onBlur())}),e.ALo(1,"translate"),e.qZA()}if(2&n){const t=e.oxw(2);e.Q6J("emailRequired","false")("floatingLabel",t.floatingLabel)("labelColor",t.labelColor)("emailLabel",t.floatingLabel?"auth.registerPassword.email":e.lcZ(1,4,"auth.registerPassword.emailLabel"))}}function W(n,s){1&n&&(e.TgZ(0,"p",48)(1,"small",49),e._uU(2),e.ALo(3,"translate"),e.qZA()()),2&n&&(e.xp6(2),e.Oqu(e.lcZ(3,1,"auth.registerPassword.referralByErrors")))}function ee(n,s){1&n&&(e.TgZ(0,"label"),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n&&(e.xp6(1),e.hij("",e.lcZ(2,1,"auth.registerPassword.confirmPassword")," *"))}function te(n,s){1&n&&e._UZ(0,"em",53)}function ne(n,s){1&n&&e._UZ(0,"em",54)}function ie(n,s){1&n&&e._UZ(0,"em",55)}function ae(n,s){if(1&n&&(e.ynx(0),e.YNc(1,te,1,0,"em",50),e.YNc(2,ne,1,0,"em",51),e.YNc(3,ie,1,0,"em",52),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!0===t.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!1===t.hasMinimum8Chars)}}function oe(n,s){1&n&&e._UZ(0,"img",59)}function se(n,s){1&n&&e._UZ(0,"img",60)}function re(n,s){1&n&&e._UZ(0,"img",61)}function le(n,s){if(1&n&&(e.ynx(0),e.YNc(1,oe,1,0,"img",56),e.YNc(2,se,1,0,"img",57),e.YNc(3,re,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!0===t.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!1===t.hasMinimum8Chars)}}function ce(n,s){1&n&&e._UZ(0,"em",53)}function ge(n,s){1&n&&e._UZ(0,"em",54)}function pe(n,s){1&n&&e._UZ(0,"em",55)}function me(n,s){if(1&n&&(e.ynx(0),e.YNc(1,ce,1,0,"em",50),e.YNc(2,ge,1,0,"em",51),e.YNc(3,pe,1,0,"em",52),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!0===t.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!1===t.hasLowerChar)}}function de(n,s){1&n&&e._UZ(0,"img",59)}function he(n,s){1&n&&e._UZ(0,"img",60)}function _e(n,s){1&n&&e._UZ(0,"img",61)}function ue(n,s){if(1&n&&(e.ynx(0),e.YNc(1,de,1,0,"img",56),e.YNc(2,he,1,0,"img",57),e.YNc(3,_e,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!0===t.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!1===t.hasLowerChar)}}function fe(n,s){1&n&&e._UZ(0,"em",53)}function xe(n,s){1&n&&e._UZ(0,"em",54)}function Ce(n,s){1&n&&e._UZ(0,"em",55)}function be(n,s){if(1&n&&(e.ynx(0),e.YNc(1,fe,1,0,"em",50),e.YNc(2,xe,1,0,"em",51),e.YNc(3,Ce,1,0,"em",52),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!0===t.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!1===t.hasUpperChar)}}function Me(n,s){1&n&&e._UZ(0,"img",59)}function we(n,s){1&n&&e._UZ(0,"img",60)}function ve(n,s){1&n&&e._UZ(0,"img",61)}function ye(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Me,1,0,"img",56),e.YNc(2,we,1,0,"img",57),e.YNc(3,ve,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!0===t.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!1===t.hasUpperChar)}}function Pe(n,s){1&n&&e._UZ(0,"em",53)}function Ne(n,s){1&n&&e._UZ(0,"em",54)}function Te(n,s){1&n&&e._UZ(0,"em",55)}function Oe(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Pe,1,0,"em",50),e.YNc(2,Ne,1,0,"em",51),e.YNc(3,Te,1,0,"em",52),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!0===t.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!1===t.hasAtleastOneNumber)}}function Ie(n,s){1&n&&e._UZ(0,"img",59)}function Ze(n,s){1&n&&e._UZ(0,"img",60)}function De(n,s){1&n&&e._UZ(0,"img",61)}function Se(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Ie,1,0,"img",56),e.YNc(2,Ze,1,0,"img",57),e.YNc(3,De,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!0===t.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!1===t.hasAtleastOneNumber)}}function Ae(n,s){1&n&&e._UZ(0,"em",53)}function Re(n,s){1&n&&e._UZ(0,"em",54)}function Le(n,s){1&n&&e._UZ(0,"em",55)}function Ue(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Ae,1,0,"em",50),e.YNc(2,Re,1,0,"em",51),e.YNc(3,Le,1,0,"em",52),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!0===t.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!1===t.hasSpecialChars)}}function ke(n,s){1&n&&e._UZ(0,"img",59)}function Je(n,s){1&n&&e._UZ(0,"img",60)}function Qe(n,s){1&n&&e._UZ(0,"img",61)}function Ye(n,s){if(1&n&&(e.ynx(0),e.YNc(1,ke,1,0,"img",56),e.YNc(2,Je,1,0,"img",57),e.YNc(3,Qe,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!0===t.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!1===t.hasSpecialChars)}}function ze(n,s){1&n&&e._UZ(0,"em",53)}function Ee(n,s){1&n&&e._UZ(0,"em",55)}function Ve(n,s){1&n&&e._UZ(0,"em",54)}function Be(n,s){if(1&n&&(e.ynx(0),e.YNc(1,ze,1,0,"em",50),e.YNc(2,Ee,1,0,"em",52),e.YNc(3,Ve,1,0,"em",51),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.passwordMatched),e.xp6(1),e.Q6J("ngIf",!1===t.passwordMatched),e.xp6(1),e.Q6J("ngIf",!0===t.passwordMatched)}}function Fe(n,s){1&n&&e._UZ(0,"img",59)}function qe(n,s){1&n&&e._UZ(0,"img",60)}function Ge(n,s){1&n&&e._UZ(0,"img",61)}function Ke(n,s){if(1&n&&(e.ynx(0),e.YNc(1,Fe,1,0,"img",56),e.YNc(2,qe,1,0,"img",57),e.YNc(3,Ge,1,0,"img",58),e.BQk()),2&n){const t=e.oxw(2);e.xp6(1),e.Q6J("ngIf",void 0===t.passwordMatched),e.xp6(1),e.Q6J("ngIf",!0===t.passwordMatched),e.xp6(1),e.Q6J("ngIf",!1===t.passwordMatched)}}const Xe=function(n){return{"mobile-content-container":n}},S=function(n){return{"p-float-label mt-3":n}},d=function(n){return{color:n}},A=function(){return{standalone:!0}};function He(n,s){if(1&n){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",8)(2,"div",9)(3,"div",10),e._UZ(4,"img",11),e.qZA(),e.TgZ(5,"div",12)(6,"div",13),e._UZ(7,"img",14),e.TgZ(8,"div",15)(9,"h2",16),e._uU(10),e.ALo(11,"translate"),e.qZA(),e.TgZ(12,"span",17),e._uU(13),e.ALo(14,"translate"),e.qZA()()(),e.TgZ(15,"div",18)(16,"p",19),e._uU(17),e.ALo(18,"translate"),e.qZA(),e.TgZ(19,"div",20),e._uU(20),e.ALo(21,"translate"),e.qZA()(),e.TgZ(22,"div",21)(23,"form",22)(24,"app-name-input",23),e.NdJ("firstNameChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onFirstNameChange(i))})("lastNameChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onLastNameChange(i))})("validationChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onNameValidationChange(i))})("blur",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBlur())}),e.qZA(),e.YNc(25,j,1,4,"app-phone-input",24),e.YNc(26,$,2,6,"app-email-input",25),e.TgZ(27,"div",26)(28,"span",27)(29,"label",28),e._uU(30),e.ALo(31,"translate"),e.qZA(),e.TgZ(32,"input",29),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBlur())})("input",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onReferralChange(i))})("ngModelChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.referralBy=i)}),e.qZA()(),e.YNc(33,W,4,3,"p",30),e.qZA(),e.TgZ(34,"app-password-input",31),e.NdJ("passwordChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onPasswordChange(i))})("validationChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onPasswordValidationChange(i))}),e.qZA(),e.TgZ(35,"div",26)(36,"span",27)(37,"label",32),e._uU(38),e.ALo(39,"translate"),e.qZA(),e.TgZ(40,"p-password",33),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.onBlur())})("ngModelChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.confirmPassword=i)})("ngModelChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.onConfirmPasswordChange(i))}),e.qZA(),e.YNc(41,ee,3,3,"label",2),e.qZA()(),e.TgZ(42,"div",26)(43,"ul",34)(44,"li",35),e.YNc(45,ae,4,3,"ng-container",2),e.YNc(46,le,4,3,"ng-container",2),e.TgZ(47,"span",36),e._uU(48),e.ALo(49,"translate"),e.qZA()(),e.TgZ(50,"li",35),e.YNc(51,me,4,3,"ng-container",2),e.YNc(52,ue,4,3,"ng-container",2),e.TgZ(53,"span",36),e._uU(54),e.ALo(55,"translate"),e.qZA()(),e.TgZ(56,"li",35),e.YNc(57,be,4,3,"ng-container",2),e.YNc(58,ye,4,3,"ng-container",2),e.TgZ(59,"span",36),e._uU(60),e.ALo(61,"translate"),e.qZA()(),e.TgZ(62,"li",35),e.YNc(63,Oe,4,3,"ng-container",2),e.YNc(64,Se,4,3,"ng-container",2),e.TgZ(65,"span",36),e._uU(66),e.ALo(67,"translate"),e.qZA()(),e.TgZ(68,"li",35),e.YNc(69,Ue,4,3,"ng-container",2),e.YNc(70,Ye,4,3,"ng-container",2),e.TgZ(71,"span",36),e._uU(72),e.ALo(73,"translate"),e.qZA()(),e.TgZ(74,"li",37)(75,"p"),e.YNc(76,Be,4,3,"ng-container",2),e.YNc(77,Ke,4,3,"ng-container",2),e.TgZ(78,"span",36),e._uU(79),e.ALo(80,"translate"),e.qZA()()()()()(),e.TgZ(81,"button",38),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkSubmissionFlow(i.floatingLabel))}),e.ALo(82,"translate"),e.qZA(),e.TgZ(83,"div",39)(84,"p-checkbox",40),e.NdJ("ngModelChange",function(i){e.CHM(t);const o=e.oxw();return e.KtG(o.isChecked=i)}),e.qZA(),e.TgZ(85,"span"),e._uU(86),e.ALo(87,"translate"),e.qZA()(),e.TgZ(88,"p",41),e._uU(89),e.ALo(90,"translate"),e.TgZ(91,"a",42),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.termsAndConditionsModal())}),e._uU(92),e.ALo(93,"translate"),e.qZA(),e._uU(94),e.ALo(95,"translate"),e.TgZ(96,"a",42),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.reloadCurrentPage(3,"Privacy policy"))}),e._uU(97),e.ALo(98,"translate"),e.qZA(),e._uU(99),e.ALo(100,"translate"),e.qZA(),e.TgZ(101,"div",43)(102,"div",44),e._uU(103),e.ALo(104,"translate"),e.TgZ(105,"span",45),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.termsAndConditionsModal())}),e._uU(106),e.ALo(107,"translate"),e.qZA()()()()()()(),e.BQk()}if(2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngClass",e.VKq(108,Xe,t.isMobileLayout&&t.screenWidth<=767)),e.xp6(9),e.hij(" ",e.lcZ(11,66,"auth.registerPassword.title")," "),e.xp6(3),e.Oqu(e.lcZ(14,68,"auth.registerPassword.desc")),e.xp6(4),e.hij(" ",e.lcZ(18,70,"auth.registerPassword.title")," "),e.xp6(3),e.hij(" ",e.lcZ(21,72,"auth.registerPassword.subTitle")," "),e.xp6(4),e.Q6J("labelColor",t.labelColor)("floatingLabel",t.floatingLabel),e.xp6(1),e.Q6J("ngIf","email"===t.selectedRegistrationMethod),e.xp6(1),e.Q6J("ngIf","phone"===t.selectedRegistrationMethod),e.xp6(2),e.Q6J("ngClass",e.VKq(110,S,t.floatingLabel)),e.xp6(1),e.Q6J("ngStyle",e.VKq(112,d,t.referralBy.length>50?"red":t.labelColor))("for",t.floatingLabel?"custom-float-input":"float-input"),e.xp6(1),e.AsE(" ",e.lcZ(31,74,"auth.registerPassword.referralBy")," ",""," "),e.xp6(2),e.Q6J("ngModel",t.referralBy)("ngModelOptions",e.DdM(114,A))("id",t.floatingLabel?"custom-float-input":"float-input"),e.xp6(1),e.Q6J("ngIf",t.referralBy.length>50),e.xp6(1),e.Q6J("showForgotPassword",!1)("floatingLabel",t.floatingLabel),e.xp6(2),e.Q6J("ngClass",e.VKq(115,S,t.floatingLabel)),e.xp6(1),e.Q6J("ngStyle",e.VKq(117,d,t.firstNameFlag&&0===t.confirmPassword.length||t.password.length>0&&t.confirmPassword.length>0&&t.password!==t.confirmPassword?"red":t.labelColor)),e.xp6(1),e.AsE(" ",e.lcZ(39,76,"auth.registerPassword.confirmPassword")," ","*"," "),e.xp6(2),e.Q6J("ngModel",t.confirmPassword)("ngModelOptions",e.DdM(119,A))("feedback",!1)("toggleMask",!0)("id",t.floatingLabel?"custom-password-input":""),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(4),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(120,d,!0===t.hasMinimum8Chars?"#01B467":!1===t.hasMinimum8Chars?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(49,78,"auth.registerPassword.validation1")," "),e.xp6(3),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(122,d,!0===t.hasLowerChar?"#01B467":!1===t.hasLowerChar?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(55,80,"auth.registerPassword.validation2")," "),e.xp6(3),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(124,d,!0===t.hasUpperChar?"#01B467":!1===t.hasUpperChar?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(61,82,"auth.registerPassword.validation3")," "),e.xp6(3),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(126,d,!0===t.hasAtleastOneNumber?"#01B467":!1===t.hasAtleastOneNumber?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(67,84,"auth.registerPassword.validation4")," "),e.xp6(3),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(128,d,!0===t.hasSpecialChars?"#01B467":!1===t.hasSpecialChars?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(73,86,"auth.registerPassword.validation5")," "),e.xp6(4),e.Q6J("ngIf",!t.floatingLabel),e.xp6(1),e.Q6J("ngIf",t.floatingLabel),e.xp6(1),e.Q6J("ngStyle",e.VKq(130,d,!0===t.passwordMatched?"#01B467":!1===t.passwordMatched?"red":"grey")),e.xp6(1),e.hij(" ",e.lcZ(80,88,"auth.registerPassword.validation7")," "),e.xp6(2),e.Q6J("disabled",t.checkValidity())("label",e.lcZ(82,90,"auth.registerPassword.signup")),e.xp6(3),e.Q6J("ngModel",t.isChecked)("binary",!0),e.xp6(2),e.Oqu(e.lcZ(87,92,"auth.optInCheckBox")),e.xp6(3),e.hij(" ",e.lcZ(90,94,"signIn.AgreeTermsOne")," "),e.xp6(3),e.Oqu(e.lcZ(93,96,"signIn.AgreeTermsTwo")),e.xp6(2),e.hij(" \xa0",e.lcZ(95,98,"signIn.AgreeTermsThree")," "),e.xp6(3),e.Oqu(e.lcZ(98,100,"signIn.AgreeTermsFour")),e.xp6(2),e.hij(" \xa0",e.lcZ(100,102,"signIn.AgreeTermsFive"),". "),e.xp6(4),e.hij(" ",e.lcZ(104,104,"auth.registerPassword.agreementText")," "),e.xp6(3),e.hij(" ",e.lcZ(107,106,"auth.registerPassword.termsAndConds")," ")}}function je(n,s){1&n&&(e.TgZ(0,"div",62),e._UZ(1,"em",63),e.qZA(),e.TgZ(2,"p",64),e._uU(3),e.ALo(4,"translate"),e.qZA()),2&n&&(e.xp6(3),e.hij(" ",e.lcZ(4,1,"auth.registerPassword.continue")," "))}function $e(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"div",65)(1,"button",66),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.checkSubmissionFlow(i.floatingLabel))}),e.qZA()()}}function We(n,s){1&n&&(e.TgZ(0,"h1",71),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&n&&(e.xp6(1),e.hij(" ",e.lcZ(2,1,"auth.registerPassword.termsAndConditions")," "))}function et(n,s){if(1&n&&(e.TgZ(0,"p",72),e._uU(1),e.qZA()),2&n){const t=e.oxw(2);e.Q6J("innerHtml",t.termsAndConditions,e.oJD),e.xp6(1),e.hij(" ",t.termsAndConditions," ")}}function tt(n,s){if(1&n&&(e.TgZ(0,"div",67),e.YNc(1,We,3,3,"h1",68),e.TgZ(2,"div",69),e.YNc(3,et,2,2,"p",70),e.qZA()()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",t.aboutUsDetails.title),e.xp6(2),e.Q6J("ngIf",t.aboutUsDetails.content)}}function nt(n,s){if(1&n){const t=e.EpF();e.TgZ(0,"button",73),e.NdJ("click",function(){e.CHM(t);const i=e.oxw();return e.KtG(i.closeTermsModal())}),e.ALo(1,"translate"),e.qZA()}2&n&&e.Q6J("label",e.lcZ(1,1,"buttons.close"))}const it=function(){return{width:"40vw"}},R=function(){return{"960px":"75vw","640px":"100vw"}},at=function(){return{width:"30vw"}};let ot=(()=>{class n{messageService;el;translate;router;otpService;aboutUsService;domSanitizer;config;appDataService;$gaService;permissionService;document;platformId;userService;dialogService;$gtmService;auth;store;mainDataService;cookieService;authTokenService;cartService;customGAService;firstName="";lastName="";email=null;password="";confirmPassword="";phoneNumber=null;referralBy="";isNameValid=!1;isEmailValid=!1;isPasswordValid=!1;isPhoneValid=!1;termsConditions=!1;firstNameFlag=!1;submitted=!1;hasUpperChar;hasAtleastOneNumber;hasLowerChar;passwordMatched=void 0;hasMinimum8Chars;hasSpecialChars;passwordIsValid=!1;emailReuiredValidation=null;phoneInputLength=12;customPlaceHolder="";CustomCountryISO;preferredCountries=[_.HT.Uganda,_.HT.Ghana,_.HT.C\u00f4teDIvoire,_.HT.Albania,_.HT.Egypt,_.HT.UnitedArabEmirates];selectedRegistrationMethod="phone";displayApprovedModal=!1;displayTermsAndConditions=!1;loading=!1;isMobileLayout=!1;screenWidth=window.innerWidth;isGoogleAnalytics=!1;isChecked=!1;mobileNumberOrEmail="";otpCode="";aboutUsDetails={};termsAndConditions;pageId=5;title="";tagName=O.Ir;ref;phoneNumberMask="000-000-000-000";mask="999-999-999-999";redirctURL;cartListCount=0;cartListData=[];products=[];constructor(t,a,i,o,r,g,h,f,st,rt,lt,ct,gt,pt,mt,dt,ht,_t,ut,ft,xt,Ct,bt){this.messageService=t,this.el=a,this.translate=i,this.router=o,this.otpService=r,this.aboutUsService=g,this.domSanitizer=h,this.config=f,this.appDataService=st,this.$gaService=rt,this.permissionService=lt,this.document=ct,this.platformId=gt,this.userService=pt,this.dialogService=mt,this.$gtmService=dt,this.auth=ht,this.store=_t,this.mainDataService=ut,this.cookieService=ft,this.authTokenService=xt,this.cartService=Ct,this.customGAService=bt,this.setupCustomPlaceholder(),console.log("RegisterDetailsComponent initialized")}ngOnInit(){if(this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.$gtmService.pushPageView("signUp","step 2"),this.selectedRegistrationMethod=localStorage.getItem("registrationMethod"),this.CustomCountryISO=localStorage.getItem("isoCode"),this.emailReuiredValidation=localStorage.getItem("emailRequired"),this.appDataService.configuration){const o=this.appDataService.configuration.records.find(g=>"EmailRequired"===g.key);o&&(this.emailReuiredValidation=o.value);const r=this.appDataService.configuration.records.find(g=>"PhoneLength"===g.key);r&&(this.phoneInputLength=parseInt(r.value))}let t=localStorage.getItem("PhoneNumberMask");t&&(this.phoneNumberMask=t);let a=localStorage.getItem("countryPhone"),i="256";a&&(i=a?.replace("+","")),this.getAboutUsDetails(),this.mobileNumberOrEmail=history.state.mobile,history?.state?.mobile&&(history?.state?.mobile.slice(i?.length),this.phoneNumber={e164Number:"+"+history.state.mobile}),this.otpCode=history.state.otp}setupCustomPlaceholder(){let t=localStorage.getItem("tenantId");t&&""!==t&&(this.customPlaceHolder={1:"XXXXXXXXX",2:"XXXXXXXXX",3:"XXXXXXXXX",4:"XXXXXXXXXX"}[t]||"")}onFirstNameChange(t){this.firstName=t,this.checkFormValidity()}onLastNameChange(t){this.lastName=t,this.checkFormValidity()}onNameValidationChange(t){this.isNameValid=t,this.checkFormValidity()}onEmailChange(t){this.email=t,this.checkFormValidity()}onEmailValidationChange(t){this.isEmailValid=t,this.checkFormValidity()}onPasswordChange(t){this.password=t,this.checkPasswordPattern(t)}onPasswordValidationChange(t){this.checkFormValidity()}onConfirmPasswordChange(t){this.confirmPassword=t,this.onChangeConfirmPassword()}onPhoneNumberChange(t){this.phoneNumber=t,this.checkFormValidity()}onPhoneValidationChange(t){this.isPhoneValid=t,this.checkFormValidity()}onBlur(){this.isRequired()}onReferralChange(t){if(this.referralBy=t.target.value,t.target.value){const a=t.target.value.indexOf(" ");0==a&&(t.target.value=t.target.value.substring(0,a)+t.target.value.substring(a+1),this.referralBy=t.target.value)}}checkPasswordPattern(t){return this.hasAtleastOneNumber=/\d+/.test(t),this.hasUpperChar=/[A-Z]+/.test(t),this.hasLowerChar=/[a-z]+/.test(t),this.hasMinimum8Chars=/.{10,}/.test(t),this.hasSpecialChars=/[!@#$%^&*()\-_=+\[\]{};:,.\?\/]/.test(t),this.passwordMatched=this.password&&this.confirmPassword&&this.password.length>0&&this.confirmPassword.length>0?this.password===this.confirmPassword:void 0,this.passwordIsValid=this.hasMinimum8Chars&&this.hasUpperChar&&this.hasLowerChar&&this.hasSpecialChars&&!0===this.passwordMatched&&this.hasAtleastOneNumber,this.checkFormValidity(),this.passwordIsValid}onChangeConfirmPassword(){this.passwordMatched=this.password&&this.confirmPassword&&this.password.length>0&&this.confirmPassword.length>0?this.password===this.confirmPassword:void 0,this.passwordIsValid=this.hasMinimum8Chars&&this.hasUpperChar&&this.hasLowerChar&&this.hasSpecialChars&&!0===this.passwordMatched&&this.hasAtleastOneNumber,this.checkFormValidity()}checkFormValidity(){this.submitted=this.firstName.length>0&&this.lastName.length>0&&(this.isPhoneValid&&"email"===this.selectedRegistrationMethod&&this.phoneNumber.number.length>3||"phone"===this.selectedRegistrationMethod)&&this.passwordIsValid&&this.referralBy.length<=50}checkValidity(){return!(this.submitted&&this.passwordIsValid)}isRequired(){this.firstNameFlag=!0}checkSubmissionFlow(t){"phone"===this.selectedRegistrationMethod?this.submitPhoneResistration(t):this.submitEmailResistration()}fireRegistrationEvent(){this.isGoogleAnalytics&&this.$gaService.event(Q.s.CLICK_ON_COMPLETE_REGISTERATION,"","SIGN_UP_STEP_2 ",1,!0)}fireRegistrationSuccessEvent(t){this.isGoogleAnalytics&&this.permissionService.getTagFeature("SIGN_UP")&&this.$gaService.event(this.tagName.SIGN_UP,"","",1,!0,{user_ID:t})}getRegisterUserByEmailDTO(t){const a=this.phoneNumber,i=this.password,r=(a.e164Number?.slice(1),`${this.firstName} ${this.lastName}`),h=localStorage.getItem("CountryPhone")?.replace("+","");return a.e164Number?.slice((h?.length||3)+1),{UserName:this.mobileNumberOrEmail??"",Password:i,OTPCode:this.otpCode,Name:r,Email:this.mobileNumberOrEmail??"",phoneNumber:this.phoneNumber.dialCode?.replace("+","")+this.phoneNumber.number,ReferralBy:this.referralBy,IsSubscribed:this.isChecked??!1}}submitEmailResistration(t){if(this.fireRegistrationEvent(),!this.password)return void this.handleError(this.translate.instant("ErrorMessages.mobileRequired"));const a=this.getRegisterUserByEmailDTO(t);this.otpService.registerUserByEmail(a).subscribe({next:i=>{i.success?(this.handleRegisterUserResponse(i,a?.UserName,a?.Password),this.fireRegistrationSuccessEvent(a?.UserName)):this.handleError(i.message)},error:i=>this.handleError(i.message)})}submitPhoneResistration(t){if(this.fireRegistrationEvent(),!this.password)return void this.handleError(this.translate.instant("ErrorMessages.mobileRequired"));const a=this.getRequestDto();this.otpService.registerUser(a).subscribe({next:i=>{this.handleRegisterUserResponse(i,a.UserName,a.Password),this.fireRegistrationSuccessEvent(a.UserName)},error:i=>this.handleError(i.message)})}getRequestDto(t){const a=this.phoneNumber,i=this.password,o=a.e164Number?.slice(1),r=`${this.firstName} ${this.lastName}`,h=localStorage.getItem("CountryPhone")?.replace("+",""),f=a.e164Number?.slice((h?.length||3)+1);return{UserName:t?o:h+f,Password:i,OTPCode:this.otpCode,Name:r,Email:this.email,number:t?o:h+f,ReferralBy:this.referralBy,IsSubscribed:this.isChecked??!1}}handleRegisterUserResponse(t,a,i){t.success?(this.isGoogleAnalytics&&this.customGAService.signUpEvent("Phone",a,"register_page"),this.isGoogleAnalytics&&this.permissionService.getTagFeature("consumer_register")&&this.$gaService.event("consumer_register","register",a),this.openSuccessDialog(a,i),this.handleSuccess()):this.handleError(t.message),(0,m.NF)(this.platformId)&&this.document.body.classList.remove("overlay")}handleLoginSuccess(t){var a=this;return(0,M.Z)(function*(){if(t?.success&&"consumer"==t.data.role){a.isGoogleAnalytics&&a.permissionService.getTagFeature("LOGIN")&&a.$gaService.event(a.tagName.LOGIN,"","",1,!0,{user_ID:t.data.mobileNumber});const i={consentType:k.h.Cookie,sessionId:localStorage.getItem("consumer-consent-sessionId")||"",consent:!0,userId:t?.data?.id};a.userService.updateUserConsent(i).subscribe({next:g=>{}}),a.setUserData(t.data),a.setAuthToken(t.data.authToken),t?.data?.currency&&a.store.set("currency",t.data.currency),localStorage.setItem("refreshToken",t.data.refreshToken),a.store.set("refreshToken",t.data.refreshToken);const o={sessionId:localStorage.getItem("sessionId")},r=localStorage.getItem("cartId");yield a.checkCart(o,r),a.handlePostLoginNavigation(t.data)}else a.handleLoginFailure(t?.message)})()}openSuccessDialog(t,a){var i=this;const o=window.innerWidth<=767;this.ref=this.dialogService.open(J.z,{width:o?"90%":"540px",closable:!1,closeOnEscape:!0,showHeader:!1,styleClass:"dialog-wrapper",position:"center",data:{successMsg:1==(localStorage.getItem("tenantId")??1)?`<div style="display: flex; flex-direction: column"><div class="text-600 text-center"> <b>${this.translate.instant("auth.registerPassword.RegistrationSuccessful")} </b> </div>\n            <div>${this.translate.instant("auth.registerPassword.registerSuccessMsgUghanda")} </div></div>`:"auth.registerPassword.registerSuccessMsg"}}),this.ref.onClose.pipe((0,L.b)(()=>{var r;this.auth.login({username:t,password:a}).subscribe({next:(r=(0,M.Z)(function*(g){yield i.handleLoginSuccess(g),i.loading=!1}),function(h){return r.apply(this,arguments)}),error:r=>{this.handleLoginError(r)}})}),(0,U.P)()).subscribe()}handleSuccess(){this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.register"),detail:this.translate.instant("ResponseMessages.registerSuccess")})}handleError(t){this.loading=!1,this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t})}termsAndConditionsModal(){this.pageId=5,this.getAboutUsDetails(),this.aboutUsService.getShopAboutUs().subscribe({next:t=>{let a=t.data?.records?.filter(i=>i.pageId==this.pageId);this.aboutUsDetails.title=a[0]?.title,this.aboutUsDetails.content=atob(a[0].content),this.displayTermsAndConditions=!0},error:t=>{}})}closeTermsModal(){this.displayTermsAndConditions=!1,(0,m.NF)(this.platformId)&&this.document.body.classList.remove("overlay")}reloadCurrentPage(t,a){this.router.navigateByUrl("/",{skipLocationChange:!0}).then(()=>this.router.navigate(["/about-us/"],{queryParams:{pageId:t,title:a}}))}approveModal(){this.displayApprovedModal=!0}getAboutUsDetails(){}get labelColor(){return this.screenWidth<=767?"grey":"white"}get floatingLabel(){return this.screenWidth<=767}get forgotPasswordClass(){return this.screenWidth<=767?"no-underline main-color font-size-12 bold-font mb-4 mt-4":"font-size-12 mb-4"}setUserData(t){this.mainDataService.setUserData(t),this.store.set("profile",t),this.store.set("userPhone",t.mobileNumber),localStorage.setItem("userId",t.id),this.store.set("timeInterval",(new Date).getTime())}setAuthToken(t){let a=t.replace("bearer ",""),o=((0,V.Z)(a).exp/864e5).toFixed(0);localStorage.removeItem("visited");const r=new Date;r.setDate(r.getDate()+parseInt(o));let g=F.AES.encrypt(a,"paysky").toString();localStorage.setItem("auth_enc",g),this.cookieService.set("authToken",a,{expires:r,path:"/",sameSite:"Strict"}),localStorage.removeItem("isGuest"),this.authTokenService.authTokenSet(a)}handlePostLoginNavigation(t){this.router.navigate(["/"]),this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.login"),detail:this.translate.instant("ResponseMessages.loggedInSuccessfully")})}handleLoginError(t){this.store.set("profile",""),this.loading=!1,this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message}),localStorage.setItem("isGuest","true")}handleLoginFailure(t){this.store.set("profile",""),this.loading=!1,this.messageService.add({severity:"error",summary:t||this.translate.instant("ErrorMessages.invalidUserNameOrPassword")}),localStorage.setItem("isGuest","true")}checkCart(t,a){return new Promise((i,o)=>{t.sessionId?(t.cartId=a&&""!=a?parseInt(a):0,this.cartService.updateCart(t).subscribe({next:r=>{r?.data?.cartItems?.length&&(this.cartListData=r.data.cartItems,this.cartListCount=r.data.cartItems.length),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(t),i()},error:r=>{this.cartListCount=0,this.cartListData=[],this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(t),o(r)}})):(localStorage.setItem("sessionId",B.R.newGuid()),t.sessionId=localStorage.getItem("sessionId"),this.getAllCart(t),i())})}getAllCart(t){this.products=[];let a={sessionId:t.sessionId},i=localStorage.getItem("apply-to");i&&""!=i&&(a.applyTo=i),this.cartService.getCart(a).subscribe({next:o=>{this.cartListCount=0,this.cartListData=[],o.data?.records?.length?(this.cartListCount=0,o.data.records[0].cartDetails.length&&(this.cartListCount=o.data.records[0].cartDetails.length,this.cartListData=o.data.records[0].cartDetails),o.data.records[0].cartDetailsDPay&&o.data.records[0].cartDetailsDPay.length&&(this.cartListCount+=o.data.records[0].cartDetailsDPay.length,this.cartListData=this.cartListData.concat(o.data.records[0].cartDetailsDPay)),this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData)):(this.mainDataService.setCartLenghtData(0),this.mainDataService.setCartItemsData([]))}})}getShipmentMethodByTenantId(t){this.permissionService.hasPermission("Shipment-Fee")?this.cartService.getShipmentMethodByTenantId().subscribe(a=>{a.success&&a.data.length&&(localStorage.setItem("apply-to",a.data[0].applyTo),this.getAllCart(t))}):(localStorage.setItem("apply-to","2"),this.getAllCart(t))}static \u0275fac=function(a){return new(a||n)(e.Y36(C.ez),e.Y36(e.SBq),e.Y36(u.sK),e.Y36(w.F0),e.Y36(p.aO),e.Y36(p.Dr),e.Y36(G.H7),e.Y36(C.b4),e.Y36(p.UW),e.Y36(O.$r),e.Y36(p.$A),e.Y36(m.K0),e.Y36(e.Lbi),e.Y36(p.KD),e.Y36(N.xA),e.Y36(K.J),e.Y36(p.e8),e.Y36(p.d6),e.Y36(p.iI),e.Y36(X.N),e.Y36(p.Lz),e.Y36(p.Ni),e.Y36(p.$V))};static \u0275cmp=e.Xpm({type:n,selectors:[["app-register-details"]],standalone:!0,features:[e._Bn([N.xA]),e.jDz],decls:9,vars:18,consts:[[1,"update-password-page"],["class","spinner",4,"ngIf"],[4,"ngIf"],[3,"visible","breakpoints","closable","resizable","visibleChange"],["pTemplate","content"],["pTemplate","footer"],[1,"termsConditionModal",3,"visible","baseZIndex","breakpoints","closable","visibleChange"],[1,"spinner"],[1,"content-container",3,"ngClass"],[1,"grid","justify-content-center","shadow-signin"],[1,"image","col-12","desktop-only"],["src","assets/images/registerLogo.svg","alt","","srcset",""],[1,"col-12","col-md-8","col-lg-6","bg-white","content-body"],[1,"mobile-header","mobile-only"],["src","assets/images/signup.svg","alt","Sign Up"],[1,"head-desc"],[1,"signup-heading"],[1,"signup-desc"],[1,"desktop-only"],[1,"m-0","py-0","signup-heading"],[1,"main-color","mb-3","please-signin"],[1,"p-fluid"],["autocomplete","new-password"],[3,"labelColor","floatingLabel","firstNameChange","lastNameChange","validationChange","blur"],[3,"maxLength","selectedCountryISO","preferredCountries","placeholder","phoneNumberChange","validationChange",4,"ngIf"],[3,"emailRequired","floatingLabel","labelColor","emailLabel","emailChange","validationChange","blur",4,"ngIf"],[1,"p-field","p-col-12"],[3,"ngClass"],[3,"ngStyle","for"],["name","referralBy","pInputText","","type","text",3,"ngModel","ngModelOptions","id","click","input","ngModelChange"],["class","field-error",4,"ngIf"],[3,"showForgotPassword","floatingLabel","passwordChange","validationChange"],[3,"ngStyle"],["name","confirmPassword","required","true",1,"customClass",3,"ngModel","ngModelOptions","feedback","toggleMask","id","click","ngModelChange"],[1,"list","p-0","mt-3"],[1,"list-none","font-size-13","font-italic","mb-2"],[1,"ml-2","instruction",3,"ngStyle"],[1,"list-none","font-size-13","font-italic"],["pButton","","type","button",1,"submit-btn",3,"disabled","label","click"],["id","opt-in",1,"opt-in"],[3,"ngModel","binary","ngModelChange"],[1,"signin-agreement","cursor-pointer","desktop-only"],[3,"click"],[1,"p-field","p-col-12","mt-3","mb-4","mobile-only"],[1,"Terms-conditions"],[1,"underline","cursor-pointer","main-color",3,"click"],[3,"maxLength","selectedCountryISO","preferredCountries","placeholder","phoneNumberChange","validationChange"],[3,"emailRequired","floatingLabel","labelColor","emailLabel","emailChange","validationChange","blur"],[1,"field-error"],[2,"color","red"],["aria-hidden","true","class","pi pi-info-circle","style","color: grey; font-size: 0.8rem",4,"ngIf"],["aria-hidden","true","class","pi pi-check-circle","style","color: #01b467; font-size: 0.8rem",4,"ngIf"],["aria-hidden","true","class","pi pi-times-circle","style","color: red; font-size: 0.8rem",4,"ngIf"],["aria-hidden","true",1,"pi","pi-info-circle",2,"color","grey","font-size","0.8rem"],["aria-hidden","true",1,"pi","pi-check-circle",2,"color","#01b467","font-size","0.8rem"],["aria-hidden","true",1,"pi","pi-times-circle",2,"color","red","font-size","0.8rem"],["src","assets/images/signUp/font-Awsome-info-circle.svg","aria-hidden","true","class","size-circle",4,"ngIf"],["src","assets/images/signUp/font-Awsome-check-circle.svg","aria-hidden","true","class","size-circle",4,"ngIf"],["src","assets/images/signUp/font-Awsome-times-circle.svg","aria-hidden","true","class","size-circle",4,"ngIf"],["src","assets/images/signUp/font-Awsome-info-circle.svg","aria-hidden","true",1,"size-circle"],["src","assets/images/signUp/font-Awsome-check-circle.svg","aria-hidden","true",1,"size-circle"],["src","assets/images/signUp/font-Awsome-times-circle.svg","aria-hidden","true",1,"size-circle"],[1,"icon","mt-5","bg-#01B467-500","text-white","text-center","w-3rem","h-2rem","border-circle","icon","bg-#01B467-500"],[1,"pi","pi-check"],[1,"font-bold","text-center","text-black-alpha-90"],[1,"d-flex","jc-center","ai-center",2,"gap","1rem"],["label","Confirm","pButton","","type","button",1,"p-field","my-4","width-25","font-size-14","second-btn",3,"click"],[1,"termsandcondition"],["class","term-condition-heading",4,"ngIf"],[1,"h-19rem","terms-condition"],["class","col-12 font-size-18 m-0 py-0 mx-4",3,"innerHtml",4,"ngIf"],[1,"term-condition-heading"],[1,"col-12","font-size-18","m-0","py-0","mx-4",3,"innerHtml"],["pButton","","type","button",1,"p-field","p-col-12","my-4","width-25","m-auto","font-size-14","second-btn",3,"label","click"]],template:function(a,i){1&a&&(e.TgZ(0,"section",0),e.YNc(1,H,2,0,"div",1),e.YNc(2,He,108,132,"ng-container",2),e.TgZ(3,"p-dialog",3),e.NdJ("visibleChange",function(r){return i.displayApprovedModal=r}),e.YNc(4,je,5,3,"ng-template",4),e.YNc(5,$e,2,0,"ng-template",5),e.qZA(),e.TgZ(6,"p-dialog",6),e.NdJ("visibleChange",function(r){return i.displayTermsAndConditions=r}),e.YNc(7,tt,4,2,"ng-template",4),e.YNc(8,nt,2,3,"ng-template",5),e.qZA()()),2&a&&(e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading),e.xp6(1),e.Akn(e.DdM(14,it)),e.Q6J("visible",i.displayApprovedModal)("breakpoints",e.DdM(15,R))("closable",!0)("resizable",!1),e.xp6(3),e.Akn(e.DdM(16,at)),e.Q6J("visible",i.displayTermsAndConditions)("baseZIndex",1e4)("breakpoints",e.DdM(17,R))("closable",!0))},dependencies:[m.ez,m.mk,m.O5,m.PC,w.Bz,u.aw,u.X$,v.hJ,v.Hq,C.jx,y.S,y.V,P.nD,P.XZ,c.u5,c._Y,c.Fj,c.JJ,c.JL,c.Q7,c.On,c.F,T.gz,T.ro,x.j,x.o,Y,z.q,E.C,D.L,D.G,q.T],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}  .p-dialog .p-dialog-header{display:none}  .p-dialog .p-dialog-footer{text-align:center}  .p-dialog .p-dialog-footer button{width:90%!important}[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:auto!important}  .country-dropdown{max-height:225px!important;overflow:auto!important}div.icon[_ngcontent-%COMP%]{position:relative;margin:auto}div.icon[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%]{position:absolute;top:31%;font-weight:700;left:30%;font-size:1.2rem}[_nghost-%COMP%]     button.back-btn{position:relative!important}.update-password-page[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;min-height:100vh;width:100%}@media screen and (max-width: 768px){.update-password-page[_ngcontent-%COMP%]{margin-top:0}}.content-container[_ngcontent-%COMP%]{width:100%;max-width:1200px;margin:0 auto;padding:0 1rem}.content-container.mobile-content-container[_ngcontent-%COMP%]{margin-top:120px}@media screen and (max-width: 480px){.content-container.mobile-content-container[_ngcontent-%COMP%]{margin-top:100px;padding:0 .75rem}}.shadow-signin[_ngcontent-%COMP%]{margin:2rem auto;padding:2rem;border:1px solid rgba(151,151,151,.17);border-radius:7px;background-color:#fff;max-width:1000px}@media screen and (max-width: 767px){.shadow-signin[_ngcontent-%COMP%]{margin:1rem auto;padding:1.5rem;border:none}}@media screen and (max-width: 480px){.shadow-signin[_ngcontent-%COMP%]{margin:1rem auto;padding:1rem}}.image[_ngcontent-%COMP%]{flex:1;text-align:end}.image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px;border-radius:281.739px}@media screen and (max-width: 768px){.image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:250px}}@media screen and (max-width: 480px){.image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:200px}}.content-body[_ngcontent-%COMP%]{flex:1;max-width:600px;margin:0 auto;padding:2rem}@media screen and (max-width: 767px){.content-body[_ngcontent-%COMP%]{padding:1.5rem}}@media screen and (max-width: 480px){.content-body[_ngcontent-%COMP%]{padding:1rem}}.signup-heading[_ngcontent-%COMP%]{font-size:20px;font-weight:500;color:#212121;font-family:var(--medium-font)!important;margin-bottom:10px!important}@media screen and (max-width: 480px){.signup-heading[_ngcontent-%COMP%]{font-size:18px}}.please-signin[_ngcontent-%COMP%]{font-size:14px;font-weight:400;margin-top:20px;color:#443f3f;font-family:var(--regular-font)!important}@media screen and (max-width: 480px){.please-signin[_ngcontent-%COMP%]{font-size:13px;margin-top:15px}}.mobile-header[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding:.75rem 0}@media screen and (max-width: 480px){.mobile-header[_ngcontent-%COMP%]{padding:.5rem 0}}.mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{margin-right:1rem}@media screen and (max-width: 480px){.mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{margin-right:.75rem}}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]{display:flex;flex-direction:column}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{margin:0!important;font-size:18px}@media screen and (max-width: 480px){.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{font-size:16px}}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#443f3f;font-family:var(--regular-font)!important}@media screen and (max-width: 480px){.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%]{font-size:11px}}input#float-input[_ngcontent-%COMP%], input#custom-float-input[_ngcontent-%COMP%]{height:50px!important;width:100%;border-radius:2px;opacity:1;border:none!important;border:1px solid #e4e7e9!important;padding-left:10px;padding-right:10px;font-size:14px;font-weight:400;font-family:var(--regular-font)!important;background-color:#fff!important}@media screen and (max-width: 480px){input#float-input[_ngcontent-%COMP%], input#custom-float-input[_ngcontent-%COMP%]{height:48px!important;font-size:14px;padding-left:8px;padding-right:8px}}  .customClass input{height:50px!important;width:100%;border-radius:2px;opacity:1;border:none!important;border:1px solid #e4e7e9!important;padding-left:10px;padding-right:10px;padding-top:20px;font-family:var(--regular-font)!important;font-size:14px}@media screen and (max-width: 480px){  .customClass input{height:48px!important;font-size:14px;padding-left:8px;padding-right:8px;padding-top:18px}}label[_ngcontent-%COMP%]{color:#323232!important;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}@media screen and (max-width: 480px){label[_ngcontent-%COMP%]{font-size:10px!important;padding:8px}}  .pi-eye{position:absolute!important;right:9px!important;top:30px!important}@media screen and (max-width: 480px){  .pi-eye{right:8px!important;top:28px!important}}.list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{text-align:left;letter-spacing:0px;opacity:1;font-family:main-medium,sans-serif;font-style:inherit!important}@media screen and (max-width: 480px){.list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:6px}}.size-circle[_ngcontent-%COMP%]{width:13px}@media screen and (max-width: 480px){.size-circle[_ngcontent-%COMP%]{width:12px}}.instruction[_ngcontent-%COMP%]{font-size:12px;font-weight:400;font-family:var(--regular-font)!important}@media screen and (max-width: 480px){.instruction[_ngcontent-%COMP%]{font-size:11px}}.submit-btn[_ngcontent-%COMP%]{padding:12px 24px;border-radius:4px;font-size:14px;font-weight:500;font-family:var(--regular-font)!important;text-transform:uppercase;width:100%;margin:.5rem 0;background-color:#204e6e;color:#fff;border:none;height:48px}.submit-btn[_ngcontent-%COMP%]:hover{background-color:#1d4c69}@media screen and (max-width: 767px){.submit-btn[_ngcontent-%COMP%]{background-color:var(--primary);margin-top:.5rem}.submit-btn[_ngcontent-%COMP%]:hover{background-color:#1d4c69}}@media screen and (max-width: 480px){.submit-btn[_ngcontent-%COMP%]{height:46px;font-size:14px;padding:10px 20px;margin:.75rem 0}}.second-btn[_ngcontent-%COMP%]{padding:12px 24px;border-radius:4px;font-size:14px;font-weight:500;font-family:var(--regular-font)!important;text-transform:uppercase}@media screen and (max-width: 480px){.second-btn[_ngcontent-%COMP%]{padding:10px 20px;font-size:13px}}.signin-agreement[_ngcontent-%COMP%]{font-size:12px;color:#272727;margin-top:16px;font-family:var(--regular-font)!important}@media screen and (max-width: 480px){.signin-agreement[_ngcontent-%COMP%]{font-size:11px;margin-top:12px}}.signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;text-decoration:underline!important}.Terms-conditions[_ngcontent-%COMP%]{font-size:12px;font-weight:400;font-family:var(--regular-font)!important;color:#323232;line-height:1.5;padding:0 10px}@media screen and (max-width: 767px){.Terms-conditions[_ngcontent-%COMP%]{font-size:11px;padding:0 8px}}.Terms-conditions[_ngcontent-%COMP%]   .underline[_ngcontent-%COMP%]{color:var(--primary);text-decoration:underline;cursor:pointer}.Terms-conditions[_ngcontent-%COMP%]   .underline[_ngcontent-%COMP%]:hover{opacity:.8}.opt-in[_ngcontent-%COMP%]{display:flex;gap:10px;margin:1rem 0}@media screen and (max-width: 480px){.opt-in[_ngcontent-%COMP%]{gap:8px;margin:.75rem 0}}.opt-in[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:var(--dark-gray);font:normal 400 12px/20px var(--regular-font);letter-spacing:.4px}@media screen and (max-width: 480px){.opt-in[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:11px;line-height:18px}}input[type=checkbox][_ngcontent-%COMP%]{accent-color:#004f71;margin-right:8px}@media screen and (max-width: 480px){input[type=checkbox][_ngcontent-%COMP%]{margin-right:6px}}.signin-action[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:10px;padding-bottom:25px}@media screen and (max-width: 480px){.signin-action[_ngcontent-%COMP%]{gap:8px;padding-bottom:20px}}.signin-action[_ngcontent-%COMP%]   .signin-btn[_ngcontent-%COMP%]{background-color:var(--light-blue);border-radius:8px;padding:0 24px;color:var(--primary);font-size:14px;font-style:normal;font-weight:500;line-height:56px;border:none}@media screen and (max-width: 480px){.signin-action[_ngcontent-%COMP%]   .signin-btn[_ngcontent-%COMP%]{padding:0 20px;font-size:13px;line-height:50px}}.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#373636;font-size:14px;font-style:normal;font-weight:500;line-height:normal;position:relative;font-family:var(--medium-font)}@media screen and (max-width: 480px){.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:13px}}.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]:before{content:"";position:absolute;left:-32px;width:40px;height:1px;opacity:.1;background:var(--primary, #204e6e);top:10px}@media screen and (max-width: 480px){.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]:before{left:-28px;width:35px}}.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]:after{content:"";position:absolute;right:-32px;width:40px;height:1px;opacity:.1;background:var(--primary, #204e6e);top:10px}@media screen and (max-width: 480px){.signin-action[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]:after{right:-28px;width:35px}}.terms-condition[_ngcontent-%COMP%]{font-family:main-medium,sans-serif;height:auto!important}.term-condition-heading[_ngcontent-%COMP%]{font-family:main-medium,sans-serif}@media screen and (max-width: 480px){.term-condition-heading[_ngcontent-%COMP%]{font-size:18px}}.termsandcondition[_ngcontent-%COMP%]{padding:35px 10px 0}@media screen and (max-width: 480px){.termsandcondition[_ngcontent-%COMP%]{padding:25px 8px 0}}.field-error[_ngcontent-%COMP%]{margin-top:.25rem}@media screen and (max-width: 480px){.field-error[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:10px}}.mobile-only[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 767px){.mobile-only[_ngcontent-%COMP%]{display:flex}}.desktop-only[_ngcontent-%COMP%]{display:block}@media screen and (max-width: 767px){.desktop-only[_ngcontent-%COMP%]{display:none}}@media screen and (max-width: 767px){.mobile-container[_ngcontent-%COMP%]{margin:120px 0 30px;padding:0}.p-fluid[_ngcontent-%COMP%]{padding:0 1rem}}@media screen and (max-width: 480px){.mobile-container[_ngcontent-%COMP%]{margin:100px 0 20px;padding:0}.p-fluid[_ngcontent-%COMP%]{padding:0 .75rem}.p-field[_ngcontent-%COMP%]{margin-bottom:.75rem}}@media screen and (max-width: 360px){.signup-heading[_ngcontent-%COMP%]{font-size:16px}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{font-size:15px}.submit-btn[_ngcontent-%COMP%]{height:44px;font-size:12px}.p-fluid[_ngcontent-%COMP%]{padding:0 .5rem}}.pointer[_ngcontent-%COMP%]{cursor:pointer}  .disable{pointer-events:none!important}.p-fluid[_ngcontent-%COMP%]{max-width:100%;margin:0 auto}.p-fluid[_ngcontent-%COMP%]   .p-grid[_ngcontent-%COMP%]{margin:0}@media screen and (max-width: 480px){.p-field[_ngcontent-%COMP%]{margin-bottom:1rem}}']})}return n})()}}]);