$yellowButtonColor: #FFCC00;
$blueButtonColor: #004F71;
$formFieldOutLine: #F5F5F5;
$hintMessageColor: #A3A3A3;
$whiteColor: #fff;
$blackColor: #000;
$font-main-bold: 'MTNBrighterSans-Bold';
$font-main-medium: 'main-medium';
$font-main-regular: 'main-regular';
$font-main-light: 'main-light';
$header_height: 122px;
$header_height_without_navbar: 73px;

$breakpoints: (
  mobile: "only screen and (max-width: 767px)",
  tablet: "only screen and (min-width: 768px) and (max-width: 1200px)",
  desktop: "only screen and (min-width: 1201px) and (max-width: 1700px)",
  large-desktop: "only screen and (min-width: 1701px)"
);

// Define mixin for media queries
@mixin responsive($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media #{map-get($breakpoints, $breakpoint)} {
      @content;
    }
  }

  @else {
    @error "Unknown breakpoint: #{$breakpoint}. Please use 'mobile', 'tablet', or 'desktop'.";
  }
}

.header-spacing {
  margin-top: $header_height;
}

.header-spacing-not-navbar {
  margin-top: $header_height;
}
.discount-price{
  font-size: 14px;
  font-weight:500;
  color:#E21836 !important;
  font-family: var(--medium-font) !important;
}
