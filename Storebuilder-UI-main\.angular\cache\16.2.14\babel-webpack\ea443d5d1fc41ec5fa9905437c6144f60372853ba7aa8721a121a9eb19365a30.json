{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr, faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faLine } from '@fortawesome/free-brands-svg-icons';\nimport { faSms, faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus } from '@fortawesome/free-solid-svg-icons';\nimport * as i1 from '@fortawesome/angular-fontawesome';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nconst shareIcons = [faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr, faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faSms, faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus, faLine];\nclass ShareIconsModule {\n  constructor(iconLibrary) {\n    iconLibrary.addIcons(...shareIcons);\n  }\n  static #_ = this.ɵfac = function ShareIconsModule_Factory(t) {\n    return new (t || ShareIconsModule)(i0.ɵɵinject(i1.FaIconLibrary));\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ShareIconsModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [FontAwesomeModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShareIconsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FontAwesomeModule]\n    }]\n  }], function () {\n    return [{\n      type: i1.FaIconLibrary\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ShareIconsModule };", "map": {"version": 3, "names": ["i0", "NgModule", "faFacebookF", "faTwitter", "faLinkedinIn", "faPinterestP", "faRedditAlien", "faTumblr", "faWhatsapp", "faViber", "faVk", "faFacebookMessenger", "faTelegramPlane", "faMix", "faXing", "faLine", "faSms", "faEnvelope", "faCheck", "faPrint", "faExclamation", "faLink", "faEllipsisH", "faMinus", "i1", "FontAwesomeModule", "shareIcons", "ShareIconsModule", "constructor", "iconLibrary", "addIcons", "_", "ɵfac", "ShareIconsModule_Factory", "t", "ɵɵinject", "FaIconLibrary", "_2", "ɵmod", "ɵɵdefineNgModule", "type", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ngx-sharebuttons/fesm2022/ngx-sharebuttons-icons.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr, faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faLine } from '@fortawesome/free-brands-svg-icons';\nimport { faSms, faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus } from '@fortawesome/free-solid-svg-icons';\nimport * as i1 from '@fortawesome/angular-fontawesome';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\n\nconst shareIcons = [\n    faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr,\n    faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faSms,\n    faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus, faLine\n];\nclass ShareIconsModule {\n    constructor(iconLibrary) {\n        iconLibrary.addIcons(...shareIcons);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: ShareIconsModule, deps: [{ token: i1.FaIconLibrary }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.2\", ngImport: i0, type: ShareIconsModule, imports: [FontAwesomeModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: ShareIconsModule, imports: [FontAwesomeModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: ShareIconsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        FontAwesomeModule,\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: i1.FaIconLibrary }]; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ShareIconsModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,QAAQ,oCAAoC;AACxN,SAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,mCAAmC;AACpI,OAAO,KAAKC,EAAE,MAAM,kCAAkC;AACtD,SAASC,iBAAiB,QAAQ,kCAAkC;AAEpE,MAAMC,UAAU,GAAG,CACfxB,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAC3EC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEE,KAAK,EACrFC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAER,MAAM,CACpF;AACD,MAAMY,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,WAAW,EAAE;IACrBA,WAAW,CAACC,QAAQ,CAAC,GAAGJ,UAAU,CAAC;EACvC;EAAC,QAAAK,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFP,gBAAgB,EAA1B3B,EAAE,CAAAmC,QAAA,CAA0CX,EAAE,CAACY,aAAa;EAAA,CAA2C;EAAA,QAAAC,EAAA,GAC9L,IAAI,CAACC,IAAI,kBAD8EtC,EAAE,CAAAuC,gBAAA;IAAAC,IAAA,EACSb;EAAgB,EAAiC;EAAA,QAAAc,EAAA,GACnJ,IAAI,CAACC,IAAI,kBAF8E1C,EAAE,CAAA2C,gBAAA;IAAAC,OAAA,GAEqCnB,iBAAiB;EAAA,EAAI;AAChK;AACA;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAJoG7C,EAAE,CAAA8C,iBAAA,CAIXnB,gBAAgB,EAAc,CAAC;IAC9Ga,IAAI,EAAEvC,QAAQ;IACd8C,IAAI,EAAE,CAAC;MACCH,OAAO,EAAE,CACLnB,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEe,IAAI,EAAEhB,EAAE,CAACY;IAAc,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEhF;AACA;AACA;;AAEA,SAAST,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}