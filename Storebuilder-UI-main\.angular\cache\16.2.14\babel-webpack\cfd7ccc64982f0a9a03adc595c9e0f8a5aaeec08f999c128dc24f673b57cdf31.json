{"ast": null, "code": "import { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { k as getSlideTransformEl } from '../shared/utils.mjs';\nfunction EffectFade(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\nexport { EffectFade as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}