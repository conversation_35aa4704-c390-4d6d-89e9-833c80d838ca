{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate, __param } from \"tslib\";\nimport { Component, HostListener, Inject, PLATFORM_ID, ViewChild } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { isPlatformBrowser } from '@angular/common';\nimport { CountryISO, PhoneNumberFormat, SearchCountryField } from \"ngx-intl-tel-input-gg\";\nimport { TenantRecords } from \"@core/interface\";\nlet AddressComponent = class AddressComponent {\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(ngZone, addressService, messageService, router, store, route, translate, mainDataService, loaderService, _location, cd, permissionService, appDataService, $gtmService, platformId) {\n    this.ngZone = ngZone;\n    this.addressService = addressService;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.route = route;\n    this.translate = translate;\n    this.mainDataService = mainDataService;\n    this.loaderService = loaderService;\n    this._location = _location;\n    this.cd = cd;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.$gtmService = $gtmService;\n    this.platformId = platformId;\n    this.markerOptions = {\n      draggable: true,\n      icon: 'assets/images/map-pin.svg'\n    };\n    this.phoneLength = 13;\n    this.landMarkAddressRequired = false;\n    this.isMobileTemplate = false;\n    this.isDisplaySuccessModal = false;\n    this.isDisplayOtherModal = false;\n    this.message = '';\n    this.addressDetailCity = '';\n    this.addressForm = new UntypedFormGroup({\n      addressLabel: new UntypedFormControl('Home'),\n      receiverFirstName: new UntypedFormControl(''),\n      receiverLastName: new UntypedFormControl(''),\n      streetAddress: new UntypedFormControl('', Validators.required),\n      country: new UntypedFormControl('', Validators.required),\n      city: new UntypedFormControl('', Validators.required),\n      landMark: new UntypedFormControl('', Validators.required),\n      deliveryInstructions: new UntypedFormControl(''),\n      buldingNumber: new UntypedFormControl(''),\n      postcode: new UntypedFormControl(''),\n      receiverPhoneNumber: new UntypedFormControl('', Validators.required),\n      geo_location: new UntypedFormControl(''),\n      Lat: new UntypedFormControl(''),\n      Lng: new UntypedFormControl(''),\n      Id: new UntypedFormControl(''),\n      additionalAddress: new UntypedFormControl(''),\n      region: new UntypedFormGroup({\n        id: new UntypedFormControl(''),\n        regionName: new UntypedFormControl('')\n      }, Validators.required)\n    });\n    this.search = '';\n    this.isDefault = false;\n    this.center = {\n      lat: 0.3,\n      lng: 32.5\n    };\n    this.mapOptions = {\n      fullscreenControl: false,\n      disableDefaultUI: true\n    };\n    this.allCities = [];\n    this.isDifferentCity = false;\n    this.addressLabelList = [{\n      'name': 'Home',\n      'id': 1\n    }, {\n      'name': 'Work',\n      'id': 2\n    }, {\n      'name': 'Other',\n      'id': 3\n    }];\n    this.id = '';\n    this.geoCoder = new google.maps.Geocoder();\n    this.borderBottomStyle = '2px solid red !important';\n    this.phoneInputLength = 12;\n    this.preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n    this.customPlaceHolder = '';\n    this.PhoneNumberFormat = PhoneNumberFormat;\n    this.SearchCountryField = SearchCountryField;\n    this.allRegionList = [];\n    this.filteredCities = [];\n    this.screenWidth = window.innerWidth;\n    this.selectedCitiesValue = [];\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.source = this.router.getCurrentNavigation()?.extras?.state;\n    let tenantId = localStorage.getItem('tenantId');\n    if (tenantId && tenantId !== '') {\n      if (tenantId == '1') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '2') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '3') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '4') {\n        this.customPlaceHolder = 'XXXXXXXXXX';\n      }\n    }\n  }\n  onBack() {\n    this._location.back();\n  }\n  ngOnInit() {\n    this.route.queryParamMap.subscribe(queryParams => {\n      this.routeToCheckOut = queryParams.get(\"checkout\");\n    });\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.lat = history.state.lat;\n    this.lng = history.state.lng;\n    this.route.queryParams.subscribe(params => {\n      console.log('parr', params);\n      this.redirectUrl = params.returnUrl;\n    });\n    this.routeSub = this.route.params.subscribe(params => {\n      this.id = params['id'];\n      if (this.id != 'add-address') {\n        this.getCustomerAddress();\n        this.$gtmService.pushPageView('Your Addresses', 'Update Address');\n      } else {\n        this.$gtmService.pushPageView('Your Addresses', 'Add Address');\n        const defaultPhoneNumber = localStorage.getItem('userPhone');\n        const countryPhone = localStorage.getItem('CountryPhone')?.replace('+', '') || '';\n        const phoneNumberWithoutCode = defaultPhoneNumber?.replace(countryPhone, '');\n        this.addressForm.get('receiverPhoneNumber')?.setValue(phoneNumberWithoutCode);\n        this.addressForm.get('receiverPhoneNumber')?.markAllAsTouched();\n        this.getAllRegion();\n        if (this.lat && this.lng) {\n          this.setCurrentLocation({\n            lat: this.lat,\n            lng: this.lng\n          });\n        } else {\n          this.setCurrentLocation();\n        }\n      }\n    });\n    let phoneLength = localStorage.getItem('PhoneLength')?.toString();\n    let landMarkAddress = localStorage.getItem('customerAddressLandmarkRequired')?.toString();\n    if (landMarkAddress && landMarkAddress == 'True') {\n      this.landMarkAddressRequired = true;\n      this.addressForm.controls['landMark'].setValidators([Validators.required]);\n      this.addressForm.controls['landMark'].updateValueAndValidity();\n    }\n    if (phoneLength) {\n      this.phoneLength = parseInt(phoneLength) - 2;\n    }\n    let userDetails = this.store.get('profile');\n    this.mainDataService.setUserData(userDetails);\n    let name = userDetails?.name?.split(' ');\n    this.addressForm.patchValue({\n      receiverFirstName: name[0] ? name[0] : '',\n      receiverLastName: name[1] ? name[1] : ''\n      // receiverPhoneNumber: userDetails.mobileNumber,\n    });\n\n    if (!localStorage.getItem(\"isoCode\")) {\n      const tenants = this.appDataService.tenants;\n      if (tenants.records != undefined) {\n        let tenantId = localStorage.getItem('tenantId');\n        let data = tenants.records;\n        let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n        localStorage.setItem('isoCode', arr?.isoCode);\n        this.store.set('allCountryTenants', tenants.records);\n      }\n    } else {\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n    }\n    if (this.appDataService.configuration) {\n      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n    }\n    this.filteredCities = this.allCities;\n    this.region.valueChanges.subscribe(value => {\n      const selectedRegion = this.allRegionList?.find(r => r.id == value.id);\n      this.addressForm.patchValue({\n        region: {\n          id: selectedRegion.id,\n          regionName: selectedRegion.regionName\n        }\n      }, {\n        emitEvent: false\n      });\n    });\n  }\n  mapInitialize(map) {\n    this.map = map;\n  }\n  handleAddressChange(place) {\n    this.Lat = place.geometry.location.lat();\n    this.Lng = place.geometry.location.lng();\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.zoom = 12;\n    this.center = this.position[0].position;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  ngOnDestroy() {\n    this.routeSub.unsubscribe();\n  }\n  getCustomerAddress() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loaderService.show();\n      try {\n        const res = yield _this.addressService.getAddressById(_this.id).toPromise();\n        _this.addressService.loadedAddress = true;\n        if (res.data) {\n          _this.isDefault = res.data.isDefault;\n          _this.addressService.chosenAddress = res.data;\n          const trimmedPhoneNumber = _this.trimPhoneNumber(res.data.receiverPhoneNumber);\n          yield _this.getAllRegion();\n          const selectedRegion = _this.selectedRegion(res.data.region);\n          const selectedCity = _this.selectedCity(res.data.city);\n          _this.addressForm.patchValue({\n            addressLabel: res.data.addressLabel,\n            receiverFirstName: res.data.receiverFirstName,\n            receiverLastName: res.data.receiverLastName,\n            postcode: res.data.postCode,\n            receiverPhoneNumber: trimmedPhoneNumber,\n            landMark: res.data.landMark,\n            deliveryInstructions: res.data.deliveryInstructions,\n            buldingNumber: res.data.buldingNumber,\n            additionalAddress: res.data.additionalAddress,\n            city: selectedCity.cityName,\n            region: selectedRegion\n          });\n          if (res.data.addressLabel != 'Home' && res.data.addressLabel != 'Work') {\n            _this.addressLabelList[2].name = res.data.addressLabel;\n          }\n          _this.selectedAddressType = res.data.addressLabel;\n          _this.Lat = parseFloat(res.data.lat);\n          _this.Lng = parseFloat(res.data.lng);\n          const streetAddress = res.data.streetAddress;\n          if (_this.searchElementRef) {\n            _this.searchElementRef.nativeElement.value = streetAddress;\n          }\n          _this.position = [{\n            position: {\n              lat: _this.Lat,\n              lng: _this.Lng\n            }\n          }];\n          _this.zoom = 8;\n          _this.center = _this.position[0].position;\n          if (res.data.streetAddress || res.data.country) {\n            _this.addressForm.patchValue({\n              streetAddress: res.data.streetAddress,\n              country: res.data.country\n            });\n          } else {\n            _this.getAddress(_this.Lat, _this.Lng);\n          }\n          _this.createLocationButton();\n        } else {\n          _this.setCurrentLocation();\n          _this.createLocationButton();\n        }\n        _this.loaderService.hide();\n      } catch (err) {\n        _this.addressService.loadedAddress = true;\n        _this.setCurrentLocation();\n        _this.messageService.add({\n          severity: 'error',\n          summary: _this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      } finally {\n        _this.loaderService.hide();\n      }\n    })();\n  }\n  trimPhoneNumber(phoneNumber) {\n    return phoneNumber.length > 3 ? phoneNumber.substring(3) : phoneNumber;\n  }\n  selectedRegion(regionName) {\n    const region = this.allRegionList.find(r => r.regionName === regionName);\n    if (region) {\n      this.filterCitiesByRegion(region.id);\n      this.selectedCitiesValue = region.cities;\n      return {\n        id: region.id,\n        regionName: region.regionName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"regionName\": null\n    };\n  }\n  selectedCity(cityName) {\n    const city = this.selectedCitiesValue.find(r => r.cityName === cityName);\n    if (city) {\n      return {\n        id: city.id,\n        cityName: city.cityName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"cityName\": null\n    };\n  }\n  getAddress(latitude, longitude) {\n    this.geoCoder.geocode({\n      location: {\n        lat: latitude,\n        lng: longitude\n      }\n    }, (results, status) => {\n      if (status === 'OK') {\n        if (results[0]) {\n          this.position = [{\n            position: {\n              lat: latitude,\n              lng: longitude\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.address = results[0].formatted_address;\n          if (results[0]?.address_components.length) {\n            const city = results[0].address_components.find(item => item.types.includes('locality'));\n            this.addressDetailCity = city.long_name;\n          }\n          this.addressForm.patchValue({\n            streetAddress: this.address,\n            country: results[results.length - 1].formatted_address\n            // city: results[results.length - 3].formatted_address,\n          });\n\n          this.validate();\n          this.getCoordinates();\n          this.cd.detectChanges();\n        } else {\n          if (isPlatformBrowser(this.platformId)) {\n            window.alert('No results found');\n          }\n        }\n      } else {\n        if (isPlatformBrowser(this.platformId)) {\n          window.alert('Geocoder failed due to: ' + status);\n        }\n      }\n    });\n  }\n  clear() {\n    this.searchElementRef.nativeElement.value = '';\n  }\n  onSubmit() {\n    this.addressForm.patchValue({\n      Lat: this.Lat ? this.Lat.toString() : '',\n      Lng: this.Lng ? this.Lng.toString() : ''\n      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\n    });\n\n    this.loaderService.show();\n    if (this.addressForm.value.postcode == '') this.addressForm.value.postcode = 0;\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      if (this.addressForm.value.postcode === \"\") delete this.addressForm.value.postcode;\n      this.addressService.addAddress(formValue).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.loaderService.hide();\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully');\n          } else {\n            this.loaderService.hide();\n            this.messageService.add({\n              severity: 'error',\n              summary: res?.message\n            });\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  onConfrim() {\n    this.isDisplaySuccessModal = false;\n    if (this.routeToCheckOut) {\n      this.router.navigateByUrl(\"/checkout/selectAddress\");\n    } else {\n      this.router.navigate(['/account/address']);\n    }\n  }\n  Update() {\n    if (this.Lat?.toString() === \"\" || this.Lng?.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.addressIsRequired')\n      });\n      return;\n    }\n    this.addressForm.patchValue({\n      Lat: this.Lat?.toString(),\n      Lng: this.Lng?.toString(),\n      Id: this.addressService.chosenAddress.id\n    });\n    this.loaderService.show();\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      this.addressService.updateAddress(formValue).subscribe({\n        next: res => {\n          this.loaderService.hide();\n          if (!res.success) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.address'),\n              detail: this.translate.instant(res.message)\n            });\n          } else {\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  OnlyNumeric(val) {\n    if (!Number(val.value)) {\n      this.addressForm.value.postcode = '';\n      this.addressForm.value.phone = '';\n    }\n    return false;\n  }\n  checkPlaceHolder() {\n    if (this.myplaceHolder) {\n      this.myplaceHolder = '';\n    } else {\n      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\n      if (this.myplaceHolder) this.myplaceHolder = this.myplaceHolder + ' 000 000 000';else this.myplaceHolder = '256 000 000 000';\n    }\n  }\n  validate() {\n    if (!this.addressForm.valid) return true;\n  }\n  setAsDefault() {\n    this.addressService.setDefault(this.id).subscribe({\n      next: res => {\n        this.messageService.add({\n          severity: 'success',\n          summary: this.translate.instant('ResponseMessages.address'),\n          detail: this.translate.instant('ResponseMessages.defaultAddressSuccessfully')\n        });\n        if (this.redirectUrl && this.redirectUrl !== '') {\n          this.router.navigate([this.redirectUrl]);\n        } else {\n          this.router.navigate(['/account/address']);\n        }\n      },\n      error: err => {\n        this.loaderService.hide();\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err\n        });\n      }\n    });\n  }\n  setCurrentLocation(selectedPosition) {\n    this.addressService.chosenAddress = null;\n    if ('geolocation' in navigator) {\n      if (!selectedPosition) {\n        navigator.geolocation.getCurrentPosition(position => {\n          this.Lat = position.coords.latitude;\n          this.Lng = position.coords.longitude;\n          this.position = [{\n            position: {\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.getAddress(this.Lat, this.Lng);\n          this.createLocationButton();\n        });\n      } else {\n        this.Lat = parseFloat(selectedPosition.lat);\n        this.Lng = parseFloat(selectedPosition.lng);\n        this.position = [{\n          position: {\n            lat: this.Lat,\n            lng: this.Lng\n          }\n        }];\n        this.getAddress(this.Lat, this.Lng);\n        this.createLocationButton();\n      }\n    }\n  }\n  mapClicked(event) {\n    let latLng = JSON.parse(JSON.stringify(event.latLng));\n    this.Lat = latLng.lat;\n    this.Lng = latLng.lng;\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.center = this.position[0].position;\n    this.zoom = 12;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  createLocationButton() {\n    if (isPlatformBrowser(this.platformId)) {\n      const controlDiv = document.createElement('div');\n      controlDiv.index = 100;\n      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\n    }\n  }\n  markerDragEnd(event) {\n    if (event.latLng != null) {\n      const latLng = event.latLng.toJSON();\n      ;\n      this.getAddress(latLng.lat, latLng.lng);\n    }\n  }\n  getCities() {\n    const reqObj = {\n      currentPage: 1,\n      pageSize: 5,\n      ignorePagination: true\n    };\n    this.addressService.getAllCities(reqObj).subscribe(res => {\n      if (res.success) {\n        this.allCities = res.data.records;\n      }\n    });\n  }\n  getCoordinates() {\n    var geocoder = new google.maps.Geocoder();\n    this.isDifferentCity = true;\n    geocoder.geocode({\n      'address': this.addressForm.controls['streetAddress'].value\n    }, (results, status) => {\n      if (status == google.maps.GeocoderStatus.OK) {\n        if (results[0].address_components.length) {\n          const city = results[0].address_components.find(item => item.types.includes('locality'));\n          if (city.long_name === this.addressDetailCity) {\n            this.isDifferentCity = false;\n            this.cd.detectChanges();\n          }\n        }\n      }\n    });\n  }\n  getAllRegion() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let reqObj = {\n        currentPage: 1,\n        pageSize: 5,\n        ignorePagination: true\n      };\n      try {\n        const res = yield _this2.addressService.getAllRegions(reqObj).toPromise();\n        if (res.success) {\n          _this2.allRegionList = res.data.records;\n          _this2.filteredCities = res.data.records[0].cities;\n        }\n      } catch (error) {\n        _this2.messageService.add({\n          severity: 'error',\n          summary: _this2.translate.instant('ErrorMessages.fetchError')\n        });\n      }\n    })();\n  }\n  filterCitiesByRegion(regionId) {\n    const selectedRegion = this.allRegionList.find(region => region.id === regionId);\n    if (selectedRegion) {\n      this.filteredCities = selectedRegion?.cities;\n    }\n  }\n  selectAddressType(type) {\n    this.selectedAddressType = type;\n    this.addressForm.patchValue({\n      addressLabel: type\n    });\n    if (this.selectedAddressType == 'Other') {\n      this.isDisplayOtherModal = true;\n    }\n  }\n  addAddress(event) {\n    if (event) {\n      this.addressForm.patchValue({\n        addressLabel: event\n      });\n      this.addressLabelList[2].name = event;\n    }\n    this.isDisplayOtherModal = false;\n  }\n  onAddressDialogCancel(event) {\n    this.isDisplayOtherModal = event;\n  }\n  get region() {\n    return this.addressForm.get('region');\n  }\n};\n__decorate([ViewChild(\"placesRef\")], AddressComponent.prototype, \"placesRef\", void 0);\n__decorate([ViewChild('search')], AddressComponent.prototype, \"searchElementRef\", void 0);\n__decorate([HostListener('window:resize', ['$event'])], AddressComponent.prototype, \"onResize\", null);\nAddressComponent = __decorate([Component({\n  selector: 'app-address',\n  templateUrl: './address.component.html',\n  styleUrls: ['./address.component.scss']\n}), __param(14, Inject(PLATFORM_ID))], AddressComponent);\nexport { AddressComponent };", "map": {"version": 3, "names": ["Component", "HostListener", "Inject", "PLATFORM_ID", "ViewChild", "UntypedFormControl", "UntypedFormGroup", "Validators", "isPlatformBrowser", "CountryISO", "PhoneNumberFormat", "SearchCountryField", "TenantRecords", "AddressComponent", "onResize", "event", "platformId", "screenWidth", "window", "innerWidth", "constructor", "ngZone", "addressService", "messageService", "router", "store", "route", "translate", "mainDataService", "loaderService", "_location", "cd", "permissionService", "appDataService", "$gtmService", "markerOptions", "draggable", "icon", "phoneLength", "landMarkAddressRequired", "isMobileTemplate", "isDisplaySuccessModal", "isDisplayOtherModal", "message", "addressDetailCity", "addressForm", "addressLabel", "receiverFirstName", "receiverLastName", "streetAddress", "required", "country", "city", "landMark", "deliveryInstructions", "buldingNumber", "postcode", "receiverPhoneNumber", "geo_location", "Lat", "Lng", "Id", "additional<PERSON>ddress", "region", "id", "regionName", "search", "isDefault", "center", "lat", "lng", "mapOptions", "fullscreenControl", "disableDefaultUI", "allCities", "isDifferentCity", "addressLabelList", "geoCoder", "google", "maps", "Geocoder", "borderBottomStyle", "phoneInputLength", "preferredCountries", "Uganda", "Ghana", "CôteDIvoire", "customPlaceHolder", "allRegionList", "filteredCities", "selectedCitiesValue", "hasPermission", "source", "getCurrentNavigation", "extras", "state", "tenantId", "localStorage", "getItem", "onBack", "back", "ngOnInit", "queryParamMap", "subscribe", "queryParams", "routeToCheckOut", "get", "navbarData", "layoutTemplate", "find", "section", "type", "history", "params", "console", "log", "redirectUrl", "returnUrl", "routeSub", "getCustomerAddress", "pushPageView", "defaultPhoneNumber", "countryPhone", "replace", "phoneNumberWithoutCode", "setValue", "mark<PERSON>llAsTouched", "getAllRegion", "setCurrentLocation", "toString", "landMarkAddress", "controls", "setValidators", "updateValueAndValidity", "parseInt", "userDetails", "setUserData", "name", "split", "patchValue", "tenants", "records", "undefined", "data", "arr", "element", "setItem", "isoCode", "set", "CustomCountryISO", "configuration", "item", "key", "value", "valueChanges", "selectedRegion", "r", "emitEvent", "mapInitialize", "map", "handleAddressChange", "place", "geometry", "location", "position", "zoom", "get<PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "show", "res", "getAddressById", "to<PERSON>romise", "loadedAddress", "<PERSON><PERSON><PERSON><PERSON>", "trimmedPhoneNumber", "trimPhoneNumber", "selectedCity", "postCode", "cityName", "selectedAddressType", "parseFloat", "searchElementRef", "nativeElement", "createLocationButton", "hide", "err", "add", "severity", "summary", "instant", "detail", "phoneNumber", "length", "substring", "filterCitiesByRegion", "cities", "latitude", "longitude", "geocode", "results", "status", "address", "formatted_address", "address_components", "types", "includes", "long_name", "validate", "getCoordinates", "detectChanges", "alert", "clear", "onSubmit", "valid", "formValue", "e164Number", "slice", "addAddress", "next", "success", "error", "onConfrim", "navigateByUrl", "navigate", "Update", "updateAddress", "OnlyNumeric", "val", "Number", "phone", "checkPlaceHolder", "myplaceHolder", "setAsDefault", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "navigator", "geolocation", "getCurrentPosition", "coords", "mapClicked", "latLng", "JSON", "parse", "stringify", "controlDiv", "document", "createElement", "index", "ControlPosition", "RIGHT_BOTTOM", "push", "markerDragEnd", "toJSON", "getCities", "req<PERSON>bj", "currentPage", "pageSize", "ignorePagination", "getAllCities", "geocoder", "GeocoderStatus", "OK", "_this2", "getAllRegions", "regionId", "selectAddressType", "onAddressDialogCancel", "__decorate", "selector", "templateUrl", "styleUrls", "__param"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\address\\address.component.ts"], "sourcesContent": ["import {\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef,\r\n  HostListener,\r\n  Inject,\r\n  <PERSON><PERSON><PERSON>,\r\n  OnInit,\r\n  PLATFORM_ID,\r\n  ViewChild,\r\n} from '@angular/core';\r\n\r\nimport {UntypedFormControl, UntypedFormGroup, Validators,} from '@angular/forms';\r\nimport {MessageService} from 'primeng/api';\r\nimport {ActivatedRoute, Router} from '@angular/router';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {NgxGpAutocompleteDirective} from \"@angular-magic/ngx-gp-autocomplete\";\r\n\r\nimport {\r\n  LoaderService,\r\n  AddressService,\r\n  StoreService,\r\n  MainDataService,\r\n  AppDataService,\r\n  PermissionService\r\n} from \"@core/services\";\r\n\r\ndeclare const google: any;\r\nimport {isPlatformBrowser, Location} from '@angular/common';\r\nimport {CountryISO, PhoneNumberFormat, SearchCountryField} from \"ngx-intl-tel-input-gg\";\r\nimport {TenantRecords} from \"@core/interface\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-address',\r\n  templateUrl: './address.component.html',\r\n  styleUrls: ['./address.component.scss'],\r\n})\r\nexport class AddressComponent implements OnInit {\r\n  routeToCheckOut:any;\r\n  markerOptions: any = { draggable: true,icon:'assets/images/map-pin.svg' };\r\n  latitude?: number;\r\n  longitude?: number;\r\n  phoneLength: number = 13;\r\n  position: any\r\n  landMarkAddressRequired: boolean = false;\r\n  isMobileTemplate:boolean=false;\r\n  isDisplaySuccessModal: boolean = false;\r\n  isDisplayOtherModal:boolean=false;\r\n  message: string ='';\r\n  addressDetailCity : string = '';\r\n  @ViewChild(\"placesRef\") placesRef: NgxGpAutocompleteDirective;\r\n\r\n  addressForm = new UntypedFormGroup({\r\n    addressLabel: new UntypedFormControl('Home'),\r\n    receiverFirstName: new UntypedFormControl(''),\r\n    receiverLastName: new UntypedFormControl(''),\r\n    streetAddress: new UntypedFormControl('', Validators.required),\r\n    country: new UntypedFormControl('', Validators.required),\r\n    city: new UntypedFormControl('', Validators.required),\r\n    landMark: new UntypedFormControl('', Validators.required ),\r\n    deliveryInstructions: new UntypedFormControl(''),\r\n    buldingNumber: new UntypedFormControl(''),\r\n    postcode: new UntypedFormControl(''),\r\n    receiverPhoneNumber: new UntypedFormControl('', Validators.required),\r\n    geo_location: new UntypedFormControl(''),\r\n    Lat: new UntypedFormControl(''),\r\n    Lng: new UntypedFormControl(''),\r\n    Id: new UntypedFormControl(''),\r\n    additionalAddress:new UntypedFormControl(''),\r\n    region: new UntypedFormGroup({\r\n      id: new UntypedFormControl(''),\r\n      regionName: new UntypedFormControl('')\r\n    },Validators.required)\r\n  });\r\n  search: string = '';\r\n  Lat!: number;\r\n  Lng!: number;\r\n  zoom!: number;\r\n  address!: string;\r\n  myplaceHolder: string | undefined;\r\n  source: any;\r\n  isDefault = false;\r\n  navbarData:any;\r\n  center: google.maps.LatLngLiteral = {\r\n    lat: 0.3,\r\n    lng: 32.5\r\n  };\r\n  mapOptions = {\r\n    fullscreenControl: false,\r\n    disableDefaultUI: true\r\n  };\r\n  allCities : any = [];\r\n  isDifferentCity: boolean = false;\r\n  public addressLabelList = [\r\n    {'name': 'Home', 'id': 1},{'name': 'Work', 'id': 2},{'name': 'Other', 'id': 3}\r\n\r\n  ]\r\n  selectedAddressType: any;\r\n  @ViewChild('search')\r\n  public searchElementRef!: ElementRef;\r\n  id: string = '';\r\n  routeSub: any;\r\n  private geoCoder: any = new google.maps.Geocoder();\r\n  private redirectUrl: string;\r\n  map: google.maps.Map;\r\n  borderBottomStyle = '2px solid red !important';\r\n  phoneInputLength: number = 12;\r\n  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\r\n  CustomCountryISO: any;\r\n  customPlaceHolder: any = '';\r\n  PhoneNumberFormat = PhoneNumberFormat;\r\n  SearchCountryField = SearchCountryField;\r\n  allRegionList : any = [];\r\n  filteredCities: any[] = [];\r\n  screenWidth:any=window.innerWidth;\r\n  lat:string;\r\n  lng:string\r\n  selectedCitiesValue : any = []\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n  constructor(\r\n    private ngZone: NgZone,\r\n    public addressService: AddressService,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private store: StoreService,\r\n    private route: ActivatedRoute,\r\n    private translate: TranslateService,\r\n    private mainDataService: MainDataService,\r\n    private loaderService: LoaderService,\r\n     private _location: Location,\r\n    private cd : ChangeDetectorRef,\r\n    private permissionService: PermissionService,\r\n    private appDataService: AppDataService,\r\n    private $gtmService:GTMService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n  ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.source = this.router.getCurrentNavigation()?.extras?.state;\r\n\r\n    let tenantId = localStorage.getItem('tenantId');\r\n    if(tenantId && tenantId !== '') {\r\n      if(tenantId == '1') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      } else if(tenantId == '2') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      }else if(tenantId == '3') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      }else if(tenantId == '4') {\r\n        this.customPlaceHolder = 'XXXXXXXXXX';\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  onBack(){\r\n    this._location.back();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.route.queryParamMap.subscribe((queryParams) => {\r\n      this.routeToCheckOut = queryParams.get(\"checkout\");\r\n    });\r\n\r\n    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n    this.lat=history.state.lat;\r\n    this.lng=history.state.lng;\r\n    this.route.queryParams.subscribe((params: any) => {\r\n      console.log('parr',params)\r\n\r\n      this.redirectUrl = params.returnUrl;\r\n    });\r\n\r\n    this.routeSub = this.route.params.subscribe((params) => {\r\n\r\n      this.id = params['id'];\r\n      if (this.id != 'add-address'){\r\n        this.getCustomerAddress();\r\n        this.$gtmService.pushPageView('Your Addresses', 'Update Address')\r\n      }\r\n      else{\r\n        this.$gtmService.pushPageView('Your Addresses', 'Add Address')\r\n        const defaultPhoneNumber = localStorage.getItem('userPhone');\r\n        const countryPhone = localStorage.getItem('CountryPhone')?.replace('+','') || '';\r\n        const phoneNumberWithoutCode = defaultPhoneNumber?.replace(countryPhone,'')\r\n        this.addressForm.get('receiverPhoneNumber')?.setValue(phoneNumberWithoutCode)\r\n        this.addressForm.get('receiverPhoneNumber')?.markAllAsTouched();\r\n\r\n         this.getAllRegion();\r\n        if(this.lat && this.lng) {\r\n          this.setCurrentLocation({lat: this.lat, lng: this.lng});\r\n        }\r\n        else{\r\n          this.setCurrentLocation();\r\n        }\r\n\r\n      }\r\n    });\r\n\r\n    let phoneLength = localStorage.getItem('PhoneLength')?.toString();\r\n    let landMarkAddress = localStorage\r\n      .getItem('customerAddressLandmarkRequired')\r\n      ?.toString();\r\n    if (landMarkAddress && landMarkAddress == 'True') {\r\n      this.landMarkAddressRequired = true;\r\n      this.addressForm.controls['landMark'].setValidators([\r\n        Validators.required,\r\n      ]);\r\n      this.addressForm.controls['landMark'].updateValueAndValidity();\r\n    }\r\n    if (phoneLength) {\r\n      this.phoneLength = parseInt(phoneLength) - 2;\r\n    }\r\n    let userDetails = this.store.get('profile');\r\n    this.mainDataService.setUserData(userDetails);\r\n    let name = userDetails?.name?.split(' ');\r\n    this.addressForm.patchValue({\r\n      receiverFirstName: name[0] ? name[0] : '',\r\n      receiverLastName: name[1] ? name[1] : '',\r\n      // receiverPhoneNumber: userDetails.mobileNumber,\r\n    });\r\n\r\n\r\n    if (!localStorage.getItem(\"isoCode\")) {\r\n\r\n      const tenants = this.appDataService.tenants\r\n      if (tenants.records != undefined) {\r\n        let tenantId = localStorage.getItem('tenantId');\r\n        let data = tenants.records;\r\n        let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();\r\n        localStorage.setItem('isoCode', arr?.isoCode);\r\n        this.store.set('allCountryTenants', tenants.records);\r\n      }\r\n\r\n    } else {\r\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\r\n    }\r\n      if(this.appDataService.configuration) {\r\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')\r\n        if(phoneLength) this.phoneInputLength = parseInt(phoneLength.value)\r\n      }\r\n    this.filteredCities = this.allCities;\r\n      this.region.valueChanges.subscribe(value=>{\r\n       const selectedRegion = this.allRegionList?.find((r: any) => r.id == value.id)\r\n        this.addressForm.patchValue({\r\n          region: {\r\n            id: selectedRegion.id,\r\n            regionName: selectedRegion.regionName\r\n          }\r\n        },{emitEvent: false});\r\n      })    \r\n  }\r\n  mapInitialize(map: any) {\r\n    this.map = map;\r\n  }\r\n  public handleAddressChange(place: any) {\r\n    this.Lat = place.geometry.location.lat();\r\n    this.Lng = place.geometry.location.lng();\r\n    this.position = [{\r\n      position: {lat: this.Lat, lng: this.Lng}\r\n    }]\r\n    this.zoom = 12;\r\n    this.center = this.position[0].position\r\n    this.getAddress(this.Lat, this.Lng)\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.routeSub.unsubscribe();\r\n  }\r\n\r\n  async getCustomerAddress() {\r\n    this.loaderService.show();\r\n\r\n    try {\r\n      const res: any = await this.addressService.getAddressById(this.id).toPromise();\r\n      this.addressService.loadedAddress = true;\r\n      if (res.data) {\r\n\r\n        this.isDefault = res.data.isDefault;\r\n        this.addressService.chosenAddress = res.data;\r\n        const trimmedPhoneNumber = this.trimPhoneNumber(res.data.receiverPhoneNumber);\r\n        await this.getAllRegion();\r\n        const selectedRegion = this.selectedRegion(res.data.region);\r\n        const selectedCity = this.selectedCity(res.data.city)\r\n\r\n        this.addressForm.patchValue({\r\n          addressLabel: res.data.addressLabel,\r\n          receiverFirstName: res.data.receiverFirstName,\r\n          receiverLastName: res.data.receiverLastName,\r\n          postcode: res.data.postCode,\r\n          receiverPhoneNumber: trimmedPhoneNumber,\r\n          landMark: res.data.landMark,\r\n          deliveryInstructions: res.data.deliveryInstructions,\r\n          buldingNumber: res.data.buldingNumber,\r\n          additionalAddress: res.data.additionalAddress,\r\n          city: selectedCity.cityName,\r\n          region: selectedRegion,\r\n        });\r\n\r\n\r\n        if(res.data.addressLabel!='Home' && res.data.addressLabel!='Work' ) {\r\n          this.addressLabelList[2].name = res.data.addressLabel;\r\n        }\r\n        this.selectedAddressType= res.data.addressLabel;\r\n        this.Lat = parseFloat(res.data.lat);\r\n        this.Lng = parseFloat(res.data.lng);\r\n        const streetAddress = res.data.streetAddress;\r\n        if(this.searchElementRef){\r\n          this.searchElementRef.nativeElement.value = streetAddress;\r\n        }\r\n\r\n        this.position = [{\r\n          position: { lat: this.Lat, lng: this.Lng }\r\n        }];\r\n        this.zoom = 8;\r\n        this.center = this.position[0].position;\r\n\r\n        if (res.data.streetAddress || res.data.country) {\r\n\r\n          this.addressForm.patchValue({\r\n            streetAddress: res.data.streetAddress,\r\n            country: res.data.country,\r\n          });\r\n        }else {\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n\r\n\r\n        }\r\n\r\n        this.createLocationButton();\r\n      } else {\r\n        this.setCurrentLocation();\r\n        this.createLocationButton();\r\n      }\r\n      this.loaderService.hide();\r\n    } catch (err: any) {\r\n      this.addressService.loadedAddress = true;\r\n      this.setCurrentLocation();\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n        detail: err.message,\r\n      });\r\n    } finally {\r\n      this.loaderService.hide();\r\n    }\r\n  }\r\n\r\n  private trimPhoneNumber(phoneNumber: string): string {\r\n    return phoneNumber.length > 3 ? phoneNumber.substring(3) : phoneNumber;\r\n  }\r\n  private selectedRegion(regionName: string): { id: number; regionName: null } {\r\n\r\n    const region = this.allRegionList.find((r: any) => r.regionName === regionName);\r\n    if (region) {\r\n      this.filterCitiesByRegion(region.id)\r\n      this.selectedCitiesValue =  region.cities\r\n      return { id: region.id, regionName: region.regionName };\r\n    }\r\n    return { \"id\": -1, \"regionName\": null };\r\n\r\n  }\r\n  private selectedCity(cityName: string): { id: number; cityName: null } {\r\n    const city = this.selectedCitiesValue.find((r: any) => r.cityName === cityName);\r\n    if (city) {\r\n      return { id: city.id, cityName: city.cityName };\r\n    }\r\n    return { \"id\": -1, \"cityName\": null };\r\n  }\r\n\r\n\r\n  getAddress(latitude: number, longitude: number) {\r\n\r\n    this.geoCoder.geocode(\r\n      {location: {lat: latitude, lng: longitude}},\r\n      (results: { formatted_address: string, address_components: any }[], status: string) => {\r\n        if (status === 'OK') {\r\n          if (results[0]) {\r\n\r\n            this.position = [{\r\n              position: {lat: latitude, lng: longitude}\r\n            }]\r\n            this.center = this.position[0].position;\r\n            this.zoom = 12;\r\n            this.address = results[0].formatted_address;\r\n            if(results[0]?.address_components.length){\r\n              const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))\r\n              this.addressDetailCity = city.long_name;\r\n\r\n            }\r\n            this.addressForm.patchValue({\r\n              streetAddress: this.address,\r\n              country: results[results.length - 1].formatted_address,\r\n              // city: results[results.length - 3].formatted_address,\r\n            });\r\n            this.validate();\r\n            this.getCoordinates();\r\n            this.cd.detectChanges();\r\n          } else {\r\n            if (isPlatformBrowser(this.platformId)) {\r\n              window.alert('No results found');\r\n            }\r\n          }\r\n        } else {\r\n          if (isPlatformBrowser(this.platformId)) {\r\n            window.alert('Geocoder failed due to: ' + status);\r\n\r\n          }\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.searchElementRef.nativeElement.value = '';\r\n\r\n  }\r\n\r\n  onSubmit() {\r\n\r\n    this.addressForm.patchValue({\r\n      Lat: this.Lat ? this.Lat.toString() : '',\r\n      Lng: this.Lng ? this.Lng.toString() : '',\r\n      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\r\n    });\r\n\r\n\r\n    this.loaderService.show();\r\n    if (this.addressForm.value.postcode == '')\r\n      this.addressForm.value.postcode = 0;\r\n\r\n    if (this.addressForm.valid) {\r\n      const formValue = {\r\n        ...this.addressForm.value,\r\n        region: this.addressForm.value.region.regionName, // Send regionName only\r\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\r\n      };\r\n      if (this.addressForm.value.postcode === \"\") delete this.addressForm.value.postcode;\r\n      this.addressService.addAddress(formValue).subscribe({\r\n        next: (res: any) => {\r\n          if(res?.success){\r\n            this.loaderService.hide();\r\n            this.isDisplaySuccessModal = true;\r\n            this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully')\r\n          } else{\r\n            this.loaderService.hide();\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: res?.message,\r\n            });\r\n          }\r\n        \r\n        },\r\n        error: (err: any) => {\r\n\r\n          this.loaderService.hide();\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: this.translate.instant('ErrorMessages.fetchError'),\r\n            detail: err,\r\n          });\r\n        },\r\n      });\r\n    }\r\n  }\r\n  onConfrim(){\r\n    this.isDisplaySuccessModal = false;\r\n    if(this.routeToCheckOut){\r\n        this.router.navigateByUrl(\"/checkout/selectAddress\");\r\n    } else {\r\n        this.router.navigate(['/account/address']);\r\n    }\r\n  }\r\n  Update() {\r\n    if (this.Lat?.toString() === \"\" || this.Lng?.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.addressIsRequired'),\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.addressForm.patchValue({\r\n      Lat: this.Lat?.toString(),\r\n      Lng: this.Lng?.toString(),\r\n      Id: this.addressService.chosenAddress.id,\r\n    });\r\n\r\n    this.loaderService.show();\r\n\r\n    if (this.addressForm.valid) {\r\n      const formValue = {\r\n        ...this.addressForm.value,\r\n        region: this.addressForm.value.region.regionName,\r\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\r\n      };\r\n\r\n      this.addressService.updateAddress(formValue).subscribe({\r\n        next: (res: any) => {\r\n          this.loaderService.hide();\r\n          if (!res.success) {\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: this.translate.instant('ResponseMessages.address'),\r\n              detail: this.translate.instant(res.message),\r\n            });\r\n          } else {\r\n            this.isDisplaySuccessModal = true;\r\n            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          this.loaderService.hide();\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: this.translate.instant('ErrorMessages.fetchError'),\r\n            detail: err,\r\n          });\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  OnlyNumeric(val: any): boolean {\r\n    if (!Number(val.value)) {\r\n      this.addressForm.value.postcode = '';\r\n      this.addressForm.value.phone = '';\r\n    }\r\n    return false;\r\n  }\r\n\r\n  checkPlaceHolder() {\r\n    if (this.myplaceHolder) {\r\n      this.myplaceHolder = '';\r\n    } else {\r\n      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\r\n      if (this.myplaceHolder)\r\n        this.myplaceHolder = this.myplaceHolder + ' 000 000 000';\r\n      else this.myplaceHolder = '256 000 000 000';\r\n    }\r\n  }\r\n\r\n  validate() {\r\n    if (!this.addressForm.valid) return true;\r\n  }\r\n\r\n  setAsDefault() {\r\n    this.addressService.setDefault(this.id).subscribe({\r\n      next: (res: any) => {\r\n        this.messageService.add({\r\n          severity: 'success',\r\n          summary: this.translate.instant('ResponseMessages.address'),\r\n          detail: this.translate.instant(\r\n            'ResponseMessages.defaultAddressSuccessfully'\r\n          ),\r\n        });\r\n        if(this.redirectUrl && this.redirectUrl !== '') {\r\n          this.router.navigate([this.redirectUrl])\r\n        } else {\r\n          this.router.navigate(['/account/address']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.loaderService.hide();\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          detail: err,\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  private setCurrentLocation(selectedPosition?:any) {\r\n    this.addressService.chosenAddress = null;\r\n    if ('geolocation' in navigator) {\r\n      if(!selectedPosition) {\r\n        navigator.geolocation.getCurrentPosition((position) => {\r\n          this.Lat = position.coords.latitude;\r\n          this.Lng = position.coords.longitude;\r\n          this.position = [{\r\n            position: {lat: this.Lat, lng: this.Lng}\r\n          }]\r\n          this.center = this.position[0].position\r\n          this.zoom = 12;\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n          this.createLocationButton();\r\n        });\r\n      }\r\n      else {\r\n\r\n          this.Lat = parseFloat(selectedPosition.lat);\r\n          this.Lng = parseFloat(selectedPosition.lng);\r\n          this.position = [{\r\n            position: { lat: this.Lat, lng: this.Lng }\r\n          }];\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n          this.createLocationButton();\r\n\r\n      }\r\n    }\r\n\r\n  }\r\n  mapClicked(event : any){\r\n    let latLng = JSON.parse(JSON.stringify(event.latLng));\r\n    this.Lat = latLng.lat;\r\n    this.Lng =latLng.lng\r\n    this.position = [{\r\n      position: {lat: this.Lat, lng: this.Lng}\r\n    }]\r\n    this.center = this.position[0].position\r\n    this.zoom = 12;\r\n    this.getAddress(this.Lat, this.Lng);\r\n  }\r\n  createLocationButton() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const controlDiv : any = document.createElement('div');\r\n      controlDiv.index = 100;\r\n      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\r\n    }\r\n  }\r\n  markerDragEnd(event: any) {\r\n    if (event.latLng != null) {\r\n      const latLng = event.latLng.toJSON();\r\n;\r\n      this.getAddress(latLng.lat, latLng.lng);\r\n    }\r\n  }\r\n  getCities(){\r\n    const reqObj : any = {\r\n      currentPage : 1,\r\n      pageSize:5,\r\n      ignorePagination : true\r\n    }\r\n    this.addressService.getAllCities(reqObj).subscribe((res: any) => {\r\n      if(res.success){\r\n        this.allCities = res.data.records;\r\n      }\r\n    })\r\n  }\r\n  getCoordinates() {\r\n    var geocoder = new google.maps.Geocoder();\r\n    this.isDifferentCity = true;\r\n    geocoder.geocode({ 'address': this.addressForm.controls['streetAddress'].value },  (results: any, status: any) => {\r\n      if (status == google.maps.GeocoderStatus.OK) {\r\n        if(results[0].address_components.length){\r\n          const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))\r\n          if(city.long_name === this.addressDetailCity){\r\n           this.isDifferentCity = false;\r\n           this.cd.detectChanges();\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAllRegion() {\r\n    let reqObj = {\r\n      currentPage: 1,\r\n      pageSize: 5,\r\n      ignorePagination: true\r\n    };\r\n    try {\r\n      const res: any = await this.addressService.getAllRegions(reqObj).toPromise();\r\n      if (res.success) {\r\n        this.allRegionList = res.data.records;\r\n\r\n        this.filteredCities = res.data.records[0].cities\r\n      }\r\n    } catch (error) {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  filterCitiesByRegion(regionId: any): void {\r\n    const selectedRegion = this.allRegionList.find((region: { id: any; }) => region.id === regionId);\r\n    if (selectedRegion) {\r\n      this.filteredCities = selectedRegion?.cities\r\n    }\r\n  }\r\n  selectAddressType(type: string) {\r\n    this.selectedAddressType = type;\r\n    this.addressForm.patchValue({ addressLabel: type });\r\n    if(this.selectedAddressType=='Other'){\r\n      this.isDisplayOtherModal=true;\r\n    }\r\n  }\r\n\r\n  addAddress(event:any) {\r\n    if(event){\r\n      this.addressForm.patchValue({ addressLabel: event });\r\n      this.addressLabelList[2].name = event;\r\n    }\r\n    this.isDisplayOtherModal=false\r\n  }\r\n\r\n  onAddressDialogCancel(event:any) {\r\n    this.isDisplayOtherModal=event\r\n  }\r\n  get region() {\r\n    return (this.addressForm.get('region') as UntypedFormGroup)\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAEEA,SAAS,EAETC,YAAY,EACZC,MAAM,EAGNC,WAAW,EACXC,SAAS,QACJ,eAAe;AAEtB,SAAQC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAgBhF,SAAQC,iBAAiB,QAAiB,iBAAiB;AAC3D,SAAQC,UAAU,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAO,uBAAuB;AACvF,SAAQC,aAAa,QAAO,iBAAiB;AAStC,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAkF3BC,QAAQA,CAACC,KAAW;IAClB,IAAIP,iBAAiB,CAAC,IAAI,CAACQ,UAAU,CAAC,EAAE;MACtC,IAAI,CAACC,WAAW,GAAGC,MAAM,CAACC,UAAU;;EAExC;EACAC,YACUC,MAAc,EACfC,cAA8B,EAC7BC,cAA8B,EAC9BC,MAAc,EACdC,KAAmB,EACnBC,KAAqB,EACrBC,SAA2B,EAC3BC,eAAgC,EAChCC,aAA4B,EAC3BC,SAAmB,EACpBC,EAAsB,EACtBC,iBAAoC,EACpCC,cAA8B,EAC9BC,WAAsB,EACDlB,UAAe;IAdpC,KAAAK,MAAM,GAANA,MAAM;IACP,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,SAAS,GAATA,SAAS;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACU,KAAAlB,UAAU,GAAVA,UAAU;IApGzC,KAAAmB,aAAa,GAAQ;MAAEC,SAAS,EAAE,IAAI;MAACC,IAAI,EAAC;IAA2B,CAAE;IAGzE,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAAC,gBAAgB,GAAS,KAAK;IAC9B,KAAAC,qBAAqB,GAAY,KAAK;IACtC,KAAAC,mBAAmB,GAAS,KAAK;IACjC,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,iBAAiB,GAAY,EAAE;IAG/B,KAAAC,WAAW,GAAG,IAAIvC,gBAAgB,CAAC;MACjCwC,YAAY,EAAE,IAAIzC,kBAAkB,CAAC,MAAM,CAAC;MAC5C0C,iBAAiB,EAAE,IAAI1C,kBAAkB,CAAC,EAAE,CAAC;MAC7C2C,gBAAgB,EAAE,IAAI3C,kBAAkB,CAAC,EAAE,CAAC;MAC5C4C,aAAa,EAAE,IAAI5C,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAC;MAC9DC,OAAO,EAAE,IAAI9C,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAC;MACxDE,IAAI,EAAE,IAAI/C,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAC;MACrDG,QAAQ,EAAE,IAAIhD,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAE;MAC1DI,oBAAoB,EAAE,IAAIjD,kBAAkB,CAAC,EAAE,CAAC;MAChDkD,aAAa,EAAE,IAAIlD,kBAAkB,CAAC,EAAE,CAAC;MACzCmD,QAAQ,EAAE,IAAInD,kBAAkB,CAAC,EAAE,CAAC;MACpCoD,mBAAmB,EAAE,IAAIpD,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC2C,QAAQ,CAAC;MACpEQ,YAAY,EAAE,IAAIrD,kBAAkB,CAAC,EAAE,CAAC;MACxCsD,GAAG,EAAE,IAAItD,kBAAkB,CAAC,EAAE,CAAC;MAC/BuD,GAAG,EAAE,IAAIvD,kBAAkB,CAAC,EAAE,CAAC;MAC/BwD,EAAE,EAAE,IAAIxD,kBAAkB,CAAC,EAAE,CAAC;MAC9ByD,iBAAiB,EAAC,IAAIzD,kBAAkB,CAAC,EAAE,CAAC;MAC5C0D,MAAM,EAAE,IAAIzD,gBAAgB,CAAC;QAC3B0D,EAAE,EAAE,IAAI3D,kBAAkB,CAAC,EAAE,CAAC;QAC9B4D,UAAU,EAAE,IAAI5D,kBAAkB,CAAC,EAAE;OACtC,EAACE,UAAU,CAAC2C,QAAQ;KACtB,CAAC;IACF,KAAAgB,MAAM,GAAW,EAAE;IAOnB,KAAAC,SAAS,GAAG,KAAK;IAEjB,KAAAC,MAAM,GAA8B;MAClCC,GAAG,EAAE,GAAG;MACRC,GAAG,EAAE;KACN;IACD,KAAAC,UAAU,GAAG;MACXC,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE;KACnB;IACD,KAAAC,SAAS,GAAS,EAAE;IACpB,KAAAC,eAAe,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAG,CACxB;MAAC,MAAM,EAAE,MAAM;MAAE,IAAI,EAAE;IAAC,CAAC,EAAC;MAAC,MAAM,EAAE,MAAM;MAAE,IAAI,EAAE;IAAC,CAAC,EAAC;MAAC,MAAM,EAAE,OAAO;MAAE,IAAI,EAAE;IAAC,CAAC,CAE/E;IAID,KAAAZ,EAAE,GAAW,EAAE;IAEP,KAAAa,QAAQ,GAAQ,IAAIC,MAAM,CAACC,IAAI,CAACC,QAAQ,EAAE;IAGlD,KAAAC,iBAAiB,GAAG,0BAA0B;IAC9C,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,kBAAkB,GAAiB,CAAC1E,UAAU,CAAC2E,MAAM,EAAE3E,UAAU,CAAC4E,KAAK,EAAE5E,UAAU,CAAC6E,WAAW,CAAC;IAEhG,KAAAC,iBAAiB,GAAQ,EAAE;IAC3B,KAAA7E,iBAAiB,GAAGA,iBAAiB;IACrC,KAAAC,kBAAkB,GAAGA,kBAAkB;IACvC,KAAA6E,aAAa,GAAS,EAAE;IACxB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAxE,WAAW,GAAKC,MAAM,CAACC,UAAU;IAGjC,KAAAuE,mBAAmB,GAAS,EAAE;IAwB5B,IAAI,CAAClD,gBAAgB,GAAG,IAAI,CAACR,iBAAiB,CAAC2D,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACC,MAAM,GAAG,IAAI,CAACpE,MAAM,CAACqE,oBAAoB,EAAE,EAAEC,MAAM,EAAEC,KAAK;IAE/D,IAAIC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC/C,IAAGF,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAC9B,IAAGA,QAAQ,IAAI,GAAG,EAAE;QAClB,IAAI,CAACT,iBAAiB,GAAG,WAAW;OACrC,MAAM,IAAGS,QAAQ,IAAI,GAAG,EAAE;QACzB,IAAI,CAACT,iBAAiB,GAAG,WAAW;OACrC,MAAK,IAAGS,QAAQ,IAAI,GAAG,EAAE;QACxB,IAAI,CAACT,iBAAiB,GAAG,WAAW;OACrC,MAAK,IAAGS,QAAQ,IAAI,GAAG,EAAE;QACxB,IAAI,CAACT,iBAAiB,GAAG,YAAY;;;EAG3C;EAGAY,MAAMA,CAAA;IACJ,IAAI,CAACrE,SAAS,CAACsE,IAAI,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC3E,KAAK,CAAC4E,aAAa,CAACC,SAAS,CAAEC,WAAW,IAAI;MACjD,IAAI,CAACC,eAAe,GAAGD,WAAW,CAACE,GAAG,CAAC,UAAU,CAAC;IACpD,CAAC,CAAC;IAEF,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC1E,cAAc,CAAC2E,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IACtG,IAAI,CAAC1C,GAAG,GAAC2C,OAAO,CAACjB,KAAK,CAAC1B,GAAG;IAC1B,IAAI,CAACC,GAAG,GAAC0C,OAAO,CAACjB,KAAK,CAACzB,GAAG;IAC1B,IAAI,CAAC5C,KAAK,CAAC8E,WAAW,CAACD,SAAS,CAAEU,MAAW,IAAI;MAC/CC,OAAO,CAACC,GAAG,CAAC,MAAM,EAACF,MAAM,CAAC;MAE1B,IAAI,CAACG,WAAW,GAAGH,MAAM,CAACI,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC5F,KAAK,CAACuF,MAAM,CAACV,SAAS,CAAEU,MAAM,IAAI;MAErD,IAAI,CAACjD,EAAE,GAAGiD,MAAM,CAAC,IAAI,CAAC;MACtB,IAAI,IAAI,CAACjD,EAAE,IAAI,aAAa,EAAC;QAC3B,IAAI,CAACuD,kBAAkB,EAAE;QACzB,IAAI,CAACrF,WAAW,CAACsF,YAAY,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;OAClE,MACG;QACF,IAAI,CAACtF,WAAW,CAACsF,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAC9D,MAAMC,kBAAkB,GAAGxB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC5D,MAAMwB,YAAY,GAAGzB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAEyB,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,IAAI,EAAE;QAChF,MAAMC,sBAAsB,GAAGH,kBAAkB,EAAEE,OAAO,CAACD,YAAY,EAAC,EAAE,CAAC;QAC3E,IAAI,CAAC7E,WAAW,CAAC6D,GAAG,CAAC,qBAAqB,CAAC,EAAEmB,QAAQ,CAACD,sBAAsB,CAAC;QAC7E,IAAI,CAAC/E,WAAW,CAAC6D,GAAG,CAAC,qBAAqB,CAAC,EAAEoB,gBAAgB,EAAE;QAE9D,IAAI,CAACC,YAAY,EAAE;QACpB,IAAG,IAAI,CAAC1D,GAAG,IAAI,IAAI,CAACC,GAAG,EAAE;UACvB,IAAI,CAAC0D,kBAAkB,CAAC;YAAC3D,GAAG,EAAE,IAAI,CAACA,GAAG;YAAEC,GAAG,EAAE,IAAI,CAACA;UAAG,CAAC,CAAC;SACxD,MACG;UACF,IAAI,CAAC0D,kBAAkB,EAAE;;;IAI/B,CAAC,CAAC;IAEF,IAAI1F,WAAW,GAAG2D,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE+B,QAAQ,EAAE;IACjE,IAAIC,eAAe,GAAGjC,YAAY,CAC/BC,OAAO,CAAC,iCAAiC,CAAC,EACzC+B,QAAQ,EAAE;IACd,IAAIC,eAAe,IAAIA,eAAe,IAAI,MAAM,EAAE;MAChD,IAAI,CAAC3F,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAACM,WAAW,CAACsF,QAAQ,CAAC,UAAU,CAAC,CAACC,aAAa,CAAC,CAClD7H,UAAU,CAAC2C,QAAQ,CACpB,CAAC;MACF,IAAI,CAACL,WAAW,CAACsF,QAAQ,CAAC,UAAU,CAAC,CAACE,sBAAsB,EAAE;;IAEhE,IAAI/F,WAAW,EAAE;MACf,IAAI,CAACA,WAAW,GAAGgG,QAAQ,CAAChG,WAAW,CAAC,GAAG,CAAC;;IAE9C,IAAIiG,WAAW,GAAG,IAAI,CAAC9G,KAAK,CAACiF,GAAG,CAAC,SAAS,CAAC;IAC3C,IAAI,CAAC9E,eAAe,CAAC4G,WAAW,CAACD,WAAW,CAAC;IAC7C,IAAIE,IAAI,GAAGF,WAAW,EAAEE,IAAI,EAAEC,KAAK,CAAC,GAAG,CAAC;IACxC,IAAI,CAAC7F,WAAW,CAAC8F,UAAU,CAAC;MAC1B5F,iBAAiB,EAAE0F,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;MACzCzF,gBAAgB,EAAEyF,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG;MACtC;KACD,CAAC;;IAGF,IAAI,CAACxC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAEpC,MAAM0C,OAAO,GAAG,IAAI,CAAC3G,cAAc,CAAC2G,OAAO;MAC3C,IAAIA,OAAO,CAACC,OAAO,IAAIC,SAAS,EAAE;QAChC,IAAI9C,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAC/C,IAAI6C,IAAI,GAAGH,OAAO,CAACC,OAAO;QAC1B,IAAIG,GAAG,GAAkBD,IAAI,CAAClC,IAAI,CAAEoC,OAAY,IAAKA,OAAO,CAACjD,QAAQ,IAAIA,QAAQ,CAAC,IAAI,IAAIpF,aAAa,EAAE;QACzGqF,YAAY,CAACiD,OAAO,CAAC,SAAS,EAAEF,GAAG,EAAEG,OAAO,CAAC;QAC7C,IAAI,CAAC1H,KAAK,CAAC2H,GAAG,CAAC,mBAAmB,EAAER,OAAO,CAACC,OAAO,CAAC;;KAGvD,MAAM;MACL,IAAI,CAACQ,gBAAgB,GAAGpD,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;;IAEvD,IAAG,IAAI,CAACjE,cAAc,CAACqH,aAAa,EAAE;MACpC,MAAMhH,WAAW,GAAG,IAAI,CAACL,cAAc,CAACqH,aAAa,CAACT,OAAO,CAAChC,IAAI,CAAC0C,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAK,aAAa,CAAC;MACtG,IAAGlH,WAAW,EAAE,IAAI,CAAC4C,gBAAgB,GAAGoD,QAAQ,CAAChG,WAAW,CAACmH,KAAK,CAAC;;IAEvE,IAAI,CAAChE,cAAc,GAAG,IAAI,CAACf,SAAS;IAClC,IAAI,CAACX,MAAM,CAAC2F,YAAY,CAACnD,SAAS,CAACkD,KAAK,IAAE;MACzC,MAAME,cAAc,GAAG,IAAI,CAACnE,aAAa,EAAEqB,IAAI,CAAE+C,CAAM,IAAKA,CAAC,CAAC5F,EAAE,IAAIyF,KAAK,CAACzF,EAAE,CAAC;MAC5E,IAAI,CAACnB,WAAW,CAAC8F,UAAU,CAAC;QAC1B5E,MAAM,EAAE;UACNC,EAAE,EAAE2F,cAAc,CAAC3F,EAAE;UACrBC,UAAU,EAAE0F,cAAc,CAAC1F;;OAE9B,EAAC;QAAC4F,SAAS,EAAE;MAAK,CAAC,CAAC;IACvB,CAAC,CAAC;EACN;EACAC,aAAaA,CAACC,GAAQ;IACpB,IAAI,CAACA,GAAG,GAAGA,GAAG;EAChB;EACOC,mBAAmBA,CAACC,KAAU;IACnC,IAAI,CAACtG,GAAG,GAAGsG,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAAC9F,GAAG,EAAE;IACxC,IAAI,CAACT,GAAG,GAAGqG,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAAC7F,GAAG,EAAE;IACxC,IAAI,CAAC8F,QAAQ,GAAG,CAAC;MACfA,QAAQ,EAAE;QAAC/F,GAAG,EAAE,IAAI,CAACV,GAAG;QAAEW,GAAG,EAAE,IAAI,CAACV;MAAG;KACxC,CAAC;IACF,IAAI,CAACyG,IAAI,GAAG,EAAE;IACd,IAAI,CAACjG,MAAM,GAAG,IAAI,CAACgG,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;IACvC,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC3G,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;EACrC;EAEA2G,WAAWA,CAAA;IACT,IAAI,CAACjD,QAAQ,CAACkD,WAAW,EAAE;EAC7B;EAEMjD,kBAAkBA,CAAA;IAAA,IAAAkD,KAAA;IAAA,OAAAC,iBAAA;MACtBD,KAAI,CAAC5I,aAAa,CAAC8I,IAAI,EAAE;MAEzB,IAAI;QACF,MAAMC,GAAG,SAAcH,KAAI,CAACnJ,cAAc,CAACuJ,cAAc,CAACJ,KAAI,CAACzG,EAAE,CAAC,CAAC8G,SAAS,EAAE;QAC9EL,KAAI,CAACnJ,cAAc,CAACyJ,aAAa,GAAG,IAAI;QACxC,IAAIH,GAAG,CAAC7B,IAAI,EAAE;UAEZ0B,KAAI,CAACtG,SAAS,GAAGyG,GAAG,CAAC7B,IAAI,CAAC5E,SAAS;UACnCsG,KAAI,CAACnJ,cAAc,CAAC0J,aAAa,GAAGJ,GAAG,CAAC7B,IAAI;UAC5C,MAAMkC,kBAAkB,GAAGR,KAAI,CAACS,eAAe,CAACN,GAAG,CAAC7B,IAAI,CAACtF,mBAAmB,CAAC;UAC7E,MAAMgH,KAAI,CAAC1C,YAAY,EAAE;UACzB,MAAM4B,cAAc,GAAGc,KAAI,CAACd,cAAc,CAACiB,GAAG,CAAC7B,IAAI,CAAChF,MAAM,CAAC;UAC3D,MAAMoH,YAAY,GAAGV,KAAI,CAACU,YAAY,CAACP,GAAG,CAAC7B,IAAI,CAAC3F,IAAI,CAAC;UAErDqH,KAAI,CAAC5H,WAAW,CAAC8F,UAAU,CAAC;YAC1B7F,YAAY,EAAE8H,GAAG,CAAC7B,IAAI,CAACjG,YAAY;YACnCC,iBAAiB,EAAE6H,GAAG,CAAC7B,IAAI,CAAChG,iBAAiB;YAC7CC,gBAAgB,EAAE4H,GAAG,CAAC7B,IAAI,CAAC/F,gBAAgB;YAC3CQ,QAAQ,EAAEoH,GAAG,CAAC7B,IAAI,CAACqC,QAAQ;YAC3B3H,mBAAmB,EAAEwH,kBAAkB;YACvC5H,QAAQ,EAAEuH,GAAG,CAAC7B,IAAI,CAAC1F,QAAQ;YAC3BC,oBAAoB,EAAEsH,GAAG,CAAC7B,IAAI,CAACzF,oBAAoB;YACnDC,aAAa,EAAEqH,GAAG,CAAC7B,IAAI,CAACxF,aAAa;YACrCO,iBAAiB,EAAE8G,GAAG,CAAC7B,IAAI,CAACjF,iBAAiB;YAC7CV,IAAI,EAAE+H,YAAY,CAACE,QAAQ;YAC3BtH,MAAM,EAAE4F;WACT,CAAC;UAGF,IAAGiB,GAAG,CAAC7B,IAAI,CAACjG,YAAY,IAAE,MAAM,IAAI8H,GAAG,CAAC7B,IAAI,CAACjG,YAAY,IAAE,MAAM,EAAG;YAClE2H,KAAI,CAAC7F,gBAAgB,CAAC,CAAC,CAAC,CAAC6D,IAAI,GAAGmC,GAAG,CAAC7B,IAAI,CAACjG,YAAY;;UAEvD2H,KAAI,CAACa,mBAAmB,GAAEV,GAAG,CAAC7B,IAAI,CAACjG,YAAY;UAC/C2H,KAAI,CAAC9G,GAAG,GAAG4H,UAAU,CAACX,GAAG,CAAC7B,IAAI,CAAC1E,GAAG,CAAC;UACnCoG,KAAI,CAAC7G,GAAG,GAAG2H,UAAU,CAACX,GAAG,CAAC7B,IAAI,CAACzE,GAAG,CAAC;UACnC,MAAMrB,aAAa,GAAG2H,GAAG,CAAC7B,IAAI,CAAC9F,aAAa;UAC5C,IAAGwH,KAAI,CAACe,gBAAgB,EAAC;YACvBf,KAAI,CAACe,gBAAgB,CAACC,aAAa,CAAChC,KAAK,GAAGxG,aAAa;;UAG3DwH,KAAI,CAACL,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAE/F,GAAG,EAAEoG,KAAI,CAAC9G,GAAG;cAAEW,GAAG,EAAEmG,KAAI,CAAC7G;YAAG;WACzC,CAAC;UACF6G,KAAI,CAACJ,IAAI,GAAG,CAAC;UACbI,KAAI,CAACrG,MAAM,GAAGqG,KAAI,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UAEvC,IAAIQ,GAAG,CAAC7B,IAAI,CAAC9F,aAAa,IAAI2H,GAAG,CAAC7B,IAAI,CAAC5F,OAAO,EAAE;YAE9CsH,KAAI,CAAC5H,WAAW,CAAC8F,UAAU,CAAC;cAC1B1F,aAAa,EAAE2H,GAAG,CAAC7B,IAAI,CAAC9F,aAAa;cACrCE,OAAO,EAAEyH,GAAG,CAAC7B,IAAI,CAAC5F;aACnB,CAAC;WACH,MAAK;YAEJsH,KAAI,CAACH,UAAU,CAACG,KAAI,CAAC9G,GAAG,EAAE8G,KAAI,CAAC7G,GAAG,CAAC;;UAKrC6G,KAAI,CAACiB,oBAAoB,EAAE;SAC5B,MAAM;UACLjB,KAAI,CAACzC,kBAAkB,EAAE;UACzByC,KAAI,CAACiB,oBAAoB,EAAE;;QAE7BjB,KAAI,CAAC5I,aAAa,CAAC8J,IAAI,EAAE;OAC1B,CAAC,OAAOC,GAAQ,EAAE;QACjBnB,KAAI,CAACnJ,cAAc,CAACyJ,aAAa,GAAG,IAAI;QACxCN,KAAI,CAACzC,kBAAkB,EAAE;QACzByC,KAAI,CAAClJ,cAAc,CAACsK,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEtB,KAAI,CAAC9I,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEL,GAAG,CAACjJ;SACb,CAAC;OACH,SAAS;QACR8H,KAAI,CAAC5I,aAAa,CAAC8J,IAAI,EAAE;;IAC1B;EACH;EAEQT,eAAeA,CAACgB,WAAmB;IACzC,OAAOA,WAAW,CAACC,MAAM,GAAG,CAAC,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,GAAGF,WAAW;EACxE;EACQvC,cAAcA,CAAC1F,UAAkB;IAEvC,MAAMF,MAAM,GAAG,IAAI,CAACyB,aAAa,CAACqB,IAAI,CAAE+C,CAAM,IAAKA,CAAC,CAAC3F,UAAU,KAAKA,UAAU,CAAC;IAC/E,IAAIF,MAAM,EAAE;MACV,IAAI,CAACsI,oBAAoB,CAACtI,MAAM,CAACC,EAAE,CAAC;MACpC,IAAI,CAAC0B,mBAAmB,GAAI3B,MAAM,CAACuI,MAAM;MACzC,OAAO;QAAEtI,EAAE,EAAED,MAAM,CAACC,EAAE;QAAEC,UAAU,EAAEF,MAAM,CAACE;MAAU,CAAE;;IAEzD,OAAO;MAAE,IAAI,EAAE,CAAC,CAAC;MAAE,YAAY,EAAE;IAAI,CAAE;EAEzC;EACQkH,YAAYA,CAACE,QAAgB;IACnC,MAAMjI,IAAI,GAAG,IAAI,CAACsC,mBAAmB,CAACmB,IAAI,CAAE+C,CAAM,IAAKA,CAAC,CAACyB,QAAQ,KAAKA,QAAQ,CAAC;IAC/E,IAAIjI,IAAI,EAAE;MACR,OAAO;QAAEY,EAAE,EAAEZ,IAAI,CAACY,EAAE;QAAEqH,QAAQ,EAAEjI,IAAI,CAACiI;MAAQ,CAAE;;IAEjD,OAAO;MAAE,IAAI,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE;IAAI,CAAE;EACvC;EAGAf,UAAUA,CAACiC,QAAgB,EAAEC,SAAiB;IAE5C,IAAI,CAAC3H,QAAQ,CAAC4H,OAAO,CACnB;MAACtC,QAAQ,EAAE;QAAC9F,GAAG,EAAEkI,QAAQ;QAAEjI,GAAG,EAAEkI;MAAS;IAAC,CAAC,EAC3C,CAACE,OAAiE,EAAEC,MAAc,KAAI;MACpF,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnB,IAAID,OAAO,CAAC,CAAC,CAAC,EAAE;UAEd,IAAI,CAACtC,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAC/F,GAAG,EAAEkI,QAAQ;cAAEjI,GAAG,EAAEkI;YAAS;WACzC,CAAC;UACF,IAAI,CAACpI,MAAM,GAAG,IAAI,CAACgG,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UACvC,IAAI,CAACC,IAAI,GAAG,EAAE;UACd,IAAI,CAACuC,OAAO,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACG,iBAAiB;UAC3C,IAAGH,OAAO,CAAC,CAAC,CAAC,EAAEI,kBAAkB,CAACX,MAAM,EAAC;YACvC,MAAM/I,IAAI,GAAKsJ,OAAO,CAAC,CAAC,CAAC,CAACI,kBAAkB,CAACjG,IAAI,CAAE0C,IAAS,IAAKA,IAAI,CAACwD,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACjG,IAAI,CAACpK,iBAAiB,GAAGQ,IAAI,CAAC6J,SAAS;;UAGzC,IAAI,CAACpK,WAAW,CAAC8F,UAAU,CAAC;YAC1B1F,aAAa,EAAE,IAAI,CAAC2J,OAAO;YAC3BzJ,OAAO,EAAEuJ,OAAO,CAACA,OAAO,CAACP,MAAM,GAAG,CAAC,CAAC,CAACU;YACrC;WACD,CAAC;;UACF,IAAI,CAACK,QAAQ,EAAE;UACf,IAAI,CAACC,cAAc,EAAE;UACrB,IAAI,CAACpL,EAAE,CAACqL,aAAa,EAAE;SACxB,MAAM;UACL,IAAI5M,iBAAiB,CAAC,IAAI,CAACQ,UAAU,CAAC,EAAE;YACtCE,MAAM,CAACmM,KAAK,CAAC,kBAAkB,CAAC;;;OAGrC,MAAM;QACL,IAAI7M,iBAAiB,CAAC,IAAI,CAACQ,UAAU,CAAC,EAAE;UACtCE,MAAM,CAACmM,KAAK,CAAC,0BAA0B,GAAGV,MAAM,CAAC;;;IAIvD,CAAC,CACF;EACH;EAEAW,KAAKA,CAAA;IACH,IAAI,CAAC9B,gBAAgB,CAACC,aAAa,CAAChC,KAAK,GAAG,EAAE;EAEhD;EAEA8D,QAAQA,CAAA;IAEN,IAAI,CAAC1K,WAAW,CAAC8F,UAAU,CAAC;MAC1BhF,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACsE,QAAQ,EAAE,GAAG,EAAE;MACxCrE,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACqE,QAAQ,EAAE,GAAG;MACtC;KACD,CAAC;;IAGF,IAAI,CAACpG,aAAa,CAAC8I,IAAI,EAAE;IACzB,IAAI,IAAI,CAAC9H,WAAW,CAAC4G,KAAK,CAACjG,QAAQ,IAAI,EAAE,EACvC,IAAI,CAACX,WAAW,CAAC4G,KAAK,CAACjG,QAAQ,GAAG,CAAC;IAErC,IAAI,IAAI,CAACX,WAAW,CAAC2K,KAAK,EAAE;MAC1B,MAAMC,SAAS,GAAG;QAChB,GAAG,IAAI,CAAC5K,WAAW,CAAC4G,KAAK;QACzB1F,MAAM,EAAE,IAAI,CAAClB,WAAW,CAAC4G,KAAK,CAAC1F,MAAM,CAACE,UAAU;QAChDR,mBAAmB,EAAE,IAAI,CAACZ,WAAW,CAACsF,QAAQ,CAAC1E,mBAAmB,CAACgG,KAAK,CAACiE,UAAU,CAACC,KAAK,CAAC,CAAC;OAC5F;MACD,IAAI,IAAI,CAAC9K,WAAW,CAAC4G,KAAK,CAACjG,QAAQ,KAAK,EAAE,EAAE,OAAO,IAAI,CAACX,WAAW,CAAC4G,KAAK,CAACjG,QAAQ;MAClF,IAAI,CAAClC,cAAc,CAACsM,UAAU,CAACH,SAAS,CAAC,CAAClH,SAAS,CAAC;QAClDsH,IAAI,EAAGjD,GAAQ,IAAI;UACjB,IAAGA,GAAG,EAAEkD,OAAO,EAAC;YACd,IAAI,CAACjM,aAAa,CAAC8J,IAAI,EAAE;YACzB,IAAI,CAAClJ,qBAAqB,GAAG,IAAI;YACjC,IAAI,CAACE,OAAO,GAAG,IAAI,CAAChB,SAAS,CAACqK,OAAO,CAAC,2CAA2C,CAAC;WACnF,MAAK;YACJ,IAAI,CAACnK,aAAa,CAAC8J,IAAI,EAAE;YACzB,IAAI,CAACpK,cAAc,CAACsK,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,OAAO,EAAEnB,GAAG,EAAEjI;aACf,CAAC;;QAGN,CAAC;QACDoL,KAAK,EAAGnC,GAAQ,IAAI;UAElB,IAAI,CAAC/J,aAAa,CAAC8J,IAAI,EAAE;UACzB,IAAI,CAACpK,cAAc,CAACsK,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAEL;WACT,CAAC;QACJ;OACD,CAAC;;EAEN;EACAoC,SAASA,CAAA;IACP,IAAI,CAACvL,qBAAqB,GAAG,KAAK;IAClC,IAAG,IAAI,CAACgE,eAAe,EAAC;MACpB,IAAI,CAACjF,MAAM,CAACyM,aAAa,CAAC,yBAAyB,CAAC;KACvD,MAAM;MACH,IAAI,CAACzM,MAAM,CAAC0M,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAEhD;EACAC,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACxK,GAAG,EAAEsE,QAAQ,EAAE,KAAK,EAAE,IAAI,IAAI,CAACrE,GAAG,EAAEqE,QAAQ,EAAE,KAAK,EAAE,IAAI,IAAI,CAACpF,WAAW,CAACsF,QAAQ,CAAC,eAAe,CAAC,CAACsB,KAAK,KAAK,EAAE,EAAE;MACzH,IAAI,CAAClI,cAAc,CAACsK,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,iCAAiC;OAClE,CAAC;MACF;;IAGF,IAAI,CAACnJ,WAAW,CAAC8F,UAAU,CAAC;MAC1BhF,GAAG,EAAE,IAAI,CAACA,GAAG,EAAEsE,QAAQ,EAAE;MACzBrE,GAAG,EAAE,IAAI,CAACA,GAAG,EAAEqE,QAAQ,EAAE;MACzBpE,EAAE,EAAE,IAAI,CAACvC,cAAc,CAAC0J,aAAa,CAAChH;KACvC,CAAC;IAEF,IAAI,CAACnC,aAAa,CAAC8I,IAAI,EAAE;IAEzB,IAAI,IAAI,CAAC9H,WAAW,CAAC2K,KAAK,EAAE;MAC1B,MAAMC,SAAS,GAAG;QAChB,GAAG,IAAI,CAAC5K,WAAW,CAAC4G,KAAK;QACzB1F,MAAM,EAAE,IAAI,CAAClB,WAAW,CAAC4G,KAAK,CAAC1F,MAAM,CAACE,UAAU;QAChDR,mBAAmB,EAAE,IAAI,CAACZ,WAAW,CAACsF,QAAQ,CAAC1E,mBAAmB,CAACgG,KAAK,CAACiE,UAAU,CAACC,KAAK,CAAC,CAAC;OAC5F;MAED,IAAI,CAACrM,cAAc,CAAC8M,aAAa,CAACX,SAAS,CAAC,CAAClH,SAAS,CAAC;QACrDsH,IAAI,EAAGjD,GAAQ,IAAI;UACjB,IAAI,CAAC/I,aAAa,CAAC8J,IAAI,EAAE;UACzB,IAAI,CAACf,GAAG,CAACkD,OAAO,EAAE;YAChB,IAAI,CAACvM,cAAc,CAACsK,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;cAC3DC,MAAM,EAAE,IAAI,CAACtK,SAAS,CAACqK,OAAO,CAACpB,GAAG,CAACjI,OAAO;aAC3C,CAAC;WACH,MAAM;YACL,IAAI,CAACF,qBAAqB,GAAG,IAAI;YACjC,IAAI,CAACE,OAAO,GAAG,IAAI,CAAChB,SAAS,CAACqK,OAAO,CAAC,6CAA6C,CAAC;;QAExF,CAAC;QACD+B,KAAK,EAAGnC,GAAQ,IAAI;UAClB,IAAI,CAAC/J,aAAa,CAAC8J,IAAI,EAAE;UACzB,IAAI,CAACpK,cAAc,CAACsK,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAEL;WACT,CAAC;QACJ;OACD,CAAC;;EAEN;EAGAyC,WAAWA,CAACC,GAAQ;IAClB,IAAI,CAACC,MAAM,CAACD,GAAG,CAAC7E,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC5G,WAAW,CAAC4G,KAAK,CAACjG,QAAQ,GAAG,EAAE;MACpC,IAAI,CAACX,WAAW,CAAC4G,KAAK,CAAC+E,KAAK,GAAG,EAAE;;IAEnC,OAAO,KAAK;EACd;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,GAAG,EAAE;KACxB,MAAM;MACL,IAAI,CAACA,aAAa,GAAGzI,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE+B,QAAQ,EAAE;MACrE,IAAI,IAAI,CAACyG,aAAa,EACpB,IAAI,CAACA,aAAa,GAAG,IAAI,CAACA,aAAa,GAAG,cAAc,CAAC,KACtD,IAAI,CAACA,aAAa,GAAG,iBAAiB;;EAE/C;EAEAxB,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACrK,WAAW,CAAC2K,KAAK,EAAE,OAAO,IAAI;EAC1C;EAEAmB,YAAYA,CAAA;IACV,IAAI,CAACrN,cAAc,CAACsN,UAAU,CAAC,IAAI,CAAC5K,EAAE,CAAC,CAACuC,SAAS,CAAC;MAChDsH,IAAI,EAAGjD,GAAQ,IAAI;QACjB,IAAI,CAACrJ,cAAc,CAACsK,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAE,IAAI,CAACtK,SAAS,CAACqK,OAAO,CAC5B,6CAA6C;SAEhD,CAAC;QACF,IAAG,IAAI,CAAC5E,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,EAAE,EAAE;UAC9C,IAAI,CAAC5F,MAAM,CAAC0M,QAAQ,CAAC,CAAC,IAAI,CAAC9G,WAAW,CAAC,CAAC;SACzC,MAAM;UACL,IAAI,CAAC5F,MAAM,CAAC0M,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;MAE9C,CAAC;MACDH,KAAK,EAAGnC,GAAQ,IAAI;QAClB,IAAI,CAAC/J,aAAa,CAAC8J,IAAI,EAAE;QACzB,IAAI,CAACpK,cAAc,CAACsK,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,IAAI,CAACpK,SAAS,CAACqK,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEL;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAGQ5D,kBAAkBA,CAAC6G,gBAAqB;IAC9C,IAAI,CAACvN,cAAc,CAAC0J,aAAa,GAAG,IAAI;IACxC,IAAI,aAAa,IAAI8D,SAAS,EAAE;MAC9B,IAAG,CAACD,gBAAgB,EAAE;QACpBC,SAAS,CAACC,WAAW,CAACC,kBAAkB,CAAE5E,QAAQ,IAAI;UACpD,IAAI,CAACzG,GAAG,GAAGyG,QAAQ,CAAC6E,MAAM,CAAC1C,QAAQ;UACnC,IAAI,CAAC3I,GAAG,GAAGwG,QAAQ,CAAC6E,MAAM,CAACzC,SAAS;UACpC,IAAI,CAACpC,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAC/F,GAAG,EAAE,IAAI,CAACV,GAAG;cAAEW,GAAG,EAAE,IAAI,CAACV;YAAG;WACxC,CAAC;UACF,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACgG,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UACvC,IAAI,CAACC,IAAI,GAAG,EAAE;UAEd,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC3G,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;UACnC,IAAI,CAAC8H,oBAAoB,EAAE;QAC7B,CAAC,CAAC;OACH,MACI;QAED,IAAI,CAAC/H,GAAG,GAAG4H,UAAU,CAACsD,gBAAgB,CAACxK,GAAG,CAAC;QAC3C,IAAI,CAACT,GAAG,GAAG2H,UAAU,CAACsD,gBAAgB,CAACvK,GAAG,CAAC;QAC3C,IAAI,CAAC8F,QAAQ,GAAG,CAAC;UACfA,QAAQ,EAAE;YAAE/F,GAAG,EAAE,IAAI,CAACV,GAAG;YAAEW,GAAG,EAAE,IAAI,CAACV;UAAG;SACzC,CAAC;QAEF,IAAI,CAAC0G,UAAU,CAAC,IAAI,CAAC3G,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;QACnC,IAAI,CAAC8H,oBAAoB,EAAE;;;EAKnC;EACAwD,UAAUA,CAACnO,KAAW;IACpB,IAAIoO,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvO,KAAK,CAACoO,MAAM,CAAC,CAAC;IACrD,IAAI,CAACxL,GAAG,GAAGwL,MAAM,CAAC9K,GAAG;IACrB,IAAI,CAACT,GAAG,GAAEuL,MAAM,CAAC7K,GAAG;IACpB,IAAI,CAAC8F,QAAQ,GAAG,CAAC;MACfA,QAAQ,EAAE;QAAC/F,GAAG,EAAE,IAAI,CAACV,GAAG;QAAEW,GAAG,EAAE,IAAI,CAACV;MAAG;KACxC,CAAC;IACF,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACgG,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;IACvC,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC3G,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;EACrC;EACA8H,oBAAoBA,CAAA;IAClB,IAAIlL,iBAAiB,CAAC,IAAI,CAACQ,UAAU,CAAC,EAAE;MACtC,MAAMuO,UAAU,GAASC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACtDF,UAAU,CAACG,KAAK,GAAG,GAAG;MACtB,IAAI,CAAC3F,GAAG,CAAC5B,QAAQ,CAACrD,MAAM,CAACC,IAAI,CAAC4K,eAAe,CAACC,YAAY,CAAC,CAACC,IAAI,CAACN,UAAU,CAAC;;EAEhF;EACAO,aAAaA,CAAC/O,KAAU;IACtB,IAAIA,KAAK,CAACoO,MAAM,IAAI,IAAI,EAAE;MACxB,MAAMA,MAAM,GAAGpO,KAAK,CAACoO,MAAM,CAACY,MAAM,EAAE;MAC1C;MACM,IAAI,CAACzF,UAAU,CAAC6E,MAAM,CAAC9K,GAAG,EAAE8K,MAAM,CAAC7K,GAAG,CAAC;;EAE3C;EACA0L,SAASA,CAAA;IACP,MAAMC,MAAM,GAAS;MACnBC,WAAW,EAAG,CAAC;MACfC,QAAQ,EAAC,CAAC;MACVC,gBAAgB,EAAG;KACpB;IACD,IAAI,CAAC9O,cAAc,CAAC+O,YAAY,CAACJ,MAAM,CAAC,CAAC1J,SAAS,CAAEqE,GAAQ,IAAI;MAC9D,IAAGA,GAAG,CAACkD,OAAO,EAAC;QACb,IAAI,CAACpJ,SAAS,GAAGkG,GAAG,CAAC7B,IAAI,CAACF,OAAO;;IAErC,CAAC,CAAC;EACJ;EACAsE,cAAcA,CAAA;IACZ,IAAImD,QAAQ,GAAG,IAAIxL,MAAM,CAACC,IAAI,CAACC,QAAQ,EAAE;IACzC,IAAI,CAACL,eAAe,GAAG,IAAI;IAC3B2L,QAAQ,CAAC7D,OAAO,CAAC;MAAE,SAAS,EAAE,IAAI,CAAC5J,WAAW,CAACsF,QAAQ,CAAC,eAAe,CAAC,CAACsB;IAAK,CAAE,EAAG,CAACiD,OAAY,EAAEC,MAAW,KAAI;MAC/G,IAAIA,MAAM,IAAI7H,MAAM,CAACC,IAAI,CAACwL,cAAc,CAACC,EAAE,EAAE;QAC3C,IAAG9D,OAAO,CAAC,CAAC,CAAC,CAACI,kBAAkB,CAACX,MAAM,EAAC;UACtC,MAAM/I,IAAI,GAAKsJ,OAAO,CAAC,CAAC,CAAC,CAACI,kBAAkB,CAACjG,IAAI,CAAE0C,IAAS,IAAKA,IAAI,CAACwD,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;UACjG,IAAG5J,IAAI,CAAC6J,SAAS,KAAK,IAAI,CAACrK,iBAAiB,EAAC;YAC5C,IAAI,CAAC+B,eAAe,GAAG,KAAK;YAC5B,IAAI,CAAC5C,EAAE,CAACqL,aAAa,EAAE;;;;IAI9B,CAAC,CAAC;EACJ;EAEMrF,YAAYA,CAAA;IAAA,IAAA0I,MAAA;IAAA,OAAA/F,iBAAA;MAChB,IAAIuF,MAAM,GAAG;QACXC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE,CAAC;QACXC,gBAAgB,EAAE;OACnB;MACD,IAAI;QACF,MAAMxF,GAAG,SAAc6F,MAAI,CAACnP,cAAc,CAACoP,aAAa,CAACT,MAAM,CAAC,CAACnF,SAAS,EAAE;QAC5E,IAAIF,GAAG,CAACkD,OAAO,EAAE;UACf2C,MAAI,CAACjL,aAAa,GAAGoF,GAAG,CAAC7B,IAAI,CAACF,OAAO;UAErC4H,MAAI,CAAChL,cAAc,GAAGmF,GAAG,CAAC7B,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAACyD,MAAM;;OAEnD,CAAC,OAAOyB,KAAK,EAAE;QACd0C,MAAI,CAAClP,cAAc,CAACsK,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE0E,MAAI,CAAC9O,SAAS,CAACqK,OAAO,CAAC,0BAA0B;SAC3D,CAAC;;IACH;EACH;EAGAK,oBAAoBA,CAACsE,QAAa;IAChC,MAAMhH,cAAc,GAAG,IAAI,CAACnE,aAAa,CAACqB,IAAI,CAAE9C,MAAoB,IAAKA,MAAM,CAACC,EAAE,KAAK2M,QAAQ,CAAC;IAChG,IAAIhH,cAAc,EAAE;MAClB,IAAI,CAAClE,cAAc,GAAGkE,cAAc,EAAE2C,MAAM;;EAEhD;EACAsE,iBAAiBA,CAAC7J,IAAY;IAC5B,IAAI,CAACuE,mBAAmB,GAAGvE,IAAI;IAC/B,IAAI,CAAClE,WAAW,CAAC8F,UAAU,CAAC;MAAE7F,YAAY,EAAEiE;IAAI,CAAE,CAAC;IACnD,IAAG,IAAI,CAACuE,mBAAmB,IAAE,OAAO,EAAC;MACnC,IAAI,CAAC5I,mBAAmB,GAAC,IAAI;;EAEjC;EAEAkL,UAAUA,CAAC7M,KAAS;IAClB,IAAGA,KAAK,EAAC;MACP,IAAI,CAAC8B,WAAW,CAAC8F,UAAU,CAAC;QAAE7F,YAAY,EAAE/B;MAAK,CAAE,CAAC;MACpD,IAAI,CAAC6D,gBAAgB,CAAC,CAAC,CAAC,CAAC6D,IAAI,GAAG1H,KAAK;;IAEvC,IAAI,CAAC2B,mBAAmB,GAAC,KAAK;EAChC;EAEAmO,qBAAqBA,CAAC9P,KAAS;IAC7B,IAAI,CAAC2B,mBAAmB,GAAC3B,KAAK;EAChC;EACA,IAAIgD,MAAMA,CAAA;IACR,OAAQ,IAAI,CAAClB,WAAW,CAAC6D,GAAG,CAAC,QAAQ,CAAsB;EAC7D;CACD;AAzpByBoK,UAAA,EAAvB1Q,SAAS,CAAC,WAAW,CAAC,C,kDAAuC;AAiD9D0Q,UAAA,EADC1Q,SAAS,CAAC,QAAQ,CAAC,C,yDACiB;AAoBrC0Q,UAAA,EADC7Q,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,+CAKzC;AAtFUY,gBAAgB,GAAAiQ,UAAA,EAL5B9Q,SAAS,CAAC;EACT+Q,QAAQ,EAAE,aAAa;EACvBC,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,CAAC,0BAA0B;CACvC,CAAC,EAuGGC,OAAA,KAAAhR,MAAM,CAACC,WAAW,CAAC,E,EAtGXU,gBAAgB,CAsqB5B;SAtqBYA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}