{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ShipmentService {\n  http;\n  shipmentCost = 0;\n  actualShipmentFee = 0;\n  baseUrl;\n  latitude = \"\";\n  longitude = \"\";\n  shipmentLoaded;\n  currentShipment = null;\n  currentOrderTotal = 0;\n  updateShipMentUrl = '';\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Shipment/YellowBirdShipment`;\n    this.updateShipMentUrl = `${environment.apiEndPoint}/Shipment/PAPsShipment`;\n  }\n  createDeliveryRequest(data) {\n    return this.http.post(`${this.baseUrl}/DeliveryRequest`, data);\n  }\n  GetDeliveryPrice(data) {\n    return this.http.post(`${this.baseUrl}/PriceEstimation`, data);\n  }\n  updateShipmentStatus(orderId) {\n    return this.http.get(`${this.updateShipMentUrl}/UpdateShipmentStatus?orderId=${orderId}`);\n  }\n  static ɵfac = function ShipmentService_Factory(t) {\n    return new (t || ShipmentService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ShipmentService,\n    factory: ShipmentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}