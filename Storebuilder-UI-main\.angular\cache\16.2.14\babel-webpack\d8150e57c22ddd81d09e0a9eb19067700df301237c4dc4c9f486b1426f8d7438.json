{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { FormControl } from '@angular/forms';\nimport { environment } from '@environments/environment';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { GuidGenerator } from \"@core/services\";\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { verifyImageURL } from '@pages/merchant-livestream/merchant-livestream-details/merchant-livestream-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"ngx-cookie-service\";\nimport * as i7 from \"ngx-bootstrap/modal\";\nimport * as i8 from \"ngx-google-analytics\";\nimport * as i9 from \"@core/services/custom-GA.service\";\nimport * as i10 from \"@angular/platform-browser\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"@shared/components/size-guide-modal/size-guide-modal.component\";\nimport * as i14 from \"primeng/dialog\";\nimport * as i15 from \"@shared/modals/success-info-modal/success-info-modal.component\";\nimport * as i16 from \"@shared/modals/notify-modal/notify-modal.component\";\nimport * as i17 from \"@shared/modals/age-consent-modal/age-consent-modal.component\";\nimport * as i18 from \"@shared/modals/ineligable-purchase-modal/ineligable-purchase-modal.component\";\nimport * as i19 from \"primeng/divider\";\nimport * as i20 from \"primeng/rating\";\nimport * as i21 from \"primeng/progressbar\";\nimport * as i22 from \"primeng/tabview\";\nimport * as i23 from \"ngx-sharebuttons/button\";\nimport * as i24 from \"../seller-info/seller-info.component\";\nfunction DetailsComponent_div_0_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r24.product == null ? null : ctx_r24.product.currencyCode, \" \", i0.ɵɵpipeBind2(2, 2, ctx_r24.selectedVariant == null ? null : ctx_r24.selectedVariant.price, ctx_r24.disableCent == \"false\" ? \"1.\" + ctx_r24.decimalValue + \"-\" + ctx_r24.decimalValue : \"\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(2, 3, \"productDetails.details.only\"), \" \\u00A0\", ctx_r25.selectedVariant.quantity, \"\\u00A0 \", i0.ɵɵpipeBind1(3, 5, \"productDetails.details.itemsLeft\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"span\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵtemplate(6, DetailsComponent_div_0_div_1_div_6_Template, 3, 5, \"div\", 46);\n    i0.ɵɵtemplate(7, DetailsComponent_div_0_div_1_span_7_Template, 4, 7, \"span\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48)(9, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.showShareModal());\n    });\n    i0.ɵɵelement(10, \"img\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.product == null ? null : ctx_r3.product.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(5, 4, ctx_r3.selectedVariant.salePrice ? ctx_r3.selectedVariant.salePrice : ctx_r3.selectedVariant == null ? null : ctx_r3.selectedVariant.price, ctx_r3.disableCent == \"false\" ? \"1.\" + ctx_r3.decimalValue + \"-\" + ctx_r3.decimalValue : \"\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedVariant.salePrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedVariant.stockStatusId === 3);\n  }\n}\nfunction DetailsComponent_div_0_div_2_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 60);\n  }\n  if (rf & 2) {\n    const color_r29 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleProp(\"background-color\", color_r29.value);\n  }\n}\nfunction DetailsComponent_div_0_div_2_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.multiColor\"), \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"selected\": a0\n  };\n};\nfunction DetailsComponent_div_0_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_2_div_5_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r34);\n      const color_r29 = restoredCtx.$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r33.onColorChange(color_r29));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_2_div_5_div_1_Template, 1, 2, \"div\", 58);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_2_div_5_div_2_Template, 3, 3, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r29 = ctx.$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, color_r29.value === ctx_r28.selectedColor));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r29.value !== \"multi-color\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r29.value === \"multi-color\");\n  }\n}\nfunction DetailsComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 55);\n    i0.ɵɵtemplate(5, DetailsComponent_div_0_div_2_div_5_Template, 3, 5, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"productDetails.details.colors\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.colors);\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66);\n    i0.ɵɵtext(2, \" Sizes: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"mobile__sizes__selectedValue\": a0,\n    \"mobile__sizes__dimmed\": a1\n  };\n};\nfunction DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_container_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const size_r46 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r47.onSizeChange(size_r46, 0));\n    });\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const size_r46 = ctx.$implicit;\n    const ctx_r45 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, size_r46.value === ctx_r45.selectedSize, !ctx_r45.isSizeAvailable(size_r46)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r46.value, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_container_3_div_2_Template, 3, 5, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.size1);\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_template_4_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const size_r50 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r51.onSizeChange(size_r50, 1));\n    });\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const size_r50 = ctx.$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c1, size_r50.value === ctx_r49.selectedSize2, !ctx_r49.isSizeAvailable(size_r50)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r50.value, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_template_4_div_1_Template, 3, 5, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r44.size2);\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_container_3_Template, 3, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(4, DetailsComponent_div_0_div_3_ng_template_3_div_1_ng_template_4_Template, 2, 1, \"ng-template\", null, 69, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r40 = ctx.$implicit;\n    const _r43 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", size_r40.label || size_r40.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", size_r40.name === \"Size\")(\"ngIfElse\", _r43);\n  }\n}\nfunction DetailsComponent_div_0_div_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_3_ng_template_3_div_1_Template, 6, 3, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r37.varianceSpec);\n  }\n}\nfunction DetailsComponent_div_0_div_3_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 74);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_3_a_5_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.showSizeModal());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.sizeGuide\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_3_ng_container_2_Template, 3, 0, \"ng-container\", 28);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_3_ng_template_3_Template, 2, 1, \"ng-template\", null, 64, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, DetailsComponent_div_0_div_3_a_5_Template, 3, 3, \"a\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r36 = i0.ɵɵreference(4);\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.checkLabel(\"Size\", 0))(\"ngIfElse\", _r36);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.sizeGuidImage);\n  }\n}\nfunction DetailsComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"img\", 76);\n    i0.ɵɵelementStart(2, \"span\", 77);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"productDetails.details.deliverTo\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedAddress.addressLabel);\n  }\n}\nfunction DetailsComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 35)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"img\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"div\")(6, \"span\", 82);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(8, 3, \"sellerInfo.daysReturnable\"), \" \", ctx_r7.product.returnPeriod, \" \", i0.ɵɵpipeBind1(9, 5, \"sellerInfo.days\"), \". \");\n  }\n}\nfunction DetailsComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 35)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"img\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"div\")(6, \"span\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 85)(10, \"div\", 86);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_6_Template_div_click_12_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.triggerAnalytics(\"CLICK_ON_MORE_SELLER\", \"MORE_FROM_SELLER\"));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"sellerInfo.sellerInformation\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.product.sellerName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", \"/merchants/merchant-product/\" + ctx_r8.product.shopId + \"/\" + ctx_r8.product.sellerName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 6, \"sellerInfo.moreSeller\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_7_ng_container_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_7_ng_container_2_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r63.addItem(ctx_r63.selectedVariant, ctx_r63.product.shopId, ctx_r63.product));\n    });\n    i0.ɵɵelement(1, \"img\", 95);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.addToCart\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_7_ng_container_2_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_7_ng_container_2_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r65.notifyMe());\n    });\n    i0.ɵɵelement(1, \"img\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.notifyMe\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_7_ng_container_2_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_7_ng_container_2_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r67.shopNow(ctx_r67.selectedVariant, ctx_r67.product.shopId, ctx_r67.product));\n    });\n    i0.ɵɵelement(1, \"img\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_7_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_7_ng_container_2_button_1_Template, 4, 3, \"button\", 91);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_7_ng_container_2_button_2_Template, 4, 3, \"button\", 92);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_7_ng_container_2_button_3_Template, 4, 3, \"button\", 93);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.selectedVariant == null ? null : ctx_r57.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.selectedVariant.soldOut);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    opacity: a0\n  };\n};\nfunction DetailsComponent_div_0_div_7_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_7_ng_template_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r69.addItem(ctx_r69.selectedVariant, ctx_r69.product.shopId, ctx_r69.product));\n    });\n    i0.ɵɵelement(1, \"img\", 95);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_7_ng_template_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r71 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r71.shopNow(ctx_r71.selectedVariant, ctx_r71.product.shopId, ctx_r71.product));\n    });\n    i0.ɵɵelement(5, \"img\", 99);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r59.selectedVariant == null ? null : ctx_r59.selectedVariant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(10, _c2, ctx_r59.selectedVariant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 6, \"productDetails.details.addToCart\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r59.selectedVariant == null ? null : ctx_r59.selectedVariant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(12, _c2, ctx_r59.selectedVariant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 8, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"add-tocart\": a0,\n    \"add-tocart-old\": a1\n  };\n};\nfunction DetailsComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_7_ng_container_2_Template, 4, 3, \"ng-container\", 28);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_7_ng_template_3_Template, 8, 14, \"ng-template\", null, 90, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r58 = i0.ɵɵreference(4);\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c3, ctx_r9.isMobileTemplate, !ctx_r9.isMobileTemplate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.profile && ctx_r9.isShowNotifyFeature)(\"ngIfElse\", _r58);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_5_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 125);\n  }\n  if (rf & 2) {\n    const badge_r90 = ctx.$implicit;\n    const ctx_r89 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r89.getImageUrl(badge_r90.desktopImage), i0.ɵɵsanitizeUrl)(\"alt\", badge_r90.name);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_5_img_1_Template, 1, 2, \"img\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r72 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r72.product.badgesList);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.brand\"), \": \", ctx_r73.selectedVariant.specs == null ? null : ctx_r73.selectedVariant.specs.Brand == null ? null : ctx_r73.selectedVariant.specs.Brand.value, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_19_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productAttributes_r91 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.brand\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", productAttributes_r91 == null ? null : productAttributes_r91.attributeValue, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_19_span_1_Template, 5, 4, \"span\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productAttributes_r91 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (productAttributes_r91 == null ? null : productAttributes_r91.attributeType) === \"brand\");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r75.selectedVariant == null ? null : ctx_r75.selectedVariant.salePercent == null ? null : ctx_r75.selectedVariant.salePercent.toFixed(0), \"% \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.off\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"div\", 128);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.outOfStock\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtext(3);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r94 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"productDetails.details.only\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r94.selectedVariant.quantity, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 5, \"productDetails.details.leftInStock\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 129);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_ng_container_23_div_2_Template, 6, 7, \"div\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r77.selectedVariant.stockStatusId === 3);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_25_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 60);\n  }\n  if (rf & 2) {\n    const color_r96 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleProp(\"background-color\", color_r96.value);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_25_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.multiColor\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_25_div_6_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r101);\n      const color_r96 = restoredCtx.$implicit;\n      const ctx_r100 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r100.onColorChange(color_r96));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_25_div_6_div_1_Template, 1, 2, \"div\", 58);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_div_25_div_6_div_2_Template, 3, 3, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r96 = ctx.$implicit;\n    const ctx_r95 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"selected\", color_r96.value === ctx_r95.selectedColor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r96.value !== \"multi-color\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r96.value === \"multi-color\");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 132)(2, \"h3\", 133);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 134);\n    i0.ɵɵtemplate(6, DetailsComponent_div_0_div_10_div_25_div_6_Template, 3, 4, \"div\", 135);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"productDetails.details.colors\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r78.colors);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 141);\n    i0.ɵɵtext(2, \" Sizes: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c4 = function (a0, a1) {\n  return {\n    \"details__product-info__attributes__sizes__selectedValue\": a0,\n    \"dimmed\": a1\n  };\n};\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_container_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r115);\n      const size_r113 = restoredCtx.$implicit;\n      const ctx_r114 = i0.ɵɵnextContext(7);\n      return i0.ɵɵresetView(ctx_r114.onSizeChange(size_r113, 0));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r113 = ctx.$implicit;\n    const ctx_r112 = i0.ɵɵnextContext(7);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c4, size_r113.value === ctx_r112.selectedSize, !ctx_r112.isSizeAvailable(size_r113)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r113.value, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_container_3_div_2_Template, 2, 5, \"div\", 144);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r109 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r109.size1);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r119 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 145);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_template_4_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r119);\n      const size_r117 = restoredCtx.$implicit;\n      const ctx_r118 = i0.ɵɵnextContext(7);\n      return i0.ɵɵresetView(ctx_r118.onSizeChange(size_r117, 1));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r117 = ctx.$implicit;\n    const ctx_r116 = i0.ɵɵnextContext(7);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c4, size_r117.value === ctx_r116.selectedSize2, !ctx_r116.isSizeAvailable(size_r117)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r117.value, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_template_4_div_1_Template, 2, 5, \"div\", 144);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r111 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r111.size2);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 142);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_container_3_Template, 3, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(4, DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_ng_template_4_Template, 2, 1, \"ng-template\", null, 143, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r107 = ctx.$implicit;\n    const _r110 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", size_r107.label || size_r107.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", size_r107.name === \"Size\")(\"ngIfElse\", _r110);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_26_ng_template_3_div_1_Template, 6, 3, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r104 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r104.varianceSpec);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r121 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 146);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_26_a_5_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r121);\n      const ctx_r120 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r120.showSizeModal());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.sizeGuide\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"div\", 138);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_div_26_ng_container_2_Template, 3, 0, \"ng-container\", 28);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_10_div_26_ng_template_3_Template, 2, 1, \"ng-template\", null, 139, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, DetailsComponent_div_0_div_10_div_26_a_5_Template, 3, 3, \"a\", 140);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r103 = i0.ɵɵreference(4);\n    const ctx_r79 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r79.checkLabel(\"Size\", 0))(\"ngIfElse\", _r103);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.sizeGuidImage);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r80.disableCent === \"false\" ? i0.ɵɵpipeBind2(2, 1, ctx_r80.selectedVariant.salePrice, \"1.\" + ctx_r80.decimalValue + \"-\" + ctx_r80.decimalValue) : ctx_r80.selectedVariant.salePrice, \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"number\");\n    i0.ɵɵpipe(2, \"number\");\n  }\n  if (rf & 2) {\n    const ctx_r82 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r82.disableCent === \"false\" ? i0.ɵɵpipeBind2(1, 1, ctx_r82.selectedVariant.price, \"1.\" + ctx_r82.decimalValue + \"-\" + ctx_r82.decimalValue) : i0.ɵɵpipeBind2(2, 4, ctx_r82.selectedVariant.price, ctx_r82.disableCent == \"false\" ? \"1.\" + ctx_r82.decimalValue + \"-\" + ctx_r82.decimalValue : \"\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147);\n    i0.ɵɵtext(1);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r83 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r83.product == null ? null : ctx_r83.product.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r83.disableCent === \"false\" ? i0.ɵɵpipeBind2(3, 2, ctx_r83.selectedVariant == null ? null : ctx_r83.selectedVariant.price, \"1.\" + ctx_r83.decimalValue + \"-\" + ctx_r83.decimalValue) : i0.ɵɵpipeBind2(4, 5, ctx_r83.selectedVariant == null ? null : ctx_r83.selectedVariant.price, ctx_r83.disableCent == \"false\" ? \"1.\" + ctx_r83.decimalValue + \"-\" + ctx_r83.decimalValue : \"\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r123 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r123);\n      const ctx_r122 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r122.showShareModal());\n    });\n    i0.ɵɵelement(1, \"img\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_div_10_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r125 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_button_38_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r125);\n      const ctx_r124 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r124.showShareModal());\n    });\n    i0.ɵɵelement(1, \"img\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_div_10_button_39_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 151);\n  }\n}\nfunction DetailsComponent_div_0_div_10_button_39_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 152);\n  }\n}\nfunction DetailsComponent_div_0_div_10_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r129 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r129);\n      const ctx_r128 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r128.addToWishlist(ctx_r128.selectedVariant == null ? null : ctx_r128.selectedVariant.specProductId, ctx_r128.selectedVariant == null ? null : ctx_r128.selectedVariant.isLiked, ctx_r128.product));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_button_39_img_1_Template, 1, 0, \"img\", 149);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_button_39_img_2_Template, 1, 0, \"img\", 150);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r86 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r86.selectedVariant == null ? null : ctx_r86.selectedVariant.isLiked));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r86.selectedVariant == null ? null : ctx_r86.selectedVariant.isLiked);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 159);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 160);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r139 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r139);\n      const ctx_r138 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r138.addItem(ctx_r138.selectedVariant, ctx_r138.product.shopId, ctx_r138.product));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_img_1_Template, 1, 0, \"img\", 157);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_img_2_Template, 1, 0, \"img\", 158);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r133 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r133.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r133.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, \"productDetails.details.addToCart\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r141 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r141);\n      const ctx_r140 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r140.shopNow(ctx_r140.selectedVariant, ctx_r140.product.shopId, ctx_r140.product));\n    });\n    i0.ɵɵelement(1, \"img\", 162);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r142 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r142.notifyMe());\n    });\n    i0.ɵɵelement(1, \"img\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.notifyMe\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_1_Template, 5, 5, \"button\", 153);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_2_Template, 4, 3, \"button\", 154);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_button_3_Template, 4, 3, \"button\", 155);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r130 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r130.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r130.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r130.selectedVariant == null ? null : ctx_r130.selectedVariant.soldOut);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 159);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 160);\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r147 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r147);\n      const ctx_r146 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r146.addItem(ctx_r146.selectedVariant, ctx_r146.product.shopId, ctx_r146.product));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_img_1_Template, 1, 0, \"img\", 157);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_img_2_Template, 1, 0, \"img\", 158);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r147);\n      const ctx_r148 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r148.shopNow(ctx_r148.selectedVariant, ctx_r148.product.shopId, ctx_r148.product));\n    });\n    i0.ɵɵelement(6, \"img\", 162);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r132 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r132.selectedVariant == null ? null : ctx_r132.selectedVariant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(12, _c2, ctx_r132.selectedVariant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r132.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r132.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 8, \"productDetails.details.addToCart\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r132.selectedVariant == null ? null : ctx_r132.selectedVariant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(14, _c2, ctx_r132.selectedVariant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 10, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_ng_container_40_ng_container_1_Template, 4, 3, \"ng-container\", 28);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_ng_container_40_ng_template_2_Template, 9, 16, \"ng-template\", null, 90, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r131 = i0.ɵɵreference(3);\n    const ctx_r87 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r87.profile && ctx_r87.isShowNotifyFeature)(\"ngIfElse\", _r131);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_41_ng_container_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r155 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_41_ng_container_2_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r155);\n      const ctx_r154 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r154.addItem(ctx_r154.selectedVariant, ctx_r154.product.shopId, ctx_r154.product));\n    });\n    i0.ɵɵelement(1, \"img\", 159);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.addToCart\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_41_ng_container_2_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r157 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_41_ng_container_2_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r157);\n      const ctx_r156 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r156.notifyMe());\n    });\n    i0.ɵɵelement(1, \"img\", 97);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.notifyMe\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_41_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r159 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_10_div_41_ng_container_2_button_1_Template, 4, 3, \"button\", 153);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_div_41_ng_container_2_button_2_Template, 4, 3, \"button\", 155);\n    i0.ɵɵelementStart(3, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_41_ng_container_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r159);\n      const ctx_r158 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r158.addToWishlist(ctx_r158.selectedVariant == null ? null : ctx_r158.selectedVariant.specProductId, ctx_r158.selectedVariant == null ? null : ctx_r158.selectedVariant.isLiked, ctx_r158.product));\n    });\n    i0.ɵɵelement(4, \"img\", 168);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r149 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r149.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r149.selectedVariant == null ? null : ctx_r149.selectedVariant.soldOut);\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_41_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r161 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_41_ng_template_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r160 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r160.addItem(ctx_r160.selectedVariant, ctx_r160.product.shopId, ctx_r160.product));\n    });\n    i0.ɵɵelement(1, \"img\", 159);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_div_10_div_41_ng_template_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r162 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r162.addToWishlist(ctx_r162.selectedVariant == null ? null : ctx_r162.selectedVariant.specProductId, ctx_r162.selectedVariant == null ? null : ctx_r162.selectedVariant.isLiked, ctx_r162.product));\n    });\n    i0.ɵɵelement(5, \"img\", 168);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r151 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r151.selectedVariant == null ? null : ctx_r151.selectedVariant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(5, _c2, ctx_r151.selectedVariant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"productDetails.details.addToCart\"), \" \");\n  }\n}\nfunction DetailsComponent_div_0_div_10_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 166)(1, \"div\", 167);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_10_div_41_ng_container_2_Template, 5, 2, \"ng-container\", 28);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_10_div_41_ng_template_3_Template, 6, 7, \"ng-template\", null, 90, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r150 = i0.ɵɵreference(4);\n    const ctx_r88 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r88.profile && ctx_r88.isShowNotifyFeature)(\"ngIfElse\", _r150);\n  }\n}\nfunction DetailsComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"div\", 104)(3, \"div\", 105);\n    i0.ɵɵtext(4);\n    i0.ɵɵtemplate(5, DetailsComponent_div_0_div_10_div_5_Template, 2, 1, \"div\", 106);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"div\", 108);\n    i0.ɵɵtemplate(8, DetailsComponent_div_0_div_10_div_8_Template, 3, 4, \"div\", 2);\n    i0.ɵɵelementStart(9, \"div\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementStart(12, \"span\", 109);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementStart(17, \"span\", 109);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, DetailsComponent_div_0_div_10_div_19_Template, 2, 1, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 110);\n    i0.ɵɵtemplate(21, DetailsComponent_div_0_div_10_div_21_Template, 3, 4, \"div\", 111);\n    i0.ɵɵtemplate(22, DetailsComponent_div_0_div_10_div_22_Template, 4, 3, \"div\", 112);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, DetailsComponent_div_0_div_10_ng_container_23_Template, 3, 1, \"ng-container\", 2);\n    i0.ɵɵelementStart(24, \"div\", 113);\n    i0.ɵɵtemplate(25, DetailsComponent_div_0_div_10_div_25_Template, 7, 4, \"div\", 9);\n    i0.ɵɵtemplate(26, DetailsComponent_div_0_div_10_div_26_Template, 6, 3, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 115)(28, \"div\", 44)(29, \"span\", 45);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, DetailsComponent_div_0_div_10_ng_container_31_Template, 3, 4, \"ng-container\", 28);\n    i0.ɵɵtemplate(32, DetailsComponent_div_0_div_10_ng_template_32_Template, 3, 7, \"ng-template\", null, 116, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, DetailsComponent_div_0_div_10_div_34_Template, 5, 8, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 118);\n    i0.ɵɵtemplate(36, DetailsComponent_div_0_div_10_button_36_Template, 2, 0, \"button\", 119);\n    i0.ɵɵelementStart(37, \"div\", 120);\n    i0.ɵɵtemplate(38, DetailsComponent_div_0_div_10_button_38_Template, 2, 0, \"button\", 119);\n    i0.ɵɵtemplate(39, DetailsComponent_div_0_div_10_button_39_Template, 3, 2, \"button\", 121);\n    i0.ɵɵtemplate(40, DetailsComponent_div_0_div_10_ng_container_40_Template, 4, 2, \"ng-container\", 2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, DetailsComponent_div_0_div_10_div_41_Template, 5, 2, \"div\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r81 = i0.ɵɵreference(33);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r10.product == null ? null : ctx_r10.product.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.product == null ? null : ctx_r10.product.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.product == null ? null : ctx_r10.product.badgesList == null ? null : ctx_r10.product.badgesList.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.specs == null ? null : ctx_r10.selectedVariant.specs.Brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 24, \"productDetails.details.sku\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.sku, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 26, \"productDetails.details.skuAutoGenerated\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.skuAutoGenerated, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.product.productAttributeValues);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.salePercent) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.product.productVariances.length && ctx_r10.screenWidth > 767);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.colors.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.sizes.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.product == null ? null : ctx_r10.product.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedVariant.salePrice)(\"ngIfElse\", _r81);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedVariant == null ? null : ctx_r10.selectedVariant.salePrice);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.screenWidth <= 1200 ? \"justify-content-space-between\" : \"justify-content-end\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.screenWidth <= 1200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.screenWidth >= 1200);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.screenWidth > 700);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.screenWidth > 700);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.screenWidth <= 700);\n  }\n}\nfunction DetailsComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 169);\n    i0.ɵɵelement(1, \"app-seller-info\", 170);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"product\", ctx_r11.product);\n  }\n}\nfunction DetailsComponent_div_0_p_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 173);\n  }\n  if (rf & 2) {\n    const ctx_r163 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r163.sanitizedDescription, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DetailsComponent_div_0_p_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r164 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r164.product.description);\n  }\n}\nfunction DetailsComponent_div_0_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 171);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_p_20_span_1_Template, 1, 1, \"span\", 172);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_p_20_span_2_Template, 2, 1, \"span\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.descriptionBase64);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.descriptionBase64);\n  }\n}\nfunction DetailsComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 174);\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r13.sanitizedDescription, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DetailsComponent_div_0_ng_container_35_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const variantSpec_r166 = i0.ɵɵnextContext().$implicit;\n    const ctx_r167 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", variantSpec_r166.name, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", variantSpec_r166.value, \" \", ctx_r167.selectedVariant == null ? null : ctx_r167.selectedVariant.specs[variantSpec_r166.name] == null ? null : ctx_r167.selectedVariant.specs[variantSpec_r166.name].unit, \"\");\n  }\n}\nfunction DetailsComponent_div_0_ng_container_35_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_container_35_ng_container_1_div_1_Template, 5, 3, \"div\", 175);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variantSpec_r166 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", variantSpec_r166.name === \"Weight\");\n  }\n}\nfunction DetailsComponent_div_0_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_container_35_ng_container_1_Template, 2, 1, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.selectedVariant.varianceSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_36_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productSpec_r170 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", productSpec_r170.name, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", productSpec_r170.value, \" \", productSpec_r170.unit, \"\");\n  }\n}\nfunction DetailsComponent_div_0_ng_template_36_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_template_36_ng_container_0_div_1_Template, 5, 3, \"div\", 175);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const productSpec_r170 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", productSpec_r170.name === \"Weight\");\n  }\n}\nfunction DetailsComponent_div_0_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DetailsComponent_div_0_ng_template_36_ng_container_0_Template, 2, 1, \"ng-container\", 67);\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.product.productSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_container_40_ng_container_1_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" X\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_container_40_ng_container_1_p_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \":\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_container_40_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_ng_container_40_ng_container_1_p_2_Template, 2, 0, \"p\", 2);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_ng_container_40_ng_container_1_p_3_Template, 2, 0, \"p\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variantSpec_r174 = ctx.$implicit;\n    const i_r175 = ctx.index;\n    const ctx_r173 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", variantSpec_r174.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r175 !== ctx_r173.filteredVarianceSpecs.length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r175 === ctx_r173.filteredVarianceSpecs.length - 1);\n  }\n}\nfunction DetailsComponent_div_0_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_container_40_ng_container_1_Template, 4, 3, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.filteredVarianceSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"X\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_p_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \":\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_p_2_Template, 2, 0, \"p\", 2);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_p_3_Template, 2, 0, \"p\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const productSpec_r180 = ctx.$implicit;\n    const i_r181 = ctx.index;\n    const ctx_r179 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", productSpec_r180.name, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r181 !== ctx_r179.filteredProductSpecs.length - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r181 === ctx_r179.filteredProductSpecs.length - 1);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_41_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_template_41_ng_container_0_ng_container_1_Template, 4, 3, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r178 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r178.filteredProductSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DetailsComponent_div_0_ng_template_41_ng_container_0_Template, 2, 1, \"ng-container\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.filteredProductSpecs.length > 0);\n  }\n}\nfunction DetailsComponent_div_0_ng_container_44_ng_container_1_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"X\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_container_44_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_ng_container_44_ng_container_1_p_2_Template, 2, 0, \"p\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variantSpec_r185 = ctx.$implicit;\n    const i_r186 = ctx.index;\n    const ctx_r184 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", variantSpec_r185.value, \" \", ctx_r184.selectedVariant == null ? null : ctx_r184.selectedVariant.specs[variantSpec_r185.name] == null ? null : ctx_r184.selectedVariant.specs[variantSpec_r185.name].unit, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r186 !== ctx_r184.filteredVarianceSpecs.length - 1);\n  }\n}\nfunction DetailsComponent_div_0_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_container_44_ng_container_1_Template, 3, 3, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r20.filteredVarianceSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_45_ng_container_0_ng_container_1_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"X\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_0_ng_template_45_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_ng_template_45_ng_container_0_ng_container_1_p_2_Template, 2, 0, \"p\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const productSpec_r190 = ctx.$implicit;\n    const i_r191 = ctx.index;\n    const ctx_r189 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", productSpec_r190.value, \" \", productSpec_r190.unit, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r191 !== ctx_r189.filteredProductSpecs.length - 1);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_45_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_ng_template_45_ng_container_0_ng_container_1_Template, 3, 3, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r188 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r188.filteredProductSpecs);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DetailsComponent_div_0_ng_template_45_ng_container_0_Template, 2, 1, \"ng-container\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.filteredProductSpecs.length > 0);\n  }\n}\nfunction DetailsComponent_div_0_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r194 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 176)(1, \"div\", 177);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 178);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 179);\n    i0.ɵɵelement(8, \"share-button\", 180)(9, \"share-button\", 181)(10, \"share-button\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 183)(12, \"div\", 184);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 185)(16, \"div\", 186);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_0_ng_template_60_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r194);\n      const ctx_r193 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r193.copyLink(ctx_r193.currentLink));\n    });\n    i0.ɵɵelement(19, \"img\", 188);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 7, \"productDetails.details.shareThisProduct\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 9, \"productDetails.details.ProductShareWithYourFriend\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"url\", ctx_r23.currentLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"url\", ctx_r23.currentLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"url\", ctx_r23.currentLink);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 11, \"productDetails.details.orCopyLink\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.currentLink, \" \");\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    \"flex-column\": a0\n  };\n};\nconst _c6 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nfunction DetailsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r196 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, DetailsComponent_div_0_div_1_Template, 11, 7, \"div\", 8);\n    i0.ɵɵtemplate(2, DetailsComponent_div_0_div_2_Template, 6, 4, \"div\", 9);\n    i0.ɵɵtemplate(3, DetailsComponent_div_0_div_3_Template, 6, 3, \"div\", 10);\n    i0.ɵɵtemplate(4, DetailsComponent_div_0_div_4_Template, 7, 4, \"div\", 11);\n    i0.ɵɵtemplate(5, DetailsComponent_div_0_div_5_Template, 10, 7, \"div\", 12);\n    i0.ɵɵtemplate(6, DetailsComponent_div_0_div_6_Template, 15, 8, \"div\", 12);\n    i0.ɵɵtemplate(7, DetailsComponent_div_0_div_7_Template, 5, 6, \"div\", 13);\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"div\", 15);\n    i0.ɵɵtemplate(10, DetailsComponent_div_0_div_10_Template, 42, 28, \"div\", 16);\n    i0.ɵɵtemplate(11, DetailsComponent_div_0_div_11_Template, 2, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 18)(13, \"div\", 19)(14, \"p-tabView\")(15, \"p-tabPanel\", 20);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementStart(17, \"div\", 21);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, DetailsComponent_div_0_p_20_Template, 3, 2, \"p\", 22);\n    i0.ɵɵtemplate(21, DetailsComponent_div_0_div_21_Template, 1, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p-tabPanel\", 20);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementStart(24, \"div\", 24)(25, \"div\", 25)(26, \"div\", 26);\n    i0.ɵɵtext(27, \"Product SKU:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 27);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 25)(31, \"div\", 26);\n    i0.ɵɵtext(32, \"Merchant SKU:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 27);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, DetailsComponent_div_0_ng_container_35_Template, 2, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(36, DetailsComponent_div_0_ng_template_36_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(38, \"div\", 25)(39, \"div\", 30);\n    i0.ɵɵtemplate(40, DetailsComponent_div_0_ng_container_40_Template, 2, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(41, DetailsComponent_div_0_ng_template_41_Template, 1, 1, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 32);\n    i0.ɵɵtemplate(44, DetailsComponent_div_0_ng_container_44_Template, 2, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(45, DetailsComponent_div_0_ng_template_45_Template, 1, 1, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"p-tabPanel\", 20);\n    i0.ɵɵpipe(48, \"translate\");\n    i0.ɵɵelementStart(49, \"div\", 34)(50, \"div\", 35)(51, \"div\", 36)(52, \"div\", 37);\n    i0.ɵɵtext(53, \" 0.0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 38)(55, \"p-rating\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function DetailsComponent_div_0_Template_p_rating_ngModelChange_55_listener($event) {\n      i0.ɵɵrestoreView(_r196);\n      const ctx_r195 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r195.rating = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 40);\n    i0.ɵɵtext(57);\n    i0.ɵɵpipe(58, \"translate\");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(59, \"p-dialog\", 41);\n    i0.ɵɵlistener(\"visibleChange\", function DetailsComponent_div_0_Template_p_dialog_visibleChange_59_listener($event) {\n      i0.ɵɵrestoreView(_r196);\n      const ctx_r197 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r197.displayShareModal = $event);\n    })(\"onHide\", function DetailsComponent_div_0_Template_p_dialog_onHide_59_listener() {\n      i0.ɵɵrestoreView(_r196);\n      const ctx_r198 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r198.displayShareModal = false);\n    });\n    i0.ɵɵtemplate(60, DetailsComponent_div_0_ng_template_60_Template, 20, 13, \"ng-template\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(37);\n    const _r18 = i0.ɵɵreference(42);\n    const _r21 = i0.ɵɵreference(46);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768 && ctx_r0.colors.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768 && ctx_r0.sizes.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768 && ctx_r0.profile && ctx_r0.selectedAddress && !(ctx_r0.selectedAddress == null ? null : ctx_r0.selectedAddress.addressLabel == null ? null : ctx_r0.selectedAddress.addressLabel.includes(\"with no address\")));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768 && ctx_r0.product.isRefundable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth < 768);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c5, ctx_r0.screenWidth <= 1200));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth >= 768);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.screenWidth >= 768);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"header\", i0.ɵɵpipeBind1(16, 36, \"productDetails.details.description\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 38, \"productDetails.details.description\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.channelId === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.channelId === 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"header\", i0.ɵɵpipeBind1(23, 40, \"productDetails.details.specification\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedVariant.skuAutoGenerated, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.selectedVariant.sku);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedVariant.varianceSpecs.length > 2)(\"ngIfElse\", _r15);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredVarianceSpecs.length > 2)(\"ngIfElse\", _r18);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredVarianceSpecs.length > 2)(\"ngIfElse\", _r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"header\", i0.ɵɵpipeBind1(48, 42, \"productDetails.details.review\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.rating)(\"cancel\", false)(\"readonly\", true)(\"stars\", 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(58, 44, \"productDetails.details.customerRating\"), \"(0) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"visible\", ctx_r0.displayShareModal)(\"breakpoints\", i0.ɵɵpureFunction0(48, _c6))(\"dismissableMask\", true)(\"draggable\", false)(\"showHeader\", false)(\"modal\", true)(\"resizable\", false);\n  }\n}\nfunction DetailsComponent_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 232)(2, \"span\", 233);\n    i0.ɵɵtext(3, \"Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 234);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 235)(9, \"span\", 236);\n    i0.ɵɵtext(10, \"Was\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 237);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r199 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r199.product == null ? null : ctx_r199.product.currencyCode, \" \", ctx_r199.disableCent === \"false\" ? i0.ɵɵpipeBind2(6, 4, ctx_r199.selectedVariant.salePrice, \"1.\" + ctx_r199.decimalValue + \"-\" + ctx_r199.decimalValue) : i0.ɵɵpipeBind1(7, 7, ctx_r199.selectedVariant.salePrice), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r199.product.currencyCode, \" \", ctx_r199.disableCent === \"false\" ? i0.ɵɵpipeBind2(13, 9, ctx_r199.selectedVariant == null ? null : ctx_r199.selectedVariant.price, \"1.\" + ctx_r199.decimalValue + \"-\" + ctx_r199.decimalValue) : i0.ɵɵpipeBind1(14, 12, ctx_r199.selectedVariant == null ? null : ctx_r199.selectedVariant.price), \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 232)(2, \"span\", 234);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r200 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r200.product == null ? null : ctx_r200.product.currencyCode, \" \", ctx_r200.disableCent === \"false\" ? i0.ɵɵpipeBind2(4, 2, ctx_r200.selectedVariant == null ? null : ctx_r200.selectedVariant.price, \"1.\" + ctx_r200.decimalValue + \"-\" + ctx_r200.decimalValue) : i0.ɵɵpipeBind1(5, 5, ctx_r200.selectedVariant == null ? null : ctx_r200.selectedVariant.price), \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_17_div_6_span_1_em_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 248);\n  }\n}\nfunction DetailsComponent_div_1_div_17_div_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 246);\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_17_div_6_span_1_em_1_Template, 1, 0, \"em\", 247);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r217 = i0.ɵɵnextContext().$implicit;\n    const ctx_r218 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", color_r217.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r217.value === ctx_r218.selectedColor);\n  }\n}\nfunction DetailsComponent_div_1_div_17_div_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 249);\n    i0.ɵɵtext(1, \" Multi Color\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_1_div_17_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r223 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 243);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_div_17_div_6_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r223);\n      const color_r217 = restoredCtx.$implicit;\n      const ctx_r222 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r222.onColorChange(color_r217));\n    });\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_17_div_6_span_1_Template, 2, 3, \"span\", 244);\n    i0.ɵɵtemplate(2, DetailsComponent_div_1_div_17_div_6_span_2_Template, 2, 0, \"span\", 245);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r217 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r217.value !== \"multi-color\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", color_r217.value === \"multi-color\");\n  }\n}\nfunction DetailsComponent_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 238)(1, \"div\", 239)(2, \"div\", 240)(3, \"span\", 241);\n    i0.ɵɵtext(4, \"Color\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 103);\n    i0.ɵɵtemplate(6, DetailsComponent_div_1_div_17_div_6_Template, 3, 2, \"div\", 242);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r201 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r201.colors);\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 241);\n    i0.ɵɵtext(2, \"Size:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    \"selected-size\": a0\n  };\n};\nfunction DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_container_3_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r237 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 254);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_container_3_p_1_Template_p_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r237);\n      const size_r235 = restoredCtx.$implicit;\n      const ctx_r236 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r236.onSizeChange(size_r235, 0));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r235 = ctx.$implicit;\n    const ctx_r234 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, size_r235.value === ctx_r234.selectedSize));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r235.value, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_container_3_p_1_Template, 2, 4, \"p\", 253);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r231 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r231.size1);\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_template_4_p_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r241 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 254);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_template_4_p_0_Template_p_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r241);\n      const size_r239 = restoredCtx.$implicit;\n      const ctx_r240 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r240.onSizeChange(size_r239, 1));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r239 = ctx.$implicit;\n    const ctx_r238 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, size_r239.value === ctx_r238.selectedSize2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", size_r239.value, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_template_4_p_0_Template, 2, 4, \"p\", 253);\n  }\n  if (rf & 2) {\n    const ctx_r233 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r233.size2);\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_template_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 241);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 28);\n    i0.ɵɵtemplate(4, DetailsComponent_div_1_div_18_ng_template_6_div_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 143, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r229 = ctx.$implicit;\n    const parentIndex_r230 = ctx.index;\n    const _r232 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", size_r229.label, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", parentIndex_r230 === 0)(\"ngIfElse\", _r232);\n  }\n}\nfunction DetailsComponent_div_1_div_18_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 141);\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_18_ng_template_6_div_1_Template, 6, 3, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r226 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r226.varianceSpec);\n  }\n}\nfunction DetailsComponent_div_1_div_18_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r243 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 255);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_div_18_a_10_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r243);\n      const ctx_r242 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r242.showSizeModal());\n    });\n    i0.ɵɵtext(1, \"Size Guide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 250)(1, \"div\")(2, \"div\", 251)(3, \"div\", 239)(4, \"div\", 240);\n    i0.ɵɵtemplate(5, DetailsComponent_div_1_div_18_ng_container_5_Template, 3, 0, \"ng-container\", 28);\n    i0.ɵɵtemplate(6, DetailsComponent_div_1_div_18_ng_template_6_Template, 2, 1, \"ng-template\", null, 139, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\")(9, \"div\", 192);\n    i0.ɵɵtemplate(10, DetailsComponent_div_1_div_18_a_10_Template, 2, 0, \"a\", 252);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r225 = i0.ɵɵreference(7);\n    const ctx_r202 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r202.checkLabel(\"Size\", 0))(\"ngIfElse\", _r225);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r202.sizeGuidImage);\n  }\n}\nfunction DetailsComponent_div_1_img_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 256);\n  }\n}\nfunction DetailsComponent_div_1_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 257);\n  }\n}\nfunction DetailsComponent_div_1_img_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 258);\n  }\n}\nfunction DetailsComponent_div_1_img_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 259);\n  }\n}\nconst _c8 = function (a0) {\n  return {\n    \"show-more\": a0\n  };\n};\nfunction DetailsComponent_div_1_div_30_textarea_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"textarea\", 264);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    const ctx_r244 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r244.textareaHeight, \"px\");\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 4, \"productDetails.details.description\"));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c8, ctx_r244.showMore));\n  }\n}\nfunction DetailsComponent_div_1_div_30_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r247 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 265);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_div_30_p_9_Template_p_click_0_listener() {\n      i0.ɵɵrestoreView(_r247);\n      const ctx_r246 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r246.readMore());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r245 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, !ctx_r245.showMore ? \"buttons.readMore\" : \"buttons.readLess\"), \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 211)(1, \"p\", 212);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form\", 260)(5, \"pre\", 261);\n    i0.ɵɵtext(6, \"          \");\n    i0.ɵɵtemplate(7, DetailsComponent_div_1_div_30_textarea_7_Template, 2, 8, \"textarea\", 262);\n    i0.ɵɵtext(8, \"\\n        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, DetailsComponent_div_1_div_30_p_9_Template, 3, 3, \"p\", 263);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r207 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"productDetails.details.aboutThisProduct\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r207.productForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r207.isDescription);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r207.product && ctx_r207.product.description && ctx_r207.product.description.length > 40);\n  }\n}\nfunction DetailsComponent_div_1_div_31_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p\", 173);\n  }\n  if (rf & 2) {\n    const ctx_r248 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r248.product.description, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DetailsComponent_div_1_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 211)(1, \"p\", 212);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DetailsComponent_div_1_div_31_p_4_Template, 1, 1, \"p\", 172);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r208 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"productDetails.details.aboutThisProduct\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r208.product && ctx_r208.product.description && ctx_r208.product.description.length > 0);\n  }\n}\nfunction DetailsComponent_div_1_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"p\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r209 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"productDetails.details.width\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_1_0 = ctx_r209.product == null ? null : ctx_r209.product.specs == null ? null : ctx_r209.product.specs.Width == null ? null : ctx_r209.product.specs.Width.value) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r209.selectedVariant == null ? null : ctx_r209.selectedVariant.specs == null ? null : ctx_r209.selectedVariant.specs.Width == null ? null : ctx_r209.selectedVariant.specs.Width.value, \" \", (tmp_1_0 = ctx_r209.product == null ? null : ctx_r209.product.specs == null ? null : ctx_r209.product.specs.Width == null ? null : ctx_r209.product.specs.Width.unit) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r209.selectedVariant == null ? null : ctx_r209.selectedVariant.specs == null ? null : ctx_r209.selectedVariant.specs.Width == null ? null : ctx_r209.selectedVariant.specs.Width.unit, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"p\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r210 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"productDetails.details.height\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_1_0 = ctx_r210.product == null ? null : ctx_r210.product.specs == null ? null : ctx_r210.product.specs.Height == null ? null : ctx_r210.product.specs.Height.value) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r210.selectedVariant == null ? null : ctx_r210.selectedVariant.specs == null ? null : ctx_r210.selectedVariant.specs.Height == null ? null : ctx_r210.selectedVariant.specs.Height.value, \" \", (tmp_1_0 = ctx_r210.product == null ? null : ctx_r210.product.specs == null ? null : ctx_r210.product.specs.Height == null ? null : ctx_r210.product.specs.Height.unit) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r210.selectedVariant == null ? null : ctx_r210.selectedVariant.specs == null ? null : ctx_r210.selectedVariant.specs.Height == null ? null : ctx_r210.selectedVariant.specs.Height.unit, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"p\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r211 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"productDetails.details.weight\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_1_0 = ctx_r211.product == null ? null : ctx_r211.product.specs == null ? null : ctx_r211.product.specs.Weight == null ? null : ctx_r211.product.specs.Weight.value) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r211.selectedVariant == null ? null : ctx_r211.selectedVariant.specs == null ? null : ctx_r211.selectedVariant.specs.Weight == null ? null : ctx_r211.selectedVariant.specs.Weight.value, \" \", (tmp_1_0 = ctx_r211.product == null ? null : ctx_r211.product.specs == null ? null : ctx_r211.product.specs.Weight == null ? null : ctx_r211.product.specs.Weight.unit) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r211.selectedVariant == null ? null : ctx_r211.selectedVariant.specs == null ? null : ctx_r211.selectedVariant.specs.Weight == null ? null : ctx_r211.selectedVariant.specs.Weight.unit, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"p\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r212 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"productDetails.details.length\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (tmp_1_0 = ctx_r212.product == null ? null : ctx_r212.product.specs == null ? null : ctx_r212.product.specs.Length == null ? null : ctx_r212.product.specs.Length.value) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r212.selectedVariant == null ? null : ctx_r212.selectedVariant.specs == null ? null : ctx_r212.selectedVariant.specs.Length == null ? null : ctx_r212.selectedVariant.specs.Length.value, \" \", (tmp_1_0 = ctx_r212.product == null ? null : ctx_r212.product.specs == null ? null : ctx_r212.product.specs.Length == null ? null : ctx_r212.product.specs.Length.unit) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r212.selectedVariant == null ? null : ctx_r212.selectedVariant.specs == null ? null : ctx_r212.selectedVariant.specs.Length == null ? null : ctx_r212.selectedVariant.specs.Length.unit, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 213)(1, \"p\", 214);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 215);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r213 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"productDetails.details.gender\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r213.genderSpeces[ctx_r213.product == null ? null : ctx_r213.product.specs == null ? null : ctx_r213.product.specs.Gender == null ? null : ctx_r213.product.specs.Gender.value]) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r213.genderSpeces[ctx_r213.selectedVariant == null ? null : ctx_r213.selectedVariant.specs == null ? null : ctx_r213.selectedVariant.specs.Gender == null ? null : ctx_r213.selectedVariant.specs.Gender.value], \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_110_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r254 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 268);\n    i0.ɵɵelement(1, \"p-divider\");\n    i0.ɵɵelementStart(2, \"div\", 192)(3, \"div\", 269);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"em\", 270);\n    i0.ɵɵelementStart(6, \"div\", 271);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p-rating\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function DetailsComponent_div_1_div_110_ng_container_1_div_1_Template_p_rating_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r254);\n      const review_r250 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(review_r250.rate = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 271);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 272);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"p-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const review_r250 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", review_r250 == null ? null : review_r250.customerName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 8, \"productDetails.details.verifiedPurchase\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", review_r250.rate)(\"cancel\", false)(\"readonly\", true)(\"stars\", 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 10, review_r250 == null ? null : review_r250.creationOn, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", review_r250 == null ? null : review_r250.describtion, \" \");\n  }\n}\nfunction DetailsComponent_div_1_div_110_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_110_ng_container_1_div_1_Template, 16, 13, \"div\", 267);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const review_r250 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", review_r250 == null ? null : review_r250.rate);\n  }\n}\nconst _c9 = function (a0) {\n  return {\n    \"user-review-scroll\": a0\n  };\n};\nfunction DetailsComponent_div_1_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 266);\n    i0.ɵɵtemplate(1, DetailsComponent_div_1_div_110_ng_container_1_Template, 2, 1, \"ng-container\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r214 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c9, ctx_r214.reviewsLenght > 2 ? true : false));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r214.reviews);\n  }\n}\nfunction DetailsComponent_div_1_ng_container_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r257 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-mtn-size-guide-modal\", 273);\n    i0.ɵɵlistener(\"cancel\", function DetailsComponent_div_1_ng_container_111_Template_app_mtn_size_guide_modal_cancel_1_listener($event) {\n      i0.ɵɵrestoreView(_r257);\n      const ctx_r256 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r256.onSubmit($event));\n    })(\"submit\", function DetailsComponent_div_1_ng_container_111_Template_app_mtn_size_guide_modal_submit_1_listener($event) {\n      i0.ɵɵrestoreView(_r257);\n      const ctx_r258 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r258.onSubmit($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r215 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r215.displayModal)(\"sizeGuidImage\", ctx_r215.sizeGuidImage);\n  }\n}\nconst _c10 = function (a0, a1) {\n  return {\n    soldOut: a0,\n    instock: a1\n  };\n};\nconst _c11 = function (a0) {\n  return {\n    redDot: a0\n  };\n};\nfunction DetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r260 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 189)(1, \"div\", 190)(2, \"p\", 191);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 192)(5, \"div\", 193);\n    i0.ɵɵelement(6, \"div\", 194);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 195);\n    i0.ɵɵelement(9, \"em\", 196);\n    i0.ɵɵelementStart(10, \"div\", 197);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 198);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 199);\n    i0.ɵɵtemplate(15, DetailsComponent_div_1_div_15_Template, 15, 14, \"div\", 2);\n    i0.ɵɵtemplate(16, DetailsComponent_div_1_div_16_Template, 6, 7, \"div\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, DetailsComponent_div_1_div_17_Template, 7, 1, \"div\", 200);\n    i0.ɵɵtemplate(18, DetailsComponent_div_1_div_18_Template, 11, 3, \"div\", 201);\n    i0.ɵɵelementStart(19, \"div\", 202)(20, \"button\", 203);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r260);\n      const ctx_r259 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r259.addItem(ctx_r259.selectedVariant, ctx_r259.product.shopId, ctx_r259.product));\n    });\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 204);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r260);\n      const ctx_r261 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r261.shopNow(ctx_r261.selectedVariant, ctx_r261.product.shopId, ctx_r261.product));\n    });\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 205);\n    i0.ɵɵlistener(\"click\", function DetailsComponent_div_1_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r260);\n      const ctx_r262 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r262.addToWishlist(ctx_r262.selectedVariant == null ? null : ctx_r262.selectedVariant.specProductId, ctx_r262.selectedVariant == null ? null : ctx_r262.selectedVariant.isLiked, ctx_r262.product));\n    });\n    i0.ɵɵelementStart(25, \"em\");\n    i0.ɵɵtemplate(26, DetailsComponent_div_1_img_26_Template, 1, 0, \"img\", 206);\n    i0.ɵɵtemplate(27, DetailsComponent_div_1_img_27_Template, 1, 0, \"img\", 207);\n    i0.ɵɵtemplate(28, DetailsComponent_div_1_img_28_Template, 1, 0, \"img\", 208);\n    i0.ɵɵtemplate(29, DetailsComponent_div_1_img_29_Template, 1, 0, \"img\", 209);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(30, DetailsComponent_div_1_div_30_Template, 10, 6, \"div\", 210);\n    i0.ɵɵtemplate(31, DetailsComponent_div_1_div_31_Template, 5, 4, \"div\", 210);\n    i0.ɵɵelementStart(32, \"div\", 211)(33, \"p\", 212);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 213)(37, \"p\", 214);\n    i0.ɵɵtext(38);\n    i0.ɵɵpipe(39, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 215);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 213)(43, \"p\", 214);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"p\", 215);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(48, DetailsComponent_div_1_div_48_Template, 6, 5, \"div\", 216);\n    i0.ɵɵtemplate(49, DetailsComponent_div_1_div_49_Template, 6, 5, \"div\", 216);\n    i0.ɵɵtemplate(50, DetailsComponent_div_1_div_50_Template, 6, 5, \"div\", 216);\n    i0.ɵɵtemplate(51, DetailsComponent_div_1_div_51_Template, 6, 5, \"div\", 216);\n    i0.ɵɵtemplate(52, DetailsComponent_div_1_div_52_Template, 6, 4, \"div\", 216);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 211)(54, \"p\", 217);\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 218)(58, \"div\", 219)(59, \"p\", 220);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 221);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"p-rating\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function DetailsComponent_div_1_Template_p_rating_ngModelChange_64_listener($event) {\n      i0.ɵɵrestoreView(_r260);\n      const ctx_r263 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r263.rating = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"p\", 222);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵpipe(68, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 223)(70, \"div\", 224)(71, \"div\", 225);\n    i0.ɵɵtext(72, \"5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(73, \"em\", 226);\n    i0.ɵɵelementStart(74, \"div\", 227);\n    i0.ɵɵelement(75, \"p-progressBar\", 228);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"div\", 229);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 230)(79, \"div\", 225);\n    i0.ɵɵtext(80, \"4\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(81, \"em\", 226);\n    i0.ɵɵelementStart(82, \"div\", 227);\n    i0.ɵɵelement(83, \"p-progressBar\", 228);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 229);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 230)(87, \"div\", 225);\n    i0.ɵɵtext(88, \"3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(89, \"em\", 226);\n    i0.ɵɵelementStart(90, \"div\", 227);\n    i0.ɵɵelement(91, \"p-progressBar\", 228);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"div\", 229);\n    i0.ɵɵtext(93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"div\", 230)(95, \"div\", 225);\n    i0.ɵɵtext(96, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(97, \"em\", 226);\n    i0.ɵɵelementStart(98, \"div\", 227);\n    i0.ɵɵelement(99, \"p-progressBar\", 228);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"div\", 229);\n    i0.ɵɵtext(101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 230)(103, \"div\", 225);\n    i0.ɵɵtext(104, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(105, \"em\", 226);\n    i0.ɵɵelementStart(106, \"div\", 227);\n    i0.ɵɵelement(107, \"p-progressBar\", 228);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(108, \"div\", 229);\n    i0.ɵɵtext(109);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(110, DetailsComponent_div_1_div_110_Template, 2, 4, \"div\", 231);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(111, DetailsComponent_div_1_ng_container_111_Template, 2, 2, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.product == null ? null : ctx_r1.product.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(75, _c10, ctx_r1.selectedVariant.soldOut, ctx_r1.selectedVariant.soldOut === false));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(78, _c11, ctx_r1.selectedVariant.soldOut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.checkUsername(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedVariant && ctx_r1.selectedVariant.rate ? ctx_r1.selectedVariant.rate : 0, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.product.count ? ctx_r1.product.count : 0, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.salePrice) && (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.salePrice) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.salePrice) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.salePrice) === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.colors.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sizes.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.soldOut) === true)(\"label\", i0.ɵɵpipeBind1(21, 57, \"buttons.addToCart\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.soldOut) === true)(\"label\", i0.ɵɵpipeBind1(23, 59, \"buttons.shopNow\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.isLiked) && ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.isLiked) && !ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.isLiked) && ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.isLiked) && !ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.channelId === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.product.channelId === 2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(35, 61, \"productDetails.details.productDetails\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(39, 63, \"productDetails.details.merchantName\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.product == null ? null : ctx_r1.product.sellerName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(45, 65, \"productDetails.details.sku\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.sku);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.product == null ? null : ctx_r1.product.specs == null ? null : ctx_r1.product.specs.Width == null ? null : ctx_r1.product.specs.Width.value) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.specs == null ? null : ctx_r1.selectedVariant.specs.Width == null ? null : ctx_r1.selectedVariant.specs.Width.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.product == null ? null : ctx_r1.product.specs == null ? null : ctx_r1.product.specs.Height == null ? null : ctx_r1.product.specs.Height.value) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.specs == null ? null : ctx_r1.selectedVariant.specs.Height == null ? null : ctx_r1.selectedVariant.specs.Height.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.product == null ? null : ctx_r1.product.specs == null ? null : ctx_r1.product.specs.Weight == null ? null : ctx_r1.product.specs.Weight.value) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.specs == null ? null : ctx_r1.selectedVariant.specs.Weight == null ? null : ctx_r1.selectedVariant.specs.Weight.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.product == null ? null : ctx_r1.product.specs == null ? null : ctx_r1.product.specs.Length == null ? null : ctx_r1.product.specs.Length.value) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.specs == null ? null : ctx_r1.selectedVariant.specs.Length == null ? null : ctx_r1.selectedVariant.specs.Length.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.product == null ? null : ctx_r1.product.specs == null ? null : ctx_r1.product.specs.Gender == null ? null : ctx_r1.product.specs.Gender.value) || (ctx_r1.selectedVariant == null ? null : ctx_r1.selectedVariant.specs == null ? null : ctx_r1.selectedVariant.specs.Gender == null ? null : ctx_r1.selectedVariant.specs.Gender.value));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(56, 67, \"productDetails.details.customerReviews\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(61, 69, \"productDetails.details.overallRating\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.rating === null ? 0 : ctx_r1.rating, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.rating)(\"cancel\", false)(\"readonly\", true)(\"stars\", 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(67, 71, \"productDetails.details.basedOn\"), \" \", ctx_r1.rateCount === null ? 0 : ctx_r1.rateCount, \" \", i0.ɵɵpipeBind1(68, 73, \"productDetails.details.ratings\"), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"showValue\", false)(\"value\", ctx_r1.rate5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.rate5 === null ? 0 : ctx_r1.rate5, \") \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"showValue\", false)(\"value\", ctx_r1.rate4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.rate4 === null ? 0 : ctx_r1.rate4, \") \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"showValue\", false)(\"value\", ctx_r1.rate3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.rate3 === null ? 0 : ctx_r1.rate3, \") \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"showValue\", false)(\"value\", ctx_r1.rate2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.rate2 === null ? 0 : ctx_r1.rate2, \") \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"showValue\", false)(\"value\", ctx_r1.rate1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r1.rate1 === null ? 0 : ctx_r1.rate1, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.reviews == null ? null : ctx_r1.reviews.length) !== 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.displayModal);\n  }\n}\nfunction DetailsComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r265 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-mtn-size-guide-modal\", 273);\n    i0.ɵɵlistener(\"cancel\", function DetailsComponent_ng_container_2_Template_app_mtn_size_guide_modal_cancel_1_listener($event) {\n      i0.ɵɵrestoreView(_r265);\n      const ctx_r264 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r264.onSubmit($event));\n    })(\"submit\", function DetailsComponent_ng_container_2_Template_app_mtn_size_guide_modal_submit_1_listener($event) {\n      i0.ɵɵrestoreView(_r265);\n      const ctx_r266 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r266.onSubmit($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r2.displayModal)(\"sizeGuidImage\", ctx_r2.sizeGuidImage);\n  }\n}\nexport let DetailsComponent = /*#__PURE__*/(() => {\n  class DetailsComponent {\n    productLogicService;\n    messageService;\n    store;\n    fb;\n    router;\n    detailsService;\n    translate;\n    authTokenService;\n    cookieService;\n    userService;\n    modalService;\n    ref;\n    cartService;\n    mainDataService;\n    productService;\n    $gaService;\n    permissionService;\n    route;\n    addressService;\n    _GACustomEvents;\n    sanitizer;\n    platformId;\n    product = {};\n    channelId = '';\n    currency = {};\n    selectedVariant;\n    selectedSize;\n    selectedSize2;\n    selectedColor;\n    cols = [];\n    onItemLike = new EventEmitter();\n    onChangeVariant = new EventEmitter();\n    onResize(event) {\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n    }\n    _BaseURL = environment.apiEndPoint;\n    Variant = {};\n    showMore = false;\n    reviewLimit = 0;\n    cartId = '0';\n    rate1;\n    rate2;\n    rate3;\n    scConfig = false;\n    rate4;\n    rate5;\n    rating;\n    rateCount;\n    reviews;\n    reviewrating;\n    reviewDes;\n    soldOut;\n    decimalValue = 0;\n    productForm;\n    textareaHeight = 0;\n    isDescription = false;\n    currencyCode = '';\n    reviewsLenght;\n    colorsValues = [];\n    colors = [];\n    sizes = [];\n    sizesValues = [];\n    variants = [];\n    variantIds = [];\n    displaySizes = [];\n    displayColors = [];\n    message = '';\n    displayModal = false;\n    displaySuccessModal = false;\n    displayShareModal = false;\n    CustomCountryISO;\n    genderSpeces;\n    isStoreCloud = environment.isStoreCloud;\n    disableCent;\n    authToken;\n    cartListCount = 0;\n    cartListData = [];\n    sizeGuidAttributeId;\n    sizeGuidImage = '';\n    productId;\n    screenWidth;\n    stockStatusId;\n    currentLink;\n    state;\n    isLayoutTemplate = false;\n    isMobileTemplate = false;\n    isShowNotifyFeature = false;\n    userDetails;\n    sessionId;\n    isEmailExist = false;\n    isGoogleAnalytics = false;\n    varianceSpec = [];\n    size1 = [];\n    size2 = [];\n    displayNotifyModal = false;\n    successTitleMessage = '';\n    successBodyMessage = '';\n    profile;\n    tagName = GaActionEnum;\n    tagNameLocal = GaLocalActionEnum;\n    selectedAddress;\n    displayAgeConsentModal = false;\n    displayEligableModal = false;\n    restrictionAge;\n    restrictedProductTobePurchased;\n    sanitizedDescription = '';\n    descriptionBase64 = false;\n    filteredProductSpecs = [];\n    filteredVarianceSpecs = [];\n    canShare = false;\n    // Constructor for the DetailsComponent\n    constructor(\n    // Injects a service to handle product-specific logic\n    productLogicService,\n    // Service to display messages (e.g., success, error, etc.)\n    messageService,\n    // Manages the application's state storage\n    store,\n    // FormBuilder for creating and managing forms\n    fb,\n    // Router service for navigation\n    router,\n    // Service to handle product details\n    detailsService,\n    // Handles translations for internationalization\n    translate,\n    // Service to manage authentication tokens\n    authTokenService,\n    // Service to handle browser cookies\n    cookieService,\n    // Service to manage user-related functionalities\n    userService,\n    // Service to manage modals\n    modalService,\n    // Change detection to force UI updates\n    ref,\n    // Service to manage shopping cart operations\n    cartService,\n    // Service to handle core data operations\n    mainDataService,\n    // Service to handle API requests for product-related operations\n    productService,\n    // Google Analytics service for tracking events\n    $gaService,\n    // Service to check user permissions\n    permissionService,\n    // Activated route to access route parameters and query strings\n    route,\n    // Service to manage user address data\n    addressService, _GACustomEvents, sanitizer,\n    // Injects the platform ID to differentiate between browser and server environments\n    platformId) {\n      this.productLogicService = productLogicService;\n      this.messageService = messageService;\n      this.store = store;\n      this.fb = fb;\n      this.router = router;\n      this.detailsService = detailsService;\n      this.translate = translate;\n      this.authTokenService = authTokenService;\n      this.cookieService = cookieService;\n      this.userService = userService;\n      this.modalService = modalService;\n      this.ref = ref;\n      this.cartService = cartService;\n      this.mainDataService = mainDataService;\n      this.productService = productService;\n      this.$gaService = $gaService;\n      this.permissionService = permissionService;\n      this.route = route;\n      this.addressService = addressService;\n      this._GACustomEvents = _GACustomEvents;\n      this.sanitizer = sanitizer;\n      this.platformId = platformId;\n      // Checks if the environment is configured for store cloud\n      this.scConfig = environment.isStoreCloud;\n      // Checks if the user has permission for layout templates\n      this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n      // Checks if the user has permission for mobile layout templates\n      this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n      // Captures the current router state snapshot\n      this.state = router.routerState.snapshot;\n      // Retrieves a flag to disable displaying cents in prices\n      this.disableCent = localStorage.getItem('DisableCents');\n      // Retrieves and parses the decimal value for price formatting\n      let value = localStorage.getItem('CurrencyDecimal');\n      if (value) this.decimalValue = parseInt(value);\n      // Initializes the form group with a description field\n      this.productForm = this.fb.group({\n        description: new FormControl('')\n      });\n      // If running in a browser environment\n      if (isPlatformBrowser(this.platformId)) {\n        // Sets the initial screen width\n        this.screenWidth = window.innerWidth;\n        // Captures the current page URL\n        this.currentLink = window.location.href;\n      }\n      // Initialize gender specifications with translations\n      this.genderSpeces = {\n        1: this.translate.instant('productDetails.details.male'),\n        2: this.translate.instant('productDetails.details.female'),\n        3: this.translate.instant('productDetails.details.uniSex')\n      };\n    }\n    /**\r\n     * Initializes the component when it is first loaded.\r\n     * This method performs various setup tasks such as retrieving route parameters,\r\n     * setting up user profile data, fetching product details, and initializing UI states.\r\n     */\n    ngOnInit() {\n      // Retrieve the full URL of the current page\n      const fullUrl = window.location.href;\n      let productId;\n      let tenantId;\n      // Subscribe to query parameters to retrieve the tenant ID\n      this.route.queryParams.subscribe(params => {\n        tenantId = params.tenantId;\n      });\n      // Subscribe to route parameters to retrieve the product ID\n      this.route.params.subscribe(params => {\n        productId = params.id;\n      });\n      // Load user profile and session data from local storage\n      this.profile = localStorage.getItem('profile');\n      this.userDetails = this.store.get('profile');\n      this.sessionId = localStorage.getItem('sessionId');\n      // Check for specific permissions using the permission service\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      this.isShowNotifyFeature = this.permissionService.hasPermission('Notify-Me');\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n      // Update the scroll position via the user service\n      this.userService.updateScrollTop(true);\n      // If running in a browser environment, reset the scroll position to the top\n      if (isPlatformBrowser(this.platformId)) {\n        window.scroll({\n          top: 0,\n          left: 0,\n          behavior: 'smooth'\n        });\n        // Smooth scroll to the top\n        const scrollToTop = window.setInterval(() => {\n          let pos = window.scrollY ?? null;\n          if (pos > 0) {\n            window.scrollTo(0, pos - 50);\n          } else {\n            window.clearInterval(scrollToTop);\n          }\n        }, 10);\n      }\n      // Fetch customer address if the user profile exists\n      if (this.profile) {\n        this.getCustomerAddress();\n      }\n      this.getFilteredProductSpec();\n      this.getFilteredVarianceSpec();\n      // If the product has a description, initialize the description field\n      if (this.product.description) {\n        // Check if the description is Base64 encoded\n        this.descriptionBase64 = this.isBase64(this.product.description);\n        // Decode if Base64, otherwise keep the original\n        this.product.description = this.descriptionBase64 ? decodeURIComponent(escape(atob(this.product.description))) : this.product.description;\n        // Update the form control with a truncated version of the description\n        this.productForm.controls['description'].setValue(this.product.description.substring(0, 150) + '.....');\n        this.isDescription = true;\n        this.updateSanitizedDescription(this.product.description);\n      }\n      // Initialize product variants and ID\n      this.variants = this.product.productVariances;\n      this.productId = this.product.id;\n      // Fetch additional data and refresh the UI\n      this.setData();\n      this.ref.markForCheck();\n      this.ref.detectChanges();\n      // Retrieve the cart ID\n      this.getCartId();\n      // Set product rating and review data\n      this.rating = this.selectedVariant?.rate;\n      this.rateCount = this.selectedVariant?.count;\n      this.rate1 = this.selectedVariant?.rateOne;\n      this.rate2 = this.selectedVariant?.rateTwo;\n      this.rate3 = this.selectedVariant?.rateThree;\n      this.rate4 = this.selectedVariant?.rateFour;\n      this.rate5 = this.selectedVariant?.rateFive;\n      this.reviews = this.selectedVariant?.reviewDetails;\n      this.reviewsLenght = this.selectedVariant?.reviewDetails?.length;\n      // Set the initial height of the description textarea\n      this.textareaHeight = 70;\n      this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n      this.canShare = 'share' in navigator;\n    }\n    /**\r\n     * Computes the intersection of two arrays.\r\n     *\r\n     * @param arr1 - The first array of elements.\r\n     * @param arr2 - The second array of elements.\r\n     * @returns An array containing elements that are common to both input arrays.\r\n     */\n    performIntersection(arr1, arr2) {\n      // Convert the input arrays into sets for efficient lookup\n      const setA = new Set(arr1);\n      const setB = new Set(arr2);\n      // Initialize an array to store the intersection result\n      let intersectionResult = [];\n      // Iterate through elements in the second set\n      for (let i of setB) {\n        // If the element exists in the first set, add it to the result\n        if (setA.has(i)) {\n          intersectionResult.push(i);\n        }\n      }\n      // Return the array of intersecting elements\n      return intersectionResult;\n    }\n    /**\r\n     * Toggles the \"Read More\" functionality for the product description.\r\n     *\r\n     * When expanded, the full product description is displayed, and the textarea height is adjusted based on the number of lines.\r\n     * When collapsed, only a truncated version of the description is displayed with a fixed height.\r\n     */\n    readMore() {\n      if (!this.showMore) {\n        // Expand: Set the full product description and calculate the textarea height based on the number of lines.\n        this.productForm.controls['description'].setValue(this.product.description);\n        let lines = this.productForm.controls['description'].value.split(/\\s+/).length;\n        this.textareaHeight = (15 + lines) * 2; // Adjust the height dynamically.\n      } else {\n        // Collapse: Set a truncated version of the description and reset the height.\n        this.productForm.controls['description'].setValue(this.product.description.substring(0, 150) + '.....');\n        this.textareaHeight = 70; // Set to default height.\n      }\n      // Toggle the \"showMore\" flag to switch between expanded and collapsed states.\n      this.showMore = !this.showMore;\n    }\n    /**\r\n     * Adds a product to the cart and sends tracking data to Google Analytics if enabled.\r\n     *\r\n     * @param productItem - The specific product variant to be added to the cart.\r\n     * @param shopId - The ID of the shop from which the product is being added.\r\n     * @param productDetails - The general product details including name, category, and seller information.\r\n     */\n    addItem(productItem, shopId, productDetails) {\n      // Track the \"Add to Cart\" event in Google Analytics if enabled.\n      if (this.isGoogleAnalytics) {\n        this._GACustomEvents.addToCartEvent(productItem, productDetails);\n      }\n      // Do not proceed if the product is sold out.\n      if (this.product.soldOut) return;\n      // Assign the current cart ID to the product item.\n      productItem.cartId = this.cartId;\n      let isTrigger = true;\n      // Retrieve current cart data and handle adding the product.\n      this.mainDataService.getCartItemsData().subscribe(res => {\n        if (isTrigger) {\n          if (res && res.length > 0) {\n            // Check if the product already exists in the cart.\n            let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n            if (cartProduct) {\n              // Prevent exceeding item-per-customer limits if applicable.\n              if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n                this.messageService.add({\n                  severity: 'error',\n                  summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n                });\n                isTrigger = false;\n                return;\n              }\n            }\n            // Prepare the request object for adding the product to the cart.\n            const reqObj = {\n              productId: this.product.id,\n              quantity: 1,\n              specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n              cartId: this.cartId,\n              priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n              shopId: shopId,\n              sessionId: productItem.sessionId,\n              channelId: this.product?.channelId,\n              proSchedulingId: productItem.proSchedulingId\n            };\n            isTrigger = false;\n            this.createCart(reqObj, productDetails);\n          } else {\n            // Add the product as a new item in the cart.\n            const reqObj = {\n              productId: this.product.id,\n              quantity: 1,\n              specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n              cartId: this.cartId,\n              priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n              shopId: shopId,\n              sessionId: productItem.sessionId,\n              channelId: this.product?.channelId,\n              proSchedulingId: productItem.proSchedulingId\n            };\n            isTrigger = false;\n            this.createCart(reqObj, productDetails);\n          }\n        }\n      });\n    }\n    /**\r\n     * Creates a cart entry for the given product and handles the response from the API.\r\n     *\r\n     * @param product - The product object containing details required for adding to the cart.\r\n     * @param productDetails - Additional details of the product, such as name and category.\r\n     * @param navigate - Optional flag to indicate whether to navigate after adding the product.\r\n     */\n    createCart(product, productDetails, navigate) {\n      // Ensure the product has a session ID; generate and store one if missing.\n      product.sessionId = localStorage.getItem('sessionId');\n      if (!product.sessionId) {\n        product.sessionId = GuidGenerator.newGuid();\n        localStorage.setItem('sessionId', product.sessionId);\n      }\n      // Call the cart service to add the product to the cart.\n      this.cartService.addToCart(product).subscribe({\n        next: res => {\n          // Handle successful addition to the cart.\n          if (!res.data.userFailedProductEligibility) {\n            if (res?.success) {\n              // Store the cart ID in localStorage.\n              localStorage.setItem(\"cartId\", res.data.cartItems[0].cartId);\n              // Show a success message.\n              this.messageService.add({\n                severity: 'success',\n                summary: this.translate.instant('ResponseMessages.cart'),\n                detail: this.translate.instant('ResponseMessages.successfullyAddedToCart')\n              });\n              // Fetch the updated cart items.\n              this.getAllCart(product.sessionId, navigate);\n            } else {\n              // Show an error message if the cart operation failed.\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ResponseMessages.cart'),\n                detail: res.message\n              });\n            }\n          } else {\n            // Handle age restriction for the product.\n            this.restrictionAge = res.data.productAgeRestriction;\n            this.restrictedProductTobePurchased = {\n              product: product,\n              productDetails: productDetails,\n              navigate: navigate\n            };\n            this.displayAgeConsentModal = true;\n          }\n        },\n        error: err => {\n          // Show an error message for any API errors.\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        }\n      });\n    }\n    /**\r\n     * Handles the \"Buy Now\" action for a product, adding it to the cart and navigating to checkout.\r\n     *\r\n     * @param productItem - The specific product item being purchased.\r\n     * @param shopId - The ID of the shop where the product is sold.\r\n     * @param productDetails - Additional details about the product, such as name and category.\r\n     */\n    shopNow(productItem, shopId, productDetails) {\n      // Trigger Google Analytics event for \"Buy Now\" action, if enabled.\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(this.tagNameLocal.CLICK_ON_BUY_NOW, 'product', 'BUY_NOW', 1, true, {\n          product_ID: productDetails.id,\n          product_name: productDetails.name,\n          category_name: productDetails.categoryName,\n          user_ID: this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          session_ID: this.sessionId,\n          shop_ID: productDetails.shopId,\n          product_SKU: productItem?.skuAutoGenerated,\n          seller_name: productDetails?.sellerName,\n          ip_Address: this.store.get('userIP'),\n          device_Type: this.store.get('deviceInfo')?.deviceType,\n          device_Id: this.store.get('deviceInfo')?.deviceId,\n          product_tags: productItem?.bestSeller ? 'Best Seller' : productItem?.newArrival ? 'New Arrival' : productItem?.hotDeals ? 'Hot Deals' : 'None',\n          promotion: productItem?.promotionName ? productItem?.promotionName : 'None'\n        });\n      }\n      // Return if the product is sold out.\n      if (this.product.soldOut) return;\n      // Assign cart ID to the product item.\n      productItem.cartId = this.cartId;\n      let isTrigger = true;\n      // Fetch existing cart data to manage quantities and avoid duplicates.\n      this.mainDataService.getCartItemsData().subscribe(res => {\n        if (isTrigger) {\n          if (res && res.length > 0) {\n            // Check if the product already exists in the cart and manage customer quantity limits.\n            let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n            if (cartProduct) {\n              if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n                this.messageService.add({\n                  severity: 'error',\n                  summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n                });\n                isTrigger = false;\n                return;\n              }\n            }\n            // Construct the request object and proceed to add the product to the cart.\n            const reqObj = {\n              productId: this.product.id,\n              quantity: 1,\n              specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n              cartId: this.cartId,\n              priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n              shopId: shopId,\n              sessionId: productItem.sessionId,\n              channelId: this.product?.channelId\n            };\n            isTrigger = false;\n            this.createCart(reqObj, productDetails, true);\n          } else {\n            // If the cart is empty, create a new cart item.\n            const reqObj = {\n              productId: this.product.id,\n              quantity: 1,\n              specsProductId: this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n              cartId: this.cartId,\n              priceId: this.product.channelId == '1' ? productItem.priceId : '0',\n              shopId: shopId,\n              sessionId: productItem.sessionId,\n              channelId: this.product?.channelId\n            };\n            isTrigger = false;\n            this.createCart(reqObj, productDetails, true);\n          }\n        }\n      });\n    }\n    /**\r\n     * Retrieves the cart ID from the store's \"cartProducts\" subscription.\r\n     * Updates the `cartId` property if cart products exist in the store.\r\n     */\n    getCartId() {\n      // Subscribe to the \"cartProducts\" observable in the store.\n      this.store.subscription('cartProducts').subscribe({\n        // On successful retrieval of cart products.\n        next: res => {\n          // If cart products exist, update the cartId with the first product's cartId.\n          if (res.length > 0) {\n            this.cartId = res[0].cartId;\n          }\n        },\n        // Handle any errors encountered during the subscription.\n        error: err => {\n          console.error(err); // Log the error for debugging purposes.\n        }\n      });\n    }\n    /**\r\n     * Checks the stock status of the selected variant and returns an appropriate message.\r\n     *\r\n     * @returns {string} - Translated message indicating whether the product is in stock or sold out.\r\n     */\n    checkUsername() {\n      // If the selected variant is not sold out, return the \"in stock\" message.\n      if (!this.selectedVariant?.soldOut) {\n        return this.translate.instant('productCard.instock');\n      }\n      // Otherwise, return the \"sold out\" message.\n      else {\n        return this.translate.instant('productCard.soldOut');\n      }\n    }\n    /**\r\n     * Toggles the wishlist status for a product. Adds or removes the product from the wishlist based on the current state.\r\n     *\r\n     * @param {any} specsProductId - The ID of the specific product variant to add/remove.\r\n     * @param {any} flag - Indicates whether the product is currently in the wishlist (true = remove, false = add).\r\n     * @param {Product} productDetails - Details of the product to be added/removed from the wishlist.\r\n     */\n    addToWishlist(specsProductId, flag, productDetails) {\n      // Track \"add to wishlist\" event in Google Analytics if enabled.\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event('add_to_wishlist', 'product', specsProductId);\n      }\n      // Retrieve authentication token from the service or cookies.\n      this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n      if (!this.authToken) {\n        this.authToken = this.cookieService.get('authToken');\n      }\n      // If the user is not authenticated, redirect to the login page.\n      if (!this.authToken) {\n        this.router.navigate(['login'], {\n          queryParams: {\n            returnUrl: this.state.url\n          }\n        });\n        return;\n      }\n      // Prepare the request object for toggling the wishlist status.\n      const obj = {\n        specsProductId,\n        flag,\n        productId: this.product.id,\n        channelId: this.product.channelId\n      };\n      // Call the wishlist toggle service and handle the response.\n      this.detailsService.wishlistToggle(obj).subscribe({\n        next: res => {\n          if (res?.success) {\n            // Toggle the \"isLiked\" state for the selected variant.\n            this.selectedVariant.isLiked = !this.selectedVariant.isLiked;\n            if (!flag) {\n              // Handle successful addition to the wishlist.\n              if (this.isGoogleAnalytics) {\n                this._GACustomEvents.addToWishlistEvent(productDetails, this.selectedVariant);\n                this.$gaService.event(this.tagName.ADD_TO_WISHLIST, productDetails.categoryName, productDetails.name, 1, true, {\n                  \"product_ID\": productDetails.id,\n                  \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                  \"session_ID\": this.sessionId,\n                  \"shop_ID\": productDetails.shopId\n                });\n              }\n              this.messageService.add({\n                severity: 'success',\n                summary: this.translate.instant('ResponseMessages.wishList'),\n                detail: this.translate.instant('ResponseMessages.successfullyAddedToWishList')\n              });\n            } else {\n              // Handle successful removal from the wishlist.\n              if (this.isGoogleAnalytics) {\n                this.$gaService.event(\"remove_from_wishlist\", productDetails.categoryName, 'DELETE_FROM_CART', 1, true, {\n                  \"product_name\": productDetails.name,\n                  \"product_ID\": productDetails.id,\n                  \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                  \"session_ID\": this.sessionId,\n                  \"shop_ID\": productDetails.shopId\n                });\n              }\n              this.messageService.add({\n                severity: 'success',\n                summary: this.translate.instant('ResponseMessages.wishList'),\n                detail: this.translate.instant('ResponseMessages.successfullyRemovedToWishList')\n              });\n            }\n          }\n        },\n        error: err => {\n          // Handle errors during the wishlist toggle operation.\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        }\n      });\n    }\n    /**\r\n     * Handles the selection of a size attribute for a product variant.\r\n     * Updates the selected size, selected variant, and emits the updated variant.\r\n     *\r\n     * @param {any} size - The size object that the user selects.\r\n     * @param {number} type - The type of size selection (0 for the first size group, 1 for the second).\r\n     */\n    onSizeChange(size, type) {\n      // Update the selected size or size2 based on the type\n      if (type === 0) {\n        this.selectedSize = size.value; // Update primary size\n      } else {\n        this.selectedSize2 = size.value; // Update secondary size\n      }\n      // Filter available variants dynamically based on selected attributes\n      const matchingVariants = this.product.productVariances.filter(variant => {\n        const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name === \"Size\" && spec.value === this.selectedSize) : true; // Match any variant if no size is selected\n        const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name === \"Size 2\" && spec.value === this.selectedSize2) : true; // Match any variant if no size 2 is selected\n        return sizeMatch && size2Match;\n      });\n      // If matching variants exist, select the first one and update attributes\n      if (matchingVariants.length > 0) {\n        this.selectedVariant = matchingVariants[0];\n        this.selectedColor = this.selectedVariant.color || this.selectedColor;\n        this.selectedSize = this.selectedVariant.varianceSpecs.find(spec => spec.name === \"Size\")?.value || this.selectedSize;\n        this.selectedSize2 = this.selectedVariant.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || this.selectedSize2;\n      } else {\n        // If no matching variants exist, reset the selected variant\n        this.selectedVariant = null;\n      }\n      // Emit the updated variant for other components\n      this.onChangeVariant.emit(this.selectedVariant);\n      // Trigger Angular change detection to update the UI\n      this.ref.detectChanges();\n      this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n    }\n    /**\r\n     * Handles the selection of a color attribute for a product variant.\r\n     * Updates the selected color, adjusts available sizes, determines the appropriate variant, and emits the updated variant.\r\n     *\r\n     * @param {any} color - The color object that the user selects.\r\n     */\n    onColorChange(color) {\n      // Update the selected color\n      this.selectedColor = color.value;\n      // Filter variants for the selected color\n      this.variants = this.product.productVariances.filter(variant => variant.color === this.selectedColor);\n      // Find a matching variant based on the current size and size2 selections\n      const matchingVariant = this.variants.find(variant => {\n        const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name === \"Size\" && spec.value === this.selectedSize) : true;\n        const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name === \"Size 2\" && spec.value === this.selectedSize2) : true;\n        return sizeMatch && size2Match;\n      });\n      // If a matching variant exists, update the selected attributes\n      if (matchingVariant) {\n        this.selectedVariant = matchingVariant;\n        this.selectedSize = matchingVariant.varianceSpecs.find(spec => spec.name === \"Size\")?.value || this.selectedSize;\n        this.selectedSize2 = matchingVariant.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || this.selectedSize2;\n      } else {\n        // If no matching variant exists, fallback to the first variant for the selected color\n        this.selectedVariant = this.variants[0] || null;\n        this.selectedSize = this.selectedVariant?.varianceSpecs.find(spec => spec.name === \"Size\")?.value || null;\n        this.selectedSize2 = this.selectedVariant?.varianceSpecs.find(spec => spec.name === \"Size 2\")?.value || null;\n      }\n      // Update the display sizes to reflect the selected color\n      this.displaySizes = this.variants.flatMap(variant => variant.varianceSpecs.filter(spec => spec.name === \"Size\").map(spec => spec.value));\n      // Emit the updated variant\n      this.onChangeVariant.emit(this.selectedVariant);\n      // Trigger change detection to update the UI\n      this.ref.detectChanges();\n      this._GACustomEvents.viewItemEvent(this.selectedVariant, this.product);\n    }\n    /**\r\n     *\r\n     * @param variant  - The variant object that the user selects.\r\n     * @returns  - A boolean value indicating whether the selected variant is available for the current color.\r\n     */\n    isVariantAvailable(variant) {\n      // Match selected color\n      const colorMatch = this.selectedColor ? variant.color === this.selectedColor : true;\n      // Match selected size (if exists)\n      const sizeMatch = this.selectedSize ? variant.varianceSpecs.some(spec => spec.name.toLowerCase().includes(\"size\") && spec.value === this.selectedSize) : true;\n      // Match selected size2 (if exists)\n      const size2Match = this.selectedSize2 ? variant.varianceSpecs.some(spec => spec.name.toLowerCase().includes(\"size 2\") && spec.value === this.selectedSize2) : true;\n      // Return true if the variant matches all selected attributes\n      return colorMatch && sizeMatch && size2Match;\n    }\n    productImage = '';\n    productPrice = '';\n    productLowStock = '';\n    onVariantChange(variant) {\n      if (this.isVariantAvailable(variant)) {\n        this.selectedVariant = variant;\n        // Dynamically resolve attribute names\n        const colorAttribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Color')?.attributeName || 'Color';\n        const sizeAttribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Size')?.attributeName || 'Size';\n        const size2Attribute = this.cols.find(col => col.templateDefaultFields.fieldMapping === 'Size 2')?.attributeName || 'Size 2';\n        // Dynamically update selected attributes\n        this.selectedColor = variant.varianceSpecs.find(spec => spec.name === colorAttribute)?.value || this.selectedColor;\n        this.selectedSize = variant.varianceSpecs.find(spec => spec.name === sizeAttribute)?.value || this.selectedSize;\n        this.selectedSize2 = variant.varianceSpecs.find(spec => spec.name === size2Attribute)?.value || this.selectedSize2;\n        // Emit the updated variant\n        this.onChangeVariant.emit(this.selectedVariant);\n        // Trigger change detection\n        this.ref.detectChanges();\n      }\n    }\n    /**\r\n     *\r\n     * @param color  - The color object that the user selects.\r\n     * @returns  - A boolean value indicating whether the selected color has available variants.\r\n     */\n    isColorAvailable(color) {\n      // Check if the color has available variants\n      return this.variants.some(variant => variant.color === color.value);\n    }\n    /**\r\n     * Checks whether a size is available for the selected color.\r\n     * Returns false for all sizes if no variants exist for the selected color.\r\n     *\r\n     * @param {any} size - The size object to check.\r\n     * @returns {boolean} - True if the size is available, false otherwise.\r\n     */\n    isSizeAvailable(size) {\n      // Check size availability based on variants\n      return this.variants.some(variant => variant.varianceSpecs.some(spec => spec.value === size.value));\n    }\n    /**\r\n     * Updates the selected sizes (Size and Size 2) based on the currently selected variant's specifications.\r\n     * This ensures that the selected size attributes reflect the active variant's data.\r\n     */\n    getVariantSelectedSize() {\n      // Check if the current variant has a specification named \"Size\" and update the selected size accordingly.\n      if (this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size\")) {\n        this.selectedSize = this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size\").value;\n      }\n      // Check if the current variant has a specification named \"Size 2\" and update the selected size2 accordingly.\n      if (this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size 2\")?.value) {\n        this.selectedSize2 = this.selectedVariant?.varianceSpecs.find(item => item.name === \"Size 2\").value;\n      }\n      // Trigger Angular's change detection to update the UI with the new size selections.\n      this.ref.markForCheck();\n      this.ref.detectChanges();\n    }\n    /**\r\n     * Displays the size guide modal to the user.\r\n     * This method sets the `displayModal` flag to `true`, triggering the modal to appear.\r\n     */\n    showSizeModal() {\n      this.displayModal = true; // Set the flag to display the size guide modal.\n    }\n    /**\r\n     * Fetches the size guide image based on the provided size guide attribute ID.\r\n     * This method calls the `getSizeGuidDetails` service to retrieve the size guide details.\r\n     * If an image URL is returned, it verifies and assigns the URL to `sizeGuidImage`.\r\n     *\r\n     * @param sizeGuidAttributeId - The ID of the size guide attribute.\r\n     */\n    getSizeGuideImage(sizeGuidAttributeId) {\n      this.productService.getSizeGuidDetails(sizeGuidAttributeId).subscribe({\n        next: res => {\n          if (res?.data?.imageUrl) {\n            // Verify and set the size guide image URL.\n            this.sizeGuidImage = UtilityFunctions.verifyImageURL(res?.data?.imageUrl, this._BaseURL);\n          }\n        },\n        error: () => {\n          //TODO: Handle errors silently.\n          // Handle errors silently.\n        }\n      });\n    }\n    /**\r\n     * Handles the submission of a form or action.\r\n     * This method is typically triggered by a form submission event.\r\n     * It closes the modal by setting `displayModal` to `false`.\r\n     *\r\n     * @param event - The event object triggered on form submission.\r\n     */\n    onSubmit(event) {\n      this.displayModal = false;\n    }\n    /**\r\n     * Fetches all cart items associated with a specific session and optionally navigates to the cart page.\r\n     * Updates the cart list count and data based on the API response, and synchronizes this data with the application state.\r\n     *\r\n     * @param sessionId - The session ID to identify the user's cart.\r\n     * @param navigate - A boolean flag indicating whether to navigate to the cart page after fetching data.\r\n     */\n    getAllCart(sessionId, navigate) {\n      let cartData = {\n        sessionId: sessionId\n      };\n      // Include \"applyTo\" parameter if it exists in local storage\n      let applyTo = localStorage.getItem('apply-to');\n      if (applyTo) {\n        cartData['applyTo'] = applyTo;\n      }\n      // Fetch cart data using the CartService\n      this.cartService.getCart(cartData).subscribe({\n        next: res => {\n          this.cartListCount = 0;\n          this.cartListData = [];\n          if (res.data?.records?.length) {\n            this.cartListCount = 0;\n            // Update cart count and data from regular cart details\n            if (res.data.records[0].cartDetails.length) {\n              this.cartListCount = res.data.records[0].cartDetails.length;\n              this.cartListData = res.data.records[0].cartDetails;\n            }\n            // Update cart count and data from DPay cart details\n            if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n              this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n              this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n            }\n            // Synchronize cart data with the application state\n            this.mainDataService.setCartLenghtData(this.cartListCount);\n            this.mainDataService.setCartItemsData(this.cartListData);\n          } else {\n            // Reset cart data if no records exist\n            this.mainDataService.setCartLenghtData(0);\n            this.mainDataService.setCartItemsData([]);\n          }\n          // Navigate to the cart page if the `navigate` flag is true\n          if (navigate) {\n            this.router.navigate(['/cart']);\n          }\n        },\n        error: () => {\n          // Handle any errors that occur during the API call\n        }\n      });\n    }\n    /**\r\n     * Displays the share modal to allow users to share product details.\r\n     * This method simply sets the `displayShareModal` flag to `true`,\r\n     * triggering the modal to appear in the UI.\r\n     */\n    showShareModal() {\n      if (!this.canShare) {\n        this.displayShareModal = true;\n      } else {\n        this.shareContent();\n      }\n    }\n    /**\r\n     * Copies the provided link to the user's clipboard.\r\n     *\r\n     * Steps:\r\n     * - Creates a temporary, invisible `textarea` element to hold the link.\r\n     * - Selects the link and copies it to the clipboard using the `navigator.clipboard.writeText` API.\r\n     * - Removes the temporary `textarea` element after copying.\r\n     * - Closes the share modal (`displayShareModal`) and shows a success modal with a message indicating\r\n     *   the link was copied successfully.\r\n     *\r\n     * @param val - The URL or string to be copied to the clipboard.\r\n     */\n    copyLink(val) {\n      if (isPlatformBrowser(this.platformId)) {\n        const selBox = document.createElement('textarea');\n        selBox.style.position = 'fixed';\n        selBox.style.left = '0';\n        selBox.style.top = '0';\n        selBox.style.opacity = '0';\n        selBox.value = val;\n        document.body.appendChild(selBox);\n        selBox.focus();\n        selBox.select();\n        navigator.clipboard.writeText(this.currentLink);\n        document.body.removeChild(selBox);\n      }\n      // Close the share modal and display success message\n      this.displayShareModal = false;\n      this.successTitleMessage = this.translate.instant(\"productDetails.details.linkIsCopiedSuccessfully\");\n      // this.displaySuccessModal = true;\n    }\n    /**\r\n     * Opens the \"Notify Me\" modal to allow the user to subscribe for notifications about a product.\r\n     *\r\n     * Steps:\r\n     * - Sets the `displayNotifyModal` flag to `true` to display the modal.\r\n     * - Checks the user's profile data from `localStorage` to determine if an email address exists.\r\n     * - If the user's profile contains an email, sets `isEmailExist` to `true`; otherwise, sets it to `false`.\r\n     */\n    notifyMe() {\n      this.displayNotifyModal = true;\n      // Retrieve and parse the user's profile data from localStorage\n      const profile = JSON.parse(localStorage.getItem('profile') ?? '');\n      // Check if the profile contains an email and update `isEmailExist` accordingly\n      if (profile?.email) {\n        this.isEmailExist = true;\n      } else {\n        this.isEmailExist = false;\n      }\n    }\n    /**\r\n     * Handles the submission of the \"Notify Me\" form.\r\n     *\r\n     * Steps:\r\n     * 1. Constructs a request object (`reqObj`) to store notification details such as email and phone number.\r\n     * 2. Checks if the product has variances and includes the `specProductId` in the request object.\r\n     * 3. Processes the user's phone input, formatting it to include the dial code and number without spaces.\r\n     * 4. Calls the `notifyMeProduct` service to submit the notification request.\r\n     * 5. On success:\r\n     *    - Sets success messages using translations.\r\n     *    - Displays the success modal and hides the notify modal.\r\n     *\r\n     * @param data - Form data containing user inputs such as email and phone number.\r\n     */\n    onSubmitNotify(data) {\n      let reqObj = {};\n      // Set the email in the request object, or leave it empty if not provided\n      reqObj['email'] = data.email ? data.email : '';\n      // Include the specProductId if the product has variances\n      if (this.product.productVariances.length) {\n        reqObj['specProductId'] = this.product.productVariances[0].specProductId;\n      }\n      // Format and include the phone number if provided, or set it to an empty string\n      if (data.phone) {\n        let dialCode = data.phone.dialCode.substring(1, 4);\n        reqObj['phoneNumber'] = dialCode + data.phone.number.replace(/\\s/g, '');\n      } else {\n        reqObj['phoneNumber'] = '';\n      }\n      // Call the service to submit the \"Notify Me\" request\n      this.productService.notifyMeProduct(reqObj).subscribe(res => {\n        if (res.success) {\n          // Display success messages and modal\n          this.successTitleMessage = this.translate.instant(\"notifyMeDetails.thanksForInterest\");\n          this.successBodyMessage = this.translate.instant(\"notifyMeDetails.notifyProductIsAvaialble\");\n          this.displaySuccessModal = true;\n          this.displayNotifyModal = false;\n        }\n      });\n    }\n    /**\r\n     * Handles the cancellation of modals.\r\n     *\r\n     * This function is used to close both the success modal and the \"Notify Me\" modal\r\n     * by setting their respective display flags to `false`.\r\n     */\n    onCancel() {\n      this.displaySuccessModal = false; // Close the success modal\n      this.displayNotifyModal = false; // Close the \"Notify Me\" modal\n    }\n    /**\r\n     * Processes product variant data to prepare for rendering and interaction.\r\n     *\r\n     * - This function extracts and organizes the `colors` and `sizes` data from the product's variants.\r\n     * - For each variant, it adds color and size information, ensuring unique entries in the respective arrays.\r\n     * - Links sizes to colors, and vice versa, to maintain associations between attributes.\r\n     * - Sets up size guide images if available for the first size variant.\r\n     */\n    setData() {\n      // Iterate through each product variant\n      this.product.productVariances.forEach((variant, index) => {\n        this.variantIds.push(variant.specProductId);\n        // Handle color attributes\n        if (variant.color) {\n          if (!this.colorsValues.includes(variant.color)) {\n            this.colors.push({\n              value: variant.color,\n              variants: [variant],\n              variantSpecIds: [variant.specProductId],\n              sizes: []\n            });\n            this.colorsValues.push(variant.color);\n          } else {\n            let index = this.colorsValues.indexOf(variant.color);\n            this.colors[index].variants.push(variant);\n            this.colors[index].variantSpecIds.push(variant.specProductId);\n          }\n        }\n        // Process additional specs for marketplace products\n        if (this.product.channelId == '1') {\n          variant.varianceSpecs.forEach(specs => {\n            // Handle size attributes (Size and Size 2)\n            if (specs.name === \"Size\" || specs.name === 'Size 2') {\n              if (index === 0) {\n                this.getSizeGuideImage(specs.attributeId);\n              }\n              if (!this.sizesValues.includes(specs.value)) {\n                this.sizes.push({\n                  value: specs.value,\n                  name: specs.name,\n                  variants: [variant],\n                  variantSpecIds: [variant.specProductId],\n                  colors: [],\n                  attributeValueId: specs.attributeValueId\n                });\n                this.sizesValues.push(specs.value);\n              } else {\n                let index = this.sizesValues.indexOf(specs.value);\n                this.sizes[index].variants.push(variant);\n                this.sizes[index].variantSpecIds.push(variant.specProductId);\n              }\n            }\n          });\n        }\n      });\n      // Associate sizes with colors and manage display sizes\n      this.colors.forEach(color => {\n        if (color.value === this.selectedColor) {\n          if (color.sizes.length) {\n            this.variants = this.performIntersection(this.variants, color.variants);\n          }\n          this.displaySizes = color.sizes;\n          this.size1 = this.sizes.filter(item => item.name === 'Size');\n          this.size2 = this.sizes.filter(item => item.name === 'Size 2');\n        }\n        this.sizes.forEach(size => {\n          let relateSize = this.performIntersection(size.variants, color.variants);\n          if (relateSize.length > 0) {\n            color.sizes.push(size);\n          }\n        });\n      });\n      // Finalize color-size associations\n      this.setColorsSize();\n    }\n    /**\r\n     * Associates colors and sizes based on their shared variants.\r\n     *\r\n     * - Iterates through the list of `sizes` to link associated `colors` based on variant intersections.\r\n     * - For each size:\r\n     *   - Filters and updates the list of associated colors.\r\n     *   - Narrows down the variants based on the selected size and its associated colors.\r\n     * - If no colors are available, defaults the display to all sizes and segregates `size1` and `size2` based on their names.\r\n     */\n    setColorsSize() {\n      // Loop through each size to associate related colors\n      this.sizes.forEach(size => {\n        if (size.value === this.selectedSize) {\n          // If the size has associated colors, filter the variants\n          if (size.colors.length) {\n            this.variants = this.performIntersection(this.variants, size.variants);\n          }\n          // Update the displayColors to match the colors associated with this size\n          this.displayColors = size.colors;\n        }\n        // Associate colors to the current size based on shared variants\n        this.colors.forEach(color => {\n          let relateColor = this.performIntersection(color.variants, size.variants);\n          if (relateColor.length > 0) {\n            size.colors.push(color);\n          }\n        });\n      });\n      // Default behavior when no colors are available it should display all sizes dimmed\n      if (!this.colors || this.colors.length === 0) {\n        this.displaySizes = this.sizes;\n        this.size1 = this.sizes.filter(item => item.name === 'Size');\n        this.size2 = this.sizes.filter(item => item.name === 'Size 2');\n      }\n    }\n    /**\r\n     * Checks if a label associated with a given key exists and processes variance specifications accordingly.\r\n     *\r\n     * @param {string} key - The field mapping key to search for in the `cols` array.\r\n     * @param {number} [type=0] - An optional parameter (default: 0) that can be used for additional logic if needed in the future.\r\n     * @returns {any[]} - An array of columns that match the given key.\r\n     *\r\n     * **Logic:**\r\n     * - If the `cols` array has elements:\r\n     *   1. Initialize `varianceSpec` as an empty array.\r\n     *   2. Filter `cols` to find items where the `templateDefaultFields.fieldMapping` matches the given key.\r\n     *   3. For each matching item:\r\n     *      - Iterate over the `varianceSpecs` of the `selectedVariant`.\r\n     *      - If the `attributeId` matches, add the spec to `varianceSpec`.\r\n     *   4. Remove duplicates from `varianceSpec`.\r\n     *   5. Return the filtered columns.\r\n     */\n    checkLabel(key, type = 0) {\n      if (this.cols.length) {\n        this.varianceSpec = [];\n        // Filter columns based on the key\n        const data = this.cols.filter(item => item.templateDefaultFields.fieldMapping === key);\n        // Map matching columns to variance specs\n        data.forEach(item => {\n          this.selectedVariant.varianceSpecs.forEach(spec => {\n            if (spec.attributeId === item.attributeId) {\n              this.varianceSpec.push(spec);\n            }\n          });\n        });\n        // Remove duplicate entries from varianceSpec\n        this.varianceSpec = this.removeDuplicates(this.varianceSpec);\n        // Return filtered columns that match the key\n        return this.cols.filter(item => item.templateDefaultFields.fieldMapping === key);\n      }\n    }\n    /**\r\n     * Removes duplicate objects from an array based on a unique `attributeValueId` property.\r\n     *\r\n     * @param {any[]} data - The array of objects to process.\r\n     * @returns {any[]} - A new array with duplicate entries removed.\r\n     *\r\n     * **Logic:**\r\n     * - Iterates over the input array and filters out duplicate entries.\r\n     * - Uses `findIndex` to check if the current item's `attributeValueId` already exists in the array.\r\n     * - Ensures that only the first occurrence of each unique `attributeValueId` is retained.\r\n     * ```\r\n     */\n    removeDuplicates(data) {\n      return data.filter((item, index, self) => index === self.findIndex(t => t.attributeValueId === item.attributeValueId));\n    }\n    /**\r\n     * Retrieves the customer's addresses and sets the default address if available.\r\n     *\r\n     * **Functionality:**\r\n     * - Calls the `getAddress` method from the `AddressService` to fetch the list of customer addresses.\r\n     * - Checks if there are any addresses in the response.\r\n     * - Finds and sets the `selectedAddress` to the address marked as default (`isDefault`).\r\n     *\r\n     * **API Response Handling:**\r\n     * - The `next` block processes the API response.\r\n     * - If the response contains a `records` array, it looks for the address with `isDefault` set to `true`.\r\n     * - No action is taken if no default address is found or if the `records` array is empty.\r\n     *\r\n     * **Example Use Case:**\r\n     * - Useful in applications where the user has multiple saved addresses, and a default address is pre-selected for convenience during checkout.\r\n     */\n    getCustomerAddress() {\n      this.addressService.getAddress().subscribe({\n        next: res => {\n          if (res.data.records.length) {\n            this.selectedAddress = res.data.records.find(item => item.isDefault);\n          }\n        }\n      });\n    }\n    /**\r\n     * Sends an event to Google Analytics for tracking purposes.\r\n     *\r\n     * @param tagNameKey - The key representing the analytics event name.\r\n     * @param label - A label describing the event for context.\r\n     *\r\n     * If Google Analytics is enabled (`isGoogleAnalytics`), this method logs the event using `$gaService.event`.\r\n     */\n    triggerAnalytics(tagNameKey, label) {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(this.tagNameLocal[tagNameKey], '', label, 1, true);\n      }\n    }\n    /**\r\n     * Handles the submission of the age consent modal.\r\n     *\r\n     * This method gathers user session and profile data, creates an age consent object (`AgeConsent`),\r\n     * and sends it to the backend via `userService.updateAgeConsent`. If the backend call is successful,\r\n     * the product is added to the cart. If an error occurs, it is handled gracefully.\r\n     */\n    onSubmitConsent() {\n      // Close the age consent modal\n      this.displayAgeConsentModal = false;\n      // Retrieve the user profile from local storage\n      const userProfile = localStorage.getItem(\"profile\") || '';\n      const userId = userProfile ? JSON.parse(userProfile)?.id : null;\n      // Create the AgeConsent object with required session and age restriction data\n      let data = {\n        sessionId: localStorage.getItem('sessionId') || '',\n        MinimumAgeForProductEligibility: this.restrictionAge\n      };\n      // Include the userId in the consent object if available\n      if (userId) {\n        data.userId = userId;\n      }\n      // Extract product and navigation-related information for cart creation\n      const product = this.restrictedProductTobePurchased.product;\n      const productDetails = this.restrictedProductTobePurchased.productDetails;\n      const navigate = this.restrictedProductTobePurchased.navigate;\n      // Send the age consent to the backend and handle the response\n      this.userService.updateAgeConsent(data).subscribe({\n        next: res => {\n          // Successfully updated consent, proceed to create the cart\n          this.createCart(product, productDetails, navigate);\n        },\n        error: err => {\n          // Handle any errors encountered during the backend call\n          this.handleError(err.message);\n        }\n      });\n    }\n    /**\r\n     * Closes the age consent modal and opens the eligibility modal.\r\n     *\r\n     * This method ensures that the `displayAgeConsentModal` is set to `false`\r\n     * to close the age consent modal and `displayEligableModal` is set to `true`\r\n     * to show the eligibility modal to the user.\r\n     */\n    closeConsentModal() {\n      this.displayEligableModal = true; // Open the eligibility modal\n      this.displayAgeConsentModal = false; // Close the age consent modal\n    }\n    /**\r\n     * Closes the eligibility modal.\r\n     *\r\n     * This method sets `displayEligableModal` to `false` to hide the eligibility modal from the user.\r\n     */\n    closeEligableModal() {\n      this.displayEligableModal = false; // Close the eligibility modal\n    }\n    /**\r\n     * Handles errors by displaying an error message to the user.\r\n     *\r\n     * This method uses the `MessageService` to display an error notification with a localized error summary\r\n     * and a detailed message passed as a parameter.\r\n     *\r\n     * @param {string} message - The error message to be displayed to the user.\r\n     */\n    handleError(message) {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: message // Detailed error message\n      });\n    }\n\n    isBase64(str) {\n      const base64Regex = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/;\n      return base64Regex.test(str);\n    }\n    updateSanitizedDescription(description) {\n      this.sanitizedDescription = this.sanitizer.bypassSecurityTrustHtml(description);\n    }\n    getFilteredProductSpec() {\n      this.filteredProductSpecs = this.product.productSpecs?.filter(spec => spec.name !== 'Gender' && spec.name !== 'Weight' && spec.name !== 'Size' && spec.name !== 'Size 2') || [];\n    }\n    getFilteredVarianceSpec() {\n      this.filteredVarianceSpecs = this.selectedVariant.varianceSpecs?.filter(spec => spec.name !== 'Gender' && spec.name !== 'Weight' && spec.name !== 'Size' && spec.name !== 'Size 2') || [];\n    }\n    shareContent() {\n      return _asyncToGenerator(function* () {\n        const shareData = {\n          url: window.location.href\n        };\n        yield navigator.share(shareData);\n      })();\n    }\n    getImageUrl(imageLink) {\n      return verifyImageURL(imageLink, this._BaseURL);\n    }\n    static ɵfac = function DetailsComponent_Factory(t) {\n      return new (t || DetailsComponent)(i0.ɵɵdirectiveInject(i1.ProductLogicService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.DetailsService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i6.CookieService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i7.BsModalService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i8.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i9.CustomGAService), i0.ɵɵdirectiveInject(i10.DomSanitizer), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailsComponent,\n      selectors: [[\"app-details\"]],\n      hostBindings: function DetailsComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function DetailsComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        product: \"product\",\n        channelId: \"channelId\",\n        currency: \"currency\",\n        selectedVariant: \"selectedVariant\",\n        selectedSize: \"selectedSize\",\n        selectedSize2: \"selectedSize2\",\n        selectedColor: \"selectedColor\",\n        cols: \"cols\"\n      },\n      outputs: {\n        onItemLike: \"onItemLike\",\n        onChangeVariant: \"onChangeVariant\"\n      },\n      decls: 7,\n      vars: 11,\n      consts: [[\"class\", \"new-product-details\", 4, \"ngIf\"], [\"class\", \"old-product-details\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"bodyMessage\", \"displayModal\", \"titleMessage\", \"cancel\"], [3, \"isEmailExist\", \"displayModal\", \"close\", \"submit\"], [3, \"age\", \"displayModal\", \"submit\", \"cancel\"], [3, \"displayModal\", \"cancel\"], [1, \"new-product-details\"], [\"class\", \"d-flex flex-row details__product-info__prices price-product justify-content-between\", 4, \"ngIf\"], [\"class\", \"details__product-info__attributes__colors\", 4, \"ngIf\"], [\"style\", \"padding: 0 17px\", \"class\", \"mobile__product-info__attributes__sizes\", 4, \"ngIf\"], [\"class\", \"deliver-to\", 4, \"ngIf\"], [\"class\", \"seller-info\", 4, \"ngIf\"], [\"class\", \"flex-row details__product-info__buttons justify-content-end\", 4, \"ngIf\"], [1, \"details\"], [1, \"d-flex\", \"flex-row\", 3, \"ngClass\"], [\"class\", \"details__product-info\", 4, \"ngIf\"], [\"class\", \"d-inline-flex details__seller-info-section\", 4, \"ngIf\"], [1, \"details__tabView\"], [1, \"card\"], [3, \"header\"], [1, \"details__tabView__description__title\"], [\"class\", \"details__tabView__description__value ql-editor\", 4, \"ngIf\"], [\"class\", \"d-flex inner-html ql-editor\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\"], [1, \"details-specification\"], [1, \"specification-name\"], [1, \"specification-value\"], [4, \"ngIf\", \"ngIfElse\"], [\"productVariance\", \"\"], [1, \"specification-name\", \"d-flex\"], [\"otherProductVarianceLabels\", \"\"], [1, \"specification-value\", \"d-flex\"], [\"otherProductVarianceValues\", \"\"], [1, \"details__tabView__review\"], [1, \"d-flex\", \"flex-row\"], [1, \"details__tabView__review__rating-card\"], [1, \"details__tabView__review__rating-card__rating\"], [1, \"details__tabView__review__rating_stars\"], [3, \"ngModel\", \"cancel\", \"readonly\", \"stars\", \"ngModelChange\"], [1, \"details__tabView__review__rating-card__rating_count\"], [1, \"share-modal\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"showHeader\", \"modal\", \"resizable\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"content\"], [1, \"d-flex\", \"flex-row\", \"details__product-info__prices\", \"price-product\", \"justify-content-between\"], [1, \"details__product-info__prices__price\"], [1, \"details__product-info__prices__currency\"], [\"class\", \"details__product-info__prices__sale-price\", \"style\", \"margin-left: 0 !important;\", 4, \"ngIf\"], [\"class\", \"details__product-info__low-stock d-block\", 4, \"ngIf\"], [1, \"details__product-info__share\"], [1, \"details__product-info__buttons__share-button\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/share-icon.svg\"], [1, \"details__product-info__prices__sale-price\", 2, \"margin-left\", \"0 !important\"], [1, \"details__product-info__low-stock\", \"d-block\"], [1, \"details__product-info__attributes__colors\"], [1, \"details__product-info__attributes__colors__title\"], [1, \"color-options\"], [\"class\", \"color-option\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"color-option\", 3, \"ngClass\", \"click\"], [\"class\", \"color-circle\", 3, \"background-color\", 4, \"ngIf\"], [\"class\", \"multi-color-circle\", 4, \"ngIf\"], [1, \"color-circle\"], [1, \"multi-color-circle\"], [1, \"mobile__product-info__attributes__sizes\", 2, \"padding\", \"0 17px\"], [1, \"mobile__sizes__container\"], [\"isSizeLabelMobile\", \"\"], [\"class\", \"mobile__sizes__size-guide\", 3, \"click\", 4, \"ngIf\"], [1, \"mobile__sizes__title\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mobile__sizes__label\"], [\"secondLabelMobile\", \"\"], [1, \"mobile__sizes__options\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"], [1, \"mobile__sizes__value\", 3, \"ngClass\"], [1, \"mobile__sizes__size-guide\", 3, \"click\"], [1, \"deliver-to\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/location.svg\"], [1, \"deliver-to-tag\"], [1, \"home-location\"], [1, \"seller-info\"], [1, \"align-self-center\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/return-policy-mobile-icon.svg\", 1, \"mr-2\"], [1, \"return-policy-merchant\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shop.svg\", 1, \"mr-2\"], [1, \"seller-info-merchant\"], [1, \"d-inline-flex\", \"justify-content-between\", \"w-100\"], [1, \"seller-shop-merchant\"], [1, \"more-merchant\", 3, \"routerLink\", \"click\"], [1, \"flex-row\", \"details__product-info__buttons\", \"justify-content-end\"], [1, \"d-flex\", \"details__product-info__buttons__action-buttons\", \"mt-4\", 3, \"ngClass\"], [\"loggedOut\", \"\"], [\"class\", \"details__product-info__buttons__action-buttons__cart-button btn-width\", \"style\", \"border-radius: 8px\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"details__product-info__buttons__action-buttons__notify-button\", \"style\", \"border-radius: 8px; width: 244px\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"details__product-info__buttons__action-buttons__buy-button buy-now\", \"style\", \"border-radius: 8px\", 3, \"click\", 4, \"ngIf\"], [1, \"details__product-info__buttons__action-buttons__cart-button\", \"btn-width\", 2, \"border-radius\", \"8px\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/add-cart.svg\"], [1, \"details__product-info__buttons__action-buttons__notify-button\", 2, \"border-radius\", \"8px\", \"width\", \"244px\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/notify-me.svg\"], [1, \"details__product-info__buttons__action-buttons__buy-button\", \"buy-now\", 2, \"border-radius\", \"8px\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/buy-now.svg\"], [1, \"details__product-info__buttons__action-buttons__cart-button\", \"btn-width\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"details__product-info__buttons__buy-button\", \"buy-now\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"details__product-info\"], [1, \"d-flex\"], [1, \"d-inline-flex\", \"w-100\"], [\"data-placement\", \"top\", \"data-toggle\", \"tooltip\", 1, \"details__product-info__name\", 3, \"title\"], [\"class\", \"details__product-info__badges-row\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-row\", \"justify-content-space-between\", \"details__product-info__specifications\"], [1, \"d-inline-flex\", \"flex-column\", \"details__product-info__specifications__text\"], [1, \"details__product-info__specifications__text__bold\"], [1, \"d-inline-flex\", \"details__product-info__specifications__labels\"], [\"class\", \"details__product-info__specifications__labels__green-label\", 4, \"ngIf\"], [\"class\", \"d-inline-flex justify-content-normal\", 4, \"ngIf\"], [1, \"details__product-info__attributes\"], [\"class\", \"details__product-info__attributes__sizes\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-row\", \"details__product-info__prices\"], [\"priceView\", \"\"], [\"class\", \"details__product-info__prices__sale-price\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-row\", \"details__product-info__buttons\", 3, \"ngClass\"], [\"class\", \"details__product-info__buttons__share-button\", 3, \"click\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-row-reverse\", \"details__product-info__buttons__action-buttons\"], [\"class\", \"details__product-info__buttons__action-buttons__wish-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"d-flex flex-row details__product-info__buttons justify-content-end\", 4, \"ngIf\"], [1, \"details__product-info__badges-row\"], [\"class\", \"details__product-info__badge-image\", 3, \"src\", \"alt\", 4, \"ngFor\", \"ngForOf\"], [1, \"details__product-info__badge-image\", 3, \"src\", \"alt\"], [1, \"details__product-info__specifications__labels__green-label\"], [1, \"d-inline-flex\", \"justify-content-normal\"], [1, \"details__product-info__specifications__stock\"], [1, \"d-flex\", \"justify-content-end\"], [\"class\", \"details__product-info__low-stock\", 4, \"ngIf\"], [1, \"details__product-info__low-stock\"], [1, \"product-colors-section\"], [1, \"product-colors-title\"], [1, \"d-flex\", \"flex-row\", \"color-options\"], [\"class\", \"color-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"color-option\", 3, \"click\"], [1, \"details__product-info__attributes__sizes\"], [1, \"d-flex\", \"flex-row\", \"justify-content-space-between\"], [\"isSizeLabel\", \"\"], [\"class\", \"details__product-info__attributes__sizes__size-guide\", 3, \"click\", 4, \"ngIf\"], [1, \"details__product-info__attributes__sizes__title\"], [1, \"span-text\", \"mrg-btm-10\"], [\"secondLabel\", \"\"], [\"class\", \"details__product-info__attributes__sizes__value\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"details__product-info__attributes__sizes__value\", 3, \"ngClass\", \"click\"], [1, \"details__product-info__attributes__sizes__size-guide\", 3, \"click\"], [1, \"details__product-info__prices__sale-price\"], [1, \"details__product-info__buttons__action-buttons__wish-button\", 3, \"click\"], [\"alt\", \"Heart Thin icon\", \"height\", \"18\", \"src\", \"assets/icons/mobile-heart-icon.svg\", \"title\", \"Heart Thin icon\", \"width\", \"20\", 4, \"ngIf\"], [\"alt\", \"Heart Thin icon\", \"height\", \"15\", \"src\", \"assets/icons/filled-heart-icon.svg\", \"title\", \"Heart Thin icon\", \"width\", \"15\", 4, \"ngIf\"], [\"alt\", \"Heart Thin icon\", \"height\", \"18\", \"src\", \"assets/icons/mobile-heart-icon.svg\", \"title\", \"Heart Thin icon\", \"width\", \"20\"], [\"alt\", \"Heart Thin icon\", \"height\", \"15\", \"src\", \"assets/icons/filled-heart-icon.svg\", \"title\", \"Heart Thin icon\", \"width\", \"15\"], [\"class\", \"details__product-info__buttons__action-buttons__cart-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"details__product-info__buttons__action-buttons__buy-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"details__product-info__buttons__action-buttons__notify-button\", \"style\", \"width: 244px\", 3, \"click\", 4, \"ngIf\"], [1, \"details__product-info__buttons__action-buttons__cart-button\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-sc.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-sc.svg\"], [1, \"details__product-info__buttons__action-buttons__buy-button\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-white.svg\"], [1, \"details__product-info__buttons__action-buttons__notify-button\", 2, \"width\", \"244px\", 3, \"click\"], [1, \"details__product-info__buttons__action-buttons__cart-button\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"details__product-info__buttons__action-buttons__buy-button\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"d-flex\", \"flex-row\", \"details__product-info__buttons\", \"justify-content-end\"], [1, \"d-flex\", \"details__product-info__buttons__action-buttons\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/wish-icon.svg\"], [1, \"d-inline-flex\", \"details__seller-info-section\"], [3, \"product\"], [1, \"details__tabView__description__value\", \"ql-editor\"], [3, \"innerHTML\", 4, \"ngIf\"], [3, \"innerHTML\"], [1, \"d-flex\", \"inner-html\", \"ql-editor\", 3, \"innerHTML\"], [\"class\", \"details-specification\", 4, \"ngIf\"], [1, \"details__shareModal\"], [1, \"details__shareModal__title\"], [1, \"details__shareModal__sub-title\"], [1, \"d-inline-flex\", \"details__shareModal__share-icons\", \"justify-content-evenly\"], [\"button\", \"facebook\", \"theme\", \"circles-dark\", 1, \"sb-facebook-btn\", 3, \"url\"], [\"button\", \"whatsapp\", \"description\", \" \", \"theme\", \"circles-dark\", 1, \"sb-whatsapp-btn\", 3, \"url\"], [\"button\", \"twitter\", \"description\", \" \", \"theme\", \"circles-dark\", 1, \"sb-twitter-btn\", 3, \"url\"], [1, \"details__shareModal__copy-link\"], [1, \"details__shareModal__copy-link__title\"], [1, \"details__shareModal__copy-link__copy\"], [1, \"details__shareModal__copy-link__copy__text\"], [1, \"details__shareModal__copy-link__copy__copy-btn\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/copy-icon.svg\"], [1, \"old-product-details\"], [1, \"information\", \"p-4\", \"shadow-1\", \"mobile-information\"], [1, \"product-name\", \"mb-0\", \"mobile-display-none\", \"mb-2\"], [1, \"flex\", \"flex-row\", \"align-items-center\"], [1, \"in-stock\", \"mobile-display-none\", 3, \"ngClass\"], [1, \"dot-6\", \"bg-green-400\", \"green-dot\", 3, \"ngClass\"], [1, \"rating\", \"mt-1\", \"mx-2\", \"mobile-display-none\"], [1, \"star\", \"pi\", \"pi-star-fill\"], [1, \"rate\", \"mx-1\"], [1, \"rating-number\"], [1, \"mt-4\", \"d-flex\", \"justify-content-between\"], [\"class\", \"mt-3 flex flex-row justify-content-between\", 4, \"ngIf\"], [\"class\", \"mt-2 flex flex-row justify-content-between\", 4, \"ngIf\"], [1, \"mt-2\", \"flex\", \"flex-row\", \"justify-content-between\", \"col--12\", \"pl-0\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"mr-1\", \"width-50\", \"main-btn\", \"mobile-buy\", 3, \"disabled\", \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"ml-1\", \"width-50\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [\"type\", \"button\", 1, \"ml-1\", \"wishlist-btn\", \"cursor-pointer\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty-sc.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart-sc.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart.svg\", 4, \"ngIf\"], [\"class\", \"mt-5\", 4, \"ngIf\"], [1, \"mt-5\"], [1, \"bold-font\", \"about-product\"], [1, \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"my-1\", \"details-name\"], [1, \"prop-color\", \"my-1\", \"details-name\"], [\"class\", \"flex flex-row justify-content-between\", 4, \"ngIf\"], [1, \"about-product\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"rating-wrapper\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"width-45\", \"overall-rating\", \"star-rating\"], [1, \"bold-font\", \"overall-rate\"], [1, \"medium-font\", \"font-size-28\", \"fourth-color\"], [1, \"prop-color\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"width-45\", \"vertical-rating\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"w-full\"], [1, \"font-medium\", \"font-size-13\", \"review-number\"], [1, \"pi\", \"pi-star-fill\", \"rating-color\"], [1, \"width-75\", \"mx-1\"], [1, \"w-full\", 3, \"showValue\", \"value\"], [1, \"font-light\", \"font-size-13\", \"prop-color\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"w-full\", \"my-1\"], [\"class\", \"mt-5\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"price\", \"m-0\", \"font-size-16\"], [1, \"now-currency\"], [1, \"tag-now\"], [1, \"price\", \"m-0\", \"font-size-16\", \"was-currency\"], [1, \"was-tag\"], [1, \"tag-was\"], [1, \"mt-3\", \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"product-size-box\"], [1, \"row\"], [1, \"span-text\"], [\"class\", \"color-circle\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"color-circle\", 3, \"click\"], [\"class\", \"black-circle\", 3, \"background-color\", 4, \"ngIf\"], [\"class\", \"border-circle\", 4, \"ngIf\"], [1, \"black-circle\"], [\"class\", \"fa-solid fa-check col-12 select-color\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-check\", \"col-12\", \"select-color\"], [1, \"border-circle\"], [1, \"mt-2\", \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"row\", \"padding-10-20\"], [\"class\", \"show-guide cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"col-3 product-size\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-3\", \"product-size\", 3, \"ngClass\", \"click\"], [1, \"show-guide\", \"cursor-pointer\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty-sc.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart-sc.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart.svg\"], [3, \"formGroup\"], [1, \"description\"], [\"class\", \"mt-15 w-100\", \"class\", \"pt-2 \", \"formControlName\", \"description\", \"readonly\", \"true\", 3, \"ngClass\", \"height\", \"placeholder\", 4, \"ngIf\"], [\"class\", \"main-color cursor-pointer medium-font font-size-14\", 3, \"click\", 4, \"ngIf\"], [\"formControlName\", \"description\", \"readonly\", \"true\", 1, \"pt-2\", 3, \"ngClass\", \"placeholder\"], [1, \"main-color\", \"cursor-pointer\", \"medium-font\", \"font-size-14\", 3, \"click\"], [1, \"mt-5\", 3, \"ngClass\"], [\"class\", \"users-reviews\", 4, \"ngIf\"], [1, \"users-reviews\"], [1, \"medium-font\", \"font-size-13\"], [1, \"pi\", \"pi-check-circle\", \"mx-2\", \"main-color\", \"font-size-12\"], [1, \"light-font\", \"font-size-12\", \"prop-color\"], [1, \"light-font\", \"font-size-14\", \"mt-1\", \"one-line\"], [3, \"displayModal\", \"sizeGuidImage\", \"cancel\", \"submit\"]],\n      template: function DetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DetailsComponent_div_0_Template, 61, 49, \"div\", 0);\n          i0.ɵɵtemplate(1, DetailsComponent_div_1_Template, 112, 80, \"div\", 1);\n          i0.ɵɵtemplate(2, DetailsComponent_ng_container_2_Template, 2, 2, \"ng-container\", 2);\n          i0.ɵɵelementStart(3, \"app-success-info-modal\", 3);\n          i0.ɵɵlistener(\"cancel\", function DetailsComponent_Template_app_success_info_modal_cancel_3_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"app-notify-modal\", 4);\n          i0.ɵɵlistener(\"close\", function DetailsComponent_Template_app_notify_modal_close_4_listener() {\n            return ctx.displayNotifyModal = false;\n          })(\"submit\", function DetailsComponent_Template_app_notify_modal_submit_4_listener($event) {\n            return ctx.onSubmitNotify($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"app-age-consent-modal\", 5);\n          i0.ɵɵlistener(\"submit\", function DetailsComponent_Template_app_age_consent_modal_submit_5_listener() {\n            return ctx.onSubmitConsent();\n          })(\"cancel\", function DetailsComponent_Template_app_age_consent_modal_cancel_5_listener() {\n            return ctx.closeConsentModal();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"app-ineligable-purchase-modal\", 6);\n          i0.ɵɵlistener(\"cancel\", function DetailsComponent_Template_app_ineligable_purchase_modal_cancel_6_listener() {\n            return ctx.closeEligableModal();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.displayModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"bodyMessage\", ctx.successBodyMessage)(\"displayModal\", ctx.displaySuccessModal)(\"titleMessage\", ctx.successTitleMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"isEmailExist\", ctx.isEmailExist)(\"displayModal\", ctx.displayNotifyModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"age\", ctx.restrictionAge)(\"displayModal\", ctx.displayAgeConsentModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayModal\", ctx.displayEligableModal);\n        }\n      },\n      dependencies: [i11.NgClass, i11.NgForOf, i11.NgIf, i11.NgStyle, i12.ButtonDirective, i2.PrimeTemplate, i4.RouterLink, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.FormGroupDirective, i3.FormControlName, i13.SizeGuideModalComponent, i14.Dialog, i15.SuccessInfoModalComponent, i16.NotifyModalComponent, i17.AgeConsentModalComponent, i18.IneligablePurchaseModalComponent, i19.Divider, i20.Rating, i21.ProgressBar, i22.TabView, i22.TabPanel, i23.ShareButton, i24.SellerInfoComponent, i11.DecimalPipe, i11.DatePipe, i5.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.new-product-details[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{width:100%}.new-product-details[_ngcontent-%COMP%]   .details__product-info[_ngcontent-%COMP%]{padding:24px 20px;border-radius:4px;background:var(--colors-fff, #FFF);box-shadow:3px 1px 5px #0000001f,0 2px 8px 2px #00000024,0 1px 1px #0003;max-width:75%;width:75%;height:-moz-fit-content;height:fit-content;gap:16px}@media only screen and (min-width: 768px) and (max-width: 1200px){.new-product-details[_ngcontent-%COMP%]   .details__product-info[_ngcontent-%COMP%]{max-width:98%;width:98%;margin:10px 5px}}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info[_ngcontent-%COMP%]{max-width:97%;width:97%;margin:10px 5px}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__low-stock[_ngcontent-%COMP%]{color:var(--custom-error, #FF5252);font-family:var(--medium-font);font-size:14px;font-style:normal;font-weight:500}.new-product-details[_ngcontent-%COMP%]   .details__product-info__name[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;color:#191919;font-family:var(--regular-font);font-size:32px;font-style:normal;font-weight:400;line-height:normal;text-transform:uppercase;margin-bottom:16px;align-items:flex-start;flex-direction:column}.new-product-details[_ngcontent-%COMP%]   .details__product-info__rating__rate-count[_ngcontent-%COMP%]{color:#77878f;font-family:var(--regular-font);font-size:20px;font-style:normal;font-weight:400;line-height:20px;margin-left:5px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications[_ngcontent-%COMP%]{margin-bottom:16px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__text[_ngcontent-%COMP%]{color:#191919;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:normal;gap:8px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__text__bold[_ngcontent-%COMP%]{font-family:var(--medium-font)}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__stock[_ngcontent-%COMP%]{border-radius:2px;background:var(--Gray-400, #929FA5);color:var(--Gray-00, var(--colors-fff, #FFF));font-size:12px;font-style:normal;font-weight:700;padding:5px 10px;line-height:16px;font-family:var(--light-font)}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__labels[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:flex-end}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__labels__red-label[_ngcontent-%COMP%]{margin:10px 0;background:#EE5858;padding:5px 10px;align-items:flex-start;border-radius:2px;color:#fff;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:16px;width:-moz-fit-content;width:fit-content}.new-product-details[_ngcontent-%COMP%]   .details__product-info__specifications__labels__green-label[_ngcontent-%COMP%]{margin:10px 0;background:#2DB224;padding:5px 10px;align-items:flex-start;border-radius:2px;color:#fff;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:16px;width:-moz-fit-content;width:fit-content}.new-product-details[_ngcontent-%COMP%]   .details__product-info__dimenesions[_ngcontent-%COMP%]{padding:8px 0 16px;border-bottom:1px solid #F0F0F0;margin-bottom:16px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__dimenesions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{gap:12px;display:flex;flex-direction:column;padding-left:1rem}.new-product-details[_ngcontent-%COMP%]   .details__product-info__dimenesions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:#191919;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:normal}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__colors[_ngcontent-%COMP%]{margin-bottom:16px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__colors__title[_ngcontent-%COMP%]{color:#989898;font-family:var(--regular-font);font-size:10px;font-style:normal;font-weight:400;line-height:20px;letter-spacing:.48px;margin-bottom:8px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__colors__value[_ngcontent-%COMP%]{width:36px;height:32px;border-radius:2px;margin:0 5px;border:1px solid black}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__colors__text-value[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;height:32px;border-radius:2px;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:20px;margin:0 5px;padding:2px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__colors__selectedValue[_ngcontent-%COMP%]{padding:2px;border-radius:2px;border:1px solid #004D9C;box-shadow:0 0 0 1px #f3f5f6 inset}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes[_ngcontent-%COMP%]{margin-bottom:16px;gap:2px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__title[_ngcontent-%COMP%]{color:#000;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:400;line-height:20px;letter-spacing:.48px;margin-bottom:16px}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__title[_ngcontent-%COMP%]{color:#989898;font-size:10px;margin-bottom:0;text-transform:capitalize}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__size-guide[_ngcontent-%COMP%]{color:#2196f3;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:normal;text-decoration-line:underline}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value[_ngcontent-%COMP%]{display:flex;width:auto;padding:8px 16px;align-items:center;margin:0 5px;color:#004d9c;font-family:var(--regular-font);font-size:18px;font-style:normal;font-weight:400;line-height:normal;border-radius:4px}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value[_ngcontent-%COMP%]{font-weight:500;font-size:10px}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__selectedValue[_ngcontent-%COMP%]{padding:8px 16px;border-radius:4px;border:1px solid #004D9C}.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices[_ngcontent-%COMP%]{margin-bottom:16px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices__price[_ngcontent-%COMP%]{color:#191919;font-family:var(--regular-font);font-size:18px;font-style:normal;font-weight:600;line-height:normal;text-transform:uppercase}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices__price[_ngcontent-%COMP%]{color:#204e6e;font-size:20px;font-weight:700;text-transform:none}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices__sale-price[_ngcontent-%COMP%]{color:#929fa5;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:25px;text-decoration:line-through;text-transform:uppercase;margin-left:4px}.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices__currency[_ngcontent-%COMP%]{font-weight:700}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__prices__currency[_ngcontent-%COMP%]{font-weight:400;font-size:12px;color:#2a2a2a}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__share-button[_ngcontent-%COMP%]{display:flex;padding:16px;align-items:flex-start;gap:10px;border-radius:4px;background:#E1E9EC;border:none}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__share-button[_ngcontent-%COMP%]{background:transparent}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons[_ngcontent-%COMP%]{gap:10px}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons[_ngcontent-%COMP%]{margin:5px 0}}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons__wish-button[_ngcontent-%COMP%]{display:flex;padding:16px;align-items:flex-start;gap:10px;border-radius:4px;background:#E1E9EC;border:none}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons__notify-button[_ngcontent-%COMP%]{display:flex;padding:4px 12px;justify-content:center;align-items:center;gap:8px;border-radius:4px;border:2px solid #204E6E;color:#204e6e;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:700;line-height:48px;letter-spacing:.192px;text-transform:uppercase;background:transparent}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons__cart-button[_ngcontent-%COMP%]{display:flex;padding:4px 12px;justify-content:center;align-items:center;gap:8px;border-radius:4px;border:1px solid #204E6E;color:#204e6e;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:700;line-height:35px;letter-spacing:.192px;text-transform:uppercase;background:transparent}.new-product-details[_ngcontent-%COMP%]   .details__product-info__buttons__action-buttons__buy-button[_ngcontent-%COMP%]{display:flex;padding:4px 12px;justify-content:center;align-items:center;gap:8px;border-radius:4px;background:#204E6E;color:#fff;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:400;line-height:35px;letter-spacing:.192px;text-transform:uppercase;border:none}.new-product-details[_ngcontent-%COMP%]   .details__product-info__badges-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;gap:16px;margin-top:8px;flex-wrap:wrap;width:100%}.new-product-details[_ngcontent-%COMP%]   .details__seller-info-section[_ngcontent-%COMP%]{max-width:25%;width:25%;padding:0 12px}.new-product-details[_ngcontent-%COMP%]   .details__seller-info-section[_ngcontent-%COMP%]   app-seller-info[_ngcontent-%COMP%]{width:100%}@media only screen and (min-width: 768px) and (max-width: 1200px){.new-product-details[_ngcontent-%COMP%]   .details__seller-info-section[_ngcontent-%COMP%]{max-width:99%;width:99%;margin:10px 0;padding:0 5px}}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__seller-info-section[_ngcontent-%COMP%]{max-width:100%;width:100%;margin:10px 0;padding:0 5px}}.new-product-details[_ngcontent-%COMP%]   .details__tabView[_ngcontent-%COMP%]{padding:20px 5px}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__tabView[_ngcontent-%COMP%]{padding:20px 0 0}}.new-product-details[_ngcontent-%COMP%]   .details__tabView[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #E4E7E9;background:#FFF}.new-product-details[_ngcontent-%COMP%]   .details__tabView__description__title[_ngcontent-%COMP%]{color:#191c1f;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:400;line-height:24px}@media screen and (min-width: 768px){.new-product-details[_ngcontent-%COMP%]   .details__tabView__description__title[_ngcontent-%COMP%]{font-family:sans-serif;font-weight:600;margin-bottom:16px}}.new-product-details[_ngcontent-%COMP%]   .details__tabView__description__value[_ngcontent-%COMP%]{color:var(--pale-sky);font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:20px}.new-product-details[_ngcontent-%COMP%]   .details__tabView__specification__title[_ngcontent-%COMP%]{color:#191c1f;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:400;line-height:24px}.new-product-details[_ngcontent-%COMP%]   .details__tabView__specification__details[_ngcontent-%COMP%]{width:100%}.new-product-details[_ngcontent-%COMP%]   .details__tabView__specification__details__heading[_ngcontent-%COMP%]{width:30%;max-width:30%}.new-product-details[_ngcontent-%COMP%]   .details__tabView__specification__details__value[_ngcontent-%COMP%]{width:70%;max-width:70%}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review[_ngcontent-%COMP%]{padding:0 40px;justify-content:center;align-items:center;gap:20px;align-self:stretch;width:100%}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-card[_ngcontent-%COMP%]{display:flex;padding:32px;flex-direction:column;justify-content:center;align-items:center;gap:12px;border-radius:4px;background:#FBF4CE;width:30%;max-width:30%}@media only screen and (max-width: 767px){.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-card[_ngcontent-%COMP%]{width:100%;max-width:100%}}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-card__rating[_ngcontent-%COMP%]{color:#191c1f;text-align:center;font-family:var(--regular-font);font-size:56px;font-style:normal;font-weight:700;line-height:64px}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-card[_ngcontent-%COMP%]   .new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-card__rating_count[_ngcontent-%COMP%]{color:var(--gray-900, #191C1F);text-align:center;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:500;line-height:24px}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__rating-stars[_ngcontent-%COMP%]{width:24px;height:24px}.new-product-details[_ngcontent-%COMP%]   .details__tabView__review__progress_bar[_ngcontent-%COMP%]{width:70%;max-width:70%}.new-product-details[_ngcontent-%COMP%]   .details__shareModal[_ngcontent-%COMP%]{width:100%;display:flex;padding:16px;flex-direction:column;align-items:flex-start;gap:16px;border-radius:8px;background:#FFF}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__title[_ngcontent-%COMP%]{color:#222;font-size:24px;font-style:normal;font-weight:500;line-height:normal;font-family:var(--bold-font)}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__sub-title[_ngcontent-%COMP%]{color:#0009;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:normal}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__share-icons[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:24px;align-self:stretch;filter:drop-shadow(0px 12px 24px rgba(0,0,0,.1))}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__share-icons__sb-custom-container__sb-custom-button[_ngcontent-%COMP%]{cursor:pointer;background-color:#e1306c;color:#fff;box-shadow:0 3px 5px -1px #0003,0 6px 10px #00000024,0 1px 18px #0000001f;width:2.8em;height:2.8em;border-radius:50%;transition:background .4s cubic-bezier(.25,.8,.25,1),box-shadow .28s cubic-bezier(.4,0,.2,1);display:flex;align-items:center;justify-content:center}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__share-icons[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:96px;height:96px}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__copy-link[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;width:100%}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__copy-link__title[_ngcontent-%COMP%]{color:#0009;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:normal}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__copy-link__copy[_ngcontent-%COMP%]{display:flex;padding:10px 16px;justify-content:space-between;align-items:flex-start;align-self:stretch;border-radius:4px;border:1px solid rgba(0,0,0,.5)}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__copy-link__copy__text[_ngcontent-%COMP%]{color:#000000b3;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:400;line-height:normal;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:50%}.new-product-details[_ngcontent-%COMP%]   .details__shareModal__copy-link__copy__copy-btn[_ngcontent-%COMP%]{background:transparent;border:none}.new-product-details[_ngcontent-%COMP%]   .details__share-success[_ngcontent-%COMP%]{display:flex;width:100%;padding:24px 16px;flex-direction:row;justify-content:center;align-items:center;gap:51px;border-radius:8px;background:var(--colors-fff, #FFF)}.new-product-details[_ngcontent-%COMP%]   .details__share-success__text[_ngcontent-%COMP%]{color:#000;font-family:var(--regular-font);font-size:17px;font-style:normal;font-weight:400;line-height:normal}.old-product-details[_ngcontent-%COMP%]   .information[_ngcontent-%COMP%]{background-color:var(--white-color);min-height:10vh;min-width:100%;border-radius:5px;overflow-x:hidden;border:1px solid rgba(151,151,151,.17);box-shadow:none!important}.old-product-details[_ngcontent-%COMP%]   .information[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:99%}.old-product-details[_ngcontent-%COMP%]   .information[_ngcontent-%COMP%]   .prop-color[_ngcontent-%COMP%]{color:#a3a3a3;font-family:var(--regular-font)!important;font-size:13px;font-weight:300;margin-top:4px}@media (max-width: 400px){.old-product-details[_ngcontent-%COMP%]   .star-rating[_ngcontent-%COMP%]{width:80%!important}}@media screen and (max-width: 768px){.old-product-details[_ngcontent-%COMP%]     p-breadcrumb.p-element.col-12{padding-top:0!important}.old-product-details[_ngcontent-%COMP%]   .width-50[_ngcontent-%COMP%]{font-size:14px;font-family:var(--regular-font)!important}.old-product-details[_ngcontent-%COMP%]   .mobile-display-none[_ngcontent-%COMP%]{display:none}.old-product-details[_ngcontent-%COMP%]   .mobile-information[_ngcontent-%COMP%]{border:none!important;box-shadow:none!important;padding:0!important}.old-product-details[_ngcontent-%COMP%]   .details-name[_ngcontent-%COMP%]{font-size:15px!important}.old-product-details[_ngcontent-%COMP%]   .review-number[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-size:13.035px;font-weight:500!important;color:#000}.old-product-details[_ngcontent-%COMP%]   .main-btn[_ngcontent-%COMP%], .old-product-details[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]{height:34px!important}.old-product-details[_ngcontent-%COMP%]   .mobile-buy[_ngcontent-%COMP%]{width:53%}.old-product-details[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{padding:10px!important;height:36px!important;width:36px!important}.old-product-details[_ngcontent-%COMP%]   .vertical-rating[_ngcontent-%COMP%]{width:90%!important}}@media (max-width: 320px){.old-product-details[_ngcontent-%COMP%]   .rating-wrapper[_ngcontent-%COMP%]{flex-direction:column!important}.old-product-details[_ngcontent-%COMP%]   .star-rating[_ngcontent-%COMP%], .old-product-details[_ngcontent-%COMP%]   .vertical-rating[_ngcontent-%COMP%]{width:100%!important}}.old-product-details   [_nghost-%COMP%]     .information .p-progressbar{height:5px;border-radius:3px}.old-product-details[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{outline:none;background:transparent;font-family:var(--regular-font)!important;font-size:16px;font-weight:400;resize:none;border:0 none;box-shadow:0 2px 4px #00000003;border-radius:5px;padding-right:50px;width:100%}.old-product-details[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{background:#f5f5f5 0% 0% no-repeat padding-box}.old-product-details[_ngcontent-%COMP%]   .user-review-scroll[_ngcontent-%COMP%]{height:200px;overflow-y:scroll}.old-product-details[_ngcontent-%COMP%]   .green-dot[_ngcontent-%COMP%]{width:10px;height:10px;display:inline-block;border-radius:50%}.old-product-details[_ngcontent-%COMP%]   .one-line[_ngcontent-%COMP%]{display:block;overflow:hidden;white-space:pre-line;text-overflow:ellipsis}.old-product-details[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{color:#fff;background-color:#e5edf1;border:1px solid #f1ebe5;align-self:center;border-radius:50%;padding:16px;height:48px;width:48px}.old-product-details[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{display:block}.old-product-details[_ngcontent-%COMP%]   .fourth-color[_ngcontent-%COMP%]{margin-bottom:8px;font-size:27px}.old-product-details[_ngcontent-%COMP%]   .rating-color[_ngcontent-%COMP%]{color:var(--rating-color)!important;margin-left:11px;margin-right:7px;font-size:15px;line-height:28px}.old-product-details[_ngcontent-%COMP%]   .product-size-box[_ngcontent-%COMP%]   .product-size-heading[_ngcontent-%COMP%]{font-family:var(--regular-font)!important;letter-spacing:0px;color:#00000061;opacity:1}.old-product-details[_ngcontent-%COMP%]   .product-size-box[_ngcontent-%COMP%]   .product-size[_ngcontent-%COMP%]{text-align:center;padding-top:8px;width:auto;height:36px;font-family:var(--regular-font)!important;color:#00000061;background:#ffffff 0% 0% no-repeat padding-box;border:1px solid #dbe2ea;opacity:1}.old-product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-weight:400;font-size:20px;color:#000;font-family:var(--regular-font)!important}.old-product-details[_ngcontent-%COMP%]   .now-currency[_ngcontent-%COMP%]{padding:5px;color:#ffcb05;background:#faf5e1;font-size:10px;font-weight:300;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .was-currency[_ngcontent-%COMP%]{margin-top:5px!important}.old-product-details[_ngcontent-%COMP%]   .was-tag[_ngcontent-%COMP%]{padding:5px;color:#a3a3a3;background:#f1f1f1;font-size:10px;font-weight:300;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .tag-now[_ngcontent-%COMP%]{font-size:16px;font-weight:300;color:#000;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .tag-was[_ngcontent-%COMP%]{font-size:16px;font-weight:300;color:#a3a3a3;text-decoration-line:line-through;text-decoration-color:#707070;text-decoration-thickness:1px;font-family:var(--regular-font)!important;margin-left:6px}.old-product-details[_ngcontent-%COMP%]   .black-circle[_ngcontent-%COMP%]{background:black;width:32px;height:32px;display:flex;border-radius:24px;border:1px solid black}.old-product-details[_ngcontent-%COMP%]   .border-circle[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;height:32px;display:flex;border-radius:6px!important;padding:3px;border:1px solid black}.old-product-details[_ngcontent-%COMP%]   .blue-circle[_ngcontent-%COMP%]{background:#1083b5;width:32px;height:32px;display:flex;border-radius:24px}.old-product-details[_ngcontent-%COMP%]   .pink-circle[_ngcontent-%COMP%]{background:#ff0049;width:32px;height:32px;display:flex;border-radius:24px}.old-product-details[_ngcontent-%COMP%]   .gray-circle[_ngcontent-%COMP%]{background:#969696;width:32px;height:32px;display:flex;border-radius:24px}.old-product-details[_ngcontent-%COMP%]   .select-color[_ngcontent-%COMP%]{width:12px;height:10px;color:#fff;position:absolute;font-size:20px;padding-top:7px}.old-product-details[_ngcontent-%COMP%]   .size-padding[_ngcontent-%COMP%]{padding-bottom:0!important;padding-top:4px!important;padding-left:0!important}.old-product-details[_ngcontent-%COMP%]   .show-guide[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:var(--header_bgcolor);font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .span-text[_ngcontent-%COMP%]{font-size:10px;font-weight:400;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .main-btn[_ngcontent-%COMP%], .old-product-details[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]{height:44.49px}.old-product-details[_ngcontent-%COMP%]   .about-product[_ngcontent-%COMP%]{font-size:18px;font-weight:700;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .details-name[_ngcontent-%COMP%]{font-size:15px;font-weight:400;font-family:var(--medium-font)!important;color:#000}.old-product-details[_ngcontent-%COMP%]   .overall-rate[_ngcontent-%COMP%]{font-size:15px;font-weight:700;color:#000;font-family:var(--medium-font)!important}.old-product-details[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%]{padding:6px 23px 0 0}.old-product-details[_ngcontent-%COMP%]   .width-50[_ngcontent-%COMP%]{text-transform:uppercase}.old-product-details[_ngcontent-%COMP%]   .p-rating[_ngcontent-%COMP%]   .p-rating-icon[_ngcontent-%COMP%]{margin-left:.3rem!important}.old-product-details[_ngcontent-%COMP%]   .selected-size[_ngcontent-%COMP%]{background:rgba(219,226,234,.3882352941)!important;border:none!important}.old-product-details[_ngcontent-%COMP%]   .soldOut[_ngcontent-%COMP%]{color:red}.old-product-details[_ngcontent-%COMP%]   .redDot[_ngcontent-%COMP%]{background-color:red!important}.mrg-btm-10[_ngcontent-%COMP%]{margin-bottom:10px}@media only screen and (max-width: 767px){.mrg-btm-10[_ngcontent-%COMP%]{margin-bottom:8px}}@media only screen and (max-width: 767px){.price-product[_ngcontent-%COMP%]{padding:0 17px;margin-bottom:5px!important}.item-left[_ngcontent-%COMP%]{color:#ee5858;display:block;font-size:10px;font-weight:400;font-family:var(--regular-font)}.refundable[_ngcontent-%COMP%]{padding:0 20px;margin-top:10px;display:flex;align-items:center;gap:4px}.refundable-img[_ngcontent-%COMP%]{width:16px;height:16px}.refundable-period[_ngcontent-%COMP%]{color:#292d32;font-size:10px;font-weight:400;font-family:main-regular!important}.deliver-to[_ngcontent-%COMP%]{padding:0 20px;margin-top:10px}.deliver-to-tag[_ngcontent-%COMP%]{color:#818181;font-size:10px;font-weight:400;font-family:var(--regular-font)}.home-location[_ngcontent-%COMP%]{color:#204e6e;font-size:12px;font-weight:500;font-family:var(--regular-font);text-decoration-line:underline}.seller-info[_ngcontent-%COMP%]{padding:0 20px;margin-top:10px}.seller-info-merchant[_ngcontent-%COMP%]{color:#818181;font-size:10px;font-weight:400;font-family:var(--regular-font)}.return-policy-merchant[_ngcontent-%COMP%]{color:#2a2a2a;font-size:10px;font-weight:400;font-family:var(--regular-font)}.seller-shop-merchant[_ngcontent-%COMP%]{color:#2a2a2a;font-family:var(--regular-font);font-size:12px;font-weight:400}.more-merchant[_ngcontent-%COMP%]{color:#204e6e;font-size:10px;font-family:var(--regular-font);font-weight:400;text-decoration:underline}.add-tocart[_ngcontent-%COMP%]{flex-direction:column;position:fixed;bottom:60px;z-index:99;width:100%;background:#FFF;box-shadow:4px -2px 20px #00000040;padding:5px}.add-tocart-old[_ngcontent-%COMP%]{padding:0 20px;flex-direction:column}.btn-width[_ngcontent-%COMP%]{width:100%;background:#204E6E!important;color:#fff!important;font-size:12px!important;font-weight:500!important;text-transform:revert!important;font-family:var(--regular-font)!important}.buy-now[_ngcontent-%COMP%]{background:white!important;color:#204e6e!important;font-size:12px!important;font-weight:500!important;border:1px solid #204E6E!important;text-transform:revert!important;font-family:var(--regular-font)!important}}.inner-html[_ngcontent-%COMP%]{overflow:scroll;display:flex;height:50vh}[_nghost-%COMP%]     .detailmodule_html img{max-width:100%!important}[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px}[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:rgba(136,136,136,.62)}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:rgba(85,85,85,.68)}@media screen and (min-width: 768px){[_nghost-%COMP%]     .p-tabview .p-tabview-panels{padding:40px 20px}}.details-specification[_ngcontent-%COMP%]{display:grid;grid-template-columns:.4fr 1fr;gap:5px;height:34px;color:#3d3d3d;font-size:14px;padding:8px;justify-content:left}@media screen and (min-width: 768px){.details-specification[_ngcontent-%COMP%]{padding:8px 0}}@media screen and (max-width: 768px){.details-specification[_ngcontent-%COMP%]{grid-template-columns:1fr 1fr;gap:10px}}.details-specification__header[_ngcontent-%COMP%]{color:#191c1f;margin-bottom:8px;font-size:16px;font-weight:600;line-height:24px;font-family:sans-serif}.details-specification[_ngcontent-%COMP%]:nth-child(odd){background-color:#204e6e33}@media screen and (min-width: 768px){.details-specification[_ngcontent-%COMP%]:nth-child(odd){background-color:transparent}}.details-specification[_ngcontent-%COMP%]:nth-child(2n){background-color:#f6f6f6}@media screen and (min-width: 768px){.details-specification[_ngcontent-%COMP%]:nth-child(2n){background-color:transparent}}.details-specification[_ngcontent-%COMP%]   .specification-name[_ngcontent-%COMP%]{gap:4px}@media screen and (min-width: 768px){.details-specification[_ngcontent-%COMP%]   .specification-name[_ngcontent-%COMP%]{color:#191c1f;font-family:sans-serif;font-size:14px;line-height:20px;font-weight:500}}.details-specification[_ngcontent-%COMP%]   .specification-name[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0!important}.details-specification[_ngcontent-%COMP%]   .specification-value[_ngcontent-%COMP%]{gap:4px}@media screen and (min-width: 768px){.details-specification[_ngcontent-%COMP%]   .specification-value[_ngcontent-%COMP%]{font-family:sans-serif;font-size:14px;font-weight:400;line-height:20px;text-align:left;color:var(--pale-sky)}}.details-specification[_ngcontent-%COMP%]   .specification-value[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0!important}.color-options[_ngcontent-%COMP%]{display:flex;gap:10px;align-items:center}.color-options[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]{position:relative;cursor:pointer;transition:transform .3s ease,border .3s ease}.color-options[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:0;border:1px solid #ccc;transition:all .3s ease}.color-options[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]   .multi-color-circle[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:0;border:1px solid #ccc;text-align:center;line-height:30px;font-size:12px;transition:all .3s ease}.color-options[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]{transform:scale(1.1);z-index:1}.color-options[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%], .color-options[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]   .multi-color-circle[_ngcontent-%COMP%]{border:1px solid #007bff}.color-options[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]:hover   .color-circle[_ngcontent-%COMP%], .color-options[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]:hover   .multi-color-circle[_ngcontent-%COMP%]{border:1px solid #0056b3}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__title[_ngcontent-%COMP%]{font-size:16px;font-weight:400;line-height:28px;font-family:Roboto,sans-serif;letter-spacing:.009375em;margin-bottom:10px;color:#333}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:4px;padding:8px 12px;margin:5px;cursor:pointer;text-align:center;font-size:14px;color:#333;transition:all .3s ease}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value.dimmed[_ngcontent-%COMP%]{opacity:.5;pointer-events:auto}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value.dimmed[_ngcontent-%COMP%]:hover{opacity:.7}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value.details__product-info__attributes__sizes__selectedValue[_ngcontent-%COMP%]{border:1px solid #007bff;font-weight:700;color:#007bff;background-color:#eef6ff}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__value[_ngcontent-%COMP%]:hover:not(.dimmed){border:1px solid #0056b3;color:#0056b3;background-color:#f0f8ff}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__size-guide[_ngcontent-%COMP%]{font-size:14px;color:#007bff;text-decoration:underline;cursor:pointer;margin-top:10px}.details__product-info__attributes__sizes[_ngcontent-%COMP%]   .details__product-info__attributes__sizes__size-guide[_ngcontent-%COMP%]:hover{color:#0056b3;text-decoration:none}@media (max-width: 768px){.details__product-info__attributes__colors[_ngcontent-%COMP%]{margin-left:16px}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .details__product-info__attributes__colors__title[_ngcontent-%COMP%]{font-size:16px;font-weight:700;margin-bottom:10px;color:#333}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-options[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px;justify-content:flex-start}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]{cursor:pointer;transition:transform .3s ease,border .3s ease}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:0;border:1px solid #ccc;transition:all .3s ease}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]   .multi-color-circle[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:0;border:1px solid #ccc;text-align:center;line-height:30px;font-size:12px;transition:all .3s ease}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]{transform:scale(1.2);z-index:1}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%], .details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option.selected[_ngcontent-%COMP%]   .multi-color-circle[_ngcontent-%COMP%]{border:1px solid #007bff}.details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]:hover   .color-circle[_ngcontent-%COMP%], .details__product-info__attributes__colors[_ngcontent-%COMP%]   .color-option[_ngcontent-%COMP%]:hover   .multi-color-circle[_ngcontent-%COMP%]{border:1px solid #0056b3}}@media (max-width: 768px){.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]{padding:0 17px}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__container[_ngcontent-%COMP%]{display:block}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__title[_ngcontent-%COMP%]{font-size:16px;font-weight:700;margin-bottom:10px;color:#333}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__label[_ngcontent-%COMP%]{font-size:14px;margin-bottom:5px;color:#555}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__options[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px;margin-bottom:10px}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__value[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:5px;padding:8px 12px;margin:5px 0;text-align:center;cursor:pointer;transition:all .3s ease}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__value.mobile__sizes__selectedValue[_ngcontent-%COMP%]{border:1px solid #007bff;font-weight:700;color:#007bff;background-color:#f0f8ff}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__value.mobile__sizes__dimmed[_ngcontent-%COMP%]{opacity:.4;pointer-events:auto;cursor:pointer}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__value[_ngcontent-%COMP%]:hover:not(.mobile__sizes__dimmed){border:1px solid #0056b3;color:#0056b3}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__size-guide[_ngcontent-%COMP%]{font-size:14px;color:#007bff;text-decoration:underline;cursor:pointer}.mobile__product-info__attributes__sizes[_ngcontent-%COMP%]   .mobile__sizes__size-guide[_ngcontent-%COMP%]:hover{color:#0056b3}}\"]\n    });\n  }\n  return DetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}