{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { DynamicDialogModule } from 'primeng/dynamicdialog';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { InputMaskModule } from \"primeng/inputmask\";\nimport { CheckboxModule } from \"primeng/checkbox\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { routes } from \"./routes\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let CheckoutModule = /*#__PURE__*/(() => {\n  class CheckoutModule {\n    static ɵfac = function CheckoutModule_Factory(t) {\n      return new (t || CheckoutModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CheckoutModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), RippleModule, RadioButtonModule, DropdownModule, DynamicDialogModule, SelectButtonModule, InputMaskModule, CheckboxModule, FormsModule, ReactiveFormsModule, TranslateModule, ButtonModule, SharedModule, DialogModule]\n    });\n  }\n  return CheckoutModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}