import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { InitiateApmWidgetResponse } from '@pages/checkout/modals/APM';

@Injectable({
  providedIn: 'root'
})
export class PaymentStateService {
  private apmConfigSubject = new BehaviorSubject<InitiateApmWidgetResponse | null>(null);
  apmConfig$ = this.apmConfigSubject.asObservable();

  setApmConfig(config: InitiateApmWidgetResponse) {
    this.apmConfigSubject.next(config);
  }

  getApmConfig(): InitiateApmWidgetResponse | null {
    return this.apmConfigSubject.value;
  }

  clearPaymentState() {
    this.apmConfigSubject.next(null);
  }
} 