<div *ngIf="isLayoutTemplate" class="new-payment-cart">

  <ng-container *ngIf="isMobileTemplate && screenWidth <= 768; else oldContainer">
    <div class="new-checkout-card">
      <section class="checkout-card">

        <div class="row">
          <div class="col-md-12 error-container">
            <p class="error-msg"></p>
          </div>
        </div>

        <button (click)="show()" [disabled]="!isProceccedCheckOut" [ngStyle]="{'opacity':   !isProceccedCheckOut? '0.5': ''}"
                class="button-container-mobile">
          <div class="button-content">
              <span class="items">{{
                  orderDetails.productDetails
                    .length
                }} {{
                  (orderDetails.productDetails.length > 1 ? 'checkout.items' : 'checkout.item') | translate
                }}</span>
            <span class="price">
                 {{ currencyCode }}
              <!--            {{orderDetails.orderAmount | number : "1." + decimalValue + "-" + decimalValue}}-->
              {{
                disableCent === "false" ? (orderDetails.orderAmount + shipmentService.shipmentCost - orderDiscount | number: "1." + decimalValue + "-" + decimalValue)
                  :
                  (orderDetails.orderAmount + shipmentService.shipmentCost - orderDiscount)
              }}
              </span>

          </div>
          <div class="checkout-button">
            {{ 'checkout.paymentCart.PayNow' | translate }}


            <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
              <path d="M3.125 10L16.875 10" stroke="#F5F7FC" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
              <path d="M11.25 15.625L16.875 10L11.25 4.375" stroke="#F5F7FC" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"/>
            </svg>
          </div>

        </button>
      </section>
    </div>
  </ng-container>
  <ng-template #oldContainer>
      <section class="payment-card">
        <div *ngIf="timeString && isShipmentFeeExist" class="payment-card__arrives">
          {{ "checkout.paymentCart.arrives" | translate }}:
          <span class="ckeckout-count">{{ timeString | deliveryDateFormat }}</span>
        </div>
        <button (click)="show()" [disabled]="!isProceccedCheckOut" [ngStyle]="{
            opacity: !isProceccedCheckOut? '0.5' : ''
          }" class="payment-card__checkout">
          {{ 'checkout.paymentCart.proceed' | translate }}
        </button>
      </section>
  </ng-template>

  <!--old implementation-->
  <section class="payment-card d-none">
    <div class="grid align-items-center justify-content-center px-7 bg-white border-round">
      <button (click)="show()" class="my-2 second-btn" label="{{ 'checkout.paymentCart.proceed' | translate }}" pButton
              type="button"></button>

    </div>
  </section>
</div>
<div *ngIf="!isLayoutTemplate" class="old-payment-cart">
  <section class="payment-card">
    <div
      class="grid align-items-center justify-content-center px-7 bg-white border-round"
    >
      <button
        (click)="show()"
        class="my-2 second-btn"
        label="{{ 'checkout.paymentCart.proceed' | translate }}"
        pButton
        type="button"
      ></button>

    </div>
  </section>


</div>

<app-payment-method-wrapper
  #paymentMethodWrapper
  [featureFlag]="featureFlag"
  [lightboxConfig]="lightboxConfigObj"
  [apmConfig]="apmConfig"
  (onComplete)="handlePaymentComplete($event)"
  (onCancel)="handlePaymentCancel()"
  (onError)="handlePaymentError()"
></app-payment-method-wrapper>
 