"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[261],{3261:(NI,w,l)=>{l.r(w),l.d(w,{SearchModule:()=>uI});var a=l(6814),u=l(6075),g=l(5879),S=l(553),M=l(5662),p=l(864),s=l(5219),N=l(6663),E=l(7875),_=l(6223),h=l(4480),c=l(6825),L=l(4713);let m=(()=>{class I extends L.s{static \u0275fac=function(){let C;return function(e){return(C||(C=g.n5z(I)))(e||I)}}();static \u0275cmp=g.Xpm({type:I,selectors:[["MinusIcon"]],standalone:!0,features:[g.qOj,g.jDz],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M13.2222 7.77778H0.777778C0.571498 7.77778 0.373667 7.69584 0.227806 7.54998C0.0819442 7.40412 0 7.20629 0 7.00001C0 6.79373 0.0819442 6.5959 0.227806 6.45003C0.373667 6.30417 0.571498 6.22223 0.777778 6.22223H13.2222C13.4285 6.22223 13.6263 6.30417 13.7722 6.45003C13.9181 6.5959 14 6.79373 14 7.00001C14 7.20629 13.9181 7.40412 13.7722 7.54998C13.6263 7.69584 13.4285 7.77778 13.2222 7.77778Z","fill","currentColor"]],template:function(A,e){1&A&&(g.O4$(),g.TgZ(0,"svg",0),g._UZ(1,"path",1),g.qZA()),2&A&&(g.Tol(e.getClassNames()),g.uIk("aria-label",e.ariaLabel)("aria-hidden",e.ariaHidden)("role",e.role))},dependencies:[a.ez],encapsulation:2})}return I})();var x=l(3983);function U(I,n){if(1&I&&(g.TgZ(0,"span",10),g._uU(1),g.qZA()),2&I){const C=g.oxw(2);g.uIk("id",C.id+"_header"),g.xp6(1),g.Oqu(C.header)}}function F(I,n){1&I&&g.GkF(0)}function G(I,n){}function z(I,n){1&I&&g.YNc(0,G,0,0,"ng-template")}function H(I,n){if(1&I&&g._UZ(0,"span",16),2&I){const C=g.oxw(5);g.Tol(C.expandIcon),g.Q6J("ngClass",C.iconClass)}}function B(I,n){if(1&I&&g._UZ(0,"MinusIcon",17),2&I){const C=g.oxw(5);g.Q6J("styleClass",C.iconClass)}}function V(I,n){if(1&I&&(g.ynx(0),g.YNc(1,H,1,3,"span",14),g.YNc(2,B,1,1,"MinusIcon",15),g.BQk()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngIf",C.expandIcon),g.xp6(1),g.Q6J("ngIf",!C.expandIcon)}}function R(I,n){if(1&I&&g._UZ(0,"span",16),2&I){const C=g.oxw(5);g.Tol(C.collapseIcon),g.Q6J("ngClass",C.iconClass)}}function q(I,n){if(1&I&&g._UZ(0,"PlusIcon",17),2&I){const C=g.oxw(5);g.Q6J("styleClass",C.iconClass)}}function j(I,n){if(1&I&&(g.ynx(0),g.YNc(1,R,1,3,"span",14),g.YNc(2,q,1,1,"PlusIcon",15),g.BQk()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngIf",C.collapseIcon),g.xp6(1),g.Q6J("ngIf",!C.collapseIcon)}}function W(I,n){if(1&I&&(g.ynx(0),g.YNc(1,V,3,2,"ng-container",12),g.YNc(2,j,3,2,"ng-container",12),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngIf",!C.collapsed),g.xp6(1),g.Q6J("ngIf",C.collapsed)}}function X(I,n){}function $(I,n){1&I&&g.YNc(0,X,0,0,"ng-template")}const gg=function(I){return{$implicit:I}};function Cg(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"button",11),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onIconClick(e))})("keydown.enter",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onIconClick(e))}),g.YNc(1,W,3,2,"ng-container",12),g.YNc(2,$,1,0,null,13),g.qZA()}if(2&I){const C=g.oxw(2);g.uIk("aria-label","collapse button")("id",C.id+"-label")("aria-controls",C.id+"-content")("aria-expanded",!C.collapsed),g.xp6(1),g.Q6J("ngIf",!C.headerIconTemplate),g.xp6(1),g.Q6J("ngTemplateOutlet",C.headerIconTemplate)("ngTemplateOutletContext",g.VKq(7,gg,C.collapsed))}}const Ig=function(I,n,C){return{"p-panel-icons-start":I,"p-panel-icons-end":n,"p-panel-icons-center":C}};function Ag(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",6),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw();return g.KtG(t.onHeaderClick(e))}),g.YNc(1,U,2,2,"span",7),g.Hsn(2,1),g.YNc(3,F,1,0,"ng-container",4),g.TgZ(4,"div",8),g.YNc(5,z,1,0,null,4),g.YNc(6,Cg,3,9,"button",9),g.qZA()()}if(2&I){const C=g.oxw();g.uIk("id",C.id+"-titlebar"),g.xp6(1),g.Q6J("ngIf",C.header),g.xp6(2),g.Q6J("ngTemplateOutlet",C.headerTemplate),g.xp6(1),g.Q6J("ngClass",g.kEZ(6,Ig,"start"===C.iconPos,"end"===C.iconPos,"center"===C.iconPos)),g.xp6(1),g.Q6J("ngTemplateOutlet",C.iconTemplate),g.xp6(1),g.Q6J("ngIf",C.toggleable)}}function eg(I,n){1&I&&g.GkF(0)}function tg(I,n){1&I&&g.GkF(0)}function ng(I,n){if(1&I&&(g.TgZ(0,"div",18),g.Hsn(1,2),g.YNc(2,tg,1,0,"ng-container",4),g.qZA()),2&I){const C=g.oxw();g.xp6(2),g.Q6J("ngTemplateOutlet",C.footerTemplate)}}const ig=["*",[["p-header"]],[["p-footer"]]],og=function(I,n){return{"p-panel p-component":!0,"p-panel-toggleable":I,"p-panel-expanded":n}},lg=function(I){return{transitionParams:I,height:"0",opacity:"0"}},rg=function(I){return{value:"hidden",params:I}},ag=function(I){return{transitionParams:I,height:"*",opacity:"1"}},sg=function(I){return{value:"visible",params:I}},cg=["*","p-header","p-footer"];let dg=0,pg=(()=>{class I{el;toggleable;header;collapsed;style;styleClass;iconPos="end";expandIcon;collapseIcon;showHeader=!0;toggler="icon";transitionOptions="400ms cubic-bezier(0.86, 0, 0.07, 1)";collapsedChange=new g.vpe;onBeforeToggle=new g.vpe;onAfterToggle=new g.vpe;footerFacet;templates;iconTemplate;animating;headerTemplate;contentTemplate;footerTemplate;headerIconTemplate;id="p-panel-"+dg++;constructor(C){this.el=C}ngAfterContentInit(){this.templates.forEach(C=>{switch(C.getType()){case"header":this.headerTemplate=C.template;break;case"content":default:this.contentTemplate=C.template;break;case"footer":this.footerTemplate=C.template;break;case"icons":this.iconTemplate=C.template;break;case"headericons":this.headerIconTemplate=C.template}})}onHeaderClick(C){"header"===this.toggler&&this.toggle(C)}onIconClick(C){"icon"===this.toggler&&this.toggle(C)}toggle(C){if(this.animating)return!1;this.animating=!0,this.onBeforeToggle.emit({originalEvent:C,collapsed:this.collapsed}),this.toggleable&&(this.collapsed?this.expand():this.collapse()),C.preventDefault()}expand(){this.collapsed=!1,this.collapsedChange.emit(this.collapsed)}collapse(){this.collapsed=!0,this.collapsedChange.emit(this.collapsed)}getBlockableElement(){return this.el.nativeElement.children[0]}onToggleDone(C){this.animating=!1,this.onAfterToggle.emit({originalEvent:C,collapsed:this.collapsed})}static \u0275fac=function(A){return new(A||I)(g.Y36(g.SBq))};static \u0275cmp=g.Xpm({type:I,selectors:[["p-panel"]],contentQueries:function(A,e,t){if(1&A&&(g.Suo(t,s.$_,5),g.Suo(t,s.jx,4)),2&A){let i;g.iGM(i=g.CRH())&&(e.footerFacet=i.first),g.iGM(i=g.CRH())&&(e.templates=i)}},hostAttrs:[1,"p-element"],inputs:{toggleable:"toggleable",header:"header",collapsed:"collapsed",style:"style",styleClass:"styleClass",iconPos:"iconPos",expandIcon:"expandIcon",collapseIcon:"collapseIcon",showHeader:"showHeader",toggler:"toggler",transitionOptions:"transitionOptions"},outputs:{collapsedChange:"collapsedChange",onBeforeToggle:"onBeforeToggle",onAfterToggle:"onAfterToggle"},ngContentSelectors:cg,decls:7,vars:23,consts:[[3,"ngClass","ngStyle"],["class","p-panel-header",3,"click",4,"ngIf"],["role","region",1,"p-toggleable-content"],[1,"p-panel-content"],[4,"ngTemplateOutlet"],["class","p-panel-footer",4,"ngIf"],[1,"p-panel-header",3,"click"],["class","p-panel-title",4,"ngIf"],["role","tablist",1,"p-panel-icons",3,"ngClass"],["type","button","class","p-panel-header-icon p-panel-toggler p-link","pRipple","","role","tab",3,"click","keydown.enter",4,"ngIf"],[1,"p-panel-title"],["type","button","pRipple","","role","tab",1,"p-panel-header-icon","p-panel-toggler","p-link",3,"click","keydown.enter"],[4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"class","ngClass",4,"ngIf"],[3,"styleClass",4,"ngIf"],[3,"ngClass"],[3,"styleClass"],[1,"p-panel-footer"]],template:function(A,e){1&A&&(g.F$t(ig),g.TgZ(0,"div",0),g.YNc(1,Ag,7,10,"div",1),g.TgZ(2,"div",2),g.NdJ("@panelContent.done",function(i){return e.onToggleDone(i)}),g.TgZ(3,"div",3),g.Hsn(4),g.YNc(5,eg,1,0,"ng-container",4),g.qZA(),g.YNc(6,ng,3,1,"div",5),g.qZA()()),2&A&&(g.Tol(e.styleClass),g.Q6J("ngClass",g.WLB(12,og,e.toggleable,!e.collapsed&&e.toggleable))("ngStyle",e.style),g.uIk("id",e.id),g.xp6(1),g.Q6J("ngIf",e.showHeader),g.xp6(1),g.Q6J("@panelContent",e.collapsed?g.VKq(17,rg,g.VKq(15,lg,e.animating?e.transitionOptions:"0ms")):g.VKq(21,sg,g.VKq(19,ag,e.animating?e.transitionOptions:"0ms"))),g.uIk("id",e.id+"-content")("aria-hidden",e.collapsed)("aria-labelledby",e.id+"-titlebar"),g.xp6(3),g.Q6J("ngTemplateOutlet",e.contentTemplate),g.xp6(1),g.Q6J("ngIf",e.footerFacet||e.footerTemplate))},dependencies:function(){return[a.mk,a.O5,a.tP,a.PC,h.H,x.p,m]},styles:[".p-panel-header{display:flex;align-items:center}.p-panel-title{line-height:1;order:1}.p-panel-header-icon{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;text-decoration:none;overflow:hidden;position:relative}.p-panel-toggleable.p-panel-expanded .p-toggleable-content:not(.ng-animating){overflow:visible}.p-panel-toggleable .p-toggleable-content{overflow:hidden}\n"],encapsulation:2,data:{animation:[(0,c.X$)("panelContent",[(0,c.SB)("hidden",(0,c.oB)({height:"0"})),(0,c.SB)("void",(0,c.oB)({height:"{{height}}"}),{params:{height:"0"}}),(0,c.SB)("visible",(0,c.oB)({height:"*"})),(0,c.eR)("visible <=> hidden",[(0,c.jt)("{{transitionParams}}")]),(0,c.eR)("void => hidden",(0,c.jt)("{{transitionParams}}")),(0,c.eR)("void => visible",(0,c.jt)("{{transitionParams}}"))])]},changeDetection:0})}return I})(),_g=(()=>{class I{static \u0275fac=function(A){return new(A||I)};static \u0275mod=g.oAB({type:I});static \u0275inj=g.cJS({imports:[a.ez,s.m8,h.T,x.p,m,s.m8]})}return I})();var f=l(2076),T=l(6489),y=l(2332),k=l(2591),Z=l(6005),P=l(4562),D=l(2324),J=l(8717);const Q=function(I){return{"p-treenode-droppoint-active":I}};function hg(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"li",4),g.NdJ("drop",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPoint(e,-1))})("dragover",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragOver(e))})("dragenter",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragEnter(e,-1))})("dragleave",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragLeave(e))}),g.qZA()}if(2&I){const C=g.oxw(2);g.Q6J("ngClass",g.VKq(1,Q,C.draghoverPrev))}}function fg(I,n){1&I&&g._UZ(0,"ChevronRightIcon",14),2&I&&g.Q6J("styleClass","p-tree-toggler-icon")}function ug(I,n){1&I&&g._UZ(0,"ChevronDownIcon",14),2&I&&g.Q6J("styleClass","p-tree-toggler-icon")}function mg(I,n){if(1&I&&(g.ynx(0),g.YNc(1,fg,1,1,"ChevronRightIcon",13),g.YNc(2,ug,1,1,"ChevronDownIcon",13),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngIf",!C.node.expanded),g.xp6(1),g.Q6J("ngIf",C.node.expanded)}}function xg(I,n){}function vg(I,n){1&I&&g.YNc(0,xg,0,0,"ng-template")}const v=function(I){return{$implicit:I}};function Ng(I,n){if(1&I&&(g.TgZ(0,"span",15),g.YNc(1,vg,1,0,null,16),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngTemplateOutlet",C.tree.togglerIconTemplate)("ngTemplateOutletContext",g.VKq(2,v,C.node.expanded))}}function Tg(I,n){1&I&&g._UZ(0,"CheckIcon",14),2&I&&g.Q6J("styleClass","p-checkbox-icon")}function yg(I,n){1&I&&g._UZ(0,"MinusIcon",14),2&I&&g.Q6J("styleClass","p-checkbox-icon")}function bg(I,n){if(1&I&&(g.ynx(0),g.YNc(1,Tg,1,1,"CheckIcon",13),g.YNc(2,yg,1,1,"MinusIcon",13),g.BQk()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngIf",C.isSelected()),g.xp6(1),g.Q6J("ngIf",C.node.partialSelected)}}function wg(I,n){}function Sg(I,n){1&I&&g.YNc(0,wg,0,0,"ng-template")}const Mg=function(I){return{"p-checkbox-disabled":I}},kg=function(I,n){return{"p-highlight":I,"p-indeterminate":n}},Zg=function(I,n){return{$implicit:I,partialSelected:n}};function Pg(I,n){if(1&I&&(g.TgZ(0,"div",17)(1,"div",18),g.YNc(2,bg,3,2,"ng-container",8),g.YNc(3,Sg,1,0,null,16),g.qZA()()),2&I){const C=g.oxw(3);g.Q6J("ngClass",g.VKq(6,Mg,!1===C.node.selectable)),g.uIk("aria-checked",C.isSelected()),g.xp6(1),g.Q6J("ngClass",g.WLB(8,kg,C.isSelected(),C.node.partialSelected)),g.xp6(1),g.Q6J("ngIf",!C.tree.checkboxIconTemplate),g.xp6(1),g.Q6J("ngTemplateOutlet",C.tree.checkboxIconTemplate)("ngTemplateOutletContext",g.WLB(11,Zg,C.isSelected(),C.node.partialSelected))}}function Dg(I,n){if(1&I&&g._UZ(0,"span"),2&I){const C=g.oxw(3);g.Tol(C.getIcon())}}function Jg(I,n){if(1&I&&(g.TgZ(0,"span"),g._uU(1),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Oqu(C.node.label)}}function Qg(I,n){1&I&&g.GkF(0)}function Og(I,n){if(1&I&&(g.TgZ(0,"span"),g.YNc(1,Qg,1,0,"ng-container",16),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngTemplateOutlet",C.tree.getTemplateForNode(C.node))("ngTemplateOutletContext",g.VKq(2,v,C.node))}}function Yg(I,n){if(1&I&&g._UZ(0,"p-treeNode",21),2&I){const C=n.$implicit,A=n.first,e=n.last,t=n.index,i=g.oxw(4);g.Q6J("node",C)("parentNode",i.node)("firstChild",A)("lastChild",e)("index",t)("itemSize",i.itemSize)("level",i.level+1)}}function Kg(I,n){if(1&I&&(g.TgZ(0,"ul",19),g.YNc(1,Yg,1,7,"p-treeNode",20),g.qZA()),2&I){const C=g.oxw(3);g.Udp("display",C.node.expanded?"block":"none"),g.xp6(1),g.Q6J("ngForOf",C.node.children)("ngForTrackBy",C.tree.trackBy)}}const Eg=function(I,n){return["p-treenode",I,n]},O=function(I){return{height:I}},Lg=function(I,n,C){return{"p-treenode-selectable":I,"p-treenode-dragover":n,"p-highlight":C}};function Ug(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"li",5)(1,"div",6),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onNodeClick(e))})("contextmenu",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onNodeRightClick(e))})("touchend",function(){g.CHM(C);const e=g.oxw(2);return g.KtG(e.onNodeTouchEnd())})("drop",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropNode(e))})("dragover",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropNodeDragOver(e))})("dragenter",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropNodeDragEnter(e))})("dragleave",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropNodeDragLeave(e))})("dragstart",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDragStart(e))})("dragend",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDragStop(e))})("keydown",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onKeyDown(e))}),g.TgZ(2,"button",7),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.toggle(e))}),g.YNc(3,mg,3,2,"ng-container",8),g.YNc(4,Ng,2,4,"span",9),g.qZA(),g.YNc(5,Pg,4,14,"div",10),g.YNc(6,Dg,1,2,"span",3),g.TgZ(7,"span",11),g.YNc(8,Jg,2,1,"span",8),g.YNc(9,Og,2,4,"span",8),g.qZA()(),g.YNc(10,Kg,2,4,"ul",12),g.qZA()}if(2&I){const C=g.oxw(2);g.Akn(C.node.style),g.Q6J("ngClass",g.WLB(22,Eg,C.node.styleClass||"",C.isLeaf()?"p-treenode-leaf":""))("ngStyle",g.VKq(25,O,C.itemSize+"px")),g.xp6(1),g.Udp("padding-left",C.level*C.indentation+"rem"),g.Q6J("draggable",C.tree.draggableNodes)("ngClass",g.kEZ(27,Lg,C.tree.selectionMode&&!1!==C.node.selectable,C.draghoverNode,C.isSelected())),g.uIk("tabindex",0)("aria-posinset",C.index+1)("aria-expanded",C.node.expanded)("aria-selected",C.isSelected())("aria-label",C.node.label)("data-id",C.node.key),g.xp6(1),g.uIk("aria-label",C.tree.togglerAriaLabel),g.xp6(1),g.Q6J("ngIf",!C.tree.togglerIconTemplate),g.xp6(1),g.Q6J("ngIf",C.tree.togglerIconTemplate),g.xp6(1),g.Q6J("ngIf","checkbox"==C.tree.selectionMode),g.xp6(1),g.Q6J("ngIf",C.node.icon||C.node.expandedIcon||C.node.collapsedIcon),g.xp6(2),g.Q6J("ngIf",!C.tree.getTemplateForNode(C.node)),g.xp6(1),g.Q6J("ngIf",C.tree.getTemplateForNode(C.node)),g.xp6(1),g.Q6J("ngIf",!C.tree.virtualScroll&&C.node.children&&C.node.expanded)}}function Fg(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"li",4),g.NdJ("drop",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPoint(e,1))})("dragover",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragOver(e))})("dragenter",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragEnter(e,1))})("dragleave",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onDropPointDragLeave(e))}),g.qZA()}if(2&I){const C=g.oxw(2);g.Q6J("ngClass",g.VKq(1,Q,C.draghoverNext))}}const Y=function(I){return{"p-treenode-connector-line":I}};function Gg(I,n){if(1&I&&(g.TgZ(0,"td",27)(1,"table",28)(2,"tbody")(3,"tr"),g._UZ(4,"td",29),g.qZA(),g.TgZ(5,"tr"),g._UZ(6,"td",29),g.qZA()()()()),2&I){const C=g.oxw(3);g.xp6(4),g.Q6J("ngClass",g.VKq(2,Y,!C.firstChild)),g.xp6(2),g.Q6J("ngClass",g.VKq(4,Y,!C.lastChild))}}function zg(I,n){if(1&I&&g._UZ(0,"PlusIcon",32),2&I){const C=g.oxw(5);g.Q6J("styleClass","p-tree-toggler-icon")("ariaLabel",C.tree.togglerAriaLabel)}}function Hg(I,n){if(1&I&&g._UZ(0,"MinusIcon",32),2&I){const C=g.oxw(5);g.Q6J("styleClass","p-tree-toggler-icon")("ariaLabel",C.tree.togglerAriaLabel)}}function Bg(I,n){if(1&I&&(g.ynx(0),g.YNc(1,zg,1,2,"PlusIcon",31),g.YNc(2,Hg,1,2,"MinusIcon",31),g.BQk()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngIf",!C.node.expanded),g.xp6(1),g.Q6J("ngIf",C.node.expanded)}}function Vg(I,n){}function Rg(I,n){1&I&&g.YNc(0,Vg,0,0,"ng-template")}function qg(I,n){if(1&I&&(g.TgZ(0,"span",15),g.YNc(1,Rg,1,0,null,16),g.qZA()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngTemplateOutlet",C.tree.togglerIconTemplate)("ngTemplateOutletContext",g.VKq(2,v,C.node.expanded))}}function jg(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"span",30),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw(3);return g.KtG(t.toggle(e))}),g.YNc(1,Bg,3,2,"ng-container",8),g.YNc(2,qg,2,4,"span",9),g.qZA()}if(2&I){const C=g.oxw(3);g.Q6J("ngClass","p-tree-toggler"),g.xp6(1),g.Q6J("ngIf",!C.tree.togglerIconTemplate),g.xp6(1),g.Q6J("ngIf",C.tree.togglerIconTemplate)}}function Wg(I,n){if(1&I&&g._UZ(0,"span"),2&I){const C=g.oxw(3);g.Tol(C.getIcon())}}function Xg(I,n){if(1&I&&(g.TgZ(0,"span"),g._uU(1),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Oqu(C.node.label)}}function $g(I,n){1&I&&g.GkF(0)}function gC(I,n){if(1&I&&(g.TgZ(0,"span"),g.YNc(1,$g,1,0,"ng-container",16),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngTemplateOutlet",C.tree.getTemplateForNode(C.node))("ngTemplateOutletContext",g.VKq(2,v,C.node))}}function CC(I,n){if(1&I&&g._UZ(0,"p-treeNode",36),2&I){const A=n.first,e=n.last;g.Q6J("node",n.$implicit)("firstChild",A)("lastChild",e)}}function IC(I,n){if(1&I&&(g.TgZ(0,"td",33)(1,"div",34),g.YNc(2,CC,1,3,"p-treeNode",35),g.qZA()()),2&I){const C=g.oxw(3);g.Udp("display",C.node.expanded?"table-cell":"none"),g.xp6(2),g.Q6J("ngForOf",C.node.children)("ngForTrackBy",C.tree.trackBy)}}const AC=function(I){return{"p-treenode-collapsed":I}},eC=function(I,n){return{"p-treenode-selectable":I,"p-highlight":n}};function tC(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"table")(1,"tbody")(2,"tr"),g.YNc(3,Gg,7,6,"td",22),g.TgZ(4,"td",23)(5,"div",24),g.NdJ("click",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onNodeClick(e))})("contextmenu",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onNodeRightClick(e))})("touchend",function(){g.CHM(C);const e=g.oxw(2);return g.KtG(e.onNodeTouchEnd())})("keydown",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onNodeKeydown(e))}),g.YNc(6,jg,3,3,"span",25),g.YNc(7,Wg,1,2,"span",3),g.TgZ(8,"span",11),g.YNc(9,Xg,2,1,"span",8),g.YNc(10,gC,2,4,"span",8),g.qZA()()(),g.YNc(11,IC,3,4,"td",26),g.qZA()()()}if(2&I){const C=g.oxw(2);g.Tol(C.node.styleClass),g.xp6(3),g.Q6J("ngIf",!C.root),g.xp6(1),g.Q6J("ngClass",g.VKq(10,AC,!C.node.expanded)),g.xp6(1),g.Q6J("ngClass",g.WLB(12,eC,C.tree.selectionMode,C.isSelected())),g.xp6(1),g.Q6J("ngIf",!C.isLeaf()),g.xp6(1),g.Q6J("ngIf",C.node.icon||C.node.expandedIcon||C.node.collapsedIcon),g.xp6(2),g.Q6J("ngIf",!C.tree.getTemplateForNode(C.node)),g.xp6(1),g.Q6J("ngIf",C.tree.getTemplateForNode(C.node)),g.xp6(1),g.Q6J("ngIf",C.node.children&&C.node.expanded)}}function nC(I,n){if(1&I&&(g.YNc(0,hg,1,3,"li",1),g.YNc(1,Ug,11,31,"li",2),g.YNc(2,Fg,1,3,"li",1),g.YNc(3,tC,12,15,"table",3)),2&I){const C=g.oxw();g.Q6J("ngIf",C.tree.droppableNodes),g.xp6(1),g.Q6J("ngIf",!C.tree.horizontal),g.xp6(1),g.Q6J("ngIf",C.tree.droppableNodes&&C.lastChild),g.xp6(1),g.Q6J("ngIf",C.tree.horizontal)}}const iC=["filter"],oC=["scroller"],lC=["wrapper"];function rC(I,n){if(1&I&&g._UZ(0,"i"),2&I){const C=g.oxw(3);g.Tol("p-tree-loading-icon pi-spin "+C.loadingIcon)}}function aC(I,n){1&I&&g._UZ(0,"SpinnerIcon",13),2&I&&g.Q6J("spin",!0)("styleClass","p-tree-loading-icon")}function sC(I,n){}function cC(I,n){1&I&&g.YNc(0,sC,0,0,"ng-template")}function dC(I,n){if(1&I&&(g.TgZ(0,"span",14),g.YNc(1,cC,1,0,null,4),g.qZA()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngTemplateOutlet",C.loadingIconTemplate)}}function pC(I,n){if(1&I&&(g.ynx(0),g.YNc(1,aC,1,2,"SpinnerIcon",11),g.YNc(2,dC,2,1,"span",12),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngIf",!C.loadingIconTemplate),g.xp6(1),g.Q6J("ngIf",C.loadingIconTemplate)}}function _C(I,n){if(1&I&&(g.TgZ(0,"div",9),g.YNc(1,rC,1,2,"i",10),g.YNc(2,pC,3,2,"ng-container",7),g.qZA()),2&I){const C=g.oxw(2);g.xp6(1),g.Q6J("ngIf",C.loadingIcon),g.xp6(1),g.Q6J("ngIf",!C.loadingIcon)}}function hC(I,n){1&I&&g.GkF(0)}function fC(I,n){1&I&&g._UZ(0,"SearchIcon",20),2&I&&g.Q6J("styleClass","p-tree-filter-icon")}function uC(I,n){}function mC(I,n){1&I&&g.YNc(0,uC,0,0,"ng-template")}function xC(I,n){if(1&I&&(g.TgZ(0,"span",21),g.YNc(1,mC,1,0,null,4),g.qZA()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngTemplateOutlet",C.filterIconTemplate)}}function vC(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",15)(1,"input",16,17),g.NdJ("keydown.enter",function(e){return e.preventDefault()})("input",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t._filter(e.target.value))}),g.qZA(),g.YNc(3,fC,1,1,"SearchIcon",18),g.YNc(4,xC,2,1,"span",19),g.qZA()}if(2&I){const C=g.oxw(2);g.xp6(1),g.uIk("placeholder",C.filterPlaceholder),g.xp6(2),g.Q6J("ngIf",!C.filterIconTemplate),g.xp6(1),g.Q6J("ngIf",C.filterIconTemplate)}}function NC(I,n){if(1&I&&g._UZ(0,"p-treeNode",28,29),2&I){const C=n.$implicit,A=n.first,e=n.last,t=n.index,i=g.oxw(2).options,o=g.oxw(3);g.Q6J("level",C.level)("rowNode",C)("node",C.node)("firstChild",A)("lastChild",e)("index",o.getIndex(i,t))("itemSize",i.itemSize)("indentation",o.indentation)}}function TC(I,n){if(1&I&&(g.TgZ(0,"ul",26),g.YNc(1,NC,2,8,"p-treeNode",27),g.qZA()),2&I){const C=g.oxw(),A=C.options,e=C.$implicit,t=g.oxw(3);g.Akn(A.contentStyle),g.Q6J("ngClass",A.contentStyleClass),g.uIk("aria-label",t.ariaLabel)("aria-labelledby",t.ariaLabelledBy),g.xp6(1),g.Q6J("ngForOf",e)("ngForTrackBy",t.trackBy)}}function yC(I,n){1&I&&g.YNc(0,TC,2,7,"ul",25),2&I&&g.Q6J("ngIf",n.$implicit)}function bC(I,n){1&I&&g.GkF(0)}const wC=function(I){return{options:I}};function SC(I,n){if(1&I&&g.YNc(0,bC,1,0,"ng-container",31),2&I){const C=n.options,A=g.oxw(4);g.Q6J("ngTemplateOutlet",A.loaderTemplate)("ngTemplateOutletContext",g.VKq(2,wC,C))}}function MC(I,n){1&I&&(g.ynx(0),g.YNc(1,SC,1,4,"ng-template",30),g.BQk())}function kC(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"p-scroller",22,23),g.NdJ("onScroll",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onScroll.emit(e))})("onScrollIndexChange",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onScrollIndexChange.emit(e))})("onLazyLoad",function(e){g.CHM(C);const t=g.oxw(2);return g.KtG(t.onLazyLoad.emit(e))}),g.YNc(2,yC,1,1,"ng-template",24),g.YNc(3,MC,2,0,"ng-container",7),g.qZA()}if(2&I){const C=g.oxw(2);g.Akn(g.VKq(9,O,"flex"!==C.scrollHeight?C.scrollHeight:void 0)),g.Q6J("items",C.serializedValue)("tabindex",-1)("scrollHeight","flex"!==C.scrollHeight?void 0:"100%")("itemSize",C.virtualScrollItemSize||C._virtualNodeHeight)("lazy",C.lazy)("options",C.virtualScrollOptions),g.xp6(3),g.Q6J("ngIf",C.loaderTemplate)}}function ZC(I,n){if(1&I&&g._UZ(0,"p-treeNode",37),2&I){const A=n.first,e=n.last,t=n.index;g.Q6J("node",n.$implicit)("firstChild",A)("lastChild",e)("index",t)("level",0)}}function PC(I,n){if(1&I&&(g.TgZ(0,"ul",35),g.YNc(1,ZC,1,5,"p-treeNode",36),g.qZA()),2&I){const C=g.oxw(3);g.uIk("aria-label",C.ariaLabel)("aria-labelledby",C.ariaLabelledBy),g.xp6(1),g.Q6J("ngForOf",C.getRootNode())("ngForTrackBy",C.trackBy)}}function DC(I,n){if(1&I&&(g.ynx(0),g.TgZ(1,"div",32,33),g.YNc(3,PC,2,4,"ul",34),g.qZA(),g.BQk()),2&I){const C=g.oxw(2);g.xp6(1),g.Udp("max-height",C.scrollHeight),g.xp6(2),g.Q6J("ngIf",C.getRootNode())}}function JC(I,n){if(1&I&&(g.ynx(0),g._uU(1),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.hij(" ",C.emptyMessageLabel," ")}}function QC(I,n){1&I&&g.GkF(0,null,40)}function OC(I,n){if(1&I&&(g.TgZ(0,"div",38),g.YNc(1,JC,2,1,"ng-container",39),g.YNc(2,QC,2,0,"ng-container",4),g.qZA()),2&I){const C=g.oxw(2);g.xp6(1),g.Q6J("ngIf",!C.emptyMessageTemplate)("ngIfElse",C.emptyFilter),g.xp6(1),g.Q6J("ngTemplateOutlet",C.emptyMessageTemplate)}}function YC(I,n){1&I&&g.GkF(0)}const KC=function(I,n,C,A){return{"p-tree p-component":!0,"p-tree-selectable":I,"p-treenode-dragover":n,"p-tree-loading":C,"p-tree-flex-scrollable":A}};function EC(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",2),g.NdJ("drop",function(e){g.CHM(C);const t=g.oxw();return g.KtG(t.onDrop(e))})("dragover",function(e){g.CHM(C);const t=g.oxw();return g.KtG(t.onDragOver(e))})("dragenter",function(){g.CHM(C);const e=g.oxw();return g.KtG(e.onDragEnter())})("dragleave",function(e){g.CHM(C);const t=g.oxw();return g.KtG(t.onDragLeave(e))}),g.YNc(1,_C,3,2,"div",3),g.YNc(2,hC,1,0,"ng-container",4),g.YNc(3,vC,5,3,"div",5),g.YNc(4,kC,4,11,"p-scroller",6),g.YNc(5,DC,4,3,"ng-container",7),g.YNc(6,OC,3,3,"div",8),g.YNc(7,YC,1,0,"ng-container",4),g.qZA()}if(2&I){const C=g.oxw();g.Tol(C.styleClass),g.Q6J("ngClass",g.l5B(11,KC,C.selectionMode,C.dragHover,C.loading,"flex"===C.scrollHeight))("ngStyle",C.style),g.xp6(1),g.Q6J("ngIf",C.loading),g.xp6(1),g.Q6J("ngTemplateOutlet",C.headerTemplate),g.xp6(1),g.Q6J("ngIf",C.filter),g.xp6(1),g.Q6J("ngIf",C.virtualScroll),g.xp6(1),g.Q6J("ngIf",!C.virtualScroll),g.xp6(1),g.Q6J("ngIf",!C.loading&&(null==C.getRootNode()||0===C.getRootNode().length)),g.xp6(1),g.Q6J("ngTemplateOutlet",C.footerTemplate)}}function LC(I,n){1&I&&g.GkF(0)}function UC(I,n){if(1&I&&g._UZ(0,"i"),2&I){const C=g.oxw(3);g.Tol("p-tree-loading-icon pi-spin "+C.loadingIcon)}}function FC(I,n){1&I&&g._UZ(0,"SpinnerIcon",13),2&I&&g.Q6J("spin",!0)("styleClass","p-tree-loading-icon")}function GC(I,n){}function zC(I,n){1&I&&g.YNc(0,GC,0,0,"ng-template")}function HC(I,n){if(1&I&&(g.TgZ(0,"span",14),g.YNc(1,zC,1,0,null,4),g.qZA()),2&I){const C=g.oxw(4);g.xp6(1),g.Q6J("ngTemplateOutlet",C.loadingIconTemplate)}}function BC(I,n){if(1&I&&(g.ynx(0),g.YNc(1,FC,1,2,"SpinnerIcon",11),g.YNc(2,HC,2,1,"span",12),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.Q6J("ngIf",!C.loadingIconTemplate),g.xp6(1),g.Q6J("ngIf",C.loadingIconTemplate)}}function VC(I,n){if(1&I&&(g.TgZ(0,"div",43),g.YNc(1,UC,1,2,"i",10),g.YNc(2,BC,3,2,"ng-container",7),g.qZA()),2&I){const C=g.oxw(2);g.xp6(1),g.Q6J("ngIf",C.loadingIcon),g.xp6(1),g.Q6J("ngIf",!C.loadingIcon)}}function RC(I,n){if(1&I&&(g.TgZ(0,"table"),g._UZ(1,"p-treeNode",44),g.qZA()),2&I){const C=g.oxw(2);g.xp6(1),g.Q6J("node",C.value[0])("root",!0)}}function qC(I,n){if(1&I&&(g.ynx(0),g._uU(1),g.BQk()),2&I){const C=g.oxw(3);g.xp6(1),g.hij(" ",C.emptyMessageLabel," ")}}function jC(I,n){1&I&&g.GkF(0,null,40)}function WC(I,n){if(1&I&&(g.TgZ(0,"div",38),g.YNc(1,qC,2,1,"ng-container",39),g.YNc(2,jC,2,0,"ng-container",4),g.qZA()),2&I){const C=g.oxw(2);g.xp6(1),g.Q6J("ngIf",!C.emptyMessageTemplate)("ngIfElse",C.emptyFilter),g.xp6(1),g.Q6J("ngTemplateOutlet",C.emptyMessageTemplate)}}function XC(I,n){1&I&&g.GkF(0)}const $C=function(I){return{"p-tree p-tree-horizontal p-component":!0,"p-tree-selectable":I}};function gI(I,n){if(1&I&&(g.TgZ(0,"div",41),g.YNc(1,LC,1,0,"ng-container",4),g.YNc(2,VC,3,2,"div",42),g.YNc(3,RC,2,2,"table",7),g.YNc(4,WC,3,3,"div",8),g.YNc(5,XC,1,0,"ng-container",4),g.qZA()),2&I){const C=g.oxw();g.Tol(C.styleClass),g.Q6J("ngClass",g.VKq(9,$C,C.selectionMode))("ngStyle",C.style),g.xp6(1),g.Q6J("ngTemplateOutlet",C.headerTemplate),g.xp6(1),g.Q6J("ngIf",C.loading),g.xp6(1),g.Q6J("ngIf",C.value&&C.value[0]),g.xp6(1),g.Q6J("ngIf",!C.loading&&(null==C.getRootNode()||0===C.getRootNode().length)),g.xp6(1),g.Q6J("ngTemplateOutlet",C.footerTemplate)}}let CI=(()=>{class I{static ICON_CLASS="p-treenode-icon ";rowNode;node;parentNode;root;index;firstChild;lastChild;level;indentation;itemSize;tree;timeout;draghoverPrev;draghoverNext;draghoverNode;constructor(C){this.tree=C}ngOnInit(){this.node.parent=this.parentNode,this.parentNode&&this.tree.syncNodeOption(this.node,this.tree.value,"parent",this.tree.getNodeWithKey(this.parentNode.key,this.tree.value))}getIcon(){let C;return C=this.node.icon?this.node.icon:this.node.expanded&&this.node.children&&this.node.children?.length?this.node.expandedIcon:this.node.collapsedIcon,I.ICON_CLASS+" "+C}isLeaf(){return this.tree.isNodeLeaf(this.node)}toggle(C){this.node.expanded?this.collapse(C):this.expand(C),C.stopPropagation()}expand(C){this.node.expanded=!0,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeExpand.emit({originalEvent:C,node:this.node})}collapse(C){this.node.expanded=!1,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeCollapse.emit({originalEvent:C,node:this.node})}onNodeClick(C){this.tree.onNodeClick(C,this.node)}onNodeKeydown(C){13===C.which&&this.tree.onNodeClick(C,this.node)}onNodeTouchEnd(){this.tree.onNodeTouchEnd()}onNodeRightClick(C){this.tree.onNodeRightClick(C,this.node)}isSelected(){return this.tree.isSelected(this.node)}onDropPoint(C,A){C.preventDefault();let e=this.tree.dragNode,o=this.tree.dragNodeTree!==this.tree||1===A||this.tree.dragNodeIndex!==this.index-1;if(this.tree.allowDrop(e,this.node,this.tree.dragNodeScope)&&o){let r={...this.createDropPointEventMetadata(A)};this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:C,dragNode:e,dropNode:this.node,index:this.index,accept:()=>{this.processPointDrop(r)}}):(this.processPointDrop(r),this.tree.onNodeDrop.emit({originalEvent:C,dragNode:e,dropNode:this.node,index:this.index}))}this.draghoverPrev=!1,this.draghoverNext=!1}processPointDrop(C){let A=C.dropNode.parent?C.dropNode.parent.children:this.tree.value;C.dragNodeSubNodes.splice(C.dragNodeIndex,1);let e=this.index;C.position<0?(e=C.dragNodeSubNodes===A?C.dragNodeIndex>C.index?C.index:C.index-1:C.index,A.splice(e,0,C.dragNode)):(e=A.length,A.push(C.dragNode)),this.tree.dragDropService.stopDrag({node:C.dragNode,subNodes:C.dropNode.parent?C.dropNode.parent.children:this.tree.value,index:C.dragNodeIndex})}createDropPointEventMetadata(C){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node,index:this.index,position:C}}onDropPointDragOver(C){C.dataTransfer.dropEffect="move",C.preventDefault()}onDropPointDragEnter(C,A){this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(A<0?this.draghoverPrev=!0:this.draghoverNext=!0)}onDropPointDragLeave(C){this.draghoverPrev=!1,this.draghoverNext=!1}onDragStart(C){this.tree.draggableNodes&&!1!==this.node.draggable?(C.dataTransfer.setData("text","data"),this.tree.dragDropService.startDrag({tree:this,node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index,scope:this.tree.draggableScope})):C.preventDefault()}onDragStop(C){this.tree.dragDropService.stopDrag({node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index})}onDropNodeDragOver(C){C.dataTransfer.dropEffect="move",this.tree.droppableNodes&&(C.preventDefault(),C.stopPropagation())}onDropNode(C){if(this.tree.droppableNodes&&!1!==this.node?.droppable){let A=this.tree.dragNode;if(this.tree.allowDrop(A,this.node,this.tree.dragNodeScope)){let e={...this.createDropNodeEventMetadata()};this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:C,dragNode:A,dropNode:this.node,index:this.index,accept:()=>{this.processNodeDrop(e)}}):(this.processNodeDrop(e),this.tree.onNodeDrop.emit({originalEvent:C,dragNode:A,dropNode:this.node,index:this.index}))}}C.preventDefault(),C.stopPropagation(),this.draghoverNode=!1}createDropNodeEventMetadata(){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node}}processNodeDrop(C){let A=C.dragNodeIndex;C.dragNodeSubNodes.splice(A,1),C.dropNode.children?C.dropNode.children.push(C.dragNode):C.dropNode.children=[C.dragNode],this.tree.dragDropService.stopDrag({node:C.dragNode,subNodes:C.dropNode.parent?C.dropNode.parent.children:this.tree.value,index:A})}onDropNodeDragEnter(C){this.tree.droppableNodes&&!1!==this.node?.droppable&&this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(this.draghoverNode=!0)}onDropNodeDragLeave(C){if(this.tree.droppableNodes){let A=C.currentTarget.getBoundingClientRect();(C.x>A.left+A.width||C.x<A.left||C.y>=Math.floor(A.top+A.height)||C.y<A.top)&&(this.draghoverNode=!1)}}onKeyDown(C){const A=C.target.parentElement?.parentElement;if(!("P-TREENODE"!==A?.nodeName||this.tree.contextMenu&&"block"===this.tree.contextMenu.containerViewChild.nativeElement.style.display))switch(C.which){case 40:const e=this.tree.droppableNodes?A.children[1].children[1]:A.children[0].children[1];if(e&&e.children.length>0)this.focusNode(e.children[0]);else{const t=A.nextElementSibling;if(t)this.focusNode(t);else{let i=this.findNextSiblingOfAncestor(A);i&&this.focusNode(i)}}C.preventDefault();break;case 38:if(A.previousElementSibling)this.focusNode(this.findLastVisibleDescendant(A.previousElementSibling));else{let t=this.getParentNodeElement(A);t&&this.focusNode(t)}C.preventDefault();break;case 39:!this.node?.expanded&&!this.tree.isNodeLeaf(this.node)&&this.expand(C),C.preventDefault();break;case 37:if(this.node?.expanded)this.collapse(C);else{let t=this.getParentNodeElement(A);t&&this.focusNode(t)}C.preventDefault();break;case 13:this.tree.onNodeClick(C,this.node),C.preventDefault()}}findNextSiblingOfAncestor(C){let A=this.getParentNodeElement(C);return A?A.nextElementSibling?A.nextElementSibling:this.findNextSiblingOfAncestor(A):null}findLastVisibleDescendant(C){const e=Array.from(C.children).find(t=>f.p.hasClass(t,"p-treenode")).children[1];return e&&e.children.length>0?this.findLastVisibleDescendant(e.children[e.children.length-1]):C}getParentNodeElement(C){const A=C.parentElement?.parentElement?.parentElement;return"P-TREENODE"===A?.tagName?A:null}focusNode(C){this.tree.droppableNodes?C.children[1].children[0].focus():C.children[0].children[0].focus()}focusVirtualNode(){this.timeout=setTimeout(()=>{let C=f.p.findSingle(document.body,`[data-id="${this.node?.key??this.node?.data}"]`);f.p.focus(C)},1)}static \u0275fac=function(A){return new(A||I)(g.Y36((0,g.Gpc)(()=>K)))};static \u0275cmp=g.Xpm({type:I,selectors:[["p-treeNode"]],hostAttrs:[1,"p-element"],inputs:{rowNode:"rowNode",node:"node",parentNode:"parentNode",root:"root",index:"index",firstChild:"firstChild",lastChild:"lastChild",level:"level",indentation:"indentation",itemSize:"itemSize"},decls:1,vars:1,consts:[[3,"ngIf"],["class","p-treenode-droppoint",3,"ngClass","drop","dragover","dragenter","dragleave",4,"ngIf"],[3,"ngClass","ngStyle","style",4,"ngIf"],[3,"class",4,"ngIf"],[1,"p-treenode-droppoint",3,"ngClass","drop","dragover","dragenter","dragleave"],[3,"ngClass","ngStyle"],["role","treeitem",1,"p-treenode-content",3,"draggable","ngClass","click","contextmenu","touchend","drop","dragover","dragenter","dragleave","dragstart","dragend","keydown"],["type","button","pRipple","","tabindex","-1",1,"p-tree-toggler","p-link",3,"click"],[4,"ngIf"],["class","p-tree-toggler-icon",4,"ngIf"],["class","p-checkbox p-component",3,"ngClass",4,"ngIf"],[1,"p-treenode-label"],["class","p-treenode-children","style","display: none;","role","group",3,"display",4,"ngIf"],[3,"styleClass",4,"ngIf"],[3,"styleClass"],[1,"p-tree-toggler-icon"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"p-checkbox","p-component",3,"ngClass"],[1,"p-checkbox-box",3,"ngClass"],["role","group",1,"p-treenode-children",2,"display","none"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level"],["class","p-treenode-connector",4,"ngIf"],[1,"p-treenode",3,"ngClass"],["tabindex","0",1,"p-treenode-content",3,"ngClass","click","contextmenu","touchend","keydown"],[3,"ngClass","click",4,"ngIf"],["class","p-treenode-children-container",3,"display",4,"ngIf"],[1,"p-treenode-connector"],[1,"p-treenode-connector-table"],[3,"ngClass"],[3,"ngClass","click"],[3,"styleClass","ariaLabel",4,"ngIf"],[3,"styleClass","ariaLabel"],[1,"p-treenode-children-container"],[1,"p-treenode-children"],[3,"node","firstChild","lastChild",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","firstChild","lastChild"]],template:function(A,e){1&A&&g.YNc(0,nC,4,4,"ng-template",0),2&A&&g.Q6J("ngIf",e.node)},dependencies:function(){return[a.mk,a.sg,a.O5,a.tP,a.PC,h.H,k.n,Z.v,P.X,m,x.p,I]},encapsulation:2})}return I})(),K=(()=>{class I{el;dragDropService;config;cd;value;selectionMode;selection;style;styleClass;contextMenu;layout="vertical";draggableScope;droppableScope;draggableNodes;droppableNodes;metaKeySelection=!0;propagateSelectionUp=!0;propagateSelectionDown=!0;loading;loadingIcon;emptyMessage="";ariaLabel;togglerAriaLabel;ariaLabelledBy;validateDrop;filter;filterBy="label";filterMode="lenient";filterPlaceholder;filteredNodes;filterLocale;scrollHeight;lazy=!1;virtualScroll;virtualScrollItemSize;virtualScrollOptions;indentation=1.5;_templateMap;trackBy=(C,A)=>A;_virtualNodeHeight;get virtualNodeHeight(){return this._virtualNodeHeight}set virtualNodeHeight(C){this._virtualNodeHeight=C,console.warn("The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.")}selectionChange=new g.vpe;onNodeSelect=new g.vpe;onNodeUnselect=new g.vpe;onNodeExpand=new g.vpe;onNodeCollapse=new g.vpe;onNodeContextMenuSelect=new g.vpe;onNodeDrop=new g.vpe;onLazyLoad=new g.vpe;onScroll=new g.vpe;onScrollIndexChange=new g.vpe;onFilter=new g.vpe;templates;filterViewChild;scroller;wrapperViewChild;serializedValue;headerTemplate;footerTemplate;loaderTemplate;emptyMessageTemplate;togglerIconTemplate;checkboxIconTemplate;loadingIconTemplate;filterIconTemplate;nodeTouched;dragNodeTree;dragNode;dragNodeSubNodes;dragNodeIndex;dragNodeScope;dragHover;dragStartSubscription;dragStopSubscription;constructor(C,A,e,t){this.el=C,this.dragDropService=A,this.config=e,this.cd=t}ngOnInit(){this.droppableNodes&&(this.dragStartSubscription=this.dragDropService.dragStart$.subscribe(C=>{this.dragNodeTree=C.tree,this.dragNode=C.node,this.dragNodeSubNodes=C.subNodes,this.dragNodeIndex=C.index,this.dragNodeScope=C.scope}),this.dragStopSubscription=this.dragDropService.dragStop$.subscribe(C=>{this.dragNodeTree=null,this.dragNode=null,this.dragNodeSubNodes=null,this.dragNodeIndex=null,this.dragNodeScope=null,this.dragHover=!1}))}ngOnChanges(C){C.value&&this.updateSerializedValue()}get horizontal(){return"horizontal"==this.layout}get emptyMessageLabel(){return this.emptyMessage||this.config.getTranslation(s.ws.EMPTY_MESSAGE)}ngAfterContentInit(){this.templates.length&&(this._templateMap={}),this.templates.forEach(C=>{switch(C.getType()){case"header":this.headerTemplate=C.template;break;case"empty":this.emptyMessageTemplate=C.template;break;case"footer":this.footerTemplate=C.template;break;case"loader":this.loaderTemplate=C.template;break;case"togglericon":this.togglerIconTemplate=C.template;break;case"checkboxicon":this.checkboxIconTemplate=C.template;break;case"loadingicon":this.loadingIconTemplate=C.template;break;case"filtericon":this.filterIconTemplate=C.template;break;default:this._templateMap[C.name]=C.template}})}updateSerializedValue(){this.serializedValue=[],this.serializeNodes(null,this.getRootNode(),0,!0)}serializeNodes(C,A,e,t){if(A&&A.length)for(let i of A){i.parent=C;const o={node:i,parent:C,level:e,visible:t&&(!C||C.expanded)};this.serializedValue.push(o),o.visible&&i.expanded&&this.serializeNodes(i,i.children,e+1,o.visible)}}onNodeClick(C,A){let e=C.target;if(!f.p.hasClass(e,"p-tree-toggler")&&!f.p.hasClass(e,"p-tree-toggler-icon")){if(this.selectionMode){if(!1===A.selectable||this.hasFilteredNodes()&&!(A=this.getNodeWithKey(A.key,this.value)))return;let t=this.findIndexInSelection(A),i=t>=0;if(this.isCheckboxSelectionMode())i?(this.propagateSelectionDown?this.propagateDown(A,!1):this.selection=this.selection.filter((o,r)=>r!=t),this.propagateSelectionUp&&A.parent&&this.propagateUp(A.parent,!1),this.selectionChange.emit(this.selection),this.onNodeUnselect.emit({originalEvent:C,node:A})):(this.propagateSelectionDown?this.propagateDown(A,!0):this.selection=[...this.selection||[],A],this.propagateSelectionUp&&A.parent&&this.propagateUp(A.parent,!0),this.selectionChange.emit(this.selection),this.onNodeSelect.emit({originalEvent:C,node:A}));else if(!this.nodeTouched&&this.metaKeySelection){let r=C.metaKey||C.ctrlKey;i&&r?(this.isSingleSelectionMode()?this.selectionChange.emit(null):(this.selection=this.selection.filter((d,b)=>b!=t),this.selectionChange.emit(this.selection)),this.onNodeUnselect.emit({originalEvent:C,node:A})):(this.isSingleSelectionMode()?this.selectionChange.emit(A):this.isMultipleSelectionMode()&&(this.selection=r&&this.selection||[],this.selection=[...this.selection,A],this.selectionChange.emit(this.selection)),this.onNodeSelect.emit({originalEvent:C,node:A}))}else this.isSingleSelectionMode()?i?(this.selection=null,this.onNodeUnselect.emit({originalEvent:C,node:A})):(this.selection=A,this.onNodeSelect.emit({originalEvent:C,node:A})):i?(this.selection=this.selection.filter((r,d)=>d!=t),this.onNodeUnselect.emit({originalEvent:C,node:A})):(this.selection=[...this.selection||[],A],this.onNodeSelect.emit({originalEvent:C,node:A})),this.selectionChange.emit(this.selection)}this.nodeTouched=!1}}onNodeTouchEnd(){this.nodeTouched=!0}onNodeRightClick(C,A){if(this.contextMenu){let e=C.target;if(e.className&&0===e.className.indexOf("p-tree-toggler"))return;this.findIndexInSelection(A)>=0||(this.isSingleSelectionMode()?this.selectionChange.emit(A):this.selectionChange.emit([A])),this.contextMenu.show(C),this.onNodeContextMenuSelect.emit({originalEvent:C,node:A})}}findIndexInSelection(C){let A=-1;if(this.selectionMode&&this.selection)if(this.isSingleSelectionMode())A=this.selection.key&&this.selection.key===C.key||this.selection==C?0:-1;else for(let e=0;e<this.selection.length;e++){let t=this.selection[e];if(t.key&&t.key===C.key||t==C){A=e;break}}return A}syncNodeOption(C,A,e,t){const i=this.hasFilteredNodes()?this.getNodeWithKey(C.key,A):null;i&&(i[e]=t||C[e])}hasFilteredNodes(){return this.filter&&this.filteredNodes&&this.filteredNodes.length}getNodeWithKey(C,A){for(let e of A){if(e.key===C)return e;if(e.children){let t=this.getNodeWithKey(C,e.children);if(t)return t}}}propagateUp(C,A){if(C.children&&C.children.length){let t=0,i=!1;for(let o of C.children)this.isSelected(o)?t++:o.partialSelected&&(i=!0);if(A&&t==C.children.length)this.selection=[...this.selection||[],C],C.partialSelected=!1;else{if(!A){let o=this.findIndexInSelection(C);o>=0&&(this.selection=this.selection.filter((r,d)=>d!=o))}C.partialSelected=!!(i||t>0&&t!=C.children.length)}this.syncNodeOption(C,this.filteredNodes,"partialSelected")}let e=C.parent;e&&this.propagateUp(e,A)}propagateDown(C,A){let e=this.findIndexInSelection(C);if(A&&-1==e?this.selection=[...this.selection||[],C]:!A&&e>-1&&(this.selection=this.selection.filter((t,i)=>i!=e)),C.partialSelected=!1,this.syncNodeOption(C,this.filteredNodes,"partialSelected"),C.children&&C.children.length)for(let t of C.children)this.propagateDown(t,A)}isSelected(C){return-1!=this.findIndexInSelection(C)}isSingleSelectionMode(){return this.selectionMode&&"single"==this.selectionMode}isMultipleSelectionMode(){return this.selectionMode&&"multiple"==this.selectionMode}isCheckboxSelectionMode(){return this.selectionMode&&"checkbox"==this.selectionMode}isNodeLeaf(C){return 0!=C.leaf&&!(C.children&&C.children.length)}getRootNode(){return this.filteredNodes?this.filteredNodes:this.value}getTemplateForNode(C){return this._templateMap?C.type?this._templateMap[C.type]:this._templateMap.default:null}onDragOver(C){this.droppableNodes&&(!this.value||0===this.value.length)&&(C.dataTransfer.dropEffect="move",C.preventDefault())}onDrop(C){if(this.droppableNodes&&(!this.value||0===this.value.length)){C.preventDefault();let A=this.dragNode;if(this.allowDrop(A,null,this.dragNodeScope)){let e=this.dragNodeIndex;this.value=this.value||[],this.validateDrop?this.onNodeDrop.emit({originalEvent:C,dragNode:A,dropNode:null,index:e,accept:()=>{this.processTreeDrop(A,e)}}):(this.onNodeDrop.emit({originalEvent:C,dragNode:A,dropNode:null,index:e}),this.processTreeDrop(A,e))}}}processTreeDrop(C,A){this.dragNodeSubNodes.splice(A,1),this.value.push(C),this.dragDropService.stopDrag({node:C})}onDragEnter(){this.droppableNodes&&this.allowDrop(this.dragNode,null,this.dragNodeScope)&&(this.dragHover=!0)}onDragLeave(C){if(this.droppableNodes){let A=C.currentTarget.getBoundingClientRect();(C.x>A.left+A.width||C.x<A.left||C.y>A.top+A.height||C.y<A.top)&&(this.dragHover=!1)}}allowDrop(C,A,e){if(C){if(this.isValidDragScope(e)){let t=!0;if(A)if(C===A)t=!1;else{let i=A.parent;for(;null!=i;){if(i===C){t=!1;break}i=i.parent}}return t}return!1}return!1}isValidDragScope(C){let A=this.droppableScope;if(A){if("string"==typeof A){if("string"==typeof C)return A===C;if(Array.isArray(C))return-1!=C.indexOf(A)}else if(Array.isArray(A)){if("string"==typeof C)return-1!=A.indexOf(C);if(Array.isArray(C))for(let e of A)for(let t of C)if(e===t)return!0}return!1}return!0}_filter(C){let A=C;if(""===A)this.filteredNodes=null;else{this.filteredNodes=[];const e=this.filterBy.split(","),t=y.gb.removeAccents(A).toLocaleLowerCase(this.filterLocale),i="strict"===this.filterMode;for(let o of this.value){let r={...o},d={searchFields:e,filterText:t,isStrictMode:i};(i&&(this.findFilteredNodes(r,d)||this.isFilterMatched(r,d))||!i&&(this.isFilterMatched(r,d)||this.findFilteredNodes(r,d)))&&this.filteredNodes.push(r)}}this.updateSerializedValue(),this.onFilter.emit({filter:A,filteredValue:this.filteredNodes})}resetFilter(){this.filteredNodes=null,this.filterViewChild&&this.filterViewChild.nativeElement&&(this.filterViewChild.nativeElement.value="")}scrollToVirtualIndex(C){this.virtualScroll&&this.scroller?.scrollToIndex(C)}scrollTo(C){this.virtualScroll?this.scroller?.scrollTo(C):this.wrapperViewChild&&this.wrapperViewChild.nativeElement&&(this.wrapperViewChild.nativeElement.scrollTo?this.wrapperViewChild.nativeElement.scrollTo(C):(this.wrapperViewChild.nativeElement.scrollLeft=C.left,this.wrapperViewChild.nativeElement.scrollTop=C.top))}findFilteredNodes(C,A){if(C){let e=!1;if(C.children){let t=[...C.children];C.children=[];for(let i of t){let o={...i};this.isFilterMatched(o,A)&&(e=!0,C.children.push(o))}}if(e)return C.expanded=!0,!0}}isFilterMatched(C,A){let{searchFields:e,filterText:t,isStrictMode:i}=A,o=!1;for(let r of e)y.gb.removeAccents(String(y.gb.resolveFieldData(C,r))).toLocaleLowerCase(this.filterLocale).indexOf(t)>-1&&(o=!0);return(!o||i&&!this.isNodeLeaf(C))&&(o=this.findFilteredNodes(C,{searchFields:e,filterText:t,isStrictMode:i})||o),o}getIndex(C,A){const e=C.getItemOptions;return e?e(A).index:A}getBlockableElement(){return this.el.nativeElement.children[0]}ngOnDestroy(){this.dragStartSubscription&&this.dragStartSubscription.unsubscribe(),this.dragStopSubscription&&this.dragStopSubscription.unsubscribe()}static \u0275fac=function(A){return new(A||I)(g.Y36(g.SBq),g.Y36(s.Y,8),g.Y36(s.b4),g.Y36(g.sBO))};static \u0275cmp=g.Xpm({type:I,selectors:[["p-tree"]],contentQueries:function(A,e,t){if(1&A&&g.Suo(t,s.jx,4),2&A){let i;g.iGM(i=g.CRH())&&(e.templates=i)}},viewQuery:function(A,e){if(1&A&&(g.Gf(iC,5),g.Gf(oC,5),g.Gf(lC,5)),2&A){let t;g.iGM(t=g.CRH())&&(e.filterViewChild=t.first),g.iGM(t=g.CRH())&&(e.scroller=t.first),g.iGM(t=g.CRH())&&(e.wrapperViewChild=t.first)}},hostAttrs:[1,"p-element"],inputs:{value:"value",selectionMode:"selectionMode",selection:"selection",style:"style",styleClass:"styleClass",contextMenu:"contextMenu",layout:"layout",draggableScope:"draggableScope",droppableScope:"droppableScope",draggableNodes:"draggableNodes",droppableNodes:"droppableNodes",metaKeySelection:"metaKeySelection",propagateSelectionUp:"propagateSelectionUp",propagateSelectionDown:"propagateSelectionDown",loading:"loading",loadingIcon:"loadingIcon",emptyMessage:"emptyMessage",ariaLabel:"ariaLabel",togglerAriaLabel:"togglerAriaLabel",ariaLabelledBy:"ariaLabelledBy",validateDrop:"validateDrop",filter:"filter",filterBy:"filterBy",filterMode:"filterMode",filterPlaceholder:"filterPlaceholder",filteredNodes:"filteredNodes",filterLocale:"filterLocale",scrollHeight:"scrollHeight",lazy:"lazy",virtualScroll:"virtualScroll",virtualScrollItemSize:"virtualScrollItemSize",virtualScrollOptions:"virtualScrollOptions",indentation:"indentation",_templateMap:"_templateMap",trackBy:"trackBy",virtualNodeHeight:"virtualNodeHeight"},outputs:{selectionChange:"selectionChange",onNodeSelect:"onNodeSelect",onNodeUnselect:"onNodeUnselect",onNodeExpand:"onNodeExpand",onNodeCollapse:"onNodeCollapse",onNodeContextMenuSelect:"onNodeContextMenuSelect",onNodeDrop:"onNodeDrop",onLazyLoad:"onLazyLoad",onScroll:"onScroll",onScrollIndexChange:"onScrollIndexChange",onFilter:"onFilter"},features:[g.TTD],decls:2,vars:2,consts:[[3,"ngClass","ngStyle","class","drop","dragover","dragenter","dragleave",4,"ngIf"],[3,"ngClass","ngStyle","class",4,"ngIf"],[3,"ngClass","ngStyle","drop","dragover","dragenter","dragleave"],["class","p-tree-loading-overlay p-component-overlay",4,"ngIf"],[4,"ngTemplateOutlet"],["class","p-tree-filter-container",4,"ngIf"],["styleClass","p-tree-wrapper",3,"items","tabindex","style","scrollHeight","itemSize","lazy","options","onScroll","onScrollIndexChange","onLazyLoad",4,"ngIf"],[4,"ngIf"],["class","p-tree-empty-message",4,"ngIf"],[1,"p-tree-loading-overlay","p-component-overlay"],[3,"class",4,"ngIf"],[3,"spin","styleClass",4,"ngIf"],["class","p-tree-loading-icon",4,"ngIf"],[3,"spin","styleClass"],[1,"p-tree-loading-icon"],[1,"p-tree-filter-container"],["type","text","autocomplete","off",1,"p-tree-filter","p-inputtext","p-component",3,"keydown.enter","input"],["filter",""],[3,"styleClass",4,"ngIf"],["class","p-tree-filter-icon",4,"ngIf"],[3,"styleClass"],[1,"p-tree-filter-icon"],["styleClass","p-tree-wrapper",3,"items","tabindex","scrollHeight","itemSize","lazy","options","onScroll","onScrollIndexChange","onLazyLoad"],["scroller",""],["pTemplate","content"],["class","p-tree-container","role","tree",3,"ngClass","style",4,"ngIf"],["role","tree",1,"p-tree-container",3,"ngClass"],[3,"level","rowNode","node","firstChild","lastChild","index","itemSize","indentation",4,"ngFor","ngForOf","ngForTrackBy"],[3,"level","rowNode","node","firstChild","lastChild","index","itemSize","indentation"],["treeNode",""],["pTemplate","loader"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"p-tree-wrapper"],["wrapper",""],["class","p-tree-container","role","tree",4,"ngIf"],["role","tree",1,"p-tree-container"],[3,"node","firstChild","lastChild","index","level",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","firstChild","lastChild","index","level"],[1,"p-tree-empty-message"],[4,"ngIf","ngIfElse"],["emptyFilter",""],[3,"ngClass","ngStyle"],["class","p-tree-loading-mask p-component-overlay",4,"ngIf"],[1,"p-tree-loading-mask","p-component-overlay"],[3,"node","root"]],template:function(A,e){1&A&&(g.YNc(0,EC,8,16,"div",0),g.YNc(1,gI,6,11,"div",1)),2&A&&(g.Q6J("ngIf",!e.horizontal),g.xp6(1),g.Q6J("ngIf",e.horizontal))},dependencies:function(){return[a.mk,a.sg,a.O5,a.tP,a.PC,s.jx,T.T,D.W,J.L,CI]},styles:[".p-tree-container{margin:0;padding:0;list-style-type:none;overflow:auto}.p-treenode-children{margin:0;padding:0;list-style-type:none}.p-tree-wrapper{overflow:auto}.p-treenode-selectable{cursor:pointer;-webkit-user-select:none;user-select:none}.p-tree-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex-shrink:0}.p-treenode-leaf>.p-treenode-content .p-tree-toggler{visibility:hidden}.p-treenode-content{display:flex;align-items:center}.p-tree-filter{width:100%}.p-tree-filter-container{position:relative;display:block;width:100%}.p-tree-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-tree-loading{position:relative;min-height:4rem}.p-tree .p-tree-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-tree-flex-scrollable{display:flex;flex:1;height:100%;flex-direction:column}.p-tree-flex-scrollable .p-tree-wrapper{flex:1}.p-tree .p-treenode-droppoint{height:4px;list-style-type:none}.p-tree .p-treenode-droppoint-active{border:0 none}.p-tree-horizontal{width:auto;padding-left:0;padding-right:0;overflow:auto}.p-tree.p-tree-horizontal table,.p-tree.p-tree-horizontal tr,.p-tree.p-tree-horizontal td{border-collapse:collapse;margin:0;padding:0;vertical-align:middle}.p-tree-horizontal .p-treenode-content{font-weight:400;padding:.4em 1em .4em .2em;display:flex;align-items:center}.p-tree-horizontal .p-treenode-parent .p-treenode-content{font-weight:400;white-space:nowrap}.p-tree.p-tree-horizontal .p-treenode{background:url(data:image/gif;base64,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) repeat-x scroll center center transparent;padding:.25rem 2.5rem}.p-tree.p-tree-horizontal .p-treenode.p-treenode-leaf,.p-tree.p-tree-horizontal .p-treenode.p-treenode-collapsed{padding-right:0}.p-tree.p-tree-horizontal .p-treenode-children{padding:0;margin:0}.p-tree.p-tree-horizontal .p-treenode-connector{width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-table{height:100%;width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-line{background:url(data:image/gif;base64,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) repeat-y scroll 0 0 transparent;width:1px}.p-tree.p-tree-horizontal table{height:0}.p-scroller .p-tree-container{overflow:visible}\n"],encapsulation:2})}return I})(),II=(()=>{class I{static \u0275fac=function(A){return new(A||I)};static \u0275mod=g.oAB({type:I});static \u0275inj=g.cJS({imports:[a.ez,s.m8,h.T,T.v,k.n,Z.v,P.X,m,D.W,J.L,x.p,s.m8,T.v]})}return I})();const AI=function(I,n){return{"custom-main-node":I,"custom-child-node":n}};function eI(I,n){if(1&I&&(g.TgZ(0,"span",17),g._uU(1),g.qZA()),2&I){const C=n.$implicit;g.Q6J("ngClass",g.WLB(2,AI,C.children,!C.children)),g.xp6(1),g.Oqu(C.label)}}function tI(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",23)(1,"div",24),g._uU(2),g.ALo(3,"translate"),g.qZA(),g.TgZ(4,"div",25)(5,"span",26),g.NdJ("click",function(){g.CHM(C);const e=g.oxw().index,t=g.oxw(2);return g.KtG(t.clearAll(e))}),g._uU(6),g.ALo(7,"translate"),g.qZA(),g.TgZ(8,"button",27),g.NdJ("click",function(){g.CHM(C);const e=g.oxw().$implicit,t=g.oxw(2);return g.KtG(t.onPanelToggle(e))}),g._UZ(9,"em",28),g.qZA()()()}if(2&I){const C=g.oxw().$implicit;g.xp6(2),g.hij(" ",g.lcZ(3,3,C.name)," "),g.xp6(4),g.Oqu(g.lcZ(7,5,"landingPageSearch.clear")),g.xp6(3),g.Q6J("ngClass",C.isCollapsed?"pi pi-chevron-down":"pi pi-chevron-up")}}function nI(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",29)(1,"input",30),g.NdJ("ngModelChange",function(e){g.CHM(C);const t=g.oxw(3);return g.KtG(t.onSearch(e))})("ngModelChange",function(e){g.CHM(C);const t=g.oxw(3);return g.KtG(t.searchString=e)}),g.ALo(2,"translate"),g.qZA(),g.TgZ(3,"span"),g._UZ(4,"em",31),g.qZA()()}if(2&I){const C=g.oxw(3);g.xp6(1),g.s9C("placeholder",g.lcZ(2,2,"landingPageSearch.searchFilter")),g.Q6J("ngModel",C.searchString)}}function iI(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",1)(1,"div",34)(2,"input",35),g.NdJ("ngModelChange",function(){const t=g.CHM(C).$implicit,i=g.oxw(2).index,o=g.oxw(2);return g.KtG(o.onChange(t,i))})("ngModelChange",function(e){const i=g.CHM(C).$implicit;return g.KtG(i.isChecked=e)}),g.qZA()(),g.TgZ(3,"div",36)(4,"label"),g._uU(5),g.qZA()()()}if(2&I){const C=n.$implicit;g.xp6(2),g.Q6J("ngModel",C.isChecked),g.xp6(3),g.Oqu(C.label)}}function oI(I,n){if(1&I&&(g.TgZ(0,"div",32),g.YNc(1,iI,6,2,"div",33),g.qZA()),2&I){const C=g.oxw().$implicit;g.xp6(1),g.Q6J("ngForOf",null==C?null:C.item)}}function lI(I,n){1&I&&(g.TgZ(0,"div",37)(1,"div",38),g._UZ(2,"input",39),g.qZA(),g.TgZ(3,"div",38),g._UZ(4,"input",40),g.qZA()())}function rI(I,n){if(1&I&&(g.TgZ(0,"div")(1,"p-panel",18),g.YNc(2,tI,10,7,"ng-template",19),g.YNc(3,nI,5,4,"div",20),g.YNc(4,oI,2,1,"div",21),g.YNc(5,lI,5,0,"div",22),g.qZA()()),2&I){const C=n.$implicit;g.xp6(1),g.Q6J("collapsed",C.isCollapsed)("toggleable",!0),g.xp6(2),g.Q6J("ngIf","brands"===C.type),g.xp6(1),g.Q6J("ngIf","price"!==C.type),g.xp6(1),g.Q6J("ngIf","price"===C.type)}}function aI(I,n){if(1&I){const C=g.EpF();g.TgZ(0,"div",10)(1,"div",11)(2,"p-tree",12),g.NdJ("selectionChange",function(e){g.CHM(C);const t=g.oxw();return g.KtG(t.selectedCategories=e)}),g.YNc(3,eI,2,5,"ng-template",13),g.qZA()(),g.TgZ(4,"div",14)(5,"div",15),g._uU(6),g.ALo(7,"translate"),g.qZA(),g.YNc(8,rI,6,5,"div",16),g.qZA()()}if(2&I){const C=g.oxw();g.xp6(2),g.Q6J("selection",C.selectedCategories)("value",C.categories),g.xp6(4),g.hij(" ",g.lcZ(7,4,"landingPageSearch.refineBy")," "),g.xp6(2),g.Q6J("ngForOf",C.panelList)}}function sI(I,n){1&I&&(g.TgZ(0,"div",44),g._uU(1),g.ALo(2,"translate"),g.qZA()),2&I&&(g.xp6(1),g.hij(" ",g.lcZ(2,1,"landingPageSearch.shortBy")," "))}function cI(I,n){if(1&I&&(g.TgZ(0,"div",41)(1,"div",42),g._uU(2),g.ALo(3,"translate"),g.qZA(),g.YNc(4,sI,3,3,"div",43),g.qZA()),2&I){const C=g.oxw();g.xp6(2),g.AsE(" ",g.lcZ(3,3,"landingPageSearch.searchFor"),'"',C.word,'" '),g.xp6(2),g.Q6J("ngIf",C.isShowAdvanceSearch)}}function dI(I,n){if(1&I&&(g.TgZ(0,"a",47),g._UZ(1,"app-mtn-product-card",48),g.qZA()),2&I){const C=n.$implicit,A=g.oxw(2);g.xp6(1),g.Q6J("currency",A.currency)("product",C)}}function pI(I,n){if(1&I&&(g.TgZ(0,"div",45),g.YNc(1,dI,2,2,"a",46),g.qZA()),2&I){const C=g.oxw();g.xp6(1),g.Q6J("ngForOf",C.products)}}function _I(I,n){if(1&I&&(g.TgZ(0,"div",49)(1,"div",50),g._UZ(2,"img",51),g.qZA(),g.TgZ(3,"div",50)(4,"span",52),g._uU(5),g.ALo(6,"translate"),g.qZA()(),g.TgZ(7,"div",50)(8,"span",53),g._uU(9),g.ALo(10,"translate"),g.qZA()(),g.TgZ(11,"div",50)(12,"span",53),g._uU(13),g.ALo(14,"translate"),g.qZA()(),g.TgZ(15,"div",50)(16,"span",53),g._uU(17),g.ALo(18,"translate"),g.qZA()(),g.TgZ(19,"div",54)(20,"div",55),g._uU(21),g.ALo(22,"translate"),g.qZA()()()),2&I){const C=g.oxw();g.xp6(5),g.AsE(" ",g.lcZ(6,6,"cart.emptyCart.noResultFound"),'"',C.word,'". '),g.xp6(4),g.hij(" ",g.lcZ(10,8,"cart.emptyCart.checkSpelling")," "),g.xp6(4),g.hij(" ",g.lcZ(14,10,"cart.emptyCart.shortWords")," "),g.xp6(4),g.hij(" ",g.lcZ(18,12,"cart.emptyCart.searchGeneralTerms")," "),g.xp6(4),g.hij(" ",g.lcZ(22,14,"cart.emptyCart.goToHome")," ")}}const hI=[{path:"",component:(()=>{class I{activatedRoute;productService;store;reviewsService;messageService;translate;ref;router;loaderService;permissionService;$gaService;platformId;categoryId;searchString;panelList=[];allPanelList=[];categories=[{label:"Electronic",children:[{label:"Television & Video",children:[{label:"Analog-to-Digital (DTV) Converters"},{label:"AV Receivers & Amplifiers"},{label:"Blu-ray Players & Recorders"},{label:"Cable Receiver Boxes"}]},{label:"Accessories & Supplies"},{label:"Cameras"},{label:"GPS & Navigation"}]}];selectedCategories;topNumber;category;categoryName;items=[];breadItems=[];home={icon:"pi pi-home",routerLink:"/"};currency={};baseUrl=S.N.apiEndPoint+"/";emptyMsg="Your Category Is Empty";subPath;subId;catIds="";catPaths="";word;products=[];pageSize=50;currentPageSize=50;triggerProductsCall=!1;showProductSpinner=!1;loadDataType="";currentPageNumber=1;total=0;reviews;rawCategories=[];newCategory=null;isShowAdvanceSearch=!1;shouldCallNextProducts=!0;isGoogleAnalytics=!1;userDetails;tagName=M.Ir;_BaseURL=S.N.apiEndPoint;onScroll(C){if((0,a.NF)(this.platformId)){const A=window.scrollY||document.documentElement.scrollTop,e=document.documentElement.scrollHeight;A+window.innerHeight>=e&&this.loadScrollData()}}loadScrollData(){this.triggerProductsCall||this.total>=this.pageSize&&this.shouldCallNextProducts&&this.loadPaginatedProducts()}constructor(C,A,e,t,i,o,r,d,b,mI,xI,vI){this.activatedRoute=C,this.productService=A,this.store=e,this.reviewsService=t,this.messageService=i,this.translate=o,this.ref=r,this.router=d,this.loaderService=b,this.permissionService=mI,this.$gaService=xI,this.platformId=vI,this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics")}onPanelToggle(C){this.panelList.forEach(A=>{A.id===C.id&&(A.isCollapsed=!A.isCollapsed)})}ngOnInit(){this.panelList=[{id:0,type:"brands",isCollapsed:!1,name:"landingPageSearch.brands",item:[{label:"LG",isChecked:!1,id:0},{label:"Anker",isChecked:!1,id:1},{label:"Oppo",isChecked:!1,id:2},{label:"Realme",isChecked:!1,id:3},{label:"Apple",isChecked:!1,id:4},{label:"Nokia",isChecked:!1,id:5},{label:"Samsung",isChecked:!1,id:6}]},{id:1,type:"colors",isCollapsed:!1,name:"landingPageSearch.colors",item:[{label:"White",isChecked:!1,id:0},{label:"Black",isChecked:!1,id:1},{label:"Blue",isChecked:!1,id:2},{label:"Red",isChecked:!1,id:3},{label:"Green",isChecked:!1,id:4},{label:"Grey",isChecked:!1,id:5},{label:"Yellow",isChecked:!1,id:6}]},{id:2,type:"price",isCollapsed:!1,name:"landingPageSearch.price",item:[]}],this.showProductSpinner=!0,this.allPanelList=JSON.parse(JSON.stringify(this.panelList)),this.activatedRoute.queryParams.subscribe(C=>{C.q?(this.word=C.q,this.userDetails=this.store.get("profile"),this.loaderService.show(),this.productService.FilterWithProductName(C.q,this.currentPageNumber,this.pageSize).subscribe({next:A=>{this.products=[],this.total=A.data?.products?.records.length,this.triggerAnalytics(),A?.data?.products&&(this.showProductSpinner=!1,A.data?.products?.records.forEach(e=>{this.addProductFirstTime(e)}),this.loaderService.hide())},error:A=>{this.handleError(A.message),this.loaderService.hide()},complete:()=>{this.loaderService.hide()}})):this.router.navigate(["/"])})}triggerAnalytics(){this.isGoogleAnalytics&&(this.$gaService.pageView("/search","Search query: "+this.word),this.$gaService.event(`search_${this.word}`,this.word,"SEARCH",1,!0,{search_term:this.word,total_items:this.total,user_ID:this.userDetails?this.userDetails.mobileNumber:"Un_Authenticated"}))}loadPaginatedProducts(){this.triggerProductsCall=!0,this.showProductSpinner=!0,this.currentPageSize+=this.pageSize,this.currentPageNumber+=1,this.ref.detectChanges(),this.loaderService.show(),this.productService.FilterWithProductName(this.word,this.currentPageNumber,this.pageSize).subscribe({next:C=>{C?.data?.products&&(0==C.data?.products?.records.length&&(this.shouldCallNextProducts=!1),this.total=C.data?.products?.records.length,C.data?.products?.records.forEach(A=>{this.addProduct(A)}),this.loaderService.hide(),this.triggerProductsCall=!C.data.products.records.length,this.showProductSpinner=!1,this.ref.markForCheck())},error:C=>{this.handleError(C.message),this.loaderService.hide()},complete:()=>{this.loaderService.hide()}})}handleError(C){this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:C})}addProduct(C){let A,e=C?.specsProducts?.find(o=>o.isDefault);if(e)A=e;else{let o=C?.specsProducts?.find(r=>!r.soldOut);A=o||C?.specsProducts[0]}let t=[];A?.productFeaturesList&&(t=A?.productFeaturesList[0]?.featureList);let i={productId:C?.id,productName:C?.name,isLiked:C?.isLiked,priceValue:A?.price,salePriceValue:A?.salePrice,currencyCode:C?.currencyCode,masterImageUrl:A?.thumbnailImage??A?.masterImageUrl,soldOut:A?.soldOut,rate:A?.rate?A?.rate:0,count:0,salePercent:A?.salePrice?100-A?.salePrice/A?.price*100:0,shopId:C.shopId,channelId:C?.channelId,isHot:t?.includes(1),isNew:t?.includes(2),isBest:t?.includes(3),quantity:A?.quantity,proSchedulingId:A?.proSchedulingId,stockPerSKU:A.stockPerSKU,stockStatus:A.stockStatus,sku:A?.sku,skuAutoGenerated:A.skuAutoGenerated,badgesList:C?.badgesList};this.products.push(i)}addProductFirstTime(C){let A,e=C?.specsProducts?.find(o=>o.isDefault);if(e)A=e;else{let o=C?.specsProducts?.find(r=>!r.soldOut);A=o||C?.specsProducts[0]}let t=[];A?.productFeaturesList&&(t=A?.productFeaturesList[0]?.featureList);let i={productId:C?.id,productName:C?.name,isLiked:C?.isLiked,priceValue:A?.price,priceId:A?.priceId,salePriceValue:A?.salePrice,currencyCode:C?.currencyCode,masterImageUrl:A?.thumbnailImage??A?.masterImageUrl,soldOut:A?.soldOut,rate:A?.rate?A?.rate:0,count:0,salePercent:A?.salePrice?100-A?.salePrice/A?.price*100:0,shopId:C.shopId,specProductId:A.specProductId,channelId:C.channelId??"1",isHot:t?.includes(1),isNew:t?.includes(2),isBest:t?.includes(3),quantity:A?.quantity,stockStatus:A.stockStatus,proSchedulingId:A?.proSchedulingId,badgesList:C?.badgesList};i.salePriceValue&&(i.salePercent=100-i.salePriceValue/i.priceValue*100),this.products.push(i)}onSearch(C){this.panelList[0].item=this.allPanelList[0].item,C&&(this.panelList[0].item=this.panelList[0].item.filter(A=>A.label.toLowerCase().includes(C.toLowerCase())))}clearAll(C){this.panelList[C].item.forEach(A=>{A.isChecked=!1})}onChange(C,A){this.panelList[A].item.forEach(e=>{e.id===C.id&&(e.isChecked=!e.isChecked)}),this.allPanelList[A].item.forEach(e=>{e.id===C.id&&(e.isChecked=!e.isChecked)})}static \u0275fac=function(A){return new(A||I)(g.Y36(u.gz),g.Y36(p.M5),g.Y36(p.d6),g.Y36(p.Y0),g.Y36(s.ez),g.Y36(N.sK),g.Y36(g.sBO),g.Y36(u.F0),g.Y36(p.D1),g.Y36(p.$A),g.Y36(M.$r),g.Y36(g.Lbi))};static \u0275cmp=g.Xpm({type:I,selectors:[["app-category-products"]],hostBindings:function(A,e){1&A&&g.NdJ("scroll",function(i){return e.onScroll(i)},!1,g.Jf7)},inputs:{products:"products"},decls:10,vars:4,consts:[[1,"category-products-page"],[1,"row"],["class","col-md-2 filter-container",4,"ngIf"],[1,"col-md-12","mx-4"],[1,"content-container","mt-5"],["class"," d-flex justify-content-between search-short",4,"ngIf"],[1,""],["class","my-3 flex flex-row flex-wrap",4,"ngIf"],[1,"empty-container"],["class","productEmpty",4,"ngIf"],[1,"col-md-2","filter-container"],[1,"d-flex-row"],["selectionMode","single",1,"w-full","md:w-30rem","custom-tree",3,"selection","value","selectionChange"],["pTemplate","default"],[1,"left-filter","mt-3"],[1,"left-filter-heading"],[4,"ngFor","ngForOf"],[3,"ngClass"],[3,"collapsed","toggleable"],["pTemplate","header"],["class","filter-search-left p-inputgroup mt-1",4,"ngIf"],["class","border-filter",4,"ngIf"],["class","d-flex justify-content-between mt-3",4,"ngIf"],[1,"d-flex","justify-content-between","header-container"],[1,"d-inline-flex","brands-filter"],[1,"d-inline-flex"],[1,"short-by",3,"click"],["pButton","",1,"p-panel-header-icon","p-link",3,"click"],[2,"cursor","pointer",3,"ngClass"],[1,"filter-search-left","p-inputgroup","mt-1"],["autocapitalize","off","autocomplete","new-password","autocorrect","off","pInputText","","spellcheck","false","type","text",1,"search-input",3,"ngModel","placeholder","ngModelChange"],[1,"pi","pi-search"],[1,"border-filter"],["class","row",4,"ngFor","ngForOf"],[1,"col-md-1","col-1"],["required","","type","checkbox",1,"custom-search-checkbox",3,"ngModel","ngModelChange"],[1,"col-md-10","col-10","product-def-item-name"],[1,"d-flex","justify-content-between","mt-3"],[1,"d-inline-flex","date-input-container",2,"width","49%"],["placeholder","From","type","text",1,"p-inputtext","p-component","p-element","product-input","date-input"],["placeholder","To","type","text",1,"p-inputtext","date-input-containerp-component","p-element","product-input","date-input"],[1,"d-flex","justify-content-between","search-short"],[1,"search-for"],["class","short-by",4,"ngIf"],[1,"short-by"],[1,"my-3","flex","flex-row","flex-wrap"],["class","slide","class","mt-2 mx-2 mb-5",4,"ngFor","ngForOf"],[1,"mt-2","mx-2","mb-5"],[2,"width","100%",3,"currency","product"],[1,"productEmpty"],[1,"d-flex","justify-content-center"],["alt","No Image","src","assets/images/search-empty-icon.svg"],[1,"empty-result-label"],[1,"instructions"],[1,"d-flex","justify-content-center","mt-3"],["routerLink","/",1,"homepage-btn","cursor-pointer"]],template:function(A,e){1&A&&(g.TgZ(0,"section",0)(1,"div",1),g.YNc(2,aI,9,6,"div",2),g.TgZ(3,"div",3)(4,"div",4),g.YNc(5,cI,5,5,"div",5),g.TgZ(6,"div",6),g.YNc(7,pI,2,1,"div",7),g.TgZ(8,"div",8),g.YNc(9,_I,23,16,"div",9),g.qZA()()()()()()),2&A&&(g.xp6(2),g.Q6J("ngIf",e.isShowAdvanceSearch),g.xp6(3),g.Q6J("ngIf",e.products&&e.products.length>0),g.xp6(2),g.Q6J("ngIf",e.products&&e.products.length>0),g.xp6(2),g.Q6J("ngIf",e.products&&0===e.products.length&&!e.showProductSpinner))},dependencies:[a.mk,a.sg,a.O5,E.Y,s.jx,_.Fj,_.Wl,_.JJ,_.Zs,_.On,u.rH,pg,K,N.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}a[_ngcontent-%COMP%]{text-decoration:none;width:18%;flex:0 0 18%}.showProducts[_ngcontent-%COMP%]{max-width:1000px}@media screen and (min-width: 769px){.content-container.mt-5[_ngcontent-%COMP%]{padding-left:0rem!important;padding-right:0rem!important;margin-top:4rem!important}}@media screen and (max-width: 768px){.category-products-page[_ngcontent-%COMP%], .breadcrumb[_ngcontent-%COMP%]{margin-top:0!important}.col-12.md\\:col-6.flex.justify-content-center.md\\:justify-content-start[_ngcontent-%COMP%]{justify-content:left!important}}@media screen and (max-width: 768px) and (min-width: 325px){.category-products-page[_ngcontent-%COMP%], .breadcrumb[_ngcontent-%COMP%]{margin-top:0!important}}.productEmpty[_ngcontent-%COMP%]{font-size:25px;font-weight:500;font-family:var(--regular-font)!important;text-align:center}.search-for[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:var(--gray-900, #191C1F);font-family:var(--medium-font)!important}.search-short[_ngcontent-%COMP%]{width:86%}.short-by[_ngcontent-%COMP%]{color:var(--bottom_Strock, #A3A3A3);font-size:14px;font-weight:500;font-family:var(--regular-font)!important;text-decoration-line:underline;cursor:pointer}.left-filter[_ngcontent-%COMP%]{padding:16px;margin-top:2rem!important;margin-bottom:35%}.left-filter-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#2c2738;font-family:var(--medium-font)!important}.brands-filter[_ngcontent-%COMP%]{font-size:12px;font-weight:700;font-family:var(--medium-font)!important;color:#2c2738}.brand-dropdown[_ngcontent-%COMP%]{color:#7a7e83;font-size:12px;font-weight:400;font-family:var(--medium-font)!important}.search-input[_ngcontent-%COMP%]{width:100%;color:var(--gray-500, #77878F)!important;font-size:14px;font-weight:400;font-family:var(--medium-font)!important;background-color:var(--gray-00, #FFF)!important}.filter-search-left[_ngcontent-%COMP%]{border-radius:2px;border:1px solid var(--gray-100, #E4E7E9);background:var(--gray-00, #FFF);padding:8px 16px;height:40px}.border-filter[_ngcontent-%COMP%]{border-bottom:2px solid #E9EBEE}.product-def-item-name[_ngcontent-%COMP%]{color:#2c2738;font-family:var(--medium-font)!important;font-size:12px;font-style:normal;font-weight:400;line-height:normal}.custom-search-checkbox[_ngcontent-%COMP%]{width:18px;height:18px}.date-input[_ngcontent-%COMP%], .header-container[_ngcontent-%COMP%]{width:100%}.date-input-container[_ngcontent-%COMP%]{border-radius:2px;border:1px solid #d5d3d3;background:var(--gray-00, #FFF)}.filter-container[_ngcontent-%COMP%]{background:var(--gray-00, #FFF)!important}.empty-result-label[_ngcontent-%COMP%]{color:var(--gray-900, #191C1F);font-family:var(--medium-font)!important;font-size:20px;font-style:normal;font-weight:700;line-height:32px}.instructions[_ngcontent-%COMP%]{color:var(--gray-900, #191C1F);text-align:center;font-family:var(--regular-font);font-size:16px;font-style:normal;font-weight:400;line-height:32px}.homepage-btn[_ngcontent-%COMP%]{border-radius:8px;font-family:var(--regular-font);position:relative;text-align:center;z-index:3;color:var(--gray-00, var(--colors-fff, #FFF));font-size:14px;font-style:normal;font-weight:700;line-height:100%;letter-spacing:.168px;text-transform:uppercase;background-color:var(--header_bgcolor);padding:13px 24px;width:auto;height:43px}.custom-tree[_ngcontent-%COMP%]   .custom-main-node[_ngcontent-%COMP%]{font-weight:700!important}.custom-tree[_ngcontent-%COMP%]   .custom-child-node[_ngcontent-%COMP%]{color:gray!important}.empty-container[_ngcontent-%COMP%]{padding:100px}@media only screen and (max-width: 767px){.empty-container[_ngcontent-%COMP%]{margin-top:250px!important;padding:0!important;margin-bottom:30px!important}.content-container[_ngcontent-%COMP%]{padding-left:0rem!important;margin-top:233px!important}a[_ngcontent-%COMP%]{text-decoration:none;width:45%;flex:0 0 45%}}@media only screen and (min-width: 768px) and (max-width: 1200px){a[_ngcontent-%COMP%]{width:25%;flex:0 0 25%}}"]})}return I})()}];var fI=l(6574);let uI=(()=>{class I{static \u0275fac=function(A){return new(A||I)};static \u0275mod=g.oAB({type:I});static \u0275inj=g.cJS({imports:[a.ez,fI.p,u.Bz.forChild(hI),h.T,N.aw,_g,II]})}return I})()}}]);