{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/button\";\nexport class PaymentErrorDialogComponent {\n  dialogService;\n  previousref;\n  config;\n  router;\n  activatedRoute;\n  constructor(dialogService, previousref, config, router, activatedRoute) {\n    this.dialogService = dialogService;\n    this.previousref = previousref;\n    this.config = config;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n  }\n  ngOnInit() {\n    /**/\n  }\n  viewOrders() {\n    this.previousref.destroy();\n    this.router.navigate(['success'], {\n      relativeTo: this.activatedRoute\n    });\n  }\n  static ɵfac = function PaymentErrorDialogComponent_Factory(t) {\n    return new (t || PaymentErrorDialogComponent)(i0.ɵɵdirectiveInject(i1.DialogService), i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PaymentErrorDialogComponent,\n    selectors: [[\"app-payment-error-dialog\"]],\n    decls: 12,\n    vars: 0,\n    consts: [[1, \"payment-error-dialog\"], [1, \"border-none\", \"shadow-4\", \"h-3rem\"], [1, \"padding-21\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"100%\", \"min-height\", \"60vh\"], [1, \"border-circle\", \"w-4rem\", \"h-4rem\", \"m-2\", \"bg-red\", \"font-bold\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-times\", \"text-white\", 2, \"font-size\", \"1rem\"], [1, \"text-900\", \"font-size-24\", \"bold-font\", \"mb-0\"], [1, \"text-400\", \"font-size-15\", \"text-center\", \"width-80\", \"light-font\", \"mt-0\"], [1, \"flex-column\", \"flex\", \"justify-content-end\", \"align-content-end\"], [\"label\", \"Try again\", \"pButton\", \"\", \"type\", \"button\", 1, \"col-12\", \"my-2\", \"width-100\", \"second-btn\", 3, \"click\"]],\n    template: function PaymentErrorDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelement(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"a\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"p\", 6);\n        i0.ɵɵtext(7, \" Payment failed \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 7);\n        i0.ɵɵtext(9, \" Your payment was not successfully processed. Please try again \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function PaymentErrorDialogComponent_Template_button_click_11_listener() {\n          return ctx.viewOrders();\n        });\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    dependencies: [i3.ButtonDirective],\n    styles: [\".p-dialog .p-dialog-header {\\n  display: none;\\n}\\n  .p-dialog .p-dialog-content {\\n  padding: 0px;\\n}\\n  .bg-red {\\n  background-color: #FF0049 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY2hlY2tvdXQvY29tcG9uZW50cy9wYXltZW50LWVycm9yLWRpYWxvZy9wYXltZW50LWVycm9yLWRpYWxvZy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDRTtFQUNFLGFBQUE7QUFBSjtBQUdFO0VBQ0UsWUFBQTtBQURKO0FBR0U7RUFDRSxvQ0FBQTtBQURKIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIHtcclxuICAucC1kaWFsb2cgLnAtZGlhbG9nLWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBub25lO1xyXG4gIH1cclxuXHJcbiAgLnAtZGlhbG9nIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgIHBhZGRpbmc6IDBweDtcclxuICB9XHJcbiAgLmJnLXJlZHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNGRjAwNDkgIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["PaymentErrorDialogComponent", "dialogService", "previousref", "config", "router", "activatedRoute", "constructor", "ngOnInit", "viewOrders", "destroy", "navigate", "relativeTo", "i0", "ɵɵdirectiveInject", "i1", "DialogService", "DynamicDialogRef", "DynamicDialogConfig", "i2", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "PaymentErrorDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "PaymentErrorDialogComponent_Template_button_click_11_listener"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\components\\payment-error-dialog\\payment-error-dialog.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\components\\payment-error-dialog\\payment-error-dialog.component.html"], "sourcesContent": ["import {Component, OnInit} from '@angular/core';\r\nimport {DialogService, DynamicDialogConfig, DynamicDialogRef} from \"primeng/dynamicdialog\";\r\nimport {ActivatedRoute, Router} from \"@angular/router\";\r\n\r\n@Component({\r\n  selector: 'app-payment-error-dialog',\r\n  templateUrl: './payment-error-dialog.component.html',\r\n  styleUrls: ['./payment-error-dialog.component.scss']\r\n})\r\nexport class PaymentErrorDialogComponent implements OnInit {\r\n\r\n  constructor(public dialogService: DialogService,\r\n              public previousref: DynamicDialogRef,\r\n              public config: DynamicDialogConfig,\r\n              private router: Router,\r\n              private activatedRoute: ActivatedRoute) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n  viewOrders() {\r\n    this.previousref.destroy();\r\n    this.router.navigate(['success'], {relativeTo: this.activatedRoute});\r\n  }\r\n}\r\n", "<section class=\"payment-error-dialog\">\r\n  <div class=\"border-none shadow-4 h-3rem\">\r\n  </div>\r\n  <div class=\"padding-21\">\r\n    <div class=\"flex flex-column align-items-center justify-content-center\" style=\"height: 100%; min-height: 60vh\">\r\n      <div class=\"border-circle w-4rem h-4rem m-2 bg-red font-bold flex align-items-center justify-content-center\">\r\n        <a class=\"pi pi-times text-white\" style=\"font-size: 1rem\"></a>\r\n      </div>\r\n      <p class=\"text-900 font-size-24 bold-font mb-0\">\r\n        Payment failed\r\n      </p>\r\n      <p class=\"text-400 font-size-15 text-center width-80 light-font mt-0\">\r\n        Your payment was not successfully processed. Please try again\r\n      </p>\r\n    </div>\r\n    <div class=\"flex-column flex justify-content-end align-content-end\">\r\n      <button label=\"Try again\" class=\"col-12 my-2 width-100  second-btn\"\r\n              (click)=\"viewOrders()\"\r\n              pButton\r\n              type=\"button\">\r\n      </button>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": ";;;;AASA,OAAM,MAAOA,2BAA2B;EAEnBC,aAAA;EACAC,WAAA;EACAC,MAAA;EACCC,MAAA;EACAC,cAAA;EAJpBC,YAAmBL,aAA4B,EAC5BC,WAA6B,EAC7BC,MAA2B,EAC1BC,MAAc,EACdC,cAA8B;IAJ/B,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;EAClC;EAEAE,QAAQA,CAAA;IACN;EAAA;EAGFC,UAAUA,CAAA;IACR,IAAI,CAACN,WAAW,CAACO,OAAO,EAAE;IAC1B,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAACC,UAAU,EAAE,IAAI,CAACN;IAAc,CAAC,CAAC;EACtE;;qBAhBWL,2BAA2B,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAE,gBAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAG,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAE,cAAA;EAAA;;UAA3BpB,2BAA2B;IAAAqB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxCf,EAAA,CAAAiB,cAAA,iBAAsC;QACpCjB,EAAA,CAAAkB,SAAA,aACM;QACNlB,EAAA,CAAAiB,cAAA,aAAwB;QAGlBjB,EAAA,CAAAkB,SAAA,WAA8D;QAChElB,EAAA,CAAAmB,YAAA,EAAM;QACNnB,EAAA,CAAAiB,cAAA,WAAgD;QAC9CjB,EAAA,CAAAoB,MAAA,uBACF;QAAApB,EAAA,CAAAmB,YAAA,EAAI;QACJnB,EAAA,CAAAiB,cAAA,WAAsE;QACpEjB,EAAA,CAAAoB,MAAA,sEACF;QAAApB,EAAA,CAAAmB,YAAA,EAAI;QAENnB,EAAA,CAAAiB,cAAA,cAAoE;QAE1DjB,EAAA,CAAAqB,UAAA,mBAAAC,8DAAA;UAAA,OAASN,GAAA,CAAApB,UAAA,EAAY;QAAA,EAAC;QAG9BI,EAAA,CAAAmB,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}