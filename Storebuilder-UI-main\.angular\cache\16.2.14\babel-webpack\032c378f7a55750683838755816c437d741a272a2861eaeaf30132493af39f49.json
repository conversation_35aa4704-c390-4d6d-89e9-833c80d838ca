{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { environment } from \"@environments/environment\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { Carousel } from 'primeng/carousel';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-google-analytics\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/carousel\";\nconst _c0 = function (a0) {\n  return {\n    \"background-image\": a0\n  };\n};\nfunction MtnMainSliderComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function MtnMainSliderComponent_ng_container_1_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.routeToCTA(ctx_r3.sliders[0], (ctx_r3.sliders[0] == null ? null : ctx_r3.sliders[0].CTALink) || \"\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(1, _c0, \"url(\" + ctx_r0.getBannerImages((ctx_r0.sliders[0] == null ? null : ctx_r0.sliders[0].imageUrl) || \"\") + \")\"));\n  }\n}\nfunction MtnMainSliderComponent_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function MtnMainSliderComponent_ng_template_2_ng_template_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const mainSlider_r6 = restoredCtx.$implicit;\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.routeToCTA(mainSlider_r6, mainSlider_r6.CTALink));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mainSlider_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(1, _c0, \"url(\" + ctx_r5.getBannerImages(mainSlider_r6.imageUrl) + \")\"));\n  }\n}\nfunction MtnMainSliderComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-carousel\", 4);\n    i0.ɵɵlistener(\"onPage\", function MtnMainSliderComponent_ng_template_2_Template_p_carousel_onPage_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.changeSliderDot($event));\n    });\n    i0.ɵɵtemplate(1, MtnMainSliderComponent_ng_template_2_ng_template_1_Template, 1, 3, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r2.sliders)(\"circular\", true)(\"autoplayInterval\", 3000)(\"showIndicators\", false)(\"showNavigators\", true);\n  }\n}\nexport let MtnMainSliderComponent = /*#__PURE__*/(() => {\n  class MtnMainSliderComponent {\n    router;\n    platformId;\n    $gaService;\n    sliderOptions;\n    innerWidth;\n    mobileScreen = false;\n    sliders = [];\n    sliderDots = [];\n    constructor(router, platformId, $gaService) {\n      this.router = router;\n      this.platformId = platformId;\n      this.$gaService = $gaService;\n      Carousel.prototype.onTouchMove = () => {};\n      this.sliderOptions = {\n        loop: true,\n        autoplay: true,\n        center: true,\n        dots: true,\n        autoplayTimeout: 3000,\n        autoHeight: false,\n        autoWidth: true,\n        lazyLoad: true,\n        autoplayHoverPause: true,\n        navText: ['<em class=\"pi pi-angle-left white-color font-size-30\"></em>', '<em class=\"pi pi-angle-right white-color font-size-30\"></em>'],\n        responsive: {\n          0: {\n            items: 1,\n            dots: true,\n            nav: false\n          },\n          600: {\n            items: 1,\n            dots: true,\n            nav: false\n          },\n          1000: {\n            items: 1\n          }\n        }\n      };\n    }\n    ngOnInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.innerWidth = window.innerWidth;\n        if (this.innerWidth < 768) {\n          this.mobileScreen = true;\n        } else {\n          this.mobileScreen = false;\n        }\n      }\n      this.sliders.map((x, index) => {\n        this.sliderDots.push({\n          index,\n          selected: index === 0\n        });\n      });\n    }\n    routeToCTA(banner, url = '') {\n      if (!url) return;\n      if (isPlatformBrowser(this.platformId)) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_BANNERS, '', 'BANNERS_ON_HOMEPAGE', 1, true, {\n          bannerId: banner.promotionId || url.split('/').pop(),\n          redirectPage: url\n        });\n        let finalUrl = url.trim();\n        if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {\n          if (finalUrl.startsWith('www.')) {\n            finalUrl = 'https://' + finalUrl;\n          } else if (finalUrl.startsWith('/')) {\n            // internal route\n            finalUrl = window.location.origin + finalUrl;\n          } else {\n            finalUrl = 'https://' + finalUrl;\n          }\n        }\n        // ✅ If promotion\n        if (banner.promotionId) {\n          if (banner.CTALink) {\n            const cta = banner.CTALink.replace(/promotions\\//g, 'promotion/');\n            if (!cta.startsWith('http://') && !cta.startsWith('https://')) {\n              window.location.href = 'https://' + cta;\n            } else {\n              window.location.href = cta;\n            }\n          } else {\n            const tempurl = 'https://' + environment.marketPlaceHostName + '/promotion/' + banner.promotionId;\n            window.location.href = tempurl;\n          }\n        } else {\n          window.location.href = finalUrl;\n        }\n      }\n    }\n    getBannerImages(url) {\n      return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n    }\n    changeSliderDot(event) {\n      this.sliderDots.forEach(obj => {\n        obj.selected = obj.index == event.page;\n      });\n    }\n    static ɵfac = function MtnMainSliderComponent_Factory(t) {\n      return new (t || MtnMainSliderComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.GoogleAnalyticsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MtnMainSliderComponent,\n      selectors: [[\"app-mtn-main-slider\"]],\n      inputs: {\n        sliders: \"sliders\"\n      },\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"mtn-main-slider\"], [4, \"ngIf\", \"ngIfElse\"], [\"multiSlider\", \"\"], [1, \"banner-image\", \"single-image\", 3, \"ngStyle\", \"click\"], [3, \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"showNavigators\", \"onPage\"], [\"pTemplate\", \"item\"], [1, \"banner-image\", 3, \"ngStyle\", \"click\"]],\n      template: function MtnMainSliderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, MtnMainSliderComponent_ng_container_1_Template, 2, 3, \"ng-container\", 1);\n          i0.ɵɵtemplate(2, MtnMainSliderComponent_ng_template_2_Template, 2, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.sliders == null ? null : ctx.sliders.length) === 1)(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: [i3.PrimeTemplate, i4.NgIf, i4.NgStyle, i5.Carousel],\n      styles: [\".mtn-main-slider .owl-theme .owl-dots{position:relative!important}@media screen and (max-width: 768px){.slider-height[_ngcontent-%COMP%]{margin-top:0rem!important}.slider[_ngcontent-%COMP%]{margin-top:18px!important}.mbl-des[_ngcontent-%COMP%]{height:170px!important}}@media screen and (min-width: 769px){.slider-height[_ngcontent-%COMP%]{margin-top:8rem!important}}.animated[_ngcontent-%COMP%]{animation-duration:3s;animation-fill-mode:both}.owl-animated-out[_ngcontent-%COMP%]{z-index:1}.owl-animated-in[_ngcontent-%COMP%]{z-index:0}.fadeOut[_ngcontent-%COMP%]{animation-name:_ngcontent-%COMP%_fadeOut}@keyframes _ngcontent-%COMP%_fadeOut{0%{opacity:1}to{opacity:0}}div[_ngcontent-%COMP%], div[_ngcontent-%COMP%]   .p-element[_ngcontent-%COMP%]{height:inherit}div[_ngcontent-%COMP%]   .p-element[_ngcontent-%COMP%]   .p-carousel-items-content[_ngcontent-%COMP%]{height:inherit!important}.mtn-main-slider[_ngcontent-%COMP%]{width:1300px;border-radius:6px;overflow:hidden;box-shadow:0 4px 4px #00000040}@media screen and (max-width: 768px){.mtn-main-slider[_ngcontent-%COMP%]{width:90vw}}.mtn-main-slider[_ngcontent-%COMP%]   .banner-image[_ngcontent-%COMP%]{border-radius:6px}.single-image[_ngcontent-%COMP%]{height:330px}@media (max-width: 768px){.single-image[_ngcontent-%COMP%]{height:inherit}}\"]\n    });\n  }\n  return MtnMainSliderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}