@import '../../../../assets/scss/common.scss';

// Base styles
.category-products-page {
  min-height: 100vh;
  
  &.hidden-navbar {
    margin-top: 0;
  }
  
  &.mobile-layout {
    margin-top: 65px;
  }
}

.breadcrumb {
  margin-bottom: 20px;
}

.hiddenNavbarBreadcrum {
  margin-top: 20px;
}

.category-banner {
  margin-bottom: 30px;
  text-align: center;
  
  &__img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.font-size-22 {
  font-size: 22px;
}

.bold-font {
  font-weight: bold;
}

.font-bold {
  font: normal 500 18px/24px var(--regular-font);
  text-transform: uppercase;
}

.products-header {
  margin-bottom: 1rem;
}

.products-grid {
  display: flex;
  flex-flow: row wrap;
  margin: 5px 0 1rem;
}

.product-card-link {
  display: inline-block;
  text-decoration: none;
  color: inherit;
  margin: 0.5rem 0 16px;
  padding: 0 8px;
}

.spinner-product {
  margin: 40px 0;
  text-align: center;
}

// Utility classes
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mx-1 {
  margin: 0 0.25rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

// Mobile (≤768px)
@media (max-width: 768px) {
  .category-products-page {
    padding: 15px;
    
    &:not(.mobile-layout) {
      margin-top: 210px;
    }
  }
  .hidden-navbar {
    margin-top: 65px !important;
  }
  
  .breadcrumb,
  .hiddenNavbarBreadcrum {
    display: none;
  }
  
  .category-banner {
    &__img {
      border-radius: 4px;
    }
  }
  
  .font-size-22 {
    font: normal 500 18px/24px $font-main-regular;
    text-transform: uppercase;
  }
  
  .products-header {
    padding: 16px 15px 0;
    margin: 0;
    
    .category-title {
      padding-left: 0 !important;
    }
  }
  
  .products-grid {
    width: 100%;
    justify-content: left;
  }
  
  .product-card-link {
    flex: 0 0 50%;
    max-width: 50%;
    width: 50% !important;
    padding: 0 12px;
    margin: 0.5rem 0 16px;
  }
}

// Tablet & Desktop (≥769px)
@media (min-width: 769px) {
  .category-banner {
    margin-bottom: 24px;
  }
  
  .product-card-link {
    width: var(--product-card-width);
    margin: 0.5rem 0.25rem 16px;
    --product-card-width: 18%;
    
    &.isLayoutTemplate {
      --product-card-width: 11% !important;
    }
  }
}

// Large Desktop (≥1200px)
@media (min-width: 1200px) {
  .product-card-link {
    --product-card-width: 18%;
    
    &.isLayoutTemplate {
      --product-card-width: 11% !important;
    }
  }
}
