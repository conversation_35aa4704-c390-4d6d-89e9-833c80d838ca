{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { Subject } from 'rxjs';\nimport { PromoCodeComponent } from '../promo-code/promo-code.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-google-analytics\";\nimport * as i4 from \"@core/services/gtm.service\";\nimport * as i5 from \"@core/services/custom-GA.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@shared/modals/address-modal/address-modal.component\";\nimport * as i8 from \"@shared/components/age-restriction/age-restriction.component\";\nimport * as i9 from \"../payment-cart/payment-cart.component\";\nimport * as i10 from \"../delivery-method-cart/delivery-method-cart.component\";\nimport * as i11 from \"../order-summary-cart/order-summary-cart.component\";\nimport * as i12 from \"../promo-code/promo-code.component\";\nimport * as i13 from \"@ngx-translate/core\";\nfunction IndexComponent_section_1_app_age_restriction_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-age-restriction\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"restrictionMessage\", ctx_r4.productEligibilityMessage);\n  }\n}\nfunction IndexComponent_section_1_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"checkout.deliveryMethod.default\"), \" \");\n  }\n}\nfunction IndexComponent_section_1_app_promo_code_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-promo-code\", 27);\n    i0.ɵɵlistener(\"couponApplied\", function IndexComponent_section_1_app_promo_code_29_Template_app_promo_code_couponApplied_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onCouponApplied($event));\n    })(\"couponRemoved\", function IndexComponent_section_1_app_promo_code_29_Template_app_promo_code_couponRemoved_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onCouponRemoved());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"refreshSummary\", ctx_r6.refreshOrderSummary)(\"paymentMethodDetails\", ctx_r6.paymentMethod)(\"regionId\", ctx_r6.regionId)(\"deliveryOptionDetails\", ctx_r6.deliveryOptionDetails);\n  }\n}\nfunction IndexComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 5)(1, \"div\", 6)(2, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function IndexComponent_section_1_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onBack());\n    });\n    i0.ɵɵelement(3, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 9);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"div\", 11)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, IndexComponent_section_1_app_age_restriction_12_Template, 1, 1, \"app-age-restriction\", 12);\n    i0.ɵɵelementStart(13, \"div\", 13)(14, \"div\", 14)(15, \"div\")(16, \"div\", 15)(17, \"div\", 6)(18, \"div\", 16);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, IndexComponent_section_1_button_20_Template, 3, 3, \"button\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"img\", 18);\n    i0.ɵɵlistener(\"click\", function IndexComponent_section_1_Template_img_click_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onChangeAddress());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 19);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 19);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(27, \"div\")(28, \"app-delivery-method-cart\", 20);\n    i0.ɵɵlistener(\"onChangeDeliveryOption\", function IndexComponent_section_1_Template_app_delivery_method_cart_onChangeDeliveryOption_28_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.changeDeliveryOption($event));\n    })(\"onPaymentMethodselection\", function IndexComponent_section_1_Template_app_delivery_method_cart_onPaymentMethodselection_28_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.changePaymentOption($event));\n    })(\"onChangeRegionID\", function IndexComponent_section_1_Template_app_delivery_method_cart_onChangeRegionID_28_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onChangeRegionID($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, IndexComponent_section_1_app_promo_code_29_Template, 1, 4, \"app-promo-code\", 21);\n    i0.ɵɵelementStart(30, \"div\", 22);\n    i0.ɵɵelement(31, \"app-order-summary-cart\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"app-payment-cart\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 15, \"checkout.checkoutLabel\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 17, \"checkout.ShippingAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAgeEligible);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r0.selectedAddress.addressLabel ? ctx_r0.defaultAddress == null ? null : ctx_r0.defaultAddress.addressLabel : ctx_r0.selectedAddress.addressLabel, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedAddress.isDefault);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"state\", ctx_r0.selectedAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r0.selectedAddress.streetAddress ? ctx_r0.defaultAddress == null ? null : ctx_r0.defaultAddress.streetAddress : ctx_r0.selectedAddress.streetAddress, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", !ctx_r0.selectedAddress.receiverPhoneNumber ? ctx_r0.defaultAddress == null ? null : ctx_r0.defaultAddress.receiverPhoneNumber : ctx_r0.selectedAddress.receiverPhoneNumber, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.AllowCouponDiscount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"deliveryOptionDetails\", ctx_r0.deliveryOptionDetails)(\"refreshSummary\", ctx_r0.refreshOrderSummary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"deliveryOptionDetails\", ctx_r0.deliveryOptionDetails)(\"paymentMethodDetails\", ctx_r0.paymentMethodDetails ? ctx_r0.paymentMethodDetails : ctx_r0.defaultPayment.name)(\"refreshSummary\", ctx_r0.refreshOrderSummary)(\"cartItems\", ctx_r0.cartItems);\n  }\n}\nfunction IndexComponent_div_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"(\", ctx_r16.totalCount, \" \", i0.ɵɵpipeBind1(2, 2, \"checkout.item\"), \")\");\n  }\n}\nfunction IndexComponent_div_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"(\", ctx_r17.totalCount, \" \", i0.ɵɵpipeBind1(2, 2, \"checkout.items\"), \")\");\n  }\n}\nfunction IndexComponent_div_2_app_promo_code_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-promo-code\", 27);\n    i0.ɵɵlistener(\"couponApplied\", function IndexComponent_div_2_app_promo_code_10_Template_app_promo_code_couponApplied_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onCouponApplied($event));\n    })(\"couponRemoved\", function IndexComponent_div_2_app_promo_code_10_Template_app_promo_code_couponRemoved_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onCouponRemoved());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"refreshSummary\", ctx_r18.refreshOrderSummary)(\"paymentMethodDetails\", ctx_r18.paymentMethod)(\"regionId\", ctx_r18.regionId)(\"deliveryOptionDetails\", ctx_r18.deliveryOptionDetails);\n  }\n}\nfunction IndexComponent_div_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"app-age-restriction\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"restrictionMessage\", ctx_r19.productEligibilityMessage);\n  }\n}\nfunction IndexComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 5)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵtemplate(6, IndexComponent_div_2_span_6_Template, 3, 4, \"span\", 30);\n    i0.ɵɵtemplate(7, IndexComponent_div_2_span_7_Template, 3, 4, \"span\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\")(9, \"app-delivery-method-cart\", 20);\n    i0.ɵɵlistener(\"onChangeDeliveryOption\", function IndexComponent_div_2_Template_app_delivery_method_cart_onChangeDeliveryOption_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.changeDeliveryOption($event));\n    })(\"onPaymentMethodselection\", function IndexComponent_div_2_Template_app_delivery_method_cart_onPaymentMethodselection_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.changePaymentOption($event));\n    })(\"onChangeRegionID\", function IndexComponent_div_2_Template_app_delivery_method_cart_onChangeRegionID_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onChangeRegionID($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, IndexComponent_div_2_app_promo_code_10_Template, 1, 4, \"app-promo-code\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵelement(12, \"app-order-summary-cart\", 23);\n    i0.ɵɵtemplate(13, IndexComponent_div_2_div_13_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelement(14, \"app-payment-cart\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 11, \"checkout.checkoutLabel\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalCount && ctx_r1.totalCount === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.totalCount && ctx_r1.totalCount > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.AllowCouponDiscount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"deliveryOptionDetails\", ctx_r1.deliveryOptionDetails)(\"refreshSummary\", ctx_r1.refreshOrderSummary);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAgeEligible);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"deliveryOptionDetails\", ctx_r1.deliveryOptionDetails)(\"paymentMethodDetails\", ctx_r1.paymentMethodDetails ? ctx_r1.paymentMethodDetails : ctx_r1.defaultPayment.name)(\"refreshSummary\", ctx_r1.refreshOrderSummary)(\"cartItems\", ctx_r1.cartItems);\n  }\n}\nfunction IndexComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"section\", 36);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38)(5, \"div\", 39)(6, \"h2\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 41)(10, \"div\", 42)(11, \"div\", 43);\n    i0.ɵɵelement(12, \"app-delivery-method-cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 44);\n    i0.ɵɵelement(14, \"app-order-summary-cart\", 45)(15, \"app-payment-cart\", 46);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(16, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 5, \"checkout.checkoutLabel\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"deliveryOptionDetails\", ctx_r2.deliveryOptionDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"cartItems\", ctx_r2.cartItems)(\"deliveryOptionDetails\", ctx_r2.deliveryOptionDetails)(\"paymentMethodDetails\", ctx_r2.paymentMethodDetails ? ctx_r2.paymentMethodDetails : ctx_r2.defaultPayment.name);\n  }\n}\nfunction IndexComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-mtn-address-modal\", 47);\n    i0.ɵɵlistener(\"submit\", function IndexComponent_ng_container_4_Template_app_mtn_address_modal_submit_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.onSubmit($event));\n    })(\"addressSelected\", function IndexComponent_ng_container_4_Template_app_mtn_address_modal_addressSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.selectAddress($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"selectedId\", ctx_r3.addressService.chosenAddress == null ? null : ctx_r3.addressService.chosenAddress.id)(\"displayModal\", ctx_r3.displayModal);\n  }\n}\nexport let IndexComponent = /*#__PURE__*/(() => {\n  class IndexComponent {\n    addressService;\n    cartService;\n    permissionService;\n    router;\n    $gaService;\n    activeRoute;\n    $gtmService;\n    store;\n    _GACustomEvent;\n    deliveryOptionDetails;\n    totalCount;\n    isMobileTemplate = false;\n    displayModal = false;\n    dataList = [];\n    onChangeDeliveryOption = new EventEmitter();\n    address = [];\n    selectedDeliveryOption;\n    isLayoutTemplate = false;\n    isMobileLayout = false;\n    isGoogleAnalytics = false;\n    screenWidth = window.innerWidth;\n    AllowCouponDiscount = false;\n    cartItems = [];\n    defaultPayment = {\n      id: 31,\n      name: 'MoMo Wallet',\n      status: true,\n      default: true,\n      applyTo: 2,\n      isActive: true,\n      tenantId: 1,\n      isDeleted: false,\n      deliveryDateAfter: null,\n      createdAt: '2023-10-11T07:25:37.8823753',\n      updatedAt: '2024-06-24T07:41:57.9118903'\n    };\n    sessionId;\n    userDetails;\n    paymentMethodDetails = this.defaultPayment.name;\n    paymentMethod = this.defaultPayment;\n    refreshOrderSummary = new Subject();\n    isAgeEligible = false;\n    productEligibilityMessage;\n    regionId = null;\n    hasEmittedInitialShippingGA = false;\n    promoCodeComponent;\n    constructor(addressService, cartService, permissionService, router, $gaService, activeRoute, $gtmService, store, _GACustomEvent) {\n      this.addressService = addressService;\n      this.cartService = cartService;\n      this.permissionService = permissionService;\n      this.router = router;\n      this.$gaService = $gaService;\n      this.activeRoute = activeRoute;\n      this.$gtmService = $gtmService;\n      this.store = store;\n      this._GACustomEvent = _GACustomEvent;\n      this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n      this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n      this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n      this.screenWidth = window.innerWidth;\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      this.AllowCouponDiscount = this.permissionService.hasPermission('AllowCouponDiscount');\n    }\n    selectedAddress;\n    defaultAddress;\n    ngOnInit() {\n      this.selectedAddress = history.state;\n      this.$gtmService.pushPageView('checkout');\n      this.addressService.getAddress().subscribe({\n        next: res => {\n          res.data.records.map(el => {\n            if (el.isDefault) {\n              this.defaultAddress = el;\n            }\n          });\n        }\n      });\n      if (this.permissionService.hasPermission('Shipment-Fee')) {\n        this.getShipmentMethodByTenantId();\n      }\n      this.getAllCart();\n    }\n    changeDeliveryOption(event) {\n      this.selectedDeliveryOption = event;\n      this.deliveryOptionDetails = event;\n      const shippingName = event?.name || event?.shippingMethod || event?.title;\n      if (this.isGoogleAnalytics && shippingName && this.cartItems && this.cartItems.length) {\n        this._GACustomEvent.addShippingInfoEvent(this.cartItems, shippingName, 1, this.getCurrentCouponCode());\n      }\n    }\n    changePaymentOption(event) {\n      if (event.id !== this.paymentMethod.id) {\n        this._GACustomEvent.addPaymentInfoEvent(this.cartItems, event.name, 1, this.getCurrentCouponCode());\n      }\n      this.paymentMethodDetails = event.name;\n      this.paymentMethod = event;\n    }\n    getAllCart() {\n      let cartData = {\n        sessionId: localStorage.getItem('sessionId') ?? ''\n      };\n      let applyTo = localStorage.getItem('apply-to');\n      if (applyTo && applyTo != '') {\n        cartData['applyTo'] = applyTo;\n      }\n      if (cartData.sessionId) {\n        this.cartService.getCart(cartData).subscribe({\n          next: res => {\n            if (res.data?.records) {\n              this.cartItems = res.data.records[0]?.cartDetails;\n              this._GACustomEvent.addPaymentInfoEvent(this.cartItems, this.paymentMethod.name, 1, this.getCurrentCouponCode());\n              this.maybeEmitInitialShippingGA();\n              this.totalCount = res.data.records[0]?.cartDetails.length;\n              const eligibleItems = res.data.records[0]?.cartDetails.filter(item => item.isAgeEligible === true);\n              if (eligibleItems.length > 0) {\n                const maxEligibleItem = eligibleItems.reduce((maxItem, currentItem) => {\n                  return currentItem.productEligibilityAge > maxItem.productEligibilityAge ? currentItem : maxItem;\n                }, eligibleItems[0]);\n                this.isAgeEligible = true;\n                this.productEligibilityMessage = maxEligibleItem.productEligibilityMessage;\n              }\n            } else {\n              this.totalCount = 0;\n            }\n          }\n        });\n      } else {\n        this.totalCount = 0;\n      }\n    }\n    triggerGoogleAnaytics() {\n      if (this.isGoogleAnalytics) {\n        this.sessionId = localStorage.getItem('sessionId');\n        this.userDetails = this.store.get('profile');\n        this.$gaService.event(GaLocalActionEnum.click_on_change_address, 'checkout', 'CHANGE_ADDRESS', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"session_ID\": this.sessionId,\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId\n        });\n      }\n    }\n    onChangeAddress() {\n      this.triggerGoogleAnaytics();\n    }\n    changeOption() {\n      this.onChangeDeliveryOption.emit(this.selectedDeliveryOption);\n    }\n    selectAddress(event) {\n      this.displayModal = false;\n      this.addressService.chosenAddress = event;\n      this.addressService.setCustomAddress(event);\n    }\n    onSubmit(event) {\n      this.displayModal = false;\n    }\n    getShipmentMethodByTenantId() {\n      this.cartService.getShipmentMethodByTenantId().subscribe(res => {\n        if (res.success && res.data.length) {\n          if (res.data[0].applyTo === 1) {\n            this.getOwnShipmentOptions();\n          } else {\n            this.getReterviedShipmentOptions();\n          }\n        }\n      });\n    }\n    getReterviedShipmentOptions() {\n      const reqObj = {\n        pageSize: 5,\n        currentPage: 1,\n        ignorePagination: true\n      };\n      this.cartService.getReterviedShipmentOptions(reqObj).subscribe(res => {\n        if (res.success) {\n          this.dataList = res.data.records.filter(record => record.status);\n          this.dataList.forEach(item => {\n            if (item.default) {\n              this.selectedDeliveryOption = item;\n              this.deliveryOptionDetails = item;\n              this.maybeEmitInitialShippingGA();\n              this.onChangeDeliveryOption.emit(this.selectedDeliveryOption);\n            }\n          });\n        }\n      });\n    }\n    getOwnShipmentOptions() {\n      const reqObj = {\n        pageSize: 5,\n        currentPage: 1,\n        ignorePagination: true\n      };\n      this.cartService.getOwnShipmentOptions(reqObj).subscribe(res => {\n        if (res.success && res.data.records.length) {\n          this.dataList = res.data.records.filter(record => record.status);\n          this.selectedDeliveryOption = this.dataList[0];\n          this.deliveryOptionDetails = this.selectedDeliveryOption;\n          this.maybeEmitInitialShippingGA();\n          this.onChangeDeliveryOption.emit(this.selectedDeliveryOption);\n        }\n      });\n    }\n    onBack() {\n      this.router.navigate(['/cart']);\n    }\n    triggerOrderSummaryRefresh() {\n      this.refreshOrderSummary.next();\n    }\n    onChangeRegionID(event) {\n      this.regionId = event;\n    }\n    getCurrentCouponCode() {\n      return this.promoCodeComponent?.discount || undefined;\n    }\n    retriggerGAEventsWithCoupon() {\n      if (this.isGoogleAnalytics && this.paymentMethod) {\n        this._GACustomEvent.addPaymentInfoEvent(this.cartItems, this.paymentMethod.name, 1, this.getCurrentCouponCode());\n      }\n      if (this.isGoogleAnalytics && this.deliveryOptionDetails) {\n        const shippingName = this.deliveryOptionDetails.name || this.deliveryOptionDetails.shippingMethod || this.deliveryOptionDetails.title;\n        if (shippingName) {\n          this._GACustomEvent.addShippingInfoEvent(this.cartItems, shippingName, 1, this.getCurrentCouponCode());\n        }\n      }\n    }\n    maybeEmitInitialShippingGA() {\n      if (!this.hasEmittedInitialShippingGA && this.isGoogleAnalytics && this.cartItems && this.cartItems.length && this.deliveryOptionDetails && (this.deliveryOptionDetails.name || this.deliveryOptionDetails.shippingMethod || this.deliveryOptionDetails.title)) {\n        const shippingName = this.deliveryOptionDetails.name || this.deliveryOptionDetails.shippingMethod || this.deliveryOptionDetails.title;\n        this._GACustomEvent.addShippingInfoEvent(this.cartItems, shippingName, 1, this.getCurrentCouponCode());\n        this.hasEmittedInitialShippingGA = true;\n      }\n    }\n    onCouponApplied(couponCode) {\n      this.retriggerGAEventsWithCoupon();\n    }\n    onCouponRemoved() {\n      this.retriggerGAEventsWithCoupon();\n    }\n    static ɵfac = function IndexComponent_Factory(t) {\n      return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i4.GTMService), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i5.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndexComponent,\n      selectors: [[\"app-index\"]],\n      viewQuery: function IndexComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(PromoCodeComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.promoCodeComponent = _t.first);\n        }\n      },\n      outputs: {\n        onChangeDeliveryOption: \"onChangeDeliveryOption\"\n      },\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"new-checkout\"], [\"class\", \"d-flex flex-row checkout\", 4, \"ngIf\"], [\"class\", \"new-checkout\", 4, \"ngIf\"], [\"class\", \"old-checkout\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"flex-row\", \"checkout\"], [1, \"d-flex\"], [2, \"padding\", \"12px 0 12px 12px\", 3, \"click\"], [\"src\", \"assets/icons/mobile-icons/ArrowLeft.svg\", \"alt\", \"No Image\"], [1, \"d-flex\", \"checkout__checkout-section__title\"], [1, \"checkout-shipping-address\", \"col-12\"], [1, \"delivery-method-card__sub-heading\"], [3, \"restrictionMessage\", 4, \"ngIf\"], [1, \"delivery-method-card__section\"], [1, \"delivery-method-card__section__values\"], [1, \"d-flex\", \"justify-content-space-between\"], [1, \"delivery-method-card__delivery-address__text\"], [\"class\", \"delivery-method-card__delivery-address__default\", 4, \"ngIf\"], [\"routerLink\", \"selectAddress\", \"alt\", \"No Image\", \"src\", \"assets/icons/edit-address.svg\", 3, \"state\", \"click\"], [1, \"delivery-method-card__delivery-address__streetAddress\"], [3, \"onChangeDeliveryOption\", \"onPaymentMethodselection\", \"onChangeRegionID\"], [3, \"refreshSummary\", \"paymentMethodDetails\", \"regionId\", \"deliveryOptionDetails\", \"couponApplied\", \"couponRemoved\", 4, \"ngIf\"], [1, \"checkout__order-summary-section\"], [3, \"deliveryOptionDetails\", \"refreshSummary\"], [1, \"paynow-btn\", 3, \"deliveryOptionDetails\", \"paymentMethodDetails\", \"refreshSummary\", \"cartItems\"], [3, \"restrictionMessage\"], [1, \"delivery-method-card__delivery-address__default\"], [3, \"refreshSummary\", \"paymentMethodDetails\", \"regionId\", \"deliveryOptionDetails\", \"couponApplied\", \"couponRemoved\"], [1, \"checkout__checkout-section\"], [1, \"checkout__checkout-section__title\"], [\"class\", \"ckeckout-count\", 4, \"ngIf\"], [\"class\", \"checkout__order-summary-section__age-restriction\", 4, \"ngIf\"], [3, \"deliveryOptionDetails\", \"paymentMethodDetails\", \"refreshSummary\", \"cartItems\"], [1, \"ckeckout-count\"], [1, \"checkout__order-summary-section__age-restriction\"], [1, \"old-checkout\"], [1, \"checkout\", \"checkout-top\"], [1, \"content-container\", \"my-3\"], [1, \"grid\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\", \"align-items-start\", \"justify-content-start\"], [1, \"checkout\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\"], [1, \"grid\", \"align-items-start\", \"justify-content-between\"], [1, \"col-12\", \"col-md-12\", \"col-lg-7\"], [1, \"col-12\", \"col-md-12\", \"col-lg-5\", \"shadow-1\"], [3, \"deliveryOptionDetails\"], [3, \"cartItems\", \"deliveryOptionDetails\", \"paymentMethodDetails\"], [3, \"selectedId\", \"displayModal\", \"submit\", \"addressSelected\"]],\n      template: function IndexComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, IndexComponent_section_1_Template, 33, 19, \"section\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, IndexComponent_div_2_Template, 15, 13, \"div\", 2);\n          i0.ɵɵtemplate(3, IndexComponent_div_3_Template, 17, 7, \"div\", 3);\n          i0.ɵɵtemplate(4, IndexComponent_ng_container_4_Template, 2, 2, \"ng-container\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate && ctx.screenWidth <= 768);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate && ctx.screenWidth >= 768);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.displayModal);\n        }\n      },\n      dependencies: [i6.NgIf, i2.RouterLink, i7.AddressModalComponent, i8.AgeRestrictionComponent, i9.PaymentCartComponent, i10.DeliveryMethodCartComponent, i11.OrderSummaryCartComponent, i12.PromoCodeComponent, i13.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}@media only screen and (max-width: 767px){.new-checkout[_ngcontent-%COMP%]   .layout-checkout-mobile[_ngcontent-%COMP%]{margin-top:210px!important}}.new-checkout[_ngcontent-%COMP%]   .checkout[_ngcontent-%COMP%]{padding:32px;justify-content:center;align-items:flex-start;align-content:flex-start;gap:0px 32px;align-self:stretch}@media only screen and (max-width: 767px){.new-checkout[_ngcontent-%COMP%]   .checkout[_ngcontent-%COMP%]{flex-direction:column!important;padding:10px;margin-top:84px;margin-bottom:60px}}.new-checkout[_ngcontent-%COMP%]   .checkout__checkout-section[_ngcontent-%COMP%]{width:70%;max-width:70%;border:1px solid var(--stroke-color, #E4E7E9)}@media only screen and (max-width: 767px){.new-checkout[_ngcontent-%COMP%]   .checkout__checkout-section[_ngcontent-%COMP%]{width:100%;max-width:100%}}.new-checkout[_ngcontent-%COMP%]   .checkout__checkout-section__title[_ngcontent-%COMP%]{display:flex;padding:20px 24px;align-items:flex-start;gap:10px;color:#191c1f;font-family:var(--medium-font)!important;font-size:18px;font-style:normal;font-weight:500;line-height:24px}@media only screen and (max-width: 767px){.new-checkout[_ngcontent-%COMP%]   .checkout__checkout-section__title[_ngcontent-%COMP%]{padding:12px;color:var(--Gray-900, #191C1F);margin:0}}.new-checkout[_ngcontent-%COMP%]   .checkout__order-summary-section[_ngcontent-%COMP%]{width:30%;max-width:30%;border:1px solid var(--stroke-color, #E4E7E9)}@media only screen and (max-width: 767px){.new-checkout[_ngcontent-%COMP%]   .checkout__order-summary-section[_ngcontent-%COMP%]{width:100%;max-width:100%;border:none!important}}.new-checkout[_ngcontent-%COMP%]   .checkout__order-summary-section__age-restriction[_ngcontent-%COMP%]{padding:0 20px 10px}.old-checkout[_ngcontent-%COMP%]   .checkout[_ngcontent-%COMP%]{font-weight:700;font-size:28px;font-family:var(--medium-font)!important}.old-checkout[_ngcontent-%COMP%]   .checkout-top[_ngcontent-%COMP%]{margin-top:60px}.old-checkout[_ngcontent-%COMP%]   .ckeckout-count[_ngcontent-%COMP%]{font-size:15px;font-weight:300;font-family:var(--regular-font)!important;position:relative;margin-left:6px;color:#a3a3a3;bottom:3px}@media screen and (max-width: 768px){.old-checkout[_ngcontent-%COMP%]   .checkout-top[_ngcontent-%COMP%]{margin-top:25px!important}}.delivery-method-card__sub-heading[_ngcontent-%COMP%]{padding:10px 0;color:#292d32;font-family:main-medium;font-size:14px;font-style:normal;font-weight:500;line-height:normal;text-transform:capitalize}.delivery-method-card__section[_ngcontent-%COMP%]{border-radius:16px;border-bottom:1px solid #E4E7E9;background:#FFF}.delivery-method-card__section__header[_ngcontent-%COMP%]{padding:10px 24px;align-items:center;gap:24px;align-self:stretch;background:#F2F4F5;color:var(--gray-700, #475156);font-family:var(--medium-font);font-size:14px;font-style:normal;font-weight:700;line-height:normal;text-transform:capitalize}.delivery-method-card__section__values[_ngcontent-%COMP%]{display:flex;padding:24px;align-items:flex-start;flex-wrap:wrap;gap:3px;align-self:stretch;background:var(--colors-fff, #FFF)}@media only screen and (max-width: 767px){.delivery-method-card__section__values[_ngcontent-%COMP%]{padding:20px 12px!important;display:block}}.delivery-method-card__delivery-option[_ngcontent-%COMP%]{display:flex;padding:8px 12px;justify-content:center;align-items:center;gap:2px;border-radius:4px;border:1px solid #E4E7E9;color:#191c1f;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:500;line-height:20px}@media only screen and (max-width: 767px){.delivery-method-card__delivery-option[_ngcontent-%COMP%]{border:none!important;padding-left:0!important;padding-right:30px!important}}.delivery-method-card__delivery-address[_ngcontent-%COMP%]{gap:8px}.delivery-method-card__delivery-address__default[_ngcontent-%COMP%]{display:flex;height:19px;padding:3px 16px;flex-direction:column;justify-content:space-between;align-items:center;border-radius:50px;background:#FFCB05;color:#323232;font-family:var(--regular-font);font-size:11px;font-style:normal;font-weight:500;line-height:normal;border:none}@media only screen and (max-width: 767px){.delivery-method-card__delivery-address__default[_ngcontent-%COMP%]{border-radius:4px!important;background:#DCE6FD!important;color:#022c61!important;font-size:10px!important;font-style:normal!important;font-weight:400!important;font-family:var(--regular-font)}}.delivery-method-card__delivery-address__text[_ngcontent-%COMP%]{color:var(--gray-900, #191C1F);font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:500;line-height:100%}@media only screen and (max-width: 767px){.delivery-method-card__delivery-address__text[_ngcontent-%COMP%]{margin-right:13px}}.delivery-method-card__delivery-address__streetAddress[_ngcontent-%COMP%]{color:var(--gray-900, #191C1F);font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:20px}.delivery-method-card__delivery-address__change-button[_ngcontent-%COMP%]{display:flex;height:38px;padding:0 12px;justify-content:center;align-items:center;gap:8px;border-radius:6px;border:2px solid #204E6E;color:var(--colors-main-color, #204E6E);font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:700;line-height:40px;letter-spacing:.168px;text-transform:uppercase}.checkout-shipping-address[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#292d32;font-family:var(--medium-font)!important}@media only screen and (max-width: 767px){.checkout-shipping-address[_ngcontent-%COMP%]{flex-direction:column;padding:10px 12px;width:100%;justify-content:space-between;overflow:scroll;overflow-x:hidden}}@media only screen and (max-width: 767px){.paynow-btn[_ngcontent-%COMP%]{width:100%}}\"]\n    });\n  }\n  return IndexComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}