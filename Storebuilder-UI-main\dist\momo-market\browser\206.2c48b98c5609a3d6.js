"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[206],{7206:(y,P,s)=>{s.r(P),s.d(P,{ResetPasswordConfirmationComponent:()=>re});var i=s(6814),p=s(6223),w=s(6075),C=s(6663),x=s(707),d=s(3714),Z=s(1423),f=s(1312),e=s(5879),b=s(864),v=s(5219);function M(t,a){1&t&&(e.TgZ(0,"p",28)(1,"small",29),e._uU(2),e.ALo(3,"translate"),e.qZA()()),2&t&&(e.xp6(2),e.Oqu(e.lcZ(3,1,"auth.registerPassword.validation8")))}function A(t,a){1&t&&e._UZ(0,"em",30)}function T(t,a){1&t&&e._UZ(0,"em",31)}function q(t,a){1&t&&e._UZ(0,"em",32)}const h=function(t){return{color:t}};function N(t,a){if(1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t){const o=e.oxw();e.Q6J("ngStyle",e.VKq(4,h,!0===o.hasMinimum8Chars?"#01B467":"red")),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation1"))}}const _=function(){return{color:"grey"}};function I(t,a){1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t&&(e.Q6J("ngStyle",e.DdM(4,_)),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation1")))}function U(t,a){1&t&&e._UZ(0,"em",30)}function J(t,a){1&t&&e._UZ(0,"em",31)}function R(t,a){1&t&&e._UZ(0,"em",32)}function S(t,a){if(1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t){const o=e.oxw();e.Q6J("ngStyle",e.VKq(4,h,!0===o.hasLowerChar?"#01B467":"red")),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation2"))}}function O(t,a){1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t&&(e.Q6J("ngStyle",e.DdM(4,_)),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation2")))}function z(t,a){1&t&&e._UZ(0,"em",30)}function Q(t,a){1&t&&e._UZ(0,"em",31)}function Y(t,a){1&t&&e._UZ(0,"em",32)}function k(t,a){if(1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t){const o=e.oxw();e.Q6J("ngStyle",e.VKq(4,h,!0===o.hasUpperChar?"#01B467":"red")),e.xp6(1),e.hij(" ",e.lcZ(2,2,"auth.registerPassword.validation3"),"")}}function L(t,a){1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t&&(e.Q6J("ngStyle",e.DdM(4,_)),e.xp6(1),e.hij(" ",e.lcZ(2,2,"auth.registerPassword.validation3"),""))}function j(t,a){1&t&&e._UZ(0,"em",30)}function E(t,a){1&t&&e._UZ(0,"em",31)}function B(t,a){1&t&&e._UZ(0,"em",32)}function D(t,a){if(1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t){const o=e.oxw();e.Q6J("ngStyle",e.VKq(4,h,!0===o.hasAtleastOneNumber?"#01B467":"red")),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation4"))}}function F(t,a){1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t&&(e.Q6J("ngStyle",e.DdM(4,_)),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation4")))}function V(t,a){1&t&&e._UZ(0,"em",30)}function K(t,a){1&t&&e._UZ(0,"em",31)}function G(t,a){1&t&&e._UZ(0,"em",32)}function H(t,a){if(1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t){const o=e.oxw();e.Q6J("ngStyle",e.VKq(4,h,!0===o.hasSpecialChars?"#01B467":"red")),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation5"))}}function W(t,a){1&t&&(e.TgZ(0,"span",33),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&t&&(e.Q6J("ngStyle",e.DdM(4,_)),e.xp6(1),e.Oqu(e.lcZ(2,2,"auth.registerPassword.validation5")))}function X(t,a){1&t&&e._UZ(0,"em",36)}function $(t,a){if(1&t&&(e.TgZ(0,"p",28),e.YNc(1,X,1,0,"em",34),e.TgZ(2,"span",35),e._uU(3),e.ALo(4,"translate"),e.qZA()()),2&t){const o=e.oxw();e.xp6(1),e.Q6J("ngIf",!1===o.hasSpecialChars),e.xp6(2),e.Oqu(e.lcZ(4,2,"auth.registerPassword.validation7"))}}function ee(t,a){1&t&&(e.TgZ(0,"div",37),e._UZ(1,"img",38),e.qZA(),e.TgZ(2,"p",39),e._uU(3),e.ALo(4,"translate"),e.qZA()),2&t&&(e.xp6(3),e.hij(" ",e.lcZ(4,1,"updatePassword.passwordReset")," "))}const te=function(){return["/login"]};function ne(t,a){1&t&&(e._UZ(0,"button",40),e.ALo(1,"translate")),2&t&&e.Q6J("label",e.lcZ(1,2,"updatePassword.backToLogin"))("routerLink",e.DdM(4,te))}const oe=function(t){return{marginTop:t}},ae=function(){return{width:"22vw"}},se=function(){return{"960px":"75vw","640px":"100vw"}};let re=(()=>{class t{store;fb;permissionService;user;translate;messageService;el;router;newPassword="";confirmPassword="";phoneNumber;verificationCode="";hasUpperChar;hasAtleastOneNumber;hasLowerChar;passwordMatched=!1;hasMinimum8Chars=void 0;hasSpecialChars;passwordIsValid=!1;password="";forgetPassword;firstNameFlag=!1;displayApprovedModal=!1;isMobileLayout=!1;constructor(o,c,n,l,r,u,g,ie){this.store=o,this.fb=c,this.permissionService=n,this.user=l,this.translate=r,this.messageService=u,this.el=g,this.router=ie}get formcontrols(){return this.forgetPassword.controls}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.verificationCode=this.store.get("verificationCode"),this.phoneNumber=localStorage.getItem("userPhone"),this.formGroup()}formGroup(){this.forgetPassword=this.fb.group({newPassword:["",[p.kI.required,t=>{const a=t.value.toLowerCase();return["Yalla Mall","Momo Market"].some(r=>a.includes(r))||["january","february","march","april","may","june","july","august","september","october","november","december","January","February","March","April","May","June","July","August","September","October","November","December","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER"].some(r=>a.includes(r))||["spring","summer","autumn","winter","Spring","Summer","Autumn","Winter","SPRING","SUMMER","AUTUMN","WINTER"].some(r=>a.includes(r))||[" ","123456","123456789","guest","qwerty","12345678","111111","12345","col123456","123123","1234567","1234","1234567890","000000","555555","666666","123321","654321","7777777","123","D1lakiss","777777","110110jp","1111","987654321","121212","Gizli","abc123","112233","azerty","159753","1q2w3e4r","54321","pass@123","222222","qwertyuiop","qwerty123","qazwsx","vip","asdasd","123qwe","123654","iloveyou","a1b2c3","999999","Groupd2013","1q2w3e","usr","Liman1000","1111111","333333","123123123","9136668099","11111111","1qaz2wsx","password1","mar20lt","987654321","gfhjkm","159357","abcd1234","131313","789456","luzit2000","aaaaaa","zxcvbnm","asdfghjkl","1234qwer","88888888","dragon","987654","888888","qwe123","football","3601","asdfgh","master","samsung","12345678910","killer","1237895","1234561","12344321","daniel","000000","444444","101010","fuckyou","qazwsxedc","789456123","super123","qwer1234","123456789a","823477aA","147258369","unknown","98765","q1w2e3r4","232323","102030","12341234","147258","shadow","123456a","87654321","10203","pokemon","princess","azertyuiop","thomas","baseball","monkey","jordan","michael","love","1111111111","11223344","123456789","asdf1234","147852","252525","11111","loulou","111222","superman","qweasdzxc","soccer","qqqqqq","123abc","computer","qweasd","zxcvbn","sunshine","1234554321","asd123","marina","lol123","a123456","Password","123789","jordan23","jessica","212121","7654321","googledummy","qwerty1","123654789","naruto","Indya123","internet","doudou","anmol123","55555","andrea","anthony","martin","basketball","nicole","xxxxxx","1qazxsw2","charlie","12345qwert","zzzzzz","q1w2e3","147852369","hello","welcome","marseille","456123","secret","matrix","zaq12wsx","password123","qwertyu","hunter","freedom","999999999","eminem","junior","696969","andrew","michelle","wow12345","juventus","batman","justin","12qwaszx","Pass@123","passw0rd","soleil","nikita","Password1","qweqwe","nicolas","robert","starwars","liverpool","5555555","bonjour","124578"].includes(a)?{invalidPassword:{value:t.value}}:null}]],confirmPassword:["",p.kI.required]})}approveModal(){this.user.UpdatePassword({mobileNumber:this.phoneNumber,newPassword:this.forgetPassword?.controls.newPassword?.value,RequestId:this.verificationCode}).subscribe({next:o=>{o.success?this.displayApprovedModal=!0:this.messageService.add({severity:"error",summary:this.translate.instant(o.message)})},error:o=>{this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:o.message})}})}checkPasswordPattern(o){return this.hasAtleastOneNumber=/\d+/.test(o),this.hasUpperChar=/[A-Z]+/.test(o),this.hasLowerChar=/[a-z]+/.test(o),this.hasMinimum8Chars=/.{10,}/.test(o),this.hasSpecialChars=/[!@#$%^&*(),.?":{}|<>]/.test(o),this.passwordMatched=this.confirmPassword===this.password,this.passwordIsValid=this.hasMinimum8Chars&&this.hasUpperChar&&this.hasLowerChar&&this.hasSpecialChars&&this.passwordMatched&&this.hasAtleastOneNumber,this.passwordIsValid}disabled(){return!((this.forgetPassword?.controls.newPassword?.value===this.forgetPassword?.controls.confirmPassword?.value||""===this.forgetPassword?.controls.newPassword?.value)&&this.hasSpecialChars&&this.hasUpperChar&&this.hasLowerChar&&this.hasMinimum8Chars&&this.hasAtleastOneNumber)}onblur(o){this.isRequired("newPassword"),"cp"!==o&&!this.hasMinimum8Chars&&!this.hasAtleastOneNumber&&!this.hasLowerChar&&!this.hasSpecialChars&&!this.hasUpperChar&&(this.hasMinimum8Chars=!1,this.hasLowerChar=!1,this.hasAtleastOneNumber=!1,this.hasSpecialChars=!1,this.hasUpperChar=!1)}isRequired(o){this.formcontrols[o]?.errors?.required&&(this.firstNameFlag=!0)}isError(o){return this.formcontrols[o]?.errors}focusFirstInvalidField(){for(const o of Object.keys(this.formcontrols))if(this.formcontrols[o].invalid){this.focusInputField(o);break}}focusInputField(o){this.el.nativeElement.querySelector('[formControlName="'+o+'"]').focus()}static \u0275fac=function(c){return new(c||t)(e.Y36(b.d6),e.Y36(p.qu),e.Y36(b.$A),e.Y36(b.KD),e.Y36(C.sK),e.Y36(v.ez),e.Y36(e.SBq),e.Y36(w.F0))};static \u0275cmp=e.Xpm({type:t,selectors:[["app-reset-password-confirmation"]],standalone:!0,features:[e.jDz],decls:67,vars:66,consts:[[1,"update-password-page"],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center","margin-x-100"],[1,"col-12","py-0","text-center","reset-pass"],[1,"col-12","mb-3","text-center","secure-pass"],[1,"col-12","col-md-8","col-lg-6","bg-white","shadow-signin","px-5","pt-0","pb-4"],[1,"p-fluid","p-grid"],[3,"formGroup"],["form","ngForm"],[1,"p-field","p-col-12","mt-3"],[1,"p-float-label","mt-5"],["aria-autocomplete","false","autocomplete","newPassword","formControlName","newPassword","name","something",1,"customClass",3,"feedback","toggleMask","click","ngModelChange"],[3,"ngStyle"],["class","field-error",4,"ngIf"],[1,"p-float-label","mt-3"],["aria-autocomplete","false","autocomplete","newPassword","formControlName","confirmPassword","name","something",1,"customClass",3,"ngModel","feedback","toggleMask","click","ngModelChange"],[1,"p-field","p-col-12"],[1,"list","p-0"],[1,"list-none","font-size-13","font-italic","mb-2"],["aria-hidden","true","class","pi pi-info-circle","style","color: grey; font-size: 0.8rem",4,"ngIf"],["aria-hidden","true","class","pi pi-check-circle","style","color: #01b467; font-size: 0.8rem",4,"ngIf"],["aria-hidden","true","class","pi pi-times-circle","style","color: red; font-size: 0.8rem",4,"ngIf"],["class","ml-2 instruction",3,"ngStyle",4,"ngIf"],[1,"list-none","font-size-13","font-italic","mb-5"],["pButton","","type","button",1,"p-field","p-col-12","my-2","width-100","font-size-14","second-btn",3,"disabled","label","click"],[1,"approvedModal",3,"visible","modal","baseZIndex","breakpoints","visibleChange"],["pTemplate","content"],["pTemplate","footer"],[1,"field-error"],[2,"color","red"],["aria-hidden","true",1,"pi","pi-info-circle",2,"color","grey","font-size","0.8rem"],["aria-hidden","true",1,"pi","pi-check-circle",2,"color","#01b467","font-size","0.8rem"],["aria-hidden","true",1,"pi","pi-times-circle",2,"color","red","font-size","0.8rem"],[1,"ml-2","instruction",3,"ngStyle"],["aria-hidden","true","class","fa fa-times-circle fa-size","style","color: red",4,"ngIf"],[1,"ml-2","instruction",2,"color","red"],["aria-hidden","true",1,"fa","fa-times-circle","fa-size",2,"color","red"],[1,"icon","mt-5","mb-5","bg-#01B467-500","text-white","text-center","w-3rem","h-3rem","border-circle","icon","bg-#01B467-500"],["alt","No Image","src","assets/images/Validated.png"],[1,"font-bold","text-center","text-black-alpha-90","font-family-password"],["pButton","","type","button",1,"p-field","p-col-12","my-4","width-25","m-auto","font-size-14","second-btn",3,"label","routerLink"]],template:function(c,n){if(1&c&&(e.TgZ(0,"section",0),e.ynx(1),e.TgZ(2,"div",1)(3,"div",2)(4,"p",3),e._uU(5),e.ALo(6,"translate"),e.qZA(),e.TgZ(7,"div",4),e._uU(8),e.ALo(9,"translate"),e.qZA(),e.TgZ(10,"div",5)(11,"div",6)(12,"form",7,8)(14,"div",9)(15,"span",10)(16,"p-password",11),e.NdJ("click",function(){return n.onblur()})("ngModelChange",function(r){return n.checkPasswordPattern(r)}),e.qZA(),e.TgZ(17,"label",12),e._uU(18),e.ALo(19,"translate"),e.qZA()(),e.YNc(20,M,4,3,"p",13),e.qZA(),e.TgZ(21,"div",9)(22,"span",14)(23,"p-password",15),e.NdJ("click",function(){return n.onblur("cp")})("ngModelChange",function(r){return n.confirmPassword=r}),e.qZA(),e.TgZ(24,"label",12),e._uU(25),e.ALo(26,"translate"),e.qZA()()()(),e._UZ(27,"br"),e.TgZ(28,"div",16)(29,"ul",17)(30,"li",18),e.YNc(31,A,1,0,"em",19),e.YNc(32,T,1,0,"em",20),e.YNc(33,q,1,0,"em",21),e.YNc(34,N,3,6,"span",22),e.YNc(35,I,3,5,"span",22),e.qZA(),e.TgZ(36,"li",18),e.YNc(37,U,1,0,"em",19),e.YNc(38,J,1,0,"em",20),e.YNc(39,R,1,0,"em",21),e.YNc(40,S,3,6,"span",22),e.YNc(41,O,3,5,"span",22),e.qZA(),e.TgZ(42,"li",18),e.YNc(43,z,1,0,"em",19),e.YNc(44,Q,1,0,"em",20),e.YNc(45,Y,1,0,"em",21),e.YNc(46,k,3,6,"span",22),e.YNc(47,L,3,5,"span",22),e.qZA(),e.TgZ(48,"li",18),e.YNc(49,j,1,0,"em",19),e.YNc(50,E,1,0,"em",20),e.YNc(51,B,1,0,"em",21),e.YNc(52,D,3,6,"span",22),e.YNc(53,F,3,5,"span",22),e.qZA(),e.TgZ(54,"li",18),e.YNc(55,V,1,0,"em",19),e.YNc(56,K,1,0,"em",20),e.YNc(57,G,1,0,"em",21),e.YNc(58,H,3,6,"span",22),e.YNc(59,W,3,5,"span",22),e.qZA(),e.TgZ(60,"li",23),e.YNc(61,$,5,4,"p",13),e.qZA()()(),e.TgZ(62,"button",24),e.NdJ("click",function(){return n.approveModal()}),e.ALo(63,"translate"),e.qZA()()()()(),e.TgZ(64,"p-dialog",25),e.NdJ("visibleChange",function(r){return n.displayApprovedModal=r}),e.YNc(65,ee,5,3,"ng-template",26),e.YNc(66,ne,2,5,"ng-template",27),e.qZA(),e.BQk(),e.qZA()),2&c){let l,r,u,g;e.xp6(2),e.Q6J("ngStyle",e.VKq(58,oe,n.isMobileLayout?"1rem":"220px")),e.xp6(3),e.hij(" ",e.lcZ(6,48,"updatePassword.resetPassword")," "),e.xp6(3),e.hij(" ",e.lcZ(9,50,"updatePassword.setUpSecurePassword"),". "),e.xp6(4),e.Q6J("formGroup",n.forgetPassword),e.xp6(4),e.Q6J("feedback",!0)("toggleMask",!0),e.xp6(1),e.Q6J("ngStyle",e.VKq(60,h,n.firstNameFlag&&0===(null==(l=n.forgetPassword.get("newPassword"))?null:l.value.length)?"red":"grey")),e.xp6(1),e.hij("",e.lcZ(19,52,"updatePassword.newPassword")," *"),e.xp6(2),e.Q6J("ngIf",null==(r=n.forgetPassword.get("newPassword"))||null==r.errors?null:r.errors.invalidPassword),e.xp6(3),e.Q6J("ngModel",n.confirmPassword)("feedback",!1)("toggleMask",!0),e.xp6(1),e.Q6J("ngStyle",e.VKq(62,h,n.firstNameFlag&&0===(null==(u=n.forgetPassword.get("newPassword"))?null:u.value.length)||(null==(u=n.forgetPassword.get("newPassword"))?null:u.value)!==(null==(u=n.forgetPassword.get("confirmPassword"))?null:u.value)?"red":"grey")),e.xp6(1),e.hij("",e.lcZ(26,54,"updatePassword.confirmPassword")," *"),e.xp6(6),e.Q6J("ngIf",void 0===n.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!0===n.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",!1===n.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",void 0!==n.hasMinimum8Chars),e.xp6(1),e.Q6J("ngIf",void 0===n.hasMinimum8Chars),e.xp6(2),e.Q6J("ngIf",void 0===n.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!0===n.hasLowerChar),e.xp6(1),e.Q6J("ngIf",!1===n.hasLowerChar),e.xp6(1),e.Q6J("ngIf",void 0!==n.hasLowerChar),e.xp6(1),e.Q6J("ngIf",void 0===n.hasLowerChar),e.xp6(2),e.Q6J("ngIf",void 0===n.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!0===n.hasUpperChar),e.xp6(1),e.Q6J("ngIf",!1===n.hasUpperChar),e.xp6(1),e.Q6J("ngIf",void 0!==n.hasUpperChar),e.xp6(1),e.Q6J("ngIf",void 0===n.hasUpperChar),e.xp6(2),e.Q6J("ngIf",void 0===n.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!0===n.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",!1===n.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",void 0!==n.hasAtleastOneNumber),e.xp6(1),e.Q6J("ngIf",void 0===n.hasAtleastOneNumber),e.xp6(2),e.Q6J("ngIf",void 0===n.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!0===n.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",!1===n.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",void 0!==n.hasSpecialChars),e.xp6(1),e.Q6J("ngIf",void 0===n.hasSpecialChars),e.xp6(2),e.Q6J("ngIf",(null==n.forgetPassword||null==(g=n.forgetPassword.get("newPassword"))?null:g.value)!==(null==n.forgetPassword||null==(g=n.forgetPassword.get("confirmPassword"))?null:g.value)),e.xp6(1),e.Q6J("disabled",!0===n.disabled())("label",e.lcZ(63,56,"updatePassword.passwordReset")),e.xp6(2),e.Akn(e.DdM(64,ae)),e.Q6J("visible",n.displayApprovedModal)("modal",!0)("baseZIndex",1e4)("breakpoints",e.DdM(65,se))}},dependencies:[i.ez,i.O5,i.PC,p.UX,p._Y,p.JJ,p.JL,p.sg,p.u,C.aw,C.X$,x.hJ,x.Hq,v.jx,d.j,Z.gz,Z.ro,f.S,f.V,w.Bz,w.rH],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}  .p-dialog .p-dialog-header{display:none}div.icon[_ngcontent-%COMP%]{position:relative;margin:auto}div.icon[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%]{position:absolute;top:31%;font-weight:700;left:30%;font-size:1.2rem}  .p-dialog .p-dialog-footer{text-align:center}  .p-dialog .p-dialog-footer button{width:90%!important}input[_ngcontent-%COMP%]{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol;font-size:1rem;color:#495057;background:#ffffff;padding:.5rem;border:1px solid #ced4da;transition:background-color .2s,color .2s,border-color .2s,box-shadow .2s;appearance:none;border-radius:3px;width:100%}.green[_ngcontent-%COMP%]{color:#228b22!important}.fa-size[_ngcontent-%COMP%]{font-size:15px}li[_ngcontent-%COMP%]{text-align:left;letter-spacing:0px;opacity:1;font-family:main-medium,sans-serif;font-style:inherit!important}.reset-pass[_ngcontent-%COMP%]{font-size:28px;font-weight:700;font-family:var(--medium-font)!important}.secure-pass[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#a3a3a3;font-family:var(--regular-font)!important}  .customClass input{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;padding-top:20px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px}  .pi-eye{position:absolute!important;right:9px!important;top:30px!important}label[_ngcontent-%COMP%]{color:#323232!important;position:absolute;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.instruction[_ngcontent-%COMP%]{font-size:12px;font-weight:400;font-family:var(--regular-font)!important}.shadow-signin[_ngcontent-%COMP%]{border:1px solid rgba(151,151,151,.17);border-radius:7px}.second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important;text-transform:uppercase}@media screen and (max-width: 768px){.update-password-page[_ngcontent-%COMP%]{margin-top:0}}.font-family-password[_ngcontent-%COMP%]{font-family:var(--medium-font)!important}  .p-dialog{top:-74px!important;box-shadow:none!important;border:1px solid rgba(151,151,151,.17)!important}  .p-dialog-content{margin-top:4rem!important}  .p-dialog-footer{padding-bottom:0rem!important}@media only screen and (max-width: 767px){.margin-x-100[_ngcontent-%COMP%]{margin-top:250px!important}}']})}return t})()},3714:(y,P,s)=>{s.d(P,{j:()=>x,o:()=>C});var i=s(5879),p=s(6814),w=s(6223);let C=(()=>{class d{el;ngModel;cd;filled;constructor(f,m,e){this.el=f,this.ngModel=m,this.cd=e}ngAfterViewInit(){this.updateFilledState(),this.cd.detectChanges()}ngDoCheck(){this.updateFilledState()}onInput(){this.updateFilledState()}updateFilledState(){this.filled=this.el.nativeElement.value&&this.el.nativeElement.value.length||this.ngModel&&this.ngModel.model}static \u0275fac=function(m){return new(m||d)(i.Y36(i.SBq),i.Y36(w.On,8),i.Y36(i.sBO))};static \u0275dir=i.lG2({type:d,selectors:[["","pInputText",""]],hostAttrs:[1,"p-inputtext","p-component","p-element"],hostVars:2,hostBindings:function(m,e){1&m&&i.NdJ("input",function(v){return e.onInput(v)}),2&m&&i.ekj("p-filled",e.filled)}})}return d})(),x=(()=>{class d{static \u0275fac=function(m){return new(m||d)};static \u0275mod=i.oAB({type:d});static \u0275inj=i.cJS({imports:[p.ez]})}return d})()}}]);