{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\promotion.ts"], "sourcesContent": ["export interface Promotion {\r\n  imageUrl: string;\r\n  ctaLink: string;\r\n  isActive?: boolean;\r\n  promotionId?:number;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}