{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/services/store.service\";\nfunction DetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailsComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 5)(3, \"p\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"div\", 10);\n    i0.ɵɵelement(10, \"input\", 11);\n    i0.ɵɵelementStart(11, \"label\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 10);\n    i0.ɵɵelement(15, \"input\", 11);\n    i0.ɵɵelementStart(16, \"label\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 10);\n    i0.ɵɵelement(20, \"input\", 11);\n    i0.ɵɵelementStart(21, \"label\");\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 10);\n    i0.ɵɵelement(25, \"input\", 11);\n    i0.ɵɵelementStart(26, \"label\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 9, \"account.details.yourDetails\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.userDetails.userName[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(13, 11, \"account.details.firstName\"), \" * \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.userDetails.userName[1]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(18, 13, \"account.details.lastName\"), \" * \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.userDetails.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(23, 15, \"account.details.email\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r1.userDetails.mobileNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(28, 17, \"account.details.phoneNumber\"), \" * \");\n  }\n}\nexport class DetailsComponent {\n  constructor(store) {\n    this.store = store;\n    this.loading = false;\n    this.confirmPassword = \"\";\n    this.emailAddress = \"\";\n    this.lastName = \"\";\n    this.firstName = \"\";\n    this.mobileNumber = \"\";\n    this.otpCode = \"\";\n    this.hasUpperChar = false;\n    this.hasLowerChar = false;\n    this.hasMinimum8Chars = false;\n    this.hasSpecialChars = false;\n    this.passwordIsValid = false;\n    this.password = \"Abcde@12_s\";\n    this.displayApprovedModal = false;\n  }\n  ngOnInit() {\n    this.userDetails = this.store.get('profile');\n    if (this.userDetails) {\n      this.userDetails.userName = this.userDetails.name.split(\" \");\n      // console.log(this.userDetails);\n    }\n  }\n\n  approveModal() {\n    this.displayApprovedModal = true;\n  }\n  submit() {}\n  checkPasswordPattern(password) {\n    this.hasUpperChar = /[A-Z]+/.test(password);\n    this.hasLowerChar = /[a-z]+/.test(password);\n    this.hasMinimum8Chars = /.{8,}/.test(password);\n    this.hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    this.passwordIsValid = this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars;\n    return this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars;\n  }\n  static #_ = this.ɵfac = function DetailsComponent_Factory(t) {\n    return new (t || DetailsComponent)(i0.ɵɵdirectiveInject(i1.StoreService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DetailsComponent,\n    selectors: [[\"app-details\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"update-password-page\"], [\"class\", \"spinner\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"spinner\"], [1, \"content-container\", \"my-3\"], [1, \"grid\", \"justify-content-start\", \"margin-x-100\"], [1, \"col-12\", \"bold-font\", \"font-size-28\", \"m-0\", \"py-0\", \"mx-4\", \"mb-4\"], [1, \"col-12\", \"md:col-6\", \"lg:col-4\", \"border-round\", \"bg-white\", \"shadow-1\", \"px-5\", \"pt-6\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\", \"mb-8\"], [1, \"p-field\", \"p-col-12\"], [\"type\", \"text\", \"disabled\", \"\", 3, \"value\"]],\n    template: function DetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, DetailsComponent_div_1_Template, 2, 0, \"div\", 1);\n        i0.ɵɵtemplate(2, DetailsComponent_ng_container_2_Template, 29, 19, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      }\n    },\n    styles: [\".p-field[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 1rem;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background-color: whitesmoke;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  padding: 22px 10px 13px;\\n  font-size: 15px;\\n  color: black;\\n  font-family: var(--regular-font);\\n  border: 1px solid whitesmoke;\\n  border-radius: 5px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: none;\\n  top: 15%;\\n  margin-top: -0.5rem;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  color: #a59e9e;\\n  font-size: 12px;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .update-password-page[_ngcontent-%COMP%] {\\n    margin-top: 160px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵpropertyInterpolate", "ctx_r1", "userDetails", "userName", "ɵɵtextInterpolate1", "email", "mobileNumber", "DetailsComponent", "constructor", "store", "loading", "confirmPassword", "emailAddress", "lastName", "firstName", "otpCode", "hasUpperChar", "hasLowerChar", "hasMinimum8Chars", "hasSpecialChars", "passwordIsValid", "password", "displayApprovedModal", "ngOnInit", "get", "name", "split", "approveModal", "submit", "checkPasswordPattern", "test", "_", "ɵɵdirectiveInject", "i1", "StoreService", "_2", "selectors", "decls", "vars", "consts", "template", "DetailsComponent_Template", "rf", "ctx", "ɵɵtemplate", "DetailsComponent_div_1_Template", "DetailsComponent_ng_container_2_Template", "ɵɵproperty"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\details\\details.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\details\\details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { StoreService } from 'src/app/shared/services/store.service';\r\n\r\n@Component({\r\n  selector: 'app-details',\r\n  templateUrl: './details.component.html',\r\n  styleUrls: ['./details.component.scss']\r\n})\r\nexport class DetailsComponent implements OnInit {\r\n\r\n  constructor(private store: StoreService\r\n  ) { }\r\n  loading: boolean = false;\r\n  confirmPassword: string = \"\";\r\n  emailAddress: string = \"\";\r\n  lastName: string = \"\";\r\n  firstName: string = \"\";\r\n  mobileNumber: string = \"\";\r\n  otpCode: string = \"\";\r\n  hasUpperChar: boolean = false;\r\n  hasLowerChar: boolean = false;\r\n  hasMinimum8Chars: boolean = false;\r\n  hasSpecialChars: boolean = false;\r\n  passwordIsValid: boolean = false;\r\n  userDetails: any;\r\n  password: string = \"Abcde@12_s\";\r\n  displayApprovedModal: boolean = false;\r\n\r\n  ngOnInit(): void {\r\n    this.userDetails = this.store.get('profile');\r\n    if (this.userDetails) {\r\n      this.userDetails.userName = this.userDetails.name.split(\" \");\r\n      // console.log(this.userDetails);\r\n    }\r\n\r\n  }\r\n  approveModal() {\r\n    this.displayApprovedModal = true;\r\n  }\r\n  submit() {\r\n  }\r\n  checkPasswordPattern(password: string) {\r\n    this.hasUpperChar = /[A-Z]+/.test(password);\r\n    this.hasLowerChar = /[a-z]+/.test(password);\r\n    this.hasMinimum8Chars = /.{8,}/.test(password);\r\n    this.hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\r\n\r\n    this.passwordIsValid = this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars;\r\n    return this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars;\r\n  }\r\n}\r\n", "<section class=\"update-password-page\">\r\n  <div *ngIf=\"loading\" class=\"spinner\">\r\n    <p-progressSpinner></p-progressSpinner>\r\n  </div>\r\n  <ng-container *ngIf=\"!loading\">\r\n    <div class=\"content-container my-3\">\r\n      <div class=\"grid justify-content-start margin-x-100\">\r\n        <p class=\"col-12 bold-font font-size-28 m-0 py-0 mx-4 mb-4\">{{\"account.details.yourDetails\" | translate}}</p>\r\n        <div\r\n          class=\"col-12 md:col-6 lg:col-4 border-round bg-white shadow-1 px-5 pt-6\"\r\n        >\r\n          <div class=\"p-fluid p-grid\">\r\n            <div class=\"p-field p-col-12 mb-8\">\r\n              <div class=\"p-field p-col-12\">\r\n                <input\r\n                  type=\"text\"\r\n                  value=\"{{ userDetails.userName[0] }}\"\r\n                  disabled\r\n                />\r\n                <label>{{\"account.details.firstName\" | translate}} * </label>\r\n              </div>\r\n              <div class=\"p-field p-col-12\">\r\n                <input\r\n                  type=\"text\"\r\n                  value=\"{{ userDetails.userName[1] }}\"\r\n                  disabled\r\n                />\r\n                <label>{{\"account.details.lastName\" | translate}} * </label>\r\n              </div>\r\n              <div class=\"p-field p-col-12\">\r\n                <input type=\"text\" value=\"{{ userDetails.email }}\" disabled />\r\n                <label>{{\"account.details.email\" | translate}} </label>\r\n              </div>\r\n              <div class=\"p-field p-col-12\">\r\n                <input\r\n                  type=\"text\"\r\n                  value=\"{{ userDetails.mobileNumber }}\"\r\n                  disabled\r\n                />\r\n                <label>{{\"account.details.phoneNumber\" | translate}} * </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</section>\r\n"], "mappings": ";;;;ICCEA,EAAA,CAAAC,cAAA,aAAqC;IACnCD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAI,uBAAA,GAA+B;IAC7BJ,EAAA,CAAAC,cAAA,aAAoC;IAE4BD,EAAA,CAAAK,MAAA,GAA6C;;IAAAL,EAAA,CAAAG,YAAA,EAAI;IAC7GH,EAAA,CAAAC,cAAA,aAEC;IAIOD,EAAA,CAAAE,SAAA,iBAIE;IACFF,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAK,MAAA,IAA8C;;IAAAL,EAAA,CAAAG,YAAA,EAAQ;IAE/DH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,SAAA,iBAIE;IACFF,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAK,MAAA,IAA6C;;IAAAL,EAAA,CAAAG,YAAA,EAAQ;IAE9DH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,SAAA,iBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAK,MAAA,IAAwC;;IAAAL,EAAA,CAAAG,YAAA,EAAQ;IAEzDH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,SAAA,iBAIE;IACFF,EAAA,CAAAC,cAAA,aAAO;IAAAD,EAAA,CAAAK,MAAA,IAAgD;;IAAAL,EAAA,CAAAG,YAAA,EAAQ;IAO7EH,EAAA,CAAAM,qBAAA,EAAe;;;;IAvCmDN,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAS,WAAA,sCAA6C;IAS/FT,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAU,qBAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,QAAA,IAAqC;IAGhCb,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAS,WAAA,6CAA8C;IAKnDT,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAU,qBAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,QAAA,IAAqC;IAGhCb,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAS,WAAA,4CAA6C;IAGjCT,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAU,qBAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAG,KAAA,CAA+B;IAC3Cf,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAS,WAAA,uCAAwC;IAK7CT,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAU,qBAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAI,YAAA,CAAsC;IAGjChB,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAS,WAAA,+CAAgD;;;AD/BvE,OAAM,MAAOQ,gBAAgB;EAE3BC,YAAoBC,KAAmB;IAAnB,KAAAA,KAAK,GAALA,KAAK;IAEzB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAR,YAAY,GAAW,EAAE;IACzB,KAAAS,OAAO,GAAW,EAAE;IACpB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,eAAe,GAAY,KAAK;IAEhC,KAAAC,QAAQ,GAAW,YAAY;IAC/B,KAAAC,oBAAoB,GAAY,KAAK;EAfjC;EAiBJC,QAAQA,CAAA;IACN,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACO,KAAK,CAACe,GAAG,CAAC,SAAS,CAAC;IAC5C,IAAI,IAAI,CAACtB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACC,QAAQ,GAAG,IAAI,CAACD,WAAW,CAACuB,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC5D;;EAGJ;;EACAC,YAAYA,CAAA;IACV,IAAI,CAACL,oBAAoB,GAAG,IAAI;EAClC;EACAM,MAAMA,CAAA,GACN;EACAC,oBAAoBA,CAACR,QAAgB;IACnC,IAAI,CAACL,YAAY,GAAG,QAAQ,CAACc,IAAI,CAACT,QAAQ,CAAC;IAC3C,IAAI,CAACJ,YAAY,GAAG,QAAQ,CAACa,IAAI,CAACT,QAAQ,CAAC;IAC3C,IAAI,CAACH,gBAAgB,GAAG,OAAO,CAACY,IAAI,CAACT,QAAQ,CAAC;IAC9C,IAAI,CAACF,eAAe,GAAG,wBAAwB,CAACW,IAAI,CAACT,QAAQ,CAAC;IAE9D,IAAI,CAACD,eAAe,GAAG,IAAI,CAACF,gBAAgB,IAAI,IAAI,CAACF,YAAY,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACE,eAAe;IAC9G,OAAO,IAAI,CAACD,gBAAgB,IAAI,IAAI,CAACF,YAAY,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACE,eAAe;EAChG;EAAC,QAAAY,CAAA,G;qBAzCUxB,gBAAgB,EAAAjB,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB5B,gBAAgB;IAAA6B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR7BpD,EAAA,CAAAC,cAAA,iBAAsC;QACpCD,EAAA,CAAAsD,UAAA,IAAAC,+BAAA,iBAEM;QACNvD,EAAA,CAAAsD,UAAA,IAAAE,wCAAA,4BA0Ce;QACjBxD,EAAA,CAAAG,YAAA,EAAU;;;QA9CFH,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAyD,UAAA,SAAAJ,GAAA,CAAAjC,OAAA,CAAa;QAGJpB,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAyD,UAAA,UAAAJ,GAAA,CAAAjC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}