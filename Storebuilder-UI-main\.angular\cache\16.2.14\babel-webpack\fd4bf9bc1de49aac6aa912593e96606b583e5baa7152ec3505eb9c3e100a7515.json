{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport { debounceTime, distinctUntilChanged, fromEvent, map, switchMap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"primeng/divider\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-intl-tel-input-gg\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = [\"inputField\"];\nfunction SearchComponent_ng_container_0_ng_container_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_ng_container_9_div_3_Template_div_mousedown_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const option_r6 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.NavigateToProduct(option_r6.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"p-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", option_r6.currencyCode, \"\\u00A0\", (option_r6.specsProducts[0] == null ? null : option_r6.specsProducts[0].salePrice) ? option_r6.specsProducts[0] == null ? null : option_r6.specsProducts[0].salePrice : option_r6.specsProducts[0] == null ? null : option_r6.specsProducts[0].price, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"search.in\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r6.categoryName);\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_9_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"searchNotFound\"), \" \");\n  }\n}\nfunction SearchComponent_ng_container_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵtemplate(3, SearchComponent_ng_container_0_ng_container_9_div_3_Template, 13, 7, \"div\", 8);\n    i0.ɵɵtemplate(4, SearchComponent_ng_container_0_ng_container_9_p_4_Template, 3, 3, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.searchResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.searchResult && ctx_r3.searchResult.length === 0);\n  }\n}\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction SearchComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"form\", 2)(3, \"input\", 3, 4);\n    i0.ɵɵlistener(\"focusout\", function SearchComponent_ng_container_0_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.closeDropDown());\n    })(\"keyup.enter\", function SearchComponent_ng_container_0_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.searchProduct($event, \"\"));\n    })(\"ngModelChange\", function SearchComponent_ng_container_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.searchChange($event));\n    })(\"ngModelChange\", function SearchComponent_ng_container_0_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.search = $event);\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\")(8, \"em\", 5);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_0_Template_em_mousedown_8_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.searchProduct($event, ctx_r15.search));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, SearchComponent_ng_container_0_ng_container_9_Template, 5, 2, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r0.scConfig ? i0.ɵɵpipeBind1(5, 4, \"search.scSearchOnMoMoMarket\") : i0.ɵɵpipeBind1(6, 6, \"search.searchOnMoMoMarket\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.search)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayResult);\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_ng_container_9_div_3_Template_div_mousedown_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const option_r20 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.NavigateToProduct(option_r20.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"p-divider\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", option_r20.currencyCode, \"\\u00A0\", (option_r20.specsProducts[0] == null ? null : option_r20.specsProducts[0].salePrice) ? option_r20.specsProducts[0] == null ? null : option_r20.specsProducts[0].salePrice : option_r20.specsProducts[0] == null ? null : option_r20.specsProducts[0].price, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"search.in\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r20.categoryName);\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"searchNotFound\"), \" \");\n  }\n}\nfunction SearchComponent_ng_container_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵtemplate(3, SearchComponent_ng_container_1_ng_container_9_div_3_Template, 13, 7, \"div\", 8);\n    i0.ɵɵtemplate(4, SearchComponent_ng_container_1_ng_container_9_p_4_Template, 3, 3, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.searchResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.searchResult && ctx_r17.searchResult.length === 0);\n  }\n}\nfunction SearchComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"form\", 2)(3, \"input\", 3, 4);\n    i0.ɵɵlistener(\"focusout\", function SearchComponent_ng_container_1_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.closeDropDown());\n    })(\"keyup.enter\", function SearchComponent_ng_container_1_Template_input_keyup_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.searchProduct($event, \"\"));\n    })(\"ngModelChange\", function SearchComponent_ng_container_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.searchChange($event));\n    })(\"ngModelChange\", function SearchComponent_ng_container_1_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.search = $event);\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\")(8, \"em\", 5);\n    i0.ɵɵlistener(\"mousedown\", function SearchComponent_ng_container_1_Template_em_mousedown_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.searchProduct($event, ctx_r29.search));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, SearchComponent_ng_container_1_ng_container_9_Template, 5, 2, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", ctx_r1.scConfig ? i0.ɵɵpipeBind1(5, 4, \"search.scSearchOnMoMoMarket\") : i0.ɵɵpipeBind1(6, 6, \"search.searchOnMoMoMarket\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.search)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.displayResult);\n  }\n}\nexport class SearchComponent {\n  onDocumentClick(event) {\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\n    if (!clickedInside) {\n      this.showResultPopUp(false);\n    }\n  }\n  constructor(store, productService, messageService, cd, elementRef, permissionService, translate, router, $gaService) {\n    this.store = store;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.cd = cd;\n    this.elementRef = elementRef;\n    this.permissionService = permissionService;\n    this.translate = translate;\n    this.router = router;\n    this.$gaService = $gaService;\n    this.search = '';\n    this.displayResult = false;\n    this.searchResult = [];\n    this.productId = 0;\n    this.scConfig = false;\n    this.isMobileTemplate = false;\n    this.isGoogleAnalytics = false;\n    this.onResult = new EventEmitter();\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    router.events.subscribe(event => {\n      if (event.navigationTrigger === 'popstate' || event.navigationTrigger === 'imperative' && !event.url?.includes('search')) {\n        this.search = null;\n      }\n    });\n    if (environment.isStoreCloud) {\n      this.scConfig = true;\n    } else {\n      this.scConfig = false;\n    }\n    this.router.routeReuseStrategy.shouldReuseRoute = function () {\n      return false;\n    };\n  }\n  ngOnInit() {\n    /**/\n  }\n  ngAfterViewInit() {\n    this.keyupSearch();\n    this.store.subscription('search').subscribe(res => {\n      this.search = res;\n    });\n  }\n  searchProduct(event, searchKay) {\n    if (event?.target?.value) {\n      this.closeDropDown();\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: event?.target?.value\n        }\n      });\n    }\n  }\n  showResultPopUp(show) {\n    this.displayResult = show;\n  }\n  NavigateToProduct(productId) {\n    this.router.navigate(['product/' + productId + '/' + '1'], {\n      queryParams: {\n        tenantId: localStorage.getItem(\"tenantId\"),\n        lang: localStorage.getItem(\"lang\")\n      },\n      queryParamsHandling: 'merge'\n    });\n    this.showResultPopUp(false);\n  }\n  closeDropDown() {\n    this.showResultPopUp(false);\n  }\n  searchChange(event) {\n    const regex = /^[^a-zA-Z0-9]+/;\n    this.search = event.replace(regex, '');\n    this.cd.detectChanges();\n    if (this.search === '') {\n      this.search = null;\n    }\n  }\n  // TODO: will be removed after testing the new solution\n  // keyUpSearch(event:any){\n  //   if (event.target.value && event.code != 'Enter') {\n  //     this.productService.FilterWithSearchProductName(event.target.value).subscribe({\n  //       next: (res: any) => {\n  //         if(this.isGoogleAnalytics){\n  //           this.$gaService.event(GaLocalActionEnum.CLICK_ON_SEARCH_BAR, '', 'SEARCH', 1, true, {\n  //           searchKeywords: event?.target?.value\n  //         });\n  //       }\n  //         this.searchResult = res.data.products.records;\n  //         this.showResultPopUp(true);\n  //       },\n  //       error: (err: any) => {\n  //         this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\n  //       },\n  //       complete: () => { }\n  //     });\n  //   }\n  // }\n  keyupSearch() {\n    // if(event.target.value && event.target.value?.length > 2 && event.code != 'Enter'){\n    fromEvent(this.inputField?.nativeElement, 'input').pipe(debounceTime(500),\n    // Wait for 500ms pause in events\n    map(event => event),\n    // Get the input value\n    distinctUntilChanged(),\n    // Only emit if the value has changed\n    switchMap(event => {\n      if (event.target.value && event.target.value?.length > 2 && event.code != 'Enter') {\n        const query = event.target.value;\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_SEARCH_BAR, 'search', 'SEARCH', 1, true, {\n            \"search_term\": query\n          });\n        }\n        return this.productService.FilterWithSearchProductName(query); // Call the API\n      } else {\n        return Promise.resolve([]); // Return an empty array for no query\n      }\n    })).subscribe({\n      next: res => {\n        this.searchResult = res.data.products.records;\n        this.showResultPopUp(true);\n      },\n      error: err => {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      },\n      complete: () => {}\n    });\n  }\n  static #_ = this.ɵfac = function SearchComponent_Factory(t) {\n    return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SearchComponent,\n    selectors: [[\"app-mtn-search\"]],\n    viewQuery: function SearchComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputField = _t.first);\n      }\n    },\n    hostBindings: function SearchComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function SearchComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    outputs: {\n      onResult: \"onResult\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[4, \"ngIf\"], [1, \"product-search\", \"p-inputgroup\"], [\"autocomplete\", \"off\", 2, \"display\", \"contents\"], [\"autocapitalize\", \"off\", \"autocomplete\", \"new-password\", \"autocorrect\", \"off\", \"pInputText\", \"\", \"spellcheck\", \"false\", \"type\", \"text\", 3, \"ngModel\", \"ngModelOptions\", \"placeholder\", \"focusout\", \"keyup.enter\", \"ngModelChange\"], [\"inputField\", \"\"], [1, \"pi\", \"pi-search\", 3, \"mousedown\"], [1, \"search-results\"], [1, \"my-3\"], [\"class\", \"px-2 padding-y-5\", \"style\", \"cursor: pointer\", 3, \"mousedown\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ml-3\", 4, \"ngIf\"], [1, \"px-2\", \"padding-y-5\", 2, \"cursor\", \"pointer\", 3, \"mousedown\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"medium-font\", \"font-size-14\", \"large-name\"], [1, \"medium-font\", \"font-size-14\"], [1, \"d-flex\"], [1, \"prop-color\"], [1, \"main-color\", \"font-size-14\", \"mx-1\", 2, \"color\", \"#004f71\", \"font-weight\", \"bold\"], [1, \"ml-3\"], [1, \"product-search-mobile\", \"p-inputgroup\"]],\n    template: function SearchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, SearchComponent_ng_container_0_Template, 10, 9, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, SearchComponent_ng_container_1_Template, 10, 9, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isMobileTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate);\n      }\n    },\n    dependencies: [i6.Divider, i7.NgForOf, i7.NgIf, i8.NativeElementInjectorDirective, i9.ɵNgNoValidate, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgControlStatusGroup, i9.NgModel, i9.NgForm, i3.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.product-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background: #f5f5f5 0 0 no-repeat padding-box !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059) !important;\\n  border: unset;\\n  padding: 10px 28px;\\n  color: #989898 !important;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n  z-index: 2;\\n  border-radius: 8px;\\n  font-family: var(--regular-font);\\n}\\n.product-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n  background: #f5f5f5 0 0 no-repeat padding-box !important;\\n}\\n.product-search[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::placeholder {\\n  \\n\\n  color: #989898 !important;\\n  font-size: 10px;\\n  font-weight: 700;\\n  opacity: 1;\\n  \\n\\n}\\n.product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n}\\n.product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n.product-search[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n.product-search[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%] {\\n  display: block;\\n  position: absolute;\\n  width: 100%;\\n  height: auto;\\n  top: 39px;\\n  left: 0;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n  background-color: white;\\n  border: 1px solid rgba(151, 151, 151, 0.168627451);\\n  border-radius: 5px;\\n  z-index: 4;\\n}\\n.product-search[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  width: 100%;\\n  height: 100%;\\n  top: 0;\\n  left: 0;\\n  background-color: rgba(0, 0, 0, 0.5019607843);\\n  opacity: 0.4;\\n}\\n.product-search[_ngcontent-%COMP%]   .prop-color[_ngcontent-%COMP%] {\\n  color: #a3a3a3;\\n}\\n\\n.product-search-mobile[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background: #f5f5f5 0 0 no-repeat padding-box !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059) !important;\\n  border: unset;\\n  padding: 10px 28px;\\n  color: #989898 !important;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n  z-index: 2;\\n  border-radius: 8px;\\n  font-family: var(--regular-font);\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n  background: #f5f5f5 0 0 no-repeat padding-box !important;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::placeholder {\\n  \\n\\n  color: #989898 !important;\\n  font-size: 10px;\\n  font-weight: 700;\\n  opacity: 1;\\n  \\n\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .color[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%] {\\n  display: block;\\n  position: absolute;\\n  width: 100%;\\n  height: auto;\\n  top: 39px;\\n  left: 0;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n  background-color: white;\\n  border: 1px solid rgba(151, 151, 151, 0.168627451);\\n  border-radius: 5px;\\n  z-index: 4;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  width: 100%;\\n  height: 100%;\\n  top: 0;\\n  left: 0;\\n  background-color: rgba(0, 0, 0, 0.5019607843);\\n  opacity: 0.4;\\n}\\n.product-search-mobile[_ngcontent-%COMP%]   .prop-color[_ngcontent-%COMP%] {\\n  color: #a3a3a3;\\n}\\n\\n.large-name[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  width: 76%;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  em[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .product-search-mobile[_ngcontent-%COMP%] {\\n    z-index: auto !important;\\n    height: 52px;\\n    width: 100% !important;\\n    border-radius: 25px;\\n  }\\n  .search-results[_ngcontent-%COMP%] {\\n    top: 51px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvc2Nzcy9jb21tb24uc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvc2VhcmNoL3NlYXJjaC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFpQ0E7RUFDRSxpQkF4QmM7QUNSaEI7O0FEbUNBO0VBQ0UsaUJBNUJjO0FDSmhCOztBRGtDQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsMENBQUE7QUMvQkY7O0FBWEE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSx3REFBQTtFQUNBLDREQUFBO0VBQ0EsYUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsVUFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0NBQUE7QUFjRjtBQVpFO0VBQ0UsYUFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLHdEQUFBO0FBY0o7QUFYRTtFQUNFLHlDQUFBO0VBQ0EseUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxVQUFBO0VBQ0EsWUFBQTtBQWFKO0FBVkU7RUFDRSx5QkFBQTtBQVlKO0FBVkk7RUFDRSxZQUFBO0FBWU47QUFUSTtFQUNFLFlBQUE7QUFXTjtBQVBFO0VBQ0UsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLGtEQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBU0o7QUFORTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsNkNBQUE7RUFDQSxZQUFBO0FBUUo7QUFKRTtFQUNFLGNBQUE7QUFNSjs7QUFIQTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLHdEQUFBO0VBQ0EsNERBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxVQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQ0FBQTtBQU1GO0FBSkU7RUFDRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxRQUFBO0VBQ0Esd0RBQUE7QUFNSjtBQUZFO0VBQ0UseUNBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0FBSUo7QUFERTtFQUNFLHlCQUFBO0FBR0o7QUFESTtFQUNFLFlBQUE7QUFHTjtBQUFJO0VBQ0UsWUFBQTtBQUVOO0FBRUU7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0RBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7QUFBSjtBQUdFO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSw2Q0FBQTtFQUNBLFlBQUE7QUFESjtBQUtFO0VBQ0UsY0FBQTtBQUhKOztBQVNBO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsVUFBQTtBQU5GOztBRHJJSTtFQzhJRjtJQUNFLGFBQUE7RUFMRjtFQU9BO0lBQ0Usd0JBQUE7SUFDQSxZQUFBO0lBQ0Esc0JBQUE7SUFDQSxtQkFBQTtFQUxGO0VBT0E7SUFDRSxvQkFBQTtFQUxGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIkeWVsbG93QnV0dG9uQ29sb3I6ICNGRkNDMDA7XHJcbiRibHVlQnV0dG9uQ29sb3I6ICMwMDRGNzE7XHJcbiRmb3JtRmllbGRPdXRMaW5lOiAjRjVGNUY1O1xyXG4kaGludE1lc3NhZ2VDb2xvcjogI0EzQTNBMztcclxuJHdoaXRlQ29sb3I6ICNmZmY7XHJcbiRibGFja0NvbG9yOiAjMDAwO1xyXG4kZm9udC1tYWluLWJvbGQ6ICdNVE5CcmlnaHRlclNhbnMtQm9sZCc7XHJcbiRmb250LW1haW4tbWVkaXVtOiAnbWFpbi1tZWRpdW0nO1xyXG4kZm9udC1tYWluLXJlZ3VsYXI6ICdtYWluLXJlZ3VsYXInO1xyXG4kZm9udC1tYWluLWxpZ2h0OiAnbWFpbi1saWdodCc7XHJcbiRoZWFkZXJfaGVpZ2h0OiAxMjJweDtcclxuJGhlYWRlcl9oZWlnaHRfd2l0aG91dF9uYXZiYXI6IDczcHg7XHJcblxyXG4kYnJlYWtwb2ludHM6IChcclxuICBtb2JpbGU6IFwib25seSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2N3B4KVwiLFxyXG4gIHRhYmxldDogXCJvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogNzY4cHgpIGFuZCAobWF4LXdpZHRoOiAxMjAwcHgpXCIsXHJcbiAgZGVza3RvcDogXCJvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMTIwMXB4KSBhbmQgKG1heC13aWR0aDogMTcwMHB4KVwiLFxyXG4gIGxhcmdlLWRlc2t0b3A6IFwib25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IDE3MDFweClcIlxyXG4pO1xyXG5cclxuLy8gRGVmaW5lIG1peGluIGZvciBtZWRpYSBxdWVyaWVzXHJcbkBtaXhpbiByZXNwb25zaXZlKCRicmVha3BvaW50KSB7XHJcbiAgQGlmIG1hcC1oYXMta2V5KCRicmVha3BvaW50cywgJGJyZWFrcG9pbnQpIHtcclxuICAgIEBtZWRpYSAje21hcC1nZXQoJGJyZWFrcG9pbnRzLCAkYnJlYWtwb2ludCl9IHtcclxuICAgICAgQGNvbnRlbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBAZWxzZSB7XHJcbiAgICBAZXJyb3IgXCJVbmtub3duIGJyZWFrcG9pbnQ6ICN7JGJyZWFrcG9pbnR9LiBQbGVhc2UgdXNlICdtb2JpbGUnLCAndGFibGV0Jywgb3IgJ2Rlc2t0b3AnLlwiO1xyXG4gIH1cclxufVxyXG5cclxuLmhlYWRlci1zcGFjaW5nIHtcclxuICBtYXJnaW4tdG9wOiAkaGVhZGVyX2hlaWdodDtcclxufVxyXG5cclxuLmhlYWRlci1zcGFjaW5nLW5vdC1uYXZiYXIge1xyXG4gIG1hcmdpbi10b3A6ICRoZWFkZXJfaGVpZ2h0O1xyXG59XHJcbi5kaXNjb3VudC1wcmljZXtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgZm9udC13ZWlnaHQ6NTAwO1xyXG4gIGNvbG9yOiNFMjE4MzYgIWltcG9ydGFudDtcclxuICBmb250LWZhbWlseTogdmFyKC0tbWVkaXVtLWZvbnQpICFpbXBvcnRhbnQ7XHJcbn0iLCJAaW1wb3J0XCIuLi8uLi8uLi8uLi9hc3NldHMvc2Nzcy9jb21tb24uc2Nzc1wiO1xyXG5cclxuLnByb2R1Y3Qtc2VhcmNoIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNSAwIDAgbm8tcmVwZWF0IHBhZGRpbmctYm94ICFpbXBvcnRhbnQ7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNHB4ICMwMDAwMDAwMyAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogdW5zZXQ7XHJcbiAgcGFkZGluZzogMTBweCAyOHB4O1xyXG4gIGNvbG9yOiAjOTg5ODk4ICFpbXBvcnRhbnQ7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGxpbmUtaGVpZ2h0OiBub3JtYWw7XHJcbiAgei1pbmRleDogMjtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgZm9udC1mYW1pbHk6IHZhcigtLXJlZ3VsYXItZm9udCk7XHJcblxyXG4gIGlucHV0IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgIGdhcDogNHB4O1xyXG4gICAgYmFja2dyb3VuZDogI2Y1ZjVmNSAwIDAgbm8tcmVwZWF0IHBhZGRpbmctYm94ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICA6OnBsYWNlaG9sZGVyIHtcclxuICAgIC8qIENocm9tZSwgRmlyZWZveCwgT3BlcmEsIFNhZmFyaSAxMC4xKyAqL1xyXG4gICAgY29sb3I6ICM5ODk4OTggIWltcG9ydGFudDtcclxuICAgIGZvbnQtc2l6ZTogMTBweDtcclxuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgLyogRmlyZWZveCAqL1xyXG4gIH1cclxuXHJcbiAgLmNvbG9yIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XHJcblxyXG4gICAgaSB7XHJcbiAgICAgIGNvbG9yOiBibGFjaztcclxuICAgIH1cclxuXHJcbiAgICBlbSB7XHJcbiAgICAgIGNvbG9yOiBibGFjaztcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5zZWFyY2gtcmVzdWx0cyB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiBhdXRvO1xyXG4gICAgdG9wOiAzOXB4O1xyXG4gICAgbGVmdDogMDtcclxuICAgIG1heC1oZWlnaHQ6IDUwdmg7XHJcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjOTc5Nzk3MmI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1cHg7XHJcbiAgICB6LWluZGV4OiA0O1xyXG4gIH1cclxuXHJcbiAgLm92ZXJsYXkge1xyXG4gICAgcG9zaXRpb246IGZpeGVkO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzAwMDAwMDgwO1xyXG4gICAgb3BhY2l0eTogMC40O1xyXG5cclxuICB9XHJcblxyXG4gIC5wcm9wLWNvbG9yIHtcclxuICAgIGNvbG9yOiAjYTNhM2EzO1xyXG4gIH1cclxufVxyXG4ucHJvZHVjdC1zZWFyY2gtbW9iaWxlIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNSAwIDAgbm8tcmVwZWF0IHBhZGRpbmctYm94ICFpbXBvcnRhbnQ7XHJcbiAgYm94LXNoYWRvdzogMCAycHggNHB4ICMwMDAwMDAwMyAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogdW5zZXQ7XHJcbiAgcGFkZGluZzogMTBweCAyOHB4O1xyXG4gIGNvbG9yOiAjOTg5ODk4ICFpbXBvcnRhbnQ7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGxpbmUtaGVpZ2h0OiBub3JtYWw7XHJcbiAgei1pbmRleDogMjtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgZm9udC1mYW1pbHk6IHZhcigtLXJlZ3VsYXItZm9udCk7XHJcblxyXG4gIGlucHV0IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgIGdhcDogNHB4O1xyXG4gICAgYmFja2dyb3VuZDogI2Y1ZjVmNSAwIDAgbm8tcmVwZWF0IHBhZGRpbmctYm94ICFpbXBvcnRhbnQ7XHJcblxyXG4gIH1cclxuXHJcbiAgOjpwbGFjZWhvbGRlciB7XHJcbiAgICAvKiBDaHJvbWUsIEZpcmVmb3gsIE9wZXJhLCBTYWZhcmkgMTAuMSsgKi9cclxuICAgIGNvbG9yOiAjOTg5ODk4ICFpbXBvcnRhbnQ7XHJcbiAgICBmb250LXNpemU6IDEwcHg7XHJcbiAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgb3BhY2l0eTogMTtcclxuICAgIC8qIEZpcmVmb3ggKi9cclxuICB9XHJcblxyXG4gIC5jb2xvciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1O1xyXG5cclxuICAgIGkge1xyXG4gICAgICBjb2xvcjogYmxhY2s7XHJcbiAgICB9XHJcblxyXG4gICAgZW0ge1xyXG4gICAgICBjb2xvcjogYmxhY2s7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuc2VhcmNoLXJlc3VsdHMge1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGhlaWdodDogYXV0bztcclxuICAgIHRvcDogMzlweDtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICBtYXgtaGVpZ2h0OiA1MHZoO1xyXG4gICAgb3ZlcmZsb3cteTogYXV0bztcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgIzk3OTc5NzJiO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNXB4O1xyXG4gICAgei1pbmRleDogNDtcclxuICB9XHJcblxyXG4gIC5vdmVybGF5IHtcclxuICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgdG9wOiAwO1xyXG4gICAgbGVmdDogMDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDAwMDA4MDtcclxuICAgIG9wYWNpdHk6IDAuNDtcclxuXHJcbiAgfVxyXG5cclxuICAucHJvcC1jb2xvciB7XHJcbiAgICBjb2xvcjogI2EzYTNhMztcclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuLmxhcmdlLW5hbWUge1xyXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcclxuICB3aWR0aDogNzYlO1xyXG59XHJcbkBpbmNsdWRlIHJlc3BvbnNpdmUobW9iaWxlKXtcclxuICBlbXtcclxuICAgIGRpc3BsYXk6bm9uZVxyXG4gIH1cclxuICAucHJvZHVjdC1zZWFyY2gtbW9iaWxle1xyXG4gICAgei1pbmRleDphdXRvICFpbXBvcnRhbnQ7XHJcbiAgICBoZWlnaHQ6NTJweDtcclxuICAgIHdpZHRoOjEwMCUgIWltcG9ydGFudDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDI1cHg7XHJcbiAgfVxyXG4gIC5zZWFyY2gtcmVzdWx0c3tcclxuICAgIHRvcDo1MXB4ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "environment", "GaLocalActionEnum", "debounceTime", "distinctUntilChanged", "fromEvent", "map", "switchMap", "i0", "ɵɵelementStart", "ɵɵlistener", "SearchComponent_ng_container_0_ng_container_9_div_3_Template_div_mousedown_0_listener", "restoredCtx", "ɵɵrestoreView", "_r9", "option_r6", "$implicit", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "NavigateToProduct", "id", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate2", "currencyCode", "specsProducts", "salePrice", "price", "ɵɵpipeBind1", "categoryName", "ɵɵtextInterpolate1", "ɵɵelementContainerStart", "ɵɵtemplate", "SearchComponent_ng_container_0_ng_container_9_div_3_Template", "SearchComponent_ng_container_0_ng_container_9_p_4_Template", "ɵɵelementContainerEnd", "ɵɵproperty", "ctx_r3", "searchResult", "length", "SearchComponent_ng_container_0_Template_input_focusout_3_listener", "_r11", "ctx_r10", "closeDropDown", "SearchComponent_ng_container_0_Template_input_keyup_enter_3_listener", "$event", "ctx_r12", "searchProduct", "SearchComponent_ng_container_0_Template_input_ngModelChange_3_listener", "ctx_r13", "searchChange", "ctx_r14", "search", "SearchComponent_ng_container_0_Template_em_mousedown_8_listener", "ctx_r15", "SearchComponent_ng_container_0_ng_container_9_Template", "ɵɵpropertyInterpolate", "ctx_r0", "scConfig", "ɵɵpureFunction0", "_c1", "displayResult", "SearchComponent_ng_container_1_ng_container_9_div_3_Template_div_mousedown_0_listener", "_r23", "option_r20", "ctx_r22", "SearchComponent_ng_container_1_ng_container_9_div_3_Template", "SearchComponent_ng_container_1_ng_container_9_p_4_Template", "ctx_r17", "SearchComponent_ng_container_1_Template_input_focusout_3_listener", "_r25", "ctx_r24", "SearchComponent_ng_container_1_Template_input_keyup_enter_3_listener", "ctx_r26", "SearchComponent_ng_container_1_Template_input_ngModelChange_3_listener", "ctx_r27", "ctx_r28", "SearchComponent_ng_container_1_Template_em_mousedown_8_listener", "ctx_r29", "SearchComponent_ng_container_1_ng_container_9_Template", "ctx_r1", "SearchComponent", "onDocumentClick", "event", "clickedInside", "elementRef", "nativeElement", "contains", "target", "showResultPopUp", "constructor", "store", "productService", "messageService", "cd", "permissionService", "translate", "router", "$gaService", "productId", "isMobileTemplate", "isGoogleAnalytics", "onResult", "hasPermission", "events", "subscribe", "navigationTrigger", "url", "includes", "isStoreCloud", "routeReuseStrategy", "shouldReuseRoute", "ngOnInit", "ngAfterViewInit", "keyupSearch", "subscription", "res", "searchKay", "value", "navigate", "queryParams", "q", "show", "tenantId", "localStorage", "getItem", "lang", "queryParamsHandling", "regex", "replace", "detectChanges", "inputField", "pipe", "code", "query", "CLICK_ON_SEARCH_BAR", "FilterWithSearchProductName", "Promise", "resolve", "next", "data", "products", "records", "error", "err", "add", "severity", "summary", "instant", "detail", "message", "complete", "_", "ɵɵdirectiveInject", "i1", "StoreService", "ProductService", "i2", "MessageService", "ChangeDetectorRef", "ElementRef", "PermissionService", "i3", "TranslateService", "i4", "Router", "i5", "GoogleAnalyticsService", "_2", "selectors", "viewQuery", "SearchComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "SearchComponent_ng_container_0_Template", "SearchComponent_ng_container_1_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\search\\search.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\search\\search.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  OnInit,\r\n  Output,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport {Router} from \"@angular/router\";\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {MessageService} from 'primeng/api';\r\nimport {environment} from '@environments/environment';\r\nimport {StoreService, ProductService, PermissionService} from \"@core/services\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport { debounceTime, distinctUntilChanged, fromEvent, map, switchMap } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-mtn-search',\r\n  templateUrl: './search.component.html',\r\n  styleUrls: ['./search.component.scss']\r\n})\r\nexport class SearchComponent implements OnInit, AfterViewInit {\r\n  search: any = '';\r\n  displayResult: boolean = false;\r\n  searchResult: Array<any> = [];\r\n  productId: number = 0;\r\n  scConfig: boolean = false;\r\n  isMobileTemplate:boolean=false;\r\n  isGoogleAnalytics:boolean=false;\r\n  @Output() onResult: EventEmitter<boolean> = new EventEmitter();\r\n  @ViewChild('inputField') inputField:ElementRef;\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const clickedInside = this.elementRef.nativeElement.contains(event.target);\r\n\r\n    if (!clickedInside) {\r\n      this.showResultPopUp(false);\r\n    }\r\n  }\r\n  constructor(\r\n    private store: StoreService, private productService: ProductService,\r\n    private messageService: MessageService, private cd: ChangeDetectorRef,private elementRef: ElementRef,\r\n    private permissionService: PermissionService,\r\n    private translate: TranslateService, private router: Router ,\r\n    private $gaService: GoogleAnalyticsService,\r\n    ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    router.events\r\n      .subscribe((event: any) => {\r\n        if (event.navigationTrigger === 'popstate' || (event.navigationTrigger === 'imperative' && !event.url?.includes('search'))) {\r\n          this.search = null;\r\n        }\r\n      });\r\n    if (environment.isStoreCloud) {\r\n      this.scConfig = true;\r\n    } else {\r\n      this.scConfig = false;\r\n    }\r\n    this.router.routeReuseStrategy.shouldReuseRoute = function () {\r\n      return false;\r\n    };\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n\r\n  ngAfterViewInit(): void {\r\n    this.keyupSearch()\r\n    this.store.subscription('search')\r\n      .subscribe(\r\n        (res: string) => {\r\n          this.search = res;\r\n        }\r\n      );\r\n  }\r\n\r\n  searchProduct(event: any, searchKay?: any): void {\r\n    if (event?.target?.value) {\r\n      this.closeDropDown();\r\n      this.router.navigate(\r\n        ['/search'],\r\n        {queryParams: {q: event?.target?.value}}\r\n      );\r\n    }\r\n\r\n  }\r\n\r\n  showResultPopUp(show: boolean): void {\r\n    this.displayResult = show;\r\n  }\r\n\r\n  NavigateToProduct(productId: any) {\r\n\r\n    this.router.navigate(['product/'+ productId+'/'+('1')],{\r\n      queryParams: {\r\n        tenantId: localStorage.getItem(\"tenantId\"),\r\n        lang:localStorage.getItem(\"lang\")\r\n      },\r\n      queryParamsHandling: 'merge',\r\n    })\r\n    this.showResultPopUp(false);\r\n\r\n  }\r\n\r\n  closeDropDown() {\r\n    this.showResultPopUp(false);\r\n\r\n  }\r\n\r\n  searchChange(event: any) {\r\n    const regex = /^[^a-zA-Z0-9]+/;\r\n\r\n\r\n    this.search = event.replace(regex, '');\r\n    this.cd.detectChanges();\r\n    if (this.search === '') {\r\n      this.search = null\r\n    }\r\n\r\n\r\n  }\r\n  // TODO: will be removed after testing the new solution\r\n\r\n  // keyUpSearch(event:any){\r\n\r\n  //   if (event.target.value && event.code != 'Enter') {\r\n\r\n  //     this.productService.FilterWithSearchProductName(event.target.value).subscribe({\r\n  //       next: (res: any) => {\r\n  //         if(this.isGoogleAnalytics){\r\n  //           this.$gaService.event(GaLocalActionEnum.CLICK_ON_SEARCH_BAR, '', 'SEARCH', 1, true, {\r\n  //           searchKeywords: event?.target?.value\r\n  //         });\r\n  //       }\r\n  //         this.searchResult = res.data.products.records;\r\n  //         this.showResultPopUp(true);\r\n  //       },\r\n  //       error: (err: any) => {\r\n  //         this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\r\n  //       },\r\n  //       complete: () => { }\r\n  //     });\r\n  //   }\r\n  // }\r\n\r\n  keyupSearch(){\r\n    // if(event.target.value && event.target.value?.length > 2 && event.code != 'Enter'){\r\n      fromEvent(this.inputField?.nativeElement, 'input').pipe(\r\n        debounceTime(500), // Wait for 500ms pause in events\r\n        map((event:any) => event), // Get the input value\r\n        distinctUntilChanged(), // Only emit if the value has changed\r\n        switchMap(event => {\r\n            if (event.target.value && event.target.value?.length > 2 && event.code != 'Enter') {\r\n              const query =event.target.value\r\n              if(this.isGoogleAnalytics){\r\n                this.$gaService.event(GaLocalActionEnum.CLICK_ON_SEARCH_BAR, 'search', 'SEARCH', 1, true, {\r\n                \"search_term\": query\r\n              });\r\n            }\r\n                return this.productService.FilterWithSearchProductName(query); // Call the API\r\n            } else {\r\n                return Promise.resolve([]); // Return an empty array for no query\r\n            }\r\n        })\r\n    ).subscribe({\r\n      next: (res: any) => {\r\n     \r\n        this.searchResult = res.data.products.records;\r\n        this.showResultPopUp(true);\r\n      },\r\n      error: (err: any) => {\r\n        this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\r\n      },\r\n      complete: () => { }\r\n    });\r\n    }\r\n  \r\n  // }\r\n}\r\n", "<ng-container *ngIf=\"!isMobileTemplate\" >\r\n<div class=\"product-search p-inputgroup\">\r\n\r\n  <form autocomplete=\"off\" style=\"display: contents\">\r\n    <input #inputField (focusout)=\"closeDropDown()\"\r\n           (keyup.enter)=\"searchProduct($event,'')\" (ngModelChange)=\"searchChange($event)\" [(ngModel)]=\"search\"\r\n           [ngModelOptions]=\"{ standalone: true }\" autocapitalize=\"off\" autocomplete=\"new-password\" autocorrect=\"off\"\r\n           pInputText placeholder=\"{{\r\n        scConfig\r\n          ? ('search.scSearchOnMoMoMarket' | translate)\r\n          : ('search.searchOnMoMoMarket' | translate)\r\n      }}\" spellcheck=\"false\" type=\"text\" />\r\n    <span>\r\n      <em (mousedown)=\"searchProduct($event,search)\" class=\"pi pi-search\"></em>\r\n    </span>\r\n  </form>\r\n\r\n  <ng-container *ngIf=\"displayResult\">\r\n    <div class=\"search-results\">\r\n      <div class=\"my-3\"></div>\r\n      <div (mousedown)=\"NavigateToProduct(option.id)\" *ngFor=\"let option of searchResult; index as i\"\r\n           class=\"px-2 padding-y-5\" style=\"cursor: pointer\">\r\n        <div class=\"d-flex justify-content-between\">\r\n          <span class=\"medium-font font-size-14 large-name\">{{ option.name }}</span>\r\n          <span\r\n            class=\"medium-font font-size-14\">{{option.currencyCode}}&nbsp;{{option.specsProducts[0]?.salePrice?option.specsProducts[0]?.salePrice:option.specsProducts[0]?.price}}</span>\r\n        </div>\r\n        <div class=\"d-flex\">\r\n          <span class=\"prop-color\">{{ \"search.in\" | translate }}</span>\r\n          <span class=\"main-color font-size-14 mx-1\" style=\"color: #004f71; font-weight: bold\">{{ option.categoryName\r\n            }}</span>\r\n        </div>\r\n        <p-divider></p-divider>\r\n      </div>\r\n      <p *ngIf=\"searchResult && searchResult.length === 0\" class=\"ml-3\">\r\n        {{ \"searchNotFound\" | translate }}\r\n      </p>\r\n    </div>\r\n  </ng-container>\r\n</div>\r\n</ng-container>\r\n<ng-container *ngIf=\"isMobileTemplate\" >\r\n  <div class=\"product-search-mobile p-inputgroup\">\r\n\r\n    <form autocomplete=\"off\" style=\"display: contents\">\r\n      <input #inputField (focusout)=\"closeDropDown()\"\r\n             (keyup.enter)=\"searchProduct($event,'')\" (ngModelChange)=\"searchChange($event)\" [(ngModel)]=\"search\"\r\n             [ngModelOptions]=\"{ standalone: true }\" autocapitalize=\"off\" autocomplete=\"new-password\" autocorrect=\"off\"\r\n             pInputText placeholder=\"{{\r\n        scConfig\r\n          ? ('search.scSearchOnMoMoMarket' | translate)\r\n          : ('search.searchOnMoMoMarket' | translate)\r\n      }}\" spellcheck=\"false\" type=\"text\" />\r\n      <span>\r\n      <em (mousedown)=\"searchProduct($event,search)\" class=\"pi pi-search\"></em>\r\n    </span>\r\n    </form>\r\n\r\n    <ng-container *ngIf=\"displayResult\">\r\n      <div class=\"search-results\">\r\n        <div class=\"my-3\"></div>\r\n        <div (mousedown)=\"NavigateToProduct(option.id)\" *ngFor=\"let option of searchResult; index as i\"\r\n             class=\"px-2 padding-y-5\" style=\"cursor: pointer\">\r\n          <div class=\"d-flex justify-content-between\">\r\n            <span class=\"medium-font font-size-14 large-name\">{{ option.name }}</span>\r\n            <span\r\n              class=\"medium-font font-size-14\">{{option.currencyCode}}&nbsp;{{option.specsProducts[0]?.salePrice?option.specsProducts[0]?.salePrice:option.specsProducts[0]?.price}}</span>\r\n          </div>\r\n          <div class=\"d-flex\">\r\n            <span class=\"prop-color\">{{ \"search.in\" | translate }}</span>\r\n            <span class=\"main-color font-size-14 mx-1\" style=\"color: #004f71; font-weight: bold\">{{ option.categoryName\r\n              }}</span>\r\n          </div>\r\n          <p-divider></p-divider>\r\n        </div>\r\n        <p *ngIf=\"searchResult && searchResult.length === 0\" class=\"ml-3\">\r\n          {{ \"searchNotFound\" | translate }}\r\n        </p>\r\n      </div>\r\n    </ng-container>\r\n  </div>\r\n</ng-container>\r\n"], "mappings": "AAAA,SAKEA,YAAY,QAKP,eAAe;AAItB,SAAQC,WAAW,QAAO,2BAA2B;AAErD,SAAQC,iBAAiB,QAAO,kCAAkC;AAElE,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICE9EC,EAAA,CAAAC,cAAA,cACsD;IADjDD,EAAA,CAAAE,UAAA,uBAAAC,sFAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,iBAAA,CAAAL,SAAA,CAAAM,EAAA,CAA4B;IAAA,EAAC;IAE7Cb,EAAA,CAAAC,cAAA,cAA4C;IACQD,EAAA,CAAAc,MAAA,GAAiB;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAC1Ef,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAc,MAAA,GAAqI;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAEjLf,EAAA,CAAAC,cAAA,cAAoB;IACOD,EAAA,CAAAc,MAAA,GAA6B;;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAC7Df,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAc,MAAA,IACjF;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAEbf,EAAA,CAAAgB,SAAA,iBAAuB;IACzBhB,EAAA,CAAAe,YAAA,EAAM;;;;IAVgDf,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAY,IAAA,CAAiB;IAEhCnB,EAAA,CAAAiB,SAAA,GAAqI;IAArIjB,EAAA,CAAAoB,kBAAA,KAAAb,SAAA,CAAAc,YAAA,aAAAd,SAAA,CAAAe,aAAA,qBAAAf,SAAA,CAAAe,aAAA,IAAAC,SAAA,IAAAhB,SAAA,CAAAe,aAAA,qBAAAf,SAAA,CAAAe,aAAA,IAAAC,SAAA,GAAAhB,SAAA,CAAAe,aAAA,qBAAAf,SAAA,CAAAe,aAAA,IAAAE,KAAA,KAAqI;IAG/IxB,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAyB,WAAA,oBAA6B;IAC+BzB,EAAA,CAAAiB,SAAA,GACjF;IADiFjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAmB,YAAA,CACjF;;;;;IAIR1B,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAc,MAAA,GACF;;IAAAd,EAAA,CAAAe,YAAA,EAAI;;;IADFf,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAAyB,WAAA,8BACF;;;;;IAnBJzB,EAAA,CAAA4B,uBAAA,GAAoC;IAClC5B,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAgB,SAAA,aAAwB;IACxBhB,EAAA,CAAA6B,UAAA,IAAAC,4DAAA,kBAaM;IACN9B,EAAA,CAAA6B,UAAA,IAAAE,0DAAA,eAEI;IACN/B,EAAA,CAAAe,YAAA,EAAM;IACRf,EAAA,CAAAgC,qBAAA,EAAe;;;;IAlBwDhC,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAiC,UAAA,YAAAC,MAAA,CAAAC,YAAA,CAAiB;IAchFnC,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAAiC,UAAA,SAAAC,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAC,YAAA,CAAAC,MAAA,OAA+C;;;;;;;;;;;IAlCzDpC,EAAA,CAAA4B,uBAAA,GAAyC;IACzC5B,EAAA,CAAAC,cAAA,aAAyC;IAGlBD,EAAA,CAAAE,UAAA,sBAAAmC,kEAAA;MAAArC,EAAA,CAAAK,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAU,aAAA;MAAA,OAAYV,EAAA,CAAAW,WAAA,CAAA4B,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC,yBAAAC,qEAAAC,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAAiC,IAAA;MAAA,MAAAK,OAAA,GAAA3C,EAAA,CAAAU,aAAA;MAAA,OACzBV,EAAA,CAAAW,WAAA,CAAAgC,OAAA,CAAAC,aAAA,CAAAF,MAAA,EAAqB,EAAE,CAAC;IAAA,EADC,2BAAAG,uEAAAH,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAAiC,IAAA;MAAA,MAAAQ,OAAA,GAAA9C,EAAA,CAAAU,aAAA;MAAA,OACkBV,EAAA,CAAAW,WAAA,CAAAmC,OAAA,CAAAC,YAAA,CAAAL,MAAA,CAAoB;IAAA,EADtC,2BAAAG,uEAAAH,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAAiC,IAAA;MAAA,MAAAU,OAAA,GAAAhD,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAqC,OAAA,CAAAC,MAAA,GAAAP,MAAA;IAAA;;;IAA/C1C,EAAA,CAAAe,YAAA,EAOuC;IACvCf,EAAA,CAAAC,cAAA,WAAM;IACAD,EAAA,CAAAE,UAAA,uBAAAgD,gEAAAR,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAAiC,IAAA;MAAA,MAAAa,OAAA,GAAAnD,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAwC,OAAA,CAAAP,aAAA,CAAAF,MAAA,EAAAS,OAAA,CAAAF,MAAA,CAA4B;IAAA,EAAC;IAAsBjD,EAAA,CAAAe,YAAA,EAAK;IAI7Ef,EAAA,CAAA6B,UAAA,IAAAuB,sDAAA,0BAqBe;IACjBpD,EAAA,CAAAe,YAAA,EAAM;IACNf,EAAA,CAAAgC,qBAAA,EAAe;;;;IAjCOhC,EAAA,CAAAiB,SAAA,GAIb;IAJajB,EAAA,CAAAqD,qBAAA,gBAAAC,MAAA,CAAAC,QAAA,GAAAvD,EAAA,CAAAyB,WAAA,wCAAAzB,EAAA,CAAAyB,WAAA,oCAIb;IANkFzB,EAAA,CAAAiC,UAAA,YAAAqB,MAAA,CAAAL,MAAA,CAAoB,mBAAAjD,EAAA,CAAAwD,eAAA,IAAAC,GAAA;IAY9FzD,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAiC,UAAA,SAAAqB,MAAA,CAAAI,aAAA,CAAmB;;;;;;IA4C5B1D,EAAA,CAAAC,cAAA,cACsD;IADjDD,EAAA,CAAAE,UAAA,uBAAAyD,sFAAA;MAAA,MAAAvD,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAuD,IAAA;MAAA,MAAAC,UAAA,GAAAzD,WAAA,CAAAI,SAAA;MAAA,MAAAsD,OAAA,GAAA9D,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAmD,OAAA,CAAAlD,iBAAA,CAAAiD,UAAA,CAAAhD,EAAA,CAA4B;IAAA,EAAC;IAE7Cb,EAAA,CAAAC,cAAA,cAA4C;IACQD,EAAA,CAAAc,MAAA,GAAiB;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAC1Ef,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAc,MAAA,GAAqI;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAEjLf,EAAA,CAAAC,cAAA,cAAoB;IACOD,EAAA,CAAAc,MAAA,GAA6B;;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAC7Df,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAc,MAAA,IACjF;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAEbf,EAAA,CAAAgB,SAAA,iBAAuB;IACzBhB,EAAA,CAAAe,YAAA,EAAM;;;;IAVgDf,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,iBAAA,CAAA2C,UAAA,CAAA1C,IAAA,CAAiB;IAEhCnB,EAAA,CAAAiB,SAAA,GAAqI;IAArIjB,EAAA,CAAAoB,kBAAA,KAAAyC,UAAA,CAAAxC,YAAA,aAAAwC,UAAA,CAAAvC,aAAA,qBAAAuC,UAAA,CAAAvC,aAAA,IAAAC,SAAA,IAAAsC,UAAA,CAAAvC,aAAA,qBAAAuC,UAAA,CAAAvC,aAAA,IAAAC,SAAA,GAAAsC,UAAA,CAAAvC,aAAA,qBAAAuC,UAAA,CAAAvC,aAAA,IAAAE,KAAA,KAAqI;IAG/IxB,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAyB,WAAA,oBAA6B;IAC+BzB,EAAA,CAAAiB,SAAA,GACjF;IADiFjB,EAAA,CAAAkB,iBAAA,CAAA2C,UAAA,CAAAnC,YAAA,CACjF;;;;;IAIR1B,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAc,MAAA,GACF;;IAAAd,EAAA,CAAAe,YAAA,EAAI;;;IADFf,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAAyB,WAAA,8BACF;;;;;IAnBJzB,EAAA,CAAA4B,uBAAA,GAAoC;IAClC5B,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAgB,SAAA,aAAwB;IACxBhB,EAAA,CAAA6B,UAAA,IAAAkC,4DAAA,kBAaM;IACN/D,EAAA,CAAA6B,UAAA,IAAAmC,0DAAA,eAEI;IACNhE,EAAA,CAAAe,YAAA,EAAM;IACRf,EAAA,CAAAgC,qBAAA,EAAe;;;;IAlBwDhC,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAiC,UAAA,YAAAgC,OAAA,CAAA9B,YAAA,CAAiB;IAchFnC,EAAA,CAAAiB,SAAA,GAA+C;IAA/CjB,EAAA,CAAAiC,UAAA,SAAAgC,OAAA,CAAA9B,YAAA,IAAA8B,OAAA,CAAA9B,YAAA,CAAAC,MAAA,OAA+C;;;;;;IAlC3DpC,EAAA,CAAA4B,uBAAA,GAAwC;IACtC5B,EAAA,CAAAC,cAAA,cAAgD;IAGzBD,EAAA,CAAAE,UAAA,sBAAAgE,kEAAA;MAAAlE,EAAA,CAAAK,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAU,aAAA;MAAA,OAAYV,EAAA,CAAAW,WAAA,CAAAyD,OAAA,CAAA5B,aAAA,EAAe;IAAA,EAAC,yBAAA6B,qEAAA3B,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAA8D,IAAA;MAAA,MAAAG,OAAA,GAAAtE,EAAA,CAAAU,aAAA;MAAA,OACzBV,EAAA,CAAAW,WAAA,CAAA2D,OAAA,CAAA1B,aAAA,CAAAF,MAAA,EAAqB,EAAE,CAAC;IAAA,EADC,2BAAA6B,uEAAA7B,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAA8D,IAAA;MAAA,MAAAK,OAAA,GAAAxE,EAAA,CAAAU,aAAA;MAAA,OACkBV,EAAA,CAAAW,WAAA,CAAA6D,OAAA,CAAAzB,YAAA,CAAAL,MAAA,CAAoB;IAAA,EADtC,2BAAA6B,uEAAA7B,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAA8D,IAAA;MAAA,MAAAM,OAAA,GAAAzE,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAA8D,OAAA,CAAAxB,MAAA,GAAAP,MAAA;IAAA;;;IAA/C1C,EAAA,CAAAe,YAAA,EAOqC;IACrCf,EAAA,CAAAC,cAAA,WAAM;IACFD,EAAA,CAAAE,UAAA,uBAAAwE,gEAAAhC,MAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAA8D,IAAA;MAAA,MAAAQ,OAAA,GAAA3E,EAAA,CAAAU,aAAA;MAAA,OAAaV,EAAA,CAAAW,WAAA,CAAAgE,OAAA,CAAA/B,aAAA,CAAAF,MAAA,EAAAiC,OAAA,CAAA1B,MAAA,CAA4B;IAAA,EAAC;IAAsBjD,EAAA,CAAAe,YAAA,EAAK;IAI3Ef,EAAA,CAAA6B,UAAA,IAAA+C,sDAAA,0BAqBe;IACjB5E,EAAA,CAAAe,YAAA,EAAM;IACRf,EAAA,CAAAgC,qBAAA,EAAe;;;;IAjCShC,EAAA,CAAAiB,SAAA,GAIf;IAJejB,EAAA,CAAAqD,qBAAA,gBAAAwB,MAAA,CAAAtB,QAAA,GAAAvD,EAAA,CAAAyB,WAAA,wCAAAzB,EAAA,CAAAyB,WAAA,oCAIf;IANoFzB,EAAA,CAAAiC,UAAA,YAAA4C,MAAA,CAAA5B,MAAA,CAAoB,mBAAAjD,EAAA,CAAAwD,eAAA,IAAAC,GAAA;IAY9FzD,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAiC,UAAA,SAAA4C,MAAA,CAAAnB,aAAA,CAAmB;;;ADjCtC,OAAM,MAAOoB,eAAe;EAW1BC,eAAeA,CAACC,KAAY;IAC1B,MAAMC,aAAa,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,QAAQ,CAACJ,KAAK,CAACK,MAAM,CAAC;IAE1E,IAAI,CAACJ,aAAa,EAAE;MAClB,IAAI,CAACK,eAAe,CAAC,KAAK,CAAC;;EAE/B;EACAC,YACUC,KAAmB,EAAUC,cAA8B,EAC3DC,cAA8B,EAAUC,EAAqB,EAAST,UAAsB,EAC5FU,iBAAoC,EACpCC,SAA2B,EAAUC,MAAc,EACnDC,UAAkC;IAJlC,KAAAP,KAAK,GAALA,KAAK;IAAwB,KAAAC,cAAc,GAAdA,cAAc;IAC3C,KAAAC,cAAc,GAAdA,cAAc;IAA0B,KAAAC,EAAE,GAAFA,EAAE;IAA4B,KAAAT,UAAU,GAAVA,UAAU;IAChF,KAAAU,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,SAAS,GAATA,SAAS;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAC3C,KAAAC,UAAU,GAAVA,UAAU;IAtBpB,KAAA9C,MAAM,GAAQ,EAAE;IAChB,KAAAS,aAAa,GAAY,KAAK;IAC9B,KAAAvB,YAAY,GAAe,EAAE;IAC7B,KAAA6D,SAAS,GAAW,CAAC;IACrB,KAAAzC,QAAQ,GAAY,KAAK;IACzB,KAAA0C,gBAAgB,GAAS,KAAK;IAC9B,KAAAC,iBAAiB,GAAS,KAAK;IACrB,KAAAC,QAAQ,GAA0B,IAAI3G,YAAY,EAAE;IAiB5D,IAAI,CAACyG,gBAAgB,GAAG,IAAI,CAACL,iBAAiB,CAACQ,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACN,iBAAiB,CAACQ,aAAa,CAAC,kBAAkB,CAAC;IAEjFN,MAAM,CAACO,MAAM,CACVC,SAAS,CAAEtB,KAAU,IAAI;MACxB,IAAIA,KAAK,CAACuB,iBAAiB,KAAK,UAAU,IAAKvB,KAAK,CAACuB,iBAAiB,KAAK,YAAY,IAAI,CAACvB,KAAK,CAACwB,GAAG,EAAEC,QAAQ,CAAC,QAAQ,CAAE,EAAE;QAC1H,IAAI,CAACxD,MAAM,GAAG,IAAI;;IAEtB,CAAC,CAAC;IACJ,IAAIxD,WAAW,CAACiH,YAAY,EAAE;MAC5B,IAAI,CAACnD,QAAQ,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,KAAK;;IAEvB,IAAI,CAACuC,MAAM,CAACa,kBAAkB,CAACC,gBAAgB,GAAG;MAChD,OAAO,KAAK;IACd,CAAC;EACH;EAEAC,QAAQA,CAAA;IACN;EAAA;EAIFC,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACvB,KAAK,CAACwB,YAAY,CAAC,QAAQ,CAAC,CAC9BV,SAAS,CACPW,GAAW,IAAI;MACd,IAAI,CAAChE,MAAM,GAAGgE,GAAG;IACnB,CAAC,CACF;EACL;EAEArE,aAAaA,CAACoC,KAAU,EAAEkC,SAAe;IACvC,IAAIlC,KAAK,EAAEK,MAAM,EAAE8B,KAAK,EAAE;MACxB,IAAI,CAAC3E,aAAa,EAAE;MACpB,IAAI,CAACsD,MAAM,CAACsB,QAAQ,CAClB,CAAC,SAAS,CAAC,EACX;QAACC,WAAW,EAAE;UAACC,CAAC,EAAEtC,KAAK,EAAEK,MAAM,EAAE8B;QAAK;MAAC,CAAC,CACzC;;EAGL;EAEA7B,eAAeA,CAACiC,IAAa;IAC3B,IAAI,CAAC7D,aAAa,GAAG6D,IAAI;EAC3B;EAEA3G,iBAAiBA,CAACoF,SAAc;IAE9B,IAAI,CAACF,MAAM,CAACsB,QAAQ,CAAC,CAAC,UAAU,GAAEpB,SAAS,GAAC,GAAG,GAAE,GAAI,CAAC,EAAC;MACrDqB,WAAW,EAAE;QACXG,QAAQ,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAC1CC,IAAI,EAACF,YAAY,CAACC,OAAO,CAAC,MAAM;OACjC;MACDE,mBAAmB,EAAE;KACtB,CAAC;IACF,IAAI,CAACtC,eAAe,CAAC,KAAK,CAAC;EAE7B;EAEA9C,aAAaA,CAAA;IACX,IAAI,CAAC8C,eAAe,CAAC,KAAK,CAAC;EAE7B;EAEAvC,YAAYA,CAACiC,KAAU;IACrB,MAAM6C,KAAK,GAAG,gBAAgB;IAG9B,IAAI,CAAC5E,MAAM,GAAG+B,KAAK,CAAC8C,OAAO,CAACD,KAAK,EAAE,EAAE,CAAC;IACtC,IAAI,CAAClC,EAAE,CAACoC,aAAa,EAAE;IACvB,IAAI,IAAI,CAAC9E,MAAM,KAAK,EAAE,EAAE;MACtB,IAAI,CAACA,MAAM,GAAG,IAAI;;EAItB;EACA;EAEA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA8D,WAAWA,CAAA;IACT;IACElH,SAAS,CAAC,IAAI,CAACmI,UAAU,EAAE7C,aAAa,EAAE,OAAO,CAAC,CAAC8C,IAAI,CACrDtI,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBG,GAAG,CAAEkF,KAAS,IAAKA,KAAK,CAAC;IAAE;IAC3BpF,oBAAoB,EAAE;IAAE;IACxBG,SAAS,CAACiF,KAAK,IAAG;MACd,IAAIA,KAAK,CAACK,MAAM,CAAC8B,KAAK,IAAInC,KAAK,CAACK,MAAM,CAAC8B,KAAK,EAAE/E,MAAM,GAAG,CAAC,IAAI4C,KAAK,CAACkD,IAAI,IAAI,OAAO,EAAE;QACjF,MAAMC,KAAK,GAAEnD,KAAK,CAACK,MAAM,CAAC8B,KAAK;QAC/B,IAAG,IAAI,CAACjB,iBAAiB,EAAC;UACxB,IAAI,CAACH,UAAU,CAACf,KAAK,CAACtF,iBAAiB,CAAC0I,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE;YAC1F,aAAa,EAAED;WAChB,CAAC;;QAEA,OAAO,IAAI,CAAC1C,cAAc,CAAC4C,2BAA2B,CAACF,KAAK,CAAC,CAAC,CAAC;OAClE,MAAM;QACH,OAAOG,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEpC,CAAC,CAAC,CACL,CAACjC,SAAS,CAAC;MACVkC,IAAI,EAAGvB,GAAQ,IAAI;QAEjB,IAAI,CAAC9E,YAAY,GAAG8E,GAAG,CAACwB,IAAI,CAACC,QAAQ,CAACC,OAAO;QAC7C,IAAI,CAACrD,eAAe,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDsD,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACnD,cAAc,CAACoD,GAAG,CAAC;UAAEC,QAAQ,EAAE,OAAO;UAAEC,OAAO,EAAE,IAAI,CAACnD,SAAS,CAACoD,OAAO,CAAC,0BAA0B,CAAC;UAAEC,MAAM,EAAEL,GAAG,CAACM;QAAO,CAAE,CAAC;MAClI,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK,CAAG;KACnB,CAAC;EACF;EAAC,QAAAC,CAAA,G;qBA9JQvE,eAAe,EAAA9E,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAzJ,EAAA,CAAAsJ,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAtJ,EAAA,CAAA4J,iBAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAtJ,EAAA,CAAA6J,UAAA,GAAA7J,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAO,iBAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhK,EAAA,CAAAsJ,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAlK,EAAA,CAAAsJ,iBAAA,CAAAa,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAfvF,eAAe;IAAAwF,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;iBAAfC,GAAA,CAAA3F,eAAA,CAAArC,MAAA,CAAuB;QAAA,UAAA1C,EAAA,CAAA2K,iBAAA;;;;;;;;;;;QCzBpC3K,EAAA,CAAA6B,UAAA,IAAA+I,uCAAA,2BAwCe;QACf5K,EAAA,CAAA6B,UAAA,IAAAgJ,uCAAA,2BAwCe;;;QAjFA7K,EAAA,CAAAiC,UAAA,UAAAyI,GAAA,CAAAzE,gBAAA,CAAuB;QAyCvBjG,EAAA,CAAAiB,SAAA,GAAsB;QAAtBjB,EAAA,CAAAiC,UAAA,SAAAyI,GAAA,CAAAzE,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}