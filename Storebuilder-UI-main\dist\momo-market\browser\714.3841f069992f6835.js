"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[714],{4714:(T,r,i)=>{i.r(r),i.d(r,{AboutUsModule:()=>y});var s=i(6814),l=i(6075),g=i(4532),d=i(4480),u=i(553),t=i(5879),f=i(864),b=i(6593),h=i(9147),v=i(8562);function x(o,a){if(1&o&&(t.TgZ(0,"div",14)(1,"p",15),t._uU(2),t.qZA()()),2&o){const n=t.oxw(2);t.xp6(2),t.hij(" ",n.aboutUsDetails.title," ")}}function C(o,a){if(1&o&&(t._UZ(0,"p",16),t.<PERSON>o(1,"safeComment")),2&o){const n=t.oxw(2);t.Q6J("innerHtml",t.lcZ(1,1,n.aboutUsDetails.content),t.oJD)}}function U(o,a){if(1&o&&(t.TgZ(0,"div",19),t._UZ(1,"img",20),t.qZA()),2&o){const n=t.oxw(3);t.xp6(1),t.Q6J("src",n.aboutUsDetails.image,t.LSH)}}function P(o,a){if(1&o&&(t.TgZ(0,"div",17),t.YNc(1,U,2,1,"div",18),t.qZA()),2&o){const n=t.oxw(2);t.xp6(1),t.Q6J("ngIf",n.aboutUsDetails.image)}}function M(o,a){if(1&o&&(t.TgZ(0,"section",1)(1,"div",2)(2,"div",3),t._uU(3),t.qZA(),t.TgZ(4,"div",4)(5,"div",5)(6,"p-card",6)(7,"div",7)(8,"div",8),t.YNc(9,x,3,1,"div",9),t.TgZ(10,"div",10),t.YNc(11,C,2,3,"p",11),t.qZA()(),t._UZ(12,"div",12),t.qZA()()(),t.YNc(13,P,2,1,"div",13),t.qZA()()()),2&o){const n=t.oxw();t.xp6(3),t.hij(" ",n.aboutUsDetails.pageTitle," "),t.xp6(2),t.Q6J("ngClass","col-md-10"),t.xp6(4),t.Q6J("ngIf",n.aboutUsDetails.title),t.xp6(2),t.Q6J("ngIf",n.aboutUsDetails.content),t.xp6(2),t.Q6J("ngIf",(null==n.aboutUsDetails?null:n.aboutUsDetails.image)&&(null==n.aboutUsDetails?null:n.aboutUsDetails.showImage))}}const O=[{path:"",component:(()=>{class o{aboutUsService;router;_router;domSanitizer;$gtmService;aboutUsDetails={};pageId=0;title="";isPageVisible=!1;constructor(n,e,c,I,w){this.aboutUsService=n,this.router=e,this._router=c,this.domSanitizer=I,this.$gtmService=w;let p=e.snapshot.queryParamMap.get("pageId"),m=e.snapshot.queryParamMap.get("title");p&&(this.pageId=parseInt(p)),m&&(this.title=m)}ngOnInit(){this.getAboutUsDetails(),this.$gtmService.pushPageView("about us",this.title),this.pageId=history.state.id}getAboutUsDetails(){this.aboutUsService.getFooterPageDetails(this.pageId).subscribe({next:n=>{let e=n.data;e[0].visibility||this._router.navigate(["/page-not-found"]),e&&(this.aboutUsDetails.pageTitle=e[0].pageTitle,this.aboutUsDetails.title=e[0].title,this.aboutUsDetails.showImage=e[0].showImage,this.aboutUsDetails.visibility=e[0].visibility,this.isPageVisible=e[0].visibility,e[0].image&&(this.aboutUsDetails.image="data:image/png;base64, "+e[0].image),this.aboutUsDetails.content=decodeURIComponent(escape(atob(e[0].content))))},error:n=>{}})}environment=u.N;static \u0275fac=function(e){return new(e||o)(t.Y36(f.Dr),t.Y36(l.gz),t.Y36(l.F0),t.Y36(b.H7),t.Y36(h.J))};static \u0275cmp=t.Xpm({type:o,selectors:[["app-AboutUsComponent"]],inputs:{aboutUsDetails:"aboutUsDetails"},decls:1,vars:1,consts:[["class","contact-us-page pageTop",4,"ngIf"],[1,"contact-us-page","pageTop"],[1,"about-us-container","mt-7"],[1,"font-size-28","bold-font","mb-3"],[1,"row"],[3,"ngClass"],[1,"m-6"],[1,"grid"],[1,"col-12","detail-container"],["class","row bold-font font-size-24 ",4,"ngIf"],[1,"row","font-size-18","table-scroll-mobile"],["class","col-md-12 footer-font ql-editor",3,"innerHtml",4,"ngIf"],[1,"col-4"],["class","col-md-2 mt-3",4,"ngIf"],[1,"row","bold-font","font-size-24"],[1,"col-md-12"],[1,"col-md-12","footer-font","ql-editor",3,"innerHtml"],[1,"col-md-2","mt-3"],["class","image-container",4,"ngIf"],[1,"image-container"],["alt","No Image",1,"data-image",3,"src"]],template:function(e,c){1&e&&t.YNc(0,M,14,5,"section",0),2&e&&t.Q6J("ngIf",c.isPageVisible)},dependencies:[s.mk,s.O5,g.Z,v.R],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}h2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%]{font-size:16px!important}ul[_ngcontent-%COMP%], li[_ngcontent-%COMP%], ol[_ngcontent-%COMP%], span[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h2[_ngcontent-%COMP%]{font-weight:200!important;margin:0!important;padding:0!important}.tg[_ngcontent-%COMP%]{border-collapse:collapse;border-spacing:0}.tg[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-color:#000;border-style:solid;border-width:1px;font-family:Arial,sans-serif;font-size:14px;overflow:hidden;padding:10px 5px;word-break:normal}.tg[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-color:#000;border-style:solid;border-width:1px;font-family:Arial,sans-serif;font-size:14px;font-weight:400;overflow:hidden;padding:10px 5px;word-break:normal}.tg[_ngcontent-%COMP%]   .tg-0pky[_ngcontent-%COMP%]{border-color:inherit;text-align:left;vertical-align:top}.data-image[_ngcontent-%COMP%]{width:220px;height:220px}@media only screen and (max-width: 767px){.about-us-container[_ngcontent-%COMP%]{padding:2rem 1rem!important}}.detail-container[_ngcontent-%COMP%]{padding:1rem 1.5rem 0rem!important}@media screen and (max-width: 768px){.table-scroll-mobile[_ngcontent-%COMP%]{overflow-y:scroll}.pageTop[_ngcontent-%COMP%]{margin-top:100px}}.footer-font[_ngcontent-%COMP%]{font-family:var(--regular-font)}.image-container[_ngcontent-%COMP%]{width:220px;height:220px;box-shadow:0 1px 5px #0000001f,0 2px 2px #00000024,0 1px 1px #0003}"]})}return o})()}];var D=i(6663),A=i(258);let y=(()=>{class o{static \u0275fac=function(e){return new(e||o)};static \u0275mod=t.oAB({type:o});static \u0275inj=t.cJS({imports:[s.ez,l.Bz.forChild(O),d.T,g.d,D.aw,A.m]})}return o})()}}]);