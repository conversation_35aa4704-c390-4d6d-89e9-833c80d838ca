{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate, __param } from \"tslib\";\nimport { Component, HostListener, Inject, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { FeaturedDataTypeEnum, FeatureType } from \"@core/interface\";\nimport { ShowRoomTypeEnum } from \"@core/enums\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nlet IndexComponent = class IndexComponent {\n  onResize(event) {\n    this.updateZoomClass();\n  }\n  updateZoomClass() {\n    if (isPlatformBrowser(this.platformId)) {\n      const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n      if (zoomLevel <= 91) {\n        this.zoomLevelClass = 'zoom-110';\n      }\n      if (zoomLevel <= 112) {\n        this.zoomLevelClass = 'zoom-90';\n      } else if (zoomLevel <= 125) {\n        this.zoomLevelClass = 'zoom-80';\n      } else if (zoomLevel <= 134) {\n        this.zoomLevelClass = 'zoom-75';\n      } else if (zoomLevel <= 150) {\n        this.zoomLevelClass = 'zoom-67';\n      } else if (zoomLevel <= 200) {\n        this.zoomLevelClass = 'zoom-50';\n      } else if (zoomLevel <= 300) {\n        this.zoomLevelClass = 'zoom-33';\n      } else if (zoomLevel <= 400) {\n        this.zoomLevelClass = 'zoom-25';\n      } else {\n        this.zoomLevelClass = 'default-zoom';\n      }\n    }\n  }\n  constructor(store, mainDataService, homeService, productService, messageService, reviewsService, translate, router, cookieService, authTokenService, loaderService, permissionService, appDataService, cd, platformId) {\n    this.store = store;\n    this.mainDataService = mainDataService;\n    this.homeService = homeService;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.reviewsService = reviewsService;\n    this.translate = translate;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.loaderService = loaderService;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.cd = cd;\n    this.platformId = platformId;\n    this.sections = [];\n    this.mainSlider = [];\n    this.categories = [];\n    this.enableFeaturedProducts = false;\n    this.enableNewProducts = false;\n    this.banner = [{\n      src: 'assets/images/banner/banner1.jpg'\n    }, {\n      src: 'assets/images/banner/banner2.jpg'\n    }, {\n      src: 'assets/images/banner/banner3.jpg'\n    }];\n    this.showRoomConfigurationRes = [];\n    this.productOffers = [];\n    this.featuredData = [];\n    this.templateId = 1;\n    this.zoomLevelClass = 'default-zoom';\n    this.isMobileTemplate = false;\n    this.isLayoutTemplate = false;\n    this.isMobileView = window.screen.width < 768;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.appDataService.layoutTemplate) {\n        _this.navbarData = yield _this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n        _this.allBanners = yield _this.appDataService.layoutTemplate.find(section => section.type === 'main_banner');\n        _this.mainDataService.setBannerData({\n          isBannerActive: _this.allBanners?.isActive ?? false,\n          isNavbarDataActive: _this.navbarData?.isActive ?? false\n        });\n      }\n      if (!_this.permissionService.hasPermission('Layout-Template')) {\n        _this.getShowRoom();\n        _this.isLayoutTemplate = false;\n      } else {\n        _this.isLayoutTemplate = true;\n      }\n      _this.cd.detectChanges();\n    })();\n  }\n  findShowRoomTypeId(showRoomId) {\n    return this.showRoomConfigurationRes.find(element => element.showRoomTypeId == showRoomId);\n  }\n  getShowRoom() {\n    this.showRoomConfigurationRes = this.appDataService.showRoomConfiguration?.records;\n    if (this.findShowRoomTypeId(ShowRoomTypeEnum.MainBanner)) {\n      this.getMainSliderData();\n    }\n    this.addFeatureData();\n    const foundFeatureProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && element.featureProduct);\n    const foundCategoryProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && !element.featureProduct && element.categoryId);\n    if (foundFeatureProduct?.length) {\n      this.handleFeature(foundFeatureProduct);\n    } else {\n      this.handleFeatureNolength();\n    }\n    if (foundCategoryProduct.length) {\n      this.handleFoundProduct(foundCategoryProduct);\n    }\n  }\n  addFeatureData() {\n    const foundCategories = this.findShowRoomTypeId(ShowRoomTypeEnum.Category);\n    if (foundCategories?.showRoomTypeId) {\n      const categories = this.appDataService.categories.records;\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Category,\n        data: categories,\n        image: foundCategories.image,\n        color: foundCategories.color,\n        feature: foundCategories.featureProduct,\n        order: foundCategories.order,\n        categoryId: foundCategories.categoryId,\n        fetchStatus: 'completed',\n        isDragged: false,\n        title: 'Categories'\n      });\n    }\n  }\n  signOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/login']);\n    sessionStorage.clear();\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    this.getShowRoom();\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n    }\n  }\n  getMainSliderData() {\n    this.homeService.getMainSliders({}).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        Object.keys(res.data.records).forEach(function (k) {\n          res.data.records[k].imageUrl = UtilityFunctions.verifyImageURL(res.data.records[k].imageUrl, environment.apiEndPoint);\n        });\n        this.mainSlider.push(...res.data.records);\n      },\n      error: err => {\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n  }\n  handleFeature(foundFeatureProduct) {\n    var _this2 = this;\n    foundFeatureProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Feature,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: p.featureProduct,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        title: FeatureType[p.featureProduct],\n        topNumber: p.topNumber\n      });\n    });\n    Promise.all(foundFeatureProduct.map(p => this.productService.GetAllProductsByFeature(p.featureProduct, p.topNumber, true, 1, 20, false, null, true))).then(response => {\n      Promise.all(response.map(respObj => {\n        return new Promise((resolve, reject) => {\n          respObj.subscribe(r => {\n            if (r.data.records) {\n              this.featuredData.forEach(x => {\n                if (x.feature && x.feature === r.data.feature) {\n                  x.data = r.data.records;\n                  x.fetchStatus = 'completed';\n                }\n              });\n              this.featuredData.sort((a, b) => a.order - b.order);\n              this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n            }\n            resolve(true);\n          }, err => {\n            reject(new Error('An error occurred'));\n          });\n        });\n      })).then( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (data) {\n          const foundBanner = _this2.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n          if (foundBanner?.showRoomTypeId) {\n            _this2.homeService.getBanners(foundBanner.bannerId).subscribe({\n              next: res => {\n                _this2.featuredData.push({\n                  type: FeaturedDataTypeEnum.Banner,\n                  data: res.data.imageUrl,\n                  image: foundBanner.imageURL,\n                  order: foundBanner.order\n                });\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              },\n              error: err => {\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              }\n            });\n          }\n          _this2.loaderService.hide();\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).then( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (data) {\n          _this2.loaderService.hide();\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    });\n  }\n  handleFeatureNolength() {\n    const foundBanner = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n    if (foundBanner?.showRoomTypeId) {\n      this.homeService.getBanners(foundBanner.bannerId).subscribe(res => {\n        this.featuredData.push({\n          type: FeaturedDataTypeEnum.Banner,\n          data: res.data,\n          image: foundBanner.imageURL,\n          order: foundBanner.order,\n          categoryId: foundBanner.categoryId\n        });\n        this.featuredData.sort((a, b) => a.order - b.order);\n        this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        this.loaderService.hide();\n      });\n    }\n  }\n  handleFoundProduct(foundCategoryProduct) {\n    foundCategoryProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.CategoryProduct,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: null,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        name: \"Category\"\n      });\n      this.productService.getCategoryProducts(p.categoryId, 15, true).subscribe({\n        next: r => {\n          if (r.data.productsList.records) {\n            this.featuredData.forEach(x => {\n              if (x.categoryId == p.categoryId) {\n                x.data = r.data.productsList.records;\n                x.fetchStatus = 'completed';\n                x.name = r.data.productsList.records[0].categoryName;\n              }\n            });\n            this.loaderService.hide();\n          }\n          this.featuredData.sort((a, b) => a.order - b.order);\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        },\n        error: () => {\n          this.loaderService.hide();\n        }\n      });\n    });\n  }\n};\n__decorate([HostListener('window:resize', ['$event'])], IndexComponent.prototype, \"onResize\", null);\nIndexComponent = __decorate([Component({\n  selector: 'app-index',\n  templateUrl: './index.component.html',\n  styleUrls: ['./index.component.scss']\n}), __param(14, Inject(PLATFORM_ID))], IndexComponent);\nexport { IndexComponent };", "map": {"version": 3, "names": ["Component", "HostListener", "Inject", "PLATFORM_ID", "environment", "FeaturedDataTypeEnum", "FeatureType", "ShowRoomTypeEnum", "isPlatformBrowser", "UtilityFunctions", "IndexComponent", "onResize", "event", "updateZoomClass", "platformId", "zoomLevel", "window", "innerWidth", "screen", "availWidth", "zoomLevelClass", "constructor", "store", "mainDataService", "homeService", "productService", "messageService", "reviewsService", "translate", "router", "cookieService", "authTokenService", "loaderService", "permissionService", "appDataService", "cd", "sections", "mainSlider", "categories", "enableFeaturedProducts", "enableNewProducts", "banner", "src", "showRoomConfigurationRes", "productOffers", "featuredData", "templateId", "isMobileTemplate", "isLayoutTemplate", "isMobile<PERSON>iew", "width", "hasPermission", "ngOnInit", "_this", "_asyncToGenerator", "layoutTemplate", "navbarData", "find", "section", "type", "allBanners", "setBannerData", "isBannerActive", "isActive", "isNavbarDataActive", "getShowRoom", "detectChanges", "findShowRoomTypeId", "showRoomId", "element", "showRoomTypeId", "showRoomConfiguration", "records", "MainBanner", "getMainSliderData", "addFeatureData", "foundFeatureProduct", "filter", "featureProduct", "foundCategoryProduct", "categoryId", "length", "handleFeature", "handleFeatureNolength", "handleFoundProduct", "foundCategories", "Category", "push", "data", "image", "color", "feature", "order", "fetchStatus", "isDragged", "title", "signOut", "setStoreData", "authTokenSet", "delete", "set", "localStorage", "removeItem", "navigate", "sessionStorage", "clear", "setItem", "localStoreNames", "notifications", "unreadNotifications", "shipping", "payment", "promo", "steps", "profile", "orderId", "JSON", "stringify", "getMainSliders", "subscribe", "next", "res", "hide", "Object", "keys", "for<PERSON>ach", "k", "imageUrl", "verifyImageURL", "apiEndPoint", "error", "err", "complete", "_this2", "p", "Feature", "topNumber", "Promise", "all", "map", "GetAllProductsByFeature", "then", "response", "respObj", "resolve", "reject", "r", "x", "sort", "a", "b", "parse", "Error", "_ref", "foundBanner", "Banner", "getBanners", "bannerId", "imageURL", "_x", "apply", "arguments", "_ref2", "_x2", "CategoryProduct", "name", "getCategoryProducts", "productsList", "categoryName", "__decorate", "selector", "templateUrl", "styleUrls", "__param"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\components\\index\\index.component.ts"], "sourcesContent": ["import {ChangeDetectorRef, Component, HostListener, Inject, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { environment } from '@environments/environment';\r\n\r\nimport {\r\n  BannerResponse,\r\n  Offers,\r\n  Section,\r\n  FeaturedDataTypeEnum,\r\n  FeatureType,\r\n  ProductRate, CategoryRecords\r\n} from \"@core/interface\";\r\n\r\nimport {\r\n  StoreService,\r\n  MainDataService,\r\n  ProductService,\r\n  HomeService,\r\n  LoaderService,\r\n  AuthTokenService,\r\n  ReviewsService, AppDataService, PermissionService\r\n} from '@core/services';\r\nimport { ShowRoomTypeEnum } from \"@core/enums\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\n\r\n@Component({\r\n  selector: 'app-index',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexComponent implements OnInit {\r\n  sections: Array<Section> = [];\r\n  mainSlider: Array<any> = [];\r\n  categories: Array<CategoryRecords> = [];\r\n  enableFeaturedProducts: boolean = false;\r\n  enableNewProducts: boolean = false;\r\n  reviews: ProductRate[] | undefined;\r\n  banner: any = [\r\n    { src: 'assets/images/banner/banner1.jpg' },\r\n    { src: 'assets/images/banner/banner2.jpg' },\r\n    { src: 'assets/images/banner/banner3.jpg' },\r\n  ];\r\n\r\n\r\n  showRoomConfigurationRes: any = [];\r\n  productOffers: Offers[] = [];\r\n  featuredData: any = [];\r\n\r\n  templateId: number = 1;\r\n  zoomLevelClass: string = 'default-zoom';\r\n  navbarData: any;\r\n  allBanners: any;\r\n  isMobileTemplate:boolean=false;\r\n  isLayoutTemplate: boolean = false;\r\n  isMobileView: boolean = window.screen.width < 768;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: Event) {\r\n    this.updateZoomClass();\r\n  }\r\n\r\n  private updateZoomClass() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const zoomLevel = (window.innerWidth / window.screen.availWidth) * 100;\r\n      if (zoomLevel <= 91) {\r\n        this.zoomLevelClass = 'zoom-110';\r\n      }\r\n      if (zoomLevel <= 112) {\r\n        this.zoomLevelClass = 'zoom-90';\r\n      } else if (zoomLevel <= 125) {\r\n        this.zoomLevelClass = 'zoom-80';\r\n      } else if (zoomLevel <= 134) {\r\n        this.zoomLevelClass = 'zoom-75';\r\n      } else if (zoomLevel <= 150) {\r\n        this.zoomLevelClass = 'zoom-67';\r\n      } else if (zoomLevel <= 200) {\r\n        this.zoomLevelClass = 'zoom-50';\r\n      } else if (zoomLevel <= 300) {\r\n        this.zoomLevelClass = 'zoom-33';\r\n      } else if (zoomLevel <= 400) {\r\n        this.zoomLevelClass = 'zoom-25';\r\n      } else {\r\n        this.zoomLevelClass = 'default-zoom';\r\n      }\r\n    }\r\n  }\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private mainDataService: MainDataService,\r\n    private homeService: HomeService,\r\n    private productService: ProductService,\r\n    private messageService: MessageService,\r\n    private reviewsService: ReviewsService,\r\n    private translate: TranslateService,\r\n    private router: Router,\r\n    private cookieService: CookieService,\r\n    private authTokenService: AuthTokenService,\r\n    private loaderService: LoaderService,\r\n    private permissionService: PermissionService,\r\n    private appDataService: AppDataService,\r\n    private cd: ChangeDetectorRef,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n  ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n  }\r\n\r\n  async ngOnInit() {\r\nif(this.appDataService.layoutTemplate){\r\n  this.navbarData = await this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n  this.allBanners = await this.appDataService.layoutTemplate.find((section: any) => section.type === 'main_banner');\r\n\r\n  this.mainDataService.setBannerData(\r\n    {\r\n      isBannerActive: this.allBanners?.isActive ?? false,\r\n      isNavbarDataActive: this.navbarData?.isActive ?? false\r\n    }\r\n  )\r\n}\r\n\r\n\r\n    if (!this.permissionService.hasPermission('Layout-Template')) {\r\n      this.getShowRoom()\r\n      this.isLayoutTemplate = false\r\n    } else {\r\n      this.isLayoutTemplate = true\r\n    }\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  findShowRoomTypeId(showRoomId: ShowRoomTypeEnum) {\r\n    return this.showRoomConfigurationRes.find(\r\n      (element: any) => element.showRoomTypeId == showRoomId\r\n    );\r\n  }\r\n\r\n  getShowRoom() {\r\n\r\n    this.showRoomConfigurationRes = this.appDataService.showRoomConfiguration?.records;\r\n\r\n    if (this.findShowRoomTypeId(ShowRoomTypeEnum.MainBanner)) {\r\n      this.getMainSliderData();\r\n    }\r\n\r\n    this.addFeatureData();\r\n\r\n    const foundFeatureProduct = this.showRoomConfigurationRes.filter(\r\n      (element: any) => element.showRoomTypeId == 2 && element.featureProduct\r\n    );\r\n    const foundCategoryProduct = this.showRoomConfigurationRes.filter((element: any) => element.showRoomTypeId == 2 && !element.featureProduct && element.categoryId);\r\n\r\n\r\n    if (foundFeatureProduct?.length) {\r\n    this.handleFeature(foundFeatureProduct)\r\n    } else {\r\n      this.handleFeatureNolength();\r\n    }\r\n\r\n    if (foundCategoryProduct.length) {\r\n      this.handleFoundProduct(foundCategoryProduct);\r\n\r\n    }\r\n  }\r\n\r\n  addFeatureData(){\r\n    const foundCategories = this.findShowRoomTypeId(ShowRoomTypeEnum.Category);\r\n    if (foundCategories?.showRoomTypeId) {\r\n      const categories = this.appDataService.categories.records\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.Category,\r\n        data: categories,\r\n        image: foundCategories.image,\r\n        color: foundCategories.color,\r\n        feature: foundCategories.featureProduct,\r\n        order: foundCategories.order,\r\n        categoryId: foundCategories.categoryId,\r\n        fetchStatus: 'completed',\r\n        isDragged: false,\r\n        title: 'Categories'\r\n      });\r\n    }\r\n  }\r\n  signOut(): void {\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet('');\r\n    this.cookieService.delete('authToken', '/');\r\n\r\n    this.store.set('cartProducts', '');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    this.router.navigate(['/login']);\r\n\r\n    sessionStorage.clear();\r\n    this.store.set('profile', '');\r\n    this.store.set('cartProducts', '');\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    this.getShowRoom();\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0,\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null,\r\n      });\r\n    } else {\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n      localStorage.removeItem('refreshToken');\r\n      localStorage.removeItem('auth_enc');\r\n    }\r\n  }\r\n\r\n  getMainSliderData(): void {\r\n    this.homeService.getMainSliders({}).subscribe({\r\n      next: (res: any) => {\r\n        this.loaderService.hide();\r\n\r\n        Object.keys(res.data.records).forEach(function (k) {\r\n          res.data.records[k].imageUrl =  UtilityFunctions.verifyImageURL(res.data.records[k].imageUrl, environment.apiEndPoint);\r\n        });\r\n        this.mainSlider.push(...res.data.records);\r\n      },\r\n      error: (err: any) => {\r\n        this.loaderService.hide();\r\n      },\r\n      complete: () => {\r\n        this.loaderService.hide();\r\n\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleFeature(foundFeatureProduct: any) {\r\n    foundFeatureProduct.forEach((p: any) => {\r\n\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.Feature,\r\n        data: [],\r\n        image: p.image,\r\n        color: p.color,\r\n        feature: p.featureProduct,\r\n        order: p.order,\r\n        categoryId: p.categoryId,\r\n        fetchStatus: 'pending',\r\n        isDragged: false,\r\n        title: FeatureType[p.featureProduct],\r\n        topNumber: p.topNumber\r\n      });\r\n    });\r\n    Promise.all(\r\n      foundFeatureProduct.map((p: any) =>\r\n        this.productService.GetAllProductsByFeature(p.featureProduct, p.topNumber, true, 1, 20, false, null, true)\r\n      )\r\n    ).then((response: any) => {\r\n      Promise.all(response.map((respObj: any) => {\r\n        return new Promise((resolve, reject) => {\r\n          respObj.subscribe((r: any) => {\r\n            if (r.data.records) {\r\n              this.featuredData.forEach((x: any) => {\r\n                if (x.feature && x.feature === r.data.feature) {\r\n                  x.data = r.data.records;\r\n                  x.fetchStatus = 'completed';\r\n                }\r\n              });\r\n              this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n              this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n            }\r\n            resolve(true);\r\n          }, (err: any) => {\r\n            reject(new Error('An error occurred'));\r\n          });\r\n        });\r\n\r\n      }))\r\n        .then(async (data: any) => {\r\n          const foundBanner: any = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\r\n          if (foundBanner?.showRoomTypeId) {\r\n            this.homeService\r\n              .getBanners(foundBanner.bannerId)\r\n              .subscribe({\r\n                next: (res: any) => {\r\n                  this.featuredData.push({\r\n                    type: FeaturedDataTypeEnum.Banner,\r\n                    data: res.data.imageUrl,\r\n                    image: foundBanner.imageURL,\r\n                    order: foundBanner.order,\r\n                  });\r\n                  this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n                  this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n                },\r\n                error: (err: any) => {\r\n                  this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n                  this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n                }\r\n              });\r\n          }\r\n          this.loaderService.hide();\r\n        })\r\n        .then(async (data: any) => {\r\n\r\n          this.loaderService.hide();\r\n        });\r\n    });\r\n\r\n  }\r\n\r\n  private handleFeatureNolength() {\r\n    const foundBanner: any = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\r\n    if (foundBanner?.showRoomTypeId) {\r\n      this.homeService.getBanners(foundBanner.bannerId)\r\n        .subscribe((res: BannerResponse) => {\r\n          this.featuredData.push({\r\n            type: FeaturedDataTypeEnum.Banner,\r\n            data: res.data,\r\n            image: foundBanner.imageURL,\r\n            order: foundBanner.order,\r\n            categoryId: foundBanner.categoryId\r\n          });\r\n          this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n          this.loaderService.hide();\r\n        });\r\n    }\r\n  }\r\n\r\n  private handleFoundProduct(foundCategoryProduct: any) {\r\n    foundCategoryProduct.forEach((p: any) => {\r\n\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.CategoryProduct,\r\n        data: [],\r\n        image: p.image,\r\n        color: p.color,\r\n        feature: null,\r\n        order: p.order,\r\n        categoryId: p.categoryId,\r\n        fetchStatus: 'pending',\r\n        isDragged: false,\r\n        name: \"Category\"\r\n      });\r\n      this.productService.getCategoryProducts(p.categoryId, 15, true).subscribe({\r\n        next: (r: any) => {\r\n          if (r.data.productsList.records) {\r\n            this.featuredData.forEach((x: any) => {\r\n              if (x.categoryId == p.categoryId) {\r\n                x.data = r.data.productsList.records;\r\n                x.fetchStatus = 'completed';\r\n                x.name = r.data.productsList.records[0].categoryName;\r\n              }\r\n\r\n            });\r\n            this.loaderService.hide();\r\n          }\r\n          this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n        },\r\n        error: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAA2BA,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAUC,WAAW,QAAO,eAAe;AAMrG,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,SAIEC,oBAAoB,EACpBC,WAAW,QAEN,iBAAiB;AAWxB,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,OAAOC,gBAAgB,MAAM,2BAA2B;AAOjD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EA2BzBC,QAAQA,CAACC,KAAY;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,IAAIL,iBAAiB,CAAC,IAAI,CAACM,UAAU,CAAC,EAAE;MACtC,MAAMC,SAAS,GAAIC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,MAAM,CAACC,UAAU,GAAI,GAAG;MACtE,IAAIJ,SAAS,IAAI,EAAE,EAAE;QACnB,IAAI,CAACK,cAAc,GAAG,UAAU;;MAElC,IAAIL,SAAS,IAAI,GAAG,EAAE;QACpB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,cAAc;;;EAG1C;EAEAC,YACUC,KAAmB,EACnBC,eAAgC,EAChCC,WAAwB,EACxBC,cAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B,EAC9BC,SAA2B,EAC3BC,MAAc,EACdC,aAA4B,EAC5BC,gBAAkC,EAClCC,aAA4B,EAC5BC,iBAAoC,EACpCC,cAA8B,EAC9BC,EAAqB,EACArB,UAAe;IAdpC,KAAAQ,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACmB,KAAArB,UAAU,GAAVA,UAAU;IAvEzC,KAAAsB,QAAQ,GAAmB,EAAE;IAC7B,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,UAAU,GAA2B,EAAE;IACvC,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,iBAAiB,GAAY,KAAK;IAElC,KAAAC,MAAM,GAAQ,CACZ;MAAEC,GAAG,EAAE;IAAkC,CAAE,EAC3C;MAAEA,GAAG,EAAE;IAAkC,CAAE,EAC3C;MAAEA,GAAG,EAAE;IAAkC,CAAE,CAC5C;IAGD,KAAAC,wBAAwB,GAAQ,EAAE;IAClC,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,YAAY,GAAQ,EAAE;IAEtB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAA1B,cAAc,GAAW,cAAc;IAGvC,KAAA2B,gBAAgB,GAAS,KAAK;IAC9B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,YAAY,GAAYjC,MAAM,CAACE,MAAM,CAACgC,KAAK,GAAG,GAAG;IAkD/C,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACd,iBAAiB,CAACkB,aAAa,CAAC,eAAe,CAAC;EAC/E;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAGD,KAAI,CAACnB,cAAc,CAACqB,cAAc,EAAC;QACpCF,KAAI,CAACG,UAAU,SAASH,KAAI,CAACnB,cAAc,CAACqB,cAAc,CAACE,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;QAC5GN,KAAI,CAACO,UAAU,SAASP,KAAI,CAACnB,cAAc,CAACqB,cAAc,CAACE,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,aAAa,CAAC;QAEjHN,KAAI,CAAC9B,eAAe,CAACsC,aAAa,CAChC;UACEC,cAAc,EAAET,KAAI,CAACO,UAAU,EAAEG,QAAQ,IAAI,KAAK;UAClDC,kBAAkB,EAAEX,KAAI,CAACG,UAAU,EAAEO,QAAQ,IAAI;SAClD,CACF;;MAIC,IAAI,CAACV,KAAI,CAACpB,iBAAiB,CAACkB,aAAa,CAAC,iBAAiB,CAAC,EAAE;QAC5DE,KAAI,CAACY,WAAW,EAAE;QAClBZ,KAAI,CAACL,gBAAgB,GAAG,KAAK;OAC9B,MAAM;QACLK,KAAI,CAACL,gBAAgB,GAAG,IAAI;;MAE9BK,KAAI,CAAClB,EAAE,CAAC+B,aAAa,EAAE;IAAC;EAC1B;EAEAC,kBAAkBA,CAACC,UAA4B;IAC7C,OAAO,IAAI,CAACzB,wBAAwB,CAACc,IAAI,CACtCY,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAIF,UAAU,CACvD;EACH;EAEAH,WAAWA,CAAA;IAET,IAAI,CAACtB,wBAAwB,GAAG,IAAI,CAACT,cAAc,CAACqC,qBAAqB,EAAEC,OAAO;IAElF,IAAI,IAAI,CAACL,kBAAkB,CAAC5D,gBAAgB,CAACkE,UAAU,CAAC,EAAE;MACxD,IAAI,CAACC,iBAAiB,EAAE;;IAG1B,IAAI,CAACC,cAAc,EAAE;IAErB,MAAMC,mBAAmB,GAAG,IAAI,CAACjC,wBAAwB,CAACkC,MAAM,CAC7DR,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAI,CAAC,IAAID,OAAO,CAACS,cAAc,CACxE;IACD,MAAMC,oBAAoB,GAAG,IAAI,CAACpC,wBAAwB,CAACkC,MAAM,CAAER,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAI,CAAC,IAAI,CAACD,OAAO,CAACS,cAAc,IAAIT,OAAO,CAACW,UAAU,CAAC;IAGjK,IAAIJ,mBAAmB,EAAEK,MAAM,EAAE;MACjC,IAAI,CAACC,aAAa,CAACN,mBAAmB,CAAC;KACtC,MAAM;MACL,IAAI,CAACO,qBAAqB,EAAE;;IAG9B,IAAIJ,oBAAoB,CAACE,MAAM,EAAE;MAC/B,IAAI,CAACG,kBAAkB,CAACL,oBAAoB,CAAC;;EAGjD;EAEAJ,cAAcA,CAAA;IACZ,MAAMU,eAAe,GAAG,IAAI,CAAClB,kBAAkB,CAAC5D,gBAAgB,CAAC+E,QAAQ,CAAC;IAC1E,IAAID,eAAe,EAAEf,cAAc,EAAE;MACnC,MAAMhC,UAAU,GAAG,IAAI,CAACJ,cAAc,CAACI,UAAU,CAACkC,OAAO;MACzD,IAAI,CAAC3B,YAAY,CAAC0C,IAAI,CAAC;QACrB5B,IAAI,EAAEtD,oBAAoB,CAACiF,QAAQ;QACnCE,IAAI,EAAElD,UAAU;QAChBmD,KAAK,EAAEJ,eAAe,CAACI,KAAK;QAC5BC,KAAK,EAAEL,eAAe,CAACK,KAAK;QAC5BC,OAAO,EAAEN,eAAe,CAACP,cAAc;QACvCc,KAAK,EAAEP,eAAe,CAACO,KAAK;QAC5BZ,UAAU,EAAEK,eAAe,CAACL,UAAU;QACtCa,WAAW,EAAE,WAAW;QACxBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;OACR,CAAC;;EAEN;EACAC,OAAOA,CAAA;IACL,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAAClE,gBAAgB,CAACmE,YAAY,CAAC,EAAE,CAAC;IACtC,IAAI,CAACpE,aAAa,CAACqE,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAE3C,IAAI,CAAC7E,KAAK,CAAC8E,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAACzE,MAAM,CAAC0E,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEhCC,cAAc,CAACC,KAAK,EAAE;IACtB,IAAI,CAACnF,KAAK,CAAC8E,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACK,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCL,YAAY,CAACK,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzC,IAAI,CAACzC,WAAW,EAAE;EACpB;EAEAgC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC3E,KAAK,CAACqF,eAAe,CAAC1B,MAAM,EAAE;MACrC,IAAI,CAAC3D,KAAK,CAAC8E,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAAC9E,KAAK,CAAC8E,GAAG,CAAC,eAAe,EAAE;QAC9BQ,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAACvF,KAAK,CAAC8E,GAAG,CAAC,cAAc,EAAE;QAC7BU,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACLd,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCL,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCL,YAAY,CAACK,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCL,YAAY,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MACxDhB,YAAY,CAACK,OAAO,CAAC,oBAAoB,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9DhB,YAAY,CAACK,OAAO,CAAC,iBAAiB,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3DhB,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MACtCL,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;MACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;;EAEvC;EAEA5B,iBAAiBA,CAAA;IACf,IAAI,CAAClD,WAAW,CAAC8F,cAAc,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACzF,aAAa,CAAC0F,IAAI,EAAE;QAEzBC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACjC,IAAI,CAAChB,OAAO,CAAC,CAACqD,OAAO,CAAC,UAAUC,CAAC;UAC/CL,GAAG,CAACjC,IAAI,CAAChB,OAAO,CAACsD,CAAC,CAAC,CAACC,QAAQ,GAAItH,gBAAgB,CAACuH,cAAc,CAACP,GAAG,CAACjC,IAAI,CAAChB,OAAO,CAACsD,CAAC,CAAC,CAACC,QAAQ,EAAE3H,WAAW,CAAC6H,WAAW,CAAC;QACxH,CAAC,CAAC;QACF,IAAI,CAAC5F,UAAU,CAACkD,IAAI,CAAC,GAAGkC,GAAG,CAACjC,IAAI,CAAChB,OAAO,CAAC;MAC3C,CAAC;MACD0D,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACnG,aAAa,CAAC0F,IAAI,EAAE;MAC3B,CAAC;MACDU,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACpG,aAAa,CAAC0F,IAAI,EAAE;MAE3B;KACD,CAAC;EACJ;EAEQxC,aAAaA,CAACN,mBAAwB;IAAA,IAAAyD,MAAA;IAC5CzD,mBAAmB,CAACiD,OAAO,CAAES,CAAM,IAAI;MAErC,IAAI,CAACzF,YAAY,CAAC0C,IAAI,CAAC;QACrB5B,IAAI,EAAEtD,oBAAoB,CAACkI,OAAO;QAClC/C,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE6C,CAAC,CAAC7C,KAAK;QACdC,KAAK,EAAE4C,CAAC,CAAC5C,KAAK;QACdC,OAAO,EAAE2C,CAAC,CAACxD,cAAc;QACzBc,KAAK,EAAE0C,CAAC,CAAC1C,KAAK;QACdZ,UAAU,EAAEsD,CAAC,CAACtD,UAAU;QACxBa,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEzF,WAAW,CAACgI,CAAC,CAACxD,cAAc,CAAC;QACpC0D,SAAS,EAAEF,CAAC,CAACE;OACd,CAAC;IACJ,CAAC,CAAC;IACFC,OAAO,CAACC,GAAG,CACT9D,mBAAmB,CAAC+D,GAAG,CAAEL,CAAM,IAC7B,IAAI,CAAC7G,cAAc,CAACmH,uBAAuB,CAACN,CAAC,CAACxD,cAAc,EAAEwD,CAAC,CAACE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAC3G,CACF,CAACK,IAAI,CAAEC,QAAa,IAAI;MACvBL,OAAO,CAACC,GAAG,CAACI,QAAQ,CAACH,GAAG,CAAEI,OAAY,IAAI;QACxC,OAAO,IAAIN,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;UACrCF,OAAO,CAACxB,SAAS,CAAE2B,CAAM,IAAI;YAC3B,IAAIA,CAAC,CAAC1D,IAAI,CAAChB,OAAO,EAAE;cAClB,IAAI,CAAC3B,YAAY,CAACgF,OAAO,CAAEsB,CAAM,IAAI;gBACnC,IAAIA,CAAC,CAACxD,OAAO,IAAIwD,CAAC,CAACxD,OAAO,KAAKuD,CAAC,CAAC1D,IAAI,CAACG,OAAO,EAAE;kBAC7CwD,CAAC,CAAC3D,IAAI,GAAG0D,CAAC,CAAC1D,IAAI,CAAChB,OAAO;kBACvB2E,CAAC,CAACtD,WAAW,GAAG,WAAW;;cAE/B,CAAC,CAAC;cACF,IAAI,CAAChD,YAAY,CAACuG,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC;cAC7D,IAAI,CAAC/C,YAAY,GAAGuE,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxE,YAAY,CAAC,CAAC;;YAEnEmG,OAAO,CAAC,IAAI,CAAC;UACf,CAAC,EAAGb,GAAQ,IAAI;YACdc,MAAM,CAAC,IAAIO,KAAK,CAAC,mBAAmB,CAAC,CAAC;UACxC,CAAC,CAAC;QACJ,CAAC,CAAC;MAEJ,CAAC,CAAC,CAAC,CACAX,IAAI;QAAA,IAAAY,IAAA,GAAAnG,iBAAA,CAAC,WAAOkC,IAAS,EAAI;UACxB,MAAMkE,WAAW,GAAQrB,MAAI,CAAClE,kBAAkB,CAAC5D,gBAAgB,CAACoJ,MAAM,CAAC;UACzE,IAAID,WAAW,EAAEpF,cAAc,EAAE;YAC/B+D,MAAI,CAAC7G,WAAW,CACboI,UAAU,CAACF,WAAW,CAACG,QAAQ,CAAC,CAChCtC,SAAS,CAAC;cACTC,IAAI,EAAGC,GAAQ,IAAI;gBACjBY,MAAI,CAACxF,YAAY,CAAC0C,IAAI,CAAC;kBACrB5B,IAAI,EAAEtD,oBAAoB,CAACsJ,MAAM;kBACjCnE,IAAI,EAAEiC,GAAG,CAACjC,IAAI,CAACuC,QAAQ;kBACvBtC,KAAK,EAAEiE,WAAW,CAACI,QAAQ;kBAC3BlE,KAAK,EAAE8D,WAAW,CAAC9D;iBACpB,CAAC;gBACFyC,MAAI,CAACxF,YAAY,CAACuG,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC;gBAC7DyC,MAAI,CAACxF,YAAY,GAAGuE,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAACgB,MAAI,CAACxF,YAAY,CAAC,CAAC;cACnE,CAAC;cACDqF,KAAK,EAAGC,GAAQ,IAAI;gBAClBE,MAAI,CAACxF,YAAY,CAACuG,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC;gBAC7DyC,MAAI,CAACxF,YAAY,GAAGuE,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAACgB,MAAI,CAACxF,YAAY,CAAC,CAAC;cACnE;aACD,CAAC;;UAENwF,MAAI,CAACrG,aAAa,CAAC0F,IAAI,EAAE;QAC3B,CAAC;QAAA,iBAAAqC,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC,CACDpB,IAAI;QAAA,IAAAqB,KAAA,GAAA5G,iBAAA,CAAC,WAAOkC,IAAS,EAAI;UAExB6C,MAAI,CAACrG,aAAa,CAAC0F,IAAI,EAAE;QAC3B,CAAC;QAAA,iBAAAyC,GAAA;UAAA,OAAAD,KAAA,CAAAF,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IACN,CAAC,CAAC;EAEJ;EAEQ9E,qBAAqBA,CAAA;IAC3B,MAAMuE,WAAW,GAAQ,IAAI,CAACvF,kBAAkB,CAAC5D,gBAAgB,CAACoJ,MAAM,CAAC;IACzE,IAAID,WAAW,EAAEpF,cAAc,EAAE;MAC/B,IAAI,CAAC9C,WAAW,CAACoI,UAAU,CAACF,WAAW,CAACG,QAAQ,CAAC,CAC9CtC,SAAS,CAAEE,GAAmB,IAAI;QACjC,IAAI,CAAC5E,YAAY,CAAC0C,IAAI,CAAC;UACrB5B,IAAI,EAAEtD,oBAAoB,CAACsJ,MAAM;UACjCnE,IAAI,EAAEiC,GAAG,CAACjC,IAAI;UACdC,KAAK,EAAEiE,WAAW,CAACI,QAAQ;UAC3BlE,KAAK,EAAE8D,WAAW,CAAC9D,KAAK;UACxBZ,UAAU,EAAE0E,WAAW,CAAC1E;SACzB,CAAC;QACF,IAAI,CAACnC,YAAY,CAACuG,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC;QAC7D,IAAI,CAAC/C,YAAY,GAAGuE,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxE,YAAY,CAAC,CAAC;QACjE,IAAI,CAACb,aAAa,CAAC0F,IAAI,EAAE;MAC3B,CAAC,CAAC;;EAER;EAEQtC,kBAAkBA,CAACL,oBAAyB;IAClDA,oBAAoB,CAAC8C,OAAO,CAAES,CAAM,IAAI;MAEtC,IAAI,CAACzF,YAAY,CAAC0C,IAAI,CAAC;QACrB5B,IAAI,EAAEtD,oBAAoB,CAAC+J,eAAe;QAC1C5E,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE6C,CAAC,CAAC7C,KAAK;QACdC,KAAK,EAAE4C,CAAC,CAAC5C,KAAK;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE0C,CAAC,CAAC1C,KAAK;QACdZ,UAAU,EAAEsD,CAAC,CAACtD,UAAU;QACxBa,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,KAAK;QAChBuE,IAAI,EAAE;OACP,CAAC;MACF,IAAI,CAAC5I,cAAc,CAAC6I,mBAAmB,CAAChC,CAAC,CAACtD,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,CAACuC,SAAS,CAAC;QACxEC,IAAI,EAAG0B,CAAM,IAAI;UACf,IAAIA,CAAC,CAAC1D,IAAI,CAAC+E,YAAY,CAAC/F,OAAO,EAAE;YAC/B,IAAI,CAAC3B,YAAY,CAACgF,OAAO,CAAEsB,CAAM,IAAI;cACnC,IAAIA,CAAC,CAACnE,UAAU,IAAIsD,CAAC,CAACtD,UAAU,EAAE;gBAChCmE,CAAC,CAAC3D,IAAI,GAAG0D,CAAC,CAAC1D,IAAI,CAAC+E,YAAY,CAAC/F,OAAO;gBACpC2E,CAAC,CAACtD,WAAW,GAAG,WAAW;gBAC3BsD,CAAC,CAACkB,IAAI,GAAGnB,CAAC,CAAC1D,IAAI,CAAC+E,YAAY,CAAC/F,OAAO,CAAC,CAAC,CAAC,CAACgG,YAAY;;YAGxD,CAAC,CAAC;YACF,IAAI,CAACxI,aAAa,CAAC0F,IAAI,EAAE;;UAE3B,IAAI,CAAC7E,YAAY,CAACuG,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACzD,KAAK,GAAG0D,CAAC,CAAC1D,KAAK,CAAC;UAC7D,IAAI,CAAC/C,YAAY,GAAGuE,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxE,YAAY,CAAC,CAAC;QACnE,CAAC;QACDqF,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAClG,aAAa,CAAC0F,IAAI,EAAE;QAC3B;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;CACD;AAxUC+C,UAAA,EADCxK,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,6CAGzC;AA7BUS,cAAc,GAAA+J,UAAA,EAL1BzK,SAAS,CAAC;EACT0K,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB;CACrC,CAAC,EAyEGC,OAAA,KAAA3K,MAAM,CAACC,WAAW,CAAC,E,EAxEXO,cAAc,CAmW1B;SAnWYA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}