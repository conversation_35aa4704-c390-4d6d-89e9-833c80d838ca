{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/progressspinner\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../product-card/product-card.component\";\nimport * as i5 from \"@shared/components/mtn-main-slider/mtn-main-slider.component\";\nimport * as i6 from \"@ngx-translate/core\";\nfunction CustomSectionOrBannerComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"app-mtn-main-slider\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r0.desktopbannerList);\n  }\n}\nfunction CustomSectionOrBannerComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"app-mtn-main-slider\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r1.mobilebannerList);\n  }\n}\nfunction CustomSectionOrBannerComponent_section_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9);\n    i0.ɵɵelement(2, \"p-progressSpinner\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomSectionOrBannerComponent_section_3_ng_template_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function CustomSectionOrBannerComponent_section_3_ng_template_2_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.productsListUrl(ctx_r9.section.data.Url));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"landing.seeMore\"), \" \");\n  }\n}\nfunction CustomSectionOrBannerComponent_section_3_ng_template_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function CustomSectionOrBannerComponent_section_3_ng_template_2_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r11.productsListUrl(ctx_r11.section.data.Url));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"landing.seeAll\"), \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"p-0\": a0\n  };\n};\nfunction CustomSectionOrBannerComponent_section_3_ng_template_2_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵelement(1, \"app-mtn-product-card\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r13 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r8.section.sectionProductsList.length >= 5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"currency\", ctx_r8.section.sectionProductsList[0] == null ? null : ctx_r8.section.sectionProductsList[0].currencyCode)(\"product\", product_r13);\n  }\n}\nfunction CustomSectionOrBannerComponent_section_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CustomSectionOrBannerComponent_section_3_ng_template_2_div_3_Template, 3, 3, \"div\", 12);\n    i0.ɵɵtemplate(4, CustomSectionOrBannerComponent_section_3_ng_template_2_div_4_Template, 3, 3, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 14);\n    i0.ɵɵtemplate(6, CustomSectionOrBannerComponent_section_3_ng_template_2_a_6_Template, 2, 5, \"a\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.section.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.section.isMobile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.section.isMobile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.section.sectionProductsList);\n  }\n}\nfunction CustomSectionOrBannerComponent_section_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 6);\n    i0.ɵɵtemplate(1, CustomSectionOrBannerComponent_section_3_div_1_Template, 3, 0, \"div\", 7);\n    i0.ɵɵtemplate(2, CustomSectionOrBannerComponent_section_3_ng_template_2_Template, 7, 4, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(3);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.section.showLoader)(\"ngIfElse\", _r4);\n  }\n}\nexport class CustomSectionOrBannerComponent {\n  productService;\n  section;\n  desktopbannerList = [];\n  mobilebannerList = [];\n  mobileScreen;\n  sectionProductsList;\n  products;\n  constructor(productService) {\n    this.productService = productService;\n  }\n  ngOnInit() {\n    this.mobileScreen = window.innerWidth < 768;\n    if (this.section.type === 'Banner' && this.section.data) {\n      let data = this.section.data;\n      if (typeof data === 'string') {\n        try {\n          data = JSON.parse(data);\n        } catch (err) {\n          console.error('Invalid JSON in section.data:', err);\n          return;\n        }\n      }\n      const desktop = data.DesktopBanners || [];\n      const mobile = data.MobileBanners || [];\n      this.desktopbannerList = (desktop || []).map(b => ({\n        imageUrl: b.imageUrl || b.ImageUrl || '',\n        CTALink: b.redirectUrl || b.RedirectUrl || b.CTALink || b.ctaLink || '',\n        promotionId: b.promotionId || b.id || null\n      }));\n      this.mobilebannerList = (mobile || []).map(b => ({\n        imageUrl: b.imageUrl || b.ImageUrl || '',\n        CTALink: b.redirectUrl || b.RedirectUrl || b.CTALink || b.ctaLink || '',\n        promotionId: b.promotionId || b.id || null\n      }));\n    }\n    if (this.section.type === 'ProductsList') {\n      const parsedData = this.parseSectionData(this.section.data);\n      if (parsedData?.TagId) {\n        this.section.showLoader = true;\n        this.productService.getProductsByTag(parsedData.TagId).subscribe(res => {\n          this.section.sectionProductsList = this.normalizeProductsResponse(res);\n          this.section.title = parsedData.Label || '';\n          this.section.url = parsedData.url || '';\n          this.section.showLoader = false;\n        });\n      }\n    }\n  }\n  normalizeProductsResponse(res) {\n    const records = res?.data?.products?.records || [];\n    return records.map(record => {\n      const selectedVariance = record?.specsProducts?.[0] || {};\n      const features = selectedVariance?.productFeaturesList?.[0]?.featureList || [];\n      const product = {\n        badges: record.badgesList,\n        productId: record?.id,\n        productName: record?.name,\n        isLiked: record?.isLiked,\n        priceValue: selectedVariance?.price,\n        salePriceValue: selectedVariance?.salePrice,\n        priceId: selectedVariance?.priceId,\n        currencyCode: record?.currencyCode,\n        masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        specProductId: selectedVariance.specProductId,\n        channelId: record.channelId ?? '1',\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: record.shopId,\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        stockStatus: selectedVariance.stockStatus,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated,\n        isDisable: record.isDisable,\n        badgesList: record?.badgesList\n      };\n      return product;\n    });\n  }\n  parseSectionData(data) {\n    try {\n      return typeof data === 'string' ? JSON.parse(data) : data;\n    } catch (err) {\n      console.error('Error parsing section data:', err);\n      return null;\n    }\n  }\n  productsListUrl(url) {\n    if (!url) return;\n    const fixedUrl = url.startsWith('http') ? url : `https://${url}`;\n    window.location.href = fixedUrl;\n  }\n  static ɵfac = function CustomSectionOrBannerComponent_Factory(t) {\n    return new (t || CustomSectionOrBannerComponent)(i0.ɵɵdirectiveInject(i1.ProductService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CustomSectionOrBannerComponent,\n    selectors: [[\"app-custom-section-or-banner\"]],\n    inputs: {\n      section: \"section\"\n    },\n    decls: 4,\n    vars: 3,\n    consts: [[1, \"template-one\"], [\"class\", \"template-one__slider\", 4, \"ngIf\"], [\"class\", \"category-products-page\", 4, \"ngIf\"], [1, \"template-one__slider\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"sliders\"], [1, \"category-products-page\"], [4, \"ngIf\", \"ngIfElse\"], [\"showProducts\", \"\"], [1, \"feature-loader\"], [1, \"feature-products__header\", \"d-flex\", \"justify-content-between\"], [1, \"feature-products__header__title\"], [\"class\", \"feature-products__header__browseAll see-all\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"feature-products__header__browseAll see-all-mobile\", 3, \"click\", 4, \"ngIf\"], [1, \"other-products\", \"d-flex\", \"flex-wrap\", \"low-products\", \"new-arrival-products\"], [\"class\", \"d-inline-flex product-card-inner\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-products__header__browseAll\", \"see-all\", 3, \"click\"], [1, \"feature-products__header__browseAll\", \"see-all-mobile\", 3, \"click\"], [1, \"d-inline-flex\", \"product-card-inner\", 3, \"ngClass\"], [2, \"width\", \"100%\", 3, \"currency\", \"product\"]],\n    template: function CustomSectionOrBannerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0, 0);\n        i0.ɵɵtemplate(1, CustomSectionOrBannerComponent_section_1_Template, 3, 1, \"section\", 1);\n        i0.ɵɵtemplate(2, CustomSectionOrBannerComponent_section_2_Template, 3, 1, \"section\", 1);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(3, CustomSectionOrBannerComponent_section_3_Template, 4, 2, \"section\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.mobileScreen && ctx.desktopbannerList.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.mobileScreen && ctx.mobilebannerList.length > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.section.type === \"ProductsList\");\n      }\n    },\n    dependencies: [i2.ProgressSpinner, i3.NgClass, i3.NgForOf, i3.NgIf, i4.ProductCardComponent, i5.MtnMainSliderComponent, i6.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.feature-loader[_ngcontent-%COMP%] {\\n  text-align: center;\\n  height: inherit;\\n  margin-top: 25%;\\n}\\n\\n.feature-products[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.feature-products__header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0px 10px 0px 10px !important;\\n}\\n.feature-products__header__title[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--regular-font);\\n  font-size: 30px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 32px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .feature-products__header__title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    margin-bottom: 3px;\\n  }\\n}\\n.feature-products__header__browseAll[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--medium-font);\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 20px;\\n  \\n\\n}\\n@media only screen and (max-width: 767px) {\\n  .feature-products__header__browseAll[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n.feature-products__header[_ngcontent-%COMP%]   .see-all[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #1a445e;\\n}\\n.feature-products[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n.feature-products[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .feature-products[_ngcontent-%COMP%]     .mtn-product-card {\\n    width: 15rem;\\n  }\\n}\\n\\n.low-products[_ngcontent-%COMP%] {\\n  justify-content: start !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .new-arrival-products[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    padding: 4px 4px 4px 4px !important;\\n    width: 50%;\\n    flex: 0 0 50%;\\n  }\\n  a[_ngcontent-%COMP%] {\\n    width: 50%;\\n    flex: 0 0 50%;\\n    padding: 4px 4px 4px 4px !important;\\n  }\\n  .see-all-mobile[_ngcontent-%COMP%] {\\n    color: #204E6E;\\n    text-decoration-line: underline;\\n    font-size: 12px;\\n    font-weight: 300;\\n    line-height: 30px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .new-arrival-products[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    padding: 10px 10px 0px 10px !important;\\n    width: 20%;\\n    flex: 0 0 20%;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .new-arrival-products[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    padding: 16px 10px 16px 10px !important;\\n    width: 20%;\\n    flex: 0 0 20%;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  .new-arrival-products[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    padding: 16px 10px 16px 10px !important;\\n    width: 20%;\\n    flex: 0 0 20%;\\n  }\\n}\\n.template-one[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin: 0 auto;\\n  padding: 0 !important;\\n}\\n.template-one__slider[_ngcontent-%COMP%] {\\n  height: 330px;\\n  width: 1300px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  height: inherit;\\n}\\n.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n@media only screen and (max-width: 767px) {\\n  .template-one__slider[_ngcontent-%COMP%] {\\n    height: 220px;\\n    width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .template-one__slider[_ngcontent-%COMP%] {\\n    width: 90vw;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}