{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/button\";\nimport * as i3 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return [\"/\"];\n};\nexport class CategoryNotFoundComponent {\n  static ɵfac = function CategoryNotFoundComponent_Factory(t) {\n    return new (t || CategoryNotFoundComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CategoryNotFoundComponent,\n    selectors: [[\"app-category-not-found\"]],\n    decls: 8,\n    vars: 8,\n    consts: [[1, \"empty-cart\", \"align-items-center\"], [1, \"center\", \"justify-content-center\"], [1, \"d-flex\", \"justify-content-center\", \"m-0\", \"mx-4\", \"cartText\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"margin-x-30\", \"width-100\", \"second-btn\", 3, \"routerLink\", \"label\"]],\n    template: function CategoryNotFoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"p\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3);\n        i0.ɵɵelement(6, \"button\", 4);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, \"categories.productNotFound\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(7, 5, \"categories.goToHomePage\"));\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c0));\n      }\n    },\n    dependencies: [i1.RouterLink, i2.ButtonDirective, i3.TranslatePipe],\n    styles: [\".empty-cart[_ngcontent-%COMP%] {\\n  height: 640px;\\n  position: relative;\\n}\\n\\n.center[_ngcontent-%COMP%] {\\n  margin: 0;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -25%);\\n}\\n\\n.cartText[_ngcontent-%COMP%] {\\n  width: 302px;\\n  font-size: 28px;\\n  font-weight: 700;\\n  margin-bottom: 15px !important;\\n  margin-top: 25px !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500 !important;\\n  width: 293px !important;\\n  font-size: 14px;\\n}\\n\\n.cart-wait[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 300;\\n  color: #A3A3A3 !important;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .empty-cart[_ngcontent-%COMP%] {\\n    height: 300px;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}