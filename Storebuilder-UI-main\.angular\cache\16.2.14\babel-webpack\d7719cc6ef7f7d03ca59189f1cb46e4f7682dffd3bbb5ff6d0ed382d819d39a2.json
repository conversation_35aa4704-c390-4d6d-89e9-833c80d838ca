{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { io } from 'socket.io-client';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services/livestream.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"ngx-drag-scroll\";\nimport * as i6 from \"@angular/material/icon\";\nconst _c0 = [\"receiverVideo\"];\nfunction MerchantLivestreamDetailsComponent_div_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\")(1, \"img\", 30);\n    i0.ɵɵlistener(\"error\", function MerchantLivestreamDetailsComponent_div_18_span_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.errorHandler($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const p_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r8.cartListImage(p_r6.specsProducts[0].thumbnailImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MerchantLivestreamDetailsComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function MerchantLivestreamDetailsComponent_div_18_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const p_r6 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.navigateToProduct(p_r6.id));\n    });\n    i0.ɵɵtemplate(2, MerchantLivestreamDetailsComponent_div_18_span_2_Template, 2, 1, \"span\", 26);\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"h4\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const p_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", p_r6.specsProducts.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", p_r6 == null ? null : p_r6.description, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r6 == null ? null : p_r6.specsProducts[0].stockStatus);\n  }\n}\nfunction MerchantLivestreamDetailsComponent_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"LIVE CHAT WITH - \", ctx_r4.streamerName, \"\");\n  }\n}\nfunction MerchantLivestreamDetailsComponent_div_27_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const object_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", object_r14.message.userName, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", object_r14.message.message, \" \");\n  }\n}\nfunction MerchantLivestreamDetailsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, MerchantLivestreamDetailsComponent_div_27_ng_container_1_Template, 4, 2, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const object_r14 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", object_r14.message.room == ctx_r5.streamId);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"btn-disabled\": a0\n  };\n};\nexport class MerchantLivestreamDetailsComponent {\n  constructor(route, livestream, router, location) {\n    this.route = route;\n    this.livestream = livestream;\n    this.router = router;\n    this.location = location;\n    this.editFlag = false;\n    this.productList = [];\n    this.StreamDetail = {};\n    this.baseUrl = environment.apiEndPoint;\n    this.broadcastersList = [];\n    this.config = {\n      iceServers: [{\n        urls: ['stun:stun.l.google.com:19302']\n      }, {\n        urls: 'turn:coturn.paysky.io:3478',\n        credential: 'somepassword',\n        username: 'guest'\n      }]\n    };\n    this.peerConnection = new RTCPeerConnection(this.config);\n    this.messagesSubject = new BehaviorSubject([]);\n    this.messages$ = this.messagesSubject.asObservable();\n    this.messageInput = '';\n    this.isAvalable = true;\n    this.socket = io('https://dev-apps.paysky.io', {\n      path: '/onfido-node/socket.io'\n    });\n    // this.socket = io('http://localhost:80')\n    this.route.queryParams.subscribe(params => {\n      this.streamId = params['streamId'];\n      this.roomId = params['roomId'];\n      this.getStreamDetail(this.streamId);\n    });\n    this.userName = 'Guest-User' + Math.floor(100 + Math.random() * 900).toString();\n  }\n  ngOnInit() {\n    this.socket.emit('request-broadcasters');\n    this.socket.on('broadcasters-list', broadcastersList => {\n      this.broadcastersList = broadcastersList;\n      let result = this.broadcastersList.filter(val => val.includes(this.roomId));\n      if (result.length === 0) {\n        this.isAvalable = false;\n        this.location.back();\n      }\n    });\n    this.socket.on('isAvailable', val => {\n      this.isConnected = val;\n    });\n    this.socket.on('chat message', data => {\n      this.messagesSubject.next([...this.messagesSubject.value, data]);\n    });\n    this.socket.on('offer', (id, description) => {\n      this.peerConnection.setRemoteDescription(description).then(() => this.peerConnection.createAnswer()).then(sdp => this.peerConnection.setLocalDescription(sdp)).then(() => {\n        this.socket.emit('answer', id, this.peerConnection.localDescription);\n      });\n      this.peerConnection.ontrack = event => {\n        this.receiverVideo.nativeElement.srcObject = event.streams[0];\n        setTimeout(() => {\n          this.receiverVideo.nativeElement.play();\n        }, 1000);\n      };\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.socket.emit('candidate', id, event.candidate);\n        }\n      };\n    });\n    this.socket.on('candidate', (id, candidate) => {\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(e => console.error(e));\n    });\n    this.socket.on('connect', () => {\n      this.socket.emit('watcher');\n    });\n    this.socket.on('disconnect', () => {});\n    setTimeout(() => {\n      this.socket.emit('watcher', this.roomId);\n    }, 1000);\n  }\n  unloadNotification($event) {\n    this.socket.close();\n    this.peerConnection.close();\n  }\n  getStreamDetail(shopId) {\n    this.livestream.getLiveStreamDetailById(shopId).subscribe({\n      next: res => {\n        this.StreamDetail = res?.data;\n        this.streamerName = this.StreamDetail.products[0].sellerName;\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  errorHandler(event) {\n    event.target.src = 'https://alcodesbase.blob.core.windows.net/generic/sections-default-image.png';\n  }\n  cartListImage(img) {\n    return verifyImageURL(img, this.baseUrl);\n  }\n  navigateToProduct(_id) {\n    this.navigateUrl = `product/${_id}/1?tenantId=3&lang=en`;\n    const fullPath = this.location.prepareExternalUrl(this.navigateUrl);\n    window.open(fullPath, '_blank');\n  }\n  sendMessage() {\n    let increment = 0;\n    if (this.messageInput.trim() !== '') {\n      // Send the message to the server\n      this.socket.emit('chat message', {\n        message: this.messageInput,\n        room: this.streamId,\n        userName: this.userName\n      });\n      this.messageInput = ''; // Clear the input field\n    }\n  }\n  static #_ = this.ɵfac = function MerchantLivestreamDetailsComponent_Factory(t) {\n    return new (t || MerchantLivestreamDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.LiveStreamService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.Location));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MerchantLivestreamDetailsComponent,\n    selectors: [[\"app-merchant-livestream-details\"]],\n    viewQuery: function MerchantLivestreamDetailsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.receiverVideo = _t.first);\n      }\n    },\n    hostBindings: function MerchantLivestreamDetailsComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"beforeunload\", function MerchantLivestreamDetailsComponent_beforeunload_HostBindingHandler($event) {\n          return ctx.unloadNotification($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 34,\n    vars: 17,\n    consts: [[1, \"cart\", \"mt-8\", \"cart-top\"], [1, \"content-container\", \"my-3\", 2, \"margin-top\", \"180px !important\"], [1, \"content-container\", \"my-3\"], [1, \"row\", 2, \"justify-content\", \"center\"], [1, \"streamView\", 2, \"width\", \"70%\"], [\"width\", \"100%\", \"height\", \"500\", \"playsinline\", \"\", \"controls\", \"\", \"autoplay\", \"\", 2, \"object-fit\", \"cover\"], [\"remoteVideoone\", \"\", \"receiverVideo\", \"\"], [1, \"streamDetails\"], [1, \"description-holder\"], [1, \"selected-product-card\"], [3, \"scroll-x-wheel-enabled\"], [\"nav\", \"\"], [\"drag-scroll-item\", \"\", 4, \"ngFor\", \"ngForOf\"], [1, \"streamView\", 2, \"width\", \"30%\"], [1, \"view-holder\"], [1, \"instruction-View-second\"], [1, \"p-2\", \"headerMain\", 2, \"height\", \"62px !important\"], [1, \"checklist-title\"], [\"class\", \"checklist\", 4, \"ngIf\"], [1, \"message-box\"], [\"class\", \"message-show\", 4, \"ngFor\", \"ngForOf\"], [1, \"message-send-section\"], [\"placeholder\", \"Type your message...\", 1, \"message-textarea\", 3, \"ngModel\", \"disabled\", \"ngClass\", \"ngModelChange\", \"keyup.enter\"], [\"mat-raised-button\", \"\", 1, \"send-button\", \"add-product-btn\", \"send-button-setting\", 3, \"disabled\", \"ngClass\", \"click\"], [\"drag-scroll-item\", \"\"], [1, \"card\", \"shadow-sm\", 3, \"click\"], [4, \"ngIf\"], [1, \"card-body\"], [1, \"card-title\"], [1, \"description-holder\", 2, \"margin-bottom\", \"0\"], [\"alt\", \"...\", 1, \"card-img-top\", 3, \"src\", \"error\"], [1, \"checklist\"], [1, \"message-show\"]],\n    template: function MerchantLivestreamDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelementContainerStart(1);\n        i0.ɵɵelement(2, \"div\", 1);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 2);\n        i0.ɵɵelementContainerStart(4);\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4);\n        i0.ɵɵelement(7, \"video\", 5, 6);\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"h1\");\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"p\", 8);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"drag-scroll\", 10, 11);\n        i0.ɵɵtemplate(18, MerchantLivestreamDetailsComponent_div_18_Template, 10, 4, \"div\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17)(24, \"strong\");\n        i0.ɵɵtemplate(25, MerchantLivestreamDetailsComponent_p_25_Template, 2, 1, \"p\", 18);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(26, \"div\", 19);\n        i0.ɵɵtemplate(27, MerchantLivestreamDetailsComponent_div_27_Template, 2, 1, \"div\", 20);\n        i0.ɵɵpipe(28, \"async\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"section\", 21)(30, \"textarea\", 22);\n        i0.ɵɵlistener(\"ngModelChange\", function MerchantLivestreamDetailsComponent_Template_textarea_ngModelChange_30_listener($event) {\n          return ctx.messageInput = $event;\n        })(\"keyup.enter\", function MerchantLivestreamDetailsComponent_Template_textarea_keyup_enter_30_listener() {\n          return ctx.sendMessage();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function MerchantLivestreamDetailsComponent_Template_button_click_31_listener() {\n          return ctx.sendMessage();\n        });\n        i0.ɵɵelementStart(32, \"mat-icon\");\n        i0.ɵɵtext(33, \"send\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(ctx.StreamDetail == null ? null : ctx.StreamDetail.title);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.StreamDetail == null ? null : ctx.StreamDetail.description);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"scroll-x-wheel-enabled\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.StreamDetail.products);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.streamerName);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(28, 11, ctx.messages$));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.messageInput)(\"disabled\", !ctx.isAvalable)(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, !ctx.isAvalable));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", !ctx.isAvalable)(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, !ctx.isAvalable));\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.DragScrollComponent, i5.DragScrollItemDirective, i6.MatIcon, i3.AsyncPipe],\n    styles: [\"[_nghost-%COMP%] {\\n  width: 100%;\\n}\\n\\n.wrapper[_ngcontent-%COMP%] {\\n  padding: 44px 44px 44px 50px !important;\\n}\\n.wrapper[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  font-size: 35px;\\n  font-weight: 700;\\n  font-family: var(--bold-font);\\n  margin-bottom: 40px;\\n  padding-top: 3px;\\n}\\n.wrapper[_ngcontent-%COMP%]   .select-category[_ngcontent-%COMP%] {\\n  width: 268px;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n  color: #323232;\\n  font-size: 12px;\\n}\\n.wrapper[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #b9b9b9 !important;\\n  border-radius: 0px !important;\\n  height: 50px !important;\\n  background: #f5f5f5 0% 0% no-repeat padding-box;\\n  padding-left: 11px;\\n  padding-top: 8px;\\n  width: 268px;\\n  margin-left: 10px;\\n}\\n.wrapper[_ngcontent-%COMP%]     .p-button.p-button-icon-only {\\n  width: 3rem;\\n  padding: 0.75rem 0;\\n  bottom: 24px;\\n  border: none;\\n  background: #f5f5f5 0% 0% no-repeat padding-box;\\n  color: black;\\n}\\n.wrapper[_ngcontent-%COMP%]    .p-button:enabled:hover {\\n  background: none;\\n  color: #000;\\n  border-color: #f5f5f5;\\n}\\n.wrapper[_ngcontent-%COMP%]     input:focus-visible {\\n  outline: none;\\n  border-radius: 0px;\\n}\\n.wrapper[_ngcontent-%COMP%]     .p-button:enabled:active {\\n  background: none;\\n  color: #000;\\n  border-color: #f5f5f5;\\n}\\n.wrapper[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%] {\\n  color: #323232;\\n  font-size: 12px;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n.wrapper[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.wrapper[_ngcontent-%COMP%]     .p-calendar {\\n  width: 100% !important;\\n}\\n.wrapper[_ngcontent-%COMP%]   .custom-calendar[_ngcontent-%COMP%]     .p-calendar .p-inputtext {\\n  width: 100%;\\n  background: transparent !important;\\n  border: 0 none !important;\\n  padding: 0 !important;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n  font-style: normal !important;\\n  font-weight: 400 !important;\\n  font-size: 12px !important;\\n  line-height: 16px !important;\\n  color: #323232;\\n  bottom: 13px;\\n  position: inherit;\\n}\\n.wrapper[_ngcontent-%COMP%]     .p-inputtext:enabled:focus {\\n  box-shadow: none;\\n}\\n.wrapper[_ngcontent-%COMP%]     .p-button:focus {\\n  box-shadow: none !important;\\n}\\n.wrapper[_ngcontent-%COMP%]     .catalog-filters .ng-select .ng-select-container {\\n  border-bottom: 1px solid #b9b9b9 !important;\\n}\\n\\n.streamView[_ngcontent-%COMP%] {\\n  background-color: #f8f8f8;\\n  border-color: #e7e7e7;\\n  border-radius: 2px;\\n  box-shadow: 0px 1px 5px 2px rgba(194, 194, 194, 0.35);\\n}\\n\\n.instructionView[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  background-color: #f8f8f8;\\n  border-color: #e7e7e7;\\n  border-radius: 2px;\\n  box-shadow: 0px 1px 5px 2px rgba(194, 194, 194, 0.35);\\n  margin: 8px;\\n}\\n\\n.headerMain[_ngcontent-%COMP%] {\\n  background-color: #f8f8f8;\\n  border-color: #e7e7e7;\\n  border-radius: 2px;\\n  box-shadow: 0px 1px 5px 2px rgba(194, 194, 194, 0.35);\\n  display: flex;\\n  align-items: center;\\n  height: 80px;\\n}\\n\\n.offline_pin[_ngcontent-%COMP%] {\\n  transform: scale(1.42);\\n  margin-right: 4px;\\n}\\n\\n.headerStatus[_ngcontent-%COMP%] {\\n  font-size: 30px;\\n  margin-top: 16px;\\n}\\n\\n.statusHolder[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  width: 180px;\\n  border-right: 2px solid;\\n}\\n\\n.Description[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.checklist[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n  align-items: center;\\n  display: contents;\\n}\\n\\n.checklist-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: contents;\\n}\\n\\n.listdiv[_ngcontent-%COMP%]   mat-list[_ngcontent-%COMP%]   mat-list-item[_ngcontent-%COMP%] {\\n  height: 58px;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n.checked[_ngcontent-%COMP%] {\\n  content: \\\"\\\\f058\\\";\\n  transform: rotate(360deg);\\n  transition: transform 0.5s ease-in-out;\\n  -o-transition: transform 0.5s ease-in-out;\\n  color: rgb(103, 207, 159);\\n  opacity: 1;\\n}\\n\\n.unchecked[_ngcontent-%COMP%] {\\n  color: rgb(191, 201, 196);\\n}\\n\\n.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: rotate(-360deg);\\n}\\n\\n.checklist-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n  .listdiv mat-list mat-list-item .mdc-list-item__primary-text {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.selected-product-card[_ngcontent-%COMP%] {\\n  height: 283px;\\n  padding-right: 15px;\\n}\\n\\n.add-product-btn[_ngcontent-%COMP%] {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  min-width: 150px;\\n  max-width: 250px;\\n  height: 40px;\\n  background: #004f71 0% 0% no-repeat padding-box;\\n  border-radius: 28px;\\n  opacity: 1;\\n  text-align: center;\\n  letter-spacing: 0px;\\n  color: #ffffff;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n}\\n\\n.stepper-holder[_ngcontent-%COMP%] {\\n  min-height: 581px;\\n  background: white;\\n  height: 100%;\\n}\\n\\n  mat-stepper div div mat-step-header {\\n  background-color: transparent !important;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 14rem;\\n  width: 12rem;\\n  margin-right: 0.5rem;\\n  margin-left: 0.5rem;\\n  margin-top: 0.5rem;\\n  border-radius: 0.75rem;\\n}\\n\\n.card[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0.75rem;\\n  border-top-right-radius: 0.75rem;\\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  font-family: \\\"Noto Sans SC\\\", sans-serif;\\n}\\n\\n.description-holder[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n}\\n\\n.card-price[_ngcontent-%COMP%] {\\n  font-size: medium;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.card-img-top[_ngcontent-%COMP%] {\\n  height: 120px;\\n}\\n\\ndrag-scroll[_ngcontent-%COMP%] {\\n  height: 15rem;\\n  width: 100%;\\n}\\n\\n.button-options[_ngcontent-%COMP%] {\\n  text-align: end;\\n  padding-right: 17px;\\n}\\n\\n.example-margin[_ngcontent-%COMP%] {\\n  position: absolute;\\n}\\n\\n.apply-holder[_ngcontent-%COMP%] {\\n  padding: 0px 0px 6px 9px;\\n}\\n\\n.streamDetails[_ngcontent-%COMP%] {\\n  padding: 0 20px;\\n}\\n\\n.message-box[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  background-color: rgba(223, 223, 223, 0.7);\\n  height: 501px;\\n  overflow-y: scroll;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  min-height: 500px;\\n}\\n\\ntextarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  outline: none;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::placeholder {\\n  font-size: 16px;\\n  opacity: 0.7;\\n}\\n\\n.view-holder[_ngcontent-%COMP%] {\\n  box-shadow: 0px 1px 5px 2px rgba(194, 194, 194, 0.35);\\n  margin: 8px;\\n  margin-bottom: 0;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 14px;\\n  right: 16px;\\n}\\n\\n.message-send-section[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.message-textarea[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n\\n.message-show[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  padding-bottom: 0;\\n  padding-top: 6px;\\n}\\n\\n.send-button-setting[_ngcontent-%COMP%] {\\n  min-width: 63px !important;\\n  height: 30px !important;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 10px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  -webkit-box-shadow: inset 0 0 4px rgba(117, 116, 116, 0.3);\\n  border-radius: 8px;\\n  margin-top: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  border-radius: 10px;\\n  background-color: rgb(248, 188, 188);\\n}\\n\\n@media (min-width: 768px) {\\n  .adjustable[_ngcontent-%COMP%] {\\n    margin-top: -145px;\\n  }\\n}\\n@media only screen and (max-width: 500px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    margin: 0 0 273px 0 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport function verifyImageURL(url, baseUrl) {\n  if (url) {\n    if (baseUrl[-1] == '/') {\n      baseUrl = baseUrl.substring(-1);\n    }\n    if (url.startsWith('/')) url = url.substring(1);\n    const sub = url.substring(0, url.indexOf('/'));\n    if (sub.toLowerCase().includes('images')) return `${baseUrl}${url}`;else {\n      return `${baseUrl}/Images/${url}`;\n    }\n  } else {\n    return '';\n  }\n}", "map": {"version": 3, "names": ["environment", "io", "BehaviorSubject", "i0", "ɵɵelementStart", "ɵɵlistener", "MerchantLivestreamDetailsComponent_div_18_span_2_Template_img_error_1_listener", "$event", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r8", "cartListImage", "p_r6", "specsProducts", "thumbnailImage", "ɵɵsanitizeUrl", "MerchantLivestreamDetailsComponent_div_18_Template_div_click_1_listener", "restoredCtx", "_r13", "$implicit", "ctx_r12", "navigateToProduct", "id", "ɵɵtemplate", "MerchantLivestreamDetailsComponent_div_18_span_2_Template", "ɵɵtext", "length", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "description", "stockStatus", "ctx_r4", "streamerName", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "object_r14", "message", "userName", "MerchantLivestreamDetailsComponent_div_27_ng_container_1_Template", "room", "ctx_r5", "streamId", "MerchantLivestreamDetailsComponent", "constructor", "route", "livestream", "router", "location", "editFlag", "productList", "StreamDetail", "baseUrl", "apiEndPoint", "broadcastersList", "config", "iceServers", "urls", "credential", "username", "peerConnection", "RTCPeerConnection", "messagesSubject", "messages$", "asObservable", "messageInput", "isAvalable", "socket", "path", "queryParams", "subscribe", "params", "roomId", "getStreamDetail", "Math", "floor", "random", "toString", "ngOnInit", "emit", "on", "result", "filter", "val", "includes", "back", "isConnected", "data", "next", "value", "setRemoteDescription", "then", "createAnswer", "sdp", "setLocalDescription", "localDescription", "ontrack", "event", "<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "srcObject", "streams", "setTimeout", "play", "onicecandidate", "candidate", "addIceCandidate", "RTCIceCandidate", "catch", "e", "console", "error", "unloadNotification", "close", "shopId", "getLiveStreamDetailById", "res", "products", "sellerName", "err", "target", "src", "img", "verifyImageURL", "_id", "navigateUrl", "fullPath", "prepareExternalUrl", "window", "open", "sendMessage", "increment", "trim", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "LiveStreamService", "Router", "i3", "Location", "_2", "selectors", "viewQuery", "MerchantLivestreamDetailsComponent_Query", "rf", "ctx", "ɵɵresolveWindow", "ɵɵelement", "MerchantLivestreamDetailsComponent_div_18_Template", "MerchantLivestreamDetailsComponent_p_25_Template", "MerchantLivestreamDetailsComponent_div_27_Template", "MerchantLivestreamDetailsComponent_Template_textarea_ngModelChange_30_listener", "MerchantLivestreamDetailsComponent_Template_textarea_keyup_enter_30_listener", "MerchantLivestreamDetailsComponent_Template_button_click_31_listener", "title", "ɵɵpipeBind1", "ɵɵpureFunction1", "_c1", "url", "substring", "startsWith", "sub", "indexOf", "toLowerCase"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchant-livestream\\merchant-livestream-details\\merchant-livestream-details.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchant-livestream\\merchant-livestream-details\\merchant-livestream-details.component.html"], "sourcesContent": ["import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { LiveStreamService } from '@core/services/livestream.service';\r\nimport { environment } from '@environments/environment';\r\nimport { io } from 'socket.io-client';\r\nimport { Location } from '@angular/common';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-merchant-livestream-details',\r\n  templateUrl: './merchant-livestream-details.component.html',\r\n  styleUrls: ['./merchant-livestream-details.component.scss'],\r\n})\r\nexport class MerchantLivestreamDetailsComponent {\r\n  @ViewChild('receiverVideo') receiverVideo!: ElementRef<HTMLVideoElement>;\r\n  editFlag: boolean = false;\r\n  productList: any = [];\r\n\r\n  StreamDetail: any = {};\r\n  routeId: any;\r\n  baseUrl = environment.apiEndPoint;\r\n  roomId: any;\r\n  broadcastersList: string[] = [];\r\n  config = {\r\n    iceServers: [\r\n      {\r\n        urls: ['stun:stun.l.google.com:19302'],\r\n      },\r\n      {\r\n        urls: 'turn:coturn.paysky.io:3478',\r\n        credential: 'somepassword',\r\n        username: 'guest',\r\n      },\r\n    ],\r\n  };\r\n  peerConnection: any = new RTCPeerConnection(this.config);\r\n  socket: any;\r\n  isConnected: string;\r\n  navigateUrl: string;\r\n  private messagesSubject = new BehaviorSubject<any[]>([]);\r\n  messages$ = this.messagesSubject.asObservable();\r\n  messageInput = '';\r\n  streamId: number;\r\n  isAvalable: boolean=true;\r\n  userName: string;\r\n  streamerName: any;\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private livestream: LiveStreamService,\r\n    private router: Router,\r\n    private location: Location\r\n  ) {\r\n    this.socket = io('https://dev-apps.paysky.io', {\r\n      path: '/onfido-node/socket.io',\r\n    });\r\n    // this.socket = io('http://localhost:80')\r\n    this.route.queryParams.subscribe((params) => {\r\n       this.streamId = params['streamId'];\r\n      this.roomId = params['roomId'];\r\n      this.getStreamDetail(this.streamId);\r\n    });\r\n    this.userName = 'Guest-User' + Math.floor(100 + Math.random() * 900).toString();\r\n  }\r\n  ngOnInit(): void {\r\n    this.socket.emit('request-broadcasters');\r\n    this.socket.on('broadcasters-list', (broadcastersList: string[]) => {\r\n      this.broadcastersList = broadcastersList;\r\n      let result  = this.broadcastersList.filter((val) => val.includes(this.roomId));\r\n      if(result.length === 0){\r\n       this.isAvalable = false;\r\n       this.location.back();\r\n\r\n      }\r\n\r\n    });\r\n    this.socket.on('isAvailable', (val: string) => {\r\n      this.isConnected = val;\r\n    });\r\n    this.socket.on('chat message', (data: any) => {\r\n      this.messagesSubject.next([...this.messagesSubject.value, data]);\r\n    });\r\n    this.socket.on('offer', (id: any, description: any) => {\r\n      this.peerConnection\r\n        .setRemoteDescription(description)\r\n        .then(() => this.peerConnection.createAnswer())\r\n        .then((sdp: any) => this.peerConnection.setLocalDescription(sdp))\r\n        .then(() => {\r\n          this.socket.emit('answer', id, this.peerConnection.localDescription);\r\n        });\r\n      this.peerConnection.ontrack = (event: any) => {\r\n        this.receiverVideo.nativeElement.srcObject = event.streams[0];\r\n        setTimeout(() => {\r\n          this.receiverVideo.nativeElement.play();\r\n        }, 1000);\r\n\r\n      };\r\n      this.peerConnection.onicecandidate = (event: any) => {\r\n        if (event.candidate) {\r\n          this.socket.emit('candidate', id, event.candidate);\r\n        }\r\n      };\r\n    });\r\n\r\n    this.socket.on('candidate', (id: any, candidate: RTCIceCandidateInit) => {\r\n      this.peerConnection\r\n        .addIceCandidate(new RTCIceCandidate(candidate))\r\n        .catch((e: any) => console.error(e));\r\n    });\r\n\r\n    this.socket.on('connect', () => {\r\n      this.socket.emit('watcher');\r\n    });\r\n    this.socket.on('disconnect', () => {\r\n    })\r\n\r\n    setTimeout(() => {\r\n      this.socket.emit('watcher', this.roomId);\r\n    }, 1000);\r\n\r\n  }\r\n  @HostListener('window:beforeunload', ['$event'])\r\n  unloadNotification($event: any): void {\r\n    this.socket.close();\r\n    this.peerConnection.close();\r\n  }\r\n\r\n  getStreamDetail(shopId: any) {\r\n    this.livestream.getLiveStreamDetailById(shopId).subscribe({\r\n      next: (res: any) => {\r\n        this.StreamDetail = res?.data;\r\n        this.streamerName = this.StreamDetail.products[0].sellerName\r\n\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n      },\r\n    });\r\n  }\r\n\r\n  errorHandler(event: any) {\r\n    event.target.src ='https://alcodesbase.blob.core.windows.net/generic/sections-default-image.png';\r\n  }\r\n\r\n  cartListImage(img: any) {\r\n    return verifyImageURL(img, this.baseUrl);\r\n  }\r\n  navigateToProduct(_id:number){\r\n   this.navigateUrl = `product/${_id}/1?tenantId=3&lang=en`\r\n   const fullPath = this.location.prepareExternalUrl(this.navigateUrl);\r\n   window.open(fullPath, '_blank');\r\n  }\r\n  sendMessage() {\r\n    let increment = 0\r\n    if (this.messageInput.trim() !== '') {\r\n      // Send the message to the server\r\n      this.socket.emit('chat message', { message: this.messageInput, room: this.streamId , userName : this.userName});\r\n      this.messageInput = ''; // Clear the input field\r\n    }\r\n  }\r\n}\r\nexport function verifyImageURL(url: string, baseUrl: string): string {\r\n  if (url) {\r\n    if (baseUrl[-1] == '/') {\r\n      baseUrl = baseUrl.substring(-1);\r\n    }\r\n    if (url.startsWith('/')) url = url.substring(1);\r\n    const sub = url.substring(0, url.indexOf('/'));\r\n    if (sub.toLowerCase().includes('images')) return `${baseUrl}${url}`;\r\n    else {\r\n      return `${baseUrl}/Images/${url}`;\r\n    }\r\n  } else {\r\n    return '';\r\n  }\r\n}\r\n", "<section class=\"cart mt-8 cart-top\">\r\n  <ng-container>\r\n    <div class=\"content-container my-3\" style=\"margin-top: 180px !important\">\r\n    </div>\r\n  </ng-container>\r\n</section>\r\n<div class=\"content-container my-3\" >\r\n\r\n  <ng-container>\r\n    <div class=\"row\" style=\"justify-content: center\">\r\n      <div class=\"streamView\" style=\"width: 70%\">\r\n        <video\r\n          #remoteVideoone\r\n          width=\"100%\"\r\n          height=\"500\"\r\n          style=\"object-fit: cover\"\r\n          playsinline\r\n          #receiverVide<PERSON>\r\n          controls\r\n          autoplay\r\n        ></video>\r\n\r\n        <div class=\"streamDetails\">\r\n          <h1>{{ StreamDetail?.title }}</h1>\r\n          <p class=\"description-holder\">{{ StreamDetail?.description }}</p>\r\n        </div>\r\n        <div class=\"selected-product-card\">\r\n          <!-- Product with show up here with horizontal scroll -->\r\n          <drag-scroll #nav [scroll-x-wheel-enabled]=\"true\">\r\n            <div\r\n              *ngFor=\"let p of StreamDetail.products; let i = index\"\r\n              drag-scroll-item\r\n            >\r\n              <div class=\"card shadow-sm\" (click)=\"navigateToProduct(p.id)\">\r\n                <span *ngIf=\"p.specsProducts.length > 0\"\r\n                  ><img\r\n                    (error)=\"errorHandler($event)\"\r\n                    [src]=\"cartListImage(p.specsProducts[0].thumbnailImage)\"\r\n                    class=\"card-img-top\"\r\n                    alt=\"...\"\r\n                /></span>\r\n\r\n                <div class=\"card-body\">\r\n                  <h4 class=\"card-title\">{{ p.name }}</h4>\r\n\r\n                  <p class=\"description-holder\" style=\"margin-bottom: 0\">\r\n                    {{ p?.description }}\r\n                  </p>\r\n                  <p>{{ p?.specsProducts[0].stockStatus }}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </drag-scroll>\r\n        </div>\r\n      </div>\r\n      <div class=\"streamView\" style=\"width: 30%\">\r\n        <div class=\"view-holder\">\r\n          <div class=\"instruction-View-second\">\r\n            <div class=\"p-2 headerMain\" style=\"height: 62px !important\">\r\n              <div class=\"checklist-title\">\r\n                <strong> <p class=\"checklist\" *ngIf=\"streamerName\">LIVE CHAT WITH - {{streamerName}}</p></strong>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"message-box\">\r\n            <div class=\"message-show\" *ngFor=\"let object of messages$ | async\">\r\n              <ng-container *ngIf=\"object.message.room == streamId\">\r\n                <strong >{{object.message.userName}}:</strong>\r\n                {{ object.message.message }}\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n          <section class=\"message-send-section\">\r\n            <textarea\r\n              class=\"message-textarea\"\r\n              [(ngModel)]=\"messageInput\"\r\n              placeholder=\"Type your message...\"\r\n              (keyup.enter)=\"sendMessage()\"\r\n              [disabled]=\"!isAvalable\"\r\n              [ngClass]=\"{ 'btn-disabled': !isAvalable }\"\r\n            ></textarea>\r\n            <button\r\n              class=\"send-button add-product-btn send-button-setting\"\r\n              [disabled]=\"!isAvalable\"\r\n              [ngClass]=\"{ 'btn-disabled': !isAvalable }\"\r\n              (click)=\"sendMessage()\"\r\n              mat-raised-button\r\n            >\r\n            <mat-icon>send</mat-icon>\r\n            </button>\r\n          </section>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</div>\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,2BAA2B;AACvD,SAASC,EAAE,QAAQ,kBAAkB;AAErC,SAASC,eAAe,QAAQ,MAAM;;;;;;;;;;;;IC4BtBC,EAAA,CAAAC,cAAA,WACG;IACCD,EAAA,CAAAE,UAAA,mBAAAC,+EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAD/BJ,EAAA,CAAAW,YAAA,EAKD;;;;;IAHEX,EAAA,CAAAY,SAAA,GAAwD;IAAxDZ,EAAA,CAAAa,UAAA,QAAAC,MAAA,CAAAC,aAAA,CAAAC,IAAA,CAAAC,aAAA,IAAAC,cAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAwD;;;;;;IARhEnB,EAAA,CAAAC,cAAA,cAGC;IAC6BD,EAAA,CAAAE,UAAA,mBAAAkB,wEAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,IAAA;MAAA,MAAAN,IAAA,GAAAK,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAe,OAAA,CAAAC,iBAAA,CAAAT,IAAA,CAAAU,EAAA,CAAuB;IAAA,EAAC;IAC3D1B,EAAA,CAAA2B,UAAA,IAAAC,yDAAA,mBAMS;IAET5B,EAAA,CAAAC,cAAA,cAAuB;IACED,EAAA,CAAA6B,MAAA,GAAY;IAAA7B,EAAA,CAAAW,YAAA,EAAK;IAExCX,EAAA,CAAAC,cAAA,YAAuD;IACrDD,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAA6B,MAAA,GAAqC;IAAA7B,EAAA,CAAAW,YAAA,EAAI;;;;IAdvCX,EAAA,CAAAY,SAAA,GAAgC;IAAhCZ,EAAA,CAAAa,UAAA,SAAAG,IAAA,CAAAC,aAAA,CAAAa,MAAA,KAAgC;IASd9B,EAAA,CAAAY,SAAA,GAAY;IAAZZ,EAAA,CAAA+B,iBAAA,CAAAf,IAAA,CAAAgB,IAAA,CAAY;IAGjChC,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAiC,kBAAA,MAAAjB,IAAA,kBAAAA,IAAA,CAAAkB,WAAA,MACF;IACGlC,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAA+B,iBAAA,CAAAf,IAAA,kBAAAA,IAAA,CAAAC,aAAA,IAAAkB,WAAA,CAAqC;;;;;IAYjCnC,EAAA,CAAAC,cAAA,YAA0C;IAAAD,EAAA,CAAA6B,MAAA,GAAiC;IAAA7B,EAAA,CAAAW,YAAA,EAAI;;;;IAArCX,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAiC,kBAAA,sBAAAG,MAAA,CAAAC,YAAA,KAAiC;;;;;IAMtFrC,EAAA,CAAAsC,uBAAA,GAAsD;IACpDtC,EAAA,CAAAC,cAAA,aAAS;IAAAD,EAAA,CAAA6B,MAAA,GAA4B;IAAA7B,EAAA,CAAAW,YAAA,EAAS;IAC9CX,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAuC,qBAAA,EAAe;;;;IAFJvC,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAiC,kBAAA,KAAAO,UAAA,CAAAC,OAAA,CAAAC,QAAA,MAA4B;IACrC1C,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAiC,kBAAA,MAAAO,UAAA,CAAAC,OAAA,CAAAA,OAAA,MACF;;;;;IAJFzC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAA2B,UAAA,IAAAgB,iEAAA,2BAGe;IACjB3C,EAAA,CAAAW,YAAA,EAAM;;;;;IAJWX,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAa,UAAA,SAAA2B,UAAA,CAAAC,OAAA,CAAAG,IAAA,IAAAC,MAAA,CAAAC,QAAA,CAAqC;;;;;;;;ADrDlE,OAAM,MAAOC,kCAAkC;EAiC7CC,YACUC,KAAqB,EACrBC,UAA6B,EAC7BC,MAAc,EACdC,QAAkB;IAHlB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnClB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAQ,EAAE;IAErB,KAAAC,YAAY,GAAQ,EAAE;IAEtB,KAAAC,OAAO,GAAG3D,WAAW,CAAC4D,WAAW;IAEjC,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,MAAM,GAAG;MACPC,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,CAAC,8BAA8B;OACtC,EACD;QACEA,IAAI,EAAE,4BAA4B;QAClCC,UAAU,EAAE,cAAc;QAC1BC,QAAQ,EAAE;OACX;KAEJ;IACD,KAAAC,cAAc,GAAQ,IAAIC,iBAAiB,CAAC,IAAI,CAACN,MAAM,CAAC;IAIhD,KAAAO,eAAe,GAAG,IAAInE,eAAe,CAAQ,EAAE,CAAC;IACxD,KAAAoE,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;IAC/C,KAAAC,YAAY,GAAG,EAAE;IAEjB,KAAAC,UAAU,GAAU,IAAI;IAStB,IAAI,CAACC,MAAM,GAAGzE,EAAE,CAAC,4BAA4B,EAAE;MAC7C0E,IAAI,EAAE;KACP,CAAC;IACF;IACA,IAAI,CAACvB,KAAK,CAACwB,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MACzC,IAAI,CAAC7B,QAAQ,GAAG6B,MAAM,CAAC,UAAU,CAAC;MACnC,IAAI,CAACC,MAAM,GAAGD,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC/B,QAAQ,CAAC;IACrC,CAAC,CAAC;IACF,IAAI,CAACJ,QAAQ,GAAG,YAAY,GAAGoC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC,CAACC,QAAQ,EAAE;EACjF;EACAC,QAAQA,CAAA;IACN,IAAI,CAACX,MAAM,CAACY,IAAI,CAAC,sBAAsB,CAAC;IACxC,IAAI,CAACZ,MAAM,CAACa,EAAE,CAAC,mBAAmB,EAAG1B,gBAA0B,IAAI;MACjE,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI2B,MAAM,GAAI,IAAI,CAAC3B,gBAAgB,CAAC4B,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACC,QAAQ,CAAC,IAAI,CAACZ,MAAM,CAAC,CAAC;MAC9E,IAAGS,MAAM,CAACvD,MAAM,KAAK,CAAC,EAAC;QACtB,IAAI,CAACwC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAClB,QAAQ,CAACqC,IAAI,EAAE;;IAIvB,CAAC,CAAC;IACF,IAAI,CAAClB,MAAM,CAACa,EAAE,CAAC,aAAa,EAAGG,GAAW,IAAI;MAC5C,IAAI,CAACG,WAAW,GAAGH,GAAG;IACxB,CAAC,CAAC;IACF,IAAI,CAAChB,MAAM,CAACa,EAAE,CAAC,cAAc,EAAGO,IAAS,IAAI;MAC3C,IAAI,CAACzB,eAAe,CAAC0B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC1B,eAAe,CAAC2B,KAAK,EAAEF,IAAI,CAAC,CAAC;IAClE,CAAC,CAAC;IACF,IAAI,CAACpB,MAAM,CAACa,EAAE,CAAC,OAAO,EAAE,CAAC1D,EAAO,EAAEQ,WAAgB,KAAI;MACpD,IAAI,CAAC8B,cAAc,CAChB8B,oBAAoB,CAAC5D,WAAW,CAAC,CACjC6D,IAAI,CAAC,MAAM,IAAI,CAAC/B,cAAc,CAACgC,YAAY,EAAE,CAAC,CAC9CD,IAAI,CAAEE,GAAQ,IAAK,IAAI,CAACjC,cAAc,CAACkC,mBAAmB,CAACD,GAAG,CAAC,CAAC,CAChEF,IAAI,CAAC,MAAK;QACT,IAAI,CAACxB,MAAM,CAACY,IAAI,CAAC,QAAQ,EAAEzD,EAAE,EAAE,IAAI,CAACsC,cAAc,CAACmC,gBAAgB,CAAC;MACtE,CAAC,CAAC;MACJ,IAAI,CAACnC,cAAc,CAACoC,OAAO,GAAIC,KAAU,IAAI;QAC3C,IAAI,CAACC,aAAa,CAACC,aAAa,CAACC,SAAS,GAAGH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;QAC7DC,UAAU,CAAC,MAAK;UACd,IAAI,CAACJ,aAAa,CAACC,aAAa,CAACI,IAAI,EAAE;QACzC,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC;MACD,IAAI,CAAC3C,cAAc,CAAC4C,cAAc,GAAIP,KAAU,IAAI;QAClD,IAAIA,KAAK,CAACQ,SAAS,EAAE;UACnB,IAAI,CAACtC,MAAM,CAACY,IAAI,CAAC,WAAW,EAAEzD,EAAE,EAAE2E,KAAK,CAACQ,SAAS,CAAC;;MAEtD,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACtC,MAAM,CAACa,EAAE,CAAC,WAAW,EAAE,CAAC1D,EAAO,EAAEmF,SAA8B,KAAI;MACtE,IAAI,CAAC7C,cAAc,CAChB8C,eAAe,CAAC,IAAIC,eAAe,CAACF,SAAS,CAAC,CAAC,CAC/CG,KAAK,CAAEC,CAAM,IAAKC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,IAAI,CAAC1C,MAAM,CAACa,EAAE,CAAC,SAAS,EAAE,MAAK;MAC7B,IAAI,CAACb,MAAM,CAACY,IAAI,CAAC,SAAS,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAACZ,MAAM,CAACa,EAAE,CAAC,YAAY,EAAE,MAAK,CAClC,CAAC,CAAC;IAEFsB,UAAU,CAAC,MAAK;MACd,IAAI,CAACnC,MAAM,CAACY,IAAI,CAAC,SAAS,EAAE,IAAI,CAACP,MAAM,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;EAEV;EAEAwC,kBAAkBA,CAAChH,MAAW;IAC5B,IAAI,CAACmE,MAAM,CAAC8C,KAAK,EAAE;IACnB,IAAI,CAACrD,cAAc,CAACqD,KAAK,EAAE;EAC7B;EAEAxC,eAAeA,CAACyC,MAAW;IACzB,IAAI,CAACpE,UAAU,CAACqE,uBAAuB,CAACD,MAAM,CAAC,CAAC5C,SAAS,CAAC;MACxDkB,IAAI,EAAG4B,GAAQ,IAAI;QACjB,IAAI,CAACjE,YAAY,GAAGiE,GAAG,EAAE7B,IAAI;QAC7B,IAAI,CAACtD,YAAY,GAAG,IAAI,CAACkB,YAAY,CAACkE,QAAQ,CAAC,CAAC,CAAC,CAACC,UAAU;MAE9D,CAAC;MACDP,KAAK,EAAGQ,GAAQ,IAAI;QAClBT,OAAO,CAACC,KAAK,CAACQ,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAjH,YAAYA,CAAC2F,KAAU;IACrBA,KAAK,CAACuB,MAAM,CAACC,GAAG,GAAE,8EAA8E;EAClG;EAEA9G,aAAaA,CAAC+G,GAAQ;IACpB,OAAOC,cAAc,CAACD,GAAG,EAAE,IAAI,CAACtE,OAAO,CAAC;EAC1C;EACA/B,iBAAiBA,CAACuG,GAAU;IAC3B,IAAI,CAACC,WAAW,GAAG,WAAWD,GAAG,uBAAuB;IACxD,MAAME,QAAQ,GAAG,IAAI,CAAC9E,QAAQ,CAAC+E,kBAAkB,CAAC,IAAI,CAACF,WAAW,CAAC;IACnEG,MAAM,CAACC,IAAI,CAACH,QAAQ,EAAE,QAAQ,CAAC;EAChC;EACAI,WAAWA,CAAA;IACT,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAI,IAAI,CAAClE,YAAY,CAACmE,IAAI,EAAE,KAAK,EAAE,EAAE;MACnC;MACA,IAAI,CAACjE,MAAM,CAACY,IAAI,CAAC,cAAc,EAAE;QAAE1C,OAAO,EAAE,IAAI,CAAC4B,YAAY;QAAEzB,IAAI,EAAE,IAAI,CAACE,QAAQ;QAAGJ,QAAQ,EAAG,IAAI,CAACA;MAAQ,CAAC,CAAC;MAC/G,IAAI,CAAC2B,YAAY,GAAG,EAAE,CAAC,CAAC;;EAE5B;EAAC,QAAAoE,CAAA,G;qBAjJU1F,kCAAkC,EAAA/C,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAI,MAAA,GAAA/I,EAAA,CAAA0I,iBAAA,CAAAM,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlCnG,kCAAkC;IAAAoG,SAAA;IAAAC,SAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;iBAAlCC,GAAA,CAAAnC,kBAAA,CAAAhH,MAAA,CAA0B;QAAA,UAAAJ,EAAA,CAAAwJ,eAAA;;;;;;;;QCbvCxJ,EAAA,CAAAC,cAAA,iBAAoC;QAClCD,EAAA,CAAAsC,uBAAA,GAAc;QACZtC,EAAA,CAAAyJ,SAAA,aACM;QACRzJ,EAAA,CAAAuC,qBAAA,EAAe;QACjBvC,EAAA,CAAAW,YAAA,EAAU;QACVX,EAAA,CAAAC,cAAA,aAAqC;QAEnCD,EAAA,CAAAsC,uBAAA,GAAc;QACZtC,EAAA,CAAAC,cAAA,aAAiD;QAE7CD,EAAA,CAAAyJ,SAAA,kBASS;QAETzJ,EAAA,CAAAC,cAAA,cAA2B;QACrBD,EAAA,CAAA6B,MAAA,IAAyB;QAAA7B,EAAA,CAAAW,YAAA,EAAK;QAClCX,EAAA,CAAAC,cAAA,YAA8B;QAAAD,EAAA,CAAA6B,MAAA,IAA+B;QAAA7B,EAAA,CAAAW,YAAA,EAAI;QAEnEX,EAAA,CAAAC,cAAA,cAAmC;QAG/BD,EAAA,CAAA2B,UAAA,KAAA+H,kDAAA,mBAsBM;QACR1J,EAAA,CAAAW,YAAA,EAAc;QAGlBX,EAAA,CAAAC,cAAA,eAA2C;QAKxBD,EAAA,CAAA2B,UAAA,KAAAgI,gDAAA,gBAA+E;QAAA3J,EAAA,CAAAW,YAAA,EAAS;QAIvGX,EAAA,CAAAC,cAAA,eAAyB;QACvBD,EAAA,CAAA2B,UAAA,KAAAiI,kDAAA,kBAKM;;QACR5J,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,mBAAsC;QAGlCD,EAAA,CAAAE,UAAA,2BAAA2J,+EAAAzJ,MAAA;UAAA,OAAAmJ,GAAA,CAAAlF,YAAA,GAAAjE,MAAA;QAAA,EAA0B,yBAAA0J,6EAAA;UAAA,OAEXP,GAAA,CAAAjB,WAAA,EAAa;QAAA,EAFF;QAK3BtI,EAAA,CAAAW,YAAA,EAAW;QACZX,EAAA,CAAAC,cAAA,kBAMC;QAFCD,EAAA,CAAAE,UAAA,mBAAA6J,qEAAA;UAAA,OAASR,GAAA,CAAAjB,WAAA,EAAa;QAAA,EAAC;QAGzBtI,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAA6B,MAAA,YAAI;QAAA7B,EAAA,CAAAW,YAAA,EAAW;QAMnCX,EAAA,CAAAuC,qBAAA,EAAe;QACjBvC,EAAA,CAAAW,YAAA,EAAM;;;QAxEQX,EAAA,CAAAY,SAAA,IAAyB;QAAzBZ,EAAA,CAAA+B,iBAAA,CAAAwH,GAAA,CAAAhG,YAAA,kBAAAgG,GAAA,CAAAhG,YAAA,CAAAyG,KAAA,CAAyB;QACChK,EAAA,CAAAY,SAAA,GAA+B;QAA/BZ,EAAA,CAAA+B,iBAAA,CAAAwH,GAAA,CAAAhG,YAAA,kBAAAgG,GAAA,CAAAhG,YAAA,CAAArB,WAAA,CAA+B;QAI3ClC,EAAA,CAAAY,SAAA,GAA+B;QAA/BZ,EAAA,CAAAa,UAAA,gCAA+B;QAE/Bb,EAAA,CAAAY,SAAA,GAA0B;QAA1BZ,EAAA,CAAAa,UAAA,YAAA0I,GAAA,CAAAhG,YAAA,CAAAkE,QAAA,CAA0B;QA8BPzH,EAAA,CAAAY,SAAA,GAAkB;QAAlBZ,EAAA,CAAAa,UAAA,SAAA0I,GAAA,CAAAlH,YAAA,CAAkB;QAKRrC,EAAA,CAAAY,SAAA,GAAoB;QAApBZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiK,WAAA,SAAAV,GAAA,CAAApF,SAAA,EAAoB;QAU/DnE,EAAA,CAAAY,SAAA,GAA0B;QAA1BZ,EAAA,CAAAa,UAAA,YAAA0I,GAAA,CAAAlF,YAAA,CAA0B,cAAAkF,GAAA,CAAAjF,UAAA,aAAAtE,EAAA,CAAAkK,eAAA,KAAAC,GAAA,GAAAZ,GAAA,CAAAjF,UAAA;QAQ1BtE,EAAA,CAAAY,SAAA,GAAwB;QAAxBZ,EAAA,CAAAa,UAAA,cAAA0I,GAAA,CAAAjF,UAAA,CAAwB,YAAAtE,EAAA,CAAAkK,eAAA,KAAAC,GAAA,GAAAZ,GAAA,CAAAjF,UAAA;;;;;;;AD6EtC,OAAM,SAAUyD,cAAcA,CAACqC,GAAW,EAAE5G,OAAe;EACzD,IAAI4G,GAAG,EAAE;IACP,IAAI5G,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACtBA,OAAO,GAAGA,OAAO,CAAC6G,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEjC,IAAID,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGA,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC;IAC/C,MAAME,GAAG,GAAGH,GAAG,CAACC,SAAS,CAAC,CAAC,EAAED,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,CAAC;IAC9C,IAAID,GAAG,CAACE,WAAW,EAAE,CAACjF,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,GAAGhC,OAAO,GAAG4G,GAAG,EAAE,CAAC,KAC/D;MACH,OAAO,GAAG5G,OAAO,WAAW4G,GAAG,EAAE;;GAEpC,MAAM;IACL,OAAO,EAAE;;AAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}