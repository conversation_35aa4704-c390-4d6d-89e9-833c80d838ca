import {AfterViewInit, ChangeDetectorRef, Component, HostListener, Inject, On<PERSON><PERSON>roy, OnInit, PLATFORM_ID} from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ActivatedRoute } from "@angular/router";
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { Category, Product } from '@core/interface';
import { StoreService, ProductService } from '@core/services';
import {isPlatformBrowser, CommonModule} from "@angular/common";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { InitialModule } from '@shared/modules/initial.module';
import { Subscription } from 'rxjs';
import { CategoryCardComponent } from '../../../shared_v2/components/category-card/category-card.component';

@Component({
  selector: 'app-category-container',
  templateUrl: './category-container.component.html',
  styleUrls: ['./category-container.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbModule,
    InitialModule,
    CategoryCardComponent
  ]
})
export class CategoryContainerComponent implements OnInit, AfterViewInit, OnDestroy {
  items: MenuItem[] = [];
  categories: Array<Category> = [];
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  categoryId: number;
  products: Product[] = [];
  selectedCategory: Category | null = null;

  private categoriesSub?: Subscription;
  private productsSub?: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private store: StoreService,
    private productService: ProductService,
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef,
  ) {
    this.categoryId = this.activatedRoute.snapshot.params['id'];
  }

  ngOnInit(): void {
    this.items = [
      { label: this.translateService.instant('categories.allCategories') }
    ];

    this.home = { icon: 'pi pi-home', routerLink: '/' };
  }

  ngAfterViewInit(): void {
    this.categoriesSub = this.store.subscription('categories')
      .subscribe({
        next: (res: Category[]) => {
          if (res && res.length > 0) {
            this.categories = res;
            this.cdr.detectChanges();
          } else {
            const localCategories = localStorage.getItem('allCategories');
            if (localCategories && localCategories !== '') {
              this.categories = JSON.parse(localCategories);
              this.cdr.detectChanges();
            }
          }
        },
        error: (err: Error) => {
      console.error(err);
        }
      });
  }

  onSelectCategory(category: Category) {
    this.selectedCategory = category;
    this.productsSub?.unsubscribe();
    this.productsSub = this.productService.getCategoryProducts(category.id, 50, true)
      .subscribe((data: { products?: Product[] } | Product[]) => this.products = Array.isArray(data) ? data : (data.products ?? []));
  }
  ngOnDestroy(): void {
    this.categoriesSub?.unsubscribe();
    this.productsSub?.unsubscribe();
  }
}
