{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"93vw\"\n  };\n};\nexport class DeleteCartModalComponent {\n  constructor(translate) {\n    this.translate = translate;\n    this.displayModal = false;\n    this.submit = new EventEmitter();\n    this.buttonText = this.translate.instant('settings.address.addAddress');\n    this.addressName = '';\n    this.isStoreCloud = environment.isStoreCloud;\n  }\n  ngOnInit() {\n    /**/\n  }\n  onDelete() {\n    this.submit.emit(true);\n  }\n  onCancel(event) {\n    this.submit.emit(false);\n  }\n  static #_ = this.ɵfac = function DeleteCartModalComponent_Factory(t) {\n    return new (t || DeleteCartModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DeleteCartModalComponent,\n    selectors: [[\"app-mtn-delete-cart-modal\"]],\n    inputs: {\n      displayModal: \"displayModal\"\n    },\n    outputs: {\n      submit: \"submit\"\n    },\n    decls: 18,\n    vars: 19,\n    consts: [[3, \"visible\", \"breakpoints\", \"resizable\", \"closable\", \"modal\", \"showHeader\", \"visibleChange\"], [1, \"row\", \"text-center\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-trash\", \"delete-color\"], [1, \"delete-text-heading\", \"mt-4\"], [1, \"delete-text\", \"text-center\"], [1, \"mt-2\", \"flex\", \"flex-row\", \"justify-content-between\", \"mt-3\", \"p-3\"], [\"type\", \"button\", 1, \"p-element\", \"delete-btn\", \"second-btn\", \"p-button\", \"p-component\", \"ng-star-inserted\", \"delete-btn\", 3, \"click\"], [1, \"p-button-label\"], [\"type\", \"button\", 1, \"p-element\", \"ml-1\", \"cancel-btn\", \"main-btn\", \"p-button\", \"p-component\", \"ng-star-inserted\"], [1, \"p-button-label\", \"cancel-text-btn\", 3, \"click\"]],\n    template: function DeleteCartModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function DeleteCartModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelement(2, \"em\", 2);\n        i0.ɵɵelementStart(3, \"p\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"p\", 4);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function DeleteCartModalComponent_Template_button_click_10_listener() {\n          return ctx.onDelete();\n        });\n        i0.ɵɵelementStart(11, \"span\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"button\", 8)(15, \"span\", 9);\n        i0.ɵɵlistener(\"click\", function DeleteCartModalComponent_Template_span_click_15_listener($event) {\n          return ctx.onCancel($event);\n        });\n        i0.ɵɵtext(16);\n        i0.ɵɵpipe(17, \"translate\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(18, _c0))(\"resizable\", false)(\"closable\", false)(\"modal\", true)(\"showHeader\", false);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 10, \"deleteCart.delete\"), \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 12, \"deleteCart.sureMesg\"), \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 14, \"deleteCart.delete\"), \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 16, \"deleteCart.cancel\"), \"\");\n      }\n    },\n    styles: [\"*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  list-style: none;\\n  text-decoration: none;\\n}\\n\\n  .p-dialog-content {\\n  border-bottom: none !important;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font);\\n  padding: 10px 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n}\\n\\n.delete-color[_ngcontent-%COMP%] {\\n  font-size: 30px;\\n  margin-top: 10px;\\n  color: var(--main_bt_txtcolor);\\n}\\n\\n.delete-text-heading[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #000;\\n  font-family: var(--medium-font);\\n}\\n\\n.delete-text[_ngcontent-%COMP%] {\\n  color: #a3a3a3;\\n  font-size: 15px;\\n  font-weight: 400;\\n  font-family: var(--regular-font);\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  width: 118px;\\n  height: 35px;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  width: 122px;\\n  height: 35px;\\n  text-transform: uppercase;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .delete-btn[_ngcontent-%COMP%] {\\n    width: 44%;\\n  }\\n  .cancel-btn[_ngcontent-%COMP%] {\\n    width: 44%;\\n  }\\n  .cancel-text-btn[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    font-family: var(--regular-font);\\n  }\\n  .delete-text[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    font-family: var(--regular-font);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "environment", "DeleteCartModalComponent", "constructor", "translate", "displayModal", "submit", "buttonText", "instant", "addressName", "isStoreCloud", "ngOnInit", "onDelete", "emit", "onCancel", "event", "_", "i0", "ɵɵdirectiveInject", "i1", "TranslateService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DeleteCartModalComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "DeleteCartModalComponent_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "DeleteCartModalComponent_Template_button_click_10_listener", "DeleteCartModalComponent_Template_span_click_15_listener", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\delete-cart-modal\\delete-cart-modal.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\delete-cart-modal\\delete-cart-modal.component.html"], "sourcesContent": ["import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {environment} from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-mtn-delete-cart-modal',\r\n  templateUrl: './delete-cart-modal.component.html',\r\n  styleUrls: ['./delete-cart-modal.component.scss']\r\n})\r\nexport class DeleteCartModalComponent implements OnInit {\r\n  @Input() displayModal: boolean = false\r\n  @Output() submit = new EventEmitter<boolean>();\r\n  buttonText: string = this.translate.instant('settings.address.addAddress');\r\n  addressName: string = ''\r\n  isStoreCloud: boolean = environment.isStoreCloud\r\n\r\n  constructor(private translate: TranslateService) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n  onDelete() {\r\n    this.submit.emit(true)\r\n  }\r\n\r\n  onCancel(event: any) {\r\n    this.submit.emit(false)\r\n  }\r\n}\r\n", "<p-dialog [(visible)]=\"displayModal\" [breakpoints]=\"{ '960px': '75vw', '640px': '93vw' }\" [resizable]=\"false\"\r\n  [closable]=\"false\" [modal]=\"true\" [showHeader]=\"false\">\r\n  <div class=\"row text-center\">\r\n    <em class=\"pi pi-trash delete-color\" aria-hidden=\"true\"></em>\r\n    <p class=\"delete-text-heading mt-4\">\r\n      {{ \"deleteCart.delete\" | translate }}</p>\r\n  </div>\r\n  <p class=\"delete-text text-center\">\r\n    {{ \"deleteCart.sureMesg\" | translate }}\r\n\r\n  </p>\r\n  <div class=\"mt-2 flex flex-row justify-content-between mt-3 p-3\">\r\n    <button (click)=\"onDelete()\" type=\"button\"\r\n      class=\"p-element delete-btn second-btn p-button p-component ng-star-inserted delete-btn\">\r\n      <span class=\"p-button-label\"> {{ \"deleteCart.delete\" | translate }}</span>\r\n    </button>\r\n\r\n    <button type=\"button\" class=\"p-element ml-1 cancel-btn main-btn p-button p-component ng-star-inserted\">\r\n      <span class=\"p-button-label cancel-text-btn\" (click)=\"onCancel($event)\"> {{ \"deleteCart.cancel\" | translate\r\n        }}</span>\r\n    </button>\r\n  </div>\r\n</p-dialog>\r\n"], "mappings": "AAAA,SAAmBA,YAAY,QAA8B,eAAe;AAE5E,SAAQC,WAAW,QAAO,8BAA8B;;;;;;;;;AAOxD,OAAM,MAAOC,wBAAwB;EAOnCC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;IANpB,KAAAC,YAAY,GAAY,KAAK;IAC5B,KAAAC,MAAM,GAAG,IAAIN,YAAY,EAAW;IAC9C,KAAAO,UAAU,GAAW,IAAI,CAACH,SAAS,CAACI,OAAO,CAAC,6BAA6B,CAAC;IAC1E,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAYT,WAAW,CAACS,YAAY;EAGhD;EAEAC,QAAQA,CAAA;IACN;EAAA;EAGFC,QAAQA,CAAA;IACN,IAAI,CAACN,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EACxB;EAEAC,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACT,MAAM,CAACO,IAAI,CAAC,KAAK,CAAC;EACzB;EAAC,QAAAG,CAAA,G;qBApBUd,wBAAwB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBnB,wBAAwB;IAAAoB,SAAA;IAAAC,MAAA;MAAAlB,YAAA;IAAA;IAAAmB,OAAA;MAAAlB,MAAA;IAAA;IAAAmB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrCb,EAAA,CAAAe,cAAA,kBACyD;QAD/Cf,EAAA,CAAAgB,UAAA,2BAAAC,oEAAAC,MAAA;UAAA,OAAAJ,GAAA,CAAA1B,YAAA,GAAA8B,MAAA;QAAA,EAA0B;QAElClB,EAAA,CAAAe,cAAA,aAA6B;QAC3Bf,EAAA,CAAAmB,SAAA,YAA6D;QAC7DnB,EAAA,CAAAe,cAAA,WAAoC;QAClCf,EAAA,CAAAoB,MAAA,GAAqC;;QAAApB,EAAA,CAAAqB,YAAA,EAAI;QAE7CrB,EAAA,CAAAe,cAAA,WAAmC;QACjCf,EAAA,CAAAoB,MAAA,GAEF;;QAAApB,EAAA,CAAAqB,YAAA,EAAI;QACJrB,EAAA,CAAAe,cAAA,aAAiE;QACvDf,EAAA,CAAAgB,UAAA,mBAAAM,2DAAA;UAAA,OAASR,GAAA,CAAAnB,QAAA,EAAU;QAAA,EAAC;QAE1BK,EAAA,CAAAe,cAAA,eAA6B;QAACf,EAAA,CAAAoB,MAAA,IAAqC;;QAAApB,EAAA,CAAAqB,YAAA,EAAO;QAG5ErB,EAAA,CAAAe,cAAA,iBAAuG;QACxDf,EAAA,CAAAgB,UAAA,mBAAAO,yDAAAL,MAAA;UAAA,OAASJ,GAAA,CAAAjB,QAAA,CAAAqB,MAAA,CAAgB;QAAA,EAAC;QAAElB,EAAA,CAAAoB,MAAA,IACrE;;QAAApB,EAAA,CAAAqB,YAAA,EAAO;;;QAnBPrB,EAAA,CAAAwB,UAAA,YAAAV,GAAA,CAAA1B,YAAA,CAA0B,gBAAAY,EAAA,CAAAyB,eAAA,KAAAC,GAAA;QAK9B1B,EAAA,CAAA2B,SAAA,GAAqC;QAArC3B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,iCAAqC;QAGvC7B,EAAA,CAAA2B,SAAA,GAEF;QAFE3B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,oCAEF;QAIkC7B,EAAA,CAAA2B,SAAA,GAAqC;QAArC3B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,kCAAqC;QAIM7B,EAAA,CAAA2B,SAAA,GACrE;QADqE3B,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,kCACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}