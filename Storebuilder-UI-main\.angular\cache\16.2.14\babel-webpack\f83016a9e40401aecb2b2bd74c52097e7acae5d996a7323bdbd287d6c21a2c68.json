{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport { catchError, map, of, Subject, tap } from \"rxjs\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { castToHttpParams } from \"../utilities\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/services/main-data.service\";\nimport * as i3 from \"@core/services/loader.service\";\nexport class CartService {\n  constructor(http, mainDataService, loaderService) {\n    this.http = http;\n    this.mainDataService = mainDataService;\n    this.loaderService = loaderService;\n    this.productChangeDetection = new Subject();\n    this.baseUrl = `${environment.apiEndPoint}/Product/Cart`;\n  }\n  getCart(data) {\n    let url = `${this.baseUrl}/GetAllCart`;\n    if (data.sessionId != '') {\n      url += '?SessionId=' + data.sessionId;\n    }\n    if (data.applyTo) {\n      url += '&applyTo=' + data.applyTo;\n    }\n    return this.http.get(url).pipe(map(res => {\n      if (res.data.records) {\n        if (res.data.records.length) {\n          this.mainDataService._cartItemshDataAfterLoginIn.next(res.data.records[0].cartDetails);\n          if (res.data.records[0].cartDetails.length) {\n            localStorage.setItem('cartId', res.data.records[0].cartDetails[0].cartId);\n          } else if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            localStorage.setItem('cartId', res.data.records[0].cartDetailsDPay[0].cartId);\n          }\n          this.mainDataService.setCartLenghtData(res.data.records[0].cartDetails.length);\n          this.mainDataService.setCartItemsData(res.data.records[0].cartDetails);\n        }\n      }\n      return res;\n    }));\n  }\n  getCartCount(data) {\n    let url = `${this.baseUrl}/GetAllCartCount`;\n    if (data.sessionId != '') {\n      url += '?SessionId=' + data.sessionId;\n    }\n    if (data.applyTo) {\n      url += '&applyTo=' + data.applyTo;\n    }\n    return this.http.get(url);\n  }\n  addToCart(data) {\n    if (data.channelId == 2) {\n      data.priceId = 0;\n      data.specsProductId = 0;\n    }\n    return this.http.post(`${this.baseUrl}/CreateCart`, data);\n  }\n  emptyCart(cartId) {\n    return this.http.get(`${this.baseUrl}/DeleteCart/${cartId}`);\n  }\n  removeItemFromCart(productId) {\n    return this.http.get(`${this.baseUrl}/cart/remove-item/${productId}`);\n  }\n  updateCart(data) {\n    return this.http.post(`${this.baseUrl}/UpdateCart`, data);\n  }\n  productChangeDetect(product) {\n    this.productChangeDetection.next(product);\n  }\n  getShipmentMethodByTenantId() {\n    return this.http.get(`${environment.apiEndPoint}/Shipment/ShipmentMethod/GetShipmentMethodByTenantId`).pipe(map(data => {\n      return data;\n    }));\n  }\n  getOwnShipmentOptions(filter) {\n    return this.http.get(`${environment.apiEndPoint}/Shipment/DeliveryOptions/getall?ApplyTo=1`, {\n      params: castToHttpParams(filter)\n    }).pipe(map(data => {\n      return data;\n    }));\n  }\n  getReterviedShipmentOptions(filter) {\n    return this.http.get(`${environment.apiEndPoint}/Shipment/DeliveryOptions/getall?ApplyTo=2`, {\n      params: castToHttpParams(filter)\n    }).pipe(map(data => {\n      return data;\n    }));\n  }\n  getAllCart(data) {\n    this.loaderService.show();\n    return this.getCart(data).pipe(tap(() => this.loaderService.hide()), map(res => {\n      let cartListCount = 0;\n      let cartListData = [];\n      if (res.data?.records?.length) {\n        if (res.data.records[0].cartDetails.length) {\n          cartListCount = res.data.records[0].cartDetails.length;\n          cartListData = res.data.records[0].cartDetails;\n        }\n        this.mainDataService.setCartLenghtData(cartListCount);\n        this.mainDataService.setCartItemsData(cartListData);\n      } else {\n        this.mainDataService.setCartLenghtData(0);\n        this.mainDataService.setCartItemsData([]);\n      }\n      return cartListData;\n    }), catchError(error => {\n      this.loaderService.hide();\n      return of([]); // Return an empty array or handle accordingly\n    }));\n  }\n  static #_ = this.ɵfac = function CartService_Factory(t) {\n    return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.MainDataService), i0.ɵɵinject(i3.LoaderService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CartService,\n    factory: CartService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport class GuidGenerator {\n  static newGuid() {\n    return uuidv4();\n  }\n}", "map": {"version": 3, "names": ["environment", "catchError", "map", "of", "Subject", "tap", "v4", "uuidv4", "castToHttpParams", "CartService", "constructor", "http", "mainDataService", "loaderService", "productChangeDetection", "baseUrl", "apiEndPoint", "getCart", "data", "url", "sessionId", "applyTo", "get", "pipe", "res", "records", "length", "_cartItemshDataAfterLoginIn", "next", "cartDetails", "localStorage", "setItem", "cartId", "cartDetailsDPay", "setCartLenghtData", "setCartItemsData", "getCartCount", "addToCart", "channelId", "priceId", "specsProductId", "post", "emptyCart", "removeItemFromCart", "productId", "updateCart", "productChangeDetect", "product", "getShipmentMethodByTenantId", "getOwnShipmentOptions", "filter", "params", "getReterviedShipmentOptions", "getAllCart", "show", "hide", "cartListCount", "cartListData", "error", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "MainDataService", "i3", "LoaderService", "_2", "factory", "ɵfac", "providedIn", "GuidGenerator", "newGuid"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\cart.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from 'src/environments/environment';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {catchError, map, Observable, of, Subject, tap} from \"rxjs\";\r\nimport {v4 as uuidv4} from \"uuid\";\r\nimport {castToHttpParams} from \"../utilities\";\r\nimport {MainDataService} from \"@core/services/main-data.service\";\r\nimport {LoaderService} from \"@core/services/loader.service\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CartService {\r\n\r\n  baseUrl: string;\r\n  productChangeDetection = new Subject<any>();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private mainDataService: MainDataService,\r\n    private loaderService: LoaderService\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}/Product/Cart`;\r\n  }\r\n\r\n  getCart(data: any): Observable<object> {\r\n    let url = `${this.baseUrl}/GetAllCart`;\r\n    if (data.sessionId != '') {\r\n      url += '?SessionId=' + data.sessionId;\r\n    }\r\n    if (data.applyTo) {\r\n      url += '&applyTo=' + data.applyTo;\r\n    }\r\n    return this.http.get(url).pipe(\r\n      map((res: any) => {\r\n        if (res.data.records) {\r\n          if (res.data.records.length) {\r\n            this.mainDataService._cartItemshDataAfterLoginIn.next(res.data.records[0].cartDetails)\r\n            if (res.data.records[0].cartDetails.length) {\r\n              localStorage.setItem('cartId', res.data.records[0].cartDetails[0].cartId)\r\n            } else if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\r\n              localStorage.setItem('cartId', res.data.records[0].cartDetailsDPay[0].cartId)\r\n            }\r\n            this.mainDataService.setCartLenghtData(res.data.records[0].cartDetails.length);\r\n            this.mainDataService.setCartItemsData(res.data.records[0].cartDetails);\r\n          }\r\n         }\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  getCartCount(data: any): Observable<object> {\r\n    let url = `${this.baseUrl}/GetAllCartCount`;\r\n    if (data.sessionId != '') {\r\n      url += '?SessionId=' + data.sessionId;\r\n    }\r\n    if (data.applyTo) {\r\n      url += '&applyTo=' + data.applyTo;\r\n    }\r\n    return this.http.get(url);\r\n  }\r\n\r\n  addToCart(data: any): Observable<object> {\r\n    if (data.channelId == 2) {\r\n      data.priceId = 0;\r\n      data.specsProductId = 0;\r\n    }\r\n    return this.http.post(`${this.baseUrl}/CreateCart`, data);\r\n  }\r\n\r\n  emptyCart(cartId: any): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/DeleteCart/${cartId}`);\r\n  }\r\n\r\n  removeItemFromCart(productId: number): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/cart/remove-item/${productId}`);\r\n  }\r\n\r\n  updateCart(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/UpdateCart`, data);\r\n  }\r\n\r\n  productChangeDetect(product: any) {\r\n    this.productChangeDetection.next(product);\r\n  }\r\n\r\n  getShipmentMethodByTenantId() {\r\n    return this.http.get(`${environment.apiEndPoint}/Shipment/ShipmentMethod/GetShipmentMethodByTenantId`).pipe(\r\n      map((data) => {\r\n        return data;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getOwnShipmentOptions(filter: any) {\r\n    return this.http.get(`${environment.apiEndPoint}/Shipment/DeliveryOptions/getall?ApplyTo=1`, {params: castToHttpParams(filter)}).pipe(\r\n      map((data) => {\r\n        return data;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getReterviedShipmentOptions(filter: any) {\r\n    return this.http.get(`${environment.apiEndPoint}/Shipment/DeliveryOptions/getall?ApplyTo=2`, {params: castToHttpParams(filter)}).pipe(\r\n      map((data) => {\r\n        return data;\r\n      })\r\n    );\r\n  }\r\n\r\n  getAllCart(data: any): Observable<any> {\r\n    this.loaderService.show()\r\n    return this.getCart(data).pipe(\r\n      tap(() => this.loaderService.hide()),\r\n      map((res: any) => {\r\n        let cartListCount = 0;\r\n        let cartListData = [];\r\n        if (res.data?.records?.length) {\r\n          if (res.data.records[0].cartDetails.length) {\r\n            cartListCount = res.data.records[0].cartDetails.length;\r\n            cartListData = res.data.records[0].cartDetails;\r\n          }\r\n          this.mainDataService.setCartLenghtData(cartListCount);\r\n          this.mainDataService.setCartItemsData(cartListData);\r\n        } else {\r\n          this.mainDataService.setCartLenghtData(0);\r\n          this.mainDataService.setCartItemsData([]);\r\n        }\r\n        return cartListData;\r\n      }),\r\n      catchError(error => {\r\n        this.loaderService.hide();\r\n        return of([]); // Return an empty array or handle accordingly\r\n      })\r\n    );\r\n\r\n  }\r\n\r\n}\r\n\r\nexport class GuidGenerator {\r\n  static newGuid() {\r\n    return uuidv4()\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,8BAA8B;AAExD,SAAQC,UAAU,EAAEC,GAAG,EAAcC,EAAE,EAAEC,OAAO,EAAEC,GAAG,QAAO,MAAM;AAClE,SAAQC,EAAE,IAAIC,MAAM,QAAO,MAAM;AACjC,SAAQC,gBAAgB,QAAO,cAAc;;;;;AAO7C,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,eAAgC,EAChCC,aAA4B;IAF5B,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IALvB,KAAAC,sBAAsB,GAAG,IAAIV,OAAO,EAAO;IAOzC,IAAI,CAACW,OAAO,GAAG,GAAGf,WAAW,CAACgB,WAAW,eAAe;EAC1D;EAEAC,OAAOA,CAACC,IAAS;IACf,IAAIC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,aAAa;IACtC,IAAIG,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;MACxBD,GAAG,IAAI,aAAa,GAAGD,IAAI,CAACE,SAAS;;IAEvC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAChBF,GAAG,IAAI,WAAW,GAAGD,IAAI,CAACG,OAAO;;IAEnC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAACH,GAAG,CAAC,CAACI,IAAI,CAC5BrB,GAAG,CAAEsB,GAAQ,IAAI;MACf,IAAIA,GAAG,CAACN,IAAI,CAACO,OAAO,EAAE;QACpB,IAAID,GAAG,CAACN,IAAI,CAACO,OAAO,CAACC,MAAM,EAAE;UAC3B,IAAI,CAACd,eAAe,CAACe,2BAA2B,CAACC,IAAI,CAACJ,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC;UACtF,IAAIL,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAACH,MAAM,EAAE;YAC1CI,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEP,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC;WAC1E,MAAM,IAAIR,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACQ,eAAe,IAAIT,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACQ,eAAe,CAACP,MAAM,EAAE;YAC5FI,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEP,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACQ,eAAe,CAAC,CAAC,CAAC,CAACD,MAAM,CAAC;;UAE/E,IAAI,CAACpB,eAAe,CAACsB,iBAAiB,CAACV,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAACH,MAAM,CAAC;UAC9E,IAAI,CAACd,eAAe,CAACuB,gBAAgB,CAACX,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC;;;MAG1E,OAAOL,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEAY,YAAYA,CAAClB,IAAS;IACpB,IAAIC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,kBAAkB;IAC3C,IAAIG,IAAI,CAACE,SAAS,IAAI,EAAE,EAAE;MACxBD,GAAG,IAAI,aAAa,GAAGD,IAAI,CAACE,SAAS;;IAEvC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAChBF,GAAG,IAAI,WAAW,GAAGD,IAAI,CAACG,OAAO;;IAEnC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAACH,GAAG,CAAC;EAC3B;EAEAkB,SAASA,CAACnB,IAAS;IACjB,IAAIA,IAAI,CAACoB,SAAS,IAAI,CAAC,EAAE;MACvBpB,IAAI,CAACqB,OAAO,GAAG,CAAC;MAChBrB,IAAI,CAACsB,cAAc,GAAG,CAAC;;IAEzB,OAAO,IAAI,CAAC7B,IAAI,CAAC8B,IAAI,CAAC,GAAG,IAAI,CAAC1B,OAAO,aAAa,EAAEG,IAAI,CAAC;EAC3D;EAEAwB,SAASA,CAACV,MAAW;IACnB,OAAO,IAAI,CAACrB,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACP,OAAO,eAAeiB,MAAM,EAAE,CAAC;EAC9D;EAEAW,kBAAkBA,CAACC,SAAiB;IAClC,OAAO,IAAI,CAACjC,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACP,OAAO,qBAAqB6B,SAAS,EAAE,CAAC;EACvE;EAEAC,UAAUA,CAAC3B,IAAS;IAClB,OAAO,IAAI,CAACP,IAAI,CAAC8B,IAAI,CAAC,GAAG,IAAI,CAAC1B,OAAO,aAAa,EAAEG,IAAI,CAAC;EAC3D;EAEA4B,mBAAmBA,CAACC,OAAY;IAC9B,IAAI,CAACjC,sBAAsB,CAACc,IAAI,CAACmB,OAAO,CAAC;EAC3C;EAEAC,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACrC,IAAI,CAACW,GAAG,CAAC,GAAGtB,WAAW,CAACgB,WAAW,sDAAsD,CAAC,CAACO,IAAI,CACzGrB,GAAG,CAAEgB,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEO+B,qBAAqBA,CAACC,MAAW;IACtC,OAAO,IAAI,CAACvC,IAAI,CAACW,GAAG,CAAC,GAAGtB,WAAW,CAACgB,WAAW,4CAA4C,EAAE;MAACmC,MAAM,EAAE3C,gBAAgB,CAAC0C,MAAM;IAAC,CAAC,CAAC,CAAC3B,IAAI,CACnIrB,GAAG,CAAEgB,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEOkC,2BAA2BA,CAACF,MAAW;IAC5C,OAAO,IAAI,CAACvC,IAAI,CAACW,GAAG,CAAC,GAAGtB,WAAW,CAACgB,WAAW,4CAA4C,EAAE;MAACmC,MAAM,EAAE3C,gBAAgB,CAAC0C,MAAM;IAAC,CAAC,CAAC,CAAC3B,IAAI,CACnIrB,GAAG,CAAEgB,IAAI,IAAI;MACX,OAAOA,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEAmC,UAAUA,CAACnC,IAAS;IAClB,IAAI,CAACL,aAAa,CAACyC,IAAI,EAAE;IACzB,OAAO,IAAI,CAACrC,OAAO,CAACC,IAAI,CAAC,CAACK,IAAI,CAC5BlB,GAAG,CAAC,MAAM,IAAI,CAACQ,aAAa,CAAC0C,IAAI,EAAE,CAAC,EACpCrD,GAAG,CAAEsB,GAAQ,IAAI;MACf,IAAIgC,aAAa,GAAG,CAAC;MACrB,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIjC,GAAG,CAACN,IAAI,EAAEO,OAAO,EAAEC,MAAM,EAAE;QAC7B,IAAIF,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAACH,MAAM,EAAE;UAC1C8B,aAAa,GAAGhC,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW,CAACH,MAAM;UACtD+B,YAAY,GAAGjC,GAAG,CAACN,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACI,WAAW;;QAEhD,IAAI,CAACjB,eAAe,CAACsB,iBAAiB,CAACsB,aAAa,CAAC;QACrD,IAAI,CAAC5C,eAAe,CAACuB,gBAAgB,CAACsB,YAAY,CAAC;OACpD,MAAM;QACL,IAAI,CAAC7C,eAAe,CAACsB,iBAAiB,CAAC,CAAC,CAAC;QACzC,IAAI,CAACtB,eAAe,CAACuB,gBAAgB,CAAC,EAAE,CAAC;;MAE3C,OAAOsB,YAAY;IACrB,CAAC,CAAC,EACFxD,UAAU,CAACyD,KAAK,IAAG;MACjB,IAAI,CAAC7C,aAAa,CAAC0C,IAAI,EAAE;MACzB,OAAOpD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CACH;EAEH;EAAC,QAAAwD,CAAA,G;qBA7HUlD,WAAW,EAAAmD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAX3D,WAAW;IAAA4D,OAAA,EAAX5D,WAAW,CAAA6D,IAAA;IAAAC,UAAA,EAFV;EAAM;;AAmIpB,OAAM,MAAOC,aAAa;EACxB,OAAOC,OAAOA,CAAA;IACZ,OAAOlE,MAAM,EAAE;EACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}