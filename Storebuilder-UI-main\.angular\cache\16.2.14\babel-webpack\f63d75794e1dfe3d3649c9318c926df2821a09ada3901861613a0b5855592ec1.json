{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./token.service\";\nimport * as i2 from \"ngx-cookie-service\";\nexport class StoreService {\n  authTokenService;\n  cookieService;\n  storeData;\n  localStoreNames;\n  store;\n  constructor(authTokenService, cookieService) {\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.storeData = {};\n    this.store = {};\n    this.localStoreNames = [];\n  }\n  createNewStore(storeName, initData, localStore) {\n    this.store[storeName] = new BehaviorSubject(initData);\n    if (localStore) {\n      const data = this.getFromLocalStorage(storeName) || initData;\n      this.localStoreNames.push(storeName);\n      this.set(storeName, data);\n    } else {\n      this.set(storeName, initData);\n    }\n  }\n  subscription(storeName) {\n    if (storeName == \"authToken\") {\n      let token;\n      this.authTokenService.authTokenData.subscribe(message => token = message);\n      if (!token) {\n        this.cookieService.get('authToken');\n        return token;\n      }\n      return token;\n    } else {\n      return this.store[storeName];\n    }\n  }\n  set(storeName, data) {\n    const currentStore = this.store[storeName];\n    if (currentStore) {\n      if (this.localStoreNames.includes(storeName)) {\n        this.setLocalStorage(storeName, data);\n        this.update(storeName, data);\n        this.fireChanges(storeName, currentStore);\n      } else {\n        this.update(storeName, data);\n        this.fireChanges(storeName, currentStore);\n      }\n    } else {\n      console.error(`${storeName} is not assign to store please check it`);\n    }\n  }\n  update(storeName, data) {\n    this.storeData[storeName] = data;\n  }\n  get(key) {\n    return this.storeData[key];\n  }\n  fireChanges(storeName, currentStore) {\n    currentStore.next(this.storeData[storeName]);\n  }\n  isJson(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  setLocalStorage(key, data) {\n    let stringData = '';\n    if (data) {\n      if (typeof data === 'string') {\n        stringData = data;\n      } else {\n        stringData = JSON.stringify(data);\n      }\n    }\n    return localStorage.setItem(key, stringData);\n  }\n  getFromLocalStorage(key) {\n    const data = localStorage.getItem(key) ?? '';\n    if (this.isJson(data)) {\n      return JSON.parse(data) || null;\n    } else {\n      return data || null;\n    }\n  }\n  static ɵfac = function StoreService_Factory(t) {\n    return new (t || StoreService)(i0.ɵɵinject(i1.AuthTokenService), i0.ɵɵinject(i2.CookieService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: StoreService,\n    factory: StoreService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}