{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-google-analytics\";\nimport * as i3 from \"@core/services\";\nexport let RouteInterceptor = /*#__PURE__*/(() => {\n  class RouteInterceptor {\n    router;\n    route;\n    $gaService;\n    store;\n    permissionService;\n    userDetails;\n    sessionId;\n    fullUrl;\n    tenantId;\n    productId;\n    isGoogleAnalytics = false;\n    constructor(router, route, $gaService, store, permissionService) {\n      this.router = router;\n      this.route = route;\n      this.$gaService = $gaService;\n      this.store = store;\n      this.permissionService = permissionService;\n      this.userDetails = this.store.get('profile');\n      this.sessionId = localStorage.getItem('sessionId');\n      this.fullUrl = window.location.href;\n      this.route.queryParams.subscribe(params => {\n        this.tenantId = params.tenantId;\n      });\n      this.route.params.subscribe(params => {\n        this.productId = params.id;\n      });\n    }\n    intercept(request, next) {\n      // Intercept request here\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      if (this.isGoogleAnalytics && this.permissionService.getTagFeature('page_visited')) {\n        this.$gaService.event(\"page_visited\", '', '', 1, true, {\n          \"product_ID\": this.productId ? this.productId : null,\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"session_ID\": this.sessionId,\n          \"tenant_ID\": this.tenantId,\n          \"visitedURL\": this.fullUrl\n        });\n      }\n      // Pass the request to the next handler\n      return next.handle(request);\n    }\n    static ɵfac = function RouteInterceptor_Factory(t) {\n      return new (t || RouteInterceptor)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i1.ActivatedRoute), i0.ɵɵinject(i2.GoogleAnalyticsService), i0.ɵɵinject(i3.StoreService), i0.ɵɵinject(i3.PermissionService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RouteInterceptor,\n      factory: RouteInterceptor.ɵfac\n    });\n  }\n  return RouteInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}