{"ast": null, "code": "import * as __NgCli_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from '@environments/environment';\nif (environment.production) {\n  enableProdMode();\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "names": ["enableProdMode", "AppModule", "environment", "production", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err", "console", "error"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\main.ts"], "sourcesContent": ["import { enableProdMode } from '@angular/core';\r\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\nimport { AppModule } from './app/app.module';\r\nimport { environment } from '@environments/environment';\r\n\r\nif (environment.production) {\r\n  enableProdMode();\r\n}\r\n\r\nplatformBrowserDynamic().bootstrapModule(AppModule)\r\n  .catch(err => console.error(err));\r\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,eAAe;AAG9C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,IAAIA,WAAW,CAACC,UAAU,EAAE;EAC1BH,cAAc,EAAE;;AAGlBI,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACL,SAAS,CAAC,CAChDM,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}