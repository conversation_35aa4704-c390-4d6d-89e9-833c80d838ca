{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { environment } from 'src/environments/environment';\nimport { GuidGenerator } from '@core/services';\nimport { last } from \"rxjs\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/custom-GA.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"@shared/modals/success-info-modal/success-info-modal.component\";\nimport * as i11 from \"@shared/modals/notify-modal/notify-modal.component\";\nimport * as i12 from \"@shared/modals/age-consent-modal/age-consent-modal.component\";\nimport * as i13 from \"@shared/modals/ineligable-purchase-modal/ineligable-purchase-modal.component\";\nfunction FloatingPanelComponent_div_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.disableCent === \"false\" ? i0.ɵɵpipeBind2(2, 1, ctx_r2.variant.salePrice, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue) : ctx_r2.variant.salePrice, \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"number\");\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.disableCent === \"false\" ? i0.ɵɵpipeBind2(1, 1, ctx_r4.variant.price, \"1.\" + ctx_r4.decimalValue + \"-\" + ctx_r4.decimalValue) : ctx_r4.variant.price, \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r5.currencyCode, \" \", ctx_r5.disableCent === \"false\" ? i0.ɵɵpipeBind2(2, 2, ctx_r5.variant.price, \"1.\" + ctx_r5.decimalValue + \"-\" + ctx_r5.decimalValue) : ctx_r5.variant.price, \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n}\nfunction FloatingPanelComponent_div_0_img_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_button_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_button_1_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_ng_container_15_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.addItem(ctx_r16.variant, ctx_r16.product.shopId, ctx_r16.product));\n    });\n    i0.ɵɵtemplate(1, FloatingPanelComponent_div_0_ng_container_15_button_1_img_1_Template, 1, 0, \"img\", 27);\n    i0.ɵɵtemplate(2, FloatingPanelComponent_div_0_ng_container_15_button_1_img_2_Template, 1, 0, \"img\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, \"productDetails.details.addToCart\"), \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_ng_container_15_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.shopNow(ctx_r18.variant, ctx_r18.product.shopId, ctx_r18.product));\n    });\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_ng_container_15_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.notifyMe());\n    });\n    i0.ɵɵelement(1, \"img\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"productDetails.details.notifyMe\"), \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FloatingPanelComponent_div_0_ng_container_15_button_1_Template, 5, 5, \"button\", 23);\n    i0.ɵɵtemplate(2, FloatingPanelComponent_div_0_ng_container_15_button_2_Template, 4, 3, \"button\", 24);\n    i0.ɵɵtemplate(3, FloatingPanelComponent_div_0_ng_container_15_button_3_Template, 4, 3, \"button\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.variant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.variant.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.variant == null ? null : ctx_r8.variant.soldOut);\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_template_16_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction FloatingPanelComponent_div_0_ng_template_16_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    opacity: a0\n  };\n};\nfunction FloatingPanelComponent_div_0_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_ng_template_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.addItem(ctx_r24.variant, ctx_r24.product.shopId, ctx_r24.product));\n    });\n    i0.ɵɵtemplate(1, FloatingPanelComponent_div_0_ng_template_16_img_1_Template, 1, 0, \"img\", 27);\n    i0.ɵɵtemplate(2, FloatingPanelComponent_div_0_ng_template_16_img_2_Template, 1, 0, \"img\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_ng_template_16_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.shopNow(ctx_r26.variant, ctx_r26.product.shopId, ctx_r26.product));\n    });\n    i0.ɵɵelement(6, \"img\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.variant == null ? null : ctx_r10.variant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(12, _c0, ctx_r10.variant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 8, \"productDetails.details.addToCart\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.variant == null ? null : ctx_r10.variant.soldOut)(\"ngStyle\", i0.ɵɵpureFunction1(14, _c0, ctx_r10.variant.soldOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 10, \"productDetails.details.buyNow\"), \" \");\n  }\n}\nfunction FloatingPanelComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8)(3, \"div\", 9)(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, FloatingPanelComponent_div_0_ng_container_6_Template, 3, 4, \"ng-container\", 11);\n    i0.ɵɵtemplate(7, FloatingPanelComponent_div_0_ng_template_7_Template, 2, 4, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, FloatingPanelComponent_div_0_div_9_Template, 3, 5, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 14)(11, \"div\", 15)(12, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.addToWishlist(ctx_r27.variant.specProductId, ctx_r27.variant.isLiked, ctx_r27.product.id, ctx_r27.product));\n    });\n    i0.ɵɵtemplate(13, FloatingPanelComponent_div_0_img_13_Template, 1, 0, \"img\", 17);\n    i0.ɵɵtemplate(14, FloatingPanelComponent_div_0_img_14_Template, 1, 0, \"img\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, FloatingPanelComponent_div_0_ng_container_15_Template, 4, 3, \"ng-container\", 11);\n    i0.ɵɵtemplate(16, FloatingPanelComponent_div_0_ng_template_16_Template, 9, 16, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(8);\n    const _r9 = i0.ɵɵreference(17);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.variant.salePrice)(\"ngIfElse\", _r3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.variant.salePrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r0.variant == null ? null : ctx_r0.variant.isLiked));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.variant == null ? null : ctx_r0.variant.isLiked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.profile && ctx_r0.isShowNotifyFeature)(\"ngIfElse\", _r9);\n  }\n}\nfunction FloatingPanelComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"p\", 52)(3, \"span\", 53);\n    i0.ɵɵtext(4, \"Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"p\", 52)(10, \"span\", 56);\n    i0.ɵɵtext(11, \"Was\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 57);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r29.currencyCode, \" \", ctx_r29.disableCent === \"false\" ? i0.ɵɵpipeBind2(7, 4, ctx_r29.variant.salePrice, \"1.\" + ctx_r29.decimalValue + \"-\" + ctx_r29.decimalValue) : ctx_r29.variant.salePrice, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r29.product.currencyCode, \" \", ctx_r29.disableCent === \"false\" ? i0.ɵɵpipeBind2(14, 7, ctx_r29.product.price, \"1.\" + ctx_r29.decimalValue + \"-\" + ctx_r29.decimalValue) : ctx_r29.product.price, \" \");\n  }\n}\nfunction FloatingPanelComponent_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"p\", 52)(3, \"span\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r30.currencyCode, \" \", ctx_r30.disableCent === \"false\" ? i0.ɵɵpipeBind2(5, 2, ctx_r30.variant.price, \"1.\" + ctx_r30.decimalValue + \"-\" + ctx_r30.decimalValue) : ctx_r30.variant.price, \" \");\n  }\n}\nfunction FloatingPanelComponent_div_1_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n}\nfunction FloatingPanelComponent_div_1_img_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n}\nfunction FloatingPanelComponent_div_1_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n}\nfunction FloatingPanelComponent_div_1_img_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n}\nfunction FloatingPanelComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38)(2, \"div\", 39);\n    i0.ɵɵtemplate(3, FloatingPanelComponent_div_1_div_3_Template, 15, 10, \"div\", 40);\n    i0.ɵɵtemplate(4, FloatingPanelComponent_div_1_div_4_Template, 6, 5, \"div\", 40);\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"div\", 42)(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.addItem(ctx_r35.variant, ctx_r35.product.shopId, ctx_r35.product));\n    });\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.shopNow(ctx_r37.variant, ctx_r37.product.shopId, ctx_r37.product));\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function FloatingPanelComponent_div_1_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.addToWishlist(ctx_r38.variant.specProductId, ctx_r38.variant.isLiked, ctx_r38.product.id, ctx_r38.product));\n    });\n    i0.ɵɵelementStart(12, \"em\");\n    i0.ɵɵtemplate(13, FloatingPanelComponent_div_1_img_13_Template, 1, 0, \"img\", 46);\n    i0.ɵɵtemplate(14, FloatingPanelComponent_div_1_img_14_Template, 1, 0, \"img\", 47);\n    i0.ɵɵtemplate(15, FloatingPanelComponent_div_1_img_15_Template, 1, 0, \"img\", 48);\n    i0.ɵɵtemplate(16, FloatingPanelComponent_div_1_img_16_Template, 1, 0, \"img\", 49);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.variant.salePrice && ctx_r1.variant.salePrice > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.variant.salePrice || ctx_r1.variant.salePrice === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.variant == null ? null : ctx_r1.variant.soldOut)(\"label\", i0.ɵɵpipeBind1(8, 10, \"buttons.addToCart\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.variant == null ? null : ctx_r1.variant.soldOut)(\"label\", i0.ɵɵpipeBind1(10, 12, \"buttons.shopNow\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.variant.isLiked && !ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.variant.isLiked && !ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.variant.isLiked && ctx_r1.isStoreCloud);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.variant.isLiked && ctx_r1.isStoreCloud);\n  }\n}\nexport class FloatingPanelComponent {\n  productLogicService;\n  store;\n  router;\n  detailsService;\n  translate;\n  messageService;\n  cartService;\n  mainDataService;\n  authTokenService;\n  cookieService;\n  $gaService;\n  permissionService;\n  productService;\n  userService;\n  _GACustomEvents;\n  product = {};\n  currency = {};\n  onItemLike = new EventEmitter();\n  variant = {};\n  cartId = '0';\n  decimalValue = 0;\n  currencyCode = '';\n  scConfig = false;\n  disableCent;\n  cartListCount = 0;\n  cartListData = [];\n  authToken;\n  productId;\n  isStoreCloud = environment.isStoreCloud;\n  displayNotifyModal = false;\n  displaySuccessModal = false;\n  successTitleMessage = '';\n  successBodyMessage = '';\n  isLayoutTemplate = false;\n  profile;\n  isShowNotifyFeature = false;\n  userDetails;\n  sessionId;\n  tagName = GaLocalActionEnum;\n  isEmailExist = false;\n  isGoogleAnalytics = false;\n  displayAgeConsentModal = false;\n  displayEligableModal = false;\n  restrictionAge;\n  restrictedProductTobePurchased;\n  constructor(productLogicService, store, router, detailsService, translate, messageService, cartService, mainDataService, authTokenService, cookieService, $gaService, permissionService, productService, userService, _GACustomEvents) {\n    this.productLogicService = productLogicService;\n    this.store = store;\n    this.router = router;\n    this.detailsService = detailsService;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.cartService = cartService;\n    this.mainDataService = mainDataService;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.productService = productService;\n    this.userService = userService;\n    this._GACustomEvents = _GACustomEvents;\n    this.scConfig = environment.isStoreCloud;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.disableCent = localStorage.getItem('DisableCents');\n    let value = localStorage.getItem('CurrencyDecimal');\n    if (value) this.decimalValue = parseInt(value);\n  }\n  ngOnInit() {\n    this.profile = localStorage.getItem('profile');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isShowNotifyFeature = this.permissionService.hasPermission('Notify-Me');\n    this.getCartId();\n    this.currencyCode = this.product.currencyCode;\n  }\n  addItem(productItem, shopId, productDetails) {\n    if (this.isGoogleAnalytics) {\n      this._GACustomEvents.addToCartEvent(productItem, productDetails);\n    }\n    if (this.product.soldOut) return;\n    productItem.quantity = 1;\n    productItem.cartId = this.cartId;\n    let isTrigger = true;\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger) {\n        if (res && res.length > 0) {\n          let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n          if (cartProduct) {\n            if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n              });\n              isTrigger = false;\n              return;\n            }\n          }\n          const reqObj = {\n            \"productId\": this.product.id,\n            \"quantity\": productItem.quantity,\n            \"specsProductId\": this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            \"cartId\": this.cartId,\n            \"priceId\": this.product.channelId == '1' ? productItem.priceId : '0',\n            \"shopId\": shopId,\n            \"sessionId\": productItem.sessionId,\n            \"channelId\": this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails);\n        } else {\n          const reqObj = {\n            \"productId\": this.product.id,\n            \"quantity\": productItem.quantity,\n            \"specsProductId\": this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            \"cartId\": this.cartId,\n            \"priceId\": this.product.channelId == '1' ? productItem.priceId : '0',\n            \"shopId\": shopId,\n            \"sessionId\": productItem.sessionId,\n            \"channelId\": this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails);\n        }\n      }\n    });\n  }\n  createCart(product, productDetails, navigate) {\n    product.sessionId = localStorage.getItem('sessionId');\n    if (!product.sessionId) {\n      product.sessionId = GuidGenerator.newGuid();\n      localStorage.setItem('sessionId', product.sessionId);\n    }\n    this.cartService.addToCart(product).subscribe({\n      next: res => {\n        if (!res.data.userFailedProductEligibility) {\n          if (res?.success) {\n            localStorage.setItem(\"cartId\", res.data.cartItems[0].cartId);\n            this.userDetails = this.store.get('profile');\n            this.sessionId = localStorage.getItem('sessionId');\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.cart'),\n              detail: this.translate.instant('ResponseMessages.successfullyAddedToCart')\n            });\n            this.getAllCart(product.sessionId, navigate);\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.cart'),\n              detail: res.message\n            });\n          }\n        } else {\n          this.restrictionAge = res.data.productAgeRestriction;\n          this.restrictedProductTobePurchased = {\n            'product': product,\n            'productDetails': productDetails,\n            'navigate': navigate\n          };\n          this.displayAgeConsentModal = true;\n        }\n      },\n      error: err => {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  shopNow(productItem, shopId, productDetails) {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(this.tagName.CLICK_ON_BUY_NOW, 'product', 'BUY_NOW', 1, true, {\n        \"product_ID\": productDetails.id,\n        \"product_name\": productDetails.name,\n        \"category_name\": productDetails.categoryName,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        \"session_ID\": this.sessionId,\n        \"shop_ID\": productDetails.shopId,\n        \"product_SKU\": productItem['skuAutoGenerated'],\n        \"ip_Address\": this.store.get('userIP'),\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n        \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n        \"product_tags\": productItem?.bestSeller ? 'Best Seller' : productItem?.newArrival ? 'New Arrival' : productItem?.hotDeals ? 'Hot Deals' : 'None',\n        \"promotion\": productItem?.promotionName ? productItem?.promotionName : 'None'\n      });\n    }\n    if (this.product.soldOut) return;\n    productItem.quantity = 1;\n    productItem.cartId = this.cartId;\n    let isTrigger = true;\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger) {\n        if (res && res.length > 0) {\n          let cartProduct = res.find(x => x.specsProductId === productItem.specProductId);\n          if (cartProduct) {\n            if (productItem.itemPerCustomer && cartProduct.quantity + 1 > productItem.itemPerCustomer) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + productItem.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n              });\n              isTrigger = false;\n              return;\n            }\n          }\n          const reqObj = {\n            \"productId\": this.product.id,\n            \"quantity\": productItem.quantity,\n            \"specsProductId\": this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            \"cartId\": this.cartId,\n            \"priceId\": this.product.channelId == '1' ? productItem.priceId : '0',\n            \"shopId\": shopId,\n            \"sessionId\": productItem.sessionId,\n            \"channelId\": this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails, true);\n        } else {\n          const reqObj = {\n            \"productId\": this.product.id,\n            \"quantity\": productItem.quantity,\n            \"specsProductId\": this.product.channelId == '1' ? productItem.specProductId : this.product?.id,\n            \"cartId\": this.cartId,\n            \"priceId\": this.product.channelId == '1' ? productItem.priceId : '0',\n            \"shopId\": shopId,\n            \"sessionId\": productItem.sessionId,\n            \"channelId\": this.product?.channelId\n          };\n          isTrigger = false;\n          this.createCart(reqObj, productDetails, true);\n        }\n      }\n    });\n  }\n  getAllCart(sessionId, navigate) {\n    let cartData = {\n      sessionId: sessionId\n    };\n    let applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo != '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getCart(cartData).subscribe({\n      next: res => {\n        this.cartListCount = 0;\n        this.cartListData = [];\n        if (res.data?.records?.length) {\n          this.cartListCount = 0;\n          if (res.data.records[0].cartDetails.length) {\n            this.cartListCount = res.data.records[0].cartDetails.length;\n            this.cartListData = res.data.records[0].cartDetails;\n          }\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n          }\n          this.mainDataService.setCartLenghtData(this.cartListCount);\n          this.mainDataService.setCartItemsData(this.cartListData);\n        } else {\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n        }\n        if (navigate) {\n          this.router.navigate(['/cart']);\n        }\n      },\n      error: () => {}\n    });\n  }\n  getCartId() {\n    this.store.subscription('cartProducts').subscribe({\n      next: res => {\n        if (res.length > 0) {\n          this.cartId = res[0].cartId;\n          this.currencyCode = res[0].currencyCode;\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  addToWishlist(specsProductId, flag, productId, productDetails) {\n    this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n    if (!this.authToken) {\n      this.authToken = this.cookieService.get('authToken');\n    }\n    if (!this.authToken) {\n      this.router.navigate(['login'], {\n        queryParams: {\n          returnUrl: `/product/${productId}`\n        }\n      });\n      return;\n    }\n    const obj = {\n      specsProductId,\n      flag,\n      productId: this.product.id,\n      channelId: this.product.channelId\n    };\n    this.detailsService.wishlistToggle(obj).subscribe({\n      next: res => {\n        if (res?.success) {\n          this.variant.isLiked = !this.variant.isLiked;\n          if (!flag) {\n            if (this.isGoogleAnalytics) {\n              this._GACustomEvents.addToWishlistEvent(productDetails, this.variant);\n              this.$gaService.event(this.tagName.ADD_TO_WISHLIST, productDetails.categoryName, productDetails.name, 1, true, {\n                \"product_ID\": productDetails.id,\n                \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                \"session_ID\": this.sessionId,\n                \"shop_ID\": productDetails.shopId\n              });\n            }\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyAddedToWishList')\n            });\n          } else {\n            if (this.isGoogleAnalytics) {\n              this.$gaService.event(\"remove_from_wishlist\", productDetails.categoryName, productDetails.name, 1, true, {\n                \"product_ID\": productDetails.id,\n                \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n                \"session_ID\": this.sessionId,\n                \"shop_ID\": productDetails.shopId\n              });\n            }\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyRemovedToWishList')\n            });\n          }\n        }\n      },\n      error: err => {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  notifyMe() {\n    this.displayNotifyModal = true;\n    const profile = JSON.parse(localStorage.getItem('profile') ?? '');\n    if (profile?.email) {\n      this.isEmailExist = true;\n    } else {\n      this.isEmailExist = false;\n    }\n  }\n  onSubmitNotify(data) {\n    let reqObj = {};\n    reqObj['email'] = data.email ? data.email : '';\n    if (this.product.productVariances.length) {\n      reqObj['specProductId'] = this.product.productVariances[0].specProductId;\n    }\n    if (data.phone) {\n      let dialCode = data.phone.dialCode.substring(1, 4);\n      reqObj['phoneNumber'] = dialCode + data.phone.number.replace(/\\s/g, '');\n    } else {\n      reqObj['phoneNumber'] = '';\n    }\n    this.productService.notifyMeProduct(reqObj).subscribe(res => {\n      if (res.success) {\n        this.successTitleMessage = this.translate.instant(\"notifyMeDetails.thanksForInterest\");\n        this.successBodyMessage = this.translate.instant(\"notifyMeDetails.notifyProductIsAvaialble\");\n        this.displaySuccessModal = true;\n        this.displayNotifyModal = false;\n      }\n    });\n  }\n  onCancel() {\n    this.displaySuccessModal = false;\n    this.displayNotifyModal = false;\n  }\n  onSubmitConsent() {\n    this.displayAgeConsentModal = false;\n    const userProfile = localStorage.getItem(\"profile\") || '';\n    const userId = userProfile ? JSON.parse(userProfile)?.id : null;\n    let data = {\n      sessionId: localStorage.getItem('sessionId') || '',\n      MinimumAgeForProductEligibility: this.restrictionAge\n    };\n    userId ? data.userId = userId : '';\n    const product = this.restrictedProductTobePurchased.product;\n    const productDetails = this.restrictedProductTobePurchased.productDetails;\n    const navigate = this.restrictedProductTobePurchased.navigate;\n    this.userService.updateAgeConsent(data).subscribe({\n      next: res => {\n        this.createCart(product, productDetails, navigate);\n      },\n      error: err => {\n        this.handleError(err.message);\n      }\n    });\n  }\n  closeConsentModal() {\n    this.displayEligableModal = true;\n    this.displayAgeConsentModal = false;\n  }\n  closeEligableModal() {\n    this.displayEligableModal = false;\n  }\n  handleError(message) {\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: message\n    });\n  }\n  last = last;\n  static ɵfac = function FloatingPanelComponent_Factory(t) {\n    return new (t || FloatingPanelComponent)(i0.ɵɵdirectiveInject(i1.ProductLogicService), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.DetailsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i7.CustomGAService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FloatingPanelComponent,\n    selectors: [[\"app-floating-panel\"]],\n    inputs: {\n      product: \"product\",\n      currency: \"currency\",\n      variant: \"variant\"\n    },\n    outputs: {\n      onItemLike: \"onItemLike\"\n    },\n    decls: 6,\n    vars: 10,\n    consts: [[\"class\", \"new-floating-panel\", 4, \"ngIf\"], [\"class\", \"old-floating-panel\", 4, \"ngIf\"], [3, \"titleMessage\", \"bodyMessage\", \"displayModal\", \"cancel\"], [3, \"isEmailExist\", \"displayModal\", \"close\", \"submit\"], [3, \"age\", \"displayModal\", \"submit\", \"cancel\"], [3, \"displayModal\", \"cancel\"], [1, \"new-floating-panel\"], [1, \"d-flex\", \"floating-panel\", \"flex-row\", \"justify-content-space-between\"], [1, \"d-flex\", \"flex-row\", \"floating-panel__prices\"], [1, \"floating-panel__prices__price\"], [1, \"floating-panel__prices__currency\"], [4, \"ngIf\", \"ngIfElse\"], [\"priceView\", \"\"], [\"class\", \"floating-panel__prices__sale-price\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-row\", \"floating-panel__buttons\"], [1, \"d-flex\", \"flex-row-reverse\", \"floating-panel__buttons__action-buttons\"], [1, \"floating-panel__buttons__action-buttons__wish-button\", 3, \"click\"], [\"src\", \"assets/icons/mobile-heart-icon.svg\", \"width\", \"20\", \"height\", \"18\", \"alt\", \"Heart Thin icon\", \"title\", \"Heart Thin icon\", 4, \"ngIf\"], [\"src\", \"assets/icons/filled-heart-icon.svg\", \"width\", \"15\", \"height\", \"15\", \"alt\", \"Heart Thin icon\", \"title\", \"Heart Thin icon\", 4, \"ngIf\"], [\"loggedOut\", \"\"], [1, \"floating-panel__prices__sale-price\"], [\"src\", \"assets/icons/mobile-heart-icon.svg\", \"width\", \"20\", \"height\", \"18\", \"alt\", \"Heart Thin icon\", \"title\", \"Heart Thin icon\"], [\"src\", \"assets/icons/filled-heart-icon.svg\", \"width\", \"15\", \"height\", \"15\", \"alt\", \"Heart Thin icon\", \"title\", \"Heart Thin icon\"], [\"class\", \"floating-panel__buttons__action-buttons__cart-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"floating-panel__buttons__action-buttons__buy-button\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"width:244px\", \"class\", \"floating-panel__buttons__action-buttons__notify-button\", 3, \"click\", 4, \"ngIf\"], [1, \"floating-panel__buttons__action-buttons__cart-button\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-sc.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-sc.svg\"], [1, \"floating-panel__buttons__action-buttons__buy-button\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/shopping-cart-white.svg\"], [1, \"floating-panel__buttons__action-buttons__notify-button\", 2, \"width\", \"244px\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/notify-me.svg\"], [1, \"floating-panel__buttons__action-buttons__cart-button\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"floating-panel__buttons__action-buttons__buy-button\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"old-floating-panel\"], [1, \"floating-panel\", \"shadow-1\", \"lg:px-11\", \"md:px-8\", \"px-5\"], [1, \"grid\", \"h-100\", \"mt-0\"], [\"class\", \"col-12 col-md-6 col-lg-8\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-6\", \"col-lg-4\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"buttons\", \"lg:px-5\", \"md:px-3\", \"sm:px-2\", \"px-1\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"mr-1\", \"width-50\", \"main-btn\", 3, \"disabled\", \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"ml-1\", \"width-50\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [\"type\", \"button\", 1, \"ml-1\", \"width-50\", \"wishlist-btn\", \"cursor-pointer\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty-sc.svg\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart-sc.svg\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-6\", \"col-lg-8\"], [1, \"flex-row\", \"lg:justify-content-start\", \"md:justify-content-center\", \"mt-3\", \"mb-2\", \"mobile-currency-center\"], [1, \"price\", \"m-0\", \"font-size-16\"], [1, \"now-currency\"], [1, \"tag-now\"], [1, \"flex-row\", \"lg:justify-content-start\", \"md:justify-content-center\", \"mobile-currency-center\"], [1, \"was-tag\"], [1, \"tag-was\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/ionic-md-heart-empty-sc.svg\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/fill-heart-sc.svg\"]],\n    template: function FloatingPanelComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, FloatingPanelComponent_div_0_Template, 18, 8, \"div\", 0);\n        i0.ɵɵtemplate(1, FloatingPanelComponent_div_1_Template, 17, 14, \"div\", 1);\n        i0.ɵɵelementStart(2, \"app-success-info-modal\", 2);\n        i0.ɵɵlistener(\"cancel\", function FloatingPanelComponent_Template_app_success_info_modal_cancel_2_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"app-notify-modal\", 3);\n        i0.ɵɵlistener(\"close\", function FloatingPanelComponent_Template_app_notify_modal_close_3_listener() {\n          return ctx.displayNotifyModal = false;\n        })(\"submit\", function FloatingPanelComponent_Template_app_notify_modal_submit_3_listener($event) {\n          return ctx.onSubmitNotify($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"app-age-consent-modal\", 4);\n        i0.ɵɵlistener(\"submit\", function FloatingPanelComponent_Template_app_age_consent_modal_submit_4_listener() {\n          return ctx.onSubmitConsent();\n        })(\"cancel\", function FloatingPanelComponent_Template_app_age_consent_modal_cancel_4_listener() {\n          return ctx.closeConsentModal();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"app-ineligable-purchase-modal\", 5);\n        i0.ɵɵlistener(\"cancel\", function FloatingPanelComponent_Template_app_ineligable_purchase_modal_cancel_5_listener() {\n          return ctx.closeEligableModal();\n        });\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"titleMessage\", ctx.successTitleMessage)(\"bodyMessage\", ctx.successBodyMessage)(\"displayModal\", ctx.displaySuccessModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"isEmailExist\", ctx.isEmailExist)(\"displayModal\", ctx.displayNotifyModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"age\", ctx.restrictionAge)(\"displayModal\", ctx.displayAgeConsentModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"displayModal\", ctx.displayEligableModal);\n      }\n    },\n    dependencies: [i8.NgIf, i8.NgStyle, i9.ButtonDirective, i10.SuccessInfoModalComponent, i11.NotifyModalComponent, i12.AgeConsentModalComponent, i13.IneligablePurchaseModalComponent, i8.DecimalPipe, i3.TranslatePipe],\n    styles: [\".new-floating-panel[_ngcontent-%COMP%]   .floating-panel[_ngcontent-%COMP%] {\\n  padding: 20px 32px;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__prices[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__prices__price[_ngcontent-%COMP%] {\\n  color: #191919;\\n  font-family: var(--regular-font);\\n  font-size: 32px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  text-transform: uppercase;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__prices__sale-price[_ngcontent-%COMP%] {\\n  color: #929FA5;\\n  font-family: var(--regular-font);\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 40px;\\n  text-decoration: line-through;\\n  text-transform: uppercase;\\n  margin-left: 8px;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__prices__currency[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__share-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 16px;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-radius: 4px;\\n  background: #E1E9EC;\\n  border: none;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__action-buttons[_ngcontent-%COMP%] {\\n  gap: 10px;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__action-buttons__wish-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 16px;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-radius: 4px;\\n  background: #E1E9EC;\\n  border: none;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__action-buttons__notify-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 4px 12px 4px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  border-radius: 4px;\\n  border: 2px solid #204E6E;\\n  color: #204E6E;\\n  font-family: var(--regular-font);\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 48px;\\n  \\n\\n  letter-spacing: 0.192px;\\n  text-transform: uppercase;\\n  background: transparent;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__action-buttons__cart-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 4px 24px 4px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  border-radius: 4px;\\n  border: 1px solid #204E6E;\\n  color: #204E6E;\\n  font-family: var(--regular-font);\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 48px; \\n\\n  letter-spacing: 0.192px;\\n  text-transform: uppercase;\\n  background: transparent;\\n}\\n.new-floating-panel[_ngcontent-%COMP%]   .floating-panel__buttons__action-buttons__buy-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 4px 24px 4px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  border-radius: 4px;\\n  background: #204E6E;\\n  color: #FFF;\\n  font-family: var(--regular-font);\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 48px; \\n\\n  letter-spacing: 0.192px;\\n  text-transform: uppercase;\\n  border: none;\\n}\\n\\n.old-floating-panel[_ngcontent-%COMP%]   .floating-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  width: 1350px;\\n  height: 86px;\\n  bottom: -31px;\\n  z-index: 10;\\n  background-color: white;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .floating-panel[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .floating-panel[_ngcontent-%COMP%]   .h-100[_ngcontent-%COMP%] {\\n  min-height: 86px;\\n  height: auto;\\n}\\n@media screen and (max-width: 768px) {\\n  .old-floating-panel[_ngcontent-%COMP%]   .floating-panel[_ngcontent-%COMP%] {\\n    bottom: 0px;\\n    height: auto !important;\\n    padding-right: 1rem !important;\\n    padding-left: 1rem !important;\\n    width: 100%;\\n  }\\n  .old-floating-panel[_ngcontent-%COMP%]   .width-50[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-family: var(--regular-font) !important;\\n    text-transform: uppercase;\\n    width: 66%;\\n  }\\n  .old-floating-panel[_ngcontent-%COMP%]   .mobile-currency-center[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .width-50[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ffffff;\\n  background-color: #e5edf1;\\n  border-radius: 25px;\\n  border: 1px solid #e5edf1;\\n  width: 46px;\\n  height: 40px;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .now-currency[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  color: #ffcb05;\\n  background: #faf5e1;\\n  font-size: 10px;\\n  font-weight: 300;\\n  font-family: var(--medium-font) !important;\\n  margin-right: 8px;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .tag-now[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 300;\\n  color: #000000;\\n  font-family: var(--medium-font) !important;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .currency-code[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 300;\\n  color: #000000;\\n  font-family: var(--medium-font) !important;\\n  margin-left: 8px;\\n  margin-right: 6px;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .was-tag[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  color: #a3a3a3;\\n  background: #f1f1f1;\\n  font-size: 10px;\\n  font-weight: 300;\\n  font-family: var(--medium-font) !important;\\n  margin-right: 8px;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .tag-was[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 300;\\n  color: #a3a3a3;\\n  text-decoration-line: line-through;\\n  text-decoration-color: #707070;\\n  text-decoration-thickness: 1px;\\n  font-family: var(--regular-font) !important;\\n}\\n.old-floating-panel[_ngcontent-%COMP%]   .was-currency-code[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 300;\\n  color: #a3a3a3;\\n  text-decoration-line: line-through;\\n  text-decoration-color: #707070;\\n  text-decoration-thickness: 1px;\\n  font-family: var(--regular-font) !important;\\n  margin-left: 6px;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}