<div *ngIf="isLayoutTemplate" class="new-product-details">

  <!-- Section: Product Price Display for Mobile View -->
  <div
    *ngIf="screenWidth < 768"
    class="d-flex flex-row details__product-info__prices price-product justify-content-between"
  >
    <!-- Display Product Price -->
    <div class="details__product-info__prices__price">
      <!-- Currency Code -->
      <span class="details__product-info__prices__currency">
        {{ product?.currencyCode }}
      </span>
      <!-- Product Price or Sale Price -->
      {{
        (selectedVariant.salePrice
          ? selectedVariant.salePrice
          : selectedVariant?.price) | number: (disableCent == "false"
          ? "1." + decimalValue + "-" + decimalValue
          : "")
      }}
      <!-- Sale Price Display -->
      <div
        *ngIf="selectedVariant.salePrice"
        class="details__product-info__prices__sale-price"
        style="margin-left: 0 !important;"
      >
        {{ product?.currencyCode }}
        {{
          selectedVariant?.price | number: (disableCent == "false"
            ? "1." + decimalValue + "-" + decimalValue
            : "")
        }}
      </div>
      <!-- Low Stock Warning -->
      <span
        *ngIf="selectedVariant.stockStatusId === 3"
        class="details__product-info__low-stock d-block"
      >
        {{"productDetails.details.only" | translate}}
        &nbsp;{{selectedVariant.quantity}}&nbsp;
        {{"productDetails.details.itemsLeft" | translate}}
      </span>
    </div>

    <!-- Share Button -->
    <div class="details__product-info__share">
      <button
        (click)="showShareModal()"
        class="details__product-info__buttons__share-button"
      >
        <img alt="No Image" src="assets/icons/share-icon.svg" />
      </button>
    </div>
  </div>

  <!-- Section: Color Options for Mobile View -->
  <div
  *ngIf="screenWidth < 768 && colors.length > 0"
  class="details__product-info__attributes__colors"
>
  <!-- Colors Title -->
  <div class="details__product-info__attributes__colors__title">
    {{"productDetails.details.colors" | translate}}:
  </div>

  <!-- Color Options Container -->
  <div class="color-options">
    <div
      class="color-option"
      (click)="onColorChange(color)"
      *ngFor="let color of colors"
      [ngClass]="{
        'selected': color.value === selectedColor
      }"
    >
      <!-- Single Color Display -->
      <div
        *ngIf="color.value !== 'multi-color'"
        class="color-circle"
        [style.background-color]="color.value"
      ></div>

      <!-- Multi-Color Display -->
      <div
        *ngIf="color.value === 'multi-color'"
        class="multi-color-circle"
      >
        {{"productDetails.details.multiColor" | translate}}
      </div>
    </div>
  </div>
</div>

<!-- Section: Size Options for Mobile View -->
<div
  *ngIf="screenWidth < 768 && sizes.length > 0"
  style="padding: 0 17px"
  class="mobile__product-info__attributes__sizes"
>
  <div class="mobile__sizes__container">
    <!-- Size Title -->
    <ng-container *ngIf="!checkLabel('Size', 0) else isSizeLabelMobile">
      <div class="mobile__sizes__title">
        Sizes:
      </div>
    </ng-container>

    <!-- Display Sizes -->
    <ng-template #isSizeLabelMobile>
      <div class="mobile__sizes__title">
        <div *ngFor="let size of varianceSpec let parentIndex = index">
          <div class="mobile__sizes__label">
            {{ size.label || size.name }}
          </div>

          <!-- Primary Size Options -->
          <ng-container *ngIf="size.name === 'Size' else secondLabelMobile">
            <div class="mobile__sizes__options">
              <div
                (click)="onSizeChange(size, 0)"
                *ngFor="let size of size1"
              >
                <div
                  [ngClass]="{
                    'mobile__sizes__selectedValue': size.value === selectedSize,
                    'mobile__sizes__dimmed': !isSizeAvailable(size)
                  }"
                  class="mobile__sizes__value"
                >
                  {{ size.value }}
                </div>
              </div>
            </div>
          </ng-container>
          <!-- Secondary Size Options -->
          <ng-template #secondLabelMobile>
            <div class="mobile__sizes__options">
              <div
                (click)="onSizeChange(size, 1)"
                *ngFor="let size of size2"
              >
                <div
                  [ngClass]="{
                    'mobile__sizes__selectedValue': size.value === selectedSize2,
                    'mobile__sizes__dimmed': !isSizeAvailable(size)
                  }"
                  class="mobile__sizes__value"
                >
                  {{ size.value }}
                </div>
              </div>
            </div>
          </ng-template>



        </div>
      </div>
    </ng-template>

    <!-- Size Guide Link -->
    <a
      (click)="showSizeModal()"
      *ngIf="sizeGuidImage"
      class="mobile__sizes__size-guide"
    >
      {{"productDetails.details.sizeGuide" | translate}}
    </a>
  </div>
</div>

  <!-- Section: Delivery Address for Mobile View -->
  <div
    *ngIf="(screenWidth < 768 && profile) && (selectedAddress && (!selectedAddress?.addressLabel?.includes('with no address')))"
    class="deliver-to"
  >
    <!-- Delivery Address -->
    <img alt="No Image" src="assets/icons/location.svg" />
    <span class="deliver-to-tag">
      {{"productDetails.details.deliverTo" | translate}}
    </span>
    <span class="home-location">{{ selectedAddress.addressLabel }}</span>
  </div>

  <!-- Section: Return Policy for Mobile View -->
  <div *ngIf="screenWidth < 768 && product.isRefundable" class="seller-info">
    <div class="d-flex flex-row">
      <div class="align-self-center">
        <img class="mr-2" alt="No Image" src="assets/icons/return-policy-mobile-icon.svg" />
      </div>
      <div>
        <div>
          <span class="return-policy-merchant">
            {{"sellerInfo.daysReturnable" | translate}} {{product.returnPeriod}} {{"sellerInfo.days" | translate}}.
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Section: Seller Information for Mobile View -->
  <div *ngIf="screenWidth < 768" class="seller-info">
    <div class="d-flex flex-row">
      <div class="align-self-center">
        <img class="mr-2" alt="No Image" src="assets/icons/shop.svg" />
      </div>
      <div>
        <div>
          <span class="seller-info-merchant">
            {{"sellerInfo.sellerInformation" | translate}}
          </span>
          <div class="d-inline-flex justify-content-between w-100">
            <!-- Seller Name -->
            <div class="seller-shop-merchant">
              {{ product.sellerName }}
            </div>
            <!-- More from Seller Link -->
            <div
              class="more-merchant"
              (click)="triggerAnalytics('CLICK_ON_MORE_SELLER', 'MORE_FROM_SELLER')"
              [routerLink]="'/merchants/merchant-product/' + product.shopId + '/' + product.sellerName"
            >
              {{"sellerInfo.moreSeller" | translate}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section: Action Buttons for Mobile View -->
  <div
    *ngIf="screenWidth < 768"
    class="flex-row details__product-info__buttons justify-content-end"
  >
    <div
      class="d-flex details__product-info__buttons__action-buttons mt-4"
      [ngClass]="{'add-tocart': isMobileTemplate, 'add-tocart-old': !isMobileTemplate}"
    >
      <!-- Logged-in User Actions -->
      <ng-container *ngIf="profile && isShowNotifyFeature; else loggedOut">
        <!-- Add to Cart Button -->
        <button
          class="details__product-info__buttons__action-buttons__cart-button btn-width"
          *ngIf="!selectedVariant.soldOut"
          style="border-radius: 8px"
          (click)="addItem(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/add-cart.svg" />
          {{"productDetails.details.addToCart" | translate}}
        </button>
        <!-- Notify Me Button -->
        <button
          class="details__product-info__buttons__action-buttons__notify-button"
          *ngIf="selectedVariant?.soldOut"
          style="border-radius: 8px; width: 244px"
          (click)="notifyMe()"
        >
          <img alt="No Image" src="assets/icons/notify-me.svg" />
          {{"productDetails.details.notifyMe" | translate}}
        </button>
        <!-- Buy Now Button -->
        <button
          class="details__product-info__buttons__action-buttons__buy-button buy-now"
          *ngIf="!selectedVariant.soldOut"
          style="border-radius: 8px"
          (click)="shopNow(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/buy-now.svg" />
          {{"productDetails.details.buyNow" | translate}}
        </button>
      </ng-container>

      <!-- Logged-out User Actions -->
      <ng-template #loggedOut>
        <button
          class="details__product-info__buttons__action-buttons__cart-button btn-width"
          [disabled]="selectedVariant?.soldOut"
          [ngStyle]="{opacity: selectedVariant.soldOut ? '0.5' : ''}"
          (click)="addItem(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/add-cart.svg" />
          {{"productDetails.details.addToCart" | translate}}
        </button>
        <button
          class="details__product-info__buttons__buy-button buy-now"
          [disabled]="selectedVariant?.soldOut"
          [ngStyle]="{opacity: selectedVariant.soldOut ? '0.5' : ''}"
          (click)="shopNow(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/buy-now.svg" />
          {{"productDetails.details.buyNow" | translate}}
        </button>
      </ng-template>
    </div>
  </div>

  <!-- Section: Product Information for Mobile View -->
    <div class="details">
    <!-- Product Information -->
      <div [ngClass]="{'flex-column':screenWidth<=1200}" class="d-flex flex-row">
     <!-- Product Name -->
        <div class="details__product-info" *ngIf="screenWidth >= 768">
          <div class="d-flex">
            <div class="d-inline-flex w-100">
              <div class="details__product-info__name" data-placement="top" data-toggle="tooltip"
                   title="{{product?.name}}">
                {{product?.name}}
                <div *ngIf="product?.badgesList?.length" class="details__product-info__badges-row">
                  <img *ngFor="let badge of product.badgesList" [src]="getImageUrl(badge.desktopImage)" [alt]="badge.name" class="details__product-info__badge-image" />
                </div>
              </div>

            </div>

          </div>
          <!-- Section: Product Specifications -->
  <div
  class="d-flex flex-row justify-content-space-between details__product-info__specifications"
  >
  <!-- Textual Specifications Section -->
  <div
    class="d-inline-flex flex-column details__product-info__specifications__text"
  >
    <!-- Brand Information -->
    <div *ngIf="selectedVariant?.specs?.Brand">
      {{ "productDetails.details.brand" | translate }}:
      {{ selectedVariant.specs?.Brand?.value }}
    </div>

    <!-- SKU (Stock Keeping Unit) -->
    <div>
      {{ "productDetails.details.sku" | translate }}:
      <span class="details__product-info__specifications__text__bold">
        {{ selectedVariant?.sku }}
      </span>
    </div>

    <!-- Auto-Generated SKU -->
    <div>
      {{ "productDetails.details.skuAutoGenerated" | translate }}:
      <span class="details__product-info__specifications__text__bold">
        {{ selectedVariant?.skuAutoGenerated }}
      </span>
    </div>

    <!-- Additional Product Attributes (e.g., Brand) -->
    <div *ngFor="let productAttributes of product.productAttributeValues">
      <span *ngIf="productAttributes?.attributeType === 'brand'">
        {{ "productDetails.details.brand" | translate }}:
        <span class="details__product-info__specifications__text__bold">
          {{ productAttributes?.attributeValue }}
        </span>
      </span>
    </div>
  </div>

  <!-- Labels Section (e.g., Sale Percent, Stock Status) -->
  <div class="d-inline-flex details__product-info__specifications__labels">
    <!-- Sale Percentage Label -->
    <div
      *ngIf="selectedVariant?.salePercent > 0"
      class="details__product-info__specifications__labels__green-label"
    >
      {{ selectedVariant?.salePercent?.toFixed(0) }}%
      {{"productDetails.details.off" | translate}}
    </div>

    <!-- Stock Status Label -->
    <div
      *ngIf="selectedVariant?.soldOut"
      class="d-inline-flex justify-content-normal"
    >
      <div class="details__product-info__specifications__stock">
        {{"productDetails.details.outOfStock" | translate}}
      </div>
    </div>
  </div>
  </div>



      <!-- Section: Low Stock Notification -->
  <ng-container
  *ngIf="product.productVariances.length && screenWidth > 767"
  >
  <div class="d-flex justify-content-end">
    <!-- Low Stock Message -->
    <div
      *ngIf="selectedVariant.stockStatusId === 3"
      class="details__product-info__low-stock"
    >
      <!-- Display translated 'Only' message -->
      {{"productDetails.details.only" | translate}}

      <!-- Show available quantity -->
      {{ selectedVariant.quantity }}

      <!-- Display translated 'Items Left in Stock' message -->
      {{"productDetails.details.leftInStock" | translate}}
    </div>
  </div>
  </ng-container>




<!-- Section: Product Attributes -->
<div class="details__product-info__attributes">

  <!-- Colors Attribute -->
  <div *ngIf="colors.length > 0" class="details__product-info__attributes__colors">
    <!-- Colors Title -->
    <div class="product-colors-section">
      <h3 class="product-colors-title">
        {{ "productDetails.details.colors" | translate }}
      </h3>
    </div>

    <!-- Color Options -->
    <div class="d-flex flex-row color-options">
      <!-- Iterate through colors -->
      <div
        (click)="onColorChange(color)"
        *ngFor="let color of colors"
        class="color-option"
        [class.selected]="color.value === selectedColor"
      >
        <!-- Display individual color -->
        <div
          *ngIf="color.value !== 'multi-color'"
          [style.background-color]="color.value"
          class="color-circle"
        ></div>

        <!-- Display 'Multi-Color' option -->
        <div
          *ngIf="color.value === 'multi-color'"
          class="multi-color-circle"
        >
          {{ "productDetails.details.multiColor" | translate }}
        </div>
      </div>
    </div>
  </div>

  <!-- Sizes Attribute -->
  <div *ngIf="sizes.length > 0" class="details__product-info__attributes__sizes">
    <div class="d-flex flex-row justify-content-space-between">
      <ng-container *ngIf="!checkLabel('Size', 0) else isSizeLabel">
        <div class="details__product-info__attributes__sizes__title">
          Sizes:
        </div>
      </ng-container>

      <ng-template #isSizeLabel>
        <div class="details__product-info__attributes__sizes__title">

          <div *ngFor="let size of varianceSpec; let parentIndex = index">
            <div class="span-text mrg-btm-10">
              {{ size.label || size.name }}
            </div>



<!-- Primary Size Options -->
<ng-container *ngIf="size.name === 'Size' else secondLabel">
  <div class="d-flex flex-row">

    <div
      (click)="onSizeChange(size, 0)"
      *ngFor="let size of size1"
      [ngClass]="{
        'details__product-info__attributes__sizes__selectedValue': size.value === selectedSize,
        'dimmed': !isSizeAvailable(size)
      }"
      class="details__product-info__attributes__sizes__value"
    >
      {{ size.value }}
    </div>
  </div>
</ng-container>

<!-- Secondary Size Options -->
<ng-template #secondLabel>
  <div class="d-flex flex-row">
    <div
      (click)="onSizeChange(size, 1)"
      *ngFor="let size of size2"
      [ngClass]="{
        'details__product-info__attributes__sizes__selectedValue': size.value === selectedSize2,
        'dimmed': !isSizeAvailable(size)
      }"
      class="details__product-info__attributes__sizes__value"
    >
      {{ size.value }}
    </div>
  </div>
</ng-template>

          </div>
        </div>
      </ng-template>

      <a
        (click)="showSizeModal()"
        *ngIf="sizeGuidImage"
        class="details__product-info__attributes__sizes__size-guide"
      >
        {{ "productDetails.details.sizeGuide" | translate }}
      </a>
    </div>
  </div>

</div>

  <!-- Section: Product Pricing -->
  <div class="d-flex flex-row details__product-info__prices">

    <!-- Main Price Display -->
    <div class="details__product-info__prices__price">
      <!-- Currency Code -->
      <span class="details__product-info__prices__currency">
        {{ product?.currencyCode }}
      </span>

      <!-- Sale Price or Regular Price -->
      <ng-container *ngIf="selectedVariant.salePrice else priceView">
        <!-- Show sale price with or without decimals -->
        {{ disableCent === "false"
          ? (selectedVariant.salePrice | number: "1." + decimalValue + "-" + decimalValue)
          : selectedVariant.salePrice  }}
      </ng-container>

      <!-- Regular Price Template -->
      <ng-template #priceView>
        <!-- Show regular price with or without decimals -->
        {{ disableCent === "false"
          ? (selectedVariant.price | number: "1." + decimalValue + "-" + decimalValue)
          : (selectedVariant.price | number: (disableCent == "false" ? "1." + decimalValue + "-" + decimalValue : "")) }}
      </ng-template>
    </div>

    <!-- Sale Price Display -->
    <div
      *ngIf="selectedVariant?.salePrice"
      class="details__product-info__prices__sale-price"
    >
      <!-- Currency Code -->
      {{ product?.currencyCode }}
      <!-- Original Price with or without decimals -->
      {{ disableCent === "false"
        ? (selectedVariant?.price | number: "1." + decimalValue + "-" + decimalValue)
        : (selectedVariant?.price | number: (disableCent == "false" ? "1." + decimalValue + "-" + decimalValue : ""))}}
    </div>
  </div>

  <!-- Section: Product Action Buttons -->
  <div
    [ngClass]="
      screenWidth <= 1200
        ? 'justify-content-space-between'
        : 'justify-content-end'
    "
    class="d-flex flex-row details__product-info__buttons"
  >
    <!-- Share Button -->
    <button
      (click)="showShareModal()"
      *ngIf="screenWidth <= 1200"
      class="details__product-info__buttons__share-button"
    >
      <img alt="No Image" src="assets/icons/share-icon.svg" />
    </button>

    <!-- Wishlist and Action Buttons -->
    <div class="d-flex flex-row-reverse details__product-info__buttons__action-buttons">
      <button
      (click)="showShareModal()"
      *ngIf="screenWidth >= 1200"
      class="details__product-info__buttons__share-button"
    >
      <img alt="No Image" src="assets/icons/share-icon.svg" />
    </button>
      <!-- Wishlist Button -->
      <button
        (click)="addToWishlist(selectedVariant?.specProductId, selectedVariant?.isLiked, product)"
        *ngIf="screenWidth > 700"
        class="details__product-info__buttons__action-buttons__wish-button"
      >
        <img
          *ngIf="!selectedVariant?.isLiked"
          alt="Heart Thin icon"
          height="18"
          src="assets/icons/mobile-heart-icon.svg"
          title="Heart Thin icon"
          width="20"
        />
        <img
          *ngIf="selectedVariant?.isLiked"
          alt="Heart Thin icon"
          height="15"
          src="assets/icons/filled-heart-icon.svg"
          title="Heart Thin icon"
          width="15"
        />
      </button>
   
      <!-- Action Buttons for Cart and Purchase -->
      <ng-container *ngIf="screenWidth > 700">
        <ng-container *ngIf="profile && isShowNotifyFeature; else loggedOut">
          <!-- Add to Cart Button -->
          <button
            class="details__product-info__buttons__action-buttons__cart-button"
            *ngIf="!selectedVariant.soldOut"
            (click)="addItem(selectedVariant, product.shopId, product)"
          >
            <img
              *ngIf="!scConfig"
              alt="No Image"
              src="assets/icons/shopping-cart.svg"
            />
            <img
              *ngIf="scConfig"
              alt="No Image"
              src="assets/icons/shopping-cart-sc.svg"
            />
            {{"productDetails.details.addToCart" | translate}}
          </button>

          <!-- Buy Now Button -->
          <button
            class="details__product-info__buttons__action-buttons__buy-button"
            *ngIf="!selectedVariant.soldOut"
            (click)="shopNow(selectedVariant, product.shopId, product)"
          >
            <img alt="No Image" src="assets/icons/shopping-cart-white.svg" />
            {{"productDetails.details.buyNow" | translate}}
          </button>

          <!-- Notify Me Button -->
          <button
            class="details__product-info__buttons__action-buttons__notify-button"
            *ngIf="selectedVariant?.soldOut"
            style="width: 244px"
            (click)="notifyMe()"
          >
            <img alt="No Image" src="assets/icons/notify-me.svg" />
            {{"productDetails.details.notifyMe" | translate}}
          </button>
        </ng-container>

        <!-- Logged Out State -->
        <ng-template #loggedOut>
          <!-- Add to Cart Button (Disabled for Sold Out) -->
          <button
            class="details__product-info__buttons__action-buttons__cart-button"
            [disabled]="selectedVariant?.soldOut"
            [ngStyle]="{ opacity: selectedVariant.soldOut ? '0.5' : '' }"
            (click)="addItem(selectedVariant, product.shopId, product)"
          >
            <img
              *ngIf="!scConfig"
              alt="No Image"
              src="assets/icons/shopping-cart.svg"
            />
            <img
              *ngIf="scConfig"
              alt="No Image"
              src="assets/icons/shopping-cart-sc.svg"
            />
            {{"productDetails.details.addToCart" | translate}}
          </button>

          <!-- Buy Now Button (Disabled for Sold Out) -->
          <button
            class="details__product-info__buttons__action-buttons__buy-button"
            [disabled]="selectedVariant?.soldOut"
            [ngStyle]="{ opacity: selectedVariant.soldOut ? '0.5' : '' }"
            (click)="shopNow(selectedVariant, product.shopId, product)"
          >
            <img alt="No Image" src="assets/icons/shopping-cart-white.svg" />
            {{"productDetails.details.buyNow" | translate}}
          </button>
        </ng-template>
      </ng-container>
    </div>
  </div>

  <!-- Section: Action Buttons for Small Screens -->
  <div
    *ngIf="screenWidth <= 700"
    class="d-flex flex-row details__product-info__buttons justify-content-end"
  >
    <!-- Container for Action Buttons -->
    <div class="d-flex details__product-info__buttons__action-buttons">
      <!-- Buttons for Logged-In Users -->
      <ng-container *ngIf="profile && isShowNotifyFeature; else loggedOut">
        <!-- Add to Cart Button -->
        <button
          class="details__product-info__buttons__action-buttons__cart-button"
          *ngIf="!selectedVariant.soldOut"
          (click)="addItem(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/shopping-cart.svg" />
          {{"productDetails.details.addToCart" | translate}}
        </button>

        <!-- Notify Me Button -->
        <button
          class="details__product-info__buttons__action-buttons__notify-button"
          *ngIf="selectedVariant?.soldOut"
          style="width: 244px"
          (click)="notifyMe()"
        >
          <img alt="No Image" src="assets/icons/notify-me.svg" />
          {{"productDetails.details.notifyMe" | translate}}
        </button>

        <!-- Wishlist Button -->
        <button
          (click)="addToWishlist(selectedVariant?.specProductId, selectedVariant?.isLiked, product)"
          class="details__product-info__buttons__action-buttons__wish-button"
        >
          <img alt="No Image" src="assets/icons/wish-icon.svg" />
        </button>
      </ng-container>

      <!-- Buttons for Logged-Out Users -->
      <ng-template #loggedOut>
        <!-- Add to Cart Button (Disabled for Sold Out) -->
        <button
          class="details__product-info__buttons__action-buttons__cart-button"
          [disabled]="selectedVariant?.soldOut"
          [ngStyle]="{ opacity: selectedVariant.soldOut ? '0.5' : '' }"
          (click)="addItem(selectedVariant, product.shopId, product)"
        >
          <img alt="No Image" src="assets/icons/shopping-cart.svg" />
          {{"productDetails.details.addToCart" | translate}}
        </button>

        <!-- Wishlist Button -->
        <button
          (click)="addToWishlist(selectedVariant?.specProductId, selectedVariant?.isLiked, product)"
          class="details__product-info__buttons__action-buttons__wish-button"
        >
          <img alt="No Image" src="assets/icons/wish-icon.svg" />
        </button>
      </ng-template>
    </div>
  </div>



        </div>











     <!-- Section: Seller Info for Large Screens -->
  <div
  class="d-inline-flex details__seller-info-section"
  *ngIf="screenWidth >= 768"
  >
  <!-- Seller Info Component -->
  <app-seller-info [product]="product"></app-seller-info>
  </div>




      </div>








      <!-- Section: Tab View for Product Details -->
  <div class="details__tabView">
    <div class="card">
      <p-tabView>
        <!-- Tab Panel: Product Description -->
        <p-tabPanel
          header="{{'productDetails.details.description' | translate}}"
        >
          <div class="details__tabView__description__title">
            {{"productDetails.details.description" | translate}}
          </div>

          <!-- Display Product Description for Channel 1 -->
          <p *ngIf="channelId === 1" class="details__tabView__description__value ql-editor">
            <span *ngIf="descriptionBase64" [innerHTML]="sanitizedDescription"></span>
            <span *ngIf="!descriptionBase64">{{ product.description }}</span>
          </p>

          <!-- Display Product Description for Channel 2 -->
          <div *ngIf="channelId === 2" class="d-flex inner-html ql-editor" [innerHTML]="sanitizedDescription"></div>

        </p-tabPanel>

        <!-- Tab Panel: Product Specification -->
        <p-tabPanel
          header="{{'productDetails.details.specification' | translate}}"
        >
          <div class="d-flex flex-column">
            <!-- Display Auto-Generated and Merchant SKUs -->
            <div class="details-specification">
              <div class="specification-name">Product SKU:</div>
              <div class="specification-value">
                {{selectedVariant.skuAutoGenerated}}
              </div>
            </div>
            <div class="details-specification">
              <div class="specification-name">Merchant SKU:</div>
              <div class="specification-value">{{selectedVariant.sku}}</div>
            </div>
            <!-- Display Weight Variant Specifications -->
            <ng-container
              *ngIf="selectedVariant.varianceSpecs.length > 2; else productVariance"
            >
              <ng-container
                *ngFor="let variantSpec of selectedVariant.varianceSpecs"
              >
                <div
                  class="details-specification"
                  *ngIf="variantSpec.name === 'Weight'"
                >
                  <div class="specification-name">{{variantSpec.name}}:</div>
                  <div class="specification-value">{{variantSpec.value}} {{ selectedVariant?.specs[variantSpec.name]?.unit}}</div>
                </div>
              </ng-container>
            </ng-container>

            <!-- Display Product Specifications if Weight Variant Specs are not available -->
            <ng-template #productVariance>
              <ng-container *ngFor="let productSpec of product.productSpecs">
                <div class="details-specification" *ngIf="productSpec.name === 'Weight'">
                  <div class="specification-name">{{productSpec.name}}:</div>
                  <div class="specification-value">{{productSpec.value}} {{productSpec.unit}}</div>
                </div>
              </ng-container>
            </ng-template>

            <div class="details-specification">
              <div class="specification-name d-flex">

                <!-- Display Other Variant Specifications Labels -->
                <ng-container *ngIf="filteredVarianceSpecs.length > 2; else otherProductVarianceLabels">
                  <ng-container *ngFor="let variantSpec of filteredVarianceSpecs; let i = index">
                      {{variantSpec.name}}
                      <p *ngIf="i !== filteredVarianceSpecs.length - 1"> X</p>
                      <p *ngIf="i === filteredVarianceSpecs.length - 1">:</p>
                  </ng-container>
                </ng-container>

                <!-- Display Product Specifications if Other Variant Specs Labels are not available -->
                <ng-template #otherProductVarianceLabels>
                  <ng-container *ngIf="filteredProductSpecs.length > 0">
                    <ng-container *ngFor="let productSpec of filteredProductSpecs; let i = index">
                      {{ productSpec.name }}
                      <p *ngIf="i !== filteredProductSpecs.length - 1">X</p>
                      <p *ngIf="i === filteredProductSpecs.length - 1">:</p>
                    </ng-container>
                  </ng-container>
                </ng-template>
              </div>
              <div class="specification-value d-flex">

                <!-- Display Other Variant Specifications Values -->
                <ng-container *ngIf="filteredVarianceSpecs.length > 2; else otherProductVarianceValues">
                  <ng-container
                    *ngFor="let variantSpec of filteredVarianceSpecs; let i = index"
                  >
                      {{variantSpec.value}} {{ selectedVariant?.specs[variantSpec.name]?.unit}}
                      <p *ngIf="i !== filteredVarianceSpecs.length - 1">X</p>
                  </ng-container>
                </ng-container>

                <!-- Display Product Specifications if Other Variant Specs Values are not available -->
                <ng-template #otherProductVarianceValues>
                  <ng-container *ngIf="filteredProductSpecs.length > 0">
                    <ng-container *ngFor="let productSpec of filteredProductSpecs; let i = index">
                      {{ productSpec.value }} {{ productSpec.unit }}
                      <p *ngIf="i !== filteredProductSpecs.length - 1">X</p>
                    </ng-container>
                  </ng-container>
                </ng-template>
              </div>
            </div>
          </div>

        </p-tabPanel>

        <!-- Tab Panel: Product Reviews -->
        <p-tabPanel header="{{'productDetails.details.review' | translate}}">
          <div class="details__tabView__review">
            <div class="d-flex flex-row">
              <div class="details__tabView__review__rating-card">
                <div class="details__tabView__review__rating-card__rating">
                  0.0
                </div>
                <!-- Star Rating -->
                <div class="details__tabView__review__rating_stars">
                  <p-rating
                    [(ngModel)]="rating"
                    [cancel]="false"
                    [readonly]="true"
                    [stars]="5"
                  ></p-rating>
                </div>
                <div
                  class="details__tabView__review__rating-card__rating_count"
                >
                  {{"productDetails.details.customerRating" | translate}}(0)
                </div>
              </div>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
  <p-dialog [(visible)]="displayShareModal" (onHide)="displayShareModal=false"
  [breakpoints]="{ '960px': '75vw', '640px': '90vw' }" [dismissableMask]="true" [draggable]="false" [showHeader]="false"
  [modal]="true" [resizable]="false" class="share-modal">
    <ng-template pTemplate="content">
       <div class="details__shareModal">
        <div class="details__shareModal__title">
        {{"productDetails.details.shareThisProduct" | translate}}


      </div>
    <div class="details__shareModal__sub-title">
        {{"productDetails.details.ProductShareWithYourFriend" | translate}}
        </div>
    <div class="d-inline-flex details__shareModal__share-icons justify-content-evenly">
        <share-button button="facebook" theme="circles-dark"  [url]="currentLink" class="sb-facebook-btn"></share-button>
        <share-button button="whatsapp" description=" " theme="circles-dark" [url]="currentLink" class="sb-whatsapp-btn"></share-button>
        <share-button button="twitter" description=" " theme="circles-dark" [url]="currentLink" class="sb-twitter-btn" ></share-button>
    </div>
    <div class="details__shareModal__copy-link">
    <div class="details__shareModal__copy-link__title">
      {{"productDetails.details.orCopyLink" | translate}}

    </div>
    <div class="details__shareModal__copy-link__copy">
      <div class="details__shareModal__copy-link__copy__text">
        {{currentLink}}
      </div>
      <button class="details__shareModal__copy-link__copy__copy-btn" (click)="copyLink(currentLink)">
        <img alt="No Image" src="assets/icons/copy-icon.svg">
      </button>

    </div>


    </div>
    </div>

    </ng-template>
  </p-dialog>
    </div>
    
  </div>

































<!-- this is the  product details -->
  <div *ngIf="!isLayoutTemplate" class="old-product-details">
    <div class="information p-4 shadow-1 mobile-information">
      <p class="product-name mb-0 mobile-display-none mb-2">
        {{ product?.name }}
      </p>
      <div class="flex flex-row align-items-center">
        <div
          [ngClass]="{
          soldOut: selectedVariant.soldOut,
          instock: selectedVariant.soldOut === false
        }"
          class="in-stock mobile-display-none"
        >
          <div
            [ngClass]="{
            redDot: selectedVariant.soldOut
          }"
            class="dot-6 bg-green-400 green-dot"
          ></div>
          {{ checkUsername() }}
        </div>
        <div class="rating mt-1 mx-2 mobile-display-none">
          <em class="star pi pi-star-fill"></em>
          <div class="rate mx-1">
            {{ selectedVariant && selectedVariant.rate ? selectedVariant.rate : 0 }}
          </div>
          <div class="rating-number">
            ({{ product.count ? product.count : 0 }})
          </div>
        </div>
      </div>

      <div class="mt-4 d-flex justify-content-between">
        <div *ngIf="selectedVariant?.salePrice && selectedVariant?.salePrice > 0">
          <p class="price m-0 font-size-16">
            <span class="now-currency">Now</span>
            <span class="tag-now">
              {{ product?.currencyCode }}
              {{disableCent === "false" ? ( selectedVariant.salePrice | number: "1." + decimalValue + "-" + decimalValue)
              :
              ( selectedVariant.salePrice | number) }}
            </span>
          </p>
          <p class="price m-0 font-size-16 was-currency">
            <span class="was-tag">Was</span>
            <span class="tag-was">
              {{ product.currencyCode }}
              {{disableCent === "false" ? (selectedVariant?.price | number: "1." + decimalValue + "-" + decimalValue)
              :
              (selectedVariant?.price | number) }}
            </span>
          </p>
        </div>
        <div
          *ngIf="!selectedVariant?.salePrice || selectedVariant?.salePrice === 0"
        >
          <p class="price m-0 font-size-16">
            <span class="tag-now">
              {{ product?.currencyCode }}
              {{disableCent === "false" ? (selectedVariant?.price | number: "1." + decimalValue + "-" + decimalValue)
            :
            (selectedVariant?.price | number) }}
            </span>
          </p>
        </div>
      </div>

      <div
        *ngIf="colors.length > 0"
        class="mt-3 flex flex-row justify-content-between"
      >
        <div class="product-size-box">
          <div class="row">
            <span class="span-text">Color</span>
          </div>

          <div class="d-flex">
            <div
              (click)="onColorChange(color)"
              *ngFor="let color of colors"
              class="color-circle"
            >
              <span
                *ngIf="color.value!=='multi-color'"
                [style.background-color]="color.value"
                class="black-circle"
                ><em
                  *ngIf="color.value === selectedColor"
                  class="fa-solid fa-check col-12 select-color"
                ></em
              ></span>
              <span *ngIf="color.value==='multi-color'" class="border-circle">
                Multi Color</span
              >
            </div>
          </div>

        </div>
      </div>

      <div
        *ngIf="sizes.length > 0"
        class="mt-2 flex flex-row justify-content-between"
      >
        <div>
          <div class="row padding-10-20">
            <div class="product-size-box">
              <div class="row">
                <ng-container *ngIf="!checkLabel('Size' , 0) else isSizeLabel">
                  <span class="span-text">Size:</span>
                </ng-container>
                <ng-template #isSizeLabel>
                  <div class="details__product-info__attributes__sizes__title">
                    <div
                      *ngFor="let size of varianceSpec  let parentIndex =  index"
                    >
                      <span class="span-text">{{size.label}} </span>
                      <ng-container *ngIf="parentIndex === 0 else secondLabel">
                        <p
                          (click)="onSizeChange(size, 0)"
                          *ngFor="let size of size1"
                          [ngClass]="{ 'selected-size': size.value === selectedSize }"
                          class="col-3 product-size"
                        >
                          {{ size.value }}
                        </p>
                      </ng-container>
                      <ng-template #secondLabel>
                        <p
                          (click)="onSizeChange(size, 1)"
                          *ngFor="let size of size2"
                          [ngClass]="{ 'selected-size': size.value === selectedSize2 }"
                          class="col-3 product-size"
                        >
                          {{ size.value }}
                        </p>
                      </ng-template>
                    </div>
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="flex flex-row align-items-center">
            <a
              (click)="showSizeModal()"
              *ngIf="sizeGuidImage"
              class="show-guide cursor-pointer"
              >Size Guide</a
            >
          </div>
        </div>
      </div>

      <div class="mt-2 flex flex-row justify-content-between col--12 pl-0">
        <button
          (click)="addItem(selectedVariant, product.shopId,product)"
          [disabled]="selectedVariant?.soldOut === true"
          [label]="'buttons.addToCart' | translate"
          class="mr-1 width-50 main-btn mobile-buy"
          pButton
          type="button"
        ></button>
        <button
          (click)="shopNow(selectedVariant, product.shopId,product)"
          [disabled]="selectedVariant?.soldOut === true"
          [label]="'buttons.shopNow' | translate"
          class="ml-1 width-50 second-btn"
          pButton
          type="button"
        ></button>

        <button
          (click)="
          addToWishlist(selectedVariant?.specProductId, selectedVariant?.isLiked,product)
        "
          class="ml-1 wishlist-btn cursor-pointer"
          type="button"
        >
          <em>
            <img
              *ngIf="!selectedVariant?.isLiked && isStoreCloud"
              alt="No Image"
              src="assets/icons/ionic-md-heart-empty-sc.svg"
            />
            <img
              *ngIf="!selectedVariant?.isLiked && !isStoreCloud"
              alt="No Image"
              src="assets/icons/ionic-md-heart-empty.svg"
            />
            <img
              *ngIf="selectedVariant?.isLiked && isStoreCloud"
              alt="No Image"
              src="assets/icons/fill-heart-sc.svg"
            />
            <img
              *ngIf="selectedVariant?.isLiked && !isStoreCloud"
              alt="No Image"
              src="assets/icons/fill-heart.svg"
            />
          </em>
        </button>
      </div>

      <div *ngIf="product.channelId === 1" class="mt-5">
        <p class="bold-font about-product">
          {{ "productDetails.details.aboutThisProduct" | translate }}
        </p>
        <form [formGroup]="productForm">
          <pre class="description">
          <textarea *ngIf="isDescription" [ngClass]="{'show-more': showMore}" [style.height.px]="textareaHeight"
                    class="mt-15 w-100"
                    class="pt-2 " formControlName="description"
                    placeholder="{{'productDetails.details.description' | translate}}"
                    readonly="true"></textarea>
        </pre>
        </form>
        <p
          (click)="readMore()"
          *ngIf="product && product.description && product.description.length > 40"
          class="main-color cursor-pointer medium-font font-size-14"
        >
          {{ (!showMore ? "buttons.readMore" : "buttons.readLess") | translate }}
        </p>
      </div>

      <div *ngIf="product.channelId === 2" class="mt-5">
        <p class="bold-font about-product">
          {{ "productDetails.details.aboutThisProduct" | translate }}
        </p>
        <p
          *ngIf="product && product.description && product.description.length > 0"
          [innerHTML]="product.description"
        ></p>
      </div>

      <div class="mt-5">
        <p class="bold-font about-product">
          {{ "productDetails.details.productDetails" | translate }}
        </p>
        <div class="flex flex-row justify-content-between">
          <p class="my-1 details-name">
            {{ "productDetails.details.merchantName" | translate }}
          </p>
          <p class="prop-color my-1 details-name">{{ product?.sellerName }}</p>
        </div>
        <div class="flex flex-row justify-content-between">
          <p class="my-1 details-name">
            {{ "productDetails.details.sku" | translate }}
          </p>
          <p class="prop-color my-1 details-name">{{ selectedVariant?.sku }}</p>
        </div>

        <div
          *ngIf="product?.specs?.Width?.value ||  selectedVariant?.specs?.Width?.value"
          class="flex flex-row justify-content-between"
        >
          <p class="my-1 details-name">
            {{ "productDetails.details.width" | translate }}
          </p>
          <p class="prop-color my-1 details-name">
            {{ product?.specs?.Width?.value ?? selectedVariant?.specs?.Width?.value }}
            {{ product?.specs?.Width?.unit ?? selectedVariant?.specs?.Width?.unit }}
          </p>
        </div>
        <div
          *ngIf="product?.specs?.Height?.value ||  selectedVariant?.specs?.Height?.value"
          class="flex flex-row justify-content-between"
        >
          <p class="my-1 details-name">
            {{ "productDetails.details.height" | translate }}
          </p>
          <p class="prop-color my-1 details-name">
            {{ product?.specs?.Height?.value ?? selectedVariant?.specs?.Height?.value }}
            {{ product?.specs?.Height?.unit ?? selectedVariant?.specs?.Height?.unit}}
          </p>
        </div>
        <div
          *ngIf="product?.specs?.Weight?.value || selectedVariant?.specs?.Weight?.value"
          class="flex flex-row justify-content-between"
        >
          <p class="my-1 details-name">
            {{ "productDetails.details.weight" | translate }}
          </p>
          <p class="prop-color my-1 details-name">
            {{ product?.specs?.Weight?.value ?? selectedVariant?.specs?.Weight?.value }}
            {{ product?.specs?.Weight?.unit ?? selectedVariant?.specs?.Weight?.unit}}
          </p>
        </div>
        <div
          *ngIf="product?.specs?.Length?.value ||  selectedVariant?.specs?.Length?.value"
          class="flex flex-row justify-content-between"
        >
          <p class="my-1 details-name">
            {{ "productDetails.details.length" | translate }}
          </p>
          <p class="prop-color my-1 details-name">
            {{ product?.specs?.Length?.value ?? selectedVariant?.specs?.Length?.value }}
            {{ product?.specs?.Length?.unit ?? selectedVariant?.specs?.Length?.unit}}
          </p>
        </div>
        <div
          *ngIf="product?.specs?.Gender?.value ||  selectedVariant?.specs?.Gender?.value"
          class="flex flex-row justify-content-between"
        >
          <p class="my-1 details-name">
            {{ "productDetails.details.gender" | translate }}
          </p>
          <p class="prop-color my-1 details-name">
            {{ genderSpeces[product?.specs?.Gender?.value] ?? genderSpeces[selectedVariant?.specs?.Gender?.value] }}
          </p>
        </div>
      </div>

      <div class="mt-5">
        <p class="about-product">
          {{ "productDetails.details.customerReviews" | translate }}
        </p>

        <div class="flex flex-row justify-content-between rating-wrapper">
          <div
            class="flex flex-column align-items-center justify-content-center width-45 overall-rating star-rating"
          >
            <p class="bold-font overall-rate">
              {{ "productDetails.details.overallRating" | translate }}
            </p>
            <div class="medium-font font-size-28 fourth-color">
              {{ rating === null ? 0 : rating }}
            </div>
            <p-rating
              [(ngModel)]="rating"
              [cancel]="false"
              [readonly]="true"
              [stars]="5"
            ></p-rating>
            <p class="prop-color">
              {{ "productDetails.details.basedOn" | translate }}
              {{ rateCount === null ? 0 : rateCount }}
              {{ "productDetails.details.ratings" | translate }}
            </p>
          </div>
          <div
            class="flex flex-column align-items-center justify-content-center width-45 vertical-rating"
          >
            <div class="flex flex-row align-items-center w-full">
              <div class="font-medium font-size-13 review-number">5</div>
              <em class="pi pi-star-fill rating-color"></em>
              <div class="width-75 mx-1">
                <p-progressBar
                  [showValue]="false"
                  [value]="rate5"
                  class="w-full"
                ></p-progressBar>
              </div>
              <div class="font-light font-size-13 prop-color">
                ({{ rate5 === null ? 0 : rate5 }})
              </div>
            </div>
            <div class="flex flex-row align-items-center w-full my-1">
              <div class="font-medium font-size-13 review-number">4</div>
              <em class="pi pi-star-fill rating-color"></em>
              <div class="width-75 mx-1">
                <p-progressBar
                  [showValue]="false"
                  [value]="rate4"
                  class="w-full"
                ></p-progressBar>
              </div>
              <div class="font-light font-size-13 prop-color">
                ({{ rate4 === null ? 0 : rate4 }})
              </div>
            </div>
            <div class="flex flex-row align-items-center w-full my-1">
              <div class="font-medium font-size-13 review-number">3</div>
              <em class="pi pi-star-fill rating-color"></em>
              <div class="width-75 mx-1">
                <p-progressBar
                  [showValue]="false"
                  [value]="rate3"
                  class="w-full"
                ></p-progressBar>
              </div>
              <div class="font-light font-size-13 prop-color">
                ({{ rate3 === null ? 0 : rate3 }})
              </div>
            </div>
            <div class="flex flex-row align-items-center w-full my-1">
              <div class="font-medium font-size-13 review-number">2</div>
              <em class="pi pi-star-fill rating-color"></em>
              <div class="width-75 mx-1">
                <p-progressBar
                  [showValue]="false"
                  [value]="rate2"
                  class="w-full"
                ></p-progressBar>
              </div>
              <div class="font-light font-size-13 prop-color">
                ({{ rate2 === null ? 0 : rate2 }})
              </div>
            </div>
            <div class="flex flex-row align-items-center w-full my-1">
              <div class="font-medium font-size-13 review-number">1</div>
              <em class="pi pi-star-fill rating-color"></em>
              <div class="width-75 mx-1">
                <p-progressBar
                  [showValue]="false"
                  [value]="rate1"
                  class="w-full"
                ></p-progressBar>
              </div>
              <div class="font-light font-size-13 prop-color">
                ({{ rate1 === null ? 0 : rate1 }})
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="reviews?.length !== 0"
        [ngClass]="{ 'user-review-scroll': reviewsLenght > 2 ? true : false }"
        class="mt-5"
      >
        <ng-container *ngFor="let review of reviews">
          <div *ngIf="review?.rate" class="users-reviews">
            <p-divider></p-divider>
            <div class="flex flex-row align-items-center">
              <div class="medium-font font-size-13">
                {{ review?.customerName }}
              </div>
              <em class="pi pi-check-circle mx-2 main-color font-size-12"></em>
              <div class="light-font font-size-12 prop-color">
                {{ "productDetails.details.verifiedPurchase" | translate }}
              </div>
            </div>
            <p-rating
              [(ngModel)]="review.rate"
              [cancel]="false"
              [readonly]="true"
              [stars]="5"
            ></p-rating>
            <div class="light-font font-size-12 prop-color">
              {{ review?.creationOn | date : "dd/MM/yyyy" }}
            </div>
            <p class="light-font font-size-14 mt-1 one-line">
              {{ review?.describtion }}
            </p>
            <p-divider></p-divider>
          </div>
        </ng-container>
      </div>
    </div>
    <ng-container *ngIf="displayModal">
      <app-mtn-size-guide-modal
        (cancel)="onSubmit($event)"
        (submit)="onSubmit($event)"
        [displayModal]="displayModal"
        [sizeGuidImage]="sizeGuidImage"
      ></app-mtn-size-guide-modal>
    </ng-container>
  </div>












  <ng-container *ngIf="displayModal">
    <app-mtn-size-guide-modal
      (cancel)="onSubmit($event)"
      (submit)="onSubmit($event)"
      [displayModal]="displayModal"
      [sizeGuidImage]="sizeGuidImage"
    ></app-mtn-size-guide-modal>
  </ng-container>

  <app-success-info-modal
    (cancel)="onCancel()"
    [bodyMessage]="successBodyMessage"
    [displayModal]="displaySuccessModal"
    [titleMessage]="successTitleMessage"
  ></app-success-info-modal>
  <app-notify-modal
    [isEmailExist]="isEmailExist"
    (close)="displayNotifyModal = false"
    (submit)="onSubmitNotify($event)"
    [displayModal]="displayNotifyModal"
  ></app-notify-modal>

  <app-age-consent-modal
    [age]="restrictionAge"
    [displayModal]="displayAgeConsentModal"
    (submit)="onSubmitConsent()"
    (cancel)="closeConsentModal()"
  ></app-age-consent-modal>
  <app-ineligable-purchase-modal
    [displayModal]="displayEligableModal"
    (cancel)="closeEligableModal()"
  ></app-ineligable-purchase-modal>
 