{"ast": null, "code": "import { IndexComponent } from \"./components/index/index.component\";\nexport const routes = [{\n  path: '',\n  component: IndexComponent\n}];", "map": {"version": 3, "names": ["IndexComponent", "routes", "path", "component"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\routes.ts"], "sourcesContent": ["import {Routes} from \"@angular/router\";\r\nimport {IndexComponent} from \"./components/index/index.component\";\r\n\r\nexport const routes: Routes = [\r\n  { path: '', component: IndexComponent },\r\n];\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,oCAAoC;AAEjE,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAc,CAAE,CACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}