{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * Represents a big integer using a buffer of its individual digits, with the least significant\n * digit stored at the beginning of the array (little endian).\n *\n * For performance reasons, each instance is mutable. The addition operation can be done in-place\n * to reduce memory pressure of allocation for the digits array.\n */\nclass BigInteger {\n  static zero() {\n    return new BigInteger([0]);\n  }\n  static one() {\n    return new BigInteger([1]);\n  }\n  /**\n   * Creates a big integer using its individual digits in little endian storage.\n   */\n  constructor(digits) {\n    this.digits = digits;\n  }\n  /**\n   * Creates a clone of this instance.\n   */\n  clone() {\n    return new BigInteger(this.digits.slice());\n  }\n  /**\n   * Returns a new big integer with the sum of `this` and `other` as its value. This does not mutate\n   * `this` but instead returns a new instance, unlike `addToSelf`.\n   */\n  add(other) {\n    const result = this.clone();\n    result.addToSelf(other);\n    return result;\n  }\n  /**\n   * Adds `other` to the instance itself, thereby mutating its value.\n   */\n  addToSelf(other) {\n    const maxNrOfDigits = Math.max(this.digits.length, other.digits.length);\n    let carry = 0;\n    for (let i = 0; i < maxNrOfDigits; i++) {\n      let digitSum = carry;\n      if (i < this.digits.length) {\n        digitSum += this.digits[i];\n      }\n      if (i < other.digits.length) {\n        digitSum += other.digits[i];\n      }\n      if (digitSum >= 10) {\n        this.digits[i] = digitSum - 10;\n        carry = 1;\n      } else {\n        this.digits[i] = digitSum;\n        carry = 0;\n      }\n    }\n    // Apply a remaining carry if needed.\n    if (carry > 0) {\n      this.digits[maxNrOfDigits] = 1;\n    }\n  }\n  /**\n   * Builds the decimal string representation of the big integer. As this is stored in\n   * little endian, the digits are concatenated in reverse order.\n   */\n  toString() {\n    let res = '';\n    for (let i = this.digits.length - 1; i >= 0; i--) {\n      res += this.digits[i];\n    }\n    return res;\n  }\n}\n/**\n * Represents a big integer which is optimized for multiplication operations, as its power-of-twos\n * are memoized. See `multiplyBy()` for details on the multiplication algorithm.\n */\nclass BigIntForMultiplication {\n  constructor(value) {\n    this.powerOfTwos = [value];\n  }\n  /**\n   * Returns the big integer itself.\n   */\n  getValue() {\n    return this.powerOfTwos[0];\n  }\n  /**\n   * Computes the value for `num * b`, where `num` is a JS number and `b` is a big integer. The\n   * value for `b` is represented by a storage model that is optimized for this computation.\n   *\n   * This operation is implemented in N(log2(num)) by continuous halving of the number, where the\n   * least-significant bit (LSB) is tested in each iteration. If the bit is set, the bit's index is\n   * used as exponent into the power-of-two multiplication of `b`.\n   *\n   * As an example, consider the multiplication num=42, b=1337. In binary 42 is 0b00101010 and the\n   * algorithm unrolls into the following iterations:\n   *\n   *  Iteration | num        | LSB  | b * 2^iter | Add? | product\n   * -----------|------------|------|------------|------|--------\n   *  0         | 0b00101010 | 0    | 1337       | No   | 0\n   *  1         | 0b00010101 | 1    | 2674       | Yes  | 2674\n   *  2         | 0b00001010 | 0    | 5348       | No   | 2674\n   *  3         | 0b00000101 | 1    | 10696      | Yes  | 13370\n   *  4         | 0b00000010 | 0    | 21392      | No   | 13370\n   *  5         | 0b00000001 | 1    | 42784      | Yes  | 56154\n   *  6         | 0b00000000 | 0    | 85568      | No   | 56154\n   *\n   * The computed product of 56154 is indeed the correct result.\n   *\n   * The `BigIntForMultiplication` representation for a big integer provides memoized access to the\n   * power-of-two values to reduce the workload in computing those values.\n   */\n  multiplyBy(num) {\n    const product = BigInteger.zero();\n    this.multiplyByAndAddTo(num, product);\n    return product;\n  }\n  /**\n   * See `multiplyBy()` for details. This function allows for the computed product to be added\n   * directly to the provided result big integer.\n   */\n  multiplyByAndAddTo(num, result) {\n    for (let exponent = 0; num !== 0; num = num >>> 1, exponent++) {\n      if (num & 1) {\n        const value = this.getMultipliedByPowerOfTwo(exponent);\n        result.addToSelf(value);\n      }\n    }\n  }\n  /**\n   * Computes and memoizes the big integer value for `this.number * 2^exponent`.\n   */\n  getMultipliedByPowerOfTwo(exponent) {\n    // Compute the powers up until the requested exponent, where each value is computed from its\n    // predecessor. This is simple as `this.number * 2^(exponent - 1)` only has to be doubled (i.e.\n    // added to itself) to reach `this.number * 2^exponent`.\n    for (let i = this.powerOfTwos.length; i <= exponent; i++) {\n      const previousPower = this.powerOfTwos[i - 1];\n      this.powerOfTwos[i] = previousPower.add(previousPower);\n    }\n    return this.powerOfTwos[exponent];\n  }\n}\n/**\n * Represents an exponentiation operation for the provided base, of which exponents are computed and\n * memoized. The results are represented by a `BigIntForMultiplication` which is tailored for\n * multiplication operations by memoizing the power-of-twos. This effectively results in a matrix\n * representation that is lazily computed upon request.\n */\nclass BigIntExponentiation {\n  constructor(base) {\n    this.base = base;\n    this.exponents = [new BigIntForMultiplication(BigInteger.one())];\n  }\n  /**\n   * Compute the value for `this.base^exponent`, resulting in a big integer that is optimized for\n   * further multiplication operations.\n   */\n  toThePowerOf(exponent) {\n    // Compute the results up until the requested exponent, where every value is computed from its\n    // predecessor. This is because `this.base^(exponent - 1)` only has to be multiplied by `base`\n    // to reach `this.base^exponent`.\n    for (let i = this.exponents.length; i <= exponent; i++) {\n      const value = this.exponents[i - 1].multiplyBy(this.base);\n      this.exponents[i] = new BigIntForMultiplication(value);\n    }\n    return this.exponents[exponent];\n  }\n}\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nfunction digest(message) {\n  return message.id || computeDigest(message);\n}\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nfunction computeDigest(message) {\n  return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nfunction decimalDigest(message) {\n  return message.id || computeDecimalDigest(message);\n}\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nfunction computeDecimalDigest(message) {\n  const visitor = new _SerializerIgnoreIcuExpVisitor();\n  const parts = message.nodes.map(a => a.visit(visitor, null));\n  return computeMsgId(parts.join(''), message.meaning);\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor {\n  visitText(text, context) {\n    return text.value;\n  }\n  visitContainer(container, context) {\n    return `[${container.children.map(child => child.visit(this)).join(', ')}]`;\n  }\n  visitIcu(icu, context) {\n    const strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n  }\n  visitTagPlaceholder(ph, context) {\n    return ph.isVoid ? `<ph tag name=\"${ph.startName}\"/>` : `<ph tag name=\"${ph.startName}\">${ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n  visitPlaceholder(ph, context) {\n    return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n  }\n  visitIcuPlaceholder(ph, context) {\n    return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n  }\n}\nconst serializerVisitor = new _SerializerVisitor();\nfunction serializeNodes(nodes) {\n  return nodes.map(a => a.visit(serializerVisitor, null));\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {\n  visitIcu(icu, context) {\n    let strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    // Do not take the expression into account\n    return `{${icu.type}, ${strCases.join(', ')}}`;\n  }\n}\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nfunction sha1(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = [...textEncoder.encode(str)];\n  const words32 = bytesToWords32(utf8, Endian.Big);\n  const len = utf8.length * 8;\n  const w = new Uint32Array(80);\n  let a = 0x67452301,\n    b = 0xefcdab89,\n    c = 0x98badcfe,\n    d = 0x10325476,\n    e = 0xc3d2e1f0;\n  words32[len >> 5] |= 0x80 << 24 - len % 32;\n  words32[(len + 64 >> 9 << 4) + 15] = len;\n  for (let i = 0; i < words32.length; i += 16) {\n    const h0 = a,\n      h1 = b,\n      h2 = c,\n      h3 = d,\n      h4 = e;\n    for (let j = 0; j < 80; j++) {\n      if (j < 16) {\n        w[j] = words32[i + j];\n      } else {\n        w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      }\n      const fkVal = fk(j, b, c, d);\n      const f = fkVal[0];\n      const k = fkVal[1];\n      const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n      e = d;\n      d = c;\n      c = rol32(b, 30);\n      b = a;\n      a = temp;\n    }\n    a = add32(a, h0);\n    b = add32(b, h1);\n    c = add32(c, h2);\n    d = add32(d, h3);\n    e = add32(e, h4);\n  }\n  // Convert the output parts to a 160-bit hexadecimal string\n  return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value) {\n  // unsigned right shift of zero ensures an unsigned 32-bit number\n  return (value >>> 0).toString(16).padStart(8, '0');\n}\nfunction fk(index, b, c, d) {\n  if (index < 20) {\n    return [b & c | ~b & d, 0x5a827999];\n  }\n  if (index < 40) {\n    return [b ^ c ^ d, 0x6ed9eba1];\n  }\n  if (index < 60) {\n    return [b & c | b & d | c & d, 0x8f1bbcdc];\n  }\n  return [b ^ c ^ d, 0xca62c1d6];\n}\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -0x6b5f56d8;\n  }\n  return [hi, lo];\n}\nfunction computeMsgId(msg, meaning = '') {\n  let msgFingerprint = fingerprint(msg);\n  if (meaning) {\n    const meaningFingerprint = fingerprint(meaning);\n    msgFingerprint = add64(rol64(msgFingerprint, 1), meaningFingerprint);\n  }\n  const hi = msgFingerprint[0];\n  const lo = msgFingerprint[1];\n  return wordsToDecimalString(hi & 0x7fffffff, lo);\n}\nfunction hash32(view, length, c) {\n  let a = 0x9e3779b9,\n    b = 0x9e3779b9;\n  let index = 0;\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    a = res[0], b = res[1], c = res[2];\n  }\n  const remainder = length - index;\n  // the first byte of c is reserved for the length\n  c += length;\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n  return mix(a, b, c)[2];\n}\n// clang-format off\nfunction mix(a, b, c) {\n  a -= b;\n  a -= c;\n  a ^= c >>> 13;\n  b -= c;\n  b -= a;\n  b ^= a << 8;\n  c -= a;\n  c -= b;\n  c ^= b >>> 13;\n  a -= b;\n  a -= c;\n  a ^= c >>> 12;\n  b -= c;\n  b -= a;\n  b ^= a << 16;\n  c -= a;\n  c -= b;\n  c ^= b >>> 5;\n  a -= b;\n  a -= c;\n  a ^= c >>> 3;\n  b -= c;\n  b -= a;\n  b ^= a << 10;\n  c -= a;\n  c -= b;\n  c ^= b >>> 15;\n  return [a, b, c];\n}\n// clang-format on\n// Utils\nvar Endian;\n(function (Endian) {\n  Endian[Endian[\"Little\"] = 0] = \"Little\";\n  Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\nfunction add32(a, b) {\n  return add32to64(a, b)[1];\n}\nfunction add32to64(a, b) {\n  const low = (a & 0xffff) + (b & 0xffff);\n  const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n  return [high >>> 16, high << 16 | low & 0xffff];\n}\nfunction add64(a, b) {\n  const ah = a[0],\n    al = a[1];\n  const bh = b[0],\n    bl = b[1];\n  const result = add32to64(al, bl);\n  const carry = result[0];\n  const l = result[1];\n  const h = add32(add32(ah, bh), carry);\n  return [h, l];\n}\n// Rotate a 32b number left `count` position\nfunction rol32(a, count) {\n  return a << count | a >>> 32 - count;\n}\n// Rotate a 64b number left `count` position\nfunction rol64(num, count) {\n  const hi = num[0],\n    lo = num[1];\n  const h = hi << count | lo >>> 32 - count;\n  const l = lo << count | hi >>> 32 - count;\n  return [h, l];\n}\nfunction bytesToWords32(bytes, endian) {\n  const size = bytes.length + 3 >>> 2;\n  const words32 = [];\n  for (let i = 0; i < size; i++) {\n    words32[i] = wordAt(bytes, i * 4, endian);\n  }\n  return words32;\n}\nfunction byteAt(bytes, index) {\n  return index >= bytes.length ? 0 : bytes[index];\n}\nfunction wordAt(bytes, index, endian) {\n  let word = 0;\n  if (endian === Endian.Big) {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 24 - 8 * i;\n    }\n  } else {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 8 * i;\n    }\n  }\n  return word;\n}\n/**\n * Create a shared exponentiation pool for base-256 computations. This shared pool provides memoized\n * power-of-256 results with memoized power-of-two computations for efficient multiplication.\n *\n * For our purposes, this can be safely stored as a global without memory concerns. The reason is\n * that we encode two words, so only need the 0th (for the low word) and 4th (for the high word)\n * exponent.\n */\nconst base256 = new BigIntExponentiation(256);\n/**\n * Represents two 32-bit words as a single decimal number. This requires a big integer storage\n * model as JS numbers are not accurate enough to represent the 64-bit number.\n *\n * Based on https://www.danvk.org/hex2dec.html\n */\nfunction wordsToDecimalString(hi, lo) {\n  // Encode the four bytes in lo in the lower digits of the decimal number.\n  // Note: the multiplication results in lo itself but represented by a big integer using its\n  // decimal digits.\n  const decimal = base256.toThePowerOf(0).multiplyBy(lo);\n  // Encode the four bytes in hi above the four lo bytes. lo is a maximum of (2^8)^4, which is why\n  // this multiplication factor is applied.\n  base256.toThePowerOf(4).multiplyByAndAddTo(hi, decimal);\n  return decimal.toString();\n}\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n  const substitutions = {};\n  const substitutionLocations = {};\n  const associatedMessageIds = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts = [metadata.text];\n  const placeholderNames = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location\n  };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n  const {\n    text: messageString,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      text: messageString\n    };\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {\n      text: messageString,\n      meaning,\n      description,\n      customId,\n      legacyIds\n    };\n  }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n  const {\n    text: messagePart,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      messagePart\n    };\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {\n      messagePart,\n      placeholderName,\n      associatedMessageId\n    };\n  }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n  if (raw.charAt(0) !== BLOCK_MARKER$1) {\n    return {\n      text: cooked\n    };\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1)\n    };\n  }\n}\nfunction computePlaceholderName(index) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\nclass MissingTranslationError extends Error {\n  constructor(parsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n    this.parsedMessage = parsedMessage;\n    this.type = 'MissingTranslationError';\n  }\n}\nfunction isMissingTranslationError(e) {\n  return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n  const message = parseMessage(messageParts, substitutions);\n  // Look up the translation using the messageId, and then the legacyId if available.\n  let translation = translations[message.id];\n  // If the messageId did not match a translation, try matching the legacy ids instead\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n  return [translation.messageParts, translation.placeholderNames.map(placeholder => {\n    if (message.substitutions.hasOwnProperty(placeholder)) {\n      return message.substitutions[placeholder];\n    } else {\n      throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` + `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n    }\n  })];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames = [];\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n  const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames\n  };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n  let messageString = messageParts[0];\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames\n  };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n  Object.defineProperty(cooked, 'raw', {\n    value: raw\n  });\n  return cooked;\n}\nfunction describeMessage(message) {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy = message.legacyIds && message.legacyIds.length > 0 ? ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` : '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n  Object.keys(translations).forEach(key => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n  try {\n    return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn(e.message);\n    return [messageParts, substitutions];\n  }\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n  if ($localize$1.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize$1.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) : messagePart;\n}\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n\n// This file contains the public API of the `@angular/localize` entry-point\n\n// DO NOT ADD public exports to this file.\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };", "map": {"version": 3, "names": ["BLOCK_MARKER$1", "MEANING_SEPARATOR", "ID_SEPARATOR", "LEGACY_ID_INDICATOR", "BigInteger", "zero", "one", "constructor", "digits", "clone", "slice", "add", "other", "result", "addToSelf", "maxNrOfDigits", "Math", "max", "length", "carry", "i", "digitSum", "toString", "res", "BigIntForMultiplication", "value", "powerOfTwos", "getValue", "multiplyBy", "num", "product", "multiplyByAndAddTo", "exponent", "getMultipliedByPowerOfTwo", "previousPower", "BigIntExponentiation", "base", "exponents", "toThePowerOf", "textEncoder", "digest", "message", "id", "computeDigest", "sha1", "serializeNodes", "nodes", "join", "meaning", "decimalDigest", "computeDecimalDigest", "visitor", "_SerializerIgnoreIcuExpVisitor", "parts", "map", "a", "visit", "computeMsgId", "_SerializerVisitor", "visitText", "text", "context", "visitContainer", "container", "children", "child", "visitIcu", "icu", "strCases", "Object", "keys", "cases", "k", "expression", "type", "visitTagPlaceholder", "ph", "isVoid", "startName", "closeName", "visitPlaceholder", "name", "visitIcuPlaceholder", "serializerVisitor", "str", "TextEncoder", "utf8", "encode", "words32", "bytesToWords32", "<PERSON><PERSON>", "Big", "len", "w", "Uint32Array", "b", "c", "d", "e", "h0", "h1", "h2", "h3", "h4", "j", "rol32", "fkVal", "fk", "f", "temp", "reduce", "add32", "toHexU32", "padStart", "index", "fingerprint", "view", "DataView", "buffer", "byteOffset", "byteLength", "hi", "hash32", "lo", "msg", "msgFingerprint", "meaningFingerprint", "add64", "rol64", "wordsToDecimalString", "end", "getUint32", "mix", "remainder", "getUint8", "add32to64", "low", "high", "ah", "al", "bh", "bl", "l", "h", "count", "bytes", "endian", "size", "wordAt", "byteAt", "word", "base256", "decimal", "parseMessage", "messageParts", "expressions", "location", "messagePartLocations", "expressionLocations", "substitutions", "substitutionLocations", "associatedMessageIds", "metadata", "parseMetadata", "raw", "cleanedMessageParts", "placeholder<PERSON><PERSON><PERSON>", "messageString", "messagePart", "placeholder<PERSON><PERSON>", "computePlaceholderName", "associatedMessageId", "parsePlaceholder", "undefined", "push", "messageId", "customId", "legacyIds", "filter", "description", "cooked", "block", "splitBlock", "meaningDescAndId", "split", "meaningAndDesc", "char<PERSON>t", "endOfBlock", "findEndOfBlock", "substring", "cookedIndex", "rawIndex", "Error", "MissingTranslationError", "parsedMessage", "describeMessage", "isMissingTranslationError", "translate$1", "translations", "translation", "placeholder", "hasOwnProperty", "parseTranslation", "rawMessageParts", "part", "makeTemplateObject", "makeParsedTranslation", "defineProperty", "meaningString", "legacy", "loadTranslations", "$localize", "translate", "TRANSLATIONS", "for<PERSON>ach", "key", "clearTranslations", "console", "warn", "$localize$1", "stripBlock", "BLOCK_MARKER", "rawMessagePart", "ɵ$localize", "ɵMissingTranslationError", "ɵcomputeMsgId", "ɵfindEndOfBlock", "ɵisMissingTranslationError", "ɵmakeParsedTranslation", "ɵmakeTemplateObject", "ɵparseMessage", "ɵparseMetadata", "ɵparseTranslation", "ɵsplitBlock", "ɵtranslate"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@angular/localize/fesm2022/localize.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * Represents a big integer using a buffer of its individual digits, with the least significant\n * digit stored at the beginning of the array (little endian).\n *\n * For performance reasons, each instance is mutable. The addition operation can be done in-place\n * to reduce memory pressure of allocation for the digits array.\n */\nclass BigInteger {\n    static zero() {\n        return new BigInteger([0]);\n    }\n    static one() {\n        return new BigInteger([1]);\n    }\n    /**\n     * Creates a big integer using its individual digits in little endian storage.\n     */\n    constructor(digits) {\n        this.digits = digits;\n    }\n    /**\n     * Creates a clone of this instance.\n     */\n    clone() {\n        return new BigInteger(this.digits.slice());\n    }\n    /**\n     * Returns a new big integer with the sum of `this` and `other` as its value. This does not mutate\n     * `this` but instead returns a new instance, unlike `addToSelf`.\n     */\n    add(other) {\n        const result = this.clone();\n        result.addToSelf(other);\n        return result;\n    }\n    /**\n     * Adds `other` to the instance itself, thereby mutating its value.\n     */\n    addToSelf(other) {\n        const maxNrOfDigits = Math.max(this.digits.length, other.digits.length);\n        let carry = 0;\n        for (let i = 0; i < maxNrOfDigits; i++) {\n            let digitSum = carry;\n            if (i < this.digits.length) {\n                digitSum += this.digits[i];\n            }\n            if (i < other.digits.length) {\n                digitSum += other.digits[i];\n            }\n            if (digitSum >= 10) {\n                this.digits[i] = digitSum - 10;\n                carry = 1;\n            }\n            else {\n                this.digits[i] = digitSum;\n                carry = 0;\n            }\n        }\n        // Apply a remaining carry if needed.\n        if (carry > 0) {\n            this.digits[maxNrOfDigits] = 1;\n        }\n    }\n    /**\n     * Builds the decimal string representation of the big integer. As this is stored in\n     * little endian, the digits are concatenated in reverse order.\n     */\n    toString() {\n        let res = '';\n        for (let i = this.digits.length - 1; i >= 0; i--) {\n            res += this.digits[i];\n        }\n        return res;\n    }\n}\n/**\n * Represents a big integer which is optimized for multiplication operations, as its power-of-twos\n * are memoized. See `multiplyBy()` for details on the multiplication algorithm.\n */\nclass BigIntForMultiplication {\n    constructor(value) {\n        this.powerOfTwos = [value];\n    }\n    /**\n     * Returns the big integer itself.\n     */\n    getValue() {\n        return this.powerOfTwos[0];\n    }\n    /**\n     * Computes the value for `num * b`, where `num` is a JS number and `b` is a big integer. The\n     * value for `b` is represented by a storage model that is optimized for this computation.\n     *\n     * This operation is implemented in N(log2(num)) by continuous halving of the number, where the\n     * least-significant bit (LSB) is tested in each iteration. If the bit is set, the bit's index is\n     * used as exponent into the power-of-two multiplication of `b`.\n     *\n     * As an example, consider the multiplication num=42, b=1337. In binary 42 is 0b00101010 and the\n     * algorithm unrolls into the following iterations:\n     *\n     *  Iteration | num        | LSB  | b * 2^iter | Add? | product\n     * -----------|------------|------|------------|------|--------\n     *  0         | 0b00101010 | 0    | 1337       | No   | 0\n     *  1         | 0b00010101 | 1    | 2674       | Yes  | 2674\n     *  2         | 0b00001010 | 0    | 5348       | No   | 2674\n     *  3         | 0b00000101 | 1    | 10696      | Yes  | 13370\n     *  4         | 0b00000010 | 0    | 21392      | No   | 13370\n     *  5         | 0b00000001 | 1    | 42784      | Yes  | 56154\n     *  6         | 0b00000000 | 0    | 85568      | No   | 56154\n     *\n     * The computed product of 56154 is indeed the correct result.\n     *\n     * The `BigIntForMultiplication` representation for a big integer provides memoized access to the\n     * power-of-two values to reduce the workload in computing those values.\n     */\n    multiplyBy(num) {\n        const product = BigInteger.zero();\n        this.multiplyByAndAddTo(num, product);\n        return product;\n    }\n    /**\n     * See `multiplyBy()` for details. This function allows for the computed product to be added\n     * directly to the provided result big integer.\n     */\n    multiplyByAndAddTo(num, result) {\n        for (let exponent = 0; num !== 0; num = num >>> 1, exponent++) {\n            if (num & 1) {\n                const value = this.getMultipliedByPowerOfTwo(exponent);\n                result.addToSelf(value);\n            }\n        }\n    }\n    /**\n     * Computes and memoizes the big integer value for `this.number * 2^exponent`.\n     */\n    getMultipliedByPowerOfTwo(exponent) {\n        // Compute the powers up until the requested exponent, where each value is computed from its\n        // predecessor. This is simple as `this.number * 2^(exponent - 1)` only has to be doubled (i.e.\n        // added to itself) to reach `this.number * 2^exponent`.\n        for (let i = this.powerOfTwos.length; i <= exponent; i++) {\n            const previousPower = this.powerOfTwos[i - 1];\n            this.powerOfTwos[i] = previousPower.add(previousPower);\n        }\n        return this.powerOfTwos[exponent];\n    }\n}\n/**\n * Represents an exponentiation operation for the provided base, of which exponents are computed and\n * memoized. The results are represented by a `BigIntForMultiplication` which is tailored for\n * multiplication operations by memoizing the power-of-twos. This effectively results in a matrix\n * representation that is lazily computed upon request.\n */\nclass BigIntExponentiation {\n    constructor(base) {\n        this.base = base;\n        this.exponents = [new BigIntForMultiplication(BigInteger.one())];\n    }\n    /**\n     * Compute the value for `this.base^exponent`, resulting in a big integer that is optimized for\n     * further multiplication operations.\n     */\n    toThePowerOf(exponent) {\n        // Compute the results up until the requested exponent, where every value is computed from its\n        // predecessor. This is because `this.base^(exponent - 1)` only has to be multiplied by `base`\n        // to reach `this.base^exponent`.\n        for (let i = this.exponents.length; i <= exponent; i++) {\n            const value = this.exponents[i - 1].multiplyBy(this.base);\n            this.exponents[i] = new BigIntForMultiplication(value);\n        }\n        return this.exponents[exponent];\n    }\n}\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nfunction digest(message) {\n    return message.id || computeDigest(message);\n}\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nfunction computeDigest(message) {\n    return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nfunction decimalDigest(message) {\n    return message.id || computeDecimalDigest(message);\n}\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nfunction computeDecimalDigest(message) {\n    const visitor = new _SerializerIgnoreIcuExpVisitor();\n    const parts = message.nodes.map(a => a.visit(visitor, null));\n    return computeMsgId(parts.join(''), message.meaning);\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor {\n    visitText(text, context) {\n        return text.value;\n    }\n    visitContainer(container, context) {\n        return `[${container.children.map(child => child.visit(this)).join(', ')}]`;\n    }\n    visitIcu(icu, context) {\n        const strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);\n        return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n    }\n    visitTagPlaceholder(ph, context) {\n        return ph.isVoid ?\n            `<ph tag name=\"${ph.startName}\"/>` :\n            `<ph tag name=\"${ph.startName}\">${ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n    }\n    visitPlaceholder(ph, context) {\n        return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n    }\n    visitIcuPlaceholder(ph, context) {\n        return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n    }\n}\nconst serializerVisitor = new _SerializerVisitor();\nfunction serializeNodes(nodes) {\n    return nodes.map(a => a.visit(serializerVisitor, null));\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {\n    visitIcu(icu, context) {\n        let strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);\n        // Do not take the expression into account\n        return `{${icu.type}, ${strCases.join(', ')}}`;\n    }\n}\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nfunction sha1(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = [...textEncoder.encode(str)];\n    const words32 = bytesToWords32(utf8, Endian.Big);\n    const len = utf8.length * 8;\n    const w = new Uint32Array(80);\n    let a = 0x67452301, b = 0xefcdab89, c = 0x98badcfe, d = 0x10325476, e = 0xc3d2e1f0;\n    words32[len >> 5] |= 0x80 << (24 - len % 32);\n    words32[((len + 64 >> 9) << 4) + 15] = len;\n    for (let i = 0; i < words32.length; i += 16) {\n        const h0 = a, h1 = b, h2 = c, h3 = d, h4 = e;\n        for (let j = 0; j < 80; j++) {\n            if (j < 16) {\n                w[j] = words32[i + j];\n            }\n            else {\n                w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n            }\n            const fkVal = fk(j, b, c, d);\n            const f = fkVal[0];\n            const k = fkVal[1];\n            const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n            e = d;\n            d = c;\n            c = rol32(b, 30);\n            b = a;\n            a = temp;\n        }\n        a = add32(a, h0);\n        b = add32(b, h1);\n        c = add32(c, h2);\n        d = add32(d, h3);\n        e = add32(e, h4);\n    }\n    // Convert the output parts to a 160-bit hexadecimal string\n    return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value) {\n    // unsigned right shift of zero ensures an unsigned 32-bit number\n    return (value >>> 0).toString(16).padStart(8, '0');\n}\nfunction fk(index, b, c, d) {\n    if (index < 20) {\n        return [(b & c) | (~b & d), 0x5a827999];\n    }\n    if (index < 40) {\n        return [b ^ c ^ d, 0x6ed9eba1];\n    }\n    if (index < 60) {\n        return [(b & c) | (b & d) | (c & d), 0x8f1bbcdc];\n    }\n    return [b ^ c ^ d, 0xca62c1d6];\n}\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = textEncoder.encode(str);\n    const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n    let hi = hash32(view, utf8.length, 0);\n    let lo = hash32(view, utf8.length, 102072);\n    if (hi == 0 && (lo == 0 || lo == 1)) {\n        hi = hi ^ 0x130f9bef;\n        lo = lo ^ -0x6b5f56d8;\n    }\n    return [hi, lo];\n}\nfunction computeMsgId(msg, meaning = '') {\n    let msgFingerprint = fingerprint(msg);\n    if (meaning) {\n        const meaningFingerprint = fingerprint(meaning);\n        msgFingerprint = add64(rol64(msgFingerprint, 1), meaningFingerprint);\n    }\n    const hi = msgFingerprint[0];\n    const lo = msgFingerprint[1];\n    return wordsToDecimalString(hi & 0x7fffffff, lo);\n}\nfunction hash32(view, length, c) {\n    let a = 0x9e3779b9, b = 0x9e3779b9;\n    let index = 0;\n    const end = length - 12;\n    for (; index <= end; index += 12) {\n        a += view.getUint32(index, true);\n        b += view.getUint32(index + 4, true);\n        c += view.getUint32(index + 8, true);\n        const res = mix(a, b, c);\n        a = res[0], b = res[1], c = res[2];\n    }\n    const remainder = length - index;\n    // the first byte of c is reserved for the length\n    c += length;\n    if (remainder >= 4) {\n        a += view.getUint32(index, true);\n        index += 4;\n        if (remainder >= 8) {\n            b += view.getUint32(index, true);\n            index += 4;\n            // Partial 32-bit word for c\n            if (remainder >= 9) {\n                c += view.getUint8(index++) << 8;\n            }\n            if (remainder >= 10) {\n                c += view.getUint8(index++) << 16;\n            }\n            if (remainder === 11) {\n                c += view.getUint8(index++) << 24;\n            }\n        }\n        else {\n            // Partial 32-bit word for b\n            if (remainder >= 5) {\n                b += view.getUint8(index++);\n            }\n            if (remainder >= 6) {\n                b += view.getUint8(index++) << 8;\n            }\n            if (remainder === 7) {\n                b += view.getUint8(index++) << 16;\n            }\n        }\n    }\n    else {\n        // Partial 32-bit word for a\n        if (remainder >= 1) {\n            a += view.getUint8(index++);\n        }\n        if (remainder >= 2) {\n            a += view.getUint8(index++) << 8;\n        }\n        if (remainder === 3) {\n            a += view.getUint8(index++) << 16;\n        }\n    }\n    return mix(a, b, c)[2];\n}\n// clang-format off\nfunction mix(a, b, c) {\n    a -= b;\n    a -= c;\n    a ^= c >>> 13;\n    b -= c;\n    b -= a;\n    b ^= a << 8;\n    c -= a;\n    c -= b;\n    c ^= b >>> 13;\n    a -= b;\n    a -= c;\n    a ^= c >>> 12;\n    b -= c;\n    b -= a;\n    b ^= a << 16;\n    c -= a;\n    c -= b;\n    c ^= b >>> 5;\n    a -= b;\n    a -= c;\n    a ^= c >>> 3;\n    b -= c;\n    b -= a;\n    b ^= a << 10;\n    c -= a;\n    c -= b;\n    c ^= b >>> 15;\n    return [a, b, c];\n}\n// clang-format on\n// Utils\nvar Endian;\n(function (Endian) {\n    Endian[Endian[\"Little\"] = 0] = \"Little\";\n    Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\nfunction add32(a, b) {\n    return add32to64(a, b)[1];\n}\nfunction add32to64(a, b) {\n    const low = (a & 0xffff) + (b & 0xffff);\n    const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n    return [high >>> 16, (high << 16) | (low & 0xffff)];\n}\nfunction add64(a, b) {\n    const ah = a[0], al = a[1];\n    const bh = b[0], bl = b[1];\n    const result = add32to64(al, bl);\n    const carry = result[0];\n    const l = result[1];\n    const h = add32(add32(ah, bh), carry);\n    return [h, l];\n}\n// Rotate a 32b number left `count` position\nfunction rol32(a, count) {\n    return (a << count) | (a >>> (32 - count));\n}\n// Rotate a 64b number left `count` position\nfunction rol64(num, count) {\n    const hi = num[0], lo = num[1];\n    const h = (hi << count) | (lo >>> (32 - count));\n    const l = (lo << count) | (hi >>> (32 - count));\n    return [h, l];\n}\nfunction bytesToWords32(bytes, endian) {\n    const size = (bytes.length + 3) >>> 2;\n    const words32 = [];\n    for (let i = 0; i < size; i++) {\n        words32[i] = wordAt(bytes, i * 4, endian);\n    }\n    return words32;\n}\nfunction byteAt(bytes, index) {\n    return index >= bytes.length ? 0 : bytes[index];\n}\nfunction wordAt(bytes, index, endian) {\n    let word = 0;\n    if (endian === Endian.Big) {\n        for (let i = 0; i < 4; i++) {\n            word += byteAt(bytes, index + i) << (24 - 8 * i);\n        }\n    }\n    else {\n        for (let i = 0; i < 4; i++) {\n            word += byteAt(bytes, index + i) << 8 * i;\n        }\n    }\n    return word;\n}\n/**\n * Create a shared exponentiation pool for base-256 computations. This shared pool provides memoized\n * power-of-256 results with memoized power-of-two computations for efficient multiplication.\n *\n * For our purposes, this can be safely stored as a global without memory concerns. The reason is\n * that we encode two words, so only need the 0th (for the low word) and 4th (for the high word)\n * exponent.\n */\nconst base256 = new BigIntExponentiation(256);\n/**\n * Represents two 32-bit words as a single decimal number. This requires a big integer storage\n * model as JS numbers are not accurate enough to represent the 64-bit number.\n *\n * Based on https://www.danvk.org/hex2dec.html\n */\nfunction wordsToDecimalString(hi, lo) {\n    // Encode the four bytes in lo in the lower digits of the decimal number.\n    // Note: the multiplication results in lo itself but represented by a big integer using its\n    // decimal digits.\n    const decimal = base256.toThePowerOf(0).multiplyBy(lo);\n    // Encode the four bytes in hi above the four lo bytes. lo is a maximum of (2^8)^4, which is why\n    // this multiplication factor is applied.\n    base256.toThePowerOf(4).multiplyByAndAddTo(hi, decimal);\n    return decimal.toString();\n}\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\nclass MissingTranslationError extends Error {\n    constructor(parsedMessage) {\n        super(`No translation found for ${describeMessage(parsedMessage)}.`);\n        this.parsedMessage = parsedMessage;\n        this.type = 'MissingTranslationError';\n    }\n}\nfunction isMissingTranslationError(e) {\n    return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n    const message = parseMessage(messageParts, substitutions);\n    // Look up the translation using the messageId, and then the legacyId if available.\n    let translation = translations[message.id];\n    // If the messageId did not match a translation, try matching the legacy ids instead\n    if (message.legacyIds !== undefined) {\n        for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n            translation = translations[message.legacyIds[i]];\n        }\n    }\n    if (translation === undefined) {\n        throw new MissingTranslationError(message);\n    }\n    return [\n        translation.messageParts, translation.placeholderNames.map(placeholder => {\n            if (message.substitutions.hasOwnProperty(placeholder)) {\n                return message.substitutions[placeholder];\n            }\n            else {\n                throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` +\n                    `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n            }\n        })\n    ];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n    const parts = messageString.split(/{\\$([^}]*)}/);\n    const messageParts = [parts[0]];\n    const placeholderNames = [];\n    for (let i = 1; i < parts.length - 1; i += 2) {\n        placeholderNames.push(parts[i]);\n        messageParts.push(`${parts[i + 1]}`);\n    }\n    const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, rawMessageParts),\n        placeholderNames,\n    };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n    let messageString = messageParts[0];\n    for (let i = 0; i < placeholderNames.length; i++) {\n        messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n    }\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, messageParts),\n        placeholderNames\n    };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n    Object.defineProperty(cooked, 'raw', { value: raw });\n    return cooked;\n}\nfunction describeMessage(message) {\n    const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n    const legacy = message.legacyIds && message.legacyIds.length > 0 ?\n        ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` :\n        '';\n    return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n    // Ensure the translate function exists\n    if (!$localize.translate) {\n        $localize.translate = translate;\n    }\n    if (!$localize.TRANSLATIONS) {\n        $localize.TRANSLATIONS = {};\n    }\n    Object.keys(translations).forEach(key => {\n        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n    });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n    $localize.translate = undefined;\n    $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n    try {\n        return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n    }\n    catch (e) {\n        console.warn(e.message);\n        return [messageParts, substitutions];\n    }\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n    if ($localize$1.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize$1.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER ?\n        messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) :\n        messagePart;\n}\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n\n// This file contains the public API of the `@angular/localize` entry-point\n\n// DO NOT ADD public exports to this file.\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAG,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,QAAQ;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb,OAAOC,IAAIA,CAAA,EAAG;IACV,OAAO,IAAID,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;EACA,OAAOE,GAAGA,CAAA,EAAG;IACT,OAAO,IAAIF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;EACIG,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACIC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIL,UAAU,CAAC,IAAI,CAACI,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;EACIC,GAAGA,CAACC,KAAK,EAAE;IACP,MAAMC,MAAM,GAAG,IAAI,CAACJ,KAAK,CAAC,CAAC;IAC3BI,MAAM,CAACC,SAAS,CAACF,KAAK,CAAC;IACvB,OAAOC,MAAM;EACjB;EACA;AACJ;AACA;EACIC,SAASA,CAACF,KAAK,EAAE;IACb,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,MAAM,CAACU,MAAM,EAAEN,KAAK,CAACJ,MAAM,CAACU,MAAM,CAAC;IACvE,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,EAAEK,CAAC,EAAE,EAAE;MACpC,IAAIC,QAAQ,GAAGF,KAAK;MACpB,IAAIC,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACU,MAAM,EAAE;QACxBG,QAAQ,IAAI,IAAI,CAACb,MAAM,CAACY,CAAC,CAAC;MAC9B;MACA,IAAIA,CAAC,GAAGR,KAAK,CAACJ,MAAM,CAACU,MAAM,EAAE;QACzBG,QAAQ,IAAIT,KAAK,CAACJ,MAAM,CAACY,CAAC,CAAC;MAC/B;MACA,IAAIC,QAAQ,IAAI,EAAE,EAAE;QAChB,IAAI,CAACb,MAAM,CAACY,CAAC,CAAC,GAAGC,QAAQ,GAAG,EAAE;QAC9BF,KAAK,GAAG,CAAC;MACb,CAAC,MACI;QACD,IAAI,CAACX,MAAM,CAACY,CAAC,CAAC,GAAGC,QAAQ;QACzBF,KAAK,GAAG,CAAC;MACb;IACJ;IACA;IACA,IAAIA,KAAK,GAAG,CAAC,EAAE;MACX,IAAI,CAACX,MAAM,CAACO,aAAa,CAAC,GAAG,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACIO,QAAQA,CAAA,EAAG;IACP,IAAIC,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIH,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACU,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9CG,GAAG,IAAI,IAAI,CAACf,MAAM,CAACY,CAAC,CAAC;IACzB;IACA,OAAOG,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BjB,WAAWA,CAACkB,KAAK,EAAE;IACf,IAAI,CAACC,WAAW,GAAG,CAACD,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;EACIE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAACC,GAAG,EAAE;IACZ,MAAMC,OAAO,GAAG1B,UAAU,CAACC,IAAI,CAAC,CAAC;IACjC,IAAI,CAAC0B,kBAAkB,CAACF,GAAG,EAAEC,OAAO,CAAC;IACrC,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACF,GAAG,EAAEhB,MAAM,EAAE;IAC5B,KAAK,IAAImB,QAAQ,GAAG,CAAC,EAAEH,GAAG,KAAK,CAAC,EAAEA,GAAG,GAAGA,GAAG,KAAK,CAAC,EAAEG,QAAQ,EAAE,EAAE;MAC3D,IAAIH,GAAG,GAAG,CAAC,EAAE;QACT,MAAMJ,KAAK,GAAG,IAAI,CAACQ,yBAAyB,CAACD,QAAQ,CAAC;QACtDnB,MAAM,CAACC,SAAS,CAACW,KAAK,CAAC;MAC3B;IACJ;EACJ;EACA;AACJ;AACA;EACIQ,yBAAyBA,CAACD,QAAQ,EAAE;IAChC;IACA;IACA;IACA,KAAK,IAAIZ,CAAC,GAAG,IAAI,CAACM,WAAW,CAACR,MAAM,EAAEE,CAAC,IAAIY,QAAQ,EAAEZ,CAAC,EAAE,EAAE;MACtD,MAAMc,aAAa,GAAG,IAAI,CAACR,WAAW,CAACN,CAAC,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACM,WAAW,CAACN,CAAC,CAAC,GAAGc,aAAa,CAACvB,GAAG,CAACuB,aAAa,CAAC;IAC1D;IACA,OAAO,IAAI,CAACR,WAAW,CAACM,QAAQ,CAAC;EACrC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,oBAAoB,CAAC;EACvB5B,WAAWA,CAAC6B,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAG,CAAC,IAAIb,uBAAuB,CAACpB,UAAU,CAACE,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;EACIgC,YAAYA,CAACN,QAAQ,EAAE;IACnB;IACA;IACA;IACA,KAAK,IAAIZ,CAAC,GAAG,IAAI,CAACiB,SAAS,CAACnB,MAAM,EAAEE,CAAC,IAAIY,QAAQ,EAAEZ,CAAC,EAAE,EAAE;MACpD,MAAMK,KAAK,GAAG,IAAI,CAACY,SAAS,CAACjB,CAAC,GAAG,CAAC,CAAC,CAACQ,UAAU,CAAC,IAAI,CAACQ,IAAI,CAAC;MACzD,IAAI,CAACC,SAAS,CAACjB,CAAC,CAAC,GAAG,IAAII,uBAAuB,CAACC,KAAK,CAAC;IAC1D;IACA,OAAO,IAAI,CAACY,SAAS,CAACL,QAAQ,CAAC;EACnC;AACJ;;AAEA;AACA;AACA;AACA,IAAIO,WAAW;AACf;AACA;AACA;AACA,SAASC,MAAMA,CAACC,OAAO,EAAE;EACrB,OAAOA,OAAO,CAACC,EAAE,IAAIC,aAAa,CAACF,OAAO,CAAC;AAC/C;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACF,OAAO,EAAE;EAC5B,OAAOG,IAAI,CAACC,cAAc,CAACJ,OAAO,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,GAAI,IAAGN,OAAO,CAACO,OAAQ,GAAE,CAAC;AAChF;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACR,OAAO,EAAE;EAC5B,OAAOA,OAAO,CAACC,EAAE,IAAIQ,oBAAoB,CAACT,OAAO,CAAC;AACtD;AACA;AACA;AACA;AACA,SAASS,oBAAoBA,CAACT,OAAO,EAAE;EACnC,MAAMU,OAAO,GAAG,IAAIC,8BAA8B,CAAC,CAAC;EACpD,MAAMC,KAAK,GAAGZ,OAAO,CAACK,KAAK,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAACL,OAAO,EAAE,IAAI,CAAC,CAAC;EAC5D,OAAOM,YAAY,CAACJ,KAAK,CAACN,IAAI,CAAC,EAAE,CAAC,EAAEN,OAAO,CAACO,OAAO,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,kBAAkB,CAAC;EACrBC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACrB,OAAOD,IAAI,CAACnC,KAAK;EACrB;EACAqC,cAAcA,CAACC,SAAS,EAAEF,OAAO,EAAE;IAC/B,OAAQ,IAAGE,SAAS,CAACC,QAAQ,CAACV,GAAG,CAACW,KAAK,IAAIA,KAAK,CAACT,KAAK,CAAC,IAAI,CAAC,CAAC,CAACT,IAAI,CAAC,IAAI,CAAE,GAAE;EAC/E;EACAmB,QAAQA,CAACC,GAAG,EAAEN,OAAO,EAAE;IACnB,MAAMO,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,KAAK,CAAC,CAACjB,GAAG,CAAEkB,CAAC,IAAM,GAAEA,CAAE,KAAIL,GAAG,CAACI,KAAK,CAACC,CAAC,CAAC,CAAChB,KAAK,CAAC,IAAI,CAAE,GAAE,CAAC;IACxF,OAAQ,IAAGW,GAAG,CAACM,UAAW,KAAIN,GAAG,CAACO,IAAK,KAAIN,QAAQ,CAACrB,IAAI,CAAC,IAAI,CAAE,GAAE;EACrE;EACA4B,mBAAmBA,CAACC,EAAE,EAAEf,OAAO,EAAE;IAC7B,OAAOe,EAAE,CAACC,MAAM,GACX,iBAAgBD,EAAE,CAACE,SAAU,KAAI,GACjC,iBAAgBF,EAAE,CAACE,SAAU,KAAIF,EAAE,CAACZ,QAAQ,CAACV,GAAG,CAACW,KAAK,IAAIA,KAAK,CAACT,KAAK,CAAC,IAAI,CAAC,CAAC,CAACT,IAAI,CAAC,IAAI,CAAE,cAAa6B,EAAE,CAACG,SAAU,IAAG;EAC9H;EACAC,gBAAgBA,CAACJ,EAAE,EAAEf,OAAO,EAAE;IAC1B,OAAOe,EAAE,CAACnD,KAAK,GAAI,aAAYmD,EAAE,CAACK,IAAK,KAAIL,EAAE,CAACnD,KAAM,OAAM,GAAI,aAAYmD,EAAE,CAACK,IAAK,KAAI;EAC1F;EACAC,mBAAmBA,CAACN,EAAE,EAAEf,OAAO,EAAE;IAC7B,OAAQ,iBAAgBe,EAAE,CAACK,IAAK,KAAIL,EAAE,CAACnD,KAAK,CAAC+B,KAAK,CAAC,IAAI,CAAE,OAAM;EACnE;AACJ;AACA,MAAM2B,iBAAiB,GAAG,IAAIzB,kBAAkB,CAAC,CAAC;AAClD,SAASb,cAAcA,CAACC,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC2B,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM/B,8BAA8B,SAASM,kBAAkB,CAAC;EAC5DQ,QAAQA,CAACC,GAAG,EAAEN,OAAO,EAAE;IACnB,IAAIO,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,KAAK,CAAC,CAACjB,GAAG,CAAEkB,CAAC,IAAM,GAAEA,CAAE,KAAIL,GAAG,CAACI,KAAK,CAACC,CAAC,CAAC,CAAChB,KAAK,CAAC,IAAI,CAAE,GAAE,CAAC;IACtF;IACA,OAAQ,IAAGW,GAAG,CAACO,IAAK,KAAIN,QAAQ,CAACrB,IAAI,CAAC,IAAI,CAAE,GAAE;EAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAACwC,GAAG,EAAE;EACf7C,WAAW,KAAK,IAAI8C,WAAW,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAG,CAAC,GAAG/C,WAAW,CAACgD,MAAM,CAACH,GAAG,CAAC,CAAC;EACzC,MAAMI,OAAO,GAAGC,cAAc,CAACH,IAAI,EAAEI,MAAM,CAACC,GAAG,CAAC;EAChD,MAAMC,GAAG,GAAGN,IAAI,CAACpE,MAAM,GAAG,CAAC;EAC3B,MAAM2E,CAAC,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;EAC7B,IAAIvC,CAAC,GAAG,UAAU;IAAEwC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;EAClFV,OAAO,CAACI,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,IAAK,EAAE,GAAGA,GAAG,GAAG,EAAG;EAC5CJ,OAAO,CAAC,CAAEI,GAAG,GAAG,EAAE,IAAI,CAAC,IAAK,CAAC,IAAI,EAAE,CAAC,GAAGA,GAAG;EAC1C,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,OAAO,CAACtE,MAAM,EAAEE,CAAC,IAAI,EAAE,EAAE;IACzC,MAAM+E,EAAE,GAAG5C,CAAC;MAAE6C,EAAE,GAAGL,CAAC;MAAEM,EAAE,GAAGL,CAAC;MAAEM,EAAE,GAAGL,CAAC;MAAEM,EAAE,GAAGL,CAAC;IAC5C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIA,CAAC,GAAG,EAAE,EAAE;QACRX,CAAC,CAACW,CAAC,CAAC,GAAGhB,OAAO,CAACpE,CAAC,GAAGoF,CAAC,CAAC;MACzB,CAAC,MACI;QACDX,CAAC,CAACW,CAAC,CAAC,GAAGC,KAAK,CAACZ,CAAC,CAACW,CAAC,GAAG,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,GAAG,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,GAAG,EAAE,CAAC,GAAGX,CAAC,CAACW,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAChE;MACA,MAAME,KAAK,GAAGC,EAAE,CAACH,CAAC,EAAET,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC5B,MAAMW,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;MAClB,MAAMlC,CAAC,GAAGkC,KAAK,CAAC,CAAC,CAAC;MAClB,MAAMG,IAAI,GAAG,CAACJ,KAAK,CAAClD,CAAC,EAAE,CAAC,CAAC,EAAEqD,CAAC,EAAEV,CAAC,EAAE1B,CAAC,EAAEqB,CAAC,CAACW,CAAC,CAAC,CAAC,CAACM,MAAM,CAACC,KAAK,CAAC;MACvDb,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGS,KAAK,CAACV,CAAC,EAAE,EAAE,CAAC;MAChBA,CAAC,GAAGxC,CAAC;MACLA,CAAC,GAAGsD,IAAI;IACZ;IACAtD,CAAC,GAAGwD,KAAK,CAACxD,CAAC,EAAE4C,EAAE,CAAC;IAChBJ,CAAC,GAAGgB,KAAK,CAAChB,CAAC,EAAEK,EAAE,CAAC;IAChBJ,CAAC,GAAGe,KAAK,CAACf,CAAC,EAAEK,EAAE,CAAC;IAChBJ,CAAC,GAAGc,KAAK,CAACd,CAAC,EAAEK,EAAE,CAAC;IAChBJ,CAAC,GAAGa,KAAK,CAACb,CAAC,EAAEK,EAAE,CAAC;EACpB;EACA;EACA,OAAOS,QAAQ,CAACzD,CAAC,CAAC,GAAGyD,QAAQ,CAACjB,CAAC,CAAC,GAAGiB,QAAQ,CAAChB,CAAC,CAAC,GAAGgB,QAAQ,CAACf,CAAC,CAAC,GAAGe,QAAQ,CAACd,CAAC,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,QAAQA,CAACvF,KAAK,EAAE;EACrB;EACA,OAAO,CAACA,KAAK,KAAK,CAAC,EAAEH,QAAQ,CAAC,EAAE,CAAC,CAAC2F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AACtD;AACA,SAASN,EAAEA,CAACO,KAAK,EAAEnB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAIiB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAAEnB,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE,EAAE,UAAU,CAAC;EAC3C;EACA,IAAIiB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAACnB,CAAC,GAAGC,CAAC,GAAGC,CAAC,EAAE,UAAU,CAAC;EAClC;EACA,IAAIiB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAAEnB,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE,EAAE,UAAU,CAAC;EACpD;EACA,OAAO,CAACF,CAAC,GAAGC,CAAC,GAAGC,CAAC,EAAE,UAAU,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,WAAWA,CAAC/B,GAAG,EAAE;EACtB7C,WAAW,KAAK,IAAI8C,WAAW,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAG/C,WAAW,CAACgD,MAAM,CAACH,GAAG,CAAC;EACpC,MAAMgC,IAAI,GAAG,IAAIC,QAAQ,CAAC/B,IAAI,CAACgC,MAAM,EAAEhC,IAAI,CAACiC,UAAU,EAAEjC,IAAI,CAACkC,UAAU,CAAC;EACxE,IAAIC,EAAE,GAAGC,MAAM,CAACN,IAAI,EAAE9B,IAAI,CAACpE,MAAM,EAAE,CAAC,CAAC;EACrC,IAAIyG,EAAE,GAAGD,MAAM,CAACN,IAAI,EAAE9B,IAAI,CAACpE,MAAM,EAAE,MAAM,CAAC;EAC1C,IAAIuG,EAAE,IAAI,CAAC,KAAKE,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,CAAC,EAAE;IACjCF,EAAE,GAAGA,EAAE,GAAG,UAAU;IACpBE,EAAE,GAAGA,EAAE,GAAG,CAAC,UAAU;EACzB;EACA,OAAO,CAACF,EAAE,EAAEE,EAAE,CAAC;AACnB;AACA,SAASlE,YAAYA,CAACmE,GAAG,EAAE5E,OAAO,GAAG,EAAE,EAAE;EACrC,IAAI6E,cAAc,GAAGV,WAAW,CAACS,GAAG,CAAC;EACrC,IAAI5E,OAAO,EAAE;IACT,MAAM8E,kBAAkB,GAAGX,WAAW,CAACnE,OAAO,CAAC;IAC/C6E,cAAc,GAAGE,KAAK,CAACC,KAAK,CAACH,cAAc,EAAE,CAAC,CAAC,EAAEC,kBAAkB,CAAC;EACxE;EACA,MAAML,EAAE,GAAGI,cAAc,CAAC,CAAC,CAAC;EAC5B,MAAMF,EAAE,GAAGE,cAAc,CAAC,CAAC,CAAC;EAC5B,OAAOI,oBAAoB,CAACR,EAAE,GAAG,UAAU,EAAEE,EAAE,CAAC;AACpD;AACA,SAASD,MAAMA,CAACN,IAAI,EAAElG,MAAM,EAAE8E,CAAC,EAAE;EAC7B,IAAIzC,CAAC,GAAG,UAAU;IAAEwC,CAAC,GAAG,UAAU;EAClC,IAAImB,KAAK,GAAG,CAAC;EACb,MAAMgB,GAAG,GAAGhH,MAAM,GAAG,EAAE;EACvB,OAAOgG,KAAK,IAAIgB,GAAG,EAAEhB,KAAK,IAAI,EAAE,EAAE;IAC9B3D,CAAC,IAAI6D,IAAI,CAACe,SAAS,CAACjB,KAAK,EAAE,IAAI,CAAC;IAChCnB,CAAC,IAAIqB,IAAI,CAACe,SAAS,CAACjB,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpClB,CAAC,IAAIoB,IAAI,CAACe,SAAS,CAACjB,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpC,MAAM3F,GAAG,GAAG6G,GAAG,CAAC7E,CAAC,EAAEwC,CAAC,EAAEC,CAAC,CAAC;IACxBzC,CAAC,GAAGhC,GAAG,CAAC,CAAC,CAAC,EAAEwE,CAAC,GAAGxE,GAAG,CAAC,CAAC,CAAC,EAAEyE,CAAC,GAAGzE,GAAG,CAAC,CAAC,CAAC;EACtC;EACA,MAAM8G,SAAS,GAAGnH,MAAM,GAAGgG,KAAK;EAChC;EACAlB,CAAC,IAAI9E,MAAM;EACX,IAAImH,SAAS,IAAI,CAAC,EAAE;IAChB9E,CAAC,IAAI6D,IAAI,CAACe,SAAS,CAACjB,KAAK,EAAE,IAAI,CAAC;IAChCA,KAAK,IAAI,CAAC;IACV,IAAImB,SAAS,IAAI,CAAC,EAAE;MAChBtC,CAAC,IAAIqB,IAAI,CAACe,SAAS,CAACjB,KAAK,EAAE,IAAI,CAAC;MAChCA,KAAK,IAAI,CAAC;MACV;MACA,IAAImB,SAAS,IAAI,CAAC,EAAE;QAChBrC,CAAC,IAAIoB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAImB,SAAS,IAAI,EAAE,EAAE;QACjBrC,CAAC,IAAIoB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;MACA,IAAImB,SAAS,KAAK,EAAE,EAAE;QAClBrC,CAAC,IAAIoB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ,CAAC,MACI;MACD;MACA,IAAImB,SAAS,IAAI,CAAC,EAAE;QAChBtC,CAAC,IAAIqB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC;MAC/B;MACA,IAAImB,SAAS,IAAI,CAAC,EAAE;QAChBtC,CAAC,IAAIqB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAImB,SAAS,KAAK,CAAC,EAAE;QACjBtC,CAAC,IAAIqB,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ;EACJ,CAAC,MACI;IACD;IACA,IAAImB,SAAS,IAAI,CAAC,EAAE;MAChB9E,CAAC,IAAI6D,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC;IAC/B;IACA,IAAImB,SAAS,IAAI,CAAC,EAAE;MAChB9E,CAAC,IAAI6D,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,CAAC;IACpC;IACA,IAAImB,SAAS,KAAK,CAAC,EAAE;MACjB9E,CAAC,IAAI6D,IAAI,CAACkB,QAAQ,CAACpB,KAAK,EAAE,CAAC,IAAI,EAAE;IACrC;EACJ;EACA,OAAOkB,GAAG,CAAC7E,CAAC,EAAEwC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA;AACA,SAASoC,GAAGA,CAAC7E,CAAC,EAAEwC,CAAC,EAAEC,CAAC,EAAE;EAClBzC,CAAC,IAAIwC,CAAC;EACNxC,CAAC,IAAIyC,CAAC;EACNzC,CAAC,IAAIyC,CAAC,KAAK,EAAE;EACbD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIxC,CAAC;EACNwC,CAAC,IAAIxC,CAAC,IAAI,CAAC;EACXyC,CAAC,IAAIzC,CAAC;EACNyC,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACbxC,CAAC,IAAIwC,CAAC;EACNxC,CAAC,IAAIyC,CAAC;EACNzC,CAAC,IAAIyC,CAAC,KAAK,EAAE;EACbD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIxC,CAAC;EACNwC,CAAC,IAAIxC,CAAC,IAAI,EAAE;EACZyC,CAAC,IAAIzC,CAAC;EACNyC,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,CAAC;EACZxC,CAAC,IAAIwC,CAAC;EACNxC,CAAC,IAAIyC,CAAC;EACNzC,CAAC,IAAIyC,CAAC,KAAK,CAAC;EACZD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAIxC,CAAC;EACNwC,CAAC,IAAIxC,CAAC,IAAI,EAAE;EACZyC,CAAC,IAAIzC,CAAC;EACNyC,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACb,OAAO,CAACxC,CAAC,EAAEwC,CAAC,EAAEC,CAAC,CAAC;AACpB;AACA;AACA;AACA,IAAIN,MAAM;AACV,CAAC,UAAUA,MAAM,EAAE;EACfA,MAAM,CAACA,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACvCA,MAAM,CAACA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACrC,CAAC,EAAEA,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,SAASqB,KAAKA,CAACxD,CAAC,EAAEwC,CAAC,EAAE;EACjB,OAAOwC,SAAS,CAAChF,CAAC,EAAEwC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA,SAASwC,SAASA,CAAChF,CAAC,EAAEwC,CAAC,EAAE;EACrB,MAAMyC,GAAG,GAAG,CAACjF,CAAC,GAAG,MAAM,KAAKwC,CAAC,GAAG,MAAM,CAAC;EACvC,MAAM0C,IAAI,GAAG,CAAClF,CAAC,KAAK,EAAE,KAAKwC,CAAC,KAAK,EAAE,CAAC,IAAIyC,GAAG,KAAK,EAAE,CAAC;EACnD,OAAO,CAACC,IAAI,KAAK,EAAE,EAAGA,IAAI,IAAI,EAAE,GAAKD,GAAG,GAAG,MAAO,CAAC;AACvD;AACA,SAAST,KAAKA,CAACxE,CAAC,EAAEwC,CAAC,EAAE;EACjB,MAAM2C,EAAE,GAAGnF,CAAC,CAAC,CAAC,CAAC;IAAEoF,EAAE,GAAGpF,CAAC,CAAC,CAAC,CAAC;EAC1B,MAAMqF,EAAE,GAAG7C,CAAC,CAAC,CAAC,CAAC;IAAE8C,EAAE,GAAG9C,CAAC,CAAC,CAAC,CAAC;EAC1B,MAAMlF,MAAM,GAAG0H,SAAS,CAACI,EAAE,EAAEE,EAAE,CAAC;EAChC,MAAM1H,KAAK,GAAGN,MAAM,CAAC,CAAC,CAAC;EACvB,MAAMiI,CAAC,GAAGjI,MAAM,CAAC,CAAC,CAAC;EACnB,MAAMkI,CAAC,GAAGhC,KAAK,CAACA,KAAK,CAAC2B,EAAE,EAAEE,EAAE,CAAC,EAAEzH,KAAK,CAAC;EACrC,OAAO,CAAC4H,CAAC,EAAED,CAAC,CAAC;AACjB;AACA;AACA,SAASrC,KAAKA,CAAClD,CAAC,EAAEyF,KAAK,EAAE;EACrB,OAAQzF,CAAC,IAAIyF,KAAK,GAAKzF,CAAC,KAAM,EAAE,GAAGyF,KAAO;AAC9C;AACA;AACA,SAAShB,KAAKA,CAACnG,GAAG,EAAEmH,KAAK,EAAE;EACvB,MAAMvB,EAAE,GAAG5F,GAAG,CAAC,CAAC,CAAC;IAAE8F,EAAE,GAAG9F,GAAG,CAAC,CAAC,CAAC;EAC9B,MAAMkH,CAAC,GAAItB,EAAE,IAAIuB,KAAK,GAAKrB,EAAE,KAAM,EAAE,GAAGqB,KAAO;EAC/C,MAAMF,CAAC,GAAInB,EAAE,IAAIqB,KAAK,GAAKvB,EAAE,KAAM,EAAE,GAAGuB,KAAO;EAC/C,OAAO,CAACD,CAAC,EAAED,CAAC,CAAC;AACjB;AACA,SAASrD,cAAcA,CAACwD,KAAK,EAAEC,MAAM,EAAE;EACnC,MAAMC,IAAI,GAAIF,KAAK,CAAC/H,MAAM,GAAG,CAAC,KAAM,CAAC;EACrC,MAAMsE,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+H,IAAI,EAAE/H,CAAC,EAAE,EAAE;IAC3BoE,OAAO,CAACpE,CAAC,CAAC,GAAGgI,MAAM,CAACH,KAAK,EAAE7H,CAAC,GAAG,CAAC,EAAE8H,MAAM,CAAC;EAC7C;EACA,OAAO1D,OAAO;AAClB;AACA,SAAS6D,MAAMA,CAACJ,KAAK,EAAE/B,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI+B,KAAK,CAAC/H,MAAM,GAAG,CAAC,GAAG+H,KAAK,CAAC/B,KAAK,CAAC;AACnD;AACA,SAASkC,MAAMA,CAACH,KAAK,EAAE/B,KAAK,EAAEgC,MAAM,EAAE;EAClC,IAAII,IAAI,GAAG,CAAC;EACZ,IAAIJ,MAAM,KAAKxD,MAAM,CAACC,GAAG,EAAE;IACvB,KAAK,IAAIvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBkI,IAAI,IAAID,MAAM,CAACJ,KAAK,EAAE/B,KAAK,GAAG9F,CAAC,CAAC,IAAK,EAAE,GAAG,CAAC,GAAGA,CAAE;IACpD;EACJ,CAAC,MACI;IACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBkI,IAAI,IAAID,MAAM,CAACJ,KAAK,EAAE/B,KAAK,GAAG9F,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC;IAC7C;EACJ;EACA,OAAOkI,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,IAAIpH,oBAAoB,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8F,oBAAoBA,CAACR,EAAE,EAAEE,EAAE,EAAE;EAClC;EACA;EACA;EACA,MAAM6B,OAAO,GAAGD,OAAO,CAACjH,YAAY,CAAC,CAAC,CAAC,CAACV,UAAU,CAAC+F,EAAE,CAAC;EACtD;EACA;EACA4B,OAAO,CAACjH,YAAY,CAAC,CAAC,CAAC,CAACP,kBAAkB,CAAC0F,EAAE,EAAE+B,OAAO,CAAC;EACvD,OAAOA,OAAO,CAAClI,QAAQ,CAAC,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmI,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,mBAAmB,GAAG,EAAE,EAAE;EACvG,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,qBAAqB,GAAG,CAAC,CAAC;EAChC,MAAMC,oBAAoB,GAAG,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAGC,aAAa,CAACT,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMC,mBAAmB,GAAG,CAACH,QAAQ,CAACtG,IAAI,CAAC;EAC3C,MAAM0G,gBAAgB,GAAG,EAAE;EAC3B,IAAIC,aAAa,GAAGL,QAAQ,CAACtG,IAAI;EACjC,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,YAAY,CAACxI,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC1C,MAAM;MAAEoJ,WAAW;MAAEC,eAAe,GAAGC,sBAAsB,CAACtJ,CAAC,CAAC;MAAEuJ;IAAoB,CAAC,GAAGC,gBAAgB,CAAClB,YAAY,CAACtI,CAAC,CAAC,EAAEsI,YAAY,CAACU,GAAG,CAAChJ,CAAC,CAAC,CAAC;IAChJmJ,aAAa,IAAK,KAAIE,eAAgB,IAAGD,WAAY,EAAC;IACtD,IAAIb,WAAW,KAAKkB,SAAS,EAAE;MAC3Bd,aAAa,CAACU,eAAe,CAAC,GAAGd,WAAW,CAACvI,CAAC,GAAG,CAAC,CAAC;MACnD4I,qBAAqB,CAACS,eAAe,CAAC,GAAGX,mBAAmB,CAAC1I,CAAC,GAAG,CAAC,CAAC;IACvE;IACAkJ,gBAAgB,CAACQ,IAAI,CAACL,eAAe,CAAC;IACtC,IAAIE,mBAAmB,KAAKE,SAAS,EAAE;MACnCZ,oBAAoB,CAACQ,eAAe,CAAC,GAAGE,mBAAmB;IAC/D;IACAN,mBAAmB,CAACS,IAAI,CAACN,WAAW,CAAC;EACzC;EACA,MAAMO,SAAS,GAAGb,QAAQ,CAACc,QAAQ,IAAIvH,YAAY,CAAC8G,aAAa,EAAEL,QAAQ,CAAClH,OAAO,IAAI,EAAE,CAAC;EAC1F,MAAMiI,SAAS,GAAGf,QAAQ,CAACe,SAAS,GAAGf,QAAQ,CAACe,SAAS,CAACC,MAAM,CAACxI,EAAE,IAAIA,EAAE,KAAKqI,SAAS,CAAC,GAAG,EAAE;EAC7F,OAAO;IACHrI,EAAE,EAAEqI,SAAS;IACbE,SAAS;IACTlB,aAAa;IACbC,qBAAqB;IACrBpG,IAAI,EAAE2G,aAAa;IACnBS,QAAQ,EAAEd,QAAQ,CAACc,QAAQ;IAC3BhI,OAAO,EAAEkH,QAAQ,CAAClH,OAAO,IAAI,EAAE;IAC/BmI,WAAW,EAAEjB,QAAQ,CAACiB,WAAW,IAAI,EAAE;IACvCzB,YAAY,EAAEW,mBAAmB;IACjCR,oBAAoB;IACpBS,gBAAgB;IAChBL,oBAAoB;IACpBL;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,aAAaA,CAACiB,MAAM,EAAEhB,GAAG,EAAE;EAChC,MAAM;IAAExG,IAAI,EAAE2G,aAAa;IAAEc;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEhB,GAAG,CAAC;EAC9D,IAAIiB,KAAK,KAAKR,SAAS,EAAE;IACrB,OAAO;MAAEjH,IAAI,EAAE2G;IAAc,CAAC;EAClC,CAAC,MACI;IACD,MAAM,CAACgB,gBAAgB,EAAE,GAAGN,SAAS,CAAC,GAAGI,KAAK,CAACG,KAAK,CAACrL,mBAAmB,CAAC;IACzE,MAAM,CAACsL,cAAc,EAAET,QAAQ,CAAC,GAAGO,gBAAgB,CAACC,KAAK,CAACtL,YAAY,EAAE,CAAC,CAAC;IAC1E,IAAI,CAAC8C,OAAO,EAAEmI,WAAW,CAAC,GAAGM,cAAc,CAACD,KAAK,CAACvL,iBAAiB,EAAE,CAAC,CAAC;IACvE,IAAIkL,WAAW,KAAKN,SAAS,EAAE;MAC3BM,WAAW,GAAGnI,OAAO;MACrBA,OAAO,GAAG6H,SAAS;IACvB;IACA,IAAIM,WAAW,KAAK,EAAE,EAAE;MACpBA,WAAW,GAAGN,SAAS;IAC3B;IACA,OAAO;MAAEjH,IAAI,EAAE2G,aAAa;MAAEvH,OAAO;MAAEmI,WAAW;MAAEH,QAAQ;MAAEC;IAAU,CAAC;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,gBAAgBA,CAACQ,MAAM,EAAEhB,GAAG,EAAE;EACnC,MAAM;IAAExG,IAAI,EAAE4G,WAAW;IAAEa;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEhB,GAAG,CAAC;EAC5D,IAAIiB,KAAK,KAAKR,SAAS,EAAE;IACrB,OAAO;MAAEL;IAAY,CAAC;EAC1B,CAAC,MACI;IACD,MAAM,CAACC,eAAe,EAAEE,mBAAmB,CAAC,GAAGU,KAAK,CAACG,KAAK,CAACtL,YAAY,CAAC;IACxE,OAAO;MAAEsK,WAAW;MAAEC,eAAe;MAAEE;IAAoB,CAAC;EAChE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,UAAUA,CAACF,MAAM,EAAEhB,GAAG,EAAE;EAC7B,IAAIA,GAAG,CAACsB,MAAM,CAAC,CAAC,CAAC,KAAK1L,cAAc,EAAE;IAClC,OAAO;MAAE4D,IAAI,EAAEwH;IAAO,CAAC;EAC3B,CAAC,MACI;IACD,MAAMO,UAAU,GAAGC,cAAc,CAACR,MAAM,EAAEhB,GAAG,CAAC;IAC9C,OAAO;MACHiB,KAAK,EAAED,MAAM,CAACS,SAAS,CAAC,CAAC,EAAEF,UAAU,CAAC;MACtC/H,IAAI,EAAEwH,MAAM,CAACS,SAAS,CAACF,UAAU,GAAG,CAAC;IACzC,CAAC;EACL;AACJ;AACA,SAASjB,sBAAsBA,CAACxD,KAAK,EAAE;EACnC,OAAOA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAI,MAAKA,KAAK,GAAG,CAAE,EAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0E,cAAcA,CAACR,MAAM,EAAEhB,GAAG,EAAE;EACjC,KAAK,IAAI0B,WAAW,GAAG,CAAC,EAAEC,QAAQ,GAAG,CAAC,EAAED,WAAW,GAAGV,MAAM,CAAClK,MAAM,EAAE4K,WAAW,EAAE,EAAEC,QAAQ,EAAE,EAAE;IAC5F,IAAI3B,GAAG,CAAC2B,QAAQ,CAAC,KAAK,IAAI,EAAE;MACxBA,QAAQ,EAAE;IACd,CAAC,MACI,IAAIX,MAAM,CAACU,WAAW,CAAC,KAAK9L,cAAc,EAAE;MAC7C,OAAO8L,WAAW;IACtB;EACJ;EACA,MAAM,IAAIE,KAAK,CAAE,6CAA4C5B,GAAI,IAAG,CAAC;AACzE;AAEA,MAAM6B,uBAAuB,SAASD,KAAK,CAAC;EACxCzL,WAAWA,CAAC2L,aAAa,EAAE;IACvB,KAAK,CAAE,4BAA2BC,eAAe,CAACD,aAAa,CAAE,GAAE,CAAC;IACpE,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACxH,IAAI,GAAG,yBAAyB;EACzC;AACJ;AACA,SAAS0H,yBAAyBA,CAAClG,CAAC,EAAE;EAClC,OAAOA,CAAC,CAACxB,IAAI,KAAK,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2H,WAAWA,CAACC,YAAY,EAAE5C,YAAY,EAAEK,aAAa,EAAE;EAC5D,MAAMtH,OAAO,GAAGgH,YAAY,CAACC,YAAY,EAAEK,aAAa,CAAC;EACzD;EACA,IAAIwC,WAAW,GAAGD,YAAY,CAAC7J,OAAO,CAACC,EAAE,CAAC;EAC1C;EACA,IAAID,OAAO,CAACwI,SAAS,KAAKJ,SAAS,EAAE;IACjC,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,OAAO,CAACwI,SAAS,CAAC/J,MAAM,IAAIqL,WAAW,KAAK1B,SAAS,EAAEzJ,CAAC,EAAE,EAAE;MAC5EmL,WAAW,GAAGD,YAAY,CAAC7J,OAAO,CAACwI,SAAS,CAAC7J,CAAC,CAAC,CAAC;IACpD;EACJ;EACA,IAAImL,WAAW,KAAK1B,SAAS,EAAE;IAC3B,MAAM,IAAIoB,uBAAuB,CAACxJ,OAAO,CAAC;EAC9C;EACA,OAAO,CACH8J,WAAW,CAAC7C,YAAY,EAAE6C,WAAW,CAACjC,gBAAgB,CAAChH,GAAG,CAACkJ,WAAW,IAAI;IACtE,IAAI/J,OAAO,CAACsH,aAAa,CAAC0C,cAAc,CAACD,WAAW,CAAC,EAAE;MACnD,OAAO/J,OAAO,CAACsH,aAAa,CAACyC,WAAW,CAAC;IAC7C,CAAC,MACI;MACD,MAAM,IAAIR,KAAK,CAAE,sFAAqFG,eAAe,CAAC1J,OAAO,CAAE,KAAI,GAC9H,oDAAmD+J,WAAY,wCAAuC,CAAC;IAChH;EACJ,CAAC,CAAC,CACL;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACnC,aAAa,EAAE;EACrC,MAAMlH,KAAK,GAAGkH,aAAa,CAACiB,KAAK,CAAC,aAAa,CAAC;EAChD,MAAM9B,YAAY,GAAG,CAACrG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B,MAAMiH,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAIlJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,KAAK,CAACnC,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;IAC1CkJ,gBAAgB,CAACQ,IAAI,CAACzH,KAAK,CAACjC,CAAC,CAAC,CAAC;IAC/BsI,YAAY,CAACoB,IAAI,CAAE,GAAEzH,KAAK,CAACjC,CAAC,GAAG,CAAC,CAAE,EAAC,CAAC;EACxC;EACA,MAAMuL,eAAe,GAAGjD,YAAY,CAACpG,GAAG,CAACsJ,IAAI,IAAIA,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC,KAAK1L,cAAc,GAAG,IAAI,GAAG4M,IAAI,GAAGA,IAAI,CAAC;EACxG,OAAO;IACHhJ,IAAI,EAAE2G,aAAa;IACnBb,YAAY,EAAEmD,kBAAkB,CAACnD,YAAY,EAAEiD,eAAe,CAAC;IAC/DrC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,qBAAqBA,CAACpD,YAAY,EAAEY,gBAAgB,GAAG,EAAE,EAAE;EAChE,IAAIC,aAAa,GAAGb,YAAY,CAAC,CAAC,CAAC;EACnC,KAAK,IAAItI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkJ,gBAAgB,CAACpJ,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC9CmJ,aAAa,IAAK,KAAID,gBAAgB,CAAClJ,CAAC,CAAE,IAAGsI,YAAY,CAACtI,CAAC,GAAG,CAAC,CAAE,EAAC;EACtE;EACA,OAAO;IACHwC,IAAI,EAAE2G,aAAa;IACnBb,YAAY,EAAEmD,kBAAkB,CAACnD,YAAY,EAAEA,YAAY,CAAC;IAC5DY;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuC,kBAAkBA,CAACzB,MAAM,EAAEhB,GAAG,EAAE;EACrC/F,MAAM,CAAC0I,cAAc,CAAC3B,MAAM,EAAE,KAAK,EAAE;IAAE3J,KAAK,EAAE2I;EAAI,CAAC,CAAC;EACpD,OAAOgB,MAAM;AACjB;AACA,SAASe,eAAeA,CAAC1J,OAAO,EAAE;EAC9B,MAAMuK,aAAa,GAAGvK,OAAO,CAACO,OAAO,IAAK,OAAMP,OAAO,CAACO,OAAQ,GAAE;EAClE,MAAMiK,MAAM,GAAGxK,OAAO,CAACwI,SAAS,IAAIxI,OAAO,CAACwI,SAAS,CAAC/J,MAAM,GAAG,CAAC,GAC3D,KAAIuB,OAAO,CAACwI,SAAS,CAAC3H,GAAG,CAACwF,CAAC,IAAK,IAAGA,CAAE,GAAE,CAAC,CAAC/F,IAAI,CAAC,IAAI,CAAE,GAAE,GACvD,EAAE;EACN,OAAQ,IAAGN,OAAO,CAACC,EAAG,IAAGuK,MAAO,MAAKxK,OAAO,CAACmB,IAAK,IAAGoJ,aAAc,GAAE;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACZ,YAAY,EAAE;EACpC;EACA,IAAI,CAACa,SAAS,CAACC,SAAS,EAAE;IACtBD,SAAS,CAACC,SAAS,GAAGA,SAAS;EACnC;EACA,IAAI,CAACD,SAAS,CAACE,YAAY,EAAE;IACzBF,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;EAC/B;EACAhJ,MAAM,CAACC,IAAI,CAACgI,YAAY,CAAC,CAACgB,OAAO,CAACC,GAAG,IAAI;IACrCJ,SAAS,CAACE,YAAY,CAACE,GAAG,CAAC,GAAGb,gBAAgB,CAACJ,YAAY,CAACiB,GAAG,CAAC,CAAC;EACrE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzBL,SAAS,CAACC,SAAS,GAAGvC,SAAS;EAC/BsC,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,SAASA,CAAC1D,YAAY,EAAEK,aAAa,EAAE;EAC5C,IAAI;IACA,OAAOsC,WAAW,CAACc,SAAS,CAACE,YAAY,EAAE3D,YAAY,EAAEK,aAAa,CAAC;EAC3E,CAAC,CACD,OAAO7D,CAAC,EAAE;IACNuH,OAAO,CAACC,IAAI,CAACxH,CAAC,CAACzD,OAAO,CAAC;IACvB,OAAO,CAACiH,YAAY,EAAEK,aAAa,CAAC;EACxC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4D,WAAW,GAAG,SAAAA,CAAUjE,YAAY,EAAE,GAAGC,WAAW,EAAE;EACxD,IAAIgE,WAAW,CAACP,SAAS,EAAE;IACvB;IACA,MAAMb,WAAW,GAAGoB,WAAW,CAACP,SAAS,CAAC1D,YAAY,EAAEC,WAAW,CAAC;IACpED,YAAY,GAAG6C,WAAW,CAAC,CAAC,CAAC;IAC7B5C,WAAW,GAAG4C,WAAW,CAAC,CAAC,CAAC;EAChC;EACA,IAAI9J,OAAO,GAAGmL,UAAU,CAAClE,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,KAAK,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,YAAY,CAACxI,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC1CqB,OAAO,IAAIkH,WAAW,CAACvI,CAAC,GAAG,CAAC,CAAC,GAAGwM,UAAU,CAAClE,YAAY,CAACtI,CAAC,CAAC,EAAEsI,YAAY,CAACU,GAAG,CAAChJ,CAAC,CAAC,CAAC;EACpF;EACA,OAAOqB,OAAO;AAClB,CAAC;AACD,MAAMoL,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,UAAUA,CAACpD,WAAW,EAAEsD,cAAc,EAAE;EAC7C,OAAOA,cAAc,CAACpC,MAAM,CAAC,CAAC,CAAC,KAAKmC,YAAY,GAC5CrD,WAAW,CAACqB,SAAS,CAACD,cAAc,CAACpB,WAAW,EAAEsD,cAAc,CAAC,GAAG,CAAC,CAAC,GACtEtD,WAAW;AACnB;;AAEA;;AAEA;;AAEA;;AAEA,SAASgD,iBAAiB,EAAEN,gBAAgB,EAAES,WAAW,IAAII,UAAU,EAAE9B,uBAAuB,IAAI+B,wBAAwB,EAAEvK,YAAY,IAAIwK,aAAa,EAAErC,cAAc,IAAIsC,eAAe,EAAE9B,yBAAyB,IAAI+B,0BAA0B,EAAErB,qBAAqB,IAAIsB,sBAAsB,EAAEvB,kBAAkB,IAAIwB,mBAAmB,EAAE5E,YAAY,IAAI6E,aAAa,EAAEnE,aAAa,IAAIoE,cAAc,EAAE7B,gBAAgB,IAAI8B,iBAAiB,EAAElD,UAAU,IAAImD,WAAW,EAAEpC,WAAW,IAAIqC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}