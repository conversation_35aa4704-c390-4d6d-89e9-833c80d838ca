{"ast": null, "code": "function Grid(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  let wasMultiRow;\n  const getSpaceBetween = () => {\n    let spaceBetween = swiper.params.spaceBetween;\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n    } else if (typeof spaceBetween === 'string') {\n      spaceBetween = parseFloat(spaceBetween);\n    }\n    return spaceBetween;\n  };\n  const initSlides = slidesLength => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n    slidesPerRow = slidesNumberEvenToRows / rows;\n  };\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const {\n      slidesPerGroup\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.row = row;\n    slide.column = column;\n    slide.style[getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const {\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  const onInit = () => {\n    wasMultiRow = swiper.params.grid && swiper.params.grid.rows > 1;\n  };\n  const onUpdate = () => {\n    const {\n      params,\n      el\n    } = swiper;\n    const isMultiRow = params.grid && params.grid.rows > 1;\n    if (wasMultiRow && !isMultiRow) {\n      el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n      numFullColumns = 1;\n      swiper.emitContainerClasses();\n    } else if (!wasMultiRow && isMultiRow) {\n      el.classList.add(`${params.containerModifierClass}grid`);\n      if (params.grid.fill === 'column') {\n        el.classList.add(`${params.containerModifierClass}grid-column`);\n      }\n      swiper.emitContainerClasses();\n    }\n    wasMultiRow = isMultiRow;\n  };\n  on('init', onInit);\n  on('update', onUpdate);\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}\nexport { Grid as default };", "map": {"version": 3, "names": ["Grid", "_ref", "swiper", "extendParams", "on", "grid", "rows", "fill", "slidesNumberEvenToRows", "slidesPerRow", "numFullColumns", "wasMultiRow", "getSpaceBetween", "spaceBetween", "params", "indexOf", "parseFloat", "replace", "size", "initSlides", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>iew", "Math", "floor", "ceil", "max", "updateSlide", "i", "slide", "getDirectionLabel", "slidesPerGroup", "newSlideOrderIndex", "column", "row", "groupIndex", "slideIndexInGroup", "columnsInGroup", "min", "style", "order", "updateWrapperSize", "slideSize", "snapGrid", "centeredSlides", "roundLengths", "virtualSize", "wrapperEl", "newSlidesGrid", "length", "slidesGridItem", "push", "splice", "onInit", "onUpdate", "el", "isMultiRow", "classList", "remove", "containerModifierClass", "emitContainerClasses", "add", "default"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/swiper/modules/grid.mjs"], "sourcesContent": ["function Grid(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    grid: {\n      rows: 1,\n      fill: 'column'\n    }\n  });\n  let slidesNumberEvenToRows;\n  let slidesPerRow;\n  let numFullColumns;\n  let wasMultiRow;\n  const getSpaceBetween = () => {\n    let spaceBetween = swiper.params.spaceBetween;\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n    } else if (typeof spaceBetween === 'string') {\n      spaceBetween = parseFloat(spaceBetween);\n    }\n    return spaceBetween;\n  };\n  const initSlides = slidesLength => {\n    const {\n      slidesPerView\n    } = swiper.params;\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    numFullColumns = Math.floor(slidesLength / rows);\n    if (Math.floor(slidesLength / rows) === slidesLength / rows) {\n      slidesNumberEvenToRows = slidesLength;\n    } else {\n      slidesNumberEvenToRows = Math.ceil(slidesLength / rows) * rows;\n    }\n    if (slidesPerView !== 'auto' && fill === 'row') {\n      slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, slidesPerView * rows);\n    }\n    slidesPerRow = slidesNumberEvenToRows / rows;\n  };\n  const updateSlide = (i, slide, slidesLength, getDirectionLabel) => {\n    const {\n      slidesPerGroup\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows,\n      fill\n    } = swiper.params.grid;\n    // Set slides order\n    let newSlideOrderIndex;\n    let column;\n    let row;\n    if (fill === 'row' && slidesPerGroup > 1) {\n      const groupIndex = Math.floor(i / (slidesPerGroup * rows));\n      const slideIndexInGroup = i - rows * slidesPerGroup * groupIndex;\n      const columnsInGroup = groupIndex === 0 ? slidesPerGroup : Math.min(Math.ceil((slidesLength - groupIndex * rows * slidesPerGroup) / rows), slidesPerGroup);\n      row = Math.floor(slideIndexInGroup / columnsInGroup);\n      column = slideIndexInGroup - row * columnsInGroup + groupIndex * slidesPerGroup;\n      newSlideOrderIndex = column + row * slidesNumberEvenToRows / rows;\n      slide.style.order = newSlideOrderIndex;\n    } else if (fill === 'column') {\n      column = Math.floor(i / rows);\n      row = i - column * rows;\n      if (column > numFullColumns || column === numFullColumns && row === rows - 1) {\n        row += 1;\n        if (row >= rows) {\n          row = 0;\n          column += 1;\n        }\n      }\n    } else {\n      row = Math.floor(i / slidesPerRow);\n      column = i - row * slidesPerRow;\n    }\n    slide.row = row;\n    slide.column = column;\n    slide.style[getDirectionLabel('margin-top')] = row !== 0 ? spaceBetween && `${spaceBetween}px` : '';\n  };\n  const updateWrapperSize = (slideSize, snapGrid, getDirectionLabel) => {\n    const {\n      centeredSlides,\n      roundLengths\n    } = swiper.params;\n    const spaceBetween = getSpaceBetween();\n    const {\n      rows\n    } = swiper.params.grid;\n    swiper.virtualSize = (slideSize + spaceBetween) * slidesNumberEvenToRows;\n    swiper.virtualSize = Math.ceil(swiper.virtualSize / rows) - spaceBetween;\n    swiper.wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n    if (centeredSlides) {\n      const newSlidesGrid = [];\n      for (let i = 0; i < snapGrid.length; i += 1) {\n        let slidesGridItem = snapGrid[i];\n        if (roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n        if (snapGrid[i] < swiper.virtualSize + snapGrid[0]) newSlidesGrid.push(slidesGridItem);\n      }\n      snapGrid.splice(0, snapGrid.length);\n      snapGrid.push(...newSlidesGrid);\n    }\n  };\n  const onInit = () => {\n    wasMultiRow = swiper.params.grid && swiper.params.grid.rows > 1;\n  };\n  const onUpdate = () => {\n    const {\n      params,\n      el\n    } = swiper;\n    const isMultiRow = params.grid && params.grid.rows > 1;\n    if (wasMultiRow && !isMultiRow) {\n      el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n      numFullColumns = 1;\n      swiper.emitContainerClasses();\n    } else if (!wasMultiRow && isMultiRow) {\n      el.classList.add(`${params.containerModifierClass}grid`);\n      if (params.grid.fill === 'column') {\n        el.classList.add(`${params.containerModifierClass}grid-column`);\n      }\n      swiper.emitContainerClasses();\n    }\n    wasMultiRow = isMultiRow;\n  };\n  on('init', onInit);\n  on('update', onUpdate);\n  swiper.grid = {\n    initSlides,\n    updateSlide,\n    updateWrapperSize\n  };\n}\n\nexport { Grid as default };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,IAAI,EAAE;MACJC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,IAAIC,sBAAsB;EAC1B,IAAIC,YAAY;EAChB,IAAIC,cAAc;EAClB,IAAIC,WAAW;EACf,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,YAAY,GAAGX,MAAM,CAACY,MAAM,CAACD,YAAY;IAC7C,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACtEF,YAAY,GAAGG,UAAU,CAACH,YAAY,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGf,MAAM,CAACgB,IAAI;IAC9E,CAAC,MAAM,IAAI,OAAOL,YAAY,KAAK,QAAQ,EAAE;MAC3CA,YAAY,GAAGG,UAAU,CAACH,YAAY,CAAC;IACzC;IACA,OAAOA,YAAY;EACrB,CAAC;EACD,MAAMM,UAAU,GAAGC,YAAY,IAAI;IACjC,MAAM;MACJC;IACF,CAAC,GAAGnB,MAAM,CAACY,MAAM;IACjB,MAAM;MACJR,IAAI;MACJC;IACF,CAAC,GAAGL,MAAM,CAACY,MAAM,CAACT,IAAI;IACtBK,cAAc,GAAGY,IAAI,CAACC,KAAK,CAACH,YAAY,GAAGd,IAAI,CAAC;IAChD,IAAIgB,IAAI,CAACC,KAAK,CAACH,YAAY,GAAGd,IAAI,CAAC,KAAKc,YAAY,GAAGd,IAAI,EAAE;MAC3DE,sBAAsB,GAAGY,YAAY;IACvC,CAAC,MAAM;MACLZ,sBAAsB,GAAGc,IAAI,CAACE,IAAI,CAACJ,YAAY,GAAGd,IAAI,CAAC,GAAGA,IAAI;IAChE;IACA,IAAIe,aAAa,KAAK,MAAM,IAAId,IAAI,KAAK,KAAK,EAAE;MAC9CC,sBAAsB,GAAGc,IAAI,CAACG,GAAG,CAACjB,sBAAsB,EAAEa,aAAa,GAAGf,IAAI,CAAC;IACjF;IACAG,YAAY,GAAGD,sBAAsB,GAAGF,IAAI;EAC9C,CAAC;EACD,MAAMoB,WAAW,GAAGA,CAACC,CAAC,EAAEC,KAAK,EAAER,YAAY,EAAES,iBAAiB,KAAK;IACjE,MAAM;MACJC;IACF,CAAC,GAAG5B,MAAM,CAACY,MAAM;IACjB,MAAMD,YAAY,GAAGD,eAAe,CAAC,CAAC;IACtC,MAAM;MACJN,IAAI;MACJC;IACF,CAAC,GAAGL,MAAM,CAACY,MAAM,CAACT,IAAI;IACtB;IACA,IAAI0B,kBAAkB;IACtB,IAAIC,MAAM;IACV,IAAIC,GAAG;IACP,IAAI1B,IAAI,KAAK,KAAK,IAAIuB,cAAc,GAAG,CAAC,EAAE;MACxC,MAAMI,UAAU,GAAGZ,IAAI,CAACC,KAAK,CAACI,CAAC,IAAIG,cAAc,GAAGxB,IAAI,CAAC,CAAC;MAC1D,MAAM6B,iBAAiB,GAAGR,CAAC,GAAGrB,IAAI,GAAGwB,cAAc,GAAGI,UAAU;MAChE,MAAME,cAAc,GAAGF,UAAU,KAAK,CAAC,GAAGJ,cAAc,GAAGR,IAAI,CAACe,GAAG,CAACf,IAAI,CAACE,IAAI,CAAC,CAACJ,YAAY,GAAGc,UAAU,GAAG5B,IAAI,GAAGwB,cAAc,IAAIxB,IAAI,CAAC,EAAEwB,cAAc,CAAC;MAC1JG,GAAG,GAAGX,IAAI,CAACC,KAAK,CAACY,iBAAiB,GAAGC,cAAc,CAAC;MACpDJ,MAAM,GAAGG,iBAAiB,GAAGF,GAAG,GAAGG,cAAc,GAAGF,UAAU,GAAGJ,cAAc;MAC/EC,kBAAkB,GAAGC,MAAM,GAAGC,GAAG,GAAGzB,sBAAsB,GAAGF,IAAI;MACjEsB,KAAK,CAACU,KAAK,CAACC,KAAK,GAAGR,kBAAkB;IACxC,CAAC,MAAM,IAAIxB,IAAI,KAAK,QAAQ,EAAE;MAC5ByB,MAAM,GAAGV,IAAI,CAACC,KAAK,CAACI,CAAC,GAAGrB,IAAI,CAAC;MAC7B2B,GAAG,GAAGN,CAAC,GAAGK,MAAM,GAAG1B,IAAI;MACvB,IAAI0B,MAAM,GAAGtB,cAAc,IAAIsB,MAAM,KAAKtB,cAAc,IAAIuB,GAAG,KAAK3B,IAAI,GAAG,CAAC,EAAE;QAC5E2B,GAAG,IAAI,CAAC;QACR,IAAIA,GAAG,IAAI3B,IAAI,EAAE;UACf2B,GAAG,GAAG,CAAC;UACPD,MAAM,IAAI,CAAC;QACb;MACF;IACF,CAAC,MAAM;MACLC,GAAG,GAAGX,IAAI,CAACC,KAAK,CAACI,CAAC,GAAGlB,YAAY,CAAC;MAClCuB,MAAM,GAAGL,CAAC,GAAGM,GAAG,GAAGxB,YAAY;IACjC;IACAmB,KAAK,CAACK,GAAG,GAAGA,GAAG;IACfL,KAAK,CAACI,MAAM,GAAGA,MAAM;IACrBJ,KAAK,CAACU,KAAK,CAACT,iBAAiB,CAAC,YAAY,CAAC,CAAC,GAAGI,GAAG,KAAK,CAAC,GAAGpB,YAAY,IAAK,GAAEA,YAAa,IAAG,GAAG,EAAE;EACrG,CAAC;EACD,MAAM2B,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEb,iBAAiB,KAAK;IACpE,MAAM;MACJc,cAAc;MACdC;IACF,CAAC,GAAG1C,MAAM,CAACY,MAAM;IACjB,MAAMD,YAAY,GAAGD,eAAe,CAAC,CAAC;IACtC,MAAM;MACJN;IACF,CAAC,GAAGJ,MAAM,CAACY,MAAM,CAACT,IAAI;IACtBH,MAAM,CAAC2C,WAAW,GAAG,CAACJ,SAAS,GAAG5B,YAAY,IAAIL,sBAAsB;IACxEN,MAAM,CAAC2C,WAAW,GAAGvB,IAAI,CAACE,IAAI,CAACtB,MAAM,CAAC2C,WAAW,GAAGvC,IAAI,CAAC,GAAGO,YAAY;IACxEX,MAAM,CAAC4C,SAAS,CAACR,KAAK,CAACT,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAI,GAAE3B,MAAM,CAAC2C,WAAW,GAAGhC,YAAa,IAAG;IAC7F,IAAI8B,cAAc,EAAE;MAClB,MAAMI,aAAa,GAAG,EAAE;MACxB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,QAAQ,CAACM,MAAM,EAAErB,CAAC,IAAI,CAAC,EAAE;QAC3C,IAAIsB,cAAc,GAAGP,QAAQ,CAACf,CAAC,CAAC;QAChC,IAAIiB,YAAY,EAAEK,cAAc,GAAG3B,IAAI,CAACC,KAAK,CAAC0B,cAAc,CAAC;QAC7D,IAAIP,QAAQ,CAACf,CAAC,CAAC,GAAGzB,MAAM,CAAC2C,WAAW,GAAGH,QAAQ,CAAC,CAAC,CAAC,EAAEK,aAAa,CAACG,IAAI,CAACD,cAAc,CAAC;MACxF;MACAP,QAAQ,CAACS,MAAM,CAAC,CAAC,EAAET,QAAQ,CAACM,MAAM,CAAC;MACnCN,QAAQ,CAACQ,IAAI,CAAC,GAAGH,aAAa,CAAC;IACjC;EACF,CAAC;EACD,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnBzC,WAAW,GAAGT,MAAM,CAACY,MAAM,CAACT,IAAI,IAAIH,MAAM,CAACY,MAAM,CAACT,IAAI,CAACC,IAAI,GAAG,CAAC;EACjE,CAAC;EACD,MAAM+C,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAM;MACJvC,MAAM;MACNwC;IACF,CAAC,GAAGpD,MAAM;IACV,MAAMqD,UAAU,GAAGzC,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACC,IAAI,GAAG,CAAC;IACtD,IAAIK,WAAW,IAAI,CAAC4C,UAAU,EAAE;MAC9BD,EAAE,CAACE,SAAS,CAACC,MAAM,CAAE,GAAE3C,MAAM,CAAC4C,sBAAuB,MAAK,EAAG,GAAE5C,MAAM,CAAC4C,sBAAuB,aAAY,CAAC;MAC1GhD,cAAc,GAAG,CAAC;MAClBR,MAAM,CAACyD,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAI,CAAChD,WAAW,IAAI4C,UAAU,EAAE;MACrCD,EAAE,CAACE,SAAS,CAACI,GAAG,CAAE,GAAE9C,MAAM,CAAC4C,sBAAuB,MAAK,CAAC;MACxD,IAAI5C,MAAM,CAACT,IAAI,CAACE,IAAI,KAAK,QAAQ,EAAE;QACjC+C,EAAE,CAACE,SAAS,CAACI,GAAG,CAAE,GAAE9C,MAAM,CAAC4C,sBAAuB,aAAY,CAAC;MACjE;MACAxD,MAAM,CAACyD,oBAAoB,CAAC,CAAC;IAC/B;IACAhD,WAAW,GAAG4C,UAAU;EAC1B,CAAC;EACDnD,EAAE,CAAC,MAAM,EAAEgD,MAAM,CAAC;EAClBhD,EAAE,CAAC,QAAQ,EAAEiD,QAAQ,CAAC;EACtBnD,MAAM,CAACG,IAAI,GAAG;IACZc,UAAU;IACVO,WAAW;IACXc;EACF,CAAC;AACH;AAEA,SAASxC,IAAI,IAAI6D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}