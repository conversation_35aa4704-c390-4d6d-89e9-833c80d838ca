{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { routes } from './routes';\nimport { NotFoundComponent } from './components/not-found.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class NotFoundModule {\n  static ɵfac = function NotFoundModule_Factory(t) {\n    return new (t || NotFoundModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NotFoundModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), TranslateModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotFoundModule, {\n    declarations: [NotFoundComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "routes", "NotFoundComponent", "TranslateModule", "NotFoundModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\not-found\\not-found.module.ts"], "sourcesContent": ["import {CommonModule} from '@angular/common';\r\nimport {NgModule} from '@angular/core';\r\nimport {RouterModule} from '@angular/router';\r\n\r\nimport {routes} from './routes';\r\nimport {NotFoundComponent} from './components/not-found.component';\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\n\r\n@NgModule({\r\n  declarations: [\r\n    NotFoundComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild(routes),\r\n    TranslateModule\r\n  ]\r\n\r\n})\r\nexport class NotFoundModule {\r\n}\r\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,iBAAiB;AAE5C,SAAQC,YAAY,QAAO,iBAAiB;AAE5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,iBAAiB,QAAO,kCAAkC;AAClE,SAAQC,eAAe,QAAO,qBAAqB;;;AAanD,OAAM,MAAOC,cAAc;;qBAAdA,cAAc;EAAA;;UAAdA;EAAc;;cANvBL,YAAY,EACZC,YAAY,CAACK,QAAQ,CAACJ,MAAM,CAAC,EAC7BE,eAAe;EAAA;;;2EAINC,cAAc;IAAAE,YAAA,GATvBJ,iBAAiB;IAAAK,OAAA,GAGjBR,YAAY,EAAAS,EAAA,CAAAR,YAAA,EAEZG,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}