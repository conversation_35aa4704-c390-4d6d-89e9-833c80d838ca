{"ast": null, "code": "import { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class SignInUpHeaderComponent {\n  static #_ = this.ɵfac = function SignInUpHeaderComponent_Factory(t) {\n    return new (t || SignInUpHeaderComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SignInUpHeaderComponent,\n    selectors: [[\"sign-in-up-header\"]],\n    inputs: {\n      title: \"title\",\n      img: \"img\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 11,\n    vars: 7,\n    consts: [[1, \"sign-in-up-container\"], [1, \"sign-in-up-container__image\"], [\"alt\", \"login image\", 3, \"src\"], [1, \"sign-in-up-container__content\"], [1, \"sign-in-up-container__content--title\"], [1, \"signup-heading\"], [1, \"signup-desc\"]],\n    template: function SignInUpHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"img\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"src\", ctx.img, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 3, ctx.title));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 5, \"signIn.signInText\"), \"\");\n      }\n    },\n    dependencies: [TranslateModule, i1.TranslatePipe],\n    styles: [\".sign-in-up-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  margin: 20px 0;\\n}\\n.sign-in-up-container__image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 90px;\\n  height: 90px;\\n}\\n.sign-in-up-container__content--title[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n}\\n.sign-in-up-container__content--title[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #212121;\\n  margin-bottom: 8px;\\n}\\n.sign-in-up-container__content--title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #443F3F;\\n  line-height: 18px;\\n  font-weight: 500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvc2lnbi1pbi11cC1oZWFkZXIvc2lnbi1pbi11cC1oZWFkZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxjQUFBO0FBQ0Y7QUFDSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0FBQ047QUFJSTtFQUNFLGFBQUE7RUFDRixzQkFBQTtFQUNBLHVCQUFBO0FBRko7QUFHTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQURSO0FBSU07RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFGUiIsInNvdXJjZXNDb250ZW50IjpbIi5zaWduLWluLXVwLWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGdhcDogMTBweDtcclxuICBtYXJnaW46IDIwcHggMDtcclxuICAmX19pbWFnZSB7XHJcbiAgICBpbWcge1xyXG4gICAgICB3aWR0aDogOTBweDtcclxuICAgICAgaGVpZ2h0OiA5MHB4O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgJl9fY29udGVudCB7XHJcbiAgICAmLS10aXRsZSB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgIGg0IHtcclxuICAgICAgICBmb250LXNpemU6IDE4cHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICBjb2xvcjogIzIxMjEyMTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHAge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICBjb2xvcjogIzQ0M0YzRjtcclxuICAgICAgICBsaW5lLWhlaWdodDogMThweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["TranslateModule", "SignInUpHeaderComponent", "_", "_2", "selectors", "inputs", "title", "img", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SignInUpHeaderComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate1", "i1", "TranslatePipe", "styles"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\sign-in-up-header\\sign-in-up-header.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\sign-in-up-header\\sign-in-up-header.component.html"], "sourcesContent": ["import {Component, Input} from '@angular/core';\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\n\r\n@Component({\r\n  selector: 'sign-in-up-header',\r\n  standalone: true,\r\n  imports: [\r\n    TranslateModule\r\n  ],\r\n  templateUrl: './sign-in-up-header.component.html',\r\n  styleUrls: ['./sign-in-up-header.component.scss']\r\n})\r\nexport class SignInUpHeaderComponent {\r\n  @Input() title: string;\r\n  @Input() img: string;\r\n}\r\n", "<div class=\"sign-in-up-container\">\r\n  <div class=\"sign-in-up-container__image\">\r\n    <img [src]=\"img\" alt=\"login image\">\r\n  </div>\r\n  <div class=\"sign-in-up-container__content\">\r\n    <div class=\"sign-in-up-container__content--title\">\r\n      <h4 class=\"signup-heading\">{{ title | translate}}</h4>\r\n      <p class=\"signup-desc\"> {{ \"signIn.signInText\" | translate}}</p>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAQA,eAAe,QAAO,qBAAqB;;;AAWnD,OAAM,MAAOC,uBAAuB;EAAA,QAAAC,CAAA,G;qBAAvBD,uBAAuB;EAAA;EAAA,QAAAE,EAAA,G;UAAvBF,uBAAuB;IAAAG,SAAA;IAAAC,MAAA;MAAAC,KAAA;MAAAC,GAAA;IAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZpCP,EAAA,CAAAS,cAAA,aAAkC;QAE9BT,EAAA,CAAAU,SAAA,aAAmC;QACrCV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,aAA2C;QAEZT,EAAA,CAAAY,MAAA,GAAsB;;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACtDX,EAAA,CAAAS,cAAA,WAAuB;QAACT,EAAA,CAAAY,MAAA,GAAoC;;QAAAZ,EAAA,CAAAW,YAAA,EAAI;;;QAL7DX,EAAA,CAAAa,SAAA,GAAW;QAAXb,EAAA,CAAAc,UAAA,QAAAN,GAAA,CAAAX,GAAA,EAAAG,EAAA,CAAAe,aAAA,CAAW;QAIaf,EAAA,CAAAa,SAAA,GAAsB;QAAtBb,EAAA,CAAAgB,iBAAA,CAAAhB,EAAA,CAAAiB,WAAA,OAAAT,GAAA,CAAAZ,KAAA,EAAsB;QACzBI,EAAA,CAAAa,SAAA,GAAoC;QAApCb,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAiB,WAAA,iCAAoC;;;mBDA9D3B,eAAe,EAAA6B,EAAA,CAAAC,aAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}