import {Component, HostListener, Inject, Input, OnInit, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import {PriceEstimation} from '@core/interface';
import {StoreService, ShipmentService, AddressService, MainDataService, PermissionService, OrderService} from '@core/services';
import {isPlatformBrowser} from "@angular/common";
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-order-summary-cart',
  templateUrl: './order-summary-cart.component.html',
  styleUrls: ['./order-summary-cart.component.scss']
})
export class OrderSummaryCartComponent implements OnInit {
  @Input() deliveryOptionDetails : any;
  @Input() refreshSummary: Subject<void>;
  private refreshSubscription: Subscription;

  address: string = "";
  itemsTotalPrices: number = 0;
  discount: any = 0;
  countryCode: string = "";
  shopLat: any;
  shopLng: any;
  req: any;
  calledGetAddress: boolean = false;
  shipmentDetails = new Array<any>();
  minTimeDelivery: number = Number.MAX_SAFE_INTEGER;
  maxTimeDelivery: number = -1;
  visitedShops: any = [];
  decimalValue: number = 0;
  currencyCode: string = '';
  currency?: string = '';
  disableCent: any;
  isLayoutTemplate: boolean = false;
  isMobileTemplete: boolean = false;
  isShipmentFee: boolean = false;
  orderDiscount: number = 0;
  orderDetails: any;
  // Tab-specific properties to prevent cross-tab contamination
  private readonly tabId: string;
  private sessionOrderId: string = '';

  constructor(private store: StoreService,
              public shipmentService: ShipmentService, public addressService: AddressService,
              private route: Router,
              private permissionService :PermissionService,
              private mainDataService: MainDataService,
              private orderService: OrderService,
              @Inject(PLATFORM_ID) private platformId: any,

  ) {
    // Generate unique tab identifier
    this.tabId = this.generateTabId();
    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')
    this.isMobileTemplete = this.permissionService.hasPermission('Mobile-Layout')
    this.isShipmentFee = this.permissionService.hasPermission('Shipment-Fee')
    this.disableCent = localStorage.getItem('DisableCents');
    this.req = new PriceEstimation();
    let value = localStorage.getItem('CurrencyDecimal');
    value = value?value:'2'
    if (value)
      this.decimalValue = parseInt(value);
  }
   skipNextRefresh = false;

  screenWidth:any=window.innerWidth;
  @HostListener('window:resize', ['$event'])
  onResize(event?: any) {
    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth
    }
  }

  ngOnInit(): void {
    this.currency = localStorage.getItem('currency')?.toString();
    this.shipmentService.shipmentCost= 0;
    this.getCartId();

    this.store.set('shipmentCost', "");

    this.getOrderData();

    this.refreshSubscription = this.refreshSummary.subscribe(() => {
      if (this.skipNextRefresh) {
        this.skipNextRefresh = false; // reset it
        return;
      }
  
      // Use tab-specific OrderId to prevent cross-tab contamination
      const correctOrderId = this.getCorrectOrderId();
      this.getOrderDiscount(parseInt(correctOrderId));
    });
  
    this.orderService.discountReset$.subscribe(() => {
      this.skipNextRefresh = true;     //  skip the next refresh logic
      this.orderDiscount = 0;
      this.refreshSummary.next();      // still emits, but logic inside is skipped once
    });
  }

  getCartId() {
    this.mainDataService.getCartItemsData()
      .subscribe({
        next: (res: any) => {

          if (res.length > 0) {
            this.itemsTotalPrices = 0;
            this.discount=0;
            res.forEach((element: any) => {
              if(element.salePriceValue){
                this.itemsTotalPrices +=element.salePriceValue * element.quantity;
                this.discount += (element.price - element.salePriceValue) * element.quantity;
                this.discount = parseFloat(this.discount.toFixed(2));
              }
              else{
                this.itemsTotalPrices +=element.price * element.quantity;
              }
            });
            this.shipmentService.currentOrderTotal = this.itemsTotalPrices;
            this.currencyCode = res[0].currencyCode;
          } else {
            this.currencyCode = this.currency ?? '';
          }
        },
        error: (err: any) => {
          console.error(err);
        }
      });
  }

  routeAddress() {
    this.route.navigate(['/account/address'], {
      state: {
        checkout: true,
      }
    });
  };

  routeUpdateAddress() {
    this.route.navigate(['/account/address'], {
      state: {
        checkout: true,
        data: this.addressService.chosenAddress
      }
    });

  };

  /**
   * Generate a unique tab identifier
   */
  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Store OrderId in session storage with tab-specific key
   */
  private storeTabSpecificOrderId(orderId: string): void {
    if (isPlatformBrowser(this.platformId)) {
      sessionStorage.setItem(`orderData_${this.tabId}`, JSON.stringify({
        orderId: orderId,
        timestamp: Date.now()
      }));
      this.sessionOrderId = orderId;
    }
  }

  /**
   * Retrieve OrderId from session storage for this specific tab
   */
  private getTabSpecificOrderId(): string | null {
    if (isPlatformBrowser(this.platformId)) {
      const storedData = sessionStorage.getItem(`orderData_${this.tabId}`);
      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          return parsed.orderId;
        } catch (e) {
          console.error('Error parsing tab-specific order data:', e);
        }
      }
    }
    return null;
  }

  /**
   * Get the correct OrderId for this tab, with fallback chain
   */
  private getCorrectOrderId(): string {
    return this.getTabSpecificOrderId() || this.sessionOrderId || this.orderDetails?.orderId;
  }

  async getOrderData() {
    this.store.subscription('orderData').subscribe({
      next: (res: any) => {
        if (res) {
          this.orderDetails = res;
          // Store the OrderId for this specific tab session
          if (res.orderId) {
            this.storeTabSpecificOrderId(res.orderId);
          }
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  getOrderDiscount (id: number) {
    this.orderService.getOrderDiscount(id).subscribe({
      next: (res) => {
        if (res.success) {
          this.orderDiscount = res.data >= this.itemsTotalPrices ? this.itemsTotalPrices : res.data ;
          this.store.subscription('shipmentCost').subscribe({
            next: (shipmentData: any) => {
              if (shipmentData?.isApplyShippingFeeDiscount !== null && shipmentData?.isApplyShippingFeeDiscount) {
                 this.orderDiscount = res.data
              }
            }
          });
          this.shipmentService.currentOrderTotal = this.itemsTotalPrices;
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  ngOnDestroy() {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }
}
