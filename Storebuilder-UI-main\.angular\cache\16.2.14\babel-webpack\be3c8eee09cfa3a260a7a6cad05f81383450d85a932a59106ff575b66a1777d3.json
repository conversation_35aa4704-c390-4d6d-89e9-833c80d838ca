{"ast": null, "code": "import { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"ngx-cookie-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-google-analytics\";\nimport * as i5 from \"primeng/badge\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ngx-translate/core\";\nfunction MobileNavbarComponent_div_0_div_1_p_badge_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r3.cartListCount);\n  }\n}\nfunction MobileNavbarComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function MobileNavbarComponent_div_0_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const menu_r2 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateFloating(menu_r2));\n    });\n    i0.ɵɵelement(1, \"img\", 4);\n    i0.ɵɵtemplate(2, MobileNavbarComponent_div_0_div_1_p_badge_2_Template, 1, 1, \"p-badge\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const menu_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r1.renderMenuFilledIcon(menu_r2.name) ? menu_r2.filledIcon : menu_r2.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", menu_r2.name === \"navbar.Cart\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.renderMenuFilledIcon(menu_r2.name) ? \"mobile-navbar__navs__name-filled\" : \"mobile-navbar__navs__name\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, menu_r2.name));\n  }\n}\nfunction MobileNavbarComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, MobileNavbarComponent_div_0_div_1_Template, 6, 6, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.floatingMenu);\n  }\n}\nexport let MobileNavbarComponent = /*#__PURE__*/(() => {\n  class MobileNavbarComponent {\n    authTokenService;\n    cookieService;\n    router;\n    mainDataService;\n    cd;\n    commonService;\n    $gaService;\n    permissionService;\n    store;\n    customGAService;\n    authToken = '';\n    floatingMenu;\n    cartListCount = 0;\n    isMobileTemplate = false;\n    isGoogleAnalytics = false;\n    selectedMenu;\n    userDetails;\n    constructor(authTokenService, cookieService, router, mainDataService, cd, commonService, $gaService, permissionService, store, customGAService) {\n      this.authTokenService = authTokenService;\n      this.cookieService = cookieService;\n      this.router = router;\n      this.mainDataService = mainDataService;\n      this.cd = cd;\n      this.commonService = commonService;\n      this.$gaService = $gaService;\n      this.permissionService = permissionService;\n      this.store = store;\n      this.customGAService = customGAService;\n      this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    }\n    ngOnInit() {\n      this.userDetails = this.store.get('profile');\n      this.mainDataService.getCartLengthData().subscribe(data => {\n        this.cartListCount = data ?? 0;\n        if (this.cartListCount > 0) {\n          if (this.cartListCount > 9) {\n            this.cartListCount = '9+';\n          }\n        }\n        this.cd.markForCheck();\n        this.cd.detectChanges();\n      });\n      this.floatingMenu = [{\n        name: 'navbar.home',\n        icon: 'assets/icons/mobile-home.svg',\n        filledIcon: 'assets/icons/mobile-home-filled.svg'\n      }, {\n        name: 'navbar.Wishlist',\n        icon: 'assets/icons/mobile-wishlist.svg',\n        filledIcon: 'assets/icons/mobile-wishlist-filled.svg'\n      }, {\n        name: 'navbar.Categories',\n        icon: 'assets/icons/mobile-categories.svg',\n        filledIcon: 'assets/icons/mobile-categories-filled.svg'\n      }, {\n        name: 'navbar.Cart',\n        icon: 'assets/icons/mobile-cart.svg',\n        filledIcon: 'assets/icons/mobile-cart-flled.svg'\n      }, {\n        name: 'navbar.Profile',\n        icon: 'assets/icons/mobile-profile.svg',\n        filledIcon: 'assets/icons/mobile-profile-filled.svg'\n      }];\n      this.selectedMenu = this.floatingMenu[0];\n      this.commonService.isShowSearchBox.next(true);\n      this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n      if (!this.authToken) {\n        this.authToken = this.cookieService.get('authToken');\n      }\n    }\n    navigateFloating(menu) {\n      const tempSelectedMenu = this.floatingMenu.find(m => m.name === menu.name) ?? null;\n      if (menu.name === 'navbar.Profile') {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_IN_UP, '', 'SIGNIN/SIGNUP', 1, true, {\n            \"user_ID\": this.userDetails ? this.userDetails?.mobileNumber : 'Un_Authenticated',\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId\n          });\n          this.customGAService.signupCtaClickEvent('mobile_navbar');\n        }\n        if (this.authToken) this.router.navigate(['/account']);else this.router.navigate(['/login']);\n      } else if (menu.name === 'navbar.home') {\n        this.router.navigate(['/']);\n      } else if (menu.name === 'navbar.Wishlist') {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_WISHLIST_FOOTER, '', '', 1, true, {\n            \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId\n          });\n        }\n        if (this.authToken) this.router.navigate(['/wishlist']);else this.router.navigate(['/login']);\n      } else if (menu.name === 'navbar.Cart') {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_CART_ICON, 'navigation', 'CART_ON_TOP_BANNER', 1, true, {\n            \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n            \"session_ID\": localStorage.getItem('sessionId')\n          });\n        }\n        this.router.navigate(['/cart']);\n      } else if (menu.name === 'navbar.Categories') {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true, {\n            \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId\n          });\n        }\n        this.router.navigate(['/categories-list']);\n      }\n      if (tempSelectedMenu?.name === this.selectedMenu?.name) {\n        this.commonService.isShowSearchBox.next(true);\n      } else {\n        this.commonService.isShowSearchBox.next(false);\n      }\n      this.selectedMenu = tempSelectedMenu;\n    }\n    renderMenuFilledIcon(menu) {\n      const tempSelectedMenu = this.floatingMenu.find(m => m.name === menu) ?? null;\n      if (tempSelectedMenu) {\n        if (tempSelectedMenu.name === 'navbar.Profile') {\n          return this.router.url.includes('login') || this.router.url.includes('account');\n        } else if (tempSelectedMenu.name === 'navbar.home') {\n          return this.router.url === '/';\n        } else if (tempSelectedMenu.name === 'navbar.Wishlist') {\n          return this.router.url.includes('/wishlist');\n        } else if (tempSelectedMenu.name === 'navbar.Cart') {\n          return this.router.url === '/cart';\n        } else if (tempSelectedMenu.name === 'navbar.Categories') {\n          return this.router.url === '/categories-list';\n        }\n      }\n    }\n    static ɵfac = function MobileNavbarComponent_Factory(t) {\n      return new (t || MobileNavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i2.CookieService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i4.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MobileNavbarComponent,\n      selectors: [[\"app-mobile-navbar\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"d-flex mobile-navbar\", 4, \"ngIf\"], [1, \"d-flex\", \"mobile-navbar\"], [\"class\", \"mobile-navbar__navs\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"mobile-navbar__navs\", 3, \"click\"], [\"alt\", \"No Image\", 3, \"src\"], [\"class\", \"mobile-navbar__navs__badge\", 3, \"value\", 4, \"ngIf\"], [1, \"text-capitalize\", 3, \"ngClass\"], [1, \"mobile-navbar__navs__badge\", 3, \"value\"]],\n      template: function MobileNavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MobileNavbarComponent_div_0_Template, 2, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate);\n        }\n      },\n      dependencies: [i5.Badge, i6.NgClass, i6.NgForOf, i6.NgIf, i7.TranslatePipe],\n      styles: [\".mobile-navbar[_ngcontent-%COMP%]{position:fixed;bottom:0;width:100%;display:flex;padding:8px 25px;justify-content:space-between;align-items:flex-start;background:#204E6E;box-shadow:0 -2px 8px #00000040;z-index:1000}.mobile-navbar__navs[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:6px;padding:8px 0;position:relative}.mobile-navbar__navs[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:22px;height:22px;flex-shrink:0}.mobile-navbar__navs__name[_ngcontent-%COMP%]{color:#fff;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:100%}.mobile-navbar__navs__badge[_ngcontent-%COMP%]{position:absolute;top:-2px;right:-8px;transform:translate(50%)}.mobile-navbar__navs__name-filled[_ngcontent-%COMP%]{color:#ffcb05;font-family:var(--regular-font);font-size:12px;font-style:normal;font-weight:400;line-height:100%}  .p-badge{font-size:.7rem;min-width:1.2rem;height:1.2rem;line-height:1.2rem;background:#ffcc00;color:#000;padding:0 .3rem!important;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:600;border:2px solid #204E6E}\"]\n    });\n  }\n  return MobileNavbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}