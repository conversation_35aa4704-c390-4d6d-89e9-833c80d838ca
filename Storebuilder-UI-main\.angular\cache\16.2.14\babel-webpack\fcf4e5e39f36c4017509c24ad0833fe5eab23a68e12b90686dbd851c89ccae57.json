{"ast": null, "code": "export class ContactInfo {\n  constructor() {\n    this.fullName = \"\";\n    this.phoneNumber = \"\";\n    this.countryCode = \"\";\n    this.email = \"\";\n    this.gender = \"\";\n    this.description = \"\";\n    this.addressLatLng = \"\";\n    this.addressLabel = \"\";\n    this.city = \"cairo\";\n    this.building = \"\";\n    this.plotNumber = \"\";\n  }\n}\nexport class RequestDelivery {\n  constructor() {\n    this.action = \"\";\n    this.countryCode = \"\";\n    this.vehicleType = \"DELIVERY_BIKE\";\n    this.paymentMode = \"MOBILE_WALLET\";\n    this.pickupContactInfo = new ContactInfo();\n    this.dropOffContactInfo = new ContactInfo();\n  }\n}\nexport class PriceEstimation {\n  constructor() {\n    this.action = \"\";\n    this.countryCode = \"\";\n    this.origin = \"\";\n    this.destination = \"\";\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}