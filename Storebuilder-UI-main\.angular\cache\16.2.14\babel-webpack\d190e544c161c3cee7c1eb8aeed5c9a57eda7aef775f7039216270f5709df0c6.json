{"ast": null, "code": "import { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"primeng/dialog\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-intl-tel-input-gg\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/modals/ineligable-purchase-modal/ineligable-purchase-modal.component\";\nfunction AgeConsentModalComponent_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", day_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(day_r4);\n  }\n}\nfunction AgeConsentModalComponent_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const month_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", month_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(month_r5);\n  }\n}\nfunction AgeConsentModalComponent_option_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const year_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", year_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(year_r6);\n  }\n}\nfunction AgeConsentModalComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 22);\n    i0.ɵɵelement(3, \"path\", 23)(4, \"path\", 24)(5, \"path\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\", 50)(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 1, \"ageConsentModal.errorLabel\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"768px\": \"92vw\"\n  };\n};\nexport class AgeConsentModalComponent {\n  constructor(translate, platformId) {\n    this.translate = translate;\n    this.platformId = platformId;\n    this.displayModal = false;\n    this.age = 10;\n    this.eligibilityWarningLabel = '';\n    this.submit = new EventEmitter();\n    this.cancel = new EventEmitter();\n    this.currentYear = new Date().getFullYear();\n    this.days = Array.from({\n      length: 31\n    }, (_, i) => i + 1);\n    this.months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n    this.years = Array.from({\n      length: 101\n    }, (_, i) => this.currentYear - i);\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  ngOnInit() {\n    this.selectedDay = this.days[0];\n    this.selectedMonth = this.months[0];\n    this.selectedYear = this.years[0];\n    this.productAllowed = false;\n    this.enableProceedButton = false;\n    this.showAgeErrorMessage = false;\n  }\n  getDaysInMonth() {\n    const monthIndex = this.months.indexOf(this.selectedMonth);\n    const year = this.selectedYear;\n    const daysInMonth = new Date(year, monthIndex + 1, 0).getDate(); // Get number of days in the month\n    this.days = Array.from({\n      length: daysInMonth\n    }, (_, i) => i + 1); // Update day options\n    if (this.selectedDay > daysInMonth) {\n      this.selectedDay = daysInMonth; // Adjust selected day if it's invalid\n    }\n  }\n\n  onMonthOrYearChange() {\n    this.getDaysInMonth();\n    this.validateDoB();\n  }\n  validateDoB() {\n    let selectedDate = new Date(this.selectedYear, this.months.indexOf(this.selectedMonth), this.selectedDay); // Selected date\n    let today = new Date(); // Current date\n    // Calculate the age\n    let calculatedAge = today.getFullYear() - selectedDate.getFullYear();\n    // Adjust age if the current date is before the birthday this year\n    if (today.getMonth() < selectedDate.getMonth() || today.getMonth() === selectedDate.getMonth() && today.getDate() < selectedDate.getDate()) {\n      calculatedAge--;\n    }\n    if (calculatedAge > this.age) {\n      this.ageEligible = true;\n    } else this.ageEligible = false;\n    if (this.screenWidth > 786) this.showAgeErrorMessage = !this.ageEligible;else this.displayEligableModal = !this.ageEligible;\n    this.enableProceedButton = this.ageEligible && this.termsAndCondition;\n    // this.showAgeErrorMessage = this.ageEligible && this.termsAndCondition;\n  }\n\n  loadTermsAndConditions(title) {\n    const pageId = localStorage.getItem('TermsAndConditionsId') ?? '';\n    if (pageId) {\n      const queryParams = new URLSearchParams({\n        pageId: pageId,\n        title: title\n      }).toString();\n      const url = `/about-us/?${queryParams}`;\n      window.open(url, '_blank');\n    }\n  }\n  onSumbit() {\n    this.submit.emit(new Date(this.selectedYear, this.months.indexOf(this.selectedMonth), this.selectedDay));\n  }\n  onCancel() {\n    this.cancel.emit();\n  }\n  closeEligableModal() {\n    this.displayEligableModal = false;\n  }\n  static #_ = this.ɵfac = function AgeConsentModalComponent_Factory(t) {\n    return new (t || AgeConsentModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AgeConsentModalComponent,\n    selectors: [[\"app-age-consent-modal\"]],\n    hostBindings: function AgeConsentModalComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function AgeConsentModalComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      displayModal: \"displayModal\",\n      age: \"age\",\n      eligibilityWarningLabel: \"eligibilityWarningLabel\"\n    },\n    outputs: {\n      submit: \"submit\",\n      cancel: \"cancel\"\n    },\n    decls: 74,\n    vars: 22,\n    consts: [[1, \"age-consent\", 3, \"visible\", \"breakpoints\", \"resizable\", \"closable\", \"modal\", \"header\", \"baseZIndex\", \"visibleChange\"], [1, \"age-modal\"], [1, \"age-header\"], [1, \"age-data\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"120\", \"height\", \"120\", \"viewBox\", \"0 0 120 120\", \"fill\", \"none\"], [\"filter\", \"url(#filter0_d_52348_42654)\"], [\"cx\", \"60\", \"cy\", \"56\", \"r\", \"48\", \"fill\", \"white\"], [\"cx\", \"60.0001\", \"cy\", \"56.0001\", \"r\", \"45.0783\", \"fill\", \"#F2F2F2\"], [\"id\", \"filter0_d_52348_42654\", \"x\", \"0\", \"y\", \"0\", \"width\", \"120\", \"height\", \"120\", \"filterUnits\", \"userSpaceOnUse\", \"color-interpolation-filters\", \"sRGB\"], [\"flood-opacity\", \"0\", \"result\", \"BackgroundImageFix\"], [\"in\", \"SourceAlpha\", \"type\", \"matrix\", \"values\", \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\", \"result\", \"hardAlpha\"], [\"dy\", \"4\"], [\"stdDeviation\", \"6\"], [\"in2\", \"hardAlpha\", \"operator\", \"out\"], [\"type\", \"matrix\", \"values\", \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"], [\"mode\", \"normal\", \"in2\", \"BackgroundImageFix\", \"result\", \"effect1_dropShadow_52348_42654\"], [\"mode\", \"normal\", \"in\", \"SourceGraphic\", \"in2\", \"effect1_dropShadow_52348_42654\", \"result\", \"shape\"], [1, \"age-value\"], [1, \"age-text\"], [1, \"heading\"], [1, \"warning-lable\"], [1, \"warning-icon\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\", \"stroke\", \"#191C1F\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M11.25 11.25H12V16.5H12.75\", \"stroke\", \"#191C1F\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M11.8125 9C12.4338 9 12.9375 8.49632 12.9375 7.875C12.9375 7.25368 12.4338 6.75 11.8125 6.75C11.1912 6.75 10.6875 7.25368 10.6875 7.875C10.6875 8.49632 11.1912 9 11.8125 9Z\", \"fill\", \"#191C1F\"], [1, \"warning-text\"], [2, \"width\", \"100%\"], [\"id\", \"body-text\"], [1, \"dropdown-select\"], [1, \"dropdowns\"], [1, \"col-flex\"], [1, \"dropdown-label\"], [3, \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 16 16\", \"fill\", \"none\"], [\"d\", \"M12.6668 2.66732L11.3335 2.66732V2.00065C11.3335 1.82384 11.2633 1.65427 11.1382 1.52925C11.0132 1.40422 10.8436 1.33398 10.6668 1.33398C10.49 1.33398 10.3204 1.40422 10.1954 1.52925C10.0704 1.65427 10.0002 1.82384 10.0002 2.00065V2.66732L6.00016 2.66732V2.00065C6.00016 1.82384 5.92992 1.65427 5.8049 1.52925C5.67988 1.40422 5.51031 1.33398 5.3335 1.33398C5.15669 1.33398 4.98712 1.40422 4.86209 1.52925C4.73707 1.65427 4.66683 1.82384 4.66683 2.00065V2.66732L3.3335 2.66732C2.80306 2.66732 2.29436 2.87803 1.91928 3.2531C1.54421 3.62818 1.3335 4.13688 1.3335 4.66732L1.3335 12.6673C1.3335 13.1978 1.54421 13.7065 1.91928 14.0815C2.29436 14.4566 2.80306 14.6673 3.3335 14.6673L12.6668 14.6673C13.1973 14.6673 13.706 14.4566 14.081 14.0815C14.4561 13.7065 14.6668 13.1978 14.6668 12.6673L14.6668 4.66732C14.6668 4.13688 14.4561 3.62818 14.081 3.2531C13.706 2.87803 13.1973 2.66732 12.6668 2.66732ZM13.3335 12.6673C13.3335 12.8441 13.2633 13.0137 13.1382 13.1387C13.0132 13.2637 12.8436 13.334 12.6668 13.334L3.3335 13.334C3.15669 13.334 2.98712 13.2637 2.86209 13.1387C2.73707 13.0137 2.66683 12.8441 2.66683 12.6673V8.00065H13.3335L13.3335 12.6673ZM13.3335 6.66732L2.66683 6.66732L2.66683 4.66732C2.66683 4.49051 2.73707 4.32094 2.86209 4.19591C2.98712 4.07089 3.15669 4.00065 3.3335 4.00065H4.66683V4.66732C4.66683 4.84413 4.73707 5.0137 4.86209 5.13872C4.98712 5.26375 5.15669 5.33398 5.3335 5.33398C5.51031 5.33398 5.67988 5.26375 5.8049 5.13872C5.92992 5.0137 6.00016 4.84413 6.00016 4.66732V4.00065L10.0002 4.00065V4.66732C10.0002 4.84413 10.0704 5.0137 10.1954 5.13872C10.3204 5.26375 10.49 5.33398 10.6668 5.33398C10.8436 5.33398 11.0132 5.26375 11.1382 5.13872C11.2633 5.0137 11.3335 4.84413 11.3335 4.66732V4.00065L12.6668 4.00065C12.8436 4.00065 13.0132 4.07089 13.1382 4.19591C13.2633 4.32094 13.3335 4.49051 13.3335 4.66732V6.66732Z\", \"fill\", \"#030303\"], [1, \"termsAndCondition\"], [1, \"termsAndCondition-checkbox\"], [\"type\", \"checkbox\", 1, \"checkBox\", 3, \"ngModel\", \"change\", \"ngModelChange\"], [1, \"termsAndCondition-text\"], [1, \"termsAndCondition-link\", 3, \"click\"], [\"class\", \"error-lable\", 4, \"ngIf\"], [1, \"footer\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", 1, \"cancel-btn\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Proceed\", 1, \"proceed-btn\", 3, \"disabled\", \"click\"], [3, \"displayModal\", \"cancel\"], [3, \"value\"], [1, \"error-lable\"], [2, \"width\", \"24px\", \"height\", \"24px\"], [1, \"error-text\"]],\n    template: function AgeConsentModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function AgeConsentModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 4)(5, \"g\", 5);\n        i0.ɵɵelement(6, \"circle\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(7, \"circle\", 7);\n        i0.ɵɵelementStart(8, \"defs\")(9, \"filter\", 8);\n        i0.ɵɵelement(10, \"feFlood\", 9)(11, \"feColorMatrix\", 10)(12, \"feOffset\", 11)(13, \"feGaussianBlur\", 12)(14, \"feComposite\", 13)(15, \"feColorMatrix\", 14)(16, \"feBlend\", 15)(17, \"feBlend\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(18, \"div\", 17)(19, \"span\", 18);\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(21, \"h1\", 19);\n        i0.ɵɵtext(22, \" Age verification \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 20)(24, \"div\", 21);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(25, \"svg\", 22);\n        i0.ɵɵelement(26, \"path\", 23)(27, \"path\", 24)(28, \"path\", 25);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(29, \"div\", 26)(30, \"p\");\n        i0.ɵɵtext(31);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(32, \"div\", 27)(33, \"p\", 28);\n        i0.ɵɵtext(34, \" Please verify your age to enter. \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"div\", 29)(36, \"div\", 30)(37, \"div\", 31)(38, \"label\", 32);\n        i0.ɵɵtext(39, \" Day* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"select\", 33);\n        i0.ɵɵlistener(\"ngModelChange\", function AgeConsentModalComponent_Template_select_ngModelChange_40_listener($event) {\n          return ctx.selectedDay = $event;\n        })(\"change\", function AgeConsentModalComponent_Template_select_change_40_listener() {\n          return ctx.validateDoB();\n        });\n        i0.ɵɵtemplate(41, AgeConsentModalComponent_option_41_Template, 2, 2, \"option\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(42, \"svg\", 35);\n        i0.ɵɵelement(43, \"path\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(44, \"div\", 30)(45, \"div\", 31)(46, \"label\", 32);\n        i0.ɵɵtext(47, \" Month* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"select\", 33);\n        i0.ɵɵlistener(\"ngModelChange\", function AgeConsentModalComponent_Template_select_ngModelChange_48_listener($event) {\n          return ctx.selectedMonth = $event;\n        })(\"change\", function AgeConsentModalComponent_Template_select_change_48_listener() {\n          return ctx.onMonthOrYearChange();\n        });\n        i0.ɵɵtemplate(49, AgeConsentModalComponent_option_49_Template, 2, 2, \"option\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(50, \"svg\", 35);\n        i0.ɵɵelement(51, \"path\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(52, \"div\", 30)(53, \"div\", 31)(54, \"label\", 32);\n        i0.ɵɵtext(55, \" Year* \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"select\", 33);\n        i0.ɵɵlistener(\"ngModelChange\", function AgeConsentModalComponent_Template_select_ngModelChange_56_listener($event) {\n          return ctx.selectedYear = $event;\n        })(\"change\", function AgeConsentModalComponent_Template_select_change_56_listener() {\n          return ctx.onMonthOrYearChange();\n        });\n        i0.ɵɵtemplate(57, AgeConsentModalComponent_option_57_Template, 2, 2, \"option\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(58, \"svg\", 35);\n        i0.ɵɵelement(59, \"path\", 36);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(60, \"div\", 37)(61, \"div\", 38)(62, \"input\", 39);\n        i0.ɵɵlistener(\"change\", function AgeConsentModalComponent_Template_input_change_62_listener() {\n          return ctx.validateDoB();\n        })(\"ngModelChange\", function AgeConsentModalComponent_Template_input_ngModelChange_62_listener($event) {\n          return ctx.termsAndCondition = $event;\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(63, \"div\", 40)(64, \"p\");\n        i0.ɵɵtext(65, \"I agree all \\u00A0\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"div\", 41);\n        i0.ɵɵlistener(\"click\", function AgeConsentModalComponent_Template_div_click_66_listener() {\n          return ctx.loadTermsAndConditions(\"Terms and Conditions\");\n        });\n        i0.ɵɵtext(67);\n        i0.ɵɵpipe(68, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(69, AgeConsentModalComponent_div_69_Template, 10, 3, \"div\", 42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 43)(71, \"button\", 44);\n        i0.ɵɵlistener(\"click\", function AgeConsentModalComponent_Template_button_click_71_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"button\", 45);\n        i0.ɵɵlistener(\"click\", function AgeConsentModalComponent_Template_button_click_72_listener() {\n          return ctx.onSumbit();\n        });\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(73, \"app-ineligable-purchase-modal\", 46);\n        i0.ɵɵlistener(\"cancel\", function AgeConsentModalComponent_Template_app_ineligable_purchase_modal_cancel_73_listener() {\n          return ctx.closeEligableModal();\n        });\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(21, _c0))(\"resizable\", false)(\"closable\", false)(\"modal\", true)(\"baseZIndex\", 9999);\n        i0.ɵɵadvance(20);\n        i0.ɵɵtextInterpolate1(\"\", ctx.age, \"+\");\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate(ctx.eligibilityWarningLabel);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedDay);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.days);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedMonth);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.months);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedYear);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.years);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.termsAndCondition);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(68, 19, \"footer.termsAndConditions\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showAgeErrorMessage);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", !ctx.enableProceedButton);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"displayModal\", ctx.displayEligableModal);\n      }\n    },\n    dependencies: [i2.Dialog, i3.ButtonDirective, i4.NgForOf, i4.NgIf, i5.NativeElementInjectorDirective, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.CheckboxControlValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, i7.IneligablePurchaseModalComponent, i1.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog .p-dialog-header {\\n  display: none;\\n}\\n\\n  .p-dialog .p-dialog-content {\\n  padding: 0 !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .dropdown-select[_ngcontent-%COMP%] {\\n    grid-template-columns: auto !important; \\n\\n  }\\n  .dropdowns[_ngcontent-%COMP%]   .col-flex[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .col-flex[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n    width: 80%;\\n  }\\n  .age-modal[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    padding: 22px !important;\\n    gap: 10px !important;\\n  }\\n  .age-header[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    justify-content: flex-start !important;\\n  }\\n  .warning-text[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n  }\\n  .warning-icon[_ngcontent-%COMP%] {\\n    width: 20px !important;\\n    height: 20px !important;\\n  }\\n  .heading[_ngcontent-%COMP%] {\\n    font-size: 18px !important;\\n  }\\n  .age-value[_ngcontent-%COMP%] {\\n    max-width: 50px !important;\\n    max-height: 50px !important;\\n  }\\n  .age-text[_ngcontent-%COMP%] {\\n    font-size: 14.93px !important;\\n  }\\n  #body-text[_ngcontent-%COMP%] {\\n    font-size: 14px !important;\\n  }\\n  .termsAndCondition-text[_ngcontent-%COMP%] {\\n    font-size: 14px !important;\\n    padding-top: 1px;\\n  }\\n  .footer[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .age-data[_ngcontent-%COMP%] {\\n    max-width: 80px !important;\\n    max-height: 80px !important;\\n  }\\n  .error-text[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n  }\\n  button.cancel-btn[_ngcontent-%COMP%], button.proceed-btn[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .footer[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n}\\n.termsAndCondition-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.termsAndCondition-link[_ngcontent-%COMP%] {\\n  color: #204E6E;\\n}\\n\\n.age-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.warning-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n\\nselect[_ngcontent-%COMP%] {\\n  color: black;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n  appearance: none;\\n  background: none;\\n  background-color: transparent;\\n  width: 100px;\\n  border: none;\\n  outline: none;\\n  align-self: stretch;\\n}\\n\\n.age-modal[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 600px;\\n  padding: 34px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 32px;\\n}\\n\\n.age-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 12px;\\n  align-self: stretch;\\n}\\n\\n.anchor[_ngcontent-%COMP%] {\\n  position: relative;\\n  justify-content: center;\\n  display: flex;\\n}\\n\\n.age-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 74.667px;\\n  height: 74.667px;\\n  \\n\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 9.333px;\\n  position: absolute;\\n  bottom: 22%;\\n  border-radius: 93.333px;\\n  border: 5.6px solid #BA0303;\\n  background: #FFF;\\n}\\n\\n.age-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  text-align: center;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 22.4px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 22.4px;\\n  \\n\\n  letter-spacing: 0.467px;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  color: #000;\\n  text-align: center;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%;\\n  margin: 0;\\n  letter-spacing: 0.5px;\\n}\\n\\n.warning-lable[_ngcontent-%COMP%] {\\n  border-radius: 2px;\\n  border-left: 4px solid #000;\\n  background: rgba(255, 203, 5, 0.2);\\n  display: flex;\\n  padding: 12px;\\n  align-items: flex-start;\\n  gap: 12px;\\n  align-self: stretch;\\n}\\n\\n.warning-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 120%;\\n  \\n\\n  letter-spacing: 0.5px;\\n}\\n\\n.error-lable[_ngcontent-%COMP%] {\\n  border-radius: 2px;\\n  border-left: 4px solid #F00;\\n  background: rgba(255, 0, 0, 0.1);\\n  display: flex;\\n  padding: 12px;\\n  align-items: flex-start;\\n  gap: 12px;\\n  align-self: stretch;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 120%; \\n\\n  letter-spacing: 0.5px;\\n}\\n\\n.error-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .warning-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n#body-text[_ngcontent-%COMP%] {\\n  align-self: stretch;\\n  color: #000;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  \\n\\n  letter-spacing: 0.5px;\\n}\\n\\n.dropdown-select[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: auto auto auto;\\n  min-width: 170px;\\n  max-width: 343px;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1 0 0;\\n  align-self: stretch;\\n}\\n\\n.dropdowns[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n  border-radius: 8px;\\n  opacity: 0.99;\\n  background: #F5F5F5;\\n  display: flex;\\n  min-width: 170px;\\n  max-width: 343px;\\n  padding: 8px 16px;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1 0 0;\\n  align-self: stretch;\\n}\\n\\n.dropdown-label[_ngcontent-%COMP%] {\\n  color: #323232;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n\\n.col-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.termsAndCondition[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-self: stretch;\\n  padding-top: 5px;\\n}\\n\\n.checkBox[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 16px;\\n  height: 16px;\\n  padding: 0px 16px;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 32px;\\n}\\n\\n.termsAndCondition-text[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  \\n\\n  letter-spacing: 0.5px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.termsAndCondition-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 32px;\\n  height: 32px;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n}\\n\\nbutton.cancel-btn[_ngcontent-%COMP%], button.proceed-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 272px;\\n  height: 48px;\\n  padding: 12px 24px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  border-radius: 6px;\\n  border: 2px solid var(--Secondary-100, #D5EDFD);\\n  background: none;\\n  color: var(--colors-Main-Color, #204E6E);\\n  font-family: \\\"Public Sans\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%;\\n  letter-spacing: 0.168px;\\n}\\n\\nbutton.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: none;\\n  color: var(--colors-Main-Color, #204E6E);\\n  font-family: \\\"Public Sans\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%;\\n  letter-spacing: 0.168px;\\n}\\n\\nbutton.proceed-btn[_ngcontent-%COMP%]:hover {\\n  border-radius: 6px;\\n  background: var(--colors-Main-Color, #204E6E);\\n  color: var(--Gray-00, #FFF);\\n  font-family: \\\"Public Sans\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%;\\n  \\n\\n  letter-spacing: 0.168px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "PLATFORM_ID", "isPlatformBrowser", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "day_r4", "ɵɵadvance", "ɵɵtextInterpolate", "month_r5", "year_r6", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵnamespaceHTML", "ɵɵpipeBind1", "AgeConsentModalComponent", "constructor", "translate", "platformId", "displayModal", "age", "eligibilityWarningLabel", "submit", "cancel", "currentYear", "Date", "getFullYear", "days", "Array", "from", "length", "_", "i", "months", "years", "screenWidth", "window", "innerWidth", "onResize", "event", "ngOnInit", "selected<PERSON>ay", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON>ear", "productAllowed", "enableProceedButton", "showAgeErrorMessage", "getDaysInMonth", "monthIndex", "indexOf", "year", "daysInMonth", "getDate", "onMonthOrYearChange", "validateDoB", "selectedDate", "today", "calculatedAge", "getMonth", "ageEligible", "displayEligableModal", "termsAndCondition", "loadTermsAndConditions", "title", "pageId", "localStorage", "getItem", "queryParams", "URLSearchParams", "toString", "url", "open", "onSumbit", "emit", "onCancel", "closeEligableModal", "ɵɵdirectiveInject", "i1", "TranslateService", "_2", "selectors", "hostBindings", "AgeConsentModalComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "ɵɵlistener", "AgeConsentModalComponent_Template_p_dialog_visibleChange_0_listener", "AgeConsentModalComponent_Template_select_ngModelChange_40_listener", "AgeConsentModalComponent_Template_select_change_40_listener", "ɵɵtemplate", "AgeConsentModalComponent_option_41_Template", "AgeConsentModalComponent_Template_select_ngModelChange_48_listener", "AgeConsentModalComponent_Template_select_change_48_listener", "AgeConsentModalComponent_option_49_Template", "AgeConsentModalComponent_Template_select_ngModelChange_56_listener", "AgeConsentModalComponent_Template_select_change_56_listener", "AgeConsentModalComponent_option_57_Template", "AgeConsentModalComponent_Template_input_change_62_listener", "AgeConsentModalComponent_Template_input_ngModelChange_62_listener", "AgeConsentModalComponent_Template_div_click_66_listener", "AgeConsentModalComponent_div_69_Template", "AgeConsentModalComponent_Template_button_click_71_listener", "AgeConsentModalComponent_Template_button_click_72_listener", "AgeConsentModalComponent_Template_app_ineligable_purchase_modal_cancel_73_listener", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\age-consent-modal\\age-consent-modal.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\age-consent-modal\\age-consent-modal.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, HostListener, Inject, PLATFORM_ID } from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { isPlatformBrowser } from \"@angular/common\";\r\n\r\n@Component({\r\n  selector: 'app-age-consent-modal',\r\n  templateUrl: './age-consent-modal.component.html',\r\n  styleUrls: ['./age-consent-modal.component.scss'],\r\n})\r\n\r\nexport class AgeConsentModalComponent {\r\n  @Input() displayModal: boolean = false;\r\n  @Input() age: number = 10;\r\n  @Input() eligibilityWarningLabel: string = '';\r\n  @Output() submit = new EventEmitter();\r\n  @Output() cancel = new EventEmitter();\r\n\r\n  constructor(private translate: TranslateService, @Inject(PLATFORM_ID) private platformId: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n\r\n  screenWidth: number;\r\n  currentYear = new Date().getFullYear();\r\n  days = Array.from({ length: 31 }, (_, i) => i + 1);\r\n  months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\r\n  years = Array.from({ length: 101 }, (_, i) => this.currentYear - i);\r\n  termsAndCondition: boolean;\r\n  productAllowed: boolean;\r\n  ageEligible: boolean;\r\n  enableProceedButton: boolean;\r\n  showAgeErrorMessage: boolean;\r\n  displayEligableModal: boolean;\r\n\r\n  selectedDay: number;\r\n  selectedMonth: string;\r\n  selectedYear: number;\r\n\r\n  ngOnInit(): void {\r\n    this.selectedDay = this.days[0];\r\n    this.selectedMonth = this.months[0];\r\n    this.selectedYear = this.years[0];\r\n    this.productAllowed = false;\r\n    this.enableProceedButton = false;\r\n    this.showAgeErrorMessage = false;\r\n  }\r\n\r\n\r\n  getDaysInMonth() {\r\n    const monthIndex = this.months.indexOf(this.selectedMonth);\r\n    const year = this.selectedYear;\r\n\r\n    const daysInMonth = new Date(year, monthIndex + 1, 0).getDate(); // Get number of days in the month\r\n    this.days = Array.from({ length: daysInMonth }, (_, i) => i + 1); // Update day options\r\n    if (this.selectedDay > daysInMonth) {\r\n      this.selectedDay = daysInMonth; // Adjust selected day if it's invalid\r\n    }\r\n  }\r\n\r\n  onMonthOrYearChange() {\r\n    this.getDaysInMonth();\r\n    this.validateDoB();\r\n  }\r\n\r\n  validateDoB() {\r\n    let selectedDate = new Date(this.selectedYear, this.months.indexOf(this.selectedMonth), this.selectedDay); // Selected date\r\n\r\n    let today = new Date(); // Current date\r\n\r\n    // Calculate the age\r\n    let calculatedAge = today.getFullYear() - selectedDate.getFullYear();\r\n\r\n    // Adjust age if the current date is before the birthday this year\r\n    if (\r\n      today.getMonth() < selectedDate.getMonth() ||\r\n      (today.getMonth() === selectedDate.getMonth() && today.getDate() < selectedDate.getDate())\r\n    ) {\r\n      calculatedAge--;\r\n    }\r\n\r\n    if (calculatedAge > this.age) {\r\n      this.ageEligible = true;\r\n    }\r\n    else this.ageEligible = false;\r\n\r\n    if (this.screenWidth > 786)\r\n      this.showAgeErrorMessage = !this.ageEligible;\r\n    else\r\n      this.displayEligableModal = !this.ageEligible;\r\n    this.enableProceedButton = this.ageEligible && this.termsAndCondition;\r\n    // this.showAgeErrorMessage = this.ageEligible && this.termsAndCondition;\r\n  }\r\n\r\n  loadTermsAndConditions(title: string) {\r\n    const pageId: string = localStorage.getItem('TermsAndConditionsId') ?? '';\r\n\r\n    if (pageId) {\r\n      const queryParams = new URLSearchParams({\r\n        pageId: pageId,\r\n        title: title,\r\n      }).toString();\r\n\r\n      const url = `/about-us/?${queryParams}`;\r\n      window.open(url, '_blank');\r\n    }\r\n  }\r\n\r\n  onSumbit() {\r\n    this.submit.emit(new Date(this.selectedYear, this.months.indexOf(this.selectedMonth), this.selectedDay));\r\n  }\r\n\r\n  onCancel() {\r\n    this.cancel.emit();\r\n  }\r\n\r\n  closeEligableModal(){\r\n    this.displayEligableModal = false;\r\n  }\r\n}\r\n", "<p-dialog class=\"age-consent\" [(visible)]=\"displayModal\" [breakpoints]=\"{ '960px': '75vw', '768px': '92vw' }\"\r\n  [resizable]=\"false\" [closable]=\"false\" [modal]=\"true\" [header]=\"\" [baseZIndex]=\"9999\">\r\n\r\n  \r\n\r\n    <!-- <ng-template pTemplate=\"header\"> -->\r\n    <div class=\"age-modal\">\r\n      <div class=\"age-header\">\r\n        <div class=\"age-data\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\r\n            <g filter=\"url(#filter0_d_52348_42654)\">\r\n              <circle cx=\"60\" cy=\"56\" r=\"48\" fill=\"white\" />\r\n            </g>\r\n            <circle cx=\"60.0001\" cy=\"56.0001\" r=\"45.0783\" fill=\"#F2F2F2\" />\r\n            <defs>\r\n              <filter id=\"filter0_d_52348_42654\" x=\"0\" y=\"0\" width=\"120\" height=\"120\" filterUnits=\"userSpaceOnUse\"\r\n                color-interpolation-filters=\"sRGB\">\r\n                <feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\" />\r\n                <feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                  result=\"hardAlpha\" />\r\n                <feOffset dy=\"4\" />\r\n                <feGaussianBlur stdDeviation=\"6\" />\r\n                <feComposite in2=\"hardAlpha\" operator=\"out\" />\r\n                <feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\" />\r\n                <feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_52348_42654\" />\r\n                <feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_52348_42654\" result=\"shape\" />\r\n              </filter>\r\n            </defs>\r\n          </svg>\r\n          <div class=\"age-value\">\r\n            <span class=\"age-text\">{{age}}+</span>\r\n          </div>\r\n        </div>\r\n        <h1 class=\"heading\">\r\n          Age verification\r\n        </h1>\r\n      </div>\r\n\r\n      <div class=\"warning-lable\">\r\n        <div class=\"warning-icon\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <path\r\n              d=\"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\"\r\n              stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n            <path d=\"M11.25 11.25H12V16.5H12.75\" stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\" />\r\n            <path\r\n              d=\"M11.8125 9C12.4338 9 12.9375 8.49632 12.9375 7.875C12.9375 7.25368 12.4338 6.75 11.8125 6.75C11.1912 6.75 10.6875 7.25368 10.6875 7.875C10.6875 8.49632 11.1912 9 11.8125 9Z\"\r\n              fill=\"#191C1F\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"warning-text\">\r\n          <p>{{eligibilityWarningLabel}}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"width: 100%;\">\r\n        <p id=\"body-text\">\r\n          Please verify your age to enter.\r\n        </p>\r\n        <div class=\"dropdown-select\">\r\n          <div class=\"dropdowns\">\r\n            <div class=\"col-flex\">\r\n              <label class=\"dropdown-label\">\r\n                Day*\r\n              </label>\r\n              <select [(ngModel)]=\"selectedDay\" (change)=\"validateDoB()\">\r\n                <option *ngFor=\"let day of days\" [value]=\"day\">{{ day }}</option>\r\n              </select>\r\n            </div>\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\">\r\n              <path\r\n                d=\"M12.6668 2.66732L11.3335 2.66732V2.00065C11.3335 1.82384 11.2633 1.65427 11.1382 1.52925C11.0132 1.40422 10.8436 1.33398 10.6668 1.33398C10.49 1.33398 10.3204 1.40422 10.1954 1.52925C10.0704 1.65427 10.0002 1.82384 10.0002 2.00065V2.66732L6.00016 2.66732V2.00065C6.00016 1.82384 5.92992 1.65427 5.8049 1.52925C5.67988 1.40422 5.51031 1.33398 5.3335 1.33398C5.15669 1.33398 4.98712 1.40422 4.86209 1.52925C4.73707 1.65427 4.66683 1.82384 4.66683 2.00065V2.66732L3.3335 2.66732C2.80306 2.66732 2.29436 2.87803 1.91928 3.2531C1.54421 3.62818 1.3335 4.13688 1.3335 4.66732L1.3335 12.6673C1.3335 13.1978 1.54421 13.7065 1.91928 14.0815C2.29436 14.4566 2.80306 14.6673 3.3335 14.6673L12.6668 14.6673C13.1973 14.6673 13.706 14.4566 14.081 14.0815C14.4561 13.7065 14.6668 13.1978 14.6668 12.6673L14.6668 4.66732C14.6668 4.13688 14.4561 3.62818 14.081 3.2531C13.706 2.87803 13.1973 2.66732 12.6668 2.66732ZM13.3335 12.6673C13.3335 12.8441 13.2633 13.0137 13.1382 13.1387C13.0132 13.2637 12.8436 13.334 12.6668 13.334L3.3335 13.334C3.15669 13.334 2.98712 13.2637 2.86209 13.1387C2.73707 13.0137 2.66683 12.8441 2.66683 12.6673V8.00065H13.3335L13.3335 12.6673ZM13.3335 6.66732L2.66683 6.66732L2.66683 4.66732C2.66683 4.49051 2.73707 4.32094 2.86209 4.19591C2.98712 4.07089 3.15669 4.00065 3.3335 4.00065H4.66683V4.66732C4.66683 4.84413 4.73707 5.0137 4.86209 5.13872C4.98712 5.26375 5.15669 5.33398 5.3335 5.33398C5.51031 5.33398 5.67988 5.26375 5.8049 5.13872C5.92992 5.0137 6.00016 4.84413 6.00016 4.66732V4.00065L10.0002 4.00065V4.66732C10.0002 4.84413 10.0704 5.0137 10.1954 5.13872C10.3204 5.26375 10.49 5.33398 10.6668 5.33398C10.8436 5.33398 11.0132 5.26375 11.1382 5.13872C11.2633 5.0137 11.3335 4.84413 11.3335 4.66732V4.00065L12.6668 4.00065C12.8436 4.00065 13.0132 4.07089 13.1382 4.19591C13.2633 4.32094 13.3335 4.49051 13.3335 4.66732V6.66732Z\"\r\n                fill=\"#030303\" />\r\n            </svg>\r\n          </div>\r\n          <div class=\"dropdowns\">\r\n            <div class=\"col-flex\">\r\n              <label class=\"dropdown-label\">\r\n                Month*\r\n              </label>\r\n              <select [(ngModel)]=\"selectedMonth\" (change)=\"onMonthOrYearChange()\">\r\n                <option *ngFor=\"let month of months\" [value]=\"month\">{{ month }}</option>\r\n              </select>\r\n            </div>\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\">\r\n              <path\r\n                d=\"M12.6668 2.66732L11.3335 2.66732V2.00065C11.3335 1.82384 11.2633 1.65427 11.1382 1.52925C11.0132 1.40422 10.8436 1.33398 10.6668 1.33398C10.49 1.33398 10.3204 1.40422 10.1954 1.52925C10.0704 1.65427 10.0002 1.82384 10.0002 2.00065V2.66732L6.00016 2.66732V2.00065C6.00016 1.82384 5.92992 1.65427 5.8049 1.52925C5.67988 1.40422 5.51031 1.33398 5.3335 1.33398C5.15669 1.33398 4.98712 1.40422 4.86209 1.52925C4.73707 1.65427 4.66683 1.82384 4.66683 2.00065V2.66732L3.3335 2.66732C2.80306 2.66732 2.29436 2.87803 1.91928 3.2531C1.54421 3.62818 1.3335 4.13688 1.3335 4.66732L1.3335 12.6673C1.3335 13.1978 1.54421 13.7065 1.91928 14.0815C2.29436 14.4566 2.80306 14.6673 3.3335 14.6673L12.6668 14.6673C13.1973 14.6673 13.706 14.4566 14.081 14.0815C14.4561 13.7065 14.6668 13.1978 14.6668 12.6673L14.6668 4.66732C14.6668 4.13688 14.4561 3.62818 14.081 3.2531C13.706 2.87803 13.1973 2.66732 12.6668 2.66732ZM13.3335 12.6673C13.3335 12.8441 13.2633 13.0137 13.1382 13.1387C13.0132 13.2637 12.8436 13.334 12.6668 13.334L3.3335 13.334C3.15669 13.334 2.98712 13.2637 2.86209 13.1387C2.73707 13.0137 2.66683 12.8441 2.66683 12.6673V8.00065H13.3335L13.3335 12.6673ZM13.3335 6.66732L2.66683 6.66732L2.66683 4.66732C2.66683 4.49051 2.73707 4.32094 2.86209 4.19591C2.98712 4.07089 3.15669 4.00065 3.3335 4.00065H4.66683V4.66732C4.66683 4.84413 4.73707 5.0137 4.86209 5.13872C4.98712 5.26375 5.15669 5.33398 5.3335 5.33398C5.51031 5.33398 5.67988 5.26375 5.8049 5.13872C5.92992 5.0137 6.00016 4.84413 6.00016 4.66732V4.00065L10.0002 4.00065V4.66732C10.0002 4.84413 10.0704 5.0137 10.1954 5.13872C10.3204 5.26375 10.49 5.33398 10.6668 5.33398C10.8436 5.33398 11.0132 5.26375 11.1382 5.13872C11.2633 5.0137 11.3335 4.84413 11.3335 4.66732V4.00065L12.6668 4.00065C12.8436 4.00065 13.0132 4.07089 13.1382 4.19591C13.2633 4.32094 13.3335 4.49051 13.3335 4.66732V6.66732Z\"\r\n                fill=\"#030303\" />\r\n            </svg>\r\n          </div>\r\n          <div class=\"dropdowns\">\r\n            <div class=\"col-flex\">\r\n              <label class=\"dropdown-label\">\r\n                Year*\r\n              </label>\r\n              <select [(ngModel)]=\"selectedYear\" (change)=\"onMonthOrYearChange()\">\r\n                <option *ngFor=\"let year of years\" [value]=\"year\">{{ year }}</option>\r\n              </select>\r\n            </div>\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\">\r\n              <path\r\n                d=\"M12.6668 2.66732L11.3335 2.66732V2.00065C11.3335 1.82384 11.2633 1.65427 11.1382 1.52925C11.0132 1.40422 10.8436 1.33398 10.6668 1.33398C10.49 1.33398 10.3204 1.40422 10.1954 1.52925C10.0704 1.65427 10.0002 1.82384 10.0002 2.00065V2.66732L6.00016 2.66732V2.00065C6.00016 1.82384 5.92992 1.65427 5.8049 1.52925C5.67988 1.40422 5.51031 1.33398 5.3335 1.33398C5.15669 1.33398 4.98712 1.40422 4.86209 1.52925C4.73707 1.65427 4.66683 1.82384 4.66683 2.00065V2.66732L3.3335 2.66732C2.80306 2.66732 2.29436 2.87803 1.91928 3.2531C1.54421 3.62818 1.3335 4.13688 1.3335 4.66732L1.3335 12.6673C1.3335 13.1978 1.54421 13.7065 1.91928 14.0815C2.29436 14.4566 2.80306 14.6673 3.3335 14.6673L12.6668 14.6673C13.1973 14.6673 13.706 14.4566 14.081 14.0815C14.4561 13.7065 14.6668 13.1978 14.6668 12.6673L14.6668 4.66732C14.6668 4.13688 14.4561 3.62818 14.081 3.2531C13.706 2.87803 13.1973 2.66732 12.6668 2.66732ZM13.3335 12.6673C13.3335 12.8441 13.2633 13.0137 13.1382 13.1387C13.0132 13.2637 12.8436 13.334 12.6668 13.334L3.3335 13.334C3.15669 13.334 2.98712 13.2637 2.86209 13.1387C2.73707 13.0137 2.66683 12.8441 2.66683 12.6673V8.00065H13.3335L13.3335 12.6673ZM13.3335 6.66732L2.66683 6.66732L2.66683 4.66732C2.66683 4.49051 2.73707 4.32094 2.86209 4.19591C2.98712 4.07089 3.15669 4.00065 3.3335 4.00065H4.66683V4.66732C4.66683 4.84413 4.73707 5.0137 4.86209 5.13872C4.98712 5.26375 5.15669 5.33398 5.3335 5.33398C5.51031 5.33398 5.67988 5.26375 5.8049 5.13872C5.92992 5.0137 6.00016 4.84413 6.00016 4.66732V4.00065L10.0002 4.00065V4.66732C10.0002 4.84413 10.0704 5.0137 10.1954 5.13872C10.3204 5.26375 10.49 5.33398 10.6668 5.33398C10.8436 5.33398 11.0132 5.26375 11.1382 5.13872C11.2633 5.0137 11.3335 4.84413 11.3335 4.66732V4.00065L12.6668 4.00065C12.8436 4.00065 13.0132 4.07089 13.1382 4.19591C13.2633 4.32094 13.3335 4.49051 13.3335 4.66732V6.66732Z\"\r\n                fill=\"#030303\" />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n        <div class=\"termsAndCondition\">        \r\n          <div class=\"termsAndCondition-checkbox\"><input type=\"checkbox\" (change)=\"validateDoB()\" [(ngModel)]=\"termsAndCondition\" class=\"checkBox\"/> </div>\r\n          <div class=\"termsAndCondition-text\">\r\n            <p>I agree all &nbsp;</p>\r\n            <div (click)=\"loadTermsAndConditions('Terms and Conditions')\" class=\"termsAndCondition-link\">\r\n              {{ \"footer.termsAndConditions\" | translate }}\r\n            </div>\r\n          </div>        \r\n      </div>\r\n\r\n      <div class=\"error-lable\" *ngIf=\"showAgeErrorMessage\">\r\n        <div style=\"width: 24px;height: 24px;\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <path\r\n              d=\"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\"\r\n              stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\r\n            <path d=\"M11.25 11.25H12V16.5H12.75\" stroke=\"#191C1F\" stroke-width=\"1.5\" stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\" />\r\n            <path\r\n              d=\"M11.8125 9C12.4338 9 12.9375 8.49632 12.9375 7.875C12.9375 7.25368 12.4338 6.75 11.8125 6.75C11.1912 6.75 10.6875 7.25368 10.6875 7.875C10.6875 8.49632 11.1912 9 11.8125 9Z\"\r\n              fill=\"#191C1F\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"error-text\">\r\n          <p>{{'ageConsentModal.errorLabel' | translate}}</p>\r\n        </div>\r\n      </div>\r\n      </div>\r\n\r\n      <div class=\"footer\">\r\n        <button\r\n          pButton\r\n          pRipple\r\n          label=\"Cancel\"\r\n          class=\"cancel-btn\"\r\n          (click)=\"onCancel()\"\r\n        ></button>\r\n  \r\n        <button\r\n          pButton\r\n          pRipple\r\n          label=\"Proceed\"\r\n          class=\"proceed-btn\"\r\n          (click)=\"onSumbit()\"\r\n          [disabled]=\"!enableProceedButton\"\r\n        ></button>\r\n      </div>\r\n    </div>\r\n</p-dialog>\r\n\r\n<app-ineligable-purchase-modal\r\n[displayModal]=\"displayEligableModal\"\r\n(cancel)=\"closeEligableModal()\"\r\n></app-ineligable-purchase-modal>"], "mappings": "AAAA,SAAoBA,YAAY,EAA+CC,WAAW,QAAQ,eAAe;AAEjH,SAASC,iBAAiB,QAAQ,iBAAiB;;;;;;;;;;;ICiEnCC,EAAA,CAAAC,cAAA,iBAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAhCH,EAAA,CAAAI,UAAA,UAAAC,MAAA,CAAa;IAACL,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAO,iBAAA,CAAAF,MAAA,CAAS;;;;;IAexDL,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApCH,EAAA,CAAAI,UAAA,UAAAI,QAAA,CAAe;IAACR,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAW;;;;;IAehER,EAAA,CAAAC,cAAA,iBAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAACT,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,iBAAA,CAAAE,OAAA,CAAU;;;;;IAoBtET,EAAA,CAAAC,cAAA,cAAqD;IAEjDD,EAAA,CAAAU,cAAA,EAA+F;IAA/FV,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAW,SAAA,eAEuF;IAMzFX,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAY,eAAA,EAAwB;IAAxBZ,EAAA,CAAAC,cAAA,cAAwB;IACnBD,EAAA,CAAAE,MAAA,GAA4C;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IAAhDH,EAAA,CAAAM,SAAA,GAA4C;IAA5CN,EAAA,CAAAO,iBAAA,CAAAP,EAAA,CAAAa,WAAA,qCAA4C;;;;;;;;;ADzHzD,OAAM,MAAOC,wBAAwB;EAOnCC,YAAoBC,SAA2B,EAA+BC,UAAe;IAAzE,KAAAD,SAAS,GAATA,SAAS;IAAiD,KAAAC,UAAU,GAAVA,UAAU;IAN/E,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,uBAAuB,GAAW,EAAE;IACnC,KAAAC,MAAM,GAAG,IAAIxB,YAAY,EAAE;IAC3B,KAAAyB,MAAM,GAAG,IAAIzB,YAAY,EAAE;IAgBrC,KAAA0B,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACtC,KAAAC,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IAClD,KAAAC,MAAM,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;IACnI,KAAAC,KAAK,GAAGN,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACR,WAAW,GAAGQ,CAAC,CAAC;IAhBjE,IAAIhC,iBAAiB,CAAC,IAAI,CAACkB,UAAU,CAAC,EAAE;MACtC,IAAI,CAACiB,WAAW,GAAGC,MAAM,CAACC,UAAU;;EAExC;EAGAC,QAAQA,CAACC,KAAW;IAClB,IAAIvC,iBAAiB,CAAC,IAAI,CAACkB,UAAU,CAAC,EAAE;MACtC,IAAI,CAACiB,WAAW,GAAGC,MAAM,CAACC,UAAU;;EAExC;EAkBAG,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACd,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACe,aAAa,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,CAACU,YAAY,GAAG,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC;IACjC,IAAI,CAACU,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;EAClC;EAGAC,cAAcA,CAAA;IACZ,MAAMC,UAAU,GAAG,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,IAAI,CAACP,aAAa,CAAC;IAC1D,MAAMQ,IAAI,GAAG,IAAI,CAACP,YAAY;IAE9B,MAAMQ,WAAW,GAAG,IAAI1B,IAAI,CAACyB,IAAI,EAAEF,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAACI,OAAO,EAAE,CAAC,CAAC;IACjE,IAAI,CAACzB,IAAI,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEqB;IAAW,CAAE,EAAE,CAACpB,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClE,IAAI,IAAI,CAACS,WAAW,GAAGU,WAAW,EAAE;MAClC,IAAI,CAACV,WAAW,GAAGU,WAAW,CAAC,CAAC;;EAEpC;;EAEAE,mBAAmBA,CAAA;IACjB,IAAI,CAACN,cAAc,EAAE;IACrB,IAAI,CAACO,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAIC,YAAY,GAAG,IAAI9B,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE,IAAI,CAACV,MAAM,CAACgB,OAAO,CAAC,IAAI,CAACP,aAAa,CAAC,EAAE,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC;IAE3G,IAAIe,KAAK,GAAG,IAAI/B,IAAI,EAAE,CAAC,CAAC;IAExB;IACA,IAAIgC,aAAa,GAAGD,KAAK,CAAC9B,WAAW,EAAE,GAAG6B,YAAY,CAAC7B,WAAW,EAAE;IAEpE;IACA,IACE8B,KAAK,CAACE,QAAQ,EAAE,GAAGH,YAAY,CAACG,QAAQ,EAAE,IACzCF,KAAK,CAACE,QAAQ,EAAE,KAAKH,YAAY,CAACG,QAAQ,EAAE,IAAIF,KAAK,CAACJ,OAAO,EAAE,GAAGG,YAAY,CAACH,OAAO,EAAG,EAC1F;MACAK,aAAa,EAAE;;IAGjB,IAAIA,aAAa,GAAG,IAAI,CAACrC,GAAG,EAAE;MAC5B,IAAI,CAACuC,WAAW,GAAG,IAAI;KACxB,MACI,IAAI,CAACA,WAAW,GAAG,KAAK;IAE7B,IAAI,IAAI,CAACxB,WAAW,GAAG,GAAG,EACxB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACa,WAAW,CAAC,KAE7C,IAAI,CAACC,oBAAoB,GAAG,CAAC,IAAI,CAACD,WAAW;IAC/C,IAAI,CAACd,mBAAmB,GAAG,IAAI,CAACc,WAAW,IAAI,IAAI,CAACE,iBAAiB;IACrE;EACF;;EAEAC,sBAAsBA,CAACC,KAAa;IAClC,MAAMC,MAAM,GAAWC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,EAAE;IAEzE,IAAIF,MAAM,EAAE;MACV,MAAMG,WAAW,GAAG,IAAIC,eAAe,CAAC;QACtCJ,MAAM,EAAEA,MAAM;QACdD,KAAK,EAAEA;OACR,CAAC,CAACM,QAAQ,EAAE;MAEb,MAAMC,GAAG,GAAG,cAAcH,WAAW,EAAE;MACvC/B,MAAM,CAACmC,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;;EAE9B;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAClD,MAAM,CAACmD,IAAI,CAAC,IAAIhD,IAAI,CAAC,IAAI,CAACkB,YAAY,EAAE,IAAI,CAACV,MAAM,CAACgB,OAAO,CAAC,IAAI,CAACP,aAAa,CAAC,EAAE,IAAI,CAACD,WAAW,CAAC,CAAC;EAC1G;EAEAiC,QAAQA,CAAA;IACN,IAAI,CAACnD,MAAM,CAACkD,IAAI,EAAE;EACpB;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,CAACf,oBAAoB,GAAG,KAAK;EACnC;EAAC,QAAA7B,CAAA,G;qBAnHUhB,wBAAwB,EAAAd,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAOsB7E,WAAW;EAAA;EAAA,QAAAgF,EAAA,G;UAPzDhE,wBAAwB;IAAAiE,SAAA;IAAAC,YAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAxBC,GAAA,CAAA9C,QAAA,CAAA+C,MAAA,CAAgB;QAAA,UAAApF,EAAA,CAAAqF,eAAA;;;;;;;;;;;;;;;;;QCV7BrF,EAAA,CAAAC,cAAA,kBACwF;QAD1DD,EAAA,CAAAsF,UAAA,2BAAAC,oEAAAH,MAAA;UAAA,OAAAD,GAAA,CAAAjE,YAAA,GAAAkE,MAAA;QAAA,EAA0B;QAMpDpF,EAAA,CAAAC,cAAA,aAAuB;QAGjBD,EAAA,CAAAU,cAAA,EAAmG;QAAnGV,EAAA,CAAAC,cAAA,aAAmG;QAE/FD,EAAA,CAAAW,SAAA,gBAA8C;QAChDX,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAW,SAAA,gBAA+D;QAC/DX,EAAA,CAAAC,cAAA,WAAM;QAGFD,EAAA,CAAAW,SAAA,kBAAyD;QAS3DX,EAAA,CAAAG,YAAA,EAAS;QAGbH,EAAA,CAAAY,eAAA,EAAuB;QAAvBZ,EAAA,CAAAC,cAAA,eAAuB;QACED,EAAA,CAAAE,MAAA,IAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAG1CH,EAAA,CAAAC,cAAA,cAAoB;QAClBD,EAAA,CAAAE,MAAA,0BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGPH,EAAA,CAAAC,cAAA,eAA2B;QAEvBD,EAAA,CAAAU,cAAA,EAA+F;QAA/FV,EAAA,CAAAC,cAAA,eAA+F;QAC7FD,EAAA,CAAAW,SAAA,gBAEuF;QAMzFX,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAY,eAAA,EAA0B;QAA1BZ,EAAA,CAAAC,cAAA,eAA0B;QACrBD,EAAA,CAAAE,MAAA,IAA2B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAItCH,EAAA,CAAAC,cAAA,eAA0B;QAEtBD,EAAA,CAAAE,MAAA,0CACF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,eAA6B;QAIrBD,EAAA,CAAAE,MAAA,cACF;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,kBAA2D;QAAnDD,EAAA,CAAAsF,UAAA,2BAAAE,mEAAAJ,MAAA;UAAA,OAAAD,GAAA,CAAA3C,WAAA,GAAA4C,MAAA;QAAA,EAAyB,oBAAAK,4DAAA;UAAA,OAAWN,GAAA,CAAA9B,WAAA,EAAa;QAAA,EAAxB;QAC/BrD,EAAA,CAAA0F,UAAA,KAAAC,2CAAA,qBAAiE;QACnE3F,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAU,cAAA,EAA+F;QAA/FV,EAAA,CAAAC,cAAA,eAA+F;QAC7FD,EAAA,CAAAW,SAAA,gBAEmB;QACrBX,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAY,eAAA,EAAuB;QAAvBZ,EAAA,CAAAC,cAAA,eAAuB;QAGjBD,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,kBAAqE;QAA7DD,EAAA,CAAAsF,UAAA,2BAAAM,mEAAAR,MAAA;UAAA,OAAAD,GAAA,CAAA1C,aAAA,GAAA2C,MAAA;QAAA,EAA2B,oBAAAS,4DAAA;UAAA,OAAWV,GAAA,CAAA/B,mBAAA,EAAqB;QAAA,EAAhC;QACjCpD,EAAA,CAAA0F,UAAA,KAAAI,2CAAA,qBAAyE;QAC3E9F,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAU,cAAA,EAA+F;QAA/FV,EAAA,CAAAC,cAAA,eAA+F;QAC7FD,EAAA,CAAAW,SAAA,gBAEmB;QACrBX,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAY,eAAA,EAAuB;QAAvBZ,EAAA,CAAAC,cAAA,eAAuB;QAGjBD,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,kBAAoE;QAA5DD,EAAA,CAAAsF,UAAA,2BAAAS,mEAAAX,MAAA;UAAA,OAAAD,GAAA,CAAAzC,YAAA,GAAA0C,MAAA;QAAA,EAA0B,oBAAAY,4DAAA;UAAA,OAAWb,GAAA,CAAA/B,mBAAA,EAAqB;QAAA,EAAhC;QAChCpD,EAAA,CAAA0F,UAAA,KAAAO,2CAAA,qBAAqE;QACvEjG,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAU,cAAA,EAA+F;QAA/FV,EAAA,CAAAC,cAAA,eAA+F;QAC7FD,EAAA,CAAAW,SAAA,gBAEmB;QACrBX,EAAA,CAAAG,YAAA,EAAM;QAGVH,EAAA,CAAAY,eAAA,EAA+B;QAA/BZ,EAAA,CAAAC,cAAA,eAA+B;QACkCD,EAAA,CAAAsF,UAAA,oBAAAY,2DAAA;UAAA,OAAUf,GAAA,CAAA9B,WAAA,EAAa;QAAA,EAAC,2BAAA8C,kEAAAf,MAAA;UAAA,OAAAD,GAAA,CAAAvB,iBAAA,GAAAwB,MAAA;QAAA;QAA/CpF,EAAA,CAAAG,YAAA,EAAkG;QAC1IH,EAAA,CAAAC,cAAA,eAAoC;QAC/BD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACzBH,EAAA,CAAAC,cAAA,eAA6F;QAAxFD,EAAA,CAAAsF,UAAA,mBAAAc,wDAAA;UAAA,OAASjB,GAAA,CAAAtB,sBAAA,CAAuB,sBAAsB,CAAC;QAAA,EAAC;QAC3D7D,EAAA,CAAAE,MAAA,IACF;;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAA0F,UAAA,KAAAW,wCAAA,mBAgBM;QACNrG,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAAoB;QAMhBD,EAAA,CAAAsF,UAAA,mBAAAgB,2DAAA;UAAA,OAASnB,GAAA,CAAAV,QAAA,EAAU;QAAA,EAAC;QACrBzE,EAAA,CAAAG,YAAA,EAAS;QAEVH,EAAA,CAAAC,cAAA,kBAOC;QAFCD,EAAA,CAAAsF,UAAA,mBAAAiB,2DAAA;UAAA,OAASpB,GAAA,CAAAZ,QAAA,EAAU;QAAA,EAAC;QAErBvE,EAAA,CAAAG,YAAA,EAAS;QAKlBH,EAAA,CAAAC,cAAA,yCAGC;QADDD,EAAA,CAAAsF,UAAA,oBAAAkB,mFAAA;UAAA,OAAUrB,GAAA,CAAAT,kBAAA,EAAoB;QAAA,EAAC;QAC9B1E,EAAA,CAAAG,YAAA,EAAgC;;;QAhKHH,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAjE,YAAA,CAA0B,gBAAAlB,EAAA,CAAAyG,eAAA,KAAAC,GAAA;QA8BrB1G,EAAA,CAAAM,SAAA,IAAQ;QAARN,EAAA,CAAA2G,kBAAA,KAAAxB,GAAA,CAAAhE,GAAA,MAAQ;QAsB9BnB,EAAA,CAAAM,SAAA,IAA2B;QAA3BN,EAAA,CAAAO,iBAAA,CAAA4E,GAAA,CAAA/D,uBAAA,CAA2B;QAclBpB,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAA3C,WAAA,CAAyB;QACPxC,EAAA,CAAAM,SAAA,GAAO;QAAPN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAzD,IAAA,CAAO;QAczB1B,EAAA,CAAAM,SAAA,GAA2B;QAA3BN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAA1C,aAAA,CAA2B;QACPzC,EAAA,CAAAM,SAAA,GAAS;QAATN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAnD,MAAA,CAAS;QAc7BhC,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAzC,YAAA,CAA0B;QACP1C,EAAA,CAAAM,SAAA,GAAQ;QAARN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAlD,KAAA,CAAQ;QAWiDjC,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAvB,iBAAA,CAA+B;QAInH5D,EAAA,CAAAM,SAAA,GACF;QADEN,EAAA,CAAA2G,kBAAA,MAAA3G,EAAA,CAAAa,WAAA,2CACF;QAIoBb,EAAA,CAAAM,SAAA,GAAyB;QAAzBN,EAAA,CAAAI,UAAA,SAAA+E,GAAA,CAAAtC,mBAAA,CAAyB;QAkC/C7C,EAAA,CAAAM,SAAA,GAAiC;QAAjCN,EAAA,CAAAI,UAAA,cAAA+E,GAAA,CAAAvC,mBAAA,CAAiC;QAO3C5C,EAAA,CAAAM,SAAA,GAAqC;QAArCN,EAAA,CAAAI,UAAA,iBAAA+E,GAAA,CAAAxB,oBAAA,CAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}