{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { WishlistRoutingModule } from './wishlist-routing.module';\nimport { ListComponent } from './components/list/list.component';\nimport { WishlistService } from \"@core/services\";\nimport { EmptyWishComponent } from './components/empty-wish/empty-wish.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ToastModule } from \"primeng/toast\";\nimport { RatingModule } from \"primeng/rating\";\nimport { FormsModule } from '@angular/forms';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { EmptyScreenComponent } from \"@shared/components/empty-screen/empty-screen.component\";\nimport { BackButtonComponent } from \"@shared/components/back-button/back-button.component\";\nimport { GAImpressionDirective } from '@core/directives/ga-impression.directive';\nimport * as i0 from \"@angular/core\";\nexport class WishlistModule {\n  static #_ = this.ɵfac = function WishlistModule_Factory(t) {\n    return new (t || WishlistModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: WishlistModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [WishlistService],\n    imports: [CommonModule, WishlistRoutingModule, TranslateModule, ToastModule, RatingModule, FormsModule, InitialModule, EmptyScreenComponent, BackButtonComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WishlistModule, {\n    declarations: [ListComponent, EmptyWishComponent],\n    imports: [CommonModule, WishlistRoutingModule, TranslateModule, ToastModule, RatingModule, FormsModule, InitialModule, EmptyScreenComponent, BackButtonComponent, GAImpressionDirective]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "WishlistRoutingModule", "ListComponent", "WishlistService", "EmptyWishComponent", "TranslateModule", "ToastModule", "RatingModule", "FormsModule", "InitialModule", "EmptyScreenComponent", "BackButtonComponent", "GAImpressionDirective", "WishlistModule", "_", "_2", "_3", "imports", "declarations"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\wishlist\\wishlist.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { WishlistRoutingModule } from './wishlist-routing.module';\r\nimport { ListComponent } from './components/list/list.component';\r\nimport {WishlistService} from \"@core/services\";\r\nimport { EmptyWishComponent } from './components/empty-wish/empty-wish.component';\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {ToastModule} from \"primeng/toast\";\r\nimport {RatingModule} from \"primeng/rating\";\r\nimport { FormsModule } from '@angular/forms';\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nimport {EmptyScreenComponent} from \"@shared/components/empty-screen/empty-screen.component\";\r\nimport {BackButtonComponent} from \"@shared/components/back-button/back-button.component\";\r\nimport { GAImpressionDirective } from '@core/directives/ga-impression.directive';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ListComponent,\r\n    EmptyWishComponent,\r\n\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    WishlistRoutingModule,\r\n    TranslateModule,\r\n    ToastModule,\r\n    RatingModule, FormsModule, InitialModule, EmptyScreenComponent,\r\n    BackButtonComponent,\r\n    GAImpressionDirective,\r\n  ],\r\n  providers: [\r\n    WishlistService\r\n  ]\r\n})\r\nexport class WishlistModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAAQC,eAAe,QAAO,gBAAgB;AAC9C,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,WAAW,QAAO,eAAe;AACzC,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAQC,aAAa,QAAO,gCAAgC;AAC5D,SAAQC,oBAAoB,QAAO,wDAAwD;AAC3F,SAAQC,mBAAmB,QAAO,sDAAsD;AACxF,SAASC,qBAAqB,QAAQ,0CAA0C;;AAqBhF,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;eAJd,CACTb,eAAe,CAChB;IAAAc,OAAA,GAVCjB,YAAY,EACZC,qBAAqB,EACrBI,eAAe,EACfC,WAAW,EACXC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,EAC9DC,mBAAmB;EAAA;;;2EAOVE,cAAc;IAAAK,YAAA,GAjBvBhB,aAAa,EACbE,kBAAkB;IAAAa,OAAA,GAIlBjB,YAAY,EACZC,qBAAqB,EACrBI,eAAe,EACfC,WAAW,EACXC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,EAC9DC,mBAAmB,EACnBC,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}