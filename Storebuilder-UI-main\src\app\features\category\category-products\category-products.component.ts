import {ChangeDetectorRef, Component, Inject, Input, OnInit, OnDestroy, PLATFORM_ID} from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import {ActivatedRoute, Router} from '@angular/router';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { CookieService } from "ngx-cookie-service";
import { environment } from '@environments/environment';
import { Subject, fromEvent } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';

import {
  MainDataService,
  AuthTokenService,
  TenantService,
  LoaderService,
  ReviewsService,
  StoreService,
  ProductService, AppDataService, PermissionService
} from "@core/services";

import { ProductRate, Category, Product, Currency, ProductBadge, CategoryList } from '@core/interface';
import {isPlatformBrowser, CommonModule} from "@angular/common";
import {GaActionEnum, GoogleAnalyticsService} from "ngx-google-analytics";
import { GTMService } from '@core/services/gtm.service';
import UtilityFunctions from '@core/utilities/functions';
import { BreadcrumbModule } from "primeng/breadcrumb";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { InitialModule } from '@shared/modules/initial.module';
import { CategoryNotFoundComponent } from '../category-not-found/category-not-found.component';

@Component({
  selector: 'app-category-products',
  templateUrl: './category-products.component.html',
  styleUrls: ['./category-products.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    InitialModule,
    CategoryNotFoundComponent,
  ]
})
export class CategoryProductsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  categoryId: number;
  topNumber: string;
  category!: Category;
  categoryName: string;
  items: MenuItem[] = [];
  breadItems: MenuItem[] = [];
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  currency: Currency = {} as Currency;
  baseUrl: string = environment.apiEndPoint + '/';
  emptyMsg: string = 'Your Category Is Empty';
  subPath: string[];
  subId: string[];
  catIds: string = '';
  catPaths: string = '';
  @Input() products: Array<Product>;
  isError : boolean = false;
  isBlank : boolean = true;
  reviews: ProductRate[] | undefined;
  badgesList: ProductBadge[] = [];

  rawCategories: CategoryList[] = [];
  newCategory: Category | null = null;
  token: string;
  pageSize: number = 50;
  currentPageSize: number = 50;
  triggerProductsCall: boolean = false;
  showProductSpinner: boolean = false;

  loadDataType: string = '';
  currentPageNumber: number = 1;
  total: number = 0;
  ignorePagination:boolean = false;
  shouldCallNextFeatureProduct: boolean = true;
  shouldCallNextCategoryProduct: boolean = true;
  navbarData: any;
  isLayoutTemplate: boolean = false;
  promotionId: string;
  promotionName: string;
  isGoogleAnalytics: boolean = false
  userDetails: any;
  tagName: typeof GaActionEnum = GaActionEnum;
  isMobileLayout: boolean = false;
  categoryBanner: any;

  private onSetupScrollListener(): void {
    if (isPlatformBrowser(this.platformId)) {
      fromEvent(window, 'scroll')
        .pipe(
          debounceTime(200),
          takeUntil(this.destroy$)
        )
        .subscribe(() => this.onCheckScrollPosition());
    }
  }

  private onCheckScrollPosition(): void {
    const scrollPosition = window.scrollY || document.documentElement.scrollTop;
    const totalHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    const isAtBottom = scrollPosition + windowHeight >= totalHeight - 1;

    if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {
      this.onLoadScrollData();
    }
  }

  onLoadScrollData(): void {
      if (this.loadDataType === 'category' ) {
          if (this.shouldCallNextCategoryProduct) this.onLoadPaginatedProducts()
      } else if (this.loadDataType === 'feature') {
          if (this.shouldCallNextFeatureProduct) this.onLoadPaginatedFeatureProducts()
      }
      else if (this.loadDataType === 'promotion') {
          if (this.shouldCallNextFeatureProduct) this.onLoadPaginatedPromotionProducts()
      }
  }

  constructor(
    private activatedRoute: ActivatedRoute,
    private productService: ProductService,
    private store: StoreService,
    private reviewsService: ReviewsService,
    private messageService: MessageService,
    private translate: TranslateService,
    private ref: ChangeDetectorRef,
    private loaderService: LoaderService,
    private tenantService: TenantService,
    private authTokenService: AuthTokenService,
    private cookieService: CookieService,
    private mainDataService: MainDataService,
    private router: Router,
    private appDataService: AppDataService,
    private permissionService: PermissionService,
    private $gaService: GoogleAnalyticsService,
    @Inject(PLATFORM_ID) private platformId: any,
    private $gtmService:GTMService

  ) {
    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')
    this.onScrollToTop()
  }

  ngOnInit(): void {
    this.navbarData = this.appDataService.layoutTemplate.find((section: { type: string }) => section.type === 'navbar');
    this.onSubscribeToRouteChanges();
    this.onSetupScrollListener();
    this.onScrollToTop();
  }

  onSubscribeToRouteChanges(): void {
    this.activatedRoute.paramMap
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.products = [];
        const routeParam = params.get('id');
        const paramParts = routeParam?.split('&');
        this.userDetails = this.store.get('profile');
        
        if (this.router.url.includes('promotion')) {
          this.onHandlePromotionRoute(paramParts);
        } else {
          this.onHandleCategoryRoute(paramParts);
        }
      });
  }

  private onHandlePromotionRoute(paramParts: string[] | undefined): void {
    if (paramParts?.length === 1) {
      this.promotionId = paramParts[0];
      this.loadDataType = 'promotion';
      this.onTriggerAnalytics('promotion', parseInt(this.promotionId));
      this.onLoadPromotionData();
    }
  }

  private onHandleCategoryRoute(paramParts: string[] | undefined): void {
    if (paramParts?.length === 1) {
      this.categoryId = parseInt(paramParts[0]);
      this.loadDataType = 'category';
      this.onTriggerAnalytics('category', this.categoryId);
      this.onLoadCategoryData();
    } else if (paramParts?.length === 3) {
      this.categoryId = parseInt(paramParts[0]);
      this.topNumber = paramParts[1];
      this.categoryName = paramParts[2];
      this.loadDataType = 'feature';
      this.onTriggerAnalytics('feature', this.categoryId);
      this.onLoadFeatureData();
      this.$gtmService.pushPageView('feature', this.categoryName);
    }
  }

  onLoadCategoryData(): void {
    this.loaderService.show();
    this.productService
      .getCategoryProducts(this.categoryId, this.currentPageSize, true, true)
      .subscribe({
        next: (res: any) => {
          this.products = [];
          this.isBlank = false;
          if (res.success) {
            this.isError= false;
            this.total = res.data.productsList.records.length;
            this.categoryBanner = res.data.categoryBanner;
            res.data?.productsList?.records.forEach((record: any) => {
              this.addProductFromLoadData(record)
              this.badgesList = record.badgesList[record]?.desktopImage || [];
            })
           this.getAllCategories();
          }
          this.loaderService.hide();
        },
        error: (err: Error) => {
          this.onHandleError(err);
        },
      });

    this.store.subscription('currency')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => (this.currency = res),
      });

    this.activatedRoute.queryParams.subscribe((res) => {
      this.subPath = res?.path?.split('//');
      this.subId = res?.id?.split('//');
    });

    if (!this.subPath) {
      this.store.subscription('categories').subscribe({
        next: (res: Category[]) => {
          res.forEach((element: Category) => {
            if (element.id == this.categoryId) {
              this.category = element;
            }
          });
          this.activatedRoute.queryParamMap.subscribe(params => {
          this.categoryName = params.get('categoryName') || '';
          })
        
        },
        error: (err: Error) => {
          console.error(err);
        },
      });
      let label = 'Products';

      this.translate.get('categoryCard.products').subscribe((data: string) => {
        label = data;
      });

      this.items = [{ label: this.category?.categoryName }, { label: label }];
      this.home = { icon: 'pi pi-home', routerLink: '/' };
    } else {
      let label = 'Products';

      this.translate.get('categoryCard.products').subscribe((data: string) => {
        label = data;
      });
      this.categoryName = this.subPath[this.subPath.length - 1];
      this.items = [];
      this.subPath.forEach((x: string, index: number) => {
        this.items.push({
          label: x,
          id: this.subId[index]
        });
      });

      this.items.forEach((val: MenuItem, index: number) => {
        const label = val.label ?? '';
        const idStr = String(val.id ?? '');
        this.catPaths = index === 0 ? label : this.catPaths + '//' + label;
        this.catIds = index === 0 ? idStr : this.catIds + '//' + idStr;
        val.routerLink = `/category/${idStr}`;
        val.queryParams = { path: this.catPaths, id: this.catIds }
      });

      this.items.push({
        label: label,
      });
      this.home = { icon: 'pi pi-home', routerLink: '/' };
    }
  }

  onLoadPromotionData(): void {
    this.loaderService.show();
    this.productService
      .GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber)
      .subscribe({
        next: (res: any) => {
          this.products = [];
          this.isBlank = false;
          this.categoryName=res.data.promotionName;
          this.breadItems = [{ label: this.categoryName }];
          if (res?.data?.records?.length > 0) {
            this.total = res?.data?.records?.length;
            res.data?.records.forEach((record: Product) => {
              this.addProductFromLoadSectionData(record);
            })
            this.loaderService.hide();
          }
        },
        error: (err: Error) => {
          console.error(err);
          this.isBlank = false;
          this.loaderService.hide();
        },
        complete: () => {
          this.loaderService.hide();
        }
      });

    this.store.subscription('currency')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => (this.currency = res),
      });

    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.ref.detectChanges();
    this.ref.markForCheck();
  }

  private onHandleError(err: Error): void {
    this.isBlank = false;
    this.isError = true;
    this.loaderService.hide();
  }

  addProductFromLoadData(record:Product){
    let selectedVariance;
    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)
    if (defaultVariant) {
      selectedVariance = defaultVariant;
    } else {
      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);
      if (approvedVariant) {
        selectedVariance = approvedVariant;
      } else {
        selectedVariance = record?.productVariances[0];
      }
    }
    let features=[];
    if(selectedVariance?.productFeaturesList){
      features=selectedVariance?.productFeaturesList[0]?.featureList;
    }

    let product: any = {
      badges:record.badgesList,
      productId: record?.id,
      productName: record?.name,
      isLiked:record?.isLiked,
      priceValue: selectedVariance?.price,
      salePriceValue: selectedVariance?.salePrice,
      priceId: selectedVariance?.priceId,
      currencyCode: record?.currencyCode,
      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),
      thumbnailImages: selectedVariance?.thumbnailImages,
      soldOut: selectedVariance?.soldOut,
      rate: selectedVariance?.rate,
      count: selectedVariance?.count ?? 0,
      specProductId: selectedVariance.specProductId,
      channelId: record.channelId ?? '1',
      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,
      shopId: record.shopId,
      isHot:features?.includes(1),
      isNew:features?.includes(2),
      isBest:features?.includes(3),
      quantity:selectedVariance.quantity,
      proSchedulingId:selectedVariance.proSchedulingId,
      stockPerSKU:selectedVariance.stockPerSKU,
      stockStatus:selectedVariance.stockStatus,
      sku:selectedVariance?.sku,
      skuAutoGenerated : selectedVariance.skuAutoGenerated
    }
    this.products.push(product)
  }

  onLoadFeatureData(): void {
    this.loaderService.show();
    this.productService
      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination)
      .subscribe({
        next: (res: any) => {
          this.products = [];
          this.isBlank = false;
          if (res?.data?.records?.length > 0) {
            this.total = res?.data?.records?.length;
            res.data?.records.forEach((record: any) => {
             this.addProductFromLoadSectionData(record);
            })
            this.loaderService.hide();
          }
        },
        error: (err: Error) => {
          console.error(err);
          this.isBlank = false;
          this.loaderService.hide();
        },
        complete: () => {
          this.loaderService.hide();
        }
      });

    this.store.subscription('currency')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => (this.currency = res),
      });

    this.breadItems = [{ label: this.categoryName }];
    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.ref.detectChanges();
    this.ref.markForCheck();
  }

  addProductFromLoadSectionData(record:any){
    let selectedVariance;
    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)
    if (defaultVariant) {
      selectedVariance = defaultVariant;
    } else {
      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);
      if (approvedVariant) {
        selectedVariance = approvedVariant;
      } else {
        selectedVariance = record?.productVariances[0];
      }
    }
    if (selectedVariance) {
      let features=[];
      if(selectedVariance?.productFeaturesList){
        features=selectedVariance?.productFeaturesList[0]?.featureList;
      }
      let product: any = {
        badges:record.badgesList,
        productId: record?.id,
        productName: record?.name,
        isLiked:record?.isLiked,
        priceValue: selectedVariance?.price,
        priceId: selectedVariance?.priceId,
        salePriceValue: selectedVariance?.salePrice,
        currencyCode: record?.currencyCode,
        masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],
        thumbnailImages: selectedVariance?.thumbnailImages,
        soldOut: selectedVariance?.soldOut,
        rate: selectedVariance?.rate,
        count: selectedVariance?.count ?? 0,
        salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,
        shopId: record.shopId,
        specProductId: selectedVariance.specProductId,
        channelId: record.channelId ?? '1',
        isHot:features?.includes(1),
        isNew:features?.includes(2),
        isBest:features?.includes(3),
        quantity:selectedVariance.quantity,
        proSchedulingId:selectedVariance.proSchedulingId,
        stockPerSKU:selectedVariance.stockPerSKU,
        stockStatus: selectedVariance.stockStatus,
        sku:selectedVariance?.sku,
        skuAutoGenerated : selectedVariance.skuAutoGenerated
      }
      if (product.salePriceValue) {
        product.salePercent = 100 - (product.salePriceValue / product.priceValue * 100);
      }
      this.products.push(product)
    }
  }

  fetchCategories(category: CategoryList[], cat: CategoryList) {
    if (category.length == 0) {
      return;
    }
    for (const element of category) {
      if (element.id == this.categoryId) {
        this.assignBreadCrumbsData(element);
      }
      this.fetchCategories(element.categories, element);
    }
  }

  assignBreadCrumbsData(category: { categoryIds: string; categoryPath: string }) {
    let idsArray: number[] = category?.categoryIds?.split("->")?.map(Number);
    let nameArray: string[] = category?.categoryPath?.split("->")?.map(String);
    let breadCrumbs: MenuItem[] = [];
    if (idsArray.length === nameArray.length) {
      idsArray.forEach((e, i) => {
        breadCrumbs.push({ routerLink: '/category/products/' + e.toString(), label: nameArray[i] });
      });
      this.breadItems = breadCrumbs;
      this.ref.detectChanges();
      this.ref.markForCheck();
    }
    this.$gtmService.pushPageView('category', category?.categoryPath)
  }

  onLoadPaginatedProducts(): void {
    this.triggerProductsCall = true
    this.showProductSpinner = true
    this.currentPageSize += this.pageSize;
    this.currentPageNumber += 1;
    this.ref.detectChanges();

    this.productService
      .getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber)
      .subscribe({
        next: (res: any) => {
          this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;
          const lastPageSizeProducts = res.data.productsList.records;
          this.total = lastPageSizeProducts.length;
          lastPageSizeProducts.forEach((record: any) => {
            this.addProductFromLoadData(record);
          })
          this.triggerProductsCall = !res.data.productsList.records.length;
          this.showProductSpinner = false
          this.ref.markForCheck()
        }
      })
  }

  onLoadPaginatedFeatureProducts(): void {
    this.triggerProductsCall = true
    this.showProductSpinner = true
    this.currentPageSize += this.pageSize;
    this.currentPageNumber += 1;
    this.ref.detectChanges()
    this.productService
      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)
      .subscribe({
        next: (res: any) => {
          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;
          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);
          this.total = res.data?.records?.length;

          lastPageSizeProducts.forEach((record: any) => {
            this.addProductFromLoadSectionData(record);
          })
          if (res.data?.records.length) this.triggerProductsCall = false;
          else this.triggerProductsCall = true;
          this.showProductSpinner = false
          this.ref.markForCheck()
        }
      })
  }

  onLoadPaginatedPromotionProducts(): void {
    this.triggerProductsCall = true
    this.showProductSpinner = true
    this.currentPageSize += this.pageSize;
    this.currentPageNumber += 1;
    this.ref.detectChanges()
    this.productService
      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)
      .subscribe({
        next: (res: any) => {
          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;
          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);
          this.total = lastPageSizeProducts.length;

          lastPageSizeProducts.forEach((record: any) => {
            this.addProduct(record);
          })
          if (res.data?.records.length) this.triggerProductsCall = false;
          else this.triggerProductsCall = true;
          this.showProductSpinner = false
          this.ref.markForCheck()
        }
      })
  }

  addProduct(record:Product){
    let selectedVariance;
    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)
    if (defaultVariant) {
      selectedVariance = defaultVariant;
    } else {
      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);
      if (approvedVariant) {
        selectedVariance = approvedVariant;
      } else {
        selectedVariance = record?.productVariances[0];
      }
    }
    let features=[];
    if(selectedVariance?.productFeaturesList){
      features=selectedVariance?.productFeaturesList[0]?.featureList;
    }
    let product: any = {
      productId: record?.id,
      productName: record?.name,
      priceValue: selectedVariance?.price,
      salePriceValue: selectedVariance?.salePrice,
      currencyCode: record?.currencyCode,
      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],
      thumbnailImages: selectedVariance?.thumbnailImages,
      soldOut: selectedVariance?.soldOut,
      rate: selectedVariance?.rate,
      count: selectedVariance?.count ?? 0,
      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,
      channelId: record?.channelId,
      isHot:features?.includes(1),
      isNew:features?.includes(2),
      isBest:features?.includes(3),
      quantity:selectedVariance.quantity,
      proSchedulingId:selectedVariance.proSchedulingId,
      stockPerSKU:selectedVariance.stockPerSKU,
      stockStatus: selectedVariance.stockStatus,
      sku:selectedVariance?.sku,
      skuAutoGenerated : selectedVariance?.skuAutoGenerated
    }
    this.products.push(product)
  }

  private getAllCategories() {
    const raw = localStorage.getItem('allCategories');
    const allCategories: CategoryList[] = raw ? JSON.parse(raw) : [];
    this.rawCategories = allCategories;

    this.rawCategories?.forEach((cat: CategoryList) => {
      if (cat.id == this.categoryId) {
        this.assignBreadCrumbsData(cat);
      }
      cat['path'] = cat.categoryName;
      cat['catIds'] = cat.id;
      this.fetchCategories(cat.categories, cat);
    });
  }

  onScrollToTop(): void {
    if (isPlatformBrowser(this.platformId)) {
      window.scrollTo({top: 0, behavior: 'smooth'});
    }
  }

  onTriggerAnalytics(type: string, id: number): void {
    if(this.isGoogleAnalytics &&  this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')){
      if(type === 'promotion') {
        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);
      } else if (type === 'category') {
        this.$gaService.pageView('/category', 'Category ID: ' + id);
      } else if (type === 'feature') {
        this.$gaService.pageView('/category', 'Feature ID: ' + id);
      }
      this.$gaService.event(
        this.tagName.SEARCH,
        String(id),
        type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST',
        1,
        true,
        {
          "id": id,
          "user_ID":this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'
        }
      );
    }
  }

  getBannerImages(url: string) {
    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
