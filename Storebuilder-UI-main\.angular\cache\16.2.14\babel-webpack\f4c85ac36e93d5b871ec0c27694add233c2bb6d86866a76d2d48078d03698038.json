{"ast": null, "code": "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n// do not edit .js files directly - edit src/index.jst\n\nvar fastDeepEqual = function equal(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!equal(a[key], b[key])) return false;\n    }\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n};\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqual);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n  LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n  LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n  LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n  LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n  /**\n   * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n   * using this library, instead the defaults are set by the Google Maps\n   * JavaScript API server.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n   * ```\n   */\n  constructor({\n    apiKey,\n    authReferrerPolicy,\n    channel,\n    client,\n    id = DEFAULT_ID,\n    language,\n    libraries = [],\n    mapIds,\n    nonce,\n    region,\n    retries = 3,\n    url = \"https://maps.googleapis.com/maps/api/js\",\n    version\n  }) {\n    this.callbacks = [];\n    this.done = false;\n    this.loading = false;\n    this.errors = [];\n    this.apiKey = apiKey;\n    this.authReferrerPolicy = authReferrerPolicy;\n    this.channel = channel;\n    this.client = client;\n    this.id = id || DEFAULT_ID; // Do not allow empty string\n    this.language = language;\n    this.libraries = libraries;\n    this.mapIds = mapIds;\n    this.nonce = nonce;\n    this.region = region;\n    this.retries = retries;\n    this.url = url;\n    this.version = version;\n    if (Loader.instance) {\n      if (!isEqual(this.options, Loader.instance.options)) {\n        throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(Loader.instance.options)}`);\n      }\n      return Loader.instance;\n    }\n    Loader.instance = this;\n  }\n  get options() {\n    return {\n      version: this.version,\n      apiKey: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      id: this.id,\n      libraries: this.libraries,\n      language: this.language,\n      region: this.region,\n      mapIds: this.mapIds,\n      nonce: this.nonce,\n      url: this.url,\n      authReferrerPolicy: this.authReferrerPolicy\n    };\n  }\n  get status() {\n    if (this.errors.length) {\n      return LoaderStatus.FAILURE;\n    }\n    if (this.done) {\n      return LoaderStatus.SUCCESS;\n    }\n    if (this.loading) {\n      return LoaderStatus.LOADING;\n    }\n    return LoaderStatus.INITIALIZED;\n  }\n  get failed() {\n    return this.done && !this.loading && this.errors.length >= this.retries + 1;\n  }\n  /**\n   * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n   *\n   * @ignore\n   * @deprecated\n   */\n  createUrl() {\n    let url = this.url;\n    url += `?callback=__googleMapsCallback&loading=async`;\n    if (this.apiKey) {\n      url += `&key=${this.apiKey}`;\n    }\n    if (this.channel) {\n      url += `&channel=${this.channel}`;\n    }\n    if (this.client) {\n      url += `&client=${this.client}`;\n    }\n    if (this.libraries.length > 0) {\n      url += `&libraries=${this.libraries.join(\",\")}`;\n    }\n    if (this.language) {\n      url += `&language=${this.language}`;\n    }\n    if (this.region) {\n      url += `&region=${this.region}`;\n    }\n    if (this.version) {\n      url += `&v=${this.version}`;\n    }\n    if (this.mapIds) {\n      url += `&map_ids=${this.mapIds.join(\",\")}`;\n    }\n    if (this.authReferrerPolicy) {\n      url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n    }\n    return url;\n  }\n  deleteScript() {\n    const script = document.getElementById(this.id);\n    if (script) {\n      script.remove();\n    }\n  }\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   * @deprecated, use importLibrary() instead.\n   */\n  load() {\n    return this.loadPromise();\n  }\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   *\n   * @ignore\n   * @deprecated, use importLibrary() instead.\n   */\n  loadPromise() {\n    return new Promise((resolve, reject) => {\n      this.loadCallback(err => {\n        if (!err) {\n          resolve(window.google);\n        } else {\n          reject(err.error);\n        }\n      });\n    });\n  }\n  importLibrary(name) {\n    this.execute();\n    return google.maps.importLibrary(name);\n  }\n  /**\n   * Load the Google Maps JavaScript API script with a callback.\n   * @deprecated, use importLibrary() instead.\n   */\n  loadCallback(fn) {\n    this.callbacks.push(fn);\n    this.execute();\n  }\n  /**\n   * Set the script on document.\n   */\n  setScript() {\n    var _a, _b;\n    if (document.getElementById(this.id)) {\n      // TODO wrap onerror callback for cases where the script was loaded elsewhere\n      this.callback();\n      return;\n    }\n    const params = {\n      key: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      libraries: this.libraries.length && this.libraries,\n      v: this.version,\n      mapIds: this.mapIds,\n      language: this.language,\n      region: this.region,\n      authReferrerPolicy: this.authReferrerPolicy\n    };\n    // keep the URL minimal:\n    Object.keys(params).forEach(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    key => !params[key] && delete params[key]);\n    if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n      // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n      // which also sets the base url, the id, and the nonce\n      /* eslint-disable */\n      (g => {\n        // @ts-ignore\n        let h,\n          a,\n          k,\n          p = \"The Google Maps JavaScript API\",\n          c = \"google\",\n          l = \"importLibrary\",\n          q = \"__ib__\",\n          m = document,\n          b = window;\n        // @ts-ignore\n        b = b[c] || (b[c] = {});\n        // @ts-ignore\n        const d = b.maps || (b.maps = {}),\n          r = new Set(),\n          e = new URLSearchParams(),\n          u = () =>\n          // @ts-ignore\n          h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            yield a = m.createElement(\"script\");\n            a.id = this.id;\n            e.set(\"libraries\", [...r] + \"\");\n            // @ts-ignore\n            for (k in g) e.set(k.replace(/[A-Z]/g, t => \"_\" + t[0].toLowerCase()), g[k]);\n            e.set(\"callback\", c + \".maps.\" + q);\n            a.src = this.url + `?` + e;\n            d[q] = f;\n            a.onerror = () => h = n(Error(p + \" could not load.\"));\n            // @ts-ignore\n            a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n            m.head.append(a);\n          })));\n        // @ts-ignore\n        d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n));\n      })(params);\n      /* eslint-enable */\n    }\n    // While most libraries populate the global namespace when loaded via bootstrap params,\n    // this is not the case for \"marker\" when used with the inline bootstrap loader\n    // (and maybe others in the future). So ensure there is an importLibrary for each:\n    const libraryPromises = this.libraries.map(library => this.importLibrary(library));\n    // ensure at least one library, to kick off loading...\n    if (!libraryPromises.length) {\n      libraryPromises.push(this.importLibrary(\"core\"));\n    }\n    Promise.all(libraryPromises).then(() => this.callback(), error => {\n      const event = new ErrorEvent(\"error\", {\n        error\n      }); // for backwards compat\n      this.loadErrorCallback(event);\n    });\n  }\n  /**\n   * Reset the loader state.\n   */\n  reset() {\n    this.deleteScript();\n    this.done = false;\n    this.loading = false;\n    this.errors = [];\n    this.onerrorEvent = null;\n  }\n  resetIfRetryingFailed() {\n    if (this.failed) {\n      this.reset();\n    }\n  }\n  loadErrorCallback(e) {\n    this.errors.push(e);\n    if (this.errors.length <= this.retries) {\n      const delay = this.errors.length * Math.pow(2, this.errors.length);\n      console.error(`Failed to load Google Maps script, retrying in ${delay} ms.`);\n      setTimeout(() => {\n        this.deleteScript();\n        this.setScript();\n      }, delay);\n    } else {\n      this.onerrorEvent = e;\n      this.callback();\n    }\n  }\n  callback() {\n    this.done = true;\n    this.loading = false;\n    this.callbacks.forEach(cb => {\n      cb(this.onerrorEvent);\n    });\n    this.callbacks = [];\n  }\n  execute() {\n    this.resetIfRetryingFailed();\n    if (this.loading) {\n      // do nothing but wait\n      return;\n    }\n    if (this.done) {\n      this.callback();\n    } else {\n      // short circuit and warn if google.maps is already loaded\n      if (window.google && window.google.maps && window.google.maps.version) {\n        console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" + \"This may result in undesirable behavior as options and script parameters may not match.\");\n        this.callback();\n        return;\n      }\n      this.loading = true;\n      this.setScript();\n    }\n  }\n}\nexport { DEFAULT_ID, Loader, LoaderStatus };\n//# sourceMappingURL=index.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}