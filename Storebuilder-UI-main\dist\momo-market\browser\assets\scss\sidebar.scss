@import "variables";
@import "../../assets/scss/common.scss";

.side-menu {
  .p-sidebar {
    .p-sidebar-header {
      height: 73px;
      background-color: #ffffff;
      padding-top: 28px;

      .p-sidebar-close {
        display: none;
      }
    }

    .p-sidebar-content {
      padding: 0 !important;
      background: var(--navbar_bgcolor);

      .sidebar-categories {
        &__category {
          width: 100%;
          height: 45px;
          padding: 8px;
          background: var(--navbar_bgcolor);
          color: #ffffff;
          font-size: 18px;
          font-weight: 500;
          cursor: pointer;

          &__item {
            max-width: 100%;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }
        }

        @include responsive(mobile) {
          margin-top: 84px;
        }

      }

      .sidebar-sub-category {
        position: fixed;
        top: 74px;
        margin-left: 320px;
        background: var(--navbar_bgcolor);
        width: 319px;
        z-index: 9999;
        padding: 0 0 20px 12px;
        border-radius: 0 8px 8px 0;

        min-height: 524px;
        overflow-y: auto;
        max-height: calc(100vh - 74px);

        &__section {
          width: 270px;
          //height: 150px;
          padding: 0px 8px 0px 8px;

          &__heading {
            height: 41px;
            width: 270px;
            color: #fff;
            border-bottom: 1px solid #fff;
            text-align: left;
            margin-bottom: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: var(--regular-font);
            font-size: 18px;
            overflow: hidden;
          }

          &__sub-name-section {
            padding-left: 12px;
            height: 100%;

            &__sub-name {
              font-weight: 400;
              font-size: 14px;
              color: #ffffff;

              &__item {
                max-width: 100%;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: left;
              }
            }

            &__see-all {
              margin-top: auto;
            }
          }
        }
      }
    }

    .p-sidebar-footer {
      padding: 0 !important;
      background: var(--navbar_bgcolor);
    }
  }


}
