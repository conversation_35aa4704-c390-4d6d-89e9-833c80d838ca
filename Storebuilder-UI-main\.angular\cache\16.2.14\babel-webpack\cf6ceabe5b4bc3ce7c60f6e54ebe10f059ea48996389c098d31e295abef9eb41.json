{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { get } from 'scriptjs';\nimport { environment } from '@environments/environment';\nimport { RequestDelivery } from '@core/interface';\nimport { forkJoin, take } from \"rxjs\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport { PaymentMethodEnum } from '@core/enums/payment-method-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/custom-GA.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"@pages/checkout/modals/lightbox-loader-modal/lightbox-loader-modal.component\";\nconst _c0 = function (a0) {\n  return {\n    \"opacity\": a0\n  };\n};\nfunction PaymentCartComponent_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"section\", 10)(3, \"div\", 11)(4, \"div\", 12);\n    i0.ɵɵelement(5, \"p\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_ng_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.show());\n    });\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"span\", 16);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 17);\n    i0.ɵɵtext(12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 18);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 19);\n    i0.ɵɵelement(19, \"path\", 20)(20, \"path\", 21);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.isProceccedCheckOut)(\"ngStyle\", i0.ɵɵpureFunction1(14, _c0, !ctx_r3.isProceccedCheckOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.orderDetails.productDetails.length, \" \", i0.ɵɵpipeBind1(10, 7, ctx_r3.orderDetails.productDetails.length > 1 ? \"checkout.items\" : \"checkout.item\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.disableCent === \"false\" ? i0.ɵɵpipeBind2(14, 9, ctx_r3.orderDetails.orderAmount + ctx_r3.shipmentService.shipmentCost - ctx_r3.orderDiscount, \"1.\" + ctx_r3.decimalValue + \"-\" + ctx_r3.decimalValue) : ctx_r3.orderDetails.orderAmount + ctx_r3.shipmentService.shipmentCost - ctx_r3.orderDiscount, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 12, \"checkout.paymentCart.PayNow\"), \" \");\n  }\n}\nfunction PaymentCartComponent_div_0_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"checkout.paymentCart.arrives\"), \": \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.timeString);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    opacity: a0\n  };\n};\nfunction PaymentCartComponent_div_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 22);\n    i0.ɵɵtemplate(1, PaymentCartComponent_div_0_ng_template_2_div_1_Template, 5, 4, \"div\", 23);\n    i0.ɵɵelementStart(2, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_ng_template_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.show());\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.timeString && ctx_r5.isShipmentFeeExist);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.isProceccedCheckOut)(\"ngStyle\", i0.ɵɵpureFunction1(6, _c1, !ctx_r5.isProceccedCheckOut ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 4, \"checkout.paymentCart.proceed\"), \" \");\n  }\n}\nfunction PaymentCartComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, PaymentCartComponent_div_0_ng_container_1_Template, 21, 16, \"ng-container\", 4);\n    i0.ɵɵtemplate(2, PaymentCartComponent_div_0_ng_template_2_Template, 5, 8, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"section\", 6)(5, \"div\", 7)(6, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.show());\n    });\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(3);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isMobileTemplate && ctx_r0.screenWidth <= 768)(\"ngIfElse\", _r4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(7, 3, \"checkout.paymentCart.proceed\"));\n  }\n}\nfunction PaymentCartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"section\", 22)(2, \"div\", 7)(3, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PaymentCartComponent_div_1_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.show());\n    });\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(4, 1, \"checkout.paymentCart.proceed\"));\n  }\n}\nfunction PaymentCartComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-lightbox-loader-modal\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r2.displayLoaderModal);\n  }\n}\nexport let PaymentCartComponent = /*#__PURE__*/(() => {\n  class PaymentCartComponent {\n    dialogService;\n    platformId;\n    store;\n    paymentService;\n    router;\n    messageService;\n    shopService;\n    transactionService;\n    transactionDetailsService;\n    orderService;\n    translate;\n    addressService;\n    productLogicService;\n    loaderService;\n    authService;\n    mainDataService;\n    permissionService;\n    $gaService;\n    shipmentService;\n    renderer;\n    cd;\n    _GACustomEvent;\n    deliveryOptionDetails;\n    refreshSummary;\n    cartItems;\n    paymentMethodDetails;\n    refreshSubscription;\n    ref;\n    lightBoxData = null;\n    shopsPayments = new Array();\n    totalLogisticsFee = 0;\n    subOrdersCommission = new Array();\n    lightBoxURL;\n    arrayIframe;\n    shipmentFee = 0;\n    totalDiscount = 0;\n    allowedpaymentMethod = 0;\n    userPhoneNumber;\n    showPayButton = false;\n    orderDetails;\n    orderDetailsWithConfig;\n    paymentResult;\n    currency;\n    countryCode;\n    countryPhone;\n    shipmentCost = -1;\n    AmountTrxn = 0;\n    requestData = new RequestDelivery();\n    shipmentDetails;\n    subOrderDetails;\n    minDate = new Date().toDateString();\n    maxDate = new Date().toDateString();\n    multipleTransactionDetails = new Array();\n    PaymentCalled = false;\n    shopIds = [];\n    merchantId = '';\n    terminalId = '';\n    isMobileTemplate = false;\n    OrderId = '';\n    OrderShopId = null;\n    minTimeDelivery = Number.MAX_SAFE_INTEGER;\n    maxTimeDelivery = -1;\n    MomoPayCommissionAmount = 0;\n    cartId = 0;\n    arrivalDate = '';\n    timeString = '';\n    isLightboxLoaded = false;\n    isFetchOrderPaymentConfig = false;\n    isProceccedCheckOut = false;\n    isLayoutTemplate = false;\n    isShipmentFeeExist = false;\n    isGoogleAnalytics = false;\n    itemsTotalPrices = 0;\n    currencyCode = '';\n    decimalValue = 0;\n    disableCent;\n    displayLoaderModal = false;\n    observer;\n    iframeLoadListener;\n    addressSubscription;\n    mobileSubscription;\n    screenWidth = window.innerWidth;\n    sessionId;\n    userDetails;\n    orderDiscount = 0;\n    onResize(event) {\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n    }\n    constructor(dialogService, platformId, store, paymentService, router, messageService, shopService, transactionService, transactionDetailsService, orderService, translate, addressService, productLogicService, loaderService, authService, mainDataService, permissionService, $gaService, shipmentService, renderer, cd, _GACustomEvent) {\n      this.dialogService = dialogService;\n      this.platformId = platformId;\n      this.store = store;\n      this.paymentService = paymentService;\n      this.router = router;\n      this.messageService = messageService;\n      this.shopService = shopService;\n      this.transactionService = transactionService;\n      this.transactionDetailsService = transactionDetailsService;\n      this.orderService = orderService;\n      this.translate = translate;\n      this.addressService = addressService;\n      this.productLogicService = productLogicService;\n      this.loaderService = loaderService;\n      this.authService = authService;\n      this.mainDataService = mainDataService;\n      this.permissionService = permissionService;\n      this.$gaService = $gaService;\n      this.shipmentService = shipmentService;\n      this.renderer = renderer;\n      this.cd = cd;\n      this._GACustomEvent = _GACustomEvent;\n      let value = localStorage.getItem('CurrencyDecimal');\n      if (value) this.decimalValue = parseInt(value);\n      this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n      this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n      this.disableCent = localStorage.getItem('DisableCents');\n      this.isFetchOrderPaymentConfig = false;\n    }\n    ngOnInit() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isLayoutTemplate = _this.permissionService.hasPermission('Layout-Template');\n        _this.isGoogleAnalytics = _this.permissionService.hasPermission('Google Analytics');\n        _this.isShipmentFeeExist = _this.permissionService.hasPermission('Shipment-Fee');\n        _this.getOrderData();\n        _this.getCartId();\n        _this.addressSubscription = _this.addressService.getCustomAddress().subscribe(res => {\n          if (res) {\n            _this.getFullOrderData();\n          }\n        });\n        _this.mobileSubscription = _this.addressService.getCustomMobile().subscribe(res => {\n          if (res) {\n            _this.UpdateOrder();\n          }\n        });\n        // this.addressService.addresseData.subscribe((res: any) => {\n        //   if (res) {\n        //     this.getFullOrderData();\n        //   }\n        // })\n        // this.addressService.mobileData.subscribe((res: any) => {\n        //   if (res) {\n        //     this.UpdateOrder();\n        //   }\n        // })\n        _this.showPayButton = false;\n        _this.getCurrentCartId();\n        _this.store.subscription('mainData').subscribe({\n          next: res => {\n            let data = res.find(obj => obj.key.toLocaleLowerCase() === 'LightBoxURL'.toLocaleLowerCase());\n            if (data) _this.lightBoxURL = data.lightBoxURL;\n            data = res.find(obj => obj.key.toLocaleLowerCase() === 'currency'.toLocaleLowerCase());\n            if (data) _this.currency = data.displayName;\n            data = res.find(obj => obj.key.toLocaleLowerCase() === 'countryCode'.toLocaleLowerCase());\n            if (data) _this.requestData.countryCode = data.displayName;\n            data = res.find(obj => obj.key.toLocaleLowerCase() === 'countryphone'.toLocaleLowerCase());\n            if (data) {\n              _this.requestData.dropOffContactInfo.countryCode = data.displayName;\n              _this.requestData.pickupContactInfo.countryCode = data.displayName;\n            }\n          },\n          error: err => {\n            console.error(err);\n          }\n        });\n        _this.sessionId = localStorage.getItem('sessionId');\n        _this.userDetails = _this.store.get('profile');\n        _this.refreshSubscription = _this.refreshSummary.subscribe(() => {\n          _this.getDiscountValue();\n          _this.getFullOrderData();\n        });\n      })();\n    }\n    getOrderData() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.store.subscription('orderData').subscribe({\n          next: res => {\n            if (res) {\n              _this2.orderDetails = res;\n              if (_this2.orderDetails.productDetails.length > 0) {\n                _this2.currencyCode = _this2.orderDetails.productDetails[0].currencyCode;\n              }\n              _this2.getCustomerAddress();\n              _this2.getCustomerPhone();\n            }\n          },\n          error: err => {\n            console.error(err);\n          }\n        });\n      })();\n    }\n    getCustomerAddress() {\n      this.addressService.getAddress().subscribe({\n        next: res => {\n          if (res.data.records.length > 0) {\n            this.addressService.chosenAddress = res.data.records[0];\n            if (!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\" || !this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\") {\n              this.messageService.add({\n                severity: 'info',\n                summary: this.translate.instant('ResponseMessages.address'),\n                detail: this.translate.instant('ResponseMessages.invalidCityAddress')\n              });\n              this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], {\n                queryParams: {\n                  returnUrl: '/checkout'\n                }\n              });\n              return;\n            }\n            if (!this.deliveryOptionDetails && !this.permissionService.hasPermission('Shipment-Fee')) {\n              this.getFullOrderData();\n            }\n          } else {\n            this.messageService.add({\n              severity: 'info',\n              summary: this.translate.instant('ResponseMessages.address'),\n              detail: this.translate.instant('ResponseMessages.pleaseProvideYourAddress')\n            });\n            this.router.navigate(['/account/address'], {\n              queryParams: {\n                returnUrl: '/checkout'\n              }\n            });\n            return;\n          }\n          this.addressService.loadedAddress = true;\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err.message\n          });\n        }\n      });\n    }\n    ngOnChanges() {\n      this.deliveryOptionDetails = this.deliveryOptionDetails?.deliveryOption ? this.deliveryOptionDetails?.deliveryOption : this.deliveryOptionDetails;\n      if (this.deliveryOptionDetails && this.permissionService.hasPermission('Shipment-Fee')) {\n        this.getFullOrderData();\n      }\n    }\n    getFullOrderData() {\n      if (this.deliveryOptionDetails?.id) {\n        let data = {\n          OrderId: this.orderDetails?.orderId,\n          AddressId: this.addressService.chosenAddress.id\n        };\n        data['deliveryOptionId'] = this.deliveryOptionDetails?.id || data['deliveryOptionId'];\n        this.orderService.GetOrderWithPaymentsConfigurations(data).subscribe({\n          next: res => {\n            if (res?.success) {\n              this.isProceccedCheckOut = true;\n              this.isFetchOrderPaymentConfig = true;\n              this.orderDetailsWithConfig = res.data;\n              this.lightBoxURL = res.data.lightBoxURL;\n              this.merchantId = res.data.merchantId;\n              this.terminalId = res.data.terminalId;\n              this.subOrderDetails = res.data.shopsDetails;\n              this.shopsPayments = res.data.shopsPayments;\n              this.totalLogisticsFee = res.data.totalLogisticsFee;\n              this.subOrdersCommission = res.data.SubOrdersCommission;\n              this.AmountTrxn = res.data.amountTrxn;\n              this.OrderId = this.orderDetails.orderId;\n              this.OrderShopId = this.orderDetails.shopId;\n              this.MomoPayCommissionAmount = res.data.momoPayCommissionAmount;\n              this.shipmentService.shipmentCost = res.data.totalDeliveryCost;\n              this.shipmentService.actualShipmentFee = res.data.calculateShipmentFeeRes ? res.data.calculateShipmentFeeRes.adjustedShipmentFee : 0;\n              this.shipmentFee = 0;\n              this.totalDiscount = res.data.totalDiscount;\n              this.allowedpaymentMethod = res.data?.allowedpaymentMethod;\n              if (res.data?.shopsDetails.length) {\n                res.data?.shopsDetails.forEach(item => {\n                  if (item.shipmentFee) {\n                    this.shipmentFee = this.shipmentFee + item.shipmentFee;\n                  }\n                });\n              }\n              this.maxTimeDelivery = res.data.maxTimeDelivery;\n              this.minTimeDelivery = res.data.minTimeDelivery;\n              this.timeString = res.data.timeString;\n              this.shipmentService.currentShipment = {\n                totalDeliveryCost: Number(this.shipmentService.shipmentCost),\n                shipmentDetails: this.shipmentDetails,\n                maxTime: this.maxTimeDelivery,\n                minTime: this.minTimeDelivery\n              };\n              this.showPayButton = true;\n              this.store.set('shipmentCost', {\n                totalDeliveryCost: res.data.totalDeliveryCost,\n                shipmentDetails: this.shipmentDetails,\n                deliveryOption: this.deliveryOptionDetails,\n                isApplyShippingFeeDiscount: res.data.isApplyShippingFeeDiscount\n              });\n              this.UpdateOrder();\n              this.CallLightBox();\n            } else {\n              this.isProceccedCheckOut = false;\n              if (res?.message != null) {\n                if (res.message === 'City is not defined in any region. Please update your address.' || !this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === \"\" || !this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === \"\") {\n                  this.messageService.add({\n                    severity: 'info',\n                    summary: this.translate.instant('ResponseMessages.address'),\n                    detail: this.translate.instant('ResponseMessages.invalidCityAddress')\n                  });\n                  this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], {\n                    queryParams: {\n                      returnUrl: '/checkout'\n                    }\n                  });\n                  return;\n                } else {\n                  this.messageService.add({\n                    severity: 'error',\n                    summary: this.translate.instant('ErrorMessages.fetchError'),\n                    detail: res.message\n                  });\n                }\n              }\n            }\n          },\n          error: err => {\n            this.isProceccedCheckOut = false;\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.fetchError'),\n              detail: this.translate.instant('ErrorMessages.pleaseContactCallCenter')\n            });\n            this.router.navigate(['/cart']);\n          }\n        });\n      }\n    }\n    /**\r\n    * This function handles the display of the Lightbox for the checkout process,\r\n    * logs analytics events if enabled, and performs a series of API calls to validate\r\n    * promotional stock and retrieve phone numbers. Based on the API responses,\r\n    * it determines whether to proceed with the checkout flow or redirect the user back to the cart.\r\n    */\n    show() {\n      if (this.isGoogleAnalytics) {\n        // Log an analytics event for proceeding to payment\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_PROCEED_TO_PAYMENT,\n        // Action enum\n        'checkout',\n        // Category\n        'PROCEED_TO_PAYMENT',\n        // Label\n        1,\n        // Value\n        true,\n        // Non-interaction flag\n        {\n          \"order_amount\": this.shipmentService.currentOrderTotal + this.shipmentService.shipmentCost,\n          \"order_ID\": this.OrderId,\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"session_ID\": this.sessionId,\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n          \"order_commission\": this.MomoPayCommissionAmount,\n          \"merchant_ID\": this.merchantId,\n          \"shipping_fee\": this.shipmentFee,\n          \"order_totalItems\": this.orderDetails?.productDetails?.length,\n          \"payment_method\": this.paymentMethodDetails,\n          \"suborder_commission\": this.subOrdersCommission,\n          \"delivery_option\": this.deliveryOptionDetails.name\n        });\n      }\n      forkJoin([this.authService.getPhoneNumbers(), this.authService.PromotionStockCheck(this.orderDetails.orderId), this.orderService.verifyOrderProductsVisibilityBeforeCheckout({\n        OrderId: this.orderDetails.orderId\n      })]).subscribe({\n        next: ([phoneNumberRes, promotionStockCheck, visibilityCheck]) => {\n          let promotionStockStatus = promotionStockCheck.data.promotionalStockAvailable;\n          let isValidOrder = visibilityCheck?.success;\n          if (!promotionStockStatus) {\n            this.router.navigateByUrl('/cart');\n          }\n          if (!isValidOrder) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.orderIsInValid')\n            });\n            this.router.navigateByUrl('/cart');\n          } else if (phoneNumberRes.success && promotionStockStatus && isValidOrder) {\n            this.displayLoaderModal = true;\n            this.showPayButton = false;\n            if (this.isProceccedCheckOut) {\n              Lightbox.Checkout.showLightbox();\n            }\n          }\n        },\n        error: err => {\n          console.error('Error in API calls', err);\n        },\n        complete: () => {}\n      });\n    }\n    ngAfterViewInit() {\n      // Set up a MutationObserver to watch for changes in the DOM\n      this.observer = new MutationObserver(mutations => {\n        for (let mutation of mutations) {\n          if (mutation.type === 'childList') {\n            Array.from(mutation.addedNodes).forEach(node => {\n              if (node.nodeName === 'IFRAME') {\n                const iframe = node;\n                this.addIframeLoadListener(iframe);\n              }\n            });\n          }\n        }\n      });\n      // Start observing the body for added nodes\n      this.observer.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    }\n    addIframeLoadListener(iframe) {\n      this.displayLoaderModal = true;\n      this.iframeLoadListener = this.renderer.listen(iframe, 'load', () => {\n        this.displayLoaderModal = false;\n        this.cd.detectChanges();\n        this.accessIframeElements(iframe);\n      });\n    }\n    accessIframeElements(iframe) {\n      try {\n        const iframeDocument = iframe.contentDocument || iframe?.contentWindow?.document;\n        const iframeBody = iframeDocument?.body;\n        // Example: Check for a specific element or value inside the iframe\n        const element = iframeDocument?.querySelector('#elementId');\n        if (element) {} else {}\n      } catch (error) {\n        console.error('Error accessing iframe content:', error);\n      }\n    }\n    // ngAfterViewInit() {\n    //   this.iframeInterval = setInterval(() => {\n    //     const iframe = document.querySelector('iframe'); // Adjust the selector to match your iframe\n    //     if (iframe && iframe.contentWindow && iframe.contentDocument) {\n    //       this.loaderService.show();\n    //       this.accessIframeElements(iframe as HTMLIFrameElement);\n    //       clearInterval(this.iframeInterval);\n    //     }\n    //   }, 100);\n    // }\n    // accessIframeElements(iframe: HTMLIFrameElement) {\n    //\n    //   try {\n    //     const iframeDocument = iframe?.contentWindow?.document || iframe.contentDocument;\n    //     const iframeBody = iframeDocument?.body;\n    //\n    //     // Example: Accessing an element inside the iframe\n    //     const element = iframeDocument?.querySelector('#DivInsidemyModal');\n    //     if (element) {\n    //     } else {\n    //     }\n    //   } catch (error) {\n    //     console.error('Error accessing iframe content:', error);\n    //   }\n    // }\n    CallLightBox() {\n      let that = this;\n      let storedLang = localStorage.getItem('lang');\n      get(this.lightBoxURL, () => {\n        if (environment.isStoreCloud) {\n          this.getData(that, storedLang);\n        } else {\n          this.orderService.orderId = this.OrderId;\n          Lightbox.Checkout.configure = {\n            defaultPaymentMethod: this.paymentMethodDetails?.toLocaleLowerCase()?.includes('card') ? PaymentMethodEnum['Card'] : PaymentMethodEnum['Wallet'],\n            momoPaySubMerchantsDataStr: JSON.stringify(this.shopsPayments),\n            MomoPayCommissionAmount: this.MomoPayCommissionAmount,\n            MID: this.merchantId,\n            TID: this.terminalId,\n            lang: storedLang,\n            MerchantReference: this.OrderId ?? '',\n            AmountTrxn: this.AmountTrxn,\n            AdditionalCustomerData: {\n              CustomerMobile: this.userPhoneNumber\n            },\n            MomoPayLogisticFees: this.totalLogisticsFee,\n            MomoPayDiscounts: -this.totalDiscount,\n            paymentMethodFromLightBox: this.totalDiscount ? this.allowedpaymentMethod : PaymentMethodEnum['Both'],\n            completeCallback: function (data) {\n              that.loaderService.hide();\n              let transactionData = {\n                TransactionId: data.SystemReference,\n                CardNumber: data.PayerAccount,\n                PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\n                UpdateProductQuantityList: null,\n                OrderId: this.OrderId,\n                OrderShopId: this.OrderShopId\n              };\n              that.PaymentSuccess(transactionData);\n              that.onDeleteCart();\n              Lightbox.Checkout.closeLightbox();\n              if (data) {\n                that.orderService.updateOrderStatus(that.orderDetails.orderId).pipe(take(1)).subscribe(res => {\n                  that.showPayButton = true;\n                });\n              }\n            },\n            errorCallback: function () {\n              that.loaderService.hide();\n              that.showPayButton = true;\n              Lightbox.Checkout.closeLightbox();\n            },\n            cancelCallback: function () {\n              that.loaderService.hide();\n              that.showPayButton = true;\n              Lightbox.Checkout.closeLightbox();\n            }\n          };\n        }\n      });\n    }\n    PaymentSuccess(transactionData) {\n      this.store.set('transactionData', transactionData);\n      this._GACustomEvent.purchaseEvent(this.cartItems, transactionData.TransactionId, this.shipmentFee, this.currencyCode, this.deliveryOptionDetails?.name, this.getCurrentCouponCode());\n      this.router.navigate(['/checkout/success']);\n    }\n    getCurrentCouponCode() {\n      const promoCodeComponent = document.querySelector('app-promo-code');\n      return promoCodeComponent?.discount || undefined;\n    }\n    getProductShopLatLng(transactionId, shopId, orderId, productId, cost) {\n      this.shopService.getShopById(shopId).subscribe({\n        next: res => {\n          if (res.data === null) {\n            return;\n          }\n          let location = [res.data.lat, res.data.lng];\n          this.requestData.pickupContactInfo.addressLatLng = '[' + location.toString() + ']';\n          this.requestData.dropOffContactInfo.addressLatLng = '[' + [this.addressService.chosenAddress.lat, this.addressService.chosenAddress.lng].toString() + ']';\n        },\n        error: err => {}\n      });\n    }\n    UpdateOrder() {\n      let applyTo = null;\n      let deliveryOption = null;\n      if (this.deliveryOptionDetails) {\n        if (this.deliveryOptionDetails.applyTo) {\n          applyTo = this.deliveryOptionDetails.applyTo;\n        }\n        if (this.deliveryOptionDetails.applyTo) {\n          deliveryOption = this.deliveryOptionDetails.id;\n        }\n      }\n      this.orderService.updateOrder({\n        id: this.orderDetails.orderId,\n        TransferReferenceId: this.paymentResult?.SystemReference,\n        totalDeliveryCost: this.shipmentService.shipmentCost,\n        shipmentFee: this.shipmentFee,\n        applyTo: applyTo,\n        deliveryOption: deliveryOption,\n        total: this.orderDetails.orderAmount + Number(this.shipmentService.shipmentCost) - this.orderDiscount,\n        addressId: this.addressService.chosenAddress.id,\n        subOrderDetails: this.subOrderDetails,\n        StreetAddress: this.addressService.chosenAddress.streetAddress,\n        State: this.addressService.chosenAddress.state,\n        Floor: this.addressService.chosenAddress.floor,\n        BuldingNumber: this.addressService.chosenAddress.buldingNumber,\n        CountryName: this.addressService.chosenAddress.countryName,\n        City: this.addressService.chosenAddress.city,\n        LandMark: this.addressService.chosenAddress.landMark\n      }).subscribe({\n        next: res => {},\n        error: err => {}\n      });\n    }\n    onDeleteCart() {\n      let cartId = localStorage.getItem('cartId');\n      if (!cartId || cartId == '') {\n        cartId = this.cartId;\n      }\n      if (this.isGoogleAnalytics && this.permissionService.getTagFeature('delete_cart')) {\n        this.$gaService.event('delete_cart', 'cart', cartId);\n      }\n      this.mainDataService.setCartLenghtData(0);\n      this.mainDataService.setCartItemsData([]);\n      this.productLogicService.emptyCart(cartId);\n    }\n    getCurrentCartId() {\n      this.store.subscription('cartProducts').subscribe({\n        next: res => {\n          this.cartId = res[0]?.cartId;\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }\n    getData(that, storedLang) {\n      Lightbox.Checkout.configure = {\n        MID: this.merchantId,\n        TID: this.terminalId,\n        lang: storedLang,\n        MerchantReference: this.OrderId ?? '',\n        AmountTrxn: 120,\n        AdditionalCustomerData: {\n          CustomerMobile: this.userPhoneNumber\n        },\n        completeCallback: function (data) {\n          that.loaderService.hide();\n          let transactionData = {\n            TransactionId: data.SystemReference,\n            CardNumber: data.PayerAccount,\n            PaymentMethod: data.PaidThrough == 'Card' ? 1 : 2,\n            UpdateProductQuantityList: null,\n            OrderId: this.OrderId,\n            OrderShopId: this.OrderShopId\n          };\n          that.PaymentSuccess(transactionData);\n          that.onDeleteCart();\n          Lightbox.Checkout.closeLightbox();\n          if (data) {\n            that.orderService.updateOrderStatus(that.orderDetails.orderId).subscribe(res => {\n              that.showPayButton = true;\n            });\n          }\n        },\n        errorCallback: function () {\n          that.loaderService.hide();\n          that.showPayButton = true;\n          Lightbox.Checkout.closeLightbox();\n        },\n        cancelCallback: function () {\n          that.loaderService.hide();\n          that.showPayButton = true;\n          Lightbox.Checkout.closeLightbox();\n        }\n      };\n    }\n    getCustomerPhone() {\n      this.authService.getPhoneNumbers().subscribe({\n        next: res => {\n          if (res.data && res.data.records && res.data.records.length) {\n            // let mobileData = res.data.records.find((obj: any) => obj.isPrimary === true);\n            // if (!mobileData) {\n            //   mobileData =  res.data.records[0];\n            // }\n            this.userPhoneNumber = res.data.records.map(obj => obj.phoneNumber);\n            this.requestData.dropOffContactInfo.phoneNumber = res.data.records.map(obj => obj.phoneNumber);\n          } else {\n            this.userPhoneNumber = localStorage.getItem('phoneNumberArray');\n          }\n        }\n      });\n    }\n    getDiscountValue() {\n      this.orderService.getOrderDiscount(this.orderDetails.orderId).subscribe({\n        next: res => {\n          if (res.success) {\n            this.orderDiscount = res.data >= this.orderDetails.orderAmount ? this.orderDetails.orderAmount : res.data;\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.addressSubscription.unsubscribe();\n      this.mobileSubscription.unsubscribe();\n      if (this.observer) {\n        this.observer.disconnect();\n      }\n      if (this.iframeLoadListener) {\n        this.iframeLoadListener(); // Remove the event listener\n      }\n      // this.addressService.addresses.unsubscribe()\n      if (this.refreshSubscription) {\n        this.refreshSubscription.unsubscribe();\n      }\n    }\n    getCartId() {}\n    static ɵfac = function PaymentCartComponent_Factory(t) {\n      return new (t || PaymentCartComponent)(i0.ɵɵdirectiveInject(i1.DialogService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.PaymentService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i2.ShopService), i0.ɵɵdirectiveInject(i2.TransactionService), i0.ɵɵdirectiveInject(i2.TransactionDetailsService), i0.ɵɵdirectiveInject(i2.OrderService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i2.AddressService), i0.ɵɵdirectiveInject(i2.ProductLogicService), i0.ɵɵdirectiveInject(i2.LoaderService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.ShipmentService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentCartComponent,\n      selectors: [[\"app-payment-cart\"]],\n      hostBindings: function PaymentCartComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function PaymentCartComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        deliveryOptionDetails: \"deliveryOptionDetails\",\n        refreshSummary: \"refreshSummary\",\n        cartItems: \"cartItems\",\n        paymentMethodDetails: \"paymentMethodDetails\",\n        isFetchOrderPaymentConfig: \"isFetchOrderPaymentConfig\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService]), i0.ɵɵNgOnChangesFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"new-payment-cart\", 4, \"ngIf\"], [\"class\", \"old-payment-cart\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"new-payment-cart\"], [4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [1, \"payment-card\", \"d-none\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"px-7\", \"bg-white\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"my-2\", \"second-btn\", 3, \"label\", \"click\"], [1, \"new-checkout-card\"], [1, \"checkout-card\"], [1, \"row\"], [1, \"col-md-12\", \"error-container\"], [1, \"error-msg\"], [1, \"button-container-mobile\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"button-content\"], [1, \"items\"], [1, \"price\"], [1, \"checkout-button\"], [\"fill\", \"none\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"width\", \"20\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M3.125 10L16.875 10\", \"stroke\", \"#F5F7FC\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\"], [\"d\", \"M11.25 15.625L16.875 10L11.25 4.375\", \"stroke\", \"#F5F7FC\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\"], [1, \"payment-card\"], [\"class\", \"payment-card__arrives\", 4, \"ngIf\"], [1, \"payment-card__checkout\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"payment-card__arrives\"], [1, \"ckeckout-count\"], [1, \"old-payment-cart\"], [3, \"displayModal\"]],\n      template: function PaymentCartComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaymentCartComponent_div_0_Template, 8, 5, \"div\", 0);\n          i0.ɵɵtemplate(1, PaymentCartComponent_div_1_Template, 5, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, PaymentCartComponent_ng_container_2_Template, 2, 1, \"ng-container\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.displayLoaderModal);\n        }\n      },\n      dependencies: [i8.NgIf, i8.NgStyle, i9.ButtonDirective, i10.LightboxLoaderModalComponent, i8.DecimalPipe, i5.TranslatePipe],\n      styles: [\".new-payment-cart[_ngcontent-%COMP%]   .payment-card[_ngcontent-%COMP%]{display:flex;padding:0 24px;flex-direction:column;justify-content:center;align-items:center;gap:16px;align-self:stretch;margin-bottom:85px}.new-payment-cart[_ngcontent-%COMP%]   .payment-card__arrives[_ngcontent-%COMP%]{color:#000;font-family:var(--medium-font);font-size:14px;font-style:normal;font-weight:500;line-height:normal}.new-payment-cart[_ngcontent-%COMP%]   .payment-card__checkout[_ngcontent-%COMP%]{width:100%;height:56px;padding:0 24px;justify-content:center;align-self:stretch;border-radius:6px;background:var(--main_bt_txtcolor);color:#fff;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:700;line-height:56px;letter-spacing:.168px;text-transform:uppercase;border:none}.old-payment-cart[_ngcontent-%COMP%]   .arrives-tag[_ngcontent-%COMP%]{font-size:12px;font-weight:500;font-family:var(--medium-font)!important}.old-payment-cart[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]{text-transform:uppercase}@media only screen and (max-width: 786px){.old-payment-cart[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important}}.button-container-mobile[_ngcontent-%COMP%]{display:flex;align-items:center;height:56px;width:100%;padding:0 12px;border-radius:6px;background:var(--main_bt_txtcolor);color:#fff;font-family:var(--medium-font);font-size:14px;font-style:normal;font-weight:700;line-height:56px;letter-spacing:.168px;border:none;justify-content:space-between}.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;line-height:1.7;text-align:start}.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]{font-size:14px;font-family:main-regular;text-transform:lowercase;font-weight:400}.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{font-size:14px;font-family:main-regular;font-weight:700}.button-container-mobile[_ngcontent-%COMP%]   .checkout-button[_ngcontent-%COMP%]{background-color:transparent;border:none;color:#fff;font-size:16px;font-style:normal;font-weight:700;font-family:main-regular;display:inline-flex;width:60%;justify-content:space-between;align-items:center;cursor:pointer}\"]\n    });\n  }\n  return PaymentCartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}