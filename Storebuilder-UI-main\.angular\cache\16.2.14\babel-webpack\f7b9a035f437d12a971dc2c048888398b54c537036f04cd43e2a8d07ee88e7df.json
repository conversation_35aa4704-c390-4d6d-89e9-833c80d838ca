{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MainSliderComponent } from './components/main-slider/main-slider.component';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { BannerComponent } from './components/banner/banner.component';\nimport { CategorySliderComponent } from './components/category-slider/category-slider.component';\nimport { SectionCategoryComponent } from './components/section-category/section.component';\nimport { routes } from \"./routes\";\nimport { register } from 'swiper/element/bundle';\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { CarouselModule } from \"ngx-owl-carousel-o\";\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nregister();\nexport class HomeModule {\n  static #_ = this.ɵfac = function HomeModule_Factory(t) {\n    return new (t || HomeModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HomeModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, TranslateModule, RouterModule.forChild(routes), CarouselModule, InitialModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeModule, {\n    declarations: [MainSliderComponent, IndexComponent, BannerComponent, CategorySliderComponent, SectionCategoryComponent],\n    imports: [CommonModule, SharedModule, TranslateModule, i1.RouterModule, CarouselModule, InitialModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MainSliderComponent", "IndexComponent", "RouterModule", "BannerComponent", "CategorySliderComponent", "SectionCategoryComponent", "routes", "register", "SharedModule", "TranslateModule", "CarouselModule", "InitialModule", "HomeModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\home.module.ts"], "sourcesContent": ["import { NgModule,CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MainSliderComponent } from './components/main-slider/main-slider.component';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport { BannerComponent } from './components/banner/banner.component';\r\nimport {CategorySliderComponent} from './components/category-slider/category-slider.component';\r\nimport {SectionCategoryComponent} from './components/section-category/section.component';\r\nimport {routes} from \"./routes\";\r\nimport { register } from 'swiper/element/bundle';\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {CarouselModule} from \"ngx-owl-carousel-o\";\r\nimport {InitialModule} from \"@shared/modules/initial.module\";\r\nregister();\r\n\r\n@NgModule({\r\n  declarations: [\r\n    MainSliderComponent,\r\n    IndexComponent,\r\n    BannerComponent,\r\n    CategorySliderComponent,\r\n    SectionCategoryComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    TranslateModule,\r\n    RouterModule.forChild(routes),\r\n    CarouselModule,\r\n    InitialModule,\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\r\n\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAAQC,uBAAuB,QAAO,wDAAwD;AAC9F,SAAQC,wBAAwB,QAAO,iDAAiD;AACxF,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,cAAc,QAAO,oBAAoB;AACjD,SAAQC,aAAa,QAAO,gCAAgC;;;AAC5DJ,QAAQ,EAAE;AAqBV,OAAM,MAAOK,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAVnBhB,YAAY,EACZS,YAAY,EACZC,eAAe,EACfP,YAAY,CAACc,QAAQ,CAACV,MAAM,CAAC,EAC7BI,cAAc,EACdC,aAAa;EAAA;;;2EAKJC,UAAU;IAAAK,YAAA,GAjBnBjB,mBAAmB,EACnBC,cAAc,EACdE,eAAe,EACfC,uBAAuB,EACvBC,wBAAwB;IAAAa,OAAA,GAGxBnB,YAAY,EACZS,YAAY,EACZC,eAAe,EAAAU,EAAA,CAAAjB,YAAA,EAEfQ,cAAc,EACdC,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}