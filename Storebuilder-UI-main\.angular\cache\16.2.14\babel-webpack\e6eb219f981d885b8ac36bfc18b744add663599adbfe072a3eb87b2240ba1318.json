{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject } from \"rxjs\";\nimport { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./\";\nexport class TenantService {\n  mainDataService;\n  appDataService;\n  homeService;\n  tenant = new Subject();\n  header = new Subject();\n  isAllowedCached = {\n    isLandingPage: false,\n    isHotDeals: false,\n    isNewDeals: false,\n    isNewArrivals: false,\n    isGlobal: false\n  };\n  constructor(mainDataService, appDataService, homeService) {\n    this.mainDataService = mainDataService;\n    this.appDataService = appDataService;\n    this.homeService = homeService;\n  }\n  setTenant(tenantId) {\n    this.appDataService.clearAppData();\n    this.tenant.next(tenantId);\n  }\n  getTenant() {\n    return this.tenant;\n  }\n  setHeader(tenantId) {\n    this.header.next(tenantId);\n  }\n  getHeader() {\n    return this.header;\n  }\n  getAppConfigurationData(isMobile = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!(yield _this.shouldGetAppDataCache())) {\n        if (_this.isAllowedCached.isLandingPage) {\n          const referer = environment.referer;\n          const allAppConfiguration = _this.mainDataService.getAllAppConfigurations(referer).toPromise();\n          let res = yield Promise.all([allAppConfiguration]);\n          // below condition for initial data is because we are expecting an object but getting array from backend\n          _this.appDataService.initialData = Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData;\n          _this.appDataService.configuration = res[0].data.allConfiguration;\n          _this.appDataService.tenants = res[0].data.allCountryTenants;\n          _this.appDataService.categories = res[0].data.allCategory;\n          _this.appDataService.showRoomConfiguration = res[0].data.tenantShowRoomConfiguration;\n          const appDataCache = {\n            initialData: Array.isArray(res[0].data.initialData) ? res[0].data.initialData[0] : res[0].data.initialData,\n            configuration: res[0].data.allConfiguration,\n            tenants: res[0].data.allCountryTenants,\n            categories: res[0].data.allCategory,\n            showRoomConfiguration: res[0].data.tenantShowRoomConfiguration\n          };\n          // localStorage.setItem('appData_expiry', (new Date).getTime().toString())\n          localStorage.setItem('appData_cache', JSON.stringify(appDataCache));\n        } else {\n          let appDataCache = localStorage.getItem('appData_cache') ?? '';\n          if (!appDataCache || appDataCache === '') {\n            localStorage.removeItem('apiDataVersion');\n            localStorage.removeItem('appData_cache');\n            yield _this.getAppConfigurationData();\n          } else {\n            appDataCache = JSON.parse(appDataCache);\n            _this.appDataService.initialData = appDataCache.initialData;\n            _this.appDataService.configuration = appDataCache.configuration;\n            _this.appDataService.tenants = appDataCache.tenants;\n            _this.appDataService.categories = appDataCache.categories;\n            _this.appDataService.showRoomConfiguration = appDataCache.showRoomConfiguration;\n          }\n        }\n      }\n    })();\n  }\n  getConfigurationWithoutCache(isMobile) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const layoutTemplatePromise = _this2.mainDataService.getLayoutTemplate(isMobile).toPromise();\n      let res = yield Promise.all([layoutTemplatePromise]);\n      _this2.appDataService.layoutTemplate = res[0].data;\n    })();\n  }\n  shouldGetAppDataCache() {\n    return new Promise((resolve, reject) => {\n      const tenantId = localStorage.getItem('tenantId') ?? '';\n      this.homeService.getAppDataVersion(tenantId).subscribe({\n        next: version => {\n          let apiDataVersion = localStorage.getItem('apiDataVersion');\n          if (!apiDataVersion || apiDataVersion === 'null' || apiDataVersion[0] === 'v') {\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\n            this.isAllowedCached.isLandingPage = true;\n            this.isAllowedCached.isBestSeller = true;\n            this.isAllowedCached.isNewArrivals = true;\n            this.isAllowedCached.isHotDeals = true;\n            this.isAllowedCached.isGlobal = true;\n            resolve(false);\n            return false;\n          } else {\n            let versionList = JSON.parse(localStorage.getItem('apiDataVersion') ?? '');\n            if (version.find(item => item.key === 'ApiDataVersion')?.value != versionList.find(item => item.key === 'ApiDataVersion')?.value) {\n              this.isAllowedCached.isLandingPage = true;\n            }\n            if (!versionList.find(item => item.key === 'BestSeller') || version.find(item => item.key === 'BestSeller')?.value != versionList.find(item => item.key === 'BestSeller')?.value) {\n              this.isAllowedCached.isBestSeller = true;\n            }\n            if (!versionList.find(item => item.key === 'NewArrivals') || version.find(item => item.key === 'NewArrivals')?.value != versionList.find(item => item.key === 'NewArrivals')?.value) {\n              this.isAllowedCached.isNewArrivals = true;\n            }\n            if (!versionList.find(item => item.key === 'HotDeals') || version.find(item => item.key === 'HotDeals')?.value != versionList.find(item => item.key === 'HotDeals')?.value) {\n              this.isAllowedCached.isHotDeals = true;\n            }\n            if (!versionList.find(item => item.key === 'Global') || version.find(item => item.key === 'Global')?.value != versionList.find(item => item.key === 'HotDeals')?.value) {\n              this.isAllowedCached.isGlobal = true;\n            }\n            localStorage.setItem('apiDataVersion', JSON.stringify(version));\n            resolve(false);\n            return false;\n          }\n        },\n        error: () => {\n          resolve(false);\n          return false;\n        }\n      });\n    });\n  }\n  static ɵfac = function TenantService_Factory(t) {\n    return new (t || TenantService)(i0.ɵɵinject(i1.MainDataService), i0.ɵɵinject(i1.AppDataService), i0.ɵɵinject(i1.HomeService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TenantService,\n    factory: TenantService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}