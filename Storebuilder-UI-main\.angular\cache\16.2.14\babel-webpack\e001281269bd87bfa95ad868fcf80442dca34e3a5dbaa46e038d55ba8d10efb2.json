{"ast": null, "code": "import { e as elementChildren } from '../shared/utils.mjs';\nfunction Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const elementsSelector = '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]';\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid,\n      isElement\n    } = swiper;\n    const elements = elementChildren(el, elementsSelector);\n    if (swiper.isElement) {\n      elements.push(...elementChildren(swiper.hostEl, elementsSelector));\n    }\n    elements.forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll(`${elementsSelector}, [data-swiper-parallax-rotate]`).forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function (duration) {\n    if (duration === void 0) {\n      duration = swiper.params.speed;\n    }\n    const {\n      el,\n      hostEl\n    } = swiper;\n    const elements = [...el.querySelectorAll(elementsSelector)];\n    if (swiper.isElement) {\n      elements.push(...hostEl.querySelectorAll(elementsSelector));\n    }\n    elements.forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\nexport { Parallax as default };", "map": {"version": 3, "names": ["e", "elementChildren", "Parallax", "_ref", "swiper", "extendParams", "on", "parallax", "enabled", "elementsSelector", "setTransform", "el", "progress", "rtl", "rtlFactor", "p", "getAttribute", "x", "y", "scale", "opacity", "rotate", "isHorizontal", "indexOf", "parseInt", "currentOpacity", "Math", "abs", "style", "transform", "currentScale", "currentRotate", "setTranslate", "slides", "snapGrid", "isElement", "elements", "push", "hostEl", "for<PERSON>ach", "subEl", "slideEl", "slideIndex", "slideProgress", "params", "slidesPerGroup", "<PERSON><PERSON><PERSON><PERSON>iew", "ceil", "length", "min", "max", "querySelectorAll", "setTransition", "duration", "speed", "parallaxEl", "parallaxDuration", "transitionDuration", "watchSlidesProgress", "originalParams", "_swiper", "default"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/swiper/modules/parallax.mjs"], "sourcesContent": ["import { e as elementChildren } from '../shared/utils.mjs';\n\nfunction Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const elementsSelector = '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]';\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid,\n      isElement\n    } = swiper;\n    const elements = elementChildren(el, elementsSelector);\n    if (swiper.isElement) {\n      elements.push(...elementChildren(swiper.hostEl, elementsSelector));\n    }\n    elements.forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll(`${elementsSelector}, [data-swiper-parallax-rotate]`).forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function (duration) {\n    if (duration === void 0) {\n      duration = swiper.params.speed;\n    }\n    const {\n      el,\n      hostEl\n    } = swiper;\n    const elements = [...el.querySelectorAll(elementsSelector)];\n    if (swiper.isElement) {\n      elements.push(...hostEl.querySelectorAll(elementsSelector));\n    }\n    elements.forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\n\nexport { Parallax as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAE1D,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,QAAQ,EAAE;MACRC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG,0IAA0I;EACnK,MAAMC,YAAY,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;IACrC,MAAM;MACJC;IACF,CAAC,GAAGT,MAAM;IACV,MAAMU,SAAS,GAAGD,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B,MAAME,CAAC,GAAGJ,EAAE,CAACK,YAAY,CAAC,sBAAsB,CAAC,IAAI,GAAG;IACxD,IAAIC,CAAC,GAAGN,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,IAAIE,CAAC,GAAGP,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,MAAMG,KAAK,GAAGR,EAAE,CAACK,YAAY,CAAC,4BAA4B,CAAC;IAC3D,MAAMI,OAAO,GAAGT,EAAE,CAACK,YAAY,CAAC,8BAA8B,CAAC;IAC/D,MAAMK,MAAM,GAAGV,EAAE,CAACK,YAAY,CAAC,6BAA6B,CAAC;IAC7D,IAAIC,CAAC,IAAIC,CAAC,EAAE;MACVD,CAAC,GAAGA,CAAC,IAAI,GAAG;MACZC,CAAC,GAAGA,CAAC,IAAI,GAAG;IACd,CAAC,MAAM,IAAId,MAAM,CAACkB,YAAY,CAAC,CAAC,EAAE;MAChCL,CAAC,GAAGF,CAAC;MACLG,CAAC,GAAG,GAAG;IACT,CAAC,MAAM;MACLA,CAAC,GAAGH,CAAC;MACLE,CAAC,GAAG,GAAG;IACT;IACA,IAAIA,CAAC,CAACM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBN,CAAC,GAAI,GAAEO,QAAQ,CAACP,CAAC,EAAE,EAAE,CAAC,GAAGL,QAAQ,GAAGE,SAAU,GAAE;IAClD,CAAC,MAAM;MACLG,CAAC,GAAI,GAAEA,CAAC,GAAGL,QAAQ,GAAGE,SAAU,IAAG;IACrC;IACA,IAAII,CAAC,CAACK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBL,CAAC,GAAI,GAAEM,QAAQ,CAACN,CAAC,EAAE,EAAE,CAAC,GAAGN,QAAS,GAAE;IACtC,CAAC,MAAM;MACLM,CAAC,GAAI,GAAEA,CAAC,GAAGN,QAAS,IAAG;IACzB;IACA,IAAI,OAAOQ,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,EAAE;MACtD,MAAMK,cAAc,GAAGL,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACf,QAAQ,CAAC,CAAC;MACzED,EAAE,CAACiB,KAAK,CAACR,OAAO,GAAGK,cAAc;IACnC;IACA,IAAII,SAAS,GAAI,eAAcZ,CAAE,KAAIC,CAAE,QAAO;IAC9C,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClD,MAAMW,YAAY,GAAGX,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACf,QAAQ,CAAC,CAAC;MACnEiB,SAAS,IAAK,UAASC,YAAa,GAAE;IACxC;IACA,IAAIT,MAAM,IAAI,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI,EAAE;MAC9D,MAAMU,aAAa,GAAGV,MAAM,GAAGT,QAAQ,GAAG,CAAC,CAAC;MAC5CiB,SAAS,IAAK,WAAUE,aAAc,MAAK;IAC7C;IACApB,EAAE,CAACiB,KAAK,CAACC,SAAS,GAAGA,SAAS;EAChC,CAAC;EACD,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJrB,EAAE;MACFsB,MAAM;MACNrB,QAAQ;MACRsB,QAAQ;MACRC;IACF,CAAC,GAAG/B,MAAM;IACV,MAAMgC,QAAQ,GAAGnC,eAAe,CAACU,EAAE,EAAEF,gBAAgB,CAAC;IACtD,IAAIL,MAAM,CAAC+B,SAAS,EAAE;MACpBC,QAAQ,CAACC,IAAI,CAAC,GAAGpC,eAAe,CAACG,MAAM,CAACkC,MAAM,EAAE7B,gBAAgB,CAAC,CAAC;IACpE;IACA2B,QAAQ,CAACG,OAAO,CAACC,KAAK,IAAI;MACxB9B,YAAY,CAAC8B,KAAK,EAAE5B,QAAQ,CAAC;IAC/B,CAAC,CAAC;IACFqB,MAAM,CAACM,OAAO,CAAC,CAACE,OAAO,EAAEC,UAAU,KAAK;MACtC,IAAIC,aAAa,GAAGF,OAAO,CAAC7B,QAAQ;MACpC,IAAIR,MAAM,CAACwC,MAAM,CAACC,cAAc,GAAG,CAAC,IAAIzC,MAAM,CAACwC,MAAM,CAACE,aAAa,KAAK,MAAM,EAAE;QAC9EH,aAAa,IAAIjB,IAAI,CAACqB,IAAI,CAACL,UAAU,GAAG,CAAC,CAAC,GAAG9B,QAAQ,IAAIsB,QAAQ,CAACc,MAAM,GAAG,CAAC,CAAC;MAC/E;MACAL,aAAa,GAAGjB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,GAAG,CAACP,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxDF,OAAO,CAACU,gBAAgB,CAAE,GAAE1C,gBAAiB,iCAAgC,CAAC,CAAC8B,OAAO,CAACC,KAAK,IAAI;QAC9F9B,YAAY,CAAC8B,KAAK,EAAEG,aAAa,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMS,aAAa,GAAG,SAAAA,CAAUC,QAAQ,EAAE;IACxC,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAGjD,MAAM,CAACwC,MAAM,CAACU,KAAK;IAChC;IACA,MAAM;MACJ3C,EAAE;MACF2B;IACF,CAAC,GAAGlC,MAAM;IACV,MAAMgC,QAAQ,GAAG,CAAC,GAAGzB,EAAE,CAACwC,gBAAgB,CAAC1C,gBAAgB,CAAC,CAAC;IAC3D,IAAIL,MAAM,CAAC+B,SAAS,EAAE;MACpBC,QAAQ,CAACC,IAAI,CAAC,GAAGC,MAAM,CAACa,gBAAgB,CAAC1C,gBAAgB,CAAC,CAAC;IAC7D;IACA2B,QAAQ,CAACG,OAAO,CAACgB,UAAU,IAAI;MAC7B,IAAIC,gBAAgB,GAAGhC,QAAQ,CAAC+B,UAAU,CAACvC,YAAY,CAAC,+BAA+B,CAAC,EAAE,EAAE,CAAC,IAAIqC,QAAQ;MACzG,IAAIA,QAAQ,KAAK,CAAC,EAAEG,gBAAgB,GAAG,CAAC;MACxCD,UAAU,CAAC3B,KAAK,CAAC6B,kBAAkB,GAAI,GAAED,gBAAiB,IAAG;IAC/D,CAAC,CAAC;EACJ,CAAC;EACDlD,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACwC,MAAM,CAACrC,QAAQ,CAACC,OAAO,EAAE;IACrCJ,MAAM,CAACwC,MAAM,CAACc,mBAAmB,GAAG,IAAI;IACxCtD,MAAM,CAACuD,cAAc,CAACD,mBAAmB,GAAG,IAAI;EAClD,CAAC,CAAC;EACFpD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAI,CAACF,MAAM,CAACwC,MAAM,CAACrC,QAAQ,CAACC,OAAO,EAAE;IACrCwB,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACF1B,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACwC,MAAM,CAACrC,QAAQ,CAACC,OAAO,EAAE;IACrCwB,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACF1B,EAAE,CAAC,eAAe,EAAE,CAACsD,OAAO,EAAEP,QAAQ,KAAK;IACzC,IAAI,CAACjD,MAAM,CAACwC,MAAM,CAACrC,QAAQ,CAACC,OAAO,EAAE;IACrC4C,aAAa,CAACC,QAAQ,CAAC;EACzB,CAAC,CAAC;AACJ;AAEA,SAASnD,QAAQ,IAAI2D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}