{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, forwardRef, Component, Input, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nfunction NgToggleComponent_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.labelLeftStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.labelChecked, \" \");\n  }\n}\nfunction NgToggleComponent_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.labelRightStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.labelUnchecked, \" \");\n  }\n}\nfunction NgToggleComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgToggleComponent_ng_container_4_span_1_Template, 2, 2, \"span\", 5);\n    i0.ɵɵtemplate(2, NgToggleComponent_ng_container_4_span_2_Template, 2, 2, \"span\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.toggled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.toggled);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"ng-toggle-focused\": a0\n  };\n};\nclass NgToggleConfig {}\nNgToggleConfig.ɵfac = function NgToggleConfig_Factory(t) {\n  return new (t || NgToggleConfig)();\n};\nNgToggleConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgToggleConfig,\n  factory: NgToggleConfig.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgToggleConfig, [{\n    type: Injectable\n  }], null, null);\n})();\nconst DEFAULT_COLOR_CHECKED = '#0099CC';\nconst DEFAULT_COLOR_UNCHECKED = '#e0e0e0';\nconst DEFAULT_LABEL_CHECKED = '';\nconst DEFAULT_LABEL_UNCHECKED = '';\nconst DEFAULT_SWITCH_COLOR = '#fff';\nconst DISABLED_COLOR = '#dbdbdb';\nconst DISABLED_BUTTON_COLOR = 'silver';\nlet nextUniqueId = 0;\nclass NgToggleComponent {\n  constructor(config, _elementRef) {\n    this.config = config;\n    this._elementRef = _elementRef;\n    this.value = this.config.value || true;\n    this.name = this.config.name || '';\n    this.disabled = this.config.disabled || false;\n    this.height = this.config.height || 25;\n    this.width = this.config.width || 45;\n    this.margin = this.config.margin || 2;\n    this.fontSize = this.config.fontSize || undefined;\n    this.speed = this.config.speed || 300;\n    this.color = this.config.color;\n    this.switchColor = this.config.switchColor;\n    this.labels = this.config.labels || true;\n    this.fontColor = this.config.fontColor || undefined;\n    this.values = this.config.values || {\n      checked: true,\n      unchecked: false\n    };\n    this.textAlign = this.config.textAlign || {\n      checked: 'left',\n      unchecked: 'right'\n    };\n    this.id = '';\n    this.ariaLabel = null;\n    this.ariaLabelledby = null;\n    this.cssColors = false;\n    this.change = new EventEmitter();\n    this.valueChange = new EventEmitter();\n    this.onChange = _ => {};\n    this.onTouch = () => {};\n    this._uniqueId = 'ng-toggle-' + ++nextUniqueId;\n    this.id = this.id || this._uniqueId;\n    this.ariaLabel = this.ariaLabel || this.name || this.id;\n  }\n  ngOnInit() {\n    this.setToogle();\n  }\n  onInput(value) {\n    this.value = value;\n    this.onTouch();\n    this.onChange(this.value);\n  }\n  writeValue(value) {\n    this.value = value;\n    this.setToogle();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  setToogle() {\n    const value = this.value;\n    let index = Object.values(this.values).findIndex(el => el == value);\n    if (index > -1) this.toggled = Object.keys(this.values)[index] == 'checked' ? true : false;\n  }\n  ngOnChanges(changes) {\n    for (const propName in changes) {\n      const chng = changes[propName];\n      if (propName == 'value') this.writeValue(chng.currentValue);\n    }\n  }\n  get coreStyle() {\n    return {\n      width: px(this.width),\n      height: px(this.height),\n      transition: `all ${this.speed}ms`,\n      backgroundColor: this.cssColors ? null : this.disabled ? this.colorDisabled : this.colorCurrent,\n      borderRadius: px(Math.round(this.height / 2))\n    };\n  }\n  get buttonRadius() {\n    const radius = this.height - this.margin * 2;\n    return radius > 0 ? radius : 0;\n  }\n  get distance() {\n    return px(this.width - this.height + this.margin);\n  }\n  get buttonStyle() {\n    const transition = `all ${this.speed}ms`;\n    const margin = px(this.margin);\n    const transform = this.toggled ? translate(this.distance, margin) : translate(margin, margin);\n    let background = this.switchColor ? this.switchColorCurrent : null;\n    background = this.disabled ? this.switchColorDisabled : background;\n    return {\n      width: px(this.buttonRadius),\n      height: px(this.buttonRadius),\n      transition,\n      transform,\n      background\n    };\n  }\n  get labelStyle() {\n    return {\n      lineHeight: px(this.height),\n      fontSize: this.fontSize ? px(this.fontSize) : null,\n      color: this.fontColor ? this.fontColorCurrent : null,\n      width: px(this.width - this.buttonRadius - this.margin)\n    };\n  }\n  get labelLeftStyle() {\n    return {\n      ...this.labelStyle,\n      textAlign: this.textAlign.checked || this.textAlign\n    };\n  }\n  get labelRightStyle() {\n    return {\n      ...this.labelStyle,\n      textAlign: this.textAlign.unchecked || this.textAlign\n    };\n  }\n  get colorChecked() {\n    let {\n      color\n    } = this;\n    if (!isObject(color)) {\n      return color || DEFAULT_COLOR_CHECKED;\n    }\n    return get(color, 'checked', DEFAULT_COLOR_CHECKED);\n  }\n  get colorUnchecked() {\n    return get(this.color, 'unchecked', DEFAULT_COLOR_UNCHECKED);\n  }\n  get colorDisabled() {\n    return get(this.color, 'disabled', DISABLED_COLOR);\n  }\n  get colorCurrent() {\n    return this.toggled ? this.colorChecked : this.colorUnchecked;\n  }\n  get labelChecked() {\n    return get(this.labels, 'checked', DEFAULT_LABEL_CHECKED);\n  }\n  get labelUnchecked() {\n    return get(this.labels, 'unchecked', DEFAULT_LABEL_UNCHECKED);\n  }\n  get switchColorChecked() {\n    return get(this.switchColor, 'checked', DEFAULT_SWITCH_COLOR);\n  }\n  get switchColorUnchecked() {\n    return get(this.switchColor, 'unchecked', DEFAULT_SWITCH_COLOR);\n  }\n  get switchColorDisabled() {\n    return get(this.switchColor, 'disabled', DISABLED_BUTTON_COLOR);\n  }\n  get switchColorCurrent() {\n    if (!isObject(this.switchColor)) {\n      return this.switchColor || DEFAULT_SWITCH_COLOR;\n    }\n    return this.toggled ? this.switchColorChecked : this.switchColorUnchecked;\n  }\n  get fontColorChecked() {\n    return get(this.fontColor, 'checked', DEFAULT_SWITCH_COLOR);\n  }\n  get fontColorUnchecked() {\n    return get(this.fontColor, 'unchecked', DEFAULT_SWITCH_COLOR);\n  }\n  get fontColorDisabled() {\n    return get(this.fontColor, 'disabled', DEFAULT_SWITCH_COLOR);\n  }\n  get fontColorCurrent() {\n    if (!isObject(this.fontColor)) {\n      return this.fontColor || DEFAULT_SWITCH_COLOR;\n    }\n    if (this.disabled) {\n      return this.fontColorDisabled;\n    }\n    return this.toggled ? this.fontColorChecked : this.fontColorUnchecked;\n  }\n  get label() {\n    if (this.ariaLabelledby) {\n      return this.ariaLabelledby;\n    }\n    return this.ariaLabel ? null : `${this._uniqueId}-label`;\n  }\n  toggle(event) {\n    const toggled = !this.toggled;\n    this.toggled = toggled;\n    this.value = this.getValue(toggled);\n    this.onTouch();\n    this.onChange(this.value);\n    this.valueChange.emit(this.value);\n  }\n  getValue(key) {\n    return key === true ? this.values['checked'] : this.values['unchecked'];\n  }\n  onFocus(event) {\n    if (!this.focused && event.relatedTarget) {\n      this.focused = true;\n    }\n  }\n  onFocusout(event) {\n    if (!this._elementRef.nativeElement.contains(event.relatedTarget)) {\n      this.focused = false;\n      this.onTouch();\n    }\n  }\n}\nNgToggleComponent.ɵfac = function NgToggleComponent_Factory(t) {\n  return new (t || NgToggleComponent)(i0.ɵɵdirectiveInject(NgToggleConfig), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nNgToggleComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgToggleComponent,\n  selectors: [[\"ng-toggle\"]],\n  inputs: {\n    value: \"value\",\n    name: \"name\",\n    disabled: \"disabled\",\n    height: \"height\",\n    width: \"width\",\n    margin: \"margin\",\n    fontSize: \"fontSize\",\n    speed: \"speed\",\n    color: \"color\",\n    switchColor: \"switchColor\",\n    labels: \"labels\",\n    fontColor: \"fontColor\",\n    values: \"values\",\n    textAlign: \"textAlign\",\n    id: \"id\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"]\n  },\n  outputs: {\n    change: \"change\",\n    valueChange: \"valueChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => NgToggleComponent),\n    multi: true\n  }]), i0.ɵɵNgOnChangesFeature],\n  decls: 5,\n  vars: 16,\n  consts: [[1, \"ng-toggle-switch\", 3, \"for\"], [\"type\", \"checkbox\", \"role\", \"checkbox\", 1, \"ng-toggle-switch-input\", 3, \"checked\", \"disabled\", \"change\", \"focusin\", \"focusout\"], [1, \"ng-toggle-switch-core\", 3, \"ngClass\", \"ngStyle\"], [1, \"ng-toggle-switch-button\", 3, \"ngStyle\"], [4, \"ngIf\"], [\"class\", \"ng-toggle-switch-label ng-toggle-left\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"ng-toggle-switch-label ng-toggle-right\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"ng-toggle-switch-label\", \"ng-toggle-left\", 3, \"ngStyle\"], [1, \"ng-toggle-switch-label\", \"ng-toggle-right\", 3, \"ngStyle\"]],\n  template: function NgToggleComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"label\", 0)(1, \"input\", 1);\n      i0.ɵɵlistener(\"change\", function NgToggleComponent_Template_input_change_1_listener($event) {\n        return ctx.toggle($event);\n      })(\"focusin\", function NgToggleComponent_Template_input_focusin_1_listener($event) {\n        return ctx.onFocus($event);\n      })(\"focusout\", function NgToggleComponent_Template_input_focusout_1_listener($event) {\n        return ctx.onFocusout($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵelement(3, \"div\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, NgToggleComponent_ng_container_4_Template, 3, 2, \"ng-container\", 4);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"for\", ctx.id);\n      i0.ɵɵattribute(\"id\", ctx.label);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"checked\", ctx.value)(\"disabled\", ctx.disabled);\n      i0.ɵɵattribute(\"id\", ctx.id)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.label)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.toggled);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.focused))(\"ngStyle\", ctx.coreStyle);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", ctx.buttonStyle);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.labels);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgStyle],\n  styles: [\"label[_ngcontent-%COMP%]{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.ng-toggle-switch[_ngcontent-%COMP%]{display:inline-block;position:relative;vertical-align:middle;-webkit-user-select:none;user-select:none;font-size:10px;cursor:pointer}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-input[_ngcontent-%COMP%]{opacity:0;position:absolute;width:1px;height:1px}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label[_ngcontent-%COMP%]{position:absolute;top:0;font-weight:600;color:#fff;z-index:1;padding:0 10px;box-sizing:border-box}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label.ng-toggle-left[_ngcontent-%COMP%]{left:0}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-label.ng-toggle-right[_ngcontent-%COMP%]{right:0}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-core[_ngcontent-%COMP%]{display:block;position:relative;box-sizing:border-box;outline:0;margin:0;transition:border-color .3s,background-color .3s;-webkit-user-select:none;user-select:none}.ng-toggle-switch[_ngcontent-%COMP%]   .ng-toggle-switch-core[_ngcontent-%COMP%]   .ng-toggle-switch-button[_ngcontent-%COMP%]{display:block;position:absolute;overflow:hidden;top:0;left:0;border-radius:100%;background-color:#fff;z-index:2}.ng-toggle-switch.disabled[_ngcontent-%COMP%]{pointer-events:none;opacity:.6}.ng-toggle-focused[_ngcontent-%COMP%]{box-shadow:0 0 4px 3px #999}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgToggleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-toggle',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgToggleComponent),\n        multi: true\n      }],\n      template: \"<label class=\\\"ng-toggle-switch\\\" [for]=\\\"id\\\" [attr.id]=\\\"label\\\">\\n  <input\\n    type=\\\"checkbox\\\"\\n    class=\\\"ng-toggle-switch-input\\\"\\n    [checked]=\\\"value\\\"\\n    [disabled]=\\\"disabled\\\"\\n    (change)=\\\"toggle($event)\\\"\\n    (focusin)=\\\"onFocus($event)\\\"\\n    (focusout)=\\\"onFocusout($event)\\\"\\n    [attr.id]=\\\"id\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"label\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-checked]=\\\"toggled\\\"\\n    role=\\\"checkbox\\\"\\n  >\\n  <div\\n    class=\\\"ng-toggle-switch-core\\\"\\n    [ngClass]=\\\"{'ng-toggle-focused': focused}\\\"\\n    [ngStyle]=\\\"coreStyle\\\"\\n  >\\n    <div\\n      class=\\\"ng-toggle-switch-button\\\"\\n      [ngStyle]=\\\"buttonStyle\\\">\\n    </div>\\n  </div>\\n  <ng-container *ngIf=\\\"labels\\\">\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-left\\\"\\n      [ngStyle]=\\\"labelLeftStyle\\\"\\n      *ngIf=\\\"toggled\\\"\\n    >\\n      {{labelChecked}}\\n    </span>\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-right\\\"\\n      [ngStyle]=\\\"labelRightStyle\\\"\\n      *ngIf=\\\"!toggled\\\"\\n    >\\n      {{labelUnchecked}}\\n    </span>\\n  </ng-container>\\n</label>\",\n      styles: [\"label{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.ng-toggle-switch{display:inline-block;position:relative;vertical-align:middle;-webkit-user-select:none;user-select:none;font-size:10px;cursor:pointer}.ng-toggle-switch .ng-toggle-switch-input{opacity:0;position:absolute;width:1px;height:1px}.ng-toggle-switch .ng-toggle-switch-label{position:absolute;top:0;font-weight:600;color:#fff;z-index:1;padding:0 10px;box-sizing:border-box}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-left{left:0}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-right{right:0}.ng-toggle-switch .ng-toggle-switch-core{display:block;position:relative;box-sizing:border-box;outline:0;margin:0;transition:border-color .3s,background-color .3s;-webkit-user-select:none;user-select:none}.ng-toggle-switch .ng-toggle-switch-core .ng-toggle-switch-button{display:block;position:absolute;overflow:hidden;top:0;left:0;border-radius:100%;background-color:#fff;z-index:2}.ng-toggle-switch.disabled{pointer-events:none;opacity:.6}.ng-toggle-focused{box-shadow:0 0 4px 3px #999}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: NgToggleConfig\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    margin: [{\n      type: Input\n    }],\n    fontSize: [{\n      type: Input\n    }],\n    speed: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    switchColor: [{\n      type: Input\n    }],\n    labels: [{\n      type: Input\n    }],\n    fontColor: [{\n      type: Input\n    }],\n    values: [{\n      type: Input\n    }],\n    textAlign: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    change: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\nconst isObject = value => {\n  return typeof value === 'object';\n};\nconst has = (object, key) => {\n  return isObject(object) && object.hasOwnProperty(key);\n};\nconst get = (object, key, defaultValue) => {\n  return has(object, key) ? object[key] : defaultValue;\n};\nconst px = value => {\n  return `${value}px`;\n};\nconst translate = (x, y) => {\n  return `translate(${x}, ${y})`;\n};\nclass NgToggleModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: NgToggleModule,\n      providers: [{\n        provide: NgToggleConfig,\n        useValue: config\n      }]\n    };\n  }\n}\nNgToggleModule.ɵfac = function NgToggleModule_Factory(t) {\n  return new (t || NgToggleModule)();\n};\nNgToggleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgToggleModule\n});\nNgToggleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [NgToggleConfig],\n  imports: [CommonModule]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgToggleModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgToggleComponent],\n      imports: [CommonModule],\n      exports: [NgToggleComponent],\n      providers: [NgToggleConfig]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ng-toogle\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgToggleComponent, NgToggleConfig, NgToggleModule, get, has, isObject, px, translate };\n//# sourceMappingURL=ng-toggle-button.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "forwardRef", "Component", "Input", "Output", "NgModule", "NG_VALUE_ACCESSOR", "i2", "CommonModule", "NgToggleComponent_ng_container_4_span_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "labelLeftStyle", "ɵɵadvance", "ɵɵtextInterpolate1", "labelChecked", "NgToggleComponent_ng_container_4_span_2_Template", "ctx_r2", "labelRightStyle", "labelUnchecked", "NgToggleComponent_ng_container_4_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r0", "toggled", "_c0", "a0", "NgToggleConfig", "ɵfac", "NgToggleConfig_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "DEFAULT_COLOR_CHECKED", "DEFAULT_COLOR_UNCHECKED", "DEFAULT_LABEL_CHECKED", "DEFAULT_LABEL_UNCHECKED", "DEFAULT_SWITCH_COLOR", "DISABLED_COLOR", "DISABLED_BUTTON_COLOR", "nextUniqueId", "NgToggleComponent", "constructor", "config", "_elementRef", "value", "name", "disabled", "height", "width", "margin", "fontSize", "undefined", "speed", "color", "switchColor", "labels", "fontColor", "values", "checked", "unchecked", "textAlign", "id", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cssColors", "change", "valueChange", "onChange", "_", "onTouch", "_uniqueId", "ngOnInit", "setToogle", "onInput", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "index", "Object", "findIndex", "el", "keys", "ngOnChanges", "changes", "propName", "chng", "currentValue", "coreStyle", "px", "transition", "backgroundColor", "colorDisabled", "colorCurrent", "borderRadius", "Math", "round", "buttonRadius", "radius", "distance", "buttonStyle", "transform", "translate", "background", "switchColorCurrent", "switchColorDisabled", "labelStyle", "lineHeight", "fontColorCurrent", "colorChecked", "isObject", "get", "colorUnchecked", "switchColorChecked", "switchColor<PERSON>nch<PERSON>ed", "fontColorChecked", "fontColorUnchecked", "fontColorDisabled", "label", "toggle", "event", "getValue", "emit", "key", "onFocus", "focused", "relatedTarget", "onFocusout", "nativeElement", "contains", "NgToggleComponent_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "aria<PERSON><PERSON><PERSON><PERSON>", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NgToggleComponent_Template", "ɵɵlistener", "NgToggleComponent_Template_input_change_1_listener", "$event", "NgToggleComponent_Template_input_focusin_1_listener", "NgToggleComponent_Template_input_focusout_1_listener", "ɵɵelement", "ɵɵattribute", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "styles", "args", "selector", "providers", "has", "object", "hasOwnProperty", "defaultValue", "x", "y", "NgToggleModule", "forRoot", "ngModule", "useValue", "NgToggleModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ng-toggle-button/fesm2020/ng-toggle-button.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, forwardRef, Component, Input, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass NgToggleConfig {\n}\nNgToggleConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgToggleConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleConfig, decorators: [{\n            type: Injectable\n        }] });\n\nconst DEFAULT_COLOR_CHECKED = '#0099CC';\nconst DEFAULT_COLOR_UNCHECKED = '#e0e0e0';\nconst DEFAULT_LABEL_CHECKED = '';\nconst DEFAULT_LABEL_UNCHECKED = '';\nconst DEFAULT_SWITCH_COLOR = '#fff';\nconst DISABLED_COLOR = '#dbdbdb';\nconst DISABLED_BUTTON_COLOR = 'silver';\nlet nextUniqueId = 0;\nclass NgToggleComponent {\n    constructor(config, _elementRef) {\n        this.config = config;\n        this._elementRef = _elementRef;\n        this.value = this.config.value || true;\n        this.name = this.config.name || '';\n        this.disabled = this.config.disabled || false;\n        this.height = this.config.height || 25;\n        this.width = this.config.width || 45;\n        this.margin = this.config.margin || 2;\n        this.fontSize = this.config.fontSize || undefined;\n        this.speed = this.config.speed || 300;\n        this.color = this.config.color;\n        this.switchColor = this.config.switchColor;\n        this.labels = this.config.labels || true;\n        this.fontColor = this.config.fontColor || undefined;\n        this.values = this.config.values || { checked: true, unchecked: false };\n        this.textAlign = this.config.textAlign || {\n            checked: 'left',\n            unchecked: 'right',\n        };\n        this.id = '';\n        this.ariaLabel = null;\n        this.ariaLabelledby = null;\n        this.cssColors = false;\n        this.change = new EventEmitter();\n        this.valueChange = new EventEmitter();\n        this.onChange = (_) => { };\n        this.onTouch = () => { };\n        this._uniqueId = 'ng-toggle-' + (++nextUniqueId);\n        this.id = this.id || this._uniqueId;\n        this.ariaLabel = this.ariaLabel || this.name || this.id;\n    }\n    ngOnInit() {\n        this.setToogle();\n    }\n    onInput(value) {\n        this.value = value;\n        this.onTouch();\n        this.onChange(this.value);\n    }\n    writeValue(value) {\n        this.value = value;\n        this.setToogle();\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouch = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    setToogle() {\n        const value = this.value;\n        let index = Object.values(this.values).findIndex(el => el == value);\n        if (index > -1)\n            this.toggled = Object.keys(this.values)[index] == 'checked' ? true : false;\n    }\n    ngOnChanges(changes) {\n        for (const propName in changes) {\n            const chng = changes[propName];\n            if (propName == 'value')\n                this.writeValue(chng.currentValue);\n        }\n    }\n    get coreStyle() {\n        return {\n            width: px(this.width),\n            height: px(this.height),\n            transition: `all ${this.speed}ms`,\n            backgroundColor: this.cssColors\n                ? null\n                : (this.disabled ? this.colorDisabled : this.colorCurrent),\n            borderRadius: px(Math.round(this.height / 2))\n        };\n    }\n    get buttonRadius() {\n        const radius = this.height - this.margin * 2;\n        return radius > 0 ? radius : 0;\n    }\n    get distance() {\n        return px(this.width - this.height + this.margin);\n    }\n    get buttonStyle() {\n        const transition = `all ${this.speed}ms`;\n        const margin = px(this.margin);\n        const transform = this.toggled\n            ? translate(this.distance, margin)\n            : translate(margin, margin);\n        let background = this.switchColor\n            ? this.switchColorCurrent\n            : null;\n        background = this.disabled ? this.switchColorDisabled : background;\n        return {\n            width: px(this.buttonRadius),\n            height: px(this.buttonRadius),\n            transition,\n            transform,\n            background,\n        };\n    }\n    get labelStyle() {\n        return {\n            lineHeight: px(this.height),\n            fontSize: this.fontSize ? px(this.fontSize) : null,\n            color: this.fontColor ? this.fontColorCurrent : null,\n            width: px(this.width - this.buttonRadius - this.margin),\n        };\n    }\n    get labelLeftStyle() {\n        return {\n            ...this.labelStyle,\n            textAlign: this.textAlign.checked || this.textAlign\n        };\n    }\n    get labelRightStyle() {\n        return {\n            ...this.labelStyle,\n            textAlign: this.textAlign.unchecked || this.textAlign\n        };\n    }\n    get colorChecked() {\n        let { color } = this;\n        if (!isObject(color)) {\n            return color || DEFAULT_COLOR_CHECKED;\n        }\n        return get(color, 'checked', DEFAULT_COLOR_CHECKED);\n    }\n    get colorUnchecked() {\n        return get(this.color, 'unchecked', DEFAULT_COLOR_UNCHECKED);\n    }\n    get colorDisabled() {\n        return get(this.color, 'disabled', DISABLED_COLOR);\n    }\n    get colorCurrent() {\n        return this.toggled\n            ? this.colorChecked\n            : this.colorUnchecked;\n    }\n    get labelChecked() {\n        return get(this.labels, 'checked', DEFAULT_LABEL_CHECKED);\n    }\n    get labelUnchecked() {\n        return get(this.labels, 'unchecked', DEFAULT_LABEL_UNCHECKED);\n    }\n    get switchColorChecked() {\n        return get(this.switchColor, 'checked', DEFAULT_SWITCH_COLOR);\n    }\n    get switchColorUnchecked() {\n        return get(this.switchColor, 'unchecked', DEFAULT_SWITCH_COLOR);\n    }\n    get switchColorDisabled() {\n        return get(this.switchColor, 'disabled', DISABLED_BUTTON_COLOR);\n    }\n    get switchColorCurrent() {\n        if (!isObject(this.switchColor)) {\n            return this.switchColor || DEFAULT_SWITCH_COLOR;\n        }\n        return this.toggled\n            ? this.switchColorChecked\n            : this.switchColorUnchecked;\n    }\n    get fontColorChecked() {\n        return get(this.fontColor, 'checked', DEFAULT_SWITCH_COLOR);\n    }\n    get fontColorUnchecked() {\n        return get(this.fontColor, 'unchecked', DEFAULT_SWITCH_COLOR);\n    }\n    get fontColorDisabled() {\n        return get(this.fontColor, 'disabled', DEFAULT_SWITCH_COLOR);\n    }\n    get fontColorCurrent() {\n        if (!isObject(this.fontColor)) {\n            return this.fontColor || DEFAULT_SWITCH_COLOR;\n        }\n        if (this.disabled) {\n            return this.fontColorDisabled;\n        }\n        return this.toggled\n            ? this.fontColorChecked\n            : this.fontColorUnchecked;\n    }\n    get label() {\n        if (this.ariaLabelledby) {\n            return this.ariaLabelledby;\n        }\n        return this.ariaLabel ? null : `${this._uniqueId}-label`;\n    }\n    toggle(event) {\n        const toggled = !this.toggled;\n        this.toggled = toggled;\n        this.value = this.getValue(toggled);\n        this.onTouch();\n        this.onChange(this.value);\n        this.valueChange.emit(this.value);\n    }\n    getValue(key) {\n        return key === true ? this.values['checked'] : this.values['unchecked'];\n    }\n    onFocus(event) {\n        if (!this.focused && event.relatedTarget) {\n            this.focused = true;\n        }\n    }\n    onFocusout(event) {\n        if (!this._elementRef.nativeElement.contains(event.relatedTarget)) {\n            this.focused = false;\n            this.onTouch();\n        }\n    }\n}\nNgToggleComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleComponent, deps: [{ token: NgToggleConfig }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nNgToggleComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"15.2.8\", type: NgToggleComponent, selector: \"ng-toggle\", inputs: { value: \"value\", name: \"name\", disabled: \"disabled\", height: \"height\", width: \"width\", margin: \"margin\", fontSize: \"fontSize\", speed: \"speed\", color: \"color\", switchColor: \"switchColor\", labels: \"labels\", fontColor: \"fontColor\", values: \"values\", textAlign: \"textAlign\", id: \"id\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"] }, outputs: { change: \"change\", valueChange: \"valueChange\" }, providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => NgToggleComponent),\n            multi: true\n        }\n    ], usesOnChanges: true, ngImport: i0, template: \"<label class=\\\"ng-toggle-switch\\\" [for]=\\\"id\\\" [attr.id]=\\\"label\\\">\\n  <input\\n    type=\\\"checkbox\\\"\\n    class=\\\"ng-toggle-switch-input\\\"\\n    [checked]=\\\"value\\\"\\n    [disabled]=\\\"disabled\\\"\\n    (change)=\\\"toggle($event)\\\"\\n    (focusin)=\\\"onFocus($event)\\\"\\n    (focusout)=\\\"onFocusout($event)\\\"\\n    [attr.id]=\\\"id\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"label\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-checked]=\\\"toggled\\\"\\n    role=\\\"checkbox\\\"\\n  >\\n  <div\\n    class=\\\"ng-toggle-switch-core\\\"\\n    [ngClass]=\\\"{'ng-toggle-focused': focused}\\\"\\n    [ngStyle]=\\\"coreStyle\\\"\\n  >\\n    <div\\n      class=\\\"ng-toggle-switch-button\\\"\\n      [ngStyle]=\\\"buttonStyle\\\">\\n    </div>\\n  </div>\\n  <ng-container *ngIf=\\\"labels\\\">\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-left\\\"\\n      [ngStyle]=\\\"labelLeftStyle\\\"\\n      *ngIf=\\\"toggled\\\"\\n    >\\n      {{labelChecked}}\\n    </span>\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-right\\\"\\n      [ngStyle]=\\\"labelRightStyle\\\"\\n      *ngIf=\\\"!toggled\\\"\\n    >\\n      {{labelUnchecked}}\\n    </span>\\n  </ng-container>\\n</label>\", styles: [\"label{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.ng-toggle-switch{display:inline-block;position:relative;vertical-align:middle;-webkit-user-select:none;user-select:none;font-size:10px;cursor:pointer}.ng-toggle-switch .ng-toggle-switch-input{opacity:0;position:absolute;width:1px;height:1px}.ng-toggle-switch .ng-toggle-switch-label{position:absolute;top:0;font-weight:600;color:#fff;z-index:1;padding:0 10px;box-sizing:border-box}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-left{left:0}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-right{right:0}.ng-toggle-switch .ng-toggle-switch-core{display:block;position:relative;box-sizing:border-box;outline:0;margin:0;transition:border-color .3s,background-color .3s;-webkit-user-select:none;user-select:none}.ng-toggle-switch .ng-toggle-switch-core .ng-toggle-switch-button{display:block;position:absolute;overflow:hidden;top:0;left:0;border-radius:100%;background-color:#fff;z-index:2}.ng-toggle-switch.disabled{pointer-events:none;opacity:.6}.ng-toggle-focused{box-shadow:0 0 4px 3px #999}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ng-toggle', providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NgToggleComponent),\n                            multi: true\n                        }\n                    ], template: \"<label class=\\\"ng-toggle-switch\\\" [for]=\\\"id\\\" [attr.id]=\\\"label\\\">\\n  <input\\n    type=\\\"checkbox\\\"\\n    class=\\\"ng-toggle-switch-input\\\"\\n    [checked]=\\\"value\\\"\\n    [disabled]=\\\"disabled\\\"\\n    (change)=\\\"toggle($event)\\\"\\n    (focusin)=\\\"onFocus($event)\\\"\\n    (focusout)=\\\"onFocusout($event)\\\"\\n    [attr.id]=\\\"id\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"label\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-checked]=\\\"toggled\\\"\\n    role=\\\"checkbox\\\"\\n  >\\n  <div\\n    class=\\\"ng-toggle-switch-core\\\"\\n    [ngClass]=\\\"{'ng-toggle-focused': focused}\\\"\\n    [ngStyle]=\\\"coreStyle\\\"\\n  >\\n    <div\\n      class=\\\"ng-toggle-switch-button\\\"\\n      [ngStyle]=\\\"buttonStyle\\\">\\n    </div>\\n  </div>\\n  <ng-container *ngIf=\\\"labels\\\">\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-left\\\"\\n      [ngStyle]=\\\"labelLeftStyle\\\"\\n      *ngIf=\\\"toggled\\\"\\n    >\\n      {{labelChecked}}\\n    </span>\\n    <span\\n      class=\\\"ng-toggle-switch-label ng-toggle-right\\\"\\n      [ngStyle]=\\\"labelRightStyle\\\"\\n      *ngIf=\\\"!toggled\\\"\\n    >\\n      {{labelUnchecked}}\\n    </span>\\n  </ng-container>\\n</label>\", styles: [\"label{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}.ng-toggle-switch{display:inline-block;position:relative;vertical-align:middle;-webkit-user-select:none;user-select:none;font-size:10px;cursor:pointer}.ng-toggle-switch .ng-toggle-switch-input{opacity:0;position:absolute;width:1px;height:1px}.ng-toggle-switch .ng-toggle-switch-label{position:absolute;top:0;font-weight:600;color:#fff;z-index:1;padding:0 10px;box-sizing:border-box}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-left{left:0}.ng-toggle-switch .ng-toggle-switch-label.ng-toggle-right{right:0}.ng-toggle-switch .ng-toggle-switch-core{display:block;position:relative;box-sizing:border-box;outline:0;margin:0;transition:border-color .3s,background-color .3s;-webkit-user-select:none;user-select:none}.ng-toggle-switch .ng-toggle-switch-core .ng-toggle-switch-button{display:block;position:absolute;overflow:hidden;top:0;left:0;border-radius:100%;background-color:#fff;z-index:2}.ng-toggle-switch.disabled{pointer-events:none;opacity:.6}.ng-toggle-focused{box-shadow:0 0 4px 3px #999}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: NgToggleConfig }, { type: i0.ElementRef }]; }, propDecorators: { value: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], margin: [{\n                type: Input\n            }], fontSize: [{\n                type: Input\n            }], speed: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], switchColor: [{\n                type: Input\n            }], labels: [{\n                type: Input\n            }], fontColor: [{\n                type: Input\n            }], values: [{\n                type: Input\n            }], textAlign: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], change: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\nconst isObject = (value) => {\n    return typeof value === 'object';\n};\nconst has = (object, key) => {\n    return isObject(object) && object.hasOwnProperty(key);\n};\nconst get = (object, key, defaultValue) => {\n    return has(object, key) ? object[key] : defaultValue;\n};\nconst px = value => {\n    return `${value}px`;\n};\nconst translate = (x, y) => {\n    return `translate(${x}, ${y})`;\n};\n\nclass NgToggleModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: NgToggleModule,\n            providers: [\n                {\n                    provide: NgToggleConfig,\n                    useValue: config\n                }\n            ]\n        };\n    }\n}\nNgToggleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgToggleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleModule, declarations: [NgToggleComponent], imports: [CommonModule], exports: [NgToggleComponent] });\nNgToggleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleModule, providers: [NgToggleConfig], imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.2.8\", ngImport: i0, type: NgToggleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [NgToggleComponent],\n                    imports: [\n                        CommonModule\n                    ],\n                    exports: [NgToggleComponent],\n                    providers: [NgToggleConfig]\n                }]\n        }] });\n\n/*\n * Public API Surface of ng-toogle\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgToggleComponent, NgToggleConfig, NgToggleModule, get, has, isObject, px, translate };\n//# sourceMappingURL=ng-toggle-button.mjs.map\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACxG,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,SAAAC,iDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAIkDZ,EAAE,CAAAc,cAAA,aA0O83B,CAAC;IA1Oj4Bd,EAAE,CAAAe,MAAA,EA0O45B,CAAC;IA1O/5Bf,EAAE,CAAAgB,YAAA,CA0Om6B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GA1Ot6BjB,EAAE,CAAAkB,aAAA;IAAFlB,EAAE,CAAAmB,UAAA,YAAAF,MAAA,CAAAG,cA0O81B,CAAC;IA1Oj2BpB,EAAE,CAAAqB,SAAA,EA0O45B,CAAC;IA1O/5BrB,EAAE,CAAAsB,kBAAA,MAAAL,MAAA,CAAAM,YAAA,KA0O45B,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1O/5BZ,EAAE,CAAAc,cAAA,aA0O4iC,CAAC;IA1O/iCd,EAAE,CAAAe,MAAA,EA0O4kC,CAAC;IA1O/kCf,EAAE,CAAAgB,YAAA,CA0OmlC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAa,MAAA,GA1OtlCzB,EAAE,CAAAkB,aAAA;IAAFlB,EAAE,CAAAmB,UAAA,YAAAM,MAAA,CAAAC,eA0O2gC,CAAC;IA1O9gC1B,EAAE,CAAAqB,SAAA,EA0O4kC,CAAC;IA1O/kCrB,EAAE,CAAAsB,kBAAA,MAAAG,MAAA,CAAAE,cAAA,KA0O4kC,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1O/kCZ,EAAE,CAAA6B,uBAAA,EA0OwvB,CAAC;IA1O3vB7B,EAAE,CAAA8B,UAAA,IAAAnB,gDAAA,iBA0Om6B,CAAC;IA1Ot6BX,EAAE,CAAA8B,UAAA,IAAAN,gDAAA,iBA0OmlC,CAAC;IA1OtlCxB,EAAE,CAAA+B,qBAAA,CA0OsmC,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAoB,MAAA,GA1OzmChC,EAAE,CAAAkB,aAAA;IAAFlB,EAAE,CAAAqB,SAAA,EA0Oq3B,CAAC;IA1Ox3BrB,EAAE,CAAAmB,UAAA,SAAAa,MAAA,CAAAC,OA0Oq3B,CAAC;IA1Ox3BjC,EAAE,CAAAqB,SAAA,EA0OmiC,CAAC;IA1OtiCrB,EAAE,CAAAmB,UAAA,UAAAa,MAAA,CAAAC,OA0OmiC,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAA,qBAAAA;EAAA;AAAA;AA5OvoC,MAAMC,cAAc,CAAC;AAErBA,cAAc,CAACC,IAAI,YAAAC,uBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFH,cAAc;AAAA,CAAoD;AAC7KA,cAAc,CAACI,KAAK,kBAD6ExC,EAAE,CAAAyC,kBAAA;EAAAC,KAAA,EACYN,cAAc;EAAAO,OAAA,EAAdP,cAAc,CAAAC;AAAA,EAAG;AAChI;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAFiG5C,EAAE,CAAA6C,iBAAA,CAERT,cAAc,EAAc,CAAC;IAC5GU,IAAI,EAAE7C;EACV,CAAC,CAAC;AAAA;AAEV,MAAM8C,qBAAqB,GAAG,SAAS;AACvC,MAAMC,uBAAuB,GAAG,SAAS;AACzC,MAAMC,qBAAqB,GAAG,EAAE;AAChC,MAAMC,uBAAuB,GAAG,EAAE;AAClC,MAAMC,oBAAoB,GAAG,MAAM;AACnC,MAAMC,cAAc,GAAG,SAAS;AAChC,MAAMC,qBAAqB,GAAG,QAAQ;AACtC,IAAIC,YAAY,GAAG,CAAC;AACpB,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAE;IAC7B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,KAAK,GAAG,IAAI,CAACF,MAAM,CAACE,KAAK,IAAI,IAAI;IACtC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACH,MAAM,CAACG,IAAI,IAAI,EAAE;IAClC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,MAAM,CAACI,QAAQ,IAAI,KAAK;IAC7C,IAAI,CAACC,MAAM,GAAG,IAAI,CAACL,MAAM,CAACK,MAAM,IAAI,EAAE;IACtC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACN,MAAM,CAACM,KAAK,IAAI,EAAE;IACpC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACP,MAAM,CAACO,MAAM,IAAI,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACR,MAAM,CAACQ,QAAQ,IAAIC,SAAS;IACjD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACV,MAAM,CAACU,KAAK,IAAI,GAAG;IACrC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACX,MAAM,CAACW,KAAK;IAC9B,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,MAAM,CAACY,WAAW;IAC1C,IAAI,CAACC,MAAM,GAAG,IAAI,CAACb,MAAM,CAACa,MAAM,IAAI,IAAI;IACxC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACd,MAAM,CAACc,SAAS,IAAIL,SAAS;IACnD,IAAI,CAACM,MAAM,GAAG,IAAI,CAACf,MAAM,CAACe,MAAM,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAM,CAAC;IACvE,IAAI,CAACC,SAAS,GAAG,IAAI,CAAClB,MAAM,CAACkB,SAAS,IAAI;MACtCF,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE;IACf,CAAC;IACD,IAAI,CAACE,EAAE,GAAG,EAAE;IACZ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI9E,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC+E,WAAW,GAAG,IAAI/E,YAAY,CAAC,CAAC;IACrC,IAAI,CAACgF,QAAQ,GAAIC,CAAC,IAAK,CAAE,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAG,MAAM,CAAE,CAAC;IACxB,IAAI,CAACC,SAAS,GAAG,YAAY,GAAI,EAAE/B,YAAa;IAChD,IAAI,CAACsB,EAAE,GAAG,IAAI,CAACA,EAAE,IAAI,IAAI,CAACS,SAAS;IACnC,IAAI,CAACR,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI,CAACjB,IAAI,IAAI,IAAI,CAACgB,EAAE;EAC3D;EACAU,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,OAAOA,CAAC7B,KAAK,EAAE;IACX,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACyB,OAAO,CAAC,CAAC;IACd,IAAI,CAACF,QAAQ,CAAC,IAAI,CAACvB,KAAK,CAAC;EAC7B;EACA8B,UAAUA,CAAC9B,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4B,SAAS,CAAC,CAAC;EACpB;EACAG,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACT,QAAQ,GAAGS,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACP,OAAO,GAAGO,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACjC,QAAQ,GAAGiC,UAAU;EAC9B;EACAP,SAASA,CAAA,EAAG;IACR,MAAM5B,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIoC,KAAK,GAAGC,MAAM,CAACxB,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC,CAACyB,SAAS,CAACC,EAAE,IAAIA,EAAE,IAAIvC,KAAK,CAAC;IACnE,IAAIoC,KAAK,GAAG,CAAC,CAAC,EACV,IAAI,CAAC9D,OAAO,GAAG+D,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC3B,MAAM,CAAC,CAACuB,KAAK,CAAC,IAAI,SAAS,GAAG,IAAI,GAAG,KAAK;EAClF;EACAK,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,MAAMC,QAAQ,IAAID,OAAO,EAAE;MAC5B,MAAME,IAAI,GAAGF,OAAO,CAACC,QAAQ,CAAC;MAC9B,IAAIA,QAAQ,IAAI,OAAO,EACnB,IAAI,CAACb,UAAU,CAACc,IAAI,CAACC,YAAY,CAAC;IAC1C;EACJ;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO;MACH1C,KAAK,EAAE2C,EAAE,CAAC,IAAI,CAAC3C,KAAK,CAAC;MACrBD,MAAM,EAAE4C,EAAE,CAAC,IAAI,CAAC5C,MAAM,CAAC;MACvB6C,UAAU,EAAG,OAAM,IAAI,CAACxC,KAAM,IAAG;MACjCyC,eAAe,EAAE,IAAI,CAAC7B,SAAS,GACzB,IAAI,GACH,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAACgD,aAAa,GAAG,IAAI,CAACC,YAAa;MAC9DC,YAAY,EAAEL,EAAE,CAACM,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnD,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC;EACL;EACA,IAAIoD,YAAYA,CAAA,EAAG;IACf,MAAMC,MAAM,GAAG,IAAI,CAACrD,MAAM,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC;IAC5C,OAAOmD,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;EAClC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAOV,EAAE,CAAC,IAAI,CAAC3C,KAAK,GAAG,IAAI,CAACD,MAAM,GAAG,IAAI,CAACE,MAAM,CAAC;EACrD;EACA,IAAIqD,WAAWA,CAAA,EAAG;IACd,MAAMV,UAAU,GAAI,OAAM,IAAI,CAACxC,KAAM,IAAG;IACxC,MAAMH,MAAM,GAAG0C,EAAE,CAAC,IAAI,CAAC1C,MAAM,CAAC;IAC9B,MAAMsD,SAAS,GAAG,IAAI,CAACrF,OAAO,GACxBsF,SAAS,CAAC,IAAI,CAACH,QAAQ,EAAEpD,MAAM,CAAC,GAChCuD,SAAS,CAACvD,MAAM,EAAEA,MAAM,CAAC;IAC/B,IAAIwD,UAAU,GAAG,IAAI,CAACnD,WAAW,GAC3B,IAAI,CAACoD,kBAAkB,GACvB,IAAI;IACVD,UAAU,GAAG,IAAI,CAAC3D,QAAQ,GAAG,IAAI,CAAC6D,mBAAmB,GAAGF,UAAU;IAClE,OAAO;MACHzD,KAAK,EAAE2C,EAAE,CAAC,IAAI,CAACQ,YAAY,CAAC;MAC5BpD,MAAM,EAAE4C,EAAE,CAAC,IAAI,CAACQ,YAAY,CAAC;MAC7BP,UAAU;MACVW,SAAS;MACTE;IACJ,CAAC;EACL;EACA,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO;MACHC,UAAU,EAAElB,EAAE,CAAC,IAAI,CAAC5C,MAAM,CAAC;MAC3BG,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAGyC,EAAE,CAAC,IAAI,CAACzC,QAAQ,CAAC,GAAG,IAAI;MAClDG,KAAK,EAAE,IAAI,CAACG,SAAS,GAAG,IAAI,CAACsD,gBAAgB,GAAG,IAAI;MACpD9D,KAAK,EAAE2C,EAAE,CAAC,IAAI,CAAC3C,KAAK,GAAG,IAAI,CAACmD,YAAY,GAAG,IAAI,CAAClD,MAAM;IAC1D,CAAC;EACL;EACA,IAAI5C,cAAcA,CAAA,EAAG;IACjB,OAAO;MACH,GAAG,IAAI,CAACuG,UAAU;MAClBhD,SAAS,EAAE,IAAI,CAACA,SAAS,CAACF,OAAO,IAAI,IAAI,CAACE;IAC9C,CAAC;EACL;EACA,IAAIjD,eAAeA,CAAA,EAAG;IAClB,OAAO;MACH,GAAG,IAAI,CAACiG,UAAU;MAClBhD,SAAS,EAAE,IAAI,CAACA,SAAS,CAACD,SAAS,IAAI,IAAI,CAACC;IAChD,CAAC;EACL;EACA,IAAImD,YAAYA,CAAA,EAAG;IACf,IAAI;MAAE1D;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI,CAAC2D,QAAQ,CAAC3D,KAAK,CAAC,EAAE;MAClB,OAAOA,KAAK,IAAIrB,qBAAqB;IACzC;IACA,OAAOiF,GAAG,CAAC5D,KAAK,EAAE,SAAS,EAAErB,qBAAqB,CAAC;EACvD;EACA,IAAIkF,cAAcA,CAAA,EAAG;IACjB,OAAOD,GAAG,CAAC,IAAI,CAAC5D,KAAK,EAAE,WAAW,EAAEpB,uBAAuB,CAAC;EAChE;EACA,IAAI6D,aAAaA,CAAA,EAAG;IAChB,OAAOmB,GAAG,CAAC,IAAI,CAAC5D,KAAK,EAAE,UAAU,EAAEhB,cAAc,CAAC;EACtD;EACA,IAAI0D,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC7E,OAAO,GACb,IAAI,CAAC6F,YAAY,GACjB,IAAI,CAACG,cAAc;EAC7B;EACA,IAAI1G,YAAYA,CAAA,EAAG;IACf,OAAOyG,GAAG,CAAC,IAAI,CAAC1D,MAAM,EAAE,SAAS,EAAErB,qBAAqB,CAAC;EAC7D;EACA,IAAItB,cAAcA,CAAA,EAAG;IACjB,OAAOqG,GAAG,CAAC,IAAI,CAAC1D,MAAM,EAAE,WAAW,EAAEpB,uBAAuB,CAAC;EACjE;EACA,IAAIgF,kBAAkBA,CAAA,EAAG;IACrB,OAAOF,GAAG,CAAC,IAAI,CAAC3D,WAAW,EAAE,SAAS,EAAElB,oBAAoB,CAAC;EACjE;EACA,IAAIgF,oBAAoBA,CAAA,EAAG;IACvB,OAAOH,GAAG,CAAC,IAAI,CAAC3D,WAAW,EAAE,WAAW,EAAElB,oBAAoB,CAAC;EACnE;EACA,IAAIuE,mBAAmBA,CAAA,EAAG;IACtB,OAAOM,GAAG,CAAC,IAAI,CAAC3D,WAAW,EAAE,UAAU,EAAEhB,qBAAqB,CAAC;EACnE;EACA,IAAIoE,kBAAkBA,CAAA,EAAG;IACrB,IAAI,CAACM,QAAQ,CAAC,IAAI,CAAC1D,WAAW,CAAC,EAAE;MAC7B,OAAO,IAAI,CAACA,WAAW,IAAIlB,oBAAoB;IACnD;IACA,OAAO,IAAI,CAAClB,OAAO,GACb,IAAI,CAACiG,kBAAkB,GACvB,IAAI,CAACC,oBAAoB;EACnC;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAOJ,GAAG,CAAC,IAAI,CAACzD,SAAS,EAAE,SAAS,EAAEpB,oBAAoB,CAAC;EAC/D;EACA,IAAIkF,kBAAkBA,CAAA,EAAG;IACrB,OAAOL,GAAG,CAAC,IAAI,CAACzD,SAAS,EAAE,WAAW,EAAEpB,oBAAoB,CAAC;EACjE;EACA,IAAImF,iBAAiBA,CAAA,EAAG;IACpB,OAAON,GAAG,CAAC,IAAI,CAACzD,SAAS,EAAE,UAAU,EAAEpB,oBAAoB,CAAC;EAChE;EACA,IAAI0E,gBAAgBA,CAAA,EAAG;IACnB,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACxD,SAAS,CAAC,EAAE;MAC3B,OAAO,IAAI,CAACA,SAAS,IAAIpB,oBAAoB;IACjD;IACA,IAAI,IAAI,CAACU,QAAQ,EAAE;MACf,OAAO,IAAI,CAACyE,iBAAiB;IACjC;IACA,OAAO,IAAI,CAACrG,OAAO,GACb,IAAI,CAACmG,gBAAgB,GACrB,IAAI,CAACC,kBAAkB;EACjC;EACA,IAAIE,KAAKA,CAAA,EAAG;IACR,IAAI,IAAI,CAACzD,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc;IAC9B;IACA,OAAO,IAAI,CAACD,SAAS,GAAG,IAAI,GAAI,GAAE,IAAI,CAACQ,SAAU,QAAO;EAC5D;EACAmD,MAAMA,CAACC,KAAK,EAAE;IACV,MAAMxG,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC7B,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0B,KAAK,GAAG,IAAI,CAAC+E,QAAQ,CAACzG,OAAO,CAAC;IACnC,IAAI,CAACmD,OAAO,CAAC,CAAC;IACd,IAAI,CAACF,QAAQ,CAAC,IAAI,CAACvB,KAAK,CAAC;IACzB,IAAI,CAACsB,WAAW,CAAC0D,IAAI,CAAC,IAAI,CAAChF,KAAK,CAAC;EACrC;EACA+E,QAAQA,CAACE,GAAG,EAAE;IACV,OAAOA,GAAG,KAAK,IAAI,GAAG,IAAI,CAACpE,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAACA,MAAM,CAAC,WAAW,CAAC;EAC3E;EACAqE,OAAOA,CAACJ,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACK,OAAO,IAAIL,KAAK,CAACM,aAAa,EAAE;MACtC,IAAI,CAACD,OAAO,GAAG,IAAI;IACvB;EACJ;EACAE,UAAUA,CAACP,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAAC/E,WAAW,CAACuF,aAAa,CAACC,QAAQ,CAACT,KAAK,CAACM,aAAa,CAAC,EAAE;MAC/D,IAAI,CAACD,OAAO,GAAG,KAAK;MACpB,IAAI,CAAC1D,OAAO,CAAC,CAAC;IAClB;EACJ;AACJ;AACA7B,iBAAiB,CAAClB,IAAI,YAAA8G,0BAAA5G,CAAA;EAAA,YAAAA,CAAA,IAAwFgB,iBAAiB,EAnO9BvD,EAAE,CAAAoJ,iBAAA,CAmO8ChH,cAAc,GAnO9DpC,EAAE,CAAAoJ,iBAAA,CAmOyEpJ,EAAE,CAACqJ,UAAU;AAAA,CAA4C;AACrO9F,iBAAiB,CAAC+F,IAAI,kBApO2EtJ,EAAE,CAAAuJ,iBAAA;EAAAzG,IAAA,EAoODS,iBAAiB;EAAAiG,SAAA;EAAAC,MAAA;IAAA9F,KAAA;IAAAC,IAAA;IAAAC,QAAA;IAAAC,MAAA;IAAAC,KAAA;IAAAC,MAAA;IAAAC,QAAA;IAAAE,KAAA;IAAAC,KAAA;IAAAC,WAAA;IAAAC,MAAA;IAAAC,SAAA;IAAAC,MAAA;IAAAG,SAAA;IAAAC,EAAA;IAAAC,SAAA;IAAAC,cAAA;IAAA4E,eAAA;EAAA;EAAAC,OAAA;IAAA3E,MAAA;IAAAC,WAAA;EAAA;EAAA2E,QAAA,GApOlB5J,EAAE,CAAA6J,kBAAA,CAoO4iB,CACvoB;IACIC,OAAO,EAAEtJ,iBAAiB;IAC1BuJ,WAAW,EAAE5J,UAAU,CAAC,MAAMoD,iBAAiB,CAAC;IAChDyG,KAAK,EAAE;EACX,CAAC,CACJ,GA1O4FhK,EAAE,CAAAiK,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAA1J,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFZ,EAAE,CAAAc,cAAA,cA0OoB,CAAC,cAAD,CAAC;MA1OvBd,EAAE,CAAAuK,UAAA,oBAAAC,mDAAAC,MAAA;QAAA,OA0OmK5J,GAAA,CAAA2H,MAAA,CAAAiC,MAAa,CAAC;MAAA,CAAC,CAAC,qBAAAC,oDAAAD,MAAA;QAAA,OAAkB5J,GAAA,CAAAgI,OAAA,CAAA4B,MAAc,CAAC;MAAA,CAAlC,CAAC,sBAAAE,qDAAAF,MAAA;QAAA,OAAsD5J,GAAA,CAAAmI,UAAA,CAAAyB,MAAiB,CAAC;MAAA,CAAzE,CAAC;MA1OrLzK,EAAE,CAAAgB,YAAA,CA0Oye,CAAC;MA1O5ehB,EAAE,CAAAc,cAAA,YA0O0mB,CAAC;MA1O7mBd,EAAE,CAAA4K,SAAA,YA0O2sB,CAAC;MA1O9sB5K,EAAE,CAAAgB,YAAA,CA0OqtB,CAAC;MA1OxtBhB,EAAE,CAAA8B,UAAA,IAAAF,yCAAA,yBA0OsmC,CAAC;MA1OzmC5B,EAAE,CAAAgB,YAAA,CA0OgnC,CAAC;IAAA;IAAA,IAAAJ,EAAA;MA1OnnCZ,EAAE,CAAAmB,UAAA,QAAAN,GAAA,CAAA+D,EA0OD,CAAC;MA1OF5E,EAAE,CAAA6K,WAAA,OAAAhK,GAAA,CAAA0H,KA0OmB,CAAC;MA1OtBvI,EAAE,CAAAqB,SAAA,EA0OoH,CAAC;MA1OvHrB,EAAE,CAAAmB,UAAA,YAAAN,GAAA,CAAA8C,KA0OoH,CAAC,aAAA9C,GAAA,CAAAgD,QAAD,CAAC;MA1OvH7D,EAAE,CAAA6K,WAAA,OAAAhK,GAAA,CAAA+D,EA0OkR,CAAC,SAAA/D,GAAA,CAAA+C,IAAD,CAAC,eAAA/C,GAAA,CAAAgE,SAAD,CAAC,oBAAAhE,GAAA,CAAA0H,KAAD,CAAC,qBAAA1H,GAAA,CAAA6I,eAAD,CAAC,iBAAA7I,GAAA,CAAAoB,OAAD,CAAC;MA1OrRjC,EAAE,CAAAqB,SAAA,EA0OwkB,CAAC;MA1O3kBrB,EAAE,CAAAmB,UAAA,YAAFnB,EAAE,CAAA8K,eAAA,KAAA5I,GAAA,EAAArB,GAAA,CAAAiI,OAAA,CA0OwkB,CAAC,YAAAjI,GAAA,CAAA4F,SAAD,CAAC;MA1O3kBzG,EAAE,CAAAqB,SAAA,EA0O8rB,CAAC;MA1OjsBrB,EAAE,CAAAmB,UAAA,YAAAN,GAAA,CAAAwG,WA0O8rB,CAAC;MA1OjsBrH,EAAE,CAAAqB,SAAA,EA0OqvB,CAAC;MA1OxvBrB,EAAE,CAAAmB,UAAA,SAAAN,GAAA,CAAAyD,MA0OqvB,CAAC;IAAA;EAAA;EAAAyG,YAAA,GAAs/CtK,EAAE,CAACuK,OAAO,EAAoFvK,EAAE,CAACwK,IAAI,EAA6FxK,EAAE,CAACyK,OAAO;EAAAC,MAAA;AAAA,EAAkD;AAC7kF;EAAA,QAAAvI,SAAA,oBAAAA,SAAA,KA3OiG5C,EAAE,CAAA6C,iBAAA,CA2ORU,iBAAiB,EAAc,CAAC;IAC/GT,IAAI,EAAE1C,SAAS;IACfgL,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAEC,SAAS,EAAE,CAC/B;QACIxB,OAAO,EAAEtJ,iBAAiB;QAC1BuJ,WAAW,EAAE5J,UAAU,CAAC,MAAMoD,iBAAiB,CAAC;QAChDyG,KAAK,EAAE;MACX,CAAC,CACJ;MAAEK,QAAQ,EAAE,iqCAAiqC;MAAEc,MAAM,EAAE,CAAC,kkCAAkkC;IAAE,CAAC;EAC1wE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErI,IAAI,EAAEV;IAAe,CAAC,EAAE;MAAEU,IAAI,EAAE9C,EAAE,CAACqJ;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE1F,KAAK,EAAE,CAAC;MACnHb,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEuD,IAAI,EAAE,CAAC;MACPd,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEwD,QAAQ,EAAE,CAAC;MACXf,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEyD,MAAM,EAAE,CAAC;MACThB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAE0D,KAAK,EAAE,CAAC;MACRjB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAE2D,MAAM,EAAE,CAAC;MACTlB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAE4D,QAAQ,EAAE,CAAC;MACXnB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAE8D,KAAK,EAAE,CAAC;MACRrB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAE+D,KAAK,EAAE,CAAC;MACRtB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEgE,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEiE,MAAM,EAAE,CAAC;MACTxB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEkE,SAAS,EAAE,CAAC;MACZzB,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEmE,MAAM,EAAE,CAAC;MACT1B,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEsE,SAAS,EAAE,CAAC;MACZ7B,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEuE,EAAE,EAAE,CAAC;MACL9B,IAAI,EAAEzC;IACV,CAAC,CAAC;IAAEwE,SAAS,EAAE,CAAC;MACZ/B,IAAI,EAAEzC,KAAK;MACX+K,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEtG,cAAc,EAAE,CAAC;MACjBhC,IAAI,EAAEzC,KAAK;MACX+K,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE1B,eAAe,EAAE,CAAC;MAClB5G,IAAI,EAAEzC,KAAK;MACX+K,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEpG,MAAM,EAAE,CAAC;MACTlC,IAAI,EAAExC;IACV,CAAC,CAAC;IAAE2E,WAAW,EAAE,CAAC;MACdnC,IAAI,EAAExC;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyH,QAAQ,GAAIpE,KAAK,IAAK;EACxB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC,CAAC;AACD,MAAM4H,GAAG,GAAGA,CAACC,MAAM,EAAE5C,GAAG,KAAK;EACzB,OAAOb,QAAQ,CAACyD,MAAM,CAAC,IAAIA,MAAM,CAACC,cAAc,CAAC7C,GAAG,CAAC;AACzD,CAAC;AACD,MAAMZ,GAAG,GAAGA,CAACwD,MAAM,EAAE5C,GAAG,EAAE8C,YAAY,KAAK;EACvC,OAAOH,GAAG,CAACC,MAAM,EAAE5C,GAAG,CAAC,GAAG4C,MAAM,CAAC5C,GAAG,CAAC,GAAG8C,YAAY;AACxD,CAAC;AACD,MAAMhF,EAAE,GAAG/C,KAAK,IAAI;EAChB,OAAQ,GAAEA,KAAM,IAAG;AACvB,CAAC;AACD,MAAM4D,SAAS,GAAGA,CAACoE,CAAC,EAAEC,CAAC,KAAK;EACxB,OAAQ,aAAYD,CAAE,KAAIC,CAAE,GAAE;AAClC,CAAC;AAED,MAAMC,cAAc,CAAC;EACjB,OAAOC,OAAOA,CAACrI,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHsI,QAAQ,EAAEF,cAAc;MACxBP,SAAS,EAAE,CACP;QACIxB,OAAO,EAAE1H,cAAc;QACvB4J,QAAQ,EAAEvI;MACd,CAAC;IAET,CAAC;EACL;AACJ;AACAoI,cAAc,CAACxJ,IAAI,YAAA4J,uBAAA1J,CAAA;EAAA,YAAAA,CAAA,IAAwFsJ,cAAc;AAAA,CAAkD;AAC3KA,cAAc,CAACK,IAAI,kBA9T8ElM,EAAE,CAAAmM,gBAAA;EAAArJ,IAAA,EA8TS+I;AAAc,EAA6F;AACvNA,cAAc,CAACO,IAAI,kBA/T8EpM,EAAE,CAAAqM,gBAAA;EAAAf,SAAA,EA+ToC,CAAClJ,cAAc,CAAC;EAAAkK,OAAA,GAAY5L,YAAY;AAAA,EAAI;AACnL;EAAA,QAAAkC,SAAA,oBAAAA,SAAA,KAhUiG5C,EAAE,CAAA6C,iBAAA,CAgURgJ,cAAc,EAAc,CAAC;IAC5G/I,IAAI,EAAEvC,QAAQ;IACd6K,IAAI,EAAE,CAAC;MACCmB,YAAY,EAAE,CAAChJ,iBAAiB,CAAC;MACjC+I,OAAO,EAAE,CACL5L,YAAY,CACf;MACD8L,OAAO,EAAE,CAACjJ,iBAAiB,CAAC;MAC5B+H,SAAS,EAAE,CAAClJ,cAAc;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASmB,iBAAiB,EAAEnB,cAAc,EAAEyJ,cAAc,EAAE7D,GAAG,EAAEuD,GAAG,EAAExD,QAAQ,EAAErB,EAAE,EAAEa,SAAS;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}