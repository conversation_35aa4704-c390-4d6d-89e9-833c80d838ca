{"ast": null, "code": "import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-google-analytics\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@core/services/custom-GA.service\";\nexport class GAImpressionDirective {\n  constructor(el, googleAnalytics, permissionService, location, _GACustomEvent) {\n    this.el = el;\n    this.googleAnalytics = googleAnalytics;\n    this.permissionService = permissionService;\n    this.location = location;\n    this._GACustomEvent = _GACustomEvent;\n    this.tagName = GaLocalActionEnum;\n    this.isGoogleAnalytics = false;\n    this.isGoogleAnalytics = this.permissionService.hasPermission(\"Google Analytics\");\n  }\n  ngOnInit() {\n    this.getVisibleElements();\n    let currentPath = this.location.path();\n    if (currentPath.includes('/Portal')) {\n      currentPath.replace(\"/Portal\", \"\");\n    }\n    let expectedPath = currentPath.split(\"/\");\n    this.exactPath = expectedPath[1]?.includes('search') ? \"Search\" : expectedPath[1];\n  }\n  getVisibleElements() {\n    this.observer = new IntersectionObserver((entries, observer) => {\n      entries.forEach(entry => {\n        // Emit true if the element is in the viewport, false otherwise\n        if (entry.isIntersecting) {\n          this.logGoogleImpression();\n          observer.unobserve(entry.target);\n        }\n      });\n    });\n    this.observer.observe(this.el.nativeElement);\n  }\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n  }\n  logGoogleImpression() {\n    if (this.isGoogleAnalytics) {\n      let pageValue = '';\n      let query = window.location.search.replace('?', '');\n      if (query.includes('categoryName')) {\n        pageValue = query.replace('categoryName=', '');\n      }\n      if (query.includes('q')) {\n        pageValue = query.replace('q=', '');\n      }\n      let itemListName = this.exactPath?.includes('category') ? this.product.categoryName : pageValue;\n      this.googleAnalytics.event(this.tagName.IMPRESSIONS, 'product', 'IMPRESSIONS', 1, true, {\n        \"product_name\": this.product.productName ? this.product?.productName : this.product.name,\n        \"category_name\": this.product.categoryName,\n        \"product_SKU\": this.product?.specProductDetails?.skuAutoGenerated ? this.product?.specProductDetails?.skuAutoGenerated : this.product['skuAutoGenerated'],\n        \"product_ID\": this.product.productId,\n        \"shop_ID\": this.product.shopId,\n        \"product_tags\": this.product?.isBest || this.product.bestSeller ? 'Best Seller' : this.product?.isNew || this.product.newArrival ? 'New Arrival' : this.product?.isHot || this.product.hotDeals ? 'Hot Deals' : 'None',\n        \"promotion\": this.product?.promotionName ? this.product?.promotionName : 'None',\n        \"page_label\": this.exactPath ? this.exactPath : 'Home',\n        \"page_value\": itemListName\n      });\n      this._GACustomEvent.viewItemListEvent(this.product, itemListName ? itemListName : 'Home');\n    }\n  }\n  static #_ = this.ɵfac = function GAImpressionDirective_Factory(t) {\n    return new (t || GAImpressionDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i3.Location), i0.ɵɵdirectiveInject(i4.CustomGAService));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: GAImpressionDirective,\n    selectors: [[\"\", \"appGAImpression\", \"\"]],\n    inputs: {\n      product: \"product\"\n    },\n    standalone: true\n  });\n}", "map": {"version": 3, "names": ["GaLocalActionEnum", "GAImpressionDirective", "constructor", "el", "googleAnalytics", "permissionService", "location", "_GACustomEvent", "tagName", "isGoogleAnalytics", "hasPermission", "ngOnInit", "getVisibleElements", "currentPath", "path", "includes", "replace", "expectedPath", "split", "exactPath", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "logGoogleImpression", "unobserve", "target", "observe", "nativeElement", "ngOnDestroy", "disconnect", "pageValue", "query", "window", "search", "itemListName", "product", "categoryName", "event", "IMPRESSIONS", "productName", "name", "specProductDetails", "skuAutoGenerated", "productId", "shopId", "isBest", "bestSeller", "isNew", "newArrival", "isHot", "hotDeals", "promotionName", "viewItemListEvent", "_", "i0", "ɵɵdirectiveInject", "ElementRef", "i1", "GoogleAnalyticsService", "i2", "PermissionService", "i3", "Location", "i4", "CustomGAService", "_2", "selectors", "inputs", "standalone"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\directives\\ga-impression.directive.ts"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Directive, HostListener, Output, EventEmitter, Input, ElementRef, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\r\nimport { PermissionService } from '@core/services';\r\nimport { CustomGAService } from '@core/services/custom-GA.service';\r\nimport { Router } from 'express';\r\nimport { Renderer } from 'leaflet';\r\nimport { GoogleAnalyticsService } from 'ngx-google-analytics';\r\nimport { debounceTime, first, fromEvent, map, switchMap, take } from 'rxjs';\r\n\r\n@Directive({\r\n  selector: '[appGAImpression]',// Use this selector to apply the directive\r\n  standalone:true\r\n})\r\nexport class GAImpressionDirective {\r\n  @Input() product:any\r\n  tagName:any = GaLocalActionEnum\r\n  isGoogleAnalytics:boolean = false\r\n  exactPath:any;\r\n  private observer!: IntersectionObserver;\r\n    constructor(private el: ElementRef,\r\n        private googleAnalytics : GoogleAnalyticsService,\r\n        private permissionService:PermissionService,\r\n        private location: Location,\r\n        private _GACustomEvent:CustomGAService) {\r\n            this.isGoogleAnalytics = this.permissionService.hasPermission(\"Google Analytics\");\r\n    }\r\n    ngOnInit(){\r\n      this.getVisibleElements()\r\n      let currentPath = this.location.path()\r\n      if (currentPath.includes('/Portal')) {\r\n        currentPath.replace(\"/Portal\", \"\")\r\n      }\r\n      let expectedPath = currentPath.split(\"/\")\r\n      this.exactPath = expectedPath[1]?.includes('search') ? \"Search\" : expectedPath[1]\r\n    }\r\n    private getVisibleElements() {\r\n      this.observer = new IntersectionObserver((entries, observer) => {\r\n        entries.forEach(entry => {\r\n\r\n          // Emit true if the element is in the viewport, false otherwise\r\n         if(entry.isIntersecting){\r\n          this.logGoogleImpression();\r\n          observer.unobserve(entry.target);\r\n         }\r\n        });\r\n      });\r\n      this.observer.observe(this.el.nativeElement);\r\n    }\r\n    ngOnDestroy() {\r\n      if (this.observer) {\r\n        this.observer.disconnect();\r\n      }\r\n    }\r\n\r\n    logGoogleImpression(){\r\n        if (this.isGoogleAnalytics  ) {\r\n          let pageValue:string = ''\r\n        let query = window.location.search.replace('?','')\r\n        if(query.includes('categoryName')){\r\n          pageValue = query.replace('categoryName=','')\r\n        }\r\n        if(query.includes('q')){\r\n          pageValue = query.replace('q=','')\r\n        }\r\n        let itemListName  = this.exactPath?.includes('category') ?  this.product.categoryName : pageValue\r\n          this.googleAnalytics.event(this.tagName.IMPRESSIONS,\r\n            'product', 'IMPRESSIONS', 1 , true, {\r\n            \"product_name\": this.product.productName ? this.product?.productName : this.product.name,\r\n            \"category_name\": this.product.categoryName,\r\n            \"product_SKU\": this.product?.specProductDetails?.skuAutoGenerated ?  this.product?.specProductDetails?.skuAutoGenerated: this.product['skuAutoGenerated'],\r\n            \"product_ID\": this.product.productId,\r\n            \"shop_ID\": this.product.shopId,\r\n            \"product_tags\":(this.product?.isBest || this.product.bestSeller )? 'Best Seller' : (this.product?.isNew || this.product.newArrival )? 'New Arrival' : (this.product?.isHot  || this.product.hotDeals)? 'Hot Deals':'None',\r\n            \"promotion\":this.product?.promotionName ? this.product?.promotionName : 'None',\r\n            \"page_label\":this.exactPath ?  this.exactPath : 'Home',\r\n            \"page_value\": itemListName\r\n          });\r\n            this._GACustomEvent.viewItemListEvent(this.product,itemListName ? itemListName : 'Home')\r\n        }\r\n      }\r\n}\r\n"], "mappings": "AAEA,SAASA,iBAAiB,QAAQ,kCAAkC;;;;;;AAYpE,OAAM,MAAOC,qBAAqB;EAM9BC,YAAoBC,EAAc,EACtBC,eAAwC,EACxCC,iBAAmC,EACnCC,QAAkB,EAClBC,cAA8B;IAJtB,KAAAJ,EAAE,GAAFA,EAAE;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IAR5B,KAAAC,OAAO,GAAOR,iBAAiB;IAC/B,KAAAS,iBAAiB,GAAW,KAAK;IAQvB,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACJ,iBAAiB,CAACK,aAAa,CAAC,kBAAkB,CAAC;EACzF;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAIC,WAAW,GAAG,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACtC,IAAID,WAAW,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MACnCF,WAAW,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;;IAEpC,IAAIC,YAAY,GAAGJ,WAAW,CAACK,KAAK,CAAC,GAAG,CAAC;IACzC,IAAI,CAACC,SAAS,GAAGF,YAAY,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAGE,YAAY,CAAC,CAAC,CAAC;EACnF;EACQL,kBAAkBA,CAAA;IACxB,IAAI,CAACQ,QAAQ,GAAG,IAAIC,oBAAoB,CAAC,CAACC,OAAO,EAAEF,QAAQ,KAAI;MAC7DE,OAAO,CAACC,OAAO,CAACC,KAAK,IAAG;QAEtB;QACD,IAAGA,KAAK,CAACC,cAAc,EAAC;UACvB,IAAI,CAACC,mBAAmB,EAAE;UAC1BN,QAAQ,CAACO,SAAS,CAACH,KAAK,CAACI,MAAM,CAAC;;MAElC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACR,QAAQ,CAACS,OAAO,CAAC,IAAI,CAAC1B,EAAE,CAAC2B,aAAa,CAAC;EAC9C;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACX,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACY,UAAU,EAAE;;EAE9B;EAEAN,mBAAmBA,CAAA;IACf,IAAI,IAAI,CAACjB,iBAAiB,EAAI;MAC5B,IAAIwB,SAAS,GAAU,EAAE;MAC3B,IAAIC,KAAK,GAAGC,MAAM,CAAC7B,QAAQ,CAAC8B,MAAM,CAACpB,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC;MAClD,IAAGkB,KAAK,CAACnB,QAAQ,CAAC,cAAc,CAAC,EAAC;QAChCkB,SAAS,GAAGC,KAAK,CAAClB,OAAO,CAAC,eAAe,EAAC,EAAE,CAAC;;MAE/C,IAAGkB,KAAK,CAACnB,QAAQ,CAAC,GAAG,CAAC,EAAC;QACrBkB,SAAS,GAAGC,KAAK,CAAClB,OAAO,CAAC,IAAI,EAAC,EAAE,CAAC;;MAEpC,IAAIqB,YAAY,GAAI,IAAI,CAAClB,SAAS,EAAEJ,QAAQ,CAAC,UAAU,CAAC,GAAI,IAAI,CAACuB,OAAO,CAACC,YAAY,GAAGN,SAAS;MAC/F,IAAI,CAAC7B,eAAe,CAACoC,KAAK,CAAC,IAAI,CAAChC,OAAO,CAACiC,WAAW,EACjD,SAAS,EAAE,aAAa,EAAE,CAAC,EAAG,IAAI,EAAE;QACpC,cAAc,EAAE,IAAI,CAACH,OAAO,CAACI,WAAW,GAAG,IAAI,CAACJ,OAAO,EAAEI,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACK,IAAI;QACxF,eAAe,EAAE,IAAI,CAACL,OAAO,CAACC,YAAY;QAC1C,aAAa,EAAE,IAAI,CAACD,OAAO,EAAEM,kBAAkB,EAAEC,gBAAgB,GAAI,IAAI,CAACP,OAAO,EAAEM,kBAAkB,EAAEC,gBAAgB,GAAE,IAAI,CAACP,OAAO,CAAC,kBAAkB,CAAC;QACzJ,YAAY,EAAE,IAAI,CAACA,OAAO,CAACQ,SAAS;QACpC,SAAS,EAAE,IAAI,CAACR,OAAO,CAACS,MAAM;QAC9B,cAAc,EAAE,IAAI,CAACT,OAAO,EAAEU,MAAM,IAAI,IAAI,CAACV,OAAO,CAACW,UAAU,GAAI,aAAa,GAAI,IAAI,CAACX,OAAO,EAAEY,KAAK,IAAI,IAAI,CAACZ,OAAO,CAACa,UAAU,GAAI,aAAa,GAAI,IAAI,CAACb,OAAO,EAAEc,KAAK,IAAK,IAAI,CAACd,OAAO,CAACe,QAAQ,GAAG,WAAW,GAAC,MAAM;QACzN,WAAW,EAAC,IAAI,CAACf,OAAO,EAAEgB,aAAa,GAAG,IAAI,CAAChB,OAAO,EAAEgB,aAAa,GAAG,MAAM;QAC9E,YAAY,EAAC,IAAI,CAACnC,SAAS,GAAI,IAAI,CAACA,SAAS,GAAG,MAAM;QACtD,YAAY,EAAEkB;OACf,CAAC;MACA,IAAI,CAAC9B,cAAc,CAACgD,iBAAiB,CAAC,IAAI,CAACjB,OAAO,EAACD,YAAY,GAAGA,YAAY,GAAG,MAAM,CAAC;;EAE9F;EAAC,QAAAmB,CAAA,G;qBAlEMvD,qBAAqB,EAAAwD,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,sBAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,QAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBnE,qBAAqB;IAAAoE,SAAA;IAAAC,MAAA;MAAAhC,OAAA;IAAA;IAAAiC,UAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}