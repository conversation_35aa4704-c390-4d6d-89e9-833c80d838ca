{"ast": null, "code": "import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\n/**\n * UUID V7 - Unix Epoch time-based UUID\n *\n * The IETF has published RFC9562, introducing 3 new UUID versions (6,7,8). This\n * implementation of V7 is based on the accepted, though not yet approved,\n * revisions.\n *\n * RFC 9562:https://www.rfc-editor.org/rfc/rfc9562.html Universally Unique\n * IDentifiers (UUIDs)\n\n *\n * Sample V7 value:\n * https://www.rfc-editor.org/rfc/rfc9562.html#name-example-of-a-uuidv7-value\n *\n * Monotonic Bit Layout: RFC rfc9562.6.2 Method 1, Dedicated Counter Bits ref:\n *     https://www.rfc-editor.org/rfc/rfc9562.html#section-6.2-5.1\n *\n *   0                   1                   2                   3 0 1 2 3 4 5 6\n *   7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                          unix_ts_ms                           |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |          unix_ts_ms           |  ver  |        seq_hi         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |var|               seq_low               |        rand         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                             rand                              |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *\n * seq is a 31 bit serialized counter; comprised of 12 bit seq_hi and 19 bit\n * seq_low, and randomly initialized upon timestamp change. 31 bit counter size\n * was selected as any bitwise operations in node are done as _signed_ 32 bit\n * ints. we exclude the sign bit.\n */\n\nvar _seqLow = null;\nvar _seqHigh = null;\nvar _msecs = 0;\nfunction v7(options, buf, offset) {\n  options = options || {};\n\n  // initialize buffer and pointer\n  var i = buf && offset || 0;\n  var b = buf || new Uint8Array(16);\n\n  // rnds is Uint8Array(16) filled with random bytes\n  var rnds = options.random || (options.rng || rng)();\n\n  // milliseconds since unix epoch, 1970-01-01 00:00\n  var msecs = options.msecs !== undefined ? options.msecs : Date.now();\n\n  // seq is user provided 31 bit counter\n  var seq = options.seq !== undefined ? options.seq : null;\n\n  // initialize local seq high/low parts\n  var seqHigh = _seqHigh;\n  var seqLow = _seqLow;\n\n  // check if clock has advanced and user has not provided msecs\n  if (msecs > _msecs && options.msecs === undefined) {\n    _msecs = msecs;\n\n    // unless user provided seq, reset seq parts\n    if (seq !== null) {\n      seqHigh = null;\n      seqLow = null;\n    }\n  }\n\n  // if we have a user provided seq\n  if (seq !== null) {\n    // trim provided seq to 31 bits of value, avoiding overflow\n    if (seq > 0x7fffffff) {\n      seq = 0x7fffffff;\n    }\n\n    // split provided seq into high/low parts\n    seqHigh = seq >>> 19 & 0xfff;\n    seqLow = seq & 0x7ffff;\n  }\n\n  // randomly initialize seq\n  if (seqHigh === null || seqLow === null) {\n    seqHigh = rnds[6] & 0x7f;\n    seqHigh = seqHigh << 8 | rnds[7];\n    seqLow = rnds[8] & 0x3f; // pad for var\n    seqLow = seqLow << 8 | rnds[9];\n    seqLow = seqLow << 5 | rnds[10] >>> 3;\n  }\n\n  // increment seq if within msecs window\n  if (msecs + 10000 > _msecs && seq === null) {\n    if (++seqLow > 0x7ffff) {\n      seqLow = 0;\n      if (++seqHigh > 0xfff) {\n        seqHigh = 0;\n\n        // increment internal _msecs. this allows us to continue incrementing\n        // while staying monotonic. Note, once we hit 10k milliseconds beyond system\n        // clock, we will reset breaking monotonicity (after (2^31)*10000 generations)\n        _msecs++;\n      }\n    }\n  } else {\n    // resetting; we have advanced more than\n    // 10k milliseconds beyond system clock\n    _msecs = msecs;\n  }\n  _seqHigh = seqHigh;\n  _seqLow = seqLow;\n\n  // [bytes 0-5] 48 bits of local timestamp\n  b[i++] = _msecs / 0x10000000000 & 0xff;\n  b[i++] = _msecs / 0x100000000 & 0xff;\n  b[i++] = _msecs / 0x1000000 & 0xff;\n  b[i++] = _msecs / 0x10000 & 0xff;\n  b[i++] = _msecs / 0x100 & 0xff;\n  b[i++] = _msecs & 0xff;\n\n  // [byte 6] - set 4 bits of version (7) with first 4 bits seq_hi\n  b[i++] = seqHigh >>> 4 & 0x0f | 0x70;\n\n  // [byte 7] remaining 8 bits of seq_hi\n  b[i++] = seqHigh & 0xff;\n\n  // [byte 8] - variant (2 bits), first 6 bits seq_low\n  b[i++] = seqLow >>> 13 & 0x3f | 0x80;\n\n  // [byte 9] 8 bits seq_low\n  b[i++] = seqLow >>> 5 & 0xff;\n\n  // [byte 10] remaining 5 bits seq_low, 3 bits random\n  b[i++] = seqLow << 3 & 0xff | rnds[10] & 0x07;\n\n  // [bytes 11-15] always random\n  b[i++] = rnds[11];\n  b[i++] = rnds[12];\n  b[i++] = rnds[13];\n  b[i++] = rnds[14];\n  b[i++] = rnds[15];\n  return buf || unsafeStringify(b);\n}\nexport default v7;", "map": {"version": 3, "names": ["rng", "unsafeStringify", "_seqLow", "_seqHigh", "_msecs", "v7", "options", "buf", "offset", "i", "b", "Uint8Array", "rnds", "random", "msecs", "undefined", "Date", "now", "seq", "seqHigh", "seqLow"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/uuid/dist/esm-browser/v7.js"], "sourcesContent": ["import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\n/**\n * UUID V7 - Unix Epoch time-based UUID\n *\n * The IETF has published RFC9562, introducing 3 new UUID versions (6,7,8). This\n * implementation of V7 is based on the accepted, though not yet approved,\n * revisions.\n *\n * RFC 9562:https://www.rfc-editor.org/rfc/rfc9562.html Universally Unique\n * IDentifiers (UUIDs)\n\n *\n * Sample V7 value:\n * https://www.rfc-editor.org/rfc/rfc9562.html#name-example-of-a-uuidv7-value\n *\n * Monotonic Bit Layout: RFC rfc9562.6.2 Method 1, Dedicated Counter Bits ref:\n *     https://www.rfc-editor.org/rfc/rfc9562.html#section-6.2-5.1\n *\n *   0                   1                   2                   3 0 1 2 3 4 5 6\n *   7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                          unix_ts_ms                           |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |          unix_ts_ms           |  ver  |        seq_hi         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |var|               seq_low               |        rand         |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *  |                             rand                              |\n *  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n *\n * seq is a 31 bit serialized counter; comprised of 12 bit seq_hi and 19 bit\n * seq_low, and randomly initialized upon timestamp change. 31 bit counter size\n * was selected as any bitwise operations in node are done as _signed_ 32 bit\n * ints. we exclude the sign bit.\n */\n\nvar _seqLow = null;\nvar _seqHigh = null;\nvar _msecs = 0;\nfunction v7(options, buf, offset) {\n  options = options || {};\n\n  // initialize buffer and pointer\n  var i = buf && offset || 0;\n  var b = buf || new Uint8Array(16);\n\n  // rnds is Uint8Array(16) filled with random bytes\n  var rnds = options.random || (options.rng || rng)();\n\n  // milliseconds since unix epoch, 1970-01-01 00:00\n  var msecs = options.msecs !== undefined ? options.msecs : Date.now();\n\n  // seq is user provided 31 bit counter\n  var seq = options.seq !== undefined ? options.seq : null;\n\n  // initialize local seq high/low parts\n  var seqHigh = _seqHigh;\n  var seqLow = _seqLow;\n\n  // check if clock has advanced and user has not provided msecs\n  if (msecs > _msecs && options.msecs === undefined) {\n    _msecs = msecs;\n\n    // unless user provided seq, reset seq parts\n    if (seq !== null) {\n      seqHigh = null;\n      seqLow = null;\n    }\n  }\n\n  // if we have a user provided seq\n  if (seq !== null) {\n    // trim provided seq to 31 bits of value, avoiding overflow\n    if (seq > 0x7fffffff) {\n      seq = 0x7fffffff;\n    }\n\n    // split provided seq into high/low parts\n    seqHigh = seq >>> 19 & 0xfff;\n    seqLow = seq & 0x7ffff;\n  }\n\n  // randomly initialize seq\n  if (seqHigh === null || seqLow === null) {\n    seqHigh = rnds[6] & 0x7f;\n    seqHigh = seqHigh << 8 | rnds[7];\n    seqLow = rnds[8] & 0x3f; // pad for var\n    seqLow = seqLow << 8 | rnds[9];\n    seqLow = seqLow << 5 | rnds[10] >>> 3;\n  }\n\n  // increment seq if within msecs window\n  if (msecs + 10000 > _msecs && seq === null) {\n    if (++seqLow > 0x7ffff) {\n      seqLow = 0;\n      if (++seqHigh > 0xfff) {\n        seqHigh = 0;\n\n        // increment internal _msecs. this allows us to continue incrementing\n        // while staying monotonic. Note, once we hit 10k milliseconds beyond system\n        // clock, we will reset breaking monotonicity (after (2^31)*10000 generations)\n        _msecs++;\n      }\n    }\n  } else {\n    // resetting; we have advanced more than\n    // 10k milliseconds beyond system clock\n    _msecs = msecs;\n  }\n  _seqHigh = seqHigh;\n  _seqLow = seqLow;\n\n  // [bytes 0-5] 48 bits of local timestamp\n  b[i++] = _msecs / 0x10000000000 & 0xff;\n  b[i++] = _msecs / 0x100000000 & 0xff;\n  b[i++] = _msecs / 0x1000000 & 0xff;\n  b[i++] = _msecs / 0x10000 & 0xff;\n  b[i++] = _msecs / 0x100 & 0xff;\n  b[i++] = _msecs & 0xff;\n\n  // [byte 6] - set 4 bits of version (7) with first 4 bits seq_hi\n  b[i++] = seqHigh >>> 4 & 0x0f | 0x70;\n\n  // [byte 7] remaining 8 bits of seq_hi\n  b[i++] = seqHigh & 0xff;\n\n  // [byte 8] - variant (2 bits), first 6 bits seq_low\n  b[i++] = seqLow >>> 13 & 0x3f | 0x80;\n\n  // [byte 9] 8 bits seq_low\n  b[i++] = seqLow >>> 5 & 0xff;\n\n  // [byte 10] remaining 5 bits seq_low, 3 bits random\n  b[i++] = seqLow << 3 & 0xff | rnds[10] & 0x07;\n\n  // [bytes 11-15] always random\n  b[i++] = rnds[11];\n  b[i++] = rnds[12];\n  b[i++] = rnds[13];\n  b[i++] = rnds[14];\n  b[i++] = rnds[15];\n  return buf || unsafeStringify(b);\n}\nexport default v7;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,QAAQ,GAAG,IAAI;AACnB,IAAIC,MAAM,GAAG,CAAC;AACd,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChCF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;EACA,IAAIG,CAAC,GAAGF,GAAG,IAAIC,MAAM,IAAI,CAAC;EAC1B,IAAIE,CAAC,GAAGH,GAAG,IAAI,IAAII,UAAU,CAAC,EAAE,CAAC;;EAEjC;EACA,IAAIC,IAAI,GAAGN,OAAO,CAACO,MAAM,IAAI,CAACP,OAAO,CAACN,GAAG,IAAIA,GAAG,EAAE,CAAC;;EAEnD;EACA,IAAIc,KAAK,GAAGR,OAAO,CAACQ,KAAK,KAAKC,SAAS,GAAGT,OAAO,CAACQ,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;;EAEpE;EACA,IAAIC,GAAG,GAAGZ,OAAO,CAACY,GAAG,KAAKH,SAAS,GAAGT,OAAO,CAACY,GAAG,GAAG,IAAI;;EAExD;EACA,IAAIC,OAAO,GAAGhB,QAAQ;EACtB,IAAIiB,MAAM,GAAGlB,OAAO;;EAEpB;EACA,IAAIY,KAAK,GAAGV,MAAM,IAAIE,OAAO,CAACQ,KAAK,KAAKC,SAAS,EAAE;IACjDX,MAAM,GAAGU,KAAK;;IAEd;IACA,IAAII,GAAG,KAAK,IAAI,EAAE;MAChBC,OAAO,GAAG,IAAI;MACdC,MAAM,GAAG,IAAI;IACf;EACF;;EAEA;EACA,IAAIF,GAAG,KAAK,IAAI,EAAE;IAChB;IACA,IAAIA,GAAG,GAAG,UAAU,EAAE;MACpBA,GAAG,GAAG,UAAU;IAClB;;IAEA;IACAC,OAAO,GAAGD,GAAG,KAAK,EAAE,GAAG,KAAK;IAC5BE,MAAM,GAAGF,GAAG,GAAG,OAAO;EACxB;;EAEA;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;IACvCD,OAAO,GAAGP,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IACxBO,OAAO,GAAGA,OAAO,IAAI,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC;IAChCQ,MAAM,GAAGR,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACzBQ,MAAM,GAAGA,MAAM,IAAI,CAAC,GAAGR,IAAI,CAAC,CAAC,CAAC;IAC9BQ,MAAM,GAAGA,MAAM,IAAI,CAAC,GAAGR,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;EACvC;;EAEA;EACA,IAAIE,KAAK,GAAG,KAAK,GAAGV,MAAM,IAAIc,GAAG,KAAK,IAAI,EAAE;IAC1C,IAAI,EAAEE,MAAM,GAAG,OAAO,EAAE;MACtBA,MAAM,GAAG,CAAC;MACV,IAAI,EAAED,OAAO,GAAG,KAAK,EAAE;QACrBA,OAAO,GAAG,CAAC;;QAEX;QACA;QACA;QACAf,MAAM,EAAE;MACV;IACF;EACF,CAAC,MAAM;IACL;IACA;IACAA,MAAM,GAAGU,KAAK;EAChB;EACAX,QAAQ,GAAGgB,OAAO;EAClBjB,OAAO,GAAGkB,MAAM;;EAEhB;EACAV,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,aAAa,GAAG,IAAI;EACtCM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,WAAW,GAAG,IAAI;EACpCM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,SAAS,GAAG,IAAI;EAClCM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,OAAO,GAAG,IAAI;EAChCM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,KAAK,GAAG,IAAI;EAC9BM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGL,MAAM,GAAG,IAAI;;EAEtB;EACAM,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGU,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;;EAEpC;EACAT,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGU,OAAO,GAAG,IAAI;;EAEvB;EACAT,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGW,MAAM,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI;;EAEpC;EACAV,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGW,MAAM,KAAK,CAAC,GAAG,IAAI;;EAE5B;EACAV,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGW,MAAM,IAAI,CAAC,GAAG,IAAI,GAAGR,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;;EAE7C;EACAF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,IAAI,CAAC,EAAE,CAAC;EACjBF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,IAAI,CAAC,EAAE,CAAC;EACjBF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,IAAI,CAAC,EAAE,CAAC;EACjBF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,IAAI,CAAC,EAAE,CAAC;EACjBF,CAAC,CAACD,CAAC,EAAE,CAAC,GAAGG,IAAI,CAAC,EAAE,CAAC;EACjB,OAAOL,GAAG,IAAIN,eAAe,CAACS,CAAC,CAAC;AAClC;AACA,eAAeL,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}