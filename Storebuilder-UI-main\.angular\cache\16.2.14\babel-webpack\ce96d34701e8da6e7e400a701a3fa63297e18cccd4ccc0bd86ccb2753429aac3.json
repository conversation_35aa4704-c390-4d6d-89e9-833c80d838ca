{"ast": null, "code": "export var ConfigurationKeys;\n(function (ConfigurationKeys) {\n  ConfigurationKeys[\"CurrencyDecimal\"] = \"CurrencyDecimal\";\n  ConfigurationKeys[\"CategoryEnNameAppearOnDesign\"] = \"CategoryEnNameAppearOnDesign\";\n  ConfigurationKeys[\"CategoryArNameAppearOnDesign\"] = \"CategoryArNameAppearOnDesign\";\n  ConfigurationKeys[\"CountryPhone\"] = \"CountryPhone\";\n  ConfigurationKeys[\"PhoneLength\"] = \"PhoneLength\";\n  ConfigurationKeys[\"Currency\"] = \"Currency\";\n  ConfigurationKeys[\"CustomerAddressLandmarkRequired\"] = \"CustomerAddressLandmarkRequired\";\n  ConfigurationKeys[\"EmailRequired\"] = \"EmailRequired\";\n  ConfigurationKeys[\"DisableCents\"] = \"DisableCents\";\n  ConfigurationKeys[\"PhoneNumberMask\"] = \"PhoneNumberMask\";\n  ConfigurationKeys[\"GATrackingId\"] = \"GATrackingId\";\n})(ConfigurationKeys || (ConfigurationKeys = {}));", "map": {"version": 3, "names": ["Configuration<PERSON>eys"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\enums\\sections.ts"], "sourcesContent": ["export enum ConfigurationKeys {\r\n    CurrencyDecimal = 'CurrencyDecimal',\r\n    CategoryEnNameAppearOnDesign = 'CategoryEnNameAppearOnDesign',\r\n    CategoryArNameAppearOnDesign = 'CategoryArNameAppearOnDesign',\r\n    CountryPhone = 'CountryPhone',\r\n    PhoneLength = 'PhoneLength',\r\n    Currency = 'Currency',\r\n    CustomerAddressLandmarkRequired = 'CustomerAddressLandmarkRequired',\r\n    EmailRequired = \"EmailRequired\",\r\n    DisableCents = \"DisableCents\",\r\n    PhoneNumberMask=\"PhoneNumberMask\",\r\n    GATrackingId=\"GATrackingId\"\r\n\r\n}\r\n"], "mappings": "AAAA,WAAYA,iBAaX;AAbD,WAAYA,iBAAiB;EACzBA,iBAAA,uCAAmC;EACnCA,iBAAA,iEAA6D;EAC7DA,iBAAA,iEAA6D;EAC7DA,iBAAA,iCAA6B;EAC7BA,iBAAA,+BAA2B;EAC3BA,iBAAA,yBAAqB;EACrBA,iBAAA,uEAAmE;EACnEA,iBAAA,mCAA+B;EAC/BA,iBAAA,iCAA6B;EAC7BA,iBAAA,uCAAiC;EACjCA,iBAAA,iCAA2B;AAE/B,CAAC,EAbWA,iBAAiB,KAAjBA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}