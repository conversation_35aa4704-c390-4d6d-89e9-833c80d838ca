{"ast": null, "code": "import { ElementRef, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../side-menu/side-menu.component\";\nconst _c0 = [\"widgetsContent\"];\nfunction NavbarComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-mtn-side-menu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"em\", 17);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_8_Template_em_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.scrollLeft());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"extra-pad\": a0\n  };\n};\nfunction NavbarComponent_li_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_li_12_span_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const feature_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.redirectFeaturedProducts(feature_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r11.isShowSlider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(feature_r10.categoryName);\n  }\n}\nfunction NavbarComponent_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵtemplate(1, NavbarComponent_li_12_span_1_Template, 2, 4, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !feature_r10.shouldNotDisplay);\n  }\n}\nfunction NavbarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"em\", 22);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_13_Template_em_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.scrollRight());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"a\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"span\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"landingNavbar.liveMerchants\"));\n  }\n}\nfunction NavbarComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_a_18_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.navigateToSellerHub());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27);\n    i0.ɵɵelement(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"landingNavbar.sellOnYallaMall\"), \" \");\n  }\n}\nfunction NavbarComponent_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_a_19_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateToSellerHub());\n    });\n    i0.ɵɵelementStart(1, \"div\", 30);\n    i0.ɵɵelement(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"landingNavbar.sellOnMarketplace\"), \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"isShowlivestream\": a0\n  };\n};\nexport let NavbarComponent = /*#__PURE__*/(() => {\n  class NavbarComponent {\n    store;\n    translate;\n    mainDataService;\n    messageService;\n    appDataService;\n    router;\n    permissionService;\n    cdr;\n    $gaService;\n    $gtmService;\n    platformId;\n    categories = [];\n    widgetsContent;\n    isShowSlider = environment.isStoreCloud;\n    ShowCategory;\n    leftArrow = true;\n    rightArrow = true;\n    isShop = false;\n    link = 'link';\n    isStoreCloud = environment.isStoreCloud;\n    items = [{\n      title: 'Orangies',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Apple',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Mango',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Carrot',\n      link: 'https://www.github.com/isahohieku'\n    }];\n    allFeatures = [];\n    flashSale;\n    flashSaleData;\n    topNumber = 1000;\n    navbarData;\n    isShowLiveStream = false;\n    isGoogleAnalytics = false;\n    constructor(store, translate, mainDataService, messageService, appDataService, router, permissionService, cdr, $gaService, $gtmService, platformId) {\n      this.store = store;\n      this.translate = translate;\n      this.mainDataService = mainDataService;\n      this.messageService = messageService;\n      this.appDataService = appDataService;\n      this.router = router;\n      this.permissionService = permissionService;\n      this.cdr = cdr;\n      this.$gaService = $gaService;\n      this.$gtmService = $gtmService;\n      this.platformId = platformId;\n      this.ShowCategory = false;\n    }\n    ngOnInit() {\n      this.isShowLiveStream = this.permissionService.hasPermission('Live-Stream');\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      this.getMainData();\n      this.getAllFeatures();\n    }\n    scrollLeft() {\n      this.widgetsContent.nativeElement.scrollLeft -= 200;\n      this.widgetsContent.nativeElement.scrollTo({\n        right: this.widgetsContent.nativeElement.scrollLeft,\n        behavior: 'smooth'\n      });\n      if (this.widgetsContent.nativeElement.scrollLeft == 0 || this.widgetsContent.nativeElement.scrollLeft < 50) {\n        this.leftArrow = true;\n      } else {\n        this.leftArrow = false;\n      }\n      this.rightArrow = true;\n    }\n    scrollRight() {\n      this.widgetsContent.nativeElement.scrollLeft += 200;\n      this.widgetsContent.nativeElement.scrollTo({\n        left: this.widgetsContent.nativeElement.scrollLeft,\n        behavior: 'smooth'\n      });\n      this.leftArrow = false;\n      if (this.widgetsContent.nativeElement.scrollLeft > 0) {\n        this.leftArrow = false;\n      }\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        this.store.subscription('categories').subscribe({\n          next: res => {\n            this.categories = res;\n          },\n          error: err => {\n            console.error(err);\n          }\n        });\n      }, 1);\n    }\n    getMainData() {\n      const initialData = this.appDataService.initialData;\n      this.isShop = initialData.isShop;\n      this.store.set(\"isShop\", this.isShop);\n    }\n    getAllFeatures() {\n      this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n      this.flashSale = this.appDataService.layoutTemplate.find(section => section.description === 'Flash Sale');\n      if (this.flashSale) {\n        this.flashSaleData = JSON.parse(this.flashSale.data);\n      }\n      let features = JSON.parse(this.navbarData.data);\n      if (this.flashSale?.isActive && this.flashSale?.promotionId && this.flashSaleData.isMainMenu) {\n        let data = {\n          id: this.flashSale.promotionId,\n          categoryName: this.flashSaleData.promotionName,\n          type: this.flashSale.type,\n          isActive: this.flashSale.isActive\n        };\n        features.push(data);\n      }\n      if (this.navbarData.isActive) {\n        this.allFeatures = features;\n      }\n    }\n    redirectFeaturedProducts(feature) {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_QUICK_LINKS, '', 'QUICK_LINKS', 1, true, {\n          categorySelected: feature.categoryName\n        });\n      }\n      if (feature.type && feature.type === 'promotion') {\n        this.router.navigate([`/promotion/${feature.categoryName}`]);\n        this.$gtmService.pushPageView('promotion', feature.categoryName);\n      } else {\n        if (feature.categoryIds) {\n          this.router.navigate([`/category/${feature.id}`]);\n          this.$gtmService.pushPageView('category', feature.categoryName);\n        } else {\n          this.router.navigate([`/category/${feature.id}&${this.topNumber}&${feature.categoryName}`]);\n        }\n      }\n    }\n    translated(name) {\n      let transData;\n      if (!name) return \"\";\n      name = name?.replace(/\\s/g, '');\n      this.translate.get('featureType').subscribe(data => {\n        transData = data;\n      });\n      return transData[name?.toLowerCase()] ? transData[name?.toLowerCase()] : name;\n    }\n    navigateToSellerHub() {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE, '', 'MARKETPLACE_SELLER', 1, true);\n      }\n      if (isPlatformBrowser(this.platformId)) {\n        window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\n      }\n    }\n    static ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-mtn-navbar\"]],\n      viewQuery: function NavbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.widgetsContent = _t.first);\n        }\n      },\n      decls: 20,\n      vars: 10,\n      consts: [[1, \"navbar\", \"mb-1\", \"header-braedcrum\"], [1, \"d-flex\", \"justify-content-between\", 2, \"width\", \"100%\"], [1, \"d-inline-flex\", \"flex-row\", \"bars-tab\", 2, \"width\", \"70%\"], [4, \"ngIf\"], [1, \"showCategorySlider\"], [\"class\", \"pull-left mt-sm align-self-center mt-1 ul-mobile\", 4, \"ngIf\"], [1, \"custom-slider-main\"], [1, \"navbar-categories-list\"], [\"widgetsContent\", \"\"], [\"class\", \"ul-mobile\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"pull-right mt-sm align-self-center mt-1 ul-mobile pi-white\", 4, \"ngIf\"], [1, \"d-inline-flex\", \"flex-row\", 3, \"ngClass\"], [1, \"storecloud-button-holder\", 2, \"display\", \"inline-flex\"], [\"class\", \"storecloud-live-button\", \"style\", \"padding-right: 20px;\", 4, \"ngIf\"], [\"class\", \"sell-yalla ng-star-inserted\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"sell-marketPlace ng-star-inserted\", 3, \"click\", 4, \"ngIf\"], [1, \"pull-left\", \"mt-sm\", \"align-self-center\", \"mt-1\", \"ul-mobile\"], [1, \"pi\", \"pi-angle-left\", 3, \"click\"], [1, \"ul-mobile\"], [\"class\", \"feature-label\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"feature-label\", 3, \"ngClass\", \"click\"], [1, \"pull-right\", \"mt-sm\", \"align-self-center\", \"mt-1\", \"ul-mobile\", \"pi-white\"], [1, \"pi\", \"pi-angle-right\", 3, \"click\"], [1, \"storecloud-live-button\", 2, \"padding-right\", \"20px\"], [\"routerLink\", \"merchants-livestream\", 1, \"btn\", \"btn_live\"], [1, \"live-icon\"], [1, \"sell-yalla\", \"ng-star-inserted\", 3, \"click\"], [1, \"sell-on-yalla\", \"cursor-pointer\"], [1, \"sell-on-yalla-logo\"], [1, \"sell-marketPlace\", \"ng-star-inserted\", 3, \"click\"], [1, \"sell-on-marketPlace\", \"cursor-pointer\"], [1, \"sell-on-marketplace-logo\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"ul\")(4, \"li\")(5, \"a\");\n          i0.ɵɵtemplate(6, NavbarComponent_div_6_Template, 2, 0, \"div\", 3);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, NavbarComponent_div_8_Template, 2, 0, \"div\", 5);\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"ul\", 7, 8);\n          i0.ɵɵtemplate(12, NavbarComponent_li_12_Template, 2, 1, \"li\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, NavbarComponent_div_13_Template, 2, 0, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\")(16, \"span\", 12);\n          i0.ɵɵtemplate(17, NavbarComponent_div_17_Template, 5, 3, \"div\", 13);\n          i0.ɵɵtemplate(18, NavbarComponent_a_18_Template, 5, 3, \"a\", 14);\n          i0.ɵɵtemplate(19, NavbarComponent_a_19_Template, 5, 3, \"a\", 15);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isShop);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.leftArrow);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.allFeatures);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rightArrow && ctx.allFeatures.length > 10);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, ctx.isShowLiveStream));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowLiveStream);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStoreCloud);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStoreCloud);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.RouterLink, i8.SideMenuComponent, i2.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.navbar[_ngcontent-%COMP%]{width:100%;display:flex;align-content:center;align-items:center;padding-bottom:5px}.navbar[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;flex-wrap:nowrap;overflow-x:scroll;list-style-type:none;margin:0;padding:0;background-color:transparent}.navbar[_ngcontent-%COMP%]   .navbar-categories-list[_ngcontent-%COMP%]{width:100%;max-width:1200px}@media only screen and (min-width: 1701px){.navbar[_ngcontent-%COMP%]   .navbar-categories-list[_ngcontent-%COMP%]{max-width:1200px}}@media only screen and (min-width: 1201px) and (max-width: 1700px){.navbar[_ngcontent-%COMP%]   .navbar-categories-list[_ngcontent-%COMP%]{max-width:1000px}}.navbar[_ngcontent-%COMP%]   .custom-slider-main[_ngcontent-%COMP%]{overflow:auto}.navbar[_ngcontent-%COMP%]   .custom-slider-main[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{float:left}.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:block;color:var(--navbar_txtcolor);text-align:center;white-space:nowrap;padding-top:8px;text-decoration:none;font-size:16px;cursor:auto!important}.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{cursor:pointer}.custom-slider-main[_ngcontent-%COMP%]{display:flex;overflow:hidden;scroll-behavior:smooth}.info-box[_ngcontent-%COMP%]{width:50px;height:50px}.r-a[_ngcontent-%COMP%]{right:0;position:fixed;background-color:var(--navbar_bgcolor);cursor:pointer}.pi-white[_ngcontent-%COMP%]{color:#fff}.sell-on-yalla[_ngcontent-%COMP%]{border:2px solid #ffffff;border-radius:8px;padding:4px 16px;font-family:var(--regular-font);position:relative;font-weight:400;text-align:center;z-index:3}.sell-on-yalla[_ngcontent-%COMP%]:hover{background-color:#fff;color:#e9003a}.sell-on-yalla[_ngcontent-%COMP%]:hover   .sell-on-yalla-logo[_ngcontent-%COMP%]{background-image:url(sell-on-yalla-red.18e7033a720328c8.png)}.sell-on-yalla[_ngcontent-%COMP%]   .sell-on-yalla-logo[_ngcontent-%COMP%]{right:2px;position:relative;top:3px;background-image:url(sell-on-yalla.eb4330921d1206e3.png);width:13px;display:inline-block;height:14px}.sell-yalla[_ngcontent-%COMP%]{color:#fff;text-decoration:none}.sell-on-marketPlace[_ngcontent-%COMP%]{border-radius:8px;padding:4px 16px;font-family:var(--regular-font);position:relative;font-weight:400;text-align:center;font-size:14px;font-style:normal;line-height:24px;z-index:3;background-color:var(--header_bgcolor)}.sell-on-marketPlace[_ngcontent-%COMP%]   .sell-on-marketplace-logo[_ngcontent-%COMP%]{right:2px;position:relative;top:3px;background-image:url(marketplace-nav-log.5e7277a46e6454ab.svg);width:20px;height:20px;display:inline-block}.sell-marketPlace[_ngcontent-%COMP%]{color:#fff;text-decoration:none}i.pi.pi-angle-right[_ngcontent-%COMP%]{color:#fff!important}em.pi.pi-angle-right[_ngcontent-%COMP%]{color:#1a445e!important}i.pi.pi-angle-left[_ngcontent-%COMP%]{color:#fff!important}em.pi.pi-angle-left[_ngcontent-%COMP%]{color:#1a445e!important}.feature-label[_ngcontent-%COMP%]{color:var(--header_bgcolor);padding:.5em;position:relative;display:flex;white-space:nowrap;cursor:pointer;justify-content:space-between;font-family:var(--regular-font);border-radius:5px;font-weight:400;font-size:16px}.extra-pad[_ngcontent-%COMP%]{padding:0 .5em!important}.live-icon[_ngcontent-%COMP%]{display:inline-block;position:relative;top:calc(50% - 5px);background-color:red;width:10px;height:10px;margin-left:20px;border:1px solid rgba(0,0,0,.1);border-radius:50%;z-index:1}.live-icon[_ngcontent-%COMP%]:before{content:\\\"\\\";display:block;position:absolute;background-color:#f009;width:100%;height:100%;border-radius:50%;animation:_ngcontent-%COMP%_live 2s ease-in-out infinite;z-index:-1}@keyframes _ngcontent-%COMP%_live{0%{transform:scale(1)}to{transform:scale(3.5);background-color:#f000}}.btn[_ngcontent-%COMP%]{display:block;font-weight:700;margin-top:2px;position:relative;padding:2px 16px;background-color:#ffffffe6;text-decoration:none;color:#333;border-radius:5px;border:1px solid white;transition:all .2s;z-index:1;outline:none;-webkit-tap-highlight-color:rgba(255,255,255,0)}.btn[_ngcontent-%COMP%]:hover{transform:translateY(0);padding:calc(3px * 1.2) 19.2px;background-color:#fff;border:none;box-shadow:0 5px 10px #ededed}.btn[_ngcontent-%COMP%]:hover > span[_ngcontent-%COMP%]:before{animation:none}.isShowlivestream[_ngcontent-%COMP%]{width:29%;justify-content:flex-end}@media only screen and (max-width: 500px){.storecloud-button-holder[_ngcontent-%COMP%]{display:unset!important;width:100%}.mobile-navbar[_ngcontent-%COMP%]{width:0rem!important}.bars-tab[_ngcontent-%COMP%]{width:7%!important}.storecloud-live-button[_ngcontent-%COMP%]{padding-right:unset!important;padding-bottom:5px}.btn[_ngcontent-%COMP%]{width:250px}span.live-icon[_ngcontent-%COMP%]{position:absolute;right:20px}}@media only screen and (max-width: 350px){.btn[_ngcontent-%COMP%]{width:100%}}@media only screen and (min-width: 1701px){.navbar-menu[_ngcontent-%COMP%]{width:78%}.showCategorySlider[_ngcontent-%COMP%]{width:96%;display:flex}}@media only screen and (min-width: 1201px) and (max-width: 1700px){.navbar-menu[_ngcontent-%COMP%]{width:78%}.showCategorySlider[_ngcontent-%COMP%]{width:96%;display:flex}}@media only screen and (min-width: 768px) and (max-width: 1200px){.navbar-menu[_ngcontent-%COMP%]{width:78%}.showCategorySlider[_ngcontent-%COMP%]{width:96%;display:flex}.sell-on-marketPlace[_ngcontent-%COMP%]{zoom:.6;margin-top:13px}}@media only screen and (max-width: 767px){.showCategorySlider[_ngcontent-%COMP%]{display:none!important}.sell-button-section[_ngcontent-%COMP%]{margin-right:20px}.ul-mobile[_ngcontent-%COMP%]{display:none!important}.navbar[_ngcontent-%COMP%]{padding-top:0!important;padding-bottom:5px}li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{padding:10px 20px 0 2px!important}}.storecloud-button-holder[_ngcontent-%COMP%]{width:110%}@media only screen and (min-width: 320px) and (max-width: 360px){.sell-on-marketPlace[_ngcontent-%COMP%]{zoom:.8}}\"]\n    });\n  }\n  return NavbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}