{"ast": null, "code": "/**\n * Swiper 10.3.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2023 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: September 28, 2023\n */\n\nexport { S as Swiper, S as default } from './shared/swiper-core.mjs';", "map": {"version": 3, "names": ["S", "Swiper", "default"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/swiper/swiper.mjs"], "sourcesContent": ["/**\n * Swiper 10.3.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2023 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: September 28, 2023\n */\n\nexport { S as Swiper, S as default } from './shared/swiper-core.mjs';\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,CAAC,IAAIC,MAAM,EAAED,CAAC,IAAIE,OAAO,QAAQ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}