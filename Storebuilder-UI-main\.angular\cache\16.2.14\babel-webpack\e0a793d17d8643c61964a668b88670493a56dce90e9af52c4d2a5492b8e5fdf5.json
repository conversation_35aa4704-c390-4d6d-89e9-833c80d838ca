{"ast": null, "code": "import { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@core/services/gtm.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/card\";\nimport * as i7 from \"../../../../../../shared/pipes/santiaze.pipe\";\nfunction AboutUsComponentComponent_section_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.aboutUsDetails.title, \" \");\n  }\n}\nfunction AboutUsComponentComponent_section_0_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p\", 16);\n    i0.ɵɵpipe(1, \"safeComment\");\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHtml\", i0.ɵɵpipeBind1(1, 1, ctx_r2.aboutUsDetails.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction AboutUsComponentComponent_section_0_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"img\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.aboutUsDetails.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AboutUsComponentComponent_section_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, AboutUsComponentComponent_section_0_div_13_div_1_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.aboutUsDetails.image);\n  }\n}\nfunction AboutUsComponentComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"p-card\", 6)(7, \"div\", 7)(8, \"div\", 8);\n    i0.ɵɵtemplate(9, AboutUsComponentComponent_section_0_div_9_Template, 3, 1, \"div\", 9);\n    i0.ɵɵelementStart(10, \"div\", 10);\n    i0.ɵɵtemplate(11, AboutUsComponentComponent_section_0_p_11_Template, 2, 3, \"p\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, AboutUsComponentComponent_section_0_div_13_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.aboutUsDetails.pageTitle, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.aboutUsDetails.image && !ctx_r0.aboutUsDetails.visibility ? \"col-md-10\" : \"col-md-10\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.aboutUsDetails.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.aboutUsDetails.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.image) && (ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.showImage));\n  }\n}\nexport class AboutUsComponentComponent {\n  constructor(aboutUsService, router, _router, domSanitizer, $gtmService) {\n    this.aboutUsService = aboutUsService;\n    this.router = router;\n    this._router = _router;\n    this.domSanitizer = domSanitizer;\n    this.$gtmService = $gtmService;\n    this.aboutUsDetails = {};\n    this.pageId = 0;\n    this.title = '';\n    this.isPageVisible = false;\n    this.environment = environment;\n    let page = router.snapshot.queryParamMap.get('pageId');\n    let pageTitle = router.snapshot.queryParamMap.get('title');\n    if (page) this.pageId = parseInt(page);\n    if (pageTitle) this.title = pageTitle;\n  }\n  ngOnInit() {\n    this.getAboutUsDetails();\n    this.$gtmService.pushPageView('about us', this.title);\n    this.pageId = history.state.id;\n  }\n  getAboutUsDetails() {\n    this.aboutUsService.getFooterPageDetails(this.pageId).subscribe({\n      next: res => {\n        // let page = res.data?.records.filter(\n        //   (x: any) => x.pageId == this.pageId\n        // );\n        let page = res.data;\n        if (!page[0].visibility) {\n          this._router.navigate(['/page-not-found']);\n        }\n        if (page) {\n          this.aboutUsDetails.pageTitle = page[0].pageTitle;\n          this.aboutUsDetails.title = page[0].title;\n          this.aboutUsDetails.showImage = page[0].showImage;\n          this.aboutUsDetails.visibility = page[0].visibility;\n          this.isPageVisible = page[0].visibility;\n          if (page[0].image) {\n            // this.aboutUsDetails.image = page[0].image;\n            this.aboutUsDetails.image = 'data:image/png;base64, ' + page[0].image;\n          }\n          this.aboutUsDetails.content = decodeURIComponent(escape(atob(page[0].content)));\n        }\n      },\n      error: err => {}\n    });\n  }\n}\nAboutUsComponentComponent.ɵfac = function AboutUsComponentComponent_Factory(t) {\n  return new (t || AboutUsComponentComponent)(i0.ɵɵdirectiveInject(i1.ContactUsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.GTMService));\n};\nAboutUsComponentComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AboutUsComponentComponent,\n  selectors: [[\"app-AboutUsComponent\"]],\n  inputs: {\n    aboutUsDetails: \"aboutUsDetails\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[\"class\", \"contact-us-page pageTop\", 4, \"ngIf\"], [1, \"contact-us-page\", \"pageTop\"], [1, \"about-us-container\", \"mt-7\"], [1, \"font-size-28\", \"bold-font\", \"mb-3\"], [1, \"row\"], [3, \"ngClass\"], [1, \"m-6\"], [1, \"grid\"], [1, \"col-12\", \"detail-container\"], [\"class\", \"row bold-font font-size-24 \", 4, \"ngIf\"], [1, \"row\", \"font-size-18\", \"table-scroll-mobile\"], [\"class\", \"col-md-12 footer-font ql-editor\", 3, \"innerHtml\", 4, \"ngIf\"], [1, \"col-4\"], [\"class\", \"col-md-2 mt-3\", 4, \"ngIf\"], [1, \"row\", \"bold-font\", \"font-size-24\"], [1, \"col-md-12\"], [1, \"col-md-12\", \"footer-font\", \"ql-editor\", 3, \"innerHtml\"], [1, \"col-md-2\", \"mt-3\"], [\"class\", \"image-container\", 4, \"ngIf\"], [1, \"image-container\"], [\"alt\", \"No Image\", 1, \"data-image\", 3, \"src\"]],\n  template: function AboutUsComponentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AboutUsComponentComponent_section_0_Template, 14, 5, \"section\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.isPageVisible);\n    }\n  },\n  dependencies: [i5.NgClass, i5.NgIf, i6.Card, i7.CommentPipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\nh2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%] {\\n  font-size: 16px !important;\\n}\\n\\nul[_ngcontent-%COMP%], li[_ngcontent-%COMP%], ol[_ngcontent-%COMP%], span[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h2[_ngcontent-%COMP%] {\\n  font-weight: 200 !important;\\n  margin: 0 !important;\\n  padding: 0 !important;\\n}\\n\\n.tg[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  border-spacing: 0;\\n}\\n\\n.tg[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: black;\\n  border-style: solid;\\n  border-width: 1px;\\n  font-family: Arial, sans-serif;\\n  font-size: 14px;\\n  overflow: hidden;\\n  padding: 10px 5px;\\n  word-break: normal;\\n}\\n\\n.tg[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-color: black;\\n  border-style: solid;\\n  border-width: 1px;\\n  font-family: Arial, sans-serif;\\n  font-size: 14px;\\n  font-weight: normal;\\n  overflow: hidden;\\n  padding: 10px 5px;\\n  word-break: normal;\\n}\\n\\n.tg[_ngcontent-%COMP%]   .tg-0pky[_ngcontent-%COMP%] {\\n  border-color: inherit;\\n  text-align: left;\\n  vertical-align: top;\\n}\\n\\n.data-image[_ngcontent-%COMP%] {\\n  width: 220px;\\n  height: 220px;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .about-us-container[_ngcontent-%COMP%] {\\n    padding: 2rem 1rem !important;\\n  }\\n}\\n\\n.detail-container[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 0rem 1.5rem !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .table-scroll-mobile[_ngcontent-%COMP%] {\\n    overflow-y: scroll;\\n  }\\n  .pageTop[_ngcontent-%COMP%] {\\n    margin-top: 100px;\\n  }\\n}\\n.footer-font[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  width: 220px;\\n  height: 220px;\\n  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.12), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 1px 0px rgba(0, 0, 0, 0.2);\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}