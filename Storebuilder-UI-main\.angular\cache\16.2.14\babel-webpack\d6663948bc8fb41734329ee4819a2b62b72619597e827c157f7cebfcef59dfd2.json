{"ast": null, "code": "import { ElementRef } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { ConfigurationKeys } from './enums/sections';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./shared/services/store.service\";\nimport * as i2 from \"./shared/services/cart.service\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"./shared/services/main-data.service\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"ngx-cookie-service\";\nimport * as i9 from \"./shared/services/auth.service\";\nimport * as i10 from \"./shared/services/token.service\";\nconst _c0 = [\"scroll\"];\nfunction AppComponent_section_0_app_header_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nfunction AppComponent_section_0_app_footer_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-footer\", 8);\n    i0.ɵɵlistener(\"scrollToTop\", function AppComponent_section_0_app_footer_8_Template_app_footer_scrollToTop_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.scrollToTop());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1, 2);\n    i0.ɵɵtemplate(2, AppComponent_section_0_app_header_2_Template, 1, 0, \"app-header\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5);\n    i0.ɵɵelement(5, \"app-loader\")(6, \"p-toast\", 6)(7, \"router-outlet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, AppComponent_section_0_app_footer_8_Template, 1, 0, \"app-footer\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.themeApplied);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"baseZIndex\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.themeApplied);\n  }\n}\nexport class AppComponent {\n  constructor(store, cartService, meta, messageService, mainDataService, translate, appTitle, router, cookieService, authService, authTokenService) {\n    this.store = store;\n    this.cartService = cartService;\n    this.meta = meta;\n    this.messageService = messageService;\n    this.mainDataService = mainDataService;\n    this.translate = translate;\n    this.appTitle = appTitle;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authService = authService;\n    this.authTokenService = authTokenService;\n    this.title = 'momo-market';\n    this.lang = 'en';\n    this.storeNames = [];\n    this.display = true;\n    this.loading = false;\n    this.seoTitle = '';\n    this.seoDesc = '';\n    this.isShop = false;\n    this.isRefreshTokenCalled = false;\n    translate.addLangs(['en', 'ar']);\n    this.themeApplied = false;\n    // this language will be used as a fallback when a translation isn't found in the current language\n    translate.setDefaultLang('en');\n    // the lang to use, if the lang isn't available, it will use the current loader to get them\n    // translate.use('en');\n    appTitle.setTitle(this.title);\n  }\n  ngOnInit() {\n    let userToken = this.cookieService.get('authToken');\n    let refreshToken = localStorage.getItem('refreshToken');\n    // if (userToken)\n    // {\n    //   let encryptedText = localStorage.getItem('auth_enc')\n    //   try {\n    //     let decrypt = CryptoJS.AES.decrypt(encryptedText, 'paysky').toString(CryptoJS.enc.Utf8);\n    //     userToken = decrypt\n    //     if (!this.isRefreshTokenCalled)\n    //     {\n    //       this.isRefreshTokenCalled = true\n    //       var model = new RefreshTokenViewModel();\n    //       model.AuthToken = decrypt\n    //       model.RefreshToken = refreshToken\n    //       if (model.AuthToken && model.RefreshToken) {\n    //         this.authService.refreshToken(model).subscribe({\n    //           next: (res: any) => {\n    //             if (res.data != null) {\n    //               let token = res.data.authToken;\n    //               let decoded: any = jwt_decode(token);\n    //               let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n    //               const dateNow = new Date();\n    //               dateNow.setDate(dateNow.getDate() + parseInt(days));\n    //               this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\n    //               localStorage.setItem('refreshToken', res.data.refreshToken);\n    //             } else {\n    //               model.AuthToken = '';\n    //               model.RefreshToken = '';\n    //               this.signOut();\n    //             }\n    //             this.isRefreshTokenCalled = false\n    //             this.triggerInitialCalls()\n    //           },\n    //           error: (err: any) => {\n    //             console.error(err);\n    //             model.AuthToken = '';\n    //             model.RefreshToken = '';\n    //             this.signOut();\n    //             this.isRefreshTokenCalled = false\n    //           }\n    //         });\n    //       } else {\n    //         model.AuthToken = '';\n    //         model.RefreshToken = '';\n    //         this.signOut();\n    //       }\n    //     }\n    //   } catch (e) {\n    //   }\n    // }\n    // else\n    {\n      this.triggerInitialCalls();\n    }\n    // localStorage.setItem('countryPhone', \"\");\n    // this.store.subscription('loading')\n    //   .subscribe({\n    //     next: (res: any) => {\n    //         this.loading = res;\n    //     }\n    //   });\n  }\n\n  ngAfterViewChecked() {\n    // this.scrollToTop()\n  }\n  triggerInitialCalls() {\n    this.routeToTop();\n    this.createStores();\n    this.lang = this.store.get('lang') || 'en';\n    this.setLangSettings();\n    this.getMainData();\n    // this.getCategories();\n    // this.getConfigurationDecimal();\n    // this.getCart();\n    // this.getAllCountryTenants();\n  }\n  // @HostListener('window:resize', ['$event'])\n  // onResize(event: any) {\n  //   const width = event.target.innerWidth;\n  //   if(width <= 1200 && width >= 320){\n  //     window.location.reload()\n  //   }\n  // }\n  routeToTop() {\n    this.router.events.subscribe(event => {\n      if (!(event instanceof NavigationEnd)) {\n        return;\n      }\n      window.scrollTo(0, 0);\n    });\n  }\n  createStores() {\n    this.storeNames = [{\n      name: 'lang',\n      data: this.lang ? this.lang : 'en',\n      localStore: true\n    },\n    // { name: 'authToken', data: null, localStore: true },\n    {\n      name: 'timeInterval',\n      data: null,\n      localStore: true\n    }, {\n      name: 'orderData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'transactionData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'userPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'mainData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'allCountryTenants',\n      data: null,\n      localStore: true\n    }, {\n      name: 'tenantId',\n      data: null,\n      localStore: true\n    }, {\n      name: 'shipmentCost',\n      data: null,\n      localStore: true\n    }, {\n      name: 'isShop',\n      data: null,\n      localStorage: true\n    }, {\n      name: 'currency',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryCode',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'profile',\n      data: null,\n      localStore: true\n    }, {\n      name: 'categories',\n      data: [],\n      localStore: true\n    }, {\n      name: 'notifications',\n      data: null,\n      localStore: false\n    }, {\n      name: 'cartProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'favouritesProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'compareProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'cartProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'favouritesProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'compareProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'checkoutData',\n      data: {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      },\n      localStore: true\n    }, {\n      name: 'search',\n      data: '',\n      localStore: false\n    }, {\n      name: 'loading',\n      data: false,\n      localStore: true\n    }, {\n      name: 'verificationCode',\n      data: '',\n      localStore: true\n    }];\n    /*Create Dynamic BehaviorSubject at Store*/\n    this.storeNames.forEach(item => {\n      this.store.createNewStore(item.name, item.data, item.localStore);\n    });\n  }\n  setLangSettings() {\n    this.translate.setDefaultLang(this.lang);\n    this.translate.use(this.lang);\n    document.documentElement.setAttribute('dir', this.lang === 'ar' ? 'rtl' : 'ltr');\n  }\n  getConfigurationDecimal() {\n    this.mainDataService.getConfigurationData().subscribe({\n      next: res => {\n        if (res?.data?.records?.length > 0) {\n          let records = res.data.records;\n          var decimalValue = records.find(x => x.key == ConfigurationKeys.CurrencyDecimal);\n          var countryPhoneNumber = records.find(x => x.key == ConfigurationKeys.CountryPhone);\n          var phoneLength = records.find(x => x.key == ConfigurationKeys.PhoneLength);\n          var phoneNumberMask = records.find(x => x.key == ConfigurationKeys.PhoneNumberMask);\n          var currencyValue = records.find(x => x.key == ConfigurationKeys.Currency);\n          var emailRequired = records.find(x => x.key == ConfigurationKeys.EmailRequired);\n          var disableCent = records.find(x => x.key == ConfigurationKeys.DisableCents);\n          var customerAddressLandmarkRequired = records.find(x => x.key == ConfigurationKeys.CustomerAddressLandmarkRequired);\n          if (decimalValue) localStorage.setItem('decimalValue', decimalValue.value);\n          if (countryPhoneNumber) localStorage.setItem('countryPhone', countryPhoneNumber.value);\n          if (phoneLength) localStorage.setItem('PhoneLength', phoneLength.value);\n          if (phoneNumberMask) localStorage.setItem('PhoneNumberMask', phoneNumberMask.value);\n          if (currencyValue) localStorage.setItem('currency', currencyValue.value);\n          if (customerAddressLandmarkRequired) localStorage.setItem('customerAddressLandmarkRequired', customerAddressLandmarkRequired.value);\n          if (emailRequired) {\n            localStorage.setItem('emailRequired', emailRequired.value);\n          } else {\n            localStorage.setItem('emailRequired', 'false');\n          }\n          if (disableCent) {\n            localStorage.setItem('disableCent', disableCent.value);\n          } else {\n            localStorage.setItem('disableCent', 'false');\n          }\n        }\n        this.getCart();\n        this.getAllCountryTenants();\n        this.getCategories();\n      },\n      error: res => {\n        this.getCart();\n        this.getAllCountryTenants();\n        this.getCategories();\n      }\n    });\n  }\n  getCart() {\n    var sessionId = localStorage.getItem('sessionId') || '';\n    this.cartService.getCart(sessionId).subscribe({\n      next: res => {\n        if (res.data.records[0] != undefined) {\n          this.store.set('cartProducts', res.data.records[0].cartDetails);\n        }\n      },\n      error: err => {},\n      complete: () => console.log('cartProducts complete')\n    });\n  }\n  getMainData() {\n    this.mainDataService.getInitialData().subscribe({\n      next: res => {\n        this.getConfigurationDecimal();\n        if (res?.data?.result?.records != undefined) {\n          this.store.set('mainData', res.data.result.records);\n          // Dynamic Style\n          Object.entries(res.data.result.records).forEach(([subkey, value]) => {\n            if (res.data.result.records[subkey].key == 'AppTheme') {\n              const dynamicStyle = res.data.result.records[subkey].displayName;\n              if (dynamicStyle != null && dynamicStyle != '') {\n                const dynamicStyleObj = JSON.parse(dynamicStyle);\n                //  const dynamicStyleObj = JSON.parse(dynamicStyleJson);\n                // ////////////////////////////////////////////////////\n                //  this.bg_color =dynamicStyleObj.button.bg_color;\n                this.applyStyle(dynamicStyleObj);\n              }\n            } else {\n              this.themeApplied = true;\n            }\n          });\n        }\n        this.isShop = res.data.isShop;\n        if (this.isShop == true) {\n          this.seoTitle = res.data.shopProductSetting.seoTitle;\n          this.seoDesc = res.data.shopProductSetting.seoDescription;\n          this.meta.updateTag({\n            name: this.seoTitle,\n            content: this.seoDesc\n          });\n        } else {\n          this.meta.updateTag({\n            name: 'Description',\n            content: 'MarketPlace,Buy,products'\n          });\n        }\n      },\n      error: err => {\n        console.error(err);\n        // if (err.status == 0) {\n        this.signOut();\n        // } else {\n        // this.messageService.add({\n        //   severity: 'error',\n        //   summary: this.translate.instant('ErrorMessages.fetchError'),\n        //   detail: err.message,\n        // });\n        // }\n      },\n\n      complete: () => console.log('MainData complete')\n    });\n  }\n  getCategories() {\n    this.mainDataService.getAllCategories().subscribe({\n      next: res => {\n        if (res?.data?.records != undefined) {\n          this.store.set('categories', res.data.records);\n        }\n      },\n      error: err => {\n        console.error(err);\n        // this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\n      },\n\n      complete: () => console.log('Categories complete')\n    });\n  }\n  getAllCountryTenants() {\n    this.mainDataService.getAllCountryTenants().subscribe({\n      next: res => {\n        if (res?.data?.records != undefined) {\n          this.store.set('allCountryTenants', res.data.records);\n        }\n      },\n      error: err => {\n        console.error(err);\n        // this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\n      },\n\n      complete: () => console.log('CountryTenants complete')\n    });\n  }\n  appendCssPropertyValue(key, value, dynamicStyleObj, sub) {\n    if (value) {\n      Object.entries(value).forEach(([subKey, subSubValue]) => {\n        if (value[subKey]) {\n          document.documentElement.style.setProperty('--' + subKey, value[subKey]);\n        } else if (subKey && dynamicStyleObj && dynamicStyleObj[key]) {\n          this.appendCssPropertyValue(subKey, dynamicStyleObj[key][subKey], dynamicStyleObj, true);\n        }\n      });\n    }\n  }\n  applyStyle(dynamicStyleObj) {\n    //\n    Object.entries(dynamicStyleObj).forEach(([key, value]) => {\n      if (value && dynamicStyleObj[key] && (dynamicStyleObj[key]?.length > 2 || Object.entries(dynamicStyleObj[key])?.length >= 1)) {\n        if (dynamicStyleObj[key] != null && (dynamicStyleObj[key][0]?.length == 1 || Number(dynamicStyleObj[key][0]))) {\n          document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n        } else {\n          this.appendCssPropertyValue(key, dynamicStyleObj[key], dynamicStyleObj, false);\n        }\n      } else {\n        document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n      }\n    });\n    this.themeApplied = true;\n  }\n  signOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', window.location.hostname);\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/login']);\n    // localStorage.clear();\n    sessionStorage.clear();\n    // this.store.set('authToken', '');\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    this.getMainData();\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      // this.store.set('authToken', null);\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      // localStorage.setItem('authToken', '');\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n    }\n  }\n  scrollToTop() {\n    this.scroll.nativeElement.scrollTop = 0;\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.Meta), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.MainDataService), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i3.Title), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.CookieService), i0.ɵɵdirectiveInject(i9.AuthService), i0.ɵɵdirectiveInject(i10.AuthTokenService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    viewQuery: function AppComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroll = _t.first);\n      }\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"container\", 4, \"ngIf\"], [1, \"container\"], [\"scroll\", \"\"], [4, \"ngIf\"], [1, \"page\"], [1, \"main-content\"], [\"sticky\", \"true\", 1, \"toast\", 3, \"baseZIndex\"], [3, \"scrollToTop\", 4, \"ngIf\"], [3, \"scrollToTop\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AppComponent_section_0_Template, 9, 3, \"section\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isRefreshTokenCalled);\n      }\n    },\n    styles: [\".container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n}\\n\\n.page[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  min-height: 100vh;\\n}\\n\\n  .p-toast-top-right {\\n  top: 7rem !important;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n    .p-toast-top-right {\\n    top: 7rem !important;\\n    right: 0px !important;\\n    left: 0px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0FBRUY7O0FBR0E7RUFDRSxvQkFBQTtBQUFGOztBQUdBO0VBRUk7SUFDRSxvQkFBQTtJQUNBLHFCQUFBO0lBQ0Esb0JBQUE7RUFESjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmNvbnRhaW5lciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIG92ZXJmbG93OiBhdXRvO1xyXG59XHJcbi5wYWdlIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG59XHJcbi8vIC5tYWluLWNvbnRlbnR7XHJcbi8vICAgLy8gbWFyZ2luLXRvcDogMTUzcHg7XHJcbi8vIH1cclxuOjpuZy1kZWVwIC5wLXRvYXN0LXRvcC1yaWdodCB7XHJcbiAgdG9wOiA3cmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbkBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzg2cHgpIHtcclxuICA6Om5nLWRlZXAge1xyXG4gICAgLnAtdG9hc3QtdG9wLXJpZ2h0IHtcclxuICAgICAgdG9wOiA3cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIHJpZ2h0OiAwcHggIWltcG9ydGFudDtcclxuICAgICAgbGVmdDogMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["ElementRef", "NavigationEnd", "Configuration<PERSON>eys", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "AppComponent_section_0_app_footer_8_Template_app_footer_scrollToTop_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "scrollToTop", "ɵɵelementEnd", "ɵɵtemplate", "AppComponent_section_0_app_header_2_Template", "AppComponent_section_0_app_footer_8_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "themeApplied", "AppComponent", "constructor", "store", "cartService", "meta", "messageService", "mainDataService", "translate", "appTitle", "router", "cookieService", "authService", "authTokenService", "title", "lang", "storeNames", "display", "loading", "seo<PERSON><PERSON>le", "seoDesc", "isShop", "isRefreshTokenCalled", "addLangs", "setDefaultLang", "setTitle", "ngOnInit", "userToken", "get", "refreshToken", "localStorage", "getItem", "triggerInitialCalls", "ngAfterViewChecked", "routeToTop", "createStores", "setLangSettings", "getMainData", "events", "subscribe", "event", "window", "scrollTo", "name", "data", "localStore", "shipping", "payment", "promo", "steps", "profile", "orderId", "for<PERSON>ach", "item", "createNewStore", "use", "document", "documentElement", "setAttribute", "getConfigurationDecimal", "getConfigurationData", "next", "res", "records", "length", "decimalValue", "find", "x", "key", "CurrencyDecimal", "countryPhoneNumber", "CountryPhone", "phoneLength", "PhoneLength", "phoneNumberMask", "PhoneNumberMask", "currencyValue", "<PERSON><PERSON><PERSON><PERSON>", "emailRequired", "EmailRequired", "disableCent", "DisableCents", "customerAddressLandmarkRequired", "CustomerAddressLandmarkRequired", "setItem", "value", "getCart", "getAllCountryTenants", "getCategories", "error", "sessionId", "undefined", "set", "cartDetails", "err", "complete", "console", "log", "getInitialData", "result", "Object", "entries", "subkey", "dynamicStyle", "displayName", "dynamicStyleObj", "JSON", "parse", "applyStyle", "shopProductSetting", "seoDescription", "updateTag", "content", "signOut", "getAllCategories", "appendCssPropertyV<PERSON>ue", "sub", "subKey", "subSubValue", "style", "setProperty", "Number", "setStoreData", "authTokenSet", "delete", "location", "hostname", "removeItem", "navigate", "sessionStorage", "clear", "localStoreNames", "notifications", "unreadNotifications", "stringify", "scroll", "nativeElement", "scrollTop", "_", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "CartService", "i3", "Meta", "i4", "MessageService", "i5", "MainDataService", "i6", "TranslateService", "Title", "i7", "Router", "i8", "CookieService", "i9", "AuthService", "i10", "AuthTokenService", "_2", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "AppComponent_section_0_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\app.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, ElementRef, HostListener, ViewChild } from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Meta, Title } from '@angular/platform-browser';\r\nimport { StoreService } from './shared/services/store.service';\r\nimport { MainDataService } from './shared/services/main-data.service';\r\nimport { CartService } from './shared/services/cart.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { json } from 'express';\r\nimport { ConfigurationKeys } from './enums/sections';\r\nimport { RefreshTokenViewModel } from './interfaces/auth';\r\nimport jwt_decode from 'jwt-decode';\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { AuthService } from './shared/services/auth.service';\r\nimport { AuthTokenService } from './shared/services/token.service';\r\n\r\ndeclare var CryptoJS: any;\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n  // providers: [MessageService]\r\n})\r\nexport class AppComponent {\r\n  @ViewChild('scroll', { read: ElementRef }) public scroll!: ElementRef<any>;\r\n  title: string = 'momo-market';\r\n  lang: string = 'en';\r\n  storeNames: Array<any> = [];\r\n  display: boolean = true;\r\n  loading: boolean = false;\r\n  themeApplied: boolean;\r\n  seoTitle: string = '';\r\n  seoDesc: string = '';\r\n  isShop: boolean = false;\r\n  isRefreshTokenCalled: boolean = false;\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private cartService: CartService,\r\n    private meta: Meta,\r\n    private messageService: MessageService,\r\n    private mainDataService: MainDataService,\r\n    private translate: TranslateService,\r\n    private appTitle: Title,\r\n    private router: Router,\r\n    private cookieService: CookieService,\r\n    private authService: AuthService,\r\n    private authTokenService: AuthTokenService\r\n  ) {\r\n    translate.addLangs(['en', 'ar']);\r\n    this.themeApplied = false;\r\n    // this language will be used as a fallback when a translation isn't found in the current language\r\n    translate.setDefaultLang('en');\r\n\r\n    // the lang to use, if the lang isn't available, it will use the current loader to get them\r\n    // translate.use('en');\r\n\r\n    appTitle.setTitle(this.title);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    let userToken: any = this.cookieService.get('authToken');\r\n    let refreshToken: any = localStorage.getItem('refreshToken');\r\n    // if (userToken)\r\n    // {\r\n    //   let encryptedText = localStorage.getItem('auth_enc')\r\n    //   try {\r\n    //     let decrypt = CryptoJS.AES.decrypt(encryptedText, 'paysky').toString(CryptoJS.enc.Utf8);\r\n    //     userToken = decrypt\r\n    //     if (!this.isRefreshTokenCalled)\r\n    //     {\r\n    //       this.isRefreshTokenCalled = true\r\n\r\n    //       var model = new RefreshTokenViewModel();\r\n    //       model.AuthToken = decrypt\r\n    //       model.RefreshToken = refreshToken\r\n    //       if (model.AuthToken && model.RefreshToken) {\r\n    //         this.authService.refreshToken(model).subscribe({\r\n    //           next: (res: any) => {\r\n    //             if (res.data != null) {\r\n    //               let token = res.data.authToken;\r\n\r\n    //               let decoded: any = jwt_decode(token);\r\n    //               let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\r\n\r\n    //               const dateNow = new Date();\r\n    //               dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n    //               this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\r\n    //               localStorage.setItem('refreshToken', res.data.refreshToken);\r\n    //             } else {\r\n    //               model.AuthToken = '';\r\n    //               model.RefreshToken = '';\r\n    //               this.signOut();\r\n    //             }\r\n    //             this.isRefreshTokenCalled = false\r\n\r\n    //             this.triggerInitialCalls()\r\n    //           },\r\n    //           error: (err: any) => {\r\n    //             console.error(err);\r\n    //             model.AuthToken = '';\r\n    //             model.RefreshToken = '';\r\n    //             this.signOut();\r\n    //             this.isRefreshTokenCalled = false\r\n    //           }\r\n    //         });\r\n    //       } else {\r\n    //         model.AuthToken = '';\r\n    //         model.RefreshToken = '';\r\n    //         this.signOut();\r\n    //       }\r\n    //     }\r\n    //   } catch (e) {\r\n    //   }\r\n    // }\r\n    // else\r\n    {\r\n      this.triggerInitialCalls();\r\n    }\r\n\r\n    // localStorage.setItem('countryPhone', \"\");\r\n\r\n    // this.store.subscription('loading')\r\n    //   .subscribe({\r\n    //     next: (res: any) => {\r\n    //         this.loading = res;\r\n    //     }\r\n    //   });\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    // this.scrollToTop()\r\n  }\r\n\r\n  triggerInitialCalls() {\r\n    this.routeToTop();\r\n    this.createStores();\r\n    this.lang = this.store.get('lang') || 'en';\r\n    this.setLangSettings();\r\n    this.getMainData();\r\n    // this.getCategories();\r\n    // this.getConfigurationDecimal();\r\n    // this.getCart();\r\n    // this.getAllCountryTenants();\r\n  }\r\n\r\n  // @HostListener('window:resize', ['$event'])\r\n  // onResize(event: any) {\r\n  //   const width = event.target.innerWidth;\r\n  //   if(width <= 1200 && width >= 320){\r\n  //     window.location.reload()\r\n  //   }\r\n  // }\r\n\r\n  routeToTop(): void {\r\n    this.router.events.subscribe((event) => {\r\n      if (!(event instanceof NavigationEnd)) {\r\n        return;\r\n      }\r\n      window.scrollTo(0, 0);\r\n    });\r\n  }\r\n\r\n  createStores(): void {\r\n    this.storeNames = [\r\n      { name: 'lang', data: this.lang ? this.lang : 'en', localStore: true },\r\n      // { name: 'authToken', data: null, localStore: true },\r\n      { name: 'timeInterval', data: null, localStore: true },\r\n      { name: 'orderData', data: null, localStore: true },\r\n      { name: 'transactionData', data: null, localStore: true },\r\n      { name: 'userPhone', data: null, localStore: true },\r\n      { name: 'mainData', data: null, localStore: true },\r\n      { name: 'allCountryTenants', data: null, localStore: true },\r\n      { name: 'tenantId', data: null, localStore: true },\r\n      { name: 'shipmentCost', data: null, localStore: true },\r\n      { name: 'isShop', data: null, localStorage: true },\r\n      { name: 'currency', data: null, localStore: true },\r\n      { name: 'countryCode', data: null, localStore: true },\r\n      { name: 'countryPhone', data: null, localStore: true },\r\n      { name: 'profile', data: null, localStore: true },\r\n      { name: 'categories', data: [], localStore: true },\r\n      { name: 'notifications', data: null, localStore: false },\r\n      { name: 'cartProducts', data: [], localStore: true },\r\n      { name: 'favouritesProducts', data: [], localStore: true },\r\n      { name: 'compareProducts', data: [], localStore: true },\r\n      { name: 'cartProductSuccess', data: null, localStore: false },\r\n      { name: 'favouritesProductSuccess', data: null, localStore: false },\r\n      { name: 'compareProductSuccess', data: null, localStore: false },\r\n      {\r\n        name: 'checkoutData',\r\n        data: {\r\n          shipping: null,\r\n          payment: null,\r\n          promo: null,\r\n          steps: null,\r\n          profile: null,\r\n          orderId: null,\r\n        },\r\n        localStore: true,\r\n      },\r\n      { name: 'search', data: '', localStore: false },\r\n      { name: 'loading', data: false, localStore: true },\r\n      { name: 'verificationCode', data: '', localStore: true },\r\n    ];\r\n\r\n    /*Create Dynamic BehaviorSubject at Store*/\r\n    this.storeNames.forEach((item) => {\r\n      this.store.createNewStore(item.name, item.data, item.localStore);\r\n    });\r\n  }\r\n\r\n  setLangSettings(): void {\r\n    this.translate.setDefaultLang(this.lang);\r\n    this.translate.use(this.lang);\r\n    document.documentElement.setAttribute(\r\n      'dir',\r\n      this.lang === 'ar' ? 'rtl' : 'ltr'\r\n    );\r\n  }\r\n\r\n  getConfigurationDecimal() {\r\n    this.mainDataService.getConfigurationData().subscribe({\r\n      next: (res: any) => {\r\n        if (res?.data?.records?.length > 0) {\r\n          let records = res.data.records;\r\n          var decimalValue = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.CurrencyDecimal\r\n          );\r\n          var countryPhoneNumber = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.CountryPhone\r\n          );\r\n          var phoneLength = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.PhoneLength\r\n          );\r\n          var phoneNumberMask = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.PhoneNumberMask\r\n          );\r\n          var currencyValue = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.Currency\r\n          );\r\n\r\n          var emailRequired = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.EmailRequired\r\n          );\r\n          var disableCent = records.find(\r\n            (x: any) => x.key == ConfigurationKeys.DisableCents\r\n          );\r\n\r\n          var customerAddressLandmarkRequired = records.find(\r\n            (x: any) =>\r\n              x.key == ConfigurationKeys.CustomerAddressLandmarkRequired\r\n          );\r\n          if (decimalValue)\r\n            localStorage.setItem('decimalValue', decimalValue.value);\r\n          if (countryPhoneNumber)\r\n            localStorage.setItem('countryPhone', countryPhoneNumber.value);\r\n          if (phoneLength)\r\n            localStorage.setItem('PhoneLength', phoneLength.value);\r\n            if (phoneNumberMask)\r\n            localStorage.setItem('PhoneNumberMask', phoneNumberMask.value);\r\n          if (currencyValue)\r\n            localStorage.setItem('currency', currencyValue.value);\r\n          if (customerAddressLandmarkRequired)\r\n            localStorage.setItem(\r\n              'customerAddressLandmarkRequired',\r\n              customerAddressLandmarkRequired.value\r\n            );\r\n          if (emailRequired) {\r\n            localStorage.setItem('emailRequired', emailRequired.value);\r\n          } else {\r\n            localStorage.setItem('emailRequired', 'false');\r\n          }\r\n          if (disableCent) {\r\n            localStorage.setItem('disableCent', disableCent.value);\r\n          } else {\r\n            localStorage.setItem('disableCent', 'false');\r\n          }\r\n        }\r\n        this.getCart();\r\n        this.getAllCountryTenants();\r\n        this.getCategories();\r\n      },\r\n      error: (res: any) => {\r\n        this.getCart();\r\n        this.getAllCountryTenants();\r\n        this.getCategories();\r\n      },\r\n    });\r\n  }\r\n\r\n  getCart(): void {\r\n    var sessionId = localStorage.getItem('sessionId') || '';\r\n\r\n    this.cartService.getCart(sessionId).subscribe({\r\n      next: (res: any) => {\r\n        if (res.data.records[0] != undefined) {\r\n          this.store.set('cartProducts', res.data.records[0].cartDetails);\r\n        }\r\n      },\r\n      error: (err: any) => {},\r\n      complete: () => console.log('cartProducts complete'),\r\n    });\r\n  }\r\n\r\n  bg_color: any;\r\n\r\n  getMainData(): void {\r\n    this.mainDataService.getInitialData().subscribe({\r\n      next: (res: any) => {\r\n        this.getConfigurationDecimal();\r\n        if (res?.data?.result?.records != undefined) {\r\n          this.store.set('mainData', res.data.result.records);\r\n          // Dynamic Style\r\n          Object.entries(res.data.result.records).forEach(([subkey, value]) => {\r\n            if (res.data.result.records[subkey].key == 'AppTheme') {\r\n              const dynamicStyle = res.data.result.records[subkey].displayName;\r\n              if (dynamicStyle != null && dynamicStyle != '') {\r\n                const dynamicStyleObj = JSON.parse(dynamicStyle);\r\n                //  const dynamicStyleObj = JSON.parse(dynamicStyleJson);\r\n                // ////////////////////////////////////////////////////\r\n                //  this.bg_color =dynamicStyleObj.button.bg_color;\r\n                this.applyStyle(dynamicStyleObj);\r\n              }\r\n            } else {\r\n              this.themeApplied = true;\r\n            }\r\n          });\r\n        }\r\n        this.isShop = res.data.isShop;\r\n\r\n        if (this.isShop == true) {\r\n          this.seoTitle = res.data.shopProductSetting.seoTitle;\r\n          this.seoDesc = res.data.shopProductSetting.seoDescription;\r\n\r\n          this.meta.updateTag({ name: this.seoTitle, content: this.seoDesc });\r\n        } else {\r\n          this.meta.updateTag({\r\n            name: 'Description',\r\n            content: 'MarketPlace,Buy,products',\r\n          });\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n        // if (err.status == 0) {\r\n          this.signOut();\r\n        // } else {\r\n          // this.messageService.add({\r\n          //   severity: 'error',\r\n          //   summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          //   detail: err.message,\r\n          // });\r\n        // }\r\n      },\r\n      complete: () => console.log('MainData complete'),\r\n    });\r\n  }\r\n\r\n  getCategories(): void {\r\n    this.mainDataService.getAllCategories().subscribe({\r\n      next: (res: any) => {\r\n        if (res?.data?.records != undefined) {\r\n          this.store.set('categories', res.data.records);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n        // this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\r\n      },\r\n      complete: () => console.log('Categories complete'),\r\n    });\r\n  }\r\n\r\n  getAllCountryTenants(): void {\r\n    this.mainDataService.getAllCountryTenants().subscribe({\r\n      next: (res: any) => {\r\n        if (res?.data?.records != undefined) {\r\n          this.store.set('allCountryTenants', res.data.records);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        console.error(err);\r\n        // this.messageService.add({ severity: 'error', summary: this.translate.instant('ErrorMessages.fetchError'), detail: err.message });\r\n      },\r\n      complete: () => console.log('CountryTenants complete'),\r\n    });\r\n  }\r\n\r\n  appendCssPropertyValue(\r\n    key: any,\r\n    value: any,\r\n    dynamicStyleObj: any,\r\n    sub: boolean\r\n  ) {\r\n    if (value) {\r\n      Object.entries(value).forEach(([subKey, subSubValue]) => {\r\n        if (value[subKey]) {\r\n          document.documentElement.style.setProperty(\r\n            '--' + subKey,\r\n            value[subKey]\r\n          );\r\n        } else if (subKey && dynamicStyleObj && dynamicStyleObj[key]) {\r\n          this.appendCssPropertyValue(\r\n            subKey,\r\n            dynamicStyleObj[key][subKey],\r\n            dynamicStyleObj,\r\n            true\r\n          );\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  applyStyle(dynamicStyleObj: any) {\r\n    //\r\n    Object.entries(dynamicStyleObj).forEach(([key, value]) => {\r\n      if (\r\n        value &&\r\n        dynamicStyleObj[key] &&\r\n        (dynamicStyleObj[key]?.length > 2 ||\r\n          Object.entries(dynamicStyleObj[key])?.length >= 1)\r\n      ) {\r\n        if (\r\n          dynamicStyleObj[key] != null &&\r\n          (dynamicStyleObj[key][0]?.length == 1 ||\r\n            Number(dynamicStyleObj[key][0]))\r\n        ) {\r\n          document.documentElement.style.setProperty(\r\n            '--' + key,\r\n            dynamicStyleObj[key]\r\n          );\r\n        } else {\r\n          this.appendCssPropertyValue(\r\n            key,\r\n            dynamicStyleObj[key],\r\n            dynamicStyleObj,\r\n            false\r\n          );\r\n        }\r\n      } else {\r\n        document.documentElement.style.setProperty(\r\n          '--' + key,\r\n          dynamicStyleObj[key]\r\n        );\r\n      }\r\n    });\r\n    this.themeApplied = true;\r\n  }\r\n\r\n  signOut(): void {\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet('');\r\n    this.cookieService.delete('authToken', window.location.hostname);\r\n\r\n    this.store.set('cartProducts', '');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    this.router.navigate(['/login']);\r\n\r\n    // localStorage.clear();\r\n\r\n    sessionStorage.clear();\r\n    // this.store.set('authToken', '');\r\n    this.store.set('profile', '');\r\n    this.store.set('cartProducts', '');\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    this.getMainData();\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n      // this.store.set('authToken', null);\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0,\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null,\r\n      });\r\n    } else {\r\n      // localStorage.setItem('authToken', '');\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n      localStorage.removeItem('refreshToken');\r\n      localStorage.removeItem('auth_enc');\r\n    }\r\n  }\r\n\r\n  scrollToTop() {\r\n    this.scroll.nativeElement.scrollTop = 0;\r\n  }\r\n}\r\n", "<section class=\"container\" *ngIf=\"!isRefreshTokenCalled\" #scroll>\r\n  <app-header *ngIf=\"themeApplied\"></app-header>\r\n  <div class=\"page\">\r\n    <div class=\"main-content\">\r\n      <app-loader></app-loader>\r\n      <!-- <div *ngIf=\"loading\" class=\"spinner\">\r\n          <p-progressSpinner></p-progressSpinner>\r\n        </div> -->\r\n      <p-toast class=\"toast\" sticky=\"true\" [baseZIndex]=\"100\"></p-toast>\r\n      <router-outlet></router-outlet>\r\n    </div>\r\n    <app-footer (scrollToTop)=\"scrollToTop()\" *ngIf=\"themeApplied\"></app-footer>\r\n  </div>\r\n</section>\r\n"], "mappings": "AAAA,SAAoBA,UAAU,QAAiC,eAAe;AAO9E,SAASC,aAAa,QAAgB,iBAAiB;AAEvD,SAASC,iBAAiB,QAAQ,kBAAkB;;;;;;;;;;;;;;;ICRlDC,EAAA,CAAAC,SAAA,iBAA8C;;;;;;IAU5CD,EAAA,CAAAE,cAAA,oBAA+D;IAAnDF,EAAA,CAAAG,UAAA,yBAAAC,+EAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAeR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAAsBV,EAAA,CAAAW,YAAA,EAAa;;;;;IAXhFX,EAAA,CAAAE,cAAA,oBAAiE;IAC/DF,EAAA,CAAAY,UAAA,IAAAC,4CAAA,wBAA8C;IAC9Cb,EAAA,CAAAE,cAAA,aAAkB;IAEdF,EAAA,CAAAC,SAAA,iBAAyB;IAM3BD,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAY,UAAA,IAAAE,4CAAA,wBAA4E;IAC9Ed,EAAA,CAAAW,YAAA,EAAM;;;;IAXOX,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAkB;IAOUlB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,UAAA,mBAAkB;IAGdhB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,YAAA,CAAkB;;;ADajE,OAAM,MAAOC,YAAY;EAavBC,YACUC,KAAmB,EACnBC,WAAwB,EACxBC,IAAU,EACVC,cAA8B,EAC9BC,eAAgC,EAChCC,SAA2B,EAC3BC,QAAe,EACfC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB,EACxBC,gBAAkC;IAVlC,KAAAV,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAtB1B,KAAAC,KAAK,GAAW,aAAa;IAC7B,KAAAC,IAAI,GAAW,IAAI;IACnB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,OAAO,GAAY,KAAK;IAExB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,MAAM,GAAY,KAAK;IACvB,KAAAC,oBAAoB,GAAY,KAAK;IAenCd,SAAS,CAACe,QAAQ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,IAAI,CAACvB,YAAY,GAAG,KAAK;IACzB;IACAQ,SAAS,CAACgB,cAAc,CAAC,IAAI,CAAC;IAE9B;IACA;IAEAf,QAAQ,CAACgB,QAAQ,CAAC,IAAI,CAACX,KAAK,CAAC;EAC/B;EAEAY,QAAQA,CAAA;IACN,IAAIC,SAAS,GAAQ,IAAI,CAAChB,aAAa,CAACiB,GAAG,CAAC,WAAW,CAAC;IACxD,IAAIC,YAAY,GAAQC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAC5D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACE,IAAI,CAACC,mBAAmB,EAAE;;IAG5B;IAEA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEAC,kBAAkBA,CAAA;IAChB;EAAA;EAGFD,mBAAmBA,CAAA;IACjB,IAAI,CAACE,UAAU,EAAE;IACjB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACpB,IAAI,GAAG,IAAI,CAACZ,KAAK,CAACyB,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI;IAC1C,IAAI,CAACQ,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,EAAE;IAClB;IACA;IACA;IACA;EACF;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAH,UAAUA,CAAA;IACR,IAAI,CAACxB,MAAM,CAAC4B,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACrC,IAAI,EAAEA,KAAK,YAAY5D,aAAa,CAAC,EAAE;QACrC;;MAEF6D,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ;EAEAP,YAAYA,CAAA;IACV,IAAI,CAACnB,UAAU,GAAG,CAChB;MAAE2B,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI,CAAC7B,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI;MAAE8B,UAAU,EAAE;IAAI,CAAE;IACtE;IACA;MAAEF,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACtD;MAAEF,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACnD;MAAEF,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACzD;MAAEF,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACnD;MAAEF,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EAClD;MAAEF,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EAC3D;MAAEF,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EAClD;MAAEF,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACtD;MAAEF,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEd,YAAY,EAAE;IAAI,CAAE,EAClD;MAAEa,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EAClD;MAAEF,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACrD;MAAEF,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACtD;MAAEF,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,EACjD;MAAEF,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,EAClD;MAAEF,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAE,EACxD;MAAEF,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,EACpD;MAAEF,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,EAC1D;MAAEF,IAAI,EAAE,iBAAiB;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,EACvD;MAAEF,IAAI,EAAE,oBAAoB;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAE,EAC7D;MAAEF,IAAI,EAAE,0BAA0B;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAE,EACnE;MAAEF,IAAI,EAAE,uBAAuB;MAAEC,IAAI,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAE,EAChE;MACEF,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE;QACJE,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV;MACDN,UAAU,EAAE;KACb,EACD;MAAEF,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAK,CAAE,EAC/C;MAAEF,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAI,CAAE,EAClD;MAAEF,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CACzD;IAED;IACA,IAAI,CAAC7B,UAAU,CAACoC,OAAO,CAAEC,IAAI,IAAI;MAC/B,IAAI,CAAClD,KAAK,CAACmD,cAAc,CAACD,IAAI,CAACV,IAAI,EAAEU,IAAI,CAACT,IAAI,EAAES,IAAI,CAACR,UAAU,CAAC;IAClE,CAAC,CAAC;EACJ;EAEAT,eAAeA,CAAA;IACb,IAAI,CAAC5B,SAAS,CAACgB,cAAc,CAAC,IAAI,CAACT,IAAI,CAAC;IACxC,IAAI,CAACP,SAAS,CAAC+C,GAAG,CAAC,IAAI,CAACxC,IAAI,CAAC;IAC7ByC,QAAQ,CAACC,eAAe,CAACC,YAAY,CACnC,KAAK,EACL,IAAI,CAAC3C,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CACnC;EACH;EAEA4C,uBAAuBA,CAAA;IACrB,IAAI,CAACpD,eAAe,CAACqD,oBAAoB,EAAE,CAACrB,SAAS,CAAC;MACpDsB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAElB,IAAI,EAAEmB,OAAO,EAAEC,MAAM,GAAG,CAAC,EAAE;UAClC,IAAID,OAAO,GAAGD,GAAG,CAAClB,IAAI,CAACmB,OAAO;UAC9B,IAAIE,YAAY,GAAGF,OAAO,CAACG,IAAI,CAC5BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAACwF,eAAe,CACvD;UACD,IAAIC,kBAAkB,GAAGP,OAAO,CAACG,IAAI,CAClCC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAAC0F,YAAY,CACpD;UACD,IAAIC,WAAW,GAAGT,OAAO,CAACG,IAAI,CAC3BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAAC4F,WAAW,CACnD;UACD,IAAIC,eAAe,GAAGX,OAAO,CAACG,IAAI,CAC/BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAAC8F,eAAe,CACvD;UACD,IAAIC,aAAa,GAAGb,OAAO,CAACG,IAAI,CAC7BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAACgG,QAAQ,CAChD;UAED,IAAIC,aAAa,GAAGf,OAAO,CAACG,IAAI,CAC7BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAACkG,aAAa,CACrD;UACD,IAAIC,WAAW,GAAGjB,OAAO,CAACG,IAAI,CAC3BC,CAAM,IAAKA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAACoG,YAAY,CACpD;UAED,IAAIC,+BAA+B,GAAGnB,OAAO,CAACG,IAAI,CAC/CC,CAAM,IACLA,CAAC,CAACC,GAAG,IAAIvF,iBAAiB,CAACsG,+BAA+B,CAC7D;UACD,IAAIlB,YAAY,EACdnC,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAEnB,YAAY,CAACoB,KAAK,CAAC;UAC1D,IAAIf,kBAAkB,EACpBxC,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAEd,kBAAkB,CAACe,KAAK,CAAC;UAChE,IAAIb,WAAW,EACb1C,YAAY,CAACsD,OAAO,CAAC,aAAa,EAAEZ,WAAW,CAACa,KAAK,CAAC;UACtD,IAAIX,eAAe,EACnB5C,YAAY,CAACsD,OAAO,CAAC,iBAAiB,EAAEV,eAAe,CAACW,KAAK,CAAC;UAChE,IAAIT,aAAa,EACf9C,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAER,aAAa,CAACS,KAAK,CAAC;UACvD,IAAIH,+BAA+B,EACjCpD,YAAY,CAACsD,OAAO,CAClB,iCAAiC,EACjCF,+BAA+B,CAACG,KAAK,CACtC;UACH,IAAIP,aAAa,EAAE;YACjBhD,YAAY,CAACsD,OAAO,CAAC,eAAe,EAAEN,aAAa,CAACO,KAAK,CAAC;WAC3D,MAAM;YACLvD,YAAY,CAACsD,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC;;UAEhD,IAAIJ,WAAW,EAAE;YACflD,YAAY,CAACsD,OAAO,CAAC,aAAa,EAAEJ,WAAW,CAACK,KAAK,CAAC;WACvD,MAAM;YACLvD,YAAY,CAACsD,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC;;;QAGhD,IAAI,CAACE,OAAO,EAAE;QACd,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC;MACDC,KAAK,EAAG3B,GAAQ,IAAI;QAClB,IAAI,CAACwB,OAAO,EAAE;QACd,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACC,aAAa,EAAE;MACtB;KACD,CAAC;EACJ;EAEAF,OAAOA,CAAA;IACL,IAAII,SAAS,GAAG5D,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE;IAEvD,IAAI,CAAC3B,WAAW,CAACkF,OAAO,CAACI,SAAS,CAAC,CAACnD,SAAS,CAAC;MAC5CsB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAAClB,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,IAAI4B,SAAS,EAAE;UACpC,IAAI,CAACxF,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE9B,GAAG,CAAClB,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC8B,WAAW,CAAC;;MAEnE,CAAC;MACDJ,KAAK,EAAGK,GAAQ,IAAI,CAAE,CAAC;MACvBC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,uBAAuB;KACpD,CAAC;EACJ;EAIA5D,WAAWA,CAAA;IACT,IAAI,CAAC9B,eAAe,CAAC2F,cAAc,EAAE,CAAC3D,SAAS,CAAC;MAC9CsB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACH,uBAAuB,EAAE;QAC9B,IAAIG,GAAG,EAAElB,IAAI,EAAEuD,MAAM,EAAEpC,OAAO,IAAI4B,SAAS,EAAE;UAC3C,IAAI,CAACxF,KAAK,CAACyF,GAAG,CAAC,UAAU,EAAE9B,GAAG,CAAClB,IAAI,CAACuD,MAAM,CAACpC,OAAO,CAAC;UACnD;UACAqC,MAAM,CAACC,OAAO,CAACvC,GAAG,CAAClB,IAAI,CAACuD,MAAM,CAACpC,OAAO,CAAC,CAACX,OAAO,CAAC,CAAC,CAACkD,MAAM,EAAEjB,KAAK,CAAC,KAAI;YAClE,IAAIvB,GAAG,CAAClB,IAAI,CAACuD,MAAM,CAACpC,OAAO,CAACuC,MAAM,CAAC,CAAClC,GAAG,IAAI,UAAU,EAAE;cACrD,MAAMmC,YAAY,GAAGzC,GAAG,CAAClB,IAAI,CAACuD,MAAM,CAACpC,OAAO,CAACuC,MAAM,CAAC,CAACE,WAAW;cAChE,IAAID,YAAY,IAAI,IAAI,IAAIA,YAAY,IAAI,EAAE,EAAE;gBAC9C,MAAME,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC;gBAChD;gBACA;gBACA;gBACA,IAAI,CAACK,UAAU,CAACH,eAAe,CAAC;;aAEnC,MAAM;cACL,IAAI,CAACzG,YAAY,GAAG,IAAI;;UAE5B,CAAC,CAAC;;QAEJ,IAAI,CAACqB,MAAM,GAAGyC,GAAG,CAAClB,IAAI,CAACvB,MAAM;QAE7B,IAAI,IAAI,CAACA,MAAM,IAAI,IAAI,EAAE;UACvB,IAAI,CAACF,QAAQ,GAAG2C,GAAG,CAAClB,IAAI,CAACiE,kBAAkB,CAAC1F,QAAQ;UACpD,IAAI,CAACC,OAAO,GAAG0C,GAAG,CAAClB,IAAI,CAACiE,kBAAkB,CAACC,cAAc;UAEzD,IAAI,CAACzG,IAAI,CAAC0G,SAAS,CAAC;YAAEpE,IAAI,EAAE,IAAI,CAACxB,QAAQ;YAAE6F,OAAO,EAAE,IAAI,CAAC5F;UAAO,CAAE,CAAC;SACpE,MAAM;UACL,IAAI,CAACf,IAAI,CAAC0G,SAAS,CAAC;YAClBpE,IAAI,EAAE,aAAa;YACnBqE,OAAO,EAAE;WACV,CAAC;;MAEN,CAAC;MACDvB,KAAK,EAAGK,GAAQ,IAAI;QAClBE,OAAO,CAACP,KAAK,CAACK,GAAG,CAAC;QAClB;QACE,IAAI,CAACmB,OAAO,EAAE;QAChB;QACE;QACA;QACA;QACA;QACA;QACF;MACF,CAAC;;MACDlB,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,mBAAmB;KAChD,CAAC;EACJ;EAEAT,aAAaA,CAAA;IACX,IAAI,CAACjF,eAAe,CAAC2G,gBAAgB,EAAE,CAAC3E,SAAS,CAAC;MAChDsB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAElB,IAAI,EAAEmB,OAAO,IAAI4B,SAAS,EAAE;UACnC,IAAI,CAACxF,KAAK,CAACyF,GAAG,CAAC,YAAY,EAAE9B,GAAG,CAAClB,IAAI,CAACmB,OAAO,CAAC;;MAElD,CAAC;MACD0B,KAAK,EAAGK,GAAQ,IAAI;QAClBE,OAAO,CAACP,KAAK,CAACK,GAAG,CAAC;QAClB;MACF,CAAC;;MACDC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,qBAAqB;KAClD,CAAC;EACJ;EAEAV,oBAAoBA,CAAA;IAClB,IAAI,CAAChF,eAAe,CAACgF,oBAAoB,EAAE,CAAChD,SAAS,CAAC;MACpDsB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAElB,IAAI,EAAEmB,OAAO,IAAI4B,SAAS,EAAE;UACnC,IAAI,CAACxF,KAAK,CAACyF,GAAG,CAAC,mBAAmB,EAAE9B,GAAG,CAAClB,IAAI,CAACmB,OAAO,CAAC;;MAEzD,CAAC;MACD0B,KAAK,EAAGK,GAAQ,IAAI;QAClBE,OAAO,CAACP,KAAK,CAACK,GAAG,CAAC;QAClB;MACF,CAAC;;MACDC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,yBAAyB;KACtD,CAAC;EACJ;EAEAkB,sBAAsBA,CACpB/C,GAAQ,EACRiB,KAAU,EACVoB,eAAoB,EACpBW,GAAY;IAEZ,IAAI/B,KAAK,EAAE;MACTe,MAAM,CAACC,OAAO,CAAChB,KAAK,CAAC,CAACjC,OAAO,CAAC,CAAC,CAACiE,MAAM,EAAEC,WAAW,CAAC,KAAI;QACtD,IAAIjC,KAAK,CAACgC,MAAM,CAAC,EAAE;UACjB7D,QAAQ,CAACC,eAAe,CAAC8D,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGH,MAAM,EACbhC,KAAK,CAACgC,MAAM,CAAC,CACd;SACF,MAAM,IAAIA,MAAM,IAAIZ,eAAe,IAAIA,eAAe,CAACrC,GAAG,CAAC,EAAE;UAC5D,IAAI,CAAC+C,sBAAsB,CACzBE,MAAM,EACNZ,eAAe,CAACrC,GAAG,CAAC,CAACiD,MAAM,CAAC,EAC5BZ,eAAe,EACf,IAAI,CACL;;MAEL,CAAC,CAAC;;EAEN;EAEAG,UAAUA,CAACH,eAAoB;IAC7B;IACAL,MAAM,CAACC,OAAO,CAACI,eAAe,CAAC,CAACrD,OAAO,CAAC,CAAC,CAACgB,GAAG,EAAEiB,KAAK,CAAC,KAAI;MACvD,IACEA,KAAK,IACLoB,eAAe,CAACrC,GAAG,CAAC,KACnBqC,eAAe,CAACrC,GAAG,CAAC,EAAEJ,MAAM,GAAG,CAAC,IAC/BoC,MAAM,CAACC,OAAO,CAACI,eAAe,CAACrC,GAAG,CAAC,CAAC,EAAEJ,MAAM,IAAI,CAAC,CAAC,EACpD;QACA,IACEyC,eAAe,CAACrC,GAAG,CAAC,IAAI,IAAI,KAC3BqC,eAAe,CAACrC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEJ,MAAM,IAAI,CAAC,IACnCyD,MAAM,CAAChB,eAAe,CAACrC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAClC;UACAZ,QAAQ,CAACC,eAAe,CAAC8D,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGpD,GAAG,EACVqC,eAAe,CAACrC,GAAG,CAAC,CACrB;SACF,MAAM;UACL,IAAI,CAAC+C,sBAAsB,CACzB/C,GAAG,EACHqC,eAAe,CAACrC,GAAG,CAAC,EACpBqC,eAAe,EACf,KAAK,CACN;;OAEJ,MAAM;QACLjD,QAAQ,CAACC,eAAe,CAAC8D,KAAK,CAACC,WAAW,CACxC,IAAI,GAAGpD,GAAG,EACVqC,eAAe,CAACrC,GAAG,CAAC,CACrB;;IAEL,CAAC,CAAC;IACF,IAAI,CAACpE,YAAY,GAAG,IAAI;EAC1B;EAEAiH,OAAOA,CAAA;IACL,IAAI,CAACS,YAAY,EAAE;IACnB,IAAI,CAAC7G,gBAAgB,CAAC8G,YAAY,CAAC,EAAE,CAAC;IACtC,IAAI,CAAChH,aAAa,CAACiH,MAAM,CAAC,WAAW,EAAEnF,MAAM,CAACoF,QAAQ,CAACC,QAAQ,CAAC;IAEhE,IAAI,CAAC3H,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClC9D,YAAY,CAACiG,UAAU,CAAC,cAAc,CAAC;IACvCjG,YAAY,CAACiG,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAACrH,MAAM,CAACsH,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEhC;IAEAC,cAAc,CAACC,KAAK,EAAE;IACtB;IACA,IAAI,CAAC/H,KAAK,CAACyF,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClC9D,YAAY,CAACsD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCtD,YAAY,CAACsD,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzC,IAAI,CAAC/C,WAAW,EAAE;EACpB;EAEAqF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACvH,KAAK,CAACgI,eAAe,CAACnE,MAAM,EAAE;MACrC;MACA,IAAI,CAAC7D,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAACzF,KAAK,CAACyF,GAAG,CAAC,eAAe,EAAE;QAC9BwC,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAAClI,KAAK,CAACyF,GAAG,CAAC,cAAc,EAAE;QAC7B9C,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACL;MACArB,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCtD,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCtD,YAAY,CAACsD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCtD,YAAY,CAACsD,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCtD,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAEsB,IAAI,CAAC4B,SAAS,CAAC,EAAE,CAAC,CAAC;MACxDxG,YAAY,CAACsD,OAAO,CAAC,oBAAoB,EAAEsB,IAAI,CAAC4B,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9DxG,YAAY,CAACsD,OAAO,CAAC,iBAAiB,EAAEsB,IAAI,CAAC4B,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3DxG,YAAY,CAACsD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MACtCtD,YAAY,CAACiG,UAAU,CAAC,cAAc,CAAC;MACvCjG,YAAY,CAACiG,UAAU,CAAC,UAAU,CAAC;;EAEvC;EAEAvI,WAAWA,CAAA;IACT,IAAI,CAAC+I,MAAM,CAACC,aAAa,CAACC,SAAS,GAAG,CAAC;EACzC;EAAC,QAAAC,CAAA,G;qBAteUzI,YAAY,EAAAnB,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAC,IAAA,GAAAnK,EAAA,CAAA6J,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArK,EAAA,CAAA6J,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAvK,EAAA,CAAA6J,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAzK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAQ,KAAA,GAAA1K,EAAA,CAAA6J,iBAAA,CAAAc,EAAA,CAAAC,MAAA,GAAA5K,EAAA,CAAA6J,iBAAA,CAAAgB,EAAA,CAAAC,aAAA,GAAA9K,EAAA,CAAA6J,iBAAA,CAAAkB,EAAA,CAAAC,WAAA,GAAAhL,EAAA,CAAA6J,iBAAA,CAAAoB,GAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZhK,YAAY;IAAAiK,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BACM1L,UAAU;;;;;;;;;;;;QCzBzCG,EAAA,CAAAY,UAAA,IAAA6K,+BAAA,qBAaU;;;QAbkBzL,EAAA,CAAAgB,UAAA,UAAAwK,GAAA,CAAAhJ,oBAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}