{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport let NotFoundComponent = /*#__PURE__*/(() => {\n  class NotFoundComponent {\n    ngOnInit() {\n      /**/\n    }\n    static ɵfac = function NotFoundComponent_Factory(t) {\n      return new (t || NotFoundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotFoundComponent,\n      selectors: [[\"app-not-found\"]],\n      decls: 4,\n      vars: 3,\n      template: function NotFoundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h1\");\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"notFound.pageNotFound\"));\n        }\n      },\n      dependencies: [i1.TranslatePipe]\n    });\n  }\n  return NotFoundComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}