{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ShopService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Tenant/Shop`;\n  }\n  getShopById(shopId) {\n    return this.http.get(`${this.baseUrl}/GetShopById/${shopId}`);\n  }\n  getAllShopSorted() {\n    return this.http.get(`${this.baseUrl}/GetAllShopsSorted`);\n  }\n}\nShopService.ɵfac = function ShopService_Factory(t) {\n  return new (t || ShopService)(i0.ɵɵinject(i1.HttpClient));\n};\nShopService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: ShopService,\n  factory: ShopService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}