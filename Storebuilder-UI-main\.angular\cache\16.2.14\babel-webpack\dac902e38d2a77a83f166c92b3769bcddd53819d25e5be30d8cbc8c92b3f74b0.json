{"ast": null, "code": "import { identity } from './identity';\nexport function pipe(...fns) {\n  return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n  if (fns.length === 0) {\n    return identity;\n  }\n  if (fns.length === 1) {\n    return fns[0];\n  }\n  return function piped(input) {\n    return fns.reduce((prev, fn) => fn(prev), input);\n  };\n}", "map": {"version": 3, "names": ["identity", "pipe", "fns", "pipeFromArray", "length", "piped", "input", "reduce", "prev", "fn"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/rxjs/dist/esm/internal/util/pipe.js"], "sourcesContent": ["import { identity } from './identity';\nexport function pipe(...fns) {\n    return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n    if (fns.length === 0) {\n        return identity;\n    }\n    if (fns.length === 1) {\n        return fns[0];\n    }\n    return function piped(input) {\n        return fns.reduce((prev, fn) => fn(prev), input);\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,IAAIA,CAAC,GAAGC,GAAG,EAAE;EACzB,OAAOC,aAAa,CAACD,GAAG,CAAC;AAC7B;AACA,OAAO,SAASC,aAAaA,CAACD,GAAG,EAAE;EAC/B,IAAIA,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOJ,QAAQ;EACnB;EACA,IAAIE,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE;IAClB,OAAOF,GAAG,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,SAASG,KAAKA,CAACC,KAAK,EAAE;IACzB,OAAOJ,GAAG,CAACK,MAAM,CAAC,CAACC,IAAI,EAAEC,EAAE,KAAKA,EAAE,CAACD,IAAI,CAAC,EAAEF,KAAK,CAAC;EACpD,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}