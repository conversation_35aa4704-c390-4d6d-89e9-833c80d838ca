"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[712],{1712:(st,x,p)=>{p.d(x,{D:()=>at,U:()=>ot});var t=p(5879),g=p(6814),l=p(6223),d=p(3965),c=p(5219),m=p(4480),u=p(9653),f=p(4713);let P=(()=>{class n extends f.s{static \u0275fac=function(){let e;return function(a){return(e||(e=t.n5z(n)))(a||n)}}();static \u0275cmp=t.Xpm({type:n,selectors:[["AngleDoubleLeftIcon"]],standalone:!0,features:[t.qOj,t.jDz],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["fill-rule","evenodd","clip-rule","evenodd","d","M5.71602 11.164C5.80782 11.2021 5.9063 11.2215 6.00569 11.221C6.20216 11.2301 6.39427 11.1612 6.54025 11.0294C6.68191 10.8875 6.76148 10.6953 6.76148 10.4948C6.76148 10.2943 6.68191 10.1021 6.54025 9.96024L3.51441 6.9344L6.54025 3.90855C6.624 3.76126 6.65587 3.59011 6.63076 3.42254C6.60564 3.25498 6.525 3.10069 6.40175 2.98442C6.2785 2.86815 6.11978 2.79662 5.95104 2.7813C5.78229 2.76598 5.61329 2.80776 5.47112 2.89994L1.97123 6.39983C1.82957 6.54167 1.75 6.73393 1.75 6.9344C1.75 7.13486 1.82957 7.32712 1.97123 7.46896L5.47112 10.9991C5.54096 11.0698 5.62422 11.1259 5.71602 11.164ZM11.0488 10.9689C11.1775 11.1156 11.3585 11.2061 11.5531 11.221C11.7477 11.2061 11.9288 11.1156 12.0574 10.9689C12.1815 10.8302 12.25 10.6506 12.25 10.4645C12.25 10.2785 12.1815 10.0989 12.0574 9.96024L9.03158 6.93439L12.0574 3.90855C12.1248 3.76739 12.1468 3.60881 12.1204 3.45463C12.0939 3.30045 12.0203 3.15826 11.9097 3.04765C11.7991 2.93703 11.6569 2.86343 11.5027 2.83698C11.3486 2.81053 11.19 2.83252 11.0488 2.89994L7.51865 6.36957C7.37699 6.51141 7.29742 6.70367 7.29742 6.90414C7.29742 7.1046 7.37699 7.29686 7.51865 7.4387L11.0488 10.9689Z","fill","currentColor"]],template:function(o,a){1&o&&(t.O4$(),t.TgZ(0,"svg",0),t._UZ(1,"path",1),t.qZA()),2&o&&(t.Tol(a.getClassNames()),t.uIk("aria-label",a.ariaLabel)("aria-hidden",a.ariaHidden)("role",a.role))},encapsulation:2})}return n})(),C=(()=>{class n extends f.s{static \u0275fac=function(){let e;return function(a){return(e||(e=t.n5z(n)))(a||n)}}();static \u0275cmp=t.Xpm({type:n,selectors:[["AngleDoubleRightIcon"]],standalone:!0,features:[t.qOj,t.jDz],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["fill-rule","evenodd","clip-rule","evenodd","d","M7.68757 11.1451C7.7791 11.1831 7.8773 11.2024 7.9764 11.2019C8.07769 11.1985 8.17721 11.1745 8.26886 11.1312C8.36052 11.088 8.44238 11.0265 8.50943 10.9505L12.0294 7.49085C12.1707 7.34942 12.25 7.15771 12.25 6.95782C12.25 6.75794 12.1707 6.56622 12.0294 6.42479L8.50943 2.90479C8.37014 2.82159 8.20774 2.78551 8.04633 2.80192C7.88491 2.81833 7.73309 2.88635 7.6134 2.99588C7.4937 3.10541 7.41252 3.25061 7.38189 3.40994C7.35126 3.56927 7.37282 3.73423 7.44337 3.88033L10.4605 6.89748L7.44337 9.91463C7.30212 10.0561 7.22278 10.2478 7.22278 10.4477C7.22278 10.6475 7.30212 10.8393 7.44337 10.9807C7.51301 11.0512 7.59603 11.1071 7.68757 11.1451ZM1.94207 10.9505C2.07037 11.0968 2.25089 11.1871 2.44493 11.2019C2.63898 11.1871 2.81949 11.0968 2.94779 10.9505L6.46779 7.49085C6.60905 7.34942 6.68839 7.15771 6.68839 6.95782C6.68839 6.75793 6.60905 6.56622 6.46779 6.42479L2.94779 2.90479C2.80704 2.83757 2.6489 2.81563 2.49517 2.84201C2.34143 2.86839 2.19965 2.94178 2.08936 3.05207C1.97906 3.16237 1.90567 3.30415 1.8793 3.45788C1.85292 3.61162 1.87485 3.76975 1.94207 3.9105L4.95922 6.92765L1.94207 9.9448C1.81838 10.0831 1.75 10.2621 1.75 10.4477C1.75 10.6332 1.81838 10.8122 1.94207 10.9505Z","fill","currentColor"]],template:function(o,a){1&o&&(t.O4$(),t.TgZ(0,"svg",0),t._UZ(1,"path",1),t.qZA()),2&o&&(t.Tol(a.getClassNames()),t.uIk("aria-label",a.ariaLabel)("aria-hidden",a.ariaHidden)("role",a.role))},encapsulation:2})}return n})(),w=(()=>{class n extends f.s{static \u0275fac=function(){let e;return function(a){return(e||(e=t.n5z(n)))(a||n)}}();static \u0275cmp=t.Xpm({type:n,selectors:[["AngleLeftIcon"]],standalone:!0,features:[t.qOj,t.jDz],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M8.75 11.185C8.65146 11.1854 8.55381 11.1662 8.4628 11.1284C8.37179 11.0906 8.28924 11.0351 8.22 10.965L4.72 7.46496C4.57955 7.32433 4.50066 7.13371 4.50066 6.93496C4.50066 6.73621 4.57955 6.54558 4.72 6.40496L8.22 2.93496C8.36095 2.84357 8.52851 2.80215 8.69582 2.81733C8.86312 2.83252 9.02048 2.90344 9.14268 3.01872C9.26487 3.134 9.34483 3.28696 9.36973 3.4531C9.39463 3.61924 9.36303 3.78892 9.28 3.93496L6.28 6.93496L9.28 9.93496C9.42045 10.0756 9.49934 10.2662 9.49934 10.465C9.49934 10.6637 9.42045 10.8543 9.28 10.995C9.13526 11.1257 8.9448 11.1939 8.75 11.185Z","fill","currentColor"]],template:function(o,a){1&o&&(t.O4$(),t.TgZ(0,"svg",0),t._UZ(1,"path",1),t.qZA()),2&o&&(t.Tol(a.getClassNames()),t.uIk("aria-label",a.ariaLabel)("aria-hidden",a.ariaHidden)("role",a.role))},encapsulation:2})}return n})();var T=p(1239);function v(n,i){1&n&&t.GkF(0)}const h=function(n){return{$implicit:n}};function I(n,i){if(1&n&&(t.TgZ(0,"div",15),t.YNc(1,v,1,0,"ng-container",16),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngTemplateOutlet",e.templateLeft)("ngTemplateOutletContext",t.VKq(2,h,e.paginatorState))}}function L(n,i){if(1&n&&(t.TgZ(0,"span",17),t._uU(1),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Oqu(e.currentPageReport)}}function k(n,i){1&n&&t._UZ(0,"AngleDoubleLeftIcon",19),2&n&&t.Q6J("styleClass","p-paginator-icon")}function b(n,i){}function y(n,i){1&n&&t.YNc(0,b,0,0,"ng-template")}function J(n,i){if(1&n&&(t.TgZ(0,"span",20),t.YNc(1,y,1,0,null,21),t.qZA()),2&n){const e=t.oxw(3);t.xp6(1),t.Q6J("ngTemplateOutlet",e.firstPageLinkIconTemplate)}}const _=function(n){return{"p-disabled":n}};function R(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"button",18),t.NdJ("click",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.changePageToFirst(a))}),t.YNc(1,k,1,1,"AngleDoubleLeftIcon",6),t.YNc(2,J,2,1,"span",7),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("disabled",e.isFirstPage()||e.empty())("ngClass",t.VKq(4,_,e.isFirstPage()||e.empty())),t.xp6(1),t.Q6J("ngIf",!e.firstPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.firstPageLinkIconTemplate)}}function Z(n,i){1&n&&t._UZ(0,"AngleLeftIcon",19),2&n&&t.Q6J("styleClass","p-paginator-icon")}function A(n,i){}function M(n,i){1&n&&t.YNc(0,A,0,0,"ng-template")}function N(n,i){if(1&n&&(t.TgZ(0,"span",20),t.YNc(1,M,1,0,null,21),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngTemplateOutlet",e.previousPageLinkIconTemplate)}}const Q=function(n){return{"p-highlight":n}};function D(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"button",24),t.NdJ("click",function(a){const r=t.CHM(e).$implicit,it=t.oxw(3);return t.KtG(it.onPageLinkClick(a,r-1))}),t._uU(1),t.qZA()}if(2&n){const e=i.$implicit,o=t.oxw(3);t.Q6J("ngClass",t.VKq(2,Q,e-1==o.getPage())),t.xp6(1),t.hij(" ",e," ")}}function O(n,i){if(1&n&&(t.TgZ(0,"span",22),t.YNc(1,D,2,4,"button",23),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngForOf",e.pageLinks)}}function S(n,i){if(1&n&&t._uU(0),2&n){const e=t.oxw(3);t.Oqu(e.currentPageReport)}}function F(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"p-dropdown",25),t.NdJ("onChange",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.onPageDropdownChange(a))}),t.YNc(1,S,1,1,"ng-template",26),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("options",e.pageItems)("ngModel",e.getPage())("disabled",e.empty())("appendTo",e.dropdownAppendTo)("scrollHeight",e.dropdownScrollHeight)}}function Y(n,i){1&n&&t._UZ(0,"AngleRightIcon",19),2&n&&t.Q6J("styleClass","p-paginator-icon")}function H(n,i){}function q(n,i){1&n&&t.YNc(0,H,0,0,"ng-template")}function K(n,i){if(1&n&&(t.TgZ(0,"span",20),t.YNc(1,q,1,0,null,21),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngTemplateOutlet",e.nextPageLinkIconTemplate)}}function j(n,i){1&n&&t._UZ(0,"AngleDoubleRightIcon",19),2&n&&t.Q6J("styleClass","p-paginator-icon")}function z(n,i){}function G(n,i){1&n&&t.YNc(0,z,0,0,"ng-template")}function B(n,i){if(1&n&&(t.TgZ(0,"span",20),t.YNc(1,G,1,0,null,21),t.qZA()),2&n){const e=t.oxw(3);t.xp6(1),t.Q6J("ngTemplateOutlet",e.lastPageLinkIconTemplate)}}function U(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"button",27),t.NdJ("click",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.changePageToLast(a))}),t.YNc(1,j,1,1,"AngleDoubleRightIcon",6),t.YNc(2,B,2,1,"span",7),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("disabled",e.isLastPage()||e.empty())("ngClass",t.VKq(4,_,e.isLastPage()||e.empty())),t.xp6(1),t.Q6J("ngIf",!e.lastPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.lastPageLinkIconTemplate)}}function V(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"p-inputNumber",28),t.NdJ("ngModelChange",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.changePage(a-1))}),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("ngModel",e.currentPage())("disabled",e.empty())}}function $(n,i){1&n&&t.GkF(0)}function E(n,i){if(1&n&&t.YNc(0,$,1,0,"ng-container",16),2&n){const e=i.$implicit,o=t.oxw(4);t.Q6J("ngTemplateOutlet",o.dropdownItemTemplate)("ngTemplateOutletContext",t.VKq(2,h,e))}}function X(n,i){1&n&&(t.ynx(0),t.YNc(1,E,1,4,"ng-template",31),t.BQk())}function W(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"p-dropdown",29),t.NdJ("ngModelChange",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.rows=a)})("onChange",function(a){t.CHM(e);const s=t.oxw(2);return t.KtG(s.onRppChange(a))}),t.YNc(1,X,2,0,"ng-container",30),t.qZA()}if(2&n){const e=t.oxw(2);t.Q6J("options",e.rowsPerPageItems)("ngModel",e.rows)("disabled",e.empty())("appendTo",e.dropdownAppendTo)("scrollHeight",e.dropdownScrollHeight),t.xp6(1),t.Q6J("ngIf",e.dropdownItemTemplate)}}function tt(n,i){1&n&&t.GkF(0)}function et(n,i){if(1&n&&(t.TgZ(0,"div",32),t.YNc(1,tt,1,0,"ng-container",16),t.qZA()),2&n){const e=t.oxw(2);t.xp6(1),t.Q6J("ngTemplateOutlet",e.templateRight)("ngTemplateOutletContext",t.VKq(2,h,e.paginatorState))}}function nt(n,i){if(1&n){const e=t.EpF();t.TgZ(0,"div",1),t.YNc(1,I,2,4,"div",2),t.YNc(2,L,2,1,"span",3),t.YNc(3,R,3,6,"button",4),t.TgZ(4,"button",5),t.NdJ("click",function(a){t.CHM(e);const s=t.oxw();return t.KtG(s.changePageToPrev(a))}),t.YNc(5,Z,1,1,"AngleLeftIcon",6),t.YNc(6,N,2,1,"span",7),t.qZA(),t.YNc(7,O,2,1,"span",8),t.YNc(8,F,2,5,"p-dropdown",9),t.TgZ(9,"button",10),t.NdJ("click",function(a){t.CHM(e);const s=t.oxw();return t.KtG(s.changePageToNext(a))}),t.YNc(10,Y,1,1,"AngleRightIcon",6),t.YNc(11,K,2,1,"span",7),t.qZA(),t.YNc(12,U,3,6,"button",11),t.YNc(13,V,1,2,"p-inputNumber",12),t.YNc(14,W,2,6,"p-dropdown",13),t.YNc(15,et,2,4,"div",14),t.qZA()}if(2&n){const e=t.oxw();t.Tol(e.styleClass),t.Q6J("ngStyle",e.style)("ngClass","p-paginator p-component"),t.xp6(1),t.Q6J("ngIf",e.templateLeft),t.xp6(1),t.Q6J("ngIf",e.showCurrentPageReport),t.xp6(1),t.Q6J("ngIf",e.showFirstLastIcon),t.xp6(1),t.Q6J("disabled",e.isFirstPage()||e.empty())("ngClass",t.VKq(21,_,e.isFirstPage()||e.empty())),t.xp6(1),t.Q6J("ngIf",!e.previousPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.previousPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.showPageLinks),t.xp6(1),t.Q6J("ngIf",e.showJumpToPageDropdown),t.xp6(1),t.Q6J("disabled",e.isLastPage()||e.empty())("ngClass",t.VKq(23,_,e.isLastPage()||e.empty())),t.xp6(1),t.Q6J("ngIf",!e.nextPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.nextPageLinkIconTemplate),t.xp6(1),t.Q6J("ngIf",e.showFirstLastIcon),t.xp6(1),t.Q6J("ngIf",e.showJumpToPageInput),t.xp6(1),t.Q6J("ngIf",e.rowsPerPageOptions),t.xp6(1),t.Q6J("ngIf",e.templateRight)}}let at=(()=>{class n{cd;pageLinkSize=5;style;styleClass;alwaysShow=!0;dropdownAppendTo;templateLeft;templateRight;appendTo;dropdownScrollHeight="200px";currentPageReportTemplate="{currentPage} of {totalPages}";showCurrentPageReport;showFirstLastIcon=!0;totalRecords=0;rows=0;rowsPerPageOptions;showJumpToPageDropdown;showJumpToPageInput;showPageLinks=!0;dropdownItemTemplate;get first(){return this._first}set first(e){this._first=e}onPageChange=new t.vpe;templates;firstPageLinkIconTemplate;previousPageLinkIconTemplate;lastPageLinkIconTemplate;nextPageLinkIconTemplate;pageLinks;pageItems;rowsPerPageItems;paginatorState;_first=0;_page=0;constructor(e){this.cd=e}ngOnInit(){this.updatePaginatorState()}ngAfterContentInit(){this.templates.forEach(e=>{switch(e.getType()){case"firstpagelinkicon":this.firstPageLinkIconTemplate=e.template;break;case"previouspagelinkicon":this.previousPageLinkIconTemplate=e.template;break;case"lastpagelinkicon":this.lastPageLinkIconTemplate=e.template;break;case"nextpagelinkicon":this.nextPageLinkIconTemplate=e.template}})}ngOnChanges(e){e.totalRecords&&(this.updatePageLinks(),this.updatePaginatorState(),this.updateFirst(),this.updateRowsPerPageOptions()),e.first&&(this._first=e.first.currentValue,this.updatePageLinks(),this.updatePaginatorState()),e.rows&&(this.updatePageLinks(),this.updatePaginatorState()),e.rowsPerPageOptions&&this.updateRowsPerPageOptions()}updateRowsPerPageOptions(){if(this.rowsPerPageOptions){this.rowsPerPageItems=[];for(let e of this.rowsPerPageOptions)"object"==typeof e&&e.showAll?this.rowsPerPageItems.unshift({label:e.showAll,value:this.totalRecords}):this.rowsPerPageItems.push({label:String(e),value:e})}}isFirstPage(){return 0===this.getPage()}isLastPage(){return this.getPage()===this.getPageCount()-1}getPageCount(){return Math.ceil(this.totalRecords/this.rows)}calculatePageLinkBoundaries(){let e=this.getPageCount(),o=Math.min(this.pageLinkSize,e),a=Math.max(0,Math.ceil(this.getPage()-o/2)),s=Math.min(e-1,a+o-1);return a=Math.max(0,a-(this.pageLinkSize-(s-a+1))),[a,s]}updatePageLinks(){this.pageLinks=[];let e=this.calculatePageLinkBoundaries(),a=e[1];for(let s=e[0];s<=a;s++)this.pageLinks.push(s+1);if(this.showJumpToPageDropdown){this.pageItems=[];for(let s=0;s<this.getPageCount();s++)this.pageItems.push({label:String(s+1),value:s})}}changePage(e){var o=this.getPageCount();if(e>=0&&e<o){this._first=this.rows*e;var a={page:e,first:this.first,rows:this.rows,pageCount:o};this.updatePageLinks(),this.onPageChange.emit(a),this.updatePaginatorState()}}updateFirst(){const e=this.getPage();e>0&&this.totalRecords&&this.first>=this.totalRecords&&Promise.resolve(null).then(()=>this.changePage(e-1))}getPage(){return Math.floor(this.first/this.rows)}changePageToFirst(e){this.isFirstPage()||this.changePage(0),e.preventDefault()}changePageToPrev(e){this.changePage(this.getPage()-1),e.preventDefault()}changePageToNext(e){this.changePage(this.getPage()+1),e.preventDefault()}changePageToLast(e){this.isLastPage()||this.changePage(this.getPageCount()-1),e.preventDefault()}onPageLinkClick(e,o){this.changePage(o),e.preventDefault()}onRppChange(e){this.changePage(this.getPage())}onPageDropdownChange(e){this.changePage(e.value)}updatePaginatorState(){this.paginatorState={page:this.getPage(),pageCount:this.getPageCount(),rows:this.rows,first:this.first,totalRecords:this.totalRecords}}empty(){return 0===this.getPageCount()}currentPage(){return this.getPageCount()>0?this.getPage()+1:0}get currentPageReport(){return this.currentPageReportTemplate.replace("{currentPage}",String(this.currentPage())).replace("{totalPages}",String(this.getPageCount())).replace("{first}",String(this.totalRecords>0?this._first+1:0)).replace("{last}",String(Math.min(this._first+this.rows,this.totalRecords))).replace("{rows}",String(this.rows)).replace("{totalRecords}",String(this.totalRecords))}static \u0275fac=function(o){return new(o||n)(t.Y36(t.sBO))};static \u0275cmp=t.Xpm({type:n,selectors:[["p-paginator"]],contentQueries:function(o,a,s){if(1&o&&t.Suo(s,c.jx,4),2&o){let r;t.iGM(r=t.CRH())&&(a.templates=r)}},hostAttrs:[1,"p-element"],inputs:{pageLinkSize:"pageLinkSize",style:"style",styleClass:"styleClass",alwaysShow:"alwaysShow",dropdownAppendTo:"dropdownAppendTo",templateLeft:"templateLeft",templateRight:"templateRight",appendTo:"appendTo",dropdownScrollHeight:"dropdownScrollHeight",currentPageReportTemplate:"currentPageReportTemplate",showCurrentPageReport:"showCurrentPageReport",showFirstLastIcon:"showFirstLastIcon",totalRecords:"totalRecords",rows:"rows",rowsPerPageOptions:"rowsPerPageOptions",showJumpToPageDropdown:"showJumpToPageDropdown",showJumpToPageInput:"showJumpToPageInput",showPageLinks:"showPageLinks",dropdownItemTemplate:"dropdownItemTemplate",first:"first"},outputs:{onPageChange:"onPageChange"},features:[t.TTD],decls:1,vars:1,consts:[[3,"class","ngStyle","ngClass",4,"ngIf"],[3,"ngStyle","ngClass"],["class","p-paginator-left-content",4,"ngIf"],["class","p-paginator-current",4,"ngIf"],["type","button","pRipple","","class","p-paginator-first p-paginator-element p-link",3,"disabled","ngClass","click",4,"ngIf"],["type","button","pRipple","",1,"p-paginator-prev","p-paginator-element","p-link",3,"disabled","ngClass","click"],[3,"styleClass",4,"ngIf"],["class","p-paginator-icon",4,"ngIf"],["class","p-paginator-pages",4,"ngIf"],["styleClass","p-paginator-page-options",3,"options","ngModel","disabled","appendTo","scrollHeight","onChange",4,"ngIf"],["type","button","pRipple","",1,"p-paginator-next","p-paginator-element","p-link",3,"disabled","ngClass","click"],["type","button","pRipple","","class","p-paginator-last p-paginator-element p-link",3,"disabled","ngClass","click",4,"ngIf"],["class","p-paginator-page-input",3,"ngModel","disabled","ngModelChange",4,"ngIf"],["styleClass","p-paginator-rpp-options",3,"options","ngModel","disabled","appendTo","scrollHeight","ngModelChange","onChange",4,"ngIf"],["class","p-paginator-right-content",4,"ngIf"],[1,"p-paginator-left-content"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"p-paginator-current"],["type","button","pRipple","",1,"p-paginator-first","p-paginator-element","p-link",3,"disabled","ngClass","click"],[3,"styleClass"],[1,"p-paginator-icon"],[4,"ngTemplateOutlet"],[1,"p-paginator-pages"],["type","button","class","p-paginator-page p-paginator-element p-link","pRipple","",3,"ngClass","click",4,"ngFor","ngForOf"],["type","button","pRipple","",1,"p-paginator-page","p-paginator-element","p-link",3,"ngClass","click"],["styleClass","p-paginator-page-options",3,"options","ngModel","disabled","appendTo","scrollHeight","onChange"],["pTemplate","selectedItem"],["type","button","pRipple","",1,"p-paginator-last","p-paginator-element","p-link",3,"disabled","ngClass","click"],[1,"p-paginator-page-input",3,"ngModel","disabled","ngModelChange"],["styleClass","p-paginator-rpp-options",3,"options","ngModel","disabled","appendTo","scrollHeight","ngModelChange","onChange"],[4,"ngIf"],["pTemplate","item"],[1,"p-paginator-right-content"]],template:function(o,a){1&o&&t.YNc(0,nt,16,25,"div",0),2&o&&t.Q6J("ngIf",!!a.alwaysShow||a.pageLinks&&a.pageLinks.length>1)},dependencies:function(){return[g.mk,g.sg,g.O5,g.tP,g.PC,d.Lt,c.jx,u.Rn,l.JJ,l.On,m.H,P,C,w,T.o]},styles:[".p-paginator{display:flex;align-items:center;justify-content:center;flex-wrap:wrap}.p-paginator-left-content{margin-right:auto}.p-paginator-right-content{margin-left:auto}.p-paginator-page,.p-paginator-next,.p-paginator-last,.p-paginator-first,.p-paginator-prev,.p-paginator-current{cursor:pointer;display:inline-flex;align-items:center;justify-content:center;line-height:1;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-paginator-element:focus{z-index:1;position:relative}\n"],encapsulation:2,changeDetection:0})}return n})(),ot=(()=>{class n{static \u0275fac=function(o){return new(o||n)};static \u0275mod=t.oAB({type:n});static \u0275inj=t.cJS({imports:[g.ez,d.kW,u.L$,l.u5,c.m8,m.T,P,C,w,T.o,d.kW,u.L$,l.u5,c.m8]})}return n})()}}]);