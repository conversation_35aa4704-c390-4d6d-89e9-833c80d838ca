{"ast": null, "code": "import { CommonModule, NgOptimizedImage } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { CascadeSelectModule } from \"primeng/cascadeselect\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { PasswordModule } from 'primeng/password';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { RatingModule } from \"primeng/rating\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { FileUploadModule } from \"primeng/fileupload\";\nimport { StepsModule } from 'primeng/steps';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InitialModule } from \"../../../shared/modules/initial.module\";\nimport { InputNumberModule } from \"primeng/inputnumber\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class OrderDetailsModule {\n  static ɵfac = function OrderDetailsModule_Factory(t) {\n    return new (t || OrderDetailsModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: OrderDetailsModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, DialogModule, ButtonModule, DropdownModule, PasswordModule, CascadeSelectModule, RouterModule.forChild(routes), TranslateModule, FormsModule, ReactiveFormsModule, RatingModule, BreadcrumbModule, FileUploadModule, StepsModule, RadioButtonModule, InitialModule, InputNumberModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrderDetailsModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, DialogModule, ButtonModule, DropdownModule, PasswordModule, CascadeSelectModule, i1.RouterModule, TranslateModule, FormsModule, ReactiveFormsModule, RatingModule, BreadcrumbModule, NgOptimizedImage, FileUploadModule, StepsModule, RadioButtonModule, InitialModule, InputNumberModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}