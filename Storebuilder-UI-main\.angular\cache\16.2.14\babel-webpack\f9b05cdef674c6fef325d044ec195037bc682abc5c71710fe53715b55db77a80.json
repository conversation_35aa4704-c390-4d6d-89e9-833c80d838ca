{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"primeng/dialog\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction FlashSaleModalComponent_ng_template_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 8)(8, \"div\", 9);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 10);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 10);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.hrs < 10 ? \"0\" : \"\", \"\", ctx_r2.hrs, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 9, \"flashSaleModal.hours\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.minutes < 10 ? \"0\" : \"\", \"\", ctx_r2.minutes, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 11, \"flashSaleModal.min\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.seconds < 10 ? \"0\" : \"\", \"\", ctx_r2.seconds, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 13, \"flashSaleModal.sec\"), \" \");\n  }\n}\nfunction FlashSaleModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"img\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FlashSaleModalComponent_ng_template_1_div_3_Template, 19, 15, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImagesURl(ctx_r0.data.imageUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.data.isFlashSaleTimer);\n  }\n}\nfunction FlashSaleModalComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function FlashSaleModalComponent_ng_template_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onVisit());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function FlashSaleModalComponent_ng_template_2_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onCancel());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"flashSaleModal.visit\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 4, \"flashSaleModal.close\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"768px\": \"90vw\"\n  };\n};\nexport class FlashSaleModalComponent {\n  constructor(cd) {\n    this.cd = cd;\n    this.displayModal = false;\n    this.submit = new EventEmitter();\n    this.cancel = new EventEmitter();\n  }\n  ngOnInit() {\n    this.hrs = Number(this.data?.time?.split(':')[0]);\n    this.minutes = Number(this.data?.time?.split(':')[1]);\n    this.seconds = Number(this.data?.time?.split(':')[2]);\n    this.startTimer();\n  }\n  onCancel() {\n    localStorage.setItem('visited', 'true');\n    clearInterval(this.interval);\n    this.cancel.emit(true);\n  }\n  onVisit() {\n    localStorage.setItem('visited', 'true');\n    clearInterval(this.interval);\n    this.submit.emit(true);\n  }\n  startTimer() {\n    this.interval = setInterval(() => {\n      if (this.seconds > 0) {\n        this.seconds -= 1;\n      } else {\n        this.seconds = 59;\n        if (this.minutes > 0) {\n          this.minutes -= 1;\n        } else {\n          this.minutes = 59;\n          if (this.hrs > 0) {\n            this.hrs -= 1;\n          } else {\n            this.hrs -= 0;\n            this.minutes = 0;\n            this.seconds = 0;\n            clearInterval(this.interval);\n          }\n        }\n      }\n      this.cd.detectChanges();\n    }, 1000);\n  }\n  getImagesURl(data) {\n    return UtilityFunctions.verifyImageURL(data, environment.apiEndPoint);\n  }\n}\nFlashSaleModalComponent.ɵfac = function FlashSaleModalComponent_Factory(t) {\n  return new (t || FlashSaleModalComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nFlashSaleModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: FlashSaleModalComponent,\n  selectors: [[\"app-flash-sale-modal\"]],\n  inputs: {\n    displayModal: \"displayModal\",\n    data: \"data\"\n  },\n  outputs: {\n    submit: \"submit\",\n    cancel: \"cancel\"\n  },\n  decls: 3,\n  vars: 7,\n  consts: [[1, \"flash-sale\", 3, \"visible\", \"breakpoints\", \"showHeader\", \"resizable\", \"closable\", \"modal\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"flash-sale__content-section\"], [1, \"flash-sale__content-section__images\"], [\"alt\", \"No Image\", 3, \"src\"], [\"class\", \"d-flex flash-sale__content-section__timer-section\", 4, \"ngIf\"], [1, \"d-flex\", \"flash-sale__content-section__timer-section\"], [1, \"flash-sale__content-section__timer-section__timer\"], [1, \"flash-sale__content-section__timer-section__timer__time\"], [1, \"flash-sale__content-section__timer-section__timer__label\"], [1, \"flash-sale__button-section\"], [1, \"flash-sale__button-section__visit-button\", 3, \"click\"], [1, \"flash-sale__button-section__close-button\", 3, \"click\"]],\n  template: function FlashSaleModalComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p-dialog\", 0);\n      i0.ɵɵlistener(\"visibleChange\", function FlashSaleModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n        return ctx.displayModal = $event;\n      })(\"onHide\", function FlashSaleModalComponent_Template_p_dialog_onHide_0_listener() {\n        return ctx.onCancel();\n      });\n      i0.ɵɵtemplate(1, FlashSaleModalComponent_ng_template_1_Template, 4, 2, \"ng-template\", 1);\n      i0.ɵɵtemplate(2, FlashSaleModalComponent_ng_template_2_Template, 7, 6, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(6, _c0))(\"showHeader\", false)(\"resizable\", false)(\"closable\", false)(\"modal\", true);\n    }\n  },\n  dependencies: [i1.PrimeTemplate, i2.Dialog, i3.NgIf, i4.TranslatePipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.flash-sale[_ngcontent-%COMP%]     .p-dialog .p-dialog-content {\\n  background: #ffffff;\\n  color: #495057;\\n  padding: 32px 16px 16px;\\n  border-top-right-radius: 8px;\\n  border-top-left-radius: 8px;\\n}\\n.flash-sale[_ngcontent-%COMP%]     .p-dialog .p-dialog-footer {\\n  padding: 16px 16px;\\n  border-bottom-right-radius: 8px;\\n  border-bottom-left-radius: 8px;\\n}\\n.flash-sale[_ngcontent-%COMP%]     .p-dialog {\\n  max-width: 500px;\\n  width: 500px;\\n  background: white;\\n}\\n@media only screen and (max-width: 767px) {\\n  .flash-sale[_ngcontent-%COMP%]     .p-dialog {\\n    max-width: 360px;\\n    width: 360px;\\n  }\\n}\\n.flash-sale__content-section[_ngcontent-%COMP%] {\\n  padding: 0 32px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n@media only screen and (max-width: 767px) {\\n  .flash-sale__content-section[_ngcontent-%COMP%] {\\n    padding: 0px 12px;\\n  }\\n}\\n.flash-sale__content-section__images[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 8px 0px;\\n}\\n.flash-sale__content-section__images[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 100%;\\n  height: 210px;\\n}\\n.flash-sale__content-section__title[_ngcontent-%COMP%] {\\n  color: #2D2D2D;\\n  margin: 8px 0px;\\n  text-align: center;\\n  font-family: var(--regular-font);\\n  font-size: 24px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n}\\n.flash-sale__content-section__timer-section[_ngcontent-%COMP%] {\\n  justify-content: space-around !important;\\n}\\n.flash-sale__content-section__timer-section__timer__time[_ngcontent-%COMP%] {\\n  color: var(--custom-primary, var(--colors-Main-Color, #204E6E));\\n  text-align: center;\\n  \\n\\n  font-family: var(--medium-font);\\n  font-size: 64px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 125%; \\n\\n}\\n.flash-sale__content-section__timer-section__timer__label[_ngcontent-%COMP%] {\\n  color: var(--custom-primary, var(--colors-Main-Color, #204E6E));\\n  text-align: center;\\n  \\n\\n  font-family: var(--regular-font);\\n  font-size: 22px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 150%; \\n\\n}\\n.flash-sale__button-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 16px;\\n  align-self: stretch;\\n}\\n.flash-sale__button-section__visit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 60px;\\n  padding: 13px 24px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  align-self: stretch;\\n  border-radius: 6px;\\n  background: var(--colors-Main-Color, #204E6E);\\n  color: var(--Gray-00, var(--colors-fff, #FFF));\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n}\\n.flash-sale__button-section__close-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 60px;\\n  padding: 13px 24px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  align-self: stretch;\\n  border-radius: 6px;\\n  border: 2px solid #004D9C;\\n  color: var(--colors-Main-Color, #204E6E);\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%; \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}