{"ast": null, "code": "import { ElementRef, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../side-menu/side-menu.component\";\nconst _c0 = [\"widgetsContent\"];\nfunction NavbarComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-mtn-side-menu\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"em\", 17);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_8_Template_em_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.scrollLeft());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"extra-pad\": a0\n  };\n};\nfunction NavbarComponent_li_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_li_12_span_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const feature_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.redirectFeaturedProducts(feature_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c1, ctx_r11.isShowSlider));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(feature_r10.categoryName);\n  }\n}\nfunction NavbarComponent_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵtemplate(1, NavbarComponent_li_12_span_1_Template, 2, 4, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !feature_r10.shouldNotDisplay);\n  }\n}\nfunction NavbarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"em\", 22);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_div_13_Template_em_click_1_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.scrollRight());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"a\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelement(4, \"span\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"landingNavbar.liveMerchants\"));\n  }\n}\nfunction NavbarComponent_a_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_a_18_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.navigateToSellerHub());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27);\n    i0.ɵɵelement(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"landingNavbar.sellOnYallaMall\"), \" \");\n  }\n}\nfunction NavbarComponent_a_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_a_19_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateToSellerHub());\n    });\n    i0.ɵɵelementStart(1, \"div\", 30);\n    i0.ɵɵelement(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"landingNavbar.sellOnMarketplace\"), \" \");\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"isShowlivestream\": a0\n  };\n};\nexport class NavbarComponent {\n  constructor(store, translate, mainDataService, messageService, appDataService, router, permissionService, cdr, $gaService, $gtmService, platformId) {\n    this.store = store;\n    this.translate = translate;\n    this.mainDataService = mainDataService;\n    this.messageService = messageService;\n    this.appDataService = appDataService;\n    this.router = router;\n    this.permissionService = permissionService;\n    this.cdr = cdr;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.platformId = platformId;\n    this.categories = [];\n    this.isShowSlider = environment.isStoreCloud;\n    this.leftArrow = true;\n    this.rightArrow = true;\n    this.isShop = false;\n    this.link = 'link';\n    this.isStoreCloud = environment.isStoreCloud;\n    this.items = [{\n      title: 'Orangies',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Apple',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Mango',\n      link: 'https://www.github.com/isahohieku'\n    }, {\n      title: 'Carrot',\n      link: 'https://www.github.com/isahohieku'\n    }];\n    this.allFeatures = [];\n    this.topNumber = 1000;\n    this.isShowLiveStream = false;\n    this.isGoogleAnalytics = false;\n    this.ShowCategory = false;\n  }\n  ngOnInit() {\n    this.isShowLiveStream = this.permissionService.hasPermission('Live-Stream');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.getMainData();\n    this.getAllFeatures();\n  }\n  scrollLeft() {\n    this.widgetsContent.nativeElement.scrollLeft -= 200;\n    this.widgetsContent.nativeElement.scrollTo({\n      right: this.widgetsContent.nativeElement.scrollLeft,\n      behavior: 'smooth'\n    });\n    if (this.widgetsContent.nativeElement.scrollLeft == 0 || this.widgetsContent.nativeElement.scrollLeft < 50) {\n      this.leftArrow = true;\n    } else {\n      this.leftArrow = false;\n    }\n    this.rightArrow = true;\n  }\n  scrollRight() {\n    this.widgetsContent.nativeElement.scrollLeft += 200;\n    this.widgetsContent.nativeElement.scrollTo({\n      left: this.widgetsContent.nativeElement.scrollLeft,\n      behavior: 'smooth'\n    });\n    this.leftArrow = false;\n    if (this.widgetsContent.nativeElement.scrollLeft > 0) {\n      this.leftArrow = false;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          debugger;\n          this.categories = res.slice(0, 5);\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }, 1);\n  }\n  getMainData() {\n    const initialData = this.appDataService.initialData;\n    this.isShop = initialData.isShop;\n    this.store.set(\"isShop\", this.isShop);\n  }\n  getAllFeatures() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.flashSale = this.appDataService.layoutTemplate.find(section => section.description === 'Flash Sale');\n    if (this.flashSale) {\n      this.flashSaleData = JSON.parse(this.flashSale.data);\n    }\n    let features = JSON.parse(this.navbarData.data);\n    if (this.flashSale?.isActive && this.flashSale?.promotionId && this.flashSaleData.isMainMenu) {\n      let data = {\n        id: this.flashSale.promotionId,\n        categoryName: this.flashSaleData.promotionName,\n        type: this.flashSale.type,\n        isActive: this.flashSale.isActive\n      };\n      features.push(data);\n    }\n    if (this.navbarData.isActive) {\n      this.allFeatures = features;\n    }\n  }\n  redirectFeaturedProducts(feature) {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_QUICK_LINKS, '', 'QUICK_LINKS', 1, true, {\n        categorySelected: feature.categoryName\n      });\n    }\n    if (feature.type && feature.type === 'promotion') {\n      this.router.navigate([`/promotion/${feature.categoryName}`]);\n      this.$gtmService.pushPageView('promotion', feature.categoryName);\n    } else {\n      if (feature.categoryIds) {\n        this.router.navigate([`/category/${feature.id}`]);\n        this.$gtmService.pushPageView('category', feature.categoryName);\n      } else {\n        this.router.navigate([`/category/${feature.id}&${this.topNumber}&${feature.categoryName}`]);\n      }\n    }\n  }\n  translated(name) {\n    let transData;\n    if (!name) return \"\";\n    name = name?.replace(/\\s/g, '');\n    this.translate.get('featureType').subscribe(data => {\n      transData = data;\n    });\n    return transData[name?.toLowerCase()] ? transData[name?.toLowerCase()] : name;\n  }\n  navigateToSellerHub() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE, '', 'MARKETPLACE_SELLER', 1, true);\n    }\n    if (isPlatformBrowser(this.platformId)) {\n      window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\n    }\n  }\n  static #_ = this.ɵfac = function NavbarComponent_Factory(t) {\n    return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavbarComponent,\n    selectors: [[\"app-mtn-navbar\"]],\n    viewQuery: function NavbarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.widgetsContent = _t.first);\n      }\n    },\n    decls: 20,\n    vars: 10,\n    consts: [[1, \"navbar\", \"mb-1\", \"header-braedcrum\"], [1, \"d-flex\", \"justify-content-between\", 2, \"width\", \"100%\"], [1, \"d-inline-flex\", \"flex-row\", \"bars-tab\", 2, \"width\", \"70%\"], [4, \"ngIf\"], [1, \"showCategorySlider\"], [\"class\", \"pull-left mt-sm align-self-center mt-1 ul-mobile\", 4, \"ngIf\"], [1, \"custom-slider-main\"], [2, \"width\", \"70rem\"], [\"widgetsContent\", \"\"], [\"class\", \"ul-mobile\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"pull-right mt-sm align-self-center mt-1 ul-mobile pi-white\", 4, \"ngIf\"], [1, \"d-inline-flex\", \"flex-row\", 3, \"ngClass\"], [1, \"storecloud-button-holder\", 2, \"display\", \"inline-flex\"], [\"class\", \"storecloud-live-button\", \"style\", \"padding-right: 20px;\", 4, \"ngIf\"], [\"class\", \"sell-yalla ng-star-inserted\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"sell-marketPlace ng-star-inserted\", 3, \"click\", 4, \"ngIf\"], [1, \"pull-left\", \"mt-sm\", \"align-self-center\", \"mt-1\", \"ul-mobile\"], [1, \"pi\", \"pi-angle-left\", 3, \"click\"], [1, \"ul-mobile\"], [\"class\", \"feature-label\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"feature-label\", 3, \"ngClass\", \"click\"], [1, \"pull-right\", \"mt-sm\", \"align-self-center\", \"mt-1\", \"ul-mobile\", \"pi-white\"], [1, \"pi\", \"pi-angle-right\", 3, \"click\"], [1, \"storecloud-live-button\", 2, \"padding-right\", \"20px\"], [\"routerLink\", \"merchants-livestream\", 1, \"btn\", \"btn_live\"], [1, \"live-icon\"], [1, \"sell-yalla\", \"ng-star-inserted\", 3, \"click\"], [1, \"sell-on-yalla\", \"cursor-pointer\"], [1, \"sell-on-yalla-logo\"], [1, \"sell-marketPlace\", \"ng-star-inserted\", 3, \"click\"], [1, \"sell-on-marketPlace\", \"cursor-pointer\"], [1, \"sell-on-marketplace-logo\"]],\n    template: function NavbarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"ul\")(4, \"li\")(5, \"a\");\n        i0.ɵɵtemplate(6, NavbarComponent_div_6_Template, 2, 0, \"div\", 3);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 4);\n        i0.ɵɵtemplate(8, NavbarComponent_div_8_Template, 2, 0, \"div\", 5);\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"ul\", 7, 8);\n        i0.ɵɵtemplate(12, NavbarComponent_li_12_Template, 2, 1, \"li\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(13, NavbarComponent_div_13_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\")(16, \"span\", 12);\n        i0.ɵɵtemplate(17, NavbarComponent_div_17_Template, 5, 3, \"div\", 13);\n        i0.ɵɵtemplate(18, NavbarComponent_a_18_Template, 5, 3, \"a\", 14);\n        i0.ɵɵtemplate(19, NavbarComponent_a_19_Template, 5, 3, \"a\", 15);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isShop);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.leftArrow);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.allFeatures);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.rightArrow && ctx.allFeatures.length > 10);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, ctx.isShowLiveStream));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isShowLiveStream);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isStoreCloud);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isStoreCloud);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i4.RouterLink, i8.SideMenuComponent, i2.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-content: center;\\n  align-items: center;\\n  padding-bottom: 5px;\\n}\\n.navbar[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: nowrap;\\n  overflow-x: scroll;\\n  list-style-type: none;\\n  margin: 0;\\n  padding: 0px 0;\\n  background-color: transparent;\\n}\\n.navbar[_ngcontent-%COMP%]   .custom-slider-main[_ngcontent-%COMP%] {\\n  overflow: auto;\\n}\\n.navbar[_ngcontent-%COMP%]   .custom-slider-main[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  float: left;\\n}\\n.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: block;\\n  color: var(--navbar_txtcolor);\\n  text-align: center;\\n  white-space: nowrap;\\n  padding-top: 8px;\\n  text-decoration: none;\\n  font-size: 16px;\\n  cursor: auto !important;\\n}\\n.navbar[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.custom-slider-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  overflow: hidden;\\n  scroll-behavior: smooth;\\n}\\n\\n.info-box[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n}\\n\\n.r-a[_ngcontent-%COMP%] {\\n  right: 0;\\n  position: fixed;\\n  background-color: var(--navbar_bgcolor);\\n  cursor: pointer;\\n}\\n\\n.pi-white[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.sell-on-yalla[_ngcontent-%COMP%] {\\n  border: 2px solid #ffffff;\\n  border-radius: 8px;\\n  padding: 4px 16px 4px 16px;\\n  font-family: var(--regular-font);\\n  position: relative;\\n  font-weight: 400;\\n  text-align: center;\\n  z-index: 3;\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]:hover {\\n  background-color: #ffffff;\\n  color: #E9003A;\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]:hover   .sell-on-yalla-logo[_ngcontent-%COMP%] {\\n  background-image: url('sell-on-yalla-red.png');\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]   .sell-on-yalla-logo[_ngcontent-%COMP%] {\\n  right: 2px;\\n  position: relative;\\n  top: 3px;\\n  background-image: url('sell-on-yalla.png');\\n  width: 13px;\\n  display: inline-block;\\n  height: 14px;\\n}\\n\\n.sell-yalla[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n}\\n\\n.sell-on-marketPlace[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 4px 16px 4px 16px;\\n  font-family: var(--regular-font);\\n  position: relative;\\n  font-weight: 400;\\n  text-align: center;\\n  font-size: 14px;\\n  font-style: normal;\\n  line-height: 24px;\\n  z-index: 3;\\n  background-color: var(--header_bgcolor);\\n}\\n.sell-on-marketPlace[_ngcontent-%COMP%]   .sell-on-marketplace-logo[_ngcontent-%COMP%] {\\n  right: 2px;\\n  position: relative;\\n  top: 3px;\\n  background-image: url('marketplace-nav-log.svg');\\n  width: 20px;\\n  height: 20px;\\n  display: inline-block;\\n}\\n\\n.sell-marketPlace[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n}\\n\\ni.pi.pi-angle-right[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n\\nem.pi.pi-angle-right[_ngcontent-%COMP%] {\\n  color: #1a445e !important;\\n}\\n\\ni.pi.pi-angle-left[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n\\nem.pi.pi-angle-left[_ngcontent-%COMP%] {\\n  color: #1a445e !important;\\n}\\n\\n.feature-label[_ngcontent-%COMP%] {\\n  color: var(--header_bgcolor);\\n  padding: 0.5em;\\n  position: relative;\\n  display: flex;\\n  white-space: nowrap;\\n  cursor: pointer;\\n  justify-content: space-between;\\n  font-family: var(--regular-font);\\n  border-radius: 5px;\\n  font-weight: 400;\\n  font-size: 16px;\\n}\\n\\n.extra-pad[_ngcontent-%COMP%] {\\n  padding: 0 0.5em !important;\\n}\\n\\n.live-icon[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  position: relative;\\n  top: calc(50% - 5px);\\n  background-color: red;\\n  width: 10px;\\n  height: 10px;\\n  margin-left: 20px;\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  border-radius: 50%;\\n  z-index: 1;\\n}\\n.live-icon[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  display: block;\\n  position: absolute;\\n  background-color: rgba(255, 0, 0, 0.6);\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_live 2s ease-in-out infinite;\\n  z-index: -1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_live {\\n  0% {\\n    transform: scale(1, 1);\\n  }\\n  100% {\\n    transform: scale(3.5, 3.5);\\n    background-color: rgba(255, 0, 0, 0);\\n  }\\n}\\n.btn[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 700;\\n  margin-top: 2px;\\n  position: relative;\\n  padding: 2px 16px;\\n  background-color: rgba(255, 255, 255, 0.9);\\n  text-decoration: none;\\n  color: #333;\\n  border-radius: 5px;\\n  border: 1px solid white;\\n  transition: all 0.2s;\\n  z-index: 1;\\n  outline: none;\\n  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(0px);\\n  padding: calc(3px * 1.2) calc(16px * 1.2);\\n  background-color: white;\\n  border: none;\\n  box-shadow: 0px 5px 10px #ededed;\\n}\\n.btn[_ngcontent-%COMP%]:hover    > span[_ngcontent-%COMP%]:before {\\n  animation: none;\\n}\\n\\n.isShowlivestream[_ngcontent-%COMP%] {\\n  width: 29%;\\n  justify-content: end;\\n}\\n\\n@media only screen and (max-width: 500px) {\\n  .storecloud-button-holder[_ngcontent-%COMP%] {\\n    display: unset !important;\\n    width: 100%;\\n  }\\n  .mobile-navbar[_ngcontent-%COMP%] {\\n    width: 0rem !important;\\n  }\\n  .bars-tab[_ngcontent-%COMP%] {\\n    width: 7% !important;\\n  }\\n  .storecloud-live-button[_ngcontent-%COMP%] {\\n    padding-right: unset !important;\\n    padding-bottom: 5px;\\n  }\\n  .btn[_ngcontent-%COMP%] {\\n    width: 250px;\\n  }\\n  span.live-icon[_ngcontent-%COMP%] {\\n    position: absolute;\\n    right: 20px;\\n  }\\n}\\n@media only screen and (max-width: 350px) {\\n  .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  .navbar-menu[_ngcontent-%COMP%] {\\n    width: 78%;\\n  }\\n  .showCategorySlider[_ngcontent-%COMP%] {\\n    width: 96%;\\n    display: flex;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .navbar-menu[_ngcontent-%COMP%] {\\n    width: 78%;\\n  }\\n  .showCategorySlider[_ngcontent-%COMP%] {\\n    width: 96%;\\n    display: flex;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .navbar-menu[_ngcontent-%COMP%] {\\n    width: 78%;\\n  }\\n  .showCategorySlider[_ngcontent-%COMP%] {\\n    width: 96%;\\n    display: flex;\\n  }\\n  .sell-on-marketPlace[_ngcontent-%COMP%] {\\n    zoom: 0.6;\\n    margin-top: 13px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .showCategorySlider[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .sell-button-section[_ngcontent-%COMP%] {\\n    margin-right: 20px;\\n  }\\n  .ul-mobile[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .navbar[_ngcontent-%COMP%] {\\n    padding-top: 0px !important;\\n    padding-bottom: 5px;\\n  }\\n  li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    padding: 10px 20px 0px 2px !important;\\n  }\\n}\\n.storecloud-button-holder[_ngcontent-%COMP%] {\\n  width: 110%;\\n}\\n\\n@media only screen and (min-width: 320px) and (max-width: 360px) {\\n  .sell-on-marketPlace[_ngcontent-%COMP%] {\\n    zoom: 0.8;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["ElementRef", "PLATFORM_ID", "environment", "isPlatformBrowser", "GaLocalActionEnum", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "NavbarComponent_div_8_Template_em_click_1_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "scrollLeft", "NavbarComponent_li_12_span_1_Template_span_click_0_listener", "_r14", "feature_r10", "$implicit", "ctx_r12", "redirectFeaturedProducts", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "ctx_r11", "isShowSlider", "ɵɵadvance", "ɵɵtextInterpolate", "categoryName", "ɵɵtemplate", "NavbarComponent_li_12_span_1_Template", "shouldNotDisplay", "NavbarComponent_div_13_Template_em_click_1_listener", "_r17", "ctx_r16", "scrollRight", "ɵɵpipeBind1", "NavbarComponent_a_18_Template_a_click_0_listener", "_r19", "ctx_r18", "navigateToSellerHub", "ɵɵtextInterpolate1", "NavbarComponent_a_19_Template_a_click_0_listener", "_r21", "ctx_r20", "NavbarComponent", "constructor", "store", "translate", "mainDataService", "messageService", "appDataService", "router", "permissionService", "cdr", "$gaService", "$gtmService", "platformId", "categories", "isStoreCloud", "leftArrow", "rightArrow", "isShop", "link", "items", "title", "allFeatures", "topNumber", "isShowLiveStream", "isGoogleAnalytics", "ShowCategory", "ngOnInit", "hasPermission", "getMainData", "getAllFeatures", "widgetsContent", "nativeElement", "scrollTo", "right", "behavior", "left", "ngAfterViewInit", "setTimeout", "subscription", "subscribe", "next", "res", "slice", "error", "err", "console", "initialData", "set", "navbarData", "layoutTemplate", "find", "section", "type", "flashSale", "description", "flashSaleData", "JSON", "parse", "data", "features", "isActive", "promotionId", "isMainMenu", "id", "promotionName", "push", "feature", "event", "CLICK_ON_QUICK_LINKS", "categorySelected", "navigate", "pushPageView", "categoryIds", "translated", "name", "transData", "replace", "get", "toLowerCase", "CLICK_ON_SELL_ON_MARKETPLACE", "window", "open", "merchantURL", "localStorage", "getItem", "_", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "TranslateService", "MainDataService", "i3", "MessageService", "AppDataService", "i4", "Router", "PermissionService", "ChangeDetectorRef", "i5", "GoogleAnalyticsService", "i6", "GTMService", "_2", "selectors", "viewQuery", "NavbarComponent_Query", "rf", "ctx", "NavbarComponent_div_6_Template", "NavbarComponent_div_8_Template", "NavbarComponent_li_12_Template", "NavbarComponent_div_13_Template", "NavbarComponent_div_17_Template", "NavbarComponent_a_18_Template", "NavbarComponent_a_19_Template", "length", "_c2"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\navbar\\navbar.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef,\r\n  Inject,\r\n  OnInit,\r\n  PLATFORM_ID,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport {Category} from \"@core/interface\";\r\nimport {MessageService} from 'primeng/api';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {environment} from '@environments/environment';\r\nimport {StoreService, MainDataService, AppDataService, PermissionService} from \"@core/services\";\r\nimport {Router} from \"@angular/router\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-mtn-navbar',\r\n  templateUrl: './navbar.component.html',\r\n  styleUrls: ['./navbar.component.scss']\r\n})\r\nexport class NavbarComponent implements OnInit, AfterViewInit {\r\n  categories: Array<Category> = [];\r\n  @ViewChild('widgetsContent', { read: ElementRef }) public widgetsContent: ElementRef<any>;\r\n  isShowSlider: boolean = environment.isStoreCloud\r\n  ShowCategory;\r\n  leftArrow: boolean = true;\r\n  rightArrow: boolean = true;\r\n  isShop: boolean = false;\r\n  link = 'link';\r\n  isStoreCloud: boolean = environment.isStoreCloud;\r\n  items: any[] = [\r\n    {title: 'Orangies', link: 'https://www.github.com/isahohieku'},\r\n    {title: 'Apple', link: 'https://www.github.com/isahohieku'},\r\n    {title: 'Mango', link: 'https://www.github.com/isahohieku'},\r\n    {title: 'Carrot', link: 'https://www.github.com/isahohieku'}\r\n  ];\r\n  allFeatures: any = [];\r\n  flashSale:any;\r\n  flashSaleData:any;\r\n  topNumber:number = 1000;\r\n  navbarData: any;\r\n  isShowLiveStream: boolean = false\r\n  isGoogleAnalytics: boolean = false\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private translate: TranslateService,\r\n    private mainDataService: MainDataService,\r\n    private messageService: MessageService,\r\n    private appDataService: AppDataService,\r\n    private router: Router,\r\n    private permissionService: PermissionService,\r\n    private cdr: ChangeDetectorRef,\r\n    private $gaService: GoogleAnalyticsService,\r\n    private $gtmService:GTMService,\r\n    @Inject(PLATFORM_ID) private platformId: any\r\n  ) {\r\n    this.ShowCategory = false;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isShowLiveStream = this.permissionService.hasPermission('Live-Stream');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    this.getMainData();\r\n    this.getAllFeatures()\r\n  }\r\n\r\n  scrollLeft() {\r\n    this.widgetsContent.nativeElement.scrollLeft -= 200\r\n    this.widgetsContent.nativeElement.scrollTo({ right: (this.widgetsContent.nativeElement.scrollLeft), behavior: 'smooth' });\r\n    if (this.widgetsContent.nativeElement.scrollLeft == 0 || this.widgetsContent.nativeElement.scrollLeft < 50) {\r\n      this.leftArrow = true;\r\n    } else {\r\n      this.leftArrow = false;\r\n    }\r\n    this.rightArrow = true;\r\n\r\n  }\r\n\r\n  scrollRight() {\r\n    this.widgetsContent.nativeElement.scrollLeft += 200\r\n    this.widgetsContent.nativeElement.scrollTo({ left: (this.widgetsContent.nativeElement.scrollLeft), behavior: 'smooth' });\r\n    this.leftArrow = false;\r\n\r\n    if (this.widgetsContent.nativeElement.scrollLeft > 0) {\r\n      this.leftArrow = false;\r\n    }\r\n\r\n\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('categories')\r\n        .subscribe({\r\n          next: (res: any) => {\r\n            debugger\r\n            this.categories = res.slice(0, 5);\r\n\r\n\r\n          },\r\n          error: (err: any) => {\r\n            console.error(err);\r\n          }\r\n        });\r\n    }, 1);\r\n  }\r\n\r\n  getMainData(): void {\r\n    const initialData = this.appDataService.initialData\r\n    this.isShop = initialData.isShop;\r\n    this.store.set(\"isShop\", this.isShop);\r\n  }\r\n\r\n  getAllFeatures() {\r\n   this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar')\r\n    this.flashSale = this.appDataService.layoutTemplate.find((section: any) => section.description === 'Flash Sale');\r\n   if(this.flashSale){\r\n     this.flashSaleData=JSON.parse(this.flashSale.data);\r\n   }\r\n    let features = JSON.parse(this.navbarData.data)\r\n    if(this.flashSale?.isActive && this.flashSale?.promotionId && this.flashSaleData.isMainMenu){\r\n      let data:any={id:this.flashSale.promotionId, categoryName:this.flashSaleData.promotionName,type:this.flashSale.type,isActive:this.flashSale.isActive}\r\n      features.push(data);\r\n    }\r\n    if(this.navbarData.isActive) {\r\n      this.allFeatures = features;\r\n    }\r\n\r\n  }\r\n\r\n  redirectFeaturedProducts(feature: any){\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_QUICK_LINKS, '', 'QUICK_LINKS', 1, true, {\r\n      categorySelected: feature.categoryName,\r\n    });\r\n  }\r\n    if(feature.type && feature.type === 'promotion') {\r\n      this.router.navigate([`/promotion/${feature.categoryName}`])\r\n      this.$gtmService.pushPageView('promotion', feature.categoryName)\r\n    } else {\r\n      if(feature.categoryIds){\r\n        this.router.navigate([\r\n          `/category/${feature.id}`,\r\n        ]);\r\n        this.$gtmService.pushPageView('category', feature.categoryName)\r\n      }\r\n      else{\r\n        this.router.navigate([\r\n          `/category/${feature.id}&${this.topNumber}&${feature.categoryName}`,\r\n        ]);\r\n      }\r\n    }\r\n\r\n\r\n  }\r\n\r\n  translated(name: string) {\r\n    let transData: any;\r\n    if (!name) return \"\"\r\n    name = name?.replace(/\\s/g, '');\r\n\r\n    this.translate.get('featureType').subscribe((data: any) => {\r\n      transData = data;\r\n\r\n    });\r\n\r\n    return transData[name?.toLowerCase()] ? transData[name?.toLowerCase()] : name;\r\n  }\r\n\r\n  navigateToSellerHub() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE, '', 'MARKETPLACE_SELLER', 1, true);\r\n    }\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.open(environment.merchantURL +'?tenantId=' +`${localStorage.getItem('tenantId')}`, '_blank');\r\n    }\r\n  }\r\n}\r\n", "<section class=\"navbar mb-1 header-braedcrum\">\r\n  <div class=\"d-flex justify-content-between\" style=\"width:100%\">\r\n    <div class=\"d-inline-flex flex-row bars-tab\" style=\"width: 70%\">\r\n      <ul>\r\n        <li>\r\n          <a>\r\n            <div *ngIf=\"!isShop\">\r\n              <app-mtn-side-menu></app-mtn-side-menu>\r\n            </div>\r\n          </a>\r\n        </li>\r\n      </ul>\r\n      <div class=\"showCategorySlider\">\r\n        <div *ngIf=\"!leftArrow\" class=\"pull-left mt-sm align-self-center mt-1 ul-mobile\">\r\n          <em (click)=\"scrollLeft()\" class=\"pi pi-angle-left\"></em>\r\n        </div>\r\n        <div  class=\"custom-slider-main\">\r\n\r\n          <ul style=\"width:70rem\" #widgetsContent>\r\n            <li *ngFor=\"let feature of allFeatures\" class=\"ul-mobile\">\r\n              <span (click)=\"redirectFeaturedProducts(feature)\" [ngClass]=\"{'extra-pad': isShowSlider}\"\r\n                class=\"feature-label\" *ngIf=\"!feature.shouldNotDisplay\">{{feature.categoryName}}</span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n        <div *ngIf=\"rightArrow && allFeatures.length > 10\"\r\n          class=\"pull-right mt-sm align-self-center mt-1 ul-mobile pi-white\">\r\n          <em (click)=\"scrollRight()\" class=\"pi pi-angle-right\"></em>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"d-inline-flex flex-row\" [ngClass]=\"{'isShowlivestream': isShowLiveStream}\">\r\n\r\n      <div>\r\n        <span class=\"storecloud-button-holder\" style=\"display: inline-flex;\">\r\n          <div class=\"storecloud-live-button\"  *ngIf=\"isShowLiveStream\" style=\"padding-right: 20px;\">\r\n            <a class=\"btn btn_live\" routerLink=\"merchants-livestream\">{{'landingNavbar.liveMerchants' | translate}}<span\r\n                class=\"live-icon\"></span></a>\r\n          </div>\r\n          <a (click)=\"navigateToSellerHub()\" *ngIf=\"isStoreCloud\" class=\"sell-yalla ng-star-inserted\">\r\n\r\n            <div class=\"sell-on-yalla cursor-pointer\">\r\n              <span class=\"sell-on-yalla-logo\"> </span> {{'landingNavbar.sellOnYallaMall' | translate}}\r\n            </div>\r\n\r\n\r\n\r\n\r\n          </a>\r\n          <a (click)=\"navigateToSellerHub()\" *ngIf=\"!isStoreCloud\" class=\"sell-marketPlace ng-star-inserted\">\r\n            <div class=\"sell-on-marketPlace cursor-pointer\">\r\n              <span class=\"sell-on-marketplace-logo\"> </span> {{'landingNavbar.sellOnMarketplace' | translate}}\r\n            </div>\r\n          </a>\r\n        </span>\r\n\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</section>\r\n"], "mappings": "AAAA,SAIEA,UAAU,EAGVC,WAAW,QAEN,eAAe;AAItB,SAAQC,WAAW,QAAO,2BAA2B;AAGrD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,iBAAiB,QAAO,kCAAkC;;;;;;;;;;;;;ICXtDC,EAAA,CAAAC,cAAA,UAAqB;IACnBD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAKVH,EAAA,CAAAC,cAAA,cAAiF;IAC3ED,EAAA,CAAAI,UAAA,mBAAAC,mDAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAA0BX,EAAA,CAAAG,YAAA,EAAK;;;;;;;;;;;IAMrDH,EAAA,CAAAC,cAAA,eAC0D;IADpDD,EAAA,CAAAI,UAAA,mBAAAQ,4DAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAO,IAAA;MAAA,MAAAC,WAAA,GAAAd,EAAA,CAAAS,aAAA,GAAAM,SAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAM,OAAA,CAAAC,wBAAA,CAAAH,WAAA,CAAiC;IAAA,EAAC;IACSd,EAAA,CAAAkB,MAAA,GAAwB;IAAAlB,EAAA,CAAAG,YAAA,EAAO;;;;;IADvCH,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,YAAA,EAAuC;IAC/BvB,EAAA,CAAAwB,SAAA,GAAwB;IAAxBxB,EAAA,CAAAyB,iBAAA,CAAAX,WAAA,CAAAY,YAAA,CAAwB;;;;;IAFpF1B,EAAA,CAAAC,cAAA,aAA0D;IACxDD,EAAA,CAAA2B,UAAA,IAAAC,qCAAA,mBACyF;IAC3F5B,EAAA,CAAAG,YAAA,EAAK;;;;IADsBH,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAmB,UAAA,UAAAL,WAAA,CAAAe,gBAAA,CAA+B;;;;;;IAI9D7B,EAAA,CAAAC,cAAA,cACqE;IAC/DD,EAAA,CAAAI,UAAA,mBAAA0B,oDAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAsB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAA2BjC,EAAA,CAAAG,YAAA,EAAK;;;;;IAQ3DH,EAAA,CAAAC,cAAA,cAA2F;IAC/BD,EAAA,CAAAkB,MAAA,GAA6C;;IAAAlB,EAAA,CAAAE,SAAA,eAC1E;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IADyBH,EAAA,CAAAwB,SAAA,GAA6C;IAA7CxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAkC,WAAA,sCAA6C;;;;;;IAGzGlC,EAAA,CAAAC,cAAA,YAA4F;IAAzFD,EAAA,CAAAI,UAAA,mBAAA+B,iDAAA;MAAAnC,EAAA,CAAAM,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAA2B,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAEhCtC,EAAA,CAAAC,cAAA,cAA0C;IACxCD,EAAA,CAAAE,SAAA,eAAyC;IAACF,EAAA,CAAAkB,MAAA,GAC5C;;IAAAlB,EAAA,CAAAG,YAAA,EAAM;;;IADsCH,EAAA,CAAAwB,SAAA,GAC5C;IAD4CxB,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAAkC,WAAA,6CAC5C;;;;;;IAMFlC,EAAA,CAAAC,cAAA,YAAmG;IAAhGD,EAAA,CAAAI,UAAA,mBAAAoC,iDAAA;MAAAxC,EAAA,CAAAM,aAAA,CAAAmC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAgC,OAAA,CAAAJ,mBAAA,EAAqB;IAAA,EAAC;IAChCtC,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAE,SAAA,eAA+C;IAACF,EAAA,CAAAkB,MAAA,GAClD;;IAAAlB,EAAA,CAAAG,YAAA,EAAM;;;IAD4CH,EAAA,CAAAwB,SAAA,GAClD;IADkDxB,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAAkC,WAAA,+CAClD;;;;;;;;ADzBZ,OAAM,MAAOS,eAAe;EAwB1BC,YACUC,KAAmB,EACnBC,SAA2B,EAC3BC,eAAgC,EAChCC,cAA8B,EAC9BC,cAA8B,EAC9BC,MAAc,EACdC,iBAAoC,EACpCC,GAAsB,EACtBC,UAAkC,EAClCC,WAAsB,EACDC,UAAe;IAVpC,KAAAV,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IACU,KAAAC,UAAU,GAAVA,UAAU;IAlCzC,KAAAC,UAAU,GAAoB,EAAE;IAEhC,KAAAjC,YAAY,GAAY1B,WAAW,CAAC4D,YAAY;IAEhD,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAC,MAAM,GAAY,KAAK;IACvB,KAAAC,IAAI,GAAG,MAAM;IACb,KAAAJ,YAAY,GAAY5D,WAAW,CAAC4D,YAAY;IAChD,KAAAK,KAAK,GAAU,CACb;MAACC,KAAK,EAAE,UAAU;MAAEF,IAAI,EAAE;IAAmC,CAAC,EAC9D;MAACE,KAAK,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAmC,CAAC,EAC3D;MAACE,KAAK,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAmC,CAAC,EAC3D;MAACE,KAAK,EAAE,QAAQ;MAAEF,IAAI,EAAE;IAAmC,CAAC,CAC7D;IACD,KAAAG,WAAW,GAAQ,EAAE;IAGrB,KAAAC,SAAS,GAAU,IAAI;IAEvB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,iBAAiB,GAAY,KAAK;IAehC,IAAI,CAACC,YAAY,GAAG,KAAK;EAC3B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACf,iBAAiB,CAACmB,aAAa,CAAC,aAAa,CAAC;IAC3E,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAAChB,iBAAiB,CAACmB,aAAa,CAAC,kBAAkB,CAAC;IAEjF,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA7D,UAAUA,CAAA;IACR,IAAI,CAAC8D,cAAc,CAACC,aAAa,CAAC/D,UAAU,IAAI,GAAG;IACnD,IAAI,CAAC8D,cAAc,CAACC,aAAa,CAACC,QAAQ,CAAC;MAAEC,KAAK,EAAG,IAAI,CAACH,cAAc,CAACC,aAAa,CAAC/D,UAAW;MAAEkE,QAAQ,EAAE;IAAQ,CAAE,CAAC;IACzH,IAAI,IAAI,CAACJ,cAAc,CAACC,aAAa,CAAC/D,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC8D,cAAc,CAACC,aAAa,CAAC/D,UAAU,GAAG,EAAE,EAAE;MAC1G,IAAI,CAAC+C,SAAS,GAAG,IAAI;KACtB,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,KAAK;;IAExB,IAAI,CAACC,UAAU,GAAG,IAAI;EAExB;EAEA1B,WAAWA,CAAA;IACT,IAAI,CAACwC,cAAc,CAACC,aAAa,CAAC/D,UAAU,IAAI,GAAG;IACnD,IAAI,CAAC8D,cAAc,CAACC,aAAa,CAACC,QAAQ,CAAC;MAAEG,IAAI,EAAG,IAAI,CAACL,cAAc,CAACC,aAAa,CAAC/D,UAAW;MAAEkE,QAAQ,EAAE;IAAQ,CAAE,CAAC;IACxH,IAAI,CAACnB,SAAS,GAAG,KAAK;IAEtB,IAAI,IAAI,CAACe,cAAc,CAACC,aAAa,CAAC/D,UAAU,GAAG,CAAC,EAAE;MACpD,IAAI,CAAC+C,SAAS,GAAG,KAAK;;EAI1B;EAEAqB,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAEd,IAAI,CAACnC,KAAK,CAACoC,YAAY,CAAC,YAAY,CAAC,CAClCC,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB;UACA,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAGnC,CAAC;QACDC,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;QACpB;OACD,CAAC;IACN,CAAC,EAAE,CAAC,CAAC;EACP;EAEAhB,WAAWA,CAAA;IACT,MAAMkB,WAAW,GAAG,IAAI,CAACxC,cAAc,CAACwC,WAAW;IACnD,IAAI,CAAC7B,MAAM,GAAG6B,WAAW,CAAC7B,MAAM;IAChC,IAAI,CAACf,KAAK,CAAC6C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC9B,MAAM,CAAC;EACvC;EAEAY,cAAcA,CAAA;IACb,IAAI,CAACmB,UAAU,GAAG,IAAI,CAAC1C,cAAc,CAAC2C,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IACrG,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC/C,cAAc,CAAC2C,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACG,WAAW,KAAK,YAAY,CAAC;IACjH,IAAG,IAAI,CAACD,SAAS,EAAC;MAChB,IAAI,CAACE,aAAa,GAACC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,SAAS,CAACK,IAAI,CAAC;;IAEnD,IAAIC,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAAC,IAAI,CAACT,UAAU,CAACU,IAAI,CAAC;IAC/C,IAAG,IAAI,CAACL,SAAS,EAAEO,QAAQ,IAAI,IAAI,CAACP,SAAS,EAAEQ,WAAW,IAAI,IAAI,CAACN,aAAa,CAACO,UAAU,EAAC;MAC1F,IAAIJ,IAAI,GAAK;QAACK,EAAE,EAAC,IAAI,CAACV,SAAS,CAACQ,WAAW;QAAE9E,YAAY,EAAC,IAAI,CAACwE,aAAa,CAACS,aAAa;QAACZ,IAAI,EAAC,IAAI,CAACC,SAAS,CAACD,IAAI;QAACQ,QAAQ,EAAC,IAAI,CAACP,SAAS,CAACO;MAAQ,CAAC;MACrJD,QAAQ,CAACM,IAAI,CAACP,IAAI,CAAC;;IAErB,IAAG,IAAI,CAACV,UAAU,CAACY,QAAQ,EAAE;MAC3B,IAAI,CAACvC,WAAW,GAAGsC,QAAQ;;EAG/B;EAEArF,wBAAwBA,CAAC4F,OAAY;IACnC,IAAG,IAAI,CAAC1C,iBAAiB,EAAC;MAC1B,IAAI,CAACd,UAAU,CAACyD,KAAK,CAAC/G,iBAAiB,CAACgH,oBAAoB,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE;QACxFC,gBAAgB,EAAEH,OAAO,CAACnF;OAC3B,CAAC;;IAEF,IAAGmF,OAAO,CAACd,IAAI,IAAIc,OAAO,CAACd,IAAI,KAAK,WAAW,EAAE;MAC/C,IAAI,CAAC7C,MAAM,CAAC+D,QAAQ,CAAC,CAAC,cAAcJ,OAAO,CAACnF,YAAY,EAAE,CAAC,CAAC;MAC5D,IAAI,CAAC4B,WAAW,CAAC4D,YAAY,CAAC,WAAW,EAAEL,OAAO,CAACnF,YAAY,CAAC;KACjE,MAAM;MACL,IAAGmF,OAAO,CAACM,WAAW,EAAC;QACrB,IAAI,CAACjE,MAAM,CAAC+D,QAAQ,CAAC,CACnB,aAAaJ,OAAO,CAACH,EAAE,EAAE,CAC1B,CAAC;QACF,IAAI,CAACpD,WAAW,CAAC4D,YAAY,CAAC,UAAU,EAAEL,OAAO,CAACnF,YAAY,CAAC;OAChE,MACG;QACF,IAAI,CAACwB,MAAM,CAAC+D,QAAQ,CAAC,CACnB,aAAaJ,OAAO,CAACH,EAAE,IAAI,IAAI,CAACzC,SAAS,IAAI4C,OAAO,CAACnF,YAAY,EAAE,CACpE,CAAC;;;EAKR;EAEA0F,UAAUA,CAACC,IAAY;IACrB,IAAIC,SAAc;IAClB,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpBA,IAAI,GAAGA,IAAI,EAAEE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAE/B,IAAI,CAACzE,SAAS,CAAC0E,GAAG,CAAC,aAAa,CAAC,CAACtC,SAAS,CAAEmB,IAAS,IAAI;MACxDiB,SAAS,GAAGjB,IAAI;IAElB,CAAC,CAAC;IAEF,OAAOiB,SAAS,CAACD,IAAI,EAAEI,WAAW,EAAE,CAAC,GAAGH,SAAS,CAACD,IAAI,EAAEI,WAAW,EAAE,CAAC,GAAGJ,IAAI;EAC/E;EAEA/E,mBAAmBA,CAAA;IACjB,IAAG,IAAI,CAAC6B,iBAAiB,EAAC;MAC1B,IAAI,CAACd,UAAU,CAACyD,KAAK,CAAC/G,iBAAiB,CAAC2H,4BAA4B,EAAE,EAAE,EAAE,oBAAoB,EAAE,CAAC,EAAE,IAAI,CAAC;;IAExG,IAAI5H,iBAAiB,CAAC,IAAI,CAACyD,UAAU,CAAC,EAAE;MACtCoE,MAAM,CAACC,IAAI,CAAC/H,WAAW,CAACgI,WAAW,GAAE,YAAY,GAAE,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC;;EAEvG;EAAC,QAAAC,CAAA,G;qBA/JUrF,eAAe,EAAA3C,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAI,eAAA,GAAAtI,EAAA,CAAAiI,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAxI,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAO,cAAA,GAAAzI,EAAA,CAAAiI,iBAAA,CAAAS,EAAA,CAAAC,MAAA,GAAA3I,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAU,iBAAA,GAAA5I,EAAA,CAAAiI,iBAAA,CAAAjI,EAAA,CAAA6I,iBAAA,GAAA7I,EAAA,CAAAiI,iBAAA,CAAAa,EAAA,CAAAC,sBAAA,GAAA/I,EAAA,CAAAiI,iBAAA,CAAAe,EAAA,CAAAC,UAAA,GAAAjJ,EAAA,CAAAiI,iBAAA,CAmChBrI,WAAW;EAAA;EAAA,QAAAsJ,EAAA,G;UAnCVvG,eAAe;IAAAwG,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BAEW3J,UAAU;;;;;;;;;;;;QC7BjDK,EAAA,CAAAC,cAAA,iBAA8C;QAMlCD,EAAA,CAAA2B,UAAA,IAAA6H,8BAAA,iBAEM;QACRxJ,EAAA,CAAAG,YAAA,EAAI;QAGRH,EAAA,CAAAC,cAAA,aAAgC;QAC9BD,EAAA,CAAA2B,UAAA,IAAA8H,8BAAA,iBAEM;QACNzJ,EAAA,CAAAC,cAAA,aAAiC;QAG7BD,EAAA,CAAA2B,UAAA,KAAA+H,8BAAA,gBAGK;QACP1J,EAAA,CAAAG,YAAA,EAAK;QAEPH,EAAA,CAAA2B,UAAA,KAAAgI,+BAAA,kBAGM;QACR3J,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,eAAuF;QAIjFD,EAAA,CAAA2B,UAAA,KAAAiI,+BAAA,kBAGM;QACN5J,EAAA,CAAA2B,UAAA,KAAAkI,6BAAA,gBASI;QACJ7J,EAAA,CAAA2B,UAAA,KAAAmI,6BAAA,gBAII;QACN9J,EAAA,CAAAG,YAAA,EAAO;;;QAhDGH,EAAA,CAAAwB,SAAA,GAAa;QAAbxB,EAAA,CAAAmB,UAAA,UAAAoI,GAAA,CAAA3F,MAAA,CAAa;QAOjB5D,EAAA,CAAAwB,SAAA,GAAgB;QAAhBxB,EAAA,CAAAmB,UAAA,UAAAoI,GAAA,CAAA7F,SAAA,CAAgB;QAMM1D,EAAA,CAAAwB,SAAA,GAAc;QAAdxB,EAAA,CAAAmB,UAAA,YAAAoI,GAAA,CAAAvF,WAAA,CAAc;QAMpChE,EAAA,CAAAwB,SAAA,GAA2C;QAA3CxB,EAAA,CAAAmB,UAAA,SAAAoI,GAAA,CAAA5F,UAAA,IAAA4F,GAAA,CAAAvF,WAAA,CAAA+F,MAAA,MAA2C;QAMjB/J,EAAA,CAAAwB,SAAA,GAAkD;QAAlDxB,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAoB,eAAA,IAAA4I,GAAA,EAAAT,GAAA,CAAArF,gBAAA,EAAkD;QAI1ClE,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAAmB,UAAA,SAAAoI,GAAA,CAAArF,gBAAA,CAAsB;QAIxBlE,EAAA,CAAAwB,SAAA,GAAkB;QAAlBxB,EAAA,CAAAmB,UAAA,SAAAoI,GAAA,CAAA9F,YAAA,CAAkB;QAUlBzD,EAAA,CAAAwB,SAAA,GAAmB;QAAnBxB,EAAA,CAAAmB,UAAA,UAAAoI,GAAA,CAAA9F,YAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}