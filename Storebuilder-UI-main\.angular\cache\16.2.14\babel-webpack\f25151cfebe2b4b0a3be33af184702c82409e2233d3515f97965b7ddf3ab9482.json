{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { environment } from \"@environments/environment\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-google-analytics\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = function (a0) {\n  return {\n    \"promotion-vertical-section-small\": a0\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    \"background-image\": a0\n  };\n};\nfunction PromotionVerticalComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function PromotionVerticalComponent_div_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const banner_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.routeToCTA(banner_r1, banner_r1.ctaLink));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const banner_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.banners.length > 1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c1, \"url(\" + ctx_r0.getBannerImages(banner_r1.imageUrl) + \")\"));\n  }\n}\nexport class PromotionVerticalComponent {\n  constructor(platformId, $gaService, permissionService) {\n    this.platformId = platformId;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.banners = [];\n    this.isGoogleAnalytics = false;\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n  }\n  routeToCTA(banner, url) {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_BANNERS, '', 'BANNERS_ON_HOMEPAGE', 1, true, {\n          bannerId: url.split('/').pop(),\n          redirectPage: url\n        });\n      }\n      if (banner.promotionId) {\n        if (banner.ctaLink) {\n          const cta = banner.ctaLink.replace(/promotions\\//g, \"promotion/\");\n          if (!banner.ctaLink.includes('http://') && !banner.ctaLink.includes('https://')) {\n            window.location.href = 'https://' + cta;\n          } else {\n            window.location.href = cta;\n          }\n        } else {\n          let tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + banner.promotionId;\n          window.location.href = tempurl;\n        }\n      } else {\n        window.location.href = url;\n      }\n    }\n  }\n  getBannerImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  static #_ = this.ɵfac = function PromotionVerticalComponent_Factory(t) {\n    return new (t || PromotionVerticalComponent)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.PermissionService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PromotionVerticalComponent,\n    selectors: [[\"app-promotion-vertical\"]],\n    inputs: {\n      banners: \"banners\"\n    },\n    decls: 2,\n    vars: 1,\n    consts: [[1, \"promotion-vertical\", \"d-flex\", \"flex-column\", \"justify-content-space-between\"], [\"class\", \"promotion-vertical-section d-inline-flex\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"promotion-vertical-section\", \"d-inline-flex\", 3, \"ngClass\"], [1, \"banner-image\", \"banner-image-rounded\", 3, \"ngStyle\", \"click\"]],\n    template: function PromotionVerticalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, PromotionVerticalComponent_div_1_Template, 2, 6, \"div\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.banners);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgStyle],\n    styles: [\".promotion-vertical[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.promotion-vertical[_ngcontent-%COMP%]   .promotion-vertical-section[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.promotion-vertical[_ngcontent-%COMP%]   .promotion-vertical-section[_ngcontent-%COMP%]:not(:first-child) {\\n  margin-top: 15px;\\n}\\n.promotion-vertical[_ngcontent-%COMP%]   .promotion-vertical-section-small[_ngcontent-%COMP%] {\\n  height: 49%;\\n}\\n.promotion-vertical[_ngcontent-%COMP%]   .banner-image-rounded[_ngcontent-%COMP%] {\\n  border-radius: 11%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvbGFuZGluZy10ZW1wbGF0ZXMvcHJvbW90aW9uLXZlcnRpY2FsL3Byb21vdGlvbi12ZXJ0aWNhbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFlBQUE7QUFDRjtBQUFFO0VBQ0UsWUFBQTtBQUVKO0FBSUk7RUFDRSxnQkFBQTtBQUZOO0FBS0U7RUFDRSxXQUFBO0FBSEo7QUFNQTtFQUNFLGtCQUFBO0FBSkYiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvbW90aW9uLXZlcnRpY2Fse1xyXG4gIGhlaWdodDogMTAwJTtcclxuICAucHJvbW90aW9uLXZlcnRpY2FsLXNlY3Rpb24ge1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgLy8gaW1nIHtcclxuICAgIC8vICAgaGVpZ2h0OjEwMCU7XHJcbiAgICAvLyAgIHdpZHRoOjEwMCU7XHJcbiAgICAvLyAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIC8vIH1cclxuICAgICY6bm90KDpmaXJzdC1jaGlsZCkge1xyXG4gICAgICBtYXJnaW4tdG9wOiAxNXB4XHJcbiAgICB9XHJcbiAgfVxyXG4gIC5wcm9tb3Rpb24tdmVydGljYWwtc2VjdGlvbi1zbWFsbCB7XHJcbiAgICBoZWlnaHQ6NDklXHJcbiAgfVxyXG5cclxuLmJhbm5lci1pbWFnZS1yb3VuZGVke1xyXG4gIGJvcmRlci1yYWRpdXM6IDExJTtcclxufVxyXG59XHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "UtilityFunctions", "environment", "isPlatformBrowser", "GaLocalActionEnum", "i0", "ɵɵelementStart", "ɵɵlistener", "PromotionVerticalComponent_div_1_Template_div_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "banner_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "routeToCTA", "ctaLink", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "banners", "length", "ɵɵadvance", "_c1", "getBannerImages", "imageUrl", "PromotionVerticalComponent", "constructor", "platformId", "$gaService", "permissionService", "isGoogleAnalytics", "hasPermission", "banner", "url", "event", "CLICK_ON_BANNERS", "bannerId", "split", "pop", "redirectPage", "promotionId", "cta", "replace", "includes", "window", "location", "href", "tempurl", "marketPlaceHostName", "verifyImageURL", "apiEndPoint", "_", "ɵɵdirectiveInject", "i1", "GoogleAnalyticsService", "i2", "PermissionService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "PromotionVerticalComponent_Template", "rf", "ctx", "ɵɵtemplate", "PromotionVerticalComponent_div_1_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\landing-templates\\promotion-vertical\\promotion-vertical.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\landing-templates\\promotion-vertical\\promotion-vertical.component.html"], "sourcesContent": ["import {Component, Inject, Input, PLATFORM_ID} from '@angular/core';\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\nimport {environment} from \"@environments/environment\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport {PermissionService} from '@core/services'\r\n@Component({\r\n  selector: 'app-promotion-vertical',\r\n  templateUrl: './promotion-vertical.component.html',\r\n  styleUrls: ['./promotion-vertical.component.scss']\r\n})\r\nexport class PromotionVerticalComponent {\r\n  @Input() banners: any = [];\r\n  isGoogleAnalytics: boolean = false;\r\n\r\n  constructor(@Inject(PLATFORM_ID) private platformId: any ,\r\n              private $gaService: GoogleAnalyticsService,\r\n              private permissionService:PermissionService\r\n              ) {\r\n                this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics')\r\n                \r\n  }\r\n  routeToCTA(banner:any,url: string) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      if(this.isGoogleAnalytics){\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_BANNERS, '', 'BANNERS_ON_HOMEPAGE', 1, true, {\r\n        bannerId:  url.split('/').pop(),\r\n        redirectPage: url\r\n      });\r\n    }\r\n      if(banner.promotionId){\r\n        if(banner.ctaLink) {\r\n          const cta = banner.ctaLink.replace(/promotions\\//g, \"promotion/\")\r\n          if(!banner.ctaLink.includes('http://') && !banner.ctaLink.includes('https://')){\r\n            window.location.href = 'https://'+cta;\r\n          } else {\r\n            window.location.href = cta;\r\n          }\r\n        } else {\r\n          let tempurl=' https://'+environment.marketPlaceHostName+'/promotion/'+banner.promotionId;\r\n          window.location.href = tempurl;\r\n        }\r\n      }\r\n      else{\r\n        window.location.href = url;\r\n      }\r\n\r\n    }\r\n  }\r\n  getBannerImages(url: string) {\r\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\r\n  }\r\n}\r\n", "<div class=\"promotion-vertical d-flex flex-column justify-content-space-between\">\r\n  <div class=\"promotion-vertical-section d-inline-flex\" *ngFor=\"let banner of banners\" [ngClass]=\"{'promotion-vertical-section-small':banners.length>1}\">\r\n    <div (click)=\"routeToCTA(banner,banner.ctaLink)\" class=\"banner-image banner-image-rounded\" [ngStyle]=\"{'background-image': 'url('+ getBannerImages(banner.imageUrl) +')'}\"></div>\r\n\r\n    <!-- <img\r\n      alt=\"No Image\"\r\n      ngSrc=\"{{getBannerImages(banner.imageUrl)}}\"\r\n      width=\"100\" height=\"100\" (click)=\"routeToCTA(banner,banner.ctaLink)\" class=\"cursor-pointer\" *ngIf=\"banner.isActive\"> -->\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAkCA,WAAW,QAAO,eAAe;AACnE,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,SAAQC,WAAW,QAAO,2BAA2B;AACrD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,iBAAiB,QAAO,kCAAkC;;;;;;;;;;;;;;;;;;ICHhEC,EAAA,CAAAC,cAAA,aAAuJ;IAChJD,EAAA,CAAAE,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAL,SAAA,EAAAA,SAAA,CAAAM,OAAA,CAAiC;IAAA,EAAC;IAA2Hb,EAAA,CAAAc,YAAA,EAAM;;;;;IAD9Fd,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,CAAAC,MAAA,MAAiE;IACzDpB,EAAA,CAAAqB,SAAA,GAA+E;IAA/ErB,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAgB,eAAA,IAAAM,GAAA,WAAAJ,MAAA,CAAAK,eAAA,CAAAhB,SAAA,CAAAiB,QAAA,SAA+E;;;ADU9K,OAAM,MAAOC,0BAA0B;EAIrCC,YAAyCC,UAAe,EACpCC,UAAkC,EAClCC,iBAAmC;IAFd,KAAAF,UAAU,GAAVA,UAAU;IAC/B,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAL5B,KAAAV,OAAO,GAAQ,EAAE;IAC1B,KAAAW,iBAAiB,GAAY,KAAK;IAMpB,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,iBAAiB,CAACE,aAAa,CAAC,kBAAkB,CAAC;EAE/F;EACAnB,UAAUA,CAACoB,MAAU,EAACC,GAAW;IAC/B,IAAInC,iBAAiB,CAAC,IAAI,CAAC6B,UAAU,CAAC,EAAE;MACtC,IAAG,IAAI,CAACG,iBAAiB,EAAC;QAC1B,IAAI,CAACF,UAAU,CAACM,KAAK,CAACnC,iBAAiB,CAACoC,gBAAgB,EAAE,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE,IAAI,EAAE;UAC5FC,QAAQ,EAAGH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;UAC/BC,YAAY,EAAEN;SACf,CAAC;;MAEF,IAAGD,MAAM,CAACQ,WAAW,EAAC;QACpB,IAAGR,MAAM,CAACnB,OAAO,EAAE;UACjB,MAAM4B,GAAG,GAAGT,MAAM,CAACnB,OAAO,CAAC6B,OAAO,CAAC,eAAe,EAAE,YAAY,CAAC;UACjE,IAAG,CAACV,MAAM,CAACnB,OAAO,CAAC8B,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACX,MAAM,CAACnB,OAAO,CAAC8B,QAAQ,CAAC,UAAU,CAAC,EAAC;YAC7EC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU,GAACL,GAAG;WACtC,MAAM;YACLG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGL,GAAG;;SAE7B,MAAM;UACL,IAAIM,OAAO,GAAC,WAAW,GAAClD,WAAW,CAACmD,mBAAmB,GAAC,aAAa,GAAChB,MAAM,CAACQ,WAAW;UACxFI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGC,OAAO;;OAEjC,MACG;QACFH,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGb,GAAG;;;EAIhC;EACAV,eAAeA,CAACU,GAAW;IACzB,OAAOrC,gBAAgB,CAACqD,cAAc,CAAChB,GAAG,EAAEpC,WAAW,CAACqD,WAAW,CAAC;EACtE;EAAC,QAAAC,CAAA,G;qBAxCU1B,0BAA0B,EAAAzB,EAAA,CAAAoD,iBAAA,CAIjBzD,WAAW,GAAAK,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAJpBhC,0BAA0B;IAAAiC,SAAA;IAAAC,MAAA;MAAAxC,OAAA;IAAA;IAAAyC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZvCjE,EAAA,CAAAC,cAAA,aAAiF;QAC/ED,EAAA,CAAAmE,UAAA,IAAAC,yCAAA,iBAOM;QACRpE,EAAA,CAAAc,YAAA,EAAM;;;QARqEd,EAAA,CAAAqB,SAAA,GAAU;QAAVrB,EAAA,CAAAe,UAAA,YAAAmD,GAAA,CAAA/C,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}