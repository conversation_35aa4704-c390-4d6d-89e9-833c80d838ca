{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ListComponent } from \"./components/list/list.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ListComponent\n}, {\n  path: '**',\n  redirectTo: ''\n}];\nexport class WishlistRoutingModule {}\nWishlistRoutingModule.ɵfac = function WishlistRoutingModule_Factory(t) {\n  return new (t || WishlistRoutingModule)();\n};\nWishlistRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: WishlistRoutingModule\n});\nWishlistRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(WishlistRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}