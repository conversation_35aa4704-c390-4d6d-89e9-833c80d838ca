{"ast": null, "code": "import { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/dialog\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction MessageModalComponent_ng_container_1_ng_template_1_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"messageModal.pleaseSignInAgain\"), \" \");\n  }\n}\nfunction MessageModalComponent_ng_container_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"img\", 6);\n    i0.ɵɵelementStart(2, \"p\", 7)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageModalComponent_ng_container_1_ng_template_1_p_5_Template, 3, 3, \"p\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.type === \"primary\");\n  }\n}\nfunction MessageModalComponent_ng_container_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function MessageModalComponent_ng_container_1_ng_template_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageModalComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageModalComponent_ng_container_1_ng_template_1_Template, 6, 2, \"ng-template\", 3);\n    i0.ɵɵtemplate(2, MessageModalComponent_ng_container_1_ng_template_2_Template, 1, 0, \"ng-template\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MessageModalComponent_ng_template_2_ng_template_0_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"messageModal.pleaseSignInAgain\"), \" \");\n  }\n}\nfunction MessageModalComponent_ng_template_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"p\", 12)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MessageModalComponent_ng_template_2_ng_template_0_p_5_Template, 3, 3, \"p\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.type === \"primary\");\n  }\n}\nfunction MessageModalComponent_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function MessageModalComponent_ng_template_2_ng_template_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageModalComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MessageModalComponent_ng_template_2_ng_template_0_Template, 6, 2, \"ng-template\", 3);\n    i0.ɵɵtemplate(1, MessageModalComponent_ng_template_2_ng_template_1_Template, 1, 0, \"ng-template\", 4);\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport let MessageModalComponent = /*#__PURE__*/(() => {\n  class MessageModalComponent {\n    platformId;\n    displayModal = false;\n    message = '';\n    type = '';\n    submit = new EventEmitter();\n    screenWidth = window.innerWidth;\n    onResize(event) {\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n    }\n    constructor(platformId) {\n      this.platformId = platformId;\n    }\n    ngOnInit() {\n      /**/\n    }\n    onClick() {\n      this.submit.emit(true);\n    }\n    static ɵfac = function MessageModalComponent_Factory(t) {\n      return new (t || MessageModalComponent)(i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageModalComponent,\n      selectors: [[\"app-mtn-message-modal\"]],\n      hostBindings: function MessageModalComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function MessageModalComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        displayModal: \"displayModal\",\n        message: \"message\",\n        type: \"type\"\n      },\n      outputs: {\n        submit: \"submit\"\n      },\n      decls: 4,\n      vars: 10,\n      consts: [[1, \"message-modal\", 3, \"visible\", \"breakpoints\", \"closable\", \"blockScroll\", \"showHeader\", \"draggable\", \"modal\", \"visibleChange\"], [4, \"ngIf\", \"ngIfElse\"], [\"desktopView\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"model\", \"d-flex\", \"flex-column\", \"align-items-center\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/mobile-icons/success-message-icon.svg\"], [1, \"mb-1\"], [\"class\", \"mb-0\", 4, \"ngIf\"], [1, \"mb-0\"], [\"label\", \"OK\", \"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mt-3\", \"width-100\", \"message-modal__primary-btn\", \"second-btn\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/images/success-icon.svg\"], [1, \"mb-5\"], [\"label\", \"OK\", \"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-2\", \"mt-3\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"click\"]],\n      template: function MessageModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dialog\", 0);\n          i0.ɵɵlistener(\"visibleChange\", function MessageModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n            return ctx.displayModal = $event;\n          });\n          i0.ɵɵtemplate(1, MessageModalComponent_ng_container_1_Template, 3, 0, \"ng-container\", 1);\n          i0.ɵɵtemplate(2, MessageModalComponent_ng_template_2_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(9, _c0))(\"closable\", false)(\"blockScroll\", true)(\"showHeader\", false)(\"draggable\", false)(\"modal\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.screenWidth < 768)(\"ngIfElse\", _r1);\n        }\n      },\n      dependencies: [i1.ButtonDirective, i2.PrimeTemplate, i3.Dialog, i4.NgIf, i5.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}  .p-dialog .p-dialog-footer{text-align:center}  .p-dialog .p-dialog-header{padding:.5rem}  .p-dialog .p-dialog-footer button{width:100%}.refunfApprovedModal[_ngcontent-%COMP%]     .p-dialog{max-width:390px}  .p-dialog-content{border-bottom:none!important;border-radius:0!important}  .model img{width:50px;height:50px;margin-bottom:30px;margin-top:70px}  .model p{color:#000;font-size:18px;font-family:main-medium,sans-serif;margin-bottom:114px;text-align:center;line-height:25px;padding-right:28px;padding-left:28px}@media only screen and (max-width: 767px){.message-modal[_ngcontent-%COMP%]     .p-dialog-content{padding:24px 16px 0!important}.message-modal[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{margin-top:0!important;margin-bottom:15px!important}.message-modal[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#000;text-align:center;font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;line-height:normal}.message-modal__primary-btn[_ngcontent-%COMP%]{background:var(--primary, #204E6E);border-radius:8px!important;color:var(--Gray-00, #FFF);font-family:main-medium;font-size:14px;font-style:normal;font-weight:700;letter-spacing:.168px;text-transform:uppercase;place-content:center;padding:12px 24px;height:48px}}\"]\n    });\n  }\n  return MessageModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}