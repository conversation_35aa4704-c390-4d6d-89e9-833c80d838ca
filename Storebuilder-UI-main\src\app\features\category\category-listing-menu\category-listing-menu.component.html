<section class="category-listing">
    <div class="main-heading-category-mobile">
        <span *ngIf="!selectedTab" class="main-header-category">
            Categories
        </span>
        <span *ngIf="selectedTab" class="main-header-category" (click)="onBackButton()">
            <img
            class="mr-8"
            src="assets/icons/mobile-icons/back-icon.svg"
            width="20"
            height="18"
            alt=""
            title=""
          />
          <span> {{"buttons.back" | translate }} </span>
        </span>
    </div>
    <p-accordion class="accordion-holder" [ngClass]="{'apply-styles': selectedTab}" [activeIndex]="activeIndex">
        <p-accordionTab *ngIf="selectedTab" (click)="triggerGoogleAnalytics()" [iconPos]="'end'" [disabled]="selectedTab.disabled" [ngClass]="{'disabled-handling': selectedTab.disabled}">
            <ng-template pTemplate="header">

                <img
                class="mr-8"
                [src]="getImagesUrl(getCategoryImage())"
                width="20"
                height="20"
                alt=""
                title=""
                (error)="errorHandler($event)"
              />
              <span class="category-Name-Mobile" >
                {{
                    categoryName
                }}

              </span>

              </ng-template>
        </p-accordionTab>

        <p-accordionTab *ngFor="let item of categories; let i = index"  (click)="onSelectTab(item);"  class="disabled-handling"  [iconPos]="'end'" >
            <ng-template pTemplate="header">
                <div (click)="triggerGoogleAnalytics(item.categoryName)">
                <img
                class="mr-1"
                [src]="getImagesUrl(item.image)"
                width="20"
                height="20"
                alt=""
                title=""
                (error)="errorHandler($event)"
              />
              <span [ngClass]="{'child-categories-setting': selectedTab}"
               [routerLink]="item.totalProductCount ? '/category/' + item.id : null" [queryParams]="{ categoryName:item.categoryName }" >
                {{
                    item.categoryName
                }}

              </span>
            </div>
              </ng-template>
                <p-listbox *ngIf="selectedTab" class="list-box-child" [options]="childCategoryItems" [(ngModel)]="selectedCountry" optionLabel="name">
                    <ng-template let-country pTemplate="item">
                        <div class="flex align-items-center gap-2 ml-28" (click)="triggerGoogleAnalytics(country?.name)">
                            <div class="child-category-text"
                             [routerLink]="country.totalProductC ? '/category/' + country.id : null" [queryParams]="{ categoryName:country.name}">{{ country.name }} </div>
                        </div>
                    </ng-template>
                </p-listbox>
         </p-accordionTab>
    </p-accordion>

</section> 