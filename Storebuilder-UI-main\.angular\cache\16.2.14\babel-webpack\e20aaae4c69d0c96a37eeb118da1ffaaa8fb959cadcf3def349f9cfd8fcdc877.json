{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PaymentService {\n  http;\n  baseUrl;\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Payment`;\n  }\n  getAllPayments(req) {\n    return this.http.post(`${this.baseUrl}/Payment/GetAllPayment`, req);\n  }\n  getIsTenantCardPaymentEnabled() {\n    return this.http.get(`${this.baseUrl}/Cube/IsTenantCardPaymentEnabled`);\n  }\n  static ɵfac = function PaymentService_Factory(t) {\n    return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PaymentService,\n    factory: PaymentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}