import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { PermissionService, StoreService } from '@core/services';
import UtilityFunctions from '@core/utilities/functions';
import { environment } from '@environments/environment';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleAnalyticsService } from 'ngx-google-analytics';
import { AccordionModule } from 'primeng/accordion';
import { ListboxModule } from 'primeng/listbox';
import { CategoryList, ChildCategoryItem } from '@core/interface/category';

@Component({
  selector: 'app-category-listing-menu',
  templateUrl: './category-listing-menu.component.html',
  styleUrls: ['./category-listing-menu.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TranslateModule,
    AccordionModule,
    ListboxModule
  ]
})
export class CategoryListingMenuComponent implements OnInit {

  categories: CategoryList[];
  baseUrl: string = environment.apiEndPoint;
  selectedTab: CategoryList | null;
  activeIndex: number|number[]|null|undefined;
  selectedCountry!: string;
  childCategoryItems: ChildCategoryItem[];
  categoryName: string;
  listingHolder: CategoryList[];
  isGoogleAnalytics: boolean = false;

  constructor(
    private store: StoreService,
    private $gaService: GoogleAnalyticsService,
    private permissionService: PermissionService
  ) {}

  ngOnInit(): void {
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.onLoadCategories();
  }

  onLoadCategories(): void {
    this.store.subscription('categories')
      .subscribe({
        next: (res: CategoryList[]) => {
          if (res && res.length > 0) {
            this.categories = res;
            this.categories = this.categories.filter((item: CategoryList)=> !item.hide);
            this.categories?.forEach((cat: CategoryList) => {
              cat['path'] = cat.categoryName;
              cat['catIds'] = cat.id;
              cat['disabled'] = true;
            });
            this.listingHolder = this.categories;
          } else {
            const localCategories = localStorage.getItem('allCategories');
            if (localCategories && localCategories !== '') {
              this.categories = JSON.parse(localCategories);
              this.categories = this.categories.filter((item: CategoryList)=> !item.hide);
              this.categories?.forEach((cat: CategoryList) => {
                cat['path'] = cat.categoryName;
                cat['catIds'] = cat.id;
                cat['disabled'] = true;
              });
              this.listingHolder = this.categories;
            } 
          }
        },
        error: (err: any) => {
          console.error(err);
        }
      });
  }

  getCategoryImage(): string {
    const imageObj = this.listingHolder.find((item: CategoryList) => {
      return item.categoryName === this.categoryName;
    });
    return imageObj?.image || '';
  }

  onSelectTab(_tabData: CategoryList): void {
    this.activeIndex = -1;
    if (_tabData?.path) {
      this.categoryName = _tabData.path.split("/")[0];
    }
    if (!this.selectedTab) {
      this.categories = _tabData.categories.filter((cat: CategoryList) => !cat.hide);
      this.selectedTab = _tabData;
    }
    if (this.selectedTab !== _tabData) {
      this.selectedTab = _tabData;
      this.childCategoryItems = this.selectedTab?.categories?.map((cat: CategoryList) => {
        return {
          name: cat.categoryName,
          code: cat.image,
          id: cat.id,
          hidden: cat.hide,
          totalProductC: cat.totalProductCount
        };
      }) || [];
      this.childCategoryItems = this.childCategoryItems.filter((cat: ChildCategoryItem) => !cat.hidden);
    }
  }

  triggerGoogleAnalytics(subCategoryName?: string) {
    if (this.isGoogleAnalytics) {
      this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true, {
        categorySelected: subCategoryName ? subCategoryName : this.categoryName,
      });
    }
  }

  onBackButton(): void {
    this.onLoadCategories();
    this.selectedTab = null;
    this.activeIndex = -1;
  }

  errorHandler(event: Event) {
    if (environment.isStoreCloud) {
      (event.target as HTMLImageElement).src = "assets/images/placeholder.png";
    } else {
      (event.target as HTMLImageElement).src = "assets/images/mtn-alt.png";
    }
  }

  getImagesUrl(product: any) {
    return UtilityFunctions.verifyImageURL(product, environment.apiEndPoint);
  }
} 