"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[277],{8277:(nt,y,a)=>{a.r(y),a.d(y,{RegisterComponent:()=>B});var c=a(6814),l=a(6075),d=a(6663),h=a(707),C=a(9566),S=a(2284),_=a(7152),I=a(8145),A=a(5334),k=a(6574),m=a(5861),t=a(5879),f=a(3023),P=a(5662),Z=a(6776),X=a(6726),N=a(7134),R=a(8893),U=a(1064),D=a(7874),T=a(1312),g=a(864),x=a(5219),z=a(459),L=a(9147);function E(r,u){if(1&r){const e=t.EpF();t.TgZ(0,"div",4)(1,"div",5)(2,"div",6)(3,"div",7),t._UZ(4,"img",8),t.qZA(),t.TgZ(5,"div",9),t._UZ(6,"sign-in-up-header",10),t.TgZ(7,"div",11)(8,"p",12),t._uU(9),t.ALo(10,"translate"),t.qZA(),t.TgZ(11,"p",13),t._uU(12),t.ALo(13,"translate"),t.qZA()(),t.TgZ(14,"form",14)(15,"div",15)(16,"app-phone-input",16),t.NdJ("phoneNumberChange",function(o){t.CHM(e);const i=t.oxw(2);return t.KtG(i.onPhoneNumberChange(o))})("validationChange",function(o){t.CHM(e);const i=t.oxw(2);return t.KtG(i.onPhoneValidationChange(o))}),t.qZA(),t.TgZ(17,"app-password-input",17),t.NdJ("passwordChange",function(o){t.CHM(e);const i=t.oxw(2);return t.KtG(i.onPasswordChange(o))})("validationChange",function(o){t.CHM(e);const i=t.oxw(2);return t.KtG(i.onPasswordValidationChange(o))})("forgotPasswordClick",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.resetPassword())}),t.qZA(),t.TgZ(18,"button",18),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.login())}),t.ALo(19,"translate"),t.qZA(),t.TgZ(20,"p",19),t._uU(21),t.ALo(22,"translate"),t.TgZ(23,"a",20),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.reloadCurrentPage(171,"Terms and Conditions"))}),t._uU(24),t.ALo(25,"translate"),t.qZA(),t._uU(26),t.ALo(27,"translate"),t.TgZ(28,"a",20),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return t.KtG(o.reloadCurrentPage(170,"Privacy policy"))}),t._uU(29),t.ALo(30,"translate"),t.qZA(),t._uU(31),t.ALo(32,"translate"),t.qZA()()()()()()()}if(2&r){const e=t.oxw(2);t.xp6(6),t.Q6J("title","signIn.signIn")("img","assets/images/new-signin.svg"),t.xp6(3),t.Oqu(t.lcZ(10,20,"signIn.signIn")),t.xp6(3),t.Oqu(t.lcZ(13,22,"signIn.content")),t.xp6(4),t.Q6J("alreadyAddedPhoneNumber",e.phoneNumber)("maxLength",e.phoneInputLength)("selectedCountryISO",e.customCountryISO)("preferredCountries",e.preferredCountries)("placeholder",e.customPlaceHolder),t.xp6(1),t.Q6J("showForgotPassword",!0)("forgotPasswordClass",e.forgotPasswordClass)("floatingLabel",e.floatingLabelEnabled)("feedback",!1),t.xp6(1),t.Q6J("disabled",!e.isFormValid)("label",t.lcZ(19,24,e.buttonLabel)),t.xp6(3),t.hij(" ",t.lcZ(22,26,"signIn.AgreeTermsOne")," "),t.xp6(3),t.hij(" ",t.lcZ(25,28,"signIn.AgreeTermsTwo")," "),t.xp6(2),t.hij("\xa0",t.lcZ(27,30,"signIn.AgreeTermsThree")," "),t.xp6(3),t.hij(" ",t.lcZ(30,32,"signIn.AgreeTermsFour")," "),t.xp6(2),t.hij("\xa0",t.lcZ(32,34,"signIn.AgreeTermsFive"),". ")}}function G(r,u){if(1&r&&(t.TgZ(0,"section",2),t.YNc(1,E,33,36,"div",3),t.qZA()),2&r){const e=t.oxw();t.xp6(1),t.Q6J("ngIf",e.displayModal)}}const J=function(){return{"960px":"75vw","640px":"90vw"}};let V=(()=>{class r{store;auth;messageService;router;cartService;translate;cookieService;authTokenService;route;mainDataService;permissionService;loaderService;appDataService;$gaService;userService;platformId;$gtmService;displayModal=!1;phoneNumber=null;password="";isPhoneValid=!1;isPasswordValid=!1;phoneInputLength=12;customCountryISO;customPlaceHolder="";preferredCountries=[f.H.Uganda,f.H.Ghana,f.H.C\u00f4teDIvoire];submitted=!1;redirctURL;cartListCount=0;cartListData=[];isGoogleAnalytics=!1;isMobileLayout=!1;screenWidth;tagName=P.Ir;products=[];cancel=new t.vpe;constructor(e,n,o,i,s,p,b,O,v,w,$,K,q,Q,W,tt,et){this.store=e,this.auth=n,this.messageService=o,this.router=i,this.cartService=s,this.translate=p,this.cookieService=b,this.authTokenService=O,this.route=v,this.mainDataService=w,this.permissionService=$,this.loaderService=K,this.appDataService=q,this.$gaService=Q,this.userService=W,this.platformId=tt,this.$gtmService=et}ngOnInit(){console.log("SignInComponent initialized"),this.setupPermissions(),this.setupCountryISO(),this.setupRouteParams(),this.setupPhoneInputLength(),this.$gtmService.pushPageView("signIn")}initializeComponent(){this.setupCustomPlaceholder(),(0,c.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}setupPermissions(){this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout")}setupCountryISO(){if(localStorage.getItem("isoCode"))this.customCountryISO=localStorage.getItem("isoCode");else{const e=this.appDataService.tenants;if(null!=e.records){let n=localStorage.getItem("tenantId"),i=e.records.find(s=>s.tenantId==n)??new S.Sb;localStorage.setItem("isoCode",i?.isoCode),this.store.set("allCountryTenants",e.records)}}}setupRouteParams(){this.route.queryParams.subscribe(e=>{this.redirctURL=e.returnUrl})}setupPhoneInputLength(){if(this.appDataService.configuration){const e=this.appDataService.configuration.records.find(n=>"PhoneLength"===n.key);e&&(this.phoneInputLength=parseInt(e.value))}}setupCustomPlaceholder(){let e=localStorage.getItem("tenantId");e&&""!==e&&(this.customPlaceHolder={1:"XXXXXXXXX",2:"XXXXXXXXX",3:"XXXXXXXXX",4:"XXXXXXXXXX"}[e]||"")}get isFormValid(){return this.isPhoneValid&&this.isPasswordValid&&this.password.length>0}onPhoneNumberChange(e){this.phoneNumber=e}onPhoneValidationChange(e){this.isPhoneValid=e}onPasswordChange(e){this.password=e}onPasswordValidationChange(e){this.isPasswordValid=e}login(){var e=this;return(0,m.Z)(function*(){var n;e.isGoogleAnalytics&&e.$gaService.event(C.s.CLICK_ON_CONTINUE_FOR_SIGN_IN,"","SIGN_IN_STEP2",1,!0),e.loaderService.show(),e.submitted=!0,e.auth.login({username:e.phoneNumber.e164Number.slice(1),password:e.password}).subscribe({next:(n=(0,m.Z)(function*(o){yield e.handleLoginSuccess(o)}),function(i){return n.apply(this,arguments)}),error:n=>{e.handleLoginError(n)}})})()}handleLoginSuccess(e){var n=this;return(0,m.Z)(function*(){if(e?.success&&"consumer"==e.data.role){n.isGoogleAnalytics&&n.permissionService.getTagFeature("LOGIN")&&n.$gaService.event(n.tagName.LOGIN,"","",1,!0,{user_ID:e.data.mobileNumber}),yield n.updateUserConsent(e.data.id),n.setUserData(e.data),n.setAuthToken(e.data.authToken),n.loaderService.hide(),e?.data?.currency&&n.store.set("currency",e.data.currency),localStorage.setItem("refreshToken",e.data.refreshToken),n.store.set("refreshToken",e.data.refreshToken);const o={sessionId:localStorage.getItem("sessionId")},i=localStorage.getItem("cartId");yield n.checkCart(o,i),n.handlePostLoginNavigation(e.data)}else n.handleLoginFailure(e?.message)})()}updateUserConsent(e){var n=this;return(0,m.Z)(function*(){const o={consentType:Z.h.Cookie,sessionId:localStorage.getItem("consumer-consent-sessionId")||"",consent:!0,userId:e};n.userService.updateUserConsent(o).subscribe({next:i=>{}})})()}setUserData(e){this.mainDataService.setUserData(e),this.store.set("profile",e),this.store.set("userPhone",e.mobileNumber),localStorage.setItem("userId",e.id),this.store.set("timeInterval",(new Date).getTime())}setAuthToken(e){let n=e.replace("bearer ",""),i=((0,X.Z)(n).exp/864e5).toFixed(0);localStorage.removeItem("visited");const s=new Date;s.setDate(s.getDate()+parseInt(i));let p=D.AES.encrypt(n,"paysky").toString();localStorage.setItem("auth_enc",p),this.cookieService.set("authToken",n,{expires:s,path:"/",sameSite:"Strict"}),localStorage.removeItem("isGuest"),this.authTokenService.authTokenSet(n)}handlePostLoginNavigation(e){e.isPasswodExpired?(this.router.navigateByUrl("/change-password"),this.messageService.add({severity:"info",summary:this.translate.instant("ResponseMessages.changePassword"),detail:this.translate.instant("ResponseMessages.passwordExpirationChange")})):(this.redirctURL?(this.router.navigate([this.redirctURL]),this.redirctURL=null):this.router.navigate(["/"]),this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.login"),detail:this.translate.instant("ResponseMessages.loggedInSuccessfully")}))}handleLoginError(e){this.store.set("profile",""),this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:e.message}),localStorage.setItem("isGuest","true")}handleLoginFailure(e){this.store.set("profile",""),this.loaderService.hide(),this.messageService.add({severity:"error",summary:e||this.translate.instant("ErrorMessages.invalidUserNameOrPassword")}),localStorage.setItem("isGuest","true")}resetPassword(){this.isGoogleAnalytics&&this.$gaService.event(C.s.CLICK_ON_FORGOT_PASSWORD,"","FORGOT_PASSWORD",1,!0),this.router.navigate(["/reset-password"])}reloadCurrentPage(e,n){this.router.navigateByUrl("/",{skipLocationChange:!0}).then(()=>this.router.navigate(["/about-us/"],{queryParams:{pageId:e,title:n}}))}getAllCart(e){this.products=[];let n={sessionId:e.sessionId},o=localStorage.getItem("apply-to");o&&""!=o&&(n.applyTo=o),this.cartService.getCart(n).subscribe({next:i=>{this.cartListCount=0,this.cartListData=[],i.data?.records?.length?(this.cartListCount=0,i.data.records[0].cartDetails.length&&(this.cartListCount=i.data.records[0].cartDetails.length,this.cartListData=i.data.records[0].cartDetails),i.data.records[0].cartDetailsDPay&&i.data.records[0].cartDetailsDPay.length&&(this.cartListCount+=i.data.records[0].cartDetailsDPay.length,this.cartListData=this.cartListData.concat(i.data.records[0].cartDetailsDPay)),this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData)):(this.mainDataService.setCartLenghtData(0),this.mainDataService.setCartItemsData([]))}})}compareCartProducts(e,n){e.length?e.forEach(o=>{n.forEach(i=>{o.specsProductId===i.specsProductId&&this.products.push(o)})}):this.products=n,this.store.set("cartProducts",this.products),localStorage.setItem("addedProducts",JSON.stringify(this.products))}getShipmentMethodByTenantId(e){this.permissionService.hasPermission("Shipment-Fee")?this.cartService.getShipmentMethodByTenantId().subscribe(n=>{n.success&&n.data.length&&(localStorage.setItem("apply-to",n.data[0].applyTo),this.getAllCart(e))}):(localStorage.setItem("apply-to","2"),this.getAllCart(e))}checkCart(e,n){return new Promise((o,i)=>{e.sessionId?(e.cartId=n&&""!=n?parseInt(n):0,this.cartService.updateCart(e).subscribe({next:s=>{s?.data?.cartItems?.length&&(this.cartListData=s.data.cartItems,this.cartListCount=s.data.cartItems.length),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(e),o()},error:s=>{this.cartListCount=0,this.cartListData=[],this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(e),i(s)}})):(localStorage.setItem("sessionId",N.R.newGuid()),e.sessionId=localStorage.getItem("sessionId"),this.getAllCart(e),o())})}get forgotPasswordClass(){return"font-size-12"}get floatingLabelEnabled(){return!1}get buttonLabel(){return"signIn.continue"}static \u0275fac=function(n){return new(n||r)(t.Y36(g.d6),t.Y36(g.e8),t.Y36(x.ez),t.Y36(l.F0),t.Y36(g.Ni),t.Y36(d.sK),t.Y36(z.N),t.Y36(g.Lz),t.Y36(l.gz),t.Y36(g.iI),t.Y36(g.$A),t.Y36(g.D1),t.Y36(g.UW),t.Y36(P.$r),t.Y36(g.KD),t.Y36(t.Lbi),t.Y36(L.J))};static \u0275cmp=t.Xpm({type:r,selectors:[["app-register-user-modal"]],inputs:{displayModal:"displayModal",phoneNumber:"phoneNumber"},outputs:{cancel:"cancel"},standalone:!0,features:[t.jDz],decls:2,vars:8,consts:[[3,"visible","breakpoints","dismissableMask","draggable","showHeader","modal","resizable","visibleChange"],["pTemplate","content"],[1,"login"],["class","login-content-container mt-3",4,"ngIf"],[1,"login-content-container","mt-3"],[1,"grid","justify-content-between","mobile-top"],[1,"shadow-signin"],[1,"col-12","image"],["src","assets/images/new-signin.svg","alt","","srcset",""],[1,"col-12","col-md-8","col-lg-6","bg-white","header-body",2,"line-height","1.5"],[1,"mobile-only",3,"title","img"],[1,"desktop-only"],[1,"signin-heading"],[1,"signIn-content"],["autocomplete","new-password"],[1,"p-fluid","p-grid"],[3,"alreadyAddedPhoneNumber","maxLength","selectedCountryISO","preferredCountries","placeholder","phoneNumberChange","validationChange"],[3,"showForgotPassword","forgotPasswordClass","floatingLabel","feedback","passwordChange","validationChange","forgotPasswordClick"],["pButton","","type","button",1,"sign-in-btn",3,"disabled","label","click"],[1,"signin-agreement"],[3,"click"]],template:function(n,o){1&n&&(t.TgZ(0,"p-dialog",0),t.NdJ("visibleChange",function(s){return o.displayModal=s}),t.YNc(1,G,2,1,"ng-template",1),t.qZA()),2&n&&t.Q6J("visible",o.displayModal)("breakpoints",t.DdM(7,J))("dismissableMask",!0)("draggable",!1)("showHeader",!1)("modal",!0)("resizable",!1)},dependencies:[c.ez,c.O5,l.Bz,h.hJ,h.Hq,x.jx,I.T,R.j,U.C,d.aw,d.X$,T.S,T.V],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.login[_ngcontent-%COMP%]     button.back-btn{position:relative!important}.login   [_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}@media (min-width: 768px){.login   [_nghost-%COMP%]     button.back-btn{top:122px!important}}.login[_ngcontent-%COMP%]   .login-content-container[_ngcontent-%COMP%]{padding:8px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{background-color:#fff;display:flex}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{flex-direction:column;border:1px solid rgba(32,78,110,.1019607843);border-radius:8px}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .mobile-btn[_ngcontent-%COMP%]{border-radius:8px;padding:12px 24px;height:48px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{flex:1;text-align:end}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{flex:1;max-width:600px;padding:20px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding-left:3rem;padding-right:3rem;padding-top:1.5rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding:0;max-width:100%}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{color:#212121;font-size:20px;font-weight:500;margin-bottom:16px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{font-size:26px;font-weight:700;font-family:var(--medium-font)!important}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signIn-content[_ngcontent-%COMP%]{color:#443f3f;font-size:14px;font-style:normal;font-weight:400;line-height:130%;font-family:var(--regular-font)!important}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{pointer-events:none;padding:8px 0;font-size:12px!important;font-weight:400;color:#2d2d2d;line-height:20px}.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:block}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:none}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:block}}.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{height:56px;border-radius:8px;background-color:#204e6e;color:#fff;font-size:14px;font-weight:500;padding:12px 24px;display:flex;justify-content:center;align-items:center;width:100%}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin:.5rem 0}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin-bottom:.75rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]{justify-content:center!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-top[_ngcontent-%COMP%]{margin-top:20px!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-container[_ngcontent-%COMP%]{overflow:hidden;margin-top:0}}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]{gap:8px;padding:8px 0}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#272727;font-size:12px;font-style:normal;font-weight:400;line-height:normal;font-family:var(--regular-font)!important}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--main-color);font-weight:700;margin-top:1rem;margin-bottom:1rem}}.login[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:#f5f5f5;color:#323232;font-weight:700;border-radius:5px}.login[_ngcontent-%COMP%]   .p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#323232}.login[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;background-color:#e8effd;color:#204e6e;font-weight:500;font-size:14px;text-align:center;padding:12px 24px;border-radius:8px;display:block;margin:16px auto}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-size:12px;color:#272727;margin-top:16px}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;text-decoration:underline}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]{margin-top:0}}  .customClass input{height:50px!important;width:100%;border-radius:2px;opacity:1;border:none!important;border:1px solid #ccc!important;padding-left:10px;padding-right:10px;padding-top:20px;background-color:#fff!important;font-family:var(--medium-font)!important;font-size:16px}label[_ngcontent-%COMP%]{color:red;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.signin-agreement[_ngcontent-%COMP%]{color:#272727;font-family:var(--regular-font)!important}.signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;font-weight:400;font-size:12px;text-decoration:underline!important}.second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important}[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}a[_ngcontent-%COMP%]:hover{color:var(--header_bgcolor)}.new-customer-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#373636;font-size:14px;font-weight:500;text-align:center;display:flex;align-items:center;gap:6px;margin:0 6px;white-space:nowrap;font-family:var(--regular-font)!important}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{content:"";flex-grow:1;display:inline-block;width:90px;height:1px;background-color:#204e6e;opacity:.1}.new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;text-align:center;background-color:#e8effd;color:#204e6e;font-weight:500;padding:12px 24px;border-radius:8px;border:none;margin:16px 0;font-family:var(--regular-font)!important}[_nghost-%COMP%]     .contact-input-phone{background-color:#fff!important;border:1px solid #ccc!important;border-radius:4px;padding:10px;width:100%;box-sizing:border-box}[_nghost-%COMP%]     .iti__selected-flag{padding:0 6px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{content:"";position:absolute;top:15px;left:93px;width:1px;height:50%;background-color:#9ca69c;transform:translate(-50%);pointer-events:none}']})}return r})();var M=a(6223);function Y(r,u){if(1&r){const e=t.EpF();t.TgZ(0,"div",16)(1,"app-phone-input",27),t.NdJ("phoneNumberChange",function(o){t.CHM(e);const i=t.oxw();return t.KtG(i.onPhoneNumberChange(o))})("validationChange",function(o){t.CHM(e);const i=t.oxw();return t.KtG(i.onPhoneValidationChange(o))}),t.qZA()()}if(2&r){const e=t.oxw();t.xp6(1),t.Q6J("maxLength",e.phoneInputLength)("selectedCountryISO",e.CustomCountryISO)("preferredCountries",e.preferredCountries)("placeholder",e.customPlaceHolder)}}function H(r,u){if(1&r){const e=t.EpF();t.TgZ(0,"div",16)(1,"app-email-input",28),t.NdJ("emailChange",function(o){t.CHM(e);const i=t.oxw();return t.KtG(i.onEmailChange(o))})("validationChange",function(o){t.CHM(e);const i=t.oxw();return t.KtG(i.onEmailValidationChange(o))}),t.qZA()()}2&r&&(t.xp6(1),t.Q6J("emailRequired","true")("floatingLabel",!1)("labelColor","#333"))}function F(r,u){if(1&r&&(t.ynx(0),t._UZ(1,"app-register-user-modal",29),t.BQk()),2&r){const e=t.oxw();t.xp6(1),t.Q6J("displayModal",e.isRegisterModal)("phoneNumber",e.phoneNumber)}}const j=function(r){return{"register-mobile":r}};let B=(()=>{class r{otpService;translate;messageService;router;store;permissionService;appDataService;$gaService;$gtmService;customGAService;phoneNumber;email="";isPhoneValid=!1;isEmailValid=!1;selectedRegistrationMethod="phone";countryPhoneNumber="";countryPhoneCode="";phoneLength=12;phoneInputLength=12;CustomCountryISO;preferredCountries=[_.HT.Uganda,_.HT.Ghana,_.HT.C\u00f4teDIvoire];customPlaceHolder="";isMobileLayout=!1;screenWidth=window.innerWidth;isGoogleAnalytics=!1;isRegisterModal=!1;hasTrackedStartSignup=!1;get buttonLabel(){return this.screenWidth<=767?"register.next":"register.continue"}get isFormValid(){return"phone"===this.selectedRegistrationMethod?this.isPhoneValid:this.isEmailValid}constructor(e,n,o,i,s,p,b,O,v,w){this.otpService=e,this.translate=n,this.messageService=o,this.router=i,this.store=s,this.permissionService=p,this.appDataService=b,this.$gaService=O,this.$gtmService=v,this.customGAService=w,this.setupCustomPlaceholder()}ngOnInit(){if(this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.$gtmService.pushPageView("signUp"),this.isGoogleAnalytics&&(console.log("viewSignupFormEvent : ",this.isGoogleAnalytics),this.customGAService.viewSignupFormEvent("register_page")),this.CustomCountryISO=localStorage.getItem("isoCode"),this.appDataService.configuration){const e=this.appDataService.configuration.records.find(n=>"PhoneLength"===n.key);e&&(this.phoneInputLength=parseInt(e.value))}}setupCustomPlaceholder(){let e=localStorage.getItem("tenantId");e&&""!==e&&(this.customPlaceHolder={1:"XXXXXXXXX",2:"XXXXXXXXX",3:"XXXXXXXXX",4:"XXXXXXXXXX"}[e]||"")}onPhoneNumberChange(e){this.phoneNumber=e}onPhoneValidationChange(e){this.isPhoneValid=e,e&&this.isGoogleAnalytics&&!this.hasTrackedStartSignup&&(this.customGAService.startSignupEvent("Phone"),this.hasTrackedStartSignup=!0)}onEmailChange(e){this.email=e||""}onEmailValidationChange(e){this.isEmailValid=e,e&&this.isGoogleAnalytics&&!this.hasTrackedStartSignup&&(this.customGAService.startSignupEvent("Email"),this.hasTrackedStartSignup=!0)}selectRegistrationMethod(e){this.selectedRegistrationMethod=e,localStorage.setItem("registrationMethod",e),this.isPhoneValid=!1,this.isEmailValid=!1,this.hasTrackedStartSignup=!1}checkMobileExist(){this.isRegisterModal=!1,this.isGoogleAnalytics&&this.$gaService.event(C.s.CLICK_ON_SIGN_UP,"","SIGN_UP",1,!0),this.store.set("loading",!0),"phone"===this.selectedRegistrationMethod?this.handlePhoneRegistration():this.handleEmailRegistration()}handlePhoneRegistration(){this.phoneNumber?.e164Number&&(this.phoneNumber=this.phoneNumber.e164Number.slice(1)),this.isPhoneValid?this.processRegistration(this.phoneNumber):this.showValidationError("ErrorMessages.mobileRequired")}handleEmailRegistration(){this.isEmailValid&&this.email?this.processRegistration(this.email):this.showValidationError("ErrorMessages.emailRequired")}processRegistration(e){this.otpService.username=e,this.otpService.countryId="1448983B-0C38-450A-BD71-9204D181B925";const n={UserName:e,CountryId:"1448983B-0C38-450A-BD71-9204D181B925",UserRole:S.g8.consumer};("phone"===this.selectedRegistrationMethod?this.otpService.checkMobileNumber(n):this.otpService.checkEmailAddress(n)).subscribe({next:i=>{this.store.set("loading",!1),i.success?i.data.isRegistered?this.isRegisterModal=!0:(localStorage.setItem("registrationMethod",this.selectedRegistrationMethod),this.router.navigateByUrl("/register/register-otp",{state:{mobile:e}})):this.router.navigateByUrl("/login")},error:i=>{this.store.set("loading",!1),this.showError(i.message)}})}showValidationError(e){this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:this.translate.instant(e)}),this.store.set("loading",!1)}showError(e){this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:e}),this.store.set("loading",!1)}omit_special_char(e){let n;return n=e.charCode,n>47&&n<58}reloadCurrentPage(e,n){this.router.navigateByUrl("/",{skipLocationChange:!0}).then(()=>this.router.navigate(["/about-us/"],{queryParams:{pageId:e,title:n}}))}static \u0275fac=function(n){return new(n||r)(t.Y36(g.aO),t.Y36(d.sK),t.Y36(x.ez),t.Y36(l.F0),t.Y36(g.d6),t.Y36(g.$A),t.Y36(g.UW),t.Y36(P.$r),t.Y36(L.J),t.Y36(g.$V))};static \u0275cmp=t.Xpm({type:r,selectors:[["app-register"]],standalone:!0,features:[t.jDz],decls:57,vars:53,consts:[[1,"register",3,"ngClass"],[1,"content-container"],[1,"grid","justify-content-between","shadow-signin"],[1,"image","col-12","desktop-only"],["src","assets/images/registerLogo.svg","alt","","srcset",""],[1,"col-12","col-md-8","col-lg-6","bg-white","pt-6","header-body"],[1,"mobile-header","mobile-only"],["src","assets/images/signup.svg"],[1,"head-desc"],[1,"signup-heading"],[1,"signup-desc"],[1,"desktop-only"],[1,"signUp-heading"],[1,"signUp-content"],["autocomplete","new-password"],[1,"p-fluid","p-grid"],[1,"mt-3"],[1,"registration-tabs"],["type","button",1,"tab-button",3,"click"],["class","mt-3",4,"ngIf"],[1,"d-flex","flex-column","mb-5","btn-container"],["pButton","","type","button",1,"sign-up-btn",3,"disabled","label","click"],[1,"signin-agreement"],[3,"click"],[1,"new-customer-container"],["routerLink","/login","pButton","","type","button",1,"register-now","p-field","p-col-12",3,"label"],[4,"ngIf"],[3,"maxLength","selectedCountryISO","preferredCountries","placeholder","phoneNumberChange","validationChange"],[3,"emailRequired","floatingLabel","labelColor","emailChange","validationChange"],[3,"displayModal","phoneNumber"]],template:function(n,o){1&n&&(t.TgZ(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3),t._UZ(4,"img",4),t.qZA(),t.TgZ(5,"div",5)(6,"div",6),t._UZ(7,"img",7),t.TgZ(8,"div",8)(9,"h2",9),t._uU(10),t.ALo(11,"translate"),t.qZA(),t.TgZ(12,"span",10),t._uU(13),t.ALo(14,"translate"),t.qZA()()(),t.TgZ(15,"div",11)(16,"p",12),t._uU(17),t.ALo(18,"translate"),t.qZA(),t.TgZ(19,"p",13),t._uU(20),t.ALo(21,"translate"),t.qZA()(),t.TgZ(22,"form",14)(23,"div",15)(24,"div",16)(25,"div",17)(26,"button",18),t.NdJ("click",function(){return o.selectRegistrationMethod("phone")}),t._uU(27),t.ALo(28,"translate"),t.qZA(),t.TgZ(29,"button",18),t.NdJ("click",function(){return o.selectRegistrationMethod("email")}),t._uU(30),t.ALo(31,"translate"),t.qZA()()(),t.YNc(32,Y,2,4,"div",19),t.YNc(33,H,2,3,"div",19),t.TgZ(34,"div",20)(35,"button",21),t.NdJ("click",function(){return o.checkMobileExist()}),t.ALo(36,"translate"),t.qZA(),t.TgZ(37,"p",22),t._uU(38),t.ALo(39,"translate"),t.TgZ(40,"a",23),t.NdJ("click",function(){return o.reloadCurrentPage(171,"Terms and Conditions")}),t._uU(41),t.ALo(42,"translate"),t.qZA(),t._uU(43),t.ALo(44,"translate"),t.TgZ(45,"a",23),t.NdJ("click",function(){return o.reloadCurrentPage(170,"Privacy policy")}),t._uU(46),t.ALo(47,"translate"),t.qZA(),t._uU(48),t.ALo(49,"translate"),t.qZA(),t.TgZ(50,"div",24)(51,"p"),t._uU(52),t.ALo(53,"translate"),t.qZA(),t._UZ(54,"a",25),t.ALo(55,"translate"),t.qZA()()()()()()()(),t.YNc(56,F,2,2,"ng-container",26)),2&n&&(t.Q6J("ngClass",t.VKq(51,j,o.isMobileLayout&&o.screenWidth<=767)),t.xp6(10),t.Oqu(t.lcZ(11,23,"auth.registerPassword.title")),t.xp6(3),t.Oqu(t.lcZ(14,25,"auth.registerPassword.desc")),t.xp6(4),t.Oqu(t.lcZ(18,27,"register.signUp")),t.xp6(3),t.Oqu(t.lcZ(21,29,"register.content")),t.xp6(6),t.ekj("active","phone"===o.selectedRegistrationMethod),t.xp6(1),t.hij(" ",t.lcZ(28,31,"register.mobileNumber")," "),t.xp6(2),t.ekj("active","email"===o.selectedRegistrationMethod),t.xp6(1),t.hij(" ",t.lcZ(31,33,"auth.registerPassword.email")," "),t.xp6(2),t.Q6J("ngIf","phone"===o.selectedRegistrationMethod),t.xp6(1),t.Q6J("ngIf","email"===o.selectedRegistrationMethod),t.xp6(2),t.Q6J("disabled",!o.isFormValid)("label",t.lcZ(36,35,o.buttonLabel)),t.xp6(3),t.hij(" ",t.lcZ(39,37,"signIn.AgreeTermsOne")," "),t.xp6(3),t.hij(" ",t.lcZ(42,39,"signIn.AgreeTermsTwo")," "),t.xp6(2),t.hij("\xa0",t.lcZ(44,41,"signIn.AgreeTermsThree")," "),t.xp6(3),t.hij(" ",t.lcZ(47,43,"signIn.AgreeTermsFour")," "),t.xp6(2),t.hij("\xa0",t.lcZ(49,45,"signIn.AgreeTermsFive"),". "),t.xp6(4),t.Oqu(t.lcZ(53,47,"register.alreadyHaveAccount")),t.xp6(2),t.Q6J("label",t.lcZ(55,49,"register.login")),t.xp6(2),t.Q6J("ngIf",o.isRegisterModal))},dependencies:[c.ez,c.mk,c.O5,l.Bz,l.rH,d.aw,d.X$,h.hJ,h.Hq,I.T,A.q,k.p,M._Y,M.JL,M.F,V],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.register   section   [_nghost-%COMP%]     button.back-btn{position:relative!important}.content-container[_ngcontent-%COMP%]{padding:24px}.shadow-signin[_ngcontent-%COMP%]{padding:24px;border:1px solid rgba(151,151,151,.17);border-radius:7px;background-color:#fff;display:flex}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{flex:1;text-align:end}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px;border-radius:281.739px}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{flex:1;max-width:600px;padding:20px}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-heading[_ngcontent-%COMP%]{color:#212121;font-size:26px;margin-bottom:16px;font-weight:700;font-family:var(--medium-font)!important}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-content[_ngcontent-%COMP%]{font-family:var(--regular-font)!important;color:#443f3f;font-size:14px;font-style:normal;font-weight:400;line-height:130%}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;color:#323232;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.registration-tabs[_ngcontent-%COMP%]{display:flex;background-color:#e8effd;border-radius:8px;padding:4px;margin-bottom:16px}.registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]{flex:1;padding:12px 16px;border:none;background:transparent;border-radius:6px;font-size:14px;font-weight:500;color:#666;cursor:pointer;transition:all .3s ease;font-family:var(--medium-font)!important}.registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover{background-color:#204e6e1a;color:#204e6e}.registration-tabs[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]{background-color:#204e6e;color:#fff;box-shadow:0 2px 4px #204e6e33}@media screen and (max-width: 767px){.registration-tabs[_ngcontent-%COMP%]{margin-bottom:12px}.registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]{padding:10px 12px;font-size:13px}}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#373636;font-size:14px;font-weight:500;text-align:center;display:flex;align-items:center;gap:6px;margin:15px;white-space:nowrap}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{content:"";flex-grow:1;display:inline-block;width:90px;height:1px;background-color:#204e6e;opacity:.1}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{background-color:#e8effd;color:#204e6e;padding:16px;border-radius:4px;border:none;margin:16px 0;font-weight:500;font-size:14px;font-family:var(--regular-font)!important}.btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%]{height:48px;border-radius:4px;background-color:#204e6e;color:#fff;font-weight:500;margin-top:24px;padding:12px 24px;width:100%}.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-family:var(--regular-font)!important;font-size:12px;color:#272727;margin-top:16px}.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;text-decoration:underline}.mobile-only[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 767px){.mobile-only[_ngcontent-%COMP%]{display:block}}.desktop-only[_ngcontent-%COMP%]{display:block}@media screen and (max-width: 767px){.desktop-only[_ngcontent-%COMP%]{display:none}}@media screen and (min-width: 768px) and (max-width: 1024px){.content-container[_ngcontent-%COMP%]{padding:20px}.shadow-signin[_ngcontent-%COMP%]{padding:20px;flex-direction:column}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{text-align:center;margin-bottom:20px}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:200px}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{max-width:100%;padding:16px}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-heading[_ngcontent-%COMP%]{font-size:18px}.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-content[_ngcontent-%COMP%], .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{width:60px}}@media screen and (max-width: 767px){.register[_ngcontent-%COMP%]{margin-top:0}.register-mobile[_ngcontent-%COMP%]{height:100%;margin-top:0;display:flex;border:1px solid rgba(151,151,151,.17);border-radius:7px}.register-mobile[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{margin-top:50px;flex:1;display:flex;flex-direction:column;padding:16px}.register-mobile[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{background-color:#fff;padding:0;margin:0;flex-direction:column;border:1px solid rgba(32,78,110,.1019607843);border-radius:8px}.mobile-header[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding:1rem 0}.mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px;margin-right:1rem;flex-shrink:0}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;justify-content:center;flex:1}.mobile-header[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{margin:0!important;font-size:18px;font-weight:500;color:#212121;font-family:var(--medium-font)!important;line-height:1.2}.mobile-header[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#443f3f;font-family:var(--regular-font)!important;line-height:1.3}.header-body[_ngcontent-%COMP%]{padding:0!important}.btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%]{background-color:var(--primary);color:#fff;border:none;margin-bottom:1rem;height:44px;font-size:14px}.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-size:11px;line-height:1.4;text-align:center}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;margin:12px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{width:40px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{padding:12px;font-size:13px;margin:12px 0}}@media screen and (max-width: 480px){.content-container[_ngcontent-%COMP%]{padding:12px}.register-mobile[_ngcontent-%COMP%]{border:1px solid rgba(151,151,151,.17);border-radius:7px}.register-mobile[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{margin-top:50px;padding:12px}.mobile-header[_ngcontent-%COMP%]{padding:.8rem 0}.mobile-header[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{font-size:16px}.mobile-header[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%]{font-size:11px}.btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%]{height:42px;font-size:13px}.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-size:10px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:11px;margin:10px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{width:20px}.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{padding:10px;font-size:12px}}[_nghost-%COMP%]     .contact-input-phone{background-color:#fff!important;border:1px solid #ccc!important;border-radius:4px;padding:10px;width:100%;box-sizing:border-box}[_nghost-%COMP%]     .iti__selected-flag{padding:0 6px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{content:"";position:absolute;top:15px;left:93px;width:1px;height:50%;background-color:#9ca69c;transform:translate(-50%);pointer-events:none}[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}@media screen and (max-width: 767px){[_nghost-%COMP%]     .contact-input-phone{padding:12px 10px;font-size:14px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{top:13px;left:85px}}@media screen and (max-width: 480px){[_nghost-%COMP%]     .contact-input-phone{padding:10px 8px;font-size:13px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{left:91px}}']})}return r})()}}]);