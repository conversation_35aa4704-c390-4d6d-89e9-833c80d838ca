<div class="otp-container">
  <form class="form-container">
    <div class="otp-inputs">
      <!-- Hidden input for SMS auto-fill -->
      <input
        #hiddenInput
        type="text"
        class="hidden-otp-input"
        [value]="value"
        (input)="onAutoFillInput($event)"
        autocomplete="one-time-code"
        inputmode="numeric"
        pattern="[0-9]*"
        maxlength="4"
        aria-hidden="true"
      />
      
      <!-- Visible individual inputs -->
      <input
        #otpInput
        type="text"
        class="otp-digit-input"
        maxlength="1"
        [value]="digits[0]"
        (input)="onDigitInput(0, $event)"
        (keydown)="onKeyDown(0, $event)"
        (paste)="onPaste($event)"
        (focus)="onFocus(0)"
        inputmode="numeric"
        pattern="[0-9]*"
        aria-label="OTP Digit 1"
        placeholder="_"
      />
      <input
        #otpInput
        type="text"
        class="otp-digit-input"
        maxlength="1"
        [value]="digits[1]"
        (input)="onDigitInput(1, $event)"
        (keydown)="onKeyDown(1, $event)"
        (paste)="onPaste($event)"
        (focus)="onFocus(1)"
        inputmode="numeric"
        pattern="[0-9]*"
        aria-label="OTP Digit 2"
        placeholder="_"
      />
      <input
        #otpInput
        type="text"
        class="otp-digit-input"
        maxlength="1"
        [value]="digits[2]"
        (input)="onDigitInput(2, $event)"
        (keydown)="onKeyDown(2, $event)"
        (paste)="onPaste($event)"
        (focus)="onFocus(2)"
        inputmode="numeric"
        pattern="[0-9]*"
        aria-label="OTP Digit 3"
        placeholder="_"
      />
      <input
        #otpInput
        type="text"
        class="otp-digit-input"
        maxlength="1"
        [value]="digits[3]"
        (input)="onDigitInput(3, $event)"
        (keydown)="onKeyDown(3, $event)"
        (paste)="onPaste($event)"
        (focus)="onFocus(3)"
        inputmode="numeric"
        pattern="[0-9]*"
        aria-label="OTP Digit 4"
        placeholder="_"
      />
    </div>
  </form>
  
  <div class="count">
    <button 
      type="button"
      style="background: white; border: none; cursor: pointer" 
      class="resend" 
      *ngIf="isTimerExpired"
      (click)="onResendClick()">
      {{ "auth.otp.resend" | translate }}
    </button>
    <span class="time-left" *ngIf="!isTimerExpired"> 
      {{ "auth.otp.resendIn" | translate }} 0:{{ timeLeftDisplay }}
    </span>
  </div>
</div>