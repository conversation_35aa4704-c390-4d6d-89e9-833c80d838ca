{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport UtilityFunctions from '@core/utilities/functions';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/gtm.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../../shared/components/product-card/product-card.component\";\nimport * as i10 from \"primeng/progressspinner\";\nimport * as i11 from \"primeng/breadcrumb\";\nimport * as i12 from \"@pages/category-products/components/category-not-found/category-not-found.component\";\nfunction IndexComponent_ng_container_0_section_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r5.getBannerImages(ctx_r5.categoryBanner == null ? null : ctx_r5.categoryBanner.desktopBanner), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_5_ng_container_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵelement(1, \"app-mtn-product-card\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"isLayoutTemplate\", !ctx_r11.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"currency\", ctx_r11.currency)(\"product\", product_r10)(\"categoryName\", ctx_r11.categoryName);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_0_section_1_div_4_div_5_ng_container_1_a_1_Template, 2, 5, \"a\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r10.isDisable);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_0_section_1_div_4_div_5_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.products);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, IndexComponent_ng_container_0_section_1_div_4_div_5_Template, 2, 1, \"div\", 13);\n    i0.ɵɵtemplate(6, IndexComponent_ng_container_0_section_1_div_4_div_6_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.category ? ctx_r6.category.categoryName : ctx_r6.categoryName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.products && ctx_r6.products.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.showProductSpinner);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"category-products-page\": a0,\n    \"hidden-navbar\": a1\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"breadcrumb\": a0,\n    \"hiddenNavbarBreadcrum\": a1\n  };\n};\nfunction IndexComponent_ng_container_0_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 4)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"p-breadcrumb\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, IndexComponent_ng_container_0_section_1_div_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵtemplate(4, IndexComponent_ng_container_0_section_1_div_4_Template, 7, 3, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c0, ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive, !(ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive, !(ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"home\", ctx_r3.home)(\"model\", ctx_r3.breadItems);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.categoryBanner == null ? null : ctx_r3.categoryBanner.isBanner) && (ctx_r3.categoryBanner == null ? null : ctx_r3.categoryBanner.desktopBanner));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.products && ctx_r3.products.length);\n  }\n}\nfunction IndexComponent_ng_container_0_app_category_not_found_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-category-not-found\");\n  }\n}\nfunction IndexComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_0_section_1_Template, 5, 12, \"section\", 2);\n    i0.ɵɵtemplate(2, IndexComponent_ng_container_0_app_category_not_found_2_Template, 1, 0, \"app-category-not-found\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isBlank);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isBlank && ctx_r0.products && ctx_r0.products.length === 0);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r15.getBannerImages(ctx_r15.categoryBanner == null ? null : ctx_r15.categoryBanner.mobileBanner), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_9_ng_container_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵelement(1, \"app-mtn-product-card\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r20 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"isLayoutTemplate\", !ctx_r21.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"currency\", ctx_r21.currency)(\"product\", product_r20)(\"categoryName\", ctx_r21.categoryName);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_section_0_div_2_div_9_ng_container_1_a_1_Template, 2, 5, \"a\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const product_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r20.isDisable);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_section_0_div_2_div_9_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.products);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 23)(3, \"div\", 24)(4, \"div\", 25)(5, \"div\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 27);\n    i0.ɵɵelement(8, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, IndexComponent_ng_template_1_section_0_div_2_div_9_Template, 2, 1, \"div\", 29);\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_1_section_0_div_2_div_10_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.category ? ctx_r16.category.categoryName : ctx_r16.categoryName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.products && ctx_r16.products.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.showProductSpinner);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"margin-top\": a0\n  };\n};\nfunction IndexComponent_ng_template_1_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 22);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_section_0_div_1_Template, 2, 1, \"div\", 7);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_1_section_0_div_2_Template, 11, 3, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(3, _c2, ctx_r13.isMobileLayout ? \"65px\" : \"210px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r13.categoryBanner == null ? null : ctx_r13.categoryBanner.isBanner) && (ctx_r13.categoryBanner == null ? null : ctx_r13.categoryBanner.mobileBanner));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.products && ctx_r13.products.length);\n  }\n}\nfunction IndexComponent_ng_template_1_app_category_not_found_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-category-not-found\");\n  }\n}\nfunction IndexComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_1_section_0_Template, 3, 5, \"section\", 21);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_app_category_not_found_1_Template, 1, 0, \"app-category-not-found\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isBlank);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isBlank && ctx_r2.products && ctx_r2.products.length === 0);\n  }\n}\nexport class IndexComponent {\n  activatedRoute;\n  productService;\n  store;\n  reviewsService;\n  messageService;\n  translate;\n  ref;\n  loaderService;\n  tenantService;\n  authTokenService;\n  cookieService;\n  mainDataService;\n  router;\n  appDataService;\n  permissionService;\n  $gaService;\n  platformId;\n  $gtmService;\n  categoryId;\n  topNumber;\n  category;\n  categoryName;\n  items = [];\n  breadItems = [];\n  home = {\n    icon: 'pi pi-home',\n    routerLink: '/'\n  };\n  currency = {};\n  baseUrl = environment.apiEndPoint + '/';\n  emptyMsg = 'Your Category Is Empty';\n  subPath;\n  subId;\n  catIds = '';\n  catPaths = '';\n  products;\n  isError = false;\n  isBlank = true;\n  reviews;\n  badgesList = [];\n  rawCategories = [];\n  newCategory = null;\n  token;\n  pageSize = 50;\n  currentPageSize = 50;\n  triggerProductsCall = false;\n  showProductSpinner = false;\n  loadDataType = '';\n  currentPageNumber = 1;\n  total = 0;\n  ignorePagination = false;\n  shouldCallNextFeatureProduct = true;\n  shouldCallNextCategoryProduct = true;\n  navbarData;\n  isLayoutTemplate = false;\n  promotionId;\n  promotionName;\n  screenWidth = window.innerWidth;\n  isMobileView = this.screenWidth <= 786;\n  isGoogleAnalytics = false;\n  userDetails;\n  tagName = GaActionEnum;\n  isMobileLayout = false;\n  categoryBanner;\n  onScroll(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      const scrollPosition = window.scrollY || document.documentElement.scrollTop;\n      const totalHeight = document.documentElement.scrollHeight;\n      const windowHeight = window.innerHeight;\n      const isAtBottom = scrollPosition + windowHeight >= totalHeight - 1;\n      if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {\n        this.loadOnScrollData();\n      }\n    }\n  }\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n    if (this.screenWidth <= 768) {\n      this.isMobileView = true;\n    } else {\n      this.isMobileView = false;\n    }\n  }\n  loadOnScrollData() {\n    if (this.loadDataType === 'category') {\n      if (this.shouldCallNextCategoryProduct) this.loadPaginatedProducts();\n    } else if (this.loadDataType === 'feature') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedFeatureProducts();\n    } else if (this.loadDataType === 'promotion') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedPromotionProducts();\n    }\n  }\n  constructor(activatedRoute, productService, store, reviewsService, messageService, translate, ref, loaderService, tenantService, authTokenService, cookieService, mainDataService, router, appDataService, permissionService, $gaService, platformId, $gtmService) {\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.store = store;\n    this.reviewsService = reviewsService;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.tenantService = tenantService;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.mainDataService = mainDataService;\n    this.router = router;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.$gtmService = $gtmService;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.scrollToTop();\n  }\n  ngOnInit() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.paramfunc();\n    this.scrollToTop();\n  }\n  paramfunc() {\n    let param;\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.products = [];\n      param = params.get('id');\n      param = param?.split('&');\n      this.userDetails = this.store.get('profile');\n      if (this.router.url.includes('promotion')) {\n        if (param?.length == 1) {\n          this.promotionId = param[0];\n          this.loadDataType = 'promotion';\n          this.triggerAnalytics('promotion', this.promotionId);\n          this.loadPromotionData();\n          // this.$gtmService.pushPageView('promotion',this.categoryName)\n        }\n      } else {\n        if (param?.length == 1) {\n          this.categoryId = param[0];\n          this.loadDataType = 'category';\n          this.triggerAnalytics('category', this.categoryId);\n          // this.$gtmService.pushPageView('category',this.categoryName)\n          this.loadData();\n        } else if (param?.length == 3) {\n          this.categoryId = param[0];\n          this.topNumber = param[1];\n          this.categoryName = param[2];\n          this.loadDataType = 'feature';\n          this.triggerAnalytics('feature', this.categoryId);\n          this.loadSectionData();\n          this.$gtmService.pushPageView('feature', this.categoryName);\n        }\n      }\n    });\n  }\n  loadData() {\n    this.loaderService.show();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, true, true).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res.success) {\n          this.isError = false;\n          this.total = res.data.productsList.records.length;\n          this.categoryBanner = res.data.categoryBanner;\n          res.data?.productsList?.records.forEach(record => {\n            this.addProductFromLoadData(record);\n            this.badgesList = record.badgesList[record]?.desktopImage || [];\n          });\n          this.getAllCategories();\n        }\n        this.loaderService.hide();\n      },\n      error: err => {\n        this.handleError(err);\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.activatedRoute.queryParams.subscribe(res => {\n      this.subPath = res?.path?.split('//');\n      this.subId = res?.id?.split('//');\n    });\n    if (!this.subPath) {\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          res.forEach(element => {\n            if (element.id == this.categoryId) {\n              this.category = element;\n            }\n          });\n          this.activatedRoute.queryParamMap.subscribe(params => {\n            this.categoryName = params.get('categoryName');\n          });\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.items = [{\n        label: this.category?.categoryName\n      }, {\n        label: label\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    } else {\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.categoryName = this.subPath[this.subPath.length - 1];\n      this.items = [];\n      this.subPath.forEach((x, index) => {\n        this.items.push({\n          label: x,\n          id: this.subId[index]\n        });\n      });\n      this.items.forEach((val, index) => {\n        this.catPaths = index == 0 ? val.label : this.catPaths + '//' + val.label;\n        this.catIds = index == 0 ? val.id : this.catIds + '//' + String(val.id);\n        val.routerLink = `/category/${val.id}`;\n        val.queryParams = {\n          path: this.catPaths,\n          id: this.catIds\n        };\n      });\n      this.items.push({\n        label: label\n      });\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    }\n  }\n  loadPromotionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        this.categoryName = res.data.promotionName;\n        this.breadItems = [{\n          label: this.categoryName\n        }];\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  handleError(err) {\n    this.isBlank = false;\n    this.isError = true;\n    this.loaderService.hide();\n  }\n  addProductFromLoadData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      badges: record.badgesList,\n      productId: record?.id,\n      productName: record?.name,\n      isLiked: record?.isLiked,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      priceId: selectedVariance?.priceId,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      specProductId: selectedVariance.specProductId,\n      channelId: record.channelId ?? '1',\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      shopId: record.shopId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated,\n      isDisable: record.isDisable,\n      badgesList: record?.badgesList\n    };\n    this.products.push(product);\n  }\n  loadSectionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.breadItems = [{\n      label: this.categoryName\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  addProductFromLoadSectionData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    if (selectedVariance) {\n      let features = [];\n      if (selectedVariance?.productFeaturesList) {\n        features = selectedVariance?.productFeaturesList[0]?.featureList;\n      }\n      let product = {\n        badges: record.badgesList,\n        productId: record?.id,\n        productName: record?.name,\n        isLiked: record?.isLiked,\n        priceValue: selectedVariance?.price,\n        priceId: selectedVariance?.priceId,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: record?.currencyCode,\n        masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: record.shopId,\n        specProductId: selectedVariance.specProductId,\n        channelId: record.channelId ?? '1',\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        stockStatus: selectedVariance.stockStatus,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated,\n        badgesList: record?.badgesList\n      };\n      if (product.salePriceValue) {\n        product.salePercent = 100 - product.salePriceValue / product.priceValue * 100;\n      }\n      this.products.push(product);\n    }\n  }\n  fetchCategories(category, cat) {\n    if (category.length == 0) {\n      return;\n    }\n    for (const element of category) {\n      if (element.id == this.categoryId) {\n        this.assignBreadCrumbsData(element);\n      }\n      this.fetchCategories(element.categories, element);\n    }\n  }\n  assignBreadCrumbsData(category) {\n    let idsArray = category?.categoryIds?.split(\"->\")?.map(Number);\n    let nameArray = category?.categoryPath?.split(\"->\")?.map(String);\n    let breadCrumbs = [];\n    if (idsArray.length === nameArray.length) {\n      idsArray?.map((e, i) => {\n        breadCrumbs.push({\n          routerLink: '/category/' + e.toString(),\n          label: nameArray[i]\n        });\n      });\n      this.breadItems = breadCrumbs;\n      this.ref.detectChanges();\n      this.ref.markForCheck();\n    }\n    // this.$gtmService.pushPageView(category?.categoryPath)\n    this.$gtmService.pushPageView('category', category?.categoryPath);\n  }\n  logOut() {\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('profile', '');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    this.store.set('cartProducts', []);\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n  }\n  loadPaginatedProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber).subscribe({\n      next: res => {\n        this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;\n        const lastPageSizeProducts = res.data.productsList.records;\n        this.total = lastPageSizeProducts.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadData(record);\n        });\n        this.triggerProductsCall = !res.data.productsList.records.length;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  addProduct(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      productId: record?.id,\n      productName: record?.name,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      channelId: record?.channelId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated\n    };\n    this.products.push(product);\n  }\n  loadPaginatedFeatureProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadSectionData(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  loadPaginatedPromotionProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProduct(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  getAllCategories() {\n    let allCategories = localStorage.getItem('allCategories');\n    allCategories = JSON.parse(allCategories);\n    this.rawCategories = allCategories;\n    this.rawCategories?.forEach(cat => {\n      if (cat.id == this.categoryId) {\n        this.assignBreadCrumbsData(cat);\n      }\n      cat['path'] = cat.categoryName;\n      cat['catIds'] = cat.id;\n      this.fetchCategories(cat.categories, cat);\n    });\n  }\n  scrollToTop() {\n    if (isPlatformBrowser(this.platformId)) {\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }\n  }\n  triggerAnalytics(type, id) {\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')) {\n      if (type === 'promotion') {\n        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);\n      } else if (type === 'category') {\n        this.$gaService.pageView('/category', 'Category ID: ' + id);\n      } else if (type === 'feature') {\n        this.$gaService.pageView('/category', 'Feature ID: ' + id);\n      }\n      this.$gaService.event(this.tagName.SEARCH, id, type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST', 1, true, {\n        \"id\": id,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n  }\n  getBannerImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.ReviewsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.LoaderService), i0.ɵɵdirectiveInject(i2.TenantService), i0.ɵɵdirectiveInject(i2.AuthTokenService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AppDataService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i7.GTMService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-category-products\"]],\n    hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function IndexComponent_scroll_HostBindingHandler($event) {\n          return ctx.onScroll($event);\n        }, false, i0.ɵɵresolveWindow)(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      products: \"products\"\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"mobileView\", \"\"], [\"class\", \"category-products-page\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"category-products-page\", 3, \"ngClass\"], [1, \"breadcrumb\", \"m-0\", 3, \"ngClass\"], [3, \"home\", \"model\"], [\"class\", \"category-banner\", 4, \"ngIf\"], [1, \"category-banner\"], [1, \"category-banner__img\", 3, \"src\"], [1, \"\"], [1, \"col-12\", \"col-md-6\", \"flex\"], [1, \"font-size-22\", \"bold-font\"], [\"class\", \"title-category flex flex-row flex-wrap \", 4, \"ngIf\"], [\"class\", \"spinner-product\", 4, \"ngIf\"], [1, \"title-category\", \"flex\", \"flex-row\", \"flex-wrap\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"slide mt-2 mx-1 md:mx-md-2 lg:mx-lg-2 products-margin\", 3, \"isLayoutTemplate\", 4, \"ngIf\"], [1, \"slide\", \"mt-2\", \"mx-1\", \"md:mx-md-2\", \"lg:mx-lg-2\", \"products-margin\"], [3, \"currency\", \"product\", \"categoryName\"], [1, \"spinner-product\"], [\"class\", \"category-products-page\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"category-products-page\", 3, \"ngStyle\"], [1, \"container\", 2, \"padding-top\", \"16px\"], [1, \"row\", 2, \"padding\", \"0px 15px\"], [1, \"col-9\", 2, \"padding-left\", \"0px !important\"], [1, \"font-size-22\", \"font-bold\"], [1, \"col-3\", 2, \"padding-right\", \"0px !important\"], [2, \"float\", \"inline-end\"], [\"class\", \"row mobile-card-div\", 4, \"ngIf\"], [1, \"row\", \"mobile-card-div\"], [\"class\", \"slide mt-2 col-sm-6\", \"style\", \"padding: 0;\", 3, \"isLayoutTemplate\", 4, \"ngIf\"], [1, \"slide\", \"mt-2\", \"col-sm-6\", 2, \"padding\", \"0\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IndexComponent_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, IndexComponent_ng_template_1_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isMobileView)(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i9.ProductCardComponent, i10.ProgressSpinner, i11.Breadcrumb, i12.CategoryNotFoundComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  width: var(--product-card-width);\\n}\\n@media only screen and (max-width: 767px) {\\n  a[_ngcontent-%COMP%] {\\n    --product-card-width: 43%;\\n    margin: 8px;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 36% !important;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  a[_ngcontent-%COMP%] {\\n    --product-card-width: 18%;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 11% !important;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  a[_ngcontent-%COMP%] {\\n    width: 18%;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 11% !important;\\n  }\\n}\\n\\n.showProducts[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n}\\n\\n.font-bold[_ngcontent-%COMP%] {\\n  font-family: \\\"main-regular\\\";\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 24px; \\n\\n  text-transform: uppercase;\\n}\\n\\n.mobile-card-div[_ngcontent-%COMP%] {\\n  justify-content: left;\\n  margin: 0 15px;\\n  width: 100%;\\n}\\n\\n@media screen and (min-width: 769px) {\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    padding-left: 11rem !important;\\n    padding-right: 8rem !important;\\n    margin-top: 0px !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .breadcrumb[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .col-12.md\\\\:col-6.flex.justify-content-center.md\\\\:justify-content-start[_ngcontent-%COMP%] {\\n    justify-content: left !important;\\n  }\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n}\\n.products-margin[_ngcontent-%COMP%] {\\n  padding: 0px 0px 16px 0px !important;\\n}\\n\\n.title-category[_ngcontent-%COMP%] {\\n  margin-top: 5px !important;\\n  margin-bottom: 1rem !important;\\n}\\n\\n@media screen and (min-width: 768px) {\\n  .category-banner[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n}\\n.category-banner__img[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}