{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/button\";\nexport class ConfirmationDialogComponent {\n  dialogService;\n  previousref;\n  config;\n  translate;\n  router;\n  successMsg = '';\n  constructor(dialogService, previousref, config, translate, router) {\n    this.dialogService = dialogService;\n    this.previousref = previousref;\n    this.config = config;\n    this.translate = translate;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.successMsg = this.config?.data?.successMsg;\n  }\n  closeDialog() {\n    this.previousref.close();\n  }\n  static ɵfac = function ConfirmationDialogComponent_Factory(t) {\n    return new (t || ConfirmationDialogComponent)(i0.ɵɵdirectiveInject(i1.DialogService), i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDialogComponent,\n    selectors: [[\"app-confirmation-dialog\"]],\n    decls: 8,\n    vars: 6,\n    consts: [[1, \"confirm-dialog\"], [1, \"d-flex\", \"flex-column\", \"px-4\"], [\"src\", \"assets/images/success-avatar.svg\", \"alt\", \"Success\", 1, \"success-avatar\"], [1, \"subtitle\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"ok-btn\", \"second-btn\", 3, \"label\", \"click\"]],\n    template: function ConfirmationDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"img\", 2);\n        i0.ɵɵelementStart(3, \"p\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ConfirmationDialogComponent_Template_button_click_6_listener() {\n          return ctx.closeDialog();\n        });\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, ctx.successMsg));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(7, 4, \"auth.registerPassword.okButton\"));\n      }\n    },\n    dependencies: [i4.ButtonDirective, i2.TranslatePipe],\n    styles: [\".confirm-dialog[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  min-height: 200px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: var(--neutral-dark-2, #2F3036);\\n  text-align: center;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 1.4;\\n  margin: 20px 0;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: flex-end;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background-color: var(--light-blue);\\n  border-radius: 100px;\\n  border: none;\\n  outline: none;\\n  cursor: pointer;\\n  padding: 8px;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .success-avatar[_ngcontent-%COMP%] {\\n  height: 120px;\\n  width: auto;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .ok-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  border-radius: 8px;\\n  margin-top: 20px;\\n  padding: 12px 24px;\\n  min-height: 44px;\\n  width: 100%;\\n}\\n.confirm-dialog[_ngcontent-%COMP%]   .d-flex.flex-column.px-4[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}