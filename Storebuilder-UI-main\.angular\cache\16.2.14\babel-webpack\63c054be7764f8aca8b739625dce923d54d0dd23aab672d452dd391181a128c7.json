{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-cookie-service\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"primeng/sidebar\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ngx-translate/core\";\nfunction SideMenuComponent_ng_template_2_Template(rf, ctx) {}\nconst _c0 = function (a0) {\n  return {\n    \"pointer-events\": a0\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    categoryName: a0\n  };\n};\nfunction SideMenuComponent_ng_template_3_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵlistener(\"mouseover\", function SideMenuComponent_ng_template_3_ng_container_1_div_1_Template_div_mouseover_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const category_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      ctx_r7.showSubCategory = true;\n      return i0.ɵɵresetView(ctx_r7.subMenuCategories = category_r5.categories);\n    });\n    i0.ɵɵelementStart(1, \"span\", 12);\n    i0.ɵɵlistener(\"click\", function SideMenuComponent_ng_template_3_ng_container_1_div_1_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const category_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.closeMenu(true, category_r5.categoryName));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, !category_r5.parentChildCount ? \"none\" : \"\"))(\"routerLink\", \"/category/\" + category_r5.id)(\"queryParams\", i0.ɵɵpureFunction1(6, _c1, category_r5.categoryName));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(category_r5.categoryName);\n  }\n}\nfunction SideMenuComponent_ng_template_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SideMenuComponent_ng_template_3_ng_container_1_div_1_Template, 3, 8, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(category_r5 == null ? null : category_r5.hide));\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_div_2_h2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"h2\", 21);\n    i0.ɵɵlistener(\"click\", function SideMenuComponent_ng_template_3_div_2_div_2_h2_1_Template_h2_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const category_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.closeMenu(true, category_r14.categoryName));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", \"/category/\" + category_r14.id)(\"queryParams\", i0.ɵɵpureFunction1(4, _c1, category_r14.categoryName))(\"ngStyle\", i0.ɵɵpureFunction1(6, _c0, !category_r14.parentChildCount ? \"none\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", category_r14.categoryName, \"\");\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_div_2_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵlistener(\"click\", function SideMenuComponent_ng_template_3_div_2_div_2_div_3_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const subCategory_r22 = i0.ɵɵnextContext().$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r25.closeMenu(true, subCategory_r22.categoryName));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subCategory_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, !subCategory_r22.parentChildCount ? \"none\" : \"\"))(\"routerLink\", \"/category/\" + subCategory_r22.id)(\"queryParams\", i0.ɵɵpureFunction1(6, _c1, subCategory_r22.categoryName));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", subCategory_r22.categoryName, \" \");\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, SideMenuComponent_ng_template_3_div_2_div_2_div_3_span_2_Template, 2, 8, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subCategory_r22 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !(subCategory_r22 == null ? null : subCategory_r22.hide));\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2, \"See all\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"em\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, SideMenuComponent_ng_template_3_div_2_div_2_h2_1_Template, 2, 8, \"h2\", 17);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵtemplate(3, SideMenuComponent_ng_template_3_div_2_div_2_div_3_Template, 3, 1, \"div\", 19);\n    i0.ɵɵtemplate(4, SideMenuComponent_ng_template_3_div_2_div_2_div_4_Template, 4, 0, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r14 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(category_r14 == null ? null : category_r14.hide));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r14.categories);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", false);\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtemplate(2, SideMenuComponent_ng_template_3_div_2_div_2_Template, 5, 3, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.subMenuCategories);\n  }\n}\nfunction SideMenuComponent_ng_template_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"hr\", 29);\n    i0.ɵɵelementStart(2, \"a\", 30);\n    i0.ɵɵlistener(\"click\", function SideMenuComponent_ng_template_3_div_3_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.closeMenu(true));\n    });\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, \"sideMenu.allMerchants\"));\n  }\n}\nfunction SideMenuComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, SideMenuComponent_ng_template_3_ng_container_1_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, SideMenuComponent_ng_template_3_div_2_Template, 3, 1, \"div\", 8);\n    i0.ɵɵtemplate(3, SideMenuComponent_ng_template_3_div_3_Template, 6, 3, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSubCategory && ctx_r1.subMenuCategories.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.tenantId != \"3\" && ctx_r1.isShowAllMerchants);\n  }\n}\nconst _c2 = [\"*\"];\nexport class SideMenuComponent {\n  primengConfig;\n  store;\n  router;\n  mainDataService;\n  authTokenService;\n  cookieService;\n  appDataService;\n  cd;\n  $gaService;\n  platformId;\n  isShowAll = environment.isStoreCloud;\n  display = false;\n  categories = [];\n  mobileCategories = [];\n  mobileCategoriesStack = [];\n  lang;\n  loggedIn = false;\n  items = [];\n  authToken = '';\n  subCategories = [];\n  subCategories2 = [];\n  subCategories3 = [];\n  subCatNames = ['', '', ''];\n  tenantId = '1';\n  screenWidth;\n  showSubCategory = false;\n  subMenuCategories = [];\n  isShowAllMerchants = false;\n  isGoogleAnalytics = false;\n  userDetails;\n  ipAddress = '';\n  constructor(primengConfig, store, router, mainDataService, authTokenService, cookieService, appDataService, cd, $gaService, platformId) {\n    this.primengConfig = primengConfig;\n    this.store = store;\n    this.router = router;\n    this.mainDataService = mainDataService;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.appDataService = appDataService;\n    this.cd = cd;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.tenantId = localStorage.getItem('tenantId') ?? '1';\n  }\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n    // this.getCategories();\n    const allMerchants = this.appDataService.layoutTemplate.find(t => t.type === 'all_merchants');\n    this.isShowAllMerchants = allMerchants ? allMerchants.isActive : false;\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n      this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n      if (!this.authToken) {\n        this.authToken = this.cookieService.get('authToken');\n      }\n      if (this.authToken) this.loggedIn = true;else this.loggedIn = false;\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          this.categories = this.sortByOrder(res);\n          // Implementation changes as per Omer Raffique 23/5/2024\n          // hidden category with products count in any level should be shown\n          for (let cat of this.categories) {\n            cat['path'] = cat.categoryName;\n            cat['catIds'] = cat.id;\n            cat['parentChildCount'] = cat.totalProductCount;\n            this.fetchCategoriesCount(this.sortByOrder(cat.categories), cat);\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }, 1);\n  }\n  sortByOrder(arr) {\n    return arr.sort((a, b) => a.order - b.order);\n  }\n  fetchCategoriesCount(category, cat) {\n    if (category.length == 0) {\n      return;\n    }\n    // Helper function to check if category has products in any level\n    const hasProductsInAnyLevel = cat => {\n      // If current category has products, return true\n      if (cat.totalProductCount > 0) {\n        return true;\n      }\n      // If no direct products, check children recursively\n      if (cat.categories && cat.categories.length > 0) {\n        return cat.categories.some(child => hasProductsInAnyLevel(child));\n      }\n      return false;\n    };\n    // Filter categories that have products at any level (current or children)\n    const filteredCategories = category.filter(item => hasProductsInAnyLevel(item));\n    for (const item of filteredCategories) {\n      item['path'] = cat.path + '//' + item.categoryName;\n      item['catIds'] = cat.catIds + '//' + item.id;\n      item['parentChildCount'] = item.totalProductCount;\n      // Recursively call for subcategories\n      this.fetchCategoriesCount(item.categories, item);\n      cat['parentChildCount'] += item['parentChildCount'];\n      if (cat.hide && cat.parentChildCount > 0) {\n        cat.hide = false;\n      }\n    }\n    // Update the categories array to only include filtered categories\n    cat.categories = filteredCategories;\n  }\n  fetchCategories(category, cat) {\n    if (category.length == 0) {\n      return;\n    }\n    for (const item of category) {\n      item['path'] = cat.path + '//' + item.categoryName;\n      item['catIds'] = cat.catIds + '//' + item.id;\n      this.fetchCategories(item.categories, item);\n    }\n  }\n  openMenu() {\n    this.display = true;\n    this.getUserDetails();\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_BURGER_MENU, '', 'MENU_NAVIGATION', 1, true, {\n      \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n      \"ipAddress\": this.store.get('userIP'),\n      \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n      \"device_Id\": this.store.get('deviceInfo')?.deviceId\n    });\n  }\n  getUserDetails() {\n    this.userDetails = this.store.getFromLocalStorage('profile');\n  }\n  closeMenu(setGoogleAnalytics = false, categoryName) {\n    this.display = false;\n    this.subCategories = [];\n    this.subCategories2 = [];\n    this.subCategories3 = [];\n    this.mobileCategories = this.categories;\n    this.mobileCategoriesStack = [];\n    this.showSubCategory = false;\n    if (setGoogleAnalytics) {\n      this.getUserDetails();\n      if (categoryName) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true, {\n          categorySelected: categoryName\n        });\n      } else {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_ALL_MERCHANTS_LINK, 'navigation', 'ALL_MERCHANTS_PAGE', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId\n        });\n      }\n    }\n  }\n  logOut() {\n    this.authToken = null;\n    this.loggedIn = false;\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet(\"\");\n    this.cookieService.delete('authToken', '/');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/']);\n  }\n  removeHidedCategories(category) {\n    if (category.length) {\n      return;\n    }\n    for (let cat of category) {\n      const filteredCategores = cat.categories.filter(item => !item.hide);\n      if (filteredCategores.length) {\n        this.removeHidedCategories(cat.categories);\n      } else {\n        cat.categories = [];\n      }\n    }\n  }\n  getCategories() {\n    const categories = this.sortByOrder(this.appDataService.categories);\n    categories.records.forEach(item => {\n      const filteredCategores = item.categories.filter(item => !item.hide);\n      if (filteredCategores.length) {\n        this.removeHidedCategories(this.sortByOrder(item.categories));\n      } else {\n        item.categories = [];\n      }\n    });\n    categories.records.forEach(cat => {\n      cat['path'] = '';\n      cat['catIds'] = '';\n      this.fetchCategories(this.sortByOrder(cat.categories), cat);\n    });\n    categories.records.forEach(category => {\n      if (category.categories.length > 0) {\n        const subCategories = [];\n        this.sortByOrder(category.categories).forEach(subCat => {\n          subCategories.push({\n            label: subCat.categoryName\n          });\n        });\n        this.items.push({\n          label: category.categoryName,\n          items: subCategories\n        });\n      } else {\n        this.items.push({\n          label: category.categoryName\n        });\n      }\n    });\n    this.mobileCategories = categories.records;\n  }\n  assignSubCategories(subCat, catName) {\n    this.subCategories = subCat;\n    this.subCategories2 = [];\n    this.subCatNames[0] = catName;\n  }\n  assignSubCategories2(subCat, catName) {\n    this.subCategories2 = subCat;\n    this.subCategories3 = [];\n    this.subCatNames[1] = catName;\n  }\n  assignSubCategories3(subCat, catName) {\n    this.subCategories3 = subCat;\n    this.subCatNames[2] = catName;\n  }\n  selectMobileCategories(category) {\n    this.mobileCategories = category.categories;\n    this.mobileCategoriesStack.push(category);\n  }\n  setPreviousMobileCategories() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.mobileCategories = [];\n      yield _this.mobileCategoriesStack.pop();\n      if (_this.mobileCategoriesStack.length > 0) {\n        _this.mobileCategories = _this.mobileCategoriesStack[_this.mobileCategoriesStack.length - 1].categories;\n      } else {\n        _this.mobileCategories = _this.categories;\n      }\n    })();\n  }\n  static ɵfac = function SideMenuComponent_Factory(t) {\n    return new (t || SideMenuComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i2.AuthTokenService), i0.ɵɵdirectiveInject(i4.CookieService), i0.ɵɵdirectiveInject(i2.AppDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SideMenuComponent,\n    selectors: [[\"app-mtn-side-menu\"]],\n    hostBindings: function SideMenuComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function SideMenuComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    ngContentSelectors: _c2,\n    decls: 7,\n    vars: 3,\n    consts: [[1, \"side-menu\", \"cursor-auto\"], [3, \"visible\", \"baseZIndex\", \"position\", \"onHide\", \"visibleChange\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [1, \"pi\", \"pi-bars\", \"pi-white\", \"cursor-pointer\", 2, \"display\", \"flex\", \"margin-top\", \"2px\", 3, \"click\"], [1, \"ml-2\", \"mr-1\", \"pi-pi-text\"], [1, \"d-flex\", \"flex-column\", \"w-100\", \"sidebar-categories\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"sidebar-sub-category\", 4, \"ngIf\"], [\"class\", \"mt-auto\", 4, \"ngIf\"], [\"class\", \"d-inline-flex sidebar-categories__category\", 3, \"mouseover\", 4, \"ngIf\"], [1, \"d-inline-flex\", \"sidebar-categories__category\", 3, \"mouseover\"], [1, \"sidebar-categories__category__item\", 3, \"ngStyle\", \"routerLink\", \"queryParams\", \"click\"], [1, \"sidebar-sub-category\"], [1, \"d-flex\", \"flex-wrap\", 2, \"padding\", \"20px 0px\"], [\"class\", \"d-inline-flex flex-column sidebar-sub-category__section\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-inline-flex\", \"flex-column\", \"sidebar-sub-category__section\"], [\"class\", \"sidebar-sub-category__section__heading cursor-pointer\", 3, \"routerLink\", \"queryParams\", \"ngStyle\", \"click\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"sidebar-sub-category__section__sub-name-section\"], [\"class\", \"d-inline-flex sidebar-sub-category__section__sub-name-section__sub-name\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-inline-flex sidebar-sub-category__section__sub-name-section__sub-name\\n             sidebar-sub-category__section__sub-name-section__sub-name__see-all justify-content-end cursor-pointer\", 4, \"ngIf\"], [1, \"sidebar-sub-category__section__heading\", \"cursor-pointer\", 3, \"routerLink\", \"queryParams\", \"ngStyle\", \"click\"], [1, \"d-inline-flex\", \"sidebar-sub-category__section__sub-name-section__sub-name\"], [\"class\", \"sidebar-sub-category__section__sub-name-section__sub-name__item cursor-pointer\", 3, \"ngStyle\", \"routerLink\", \"queryParams\", \"click\", 4, \"ngIf\"], [1, \"sidebar-sub-category__section__sub-name-section__sub-name__item\", \"cursor-pointer\", 3, \"ngStyle\", \"routerLink\", \"queryParams\", \"click\"], [1, \"d-inline-flex\", \"sidebar-sub-category__section__sub-name-section__sub-name\", \"sidebar-sub-category__section__sub-name-section__sub-name__see-all\", \"justify-content-end\", \"cursor-pointer\"], [1, \"underline\"], [1, \"pi\", \"pi-angle-right\", \"ml-1\", 2, \"color\", \"#ffffff\"], [1, \"mt-auto\"], [1, \"mb-3\", \"mx-3\", \"border-top-1\", \"border-none\", \"surface-border\"], [\"routerLink\", \"/merchants\", \"pRipple\", \"\", 1, \"flex\", \"align-items-center\", \"cursor-pointer\", \"p-3\", \"gap-2\", \"border-round\", \"text-0\", \"transition-duration-150\", \"transition-colors\", \"p-ripple\", 3, \"click\"], [1, \"font-bold\", \"mobile-merchant\"]],\n    template: function SideMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"p-sidebar\", 1);\n        i0.ɵɵlistener(\"onHide\", function SideMenuComponent_Template_p_sidebar_onHide_1_listener() {\n          return ctx.closeMenu();\n        })(\"visibleChange\", function SideMenuComponent_Template_p_sidebar_visibleChange_1_listener($event) {\n          return ctx.display = $event;\n        });\n        i0.ɵɵtemplate(2, SideMenuComponent_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n        i0.ɵɵtemplate(3, SideMenuComponent_ng_template_3_Template, 4, 3, \"ng-template\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"em\", 4);\n        i0.ɵɵlistener(\"click\", function SideMenuComponent_Template_em_click_4_listener() {\n          return ctx.openMenu();\n        });\n        i0.ɵɵelementStart(5, \"span\", 5);\n        i0.ɵɵprojection(6);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"visible\", ctx.display)(\"baseZIndex\", 8)(\"position\", ctx.lang === \"ar\" ? \"right\" : \"left\");\n      }\n    },\n    dependencies: [i1.PrimeTemplate, i6.Sidebar, i7.NgForOf, i7.NgIf, i7.NgStyle, i3.RouterLink, i8.TranslatePipe],\n    styles: [\"* {\\n  -webkit-touch-callout: none !important;\\n  -webkit-text-size-adjust: none !important;\\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;\\n  -webkit-user-select: text !important;\\n}\\n\\n.side-menu[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n  border-bottom: 1px solid lightgray;\\n}\\n.side-menu[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: var(--bold-font);\\n  font-size: 16px;\\n  color: black;\\n  font-weight: 700;\\n}\\n.side-menu[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  color: #000;\\n}\\n.side-menu[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .category-drop-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  justify-content: space-between;\\n  padding-top: 0.5rem !important;\\n  padding-bottom: 0.5rem !important;\\n  margin: 0 !important;\\n  cursor: pointer;\\n}\\n.side-menu[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .category-drop-item[_ngcontent-%COMP%]:hover {\\n  background-color: #ffffd5;\\n}\\n.side-menu[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  display: block;\\n  text-decoration: none;\\n  cursor: pointer;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.side-menu[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:visited {\\n  color: var(--main-color);\\n}\\n.side-menu[_ngcontent-%COMP%]   .drop-sub-categories[_ngcontent-%COMP%] {\\n  height: 400px;\\n  width: 300px;\\n  background-color: #ffffff;\\n  position: fixed;\\n  transition: transform 0.3s;\\n  display: flex;\\n  flex-direction: column;\\n  z-index: 99999;\\n  overflow-y: auto;\\n}\\n.side-menu[_ngcontent-%COMP%]   .drop-sub-categories[_ngcontent-%COMP%]   .sub-cat-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: black;\\n  margin: 0;\\n  text-align: left;\\n  padding: 10px 15px 0 15px;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n}\\n.side-menu[_ngcontent-%COMP%]   .drop-sub-categories[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  color: var(--main-color);\\n}\\n.side-menu[_ngcontent-%COMP%]   .drop-sub-categories[_ngcontent-%COMP%]   .category-sub-drop-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  justify-content: space-between;\\n  margin: 0 !important;\\n  cursor: pointer;\\n  padding: 0.5rem 15px;\\n}\\n.side-menu[_ngcontent-%COMP%]   .drop-sub-categories[_ngcontent-%COMP%]   .category-sub-drop-item[_ngcontent-%COMP%]:hover {\\n  background-color: #ffffd5;\\n}\\n\\n.no-bullets[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .pi-pi-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n    .pi-bars {\\n    -webkit-tap-highlight-color: transparent !important;\\n  }\\n    .pi-bars .pi-bars:active {\\n    background-color: transparent;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    white-space: nowrap;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    max-width: 100%;\\n  }\\n  .bars-all[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  a[_ngcontent-%COMP%] {\\n    color: #000 !important;\\n    font-size: 13px !important;\\n    font-weight: 400;\\n    font-family: var(--medium-font) !important;\\n  }\\n  a[_ngcontent-%COMP%]:visited {\\n    color: #000 !important;\\n  }\\n  .mobile-merchant[_ngcontent-%COMP%] {\\n    color: #ffffff !important;\\n    font-size: 18px !important;\\n    font-weight: 500 !important;\\n    cursor: pointer !important;\\n  }\\n}\\nul.dashed[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n}\\n\\nul.dashed[_ngcontent-%COMP%]    > li[_ngcontent-%COMP%]    > a[_ngcontent-%COMP%]:before {\\n  content: \\\"-\\\";\\n  text-indent: -5px;\\n  margin-right: 5px;\\n}\\n\\n.list-space[_ngcontent-%COMP%] {\\n  padding-left: 5px;\\n}\\n\\n.pi-white[_ngcontent-%COMP%] {\\n  color: var(--header_bgcolor) !important;\\n}\\n\\n  span.p-sidebar-close-icon.pi.pi-times {\\n  color: white;\\n}\\n\\n  .p-sidebar .p-sidebar-header .p-sidebar-close:enabled:hover, .p-sidebar[_ngcontent-%COMP%]   .p-sidebar-header[_ngcontent-%COMP%]   .p-sidebar-icon[_ngcontent-%COMP%]:enabled:hover {\\n  background: none;\\n}\\n\\n  .p-sidebar .p-sidebar-header .p-sidebar-close:focus, .p-sidebar[_ngcontent-%COMP%]   .p-sidebar-header[_ngcontent-%COMP%]   .p-sidebar-icon[_ngcontent-%COMP%]:focus {\\n  outline: 0 none;\\n  outline-offset: 0;\\n  box-shadow: none;\\n}\\n\\n  span.p-ink {\\n  background: none;\\n}\\n\\n.bars-all[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-left: 8px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  \\n\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #cfcbcb;\\n  \\n\\n  border-radius: 6px;\\n  \\n\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background-color: #f1f1f1;\\n  \\n\\n}\\n\\n\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-button {\\n  display: none;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}