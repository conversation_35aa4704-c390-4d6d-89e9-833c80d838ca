{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { isPlatformBrowser } from '@angular/common';\nimport { CountryISO, PhoneNumberFormat, SearchCountryField } from \"ngx-intl-tel-input-gg\";\nimport { TenantRecords } from \"@core/interface\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"@shared/modals/success-modal/success-modal.component\";\nimport * as i11 from \"@angular-magic/ngx-gp-autocomplete\";\nimport * as i12 from \"@angular/google-maps\";\nimport * as i13 from \"primeng/dropdown\";\nimport * as i14 from \"ngx-intl-tel-input-gg\";\nimport * as i15 from \"../modals/address-label/address-label.component\";\nconst _c0 = [\"placesRef\"];\nconst _c1 = [\"search\"];\nfunction AddressComponent_ng_container_0_map_marker_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"map-marker\", 52);\n    i0.ɵɵlistener(\"mapDrag\", function AddressComponent_ng_container_0_map_marker_13_Template_map_marker_mapDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.markerDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marker_r16 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r5.markerOptions)(\"position\", marker_r16.position);\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r19.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r20 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r20.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r21 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r21.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const car_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", car_r22.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"ErrorMessages.phoneNumberIsUnvalid\"), \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r24.selectedAddressType === \"Home\" ? \"assets/icons/mobile-icons/home-address-white.svg\" : \"assets/icons/mobile-icons/home-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r25.selectedAddressType === \"Work\" ? \"assets/icons/mobile-icons/work-address-white.svg\" : \"assets/icons/mobile-icons/work-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_container_0_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_92_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const addressType_r23 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.selectAddressType(addressType_r23.name));\n    });\n    i0.ɵɵtemplate(1, AddressComponent_ng_container_0_div_92_img_1_Template, 1, 1, \"img\", 57);\n    i0.ɵɵtemplate(2, AddressComponent_ng_container_0_div_92_img_2_Template, 1, 1, \"img\", 58);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const addressType_r23 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r13.selectedAddressType === addressType_r23.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r23.name === \"Home\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r23.name === \"Work\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", addressType_r23.name, \" \");\n  }\n}\nfunction AddressComponent_ng_container_0_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_94_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onSubmit());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.confirmAddress\"));\n  }\n}\nfunction AddressComponent_ng_container_0_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_div_98_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.Update());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r15.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.updateAddress\"));\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"form-input-error\": a0\n  };\n};\nconst _c3 = function (a0) {\n  return {\n    \"border-bottom\": a0\n  };\n};\nconst _c4 = function () {\n  return {\n    \"width\": \"100%\",\n    \"border\": \"none\",\n    \"background\": \"none\",\n    \"outline\": \"none\",\n    \"box-shadow\": \"none\"\n  };\n};\nconst _c5 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction AddressComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 3);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"img\", 7);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onBack());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 8);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"google-map\", 11);\n    i0.ɵɵlistener(\"mapClick\", function AddressComponent_ng_container_0_Template_google_map_mapClick_12_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.mapClicked($event));\n    })(\"mapInitialized\", function AddressComponent_ng_container_0_Template_google_map_mapInitialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.mapInitialize($event));\n    });\n    i0.ɵɵtemplate(13, AddressComponent_ng_container_0_map_marker_13_Template, 1, 2, \"map-marker\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"form\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"label\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 15);\n    i0.ɵɵelement(21, \"input\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19)(24, \"div\", 20);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelement(27, \"br\");\n    i0.ɵɵelementStart(28, \"p-dropdown\", 21, 22);\n    i0.ɵɵlistener(\"onChange\", function AddressComponent_ng_container_0_Template_p_dropdown_onChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.filterCitiesByRegion($event.value));\n    });\n    i0.ɵɵtemplate(30, AddressComponent_ng_container_0_ng_template_30_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(31, AddressComponent_ng_container_0_ng_template_31_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 18)(33, \"div\", 19)(34, \"div\", 25);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelement(37, \"br\");\n    i0.ɵɵelementStart(38, \"p-dropdown\", 26, 27);\n    i0.ɵɵtemplate(40, AddressComponent_ng_container_0_ng_template_40_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(41, AddressComponent_ng_container_0_ng_template_41_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 14)(43, \"div\", 15)(44, \"label\", 28);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 15)(48, \"input\", 29);\n    i0.ɵɵlistener(\"input\", function AddressComponent_ng_container_0_Template_input_input_48_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.getCoordinates());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 14)(50, \"div\", 15)(51, \"label\", 28);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 15);\n    i0.ɵɵelement(55, \"input\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 14)(57, \"div\", 15)(58, \"label\", 31);\n    i0.ɵɵtext(59);\n    i0.ɵɵpipe(60, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 15);\n    i0.ɵɵelement(62, \"input\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 14)(64, \"div\", 15)(65, \"label\", 33);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 15);\n    i0.ɵɵelement(69, \"input\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 14)(71, \"div\", 15)(72, \"label\", 35);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 36);\n    i0.ɵɵelement(76, \"ngx-intl-tel-input\", 37);\n    i0.ɵɵtemplate(77, AddressComponent_ng_container_0_span_77_Template, 3, 3, \"span\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 14)(79, \"div\", 15)(80, \"label\", 39);\n    i0.ɵɵtext(81);\n    i0.ɵɵpipe(82, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 15);\n    i0.ɵɵelement(84, \"input\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 41)(86, \"div\", 42)(87, \"div\", 43)(88, \"div\", 44);\n    i0.ɵɵtext(89);\n    i0.ɵɵpipe(90, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 45);\n    i0.ɵɵtemplate(92, AddressComponent_ng_container_0_div_92_Template, 4, 5, \"div\", 46);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(93, \"div\", 47);\n    i0.ɵɵtemplate(94, AddressComponent_ng_container_0_div_94_Template, 3, 4, \"div\", 48);\n    i0.ɵɵelementStart(95, \"div\", 49)(96, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_container_0_Template_button_click_96_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.setAsDefault());\n    });\n    i0.ɵɵpipe(97, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(98, AddressComponent_ng_container_0_div_98_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_14_0;\n    let tmp_20_0;\n    let tmp_25_0;\n    let tmp_40_0;\n    let tmp_41_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 51, \"multipleAddress.completeAddress\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"center\", ctx_r0.center)(\"zoom\", ctx_r0.zoom)(\"options\", ctx_r0.mapOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.addressForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(19, 53, \"addingAddress.country\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c2, ((tmp_7_0 = ctx_r0.addressForm.get(\"country\")) == null ? null : tmp_7_0.hasError(\"required\")) && ((tmp_7_0 = ctx_r0.addressForm.get(\"country\")) == null ? null : tmp_7_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(77, _c3, ((tmp_8_0 = ctx_r0.addressForm.get(\"region.regionId\")) == null ? null : tmp_8_0.hasError(\"required\")) && ((tmp_8_0 = ctx_r0.addressForm.get(\"region.regionId\")) == null ? null : tmp_8_0.touched) ? \"1px solid red\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.region);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 55, \"addingAddress.region\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(79, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r0.allRegionList)(\"filter\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(80, _c3, ((tmp_14_0 = ctx_r0.addressForm.get(\"city\")) == null ? null : tmp_14_0.hasError(\"required\")) && ((tmp_14_0 = ctx_r0.addressForm.get(\"city\")) == null ? null : tmp_14_0.touched) ? \"1px solid red\" : \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 57, \"addingAddress.city\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(82, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r0.filteredCities)(\"filter\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(46, 59, \"addingAddress.addressDetails\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(83, _c2, (tmp_20_0 = ctx_r0.addressForm.get(\"streetAddress\")) == null ? null : tmp_20_0.hasError(\"required\")));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(53, 61, \"addingAddress.otherAddressSecond\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(60, 63, \"addingAddress.post-code\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r0.addressForm.value.postcode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(67, 65, \"addingAddress.landMark\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(85, _c2, ((tmp_25_0 = ctx_r0.addressForm.get(\"landMark\")) == null ? null : tmp_25_0.hasError(\"required\")) && ((tmp_25_0 = ctx_r0.addressForm.get(\"landMark\")) == null ? null : tmp_25_0.touched)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(74, 67, \"addingAddress.phone\"), \"*\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r0.phoneInputLength)(\"numberFormat\", ctx_r0.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r0.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(87, _c5, ctx_r0.SearchCountryField.Iso2, ctx_r0.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r0.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r0.customPlaceHolder)(\"ngClass\", i0.ɵɵpureFunction1(90, _c2, ((tmp_40_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.hasError(\"required\")) || ((tmp_40_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.hasError(\"validatePhoneNumber\"))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_41_0 = ctx_r0.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.hasError(\"validatePhoneNumber\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(82, 69, \"addingAddress.instructions\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(90, 71, \"addingAddress.addressLabel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.addressLabelList);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.addressService.chosenAddress === null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.addressService.chosenAddress === null || ctx_r0.addressService.chosenAddress.isDefault === true)(\"label\", i0.ɵɵpipeBind1(97, 73, \"addingAddress.defaultAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id && ctx_r0.id !== \"add-address\");\n  }\n}\nfunction AddressComponent_ng_template_1_h2_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"addingAddress.addAddress\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_h2_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"addingAddress.updateAddressTitle\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_map_marker_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"map-marker\", 52);\n    i0.ɵɵlistener(\"mapDrag\", function AddressComponent_ng_template_1_map_marker_30_Template_map_marker_mapDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.markerDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marker_r54 = ctx.$implicit;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r43.markerOptions)(\"position\", marker_r54.position);\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r57 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r57.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const region_r58 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", region_r58.regionName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r59 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r59.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const car_r60 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", car_r60.cityName, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(2, 1, \"ErrorMessages.phoneNumberIsUnvalid\"), \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_div_102_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r62.selectedAddressType === \"Home\" ? \"assets/icons/mobile-icons/home-address-white.svg\" : \"assets/icons/mobile-icons/home-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_template_1_div_102_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r63.selectedAddressType === \"Work\" ? \"assets/icons/mobile-icons/work-address-white.svg\" : \"assets/icons/mobile-icons/work-address.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AddressComponent_ng_template_1_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_102_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r65);\n      const addressType_r61 = restoredCtx.$implicit;\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.selectAddressType(addressType_r61.name));\n    });\n    i0.ɵɵtemplate(1, AddressComponent_ng_template_1_div_102_img_1_Template, 1, 1, \"img\", 57);\n    i0.ɵɵtemplate(2, AddressComponent_ng_template_1_div_102_img_2_Template, 1, 1, \"img\", 58);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const addressType_r61 = ctx.$implicit;\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r51.selectedAddressType === addressType_r61.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r61.name === \"Home\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", addressType_r61.name === \"Work\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", addressType_r61.name, \" \");\n  }\n}\nfunction AddressComponent_ng_template_1_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_107_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.onSubmit());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r52.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.addAddress\"));\n  }\n}\nfunction AddressComponent_ng_template_1_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_div_108_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r69);\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.Update());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r53.validate())(\"label\", i0.ɵɵpipeBind1(2, 2, \"addingAddress.updateAddress\"));\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"hidden-navbar\": a0\n  };\n};\nfunction AddressComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"em\", 67)(3, \"em\", 68);\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"em\", 68);\n    i0.ɵɵelementStart(8, \"span\", 69);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 70);\n    i0.ɵɵtemplate(12, AddressComponent_ng_template_1_h2_12_Template, 3, 3, \"h2\", 71);\n    i0.ɵɵtemplate(13, AddressComponent_ng_template_1_h2_13_Template, 3, 3, \"h2\", 71);\n    i0.ɵɵelementStart(14, \"div\", 47)(15, \"div\", 72)(16, \"div\", 73)(17, \"div\", 47)(18, \"div\", 10)(19, \"div\", 74)(20, \"span\", 75);\n    i0.ɵɵelement(21, \"em\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 77, 78);\n    i0.ɵɵlistener(\"onAddressChange\", function AddressComponent_ng_template_1_Template_input_onAddressChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.handleAddressChange($event));\n    });\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 79);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_Template_span_click_26_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.clear());\n    });\n    i0.ɵɵelement(27, \"em\", 80);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 10)(29, \"google-map\", 81);\n    i0.ɵɵlistener(\"mapClick\", function AddressComponent_ng_template_1_Template_google_map_mapClick_29_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.mapClicked($event));\n    })(\"mapInitialized\", function AddressComponent_ng_template_1_Template_google_map_mapInitialized_29_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.mapInitialize($event));\n    });\n    i0.ɵɵtemplate(30, AddressComponent_ng_template_1_map_marker_30_Template, 1, 2, \"map-marker\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"form\", 13)(32, \"div\", 61)(33, \"div\", 82)(34, \"div\", 83);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelement(37, \"br\");\n    i0.ɵɵelementStart(38, \"p-dropdown\", 21, 22);\n    i0.ɵɵlistener(\"onChange\", function AddressComponent_ng_template_1_Template_p_dropdown_onChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.filterCitiesByRegion($event.value));\n    });\n    i0.ɵɵtemplate(40, AddressComponent_ng_template_1_ng_template_40_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(41, AddressComponent_ng_template_1_ng_template_41_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 61)(43, \"div\", 82)(44, \"div\", 84);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelement(47, \"br\");\n    i0.ɵɵelementStart(48, \"p-dropdown\", 26, 27);\n    i0.ɵɵtemplate(50, AddressComponent_ng_template_1_ng_template_50_Template, 1, 1, \"ng-template\", 23);\n    i0.ɵɵtemplate(51, AddressComponent_ng_template_1_ng_template_51_Template, 2, 1, \"ng-template\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(52, \"div\", 61)(53, \"div\", 42);\n    i0.ɵɵelement(54, \"input\", 32);\n    i0.ɵɵelementStart(55, \"label\", 31);\n    i0.ɵɵtext(56);\n    i0.ɵɵpipe(57, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 61)(59, \"div\", 85);\n    i0.ɵɵelement(60, \"ngx-intl-tel-input\", 37);\n    i0.ɵɵtemplate(61, AddressComponent_ng_template_1_span_61_Template, 3, 3, \"span\", 38);\n    i0.ɵɵelementStart(62, \"label\", 35);\n    i0.ɵɵtext(63);\n    i0.ɵɵpipe(64, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 61)(66, \"div\", 42);\n    i0.ɵɵelement(67, \"input\", 86);\n    i0.ɵɵelementStart(68, \"label\", 16);\n    i0.ɵɵtext(69);\n    i0.ɵɵpipe(70, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"div\", 61)(72, \"div\", 42)(73, \"input\", 29);\n    i0.ɵɵlistener(\"input\", function AddressComponent_ng_template_1_Template_input_input_73_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.getCoordinates());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"label\", 28);\n    i0.ɵɵtext(75);\n    i0.ɵɵpipe(76, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(77, \"div\", 61)(78, \"div\", 42);\n    i0.ɵɵelement(79, \"input\", 30);\n    i0.ɵɵelementStart(80, \"label\", 28);\n    i0.ɵɵtext(81);\n    i0.ɵɵpipe(82, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(83, \"div\", 61)(84, \"div\", 42);\n    i0.ɵɵelement(85, \"input\", 34);\n    i0.ɵɵelementStart(86, \"label\", 33);\n    i0.ɵɵtext(87);\n    i0.ɵɵpipe(88, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(89, \"div\", 61)(90, \"div\", 42);\n    i0.ɵɵelement(91, \"input\", 40);\n    i0.ɵɵelementStart(92, \"label\", 39);\n    i0.ɵɵtext(93);\n    i0.ɵɵpipe(94, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"div\", 87)(96, \"div\", 42)(97, \"div\", 88)(98, \"div\", 44);\n    i0.ɵɵtext(99);\n    i0.ɵɵpipe(100, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(101, \"div\", 45);\n    i0.ɵɵtemplate(102, AddressComponent_ng_template_1_div_102_Template, 4, 5, \"div\", 46);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(103, \"div\", 47)(104, \"div\", 63)(105, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function AddressComponent_ng_template_1_Template_button_click_105_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.setAsDefault());\n    });\n    i0.ɵɵpipe(106, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(107, AddressComponent_ng_template_1_div_107_Template, 3, 4, \"div\", 48);\n    i0.ɵɵtemplate(108, AddressComponent_ng_template_1_div_108_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_14_0;\n    let tmp_20_0;\n    let tmp_40_0;\n    let tmp_41_0;\n    let tmp_43_0;\n    let tmp_45_0;\n    let tmp_48_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(87, _c6, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 59, \"sideMenu.yourAccount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 61, \"multipleAddress.yourAddresses\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id === \"add-address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id !== \"add-address\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(25, 63, \"addingAddress.search\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"center\", ctx_r2.center)(\"zoom\", ctx_r2.zoom)(\"options\", ctx_r2.mapOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.addressForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(89, _c3, (((tmp_14_0 = ctx_r2.addressForm.get(\"region.id\")) == null ? null : tmp_14_0.hasError(\"required\")) && ((tmp_14_0 = ctx_r2.addressForm.get(\"region.id\")) == null ? null : tmp_14_0.touched) ? \"1px solid red\" : \"\") || (!((tmp_14_0 = ctx_r2.addressForm.get(\"region\")) == null ? null : tmp_14_0.valid) ? \"1px solid red\" : \"\")));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.region);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(36, 65, \"addingAddress.region\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(91, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r2.allRegionList)(\"filter\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(92, _c3, (((tmp_20_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_20_0.hasError(\"required\")) && ((tmp_20_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_20_0.touched) ? \"1px solid red\" : \"\") || (!((tmp_20_0 = ctx_r2.addressForm.get(\"city\")) == null ? null : tmp_20_0.valid) ? \"1px solid red\" : \"\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(46, 67, \"addingAddress.city\"), \"* \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(94, _c4));\n    i0.ɵɵproperty(\"options\", ctx_r2.filteredCities)(\"filter\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r2.addressForm.value.postcode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(57, 69, \"addingAddress.post-code\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone region-label\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r2.phoneInputLength)(\"numberFormat\", ctx_r2.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r2.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(95, _c5, ctx_r2.SearchCountryField.Iso2, ctx_r2.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r2.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r2.customPlaceHolder)(\"ngClass\", i0.ɵɵpureFunction1(98, _c2, ((tmp_40_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.hasError(\"required\")) && ((tmp_40_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_40_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_41_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.hasError(\"validatePhoneNumber\")) && ((tmp_41_0 = ctx_r2.addressForm.get(\"receiverPhoneNumber\")) == null ? null : tmp_41_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(64, 71, \"addingAddress.phone\"), \"*\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(100, _c2, ((tmp_43_0 = ctx_r2.addressForm.get(\"country\")) == null ? null : tmp_43_0.hasError(\"required\")) && ((tmp_43_0 = ctx_r2.addressForm.get(\"country\")) == null ? null : tmp_43_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(70, 73, \"addingAddress.country\"), \"*\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(102, _c2, (tmp_45_0 = ctx_r2.addressForm.get(\"streetAddress\")) == null ? null : tmp_45_0.hasError(\"required\")));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(76, 75, \"addingAddress.addressDetails\"), \"*\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(82, 77, \"addingAddress.otherAddressSecond\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(104, _c2, ((tmp_48_0 = ctx_r2.addressForm.get(\"landMark\")) == null ? null : tmp_48_0.hasError(\"required\")) && ((tmp_48_0 = ctx_r2.addressForm.get(\"landMark\")) == null ? null : tmp_48_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(88, 79, \"addingAddress.landMark\"), \"*\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(94, 81, \"addingAddress.instructions\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(100, 83, \"addingAddress.addressLabel\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.addressLabelList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.addressService.chosenAddress === null || ctx_r2.addressService.chosenAddress.isDefault === true)(\"label\", i0.ɵɵpipeBind1(106, 85, \"addingAddress.defaultAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id === \"add-address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.id && ctx_r2.id !== \"add-address\");\n  }\n}\nfunction AddressComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-mtn-success-modal\", 92);\n    i0.ɵɵlistener(\"submit\", function AddressComponent_ng_container_3_Template_app_mtn_success_modal_submit_1_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.onConfrim());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r3.isDisplaySuccessModal)(\"message\", ctx_r3.message);\n  }\n}\nfunction AddressComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-address-label\", 93);\n    i0.ɵɵlistener(\"submit\", function AddressComponent_ng_container_4_Template_app_address_label_submit_1_listener($event) {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.addAddress($event));\n    })(\"cancel\", function AddressComponent_ng_container_4_Template_app_address_label_cancel_1_listener($event) {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.onAddressDialogCancel($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r4.isDisplayOtherModal);\n  }\n}\nexport class AddressComponent {\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(ngZone, addressService, messageService, router, store, route, translate, mainDataService, loaderService, _location, cd, permissionService, appDataService, $gtmService, platformId) {\n    this.ngZone = ngZone;\n    this.addressService = addressService;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.route = route;\n    this.translate = translate;\n    this.mainDataService = mainDataService;\n    this.loaderService = loaderService;\n    this._location = _location;\n    this.cd = cd;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.$gtmService = $gtmService;\n    this.platformId = platformId;\n    this.markerOptions = {\n      draggable: true,\n      icon: 'assets/images/map-pin.svg'\n    };\n    this.phoneLength = 13;\n    this.landMarkAddressRequired = false;\n    this.isMobileTemplate = false;\n    this.isDisplaySuccessModal = false;\n    this.isDisplayOtherModal = false;\n    this.message = '';\n    this.addressDetailCity = '';\n    this.addressForm = new UntypedFormGroup({\n      addressLabel: new UntypedFormControl('Home'),\n      receiverFirstName: new UntypedFormControl(''),\n      receiverLastName: new UntypedFormControl(''),\n      streetAddress: new UntypedFormControl('', Validators.required),\n      country: new UntypedFormControl('', Validators.required),\n      city: new UntypedFormControl('', Validators.required),\n      landMark: new UntypedFormControl('', Validators.required),\n      deliveryInstructions: new UntypedFormControl(''),\n      buldingNumber: new UntypedFormControl(''),\n      postcode: new UntypedFormControl(''),\n      receiverPhoneNumber: new UntypedFormControl('', Validators.required),\n      geo_location: new UntypedFormControl(''),\n      Lat: new UntypedFormControl(''),\n      Lng: new UntypedFormControl(''),\n      Id: new UntypedFormControl(''),\n      additionalAddress: new UntypedFormControl(''),\n      region: new UntypedFormGroup({\n        id: new UntypedFormControl(''),\n        regionName: new UntypedFormControl('')\n      }, Validators.required)\n    });\n    this.search = '';\n    this.isDefault = false;\n    this.center = {\n      lat: 0.3,\n      lng: 32.5\n    };\n    this.mapOptions = {\n      fullscreenControl: false,\n      disableDefaultUI: true\n    };\n    this.allCities = [];\n    this.isDifferentCity = false;\n    this.addressLabelList = [{\n      'name': 'Home',\n      'id': 1\n    }, {\n      'name': 'Work',\n      'id': 2\n    }, {\n      'name': 'Other',\n      'id': 3\n    }];\n    this.id = '';\n    this.geoCoder = new google.maps.Geocoder();\n    this.borderBottomStyle = '2px solid red !important';\n    this.phoneInputLength = 12;\n    this.preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n    this.customPlaceHolder = '';\n    this.PhoneNumberFormat = PhoneNumberFormat;\n    this.SearchCountryField = SearchCountryField;\n    this.allRegionList = [];\n    this.filteredCities = [];\n    this.screenWidth = window.innerWidth;\n    this.selectedCitiesValue = [];\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.source = this.router.getCurrentNavigation()?.extras?.state;\n    let tenantId = localStorage.getItem('tenantId');\n    if (tenantId && tenantId !== '') {\n      if (tenantId == '1') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '2') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '3') {\n        this.customPlaceHolder = 'XXXXXXXXX';\n      } else if (tenantId == '4') {\n        this.customPlaceHolder = 'XXXXXXXXXX';\n      }\n    }\n  }\n  onBack() {\n    this._location.back();\n  }\n  ngOnInit() {\n    const googleResponse = {\n      results: [{\n        \"address_components\": [{\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }, {\n          \"long_name\": \"Cocody\",\n          \"short_name\": \"Cocody\",\n          \"types\": [\"political\", \"sublocality\", \"sublocality_level_1\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"District Autonome d'Abidjan\",\n          \"short_name\": \"District Autonome d'Abidjan\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }],\n        \"formatted_address\": \"99326, Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"location\": {\n            \"lat\": 5.361162999999999,\n            \"lng\": -3.9977942\n          },\n          \"location_type\": \"GEOMETRIC_CENTER\",\n          \"viewport\": {\n            \"south\": 5.359814019708497,\n            \"west\": -3.999143180291502,\n            \"north\": 5.362511980291502,\n            \"east\": -3.996445219708498\n          }\n        },\n        \"place_id\": \"ChIJYXXkgbbrwQ8R6iRpzKnEIw8\",\n        \"plus_code\": {\n          \"compound_code\": \"9262+FV Abidjan, Côte d'Ivoire\",\n          \"global_code\": \"6CQR9262+FV\"\n        },\n        \"types\": [\"establishment\", \"point_of_interest\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Espace 168\",\n          \"short_name\": \"Espace 168\",\n          \"types\": [\"premise\"]\n        }, {\n          \"long_name\": \"Boulevard des Martyrs\",\n          \"short_name\": \"Bd des Martyrs\",\n          \"types\": [\"route\"]\n        }, {\n          \"long_name\": \"Cocody\",\n          \"short_name\": \"Cocody\",\n          \"types\": [\"political\", \"sublocality\", \"sublocality_level_1\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"District Autonome d'Abidjan\",\n          \"short_name\": \"District Autonome d'Abidjan\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Espace 168, Bd des Martyrs, Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"location\": {\n            \"lat\": 5.361365,\n            \"lng\": -3.9979289\n          },\n          \"location_type\": \"ROOFTOP\",\n          \"viewport\": {\n            \"south\": 5.360016019708498,\n            \"west\": -3.999277880291503,\n            \"north\": 5.362713980291502,\n            \"east\": -3.996579919708498\n          }\n        },\n        \"place_id\": \"ChIJDSB1NV3rwQ8RUoJ6o5QKBw4\",\n        \"types\": [\"premise\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"9262+GW\",\n          \"short_name\": \"9262+GW\",\n          \"types\": [\"plus_code\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"administrative_area_level_2\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan Autonomous District\",\n          \"short_name\": \"Abidjan Autonomous District\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"9262+GW Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 5.36125,\n            \"west\": -3.99775,\n            \"north\": 5.361375,\n            \"east\": -3.997625\n          },\n          \"location\": {\n            \"lat\": 5.3613569,\n            \"lng\": -3.9976294\n          },\n          \"location_type\": \"GEOMETRIC_CENTER\",\n          \"viewport\": {\n            \"south\": 5.359963519708498,\n            \"west\": -3.999036480291502,\n            \"north\": 5.362661480291503,\n            \"east\": -3.996338519708497\n          }\n        },\n        \"place_id\": \"GhIJW7wOiwdyFUARjjl0HyX7D8A\",\n        \"plus_code\": {\n          \"compound_code\": \"9262+GW Abidjan, Côte d'Ivoire\",\n          \"global_code\": \"6CQR9262+GW\"\n        },\n        \"types\": [\"plus_code\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Boulevard des Martyrs\",\n          \"short_name\": \"Bd des Martyrs\",\n          \"types\": [\"route\"]\n        }, {\n          \"long_name\": \"Cocody\",\n          \"short_name\": \"Cocody\",\n          \"types\": [\"political\", \"sublocality\", \"sublocality_level_1\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"District Autonome d'Abidjan\",\n          \"short_name\": \"District Autonome d'Abidjan\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Bd des Martyrs, Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 5.3607096,\n            \"west\": -3.997825,\n            \"north\": 5.362436799999999,\n            \"east\": -3.9975\n          },\n          \"location\": {\n            \"lat\": 5.3615357,\n            \"lng\": -3.9975106\n          },\n          \"location_type\": \"GEOMETRIC_CENTER\",\n          \"viewport\": {\n            \"south\": 5.360224219708497,\n            \"west\": -3.999011480291502,\n            \"north\": 5.362922180291502,\n            \"east\": -3.996313519708497\n          }\n        },\n        \"place_id\": \"ChIJk80SNF3rwQ8RMLvuyNHzvac\",\n        \"types\": [\"route\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Cocody\",\n          \"short_name\": \"Cocody\",\n          \"types\": [\"political\", \"sublocality\", \"sublocality_level_1\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"administrative_area_level_2\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan Autonomous District\",\n          \"short_name\": \"Abidjan Autonomous District\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Cocody, Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 5.3177369,\n            \"west\": -4.0176456,\n            \"north\": 5.4606275,\n            \"east\": -3.9165417\n          },\n          \"location\": {\n            \"lat\": 5.3602164,\n            \"lng\": -3.9674371\n          },\n          \"location_type\": \"APPROXIMATE\",\n          \"viewport\": {\n            \"south\": 5.3177369,\n            \"west\": -4.0176456,\n            \"north\": 5.4606275,\n            \"east\": -3.9165417\n          }\n        },\n        \"place_id\": \"ChIJ_-kvynLtwQ8RskjN5omhEcU\",\n        \"types\": [\"political\", \"sublocality\", \"sublocality_level_1\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"locality\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan\",\n          \"short_name\": \"Abidjan\",\n          \"types\": [\"administrative_area_level_2\", \"political\"]\n        }, {\n          \"long_name\": \"Abidjan Autonomous District\",\n          \"short_name\": \"Abidjan Autonomous District\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Abidjan, Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 5.2334876,\n            \"west\": -4.124071499999999,\n            \"north\": 5.4634126,\n            \"east\": -3.8352585\n          },\n          \"location\": {\n            \"lat\": 5.3252258,\n            \"lng\": -4.019603\n          },\n          \"location_type\": \"APPROXIMATE\",\n          \"viewport\": {\n            \"south\": 5.2334876,\n            \"west\": -4.124071499999999,\n            \"north\": 5.4634126,\n            \"east\": -3.8352585\n          }\n        },\n        \"place_id\": \"ChIJIZGVEVPqwQ8RpiGS4dwN5z8\",\n        \"types\": [\"locality\", \"political\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Abidjan Autonomous District\",\n          \"short_name\": \"Abidjan Autonomous District\",\n          \"types\": [\"administrative_area_level_1\", \"political\"]\n        }, {\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Abidjan Autonomous District, Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 5.218829299999999,\n            \"west\": -4.4611351,\n            \"north\": 5.6050827,\n            \"east\": -3.7163464\n          },\n          \"location\": {\n            \"lat\": 5.463496,\n            \"lng\": -4.1513764\n          },\n          \"location_type\": \"APPROXIMATE\",\n          \"viewport\": {\n            \"south\": 5.218829299999999,\n            \"west\": -4.4611351,\n            \"north\": 5.6050827,\n            \"east\": -3.7163464\n          }\n        },\n        \"place_id\": \"ChIJR0gb9_2UwQ8RiMwuqKxgDgY\",\n        \"types\": [\"administrative_area_level_1\", \"political\"]\n      }, {\n        \"address_components\": [{\n          \"long_name\": \"Côte d'Ivoire\",\n          \"short_name\": \"CI\",\n          \"types\": [\"country\", \"political\"]\n        }],\n        \"formatted_address\": \"Côte d'Ivoire\",\n        \"geometry\": {\n          \"bounds\": {\n            \"south\": 4.193,\n            \"west\": -8.6020589,\n            \"north\": 10.7410946,\n            \"east\": -2.493031\n          },\n          \"location\": {\n            \"lat\": 7.539988999999999,\n            \"lng\": -5.547079999999999\n          },\n          \"location_type\": \"APPROXIMATE\",\n          \"viewport\": {\n            \"south\": 4.193,\n            \"west\": -8.6020589,\n            \"north\": 10.7410946,\n            \"east\": -2.493031\n          }\n        },\n        \"place_id\": \"ChIJZ6BJsIcTlg8RQO65fGIe-QE\",\n        \"types\": [\"country\", \"political\"]\n      }]\n    };\n    const selectedAddress = googleResponse.results.find(result => result.types && result.types.includes(\"premise1\"));\n    this.address = selectedAddress ? selectedAddress.formatted_address : googleResponse.results[0]?.formatted_address;\n    console.log(this.address);\n    this.route.queryParamMap.subscribe(queryParams => {\n      this.routeToCheckOut = queryParams.get(\"checkout\");\n    });\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.lat = history.state.lat;\n    this.lng = history.state.lng;\n    this.route.queryParams.subscribe(params => {\n      console.log('parr', params);\n      this.redirectUrl = params.returnUrl;\n    });\n    this.routeSub = this.route.params.subscribe(params => {\n      this.id = params['id'];\n      if (this.id != 'add-address') {\n        this.getCustomerAddress();\n        this.$gtmService.pushPageView('Your Addresses', 'Update Address');\n      } else {\n        this.$gtmService.pushPageView('Your Addresses', 'Add Address');\n        const defaultPhoneNumber = localStorage.getItem('userPhone');\n        const countryPhone = localStorage.getItem('CountryPhone')?.replace('+', '') || '';\n        const phoneNumberWithoutCode = defaultPhoneNumber?.replace(countryPhone, '');\n        this.addressForm.get('receiverPhoneNumber')?.setValue(phoneNumberWithoutCode);\n        this.addressForm.get('receiverPhoneNumber')?.markAllAsTouched();\n        this.getAllRegion();\n        if (this.lat && this.lng) {\n          this.setCurrentLocation({\n            lat: this.lat,\n            lng: this.lng\n          });\n        } else {\n          this.setCurrentLocation();\n        }\n      }\n    });\n    let phoneLength = localStorage.getItem('PhoneLength')?.toString();\n    let landMarkAddress = localStorage.getItem('customerAddressLandmarkRequired')?.toString();\n    if (landMarkAddress && landMarkAddress == 'True') {\n      this.landMarkAddressRequired = true;\n      this.addressForm.controls['landMark'].setValidators([Validators.required]);\n      this.addressForm.controls['landMark'].updateValueAndValidity();\n    }\n    if (phoneLength) {\n      this.phoneLength = parseInt(phoneLength) - 2;\n    }\n    let userDetails = this.store.get('profile');\n    this.mainDataService.setUserData(userDetails);\n    let name = userDetails?.name?.split(' ');\n    this.addressForm.patchValue({\n      receiverFirstName: name[0] ? name[0] : '',\n      receiverLastName: name[1] ? name[1] : ''\n      // receiverPhoneNumber: userDetails.mobileNumber,\n    });\n\n    if (!localStorage.getItem(\"isoCode\")) {\n      const tenants = this.appDataService.tenants;\n      if (tenants.records != undefined) {\n        let tenantId = localStorage.getItem('tenantId');\n        let data = tenants.records;\n        let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n        localStorage.setItem('isoCode', arr?.isoCode);\n        this.store.set('allCountryTenants', tenants.records);\n      }\n    } else {\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n    }\n    if (this.appDataService.configuration) {\n      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n    }\n    this.filteredCities = this.allCities;\n    this.region.valueChanges.subscribe(value => {\n      const selectedRegion = this.allRegionList?.find(r => r.id == value.id);\n      this.addressForm.patchValue({\n        region: {\n          id: selectedRegion.id,\n          regionName: selectedRegion.regionName\n        }\n      }, {\n        emitEvent: false\n      });\n    });\n  }\n  mapInitialize(map) {\n    this.map = map;\n  }\n  handleAddressChange(place) {\n    this.Lat = place.geometry.location.lat();\n    this.Lng = place.geometry.location.lng();\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.zoom = 12;\n    this.center = this.position[0].position;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  ngOnDestroy() {\n    this.routeSub.unsubscribe();\n  }\n  getCustomerAddress() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loaderService.show();\n      try {\n        const res = yield _this.addressService.getAddressById(_this.id).toPromise();\n        _this.addressService.loadedAddress = true;\n        if (res.data) {\n          _this.isDefault = res.data.isDefault;\n          _this.addressService.chosenAddress = res.data;\n          const trimmedPhoneNumber = _this.trimPhoneNumber(res.data.receiverPhoneNumber);\n          yield _this.getAllRegion();\n          const selectedRegion = _this.selectedRegion(res.data.region);\n          const selectedCity = _this.selectedCity(res.data.city);\n          _this.addressForm.patchValue({\n            addressLabel: res.data.addressLabel,\n            receiverFirstName: res.data.receiverFirstName,\n            receiverLastName: res.data.receiverLastName,\n            postcode: res.data.postCode,\n            receiverPhoneNumber: trimmedPhoneNumber,\n            landMark: res.data.landMark,\n            deliveryInstructions: res.data.deliveryInstructions,\n            buldingNumber: res.data.buldingNumber,\n            additionalAddress: res.data.additionalAddress,\n            city: selectedCity.cityName,\n            region: selectedRegion\n          });\n          if (res.data.addressLabel != 'Home' && res.data.addressLabel != 'Work') {\n            _this.addressLabelList[2].name = res.data.addressLabel;\n          }\n          _this.selectedAddressType = res.data.addressLabel;\n          _this.Lat = parseFloat(res.data.lat);\n          _this.Lng = parseFloat(res.data.lng);\n          const streetAddress = res.data.streetAddress;\n          if (_this.searchElementRef) {\n            _this.searchElementRef.nativeElement.value = streetAddress;\n          }\n          _this.position = [{\n            position: {\n              lat: _this.Lat,\n              lng: _this.Lng\n            }\n          }];\n          _this.zoom = 8;\n          _this.center = _this.position[0].position;\n          if (res.data.streetAddress || res.data.country) {\n            _this.addressForm.patchValue({\n              streetAddress: res.data.streetAddress,\n              country: res.data.country\n            });\n          } else {\n            _this.getAddress(_this.Lat, _this.Lng);\n          }\n          _this.createLocationButton();\n        } else {\n          _this.setCurrentLocation();\n          _this.createLocationButton();\n        }\n        _this.loaderService.hide();\n      } catch (err) {\n        _this.addressService.loadedAddress = true;\n        _this.setCurrentLocation();\n        _this.messageService.add({\n          severity: 'error',\n          summary: _this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      } finally {\n        _this.loaderService.hide();\n      }\n    })();\n  }\n  trimPhoneNumber(phoneNumber) {\n    return phoneNumber.length > 3 ? phoneNumber.substring(3) : phoneNumber;\n  }\n  selectedRegion(regionName) {\n    const region = this.allRegionList.find(r => r.regionName === regionName);\n    if (region) {\n      this.filterCitiesByRegion(region.id);\n      this.selectedCitiesValue = region.cities;\n      return {\n        id: region.id,\n        regionName: region.regionName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"regionName\": null\n    };\n  }\n  selectedCity(cityName) {\n    const city = this.selectedCitiesValue.find(r => r.cityName === cityName);\n    if (city) {\n      return {\n        id: city.id,\n        cityName: city.cityName\n      };\n    }\n    return {\n      \"id\": -1,\n      \"cityName\": null\n    };\n  }\n  getAddress(latitude, longitude) {\n    this.geoCoder.geocode({\n      location: {\n        lat: latitude,\n        lng: longitude\n      }\n    }, (results, status) => {\n      if (status === 'OK') {\n        if (results[0]) {\n          this.position = [{\n            position: {\n              lat: latitude,\n              lng: longitude\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.address = results[0].formatted_address;\n          if (results[0]?.address_components.length) {\n            const city = results[0].address_components.find(item => item.types.includes('locality'));\n            this.addressDetailCity = city.long_name;\n          }\n          this.addressForm.patchValue({\n            streetAddress: this.address,\n            country: results[results.length - 1].formatted_address\n            // city: results[results.length - 3].formatted_address,\n          });\n\n          this.validate();\n          this.getCoordinates();\n          this.cd.detectChanges();\n        } else {\n          if (isPlatformBrowser(this.platformId)) {\n            window.alert('No results found');\n          }\n        }\n      } else {\n        if (isPlatformBrowser(this.platformId)) {\n          window.alert('Geocoder failed due to: ' + status);\n        }\n      }\n    });\n  }\n  clear() {\n    this.searchElementRef.nativeElement.value = '';\n  }\n  onSubmit() {\n    this.addressForm.patchValue({\n      Lat: this.Lat ? this.Lat.toString() : '',\n      Lng: this.Lng ? this.Lng.toString() : ''\n      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\n    });\n\n    this.loaderService.show();\n    if (this.addressForm.value.postcode == '') this.addressForm.value.postcode = 0;\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      if (this.addressForm.value.postcode === \"\") delete this.addressForm.value.postcode;\n      this.addressService.addAddress(formValue).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.loaderService.hide();\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully');\n          } else {\n            this.loaderService.hide();\n            this.messageService.add({\n              severity: 'error',\n              summary: res?.message\n            });\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  onConfrim() {\n    this.isDisplaySuccessModal = false;\n    if (this.routeToCheckOut) {\n      this.router.navigateByUrl(\"/checkout/selectAddress\");\n    } else {\n      this.router.navigate(['/account/address']);\n    }\n  }\n  Update() {\n    if (this.Lat?.toString() === \"\" || this.Lng?.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.addressIsRequired')\n      });\n      return;\n    }\n    this.addressForm.patchValue({\n      Lat: this.Lat?.toString(),\n      Lng: this.Lng?.toString(),\n      Id: this.addressService.chosenAddress.id\n    });\n    this.loaderService.show();\n    if (this.addressForm.valid) {\n      const formValue = {\n        ...this.addressForm.value,\n        region: this.addressForm.value.region.regionName,\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n      };\n      this.addressService.updateAddress(formValue).subscribe({\n        next: res => {\n          this.loaderService.hide();\n          if (!res.success) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ResponseMessages.address'),\n              detail: this.translate.instant(res.message)\n            });\n          } else {\n            this.isDisplaySuccessModal = true;\n            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n  }\n  OnlyNumeric(val) {\n    if (!Number(val.value)) {\n      this.addressForm.value.postcode = '';\n      this.addressForm.value.phone = '';\n    }\n    return false;\n  }\n  checkPlaceHolder() {\n    if (this.myplaceHolder) {\n      this.myplaceHolder = '';\n    } else {\n      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\n      if (this.myplaceHolder) this.myplaceHolder = this.myplaceHolder + ' 000 000 000';else this.myplaceHolder = '256 000 000 000';\n    }\n  }\n  validate() {\n    if (!this.addressForm.valid) return true;\n  }\n  setAsDefault() {\n    this.addressService.setDefault(this.id).subscribe({\n      next: res => {\n        this.messageService.add({\n          severity: 'success',\n          summary: this.translate.instant('ResponseMessages.address'),\n          detail: this.translate.instant('ResponseMessages.defaultAddressSuccessfully')\n        });\n        if (this.redirectUrl && this.redirectUrl !== '') {\n          this.router.navigate([this.redirectUrl]);\n        } else {\n          this.router.navigate(['/account/address']);\n        }\n      },\n      error: err => {\n        this.loaderService.hide();\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err\n        });\n      }\n    });\n  }\n  setCurrentLocation(selectedPosition) {\n    this.addressService.chosenAddress = null;\n    if ('geolocation' in navigator) {\n      if (!selectedPosition) {\n        navigator.geolocation.getCurrentPosition(position => {\n          this.Lat = position.coords.latitude;\n          this.Lng = position.coords.longitude;\n          this.position = [{\n            position: {\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.getAddress(this.Lat, this.Lng);\n          this.createLocationButton();\n        });\n      } else {\n        this.Lat = parseFloat(selectedPosition.lat);\n        this.Lng = parseFloat(selectedPosition.lng);\n        this.position = [{\n          position: {\n            lat: this.Lat,\n            lng: this.Lng\n          }\n        }];\n        this.getAddress(this.Lat, this.Lng);\n        this.createLocationButton();\n      }\n    }\n  }\n  mapClicked(event) {\n    let latLng = JSON.parse(JSON.stringify(event.latLng));\n    this.Lat = latLng.lat;\n    this.Lng = latLng.lng;\n    this.position = [{\n      position: {\n        lat: this.Lat,\n        lng: this.Lng\n      }\n    }];\n    this.center = this.position[0].position;\n    this.zoom = 12;\n    this.getAddress(this.Lat, this.Lng);\n  }\n  createLocationButton() {\n    if (isPlatformBrowser(this.platformId)) {\n      const controlDiv = document.createElement('div');\n      controlDiv.index = 100;\n      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\n    }\n  }\n  markerDragEnd(event) {\n    if (event.latLng != null) {\n      const latLng = event.latLng.toJSON();\n      ;\n      this.getAddress(latLng.lat, latLng.lng);\n    }\n  }\n  getCities() {\n    const reqObj = {\n      currentPage: 1,\n      pageSize: 5,\n      ignorePagination: true\n    };\n    this.addressService.getAllCities(reqObj).subscribe(res => {\n      if (res.success) {\n        this.allCities = res.data.records;\n      }\n    });\n  }\n  getCoordinates() {\n    var geocoder = new google.maps.Geocoder();\n    this.isDifferentCity = true;\n    geocoder.geocode({\n      'address': this.addressForm.controls['streetAddress'].value\n    }, (results, status) => {\n      if (status == google.maps.GeocoderStatus.OK) {\n        if (results[0].address_components.length) {\n          const city = results[0].address_components.find(item => item.types.includes('locality'));\n          if (city.long_name === this.addressDetailCity) {\n            this.isDifferentCity = false;\n            this.cd.detectChanges();\n          }\n        }\n      }\n    });\n  }\n  getAllRegion() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let reqObj = {\n        currentPage: 1,\n        pageSize: 5,\n        ignorePagination: true\n      };\n      try {\n        const res = yield _this2.addressService.getAllRegions(reqObj).toPromise();\n        if (res.success) {\n          _this2.allRegionList = res.data.records;\n          _this2.filteredCities = res.data.records[0].cities;\n        }\n      } catch (error) {\n        _this2.messageService.add({\n          severity: 'error',\n          summary: _this2.translate.instant('ErrorMessages.fetchError')\n        });\n      }\n    })();\n  }\n  filterCitiesByRegion(regionId) {\n    const selectedRegion = this.allRegionList.find(region => region.id === regionId);\n    if (selectedRegion) {\n      this.filteredCities = selectedRegion?.cities;\n    }\n  }\n  selectAddressType(type) {\n    this.selectedAddressType = type;\n    this.addressForm.patchValue({\n      addressLabel: type\n    });\n    if (this.selectedAddressType == 'Other') {\n      this.isDisplayOtherModal = true;\n    }\n  }\n  addAddress(event) {\n    if (event) {\n      this.addressForm.patchValue({\n        addressLabel: event\n      });\n      this.addressLabelList[2].name = event;\n    }\n    this.isDisplayOtherModal = false;\n  }\n  onAddressDialogCancel(event) {\n    this.isDisplayOtherModal = event;\n  }\n  get region() {\n    return this.addressForm.get('region');\n  }\n  static #_ = this.ɵfac = function AddressComponent_Factory(t) {\n    return new (t || AddressComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddressComponent,\n    selectors: [[\"app-address\"]],\n    viewQuery: function AddressComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.placesRef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchElementRef = _t.first);\n      }\n    },\n    hostBindings: function AddressComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function AddressComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 5,\n    vars: 4,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [4, \"ngIf\"], [1, \"account-page-mobile\"], [1, \"d-flex\", \"cart-mobile-new__address-layout\", \"flex-row\"], [1, \"d-inline-flex\", \"cart-mobile-new__address-layout__address-items-section\"], [1, \"header-container\"], [\"src\", \"assets/icons/mobile-icons/back-icon.svg\", \"alt\", \"back-icon\", 3, \"click\"], [1, \"header-container__header-detail\"], [1, \"complete-address\", \"mb-5\"], [1, \"col-12\"], [\"height\", \"111px\", \"width\", \"100%\", 3, \"center\", \"zoom\", \"options\", \"mapClick\", \"mapInitialized\"], [3, \"options\", \"position\", \"mapDrag\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", 3, \"formGroup\"], [1, \"form-bg\"], [1, \"d-flex\"], [\"for\", \"country\"], [\"formControlName\", \"country\", \"id\", \"country\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", \"input-address\", 3, \"ngClass\"], [1, \"col-12\", \"col-md-6\", \"mt-3\", \"region-dropdown\"], [1, \"search-container\", \"select-dropdown-merchant\", \"city-dropdown\", \"region-address\", 3, \"ngStyle\"], [1, \"product-label\", \"region-label\", 3, \"formGroup\"], [\"formControlName\", \"id\", \"optionValue\", \"id\", \"optionLabel\", \"regionName\", \"filterBy\", \"regionName\", 1, \"custom-dropdown\", 3, \"options\", \"filter\", \"onChange\"], [\"ddRegion\", \"\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [1, \"product-label\", \"region-label\"], [\"formControlName\", \"city\", \"optionValue\", \"cityName\", \"optionLabel\", \"cityName\", \"filterBy\", \"cityName\", 1, \"custom-dropdown\", 3, \"options\", \"filter\"], [\"ddCity\", \"\"], [\"for\", \"address-type\"], [\"formControlName\", \"streetAddress\", \"id\", \"address-type\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\", \"input\"], [\"formControlName\", \"additionalAddress\", \"id\", \"other-address-second\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\"], [\"for\", \"city\"], [\"formControlName\", \"postcode\", \"id\", \"postcode\", \"pInputText\", \"\", \"type\", \"number\", 1, \"form-input\", 3, \"value\"], [\"for\", \"landMark\"], [\"formControlName\", \"landMark\", \"id\", \"landMark\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\"], [\"for\", \"receiverPhoneNumber\"], [1, \"d-flex\", \"contact-address\", \"flex-column\"], [\"formControlName\", \"receiverPhoneNumber\", \"name\", \"receiverPhoneNumber\", \"id\", \"receiverPhoneNumber\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"maxLength\", \"numberFormat\", \"phoneValidation\", \"preferredCountries\", \"searchCountryField\", \"searchCountryFlag\", \"selectFirstCountry\", \"selectedCountryISO\", \"separateDialCode\", \"customPlaceholder\", \"ngClass\"], [\"class\", \"error-msg\", 4, \"ngIf\"], [\"for\", \"instructions\"], [\"formControlName\", \"deliveryInstructions\", \"id\", \"instructions\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\"], [1, \"col-12\", \"col-md-12\", \"mt-3\", \"form-bg\"], [1, \"p-float-label\", \"w-full\"], [1, \"search-container\", \"select-dropdown-merchant\", \"bg-address\"], [1, \"product-label\", \"bg-address\"], [1, \"options-address-list\", \"d-flex\"], [\"class\", \"custom-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\"], [\"class\", \"col-12 col-md-6 mt-3\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-6\", \"button-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"defaultBtn\", \"w-100\", \"add-address-btn\", 3, \"disabled\", \"label\", \"click\"], [\"class\", \"col-12 col-md-6 mt-3 button-address\", 4, \"ngIf\"], [3, \"options\", \"position\", \"mapDrag\"], [1, \"\"], [1, \"search-name\"], [1, \"error-msg\"], [1, \"custom-option\", 3, \"click\"], [\"alt\", \"Home Icon\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Work Icon\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Home Icon\", 3, \"src\"], [\"alt\", \"Work Icon\", 3, \"src\"], [1, \"col-12\", \"col-md-6\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"add-address-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"col-12\", \"col-md-6\", \"mt-3\", \"button-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"confirmBtn\", 3, \"disabled\", \"label\", \"click\"], [1, \"address\", 3, \"ngClass\"], [1, \"breadcrumb-address\", \"d-flex\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-home\", \"cursor-pointer\", 3, \"routerLink\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-angle-right\"], [3, \"routerLink\"], [1, \"content-container\", \"mt-5\", 2, \"padding-top\", \"1px\", \"max-width\", \"1057px\"], [\"class\", \"add-address\", \"style\", \"font-family: var(--medium-font)\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-12\", \"mb-4\"], [1, \"address-card\", \"shadow-1\", \"p-3\", \"mb-4\"], [1, \"p-inputgroup\", \"search-group\"], [1, \"p-inputgroup-addon\", \"icon-bg\"], [1, \"pi\", \"pi-search\"], [\"ngx-gp-autocomplete\", \"\", 1, \"map-search\", 3, \"placeholder\", \"onAddressChange\"], [\"placesRef\", \"ngx-places\", \"search\", \"\"], [1, \"p-inputgroup-addon\", \"icon-bg\", 3, \"click\"], [1, \"pi\", \"pi-times-circle\"], [\"height\", \"224px\", \"width\", \"100%\", 3, \"center\", \"zoom\", \"options\", \"mapClick\", \"mapInitialized\"], [1, \"search-container\", \"select-dropdown-merchant\", \"city-dropdown\", 3, \"ngStyle\"], [1, \"product-label\", 3, \"formGroup\"], [1, \"product-label\"], [1, \"p-float-label\", \"w-full\", \"contact-input-phone-container\"], [\"formControlName\", \"country\", \"id\", \"country\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", 3, \"ngClass\"], [1, \"col-12\", \"col-md-12\", \"mt-0\", \"form-bg\"], [1, \"select-dropdown-merchant\", \"bg-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"defaultBtn\", \"w-100\", 3, \"disabled\", \"label\", \"click\"], [1, \"add-address\", 2, \"font-family\", \"var(--medium-font)\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [3, \"displayModal\", \"message\", \"submit\"], [3, \"displayModal\", \"submit\", \"cancel\"]],\n    template: function AddressComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AddressComponent_ng_container_0_Template, 99, 92, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, AddressComponent_ng_template_1_Template, 109, 106, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(3, AddressComponent_ng_container_3_Template, 2, 2, \"ng-container\", 2);\n        i0.ɵɵtemplate(4, AddressComponent_ng_container_4_Template, 2, 1, \"ng-container\", 2);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isDisplaySuccessModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isDisplayOtherModal);\n      }\n    },\n    dependencies: [i2.PrimeTemplate, i7.InputText, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.FormGroupDirective, i8.FormControlName, i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i9.ButtonDirective, i3.RouterLink, i10.SuccessModalComponent, i11.NgxGpAutocompleteDirective, i12.GoogleMap, i12.MapMarker, i13.Dropdown, i14.NgxIntlTelInputComponent, i14.NativeElementInjectorDirective, i15.AddressLabelComponent, i4.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.address[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n}\\n.address[_ngcontent-%COMP%]   .address-card[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n  width: 100%;\\n  background-color: #FFFFFF;\\n  border-radius: 5px;\\n}\\n.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .map-search[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: #F5F5F5 !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  padding: 13px 5px;\\n  font-size: 15px;\\n  color: black;\\n  font-family: var(--regular-font);\\n  border-top: 1px solid #ced4da !important;\\n  border-bottom: 1px solid #ced4da !important;\\n  border-left: unset;\\n  border-right: unset;\\n}\\n.address[_ngcontent-%COMP%]   .search-group[_ngcontent-%COMP%]   .icon-bg[_ngcontent-%COMP%] {\\n  background-color: #F5F5F5 !important;\\n  cursor: pointer;\\n}\\n.address[_ngcontent-%COMP%]   .map[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 224px;\\n  border: 1px solid #ced4da;\\n  border-radius: 5px;\\n}\\n.address[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  background-color: #F5F5F5 !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  padding: 22px 10px 13px;\\n  font-size: 16px;\\n  color: black;\\n  border-bottom: 1.5px solid #AEAEAE;\\n  font-family: var(--regular-font);\\n}\\n.address[_ngcontent-%COMP%]   agm-map[_ngcontent-%COMP%] {\\n  height: 300px;\\n}\\n\\n[_nghost-%COMP%]     .address .p-float-label label {\\n  top: 30px;\\n  font-size: 11px;\\n  color: #323232;\\n  font-weight: 500;\\n  font-family: var(--medium-font);\\n}\\n[_nghost-%COMP%]     .address .p-float-label input ~ label {\\n  top: 15px !important;\\n}\\n[_nghost-%COMP%]     .address .p-float-label.contact-input-phone-container label {\\n  top: 20px;\\n}\\n\\n.button-address[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n.button-address[_ngcontent-%COMP%]   .defaultBtn[_ngcontent-%COMP%] {\\n  text-align: center;\\n  letter-spacing: 0px;\\n  color: var(--header_bgcolor);\\n  text-transform: uppercase;\\n  background: transparent;\\n  border: 1px solid var(--header_bgcolor);\\n  padding: 10px 20px;\\n  font-size: 15px;\\n  border-radius: 25px;\\n  width: 70%;\\n  outline: 0 none;\\n  font-family: var(--medium-font);\\n}\\n.button-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: var(--header_bgcolor);\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  border: 1px solid var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n  outline: 0 none;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  background-color: #F5F5F5;\\n  border-radius: 1px;\\n  padding: 8px 16px;\\n  height: 60px;\\n  border-bottom: 1.5px solid #AEAEAE;\\n}\\n@media only screen and (max-width: 767px) {\\n  .search-container[_ngcontent-%COMP%] {\\n    border-bottom: none !important;\\n  }\\n}\\n\\n.select-dropdown-merchant[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-filter-container input {\\n  border: 1px solid #ced4da !important;\\n}\\n.city-dropdown[_ngcontent-%COMP%]     .p-dropdown-items-wrapper ul {\\n  padding-left: 0;\\n}\\n\\n.product-label[_ngcontent-%COMP%] {\\n  background-color: #F5F5F5;\\n  color: #323232;\\n  font-family: var(--medium-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n@media only screen and (max-width: 767px) {\\n  .product-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    font-weight: 300;\\n    font-family: var(--regular-font);\\n  }\\n}\\n\\n.custom-dropdown[_ngcontent-%COMP%]     .p-dropdown .p-dropdown-label {\\n  background: transparent;\\n  border: 0 none;\\n  padding: 0;\\n  font-family: var(--medium-font);\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 14px;\\n  line-height: 16px;\\n  \\n\\n  color: #323232;\\n}\\n\\n@media screen and (max-width: 720px) {\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    padding-top: 0rem !important;\\n  }\\n  .address[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n}\\n.breadcrumb-address[_ngcontent-%COMP%] {\\n  background-color: #efeded;\\n  padding: 1rem 2rem;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 15px;\\n}\\n\\n.add-address[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {\\n  color: #495057;\\n  background: none !important;\\n  width: 100%;\\n}\\n\\n.search-name[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #323232;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dropdown-items-wrapper {\\n  background-color: #F5F5F5;\\n}\\n@media only screen and (max-width: 767px) {\\n    .p-dropdown-items-wrapper {\\n    margin-top: 0px !important;\\n  }\\n}\\n\\n.select-dropdown-merchant[_ngcontent-%COMP%]     .p-overlay.p-component {\\n  min-width: 784px !important;\\n  transform-origin: center top;\\n  top: 17px !important;\\n  left: 0px;\\n  margin-left: -15px;\\n}\\n\\n.city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component {\\n  min-width: 380px !important;\\n  transform-origin: center top;\\n  top: 17px !important;\\n  left: 0px;\\n  margin-left: -15px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .city-dropdown[_ngcontent-%COMP%]     .p-overlay.p-component {\\n    min-width: 100% !important;\\n    margin-left: 0px !important;\\n  }\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-transform: uppercase;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n  .custom-map-control-button {\\n  cursor: pointer !important;\\n}\\n\\n.account-page-mobile[_ngcontent-%COMP%] {\\n  margin-top: 75px !important;\\n}\\n\\n.cart-mobile-new__address-layout__address-items-section[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  border: 1px solid #E4E7E9;\\n  border-radius: 4px;\\n  flex-direction: column;\\n  background: #F6F6F6;\\n}\\n\\n.header-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #2D2D2D;\\n}\\n\\n.complete-address[_ngcontent-%COMP%] {\\n  max-width: 1057px;\\n  padding: 16px;\\n  padding-top: 0px;\\n}\\n\\n.form-bg[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: white;\\n  width: 100%;\\n  padding: 16px;\\n  border-radius: 8px;\\n}\\n\\n.input-address[_ngcontent-%COMP%] {\\n  padding: 0px !important;\\n}\\n\\n.region-address[_ngcontent-%COMP%] {\\n  padding: 12px !important;\\n  background: white;\\n  align-content: center;\\n  padding-left: 13px !important;\\n  border: none;\\n}\\n\\n.region-label[_ngcontent-%COMP%] {\\n  background: white !important;\\n}\\n\\n.region-dropdown[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%]:enabled:hover {\\n  border: none !important;\\n  width: 100%;\\n  box-shadow: none !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  input[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 0px !important;\\n    font-family: var(--regular-font);\\n  }\\n  label[_ngcontent-%COMP%] {\\n    font-family: var(--regular-font);\\n    font-size: 10px;\\n    font-weight: 300;\\n  }\\n}\\n.custom-option[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-radius: 4px;\\n  border: 1px solid var(--primary, #204E6E);\\n  padding: 4px 6px;\\n  min-width: 75px;\\n  text-align: center;\\n  width: auto;\\n}\\n\\n.custom-option.selected[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  background: var(--primary, #204E6E);\\n  color: white;\\n}\\n\\n.bg-address[_ngcontent-%COMP%] {\\n  background: #FFF;\\n  padding-left: 0px;\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-weight: 300;\\n}\\n\\n.options-address-list[_ngcontent-%COMP%] {\\n  gap: 10px;\\n  margin-top: 10px;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .contact-address[_ngcontent-%COMP%]     .contact-input-phone {\\n    border-bottom: none !important;\\n    background-color: white !important;\\n    height: 40px !important;\\n  }\\n    .map-container {\\n    border-radius: 12px;\\n    box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.1);\\n    border: 1px solid #FFF;\\n  }\\n}\\n.add-address-btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px !important;\\n  border-radius: 8px !important;\\n  text-transform: capitalize !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "UntypedFormControl", "UntypedFormGroup", "Validators", "isPlatformBrowser", "CountryISO", "PhoneNumberFormat", "SearchCountryField", "TenantRecords", "i0", "ɵɵelementStart", "ɵɵlistener", "AddressComponent_ng_container_0_map_marker_13_Template_map_marker_mapDrag_0_listener", "$event", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "markerDragEnd", "ɵɵelementEnd", "ɵɵproperty", "ctx_r5", "markerOptions", "marker_r16", "position", "ɵɵtext", "ɵɵtextInterpolate1", "item_r19", "regionName", "ɵɵadvance", "region_r20", "item_r21", "cityName", "car_r22", "ɵɵpipeBind1", "ɵɵelement", "ctx_r24", "selectedAddressType", "ɵɵsanitizeUrl", "ctx_r25", "AddressComponent_ng_container_0_div_92_Template_div_click_0_listener", "restoredCtx", "_r27", "addressType_r23", "$implicit", "ctx_r26", "selectAddressType", "name", "ɵɵtemplate", "AddressComponent_ng_container_0_div_92_img_1_Template", "AddressComponent_ng_container_0_div_92_img_2_Template", "ɵɵclassProp", "ctx_r13", "AddressComponent_ng_container_0_div_94_Template_button_click_1_listener", "_r29", "ctx_r28", "onSubmit", "ctx_r14", "validate", "AddressComponent_ng_container_0_div_98_Template_button_click_1_listener", "_r31", "ctx_r30", "Update", "ctx_r15", "ɵɵelementContainerStart", "AddressComponent_ng_container_0_Template_img_click_6_listener", "_r33", "ctx_r32", "onBack", "AddressComponent_ng_container_0_Template_google_map_mapClick_12_listener", "ctx_r34", "mapClicked", "AddressComponent_ng_container_0_Template_google_map_mapInitialized_12_listener", "ctx_r35", "mapInitialize", "AddressComponent_ng_container_0_map_marker_13_Template", "AddressComponent_ng_container_0_Template_p_dropdown_onChange_28_listener", "ctx_r36", "filterCitiesByRegion", "value", "AddressComponent_ng_container_0_ng_template_30_Template", "AddressComponent_ng_container_0_ng_template_31_Template", "AddressComponent_ng_container_0_ng_template_40_Template", "AddressComponent_ng_container_0_ng_template_41_Template", "AddressComponent_ng_container_0_Template_input_input_48_listener", "ctx_r37", "getCoordinates", "AddressComponent_ng_container_0_span_77_Template", "AddressComponent_ng_container_0_div_92_Template", "AddressComponent_ng_container_0_div_94_Template", "AddressComponent_ng_container_0_Template_button_click_96_listener", "ctx_r38", "setAsDefault", "AddressComponent_ng_container_0_div_98_Template", "ɵɵelementContainerEnd", "ctx_r0", "center", "zoom", "mapOptions", "addressForm", "ɵɵpureFunction1", "_c2", "tmp_7_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "touched", "_c3", "tmp_8_0", "region", "ɵɵstyleMap", "ɵɵpureFunction0", "_c4", "allRegionList", "tmp_14_0", "filteredCities", "tmp_20_0", "ɵɵtextInterpolate", "postcode", "tmp_25_0", "phoneInputLength", "National", "preferredCountries", "ɵɵpureFunction2", "_c5", "Iso2", "Name", "CustomCountryISO", "customPlaceHolder", "tmp_40_0", "tmp_41_0", "addressLabelList", "addressService", "<PERSON><PERSON><PERSON><PERSON>", "isDefault", "id", "AddressComponent_ng_template_1_map_marker_30_Template_map_marker_mapDrag_0_listener", "_r56", "ctx_r55", "ctx_r43", "marker_r54", "item_r57", "region_r58", "item_r59", "car_r60", "ctx_r62", "ctx_r63", "AddressComponent_ng_template_1_div_102_Template_div_click_0_listener", "_r65", "addressType_r61", "ctx_r64", "AddressComponent_ng_template_1_div_102_img_1_Template", "AddressComponent_ng_template_1_div_102_img_2_Template", "ctx_r51", "AddressComponent_ng_template_1_div_107_Template_button_click_1_listener", "_r67", "ctx_r66", "ctx_r52", "AddressComponent_ng_template_1_div_108_Template_button_click_1_listener", "_r69", "ctx_r68", "ctx_r53", "AddressComponent_ng_template_1_h2_12_Template", "AddressComponent_ng_template_1_h2_13_Template", "AddressComponent_ng_template_1_Template_input_onAddressChange_22_listener", "_r71", "ctx_r70", "handleAddressChange", "AddressComponent_ng_template_1_Template_span_click_26_listener", "ctx_r72", "clear", "AddressComponent_ng_template_1_Template_google_map_mapClick_29_listener", "ctx_r73", "AddressComponent_ng_template_1_Template_google_map_mapInitialized_29_listener", "ctx_r74", "AddressComponent_ng_template_1_map_marker_30_Template", "AddressComponent_ng_template_1_Template_p_dropdown_onChange_38_listener", "ctx_r75", "AddressComponent_ng_template_1_ng_template_40_Template", "AddressComponent_ng_template_1_ng_template_41_Template", "AddressComponent_ng_template_1_ng_template_50_Template", "AddressComponent_ng_template_1_ng_template_51_Template", "AddressComponent_ng_template_1_span_61_Template", "AddressComponent_ng_template_1_Template_input_input_73_listener", "ctx_r76", "AddressComponent_ng_template_1_div_102_Template", "AddressComponent_ng_template_1_Template_button_click_105_listener", "ctx_r77", "AddressComponent_ng_template_1_div_107_Template", "AddressComponent_ng_template_1_div_108_Template", "_c6", "ctx_r2", "navbarData", "isActive", "ɵɵpropertyInterpolate", "valid", "tmp_43_0", "tmp_45_0", "tmp_48_0", "AddressComponent_ng_container_3_Template_app_mtn_success_modal_submit_1_listener", "_r79", "ctx_r78", "onConfrim", "ctx_r3", "isDisplaySuccessModal", "message", "AddressComponent_ng_container_4_Template_app_address_label_submit_1_listener", "_r81", "ctx_r80", "addAddress", "AddressComponent_ng_container_4_Template_app_address_label_cancel_1_listener", "ctx_r82", "onAddressDialogCancel", "ctx_r4", "isDisplayOtherModal", "AddressComponent", "onResize", "event", "platformId", "screenWidth", "window", "innerWidth", "constructor", "ngZone", "messageService", "router", "store", "route", "translate", "mainDataService", "loaderService", "_location", "cd", "permissionService", "appDataService", "$gtmService", "draggable", "icon", "phoneLength", "landMarkAddressRequired", "isMobileTemplate", "addressDetailCity", "addressLabel", "receiverFirstName", "receiverLastName", "streetAddress", "required", "country", "city", "landMark", "deliveryInstructions", "buldingNumber", "receiverPhoneNumber", "geo_location", "Lat", "Lng", "Id", "additional<PERSON>ddress", "search", "lat", "lng", "fullscreenControl", "disableDefaultUI", "allCities", "isDifferentCity", "geoCoder", "google", "maps", "Geocoder", "borderBottomStyle", "Uganda", "Ghana", "CôteDIvoire", "selectedCitiesValue", "hasPermission", "source", "getCurrentNavigation", "extras", "state", "tenantId", "localStorage", "getItem", "back", "ngOnInit", "googleResponse", "results", "<PERSON><PERSON><PERSON><PERSON>", "find", "result", "types", "includes", "address", "formatted_address", "console", "log", "queryParamMap", "subscribe", "queryParams", "routeToCheckOut", "layoutTemplate", "section", "type", "history", "params", "redirectUrl", "returnUrl", "routeSub", "getCustomerAddress", "pushPageView", "defaultPhoneNumber", "countryPhone", "replace", "phoneNumberWithoutCode", "setValue", "mark<PERSON>llAsTouched", "getAllRegion", "setCurrentLocation", "toString", "landMarkAddress", "controls", "setValidators", "updateValueAndValidity", "parseInt", "userDetails", "setUserData", "split", "patchValue", "tenants", "records", "undefined", "data", "arr", "element", "setItem", "isoCode", "set", "configuration", "item", "key", "valueChanges", "selectedRegion", "r", "emitEvent", "map", "place", "geometry", "location", "get<PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "show", "res", "getAddressById", "to<PERSON>romise", "loadedAddress", "trimmedPhoneNumber", "trimPhoneNumber", "selectedCity", "postCode", "parseFloat", "searchElementRef", "nativeElement", "createLocationButton", "hide", "err", "add", "severity", "summary", "instant", "detail", "phoneNumber", "length", "substring", "cities", "latitude", "longitude", "geocode", "status", "address_components", "long_name", "detectChanges", "alert", "formValue", "e164Number", "slice", "next", "success", "error", "navigateByUrl", "navigate", "updateAddress", "OnlyNumeric", "val", "Number", "phone", "checkPlaceHolder", "myplaceHolder", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "navigator", "geolocation", "getCurrentPosition", "coords", "latLng", "JSON", "parse", "stringify", "controlDiv", "document", "createElement", "index", "ControlPosition", "RIGHT_BOTTOM", "push", "toJSON", "getCities", "req<PERSON>bj", "currentPage", "pageSize", "ignorePagination", "getAllCities", "geocoder", "GeocoderStatus", "OK", "_this2", "getAllRegions", "regionId", "_", "ɵɵdirectiveInject", "NgZone", "i1", "AddressService", "i2", "MessageService", "i3", "Router", "StoreService", "ActivatedRoute", "i4", "TranslateService", "MainDataService", "LoaderService", "i5", "Location", "ChangeDetectorRef", "PermissionService", "AppDataService", "i6", "GTMService", "_2", "selectors", "viewQuery", "AddressComponent_Query", "rf", "ctx", "ɵɵresolveWindow", "AddressComponent_ng_container_0_Template", "AddressComponent_ng_template_1_Template", "ɵɵtemplateRefExtractor", "AddressComponent_ng_container_3_Template", "AddressComponent_ng_container_4_Template", "_r1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\address\\address.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\address\\address.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef,\r\n  HostListener,\r\n  Inject,\r\n  <PERSON><PERSON><PERSON>,\r\n  OnInit,\r\n  PLATFORM_ID,\r\n  ViewChild,\r\n} from '@angular/core';\r\n\r\nimport {UntypedFormControl, UntypedFormGroup, Validators,} from '@angular/forms';\r\nimport {MessageService} from 'primeng/api';\r\nimport {ActivatedRoute, Router} from '@angular/router';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {NgxGpAutocompleteDirective} from \"@angular-magic/ngx-gp-autocomplete\";\r\n\r\nimport {\r\n  LoaderService,\r\n  AddressService,\r\n  StoreService,\r\n  MainDataService,\r\n  AppDataService,\r\n  PermissionService\r\n} from \"@core/services\";\r\n\r\ndeclare const google: any;\r\nimport {isPlatformBrowser, Location} from '@angular/common';\r\nimport {CountryISO, PhoneNumberFormat, SearchCountryField} from \"ngx-intl-tel-input-gg\";\r\nimport {TenantRecords} from \"@core/interface\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-address',\r\n  templateUrl: './address.component.html',\r\n  styleUrls: ['./address.component.scss'],\r\n})\r\nexport class AddressComponent implements OnInit {\r\n  routeToCheckOut:any;\r\n  markerOptions: any = { draggable: true,icon:'assets/images/map-pin.svg' };\r\n  latitude?: number;\r\n  longitude?: number;\r\n  phoneLength: number = 13;\r\n  position: any\r\n  landMarkAddressRequired: boolean = false;\r\n  isMobileTemplate:boolean=false;\r\n  isDisplaySuccessModal: boolean = false;\r\n  isDisplayOtherModal:boolean=false;\r\n  message: string ='';\r\n  addressDetailCity : string = '';\r\n  @ViewChild(\"placesRef\") placesRef: NgxGpAutocompleteDirective;\r\n \r\n  addressForm = new UntypedFormGroup({\r\n    addressLabel: new UntypedFormControl('Home'),\r\n    receiverFirstName: new UntypedFormControl(''),\r\n    receiverLastName: new UntypedFormControl(''),\r\n    streetAddress: new UntypedFormControl('', Validators.required),\r\n    country: new UntypedFormControl('', Validators.required),\r\n    city: new UntypedFormControl('', Validators.required),\r\n    landMark: new UntypedFormControl('', Validators.required ),\r\n    deliveryInstructions: new UntypedFormControl(''),\r\n    buldingNumber: new UntypedFormControl(''),\r\n    postcode: new UntypedFormControl(''),\r\n    receiverPhoneNumber: new UntypedFormControl('', Validators.required),\r\n    geo_location: new UntypedFormControl(''),\r\n    Lat: new UntypedFormControl(''),\r\n    Lng: new UntypedFormControl(''),\r\n    Id: new UntypedFormControl(''),\r\n    additionalAddress:new UntypedFormControl(''),\r\n    region: new UntypedFormGroup({\r\n      id: new UntypedFormControl(''),\r\n      regionName: new UntypedFormControl('')\r\n    },Validators.required)\r\n  });\r\n  search: string = '';\r\n  Lat!: number;\r\n  Lng!: number;\r\n  zoom!: number;\r\n  address!: string;\r\n  myplaceHolder: string | undefined;\r\n  source: any;\r\n  isDefault = false;\r\n  navbarData:any;\r\n  center: google.maps.LatLngLiteral = {\r\n    lat: 0.3,\r\n    lng: 32.5\r\n  };\r\n  mapOptions = {\r\n    fullscreenControl: false,\r\n    disableDefaultUI: true\r\n  };\r\n  allCities : any = [];\r\n  isDifferentCity: boolean = false;\r\n  public addressLabelList = [\r\n    {'name': 'Home', 'id': 1},{'name': 'Work', 'id': 2},{'name': 'Other', 'id': 3}\r\n\r\n  ]\r\n  selectedAddressType: any;\r\n  @ViewChild('search')\r\n  public searchElementRef!: ElementRef;\r\n  id: string = '';\r\n  routeSub: any;\r\n  private geoCoder: any = new google.maps.Geocoder();\r\n  private redirectUrl: string;\r\n  map: google.maps.Map;\r\n  borderBottomStyle = '2px solid red !important';\r\n  phoneInputLength: number = 12;\r\n  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\r\n  CustomCountryISO: any;\r\n  customPlaceHolder: any = '';\r\n  PhoneNumberFormat = PhoneNumberFormat;\r\n  SearchCountryField = SearchCountryField;\r\n  allRegionList : any = [];\r\n  filteredCities: any[] = [];\r\n  screenWidth:any=window.innerWidth;\r\n  lat:string;\r\n  lng:string\r\n  selectedCitiesValue : any = []\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n  constructor(\r\n    private ngZone: NgZone,\r\n    public addressService: AddressService,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private store: StoreService,\r\n    private route: ActivatedRoute,\r\n    private translate: TranslateService,\r\n    private mainDataService: MainDataService,\r\n    private loaderService: LoaderService,\r\n     private _location: Location,\r\n    private cd : ChangeDetectorRef,\r\n    private permissionService: PermissionService,\r\n    private appDataService: AppDataService,\r\n    private $gtmService:GTMService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n  ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.source = this.router.getCurrentNavigation()?.extras?.state;\r\n\r\n    let tenantId = localStorage.getItem('tenantId');\r\n    if(tenantId && tenantId !== '') {\r\n      if(tenantId == '1') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      } else if(tenantId == '2') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      }else if(tenantId == '3') {\r\n        this.customPlaceHolder = 'XXXXXXXXX';\r\n      }else if(tenantId == '4') {\r\n        this.customPlaceHolder = 'XXXXXXXXXX';\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  onBack(){\r\n    this._location.back();\r\n  }\r\n\r\n  ngOnInit() {\r\n    const googleResponse  = {\r\n      results: \r\n      [\r\n        {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Cocody\",\r\n                  \"short_name\": \"Cocody\",\r\n                  \"types\": [\r\n                      \"political\",\r\n                      \"sublocality\",\r\n                      \"sublocality_level_1\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"District Autonome d'Abidjan\",\r\n                  \"short_name\": \"District Autonome d'Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"99326, Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"location\": {\r\n                  \"lat\": 5.361162999999999,\r\n                  \"lng\": -3.9977942\r\n              },\r\n              \"location_type\": \"GEOMETRIC_CENTER\",\r\n              \"viewport\": {\r\n                  \"south\": 5.359814019708497,\r\n                  \"west\": -3.999143180291502,\r\n                  \"north\": 5.362511980291502,\r\n                  \"east\": -3.996445219708498\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJYXXkgbbrwQ8R6iRpzKnEIw8\",\r\n          \"plus_code\": {\r\n              \"compound_code\": \"9262+FV Abidjan, Côte d'Ivoire\",\r\n              \"global_code\": \"6CQR9262+FV\"\r\n          },\r\n          \"types\": [\r\n              \"establishment\",\r\n              \"point_of_interest\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Espace 168\",\r\n                  \"short_name\": \"Espace 168\",\r\n                  \"types\": [\r\n                      \"premise\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Boulevard des Martyrs\",\r\n                  \"short_name\": \"Bd des Martyrs\",\r\n                  \"types\": [\r\n                      \"route\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Cocody\",\r\n                  \"short_name\": \"Cocody\",\r\n                  \"types\": [\r\n                      \"political\",\r\n                      \"sublocality\",\r\n                      \"sublocality_level_1\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"District Autonome d'Abidjan\",\r\n                  \"short_name\": \"District Autonome d'Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Espace 168, Bd des Martyrs, Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"location\": {\r\n                  \"lat\": 5.361365,\r\n                  \"lng\": -3.9979289\r\n              },\r\n              \"location_type\": \"ROOFTOP\",\r\n              \"viewport\": {\r\n                  \"south\": 5.360016019708498,\r\n                  \"west\": -3.999277880291503,\r\n                  \"north\": 5.362713980291502,\r\n                  \"east\": -3.996579919708498\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJDSB1NV3rwQ8RUoJ6o5QKBw4\",\r\n          \"types\": [\r\n              \"premise\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"9262+GW\",\r\n                  \"short_name\": \"9262+GW\",\r\n                  \"types\": [\r\n                      \"plus_code\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_2\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan Autonomous District\",\r\n                  \"short_name\": \"Abidjan Autonomous District\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"9262+GW Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 5.36125,\r\n                  \"west\": -3.99775,\r\n                  \"north\": 5.361375,\r\n                  \"east\": -3.997625\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 5.3613569,\r\n                  \"lng\": -3.9976294\r\n              },\r\n              \"location_type\": \"GEOMETRIC_CENTER\",\r\n              \"viewport\": {\r\n                  \"south\": 5.359963519708498,\r\n                  \"west\": -3.999036480291502,\r\n                  \"north\": 5.362661480291503,\r\n                  \"east\": -3.996338519708497\r\n              }\r\n          },\r\n          \"place_id\": \"GhIJW7wOiwdyFUARjjl0HyX7D8A\",\r\n          \"plus_code\": {\r\n              \"compound_code\": \"9262+GW Abidjan, Côte d'Ivoire\",\r\n              \"global_code\": \"6CQR9262+GW\"\r\n          },\r\n          \"types\": [\r\n              \"plus_code\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Boulevard des Martyrs\",\r\n                  \"short_name\": \"Bd des Martyrs\",\r\n                  \"types\": [\r\n                      \"route\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Cocody\",\r\n                  \"short_name\": \"Cocody\",\r\n                  \"types\": [\r\n                      \"political\",\r\n                      \"sublocality\",\r\n                      \"sublocality_level_1\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"District Autonome d'Abidjan\",\r\n                  \"short_name\": \"District Autonome d'Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Bd des Martyrs, Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 5.3607096,\r\n                  \"west\": -3.997825,\r\n                  \"north\": 5.362436799999999,\r\n                  \"east\": -3.9975\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 5.3615357,\r\n                  \"lng\": -3.9975106\r\n              },\r\n              \"location_type\": \"GEOMETRIC_CENTER\",\r\n              \"viewport\": {\r\n                  \"south\": 5.360224219708497,\r\n                  \"west\": -3.999011480291502,\r\n                  \"north\": 5.362922180291502,\r\n                  \"east\": -3.996313519708497\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJk80SNF3rwQ8RMLvuyNHzvac\",\r\n          \"types\": [\r\n              \"route\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Cocody\",\r\n                  \"short_name\": \"Cocody\",\r\n                  \"types\": [\r\n                      \"political\",\r\n                      \"sublocality\",\r\n                      \"sublocality_level_1\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_2\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan Autonomous District\",\r\n                  \"short_name\": \"Abidjan Autonomous District\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Cocody, Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 5.3177369,\r\n                  \"west\": -4.0176456,\r\n                  \"north\": 5.4606275,\r\n                  \"east\": -3.9165417\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 5.3602164,\r\n                  \"lng\": -3.9674371\r\n              },\r\n              \"location_type\": \"APPROXIMATE\",\r\n              \"viewport\": {\r\n                  \"south\": 5.3177369,\r\n                  \"west\": -4.0176456,\r\n                  \"north\": 5.4606275,\r\n                  \"east\": -3.9165417\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJ_-kvynLtwQ8RskjN5omhEcU\",\r\n          \"types\": [\r\n              \"political\",\r\n              \"sublocality\",\r\n              \"sublocality_level_1\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"locality\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan\",\r\n                  \"short_name\": \"Abidjan\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_2\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Abidjan Autonomous District\",\r\n                  \"short_name\": \"Abidjan Autonomous District\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Abidjan, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 5.2334876,\r\n                  \"west\": -4.124071499999999,\r\n                  \"north\": 5.4634126,\r\n                  \"east\": -3.8352585\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 5.3252258,\r\n                  \"lng\": -4.019603\r\n              },\r\n              \"location_type\": \"APPROXIMATE\",\r\n              \"viewport\": {\r\n                  \"south\": 5.2334876,\r\n                  \"west\": -4.124071499999999,\r\n                  \"north\": 5.4634126,\r\n                  \"east\": -3.8352585\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJIZGVEVPqwQ8RpiGS4dwN5z8\",\r\n          \"types\": [\r\n              \"locality\",\r\n              \"political\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Abidjan Autonomous District\",\r\n                  \"short_name\": \"Abidjan Autonomous District\",\r\n                  \"types\": [\r\n                      \"administrative_area_level_1\",\r\n                      \"political\"\r\n                  ]\r\n              },\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Abidjan Autonomous District, Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 5.218829299999999,\r\n                  \"west\": -4.4611351,\r\n                  \"north\": 5.6050827,\r\n                  \"east\": -3.7163464\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 5.463496,\r\n                  \"lng\": -4.1513764\r\n              },\r\n              \"location_type\": \"APPROXIMATE\",\r\n              \"viewport\": {\r\n                  \"south\": 5.218829299999999,\r\n                  \"west\": -4.4611351,\r\n                  \"north\": 5.6050827,\r\n                  \"east\": -3.7163464\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJR0gb9_2UwQ8RiMwuqKxgDgY\",\r\n          \"types\": [\r\n              \"administrative_area_level_1\",\r\n              \"political\"\r\n          ]\r\n      },\r\n      {\r\n          \"address_components\": [\r\n              {\r\n                  \"long_name\": \"Côte d'Ivoire\",\r\n                  \"short_name\": \"CI\",\r\n                  \"types\": [\r\n                      \"country\",\r\n                      \"political\"\r\n                  ]\r\n              }\r\n          ],\r\n          \"formatted_address\": \"Côte d'Ivoire\",\r\n          \"geometry\": {\r\n              \"bounds\": {\r\n                  \"south\": 4.193,\r\n                  \"west\": -8.6020589,\r\n                  \"north\": 10.7410946,\r\n                  \"east\": -2.493031\r\n              },\r\n              \"location\": {\r\n                  \"lat\": 7.539988999999999,\r\n                  \"lng\": -5.547079999999999\r\n              },\r\n              \"location_type\": \"APPROXIMATE\",\r\n              \"viewport\": {\r\n                  \"south\": 4.193,\r\n                  \"west\": -8.6020589,\r\n                  \"north\": 10.7410946,\r\n                  \"east\": -2.493031\r\n              }\r\n          },\r\n          \"place_id\": \"ChIJZ6BJsIcTlg8RQO65fGIe-QE\",\r\n          \"types\": [\r\n              \"country\",\r\n              \"political\"\r\n          ]\r\n      }\r\n      ],\r\n    };\r\n    const selectedAddress = googleResponse.results.find((result: any) => \r\n      result.types && result.types.includes(\"premise1\")\r\n    );\r\n    \r\n    this.address = selectedAddress \r\n      ? selectedAddress.formatted_address \r\n      : googleResponse.results[0]?.formatted_address;\r\n   \r\n    console.log(this.address);\r\n    this.route.queryParamMap.subscribe((queryParams) => {\r\n      this.routeToCheckOut = queryParams.get(\"checkout\");\r\n    });\r\n\r\n    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n    this.lat=history.state.lat;\r\n    this.lng=history.state.lng;\r\n    this.route.queryParams.subscribe((params: any) => {\r\n      console.log('parr',params)\r\n\r\n      this.redirectUrl = params.returnUrl;\r\n    });\r\n\r\n    this.routeSub = this.route.params.subscribe((params) => {\r\n\r\n      this.id = params['id'];\r\n      if (this.id != 'add-address'){\r\n        this.getCustomerAddress();\r\n        this.$gtmService.pushPageView('Your Addresses', 'Update Address')\r\n      }\r\n      else{\r\n        this.$gtmService.pushPageView('Your Addresses', 'Add Address')\r\n        const defaultPhoneNumber = localStorage.getItem('userPhone');\r\n        const countryPhone = localStorage.getItem('CountryPhone')?.replace('+','') || '';\r\n        const phoneNumberWithoutCode = defaultPhoneNumber?.replace(countryPhone,'')\r\n        this.addressForm.get('receiverPhoneNumber')?.setValue(phoneNumberWithoutCode)\r\n        this.addressForm.get('receiverPhoneNumber')?.markAllAsTouched();\r\n\r\n         this.getAllRegion();\r\n        if(this.lat && this.lng) {\r\n          this.setCurrentLocation({lat: this.lat, lng: this.lng});\r\n        }\r\n        else{\r\n          this.setCurrentLocation();\r\n        }\r\n\r\n      }\r\n    });\r\n\r\n    let phoneLength = localStorage.getItem('PhoneLength')?.toString();\r\n    let landMarkAddress = localStorage\r\n      .getItem('customerAddressLandmarkRequired')\r\n      ?.toString();\r\n    if (landMarkAddress && landMarkAddress == 'True') {\r\n      this.landMarkAddressRequired = true;\r\n      this.addressForm.controls['landMark'].setValidators([\r\n        Validators.required,\r\n      ]);\r\n      this.addressForm.controls['landMark'].updateValueAndValidity();\r\n    }\r\n    if (phoneLength) {\r\n      this.phoneLength = parseInt(phoneLength) - 2;\r\n    }\r\n    let userDetails = this.store.get('profile');\r\n    this.mainDataService.setUserData(userDetails);\r\n    let name = userDetails?.name?.split(' ');\r\n    this.addressForm.patchValue({\r\n      receiverFirstName: name[0] ? name[0] : '',\r\n      receiverLastName: name[1] ? name[1] : '',\r\n      // receiverPhoneNumber: userDetails.mobileNumber,\r\n    });\r\n\r\n\r\n    if (!localStorage.getItem(\"isoCode\")) {\r\n\r\n      const tenants = this.appDataService.tenants\r\n      if (tenants.records != undefined) {\r\n        let tenantId = localStorage.getItem('tenantId');\r\n        let data = tenants.records;\r\n        let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();\r\n        localStorage.setItem('isoCode', arr?.isoCode);\r\n        this.store.set('allCountryTenants', tenants.records);\r\n      }\r\n\r\n    } else {\r\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\r\n    }\r\n      if(this.appDataService.configuration) {\r\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')\r\n        if(phoneLength) this.phoneInputLength = parseInt(phoneLength.value)\r\n      }\r\n    this.filteredCities = this.allCities;\r\n      this.region.valueChanges.subscribe(value=>{\r\n       const selectedRegion = this.allRegionList?.find((r: any) => r.id == value.id)\r\n        this.addressForm.patchValue({\r\n          region: {\r\n            id: selectedRegion.id,\r\n            regionName: selectedRegion.regionName\r\n          }\r\n        },{emitEvent: false});\r\n      })    \r\n  }\r\n  mapInitialize(map: any) {\r\n    this.map = map;\r\n  }\r\n  public handleAddressChange(place: any) {\r\n    this.Lat = place.geometry.location.lat();\r\n    this.Lng = place.geometry.location.lng();\r\n    this.position = [{\r\n      position: {lat: this.Lat, lng: this.Lng}\r\n    }]\r\n    this.zoom = 12;\r\n    this.center = this.position[0].position\r\n    this.getAddress(this.Lat, this.Lng)\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.routeSub.unsubscribe();\r\n  }\r\n\r\n  async getCustomerAddress() {\r\n    this.loaderService.show();\r\n\r\n    try {\r\n      const res: any = await this.addressService.getAddressById(this.id).toPromise();\r\n      this.addressService.loadedAddress = true;\r\n      if (res.data) {\r\n\r\n        this.isDefault = res.data.isDefault;\r\n        this.addressService.chosenAddress = res.data;\r\n        const trimmedPhoneNumber = this.trimPhoneNumber(res.data.receiverPhoneNumber);\r\n        await this.getAllRegion();\r\n        const selectedRegion = this.selectedRegion(res.data.region);\r\n        const selectedCity = this.selectedCity(res.data.city)\r\n\r\n        this.addressForm.patchValue({\r\n          addressLabel: res.data.addressLabel,\r\n          receiverFirstName: res.data.receiverFirstName,\r\n          receiverLastName: res.data.receiverLastName,\r\n          postcode: res.data.postCode,\r\n          receiverPhoneNumber: trimmedPhoneNumber,\r\n          landMark: res.data.landMark,\r\n          deliveryInstructions: res.data.deliveryInstructions,\r\n          buldingNumber: res.data.buldingNumber,\r\n          additionalAddress: res.data.additionalAddress,\r\n          city: selectedCity.cityName,\r\n          region: selectedRegion,\r\n        });\r\n\r\n\r\n        if(res.data.addressLabel!='Home' && res.data.addressLabel!='Work' ) {\r\n          this.addressLabelList[2].name = res.data.addressLabel;\r\n        }\r\n        this.selectedAddressType= res.data.addressLabel;\r\n        this.Lat = parseFloat(res.data.lat);\r\n        this.Lng = parseFloat(res.data.lng);\r\n        const streetAddress = res.data.streetAddress;\r\n        if(this.searchElementRef){\r\n          this.searchElementRef.nativeElement.value = streetAddress;\r\n        }\r\n\r\n        this.position = [{\r\n          position: { lat: this.Lat, lng: this.Lng }\r\n        }];\r\n        this.zoom = 8;\r\n        this.center = this.position[0].position;\r\n\r\n        if (res.data.streetAddress || res.data.country) {\r\n\r\n          this.addressForm.patchValue({\r\n            streetAddress: res.data.streetAddress,\r\n            country: res.data.country,\r\n          });\r\n        }else {\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n\r\n\r\n        }\r\n\r\n        this.createLocationButton();\r\n      } else {\r\n        this.setCurrentLocation();\r\n        this.createLocationButton();\r\n      }\r\n      this.loaderService.hide();\r\n    } catch (err: any) {\r\n      this.addressService.loadedAddress = true;\r\n      this.setCurrentLocation();\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n        detail: err.message,\r\n      });\r\n    } finally {\r\n      this.loaderService.hide();\r\n    }\r\n  }\r\n\r\n  private trimPhoneNumber(phoneNumber: string): string {\r\n    return phoneNumber.length > 3 ? phoneNumber.substring(3) : phoneNumber;\r\n  }\r\n  private selectedRegion(regionName: string): { id: number; regionName: null } {\r\n\r\n    const region = this.allRegionList.find((r: any) => r.regionName === regionName);\r\n    if (region) {\r\n      this.filterCitiesByRegion(region.id)\r\n      this.selectedCitiesValue =  region.cities\r\n      return { id: region.id, regionName: region.regionName };\r\n    }\r\n    return { \"id\": -1, \"regionName\": null };\r\n\r\n  }\r\n  private selectedCity(cityName: string): { id: number; cityName: null } {\r\n    const city = this.selectedCitiesValue.find((r: any) => r.cityName === cityName);\r\n    if (city) {\r\n      return { id: city.id, cityName: city.cityName };\r\n    }\r\n    return { \"id\": -1, \"cityName\": null };\r\n  }\r\n\r\n\r\n  getAddress(latitude: number, longitude: number) {\r\n\r\n    this.geoCoder.geocode(\r\n      {location: {lat: latitude, lng: longitude}},\r\n      (results: { formatted_address: string, address_components: any }[], status: string) => {\r\n        if (status === 'OK') {\r\n          if (results[0]) {\r\n\r\n            this.position = [{\r\n              position: {lat: latitude, lng: longitude}\r\n            }]\r\n            this.center = this.position[0].position;\r\n            this.zoom = 12;\r\n            this.address = results[0].formatted_address;\r\n            if(results[0]?.address_components.length){\r\n              const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))\r\n              this.addressDetailCity = city.long_name;\r\n\r\n            }\r\n            this.addressForm.patchValue({\r\n              streetAddress: this.address,\r\n              country: results[results.length - 1].formatted_address,\r\n              // city: results[results.length - 3].formatted_address,\r\n            });\r\n            this.validate();\r\n            this.getCoordinates();\r\n            this.cd.detectChanges();\r\n          } else {\r\n            if (isPlatformBrowser(this.platformId)) {\r\n              window.alert('No results found');\r\n            }\r\n          }\r\n        } else {\r\n          if (isPlatformBrowser(this.platformId)) {\r\n            window.alert('Geocoder failed due to: ' + status);\r\n\r\n          }\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  clear(): void {\r\n    this.searchElementRef.nativeElement.value = '';\r\n\r\n  }\r\n\r\n  onSubmit() {\r\n\r\n    this.addressForm.patchValue({\r\n      Lat: this.Lat ? this.Lat.toString() : '',\r\n      Lng: this.Lng ? this.Lng.toString() : '',\r\n      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\r\n    });\r\n\r\n\r\n    this.loaderService.show();\r\n    if (this.addressForm.value.postcode == '')\r\n      this.addressForm.value.postcode = 0;\r\n\r\n    if (this.addressForm.valid) {\r\n      const formValue = {\r\n        ...this.addressForm.value,\r\n        region: this.addressForm.value.region.regionName, // Send regionName only\r\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\r\n      };\r\n      if (this.addressForm.value.postcode === \"\") delete this.addressForm.value.postcode;\r\n      this.addressService.addAddress(formValue).subscribe({\r\n        next: (res: any) => {\r\n          if(res?.success){\r\n            this.loaderService.hide();\r\n            this.isDisplaySuccessModal = true;\r\n            this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully')\r\n          } else{\r\n            this.loaderService.hide();\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: res?.message,\r\n            });\r\n          }\r\n        \r\n        },\r\n        error: (err: any) => {\r\n\r\n          this.loaderService.hide();\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: this.translate.instant('ErrorMessages.fetchError'),\r\n            detail: err,\r\n          });\r\n        },\r\n      });\r\n    }\r\n  }\r\n  onConfrim(){\r\n    this.isDisplaySuccessModal = false;\r\n    if(this.routeToCheckOut){\r\n        this.router.navigateByUrl(\"/checkout/selectAddress\");\r\n    } else {\r\n        this.router.navigate(['/account/address']);\r\n    }\r\n  }\r\n  Update() {\r\n    if (this.Lat?.toString() === \"\" || this.Lng?.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.addressIsRequired'),\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.addressForm.patchValue({\r\n      Lat: this.Lat?.toString(),\r\n      Lng: this.Lng?.toString(),\r\n      Id: this.addressService.chosenAddress.id,\r\n    });\r\n\r\n    this.loaderService.show();\r\n\r\n    if (this.addressForm.valid) {\r\n      const formValue = {\r\n        ...this.addressForm.value,\r\n        region: this.addressForm.value.region.regionName,\r\n        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\r\n      };\r\n\r\n      this.addressService.updateAddress(formValue).subscribe({\r\n        next: (res: any) => {\r\n          this.loaderService.hide();\r\n          if (!res.success) {\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: this.translate.instant('ResponseMessages.address'),\r\n              detail: this.translate.instant(res.message),\r\n            });\r\n          } else {\r\n            this.isDisplaySuccessModal = true;\r\n            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          this.loaderService.hide();\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: this.translate.instant('ErrorMessages.fetchError'),\r\n            detail: err,\r\n          });\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  OnlyNumeric(val: any): boolean {\r\n    if (!Number(val.value)) {\r\n      this.addressForm.value.postcode = '';\r\n      this.addressForm.value.phone = '';\r\n    }\r\n    return false;\r\n  }\r\n\r\n  checkPlaceHolder() {\r\n    if (this.myplaceHolder) {\r\n      this.myplaceHolder = '';\r\n    } else {\r\n      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\r\n      if (this.myplaceHolder)\r\n        this.myplaceHolder = this.myplaceHolder + ' 000 000 000';\r\n      else this.myplaceHolder = '256 000 000 000';\r\n    }\r\n  }\r\n\r\n  validate() {\r\n    if (!this.addressForm.valid) return true;\r\n  }\r\n\r\n  setAsDefault() {\r\n    this.addressService.setDefault(this.id).subscribe({\r\n      next: (res: any) => {\r\n        this.messageService.add({\r\n          severity: 'success',\r\n          summary: this.translate.instant('ResponseMessages.address'),\r\n          detail: this.translate.instant(\r\n            'ResponseMessages.defaultAddressSuccessfully'\r\n          ),\r\n        });\r\n        if(this.redirectUrl && this.redirectUrl !== '') {\r\n          this.router.navigate([this.redirectUrl])\r\n        } else {\r\n          this.router.navigate(['/account/address']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.loaderService.hide();\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          summary: this.translate.instant('ErrorMessages.fetchError'),\r\n          detail: err,\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  private setCurrentLocation(selectedPosition?:any) {\r\n    this.addressService.chosenAddress = null;\r\n    if ('geolocation' in navigator) {\r\n      if(!selectedPosition) {\r\n        navigator.geolocation.getCurrentPosition((position) => {\r\n          this.Lat = position.coords.latitude;\r\n          this.Lng = position.coords.longitude;\r\n          this.position = [{\r\n            position: {lat: this.Lat, lng: this.Lng}\r\n          }]\r\n          this.center = this.position[0].position\r\n          this.zoom = 12;\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n          this.createLocationButton();\r\n        });\r\n      }\r\n      else {\r\n\r\n          this.Lat = parseFloat(selectedPosition.lat);\r\n          this.Lng = parseFloat(selectedPosition.lng);\r\n          this.position = [{\r\n            position: { lat: this.Lat, lng: this.Lng }\r\n          }];\r\n\r\n          this.getAddress(this.Lat, this.Lng);\r\n          this.createLocationButton();\r\n\r\n      }\r\n    }\r\n\r\n  }\r\n  mapClicked(event : any){\r\n    let latLng = JSON.parse(JSON.stringify(event.latLng));\r\n    this.Lat = latLng.lat;\r\n    this.Lng =latLng.lng\r\n    this.position = [{\r\n      position: {lat: this.Lat, lng: this.Lng}\r\n    }]\r\n    this.center = this.position[0].position\r\n    this.zoom = 12;\r\n    this.getAddress(this.Lat, this.Lng);\r\n  }\r\n  createLocationButton() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const controlDiv : any = document.createElement('div');\r\n      controlDiv.index = 100;\r\n      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\r\n    }\r\n  }\r\n  markerDragEnd(event: any) {\r\n    if (event.latLng != null) {\r\n      const latLng = event.latLng.toJSON();\r\n;\r\n      this.getAddress(latLng.lat, latLng.lng);\r\n    }\r\n  }\r\n  getCities(){\r\n    const reqObj : any = {\r\n      currentPage : 1,\r\n      pageSize:5,\r\n      ignorePagination : true\r\n    }\r\n    this.addressService.getAllCities(reqObj).subscribe((res: any) => {\r\n      if(res.success){\r\n        this.allCities = res.data.records;\r\n      }\r\n    })\r\n  }\r\n  getCoordinates() {\r\n    var geocoder = new google.maps.Geocoder();\r\n    this.isDifferentCity = true;\r\n    geocoder.geocode({ 'address': this.addressForm.controls['streetAddress'].value },  (results: any, status: any) => {\r\n      if (status == google.maps.GeocoderStatus.OK) {\r\n        if(results[0].address_components.length){\r\n          const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))\r\n          if(city.long_name === this.addressDetailCity){\r\n           this.isDifferentCity = false;\r\n           this.cd.detectChanges();\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAllRegion() {\r\n    let reqObj = {\r\n      currentPage: 1,\r\n      pageSize: 5,\r\n      ignorePagination: true\r\n    };\r\n    try {\r\n      const res: any = await this.addressService.getAllRegions(reqObj).toPromise();\r\n      if (res.success) {\r\n        this.allRegionList = res.data.records;\r\n\r\n        this.filteredCities = res.data.records[0].cities\r\n      }\r\n    } catch (error) {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  filterCitiesByRegion(regionId: any): void {\r\n    const selectedRegion = this.allRegionList.find((region: { id: any; }) => region.id === regionId);\r\n    if (selectedRegion) {\r\n      this.filteredCities = selectedRegion?.cities\r\n    }\r\n  }\r\n  selectAddressType(type: string) {\r\n    this.selectedAddressType = type;\r\n    this.addressForm.patchValue({ addressLabel: type });\r\n    if(this.selectedAddressType=='Other'){\r\n      this.isDisplayOtherModal=true;\r\n    }\r\n  }\r\n\r\n  addAddress(event:any) {\r\n    if(event){\r\n      this.addressForm.patchValue({ addressLabel: event });\r\n      this.addressLabelList[2].name = event;\r\n    }\r\n    this.isDisplayOtherModal=false\r\n  }\r\n\r\n  onAddressDialogCancel(event:any) {\r\n    this.isDisplayOtherModal=event\r\n  }\r\n  get region() {\r\n    return (this.addressForm.get('region') as UntypedFormGroup)\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"isMobileTemplate && screenWidth <= 768; else oldContainer\">\r\n  <section class=\"account-page-mobile\"  >\r\n    <ng-container>\r\n\r\n    <div class=\"d-flex cart-mobile-new__address-layout flex-row\">\r\n      <div class=\"d-inline-flex cart-mobile-new__address-layout__address-items-section\">\r\n        <div class=\"header-container\">\r\n          <img src=\"assets/icons/mobile-icons/back-icon.svg\" alt=\"back-icon\" (click)=\"onBack()\">\r\n          <span class=\"header-container__header-detail\">\r\n                {{\"multipleAddress.completeAddress\" | translate }}\r\n                </span>\r\n          </div>\r\n          <div class=\"complete-address mb-5\">\r\n\r\n            <div class=\"col-12\">\r\n              <google-map [center]=\"center\" [zoom]=\"zoom\" height=\"111px\" width=\"100%\" [options]=\"mapOptions\"\r\n                          (mapClick)=\"mapClicked($event)\" (mapInitialized)=\"mapInitialize($event)\">\r\n                <map-marker *ngFor=\" let marker of position\" [options]=\"markerOptions\" (mapDrag)=\"markerDragEnd($event)\"\r\n                            [position]=\"marker.position\">\r\n                </map-marker>\r\n              </google-map>\r\n              <form [formGroup]=\"addressForm\" class=\"grid\">\r\n                <div class=\"form-bg\">\r\n                  <div class=\"d-flex\">\r\n                    <label for=\"country\">{{ \"addingAddress.country\" | translate }}*</label>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <input\r\n                      [ngClass]=\"{'form-input-error':addressForm.get('country')?.hasError('required')  && addressForm.get('country')?.touched}\"\r\n                      class=\"form-input input-address\" formControlName=\"country\" id=\"country\" pInputText type=\"text\" />\r\n\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3 region-dropdown\">\r\n                  <div class=\"search-container select-dropdown-merchant city-dropdown region-address\"\r\n                       [ngStyle]=\"{'border-bottom': addressForm.get('region.regionId')?.hasError('required') && addressForm.get('region.regionId')?.touched ? '1px solid red' : ''}\">\r\n                    <div class=\"product-label region-label\"  [formGroup]=\"region\">\r\n                      {{ 'addingAddress.region' | translate }}*\r\n                      <br>\r\n                      <p-dropdown #ddRegion [options]=\"allRegionList\" formControlName=\"id\" class=\"custom-dropdown\"\r\n                                  (onChange)=\"filterCitiesByRegion($event.value)\"\r\n                                  [style]=\"{'width':'100%', 'border': 'none','background':'none', 'outline':'none', 'box-shadow':'none'}\"\r\n                                  optionValue=\"id\" optionLabel=\"regionName\" [filter]=\"true\" filterBy=\"regionName\">\r\n                        <ng-template let-item pTemplate=\"selectedItem\">\r\n                          {{ item.regionName }}\r\n                        </ng-template>\r\n                        <ng-template let-region pTemplate=\"item\">\r\n                          <div class=\"\">\r\n                            {{ region.regionName }}\r\n                          </div>\r\n                        </ng-template>\r\n                      </p-dropdown>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3 region-dropdown\">\r\n                  <div class=\"search-container select-dropdown-merchant city-dropdown region-address\"\r\n                       [ngStyle]=\"{\r\n                            'border-bottom': addressForm.get('city')?.hasError('required') && addressForm.get('city')?.touched ? '1px solid red' : ''\r\n                        }\"\r\n                  >\r\n                    <div class=\"product-label region-label\">\r\n                      {{'addingAddress.city' |translate}}*\r\n                      <br>\r\n                      <p-dropdown #ddCity [options]=\"filteredCities\" formControlName=\"city\" class=\"custom-dropdown\"\r\n                                  [style]=\"{'width':'100%', 'border': 'none','background':'none', 'outline':'none', 'box-shadow':'none'}\"\r\n                                  optionValue=\"cityName\" optionLabel=\"cityName\" [filter]=\"true\" filterBy=\"cityName\">\r\n                        <ng-template let-item pTemplate=\"selectedItem\">\r\n                          {{item.cityName}}\r\n                        </ng-template>\r\n                        <ng-template let-car pTemplate=\"item\">\r\n                          <div class=\"search-name\">\r\n                            {{car.cityName}}\r\n                          </div>\r\n                        </ng-template>\r\n                      </p-dropdown>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"form-bg\">\r\n                  <div class=\"d-flex\">\r\n                    <label for=\"address-type\">{{ \"addingAddress.addressDetails\" | translate }}*</label>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <input class=\"form-input\" formControlName=\"streetAddress\" id=\"address-type\" pInputText\r\n                           [ngClass]=\"{'form-input-error': addressForm.get('streetAddress')?.hasError('required')}\"\r\n                           type=\"text\" (input)=\"getCoordinates()\"  />\r\n                  </div>\r\n                </div>\r\n                <div class=\"form-bg\">\r\n                  <div class=\"d-flex\">\r\n                    <label for=\"address-type\">{{ \"addingAddress.otherAddressSecond\" | translate }}</label>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <input class=\"form-input\" formControlName=\"additionalAddress\" id=\"other-address-second\" pInputText\r\n                           type=\"text\" />\r\n                  </div>\r\n                </div>\r\n                <div class=\"form-bg\">\r\n                  <div class=\"d-flex\">\r\n                    <label for=\"city\">{{ \"addingAddress.post-code\" | translate }}</label>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <input [value]=\"this.addressForm.value.postcode\" class=\"form-input\" formControlName=\"postcode\"\r\n                           id=\"postcode\" pInputText type=\"number\" />\r\n                  </div>\r\n                </div>\r\n                <div class=\"form-bg\">\r\n                  <div class=\"d-flex\">\r\n                    <label for=\"landMark\">{{ \"addingAddress.landMark\" | translate }}*</label>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <input\r\n                      [ngClass]=\"{'form-input-error':addressForm.get('landMark')?.hasError('required')  && addressForm.get('landMark')?.touched}\"\r\n                      class=\"form-input\" formControlName=\"landMark\" id=\"landMark\" pInputText type=\"text\" />\r\n                  </div>\r\n                </div>\r\n\r\n              <div class=\"form-bg\">\r\n                <div class=\"d-flex\">\r\n                  <label for=\"receiverPhoneNumber\">{{ \"addingAddress.phone\" | translate }}*</label>\r\n                </div>\r\n                <div class=\"d-flex contact-address flex-column\">\r\n                  <ngx-intl-tel-input\r\n                    [cssClass]=\"'custom contact-input-phone'\"\r\n                    [enableAutoCountrySelect]=\"true\"\r\n                    [enablePlaceholder]=\"true\"\r\n                    [maxLength]=\"phoneInputLength\"\r\n                    [numberFormat]=\"PhoneNumberFormat.National\"\r\n                    [phoneValidation]=\"false\"\r\n                    [preferredCountries]=\"preferredCountries\"\r\n                    [searchCountryField]=\"[SearchCountryField.Iso2, SearchCountryField.Name]\"\r\n                    [searchCountryFlag]=\"true\"\r\n                    [selectFirstCountry]=\"false\"\r\n                    [selectedCountryISO]=\"CustomCountryISO\"\r\n                    [separateDialCode]=\"true\"\r\n                    [customPlaceholder]=\"customPlaceHolder\"\r\n                    formControlName=\"receiverPhoneNumber\"\r\n                    name=\"receiverPhoneNumber\"\r\n                    id=\"receiverPhoneNumber\"\r\n                    [ngClass]=\"{'form-input-error':(addressForm.get('receiverPhoneNumber')?.hasError('required') || addressForm.get('receiverPhoneNumber')?.hasError('validatePhoneNumber')\r\n                    )}\"\r\n                  >\r\n                  </ngx-intl-tel-input>\r\n                  <span class=\"error-msg\" *ngIf=\"addressForm.get('receiverPhoneNumber')?.hasError('validatePhoneNumber')\">{{\"ErrorMessages.phoneNumberIsUnvalid\"|translate}}\r\n          </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"form-bg\">\r\n                <div class=\"d-flex\">\r\n                  <label for=\"instructions\">{{\"addingAddress.instructions\" | translate}}</label>\r\n                </div>\r\n                <div class=\"d-flex\">\r\n                  <input class=\"form-input\" formControlName=\"deliveryInstructions\" id=\"instructions\" pInputText\r\n                         type=\"text\" />\r\n                </div>\r\n              </div>\r\n\r\n                <div class=\"col-12 col-md-12 mt-3 form-bg\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <div class=\"search-container select-dropdown-merchant bg-address\">\r\n                      <div class=\"product-label bg-address \">\r\n                        {{ 'addingAddress.addressLabel' | translate }}\r\n                      </div>\r\n\r\n                      <div class=\"options-address-list d-flex\">\r\n                        <div *ngFor=\"let addressType of addressLabelList\"\r\n                             class=\"custom-option\"\r\n                             [class.selected]=\"selectedAddressType === addressType.name\"\r\n                             (click)=\"selectAddressType(addressType.name)\">\r\n                          <img *ngIf=\"addressType.name === 'Home'\" [src]=\"selectedAddressType === 'Home' ? 'assets/icons/mobile-icons/home-address-white.svg' : 'assets/icons/mobile-icons/home-address.svg'\" alt=\"Home Icon\">\r\n                          <img *ngIf=\"addressType.name === 'Work'\" [src]=\"selectedAddressType === 'Work' ? 'assets/icons/mobile-icons/work-address-white.svg' : 'assets/icons/mobile-icons/work-address.svg'\" alt=\"Work Icon\">\r\n\r\n\r\n                          {{ addressType.name }}\r\n                        </div>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n              </form>\r\n              <div class=\"grid\">\r\n\r\n                <div *ngIf=\"addressService.chosenAddress === null\" class=\"col-12 col-md-6 mt-3\">\r\n                  <button (click)=\"onSubmit()\" [disabled]=\"validate()\" [label]=\"'addingAddress.confirmAddress' | translate\"\r\n                          class=\"w-full second-btn add-address-btn\" pButton type=\"button\"></button>\r\n                </div>\r\n                <div class=\"col-12 col-md-6  button-address\">\r\n                  <button (click)=\"setAsDefault()\" [disabled]=\"\r\n                  addressService.chosenAddress === null ||\r\n                  addressService.chosenAddress.isDefault === true\r\n                \" [label]=\"'addingAddress.defaultAddress' | translate\" class=\"defaultBtn w-100 add-address-btn\" pButton\r\n                          type=\"button\"></button>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3 button-address\" *ngIf=\"id && id !== 'add-address'\">\r\n                  <button (click)=\"Update()\" [disabled]=\"validate()\" [label]=\"'addingAddress.updateAddress' | translate\"\r\n                          class=\"w-full confirmBtn\" pButton type=\"button\"></button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </ng-container>\r\n  </section>\r\n</ng-container>\r\n\r\n<ng-template #oldContainer>\r\n  <section class=\"address\" [ngClass]=\"{'hidden-navbar':!navbarData?.isActive}\">\r\n    <div class=\"breadcrumb-address d-flex\">\r\n      <em [routerLink]=\"'/'\" aria-hidden=\"true\" class=\"pi pi-home cursor-pointer\"></em>\r\n      <em aria-hidden=\"true\" class=\"fa fa-angle-right\"></em>\r\n      <span [routerLink]=\"'/account'\">{{\r\n          \"sideMenu.yourAccount\" | translate\r\n        }}</span>\r\n      <em aria-hidden=\"true\" class=\"fa fa-angle-right\"></em>\r\n      <span [routerLink]=\"'/account/address'\">{{\r\n          \"multipleAddress.yourAddresses\" | translate\r\n        }}</span>\r\n    </div>\r\n    <div class=\"content-container mt-5\" style=\"padding-top: 1px; max-width: 1057px\">\r\n      <h2 class=\"add-address\" *ngIf=\"id && id === 'add-address'\" style=\"font-family: var(--medium-font)\">\r\n        {{ \"addingAddress.addAddress\" | translate }}\r\n      </h2>\r\n      <h2 class=\"add-address\" *ngIf=\"id && id !== 'add-address'\" style=\"font-family: var(--medium-font)\">\r\n        {{ \"addingAddress.updateAddressTitle\" | translate }}\r\n      </h2>\r\n      <div class=\"grid\">\r\n        <div class=\"col-12 col-md-12 mb-4\">\r\n          <div class=\"address-card shadow-1 p-3 mb-4\">\r\n            <div class=\"grid\">\r\n              <div class=\"col-12\">\r\n                <div class=\"p-inputgroup search-group\">\r\n                <span class=\"p-inputgroup-addon icon-bg\">\r\n                  <em class=\"pi pi-search\"></em>\r\n                </span>\r\n                  <input #placesRef=\"ngx-places\" #search (onAddressChange)=\"handleAddressChange($event)\"\r\n                         class=\"map-search\" ngx-gp-autocomplete placeholder=\"{{ 'addingAddress.search' | translate }}\" />\r\n\r\n\r\n                  <span (click)=\"clear()\" class=\"p-inputgroup-addon icon-bg\">\r\n                  <em class=\"pi pi-times-circle\"></em>\r\n                </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-12\">\r\n                <google-map [center]=\"center\" [zoom]=\"zoom\" height=\"224px\" width=\"100%\" [options]=\"mapOptions\"\r\n                            (mapClick)=\"mapClicked($event)\" (mapInitialized)=\"mapInitialize($event)\">\r\n                  <map-marker *ngFor=\" let marker of position\" [options]=\"markerOptions\" (mapDrag)=\"markerDragEnd($event)\"\r\n                              [position]=\"marker.position\">\r\n                  </map-marker>\r\n                </google-map>\r\n              </div>\r\n              <form [formGroup]=\"addressForm\" class=\"grid\">\r\n                <!--              <div class=\"col-12 col-md-12 mt-3\">-->\r\n                <!--                <div class=\"p-float-label w-full\">-->\r\n                <!--                  <div class=\"search-container select-dropdown-merchant\">-->\r\n                <!--                    <div class=\"product-label\">-->\r\n                <!--                      {{'addingAddress.addressLabel' |translate}}-->\r\n                <!--                      <br>-->\r\n\r\n                <!--                      <p-dropdown #dd4 [options]=\"addressLabelList\" [(ngModel)]=\"selectedAddressType\"-->\r\n                <!--                        formControlName=\"addressLabel\" class=\"custom-dropdown\"-->\r\n                <!--                        [style]=\"{'width':'100%', 'border': 'none','background':'none', 'outline':'none', 'box-shadow':'none'}\"-->\r\n                <!--                        optionValue=\"name\" optionLabel=\"name\">-->\r\n                <!--                        <ng-template let-item pTemplate=\"selectedItem\">-->\r\n                <!--                          {{item.name}}-->\r\n                <!--                        </ng-template>-->\r\n                <!--                        <ng-template let-car pTemplate=\"item\">-->\r\n                <!--                          <div class=\"search-name\">-->\r\n                <!--                            {{car.name}}-->\r\n                <!--                          </div>-->\r\n                <!--                        </ng-template>-->\r\n                <!--                      </p-dropdown>-->\r\n\r\n                <!--                    </div>-->\r\n                <!--                  </div>-->\r\n\r\n                <!--                </div>-->\r\n                <!--              </div>-->\r\n\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"search-container select-dropdown-merchant city-dropdown\"\r\n                       [ngStyle]=\"{'border-bottom': (addressForm.get('region.id')?.hasError('required') && addressForm.get('region.id')?.touched ? '1px solid red' : '') ||\r\n                       (!addressForm.get('region')?.valid ? '1px solid red' : '')}\">\r\n                    <div class=\"product-label\" [formGroup]=\"region\">\r\n                      {{ 'addingAddress.region' | translate }}*\r\n                      <br>\r\n                      <p-dropdown #ddRegion [options]=\"allRegionList\" formControlName=\"id\" class=\"custom-dropdown\"\r\n                                  (onChange)=\"filterCitiesByRegion($event.value)\"\r\n                                  [style]=\"{'width':'100%', 'border': 'none','background':'none', 'outline':'none', 'box-shadow':'none'}\"\r\n                                  optionValue=\"id\" optionLabel=\"regionName\" [filter]=\"true\" filterBy=\"regionName\">\r\n                        <ng-template let-item pTemplate=\"selectedItem\">\r\n                          {{ item.regionName }}\r\n                        </ng-template>\r\n                        <ng-template let-region pTemplate=\"item\">\r\n                          <div class=\"search-name\">\r\n                            {{ region.regionName }}\r\n                          </div>\r\n                        </ng-template>\r\n                      </p-dropdown>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"search-container select-dropdown-merchant city-dropdown\"\r\n                       [ngStyle]=\"{\r\n                            'border-bottom': (addressForm.get('city')?.hasError('required') && addressForm.get('city')?.touched ? '1px solid red' : '') ||\r\n                       (!addressForm.get('city')?.valid ? '1px solid red' : '')\r\n                        }\"\r\n                  >\r\n                    <div class=\"product-label\">\r\n                      {{'addingAddress.city' |translate}}*\r\n                      <br>\r\n                      <p-dropdown #ddCity [options]=\"filteredCities\" formControlName=\"city\" class=\"custom-dropdown\"\r\n                                  [style]=\"{'width':'100%', 'border': 'none','background':'none', 'outline':'none', 'box-shadow':'none'}\"\r\n                                  optionValue=\"cityName\" optionLabel=\"cityName\" [filter]=\"true\" filterBy=\"cityName\">\r\n                        <ng-template let-item pTemplate=\"selectedItem\">\r\n                          {{item.cityName}}\r\n                        </ng-template>\r\n                        <ng-template let-car pTemplate=\"item\">\r\n                          <div class=\"search-name\">\r\n                            {{car.cityName}}\r\n                          </div>\r\n                        </ng-template>\r\n                      </p-dropdown>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <input [value]=\"this.addressForm.value.postcode\" class=\"form-input\" formControlName=\"postcode\"\r\n                           id=\"postcode\" pInputText type=\"number\" />\r\n                    <label for=\"city\">{{ \"addingAddress.post-code\" | translate }}</label>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"p-float-label w-full contact-input-phone-container\">\r\n\r\n                  <ngx-intl-tel-input\r\n                    [cssClass]=\"'custom contact-input-phone region-label'\"\r\n                    [enableAutoCountrySelect]=\"true\"\r\n                    [enablePlaceholder]=\"true\"\r\n                    [maxLength]=\"phoneInputLength\"\r\n                    [numberFormat]=\"PhoneNumberFormat.National\"\r\n                    [phoneValidation]=\"false\"\r\n                    [preferredCountries]=\"preferredCountries\"\r\n                    [searchCountryField]=\"[SearchCountryField.Iso2, SearchCountryField.Name]\"\r\n                    [searchCountryFlag]=\"true\"\r\n                    [selectFirstCountry]=\"false\"\r\n                    [selectedCountryISO]=\"CustomCountryISO\"\r\n                    [separateDialCode]=\"true\"\r\n                    [customPlaceholder]=\"customPlaceHolder\"\r\n                    formControlName=\"receiverPhoneNumber\"\r\n                    name=\"receiverPhoneNumber\"\r\n                    id=\"receiverPhoneNumber\"\r\n                    [ngClass]=\"{'form-input-error':addressForm.get('receiverPhoneNumber')?.hasError('required')  && addressForm.get('receiverPhoneNumber')?.touched}\"\r\n                  >\r\n                  </ngx-intl-tel-input>\r\n                  <span class=\"error-msg\" *ngIf=\"addressForm.get('receiverPhoneNumber')?.hasError('validatePhoneNumber')\r\n                  && addressForm.get('receiverPhoneNumber')?.touched\">{{\"ErrorMessages.phoneNumberIsUnvalid\"|translate}}\r\n                 </span>\r\n                  <label for=\"receiverPhoneNumber\">{{ \"addingAddress.phone\" | translate }}*</label>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-12 col-md-6 mt-3\">\r\n                <div class=\"p-float-label w-full\">\r\n                  <input\r\n                    [ngClass]=\"{'form-input-error':addressForm.get('country')?.hasError('required')  && addressForm.get('country')?.touched}\"\r\n                    class=\"form-input\" formControlName=\"country\" id=\"country\" pInputText type=\"text\" />\r\n                  <label for=\"country\">{{ \"addingAddress.country\" | translate }}*</label>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-12 col-md-6 mt-3\">\r\n                <div class=\"p-float-label w-full\">\r\n                  <input class=\"form-input\" formControlName=\"streetAddress\" id=\"address-type\" pInputText\r\n                         [ngClass]=\"{'form-input-error': addressForm.get('streetAddress')?.hasError('required')}\"\r\n\r\n                           type=\"text\" (input)=\"getCoordinates()\"  />\r\n                    <label for=\"address-type\">{{ \"addingAddress.addressDetails\" | translate }}*</label>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <input class=\"form-input\" formControlName=\"additionalAddress\" id=\"other-address-second\" pInputText\r\n                           type=\"text\" />\r\n                    <label for=\"address-type\">{{ \"addingAddress.otherAddressSecond\" | translate }}</label>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <input\r\n                      [ngClass]=\"{'form-input-error':addressForm.get('landMark')?.hasError('required')  && addressForm.get('landMark')?.touched}\"\r\n                      class=\"form-input\" formControlName=\"landMark\" id=\"landMark\" pInputText type=\"text\" />\r\n                    <label for=\"landMark\">{{ \"addingAddress.landMark\" | translate }}*</label>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 mt-3\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <input class=\"form-input\" formControlName=\"deliveryInstructions\" id=\"instructions\" pInputText\r\n                           type=\"text\" />\r\n                    <label for=\"instructions\">{{\r\n                        \"addingAddress.instructions\" | translate\r\n                      }}</label>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-12 mt-0 form-bg\">\r\n                  <div class=\"p-float-label w-full\">\r\n                    <div class=\"select-dropdown-merchant bg-address\">\r\n                      <div class=\"product-label bg-address \">\r\n                        {{ 'addingAddress.addressLabel' | translate }}\r\n                      </div>\r\n\r\n                      <div class=\"options-address-list d-flex\">\r\n                        <div *ngFor=\"let addressType of addressLabelList\"\r\n                             class=\"custom-option\"\r\n                             [class.selected]=\"selectedAddressType === addressType.name\"\r\n                             (click)=\"selectAddressType(addressType.name)\">\r\n                          <img *ngIf=\"addressType.name === 'Home'\" [src]=\"selectedAddressType === 'Home' ? 'assets/icons/mobile-icons/home-address-white.svg' : 'assets/icons/mobile-icons/home-address.svg'\" alt=\"Home Icon\">\r\n                          <img *ngIf=\"addressType.name === 'Work'\" [src]=\"selectedAddressType === 'Work' ? 'assets/icons/mobile-icons/work-address-white.svg' : 'assets/icons/mobile-icons/work-address.svg'\" alt=\"Work Icon\">\r\n\r\n\r\n                          {{ addressType.name }}\r\n                        </div>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </form>\r\n            </div>\r\n\r\n\r\n            <div class=\"grid\">\r\n              <div class=\"col-12 col-md-6 mt-3 button-address\">\r\n                <button (click)=\"setAsDefault()\" [disabled]=\"\r\n                  addressService.chosenAddress === null ||\r\n                  addressService.chosenAddress.isDefault === true\r\n                \" [label]=\"'addingAddress.defaultAddress' | translate\" class=\"defaultBtn w-100\" pButton\r\n                        type=\"button\"></button>\r\n              </div>\r\n              <div *ngIf=\"id && id === 'add-address'\" class=\"col-12 col-md-6 mt-3\">\r\n                <button (click)=\"onSubmit()\" [disabled]=\"validate()\" [label]=\"'addingAddress.addAddress' | translate\"\r\n                        class=\"w-full second-btn\" pButton type=\"button\"></button>\r\n              </div>\r\n              <div class=\"col-12 col-md-6 mt-3 button-address\" *ngIf=\"id && id !== 'add-address'\">\r\n                <button (click)=\"Update()\" [disabled]=\"validate()\" [label]=\"'addingAddress.updateAddress' | translate\"\r\n                        class=\"w-full confirmBtn\" pButton type=\"button\"></button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</ng-template>\r\n\r\n<ng-container *ngIf=\"isDisplaySuccessModal\">\r\n  <app-mtn-success-modal [displayModal]=\"isDisplaySuccessModal\" [message]=\"message\" (submit)=\"onConfrim()\" >\r\n\r\n  </app-mtn-success-modal>\r\n</ng-container>\r\n<ng-container *ngIf=\"isDisplayOtherModal\">\r\n  <app-address-label [displayModal]=\"isDisplayOtherModal\" (submit)=\"addAddress($event)\" (cancel)=\"onAddressDialogCancel($event)\">\r\n\r\n  </app-address-label>\r\n</ng-container>\r\n"], "mappings": ";AAAA,SAQEA,WAAW,QAEN,eAAe;AAEtB,SAAQC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAgBhF,SAAQC,iBAAiB,QAAiB,iBAAiB;AAC3D,SAAQC,UAAU,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAO,uBAAuB;AACvF,SAAQC,aAAa,QAAO,iBAAiB;;;;;;;;;;;;;;;;;;;;;;ICb7BC,EAAA,CAAAC,cAAA,qBACyC;IAD8BD,EAAA,CAAAE,UAAA,qBAAAC,qFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAWR,EAAA,CAAAS,WAAA,CAAAF,OAAA,CAAAG,aAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IAExGJ,EAAA,CAAAW,YAAA,EAAa;;;;;IAFgCX,EAAA,CAAAY,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAyB,aAAAC,UAAA,CAAAC,QAAA;;;;;IA2B5DhB,EAAA,CAAAiB,MAAA,GACF;;;;IADEjB,EAAA,CAAAkB,kBAAA,MAAAC,QAAA,CAAAC,UAAA,MACF;;;;;IAEEpB,EAAA,CAAAC,cAAA,cAAc;IACZD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAI,UAAA,CAAAF,UAAA,MACF;;;;;IAmBApB,EAAA,CAAAiB,MAAA,GACF;;;;IADEjB,EAAA,CAAAkB,kBAAA,MAAAK,QAAA,CAAAC,QAAA,MACF;;;;;IAEExB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAO,OAAA,CAAAD,QAAA,MACF;;;;;IAuERxB,EAAA,CAAAC,cAAA,eAAwG;IAAAD,EAAA,CAAAiB,MAAA,GAChH;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;IADyGX,EAAA,CAAAqB,SAAA,GAChH;IADgHrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,kDAChH;;;;;IAyBgB1B,EAAA,CAAA2B,SAAA,cAAoM;;;;IAA3J3B,EAAA,CAAAY,UAAA,QAAAgB,OAAA,CAAAC,mBAAA,iHAAA7B,EAAA,CAAA8B,aAAA,CAA0I;;;;;IACnL9B,EAAA,CAAA2B,SAAA,cAAoM;;;;IAA3J3B,EAAA,CAAAY,UAAA,QAAAmB,OAAA,CAAAF,mBAAA,iHAAA7B,EAAA,CAAA8B,aAAA,CAA0I;;;;;;IALrL9B,EAAA,CAAAC,cAAA,cAGmD;IAA9CD,EAAA,CAAAE,UAAA,mBAAA8B,qEAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAK,aAAA,CAAA6B,IAAA;MAAA,MAAAC,eAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4B,OAAA,CAAAC,iBAAA,CAAAH,eAAA,CAAAI,IAAA,CAAmC;IAAA,EAAC;IAChDvC,EAAA,CAAAwC,UAAA,IAAAC,qDAAA,kBAAoM;IACpMzC,EAAA,CAAAwC,UAAA,IAAAE,qDAAA,kBAAoM;IAGpM1C,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPDX,EAAA,CAAA2C,WAAA,aAAAC,OAAA,CAAAf,mBAAA,KAAAM,eAAA,CAAAI,IAAA,CAA2D;IAExDvC,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAY,UAAA,SAAAuB,eAAA,CAAAI,IAAA,YAAiC;IACjCvC,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAY,UAAA,SAAAuB,eAAA,CAAAI,IAAA,YAAiC;IAGvCvC,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAiB,eAAA,CAAAI,IAAA,MACF;;;;;;IAWRvC,EAAA,CAAAC,cAAA,cAAgF;IACtED,EAAA,CAAAE,UAAA,mBAAA2C,wEAAA;MAAA7C,EAAA,CAAAK,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAsC,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;;IAC4ChD,EAAA,CAAAW,YAAA,EAAS;;;;IADpDX,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAY,UAAA,aAAAqC,OAAA,CAAAC,QAAA,GAAuB,UAAAlD,EAAA,CAAA0B,WAAA;;;;;;IAUtD1B,EAAA,CAAAC,cAAA,cAAoF;IAC1ED,EAAA,CAAAE,UAAA,mBAAAiD,wEAAA;MAAAnD,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA4C,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;;IAC8BtD,EAAA,CAAAW,YAAA,EAAS;;;;IADtCX,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAY,UAAA,aAAA2C,OAAA,CAAAL,QAAA,GAAuB,UAAAlD,EAAA,CAAA0B,WAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAtMpE1B,EAAA,CAAAwD,uBAAA,GAAgF;IAC9ExD,EAAA,CAAAC,cAAA,iBAAuC;IACrCD,EAAA,CAAAwD,uBAAA,GAAc;IAEdxD,EAAA,CAAAC,cAAA,aAA6D;IAGYD,EAAA,CAAAE,UAAA,mBAAAuD,8DAAA;MAAAzD,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAkD,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAArF5D,EAAA,CAAAW,YAAA,EAAsF;IACtFX,EAAA,CAAAC,cAAA,cAA8C;IACxCD,EAAA,CAAAiB,MAAA,GACA;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAEbX,EAAA,CAAAC,cAAA,cAAmC;IAInBD,EAAA,CAAAE,UAAA,sBAAA2D,yEAAAzD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAI,OAAA,GAAA9D,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAqD,OAAA,CAAAC,UAAA,CAAA3D,MAAA,CAAkB;IAAA,EAAC,4BAAA4D,+EAAA5D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAO,OAAA,GAAAjE,EAAA,CAAAQ,aAAA;MAAA,OAAmBR,EAAA,CAAAS,WAAA,CAAAwD,OAAA,CAAAC,aAAA,CAAA9D,MAAA,CAAqB;IAAA,EAAxC;IACzCJ,EAAA,CAAAwC,UAAA,KAAA2B,sDAAA,yBAEa;IACfnE,EAAA,CAAAW,YAAA,EAAa;IACbX,EAAA,CAAAC,cAAA,gBAA6C;IAGlBD,EAAA,CAAAiB,MAAA,IAA0C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAEzEX,EAAA,CAAAC,cAAA,eAAoB;IAClBD,EAAA,CAAA2B,SAAA,iBAEmG;IAErG3B,EAAA,CAAAW,YAAA,EAAM;IAERX,EAAA,CAAAC,cAAA,eAAkD;IAI5CD,EAAA,CAAAiB,MAAA,IACA;;IAAAjB,EAAA,CAAA2B,SAAA,UAAI;IACJ3B,EAAA,CAAAC,cAAA,0BAG4F;IAFhFD,EAAA,CAAAE,UAAA,sBAAAkE,yEAAAhE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAW,OAAA,GAAArE,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAA4D,OAAA,CAAAC,oBAAA,CAAAlE,MAAA,CAAAmE,KAAA,CAAkC;IAAA,EAAC;IAGzDvE,EAAA,CAAAwC,UAAA,KAAAgC,uDAAA,0BAEc;IACdxE,EAAA,CAAAwC,UAAA,KAAAiC,uDAAA,0BAIc;IAChBzE,EAAA,CAAAW,YAAA,EAAa;IAInBX,EAAA,CAAAC,cAAA,eAAkD;IAO5CD,EAAA,CAAAiB,MAAA,IACA;;IAAAjB,EAAA,CAAA2B,SAAA,UAAI;IACJ3B,EAAA,CAAAC,cAAA,0BAE8F;IAC5FD,EAAA,CAAAwC,UAAA,KAAAkC,uDAAA,0BAEc;IACd1E,EAAA,CAAAwC,UAAA,KAAAmC,uDAAA,0BAIc;IAChB3E,EAAA,CAAAW,YAAA,EAAa;IAInBX,EAAA,CAAAC,cAAA,eAAqB;IAESD,EAAA,CAAAiB,MAAA,IAAiD;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAErFX,EAAA,CAAAC,cAAA,eAAoB;IAGCD,EAAA,CAAAE,UAAA,mBAAA0E,iEAAA;MAAA5E,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAmB,OAAA,GAAA7E,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAoE,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAF7C9E,EAAA,CAAAW,YAAA,EAEiD;IAGrDX,EAAA,CAAAC,cAAA,eAAqB;IAESD,EAAA,CAAAiB,MAAA,IAAoD;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAExFX,EAAA,CAAAC,cAAA,eAAoB;IAClBD,EAAA,CAAA2B,SAAA,iBACqB;IACvB3B,EAAA,CAAAW,YAAA,EAAM;IAERX,EAAA,CAAAC,cAAA,eAAqB;IAECD,EAAA,CAAAiB,MAAA,IAA2C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAEvEX,EAAA,CAAAC,cAAA,eAAoB;IAClBD,EAAA,CAAA2B,SAAA,iBACgD;IAClD3B,EAAA,CAAAW,YAAA,EAAM;IAERX,EAAA,CAAAC,cAAA,eAAqB;IAEKD,EAAA,CAAAiB,MAAA,IAA2C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAE3EX,EAAA,CAAAC,cAAA,eAAoB;IAClBD,EAAA,CAAA2B,SAAA,iBAEuF;IACzF3B,EAAA,CAAAW,YAAA,EAAM;IAGVX,EAAA,CAAAC,cAAA,eAAqB;IAEgBD,EAAA,CAAAiB,MAAA,IAAwC;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAEnFX,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAA2B,SAAA,8BAoBqB;IACrB3B,EAAA,CAAAwC,UAAA,KAAAuC,gDAAA,mBACD;IACD/E,EAAA,CAAAW,YAAA,EAAM;IAERX,EAAA,CAAAC,cAAA,eAAqB;IAESD,EAAA,CAAAiB,MAAA,IAA4C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAEhFX,EAAA,CAAAC,cAAA,eAAoB;IAClBD,EAAA,CAAA2B,SAAA,iBACqB;IACvB3B,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAA2C;IAInCD,EAAA,CAAAiB,MAAA,IACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAwC,UAAA,KAAAwC,+CAAA,kBASM;IACRhF,EAAA,CAAAW,YAAA,EAAM;IAQdX,EAAA,CAAAC,cAAA,eAAkB;IAEhBD,EAAA,CAAAwC,UAAA,KAAAyC,+CAAA,kBAGM;IACNjF,EAAA,CAAAC,cAAA,eAA6C;IACnCD,EAAA,CAAAE,UAAA,mBAAAgF,kEAAA;MAAAlF,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAyB,OAAA,GAAAnF,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA0E,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;;IAIVpF,EAAA,CAAAW,YAAA,EAAS;IAEjCX,EAAA,CAAAwC,UAAA,KAAA6C,+CAAA,kBAGM;IACRrF,EAAA,CAAAW,YAAA,EAAM;IAOhBX,EAAA,CAAAsF,qBAAA,EAAe;IACjBtF,EAAA,CAAAW,YAAA,EAAU;IACZX,EAAA,CAAAsF,qBAAA,EAAe;;;;;;;;;;;IAzMCtF,EAAA,CAAAqB,SAAA,GACA;IADArB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,gDACA;IAKU1B,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAY,UAAA,WAAA2E,MAAA,CAAAC,MAAA,CAAiB,SAAAD,MAAA,CAAAE,IAAA,aAAAF,MAAA,CAAAG,UAAA;IAEM1F,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAY,UAAA,YAAA2E,MAAA,CAAAvE,QAAA,CAAW;IAIxChB,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAY,UAAA,cAAA2E,MAAA,CAAAI,WAAA,CAAyB;IAGJ3F,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,uCAA0C;IAI7D1B,EAAA,CAAAqB,SAAA,GAAyH;IAAzHrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAC,GAAA,IAAAC,OAAA,GAAAP,MAAA,CAAAI,WAAA,CAAAI,GAAA,8BAAAD,OAAA,CAAAE,QAAA,mBAAAF,OAAA,GAAAP,MAAA,CAAAI,WAAA,CAAAI,GAAA,8BAAAD,OAAA,CAAAG,OAAA,GAAyH;IAOxHjG,EAAA,CAAAqB,SAAA,GAA6J;IAA7JrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAM,GAAA,IAAAC,OAAA,GAAAZ,MAAA,CAAAI,WAAA,CAAAI,GAAA,sCAAAI,OAAA,CAAAH,QAAA,mBAAAG,OAAA,GAAAZ,MAAA,CAAAI,WAAA,CAAAI,GAAA,sCAAAI,OAAA,CAAAF,OAAA,0BAA6J;IACvHjG,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAY,UAAA,cAAA2E,MAAA,CAAAa,MAAA,CAAoB;IAC3DpG,EAAA,CAAAqB,SAAA,GACA;IADArB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,uCACA;IAGY1B,EAAA,CAAAqB,SAAA,GAAuG;IAAvGrB,EAAA,CAAAqG,UAAA,CAAArG,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAAuG;IAF7FvG,EAAA,CAAAY,UAAA,YAAA2E,MAAA,CAAAiB,aAAA,CAAyB;IAkB9CxG,EAAA,CAAAqB,SAAA,GAEG;IAFHrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAM,GAAA,IAAAO,QAAA,GAAAlB,MAAA,CAAAI,WAAA,CAAAI,GAAA,2BAAAU,QAAA,CAAAT,QAAA,mBAAAS,QAAA,GAAAlB,MAAA,CAAAI,WAAA,CAAAI,GAAA,2BAAAU,QAAA,CAAAR,OAAA,0BAEG;IAGJjG,EAAA,CAAAqB,SAAA,GACA;IADArB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,qCACA;IAEY1B,EAAA,CAAAqB,SAAA,GAAuG;IAAvGrB,EAAA,CAAAqG,UAAA,CAAArG,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAAuG;IAD/FvG,EAAA,CAAAY,UAAA,YAAA2E,MAAA,CAAAmB,cAAA,CAA0B;IAiBtB1G,EAAA,CAAAqB,SAAA,GAAiD;IAAjDrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,8CAAiD;IAIpE1B,EAAA,CAAAqB,SAAA,GAAwF;IAAxFrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAC,GAAA,GAAAc,QAAA,GAAApB,MAAA,CAAAI,WAAA,CAAAI,GAAA,oCAAAY,QAAA,CAAAX,QAAA,cAAwF;IAMrEhG,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,6CAAoD;IAS5D1B,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,oCAA2C;IAGtD1B,EAAA,CAAAqB,SAAA,GAAyC;IAAzCrB,EAAA,CAAAY,UAAA,UAAA2E,MAAA,CAAAI,WAAA,CAAApB,KAAA,CAAAsC,QAAA,CAAyC;IAM1B7G,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,wCAA2C;IAI/D1B,EAAA,CAAAqB,SAAA,GAA2H;IAA3HrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAC,GAAA,IAAAiB,QAAA,GAAAvB,MAAA,CAAAI,WAAA,CAAAI,GAAA,+BAAAe,QAAA,CAAAd,QAAA,mBAAAc,QAAA,GAAAvB,MAAA,CAAAI,WAAA,CAAAI,GAAA,+BAAAe,QAAA,CAAAb,OAAA,GAA2H;IAO9FjG,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,qCAAwC;IAIvE1B,EAAA,CAAAqB,SAAA,GAAyC;IAAzCrB,EAAA,CAAAY,UAAA,0CAAyC,0EAAA2E,MAAA,CAAAwB,gBAAA,kBAAAxB,MAAA,CAAA1F,iBAAA,CAAAmH,QAAA,kDAAAzB,MAAA,CAAA0B,kBAAA,wBAAAjH,EAAA,CAAAkH,eAAA,KAAAC,GAAA,EAAA5B,MAAA,CAAAzF,kBAAA,CAAAsH,IAAA,EAAA7B,MAAA,CAAAzF,kBAAA,CAAAuH,IAAA,iFAAA9B,MAAA,CAAA+B,gBAAA,iDAAA/B,MAAA,CAAAgC,iBAAA,aAAAvH,EAAA,CAAA4F,eAAA,KAAAC,GAAA,IAAA2B,QAAA,GAAAjC,MAAA,CAAAI,WAAA,CAAAI,GAAA,0CAAAyB,QAAA,CAAAxB,QAAA,mBAAAwB,QAAA,GAAAjC,MAAA,CAAAI,WAAA,CAAAI,GAAA,0CAAAyB,QAAA,CAAAxB,QAAA;IAoBlBhG,EAAA,CAAAqB,SAAA,GAA6E;IAA7ErB,EAAA,CAAAY,UAAA,UAAA6G,QAAA,GAAAlC,MAAA,CAAAI,WAAA,CAAAI,GAAA,0CAAA0B,QAAA,CAAAzB,QAAA,wBAA6E;IAM5EhG,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,uCAA4C;IAYhE1B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,4CACF;IAG+B1B,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAY,UAAA,YAAA2E,MAAA,CAAAmC,gBAAA,CAAmB;IAoBlD1H,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAY,UAAA,SAAA2E,MAAA,CAAAoC,cAAA,CAAAC,aAAA,UAA2C;IAKd5H,EAAA,CAAAqB,SAAA,GAGlC;IAHkCrB,EAAA,CAAAY,UAAA,aAAA2E,MAAA,CAAAoC,cAAA,CAAAC,aAAA,aAAArC,MAAA,CAAAoC,cAAA,CAAAC,aAAA,CAAAC,SAAA,UAGlC,UAAA7H,EAAA,CAAA0B,WAAA;IAGiD1B,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAY,UAAA,SAAA2E,MAAA,CAAAuC,EAAA,IAAAvC,MAAA,CAAAuC,EAAA,mBAAgC;;;;;IA6B5F9H,EAAA,CAAAC,cAAA,aAAmG;IACjGD,EAAA,CAAAiB,MAAA,GACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAK;;;IADHX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,wCACF;;;;;IACA1B,EAAA,CAAAC,cAAA,aAAmG;IACjGD,EAAA,CAAAiB,MAAA,GACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAK;;;IADHX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,gDACF;;;;;;IAsBY1B,EAAA,CAAAC,cAAA,qBACyC;IAD8BD,EAAA,CAAAE,UAAA,qBAAA6H,oFAAA3H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2H,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAQ,aAAA;MAAA,OAAWR,EAAA,CAAAS,WAAA,CAAAwH,OAAA,CAAAvH,aAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IAExGJ,EAAA,CAAAW,YAAA,EAAa;;;;;IAFgCX,EAAA,CAAAY,UAAA,YAAAsH,OAAA,CAAApH,aAAA,CAAyB,aAAAqH,UAAA,CAAAnH,QAAA;;;;;IA6C9DhB,EAAA,CAAAiB,MAAA,GACF;;;;IADEjB,EAAA,CAAAkB,kBAAA,MAAAkH,QAAA,CAAAhH,UAAA,MACF;;;;;IAEEpB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAmH,UAAA,CAAAjH,UAAA,MACF;;;;;IAoBApB,EAAA,CAAAiB,MAAA,GACF;;;;IADEjB,EAAA,CAAAkB,kBAAA,MAAAoH,QAAA,CAAA9G,QAAA,MACF;;;;;IAEExB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAqH,OAAA,CAAA/G,QAAA,MACF;;;;;IAqCRxB,EAAA,CAAAC,cAAA,eACoD;IAAAD,EAAA,CAAAiB,MAAA,GACrD;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;;;IAD8CX,EAAA,CAAAqB,SAAA,GACrD;IADqDrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,kDACrD;;;;;IAyDS1B,EAAA,CAAA2B,SAAA,cAAoM;;;;IAA3J3B,EAAA,CAAAY,UAAA,QAAA4H,OAAA,CAAA3G,mBAAA,iHAAA7B,EAAA,CAAA8B,aAAA,CAA0I;;;;;IACnL9B,EAAA,CAAA2B,SAAA,cAAoM;;;;IAA3J3B,EAAA,CAAAY,UAAA,QAAA6H,OAAA,CAAA5G,mBAAA,iHAAA7B,EAAA,CAAA8B,aAAA,CAA0I;;;;;;IALrL9B,EAAA,CAAAC,cAAA,cAGmD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAwI,qEAAA;MAAA,MAAAzG,WAAA,GAAAjC,EAAA,CAAAK,aAAA,CAAAsI,IAAA;MAAA,MAAAC,eAAA,GAAA3G,WAAA,CAAAG,SAAA;MAAA,MAAAyG,OAAA,GAAA7I,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAoI,OAAA,CAAAvG,iBAAA,CAAAsG,eAAA,CAAArG,IAAA,CAAmC;IAAA,EAAC;IAChDvC,EAAA,CAAAwC,UAAA,IAAAsG,qDAAA,kBAAoM;IACpM9I,EAAA,CAAAwC,UAAA,IAAAuG,qDAAA,kBAAoM;IAGpM/I,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPDX,EAAA,CAAA2C,WAAA,aAAAqG,OAAA,CAAAnH,mBAAA,KAAA+G,eAAA,CAAArG,IAAA,CAA2D;IAExDvC,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAY,UAAA,SAAAgI,eAAA,CAAArG,IAAA,YAAiC;IACjCvC,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAY,UAAA,SAAAgI,eAAA,CAAArG,IAAA,YAAiC;IAGvCvC,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAA0H,eAAA,CAAArG,IAAA,MACF;;;;;;IAkBVvC,EAAA,CAAAC,cAAA,cAAqE;IAC3DD,EAAA,CAAAE,UAAA,mBAAA+I,wEAAA;MAAAjJ,EAAA,CAAAK,aAAA,CAAA6I,IAAA;MAAA,MAAAC,OAAA,GAAAnJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA0I,OAAA,CAAAnG,QAAA,EAAU;IAAA,EAAC;;IAC4BhD,EAAA,CAAAW,YAAA,EAAS;;;;IADpCX,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAY,UAAA,aAAAwI,OAAA,CAAAlG,QAAA,GAAuB,UAAAlD,EAAA,CAAA0B,WAAA;;;;;;IAGtD1B,EAAA,CAAAC,cAAA,cAAoF;IAC1ED,EAAA,CAAAE,UAAA,mBAAAmJ,wEAAA;MAAArJ,EAAA,CAAAK,aAAA,CAAAiJ,IAAA;MAAA,MAAAC,OAAA,GAAAvJ,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAA8I,OAAA,CAAAjG,MAAA,EAAQ;IAAA,EAAC;;IAC8BtD,EAAA,CAAAW,YAAA,EAAS;;;;IADtCX,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAY,UAAA,aAAA4I,OAAA,CAAAtG,QAAA,GAAuB,UAAAlD,EAAA,CAAA0B,WAAA;;;;;;;;;;;IA/OhE1B,EAAA,CAAAC,cAAA,kBAA6E;IAEzED,EAAA,CAAA2B,SAAA,aAAiF;IAEjF3B,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAiB,MAAA,GAE5B;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IACXX,EAAA,CAAA2B,SAAA,aAAsD;IACtD3B,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAiB,MAAA,GAEpC;;IAAAjB,EAAA,CAAAW,YAAA,EAAO;IAEbX,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAwC,UAAA,KAAAiH,6CAAA,iBAEK;IACLzJ,EAAA,CAAAwC,UAAA,KAAAkH,6CAAA,iBAEK;IACL1J,EAAA,CAAAC,cAAA,eAAkB;IAOND,EAAA,CAAA2B,SAAA,cAA8B;IAChC3B,EAAA,CAAAW,YAAA,EAAO;IACLX,EAAA,CAAAC,cAAA,qBACuG;IADhED,EAAA,CAAAE,UAAA,6BAAAyJ,0EAAAvJ,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAC,OAAA,GAAA7J,EAAA,CAAAQ,aAAA;MAAA,OAAmBR,EAAA,CAAAS,WAAA,CAAAoJ,OAAA,CAAAC,mBAAA,CAAA1J,MAAA,CAA2B;IAAA,EAAC;;IAAtFJ,EAAA,CAAAW,YAAA,EACuG;IAGvGX,EAAA,CAAAC,cAAA,gBAA2D;IAArDD,EAAA,CAAAE,UAAA,mBAAA6J,+DAAA;MAAA/J,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAI,OAAA,GAAAhK,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAuJ,OAAA,CAAAC,KAAA,EAAO;IAAA,EAAC;IACvBjK,EAAA,CAAA2B,SAAA,cAAoC;IACtC3B,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,eAAoB;IAEND,EAAA,CAAAE,UAAA,sBAAAgK,wEAAA9J,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAO,OAAA,GAAAnK,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAA0J,OAAA,CAAApG,UAAA,CAAA3D,MAAA,CAAkB;IAAA,EAAC,4BAAAgK,8EAAAhK,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAS,OAAA,GAAArK,EAAA,CAAAQ,aAAA;MAAA,OAAmBR,EAAA,CAAAS,WAAA,CAAA4J,OAAA,CAAAnG,aAAA,CAAA9D,MAAA,CAAqB;IAAA,EAAxC;IACzCJ,EAAA,CAAAwC,UAAA,KAAA8H,qDAAA,yBAEa;IACftK,EAAA,CAAAW,YAAA,EAAa;IAEfX,EAAA,CAAAC,cAAA,gBAA6C;IAiCrCD,EAAA,CAAAiB,MAAA,IACA;;IAAAjB,EAAA,CAAA2B,SAAA,UAAI;IACJ3B,EAAA,CAAAC,cAAA,0BAG4F;IAFhFD,EAAA,CAAAE,UAAA,sBAAAqK,wEAAAnK,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAY,OAAA,GAAAxK,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAA+J,OAAA,CAAAlG,oBAAA,CAAAlE,MAAA,CAAAmE,KAAA,CAAkC;IAAA,EAAC;IAGzDvE,EAAA,CAAAwC,UAAA,KAAAiI,sDAAA,0BAEc;IACdzK,EAAA,CAAAwC,UAAA,KAAAkI,sDAAA,0BAIc;IAChB1K,EAAA,CAAAW,YAAA,EAAa;IAInBX,EAAA,CAAAC,cAAA,eAAkC;IAQ5BD,EAAA,CAAAiB,MAAA,IACA;;IAAAjB,EAAA,CAAA2B,SAAA,UAAI;IACJ3B,EAAA,CAAAC,cAAA,0BAE8F;IAC5FD,EAAA,CAAAwC,UAAA,KAAAmI,sDAAA,0BAEc;IACd3K,EAAA,CAAAwC,UAAA,KAAAoI,sDAAA,0BAIc;IAChB5K,EAAA,CAAAW,YAAA,EAAa;IAInBX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAA2B,SAAA,iBACgD;IAChD3B,EAAA,CAAAC,cAAA,iBAAkB;IAAAD,EAAA,CAAAiB,MAAA,IAA2C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAIzEX,EAAA,CAAAC,cAAA,eAAkC;IAGhCD,EAAA,CAAA2B,SAAA,8BAmBqB;IACrB3B,EAAA,CAAAwC,UAAA,KAAAqI,+CAAA,mBAEM;IACN7K,EAAA,CAAAC,cAAA,iBAAiC;IAAAD,EAAA,CAAAiB,MAAA,IAAwC;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAGrFX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAA2B,SAAA,iBAEqF;IACrF3B,EAAA,CAAAC,cAAA,iBAAqB;IAAAD,EAAA,CAAAiB,MAAA,IAA0C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAG3EX,EAAA,CAAAC,cAAA,eAAkC;IAKTD,EAAA,CAAAE,UAAA,mBAAA4K,gEAAA;MAAA9K,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAmB,OAAA,GAAA/K,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAsK,OAAA,CAAAjG,cAAA,EAAgB;IAAA,EAAC;IAH/C9E,EAAA,CAAAW,YAAA,EAGmD;IACjDX,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAiB,MAAA,IAAiD;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAGvFX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAA2B,SAAA,iBACqB;IACrB3B,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAiB,MAAA,IAAoD;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAG1FX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAA2B,SAAA,iBAEuF;IACvF3B,EAAA,CAAAC,cAAA,iBAAsB;IAAAD,EAAA,CAAAiB,MAAA,IAA2C;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAG7EX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAA2B,SAAA,iBACqB;IACrB3B,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAiB,MAAA,IAEtB;;IAAAjB,EAAA,CAAAW,YAAA,EAAQ;IAGhBX,EAAA,CAAAC,cAAA,eAA2C;IAInCD,EAAA,CAAAiB,MAAA,IACF;;IAAAjB,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAwC,UAAA,MAAAwI,+CAAA,kBASM;IACRhL,EAAA,CAAAW,YAAA,EAAM;IAShBX,EAAA,CAAAC,cAAA,gBAAkB;IAEND,EAAA,CAAAE,UAAA,mBAAA+K,kEAAA;MAAAjL,EAAA,CAAAK,aAAA,CAAAuJ,IAAA;MAAA,MAAAsB,OAAA,GAAAlL,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyK,OAAA,CAAA9F,YAAA,EAAc;IAAA,EAAC;;IAIVpF,EAAA,CAAAW,YAAA,EAAS;IAEjCX,EAAA,CAAAwC,UAAA,MAAA2I,+CAAA,kBAGM;IACNnL,EAAA,CAAAwC,UAAA,MAAA4I,+CAAA,kBAGM;IACRpL,EAAA,CAAAW,YAAA,EAAM;;;;;;;;;;;IAlPSX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAyF,GAAA,IAAAC,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,GAAmD;IAEpExL,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAY,UAAA,mBAAkB;IAEhBZ,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAY,UAAA,0BAAyB;IAACZ,EAAA,CAAAqB,SAAA,GAE5B;IAF4BrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,gCAE5B;IAEE1B,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAY,UAAA,kCAAiC;IAACZ,EAAA,CAAAqB,SAAA,GAEpC;IAFoCrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,0CAEpC;IAGqB1B,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAY,UAAA,SAAA0K,MAAA,CAAAxD,EAAA,IAAAwD,MAAA,CAAAxD,EAAA,mBAAgC;IAGhC9H,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAY,UAAA,SAAA0K,MAAA,CAAAxD,EAAA,IAAAwD,MAAA,CAAAxD,EAAA,mBAAgC;IAaC9H,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAyL,qBAAA,gBAAAzL,EAAA,CAAA0B,WAAA,iCAAsD;IAS1F1B,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAY,UAAA,WAAA0K,MAAA,CAAA9F,MAAA,CAAiB,SAAA8F,MAAA,CAAA7F,IAAA,aAAA6F,MAAA,CAAA5F,UAAA;IAEM1F,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAY,UAAA,YAAA0K,MAAA,CAAAtK,QAAA,CAAW;IAK1ChB,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAY,UAAA,cAAA0K,MAAA,CAAA3F,WAAA,CAAyB;IA8BtB3F,EAAA,CAAAqB,SAAA,GAC4D;IAD5DrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAM,GAAA,KAAAO,QAAA,GAAA6E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,gCAAAU,QAAA,CAAAT,QAAA,mBAAAS,QAAA,GAAA6E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,gCAAAU,QAAA,CAAAR,OAAA,iCAAAQ,QAAA,GAAA6E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,6BAAAU,QAAA,CAAAiF,KAAA,2BAC4D;IACpC1L,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAY,UAAA,cAAA0K,MAAA,CAAAlF,MAAA,CAAoB;IAC7CpG,EAAA,CAAAqB,SAAA,GACA;IADArB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,uCACA;IAGY1B,EAAA,CAAAqB,SAAA,GAAuG;IAAvGrB,EAAA,CAAAqG,UAAA,CAAArG,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAAuG;IAF7FvG,EAAA,CAAAY,UAAA,YAAA0K,MAAA,CAAA9E,aAAA,CAAyB;IAkB9CxG,EAAA,CAAAqB,SAAA,GAGG;IAHHrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,KAAAM,GAAA,KAAAS,QAAA,GAAA2E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,2BAAAY,QAAA,CAAAX,QAAA,mBAAAW,QAAA,GAAA2E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,2BAAAY,QAAA,CAAAV,OAAA,iCAAAU,QAAA,GAAA2E,MAAA,CAAA3F,WAAA,CAAAI,GAAA,2BAAAY,QAAA,CAAA+E,KAAA,2BAGG;IAGJ1L,EAAA,CAAAqB,SAAA,GACA;IADArB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,qCACA;IAEY1B,EAAA,CAAAqB,SAAA,GAAuG;IAAvGrB,EAAA,CAAAqG,UAAA,CAAArG,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAAuG;IAD/FvG,EAAA,CAAAY,UAAA,YAAA0K,MAAA,CAAA5E,cAAA,CAA0B;IAiBzC1G,EAAA,CAAAqB,SAAA,GAAyC;IAAzCrB,EAAA,CAAAY,UAAA,UAAA0K,MAAA,CAAA3F,WAAA,CAAApB,KAAA,CAAAsC,QAAA,CAAyC;IAE9B7G,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,oCAA2C;IAQ7D1B,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAY,UAAA,uDAAsD,0EAAA0K,MAAA,CAAAvE,gBAAA,kBAAAuE,MAAA,CAAAzL,iBAAA,CAAAmH,QAAA,kDAAAsE,MAAA,CAAArE,kBAAA,wBAAAjH,EAAA,CAAAkH,eAAA,KAAAC,GAAA,EAAAmE,MAAA,CAAAxL,kBAAA,CAAAsH,IAAA,EAAAkE,MAAA,CAAAxL,kBAAA,CAAAuH,IAAA,iFAAAiE,MAAA,CAAAhE,gBAAA,iDAAAgE,MAAA,CAAA/D,iBAAA,aAAAvH,EAAA,CAAA4F,eAAA,KAAAC,GAAA,IAAA2B,QAAA,GAAA8D,MAAA,CAAA3F,WAAA,CAAAI,GAAA,0CAAAyB,QAAA,CAAAxB,QAAA,mBAAAwB,QAAA,GAAA8D,MAAA,CAAA3F,WAAA,CAAAI,GAAA,0CAAAyB,QAAA,CAAAvB,OAAA;IAmB/BjG,EAAA,CAAAqB,SAAA,GACwB;IADxBrB,EAAA,CAAAY,UAAA,WAAA6G,QAAA,GAAA6D,MAAA,CAAA3F,WAAA,CAAAI,GAAA,0CAAA0B,QAAA,CAAAzB,QAAA,8BAAAyB,QAAA,GAAA6D,MAAA,CAAA3F,WAAA,CAAAI,GAAA,0CAAA0B,QAAA,CAAAxB,OAAA,EACwB;IAEhBjG,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,qCAAwC;IAMvE1B,EAAA,CAAAqB,SAAA,GAAyH;IAAzHrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,MAAAC,GAAA,IAAA8F,QAAA,GAAAL,MAAA,CAAA3F,WAAA,CAAAI,GAAA,8BAAA4F,QAAA,CAAA3F,QAAA,mBAAA2F,QAAA,GAAAL,MAAA,CAAA3F,WAAA,CAAAI,GAAA,8BAAA4F,QAAA,CAAA1F,OAAA,GAAyH;IAEtGjG,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,uCAA0C;IAMxD1B,EAAA,CAAAqB,SAAA,GAAwF;IAAxFrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,MAAAC,GAAA,GAAA+F,QAAA,GAAAN,MAAA,CAAA3F,WAAA,CAAAI,GAAA,oCAAA6F,QAAA,CAAA5F,QAAA,cAAwF;IAGnEhG,EAAA,CAAAqB,SAAA,GAAiD;IAAjDrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,8CAAiD;IAOjD1B,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,6CAAoD;IAM5E1B,EAAA,CAAAqB,SAAA,GAA2H;IAA3HrB,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAA4F,eAAA,MAAAC,GAAA,IAAAgG,QAAA,GAAAP,MAAA,CAAA3F,WAAA,CAAAI,GAAA,+BAAA8F,QAAA,CAAA7F,QAAA,mBAAA6F,QAAA,GAAAP,MAAA,CAAA3F,WAAA,CAAAI,GAAA,+BAAA8F,QAAA,CAAA5F,OAAA,GAA2H;IAEvGjG,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAA0B,WAAA,wCAA2C;IAOvC1B,EAAA,CAAAqB,SAAA,GAEtB;IAFsBrB,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA0B,WAAA,uCAEtB;IAOA1B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA0B,WAAA,6CACF;IAG+B1B,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAY,UAAA,YAAA0K,MAAA,CAAA5D,gBAAA,CAAmB;IAqBvB1H,EAAA,CAAAqB,SAAA,GAGhC;IAHgCrB,EAAA,CAAAY,UAAA,aAAA0K,MAAA,CAAA3D,cAAA,CAAAC,aAAA,aAAA0D,MAAA,CAAA3D,cAAA,CAAAC,aAAA,CAAAC,SAAA,UAGhC,UAAA7H,EAAA,CAAA0B,WAAA;IAGG1B,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAY,UAAA,SAAA0K,MAAA,CAAAxD,EAAA,IAAAwD,MAAA,CAAAxD,EAAA,mBAAgC;IAIY9H,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAY,UAAA,SAAA0K,MAAA,CAAAxD,EAAA,IAAAwD,MAAA,CAAAxD,EAAA,mBAAgC;;;;;;IAYhG9H,EAAA,CAAAwD,uBAAA,GAA4C;IAC1CxD,EAAA,CAAAC,cAAA,gCAA0G;IAAxBD,EAAA,CAAAE,UAAA,oBAAA4L,iFAAA;MAAA9L,EAAA,CAAAK,aAAA,CAAA0L,IAAA;MAAA,MAAAC,OAAA,GAAAhM,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAAuL,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAExGjM,EAAA,CAAAW,YAAA,EAAwB;IAC1BX,EAAA,CAAAsF,qBAAA,EAAe;;;;IAHUtF,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAAY,UAAA,iBAAAsL,MAAA,CAAAC,qBAAA,CAAsC,YAAAD,MAAA,CAAAE,OAAA;;;;;;IAI/DpM,EAAA,CAAAwD,uBAAA,GAA0C;IACxCxD,EAAA,CAAAC,cAAA,4BAA+H;IAAvED,EAAA,CAAAE,UAAA,oBAAAmM,6EAAAjM,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAiM,IAAA;MAAA,MAAAC,OAAA,GAAAvM,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAA8L,OAAA,CAAAC,UAAA,CAAApM,MAAA,CAAkB;IAAA,EAAC,oBAAAqM,6EAAArM,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAiM,IAAA;MAAA,MAAAI,OAAA,GAAA1M,EAAA,CAAAQ,aAAA;MAAA,OAAWR,EAAA,CAAAS,WAAA,CAAAiM,OAAA,CAAAC,qBAAA,CAAAvM,MAAA,CAA6B;IAAA,EAAxC;IAErFJ,EAAA,CAAAW,YAAA,EAAoB;IACtBX,EAAA,CAAAsF,qBAAA,EAAe;;;;IAHMtF,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAY,UAAA,iBAAAgM,MAAA,CAAAC,mBAAA,CAAoC;;;AD9azD,OAAM,MAAOC,gBAAgB;EAkF3BC,QAAQA,CAACC,KAAW;IAClB,IAAIrN,iBAAiB,CAAC,IAAI,CAACsN,UAAU,CAAC,EAAE;MACtC,IAAI,CAACC,WAAW,GAAGC,MAAM,CAACC,UAAU;;EAExC;EACAC,YACUC,MAAc,EACf3F,cAA8B,EAC7B4F,cAA8B,EAC9BC,MAAc,EACdC,KAAmB,EACnBC,KAAqB,EACrBC,SAA2B,EAC3BC,eAAgC,EAChCC,aAA4B,EAC3BC,SAAmB,EACpBC,EAAsB,EACtBC,iBAAoC,EACpCC,cAA8B,EAC9BC,WAAsB,EACDjB,UAAe;IAdpC,KAAAK,MAAM,GAANA,MAAM;IACP,KAAA3F,cAAc,GAAdA,cAAc;IACb,KAAA4F,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,SAAS,GAATA,SAAS;IACV,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACU,KAAAjB,UAAU,GAAVA,UAAU;IApGzC,KAAAnM,aAAa,GAAQ;MAAEqN,SAAS,EAAE,IAAI;MAACC,IAAI,EAAC;IAA2B,CAAE;IAGzE,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAAC,gBAAgB,GAAS,KAAK;IAC9B,KAAApC,qBAAqB,GAAY,KAAK;IACtC,KAAAU,mBAAmB,GAAS,KAAK;IACjC,KAAAT,OAAO,GAAU,EAAE;IACnB,KAAAoC,iBAAiB,GAAY,EAAE;IAG/B,KAAA7I,WAAW,GAAG,IAAIlG,gBAAgB,CAAC;MACjCgP,YAAY,EAAE,IAAIjP,kBAAkB,CAAC,MAAM,CAAC;MAC5CkP,iBAAiB,EAAE,IAAIlP,kBAAkB,CAAC,EAAE,CAAC;MAC7CmP,gBAAgB,EAAE,IAAInP,kBAAkB,CAAC,EAAE,CAAC;MAC5CoP,aAAa,EAAE,IAAIpP,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAACmP,QAAQ,CAAC;MAC9DC,OAAO,EAAE,IAAItP,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAACmP,QAAQ,CAAC;MACxDE,IAAI,EAAE,IAAIvP,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAACmP,QAAQ,CAAC;MACrDG,QAAQ,EAAE,IAAIxP,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAACmP,QAAQ,CAAE;MAC1DI,oBAAoB,EAAE,IAAIzP,kBAAkB,CAAC,EAAE,CAAC;MAChD0P,aAAa,EAAE,IAAI1P,kBAAkB,CAAC,EAAE,CAAC;MACzCqH,QAAQ,EAAE,IAAIrH,kBAAkB,CAAC,EAAE,CAAC;MACpC2P,mBAAmB,EAAE,IAAI3P,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAACmP,QAAQ,CAAC;MACpEO,YAAY,EAAE,IAAI5P,kBAAkB,CAAC,EAAE,CAAC;MACxC6P,GAAG,EAAE,IAAI7P,kBAAkB,CAAC,EAAE,CAAC;MAC/B8P,GAAG,EAAE,IAAI9P,kBAAkB,CAAC,EAAE,CAAC;MAC/B+P,EAAE,EAAE,IAAI/P,kBAAkB,CAAC,EAAE,CAAC;MAC9BgQ,iBAAiB,EAAC,IAAIhQ,kBAAkB,CAAC,EAAE,CAAC;MAC5C4G,MAAM,EAAE,IAAI3G,gBAAgB,CAAC;QAC3BqI,EAAE,EAAE,IAAItI,kBAAkB,CAAC,EAAE,CAAC;QAC9B4B,UAAU,EAAE,IAAI5B,kBAAkB,CAAC,EAAE;OACtC,EAACE,UAAU,CAACmP,QAAQ;KACtB,CAAC;IACF,KAAAY,MAAM,GAAW,EAAE;IAOnB,KAAA5H,SAAS,GAAG,KAAK;IAEjB,KAAArC,MAAM,GAA8B;MAClCkK,GAAG,EAAE,GAAG;MACRC,GAAG,EAAE;KACN;IACD,KAAAjK,UAAU,GAAG;MACXkK,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE;KACnB;IACD,KAAAC,SAAS,GAAS,EAAE;IACpB,KAAAC,eAAe,GAAY,KAAK;IACzB,KAAArI,gBAAgB,GAAG,CACxB;MAAC,MAAM,EAAE,MAAM;MAAE,IAAI,EAAE;IAAC,CAAC,EAAC;MAAC,MAAM,EAAE,MAAM;MAAE,IAAI,EAAE;IAAC,CAAC,EAAC;MAAC,MAAM,EAAE,OAAO;MAAE,IAAI,EAAE;IAAC,CAAC,CAE/E;IAID,KAAAI,EAAE,GAAW,EAAE;IAEP,KAAAkI,QAAQ,GAAQ,IAAIC,MAAM,CAACC,IAAI,CAACC,QAAQ,EAAE;IAGlD,KAAAC,iBAAiB,GAAG,0BAA0B;IAC9C,KAAArJ,gBAAgB,GAAW,EAAE;IAC7B,KAAAE,kBAAkB,GAAiB,CAACrH,UAAU,CAACyQ,MAAM,EAAEzQ,UAAU,CAAC0Q,KAAK,EAAE1Q,UAAU,CAAC2Q,WAAW,CAAC;IAEhG,KAAAhJ,iBAAiB,GAAQ,EAAE;IAC3B,KAAA1H,iBAAiB,GAAGA,iBAAiB;IACrC,KAAAC,kBAAkB,GAAGA,kBAAkB;IACvC,KAAA0G,aAAa,GAAS,EAAE;IACxB,KAAAE,cAAc,GAAU,EAAE;IAC1B,KAAAwG,WAAW,GAAKC,MAAM,CAACC,UAAU;IAGjC,KAAAoD,mBAAmB,GAAS,EAAE;IAwB5B,IAAI,CAACjC,gBAAgB,GAAG,IAAI,CAACP,iBAAiB,CAACyC,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACC,MAAM,GAAG,IAAI,CAAClD,MAAM,CAACmD,oBAAoB,EAAE,EAAEC,MAAM,EAAEC,KAAK;IAE/D,IAAIC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC/C,IAAGF,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAC9B,IAAGA,QAAQ,IAAI,GAAG,EAAE;QAClB,IAAI,CAACvJ,iBAAiB,GAAG,WAAW;OACrC,MAAM,IAAGuJ,QAAQ,IAAI,GAAG,EAAE;QACzB,IAAI,CAACvJ,iBAAiB,GAAG,WAAW;OACrC,MAAK,IAAGuJ,QAAQ,IAAI,GAAG,EAAE;QACxB,IAAI,CAACvJ,iBAAiB,GAAG,WAAW;OACrC,MAAK,IAAGuJ,QAAQ,IAAI,GAAG,EAAE;QACxB,IAAI,CAACvJ,iBAAiB,GAAG,YAAY;;;EAG3C;EAGA3D,MAAMA,CAAA;IACJ,IAAI,CAACkK,SAAS,CAACmD,IAAI,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,MAAMC,cAAc,GAAI;MACtBC,OAAO,EACP,CACE;QACE,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,EACD;UACI,WAAW,EAAE,QAAQ;UACrB,YAAY,EAAE,QAAQ;UACtB,OAAO,EAAE,CACL,WAAW,EACX,aAAa,EACb,qBAAqB;SAE5B,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,+BAA+B;QACpD,UAAU,EAAE;UACR,UAAU,EAAE;YACR,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,kBAAkB;UACnC,UAAU,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,WAAW,EAAE;UACT,eAAe,EAAE,gCAAgC;UACjD,aAAa,EAAE;SAClB;QACD,OAAO,EAAE,CACL,eAAe,EACf,mBAAmB;OAE1B,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,YAAY;UACzB,YAAY,EAAE,YAAY;UAC1B,OAAO,EAAE,CACL,SAAS;SAEhB,EACD;UACI,WAAW,EAAE,uBAAuB;UACpC,YAAY,EAAE,gBAAgB;UAC9B,OAAO,EAAE,CACL,OAAO;SAEd,EACD;UACI,WAAW,EAAE,QAAQ;UACrB,YAAY,EAAE,QAAQ;UACtB,OAAO,EAAE,CACL,WAAW,EACX,aAAa,EACb,qBAAqB;SAE5B,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,oDAAoD;QACzE,UAAU,EAAE;UACR,UAAU,EAAE;YACR,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,SAAS;UAC1B,UAAU,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,SAAS;OAEhB,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,WAAW;SAElB,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,gCAAgC;QACrD,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,CAAC,OAAO;YAChB,OAAO,EAAE,QAAQ;YACjB,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,kBAAkB;UACnC,UAAU,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,WAAW,EAAE;UACT,eAAe,EAAE,gCAAgC;UACjD,aAAa,EAAE;SAClB;QACD,OAAO,EAAE,CACL,WAAW;OAElB,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,uBAAuB;UACpC,YAAY,EAAE,gBAAgB;UAC9B,OAAO,EAAE,CACL,OAAO;SAEd,EACD;UACI,WAAW,EAAE,QAAQ;UACrB,YAAY,EAAE,QAAQ;UACtB,OAAO,EAAE,CACL,WAAW,EACX,aAAa,EACb,qBAAqB;SAE5B,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,wCAAwC;QAC7D,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC,QAAQ;YACjB,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,kBAAkB;UACnC,UAAU,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,OAAO;OAEd,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,QAAQ;UACrB,YAAY,EAAE,QAAQ;UACtB,OAAO,EAAE,CACL,WAAW,EACX,aAAa,EACb,qBAAqB;SAE5B,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,gCAAgC;QACrD,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE;YACR,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,WAAW,EACX,aAAa,EACb,qBAAqB;OAE5B,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,UAAU,EACV,WAAW;SAElB,EACD;UACI,WAAW,EAAE,SAAS;UACtB,YAAY,EAAE,SAAS;UACvB,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,wBAAwB;QAC7C,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE;YACR,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC,iBAAiB;YAC1B,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,UAAU,EACV,WAAW;OAElB,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,6BAA6B;UAC1C,YAAY,EAAE,6BAA6B;UAC3C,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;SAElB,EACD;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,4CAA4C;QACjE,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE;YACR,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,6BAA6B,EAC7B,WAAW;OAElB,EACD;QACI,oBAAoB,EAAE,CAClB;UACI,WAAW,EAAE,eAAe;UAC5B,YAAY,EAAE,IAAI;UAClB,OAAO,EAAE,CACL,SAAS,EACT,WAAW;SAElB,CACJ;QACD,mBAAmB,EAAE,eAAe;QACpC,UAAU,EAAE;UACR,QAAQ,EAAE;YACN,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,CAAC;WACZ;UACD,UAAU,EAAE;YACR,KAAK,EAAE,iBAAiB;YACxB,KAAK,EAAE,CAAC;WACX;UACD,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE;YACR,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,CAAC,SAAS;YAClB,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,CAAC;;SAEhB;QACD,UAAU,EAAE,6BAA6B;QACzC,OAAO,EAAE,CACL,SAAS,EACT,WAAW;OAElB;KAEF;IACD,MAAMC,eAAe,GAAGF,cAAc,CAACC,OAAO,CAACE,IAAI,CAAEC,MAAW,IAC9DA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACC,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,CAClD;IAED,IAAI,CAACC,OAAO,GAAGL,eAAe,GAC1BA,eAAe,CAACM,iBAAiB,GACjCR,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAEO,iBAAiB;IAEhDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACH,OAAO,CAAC;IACzB,IAAI,CAAChE,KAAK,CAACoE,aAAa,CAACC,SAAS,CAAEC,WAAW,IAAI;MACjD,IAAI,CAACC,eAAe,GAAGD,WAAW,CAACjM,GAAG,CAAC,UAAU,CAAC;IACpD,CAAC,CAAC;IAEF,IAAI,CAACwF,UAAU,GAAG,IAAI,CAAC0C,cAAc,CAACiE,cAAc,CAACZ,IAAI,CAAEa,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IACtG,IAAI,CAAC1C,GAAG,GAAC2C,OAAO,CAACxB,KAAK,CAACnB,GAAG;IAC1B,IAAI,CAACC,GAAG,GAAC0C,OAAO,CAACxB,KAAK,CAAClB,GAAG;IAC1B,IAAI,CAACjC,KAAK,CAACsE,WAAW,CAACD,SAAS,CAAEO,MAAW,IAAI;MAC/CV,OAAO,CAACC,GAAG,CAAC,MAAM,EAACS,MAAM,CAAC;MAE1B,IAAI,CAACC,WAAW,GAAGD,MAAM,CAACE,SAAS;IACrC,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC/E,KAAK,CAAC4E,MAAM,CAACP,SAAS,CAAEO,MAAM,IAAI;MAErD,IAAI,CAACxK,EAAE,GAAGwK,MAAM,CAAC,IAAI,CAAC;MACtB,IAAI,IAAI,CAACxK,EAAE,IAAI,aAAa,EAAC;QAC3B,IAAI,CAAC4K,kBAAkB,EAAE;QACzB,IAAI,CAACxE,WAAW,CAACyE,YAAY,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;OAClE,MACG;QACF,IAAI,CAACzE,WAAW,CAACyE,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAC9D,MAAMC,kBAAkB,GAAG7B,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC5D,MAAM6B,YAAY,GAAG9B,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE8B,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,IAAI,EAAE;QAChF,MAAMC,sBAAsB,GAAGH,kBAAkB,EAAEE,OAAO,CAACD,YAAY,EAAC,EAAE,CAAC;QAC3E,IAAI,CAAClN,WAAW,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAEiN,QAAQ,CAACD,sBAAsB,CAAC;QAC7E,IAAI,CAACpN,WAAW,CAACI,GAAG,CAAC,qBAAqB,CAAC,EAAEkN,gBAAgB,EAAE;QAE9D,IAAI,CAACC,YAAY,EAAE;QACpB,IAAG,IAAI,CAACxD,GAAG,IAAI,IAAI,CAACC,GAAG,EAAE;UACvB,IAAI,CAACwD,kBAAkB,CAAC;YAACzD,GAAG,EAAE,IAAI,CAACA,GAAG;YAAEC,GAAG,EAAE,IAAI,CAACA;UAAG,CAAC,CAAC;SACxD,MACG;UACF,IAAI,CAACwD,kBAAkB,EAAE;;;IAI/B,CAAC,CAAC;IAEF,IAAI9E,WAAW,GAAG0C,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,EAAEoC,QAAQ,EAAE;IACjE,IAAIC,eAAe,GAAGtC,YAAY,CAC/BC,OAAO,CAAC,iCAAiC,CAAC,EACzCoC,QAAQ,EAAE;IACd,IAAIC,eAAe,IAAIA,eAAe,IAAI,MAAM,EAAE;MAChD,IAAI,CAAC/E,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAAC3I,WAAW,CAAC2N,QAAQ,CAAC,UAAU,CAAC,CAACC,aAAa,CAAC,CAClD7T,UAAU,CAACmP,QAAQ,CACpB,CAAC;MACF,IAAI,CAAClJ,WAAW,CAAC2N,QAAQ,CAAC,UAAU,CAAC,CAACE,sBAAsB,EAAE;;IAEhE,IAAInF,WAAW,EAAE;MACf,IAAI,CAACA,WAAW,GAAGoF,QAAQ,CAACpF,WAAW,CAAC,GAAG,CAAC;;IAE9C,IAAIqF,WAAW,GAAG,IAAI,CAACjG,KAAK,CAAC1H,GAAG,CAAC,SAAS,CAAC;IAC3C,IAAI,CAAC6H,eAAe,CAAC+F,WAAW,CAACD,WAAW,CAAC;IAC7C,IAAInR,IAAI,GAAGmR,WAAW,EAAEnR,IAAI,EAAEqR,KAAK,CAAC,GAAG,CAAC;IACxC,IAAI,CAACjO,WAAW,CAACkO,UAAU,CAAC;MAC1BnF,iBAAiB,EAAEnM,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;MACzCoM,gBAAgB,EAAEpM,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG;MACtC;KACD,CAAC;;IAGF,IAAI,CAACwO,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAEpC,MAAM8C,OAAO,GAAG,IAAI,CAAC7F,cAAc,CAAC6F,OAAO;MAC3C,IAAIA,OAAO,CAACC,OAAO,IAAIC,SAAS,EAAE;QAChC,IAAIlD,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAC/C,IAAIiD,IAAI,GAAGH,OAAO,CAACC,OAAO;QAC1B,IAAIG,GAAG,GAAkBD,IAAI,CAAC3C,IAAI,CAAE6C,OAAY,IAAKA,OAAO,CAACrD,QAAQ,IAAIA,QAAQ,CAAC,IAAI,IAAI/Q,aAAa,EAAE;QACzGgR,YAAY,CAACqD,OAAO,CAAC,SAAS,EAAEF,GAAG,EAAEG,OAAO,CAAC;QAC7C,IAAI,CAAC5G,KAAK,CAAC6G,GAAG,CAAC,mBAAmB,EAAER,OAAO,CAACC,OAAO,CAAC;;KAGvD,MAAM;MACL,IAAI,CAACzM,gBAAgB,GAAGyJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;;IAEvD,IAAG,IAAI,CAAC/C,cAAc,CAACsG,aAAa,EAAE;MACpC,MAAMlG,WAAW,GAAG,IAAI,CAACJ,cAAc,CAACsG,aAAa,CAACR,OAAO,CAACzC,IAAI,CAACkD,IAAI,IAAIA,IAAI,CAACC,GAAG,KAAK,aAAa,CAAC;MACtG,IAAGpG,WAAW,EAAE,IAAI,CAACtH,gBAAgB,GAAG0M,QAAQ,CAACpF,WAAW,CAAC9J,KAAK,CAAC;;IAEvE,IAAI,CAACmC,cAAc,GAAG,IAAI,CAACoJ,SAAS;IAClC,IAAI,CAAC1J,MAAM,CAACsO,YAAY,CAAC3C,SAAS,CAACxN,KAAK,IAAE;MACzC,MAAMoQ,cAAc,GAAG,IAAI,CAACnO,aAAa,EAAE8K,IAAI,CAAEsD,CAAM,IAAKA,CAAC,CAAC9M,EAAE,IAAIvD,KAAK,CAACuD,EAAE,CAAC;MAC5E,IAAI,CAACnC,WAAW,CAACkO,UAAU,CAAC;QAC1BzN,MAAM,EAAE;UACN0B,EAAE,EAAE6M,cAAc,CAAC7M,EAAE;UACrB1G,UAAU,EAAEuT,cAAc,CAACvT;;OAE9B,EAAC;QAACyT,SAAS,EAAE;MAAK,CAAC,CAAC;IACvB,CAAC,CAAC;EACN;EACA3Q,aAAaA,CAAC4Q,GAAQ;IACpB,IAAI,CAACA,GAAG,GAAGA,GAAG;EAChB;EACOhL,mBAAmBA,CAACiL,KAAU;IACnC,IAAI,CAAC1F,GAAG,GAAG0F,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACvF,GAAG,EAAE;IACxC,IAAI,CAACJ,GAAG,GAAGyF,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACtF,GAAG,EAAE;IACxC,IAAI,CAAC3O,QAAQ,GAAG,CAAC;MACfA,QAAQ,EAAE;QAAC0O,GAAG,EAAE,IAAI,CAACL,GAAG;QAAEM,GAAG,EAAE,IAAI,CAACL;MAAG;KACxC,CAAC;IACF,IAAI,CAAC7J,IAAI,GAAG,EAAE;IACd,IAAI,CAACD,MAAM,GAAG,IAAI,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;IACvC,IAAI,CAACkU,UAAU,CAAC,IAAI,CAAC7F,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;EACrC;EAEA6F,WAAWA,CAAA;IACT,IAAI,CAAC1C,QAAQ,CAAC2C,WAAW,EAAE;EAC7B;EAEM1C,kBAAkBA,CAAA;IAAA,IAAA2C,KAAA;IAAA,OAAAC,iBAAA;MACtBD,KAAI,CAACxH,aAAa,CAAC0H,IAAI,EAAE;MAEzB,IAAI;QACF,MAAMC,GAAG,SAAcH,KAAI,CAAC1N,cAAc,CAAC8N,cAAc,CAACJ,KAAI,CAACvN,EAAE,CAAC,CAAC4N,SAAS,EAAE;QAC9EL,KAAI,CAAC1N,cAAc,CAACgO,aAAa,GAAG,IAAI;QACxC,IAAIH,GAAG,CAACvB,IAAI,EAAE;UAEZoB,KAAI,CAACxN,SAAS,GAAG2N,GAAG,CAACvB,IAAI,CAACpM,SAAS;UACnCwN,KAAI,CAAC1N,cAAc,CAACC,aAAa,GAAG4N,GAAG,CAACvB,IAAI;UAC5C,MAAM2B,kBAAkB,GAAGP,KAAI,CAACQ,eAAe,CAACL,GAAG,CAACvB,IAAI,CAAC9E,mBAAmB,CAAC;UAC7E,MAAMkG,KAAI,CAACnC,YAAY,EAAE;UACzB,MAAMyB,cAAc,GAAGU,KAAI,CAACV,cAAc,CAACa,GAAG,CAACvB,IAAI,CAAC7N,MAAM,CAAC;UAC3D,MAAM0P,YAAY,GAAGT,KAAI,CAACS,YAAY,CAACN,GAAG,CAACvB,IAAI,CAAClF,IAAI,CAAC;UAErDsG,KAAI,CAAC1P,WAAW,CAACkO,UAAU,CAAC;YAC1BpF,YAAY,EAAE+G,GAAG,CAACvB,IAAI,CAACxF,YAAY;YACnCC,iBAAiB,EAAE8G,GAAG,CAACvB,IAAI,CAACvF,iBAAiB;YAC7CC,gBAAgB,EAAE6G,GAAG,CAACvB,IAAI,CAACtF,gBAAgB;YAC3C9H,QAAQ,EAAE2O,GAAG,CAACvB,IAAI,CAAC8B,QAAQ;YAC3B5G,mBAAmB,EAAEyG,kBAAkB;YACvC5G,QAAQ,EAAEwG,GAAG,CAACvB,IAAI,CAACjF,QAAQ;YAC3BC,oBAAoB,EAAEuG,GAAG,CAACvB,IAAI,CAAChF,oBAAoB;YACnDC,aAAa,EAAEsG,GAAG,CAACvB,IAAI,CAAC/E,aAAa;YACrCM,iBAAiB,EAAEgG,GAAG,CAACvB,IAAI,CAACzE,iBAAiB;YAC7CT,IAAI,EAAE+G,YAAY,CAACtU,QAAQ;YAC3B4E,MAAM,EAAEuO;WACT,CAAC;UAGF,IAAGa,GAAG,CAACvB,IAAI,CAACxF,YAAY,IAAE,MAAM,IAAI+G,GAAG,CAACvB,IAAI,CAACxF,YAAY,IAAE,MAAM,EAAG;YAClE4G,KAAI,CAAC3N,gBAAgB,CAAC,CAAC,CAAC,CAACnF,IAAI,GAAGiT,GAAG,CAACvB,IAAI,CAACxF,YAAY;;UAEvD4G,KAAI,CAACxT,mBAAmB,GAAE2T,GAAG,CAACvB,IAAI,CAACxF,YAAY;UAC/C4G,KAAI,CAAChG,GAAG,GAAG2G,UAAU,CAACR,GAAG,CAACvB,IAAI,CAACvE,GAAG,CAAC;UACnC2F,KAAI,CAAC/F,GAAG,GAAG0G,UAAU,CAACR,GAAG,CAACvB,IAAI,CAACtE,GAAG,CAAC;UACnC,MAAMf,aAAa,GAAG4G,GAAG,CAACvB,IAAI,CAACrF,aAAa;UAC5C,IAAGyG,KAAI,CAACY,gBAAgB,EAAC;YACvBZ,KAAI,CAACY,gBAAgB,CAACC,aAAa,CAAC3R,KAAK,GAAGqK,aAAa;;UAG3DyG,KAAI,CAACrU,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAE0O,GAAG,EAAE2F,KAAI,CAAChG,GAAG;cAAEM,GAAG,EAAE0F,KAAI,CAAC/F;YAAG;WACzC,CAAC;UACF+F,KAAI,CAAC5P,IAAI,GAAG,CAAC;UACb4P,KAAI,CAAC7P,MAAM,GAAG6P,KAAI,CAACrU,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UAEvC,IAAIwU,GAAG,CAACvB,IAAI,CAACrF,aAAa,IAAI4G,GAAG,CAACvB,IAAI,CAACnF,OAAO,EAAE;YAE9CuG,KAAI,CAAC1P,WAAW,CAACkO,UAAU,CAAC;cAC1BjF,aAAa,EAAE4G,GAAG,CAACvB,IAAI,CAACrF,aAAa;cACrCE,OAAO,EAAE0G,GAAG,CAACvB,IAAI,CAACnF;aACnB,CAAC;WACH,MAAK;YAEJuG,KAAI,CAACH,UAAU,CAACG,KAAI,CAAChG,GAAG,EAAEgG,KAAI,CAAC/F,GAAG,CAAC;;UAKrC+F,KAAI,CAACc,oBAAoB,EAAE;SAC5B,MAAM;UACLd,KAAI,CAAClC,kBAAkB,EAAE;UACzBkC,KAAI,CAACc,oBAAoB,EAAE;;QAE7Bd,KAAI,CAACxH,aAAa,CAACuI,IAAI,EAAE;OAC1B,CAAC,OAAOC,GAAQ,EAAE;QACjBhB,KAAI,CAAC1N,cAAc,CAACgO,aAAa,GAAG,IAAI;QACxCN,KAAI,CAAClC,kBAAkB,EAAE;QACzBkC,KAAI,CAAC9H,cAAc,CAAC+I,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEnB,KAAI,CAAC1H,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEL,GAAG,CAACjK;SACb,CAAC;OACH,SAAS;QACRiJ,KAAI,CAACxH,aAAa,CAACuI,IAAI,EAAE;;IAC1B;EACH;EAEQP,eAAeA,CAACc,WAAmB;IACzC,OAAOA,WAAW,CAACC,MAAM,GAAG,CAAC,GAAGD,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,GAAGF,WAAW;EACxE;EACQhC,cAAcA,CAACvT,UAAkB;IAEvC,MAAMgF,MAAM,GAAG,IAAI,CAACI,aAAa,CAAC8K,IAAI,CAAEsD,CAAM,IAAKA,CAAC,CAACxT,UAAU,KAAKA,UAAU,CAAC;IAC/E,IAAIgF,MAAM,EAAE;MACV,IAAI,CAAC9B,oBAAoB,CAAC8B,MAAM,CAAC0B,EAAE,CAAC;MACpC,IAAI,CAAC0I,mBAAmB,GAAIpK,MAAM,CAAC0Q,MAAM;MACzC,OAAO;QAAEhP,EAAE,EAAE1B,MAAM,CAAC0B,EAAE;QAAE1G,UAAU,EAAEgF,MAAM,CAAChF;MAAU,CAAE;;IAEzD,OAAO;MAAE,IAAI,EAAE,CAAC,CAAC;MAAE,YAAY,EAAE;IAAI,CAAE;EAEzC;EACQ0U,YAAYA,CAACtU,QAAgB;IACnC,MAAMuN,IAAI,GAAG,IAAI,CAACyB,mBAAmB,CAACc,IAAI,CAAEsD,CAAM,IAAKA,CAAC,CAACpT,QAAQ,KAAKA,QAAQ,CAAC;IAC/E,IAAIuN,IAAI,EAAE;MACR,OAAO;QAAEjH,EAAE,EAAEiH,IAAI,CAACjH,EAAE;QAAEtG,QAAQ,EAAEuN,IAAI,CAACvN;MAAQ,CAAE;;IAEjD,OAAO;MAAE,IAAI,EAAE,CAAC,CAAC;MAAE,UAAU,EAAE;IAAI,CAAE;EACvC;EAGA0T,UAAUA,CAAC6B,QAAgB,EAAEC,SAAiB;IAE5C,IAAI,CAAChH,QAAQ,CAACiH,OAAO,CACnB;MAAChC,QAAQ,EAAE;QAACvF,GAAG,EAAEqH,QAAQ;QAAEpH,GAAG,EAAEqH;MAAS;IAAC,CAAC,EAC3C,CAAC5F,OAAiE,EAAE8F,MAAc,KAAI;MACpF,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnB,IAAI9F,OAAO,CAAC,CAAC,CAAC,EAAE;UAEd,IAAI,CAACpQ,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAC0O,GAAG,EAAEqH,QAAQ;cAAEpH,GAAG,EAAEqH;YAAS;WACzC,CAAC;UACF,IAAI,CAACxR,MAAM,GAAG,IAAI,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UACvC,IAAI,CAACyE,IAAI,GAAG,EAAE;UACd,IAAI,CAACiM,OAAO,GAAGN,OAAO,CAAC,CAAC,CAAC,CAACO,iBAAiB;UAC3C,IAAGP,OAAO,CAAC,CAAC,CAAC,EAAE+F,kBAAkB,CAACP,MAAM,EAAC;YACvC,MAAM7H,IAAI,GAAKqC,OAAO,CAAC,CAAC,CAAC,CAAC+F,kBAAkB,CAAC7F,IAAI,CAAEkD,IAAS,IAAKA,IAAI,CAAChD,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACjG,IAAI,CAACjD,iBAAiB,GAAGO,IAAI,CAACqI,SAAS;;UAGzC,IAAI,CAACzR,WAAW,CAACkO,UAAU,CAAC;YAC1BjF,aAAa,EAAE,IAAI,CAAC8C,OAAO;YAC3B5C,OAAO,EAAEsC,OAAO,CAACA,OAAO,CAACwF,MAAM,GAAG,CAAC,CAAC,CAACjF;YACrC;WACD,CAAC;;UACF,IAAI,CAACzO,QAAQ,EAAE;UACf,IAAI,CAAC4B,cAAc,EAAE;UACrB,IAAI,CAACiJ,EAAE,CAACsJ,aAAa,EAAE;SACxB,MAAM;UACL,IAAI1X,iBAAiB,CAAC,IAAI,CAACsN,UAAU,CAAC,EAAE;YACtCE,MAAM,CAACmK,KAAK,CAAC,kBAAkB,CAAC;;;OAGrC,MAAM;QACL,IAAI3X,iBAAiB,CAAC,IAAI,CAACsN,UAAU,CAAC,EAAE;UACtCE,MAAM,CAACmK,KAAK,CAAC,0BAA0B,GAAGJ,MAAM,CAAC;;;IAIvD,CAAC,CACF;EACH;EAEAjN,KAAKA,CAAA;IACH,IAAI,CAACgM,gBAAgB,CAACC,aAAa,CAAC3R,KAAK,GAAG,EAAE;EAEhD;EAEAvB,QAAQA,CAAA;IAEN,IAAI,CAAC2C,WAAW,CAACkO,UAAU,CAAC;MAC1BxE,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC+D,QAAQ,EAAE,GAAG,EAAE;MACxC9D,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC8D,QAAQ,EAAE,GAAG;MACtC;KACD,CAAC;;IAGF,IAAI,CAACvF,aAAa,CAAC0H,IAAI,EAAE;IACzB,IAAI,IAAI,CAAC5P,WAAW,CAACpB,KAAK,CAACsC,QAAQ,IAAI,EAAE,EACvC,IAAI,CAAClB,WAAW,CAACpB,KAAK,CAACsC,QAAQ,GAAG,CAAC;IAErC,IAAI,IAAI,CAAClB,WAAW,CAAC+F,KAAK,EAAE;MAC1B,MAAM6L,SAAS,GAAG;QAChB,GAAG,IAAI,CAAC5R,WAAW,CAACpB,KAAK;QACzB6B,MAAM,EAAE,IAAI,CAACT,WAAW,CAACpB,KAAK,CAAC6B,MAAM,CAAChF,UAAU;QAChD+N,mBAAmB,EAAE,IAAI,CAACxJ,WAAW,CAAC2N,QAAQ,CAACnE,mBAAmB,CAAC5K,KAAK,CAACiT,UAAU,CAACC,KAAK,CAAC,CAAC;OAC5F;MACD,IAAI,IAAI,CAAC9R,WAAW,CAACpB,KAAK,CAACsC,QAAQ,KAAK,EAAE,EAAE,OAAO,IAAI,CAAClB,WAAW,CAACpB,KAAK,CAACsC,QAAQ;MAClF,IAAI,CAACc,cAAc,CAAC6E,UAAU,CAAC+K,SAAS,CAAC,CAACxF,SAAS,CAAC;QAClD2F,IAAI,EAAGlC,GAAQ,IAAI;UACjB,IAAGA,GAAG,EAAEmC,OAAO,EAAC;YACd,IAAI,CAAC9J,aAAa,CAACuI,IAAI,EAAE;YACzB,IAAI,CAACjK,qBAAqB,GAAG,IAAI;YACjC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACuB,SAAS,CAAC8I,OAAO,CAAC,2CAA2C,CAAC;WACnF,MAAK;YACJ,IAAI,CAAC5I,aAAa,CAACuI,IAAI,EAAE;YACzB,IAAI,CAAC7I,cAAc,CAAC+I,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,OAAO,EAAEhB,GAAG,EAAEpJ;aACf,CAAC;;QAGN,CAAC;QACDwL,KAAK,EAAGvB,GAAQ,IAAI;UAElB,IAAI,CAACxI,aAAa,CAACuI,IAAI,EAAE;UACzB,IAAI,CAAC7I,cAAc,CAAC+I,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAEL;WACT,CAAC;QACJ;OACD,CAAC;;EAEN;EACApK,SAASA,CAAA;IACP,IAAI,CAACE,qBAAqB,GAAG,KAAK;IAClC,IAAG,IAAI,CAAC8F,eAAe,EAAC;MACpB,IAAI,CAACzE,MAAM,CAACqK,aAAa,CAAC,yBAAyB,CAAC;KACvD,MAAM;MACH,IAAI,CAACrK,MAAM,CAACsK,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAEhD;EACAxU,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC+L,GAAG,EAAE+D,QAAQ,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC9D,GAAG,EAAE8D,QAAQ,EAAE,KAAK,EAAE,IAAI,IAAI,CAACzN,WAAW,CAAC2N,QAAQ,CAAC,eAAe,CAAC,CAAC/O,KAAK,KAAK,EAAE,EAAE;MACzH,IAAI,CAACgJ,cAAc,CAAC+I,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,iCAAiC;OAClE,CAAC;MACF;;IAGF,IAAI,CAAC9Q,WAAW,CAACkO,UAAU,CAAC;MAC1BxE,GAAG,EAAE,IAAI,CAACA,GAAG,EAAE+D,QAAQ,EAAE;MACzB9D,GAAG,EAAE,IAAI,CAACA,GAAG,EAAE8D,QAAQ,EAAE;MACzB7D,EAAE,EAAE,IAAI,CAAC5H,cAAc,CAACC,aAAa,CAACE;KACvC,CAAC;IAEF,IAAI,CAAC+F,aAAa,CAAC0H,IAAI,EAAE;IAEzB,IAAI,IAAI,CAAC5P,WAAW,CAAC+F,KAAK,EAAE;MAC1B,MAAM6L,SAAS,GAAG;QAChB,GAAG,IAAI,CAAC5R,WAAW,CAACpB,KAAK;QACzB6B,MAAM,EAAE,IAAI,CAACT,WAAW,CAACpB,KAAK,CAAC6B,MAAM,CAAChF,UAAU;QAChD+N,mBAAmB,EAAE,IAAI,CAACxJ,WAAW,CAAC2N,QAAQ,CAACnE,mBAAmB,CAAC5K,KAAK,CAACiT,UAAU,CAACC,KAAK,CAAC,CAAC;OAC5F;MAED,IAAI,CAAC9P,cAAc,CAACoQ,aAAa,CAACR,SAAS,CAAC,CAACxF,SAAS,CAAC;QACrD2F,IAAI,EAAGlC,GAAQ,IAAI;UACjB,IAAI,CAAC3H,aAAa,CAACuI,IAAI,EAAE;UACzB,IAAI,CAACZ,GAAG,CAACmC,OAAO,EAAE;YAChB,IAAI,CAACpK,cAAc,CAAC+I,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;cAC3DC,MAAM,EAAE,IAAI,CAAC/I,SAAS,CAAC8I,OAAO,CAACjB,GAAG,CAACpJ,OAAO;aAC3C,CAAC;WACH,MAAM;YACL,IAAI,CAACD,qBAAqB,GAAG,IAAI;YACjC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACuB,SAAS,CAAC8I,OAAO,CAAC,6CAA6C,CAAC;;QAExF,CAAC;QACDmB,KAAK,EAAGvB,GAAQ,IAAI;UAClB,IAAI,CAACxI,aAAa,CAACuI,IAAI,EAAE;UACzB,IAAI,CAAC7I,cAAc,CAAC+I,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;YAC3DC,MAAM,EAAEL;WACT,CAAC;QACJ;OACD,CAAC;;EAEN;EAGA2B,WAAWA,CAACC,GAAQ;IAClB,IAAI,CAACC,MAAM,CAACD,GAAG,CAAC1T,KAAK,CAAC,EAAE;MACtB,IAAI,CAACoB,WAAW,CAACpB,KAAK,CAACsC,QAAQ,GAAG,EAAE;MACpC,IAAI,CAAClB,WAAW,CAACpB,KAAK,CAAC4T,KAAK,GAAG,EAAE;;IAEnC,OAAO,KAAK;EACd;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,GAAG,EAAE;KACxB,MAAM;MACL,IAAI,CAACA,aAAa,GAAGtH,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,EAAEoC,QAAQ,EAAE;MACrE,IAAI,IAAI,CAACiF,aAAa,EACpB,IAAI,CAACA,aAAa,GAAG,IAAI,CAACA,aAAa,GAAG,cAAc,CAAC,KACtD,IAAI,CAACA,aAAa,GAAG,iBAAiB;;EAE/C;EAEAnV,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACyC,WAAW,CAAC+F,KAAK,EAAE,OAAO,IAAI;EAC1C;EAEAtG,YAAYA,CAAA;IACV,IAAI,CAACuC,cAAc,CAAC2Q,UAAU,CAAC,IAAI,CAACxQ,EAAE,CAAC,CAACiK,SAAS,CAAC;MAChD2F,IAAI,EAAGlC,GAAQ,IAAI;QACjB,IAAI,CAACjI,cAAc,CAAC+I,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAE,IAAI,CAAC/I,SAAS,CAAC8I,OAAO,CAC5B,6CAA6C;SAEhD,CAAC;QACF,IAAG,IAAI,CAAClE,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,EAAE,EAAE;UAC9C,IAAI,CAAC/E,MAAM,CAACsK,QAAQ,CAAC,CAAC,IAAI,CAACvF,WAAW,CAAC,CAAC;SACzC,MAAM;UACL,IAAI,CAAC/E,MAAM,CAACsK,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;MAE9C,CAAC;MACDF,KAAK,EAAGvB,GAAQ,IAAI;QAClB,IAAI,CAACxI,aAAa,CAACuI,IAAI,EAAE;QACzB,IAAI,CAAC7I,cAAc,CAAC+I,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,IAAI,CAAC7I,SAAS,CAAC8I,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEL;SACT,CAAC;MACJ;KACD,CAAC;EACJ;EAGQlD,kBAAkBA,CAACoF,gBAAqB;IAC9C,IAAI,CAAC5Q,cAAc,CAACC,aAAa,GAAG,IAAI;IACxC,IAAI,aAAa,IAAI4Q,SAAS,EAAE;MAC9B,IAAG,CAACD,gBAAgB,EAAE;QACpBC,SAAS,CAACC,WAAW,CAACC,kBAAkB,CAAE1X,QAAQ,IAAI;UACpD,IAAI,CAACqO,GAAG,GAAGrO,QAAQ,CAAC2X,MAAM,CAAC5B,QAAQ;UACnC,IAAI,CAACzH,GAAG,GAAGtO,QAAQ,CAAC2X,MAAM,CAAC3B,SAAS;UACpC,IAAI,CAAChW,QAAQ,GAAG,CAAC;YACfA,QAAQ,EAAE;cAAC0O,GAAG,EAAE,IAAI,CAACL,GAAG;cAAEM,GAAG,EAAE,IAAI,CAACL;YAAG;WACxC,CAAC;UACF,IAAI,CAAC9J,MAAM,GAAG,IAAI,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;UACvC,IAAI,CAACyE,IAAI,GAAG,EAAE;UAEd,IAAI,CAACyP,UAAU,CAAC,IAAI,CAAC7F,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;UACnC,IAAI,CAAC6G,oBAAoB,EAAE;QAC7B,CAAC,CAAC;OACH,MACI;QAED,IAAI,CAAC9G,GAAG,GAAG2G,UAAU,CAACuC,gBAAgB,CAAC7I,GAAG,CAAC;QAC3C,IAAI,CAACJ,GAAG,GAAG0G,UAAU,CAACuC,gBAAgB,CAAC5I,GAAG,CAAC;QAC3C,IAAI,CAAC3O,QAAQ,GAAG,CAAC;UACfA,QAAQ,EAAE;YAAE0O,GAAG,EAAE,IAAI,CAACL,GAAG;YAAEM,GAAG,EAAE,IAAI,CAACL;UAAG;SACzC,CAAC;QAEF,IAAI,CAAC4F,UAAU,CAAC,IAAI,CAAC7F,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;QACnC,IAAI,CAAC6G,oBAAoB,EAAE;;;EAKnC;EACApS,UAAUA,CAACiJ,KAAW;IACpB,IAAI4L,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC/L,KAAK,CAAC4L,MAAM,CAAC,CAAC;IACrD,IAAI,CAACvJ,GAAG,GAAGuJ,MAAM,CAAClJ,GAAG;IACrB,IAAI,CAACJ,GAAG,GAAEsJ,MAAM,CAACjJ,GAAG;IACpB,IAAI,CAAC3O,QAAQ,GAAG,CAAC;MACfA,QAAQ,EAAE;QAAC0O,GAAG,EAAE,IAAI,CAACL,GAAG;QAAEM,GAAG,EAAE,IAAI,CAACL;MAAG;KACxC,CAAC;IACF,IAAI,CAAC9J,MAAM,GAAG,IAAI,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ;IACvC,IAAI,CAACyE,IAAI,GAAG,EAAE;IACd,IAAI,CAACyP,UAAU,CAAC,IAAI,CAAC7F,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC;EACrC;EACA6G,oBAAoBA,CAAA;IAClB,IAAIxW,iBAAiB,CAAC,IAAI,CAACsN,UAAU,CAAC,EAAE;MACtC,MAAM+L,UAAU,GAASC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACtDF,UAAU,CAACG,KAAK,GAAG,GAAG;MACtB,IAAI,CAACrE,GAAG,CAACxB,QAAQ,CAACrD,MAAM,CAACC,IAAI,CAACkJ,eAAe,CAACC,YAAY,CAAC,CAACC,IAAI,CAACN,UAAU,CAAC;;EAEhF;EACAtY,aAAaA,CAACsM,KAAU;IACtB,IAAIA,KAAK,CAAC4L,MAAM,IAAI,IAAI,EAAE;MACxB,MAAMA,MAAM,GAAG5L,KAAK,CAAC4L,MAAM,CAACW,MAAM,EAAE;MAC1C;MACM,IAAI,CAACrE,UAAU,CAAC0D,MAAM,CAAClJ,GAAG,EAAEkJ,MAAM,CAACjJ,GAAG,CAAC;;EAE3C;EACA6J,SAASA,CAAA;IACP,MAAMC,MAAM,GAAS;MACnBC,WAAW,EAAG,CAAC;MACfC,QAAQ,EAAC,CAAC;MACVC,gBAAgB,EAAG;KACpB;IACD,IAAI,CAACjS,cAAc,CAACkS,YAAY,CAACJ,MAAM,CAAC,CAAC1H,SAAS,CAAEyD,GAAQ,IAAI;MAC9D,IAAGA,GAAG,CAACmC,OAAO,EAAC;QACb,IAAI,CAAC7H,SAAS,GAAG0F,GAAG,CAACvB,IAAI,CAACF,OAAO;;IAErC,CAAC,CAAC;EACJ;EACAjP,cAAcA,CAAA;IACZ,IAAIgV,QAAQ,GAAG,IAAI7J,MAAM,CAACC,IAAI,CAACC,QAAQ,EAAE;IACzC,IAAI,CAACJ,eAAe,GAAG,IAAI;IAC3B+J,QAAQ,CAAC7C,OAAO,CAAC;MAAE,SAAS,EAAE,IAAI,CAACtR,WAAW,CAAC2N,QAAQ,CAAC,eAAe,CAAC,CAAC/O;IAAK,CAAE,EAAG,CAAC6M,OAAY,EAAE8F,MAAW,KAAI;MAC/G,IAAIA,MAAM,IAAIjH,MAAM,CAACC,IAAI,CAAC6J,cAAc,CAACC,EAAE,EAAE;QAC3C,IAAG5I,OAAO,CAAC,CAAC,CAAC,CAAC+F,kBAAkB,CAACP,MAAM,EAAC;UACtC,MAAM7H,IAAI,GAAKqC,OAAO,CAAC,CAAC,CAAC,CAAC+F,kBAAkB,CAAC7F,IAAI,CAAEkD,IAAS,IAAKA,IAAI,CAAChD,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;UACjG,IAAG1C,IAAI,CAACqI,SAAS,KAAK,IAAI,CAAC5I,iBAAiB,EAAC;YAC5C,IAAI,CAACuB,eAAe,GAAG,KAAK;YAC5B,IAAI,CAAChC,EAAE,CAACsJ,aAAa,EAAE;;;;IAI9B,CAAC,CAAC;EACJ;EAEMnE,YAAYA,CAAA;IAAA,IAAA+G,MAAA;IAAA,OAAA3E,iBAAA;MAChB,IAAImE,MAAM,GAAG;QACXC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE,CAAC;QACXC,gBAAgB,EAAE;OACnB;MACD,IAAI;QACF,MAAMpE,GAAG,SAAcyE,MAAI,CAACtS,cAAc,CAACuS,aAAa,CAACT,MAAM,CAAC,CAAC/D,SAAS,EAAE;QAC5E,IAAIF,GAAG,CAACmC,OAAO,EAAE;UACfsC,MAAI,CAACzT,aAAa,GAAGgP,GAAG,CAACvB,IAAI,CAACF,OAAO;UAErCkG,MAAI,CAACvT,cAAc,GAAG8O,GAAG,CAACvB,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC+C,MAAM;;OAEnD,CAAC,OAAOc,KAAK,EAAE;QACdqC,MAAI,CAAC1M,cAAc,CAAC+I,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAEyD,MAAI,CAACtM,SAAS,CAAC8I,OAAO,CAAC,0BAA0B;SAC3D,CAAC;;IACH;EACH;EAGAnS,oBAAoBA,CAAC6V,QAAa;IAChC,MAAMxF,cAAc,GAAG,IAAI,CAACnO,aAAa,CAAC8K,IAAI,CAAElL,MAAoB,IAAKA,MAAM,CAAC0B,EAAE,KAAKqS,QAAQ,CAAC;IAChG,IAAIxF,cAAc,EAAE;MAClB,IAAI,CAACjO,cAAc,GAAGiO,cAAc,EAAEmC,MAAM;;EAEhD;EACAxU,iBAAiBA,CAAC8P,IAAY;IAC5B,IAAI,CAACvQ,mBAAmB,GAAGuQ,IAAI;IAC/B,IAAI,CAACzM,WAAW,CAACkO,UAAU,CAAC;MAAEpF,YAAY,EAAE2D;IAAI,CAAE,CAAC;IACnD,IAAG,IAAI,CAACvQ,mBAAmB,IAAE,OAAO,EAAC;MACnC,IAAI,CAACgL,mBAAmB,GAAC,IAAI;;EAEjC;EAEAL,UAAUA,CAACQ,KAAS;IAClB,IAAGA,KAAK,EAAC;MACP,IAAI,CAACrH,WAAW,CAACkO,UAAU,CAAC;QAAEpF,YAAY,EAAEzB;MAAK,CAAE,CAAC;MACpD,IAAI,CAACtF,gBAAgB,CAAC,CAAC,CAAC,CAACnF,IAAI,GAAGyK,KAAK;;IAEvC,IAAI,CAACH,mBAAmB,GAAC,KAAK;EAChC;EAEAF,qBAAqBA,CAACK,KAAS;IAC7B,IAAI,CAACH,mBAAmB,GAACG,KAAK;EAChC;EACA,IAAI5G,MAAMA,CAAA;IACR,OAAQ,IAAI,CAACT,WAAW,CAACI,GAAG,CAAC,QAAQ,CAAsB;EAC7D;EAAC,QAAAqU,CAAA,G;qBArpCUtN,gBAAgB,EAAA9M,EAAA,CAAAqa,iBAAA,CAAAra,EAAA,CAAAsa,MAAA,GAAAta,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAxa,EAAA,CAAAqa,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA1a,EAAA,CAAAqa,iBAAA,CAAAM,EAAA,CAAAC,MAAA,GAAA5a,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAM,YAAA,GAAA7a,EAAA,CAAAqa,iBAAA,CAAAM,EAAA,CAAAG,cAAA,GAAA9a,EAAA,CAAAqa,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAAhb,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAU,eAAA,GAAAjb,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAW,aAAA,GAAAlb,EAAA,CAAAqa,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAApb,EAAA,CAAAqa,iBAAA,CAAAra,EAAA,CAAAqb,iBAAA,GAAArb,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAe,iBAAA,GAAAtb,EAAA,CAAAqa,iBAAA,CAAAE,EAAA,CAAAgB,cAAA,GAAAvb,EAAA,CAAAqa,iBAAA,CAAAmB,EAAA,CAAAC,UAAA,GAAAzb,EAAA,CAAAqa,iBAAA,CAsGjB9a,WAAW;EAAA;EAAA,QAAAmc,EAAA,G;UAtGV5O,gBAAgB;IAAA6O,SAAA;IAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;iBAAhBC,GAAA,CAAAhP,QAAA,CAAA3M,MAAA,CAAgB;QAAA,UAAAJ,EAAA,CAAAgc,eAAA;;;;;;;;QCvC7Bhc,EAAA,CAAAwC,UAAA,IAAAyZ,wCAAA,4BAkNe;QAEfjc,EAAA,CAAAwC,UAAA,IAAA0Z,uCAAA,oCAAAlc,EAAA,CAAAmc,sBAAA,CAyPc;QAEdnc,EAAA,CAAAwC,UAAA,IAAA4Z,wCAAA,0BAIe;QACfpc,EAAA,CAAAwC,UAAA,IAAA6Z,wCAAA,0BAIe;;;;QAxdArc,EAAA,CAAAY,UAAA,SAAAmb,GAAA,CAAAxN,gBAAA,IAAAwN,GAAA,CAAA7O,WAAA,QAA8C,aAAAoP,GAAA;QA+c9Ctc,EAAA,CAAAqB,SAAA,GAA2B;QAA3BrB,EAAA,CAAAY,UAAA,SAAAmb,GAAA,CAAA5P,qBAAA,CAA2B;QAK3BnM,EAAA,CAAAqB,SAAA,GAAyB;QAAzBrB,EAAA,CAAAY,UAAA,SAAAmb,GAAA,CAAAlP,mBAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}