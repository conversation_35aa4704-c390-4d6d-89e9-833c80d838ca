import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { PasswordModule } from 'primeng/password';
import { InputTextModule } from 'primeng/inputtext';
import { InputMaskModule } from 'primeng/inputmask';
import { CountryISO, PhoneNumberFormat, SearchCountryField, NgxIntlTelInputModule } from 'ngx-intl-tel-input-gg';
import { LoaderService, UserService, StoreService, AppDataService, PermissionService } from '@core/services';
import { ForgotPasswordResponse } from '../../models/Password.type';
import {EmailInputComponent} from "../email-input/email-input.component";
import {PhoneInputComponent} from "../phone-input/phone-input.component";


@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    ButtonModule,
    PasswordModule,
    InputTextModule,
    InputMaskModule,
    NgxIntlTelInputModule,
    EmailInputComponent,
    PhoneInputComponent,
  ],
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {
  @ViewChild('phoneInput2', { read: ElementRef }) phoneInput!: ElementRef;
  readonly PhoneNumberFormat = PhoneNumberFormat;
  readonly SearchCountryField = SearchCountryField;
  readonly preferredCountries: CountryISO[] = [
    CountryISO.Uganda,
    CountryISO.Ghana,
    CountryISO.CôteDIvoire
  ];

  resetPasswordForm: FormGroup;
  customPlaceHolder: string = '';
  phoneInputLength: number = 12;
  CustomCountryISO: CountryISO = CountryISO.Uganda;
  submitted: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth: number;
  email: string | null = null;
  isEmailValid: boolean = false;
  activeTab: string = 'mobileNumber';
  counter: any = 0;
  userPhoneNumber: string;
  nationalNumber: any;
  rawValue: any='';

  constructor(
    private readonly fb: FormBuilder,
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly user: UserService,
    private readonly messageService: MessageService,
    private readonly loaderService: LoaderService,
    private readonly store: StoreService,
    private readonly appDataService: AppDataService,
    private readonly permissionService: PermissionService
  ) {
    this.resetPasswordForm = this.fb.group({
      phoneNumber: ['', Validators.required],
      email: ['', Validators.required],
    });

    this.setCustomPlaceholder();
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');
    const isoCode = localStorage.getItem('isoCode');
    if (isoCode) {
      this.CustomCountryISO = isoCode as CountryISO;
    }
    this.setPhoneInputLength();
    this.resetPasswordForm.controls.phoneNumber.valueChanges.subscribe(value => {
          let tenantId = localStorage.getItem('tenantId');
          let firstChar = value?.number?.charAt(0)
          let countryCode = value?.countryCode
          if( countryCode == 'GH'){
            //  if(firstChar == '0'){
            //     this.counter++
            //   }
            // this.rawValue = inputElement?.value || '';
            // if( this.rawValue[0] == '0' && tenantId == '2' ){
            //   this.phoneInputLength=10
                // if(value?.number.length <2){
                //   this.counter = 0
                // }
            // }else{
                if (this.appDataService.configuration) {
                  const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
                  if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
                }
            // }
          }else if(countryCode == 'UG'){
              const inputElement = this.phoneInput.nativeElement.querySelector('input');
              this.rawValue = inputElement?.value || '';
              if (this.appDataService.configuration) {
                  const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
                  if (phoneLength) {
                    this.phoneInputLength = parseInt(phoneLength.value) - (this.rawValue[0] === '0' ? 0 : 1);
                  }
                }
            }
      })
  }
 

  private setCustomPlaceholder(): void {
    const tenantId = localStorage.getItem('tenantId');
    if (!tenantId) return;

    switch (tenantId) {
      case '1':
        this.customPlaceHolder = 'XXXXXXXXXX';
        break;
      case '2':
      case '3':
        this.customPlaceHolder = 'XXXXXXXXX';
        break;
      case '4':
        this.customPlaceHolder = 'XXXXXXXXXX';
        break;
    }
  }

  private setPhoneInputLength(): void {
    if (!this.appDataService.configuration) return;

    const phoneLength = this.appDataService.configuration.records.find(
      item => item.key === 'PhoneLength'
    );

    if (phoneLength) {
      this.phoneInputLength = parseInt(phoneLength.value);
    }
  }

  resetPassword(): void {
    this.loaderService.show();
    this.submitted = true;

    let userName: string | null = null;

    if (this.activeTab === 'mobileNumber') {
      // Check phone form only
      if (!this.resetPasswordForm.controls.phoneNumber.valid) {
        this.loaderService.hide();
        return;
      }
      userName = this.resetPasswordForm.controls.phoneNumber.value.e164Number.slice(1);
      let tenantISO = localStorage.getItem("isoCode")
      if(this.userPhoneNumber && this.rawValue.length == 10 && tenantISO == 'UG'){
        userName = this.userPhoneNumber.slice(1);
      }
    }

    if (this.activeTab === 'email') {
      // Check email only
      if (!this.isEmailValid || !this.email) {
        this.loaderService.hide();
        return;
      }
      userName = this.email;
    }

    if (!userName) {
      this.loaderService.hide();
      return;
    }

    this.user.username = userName;

    this.user.ForgotPassword({ userName }).subscribe({
      next: (res: any) => {
        const response = res as ForgotPasswordResponse;
        if (response.success) {
          this.handleSuccess(response.data.requestId, userName!);
        } else {
          this.handleError(response.message);
        }
        this.loaderService.hide();
      },
      error: (err: Error) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.fetchError'),
          detail: err.message
        });
      }
    });
  }

  private handleSuccess(requestId: string, phoneNumber: string): void {
    this.store.set('verificationCode', requestId);
    this.store.set('userPhone', phoneNumber);
    this.router.navigate(['/otp']);

    this.messageService.add({
      severity: 'success',
      summary: this.translate.instant('ResponseMessages.phoneNumberIsValid'),
      detail: this.translate.instant('ResponseMessages.phoneNumberIsValid')
    });
  }

  private handleError(message: string): void {
    if (this.activeTab === 'email'){
      this.messageService.add({
        severity: 'error',
        summary: message
      });
    }else {
      this.messageService.add({
        severity: 'error',
        summary: message === 'Exceed number of operation per hour'
          ? this.translate.instant('ErrorMessages.exceedOperations')
          : this.translate.instant('ErrorMessages.phoneNumberIsUnvalid')
      });
    }

  }


  get floatingLabel(): boolean {
    return this.screenWidth <= 767;
  }

  get labelColor(): string {
    return this.screenWidth <= 767 ? 'grey' : 'white';
  }

  onEmailChange(value: string | null) {
    this.email = value;
  }

  onEmailValidationChange(isValid: boolean) {
    this.isEmailValid = isValid;
  }


  switchTab(tab: string) {
    this.activeTab = tab;
    if (tab === 'mobileNumber') {
      this.email = null;
      this.isEmailValid = false;
    }
    if (tab === 'email') {
      this.resetPasswordForm.reset();
    }
  }

}


