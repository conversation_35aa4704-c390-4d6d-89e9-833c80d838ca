{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@pages/cart/components/services/is-opt-out.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/dialog\";\nfunction OptOutModalComponent_ng_template_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function OptOutModalComponent_ng_template_1_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.isOptOutModal = false);\n    });\n    i0.ɵɵelementStart(2, \"span\", 9);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"optOutModal.close\"), \"\");\n  }\n}\nfunction OptOutModalComponent_ng_template_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function OptOutModalComponent_ng_template_1_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.cancel());\n    });\n    i0.ɵɵelement(2, \"img\", 3);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function OptOutModalComponent_ng_template_1_div_7_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onProceed());\n    });\n    i0.ɵɵelement(7, \"img\", 13);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 2, \"deleteItemPopupComponent.cancel\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 4, \"orderDetails.proceed\"), \" \");\n  }\n}\nfunction OptOutModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 2);\n    i0.ɵɵelement(2, \"img\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, OptOutModalComponent_ng_template_1_div_6_Template, 5, 3, \"div\", 5);\n    i0.ɵɵtemplate(7, OptOutModalComponent_ng_template_1_div_7_Template, 11, 6, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, \"optOutModal.message\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.proceedBtnFlag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.proceedBtnFlag);\n  }\n}\nconst _c0 = function () {\n  return {\n    width: \"360px\"\n  };\n};\nconst _c1 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class OptOutModalComponent {\n  translate;\n  isOptOutService;\n  isOptOutModal = false;\n  proceedBtnFlag = false;\n  onProceedFlag = new EventEmitter();\n  constructor(translate, isOptOutService) {\n    this.translate = translate;\n    this.isOptOutService = isOptOutService;\n  }\n  onProceed() {\n    this.onProceedFlag.emit(true);\n  }\n  cancel() {\n    this.isOptOutModal = false;\n    this.onProceedFlag.emit(false);\n  }\n  static ɵfac = function OptOutModalComponent_Factory(t) {\n    return new (t || OptOutModalComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.IsOptOutService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: OptOutModalComponent,\n    selectors: [[\"app-opt-out-modal\"]],\n    inputs: {\n      isOptOutModal: \"isOptOutModal\",\n      proceedBtnFlag: \"proceedBtnFlag\"\n    },\n    outputs: {\n      onProceedFlag: \"onProceedFlag\"\n    },\n    decls: 2,\n    vars: 10,\n    consts: [[1, \"cancel-all-order\", \"rounded\", 3, \"visible\", \"breakpoints\", \"draggable\", \"modal\", \"resizable\", \"showHeader\"], [\"pTemplate\", \"content\"], [1, \"cancel-proceed-btns\", \"mt-7\"], [\"alt\", \"Logo\", \"height\", \"80\", \"ngSrc\", \"assets/icons/quit-cancel.svg\", \"width\", \"80\", 1, \"mb-2\"], [1, \"cancel-order-confirm-questions\", \"my-4\"], [\"class\", \"d-flex\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"p-element\", \"ml-1\", \"cancel-btn\", \"main-btn\", \"p-button\", \"p-component\", \"ng-star-inserted\", 3, \"click\"], [1, \"p-button-label\", \"cancel-text-btn\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"cancel-proceed-btns\", \"d-flex\", \"flex-column\", \"align-items-center\", \"m-end-3\", 3, \"click\"], [1, \"cancel-proceed-btns\", \"d-flex\", \"flex-column\", \"align-items-center\", 3, \"click\"], [\"alt\", \"Logo\", \"height\", \"80\", \"ngSrc\", \"assets/icons/proceed-cancel.svg\", \"width\", \"80\", 1, \"mb-2\"]],\n    template: function OptOutModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵtemplate(1, OptOutModalComponent_ng_template_1_Template, 8, 5, \"ng-template\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(8, _c0));\n        i0.ɵɵproperty(\"visible\", ctx.isOptOutModal)(\"breakpoints\", i0.ɵɵpureFunction0(9, _c1))(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n      }\n    },\n    dependencies: [i3.NgIf, i4.PrimeTemplate, i5.Dialog, i3.NgOptimizedImage, i1.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.cancel-all-order[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .p-dialog[_ngcontent-%COMP%] {\\n  margin-top: 100px;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .p-dialog-content[_ngcontent-%COMP%] {\\n  border: 8px;\\n  border-radius: 8px 8px 8px 8px;\\n  padding-top: 24px;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-order-heading[_ngcontent-%COMP%] {\\n  color: #204E6E;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 18px;\\n  font-weight: 700;\\n  line-height: normal;\\n  text-transform: capitalize;\\n  text-align: center;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-order-confirm-questions[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: \\\"main-bold\\\";\\n  font-size: 18px;\\n  font-weight: 700;\\n  line-height: normal;\\n  text-transform: none;\\n  text-align: center;\\n  margin: 1rem 0;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-proceed-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  background: unset;\\n  border: unset !important;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-proceed-btns[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #4A4A4A;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 14px;\\n  font-weight: 400;\\n  line-height: normal;\\n  text-transform: none;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-proceed-btns[_ngcontent-%COMP%]:disabled {\\n  background: #f5f5f5;\\n  color: #9c9b9b;\\n  cursor: not-allowed;\\n}\\n.cancel-all-order[_ngcontent-%COMP%]   .cancel-proceed-btns[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  height: 52px;\\n  width: 52px;\\n}\\n\\n.d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.justify-content-center[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\n\\n.my-4[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.m-end-3[_ngcontent-%COMP%] {\\n  margin-inline-end: 1rem !important;\\n}\\n\\n.p-dialog[_ngcontent-%COMP%] {\\n  width: 30vw;\\n}\\n\\n.cancel-order-heading[_ngcontent-%COMP%] {\\n  color: #204E6E;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 18px;\\n  font-weight: 700;\\n  line-height: normal;\\n  text-transform: capitalize;\\n}\\n\\n.cancel-proceed-btns[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #4A4A4A;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 14px;\\n  font-weight: 400;\\n  line-height: normal;\\n  text-transform: capitalize;\\n}\\n\\n.cancel-order-confirm-questions[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 14px;\\n  font-family: var(--medium-font);\\n  color: #000;\\n}\\n\\n  .p-dialog .p-dialog-content:last-of-type {\\n  border-bottom-right-radius: 8px;\\n  border-bottom-left-radius: 8px;\\n  border-top-right-radius: 8px;\\n  border-top-left-radius: 8px;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 60px;\\n  padding: 13px 24px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 8px;\\n  align-self: stretch;\\n  width: 100%;\\n  border-radius: var(--Border-Radius-borderRadius, 6px);\\n  border: 2px solid #004D9C;\\n}\\n.cancel-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  \\n\\n  \\n\\n  justify-content: center;\\n  align-items: center;\\n  \\n\\n  align-self: stretch;\\n  color: var(--colors-Main-Color, #204E6E);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 100%;\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n}\\n\\n.main-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--main_hover_bt_txtcolor) !important;\\n  background-color: rgba(29, 76, 105, 0.1098039216) !important;\\n  border: 1px solid var(--main_hover_bt_boarder_color) !important;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}