{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services\";\nexport let LoaderInterceptor = /*#__PURE__*/(() => {\n  class LoaderInterceptor {\n    loaderService;\n    noLoaders = 0;\n    exceptionURLs = ['Product/GetAllProductsByFeatureNew?showLoader=true', '/Product/CustomPage/GetSectionDetailsWithProducts', 'Banner/GetBannerById', 'paginated=true', 'showLoader=false'];\n    constructor(loaderService) {\n      this.loaderService = loaderService;\n    }\n    intercept(request, next) {\n      const hasURLMatch = this.exceptionURLs.some(substring => request.url.includes(substring));\n      if (!hasURLMatch) {\n        this.noLoaders++;\n        this.loaderService.show();\n      } else {\n        this.noLoaders--;\n        this.loaderService.hide();\n      }\n      return next.handle(request).pipe(finalize(() => {\n        this.noLoaders--;\n        if (this.noLoaders <= 0) {\n          this.noLoaders = 0;\n          this.loaderService.hide();\n        }\n      }));\n    }\n    static ɵfac = function LoaderInterceptor_Factory(t) {\n      return new (t || LoaderInterceptor)(i0.ɵɵinject(i1.LoaderService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoaderInterceptor,\n      factory: LoaderInterceptor.ɵfac\n    });\n  }\n  return LoaderInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}