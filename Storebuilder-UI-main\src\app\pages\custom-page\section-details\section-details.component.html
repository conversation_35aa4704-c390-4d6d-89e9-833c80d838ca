<main>
  <section class="category-products-page">
    <div class="breadcrumb m-0"
      [ngClass]="{'breadcrumb':navbarData?.isActive,'hiddenNavbarBreadcrum':!navbarData?.isActive}">
      <p-breadcrumb [home]="home" [model]="breadItems"></p-breadcrumb>
    </div>

    <div>
      <div class="col-12 col-md-6 flex">
        <div class="font-size-22 bold-font">
          {{ sectionName }}
        </div>
      </div>

      <div *ngIf="products?.length" class="each-product">
        <div *ngFor="let product of products" class="product-card">
          <app-mtn-product-card [product]="product"></app-mtn-product-card>
        </div>
      </div>

      <div appLazyLoad (scrolledToEnd)="onScrolledToEnd()" class="loading-indicator">
        <div *ngIf="isLoading" class="spinner-product">
          <p-progressSpinner></p-progressSpinner>
        </div>
      </div>
    </div>
  </section>
</main>