{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { GuidGenerator } from '@core/services';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"@core/services/custom-GA.service\";\nimport * as i7 from \"ngx-google-analytics\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../core/directives/ga-impression.directive\";\nimport * as i10 from \"@shared/modals/success-info-modal/success-info-modal.component\";\nimport * as i11 from \"@shared/modals/notify-modal/notify-modal.component\";\nimport * as i12 from \"@shared/modals/age-consent-modal/age-consent-modal.component\";\nimport * as i13 from \"@shared/modals/ineligable-purchase-modal/ineligable-purchase-modal.component\";\nfunction ProductCardComponent_ng_container_0_div_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\")(2, \"img\", 27);\n    i0.ɵɵlistener(\"error\", function ProductCardComponent_ng_container_0_div_1_ng_container_3_ng_container_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r16.errorHandler($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const badge_r15 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r14.getProductImages(badge_r15.desktopImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_container_0_div_1_ng_container_3_ng_container_1_Template, 3, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.product == null ? null : ctx_r5.product.badgesList);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 28);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_10_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_10_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1, \"\\u2212\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_div_10_Template_div_click_0_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(1, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_div_10_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.quantityInCart === 1 ? ctx_r21.removeFromCart($event) : ctx_r21.addItemToCart(\"addCart\", $event, \"minus\"));\n    });\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_container_0_div_1_div_10_img_2_Template, 1, 0, \"img\", 32);\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_container_0_div_1_div_10_span_3_Template, 2, 0, \"span\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_div_10_Template_button_click_7_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.addItemToCart(\"addCart\", $event));\n    });\n    i0.ɵɵelement(8, \"img\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mtn-product-card__counter-btn--delete\", ctx_r8.quantityInCart === 1)(\"mtn-product-card__counter-btn--minus\", ctx_r8.quantityInCart > 1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r8.quantityInCart === 1 ? \"Remove from cart\" : \"Decrease quantity\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.quantityInCart === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.quantityInCart > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 9, ctx_r8.displayQuantity, \"2.0-0\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add one more\");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_button_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.quantityInCart);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_button_11_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 44);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_button_11_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 45);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_button_11_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r27.product.soldOut && ctx_r27.handleCartClick($event));\n    });\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_container_0_div_1_button_11_span_1_Template, 2, 1, \"span\", 40);\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_container_0_div_1_button_11_img_2_Template, 1, 0, \"img\", 41);\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_container_0_div_1_button_11_img_3_Template, 1, 0, \"img\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"mtn-product-card__cart-btn-floating--active\", ctx_r9.quantityInCart > 0 && !ctx_r9.showDesktopCounter)(\"mtn-product-card__cart-btn-floating--disabled\", ctx_r9.product.soldOut);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.product.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.quantityInCart > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.quantityInCart === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.quantityInCart > 0);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"img\", 47);\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 2, ctx_r10.product.rate, \"1.1-1\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r10.product.count, \")\");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.product.currencyCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.disableCent === \"false\" ? i0.ɵɵpipeBind2(4, 2, ctx_r11.product.priceValue, \"1.0-\" + ctx_r11.decimalValue) : i0.ɵɵpipeBind1(5, 5, ctx_r11.product.priceValue), \" \");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 50)(2, \"span\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r12.product.currencyCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.disableCent === \"false\" ? i0.ɵɵpipeBind2(5, 2, ctx_r12.product.salePriceValue, \"1.0-\" + ctx_r12.decimalValue) : i0.ɵɵpipeBind1(6, 5, ctx_r12.product.salePriceValue), \" \");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.disableCent === \"false\" ? i0.ɵɵpipeBind2(3, 3, ctx_r29.product.priceValue, \"1.0-\" + ctx_r29.decimalValue) : i0.ɵɵpipeBind1(4, 6, ctx_r29.product.priceValue), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r29.product.salePercent == null ? null : ctx_r29.product.salePercent.toFixed(0), \"% \", i0.ɵɵpipeBind1(7, 8, \"productDetails.details.off\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_19_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.leftInStockMultiple\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_19_div_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"productDetails.details.leftInStock\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_container_0_div_1_div_19_div_2_ng_container_3_Template, 3, 3, \"ng-container\", 11);\n    i0.ɵɵtemplate(4, ProductCardComponent_ng_container_0_div_1_div_19_div_2_ng_container_4_Template, 3, 3, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 4, \"productDetails.details.only\"), \" \", ctx_r30.product.quantity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.product.quantity > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.product.quantity === 1);\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_container_0_div_1_div_19_div_1_Template, 8, 10, \"div\", 53);\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_container_0_div_1_div_19_div_2_Template, 5, 6, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.product.salePriceValue);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.product.stockStatus === \"Low Stock\");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.productDetails(ctx_r33.product.productId));\n    });\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_container_0_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_container_0_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      ctx_r35.addToWishlist($event, ctx_r35.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(5, ProductCardComponent_ng_container_0_div_1_img_5_Template, 1, 0, \"img\", 13);\n    i0.ɵɵtemplate(6, ProductCardComponent_ng_container_0_div_1_img_6_Template, 1, 0, \"img\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"img\", 16);\n    i0.ɵɵlistener(\"error\", function ProductCardComponent_ng_container_0_div_1_Template_img_error_8_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵtemplate(10, ProductCardComponent_ng_container_0_div_1_div_10_Template, 9, 12, \"div\", 18);\n    i0.ɵɵtemplate(11, ProductCardComponent_ng_container_0_div_1_button_11_Template, 4, 8, \"button\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, ProductCardComponent_ng_container_0_div_1_div_12_Template, 7, 5, \"div\", 20);\n    i0.ɵɵelementStart(13, \"div\", 21)(14, \"div\", 22);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 23);\n    i0.ɵɵtemplate(17, ProductCardComponent_ng_container_0_div_1_div_17_Template, 6, 7, \"div\", 24);\n    i0.ɵɵtemplate(18, ProductCardComponent_ng_container_0_div_1_ng_container_18_Template, 7, 7, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProductCardComponent_ng_container_0_div_1_div_19_Template, 3, 2, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"mtn-product-card--has-interaction\", ctx_r3.showDesktopCounter || ctx_r3.quantityInCart > 0);\n    i0.ɵɵproperty(\"product\", ctx_r3.product);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.product == null ? null : ctx_r3.product.badgesList == null ? null : ctx_r3.product.badgesList.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mtn-product-card__wishlist-btn--active\", (ctx_r3.product == null ? null : ctx_r3.product.isLiked) || ctx_r3.isLiked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r3.product == null ? null : ctx_r3.product.isLiked) && !ctx_r3.isLiked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.product == null ? null : ctx_r3.product.isLiked) || ctx_r3.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"ngSrc\", ctx_r3.getProductImages(ctx_r3.product.masterImageUrl ? ctx_r3.product.masterImageUrl : (ctx_r3.product.thumbnailImages == null ? null : ctx_r3.product.thumbnailImages.length) ? ctx_r3.product.thumbnailImages[0] : null));\n    i0.ɵɵproperty(\"fill\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mtn-product-card__cart-overlay--disabled\", ctx_r3.product.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showDesktopCounter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.showDesktopCounter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.product.rate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.product.productName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.product.salePriceValue);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.product.salePriceValue);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.product.salePriceValue || ctx_r3.product.stockStatus === \"Low Stock\");\n  }\n}\nfunction ProductCardComponent_ng_container_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"product\", ctx_r4.product);\n  }\n}\nfunction ProductCardComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_container_0_div_1_Template, 20, 19, \"div\", 6);\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_container_0_div_2_Template, 1, 1, \"div\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLayoutTemplate);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 76)(2, \"img\", 77);\n    i0.ɵɵlistener(\"error\", function ProductCardComponent_ng_template_1_div_0_ng_container_3_ng_container_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r49.errorHandler($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const badge_r48 = ctx.$implicit;\n    const ctx_r47 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r47.getProductImages(badge_r48.mobileImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_template_1_div_0_ng_container_3_ng_container_1_Template, 3, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r38.product.badgesList);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 78);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 79);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_11_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 82);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_11_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 83);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 84);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r52.quantityInCart);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"already-added-cart\": a0,\n    \"mtn-product-card-mobile__cart-button--disabled\": a1\n  };\n};\nfunction ProductCardComponent_ng_template_1_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_div_11_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r53.product.soldOut && ctx_r53.mainAddToCartButton(\"addCart\", $event));\n    });\n    i0.ɵɵtemplate(1, ProductCardComponent_ng_template_1_div_0_div_11_img_1_Template, 1, 0, \"img\", 81);\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_template_1_div_0_div_11_ng_container_2_Template, 4, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, ctx_r41.quantityInCart > 0, ctx_r41.product.soldOut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.quantityInCart === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.quantityInCart > 0);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_12_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_12_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \"\\u2212\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_div_12_Template_div_click_0_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_div_12_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r58.quantityInCart === 1 ? ctx_r58.removeFromCart($event) : ctx_r58.addItemToCart(\"addCart\", $event, \"minus\"));\n    });\n    i0.ɵɵtemplate(2, ProductCardComponent_ng_template_1_div_0_div_12_img_2_Template, 1, 0, \"img\", 32);\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_template_1_div_0_div_12_span_3_Template, 2, 0, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_div_12_Template_button_click_7_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r60.addItemToCart(\"addCart\", $event));\n    });\n    i0.ɵɵelement(8, \"img\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mtn-product-card-mobile__cart-clicked-button__btn--delete\", ctx_r42.quantityInCart === 1)(\"mtn-product-card-mobile__cart-clicked-button__btn--minus\", ctx_r42.quantityInCart > 1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r42.quantityInCart === 1 ? \"Remove from cart\" : \"Decrease quantity\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.quantityInCart === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r42.quantityInCart > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 9, ctx_r42.displayQuantity, \"2.0-0\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add one more\");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(2, 3, \"productDetails.details.only\"), \" \", ctx_r43.product.quantity, \" \", i0.ɵɵpipeBind1(3, 5, \"productDetails.details.leftInStock\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_container_17_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r61.product.quantity, \" \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.itemsLeft\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_container_17_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 96);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 92)(2, \"span\", 93);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 94);\n    i0.ɵɵtemplate(8, ProductCardComponent_ng_template_1_div_0_ng_container_17_div_8_Template, 3, 4, \"div\", 54);\n    i0.ɵɵtemplate(9, ProductCardComponent_ng_template_1_div_0_ng_container_17_div_9_Template, 1, 0, \"div\", 95);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.product.currencyCode, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.disableCent === \"false\" ? i0.ɵɵpipeBind2(5, 4, ctx_r44.product.priceValue, \"1.0-\" + ctx_r44.decimalValue) : i0.ɵɵpipeBind1(6, 7, ctx_r44.product.priceValue), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r44.product.stockStatus === \"Low Stock\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r44.product.stockStatus !== \"Low Stock\");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_template_18_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(2, 3, \"productDetails.details.only\"), \" \", ctx_r63.product.quantity, \" \", i0.ɵɵpipeBind1(3, 5, \"productDetails.details.leftInStock\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_template_18_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r64.product.quantity, \" \", i0.ɵɵpipeBind1(2, 2, \"productDetails.details.itemsLeft\"), \" \");\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_template_18_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 96);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_ng_template_18_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(2, 3, \"productDetails.details.only\"), \" \", ctx_r66.product.quantity, \" \", i0.ɵɵpipeBind1(3, 5, \"productDetails.details.leftInStock\"), \" \");\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"sale-price\": a0\n  };\n};\nfunction ProductCardComponent_ng_template_1_div_0_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 92)(2, \"div\", 93);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ProductCardComponent_ng_template_1_div_0_ng_template_18_div_8_Template, 4, 7, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 99)(10, \"div\", 100)(11, \"div\", 101);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ProductCardComponent_ng_template_1_div_0_ng_template_18_div_18_Template, 3, 4, \"div\", 54);\n    i0.ɵɵtemplate(19, ProductCardComponent_ng_template_1_div_0_ng_template_18_div_19_Template, 1, 0, \"div\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, ProductCardComponent_ng_template_1_div_0_ng_template_18_div_20_Template, 4, 7, \"div\", 98);\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.product.currencyCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.disableCent === \"false\" ? i0.ɵɵpipeBind2(6, 10, ctx_r46.product.salePriceValue, \"1.0-\" + ctx_r46.decimalValue) : i0.ɵɵpipeBind1(7, 13, ctx_r46.product.salePriceValue), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.product.proSchedulingId && ctx_r46.product.stockPerSKU && ctx_r46.product.quantity < ctx_r46.product.stockPerSKU && ctx_r46.screenWidth > 767);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c1, ctx_r46.isMobileTemplate));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.disableCent === \"false\" ? i0.ɵɵpipeBind2(13, 15, ctx_r46.product.priceValue, \"1.0-\" + ctx_r46.decimalValue) : i0.ɵɵpipeBind1(14, 18, ctx_r46.product.priceValue), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r46.product.salePercent == null ? null : ctx_r46.product.salePercent.toFixed(0), \"% \", i0.ɵɵpipeBind1(17, 20, \"productDetails.details.off\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.product.stockStatus === \"Low Stock\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.product.stockStatus !== \"Low Stock\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.product.proSchedulingId && ctx_r46.product.stockPerSKU && ctx_r46.product.quantity < ctx_r46.product.stockPerSKU && ctx_r46.screenWidth <= 767);\n  }\n}\nfunction ProductCardComponent_ng_template_1_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.productDetails(ctx_r67.product.productId));\n    });\n    i0.ɵɵelementStart(1, \"div\", 62)(2, \"div\", 63);\n    i0.ɵɵtemplate(3, ProductCardComponent_ng_template_1_div_0_ng_container_3_Template, 2, 1, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 64)(5, \"div\", 65);\n    i0.ɵɵlistener(\"click\", function ProductCardComponent_ng_template_1_div_0_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      ctx_r69.addToWishlist($event, ctx_r69.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(6, ProductCardComponent_ng_template_1_div_0_img_6_Template, 1, 0, \"img\", 66);\n    i0.ɵɵtemplate(7, ProductCardComponent_ng_template_1_div_0_img_7_Template, 1, 0, \"img\", 67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"img\", 69);\n    i0.ɵɵlistener(\"error\", function ProductCardComponent_ng_template_1_div_0_Template_img_error_9_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(10);\n    i0.ɵɵtemplate(11, ProductCardComponent_ng_template_1_div_0_div_11_Template, 3, 6, \"div\", 70);\n    i0.ɵɵtemplate(12, ProductCardComponent_ng_template_1_div_0_div_12_Template, 9, 12, \"div\", 71);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(13, ProductCardComponent_ng_template_1_div_0_div_13_Template, 4, 7, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 73)(15, \"h2\", 74);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ProductCardComponent_ng_template_1_div_0_ng_container_17_Template, 10, 9, \"ng-container\", 0);\n    i0.ɵɵtemplate(18, ProductCardComponent_ng_template_1_div_0_ng_template_18_Template, 21, 24, \"ng-template\", null, 75, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r45 = i0.ɵɵreference(19);\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"product\", ctx_r37.product);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.product == null ? null : ctx_r37.product.badgesList);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"mtn-product-card-mobile__right-top--active\", (ctx_r37.product == null ? null : ctx_r37.product.isLiked) || ctx_r37.isLiked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r37.product == null ? null : ctx_r37.product.isLiked) && !ctx_r37.isLiked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r37.product == null ? null : ctx_r37.product.isLiked) || ctx_r37.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"ngSrc\", ctx_r37.getProductImages(ctx_r37.product.masterImageUrl ? ctx_r37.product.masterImageUrl : (ctx_r37.product.thumbnailImages == null ? null : ctx_r37.product.thumbnailImages.length) ? ctx_r37.product.thumbnailImages[0] : null));\n    i0.ɵɵproperty(\"fill\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r37.showMobileCartClicked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.showMobileCartClicked && !ctx_r37.product.soldOut);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.product.stockStatus === \"Low Stock\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.product.productName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r37.product.salePriceValue)(\"ngIfElse\", _r45);\n  }\n}\nfunction ProductCardComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProductCardComponent_ng_template_1_div_0_Template, 20, 14, \"div\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLayoutTemplate);\n  }\n}\nexport class ProductCardComponent {\n  router;\n  platformId;\n  reviewsService;\n  store;\n  cartService;\n  userService;\n  messageService;\n  translate;\n  authTokenService;\n  cookieService;\n  detailsService;\n  productService;\n  permissionService;\n  mainDataService;\n  cd;\n  _GACustomEvents;\n  $gaService;\n  baseUrl;\n  proRate;\n  proCount;\n  isDragging = false;\n  product = {};\n  currency = {};\n  liveStreamCardFlag = false;\n  categoryName = '';\n  rating = 5;\n  scConfig = false;\n  decimalValue = 0;\n  categoryId;\n  category;\n  currencyCode = '';\n  disableCent;\n  cartId;\n  authToken;\n  selectedVariant;\n  isLiked = false;\n  displayNotifyModal = false;\n  displaySuccessModal = false;\n  successTitleMessage = '';\n  successBodyMessage = '';\n  profile;\n  isShowNotifyFeature = false;\n  isLayoutTemplate = false;\n  screenWidth = window.innerWidth;\n  isMobileView = this.screenWidth <= 786;\n  displayAgeConsentModal = false;\n  displayEligableModal = false;\n  restrictionAge;\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n    this.screenWidth = event.target.innerWidth;\n    if (this.screenWidth <= 768) {\n      this.isMobileView = true;\n    } else {\n      this.isMobileView = false;\n    }\n  }\n  isEmailExist = false;\n  topBadge = {\n    name: '',\n    className: '',\n    translatedName: '',\n    classNameMobile: ''\n  };\n  bottomBadge = {\n    name: '',\n    className: '',\n    translatedName: ''\n  };\n  tagName = GaLocalActionEnum;\n  userDetails;\n  isGoogleAnalytics = false;\n  // Mobile cart properties\n  showMobileCartClicked = false;\n  // Desktop cart properties\n  showDesktopCounter = false;\n  counterTimeout;\n  // Shared property\n  quantityInCart = 0;\n  displayQuantity = 0; // For immediate UI feedback while API is processing\n  isMobileTemplate = false;\n  type;\n  constructor(router, platformId, reviewsService, store, cartService, userService, messageService, translate, authTokenService, cookieService, detailsService, productService, permissionService, mainDataService, cd, _GACustomEvents, $gaService) {\n    this.router = router;\n    this.platformId = platformId;\n    this.reviewsService = reviewsService;\n    this.store = store;\n    this.cartService = cartService;\n    this.userService = userService;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.detailsService = detailsService;\n    this.productService = productService;\n    this.permissionService = permissionService;\n    this.mainDataService = mainDataService;\n    this.cd = cd;\n    this._GACustomEvents = _GACustomEvents;\n    this.$gaService = $gaService;\n    this.scConfig = environment.isStoreCloud;\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.disableCent = localStorage.getItem('DisableCents');\n    this.baseUrl = environment.apiEndPoint + \"/\";\n    let value = localStorage.getItem('CurrencyDecimal');\n    let parsedValue = value?.replace(/\"/g, '');\n    if (parsedValue) {\n      let number = parseInt(parsedValue);\n      if (!Number.isNaN(number)) this.decimalValue = number;\n    }\n  }\n  ngOnInit() {\n    this.profile = localStorage.getItem('profile');\n    this.userDetails = this.store.get('profile');\n    this.isShowNotifyFeature = this.permissionService.hasPermission('Notify-Me');\n    this.getCartId();\n    let currency = localStorage.getItem('Currency')?.toString();\n    if (currency) this.currencyCode = currency;\n    if (this.product) {\n      this.isLiked = this.product.isLiked;\n      this.assignCartQuantity();\n      if (this.product.proSchedulingId) {\n        this.topBadge = {\n          name: 'promo',\n          translatedName: 'Promo',\n          className: 'mtn-product-card__grey-label',\n          classNameMobile: 'mtn-product-card-mobile__grey-label'\n        };\n      } else {\n        // assign the top badge\n        // verify if product is in stock or not\n        if (this.product.soldOut) {\n          this.topBadge = {\n            name: 'sold-out',\n            translatedName: 'productDetails.details.outOfStock',\n            className: 'mtn-product-card__grey-label',\n            classNameMobile: 'mtn-product-card-mobile__grey-label'\n          };\n        } else {\n          // top badge for sale\n          if (this.product.salePercent) {\n            this.topBadge = {\n              name: 'sale',\n              translatedName: 'productDetails.details.sale',\n              className: 'mtn-product-card__green-label',\n              classNameMobile: 'mtn-product-card-mobile__green-label'\n            };\n          }\n          // top badge for hot deals\n          if (!this.isBadgeExists(['sold-out', 'sale'], 'top') && this.product.isHot) {\n            this.topBadge = {\n              name: 'hot-deal',\n              translatedName: 'productDetails.details.hot',\n              className: 'mtn-product-card__red-label',\n              classNameMobile: 'mtn-product-card-mobile__red-label'\n            };\n          }\n          // top badge for best seller\n          else if (!this.isBadgeExists(['sold-out', 'sale', 'hot-deal'], 'top') && this.product.isBest) {\n            this.topBadge = {\n              name: 'best-seller',\n              translatedName: 'productDetails.details.bestSeller',\n              className: 'mtn-product-card__blue-label',\n              classNameMobile: 'mtn-product-card-mobile__blue-label'\n            };\n          }\n          // top badge for new arrival\n          else if (!this.isBadgeExists(['sold-out', 'sale', 'hot-deal', 'best-seller'], 'top') && this.product.isNew) {\n            this.topBadge = {\n              name: 'new-arrival',\n              translatedName: 'productDetails.details.newArrivals',\n              className: 'mtn-product-card__light-blue-label',\n              classNameMobile: 'mtn-product-card-mobile__light-blue-label'\n            };\n          }\n        }\n        // assign bottom badge\n        // bottom badge for hot deals\n        if (!this.isBadgeExists(['hot-deal'], 'top') && !this.isBadgeExists(['hot-deal'], 'bottom') && this.product.isHot) {\n          this.bottomBadge = {\n            name: 'hot-deal',\n            translatedName: 'productDetails.details.hot',\n            className: 'mtn-product-card__red-label'\n          };\n        }\n        // bottom badge for best seller\n        else if (!this.isBadgeExists(['best-seller'], 'top') && !this.isBadgeExists(['best-seller'], 'bottom') && this.product.isBest) {\n          this.bottomBadge = {\n            name: 'best-seller',\n            translatedName: 'productDetails.details.bestSeller',\n            className: 'mtn-product-card__blue-label'\n          };\n        }\n        // bottom badge for new arrival\n        else if (!this.isBadgeExists(['new-arrival'], 'top') && !this.isBadgeExists(['new-arrival'], 'bottom') && this.product.isNew) {\n          this.bottomBadge = {\n            name: 'new-arrival',\n            translatedName: 'productDetails.details.newArrivals',\n            className: 'mtn-product-card__light-blue-label'\n          };\n        }\n      }\n    }\n  }\n  ngOnDestroy() {\n    // Clear the counter timeout on component destroy\n    if (this.counterTimeout) {\n      clearTimeout(this.counterTimeout);\n    }\n  }\n  ngOnChanges() {\n    if (this.categoryName) {\n      this.product['categoryName'] = this.categoryName;\n    }\n  }\n  assignCartQuantity() {\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (res && res.length > 0) {\n        const productFound = res.find(cartProduct => cartProduct.specsProductId === this.product.specProductId);\n        if (productFound) {\n          this.quantityInCart = productFound.quantity;\n          this.displayQuantity = productFound.quantity; // Initialize display quantity\n          this.cd.detectChanges();\n        }\n      }\n    });\n  }\n  isBadgeExists(presentBadges, badgePosition) {\n    if (badgePosition === 'top') {\n      return presentBadges.some(string => string === this.topBadge.name);\n    }\n  }\n  // Desktop cart interaction handlers\n  handleCartClick(event) {\n    event.stopPropagation();\n    if (this.quantityInCart === 0) {\n      // First click - add to cart\n      this.addItemToCart('addCart', event);\n    } else {\n      // Show counter interface - timeout will start after next API interaction\n      this.showDesktopCounter = true;\n      // Clear any existing timeout but don't start new one until user interacts\n      if (this.counterTimeout) {\n        clearTimeout(this.counterTimeout);\n        this.counterTimeout = null;\n      }\n    }\n  }\n  removeFromCart(event) {\n    event.stopPropagation();\n    // Clear all quantity\n    this.quantityInCart = 0;\n    this.displayQuantity = 0;\n    this.showDesktopCounter = false;\n    if (this.isMobileView) {\n      this.showMobileCartClicked = false;\n    }\n    if (this.counterTimeout) {\n      clearTimeout(this.counterTimeout);\n    }\n    // Call the existing logic to remove from cart\n    let isTrigger = true;\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger && res && res.length > 0) {\n        let cartProduct = res.find(x => x.specsProductId === this.product.specProductId);\n        if (cartProduct) {\n          cartProduct.quantity = 0;\n          isTrigger = false;\n          this.updateCart(cartProduct, 'addCart', 'delete');\n        }\n      }\n    });\n  }\n  resetCounterTimeout() {\n    if (this.counterTimeout) {\n      clearTimeout(this.counterTimeout);\n    }\n    // Hide counter after 3 seconds of inactivity\n    this.counterTimeout = setTimeout(() => {\n      this.showDesktopCounter = false;\n      this.cd.detectChanges();\n    }, 3000);\n  }\n  productDetails(productId) {\n    const itemId = this.product.specsProductId || this.product.id?.toString() || 'unknown';\n    const itemName = this.product.productName || this.product.name || 'Unknown Product';\n    const searchTerm = this.getCurrentSearchTerm();\n    this._GACustomEvents.selectItemEvent(itemId, itemName, searchTerm);\n    if (!this.isDragging && !productId) {\n      this.router.navigate(['product', this.product.specsProductId, this.product.channelId], {\n        queryParams: {\n          tenantId: localStorage.getItem(\"tenantId\"),\n          lang: localStorage.getItem(\"lang\")\n        },\n        queryParamsHandling: 'merge'\n      });\n    } else if (!this.isDragging && productId) {\n      if (this.liveStreamCardFlag == false) {\n        this.router.navigate(['product', productId, this.product.channelId], {\n          queryParams: {\n            tenantId: localStorage.getItem(\"tenantId\"),\n            lang: localStorage.getItem(\"lang\")\n          },\n          queryParamsHandling: 'merge'\n        });\n      } else {\n        const baseUrl = this.router.createUrlTree(['product', productId, this.product.channelId], {\n          queryParams: {\n            tenantId: localStorage.getItem('tenantId'),\n            lang: localStorage.getItem('lang')\n          }\n        }).toString();\n        const externalUrl = `${window.location.origin}/storecloud${baseUrl}`;\n        window.open(externalUrl, '_blank');\n      }\n    }\n  }\n  getProductImages(url) {\n    if (url == \"\") {\n      url == this.product.masterImageUrl ? this.product.masterImageUrl : this.product?.thumbnailImages?.length ? this.product?.thumbnailImages[0] : null;\n    }\n    if (this.product.channelId == 2) return url;\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  errorHandler(event) {\n    if (environment.isStoreCloud) {\n      event.target.src = \"assets/images/placeholder.png\";\n    } else {\n      event.target.src = \"assets/images/mtn-alt.png\";\n    }\n  }\n  mainAddToCartButton(type, event, action = 'add') {\n    event.stopPropagation();\n    if (this.quantityInCart > 0) {\n      this.showMobileCartClicked = true;\n      setTimeout(() => {\n        this.showMobileCartClicked = false;\n      }, 5000);\n    } else {\n      this.addItemToCart(type, event, action);\n    }\n  }\n  addItemToCart(type, event, action = 'add') {\n    event.stopPropagation();\n    if (this.product.soldOut) {\n      return;\n    }\n    // Show counter immediately and clear any existing timeout - wait for API response to start new timeout\n    if (!this.isMobileView) {\n      this.showDesktopCounter = true;\n      // Optimistic UI: compute next quantity and reflect immediately (so delete icon shows at 1)\n      const nextQuantity = action === 'add' ? Math.max(1, this.quantityInCart + 1) : Math.max(0, this.quantityInCart - 1);\n      this.displayQuantity = nextQuantity;\n      this.quantityInCart = nextQuantity;\n      if (this.counterTimeout) {\n        clearTimeout(this.counterTimeout);\n        this.counterTimeout = null;\n      }\n    } else {\n      // Mobile: Optimistic UI so delete icon/state shows immediately on first add\n      if (action === 'add') {\n        this.quantityInCart = Math.max(1, this.quantityInCart + 1);\n        this.displayQuantity = this.quantityInCart;\n        this.showMobileCartClicked = true;\n      } else {\n        this.quantityInCart = Math.max(0, this.quantityInCart - 1);\n        this.displayQuantity = this.quantityInCart;\n        if (this.quantityInCart === 0) {\n          this.showMobileCartClicked = false;\n        }\n      }\n      this.cd.detectChanges();\n    }\n    let isTrigger = true;\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      if (isTrigger) {\n        if (res && res.length > 0) {\n          let cartProduct = res.find(x => x.specsProductId === this.product.specProductId);\n          if (cartProduct) {\n            if (this.product.itemPerCustomer && cartProduct.quantity + 1 > this.product.itemPerCustomer) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ErrorMessages.itemPerCustomerError') + this.product.itemPerCustomer + this.translate.instant('ErrorMessages.itemPerCustomerErrorNext')\n              });\n              isTrigger = false;\n              return;\n            } else {\n              const originalQuantity = cartProduct.quantity;\n              // handling quantity for update Cart\n              if (action === 'add') {\n                if (this.product.proSchedulingId) {\n                  let promotionCartProduct = res.find(x => x.proSchedulingId === this.product.proSchedulingId);\n                  if (promotionCartProduct) {\n                    cartProduct.quantity = promotionCartProduct.quantity + 1;\n                  } else {\n                    cartProduct.quantity = 1;\n                  }\n                } else {\n                  cartProduct.quantity += 1;\n                }\n                // Sync displayQuantity for mobile when quantity is updated from API response\n                if (this.isMobileView) {\n                  this.displayQuantity = cartProduct.quantity;\n                }\n              } else {\n                cartProduct.quantity -= 1;\n                this.quantityInCart = cartProduct.quantity;\n                this.displayQuantity = cartProduct.quantity;\n                if (this.quantityInCart == 0) this.showMobileCartClicked = false;\n                if (this.quantityInCart < 0) {\n                  this.quantityInCart = 0;\n                  this.displayQuantity = 0;\n                  cartProduct.quantity = 0;\n                }\n              }\n            }\n            // update cart\n            isTrigger = false;\n            this.updateCart(cartProduct, type, action);\n          } else {\n            isTrigger = false;\n            this.addToCart(type, action);\n          }\n        } else {\n          isTrigger = false;\n          // create cart\n          this.addToCart(type, action);\n        }\n      }\n    });\n  }\n  addToCart(type, action) {\n    const sessionId = this.getSessionId();\n    const reqObj = {\n      productId: this.product.productId,\n      quantity: 1,\n      sessionId: sessionId,\n      shopId: this.product.shopId,\n      specsProductId: this.product.specProductId,\n      priceId: this.product.priceId ?? 0,\n      channelId: this.product.channelId,\n      proSchedulingId: this.product.proSchedulingId\n    };\n    this.type = type;\n    this.cartService.addToCart(reqObj).subscribe({\n      next: res => {\n        if (!res.data?.userFailedProductEligibility) {\n          this.updateCartInfo(res, type, this.product, sessionId, action);\n        } else {\n          this.restrictionAge = res.data.productAgeRestriction;\n          this.displayAgeConsentModal = true;\n        }\n      },\n      error: err => {\n        this.handleError(err.message);\n        this.resetCartUIState();\n      }\n    });\n  }\n  updateCart(cartProduct, type, action) {\n    const sessionId = this.getSessionId();\n    this.type = type;\n    cartProduct.proSchedulingId = this.product.proSchedulingId;\n    // Defensive: ensure nested object exists before writing\n    if (!cartProduct.specProductDetails) {\n      cartProduct.specProductDetails = {};\n    }\n    cartProduct.specProductDetails.proSchedulingId = this.product.proSchedulingId;\n    this.cartService.updateCart(cartProduct).subscribe({\n      next: res => {\n        if (!res.data?.userFailedProductEligibility) {\n          this.updateCartInfo(res, type, cartProduct, sessionId, action);\n        } else {\n          this.restrictionAge = res.data.productAgeRestriction;\n          this.displayAgeConsentModal = true;\n        }\n      },\n      error: err => {\n        this.handleError(err.message);\n        // Reset UI state on HTTP error as well\n        this.resetCartUIState();\n      }\n    });\n  }\n  updateCartInfo(res, type, cartProduct, sessionId, action) {\n    if (res?.success) {\n      if (res.data.cartItems.length > 0) {\n        localStorage.setItem(\"cartId\", res.data.cartItems[0].cartId);\n      }\n      if (type === 'addCart') {\n        if (this.isGoogleAnalytics) {\n          this._GACustomEvents.addToCartEvent(cartProduct, this.product);\n        }\n        this.handleAddToCart(type, sessionId);\n      }\n      if (type === 'buyNow') {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(this.tagName.CLICK_ON_BUY_NOW, 'product', 'BUY_NOW', 1, true, {\n            \"product_ID\": cartProduct.productId,\n            \"product_name\": cartProduct.productName,\n            \"product_SKU\": cartProduct?.specProductDetails?.skuAutoGenerated,\n            \"seller_name\": cartProduct?.sellerName,\n            \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n            \"session_ID\": sessionId,\n            \"shop_ID\": cartProduct.shopId,\n            \"category_name\": this.categoryName,\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n            \"product_tags\": this.product?.isBest ? 'Best Seller' : this.product?.isNew ? 'New Arrival' : this.product?.isHot ? 'Hot Deals' : 'None',\n            \"promotion\": cartProduct?.promotionName ? cartProduct?.promotionName : 'None',\n            \"section_name\": this.product?.sectionName ? this.product?.sectionName : null\n          });\n        }\n        this.router.navigate(['/cart']);\n      }\n      this.handleSuccess(action);\n    } else {\n      // Backend returned success: false - don't update quantity, show error\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ResponseMessages.cart'),\n        detail: res.message\n      });\n      this.resetCartUIState();\n    }\n  }\n  getSessionId() {\n    let sessionId = localStorage.getItem('sessionId') ?? '';\n    if (!sessionId) {\n      sessionId = GuidGenerator.newGuid();\n      localStorage.setItem('sessionId', sessionId);\n    }\n    return sessionId;\n  }\n  handleAddToCart(type, sessionId) {\n    const cartData = {\n      sessionId\n    };\n    const applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo !== '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getAllCart(cartData).subscribe({\n      next: data => {\n        const selectedProduct = data.find(product => product.specsProductId == this.product.specProductId);\n        if (selectedProduct) {\n          this.quantityInCart = selectedProduct.quantity;\n          this.displayQuantity = selectedProduct.quantity; // Sync display with actual quantity\n        }\n\n        if (selectedProduct && this.quantityInCart > 0) {\n          // Mobile behavior\n          if (this.isMobileView) {\n            this.showMobileCartClicked = true;\n            setTimeout(() => {\n              this.showMobileCartClicked = false;\n            }, 5000);\n          } else {\n            // Desktop behavior - show counter briefly\n            this.showDesktopCounter = true;\n            this.resetCounterTimeout();\n          }\n        }\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error fetching cart data', error);\n      }\n    });\n  }\n  handleError(message) {\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: message\n    });\n  }\n  handleSuccess(action = 'add') {\n    if (action === 'minus' || action === 'delete') {\n      this.messageService.add({\n        severity: 'success',\n        summary: this.translate.instant('ResponseMessages.cart'),\n        detail: this.translate.instant('ResponseMessages.successfullyRemovedFromCart')\n      });\n    } else {\n      this.messageService.add({\n        severity: 'success',\n        summary: this.translate.instant('ResponseMessages.cart'),\n        detail: this.translate.instant('ResponseMessages.successfullyAddedToCart')\n      });\n    }\n  }\n  resetCartUIState() {\n    // Reset mobile cart UI state\n    this.showMobileCartClicked = false;\n    // For desktop, start timeout to hide counter after error\n    if (!this.isMobileView && this.showDesktopCounter) {\n      this.resetCounterTimeout();\n    } else {\n      // Reset desktop counter UI state if not in desktop mode\n      this.showDesktopCounter = false;\n      // Clear any existing timeouts\n      if (this.counterTimeout) {\n        clearTimeout(this.counterTimeout);\n        this.counterTimeout = null;\n      }\n    }\n    // Refresh cart data to get the actual quantity from backend\n    const sessionId = this.getSessionId();\n    this.refreshCartQuantity(sessionId);\n  }\n  refreshCartQuantity(sessionId) {\n    const cartData = {\n      sessionId\n    };\n    const applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo !== '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getAllCart(cartData).subscribe({\n      next: data => {\n        const selectedProduct = data.find(product => product.specsProductId == this.product.specProductId);\n        if (selectedProduct) {\n          this.quantityInCart = selectedProduct.quantity;\n          this.displayQuantity = selectedProduct.quantity;\n        } else {\n          this.quantityInCart = 0;\n          this.displayQuantity = 0;\n        }\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error refreshing cart quantity', error);\n        this.quantityInCart = 0;\n        this.displayQuantity = 0;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  getCartId() {\n    this.store.subscription('cartProducts').subscribe({\n      next: res => {\n        if (res.length > 0) {\n          this.cartId = res[0].cartId;\n          this.currencyCode = res[0].currencyCode;\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  addToWishlist(event, product) {\n    this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n    if (!this.authToken) {\n      this.authToken = this.cookieService.get('authToken');\n    }\n    if (!this.authToken) {\n      this.router.navigate(['login'], {\n        queryParams: {\n          returnUrl: `/`\n        }\n      });\n      return;\n    }\n    let specsProductId = product.specProductId;\n    let flag;\n    if (this.isLiked) {\n      flag = true;\n    } else {\n      flag = product.isLiked;\n    }\n    const obj = {\n      specsProductId,\n      flag,\n      productId: product.productId,\n      channelId: this.product.channelId\n    };\n    this.detailsService.wishlistToggle(obj).subscribe({\n      next: res => {\n        if (res?.success) {\n          this.isLiked = !this.isLiked;\n          this.cd.detectChanges();\n          if (this.isLiked) {\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyAddedToWishList')\n            });\n          } else {\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.wishList'),\n              detail: this.translate.instant('ResponseMessages.successfullyRemovedToWishList')\n            });\n          }\n        }\n      },\n      error: err => {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  notifyMe() {\n    this.displayNotifyModal = true;\n    const profile = JSON.parse(localStorage.getItem('profile') ?? '');\n    if (profile?.email) {\n      this.isEmailExist = true;\n    } else {\n      this.isEmailExist = false;\n    }\n  }\n  onSubmitNotify(data) {\n    let reqObj = {};\n    reqObj['email'] = data.email ? data.email : '';\n    if (this.product.specProductId) {\n      reqObj['specProductId'] = this.product.specProductId;\n    }\n    if (data.phone) {\n      let dialCode = data.phone.dialCode.substring(1, 4);\n      reqObj['phoneNumber'] = dialCode + data.phone.number.replace(/\\s/g, '');\n    } else {\n      reqObj['phoneNumber'] = '';\n    }\n    this.productService.notifyMeProduct(reqObj).subscribe(res => {\n      if (res.success) {\n        this.successTitleMessage = this.translate.instant(\"notifyMeDetails.thanksForInterest\");\n        this.successBodyMessage = this.translate.instant(\"notifyMeDetails.notifyProductIsAvaialble\");\n        this.displaySuccessModal = true;\n        this.displayNotifyModal = false;\n      }\n    });\n  }\n  onCancel() {\n    this.displaySuccessModal = false;\n    this.displayNotifyModal = false;\n  }\n  getSalePricePercent(salePrice) {\n    return salePrice % 1 !== 0 ? salePrice.toFixed(2) : salePrice;\n  }\n  getSalePrice() {\n    return Number(this.product?.salePercent?.toFixed(0));\n  }\n  onSubmitConsent() {\n    this.displayAgeConsentModal = false;\n    const userProfile = localStorage.getItem(\"profile\") || '';\n    const userId = userProfile ? JSON.parse(userProfile)?.id : null;\n    let data = {\n      sessionId: localStorage.getItem('sessionId') || '',\n      MinimumAgeForProductEligibility: this.restrictionAge\n    };\n    userId ? data.userId = userId : '';\n    const sessionId = this.getSessionId();\n    const reqObj = {\n      productId: this.product.productId,\n      quantity: this.quantityInCart,\n      sessionId: sessionId,\n      shopId: this.product.shopId,\n      specsProductId: this.product.specProductId,\n      priceId: this.product.priceId ?? 0,\n      channelId: this.product.channelId\n    };\n    this.userService.updateAgeConsent(data).subscribe({\n      next: res => {\n        // On user consenting to age restriction add the product to cart\n        this.cartService.addToCart(reqObj).subscribe({\n          next: res => {\n            this.updateCartInfo(res, this.type, this.product, sessionId, 'add');\n          },\n          error: err => {\n            this.handleError(err.message);\n          }\n        });\n      },\n      error: err => {\n        this.handleError(err.message);\n      }\n    });\n  }\n  closeConsentModal() {\n    this.displayEligableModal = true;\n    this.displayAgeConsentModal = false;\n  }\n  closeEligableModal() {\n    this.displayEligableModal = false;\n  }\n  getCurrentSearchTerm() {\n    const urlParams = new URLSearchParams(window.location.search);\n    const searchTerm = urlParams.get('search') || urlParams.get('q') || urlParams.get('query');\n    if (searchTerm) {\n      return searchTerm;\n    }\n    return '';\n  }\n  static ɵfac = function ProductCardComponent_Factory(t) {\n    return new (t || ProductCardComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.ReviewsService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i2.AuthTokenService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i2.DetailsService), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.CustomGAService), i0.ɵɵdirectiveInject(i7.GoogleAnalyticsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProductCardComponent,\n    selectors: [[\"app-mtn-product-card\"]],\n    hostBindings: function ProductCardComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function ProductCardComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      isDragging: \"isDragging\",\n      product: \"product\",\n      currency: \"currency\",\n      liveStreamCardFlag: \"liveStreamCardFlag\",\n      categoryName: \"categoryName\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 7,\n    vars: 10,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"mobileView\", \"\"], [3, \"isEmailExist\", \"displayModal\", \"close\", \"submit\"], [3, \"titleMessage\", \"bodyMessage\", \"displayModal\", \"cancel\"], [3, \"age\", \"displayModal\", \"submit\", \"cancel\"], [3, \"displayModal\", \"cancel\"], [\"appGAImpression\", \"\", \"class\", \"mtn-product-card\", \"style\", \"width: inherit\", 3, \"product\", \"mtn-product-card--has-interaction\", \"click\", 4, \"ngIf\"], [\"class\", \"old-product-card\", \"appGAImpression\", \"\", 3, \"product\", 4, \"ngIf\"], [\"appGAImpression\", \"\", 1, \"mtn-product-card\", 2, \"width\", \"inherit\", 3, \"product\", \"click\"], [1, \"mtn-product-card__top-section\"], [1, \"mtn-product-card__badges\"], [4, \"ngIf\"], [1, \"mtn-product-card__wishlist-btn\", 3, \"click\"], [\"src\", \"assets/icons/heart-unselected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to wishlist\", 4, \"ngIf\"], [\"src\", \"assets/icons/heart-selected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Remove from wishlist\", 4, \"ngIf\"], [1, \"mtn-product-card__image-wrapper\"], [\"alt\", \"No Image\", 1, \"mtn-product-card__product-image\", 3, \"ngSrc\", \"fill\", \"error\"], [1, \"mtn-product-card__cart-overlay\"], [\"class\", \"mtn-product-card__counter-group\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__cart-btn-floating\", 3, \"mtn-product-card__cart-btn-floating--active\", \"mtn-product-card__cart-btn-floating--disabled\", \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__rating\", 4, \"ngIf\"], [1, \"mtn-product-card__product-details\"], [1, \"mtn-product-card__product-details__title\"], [1, \"mtn-product-card__price-section\"], [\"class\", \"mtn-product-card__product-details__price\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__bottom-row\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"alt\", \"No Image\", 1, \"mtn-product-card__badge-image\", 3, \"src\", \"error\"], [\"src\", \"assets/icons/heart-unselected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to wishlist\"], [\"src\", \"assets/icons/heart-selected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Remove from wishlist\"], [1, \"mtn-product-card__counter-group\", 3, \"click\"], [1, \"mtn-product-card__counter-btn\", 3, \"click\"], [\"src\", \"assets/icons/delete-custom.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Delete\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__counter-minus\", 4, \"ngIf\"], [1, \"mtn-product-card__counter-value\"], [1, \"mtn-product-card__counter-btn\", \"mtn-product-card__counter-btn--plus\", 3, \"click\"], [\"src\", \"assets/icons/plus-custom.svg\", \"width\", \"16\", \"height\", \"16\", \"alt\", \"Plus\"], [\"src\", \"assets/icons/delete-custom.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Delete\"], [1, \"mtn-product-card__counter-minus\"], [1, \"mtn-product-card__cart-btn-floating\", 3, \"disabled\", \"click\"], [\"class\", \"mtn-product-card__cart-badge\", 4, \"ngIf\"], [\"src\", \"assets/icons/cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to cart\", 4, \"ngIf\"], [\"src\", \"assets/icons/white cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"View cart\", 4, \"ngIf\"], [1, \"mtn-product-card__cart-badge\"], [\"src\", \"assets/icons/cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to cart\"], [\"src\", \"assets/icons/white cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"View cart\"], [1, \"mtn-product-card__rating\"], [\"src\", \"assets/icons/rating.svg\", \"width\", \"14\", \"height\", \"14\", \"alt\", \"Rating\"], [1, \"mtn-product-card__rating-value\"], [1, \"mtn-product-card__rating-count\"], [1, \"mtn-product-card__product-details__price\"], [1, \"mtn-product-card__product-details__price__currencycode\"], [1, \"mtn-product-card__bottom-row\"], [\"class\", \"mtn-product-card__sale-info\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__product-details__low-stock\", 4, \"ngIf\"], [1, \"mtn-product-card__sale-info\"], [1, \"mtn-product-card__product-details__sale-price\"], [1, \"mtn-product-card__discount-badge\"], [1, \"mtn-product-card__product-details__low-stock\"], [\"appGAImpression\", \"\", 1, \"old-product-card\", 3, \"product\"], [\"appGAImpression\", \"\", \"class\", \"mtn-product-card-mobile\", \"style\", \"width: inherit\", 3, \"product\", \"click\", 4, \"ngIf\"], [\"appGAImpression\", \"\", 1, \"mtn-product-card-mobile\", 2, \"width\", \"inherit\", 3, \"product\", \"click\"], [1, \"mtn-product-card-mobile__top-labels\"], [1, \"mtn-product-card-mobile__badge\"], [1, \"mtn-product-card-mobile__heart-holder\"], [1, \"mtn-product-card-mobile__right-top\", \"cursor-pointer\", 3, \"click\"], [\"src\", \"assets/icons/heart-unselected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to wishlist\", \"title\", \"Add to wishlist\", 4, \"ngIf\"], [\"src\", \"assets/icons/heart-selected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Remove from wishlist\", \"title\", \"Remove from wishlist\", 4, \"ngIf\"], [1, \"background-image-color\"], [\"alt\", \"No Image\", 1, \"mtn-product-card-mobile__product-image\", 3, \"ngSrc\", \"fill\", \"error\"], [\"class\", \"mtn-product-card-mobile__cart-button\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"mtn-product-card-mobile__cart-clicked-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mtn-product-card__product-details__low-stock\", \"style\", \"display: none;\", 4, \"ngIf\"], [1, \"mtn-product-card-mobile__product-details\"], [1, \"mtn-product-card-mobile__product-details__title\"], [\"sale\", \"\"], [1, \"mtn-product-card-mobile__left-top\", \"mtn-product-card__left-top\"], [\"alt\", \"No Image\", 3, \"src\", \"error\"], [\"src\", \"assets/icons/heart-unselected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to wishlist\", \"title\", \"Add to wishlist\"], [\"src\", \"assets/icons/heart-selected.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Remove from wishlist\", \"title\", \"Remove from wishlist\"], [1, \"mtn-product-card-mobile__cart-button\", 3, \"ngClass\", \"click\"], [\"src\", \"assets/icons/cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to cart\", \"title\", \"Add to cart\", 4, \"ngIf\"], [\"src\", \"assets/icons/cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"Add to cart\", \"title\", \"Add to cart\"], [1, \"cart-badge\"], [\"src\", \"assets/icons/white cart.svg\", \"width\", \"18\", \"height\", \"18\", \"alt\", \"View cart\", \"title\", \"View cart\"], [1, \"mtn-product-card-mobile__cart-clicked-button\", 3, \"click\"], [1, \"mtn-product-card-mobile__cart-clicked-button__btn\", 3, \"click\"], [\"class\", \"mtn-product-card-mobile__cart-clicked-button__minus\", 4, \"ngIf\"], [1, \"mtn-product-card-mobile__cart-clicked-button__product-cart-count\"], [1, \"mtn-product-card-mobile__cart-clicked-button__btn\", \"mtn-product-card-mobile__cart-clicked-button__btn--plus\", 3, \"click\"], [1, \"mtn-product-card-mobile__cart-clicked-button__minus\"], [1, \"mtn-product-card__product-details__low-stock\", 2, \"display\", \"none\"], [1, \"mtn-product-card-mobile__product-details__price\"], [1, \"mtn-product-card-mobile__product-details__price__currencycode\"], [1, \"mtn-product-card-mobile__sale-info\", \"mtn-product-card-mobile__sale-info--placeholder\"], [\"class\", \"mtn-product-card-mobile__status-placeholder\", 4, \"ngIf\"], [1, \"mtn-product-card-mobile__status-placeholder\"], [1, \"d-flex\", \"justify-content-between\"], [\"class\", \"mtn-product-card-mobile__product-details__low-stock\", \"style\", \"display: none;\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"mtn-product-card-mobile__sale-info\"], [1, \"mtn-product-card-mobile__product-details__sale-price\"], [1, \"mtn-product-card-mobile__product-details__low-stock\", 2, \"display\", \"none\"]],\n    template: function ProductCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProductCardComponent_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, ProductCardComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(3, \"app-notify-modal\", 2);\n        i0.ɵɵlistener(\"close\", function ProductCardComponent_Template_app_notify_modal_close_3_listener() {\n          return ctx.displayNotifyModal = false;\n        })(\"submit\", function ProductCardComponent_Template_app_notify_modal_submit_3_listener($event) {\n          return ctx.onSubmitNotify($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"app-success-info-modal\", 3);\n        i0.ɵɵlistener(\"cancel\", function ProductCardComponent_Template_app_success_info_modal_cancel_4_listener() {\n          return ctx.onCancel();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"app-age-consent-modal\", 4);\n        i0.ɵɵlistener(\"submit\", function ProductCardComponent_Template_app_age_consent_modal_submit_5_listener() {\n          return ctx.onSubmitConsent();\n        })(\"cancel\", function ProductCardComponent_Template_app_age_consent_modal_cancel_5_listener() {\n          return ctx.closeConsentModal();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"app-ineligable-purchase-modal\", 5);\n        i0.ɵɵlistener(\"cancel\", function ProductCardComponent_Template_app_ineligable_purchase_modal_cancel_6_listener() {\n          return ctx.closeEligableModal();\n        });\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isMobileView || !ctx.isMobileTemplate)(\"ngIfElse\", _r1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"isEmailExist\", ctx.isEmailExist)(\"displayModal\", ctx.displayNotifyModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"titleMessage\", ctx.successTitleMessage)(\"bodyMessage\", ctx.successBodyMessage)(\"displayModal\", ctx.displaySuccessModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"age\", ctx.restrictionAge)(\"displayModal\", ctx.displayAgeConsentModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"displayModal\", ctx.displayEligableModal);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgOptimizedImage, i9.GAImpressionDirective, i10.SuccessInfoModalComponent, i11.NotifyModalComponent, i12.AgeConsentModalComponent, i13.IneligablePurchaseModalComponent, i8.DecimalPipe, i4.TranslatePipe],\n    styles: [\".mtn-product-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  max-height: 420px;\\n  min-height: 380px;\\n  height: 100%;\\n  flex-shrink: 0;\\n  position: relative;\\n  background: #FFFFFF;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 9px 0 rgba(175, 175, 175, 0.5);\\n  overflow: hidden;\\n  transition: box-shadow 0.3s ease;\\n  cursor: pointer;\\n}\\n.mtn-product-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.12);\\n}\\n.mtn-product-card__top-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12.896px;\\n  left: 11.461px;\\n  right: 12px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  z-index: 10;\\n  pointer-events: none;\\n}\\n.mtn-product-card__top-section[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  pointer-events: auto;\\n}\\n.mtn-product-card__badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.mtn-product-card__badge-image[_ngcontent-%COMP%] {\\n  border-radius: 2.866px;\\n  color: #FFF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%;\\n  display: block;\\n}\\n.mtn-product-card__wishlist-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 11.463px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 8.597px;\\n  opacity: 0.99;\\n  background: #FFF;\\n  box-shadow: 2.866px 5.731px 11.463px 0 rgba(0, 0, 0, 0.2);\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.mtn-product-card__wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);\\n}\\n.mtn-product-card__wishlist-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.mtn-product-card__wishlist-btn--active[_ngcontent-%COMP%] {\\n  background: #204E6E;\\n}\\n.mtn-product-card__image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #F8F8F8;\\n  overflow: hidden;\\n  border-radius: 5.731px 5.731px 0 0;\\n}\\n.mtn-product-card__product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: fit-content;\\n  object-position: top;\\n  padding: 0;\\n}\\n.mtn-product-card__rating[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 165px;\\n  left: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background: #FFFFFF;\\n  border-radius: 4px;\\n  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);\\n  z-index: 5;\\n}\\n.mtn-product-card__rating-value[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mtn-product-card__rating-count[_ngcontent-%COMP%] {\\n  color: #77878F;\\n  font-family: var(--light-font);\\n  font-size: 12px;\\n  line-height: 1;\\n}\\n.mtn-product-card__cart-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  z-index: 15;\\n}\\n.mtn-product-card__cart-overlay--disabled[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  pointer-events: none;\\n}\\n.mtn-product-card__counter-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: #FFFFFF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n.mtn-product-card__counter-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n  border: none;\\n  background: transparent;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  padding: 0;\\n}\\n.mtn-product-card__counter-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.mtn-product-card__counter-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.mtn-product-card__counter-btn--delete[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(47%) sepia(69%) saturate(959%) hue-rotate(315deg) brightness(103%) contrast(90%);\\n}\\n.mtn-product-card__counter-btn--plus[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(11%) sepia(6%) saturate(1015%) hue-rotate(169deg) brightness(97%) contrast(94%);\\n}\\n.mtn-product-card__counter-btn--minus[_ngcontent-%COMP%]   .mtn-product-card__counter-minus[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 18px;\\n  font-weight: 400;\\n  line-height: 1;\\n}\\n.mtn-product-card__counter-value[_ngcontent-%COMP%] {\\n  min-width: 24px;\\n  text-align: center;\\n  color: var(--Gray-700, #475156);\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n}\\n.mtn-product-card__cart-btn-floating[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  padding: 11.463px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  border: none;\\n  border-radius: 8.597px;\\n  opacity: 0.99;\\n  background: #FFF;\\n  box-shadow: 2.866px 5.731px 11.463px 0 rgba(0, 0, 0, 0.2);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.mtn-product-card__cart-btn-floating[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.2);\\n}\\n.mtn-product-card__cart-btn-floating[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.mtn-product-card__cart-btn-floating--active[_ngcontent-%COMP%] {\\n  background: #204E6E;\\n}\\n.mtn-product-card__cart-btn-floating--active[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) invert(1);\\n}\\n.mtn-product-card__cart-btn-floating--disabled[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.mtn-product-card__cart-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 5px;\\n  left: -10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 20px;\\n  height: 20px;\\n  padding: 0 6px;\\n  background: #EAB308;\\n  color: #000000;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 12px;\\n  font-weight: 700;\\n  line-height: 1;\\n  border-radius: 10px;\\n  border: 2px solid #FFFFFF;\\n}\\n.mtn-product-card__product-details[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.mtn-product-card__product-details__title[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 3;\\n  line-clamp: 3;\\n  overflow: hidden;\\n  color: #000000;\\n  text-overflow: ellipsis;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 17.194px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 125%;\\n  min-height: 66px;\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.mtn-product-card__product-details__title[_ngcontent-%COMP%]:hover {\\n  color: #333333;\\n}\\n.mtn-product-card__product-details__price[_ngcontent-%COMP%] {\\n  color: #004F71;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 20.06px;\\n  font-style: normal;\\n  font-weight: 800;\\n  line-height: 100%;\\n}\\n.mtn-product-card__product-details__price__currencycode[_ngcontent-%COMP%] {\\n  color: #004F71;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n}\\n.mtn-product-card__product-details__sale-price[_ngcontent-%COMP%] {\\n  color: #AFAFAF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--light-font);\\n  font-size: 17.194px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  text-decoration-line: line-through;\\n}\\n.mtn-product-card__product-details__low-stock[_ngcontent-%COMP%] {\\n  color: #EE5858;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 125%;\\n}\\n.mtn-product-card__sale-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-height: 22px;\\n}\\n.mtn-product-card__sale-info--placeholder[_ngcontent-%COMP%] {\\n  visibility: hidden;\\n}\\n.mtn-product-card__status-placeholder[_ngcontent-%COMP%] {\\n  min-height: 22px;\\n  visibility: hidden;\\n}\\n.mtn-product-card__price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 8px;\\n}\\n.mtn-product-card__bottom-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.mtn-product-card__sale-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.mtn-product-card__discount-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  padding: 5.731px 8.597px;\\n  align-items: center;\\n  gap: 11.463px;\\n  border-radius: 2.866px;\\n  background: #3FBF6F;\\n  color: #FFF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%;\\n}\\n.mtn-product-card-footer[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.mtn-product-card-mobile[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  max-height: 420px;\\n  min-height: 380px;\\n  height: 100%;\\n  flex-shrink: 0;\\n  background: #FFFFFF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: box-shadow 0.3s ease;\\n}\\n.mtn-product-card-mobile[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.12);\\n}\\n.mtn-product-card-mobile__top-labels[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12.896px;\\n  left: 11.461px;\\n  right: 12px;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: flex-start;\\n  z-index: 10;\\n  pointer-events: none;\\n}\\n.mtn-product-card-mobile__top-labels[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  pointer-events: auto;\\n}\\n.mtn-product-card-mobile__badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  max-width: calc(100% - 56px);\\n  overflow: hidden;\\n}\\n.mtn-product-card-mobile__badge[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 2.866px;\\n  max-width: 100%;\\n  height: auto;\\n  color: #FFF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%;\\n  display: block;\\n}\\n.mtn-product-card-mobile__heart-holder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  display: flex;\\n  justify-content: flex-end;\\n  width: 40px;\\n}\\n.mtn-product-card-mobile__right-top[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  padding: 0;\\n  display: grid;\\n  place-items: center;\\n  border-radius: 8.597px;\\n  opacity: 0.99;\\n  background: #FFF;\\n  box-shadow: 2.866px 5.731px 11.463px 0 rgba(0, 0, 0, 0.2);\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.mtn-product-card-mobile__right-top[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);\\n}\\n.mtn-product-card-mobile__right-top[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.mtn-product-card-mobile__right-top--active[_ngcontent-%COMP%] {\\n  background: #204E6E;\\n}\\n.mtn-product-card-mobile__right-top[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  display: block;\\n}\\n.mtn-product-card-mobile[_ngcontent-%COMP%]   .background-image-color[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 200px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #F8F8F8;\\n  overflow: hidden;\\n  border-radius: 5.731px 5.731px 0 0;\\n}\\n.mtn-product-card-mobile__product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  padding: 0;\\n}\\n.mtn-product-card-mobile__cart-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  width: 40px;\\n  height: 40px;\\n  padding: 0;\\n  display: grid;\\n  place-items: center;\\n  border-radius: 8.597px;\\n  opacity: 0.99;\\n  background: #FFF;\\n  box-shadow: 2.866px 5.731px 11.463px 0 rgba(0, 0, 0, 0.2);\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.mtn-product-card-mobile__cart-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.2);\\n}\\n.mtn-product-card-mobile__cart-button.already-added-cart[_ngcontent-%COMP%] {\\n  background: #204E6E;\\n}\\n.mtn-product-card-mobile__cart-button.already-added-cart[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) invert(1);\\n}\\n.mtn-product-card-mobile__cart-button--disabled[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.mtn-product-card-mobile__cart-button[_ngcontent-%COMP%]   .cart-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 5px;\\n  left: -10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  padding: 0 6px;\\n  background: #EAB308;\\n  color: #000000;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 12px;\\n  font-weight: 700;\\n  line-height: 1;\\n  border-radius: 10px;\\n  border: 2px solid #FFFFFF;\\n}\\n.mtn-product-card-mobile__cart-clicked-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: #FFFFFF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease;\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n  border: none;\\n  background: transparent;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  padding: 0;\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn--delete[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(47%) sepia(69%) saturate(959%) hue-rotate(315deg) brightness(103%) contrast(90%);\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn--plus[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: brightness(0) saturate(100%) invert(11%) sepia(6%) saturate(1015%) hue-rotate(169deg) brightness(97%) contrast(94%);\\n}\\n.mtn-product-card-mobile__cart-clicked-button__btn--minus[_ngcontent-%COMP%]   .mtn-product-card-mobile__cart-clicked-button__minus[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 18px;\\n  font-weight: 400;\\n  line-height: 1;\\n}\\n.mtn-product-card-mobile__cart-clicked-button__product-cart-count[_ngcontent-%COMP%] {\\n  min-width: 24px;\\n  text-align: center;\\n  color: var(--Gray-700, #475156);\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n}\\n.mtn-product-card-mobile__product-details[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.mtn-product-card-mobile__product-details__title[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 3;\\n  line-clamp: 3;\\n  overflow: hidden;\\n  color: #000000;\\n  text-overflow: ellipsis;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 17.194px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 125%;\\n  min-height: 66px;\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.mtn-product-card-mobile__product-details__title[_ngcontent-%COMP%]:hover {\\n  color: #333333;\\n}\\n.mtn-product-card-mobile__product-details__price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 4px;\\n  color: #004F71;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 20.06px;\\n  font-style: normal;\\n  font-weight: 800;\\n  line-height: 100%;\\n}\\n.mtn-product-card-mobile__product-details__price__currencycode[_ngcontent-%COMP%] {\\n  color: #004F71;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n}\\n.mtn-product-card-mobile__product-details__sale-price[_ngcontent-%COMP%] {\\n  color: #AFAFAF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--light-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  text-decoration-line: line-through;\\n}\\n.mtn-product-card-mobile__product-details__low-stock[_ngcontent-%COMP%] {\\n  color: #EE5858;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 14.328px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 125%;\\n}\\n.mtn-product-card-mobile__label[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  padding: 4px 8px;\\n  font-family: var(--medium-font);\\n  font-size: 12px;\\n  font-weight: 700;\\n  line-height: 1;\\n  border-radius: 4px;\\n}\\n.mtn-product-card-mobile__label.label-color[_ngcontent-%COMP%] {\\n  background: #2DB224;\\n  color: #FFFFFF;\\n}\\n.mtn-product-card-mobile[_ngcontent-%COMP%]   .sale-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.mtn-product-card-mobile__sale-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 8px;\\n}\\n.mtn-product-card-mobile[_ngcontent-%COMP%]   .mtn-product-card__discount-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  padding: 5.731px 8.597px;\\n  align-items: center;\\n  gap: 11.463px;\\n  border-radius: 2.866px;\\n  background: #3FBF6F;\\n  color: #FFF;\\n  font-family: \\\"MTN Brighter Sans\\\", var(--medium-font);\\n  font-size: 10px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}