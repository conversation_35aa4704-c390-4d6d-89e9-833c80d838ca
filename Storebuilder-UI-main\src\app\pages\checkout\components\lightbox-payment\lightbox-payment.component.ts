import {
  Component,
  Input,
  Output,
  EventEmitter,
  AfterViewInit,
  OnDestroy,
  Renderer2,
  ChangeDetectorRef,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { Router } from '@angular/router';
import { get } from 'scriptjs';
declare const Lightbox: any;
@Component({
  selector: 'app-lightbox-payment',
  templateUrl: './lightbox-payment.component.html',
})
export class LightboxPaymentComponent implements AfterViewInit, OnDestroy, OnChanges {

  @Input() config: any;
  @Input() lightBoxURL!: string;

  @Output() onComplete = new EventEmitter<any>();
  @Output() onCancel = new EventEmitter<void>();
  @Output() onError = new EventEmitter<void>();

  isScriptLoaded = false;
  displayLoaderModal = false;
  observer!: MutationObserver;
  iframeLoadListener!: () => void;

  constructor(private renderer: Renderer2, private cd: ChangeDetectorRef, private router: Router,
  ) {
  }

  ngAfterViewInit() {
    this.observeIframeInjection();
  }

  ngOnInit(): void {
    this.callLightbox();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['config'] && !changes['config'].firstChange) {
      this.callLightbox();
    }
  }

  callLightbox(): void {

    get(this.config.lightBoxURL, () => {
      const lb = Lightbox;
      if (lb?.Checkout) {
        lb.Checkout.configure = {
          ...this.config,
          completeCallback: (data: any) => {
            Lightbox.Checkout.closeLightbox();
            this.onComplete.emit(data);
          },
          cancelCallback: () => {
            Lightbox.Checkout.closeLightbox();
            this.onCancel.emit();
          },
          errorCallback: () => {
            Lightbox.Checkout.closeLightbox();
            this.onError.emit();
          }
        }
      };
      this.isScriptLoaded = true;

    });
  }

  startPayment(): void {
    this.displayLoaderModal = true;
    const lb = Lightbox;
    if (this.isScriptLoaded && lb?.Checkout?.showLightbox) {
      lb.Checkout.showLightbox();
    } else {
      console.error('Lightbox script not loaded or Checkout not available');
    }
  }

  observeIframeInjection(): void {
    this.observer = new MutationObserver((mutations) => {
      for (let mutation of mutations) {
        if (mutation.type === 'childList') {
          Array.from(mutation.addedNodes).forEach((node: Node) => {
            if (node.nodeName === 'IFRAME') {
              const iframe = node as HTMLIFrameElement;
              this.addIframeLoadListener(iframe);
            }
          });
        }
      }
    });

    this.observer.observe(document.body, { childList: true, subtree: true });
  }

  private addIframeLoadListener(iframe: HTMLIFrameElement) {
    this.displayLoaderModal = true;
    this.iframeLoadListener = this.renderer.listen(iframe, 'load', () => {
      this.displayLoaderModal = false;
      this.cd.detectChanges()
      this.accessIframeElements(iframe);
    });
  }

  private accessIframeElements(iframe: HTMLIFrameElement) {
    try {
      const iframeDocument = iframe.contentDocument || iframe?.contentWindow?.document;
      const iframeBody = iframeDocument?.body;

      // Example: Check for a specific element or value inside the iframe
      const element = iframeDocument?.querySelector('#elementId');
      if (element) {
      } else {
      }
    } catch (error) {
      console.error('Error accessing iframe content:', error);
    }
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.iframeLoadListener) {
      this.iframeLoadListener();
    }
  }

}
