import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { GoogleAnalyticsService } from 'ngx-google-analytics';
import { GTMService } from '@core/services/gtm.service';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { AppDataService, PermissionService, RegisterService, StoreService, CustomGAService } from '@core/services';
import { RoleEnum } from '@core/interface';
import { CountryISO } from 'ngx-intl-tel-input-gg';
import { BackButtonComponent } from '../../../../shared/components/back-button/back-button.component';
import { PhoneInputComponent } from '../phone-input/phone-input.component';
import { EmailInputComponent } from '../email-input/email-input.component';
import {InitialModule} from "@shared/modules/initial.module";
import {RegisterUserModalComponent} from "../../modals/resigter-user-modal/register-user-modal.component";

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    ButtonModule,
    BackButtonComponent,
    PhoneInputComponent,
    EmailInputComponent,
    InitialModule,
    RegisterUserModalComponent

  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent implements OnInit {
  phoneNumber: any;
  email: string = '';
  isPhoneValid: boolean = false;
  isEmailValid: boolean = false;
  selectedRegistrationMethod: 'phone' | 'email' = 'phone';
  countryPhoneNumber: string = "";
  countryPhoneCode: string = "";
  phoneLength: number = 12;
  phoneInputLength: number = 12;
  CustomCountryISO: any;
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];
  customPlaceHolder: any = '';
  isMobileLayout: boolean = false;
  screenWidth: number = window.innerWidth;
  isGoogleAnalytics: boolean = false;
  isRegisterModal = false;
  hasTrackedStartSignup: boolean = false;
  counter: any = 0;
  nationalNumber: string;
  get buttonLabel(): string {
    return this.screenWidth <= 767 ? 'register.next' : 'register.continue';
  }

  get isFormValid(): boolean {
    return this.selectedRegistrationMethod === 'phone' ? this.isPhoneValid : this.isEmailValid;
  }

  constructor(
    private otpService: RegisterService,
    private translate: TranslateService,
    private messageService: MessageService,
    private router: Router,
    private store: StoreService,
    private permissionService: PermissionService,
    private appDataService: AppDataService,
    private $gaService: GoogleAnalyticsService,
    private $gtmService: GTMService,
    private customGAService: CustomGAService
  ) {
    this.setupCustomPlaceholder();
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.$gtmService.pushPageView('signUp');

    // Track view_signup_form event
    if (this.isGoogleAnalytics) {
      console.log('viewSignupFormEvent : ',this.isGoogleAnalytics);
      this.customGAService.viewSignupFormEvent('register_page');
    }

    this.CustomCountryISO = localStorage.getItem("isoCode");

    if (this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
    }
  }

  private setupCustomPlaceholder() {
    let tenantId = localStorage.getItem('tenantId');
    if (tenantId && tenantId !== '') {
      const placeholders: { [key: string]: string } = {
        '1': 'XXXXXXXXXX',
        '2': 'XXXXXXXXX',
        '3': 'XXXXXXXXX',
        '4': 'XXXXXXXXXX'
      };
      this.customPlaceHolder = placeholders[tenantId] || '';
    }
  }

  onPhoneNumberChange(phoneNumber: any) {
    let tenantId = localStorage.getItem('tenantId');
    let firstChar = phoneNumber?.number?.charAt(0)
    let countryCode = phoneNumber?.countryCode
    // let nationalNumber = phoneNumber?.nationalNumber.replace(/\s+/g, '')
    if( countryCode == 'GH'){
      // if(firstChar == '0'){
      //   this.counter++
      // }
      // if(phoneNumber?.number?.length == 1){
          // this.messageService.add({
          //   severity: 'info',
          //   summary: "Info",
          //   detail: 'Phone number must be 9 digits or start with 0 followed by 9 digits.',
          //   life:4000
          // });
      // }
      // if(firstChar == '0' || this.counter > 2 || this.counter > 5 || this.counter > 7 && tenantId == '2' ){
      //   this.phoneInputLength=10
      //       if(phoneNumber?.number?.length <2){
      //         this.counter = 0
      //       }
      // }else{
          if (this.appDataService.configuration) {
            const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
            if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
          }
      // }
    }else if(countryCode == 'UG'){
        if (this.appDataService.configuration) {
              const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
              if (phoneLength) {
                this.phoneInputLength = parseInt(phoneLength.value) - (this.nationalNumber[0] === '0' ? 0 : 1);
              }
         }
    }
    this.phoneNumber = phoneNumber;
  }
  onRawPhoneNumberChange($event: string) {
   this.nationalNumber = $event
  }
 
  onPhoneValidationChange(isValid: boolean) {
    this.isPhoneValid = isValid;
    
    // Track start_signup event when user starts interacting with the form
    if (isValid && this.isGoogleAnalytics && !this.hasTrackedStartSignup) {
      this.customGAService.startSignupEvent('Phone');
      this.hasTrackedStartSignup = true;
    }
  }

  onEmailChange(email: string | null) {
    this.email = email || '';
  }

  onEmailValidationChange(isValid: boolean) {
    this.isEmailValid = isValid;
    
    // Track start_signup event when user starts interacting with the form
    if (isValid && this.isGoogleAnalytics && !this.hasTrackedStartSignup) {
      this.customGAService.startSignupEvent('Email');
      this.hasTrackedStartSignup = true;
    }
  }

  selectRegistrationMethod(method: 'phone' | 'email') {
    this.selectedRegistrationMethod = method;
    localStorage.setItem('registrationMethod', method);
    this.isPhoneValid = false;
    this.isEmailValid = false;
    this.hasTrackedStartSignup = false;
  }

  checkMobileExist() {
    this.isRegisterModal = false;
    if (this.isGoogleAnalytics) {
      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_UP, '', 'SIGN_UP', 1, true);
    }

    this.store.set("loading", true);

    if (this.selectedRegistrationMethod === 'phone') {
      this.handlePhoneRegistration();
    } else {
      this.handleEmailRegistration();
    }
  }

  private handlePhoneRegistration() {
    if(this.phoneNumber?.e164Number){
      this.phoneNumber = this.phoneNumber.e164Number.slice(1);
    }

    if (this.isPhoneValid) {
      this.processRegistration(this.phoneNumber);
    } else {
      this.showValidationError('ErrorMessages.mobileRequired');
    }
  }

  private handleEmailRegistration() {
    if (this.isEmailValid && this.email) {
      this.processRegistration(this.email);
    } else {
      this.showValidationError('ErrorMessages.emailRequired');
    }
  }

  private processRegistration(username: string) {
    this.otpService.username = username;
    this.otpService.countryId = "1448983B-0C38-450A-BD71-9204D181B925";
    
    const requestData = {
      UserName: username,
      CountryId: "1448983B-0C38-450A-BD71-9204D181B925",
      UserRole: RoleEnum.consumer
    };

    const apiCall = this.selectedRegistrationMethod === 'phone' 
      ? this.otpService.checkMobileNumber(requestData)
      : this.otpService.checkEmailAddress(requestData);
    
    apiCall.subscribe({
      next: (res: any) => {
        this.store.set("loading", false);
        if (res.success) {
          if (res.data.isRegistered) {
            this.isRegisterModal = true;
          } else {
            localStorage.setItem('registrationMethod', this.selectedRegistrationMethod);
            this.router.navigateByUrl('/register/register-otp', { state: { mobile: username } });
          }
        } else {
     
            this.messageService.add({
               severity: 'error',
              summary: "Error",
              detail: res.message
            });
         
          this.store.set("loading", false);
          this.router.navigateByUrl('/login');

        }
      },
      error: (err: any) => {
        this.store.set("loading", false);
        this.showError(err.message);
      }
    });
  }

  private showValidationError(errorKey: string) {
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: this.translate.instant(errorKey)
    });
    this.store.set("loading", false);
  }

  private showError(message: string) {
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: message
    });
    this.store.set("loading", false);
  }

  omit_special_char(event: any) {
    let key;
    key = event.charCode;
    return (key > 47 && key < 58);
  }

  reloadCurrentPage(pageId: number, title: string) {
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
      this.router.navigate(['/about-us/'], {
        queryParams: { pageId: pageId, title: title },
      })
    );
  }
}
