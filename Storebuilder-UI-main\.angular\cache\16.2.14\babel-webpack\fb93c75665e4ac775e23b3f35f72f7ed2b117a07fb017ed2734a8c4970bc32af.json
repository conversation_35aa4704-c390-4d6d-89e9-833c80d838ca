{"ast": null, "code": "import { environment } from \"src/environments/environment\";\nexport class EndPointsConfig {\n  static get ApiUrl() {\n    return {\n      Url: `${environment.apiEndPoint}`\n    };\n  }\n  static get Tenants() {\n    return {\n      Controller: \"Tenant/Tenants\",\n      EndPoints: {\n        GetAllTenant: \"GetAllTenant\"\n      }\n    };\n  }\n  static get Shop() {\n    return {\n      Controller: \"Tenant/Shop\",\n      EndPoints: {\n        CreateShop: \"CreateShop\",\n        CreateShopAdmin: \"CreateShopAdmin\",\n        GetMarketplaceShop: \"GetMarketplaceShop\",\n        GetAllShops: \"GetAllShops\"\n      }\n    };\n  }\n  static get Category() {\n    return {\n      Controller: \"Product/Category\",\n      EndPoints: {\n        GetAllCategory: \"GetAllCategory\",\n        GetCategoryById: \"GetCategoryById\",\n        CreateCategory: \"CreateCategory\",\n        UpdateCategory: \"UpdateCategory\",\n        DeleteCategory: \"DeleteCategory\",\n        DisableCategory: \"DisableCategory\"\n      }\n    };\n  }\n  static get Order() {\n    return {\n      Controller: \"Order/Order\",\n      EndPoints: {\n        GetAllOrder: \"GetAllOrder\",\n        CreateOrder: \"CreateOrder\",\n        GetOrderById: \"GetOrderById\",\n        RefundOrder: \"RefundOrder\",\n        GetCustomerOrder: \"GetCustomerOrder\",\n        RequestOrderRefund: \"RequestOrderRefund\",\n        UpdateOrderStatus: \"UpdateOrderStatus\"\n      }\n    };\n  }\n  static get OrderDetails() {\n    return {\n      Controller: \"Order/OrderDetail\",\n      EndPoints: {\n        GetOrderDetailsByOrderId: \"GetOrderDetailsByOrderId\",\n        GetAllOrderDetail: \"GetAllOrderDetail\",\n        GetAllSubOrdersWithPayments: \"GetAllSubOrdersWithPayments\"\n      }\n    };\n  }\n  static get User() {\n    return {\n      Controller: \"Auth/User\",\n      EndPoints: {\n        GetUserDetails: \"GetUserDetails\",\n        MarketPlaceAdminById: \"MarketPlaceAdminById\",\n        MarketPlaceAdmin: \"MarketPlaceAdmin\"\n      }\n    };\n  }\n  static get Product() {\n    return {\n      Controller: \"Product/Product\",\n      EndPoints: {\n        GetAllProduct: \"GetAllProduct\",\n        ApproveProduct: \"ApproveProduct\"\n      }\n    };\n  }\n  static get SpecsProduct() {\n    return {\n      Controller: \"Product/SpecsProduct\",\n      EndPoints: {\n        GetSpecsDetailProductById: \"GetSpecsDetailProductById\"\n      }\n    };\n  }\n  static get RefundReason() {\n    return {\n      Controller: \"Order/RefundReason\",\n      EndPoints: {\n        GetAllRefundReason: \"GetAllRefundReason\",\n        GetRefundReasonList: \"GetRefundReasonList\"\n      }\n    };\n  }\n}", "map": {"version": 3, "names": ["environment", "EndPointsConfig", "ApiUrl", "Url", "apiEndPoint", "Tenants", "Controller", "EndPoints", "GetAllTenant", "Shop", "CreateShop", "CreateShopAdmin", "GetMarketplaceShop", "GetAllShops", "Category", "GetAllCategory", "GetCategoryById", "CreateCategory", "UpdateCategory", "DeleteCategory", "DisableCategory", "Order", "GetAllOrder", "CreateOrder", "GetOrderById", "RefundOrder", "GetCustomerOrder", "RequestOrderRefund", "UpdateOrderStatus", "OrderDetails", "GetOrderDetailsByOrderId", "GetAllOrderDetail", "GetAllSubOrdersWithPayments", "User", "GetUserDetails", "MarketPlaceAdminById", "MarketPlaceAdmin", "Product", "GetAllProduct", "ApproveProduct", "SpecsProduct", "GetSpecsDetailProductById", "RefundReason", "GetAllRefundReason", "GetRefundReasonList"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\services\\endPoints.config.ts"], "sourcesContent": ["import { environment } from \"src/environments/environment\"\r\n\r\nexport class EndPointsConfig {\r\n    public static get ApiUrl() {\r\n        return {\r\n            Url: `${environment.apiEndPoint}`\r\n        }\r\n    }\r\n\r\n    public static get Tenants() {\r\n        return {\r\n            Controller: \"Tenant/Tenants\",\r\n            EndPoints: {\r\n                GetAllTenant: \"GetAllTenant\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get Shop() {\r\n        return {\r\n            Controller: \"Tenant/Shop\",\r\n            EndPoints: {\r\n                CreateShop: \"CreateShop\",\r\n                CreateShopAdmin: \"CreateShopAdmin\",\r\n                GetMarketplaceShop: \"GetMarketplaceShop\",\r\n                GetAllShops: \"GetAllShops\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get Category() {\r\n        return {\r\n            Controller: \"Product/Category\",\r\n            EndPoints: {\r\n                GetAllCategory: \"GetAllCategory\",\r\n                GetCategoryById: \"GetCategoryById\",\r\n                CreateCategory: \"CreateCategory\",\r\n                UpdateCategory: \"UpdateCategory\",\r\n                DeleteCategory: \"DeleteCategory\",\r\n                DisableCategory: \"DisableCategory\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get Order() {\r\n        return {\r\n            Controller: \"Order/Order\",\r\n            EndPoints: {\r\n                GetAllOrder: \"GetAllOrder\",\r\n                CreateOrder: \"CreateOrder\",\r\n                GetOrderById: \"GetOrderById\",\r\n                RefundOrder: \"RefundOrder\",\r\n                GetCustomerOrder: \"GetCustomerOrder\",\r\n                RequestOrderRefund: \"RequestOrderRefund\",\r\n                UpdateOrderStatus: \"UpdateOrderStatus\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get OrderDetails() {\r\n        return {\r\n            Controller: \"Order/OrderDetail\",\r\n            EndPoints: {\r\n                GetOrderDetailsByOrderId: \"GetOrderDetailsByOrderId\",\r\n                GetAllOrderDetail: \"GetAllOrderDetail\",\r\n                GetAllSubOrdersWithPayments: \"GetAllSubOrdersWithPayments\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get User() {\r\n        return {\r\n            Controller: \"Auth/User\",\r\n            EndPoints: {\r\n                GetUserDetails: \"GetUserDetails\",\r\n                MarketPlaceAdminById: \"MarketPlaceAdminById\",\r\n                MarketPlaceAdmin: \"MarketPlaceAdmin\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get Product() {\r\n        return {\r\n            Controller: \"Product/Product\",\r\n            EndPoints: {\r\n                GetAllProduct: \"GetAllProduct\",\r\n                ApproveProduct: \"ApproveProduct\"\r\n            }\r\n        }\r\n    }\r\n\r\n    public static get SpecsProduct() {\r\n        return {\r\n            Controller: \"Product/SpecsProduct\",\r\n            EndPoints: {\r\n                GetSpecsDetailProductById: \"GetSpecsDetailProductById\"\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    public static get RefundReason() {\r\n        return {\r\n            Controller: \"Order/RefundReason\",\r\n            EndPoints: {\r\n                GetAllRefundReason: \"GetAllRefundReason\",\r\n                GetRefundReasonList: \"GetRefundReasonList\"\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAE1D,OAAM,MAAOC,eAAe;EACjB,WAAWC,MAAMA,CAAA;IACpB,OAAO;MACHC,GAAG,EAAE,GAAGH,WAAW,CAACI,WAAW;KAClC;EACL;EAEO,WAAWC,OAAOA,CAAA;IACrB,OAAO;MACHC,UAAU,EAAE,gBAAgB;MAC5BC,SAAS,EAAE;QACPC,YAAY,EAAE;;KAErB;EACL;EAEO,WAAWC,IAAIA,CAAA;IAClB,OAAO;MACHH,UAAU,EAAE,aAAa;MACzBC,SAAS,EAAE;QACPG,UAAU,EAAE,YAAY;QACxBC,eAAe,EAAE,iBAAiB;QAClCC,kBAAkB,EAAE,oBAAoB;QACxCC,WAAW,EAAE;;KAEpB;EACL;EAEO,WAAWC,QAAQA,CAAA;IACtB,OAAO;MACHR,UAAU,EAAE,kBAAkB;MAC9BC,SAAS,EAAE;QACPQ,cAAc,EAAE,gBAAgB;QAChCC,eAAe,EAAE,iBAAiB;QAClCC,cAAc,EAAE,gBAAgB;QAChCC,cAAc,EAAE,gBAAgB;QAChCC,cAAc,EAAE,gBAAgB;QAChCC,eAAe,EAAE;;KAExB;EACL;EAEO,WAAWC,KAAKA,CAAA;IACnB,OAAO;MACHf,UAAU,EAAE,aAAa;MACzBC,SAAS,EAAE;QACPe,WAAW,EAAE,aAAa;QAC1BC,WAAW,EAAE,aAAa;QAC1BC,YAAY,EAAE,cAAc;QAC5BC,WAAW,EAAE,aAAa;QAC1BC,gBAAgB,EAAE,kBAAkB;QACpCC,kBAAkB,EAAE,oBAAoB;QACxCC,iBAAiB,EAAE;;KAE1B;EACL;EAEO,WAAWC,YAAYA,CAAA;IAC1B,OAAO;MACHvB,UAAU,EAAE,mBAAmB;MAC/BC,SAAS,EAAE;QACPuB,wBAAwB,EAAE,0BAA0B;QACpDC,iBAAiB,EAAE,mBAAmB;QACtCC,2BAA2B,EAAE;;KAEpC;EACL;EAEO,WAAWC,IAAIA,CAAA;IAClB,OAAO;MACH3B,UAAU,EAAE,WAAW;MACvBC,SAAS,EAAE;QACP2B,cAAc,EAAE,gBAAgB;QAChCC,oBAAoB,EAAE,sBAAsB;QAC5CC,gBAAgB,EAAE;;KAEzB;EACL;EAEO,WAAWC,OAAOA,CAAA;IACrB,OAAO;MACH/B,UAAU,EAAE,iBAAiB;MAC7BC,SAAS,EAAE;QACP+B,aAAa,EAAE,eAAe;QAC9BC,cAAc,EAAE;;KAEvB;EACL;EAEO,WAAWC,YAAYA,CAAA;IAC1B,OAAO;MACHlC,UAAU,EAAE,sBAAsB;MAClCC,SAAS,EAAE;QACPkC,yBAAyB,EAAE;;KAElC;EACL;EAGO,WAAWC,YAAYA,CAAA;IAC1B,OAAO;MACHpC,UAAU,EAAE,oBAAoB;MAChCC,SAAS,EAAE;QACPoC,kBAAkB,EAAE,oBAAoB;QACxCC,mBAAmB,EAAE;;KAE5B;EACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}