{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TransactionService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Payment`;\n  }\n  createTransaction(data) {\n    return this.http.post(`${this.baseUrl}/Transaction/CreateTransaction`, data);\n  }\n  createMultipleTransactionDetails(data) {\n    return this.http.post(`${this.baseUrl}/TransactionDetails/CreateMultipleTransactionDetails`, data);\n  }\n  GetOrderTransaction(id) {\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionByOrderWithoutFilter/${id}`);\n  }\n  GetTransactionById(id) {\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionById/${id}`);\n  }\n}\nTransactionService.ɵfac = function TransactionService_Factory(t) {\n  return new (t || TransactionService)(i0.ɵɵinject(i1.HttpClient));\n};\nTransactionService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: TransactionService,\n  factory: TransactionService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}