{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { environment } from '@environments/environment';\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-cookie-service\";\nimport * as i4 from \"ngx-google-analytics\";\nimport * as i5 from \"@core/services/gtm.service\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/checkbox\";\nimport * as i11 from \"ngx-intl-tel-input-gg\";\nfunction IndexComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"h3\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 4);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 5);\n    i0.ɵɵelement(7, \"path\", 6)(8, \"path\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 8);\n    i0.ɵɵelement(13, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_Template_div_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.showAddress());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 5);\n    i0.ɵɵelement(16, \"path\", 11)(17, \"path\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 8);\n    i0.ɵɵelement(22, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.triggerGoogleAnalytics());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 5);\n    i0.ɵɵelement(25, \"path\", 14)(26, \"path\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 8);\n    i0.ɵɵelement(31, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(33, \"svg\", 5);\n    i0.ɵɵelement(34, \"path\", 17)(35, \"path\", 18)(36, \"path\", 19)(37, \"path\", 20)(38, \"path\", 21)(39, \"path\", 22)(40, \"path\", 23)(41, \"path\", 24)(42, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 26);\n    i0.ɵɵtext(47);\n    i0.ɵɵpipe(48, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 8);\n    i0.ɵɵelement(50, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(52, \"svg\", 5);\n    i0.ɵɵelement(53, \"path\", 27)(54, \"path\", 28)(55, \"path\", 29)(56, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(57, \"span\");\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_Template_div_click_60_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.modalpopUp());\n    });\n    i0.ɵɵelement(61, \"img\", 32);\n    i0.ɵɵelementStart(62, \"span\", 33);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"img\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 34);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 35);\n    i0.ɵɵelement(67, \"circle\", 36)(68, \"path\", 37)(69, \"path\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(70, \"span\");\n    i0.ɵɵtext(71);\n    i0.ɵɵpipe(72, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 39)(74, \"p-checkbox\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_container_0_Template_p_checkbox_ngModelChange_74_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.isChecked = $event);\n    })(\"onChange\", function IndexComponent_ng_container_0_Template_p_checkbox_onChange_74_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.updateSubscription($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"span\");\n    i0.ɵɵtext(76);\n    i0.ɵɵpipe(77, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_Template_div_click_78_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.logOut());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(79, \"svg\", 5);\n    i0.ɵɵelement(80, \"path\", 41)(81, \"path\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(82, \"span\");\n    i0.ɵɵtext(83);\n    i0.ɵɵpipe(84, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 14, \"account.index.profile\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 16, \"account.index.yourOrders\"), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 18, \"account.index.myAddresses\"), \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(29, 20, \"account.index.yourDetails\"), \" \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 22, \"account.index.lang\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(48, 24, \"account.index.language\"));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(59, 26, \"account.index.country\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.getImage(ctx_r0.selectedCountry == null ? null : ctx_r0.selectedCountry.flag), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.selectedCountry == null ? null : ctx_r0.selectedCountry.name);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(72, 28, \"footer.help\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.isChecked)(\"binary\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(77, 30, \"auth.optInCheckBox\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(84, 32, \"account.index.logout\"), \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"hidden-navbar\": a0\n  };\n};\nfunction IndexComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 43)(1, \"div\", 44);\n    i0.ɵɵelement(2, \"em\", 45)(3, \"em\", 46);\n    i0.ɵɵelementStart(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"div\", 49)(9, \"div\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 51)(13, \"a\", 52)(14, \"div\", 53);\n    i0.ɵɵelement(15, \"img\", 54);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"em\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.showAddress());\n    });\n    i0.ɵɵelementStart(20, \"div\", 53);\n    i0.ɵɵelement(21, \"img\", 57);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"em\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"a\", 58)(26, \"div\", 53);\n    i0.ɵɵelement(27, \"img\", 59);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"em\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 39)(32, \"p-checkbox\", 40);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_1_Template_p_checkbox_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.isChecked = $event);\n    })(\"onChange\", function IndexComponent_ng_template_1_Template_p_checkbox_onChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.updateSubscription($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_Template_a_click_36_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.logOut());\n    });\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(26, _c0, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 12, \"sideMenu.yourAccount\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 14, \"account.index.myAccount\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 16, \"account.index.yourOrders\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 18, \"account.index.myAddresses\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(29, 20, \"account.index.profile\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.isChecked)(\"binary\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(35, 22, \"auth.optInCheckBox\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 24, \"account.index.logout\"), \" \");\n  }\n}\nexport class IndexComponent {\n  constructor(store, router, cookieService, authTokenService, mainDataService, appDataService, dialogService, platformId, permissionService, $gaService, $gtmService, messageService, translate) {\n    this.store = store;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.mainDataService = mainDataService;\n    this.appDataService = appDataService;\n    this.dialogService = dialogService;\n    this.platformId = platformId;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.isMobileLayout = false;\n    this.isGoogleAnalytics = false;\n    this.isChecked = false;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n    this.baseUrl = `${environment.apiEndPoint}`;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.$gtmService.pushPageView('account');\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    /**/\n    let stringCountries = localStorage.getItem('allCountryTenants') ?? '';\n    let selectedTenant = localStorage.getItem('tenantId');\n    let countries = JSON.parse(stringCountries);\n    this.selectedCountry = countries.find(country => country.tenantId == selectedTenant);\n    this.getSubscriptionStatus();\n  }\n  logOut() {\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('profile', '');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    localStorage.setItem('secondaryDefault', 'false');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    this.store.set('cartProducts', []);\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    localStorage.setItem('isGuest', 'true');\n    this.router.navigate(['/']);\n  }\n  getImage(imageLink) {\n    if (imageLink) {\n      return `${this.baseUrl}/Images/${imageLink}`;\n    } else {\n      return '';\n    }\n  }\n  modalpopUp() {\n    this.dialogService.showDialog();\n  }\n  showAddress() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_MY_ADDRESSES, '', 'VIEW_ADDRESS ', 1, true);\n    }\n    this.router.navigate(['/account/address']);\n  }\n  triggerGoogleAnalytics() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_ACCOUNT_DETAILS, '', 'ACCOUNT_DETAILS', 1, true);\n    }\n  }\n  getSubscriptionStatus() {\n    this.mainDataService.getCustomerSubscriptionValue().subscribe(response => {\n      this.isChecked = response ?? false;\n    }, error => {\n      console.error('Error fetching subscription status:', error);\n    });\n  }\n  updateSubscription(event) {\n    this.isChecked = event.checked;\n    this.mainDataService.updateCustomerSubscriptionValue({\n      isSubscribed: this.isChecked\n    }).subscribe(() => {\n      if (this.isChecked) {\n        this.messageService.add({\n          severity: 'success',\n          summary: this.translate.instant(\"account.optInMessage\")\n        });\n      } else {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant(\"account.optOutMessage\")\n        });\n      }\n    }, error => {\n      console.error('Error updating subscription:', error);\n      this.isChecked = !this.isChecked;\n    });\n  }\n}\nIndexComponent.ɵfac = function IndexComponent_Factory(t) {\n  return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CookieService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i4.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i5.GTMService), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.TranslateService));\n};\nIndexComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: IndexComponent,\n  selectors: [[\"app-index\"]],\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [1, \"profile-container\"], [1, \"d-flex\", \"profile-heading\"], [\"routerLink\", \"/orders\", 1, \"profile-item\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"fill\", \"none\"], [\"d\", \"M16.0337 4.65046H15.7003L12.8837 1.83379C12.6587 1.60879 12.292 1.60879 12.0587 1.83379C11.8337 2.05879 11.8337 2.42546 12.0587 2.65879L14.0503 4.65046H5.95033L7.94199 2.65879C8.16699 2.43379 8.16699 2.06712 7.94199 1.83379C7.71699 1.60879 7.35033 1.60879 7.11699 1.83379L4.30866 4.65046H3.97533C3.22533 4.65046 1.66699 4.65046 1.66699 6.78379C1.66699 7.59212 1.83366 8.12546 2.18366 8.47546C2.38366 8.68379 2.62533 8.79212 2.88366 8.85046C3.12533 8.90879 3.38366 8.91712 3.63366 8.91712H16.367C16.6253 8.91712 16.867 8.90046 17.1003 8.85046C17.8003 8.68379 18.3337 8.18379 18.3337 6.78379C18.3337 4.65046 16.7753 4.65046 16.0337 4.65046Z\", \"fill\", \"#204E6E\"], [\"d\", \"M15.9087 10H4.09207C3.57541 10 3.18374 10.4583 3.26707 10.9667L3.96707 15.25C4.20041 16.6833 4.82541 18.3333 7.60041 18.3333H12.2754C15.0837 18.3333 15.5837 16.925 15.8837 15.35L16.7254 10.9917C16.8254 10.475 16.4337 10 15.9087 10ZM12.4004 13.375L9.69207 15.875C9.57541 15.9833 9.42541 16.0417 9.26707 16.0417C9.10874 16.0417 8.95041 15.9833 8.82541 15.8583L7.57541 14.6083C7.33374 14.3667 7.33374 13.9667 7.57541 13.725C7.82541 13.4833 8.21707 13.4833 8.46707 13.725L9.29207 14.55L11.5587 12.4583C11.8087 12.225 12.2087 12.2417 12.4421 12.4917C12.6754 12.75 12.6587 13.1417 12.4004 13.375Z\", \"fill\", \"#204E6E\"], [1, \"arrow\"], [\"alt\", \"\", \"src\", \"assets/icons/arrow.svg\", 2, \"width\", \"20px\", \"height\", \"20px\"], [1, \"profile-item\", 3, \"click\"], [\"d\", \"M9.99989 1.66712C10.891 1.66165 11.7742 1.83593 12.5964 2.17952C13.4187 2.52311 14.1633 3.02897 14.7856 3.66685C15.4079 4.30474 15.8952 5.06155 16.2184 5.89206C16.5416 6.72256 16.694 7.6097 16.6666 8.50045C16.6666 11.2671 14.4443 14.5449 9.99989 18.3338C5.55823 14.5421 3.336 11.2643 3.33323 8.50045C3.30576 7.6097 3.45818 6.72256 3.78137 5.89206C4.10456 5.06155 4.59188 4.30474 5.21421 3.66685C5.83653 3.02897 6.58109 2.52311 7.40336 2.17952C8.22563 1.83593 9.10874 1.66165 9.99989 1.66712Z\", \"fill\", \"#204E6E\"], [\"d\", \"M9.99968 10.0003C9.67004 10.0003 9.34781 9.90258 9.07372 9.71944C8.79964 9.53631 8.58602 9.27601 8.45988 8.97147C8.33373 8.66692 8.30072 8.33181 8.36503 8.00851C8.42934 7.68521 8.58808 7.38824 8.82116 7.15515C9.05425 6.92206 9.35122 6.76333 9.67452 6.69902C9.99783 6.63471 10.3329 6.66771 10.6375 6.79386C10.942 6.92001 11.2023 7.13363 11.3855 7.40771C11.5686 7.68179 11.6663 8.00402 11.6663 8.33366C11.665 8.77528 11.489 9.19844 11.1767 9.51071C10.8645 9.82299 10.4413 9.99901 9.99968 10.0003Z\", \"fill\", \"#F5F5F5\"], [\"routerLink\", \"/account/details\", 1, \"profile-item\", 3, \"click\"], [\"d\", \"M18.3337 10.0003C18.3337 5.40866 14.592 1.66699 10.0003 1.66699C5.40866 1.66699 1.66699 5.40866 1.66699 10.0003C1.66699 12.417 2.70866 14.592 4.35866 16.117C4.35866 16.1253 4.35866 16.1253 4.35033 16.1337C4.43366 16.217 4.53366 16.2837 4.61699 16.3587C4.66699 16.4003 4.70866 16.442 4.75866 16.4753C4.90866 16.6003 5.07533 16.717 5.23366 16.8337C5.29199 16.8753 5.34199 16.9087 5.40033 16.9503C5.55866 17.0587 5.72533 17.1587 5.90033 17.2503C5.95866 17.2837 6.02533 17.3253 6.08366 17.3587C6.25033 17.4503 6.42533 17.5337 6.60866 17.6087C6.67533 17.642 6.74199 17.6753 6.80866 17.7003C6.99199 17.7753 7.17533 17.842 7.35866 17.9003C7.42533 17.9253 7.49199 17.9503 7.55866 17.967C7.75866 18.0253 7.95866 18.0753 8.15866 18.1253C8.21699 18.142 8.27533 18.1587 8.34199 18.167C8.57533 18.217 8.80866 18.2503 9.05033 18.2753C9.08366 18.2753 9.11699 18.2837 9.15033 18.292C9.43366 18.317 9.71699 18.3337 10.0003 18.3337C10.2837 18.3337 10.567 18.317 10.842 18.292C10.8753 18.292 10.9087 18.2837 10.942 18.2753C11.1837 18.2503 11.417 18.217 11.6503 18.167C11.7087 18.1587 11.767 18.1337 11.8337 18.1253C12.0337 18.0753 12.242 18.0337 12.4337 17.967C12.5003 17.942 12.567 17.917 12.6337 17.9003C12.817 17.8337 13.0087 17.7753 13.1837 17.7003C13.2503 17.6753 13.317 17.642 13.3837 17.6087C13.5587 17.5337 13.7337 17.4503 13.9087 17.3587C13.9753 17.3253 14.0337 17.2837 14.092 17.2503C14.2587 17.1503 14.4253 17.0587 14.592 16.9503C14.6503 16.917 14.7003 16.8753 14.7587 16.8337C14.9253 16.717 15.0837 16.6003 15.2337 16.4753C15.2837 16.4337 15.3253 16.392 15.3753 16.3587C15.467 16.2837 15.5587 16.2087 15.642 16.1337C15.642 16.1253 15.642 16.1253 15.6337 16.117C17.292 14.592 18.3337 12.417 18.3337 10.0003ZM14.117 14.142C11.8587 12.6253 8.15866 12.6253 5.88366 14.142C5.51699 14.3837 5.21699 14.667 4.96699 14.9753C3.70033 13.692 2.91699 11.9337 2.91699 10.0003C2.91699 6.09199 6.09199 2.91699 10.0003 2.91699C13.9087 2.91699 17.0837 6.09199 17.0837 10.0003C17.0837 11.9337 16.3003 13.692 15.0337 14.9753C14.792 14.667 14.4837 14.3837 14.117 14.142Z\", \"fill\", \"#204E6E\"], [\"d\", \"M10 5.77539C8.275 5.77539 6.875 7.17539 6.875 8.90039C6.875 10.5921 8.2 11.9671 9.95833 12.0171C9.98333 12.0171 10.0167 12.0171 10.0333 12.0171C10.05 12.0171 10.075 12.0171 10.0917 12.0171C10.1 12.0171 10.1083 12.0171 10.1083 12.0171C11.7917 11.9587 13.1167 10.5921 13.125 8.90039C13.125 7.17539 11.725 5.77539 10 5.77539Z\", \"fill\", \"#204E6E\"], [1, \"profile-item\"], [\"d\", \"M6.3748 17.4245C6.3498 17.4245 6.31647 17.4412 6.29147 17.4412C4.6748 16.6412 3.35814 15.3162 2.5498 13.6995C2.5498 13.6745 2.56647 13.6412 2.56647 13.6162C3.58314 13.9162 4.63314 14.1412 5.6748 14.3162C5.85814 15.3662 6.0748 16.4079 6.3748 17.4245Z\", \"fill\", \"#204E6E\"], [\"d\", \"M17.4502 13.7079C16.6252 15.3662 15.2502 16.7079 13.5752 17.5162C13.8919 16.4579 14.1585 15.3912 14.3335 14.3162C15.3835 14.1412 16.4169 13.9162 17.4335 13.6162C17.4252 13.6495 17.4502 13.6829 17.4502 13.7079Z\", \"fill\", \"#204E6E\"], [\"d\", \"M17.5169 6.42507C16.4669 6.1084 15.4085 5.85007 14.3335 5.66673C14.1585 4.59173 13.9002 3.52507 13.5752 2.4834C15.3002 3.3084 16.6919 4.70007 17.5169 6.42507Z\", \"fill\", \"#204E6E\"], [\"d\", \"M6.37507 2.5748C6.07507 3.59147 5.8584 4.6248 5.6834 5.6748C4.6084 5.84147 3.54173 6.10814 2.4834 6.4248C3.29173 4.7498 4.6334 3.3748 6.29173 2.5498C6.31673 2.5498 6.35007 2.5748 6.37507 2.5748Z\", \"fill\", \"#204E6E\"], [\"d\", \"M12.9085 5.49199C10.9751 5.27533 9.02513 5.27533 7.0918 5.49199C7.30013 4.35033 7.5668 3.20866 7.9418 2.10866C7.95846 2.04199 7.95013 1.99199 7.95846 1.92533C8.6168 1.76699 9.2918 1.66699 10.0001 1.66699C10.7001 1.66699 11.3835 1.76699 12.0335 1.92533C12.0418 1.99199 12.0418 2.04199 12.0585 2.10866C12.4335 3.21699 12.7001 4.35033 12.9085 5.49199Z\", \"fill\", \"#204E6E\"], [\"d\", \"M5.49199 12.9085C4.34199 12.7001 3.20866 12.4335 2.10866 12.0585C2.04199 12.0418 1.99199 12.0501 1.92533 12.0418C1.76699 11.3835 1.66699 10.7085 1.66699 10.0001C1.66699 9.30013 1.76699 8.6168 1.92533 7.9668C1.99199 7.95846 2.04199 7.95846 2.10866 7.9418C3.21699 7.57513 4.34199 7.30013 5.49199 7.0918C5.28366 9.02513 5.28366 10.9751 5.49199 12.9085Z\", \"fill\", \"#204E6E\"], [\"d\", \"M18.3338 10.0001C18.3338 10.7085 18.2338 11.3835 18.0755 12.0418C18.0088 12.0501 17.9588 12.0418 17.8921 12.0585C16.7838 12.4251 15.6505 12.7001 14.5088 12.9085C14.7255 10.9751 14.7255 9.02513 14.5088 7.0918C15.6505 7.30013 16.7921 7.5668 17.8921 7.9418C17.9588 7.95846 18.0088 7.9668 18.0755 7.9668C18.2338 8.62513 18.3338 9.30013 18.3338 10.0001Z\", \"fill\", \"#204E6E\"], [\"d\", \"M12.9085 14.5088C12.7001 15.6588 12.4335 16.7921 12.0585 17.8921C12.0418 17.9588 12.0418 18.0088 12.0335 18.0755C11.3835 18.2338 10.7001 18.3338 10.0001 18.3338C9.2918 18.3338 8.6168 18.2338 7.95846 18.0755C7.95013 18.0088 7.95846 17.9588 7.9418 17.8921C7.57513 16.7838 7.30013 15.6588 7.0918 14.5088C8.05846 14.6171 9.02513 14.6921 10.0001 14.6921C10.9751 14.6921 11.9501 14.6171 12.9085 14.5088Z\", \"fill\", \"#204E6E\"], [\"d\", \"M13.1364 13.1364C11.0522 13.3994 8.94846 13.3994 6.86422 13.1364C6.60125 11.0522 6.60125 8.94846 6.86422 6.86422C8.94846 6.60125 11.0522 6.60125 13.1364 6.86422C13.3994 8.94846 13.3994 11.0522 13.1364 13.1364Z\", \"fill\", \"#204E6E\"], [1, \"right-text\"], [\"d\", \"M7.62533 6.24121C7.15866 6.24121 6.79199 6.61621 6.79199 7.07454C6.79199 7.53288 7.16699 7.90788 7.62533 7.90788C8.08366 7.90788 8.45866 7.53288 8.45866 7.07454C8.45866 6.61621 8.08366 6.24121 7.62533 6.24121Z\", \"fill\", \"#204E6E\"], [\"d\", \"M17.8837 4.20033C17.1837 2.57533 15.642 1.66699 13.492 1.66699H6.50866C3.83366 1.66699 1.66699 3.83366 1.66699 6.50866V13.492C1.66699 15.642 2.57533 17.1837 4.20033 17.8837C4.35866 17.9503 4.54199 17.9087 4.65866 17.792L17.792 4.65866C17.917 4.53366 17.9587 4.35033 17.8837 4.20033ZM8.77533 10.2003C8.45033 10.517 8.02533 10.667 7.60033 10.667C7.17533 10.667 6.75033 10.5087 6.42533 10.2003C5.57533 9.40033 4.64199 8.12533 5.00033 6.60866C5.31699 5.23366 6.53366 4.61699 7.60033 4.61699C8.66699 4.61699 9.88366 5.23366 10.2003 6.61699C10.5503 8.12533 9.61699 9.40033 8.77533 10.2003Z\", \"fill\", \"#204E6E\"], [\"d\", \"M16.2251 17.1079C16.4084 17.2913 16.3834 17.5913 16.1584 17.7163C15.4251 18.1246 14.5334 18.3329 13.4917 18.3329H6.50841C6.26674 18.3329 6.16674 18.0496 6.33341 17.8829L11.3667 12.8496C11.5334 12.6829 11.7917 12.6829 11.9584 12.8496L16.2251 17.1079Z\", \"fill\", \"#204E6E\"], [\"d\", \"M18.3335 6.50841V13.4917C18.3335 14.5334 18.1251 15.4334 17.7168 16.1584C17.5918 16.3834 17.2918 16.4001 17.1085 16.2251L12.8418 11.9584C12.6751 11.7917 12.6751 11.5334 12.8418 11.3667L17.8751 6.33341C18.0501 6.16674 18.3335 6.26674 18.3335 6.50841Z\", \"fill\", \"#204E6E\"], [1, \"arrow\", 3, \"click\"], [1, \"flar-header-img\", 3, \"src\"], [1, \"flag-name\"], [\"routerLink\", \"/account/help\", 1, \"profile-item\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"cx\", \"10\", \"cy\", \"10\", \"r\", \"10\", \"fill\", \"#204E6E\"], [\"d\", \"M9.68985 13.4688C9.06899 13.4688 8.56641 13.9861 8.56641 14.6069C8.56641 15.213 9.05421 15.7452 9.68985 15.7452C10.3255 15.7452 10.828 15.213 10.828 14.6069C10.828 13.9861 10.3107 13.4688 9.68985 13.4688Z\", \"fill\", \"white\"], [\"d\", \"M9.88083 4.84766C7.88522 4.84766 6.96875 6.03027 6.96875 6.82848C6.96875 7.40498 7.45655 7.67108 7.85566 7.67108C8.65391 7.67108 8.32872 6.53282 9.83648 6.53282C10.5756 6.53282 11.1669 6.85805 11.1669 7.53803C11.1669 8.33624 10.3391 8.79448 9.85126 9.20836C9.42255 9.57786 8.86087 10.184 8.86087 11.4552C8.86087 12.2239 9.06784 12.4456 9.67387 12.4456C10.3982 12.4456 10.546 12.1204 10.546 11.8395C10.546 11.0709 10.5608 10.6274 11.3738 9.99179C11.7729 9.68138 13.0294 8.67617 13.0294 7.28667C13.0294 5.89718 11.7729 4.84766 9.88083 4.84766Z\", \"fill\", \"white\"], [\"id\", \"opt-in\", 1, \"opt-in\"], [3, \"ngModel\", \"binary\", \"ngModelChange\", \"onChange\"], [\"d\", \"M14 1.66699H11.8333C9.16667 1.66699 7.5 3.33366 7.5 6.00033V9.37533H12.7083C13.05 9.37533 13.3333 9.65866 13.3333 10.0003C13.3333 10.342 13.05 10.6253 12.7083 10.6253H7.5V14.0003C7.5 16.667 9.16667 18.3337 11.8333 18.3337H13.9917C16.6583 18.3337 18.325 16.667 18.325 14.0003V6.00033C18.3333 3.33366 16.6667 1.66699 14 1.66699Z\", \"fill\", \"#204E6E\"], [\"d\", \"M3.8002 9.37454L5.5252 7.64954C5.6502 7.52454 5.70853 7.36621 5.70853 7.20788C5.70853 7.04954 5.6502 6.88288 5.5252 6.76621C5.28353 6.52454 4.88353 6.52454 4.64186 6.76621L1.8502 9.55788C1.60853 9.79954 1.60853 10.1995 1.8502 10.4412L4.64186 13.2329C4.88353 13.4745 5.28353 13.4745 5.5252 13.2329C5.76686 12.9912 5.76686 12.5912 5.5252 12.3495L3.8002 10.6245H7.5002V9.37454H3.8002V9.37454Z\", \"fill\", \"#204E6E\"], [1, \"account-page\", 3, \"ngClass\"], [1, \"breadcrumb-address\", \"d-flex\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-home\", \"cursor-pointer\", 3, \"routerLink\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-angle-left\"], [3, \"routerLink\"], [1, \"content-container\", \"mobile-top\", 2, \"margin-top\", \"40px\"], [1, \"col-12\", \"col-md-6\", \"flex\", \"md:justify-content-start\", \"mobile-left\"], [1, \"font-size-22\", \"bold-font\", \"account-heading\"], [1, \"mt-4\", \"flex\", \"flex-column\", \"justify-content-start\", \"flex-wrap\", \"account-links\"], [\"routerLink\", \"/orders\", 1, \"flex\", \"justify-content-between\", \"py-3\", \"px-3\", \"surface-100\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\"], [1, \"align-items-center\", \"d-flex\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/book.svg\", 2, \"margin-right\", \"11px\"], [1, \"pi\", \"pi-angle-right\", \"text-blue-800\"], [1, \"flex\", \"justify-content-between\", \"py-3\", \"px-3\", \"surface-100\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/pin.svg\", 1, \"img\"], [\"routerLink\", \"/account/details\", 1, \"flex\", \"justify-content-between\", \"py-3\", \"px-3\", \"surface-100\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/person.svg\", 1, \"img\"], [1, \"mt-4\", \"no-underline\", \"text-blue-800\", \"cursor-pointer\", \"logout-color\", 3, \"click\"]],\n  template: function IndexComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, IndexComponent_ng_container_0_Template, 85, 34, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, IndexComponent_ng_template_1_Template, 39, 28, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isMobileLayout && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i8.NgControlStatus, i9.NgClass, i9.NgIf, i2.RouterLink, i8.NgModel, i10.Checkbox, i11.NativeElementInjectorDirective, i7.TranslatePipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.account-links[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n}\\n.account-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  width: 330px;\\n}\\n\\n.img[_ngcontent-%COMP%] {\\n  margin-left: -5px;\\n  margin-right: 10px;\\n}\\n\\n.account-page[_ngcontent-%COMP%] {\\n  margin-bottom: 250px !important;\\n}\\n\\n.breadcrumb-address[_ngcontent-%COMP%] {\\n  background-color: #efeded;\\n  padding: 1rem;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 15px;\\n}\\n\\n.opt-in[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: flex-start;\\n  width: 47%;\\n  margin: 20px 0;\\n}\\n.opt-in[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--gray-700);\\n  font: normal 400 12px/20px var(--regular-font);\\n  letter-spacing: 0.4px;\\n  text-transform: capitalize;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .account-page[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 10px !important;\\n  }\\n  .mobile-left[_ngcontent-%COMP%] {\\n    padding-left: 0px !important;\\n  }\\n  .account-links[_ngcontent-%COMP%] {\\n    margin-top: 10px !important;\\n  }\\n  .account-heading[_ngcontent-%COMP%] {\\n    font-size: 20px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n  .opt-in[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.logout-color[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor) !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.pi[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor) !important;\\n}\\n\\n.profile-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #fff;\\n  padding: 16px;\\n  margin-top: 72px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .profile-container[_ngcontent-%COMP%] {\\n    height: 800px;\\n    overflow: auto;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .profile-container[_ngcontent-%COMP%] {\\n    height: 800px;\\n    overflow: auto;\\n  }\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-heading[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 16px;\\n  align-self: stretch;\\n  color: #292D32;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n  margin-bottom: 0;\\n  padding-left: 0;\\n}\\n\\n.profile-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  border: 1px solid #E4E7E9;\\n  cursor: pointer;\\n  display: flex;\\n  padding: 24px 16px;\\n  align-items: center;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  font-family: \\\"main-medium\\\";\\n  align-items: center;\\n  position: relative;\\n}\\n.profile-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 16px;\\n}\\n.profile-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #323232;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  align-self: center;\\n}\\n.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  right: 10px;\\n  position: absolute;\\n}\\n.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flar-header-img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  border-radius: 4px;\\n  height: 18px;\\n  margin-right: 5px;\\n}\\n.profile-item[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]   .flag-name[_ngcontent-%COMP%] {\\n  color: #A3A3A3;\\n  font-family: \\\"main-regular\\\";\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%;\\n  text-transform: capitalize;\\n}\\n.profile-item[_ngcontent-%COMP%]   .right-text[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  color: #aaa;\\n  position: absolute;\\n  right: 37px;\\n  text-transform: capitalize;\\n  font-size: 12px;\\n}\\n.profile-item[_ngcontent-%COMP%]   .country-flag[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  width: 24px;\\n  height: 16px;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}