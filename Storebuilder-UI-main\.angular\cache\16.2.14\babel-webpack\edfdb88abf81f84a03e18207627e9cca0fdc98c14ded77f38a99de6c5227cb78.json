{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { HttpParams } from \"@angular/common/http\";\nimport { map } from \"rxjs\";\nimport { EndPointsConfig } from '@core/utilities';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CancelReason {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/CancelledReason`;\n  }\n  getAllCancelReason(id) {\n    let params = new HttpParams().set('UserType', id);\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.CancelledReason.Controller}/${EndPointsConfig.CancelledReason.EndPoints.GetAllCancelledReason}`, {\n      params: params\n    }).pipe(map(res => {\n      return {\n        reasons: res?.data?.records ? res?.data?.records : []\n      };\n    }));\n  }\n}\nCancelReason.ɵfac = function CancelReason_Factory(t) {\n  return new (t || CancelReason)(i0.ɵɵinject(i1.HttpClient));\n};\nCancelReason.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: CancelReason,\n  factory: CancelReason.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}