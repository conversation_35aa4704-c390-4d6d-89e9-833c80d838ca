{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, SkipSelf } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { map, mergeMap } from 'rxjs/operators';\nimport { defer, of } from 'rxjs';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nconst JWT_OPTIONS = new InjectionToken('JWT_OPTIONS');\n\n/* eslint-disable no-bitwise */\nclass JwtHelperService {\n  constructor(config = null) {\n    this.tokenGetter = config && config.tokenGetter || function () {};\n  }\n  urlBase64Decode(str) {\n    let output = str.replace(/-/g, '+').replace(/_/g, '/');\n    switch (output.length % 4) {\n      case 0:\n        {\n          break;\n        }\n      case 2:\n        {\n          output += '==';\n          break;\n        }\n      case 3:\n        {\n          output += '=';\n          break;\n        }\n      default:\n        {\n          throw new Error('Illegal base64url string!');\n        }\n    }\n    return this.b64DecodeUnicode(output);\n  }\n  // credits for decoder goes to https://github.com/atk\n  b64decode(str) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    let output = '';\n    str = String(str).replace(/=+$/, '');\n    if (str.length % 4 === 1) {\n      throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n    }\n    for (\n    // initialize result and counters\n    let bc = 0, bs, buffer, idx = 0;\n    // get next character\n    buffer = str.charAt(idx++);\n    // character found in table? initialize bit storage and add its ascii value;\n    ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,\n    // and if not first of each 4 characters,\n    // convert the first 8 bits to one ascii character\n    bc++ % 4) ? output += String.fromCharCode(255 & bs >> (-2 * bc & 6)) : 0) {\n      // try to find character in table (0-63, not found => -1)\n      buffer = chars.indexOf(buffer);\n    }\n    return output;\n  }\n  b64DecodeUnicode(str) {\n    return decodeURIComponent(Array.prototype.map.call(this.b64decode(str), c => {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n  }\n  decodeToken(token = this.tokenGetter()) {\n    if (token instanceof Promise) {\n      return token.then(t => this._decodeToken(t));\n    }\n    return this._decodeToken(token);\n  }\n  _decodeToken(token) {\n    if (!token || token === '') {\n      return null;\n    }\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      throw new Error(`The inspected token doesn't appear to be a JWT. Check to make sure it has three parts and see https://jwt.io for more.`);\n    }\n    const decoded = this.urlBase64Decode(parts[1]);\n    if (!decoded) {\n      throw new Error('Cannot decode the token.');\n    }\n    return JSON.parse(decoded);\n  }\n  getTokenExpirationDate(token = this.tokenGetter()) {\n    if (token instanceof Promise) {\n      return token.then(t => this._getTokenExpirationDate(t));\n    }\n    return this._getTokenExpirationDate(token);\n  }\n  _getTokenExpirationDate(token) {\n    let decoded;\n    decoded = this.decodeToken(token);\n    if (!decoded || !decoded.hasOwnProperty('exp')) {\n      return null;\n    }\n    const date = new Date(0);\n    date.setUTCSeconds(decoded.exp);\n    return date;\n  }\n  isTokenExpired(token = this.tokenGetter(), offsetSeconds) {\n    if (token instanceof Promise) {\n      return token.then(t => this._isTokenExpired(t, offsetSeconds));\n    }\n    return this._isTokenExpired(token, offsetSeconds);\n  }\n  _isTokenExpired(token, offsetSeconds) {\n    if (!token || token === '') {\n      return true;\n    }\n    const date = this.getTokenExpirationDate(token);\n    offsetSeconds = offsetSeconds || 0;\n    if (date === null) {\n      return false;\n    }\n    return !(date.valueOf() > new Date().valueOf() + offsetSeconds * 1000);\n  }\n  getAuthScheme(authScheme, request) {\n    if (typeof authScheme === 'function') {\n      return authScheme(request);\n    }\n    return authScheme;\n  }\n}\nJwtHelperService.ɵfac = function JwtHelperService_Factory(t) {\n  return new (t || JwtHelperService)(i0.ɵɵinject(JWT_OPTIONS));\n};\nJwtHelperService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JwtHelperService,\n  factory: JwtHelperService.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtHelperService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [JWT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nconst fromPromiseOrValue = input => {\n  if (input instanceof Promise) {\n    return defer(() => input);\n  }\n  return of(input);\n};\nclass JwtInterceptor {\n  constructor(config, jwtHelper, document) {\n    this.jwtHelper = jwtHelper;\n    this.document = document;\n    this.standardPorts = ['80', '443'];\n    this.tokenGetter = config.tokenGetter;\n    this.headerName = config.headerName || 'Authorization';\n    this.authScheme = config.authScheme || config.authScheme === '' ? config.authScheme : 'Bearer ';\n    this.allowedDomains = config.allowedDomains || [];\n    this.disallowedRoutes = config.disallowedRoutes || [];\n    this.throwNoTokenError = config.throwNoTokenError || false;\n    this.skipWhenExpired = config.skipWhenExpired;\n  }\n  isAllowedDomain(request) {\n    const requestUrl = new URL(request.url, this.document.location.origin);\n    // If the host equals the current window origin,\n    // the domain is allowed by default\n    if (requestUrl.host === this.document.location.host) {\n      return true;\n    }\n    // If not the current domain, check the allowed list\n    const hostName = `${requestUrl.hostname}${requestUrl.port && !this.standardPorts.includes(requestUrl.port) ? ':' + requestUrl.port : ''}`;\n    return this.allowedDomains.findIndex(domain => typeof domain === 'string' ? domain === hostName : domain instanceof RegExp ? domain.test(hostName) : false) > -1;\n  }\n  isDisallowedRoute(request) {\n    const requestedUrl = new URL(request.url, this.document.location.origin);\n    return this.disallowedRoutes.findIndex(route => {\n      if (typeof route === 'string') {\n        const parsedRoute = new URL(route, this.document.location.origin);\n        return parsedRoute.hostname === requestedUrl.hostname && parsedRoute.pathname === requestedUrl.pathname;\n      }\n      if (route instanceof RegExp) {\n        return route.test(request.url);\n      }\n      return false;\n    }) > -1;\n  }\n  handleInterception(token, request, next) {\n    const authScheme = this.jwtHelper.getAuthScheme(this.authScheme, request);\n    if (!token && this.throwNoTokenError) {\n      throw new Error('Could not get token from tokenGetter function.');\n    }\n    let tokenIsExpired = of(false);\n    if (this.skipWhenExpired) {\n      tokenIsExpired = token ? fromPromiseOrValue(this.jwtHelper.isTokenExpired(token)) : of(true);\n    }\n    if (token) {\n      return tokenIsExpired.pipe(map(isExpired => isExpired && this.skipWhenExpired ? request.clone() : request.clone({\n        setHeaders: {\n          [this.headerName]: `${authScheme}${token}`\n        }\n      })), mergeMap(innerRequest => next.handle(innerRequest)));\n    }\n    return next.handle(request);\n  }\n  intercept(request, next) {\n    if (!this.isAllowedDomain(request) || this.isDisallowedRoute(request)) {\n      return next.handle(request);\n    }\n    const token = this.tokenGetter(request);\n    return fromPromiseOrValue(token).pipe(mergeMap(asyncToken => {\n      return this.handleInterception(asyncToken, request, next);\n    }));\n  }\n}\nJwtInterceptor.ɵfac = function JwtInterceptor_Factory(t) {\n  return new (t || JwtInterceptor)(i0.ɵɵinject(JWT_OPTIONS), i0.ɵɵinject(JwtHelperService), i0.ɵɵinject(DOCUMENT));\n};\nJwtInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JwtInterceptor,\n  factory: JwtInterceptor.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [JWT_OPTIONS]\n      }]\n    }, {\n      type: JwtHelperService\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass JwtModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error(`JwtModule is already loaded. It should only be imported in your application's main module.`);\n    }\n  }\n  static forRoot(options) {\n    return {\n      ngModule: JwtModule,\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }, options.jwtOptionsProvider || {\n        provide: JWT_OPTIONS,\n        useValue: options.config\n      }, JwtHelperService]\n    };\n  }\n}\nJwtModule.ɵfac = function JwtModule_Factory(t) {\n  return new (t || JwtModule)(i0.ɵɵinject(JwtModule, 12));\n};\nJwtModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: JwtModule\n});\nJwtModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtModule, [{\n    type: NgModule\n  }], function () {\n    return [{\n      type: JwtModule,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }];\n  }, null);\n})();\n\n/*\n * Public API Surface of angular-jwt\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Inject", "NgModule", "Optional", "SkipSelf", "DOCUMENT", "map", "mergeMap", "defer", "of", "HTTP_INTERCEPTORS", "JWT_OPTIONS", "JwtHelperService", "constructor", "config", "tokenGetter", "urlBase64Decode", "str", "output", "replace", "length", "Error", "b64DecodeUnicode", "b64decode", "chars", "String", "bc", "bs", "buffer", "idx", "char<PERSON>t", "fromCharCode", "indexOf", "decodeURIComponent", "Array", "prototype", "call", "c", "charCodeAt", "toString", "slice", "join", "decodeToken", "token", "Promise", "then", "t", "_decodeToken", "parts", "split", "decoded", "JSON", "parse", "getTokenExpirationDate", "_getTokenExpirationDate", "hasOwnProperty", "date", "Date", "setUTCSeconds", "exp", "isTokenExpired", "offsetSeconds", "_isTokenExpired", "valueOf", "getAuthScheme", "authScheme", "request", "ɵfac", "JwtHelperService_Factory", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "fromPromiseOrValue", "input", "JwtInterceptor", "jwtHelper", "document", "standardPorts", "headerName", "allowedDomains", "disallowedRoutes", "throwNoTokenError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAllowedDomain", "requestUrl", "URL", "url", "location", "origin", "host", "hostName", "hostname", "port", "includes", "findIndex", "domain", "RegExp", "test", "isDisallowedRoute", "requestedUrl", "route", "parsedRoute", "pathname", "handleInterception", "next", "tokenIsExpired", "pipe", "isExpired", "clone", "setHeaders", "innerRequest", "handle", "intercept", "asyncToken", "JwtInterceptor_Factory", "Document", "JwtModule", "parentModule", "forRoot", "options", "ngModule", "providers", "provide", "useClass", "multi", "jwtOptionsProvider", "useValue", "JwtModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@auth0/angular-jwt/fesm2020/auth0-angular-jwt.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, SkipSelf } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { map, mergeMap } from 'rxjs/operators';\nimport { defer, of } from 'rxjs';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n\nconst JWT_OPTIONS = new InjectionToken('JWT_OPTIONS');\n\n/* eslint-disable no-bitwise */\nclass JwtHelperService {\n    constructor(config = null) {\n        this.tokenGetter = (config && config.tokenGetter) || function () { };\n    }\n    urlBase64Decode(str) {\n        let output = str.replace(/-/g, '+').replace(/_/g, '/');\n        switch (output.length % 4) {\n            case 0: {\n                break;\n            }\n            case 2: {\n                output += '==';\n                break;\n            }\n            case 3: {\n                output += '=';\n                break;\n            }\n            default: {\n                throw new Error('Illegal base64url string!');\n            }\n        }\n        return this.b64DecodeUnicode(output);\n    }\n    // credits for decoder goes to https://github.com/atk\n    b64decode(str) {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n        let output = '';\n        str = String(str).replace(/=+$/, '');\n        if (str.length % 4 === 1) {\n            throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n        }\n        for (\n        // initialize result and counters\n        let bc = 0, bs, buffer, idx = 0; \n        // get next character\n        (buffer = str.charAt(idx++)); \n        // character found in table? initialize bit storage and add its ascii value;\n        ~buffer &&\n            ((bs = bc % 4 ? bs * 64 + buffer : buffer),\n                // and if not first of each 4 characters,\n                // convert the first 8 bits to one ascii character\n                bc++ % 4)\n            ? (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6))))\n            : 0) {\n            // try to find character in table (0-63, not found => -1)\n            buffer = chars.indexOf(buffer);\n        }\n        return output;\n    }\n    b64DecodeUnicode(str) {\n        return decodeURIComponent(Array.prototype.map\n            .call(this.b64decode(str), (c) => {\n            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n        })\n            .join(''));\n    }\n    decodeToken(token = this.tokenGetter()) {\n        if (token instanceof Promise) {\n            return token.then(t => this._decodeToken(t));\n        }\n        return this._decodeToken(token);\n    }\n    _decodeToken(token) {\n        if (!token || token === '') {\n            return null;\n        }\n        const parts = token.split('.');\n        if (parts.length !== 3) {\n            throw new Error(`The inspected token doesn't appear to be a JWT. Check to make sure it has three parts and see https://jwt.io for more.`);\n        }\n        const decoded = this.urlBase64Decode(parts[1]);\n        if (!decoded) {\n            throw new Error('Cannot decode the token.');\n        }\n        return JSON.parse(decoded);\n    }\n    getTokenExpirationDate(token = this.tokenGetter()) {\n        if (token instanceof Promise) {\n            return token.then(t => this._getTokenExpirationDate(t));\n        }\n        return this._getTokenExpirationDate(token);\n    }\n    _getTokenExpirationDate(token) {\n        let decoded;\n        decoded = this.decodeToken(token);\n        if (!decoded || !decoded.hasOwnProperty('exp')) {\n            return null;\n        }\n        const date = new Date(0);\n        date.setUTCSeconds(decoded.exp);\n        return date;\n    }\n    isTokenExpired(token = this.tokenGetter(), offsetSeconds) {\n        if (token instanceof Promise) {\n            return token.then(t => this._isTokenExpired(t, offsetSeconds));\n        }\n        return this._isTokenExpired(token, offsetSeconds);\n    }\n    _isTokenExpired(token, offsetSeconds) {\n        if (!token || token === '') {\n            return true;\n        }\n        const date = this.getTokenExpirationDate(token);\n        offsetSeconds = offsetSeconds || 0;\n        if (date === null) {\n            return false;\n        }\n        return !(date.valueOf() > new Date().valueOf() + offsetSeconds * 1000);\n    }\n    getAuthScheme(authScheme, request) {\n        if (typeof authScheme === 'function') {\n            return authScheme(request);\n        }\n        return authScheme;\n    }\n}\nJwtHelperService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtHelperService, deps: [{ token: JWT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });\nJwtHelperService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtHelperService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtHelperService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [JWT_OPTIONS]\n                }] }]; } });\n\nconst fromPromiseOrValue = (input) => {\n    if (input instanceof Promise) {\n        return defer(() => input);\n    }\n    return of(input);\n};\nclass JwtInterceptor {\n    constructor(config, jwtHelper, document) {\n        this.jwtHelper = jwtHelper;\n        this.document = document;\n        this.standardPorts = ['80', '443'];\n        this.tokenGetter = config.tokenGetter;\n        this.headerName = config.headerName || 'Authorization';\n        this.authScheme =\n            config.authScheme || config.authScheme === ''\n                ? config.authScheme\n                : 'Bearer ';\n        this.allowedDomains = config.allowedDomains || [];\n        this.disallowedRoutes = config.disallowedRoutes || [];\n        this.throwNoTokenError = config.throwNoTokenError || false;\n        this.skipWhenExpired = config.skipWhenExpired;\n    }\n    isAllowedDomain(request) {\n        const requestUrl = new URL(request.url, this.document.location.origin);\n        // If the host equals the current window origin,\n        // the domain is allowed by default\n        if (requestUrl.host === this.document.location.host) {\n            return true;\n        }\n        // If not the current domain, check the allowed list\n        const hostName = `${requestUrl.hostname}${requestUrl.port && !this.standardPorts.includes(requestUrl.port)\n            ? ':' + requestUrl.port\n            : ''}`;\n        return (this.allowedDomains.findIndex((domain) => typeof domain === 'string'\n            ? domain === hostName\n            : domain instanceof RegExp\n                ? domain.test(hostName)\n                : false) > -1);\n    }\n    isDisallowedRoute(request) {\n        const requestedUrl = new URL(request.url, this.document.location.origin);\n        return (this.disallowedRoutes.findIndex((route) => {\n            if (typeof route === 'string') {\n                const parsedRoute = new URL(route, this.document.location.origin);\n                return (parsedRoute.hostname === requestedUrl.hostname &&\n                    parsedRoute.pathname === requestedUrl.pathname);\n            }\n            if (route instanceof RegExp) {\n                return route.test(request.url);\n            }\n            return false;\n        }) > -1);\n    }\n    handleInterception(token, request, next) {\n        const authScheme = this.jwtHelper.getAuthScheme(this.authScheme, request);\n        if (!token && this.throwNoTokenError) {\n            throw new Error('Could not get token from tokenGetter function.');\n        }\n        let tokenIsExpired = of(false);\n        if (this.skipWhenExpired) {\n            tokenIsExpired = token ? fromPromiseOrValue(this.jwtHelper.isTokenExpired(token)) : of(true);\n        }\n        if (token) {\n            return tokenIsExpired.pipe(map((isExpired) => isExpired && this.skipWhenExpired\n                ? request.clone()\n                : request.clone({\n                    setHeaders: {\n                        [this.headerName]: `${authScheme}${token}`,\n                    },\n                })), mergeMap((innerRequest) => next.handle(innerRequest)));\n        }\n        return next.handle(request);\n    }\n    intercept(request, next) {\n        if (!this.isAllowedDomain(request) || this.isDisallowedRoute(request)) {\n            return next.handle(request);\n        }\n        const token = this.tokenGetter(request);\n        return fromPromiseOrValue(token).pipe(mergeMap((asyncToken) => {\n            return this.handleInterception(asyncToken, request, next);\n        }));\n    }\n}\nJwtInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtInterceptor, deps: [{ token: JWT_OPTIONS }, { token: JwtHelperService }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nJwtInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [JWT_OPTIONS]\n                }] }, { type: JwtHelperService }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nclass JwtModule {\n    constructor(parentModule) {\n        if (parentModule) {\n            throw new Error(`JwtModule is already loaded. It should only be imported in your application's main module.`);\n        }\n    }\n    static forRoot(options) {\n        return {\n            ngModule: JwtModule,\n            providers: [\n                {\n                    provide: HTTP_INTERCEPTORS,\n                    useClass: JwtInterceptor,\n                    multi: true,\n                },\n                options.jwtOptionsProvider || {\n                    provide: JWT_OPTIONS,\n                    useValue: options.config,\n                },\n                JwtHelperService,\n            ],\n        };\n    }\n}\nJwtModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtModule, deps: [{ token: JwtModule, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nJwtModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtModule });\nJwtModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.3.0\", ngImport: i0, type: JwtModule, decorators: [{\n            type: NgModule\n        }], ctorParameters: function () { return [{ type: JwtModule, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }]; } });\n\n/*\n * Public API Surface of angular-jwt\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAChG,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,GAAG,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9C,SAASC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AAChC,SAASC,iBAAiB,QAAQ,sBAAsB;AAExD,MAAMC,WAAW,GAAG,IAAIZ,cAAc,CAAC,aAAa,CAAC;;AAErD;AACA,MAAMa,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,MAAM,GAAG,IAAI,EAAE;IACvB,IAAI,CAACC,WAAW,GAAID,MAAM,IAAIA,MAAM,CAACC,WAAW,IAAK,YAAY,CAAE,CAAC;EACxE;EACAC,eAAeA,CAACC,GAAG,EAAE;IACjB,IAAIC,MAAM,GAAGD,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACtD,QAAQD,MAAM,CAACE,MAAM,GAAG,CAAC;MACrB,KAAK,CAAC;QAAE;UACJ;QACJ;MACA,KAAK,CAAC;QAAE;UACJF,MAAM,IAAI,IAAI;UACd;QACJ;MACA,KAAK,CAAC;QAAE;UACJA,MAAM,IAAI,GAAG;UACb;QACJ;MACA;QAAS;UACL,MAAM,IAAIG,KAAK,CAAC,2BAA2B,CAAC;QAChD;IACJ;IACA,OAAO,IAAI,CAACC,gBAAgB,CAACJ,MAAM,CAAC;EACxC;EACA;EACAK,SAASA,CAACN,GAAG,EAAE;IACX,MAAMO,KAAK,GAAG,mEAAmE;IACjF,IAAIN,MAAM,GAAG,EAAE;IACfD,GAAG,GAAGQ,MAAM,CAACR,GAAG,CAAC,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACpC,IAAIF,GAAG,CAACG,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIC,KAAK,CAAE,mEAAkE,CAAC;IACxF;IACA;IACA;IACA,IAAIK,EAAE,GAAG,CAAC,EAAEC,EAAE,EAAEC,MAAM,EAAEC,GAAG,GAAG,CAAC;IAC/B;IACCD,MAAM,GAAGX,GAAG,CAACa,MAAM,CAACD,GAAG,EAAE,CAAC;IAC3B;IACA,CAACD,MAAM,KACDD,EAAE,GAAGD,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAG,EAAE,GAAGC,MAAM,GAAGA,MAAM;IACrC;IACA;IACAF,EAAE,EAAE,GAAG,CAAC,CAAC,GACVR,MAAM,IAAIO,MAAM,CAACM,YAAY,CAAC,GAAG,GAAIJ,EAAE,KAAM,CAAC,CAAC,GAAGD,EAAE,GAAI,CAAC,CAAE,CAAC,GAC7D,CAAC,EAAE;MACL;MACAE,MAAM,GAAGJ,KAAK,CAACQ,OAAO,CAACJ,MAAM,CAAC;IAClC;IACA,OAAOV,MAAM;EACjB;EACAI,gBAAgBA,CAACL,GAAG,EAAE;IAClB,OAAOgB,kBAAkB,CAACC,KAAK,CAACC,SAAS,CAAC7B,GAAG,CACxC8B,IAAI,CAAC,IAAI,CAACb,SAAS,CAACN,GAAG,CAAC,EAAGoB,CAAC,IAAK;MAClC,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CACGC,IAAI,CAAC,EAAE,CAAC,CAAC;EAClB;EACAC,WAAWA,CAACC,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAAE;IACpC,IAAI4B,KAAK,YAAYC,OAAO,EAAE;MAC1B,OAAOD,KAAK,CAACE,IAAI,CAACC,CAAC,IAAI,IAAI,CAACC,YAAY,CAACD,CAAC,CAAC,CAAC;IAChD;IACA,OAAO,IAAI,CAACC,YAAY,CAACJ,KAAK,CAAC;EACnC;EACAI,YAAYA,CAACJ,KAAK,EAAE;IAChB,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,EAAE,EAAE;MACxB,OAAO,IAAI;IACf;IACA,MAAMK,KAAK,GAAGL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;IAC9B,IAAID,KAAK,CAAC5B,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIC,KAAK,CAAE,wHAAuH,CAAC;IAC7I;IACA,MAAM6B,OAAO,GAAG,IAAI,CAAClC,eAAe,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACE,OAAO,EAAE;MACV,MAAM,IAAI7B,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,OAAO8B,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;EAC9B;EACAG,sBAAsBA,CAACV,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAAE;IAC/C,IAAI4B,KAAK,YAAYC,OAAO,EAAE;MAC1B,OAAOD,KAAK,CAACE,IAAI,CAACC,CAAC,IAAI,IAAI,CAACQ,uBAAuB,CAACR,CAAC,CAAC,CAAC;IAC3D;IACA,OAAO,IAAI,CAACQ,uBAAuB,CAACX,KAAK,CAAC;EAC9C;EACAW,uBAAuBA,CAACX,KAAK,EAAE;IAC3B,IAAIO,OAAO;IACXA,OAAO,GAAG,IAAI,CAACR,WAAW,CAACC,KAAK,CAAC;IACjC,IAAI,CAACO,OAAO,IAAI,CAACA,OAAO,CAACK,cAAc,CAAC,KAAK,CAAC,EAAE;MAC5C,OAAO,IAAI;IACf;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;IACxBD,IAAI,CAACE,aAAa,CAACR,OAAO,CAACS,GAAG,CAAC;IAC/B,OAAOH,IAAI;EACf;EACAI,cAAcA,CAACjB,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAAE8C,aAAa,EAAE;IACtD,IAAIlB,KAAK,YAAYC,OAAO,EAAE;MAC1B,OAAOD,KAAK,CAACE,IAAI,CAACC,CAAC,IAAI,IAAI,CAACgB,eAAe,CAAChB,CAAC,EAAEe,aAAa,CAAC,CAAC;IAClE;IACA,OAAO,IAAI,CAACC,eAAe,CAACnB,KAAK,EAAEkB,aAAa,CAAC;EACrD;EACAC,eAAeA,CAACnB,KAAK,EAAEkB,aAAa,EAAE;IAClC,IAAI,CAAClB,KAAK,IAAIA,KAAK,KAAK,EAAE,EAAE;MACxB,OAAO,IAAI;IACf;IACA,MAAMa,IAAI,GAAG,IAAI,CAACH,sBAAsB,CAACV,KAAK,CAAC;IAC/CkB,aAAa,GAAGA,aAAa,IAAI,CAAC;IAClC,IAAIL,IAAI,KAAK,IAAI,EAAE;MACf,OAAO,KAAK;IAChB;IACA,OAAO,EAAEA,IAAI,CAACO,OAAO,CAAC,CAAC,GAAG,IAAIN,IAAI,CAAC,CAAC,CAACM,OAAO,CAAC,CAAC,GAAGF,aAAa,GAAG,IAAI,CAAC;EAC1E;EACAG,aAAaA,CAACC,UAAU,EAAEC,OAAO,EAAE;IAC/B,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;MAClC,OAAOA,UAAU,CAACC,OAAO,CAAC;IAC9B;IACA,OAAOD,UAAU;EACrB;AACJ;AACArD,gBAAgB,CAACuD,IAAI,YAAAC,yBAAAtB,CAAA;EAAA,YAAAA,CAAA,IAAwFlC,gBAAgB,EAA1Bd,EAAE,CAAAuE,QAAA,CAA0C1D,WAAW;AAAA,CAA6C;AACvMC,gBAAgB,CAAC0D,KAAK,kBAD6ExE,EAAE,CAAAyE,kBAAA;EAAA5B,KAAA,EACY/B,gBAAgB;EAAA4D,OAAA,EAAhB5D,gBAAgB,CAAAuD;AAAA,EAAG;AACpI;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KAFmG3E,EAAE,CAAA4E,iBAAA,CAEV9D,gBAAgB,EAAc,CAAC;IAC9G+D,IAAI,EAAE3E;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2E,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAE1E,MAAM;QACZ6E,IAAI,EAAE,CAACnE,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMoE,kBAAkB,GAAIC,KAAK,IAAK;EAClC,IAAIA,KAAK,YAAYpC,OAAO,EAAE;IAC1B,OAAOpC,KAAK,CAAC,MAAMwE,KAAK,CAAC;EAC7B;EACA,OAAOvE,EAAE,CAACuE,KAAK,CAAC;AACpB,CAAC;AACD,MAAMC,cAAc,CAAC;EACjBpE,WAAWA,CAACC,MAAM,EAAEoE,SAAS,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;IAClC,IAAI,CAACrE,WAAW,GAAGD,MAAM,CAACC,WAAW;IACrC,IAAI,CAACsE,UAAU,GAAGvE,MAAM,CAACuE,UAAU,IAAI,eAAe;IACtD,IAAI,CAACpB,UAAU,GACXnD,MAAM,CAACmD,UAAU,IAAInD,MAAM,CAACmD,UAAU,KAAK,EAAE,GACvCnD,MAAM,CAACmD,UAAU,GACjB,SAAS;IACnB,IAAI,CAACqB,cAAc,GAAGxE,MAAM,CAACwE,cAAc,IAAI,EAAE;IACjD,IAAI,CAACC,gBAAgB,GAAGzE,MAAM,CAACyE,gBAAgB,IAAI,EAAE;IACrD,IAAI,CAACC,iBAAiB,GAAG1E,MAAM,CAAC0E,iBAAiB,IAAI,KAAK;IAC1D,IAAI,CAACC,eAAe,GAAG3E,MAAM,CAAC2E,eAAe;EACjD;EACAC,eAAeA,CAACxB,OAAO,EAAE;IACrB,MAAMyB,UAAU,GAAG,IAAIC,GAAG,CAAC1B,OAAO,CAAC2B,GAAG,EAAE,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAACC,MAAM,CAAC;IACtE;IACA;IACA,IAAIJ,UAAU,CAACK,IAAI,KAAK,IAAI,CAACb,QAAQ,CAACW,QAAQ,CAACE,IAAI,EAAE;MACjD,OAAO,IAAI;IACf;IACA;IACA,MAAMC,QAAQ,GAAI,GAAEN,UAAU,CAACO,QAAS,GAAEP,UAAU,CAACQ,IAAI,IAAI,CAAC,IAAI,CAACf,aAAa,CAACgB,QAAQ,CAACT,UAAU,CAACQ,IAAI,CAAC,GACpG,GAAG,GAAGR,UAAU,CAACQ,IAAI,GACrB,EAAG,EAAC;IACV,OAAQ,IAAI,CAACb,cAAc,CAACe,SAAS,CAAEC,MAAM,IAAK,OAAOA,MAAM,KAAK,QAAQ,GACtEA,MAAM,KAAKL,QAAQ,GACnBK,MAAM,YAAYC,MAAM,GACpBD,MAAM,CAACE,IAAI,CAACP,QAAQ,CAAC,GACrB,KAAK,CAAC,GAAG,CAAC,CAAC;EACzB;EACAQ,iBAAiBA,CAACvC,OAAO,EAAE;IACvB,MAAMwC,YAAY,GAAG,IAAId,GAAG,CAAC1B,OAAO,CAAC2B,GAAG,EAAE,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAACC,MAAM,CAAC;IACxE,OAAQ,IAAI,CAACR,gBAAgB,CAACc,SAAS,CAAEM,KAAK,IAAK;MAC/C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAMC,WAAW,GAAG,IAAIhB,GAAG,CAACe,KAAK,EAAE,IAAI,CAACxB,QAAQ,CAACW,QAAQ,CAACC,MAAM,CAAC;QACjE,OAAQa,WAAW,CAACV,QAAQ,KAAKQ,YAAY,CAACR,QAAQ,IAClDU,WAAW,CAACC,QAAQ,KAAKH,YAAY,CAACG,QAAQ;MACtD;MACA,IAAIF,KAAK,YAAYJ,MAAM,EAAE;QACzB,OAAOI,KAAK,CAACH,IAAI,CAACtC,OAAO,CAAC2B,GAAG,CAAC;MAClC;MACA,OAAO,KAAK;IAChB,CAAC,CAAC,GAAG,CAAC,CAAC;EACX;EACAiB,kBAAkBA,CAACnE,KAAK,EAAEuB,OAAO,EAAE6C,IAAI,EAAE;IACrC,MAAM9C,UAAU,GAAG,IAAI,CAACiB,SAAS,CAAClB,aAAa,CAAC,IAAI,CAACC,UAAU,EAAEC,OAAO,CAAC;IACzE,IAAI,CAACvB,KAAK,IAAI,IAAI,CAAC6C,iBAAiB,EAAE;MAClC,MAAM,IAAInE,KAAK,CAAC,gDAAgD,CAAC;IACrE;IACA,IAAI2F,cAAc,GAAGvG,EAAE,CAAC,KAAK,CAAC;IAC9B,IAAI,IAAI,CAACgF,eAAe,EAAE;MACtBuB,cAAc,GAAGrE,KAAK,GAAGoC,kBAAkB,CAAC,IAAI,CAACG,SAAS,CAACtB,cAAc,CAACjB,KAAK,CAAC,CAAC,GAAGlC,EAAE,CAAC,IAAI,CAAC;IAChG;IACA,IAAIkC,KAAK,EAAE;MACP,OAAOqE,cAAc,CAACC,IAAI,CAAC3G,GAAG,CAAE4G,SAAS,IAAKA,SAAS,IAAI,IAAI,CAACzB,eAAe,GACzEvB,OAAO,CAACiD,KAAK,CAAC,CAAC,GACfjD,OAAO,CAACiD,KAAK,CAAC;QACZC,UAAU,EAAE;UACR,CAAC,IAAI,CAAC/B,UAAU,GAAI,GAAEpB,UAAW,GAAEtB,KAAM;QAC7C;MACJ,CAAC,CAAC,CAAC,EAAEpC,QAAQ,CAAE8G,YAAY,IAAKN,IAAI,CAACO,MAAM,CAACD,YAAY,CAAC,CAAC,CAAC;IACnE;IACA,OAAON,IAAI,CAACO,MAAM,CAACpD,OAAO,CAAC;EAC/B;EACAqD,SAASA,CAACrD,OAAO,EAAE6C,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAACrB,eAAe,CAACxB,OAAO,CAAC,IAAI,IAAI,CAACuC,iBAAiB,CAACvC,OAAO,CAAC,EAAE;MACnE,OAAO6C,IAAI,CAACO,MAAM,CAACpD,OAAO,CAAC;IAC/B;IACA,MAAMvB,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAACmD,OAAO,CAAC;IACvC,OAAOa,kBAAkB,CAACpC,KAAK,CAAC,CAACsE,IAAI,CAAC1G,QAAQ,CAAEiH,UAAU,IAAK;MAC3D,OAAO,IAAI,CAACV,kBAAkB,CAACU,UAAU,EAAEtD,OAAO,EAAE6C,IAAI,CAAC;IAC7D,CAAC,CAAC,CAAC;EACP;AACJ;AACA9B,cAAc,CAACd,IAAI,YAAAsD,uBAAA3E,CAAA;EAAA,YAAAA,CAAA,IAAwFmC,cAAc,EA5FtBnF,EAAE,CAAAuE,QAAA,CA4FsC1D,WAAW,GA5FnDb,EAAE,CAAAuE,QAAA,CA4F8DzD,gBAAgB,GA5FhFd,EAAE,CAAAuE,QAAA,CA4F2FhE,QAAQ;AAAA,CAA6C;AACrP4E,cAAc,CAACX,KAAK,kBA7F+ExE,EAAE,CAAAyE,kBAAA;EAAA5B,KAAA,EA6FUsC,cAAc;EAAAT,OAAA,EAAdS,cAAc,CAAAd;AAAA,EAAG;AAChI;EAAA,QAAAM,SAAA,oBAAAA,SAAA,KA9FmG3E,EAAE,CAAA4E,iBAAA,CA8FVO,cAAc,EAAc,CAAC;IAC5GN,IAAI,EAAE3E;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE2E,IAAI,EAAEC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAE1E,MAAM;QACZ6E,IAAI,EAAE,CAACnE,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEgE,IAAI,EAAE/D;IAAiB,CAAC,EAAE;MAAE+D,IAAI,EAAE+C,QAAQ;MAAE7C,UAAU,EAAE,CAAC;QAC7DF,IAAI,EAAE1E,MAAM;QACZ6E,IAAI,EAAE,CAACzE,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMsH,SAAS,CAAC;EACZ9G,WAAWA,CAAC+G,YAAY,EAAE;IACtB,IAAIA,YAAY,EAAE;MACd,MAAM,IAAIvG,KAAK,CAAE,4FAA2F,CAAC;IACjH;EACJ;EACA,OAAOwG,OAAOA,CAACC,OAAO,EAAE;IACpB,OAAO;MACHC,QAAQ,EAAEJ,SAAS;MACnBK,SAAS,EAAE,CACP;QACIC,OAAO,EAAEvH,iBAAiB;QAC1BwH,QAAQ,EAAEjD,cAAc;QACxBkD,KAAK,EAAE;MACX,CAAC,EACDL,OAAO,CAACM,kBAAkB,IAAI;QAC1BH,OAAO,EAAEtH,WAAW;QACpB0H,QAAQ,EAAEP,OAAO,CAAChH;MACtB,CAAC,EACDF,gBAAgB;IAExB,CAAC;EACL;AACJ;AACA+G,SAAS,CAACxD,IAAI,YAAAmE,kBAAAxF,CAAA;EAAA,YAAAA,CAAA,IAAwF6E,SAAS,EAhIZ7H,EAAE,CAAAuE,QAAA,CAgI4BsD,SAAS;AAAA,CAA2E;AACrNA,SAAS,CAACY,IAAI,kBAjIqFzI,EAAE,CAAA0I,gBAAA;EAAA7D,IAAA,EAiIEgD;AAAS,EAAG;AACnHA,SAAS,CAACc,IAAI,kBAlIqF3I,EAAE,CAAA4I,gBAAA,IAkIc;AACnH;EAAA,QAAAjE,SAAA,oBAAAA,SAAA,KAnImG3E,EAAE,CAAA4E,iBAAA,CAmIViD,SAAS,EAAc,CAAC;IACvGhD,IAAI,EAAEzE;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEyE,IAAI,EAAEgD,SAAS;MAAE9C,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAExE;MACV,CAAC,EAAE;QACCwE,IAAI,EAAEvE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASO,WAAW,EAAEC,gBAAgB,EAAEqE,cAAc,EAAE0C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}