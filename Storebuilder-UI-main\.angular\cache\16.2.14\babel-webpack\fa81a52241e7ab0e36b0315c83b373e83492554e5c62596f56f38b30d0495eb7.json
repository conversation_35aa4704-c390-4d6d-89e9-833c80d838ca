{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-translate/core\";\nconst _c0 = function (a0) {\n  return {\n    width: a0\n  };\n};\nexport class CategoryCardComponent {\n  constructor(router) {\n    this.router = router;\n    this.category = {};\n    this.showAllMobile = false;\n    this.baseUrl = environment.apiEndPoint;\n  }\n  ngOnInit() {\n    /**/\n  }\n  getImage(imageLink) {\n    if (imageLink) {\n      if (imageLink[0] === '/') imageLink = imageLink.substring(1);\n      const sub = imageLink.substring(0, imageLink.indexOf('/'));\n      if (sub.toLowerCase().includes('images')) {\n        return `${this.baseUrl}/${imageLink}`;\n      } else {\n        return `${this.baseUrl}/Images/${imageLink}`;\n      }\n    } else {\n      return '';\n    }\n  }\n  errorHandler(event) {\n    if (environment.isStoreCloud) {\n      event.target.src = \"assets/images/placeholder.png\";\n    } else {\n      event.target.src = \"assets/images/mtn-alt.png\";\n    }\n  }\n  categoriesClick(id) {\n    this.router.navigate(['category', id], {\n      queryParams: {\n        tenantId: localStorage.getItem(\"tenantId\"),\n        lang: localStorage.getItem(\"lang\")\n      },\n      queryParamsHandling: 'merge'\n    });\n  }\n}\nCategoryCardComponent.ɵfac = function CategoryCardComponent_Factory(t) {\n  return new (t || CategoryCardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n};\nCategoryCardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CategoryCardComponent,\n  selectors: [[\"app-mtn-category-card\"]],\n  inputs: {\n    category: \"category\",\n    showAllMobile: \"showAllMobile\"\n  },\n  decls: 7,\n  vars: 11,\n  consts: [[1, \"category-card\", \"shadow-1\", \"cursor-pointer\", \"no-underline\", 3, \"ngStyle\", \"click\"], [3, \"alt\", \"src\", \"error\"], [1, \"title\", \"mt-3\", 3, \"title\"], [1, \"total-items\", \"mt-2\"]],\n  template: function CategoryCardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"a\", 0);\n      i0.ɵɵlistener(\"click\", function CategoryCardComponent_Template_a_click_0_listener() {\n        return ctx.categoriesClick(ctx.category.id);\n      });\n      i0.ɵɵelementStart(1, \"img\", 1);\n      i0.ɵɵlistener(\"error\", function CategoryCardComponent_Template_img_error_1_listener($event) {\n        return ctx.errorHandler($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵtext(5);\n      i0.ɵɵpipe(6, \"translate\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(9, _c0, ctx.showAllMobile === true ? \"105px\" : \"\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"alt\", ctx.category.categoryName)(\"src\", ctx.getImage(ctx.category.image), i0.ɵɵsanitizeUrl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵpropertyInterpolate(\"title\", ctx.category.categoryName);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", ctx.category.categoryName, \" \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate2(\" \", ctx.category.totalProductCount, \" \", i0.ɵɵpipeBind1(6, 7, \"categoryCard.items\"), \" \");\n    }\n  },\n  dependencies: [i2.NgStyle, i3.TranslatePipe],\n  styles: [\".category-card[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 160px;\\n  -webkit-box-shadow: 6px 0 4px -4px rgba(219, 213, 213, 0.631372549), -6px 0 4px -4px rgba(219, 213, 213, 0.631372549) !important;\\n  border-radius: 15px;\\n  opacity: 1;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  max-height: 200px;\\n  background-color: #ffffff;\\n}\\n@media screen and (max-width: 768px) {\\n  .category-card[_ngcontent-%COMP%] {\\n    width: 82px;\\n    height: 109px;\\n    filter: drop-shadow(0px 0px 5px rgba(176, 64, 108, 0.16));\\n    margin-top: 2px;\\n  }\\n}\\n.category-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  object-fit: contain;\\n  padding-top: 12px;\\n}\\n@media screen and (max-width: 768px) {\\n  .category-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n}\\n.category-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-family: var(--medium-font);\\n  color: black;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  line-height: 16px;\\n  word-break: break-word;\\n  height: 30px;\\n}\\n.category-card[_ngcontent-%COMP%]   .total-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  flex-wrap: wrap;\\n  font-size: 14px;\\n  color: var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 10px !important;\\n    font-weight: 500;\\n    font-family: var(--medium-font);\\n    color: black !important;\\n    text-align: center !important;\\n    width: 69px !important;\\n    display: -webkit-box !important;\\n    -webkit-line-clamp: 2 !important;\\n    margin-top: 7px !important;\\n  }\\n  .total-items[_ngcontent-%COMP%] {\\n    font-size: 8px !important;\\n    color: var(--header_bgcolor);\\n  }\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}