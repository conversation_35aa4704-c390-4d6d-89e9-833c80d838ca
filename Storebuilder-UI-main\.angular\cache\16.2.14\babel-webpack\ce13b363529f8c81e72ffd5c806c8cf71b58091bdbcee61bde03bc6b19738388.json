{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { BehaviorSubject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UserService {\n  http;\n  baseUrl;\n  username = \"\";\n  countryISO = \"\";\n  scrollTop = new BehaviorSubject(false);\n  scrollTrigger = this.scrollTop.asObservable();\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/User`;\n  }\n  resetPassword(data) {\n    return this.http.post(`${this.baseUrl}/ResetPassword`, data);\n  }\n  ChangePassword(data) {\n    return this.http.post(`${this.baseUrl}/ChangePassword`, data);\n  }\n  ForgotPassword(data) {\n    return this.http.post(`${this.baseUrl}/ForgotPassword`, data);\n  }\n  UpdatePassword(data) {\n    return this.http.post(`${this.baseUrl}/UpdatePassword`, data);\n  }\n  updateScrollTop(data) {\n    this.scrollTop.next(data);\n  }\n  updateUserConsent(data) {\n    return this.http.post(`${this.baseUrl}/RegisterConsent`, data);\n  }\n  updateAgeConsent(data) {\n    return this.http.post(`${this.baseUrl}/RegisterUserAgeConsent`, data);\n  }\n  static ɵfac = function UserService_Factory(t) {\n    return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UserService,\n    factory: UserService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}