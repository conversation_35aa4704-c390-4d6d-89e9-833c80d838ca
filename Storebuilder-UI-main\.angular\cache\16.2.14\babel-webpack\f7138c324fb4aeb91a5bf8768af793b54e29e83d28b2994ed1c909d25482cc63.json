{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { DOCUMENT, isPlatformBrowser } from \"@angular/common\";\nimport { environment } from \"@environments/environment\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-google-analytics\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-intl-tel-input-gg\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = [\"map\"];\nfunction TenantConfigurationComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵtextInterpolate1(\" \", item_r5 == null ? null : item_r5.name, \" \");\n  }\n}\nfunction TenantConfigurationComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const car_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", car_r6 == null ? null : car_r6.name, \" \");\n  }\n}\nfunction TenantConfigurationComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"p\", 32);\n    i0.ɵɵtext(2, \"No country found\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TenantConfigurationComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function TenantConfigurationComponent_div_33_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const item_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.setTenant(item_r7 == null ? null : item_r7.tenantId, item_r7));\n    })(\"mouseenter\", function TenantConfigurationComponent_div_33_Template_div_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const item_r7 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.country(item_r7));\n    })(\"mouseleave\", function TenantConfigurationComponent_div_33_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.mouseLeave());\n    });\n    i0.ɵɵelement(1, \"img\", 34);\n    i0.ɵɵelementStart(2, \"p\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r4.countryFlagImages(item_r7 == null ? null : item_r7.flag), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7 == null ? null : item_r7.name);\n  }\n}\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nexport class TenantConfigurationComponent {\n  constructor(translate, tenantService, document, appDataService, store, mainDataService, cd, activatedRoute, platformId, languageService, router, $gaService) {\n    this.translate = translate;\n    this.tenantService = tenantService;\n    this.appDataService = appDataService;\n    this.store = store;\n    this.mainDataService = mainDataService;\n    this.cd = cd;\n    this.activatedRoute = activatedRoute;\n    this.platformId = platformId;\n    this.languageService = languageService;\n    this.router = router;\n    this.$gaService = $gaService;\n    this.searchCountry = '';\n    this.allCountries = [];\n    this.highlightedCountryLayer = null;\n    this._BaseURL = environment.apiEndPoint;\n    this.activatedRoute.queryParams.subscribe(queryParams => {\n      if (queryParams?.tenantId) {\n        this.tenantService.setTenant(queryParams?.tenantId);\n      }\n    });\n  }\n  ngOnInit() {\n    this.fetchGoogleTId();\n    if (isPlatformBrowser(this.platformId)) {\n      this.userLang = navigator.language.split(/[-_]/)[0];\n      localStorage.setItem(\"browserLanguage\", this.userLang);\n      this.mainDataService.browserLanguge = localStorage.getItem(\"browserLanguage\");\n      this.translate.use(this.mainDataService.browserLanguge);\n    }\n    //\n    const expiry = localStorage.getItem('apiExpiry');\n    if (expiry && expiry !== '') {\n      if (UtilityFunctions.isTimeExpired(expiry)) {\n        this.fetchCountries();\n      } else {\n        let countries = localStorage.getItem('allCountries');\n        if (countries && countries !== '') {\n          countries = JSON.parse(countries);\n          this.updateCountries(countries);\n        } else {\n          this.fetchCountries();\n        }\n      }\n    } else {\n      this.fetchCountries();\n    }\n    if (isPlatformBrowser(this.platformId)) {\n      import('leaflet').then(L => {\n        this.map = L.map('map', {\n          zoomControl: false\n        }).setView([-30, 50], 3);\n        this.map.scrollWheelZoom.disable();\n        this.map.doubleClickZoom.disable();\n        this.map.dragging.disable();\n        this.map.boxZoom.disable();\n        this.map.keyboard.disable();\n        fetch('./assets/africa.geojson').then(response => response.json()).then(data => {\n          const africanCountries = data.features.filter(feature => feature.properties.continent === 'Africa');\n          L.geoJSON(africanCountries, {\n            style: {\n              fillColor: '#FFCB05',\n              fillOpacity: 1,\n              color: 'grey',\n              weight: 2\n            }\n          }).addTo(this.map).on('add remove', layer => {\n            this.countryLayer = layer.target;\n          });\n        });\n        this.map.on('mousedown', function (e) {});\n      });\n    }\n    this.userDetails = this.store.get('profile');\n  }\n  changeLang(lang) {\n    //\n    localStorage.setItem(\"browserLanguage\", lang?.value.split(/[-_]/)[0]);\n    this.mainDataService.browserLanguge = localStorage.getItem(\"browserLanguage\");\n    this.translate.use(lang?.value);\n  }\n  searchCountries(event) {\n    if (this.country.length >= 1) {\n      const normalizedCountry = this.removeDiacritics(this.searchCountry.toLowerCase());\n      this.countriesList = this.allCountries.filter(item => this.removeDiacritics(item.name.toLowerCase()).includes(normalizedCountry));\n    } else if (this.country.length === 0) {\n      this.countriesList = this.allCountries;\n    }\n  }\n  removeDiacritics(text) {\n    return text.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n  }\n  resetHighlight() {\n    if (this.highlightedCountryLayer) {\n      this.countryLayer.resetStyle(this.highlightedCountryLayer);\n      this.highlightedCountryLayer = null;\n    }\n  }\n  country(item) {\n    fetch('./assets/africa.geojson').then(response => response.json()).then(data => {\n      const country = data.features.filter(feature => feature.properties.iso_a2 === item.isoCode);\n      const otherCountries = data.features.filter(feature => feature.properties.iso_a2 !== item.isoCode);\n      this.fillColor(country, '#204E6E');\n      this.fillColor(otherCountries, '#FFCB05');\n    });\n  }\n  fillColor(countries, color) {\n    if (isPlatformBrowser(this.platformId)) {\n      import('leaflet').then(L => {\n        L.geoJSON(countries, {\n          style: {\n            fillColor: color,\n            fillOpacity: 1,\n            color: 'grey',\n            weight: 2\n          }\n        }).addTo(this.map).on('add remove', layer => {\n          this.countryLayer = layer.target;\n        });\n      });\n    }\n  }\n  mouseLeave() {\n    if (isPlatformBrowser(this.platformId)) {\n      import('leaflet').then(L => {\n        fetch('./assets/africa.geojson').then(response => response.json()).then(data => {\n          const otherCountries = data.features.filter(feature => feature.properties.continent === 'Africa');\n          L.geoJSON(otherCountries, {\n            style: {\n              fillColor: \"#FFCB05\",\n              fillOpacity: 1,\n              color: 'grey',\n              weight: 2\n            }\n          }).addTo(this.map).on('add remove', layer => {\n            this.countryLayer = layer.target;\n          });\n        });\n      });\n    }\n  }\n  setTenant(tenantId, country) {\n    localStorage.setItem('tenantId', tenantId);\n    let defaultLanguage = country.languages.filter(lang => lang.isDefault);\n    this.languageService.setCurrentLang(defaultLanguage[0].code);\n    this.router.navigate(['/']).then(() => {\n      if (isPlatformBrowser(this.platformId)) {\n        window.location.reload();\n      }\n    });\n  }\n  countryFlagImages(img) {\n    return UtilityFunctions.verifyImageURL(img, this._BaseURL);\n  }\n  navigateToSellerHub() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE, '', 'MARKETPLACE_SELLER', 1, true, {\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n      window.open(environment.merchantURL + '?tenantId=' + this.countriesList[0].tenantId, '_blank');\n    }\n  }\n  fetchCountries() {\n    this.mainDataService.getAllCountryTenants().subscribe(res => {\n      if (res?.data?.records) {\n        localStorage.setItem('allCountries', JSON.stringify(res.data.records));\n        this.updateCountries(res.data.records);\n      }\n    });\n  }\n  fetchGoogleTId() {\n    this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\n      next: res => {\n        localStorage.setItem('GATrackingId', res.data ? res.data[0].value : '');\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  updateCountries(countries) {\n    this.countriesList = countries;\n    this.allCountries = JSON.parse(JSON.stringify(this.countriesList));\n    this.uniqueLanguagesArray = [];\n    this.countriesList.forEach(record => {\n      record.languages.forEach(language => {\n        const existingLanguage = this.uniqueLanguagesArray.find(item => item.name === language.name);\n        if (!existingLanguage) {\n          this.uniqueLanguagesArray.push({\n            name: language.name,\n            code: language.code\n          });\n        }\n      });\n    });\n    this.cd.markForCheck();\n    this.cd.detectChanges();\n    this.uniqueLanguagesArray.forEach(item => {\n      if (this.userLang === item.code) {\n        this.selectedLang = item.code;\n      }\n    });\n  }\n}\nTenantConfigurationComponent.ɵfac = function TenantConfigurationComponent_Factory(t) {\n  return new (t || TenantConfigurationComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.TenantService), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i2.AppDataService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.LanguageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.GoogleAnalyticsService));\n};\nTenantConfigurationComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TenantConfigurationComponent,\n  selectors: [[\"app-mtn-tenant-configuration\"]],\n  viewQuery: function TenantConfigurationComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mapElement = _t.first);\n    }\n  },\n  decls: 37,\n  vars: 16,\n  consts: [[1, \"image-bg-tenant\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"mt-4\", \"me-2\", \"sell-on-marketplace\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/marketplace-landing.svg\"], [1, \"language-dropdown\"], [1, \"icon\"], [\"alt\", \"globe\", \"src\", \"assets/icons/globe.svg\"], [\"optionLabel\", \"name\", \"optionValue\", \"code\", 1, \"language-tenant\", 3, \"ngModel\", \"options\", \"onChange\", \"ngModelChange\"], [\"dd4\", \"\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [1, \"d-flex\", \"mobile-display\"], [1, \"col-2\", \"scale-150\"], [1, \"col-4\", \"d-contents\"], [1, \"box-column\"], [1, \"content\"], [\"alt\", \"Logo\", \"src\", \"assets/images/logo-marketplace1.svg\", 1, \"mb-3\"], [1, \"mb-1\", \"select-country\", \"font-normal-mtn\"], [1, \"country-desp\", \"mb-3\", \"font-normal-mtn\"], [1, \"product-search\", \"p-inputgroup\", \"mb-3\"], [1, \"p-inputgroup-addon\", \"color\"], [1, \"pi\", \"pi-search\"], [\"autocomplete\", \"off\", \"novalidate\", \"\", 1, \"ng-untouched\", \"ng-pristine\", \"ng-valid\", 2, \"display\", \"contents\"], [\"autocapitalize\", \"off\", \"autocomplete\", \"new-password\", \"autocorrect\", \"off\", \"pinputtext\", \"\", \"spellcheck\", \"false\", \"type\", \"text\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"ng-untouched\", \"ng-pristine\", \"ng-valid\", \"font-normal-mtn\", 3, \"ngModel\", \"ngModelOptions\", \"placeholder\", \"input\", \"ngModelChange\"], [\"class\", \"col-12 text-center\", 4, \"ngIf\"], [1, \"line-border\"], [1, \"row\", \"countries-scoll\", \"justify-content-center\"], [\"class\", \"col-3 text-center mobile-show-countries\", 3, \"click\", \"mouseenter\", \"mouseleave\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-5\", \"mobile-display-none\"], [\"id\", \"map\"], [1, \"col-1\", \"scale-150-none\"], [1, \"col-12\", \"text-center\"], [1, \"no-found\"], [1, \"col-3\", \"text-center\", \"mobile-show-countries\", 3, \"click\", \"mouseenter\", \"mouseleave\"], [\"alt\", \"No Image\", \"alt\", \"No Image\", 1, \"country-flag\", 3, \"src\"], [1, \"country-name\", \"font-normal-mtn\"]],\n  template: function TenantConfigurationComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"button\", 2);\n      i0.ɵɵlistener(\"click\", function TenantConfigurationComponent_Template_button_click_2_listener() {\n        return ctx.navigateToSellerHub();\n      });\n      i0.ɵɵelement(3, \"img\", 3);\n      i0.ɵɵtext(4, \" Sell on marketplace \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n      i0.ɵɵelement(7, \"img\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"p-dropdown\", 7, 8);\n      i0.ɵɵlistener(\"onChange\", function TenantConfigurationComponent_Template_p_dropdown_onChange_8_listener($event) {\n        return ctx.changeLang($event);\n      })(\"ngModelChange\", function TenantConfigurationComponent_Template_p_dropdown_ngModelChange_8_listener($event) {\n        return ctx.selectedLang = $event;\n      });\n      i0.ɵɵtemplate(10, TenantConfigurationComponent_ng_template_10_Template, 1, 1, \"ng-template\", 9);\n      i0.ɵɵtemplate(11, TenantConfigurationComponent_ng_template_11_Template, 2, 1, \"ng-template\", 10);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(12, \"div\", 11);\n      i0.ɵɵelement(13, \"div\", 12);\n      i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15);\n      i0.ɵɵelement(17, \"img\", 16);\n      i0.ɵɵelementStart(18, \"h2\", 17);\n      i0.ɵɵtext(19);\n      i0.ɵɵpipe(20, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"p\", 18);\n      i0.ɵɵtext(22);\n      i0.ɵɵpipe(23, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 19)(25, \"span\", 20);\n      i0.ɵɵelement(26, \"em\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"form\", 22)(28, \"input\", 23);\n      i0.ɵɵlistener(\"input\", function TenantConfigurationComponent_Template_input_input_28_listener($event) {\n        return ctx.searchCountries($event);\n      })(\"ngModelChange\", function TenantConfigurationComponent_Template_input_ngModelChange_28_listener($event) {\n        return ctx.searchCountry = $event;\n      });\n      i0.ɵɵpipe(29, \"translate\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(30, TenantConfigurationComponent_div_30_Template, 3, 0, \"div\", 24);\n      i0.ɵɵelement(31, \"span\", 25);\n      i0.ɵɵelementStart(32, \"div\", 26);\n      i0.ɵɵtemplate(33, TenantConfigurationComponent_div_33_Template, 4, 2, \"div\", 27);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(34, \"div\", 28);\n      i0.ɵɵelement(35, \"div\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(36, \"div\", 30);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngModel\", ctx.selectedLang)(\"options\", ctx.uniqueLanguagesArray);\n      i0.ɵɵadvance(11);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 9, \"landingPage.selectCountry\"), \"\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 11, \"landingPage.clickOnCountry\"), \" \");\n      i0.ɵɵadvance(6);\n      i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(29, 13, \"landingPage.searchCountry\"));\n      i0.ɵɵproperty(\"ngModel\", ctx.searchCountry)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c1));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", (ctx.countriesList == null ? null : ctx.countriesList.length) === 0);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.countriesList);\n    }\n  },\n  dependencies: [i5.PrimeTemplate, i6.Dropdown, i7.NgForOf, i7.NgIf, i8.NativeElementInjectorDirective, i9.ɵNgNoValidate, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgControlStatusGroup, i9.NgModel, i9.NgForm, i1.TranslatePipe],\n  styles: [\".language-dropdown[_ngcontent-%COMP%]     .p-dropdown {\\n  border: none;\\n  background: transparent;\\n}\\n\\n.language-dropdown[_ngcontent-%COMP%]     .p-dropdown .p-dropdown-label {\\n  color: #FFF;\\n  font-family: \\\"main-regular\\\", sans-serif !important;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n\\n.language-dropdown[_ngcontent-%COMP%]     .p-icon {\\n  color: #FFF;\\n}\\n\\n.language-dropdown[_ngcontent-%COMP%]     .p-dropdown:not(.p-disabled).p-focus {\\n  box-shadow: none;\\n}\\n\\n.language-dropdown[_ngcontent-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin: 3px;\\n  border-bottom: 1px solid #E3F2FD;\\n  font-family: \\\"main-regular\\\", sans-serif !important;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  color: #004F71 !important;\\n}\\n\\n.language-dropdown[_ngcontent-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {\\n  background: none;\\n}\\n\\n.image-bg-tenant[_ngcontent-%COMP%] {\\n  background-image: url('background.png');\\n  background-repeat: no-repeat;\\n  background-size: 100% 100%;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.box-column[_ngcontent-%COMP%] {\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  flex-basis: 32%;\\n  padding: 20px;\\n  box-sizing: border-box;\\n  border-radius: 8px;\\n  border-bottom: 5px solid var(--custom-secondary, #ffcb05);\\n  background: #fff;\\n  \\n\\n  box-shadow: 0px 11px 14px 0px rgba(0, 0, 0, 0.2);\\n  margin: auto 0;\\n}\\n\\n.select-country[_ngcontent-%COMP%] {\\n  font-size: 22px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.map-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n  height: 100vh;\\n}\\n.map-container[_ngcontent-%COMP%]   .country-desp[_ngcontent-%COMP%] {\\n  color: #a3a3a3;\\n  font-size: 14px;\\n  font-family: var(--regular-font) !important;\\n}\\n.map-container[_ngcontent-%COMP%]   .p-inputgroup-addon[_ngcontent-%COMP%]:first-child {\\n  border-top-left-radius: 5px !important;\\n  border-bottom-left-radius: 5px !important;\\n  border: 1px solid #000000 !important;\\n  background: none;\\n  border-right: none !important;\\n  opacity: 0.6;\\n}\\n.map-container[_ngcontent-%COMP%]   .product-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059) !important;\\n  border: 1px solid #000000 !important;\\n  padding: 0 0px;\\n  font-size: 13px;\\n  font-family: var(--medium-font) !important;\\n  border-left: none !important;\\n  border-top-left-radius: 0px !important;\\n  border-bottom-left-radius: 0px !important;\\n  height: 47px;\\n  opacity: 0.6;\\n}\\n.map-container[_ngcontent-%COMP%]   .border-flag[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(0, 0, 0, 0.2);\\n}\\n.map-container[_ngcontent-%COMP%]   .column-2[_ngcontent-%COMP%] {\\n  flex-basis: 32%;\\n  padding: 20px;\\n}\\n\\n.country-flag[_ngcontent-%COMP%] {\\n  border-radius: 35px;\\n  width: 75px;\\n}\\n\\n.country-flag[_ngcontent-%COMP%]:hover {\\n  border: 5px solid #1d4c69;\\n  cursor: pointer;\\n}\\n\\n.country-name[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  color: #000;\\n  text-align: center;\\n  text-overflow: ellipsis;\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.no-found[_ngcontent-%COMP%] {\\n  font-size: 19px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n#map[_ngcontent-%COMP%] {\\n  width: 100vh !important;\\n  height: 100vh !important;\\n  margin: auto 0;\\n  outline: none;\\n}\\n\\n#map[_ngcontent-%COMP%]:hover {\\n  color: red;\\n}\\n\\n.geojson-layer[_ngcontent-%COMP%] {\\n  fill: transparent;\\n  \\n\\n}\\n\\n.geojson-layer[_ngcontent-%COMP%]:hover {\\n  stroke-width: 2px;\\n  \\n\\n}\\n\\n  path.leaflet-interactive:hover {\\n  stroke: grey;\\n  opacity: 1;\\n  fill: #204E6E;\\n}\\n\\n.leaflet-container[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 0, 0, 0);\\n}\\n\\n  .leaflet-control-attribution {\\n  display: none !important;\\n}\\n\\n.line-border[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 15px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.2);\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .box-column[_ngcontent-%COMP%] {\\n    flex-basis: 100% !important;\\n  }\\n  .column-2[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-show-countries[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n    padding: 0.5rem;\\n    width: 50%;\\n  }\\n  .country-desp[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n  }\\n  .map-container[_ngcontent-%COMP%]   .box-column[_ngcontent-%COMP%] {\\n    margin: auto 25px;\\n  }\\n}\\n.language-dropdown[_ngcontent-%COMP%] {\\n  float: right;\\n  width: 130px;\\n  margin: 20px 20px 0px 0px;\\n  font-family: var(--medium-font) !important;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  background-color: transparent;\\n  border: none;\\n  font-size: 14px;\\n  position: relative;\\n  \\n\\n}\\n\\n.icon[_ngcontent-%COMP%], .arrow-icon[_ngcontent-%COMP%] {\\n  padding: 5px;\\n}\\n\\nselect[_ngcontent-%COMP%] {\\n  color: white;\\n  border: none;\\n  background-color: transparent;\\n  -webkit-appearance: auto;\\n  outline: none;\\n  padding-right: 20px;\\n  \\n\\n}\\nselect[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  color: #1f425f;\\n}\\n\\nselect[_ngcontent-%COMP%]:after {\\n  height: 167px;\\n  width: 250px;\\n  color: red;\\n}\\n\\n.arrow-icon[_ngcontent-%COMP%] {\\n  right: 10px;\\n  top: 2px;\\n  position: absolute;\\n}\\n\\n  .leaflet-container .leaflet-overlay-pane svg {\\n  zoom: 1.5;\\n  transform: translate3d(-95px, -55px, 0px) !important;\\n}\\n\\n@media (min-resolution: 100dpi) and (max-resolution: 120dpi) {\\n    .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 1.2 !important;\\n    margin-top: 80px;\\n    margin-left: 30px;\\n  }\\n}\\n@media (min-resolution: 120dpi) and (max-resolution: 130dpi) {\\n  [_nghost-%COMP%]     .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 1.2 !important;\\n    margin-top: 90px;\\n    margin-left: 80px;\\n  }\\n}\\n@media (min-resolution: 144dpi) {\\n    .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 0.8 !important;\\n    margin-top: 150px;\\n    margin-left: 75px;\\n  }\\n  #map[_ngcontent-%COMP%] {\\n    margin: auto 0;\\n    outline: none;\\n    width: 120vh !important;\\n    height: 120vh !important;\\n  }\\n  .box-column[_ngcontent-%COMP%] {\\n    margin: 120px 0 !important;\\n  }\\n  .scale-150[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    width: 25% !important;\\n  }\\n  .scale-150-none[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    width: 0% !important;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .mobile-display[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .mobile-display-none[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .box-column[_ngcontent-%COMP%] {\\n    margin: 20px 20px !important;\\n    position: absolute;\\n    top: 18%;\\n    left: 0;\\n  }\\n  .countries-scoll[_ngcontent-%COMP%] {\\n    justify-content: left !important;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 950px) {\\n  .mobile-display[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .col-5.mobile-display-none[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .col-1.scale-150-none[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .col-2.scale-150[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .box-column[_ngcontent-%COMP%] {\\n    margin: 20px 20px !important;\\n    position: absolute;\\n    top: 30%;\\n    left: 0;\\n    width: 85%;\\n    margin-left: 72px !important;\\n  }\\n  .countries-scoll[_ngcontent-%COMP%] {\\n    justify-content: center !important;\\n  }\\n}\\n.p-inputtext[_ngcontent-%COMP%] {\\n  width: 100%;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059) !important;\\n  border: 1px solid #bfbaba !important;\\n  padding: 0;\\n  font-size: 13px;\\n  font-family: var(--medium-font) !important;\\n  border-left: none !important;\\n  border-top-left-radius: 0 !important;\\n  border-bottom-left-radius: 0 !important;\\n  height: 47px;\\n  opacity: 0.6;\\n}\\n\\n  .p-inputgroup-addon {\\n  background: none;\\n}\\n\\n.font-normal-mtn[_ngcontent-%COMP%] {\\n  font-family: \\\"main-regular\\\", sans-serif !important;\\n}\\n\\n  select option {\\n  padding: 20px;\\n}\\n\\n.sell-on-marketplace[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 8px;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 10px;\\n  border-radius: 6px;\\n  background: #FFCB05;\\n  color: #204E6E;\\n  font-family: var(--regular-font) !important;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  border: none;\\n}\\n\\n\\n\\n@media only screen and (min-device-width: 720px) and (max-device-width: 1280px) {\\n  \\n\\n    .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 0.8 !important;\\n    margin-top: 220px;\\n    margin-left: 150px;\\n  }\\n}\\n\\n\\n@media screen and (max-width: 1366px) {\\n    .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 0.8 !important;\\n    margin-top: 220px;\\n    margin-left: 150px;\\n  }\\n  \\n\\n}\\n@media screen and (min-width: 1000px) and (max-width: 1250px) {\\n    .leaflet-container .leaflet-overlay-pane svg {\\n    zoom: 0.8 !important;\\n    margin-top: 0px !important;\\n    margin-left: 0px !important;\\n  }\\n  \\n\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}