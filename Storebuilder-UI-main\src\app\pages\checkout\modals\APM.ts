export interface InitiateApmWidgetResponse {
  Token: string;
  Amount: string;
  Currency: string;
  Reference: string;
  Timestamp: string;
  Signature: string;
  MerchantId: string;
  env: String;
}

export interface InitiateApmWidgetRequest {
  Amount: string;
  Currency: string;
  OrderId: string;
}

export interface APMPaymentSDKConfig {
  container: string;
  token?: string;
  amount: number;
  reference: string;
  merchantId: string;
  timestamp: string;
  signature: string;
  currency: string;
  env: String;

  onPaymentUpdate?: (status: 'Success' | 'Failed' | string) => void;
  onError?: (error: any) => void;
  onFormValidation?: (errors: any[]) => void;
}

export interface APMPaymentSDK {
  initialize(): void;
}
