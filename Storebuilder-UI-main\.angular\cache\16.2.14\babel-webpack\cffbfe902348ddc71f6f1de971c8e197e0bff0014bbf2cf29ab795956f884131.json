{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { Subject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/store.service\";\nimport * as i2 from \"@core/services/token.service\";\nimport * as i3 from \"ngx-cookie-service\";\nexport let CommonService = /*#__PURE__*/(() => {\n  class CommonService {\n    store;\n    authTokenService;\n    platformId;\n    cookieService;\n    isShowSearchBox = new Subject();\n    constructor(store, authTokenService, platformId, cookieService) {\n      this.store = store;\n      this.authTokenService = authTokenService;\n      this.platformId = platformId;\n      this.cookieService = cookieService;\n    }\n    logOut() {\n      this.setStoreData();\n      this.authTokenService.authTokenSet('');\n      if (isPlatformBrowser(this.platformId)) {\n        this.cookieService.delete('authToken', window.location.hostname);\n      }\n      this.store.set('cartProducts', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n      sessionStorage.clear();\n      this.store.set('profile', '');\n      this.store.set('cartProducts', '');\n      localStorage.setItem('sessionId', '');\n      localStorage.setItem('addedProducts', '');\n    }\n    setStoreData() {\n      if (this.store.localStoreNames.length) {\n        this.store.set('refreshToken', null);\n        this.store.set('profile', null);\n        this.store.set('cartProducts', []);\n        this.store.set('favouritesProducts', []);\n        this.store.set('compareProducts', []);\n        this.store.set('socialAccount', null);\n        this.store.set('XXSRFTOKEN', null);\n        this.store.set('notifications', {\n          notifications: [],\n          unreadNotifications: 0\n        });\n        this.store.set('checkoutData', {\n          shipping: null,\n          payment: null,\n          promo: null,\n          steps: null,\n          profile: null,\n          orderId: null\n        });\n      } else {\n        localStorage.setItem('timeInterval', '');\n        localStorage.setItem('TenantId', '');\n        localStorage.setItem('userPhone', '');\n        localStorage.setItem('profile', '');\n        localStorage.setItem('cartProducts', JSON.stringify([]));\n        localStorage.setItem('favouritesProducts', JSON.stringify([]));\n        localStorage.setItem('compareProducts', JSON.stringify([]));\n        localStorage.setItem('XXSRFTOKEN', '');\n        localStorage.removeItem('refreshToken');\n        localStorage.removeItem('auth_enc');\n      }\n    }\n    static ɵfac = function CommonService_Factory(t) {\n      return new (t || CommonService)(i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i2.AuthTokenService), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i3.CookieService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CommonService,\n      factory: CommonService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return CommonService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}