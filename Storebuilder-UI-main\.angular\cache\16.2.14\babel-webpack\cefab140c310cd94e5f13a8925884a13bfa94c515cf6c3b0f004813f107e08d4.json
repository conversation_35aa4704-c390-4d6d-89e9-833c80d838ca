{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { JwtHelperService } from '@auth0/angular-jwt';\nimport { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-cookie-service\";\nimport * as i5 from \"@angular/common/http\";\nexport class ErrorInterceptor {\n  constructor(store, messageService, router, authService, cookieService, authTokenService, http) {\n    this.store = store;\n    this.messageService = messageService;\n    this.router = router;\n    this.authService = authService;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.http = http;\n    this.isRefreshTokenCalled = false;\n    this.helper = new JwtHelperService();\n    this.refreshSuccessful = false;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(err => {\n      if (err.status === 401) {\n        this.userToken = this.cookieService.get('authToken');\n        this.signOut();\n      }\n      const error = err?.error?.message || err.statusText;\n      return throwError(error);\n    }));\n  }\n  signOut() {\n    const tenantId = localStorage.getItem('tenantId') ?? environment.defaultTenant;\n    const maintenanceMode = localStorage.getItem('maintenanceMode') ?? 'false';\n    const saveCookie = localStorage.getItem('save_cookie') ?? '';\n    const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant');\n    localStorage.clear();\n    localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '');\n    this.setStoreData();\n    this.authTokenService.authTokenSet(\"\");\n    this.cookieService.delete('authToken', '/');\n    this.store.set('cartProducts', []);\n    localStorage.setItem('secondaryDefault', 'false');\n    alert('ok2');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    localStorage.setItem('tenantId', tenantId);\n    localStorage.setItem('maintenanceMode', maintenanceMode);\n    localStorage.setItem('save_cookie', saveCookie);\n    this.router.navigate(['/login']);\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      localStorage.setItem('refreshToken', '');\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.setItem('auth_enc', '');\n    }\n  }\n  static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n    return new (t || ErrorInterceptor)(i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i3.Router), i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i4.CookieService), i0.ɵɵinject(i1.AuthTokenService), i0.ɵɵinject(i5.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ErrorInterceptor,\n    factory: ErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "JwtHelperService", "environment", "ErrorInterceptor", "constructor", "store", "messageService", "router", "authService", "cookieService", "authTokenService", "http", "isRefreshTokenCalled", "helper", "refreshSuccessful", "intercept", "request", "next", "handle", "pipe", "err", "status", "userToken", "get", "signOut", "error", "message", "statusText", "tenantId", "localStorage", "getItem", "defaultTenant", "maintenanceMode", "save<PERSON><PERSON><PERSON>", "localMaintenanceTenant", "clear", "setItem", "setStoreData", "authTokenSet", "delete", "set", "alert", "removeItem", "navigate", "localStoreNames", "length", "notifications", "unreadNotifications", "shipping", "payment", "promo", "steps", "profile", "orderId", "JSON", "stringify", "_", "i0", "ɵɵinject", "i1", "StoreService", "i2", "MessageService", "i3", "Router", "AuthService", "i4", "CookieService", "AuthTokenService", "i5", "HttpClient", "_2", "factory", "ɵfac"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interceptors\\error.interceptor.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {HttpClient, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';\r\nimport {Observable, throwError} from 'rxjs';\r\nimport {catchError} from 'rxjs/operators';\r\nimport {Router} from '@angular/router';\r\nimport {MessageService} from 'primeng/api';\r\nimport {CookieService} from 'ngx-cookie-service';\r\nimport {JwtHelperService} from '@auth0/angular-jwt';\r\n\r\nimport {StoreService, AuthTokenService, AuthService} from '../services';\r\nimport {environment} from \"@environments/environment\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\n\r\n@Injectable()\r\nexport class ErrorInterceptor implements HttpInterceptor {\r\n  isRefreshTokenCalled: boolean = false;\r\n  decoded: any;\r\n  helper = new JwtHelperService();\r\n  refreshedUrl: any;\r\n  userToken: any;\r\n  refreshSuccessful = false;\r\n\r\n  constructor(private store: StoreService, private messageService: MessageService, private router: Router, private authService: AuthService,\r\n              private cookieService: CookieService,\r\n              private authTokenService: AuthTokenService,\r\n              private http: HttpClient) {\r\n  }\r\n\r\n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n\r\n    return next.handle(request).pipe(catchError(err => {\r\n      if (err.status === 401) {\r\n\r\n\r\n        this.userToken = this.cookieService.get('authToken');\r\n\r\n        this.signOut()\r\n      }\r\n      const error = err?.error?.message || err.statusText;\r\n      return throwError(error);\r\n    }));\r\n  }\r\n\r\n  signOut(): void {\r\n    const tenantId = localStorage.getItem('tenantId') ?? environment.defaultTenant\r\n    const maintenanceMode = localStorage.getItem('maintenanceMode') ?? 'false'\r\n    const saveCookie = localStorage.getItem('save_cookie') ?? ''\r\n    const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant')\r\n    localStorage.clear();\r\n    localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '')\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet(\"\");\r\n\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('cartProducts', []);\r\n    localStorage.setItem('secondaryDefault', 'false')\r\n    alert('ok2');\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    localStorage.setItem('cartId', '');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    localStorage.setItem('tenantId', tenantId);\r\n    localStorage.setItem('maintenanceMode', maintenanceMode);\r\n    localStorage.setItem('save_cookie', saveCookie);\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null\r\n      });\r\n    } else {\r\n\r\n      localStorage.setItem('refreshToken', '');\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n      localStorage.setItem('auth_enc', '');\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n"], "mappings": "AAEA,SAAoBA,UAAU,QAAO,MAAM;AAC3C,SAAQC,UAAU,QAAO,gBAAgB;AAIzC,SAAQC,gBAAgB,QAAO,oBAAoB;AAGnD,SAAQC,WAAW,QAAO,2BAA2B;;;;;;;AAIrD,OAAM,MAAOC,gBAAgB;EAQ3BC,YAAoBC,KAAmB,EAAUC,cAA8B,EAAUC,MAAc,EAAUC,WAAwB,EACrHC,aAA4B,EAC5BC,gBAAkC,EAClCC,IAAgB;IAHhB,KAAAN,KAAK,GAALA,KAAK;IAAwB,KAAAC,cAAc,GAAdA,cAAc;IAA0B,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,WAAW,GAAXA,WAAW;IACxG,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,IAAI,GAAJA,IAAI;IAVxB,KAAAC,oBAAoB,GAAY,KAAK;IAErC,KAAAC,MAAM,GAAG,IAAIZ,gBAAgB,EAAE;IAG/B,KAAAa,iBAAiB,GAAG,KAAK;EAMzB;EAEAC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAEpD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAACnB,UAAU,CAACoB,GAAG,IAAG;MAChD,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;QAGtB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACb,aAAa,CAACc,GAAG,CAAC,WAAW,CAAC;QAEpD,IAAI,CAACC,OAAO,EAAE;;MAEhB,MAAMC,KAAK,GAAGL,GAAG,EAAEK,KAAK,EAAEC,OAAO,IAAIN,GAAG,CAACO,UAAU;MACnD,OAAO5B,UAAU,CAAC0B,KAAK,CAAC;IAC1B,CAAC,CAAC,CAAC;EACL;EAEAD,OAAOA,CAAA;IACL,MAAMI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI5B,WAAW,CAAC6B,aAAa;IAC9E,MAAMC,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO;IAC1E,MAAMG,UAAU,GAAGJ,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;IAC5D,MAAMI,sBAAsB,GAAGL,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IAC5ED,YAAY,CAACM,KAAK,EAAE;IACpBN,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEF,sBAAsB,IAAI,EAAE,CAAC;IAC3E,IAAI,CAACG,YAAY,EAAE;IACnB,IAAI,CAAC3B,gBAAgB,CAAC4B,YAAY,CAAC,EAAE,CAAC;IAEtC,IAAI,CAAC7B,aAAa,CAAC8B,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAC3C,IAAI,CAAClC,KAAK,CAACmC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCX,YAAY,CAACO,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC;IACjDK,KAAK,CAAC,KAAK,CAAC;IACZZ,YAAY,CAACO,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCP,YAAY,CAACO,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzCP,YAAY,CAACO,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAClCP,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;IACvCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;IACnCb,YAAY,CAACO,OAAO,CAAC,UAAU,EAAER,QAAQ,CAAC;IAC1CC,YAAY,CAACO,OAAO,CAAC,iBAAiB,EAAEJ,eAAe,CAAC;IACxDH,YAAY,CAACO,OAAO,CAAC,aAAa,EAAEH,UAAU,CAAC;IAC/C,IAAI,CAAC1B,MAAM,CAACoC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAN,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChC,KAAK,CAACuC,eAAe,CAACC,MAAM,EAAE;MAErC,IAAI,CAACxC,KAAK,CAACmC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAACnC,KAAK,CAACmC,GAAG,CAAC,eAAe,EAAE;QAC9BM,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAAC1C,KAAK,CAACmC,GAAG,CAAC,cAAc,EAAE;QAC7BQ,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MAELxB,YAAY,CAACO,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCP,YAAY,CAACO,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCP,YAAY,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCP,YAAY,CAACO,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCP,YAAY,CAACO,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCP,YAAY,CAACO,OAAO,CAAC,cAAc,EAAEkB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MACxD1B,YAAY,CAACO,OAAO,CAAC,oBAAoB,EAAEkB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9D1B,YAAY,CAACO,OAAO,CAAC,iBAAiB,EAAEkB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3D1B,YAAY,CAACO,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MACtCP,YAAY,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;;EAExC;EAAC,QAAAoB,CAAA,G;qBAzFUrD,gBAAgB,EAAAsD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAM,WAAA,GAAAR,EAAA,CAAAC,QAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAAV,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAS,gBAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBpE,gBAAgB;IAAAqE,OAAA,EAAhBrE,gBAAgB,CAAAsE;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}