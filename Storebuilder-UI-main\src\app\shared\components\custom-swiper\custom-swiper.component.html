<div class="carousel-wrapper" 
     (touchstart)="onTouchStart($event)" 
     (touchmove)="onTouchMove($event)" 
     (touchend)="onTouchEnd()">
  <div class="carousel-track" [style.transform]="'translateX(-' + (activeIndex * 100) + '%)'">
    <div class="carousel-slide" *ngFor="let item of items; trackBy: trackByIndex" [style.width.%]="100">
      <ng-container *ngTemplateOutlet="carouselItemTemplate; context: { $implicit: item }"></ng-container>
    </div>
  </div>

  <!-- Arrows -->
  <button *ngIf="showArrows" class="arrow left" (click)="prevSlide()">&#8249;</button>
  <button *ngIf="showArrows" class="arrow right" (click)="nextSlide()">&#8250;</button>

  <!-- Dots -->
  <div *ngIf="showDots" class="dots">
    <span *ngFor="let dot of numberOfDots; let i = index" 
          (click)="goToSlide(i)" 
          [class.active]="i === activeIndex">
    </span>
  </div>
</div>