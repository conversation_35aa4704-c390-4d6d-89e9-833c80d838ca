{"ast": null, "code": "import { __decorate, __param } from \"tslib\";\nimport { Component, HostListener, Inject, Input, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport UtilityFunctions from '@core/utilities/functions';\nlet IndexComponent = class IndexComponent {\n  onScroll(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      const scrollPosition = window.scrollY || document.documentElement.scrollTop;\n      const totalHeight = document.documentElement.scrollHeight;\n      const windowHeight = window.innerHeight;\n      const isAtBottom = scrollPosition + windowHeight >= totalHeight - 1;\n      if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {\n        this.loadOnScrollData();\n      }\n    }\n  }\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n    if (this.screenWidth <= 768) {\n      this.isMobileView = true;\n    } else {\n      this.isMobileView = false;\n    }\n  }\n  loadOnScrollData() {\n    if (this.loadDataType === 'category') {\n      if (this.shouldCallNextCategoryProduct) this.loadPaginatedProducts();\n    } else if (this.loadDataType === 'feature') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedFeatureProducts();\n    } else if (this.loadDataType === 'promotion') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedPromotionProducts();\n    }\n  }\n  constructor(activatedRoute, productService, store, reviewsService, messageService, translate, ref, loaderService, tenantService, authTokenService, cookieService, mainDataService, router, appDataService, permissionService, $gaService, platformId, $gtmService) {\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.store = store;\n    this.reviewsService = reviewsService;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.tenantService = tenantService;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.mainDataService = mainDataService;\n    this.router = router;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.$gtmService = $gtmService;\n    this.items = [];\n    this.breadItems = [];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.currency = {};\n    this.baseUrl = environment.apiEndPoint + '/';\n    this.emptyMsg = 'Your Category Is Empty';\n    this.catIds = '';\n    this.catPaths = '';\n    this.isError = false;\n    this.isBlank = true;\n    this.badgesList = [];\n    this.rawCategories = [];\n    this.newCategory = null;\n    this.pageSize = 50;\n    this.currentPageSize = 50;\n    this.triggerProductsCall = false;\n    this.showProductSpinner = false;\n    this.loadDataType = '';\n    this.currentPageNumber = 1;\n    this.total = 0;\n    this.ignorePagination = false;\n    this.shouldCallNextFeatureProduct = true;\n    this.shouldCallNextCategoryProduct = true;\n    this.isLayoutTemplate = false;\n    this.screenWidth = window.innerWidth;\n    this.isMobileView = this.screenWidth <= 786;\n    this.isGoogleAnalytics = false;\n    this.tagName = GaActionEnum;\n    this.isMobileLayout = false;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.scrollToTop();\n  }\n  ngOnInit() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.paramfunc();\n    this.scrollToTop();\n  }\n  paramfunc() {\n    let param;\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.products = [];\n      param = params.get('id');\n      param = param?.split('&');\n      this.userDetails = this.store.get('profile');\n      if (this.router.url.includes('promotion')) {\n        if (param?.length == 1) {\n          this.promotionId = param[0];\n          this.loadDataType = 'promotion';\n          this.triggerAnalytics('promotion', this.promotionId);\n          this.loadPromotionData();\n          // this.$gtmService.pushPageView('promotion',this.categoryName)\n        }\n      } else {\n        if (param?.length == 1) {\n          this.categoryId = param[0];\n          this.loadDataType = 'category';\n          this.triggerAnalytics('category', this.categoryId);\n          // this.$gtmService.pushPageView('category',this.categoryName)\n          this.loadData();\n        } else if (param?.length == 3) {\n          this.categoryId = param[0];\n          this.topNumber = param[1];\n          this.categoryName = param[2];\n          this.loadDataType = 'feature';\n          this.triggerAnalytics('feature', this.categoryId);\n          this.loadSectionData();\n          this.$gtmService.pushPageView('feature', this.categoryName);\n        }\n      }\n    });\n  }\n  loadData() {\n    this.loaderService.show();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, true, true).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res.success) {\n          this.isError = false;\n          this.total = res.data.productsList.records.length;\n          this.categoryBanner = res.data.categoryBanner;\n          res.data?.productsList?.records.forEach(record => {\n            this.addProductFromLoadData(record);\n            this.badgesList = record.badgesList[record]?.desktopImage || [];\n          });\n          this.getAllCategories();\n        }\n        this.loaderService.hide();\n      },\n      error: err => {\n        this.handleError(err);\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.activatedRoute.queryParams.subscribe(res => {\n      this.subPath = res?.path?.split('//');\n      this.subId = res?.id?.split('//');\n    });\n    if (!this.subPath) {\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          res.forEach(element => {\n            if (element.id == this.categoryId) {\n              this.category = element;\n            }\n          });\n          this.activatedRoute.queryParamMap.subscribe(params => {\n            this.categoryName = params.get('categoryName');\n          });\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.items = [{\n        label: this.category?.categoryName\n      }, {\n        label: label\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    } else {\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.categoryName = this.subPath[this.subPath.length - 1];\n      this.items = [];\n      this.subPath.forEach((x, index) => {\n        this.items.push({\n          label: x,\n          id: this.subId[index]\n        });\n      });\n      this.items.forEach((val, index) => {\n        this.catPaths = index == 0 ? val.label : this.catPaths + '//' + val.label;\n        this.catIds = index == 0 ? val.id : this.catIds + '//' + String(val.id);\n        val.routerLink = `/category/${val.id}`;\n        val.queryParams = {\n          path: this.catPaths,\n          id: this.catIds\n        };\n      });\n      this.items.push({\n        label: label\n      });\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    }\n  }\n  loadPromotionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        this.categoryName = res.data.promotionName;\n        this.breadItems = [{\n          label: this.categoryName\n        }];\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  handleError(err) {\n    this.isBlank = false;\n    this.isError = true;\n    this.loaderService.hide();\n  }\n  addProductFromLoadData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      badges: record.badgesList,\n      productId: record?.id,\n      productName: record?.name,\n      isLiked: record?.isLiked,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      priceId: selectedVariance?.priceId,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      specProductId: selectedVariance.specProductId,\n      channelId: record.channelId ?? '1',\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      shopId: record.shopId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated\n    };\n    this.products.push(product);\n  }\n  loadSectionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.breadItems = [{\n      label: this.categoryName\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  addProductFromLoadSectionData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    if (selectedVariance) {\n      let features = [];\n      if (selectedVariance?.productFeaturesList) {\n        features = selectedVariance?.productFeaturesList[0]?.featureList;\n      }\n      let product = {\n        badges: record.badgesList,\n        productId: record?.id,\n        productName: record?.name,\n        isLiked: record?.isLiked,\n        priceValue: selectedVariance?.price,\n        priceId: selectedVariance?.priceId,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: record?.currencyCode,\n        masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: record.shopId,\n        specProductId: selectedVariance.specProductId,\n        channelId: record.channelId ?? '1',\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        stockStatus: selectedVariance.stockStatus,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated\n      };\n      if (product.salePriceValue) {\n        product.salePercent = 100 - product.salePriceValue / product.priceValue * 100;\n      }\n      this.products.push(product);\n    }\n  }\n  fetchCategories(category, cat) {\n    if (category.length == 0) {\n      return;\n    }\n    for (const element of category) {\n      if (element.id == this.categoryId) {\n        this.assignBreadCrumbsData(element);\n      }\n      this.fetchCategories(element.categories, element);\n    }\n  }\n  assignBreadCrumbsData(category) {\n    let idsArray = category?.categoryIds?.split(\"->\")?.map(Number);\n    let nameArray = category?.categoryPath?.split(\"->\")?.map(String);\n    let breadCrumbs = [];\n    if (idsArray.length === nameArray.length) {\n      idsArray?.map((e, i) => {\n        breadCrumbs.push({\n          routerLink: '/category/' + e.toString(),\n          label: nameArray[i]\n        });\n      });\n      this.breadItems = breadCrumbs;\n      this.ref.detectChanges();\n      this.ref.markForCheck();\n    }\n    // this.$gtmService.pushPageView(category?.categoryPath)\n    this.$gtmService.pushPageView('category', category?.categoryPath);\n  }\n  logOut() {\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('profile', '');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    this.store.set('cartProducts', []);\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n  }\n  loadPaginatedProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber).subscribe({\n      next: res => {\n        this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;\n        const lastPageSizeProducts = res.data.productsList.records;\n        this.total = lastPageSizeProducts.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadData(record);\n        });\n        this.triggerProductsCall = !res.data.productsList.records.length;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  addProduct(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      productId: record?.id,\n      productName: record?.name,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      channelId: record?.channelId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated\n    };\n    this.products.push(product);\n  }\n  loadPaginatedFeatureProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadSectionData(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  loadPaginatedPromotionProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProduct(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  getAllCategories() {\n    let allCategories = localStorage.getItem('allCategories');\n    allCategories = JSON.parse(allCategories);\n    this.rawCategories = allCategories;\n    this.rawCategories?.forEach(cat => {\n      if (cat.id == this.categoryId) {\n        this.assignBreadCrumbsData(cat);\n      }\n      cat['path'] = cat.categoryName;\n      cat['catIds'] = cat.id;\n      this.fetchCategories(cat.categories, cat);\n    });\n  }\n  scrollToTop() {\n    if (isPlatformBrowser(this.platformId)) {\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }\n  }\n  triggerAnalytics(type, id) {\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')) {\n      if (type === 'promotion') {\n        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);\n      } else if (type === 'category') {\n        this.$gaService.pageView('/category', 'Category ID: ' + id);\n      } else if (type === 'feature') {\n        this.$gaService.pageView('/category', 'Feature ID: ' + id);\n      }\n      this.$gaService.event(this.tagName.SEARCH, id, type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST', 1, true, {\n        \"id\": id,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n  }\n  getBannerImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n};\n__decorate([Input()], IndexComponent.prototype, \"products\", void 0);\n__decorate([HostListener('window:scroll', ['$event'])], IndexComponent.prototype, \"onScroll\", null);\n__decorate([HostListener('window:resize', ['$event'])], IndexComponent.prototype, \"onResize\", null);\nIndexComponent = __decorate([Component({\n  selector: 'app-category-products',\n  templateUrl: './index.component.html',\n  styleUrls: ['./index.component.scss']\n}), __param(16, Inject(PLATFORM_ID))], IndexComponent);\nexport { IndexComponent };", "map": {"version": 3, "names": ["Component", "HostListener", "Inject", "Input", "PLATFORM_ID", "environment", "isPlatformBrowser", "GaActionEnum", "UtilityFunctions", "IndexComponent", "onScroll", "event", "platformId", "scrollPosition", "window", "scrollY", "document", "documentElement", "scrollTop", "totalHeight", "scrollHeight", "windowHeight", "innerHeight", "isAtBottom", "triggerProductsCall", "total", "pageSize", "loadOnScrollData", "onResize", "screenWidth", "target", "innerWidth", "isMobile<PERSON>iew", "loadDataType", "shouldCallNextCategoryProduct", "loadPaginatedProducts", "shouldCallNextFeatureProduct", "loadPaginatedFeatureProducts", "loadPaginatedPromotionProducts", "constructor", "activatedRoute", "productService", "store", "reviewsService", "messageService", "translate", "ref", "loaderService", "tenantService", "authTokenService", "cookieService", "mainDataService", "router", "appDataService", "permissionService", "$gaService", "$gtmService", "items", "breadItems", "home", "icon", "routerLink", "currency", "baseUrl", "apiEndPoint", "emptyMsg", "catIds", "catPaths", "isError", "isBlank", "badgesList", "rawCategories", "newCategory", "currentPageSize", "showProductSpinner", "currentPageNumber", "ignorePagination", "isLayoutTemplate", "isGoogleAnalytics", "tagName", "isMobileLayout", "hasPermission", "scrollToTop", "ngOnInit", "navbarData", "layoutTemplate", "find", "section", "type", "paramfunc", "param", "paramMap", "subscribe", "params", "products", "get", "split", "userDetails", "url", "includes", "length", "promotionId", "triggerAnalytics", "loadPromotionData", "categoryId", "loadData", "topNumber", "categoryName", "loadSectionData", "pushPageView", "show", "getCategoryProducts", "next", "res", "success", "data", "productsList", "records", "categoryBanner", "for<PERSON>ach", "record", "addProductFromLoadData", "desktopImage", "getAllCategories", "hide", "error", "err", "handleError", "setTimeout", "subscription", "queryParams", "subPath", "path", "subId", "id", "element", "category", "queryParamMap", "console", "label", "x", "index", "push", "val", "String", "GetAllProductsByPrmotion", "promotionName", "addProductFromLoadSectionData", "complete", "detectChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "defaultVariant", "productVariances", "variant", "isDefault", "approvedVariant", "soldOut", "features", "productFeaturesList", "featureList", "product", "badges", "productId", "productName", "name", "isLiked", "priceValue", "price", "salePriceValue", "salePrice", "priceId", "currencyCode", "masterImageUrl", "images", "thumbnailImages", "rate", "count", "specProductId", "channelId", "salePercent", "shopId", "isHot", "isNew", "isBest", "quantity", "proSchedulingId", "stockPerSKU", "stockStatus", "sku", "skuAutoGenerated", "GetAllProductsByFeature", "fetchCategories", "cat", "assignBreadCrumbsData", "categories", "idsArray", "categoryIds", "map", "Number", "nameArray", "categoryPath", "breadCrumbs", "e", "i", "toString", "logOut", "sessionStorage", "clear", "authTokenSet", "delete", "set", "setCartLenghtData", "setUserData", "localStorage", "setItem", "removeItem", "lastPageSizeProducts", "addProduct", "slice", "allCategories", "getItem", "JSON", "parse", "scrollTo", "top", "behavior", "getTagFeature", "pageView", "SEARCH", "mobileNumber", "getBannerImages", "verifyImageURL", "__decorate", "selector", "templateUrl", "styleUrls", "__param"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\category-products\\components\\index\\index.component.ts"], "sourcesContent": ["import {ChangeDetectorRef, Component, HostListener, Inject, Input, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport {ActivatedRoute, Router} from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CookieService } from \"ngx-cookie-service\";\r\nimport { environment } from '@environments/environment';\r\n\r\nimport {\r\n  MainDataService,\r\n  AuthTokenService,\r\n  TenantService,\r\n  LoaderService,\r\n  ReviewsService,\r\n  StoreService,\r\n  ProductService, AppDataService, PermissionService\r\n} from \"@core/services\";\r\n\r\nimport { ProductRate, Category, Product, Currency } from '@core/interface';\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaActionEnum, GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\nimport UtilityFunctions from '@core/utilities/functions';\r\n\r\n@Component({\r\n  selector: 'app-category-products',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexComponent implements OnInit {\r\n  categoryId: any;\r\n  topNumber: any;\r\n  category!: Category;\r\n  categoryName: any;\r\n  items: MenuItem[] = [];\r\n  breadItems: MenuItem[] = [];\r\n  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };\r\n  currency: Currency = {} as Currency;\r\n  baseUrl: string = environment.apiEndPoint + '/';\r\n  emptyMsg: string = 'Your Category Is Empty';\r\n  subPath: any;\r\n  subId: any;\r\n  catIds: any = '';\r\n  catPaths: any = '';\r\n  @Input() products: Array<Product>;\r\n  isError : boolean = false;\r\n  isBlank : boolean = true;\r\n  reviews: ProductRate[] | undefined;\r\n  badgesList: any[] = [];\r\n\r\n  rawCategories: any = [];\r\n  newCategory: any = null;\r\n  token: any;\r\n  pageSize: number = 50;\r\n  currentPageSize: number = 50;\r\n  triggerProductsCall: boolean = false;\r\n  showProductSpinner: boolean = false;\r\n\r\n  loadDataType: string = '';\r\n  currentPageNumber: number = 1;\r\n  total: number = 0;\r\n  ignorePagination:boolean = false;\r\n  shouldCallNextFeatureProduct: boolean = true;\r\n  shouldCallNextCategoryProduct: boolean = true;\r\n  navbarData: any;\r\n  isLayoutTemplate: boolean = false;\r\n  promotionId:any;\r\n  promotionName:any;\r\n  screenWidth: number = window.innerWidth;\r\n  isMobileView: boolean =this.screenWidth <= 786;\r\n  isGoogleAnalytics: boolean = false\r\n  userDetails: any;\r\n  tagName:any=GaActionEnum;\r\n  isMobileLayout: boolean = false;\r\n  categoryBanner: any;\r\n\r\n  @HostListener('window:scroll', ['$event'])\r\n  onScroll(event: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const scrollPosition = window.scrollY || document.documentElement.scrollTop;\r\n      const totalHeight = document.documentElement.scrollHeight;\r\n      const windowHeight = window.innerHeight;\r\n      const isAtBottom = scrollPosition + windowHeight >= totalHeight-1;\r\n\r\n      if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {\r\n          this.loadOnScrollData();\r\n      }\r\n    }\r\n  }\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: any) {\r\n    this.screenWidth = event.target.innerWidth;\r\n    if (this.screenWidth <= 768) {\r\n      this.isMobileView = true;\r\n    } else {\r\n      this.isMobileView = false;\r\n    }\r\n\r\n  }\r\n  loadOnScrollData(){\r\n      if (this.loadDataType === 'category' ) {\r\n          if (this.shouldCallNextCategoryProduct) this.loadPaginatedProducts()\r\n      } else if (this.loadDataType === 'feature') {\r\n          if (this.shouldCallNextFeatureProduct) this.loadPaginatedFeatureProducts()\r\n      }\r\n      else if (this.loadDataType === 'promotion') {\r\n          if (this.shouldCallNextFeatureProduct) this.loadPaginatedPromotionProducts()\r\n      }\r\n  }\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private productService: ProductService,\r\n    private store: StoreService,\r\n    private reviewsService: ReviewsService,\r\n    private messageService: MessageService,\r\n    private translate: TranslateService,\r\n    private ref: ChangeDetectorRef,\r\n    private loaderService: LoaderService,\r\n    private tenantService: TenantService,\r\n    private authTokenService: AuthTokenService,\r\n    private cookieService: CookieService,\r\n    private mainDataService: MainDataService,\r\n    private router: Router,\r\n    private appDataService: AppDataService,\r\n    private permissionService: PermissionService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n    private $gtmService:GTMService\r\n\r\n  ) {\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.scrollToTop()\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n    this.paramfunc()\r\n    this.scrollToTop()\r\n  }\r\n\r\n  paramfunc() {\r\n\r\n    let param;\r\n    this.activatedRoute.paramMap.subscribe((params) => {\r\n      this.products = [];\r\n      param = params.get('id');\r\n      param = param?.split('&');\r\n      this.userDetails = this.store.get('profile');\r\n      if(this.router.url.includes('promotion')) {\r\n        if (param?.length == 1) {\r\n          this.promotionId = param[0];\r\n          this.loadDataType = 'promotion'\r\n          this.triggerAnalytics('promotion', this.promotionId)\r\n          this.loadPromotionData();\r\n          // this.$gtmService.pushPageView('promotion',this.categoryName)\r\n        }\r\n      }\r\n      else {\r\n        if (param?.length == 1) {\r\n          this.categoryId = param[0];\r\n          this.loadDataType = 'category'\r\n          this.triggerAnalytics('category', this.categoryId)\r\n          // this.$gtmService.pushPageView('category',this.categoryName)\r\n          this.loadData();\r\n        } else if (param?.length == 3) {\r\n          this.categoryId = param[0];\r\n          this.topNumber = param[1];\r\n          this.categoryName = param[2];\r\n          this.loadDataType = 'feature'\r\n          this.triggerAnalytics('feature', this.categoryId)\r\n          this.loadSectionData();\r\n          this.$gtmService.pushPageView('feature',this.categoryName)\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  loadData(): void {\r\n    this.loaderService.show();\r\n    this.productService\r\n      .getCategoryProducts(this.categoryId, this.currentPageSize, true, true)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          if (res.success) {\r\n            this.isError= false;\r\n            this.total = res.data.productsList.records.length;\r\n            this.categoryBanner = res.data.categoryBanner;\r\n            res.data?.productsList?.records.forEach((record: any) => {\r\n              this.addProductFromLoadData(record)\r\n              this.badgesList = record.badgesList[record]?.desktopImage || [];\r\n            })\r\n           this.getAllCategories();\r\n          }\r\n          this.loaderService.hide();\r\n        },\r\n        error: (err: any) => {\r\n          this.handleError(err);\r\n        },\r\n      });\r\n\r\n\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n    this.activatedRoute.queryParams.subscribe((res) => {\r\n      this.subPath = res?.path?.split('//');\r\n      this.subId = res?.id?.split('//');\r\n    });\r\n\r\n    if (!this.subPath) {\r\n      this.store.subscription('categories').subscribe({\r\n        next: (res: any) => {\r\n\r\n\r\n          res.forEach((element: Category) => {\r\n            if (element.id == this.categoryId) {\r\n              this.category = element;\r\n            }\r\n          });\r\n          this.activatedRoute.queryParamMap.subscribe(params => {\r\n\r\n\r\n          this.categoryName = params.get('categoryName')\r\n          })\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n        },\r\n      });\r\n      let label = 'Products';\r\n\r\n      this.translate.get('categoryCard.products').subscribe((data: any) => {\r\n        label = data;\r\n      });\r\n\r\n      this.items = [{ label: this.category?.categoryName }, { label: label }];\r\n      this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    } else {\r\n      let label = 'Products';\r\n\r\n      this.translate.get('categoryCard.products').subscribe((data: any) => {\r\n        label = data;\r\n      });\r\n      this.categoryName = this.subPath[this.subPath.length - 1];\r\n      this.items = [];\r\n      this.subPath.forEach((x: any, index: any) => {\r\n        this.items.push({\r\n          label: x,\r\n          id: this.subId[index]\r\n        });\r\n      });\r\n\r\n      this.items.forEach((val: any, index: any) => {\r\n        this.catPaths = index == 0 ? val.label : this.catPaths + '//' + val.label;\r\n        this.catIds = index == 0 ? val.id : this.catIds + '//' + String(val.id);\r\n        val.routerLink = `/category/${val.id}`\r\n        val.queryParams = { path: this.catPaths, id: this.catIds }\r\n      });\r\n\r\n      this.items.push({\r\n        label: label,\r\n      });\r\n      this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    }\r\n  }\r\n  loadPromotionData(): void {\r\n\r\n    this.loaderService.show();\r\n    this.productService\r\n      .GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          this.categoryName=res.data.promotionName;\r\n          this.breadItems = [{ label: this.categoryName }];\r\n          if (res?.data?.records?.length > 0) {\r\n            this.total = res?.data?.records?.length;\r\n            res.data?.records.forEach((record: any) => {\r\n              this.addProductFromLoadSectionData(record);\r\n            })\r\n            this.loaderService.hide();\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n          this.isBlank = false;\r\n          this.loaderService.hide();\r\n        },\r\n        complete: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    this.ref.detectChanges();\r\n    this.ref.markForCheck();\r\n  }\r\n\r\n  private handleError(err: any): void {\r\n    this.isBlank = false;\r\n    this.isError = true;\r\n    this.loaderService.hide();\r\n  }\r\n\r\n  addProductFromLoadData(record:any){\r\n    let selectedVariance;\r\n\r\n    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n    if (defaultVariant) {\r\n      selectedVariance = defaultVariant;\r\n    } else {\r\n      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n      if (approvedVariant) {\r\n        selectedVariance = approvedVariant;\r\n\r\n      } else {\r\n        selectedVariance = record?.productVariances[0];\r\n      }\r\n    }\r\n    let features=[];\r\n    if(selectedVariance?.productFeaturesList){\r\n      features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n    }\r\n\r\n    let product:any = {\r\n      badges:record.badgesList,\r\n      productId: record?.id,\r\n      productName: record?.name,\r\n      isLiked:record?.isLiked,\r\n      priceValue: selectedVariance?.price,\r\n      salePriceValue: selectedVariance?.salePrice,\r\n      priceId: selectedVariance?.priceId,\r\n      currencyCode: record?.currencyCode,\r\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\r\n      thumbnailImages: selectedVariance?.thumbnailImages,\r\n      soldOut: selectedVariance?.soldOut,\r\n      rate: selectedVariance?.rate,\r\n      count: selectedVariance?.count ?? 0,\r\n      specProductId: selectedVariance.specProductId,\r\n      channelId: record.channelId ?? '1',\r\n      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n      shopId: record.shopId,\r\n      isHot:features?.includes(1),\r\n      isNew:features?.includes(2),\r\n      isBest:features?.includes(3),\r\n      quantity:selectedVariance.quantity,\r\n      proSchedulingId:selectedVariance.proSchedulingId,\r\n      stockPerSKU:selectedVariance.stockPerSKU,\r\n      stockStatus:selectedVariance.stockStatus,\r\n      sku:selectedVariance?.sku,\r\n      skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n    }\r\n    this.products.push(product)\r\n  }\r\n\r\n  loadSectionData(): void {\r\n\r\n    this.loaderService.show();\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          if (res?.data?.records?.length > 0) {\r\n            this.total = res?.data?.records?.length;\r\n            res.data?.records.forEach((record: any) => {\r\n             this.addProductFromLoadSectionData(record);\r\n            })\r\n            this.loaderService.hide();\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n          this.isBlank = false;\r\n          this.loaderService.hide();\r\n        },\r\n        complete: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n    this.breadItems = [{ label: this.categoryName }];\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    this.ref.detectChanges();\r\n    this.ref.markForCheck();\r\n  }\r\n\r\n  addProductFromLoadSectionData(record:any){\r\n    let selectedVariance;\r\n  let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n  if (defaultVariant) {\r\n    selectedVariance = defaultVariant;\r\n  } else {\r\n  let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n  if (approvedVariant) {\r\n    selectedVariance = approvedVariant;\r\n\r\n  } else {\r\n  selectedVariance = record?.productVariances[0];\r\n}\r\n\r\n}\r\nif (selectedVariance) {\r\n  let features=[];\r\n  if(selectedVariance?.productFeaturesList){\r\n    features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n  }\r\n  let product:any = {\r\n    badges:record.badgesList,\r\n    productId: record?.id,\r\n    productName: record?.name,\r\n    isLiked:record?.isLiked,\r\n    priceValue: selectedVariance?.price,\r\n    priceId: selectedVariance?.priceId,\r\n    salePriceValue: selectedVariance?.salePrice,\r\n    currencyCode: record?.currencyCode,\r\n    masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\r\n    thumbnailImages: selectedVariance?.thumbnailImages,\r\n    soldOut: selectedVariance?.soldOut,\r\n    rate: selectedVariance?.rate,\r\n    count: selectedVariance?.count ?? 0,\r\n    salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n    shopId: record.shopId,\r\n    specProductId: selectedVariance.specProductId,\r\n    channelId: record.channelId ?? '1',\r\n    isHot:features?.includes(1),\r\n    isNew:features?.includes(2),\r\n    isBest:features?.includes(3),\r\n    quantity:selectedVariance.quantity,\r\n    proSchedulingId:selectedVariance.proSchedulingId,\r\n    stockPerSKU:selectedVariance.stockPerSKU,\r\n    stockStatus: selectedVariance.stockStatus,\r\n    sku:selectedVariance?.sku,\r\n    skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n\r\n  }\r\n  if (product.salePriceValue) {\r\n    product.salePercent = 100 - (product.salePriceValue / product.priceValue * 100);\r\n  }\r\n  this.products.push(product)\r\n  }\r\n  }\r\n\r\n\r\nfetchCategories(category: any, cat: any) {\r\n\r\n    if (category.length == 0) {\r\n      return;\r\n    }\r\n\r\n    for (const element of category) {\r\n      if (element.id == this.categoryId) {\r\n        this.assignBreadCrumbsData(element);\r\n      }\r\n      this.fetchCategories(element.categories, element);\r\n    }\r\n\r\n  }\r\n\r\n  assignBreadCrumbsData(category: any) {\r\n\r\n    let idsArray: any = category?.categoryIds?.split(\"->\")?.map(Number);\r\n\r\n    let nameArray: any = category?.categoryPath?.split(\"->\")?.map(String);\r\n\r\n    let breadCrumbs: any = [];\r\n    if (idsArray.length === nameArray.length) {\r\n      idsArray?.map((e: any, i: any) => {\r\n        breadCrumbs.push({ routerLink: '/category/' + e.toString(), label: nameArray[i] });\r\n      });\r\n      this.breadItems = breadCrumbs;\r\n      this.ref.detectChanges();\r\n      this.ref.markForCheck();\r\n    }\r\n    // this.$gtmService.pushPageView(category?.categoryPath)\r\n    this.$gtmService.pushPageView('category', category?.categoryPath)\r\n\r\n\r\n\r\n  }\r\n\r\n  logOut() {\r\n    sessionStorage.clear();\r\n    this.authTokenService.authTokenSet('');\r\n\r\n\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('profile', '');\r\n    this.mainDataService.setCartLenghtData(null);\r\n    this.mainDataService.setUserData(null);\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    localStorage.setItem('cartId', '');\r\n    this.store.set('cartProducts', []);\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n  }\r\n\r\n  loadPaginatedProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges();\r\n\r\n    this.productService\r\n      .getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;\r\n          const lastPageSizeProducts = res.data.productsList.records;\r\n          this.total = lastPageSizeProducts.length;\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProductFromLoadData(record);\r\n          })\r\n          this.triggerProductsCall = !res.data.productsList.records.length;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n\r\n  addProduct(record:any){\r\n    let selectedVariance;\r\n    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n    if (defaultVariant) {\r\n      selectedVariance = defaultVariant;\r\n    } else {\r\n      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n      if (approvedVariant) {\r\n        selectedVariance = approvedVariant;\r\n      } else {\r\n        selectedVariance = record?.productVariances[0];\r\n      }\r\n    }\r\n    let features=[];\r\n    if(selectedVariance?.productFeaturesList){\r\n      features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n    }\r\n    let product:any = {\r\n      productId: record?.id,\r\n      productName: record?.name,\r\n      priceValue: selectedVariance?.price,\r\n      salePriceValue: selectedVariance?.salePrice,\r\n      currencyCode: record?.currencyCode,\r\n      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\r\n      thumbnailImages: selectedVariance?.thumbnailImages,\r\n      soldOut: selectedVariance?.soldOut,\r\n      rate: selectedVariance?.rate,\r\n      count: selectedVariance?.count ?? 0,\r\n      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n      channelId: record?.channelId,\r\n      isHot:features?.includes(1),\r\n      isNew:features?.includes(2),\r\n      isBest:features?.includes(3),\r\n      quantity:selectedVariance.quantity,\r\n      proSchedulingId:selectedVariance.proSchedulingId,\r\n      stockPerSKU:selectedVariance.stockPerSKU,\r\n      stockStatus: selectedVariance.stockStatus,\r\n      sku:selectedVariance?.sku,\r\n      skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n    }\r\n    this.products.push(product)\r\n  }\r\n\r\n  loadPaginatedFeatureProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges()\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\r\n          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\r\n          this.total = res.data?.records?.length;\r\n\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProductFromLoadSectionData(record);\r\n          })\r\n          if (res.data?.records.length) this.triggerProductsCall = false;\r\n          else this.triggerProductsCall = true;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n  loadPaginatedPromotionProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges()\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\r\n          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\r\n          this.total = res.data?.records?.length;\r\n\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProduct(record);\r\n          })\r\n          if (res.data?.records.length) this.triggerProductsCall = false;\r\n          else this.triggerProductsCall = true;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n\r\n\r\n  private getAllCategories() {\r\n    let allCategories: any = localStorage.getItem('allCategories');\r\n    allCategories = JSON.parse(allCategories);\r\n    this.rawCategories = allCategories;\r\n\r\n    this.rawCategories?.forEach((cat: any) => {\r\n      if (cat.id == this.categoryId) {\r\n        this.assignBreadCrumbsData(cat);\r\n      }\r\n      cat['path'] = cat.categoryName;\r\n      cat['catIds'] = cat.id;\r\n      this.fetchCategories(cat.categories, cat);\r\n    });\r\n  }\r\n\r\n  scrollToTop(){\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.scrollTo({top: 0, behavior: 'smooth'});\r\n    }\r\n  }\r\n\r\n  triggerAnalytics(type: string, id: any) {\r\n    if(this.isGoogleAnalytics &&  this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')){\r\n      if(type === 'promotion') {\r\n        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);\r\n      } else if (type === 'category') {\r\n        this.$gaService.pageView('/category', 'Category ID: ' + id);\r\n      } else if (type === 'feature') {\r\n        this.$gaService.pageView('/category', 'Feature ID: ' + id);\r\n      }\r\n      this.$gaService.event(\r\n        this.tagName.SEARCH,\r\n        id,\r\n        type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST',\r\n        1,\r\n        true,\r\n        {\r\n          \"id\": id,\r\n          \"user_ID\":this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\r\n        }\r\n      );\r\n    }\r\n  }\r\n  getBannerImages(url: string) {\r\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAA2BA,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAUC,WAAW,QAAO,eAAe;AAK5G,SAASC,WAAW,QAAQ,2BAA2B;AAavD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,YAAY,QAA+B,sBAAsB;AAEzE,OAAOC,gBAAgB,MAAM,2BAA2B;AAOjD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAgDzBC,QAAQA,CAACC,KAAU;IACjB,IAAIL,iBAAiB,CAAC,IAAI,CAACM,UAAU,CAAC,EAAE;MACtC,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,IAAIC,QAAQ,CAACC,eAAe,CAACC,SAAS;MAC3E,MAAMC,WAAW,GAAGH,QAAQ,CAACC,eAAe,CAACG,YAAY;MACzD,MAAMC,YAAY,GAAGP,MAAM,CAACQ,WAAW;MACvC,MAAMC,UAAU,GAAGV,cAAc,GAAGQ,YAAY,IAAIF,WAAW,GAAC,CAAC;MAEjE,IAAII,UAAU,IAAI,CAAC,IAAI,CAACC,mBAAmB,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,QAAQ,EAAE;QACxE,IAAI,CAACC,gBAAgB,EAAE;;;EAG/B;EAEAC,QAAQA,CAACjB,KAAU;IACjB,IAAI,CAACkB,WAAW,GAAGlB,KAAK,CAACmB,MAAM,CAACC,UAAU;IAC1C,IAAI,IAAI,CAACF,WAAW,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACG,YAAY,GAAG,IAAI;KACzB,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,KAAK;;EAG7B;EACAL,gBAAgBA,CAAA;IACZ,IAAI,IAAI,CAACM,YAAY,KAAK,UAAU,EAAG;MACnC,IAAI,IAAI,CAACC,6BAA6B,EAAE,IAAI,CAACC,qBAAqB,EAAE;KACvE,MAAM,IAAI,IAAI,CAACF,YAAY,KAAK,SAAS,EAAE;MACxC,IAAI,IAAI,CAACG,4BAA4B,EAAE,IAAI,CAACC,4BAA4B,EAAE;KAC7E,MACI,IAAI,IAAI,CAACJ,YAAY,KAAK,WAAW,EAAE;MACxC,IAAI,IAAI,CAACG,4BAA4B,EAAE,IAAI,CAACE,8BAA8B,EAAE;;EAEpF;EAEAC,YACUC,cAA8B,EAC9BC,cAA8B,EAC9BC,KAAmB,EACnBC,cAA8B,EAC9BC,cAA8B,EAC9BC,SAA2B,EAC3BC,GAAsB,EACtBC,aAA4B,EAC5BC,aAA4B,EAC5BC,gBAAkC,EAClCC,aAA4B,EAC5BC,eAAgC,EAChCC,MAAc,EACdC,cAA8B,EAC9BC,iBAAoC,EACpCC,UAAkC,EACb3C,UAAe,EACpC4C,WAAsB;IAjBtB,KAAAhB,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACW,KAAA3C,UAAU,GAAVA,UAAU;IAC/B,KAAA4C,WAAW,GAAXA,WAAW;IA9FrB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,IAAI,GAAa;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACxD,KAAAC,QAAQ,GAAa,EAAc;IACnC,KAAAC,OAAO,GAAW1D,WAAW,CAAC2D,WAAW,GAAG,GAAG;IAC/C,KAAAC,QAAQ,GAAW,wBAAwB;IAG3C,KAAAC,MAAM,GAAQ,EAAE;IAChB,KAAAC,QAAQ,GAAQ,EAAE;IAElB,KAAAC,OAAO,GAAa,KAAK;IACzB,KAAAC,OAAO,GAAa,IAAI;IAExB,KAAAC,UAAU,GAAU,EAAE;IAEtB,KAAAC,aAAa,GAAQ,EAAE;IACvB,KAAAC,WAAW,GAAQ,IAAI;IAEvB,KAAA9C,QAAQ,GAAW,EAAE;IACrB,KAAA+C,eAAe,GAAW,EAAE;IAC5B,KAAAjD,mBAAmB,GAAY,KAAK;IACpC,KAAAkD,kBAAkB,GAAY,KAAK;IAEnC,KAAAzC,YAAY,GAAW,EAAE;IACzB,KAAA0C,iBAAiB,GAAW,CAAC;IAC7B,KAAAlD,KAAK,GAAW,CAAC;IACjB,KAAAmD,gBAAgB,GAAW,KAAK;IAChC,KAAAxC,4BAA4B,GAAY,IAAI;IAC5C,KAAAF,6BAA6B,GAAY,IAAI;IAE7C,KAAA2C,gBAAgB,GAAY,KAAK;IAGjC,KAAAhD,WAAW,GAAWf,MAAM,CAACiB,UAAU;IACvC,KAAAC,YAAY,GAAW,IAAI,CAACH,WAAW,IAAI,GAAG;IAC9C,KAAAiD,iBAAiB,GAAY,KAAK;IAElC,KAAAC,OAAO,GAAKxE,YAAY;IACxB,KAAAyE,cAAc,GAAY,KAAK;IA0D7B,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACvB,iBAAiB,CAAC2B,aAAa,CAAC,iBAAiB,CAAC;IAC/E,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACxB,iBAAiB,CAAC2B,aAAa,CAAC,kBAAkB,CAAC;IACjF,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC1B,iBAAiB,CAAC2B,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC/B,cAAc,CAACgC,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IACtG,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACP,WAAW,EAAE;EACpB;EAEAO,SAASA,CAAA;IAEP,IAAIC,KAAK;IACT,IAAI,CAAClD,cAAc,CAACmD,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MAChD,IAAI,CAACC,QAAQ,GAAG,EAAE;MAClBJ,KAAK,GAAGG,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;MACxBL,KAAK,GAAGA,KAAK,EAAEM,KAAK,CAAC,GAAG,CAAC;MACzB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACvD,KAAK,CAACqD,GAAG,CAAC,SAAS,CAAC;MAC5C,IAAG,IAAI,CAAC3C,MAAM,CAAC8C,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACxC,IAAIT,KAAK,EAAEU,MAAM,IAAI,CAAC,EAAE;UACtB,IAAI,CAACC,WAAW,GAAGX,KAAK,CAAC,CAAC,CAAC;UAC3B,IAAI,CAACzD,YAAY,GAAG,WAAW;UAC/B,IAAI,CAACqE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACD,WAAW,CAAC;UACpD,IAAI,CAACE,iBAAiB,EAAE;UACxB;;OAEH,MACI;QACH,IAAIb,KAAK,EAAEU,MAAM,IAAI,CAAC,EAAE;UACtB,IAAI,CAACI,UAAU,GAAGd,KAAK,CAAC,CAAC,CAAC;UAC1B,IAAI,CAACzD,YAAY,GAAG,UAAU;UAC9B,IAAI,CAACqE,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACE,UAAU,CAAC;UAClD;UACA,IAAI,CAACC,QAAQ,EAAE;SAChB,MAAM,IAAIf,KAAK,EAAEU,MAAM,IAAI,CAAC,EAAE;UAC7B,IAAI,CAACI,UAAU,GAAGd,KAAK,CAAC,CAAC,CAAC;UAC1B,IAAI,CAACgB,SAAS,GAAGhB,KAAK,CAAC,CAAC,CAAC;UACzB,IAAI,CAACiB,YAAY,GAAGjB,KAAK,CAAC,CAAC,CAAC;UAC5B,IAAI,CAACzD,YAAY,GAAG,SAAS;UAC7B,IAAI,CAACqE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,UAAU,CAAC;UACjD,IAAI,CAACI,eAAe,EAAE;UACtB,IAAI,CAACpD,WAAW,CAACqD,YAAY,CAAC,SAAS,EAAC,IAAI,CAACF,YAAY,CAAC;;;IAGhE,CAAC,CAAC;EACJ;EAEAF,QAAQA,CAAA;IACN,IAAI,CAAC1D,aAAa,CAAC+D,IAAI,EAAE;IACzB,IAAI,CAACrE,cAAc,CAChBsE,mBAAmB,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,CACtEmB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACnB,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACzB,OAAO,GAAG,KAAK;QACpB,IAAI4C,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAAC9C,OAAO,GAAE,KAAK;UACnB,IAAI,CAAC3C,KAAK,GAAGwF,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO,CAACjB,MAAM;UACjD,IAAI,CAACkB,cAAc,GAAGL,GAAG,CAACE,IAAI,CAACG,cAAc;UAC7CL,GAAG,CAACE,IAAI,EAAEC,YAAY,EAAEC,OAAO,CAACE,OAAO,CAAEC,MAAW,IAAI;YACtD,IAAI,CAACC,sBAAsB,CAACD,MAAM,CAAC;YACnC,IAAI,CAAClD,UAAU,GAAGkD,MAAM,CAAClD,UAAU,CAACkD,MAAM,CAAC,EAAEE,YAAY,IAAI,EAAE;UACjE,CAAC,CAAC;UACH,IAAI,CAACC,gBAAgB,EAAE;;QAExB,IAAI,CAAC5E,aAAa,CAAC6E,IAAI,EAAE;MAC3B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACC,WAAW,CAACD,GAAG,CAAC;MACvB;KACD,CAAC;IAIJE,UAAU,CAAC,MAAK;MAEd,IAAI,CAACtF,KAAK,CAACuF,YAAY,CAAC,UAAU,CAAC,CAACrC,SAAS,CAAC;QAC5CoB,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACnD,QAAQ,GAAGmD;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI,CAACzE,cAAc,CAAC0F,WAAW,CAACtC,SAAS,CAAEqB,GAAG,IAAI;MAChD,IAAI,CAACkB,OAAO,GAAGlB,GAAG,EAAEmB,IAAI,EAAEpC,KAAK,CAAC,IAAI,CAAC;MACrC,IAAI,CAACqC,KAAK,GAAGpB,GAAG,EAAEqB,EAAE,EAAEtC,KAAK,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACmC,OAAO,EAAE;MACjB,IAAI,CAACzF,KAAK,CAACuF,YAAY,CAAC,YAAY,CAAC,CAACrC,SAAS,CAAC;QAC9CoB,IAAI,EAAGC,GAAQ,IAAI;UAGjBA,GAAG,CAACM,OAAO,CAAEgB,OAAiB,IAAI;YAChC,IAAIA,OAAO,CAACD,EAAE,IAAI,IAAI,CAAC9B,UAAU,EAAE;cACjC,IAAI,CAACgC,QAAQ,GAAGD,OAAO;;UAE3B,CAAC,CAAC;UACF,IAAI,CAAC/F,cAAc,CAACiG,aAAa,CAAC7C,SAAS,CAACC,MAAM,IAAG;YAGrD,IAAI,CAACc,YAAY,GAAGd,MAAM,CAACE,GAAG,CAAC,cAAc,CAAC;UAC9C,CAAC,CAAC;QACJ,CAAC;QACD8B,KAAK,EAAGC,GAAQ,IAAI;UAClBY,OAAO,CAACb,KAAK,CAACC,GAAG,CAAC;QACpB;OACD,CAAC;MACF,IAAIa,KAAK,GAAG,UAAU;MAEtB,IAAI,CAAC9F,SAAS,CAACkD,GAAG,CAAC,uBAAuB,CAAC,CAACH,SAAS,CAAEuB,IAAS,IAAI;QAClEwB,KAAK,GAAGxB,IAAI;MACd,CAAC,CAAC;MAEF,IAAI,CAAC1D,KAAK,GAAG,CAAC;QAAEkF,KAAK,EAAE,IAAI,CAACH,QAAQ,EAAE7B;MAAY,CAAE,EAAE;QAAEgC,KAAK,EAAEA;MAAK,CAAE,CAAC;MACvE,IAAI,CAAChF,IAAI,GAAG;QAAEC,IAAI,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAG,CAAE;KACpD,MAAM;MACL,IAAI8E,KAAK,GAAG,UAAU;MAEtB,IAAI,CAAC9F,SAAS,CAACkD,GAAG,CAAC,uBAAuB,CAAC,CAACH,SAAS,CAAEuB,IAAS,IAAI;QAClEwB,KAAK,GAAGxB,IAAI;MACd,CAAC,CAAC;MACF,IAAI,CAACR,YAAY,GAAG,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC/B,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI,CAAC3C,KAAK,GAAG,EAAE;MACf,IAAI,CAAC0E,OAAO,CAACZ,OAAO,CAAC,CAACqB,CAAM,EAAEC,KAAU,KAAI;QAC1C,IAAI,CAACpF,KAAK,CAACqF,IAAI,CAAC;UACdH,KAAK,EAAEC,CAAC;UACRN,EAAE,EAAE,IAAI,CAACD,KAAK,CAACQ,KAAK;SACrB,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACpF,KAAK,CAAC8D,OAAO,CAAC,CAACwB,GAAQ,EAAEF,KAAU,KAAI;QAC1C,IAAI,CAAC1E,QAAQ,GAAG0E,KAAK,IAAI,CAAC,GAAGE,GAAG,CAACJ,KAAK,GAAG,IAAI,CAACxE,QAAQ,GAAG,IAAI,GAAG4E,GAAG,CAACJ,KAAK;QACzE,IAAI,CAACzE,MAAM,GAAG2E,KAAK,IAAI,CAAC,GAAGE,GAAG,CAACT,EAAE,GAAG,IAAI,CAACpE,MAAM,GAAG,IAAI,GAAG8E,MAAM,CAACD,GAAG,CAACT,EAAE,CAAC;QACvES,GAAG,CAAClF,UAAU,GAAG,aAAakF,GAAG,CAACT,EAAE,EAAE;QACtCS,GAAG,CAACb,WAAW,GAAG;UAAEE,IAAI,EAAE,IAAI,CAACjE,QAAQ;UAAEmE,EAAE,EAAE,IAAI,CAACpE;QAAM,CAAE;MAC5D,CAAC,CAAC;MAEF,IAAI,CAACT,KAAK,CAACqF,IAAI,CAAC;QACdH,KAAK,EAAEA;OACR,CAAC;MACF,IAAI,CAAChF,IAAI,GAAG;QAAEC,IAAI,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAG,CAAE;;EAEvD;EACA0C,iBAAiBA,CAAA;IAEf,IAAI,CAACxD,aAAa,CAAC+D,IAAI,EAAE;IACzB,IAAI,CAACrE,cAAc,CAChBwG,wBAAwB,CAAC,IAAI,CAAC5C,WAAW,EAAE,IAAI,CAAC5B,eAAe,EAAE,KAAK,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAC/FiB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACnB,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACzB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsC,YAAY,GAACM,GAAG,CAACE,IAAI,CAAC+B,aAAa;QACxC,IAAI,CAACxF,UAAU,GAAG,CAAC;UAAEiF,KAAK,EAAE,IAAI,CAAChC;QAAY,CAAE,CAAC;QAChD,IAAIM,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAEjB,MAAM,GAAG,CAAC,EAAE;UAClC,IAAI,CAAC3E,KAAK,GAAGwF,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAEjB,MAAM;UACvCa,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACE,OAAO,CAAEC,MAAW,IAAI;YACxC,IAAI,CAAC2B,6BAA6B,CAAC3B,MAAM,CAAC;UAC5C,CAAC,CAAC;UACF,IAAI,CAACzE,aAAa,CAAC6E,IAAI,EAAE;;MAE7B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClBY,OAAO,CAACb,KAAK,CAACC,GAAG,CAAC;QAClB,IAAI,CAACzD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACtB,aAAa,CAAC6E,IAAI,EAAE;MAC3B,CAAC;MACDwB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACrG,aAAa,CAAC6E,IAAI,EAAE;MAC3B;KACD,CAAC;IAEJI,UAAU,CAAC,MAAK;MAEd,IAAI,CAACtF,KAAK,CAACuF,YAAY,CAAC,UAAU,CAAC,CAACrC,SAAS,CAAC;QAC5CoB,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACnD,QAAQ,GAAGmD;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAGN,IAAI,CAACtD,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACf,GAAG,CAACuG,aAAa,EAAE;IACxB,IAAI,CAACvG,GAAG,CAACwG,YAAY,EAAE;EACzB;EAEQvB,WAAWA,CAACD,GAAQ;IAC1B,IAAI,CAACzD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACrB,aAAa,CAAC6E,IAAI,EAAE;EAC3B;EAEAH,sBAAsBA,CAACD,MAAU;IAC/B,IAAI+B,gBAAgB;IAEpB,IAAIC,cAAc,GAAGhC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACL,IAAII,eAAe,GAAGpC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OAEnC,MAAM;QACLL,gBAAgB,GAAG/B,MAAM,EAAEiC,gBAAgB,CAAC,CAAC,CAAC;;;IAGlD,IAAIK,QAAQ,GAAC,EAAE;IACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;MACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;IAGhE,IAAIC,OAAO,GAAO;MAChBC,MAAM,EAAC1C,MAAM,CAAClD,UAAU;MACxB6F,SAAS,EAAE3C,MAAM,EAAEc,EAAE;MACrB8B,WAAW,EAAE5C,MAAM,EAAE6C,IAAI;MACzBC,OAAO,EAAC9C,MAAM,EAAE8C,OAAO;MACvBC,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;MACnCC,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;MAC3CC,OAAO,EAAEpB,gBAAgB,EAAEoB,OAAO;MAClCC,YAAY,EAAEpD,MAAM,EAAEoD,YAAY;MAClCC,cAAc,EAAErD,MAAM,EAAEqD,cAAc,KAAKtB,gBAAgB,CAACuB,MAAM,GAAGvB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACvGC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;MAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;MAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;MAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;MACnCC,aAAa,EAAE3B,gBAAgB,CAAC2B,aAAa;MAC7CC,SAAS,EAAE3D,MAAM,CAAC2D,SAAS,IAAI,GAAG;MAClCC,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;MAC9Ga,MAAM,EAAE7D,MAAM,CAAC6D,MAAM;MACrBC,KAAK,EAACxB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC3BoF,KAAK,EAACzB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC3BqF,MAAM,EAAC1B,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC5BsF,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;MAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;MAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;MACxCC,WAAW,EAACrC,gBAAgB,CAACqC,WAAW;MACxCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;MACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;KACrC;IACD,IAAI,CAAChG,QAAQ,CAACgD,IAAI,CAACmB,OAAO,CAAC;EAC7B;EAEArD,eAAeA,CAAA;IAEb,IAAI,CAAC7D,aAAa,CAAC+D,IAAI,EAAE;IACzB,IAAI,CAACrE,cAAc,CAChBsJ,uBAAuB,CAAC,IAAI,CAACvF,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,KAAK,EAAE,IAAI,CAACE,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAACC,gBAAgB,CAAC,CACrIgB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACnB,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACzB,OAAO,GAAG,KAAK;QACpB,IAAI4C,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAEjB,MAAM,GAAG,CAAC,EAAE;UAClC,IAAI,CAAC3E,KAAK,GAAGwF,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAEjB,MAAM;UACvCa,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACE,OAAO,CAAEC,MAAW,IAAI;YACzC,IAAI,CAAC2B,6BAA6B,CAAC3B,MAAM,CAAC;UAC3C,CAAC,CAAC;UACF,IAAI,CAACzE,aAAa,CAAC6E,IAAI,EAAE;;MAE7B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClBY,OAAO,CAACb,KAAK,CAACC,GAAG,CAAC;QAClB,IAAI,CAACzD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACtB,aAAa,CAAC6E,IAAI,EAAE;MAC3B,CAAC;MACDwB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACrG,aAAa,CAAC6E,IAAI,EAAE;MAC3B;KACD,CAAC;IAEJI,UAAU,CAAC,MAAK;MAEd,IAAI,CAACtF,KAAK,CAACuF,YAAY,CAAC,UAAU,CAAC,CAACrC,SAAS,CAAC;QAC5CoB,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACnD,QAAQ,GAAGmD;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI,CAACvD,UAAU,GAAG,CAAC;MAAEiF,KAAK,EAAE,IAAI,CAAChC;IAAY,CAAE,CAAC;IAChD,IAAI,CAAChD,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACf,GAAG,CAACuG,aAAa,EAAE;IACxB,IAAI,CAACvG,GAAG,CAACwG,YAAY,EAAE;EACzB;EAEAH,6BAA6BA,CAAC3B,MAAU;IACtC,IAAI+B,gBAAgB;IACtB,IAAIC,cAAc,GAAGhC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACP,IAAII,eAAe,GAAGpC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OAEnC,MAAM;QACPL,gBAAgB,GAAG/B,MAAM,EAAEiC,gBAAgB,CAAC,CAAC,CAAC;;;IAIhD,IAAIF,gBAAgB,EAAE;MACpB,IAAIO,QAAQ,GAAC,EAAE;MACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;QACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;MAEhE,IAAIC,OAAO,GAAO;QAChBC,MAAM,EAAC1C,MAAM,CAAClD,UAAU;QACxB6F,SAAS,EAAE3C,MAAM,EAAEc,EAAE;QACrB8B,WAAW,EAAE5C,MAAM,EAAE6C,IAAI;QACzBC,OAAO,EAAC9C,MAAM,EAAE8C,OAAO;QACvBC,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;QACnCG,OAAO,EAAEpB,gBAAgB,EAAEoB,OAAO;QAClCF,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;QAC3CE,YAAY,EAAEpD,MAAM,EAAEoD,YAAY;QAClCC,cAAc,EAAErD,MAAM,EAAEqD,cAAc,IAAItB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC;QACpEC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;QAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;QAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;QAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;QACnCG,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;QAC9Ga,MAAM,EAAE7D,MAAM,CAAC6D,MAAM;QACrBH,aAAa,EAAE3B,gBAAgB,CAAC2B,aAAa;QAC7CC,SAAS,EAAE3D,MAAM,CAAC2D,SAAS,IAAI,GAAG;QAClCG,KAAK,EAACxB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;QAC3BoF,KAAK,EAACzB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;QAC3BqF,MAAM,EAAC1B,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;QAC5BsF,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;QAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;QAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;QACxCC,WAAW,EAAErC,gBAAgB,CAACqC,WAAW;QACzCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;QACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;OAErC;MACD,IAAI7B,OAAO,CAACQ,cAAc,EAAE;QAC1BR,OAAO,CAACmB,WAAW,GAAG,GAAG,GAAInB,OAAO,CAACQ,cAAc,GAAGR,OAAO,CAACM,UAAU,GAAG,GAAI;;MAEjF,IAAI,CAACzE,QAAQ,CAACgD,IAAI,CAACmB,OAAO,CAAC;;EAE3B;EAGF+B,eAAeA,CAACxD,QAAa,EAAEyD,GAAQ;IAEnC,IAAIzD,QAAQ,CAACpC,MAAM,IAAI,CAAC,EAAE;MACxB;;IAGF,KAAK,MAAMmC,OAAO,IAAIC,QAAQ,EAAE;MAC9B,IAAID,OAAO,CAACD,EAAE,IAAI,IAAI,CAAC9B,UAAU,EAAE;QACjC,IAAI,CAAC0F,qBAAqB,CAAC3D,OAAO,CAAC;;MAErC,IAAI,CAACyD,eAAe,CAACzD,OAAO,CAAC4D,UAAU,EAAE5D,OAAO,CAAC;;EAGrD;EAEA2D,qBAAqBA,CAAC1D,QAAa;IAEjC,IAAI4D,QAAQ,GAAQ5D,QAAQ,EAAE6D,WAAW,EAAErG,KAAK,CAAC,IAAI,CAAC,EAAEsG,GAAG,CAACC,MAAM,CAAC;IAEnE,IAAIC,SAAS,GAAQhE,QAAQ,EAAEiE,YAAY,EAAEzG,KAAK,CAAC,IAAI,CAAC,EAAEsG,GAAG,CAACtD,MAAM,CAAC;IAErE,IAAI0D,WAAW,GAAQ,EAAE;IACzB,IAAIN,QAAQ,CAAChG,MAAM,KAAKoG,SAAS,CAACpG,MAAM,EAAE;MACxCgG,QAAQ,EAAEE,GAAG,CAAC,CAACK,CAAM,EAAEC,CAAM,KAAI;QAC/BF,WAAW,CAAC5D,IAAI,CAAC;UAAEjF,UAAU,EAAE,YAAY,GAAG8I,CAAC,CAACE,QAAQ,EAAE;UAAElE,KAAK,EAAE6D,SAAS,CAACI,CAAC;QAAC,CAAE,CAAC;MACpF,CAAC,CAAC;MACF,IAAI,CAAClJ,UAAU,GAAGgJ,WAAW;MAC7B,IAAI,CAAC5J,GAAG,CAACuG,aAAa,EAAE;MACxB,IAAI,CAACvG,GAAG,CAACwG,YAAY,EAAE;;IAEzB;IACA,IAAI,CAAC9F,WAAW,CAACqD,YAAY,CAAC,UAAU,EAAE2B,QAAQ,EAAEiE,YAAY,CAAC;EAInE;EAEAK,MAAMA,CAAA;IACJC,cAAc,CAACC,KAAK,EAAE;IACtB,IAAI,CAAC/J,gBAAgB,CAACgK,YAAY,CAAC,EAAE,CAAC;IAGtC,IAAI,CAAC/J,aAAa,CAACgK,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAC3C,IAAI,CAACxK,KAAK,CAACyK,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAAChK,eAAe,CAACiK,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACjK,eAAe,CAACkK,WAAW,CAAC,IAAI,CAAC;IACtCC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCD,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzCD,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAClC,IAAI,CAAC7K,KAAK,CAACyK,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCG,YAAY,CAACE,UAAU,CAAC,cAAc,CAAC;IACvCF,YAAY,CAACE,UAAU,CAAC,UAAU,CAAC;EACrC;EAEArL,qBAAqBA,CAAA;IACnB,IAAI,CAACX,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACkD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,eAAe,IAAI,IAAI,CAAC/C,QAAQ;IACrC,IAAI,CAACiD,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAC7B,GAAG,CAACuG,aAAa,EAAE;IAExB,IAAI,CAAC5G,cAAc,CAChBsE,mBAAmB,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAACE,iBAAiB,CAAC,CACtGiB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC/E,6BAA6B,GAAG+E,GAAG,CAACE,IAAI,EAAEC,YAAY,CAACC,OAAO,CAACjB,MAAM,GAAG,CAAC;QAC9E,MAAMqH,oBAAoB,GAAGxG,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO;QAC1D,IAAI,CAAC5F,KAAK,GAAGgM,oBAAoB,CAACrH,MAAM;QACxCqH,oBAAoB,CAAClG,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAACC,sBAAsB,CAACD,MAAM,CAAC;QACrC,CAAC,CAAC;QACF,IAAI,CAAChG,mBAAmB,GAAG,CAACyF,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO,CAACjB,MAAM;QAChE,IAAI,CAAC1B,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC5B,GAAG,CAACwG,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEAoE,UAAUA,CAAClG,MAAU;IACnB,IAAI+B,gBAAgB;IACpB,IAAIC,cAAc,GAAGhC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACL,IAAII,eAAe,GAAGpC,MAAM,EAAEiC,gBAAgB,EAAEnE,IAAI,CAAEoE,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OACnC,MAAM;QACLL,gBAAgB,GAAG/B,MAAM,EAAEiC,gBAAgB,CAAC,CAAC,CAAC;;;IAGlD,IAAIK,QAAQ,GAAC,EAAE;IACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;MACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;IAEhE,IAAIC,OAAO,GAAO;MAChBE,SAAS,EAAE3C,MAAM,EAAEc,EAAE;MACrB8B,WAAW,EAAE5C,MAAM,EAAE6C,IAAI;MACzBE,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;MACnCC,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;MAC3CE,YAAY,EAAEpD,MAAM,EAAEoD,YAAY;MAClCC,cAAc,EAAErD,MAAM,EAAEqD,cAAc,IAAItB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC;MACpEC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;MAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;MAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;MAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;MACnCG,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;MAC9GW,SAAS,EAAE3D,MAAM,EAAE2D,SAAS;MAC5BG,KAAK,EAACxB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC3BoF,KAAK,EAACzB,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC3BqF,MAAM,EAAC1B,QAAQ,EAAE3D,QAAQ,CAAC,CAAC,CAAC;MAC5BsF,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;MAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;MAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;MACxCC,WAAW,EAAErC,gBAAgB,CAACqC,WAAW;MACzCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;MACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;KACrC;IACD,IAAI,CAAChG,QAAQ,CAACgD,IAAI,CAACmB,OAAO,CAAC;EAC7B;EAEA5H,4BAA4BA,CAAA;IAC1B,IAAI,CAACb,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACkD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,eAAe,IAAI,IAAI,CAAC/C,QAAQ;IACrC,IAAI,CAACiD,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAC7B,GAAG,CAACuG,aAAa,EAAE;IACxB,IAAI,CAAC5G,cAAc,CAChBsJ,uBAAuB,CAAC,IAAI,CAACvF,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,IAAI,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAC5FiB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACjB,MAAM,IAAI,CAAC,EAAE,IAAI,CAAChE,4BAA4B,GAAG,KAAK;QAC5E,MAAMqL,oBAAoB,GAAGxG,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACsG,KAAK,CAAC,CAAC,IAAI,CAACjM,QAAQ,CAAC;QACpE,IAAI,CAACD,KAAK,GAAGwF,GAAG,CAACE,IAAI,EAAEE,OAAO,EAAEjB,MAAM;QAEtCqH,oBAAoB,CAAClG,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAAC2B,6BAA6B,CAAC3B,MAAM,CAAC;QAC5C,CAAC,CAAC;QACF,IAAIP,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACjB,MAAM,EAAE,IAAI,CAAC5E,mBAAmB,GAAG,KAAK,CAAC,KAC1D,IAAI,CAACA,mBAAmB,GAAG,IAAI;QACpC,IAAI,CAACkD,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC5B,GAAG,CAACwG,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EACAhH,8BAA8BA,CAAA;IAC5B,IAAI,CAACd,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACkD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,eAAe,IAAI,IAAI,CAAC/C,QAAQ;IACrC,IAAI,CAACiD,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAC7B,GAAG,CAACuG,aAAa,EAAE;IACxB,IAAI,CAAC5G,cAAc,CAChBsJ,uBAAuB,CAAC,IAAI,CAACvF,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,IAAI,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAC5FiB,SAAS,CAAC;MACToB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACjB,MAAM,IAAI,CAAC,EAAE,IAAI,CAAChE,4BAA4B,GAAG,KAAK;QAC5E,MAAMqL,oBAAoB,GAAGxG,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACsG,KAAK,CAAC,CAAC,IAAI,CAACjM,QAAQ,CAAC;QACpE,IAAI,CAACD,KAAK,GAAGwF,GAAG,CAACE,IAAI,EAAEE,OAAO,EAAEjB,MAAM;QAEtCqH,oBAAoB,CAAClG,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAACkG,UAAU,CAAClG,MAAM,CAAC;QACzB,CAAC,CAAC;QACF,IAAIP,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACjB,MAAM,EAAE,IAAI,CAAC5E,mBAAmB,GAAG,KAAK,CAAC,KAC1D,IAAI,CAACA,mBAAmB,GAAG,IAAI;QACpC,IAAI,CAACkD,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC5B,GAAG,CAACwG,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAGQ3B,gBAAgBA,CAAA;IACtB,IAAIiG,aAAa,GAAQN,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;IAC9DD,aAAa,GAAGE,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;IACzC,IAAI,CAACrJ,aAAa,GAAGqJ,aAAa;IAElC,IAAI,CAACrJ,aAAa,EAAEgD,OAAO,CAAE0E,GAAQ,IAAI;MACvC,IAAIA,GAAG,CAAC3D,EAAE,IAAI,IAAI,CAAC9B,UAAU,EAAE;QAC7B,IAAI,CAAC0F,qBAAqB,CAACD,GAAG,CAAC;;MAEjCA,GAAG,CAAC,MAAM,CAAC,GAAGA,GAAG,CAACtF,YAAY;MAC9BsF,GAAG,CAAC,QAAQ,CAAC,GAAGA,GAAG,CAAC3D,EAAE;MACtB,IAAI,CAAC0D,eAAe,CAACC,GAAG,CAACE,UAAU,EAAEF,GAAG,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEA/G,WAAWA,CAAA;IACT,IAAI5E,iBAAiB,CAAC,IAAI,CAACM,UAAU,CAAC,EAAE;MACtCE,MAAM,CAACkN,QAAQ,CAAC;QAACC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;;EAEjD;EAEA5H,gBAAgBA,CAACd,IAAY,EAAE8C,EAAO;IACpC,IAAG,IAAI,CAACxD,iBAAiB,IAAK,IAAI,CAACxB,iBAAiB,CAAC6K,aAAa,CAAC3I,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,EAAC;MAC7H,IAAGA,IAAI,KAAK,WAAW,EAAE;QACvB,IAAI,CAACjC,UAAU,CAAC6K,QAAQ,CAAC,YAAY,EAAE,gBAAgB,GAAG9F,EAAE,CAAC;OAC9D,MAAM,IAAI9C,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAI,CAACjC,UAAU,CAAC6K,QAAQ,CAAC,WAAW,EAAE,eAAe,GAAG9F,EAAE,CAAC;OAC5D,MAAM,IAAI9C,IAAI,KAAK,SAAS,EAAE;QAC7B,IAAI,CAACjC,UAAU,CAAC6K,QAAQ,CAAC,WAAW,EAAE,cAAc,GAAG9F,EAAE,CAAC;;MAE5D,IAAI,CAAC/E,UAAU,CAAC5C,KAAK,CACnB,IAAI,CAACoE,OAAO,CAACsJ,MAAM,EACnB/F,EAAE,EACF9C,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,EAC1D,CAAC,EACD,IAAI,EACJ;QACE,IAAI,EAAE8C,EAAE;QACR,SAAS,EAAC,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqI,YAAY,GAAG;OAC9D,CACF;;EAEL;EACAC,eAAeA,CAACrI,GAAW;IACzB,OAAO1F,gBAAgB,CAACgO,cAAc,CAACtI,GAAG,EAAE7F,WAAW,CAAC2D,WAAW,CAAC;EACtE;CACD;AApoBUyK,UAAA,EAARtO,KAAK,EAAE,C,+CAA0B;AAiClCsO,UAAA,EADCxO,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,6CAYzC;AAEDwO,UAAA,EADCxO,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,6CASzC;AArEUQ,cAAc,GAAAgO,UAAA,EAL1BzO,SAAS,CAAC;EACT0O,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB;CACrC,CAAC,EAmGGC,OAAA,KAAA3O,MAAM,CAACE,WAAW,CAAC,E,EAlGXK,cAAc,CAmpB1B;SAnpBYA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}