{"ast": null, "code": "import { BehaviorSubject, map } from 'rxjs';\nimport { JwtHelperService } from \"@auth0/angular-jwt\";\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-cookie-service\";\nexport let AuthTokenService = /*#__PURE__*/(() => {\n  class AuthTokenService {\n    http;\n    cookieService;\n    isRefreshTokenCalled = false;\n    helper = new JwtHelperService();\n    baseUrl;\n    userController;\n    otpController;\n    authToken = new BehaviorSubject(null);\n    authTokenData = this.authToken.asObservable();\n    constructor(http, cookieService) {\n      this.http = http;\n      this.cookieService = cookieService;\n      this.baseUrl = `${environment.apiEndPoint}/Auth`;\n      this.userController = '/User';\n      this.otpController = '/OtpUser';\n    }\n    authTokenSet(message) {\n      this.authToken.next(message);\n    }\n    refreshToken(data) {\n      return this.http.post(`${this.baseUrl}${this.userController}/RefreshToken`, data).pipe(map(res => {\n        return res;\n      }));\n    }\n    static ɵfac = function AuthTokenService_Factory(t) {\n      return new (t || AuthTokenService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.CookieService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthTokenService,\n      factory: AuthTokenService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthTokenService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}