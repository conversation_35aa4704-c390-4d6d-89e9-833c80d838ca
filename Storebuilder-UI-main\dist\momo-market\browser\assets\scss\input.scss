
    .mobile-container{
        .iti__flag{
       // background-position: -5287px 0px;
       // border-radius: 50px;
       // width: 16px;
       border-radius: 50px;
       width: 40px;
       height: 19px !important;
       background-size: 5762px 20px;
       }
       .iti__selected-flag.dropdown-toggle{
            padding-top: 0;
       }
      .iti__flag-container{
       &::after{
          content: '';
          position: absolute;
           right: 1px;
           top: 5px;
           display: block;
           width: 1px;
           background: var( --light-grey);
           height: 25px;
           margin-right: 3px;
           margin: 2px;
       }
      }

    }
     input.mobile-input-phone{
    @include responsive(mobile) {
    border: none !important;
    background-color: white !important;
    padding-top: 0;
    height: 40px !important;
    font-family: var(--regular-font) !important;
    }
    @include responsive(tablet) {
      border: none !important;
      background-color: white !important;
      padding-top: 0;
      height: 40px !important;
      font-family: var(--regular-font) !important;
      }
  }
  .custom-input,
  #custom-float-input,
 #custom-password-input input{
    @include responsive(mobile) {
    border: 1px solid rgba(32, 78, 110, 0.10) !important;
    border-radius: 8px;
    background-color: white !important;
    overflow: auto;
    label{
      position: relative !important;
      padding: 1px 10px !important;
    }

}

  }
  #custom-password-input .p-password{
    display: flex;
  }
  #custom-float-input{
    height: 60px;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 20px;
    width: 100%;
  }
  .mobile-container  label{
    color: #292727;
    font-family: var(--regular-font);
    font-size: 12px !important;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin: 0px;
    padding-bottom: 0;
  }
