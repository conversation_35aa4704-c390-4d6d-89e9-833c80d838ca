import {
  Component,
  ElementRef,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MessageService, PrimeNGConfig } from 'primeng/api';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PasswordModule } from 'primeng/password';
import { InputTextModule } from 'primeng/inputtext';
import { first, tap } from 'rxjs';

import { CountryISO } from 'ngx-intl-tel-input-gg';
import {
  AppDataService,
  AuthService,
  AuthTokenService,
  CartService,
  ContactUsService,
  MainDataService,
  PermissionService,
  RegisterService,
  StoreService,
  UserService,
  CustomGAService,
} from '@core/services';
import {
  GetAllAboutUs,
  RegisterUser,
  RegisterUserByEmailRequestDTO,
  UserConsent,
} from '@core/interface';
import { GaActionEnum, GoogleAnalyticsService } from 'ngx-google-analytics';
import { UserConsentType } from '@core/enums/user';
import { ConfirmationDialogComponent } from '@shared/components/confirmation-dialog/confirmation-dialog.component';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { GTMService } from '@core/services/gtm.service';

import { NameInputComponent } from '../name-input/name-input.component';
import { EmailInputComponent } from '../email-input/email-input.component';
import { PasswordInputComponent } from '../password-input/password-input.component';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import jwt_decode from 'jwt-decode';
import { CookieService } from 'ngx-cookie-service';
import { GuidGenerator } from '../../utils/guid-generator';
import * as CryptoJS from 'crypto-js';
import { PhoneInputComponent } from '../phone-input/phone-input.component';

@Component({
  selector: 'app-register-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    ButtonModule,
    DialogModule,
    CheckboxModule,
    FormsModule,
    PasswordModule,
    InputTextModule,
    NameInputComponent,
    EmailInputComponent,
    PasswordInputComponent,
    ProgressSpinnerModule,
    PhoneInputComponent,
  ],
  providers: [DialogService],
  templateUrl: './register-details.component.html',
  styleUrls: ['./register-details.component.scss'],
})
export class RegisterDetailsComponent implements OnInit {
  // Form data
  firstName: string = '';
  lastName: string = '';
  email: string | null = null;
  password: string = '';
  confirmPassword: string = '';
  phoneNumber: any = null;
  referralBy: string = '';

  // Form validation
  isNameValid: boolean = false;
  isEmailValid: boolean = false;
  isPasswordValid: boolean = false;
  isPhoneValid: boolean = false;
  termsConditions: boolean = false;
  firstNameFlag: boolean = false;
  submitted: boolean = false;

  // Password validation flags
  hasUpperChar!: boolean;
  hasAtleastOneNumber!: boolean;
  hasLowerChar!: boolean;
  passwordMatched: boolean | undefined = undefined;
  hasMinimum8Chars!: boolean;
  hasSpecialChars!: boolean;
  passwordIsValid: boolean = false;

  // Configuration
  emailReuiredValidation: string | null = null;
  phoneInputLength: number = 20;
  customPlaceHolder: any = '';
  CustomCountryISO: any;
  preferredCountries: CountryISO[] = [
    CountryISO.Uganda,
    CountryISO.Ghana,
    CountryISO.CôteDIvoire,
    CountryISO.Albania,
    CountryISO.Egypt,
    CountryISO.UnitedArabEmirates,
  ];
  selectedRegistrationMethod: 'phone' | 'email' = 'phone';

  // Modal and UI state
  displayApprovedModal: boolean = false;
  displayTermsAndConditions: boolean = false;
  loading: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth: number = window.innerWidth;
  isGoogleAnalytics: boolean = false;
  isChecked: boolean = false;

  // Data from previous steps
  mobileNumberOrEmail: string = '';
  otpCode: string = '';

  // About Us content
  aboutUsDetails: GetAllAboutUs = {} as GetAllAboutUs;
  termsAndConditions: any;
  pageId: number = 5;
  title: string = '';

  // Other
  tagName: any = GaActionEnum;
  ref: DynamicDialogRef | undefined;
  phoneNumberMask = '000-000-000-000';
  mask: string = '999-999-999-999';
  redirctURL: any;

  cartListCount: any = 0;
  cartListData: any = [];
  products: Array<any> = [];
  counter: any = 0;
  nationalNumber: any;

  constructor(
    private messageService: MessageService,
    private el: ElementRef,
    private translate: TranslateService,
    private router: Router,
    private otpService: RegisterService,
    private aboutUsService: ContactUsService,
    private domSanitizer: DomSanitizer,
    private config: PrimeNGConfig,
    private appDataService: AppDataService,
    private $gaService: GoogleAnalyticsService,
    private permissionService: PermissionService,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: any,
    private userService: UserService,
    private dialogService: DialogService,
    private $gtmService: GTMService,
    private auth: AuthService,
    private store: StoreService,
    private mainDataService: MainDataService,
    private cookieService: CookieService,
    private authTokenService: AuthTokenService,
    private cartService: CartService,
    private customGAService: CustomGAService
  ) {
    this.setupCustomPlaceholder();
    console.log('RegisterDetailsComponent initialized');
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');
    this.isGoogleAnalytics =
      this.permissionService.hasPermission('Google Analytics');
    this.$gtmService.pushPageView('signUp', 'step 2');
    this.selectedRegistrationMethod = localStorage.getItem(
      'registrationMethod'
    ) as 'phone' | 'email';

    this.CustomCountryISO = localStorage.getItem('isoCode');
    this.emailReuiredValidation = localStorage.getItem('emailRequired');

    if (this.appDataService.configuration) {
      const emailRequired = this.appDataService.configuration.records.find(
        (item) => item.key === 'EmailRequired'
      );
      if (emailRequired) {
        this.emailReuiredValidation = emailRequired.value;
      }

      const phoneLength = this.appDataService.configuration.records.find(
        (item) => item.key === 'PhoneLength'
      );
      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
    }

    let phoneMask = localStorage.getItem('PhoneNumberMask');
    if (phoneMask) this.phoneNumberMask = phoneMask;

    let countryPhoneStorage = localStorage.getItem('countryPhone');
    let countryPhone = '256';
    if (countryPhoneStorage)
      countryPhone = countryPhoneStorage?.replace('+', '');

    this.getAboutUsDetails();

    // Get data from navigation state
    this.mobileNumberOrEmail = history.state.mobile;

    if (history?.state?.mobile) {
      let patchedPhone = history?.state?.mobile.slice(countryPhone?.length);
      // We'll handle this differently with the phone component
      this.phoneNumber = { e164Number: '+' + history.state.mobile };
    }

    this.otpCode = history.state.otp;
  }

  private setupCustomPlaceholder() {
    let tenantId = localStorage.getItem('tenantId');
    if (tenantId && tenantId !== '') {
      const placeholders: { [key: string]: string } = {
        '1': 'XXXXXXXXXX',
        '2': 'XXXXXXXXX',
        '3': 'XXXXXXXXX',
        '4': 'XXXXXXXXXX',
      };
      this.customPlaceHolder = placeholders[tenantId] || '';
    }
  }

  // Input component handlers
  onFirstNameChange(value: string) {
    this.firstName = value;
    this.checkFormValidity();
  }

  onLastNameChange(value: string) {
    this.lastName = value;
    this.checkFormValidity();
  }

  onNameValidationChange(isValid: boolean) {
    this.isNameValid = isValid;
    this.checkFormValidity();
  }

  onEmailChange(value: string | null) {
    this.email = value;
    this.checkFormValidity();
  }

  onEmailValidationChange(isValid: boolean) {
    this.isEmailValid = isValid;
    this.checkFormValidity();
  }

  onPasswordChange(value: string) {
    this.password = value;
    this.checkPasswordPattern(value);
  }

  onPasswordValidationChange(isValid: boolean) {
    // Basic validation from component
    this.checkFormValidity();
  }

  onConfirmPasswordChange(value: string) {
    this.confirmPassword = value;
    this.onChangeConfirmPassword();
  }

  onPhoneNumberChange(value: any) {
    let tenantId = localStorage.getItem('tenantId');
    let firstChar = value?.number?.charAt(0)
    let countryCode = value.countryCode
    if( countryCode == 'GH'){
      // if(firstChar == '0'){
      //   this.counter++
      // }
      // if(value?.number?.length == 1){
      //     this.messageService.add({
      //       severity: 'info',
      //       summary: "Info",
      //       detail: 'Phone number must be 9 digits or start with 0 followed by 9 digits.',
      //       life:4000
      //     });
      // }
        // if(this.nationalNumber[0] == '0' && tenantId == '2' ){
        //   this.phoneInputLength=10
          // if(value.number.length <2){
          //   this.counter = 0
          // }
        // }else{
            if (this.appDataService.configuration) {
              const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
              if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
            }
        // }
    }else if(countryCode == 'UG'){
        if (this.appDataService.configuration) {
              const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
              if (phoneLength) {
                this.phoneInputLength = parseInt(phoneLength.value) - (this.nationalNumber[0] === '0' ? 0 : 1);
              }
        }
      
    }else{
      this.phoneInputLength=20
    }
    this.phoneNumber = value;
    this.checkFormValidity();
  }
    onRawPhoneNumberChange($event: string) {
    this.nationalNumber = $event
    }

  onPhoneValidationChange(isValid: boolean) {
    this.isPhoneValid = isValid;
    this.checkFormValidity();
  }

  onBlur() {
    this.isRequired();
  }

  onReferralChange(event: any) {
    this.referralBy = event.target.value;
    if (event.target.value) {
      const firstSpaceIndex = event.target.value.indexOf(' ');
      if (firstSpaceIndex == 0) {
        event.target.value =
          event.target.value.substring(0, firstSpaceIndex) +
          event.target.value.substring(firstSpaceIndex + 1);
        this.referralBy = event.target.value;
      }
    }
  }

  checkPasswordPattern(password: any) {
    this.hasAtleastOneNumber = /\d+/.test(password);
    this.hasUpperChar = /[A-Z]+/.test(password);
    this.hasLowerChar = /[a-z]+/.test(password);
    this.hasMinimum8Chars = /.{10,}/.test(password);
    this.hasSpecialChars = /[!@#$%^&*()\-_=+\[\]{};:,.\?\/]/.test(password);

    // Only check password match if both fields have content
    if (
      this.password &&
      this.confirmPassword &&
      this.password.length > 0 &&
      this.confirmPassword.length > 0
    ) {
      if (this.password === this.confirmPassword) {
        this.passwordMatched = true;
      } else {
        this.passwordMatched = false;
      }
    } else {
      // Don't set password match validation if either field is empty
      this.passwordMatched = undefined;
    }

    this.passwordIsValid =
      this.hasMinimum8Chars &&
      this.hasUpperChar &&
      this.hasLowerChar &&
      this.hasSpecialChars &&
      this.passwordMatched === true &&
      this.hasAtleastOneNumber;

    this.checkFormValidity();

    return this.passwordIsValid;
  }

  onChangeConfirmPassword() {
    // Only check password match if both fields have content
    if (
      this.password &&
      this.confirmPassword &&
      this.password.length > 0 &&
      this.confirmPassword.length > 0
    ) {
      if (this.password === this.confirmPassword) {
        this.passwordMatched = true;
      } else {
        this.passwordMatched = false;
      }
    } else {
      // Don't set password match validation if either field is empty
      this.passwordMatched = undefined;
    }

    this.passwordIsValid =
      this.hasMinimum8Chars &&
      this.hasUpperChar &&
      this.hasLowerChar &&
      this.hasSpecialChars &&
      this.passwordMatched === true &&
      this.hasAtleastOneNumber;

    this.checkFormValidity();
  }

  private checkFormValidity() {
    // Update form validity based on all inputs
    const nameValid = this.firstName.length > 0 && this.lastName.length > 0;
    const phoneValid =
      this.isPhoneValid &&
      this.selectedRegistrationMethod === 'email' &&
      this.phoneNumber.number.length > 3;
    const basicValid =
      nameValid &&
      (phoneValid || this.selectedRegistrationMethod === 'phone') &&
      this.passwordIsValid;

    // Referral validation
    const referralValid = this.referralBy.length <= 50;

    this.submitted = basicValid && referralValid;
  }

  checkValidity() {
    if (this.submitted && this.passwordIsValid) {
      return false;
    }
    return true;
  }

  isRequired() {
    this.firstNameFlag = true;
  }
  checkSubmissionFlow(isMobile?: boolean) {
    if (this.selectedRegistrationMethod === 'phone') {
      this.submitPhoneResistration(isMobile);
    } else {
      this.submitEmailResistration();
    }
  }
  private fireRegistrationEvent(): void {
    if (this.isGoogleAnalytics) {
      this.$gaService.event(
        GaLocalActionEnum.CLICK_ON_COMPLETE_REGISTERATION,
        '',
        'SIGN_UP_STEP_2 ',
        1,
        true
      );
    }
  }
  private fireRegistrationSuccessEvent(UserName: string): void {
    if (
      this.isGoogleAnalytics &&
      this.permissionService.getTagFeature('SIGN_UP')
    ) {
      this.$gaService.event(this.tagName.SIGN_UP, '', '', 1, true, {
        user_ID: UserName,
      });
    }
  }
  private getRegisterUserByEmailDTO(
    isMobile?: boolean
  ): RegisterUserByEmailRequestDTO {
    const mobileNumber = this.phoneNumber;
    const password = this.password;

    const userName = mobileNumber.e164Number?.slice(1);
    const name = `${this.firstName} ${this.lastName}`;

    const CountryPhoneKey = localStorage.getItem('CountryPhone');
    const countryPhone = CountryPhoneKey?.replace('+', '');

    // Extract the phone number properly - get the number without country code
    const phoneNumberWithoutCountryCode = mobileNumber.e164Number?.slice(
      (countryPhone?.length || 3) + 1
    );
    return {
      UserName: this.mobileNumberOrEmail ?? '',
      Password: password,
      OTPCode: this.otpCode,
      Name: name,
      Email: this.mobileNumberOrEmail ?? '',
      phoneNumber:
        this.phoneNumber.dialCode?.replace('+', '') + this.phoneNumber.number,
      ReferralBy: this.referralBy,
      IsSubscribed: this.isChecked ?? false,
    };
  }
  private submitEmailResistration(isMobile?: boolean): void {
    this.fireRegistrationEvent();
    if (!this.password) {
      this.handleError(this.translate.instant('ErrorMessages.mobileRequired'));
      return;
    }
    const request = this.getRegisterUserByEmailDTO(isMobile);
    this.otpService.registerUserByEmail(request).subscribe({
      next: (res: any) => {
        if (res.success) {
          this.handleRegisterUserResponse(
            res,
            request?.UserName,
            request?.Password
          );

          this.fireRegistrationSuccessEvent(request?.UserName);
        } else {
          this.handleError(res.message);
        }
      },
      error: (err: any) => this.handleError(err.message),
    });
  }
  private submitPhoneResistration(isMobile?: boolean): void {
    this.fireRegistrationEvent();

    if (!this.password) {
      this.handleError(this.translate.instant('ErrorMessages.mobileRequired'));
      return;
    }
    const req = this.getRequestDto();
    this.otpService.registerUser(req).subscribe({
      next: (res: any) => {
        this.handleRegisterUserResponse(res, req.UserName, req.Password);
        this.fireRegistrationSuccessEvent(req.UserName);
      },
      error: (err: any) => this.handleError(err.message),
    });
  }
  getRequestDto(isMobile?: boolean): RegisterUser {
    const mobileNumber = this.phoneNumber;
    const password = this.password;

    const userName = mobileNumber.e164Number?.slice(1);
    const name = `${this.firstName} ${this.lastName}`;

    const CountryPhoneKey = localStorage.getItem('CountryPhone');
    const countryPhone = CountryPhoneKey?.replace('+', '');

    // Extract the phone number properly - get the number without country code
    const phoneNumberWithoutCountryCode = mobileNumber.e164Number?.slice(
      (countryPhone?.length || 3) + 1
    );

    return {
      UserName: !isMobile
        ? countryPhone + phoneNumberWithoutCountryCode
        : userName,
      Password: password,
      OTPCode: this.otpCode,
      Name: name,
      Email: this.email,
      number: !isMobile
        ? countryPhone + phoneNumberWithoutCountryCode
        : userName,
      ReferralBy: this.referralBy,
      IsSubscribed: this.isChecked ?? false,
    };
  }
  private handleRegisterUserResponse(
    res: any,
    userName: string,
    password: string
  ): void {
    if (res.success) {
      // Track successful signup event
      if (this.isGoogleAnalytics) {
        this.customGAService.signUpEvent('Phone', userName, 'register_page');
      }

      if (
        this.isGoogleAnalytics &&
        this.permissionService.getTagFeature('consumer_register')
      ) {
        this.$gaService.event('consumer_register', 'register', userName);
        // this.router.navigate(['/login']);
      }
      this.openSuccessDialog(userName, password);
      this.handleSuccess();
    } else {
      this.handleError(res.message);
    }
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.remove('overlay');
    }
  }
  private async handleLoginSuccess(res: any) {
    if (res?.success && res.data.role == 'consumer') {
      if (
        this.isGoogleAnalytics &&
        this.permissionService.getTagFeature('LOGIN')
      ) {
        this.$gaService.event(this.tagName.LOGIN, '', '', 1, true, {
          user_ID: res.data.mobileNumber,
        });
      }

      const data: UserConsent = {
        consentType: UserConsentType.Cookie,
        sessionId: localStorage.getItem('consumer-consent-sessionId') || '',
        consent: true,
        userId: res?.data?.id,
      };
      this.userService.updateUserConsent(data).subscribe({
        next: (res: any) => {},
      });
      this.setUserData(res.data);
      this.setAuthToken(res.data.authToken);

      if (res?.data?.currency) {
        this.store.set('currency', res.data.currency);
      }

      localStorage.setItem('refreshToken', res.data.refreshToken);
      this.store.set('refreshToken', res.data.refreshToken);

      const cartData: any = { sessionId: localStorage.getItem('sessionId') };
      const cartId = localStorage.getItem('cartId');
      await this.checkCart(cartData, cartId);

      this.handlePostLoginNavigation(res.data);
    } else {
      this.handleLoginFailure(res?.message);
    }
  }

  openSuccessDialog(userName: string, password: string) {
    const isMobile = window.innerWidth <= 767;
    this.ref = this.dialogService.open(ConfirmationDialogComponent, {
      width: isMobile ? '90%' : '540px',
      closable: false,
      closeOnEscape: true,
      showHeader: false,
      styleClass: 'dialog-wrapper',
      position: 'center',
      data: {
        successMsg:
          (localStorage.getItem('tenantId') ?? 1) == 1
            ? `<div style="display: flex; flex-direction: column"><div class="text-600 text-center"> <b>${this.translate.instant(
                'auth.registerPassword.RegistrationSuccessful'
              )} </b> </div>
            <div>${this.translate.instant(
              'auth.registerPassword.registerSuccessMsgUghanda'
            )} </div></div>`
            : 'auth.registerPassword.registerSuccessMsg',
      },
    });
    this.ref.onClose
      .pipe(
        tap(() => {
          this.auth
            .login({
              username: userName,
              password: password,
            })
            .subscribe({
              next: async (res: any) => {
                await this.handleLoginSuccess(res);
                this.loading = false;
              },
              error: (err: any) => {
                this.handleLoginError(err);
              },
            });
        }),
        first()
      )
      .subscribe();
  }

  private handleSuccess() {
    this.messageService.add({
      severity: 'success',
      summary: this.translate.instant('ResponseMessages.register'),
      detail: this.translate.instant('ResponseMessages.registerSuccess'),
    });
  }

  handleError(detail: string) {
    this.loading = false;
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: detail,
    });
  }

  termsAndConditionsModal() {
    this.pageId = 5;
    this.getAboutUsDetails();
    this.aboutUsService.getShopAboutUs().subscribe({
      next: (res: any) => {
        let page = res.data?.records?.filter(
          (x: any) => x.pageId == this.pageId
        );
        this.aboutUsDetails.title = page[0]?.title;
        this.aboutUsDetails.content = atob(page[0].content);
        this.displayTermsAndConditions = true;
      },
      error: (err: any) => {},
    });
  }

  closeTermsModal() {
    this.displayTermsAndConditions = false;
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.remove('overlay');
    }
  }

  reloadCurrentPage(pageId: number, title: string) {
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
      this.router.navigate(['/about-us/'], {
        queryParams: { pageId: pageId, title: title },
      })
    );
  }

  approveModal() {
    this.displayApprovedModal = true;
  }

  getAboutUsDetails(): void {
    // this.aboutUsService.getShopAboutUs().subscribe({
    //   next: (res: any) => {
    //     let page = res.data?.records?.filter(
    //       (x: any) => x.pageId == this.pageId
    //     );
    //     this.aboutUsDetails.title = page?.[0]?.title;
    //     this.aboutUsDetails.content = atob(page?.[0]?.content);
    //     this.termsAndConditions = atob(page?.[0]?.content);
    //   },
    //   error: (err: any) => {},
    // });
  }

  get labelColor(): string {
    return this.screenWidth <= 767 ? 'grey' : 'white';
  }

  get floatingLabel(): boolean {
    return this.screenWidth <= 767;
  }

  get forgotPasswordClass(): string {
    return this.screenWidth <= 767
      ? 'no-underline main-color font-size-12 bold-font mb-4 mt-4'
      : 'font-size-12 mb-4';
  }

  private setUserData(userData: any) {
    this.mainDataService.setUserData(userData);
    this.store.set('profile', userData);
    this.store.set('userPhone', userData.mobileNumber);
    localStorage.setItem('userId', userData.id);
    this.store.set('timeInterval', new Date().getTime());
  }

  private setAuthToken(authToken: string) {
    let token = authToken.replace('bearer ', '');
    const decoded = jwt_decode(token);
    let days: any = ((decoded as any).exp / (60 * 60 * 24 * 1000)).toFixed(0);

    localStorage.removeItem('visited');
    const dateNow = new Date();
    dateNow.setDate(dateNow.getDate() + parseInt(days));

    let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();
    localStorage.setItem('auth_enc', encryptedMessage);

    this.cookieService.set('authToken', token, {
      expires: dateNow,
      path: '/',
      sameSite: 'Strict',
    });

    localStorage.removeItem('isGuest');
    this.authTokenService.authTokenSet(token);
  }

  private handlePostLoginNavigation(userData: any) {
    this.router.navigate(['/']);
    this.messageService.add({
      severity: 'success',
      summary: this.translate.instant('ResponseMessages.login'),
      detail: this.translate.instant('ResponseMessages.loggedInSuccessfully'),
    });
  }

  private handleLoginError(err: any) {
    this.store.set('profile', '');
    this.loading = false;
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: err.message,
    });
    localStorage.setItem('isGuest', 'true');
  }

  private handleLoginFailure(message?: string) {
    this.store.set('profile', '');
    this.loading = false;
    this.messageService.add({
      severity: 'error',
      summary:
        message ||
        this.translate.instant('ErrorMessages.invalidUserNameOrPassword'),
    });
    localStorage.setItem('isGuest', 'true');
  }
  checkCart(cartData: any, cartId: any) {
    return new Promise<void>((resolve, reject) => {
      if (!cartData.sessionId) {
        localStorage.setItem('sessionId', GuidGenerator.newGuid());
        cartData.sessionId = localStorage.getItem('sessionId');
        this.getAllCart(cartData);
        resolve(); // Resolve the promise
      } else {
        if (cartId && cartId != '') {
          cartData.cartId = parseInt(cartId);
        } else {
          cartData.cartId = 0;
        }
        this.cartService.updateCart(cartData).subscribe({
          next: (res: any) => {
            if (res?.data?.cartItems?.length) {
              this.cartListData = res.data.cartItems;
              this.cartListCount = res.data.cartItems.length;
            }
            this.mainDataService.setCartLenghtData(this.cartListCount);
            this.mainDataService.setCartItemsData(this.cartListData);
            this.getShipmentMethodByTenantId(cartData);
            resolve(); // Resolve the promise
          },
          error: (err: any) => {
            this.cartListCount = 0;
            this.cartListData = [];
            this.mainDataService.setCartLenghtData(this.cartListCount);
            this.mainDataService.setCartItemsData(this.cartListData);
            this.getShipmentMethodByTenantId(cartData);
            reject(err);
          },
        });
      }
    });
  }
  getAllCart(data: any): void {
    this.products = [];
    let cartData: any = {
      sessionId: data.sessionId,
    };
    let applyTo = localStorage.getItem('apply-to');
    if (applyTo && applyTo != '') {
      cartData['applyTo'] = applyTo;
    }
    this.cartService.getCart(cartData).subscribe({
      next: (res: any) => {
        this.cartListCount = 0;
        this.cartListData = [];
        if (res.data?.records?.length) {
          this.cartListCount = 0;
          if (res.data.records[0].cartDetails.length) {
            this.cartListCount = res.data.records[0].cartDetails.length;
            this.cartListData = res.data.records[0].cartDetails;
          }
          if (
            res.data.records[0].cartDetailsDPay &&
            res.data.records[0].cartDetailsDPay.length
          ) {
            this.cartListCount += res.data.records[0].cartDetailsDPay.length;
            this.cartListData = this.cartListData.concat(
              res.data.records[0].cartDetailsDPay
            );
          }
          this.mainDataService._cartItemshDataAfterLoginIn.next(
            this.cartListData
          );
          this.mainDataService.setCartLenghtData(this.cartListCount);
          this.mainDataService.setCartItemsData(this.cartListData);
        } else {
          this.mainDataService.setCartLenghtData(0);
          this.mainDataService.setCartItemsData([]);
        }
      },
    });
  }
  getShipmentMethodByTenantId(data: any) {
    if (this.permissionService.hasPermission('Shipment-Fee')) {
      this.cartService.getShipmentMethodByTenantId().subscribe((res: any) => {
        if (res.success && res.data.length) {
          // this.applyTo = res.data[0].applyTo
          localStorage.setItem('apply-to', res.data[0].applyTo);
          this.getAllCart(data);
        }
      });
    } else {
      localStorage.setItem('apply-to', '2');
      this.getAllCart(data);
    }
  }
}
