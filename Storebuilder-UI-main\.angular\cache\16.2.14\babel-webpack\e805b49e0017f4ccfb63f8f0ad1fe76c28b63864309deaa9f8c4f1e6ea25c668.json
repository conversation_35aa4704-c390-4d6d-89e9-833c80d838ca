{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID, EventEmitter } from '@angular/core';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport { CountryISO } from '../../models/CountryISO.enum';\nimport { GaActionEnum } from 'ngx-google-analytics';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { TenantRecords } from '@core/interface';\nimport { UserConsentType } from '../../models/UserConsentType.enum';\nimport jwt_decode from 'jwt-decode';\nimport { GuidGenerator } from '../../utils/guid-generator';\nimport { PhoneInputComponent } from \"../../components/phone-input/phone-input.component\";\nimport { SignInUpHeaderComponent } from \"../../../../shared/components/sign-in-up-header/sign-in-up-header.component\";\nimport { PasswordInputComponent } from \"../../components/password-input/password-input.component\";\nimport { ButtonModule } from 'primeng/button';\nimport * as CryptoJS from 'crypto-js';\nimport { DialogModule } from 'primeng/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/gtm.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/dialog\";\nfunction RegisterUserModalComponent_ng_template_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7);\n    i0.ɵɵelement(4, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9);\n    i0.ɵɵelement(6, \"sign-in-up-header\", 10);\n    i0.ɵɵelementStart(7, \"div\", 11)(8, \"p\", 12);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 13);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"form\", 14)(15, \"div\", 15)(16, \"app-phone-input\", 16);\n    i0.ɵɵlistener(\"phoneNumberChange\", function RegisterUserModalComponent_ng_template_1_div_1_Template_app_phone_input_phoneNumberChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPhoneNumberChange($event));\n    })(\"validationChange\", function RegisterUserModalComponent_ng_template_1_div_1_Template_app_phone_input_validationChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onPhoneValidationChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"app-password-input\", 17);\n    i0.ɵɵlistener(\"passwordChange\", function RegisterUserModalComponent_ng_template_1_div_1_Template_app_password_input_passwordChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onPasswordChange($event));\n    })(\"validationChange\", function RegisterUserModalComponent_ng_template_1_div_1_Template_app_password_input_validationChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.onPasswordValidationChange($event));\n    })(\"forgotPasswordClick\", function RegisterUserModalComponent_ng_template_1_div_1_Template_app_password_input_forgotPasswordClick_17_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.resetPassword());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function RegisterUserModalComponent_ng_template_1_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.login());\n    });\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 19);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementStart(23, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterUserModalComponent_ng_template_1_div_1_Template_a_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.reloadCurrentPage(171, \"Terms and Conditions\"));\n    });\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementStart(28, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function RegisterUserModalComponent_ng_template_1_div_1_Template_a_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.reloadCurrentPage(170, \"Privacy policy\"));\n    });\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"title\", \"signIn.signIn\")(\"img\", \"assets/images/new-signin.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 20, \"signIn.signIn\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 22, \"signIn.content\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"alreadyAddedPhoneNumber\", ctx_r1.phoneNumber)(\"maxLength\", ctx_r1.phoneInputLength)(\"selectedCountryISO\", ctx_r1.customCountryISO)(\"preferredCountries\", ctx_r1.preferredCountries)(\"placeholder\", ctx_r1.customPlaceHolder);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"showForgotPassword\", true)(\"forgotPasswordClass\", ctx_r1.forgotPasswordClass)(\"floatingLabel\", ctx_r1.floatingLabelEnabled)(\"feedback\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isFormValid)(\"label\", i0.ɵɵpipeBind1(19, 24, ctx_r1.buttonLabel));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 26, \"signIn.AgreeTermsOne\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 28, \"signIn.AgreeTermsTwo\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(27, 30, \"signIn.AgreeTermsThree\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 32, \"signIn.AgreeTermsFour\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(32, 34, \"signIn.AgreeTermsFive\"), \". \");\n  }\n}\nfunction RegisterUserModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 2);\n    i0.ɵɵtemplate(1, RegisterUserModalComponent_ng_template_1_div_1_Template, 33, 36, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayModal);\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class RegisterUserModalComponent {\n  constructor(store, auth, messageService, router, cartService, translate, cookieService, authTokenService, route, mainDataService, permissionService, loaderService, appDataService, $gaService, userService, platformId, $gtmService) {\n    this.store = store;\n    this.auth = auth;\n    this.messageService = messageService;\n    this.router = router;\n    this.cartService = cartService;\n    this.translate = translate;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.route = route;\n    this.mainDataService = mainDataService;\n    this.permissionService = permissionService;\n    this.loaderService = loaderService;\n    this.appDataService = appDataService;\n    this.$gaService = $gaService;\n    this.userService = userService;\n    this.platformId = platformId;\n    this.$gtmService = $gtmService;\n    this.displayModal = false;\n    this.phoneNumber = null;\n    this.password = '';\n    this.isPhoneValid = false;\n    this.isPasswordValid = false;\n    this.phoneInputLength = 12;\n    this.customPlaceHolder = '';\n    this.preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n    this.submitted = false;\n    this.cartListCount = 0;\n    this.cartListData = [];\n    this.isGoogleAnalytics = false;\n    this.isMobileLayout = false;\n    this.tagName = GaActionEnum;\n    this.products = [];\n    this.cancel = new EventEmitter();\n  }\n  ngOnInit() {\n    console.log('SignInComponent initialized');\n    this.setupPermissions();\n    this.setupCountryISO();\n    this.setupRouteParams();\n    this.setupPhoneInputLength();\n    this.$gtmService.pushPageView('signIn');\n  }\n  initializeComponent() {\n    this.setupCustomPlaceholder();\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  setupPermissions() {\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n  }\n  setupCountryISO() {\n    if (!localStorage.getItem(\"isoCode\")) {\n      const tenants = this.appDataService.tenants;\n      if (tenants.records != undefined) {\n        let tenantId = localStorage.getItem('tenantId');\n        let data = tenants.records;\n        let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n        localStorage.setItem('isoCode', arr?.isoCode);\n        this.store.set('allCountryTenants', tenants.records);\n      }\n    } else {\n      this.customCountryISO = localStorage.getItem(\"isoCode\");\n    }\n  }\n  setupRouteParams() {\n    this.route.queryParams.subscribe(params => {\n      this.redirctURL = params.returnUrl;\n    });\n  }\n  setupPhoneInputLength() {\n    if (this.appDataService.configuration) {\n      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n    }\n  }\n  setupCustomPlaceholder() {\n    let tenantId = localStorage.getItem('tenantId');\n    if (tenantId && tenantId !== '') {\n      const placeholders = {\n        '1': 'XXXXXXXXX',\n        '2': 'XXXXXXXXX',\n        '3': 'XXXXXXXXX',\n        '4': 'XXXXXXXXXX'\n      };\n      this.customPlaceHolder = placeholders[tenantId] || '';\n    }\n  }\n  get isFormValid() {\n    return this.isPhoneValid && this.isPasswordValid && this.password.length > 0;\n  }\n  onPhoneNumberChange(phoneNumber) {\n    this.phoneNumber = phoneNumber;\n  }\n  onPhoneValidationChange(isValid) {\n    this.isPhoneValid = isValid;\n  }\n  onPasswordChange(password) {\n    this.password = password;\n  }\n  onPasswordValidationChange(isValid) {\n    this.isPasswordValid = isValid;\n  }\n  login() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.isGoogleAnalytics) {\n        _this.$gaService.event(GaLocalActionEnum.CLICK_ON_CONTINUE_FOR_SIGN_IN, '', 'SIGN_IN_STEP2', 1, true);\n      }\n      _this.loaderService.show();\n      _this.submitted = true;\n      _this.auth.login({\n        username: _this.phoneNumber.e164Number.slice(1),\n        password: _this.password\n      }).subscribe({\n        next: function () {\n          var _ref = _asyncToGenerator(function* (res) {\n            yield _this.handleLoginSuccess(res);\n          });\n          return function next(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        error: err => {\n          _this.handleLoginError(err);\n        }\n      });\n    })();\n  }\n  handleLoginSuccess(res) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (res?.success && res.data.role == 'consumer') {\n        if (_this2.isGoogleAnalytics && _this2.permissionService.getTagFeature('LOGIN')) {\n          _this2.$gaService.event(_this2.tagName.LOGIN, '', '', 1, true, {\n            \"user_ID\": res.data.mobileNumber\n          });\n        }\n        yield _this2.updateUserConsent(res.data.id);\n        _this2.setUserData(res.data);\n        _this2.setAuthToken(res.data.authToken);\n        _this2.loaderService.hide();\n        if (res?.data?.currency) {\n          _this2.store.set('currency', res.data.currency);\n        }\n        localStorage.setItem('refreshToken', res.data.refreshToken);\n        _this2.store.set('refreshToken', res.data.refreshToken);\n        const cartData = {\n          sessionId: localStorage.getItem('sessionId')\n        };\n        const cartId = localStorage.getItem('cartId');\n        yield _this2.checkCart(cartData, cartId);\n        _this2.handlePostLoginNavigation(res.data);\n      } else {\n        _this2.handleLoginFailure(res?.message);\n      }\n    })();\n  }\n  updateUserConsent(userId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const data = {\n        consentType: UserConsentType.Cookie,\n        sessionId: localStorage.getItem('consumer-consent-sessionId') || '',\n        consent: true,\n        userId: userId\n      };\n      _this3.userService.updateUserConsent(data).subscribe({\n        next: res => {}\n      });\n    })();\n  }\n  setUserData(userData) {\n    this.mainDataService.setUserData(userData);\n    this.store.set('profile', userData);\n    this.store.set('userPhone', userData.mobileNumber);\n    localStorage.setItem('userId', userData.id);\n    this.store.set('timeInterval', new Date().getTime());\n  }\n  setAuthToken(authToken) {\n    let token = authToken.replace('bearer ', '');\n    const decoded = jwt_decode(token);\n    let days = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n    localStorage.removeItem('visited');\n    const dateNow = new Date();\n    dateNow.setDate(dateNow.getDate() + parseInt(days));\n    let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();\n    localStorage.setItem('auth_enc', encryptedMessage);\n    this.cookieService.set('authToken', token, {\n      expires: dateNow,\n      path: '/',\n      sameSite: 'Strict'\n    });\n    localStorage.removeItem('isGuest');\n    this.authTokenService.authTokenSet(token);\n  }\n  handlePostLoginNavigation(userData) {\n    if (userData.isPasswodExpired) {\n      this.router.navigateByUrl('/change-password');\n      this.messageService.add({\n        severity: 'info',\n        summary: this.translate.instant('ResponseMessages.changePassword'),\n        detail: this.translate.instant('ResponseMessages.passwordExpirationChange')\n      });\n    } else {\n      if (this.redirctURL) {\n        this.router.navigate([this.redirctURL]);\n        this.redirctURL = null;\n      } else {\n        this.router.navigate(['/']);\n      }\n      this.messageService.add({\n        severity: 'success',\n        summary: this.translate.instant('ResponseMessages.login'),\n        detail: this.translate.instant('ResponseMessages.loggedInSuccessfully')\n      });\n    }\n  }\n  handleLoginError(err) {\n    this.store.set('profile', '');\n    this.loaderService.hide();\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: err.message\n    });\n    localStorage.setItem('isGuest', 'true');\n  }\n  handleLoginFailure(message) {\n    this.store.set('profile', '');\n    this.loaderService.hide();\n    this.messageService.add({\n      severity: 'error',\n      summary: message || this.translate.instant('ErrorMessages.invalidUserNameOrPassword')\n    });\n    localStorage.setItem('isGuest', 'true');\n  }\n  resetPassword() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_FORGOT_PASSWORD, '', 'FORGOT_PASSWORD', 1, true);\n    }\n    this.router.navigate(['/reset-password']);\n  }\n  reloadCurrentPage(pageId, title) {\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/about-us/'], {\n      queryParams: {\n        pageId: pageId,\n        title: title\n      }\n    }));\n  }\n  getAllCart(data) {\n    this.products = [];\n    let cartData = {\n      sessionId: data.sessionId\n    };\n    let applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo != '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getCart(cartData).subscribe({\n      next: res => {\n        this.cartListCount = 0;\n        this.cartListData = [];\n        if (res.data?.records?.length) {\n          this.cartListCount = 0;\n          if (res.data.records[0].cartDetails.length) {\n            this.cartListCount = res.data.records[0].cartDetails.length;\n            this.cartListData = res.data.records[0].cartDetails;\n          }\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n          }\n          this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData);\n          this.mainDataService.setCartLenghtData(this.cartListCount);\n          this.mainDataService.setCartItemsData(this.cartListData);\n        } else {\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n        }\n      }\n    });\n  }\n  compareCartProducts(products, storeProducts) {\n    if (products.length) {\n      products.forEach(item => {\n        storeProducts.forEach(data => {\n          if (item.specsProductId === data.specsProductId) {\n            this.products.push(item);\n          }\n        });\n      });\n    } else {\n      this.products = storeProducts;\n    }\n    this.store.set('cartProducts', this.products);\n    localStorage.setItem('addedProducts', JSON.stringify(this.products));\n  }\n  getShipmentMethodByTenantId(data) {\n    if (this.permissionService.hasPermission('Shipment-Fee')) {\n      this.cartService.getShipmentMethodByTenantId().subscribe(res => {\n        if (res.success && res.data.length) {\n          // this.applyTo = res.data[0].applyTo\n          localStorage.setItem('apply-to', res.data[0].applyTo);\n          this.getAllCart(data);\n        }\n      });\n    } else {\n      localStorage.setItem('apply-to', '2');\n      this.getAllCart(data);\n    }\n  }\n  checkCart(cartData, cartId) {\n    return new Promise((resolve, reject) => {\n      if (!cartData.sessionId) {\n        localStorage.setItem('sessionId', GuidGenerator.newGuid());\n        cartData.sessionId = localStorage.getItem('sessionId');\n        this.getAllCart(cartData);\n        resolve(); // Resolve the promise\n      } else {\n        if (cartId && cartId != '') {\n          cartData.cartId = parseInt(cartId);\n        } else {\n          cartData.cartId = 0;\n        }\n        this.cartService.updateCart(cartData).subscribe({\n          next: res => {\n            if (res?.data?.cartItems?.length) {\n              this.cartListData = res.data.cartItems;\n              this.cartListCount = res.data.cartItems.length;\n            }\n            this.mainDataService.setCartLenghtData(this.cartListCount);\n            this.mainDataService.setCartItemsData(this.cartListData);\n            this.getShipmentMethodByTenantId(cartData);\n            resolve(); // Resolve the promise\n          },\n\n          error: err => {\n            this.cartListCount = 0;\n            this.cartListData = [];\n            this.mainDataService.setCartLenghtData(this.cartListCount);\n            this.mainDataService.setCartItemsData(this.cartListData);\n            this.getShipmentMethodByTenantId(cartData);\n            reject(err);\n          }\n        });\n      }\n    });\n  }\n  get forgotPasswordClass() {\n    return 'font-size-12';\n  }\n  get floatingLabelEnabled() {\n    return false;\n  }\n  get buttonLabel() {\n    return 'signIn.continue';\n  }\n}\nRegisterUserModalComponent.ɵfac = function RegisterUserModalComponent_Factory(t) {\n  return new (t || RegisterUserModalComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i7.GTMService));\n};\nRegisterUserModalComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: RegisterUserModalComponent,\n  selectors: [[\"app-register-user-modal\"]],\n  inputs: {\n    displayModal: \"displayModal\",\n    phoneNumber: \"phoneNumber\"\n  },\n  outputs: {\n    cancel: \"cancel\"\n  },\n  standalone: true,\n  features: [i0.ɵɵStandaloneFeature],\n  decls: 2,\n  vars: 8,\n  consts: [[3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"showHeader\", \"modal\", \"resizable\", \"visibleChange\"], [\"pTemplate\", \"content\"], [1, \"login\"], [\"class\", \"login-content-container mt-3\", 4, \"ngIf\"], [1, \"login-content-container\", \"mt-3\"], [1, \"grid\", \"justify-content-between\", \"mobile-top\"], [1, \"shadow-signin\"], [1, \"col-12\", \"image\"], [\"src\", \"assets/images/new-signin.svg\", \"alt\", \"\", \"srcset\", \"\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"header-body\", 2, \"line-height\", \"1.5\"], [1, \"mobile-only\", 3, \"title\", \"img\"], [1, \"desktop-only\"], [1, \"signin-heading\"], [1, \"signIn-content\"], [\"autocomplete\", \"new-password\"], [1, \"p-fluid\", \"p-grid\"], [3, \"alreadyAddedPhoneNumber\", \"maxLength\", \"selectedCountryISO\", \"preferredCountries\", \"placeholder\", \"phoneNumberChange\", \"validationChange\"], [3, \"showForgotPassword\", \"forgotPasswordClass\", \"floatingLabel\", \"feedback\", \"passwordChange\", \"validationChange\", \"forgotPasswordClick\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"sign-in-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"signin-agreement\"], [3, \"click\"]],\n  template: function RegisterUserModalComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p-dialog\", 0);\n      i0.ɵɵlistener(\"visibleChange\", function RegisterUserModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n        return ctx.displayModal = $event;\n      });\n      i0.ɵɵtemplate(1, RegisterUserModalComponent_ng_template_1_Template, 2, 1, \"ng-template\", 1);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(7, _c0))(\"dismissableMask\", true)(\"draggable\", false)(\"showHeader\", false)(\"modal\", true)(\"resizable\", false);\n    }\n  },\n  dependencies: [CommonModule, i8.NgIf, RouterModule, ButtonModule, i9.ButtonDirective, i2.PrimeTemplate, PhoneInputComponent, SignInUpHeaderComponent, PasswordInputComponent, TranslateModule, i4.TranslatePipe, DialogModule, i10.Dialog],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.login[_ngcontent-%COMP%]     button.back-btn {\\n  position: relative !important;\\n}\\n.login   [_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container {\\n  pointer-events: none !important;\\n}\\n@media (min-width: 768px) {\\n  .login   [_nghost-%COMP%]     button.back-btn {\\n    top: 122px !important;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .login-content-container[_ngcontent-%COMP%] {\\n  padding: 8px;\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  display: flex;\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    border: 1px solid rgba(32, 78, 110, 0.1019607843);\\n    border-radius: 8px;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .mobile-btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 12px 24px;\\n  height: 48px;\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: end;\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  width: 300px;\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  padding: 20px;\\n}\\n@media screen and (min-width: 768px) {\\n  .login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%] {\\n    padding-left: 3rem;\\n    padding-right: 3rem;\\n    padding-top: 1.5rem;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%] {\\n    padding: 0;\\n    max-width: 100%;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%] {\\n  color: #212121;\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin-bottom: 16px;\\n}\\n@media screen and (min-width: 768px) {\\n  .login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%] {\\n    font-size: 26px;\\n    font-weight: 700;\\n    font-family: var(--medium-font) !important;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signIn-content[_ngcontent-%COMP%] {\\n  color: #443F3F;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 130%;\\n  font-family: var(--regular-font) !important;\\n}\\n.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  padding: 8px 0;\\n  font-size: 12px !important;\\n  font-weight: 400;\\n  color: #2D2D2D;\\n  line-height: 20px;\\n}\\n.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n@media screen and (min-width: 768px) {\\n  .login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media screen and (min-width: 768px) {\\n  .login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%] {\\n  height: 56px;\\n  border-radius: 8px;\\n  background-color: #204e6e;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 12px 24px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n}\\n@media screen and (min-width: 768px) {\\n  .login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%] {\\n    margin: 0.5rem 0;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%] {\\n    justify-content: center !important;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 20px !important;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .mobile-container[_ngcontent-%COMP%] {\\n    overflow: hidden;\\n    margin-top: 0;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%] {\\n  gap: 8px;\\n  padding: 8px 0px;\\n}\\n.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #272727;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  font-family: var(--regular-font) !important;\\n}\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    color: var(--main-color);\\n    font-weight: bold;\\n    margin-top: 1rem;\\n    margin-bottom: 1rem;\\n  }\\n}\\n.login[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  color: #323232;\\n  font-weight: 700;\\n  border-radius: 5px;\\n}\\n.login[_ngcontent-%COMP%]   .p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #323232;\\n}\\n.login[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: #e8effd;\\n  color: #204e6e;\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-align: center;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  display: block;\\n  margin: 16px auto;\\n}\\n.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #272727;\\n  margin-top: 16px;\\n}\\n.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #204e6e;\\n  text-decoration: underline;\\n}\\n\\n@media screen and (max-width: 767px) {\\n  .login[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n  .customClass input {\\n  height: 50px !important;\\n  width: 100%;\\n  border-radius: 2px;\\n  opacity: 1;\\n  border: none !important;\\n  border: 1px solid #ccc !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  padding-top: 20px;\\n  background-color: #fff !important;\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  color: red;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\\n\\n.signin-agreement[_ngcontent-%COMP%] {\\n  color: #272727;\\n  font-family: var(--regular-font) !important;\\n}\\n.signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #204E6E;\\n  font-weight: 400;\\n  font-size: 12px;\\n  text-decoration: underline !important;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container {\\n  pointer-events: none !important;\\n}\\n\\na[_ngcontent-%COMP%]:hover {\\n  color: var(--header_bgcolor);\\n}\\n\\n.new-customer-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #373636;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 6px;\\n  white-space: nowrap;\\n  font-family: var(--regular-font) !important;\\n}\\n.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before, .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  flex-grow: 1;\\n  display: inline-block;\\n  width: 90px;\\n  height: 1px;\\n  background-color: #204E6E;\\n  opacity: 0.1;\\n}\\n.new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n  background-color: #E8EFFD;\\n  color: #204E6E;\\n  font-weight: 500;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  border: none;\\n  margin: 16px 0;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n[_nghost-%COMP%]     .contact-input-phone {\\n  background-color: white !important;\\n  border: 1px solid #ccc !important;\\n  border-radius: 4px;\\n  padding: 10px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n[_nghost-%COMP%]     .iti__selected-flag {\\n  padding: 0px 6px 0px 6px;\\n}\\n\\n[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 15px;\\n  left: 93px;\\n  width: 1px;\\n  height: 50%;\\n  background-color: #9CA69C;\\n  transform: translateX(-50%);\\n  pointer-events: none;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}