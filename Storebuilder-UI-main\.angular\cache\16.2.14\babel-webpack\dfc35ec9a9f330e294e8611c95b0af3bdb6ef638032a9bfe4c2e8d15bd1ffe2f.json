{"ast": null, "code": "import { environment } from \"@environments/environment\";\nimport { map } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class HomeService {\n  http;\n  baseUrl;\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}`;\n  }\n  getSectionMapping() {\n    return this.http.get(`${this.baseUrl}/Tenant/ShowRoomConfiguration/GetTenantShowRoomConfiguration`);\n  }\n  getMainSliders(params) {\n    return this.http.get(`${this.baseUrl}/Tenant/MainBanner/GetAllMainBanner`, {\n      params\n    });\n  }\n  getBanners(id) {\n    return this.http.get(`${this.baseUrl}/Tenant/Banner/GetBannerById/${id}`);\n  }\n  fetchOffers() {\n    return this.http.get(`${this.baseUrl}/Tenant/Banner/GetAllOffers`);\n  }\n  getAppDataVersion(tenantId) {\n    return this.http.get(`${this.baseUrl}/TenantConfiguration/GetApiVersionIntegrationbyTenant?TenantId=${tenantId}&deviceType=2`).pipe(map(res => {\n      if (res.data) return res.data;\n      return null;\n    }));\n  }\n  static ɵfac = function HomeService_Factory(t) {\n    return new (t || HomeService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HomeService,\n    factory: HomeService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}