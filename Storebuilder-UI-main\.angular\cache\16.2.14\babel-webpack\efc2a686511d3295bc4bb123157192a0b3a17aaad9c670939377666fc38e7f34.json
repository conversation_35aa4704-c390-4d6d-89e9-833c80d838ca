{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function distinct(keySelector, flushes) {\n  return operate((source, subscriber) => {\n    const distinctKeys = new Set();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const key = keySelector ? keySelector(value) : value;\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes && innerFrom(flushes).subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n  });\n}\n//# sourceMappingURL=distinct.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}