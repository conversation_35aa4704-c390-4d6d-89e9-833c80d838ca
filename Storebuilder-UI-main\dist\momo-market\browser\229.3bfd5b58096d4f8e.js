"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[229],{9261:(L,u,s)=>{s.r(u),s.d(u,{RegisterOtpComponent:()=>Z});var l=s(6814),c=s(6663),h=s(707),_=s(7680),f=s(9566),t=s(5879),p=s(6223);const C=["otpInput"];function O(r,m){if(1&r){const n=t.EpF();t.TgZ(0,"button",7),t.NdJ("click",function(){t.CHM(n);const e=t.oxw();return t.KtG(e.onResendClick())}),t._uU(1),t.ALo(2,"translate"),t.qZA()}2&r&&(t.xp6(1),t.hij(" ",t.lcZ(2,1,"auth.otp.resend")," "))}function v(r,m){if(1&r&&(t.TgZ(0,"span",8),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&r){const n=t.oxw();t.xp6(1),t.AsE(" ",t.lcZ(2,2,"auth.otp.resendIn")," 0:",n.timeLeftDisplay," ")}}const d=function(){return{standalone:!0}};let M=(()=>{class r{cd;otpComplete=new t.vpe;resendOtp=new t.vpe;digitChange=new t.vpe;otpInputs;value1="";value2="";value3="";value4="";timeLeft=60;timeLeftDisplay="60";interval;constructor(n){this.cd=n}ngOnInit(){this.startTimer()}ngOnDestroy(){this.interval&&clearInterval(this.interval)}startTimer(){this.timeLeft=60,this.timeLeftDisplay="60",this.interval&&clearInterval(this.interval),this.interval=setInterval(()=>{this.timeLeft>0?(this.timeLeft--,this.timeLeftDisplay=this.timeLeft<10?`0${this.timeLeft}`:this.timeLeft.toString()):(clearInterval(this.interval),this.timeLeftDisplay="00"),this.cd.detectChanges()},1e3)}onDigitInput(n,i){const e=n.target,o=e.value;if(/^\d*$/.test(o)){switch(i){case 1:this.value1=o;break;case 2:this.value2=o;break;case 3:this.value3=o;break;case 4:this.value4=o}this.cd.detectChanges(),o&&i<4&&this.focusInput(i+1),this.checkOtpComplete()}else e.value=""}onKeyDown(n,i){"Backspace"===n.code&&!n.target.value&&i>1&&this.focusInput(i-1),n.ctrlKey&&"KeyV"===n.code&&(n.preventDefault(),this.handlePaste(n))}onPaste(n){n.preventDefault(),this.handlePaste(n)}handlePaste(n){const e=(n.clipboardData?.getData("text")||"").replace(/\D/g,"").slice(0,4);if(e.length>0){this.value1=e[0]||"",this.value2=e[1]||"",this.value3=e[2]||"",this.value4=e[3]||"";const o=Math.min(e.length+1,4);this.focusInput(o),this.checkOtpComplete()}}focusInput(n){setTimeout(()=>{const i=this.otpInputs.toArray();i[n-1]&&i[n-1].nativeElement.focus()},0)}checkOtpComplete(){this.value1&&this.value2&&this.value3&&this.value4&&this.otpComplete.emit(this.value1+this.value2+this.value3+this.value4)}onResendClick(){this.value1="",this.value2="",this.value3="",this.value4="",this.cd.detectChanges(),this.resendOtp.emit(),this.startTimer(),this.focusInput(1)}get isComplete(){return!!(this.value1&&this.value2&&this.value3&&this.value4)}get isTimerExpired(){return 0===this.timeLeft}static \u0275fac=function(i){return new(i||r)(t.Y36(t.sBO))};static \u0275cmp=t.Xpm({type:r,selectors:[["app-otp-input"]],viewQuery:function(i,e){if(1&i&&t.Gf(C,5),2&i){let o;t.iGM(o=t.CRH())&&(e.otpInputs=o)}},outputs:{otpComplete:"otpComplete",resendOtp:"resendOtp",digitChange:"digitChange"},standalone:!0,features:[t.jDz],decls:13,vars:14,consts:[[1,"otp-container"],[1,"form-container"],["placeholder","_","type","text","maxlength","1","autocomplete","off","inputmode","numeric",3,"ngModel","ngModelOptions","ngModelChange","keydown","input","paste"],["otpInput",""],[1,"count"],["type","button","style","background: white; border: none; cursor: pointer","class","resend",3,"click",4,"ngIf"],["class","time-left",4,"ngIf"],["type","button",1,"resend",2,"background","white","border","none","cursor","pointer",3,"click"],[1,"time-left"]],template:function(i,e){1&i&&(t.TgZ(0,"div",0)(1,"form",1)(2,"input",2,3),t.NdJ("ngModelChange",function(a){return e.value1=a})("keydown",function(a){return e.onKeyDown(a,1)})("input",function(a){return e.onDigitInput(a,1)})("paste",function(a){return e.onPaste(a)}),t.qZA(),t.TgZ(4,"input",2,3),t.NdJ("ngModelChange",function(a){return e.value2=a})("keydown",function(a){return e.onKeyDown(a,2)})("input",function(a){return e.onDigitInput(a,2)})("paste",function(a){return e.onPaste(a)}),t.qZA(),t.TgZ(6,"input",2,3),t.NdJ("ngModelChange",function(a){return e.value3=a})("keydown",function(a){return e.onKeyDown(a,3)})("input",function(a){return e.onDigitInput(a,3)})("paste",function(a){return e.onPaste(a)}),t.qZA(),t.TgZ(8,"input",2,3),t.NdJ("ngModelChange",function(a){return e.value4=a})("keydown",function(a){return e.onKeyDown(a,4)})("input",function(a){return e.onDigitInput(a,4)})("paste",function(a){return e.onPaste(a)}),t.qZA()(),t.TgZ(10,"div",4),t.YNc(11,O,3,3,"button",5),t.YNc(12,v,3,4,"span",6),t.qZA()()),2&i&&(t.xp6(2),t.Q6J("ngModel",e.value1)("ngModelOptions",t.DdM(10,d)),t.xp6(2),t.Q6J("ngModel",e.value2)("ngModelOptions",t.DdM(11,d)),t.xp6(2),t.Q6J("ngModel",e.value3)("ngModelOptions",t.DdM(12,d)),t.xp6(2),t.Q6J("ngModel",e.value4)("ngModelOptions",t.DdM(13,d)),t.xp6(3),t.Q6J("ngIf",e.isTimerExpired),t.xp6(1),t.Q6J("ngIf",!e.isTimerExpired))},dependencies:[l.ez,l.O5,p.u5,p._Y,p.Fj,p.JJ,p.JL,p.nD,p.On,p.F,c.aw,c.X$],styles:["form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{position:relative;display:inline-block;width:60px;height:60px;text-align:center;margin:0 .8rem;background:#F5F5F5!important;font-size:30px;font-weight:400;font-family:var(--medium-font)!important;border-radius:8px;border:1px solid transparent}.form-container[_ngcontent-%COMP%]{margin-top:1rem;display:flex;justify-content:center;align-items:center}.otp-heading[_ngcontent-%COMP%]{font-size:20px;font-weight:500;font-family:var(--medium-font)!important;color:#212121;line-height:100%}.sent-otp[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#443f3f;font-family:var(--regular-font)!important;margin-top:.5rem}.count[_ngcontent-%COMP%]{text-align:center;font-size:small;margin:1rem 0}.resend[_ngcontent-%COMP%]{cursor:pointer;font-size:14px;font-weight:800;font-family:var(--medium-font)!important}.time-left[_ngcontent-%COMP%]{font-size:14px;font-weight:400;font-family:var(--regular-font)!important;color:#204e6e}@media screen and (max-width: 768px){form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin:0 .4rem!important;width:50px;height:50px}.otp-heading[_ngcontent-%COMP%]{font-size:18px}.sent-otp[_ngcontent-%COMP%], .time-left[_ngcontent-%COMP%], .resend[_ngcontent-%COMP%]{font-size:12px}}"]})}return r})();var x=s(5219),y=s(6075),g=s(864),b=s(5662),P=s(9147);function w(r,m){1&r&&(t.TgZ(0,"div",3),t._UZ(1,"p-progressSpinner"),t.qZA())}const I=function(r){return{"mobile-content-container":r}};function T(r,m){if(1&r){const n=t.EpF();t.ynx(0),t.TgZ(1,"div",4)(2,"div",5)(3,"div",6),t._UZ(4,"img",7),t.qZA(),t.TgZ(5,"div",8)(6,"div",9),t._UZ(7,"img",10),t.TgZ(8,"div",11)(9,"h2",12),t._uU(10),t.ALo(11,"translate"),t.qZA(),t.TgZ(12,"span",13),t._uU(13),t.ALo(14,"translate"),t.qZA()()(),t.TgZ(15,"div",14)(16,"div",15)(17,"h2"),t._uU(18),t.ALo(19,"translate"),t.qZA(),t.TgZ(20,"p"),t._uU(21),t.ALo(22,"translate"),t.qZA()(),t.TgZ(23,"div",16)(24,"div",17)(25,"app-otp-input",18,19),t.NdJ("otpComplete",function(e){t.CHM(n);const o=t.oxw();return t.KtG(o.onOtpComplete(e))})("resendOtp",function(){t.CHM(n);const e=t.oxw();return t.KtG(e.resendOtp())}),t.qZA()(),t.TgZ(27,"button",20),t.NdJ("click",function(){t.CHM(n);const e=t.oxw();return t.KtG(e.validateOtp())}),t.ALo(28,"translate"),t.qZA()()(),t.TgZ(29,"div",21)(30,"div",16)(31,"div",17)(32,"app-otp-input",18,22),t.NdJ("otpComplete",function(e){t.CHM(n);const o=t.oxw();return t.KtG(o.onOtpComplete(e))})("resendOtp",function(){t.CHM(n);const e=t.oxw();return t.KtG(e.resendOtp())}),t.qZA()(),t.TgZ(34,"button",20),t.NdJ("click",function(){t.CHM(n);const e=t.oxw();return t.KtG(e.validateOtp())}),t.ALo(35,"translate"),t.qZA()()()()()(),t.BQk()}if(2&r){const n=t.MAs(26),i=t.MAs(33),e=t.oxw();t.xp6(1),t.Q6J("ngClass",t.VKq(21,I,e.isMobileLayout&&e.screenWidth<=767)),t.xp6(9),t.Oqu(t.lcZ(11,9,"auth.otp.title")),t.xp6(3),t.Oqu(t.lcZ(14,11,"auth.otp.subTitle")),t.xp6(5),t.Oqu(t.lcZ(19,13,"auth.otp.title")),t.xp6(3),t.Oqu(t.lcZ(22,15,"auth.otp.subTitle")),t.xp6(6),t.Q6J("label",t.lcZ(28,17,"auth.otp.next"))("disabled",!n.isComplete),t.xp6(7),t.Q6J("label",t.lcZ(35,19,"auth.otp.next"))("disabled",!i.isComplete)}}let Z=(()=>{class r{messageService;translate;router;otpService;store;permissionService;loaderService;$gaService;$gtmService;loading=!1;mobileNumber="";otpCode="";isMobileLayout=!1;screenWidth=window.innerWidth;constructor(n,i,e,o,a,k,D,A,S){this.messageService=n,this.translate=i,this.router=e,this.otpService=o,this.store=a,this.permissionService=k,this.loaderService=D,this.$gaService=A,this.$gtmService=S}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.$gtmService.pushPageView("signUp","OTP"),null==history.state.mobile?this.router.navigate(["/register"]):this.mobileNumber=history.state.mobile}ngOnDestroy(){}onOtpComplete(n){this.otpCode=n}validateOtp(){if(this.$gaService.event(f.s.CLICK_ON_OTP_VERIFY,"","OTP_VERIFICATION",1,!0),this.otpCode&&4===this.otpCode.length){this.loading=!0;const n={UserName:this.mobileNumber,OTPCode:this.otpCode,CountryId:"1448983B-0C38-450A-BD71-9204D181B925"};("email"===localStorage.getItem("registrationMethod")?this.otpService.verifyEmail(n):this.otpService.checkOTP(n)).subscribe({next:o=>{this.loading=!1,o.success&&12!==o.success?(o.success||87===o.success)&&this.router.navigateByUrl("/register/register-details",{state:{mobile:this.mobileNumber,otp:this.otpCode,isVerified:!0}}):this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.invalidOtp")})},error:o=>{this.loading=!1,this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:o.message})}})}else this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:this.translate.instant("ErrorMessages.mobileRequired")})}resendOtp(){this.loaderService.show();const n={UserName:this.otpService.username,CountryId:this.otpService.countryId};("email"===localStorage.getItem("registrationMethod")?this.otpService.checkEmailAddress(n):this.otpService.checkMobileNumber(n)).subscribe({next:o=>{this.loaderService.hide()},error:o=>{this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:o.message})}})}static \u0275fac=function(i){return new(i||r)(t.Y36(x.ez),t.Y36(c.sK),t.Y36(y.F0),t.Y36(g.aO),t.Y36(g.d6),t.Y36(g.$A),t.Y36(g.D1),t.Y36(b.$r),t.Y36(P.J))};static \u0275cmp=t.Xpm({type:r,selectors:[["app-register-otp"]],standalone:!0,features:[t.jDz],decls:3,vars:2,consts:[[1,"update-password-page"],["class","spinner",4,"ngIf"],[4,"ngIf"],[1,"spinner"],[1,"content-container",3,"ngClass"],[1,"grid","justify-content-center","shadow-signin"],[1,"image","col-12","desktop-only"],["src","assets/images/registerLogo.svg","alt","","srcset",""],[1,"col-12","col-md-8","col-lg-6","bg-white","content-body"],[1,"mobile-header","mobile-only"],["src","assets/images/register.svg"],[1,"head-desc"],[1,"signup-heading"],[1,"signup-desc"],[1,"desktop-layout","desktop-only"],[1,"otp-heading"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12"],[3,"otpComplete","resendOtp"],["desktopOtpInput",""],["pButton","","type","button",1,"validate-btn",3,"label","disabled","click"],[1,"mobile-layout","mobile-only"],["mobileOtpInput",""]],template:function(i,e){1&i&&(t.TgZ(0,"section",0),t.YNc(1,w,2,0,"div",1),t.YNc(2,T,36,23,"ng-container",2),t.qZA()),2&i&&(t.xp6(1),t.Q6J("ngIf",e.loading),t.xp6(1),t.Q6J("ngIf",!e.loading))},dependencies:[l.ez,l.mk,l.O5,c.aw,c.X$,h.hJ,h.Hq,_.L,_.G,M],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}[_nghost-%COMP%]     button.back-btn{position:relative!important}@media screen and (min-width: 768px){[_nghost-%COMP%]     button.back-btn{top:-22px!important}}@media screen and (max-width: 767px){.update-password-page[_ngcontent-%COMP%]{margin-top:0}}.content-container.mobile-content-container[_ngcontent-%COMP%]{margin-top:1rem}.shadow-signin[_ngcontent-%COMP%]{border:1px solid rgba(151,151,151,.17);border-radius:7px;padding-top:16px;margin:30px}@media screen and (max-width: 767px){.shadow-signin[_ngcontent-%COMP%]{padding:0!important;margin:0;border:none;background-color:#fff}}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{flex:1;text-align:end}.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px;border-radius:281.739px}.shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%]{padding-top:1.5rem;display:flex;justify-content:flex-end;align-items:center}@media screen and (max-width: 767px){.shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%]{padding-top:.75rem}}.mobile-header[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding:.75rem}.mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{margin-right:1rem}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;justify-content:center}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%]{margin:0!important;font-size:18px;font-weight:500;color:#212121;font-family:var(--medium-font)!important}.mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#443f3f;font-family:var(--regular-font)!important}.validate-btn[_ngcontent-%COMP%]{width:100%;border-radius:4px;padding:16px 24px;font-size:14px;font-weight:500;font-family:var(--medium-font)!important;text-transform:uppercase;background-color:#1d4c69;color:#fff;border:none;margin-bottom:1.25rem;margin-top:.75rem}.validate-btn[_ngcontent-%COMP%]:disabled{background-color:#1d4c69;opacity:.5;cursor:not-allowed}@media screen and (max-width: 767px){.validate-btn[_ngcontent-%COMP%]{margin:.75rem}}[_nghost-%COMP%]     .p-button.validate-btn:enabled:hover{background-color:#1d4c69!important}.mobile-only[_ngcontent-%COMP%]{display:none}@media screen and (max-width: 767px){.mobile-only[_ngcontent-%COMP%]{display:flex}}.desktop-only[_ngcontent-%COMP%]{display:block}@media screen and (max-width: 767px){.desktop-only[_ngcontent-%COMP%]{display:none}}@media screen and (max-width: 767px){.mobile-content-container[_ngcontent-%COMP%]{margin-top:7rem!important}.mobile-content-container[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{padding-left:14px!important;padding-right:24px!important;border:1px solid rgba(151,151,151,.17);border-radius:7px;padding-top:16px;margin:30px}.mobile-content-container[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .content-body[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.mobile-content-container[_ngcontent-%COMP%]   .p-fluid[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-bottom:.75rem}}@media only screen and (max-width: 767px){.margin-x-100[_ngcontent-%COMP%]{margin-top:160px!important}}.desktop-layout[_ngcontent-%COMP%]{padding:2rem}.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#212121;font-family:var(--medium-font)!important;margin-bottom:.5rem}.desktop-layout[_ngcontent-%COMP%]   .otp-heading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#443f3f;font-family:var(--regular-font)!important;margin:0}.mobile-layout[_ngcontent-%COMP%]{padding:1rem}"]})}return r})()}}]);