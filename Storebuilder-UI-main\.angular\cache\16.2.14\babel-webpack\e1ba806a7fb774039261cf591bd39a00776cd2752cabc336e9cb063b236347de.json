{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class MerchantFilterPipe {\n  transform(value, args) {\n    if (!value) return null;\n    if (!args) return value;\n    args = args.toLowerCase();\n    return value.filter(data => JSON.stringify(data).toLowerCase().includes(args));\n  }\n  static ɵfac = function MerchantFilterPipe_Factory(t) {\n    return new (t || MerchantFilterPipe)();\n  };\n  static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"merchantFilter\",\n    type: MerchantFilterPipe,\n    pure: true\n  });\n}", "map": {"version": 3, "names": ["MerchantFilterPipe", "transform", "value", "args", "toLowerCase", "filter", "data", "JSON", "stringify", "includes", "pure"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchants\\pipes\\merchant-filter.pipe.ts"], "sourcesContent": ["import {Pipe, PipeTransform} from '@angular/core';\r\nimport {Merchants} from \"@core/interface\";\r\n\r\n@Pipe({\r\n  name: 'merchantFilter'\r\n})\r\nexport class MerchantFilterPipe implements PipeTransform {\r\n  transform(value: Array<Merchants>, args?: any): any {\r\n    if (!value) return null;\r\n    if (!args) return value;\r\n\r\n    args = args.toLowerCase();\r\n    return value.filter(data => JSON.stringify(data).toLowerCase().includes(args));\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,kBAAkB;EAC7BC,SAASA,CAACC,KAAuB,EAAEC,IAAU;IAC3C,IAAI,CAACD,KAAK,EAAE,OAAO,IAAI;IACvB,IAAI,CAACC,IAAI,EAAE,OAAOD,KAAK;IAEvBC,IAAI,GAAGA,IAAI,CAACC,WAAW,EAAE;IACzB,OAAOF,KAAK,CAACG,MAAM,CAACC,IAAI,IAAIC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAACF,WAAW,EAAE,CAACK,QAAQ,CAACN,IAAI,CAAC,CAAC;EAChF;;qBAPWH,kBAAkB;EAAA;;;UAAlBA,kBAAkB;IAAAU,IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}