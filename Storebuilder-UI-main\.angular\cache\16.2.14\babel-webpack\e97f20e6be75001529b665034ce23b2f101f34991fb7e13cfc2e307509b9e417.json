{"ast": null, "code": "import { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-google-analytics\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction FooterComponent_footer_0_img_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.footerDetails.mainLogo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_0_img_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 20);\n  }\n}\nfunction FooterComponent_footer_0_div_68_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"a\", 26);\n    i0.ɵɵelement(2, \"em\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const link_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", link_r7.iconLink, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(link_r7.iconClass);\n  }\n}\nfunction FooterComponent_footer_0_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵtemplate(3, FooterComponent_footer_0_div_68_div_3_Template, 3, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.footerLinks);\n  }\n}\nfunction FooterComponent_footer_0_div_69_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"img\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const logo_r9 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r9, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_0_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\");\n    i0.ɵɵelement(7, \"img\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 31)(9, \"div\", 32);\n    i0.ɵɵtemplate(10, FooterComponent_footer_0_div_69_div_10_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 34);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"footer.weAccept\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r5.footerDetails.sideLogo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.footerDetails.payments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 6, ctx_r5.footerDetails.copyRights), \" \");\n  }\n}\nfunction FooterComponent_footer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 2)(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.backToTop());\n    });\n    i0.ɵɵelement(2, \"em\", 4);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.reloadCurrentPage(4, \"About us\"));\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_16_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.reloadCurrentPage(5, \"Terms and Conditions\"));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_19_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.reloadCurrentPage(1, \"Shipping and return\"));\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.reloadCurrentPage(3, \"Privacy policy\"));\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_25_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.reloadCurrentPage(2, \"Conditions of use\"));\n    });\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 8)(29, \"div\", 9);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 12);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 12);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 12);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 8)(42, \"div\", 9);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_45_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_48_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.reloadCurrentPage(8, \"Return & Refund\"));\n    });\n    i0.ɵɵtext(49);\n    i0.ɵɵpipe(50, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_51_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.reloadCurrentPage(9, \"Warranty Policy\"));\n    });\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_54_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.reloadCurrentPage(11, \"FAQ\"));\n    });\n    i0.ɵɵtext(55);\n    i0.ɵɵpipe(56, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 8)(58, \"div\", 9);\n    i0.ɵɵtext(59);\n    i0.ɵɵpipe(60, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_61_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.navigateToSellerHub());\n    });\n    i0.ɵɵtext(62);\n    i0.ɵɵpipe(63, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 8);\n    i0.ɵɵtemplate(65, FooterComponent_footer_0_img_65_Template, 1, 1, \"img\", 14);\n    i0.ɵɵtemplate(66, FooterComponent_footer_0_img_66_Template, 1, 0, \"img\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(67, \"div\", 16);\n    i0.ɵɵtemplate(68, FooterComponent_footer_0_div_68_Template, 4, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(69, FooterComponent_footer_0_div_69_Template, 14, 8, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 25, \"footer.backToTop\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 27, \"footer.company\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 29, \"footer.aboutUs\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 31, \"footer.termsAndConditions\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(21, 33, \"footer.shippingAndReturn\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(24, 35, \"footer.privacyPolicy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 37, \"footer.conditionsOfUse\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 39, \"footer.account\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/orders\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(34, 41, \"footer.myOrders\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 43, \"footer.myAddress\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/details\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 45, \"footer.myDetails\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(44, 47, \"footer.help\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(47, 49, \"footer.contactUs\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(50, 51, \"footer.returnAndRefund\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(53, 53, \"footer.warrantyPolicy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(56, 55, \"footer.faq\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(60, 57, \"footer.sellerHub\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(63, 59, \"footer.sellOnMarketplace\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConfig);\n  }\n}\nfunction FooterComponent_footer_1_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r22.footerDetails.mainLogoMobile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 20);\n  }\n}\nfunction FooterComponent_footer_1_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"img\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const logo_r29 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r29, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_div_39_div_1_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.footerDetails.payments);\n  }\n}\nfunction FooterComponent_footer_1_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 54);\n    i0.ɵɵelement(2, \"img\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const logo_r30 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", logo_r30.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r30.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx_r26.footerDetails.copyRightsMobile), \" \");\n  }\n}\nfunction FooterComponent_footer_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"img\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r27.footerDetails.bottonLogo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 36)(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.backToTop());\n    });\n    i0.ɵɵelement(2, \"em\", 4);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"div\", 38)(8, \"div\", 39);\n    i0.ɵɵtemplate(9, FooterComponent_footer_1_img_9_Template, 1, 1, \"img\", 14);\n    i0.ɵɵtemplate(10, FooterComponent_footer_1_img_10_Template, 1, 0, \"img\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.navigateToSellerHub());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 41)(15, \"div\", 42)(16, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_16_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.reloadCurrentPage(3, \"Privacy policy\"));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_19_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.reloadCurrentPage(5, \"Terms and Conditions\"));\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.reloadCurrentPage(1, \"Shipping and return\"));\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 44)(26, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_29_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.reloadCurrentPage(8, \"Return & Refund\"));\n    });\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 44)(33, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_33_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.reloadCurrentPage(9, \"Warranty Policy\"));\n    });\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_36_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.reloadCurrentPage(11, \"FAQ\"));\n    });\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(39, FooterComponent_footer_1_div_39_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementStart(40, \"div\", 46)(41, \"div\", 47);\n    i0.ɵɵtext(42);\n    i0.ɵɵpipe(43, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 48);\n    i0.ɵɵtemplate(45, FooterComponent_footer_1_div_45_Template, 3, 2, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(46, FooterComponent_footer_1_div_46_Template, 3, 3, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, FooterComponent_footer_1_div_47_Template, 2, 1, \"div\", 51);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 16, \"footer.backToTop\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 18, \"footer.becomeSeller\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 20, \"footer.privacyPolicy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(21, 22, \"footer.termsAndConditions\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(24, 24, \"footer.shippingAndReturn\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 26, \"footer.contactUs\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 28, \"footer.returnAndRefund\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(35, 30, \"footer.warrantyPolicy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 32, \"footer.faq\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(43, 34, \"footer.reachOut\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.footerDetails.socials);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n  }\n}\nexport class FooterComponent {\n  onResize(event) {\n    this.updateZoomClass();\n  }\n  updateZoomClass() {\n    if (isPlatformBrowser(this.platformId)) {\n      const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n      if (zoomLevel <= 91) {\n        this.zoomLevelClass = 'zoom-110';\n      } else if (zoomLevel <= 112) {\n        this.zoomLevelClass = 'zoom-90';\n      } else if (zoomLevel <= 125) {\n        this.zoomLevelClass = 'zoom-80';\n      } else if (zoomLevel <= 134) {\n        this.zoomLevelClass = 'zoom-75';\n      } else if (zoomLevel <= 150) {\n        this.zoomLevelClass = 'zoom-67';\n      } else if (zoomLevel <= 200) {\n        this.zoomLevelClass = 'zoom-50';\n      } else if (zoomLevel <= 300) {\n        this.zoomLevelClass = 'zoom-33';\n      } else if (zoomLevel <= 400) {\n        this.zoomLevelClass = 'zoom-25';\n      } else {\n        this.zoomLevelClass = 'default-zoom';\n      }\n    }\n  }\n  constructor(store, router, platformId, appDataService, permissionService, $gaService, authService) {\n    this.store = store;\n    this.router = router;\n    this.platformId = platformId;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.authService = authService;\n    this.issSellMarketplace = environment.isStoreCloud;\n    this.scrollToTop = new EventEmitter();\n    this.scConfig = false;\n    this.isSellerHub = '';\n    this.environment = environment;\n    this.zoomLevelClass = 'default-zoom';\n    this.isMobileTemplate = false;\n    this.isGoogleAnalytics = false;\n    this.footerLinks = [{\n      iconClass: 'fa-brands fa-square-facebook',\n      iconLink: 'https://www.facebook.com/YallaSuperMallEG',\n      iconName: 'facebook'\n    }, {\n      iconClass: 'fa-brands fa-square-twitter',\n      iconLink: 'https://twitter.com/YallaSuperappEG',\n      iconName: 'twitter'\n    }, {\n      iconClass: 'fa-brands fa-youtube',\n      iconLink: 'https://www.youtube.com/@yallasuperapp3030',\n      iconName: 'youtube'\n    }, {\n      iconClass: 'fa-brands fa-instagram',\n      iconLink: 'https://www.instagram.com/yallasupermalleg/',\n      iconName: 'instagram'\n    }, {\n      iconClass: 'fa-brands fa-tiktok',\n      iconLink: 'https://www.tiktok.com/@yallasupermall',\n      iconName: 'tiktok'\n    }];\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.scConfig = environment.isStoreCloud;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.screen.width;\n      if (this.screenWidth > 768) {\n        this.desktopView = true;\n      } else {\n        this.desktopView = false;\n      }\n    }\n    if (!this.authService.isMarketplace()) {\n      const socialLinks = this.appDataService.shopSettingData;\n      if (socialLinks) {\n        for (const link of this.footerLinks) {\n          if (socialLinks.hasOwnProperty(link.iconName) && socialLinks[link.iconName] !== null) {\n            link.iconUrl = socialLinks[link.iconName];\n          }\n        }\n      }\n    }\n  }\n  reloadCurrentPage(pageId, title) {\n    if (this.isGoogleAnalytics) {\n      if (title.includes('Terms and Conditions')) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_TERMS_AND_CONDITIONS, '', 'TERMS_AND_CONDITION', 1, true);\n      } else if (title.includes('Privacy policy')) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_PRIVACY_POLICY, '', 'PRIVACY_POLICY', 1, true);\n      }\n    }\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/about-us/'], {\n      queryParams: {\n        pageId: pageId,\n        title: title\n      }\n    }));\n  }\n  navigatePage(url) {\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/' + url + '/']));\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('isShop').subscribe({\n        next: res => this.isShop = res\n      });\n    }, 10);\n    this.store.subscription('allowedFeature').subscribe({\n      next: res => {\n        if (res.length) {\n          this.isSellerHub = localStorage.getItem('isSellerHub');\n        }\n      }\n    });\n  }\n  backToTop() {\n    window.scroll(0, 0);\n    if (isPlatformBrowser(this.platformId)) {\n      window.scroll({\n        top: 0,\n        left: 0,\n        behavior: 'auto'\n      });\n      this.scrollToTop.emit();\n    }\n  }\n  navigateToSellerHub() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE_FOOTER, '', '', 1, true);\n    }\n    if (isPlatformBrowser(this.platformId)) {\n      window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\n    }\n  }\n  static #_ = this.ɵfac = function FooterComponent_Factory(t) {\n    return new (t || FooterComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FooterComponent,\n    selectors: [[\"app-mtn-footer\"]],\n    hostBindings: function FooterComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function FooterComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      toHide: \"toHide\",\n      footerDetails: \"footerDetails\"\n    },\n    outputs: {\n      scrollToTop: \"scrollToTop\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"footer\", 4, \"ngIf\"], [\"class\", \"footer-mobile\", 4, \"ngIf\"], [1, \"footer\"], [1, \"footer__to-top\", \"cursor-pointer\", \"pt-4\", \"pb-4\", \"flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [1, \"pi\", \"pi-caret-up\"], [1, \"footer__content\"], [\"id\", \"footer-zoom-container\"], [1, \"footer__content-body\"], [1, \"d-inline-flex\", \"flex-column\", \"footer__content-list\"], [1, \"footer__content-list__heading\"], [1, \"footer__content-list__details\", \"d-none\", 3, \"click\"], [1, \"footer__content-list__details\", 3, \"click\"], [1, \"footer__content-list__details\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"footer__content-list__details\", \"cursor-pointer\", 3, \"click\"], [\"alt\", \"No Image\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/white-logo.svg\", 4, \"ngIf\"], [\"id\", \"footer-zoom-container\", 1, \"footer__content-body\", \"justify-content-end\"], [\"class\", \"col-12 col-md-3 flex-row justify-content-center\", 4, \"ngIf\"], [\"class\", \"footer__content-body\", \"id\", \"footer-zoom-container\", 4, \"ngIf\"], [\"alt\", \"No Image\", 3, \"src\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/white-logo.svg\"], [1, \"col-12\", \"col-md-3\", \"flex-row\", \"justify-content-center\"], [1, \"width-icon\"], [1, \"d-flex\"], [\"class\", \"mx-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"mx-3\"], [\"target\", \"_blank\", 1, \"nav-link\", 3, \"href\"], [\"id\", \"footer-zoom-container\", 1, \"footer__content-body\"], [1, \"footer__content-body__divider\", \"footer__content_bottom\"], [1, \"d-flex\", \"flex-row\", \"justify-content-between\", \"mt-3\"], [1, \"footer__content_bottom__payment-header\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"d-inline-flex\", \"flex-row\"], [\"class\", \"footer__content_bottom__payment-logos\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer__content_bottom__copy-rights\"], [1, \"footer__content_bottom__payment-logos\"], [1, \"footer-mobile\"], [1, \"footer-mobile__content\", \"pb-7\"], [1, \"footer-mobile__content__top-section\", \"d-flex\", \"justify-content-between\"], [1, \"footer-mobile__content__top-section__mtn-logo\"], [1, \"footer-mobile__content__top-section__seller_btn\", 3, \"click\"], [1, \"footer-mobile__body-section\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\", \"px-3\"], [1, \"footer-mobile__body-section__section__link\", 3, \"click\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\"], [\"class\", \"d-inline-flex footer-mobile__body-section__section justify-content-center\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"footer-mobile__body-section__section\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__contact-us\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__social-links\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-center footer-mobile__body-section__section__rights-reserved\", 4, \"ngIf\"], [\"class\", \"d-inline-flex footer-mobile__body-section__section justify-content-center footer-bottom\", 4, \"ngIf\"], [\"class\", \"footer-mobile__body-section__section\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-mobile__body-section__section\"], [\"target\", \"_blank\", 3, \"href\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__rights-reserved\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\", \"footer-bottom\"]],\n    template: function FooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, FooterComponent_footer_0_Template, 70, 61, \"footer\", 0);\n        i0.ɵɵtemplate(1, FooterComponent_footer_1_Template, 48, 36, \"footer\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.desktopView || !ctx.isMobileTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.desktopView && !ctx.toHide && ctx.isMobileTemplate);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i2.RouterLink, i5.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.footer__to-top[_ngcontent-%COMP%] {\\n  min-height: 45px;\\n  background-color: #FFCB05;\\n  color: #191C1F;\\n  font-size: 14px;\\n  font-family: var(--regular-font);\\n  right: 4px;\\n  position: fixed;\\n  z-index: 9999;\\n  width: 60px;\\n  top: 80%;\\n  height: 53px;\\n  border-bottom-left-radius: 13px;\\n  border-top-left-radius: 13px;\\n  text-transform: uppercase;\\n}\\n.footer[_ngcontent-%COMP%]   .top-arrow[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  font-size: 17px;\\n}\\n.footer__content[_ngcontent-%COMP%] {\\n  border-radius: 16px 16px 0px 0px !important;\\n  background-color: var(--footer_content_bgcolor);\\n  color: var(--footer_content_txtcolor) !important;\\n  min-height: 200px;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  padding-top: 26px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n  margin-top: 15px;\\n}\\n.footer__content-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 15px 30px;\\n  width: 100%;\\n  justify-content: space-between;\\n  flex-wrap: wrap;\\n}\\n.footer__content-body__divider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-top: 2px solid #FFF;\\n}\\n@media only screen and (max-width: 767px) {\\n  .footer__content-body[_ngcontent-%COMP%] {\\n    padding: 15px 20px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .footer__content-body[_ngcontent-%COMP%] {\\n    padding: 15px 20px;\\n  }\\n}\\n.footer__content-list[_ngcontent-%COMP%] {\\n  gap: 16px;\\n}\\n.footer__content-list__heading[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 21px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n}\\n.footer__content-list__details[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  cursor: pointer;\\n}\\n.footer__content-list__details[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n@media only screen and (max-width: 767px) {\\n  .footer__content-list[_ngcontent-%COMP%] {\\n    padding: 10px 0px;\\n  }\\n}\\n.footer__content_bottom__payment-header[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  margin: 10px 0px;\\n}\\n.footer__content_bottom__payment-logos[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n}\\n.footer__content_bottom__logo_text[_ngcontent-%COMP%] {\\n  color: #FFCB05;\\n  font-family: var(--regular-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 35px;\\n  margin-right: 10px;\\n}\\n.footer__content_bottom__copy-rights[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  text-align: right;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 200%;\\n  \\n\\n  text-decoration-line: underline;\\n}\\n.footer[_ngcontent-%COMP%]   .copy-rights[_ngcontent-%COMP%] {\\n  background-color: var(--footer_copy_bgcolor);\\n  color: var(--footer_copy_txtcolor) !important;\\n  font-size: 14px;\\n}\\n.footer[_ngcontent-%COMP%]   .pi-white[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.footer[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-size: 25px;\\n  letter-spacing: 0px;\\n  color: #ffffff;\\n}\\n\\n.footer-mobile[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.footer-mobile__content[_ngcontent-%COMP%] {\\n  border-radius: 16px 16px 0px 0px !important;\\n  background-color: var(--footer_content_bgcolor);\\n  color: var(--footer_content_txtcolor) !important;\\n  min-height: 200px;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  padding-top: 26px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n}\\n.footer-mobile__content__top-section[_ngcontent-%COMP%] {\\n  padding: 0px 20px;\\n}\\n.footer-mobile__content__top-section__seller_btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 140px;\\n  height: var(--Layout-Padding-Screen, 32px);\\n  padding: 4px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 4px;\\n  border-radius: 4px;\\n  border: 1px solid var(--Main-Colors-Primary, #032D62);\\n  opacity: 0.99;\\n  background: var(--neutral-light-0, #FFF);\\n  box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.2);\\n  color: var(--primary, #204E6E);\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%; \\n\\n}\\n.footer-mobile__body-section[_ngcontent-%COMP%] {\\n  padding: 16px 0px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.footer-mobile__body-section__section[_ngcontent-%COMP%] {\\n  gap: 10px;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%; \\n\\n  white-space: nowrap;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%]:last-child {\\n  white-space: normal;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%]:nth-child(2)::before {\\n  content: \\\".\\\";\\n  margin-right: 7px;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%]:nth-child(3)::before {\\n  content: \\\".\\\";\\n  margin-right: 7px;\\n}\\n.footer-mobile__body-section__section__payment-logos[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n}\\n.footer-mobile__body-section__section__contact-us[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n.footer-mobile__body-section__section__social-links[_ngcontent-%COMP%] {\\n  gap: 19px;\\n}\\n.footer-mobile__body-section__section__social-links[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n.footer-mobile__body-section__section__rights-reserved[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-style: normal;\\n  font-weight: 300;\\n  line-height: normal;\\n  max-width: 300px;\\n  margin: 0 auto;\\n  text-align: center;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  a[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .to-top[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .footer-tag[_ngcontent-%COMP%] {\\n    font-weight: 400;\\n    font-size: 12px;\\n    font-family: var(--regular-font) !important;\\n  }\\n  .mobile-social-icon[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .px-7[_ngcontent-%COMP%] {\\n    padding-left: 1rem !important;\\n    padding-right: 1rem !important;\\n  }\\n  .px-6[_ngcontent-%COMP%] {\\n    padding-left: 1rem !important;\\n    padding-right: 1rem !important;\\n  }\\n  .mobile-center-footer[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .mobile-center-footer[_ngcontent-%COMP%]   .width-icon[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    display: flex;\\n  }\\n}\\na[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  cursor: pointer;\\n}\\n\\n.width-icon[_ngcontent-%COMP%] {\\n  width: 220px;\\n}\\n\\n.width-text[_ngcontent-%COMP%] {\\n  width: 213px;\\n  display: block;\\n}\\n\\n.social-text[_ngcontent-%COMP%] {\\n  width: 257px;\\n  display: block;\\n  margin-top: 10px;\\n}\\n\\n.sell-on-market[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n}\\n\\n.sell-on-market[_ngcontent-%COMP%]:hover {\\n  color: blue;\\n  text-decoration: underline;\\n}\\n\\n.marketplace-btn[_ngcontent-%COMP%] {\\n  width: 96%;\\n  height: auto;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  text-decoration: underline;\\n  text-align: center;\\n  background: #FFCB05;\\n  padding: 8px;\\n}\\n.marketplace-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  margin-right: 14px;\\n}\\n\\n.marketplace-btn[_ngcontent-%COMP%]:hover {\\n  border: none !important;\\n}\\n\\n.footer-bottom[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "PLATFORM_ID", "environment", "isPlatformBrowser", "GaLocalActionEnum", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "footerDetails", "mainLogo", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "link_r7", "iconLink", "ɵɵclassMap", "iconClass", "ɵɵtemplate", "FooterComponent_footer_0_div_68_div_3_Template", "ctx_r4", "footerLinks", "logo_r9", "ɵɵtext", "FooterComponent_footer_0_div_69_div_10_Template", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r5", "sideLogo", "payments", "copyRights", "ɵɵlistener", "FooterComponent_footer_0_Template_div_click_1_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "backToTop", "FooterComponent_footer_0_Template_div_click_13_listener", "ctx_r12", "reloadCurrentPage", "FooterComponent_footer_0_Template_div_click_16_listener", "ctx_r13", "FooterComponent_footer_0_Template_div_click_19_listener", "ctx_r14", "FooterComponent_footer_0_Template_div_click_22_listener", "ctx_r15", "FooterComponent_footer_0_Template_div_click_25_listener", "ctx_r16", "FooterComponent_footer_0_Template_div_click_45_listener", "ctx_r17", "navigatePage", "FooterComponent_footer_0_Template_div_click_48_listener", "ctx_r18", "FooterComponent_footer_0_Template_div_click_51_listener", "ctx_r19", "FooterComponent_footer_0_Template_div_click_54_listener", "ctx_r20", "FooterComponent_footer_0_Template_div_click_61_listener", "ctx_r21", "navigateToSellerHub", "FooterComponent_footer_0_img_65_Template", "FooterComponent_footer_0_img_66_Template", "FooterComponent_footer_0_div_68_Template", "FooterComponent_footer_0_div_69_Template", "ɵɵtextInterpolate", "ctx_r0", "scConfig", "ctx_r22", "mainLogoMobile", "logo_r29", "FooterComponent_footer_1_div_39_div_1_Template", "ctx_r24", "logo_r30", "url", "icon", "ctx_r26", "copyRightsMobile", "ctx_r27", "bottonLogo", "FooterComponent_footer_1_Template_div_click_1_listener", "_r32", "ctx_r31", "FooterComponent_footer_1_img_9_Template", "FooterComponent_footer_1_img_10_Template", "FooterComponent_footer_1_Template_button_click_11_listener", "ctx_r33", "FooterComponent_footer_1_Template_div_click_16_listener", "ctx_r34", "FooterComponent_footer_1_Template_div_click_19_listener", "ctx_r35", "FooterComponent_footer_1_Template_div_click_22_listener", "ctx_r36", "FooterComponent_footer_1_Template_div_click_26_listener", "ctx_r37", "FooterComponent_footer_1_Template_div_click_29_listener", "ctx_r38", "FooterComponent_footer_1_Template_div_click_33_listener", "ctx_r39", "FooterComponent_footer_1_Template_div_click_36_listener", "ctx_r40", "FooterComponent_footer_1_div_39_Template", "FooterComponent_footer_1_div_45_Template", "FooterComponent_footer_1_div_46_Template", "FooterComponent_footer_1_div_47_Template", "ctx_r1", "socials", "FooterComponent", "onResize", "event", "updateZoomClass", "platformId", "zoomLevel", "window", "innerWidth", "screen", "availWidth", "zoomLevelClass", "constructor", "store", "router", "appDataService", "permissionService", "$gaService", "authService", "issSellMarketplace", "isStoreCloud", "scrollToTop", "isSellerHub", "isMobileTemplate", "isGoogleAnalytics", "iconName", "hasPermission", "screenWidth", "width", "desktopView", "isMarketplace", "socialLinks", "shopSettingData", "link", "hasOwnProperty", "iconUrl", "pageId", "title", "includes", "CLICK_ON_TERMS_AND_CONDITIONS", "CLICK_ON_PRIVACY_POLICY", "navigateByUrl", "skipLocationChange", "then", "navigate", "queryParams", "ngAfterViewInit", "setTimeout", "subscription", "subscribe", "next", "res", "isShop", "length", "localStorage", "getItem", "scroll", "top", "left", "behavior", "emit", "CLICK_ON_SELL_ON_MARKETPLACE_FOOTER", "open", "merchantURL", "_", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "Router", "AppDataService", "PermissionService", "i3", "GoogleAnalyticsService", "AuthService", "_2", "selectors", "hostBindings", "FooterComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "FooterComponent_footer_0_Template", "FooterComponent_footer_1_Template", "toHide"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\footer\\footer.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\footer\\footer.component.html"], "sourcesContent": ["import {Component, EventEmitter, HostListener, Inject, Input, Output, PLATFORM_ID} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport {AppDataService, AuthService, PermissionService, StoreService} from '@core/services';\r\nimport { environment } from '@environments/environment';\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\n\r\n@Component({\r\n  selector: 'app-mtn-footer',\r\n  templateUrl: './footer.component.html',\r\n  styleUrls: ['./footer.component.scss'],\r\n})\r\nexport class FooterComponent {\r\n  @Input() toHide: any;\r\n  @Input() footerDetails: any;\r\n  issSellMarketplace: boolean = environment.isStoreCloud\r\n  @Output() scrollToTop: EventEmitter<any> = new EventEmitter<any>();\r\n  isShop: any;\r\n  scConfig: boolean = false\r\n  screenWidth?: any;\r\n  desktopView: boolean;\r\n  isSellerHub: any = '';\r\n  protected readonly environment = environment;\r\n  zoomLevelClass: string = 'default-zoom';\r\n  isMobileTemplate:boolean=false;\r\n  isGoogleAnalytics:boolean=false;\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: Event) {\r\n    this.updateZoomClass();\r\n  }\r\n\r\n  footerLinks: any = [\r\n    {\r\n      iconClass: 'fa-brands fa-square-facebook',\r\n      iconLink: 'https://www.facebook.com/YallaSuperMallEG',\r\n      iconName: 'facebook'\r\n    },{\r\n      iconClass: 'fa-brands fa-square-twitter',\r\n      iconLink: 'https://twitter.com/YallaSuperappEG',\r\n      iconName: 'twitter'\r\n    },{\r\n      iconClass: 'fa-brands fa-youtube',\r\n      iconLink: 'https://www.youtube.com/@yallasuperapp3030',\r\n      iconName: 'youtube'\r\n    },{\r\n      iconClass: 'fa-brands fa-instagram',\r\n      iconLink: 'https://www.instagram.com/yallasupermalleg/',\r\n      iconName: 'instagram'\r\n    },{\r\n      iconClass: 'fa-brands fa-tiktok',\r\n      iconLink: 'https://www.tiktok.com/@yallasupermall',\r\n      iconName: 'tiktok'\r\n    },\r\n  ]\r\n\r\n  private updateZoomClass() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const zoomLevel = (window.innerWidth / window.screen.availWidth) * 100;\r\n      if (zoomLevel <= 91) {\r\n        this.zoomLevelClass = 'zoom-110';\r\n      }\r\n      else if (zoomLevel <= 112) {\r\n        this.zoomLevelClass = 'zoom-90';\r\n      } else if (zoomLevel <= 125) {\r\n        this.zoomLevelClass = 'zoom-80';\r\n      } else if (zoomLevel <= 134) {\r\n        this.zoomLevelClass = 'zoom-75';\r\n      } else if (zoomLevel <= 150) {\r\n        this.zoomLevelClass = 'zoom-67';\r\n      } else if (zoomLevel <= 200) {\r\n        this.zoomLevelClass = 'zoom-50';\r\n      } else if (zoomLevel <= 300) {\r\n        this.zoomLevelClass = 'zoom-33';\r\n      }\r\n      else if (zoomLevel <= 400) {\r\n        this.zoomLevelClass = 'zoom-25';\r\n      } else {\r\n        this.zoomLevelClass = 'default-zoom';\r\n      }\r\n    }\r\n\r\n  }\r\n  constructor(private store: StoreService, public router: Router, @Inject(PLATFORM_ID) private platformId: any,\r\n              private appDataService: AppDataService,\r\n              private permissionService: PermissionService,\r\n              private $gaService: GoogleAnalyticsService,\r\n              private authService: AuthService) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    this.scConfig = environment.isStoreCloud;\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.screen.width;\r\n      if (this.screenWidth > 768) {\r\n        this.desktopView = true;\r\n      } else {\r\n        this.desktopView = false;\r\n      }\r\n    }\r\n\r\n    if(!this.authService.isMarketplace()){\r\n      const socialLinks = this.appDataService.shopSettingData\r\n      if(socialLinks) {\r\n        for (const link of this.footerLinks) {\r\n          if (socialLinks.hasOwnProperty(link.iconName) && socialLinks[link.iconName] !== null) {\r\n            link.iconUrl = socialLinks[link.iconName];\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  reloadCurrentPage(pageId: number, title: string) {\r\n    if(this.isGoogleAnalytics){\r\n    if (title.includes('Terms and Conditions')) {\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_TERMS_AND_CONDITIONS, '', 'TERMS_AND_CONDITION', 1, true);\r\n    } else if (title.includes('Privacy policy')) {\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_PRIVACY_POLICY, '', 'PRIVACY_POLICY', 1, true);\r\n    }\r\n  }\r\n\r\n    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>\r\n      this.router.navigate(['/about-us/'], {\r\n        queryParams: { pageId: pageId, title: title },\r\n      })\r\n    );\r\n  }\r\n\r\n  navigatePage(url: string) {\r\n    this.router\r\n      .navigateByUrl('/', { skipLocationChange: true })\r\n      .then(() => this.router.navigate(['/' + url + '/']));\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('isShop').subscribe({\r\n        next: (res: any) => (this.isShop = res),\r\n      });\r\n    }, 10);\r\n    this.store.subscription('allowedFeature').subscribe({\r\n      next: (res: any) => {\r\n        if (res.length) {\r\n          this.isSellerHub = localStorage.getItem('isSellerHub')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  backToTop() {\r\n    window.scroll(0,0);\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.scroll({\r\n        top: 0,\r\n        left: 0,\r\n        behavior: 'auto',\r\n      });\r\n      this.scrollToTop.emit();\r\n    }\r\n  }\r\n  navigateToSellerHub() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE_FOOTER, '', '', 1, true);\r\n    }\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\r\n    }\r\n  }\r\n}\r\n", "<footer class=\"footer\" *ngIf=\"desktopView || !isMobileTemplate\">\r\n  <div (click)=\"backToTop()\"\r\n    class=\"footer__to-top cursor-pointer pt-4 pb-4 flex flex-column justify-content-center align-items-center\">\r\n    <em class=\"pi pi-caret-up\"></em>\r\n\r\n    <div>{{ \"footer.backToTop\" | translate }}</div>\r\n  </div>\r\n  <div class=\"footer__content\">\r\n    <div id=\"footer-zoom-container\">\r\n      <div class=\"footer__content-body\">\r\n\r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n          <div class=\"footer__content-list__heading\">\r\n            {{ \"footer.company\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(4, 'About us')\" class=\"footer__content-list__details d-none\">\r\n            {{ \"footer.aboutUs\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(5, 'Terms and Conditions')\" class=\"footer__content-list__details\">\r\n            {{ \"footer.termsAndConditions\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(1, 'Shipping and return')\" class=\"footer__content-list__details d-none\">\r\n            {{ \"footer.shippingAndReturn\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(3, 'Privacy policy')\" class=\"footer__content-list__details\" >\r\n            {{ \"footer.privacyPolicy\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(2, 'Conditions of use')\" class=\"footer__content-list__details d-none\">\r\n            {{ \"footer.conditionsOfUse\" | translate }}\r\n          </div>\r\n\r\n\r\n        </div>\r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n          <div class=\"footer__content-list__heading\">\r\n            {{ \"footer.account\" | translate }}\r\n          </div>\r\n          <div [routerLink]=\"'/orders'\" class=\"footer__content-list__details cursor-pointer\">\r\n            {{ \"footer.myOrders\" | translate }}\r\n          </div>\r\n          <div [routerLink]=\"'/account/address'\" class=\"footer__content-list__details cursor-pointer\">\r\n            {{ \"footer.myAddress\" | translate }}\r\n          </div>\r\n          <div [routerLink]=\"'/account/details'\" class=\"footer__content-list__details cursor-pointer\">\r\n            {{ \"footer.myDetails\" | translate }}\r\n          </div>\r\n\r\n\r\n        </div>\r\n\r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n          <div class=\"footer__content-list__heading\">\r\n            {{ \"footer.help\" | translate }}\r\n          </div>\r\n          <div (click)=\"navigatePage('contact-us')\" class=\"footer__content-list__details cursor-pointer\">\r\n            {{ \"footer.contactUs\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(8, 'Return & Refund')\" class=\"footer__content-list__details\" >\r\n            {{ \"footer.returnAndRefund\" | translate }}\r\n          </div>\r\n          <!-- <div (click)=\"reloadCurrentPage(10, 'Dispute Resolution')\" class=\"footer__content-list__details\" >\r\n            {{ \"footer.disputeResolution\" | translate }}\r\n          </div> -->\r\n          <div (click)=\"reloadCurrentPage(9, 'Warranty Policy')\" class=\"footer__content-list__details\" >\r\n            {{ \"footer.warrantyPolicy\" | translate }}\r\n          </div>\r\n          <div (click)=\"reloadCurrentPage(11, 'FAQ')\" class=\"footer__content-list__details\" >\r\n            {{ \"footer.faq\" | translate }}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n          <div class=\"footer__content-list__heading\">\r\n            {{ \"footer.sellerHub\" | translate }}\r\n          </div>\r\n          <div (click)=\"navigateToSellerHub()\" class=\"footer__content-list__details cursor-pointer\">\r\n            {{ \"footer.sellOnMarketplace\" | translate }}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n\r\n          <img *ngIf=\"!scConfig\" alt=\"No Image\" [src]=\"footerDetails.mainLogo\">\r\n          <img *ngIf=\"scConfig\" alt=\"No Image\" src=\"assets/icons/white-logo.svg\">\r\n        </div>\r\n\r\n\r\n      </div>\r\n\r\n    </div>\r\n    <div class=\"footer__content-body justify-content-end\" id=\"footer-zoom-container\">\r\n      <div\r\n        *ngIf=\"scConfig\"\r\n        class=\"col-12 col-md-3 flex-row justify-content-center\"\r\n      >\r\n        <div class=\"width-icon\">\r\n          <div class=\"d-flex\">\r\n            <div class=\"mx-3\" *ngFor=\"let link of footerLinks\">\r\n              <a\r\n                class=\"nav-link\"\r\n                [href]=\"link.iconLink\"\r\n                target=\"_blank\"\r\n              ><em class=\"{{link.iconClass}}\"></em\r\n              ></a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"footer__content-body\" id=\"footer-zoom-container\" *ngIf=\"!scConfig\">\r\n      <div class=\"footer__content-body__divider footer__content_bottom\">\r\n        <div class=\"d-flex flex-row justify-content-between mt-3\">\r\n          <div class=\"footer__content_bottom__payment-header\">\r\n            {{ \"footer.weAccept\" | translate }}\r\n          </div>\r\n          <div>\r\n            <img alt=\"No Image\" [src]=\"footerDetails.sideLogo\">\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"d-flex justify-content-between\">\r\n          <div class=\"d-inline-flex flex-row\">\r\n            <div *ngFor=\"let logo of footerDetails.payments\" class=\"footer__content_bottom__payment-logos\">\r\n              <img alt=\"No Image\" [src]=\"logo\">\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"footer__content_bottom__copy-rights\">\r\n            {{footerDetails.copyRights | translate}}\r\n          </div>\r\n\r\n        </div>\r\n\r\n      </div>\r\n\r\n\r\n    </div>\r\n\r\n\r\n  </div>\r\n</footer>\r\n<footer class=\"footer-mobile\" *ngIf=\"!desktopView && !toHide && isMobileTemplate\">\r\n  <div (click)=\"backToTop()\"\r\n       class=\"footer__to-top cursor-pointer pt-4 pb-4 flex flex-column justify-content-center align-items-center\">\r\n    <em class=\"pi pi-caret-up\"></em>\r\n\r\n    <div>{{ \"footer.backToTop\" | translate }}</div>\r\n  </div>\r\n  <div class=\"footer-mobile__content pb-7\">\r\n    <div class=\"footer-mobile__content__top-section d-flex justify-content-between\" >\r\n      <div class=\"footer-mobile__content__top-section__mtn-logo\">\r\n        <img *ngIf=\"!scConfig\" alt=\"No Image\" [src]=\"footerDetails.mainLogoMobile\">\r\n        <img *ngIf=\"scConfig\" alt=\"No Image\" src=\"assets/icons/white-logo.svg\">\r\n      </div>\r\n      <button class=\"footer-mobile__content__top-section__seller_btn\" (click)=\"navigateToSellerHub()\">\r\n        {{ \"footer.becomeSeller\" | translate }}\r\n\r\n      </button>\r\n    </div>\r\n    <div class=\"footer-mobile__body-section\">\r\n      <div class=\"d-inline-flex footer-mobile__body-section__section justify-content-center px-3\">\r\n<!--        <div (click)=\"reloadCurrentPage(4, 'About us')\" class=\"footer-mobile__body-section__section__link\">-->\r\n<!--          {{ \"footer.aboutUs\" | translate }}-->\r\n<!--        </div>-->\r\n        <div (click)=\"reloadCurrentPage(3, 'Privacy policy')\" class=\"footer-mobile__body-section__section__link\" >\r\n          {{ \"footer.privacyPolicy\" | translate }}\r\n        </div>\r\n        <div (click)=\"reloadCurrentPage(5, 'Terms and Conditions')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.termsAndConditions\" | translate }}\r\n        </div>\r\n        <div (click)=\"reloadCurrentPage(1, 'Shipping and return')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.shippingAndReturn\" | translate }}\r\n        </div>\r\n      </div>\r\n      <div class=\"d-inline-flex footer-mobile__body-section__section justify-content-center\">\r\n<!--        <div (click)=\"reloadCurrentPage(4, 'About us')\" class=\"footer-mobile__body-section__section__link\">-->\r\n<!--          {{ \"footer.careers\" | translate }}-->\r\n<!--        </div>-->\r\n<!--        <div (click)=\"reloadCurrentPage(2, 'Conditions of use')\" class=\"footer-mobile__body-section__section__link\">-->\r\n<!--          {{ \"footer.conditionsOfUse\" | translate }}-->\r\n<!--        </div>-->\r\n        <div (click)=\"navigatePage('contact-us')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.contactUs\" | translate }}\r\n        </div>\r\n        <div (click)=\"reloadCurrentPage(8, 'Return & Refund')\" class=\"footer-mobile__body-section__section__link\" >\r\n          {{ \"footer.returnAndRefund\" | translate }}\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"d-inline-flex footer-mobile__body-section__section justify-content-center\">\r\n        <!-- <div (click)=\"reloadCurrentPage(10, 'Dispute Resolution')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.disputeResolution\" | translate }}\r\n        </div> -->\r\n        <div (click)=\"reloadCurrentPage(9, 'Warranty Policy')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.warrantyPolicy\" | translate }}\r\n        </div>\r\n        <div (click)=\"reloadCurrentPage(11, 'FAQ')\" class=\"footer-mobile__body-section__section__link\">\r\n          {{ \"footer.faq\" | translate }}\r\n        </div>\r\n\r\n      </div>\r\n      <div *ngIf=\"!scConfig\" class=\"d-inline-flex footer-mobile__body-section__section justify-content-center\">\r\n      <div *ngFor=\"let logo of footerDetails.payments\" class=\"footer-mobile__body-section__section\">\r\n        <img alt=\"No Image\" [src]=\"logo\">\r\n      </div>\r\n      </div>\r\n      <div class=\"d-flex flex-column footer-mobile__body-section__section\">\r\n            <div class=\"d-flex justify-content-center footer-mobile__body-section__section__contact-us\">\r\n              {{ \"footer.reachOut\" | translate }}\r\n\r\n            </div>\r\n            <div class=\"d-flex justify-content-center footer-mobile__body-section__section__social-links\">\r\n              <div *ngFor=\"let logo of footerDetails.socials\">\r\n                <a [href]=\"logo.url\" target=\"_blank\">\r\n                <img alt=\"No Image\" [src]=\"logo.icon\">\r\n                </a>\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"!scConfig\" class=\"d-flex justify-content-center footer-mobile__body-section__section__rights-reserved\">\r\n             {{footerDetails.copyRightsMobile | translate}}\r\n            </div>\r\n      </div>\r\n      <div *ngIf=\"!scConfig\" class=\"d-inline-flex footer-mobile__body-section__section justify-content-center footer-bottom\">\r\n        <img alt=\"No Image\" [src]=\"footerDetails.bottonLogo\">\r\n      </div>\r\n\r\n\r\n    </div>\r\n  </div>\r\n\r\n</footer>\r\n"], "mappings": "AAAA,SAAmBA,YAAY,EAAuCC,WAAW,QAAO,eAAe;AAGvG,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,iBAAiB,QAAO,kCAAkC;;;;;;;;;IC6ExDC,EAAA,CAAAC,SAAA,cAAqE;;;;IAA/BD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,aAAA,CAAAC,QAAA,EAAAL,EAAA,CAAAM,aAAA,CAA8B;;;;;IACpEN,EAAA,CAAAC,SAAA,cAAuE;;;;;IAcrED,EAAA,CAAAO,cAAA,cAAmD;IAKhDP,EAAA,CAAAC,SAAA,SACA;IAAAD,EAAA,CAAAQ,YAAA,EAAI;;;;IAHHR,EAAA,CAAAS,SAAA,GAAsB;IAAtBT,EAAA,CAAAE,UAAA,SAAAQ,OAAA,CAAAC,QAAA,EAAAX,EAAA,CAAAM,aAAA,CAAsB;IAEnBN,EAAA,CAAAS,SAAA,GAA0B;IAA1BT,EAAA,CAAAY,UAAA,CAAAF,OAAA,CAAAG,SAAA,CAA0B;;;;;IAXvCb,EAAA,CAAAO,cAAA,cAGC;IAGKP,EAAA,CAAAc,UAAA,IAAAC,8CAAA,kBAOM;IACRf,EAAA,CAAAQ,YAAA,EAAM;;;;IAR+BR,EAAA,CAAAS,SAAA,GAAc;IAAdT,EAAA,CAAAE,UAAA,YAAAc,MAAA,CAAAC,WAAA,CAAc;;;;;IA2BjDjB,EAAA,CAAAO,cAAA,cAA+F;IAC7FP,EAAA,CAAAC,SAAA,cAAiC;IACnCD,EAAA,CAAAQ,YAAA,EAAM;;;;IADgBR,EAAA,CAAAS,SAAA,GAAY;IAAZT,EAAA,CAAAE,UAAA,QAAAgB,OAAA,EAAAlB,EAAA,CAAAM,aAAA,CAAY;;;;;IAd1CN,EAAA,CAAAO,cAAA,cAA+E;IAIvEP,EAAA,CAAAmB,MAAA,GACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,UAAK;IACHP,EAAA,CAAAC,SAAA,cAAmD;IACrDD,EAAA,CAAAQ,YAAA,EAAM;IAGRR,EAAA,CAAAO,cAAA,cAA4C;IAExCP,EAAA,CAAAc,UAAA,KAAAM,+CAAA,kBAEM;IACRpB,EAAA,CAAAQ,YAAA,EAAM;IAENR,EAAA,CAAAO,cAAA,eAAiD;IAC/CP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;;;;IAhBJR,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,+BACF;IAEsBtB,EAAA,CAAAS,SAAA,GAA8B;IAA9BT,EAAA,CAAAE,UAAA,QAAAqB,MAAA,CAAAnB,aAAA,CAAAoB,QAAA,EAAAxB,EAAA,CAAAM,aAAA,CAA8B;IAM5BN,EAAA,CAAAS,SAAA,GAAyB;IAAzBT,EAAA,CAAAE,UAAA,YAAAqB,MAAA,CAAAnB,aAAA,CAAAqB,QAAA,CAAyB;IAM/CzB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,QAAAC,MAAA,CAAAnB,aAAA,CAAAsB,UAAA,OACF;;;;;;IAnIV1B,EAAA,CAAAO,cAAA,gBAAgE;IACzDP,EAAA,CAAA2B,UAAA,mBAAAC,uDAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAF,OAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAExBlC,EAAA,CAAAC,SAAA,YAAgC;IAEhCD,EAAA,CAAAO,cAAA,UAAK;IAAAP,EAAA,CAAAmB,MAAA,GAAoC;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAEjDR,EAAA,CAAAO,cAAA,aAA6B;IAMnBP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA6F;IAAxFP,EAAA,CAAA2B,UAAA,mBAAAQ,wDAAA;MAAAnC,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAApC,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAG,OAAA,CAAAC,iBAAA,CAAkB,CAAC,EAAE,UAAU,CAAC;IAAA,EAAC;IAC7CrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAAkG;IAA7FP,EAAA,CAAA2B,UAAA,mBAAAW,wDAAA;MAAAtC,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAAvC,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAM,OAAA,CAAAF,iBAAA,CAAkB,CAAC,EAAE,sBAAsB,CAAC;IAAA,EAAC;IACzDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAAwG;IAAnGP,EAAA,CAAA2B,UAAA,mBAAAa,wDAAA;MAAAxC,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAzC,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAQ,OAAA,CAAAJ,iBAAA,CAAkB,CAAC,EAAE,qBAAqB,CAAC;IAAA,EAAC;IACxDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA6F;IAAxFP,EAAA,CAAA2B,UAAA,mBAAAe,wDAAA;MAAA1C,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAa,OAAA,GAAA3C,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAU,OAAA,CAAAN,iBAAA,CAAkB,CAAC,EAAE,gBAAgB,CAAC;IAAA,EAAC;IACnDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAAsG;IAAjGP,EAAA,CAAA2B,UAAA,mBAAAiB,wDAAA;MAAA5C,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAe,OAAA,GAAA7C,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAY,OAAA,CAAAR,iBAAA,CAAkB,CAAC,EAAE,mBAAmB,CAAC;IAAA,EAAC;IACtDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAIRR,EAAA,CAAAO,cAAA,cAA4D;IAExDP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAAmF;IACjFP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA4F;IAC1FP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA4F;IAC1FP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAKRR,EAAA,CAAAO,cAAA,cAA4D;IAExDP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA+F;IAA1FP,EAAA,CAAA2B,UAAA,mBAAAmB,wDAAA;MAAA9C,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAiB,OAAA,GAAA/C,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAc,OAAA,CAAAC,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IACvChD,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA8F;IAAzFP,EAAA,CAAA2B,UAAA,mBAAAsB,wDAAA;MAAAjD,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAoB,OAAA,GAAAlD,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAiB,OAAA,CAAAb,iBAAA,CAAkB,CAAC,EAAE,iBAAiB,CAAC;IAAA,EAAC;IACpDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAINR,EAAA,CAAAO,cAAA,eAA8F;IAAzFP,EAAA,CAAA2B,UAAA,mBAAAwB,wDAAA;MAAAnD,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAsB,OAAA,GAAApD,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAmB,OAAA,CAAAf,iBAAA,CAAkB,CAAC,EAAE,iBAAiB,CAAC;IAAA,EAAC;IACpDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAAmF;IAA9EP,EAAA,CAAA2B,UAAA,mBAAA0B,wDAAA;MAAArD,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAwB,OAAA,GAAAtD,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAqB,OAAA,CAAAjB,iBAAA,CAAkB,EAAE,EAAE,KAAK,CAAC;IAAA,EAAC;IACzCrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAGRR,EAAA,CAAAO,cAAA,cAA4D;IAExDP,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA0F;IAArFP,EAAA,CAAA2B,UAAA,mBAAA4B,wDAAA;MAAAvD,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAA0B,OAAA,GAAAxD,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAuB,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAClCzD,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAGRR,EAAA,CAAAO,cAAA,cAA4D;IAE1DP,EAAA,CAAAc,UAAA,KAAA4C,wCAAA,kBAAqE;IACrE1D,EAAA,CAAAc,UAAA,KAAA6C,wCAAA,kBAAuE;IACzE3D,EAAA,CAAAQ,YAAA,EAAM;IAMVR,EAAA,CAAAO,cAAA,eAAiF;IAC/EP,EAAA,CAAAc,UAAA,KAAA8C,wCAAA,kBAiBM;IACR5D,EAAA,CAAAQ,YAAA,EAAM;IAENR,EAAA,CAAAc,UAAA,KAAA+C,wCAAA,mBA2BM;IAGR7D,EAAA,CAAAQ,YAAA,EAAM;;;;IAxICR,EAAA,CAAAS,SAAA,GAAoC;IAApCT,EAAA,CAAA8D,iBAAA,CAAA9D,EAAA,CAAAsB,WAAA,4BAAoC;IAQjCtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,gCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,gCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,2CACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,0CACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,sCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,wCACF;IAMEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,gCACF;IACKtB,EAAA,CAAAS,SAAA,GAAwB;IAAxBT,EAAA,CAAAE,UAAA,yBAAwB;IAC3BF,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,iCACF;IACKtB,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAE,UAAA,kCAAiC;IACpCF,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,kCACF;IACKtB,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAE,UAAA,kCAAiC;IACpCF,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,kCACF;IAOEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,6BACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,kCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,wCACF;IAKEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,uCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,4BACF;IAKEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,kCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,0CACF;IAKMtB,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAA6D,MAAA,CAAAC,QAAA,CAAe;IACfhE,EAAA,CAAAS,SAAA,GAAc;IAAdT,EAAA,CAAAE,UAAA,SAAA6D,MAAA,CAAAC,QAAA,CAAc;IASrBhE,EAAA,CAAAS,SAAA,GAAc;IAAdT,EAAA,CAAAE,UAAA,SAAA6D,MAAA,CAAAC,QAAA,CAAc;IAmB2ChE,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAA6D,MAAA,CAAAC,QAAA,CAAe;;;;;IA0CzEhE,EAAA,CAAAC,SAAA,cAA2E;;;;IAArCD,EAAA,CAAAE,UAAA,QAAA+D,OAAA,CAAA7D,aAAA,CAAA8D,cAAA,EAAAlE,EAAA,CAAAM,aAAA,CAAoC;;;;;IAC1EN,EAAA,CAAAC,SAAA,cAAuE;;;;;IAkDzED,EAAA,CAAAO,cAAA,cAA8F;IAC5FP,EAAA,CAAAC,SAAA,cAAiC;IACnCD,EAAA,CAAAQ,YAAA,EAAM;;;;IADgBR,EAAA,CAAAS,SAAA,GAAY;IAAZT,EAAA,CAAAE,UAAA,QAAAiE,QAAA,EAAAnE,EAAA,CAAAM,aAAA,CAAY;;;;;IAFlCN,EAAA,CAAAO,cAAA,cAAyG;IACzGP,EAAA,CAAAc,UAAA,IAAAsD,8CAAA,kBAEM;IACNpE,EAAA,CAAAQ,YAAA,EAAM;;;;IAHgBR,EAAA,CAAAS,SAAA,GAAyB;IAAzBT,EAAA,CAAAE,UAAA,YAAAmE,OAAA,CAAAjE,aAAA,CAAAqB,QAAA,CAAyB;;;;;IAUvCzB,EAAA,CAAAO,cAAA,UAAgD;IAE9CP,EAAA,CAAAC,SAAA,cAAsC;IACtCD,EAAA,CAAAQ,YAAA,EAAI;;;;IAFDR,EAAA,CAAAS,SAAA,GAAiB;IAAjBT,EAAA,CAAAE,UAAA,SAAAoE,QAAA,CAAAC,GAAA,EAAAvE,EAAA,CAAAM,aAAA,CAAiB;IACAN,EAAA,CAAAS,SAAA,GAAiB;IAAjBT,EAAA,CAAAE,UAAA,QAAAoE,QAAA,CAAAE,IAAA,EAAAxE,EAAA,CAAAM,aAAA,CAAiB;;;;;IAIzCN,EAAA,CAAAO,cAAA,cAAmH;IAClHP,EAAA,CAAAmB,MAAA,GACD;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;;;;IADLR,EAAA,CAAAS,SAAA,GACD;IADCT,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,OAAAmD,OAAA,CAAArE,aAAA,CAAAsE,gBAAA,OACD;;;;;IAEN1E,EAAA,CAAAO,cAAA,cAAuH;IACrHP,EAAA,CAAAC,SAAA,cAAqD;IACvDD,EAAA,CAAAQ,YAAA,EAAM;;;;IADgBR,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAE,UAAA,QAAAyE,OAAA,CAAAvE,aAAA,CAAAwE,UAAA,EAAA5E,EAAA,CAAAM,aAAA,CAAgC;;;;;;IAlF5DN,EAAA,CAAAO,cAAA,iBAAkF;IAC3EP,EAAA,CAAA2B,UAAA,mBAAAkD,uDAAA;MAAA7E,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAA8C,OAAA,CAAA7C,SAAA,EAAW;IAAA,EAAC;IAExBlC,EAAA,CAAAC,SAAA,YAAgC;IAEhCD,EAAA,CAAAO,cAAA,UAAK;IAAAP,EAAA,CAAAmB,MAAA,GAAoC;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAEjDR,EAAA,CAAAO,cAAA,cAAyC;IAGnCP,EAAA,CAAAc,UAAA,IAAAkE,uCAAA,kBAA2E;IAC3EhF,EAAA,CAAAc,UAAA,KAAAmE,wCAAA,kBAAuE;IACzEjF,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,kBAAgG;IAAhCP,EAAA,CAAA2B,UAAA,mBAAAuD,2DAAA;MAAAlF,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAK,OAAA,GAAAnF,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAkD,OAAA,CAAA1B,mBAAA,EAAqB;IAAA,EAAC;IAC7FzD,EAAA,CAAAmB,MAAA,IAEF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAS;IAEXR,EAAA,CAAAO,cAAA,eAAyC;IAKhCP,EAAA,CAAA2B,UAAA,mBAAAyD,wDAAA;MAAApF,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAO,OAAA,GAAArF,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAoD,OAAA,CAAAhD,iBAAA,CAAkB,CAAC,EAAE,gBAAgB,CAAC;IAAA,EAAC;IACnDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA+G;IAA1GP,EAAA,CAAA2B,UAAA,mBAAA2D,wDAAA;MAAAtF,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAS,OAAA,GAAAvF,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAsD,OAAA,CAAAlD,iBAAA,CAAkB,CAAC,EAAE,sBAAsB,CAAC;IAAA,EAAC;IACzDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA8G;IAAzGP,EAAA,CAAA2B,UAAA,mBAAA6D,wDAAA;MAAAxF,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAW,OAAA,GAAAzF,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAwD,OAAA,CAAApD,iBAAA,CAAkB,CAAC,EAAE,qBAAqB,CAAC;IAAA,EAAC;IACxDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAERR,EAAA,CAAAO,cAAA,eAAuF;IAOhFP,EAAA,CAAA2B,UAAA,mBAAA+D,wDAAA;MAAA1F,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAa,OAAA,GAAA3F,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAA0D,OAAA,CAAA3C,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IACvChD,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA2G;IAAtGP,EAAA,CAAA2B,UAAA,mBAAAiE,wDAAA;MAAA5F,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAe,OAAA,GAAA7F,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAA4D,OAAA,CAAAxD,iBAAA,CAAkB,CAAC,EAAE,iBAAiB,CAAC;IAAA,EAAC;IACpDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAGRR,EAAA,CAAAO,cAAA,eAAuF;IAIhFP,EAAA,CAAA2B,UAAA,mBAAAmE,wDAAA;MAAA9F,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAiB,OAAA,GAAA/F,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAA8D,OAAA,CAAA1D,iBAAA,CAAkB,CAAC,EAAE,iBAAiB,CAAC;IAAA,EAAC;IACpDrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA+F;IAA1FP,EAAA,CAAA2B,UAAA,mBAAAqE,wDAAA;MAAAhG,EAAA,CAAA6B,aAAA,CAAAiD,IAAA;MAAA,MAAAmB,OAAA,GAAAjG,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAgE,OAAA,CAAA5D,iBAAA,CAAkB,EAAE,EAAE,KAAK,CAAC;IAAA,EAAC;IACzCrC,EAAA,CAAAmB,MAAA,IACF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IAGRR,EAAA,CAAAc,UAAA,KAAAoF,wCAAA,kBAIM;IACNlG,EAAA,CAAAO,cAAA,eAAqE;IAE7DP,EAAA,CAAAmB,MAAA,IAEF;;IAAAnB,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAO,cAAA,eAA8F;IAC5FP,EAAA,CAAAc,UAAA,KAAAqF,wCAAA,kBAIM;IACRnG,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAc,UAAA,KAAAsF,wCAAA,kBAEM;IACZpG,EAAA,CAAAQ,YAAA,EAAM;IACNR,EAAA,CAAAc,UAAA,KAAAuF,wCAAA,kBAEM;IAGRrG,EAAA,CAAAQ,YAAA,EAAM;;;;IAjFDR,EAAA,CAAAS,SAAA,GAAoC;IAApCT,EAAA,CAAA8D,iBAAA,CAAA9D,EAAA,CAAAsB,WAAA,4BAAoC;IAK/BtB,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAAoG,MAAA,CAAAtC,QAAA,CAAe;IACfhE,EAAA,CAAAS,SAAA,GAAc;IAAdT,EAAA,CAAAE,UAAA,SAAAoG,MAAA,CAAAtC,QAAA,CAAc;IAGpBhE,EAAA,CAAAS,SAAA,GAEF;IAFET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,qCAEF;IAQItB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,sCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,2CACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,0CACF;IAUEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,kCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,wCACF;IAQEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,uCACF;IAEEtB,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,4BACF;IAGItB,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAAoG,MAAA,CAAAtC,QAAA,CAAe;IAObhE,EAAA,CAAAS,SAAA,GAEF;IAFET,EAAA,CAAAqB,kBAAA,MAAArB,EAAA,CAAAsB,WAAA,iCAEF;IAEwBtB,EAAA,CAAAS,SAAA,GAAwB;IAAxBT,EAAA,CAAAE,UAAA,YAAAoG,MAAA,CAAAlG,aAAA,CAAAmG,OAAA,CAAwB;IAM1CvG,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAAoG,MAAA,CAAAtC,QAAA,CAAe;IAIrBhE,EAAA,CAAAS,SAAA,GAAe;IAAfT,EAAA,CAAAE,UAAA,UAAAoG,MAAA,CAAAtC,QAAA,CAAe;;;ADnN3B,OAAM,MAAOwC,eAAe;EAe1BC,QAAQA,CAACC,KAAY;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EA0BQA,eAAeA,CAAA;IACrB,IAAI7G,iBAAiB,CAAC,IAAI,CAAC8G,UAAU,CAAC,EAAE;MACtC,MAAMC,SAAS,GAAIC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,MAAM,CAACC,UAAU,GAAI,GAAG;MACtE,IAAIJ,SAAS,IAAI,EAAE,EAAE;QACnB,IAAI,CAACK,cAAc,GAAG,UAAU;OACjC,MACI,IAAIL,SAAS,IAAI,GAAG,EAAE;QACzB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MACI,IAAIL,SAAS,IAAI,GAAG,EAAE;QACzB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,cAAc;;;EAI1C;EACAC,YAAoBC,KAAmB,EAASC,MAAc,EAA+BT,UAAe,EACxFU,cAA8B,EAC9BC,iBAAoC,EACpCC,UAAkC,EAClCC,WAAwB;IAJxB,KAAAL,KAAK,GAALA,KAAK;IAAuB,KAAAC,MAAM,GAANA,MAAM;IAAuC,KAAAT,UAAU,GAAVA,UAAU;IACnF,KAAAU,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IAvE/B,KAAAC,kBAAkB,GAAY7H,WAAW,CAAC8H,YAAY;IAC5C,KAAAC,WAAW,GAAsB,IAAIjI,YAAY,EAAO;IAElE,KAAAqE,QAAQ,GAAY,KAAK;IAGzB,KAAA6D,WAAW,GAAQ,EAAE;IACF,KAAAhI,WAAW,GAAGA,WAAW;IAC5C,KAAAqH,cAAc,GAAW,cAAc;IACvC,KAAAY,gBAAgB,GAAS,KAAK;IAC9B,KAAAC,iBAAiB,GAAS,KAAK;IAM/B,KAAA9G,WAAW,GAAQ,CACjB;MACEJ,SAAS,EAAE,8BAA8B;MACzCF,QAAQ,EAAE,2CAA2C;MACrDqH,QAAQ,EAAE;KACX,EAAC;MACAnH,SAAS,EAAE,6BAA6B;MACxCF,QAAQ,EAAE,qCAAqC;MAC/CqH,QAAQ,EAAE;KACX,EAAC;MACAnH,SAAS,EAAE,sBAAsB;MACjCF,QAAQ,EAAE,4CAA4C;MACtDqH,QAAQ,EAAE;KACX,EAAC;MACAnH,SAAS,EAAE,wBAAwB;MACnCF,QAAQ,EAAE,6CAA6C;MACvDqH,QAAQ,EAAE;KACX,EAAC;MACAnH,SAAS,EAAE,qBAAqB;MAChCF,QAAQ,EAAE,wCAAwC;MAClDqH,QAAQ,EAAE;KACX,CACF;IAkCC,IAAI,CAACF,gBAAgB,GAAG,IAAI,CAACP,iBAAiB,CAACU,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACR,iBAAiB,CAACU,aAAa,CAAC,kBAAkB,CAAC;IAEjF,IAAI,CAACjE,QAAQ,GAAGnE,WAAW,CAAC8H,YAAY;IACxC,IAAI7H,iBAAiB,CAAC,IAAI,CAAC8G,UAAU,CAAC,EAAE;MACtC,IAAI,CAACsB,WAAW,GAAGpB,MAAM,CAACE,MAAM,CAACmB,KAAK;MACtC,IAAI,IAAI,CAACD,WAAW,GAAG,GAAG,EAAE;QAC1B,IAAI,CAACE,WAAW,GAAG,IAAI;OACxB,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,KAAK;;;IAI5B,IAAG,CAAC,IAAI,CAACX,WAAW,CAACY,aAAa,EAAE,EAAC;MACnC,MAAMC,WAAW,GAAG,IAAI,CAAChB,cAAc,CAACiB,eAAe;MACvD,IAAGD,WAAW,EAAE;QACd,KAAK,MAAME,IAAI,IAAI,IAAI,CAACvH,WAAW,EAAE;UACnC,IAAIqH,WAAW,CAACG,cAAc,CAACD,IAAI,CAACR,QAAQ,CAAC,IAAIM,WAAW,CAACE,IAAI,CAACR,QAAQ,CAAC,KAAK,IAAI,EAAE;YACpFQ,IAAI,CAACE,OAAO,GAAGJ,WAAW,CAACE,IAAI,CAACR,QAAQ,CAAC;;;;;EAKnD;EACA3F,iBAAiBA,CAACsG,MAAc,EAAEC,KAAa;IAC7C,IAAG,IAAI,CAACb,iBAAiB,EAAC;MAC1B,IAAIa,KAAK,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;QAC1C,IAAI,CAACrB,UAAU,CAACd,KAAK,CAAC3G,iBAAiB,CAAC+I,6BAA6B,EAAE,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE,IAAI,CAAC;OAC3G,MAAM,IAAIF,KAAK,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,CAACrB,UAAU,CAACd,KAAK,CAAC3G,iBAAiB,CAACgJ,uBAAuB,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC;;;IAIjG,IAAI,CAAC1B,MAAM,CAAC2B,aAAa,CAAC,GAAG,EAAE;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MAChE,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;MACnCC,WAAW,EAAE;QAAET,MAAM,EAAEA,MAAM;QAAEC,KAAK,EAAEA;MAAK;KAC5C,CAAC,CACH;EACH;EAEA5F,YAAYA,CAACuB,GAAW;IACtB,IAAI,CAAC8C,MAAM,CACR2B,aAAa,CAAC,GAAG,EAAE;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC,CAChDC,IAAI,CAAC,MAAM,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,GAAG,GAAG5E,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EACxD;EAEA8E,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAEd,IAAI,CAAClC,KAAK,CAACmC,YAAY,CAAC,QAAQ,CAAC,CAACC,SAAS,CAAC;QAC1CC,IAAI,EAAGC,GAAQ,IAAM,IAAI,CAACC,MAAM,GAAGD;OACpC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAACtC,KAAK,CAACmC,YAAY,CAAC,gBAAgB,CAAC,CAACC,SAAS,CAAC;MAClDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACE,MAAM,EAAE;UACd,IAAI,CAAC/B,WAAW,GAAGgC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;;MAE1D;KACD,CAAC;EACJ;EAEA5H,SAASA,CAAA;IACP4E,MAAM,CAACiD,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC;IAClB,IAAIjK,iBAAiB,CAAC,IAAI,CAAC8G,UAAU,CAAC,EAAE;MACtCE,MAAM,CAACiD,MAAM,CAAC;QACZC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAACtC,WAAW,CAACuC,IAAI,EAAE;;EAE3B;EACA1G,mBAAmBA,CAAA;IACjB,IAAG,IAAI,CAACsE,iBAAiB,EAAC;MAC1B,IAAI,CAACP,UAAU,CAACd,KAAK,CAAC3G,iBAAiB,CAACqK,mCAAmC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;;IAE7F,IAAItK,iBAAiB,CAAC,IAAI,CAAC8G,UAAU,CAAC,EAAE;MACtCE,MAAM,CAACuD,IAAI,CAACxK,WAAW,CAACyK,WAAW,GAAG,YAAY,GAAG,GAAGT,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC;;EAEzG;EAAC,QAAAS,CAAA,G;qBA3JU/D,eAAe,EAAAxG,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA1K,EAAA,CAAAwK,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA5K,EAAA,CAAAwK,iBAAA,CAsE8C5K,WAAW,GAAAI,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAA7K,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAK,iBAAA,GAAA9K,EAAA,CAAAwK,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAhL,EAAA,CAAAwK,iBAAA,CAAAC,EAAA,CAAAQ,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAtExE1E,eAAe;IAAA2E,SAAA;IAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAfC,GAAA,CAAA9E,QAAA,CAAA+E,MAAA,CAAgB;QAAA,UAAAxL,EAAA,CAAAyL,eAAA;;;;;;;;;;;;;;;QCb7BzL,EAAA,CAAAc,UAAA,IAAA4K,iCAAA,sBA8IS;QACT1L,EAAA,CAAAc,UAAA,IAAA6K,iCAAA,sBAyFS;;;QAxOe3L,EAAA,CAAAE,UAAA,SAAAqL,GAAA,CAAAnD,WAAA,KAAAmD,GAAA,CAAAzD,gBAAA,CAAsC;QA+I/B9H,EAAA,CAAAS,SAAA,GAAiD;QAAjDT,EAAA,CAAAE,UAAA,UAAAqL,GAAA,CAAAnD,WAAA,KAAAmD,GAAA,CAAAK,MAAA,IAAAL,GAAA,CAAAzD,gBAAA,CAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}