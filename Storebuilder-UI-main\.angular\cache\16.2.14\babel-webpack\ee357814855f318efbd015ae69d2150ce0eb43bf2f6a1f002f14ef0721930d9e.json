{"ast": null, "code": "import { PaymentErrorDialogComponent } from \"../payment-error-dialog/payment-error-dialog.component\";\nimport { PaymentWaitingDialogComponent } from \"../payment-waiting-dialog/payment-waiting-dialog.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/ripple\";\nimport * as i4 from \"primeng/selectbutton\";\nimport * as i5 from \"primeng/inputmask\";\nimport * as i6 from \"primeng/checkbox\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/button\";\nfunction PaymentDialogComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\");\n    i0.ɵɵelementStart(1, \"h2\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    i0.ɵɵclassMap(item_r7.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.name);\n  }\n}\nfunction PaymentDialogComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 9)(3, \"div\", 19)(4, \"div\", 20)(5, \"h3\", 21);\n    i0.ɵɵtext(6, \" How it works? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"button\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"p\", 23);\n    i0.ɵɵtext(10, \" You need to have a MoMo account in order to proceed. We'll send you a request via the mobile app and USSD. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"p\", 24);\n    i0.ɵɵtext(13, \" Don't have MoMo yet? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function PaymentDialogComponent_ng_container_11_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSignupClick());\n    });\n    i0.ɵɵtext(15, \" Sign-up now\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 9)(17, \"div\", 26)(18, \"div\", 27)(19, \"span\", 28)(20, \"p-inputMask\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_container_11_Template_p_inputMask_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.phoneNumber = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"label\");\n    i0.ɵɵtext(22, \"Phone number *\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.phoneNumber);\n  }\n}\nfunction PaymentDialogComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 9)(2, \"div\", 26)(3, \"span\", 30)(4, \"p-inputMask\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_template_12_Template_p_inputMask_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.creditcardNumber = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\");\n    i0.ɵɵtext(6, \"Card Number\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33)(9, \"span\", 30)(10, \"p-inputMask\", 34);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_template_12_Template_p_inputMask_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.creditcardNumber = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\");\n    i0.ɵɵtext(12, \"MM/YY\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 32)(14, \"div\", 33)(15, \"span\", 30)(16, \"p-inputMask\", 35);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_template_12_Template_p_inputMask_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.creditcardNumber = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\");\n    i0.ɵɵtext(18, \"123\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 33)(21, \"span\", 30)(22, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_template_12_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.CardholderName = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"label\", 37);\n    i0.ɵɵtext(24, \"Cardholder name\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 9)(26, \"div\", 38)(27, \"p-checkbox\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_ng_template_12_Template_p_checkbox_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.checked = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"label\", 40);\n    i0.ɵɵtext(29, \"Save card for later\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.creditcardNumber);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.creditcardNumber);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.creditcardNumber);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.CardholderName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.checked)(\"binary\", true);\n  }\n}\nfunction PaymentDialogComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PaymentDialogComponent_ng_container_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.errorPayment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction PaymentDialogComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p\", 42);\n    i0.ɵɵelement(1, \"em\", 43);\n    i0.ɵɵtext(2, \" All your informations is safe and secured \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function PaymentDialogComponent_ng_template_32_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.waitingPayment());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PaymentDialogComponent {\n  constructor(dialogService, previousref, config) {\n    this.dialogService = dialogService;\n    this.previousref = previousref;\n    this.config = config;\n    this.value = 1;\n    this.checked = false;\n    this.currencyCode = '';\n    this.paymentOptions = [{\n      name: 'MoMo',\n      value: 1,\n      icon: 'pi pi-credit-card'\n    }, {\n      name: 'Card',\n      value: 2,\n      icon: 'pi pi-credit-card'\n    }];\n  }\n  ngOnInit() {\n    let currency = localStorage.getItem('currency')?.toString();\n    if (currency) this.currencyCode = currency;\n  }\n  errorPayment() {\n    this.previousref.destroy();\n    this.ref = this.dialogService.open(PaymentErrorDialogComponent, {\n      width: '30%',\n      height: '100%',\n      closable: false,\n      showHeader: false,\n      closeOnEscape: true\n    });\n  }\n  waitingPayment() {\n    this.previousref.destroy();\n    this.ref = this.dialogService.open(PaymentWaitingDialogComponent, {\n      width: '30%',\n      height: '100%',\n      closable: false,\n      showHeader: false,\n      closeOnEscape: true\n    });\n  }\n  ngOnDestroy() {\n    if (this.ref) {\n      this.ref.close();\n    }\n  }\n}\nPaymentDialogComponent.ɵfac = function PaymentDialogComponent_Factory(t) {\n  return new (t || PaymentDialogComponent)(i0.ɵɵdirectiveInject(i1.DialogService), i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig));\n};\nPaymentDialogComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: PaymentDialogComponent,\n  selectors: [[\"app-payment-dialog\"]],\n  decls: 34,\n  vars: 7,\n  consts: [[1, \"payment-dialog\"], [1, \"border-none\", \"shadow-4\", \"h-3rem\"], [1, \"padding-21\"], [1, \"grid\"], [1, \"col-12\", \"col-md-12\", \"col-lg-12\", \"align-items-start\", \"justify-content-start\"], [1, \"m-0\", \"font-size-18\", \"text-900\", \"bold-font\"], [1, \"col-12\", \"col-md-12\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"options\", \"ngModel\", \"ngModelChange\"], [\"class\", \"width-50 justify-content-center\"], [1, \"col-12\"], [4, \"ngIf\", \"ngIfElse\"], [\"cart\", \"\"], [1, \"flex\", \"justify-content-between\", \"flex-wrap\", \"card-container\", \"purple-container\"], [1, \"font-size-15\", \"flex\", \"align-items-start\", \"justify-content-start\", \"mt-0\"], [1, \"font-size-15\", \"text-500\", \"flex\", \"align-items-end\", \"justify-content-end\", \"mt-0\"], [1, \"font-size-15\", \"font-bold\", \"text-900\", \"flex\", \"align-items-start\", \"justify-content-start\", \"m-0\"], [1, \"font-size-15\", \"font-bold\", \"text-900\", \"flex\", \"align-items-end\", \"justify-content-end\", \"m-0\"], [\"cartButton\", \"\"], [1, \"font-size-14\"], [1, \"grid\", \"border-1\", \"border-round\", \"border-200\", \"m-0\", \"mt-2\"], [1, \"col-12\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-content-between\"], [1, \"font-size-16\", \"bold-font\", \"mb-0\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times\", 1, \"p-button-rounded\", \"p-button-text\", \"p-button-plain\"], [1, \"text-400\", \"font-size-14\", \"mt-0\"], [1, \"text-400\", \"font-size-14\", \"mt-0\", \"inline\"], [1, \"no-underline\", \"main-color\", \"font-size-12\", \"bold-font\", \"mb-4\", 3, \"click\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\"], [1, \"p-float-label\", \"mt-3\"], [\"inputId\", \"phone-number\", \"mask\", \"+999 999-999-999\", 3, \"ngModel\", \"ngModelChange\"], [1, \"p-float-label\", \"p-field\", \"p-col-12\", \"mt-3\"], [\"mask\", \"************** 9999\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"col-md-6\", \"col-lg-6\"], [1, \"p-field\", \"p-grid\"], [\"mask\", \"99/99\", 3, \"ngModel\", \"ngModelChange\"], [\"mask\", \"999\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"id\", \"inputtext\", \"pInputText\", \"\", 1, \"width-100\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"inputtext\"], [1, \"field-checkbox\"], [\"id\", \"save\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"save\", 1, \"text-400\", \"font-size-14\"], [\"label\", \"Request payment\", \"pButton\", \"\", \"type\", \"button\", 1, \"col-12\", \"my-2\", \"width-100\", \"second-btn\", 3, \"click\"], [1, \"text-center\", \"text-green-500\"], [1, \"pi\", \"pi-lock\", \"text-green-500\"], [\"label\", \"Place your order\", \"pButton\", \"\", \"type\", \"button\", 1, \"col-12\", \"my-2\", \"width-100\", \"second-btn\", 3, \"click\"]],\n  template: function PaymentDialogComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"section\", 0);\n      i0.ɵɵelement(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\", 5);\n      i0.ɵɵtext(6, \" Pay with \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-selectButton\", 7);\n      i0.ɵɵlistener(\"ngModelChange\", function PaymentDialogComponent_Template_p_selectButton_ngModelChange_8_listener($event) {\n        return ctx.value = $event;\n      });\n      i0.ɵɵtemplate(9, PaymentDialogComponent_ng_template_9_Template, 3, 3, \"ng-template\", 8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(10, \"div\", 9);\n      i0.ɵɵtemplate(11, PaymentDialogComponent_ng_container_11_Template, 23, 1, \"ng-container\", 10);\n      i0.ɵɵtemplate(12, PaymentDialogComponent_ng_template_12_Template, 30, 6, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 12)(16, \"p\", 13);\n      i0.ɵɵtext(17, \" Merchant name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"p\", 14);\n      i0.ɵɵtext(19, \" AfriElec \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(20, \"div\", 12)(21, \"p\", 13);\n      i0.ɵɵtext(22, \" Merchant ref \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"p\", 14);\n      i0.ɵɵtext(24, \" 12345678 \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(25, \"div\", 12)(26, \"p\", 15);\n      i0.ɵɵtext(27, \" Payment Total \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"p\", 16);\n      i0.ɵɵtext(29);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(30, \"div\", 9);\n      i0.ɵɵtemplate(31, PaymentDialogComponent_ng_container_31_Template, 2, 0, \"ng-container\", 10);\n      i0.ɵɵtemplate(32, PaymentDialogComponent_ng_template_32_Template, 4, 0, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      const _r2 = i0.ɵɵreference(13);\n      const _r5 = i0.ɵɵreference(33);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"options\", ctx.paymentOptions)(\"ngModel\", ctx.value);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.value === 1)(\"ngIfElse\", _r2);\n      i0.ɵɵadvance(18);\n      i0.ɵɵtextInterpolate1(\" \", ctx.currencyCode, \" 10,001,400.00 \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.value === 1)(\"ngIfElse\", _r5);\n    }\n  },\n  dependencies: [i2.NgIf, i3.Ripple, i4.SelectButton, i5.InputMask, i6.Checkbox, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i8.ButtonDirective],\n  styles: [\".p-dialog[_ngcontent-%COMP%]   .p-dialog-header[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.p-buttonset[_ngcontent-%COMP%]   .p-button[_ngcontent-%COMP%] {\\n  width: 50%;\\n  background: #F5F5F5;\\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.03), 0px 0px 2px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.12) !important;\\n  border: none;\\n}\\n\\n.p-selectbutton[_ngcontent-%COMP%]   .p-button.p-highlight[_ngcontent-%COMP%]:hover {\\n  background: var(--fourth-color);\\n  border-color: var(--fourth-color);\\n  color: black;\\n}\\n\\n.p-selectbutton[_ngcontent-%COMP%]   .p-button.p-highlight[_ngcontent-%COMP%] {\\n  background: var(--fourth-color);\\n  border-color: var(--fourth-color);\\n  color: black;\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  background: #F5F5F5;\\n  color: #323232;\\n}\\n\\n.p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #323232;\\n}\\n\\n.p-ripple.p-element.p-button[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}