{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() {}\nconst hasXHR2 = function () {\n  const xhr = new XMLHttpRequest({\n    xdomain: false\n  });\n  return null != xhr.responseType;\n}();\nexport class Polling extends Transport {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @package\n   */\n  constructor(opts) {\n    super(opts);\n    this.polling = false;\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? \"443\" : \"80\";\n      }\n      this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n    if (this.opts.withCredentials) {\n      this.cookieJar = createCookieJar();\n    }\n  }\n  get name() {\n    return \"polling\";\n  }\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @protected\n   */\n  doOpen() {\n    this.poll();\n  }\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n   * @package\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n    const pause = () => {\n      this.readyState = \"paused\";\n      onPause();\n    };\n    if (this.polling || !this.writable) {\n      let total = 0;\n      if (this.polling) {\n        total++;\n        this.once(\"pollComplete\", function () {\n          --total || pause();\n        });\n      }\n      if (!this.writable) {\n        total++;\n        this.once(\"drain\", function () {\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n  /**\n   * Starts polling cycle.\n   *\n   * @private\n   */\n  poll() {\n    this.polling = true;\n    this.doPoll();\n    this.emitReserved(\"poll\");\n  }\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @protected\n   */\n  onData(data) {\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose({\n          description: \"transport closed by the server\"\n        });\n        return false;\n      }\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n    // decode payload\n    decodePayload(data, this.socket.binaryType).forEach(callback);\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emitReserved(\"pollComplete\");\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {}\n    }\n  }\n  /**\n   * For polling, send a close packet.\n   *\n   * @protected\n   */\n  doClose() {\n    const close = () => {\n      this.write([{\n        type: \"close\"\n      }]);\n    };\n    if (\"open\" === this.readyState) {\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      this.once(\"open\", close);\n    }\n  }\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} packets - data packets\n   * @protected\n   */\n  write(packets) {\n    this.writable = false;\n    encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emitReserved(\"drain\");\n      });\n    });\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    const query = this.query || {};\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @private\n   */\n  request(opts = {}) {\n    Object.assign(opts, {\n      xd: this.xd,\n      cookieJar: this.cookieJar\n    }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr post error\", xhrStatus, context);\n    });\n  }\n  /**\n   * Starts a poll cycle.\n   *\n   * @private\n   */\n  doPoll() {\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr poll error\", xhrStatus, context);\n    });\n    this.pollXhr = req;\n  }\n}\nexport let Request = /*#__PURE__*/(() => {\n  class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n      super();\n      installTimerFunctions(this, opts);\n      this.opts = opts;\n      this.method = opts.method || \"GET\";\n      this.uri = uri;\n      this.data = undefined !== opts.data ? opts.data : null;\n      this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n      var _a;\n      const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n      opts.xdomain = !!this.opts.xd;\n      const xhr = this.xhr = new XMLHttpRequest(opts);\n      try {\n        xhr.open(this.method, this.uri, true);\n        try {\n          if (this.opts.extraHeaders) {\n            xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n            for (let i in this.opts.extraHeaders) {\n              if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n              }\n            }\n          }\n        } catch (e) {}\n        if (\"POST\" === this.method) {\n          try {\n            xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n          } catch (e) {}\n        }\n        try {\n          xhr.setRequestHeader(\"Accept\", \"*/*\");\n        } catch (e) {}\n        (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n        // ie6 check\n        if (\"withCredentials\" in xhr) {\n          xhr.withCredentials = this.opts.withCredentials;\n        }\n        if (this.opts.requestTimeout) {\n          xhr.timeout = this.opts.requestTimeout;\n        }\n        xhr.onreadystatechange = () => {\n          var _a;\n          if (xhr.readyState === 3) {\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n          }\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            this.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            this.setTimeoutFn(() => {\n              this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n        xhr.send(this.data);\n      } catch (e) {\n        // Need to defer since .create() is called directly from the constructor\n        // and thus the 'error' event can only be only bound *after* this exception\n        // occurs.  Therefore, also, we cannot throw here at all.\n        this.setTimeoutFn(() => {\n          this.onError(e);\n        }, 0);\n        return;\n      }\n      if (typeof document !== \"undefined\") {\n        this.index = Request.requestsCount++;\n        Request.requests[this.index] = this;\n      }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n      this.emitReserved(\"error\", err, this.xhr);\n      this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n      if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n        return;\n      }\n      this.xhr.onreadystatechange = empty;\n      if (fromError) {\n        try {\n          this.xhr.abort();\n        } catch (e) {}\n      }\n      if (typeof document !== \"undefined\") {\n        delete Request.requests[this.index];\n      }\n      this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n      const data = this.xhr.responseText;\n      if (data !== null) {\n        this.emitReserved(\"data\", data);\n        this.emitReserved(\"success\");\n        this.cleanup();\n      }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n      this.cleanup();\n    }\n  }\n  Request.requestsCount = 0;\n  Request.requests = {};\n  /**\n   * Aborts pending requests when unloading the window. This is needed to prevent\n   * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n   * emitted.\n   */\n  return Request;\n})();\nif (typeof document !== \"undefined\") {\n  // @ts-ignore\n  if (typeof attachEvent === \"function\") {\n    // @ts-ignore\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}