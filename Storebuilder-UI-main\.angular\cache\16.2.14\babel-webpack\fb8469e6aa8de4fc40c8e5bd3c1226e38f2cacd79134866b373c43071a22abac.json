{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from \"@angular/forms\";\nimport { CountryISO } from \"ngx-intl-tel-input-gg\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { TenantRecords } from \"@core/interface\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"@angular-magic/ngx-gp-autocomplete\";\nimport * as i8 from \"@angular/google-maps\";\nconst _c0 = [\"placesRef\"];\nconst _c1 = [\"search\"];\nfunction MobileAddressMapComponent_map_marker_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"map-marker\", 12);\n    i0.ɵɵlistener(\"mapDrag\", function MobileAddressMapComponent_map_marker_14_Template_map_marker_mapDrag_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.markerDragEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marker_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"options\", ctx_r2.markerOptions)(\"position\", marker_r3.position);\n  }\n}\nexport let MobileAddressMapComponent = /*#__PURE__*/(() => {\n  class MobileAddressMapComponent {\n    ngZone;\n    addressService;\n    messageService;\n    router;\n    store;\n    route;\n    translate;\n    mainDataService;\n    loaderService;\n    _location;\n    cd;\n    appDataService;\n    platformId;\n    routeToCheckOut;\n    markerOptions = {\n      draggable: true,\n      icon: 'assets/images/map-pin.svg'\n    };\n    latitude;\n    longitude;\n    phoneLength = 13;\n    position;\n    landMarkAddressRequired = false;\n    isDisplaySuccessModal = false;\n    message = '';\n    addressDetailCity = '';\n    placesRef;\n    addressForm = new UntypedFormGroup({\n      addressLabel: new UntypedFormControl('Home'),\n      receiverFirstName: new UntypedFormControl(''),\n      receiverLastName: new UntypedFormControl(''),\n      streetAddress: new UntypedFormControl('', Validators.required),\n      country: new UntypedFormControl('', Validators.required),\n      city: new UntypedFormControl('', Validators.required),\n      state: new UntypedFormControl(''),\n      landMark: new UntypedFormControl('', Validators.required),\n      deliveryInstructions: new UntypedFormControl(''),\n      buldingNumber: new UntypedFormControl(''),\n      postcode: new UntypedFormControl(''),\n      receiverPhoneNumber: new UntypedFormControl('', Validators.required),\n      geo_location: new UntypedFormControl(''),\n      Lat: new UntypedFormControl(''),\n      Lng: new UntypedFormControl(''),\n      Id: new UntypedFormControl(''),\n      additionalAddress: new UntypedFormControl(''),\n      region: new UntypedFormGroup({\n        regionId: new UntypedFormControl('', Validators.required),\n        regionName: new UntypedFormControl('', Validators.required)\n      })\n    });\n    search = '';\n    Lat;\n    Lng;\n    zoom;\n    address;\n    myplaceHolder;\n    source;\n    isDefault = false;\n    navbarData;\n    center = {\n      lat: 0.3,\n      lng: 32.5\n    };\n    mapOptions = {\n      fullscreenControl: false,\n      disableDefaultUI: true,\n      componentRestrictions: {\n        country: 'UG'\n      }\n    };\n    allCities = [];\n    isDifferentCity = false;\n    addressLabelList = [{\n      'name': 'Home',\n      'id': 1\n    }, {\n      'name': 'Work',\n      'id': 2\n    }, {\n      'name': 'Other',\n      'id': 3\n    }];\n    selectedAddressType;\n    searchElementRef;\n    id = '';\n    routeSub;\n    geoCoder = new google.maps.Geocoder();\n    redirectUrl;\n    map;\n    borderBottomStyle = '2px solid red !important';\n    phoneInputLength = 12;\n    preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n    CustomCountryISO;\n    customPlaceHolder = '';\n    allRegionList = [];\n    filteredCities = [];\n    // Country coordinates mapping\n    countryCoordinates = {\n      'UG': {\n        lat: 0.3136,\n        lng: 32.5811,\n        country: 'UG'\n      },\n      'GH': {\n        lat: 5.595465,\n        lng: -0.242603,\n        country: 'GH'\n      },\n      'CI': {\n        lat: 7.5399,\n        lng: -5.5471,\n        country: 'CI'\n      } // Côte d'Ivoire\n    };\n\n    constructor(ngZone, addressService, messageService, router, store, route, translate, mainDataService, loaderService, _location, cd, appDataService, platformId) {\n      this.ngZone = ngZone;\n      this.addressService = addressService;\n      this.messageService = messageService;\n      this.router = router;\n      this.store = store;\n      this.route = route;\n      this.translate = translate;\n      this.mainDataService = mainDataService;\n      this.loaderService = loaderService;\n      this._location = _location;\n      this.cd = cd;\n      this.appDataService = appDataService;\n      this.platformId = platformId;\n      this.source = this.router.getCurrentNavigation()?.extras?.state;\n      let tenantId = localStorage.getItem('tenantId');\n      if (tenantId && tenantId !== '') {\n        if (tenantId == '1') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '2') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '3') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '4') {\n          this.customPlaceHolder = 'XXXXXXXXXX';\n        }\n      }\n      // Set initial map center based on isoCode\n      this.setMapCenterFromIsoCode();\n    }\n    /**\r\n      * Set map center and component restrictions based on isoCode from localStorage\r\n      */\n    setMapCenterFromIsoCode() {\n      const isoCode = localStorage.getItem('isoCode');\n      if (isoCode && this.countryCoordinates[isoCode]) {\n        const countryData = this.countryCoordinates[isoCode];\n        this.center = {\n          lat: countryData.lat,\n          lng: countryData.lng\n        };\n        // Update map options with the correct country restriction\n        this.mapOptions = {\n          ...this.mapOptions,\n          componentRestrictions: {\n            country: countryData.country\n          }\n        };\n      } else {\n        // Default to Uganda if isoCode is not found or not supported\n        const defaultCountry = this.countryCoordinates['UG'];\n        this.center = {\n          lat: defaultCountry.lat,\n          lng: defaultCountry.lng\n        };\n        this.mapOptions = {\n          ...this.mapOptions,\n          componentRestrictions: {\n            country: defaultCountry.country\n          }\n        };\n      }\n    }\n    onBack() {\n      this.routeToCheckOut ? this.router.navigateByUrl(\"/checkout/selectAddress\") : this.router.navigateByUrl(\"/account/address\");\n    }\n    ngOnInit() {\n      this.route.queryParamMap.subscribe(queryParams => {\n        this.routeToCheckOut = queryParams.get(\"checkout\");\n      });\n      this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n      this.route.queryParams.subscribe(params => {\n        this.redirectUrl = params.returnUrl;\n      });\n      this.routeSub = this.route.params.subscribe(params => {\n        this.getCities();\n        this.setCurrentLocation();\n        this.getAllRegion();\n      });\n      let phoneLength = localStorage.getItem('PhoneLength')?.toString();\n      let landMarkAddress = localStorage.getItem('customerAddressLandmarkRequired')?.toString();\n      if (landMarkAddress && landMarkAddress == 'True') {\n        this.landMarkAddressRequired = true;\n        this.addressForm.controls['landMark'].setValidators([Validators.required]);\n        this.addressForm.controls['landMark'].updateValueAndValidity();\n      }\n      if (phoneLength) {\n        this.phoneLength = parseInt(phoneLength) - 2;\n      }\n      let userDetails = this.store.get('profile');\n      this.mainDataService.setUserData(userDetails);\n      let name = userDetails?.name?.split(' ');\n      this.addressForm.patchValue({\n        receiverFirstName: name[0] ? name[0] : '',\n        receiverLastName: name[1] ? name[1] : ''\n        // receiverPhoneNumber: userDetails.mobileNumber,\n      });\n\n      if (!localStorage.getItem(\"isoCode\")) {\n        const tenants = this.appDataService.tenants;\n        if (tenants.records != undefined) {\n          let tenantId = localStorage.getItem('tenantId');\n          let data = tenants.records;\n          let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n          localStorage.setItem('isoCode', arr?.isoCode);\n          this.store.set('allCountryTenants', tenants.records);\n        }\n      } else {\n        this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n      }\n      if (this.appDataService.configuration) {\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n        if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n      }\n      this.filteredCities = this.allCities;\n    }\n    mapInitialize(map) {\n      this.map = map;\n    }\n    handleAddressChange(place) {\n      this.Lat = place.geometry.location.lat();\n      this.Lng = place.geometry.location.lng();\n      this.position = [{\n        position: {\n          lat: this.Lat,\n          lng: this.Lng\n        }\n      }];\n      this.zoom = 12;\n      this.center = this.position[0].position;\n      this.getAddress(this.Lat, this.Lng);\n    }\n    ngOnDestroy() {\n      this.routeSub.unsubscribe();\n    }\n    getAddress(latitude, longitude) {\n      this.geoCoder.geocode({\n        location: {\n          lat: latitude,\n          lng: longitude\n        }\n      }, (results, status) => {\n        if (status === 'OK') {\n          if (results[0]) {\n            const countryComponent = results[0].address_components.find(component => component.types.includes('country'));\n            console.log(countryComponent);\n            const isoCode = localStorage.getItem('isoCode');\n            if (countryComponent && countryComponent.short_name === isoCode) {\n              this.position = [{\n                position: {\n                  lat: latitude,\n                  lng: longitude\n                }\n              }];\n              this.center = this.position[0].position;\n              this.zoom = 12;\n              this.address = results[0].formatted_address;\n              if (results[0]?.address_components.length) {\n                const city = results[0].address_components.find(item => item.types.includes('locality'));\n                this.addressDetailCity = city.long_name;\n              }\n              this.addressForm.patchValue({\n                streetAddress: this.address,\n                country: results[results.length - 1].formatted_address\n                // city: results[results.length - 3].formatted_address,\n              });\n\n              this.validate();\n              this.getCoordinates();\n              this.cd.detectChanges();\n            } else {\n              this.setMapCenterFromIsoCode();\n              this.position = [{}];\n              this.cd.detectChanges();\n            }\n          } else {\n            if (isPlatformBrowser(this.platformId)) {\n              window.alert('No results found');\n            }\n          }\n        } else {\n          if (isPlatformBrowser(this.platformId)) {\n            window.alert('Geocoder failed due to: ' + status);\n          }\n        }\n      });\n    }\n    clear() {\n      this.searchElementRef.nativeElement.value = '';\n    }\n    onSubmit() {\n      this.addressForm.patchValue({\n        Lat: this.Lat ? this.Lat.toString() : '',\n        Lng: this.Lng ? this.Lng.toString() : ''\n        // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),\n      });\n\n      this.loaderService.show();\n      if (this.addressForm.value.postcode == '') this.addressForm.value.postcode = 0;\n      if (this.addressForm.valid) {\n        const formValue = {\n          ...this.addressForm.value,\n          region: this.addressForm.value.region.regionName,\n          receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n        };\n        if (formValue.postcode === \"\" || formValue.postcode === null) delete formValue.postcode;\n        this.addressService.addAddress(formValue).subscribe({\n          next: res => {\n            if (res?.success) {\n              this.loaderService.hide();\n              this.isDisplaySuccessModal = true;\n              this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully');\n            } else {\n              this.loaderService.hide();\n              this.messageService.add({\n                severity: 'error',\n                summary: res?.message\n              });\n            }\n          },\n          error: err => {\n            this.loaderService.hide();\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.fetchError'),\n              detail: err\n            });\n          }\n        });\n      }\n    }\n    onConfrim() {\n      this.isDisplaySuccessModal = false;\n      if (this.redirectUrl && this.redirectUrl !== '') {\n        this.router.navigate([this.redirectUrl]);\n      } else {\n        this._location.back();\n      }\n    }\n    Update() {\n      if (this.Lat.toString() === \"\" || this.Lng.toString() === \"\" || this.addressForm.controls['streetAddress'].value === \"\") {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.addressIsRequired')\n        });\n        return;\n      }\n      this.addressForm.patchValue({\n        Lat: this.Lat.toString(),\n        Lng: this.Lng.toString(),\n        Id: this.addressService.chosenAddress.id\n      });\n      this.loaderService.show();\n      if (this.addressForm.valid) {\n        const formValue = {\n          ...this.addressForm.value,\n          region: this.addressForm.value.region.regionName,\n          receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)\n        };\n        if (formValue.postcode === \"\" || formValue.postcode === null) delete formValue.postcode;\n        this.addressService.updateAddress(formValue).subscribe({\n          next: res => {\n            this.loaderService.hide();\n            if (!res.success) {\n              this.messageService.add({\n                severity: 'error',\n                summary: this.translate.instant('ResponseMessages.address'),\n                detail: this.translate.instant(res.message)\n              });\n            } else {\n              this.isDisplaySuccessModal = true;\n              this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');\n            }\n          },\n          error: err => {\n            this.loaderService.hide();\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.fetchError'),\n              detail: err\n            });\n          }\n        });\n      }\n    }\n    OnlyNumeric(val) {\n      if (!Number(val.value)) {\n        this.addressForm.value.postcode = '';\n        this.addressForm.value.phone = '';\n      }\n      return false;\n    }\n    checkPlaceHolder() {\n      if (this.myplaceHolder) {\n        this.myplaceHolder = '';\n      } else {\n        this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();\n        if (this.myplaceHolder) this.myplaceHolder = this.myplaceHolder + ' 000 000 000';else this.myplaceHolder = '256 000 000 000';\n      }\n    }\n    validate() {\n      if (!this.addressForm.valid) return true;\n    }\n    setAsDefault() {\n      this.addressService.setDefault(this.id).subscribe({\n        next: res => {\n          this.messageService.add({\n            severity: 'success',\n            summary: this.translate.instant('ResponseMessages.address'),\n            detail: this.translate.instant('ResponseMessages.defaultAddressSuccessfully')\n          });\n          if (this.redirectUrl && this.redirectUrl !== '') {\n            this.router.navigate([this.redirectUrl]);\n          } else {\n            this.router.navigate(['/account/address']);\n          }\n        },\n        error: err => {\n          this.loaderService.hide();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: err\n          });\n        }\n      });\n    }\n    setCurrentLocation() {\n      this.addressService.chosenAddress = null;\n      if ('geolocation' in navigator) {\n        navigator.geolocation.getCurrentPosition(position => {\n          this.Lat = position.coords.latitude;\n          this.Lng = position.coords.longitude;\n          this.position = [{\n            position: {\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          }];\n          this.center = this.position[0].position;\n          this.zoom = 12;\n          this.getAddress(this.Lat, this.Lng);\n          this.createLocationButton();\n        });\n      }\n    }\n    mapClicked(event) {\n      let latLng = JSON.parse(JSON.stringify(event.latLng));\n      this.Lat = latLng.lat;\n      this.Lng = latLng.lng;\n      this.position = [{\n        position: {\n          lat: this.Lat,\n          lng: this.Lng\n        }\n      }];\n      this.center = this.position[0].position;\n      this.zoom = 12;\n      this.getAddress(this.Lat, this.Lng);\n    }\n    createLocationButton() {\n      if (isPlatformBrowser(this.platformId)) {\n        const controlDiv = document.createElement('div');\n        controlDiv.index = 100;\n        this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);\n      }\n    }\n    markerDragEnd(event) {\n      if (event.latLng != null) {\n        const latLng = event.latLng.toJSON();\n        ;\n        this.getAddress(latLng.lat, latLng.lng);\n      }\n    }\n    getCities() {\n      const reqObj = {\n        currentPage: 1,\n        pageSize: 5,\n        ignorePagination: true\n      };\n      this.addressService.getAllCities(reqObj).subscribe(res => {\n        if (res.success) {\n          this.allCities = res.data.records;\n          this.allCities.unshift({\n            \"id\": -1,\n            \"cityName\": null,\n            \"regionId\": -1,\n            \"isActive\": true\n          });\n        }\n      });\n    }\n    getCoordinates() {\n      var geocoder = new google.maps.Geocoder();\n      this.isDifferentCity = true;\n      geocoder.geocode({\n        'address': this.addressForm.controls['streetAddress'].value\n      }, (results, status) => {\n        if (status == google.maps.GeocoderStatus.OK) {\n          if (results[0].address_components.length) {\n            const city = results[0].address_components.find(item => item.types.includes('locality'));\n            if (city?.long_name === this.addressDetailCity) {\n              this.isDifferentCity = false;\n              this.cd.detectChanges();\n            }\n          }\n        }\n      });\n    }\n    getAllRegion() {\n      let reqObj = {\n        currentPage: 1,\n        pageSize: 5,\n        ignorePagination: true\n      };\n      this.addressService.getAllRegions(reqObj).subscribe(res => {\n        if (res.success) {\n          this.allRegionList = res.data.records;\n          this.allRegionList.unshift({\n            \"id\": -1,\n            \"regionName\": null,\n            \"regionId\": -1,\n            \"isActive\": true\n          });\n        }\n      });\n    }\n    filterCitiesByRegion(regionId) {\n      const selectedRegion = this.allRegionList.find(region => region.id === regionId);\n      this.addressForm.patchValue({\n        region: {\n          regionId: selectedRegion.id,\n          regionName: selectedRegion.regionName\n        }\n      });\n      this.filteredCities = this.allCities.filter(city => city.regionId === regionId);\n    }\n    addMoreAddress() {\n      if (this.routeToCheckOut) {\n        if (this.redirectUrl && this.redirectUrl !== '') {\n          this.router.navigate(['/account/address/add-address'], {\n            state: {\n              returnUrl: this.redirectUrl,\n              lat: this.Lat,\n              lng: this.Lng\n            },\n            queryParams: {\n              checkout: 'checkout'\n            }\n          });\n          this.redirectUrl = '';\n        } else {\n          this.router.navigate(['/account/address/add-address'], {\n            state: {\n              returnUrl: this.redirectUrl,\n              lat: this.Lat,\n              lng: this.Lng\n            },\n            queryParams: {\n              checkout: 'checkout'\n            }\n          });\n        }\n      } else {\n        if (this.redirectUrl && this.redirectUrl !== '') {\n          this.router.navigate(['/account/address/add-address'], {\n            state: {\n              returnUrl: this.redirectUrl,\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          });\n          this.redirectUrl = '';\n        } else {\n          this.router.navigate(['/account/address/add-address'], {\n            state: {\n              returnUrl: this.redirectUrl,\n              lat: this.Lat,\n              lng: this.Lng\n            }\n          });\n        }\n      }\n    }\n    static ɵfac = function MobileAddressMapComponent_Factory(t) {\n      return new (t || MobileAddressMapComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MobileAddressMapComponent,\n      selectors: [[\"app-mobile-address-map\"]],\n      viewQuery: function MobileAddressMapComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.placesRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchElementRef = _t.first);\n        }\n      },\n      decls: 18,\n      vars: 14,\n      consts: [[1, \"map\"], [1, \"map-header-container\"], [\"src\", \"assets/icons/mobile-icons/back-icon.svg\", \"alt\", \"back-icon\", 3, \"click\"], [1, \"map-header-detail\"], [1, \"col-12\", \"search-bar\"], [1, \"p-inputgroup\", \"search-group\", \"search-top\"], [\"ngx-gp-autocomplete\", \"\", 1, \"map-search\", \"search-input\", 3, \"options\", \"placeholder\", \"onAddressChange\"], [\"placesRef\", \"ngx-places\", \"search\", \"\"], [\"height\", \"700px\", \"width\", \"100%\", 3, \"center\", \"zoom\", \"options\", \"mapClick\", \"mapInitialized\"], [3, \"options\", \"position\", \"mapDrag\", 4, \"ngFor\", \"ngForOf\"], [1, \"confirm-address\", \"col-12\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"confirm-address-Btn\", 3, \"label\", \"click\"], [3, \"options\", \"position\", \"mapDrag\"]],\n      template: function MobileAddressMapComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"img\", 2);\n          i0.ɵɵlistener(\"click\", function MobileAddressMapComponent_Template_img_click_2_listener() {\n            return ctx.onBack();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵpipe(5, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\")(7, \"div\", 4)(8, \"div\", 5)(9, \"input\", 6, 7);\n          i0.ɵɵlistener(\"onAddressChange\", function MobileAddressMapComponent_Template_input_onAddressChange_9_listener($event) {\n            return ctx.handleAddressChange($event);\n          });\n          i0.ɵɵpipe(12, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"google-map\", 8);\n          i0.ɵɵlistener(\"mapClick\", function MobileAddressMapComponent_Template_google_map_mapClick_13_listener($event) {\n            return ctx.mapClicked($event);\n          })(\"mapInitialized\", function MobileAddressMapComponent_Template_google_map_mapInitialized_13_listener($event) {\n            return ctx.mapInitialize($event);\n          });\n          i0.ɵɵtemplate(14, MobileAddressMapComponent_map_marker_14_Template, 1, 2, \"map-marker\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function MobileAddressMapComponent_Template_button_click_16_listener() {\n            return ctx.addMoreAddress();\n          });\n          i0.ɵɵpipe(17, \"translate\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 8, \"multipleAddress.selectAddress\"), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(12, 10, \"addingAddress.search\"));\n          i0.ɵɵproperty(\"options\", ctx.mapOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"center\", ctx.center)(\"zoom\", ctx.zoom)(\"options\", ctx.mapOptions);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.position);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(17, 12, \"multipleAddress.confirmAddress\"));\n        }\n      },\n      dependencies: [i5.NgForOf, i6.ButtonDirective, i7.NgxGpAutocompleteDirective, i8.GoogleMap, i8.MapMarker, i4.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.map[_ngcontent-%COMP%]{margin-top:74px}.search-bar[_ngcontent-%COMP%]{position:absolute;z-index:3;width:100%}.search-top[_ngcontent-%COMP%]{top:30px;position:relative;box-shadow:-2px 0 5px #33333330}.search-input[_ngcontent-%COMP%]{width:100%;padding:16px;border-radius:8px}.search-input[_ngcontent-%COMP%]::placeholder{color:#1a445e!important}.map-header-container[_ngcontent-%COMP%]{background:#F6F6F6;padding:16px}.map-header-detail[_ngcontent-%COMP%]{color:#2d2d2d;font-size:16px;font-weight:500;font-family:var(--medium-font)}.confirm-address-Btn[_ngcontent-%COMP%]{bottom:110px;color:#fff;background-color:var(--header_bgcolor);padding:16px;border:1px solid var(--header_bgcolor);font-family:var(--medium-font);outline:0 none}@media only screen and (max-width: 767px){.confirm-address-Btn[_ngcontent-%COMP%]{position:fixed;bottom:69px;left:0}}@media only screen and (min-width: 768px) and (max-width: 1200px){.confirm-address-Btn[_ngcontent-%COMP%]{position:fixed;bottom:69px;left:0}}.confirm-address[_ngcontent-%COMP%]{position:relative}\"]\n    });\n  }\n  return MobileAddressMapComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}