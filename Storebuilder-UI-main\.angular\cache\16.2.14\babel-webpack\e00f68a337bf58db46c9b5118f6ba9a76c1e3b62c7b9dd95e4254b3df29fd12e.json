{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ngx-translate/core\";\nconst _c0 = function (a0) {\n  return {\n    width: a0\n  };\n};\nexport class CategoryCardComponent {\n  router;\n  category = {};\n  showAllMobile = false;\n  baseUrl = environment.apiEndPoint;\n  constructor(router) {\n    this.router = router;\n  }\n  ngOnInit() {\n    /**/\n  }\n  getImage(imageLink) {\n    if (imageLink) {\n      if (imageLink[0] === '/') imageLink = imageLink.substring(1);\n      const sub = imageLink.substring(0, imageLink.indexOf('/'));\n      if (sub.toLowerCase().includes('images')) {\n        return `${this.baseUrl}/${imageLink}`;\n      } else {\n        return `${this.baseUrl}/Images/${imageLink}`;\n      }\n    } else {\n      return '';\n    }\n  }\n  errorHandler(event) {\n    if (environment.isStoreCloud) {\n      event.target.src = \"assets/images/placeholder.png\";\n    } else {\n      event.target.src = \"assets/images/mtn-alt.png\";\n    }\n  }\n  categoriesClick(id) {\n    this.router.navigate(['category', id], {\n      queryParams: {\n        tenantId: localStorage.getItem(\"tenantId\"),\n        lang: localStorage.getItem(\"lang\")\n      },\n      queryParamsHandling: 'merge'\n    });\n  }\n  static ɵfac = function CategoryCardComponent_Factory(t) {\n    return new (t || CategoryCardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CategoryCardComponent,\n    selectors: [[\"app-mtn-category-card\"]],\n    inputs: {\n      category: \"category\",\n      showAllMobile: \"showAllMobile\"\n    },\n    decls: 7,\n    vars: 11,\n    consts: [[1, \"category-card\", \"shadow-1\", \"cursor-pointer\", \"no-underline\", 3, \"ngStyle\", \"click\"], [3, \"alt\", \"src\", \"error\"], [1, \"title\", \"mt-3\", 3, \"title\"], [1, \"total-items\", \"mt-2\"]],\n    template: function CategoryCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"a\", 0);\n        i0.ɵɵlistener(\"click\", function CategoryCardComponent_Template_a_click_0_listener() {\n          return ctx.categoriesClick(ctx.category.id);\n        });\n        i0.ɵɵelementStart(1, \"img\", 1);\n        i0.ɵɵlistener(\"error\", function CategoryCardComponent_Template_img_error_1_listener($event) {\n          return ctx.errorHandler($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(9, _c0, ctx.showAllMobile === true ? \"105px\" : \"\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"alt\", ctx.category.categoryName)(\"src\", ctx.getImage(ctx.category.image), i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(1);\n        i0.ɵɵpropertyInterpolate(\"title\", ctx.category.categoryName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", ctx.category.categoryName, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate2(\" \", ctx.category.totalProductCount, \" \", i0.ɵɵpipeBind1(6, 7, \"categoryCard.items\"), \" \");\n      }\n    },\n    dependencies: [i2.NgStyle, i3.TranslatePipe],\n    styles: [\".category-card[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 160px;\\n  -webkit-box-shadow: 6px 0 4px -4px rgba(219, 213, 213, 0.631372549), -6px 0 4px -4px rgba(219, 213, 213, 0.631372549) !important;\\n  border-radius: 15px;\\n  opacity: 1;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  max-height: 200px;\\n  background-color: #ffffff;\\n}\\n@media screen and (max-width: 768px) {\\n  .category-card[_ngcontent-%COMP%] {\\n    width: 82px;\\n    height: 109px;\\n    filter: drop-shadow(0px 0px 5px rgba(176, 64, 108, 0.16));\\n    margin-top: 2px;\\n  }\\n}\\n.category-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  object-fit: contain;\\n  padding-top: 12px;\\n}\\n@media screen and (max-width: 768px) {\\n  .category-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n}\\n.category-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-family: var(--medium-font);\\n  color: black;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  line-height: 16px;\\n  word-break: break-word;\\n  height: 30px;\\n}\\n.category-card[_ngcontent-%COMP%]   .total-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  flex-wrap: wrap;\\n  font-size: 14px;\\n  color: var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 10px !important;\\n    font-weight: 500;\\n    font-family: var(--medium-font);\\n    color: black !important;\\n    text-align: center !important;\\n    width: 69px !important;\\n    display: -webkit-box !important;\\n    -webkit-line-clamp: 2 !important;\\n    margin-top: 7px !important;\\n  }\\n  .total-items[_ngcontent-%COMP%] {\\n    font-size: 8px !important;\\n    color: var(--header_bgcolor);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["environment", "CategoryCardComponent", "router", "category", "showAllMobile", "baseUrl", "apiEndPoint", "constructor", "ngOnInit", "getImage", "imageLink", "substring", "sub", "indexOf", "toLowerCase", "includes", "<PERSON><PERSON><PERSON><PERSON>", "event", "isStoreCloud", "target", "src", "categoriesClick", "id", "navigate", "queryParams", "tenantId", "localStorage", "getItem", "lang", "queryParamsHandling", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "decls", "vars", "consts", "template", "CategoryCardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "CategoryCardComponent_Template_a_click_0_listener", "CategoryCardComponent_Template_img_error_1_listener", "$event", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵadvance", "categoryName", "image", "ɵɵsanitizeUrl", "ɵɵpropertyInterpolate", "ɵɵtextInterpolate1", "ɵɵtextInterpolate2", "totalProductCount", "ɵɵpipeBind1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\category-card\\category-card.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\category-card\\category-card.component.html"], "sourcesContent": ["import {Component, Input, OnInit} from '@angular/core';\r\nimport {environment} from 'src/environments/environment';\r\nimport {Router} from \"@angular/router\";\r\nimport {Category} from '@core/interface';\r\n\r\n@Component({\r\n  selector: 'app-mtn-category-card',\r\n  templateUrl: './category-card.component.html',\r\n  styleUrls: ['./category-card.component.scss'],\r\n})\r\nexport class CategoryCardComponent implements OnInit {\r\n  @Input() category: Category = {} as Category;\r\n  @Input() showAllMobile: boolean = false;\r\n  baseUrl = environment.apiEndPoint;\r\n\r\n  constructor(private router: Router) {\r\n\r\n\r\n  }\r\n\r\n\r\n  ngOnInit(): void {\r\n    /**/\r\n  }\r\n\r\n  getImage(imageLink: any) {\r\n\r\n    if (imageLink) {\r\n      if (imageLink[0] === '/') imageLink = imageLink.substring(1);\r\n      const sub = imageLink.substring(0, imageLink.indexOf('/'));\r\n      if (sub.toLowerCase().includes('images')) {\r\n\r\n        return `${this.baseUrl}/${imageLink}`;\r\n      } else {\r\n\r\n        return `${this.baseUrl}/Images/${imageLink}`;\r\n      }\r\n    } else {\r\n      return '';\r\n    }\r\n  }\r\n\r\n  errorHandler(event: any) {\r\n    if (environment.isStoreCloud) {\r\n      event.target.src = \"assets/images/placeholder.png\";\r\n\r\n    } else {\r\n      event.target.src = \"assets/images/mtn-alt.png\";\r\n\r\n    }\r\n  }\r\n  categoriesClick(id:any){\r\n    this.router.navigate(['category', id],{\r\n      queryParams: {\r\n        tenantId: localStorage.getItem(\"tenantId\"),\r\n        lang:localStorage.getItem(\"lang\")\r\n      },\r\n      queryParamsHandling: 'merge',\r\n    })\r\n  }\r\n}\r\n", "<a\r\n  class=\"category-card shadow-1 cursor-pointer no-underline\"\r\n  [ngStyle]=\"{\r\n    width: showAllMobile === true ? '105px' : ''\r\n  }\"\r\n  (click)=\"categoriesClick(category.id)\"\r\n>\r\n  <img\r\n    [alt]=\"category.categoryName\"\r\n    [src]=\"getImage(category.image)\"\r\n    (error)=\"errorHandler($event)\"\r\n  />\r\n  <div class=\"title mt-3\" title=\"{{ category.categoryName }}\">\r\n    {{ category.categoryName }}\r\n  </div>\r\n  <div class=\"total-items mt-2\">\r\n    {{ category.totalProductCount }} {{ \"categoryCard.items\" | translate }}\r\n  </div>\r\n</a>\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,8BAA8B;;;;;;;;;;AASxD,OAAM,MAAOC,qBAAqB;EAKZC,MAAA;EAJXC,QAAQ,GAAa,EAAc;EACnCC,aAAa,GAAY,KAAK;EACvCC,OAAO,GAAGL,WAAW,CAACM,WAAW;EAEjCC,YAAoBL,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAG1B;EAGAM,QAAQA,CAAA;IACN;EAAA;EAGFC,QAAQA,CAACC,SAAc;IAErB,IAAIA,SAAS,EAAE;MACb,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,SAAS,GAAGA,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC;MAC5D,MAAMC,GAAG,GAAGF,SAAS,CAACC,SAAS,CAAC,CAAC,EAAED,SAAS,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;MAC1D,IAAID,GAAG,CAACE,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAExC,OAAO,GAAG,IAAI,CAACV,OAAO,IAAIK,SAAS,EAAE;OACtC,MAAM;QAEL,OAAO,GAAG,IAAI,CAACL,OAAO,WAAWK,SAAS,EAAE;;KAE/C,MAAM;MACL,OAAO,EAAE;;EAEb;EAEAM,YAAYA,CAACC,KAAU;IACrB,IAAIjB,WAAW,CAACkB,YAAY,EAAE;MAC5BD,KAAK,CAACE,MAAM,CAACC,GAAG,GAAG,+BAA+B;KAEnD,MAAM;MACLH,KAAK,CAACE,MAAM,CAACC,GAAG,GAAG,2BAA2B;;EAGlD;EACAC,eAAeA,CAACC,EAAM;IACpB,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,UAAU,EAAED,EAAE,CAAC,EAAC;MACpCE,WAAW,EAAE;QACXC,QAAQ,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAC1CC,IAAI,EAACF,YAAY,CAACC,OAAO,CAAC,MAAM;OACjC;MACDE,mBAAmB,EAAE;KACtB,CAAC;EACJ;;qBAjDW5B,qBAAqB,EAAA6B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;EAAA;;UAArBhC,qBAAqB;IAAAiC,SAAA;IAAAC,MAAA;MAAAhC,QAAA;MAAAC,aAAA;IAAA;IAAAgC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVlCX,EAAA,CAAAa,cAAA,WAMC;QADCb,EAAA,CAAAc,UAAA,mBAAAC,kDAAA;UAAA,OAASH,GAAA,CAAArB,eAAA,CAAAqB,GAAA,CAAAvC,QAAA,CAAAmB,EAAA,CAA4B;QAAA,EAAC;QAEtCQ,EAAA,CAAAa,cAAA,aAIE;QADAb,EAAA,CAAAc,UAAA,mBAAAE,oDAAAC,MAAA;UAAA,OAASL,GAAA,CAAA1B,YAAA,CAAA+B,MAAA,CAAoB;QAAA,EAAC;QAHhCjB,EAAA,CAAAkB,YAAA,EAIE;QACFlB,EAAA,CAAAa,cAAA,aAA4D;QAC1Db,EAAA,CAAAmB,MAAA,GACF;QAAAnB,EAAA,CAAAkB,YAAA,EAAM;QACNlB,EAAA,CAAAa,cAAA,aAA8B;QAC5Bb,EAAA,CAAAmB,MAAA,GACF;;QAAAnB,EAAA,CAAAkB,YAAA,EAAM;;;QAfNlB,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAV,GAAA,CAAAtC,aAAA,0BAEE;QAIA0B,EAAA,CAAAuB,SAAA,GAA6B;QAA7BvB,EAAA,CAAAoB,UAAA,QAAAR,GAAA,CAAAvC,QAAA,CAAAmD,YAAA,CAA6B,QAAAZ,GAAA,CAAAjC,QAAA,CAAAiC,GAAA,CAAAvC,QAAA,CAAAoD,KAAA,GAAAzB,EAAA,CAAA0B,aAAA;QAIP1B,EAAA,CAAAuB,SAAA,GAAmC;QAAnCvB,EAAA,CAAA2B,qBAAA,UAAAf,GAAA,CAAAvC,QAAA,CAAAmD,YAAA,CAAmC;QACzDxB,EAAA,CAAAuB,SAAA,GACF;QADEvB,EAAA,CAAA4B,kBAAA,MAAAhB,GAAA,CAAAvC,QAAA,CAAAmD,YAAA,MACF;QAEExB,EAAA,CAAAuB,SAAA,GACF;QADEvB,EAAA,CAAA6B,kBAAA,MAAAjB,GAAA,CAAAvC,QAAA,CAAAyD,iBAAA,OAAA9B,EAAA,CAAA+B,WAAA,kCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}