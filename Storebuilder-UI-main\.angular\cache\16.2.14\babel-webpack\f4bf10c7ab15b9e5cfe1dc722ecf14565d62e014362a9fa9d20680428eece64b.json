{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { timer } from \"rxjs\";\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ButtonModule } from 'primeng/button';\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/button\";\nfunction ResetPasswordOtpComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ResetPasswordOtpComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resendOtp());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.timeLeft !== \"00\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resend\"), \" \");\n  }\n}\nfunction ResetPasswordOtpComponent_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.otp.resendIn\"), \" 0:\", ctx_r1.timeLeft, \"\");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nexport class ResetPasswordOtpComponent {\n  messageService;\n  auth;\n  mainDataService;\n  cd;\n  permissionService;\n  router;\n  otpService;\n  translate;\n  store;\n  user;\n  $gaService;\n  mobileNumber = \"\";\n  size = 50;\n  value1;\n  value2;\n  value3;\n  value4;\n  otpCode = \"\";\n  countDown;\n  counter = 60;\n  timeLeft = 60;\n  tick = 1000;\n  interval;\n  isMobileLayout = false;\n  isGoogleAnalytics = false;\n  constructor(messageService, auth, mainDataService, cd, permissionService, router, otpService, translate, store, user, $gaService) {\n    this.messageService = messageService;\n    this.auth = auth;\n    this.mainDataService = mainDataService;\n    this.cd = cd;\n    this.permissionService = permissionService;\n    this.router = router;\n    this.otpService = otpService;\n    this.translate = translate;\n    this.store = store;\n    this.user = user;\n    this.$gaService = $gaService;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.countDown = timer(0, this.tick).subscribe(() => --this.counter);\n    this.startTimer();\n  }\n  ngOnDestroy() {\n    if (this.countDown) {\n      this.countDown.unsubscribe();\n    }\n  }\n  onDigitInput(event) {\n    let element;\n    if (event.code !== 'Backspace' && event.code !== 'Tab') {\n      element = event.srcElement.nextElementSibling;\n    }\n    if (event.code === 'Backspace') {\n      element = event.srcElement.previousElementSibling;\n    }\n    if (element == null) {\n      return;\n    } else {\n      element.focus();\n    }\n  }\n  validateOtp() {\n    this.store.set(\"loading\", true);\n    if (this.value1 && this.value2 && this.value3 && this.value4) {\n      this.otpCode = this.value1.toString() + this.value2.toString() + this.value3.toString() + this.value4.toString();\n      if (history?.state?.password) {\n        this.login();\n      } else {\n        this.otpService.VerifyForgotPassword({\n          mobileNumber: this.store.get(\"userPhone\"),\n          otpCode: this.otpCode,\n          RequestId: this.store.get(\"verificationCode\")\n        }).subscribe({\n          next: res => {\n            this.store.set(\"loading\", false);\n            if (!res.success) {\n              this.messageService.add({\n                severity: 'error',\n                summary: res.message\n              });\n            } else {\n              if (this.isGoogleAnalytics) {\n                this.$gaService.event(GaLocalActionEnum.CLICK_ON_OTP_VERIFY, '', 'OTP_VERIFICATION', 1, true);\n              }\n              this.router.navigateByUrl('/update-password', {\n                state: {\n                  mobile: this.mobileNumber,\n                  otp: this.otpCode\n                }\n              });\n            }\n          },\n          error: err => {\n            this.store.set(\"loading\", false);\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.fetchError'),\n              detail: err.message\n            });\n          }\n        });\n      }\n    } else {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\n      });\n    }\n  }\n  login() {\n    this.store.set(\"loading\", true);\n    this.auth.login({\n      username: history.state.Username,\n      password: history.state.password,\n      RequestId: history.state.verificationCode,\n      otp: this.otpCode\n    }).subscribe({\n      next: res => {\n        if (res?.success) {\n          this.mainDataService.setUserData(res.data);\n          this.store.set('profile', res.data);\n          this.store.set('userPhone', res.data.mobileNumber);\n          this.store.set('timeInterval', new Date().getTime());\n          this.store.set('refreshToken', res.data.refreshToken.replace('bearer', ''));\n          this.store.set(\"loading\", false);\n          if (res.data.isPasswodExpired) {\n            this.router.navigateByUrl('/change-password');\n            this.messageService.add({\n              severity: 'info',\n              summary: this.translate.instant('ResponseMessages.changePassword'),\n              detail: this.translate.instant('ResponseMessages.passwordExpirationChange')\n            });\n          } else {\n            this.router.navigate(['/']);\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('ResponseMessages.login'),\n              detail: this.translate.instant('ResponseMessages.loggedInSuccessfully')\n            });\n          }\n        } else {\n          this.store.set('profile', '');\n          this.store.set(\"loading\", false);\n          this.messageService.add({\n            severity: 'error',\n            summary: res?.Message ?? this.translate.instant('ErrorMessages.invalidUserNameOrPassword')\n          });\n        }\n      },\n      error: err => {\n        this.store.set('profile', '');\n        this.store.set(\"loading\", false);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  resendOtp() {\n    this.store.set(\"loading\", true);\n    this.user.ForgotPassword({\n      userName: this.user.username.replace('-', '')\n    }).subscribe({\n      next: res => {\n        this.startTimer();\n        this.store.set(\"loading\", false);\n        this.store.set(\"verificationCode\", res.data.requestId);\n      },\n      error: err => {\n        this.store.set(\"loading\", false);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  startTimer() {\n    this.interval = setInterval(() => {\n      if (this.timeLeft > 0) {\n        this.timeLeft--;\n      } else {\n        this.timeLeft = 60;\n      }\n      if (this.timeLeft === 0) {\n        clearInterval(this.interval);\n      }\n      if (this.timeLeft < 10) {\n        this.timeLeft = '0' + this.timeLeft;\n      }\n      this.cd.detectChanges();\n    }, 1000);\n  }\n  static ɵfac = function ResetPasswordOtpComponent_Factory(t) {\n    return new (t || ResetPasswordOtpComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i2.RegisterService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResetPasswordOtpComponent,\n    selectors: [[\"app-reset-password-otp\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 23,\n    vars: 27,\n    consts: [[1, \"otp\"], [1, \"content-container\", 3, \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-100\"], [1, \"col-12\", \"m-0\", \"py-0\", \"text-center\", \"otp-heading\"], [\"href\", \"/\", 1, \"col-12\", \"main-color\", \"mb-3\", \"text-center\", \"sent-otp\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"shadow-signin\", \"bg-white\", \"pt-6\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\", \"mb-8\"], [\"action\", \"\", 1, \"mt-5\"], [\"type\", \"text\", \"maxlength\", \"1\", \"oninput\", \"this.value=this.value.replace(/[^0-9]/g,'');\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"keyup\"], [1, \"count\"], [\"style\", \"background: white; border: none; cursor: pointer\", \"class\", \"resend\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"time-left\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"pButton\", \"\", \"type\", \"button\", 1, \"mb-5\", \"mt-3\", \"p-button\", \"p-component\", \"p-element\", \"second-btn\", \"width-100\", 3, \"label\", \"disabled\", \"click\"], [1, \"resend\", 2, \"background\", \"white\", \"border\", \"none\", \"cursor\", \"pointer\", 3, \"disabled\", \"click\"], [1, \"time-left\"]],\n    template: function ResetPasswordOtpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelementContainerStart(1);\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"p\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 4);\n        i0.ɵɵtext(8);\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"div\", 7)(13, \"form\", 8)(14, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function ResetPasswordOtpComponent_Template_input_ngModelChange_14_listener($event) {\n          return ctx.value1 = $event;\n        })(\"keyup\", function ResetPasswordOtpComponent_Template_input_keyup_14_listener($event) {\n          return ctx.onDigitInput($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function ResetPasswordOtpComponent_Template_input_ngModelChange_15_listener($event) {\n          return ctx.value2 = $event;\n        })(\"keyup\", function ResetPasswordOtpComponent_Template_input_keyup_15_listener($event) {\n          return ctx.onDigitInput($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function ResetPasswordOtpComponent_Template_input_ngModelChange_16_listener($event) {\n          return ctx.value3 = $event;\n        })(\"keyup\", function ResetPasswordOtpComponent_Template_input_keyup_16_listener($event) {\n          return ctx.onDigitInput($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function ResetPasswordOtpComponent_Template_input_ngModelChange_17_listener($event) {\n          return ctx.value4 = $event;\n        })(\"keyup\", function ResetPasswordOtpComponent_Template_input_keyup_17_listener($event) {\n          return ctx.onDigitInput($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(18, \"div\", 10);\n        i0.ɵɵtemplate(19, ResetPasswordOtpComponent_button_19_Template, 3, 4, \"button\", 11);\n        i0.ɵɵtemplate(20, ResetPasswordOtpComponent_span_20_Template, 3, 4, \"span\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function ResetPasswordOtpComponent_Template_button_click_21_listener() {\n          return ctx.validateOtp();\n        });\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c0, ctx.isMobileLayout ? \"1rem\" : \"220px\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 15, \"otp.enterOTP\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 17, \"otp.verificationCodeSent\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngModel\", ctx.value1)(\"ngModelOptions\", i0.ɵɵpureFunction0(23, _c1));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.value2)(\"ngModelOptions\", i0.ɵɵpureFunction0(24, _c1));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.value3)(\"ngModelOptions\", i0.ɵɵpureFunction0(25, _c1));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.value4)(\"ngModelOptions\", i0.ɵɵpureFunction0(26, _c1));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.timeLeft === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.timeLeft > 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(22, 19, \"auth.otp.next\"))(\"disabled\", !(ctx.value1 && ctx.value2 && ctx.value3 && ctx.value4));\n      }\n    },\n    dependencies: [CommonModule, i6.NgIf, i6.NgStyle, FormsModule, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.MaxLengthValidator, i7.NgModel, i7.NgForm, TranslateModule, i4.TranslatePipe, ButtonModule, i8.ButtonDirective],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\nform[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\nform[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 60px;\\n  height: 60px;\\n  text-align: center;\\n  margin: 0 0.8rem;\\n  background: #F5F5F5 !important;\\n  border-bottom: 1px solid #AEAEAE !important;\\n  font-size: 20px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.count[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: small;\\n}\\n\\n.shadow-signin[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n}\\n\\n.otp-heading[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.sent-otp[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #A3A3A3;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  text-transform: uppercase;\\n}\\n\\n.resend[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 800;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .otp[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n  form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n    margin: 0 0.4rem !important;\\n  }\\n}\\n.time-left[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-size: 13px;\\n  font-weight: 400;\\n  color: #A3A3A3;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .margin-x-100[_ngcontent-%COMP%] {\\n    margin-top: 250px !important;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}