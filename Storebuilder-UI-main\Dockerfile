ARG NODE_VERSION=18.20.5

FROM node:${NODE_VERSION}-alpine3.20 AS build

WORKDIR /app

COPY ./Storebuilder-UI-main/package*.json ./

RUN npm config set audit false \
    && npm config set fund false \
    && npm install -g @angular/cli \
    && npm i --legacy-peer-deps

COPY ./Storebuilder-UI-main/src ./src
COPY ./Storebuilder-UI-main/angular.json ./
COPY ./Storebuilder-UI-main/karma.conf.js ./
COPY ./Storebuilder-UI-main/tsconfig*.json ./

RUN npx ng build --aot --configuration deploy-docker-uat

FROM nginx:1.27.3-alpine3.20 AS publish

RUN alpineArch="$(apk --print-arch)" && \
    wget "https://dl-cdn.alpinelinux.org/alpine/v3.20/main/$alpineArch/bash-5.2.26-r0.apk" && \
    apk add --allow-untrusted bash-5.2.26-r0.apk && \
    rm bash-5.2.26-r0.apk

ENV scripts_path=/usr/share/nginx/html/assets/js
# RUN mkdir -p $scripts_path

COPY ./Storebuilder-UI-main/nginx.default.conf /etc/nginx/conf.d/

COPY --from=build /app/dist/momo-market/browser /usr/share/nginx/html

WORKDIR /usr/share/nginx/html
COPY ./Storebuilder-UI-main/entrypoint.sh .
COPY ./Storebuilder-UI-main/.env .

RUN chmod +x /usr/share/nginx/html/entrypoint.sh && \
    chown -R root:root /usr/share/nginx/html/entrypoint.sh

EXPOSE 443

CMD ["/bin/bash", "-c", "/usr/share/nginx/html/entrypoint.sh && nginx -g \"daemon off;\""]