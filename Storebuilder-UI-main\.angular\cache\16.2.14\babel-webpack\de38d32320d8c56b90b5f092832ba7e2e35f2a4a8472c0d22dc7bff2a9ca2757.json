{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { HttpParams } from \"@angular/common/http\";\nimport { map } from \"rxjs\";\nimport { EndPointsConfig } from '@core/utilities';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CancelReason {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/CancelledReason`;\n  }\n  getAllCancelReason(id) {\n    let params = new HttpParams().set('UserType', id);\n    return this.http.get(`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.CancelledReason.Controller}/${EndPointsConfig.CancelledReason.EndPoints.GetAllCancelledReason}`, {\n      params: params\n    }).pipe(map(res => {\n      return {\n        reasons: res?.data?.records ? res?.data?.records : []\n      };\n    }));\n  }\n  static #_ = this.ɵfac = function CancelReason_Factory(t) {\n    return new (t || CancelReason)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CancelReason,\n    factory: CancelReason.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "HttpParams", "map", "EndPointsConfig", "CancelReason", "constructor", "http", "baseUrl", "apiEndPoint", "getAllCancelReason", "id", "params", "set", "get", "ApiUrl", "Url", "CancelledReason", "Controller", "EndPoints", "GetAllCancelledReason", "pipe", "res", "reasons", "data", "records", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\cancel-reason.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from '@environments/environment';\r\nimport {HttpClient, HttpParams} from \"@angular/common/http\";\r\nimport {map, Observable} from \"rxjs\";\r\nimport {GetAllCancelledReasonReq, PaginatedResult, ResponseModel} from '@core/interface';\r\nimport {EndPointsConfig} from '@core/utilities';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CancelReason {\r\n\r\n  baseUrl: string;\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}/CancelledReason`;\r\n\r\n  }\r\n\r\n  getAllCancelReason(id:number): Observable<{ reasons: GetAllCancelledReasonReq[]; }> {\r\n    let params = new HttpParams().set('UserType', id)\r\n\r\n    return this.http.\r\n    get<ResponseModel<PaginatedResult<GetAllCancelledReasonReq>>>\r\n    (`${EndPointsConfig.ApiUrl.Url}/${EndPointsConfig.CancelledReason.Controller}/${EndPointsConfig.CancelledReason.EndPoints.GetAllCancelledReason}`,{params: params})\r\n      .pipe(map(res => {\r\n        return {\r\n          reasons: res?.data?.records ? res?.data?.records : []\r\n        };\r\n      }));\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,2BAA2B;AACrD,SAAoBC,UAAU,QAAO,sBAAsB;AAC3D,SAAQC,GAAG,QAAmB,MAAM;AAEpC,SAAQC,eAAe,QAAO,iBAAiB;;;AAK/C,OAAM,MAAOC,YAAY;EAIvBC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAEZ,IAAI,CAACC,OAAO,GAAG,GAAGP,WAAW,CAACQ,WAAW,kBAAkB;EAE7D;EAEAC,kBAAkBA,CAACC,EAAS;IAC1B,IAAIC,MAAM,GAAG,IAAIV,UAAU,EAAE,CAACW,GAAG,CAAC,UAAU,EAAEF,EAAE,CAAC;IAEjD,OAAO,IAAI,CAACJ,IAAI,CAChBO,GAAG,CACF,GAAGV,eAAe,CAACW,MAAM,CAACC,GAAG,IAAIZ,eAAe,CAACa,eAAe,CAACC,UAAU,IAAId,eAAe,CAACa,eAAe,CAACE,SAAS,CAACC,qBAAqB,EAAE,EAAC;MAACR,MAAM,EAAEA;IAAM,CAAC,CAAC,CAChKS,IAAI,CAAClB,GAAG,CAACmB,GAAG,IAAG;MACd,OAAO;QACLC,OAAO,EAAED,GAAG,EAAEE,IAAI,EAAEC,OAAO,GAAGH,GAAG,EAAEE,IAAI,EAAEC,OAAO,GAAG;OACpD;IACH,CAAC,CAAC,CAAC;EACP;EAAC,QAAAC,CAAA,G;qBAtBUrB,YAAY,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZ1B,YAAY;IAAA2B,OAAA,EAAZ3B,YAAY,CAAA4B,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}