{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\product-card.ts"], "sourcesContent": ["\r\nexport interface ProductCard {\r\n  productId: number,\r\n  productName: string,\r\n  priceValue: number,\r\n  salePriceValue: number,\r\n  currencyCode: string,\r\n  masterImageUrl: string,\r\n  soldOut: boolean,\r\n  rate: number,\r\n  count: number,\r\n  isLiked: boolean,\r\n  thumbnailImages: string[],\r\n\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}