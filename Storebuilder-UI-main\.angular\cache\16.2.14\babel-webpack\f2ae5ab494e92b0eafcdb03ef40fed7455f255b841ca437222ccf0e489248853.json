{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\nconst _c0 = [\"content\"];\nconst _c1 = [\"navbar\"];\nconst _c2 = [\"inkbar\"];\nconst _c3 = [\"prevBtn\"];\nconst _c4 = [\"nextBtn\"];\nfunction TabMenu_button_2_ChevronLeftIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\");\n  }\n}\nfunction TabMenu_button_2_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11, 12);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.navBackward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_2_ChevronLeftIcon_2_Template, 1, 0, \"ChevronLeftIcon\", 13);\n    i0.ɵɵtemplate(3, TabMenu_button_2_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.previousIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.previousIconTemplate);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.icon)(\"ngStyle\", item_r12.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.label);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 26);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r12.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_ng_container_1_span_1_Template, 1, 2, \"span\", 20);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_1_ng_container_1_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_1_ng_container_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TabMenu_li_7_a_1_ng_container_1_span_5_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(4);\n    const item_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.escape !== false)(\"ngIfElse\", _r20);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c5 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    index: a1\n  };\n};\nfunction TabMenu_li_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.itemClick($event, item_r12));\n    })(\"keydown.enter\", function TabMenu_li_7_a_1_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.itemClick($event, item_r12));\n    });\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_ng_container_1_Template, 6, 4, \"ng-container\", 13);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_1_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    const item_r12 = ctx_r33.$implicit;\n    const i_r13 = ctx_r33.index;\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", item_r12.target);\n    i0.ɵɵattribute(\"href\", item_r12.url, i0.ɵɵsanitizeUrl)(\"tabindex\", item_r12.disabled ? null : \"0\")(\"title\", item_r12.title)(\"id\", item_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r14.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(8, _c5, item_r12, i_r13));\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.icon)(\"ngStyle\", item_r12.iconStyle);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.label);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 26);\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r12.label, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r12.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_2_ng_container_1_span_1_Template, 1, 2, \"span\", 20);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_ng_container_1_span_2_Template, 2, 1, \"span\", 21);\n    i0.ɵɵtemplate(3, TabMenu_li_7_a_2_ng_container_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TabMenu_li_7_a_2_ng_container_1_span_5_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r38 = i0.ɵɵreference(4);\n    const item_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.escape !== false)(\"ngIfElse\", _r38);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r12.badge);\n  }\n}\nfunction TabMenu_li_7_a_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c6 = function () {\n  return {\n    exact: false\n  };\n};\nfunction TabMenu_li_7_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function TabMenu_li_7_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.itemClick($event, item_r12));\n    })(\"keydown.enter\", function TabMenu_li_7_a_2_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.itemClick($event, item_r12));\n    });\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_2_ng_container_1_Template, 6, 4, \"ng-container\", 13);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext();\n    const item_r12 = ctx_r51.$implicit;\n    const i_r13 = ctx_r51.index;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r12.routerLink)(\"queryParams\", item_r12.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r12.routerLinkActiveOptions || i0.ɵɵpureFunction0(17, _c6))(\"target\", item_r12.target)(\"fragment\", item_r12.fragment)(\"queryParamsHandling\", item_r12.queryParamsHandling)(\"preserveFragment\", item_r12.preserveFragment)(\"skipLocationChange\", item_r12.skipLocationChange)(\"replaceUrl\", item_r12.replaceUrl)(\"state\", item_r12.state);\n    i0.ɵɵattribute(\"tabindex\", item_r12.disabled ? null : \"0\")(\"title\", item_r12.title)(\"id\", item_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r15.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(18, _c5, item_r12, i_r13));\n  }\n}\nconst _c7 = function (a1, a2, a3) {\n  return {\n    \"p-tabmenuitem\": true,\n    \"p-disabled\": a1,\n    \"p-highlight\": a2,\n    \"p-hidden\": a3\n  };\n};\nfunction TabMenu_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 15);\n    i0.ɵɵtemplate(1, TabMenu_li_7_a_1_Template, 3, 11, \"a\", 16);\n    i0.ɵɵtemplate(2, TabMenu_li_7_a_2_Template, 3, 21, \"a\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(item_r12.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r12.style)(\"ngClass\", i0.ɵɵpureFunction3(9, _c7, item_r12.disabled, ctx_r3.isActive(item_r12), item_r12.visible === false))(\"tooltipOptions\", item_r12.tooltipOptions);\n    i0.ɵɵattribute(\"aria-selected\", ctx_r3.isActive(item_r12))(\"aria-expanded\", ctx_r3.isActive(item_r12));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r12.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12.routerLink);\n  }\n}\nfunction TabMenu_button_10_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n}\nfunction TabMenu_button_10_3_ng_template_0_Template(rf, ctx) {}\nfunction TabMenu_button_10_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TabMenu_button_10_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TabMenu_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30, 31);\n    i0.ɵɵlistener(\"click\", function TabMenu_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.navForward());\n    });\n    i0.ɵɵtemplate(2, TabMenu_button_10_ChevronRightIcon_2_Template, 1, 0, \"ChevronRightIcon\", 13);\n    i0.ɵɵtemplate(3, TabMenu_button_10_3_Template, 1, 0, null, 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.previousIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.nextIconTemplate);\n  }\n}\nconst _c8 = function (a1) {\n  return {\n    \"p-tabmenu p-component\": true,\n    \"p-tabmenu-scrollable\": a1\n  };\n};\nclass TabMenu {\n  platformId;\n  router;\n  route;\n  cd;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Defines the default active menuitem\n   * @group Props\n   */\n  activeItem;\n  /**\n   * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n   * @group Props\n   */\n  scrollable;\n  /**\n   * Defines if popup mode enabled.\n   */\n  popup;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Event fired when a tab is selected.\n   * @param {MenuItem} item - Menu item.\n   * @group Emits\n   */\n  activeItemChange = new EventEmitter();\n  content;\n  navbar;\n  inkbar;\n  prevBtn;\n  nextBtn;\n  templates;\n  itemTemplate;\n  previousIconTemplate;\n  nextIconTemplate;\n  tabChanged;\n  backwardIsDisabled = true;\n  forwardIsDisabled = false;\n  timerIdForInitialAutoScroll = null;\n  constructor(platformId, router, route, cd) {\n    this.platformId = platformId;\n    this.router = router;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'nexticon':\n          this.nextIconTemplate = item.template;\n          break;\n        case 'previousicon':\n          this.previousIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.updateInkBar();\n      this.initAutoScrollForActiveItem();\n      this.initButtonState();\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.tabChanged) {\n      this.updateInkBar();\n      this.tabChanged = false;\n    }\n  }\n  ngOnDestroy() {\n    this.clearAutoScrollHandler();\n  }\n  isActive(item) {\n    if (item.routerLink) {\n      const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n      return this.router.isActive(this.router.createUrlTree(routerLink, {\n        relativeTo: this.route\n      }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n    }\n    return item === this.activeItem;\n  }\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n    this.activeItem = item;\n    this.activeItemChange.emit(item);\n    this.tabChanged = true;\n    this.cd.markForCheck();\n  }\n  updateInkBar() {\n    const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n    if (tabHeader) {\n      this.inkbar.nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n      this.inkbar.nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n    }\n  }\n  getVisibleButtonWidths() {\n    return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => el ? acc + DomHandler.getWidth(el) : acc, 0);\n  }\n  updateButtonState() {\n    const content = this.content?.nativeElement;\n    const {\n      scrollLeft,\n      scrollWidth\n    } = content;\n    const width = DomHandler.getWidth(content);\n    this.backwardIsDisabled = scrollLeft === 0;\n    this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n  }\n  updateScrollBar(index) {\n    const tabHeader = this.navbar?.nativeElement.children[index];\n    if (!tabHeader) {\n      return;\n    }\n    tabHeader.scrollIntoView({\n      block: 'nearest',\n      inline: 'center'\n    });\n  }\n  onScroll(event) {\n    this.scrollable && this.updateButtonState();\n    event.preventDefault();\n  }\n  navBackward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft - width;\n    content.scrollLeft = pos <= 0 ? 0 : pos;\n  }\n  navForward() {\n    const content = this.content?.nativeElement;\n    const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n    const pos = content.scrollLeft + width;\n    const lastPos = content.scrollWidth - width;\n    content.scrollLeft = pos >= lastPos ? lastPos : pos;\n  }\n  initAutoScrollForActiveItem() {\n    if (!this.scrollable) {\n      return;\n    }\n    this.clearAutoScrollHandler();\n    // We have to wait for the rendering and then can scroll to element.\n    this.timerIdForInitialAutoScroll = setTimeout(() => {\n      const activeItem = this.model.findIndex(menuItem => this.isActive(menuItem));\n      if (activeItem !== -1) {\n        this.updateScrollBar(activeItem);\n      }\n    });\n  }\n  clearAutoScrollHandler() {\n    if (this.timerIdForInitialAutoScroll) {\n      clearTimeout(this.timerIdForInitialAutoScroll);\n      this.timerIdForInitialAutoScroll = null;\n    }\n  }\n  initButtonState() {\n    if (this.scrollable) {\n      // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n      // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n      Promise.resolve().then(() => {\n        this.updateButtonState();\n        this.cd.markForCheck();\n      });\n    }\n  }\n  static ɵfac = function TabMenu_Factory(t) {\n    return new (t || TabMenu)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TabMenu,\n    selectors: [[\"p-tabMenu\"]],\n    contentQueries: function TabMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function TabMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.navbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inkbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.prevBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextBtn = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      activeItem: \"activeItem\",\n      scrollable: \"scrollable\",\n      popup: \"popup\",\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    outputs: {\n      activeItemChange: \"activeItemChange\"\n    },\n    decls: 11,\n    vars: 9,\n    consts: [[3, \"ngClass\", \"ngStyle\"], [1, \"p-tabmenu-nav-container\"], [\"class\", \"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"p-tabmenu-nav-content\", 3, \"scroll\"], [\"content\", \"\"], [\"role\", \"tablist\", 1, \"p-tabmenu-nav\", \"p-reset\"], [\"navbar\", \"\"], [\"role\", \"tab\", \"pTooltip\", \"\", 3, \"ngStyle\", \"class\", \"ngClass\", \"tooltipOptions\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-tabmenu-ink-bar\"], [\"inkbar\", \"\"], [\"class\", \"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-prev\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [\"prevBtn\", \"\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"tab\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", \"role\", \"presentation\", \"pRipple\", \"\", 3, \"target\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"role\", \"presentation\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"role\", \"presentation\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"target\", \"click\", \"keydown.enter\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"presentation\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\"], [\"htmlRouteLabel\", \"\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-tabmenu-nav-next\", \"p-tabmenu-nav-btn\", \"p-link\", 3, \"click\"], [\"nextBtn\", \"\"]],\n    template: function TabMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, TabMenu_button_2_Template, 4, 2, \"button\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3, 4);\n        i0.ɵɵlistener(\"scroll\", function TabMenu_Template_div_scroll_3_listener($event) {\n          return ctx.onScroll($event);\n        });\n        i0.ɵɵelementStart(5, \"ul\", 5, 6);\n        i0.ɵɵtemplate(7, TabMenu_li_7_Template, 3, 13, \"li\", 7);\n        i0.ɵɵelement(8, \"li\", 8, 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(10, TabMenu_button_10_Template, 4, 2, \"button\", 10);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c8, ctx.scrollable))(\"ngStyle\", ctx.style);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.backwardIsDisabled);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.scrollable && !ctx.forwardIsDisabled);\n      }\n    },\n    dependencies: function () {\n      return [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.RouterLink, i1.RouterLinkActive, i3.Ripple, i4.Tooltip, ChevronLeftIcon, ChevronRightIcon];\n    },\n    styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tabMenu',\n      template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"tablist\">\n                        <li\n                            *ngFor=\"let item of model; let i = index\"\n                            role=\"tab\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.aria-selected]=\"isActive(item)\"\n                            [attr.aria-expanded]=\"isActive(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': item.disabled, 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                *ngIf=\"!item.routerLink\"\n                                [attr.href]=\"item.url\"\n                                class=\"p-menuitem-link\"\n                                role=\"presentation\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"item.routerLink\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"presentation\"\n                                class=\"p-menuitem-link\"\n                                (click)=\"itemClick($event, item)\"\n                                (keydown.enter)=\"itemClick($event, item)\"\n                                [attr.tabindex]=\"item.disabled ? null : '0'\"\n                                [target]=\"item.target\"\n                                [attr.title]=\"item.title\"\n                                [attr.id]=\"item.id\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container *ngIf=\"!itemTemplate\">\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ item.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n                                </ng-container>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                            </a>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-tabmenu-nav-container{position:relative}.p-tabmenu-scrollable .p-tabmenu-nav-container{overflow:hidden}.p-tabmenu-nav-content{overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;scrollbar-width:none;overscroll-behavior:contain auto}.p-tabmenu-nav-btn{position:absolute;top:0;z-index:2;height:100%;display:flex;align-items:center;justify-content:center}.p-tabmenu-nav-prev{left:0}.p-tabmenu-nav-next{right:0}.p-tabview-nav-content::-webkit-scrollbar{display:none}.p-tabmenu-nav{display:flex;margin:0;padding:0;list-style-type:none;flex-wrap:nowrap}.p-tabmenu-nav a{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center;position:relative;text-decoration:none;overflow:hidden}.p-tabmenu-nav a:focus{z-index:1}.p-tabmenu-nav .p-menuitem-text{line-height:1;white-space:nowrap}.p-tabmenu-ink-bar{display:none;z-index:1}.p-tabmenu-nav-content::-webkit-scrollbar{display:none}.p-tabmenuitem:not(.p-hidden){display:flex}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i1.Router\n    }, {\n      type: i1.ActivatedRoute\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    activeItem: [{\n      type: Input\n    }],\n    scrollable: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    activeItemChange: [{\n      type: Output\n    }],\n    content: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    navbar: [{\n      type: ViewChild,\n      args: ['navbar']\n    }],\n    inkbar: [{\n      type: ViewChild,\n      args: ['inkbar']\n    }],\n    prevBtn: [{\n      type: ViewChild,\n      args: ['prevBtn']\n    }],\n    nextBtn: [{\n      type: ViewChild,\n      args: ['nextBtn']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass TabMenuModule {\n  static ɵfac = function TabMenuModule_Factory(t) {\n    return new (t || TabMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TabMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon, RouterModule, SharedModule, TooltipModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TabMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n      exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n      declarations: [TabMenu]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TabMenu, TabMenuModule };\n//# sourceMappingURL=primeng-tabmenu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}