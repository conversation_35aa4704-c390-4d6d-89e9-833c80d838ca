{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { CardModule } from 'primeng/card';\nimport { RippleModule } from 'primeng/ripple';\nimport { AboutUsComponentComponent } from './components/about-us/AboutUsComponent/AboutUsComponent.component';\nimport { routes } from './routes';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AboutUsModule {}\nAboutUsModule.ɵfac = function AboutUsModule_Factory(t) {\n  return new (t || AboutUsModule)();\n};\nAboutUsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: AboutUsModule\n});\nAboutUsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule.forChild(routes), RippleModule, CardModule, TranslateModule, SharedModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AboutUsModule, {\n    declarations: [AboutUsComponentComponent],\n    imports: [CommonModule, i1.RouterModule, RippleModule, CardModule, TranslateModule, SharedModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}