{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { passwordValidator } from \"@shared/validators/password.validator\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/dialog\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"primeng/password\";\nfunction IndexComponent_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28)(1, \"small\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"auth.registerPassword.validation8\"));\n  }\n}\nfunction IndexComponent_em_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction IndexComponent_em_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction IndexComponent_em_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    color: a0\n  };\n};\nfunction IndexComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r5.hasMinimum8Chars === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation1\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    color: \"grey\"\n  };\n};\nfunction IndexComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation1\"));\n  }\n}\nfunction IndexComponent_em_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction IndexComponent_em_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction IndexComponent_em_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction IndexComponent_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r10.hasLowerChar === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation2\"));\n  }\n}\nfunction IndexComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation2\"));\n  }\n}\nfunction IndexComponent_em_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction IndexComponent_em_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction IndexComponent_em_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction IndexComponent_span_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r15.hasUpperChar === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation3\"), \"\");\n  }\n}\nfunction IndexComponent_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation3\"), \"\");\n  }\n}\nfunction IndexComponent_em_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction IndexComponent_em_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction IndexComponent_em_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction IndexComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r20.hasAtleastOneNumber === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation4\"));\n  }\n}\nfunction IndexComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation4\"));\n  }\n}\nfunction IndexComponent_em_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction IndexComponent_em_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction IndexComponent_em_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction IndexComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r25.hasSpecialChars === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation5\"));\n  }\n}\nfunction IndexComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation5\"));\n  }\n}\nfunction IndexComponent_p_61_em_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 36);\n  }\n}\nfunction IndexComponent_p_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtemplate(1, IndexComponent_p_61_em_1_Template, 1, 0, \"em\", 34);\n    i0.ɵɵelementStart(2, \"span\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.hasSpecialChars === false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"auth.registerPassword.validation7\"));\n  }\n}\nfunction IndexComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"img\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"updatePassword.passwordReset\"), \" \");\n  }\n}\nconst _c2 = function () {\n  return [\"/login\"];\n};\nfunction IndexComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 40);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(1, 2, \"updatePassword.backToLogin\"))(\"routerLink\", i0.ɵɵpureFunction0(4, _c2));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nconst _c4 = function () {\n  return {\n    width: \"22vw\"\n  };\n};\nconst _c5 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"100vw\"\n  };\n};\nexport class IndexComponent {\n  store;\n  fb;\n  permissionService;\n  user;\n  translate;\n  messageService;\n  el;\n  newPassword = \"\";\n  confirmPassword = \"\";\n  phoneNumber;\n  verificationCode = \"\";\n  countryPhoneNumber = \"\";\n  phoneLength = 12;\n  hasUpperChar;\n  hasAtleastOneNumber;\n  hasLowerChar;\n  passwordMatched = false;\n  hasMinimum8Chars = undefined;\n  hasSpecialChars;\n  passwordIsValid = false;\n  password = \"\";\n  forgetPassword;\n  firstNameFlag = false;\n  displayApprovedModal = false;\n  isMobileLayout = false;\n  constructor(store, fb, permissionService, user, translate, messageService, el) {\n    this.store = store;\n    this.fb = fb;\n    this.permissionService = permissionService;\n    this.user = user;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.el = el;\n  }\n  get formcontrols() {\n    return this.forgetPassword.controls;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.verificationCode = this.store.get('verificationCode');\n    this.phoneNumber = localStorage.getItem('userPhone');\n    this.formGroup();\n  }\n  formGroup() {\n    this.forgetPassword = this.fb.group({\n      newPassword: ['', [Validators.required, passwordValidator()]],\n      confirmPassword: ['', Validators.required]\n    });\n  }\n  approveModal() {\n    this.user.UpdatePassword({\n      mobileNumber: this.phoneNumber,\n      newPassword: this.forgetPassword?.controls['newPassword']?.value,\n      RequestId: this.verificationCode\n    }).subscribe({\n      next: res => {\n        if (res.success) {\n          this.displayApprovedModal = true;\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant(res.message)\n          });\n        }\n      },\n      error: err => {}\n    });\n  }\n  checkPasswordPattern(password) {\n    this.hasAtleastOneNumber = /\\d+/.test(password);\n    this.hasUpperChar = /[A-Z]+/.test(password);\n    this.hasLowerChar = /[a-z]+/.test(password);\n    this.hasMinimum8Chars = /.{10,}/.test(password);\n    this.hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    if (this.confirmPassword === this.password) this.passwordMatched = true;else this.passwordMatched = false;\n    this.passwordIsValid = this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars && this.passwordMatched && this.hasAtleastOneNumber;\n    return this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars && this.passwordMatched && this.hasAtleastOneNumber;\n  }\n  disabled() {\n    if ((this.forgetPassword?.controls['newPassword']?.value === this.forgetPassword?.controls['confirmPassword']?.value || this.forgetPassword?.controls['newPassword']?.value === '') && this.hasSpecialChars && this.hasUpperChar && this.hasLowerChar && this.hasMinimum8Chars && this.hasAtleastOneNumber) {\n      return false;\n    } else {\n      return true;\n    }\n  }\n  onblur(value) {\n    this.isRequired(\"newPassword\");\n    if (value !== 'cp' && !this.hasMinimum8Chars && !this.hasAtleastOneNumber && !this.hasLowerChar && !this.hasSpecialChars && !this.hasUpperChar) {\n      this.hasMinimum8Chars = false;\n      this.hasLowerChar = false;\n      this.hasAtleastOneNumber = false;\n      this.hasSpecialChars = false;\n      this.hasUpperChar = false;\n    }\n  }\n  isRequired(fieldName) {\n    if (this.formcontrols[fieldName]?.errors?.required) {\n      this.firstNameFlag = true;\n    }\n  }\n  isError(fieldName) {\n    return this.formcontrols[fieldName]?.errors;\n  }\n  focusFirstInvalidField() {\n    for (const key of Object.keys(this.formcontrols)) {\n      if (this.formcontrols[key].invalid) {\n        this.focusInputField(key);\n        break;\n      }\n    }\n  }\n  focusInputField(key) {\n    const invalidControl = this.el.nativeElement.querySelector('[formControlName=\"' + key + '\"]');\n    invalidControl.focus();\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    decls: 67,\n    vars: 66,\n    consts: [[1, \"update-password-page\"], [1, \"content-container\", 3, \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-100\"], [1, \"col-12\", \"py-0\", \"text-center\", \"reset-pass\"], [1, \"col-12\", \"mb-3\", \"text-center\", \"secure-pass\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"shadow-signin\", \"px-5\", \"pt-0\", \"pb-4\"], [1, \"p-fluid\", \"p-grid\"], [3, \"formGroup\"], [\"form\", \"ngForm\"], [1, \"p-field\", \"p-col-12\", \"mt-3\"], [1, \"p-float-label\", \"mt-5\"], [\"aria-autocomplete\", \"false\", \"autocomplete\", \"newPassword\", \"formControlName\", \"newPassword\", \"name\", \"something\", 1, \"customClass\", 3, \"feedback\", \"toggleMask\", \"click\", \"ngModelChange\"], [3, \"ngStyle\"], [\"class\", \"field-error\", 4, \"ngIf\"], [1, \"p-float-label\", \"mt-3\"], [\"aria-autocomplete\", \"false\", \"autocomplete\", \"newPassword\", \"formControlName\", \"confirmPassword\", \"name\", \"something\", 1, \"customClass\", 3, \"ngModel\", \"feedback\", \"toggleMask\", \"click\", \"ngModelChange\"], [1, \"p-field\", \"p-col-12\"], [1, \"list\", \"p-0\"], [1, \"list-none\", \"font-size-13\", \"font-italic\", \"mb-2\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-info-circle\", \"style\", \"color: grey; font-size: 0.8rem\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-check-circle\", \"style\", \"color: #01b467; font-size: 0.8rem\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-times-circle\", \"style\", \"color: red; font-size: 0.8rem\", 4, \"ngIf\"], [\"class\", \"ml-2 instruction\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"list-none\", \"font-size-13\", \"font-italic\", \"mb-5\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-2\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"approvedModal\", 3, \"visible\", \"modal\", \"baseZIndex\", \"breakpoints\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"field-error\"], [2, \"color\", \"red\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-info-circle\", 2, \"color\", \"grey\", \"font-size\", \"0.8rem\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-check-circle\", 2, \"color\", \"#01b467\", \"font-size\", \"0.8rem\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-times-circle\", 2, \"color\", \"red\", \"font-size\", \"0.8rem\"], [1, \"ml-2\", \"instruction\", 3, \"ngStyle\"], [\"aria-hidden\", \"true\", \"class\", \"fa fa-times-circle fa-size\", \"style\", \"color: red\", 4, \"ngIf\"], [1, \"ml-2\", \"instruction\", 2, \"color\", \"red\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-times-circle\", \"fa-size\", 2, \"color\", \"red\"], [1, \"icon\", \"mt-5\", \"mb-5\", \"bg-#01B467-500\", \"text-white\", \"text-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"icon\", \"bg-#01B467-500\"], [\"alt\", \"No Image\", \"src\", \"assets/images/Validated.png\"], [1, \"font-bold\", \"text-center\", \"text-black-alpha-90\", \"font-family-password\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-4\", \"width-25\", \"m-auto\", \"font-size-14\", \"second-btn\", 3, \"label\", \"routerLink\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelementContainerStart(1);\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"p\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 4);\n        i0.ɵɵtext(8);\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"form\", 7, 8)(14, \"div\", 9)(15, \"span\", 10)(16, \"p-password\", 11);\n        i0.ɵɵlistener(\"click\", function IndexComponent_Template_p_password_click_16_listener() {\n          return ctx.onblur();\n        })(\"ngModelChange\", function IndexComponent_Template_p_password_ngModelChange_16_listener($event) {\n          return ctx.checkPasswordPattern($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"label\", 12);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(20, IndexComponent_p_20_Template, 4, 3, \"p\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"span\", 14)(23, \"p-password\", 15);\n        i0.ɵɵlistener(\"click\", function IndexComponent_Template_p_password_click_23_listener() {\n          return ctx.onblur(\"cp\");\n        })(\"ngModelChange\", function IndexComponent_Template_p_password_ngModelChange_23_listener($event) {\n          return ctx.confirmPassword = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"label\", 12);\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(27, \"br\");\n        i0.ɵɵelementStart(28, \"div\", 16)(29, \"ul\", 17)(30, \"li\", 18);\n        i0.ɵɵtemplate(31, IndexComponent_em_31_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(32, IndexComponent_em_32_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(33, IndexComponent_em_33_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(34, IndexComponent_span_34_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(35, IndexComponent_span_35_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"li\", 18);\n        i0.ɵɵtemplate(37, IndexComponent_em_37_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(38, IndexComponent_em_38_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(39, IndexComponent_em_39_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(40, IndexComponent_span_40_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(41, IndexComponent_span_41_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"li\", 18);\n        i0.ɵɵtemplate(43, IndexComponent_em_43_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(44, IndexComponent_em_44_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(45, IndexComponent_em_45_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(46, IndexComponent_span_46_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(47, IndexComponent_span_47_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"li\", 18);\n        i0.ɵɵtemplate(49, IndexComponent_em_49_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(50, IndexComponent_em_50_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(51, IndexComponent_em_51_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(52, IndexComponent_span_52_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(53, IndexComponent_span_53_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"li\", 18);\n        i0.ɵɵtemplate(55, IndexComponent_em_55_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(56, IndexComponent_em_56_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(57, IndexComponent_em_57_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(58, IndexComponent_span_58_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(59, IndexComponent_span_59_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"li\", 23);\n        i0.ɵɵtemplate(61, IndexComponent_p_61_Template, 5, 4, \"p\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(62, \"button\", 24);\n        i0.ɵɵlistener(\"click\", function IndexComponent_Template_button_click_62_listener() {\n          return ctx.approveModal();\n        });\n        i0.ɵɵpipe(63, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(64, \"p-dialog\", 25);\n        i0.ɵɵlistener(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_64_listener($event) {\n          return ctx.displayApprovedModal = $event;\n        });\n        i0.ɵɵtemplate(65, IndexComponent_ng_template_65_Template, 5, 3, \"ng-template\", 26);\n        i0.ɵɵtemplate(66, IndexComponent_ng_template_66_Template, 2, 5, \"ng-template\", 27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_6_0;\n        let tmp_8_0;\n        let tmp_12_0;\n        let tmp_39_0;\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(58, _c3, ctx.isMobileLayout ? \"1rem\" : \"220px\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 48, \"updatePassword.resetPassword\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 50, \"updatePassword.setUpSecurePassword\"), \". \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.forgetPassword);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"feedback\", true)(\"toggleMask\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(60, _c0, ctx.firstNameFlag && ((tmp_6_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_6_0.value.length) === 0 ? \"red\" : \"grey\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(19, 52, \"updatePassword.newPassword\"), \" *\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.invalidPassword);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.confirmPassword)(\"feedback\", false)(\"toggleMask\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(62, _c0, ctx.firstNameFlag && ((tmp_12_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_12_0.value.length) === 0 || ((tmp_12_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_12_0.value) !== ((tmp_12_0 = ctx.forgetPassword.get(\"confirmPassword\")) == null ? null : tmp_12_0.value) ? \"red\" : \"grey\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(26, 54, \"updatePassword.confirmPassword\"), \" *\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.forgetPassword == null ? null : (tmp_39_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_39_0.value) !== (ctx.forgetPassword == null ? null : (tmp_39_0 = ctx.forgetPassword.get(\"confirmPassword\")) == null ? null : tmp_39_0.value));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.disabled() === true)(\"label\", i0.ɵɵpipeBind1(63, 56, \"updatePassword.passwordReset\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(64, _c4));\n        i0.ɵɵproperty(\"visible\", ctx.displayApprovedModal)(\"modal\", true)(\"baseZIndex\", 10000)(\"breakpoints\", i0.ɵɵpureFunction0(65, _c5));\n      }\n    },\n    dependencies: [i5.NgIf, i5.NgStyle, i6.Dialog, i4.PrimeTemplate, i7.ButtonDirective, i8.RouterLink, i9.Password, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i3.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog .p-dialog-header {\\n  display: none;\\n}\\n\\ndiv.icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: auto;\\n}\\ndiv.icon[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 31%;\\n  font-weight: bold;\\n  left: 30%;\\n  font-size: 1.2rem;\\n}\\n\\n  .p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n\\n  .p-dialog .p-dialog-footer button {\\n  width: 90% !important;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Helvetica, Arial, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\";\\n  font-size: 1rem;\\n  color: #495057;\\n  background: #ffffff;\\n  padding: 0.5rem 0.5rem;\\n  border: 1px solid #ced4da;\\n  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;\\n  appearance: none;\\n  border-radius: 3px;\\n  width: 100%;\\n}\\n\\n.green[_ngcontent-%COMP%] {\\n  color: forestgreen !important;\\n}\\n\\n.fa-size[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n\\nli[_ngcontent-%COMP%] {\\n  text-align: left;\\n  letter-spacing: 0px;\\n  opacity: 1;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n  font-style: inherit !important;\\n}\\n\\n.update-password-page[_ngcontent-%COMP%] {\\n  margin-top: 90px;\\n}\\n\\n.reset-pass[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.secure-pass[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: #A3A3A3;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n  .customClass input {\\n  height: 60px !important;\\n  width: 100%;\\n  border-radius: 5px 5px 0px 0px;\\n  opacity: 1;\\n  border: none !important;\\n  border-bottom: 1px solid #b9b9b9 !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  padding-top: 20px;\\n  background-color: #f5f5f5 !important;\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n}\\n\\n  .pi-eye {\\n  position: absolute !important;\\n  right: 9px !important;\\n  top: 30px !important;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  color: #323232 !important;\\n  position: absolute;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\\n\\n.instruction[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 400;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.shadow-signin[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  text-transform: uppercase;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .update-password-page[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.font-family-password[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog {\\n  top: -74px !important;\\n  box-shadow: none !important;\\n  border: 1px solid rgba(151, 151, 151, 0.17) !important;\\n}\\n\\n  .p-dialog-content {\\n  margin-top: 4rem !important;\\n}\\n\\n  .p-dialog-footer {\\n  padding-bottom: 0rem !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .margin-x-100[_ngcontent-%COMP%] {\\n    margin-top: 250px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "passwordValidator", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r5", "hasMinimum8Chars", "ɵɵpureFunction0", "_c1", "ctx_r10", "hasLowerChar", "ctx_r15", "hasUpperChar", "ɵɵtextInterpolate1", "ctx_r20", "hasAtleastOneNumber", "ctx_r25", "hasSpecialChars", "ɵɵtemplate", "IndexComponent_p_61_em_1_Template", "ctx_r27", "_c2", "IndexComponent", "store", "fb", "permissionService", "user", "translate", "messageService", "el", "newPassword", "confirmPassword", "phoneNumber", "verificationCode", "countryPhoneNumber", "phoneLength", "passwordMatched", "undefined", "passwordIsValid", "password", "forgetPassword", "firstNameFlag", "displayApprovedModal", "isMobileLayout", "constructor", "formcontrols", "controls", "ngOnInit", "hasPermission", "get", "localStorage", "getItem", "formGroup", "group", "required", "approveModal", "UpdatePassword", "mobileNumber", "value", "RequestId", "subscribe", "next", "res", "success", "add", "severity", "summary", "instant", "message", "error", "err", "checkPasswordPattern", "test", "disabled", "onblur", "isRequired", "fieldName", "errors", "isError", "focusFirstInvalidField", "key", "Object", "keys", "invalid", "focusInputField", "invalidControl", "nativeElement", "querySelector", "focus", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "FormBuilder", "PermissionService", "UserService", "i3", "TranslateService", "i4", "MessageService", "ElementRef", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵlistener", "IndexComponent_Template_p_password_click_16_listener", "IndexComponent_Template_p_password_ngModelChange_16_listener", "$event", "IndexComponent_p_20_Template", "IndexComponent_Template_p_password_click_23_listener", "IndexComponent_Template_p_password_ngModelChange_23_listener", "IndexComponent_em_31_Template", "IndexComponent_em_32_Template", "IndexComponent_em_33_Template", "IndexComponent_span_34_Template", "IndexComponent_span_35_Template", "IndexComponent_em_37_Template", "IndexComponent_em_38_Template", "IndexComponent_em_39_Template", "IndexComponent_span_40_Template", "IndexComponent_span_41_Template", "IndexComponent_em_43_Template", "IndexComponent_em_44_Template", "IndexComponent_em_45_Template", "IndexComponent_span_46_Template", "IndexComponent_span_47_Template", "IndexComponent_em_49_Template", "IndexComponent_em_50_Template", "IndexComponent_em_51_Template", "IndexComponent_span_52_Template", "IndexComponent_span_53_Template", "IndexComponent_em_55_Template", "IndexComponent_em_56_Template", "IndexComponent_em_57_Template", "IndexComponent_span_58_Template", "IndexComponent_span_59_Template", "IndexComponent_p_61_Template", "IndexComponent_Template_button_click_62_listener", "IndexComponent_Template_p_dialog_visibleChange_64_listener", "IndexComponent_ng_template_65_Template", "IndexComponent_ng_template_66_Template", "ɵɵelementContainerEnd", "_c3", "tmp_6_0", "length", "tmp_8_0", "invalidPassword", "tmp_12_0", "tmp_39_0", "ɵɵstyleMap", "_c4", "_c5"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\update-password\\components\\index\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\update-password\\components\\index\\index.component.html"], "sourcesContent": ["import {Component, ElementRef, OnInit} from '@angular/core';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {MessageService} from 'primeng/api';\r\nimport {FormBuilder, FormGroup, Validators} from '@angular/forms';\r\nimport {PermissionService, StoreService, UserService} from \"@core/services\";\r\nimport {passwordValidator} from \"@shared/validators/password.validator\";\r\n\r\n@Component({\r\n  selector: 'app-index',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss']\r\n})\r\nexport class IndexComponent implements OnInit {\r\n  newPassword: string = \"\";\r\n  confirmPassword: string = \"\";\r\n  phoneNumber: any;\r\n  verificationCode: string = \"\";\r\n  countryPhoneNumber: string = \"\";\r\n  phoneLength: number = 12;\r\n  hasUpperChar!: boolean;\r\n  hasAtleastOneNumber!: boolean;\r\n  hasLowerChar!: boolean;\r\n  passwordMatched: boolean = false;\r\n  hasMinimum8Chars: any = undefined;\r\n  hasSpecialChars!: boolean;\r\n  passwordIsValid: boolean = false;\r\n  password: string = \"\";\r\n\r\n\r\n  forgetPassword!: FormGroup;\r\n  firstNameFlag: boolean = false;\r\n  displayApprovedModal: boolean = false;\r\n  isMobileLayout: boolean = false;\r\n\r\n  constructor(private store: StoreService,\r\n              private fb: FormBuilder,\r\n              private permissionService: PermissionService,\r\n              private user: UserService, private translate: TranslateService, private messageService: MessageService, private el: ElementRef) {\r\n  }\r\n\r\n  get formcontrols() {\r\n    return this.forgetPassword.controls;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.verificationCode = this.store.get('verificationCode');\r\n\r\n    this.phoneNumber = localStorage.getItem('userPhone');\r\n    this.formGroup();\r\n\r\n  }\r\n\r\n  formGroup() {\r\n    this.forgetPassword = this.fb.group({\r\n\r\n\r\n      newPassword: ['', [Validators.required,passwordValidator()]],\r\n      confirmPassword: ['', Validators.required],\r\n\r\n    });\r\n  }\r\n\r\n\r\n  approveModal() {\r\n\r\n    this.user.UpdatePassword({\r\n      mobileNumber: this.phoneNumber,\r\n      newPassword: this.forgetPassword?.controls['newPassword']?.value,\r\n      RequestId: this.verificationCode\r\n    })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n\r\n          if (res.success) {\r\n            this.displayApprovedModal = true;\r\n          } else {\r\n            this.messageService.add({severity: 'error', summary: this.translate.instant(res.message)});\r\n\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n\r\n        }\r\n      });\r\n\r\n  }\r\n\r\n  checkPasswordPattern(password: string) {\r\n    this.hasAtleastOneNumber = /\\d+/.test(password);\r\n    this.hasUpperChar = /[A-Z]+/.test(password);\r\n    this.hasLowerChar = /[a-z]+/.test(password);\r\n    this.hasMinimum8Chars = /.{10,}/.test(password);\r\n    this.hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\r\n\r\n\r\n    if (this.confirmPassword === this.password)\r\n      this.passwordMatched = true;\r\n    else\r\n      this.passwordMatched = false;\r\n    this.passwordIsValid = this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars && this.passwordMatched && this.hasAtleastOneNumber;\r\n    return this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars && this.passwordMatched && this.hasAtleastOneNumber;\r\n  }\r\n\r\n  disabled() {\r\n\r\n    if (\r\n      (this.forgetPassword?.controls['newPassword']?.value === this.forgetPassword?.controls['confirmPassword']?.value || this.forgetPassword?.controls['newPassword']?.value === '') &&\r\n      this.hasSpecialChars &&\r\n      this.hasUpperChar &&\r\n      this.hasLowerChar &&\r\n      this.hasMinimum8Chars && this.hasAtleastOneNumber) {\r\n\r\n      return false;\r\n    } else {\r\n\r\n      return true;\r\n    }\r\n  }\r\n\r\n  onblur(value?: any) {\r\n    this.isRequired(\"newPassword\");\r\n    if (value !== 'cp' && !this.hasMinimum8Chars && !this.hasAtleastOneNumber && !this.hasLowerChar && !this.hasSpecialChars && !this.hasUpperChar) {\r\n      this.hasMinimum8Chars = false;\r\n      this.hasLowerChar = false;\r\n      this.hasAtleastOneNumber = false;\r\n      this.hasSpecialChars = false;\r\n      this.hasUpperChar = false;\r\n    }\r\n  }\r\n\r\n  isRequired(fieldName: any) {\r\n\r\n    if (\r\n      this.formcontrols[fieldName]?.errors?.required) {\r\n      this.firstNameFlag = true;\r\n\r\n    }\r\n\r\n\r\n  }\r\n\r\n  isError(fieldName: any) {\r\n    return (\r\n\r\n\r\n      this.formcontrols[fieldName]?.errors\r\n    );\r\n  }\r\n\r\n  private focusFirstInvalidField() {\r\n    for (const key of Object.keys(this.formcontrols)) {\r\n      if (this.formcontrols[key].invalid) {\r\n        this.focusInputField(key);\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  private focusInputField(key: string) {\r\n    const invalidControl = this.el.nativeElement.querySelector(\r\n      '[formControlName=\"' + key + '\"]'\r\n    );\r\n    invalidControl.focus();\r\n  }\r\n\r\n}\r\n\r\n", "<section class=\"update-password-page\">\r\n  <ng-container>\r\n    <div class=\"content-container\"\r\n         [ngStyle]=\"{marginTop: isMobileLayout ? '1rem' : '220px' }\">\r\n      <div class=\"grid justify-content-center margin-x-100\">\r\n        <p class=\"col-12 py-0 text-center reset-pass\">\r\n          {{ \"updatePassword.resetPassword\" | translate }}\r\n        </p>\r\n        <div class=\"col-12 mb-3 text-center secure-pass\">\r\n          {{ \"updatePassword.setUpSecurePassword\" | translate }}.\r\n        </div>\r\n        <div class=\"col-12 col-md-8 col-lg-6 bg-white shadow-signin px-5 pt-0 pb-4\">\r\n          <div class=\"p-fluid p-grid\">\r\n            <form #form=\"ngForm\" [formGroup]=\"forgetPassword\">\r\n              <div class=\"p-field p-col-12 mt-3\">\r\n                <span class=\"p-float-label mt-5\">\r\n                  <p-password (click)=\"onblur()\" (ngModelChange)=\"checkPasswordPattern($event)\" [feedback]=\"true\"\r\n                    [toggleMask]=\"true\" aria-autocomplete=\"false\" autocomplete=\"newPassword\" class=\"customClass\"\r\n                    formControlName=\"newPassword\" name=\"something\"></p-password>\r\n                  <label [ngStyle]=\"{\r\n                      color:\r\n                        firstNameFlag &&\r\n                        forgetPassword.get('newPassword')?.value.length === 0\r\n                          ? 'red'\r\n                          : 'grey'\r\n                    }\">{{ \"updatePassword.newPassword\" | translate }} *</label>\r\n\r\n                </span>\r\n                <p *ngIf=\"forgetPassword.get('newPassword')?.errors?.invalidPassword\" class=\"field-error\">\r\n                  <small style=\"color: red\">{{\r\n                    \"auth.registerPassword.validation8\" | translate\r\n                    }}</small>\r\n                </p>\r\n              </div>\r\n              <div class=\"p-field p-col-12 mt-3\">\r\n                <span class=\"p-float-label mt-3\">\r\n                  <p-password (click)=\"onblur('cp')\" [(ngModel)]=\"confirmPassword\" [feedback]=\"false\"\r\n                    [toggleMask]=\"true\" aria-autocomplete=\"false\" autocomplete=\"newPassword\" class=\"customClass\"\r\n                    formControlName=\"confirmPassword\" name=\"something\"></p-password>\r\n                  <label [ngStyle]=\"{\r\n                      color:\r\n                        (firstNameFlag &&\r\n                          forgetPassword.get('newPassword')?.value.length ===\r\n                            0) ||\r\n                        forgetPassword.get('newPassword')?.value !==\r\n                          forgetPassword.get('confirmPassword')?.value\r\n                          ? 'red'\r\n                          : 'grey'\r\n                    }\">{{ \"updatePassword.confirmPassword\" | translate }} *</label>\r\n                </span>\r\n              </div>\r\n            </form>\r\n            <br />\r\n            <div class=\"p-field p-col-12\">\r\n              <ul class=\"list p-0\">\r\n                <li class=\"list-none font-size-13 font-italic mb-2\">\r\n                  <em *ngIf=\"hasMinimum8Chars === undefined\" aria-hidden=\"true\" class=\"pi pi-info-circle\"\r\n                    style=\"color: grey; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasMinimum8Chars === true\" aria-hidden=\"true\" class=\"pi pi-check-circle\"\r\n                    style=\"color: #01b467; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasMinimum8Chars === false\" aria-hidden=\"true\" class=\"pi pi-times-circle\"\r\n                    style=\"color: red; font-size: 0.8rem\"></em>\r\n                  <span *ngIf=\"hasMinimum8Chars !== undefined\" [ngStyle]=\"{\r\n                      color: hasMinimum8Chars === true ? '#01B467' : 'red'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation1\" | translate }}</span>\r\n                  <span *ngIf=\"hasMinimum8Chars === undefined\" [ngStyle]=\"{\r\n                      color: 'grey'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation1\" | translate }}</span>\r\n                </li>\r\n\r\n                <li class=\"list-none font-size-13 font-italic mb-2\">\r\n                  <em *ngIf=\"hasLowerChar === undefined\" aria-hidden=\"true\" class=\"pi pi-info-circle\"\r\n                      style=\"color: grey; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasLowerChar === true\" aria-hidden=\"true\" class=\"pi pi-check-circle\"\r\n                      style=\"color: #01b467; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasLowerChar === false\" aria-hidden=\"true\" class=\"pi pi-times-circle\"\r\n                      style=\"color: red; font-size: 0.8rem\"></em>\r\n                  <span *ngIf=\"hasLowerChar !== undefined\" [ngStyle]=\"{\r\n                      color: hasLowerChar === true ? '#01B467' : 'red'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation2\" | translate }}</span>\r\n                  <span *ngIf=\"hasLowerChar === undefined\" [ngStyle]=\"{\r\n                      color: 'grey'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation2\" | translate }}</span>\r\n                </li>\r\n\r\n                <li class=\"list-none font-size-13 font-italic mb-2\">\r\n                  <em *ngIf=\"hasUpperChar === undefined\" aria-hidden=\"true\" class=\"pi pi-info-circle\"\r\n                      style=\"color: grey; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasUpperChar === true\" aria-hidden=\"true\" class=\"pi pi-check-circle\"\r\n                      style=\"color: #01b467; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasUpperChar === false\" aria-hidden=\"true\" class=\"pi pi-times-circle\"\r\n                      style=\"color: red; font-size: 0.8rem\"></em>\r\n                  <span *ngIf=\"hasUpperChar !== undefined\" [ngStyle]=\"{\r\n                      color: hasUpperChar === true ? '#01B467' : 'red'\r\n                    }\" class=\"ml-2 instruction\">\r\n                    {{ \"auth.registerPassword.validation3\" | translate }}</span>\r\n                  <span *ngIf=\"hasUpperChar === undefined\" [ngStyle]=\"{\r\n                      color: 'grey'\r\n                    }\" class=\"ml-2 instruction\">\r\n                    {{ \"auth.registerPassword.validation3\" | translate }}</span>\r\n                </li>\r\n                <li class=\"list-none font-size-13 font-italic mb-2\">\r\n                  <em *ngIf=\"hasAtleastOneNumber === undefined\" aria-hidden=\"true\" class=\"pi pi-info-circle\"\r\n                      style=\"color: grey; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasAtleastOneNumber === true\" aria-hidden=\"true\"class=\"pi pi-check-circle\"\r\n                      style=\"color: #01b467; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasAtleastOneNumber === false\" aria-hidden=\"true\" class=\"pi pi-times-circle\"\r\n                      style=\"color: red; font-size: 0.8rem\"></em>\r\n\r\n                  <span *ngIf=\"hasAtleastOneNumber !== undefined\" [ngStyle]=\"{\r\n                      color: hasAtleastOneNumber === true ? '#01B467' : 'red'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation4\" | translate }}</span>\r\n                  <span *ngIf=\"hasAtleastOneNumber === undefined\" [ngStyle]=\"{\r\n                      color: 'grey'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation4\" | translate }}</span>\r\n                </li>\r\n                <li class=\"list-none font-size-13 font-italic mb-2\">\r\n                  <em *ngIf=\"hasSpecialChars === undefined\" aria-hidden=\"true\" class=\"pi pi-info-circle\"\r\n                      style=\"color: grey; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasSpecialChars === true\" aria-hidden=\"true\" class=\"pi pi-check-circle\"\r\n                      style=\"color: #01b467; font-size: 0.8rem\"></em>\r\n                  <em *ngIf=\"hasSpecialChars === false\" aria-hidden=\"true\" class=\"pi pi-times-circle\"\r\n                      style=\"color: red; font-size: 0.8rem\"></em>\r\n                  <span *ngIf=\"hasSpecialChars !== undefined\" [ngStyle]=\"{\r\n                      color: hasSpecialChars === true ? '#01B467' : 'red'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation5\" | translate }}</span>\r\n                  <span *ngIf=\"hasSpecialChars === undefined\" [ngStyle]=\"{\r\n                      color: 'grey'\r\n                    }\" class=\"ml-2 instruction\">{{ \"auth.registerPassword.validation5\" | translate }}</span>\r\n                </li>\r\n                <li class=\"list-none font-size-13 font-italic mb-5\">\r\n                  <p *ngIf=\"\r\n                      forgetPassword?.get('newPassword')?.value !==\r\n                      forgetPassword?.get('confirmPassword')?.value\r\n                    \" class=\"field-error\">\r\n                    <em *ngIf=\"hasSpecialChars === false\" aria-hidden=\"true\" class=\"fa fa-times-circle fa-size\"\r\n                      style=\"color: red\"></em>\r\n                    <span class=\"ml-2 instruction\" style=\"color: red\">{{ \"auth.registerPassword.validation7\" | translate\r\n                      }}</span>\r\n                  </p>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n\r\n            <button (click)=\"approveModal()\" [disabled]=\"disabled() === true\"\r\n              [label]=\"'updatePassword.passwordReset' | translate\"\r\n              class=\"p-field p-col-12 my-2 width-100 font-size-14 second-btn\" pButton type=\"button\"></button>\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <p-dialog [(visible)]=\"displayApprovedModal\" [modal]=\"true\" [baseZIndex]=\"10000\"\r\n      [breakpoints]=\"{ '960px': '75vw', '640px': '100vw' }\" [style]=\"{ width: '22vw' }\" class=\"approvedModal\">\r\n      <ng-template pTemplate=\"content\">\r\n        <div\r\n          class=\"icon mt-5 mb-5 bg-#01B467-500 text-white text-center w-3rem h-3rem border-circle icon bg-#01B467-500\">\r\n          <img alt=\"No Image\" src=\"assets/images/Validated.png\" />\r\n        </div>\r\n\r\n        <p class=\"font-bold text-center text-black-alpha-90 font-family-password\">\r\n          {{ \"updatePassword.passwordReset\" | translate }}\r\n        </p>\r\n      </ng-template>\r\n      <ng-template pTemplate=\"footer\">\r\n        <button [label]=\"'updatePassword.backToLogin' | translate\" [routerLink]=\"['/login']\"\r\n          class=\"p-field p-col-12 my-4 width-25 m-auto font-size-14 second-btn\" pButton type=\"button\"></button>\r\n      </ng-template>\r\n    </p-dialog>\r\n  </ng-container>\r\n</section>\r\n"], "mappings": "AAGA,SAAgCA,UAAU,QAAO,gBAAgB;AAEjE,SAAQC,iBAAiB,QAAO,uCAAuC;;;;;;;;;;;;;ICuBvDC,EAAA,CAAAC,cAAA,YAA0F;IAC9DD,EAAA,CAAAE,MAAA,GAEtB;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;IAFcH,EAAA,CAAAI,SAAA,GAEtB;IAFsBJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAEtB;;;;;IAyBJN,EAAA,CAAAO,SAAA,aAC8C;;;;;IAC9CP,EAAA,CAAAO,SAAA,aACiD;;;;;IACjDP,EAAA,CAAAO,SAAA,aAC6C;;;;;;;;;;IAC7CP,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAF7CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,gBAAA,+BAEzC;IAA0BZ,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;;;;;;IACnFN,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAF7CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAEzC;IAA0Bd,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IAInFN,EAAA,CAAAO,SAAA,aACgD;;;;;IAChDP,EAAA,CAAAO,SAAA,aACmD;;;;;IACnDP,EAAA,CAAAO,SAAA,aAC+C;;;;;IAC/CP,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFjDH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAK,OAAA,CAAAC,YAAA,+BAErC;IAA0BhB,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IACnFN,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAFjDH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAErC;IAA0Bd,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IAInFN,EAAA,CAAAO,SAAA,aACgD;;;;;IAChDP,EAAA,CAAAO,SAAA,aACmD;;;;;IACnDP,EAAA,CAAAO,SAAA,aAC+C;;;;;IAC/CP,EAAA,CAAAC,cAAA,eAE8B;IAC5BD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHrBH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAO,OAAA,CAAAC,YAAA,+BAErC;IACFlB,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAM,WAAA,gDAAqD;;;;;IACvDN,EAAA,CAAAC,cAAA,eAE8B;IAC5BD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAHrBH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAErC;IACFd,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAM,WAAA,gDAAqD;;;;;IAGvDN,EAAA,CAAAO,SAAA,aACgD;;;;;IAChDP,EAAA,CAAAO,SAAA,aACmD;;;;;IACnDP,EAAA,CAAAO,SAAA,aAC+C;;;;;IAE/CP,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAF1CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAU,OAAA,CAAAC,mBAAA,+BAE5C;IAA0BrB,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IACnFN,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAF1CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAE5C;IAA0Bd,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IAGnFN,EAAA,CAAAO,SAAA,aACgD;;;;;IAChDP,EAAA,CAAAO,SAAA,aACmD;;;;;IACnDP,EAAA,CAAAO,SAAA,aAC+C;;;;;IAC/CP,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAF9CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAY,OAAA,CAAAC,eAAA,+BAExC;IAA0BvB,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IACnFN,EAAA,CAAAC,cAAA,eAE8B;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAF9CH,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAExC;IAA0Bd,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAAqD;;;;;IAOjFN,EAAA,CAAAO,SAAA,aAC0B;;;;;IAL5BP,EAAA,CAAAC,cAAA,YAGwB;IACtBD,EAAA,CAAAwB,UAAA,IAAAC,iCAAA,iBAC0B;IAC1BzB,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,GAC9C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHNH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAQ,UAAA,SAAAkB,OAAA,CAAAH,eAAA,WAA+B;IAEcvB,EAAA,CAAAI,SAAA,GAC9C;IAD8CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4CAC9C;;;;;IAkBhBN,EAAA,CAAAC,cAAA,cAC+G;IAC7GD,EAAA,CAAAO,SAAA,cAAwD;IAC1DP,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,YAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAM,WAAA,4CACF;;;;;;;;IAGAN,EAAA,CAAAO,SAAA,iBACuG;;;;IAD/FP,EAAA,CAAAQ,UAAA,UAAAR,EAAA,CAAAM,WAAA,qCAAkD,eAAAN,EAAA,CAAAa,eAAA,IAAAc,GAAA;;;;;;;;;;;;;;;;;;;AD1JlE,OAAM,MAAOC,cAAc;EAsBLC,KAAA;EACAC,EAAA;EACAC,iBAAA;EACAC,IAAA;EAA2BC,SAAA;EAAqCC,cAAA;EAAwCC,EAAA;EAxB5HC,WAAW,GAAW,EAAE;EACxBC,eAAe,GAAW,EAAE;EAC5BC,WAAW;EACXC,gBAAgB,GAAW,EAAE;EAC7BC,kBAAkB,GAAW,EAAE;EAC/BC,WAAW,GAAW,EAAE;EACxBvB,YAAY;EACZG,mBAAmB;EACnBL,YAAY;EACZ0B,eAAe,GAAY,KAAK;EAChC9B,gBAAgB,GAAQ+B,SAAS;EACjCpB,eAAe;EACfqB,eAAe,GAAY,KAAK;EAChCC,QAAQ,GAAW,EAAE;EAGrBC,cAAc;EACdC,aAAa,GAAY,KAAK;EAC9BC,oBAAoB,GAAY,KAAK;EACrCC,cAAc,GAAY,KAAK;EAE/BC,YAAoBrB,KAAmB,EACnBC,EAAe,EACfC,iBAAoC,EACpCC,IAAiB,EAAUC,SAA2B,EAAUC,cAA8B,EAAUC,EAAc;IAHtH,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,IAAI,GAAJA,IAAI;IAAuB,KAAAC,SAAS,GAATA,SAAS;IAA4B,KAAAC,cAAc,GAAdA,cAAc;IAA0B,KAAAC,EAAE,GAAFA,EAAE;EAC9H;EAEA,IAAIgB,YAAYA,CAAA;IACd,OAAO,IAAI,CAACL,cAAc,CAACM,QAAQ;EACrC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACJ,cAAc,GAAG,IAAI,CAAClB,iBAAiB,CAACuB,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACV,KAAK,CAAC0B,GAAG,CAAC,kBAAkB,CAAC;IAE1D,IAAI,CAACjB,WAAW,GAAGkB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACC,SAAS,EAAE;EAElB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACZ,cAAc,GAAG,IAAI,CAAChB,EAAE,CAAC6B,KAAK,CAAC;MAGlCvB,WAAW,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAAC8D,QAAQ,EAAC7D,iBAAiB,EAAE,CAAC,CAAC;MAC5DsC,eAAe,EAAE,CAAC,EAAE,EAAEvC,UAAU,CAAC8D,QAAQ;KAE1C,CAAC;EACJ;EAGAC,YAAYA,CAAA;IAEV,IAAI,CAAC7B,IAAI,CAAC8B,cAAc,CAAC;MACvBC,YAAY,EAAE,IAAI,CAACzB,WAAW;MAC9BF,WAAW,EAAE,IAAI,CAACU,cAAc,EAAEM,QAAQ,CAAC,aAAa,CAAC,EAAEY,KAAK;MAChEC,SAAS,EAAE,IAAI,CAAC1B;KACjB,CAAC,CACC2B,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAQ,IAAI;QAEjB,IAAIA,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAACrB,oBAAoB,GAAG,IAAI;SACjC,MAAM;UACL,IAAI,CAACd,cAAc,CAACoC,GAAG,CAAC;YAACC,QAAQ,EAAE,OAAO;YAAEC,OAAO,EAAE,IAAI,CAACvC,SAAS,CAACwC,OAAO,CAACL,GAAG,CAACM,OAAO;UAAC,CAAC,CAAC;;MAG9F,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI,CAEpB;KACD,CAAC;EAEN;EAEAC,oBAAoBA,CAAChC,QAAgB;IACnC,IAAI,CAACxB,mBAAmB,GAAG,KAAK,CAACyD,IAAI,CAACjC,QAAQ,CAAC;IAC/C,IAAI,CAAC3B,YAAY,GAAG,QAAQ,CAAC4D,IAAI,CAACjC,QAAQ,CAAC;IAC3C,IAAI,CAAC7B,YAAY,GAAG,QAAQ,CAAC8D,IAAI,CAACjC,QAAQ,CAAC;IAC3C,IAAI,CAACjC,gBAAgB,GAAG,QAAQ,CAACkE,IAAI,CAACjC,QAAQ,CAAC;IAC/C,IAAI,CAACtB,eAAe,GAAG,wBAAwB,CAACuD,IAAI,CAACjC,QAAQ,CAAC;IAG9D,IAAI,IAAI,CAACR,eAAe,KAAK,IAAI,CAACQ,QAAQ,EACxC,IAAI,CAACH,eAAe,GAAG,IAAI,CAAC,KAE5B,IAAI,CAACA,eAAe,GAAG,KAAK;IAC9B,IAAI,CAACE,eAAe,GAAG,IAAI,CAAChC,gBAAgB,IAAI,IAAI,CAACM,YAAY,IAAI,IAAI,CAACF,YAAY,IAAI,IAAI,CAACO,eAAe,IAAI,IAAI,CAACmB,eAAe,IAAI,IAAI,CAACrB,mBAAmB;IAClK,OAAO,IAAI,CAACT,gBAAgB,IAAI,IAAI,CAACM,YAAY,IAAI,IAAI,CAACF,YAAY,IAAI,IAAI,CAACO,eAAe,IAAI,IAAI,CAACmB,eAAe,IAAI,IAAI,CAACrB,mBAAmB;EACpJ;EAEA0D,QAAQA,CAAA;IAEN,IACE,CAAC,IAAI,CAACjC,cAAc,EAAEM,QAAQ,CAAC,aAAa,CAAC,EAAEY,KAAK,KAAK,IAAI,CAAClB,cAAc,EAAEM,QAAQ,CAAC,iBAAiB,CAAC,EAAEY,KAAK,IAAI,IAAI,CAAClB,cAAc,EAAEM,QAAQ,CAAC,aAAa,CAAC,EAAEY,KAAK,KAAK,EAAE,KAC9K,IAAI,CAACzC,eAAe,IACpB,IAAI,CAACL,YAAY,IACjB,IAAI,CAACF,YAAY,IACjB,IAAI,CAACJ,gBAAgB,IAAI,IAAI,CAACS,mBAAmB,EAAE;MAEnD,OAAO,KAAK;KACb,MAAM;MAEL,OAAO,IAAI;;EAEf;EAEA2D,MAAMA,CAAChB,KAAW;IAChB,IAAI,CAACiB,UAAU,CAAC,aAAa,CAAC;IAC9B,IAAIjB,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAACpD,gBAAgB,IAAI,CAAC,IAAI,CAACS,mBAAmB,IAAI,CAAC,IAAI,CAACL,YAAY,IAAI,CAAC,IAAI,CAACO,eAAe,IAAI,CAAC,IAAI,CAACL,YAAY,EAAE;MAC9I,IAAI,CAACN,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACI,YAAY,GAAG,KAAK;MACzB,IAAI,CAACK,mBAAmB,GAAG,KAAK;MAChC,IAAI,CAACE,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACL,YAAY,GAAG,KAAK;;EAE7B;EAEA+D,UAAUA,CAACC,SAAc;IAEvB,IACE,IAAI,CAAC/B,YAAY,CAAC+B,SAAS,CAAC,EAAEC,MAAM,EAAEvB,QAAQ,EAAE;MAChD,IAAI,CAACb,aAAa,GAAG,IAAI;;EAK7B;EAEAqC,OAAOA,CAACF,SAAc;IACpB,OAGE,IAAI,CAAC/B,YAAY,CAAC+B,SAAS,CAAC,EAAEC,MAAM;EAExC;EAEQE,sBAAsBA,CAAA;IAC5B,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,YAAY,CAAC,EAAE;MAChD,IAAI,IAAI,CAACA,YAAY,CAACmC,GAAG,CAAC,CAACG,OAAO,EAAE;QAClC,IAAI,CAACC,eAAe,CAACJ,GAAG,CAAC;QACzB;;;EAGN;EAEQI,eAAeA,CAACJ,GAAW;IACjC,MAAMK,cAAc,GAAG,IAAI,CAACxD,EAAE,CAACyD,aAAa,CAACC,aAAa,CACxD,oBAAoB,GAAGP,GAAG,GAAG,IAAI,CAClC;IACDK,cAAc,CAACG,KAAK,EAAE;EACxB;;qBAxJWlE,cAAc,EAAA5B,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAjG,EAAA,CAAA+F,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnG,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAI,iBAAA,GAAApG,EAAA,CAAA+F,iBAAA,CAAAC,EAAA,CAAAK,WAAA,GAAArG,EAAA,CAAA+F,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvG,EAAA,CAAA+F,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAzG,EAAA,CAAA+F,iBAAA,CAAA/F,EAAA,CAAA0G,UAAA;EAAA;;UAAd9E,cAAc;IAAA+E,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ3BjH,EAAA,CAAAC,cAAA,iBAAsC;QACpCD,EAAA,CAAAmH,uBAAA,GAAc;QACZnH,EAAA,CAAAC,cAAA,aACiE;QAG3DD,EAAA,CAAAE,MAAA,GACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,aAAiD;QAC/CD,EAAA,CAAAE,MAAA,GACF;;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAA4E;QAKtDD,EAAA,CAAAoH,UAAA,mBAAAC,qDAAA;UAAA,OAASH,GAAA,CAAAlC,MAAA,EAAQ;QAAA,EAAC,2BAAAsC,6DAAAC,MAAA;UAAA,OAAkBL,GAAA,CAAArC,oBAAA,CAAA0C,MAAA,CAA4B;QAAA,EAA9C;QAEmBvH,EAAA,CAAAG,YAAA,EAAa;QAC9DH,EAAA,CAAAC,cAAA,iBAMK;QAAAD,EAAA,CAAAE,MAAA,IAAgD;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAG/DH,EAAA,CAAAwB,UAAA,KAAAgG,4BAAA,gBAII;QACNxH,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAAmC;QAEnBD,EAAA,CAAAoH,UAAA,mBAAAK,qDAAA;UAAA,OAASP,GAAA,CAAAlC,MAAA,CAAO,IAAI,CAAC;QAAA,EAAC,2BAAA0C,6DAAAH,MAAA;UAAA,OAAAL,GAAA,CAAA7E,eAAA,GAAAkF,MAAA;QAAA;QAEmBvH,EAAA,CAAAG,YAAA,EAAa;QAClEH,EAAA,CAAAC,cAAA,iBASK;QAAAD,EAAA,CAAAE,MAAA,IAAoD;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAIvEH,EAAA,CAAAO,SAAA,UAAM;QACNP,EAAA,CAAAC,cAAA,eAA8B;QAGxBD,EAAA,CAAAwB,UAAA,KAAAmG,6BAAA,iBAC8C;QAC9C3H,EAAA,CAAAwB,UAAA,KAAAoG,6BAAA,iBACiD;QACjD5H,EAAA,CAAAwB,UAAA,KAAAqG,6BAAA,iBAC6C;QAC7C7H,EAAA,CAAAwB,UAAA,KAAAsG,+BAAA,mBAE0F;QAC1F9H,EAAA,CAAAwB,UAAA,KAAAuG,+BAAA,mBAE0F;QAC5F/H,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,cAAoD;QAClDD,EAAA,CAAAwB,UAAA,KAAAwG,6BAAA,iBACgD;QAChDhI,EAAA,CAAAwB,UAAA,KAAAyG,6BAAA,iBACmD;QACnDjI,EAAA,CAAAwB,UAAA,KAAA0G,6BAAA,iBAC+C;QAC/ClI,EAAA,CAAAwB,UAAA,KAAA2G,+BAAA,mBAE0F;QAC1FnI,EAAA,CAAAwB,UAAA,KAAA4G,+BAAA,mBAE0F;QAC5FpI,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,cAAoD;QAClDD,EAAA,CAAAwB,UAAA,KAAA6G,6BAAA,iBACgD;QAChDrI,EAAA,CAAAwB,UAAA,KAAA8G,6BAAA,iBACmD;QACnDtI,EAAA,CAAAwB,UAAA,KAAA+G,6BAAA,iBAC+C;QAC/CvI,EAAA,CAAAwB,UAAA,KAAAgH,+BAAA,mBAG8D;QAC9DxI,EAAA,CAAAwB,UAAA,KAAAiH,+BAAA,mBAG8D;QAChEzI,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAoD;QAClDD,EAAA,CAAAwB,UAAA,KAAAkH,6BAAA,iBACgD;QAChD1I,EAAA,CAAAwB,UAAA,KAAAmH,6BAAA,iBACmD;QACnD3I,EAAA,CAAAwB,UAAA,KAAAoH,6BAAA,iBAC+C;QAE/C5I,EAAA,CAAAwB,UAAA,KAAAqH,+BAAA,mBAE0F;QAC1F7I,EAAA,CAAAwB,UAAA,KAAAsH,+BAAA,mBAE0F;QAC5F9I,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAoD;QAClDD,EAAA,CAAAwB,UAAA,KAAAuH,6BAAA,iBACgD;QAChD/I,EAAA,CAAAwB,UAAA,KAAAwH,6BAAA,iBACmD;QACnDhJ,EAAA,CAAAwB,UAAA,KAAAyH,6BAAA,iBAC+C;QAC/CjJ,EAAA,CAAAwB,UAAA,KAAA0H,+BAAA,mBAE0F;QAC1FlJ,EAAA,CAAAwB,UAAA,KAAA2H,+BAAA,mBAE0F;QAC5FnJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAoD;QAClDD,EAAA,CAAAwB,UAAA,KAAA4H,4BAAA,gBAQI;QACNpJ,EAAA,CAAAG,YAAA,EAAK;QAITH,EAAA,CAAAC,cAAA,kBAEwF;QAFhFD,EAAA,CAAAoH,UAAA,mBAAAiC,iDAAA;UAAA,OAASnC,GAAA,CAAArD,YAAA,EAAc;QAAA,EAAC;;QAEwD7D,EAAA,CAAAG,YAAA,EAAS;QAOzGH,EAAA,CAAAC,cAAA,oBAC0G;QADhGD,EAAA,CAAAoH,UAAA,2BAAAkC,2DAAA/B,MAAA;UAAA,OAAAL,GAAA,CAAAlE,oBAAA,GAAAuE,MAAA;QAAA,EAAkC;QAE1CvH,EAAA,CAAAwB,UAAA,KAAA+H,sCAAA,0BASc;QACdvJ,EAAA,CAAAwB,UAAA,KAAAgI,sCAAA,0BAGc;QAChBxJ,EAAA,CAAAG,YAAA,EAAW;QACbH,EAAA,CAAAyJ,qBAAA,EAAe;QACjBzJ,EAAA,CAAAG,YAAA,EAAU;;;;;;;QAxKDH,EAAA,CAAAI,SAAA,GAA2D;QAA3DJ,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,KAAAiJ,GAAA,EAAAxC,GAAA,CAAAjE,cAAA,qBAA2D;QAG1DjD,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAM,WAAA,6CACF;QAEEN,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAM,WAAA,oDACF;QAGyBN,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAQ,UAAA,cAAA0G,GAAA,CAAApE,cAAA,CAA4B;QAGmC9C,EAAA,CAAAI,SAAA,GAAiB;QAAjBJ,EAAA,CAAAQ,UAAA,kBAAiB;QAGxFR,EAAA,CAAAI,SAAA,GAMH;QANGJ,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAwG,GAAA,CAAAnE,aAAA,MAAA4G,OAAA,GAAAzC,GAAA,CAAApE,cAAA,CAAAS,GAAA,kCAAAoG,OAAA,CAAA3F,KAAA,CAAA4F,MAAA,0BAMH;QAAC5J,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAmB,kBAAA,KAAAnB,EAAA,CAAAM,WAAA,6CAAgD;QAGnDN,EAAA,CAAAI,SAAA,GAAgE;QAAhEJ,EAAA,CAAAQ,UAAA,UAAAqJ,OAAA,GAAA3C,GAAA,CAAApE,cAAA,CAAAS,GAAA,kCAAAsG,OAAA,CAAA1E,MAAA,kBAAA0E,OAAA,CAAA1E,MAAA,CAAA2E,eAAA,CAAgE;QAQ/B9J,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAAQ,UAAA,YAAA0G,GAAA,CAAA7E,eAAA,CAA6B;QAGzDrC,EAAA,CAAAI,SAAA,GASH;QATGJ,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAS,eAAA,KAAAC,GAAA,EAAAwG,GAAA,CAAAnE,aAAA,MAAAgH,QAAA,GAAA7C,GAAA,CAAApE,cAAA,CAAAS,GAAA,kCAAAwG,QAAA,CAAA/F,KAAA,CAAA4F,MAAA,aAAAG,QAAA,GAAA7C,GAAA,CAAApE,cAAA,CAAAS,GAAA,kCAAAwG,QAAA,CAAA/F,KAAA,QAAA+F,QAAA,GAAA7C,GAAA,CAAApE,cAAA,CAAAS,GAAA,sCAAAwG,QAAA,CAAA/F,KAAA,oBASH;QAAChE,EAAA,CAAAI,SAAA,GAAoD;QAApDJ,EAAA,CAAAmB,kBAAA,KAAAnB,EAAA,CAAAM,WAAA,iDAAoD;QAQpDN,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,gBAAA,KAAA+B,SAAA,CAAoC;QAEpC3C,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,gBAAA,UAA+B;QAE/BZ,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,gBAAA,WAAgC;QAE9BZ,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,gBAAA,KAAA+B,SAAA,CAAoC;QAGpC3C,EAAA,CAAAI,SAAA,GAAoC;QAApCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,gBAAA,KAAA+B,SAAA,CAAoC;QAMtC3C,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAlG,YAAA,KAAA2B,SAAA,CAAgC;QAEhC3C,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAlG,YAAA,UAA2B;QAE3BhB,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAlG,YAAA,WAA4B;QAE1BhB,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAlG,YAAA,KAAA2B,SAAA,CAAgC;QAGhC3C,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAlG,YAAA,KAAA2B,SAAA,CAAgC;QAMlC3C,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAhG,YAAA,KAAAyB,SAAA,CAAgC;QAEhC3C,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAhG,YAAA,UAA2B;QAE3BlB,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAhG,YAAA,WAA4B;QAE1BlB,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAhG,YAAA,KAAAyB,SAAA,CAAgC;QAIhC3C,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAhG,YAAA,KAAAyB,SAAA,CAAgC;QAMlC3C,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA7F,mBAAA,KAAAsB,SAAA,CAAuC;QAEvC3C,EAAA,CAAAI,SAAA,GAAkC;QAAlCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA7F,mBAAA,UAAkC;QAElCrB,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA7F,mBAAA,WAAmC;QAGjCrB,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA7F,mBAAA,KAAAsB,SAAA,CAAuC;QAGvC3C,EAAA,CAAAI,SAAA,GAAuC;QAAvCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA7F,mBAAA,KAAAsB,SAAA,CAAuC;QAKzC3C,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA3F,eAAA,KAAAoB,SAAA,CAAmC;QAEnC3C,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA3F,eAAA,UAA8B;QAE9BvB,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA3F,eAAA,WAA+B;QAE7BvB,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA3F,eAAA,KAAAoB,SAAA,CAAmC;QAGnC3C,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAA3F,eAAA,KAAAoB,SAAA,CAAmC;QAKtC3C,EAAA,CAAAI,SAAA,GAGF;QAHEJ,EAAA,CAAAQ,UAAA,UAAA0G,GAAA,CAAApE,cAAA,mBAAAkH,QAAA,GAAA9C,GAAA,CAAApE,cAAA,CAAAS,GAAA,kCAAAyG,QAAA,CAAAhG,KAAA,OAAAkD,GAAA,CAAApE,cAAA,mBAAAkH,QAAA,GAAA9C,GAAA,CAAApE,cAAA,CAAAS,GAAA,sCAAAyG,QAAA,CAAAhG,KAAA,EAGF;QAUyBhE,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAQ,UAAA,aAAA0G,GAAA,CAAAnC,QAAA,YAAgC,UAAA/E,EAAA,CAAAM,WAAA;QAUjBN,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAiK,UAAA,CAAAjK,EAAA,CAAAa,eAAA,KAAAqJ,GAAA,EAA2B;QADzElK,EAAA,CAAAQ,UAAA,YAAA0G,GAAA,CAAAlE,oBAAA,CAAkC,oDAAAhD,EAAA,CAAAa,eAAA,KAAAsJ,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}