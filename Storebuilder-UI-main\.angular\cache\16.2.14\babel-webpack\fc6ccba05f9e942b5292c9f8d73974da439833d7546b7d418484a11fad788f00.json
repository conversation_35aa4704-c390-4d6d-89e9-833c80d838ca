{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { NavigationEnd } from \"@angular/router\";\nimport { filter } from 'rxjs';\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"../../../../shared/components/product-slider/product-slider.component\";\nimport * as i9 from \"../../../../shared/components/section/section.component\";\nimport * as i10 from \"../details/details.component\";\nimport * as i11 from \"../image-zoom/image-zoom.component\";\nimport * as i12 from \"../floating-panel/floating-panel.component\";\nfunction IndexComponent_ng_container_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"app-mtn-section\", 15);\n    i0.ɵɵelement(2, \"app-mtn-product-slider\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", \"Related Products\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"products\", ctx_r1.relatedProducts);\n  }\n}\nfunction IndexComponent_ng_container_4_app_floating_panel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-floating-panel\", 17);\n    i0.ɵɵlistener(\"onItemLike\", function IndexComponent_ng_container_4_app_floating_panel_9_Template_app_floating_panel_onItemLike_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"currency\", ctx_r2.currency)(\"product\", ctx_r2.productDetails)(\"variant\", ctx_r2.selectedVariance);\n  }\n}\nfunction IndexComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"app-image-zoom\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"app-details\", 11);\n    i0.ɵɵlistener(\"onChangeVariant\", function IndexComponent_ng_container_4_Template_app_details_onChangeVariant_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onvVarinaceChange($event));\n    })(\"onItemLike\", function IndexComponent_ng_container_4_Template_app_details_onItemLike_7_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onItemLiked($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, IndexComponent_ng_container_4_div_8_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, IndexComponent_ng_container_4_app_floating_panel_9_Template, 1, 3, \"app-floating-panel\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"product\", ctx_r0.productDetails)(\"variant\", ctx_r0.selectedVariance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"currency\", ctx_r0.currency)(\"product\", ctx_r0.productDetails)(\"selectedColor\", ctx_r0.selectedColor)(\"selectedSize\", ctx_r0.selectedSize)(\"selectedVariant\", ctx_r0.selectedVariance);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.relatedProducts.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showPanel);\n  }\n}\nexport class IndexComponent {\n  store;\n  activatedRoute;\n  productService;\n  messageService;\n  router;\n  translate;\n  ref;\n  loaderService;\n  $gaService;\n  permissionService;\n  platformId;\n  relatedProducts = [];\n  currency = {};\n  productDetails = {};\n  specProductId;\n  loading = false;\n  showPanel = false;\n  items = [];\n  home = {\n    icon: 'pi pi-home',\n    routerLink: '/'\n  };\n  categoryId;\n  category;\n  selectedColor;\n  selectedSize;\n  selectedVariance;\n  name = 'alam';\n  token;\n  channelId;\n  tagName = GaActionEnum;\n  userDetails;\n  isGoogleAnalytics = false;\n  constructor(store, activatedRoute, productService, messageService, router, translate, ref, loaderService, $gaService, permissionService, platformId) {\n    this.store = store;\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.router = router;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {});\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.specProductId = params.get('id');\n      this.channelId = params.get('channelId');\n    });\n  }\n  ngOnInit() {\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.loadData();\n    if (isPlatformBrowser(this.platformId)) {\n      window.addEventListener('scroll', this.onScrollEvent, true);\n      this.router.events.subscribe(evt => {\n        if (!(evt instanceof NavigationEnd)) {\n          return;\n        }\n        window.scrollTo(0, 0);\n      });\n    }\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  onScrollEvent = _event => {\n    if (isPlatformBrowser(this.platformId)) {\n      const sectionDetails = document.querySelector('#details');\n      const relatedProductsSection = document.querySelector('#relatedProducts');\n      const fromTop = sectionDetails ? sectionDetails.getBoundingClientRect().top < -110 : false;\n      const fromBottom = relatedProductsSection ? relatedProductsSection.getBoundingClientRect().top < 550 : false;\n      this.showPanel = fromTop && !fromBottom;\n    }\n  };\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    if (isPlatformBrowser(this.platformId)) {\n      let top = document.getElementById('top');\n      if (top !== null) {\n        top.scrollIntoView();\n        top = null;\n      }\n    }\n  }\n  onItemLiked(event) {\n    this.loadData();\n  }\n  loadData() {\n    this.loaderService.show();\n    this.productService.getProductDetails(this.specProductId, this.channelId).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        this.productDetails = res.data;\n        if (this.productDetails) {\n          this.userDetails = this.store.get('profile');\n          if (this.isGoogleAnalytics && this.permissionService.getTagFeature('VIEW_ITEM')) {\n            this.$gaService.pageView('/product', 'Product Detail: ' + this.specProductId);\n            this.$gaService.event(this.tagName.VIEW_ITEM, this.productDetails.categoryName, this.productDetails.name, 1, true, {\n              \"product_ID\": this.productDetails.id,\n              \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n            });\n          }\n          this.productDetails['specs'] = {};\n        }\n        this.productDetails?.productSpecs.forEach(spec => {\n          this.productDetails.specs[spec.name] = spec;\n        });\n        this.productDetails.productVariances.forEach(variance => {\n          variance['specs'] = [];\n          variance.varianceSpecs.forEach(spec => {\n            variance['specs'][spec.name] = spec;\n          });\n          if (variance.salePriceValue) {\n            variance.salePercent = 100 - variance.salePriceValue / variance.priceValue * 100;\n          }\n        });\n        let defaultVariant = this.productDetails.productVariances.find(variant => variant.isDefault);\n        if (defaultVariant) {\n          this.selectedVariance = defaultVariant;\n        } else {\n          let approvedVariant = this.productDetails.productVariances.find(variant => !variant.soldOut);\n          if (approvedVariant) {\n            this.selectedVariance = approvedVariant;\n          } else {\n            this.selectedVariance = this.productDetails.productVariances[0];\n          }\n        }\n        // color picker implementation\n        this.selectedColor = this.selectedVariance.color;\n        this.selectedVariance.varianceSpecs.forEach(variantSpec => {\n          if (variantSpec.name == 'Size') {\n            this.selectedSize = variantSpec.value;\n          }\n        });\n        this.assignBreadCrumbsData();\n        this.ref.detectChanges();\n        this.ref.markForCheck();\n        this.loading = false;\n      },\n      error: err => {\n        this.loaderService.hide();\n        console.error(err);\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n        this.loading = false;\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    this.store.subscription('categories').subscribe({\n      next: res => {\n        res.forEach(element => {\n          if (element.id == this.categoryId) {\n            this.category = element;\n          }\n        });\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  onvVarinaceChange(variance) {\n    this.selectedVariance = variance;\n  }\n  assignBreadCrumbsData() {\n    let idsArray = this.productDetails?.categoryIds?.split(\"->\").map(Number);\n    let nameArray = this.productDetails?.categoryPath?.split(\"->\").map(String);\n    let breadCrumbs = [];\n    if (idsArray?.length === nameArray?.length) {\n      idsArray?.map((e, i) => {\n        breadCrumbs.push({\n          routerLink: '/category/' + e.toString(),\n          label: nameArray[i]\n        });\n      });\n      this.items = breadCrumbs;\n    }\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"old-product-index\"], [\"id\", \"top\", 1, \"product-details\"], [1, \"breadcrumb\"], [1, \"col-12\", 3, \"home\", \"model\"], [4, \"ngIf\"], [1, \"content-container\", \"my-3\"], [1, \"grid\", \"pt-0\"], [1, \"col-12\", \"col-md-6\", \"col-lg-7\"], [1, \"images\"], [3, \"product\", \"variant\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\"], [\"id\", \"details\", 3, \"currency\", \"product\", \"selectedColor\", \"selectedSize\", \"selectedVariant\", \"onChangeVariant\", \"onItemLike\"], [\"class\", \"my-5\", 4, \"ngIf\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\", 4, \"ngIf\"], [1, \"my-5\"], [\"id\", \"relatedProducts\", 3, \"title\"], [3, \"products\"], [3, \"currency\", \"product\", \"variant\", \"onItemLike\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"p-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, IndexComponent_ng_container_4_Template, 10, 9, \"ng-container\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"home\", ctx.home)(\"model\", ctx.items);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.productDetails == null ? null : ctx.productDetails.id);\n      }\n    },\n    dependencies: [i6.NgIf, i7.Breadcrumb, i8.ProductSliderComponent, i9.SectionComponent, i10.DetailsComponent, i11.ImageZoomComponent, i12.FloatingPanelComponent],\n    styles: [\".old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  position: relative;\\n  margin-top: 93px;\\n}\\n.old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n  min-height: 50vh;\\n}\\n@media screen and (max-width: 320px) {\\n  .old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 140px;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 0rem !important;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .pt-6[_ngcontent-%COMP%] {\\n    padding-top: 0rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) and (min-width: 325px) {\\n  .old-product-index[_ngcontent-%COMP%]   .h-d-1[_ngcontent-%COMP%] {\\n    height: 300px !important;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    margin-top: 155px;\\n  }\\n  .old-product-index[_ngcontent-%COMP%]   .my-3[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n    margin-bottom: 1rem !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .old-product-index[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n    min-height: 100% !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcHJvZHVjdC1kZXRhaWxzL2NvbXBvbmVudHMvaW5kZXgvaW5kZXguY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0VBRUEsZ0JBQUE7QUFESjtBQUdJO0VBRUUsZ0JBQUE7QUFGTjtBQU1FO0VBQ0U7SUFDRSxpQkFBQTtFQUpKO0VBT0U7SUFDRSwyQkFBQTtJQUNBLDhCQUFBO0VBTEo7RUFRRTtJQUNFLDRCQUFBO0VBTko7QUFDRjtBQVVFO0VBQ0U7SUFDRSx3QkFBQTtFQVJKO0VBV0U7SUFDRSxpQkFBQTtFQVRKO0VBWUU7SUFDRSwyQkFBQTtJQUNBLDhCQUFBO0VBVko7QUFDRjtBQWFFO0VBQ0U7SUFDRSwyQkFBQTtFQVhKO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIub2xkLXByb2R1Y3QtaW5kZXgge1xyXG4gIC5wcm9kdWN0LWRldGFpbHMge1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgbWFyZ2luLXRvcDogOTNweDtcclxuXHJcbiAgICAuaW1hZ2VzIHtcclxuXHJcbiAgICAgIG1pbi1oZWlnaHQ6IDUwdmg7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBAbWVkaWEgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiAzMjBweCkge1xyXG4gICAgLnByb2R1Y3QtZGV0YWlscyB7XHJcbiAgICAgIG1hcmdpbi10b3A6IDE0MHB4O1xyXG4gICAgfVxyXG5cclxuICAgIC5teS0zIHtcclxuICAgICAgbWFyZ2luLXRvcDogMHJlbSAhaW1wb3J0YW50O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwcmVtICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLnB0LTYge1xyXG4gICAgICBwYWRkaW5nLXRvcDogMHJlbSAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICB9XHJcblxyXG4gIEBtZWRpYSBzY3JlZW4gYW5kIChtYXgtd2lkdGg6IDc2OHB4KSBhbmQgKG1pbi13aWR0aDogMzI1cHgpIHtcclxuICAgIC5oLWQtMSB7XHJcbiAgICAgIGhlaWdodDogMzAwcHggIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAucHJvZHVjdC1kZXRhaWxzIHtcclxuICAgICAgbWFyZ2luLXRvcDogMTU1cHg7XHJcbiAgICB9XHJcblxyXG4gICAgLm15LTMge1xyXG4gICAgICBtYXJnaW4tdG9wOiAwcmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDAxcmVtICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBAbWVkaWEgc2NyZWVuIGFuZCAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgLmltYWdlcyB7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMCUgIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "NavigationEnd", "filter", "GaActionEnum", "isPlatformBrowser", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "relatedProducts", "ɵɵlistener", "IndexComponent_ng_container_4_app_floating_panel_9_Template_app_floating_panel_onItemLike_0_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onItemLiked", "ctx_r2", "currency", "productDetails", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "IndexComponent_ng_container_4_Template_app_details_onChangeVariant_7_listener", "_r6", "ctx_r5", "onvVarinaceChange", "IndexComponent_ng_container_4_Template_app_details_onItemLike_7_listener", "ctx_r7", "ɵɵtemplate", "IndexComponent_ng_container_4_div_8_Template", "IndexComponent_ng_container_4_app_floating_panel_9_Template", "ɵɵelementContainerEnd", "ctx_r0", "selectedColor", "selectedSize", "length", "showPanel", "IndexComponent", "store", "activatedRoute", "productService", "messageService", "router", "translate", "ref", "loaderService", "$gaService", "permissionService", "platformId", "specProductId", "loading", "items", "home", "icon", "routerLink", "categoryId", "category", "name", "token", "channelId", "tagName", "userDetails", "isGoogleAnalytics", "constructor", "events", "pipe", "event", "subscribe", "paramMap", "params", "get", "ngOnInit", "hasPermission", "loadData", "window", "addEventListener", "onScrollEvent", "evt", "scrollTo", "_event", "sectionDetails", "document", "querySelector", "relatedProductsSection", "fromTop", "getBoundingClientRect", "top", "fromBottom", "ngAfterViewInit", "setTimeout", "subscription", "next", "res", "getElementById", "scrollIntoView", "show", "getProductDetails", "hide", "data", "getTagFeature", "pageView", "VIEW_ITEM", "categoryName", "id", "mobileNumber", "productSpecs", "for<PERSON>ach", "spec", "specs", "productVariances", "variance", "varianceSpecs", "salePriceValue", "salePercent", "priceValue", "defaultVariant", "find", "variant", "isDefault", "approvedVariant", "soldOut", "color", "variantSpec", "value", "assignBreadCrumbsData", "detectChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "err", "console", "add", "severity", "summary", "instant", "detail", "message", "complete", "element", "idsArray", "categoryIds", "split", "map", "Number", "nameArray", "categoryPath", "String", "breadCrumbs", "e", "i", "push", "toString", "label", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "ActivatedRoute", "ProductService", "i3", "MessageService", "Router", "i4", "TranslateService", "ChangeDetectorRef", "LoaderService", "i5", "GoogleAnalyticsService", "PermissionService", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_container_4_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\index\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\product-details\\components\\index\\index.component.html"], "sourcesContent": ["import {AfterViewInit, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, Router } from \"@angular/router\";\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { filter } from 'rxjs';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\nimport { Product, Currency, Category } from \"@core/interface\";\r\n\r\nimport {\r\n  LoaderService, PermissionService,\r\n  ProductService,\r\n  StoreService\r\n} from \"@core/services\";\r\nimport {GoogleAnalyticsService,GaActionEnum} from \"ngx-google-analytics\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\n\r\n@Component({\r\n  selector: 'app-index',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexComponent implements OnInit, AfterViewInit {\r\n  relatedProducts: Array<Product> = [];\r\n  currency: Currency = {} as Currency;\r\n\r\n  productDetails: any = {};\r\n  specProductId: any;\r\n  loading: boolean = false;\r\n  showPanel: boolean = false;\r\n  items: MenuItem[] = [];\r\n  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };\r\n  categoryId: any;\r\n  category!: Category;\r\n  selectedColor: any;\r\n  selectedSize: any;\r\n  selectedVariance: any;\r\n  name = 'alam';\r\n  token: any;\r\n  channelId: any;\r\n  tagName:any=GaActionEnum;\r\n  userDetails: any;\r\n  isGoogleAnalytics: boolean = false;\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private productService: ProductService,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private translate: TranslateService,\r\n    private ref: ChangeDetectorRef,\r\n    private loaderService: LoaderService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    private permissionService: PermissionService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n  ) {\r\n\r\n\r\n    router.events\r\n      .pipe(filter(event => event instanceof NavigationEnd))\r\n      .subscribe((event: any) => {\r\n\r\n\r\n      });\r\n    this.activatedRoute.paramMap.subscribe(params => {\r\n      this.specProductId = params.get('id');\r\n      this.channelId = params.get('channelId');\r\n\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n    this.loadData();\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.addEventListener('scroll', this.onScrollEvent, true);\r\n      this.router.events.subscribe((evt) => {\r\n        if (!(evt instanceof NavigationEnd)) {\r\n          return;\r\n        }\r\n        window.scrollTo(0, 0);\r\n      });\r\n    }\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n  }\r\n\r\n\r\n  onScrollEvent = (_event: any): void => {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const sectionDetails = document.querySelector('#details');\r\n      const relatedProductsSection = document.querySelector('#relatedProducts');\r\n      const fromTop = sectionDetails ? sectionDetails.getBoundingClientRect().top < -110 : false;\r\n      const fromBottom = relatedProductsSection ? relatedProductsSection.getBoundingClientRect().top < 550 : false;\r\n      this.showPanel = fromTop && !fromBottom;\r\n    }\r\n\r\n  };\r\n\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency')\r\n        .subscribe({\r\n          next: (res) => this.currency = res\r\n        });\r\n    }, 10);\r\n\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      let top = document.getElementById('top');\r\n      if (top !== null) {\r\n        top.scrollIntoView();\r\n        top = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  onItemLiked(event: boolean) {\r\n    this.loadData();\r\n  }\r\n\r\n  loadData(): void {\r\n\r\n    this.loaderService.show();\r\n\r\n\r\n    this.productService.getProductDetails(this.specProductId,this.channelId)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.loaderService.hide();\r\n          this.productDetails = res.data;\r\n          if(this.productDetails){\r\n            this.userDetails = this.store.get('profile');\r\n            if( this.isGoogleAnalytics &&  this.permissionService.getTagFeature('VIEW_ITEM')){\r\n              this.$gaService.pageView('/product', 'Product Detail: ' + this.specProductId);\r\n              this.$gaService.event(this.tagName.VIEW_ITEM, this.productDetails.categoryName,this.productDetails.name,1,true,{\"product_ID\":this.productDetails.id,\"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated'});\r\n            }\r\n\r\n            this.productDetails['specs'] = {}\r\n          }\r\n          this.productDetails?.productSpecs.forEach((spec: any) => {\r\n            this.productDetails.specs[spec.name] = spec;\r\n          });\r\n          this.productDetails.productVariances.forEach((variance: any) => {\r\n            variance['specs'] = [];\r\n            variance.varianceSpecs.forEach((spec: any) => {\r\n              variance['specs'][spec.name] = spec;\r\n            });\r\n            if (variance.salePriceValue) {\r\n              variance.salePercent = 100 - (variance.salePriceValue / variance.priceValue * 100);\r\n            }\r\n          })\r\n          let defaultVariant = this.productDetails.productVariances.find((variant: any) => variant.isDefault)\r\n          if (defaultVariant) {\r\n            this.selectedVariance = defaultVariant;\r\n\r\n          }\r\n          else {\r\n            let approvedVariant = this.productDetails.productVariances.find((variant: any) => !variant.soldOut)\r\n            if (approvedVariant) {\r\n              this.selectedVariance = approvedVariant;\r\n\r\n            } else {\r\n\r\n              this.selectedVariance = this.productDetails.productVariances[0];\r\n            }\r\n          }\r\n          // color picker implementation\r\n          this.selectedColor = this.selectedVariance.color\r\n          this.selectedVariance.varianceSpecs.forEach((variantSpec: any) => {\r\n            if (variantSpec.name == 'Size') {\r\n              this.selectedSize = variantSpec.value\r\n            }\r\n          })\r\n          this.assignBreadCrumbsData();\r\n          this.ref.detectChanges();\r\n          this.ref.markForCheck();\r\n\r\n\r\n          this.loading = false;\r\n\r\n        },\r\n        error: (err: any) => {\r\n          this.loaderService.hide();\r\n          console.error(err);\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: this.translate.instant('ErrorMessages.fetchError'),\r\n            detail: err.message\r\n          });\r\n          this.loading = false;\r\n        },\r\n        complete: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n    this.store.subscription('categories')\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          res.forEach((element: Category) => {\r\n            if (element.id == this.categoryId) {\r\n              this.category = element;\r\n            }\r\n          });\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n        }\r\n      });\r\n\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n  }\r\n\r\n\r\n  onvVarinaceChange(variance: any) {\r\n\r\n    this.selectedVariance = variance;\r\n  }\r\n\r\n  assignBreadCrumbsData() {\r\n\r\n    let idsArray = this.productDetails?.categoryIds?.split(\"->\").map(Number);\r\n    let nameArray = this.productDetails?.categoryPath?.split(\"->\").map(String);\r\n    let breadCrumbs: any = [];\r\n    if (idsArray?.length === nameArray?.length) {\r\n      idsArray?.map((e: any, i: any) => {\r\n        breadCrumbs.push({ routerLink: '/category/' + e.toString(), label: nameArray[i] })\r\n      })\r\n\r\n      this.items = breadCrumbs\r\n    }\r\n  }\r\n\r\n}\r\n", "<div class=\"old-product-index\">\r\n<section class=\"product-details\" id=\"top\">\r\n\r\n  <div class=\"breadcrumb\">\r\n    <p-breadcrumb [home]=\"home\" [model]=\"items\" class=\"col-12\"></p-breadcrumb>\r\n  </div>\r\n\r\n  <ng-container *ngIf=\"productDetails?.id\">\r\n    <div class=\"content-container my-3\">\r\n      <div class=\"grid pt-0\">\r\n        <div class=\"col-12 col-md-6 col-lg-7\">\r\n          <div class=\"images\">\r\n            <app-image-zoom [product]=\"productDetails\" [variant]=\"selectedVariance\"></app-image-zoom>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12 col-md-8 col-lg-6\">\r\n          <app-details (onChangeVariant)=\"onvVarinaceChange($event)\" (onItemLike)=\"onItemLiked($event)\"\r\n            [currency]=\"currency\" [product]=\"productDetails\" [selectedColor]=\"selectedColor\"\r\n            [selectedSize]=\"selectedSize\" [selectedVariant]=\"selectedVariance\" id=\"details\"></app-details>\r\n        </div>\r\n      </div>\r\n      <div *ngIf=\"relatedProducts.length\" class=\"my-5\">\r\n        <app-mtn-section [title]=\"'Related Products'\" id=\"relatedProducts\">\r\n          <app-mtn-product-slider [products]=\"relatedProducts\"></app-mtn-product-slider>\r\n        </app-mtn-section>\r\n      </div>\r\n    </div>\r\n\r\n    <app-floating-panel (onItemLike)=\"onItemLiked($event)\" *ngIf=\"showPanel\" [currency]=\"currency\"\r\n      [product]=\"productDetails\" [variant]=\"selectedVariance\"></app-floating-panel>\r\n  </ng-container>\r\n</section>\r\n</div>\r\n"], "mappings": "AAAA,SAAqEA,WAAW,QAAO,eAAe;AACtG,SAAyBC,aAAa,QAAgB,iBAAiB;AAEvE,SAASC,MAAM,QAAQ,MAAM;AAU7B,SAA+BC,YAAY,QAAO,sBAAsB;AACxE,SAAQC,iBAAiB,QAAO,iBAAiB;;;;;;;;;;;;;;;;ICO3CC,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAE,SAAA,iCAA8E;IAChFF,EAAA,CAAAG,YAAA,EAAkB;;;;IAFDH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,6BAA4B;IACnBL,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAC,eAAA,CAA4B;;;;;;IAK1DP,EAAA,CAAAC,cAAA,6BAC0D;IADtCD,EAAA,CAAAQ,UAAA,wBAAAC,qGAAAC,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAcd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IACIV,EAAA,CAAAG,YAAA,EAAqB;;;;IADNH,EAAA,CAAAK,UAAA,aAAAY,MAAA,CAAAC,QAAA,CAAqB,YAAAD,MAAA,CAAAE,cAAA,aAAAF,MAAA,CAAAG,gBAAA;;;;;;IArBhGpB,EAAA,CAAAqB,uBAAA,GAAyC;IACvCrB,EAAA,CAAAC,cAAA,aAAoC;IAI5BD,EAAA,CAAAE,SAAA,wBAAyF;IAC3FF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAsC;IACvBD,EAAA,CAAAQ,UAAA,6BAAAc,8EAAAZ,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAc,aAAA;MAAA,OAAmBd,EAAA,CAAAe,WAAA,CAAAS,MAAA,CAAAC,iBAAA,CAAAf,MAAA,CAAyB;IAAA,EAAC,wBAAAgB,yEAAAhB,MAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAY,GAAA;MAAA,MAAAI,MAAA,GAAA3B,EAAA,CAAAc,aAAA;MAAA,OAAed,EAAA,CAAAe,WAAA,CAAAY,MAAA,CAAAX,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAlC;IAEwBV,EAAA,CAAAG,YAAA,EAAc;IAGpGH,EAAA,CAAA4B,UAAA,IAAAC,4CAAA,kBAIM;IACR7B,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA4B,UAAA,IAAAE,2DAAA,iCAC+E;IACjF9B,EAAA,CAAA+B,qBAAA,EAAe;;;;IAlBW/B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,UAAA,YAAA2B,MAAA,CAAAb,cAAA,CAA0B,YAAAa,MAAA,CAAAZ,gBAAA;IAK1CpB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAA2B,MAAA,CAAAd,QAAA,CAAqB,YAAAc,MAAA,CAAAb,cAAA,mBAAAa,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAE,YAAA,qBAAAF,MAAA,CAAAZ,gBAAA;IAIrBpB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,SAAA2B,MAAA,CAAAzB,eAAA,CAAA4B,MAAA,CAA4B;IAOoBnC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,SAAA2B,MAAA,CAAAI,SAAA,CAAe;;;ADP3E,OAAM,MAAOC,cAAc;EAuBfC,KAAA;EACAC,cAAA;EACAC,cAAA;EACAC,cAAA;EACAC,MAAA;EACAC,SAAA;EACAC,GAAA;EACAC,aAAA;EACAC,UAAA;EACAC,iBAAA;EACqBC,UAAA;EAhC/BzC,eAAe,GAAmB,EAAE;EACpCW,QAAQ,GAAa,EAAc;EAEnCC,cAAc,GAAQ,EAAE;EACxB8B,aAAa;EACbC,OAAO,GAAY,KAAK;EACxBd,SAAS,GAAY,KAAK;EAC1Be,KAAK,GAAe,EAAE;EACtBC,IAAI,GAAa;IAAEC,IAAI,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAG,CAAE;EACxDC,UAAU;EACVC,QAAQ;EACRvB,aAAa;EACbC,YAAY;EACZd,gBAAgB;EAChBqC,IAAI,GAAG,MAAM;EACbC,KAAK;EACLC,SAAS;EACTC,OAAO,GAAK9D,YAAY;EACxB+D,WAAW;EACXC,iBAAiB,GAAY,KAAK;EAElCC,YACUzB,KAAmB,EACnBC,cAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B,EAC9BC,MAAc,EACdC,SAA2B,EAC3BC,GAAsB,EACtBC,aAA4B,EAC5BC,UAAkC,EAClCC,iBAAoC,EACfC,UAAe;IAVpC,KAAAV,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACI,KAAAC,UAAU,GAAVA,UAAU;IAIvCN,MAAM,CAACsB,MAAM,CACVC,IAAI,CAACpE,MAAM,CAACqE,KAAK,IAAIA,KAAK,YAAYtE,aAAa,CAAC,CAAC,CACrDuE,SAAS,CAAED,KAAU,IAAI,CAG1B,CAAC,CAAC;IACJ,IAAI,CAAC3B,cAAc,CAAC6B,QAAQ,CAACD,SAAS,CAACE,MAAM,IAAG;MAC9C,IAAI,CAACpB,aAAa,GAAGoB,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MACrC,IAAI,CAACX,SAAS,GAAGU,MAAM,CAACC,GAAG,CAAC,WAAW,CAAC;IAE1C,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACf,iBAAiB,CAACyB,aAAa,CAAC,kBAAkB,CAAC;IACjF,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI1E,iBAAiB,CAAC,IAAI,CAACiD,UAAU,CAAC,EAAE;MACtC0B,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAAC;MAC3D,IAAI,CAAClC,MAAM,CAACsB,MAAM,CAACG,SAAS,CAAEU,GAAG,IAAI;QACnC,IAAI,EAAEA,GAAG,YAAYjF,aAAa,CAAC,EAAE;UACnC;;QAEF8E,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC;;IAGJ,IAAI,CAAC1B,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;EACrD;EAGAsB,aAAa,GAAIG,MAAW,IAAU;IACpC,IAAIhF,iBAAiB,CAAC,IAAI,CAACiD,UAAU,CAAC,EAAE;MACtC,MAAMgC,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACzD,MAAMC,sBAAsB,GAAGF,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;MACzE,MAAME,OAAO,GAAGJ,cAAc,GAAGA,cAAc,CAACK,qBAAqB,EAAE,CAACC,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK;MAC1F,MAAMC,UAAU,GAAGJ,sBAAsB,GAAGA,sBAAsB,CAACE,qBAAqB,EAAE,CAACC,GAAG,GAAG,GAAG,GAAG,KAAK;MAC5G,IAAI,CAAClD,SAAS,GAAGgD,OAAO,IAAI,CAACG,UAAU;;EAG3C,CAAC;EAGDC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAEd,IAAI,CAACnD,KAAK,CAACoD,YAAY,CAAC,UAAU,CAAC,CAChCvB,SAAS,CAAC;QACTwB,IAAI,EAAGC,GAAG,IAAK,IAAI,CAAC1E,QAAQ,GAAG0E;OAChC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI7F,iBAAiB,CAAC,IAAI,CAACiD,UAAU,CAAC,EAAE;MACtC,IAAIsC,GAAG,GAAGL,QAAQ,CAACY,cAAc,CAAC,KAAK,CAAC;MACxC,IAAIP,GAAG,KAAK,IAAI,EAAE;QAChBA,GAAG,CAACQ,cAAc,EAAE;QACpBR,GAAG,GAAG,IAAI;;;EAGhB;EAEAtE,WAAWA,CAACkD,KAAc;IACxB,IAAI,CAACO,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IAEN,IAAI,CAAC5B,aAAa,CAACkD,IAAI,EAAE;IAGzB,IAAI,CAACvD,cAAc,CAACwD,iBAAiB,CAAC,IAAI,CAAC/C,aAAa,EAAC,IAAI,CAACU,SAAS,CAAC,CACrEQ,SAAS,CAAC;MACTwB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC/C,aAAa,CAACoD,IAAI,EAAE;QACzB,IAAI,CAAC9E,cAAc,GAAGyE,GAAG,CAACM,IAAI;QAC9B,IAAG,IAAI,CAAC/E,cAAc,EAAC;UACrB,IAAI,CAAC0C,WAAW,GAAG,IAAI,CAACvB,KAAK,CAACgC,GAAG,CAAC,SAAS,CAAC;UAC5C,IAAI,IAAI,CAACR,iBAAiB,IAAK,IAAI,CAACf,iBAAiB,CAACoD,aAAa,CAAC,WAAW,CAAC,EAAC;YAC/E,IAAI,CAACrD,UAAU,CAACsD,QAAQ,CAAC,UAAU,EAAE,kBAAkB,GAAG,IAAI,CAACnD,aAAa,CAAC;YAC7E,IAAI,CAACH,UAAU,CAACoB,KAAK,CAAC,IAAI,CAACN,OAAO,CAACyC,SAAS,EAAE,IAAI,CAAClF,cAAc,CAACmF,YAAY,EAAC,IAAI,CAACnF,cAAc,CAACsC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC;cAAC,YAAY,EAAC,IAAI,CAACtC,cAAc,CAACoF,EAAE;cAAC,SAAS,EAAC,IAAI,CAAC1C,WAAW,GAAC,IAAI,CAACA,WAAW,CAAC2C,YAAY,GAAC;YAAkB,CAAC,CAAC;;UAGnO,IAAI,CAACrF,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE;;QAEnC,IAAI,CAACA,cAAc,EAAEsF,YAAY,CAACC,OAAO,CAAEC,IAAS,IAAI;UACtD,IAAI,CAACxF,cAAc,CAACyF,KAAK,CAACD,IAAI,CAAClD,IAAI,CAAC,GAAGkD,IAAI;QAC7C,CAAC,CAAC;QACF,IAAI,CAACxF,cAAc,CAAC0F,gBAAgB,CAACH,OAAO,CAAEI,QAAa,IAAI;UAC7DA,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE;UACtBA,QAAQ,CAACC,aAAa,CAACL,OAAO,CAAEC,IAAS,IAAI;YAC3CG,QAAQ,CAAC,OAAO,CAAC,CAACH,IAAI,CAAClD,IAAI,CAAC,GAAGkD,IAAI;UACrC,CAAC,CAAC;UACF,IAAIG,QAAQ,CAACE,cAAc,EAAE;YAC3BF,QAAQ,CAACG,WAAW,GAAG,GAAG,GAAIH,QAAQ,CAACE,cAAc,GAAGF,QAAQ,CAACI,UAAU,GAAG,GAAI;;QAEtF,CAAC,CAAC;QACF,IAAIC,cAAc,GAAG,IAAI,CAAChG,cAAc,CAAC0F,gBAAgB,CAACO,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;QACnG,IAAIH,cAAc,EAAE;UAClB,IAAI,CAAC/F,gBAAgB,GAAG+F,cAAc;SAEvC,MACI;UACH,IAAII,eAAe,GAAG,IAAI,CAACpG,cAAc,CAAC0F,gBAAgB,CAACO,IAAI,CAAEC,OAAY,IAAK,CAACA,OAAO,CAACG,OAAO,CAAC;UACnG,IAAID,eAAe,EAAE;YACnB,IAAI,CAACnG,gBAAgB,GAAGmG,eAAe;WAExC,MAAM;YAEL,IAAI,CAACnG,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAAC0F,gBAAgB,CAAC,CAAC,CAAC;;;QAGnE;QACA,IAAI,CAAC5E,aAAa,GAAG,IAAI,CAACb,gBAAgB,CAACqG,KAAK;QAChD,IAAI,CAACrG,gBAAgB,CAAC2F,aAAa,CAACL,OAAO,CAAEgB,WAAgB,IAAI;UAC/D,IAAIA,WAAW,CAACjE,IAAI,IAAI,MAAM,EAAE;YAC9B,IAAI,CAACvB,YAAY,GAAGwF,WAAW,CAACC,KAAK;;QAEzC,CAAC,CAAC;QACF,IAAI,CAACC,qBAAqB,EAAE;QAC5B,IAAI,CAAChF,GAAG,CAACiF,aAAa,EAAE;QACxB,IAAI,CAACjF,GAAG,CAACkF,YAAY,EAAE;QAGvB,IAAI,CAAC5E,OAAO,GAAG,KAAK;MAEtB,CAAC;MACD6E,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACnF,aAAa,CAACoD,IAAI,EAAE;QACzBgC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;QAClB,IAAI,CAACvF,cAAc,CAACyF,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,IAAI,CAACzF,SAAS,CAAC0F,OAAO,CAAC,0BAA0B,CAAC;UAC3DC,MAAM,EAAEN,GAAG,CAACO;SACb,CAAC;QACF,IAAI,CAACrF,OAAO,GAAG,KAAK;MACtB,CAAC;MACDsF,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC3F,aAAa,CAACoD,IAAI,EAAE;MAC3B;KACD,CAAC;IACJ,IAAI,CAAC3D,KAAK,CAACoD,YAAY,CAAC,YAAY,CAAC,CAClCvB,SAAS,CAAC;MACTwB,IAAI,EAAGC,GAAQ,IAAI;QACjBA,GAAG,CAACc,OAAO,CAAE+B,OAAiB,IAAI;UAChC,IAAIA,OAAO,CAAClC,EAAE,IAAI,IAAI,CAAChD,UAAU,EAAE;YACjC,IAAI,CAACC,QAAQ,GAAGiF,OAAO;;QAE3B,CAAC,CAAC;MACJ,CAAC;MACDV,KAAK,EAAGC,GAAQ,IAAI;QAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;IAGJ,IAAI,CAAC5E,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;EACrD;EAGA7B,iBAAiBA,CAACqF,QAAa;IAE7B,IAAI,CAAC1F,gBAAgB,GAAG0F,QAAQ;EAClC;EAEAc,qBAAqBA,CAAA;IAEnB,IAAIc,QAAQ,GAAG,IAAI,CAACvH,cAAc,EAAEwH,WAAW,EAAEC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IACxE,IAAIC,SAAS,GAAG,IAAI,CAAC5H,cAAc,EAAE6H,YAAY,EAAEJ,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACI,MAAM,CAAC;IAC1E,IAAIC,WAAW,GAAQ,EAAE;IACzB,IAAIR,QAAQ,EAAEvG,MAAM,KAAK4G,SAAS,EAAE5G,MAAM,EAAE;MAC1CuG,QAAQ,EAAEG,GAAG,CAAC,CAACM,CAAM,EAAEC,CAAM,KAAI;QAC/BF,WAAW,CAACG,IAAI,CAAC;UAAE/F,UAAU,EAAE,YAAY,GAAG6F,CAAC,CAACG,QAAQ,EAAE;UAAEC,KAAK,EAAER,SAAS,CAACK,CAAC;QAAC,CAAE,CAAC;MACpF,CAAC,CAAC;MAEF,IAAI,CAACjG,KAAK,GAAG+F,WAAW;;EAE5B;;qBApNW7G,cAAc,EAAArC,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5J,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAA7J,EAAA,CAAAwJ,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA/J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAK,MAAA,GAAAhK,EAAA,CAAAwJ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAlK,EAAA,CAAAwJ,iBAAA,CAAAxJ,EAAA,CAAAmK,iBAAA,GAAAnK,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAW,aAAA,GAAApK,EAAA,CAAAwJ,iBAAA,CAAAa,EAAA,CAAAC,sBAAA,GAAAtK,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAc,iBAAA,GAAAvK,EAAA,CAAAwJ,iBAAA,CAiCf7J,WAAW;EAAA;;UAjCV0C,cAAc;IAAAmI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB3B9K,EAAA,CAAAC,cAAA,aAA+B;QAI3BD,EAAA,CAAAE,SAAA,sBAA0E;QAC5EF,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAA4B,UAAA,IAAAoJ,sCAAA,2BAuBe;QACjBhL,EAAA,CAAAG,YAAA,EAAU;;;QA3BQH,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAK,UAAA,SAAA0K,GAAA,CAAA3H,IAAA,CAAa,UAAA2H,GAAA,CAAA5H,KAAA;QAGdnD,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAK,UAAA,SAAA0K,GAAA,CAAA5J,cAAA,kBAAA4J,GAAA,CAAA5J,cAAA,CAAAoF,EAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}