form {
  input {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    margin: 0 0.8rem;
    background: #F5F5F5 !important;
    font-size: 30px;
    font-weight: 400;
    font-family: var(--medium-font) !important;
    border-radius: 8px;
    border: 1px solid transparent;
  }
}

/* Hidden input for SMS auto-fill */
.hidden-otp-input {
  position: absolute !important;
  left: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  opacity: 0 !important;
  pointer-events: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.otp-inputs {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.otp-digit-input {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  margin: 0 0.8rem;
  background: #F5F5F5 !important;
  font-size: 30px;
  font-weight: 400;
  font-family: var(--medium-font) !important;
  border-radius: 8px;
  border: 1px solid transparent;
}

.form-container {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.otp-heading {
  font-size: 20px;
  font-weight: 500;
  font-family: var(--medium-font) !important;
  color: #212121;
  line-height: 100%;
}

.sent-otp {
  font-size: 14px;
  font-weight: 400;
  color: #443F3F;
  font-family: var(--regular-font) !important;
  margin-top: 0.5rem;
}

.count {
  text-align: center;
  font-size: small;
  margin: 1rem 0;
}

.resend {
  cursor: pointer;
  font-size: 14px;
  font-weight: 800;
  font-family: var(--medium-font) !important;
}

.time-left {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--regular-font) !important;
  color: #204E6E;
}

@media screen and (max-width: 768px) {
  form {
    input {
      margin: 0 0.4rem !important;
      width: 50px;
      height: 50px;
    }
  }

  .otp-digit-input {
    margin: 0 0.4rem !important;
    width: 50px;
    height: 50px;
  }

  .otp-heading {
    font-size: 18px;
  }

  .sent-otp {
    font-size: 12px;
  }

  .time-left, .resend {
    font-size: 12px;
  }
}