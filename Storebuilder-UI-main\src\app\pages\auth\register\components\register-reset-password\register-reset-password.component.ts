import {Component, ElementRef, Inject, OnInit, PLATFORM_ID} from '@angular/core';
import {Router} from '@angular/router';
import {MessageService, PrimeNGConfig} from 'primeng/api';
import {DOCUMENT, isPlatformBrowser} from '@angular/common';
import {TranslateService} from '@ngx-translate/core';
import {DomSanitizer} from '@angular/platform-browser';
import {CountryISO, PhoneNumberFormat, SearchCountryField} from "ngx-intl-tel-input-gg";
import {FormBuilder, FormGroup, Validators,} from '@angular/forms';

import {AppDataService, ContactUsService, PermissionService, RegisterService, UserService, CustomGAService} from '@core/services';
import {GetAllAboutUs, UserConsent} from '@core/interface';
import {passwordValidator} from "@shared/validators/password.validator";
import {GaActionEnum, GoogleAnalyticsService} from "ngx-google-analytics";
import {UserConsentType} from "@core/enums/user";
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationDialogComponent } from '@shared/components/confirmation-dialog/confirmation-dialog.component';
import { first, tap } from 'rxjs';
import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';
import { GTMService } from '@core/services/gtm.service';

@Component({

  selector: 'app-register-reset-password',
  templateUrl: './register-reset-password.component.html',
  styleUrls: ['./register-reset-password.component.scss'],
})
export class RegisterResetPasswordComponent implements OnInit {
  aboutUsDetails: GetAllAboutUs = {} as GetAllAboutUs;
  signUp!: FormGroup;
  pageId: number = 5;
  title: string = '';
  phoneNumberMask = '000-000-000-000';
  loading: boolean = false;
  password: string = '';
  confirmPassword: string = '';
  emailAddress: string = '';
  lastName: string = '';
  firstName: string = '';
  mobileNumber: string = '';
  otpCode: string = '';
  termsConditions: boolean = false;
  hasUpperChar!: boolean;
  hasAtleastOneNumber!: boolean;
  hasLowerChar!: boolean;
  passwordMatched: boolean;
  hasMinimum8Chars!: boolean;
  hasSpecialChars!: boolean;
  passwordIsValid: boolean = false;
  displayApprovedModal: boolean = false;
  displayTermsAndConditions: boolean = false;
  email: string | null = null;
  mask: string = '999-999-999-999';
  submitted!: boolean;
  firstNameFlag: boolean = false;
  emailReuiredValidation!: string | null;
  termsAndConditions: any;
  PhoneNumberFormat = PhoneNumberFormat;
  CustomCountryISO: any;
  SearchCountryField = SearchCountryField;
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];
  customPlaceHolder: any = '';
  phoneInputLength: number = 12;
  tagName:any=GaActionEnum;
  isGoogleAnalytics: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth:number = window.innerWidth;
  ref: DynamicDialogRef | undefined;
  isChecked:boolean=false;
  constructor(
    private messageService: MessageService,
    private fb: FormBuilder,
    private el: ElementRef,
    private translate: TranslateService,
    private router: Router,
    private otpService: RegisterService,
    private aboutUsService: ContactUsService,
    private domSanitizer: DomSanitizer,
    private config: PrimeNGConfig,
    private appDataService: AppDataService,
    private $gaService: GoogleAnalyticsService,
    private permissionService: PermissionService,
    private customGAService: CustomGAService,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: any,
    private userService: UserService,
    private dialogService: DialogService,
    private $gtmService:GTMService
  ) {
    let tenantId = localStorage.getItem('tenantId');
    if(tenantId && tenantId !== '') {
      if(tenantId == '1') {
        this.customPlaceHolder = 'XXXXXXXXX';
      } else if(tenantId == '2') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '3') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '4') {
        this.customPlaceHolder = 'XXXXXXXXXX';
      }
    }
  }

  get formcontrols() {
    return this.signUp.controls;
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.$gtmService.pushPageView('signUp','step 2')
    this.CustomCountryISO = localStorage.getItem("isoCode");
    this.emailReuiredValidation = localStorage.getItem('emailRequired');
    if(this.appDataService.configuration) {
      const emailRequired = this.appDataService.configuration.records.find(item => item.key === 'EmailRequired')
      if(emailRequired){
        this.emailReuiredValidation = emailRequired.value
      }
    }
    let phoneMask = localStorage.getItem('PhoneNumberMask');
    if (phoneMask)
      this.phoneNumberMask = phoneMask;
    let countryPhoneStorage = localStorage.getItem('countryPhone');
    let countryPhone = '256';
    if (countryPhoneStorage)
      countryPhone = countryPhoneStorage?.replace('+', '');
    this.getAboutUsDetails();
    this.formGroup();


    this.mobileNumber = history.state.mobile;
    if (history?.state?.mobile) {
      let patchedPhone = history?.state?.mobile.slice(countryPhone?.length)
      this.signUp.controls.phoneNumber.patchValue(patchedPhone)

    }


    this.otpCode = history.state.otp;


    if(this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')
      if(phoneLength) this.phoneInputLength = parseInt(phoneLength.value)
    }
  }
 
  formGroup() {
    if (this.emailReuiredValidation == 'true') {
      this.signUp = this.fb.group({

        firstName: ['', Validators.required],
        lastName: ['', Validators.required],
        emailAddress: ['', [Validators.email]],
        password: ['',[ Validators.required,passwordValidator()]],
        phoneNumber: [''],
        confirmPassword: ['', Validators.required],
        referralBy: ['',[Validators.maxLength(50)]],
      });
    } else if (this.emailReuiredValidation == 'false') {
      this.signUp = this.fb.group({

        firstName: ['', Validators.required],
        lastName: ['', Validators.required],
        emailAddress: ['', [Validators.email]],
        password: ['', [Validators.required,passwordValidator()]],
        phoneNumber: [''],
        confirmPassword: ['', Validators.required],
        referralBy: ['',[Validators.maxLength(50)]],
      });
    }
  }

  approveModal() {
    this.displayApprovedModal = true;

  }

  termsAndConditionsModal() {
    this.pageId = 5;
    this.getAboutUsDetails();
    this.aboutUsService.getShopAboutUs().subscribe({
      next: (res: any) => {
        let page = res.data?.records.filter(
          (x: any) => x.pageId == this.pageId
        );
        this.aboutUsDetails.title = page[0]?.title;
        this.aboutUsDetails.content = atob(page[0].content);
        this.displayTermsAndConditions = true;
      },
      error: (err: any) => {
      },
    });


  }
  reloadCurrentPage(pageId: number, title: string) {
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
      this.router.navigate(['/about-us/'], {
        queryParams: { pageId: pageId, title: title },
      })
    );
  }

  submit(isMobile?:boolean) {
    if(this.isGoogleAnalytics){
    this.$gaService.event(GaLocalActionEnum.CLICK_ON_COMPLETE_REGISTERATION, '', 'SIGN_UP_STEP_2 ', 1, true);
    }

    const mobileNumber = this.signUp.controls.phoneNumber.value;
    const password = this.signUp.controls['password'].value;

    if (!password) {
      this.handleError(this.translate.instant('ErrorMessages.mobileRequired'));
      return;
    }

    if (this.signUp.controls['emailAddress'].value && this.signUp.controls['emailAddress'].value.trim() !== '') {
      const isValidEmail = this.isValidEmailAddress(this.signUp.controls['emailAddress'].value);
      if (!isValidEmail) {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.pleaseEnterValidEmailAddress'),
        });
        return;
      } else {
        this.email = this.signUp.controls['emailAddress'].value.trim();
      }
    }

    const userName = mobileNumber.e164Number?.slice(1); 
    const name = `${this.signUp.controls['firstName'].value} ${this.signUp.controls['lastName'].value}`;

    const CountryPhoneKey = localStorage.getItem('CountryPhone');
    const countryPhone = CountryPhoneKey?.replace('+', '');

    // Extract the phone number properly from the form control
    const phoneNumberValue = this.signUp.controls['phoneNumber'].value;
    const phoneNumberWithoutCountryCode = phoneNumberValue?.e164Number ? 
      phoneNumberValue.e164Number.slice((countryPhone?.length || 3) + 1) : 
      phoneNumberValue;

    this.otpService.registerUser({
      UserName: !isMobile ? countryPhone + phoneNumberWithoutCountryCode : userName,
      Password: password,
      OTPCode: this.otpCode,
      Name: name,
      Email: this.email,
      number: !isMobile ? countryPhone + phoneNumberWithoutCountryCode : userName,
      ReferralBy: this.signUp.controls['referralBy'].value,
      IsSubscribed: this.isChecked ?? false,
    }).subscribe({
      next: (res: any) => {
        this.handleRegisterUserResponse(res, userName);
        const data: UserConsent = {
          consentType: UserConsentType.Cookie,
          sessionId: localStorage.getItem('consumer-consent-sessionId') || '',
          consent: true,
          userId: res?.data?.id
        }
        this.userService.updateUserConsent(data).subscribe({
          next: (res: any) => {
          }
        })
        if(this.isGoogleAnalytics &&  this.permissionService.getTagFeature('SIGN_UP')){
          this.$gaService.event(this.tagName.SIGN_UP, '','',1,true, {"user_ID":userName});
        }
      },
      error: (err: any) => this.handleError(err.message),
    });
  }

  private isValidEmailAddress(email: string): boolean {
    const regex = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
    return regex.test(email);
  }

  private handleRegisterUserResponse(res: any, userName: string): void {
    this.loading = false;
    this.displayApprovedModal = true;

    if (res.success ) {
      // Track successful signup event
      if(this.isGoogleAnalytics){
        this.customGAService.signUpEvent('phone', 'register_page');
      }
      
      if(this.isGoogleAnalytics &&  this.permissionService.getTagFeature('consumer_register')){
        this.$gaService.event('consumer_register', 'register', userName);
      }
        this.openSuccessDialog()
        this.handleSuccess();
        this.router.navigate(['/login']);

    } else {
      this.displayApprovedModal = false;
      this.handleError(res.message);
    }
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.remove('overlay');
    }
  }
  openSuccessDialog(){
    const isMobile= window.innerWidth <= 767;
    this.ref = this.dialogService.open(ConfirmationDialogComponent, {
      width: isMobile ? '90%' :'auto',
      closable: false,
      closeOnEscape: true,
      styleClass:'dialog-wrapper',
      position:'center',
      data: {
        successMsg: 'auth.registerPassword.registerSuccessMsg'
    }
    });
    this.ref.onClose.pipe(
      tap(() => this.router.navigate(['/login'])),
      first()
    ).subscribe();

  }

  private handleSuccess() {
    this.messageService.add({
      severity: 'success',
      summary: this.translate.instant('ResponseMessages.register'),
      detail: this.translate.instant(
        'ResponseMessages.registerSuccess'
      ),
    });
  }
  handleError(detail:string){
    this.loading = false;
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: detail,
    });
  }

  closeTermsModal() {
    this.displayTermsAndConditions = false;
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.classList.remove('overlay');
    }
  }

  checkPasswordPattern(password: any) {
    this.hasAtleastOneNumber = /\d+/.test(password);
    this.hasUpperChar = /[A-Z]+/.test(password);
    this.hasLowerChar = /[a-z]+/.test(password);
    this.hasMinimum8Chars = /.{10,}/.test(password);
    this.hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);


    if (this.signUp.get('password')?.value && this.signUp.get('confirmPassword')?.value) {
      if (this.signUp.get('password')?.value === this.signUp.get('confirmPassword')?.value) {
        this.passwordMatched = true;
      } else {

        this.passwordMatched = false;
      }

    } else {

      this.passwordMatched = false;
    }
    this.passwordIsValid =
      this.hasMinimum8Chars &&
      this.hasUpperChar &&
      this.hasLowerChar &&
      this.hasSpecialChars &&
      this.passwordMatched &&
      this.hasAtleastOneNumber;
    return (
      this.hasMinimum8Chars &&
      this.hasUpperChar &&
      this.hasLowerChar &&
      this.hasSpecialChars &&
      this.passwordMatched &&
      this.hasAtleastOneNumber
    );
  }


  onChangeConfirmPassword() {
    if (this.signUp.get('password')?.value && this.signUp.get('confirmPassword')?.value) {
      if (this.signUp.get('password')?.value === this.signUp.get('confirmPassword')?.value) {
        this.passwordMatched = true;
      } else {

        this.passwordMatched = false;
      }

    } else {

      this.passwordMatched = false;
    }
    this.passwordIsValid =
      this.hasMinimum8Chars &&
      this.hasUpperChar &&
      this.hasLowerChar &&
      this.hasSpecialChars &&
      this.passwordMatched &&
      this.hasAtleastOneNumber;
  }

  getAboutUsDetails(): void {
    this.aboutUsService.getShopAboutUs().subscribe({
      next: (res: any) => {
        let page = res.data?.records.filter(
          (x: any) => x.pageId == this.pageId
        );
        this.aboutUsDetails.title = page[0]?.title;
        this.aboutUsDetails.content = atob(page[0].content);
        this.termsAndConditions = atob(page[0].content);
      },
      error: (err: any) => {
      },
    });
  }



  onblur(value?: any) {
    this.isRequired('firstName');
    if (
      value !== 'cp' &&
      !this.hasMinimum8Chars &&
      !this.hasLowerChar &&
      !this.hasAtleastOneNumber &&
      !this.hasSpecialChars &&
      !this.hasUpperChar &&
      !this.passwordMatched
    ) {
      this.hasMinimum8Chars = false;
      this.hasLowerChar = false;
      this.hasAtleastOneNumber = false;
      this.hasSpecialChars = false;
      this.hasUpperChar = false;
      this.passwordMatched = false;
    }
    this.translate.get('primeng').subscribe((res) => {
      this.config.setTranslation(res);
    });
  }

  isRequired(fieldName: any) {
    if (

      this.formcontrols[fieldName]?.errors?.required
    ) {
      this.firstNameFlag = true;
    }
  }



  checkValidity() {
    if (this.signUp.valid && this.passwordIsValid) {
      return false;
    }
    return true;
  }

  private focusFirstInvalidField() {
    for (const key of Object.keys(this.formcontrols)) {
      if (this.formcontrols[key].invalid) {
        this.focusInputField(key);
        break;
      }
    }
  }

  private focusInputField(key: string) {
    const invalidControl = this.el.nativeElement.querySelector(
      '[formControlName="' + key + '"]'
    );
    invalidControl.focus();
  }

  onInputChange(event:any){
    if(event.target.value){
      const firstSpaceIndex = event.target.value.indexOf(' ');
      if (firstSpaceIndex ==0) {
        event.target.value = event.target.value.substring(0, firstSpaceIndex) + event.target.value.substring(firstSpaceIndex + 1);
      }

    }

  }
  


}
