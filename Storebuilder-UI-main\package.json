{"name": "momo-market", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-gzip": "ng build --aot && npx gzipper compress ./dist", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "format": "npx prettier --write \"src/**/*.{ts,js,html,scss,css,json}\"", "format:check": "npx prettier --check \"src/**/*.{ts,js,html,scss,css,json}\"", "format:staged": "npx prettier --write", "format:changed": "git diff --name-only --cached --diff-filter=ACM | grep -E '\\.(ts|js|html|scss|css|json)$' | xargs npx prettier --write", "format:all": "npm run format", "dev:ssr": "ng run momo-market:serve-ssr", "serve:ssr": "node dist/momo-market/server/main.js", "build:ssr": "ng build && ng run momo-market:server", "prerender": "ng run momo-market:prerender", "build:run:ssr": "npm run build:ssr && npm run serve:ssr", "prepare": "husky"}, "private": true, "dependencies": {"@angular-magic/ngx-gp-autocomplete": "^1.0.4", "@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/google-maps": "^18.2.14", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/platform-server": "^18.2.13", "@angular/router": "^18.2.13", "@angular/ssr": "^18.2.20", "@auth0/angular-jwt": "^5.0.2", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@googlemaps/js-api-loader": "^1.16.2", "@ng-select/ng-select": "^15.0.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.10.2", "@types/crypto-js": "^4.1.1", "@types/google.maps": "^3.53.5", "@types/leaflet": "^1.9.3", "@types/scriptjs": "^0.0.2", "@types/ws": "^8.5.4", "angular-google-tag-manager": "^1.9.0", "bootstrap": "^5.2.2", "crypto-js": "^4.1.1", "domino": "^2.1.6", "express": "^4.15.2", "google-libphonenumber": "^3.2.32", "intl-tel-input": "^18.1.7", "jquery": "^3.6.0", "jwt-decode": "^3.1.2", "leaflet": "^1.9.4", "leaflet-boundary-canvas": "^1.0.0", "libphonenumber-js": "^1.10.38", "ng-toggle-button": "^1.5.2", "ngx-bootstrap": "^11.0.2", "ngx-cookie-service": "^18.0.0", "ngx-drag-scroll": "^16.0.0", "ngx-google-analytics": "^14.0.1", "ngx-image-zoom": "^1.0.1", "ngx-intl-tel-input-gg": "^1.0.12", "ngx-mat-intl-tel-input": "^5.0.0", "ngx-owl-carousel-o": "^16.0.0", "ngx-sharebuttons": "^13.0.0", "primeflex": "^3.2.0", "primeicons": "^6.0.1", "primeng": "^17.18.15", "rxjs": "^7.8.2", "scriptjs": "^2.5.9", "socket.io-client": "^4.7.2", "swiper": "^10.1.0", "tslib": "^2.6.1", "uuid": "^10.0.0", "zone.js": "^0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.20", "@angular-eslint/builder": "^18.4.3", "@angular-eslint/eslint-plugin": "^18.4.3", "@angular-eslint/eslint-plugin-template": "^18.4.3", "@angular-eslint/schematics": "^18.4.3", "@angular-eslint/template-parser": "^18.4.3", "@angular/cli": "^18.2.20", "@angular/compiler-cli": "^18.2.13", "@angular/localize": "^18.2.13", "@types/express": "^4.17.0", "@types/jasmine": "~3.10.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "browser-sync": "^3.0.0", "depcheck": "^1.4.3", "eslint": "^8.2.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "gzipper": "^7.2.0", "husky": "^9.1.7", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "lint-staged": "^16.2.0", "localstorage-polyfill": "^1.0.1", "prettier": "^3.6.2", "typescript": "~5.4.5"}, "lint-staged": {"*.{ts,js,html,scss,css,json}": ["npx prettier --write"]}}