"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[791],{3791:(I,g,i)=>{i.r(g),i.d(g,{ResetPasswordOtpComponent:()=>y});var u=i(6814),l=i(6223),h=i(4825),m=i(6663),c=i(707),_=i(9566),e=i(5879),f=i(5219),p=i(864),v=i(6075),M=i(5662);function O(o,E){if(1&o){const t=e.EpF();e.TgZ(0,"button",14),e.NdJ("click",function(){e.CHM(t);const s=e.oxw();return e.KtG(s.resendOtp())}),e._uU(1),e.ALo(2,"translate"),e.qZA()}if(2&o){const t=e.oxw();e.Q6J("disabled","00"!==t.timeLeft),e.xp6(1),e.hij(" ",e.lcZ(2,2,"auth.otp.resend")," ")}}function C(o,E){if(1&o&&(e.TgZ(0,"span",15),e._uU(1),e.ALo(2,"translate"),e.qZA()),2&o){const t=e.oxw();e.xp6(1),e.AsE(" ",e.lcZ(2,2,"auth.otp.resendIn")," 0:",t.timeLeft,"")}}const P=function(o){return{marginTop:o}},d=function(){return{standalone:!0}};let y=(()=>{class o{messageService;auth;mainDataService;cd;permissionService;router;otpService;translate;store;user;$gaService;mobileNumber="";size=50;value1;value2;value3;value4;otpCode="";countDown;counter=60;timeLeft=60;tick=1e3;interval;isMobileLayout=!1;isGoogleAnalytics=!1;constructor(t,a,s,r,n,b,T,D,w,x,A){this.messageService=t,this.auth=a,this.mainDataService=s,this.cd=r,this.permissionService=n,this.router=b,this.otpService=T,this.translate=D,this.store=w,this.user=x,this.$gaService=A}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.countDown=(0,h.H)(0,this.tick).subscribe(()=>--this.counter),this.startTimer()}ngOnDestroy(){this.countDown&&this.countDown.unsubscribe()}onDigitInput(t){let a;"Backspace"!==t.code&&"Tab"!==t.code&&(a=t.srcElement.nextElementSibling),"Backspace"===t.code&&(a=t.srcElement.previousElementSibling),a?.focus()}validateOtp(){this.store.set("loading",!0),this.value1&&this.value2&&this.value3&&this.value4?(this.otpCode=this.value1.toString()+this.value2.toString()+this.value3.toString()+this.value4.toString(),history?.state?.password?this.login():this.otpService.VerifyForgotPassword({mobileNumber:this.store.get("userPhone"),otpCode:this.otpCode,RequestId:this.store.get("verificationCode")}).subscribe({next:t=>{this.store.set("loading",!1),t.success?(this.isGoogleAnalytics&&this.$gaService.event(_.s.CLICK_ON_OTP_VERIFY,"","OTP_VERIFICATION",1,!0),this.router.navigateByUrl("/update-password",{state:{mobile:this.mobileNumber,otp:this.otpCode}})):this.messageService.add({severity:"error",summary:t.message})},error:t=>{this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}})):this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:this.translate.instant("ErrorMessages.mobileRequired")})}login(){this.store.set("loading",!0),this.auth.login({username:history.state.Username,password:history.state.password,RequestId:history.state.verificationCode,otp:this.otpCode}).subscribe({next:t=>{t?.success?(this.mainDataService.setUserData(t.data),this.store.set("profile",t.data),this.store.set("userPhone",t.data.mobileNumber),this.store.set("timeInterval",(new Date).getTime()),this.store.set("refreshToken",t.data.refreshToken.replace("bearer","")),this.store.set("loading",!1),t.data.isPasswodExpired?(this.router.navigateByUrl("/change-password"),this.messageService.add({severity:"info",summary:this.translate.instant("ResponseMessages.changePassword"),detail:this.translate.instant("ResponseMessages.passwordExpirationChange")})):(this.router.navigate(["/"]),this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.login"),detail:this.translate.instant("ResponseMessages.loggedInSuccessfully")}))):(this.store.set("profile",""),this.store.set("loading",!1),this.messageService.add({severity:"error",summary:t?.Message??this.translate.instant("ErrorMessages.invalidUserNameOrPassword")}))},error:t=>{this.store.set("profile",""),this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}})}resendOtp(){this.store.set("loading",!0),this.user.ForgotPassword({userName:this.user.username.replace("-","")}).subscribe({next:t=>{this.startTimer(),this.store.set("loading",!1),this.store.set("verificationCode",t.data.requestId)},error:t=>{this.store.set("loading",!1),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:t.message})}})}startTimer(){this.interval=setInterval(()=>{this.timeLeft>0?this.timeLeft--:this.timeLeft=60,0===this.timeLeft&&clearInterval(this.interval),this.timeLeft<10&&(this.timeLeft="0"+this.timeLeft),this.cd.detectChanges()},1e3)}static \u0275fac=function(a){return new(a||o)(e.Y36(f.ez),e.Y36(p.e8),e.Y36(p.iI),e.Y36(e.sBO),e.Y36(p.$A),e.Y36(v.F0),e.Y36(p.aO),e.Y36(m.sK),e.Y36(p.d6),e.Y36(p.KD),e.Y36(M.$r))};static \u0275cmp=e.Xpm({type:o,selectors:[["app-reset-password-otp"]],standalone:!0,features:[e.jDz],decls:23,vars:27,consts:[[1,"otp"],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center","margin-x-100"],[1,"col-12","m-0","py-0","text-center","otp-heading"],["href","/",1,"col-12","main-color","mb-3","text-center","sent-otp"],[1,"col-12","col-md-8","col-lg-6","shadow-signin","bg-white","pt-6"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12","mb-8"],["action","",1,"mt-5"],["type","text","maxlength","1","oninput","this.value=this.value.replace(/[^0-9]/g,'');",3,"ngModel","ngModelOptions","ngModelChange","keyup"],[1,"count"],["style","background: white; border: none; cursor: pointer","class","resend",3,"disabled","click",4,"ngIf"],["class","time-left",4,"ngIf"],["pButton","","type","button","pButton","","type","button",1,"mb-5","mt-3","p-button","p-component","p-element","second-btn","width-100",3,"label","disabled","click"],[1,"resend",2,"background","white","border","none","cursor","pointer",3,"disabled","click"],[1,"time-left"]],template:function(a,s){1&a&&(e.TgZ(0,"section",0),e.ynx(1),e.TgZ(2,"div",1)(3,"div",2)(4,"p",3),e._uU(5),e.ALo(6,"translate"),e.qZA(),e.TgZ(7,"div",4),e._uU(8),e.ALo(9,"translate"),e.qZA(),e.TgZ(10,"div",5)(11,"div",6)(12,"div",7)(13,"form",8)(14,"input",9),e.NdJ("ngModelChange",function(n){return s.value1=n})("keyup",function(n){return s.onDigitInput(n)}),e.qZA(),e.TgZ(15,"input",9),e.NdJ("ngModelChange",function(n){return s.value2=n})("keyup",function(n){return s.onDigitInput(n)}),e.qZA(),e.TgZ(16,"input",9),e.NdJ("ngModelChange",function(n){return s.value3=n})("keyup",function(n){return s.onDigitInput(n)}),e.qZA(),e.TgZ(17,"input",9),e.NdJ("ngModelChange",function(n){return s.value4=n})("keyup",function(n){return s.onDigitInput(n)}),e.qZA()()(),e.TgZ(18,"div",10),e.YNc(19,O,3,4,"button",11),e.YNc(20,C,3,4,"span",12),e.qZA(),e.TgZ(21,"button",13),e.NdJ("click",function(){return s.validateOtp()}),e.ALo(22,"translate"),e.qZA()()()()(),e.BQk(),e.qZA()),2&a&&(e.xp6(2),e.Q6J("ngStyle",e.VKq(21,P,s.isMobileLayout?"1rem":"220px")),e.xp6(3),e.hij(" ",e.lcZ(6,15,"otp.enterOTP")," "),e.xp6(3),e.hij(" ",e.lcZ(9,17,"otp.verificationCodeSent")," "),e.xp6(6),e.Q6J("ngModel",s.value1)("ngModelOptions",e.DdM(23,d)),e.xp6(1),e.Q6J("ngModel",s.value2)("ngModelOptions",e.DdM(24,d)),e.xp6(1),e.Q6J("ngModel",s.value3)("ngModelOptions",e.DdM(25,d)),e.xp6(1),e.Q6J("ngModel",s.value4)("ngModelOptions",e.DdM(26,d)),e.xp6(2),e.Q6J("ngIf",0===s.timeLeft),e.xp6(1),e.Q6J("ngIf",s.timeLeft>0),e.xp6(1),e.Q6J("label",e.lcZ(22,19,"auth.otp.next"))("disabled",!(s.value1&&s.value2&&s.value3&&s.value4)))},dependencies:[u.ez,u.O5,u.PC,l.u5,l._Y,l.Fj,l.JJ,l.JL,l.nD,l.On,l.F,m.aw,m.X$,c.hJ,c.Hq],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}form[_ngcontent-%COMP%]{text-align:center}form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{display:inline-block;width:60px;height:60px;text-align:center;margin:0 .8rem;background:#F5F5F5!important;border-bottom:1px solid #AEAEAE!important;font-size:20px;font-weight:500;font-family:var(--medium-font)!important}.count[_ngcontent-%COMP%]{text-align:center;font-size:small}.shadow-signin[_ngcontent-%COMP%]{border:1px solid rgba(151,151,151,.17);border-radius:7px}.otp-heading[_ngcontent-%COMP%]{font-size:28px;font-weight:700;font-family:var(--medium-font)!important}.sent-otp[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#a3a3a3;font-family:var(--regular-font)!important}.second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important;text-transform:uppercase}.resend[_ngcontent-%COMP%]{font-size:14px;font-weight:800;font-family:var(--medium-font)!important}@media screen and (max-width: 768px){.otp[_ngcontent-%COMP%]{margin-top:0}form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{margin:0 .4rem!important}}.time-left[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-size:13px;font-weight:400;color:#a3a3a3}@media only screen and (max-width: 767px){.margin-x-100[_ngcontent-%COMP%]{margin-top:250px!important}}"]})}return o})()}}]);