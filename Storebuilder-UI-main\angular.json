{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"momo-market": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["google-libphonenumber", "crypto-js", "scriptjs", "leaflet"], "outputPath": "dist/momo-market/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/storecloud.ico", "src/favicon.ico", "src/assets", "src/web.config"], "styles": ["node_modules/@angular/material/prebuilt-themes/pink-bluegrey.css", "node_modules/primeng/resources/themes/saga-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "node_modules/primeflex/primeflex.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "./node_modules/intl-tel-input/build/css/intlTelInput.css", "./node_modules/leaflet/dist/leaflet.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "node_modules/ngx-sharebuttons/themes/circles.scss", "node_modules/ngx-sharebuttons/themes/modern.scss", "node_modules/bootstrap/scss/bootstrap.scss", "src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/intl-tel-input/build/js/utils.js"], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5000kb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false}, "deploy-staging": {"budgets": [{"type": "initial", "maximumWarning": "5000kb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "outputHashing": "all", "sourceMap": false}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true}, "storecloud": {"budgets": [{"type": "initial", "maximumWarning": "5000kb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "400kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment-sc.ts"}], "outputHashing": "all", "index": {"input": "src/storecloud.html", "output": "index.html"}, "sourceMap": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "momo-market:build:production"}, "development": {"browserTarget": "momo-market:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "momo-market:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/pink-bluegrey.css", "src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "serve-ssr": {"builder": "@angular/ssr:dev-server", "options": {"buildTarget": "momo-market:build"}}, "build-ssr": {"builder": "@angular/ssr:build", "options": {"outputPath": "dist/momo-market"}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/momo-market/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "sourceMap": false, "extractLicenses": false}}, "defaultConfiguration": "production"}, "prerender": {"builder": "@angular-devkit/build-angular:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "momo-market:build:production", "serverTarget": "momo-market:server:production"}, "development": {"browserTarget": "momo-market:build:development", "serverTarget": "momo-market:server:development"}}, "defaultConfiguration": "production"}}}}}