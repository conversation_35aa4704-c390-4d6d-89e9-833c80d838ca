{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\n\n/** Default dropdown configuration */\nconst _c0 = function (a0) {\n  return {\n    dropdown: a0\n  };\n};\nconst _c1 = [\"*\"];\nlet BsDropdownConfig = /*#__PURE__*/(() => {\n  class BsDropdownConfig {\n    constructor() {\n      /** default dropdown auto closing behavior */\n      this.autoClose = true;\n      /** default dropdown auto closing behavior */\n      this.insideClick = false;\n      /** turn on/off animation */\n      this.isAnimated = false;\n      /** value true of stopOnClickPropagation allows event stopPropagation*/\n      this.stopOnClickPropagation = false;\n    }\n    static #_ = this.ɵfac = function BsDropdownConfig_Factory(t) {\n      return new (t || BsDropdownConfig)();\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BsDropdownConfig,\n      factory: BsDropdownConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return BsDropdownConfig;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDropdownState = /*#__PURE__*/(() => {\n  class BsDropdownState {\n    constructor() {\n      this.direction = 'down';\n      this.autoClose = true;\n      this.insideClick = false;\n      this.isAnimated = false;\n      this.stopOnClickPropagation = false;\n      this.isOpenChange = new EventEmitter();\n      this.isDisabledChange = new EventEmitter();\n      this.toggleClick = new EventEmitter();\n      this.counts = 0;\n      this.dropdownMenu = new Promise(resolve => {\n        this.resolveDropdownMenu = resolve;\n      });\n    }\n    static #_ = this.ɵfac = function BsDropdownState_Factory(t) {\n      return new (t || BsDropdownState)();\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BsDropdownState,\n      factory: BsDropdownState.ɵfac,\n      providedIn: 'platform'\n    });\n  }\n  return BsDropdownState;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DROPDOWN_ANIMATION_TIMING, style({\n  height: '*',\n  overflow: 'hidden'\n}))];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nlet BsDropdownContainerComponent = /*#__PURE__*/(() => {\n  class BsDropdownContainerComponent {\n    get direction() {\n      return this._state.direction;\n    }\n    constructor(_state, cd, _renderer, _element, _builder) {\n      this._state = _state;\n      this.cd = cd;\n      this._renderer = _renderer;\n      this._element = _element;\n      this.isOpen = false;\n      this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n      this._subscription = _state.isOpenChange.subscribe(value => {\n        this.isOpen = value;\n        const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n        this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n        if (dropdown) {\n          this._renderer.addClass(dropdown, 'show');\n          if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n            this._renderer.setStyle(dropdown, 'left', 'auto');\n            this._renderer.setStyle(dropdown, 'right', '0');\n          }\n          if (this.direction === 'up') {\n            this._renderer.setStyle(dropdown, 'top', 'auto');\n            this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n          }\n        }\n        if (dropdown && this._state.isAnimated) {\n          this._factoryDropDownAnimation.create(dropdown).play();\n        }\n        this.cd.markForCheck();\n        this.cd.detectChanges();\n      });\n    }\n    /** @internal */\n    _contains(el) {\n      return this._element.nativeElement.contains(el);\n    }\n    ngOnDestroy() {\n      this._subscription.unsubscribe();\n    }\n    static #_ = this.ɵfac = function BsDropdownContainerComponent_Factory(t) {\n      return new (t || BsDropdownContainerComponent)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BsDropdownContainerComponent,\n      selectors: [[\"bs-dropdown-container\"]],\n      hostAttrs: [2, \"display\", \"block\", \"position\", \"absolute\", \"z-index\", \"1040\"],\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 9,\n      consts: [[3, \"ngClass\"]],\n      template: function BsDropdownContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dropup\", ctx.direction === \"up\")(\"show\", ctx.isOpen)(\"open\", ctx.isOpen);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.direction === \"down\"));\n        }\n      },\n      dependencies: [i3.NgClass],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return BsDropdownContainerComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDropdownDirective = /*#__PURE__*/(() => {\n  class BsDropdownDirective {\n    /**\n     * Indicates that dropdown will be closed on item or document click,\n     * and after pressing ESC\n     */\n    set autoClose(value) {\n      this._state.autoClose = value;\n    }\n    get autoClose() {\n      return this._state.autoClose;\n    }\n    /**\n     * Indicates that dropdown will be animated\n     */\n    set isAnimated(value) {\n      this._state.isAnimated = value;\n    }\n    get isAnimated() {\n      return this._state.isAnimated;\n    }\n    /**\n     * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n     */\n    set insideClick(value) {\n      this._state.insideClick = value;\n    }\n    get insideClick() {\n      return this._state.insideClick;\n    }\n    /**\n     * Disables dropdown toggle and hides dropdown menu if opened\n     */\n    set isDisabled(value) {\n      this._isDisabled = value;\n      this._state.isDisabledChange.emit(value);\n      if (value) {\n        this.hide();\n      }\n    }\n    get isDisabled() {\n      return this._isDisabled;\n    }\n    /**\n     * Returns whether or not the popover is currently being shown\n     */\n    get isOpen() {\n      if (this._showInline) {\n        return this._isInlineOpen;\n      }\n      return this._dropdown.isShown;\n    }\n    set isOpen(value) {\n      if (value) {\n        this.show();\n      } else {\n        this.hide();\n      }\n    }\n    get _showInline() {\n      return !this.container;\n    }\n    constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n      this._elementRef = _elementRef;\n      this._renderer = _renderer;\n      this._viewContainerRef = _viewContainerRef;\n      this._cis = _cis;\n      this._state = _state;\n      this._config = _config;\n      /**\n       * This attribute indicates that the dropdown should be opened upwards\n       */\n      this.dropup = false;\n      // todo: move to component loader\n      this._isInlineOpen = false;\n      this._isDisabled = false;\n      this._subscriptions = [];\n      this._isInited = false;\n      // set initial dropdown state from config\n      this._state.autoClose = this._config.autoClose;\n      this._state.insideClick = this._config.insideClick;\n      this._state.isAnimated = this._config.isAnimated;\n      this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n      this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n      // create dropdown component loader\n      this._dropdown = this._cis.createLoader(this._elementRef, this._viewContainerRef, this._renderer).provide({\n        provide: BsDropdownState,\n        useValue: this._state\n      });\n      this.onShown = this._dropdown.onShown;\n      this.onHidden = this._dropdown.onHidden;\n      this.isOpenChange = this._state.isOpenChange;\n    }\n    ngOnInit() {\n      // fix: seems there are an issue with `routerLinkActive`\n      // which result in duplicated call ngOnInit without call to ngOnDestroy\n      // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n      if (this._isInited) {\n        return;\n      }\n      this._isInited = true;\n      // attach DOM listeners\n      this._dropdown.listen({\n        // because of dropdown inline mode\n        outsideClick: false,\n        triggers: this.triggers,\n        show: () => this.show()\n      });\n      // toggle visibility on toggle element click\n      this._subscriptions.push(this._state.toggleClick.subscribe(value => this.toggle(value)));\n      // hide dropdown if set disabled while opened\n      this._subscriptions.push(this._state.isDisabledChange.pipe(filter(value => value)).subscribe(( /*value: boolean*/) => this.hide()));\n    }\n    /**\n     * Opens an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    show() {\n      if (this.isOpen || this.isDisabled) {\n        return;\n      }\n      if (this._showInline) {\n        if (!this._inlinedMenu) {\n          this._state.dropdownMenu.then(dropdownMenu => {\n            this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n            this._inlinedMenu = this._dropdown._inlineViewRef;\n            this.addBs4Polyfills();\n            if (this._inlinedMenu) {\n              this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n            }\n            this.playAnimation();\n          })\n          // swallow errors\n          .catch();\n        }\n        this.addBs4Polyfills();\n        this._isInlineOpen = true;\n        this.onShown.emit(true);\n        this._state.isOpenChange.emit(true);\n        this.playAnimation();\n        return;\n      }\n      this._state.dropdownMenu.then(dropdownMenu => {\n        // check direction in which dropdown should be opened\n        const _dropup = this.dropup || typeof this.dropup !== 'undefined' && this.dropup;\n        this._state.direction = _dropup ? 'up' : 'down';\n        const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n        // show dropdown\n        this._dropdown.attach(BsDropdownContainerComponent).to(this.container).position({\n          attachment: _placement\n        }).show({\n          content: dropdownMenu.templateRef,\n          placement: _placement\n        });\n        this._state.isOpenChange.emit(true);\n      })\n      // swallow error\n      .catch();\n    }\n    /**\n     * Closes an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    hide() {\n      if (!this.isOpen) {\n        return;\n      }\n      if (this._showInline) {\n        this.removeShowClass();\n        this.removeDropupStyles();\n        this._isInlineOpen = false;\n        this.onHidden.emit(true);\n      } else {\n        this._dropdown.hide();\n      }\n      this._state.isOpenChange.emit(false);\n    }\n    /**\n     * Toggles an element’s popover. This is considered a “manual” triggering of\n     * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n     * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n     */\n    toggle(value) {\n      if (this.isOpen || !value) {\n        return this.hide();\n      }\n      return this.show();\n    }\n    /** @internal */\n    _contains(event) {\n      // todo: valorkin fix typings\n      return this._elementRef.nativeElement.contains(event.target) || this._dropdown.instance && this._dropdown.instance._contains(event.target);\n    }\n    navigationClick(event) {\n      const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n      if (!ref) {\n        return;\n      }\n      const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n      const allRef = ref.querySelectorAll('.dropdown-item');\n      switch (event.keyCode) {\n        case 38:\n          if (this._state.counts > 0) {\n            allRef[--this._state.counts].focus();\n          }\n          break;\n        case 40:\n          if (this._state.counts + 1 < allRef.length) {\n            if (firstActive.classList !== allRef[this._state.counts].classList) {\n              allRef[this._state.counts].focus();\n            } else {\n              allRef[++this._state.counts].focus();\n            }\n          }\n          break;\n        default:\n      }\n      event.preventDefault();\n    }\n    ngOnDestroy() {\n      // clean up subscriptions and destroy dropdown\n      for (const sub of this._subscriptions) {\n        sub.unsubscribe();\n      }\n      this._dropdown.dispose();\n    }\n    addBs4Polyfills() {\n      this.addShowClass();\n      this.checkRightAlignment();\n      this.addDropupStyles();\n    }\n    playAnimation() {\n      if (this._state.isAnimated && this._inlinedMenu) {\n        setTimeout(() => {\n          if (this._inlinedMenu) {\n            this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n          }\n        });\n      }\n    }\n    addShowClass() {\n      if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n        this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n      }\n    }\n    removeShowClass() {\n      if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n        this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n      }\n    }\n    checkRightAlignment() {\n      if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n        const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n        this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n        this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n      }\n    }\n    addDropupStyles() {\n      if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n        // a little hack to not break support of bootstrap 4 beta\n        this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n        this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n        this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n      }\n    }\n    removeDropupStyles() {\n      if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n        this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n        this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n        this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n      }\n    }\n    static #_ = this.ɵfac = function BsDropdownDirective_Factory(t) {\n      return new (t || BsDropdownDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(BsDropdownConfig), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownDirective,\n      selectors: [[\"\", \"bsDropdown\", \"\"], [\"\", \"dropdown\", \"\"]],\n      hostVars: 6,\n      hostBindings: function BsDropdownDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.arrowDown\", function BsDropdownDirective_keydown_arrowDown_HostBindingHandler($event) {\n            return ctx.navigationClick($event);\n          })(\"keydown.arrowUp\", function BsDropdownDirective_keydown_arrowUp_HostBindingHandler($event) {\n            return ctx.navigationClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dropup\", ctx.dropup)(\"open\", ctx.isOpen)(\"show\", ctx.isOpen);\n        }\n      },\n      inputs: {\n        placement: \"placement\",\n        triggers: \"triggers\",\n        container: \"container\",\n        dropup: \"dropup\",\n        autoClose: \"autoClose\",\n        isAnimated: \"isAnimated\",\n        insideClick: \"insideClick\",\n        isDisabled: \"isDisabled\",\n        isOpen: \"isOpen\"\n      },\n      outputs: {\n        isOpenChange: \"isOpenChange\",\n        onShown: \"onShown\",\n        onHidden: \"onHidden\"\n      },\n      exportAs: [\"bs-dropdown\"],\n      features: [i0.ɵɵProvidersFeature([BsDropdownState])]\n    });\n  }\n  return BsDropdownDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDropdownMenuDirective = /*#__PURE__*/(() => {\n  class BsDropdownMenuDirective {\n    constructor(_state, _viewContainer, _templateRef) {\n      _state.resolveDropdownMenu({\n        templateRef: _templateRef,\n        viewContainer: _viewContainer\n      });\n    }\n    static #_ = this.ɵfac = function BsDropdownMenuDirective_Factory(t) {\n      return new (t || BsDropdownMenuDirective)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownMenuDirective,\n      selectors: [[\"\", \"bsDropdownMenu\", \"\"], [\"\", \"dropdownMenu\", \"\"]],\n      exportAs: [\"bs-dropdown-menu\"]\n    });\n  }\n  return BsDropdownMenuDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDropdownToggleDirective = /*#__PURE__*/(() => {\n  class BsDropdownToggleDirective {\n    constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n      this._changeDetectorRef = _changeDetectorRef;\n      this._dropdown = _dropdown;\n      this._element = _element;\n      this._renderer = _renderer;\n      this._state = _state;\n      this.isOpen = false;\n      this._subscriptions = [];\n      // sync is open value with state\n      this._subscriptions.push(this._state.isOpenChange.subscribe(value => {\n        this.isOpen = value;\n        if (value) {\n          this._documentClickListener = this._renderer.listen('document', 'click', event => {\n            if (this._state.autoClose && event.button !== 2 && !this._element.nativeElement.contains(event.target) && !(this._state.insideClick && this._dropdown._contains(event))) {\n              this._state.toggleClick.emit(false);\n              this._changeDetectorRef.detectChanges();\n            }\n          });\n          this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n            if (this._state.autoClose) {\n              this._state.toggleClick.emit(false);\n              this._changeDetectorRef.detectChanges();\n            }\n          });\n        } else {\n          this._documentClickListener && this._documentClickListener();\n          this._escKeyUpListener && this._escKeyUpListener();\n        }\n      }));\n      // populate disabled state\n      this._subscriptions.push(this._state.isDisabledChange.subscribe(value => this.isDisabled = value || void 0));\n    }\n    onClick(event) {\n      if (this._state.stopOnClickPropagation) {\n        event.stopPropagation();\n      }\n      if (this.isDisabled) {\n        return;\n      }\n      this._state.toggleClick.emit(true);\n    }\n    ngOnDestroy() {\n      if (this._documentClickListener) {\n        this._documentClickListener();\n      }\n      if (this._escKeyUpListener) {\n        this._escKeyUpListener();\n      }\n      for (const sub of this._subscriptions) {\n        sub.unsubscribe();\n      }\n    }\n    static #_ = this.ɵfac = function BsDropdownToggleDirective_Factory(t) {\n      return new (t || BsDropdownToggleDirective)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(BsDropdownDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDropdownState));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: BsDropdownToggleDirective,\n      selectors: [[\"\", \"bsDropdownToggle\", \"\"], [\"\", \"dropdownToggle\", \"\"]],\n      hostVars: 3,\n      hostBindings: function BsDropdownToggleDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function BsDropdownToggleDirective_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", true)(\"disabled\", ctx.isDisabled)(\"aria-expanded\", ctx.isOpen);\n        }\n      },\n      exportAs: [\"bs-dropdown-toggle\"]\n    });\n  }\n  return BsDropdownToggleDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BsDropdownModule = /*#__PURE__*/(() => {\n  class BsDropdownModule {\n    static forRoot() {\n      return {\n        ngModule: BsDropdownModule,\n        providers: [ComponentLoaderFactory, PositioningService, BsDropdownState]\n      };\n    }\n    static #_ = this.ɵfac = function BsDropdownModule_Factory(t) {\n      return new (t || BsDropdownModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BsDropdownModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return BsDropdownModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };\n//# sourceMappingURL=ngx-bootstrap-dropdown.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}