"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[192],{5192:(j,p,r)=>{r.r(p),r.d(p,{OrdersModule:()=>U});var d=r(6814),m=r(6075),n=r(5879),_=r(2284),l=r(553),c=r(864),f=r(906),u=r(5662),g=r(6663),b=r(9147),C=r(1712),O=r(3892),P=r(5581);const M=function(e){return{color:e}};function w(e,i){if(1&e&&(n.TgZ(0,"a",10)(1,"div",11)(2,"div",12)(3,"div",13)(4,"span",14),n._uU(5),n.ALo(6,"translate"),n.qZA()(),n.TgZ(7,"span",15),n._uU(8),n.ALo(9,"translate"),n.qZ<PERSON>()(),n.TgZ(10,"div",16)(11,"span",17),n._uU(12),n.ALo(13,"translate"),n.qZA(),n.TgZ(14,"span",15),n._uU(15),n.qZA()(),n.TgZ(16,"div")(17,"span",18),n._uU(18),n.ALo(19,"date"),n.qZA(),n.TgZ(20,"span",19),n._uU(21),n.ALo(22,"date"),n.qZA()()(),n.TgZ(23,"div",20)(24,"button",21),n._UZ(25,"img",22),n.qZA()()()),2&e){const t=i.$implicit,o=n.oxw(3);n.xp6(4),n.Q6J("ngStyle",n.VKq(20,M,o.utility.orderStatus(t.status,o.OrderStatus))),n.xp6(1),n.hij(" ",n.lcZ(6,9,"order."+t.orderStatus)," "),n.xp6(3),n.AsE("",null==t.orderProducts?null:t.orderProducts.length," ",n.lcZ(9,11,"order.orderItems"),""),n.xp6(4),n.Oqu(n.lcZ(13,13,"order.orderNo")),n.xp6(3),n.Oqu(t.orderId),n.xp6(3),n.hij("",n.lcZ(19,15,t.orderDate)," \u2022 "),n.xp6(3),n.Oqu(n.xi3(22,17,t.createdOn,"shortTime")),n.xp6(3),n.Q6J("routerLink","/order/"+t.orderId)}}const x=function(){return[5,10,20,30]};function v(e,i){if(1&e){const t=n.EpF();n.TgZ(0,"p-paginator",23),n.NdJ("onPageChange",function(a){n.CHM(t);const s=n.oxw(3);return n.KtG(s.paginate(a))}),n.qZA()}if(2&e){const t=n.oxw(3);n.Q6J("rowsPerPageOptions",n.DdM(3,x))("rows",t.pageSize)("totalRecords",t.totalRecords)}}function y(e,i){if(1&e&&(n.ynx(0),n.TgZ(1,"div",3)(2,"section",4),n._UZ(3,"app-back-button",5),n.TgZ(4,"div",6)(5,"div",7),n.YNc(6,w,26,22,"a",8),n.qZA()()(),n.YNc(7,v,1,4,"p-paginator",9),n.qZA(),n.BQk()),2&e){const t=n.oxw(2);n.xp6(3),n.Q6J("backText","header.yourOrders"),n.xp6(3),n.Q6J("ngForOf",t.orders),n.xp6(1),n.Q6J("ngIf",t.orders.length>=5)}}function Z(e,i){1&e&&n._UZ(0,"app-back-button",5)(1,"empty-screen",24),2&e&&(n.Q6J("backText","header.yourOrders"),n.xp6(1),n.Q6J("title","order.noOrder")("img","assets/images/payment-icons/no-order.svg"))}function A(e,i){if(1&e&&(n.ynx(0),n.YNc(1,y,8,3,"ng-container",0),n.YNc(2,Z,2,3,"ng-template",null,2,n.W1O),n.BQk()),2&e){const t=n.MAs(3),o=n.oxw();n.xp6(1),n.Q6J("ngIf",o.orders.length>0)("ngIfElse",t)}}function I(e,i){if(1&e){const t=n.EpF();n.ynx(0),n.TgZ(1,"em",40),n.NdJ("click",function(){n.CHM(t);const a=n.oxw().$implicit,s=n.oxw(2);return n.KtG(s.updateOrderStatus(a.orderId))}),n.qZA(),n.BQk()}}function T(e,i){if(1&e&&(n.TgZ(0,"a",10)(1,"div",11)(2,"div")(3,"span",18),n._uU(4),n.ALo(5,"date"),n.qZA(),n.TgZ(6,"span",19),n._uU(7),n.ALo(8,"date"),n.qZA()(),n.TgZ(9,"div",16)(10,"span",17),n._uU(11),n.ALo(12,"translate"),n.qZA(),n.TgZ(13,"span",15),n._uU(14),n.qZA()(),n.TgZ(15,"div",12)(16,"span",17),n._uU(17),n.ALo(18,"translate"),n.qZA(),n.TgZ(19,"span",15),n._uU(20),n.ALo(21,"translate"),n.qZA()()(),n.TgZ(22,"div",20)(23,"div",33),n.YNc(24,I,2,0,"ng-container",34),n.TgZ(25,"div",35),n._UZ(26,"em",36),n.TgZ(27,"span",37),n._uU(28),n.ALo(29,"translate"),n.qZA()()(),n.TgZ(30,"button",21)(31,"span",38),n._uU(32),n.ALo(33,"translate"),n.qZA(),n._UZ(34,"img",39),n.qZA()()()),2&e){const t=i.$implicit,o=n.oxw(2);n.xp6(4),n.hij("",n.lcZ(5,12,t.orderDate)," \u2022 "),n.xp6(3),n.Oqu(n.xi3(8,14,t.createdOn,"shortTime")),n.xp6(4),n.Oqu(n.lcZ(12,17,"order.orderNo")),n.xp6(3),n.Oqu(t.orderId),n.xp6(3),n.Oqu(n.lcZ(18,19,"order.orderItemsCount")),n.xp6(3),n.AsE("",null==t.orderProducts?null:t.orderProducts.length," ",n.lcZ(21,21,"order.orderItems"),""),n.xp6(4),n.Q6J("ngIf",4===o.countryId),n.xp6(2),n.Q6J("ngStyle",n.VKq(27,M,o.utility.orderStatus(t.status,o.OrderStatus))),n.xp6(2),n.hij(" ",n.lcZ(29,23,"order."+t.orderStatus)," "),n.xp6(2),n.Q6J("routerLink","/order/"+t.orderId),n.xp6(2),n.hij(" ",n.lcZ(33,25,"order.viewDetails")," ")}}function S(e,i){if(1&e){const t=n.EpF();n.TgZ(0,"p-paginator",23),n.NdJ("onPageChange",function(a){n.CHM(t);const s=n.oxw(2);return n.KtG(s.paginate(a))}),n.qZA()}if(2&e){const t=n.oxw(2);n.Q6J("rowsPerPageOptions",n.DdM(3,x))("rows",t.pageSize)("totalRecords",t.totalRecords)}}const k=function(e,i){return{"breadcrumb-address":e,"hidden-navbar":i}};function z(e,i){if(1&e&&(n.TgZ(0,"div",25)(1,"div",26),n._UZ(2,"em",27)(3,"em",28),n.TgZ(4,"span",29),n._uU(5),n.ALo(6,"translate"),n.qZA(),n._UZ(7,"em",28),n.TgZ(8,"span"),n._uU(9),n.ALo(10,"translate"),n.qZA()(),n.TgZ(11,"section",4)(12,"div",6)(13,"div",30),n._uU(14),n.ALo(15,"translate"),n.TgZ(16,"span",31),n._uU(17),n.qZA()(),n.TgZ(18,"div",32),n._uU(19),n.ALo(20,"translate"),n.qZA(),n.TgZ(21,"div",7),n.YNc(22,T,35,29,"a",8),n.qZA()()(),n.YNc(23,S,1,4,"p-paginator",9),n.qZA()),2&e){const t=n.oxw();n.xp6(1),n.Q6J("ngClass",n.WLB(18,k,null==t.navbarData?null:t.navbarData.isActive,!(null!=t.navbarData&&t.navbarData.isActive))),n.xp6(1),n.Q6J("routerLink","/"),n.xp6(2),n.Q6J("routerLink","/account"),n.xp6(1),n.Oqu(n.lcZ(6,10,"sideMenu.yourAccount")),n.xp6(4),n.Oqu(n.lcZ(10,12,"header.yourOrders")),n.xp6(5),n.hij(" ",n.lcZ(15,14,"order.yourOrders")," "),n.xp6(3),n.hij("(",t.ordersNumbers,")"),n.xp6(2),n.hij(" ",n.lcZ(20,16,"order.allOrders")," "),n.xp6(3),n.Q6J("ngForOf",t.orders),n.xp6(1),n.Q6J("ngIf",t.orders.length>=5)}}const L=[{path:"",component:(()=>{class e{orderService;store;shipemntService;translate;appDataService;permissionService;$gaService;platformId;_location;$gtmService;ordersNumbers=0;items=[];orders=[];baseUrl=l.N.apiEndPoint;mainProductUrl="";decimalValue=0;OrderStatus=_.iF;currencyCode="";disableCent;loading=!1;btnLabel="Rafra\xeechir";btnIcon="pi pi-refresh";countryId;totalOrders=0;page=1;pageSize=5;totalRecords=0;_BaseURL=l.N.apiEndPoint;navbarData;isLayoutTemplate=!1;tagName=u.Ir;userDetails;isGoogleAnalytics=!1;screenWidth;isMobileLayout=!1;utility=f.Z;constructor(t,o,a,s,E,D,q,J,N,Q){this.orderService=t,this.store=o,this.shipemntService=a,this.translate=s,this.appDataService=E,this.permissionService=D,this.$gaService=q,this.platformId=J,this._location=N,this.$gtmService=Q,this.isLayoutTemplate=this.permissionService.hasPermission("Layout-Template"),this.disableCent=localStorage.getItem("DisableCents"),this.countryId=localStorage.getItem("tenantId");let h=localStorage.getItem("CurrencyDecimal");h&&(this.decimalValue=parseInt(h)),this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),(0,d.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.navbarData=this.appDataService.layoutTemplate.find(o=>"navbar"===o.type),this.$gtmService.pushPageView("account","your orders"),this.items=[{icon:"pi pi-angle-left",label:this.translate.instant("order.yourOrders"),routerLink:"/account"}],this.loadData();let t=localStorage.getItem("currency")?.toString();t&&(this.currencyCode=t)}paginate(t){this.page=t.page+1,this.pageSize=t.rows,this.loadData()}loadData(){this.store.set("loading",!0);let t=new _.IU;t.currentPage=this.page,t.pageSize=this.pageSize,this.orderService.getCustomerOrders(t).subscribe({next:o=>{this.btnLabel="Rafra\xeechir",this.btnIcon="pi pi-refresh",this.orders=o?.orders,this.ordersNumbers=o.count,this.totalRecords=o.count,this.userDetails=this.store.get("profile"),this.triggerAnalytics(),this.store.set("loading",!1)},error:o=>{console.error(o),this.store.set("loading",!1)}})}refreshListing(){this.btnLabel="Rafra\xeechissant",this.btnIcon="pi pi-spin pi-spinner",this.loadData()}updateOrderStatus(t){this.store.set("loading",!0),this.shipemntService.updateShipmentStatus(t).subscribe({next:o=>{this.loadData(),this.store.set("loading",!1)},error:o=>{this.store.set("loading",!1)}})}orderListImage(t){return f.Z.verifyImageURL(t,this._BaseURL)}errorHandler(t){t.target.src=l.N.isStoreCloud?"assets/images/placeholder.png":"assets/images/mtn-alt.png"}triggerAnalytics(){this.isGoogleAnalytics&&this.permissionService.getTagFeature("VIEW_ITEM_LIST")&&(this.$gaService.pageView("/orders","All Orders"),this.$gaService.event(this.tagName.SEARCH,"","VIEW_ITEM_LIST",1,!0,{user_ID:this.userDetails?this.userDetails.mobileNumber:"Un_Authenticated"}))}backButton(){this._location.back()}static \u0275fac=function(o){return new(o||e)(n.Y36(c.px),n.Y36(c.d6),n.Y36(c.ZF),n.Y36(g.sK),n.Y36(c.UW),n.Y36(c.$A),n.Y36(u.$r),n.Y36(n.Lbi),n.Y36(d.Ye),n.Y36(b.J))};static \u0275cmp=n.Xpm({type:e,selectors:[["app-index"]],decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["oldContainer",""],["noOrders",""],[1,"mobile-new-order-list","order-bottom"],[1,"orders","orders-container"],[3,"backText"],[1,"orders-wrapper"],[1,"flex","flex-column","justify-content-start","flex-wrap","orders-links"],["class","flex justify-content-between no-underline text-black-alpha-90",4,"ngFor","ngForOf"],[3,"rowsPerPageOptions","rows","totalRecords","onPageChange",4,"ngIf"],[1,"flex","justify-content-between","no-underline","text-black-alpha-90"],[1,"orders__order","mx-2"],[1,"orders__order-count"],[1,""],[1,"order-text",3,"ngStyle"],[1,"value"],[1,"orders__order-number"],[1,"title"],["id","date",1,"orders__order-date"],["id","time",1,"orders__order-time"],[1,"orders__order-status"],[1,"btn","view-details-btn",3,"routerLink"],["alt","Arrow Right","src","assets/icons/arrow.svg"],[3,"rowsPerPageOptions","rows","totalRecords","onPageChange"],[3,"title","img"],[1,"new-order-list","order-bottom"],[1,"breadcrumb-address","d-flex",3,"ngClass"],["aria-hidden","true",1,"pi","pi-home","cursor-pointer",3,"routerLink"],["aria-hidden","true",1,"pi","pi-angle-right"],[3,"routerLink"],[1,"orders__heading"],[1,"product-nums"],[1,"orders__subHeading"],[1,"refresh-btn"],[4,"ngIf"],[1,"status-badge","px-2"],[1,"pi","pi-circle-fill","mr-1",3,"ngStyle"],[1,"text-white","order-text"],[1,"mr-1"],["alt","Arrow Right","src","assets/icons/ArrowRight.svg"],[1,"pi","pi-refresh",2,"color","#1d4c69 !important",3,"click"]],template:function(o,a){if(1&o&&(n.YNc(0,A,4,2,"ng-container",0),n.YNc(1,z,24,21,"ng-template",null,1,n.W1O)),2&o){const s=n.MAs(2);n.Q6J("ngIf",a.isMobileLayout&&a.screenWidth<=768)("ngIfElse",s)}},dependencies:[d.mk,d.sg,d.O5,d.PC,m.rH,C.D,O.s,P.W,d.uU,g.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]{margin-top:130px!important;flex-direction:column;background:#F6F6F6}.mobile-new-order-list[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]{padding:16px 0;display:flex}.mobile-new-order-list[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]   .text-span[_ngcontent-%COMP%]{font-family:main-medium;font-size:16px}.mobile-new-order-list[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]   .image-span[_ngcontent-%COMP%]{display:flex}.mobile-new-order-list[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]{margin:6px 0}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__heading[_ngcontent-%COMP%]{padding:20px 24px;font-size:18px;font-weight:500;font-family:var(--medium-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__subHeading[_ngcontent-%COMP%]{color:#2d2d2d;font-size:12px;font-weight:500;line-height:18px;background-color:#f2f4f5;padding:10px 24px;border:1px solid #E4E7E9;text-transform:uppercase;font-family:var(--medium-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:100%;height:104px;padding:8px;border-radius:8px;border-bottom:1px solid #E4E7E9;background:#FFF;margin-bottom:8px}.mobile-new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]{position:relative;top:20%}.mobile-new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .mobile-new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{color:var(--header_bgcolor);font-size:19px;cursor:pointer}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order[_ngcontent-%COMP%]{width:100%;margin-top:9px}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%], .mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]{font-size:16px;font-weight:400;line-height:24px;letter-spacing:normal;display:flex;justify-content:space-between;min-width:245px}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#204e6e;font-family:main-medium;font-size:12px;font-style:normal;font-weight:500}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#204e6e;font-family:var(--medium-font)!important;font-size:12px;font-style:normal;font-weight:400}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-date[_ngcontent-%COMP%], .mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-time[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#707070;font-family:var(--regular-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;align-self:center}@media only screen and (max-width: 767px){.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]{display:inline-block}}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{height:21px;font-size:12px;font-weight:500;display:flex;align-items:center;justify-content:center}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]   .pi-circle-fill[_ngcontent-%COMP%]{font-size:8px}.mobile-new-order-list[_ngcontent-%COMP%]   .orders__order-action[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:40px;color:#204e6e;text-transform:uppercase;display:flex;align-items:center}.mobile-new-order-list[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:-20px}.mobile-new-order-list[_ngcontent-%COMP%]     .p-paginator{background:#fafafa;color:#6c757d;border:solid #e9ecef;border-width:0;padding:.5rem;border-radius:3px}.mobile-new-order-list[_ngcontent-%COMP%]     .p-paginator .pi{color:#1a445e}.mobile-new-order-list[_ngcontent-%COMP%]     .pi-refresh:before{color:#1d4c69!important;font-size:20px;margin-right:5px;margin-bottom:4px}.mobile-new-order-list[_ngcontent-%COMP%]   .product-nums[_ngcontent-%COMP%]{font-family:var(--regular-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem 2rem;gap:10px}.mobile-new-order-list[_ngcontent-%COMP%]     .p-breadcrumb ul li:last-child .p-menuitem-icon{margin:0 .5rem;font-weight:700;color:#323232}@media screen and (min-width: 768px){.mobile-new-order-list[_ngcontent-%COMP%]   .hidden-navbar[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem;gap:10px}}@media screen and (max-width: 767px){.mobile-new-order-list[_ngcontent-%COMP%]   .hidden-navbar[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem;gap:10px;margin-top:150px!important}}.mobile-new-order-list[_ngcontent-%COMP%]   .view-detail[_ngcontent-%COMP%]{font-family:var(--medium-font)!important}.mobile-new-order-list[_ngcontent-%COMP%]   .order-text[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-size:12px;font-style:normal;font-weight:400;line-height:24px}.new-order-list[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]{margin:20px 0}@media screen and (max-width: 767px){.new-order-list[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]{margin-top:40px}.new-order-list[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{margin-top:200px!important}}.new-order-list[_ngcontent-%COMP%]   .orders-wrapper[_ngcontent-%COMP%]{border:1px solid #E4E7E9}.new-order-list[_ngcontent-%COMP%]   .orders__heading[_ngcontent-%COMP%]{padding:20px 24px;font-size:18px;font-weight:500;font-family:var(--medium-font)!important}.new-order-list[_ngcontent-%COMP%]   .orders__subHeading[_ngcontent-%COMP%]{color:#2d2d2d;font-size:12px;font-weight:500;line-height:18px;background-color:#f2f4f5;padding:10px 24px;border:1px solid #E4E7E9;text-transform:uppercase;font-family:var(--medium-font)!important}.new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]{padding:8px 24px}.new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:100%;padding:16px 0}.new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:not(:last-of-type){border-bottom:1px solid #E4E7E9}.new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]{position:relative;top:20%}.new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{color:var(--header_bgcolor);font-size:19px;cursor:pointer}.new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]{font-size:16px;font-weight:400;line-height:24px;letter-spacing:normal;display:flex;justify-content:space-between;min-width:245px}.new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#204e6e;font-family:var(--medium-font)!important}.new-order-list[_ngcontent-%COMP%]   .orders__order-number[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .orders__order-count[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#2d2d2d;font-family:var(--medium-font)!important}.new-order-list[_ngcontent-%COMP%]   .orders__order-date[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .orders__order-time[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#a3a3a3;font-family:var(--regular-font)!important}.new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-basis:300px}@media only screen and (max-width: 767px){.new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]{display:inline-block}}.new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{height:21px;font-size:12px;font-weight:500;display:flex;align-items:center}.new-order-list[_ngcontent-%COMP%]   .orders__order-status[_ngcontent-%COMP%]   .pi-circle-fill[_ngcontent-%COMP%]{font-size:8px}.new-order-list[_ngcontent-%COMP%]   .orders__order-action[_ngcontent-%COMP%]{font-size:14px;font-weight:700;line-height:40px;color:#204e6e;text-transform:uppercase;display:flex;align-items:center}.new-order-list[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.new-order-list[_ngcontent-%COMP%]     .p-paginator{background:#fafafa;color:#6c757d;border:solid #e9ecef;border-width:0;padding:.5rem;border-radius:3px}.new-order-list[_ngcontent-%COMP%]     .p-paginator .pi{color:#1a445e}.new-order-list[_ngcontent-%COMP%]     .pi-refresh:before{color:#1d4c69!important;font-size:20px}.new-order-list[_ngcontent-%COMP%]   .product-nums[_ngcontent-%COMP%]{font-family:var(--regular-font)!important}.new-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem 2rem;gap:10px}.new-order-list[_ngcontent-%COMP%]     .p-breadcrumb ul li:last-child .p-menuitem-icon{margin:0 .5rem;font-weight:700;color:#323232}@media screen and (min-width: 768px){.new-order-list[_ngcontent-%COMP%]   .hidden-navbar[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem;gap:10px}}@media screen and (max-width: 767px){.new-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{margin-top:200px!important}.new-order-list[_ngcontent-%COMP%]   .hidden-navbar[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px;background-color:#efeded;padding:1rem;gap:10px;margin-top:80px!important}}.new-order-list[_ngcontent-%COMP%]   .view-detail[_ngcontent-%COMP%], .new-order-list[_ngcontent-%COMP%]   .order-text[_ngcontent-%COMP%]{font-family:var(--medium-font)!important}.old-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{width:100%}.old-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:37px;height:48px;object-fit:contain}.old-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]{position:relative;top:20%}.old-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .old-order-list[_ngcontent-%COMP%]   .orders-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   .right-icon[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{color:var(--header_bgcolor);font-size:19px;cursor:pointer}.old-order-list[_ngcontent-%COMP%]   .refresh-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:-20px}.old-order-list[_ngcontent-%COMP%]     .p-paginator{background:#fafafa;color:#6c757d;border:solid #e9ecef;border-width:0;padding:.5rem;border-radius:3px}.old-order-list[_ngcontent-%COMP%]     .p-paginator .pi{color:#1a445e}.old-order-list[_ngcontent-%COMP%]     .pi-refresh:before{color:#1d4c69!important;font-size:20px;margin-right:5px;margin-bottom:4px}.old-order-list[_ngcontent-%COMP%]   .order-border[_ngcontent-%COMP%]{border-bottom:1px solid rgba(163,163,163,.25)}.old-order-list[_ngcontent-%COMP%]   .order-heading[_ngcontent-%COMP%]{margin-bottom:30px;font-size:28px;font-weight:700;font-family:var(--medium-font)!important}.old-order-list[_ngcontent-%COMP%]   .product-nums[_ngcontent-%COMP%]{font-family:var(--regular-font)!important}.old-order-list[_ngcontent-%COMP%]   .order-date[_ngcontent-%COMP%], .old-order-list[_ngcontent-%COMP%]   .order-time[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#a3a3a3;font-family:var(--regular-font)!important}.old-order-list[_ngcontent-%COMP%]   .order-number[_ngcontent-%COMP%]{font-size:15px;font-weight:500;font-family:var(--medium-font)!important}.old-order-list[_ngcontent-%COMP%]   .order-currency[_ngcontent-%COMP%]{font-size:11.34px;font-weight:700;font-family:var(--medium-font)!important}.old-order-list[_ngcontent-%COMP%]   .order-price[_ngcontent-%COMP%]{font-size:17.82px;font-weight:700;font-family:var(--medium-font)!important;margin-left:5px}.old-order-list[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{height:21px}.old-order-list[_ngcontent-%COMP%]   .pi-circle-fill[_ngcontent-%COMP%]{font-size:6.56px}.old-order-list[_ngcontent-%COMP%]   .order-text[_ngcontent-%COMP%]{font-size:10px;font-weight:500;font-family:var(--medium-font)!important}@media screen and (max-width: 768px){.old-order-list[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%], .old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{margin-top:0!important}.old-order-list[_ngcontent-%COMP%]   .order-heading[_ngcontent-%COMP%]{font-size:20px!important}.old-order-list[_ngcontent-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page{min-width:15px!important}.old-order-list[_ngcontent-%COMP%]     button.p-ripple{min-width:21px!important}}.old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]{background-color:#efeded;padding:1rem}.old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{padding:0 6px;margin:auto 0}.old-order-list[_ngcontent-%COMP%]   .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;font-size:15px}.old-order-list[_ngcontent-%COMP%]     .p-breadcrumb ul li:last-child .p-menuitem-icon{margin:0 .5rem;font-weight:700;color:#323232}@media only screen and (min-width: 1201px) and (max-width: 1700px){.old-order-list[_ngcontent-%COMP%]   .orders-top[_ngcontent-%COMP%]{margin-top:0!important}}@media only screen and (min-width: 1701px){.old-order-list[_ngcontent-%COMP%]   .orders-top[_ngcontent-%COMP%]{margin-top:0!important}}.view-details-btn[_ngcontent-%COMP%]{color:#204e6e;font-weight:700;font-family:var(--medium-font)}@media only screen and (min-width: 1701px){.order-bottom[_ngcontent-%COMP%]{margin-bottom:368px!important}}.orders-container[_ngcontent-%COMP%]{background-color:#fff}.btn[_ngcontent-%COMP%]{padding:6px}"]})}return e})()}];let U=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=n.oAB({type:e});static \u0275inj=n.cJS({imports:[d.ez,m.Bz.forChild(L),g.aw,C.U,O.s,P.W]})}return e})()}}]);