{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { Subject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/store.service\";\nimport * as i2 from \"@core/services/token.service\";\nimport * as i3 from \"ngx-cookie-service\";\nexport class CommonService {\n  constructor(store, authTokenService, platformId, cookieService) {\n    this.store = store;\n    this.authTokenService = authTokenService;\n    this.platformId = platformId;\n    this.cookieService = cookieService;\n    this.isShowSearchBox = new Subject();\n  }\n  logOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet('');\n    if (isPlatformBrowser(this.platformId)) {\n      this.cookieService.delete('authToken', window.location.hostname);\n    }\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    sessionStorage.clear();\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n    }\n  }\n  static #_ = this.ɵfac = function CommonService_Factory(t) {\n    return new (t || CommonService)(i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i2.AuthTokenService), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i3.CookieService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CommonService,\n    factory: CommonService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "isPlatformBrowser", "Subject", "CommonService", "constructor", "store", "authTokenService", "platformId", "cookieService", "isShowSearchBox", "logOut", "setStoreData", "authTokenSet", "delete", "window", "location", "hostname", "set", "localStorage", "removeItem", "sessionStorage", "clear", "setItem", "localStoreNames", "length", "notifications", "unreadNotifications", "shipping", "payment", "promo", "steps", "profile", "orderId", "JSON", "stringify", "_", "i0", "ɵɵinject", "i1", "StoreService", "i2", "AuthTokenService", "i3", "CookieService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\common.service.ts"], "sourcesContent": ["import {Inject, Injectable, PLATFORM_ID} from '@angular/core';\r\nimport {StoreService} from \"@core/services/store.service\";\r\nimport {AuthTokenService} from \"@core/services/token.service\";\r\nimport {CookieService} from \"ngx-cookie-service\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {Subject} from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CommonService {\r\n  isShowSearchBox = new Subject<boolean>();\r\n  constructor(private store: StoreService,\r\n              private authTokenService: AuthTokenService,\r\n              @Inject(PLATFORM_ID) private platformId: any,\r\n              private cookieService: CookieService) {\r\n  }\r\n\r\n  logOut(): void {\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet('');\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.cookieService.delete('authToken', window.location.hostname);\r\n    }\r\n\r\n    this.store.set('cartProducts', '');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n\r\n\r\n    sessionStorage.clear();\r\n\r\n    this.store.set('profile', '');\r\n    this.store.set('cartProducts', '');\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0,\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null,\r\n      });\r\n    } else {\r\n\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n      localStorage.removeItem('refreshToken');\r\n      localStorage.removeItem('auth_enc');\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAA4BA,WAAW,QAAO,eAAe;AAI7D,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,OAAO,QAAO,MAAM;;;;;AAK5B,OAAM,MAAOC,aAAa;EAExBC,YAAoBC,KAAmB,EACnBC,gBAAkC,EACbC,UAAe,EACpCC,aAA4B;IAH5B,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACK,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,aAAa,GAAbA,aAAa;IAJjC,KAAAC,eAAe,GAAG,IAAIP,OAAO,EAAW;EAKxC;EAEAQ,MAAMA,CAAA;IACJ,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACL,gBAAgB,CAACM,YAAY,CAAC,EAAE,CAAC;IACtC,IAAIX,iBAAiB,CAAC,IAAI,CAACM,UAAU,CAAC,EAAE;MACtC,IAAI,CAACC,aAAa,CAACK,MAAM,CAAC,WAAW,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC;;IAGlE,IAAI,CAACX,KAAK,CAACY,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;IAGnCC,cAAc,CAACC,KAAK,EAAE;IAEtB,IAAI,CAAChB,KAAK,CAACY,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCJ,YAAY,CAACI,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;EAC3C;EAEAX,YAAYA,CAAA;IACV,IAAI,IAAI,CAACN,KAAK,CAACkB,eAAe,CAACC,MAAM,EAAE;MAErC,IAAI,CAACnB,KAAK,CAACY,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,eAAe,EAAE;QAC9BQ,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAACrB,KAAK,CAACY,GAAG,CAAC,cAAc,EAAE;QAC7BU,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MAELd,YAAY,CAACI,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCJ,YAAY,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCJ,YAAY,CAACI,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCJ,YAAY,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCJ,YAAY,CAACI,OAAO,CAAC,cAAc,EAAEW,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MACxDhB,YAAY,CAACI,OAAO,CAAC,oBAAoB,EAAEW,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9DhB,YAAY,CAACI,OAAO,CAAC,iBAAiB,EAAEW,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3DhB,YAAY,CAACI,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MACtCJ,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;MACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;;EAEvC;EAAC,QAAAgB,CAAA,G;qBA/DUhC,aAAa,EAAAiC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,QAAA,CAIJrC,WAAW,GAAAoC,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAJpBzC,aAAa;IAAA0C,OAAA,EAAb1C,aAAa,CAAA2C,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}