{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { SharedModule } from \"../../shared/modules/shared.module\";\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { RippleModule } from \"primeng/ripple\";\nimport { PaymentCartComponent } from './components/payment-cart/payment-cart.component';\nimport { DeliveryMethodCartComponent } from './components/delivery-method-cart/delivery-method-cart.component';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { OrderSummaryCartComponent } from './components/order-summary-cart/order-summary-cart.component';\nimport { DynamicDialogModule } from 'primeng/dynamicdialog';\nimport { PaymentDialogComponent } from './components/payment-dialog/payment-dialog.component';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { InputMaskModule } from \"primeng/inputmask\";\nimport { CheckboxModule } from \"primeng/checkbox\";\nimport { OrderPlacedComponent } from './components/order-placed/order-placed.component';\nimport { PaymentErrorDialogComponent } from './components/payment-error-dialog/payment-error-dialog.component';\nimport { PaymentWaitingDialogComponent } from './components/payment-waiting-dialog/payment-waiting-dialog.component';\nlet CheckoutModule = class CheckoutModule {};\nCheckoutModule = __decorate([NgModule({\n  declarations: [IndexComponent, PaymentCartComponent, DeliveryMethodCartComponent, OrderSummaryCartComponent, PaymentDialogComponent, OrderPlacedComponent, PaymentErrorDialogComponent, PaymentWaitingDialogComponent],\n  imports: [CommonModule, SharedModule, RouterModule.forChild(routes), RippleModule, RadioButtonModule, DropdownModule, DynamicDialogModule, SelectButtonModule, InputMaskModule, CheckboxModule]\n})], CheckoutModule);\nexport { CheckoutModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IndexComponent", "SharedModule", "RouterModule", "routes", "RippleModule", "PaymentCartComponent", "DeliveryMethodCartComponent", "RadioButtonModule", "DropdownModule", "OrderSummaryCartComponent", "DynamicDialogModule", "PaymentDialogComponent", "SelectButtonModule", "InputMaskModule", "CheckboxModule", "OrderPlacedComponent", "PaymentErrorDialogComponent", "PaymentWaitingDialogComponent", "CheckoutModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\checkout.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport {SharedModule} from \"../../shared/modules/shared.module\";\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {RippleModule} from \"primeng/ripple\";\r\nimport { PaymentCartComponent } from './components/payment-cart/payment-cart.component';\r\nimport { DeliveryMethodCartComponent } from './components/delivery-method-cart/delivery-method-cart.component';\r\nimport {RadioButtonModule} from 'primeng/radiobutton';\r\nimport {DropdownModule} from 'primeng/dropdown';\r\nimport { OrderSummaryCartComponent } from './components/order-summary-cart/order-summary-cart.component';\r\nimport {DynamicDialogModule} from 'primeng/dynamicdialog';\r\nimport { PaymentDialogComponent } from './components/payment-dialog/payment-dialog.component';\r\nimport {SelectButtonModule} from 'primeng/selectbutton';\r\nimport {InputMaskModule} from \"primeng/inputmask\";\r\nimport {CheckboxModule} from \"primeng/checkbox\";\r\nimport { OrderPlacedComponent } from './components/order-placed/order-placed.component';\r\nimport { PaymentErrorDialogComponent } from './components/payment-error-dialog/payment-error-dialog.component';\r\nimport { PaymentWaitingDialogComponent } from './components/payment-waiting-dialog/payment-waiting-dialog.component';\r\n\r\n@NgModule({\r\n    declarations: [\r\n        IndexComponent,\r\n        PaymentCartComponent,\r\n        DeliveryMethodCartComponent,\r\n        OrderSummaryCartComponent,\r\n        PaymentDialogComponent,\r\n        OrderPlacedComponent,\r\n        PaymentErrorDialogComponent,\r\n        PaymentWaitingDialogComponent\r\n    ],\r\n    imports: [\r\n        CommonModule,\r\n        SharedModule,\r\n        RouterModule.forChild(routes),\r\n        RippleModule,\r\n        RadioButtonModule,\r\n        DropdownModule,\r\n        DynamicDialogModule,\r\n        SelectButtonModule,\r\n        InputMaskModule,\r\n        CheckboxModule\r\n    ]\r\n})\r\nexport class CheckoutModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAAQC,YAAY,QAAO,oCAAoC;AAC/D,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAASC,yBAAyB,QAAQ,8DAA8D;AACxG,SAAQC,mBAAmB,QAAO,uBAAuB;AACzD,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAAQC,kBAAkB,QAAO,sBAAsB;AACvD,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,2BAA2B,QAAQ,kEAAkE;AAC9G,SAASC,6BAA6B,QAAQ,sEAAsE;AA0B7G,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,GAAAC,UAAA,EAxB1BrB,QAAQ,CAAC;EACNsB,YAAY,EAAE,CACVpB,cAAc,EACdK,oBAAoB,EACpBC,2BAA2B,EAC3BG,yBAAyB,EACzBE,sBAAsB,EACtBI,oBAAoB,EACpBC,2BAA2B,EAC3BC,6BAA6B,CAChC;EACDI,OAAO,EAAE,CACLtB,YAAY,EACZE,YAAY,EACZC,YAAY,CAACoB,QAAQ,CAACnB,MAAM,CAAC,EAC7BC,YAAY,EACZG,iBAAiB,EACjBC,cAAc,EACdE,mBAAmB,EACnBE,kBAAkB,EAClBC,eAAe,EACfC,cAAc;CAErB,CAAC,C,EACWI,cAAc,CAAI;SAAlBA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}