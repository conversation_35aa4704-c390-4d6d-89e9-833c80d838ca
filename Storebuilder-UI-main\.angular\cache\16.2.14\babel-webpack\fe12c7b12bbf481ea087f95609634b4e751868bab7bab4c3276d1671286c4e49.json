{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { AddressComponent } from './components/address/address.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DetailsComponent } from './components/details/details.component';\nimport { InputMaskModule } from 'primeng/inputmask';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { AddressListComponent } from './components/address-list/address-list.component';\nimport { VerifyUserComponent } from './components/verify-user/verify-user.component';\nimport { VerifyMobileComponent } from './components/verify-mobile/verify-mobile.component';\nimport { VerifyOtpComponent } from './components/verify-otp/verify-otp.component';\nimport { ConfirmationDeleteDialogComponent } from '../../shared/modals/confirmation-delete-dialog/confirmation-delete-dialog.component';\nimport { GoogleMapsModule } from \"@angular/google-maps\";\nimport { NgxGpAutocompleteModule } from \"@angular-magic/ngx-gp-autocomplete\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { RadioButtonModule } from \"primeng/radiobutton\";\nimport { NgToggleModule } from \"ng-toggle-button\";\nimport { NgxIntlTelInputModule } from \"ngx-intl-tel-input-gg\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { ProgressSpinnerModule } from \"primeng/progressspinner\";\nimport { DialogModule } from \"primeng/dialog\";\nimport { MobileAddressMapComponent } from './components/mobile-address-map/mobile-address-map.component';\nimport { AddressLabelComponent } from './components/modals/address-label/address-label.component';\nimport { HelpComponent } from './components/help/help.component';\nlet AccountModule = class AccountModule {};\nAccountModule = __decorate([NgModule({\n  declarations: [IndexComponent, AddressComponent, DetailsComponent, AddressListComponent, VerifyUserComponent, VerifyMobileComponent, VerifyOtpComponent, MobileAddressMapComponent, AddressLabelComponent, HelpComponent],\n  imports: [PasswordModule, InputTextModule, InputMaskModule, ReactiveFormsModule, CommonModule, SharedModule, FormsModule, ReactiveFormsModule, NgxGpAutocompleteModule.forRoot({\n    loaderOptions: {\n      apiKey: localStorage.getItem('mapKey') ?? '',\n      libraries: ['places']\n    }\n  }), RouterModule.forChild(routes), GoogleMapsModule, ReactiveFormsModule, TranslateModule, DropdownModule, RadioButtonModule, NgToggleModule, NgxIntlTelInputModule, MatIconModule, ProgressSpinnerModule, DialogModule, ConfirmationDeleteDialogComponent]\n})], AccountModule);\nexport { AccountModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IndexComponent", "RouterModule", "routes", "SharedModule", "AddressComponent", "FormsModule", "ReactiveFormsModule", "DetailsComponent", "InputMaskModule", "InputTextModule", "PasswordModule", "AddressListComponent", "VerifyUserComponent", "VerifyMobileComponent", "VerifyOtpComponent", "ConfirmationDeleteDialogComponent", "GoogleMapsModule", "NgxGpAutocompleteModule", "TranslateModule", "DropdownModule", "RadioButtonModule", "NgToggleModule", "NgxIntlTelInputModule", "MatIconModule", "ProgressSpinnerModule", "DialogModule", "MobileAddressMapComponent", "AddressLabelComponent", "HelpComponent", "AccountModule", "__decorate", "declarations", "imports", "forRoot", "loaderOptions", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "getItem", "libraries", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\account.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\r\nimport {CommonModule} from '@angular/common';\r\nimport {IndexComponent} from './components/index/index.component';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {routes} from \"./routes\";\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\nimport {AddressComponent} from './components/address/address.component';\r\n\r\nimport {FormsModule, ReactiveFormsModule} from '@angular/forms';\r\nimport {DetailsComponent} from './components/details/details.component';\r\nimport {InputMaskModule} from 'primeng/inputmask';\r\nimport {InputTextModule} from 'primeng/inputtext';\r\nimport {PasswordModule} from 'primeng/password';\r\nimport {AddressListComponent} from './components/address-list/address-list.component';\r\nimport {VerifyUserComponent} from './components/verify-user/verify-user.component';\r\nimport {VerifyMobileComponent} from './components/verify-mobile/verify-mobile.component';\r\nimport {VerifyOtpComponent} from './components/verify-otp/verify-otp.component';\r\nimport {ConfirmationDeleteDialogComponent} from '../../shared/modals/confirmation-delete-dialog/confirmation-delete-dialog.component';\r\nimport {GoogleMapsModule} from \"@angular/google-maps\";\r\nimport {NgxGpAutocompleteModule} from \"@angular-magic/ngx-gp-autocomplete\";\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {DropdownModule} from \"primeng/dropdown\";\r\nimport {RadioButtonModule} from \"primeng/radiobutton\";\r\nimport {NgToggleModule} from \"ng-toggle-button\";\r\nimport {NgxIntlTelInputModule} from \"ngx-intl-tel-input-gg\";\r\nimport {MatIconModule} from \"@angular/material/icon\";\r\nimport {ProgressSpinnerModule} from \"primeng/progressspinner\";\r\nimport {DialogModule} from \"primeng/dialog\";\r\nimport { MobileAddressMapComponent } from './components/mobile-address-map/mobile-address-map.component';\r\nimport { AddressLabelComponent } from './components/modals/address-label/address-label.component';\r\nimport { HelpComponent } from './components/help/help.component';\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    AddressComponent,\r\n    DetailsComponent,\r\n    AddressListComponent,\r\n    VerifyUserComponent,\r\n    VerifyMobileComponent,\r\n    VerifyOtpComponent,\r\n    MobileAddressMapComponent,\r\n    AddressLabelComponent,HelpComponent\r\n  ],\r\n  imports: [\r\n    PasswordModule,\r\n    InputTextModule,\r\n    InputMaskModule,\r\n    ReactiveFormsModule,\r\n    CommonModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    NgxGpAutocompleteModule.forRoot({\r\n      loaderOptions: {\r\n        apiKey: localStorage.getItem('mapKey') ?? '',\r\n        libraries: ['places']\r\n      }\r\n    }),\r\n    RouterModule.forChild(routes),\r\n    GoogleMapsModule,\r\n    ReactiveFormsModule,\r\n\r\n    TranslateModule,\r\n    DropdownModule,\r\n    RadioButtonModule,\r\n    NgToggleModule,\r\n    NgxIntlTelInputModule,\r\n    MatIconModule,\r\n    ProgressSpinnerModule,\r\n    DialogModule,\r\n    ConfirmationDeleteDialogComponent\r\n  ]\r\n})\r\nexport class AccountModule {\r\n}\r\n"], "mappings": ";AAAA,SAAQA,QAAQ,QAAO,eAAe;AACtC,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,cAAc,QAAO,oCAAoC;AACjE,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,MAAM,QAAO,UAAU;AAC/B,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAAQC,gBAAgB,QAAO,wCAAwC;AAEvE,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,gBAAgB,QAAO,wCAAwC;AACvE,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,oBAAoB,QAAO,kDAAkD;AACrF,SAAQC,mBAAmB,QAAO,gDAAgD;AAClF,SAAQC,qBAAqB,QAAO,oDAAoD;AACxF,SAAQC,kBAAkB,QAAO,8CAA8C;AAC/E,SAAQC,iCAAiC,QAAO,qFAAqF;AACrI,SAAQC,gBAAgB,QAAO,sBAAsB;AACrD,SAAQC,uBAAuB,QAAO,oCAAoC;AAC1E,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,iBAAiB,QAAO,qBAAqB;AACrD,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,qBAAqB,QAAO,uBAAuB;AAC3D,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,qBAAqB,QAAO,yBAAyB;AAC7D,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,yBAAyB,QAAQ,8DAA8D;AACxG,SAASC,qBAAqB,QAAQ,2DAA2D;AACjG,SAASC,aAAa,QAAQ,kCAAkC;AA2CzD,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GACzB;AADYA,aAAa,GAAAC,UAAA,EA1CzBhC,QAAQ,CAAC;EACRiC,YAAY,EAAE,CACZ/B,cAAc,EACdI,gBAAgB,EAChBG,gBAAgB,EAChBI,oBAAoB,EACpBC,mBAAmB,EACnBC,qBAAqB,EACrBC,kBAAkB,EAClBY,yBAAyB,EACzBC,qBAAqB,EAACC,aAAa,CACpC;EACDI,OAAO,EAAE,CACPtB,cAAc,EACdD,eAAe,EACfD,eAAe,EACfF,mBAAmB,EACnBP,YAAY,EACZI,YAAY,EACZE,WAAW,EACXC,mBAAmB,EACnBW,uBAAuB,CAACgB,OAAO,CAAC;IAC9BC,aAAa,EAAE;MACbC,MAAM,EAAEC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;MAC5CC,SAAS,EAAE,CAAC,QAAQ;;GAEvB,CAAC,EACFrC,YAAY,CAACsC,QAAQ,CAACrC,MAAM,CAAC,EAC7Bc,gBAAgB,EAChBV,mBAAmB,EAEnBY,eAAe,EACfC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,EACdC,qBAAqB,EACrBC,aAAa,EACbC,qBAAqB,EACrBC,YAAY,EACZV,iCAAiC;CAEpC,CAAC,C,EACWc,aAAa,CACzB;SADYA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}