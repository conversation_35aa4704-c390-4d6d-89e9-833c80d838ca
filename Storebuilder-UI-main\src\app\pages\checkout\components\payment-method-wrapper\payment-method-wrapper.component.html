<ng-container [ngSwitch]="featureFlag">

  <ng-container *ngSwitchCase="'LIGHTBOX'">
    <app-lightbox-payment *ngIf="lightboxConfig?.lightBoxURL" [config]="lightboxConfig"
      [lightBoxURL]="lightboxConfig.lightBoxURL" (onComplete)="onComplete.emit($event)" (onCancel)="onCancel.emit()"
      (onError)="onError.emit()"></app-lightbox-payment>
  </ng-container>
  <ng-container *ngSwitchCase="'APM'">
    <app-apm-payment *ngIf="apmConfig" [config]="apmConfig" (onComplete)="onComplete.emit($event)"
      (onCancel)="onCancel.emit()" (onError)="onError.emit()"></app-apm-payment>
  </ng-container>
</ng-container>