import {Component, Input, Output, EventEmitter, OnInit, forwardRef, SimpleChanges, ViewChild, ElementRef} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators, ControlValueAccessor, NG_VALUE_ACCESSOR, FormGroup, FormBuilder } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NgxIntlTelInputModule, CountryISO, PhoneNumberFormat, SearchCountryField } from 'ngx-intl-tel-input-gg';
@Component({
  selector: 'app-phone-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    NgxIntlTelInputModule
  ],
  templateUrl: './phone-input.component.html',
  styleUrls: ['./phone-input.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneInputComponent),
      multi: true
    }
  ]
})
export class PhoneInputComponent implements OnInit {
  @ViewChild('phoneInput', { read: ElementRef }) phoneInput!: ElementRef;
  @Input() maxLength: number = 12;
  @Input() selectedCountryISO: any;
  @Input() preferredCountries: CountryISO[] = [];
  @Input() placeholder: string = '';
  private _phoneNumber: string = '';
  rawValue: any;

  @Input()
  set alreadyAddedPhoneNumber(value: string) {
    if(value && typeof value != 'object'){
      this._phoneNumber =  value.slice(3)
      this.signInForm.controls.phoneNumber.setValue(this._phoneNumber)
    }

  }

  get alreadyAddedPhoneNumber(): string {
    return this._phoneNumber;
  }
  @Output() phoneNumberChange = new EventEmitter<any>();
  @Output() validationChange = new EventEmitter<boolean>();
  @Output() rawPhoneNumberChange = new EventEmitter<string>();

  PhoneNumberFormat = PhoneNumberFormat;
  SearchCountryField = SearchCountryField;
  signInForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.signInForm = this.fb.group({
      phoneNumber: ['', Validators.required]
    });
  }
  ngOnInit() {
    this.signInForm.controls.phoneNumber.valueChanges.subscribe(value => {
    const inputElement = this.phoneInput.nativeElement.querySelector('input');
      this.rawValue = inputElement?.value || '';
      this.rawPhoneNumberChange.emit(this.rawValue)
      this.phoneNumberChange.emit(value);
      this.validationChange.emit(this.signInForm.controls.phoneNumber.valid);
    });
  }
}
