{"ast": null, "code": "import { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport let SignInUpHeaderComponent = /*#__PURE__*/(() => {\n  class SignInUpHeaderComponent {\n    title;\n    img;\n    static ɵfac = function SignInUpHeaderComponent_Factory(t) {\n      return new (t || SignInUpHeaderComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignInUpHeaderComponent,\n      selectors: [[\"sign-in-up-header\"]],\n      inputs: {\n        title: \"title\",\n        img: \"img\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 7,\n      consts: [[1, \"sign-in-up-container\"], [1, \"sign-in-up-container__image\"], [\"alt\", \"login image\", 3, \"src\"], [1, \"sign-in-up-container__content\"], [1, \"sign-in-up-container__content--title\"], [1, \"signup-heading\"], [1, \"signup-desc\"]],\n      template: function SignInUpHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"h4\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"translate\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.img, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 3, ctx.title));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 5, \"signIn.signInText\"), \"\");\n        }\n      },\n      dependencies: [TranslateModule, i1.TranslatePipe],\n      styles: [\".sign-in-up-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin:20px 0}.sign-in-up-container__image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:90px;height:90px}.sign-in-up-container__content--title[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center}.sign-in-up-container__content--title[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:18px;font-weight:500;color:#212121;margin-bottom:8px}.sign-in-up-container__content--title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;color:#443f3f;line-height:18px;font-weight:500}@media screen and (max-width: 767px){.sign-in-up-container__content--title[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:16px!important;font-weight:500;color:#212121;font-family:var(--medium-font)!important;line-height:1.2}.sign-in-up-container__content--title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:11px!important;font-weight:400;color:#443f3f;font-family:var(--regular-font)!important;line-height:1.3}}\"]\n    });\n  }\n  return SignInUpHeaderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}