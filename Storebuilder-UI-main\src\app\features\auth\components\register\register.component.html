<section class="register" [ngClass]="{'register-mobile': isMobileLayout && screenWidth <= 767}">
  <div class="content-container">
    <div class="grid justify-content-between shadow-signin">
      <!-- Image - Desktop only -->
      <div class="image col-12 desktop-only">
        <img src="assets/images/registerLogo.svg" alt="" srcset="">
      </div>

      <div class="col-12 col-md-8 col-lg-6 bg-white pt-6 header-body">
        <!-- Mobile header -->
        <div class="mobile-header mobile-only">
          <img src="assets/images/signup.svg"/>
          <div class="head-desc">
            <h2 class="signup-heading">{{ "auth.registerPassword.title" | translate }}</h2>
            <span class="signup-desc">{{ "auth.registerPassword.desc" | translate }}</span>
          </div>
        </div>

        <!-- Desktop header -->
        <div class="desktop-only">
          <p class="signUp-heading">{{ "register.signUp" | translate }}</p>
          <p class="signUp-content">{{ "register.content" | translate }}</p>
        </div>

        <form autocomplete="new-password">
          <div class="p-fluid p-grid">
            <!-- Registration Method Tabs -->
            <div class="mt-3">
              <div class="registration-tabs">
                <button 
                  type="button"
                  class="tab-button"
                  [class.active]="selectedRegistrationMethod === 'phone'"
                  (click)="selectRegistrationMethod('phone')">
                  {{ "register.mobileNumber" | translate }}
                </button>
                <button 
                  type="button"
                  class="tab-button"
                  [class.active]="selectedRegistrationMethod === 'email'"
                  (click)="selectRegistrationMethod('email')">
                  {{ "auth.registerPassword.email" | translate }}
                </button>
              </div>
            </div>

            <!-- Phone Input Component -->
            <div class="mt-3" *ngIf="selectedRegistrationMethod === 'phone'">
              <app-phone-input
                [maxLength]="phoneInputLength"
                [selectedCountryISO]="CustomCountryISO"
                [preferredCountries]="preferredCountries"
                [placeholder]="customPlaceHolder"
                (phoneNumberChange)="onPhoneNumberChange($event)"
                (validationChange)="onPhoneValidationChange($event)"
                (rawPhoneNumberChange)="onRawPhoneNumberChange($event)"
                >
              </app-phone-input>
            </div>

            <!-- Email Input Component -->
            <div class="mt-3" *ngIf="selectedRegistrationMethod === 'email'">
              <app-email-input
                [emailRequired]="'true'"
                [floatingLabel]="false"
                [labelColor]="'#333'"
                (emailChange)="onEmailChange($event)"
                (validationChange)="onEmailValidationChange($event)">
              </app-email-input>
            </div>

            <div class="d-flex flex-column mb-5 btn-container">
              <button
                (click)="checkMobileExist()"
                [disabled]="!isFormValid"
                [label]="buttonLabel | translate"
                class="sign-up-btn"
                pButton
                type="button">
              </button>

              <p class="signin-agreement">
                {{ "signIn.AgreeTermsOne" | translate }}
                <a (click)="reloadCurrentPage(171, 'Terms and Conditions')">
                  {{ "signIn.AgreeTermsTwo" | translate }}
                </a>&nbsp;{{ "signIn.AgreeTermsThree" | translate }}
                <a (click)="reloadCurrentPage(170, 'Privacy policy')">
                  {{ "signIn.AgreeTermsFour" | translate }}
                </a>&nbsp;{{ "signIn.AgreeTermsFive" | translate }}.
              </p>

              <div class="new-customer-container">
                <p>{{ "register.alreadyHaveAccount" | translate }}</p>
                <a
                  routerLink="/login"
                  [label]="'register.login' | translate"
                  class="register-now p-field p-col-12"
                  pButton
                  type="button">
                </a>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<ng-container *ngIf="isRegisterModal">
  <app-register-user-modal [displayModal]="isRegisterModal" [phoneNumber]="phoneNumber"></app-register-user-modal>
</ng-container>
