{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID, effect } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport jwt_decode from \"jwt-decode\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"primeng/dialog\";\nimport * as i8 from \"primeng/badge\";\nimport * as i9 from \"primeng/overlaypanel\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"ngx-intl-tel-input-gg\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"../navbar/navbar.component\";\nimport * as i14 from \"../search/search.component\";\nconst _c0 = [\"op\"];\nfunction HeaderComponent_ng_container_0_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37);\n  }\n}\nfunction HeaderComponent_ng_container_0_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 38);\n  }\n}\nfunction HeaderComponent_ng_container_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelement(2, \"em\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.selectedTenant == null ? null : ctx_r5.selectedTenant.name, \" \");\n  }\n}\nfunction HeaderComponent_ng_container_0_select_22_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"selected\", country_r14.isoCode === ctx_r13.selectedCountry)(\"value\", country_r14.tenantId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", country_r14.name, \" \");\n  }\n}\nfunction HeaderComponent_ng_container_0_select_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 41, 42);\n    i0.ɵɵlistener(\"change\", function HeaderComponent_ng_container_0_select_22_Template_select_change_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const _r12 = i0.ɵɵreference(1);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.changeCountry(_r12.value));\n    });\n    i0.ɵɵtemplate(2, HeaderComponent_ng_container_0_select_22_option_2_Template, 2, 3, \"option\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.countries);\n  }\n}\nfunction HeaderComponent_ng_container_0_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"a\", 46);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_div_39_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.signIn());\n    });\n    i0.ɵɵelement(2, \"img\", 47);\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 1, \"header.signIn\"), \" \");\n  }\n}\nfunction HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      i0.ɵɵnextContext();\n      const _r19 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r19.hide());\n    });\n    i0.ɵɵelementStart(2, \"a\", 57);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r22);\n      i0.ɵɵnextContext();\n      const _r19 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r19.hide());\n    });\n    i0.ɵɵelementStart(6, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.openAccountAddress());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_9_listener() {\n      i0.ɵɵrestoreView(_r22);\n      i0.ɵɵnextContext();\n      const _r19 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r19.hide());\n    });\n    i0.ɵɵelementStart(10, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.showAccountDetails());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r22);\n      i0.ɵɵnextContext();\n      const _r19 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r19.hide());\n    });\n    i0.ɵɵelementStart(14, \"a\", 59);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.logOut());\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 4, \"header.yourOrders\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 6, \"header.yourAddresses\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 8, \"header.yourDetails\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 10, \"header.logout\"));\n  }\n}\nfunction HeaderComponent_ng_container_0_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 48)(2, \"p-overlayPanel\", 49, 50);\n    i0.ɵɵtemplate(4, HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template, 17, 12, \"ng-template\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 52);\n    i0.ɵɵlistener(\"mouseenter\", function HeaderComponent_ng_container_0_ng_container_40_Template_span_mouseenter_5_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const _r19 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(_r19.toggle($event));\n    });\n    i0.ɵɵelement(6, \"em\", 53);\n    i0.ɵɵelementStart(7, \"span\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵpipe(10, \"slice\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"appendTo\", \"body\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(9, 4, \"header.hi\"), \" \", i0.ɵɵpipeBind3(10, 6, ctx_r8.Name, 0, 6), \" \", ctx_r8.Name.length > 6 ? \"...\" : \"\", \" \");\n  }\n}\nfunction HeaderComponent_ng_container_0_ng_container_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 63)(3, \"div\", 64)(4, \"div\", 65);\n    i0.ɵɵelement(5, \"app-mtn-navbar\", 66);\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction HeaderComponent_ng_container_0_ng_container_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, HeaderComponent_ng_container_0_ng_container_41_div_1_Template, 6, 0, \"div\", 60);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.navbarData == null ? null : ctx_r9.navbarData.isActive);\n  }\n}\nfunction HeaderComponent_ng_container_0_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 67)(2, \"div\", 63)(3, \"div\", 64)(4, \"div\", 65);\n    i0.ɵɵelement(5, \"app-mtn-navbar\", 66);\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"mobile-header-template\": a0\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    \"pointer-events-none\": a0\n  };\n};\nfunction HeaderComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"header\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵtemplate(6, HeaderComponent_ng_container_0_img_6_Template, 1, 0, \"img\", 8);\n    i0.ɵɵtemplate(7, HeaderComponent_ng_container_0_img_7_Template, 1, 0, \"img\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 11)(10, \"g\", 12);\n    i0.ɵɵelement(11, \"path\", 13)(12, \"path\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"g\", 15);\n    i0.ɵɵelement(14, \"path\", 16)(15, \"circle\", 17)(16, \"path\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_Template_div_click_17_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!ctx_r32.isShowYello && (ctx_r32.showDialog = true));\n    });\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, HeaderComponent_ng_container_0_div_21_Template, 3, 1, \"div\", 20);\n    i0.ɵɵtemplate(22, HeaderComponent_ng_container_0_select_22_Template, 3, 1, \"select\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 22)(24, \"app-mtn-search\", 23);\n    i0.ɵɵlistener(\"onResult\", function HeaderComponent_ng_container_0_Template_app_mtn_search_onResult_24_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.isActiveSearch = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 24)(26, \"div\", 25)(27, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_Template_a_click_27_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.openWishlist());\n    });\n    i0.ɵɵelement(28, \"img\", 27);\n    i0.ɵɵelementStart(29, \"span\", 28);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_0_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.openCart());\n    });\n    i0.ɵɵelementStart(33, \"a\", 30)(34, \"span\", 31);\n    i0.ɵɵelement(35, \"img\", 32)(36, \"p-badge\", 33);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, HeaderComponent_ng_container_0_div_39_Template, 7, 3, \"div\", 34);\n    i0.ɵɵtemplate(40, HeaderComponent_ng_container_0_ng_container_40_Template, 11, 10, \"ng-container\", 0);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(41, HeaderComponent_ng_container_0_ng_container_41_Template, 2, 1, \"ng-container\", 35);\n    i0.ɵɵtemplate(42, HeaderComponent_ng_container_0_ng_template_42_Template, 6, 0, \"ng-template\", null, 36, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r10 = i0.ɵɵreference(43);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(20, _c1, ctx_r0.isMobileTemplate));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConf);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConf);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, (ctx_r0.selectedTenant == null ? null : ctx_r0.selectedTenant.tenantId) === \"3\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 14, \"header.deliverTo\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isShowYello && ctx_r0.selectedTenant);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isShowYello);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 16, \"header.wishList\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r0.cartListCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 18, \"header.cart\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.authToken);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.authToken);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.desktopView)(\"ngIfElse\", _r10);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_6_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 79);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_6_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 80);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtemplate(1, HeaderComponent_ng_container_1_div_6_img_1_Template, 1, 0, \"img\", 77);\n    i0.ɵɵtemplate(2, HeaderComponent_ng_container_1_div_6_img_2_Template, 1, 0, \"img\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r37.scConf);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.scConf);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_7_div_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"img\", 88);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r44.getImage(ctx_r44.selectedTenant == null ? null : ctx_r44.selectedTenant.flag), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_1_div_7_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r45.isShowYello && (ctx_r45.showDialog = true));\n    });\n    i0.ɵɵtemplate(1, HeaderComponent_ng_container_1_div_7_div_2_span_1_Template, 2, 1, \"span\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r43.isShowYello && ctx_r43.selectedTenant);\n  }\n}\nfunction HeaderComponent_ng_container_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵtemplate(2, HeaderComponent_ng_container_1_div_7_div_2_Template, 2, 1, \"div\", 83);\n    i0.ɵɵelementStart(3, \"div\", 84)(4, \"div\", 85);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_1_div_7_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r47.toggleSearchBar());\n    });\n    i0.ɵɵelement(5, \"em\", 86);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r38.isExpanded);\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    \"overflow-hidden\": a0\n  };\n};\nfunction HeaderComponent_ng_container_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"app-mtn-search\", 90);\n    i0.ɵɵlistener(\"onResult\", function HeaderComponent_ng_container_1_div_8_Template_app_mtn_search_onResult_1_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.isActiveSearch = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 91);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_container_1_div_8_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.toggleSearchBar());\n    });\n    i0.ɵɵelement(3, \"em\", 86);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"width-animation\", ctx_r39.isExpanded);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c3, !ctx_r39.isExpanded));\n  }\n}\nfunction HeaderComponent_ng_container_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 62)(2, \"div\", 63)(3, \"div\", 64)(4, \"div\", 65);\n    i0.ɵɵelement(5, \"app-mtn-navbar\", 66);\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction HeaderComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68)(2, \"div\", 69)(3, \"header\", 70)(4, \"div\", 5)(5, \"div\", 71);\n    i0.ɵɵtemplate(6, HeaderComponent_ng_container_1_div_6_Template, 3, 2, \"div\", 72);\n    i0.ɵɵtemplate(7, HeaderComponent_ng_container_1_div_7_Template, 6, 1, \"div\", 73);\n    i0.ɵɵtemplate(8, HeaderComponent_ng_container_1_div_8_Template, 4, 5, \"div\", 74);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, HeaderComponent_ng_container_1_div_9_Template, 6, 0, \"div\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.isMobileTemplate));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isExpanded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isExpanded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isExpanded && ctx_r1.isShowSearch);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive);\n  }\n}\nfunction HeaderComponent_ng_template_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"p\", 106);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"landingPage.noCountryFound\"));\n  }\n}\nfunction HeaderComponent_ng_template_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_ng_template_3_div_20_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const item_r54 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.setTenant(item_r54 == null ? null : item_r54.tenantId, item_r54));\n    });\n    i0.ɵɵelement(1, \"img\", 108);\n    i0.ɵɵelementStart(2, \"p\", 109);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r54 = ctx.$implicit;\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r53.countryFlagImages(item_r54 == null ? null : item_r54.flag), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r53.translateCountryName(item_r54 == null ? null : item_r54.name));\n  }\n}\nconst _c4 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction HeaderComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"img\", 94);\n    i0.ɵɵelementStart(2, \"h2\", 95);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 96);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 97)(9, \"span\", 98);\n    i0.ɵɵelement(10, \"em\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"form\", 99)(12, \"input\", 100);\n    i0.ɵɵlistener(\"input\", function HeaderComponent_ng_template_3_Template_input_input_12_listener($event) {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.searchCountries($event));\n    })(\"ngModelChange\", function HeaderComponent_ng_template_3_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.country = $event);\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, HeaderComponent_ng_template_3_div_17_Template, 4, 3, \"div\", 101);\n    i0.ɵɵelement(18, \"span\", 102);\n    i0.ɵɵelementStart(19, \"div\", 103);\n    i0.ɵɵtemplate(20, HeaderComponent_ng_template_3_div_20_Template, 4, 2, \"div\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 8, \"landingPage.selectCountry\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 10, \"landingPage.clickOnCountry\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(13, 12, \"landingPage.searchCountry\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.country)(\"ngModelOptions\", i0.ɵɵpureFunction0(16, _c4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 14, \"countries.C\\u00F4tedIvoire\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.countriesList == null ? null : ctx_r2.countriesList.length) === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.countriesList);\n  }\n}\nconst _c5 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nexport class HeaderComponent {\n  constructor(ngZone, store, router, languageService, translate, mainDataService, cd, cartService, authTokenService, messageService, cookieService, loaderService, tenantService, appDataService, productLogicService, permissionService, dialogService, commonService, $gaService, platformId) {\n    this.ngZone = ngZone;\n    this.store = store;\n    this.router = router;\n    this.languageService = languageService;\n    this.translate = translate;\n    this.mainDataService = mainDataService;\n    this.cd = cd;\n    this.cartService = cartService;\n    this.authTokenService = authTokenService;\n    this.messageService = messageService;\n    this.cookieService = cookieService;\n    this.loaderService = loaderService;\n    this.tenantService = tenantService;\n    this.appDataService = appDataService;\n    this.productLogicService = productLogicService;\n    this.permissionService = permissionService;\n    this.dialogService = dialogService;\n    this.commonService = commonService;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.isShowYello = environment.isStoreCloud;\n    this.subscription = [];\n    this.isActiveSearch = false;\n    this.isfirstTime = false;\n    this.isExpanded = false;\n    this.products = [];\n    this.authToken = '';\n    this.logo = '/Images/logo.png';\n    this.Name = '';\n    this.languages = new Array();\n    this.search = '';\n    this.countries = [];\n    this.isShop = false;\n    this.mobileScreen = false;\n    this.scConf = false;\n    this.showDialog = false;\n    this.isShowSearch = false;\n    this.merchantURL = environment.merchantURL;\n    this.allCountries = [];\n    this.cartListCount = 0;\n    this.cartListData = [];\n    this.country = '';\n    this._BaseURL = environment.apiEndPoint;\n    this.selectedCountry = 'EG';\n    this.zoomLevelClass = 'default-zoom';\n    this.isMobileTemplate = false;\n    this.isGoogleAnalytics = false;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.baseUrl = `${environment.apiEndPoint}`;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.screen.width;\n      if (this.screenWidth > 768) {\n        this.desktopView = true;\n      } else {\n        this.desktopView = false;\n      }\n      effect(() => {\n        this.showDialog = this.dialogService.dialogVisibility();\n      });\n    }\n    if (environment.isStoreCloud) {\n      this.scConf = true;\n      this.fetchGoogleTId();\n    } else {\n      this.scConf = false;\n    }\n  }\n  ngOnInit() {\n    this.selectedCountry = localStorage.getItem('isoCode');\n    this.innerWidth = window.innerWidth;\n    this.selectedLanguage = localStorage.getItem('lang') ? localStorage.getItem('lang') : this.defaultLanguage;\n    this.mainDataService.getCartLengthData().subscribe(data => {\n      this.cartListCount = data ?? 0;\n      if (this.cartListCount > 0) {\n        if (this.cartListCount > 9) {\n          this.cartListCount = '9+';\n        }\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n    this.mainDataService.getUserData().subscribe(data => {\n      if (data) {\n        let firstName = data.name.split(' ')[0];\n        this.Name = firstName;\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n    this.commonService.isShowSearchBox.subscribe(res => {\n      if (!this.desktopView) {\n        if (!res) {\n          this.isExpanded = false;\n        }\n        this.isShowSearch = res;\n      }\n    });\n    let user = this.store.get('profile');\n    if (user) {\n      this.mainDataService.setUserData(user);\n    }\n    if (this.innerWidth < 768) {\n      this.mobileScreen = true;\n    } else {\n      this.mobileScreen = false;\n    }\n    this.getLanguages();\n    this.getAllCountries();\n    if (this.appDataService?.layoutTemplate) {\n      this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    }\n    this.mainDataService.setBannerData({\n      isNavbarDataActive: this.navbarData?.isActive ?? false\n    });\n  }\n  onWindowScroll() {\n    if (this.authToken) {\n      if (isPlatformBrowser(this.platformId)) {\n        if (document.getElementById('profile')) {\n          const ele = document.getElementById('profile');\n          if (ele) {\n            ele.click();\n          }\n          this.op.hide();\n        }\n      }\n    }\n  }\n  searchCountries(event) {\n    if (this.country.length >= 1) {\n      const normalizedCountry = this.removeDiacritics(this.country.toLowerCase());\n      this.countriesList = this.allCountries.filter(item => this.removeDiacritics(item.name.toLowerCase()).includes(normalizedCountry));\n    } else if (this.country.length === 0) {\n      this.countriesList = this.allCountries;\n    }\n  }\n  removeDiacritics(text) {\n    return text.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n  }\n  countryFlagImages(img) {\n    return UtilityFunctions.verifyImageURL(img, this._BaseURL);\n  }\n  onCountrySelected(callingCode) {\n    if (callingCode == '233') {\n      return '2';\n    } else if (callingCode == '+256') {\n      return '1';\n    } else if (callingCode == '225') {\n      return '4';\n    } else if (callingCode == '20') {\n      return '3';\n    }\n    return '';\n  }\n  getMainData() {\n    const initialData = this.appDataService.initialData;\n    this.isShop = initialData.isShop;\n    this.store.set('isShop', this.isShop);\n  }\n  SetTenant() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.authToken = null;\n      const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant');\n      localStorage.clear();\n      localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '');\n      yield _this.logOut();\n      localStorage.setItem('tenantId', _this.selectedTenant);\n      localStorage.setItem('allCountryTenants', JSON.stringify(_this.countries));\n      _this.store.set('cartProducts', []);\n      localStorage.setItem('sessionId', '');\n      _this.store.set('SessionId', '');\n      _this.Name = '';\n      _this.resetStorage();\n    })();\n  }\n  getAllCountries() {\n    const tenants = this.appDataService.tenants;\n    if (tenants.records) {\n      const countries = tenants.records;\n      countries[0].selected = true;\n      const tenantId = localStorage.getItem('tenantId');\n      if (tenantId && tenantId !== '') {\n        this.selectedTenant = countries.find(country => country.tenantId == tenantId);\n      }\n      this.countries = countries;\n      this.countriesList = this.countries;\n      this.allCountries = this.countriesList;\n    }\n  }\n  changeCountry(val) {\n    console.log(val);\n    this.setTenant(val);\n  }\n  setupCountry() {\n    let tenantId = localStorage.getItem('tenantId');\n    this.isShop = localStorage.getItem('ShopId') != null;\n    let permission = 'not-granted';\n    if (window?.navigator?.geolocation) {\n      navigator?.permissions.query({\n        name: 'geolocation'\n      }).then(PermissionStatus => {\n        if (PermissionStatus.state == 'granted') {\n          permission = 'granted';\n        } else if (PermissionStatus.state == 'prompt') {\n          permission = 'not-granted';\n        } else {\n          permission = 'denied';\n        }\n        if (permission === 'not-granted') {\n          this.isfirstTime = true;\n          this.setupCountryWithLocation();\n        } else {\n          this.setupCountryWithTenant(Number(tenantId));\n        }\n      }, error => {});\n    }\n  }\n  setupCountryWithTenant(tenantId) {\n    let country = this.countries.find(x => x.tenantId === tenantId);\n    if (!country) {\n      country = this.countries[0];\n    }\n    country.selected = true;\n    if (this.selectedTenant != tenantId) {\n      this.selectedTenant = country.tenantId;\n      localStorage.setItem('tenantId', country.tenantId);\n      localStorage.setItem('allCountryTenants', JSON.stringify(this.countries));\n      location.reload();\n    } else {\n      this.selectedTenant = country.tenantId;\n      this.selectedTenant = country.tenantId;\n      localStorage.setItem('tenantId', country.tenantId);\n    }\n    this.cd.detectChanges();\n  }\n  setupCountryWithLocation() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (window?.navigator?.geolocation) {\n        this.loaderService.show();\n        window.navigator.geolocation.getCurrentPosition(position => {\n          if ('geolocation' in navigator) {\n            this.loaderService.show();\n            navigator.geolocation.getCurrentPosition(position => {\n              this.Lat = position.coords.latitude;\n              this.Lng = position.coords.longitude;\n              this.zoom = 8;\n              this.permission = true;\n              let latlng = new google.maps.LatLng(this.Lat, this.Lng);\n              this.googleMapAPiResponse(latlng);\n            }, error => {}, {\n              maximumAge: 60000,\n              timeout: 2000\n            });\n          }\n        }, error => {\n          const tenantId = localStorage.getItem('tenantId') ?? environment.defaultTenant;\n          this.setupCountryWithTenant(Number(tenantId));\n        });\n      }\n    }\n  }\n  googleMapAPiResponse(latlng) {\n    this.geoCoder.geocode({\n      'latLng': latlng\n    }, (results, status) => {\n      if (status == google.maps.GeocoderStatus.OK && results[0]) {\n        let isoCountry = 'UG';\n        results[0].address_components.forEach(obj => {\n          if (obj.types.includes('country')) isoCountry = obj.short_name;\n        });\n        let country = this.countries.find(x => x.isoCode === isoCountry);\n        this.loaderService.hide();\n        this.setupCountryWithTenant(country.tenantId ?? 0);\n      }\n    });\n  }\n  getAllCart() {\n    const cartData = {\n      cartId: localStorage.getItem('cartId'),\n      sessionId: localStorage.getItem('sessionId') ?? '',\n      applyTo: this.applyTo\n    };\n    if (cartData.sessionId) {\n      this.cartService.getCart(cartData).subscribe({\n        next: res => {\n          this.cartListCount = 0;\n          this.cartListData = [];\n          if (res.data?.records?.length) {\n            this.cartListCount = 0;\n            if (res.data.records[0].cartDetails.length) {\n              this.cartListCount = res.data.records[0].cartDetails.length;\n              this.cartListData = res.data.records[0].cartDetails;\n            }\n            if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n              this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n              this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n            }\n            this.mainDataService.setCartLenghtData(this.cartListCount);\n            this.mainDataService.setCartItemsData(this.cartListData);\n          } else {\n            this.mainDataService.setCartLenghtData(0);\n            this.mainDataService.setCartItemsData([]);\n          }\n          this.productLogicService.cartProductList = this.cartListData;\n        }\n      });\n    } else {\n      this.products = [];\n    }\n  }\n  compareCartProducts(products, storeProducts) {\n    if (products.length) {\n      products.forEach(item => {\n        storeProducts.forEach(data => {\n          if (item.specsProductId === data.specsProductId) {\n            this.products.push(item);\n          }\n        });\n      });\n    } else {\n      this.products = storeProducts;\n    }\n    localStorage.setItem('addedProducts', JSON.stringify(this.products));\n  }\n  getShipmentMethodByTenantId() {\n    if (this.permissionService.hasPermission('Shipment-Fee')) {\n      this.cartService.getShipmentMethodByTenantId().subscribe(res => {\n        if (res.success && res.data.length) {\n          this.applyTo = res.data[0].applyTo;\n          localStorage.setItem('apply-to', this.applyTo);\n          this.getAllCart();\n        }\n      });\n    } else {\n      localStorage.setItem('apply-to', '2');\n      this.applyTo = 2;\n      this.getAllCart();\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      let profile = localStorage.getItem('profile');\n      if (profile && profile !== '') {\n        this.getShipmentMethodByTenantId();\n      } else {\n        this.getAllCart();\n      }\n      this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n      if (!this.authToken) {\n        this.authToken = this.cookieService.get('authToken');\n        if (!this.authToken) {\n          let profile = localStorage.getItem('profile');\n          if (profile && profile !== '') {\n            profile = JSON.parse(profile);\n            this.authToken = profile.authToken.replace('bearer ', '');\n            const decoded = jwt_decode(this.authToken);\n            let days = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n            const dateNow = new Date();\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\n            this.cookieService.set('authToken', this.authToken, {\n              expires: dateNow,\n              path: '/',\n              sameSite: 'Strict'\n            });\n            localStorage.removeItem('isGuest');\n          }\n        }\n      }\n      this.subscription[2] = this.store.subscription('cartProducts').subscribe({\n        next: res => {\n          if (res) {\n            this.products = res;\n            this.cd.detectChanges();\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n      this.subscription[4] = this.store.subscription('mainData').subscribe({\n        next: res => {\n          let data = res?.find(obj => obj.key.toLowerCase() === 'logo');\n          if (data) this.logo = '/' + (data.displayName || this.logo);\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }, 1);\n  }\n  ngOnDestroy() {\n    this.subscription.forEach(sub => {\n      sub.unsubscribe();\n    });\n  }\n  resetStorage() {\n    localStorage.removeItem('decimalValue');\n    localStorage.removeItem('countryPhone');\n    localStorage.removeItem('PhoneLength');\n    localStorage.removeItem('currency');\n    localStorage.removeItem('customerAddressLandmarkRequired');\n    if (isPlatformBrowser(this.platformId)) {\n      window.location.reload();\n    }\n  }\n  logOut() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const tenantId = localStorage.getItem('tenantId') || '1';\n      const maintenanceMode = localStorage.getItem('maintenanceMode') ?? 'false';\n      const saveCookie = localStorage.getItem('save_cookie') || '';\n      const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant');\n      const GATrackingId = localStorage.getItem('GATrackingId') || '';\n      localStorage.clear();\n      localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '');\n      _this2.authToken = null;\n      sessionStorage.clear();\n      _this2.authTokenService.authTokenSet('');\n      _this2.cookieService.delete('authToken', '/');\n      _this2.store.set('profile', '');\n      _this2.mainDataService.setCartLenghtData(0);\n      _this2.mainDataService.setCartItemsData([]);\n      _this2.mainDataService.setUserData(null);\n      localStorage.setItem('sessionId', '');\n      localStorage.setItem('addedProducts', '');\n      localStorage.setItem('cartId', '');\n      _this2.store.set('cartProducts', []);\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n      localStorage.setItem('isGuest', 'true');\n      localStorage.setItem('tenantId', tenantId);\n      localStorage.setItem('maintenanceMode', maintenanceMode);\n      localStorage.setItem('save_cookie', saveCookie);\n      localStorage.setItem('GATrackingId', GATrackingId);\n      yield _this2.router.navigate(['/']);\n    })();\n  }\n  getLanguages() {\n    this.languageService.getLanguage().subscribe(res => {\n      if (res?.languages) {\n        let storedLang = localStorage.getItem('language');\n        if (!storedLang) {\n          let defaultLanguage = res.languages.find(x => x.isDefault);\n          if (!defaultLanguage && res.languages.length > 0) {\n            this.defaultLanguage = res.languages[0];\n          }\n          if (defaultLanguage) {\n            this.selectedLanguage = localStorage.getItem('lang') ? localStorage.getItem('lang') : '';\n          }\n        }\n        this.languages = res.languages;\n      }\n    });\n  }\n  changeLang(lang) {\n    localStorage.setItem('language', lang);\n    this.translate.use(lang);\n  }\n  setTenant(tenantId, country) {\n    let defaultLanguage = country.languages.filter(lang => lang.isDefault);\n    const tenantIdValue = localStorage.getItem('tenantId');\n    if (tenantIdValue !== tenantId) {\n      this.logOut();\n      this.languageService.setCurrentLang(defaultLanguage[0].code);\n      localStorage.setItem('tenantId', tenantId);\n      this.router.navigate(['/']).then(() => {\n        if (isPlatformBrowser(this.platformId)) {\n          window.location.reload();\n        }\n      });\n    } else {\n      this.showDialog = false;\n      this.dialogService.hideDialog();\n    }\n    this.userDetails = this.store.get('profile');\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_CHANGE_COUNTRY, '', 'SWITCH_COUNTRY', 1, true, {\n        \"country\": country.name,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        \"ip_Address\": this.store.get('userIP'),\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n        \"device_Id\": this.store.get('deviceInfo')?.deviceId\n      });\n    }\n  }\n  signIn() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_IN_UP, '', 'SIGNIN/SIGNUP', 1, true);\n    }\n    this.router.navigate(['/login']);\n  }\n  hideDialog() {\n    this.dialogService.hideDialog();\n  }\n  toggleSearchBar() {\n    this.isExpanded = !this.isExpanded;\n    if (this.isExpanded) {\n      this.isShowSearch = true;\n    }\n  }\n  getImage(imageLink) {\n    if (imageLink) {\n      return `${this.baseUrl}/Images/${imageLink}`;\n    } else {\n      return '';\n    }\n  }\n  fetchGoogleTId() {\n    this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\n      next: res => {\n        localStorage.setItem('GATrackingId', res.data ? res.data[0].value : '');\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  showAccountDetails() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_ACCOUNT_DETAILS, '', 'ACCOUNT_DETAILS', 1, true);\n    }\n    this.router.navigate(['/account']);\n  }\n  openCart() {\n    this.userDetails = this.store.getFromLocalStorage('profile');\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_CART_ICON, 'navigation', 'CART_ON_TOP_BANNER', 1, true, {\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n        \"ip_Address\": this.store.get('userIP'),\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n        \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n        \"session_ID\": localStorage.getItem('sessionId')\n      });\n    }\n    this.router.navigate(['/cart']);\n  }\n  openWishlist() {\n    this.userDetails = this.store.getFromLocalStorage('profile');\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_WISHLIST_HEADER, '', '', 1, true, {\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n    this.router.navigate(['/wishlist']);\n  }\n  openAccountAddress() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_ADDRESS, '', '', 1, true, {\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n    this.router.navigate(['/account/address']);\n  }\n  translateCountryName(name) {\n    debugger;\n    const translationKey = `countries.${name.replace(' ', '').replace(/'/g, '')}`;\n    return this.translate.instant(translationKey);\n  }\n  static #_ = this.ɵfac = function HeaderComponent_Factory(t) {\n    return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.TenantService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.ProductLogicService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HeaderComponent,\n    selectors: [[\"app-mtn-header\"]],\n    viewQuery: function HeaderComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.op = _t.first);\n      }\n    },\n    hostBindings: function HeaderComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function HeaderComponent_scroll_HostBindingHandler() {\n          return ctx.onWindowScroll();\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 4,\n    vars: 10,\n    consts: [[4, \"ngIf\"], [1, \"confirmation-modal\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"content\"], [1, \"v3-header\"], [1, \"d-flex\", \"flex-wrap\", \"justify-content-space-between\", 3, \"ngClass\"], [\"id\", \"header-zoom-container\", 1, \"main-header\"], [1, \"main-header__section\", \"d-flex\"], [1, \"w-50\", \"d-inline-flex\"], [\"alt\", \"Logo\", \"class\", \"d-flex header-logo align-items-center justify-content-center\", \"routerLink\", \"/\", \"src\", \"assets/icons/logo-marketplace.png\", 4, \"ngIf\"], [\"alt\", \"Logo\", \"class\", \"d-flex header-logo align-items-center justify-content-center\", \"routerLink\", \"/\", \"src\", \"assets/icons/white-logo.svg\", 4, \"ngIf\"], [1, \"w-50\", \"select-location\"], [\"height\", \"16\", \"id\", \"a559119926c5ebd4e4ff6e365c5da3c1\", \"viewBox\", \"0 0 15.999 16\", \"width\", \"15.999\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"data-name\", \"Group 1082\", \"id\", \"Group_1082\"], [\"d\", \"M0,0H16V16H0Z\", \"data-name\", \"Path 9826\", \"fill\", \"none\", \"id\", \"Path_9826\"], [\"d\", \"M0,0H16V16H0Z\", \"data-name\", \"Path 9827\", \"fill\", \"none\", \"id\", \"Path_9827\"], [\"data-name\", \"Group 1083\", \"id\", \"Group_1083\", \"transform\", \"translate(1.678 0.668)\"], [\"d\", \"M10.334,12.335a.667.667,0,0,1-.607-.391C9.272,10.944,7,5.875,7,4.335a3.333,3.333,0,1,1,6.666,0c0,1.54-2.271,6.609-2.727,7.609a.663.663,0,0,1-.607.391Zm0-10a2,2,0,0,0-2,2,27.475,27.475,0,0,0,2,5.684,27.461,27.461,0,0,0,2-5.684A2,2,0,0,0,10.334,2.335Z\", \"data-name\", \"Path 9828\", \"fill\", \"#fff\", \"id\", \"Path_9828\", \"transform\", \"translate(-4.012 -1.002)\"], [\"cx\", \"1.153\", \"cy\", \"1.153\", \"data-name\", \"Ellipse 15\", \"fill\", \"#fff\", \"id\", \"Ellipse_15\", \"r\", \"1.153\", \"transform\", \"translate(5.096 2.407)\"], [\"d\", \"M13.829,18H3.851a1.333,1.333,0,0,1-1.225-1.859l1.428-3.332A1.33,1.33,0,0,1,5.28,12h.549v1.333H5.28L3.851,16.669h9.978L12.4,13.336h-.571V12H12.4a1.33,1.33,0,0,1,1.225.809l1.428,3.333A1.333,1.333,0,0,1,13.829,18Z\", \"data-name\", \"Path 9829\", \"fill\", \"#fff\", \"id\", \"Path_9829\", \"transform\", \"translate(-2.517 -4.669)\"], [1, \"delivery\", \"cursor-pointer\", 3, \"ngClass\", \"click\"], [\"class\", \"deliver-to\", 4, \"ngIf\"], [\"class\", \"shadow-none border-light selector\", \"id\", \"language-dropdown\", \"name\", \"language\", 3, \"change\", 4, \"ngIf\"], [1, \"main-header__section\", \"d-inline-flex\", \"w-100\", \"search-bar\"], [1, \"d-flex\", \"w-100\", \"align-items-center\", \"justify-content-center\", 3, \"onResult\"], [1, \"main-header__section\", \"d-flex\", \"header-left-section\"], [1, \"d-inline-flex\", \"wishlist-icon\"], [1, \"d-flex\", \"mx-3\", \"cursor-pointer\", \"align-items-center\", \"justify-content-center\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/heart-empty.svg\"], [1, \"ml-1\"], [1, \"d-inline-flex\", \"cart-icon\", 3, \"click\"], [1, \"d-flex\", \"mx-3\", \"cursor-pointer\", \"align-items-center\", \"justify-content-center\"], [1, \"mx-3\", \"cursor-pointer\", 2, \"position\", \"relative\"], [\"alt\", \"No Image\", \"src\", \"assets/images/header/icon1.svg\", 1, \"mr-1\"], [1, \"badge-mobile\", 3, \"value\"], [\"class\", \"d-inline-flex unauth-signin\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"showNavBar\", \"\"], [\"alt\", \"Logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/logo-marketplace.png\", 1, \"d-flex\", \"header-logo\", \"align-items-center\", \"justify-content-center\"], [\"alt\", \"Logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/white-logo.svg\", 1, \"d-flex\", \"header-logo\", \"align-items-center\", \"justify-content-center\"], [1, \"deliver-to\"], [1, \"pi\", \"pi-angle-down\", \"text-white\", \"ml-3\"], [\"id\", \"language-dropdown\", \"name\", \"language\", 1, \"shadow-none\", \"border-light\", \"selector\", 3, \"change\"], [\"langSelect\", \"\"], [3, \"selected\", \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"selected\", \"value\"], [1, \"d-inline-flex\", \"unauth-signin\"], [1, \"d-flex\", \"flex-row\", \"align-items-center\", \"justify-content-center\", 3, \"click\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/user-account.svg\", 1, \"login-icon\", 2, \"margin-right\", \"10px\"], [1, \"actions\", \"d-inline-flex\"], [3, \"appendTo\"], [\"op\", \"\"], [\"pTemplate\", \"\"], [\"id\", \"profile\", 1, \"cursor-pointer\", \"d-flex\", \"mx-3\", \"cursor-pointer\", \"align-items-center\", \"justify-content-center\", \"user-name\", 3, \"mouseenter\"], [1, \"pi\", \"pi-user\", \"pi-white\"], [1, \"ml-2\", \"d-inline-flex\", \"flex-nowrap\", \"ml-2\", \"text-overflow-ellipsis\", \"w-6rem\", 2, \"white-space\", \"nowrap\"], [1, \"profile-menu\"], [1, \"my-2\", \"profile-menu-item\", 3, \"click\"], [\"routerLink\", \"/orders\", 1, \"cursor-pointer\", \"no-underline\", \"main-color\", \"font-size-15px\", \"regular-font\"], [1, \"cursor-pointer\", \"no-underline\", \"main-color\", \"font-size-15px\", \"regular-font\", 3, \"click\"], [1, \"cursor-pointer\", \"no-underline\", \"main-color\", \"font-size-15px\", \"regular-font\", \"logout-user\", 3, \"click\"], [\"class\", \"v3-header-cateogries\", 4, \"ngIf\"], [1, \"v3-header-cateogries\"], [1, \"header-category\"], [\"id\", \"header-zoom-container\"], [1, \"col-12\", \"px-0\", \"pb-0\", \"header-mtn\"], [1, \"flex\", \"flex-row\"], [1, \"category-navbar\", \"w-100\"], [1, \"header-category\", 2, \"top\", \"160px !important\"], [1, \"header-mobile-new\"], [1, \"v3-header\", 2, \"height\", \"75px !important\"], [3, \"ngClass\"], [1, \"main-header__section\", \"d-flex\", \"align-items-center\"], [\"class\", \"main-header__section__logo\", 4, \"ngIf\"], [\"class\", \"main-header__section__search\", 4, \"ngIf\"], [\"class\", \"search-bar\", 4, \"ngIf\"], [\"class\", \"v3-header-cateogries d-none\", 4, \"ngIf\"], [1, \"main-header__section__logo\"], [\"alt\", \"Logo\", \"class\", \"d-flex header-logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/new-logo-marketplace-mobile.svg\", 4, \"ngIf\"], [\"alt\", \"Logo\", \"class\", \"d-flex header-logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/white-logo.svg\", 4, \"ngIf\"], [\"alt\", \"Logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/new-logo-marketplace-mobile.svg\", 1, \"d-flex\", \"header-logo\"], [\"alt\", \"Logo\", \"routerLink\", \"/\", \"src\", \"assets/icons/white-logo.svg\", 1, \"d-flex\", \"header-logo\"], [1, \"main-header__section__search\"], [1, \"d-flex\", \"justify-content-end\"], [\"class\", \"main-header__section__search__flag\", 3, \"click\", 4, \"ngIf\"], [1, \"search-container\"], [1, \"search-icon\", 3, \"click\"], [1, \"pi\", \"pi-search\"], [1, \"main-header__section__search__flag\", 3, \"click\"], [1, \"flar-header-img\", 3, \"src\"], [1, \"search-bar\"], [1, \"search-bar-new\", 3, \"ngClass\", \"onResult\"], [1, \"search-icon\", \"search-icon-abs\", 3, \"click\"], [1, \"v3-header-cateogries\", \"d-none\"], [1, \"content\"], [\"alt\", \"No Image\", \"alt\", \"Logo\", \"src\", \"assets/images/logo-marketplace1.svg\", 1, \"mb-3\"], [1, \"mb-1\", \"select-country\"], [1, \"country-desp\", \"mb-3\"], [1, \"product-search\", \"p-inputgroup\", \"mb-3\"], [1, \"p-inputgroup-addon\", \"color\"], [\"autocomplete\", \"off\", \"novalidate\", \"\", 1, \"ng-untouched\", \"ng-pristine\", \"ng-valid\", 2, \"display\", \"contents\"], [\"autocapitalize\", \"off\", \"autocomplete\", \"new-password\", \"autocorrect\", \"off\", \"pinputtext\", \"\", \"spellcheck\", \"false\", \"type\", \"text\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"ng-untouched\", \"ng-pristine\", \"ng-valid\", 3, \"ngModel\", \"ngModelOptions\", \"placeholder\", \"input\", \"ngModelChange\"], [\"class\", \"col-12 text-center\", 4, \"ngIf\"], [1, \"line-border\"], [1, \"row\", \"countries-scoll\"], [\"class\", \"col-4 text-center mobile-show-countries\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"text-center\"], [1, \"no-found\"], [1, \"col-4\", \"text-center\", \"mobile-show-countries\", 3, \"click\"], [\"alt\", \"No Image\", 1, \"country-flag\", 3, \"src\"], [1, \"country-name\"]],\n    template: function HeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, HeaderComponent_ng_container_0_Template, 44, 24, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, HeaderComponent_ng_container_1_Template, 10, 7, \"ng-container\", 0);\n        i0.ɵɵelementStart(2, \"p-dialog\", 1);\n        i0.ɵɵlistener(\"visibleChange\", function HeaderComponent_Template_p_dialog_visibleChange_2_listener($event) {\n          return ctx.showDialog = $event;\n        })(\"onHide\", function HeaderComponent_Template_p_dialog_onHide_2_listener() {\n          return ctx.hideDialog();\n        });\n        i0.ɵɵtemplate(3, HeaderComponent_ng_template_3_Template, 21, 17, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.desktopView || !ctx.isMobileTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.desktopView && ctx.isMobileTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"visible\", ctx.showDialog)(\"breakpoints\", i0.ɵɵpureFunction0(9, _c5))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n      }\n    },\n    dependencies: [i4.PrimeTemplate, i7.Dialog, i8.Badge, i9.OverlayPanel, i10.NgClass, i10.NgForOf, i10.NgIf, i11.NativeElementInjectorDirective, i2.RouterLink, i12.ɵNgNoValidate, i12.NgSelectOption, i12.ɵNgSelectMultipleOption, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgControlStatusGroup, i12.NgModel, i12.NgForm, i13.NavbarComponent, i14.SearchComponent, i10.SlicePipe, i3.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.v3-header[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  z-index: 9999;\\n  background: #1a445e;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 74px;\\n  z-index: 1;\\n  background: var(--header_bgcolor);\\n  padding: 0 20px;\\n}\\n@media only screen and (min-width: 1701px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 24% !important;\\n    margin-right: 15px;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 40% !important;\\n    max-width: 40% !important;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 40% !important;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 24% !important;\\n    margin-right: 15px;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 40% !important;\\n    max-width: 40% !important;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 40% !important;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 100% !important;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 100% !important;\\n    margin-top: 12px !important;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 100% !important;\\n    justify-content: space-between;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1)   .select-location[_ngcontent-%COMP%] {\\n    justify-content: right;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 100% !important;\\n    max-width: 100% !important;\\n    padding-top: 15px;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 100% !important;\\n    padding-top: 15px;\\n    font-size: 12px;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]   .select-location[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    width: 120px;\\n  }\\n}\\n@media only screen and (max-width: 400px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 100% !important;\\n    padding-top: 15px;\\n    font-size: 10px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\\n    height: 160px;\\n    padding: 0 10px;\\n  }\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .mobile-header-template[_ngcontent-%COMP%] {\\n    height: 150px !important;\\n  }\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .header-left-section[_ngcontent-%COMP%] {\\n  width: 32%;\\n  margin-left: 5%;\\n  justify-content: flex-end;\\n  gap: 20px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .header-logo[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  width: 155px;\\n  height: 35px;\\n  border-radius: 3px;\\n  object-fit: contain;\\n  cursor: pointer;\\n  margin-left: -10px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .select-location[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem !important;\\n  margin-left: 30px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .select-location[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  text-align: left;\\n  letter-spacing: 0;\\n  color: #ffffff;\\n  font-size: 14px;\\n  font-weight: 300;\\n  width: 100px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .select-location[_ngcontent-%COMP%]   .location-dropdown[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 0;\\n  width: 100%;\\n  padding: 0;\\n  margin: 0;\\n  color: #ffffff;\\n  cursor: pointer;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .language-dropdown[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  border: none;\\n  color: white;\\n  text-decoration: none;\\n  background: var(--header_bgcolor);\\n  outline: none;\\n  font-weight: 300;\\n  margin-left: 25px;\\n  cursor: pointer;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .unauth-signin[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 130px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .unauth-signin[_ngcontent-%COMP%] {\\n    width: unset;\\n    max-width: unset;\\n  }\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .unauth-signin[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #fafafa !important;\\n  text-decoration: none;\\n  cursor: pointer;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .unauth-signin[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  width: 28px;\\n  font-size: 18px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .unauth-signin[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  width: 28px;\\n  font-size: 18px;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .wishlist-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #fafafa !important;\\n  text-decoration: none;\\n  white-space: pre;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .wishlist-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  vertical-align: middle !important;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .wishlist-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  font-size: 16px;\\n  vertical-align: middle !important;\\n}\\n@media only screen and (max-width: 767px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .wishlist-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px !important;\\n  }\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #fafafa !important;\\n  text-decoration: none;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 35px;\\n  font-size: 25px;\\n  color: #fafafa;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  min-width: 1rem;\\n  height: 1rem;\\n  line-height: 1rem;\\n  background: #ffcc00;\\n  color: #000;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  width: 35px;\\n  font-size: 25px;\\n  color: #fafafa;\\n}\\n.v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  min-width: 1rem;\\n  height: 1rem;\\n  line-height: 1rem;\\n  background: #ffcc00;\\n  color: #000;\\n}\\n.v3-header[_ngcontent-%COMP%]   .header-category[_ngcontent-%COMP%] {\\n  position: fixed;\\n  left: 0;\\n  top: 73px;\\n  width: 100%;\\n  background: #FFCB05;\\n  padding: 0 20px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .v3-header[_ngcontent-%COMP%]   .header-category[_ngcontent-%COMP%] {\\n    top: 150px;\\n  }\\n}\\n.v3-header[_ngcontent-%COMP%]   .header-category[_ngcontent-%COMP%]   .category-navbar[_ngcontent-%COMP%]     .navbar ul {\\n  overflow: hidden !important;\\n}\\n\\n  .p-overlay-badge .p-badge {\\n  top: 3px !important;\\n  right: 19px;\\n}\\n\\n.pi-white[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\na[_ngcontent-%COMP%]:hover {\\n  color: var(--header_bgcolor);\\n}\\n\\n.logout-user[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .login-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .signin-text[_ngcontent-%COMP%] {\\n    position: relative;\\n    top: 5px;\\n    color: white;\\n  }\\n  .cursor-pointer[_ngcontent-%COMP%] {\\n    margin-left: 0px !important;\\n    margin-right: 0 !important;\\n    color: white;\\n  }\\n  .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    height: 35px;\\n    border-radius: 3px;\\n    object-fit: contain;\\n  }\\n  .cart-label[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .navbar-mobile[_ngcontent-%COMP%] {\\n    display: contents;\\n  }\\n  .w-8rem[_ngcontent-%COMP%] {\\n    width: auto !important;\\n    margin-left: 10px !important;\\n    font-family: var(--regular-font);\\n  }\\n  .delivery[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-flow: column;\\n    font-family: var(--regular-font);\\n    width: 100px;\\n  }\\n  .delivery[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n    color: black;\\n  }\\n  .select-language[_ngcontent-%COMP%] {\\n    border: none;\\n    color: white;\\n    text-decoration: none;\\n    background: var(--header_bgcolor);\\n    outline: none;\\n    font-weight: 300;\\n    font-family: var(--regular-font);\\n    background-color: var(--header_bgcolor);\\n  }\\n  .sign-in-a[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .sign-in-a[_ngcontent-%COMP%]   .sign-in-a[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    margin-right: 10px;\\n  }\\n  a[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n    color: #ffffff;\\n  }\\n  .pi-shopping-cart[_ngcontent-%COMP%] {\\n    width: 31px;\\n    bottom: 5px;\\n    color: #ffffff;\\n    font-size: 26px;\\n  }\\n    .p-overlay-badge .p-badge {\\n    right: 16px !important;\\n  }\\n  .mb-3[_ngcontent-%COMP%] {\\n    margin-bottom: 15px !important;\\n  }\\n  .header[_ngcontent-%COMP%] {\\n    background-color: var(--header_bgcolor);\\n  }\\n  .d-flex.bg-main-color[_ngcontent-%COMP%] {\\n    background-color: var(--header_bgcolor);\\n  }\\n  select[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n  select.newSelectStyle[_ngcontent-%COMP%] {\\n    background: transparent;\\n    border: 0;\\n    width: 100%;\\n    padding: 0;\\n    margin: 0;\\n  }\\n  .header-margin[_ngcontent-%COMP%] {\\n    margin-top: 13px;\\n    margin-bottom: 13px !important;\\n  }\\n  .main-color[_ngcontent-%COMP%] {\\n    color: var(--header_bgcolor);\\n  }\\n  .mobile-header-color[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n  }\\n  .icon-deliver[_ngcontent-%COMP%] {\\n    align-items: center;\\n    gap: 0.5rem !important;\\n    margin-left: 30px;\\n  }\\n  .wishlist-top[_ngcontent-%COMP%] {\\n    margin-top: 5px;\\n  }\\n  .cart-top[_ngcontent-%COMP%] {\\n    margin-top: 5px;\\n  }\\n  .signin-margin[_ngcontent-%COMP%] {\\n    margin-right: 10px;\\n  }\\n  .badge-mobile[_ngcontent-%COMP%] {\\n    position: absolute;\\n    bottom: 12px;\\n    left: 5px;\\n  }\\n  .mobile-show-countries[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n    padding: 0.5rem;\\n    width: 50%;\\n  }\\n  .country-desp[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n  }\\n  .sell-yalla[_ngcontent-%COMP%] {\\n    right: 10px;\\n    position: relative;\\n  }\\n}\\n.badge-mobile[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 4px;\\n}\\n\\n.location[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.delivery[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-flow: column;\\n  font-family: var(--regular-font);\\n}\\n.delivery[_ngcontent-%COMP%]   .deliver-to[_ngcontent-%COMP%] {\\n  text-align: left;\\n  letter-spacing: 0px;\\n  color: #ffffff;\\n  font-size: 14px;\\n  font-weight: 300;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: baseline;\\n}\\n\\noption[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n\\nspan.p-inputgroup-addon.color[_ngcontent-%COMP%] {\\n  border-top-left-radius: 5px !important;\\n  border-bottom-left-radius: 5px !important;\\n  border: 1px solid #000000 !important;\\n  background: none;\\n  border-right: none !important;\\n  opacity: 0.6;\\n}\\n\\ninput.p-inputtext.p-component.p-element.ng-untouched.ng-pristine.ng-valid[_ngcontent-%COMP%] {\\n  width: 100%;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059) !important;\\n  border: 1px solid #000000 !important;\\n  padding: 0 0px;\\n  font-size: 13px;\\n  font-family: var(--medium-font) !important;\\n  border-left: none !important;\\n  border-top-left-radius: 0px !important;\\n  border-bottom-left-radius: 0px !important;\\n  height: 47px;\\n  opacity: 0.6;\\n}\\n\\n.country-flag[_ngcontent-%COMP%] {\\n  border-radius: 35px;\\n  width: 75px;\\n}\\n\\n.country-name[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  color: #000;\\n  text-align: center;\\n  text-overflow: ellipsis;\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.country-flag[_ngcontent-%COMP%]:hover {\\n  border: 5px solid #1d4c69;\\n  cursor: pointer;\\n}\\n\\n.no-found[_ngcontent-%COMP%] {\\n  font-size: 19px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.line-border[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 15px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.2);\\n}\\n\\n.country-desp[_ngcontent-%COMP%] {\\n  color: #a3a3a3;\\n  font-size: 14px;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.select-country[_ngcontent-%COMP%] {\\n  color: black;\\n  font-size: 22px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.deliver-color[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.d-inline-flex.language-dropdown[_ngcontent-%COMP%]   select#language-dropdown[_ngcontent-%COMP%]   option.ng-star-inserted[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 17px;\\n}\\n\\n.confirmation-modal[_ngcontent-%COMP%] {\\n  z-index: 9999;\\n  position: fixed;\\n}\\n\\n.selector[_ngcontent-%COMP%] {\\n  padding: 0px 8px;\\n  background: transparent;\\n  border: none;\\n  color: white;\\n}\\n\\n.selector[_ngcontent-%COMP%]:focus-visible {\\n  outline: none;\\n}\\n\\n@media (min-resolution: 120dpi) and (max-resolution: 130dpi) {\\n  \\n\\n  .select-location[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    min-width: -moz-fit-content;\\n    min-width: fit-content;\\n    margin-right: 12px;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    margin: 0px !important;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n  }\\n  .sell-on-yalla[_ngcontent-%COMP%] {\\n    min-width: 110px;\\n  }\\n}\\n@media (min-resolution: 144dpi) {\\n  \\n\\n  .select-location[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    min-width: -moz-fit-content;\\n    min-width: fit-content;\\n    margin-right: 12px;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    margin: 0px !important;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n  }\\n  .sell-on-yalla[_ngcontent-%COMP%] {\\n    min-width: 110px;\\n  }\\n}\\n.sell-on-yalla[_ngcontent-%COMP%] {\\n  border: 2px solid #ffffff;\\n  border-radius: 8px;\\n  padding: 4px 16px 4px 16px;\\n  font-family: var(--regular-font);\\n  position: relative;\\n  font-weight: 400;\\n  text-align: center;\\n  z-index: 3;\\n  margin-top: 6px;\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]:hover {\\n  background-color: #ffffff;\\n  color: #E9003A;\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]:hover   .sell-on-yalla-logo[_ngcontent-%COMP%] {\\n  background-image: url('sell-on-yalla-red.png');\\n}\\n.sell-on-yalla[_ngcontent-%COMP%]   .sell-on-yalla-logo[_ngcontent-%COMP%] {\\n  right: 2px;\\n  position: relative;\\n  top: 3px;\\n  background-image: url('sell-on-yalla.png');\\n  width: 13px;\\n  display: inline-block;\\n  height: 14px;\\n}\\n\\n.sell-yalla[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n}\\n\\n.sell-on-yalla-mobile[_ngcontent-%COMP%] {\\n  border: 2px solid #ffffff;\\n  border-radius: 8px;\\n  padding: 4px 16px 4px 16px;\\n  font-family: var(--regular-font);\\n  position: relative;\\n  font-weight: 400;\\n  text-align: center;\\n}\\n.sell-on-yalla-mobile[_ngcontent-%COMP%]   .sell-on-yalla-logo[_ngcontent-%COMP%] {\\n  right: 2px;\\n  position: relative;\\n  top: 3px;\\n  background-image: url('sell-on-yalla.png');\\n  width: 13px;\\n  display: inline-block;\\n  height: 14px;\\n}\\n\\n\\n\\n@media only screen and (min-device-width: 720px) and (max-device-width: 1280px) {\\n  .select-location[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    min-width: -moz-fit-content;\\n    min-width: fit-content;\\n    margin-right: 12px;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    margin: 0px !important;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n  }\\n  .sell-on-yalla[_ngcontent-%COMP%] {\\n    min-width: 110px;\\n  }\\n}\\n\\n\\n@media screen and (max-width: 1366px) {\\n  \\n\\n  .select-location[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    min-width: -moz-fit-content;\\n    min-width: fit-content;\\n    margin-right: 12px;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    margin: 0px !important;\\n  }\\n  .cart-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    min-width: 65px;\\n  }\\n  .sell-on-yalla[_ngcontent-%COMP%] {\\n    min-width: 110px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .main-header[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .select-location[_ngcontent-%COMP%] {\\n    justify-content: end;\\n  }\\n}\\n@media only screen and (min-width: 320px) and (max-width: 360px) {\\n  .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 100% !important;\\n    padding-top: 15px;\\n    zoom: 0.7 !important;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .header-mobile-new[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    height: 74px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n    justify-content: end;\\n    display: flex;\\n    position: relative;\\n    width: 50px;\\n    transition: width 0.3s ease-in-out;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-container.expanded[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-bar-new[_ngcontent-%COMP%] {\\n    padding: 0px;\\n    min-height: 40px;\\n    border: none;\\n    position: absolute;\\n    top: 0;\\n    right: 0px;\\n    width: 0;\\n    border-radius: 20px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .overflow-hidden[_ngcontent-%COMP%] {\\n    overflow: hidden;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .width-animation[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-container.expanded[_ngcontent-%COMP%]   .search-bar-new[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n    cursor: pointer;\\n    z-index: 1;\\n    position: relative;\\n    background: #FFCB05;\\n    padding: 5px;\\n    border-radius: 39px;\\n    height: 51px;\\n    border: 4px solid rgba(245, 245, 245, 0.9607843137);\\n    width: 51px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .search-icon-abs[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 10px;\\n    right: 12px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   em.pi.pi-search[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    display: flex;\\n    top: 9px;\\n    position: relative;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .flar-header-img[_ngcontent-%COMP%] {\\n    border-radius: 50px;\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .flag-top[_ngcontent-%COMP%] {\\n    right: 68px;\\n    position: absolute;\\n    top: 17px;\\n  }\\n}\\n.header-mobile-new[_ngcontent-%COMP%]   .header-logo[_ngcontent-%COMP%] {\\n  margin-top: 0px !important;\\n  width: 155px;\\n  height: 35px;\\n  border-radius: 3px;\\n  object-fit: contain;\\n  cursor: pointer;\\n  margin-left: 8px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .header-mobile-new[_ngcontent-%COMP%]   .header-logo[_ngcontent-%COMP%] {\\n    margin-top: 0;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .header-mobile-new[_ngcontent-%COMP%]   .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\\n    height: 74px !important;\\n    padding: 0 10px;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .v3-header[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .main-header__section[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 100% !important;\\n    justify-content: space-between;\\n    height: 100% !important;\\n  }\\n  .header-mobile-new[_ngcontent-%COMP%]   .v3-header[_ngcontent-%COMP%]   header__search__flag[_ngcontent-%COMP%] {\\n    margin: auto 10px;\\n  }\\n}\\n.header-mobile-new[_ngcontent-%COMP%]   .main-header__section__search__flag[_ngcontent-%COMP%] {\\n  margin: auto 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "effect", "environment", "UtilityFunctions", "isPlatformBrowser", "jwt_decode", "GaLocalActionEnum", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r5", "<PERSON><PERSON><PERSON><PERSON>", "name", "ɵɵproperty", "country_r14", "isoCode", "ctx_r13", "selectedCountry", "tenantId", "ɵɵlistener", "HeaderComponent_ng_container_0_select_22_Template_select_change_0_listener", "ɵɵrestoreView", "_r16", "_r12", "ɵɵreference", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "changeCountry", "value", "ɵɵtemplate", "HeaderComponent_ng_container_0_select_22_option_2_Template", "ctx_r6", "countries", "HeaderComponent_ng_container_0_div_39_Template_a_click_1_listener", "_r18", "ctx_r17", "signIn", "ɵɵpipeBind1", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_1_listener", "_r22", "_r19", "hide", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_5_listener", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_6_listener", "ctx_r24", "openAccount<PERSON>ddress", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_9_listener", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_10_listener", "ctx_r26", "showAccountDetails", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_div_click_13_listener", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template_a_click_14_listener", "ctx_r28", "logOut", "ɵɵtextInterpolate", "ɵɵelementContainerStart", "HeaderComponent_ng_container_0_ng_container_40_ng_template_4_Template", "HeaderComponent_ng_container_0_ng_container_40_Template_span_mouseenter_5_listener", "$event", "_r30", "toggle", "ɵɵelementContainerEnd", "ɵɵtextInterpolate3", "ɵɵpipeBind3", "ctx_r8", "Name", "length", "HeaderComponent_ng_container_0_ng_container_41_div_1_Template", "ctx_r9", "navbarData", "isActive", "HeaderComponent_ng_container_0_img_6_Template", "HeaderComponent_ng_container_0_img_7_Template", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "HeaderComponent_ng_container_0_Template_div_click_17_listener", "_r33", "ctx_r32", "isShowYello", "showDialog", "HeaderComponent_ng_container_0_div_21_Template", "HeaderComponent_ng_container_0_select_22_Template", "HeaderComponent_ng_container_0_Template_app_mtn_search_onResult_24_listener", "ctx_r34", "isActiveSearch", "HeaderComponent_ng_container_0_Template_a_click_27_listener", "ctx_r35", "openWishlist", "HeaderComponent_ng_container_0_Template_div_click_32_listener", "ctx_r36", "openCart", "HeaderComponent_ng_container_0_div_39_Template", "HeaderComponent_ng_container_0_ng_container_40_Template", "HeaderComponent_ng_container_0_ng_container_41_Template", "HeaderComponent_ng_container_0_ng_template_42_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction1", "_c1", "ctx_r0", "isMobileTemplate", "scConf", "_c2", "ɵɵpropertyInterpolate", "cartListCount", "authToken", "desktopView", "_r10", "HeaderComponent_ng_container_1_div_6_img_1_Template", "HeaderComponent_ng_container_1_div_6_img_2_Template", "ctx_r37", "ctx_r44", "getImage", "flag", "ɵɵsanitizeUrl", "HeaderComponent_ng_container_1_div_7_div_2_Template_div_click_0_listener", "_r46", "ctx_r45", "HeaderComponent_ng_container_1_div_7_div_2_span_1_Template", "ctx_r43", "HeaderComponent_ng_container_1_div_7_div_2_Template", "HeaderComponent_ng_container_1_div_7_Template_div_click_4_listener", "_r48", "ctx_r47", "toggleSearchBar", "ctx_r38", "isExpanded", "HeaderComponent_ng_container_1_div_8_Template_app_mtn_search_onResult_1_listener", "_r50", "ctx_r49", "HeaderComponent_ng_container_1_div_8_Template_div_click_2_listener", "ctx_r51", "ɵɵclassProp", "ctx_r39", "_c3", "HeaderComponent_ng_container_1_div_6_Template", "HeaderComponent_ng_container_1_div_7_Template", "HeaderComponent_ng_container_1_div_8_Template", "HeaderComponent_ng_container_1_div_9_Template", "ctx_r1", "isShowSearch", "HeaderComponent_ng_template_3_div_20_Template_div_click_0_listener", "restoredCtx", "_r56", "item_r54", "$implicit", "ctx_r55", "<PERSON><PERSON><PERSON><PERSON>", "ctx_r53", "countryFlagImages", "translateCountryName", "HeaderComponent_ng_template_3_Template_input_input_12_listener", "_r58", "ctx_r57", "searchCountries", "HeaderComponent_ng_template_3_Template_input_ngModelChange_12_listener", "ctx_r59", "country", "HeaderComponent_ng_template_3_div_17_Template", "HeaderComponent_ng_template_3_div_20_Template", "ctx_r2", "ɵɵpureFunction0", "_c4", "countriesList", "HeaderComponent", "constructor", "ngZone", "store", "router", "languageService", "translate", "mainDataService", "cd", "cartService", "authTokenService", "messageService", "cookieService", "loaderService", "tenantService", "appDataService", "productLogicService", "permissionService", "dialogService", "commonService", "$gaService", "platformId", "isStoreCloud", "subscription", "isfirstTime", "products", "logo", "languages", "Array", "search", "isShop", "mobileScreen", "merchantURL", "allCountries", "cartListData", "_BaseURL", "apiEndPoint", "zoomLevelClass", "isGoogleAnalytics", "hasPermission", "baseUrl", "screenWidth", "window", "screen", "width", "dialogVisibility", "fetchGoogleTId", "ngOnInit", "localStorage", "getItem", "innerWidth", "selectedLanguage", "defaultLanguage", "getCartLengthData", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "getUserData", "firstName", "split", "isShowSearchBox", "res", "user", "get", "setUserData", "getLanguages", "getAllCountries", "layoutTemplate", "find", "section", "type", "setBannerData", "isNavbarDataActive", "onWindowScroll", "document", "getElementById", "ele", "click", "op", "event", "normalizedCountry", "removeDiacritics", "toLowerCase", "filter", "item", "includes", "text", "normalize", "replace", "img", "verifyImageURL", "onCountrySelected", "callingCode", "getMainData", "initialData", "set", "Set<PERSON>enant", "_this", "_asyncToGenerator", "localMaintenanceTenant", "clear", "setItem", "JSON", "stringify", "resetStorage", "tenants", "records", "selected", "val", "console", "log", "setupCountry", "permission", "navigator", "geolocation", "permissions", "query", "then", "PermissionStatus", "state", "setupCountryWithLocation", "setupCountryWithTenant", "Number", "error", "x", "location", "reload", "show", "getCurrentPosition", "position", "Lat", "coords", "latitude", "Lng", "longitude", "zoom", "latlng", "google", "maps", "LatLng", "googleMapAPiResponse", "maximumAge", "timeout", "defaultTenant", "geoCoder", "geocode", "results", "status", "GeocoderStatus", "OK", "isoCountry", "address_components", "for<PERSON>ach", "obj", "types", "short_name", "getAllCart", "cartData", "cartId", "sessionId", "applyTo", "getCart", "next", "cartDetails", "cartDetailsDPay", "concat", "setCartLenghtData", "setCartItemsData", "cartProductList", "compareCartProducts", "storeProducts", "specsProductId", "push", "getShipmentMethodByTenantId", "success", "ngAfterViewInit", "setTimeout", "profile", "authTokenData", "message", "parse", "decoded", "days", "exp", "toFixed", "dateNow", "Date", "setDate", "getDate", "parseInt", "expires", "path", "sameSite", "removeItem", "err", "key", "displayName", "ngOnDestroy", "sub", "unsubscribe", "_this2", "maintenanceMode", "save<PERSON><PERSON><PERSON>", "GATrackingId", "sessionStorage", "authTokenSet", "delete", "navigate", "getLanguage", "storedLang", "isDefault", "changeLang", "lang", "use", "tenantIdValue", "setCurrentLang", "code", "hideDialog", "userDetails", "CLICK_ON_CHANGE_COUNTRY", "mobileNumber", "deviceType", "deviceId", "CLICK_ON_SIGN_IN_UP", "imageLink", "getGoogleAnalyticsTrackingId", "CLICK_ON_ACCOUNT_DETAILS", "getFromLocalStorage", "CLICK_ON_CART_ICON", "CLICK_ON_WISHLIST_HEADER", "CLICK_ON_ADDRESS", "<PERSON><PERSON><PERSON>", "instant", "_", "ɵɵdirectiveInject", "NgZone", "i1", "StoreService", "i2", "Router", "LanguageService", "i3", "TranslateService", "MainDataService", "ChangeDetectorRef", "CartService", "AuthTokenService", "i4", "MessageService", "i5", "CookieService", "LoaderService", "TenantService", "AppDataService", "ProductLogicService", "PermissionService", "CommonService", "i6", "GoogleAnalyticsService", "_2", "selectors", "viewQuery", "HeaderComponent_Query", "rf", "ctx", "ɵɵresolveWindow", "HeaderComponent_ng_container_0_Template", "HeaderComponent_ng_container_1_Template", "HeaderComponent_Template_p_dialog_visibleChange_2_listener", "HeaderComponent_Template_p_dialog_onHide_2_listener", "HeaderComponent_ng_template_3_Template", "_c5"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\header\\header.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\header\\header.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetector<PERSON>ef,\r\n  Component,\r\n  HostListener, Inject,\r\n  NgZone,\r\n  OnDestroy,\r\n  OnInit, PLATFORM_ID,\r\n  ViewChild,\r\n  effect,\r\n} from '@angular/core';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from '@environments/environment';\r\nimport { Router } from '@angular/router';\r\nimport { Language } from '@core/interface';\r\nimport { MessageService } from 'primeng/api';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CookieService } from 'ngx-cookie-service';\r\n\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\nimport {\r\n  StoreService,\r\n  MainDataService,\r\n  AuthTokenService,\r\n  CartService,\r\n  LoaderService,\r\n  TenantService,\r\n  AppDataService,\r\n  LanguageService, ProductLogicService, PermissionService, CommonService\r\n} from \"@core/services\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport jwt_decode from \"jwt-decode\";\r\nimport {GaActionEnum , GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\n\r\ndeclare let google: any;\r\n\r\n@Component({\r\n  selector: 'app-mtn-header',\r\n  templateUrl: './header.component.html',\r\n  styleUrls: ['./header.component.scss'],\r\n})\r\nexport class HeaderComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @ViewChild('op', { static: false }) op: any;\r\n  applyTo: any;\r\n  isShowYello: boolean = environment.isStoreCloud\r\n  subscription: Array<Subscription> = [];\r\n  isActiveSearch: boolean = false;\r\n  isfirstTime: boolean = false;\r\n  isExpanded: boolean = false;\r\n  products: Array<any> = [];\r\n  authToken: any = '';\r\n  logo: string = '/Images/logo.png';\r\n  user: any;\r\n  Name: string = '';\r\n  baseUrl: string;\r\n  languages: Array<Language> = new Array<Language>();\r\n  search: string = '';\r\n  selectedTenant: any;\r\n  Lat!: number;\r\n  Lng!: number;\r\n  zoom!: number;\r\n  address!: string;\r\n  deliverTo!: any;\r\n  countries: Array<any> = [];\r\n  myplaceHolder: string | undefined;\r\n  source: any;\r\n  permission: any;\r\n  isShop: boolean = false;\r\n  selectedLanguage: any;\r\n  screenWidth?: any;\r\n  count: any;\r\n  innerWidth: number;\r\n  mobileScreen: boolean = false;\r\n  scConf: boolean = false;\r\n  desktopView: boolean;\r\n  showDialog: boolean = false;\r\n  isShowSearch : boolean = false;\r\n  merchantURL: any = environment.merchantURL\r\n  countriesList: any;\r\n  allCountries: any = [];\r\n  cartListCount: any = 0;\r\n  cartListData: any = [];\r\n  public country: string = '';\r\n  private geoCoder: any;\r\n  private _BaseURL = environment.apiEndPoint;\r\n  defaultLanguage: Language;\r\n  selectedCountry:string | any = 'EG';\r\n  zoomLevelClass: string = 'default-zoom';\r\n  navbarData: any;\r\n  isMobileTemplate:boolean=false;\r\n  isGoogleAnalytics:boolean=false;\r\n  userDetails: any;\r\n  constructor(\r\n    private ngZone: NgZone,\r\n    private store: StoreService,\r\n    private router: Router,\r\n    private languageService: LanguageService,\r\n    private translate: TranslateService,\r\n    private mainDataService: MainDataService,\r\n    private cd: ChangeDetectorRef,\r\n    private cartService: CartService,\r\n    private authTokenService: AuthTokenService,\r\n    private messageService: MessageService,\r\n    private cookieService: CookieService,\r\n    private loaderService: LoaderService,\r\n    private tenantService: TenantService,\r\n    private appDataService: AppDataService,\r\n    private productLogicService: ProductLogicService,\r\n    private permissionService: PermissionService,\r\n    private dialogService:AppDataService,\r\n    private commonService: CommonService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    @Inject(PLATFORM_ID) private platformId: any\r\n  ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    this.baseUrl = `${environment.apiEndPoint}`;\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.screen.width;\r\n      if (this.screenWidth > 768) {\r\n        this.desktopView = true;\r\n      } else {\r\n        this.desktopView = false;\r\n      }\r\n      effect(() => {\r\n        this.showDialog = this.dialogService.dialogVisibility();\r\n      });\r\n    }\r\n\r\n    if (environment.isStoreCloud) {\r\n      this.scConf = true;\r\n      this.fetchGoogleTId()\r\n    } else {\r\n      this.scConf = false;\r\n\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.selectedCountry = localStorage.getItem('isoCode')\r\n    this.innerWidth = window.innerWidth;\r\n\r\n    this.selectedLanguage = localStorage.getItem('lang') ? localStorage.getItem('lang') : this.defaultLanguage;\r\n    this.mainDataService.getCartLengthData().subscribe((data: any) => {\r\n\r\n      this.cartListCount = data ?? 0;\r\n      if (this.cartListCount > 0) {\r\n\r\n        if (this.cartListCount > 9) {\r\n          this.cartListCount = '9+';\r\n        }\r\n      }\r\n      this.cd.markForCheck();\r\n      this.cd.detectChanges();\r\n    });\r\n    this.mainDataService.getUserData().subscribe((data: any) => {\r\n      if (data) {\r\n        let firstName = data.name.split(' ')[0];\r\n        this.Name = firstName;\r\n      }\r\n      this.cd.markForCheck();\r\n      this.cd.detectChanges();\r\n    });\r\n    this.commonService.isShowSearchBox.subscribe((res: boolean) => {\r\n      if(!this.desktopView){\r\n        if(!res){\r\n          this.isExpanded = false;\r\n        }\r\n        this.isShowSearch = res;\r\n      }\r\n    })\r\n    let user: any = this.store.get('profile');\r\n\r\n\r\n    if (user) {\r\n      this.mainDataService.setUserData(user)\r\n    }\r\n    if (this.innerWidth < 768) {\r\n      this.mobileScreen = true;\r\n    } else {\r\n      this.mobileScreen = false;\r\n    }\r\n    this.getLanguages();\r\n    this.getAllCountries();\r\n    if(this.appDataService?.layoutTemplate){\r\n      this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n    }\r\n    this.mainDataService.setBannerData(\r\n      {\r\n        isNavbarDataActive: this.navbarData?.isActive ?? false\r\n      }\r\n    )\r\n  }\r\n\r\n  @HostListener('window:scroll', [])\r\n  onWindowScroll() {\r\n    if (this.authToken) {\r\n      if (isPlatformBrowser(this.platformId)) {\r\n        if(document.getElementById('profile')){\r\n          const ele: any = document.getElementById('profile');\r\n          if(ele){\r\n            ele.click();\r\n          }\r\n          this.op.hide();\r\n        }\r\n        }\r\n\r\n    }\r\n  }\r\n\r\n  searchCountries(event: any) {\r\n    if (this.country.length >= 1) {\r\n      const normalizedCountry = this.removeDiacritics(this.country.toLowerCase());\r\n      this.countriesList = this.allCountries.filter((item: any) =>\r\n        this.removeDiacritics(item.name.toLowerCase()).includes(normalizedCountry)\r\n      );\r\n    } else if (this.country.length === 0) {\r\n      this.countriesList = this.allCountries;\r\n    }\r\n  }\r\n\r\n  removeDiacritics(text: any) {\r\n    return text.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\r\n  }\r\n  countryFlagImages(img: any) {\r\n    return UtilityFunctions.verifyImageURL(img, this._BaseURL);\r\n\r\n  }\r\n  onCountrySelected(callingCode: any) {\r\n    if (callingCode == '233') {\r\n      return '2';\r\n    } else if (callingCode == '+256') {\r\n      return '1';\r\n    } else if (callingCode == '225') {\r\n      return '4';\r\n    } else if (callingCode == '20') {\r\n      return '3';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  getMainData(): void {\r\n    const initialData = this.appDataService.initialData\r\n    this.isShop = initialData.isShop;\r\n    this.store.set('isShop', this.isShop);\r\n  }\r\n\r\n  async SetTenant() {\r\n    this.authToken = null;\r\n    const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant')\r\n    localStorage.clear();\r\n    localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '')\r\n    await this.logOut();\r\n    localStorage.setItem('tenantId', this.selectedTenant);\r\n    localStorage.setItem('allCountryTenants', JSON.stringify(this.countries));\r\n    this.store.set('cartProducts', []);\r\n    localStorage.setItem('sessionId', '');\r\n    this.store.set('SessionId', '');\r\n    this.Name = '';\r\n    this.resetStorage();\r\n  }\r\n\r\n\r\n  getAllCountries() {\r\n    const tenants = this.appDataService.tenants\r\n    if (tenants.records) {\r\n      const countries = tenants.records;\r\n      countries[0].selected = true;\r\n      const tenantId = localStorage.getItem('tenantId');\r\n      if (tenantId && tenantId !== '') {\r\n        this.selectedTenant = countries.find((country: any) => country.tenantId == tenantId)\r\n      }\r\n      this.countries = countries;\r\n      this.countriesList = this.countries;\r\n      this.allCountries = this.countriesList;\r\n    }\r\n  }\r\n  changeCountry(val:any){\r\n    console.log(val);\r\n    this.setTenant(val)\r\n  }\r\n\r\n  setupCountry() {\r\n    let tenantId = localStorage.getItem('tenantId');\r\n    this.isShop = localStorage.getItem('ShopId') != null;\r\n    let permission = 'not-granted';\r\n    if (window?.navigator?.geolocation) {\r\n      navigator?.permissions.query({ name: 'geolocation' })\r\n        .then((PermissionStatus: any) => {\r\n\r\n          if (PermissionStatus.state == 'granted') {\r\n            permission = 'granted';\r\n          } else if (PermissionStatus.state == 'prompt') {\r\n            permission = 'not-granted';\r\n          } else {\r\n            permission = 'denied';\r\n          }\r\n\r\n          if (permission === 'not-granted') {\r\n            this.isfirstTime = true;\r\n            this.setupCountryWithLocation();\r\n          } else {\r\n            this.setupCountryWithTenant(Number(tenantId));\r\n          }\r\n        },\r\n          (error: any) => {\r\n\r\n          });\r\n    }\r\n  }\r\n\r\n  setupCountryWithTenant(tenantId: number) {\r\n    let country = this.countries.find(\r\n      (x: any) => x.tenantId === tenantId\r\n    );\r\n    if (!country) {\r\n      country = this.countries[0];\r\n    }\r\n    country.selected = true;\r\n    if (this.selectedTenant != tenantId) {\r\n      this.selectedTenant = country.tenantId;\r\n      localStorage.setItem('tenantId', country.tenantId);\r\n      localStorage.setItem('allCountryTenants', JSON.stringify(this.countries));\r\n\r\n      location.reload();\r\n    } else {\r\n      this.selectedTenant = country.tenantId;\r\n      this.selectedTenant = country.tenantId;\r\n      localStorage.setItem('tenantId', country.tenantId);\r\n    }\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  setupCountryWithLocation() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      if (window?.navigator?.geolocation) {\r\n        this.loaderService.show();\r\n        window.navigator.geolocation.getCurrentPosition(\r\n          (position) => {\r\n            if ('geolocation' in navigator) {\r\n              this.loaderService.show();\r\n              navigator.geolocation.getCurrentPosition(\r\n                (position) => {\r\n                  this.Lat = position.coords.latitude;\r\n                  this.Lng = position.coords.longitude;\r\n                  this.zoom = 8;\r\n                  this.permission = true;\r\n\r\n\r\n                  let latlng = new google.maps.LatLng(this.Lat, this.Lng);\r\n                  this.googleMapAPiResponse(latlng)\r\n                },\r\n                (error: any) => {\r\n\r\n                }, {\r\n                  maximumAge: 60000,\r\n                  timeout: 2000\r\n                }\r\n              );\r\n            }\r\n          },\r\n          (error) => {\r\n            const tenantId = localStorage.getItem('tenantId') ?? environment.defaultTenant;\r\n            this.setupCountryWithTenant(Number(tenantId));\r\n          }\r\n        );\r\n      }\r\n    }\r\n  }\r\n  googleMapAPiResponse(latlng : any){\r\n      this.geoCoder.geocode({ 'latLng': latlng }, (results: any, status: any) => {\r\n      if (status == google.maps.GeocoderStatus.OK && results[0]) {\r\n      let isoCountry = 'UG';\r\n      results[0].address_components.forEach((obj: any) => {\r\n      if (obj.types.includes('country')) isoCountry = obj.short_name;\r\n    });\r\n\r\n    let country = this.countries.find(\r\n      (x: any) => x.isoCode === isoCountry\r\n    );\r\n    this.loaderService.hide();\r\n    this.setupCountryWithTenant(country.tenantId ?? 0);\r\n    }\r\n    });\r\n}\r\n  getAllCart(): void {\r\n    const cartData = {\r\n      cartId: localStorage.getItem('cartId'),\r\n      sessionId: localStorage.getItem('sessionId') ?? '',\r\n      applyTo: this.applyTo\r\n    };\r\n    if (cartData.sessionId) {\r\n      this.cartService.getCart(cartData)\r\n        .subscribe({\r\n          next: (res: any) => {\r\n            this.cartListCount = 0;\r\n            this.cartListData = [];\r\n            if (res.data?.records?.length) {\r\n              this.cartListCount = 0;\r\n              if (res.data.records[0].cartDetails.length) {\r\n                this.cartListCount = res.data.records[0].cartDetails.length;\r\n                this.cartListData = res.data.records[0].cartDetails;\r\n\r\n              }\r\n              if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\r\n                this.cartListCount += res.data.records[0].cartDetailsDPay.length;\r\n                this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay)\r\n              }\r\n              this.mainDataService.setCartLenghtData(this.cartListCount);\r\n              this.mainDataService.setCartItemsData(this.cartListData);\r\n\r\n            } else {\r\n              this.mainDataService.setCartLenghtData(0);\r\n              this.mainDataService.setCartItemsData([]);\r\n            }\r\n            this.productLogicService.cartProductList = this.cartListData;\r\n          }\r\n        });\r\n    } else {\r\n      this.products = [];\r\n    }\r\n\r\n  }\r\n\r\n  public compareCartProducts(products: [], storeProducts: []) {\r\n    if (products.length) {\r\n\r\n      products.forEach((item: any) => {\r\n        storeProducts.forEach((data: any) => {\r\n          if (item.specsProductId === data.specsProductId) {\r\n            this.products.push(item);\r\n\r\n\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      this.products = storeProducts;\r\n    }\r\n\r\n    localStorage.setItem('addedProducts', JSON.stringify(this.products));\r\n  }\r\n\r\n  getShipmentMethodByTenantId() {\r\n    if (this.permissionService.hasPermission('Shipment-Fee')) {\r\n      this.cartService.getShipmentMethodByTenantId().subscribe((res: any) => {\r\n        if (res.success && res.data.length) {\r\n          this.applyTo = res.data[0].applyTo\r\n          localStorage.setItem('apply-to', this.applyTo);\r\n          this.getAllCart();\r\n        }\r\n      })\r\n    } else {\r\n      localStorage.setItem('apply-to', '2');\r\n      this.applyTo = 2;\r\n      this.getAllCart();\r\n    }\r\n\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n\r\n    setTimeout(() => {\r\n      let profile = localStorage.getItem('profile')\r\n      if (profile && profile !== '') {\r\n        this.getShipmentMethodByTenantId()\r\n      } else {\r\n        this.getAllCart()\r\n      }\r\n      this.authTokenService.authTokenData.subscribe(\r\n        (message) => (this.authToken = message)\r\n      );\r\n      if (!this.authToken) {\r\n        this.authToken = this.cookieService.get('authToken');\r\n        if(!this.authToken) {\r\n          let profile: any = localStorage.getItem('profile')\r\n          if(profile && profile !== '') {\r\n            profile = JSON.parse(profile)\r\n            this.authToken = profile.authToken.replace('bearer ', '')\r\n            const decoded: any = jwt_decode(this.authToken);\r\n\r\n            let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(\r\n              0\r\n            );\r\n            const dateNow = new Date();\r\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n            this.cookieService.set('authToken', this.authToken, {\r\n              expires: dateNow,\r\n              path: '/',\r\n              sameSite: 'Strict',\r\n            });\r\n            localStorage.removeItem('isGuest');\r\n          }\r\n        }\r\n      }\r\n\r\n\r\n      this.subscription[2] = this.store.subscription('cartProducts').subscribe({\r\n        next: (res: any) => {\r\n          if (res) {\r\n            this.products = res;\r\n            this.cd.detectChanges();\r\n\r\n          }\r\n\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n        },\r\n      });\r\n\r\n\r\n      this.subscription[4] = this.store.subscription('mainData').subscribe({\r\n        next: (res: any) => {\r\n          let data = res?.find((obj: any) => obj.key.toLowerCase() === 'logo');\r\n          if (data) this.logo = '/' + (data.displayName || this.logo);\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n        },\r\n      });\r\n    }, 1);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscription.forEach((sub) => {\r\n      sub.unsubscribe();\r\n    });\r\n  }\r\n\r\n  resetStorage() {\r\n\r\n\r\n    localStorage.removeItem('decimalValue');\r\n    localStorage.removeItem('countryPhone');\r\n    localStorage.removeItem('PhoneLength');\r\n    localStorage.removeItem('currency');\r\n    localStorage.removeItem('customerAddressLandmarkRequired');\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.location.reload();\r\n    }\r\n  }\r\n\r\n  async logOut(): Promise<void> {\r\n\r\n    const tenantId = localStorage.getItem('tenantId') || '1';\r\n    const maintenanceMode = localStorage.getItem('maintenanceMode') ?? 'false'\r\n    const saveCookie = localStorage.getItem('save_cookie') || '';\r\n    const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant')\r\n    const GATrackingId = localStorage.getItem('GATrackingId') || ''\r\n    localStorage.clear();\r\n    localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '')\r\n    this.authToken = null;\r\n\r\n    sessionStorage.clear();\r\n    this.authTokenService.authTokenSet('');\r\n\r\n\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('profile', '');\r\n    this.mainDataService.setCartLenghtData(0);\r\n    this.mainDataService.setCartItemsData([]);\r\n    this.mainDataService.setUserData(null);\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    localStorage.setItem('cartId', '');\r\n    this.store.set('cartProducts', []);\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    localStorage.setItem('isGuest', 'true');\r\n    localStorage.setItem('tenantId', tenantId)\r\n    localStorage.setItem('maintenanceMode', maintenanceMode)\r\n    localStorage.setItem('save_cookie', saveCookie)\r\n    localStorage.setItem('GATrackingId', GATrackingId)\r\n    await this.router.navigate(['/']);\r\n\r\n  }\r\n\r\n  getLanguages() {\r\n    this.languageService.getLanguage().subscribe((res) => {\r\n\r\n      if (res?.languages) {\r\n        let storedLang = localStorage.getItem('language');\r\n        if (!storedLang) {\r\n          let defaultLanguage = res.languages.find((x) => x.isDefault);\r\n          if (!defaultLanguage && res.languages.length > 0) {\r\n            this.defaultLanguage = res.languages[0];\r\n          }\r\n          if (defaultLanguage) {\r\n            this.selectedLanguage = localStorage.getItem('lang') ? localStorage.getItem('lang') : '';\r\n          }\r\n        }\r\n        this.languages = res.languages;\r\n      }\r\n\r\n    });\r\n  }\r\n\r\n  changeLang(lang: any): void {\r\n    localStorage.setItem('language', lang);\r\n    this.translate.use(lang);\r\n  }\r\n\r\n\r\n  setTenant(tenantId: any,country?:any) {\r\n    let defaultLanguage: any = country.languages.filter((lang: any) => lang.isDefault)\r\n    const tenantIdValue = localStorage.getItem('tenantId');\r\n    if (tenantIdValue !== tenantId) {\r\n      this.logOut();\r\n      this.languageService.setCurrentLang(defaultLanguage[0].code)\r\n      localStorage.setItem('tenantId',tenantId)\r\n      this.router.navigate(['/'])\r\n        .then(() => {\r\n          if (isPlatformBrowser(this.platformId)) {\r\n            window.location.reload();\r\n          }\r\n        });\r\n    } else {\r\n      this.showDialog = false;\r\n      this.dialogService.hideDialog();\r\n    }\r\n    this.userDetails = this.store.get('profile');\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_CHANGE_COUNTRY, '', 'SWITCH_COUNTRY', 1, true, {\r\n      \"country\": country.name,\r\n       \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\r\n       \"ip_Address\": this.store.get('userIP'),\r\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n        \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n\r\n    });\r\n  }\r\n  }\r\n\r\n  signIn() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_IN_UP, '', 'SIGNIN/SIGNUP', 1, true);\r\n    }\r\n    this.router.navigate(['/login']);\r\n  }\r\n  hideDialog() {\r\n    this.dialogService.hideDialog();\r\n    }\r\n  toggleSearchBar() {\r\n    this.isExpanded = !this.isExpanded;\r\n    if(this.isExpanded){\r\n      this.isShowSearch = true;\r\n    }\r\n  }\r\n  getImage(imageLink: any) {\r\n    if (imageLink) {\r\n      return `${this.baseUrl}/Images/${imageLink}`;\r\n    } else {\r\n      return '';\r\n    }\r\n  }\r\n  fetchGoogleTId() {\r\n    this.mainDataService.getGoogleAnalyticsTrackingId().subscribe({\r\n      next: (res: any) => {\r\n        localStorage.setItem('GATrackingId', res.data?res.data[0].value:'');\r\n      }, error: (err: any) => {\r\n        console.error(err)\r\n      }\r\n    })\r\n  }\r\n\r\n  showAccountDetails() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_ACCOUNT_DETAILS, '', 'ACCOUNT_DETAILS', 1, true);\r\n    }\r\n    this.router.navigate(['/account'])\r\n  }\r\n\r\n  openCart() {\r\n    this.userDetails = this.store.getFromLocalStorage('profile');\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(\r\n      GaLocalActionEnum.CLICK_ON_CART_ICON, 'navigation', 'CART_ON_TOP_BANNER', 1, true ,\r\n      {\r\n      \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated',\r\n      \"ip_Address\": this.store.get('userIP'),\r\n      \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n      \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n      \"session_ID\": localStorage.getItem('sessionId')\r\n\r\n    });\r\n  }\r\n    this.router.navigate(['/cart'])\r\n  }\r\n\r\n  openWishlist() {\r\n    this.userDetails = this.store.getFromLocalStorage('profile');\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_WISHLIST_HEADER, '', '', 1, true , {\r\n      \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated'\r\n    });\r\n    }\r\n    this.router.navigate(['/wishlist'])\r\n  }\r\n\r\n  openAccountAddress() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_ADDRESS, '', '', 1, true , {\r\n      \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated'\r\n    });\r\n  }\r\n    this.router.navigate(['/account/address'])\r\n  }\r\n  translateCountryName(name: string): string {\r\n    debugger\r\n    const translationKey = `countries.${name.replace(' ','').replace(/'/g,'')}`;\r\n    return this.translate.instant(translationKey);\r\n  }\r\n\r\n}\r\n\r\n", "<ng-container *ngIf=\"desktopView || !isMobileTemplate\">\r\n  <div class=\"v3-header\" >\r\n    <header class=\"d-flex flex-wrap justify-content-space-between\" [ngClass]=\"{'mobile-header-template': isMobileTemplate}\">\r\n      <div id=\"header-zoom-container\" class=\"main-header\">\r\n\r\n        <div class=\"main-header__section d-flex\">\r\n          <div class=\"w-50 d-inline-flex\" >\r\n            <img *ngIf=\"!scConf\" alt=\"Logo\" class=\"d-flex header-logo align-items-center justify-content-center\"\r\n                 routerLink=\"/\" src=\"assets/icons/logo-marketplace.png\"/>\r\n            <img *ngIf=\"scConf\" alt=\"Logo\" class=\"d-flex header-logo align-items-center justify-content-center\"\r\n                 routerLink=\"/\"\r\n                 src=\"assets/icons/white-logo.svg\"/>\r\n          </div>\r\n\r\n          <div class=\"w-50 select-location\">\r\n            <svg height=\"16\" id=\"a559119926c5ebd4e4ff6e365c5da3c1\" viewBox=\"0 0 15.999 16\" width=\"15.999\"\r\n                 xmlns=\"http://www.w3.org/2000/svg\">\r\n              <g data-name=\"Group 1082\" id=\"Group_1082\">\r\n                <path d=\"M0,0H16V16H0Z\" data-name=\"Path 9826\" fill=\"none\" id=\"Path_9826\"/>\r\n                <path d=\"M0,0H16V16H0Z\" data-name=\"Path 9827\" fill=\"none\" id=\"Path_9827\"/>\r\n              </g>\r\n              <g data-name=\"Group 1083\" id=\"Group_1083\" transform=\"translate(1.678 0.668)\">\r\n                <path\r\n                  d=\"M10.334,12.335a.667.667,0,0,1-.607-.391C9.272,10.944,7,5.875,7,4.335a3.333,3.333,0,1,1,6.666,0c0,1.54-2.271,6.609-2.727,7.609a.663.663,0,0,1-.607.391Zm0-10a2,2,0,0,0-2,2,27.475,27.475,0,0,0,2,5.684,27.461,27.461,0,0,0,2-5.684A2,2,0,0,0,10.334,2.335Z\"\r\n                  data-name=\"Path 9828\" fill=\"#fff\" id=\"Path_9828\" transform=\"translate(-4.012 -1.002)\"/>\r\n                <circle cx=\"1.153\" cy=\"1.153\" data-name=\"Ellipse 15\" fill=\"#fff\" id=\"Ellipse_15\" r=\"1.153\"\r\n                        transform=\"translate(5.096 2.407)\"/>\r\n                <path\r\n                  d=\"M13.829,18H3.851a1.333,1.333,0,0,1-1.225-1.859l1.428-3.332A1.33,1.33,0,0,1,5.28,12h.549v1.333H5.28L3.851,16.669h9.978L12.4,13.336h-.571V12H12.4a1.33,1.33,0,0,1,1.225.809l1.428,3.333A1.333,1.333,0,0,1,13.829,18Z\"\r\n                  data-name=\"Path 9829\" fill=\"#fff\" id=\"Path_9829\" transform=\"translate(-2.517 -4.669)\"/>\r\n              </g>\r\n            </svg>\r\n            <div (click)=\"!isShowYello && showDialog = true\"\r\n                 [ngClass]=\"{ 'pointer-events-none': selectedTenant?.tenantId === '3' }\" class=\"delivery cursor-pointer\">\r\n              <span>{{ \"header.deliverTo\" | translate }} </span>\r\n              <div class=\"deliver-to\" *ngIf=\"!isShowYello && selectedTenant\">\r\n              {{ selectedTenant?.name }} <em class=\"pi pi-angle-down text-white ml-3\"></em></div>\r\n              <select *ngIf=\"isShowYello\" #langSelect (change)=\"changeCountry(langSelect.value)\"   class=\"shadow-none border-light selector\"  id=\"language-dropdown\" name=\"language\">\r\n                <option *ngFor=\"let country of countries\" [selected]=\"country.isoCode === selectedCountry\"\r\n                        [value]=\"country.tenantId\">\r\n                  {{ country.name  }}\r\n                </option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"main-header__section d-inline-flex w-100 search-bar\">\r\n          <app-mtn-search (onResult)=\"isActiveSearch = $event\"\r\n                          class=\"d-flex w-100 align-items-center justify-content-center\"></app-mtn-search>\r\n        </div>\r\n\r\n        <div class=\"main-header__section d-flex header-left-section\">\r\n\r\n          <!-- To Change The Language -->\r\n\r\n          <!-- To Change The Language -->\r\n          <!-- <div class=\"d-inline-flex language-dropdown\">\r\n          <!-- To Change The Language -->\r\n          <!-- <div class=\"d-inline-flex language-dropdown\">\r\n            <select #langSelect (change)=\"changeLang(langSelect.value)\" [disabled]=\"true\" id=\"language-dropdown\"\r\n                    name=\"language\">\r\n              <option *ngFor=\"let language of languages\" [selected]=\"language.code === selectedLanguage\"\r\n                      [value]=\"language.code\">\r\n                {{ language.name === \"French\" ? \"Français\" : language.name }}\r\n              </option>\r\n            </select>\r\n          </div> -->\r\n\r\n          <div class=\"d-inline-flex wishlist-icon\">\r\n            <a (click)=\"openWishlist()\" class=\"d-flex mx-3 cursor-pointer align-items-center justify-content-center\">\r\n              <img alt=\"No Image\" src=\"assets/icons/heart-empty.svg\"/>\r\n              <span class=\"ml-1\">\r\n            {{ \"header.wishList\" | translate }}\r\n          </span>\r\n            </a>\r\n          </div>\r\n\r\n          <div class=\"d-inline-flex cart-icon\" (click)=\"openCart()\">\r\n            <a class=\"d-flex mx-3 cursor-pointer align-items-center justify-content-center\" >\r\n          <span class=\"mx-3 cursor-pointer\" style=\"position: relative\">\r\n            <img alt=\"No Image\" class=\"mr-1\" src=\"assets/images/header/icon1.svg\"/>\r\n            <p-badge class=\"badge-mobile\" value=\"{{ cartListCount }}\"></p-badge>\r\n            {{ \"header.cart\" | translate }}\r\n          </span>\r\n            </a>\r\n          </div>\r\n\r\n          <div *ngIf=\"!authToken\" class=\"d-inline-flex unauth-signin\">\r\n            <a class=\"d-flex flex-row align-items-center justify-content-center\" (click)=\"signIn()\">\r\n              <img alt=\"No Image\" class=\"login-icon\" src=\"assets/icons/user-account.svg\" style=\"margin-right: 10px\">\r\n\r\n              <div>\r\n                <div>\r\n                  {{ \"header.signIn\" | translate }}\r\n                </div>\r\n              </div>\r\n            </a>\r\n          </div>\r\n\r\n          <ng-container *ngIf=\"authToken\">\r\n            <div class=\"actions d-inline-flex\">\r\n              <p-overlayPanel #op [appendTo]=\"'body'\">\r\n                <ng-template pTemplate>\r\n                  <div class=\"profile-menu\">\r\n                    <div (click)=\"op.hide()\" class=\"my-2 profile-menu-item\">\r\n                      <a class=\"cursor-pointer no-underline main-color font-size-15px regular-font\"\r\n                         routerLink=\"/orders\">{{\r\n                        \"header.yourOrders\" | translate }}</a>\r\n                    </div>\r\n                    <div (click)=\"op.hide()\" class=\"my-2 profile-menu-item\">\r\n                      <a (click)=\"openAccountAddress()\" class=\"cursor-pointer no-underline main-color font-size-15px regular-font\"\r\n                        >{{ \"header.yourAddresses\" | translate }}</a>\r\n                    </div>\r\n                    <div (click)=\"op.hide()\" class=\"my-2 profile-menu-item\">\r\n                      <a (click)=\"showAccountDetails()\" class=\"cursor-pointer no-underline main-color font-size-15px regular-font\"\r\n                         >{{\r\n                        \"header.yourDetails\" | translate }}</a>\r\n                    </div>\r\n                    <div (click)=\"op.hide()\" class=\"my-2 profile-menu-item\">\r\n                      <a (click)=\"logOut()\"\r\n                         class=\"cursor-pointer no-underline main-color font-size-15px regular-font logout-user\">{{\r\n                        \"header.logout\" | translate }}</a>\r\n                    </div>\r\n                  </div>\r\n                </ng-template>\r\n              </p-overlayPanel>\r\n              <span (mouseenter)=\"op.toggle($event)\"\r\n                    class=\"cursor-pointer d-flex mx-3 cursor-pointer align-items-center justify-content-center user-name\"\r\n                    id=\"profile\">\r\n            <em class=\"pi pi-user pi-white\"></em>\r\n            <span class=\"ml-2 d-inline-flex flex-nowrap ml-2 text-overflow-ellipsis w-6rem\" style=\"white-space: nowrap\">\r\n              {{ \"header.hi\" | translate }}\r\n              {{ Name | slice : 0 : 6 }}\r\n              {{ Name.length > 6 ? \"...\" : \"\" }}\r\n            </span>\r\n          </span>\r\n            </div>\r\n          </ng-container>\r\n        </div>\r\n\r\n      </div>\r\n\r\n    </header>\r\n    <ng-container *ngIf=\"desktopView; else showNavBar\">\r\n      <div  *ngIf=\"navbarData?.isActive\" class=\"v3-header-cateogries\">\r\n        <div class=\"header-category\">\r\n          <div id=\"header-zoom-container\">\r\n            <div class=\"col-12 px-0 pb-0 header-mtn\">\r\n              <div class=\"flex flex-row\">\r\n                <app-mtn-navbar class=\"category-navbar w-100\"></app-mtn-navbar>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n    <ng-template #showNavBar>\r\n      <div class=\"v3-header-cateogries\">\r\n        <div class=\"header-category\" style=\"top: 160px !important;\">\r\n          <div id=\"header-zoom-container\">\r\n            <div class=\"col-12 px-0 pb-0 header-mtn\">\r\n              <div class=\"flex flex-row\">\r\n                <app-mtn-navbar class=\"category-navbar w-100\"></app-mtn-navbar>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n\r\n  </div>\r\n</ng-container>\r\n\r\n\r\n<ng-container  *ngIf=\"!desktopView && isMobileTemplate\">\r\n <div class=\"header-mobile-new\">\r\n   <div class=\"v3-header\" style=\"height: 75px !important;\">\r\n     <header [ngClass]=\"{'mobile-header-template': isMobileTemplate}\">\r\n       <div id=\"header-zoom-container\" class=\"main-header\">\r\n         <div class=\"main-header__section d-flex align-items-center\">\r\n           <div class=\"main-header__section__logo\" *ngIf=\"!isExpanded\">\r\n             <img *ngIf=\"!scConf\" alt=\"Logo\" class=\"d-flex header-logo\" routerLink=\"/\" src=\"assets/icons/new-logo-marketplace-mobile.svg\"/>\r\n             <img *ngIf=\"scConf\" alt=\"Logo\" class=\"d-flex header-logo\" routerLink=\"/\" src=\"assets/icons/white-logo.svg\"/>\r\n           </div>\r\n           <div class=\"main-header__section__search\" *ngIf=\"!isExpanded\">\r\n             <div class=\"d-flex justify-content-end\">\r\n               <div class=\"main-header__section__search__flag\"  *ngIf=\"!isExpanded\" (click)=\"!isShowYello && showDialog = true\">\r\n                <span *ngIf=\"!isShowYello && selectedTenant\">\r\n                  <img class=\"flar-header-img\" [src]=\"getImage(selectedTenant?.flag)\">\r\n                </span>\r\n               </div>\r\n               <div class=\"search-container\">\r\n                 <div class=\"search-icon\" (click)=\"toggleSearchBar()\">\r\n                   <em class=\"pi pi-search\"></em>\r\n                 </div>\r\n               </div>\r\n             </div>\r\n           </div>\r\n\r\n           <div class=\"search-bar\" *ngIf=\"isExpanded && isShowSearch\">\r\n             <app-mtn-search\r\n               (onResult)=\"isActiveSearch = $event\"\r\n               [class.width-animation]=\"isExpanded\"\r\n               [ngClass]=\"{ 'overflow-hidden': !isExpanded }\"\r\n               class=\"search-bar-new\"></app-mtn-search>\r\n             <div class=\"search-icon search-icon-abs\" (click)=\"toggleSearchBar()\">\r\n               <em class=\"pi pi-search\"></em>\r\n             </div>\r\n           </div>\r\n\r\n           <!--          <div class=\"flag-top\"-->\r\n           <!--               [ngClass]=\"{ 'pointer-events-none': selectedTenant?.tenantId === '3' }\">-->\r\n\r\n           <!--            -->\r\n           <!--          </div>-->\r\n           <!--          <div class=\"search-container \" [ngClass]=\"{ 'expanded': isExpanded }\">-->\r\n\r\n           <!--            <div class=\"search-icon\" (click)=\"toggleSearchBar()\">-->\r\n           <!--              <em class=\"pi pi-search\"></em>-->\r\n           <!--            </div>-->\r\n\r\n           <!--            <app-mtn-search (onResult)=\"isActiveSearch = $event\" [class.width-animation]=\"isExpanded\" [ngClass]=\"{ 'overflow-hidden': !isExpanded }\" class=\"search-bar-new\"></app-mtn-search>-->\r\n           <!--          </div>-->\r\n         </div>\r\n\r\n       </div>\r\n     </header>\r\n     <div *ngIf=\"navbarData?.isActive\" class=\"v3-header-cateogries d-none\">\r\n       <div class=\"header-category\">\r\n         <div id=\"header-zoom-container\">\r\n           <div class=\"col-12 px-0 pb-0 header-mtn\">\r\n             <div class=\"flex flex-row\">\r\n               <app-mtn-navbar class=\"category-navbar w-100\"></app-mtn-navbar>\r\n             </div>\r\n           </div>\r\n         </div>\r\n       </div>\r\n     </div>\r\n   </div>\r\n </div>\r\n</ng-container>\r\n<p-dialog [(visible)]=\"showDialog\" (onHide)=\"hideDialog()\" [breakpoints]=\"{ '960px': '75vw', '640px': '90vw' }\" [dismissableMask]=\"true\"\r\n          [draggable]=\"false\" [modal]=\"true\" [resizable]=\"false\" [showHeader]=\"false\" class=\"confirmation-modal\">\r\n  <ng-template pTemplate=\"content\">\r\n    <div class=\"content\">\r\n      <img alt=\"No Image\" alt=\"Logo\" class=\"mb-3\" src=\"assets/images/logo-marketplace1.svg\"/>\r\n      <h2 class=\"mb-1 select-country\">{{ \"landingPage.selectCountry\" | translate }}</h2>\r\n      <p class=\"country-desp mb-3\">{{ \"landingPage.clickOnCountry\" | translate }}</p>\r\n\r\n      <div class=\"product-search p-inputgroup mb-3\">\r\n        <span class=\"p-inputgroup-addon color\"><em class=\"pi pi-search\"></em></span>\r\n        <form autocomplete=\"off\" class=\"ng-untouched ng-pristine ng-valid\" novalidate=\"\" style=\"display: contents\">\r\n          <input (input)=\"searchCountries($event)\" [(ngModel)]=\"country\" [ngModelOptions]=\"{ standalone: true }\"\r\n                 autocapitalize=\"off\" autocomplete=\"new-password\" autocorrect=\"off\"\r\n                 class=\"p-inputtext p-component p-element ng-untouched ng-pristine ng-valid\" pinputtext=\"\"\r\n                 placeholder=\"{{ 'landingPage.searchCountry' | translate }}\" spellcheck=\"false\" type=\"text\"/>\r\n        </form>\r\n      </div>\r\n      <p>{{ \"countries.CôtedIvoire\" | translate }}</p>\r\n      <div *ngIf=\"countriesList?.length === 0\" class=\"col-12 text-center\">\r\n        <p class=\"no-found\">{{ \"landingPage.noCountryFound\" | translate }}</p>\r\n      </div>\r\n      <span class=\"line-border\"></span>\r\n      <div class=\"row countries-scoll\">\r\n        <div (click)=\"setTenant(item?.tenantId,item)\" *ngFor=\"let item of countriesList\"\r\n             class=\"col-4 text-center mobile-show-countries\">\r\n          <img [src]=\"countryFlagImages(item?.flag)\" alt=\"No Image\" class=\"country-flag\"/>\r\n          <p class=\"country-name\">{{  translateCountryName(item?.name) }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-template>\r\n</p-dialog>\r\n"], "mappings": ";AAAA,SAOUA,WAAW,EAEnBC,MAAM,QACD,eAAe;AAEtB,SAASC,WAAW,QAAQ,2BAA2B;AAOvD,OAAOC,gBAAgB,MAAM,2BAA2B;AAWxD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,OAAOC,UAAU,MAAM,YAAY;AAEnC,SAAQC,iBAAiB,QAAO,kCAAkC;;;;;;;;;;;;;;;;;;;IC1BtDC,EAAA,CAAAC,SAAA,cAC6D;;;;;IAC7DD,EAAA,CAAAC,SAAA,cAEwC;;;;;IAwBtCD,EAAA,CAAAE,cAAA,cAA+D;IAC/DF,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAC,SAAA,aAAkD;IAAAD,EAAA,CAAAI,YAAA,EAAM;;;;IAAnFJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAC,IAAA,MAA2B;;;;;IAEzBT,EAAA,CAAAE,cAAA,iBACmC;IACjCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAHiCJ,EAAA,CAAAU,UAAA,aAAAC,WAAA,CAAAC,OAAA,KAAAC,OAAA,CAAAC,eAAA,CAAgD,UAAAH,WAAA,CAAAI,QAAA;IAExFf,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,WAAA,CAAAF,IAAA,MACF;;;;;;IAJFT,EAAA,CAAAE,cAAA,qBAAuK;IAA/HF,EAAA,CAAAgB,UAAA,oBAAAC,2EAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAApB,EAAA,CAAAqB,WAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,aAAA,CAAAL,IAAA,CAAAM,KAAA,CAA+B;IAAA,EAAC;IAChF1B,EAAA,CAAA2B,UAAA,IAAAC,0DAAA,qBAGS;IACX5B,EAAA,CAAAI,YAAA,EAAS;;;;IAJqBJ,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAU,UAAA,YAAAmB,MAAA,CAAAC,SAAA,CAAY;;;;;;IAmD9C9B,EAAA,CAAAE,cAAA,cAA4D;IACWF,EAAA,CAAAgB,UAAA,mBAAAe,kEAAA;MAAA/B,EAAA,CAAAkB,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAS,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACrFlC,EAAA,CAAAC,SAAA,cAAsG;IAEtGD,EAAA,CAAAE,cAAA,UAAK;IAEDF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAmC,WAAA,6BACF;;;;;;IASEnC,EAAA,CAAAE,cAAA,cAA0B;IACnBF,EAAA,CAAAgB,UAAA,mBAAAoB,2FAAA;MAAApC,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAArC,EAAA,CAAAuB,aAAA;MAAA,MAAAe,IAAA,GAAAtC,EAAA,CAAAqB,WAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAc,IAAA,CAAAC,IAAA,EAAS;IAAA,EAAC;IACtBvC,EAAA,CAAAE,cAAA,YACwB;IAAAF,EAAA,CAAAG,MAAA,GACY;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE1CJ,EAAA,CAAAE,cAAA,cAAwD;IAAnDF,EAAA,CAAAgB,UAAA,mBAAAwB,2FAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAArC,EAAA,CAAAuB,aAAA;MAAA,MAAAe,IAAA,GAAAtC,EAAA,CAAAqB,WAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAc,IAAA,CAAAC,IAAA,EAAS;IAAA,EAAC;IACtBvC,EAAA,CAAAE,cAAA,YACG;IADAF,EAAA,CAAAgB,UAAA,mBAAAyB,yFAAA;MAAAzC,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAA,MAAAK,OAAA,GAAA1C,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAkB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAC9B3C,EAAA,CAAAG,MAAA,GAAwC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjDJ,EAAA,CAAAE,cAAA,cAAwD;IAAnDF,EAAA,CAAAgB,UAAA,mBAAA4B,2FAAA;MAAA5C,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAArC,EAAA,CAAAuB,aAAA;MAAA,MAAAe,IAAA,GAAAtC,EAAA,CAAAqB,WAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAc,IAAA,CAAAC,IAAA,EAAS;IAAA,EAAC;IACtBvC,EAAA,CAAAE,cAAA,aACI;IADDF,EAAA,CAAAgB,UAAA,mBAAA6B,0FAAA;MAAA7C,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAA,MAAAS,OAAA,GAAA9C,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAsB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAC7B/C,EAAA,CAAAG,MAAA,IACiC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE3CJ,EAAA,CAAAE,cAAA,eAAwD;IAAnDF,EAAA,CAAAgB,UAAA,mBAAAgC,4FAAA;MAAAhD,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAArC,EAAA,CAAAuB,aAAA;MAAA,MAAAe,IAAA,GAAAtC,EAAA,CAAAqB,WAAA;MAAA,OAASrB,EAAA,CAAAwB,WAAA,CAAAc,IAAA,CAAAC,IAAA,EAAS;IAAA,EAAC;IACtBvC,EAAA,CAAAE,cAAA,aAC0F;IADvFF,EAAA,CAAAgB,UAAA,mBAAAiC,0FAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAAmB,IAAA;MAAA,MAAAa,OAAA,GAAAlD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA0B,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IACqEnD,EAAA,CAAAG,MAAA,IAC1D;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;IAfZJ,EAAA,CAAAK,SAAA,GACY;IADZL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,4BACY;IAIjCnC,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,+BAAwC;IAIvCnC,EAAA,CAAAK,SAAA,GACiC;IADjCL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,8BACiC;IAIqDnC,EAAA,CAAAK,SAAA,GAC1D;IAD0DL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,0BAC1D;;;;;;IAtB5CnC,EAAA,CAAAqD,uBAAA,GAAgC;IAC9BrD,EAAA,CAAAE,cAAA,cAAmC;IAE/BF,EAAA,CAAA2B,UAAA,IAAA2B,qEAAA,4BAsBc;IAChBtD,EAAA,CAAAI,YAAA,EAAiB;IACjBJ,EAAA,CAAAE,cAAA,eAEmB;IAFbF,EAAA,CAAAgB,UAAA,wBAAAuC,mFAAAC,MAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAAuC,IAAA;MAAA,MAAAnB,IAAA,GAAAtC,EAAA,CAAAqB,WAAA;MAAA,OAAcrB,EAAA,CAAAwB,WAAA,CAAAc,IAAA,CAAAoB,MAAA,CAAAF,MAAA,CAAiB;IAAA,EAAC;IAGxCxD,EAAA,CAAAC,SAAA,aAAqC;IACrCD,EAAA,CAAAE,cAAA,eAA4G;IAC1GF,EAAA,CAAAG,MAAA,GAGF;;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGTJ,EAAA,CAAA2D,qBAAA,EAAe;;;;IApCS3D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAU,UAAA,oBAAmB;IA8BvCV,EAAA,CAAAK,SAAA,GAGF;IAHEL,EAAA,CAAA4D,kBAAA,MAAA5D,EAAA,CAAAmC,WAAA,0BAAAnC,EAAA,CAAA6D,WAAA,QAAAC,MAAA,CAAAC,IAAA,cAAAD,MAAA,CAAAC,IAAA,CAAAC,MAAA,uBAGF;;;;;IAUNhE,EAAA,CAAAE,cAAA,cAAgE;IAKtDF,EAAA,CAAAC,SAAA,yBAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IAPhBJ,EAAA,CAAAqD,uBAAA,GAAmD;IACjDrD,EAAA,CAAA2B,UAAA,IAAAsC,6DAAA,kBAUM;IACRjE,EAAA,CAAA2D,qBAAA,EAAe;;;;IAXN3D,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAU,UAAA,SAAAwD,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAA0B;;;;;IAajCpE,EAAA,CAAAE,cAAA,cAAkC;IAKxBF,EAAA,CAAAC,SAAA,yBAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;IArKpBJ,EAAA,CAAAqD,uBAAA,GAAuD;IACrDrD,EAAA,CAAAE,cAAA,aAAwB;IAMdF,EAAA,CAAA2B,UAAA,IAAA0C,6CAAA,iBAC6D;IAC7DrE,EAAA,CAAA2B,UAAA,IAAA2C,6CAAA,iBAEwC;IAC1CtE,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,cAAkC;IAChCF,EAAA,CAAAuE,cAAA,EACwC;IADxCvE,EAAA,CAAAE,cAAA,cACwC;IAEpCF,EAAA,CAAAC,SAAA,gBAA0E;IAE5ED,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,aAA6E;IAC3EF,EAAA,CAAAC,SAAA,gBAEyF;IAM3FD,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAwE,eAAA,EAC6G;IAD7GxE,EAAA,CAAAE,cAAA,eAC6G;IADxGF,EAAA,CAAAgB,UAAA,mBAAAyD,8DAAA;MAAAzE,EAAA,CAAAkB,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,EAAAmD,OAAA,CAAAC,WAAA,KAAAD,OAAA,CAAAE,UAAA,GAA6B,IAAI;IAAA,EAAC;IAE9C7E,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,IAAqC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAA2B,UAAA,KAAAmD,8CAAA,kBACmF;IACnF9E,EAAA,CAAA2B,UAAA,KAAAoD,iDAAA,qBAKS;IACX/E,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAE,cAAA,eAAiE;IAC/CF,EAAA,CAAAgB,UAAA,sBAAAgE,4EAAAxB,MAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAAwD,IAAA;MAAA,MAAAO,OAAA,GAAAjF,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAyD,OAAA,CAAAC,cAAA,GAAA1B,MAAA;IAAA,EAAoC;IAC2BxD,EAAA,CAAAI,YAAA,EAAiB;IAGlGJ,EAAA,CAAAE,cAAA,eAA6D;IAkBtDF,EAAA,CAAAgB,UAAA,mBAAAmE,4DAAA;MAAAnF,EAAA,CAAAkB,aAAA,CAAAwD,IAAA;MAAA,MAAAU,OAAA,GAAApF,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA4D,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACzBrF,EAAA,CAAAC,SAAA,eAAwD;IACxDD,EAAA,CAAAE,cAAA,gBAAmB;IACrBF,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIPJ,EAAA,CAAAE,cAAA,eAA0D;IAArBF,EAAA,CAAAgB,UAAA,mBAAAsE,8DAAA;MAAAtF,EAAA,CAAAkB,aAAA,CAAAwD,IAAA;MAAA,MAAAa,OAAA,GAAAvF,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA+D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACvDxF,EAAA,CAAAE,cAAA,aAAiF;IAEjFF,EAAA,CAAAC,SAAA,eAAuE;IAEvED,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIPJ,EAAA,CAAA2B,UAAA,KAAA8D,8CAAA,kBAUM;IAENzF,EAAA,CAAA2B,UAAA,KAAA+D,uDAAA,4BAsCe;IACjB1F,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAA2B,UAAA,KAAAgE,uDAAA,2BAYe;IACf3F,EAAA,CAAA2B,UAAA,KAAAiE,sDAAA,iCAAA5F,EAAA,CAAA6F,sBAAA,CAYc;IAGhB7F,EAAA,CAAAI,YAAA,EAAM;IACRJ,EAAA,CAAA2D,qBAAA,EAAe;;;;;IA5KoD3D,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA8F,eAAA,KAAAC,GAAA,EAAAC,MAAA,CAAAC,gBAAA,EAAwD;IAKzGjG,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAU,UAAA,UAAAsF,MAAA,CAAAE,MAAA,CAAa;IAEblG,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAU,UAAA,SAAAsF,MAAA,CAAAE,MAAA,CAAY;IAwBblG,EAAA,CAAAK,SAAA,IAAuE;IAAvEL,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA8F,eAAA,KAAAK,GAAA,GAAAH,MAAA,CAAAxF,cAAA,kBAAAwF,MAAA,CAAAxF,cAAA,CAAAO,QAAA,WAAuE;IACpEf,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,kBAAA,KAAAN,EAAA,CAAAmC,WAAA,kCAAqC;IAClBnC,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAU,UAAA,UAAAsF,MAAA,CAAApB,WAAA,IAAAoB,MAAA,CAAAxF,cAAA,CAAoC;IAEpDR,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,UAAA,SAAAsF,MAAA,CAAApB,WAAA,CAAiB;IAqC5B5E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAmC,WAAA,iCACF;IAQgCnC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAoG,qBAAA,UAAAJ,MAAA,CAAAK,aAAA,CAA2B;IACzDrG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAmC,WAAA,6BACF;IAIMnC,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAU,UAAA,UAAAsF,MAAA,CAAAM,SAAA,CAAgB;IAYPtG,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAU,UAAA,SAAAsF,MAAA,CAAAM,SAAA,CAAe;IA4CrBtG,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAU,UAAA,SAAAsF,MAAA,CAAAO,WAAA,CAAmB,aAAAC,IAAA;;;;;IAuCzBxG,EAAA,CAAAC,SAAA,cAA8H;;;;;IAC9HD,EAAA,CAAAC,SAAA,cAA4G;;;;;IAF9GD,EAAA,CAAAE,cAAA,cAA4D;IAC1DF,EAAA,CAAA2B,UAAA,IAAA8E,mDAAA,kBAA8H;IAC9HzG,EAAA,CAAA2B,UAAA,IAAA+E,mDAAA,kBAA4G;IAC9G1G,EAAA,CAAAI,YAAA,EAAM;;;;IAFEJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAU,UAAA,UAAAiG,OAAA,CAAAT,MAAA,CAAa;IACblG,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAU,UAAA,SAAAiG,OAAA,CAAAT,MAAA,CAAY;;;;;IAKflG,EAAA,CAAAE,cAAA,WAA6C;IAC3CF,EAAA,CAAAC,SAAA,cAAoE;IACtED,EAAA,CAAAI,YAAA,EAAO;;;;IADwBJ,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAU,UAAA,QAAAkG,OAAA,CAAAC,QAAA,CAAAD,OAAA,CAAApG,cAAA,kBAAAoG,OAAA,CAAApG,cAAA,CAAAsG,IAAA,GAAA9G,EAAA,CAAA+G,aAAA,CAAsC;;;;;;IAFtE/G,EAAA,CAAAE,cAAA,cAAiH;IAA5CF,EAAA,CAAAgB,UAAA,mBAAAgG,yEAAA;MAAAhH,EAAA,CAAAkB,aAAA,CAAA+F,IAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,EAAA0F,OAAA,CAAAtC,WAAA,KAAAsC,OAAA,CAAArC,UAAA,GAA6B,IAAI;IAAA,EAAC;IAC/G7E,EAAA,CAAA2B,UAAA,IAAAwF,0DAAA,kBAEO;IACRnH,EAAA,CAAAI,YAAA,EAAM;;;;IAHEJ,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAU,UAAA,UAAA0G,OAAA,CAAAxC,WAAA,IAAAwC,OAAA,CAAA5G,cAAA,CAAoC;;;;;;IAHhDR,EAAA,CAAAE,cAAA,cAA8D;IAE1DF,EAAA,CAAA2B,UAAA,IAAA0F,mDAAA,kBAIM;IACNrH,EAAA,CAAAE,cAAA,cAA8B;IACHF,EAAA,CAAAgB,UAAA,mBAAAsG,mEAAA;MAAAtH,EAAA,CAAAkB,aAAA,CAAAqG,IAAA;MAAA,MAAAC,OAAA,GAAAxH,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAgG,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAClDzH,EAAA,CAAAC,SAAA,aAA8B;IAChCD,EAAA,CAAAI,YAAA,EAAM;;;;IAR0CJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,UAAA,UAAAgH,OAAA,CAAAC,UAAA,CAAiB;;;;;;;;;;;IAavE3H,EAAA,CAAAE,cAAA,cAA2D;IAEvDF,EAAA,CAAAgB,UAAA,sBAAA4G,iFAAApE,MAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAA2G,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAsG,OAAA,CAAA5C,cAAA,GAAA1B,MAAA;IAAA,EAAoC;IAGbxD,EAAA,CAAAI,YAAA,EAAiB;IAC1CJ,EAAA,CAAAE,cAAA,cAAqE;IAA5BF,EAAA,CAAAgB,UAAA,mBAAA+G,mEAAA;MAAA/H,EAAA,CAAAkB,aAAA,CAAA2G,IAAA;MAAA,MAAAG,OAAA,GAAAhI,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAwG,OAAA,CAAAP,eAAA,EAAiB;IAAA,EAAC;IAClEzH,EAAA,CAAAC,SAAA,aAA8B;IAChCD,EAAA,CAAAI,YAAA,EAAM;;;;IALJJ,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAiI,WAAA,oBAAAC,OAAA,CAAAP,UAAA,CAAoC;IACpC3H,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA8F,eAAA,IAAAqC,GAAA,GAAAD,OAAA,CAAAP,UAAA,EAA8C;;;;;IAwBxD3H,EAAA,CAAAE,cAAA,cAAsE;IAK5DF,EAAA,CAAAC,SAAA,yBAA+D;IACjED,EAAA,CAAAI,YAAA,EAAM;;;;;IA3DnBJ,EAAA,CAAAqD,uBAAA,GAAwD;IACvDrD,EAAA,CAAAE,cAAA,cAA+B;IAKrBF,EAAA,CAAA2B,UAAA,IAAAyG,6CAAA,kBAGM;IACNpI,EAAA,CAAA2B,UAAA,IAAA0G,6CAAA,kBAaM;IAENrI,EAAA,CAAA2B,UAAA,IAAA2G,6CAAA,kBASM;IAeRtI,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAA2B,UAAA,IAAA4G,6CAAA,kBAUM;IACRvI,EAAA,CAAAI,YAAA,EAAM;IAETJ,EAAA,CAAA2D,qBAAA,EAAe;;;;IA/DF3D,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA8F,eAAA,IAAAC,GAAA,EAAAyC,MAAA,CAAAvC,gBAAA,EAAwD;IAGjBjG,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,UAAA,UAAA8H,MAAA,CAAAb,UAAA,CAAiB;IAIf3H,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,UAAA,UAAA8H,MAAA,CAAAb,UAAA,CAAiB;IAenC3H,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAU,UAAA,SAAA8H,MAAA,CAAAb,UAAA,IAAAa,MAAA,CAAAC,YAAA,CAAgC;IA4BzDzI,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAU,UAAA,SAAA8H,MAAA,CAAArE,UAAA,kBAAAqE,MAAA,CAAArE,UAAA,CAAAC,QAAA,CAA0B;;;;;IAgC/BpE,EAAA,CAAAE,cAAA,eAAoE;IAC9CF,EAAA,CAAAG,MAAA,GAA8C;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;IAAlDJ,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,qCAA8C;;;;;;IAIlEnC,EAAA,CAAAE,cAAA,eACqD;IADhDF,EAAA,CAAAgB,UAAA,mBAAA0H,mEAAA;MAAA,MAAAC,WAAA,GAAA3I,EAAA,CAAAkB,aAAA,CAAA0H,IAAA;MAAA,MAAAC,QAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA/I,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAuH,OAAA,CAAAC,SAAA,CAAAH,QAAA,kBAAAA,QAAA,CAAA9H,QAAA,EAAA8H,QAAA,CAA8B;IAAA,EAAC;IAE3C7I,EAAA,CAAAC,SAAA,eAAgF;IAChFD,EAAA,CAAAE,cAAA,aAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAD9DJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAU,UAAA,QAAAuI,OAAA,CAAAC,iBAAA,CAAAL,QAAA,kBAAAA,QAAA,CAAA/B,IAAA,GAAA9G,EAAA,CAAA+G,aAAA,CAAqC;IAClB/G,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAoD,iBAAA,CAAA6F,OAAA,CAAAE,oBAAA,CAAAN,QAAA,kBAAAA,QAAA,CAAApI,IAAA,EAAuC;;;;;;;;;;;IAvBrET,EAAA,CAAAE,cAAA,cAAqB;IACnBF,EAAA,CAAAC,SAAA,cAAuF;IACvFD,EAAA,CAAAE,cAAA,aAAgC;IAAAF,EAAA,CAAAG,MAAA,GAA6C;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClFJ,EAAA,CAAAE,cAAA,YAA6B;IAAAF,EAAA,CAAAG,MAAA,GAA8C;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/EJ,EAAA,CAAAE,cAAA,cAA8C;IACLF,EAAA,CAAAC,SAAA,cAA8B;IAAAD,EAAA,CAAAI,YAAA,EAAO;IAC5EJ,EAAA,CAAAE,cAAA,gBAA2G;IAClGF,EAAA,CAAAgB,UAAA,mBAAAoI,+DAAA5F,MAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAAmI,IAAA;MAAA,MAAAC,OAAA,GAAAtJ,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA8H,OAAA,CAAAC,eAAA,CAAA/F,MAAA,CAAuB;IAAA,EAAC,2BAAAgG,uEAAAhG,MAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAAmI,IAAA;MAAA,MAAAI,OAAA,GAAAzJ,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAiI,OAAA,CAAAC,OAAA,GAAAlG,MAAA;IAAA;;IAAxCxD,EAAA,CAAAI,YAAA,EAGmG;IAGvGJ,EAAA,CAAAE,cAAA,SAAG;IAAAF,EAAA,CAAAG,MAAA,IAAyC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAA2B,UAAA,KAAAgI,6CAAA,mBAEM;IACN3J,EAAA,CAAAC,SAAA,iBAAiC;IACjCD,EAAA,CAAAE,cAAA,gBAAiC;IAC/BF,EAAA,CAAA2B,UAAA,KAAAiI,6CAAA,mBAIM;IACR5J,EAAA,CAAAI,YAAA,EAAM;;;;IAvB0BJ,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,oCAA6C;IAChDnC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,sCAA8C;IAQhEnC,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAoG,qBAAA,gBAAApG,EAAA,CAAAmC,WAAA,sCAA2D;IAHzBnC,EAAA,CAAAU,UAAA,YAAAmJ,MAAA,CAAAH,OAAA,CAAqB,mBAAA1J,EAAA,CAAA8J,eAAA,KAAAC,GAAA;IAM/D/J,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAmC,WAAA,uCAAyC;IACtCnC,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAU,UAAA,UAAAmJ,MAAA,CAAAG,aAAA,kBAAAH,MAAA,CAAAG,aAAA,CAAAhG,MAAA,QAAiC;IAK0BhE,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAU,UAAA,YAAAmJ,MAAA,CAAAG,aAAA,CAAgB;;;;;;;;;ADjOvF,OAAM,MAAOC,eAAe;EAmD1BC,YACUC,MAAc,EACdC,KAAmB,EACnBC,MAAc,EACdC,eAAgC,EAChCC,SAA2B,EAC3BC,eAAgC,EAChCC,EAAqB,EACrBC,WAAwB,EACxBC,gBAAkC,EAClCC,cAA8B,EAC9BC,aAA4B,EAC5BC,aAA4B,EAC5BC,aAA4B,EAC5BC,cAA8B,EAC9BC,mBAAwC,EACxCC,iBAAoC,EACpCC,aAA4B,EAC5BC,aAA4B,EAC5BC,UAAkC,EACbC,UAAe;IAnBpC,KAAAnB,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IACW,KAAAC,UAAU,GAAVA,UAAU;IApEzC,KAAA1G,WAAW,GAAYjF,WAAW,CAAC4L,YAAY;IAC/C,KAAAC,YAAY,GAAwB,EAAE;IACtC,KAAAtG,cAAc,GAAY,KAAK;IAC/B,KAAAuG,WAAW,GAAY,KAAK;IAC5B,KAAA9D,UAAU,GAAY,KAAK;IAC3B,KAAA+D,QAAQ,GAAe,EAAE;IACzB,KAAApF,SAAS,GAAQ,EAAE;IACnB,KAAAqF,IAAI,GAAW,kBAAkB;IAEjC,KAAA5H,IAAI,GAAW,EAAE;IAEjB,KAAA6H,SAAS,GAAoB,IAAIC,KAAK,EAAY;IAClD,KAAAC,MAAM,GAAW,EAAE;IAOnB,KAAAhK,SAAS,GAAe,EAAE;IAI1B,KAAAiK,MAAM,GAAY,KAAK;IAKvB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAA9F,MAAM,GAAY,KAAK;IAEvB,KAAArB,UAAU,GAAY,KAAK;IAC3B,KAAA4D,YAAY,GAAa,KAAK;IAC9B,KAAAwD,WAAW,GAAQtM,WAAW,CAACsM,WAAW;IAE1C,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAA7F,aAAa,GAAQ,CAAC;IACtB,KAAA8F,YAAY,GAAQ,EAAE;IACf,KAAAzC,OAAO,GAAW,EAAE;IAEnB,KAAA0C,QAAQ,GAAGzM,WAAW,CAAC0M,WAAW;IAE1C,KAAAvL,eAAe,GAAgB,IAAI;IACnC,KAAAwL,cAAc,GAAW,cAAc;IAEvC,KAAArG,gBAAgB,GAAS,KAAK;IAC9B,KAAAsG,iBAAiB,GAAS,KAAK;IAwB7B,IAAI,CAACtG,gBAAgB,GAAG,IAAI,CAACiF,iBAAiB,CAACsB,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAACrB,iBAAiB,CAACsB,aAAa,CAAC,kBAAkB,CAAC;IAEjF,IAAI,CAACC,OAAO,GAAG,GAAG9M,WAAW,CAAC0M,WAAW,EAAE;IAC3C,IAAIxM,iBAAiB,CAAC,IAAI,CAACyL,UAAU,CAAC,EAAE;MACtC,IAAI,CAACoB,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACC,KAAK;MACtC,IAAI,IAAI,CAACH,WAAW,GAAG,GAAG,EAAE;QAC1B,IAAI,CAACnG,WAAW,GAAG,IAAI;OACxB,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,KAAK;;MAE1B7G,MAAM,CAAC,MAAK;QACV,IAAI,CAACmF,UAAU,GAAG,IAAI,CAACsG,aAAa,CAAC2B,gBAAgB,EAAE;MACzD,CAAC,CAAC;;IAGJ,IAAInN,WAAW,CAAC4L,YAAY,EAAE;MAC5B,IAAI,CAACrF,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC6G,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAAC7G,MAAM,GAAG,KAAK;;EAGvB;EAEA8G,QAAQA,CAAA;IACN,IAAI,CAAClM,eAAe,GAAGmM,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IACtD,IAAI,CAACC,UAAU,GAAGR,MAAM,CAACQ,UAAU;IAEnC,IAAI,CAACC,gBAAgB,GAAGH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAACG,eAAe;IAC1G,IAAI,CAAC7C,eAAe,CAAC8C,iBAAiB,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MAE/D,IAAI,CAACnH,aAAa,GAAGmH,IAAI,IAAI,CAAC;MAC9B,IAAI,IAAI,CAACnH,aAAa,GAAG,CAAC,EAAE;QAE1B,IAAI,IAAI,CAACA,aAAa,GAAG,CAAC,EAAE;UAC1B,IAAI,CAACA,aAAa,GAAG,IAAI;;;MAG7B,IAAI,CAACoE,EAAE,CAACgD,YAAY,EAAE;MACtB,IAAI,CAAChD,EAAE,CAACiD,aAAa,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAAClD,eAAe,CAACmD,WAAW,EAAE,CAACJ,SAAS,CAAEC,IAAS,IAAI;MACzD,IAAIA,IAAI,EAAE;QACR,IAAII,SAAS,GAAGJ,IAAI,CAAC/M,IAAI,CAACoN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC9J,IAAI,GAAG6J,SAAS;;MAEvB,IAAI,CAACnD,EAAE,CAACgD,YAAY,EAAE;MACtB,IAAI,CAAChD,EAAE,CAACiD,aAAa,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAACtC,aAAa,CAAC0C,eAAe,CAACP,SAAS,CAAEQ,GAAY,IAAI;MAC5D,IAAG,CAAC,IAAI,CAACxH,WAAW,EAAC;QACnB,IAAG,CAACwH,GAAG,EAAC;UACN,IAAI,CAACpG,UAAU,GAAG,KAAK;;QAEzB,IAAI,CAACc,YAAY,GAAGsF,GAAG;;IAE3B,CAAC,CAAC;IACF,IAAIC,IAAI,GAAQ,IAAI,CAAC5D,KAAK,CAAC6D,GAAG,CAAC,SAAS,CAAC;IAGzC,IAAID,IAAI,EAAE;MACR,IAAI,CAACxD,eAAe,CAAC0D,WAAW,CAACF,IAAI,CAAC;;IAExC,IAAI,IAAI,CAACb,UAAU,GAAG,GAAG,EAAE;MACzB,IAAI,CAACnB,YAAY,GAAG,IAAI;KACzB,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,KAAK;;IAE3B,IAAI,CAACmC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAG,IAAI,CAACpD,cAAc,EAAEqD,cAAc,EAAC;MACrC,IAAI,CAAClK,UAAU,GAAG,IAAI,CAAC6G,cAAc,CAACqD,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;;IAExG,IAAI,CAAChE,eAAe,CAACiE,aAAa,CAChC;MACEC,kBAAkB,EAAE,IAAI,CAACvK,UAAU,EAAEC,QAAQ,IAAI;KAClD,CACF;EACH;EAGAuK,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrI,SAAS,EAAE;MAClB,IAAIzG,iBAAiB,CAAC,IAAI,CAACyL,UAAU,CAAC,EAAE;QACtC,IAAGsD,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,EAAC;UACpC,MAAMC,GAAG,GAAQF,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;UACnD,IAAGC,GAAG,EAAC;YACLA,GAAG,CAACC,KAAK,EAAE;;UAEb,IAAI,CAACC,EAAE,CAACzM,IAAI,EAAE;;;;EAKtB;EAEAgH,eAAeA,CAAC0F,KAAU;IACxB,IAAI,IAAI,CAACvF,OAAO,CAAC1F,MAAM,IAAI,CAAC,EAAE;MAC5B,MAAMkL,iBAAiB,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACzF,OAAO,CAAC0F,WAAW,EAAE,CAAC;MAC3E,IAAI,CAACpF,aAAa,GAAG,IAAI,CAACkC,YAAY,CAACmD,MAAM,CAAEC,IAAS,IACtD,IAAI,CAACH,gBAAgB,CAACG,IAAI,CAAC7O,IAAI,CAAC2O,WAAW,EAAE,CAAC,CAACG,QAAQ,CAACL,iBAAiB,CAAC,CAC3E;KACF,MAAM,IAAI,IAAI,CAACxF,OAAO,CAAC1F,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACgG,aAAa,GAAG,IAAI,CAACkC,YAAY;;EAE1C;EAEAiD,gBAAgBA,CAACK,IAAS;IACxB,OAAOA,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;EAC9D;EACAxG,iBAAiBA,CAACyG,GAAQ;IACxB,OAAO/P,gBAAgB,CAACgQ,cAAc,CAACD,GAAG,EAAE,IAAI,CAACvD,QAAQ,CAAC;EAE5D;EACAyD,iBAAiBA,CAACC,WAAgB;IAChC,IAAIA,WAAW,IAAI,KAAK,EAAE;MACxB,OAAO,GAAG;KACX,MAAM,IAAIA,WAAW,IAAI,MAAM,EAAE;MAChC,OAAO,GAAG;KACX,MAAM,IAAIA,WAAW,IAAI,KAAK,EAAE;MAC/B,OAAO,GAAG;KACX,MAAM,IAAIA,WAAW,IAAI,IAAI,EAAE;MAC9B,OAAO,GAAG;;IAEZ,OAAO,EAAE;EACX;EAEAC,WAAWA,CAAA;IACT,MAAMC,WAAW,GAAG,IAAI,CAAChF,cAAc,CAACgF,WAAW;IACnD,IAAI,CAACjE,MAAM,GAAGiE,WAAW,CAACjE,MAAM;IAChC,IAAI,CAAC3B,KAAK,CAAC6F,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAClE,MAAM,CAAC;EACvC;EAEMmE,SAASA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACbD,KAAI,CAAC7J,SAAS,GAAG,IAAI;MACrB,MAAM+J,sBAAsB,GAAGpD,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;MAC5ED,YAAY,CAACqD,KAAK,EAAE;MACpBrD,YAAY,CAACsD,OAAO,CAAC,uBAAuB,EAAEF,sBAAsB,IAAI,EAAE,CAAC;MAC3E,MAAMF,KAAI,CAAChN,MAAM,EAAE;MACnB8J,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAEJ,KAAI,CAAC3P,cAAc,CAAC;MACrDyM,YAAY,CAACsD,OAAO,CAAC,mBAAmB,EAAEC,IAAI,CAACC,SAAS,CAACN,KAAI,CAACrO,SAAS,CAAC,CAAC;MACzEqO,KAAI,CAAC/F,KAAK,CAAC6F,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClChD,YAAY,CAACsD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCJ,KAAI,CAAC/F,KAAK,CAAC6F,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;MAC/BE,KAAI,CAACpM,IAAI,GAAG,EAAE;MACdoM,KAAI,CAACO,YAAY,EAAE;IAAC;EACtB;EAGAtC,eAAeA,CAAA;IACb,MAAMuC,OAAO,GAAG,IAAI,CAAC3F,cAAc,CAAC2F,OAAO;IAC3C,IAAIA,OAAO,CAACC,OAAO,EAAE;MACnB,MAAM9O,SAAS,GAAG6O,OAAO,CAACC,OAAO;MACjC9O,SAAS,CAAC,CAAC,CAAC,CAAC+O,QAAQ,GAAG,IAAI;MAC5B,MAAM9P,QAAQ,GAAGkM,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD,IAAInM,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAC/B,IAAI,CAACP,cAAc,GAAGsB,SAAS,CAACwM,IAAI,CAAE5E,OAAY,IAAKA,OAAO,CAAC3I,QAAQ,IAAIA,QAAQ,CAAC;;MAEtF,IAAI,CAACe,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACkI,aAAa,GAAG,IAAI,CAAClI,SAAS;MACnC,IAAI,CAACoK,YAAY,GAAG,IAAI,CAAClC,aAAa;;EAE1C;EACAvI,aAAaA,CAACqP,GAAO;IACnBC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAChB,IAAI,CAAC9H,SAAS,CAAC8H,GAAG,CAAC;EACrB;EAEAG,YAAYA,CAAA;IACV,IAAIlQ,QAAQ,GAAGkM,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC/C,IAAI,CAACnB,MAAM,GAAGkB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI;IACpD,IAAIgE,UAAU,GAAG,aAAa;IAC9B,IAAIvE,MAAM,EAAEwE,SAAS,EAAEC,WAAW,EAAE;MAClCD,SAAS,EAAEE,WAAW,CAACC,KAAK,CAAC;QAAE7Q,IAAI,EAAE;MAAa,CAAE,CAAC,CAClD8Q,IAAI,CAAEC,gBAAqB,IAAI;QAE9B,IAAIA,gBAAgB,CAACC,KAAK,IAAI,SAAS,EAAE;UACvCP,UAAU,GAAG,SAAS;SACvB,MAAM,IAAIM,gBAAgB,CAACC,KAAK,IAAI,QAAQ,EAAE;UAC7CP,UAAU,GAAG,aAAa;SAC3B,MAAM;UACLA,UAAU,GAAG,QAAQ;;QAGvB,IAAIA,UAAU,KAAK,aAAa,EAAE;UAChC,IAAI,CAACzF,WAAW,GAAG,IAAI;UACvB,IAAI,CAACiG,wBAAwB,EAAE;SAChC,MAAM;UACL,IAAI,CAACC,sBAAsB,CAACC,MAAM,CAAC7Q,QAAQ,CAAC,CAAC;;MAEjD,CAAC,EACE8Q,KAAU,IAAI,CAEf,CAAC,CAAC;;EAEV;EAEAF,sBAAsBA,CAAC5Q,QAAgB;IACrC,IAAI2I,OAAO,GAAG,IAAI,CAAC5H,SAAS,CAACwM,IAAI,CAC9BwD,CAAM,IAAKA,CAAC,CAAC/Q,QAAQ,KAAKA,QAAQ,CACpC;IACD,IAAI,CAAC2I,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI,CAAC5H,SAAS,CAAC,CAAC,CAAC;;IAE7B4H,OAAO,CAACmH,QAAQ,GAAG,IAAI;IACvB,IAAI,IAAI,CAACrQ,cAAc,IAAIO,QAAQ,EAAE;MACnC,IAAI,CAACP,cAAc,GAAGkJ,OAAO,CAAC3I,QAAQ;MACtCkM,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE7G,OAAO,CAAC3I,QAAQ,CAAC;MAClDkM,YAAY,CAACsD,OAAO,CAAC,mBAAmB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3O,SAAS,CAAC,CAAC;MAEzEiQ,QAAQ,CAACC,MAAM,EAAE;KAClB,MAAM;MACL,IAAI,CAACxR,cAAc,GAAGkJ,OAAO,CAAC3I,QAAQ;MACtC,IAAI,CAACP,cAAc,GAAGkJ,OAAO,CAAC3I,QAAQ;MACtCkM,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE7G,OAAO,CAAC3I,QAAQ,CAAC;;IAEpD,IAAI,CAAC0J,EAAE,CAACiD,aAAa,EAAE;EACzB;EAEAgE,wBAAwBA,CAAA;IACtB,IAAI7R,iBAAiB,CAAC,IAAI,CAACyL,UAAU,CAAC,EAAE;MACtC,IAAIqB,MAAM,EAAEwE,SAAS,EAAEC,WAAW,EAAE;QAClC,IAAI,CAACtG,aAAa,CAACmH,IAAI,EAAE;QACzBtF,MAAM,CAACwE,SAAS,CAACC,WAAW,CAACc,kBAAkB,CAC5CC,QAAQ,IAAI;UACX,IAAI,aAAa,IAAIhB,SAAS,EAAE;YAC9B,IAAI,CAACrG,aAAa,CAACmH,IAAI,EAAE;YACzBd,SAAS,CAACC,WAAW,CAACc,kBAAkB,CACrCC,QAAQ,IAAI;cACX,IAAI,CAACC,GAAG,GAAGD,QAAQ,CAACE,MAAM,CAACC,QAAQ;cACnC,IAAI,CAACC,GAAG,GAAGJ,QAAQ,CAACE,MAAM,CAACG,SAAS;cACpC,IAAI,CAACC,IAAI,GAAG,CAAC;cACb,IAAI,CAACvB,UAAU,GAAG,IAAI;cAGtB,IAAIwB,MAAM,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACT,GAAG,EAAE,IAAI,CAACG,GAAG,CAAC;cACvD,IAAI,CAACO,oBAAoB,CAACJ,MAAM,CAAC;YACnC,CAAC,EACAb,KAAU,IAAI,CAEf,CAAC,EAAE;cACDkB,UAAU,EAAE,KAAK;cACjBC,OAAO,EAAE;aACV,CACF;;QAEL,CAAC,EACAnB,KAAK,IAAI;UACR,MAAM9Q,QAAQ,GAAGkM,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAIvN,WAAW,CAACsT,aAAa;UAC9E,IAAI,CAACtB,sBAAsB,CAACC,MAAM,CAAC7Q,QAAQ,CAAC,CAAC;QAC/C,CAAC,CACF;;;EAGP;EACA+R,oBAAoBA,CAACJ,MAAY;IAC7B,IAAI,CAACQ,QAAQ,CAACC,OAAO,CAAC;MAAE,QAAQ,EAAET;IAAM,CAAE,EAAE,CAACU,OAAY,EAAEC,MAAW,KAAI;MAC1E,IAAIA,MAAM,IAAIV,MAAM,CAACC,IAAI,CAACU,cAAc,CAACC,EAAE,IAAIH,OAAO,CAAC,CAAC,CAAC,EAAE;QAC3D,IAAII,UAAU,GAAG,IAAI;QACrBJ,OAAO,CAAC,CAAC,CAAC,CAACK,kBAAkB,CAACC,OAAO,CAAEC,GAAQ,IAAI;UACnD,IAAIA,GAAG,CAACC,KAAK,CAACrE,QAAQ,CAAC,SAAS,CAAC,EAAEiE,UAAU,GAAGG,GAAG,CAACE,UAAU;QAChE,CAAC,CAAC;QAEF,IAAInK,OAAO,GAAG,IAAI,CAAC5H,SAAS,CAACwM,IAAI,CAC9BwD,CAAM,IAAKA,CAAC,CAAClR,OAAO,KAAK4S,UAAU,CACrC;QACD,IAAI,CAAC1I,aAAa,CAACvI,IAAI,EAAE;QACzB,IAAI,CAACoP,sBAAsB,CAACjI,OAAO,CAAC3I,QAAQ,IAAI,CAAC,CAAC;;IAElD,CAAC,CAAC;EACN;EACE+S,UAAUA,CAAA;IACR,MAAMC,QAAQ,GAAG;MACfC,MAAM,EAAE/G,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MACtC+G,SAAS,EAAEhH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE;MAClDgH,OAAO,EAAE,IAAI,CAACA;KACf;IACD,IAAIH,QAAQ,CAACE,SAAS,EAAE;MACtB,IAAI,CAACvJ,WAAW,CAACyJ,OAAO,CAACJ,QAAQ,CAAC,CAC/BxG,SAAS,CAAC;QACT6G,IAAI,EAAGrG,GAAQ,IAAI;UACjB,IAAI,CAAC1H,aAAa,GAAG,CAAC;UACtB,IAAI,CAAC8F,YAAY,GAAG,EAAE;UACtB,IAAI4B,GAAG,CAACP,IAAI,EAAEoD,OAAO,EAAE5M,MAAM,EAAE;YAC7B,IAAI,CAACqC,aAAa,GAAG,CAAC;YACtB,IAAI0H,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAACyD,WAAW,CAACrQ,MAAM,EAAE;cAC1C,IAAI,CAACqC,aAAa,GAAG0H,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAACyD,WAAW,CAACrQ,MAAM;cAC3D,IAAI,CAACmI,YAAY,GAAG4B,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAACyD,WAAW;;YAGrD,IAAItG,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAAC0D,eAAe,IAAIvG,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAAC0D,eAAe,CAACtQ,MAAM,EAAE;cACrF,IAAI,CAACqC,aAAa,IAAI0H,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAAC0D,eAAe,CAACtQ,MAAM;cAChE,IAAI,CAACmI,YAAY,GAAG,IAAI,CAACA,YAAY,CAACoI,MAAM,CAACxG,GAAG,CAACP,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,CAAC0D,eAAe,CAAC;;YAEnF,IAAI,CAAC9J,eAAe,CAACgK,iBAAiB,CAAC,IAAI,CAACnO,aAAa,CAAC;YAC1D,IAAI,CAACmE,eAAe,CAACiK,gBAAgB,CAAC,IAAI,CAACtI,YAAY,CAAC;WAEzD,MAAM;YACL,IAAI,CAAC3B,eAAe,CAACgK,iBAAiB,CAAC,CAAC,CAAC;YACzC,IAAI,CAAChK,eAAe,CAACiK,gBAAgB,CAAC,EAAE,CAAC;;UAE3C,IAAI,CAACxJ,mBAAmB,CAACyJ,eAAe,GAAG,IAAI,CAACvI,YAAY;QAC9D;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAACT,QAAQ,GAAG,EAAE;;EAGtB;EAEOiJ,mBAAmBA,CAACjJ,QAAY,EAAEkJ,aAAiB;IACxD,IAAIlJ,QAAQ,CAAC1H,MAAM,EAAE;MAEnB0H,QAAQ,CAACgI,OAAO,CAAEpE,IAAS,IAAI;QAC7BsF,aAAa,CAAClB,OAAO,CAAElG,IAAS,IAAI;UAClC,IAAI8B,IAAI,CAACuF,cAAc,KAAKrH,IAAI,CAACqH,cAAc,EAAE;YAC/C,IAAI,CAACnJ,QAAQ,CAACoJ,IAAI,CAACxF,IAAI,CAAC;;QAI5B,CAAC,CAAC;MACJ,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAAC5D,QAAQ,GAAGkJ,aAAa;;IAG/B3H,YAAY,CAACsD,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/E,QAAQ,CAAC,CAAC;EACtE;EAEAqJ,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAAC7J,iBAAiB,CAACsB,aAAa,CAAC,cAAc,CAAC,EAAE;MACxD,IAAI,CAAC9B,WAAW,CAACqK,2BAA2B,EAAE,CAACxH,SAAS,CAAEQ,GAAQ,IAAI;QACpE,IAAIA,GAAG,CAACiH,OAAO,IAAIjH,GAAG,CAACP,IAAI,CAACxJ,MAAM,EAAE;UAClC,IAAI,CAACkQ,OAAO,GAAGnG,GAAG,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC0G,OAAO;UAClCjH,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC2D,OAAO,CAAC;UAC9C,IAAI,CAACJ,UAAU,EAAE;;MAErB,CAAC,CAAC;KACH,MAAM;MACL7G,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;MACrC,IAAI,CAAC2D,OAAO,GAAG,CAAC;MAChB,IAAI,CAACJ,UAAU,EAAE;;EAGrB;EAEAmB,eAAeA,CAAA;IAEbC,UAAU,CAAC,MAAK;MACd,IAAIC,OAAO,GAAGlI,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;MAC7C,IAAIiI,OAAO,IAAIA,OAAO,KAAK,EAAE,EAAE;QAC7B,IAAI,CAACJ,2BAA2B,EAAE;OACnC,MAAM;QACL,IAAI,CAACjB,UAAU,EAAE;;MAEnB,IAAI,CAACnJ,gBAAgB,CAACyK,aAAa,CAAC7H,SAAS,CAC1C8H,OAAO,IAAM,IAAI,CAAC/O,SAAS,GAAG+O,OAAQ,CACxC;MACD,IAAI,CAAC,IAAI,CAAC/O,SAAS,EAAE;QACnB,IAAI,CAACA,SAAS,GAAG,IAAI,CAACuE,aAAa,CAACoD,GAAG,CAAC,WAAW,CAAC;QACpD,IAAG,CAAC,IAAI,CAAC3H,SAAS,EAAE;UAClB,IAAI6O,OAAO,GAAQlI,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;UAClD,IAAGiI,OAAO,IAAIA,OAAO,KAAK,EAAE,EAAE;YAC5BA,OAAO,GAAG3E,IAAI,CAAC8E,KAAK,CAACH,OAAO,CAAC;YAC7B,IAAI,CAAC7O,SAAS,GAAG6O,OAAO,CAAC7O,SAAS,CAACoJ,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;YACzD,MAAM6F,OAAO,GAAQzV,UAAU,CAAC,IAAI,CAACwG,SAAS,CAAC;YAE/C,IAAIkP,IAAI,GAAQ,CAACD,OAAO,CAACE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAEC,OAAO,CAC3D,CAAC,CACF;YACD,MAAMC,OAAO,GAAG,IAAIC,IAAI,EAAE;YAC1BD,OAAO,CAACE,OAAO,CAACF,OAAO,CAACG,OAAO,EAAE,GAAGC,QAAQ,CAACP,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC3K,aAAa,CAACoF,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC3J,SAAS,EAAE;cAClD0P,OAAO,EAAEL,OAAO;cAChBM,IAAI,EAAE,GAAG;cACTC,QAAQ,EAAE;aACX,CAAC;YACFjJ,YAAY,CAACkJ,UAAU,CAAC,SAAS,CAAC;;;;MAMxC,IAAI,CAAC3K,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACoB,YAAY,CAAC,cAAc,CAAC,CAAC+B,SAAS,CAAC;QACvE6G,IAAI,EAAGrG,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAE;YACP,IAAI,CAACrC,QAAQ,GAAGqC,GAAG;YACnB,IAAI,CAACtD,EAAE,CAACiD,aAAa,EAAE;;QAI3B,CAAC;QACDmE,KAAK,EAAGuE,GAAQ,IAAI;UAClBrF,OAAO,CAACc,KAAK,CAACuE,GAAG,CAAC;QACpB;OACD,CAAC;MAGF,IAAI,CAAC5K,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAACpB,KAAK,CAACoB,YAAY,CAAC,UAAU,CAAC,CAAC+B,SAAS,CAAC;QACnE6G,IAAI,EAAGrG,GAAQ,IAAI;UACjB,IAAIP,IAAI,GAAGO,GAAG,EAAEO,IAAI,CAAEqF,GAAQ,IAAKA,GAAG,CAAC0C,GAAG,CAACjH,WAAW,EAAE,KAAK,MAAM,CAAC;UACpE,IAAI5B,IAAI,EAAE,IAAI,CAAC7B,IAAI,GAAG,GAAG,IAAI6B,IAAI,CAAC8I,WAAW,IAAI,IAAI,CAAC3K,IAAI,CAAC;QAC7D,CAAC;QACDkG,KAAK,EAAGuE,GAAQ,IAAI;UAClBrF,OAAO,CAACc,KAAK,CAACuE,GAAG,CAAC;QACpB;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC;EACP;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAC/K,YAAY,CAACkI,OAAO,CAAE8C,GAAG,IAAI;MAChCA,GAAG,CAACC,WAAW,EAAE;IACnB,CAAC,CAAC;EACJ;EAEA/F,YAAYA,CAAA;IAGVzD,YAAY,CAACkJ,UAAU,CAAC,cAAc,CAAC;IACvClJ,YAAY,CAACkJ,UAAU,CAAC,cAAc,CAAC;IACvClJ,YAAY,CAACkJ,UAAU,CAAC,aAAa,CAAC;IACtClJ,YAAY,CAACkJ,UAAU,CAAC,UAAU,CAAC;IACnClJ,YAAY,CAACkJ,UAAU,CAAC,iCAAiC,CAAC;IAC1D,IAAItW,iBAAiB,CAAC,IAAI,CAACyL,UAAU,CAAC,EAAE;MACtCqB,MAAM,CAACoF,QAAQ,CAACC,MAAM,EAAE;;EAE5B;EAEM7O,MAAMA,CAAA;IAAA,IAAAuT,MAAA;IAAA,OAAAtG,iBAAA;MAEV,MAAMrP,QAAQ,GAAGkM,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;MACxD,MAAMyJ,eAAe,GAAG1J,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO;MAC1E,MAAM0J,UAAU,GAAG3J,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;MAC5D,MAAMmD,sBAAsB,GAAGpD,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;MAC5E,MAAM2J,YAAY,GAAG5J,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;MAC/DD,YAAY,CAACqD,KAAK,EAAE;MACpBrD,YAAY,CAACsD,OAAO,CAAC,uBAAuB,EAAEF,sBAAsB,IAAI,EAAE,CAAC;MAC3EqG,MAAI,CAACpQ,SAAS,GAAG,IAAI;MAErBwQ,cAAc,CAACxG,KAAK,EAAE;MACtBoG,MAAI,CAAC/L,gBAAgB,CAACoM,YAAY,CAAC,EAAE,CAAC;MAGtCL,MAAI,CAAC7L,aAAa,CAACmM,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;MAC3CN,MAAI,CAACtM,KAAK,CAAC6F,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;MAC7ByG,MAAI,CAAClM,eAAe,CAACgK,iBAAiB,CAAC,CAAC,CAAC;MACzCkC,MAAI,CAAClM,eAAe,CAACiK,gBAAgB,CAAC,EAAE,CAAC;MACzCiC,MAAI,CAAClM,eAAe,CAAC0D,WAAW,CAAC,IAAI,CAAC;MACtCjB,YAAY,CAACsD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCtD,YAAY,CAACsD,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;MACzCtD,YAAY,CAACsD,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;MAClCmG,MAAI,CAACtM,KAAK,CAAC6F,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClChD,YAAY,CAACkJ,UAAU,CAAC,cAAc,CAAC;MACvClJ,YAAY,CAACkJ,UAAU,CAAC,UAAU,CAAC;MACnClJ,YAAY,CAACsD,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;MACvCtD,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAExP,QAAQ,CAAC;MAC1CkM,YAAY,CAACsD,OAAO,CAAC,iBAAiB,EAAEoG,eAAe,CAAC;MACxD1J,YAAY,CAACsD,OAAO,CAAC,aAAa,EAAEqG,UAAU,CAAC;MAC/C3J,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAEsG,YAAY,CAAC;MAClD,MAAMH,MAAI,CAACrM,MAAM,CAAC4M,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAC;EAEpC;EAEA9I,YAAYA,CAAA;IACV,IAAI,CAAC7D,eAAe,CAAC4M,WAAW,EAAE,CAAC3J,SAAS,CAAEQ,GAAG,IAAI;MAEnD,IAAIA,GAAG,EAAEnC,SAAS,EAAE;QAClB,IAAIuL,UAAU,GAAGlK,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QACjD,IAAI,CAACiK,UAAU,EAAE;UACf,IAAI9J,eAAe,GAAGU,GAAG,CAACnC,SAAS,CAAC0C,IAAI,CAAEwD,CAAC,IAAKA,CAAC,CAACsF,SAAS,CAAC;UAC5D,IAAI,CAAC/J,eAAe,IAAIU,GAAG,CAACnC,SAAS,CAAC5H,MAAM,GAAG,CAAC,EAAE;YAChD,IAAI,CAACqJ,eAAe,GAAGU,GAAG,CAACnC,SAAS,CAAC,CAAC,CAAC;;UAEzC,IAAIyB,eAAe,EAAE;YACnB,IAAI,CAACD,gBAAgB,GAAGH,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAGD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;;;QAG5F,IAAI,CAACtB,SAAS,GAAGmC,GAAG,CAACnC,SAAS;;IAGlC,CAAC,CAAC;EACJ;EAEAyL,UAAUA,CAACC,IAAS;IAClBrK,YAAY,CAACsD,OAAO,CAAC,UAAU,EAAE+G,IAAI,CAAC;IACtC,IAAI,CAAC/M,SAAS,CAACgN,GAAG,CAACD,IAAI,CAAC;EAC1B;EAGAtO,SAASA,CAACjI,QAAa,EAAC2I,OAAY;IAClC,IAAI2D,eAAe,GAAQ3D,OAAO,CAACkC,SAAS,CAACyD,MAAM,CAAEiI,IAAS,IAAKA,IAAI,CAACF,SAAS,CAAC;IAClF,MAAMI,aAAa,GAAGvK,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACtD,IAAIsK,aAAa,KAAKzW,QAAQ,EAAE;MAC9B,IAAI,CAACoC,MAAM,EAAE;MACb,IAAI,CAACmH,eAAe,CAACmN,cAAc,CAACpK,eAAe,CAAC,CAAC,CAAC,CAACqK,IAAI,CAAC;MAC5DzK,YAAY,CAACsD,OAAO,CAAC,UAAU,EAACxP,QAAQ,CAAC;MACzC,IAAI,CAACsJ,MAAM,CAAC4M,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CACxB1F,IAAI,CAAC,MAAK;QACT,IAAI1R,iBAAiB,CAAC,IAAI,CAACyL,UAAU,CAAC,EAAE;UACtCqB,MAAM,CAACoF,QAAQ,CAACC,MAAM,EAAE;;MAE5B,CAAC,CAAC;KACL,MAAM;MACL,IAAI,CAACnN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACsG,aAAa,CAACwM,UAAU,EAAE;;IAEjC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACxN,KAAK,CAAC6D,GAAG,CAAC,SAAS,CAAC;IAC5C,IAAG,IAAI,CAAC1B,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CAAClP,iBAAiB,CAAC8X,uBAAuB,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC,EAAE,IAAI,EAAE;QAC9F,SAAS,EAAEnO,OAAO,CAACjJ,IAAI;QACtB,SAAS,EAAE,IAAI,CAACmX,WAAW,GAAG,IAAI,CAACA,WAAW,CAACE,YAAY,GAAG,kBAAkB;QAChF,YAAY,EAAE,IAAI,CAAC1N,KAAK,CAAC6D,GAAG,CAAC,QAAQ,CAAC;QACrC,aAAa,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,GAAG,CAAC,YAAY,CAAC,EAAE8J,UAAU;QACvD,WAAW,EAAE,IAAI,CAAC3N,KAAK,CAAC6D,GAAG,CAAC,YAAY,CAAC,EAAE+J;OAE9C,CAAC;;EAEJ;EAEA9V,MAAMA,CAAA;IACJ,IAAG,IAAI,CAACqK,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CAAClP,iBAAiB,CAACkY,mBAAmB,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC;;IAE1F,IAAI,CAAC5N,MAAM,CAAC4M,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EACAU,UAAUA,CAAA;IACR,IAAI,CAACxM,aAAa,CAACwM,UAAU,EAAE;EAC/B;EACFlQ,eAAeA,CAAA;IACb,IAAI,CAACE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAG,IAAI,CAACA,UAAU,EAAC;MACjB,IAAI,CAACc,YAAY,GAAG,IAAI;;EAE5B;EACA5B,QAAQA,CAACqR,SAAc;IACrB,IAAIA,SAAS,EAAE;MACb,OAAO,GAAG,IAAI,CAACzL,OAAO,WAAWyL,SAAS,EAAE;KAC7C,MAAM;MACL,OAAO,EAAE;;EAEb;EACAnL,cAAcA,CAAA;IACZ,IAAI,CAACvC,eAAe,CAAC2N,4BAA4B,EAAE,CAAC5K,SAAS,CAAC;MAC5D6G,IAAI,EAAGrG,GAAQ,IAAI;QACjBd,YAAY,CAACsD,OAAO,CAAC,cAAc,EAAExC,GAAG,CAACP,IAAI,GAACO,GAAG,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC9L,KAAK,GAAC,EAAE,CAAC;MACrE,CAAC;MAAEmQ,KAAK,EAAGuE,GAAQ,IAAI;QACrBrF,OAAO,CAACc,KAAK,CAACuE,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEArT,kBAAkBA,CAAA;IAChB,IAAG,IAAI,CAACwJ,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CAAClP,iBAAiB,CAACqY,wBAAwB,EAAE,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,IAAI,CAAC;;IAEjG,IAAI,CAAC/N,MAAM,CAAC4M,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAzR,QAAQA,CAAA;IACN,IAAI,CAACoS,WAAW,GAAG,IAAI,CAACxN,KAAK,CAACiO,mBAAmB,CAAC,SAAS,CAAC;IAC5D,IAAG,IAAI,CAAC9L,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CACnBlP,iBAAiB,CAACuY,kBAAkB,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,EAAE,IAAI,EACjF;QACA,SAAS,EAAC,IAAI,CAACV,WAAW,GAAC,IAAI,CAACA,WAAW,CAACE,YAAY,GAAC,kBAAkB;QAC3E,YAAY,EAAE,IAAI,CAAC1N,KAAK,CAAC6D,GAAG,CAAC,QAAQ,CAAC;QACtC,aAAa,EAAE,IAAI,CAAC7D,KAAK,CAAC6D,GAAG,CAAC,YAAY,CAAC,EAAE8J,UAAU;QACvD,WAAW,EAAE,IAAI,CAAC3N,KAAK,CAAC6D,GAAG,CAAC,YAAY,CAAC,EAAE+J,QAAQ;QACnD,YAAY,EAAE/K,YAAY,CAACC,OAAO,CAAC,WAAW;OAE/C,CAAC;;IAEF,IAAI,CAAC7C,MAAM,CAAC4M,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA5R,YAAYA,CAAA;IACV,IAAI,CAACuS,WAAW,GAAG,IAAI,CAACxN,KAAK,CAACiO,mBAAmB,CAAC,SAAS,CAAC;IAC5D,IAAG,IAAI,CAAC9L,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CAAClP,iBAAiB,CAACwY,wBAAwB,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAG;QAClF,SAAS,EAAC,IAAI,CAACX,WAAW,GAAC,IAAI,CAACA,WAAW,CAACE,YAAY,GAAC;OAC1D,CAAC;;IAEF,IAAI,CAACzN,MAAM,CAAC4M,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAtU,kBAAkBA,CAAA;IAChB,IAAG,IAAI,CAAC4J,iBAAiB,EAAC;MAC1B,IAAI,CAAClB,UAAU,CAAC4D,KAAK,CAAClP,iBAAiB,CAACyY,gBAAgB,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAG;QAC1E,SAAS,EAAC,IAAI,CAACZ,WAAW,GAAC,IAAI,CAACA,WAAW,CAACE,YAAY,GAAC;OAC1D,CAAC;;IAEF,IAAI,CAACzN,MAAM,CAAC4M,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EACA9N,oBAAoBA,CAAC1I,IAAY;IAC/B;IACA,MAAMgY,cAAc,GAAG,aAAahY,IAAI,CAACiP,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAC,EAAE,CAAC,EAAE;IAC3E,OAAO,IAAI,CAACnF,SAAS,CAACmO,OAAO,CAACD,cAAc,CAAC;EAC/C;EAAC,QAAAE,CAAA,G;qBAhqBU1O,eAAe,EAAAjK,EAAA,CAAA4Y,iBAAA,CAAA5Y,EAAA,CAAA6Y,MAAA,GAAA7Y,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAC,YAAA,GAAA/Y,EAAA,CAAA4Y,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAjZ,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAI,eAAA,GAAAlZ,EAAA,CAAA4Y,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAApZ,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAO,eAAA,GAAArZ,EAAA,CAAA4Y,iBAAA,CAAA5Y,EAAA,CAAAsZ,iBAAA,GAAAtZ,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAS,WAAA,GAAAvZ,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAU,gBAAA,GAAAxZ,EAAA,CAAA4Y,iBAAA,CAAAa,EAAA,CAAAC,cAAA,GAAA1Z,EAAA,CAAA4Y,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAA5Z,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAe,aAAA,GAAA7Z,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAgB,aAAA,GAAA9Z,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAiB,cAAA,GAAA/Z,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAkB,mBAAA,GAAAha,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAmB,iBAAA,GAAAja,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAiB,cAAA,GAAA/Z,EAAA,CAAA4Y,iBAAA,CAAAE,EAAA,CAAAoB,aAAA,GAAAla,EAAA,CAAA4Y,iBAAA,CAAAuB,EAAA,CAAAC,sBAAA,GAAApa,EAAA,CAAA4Y,iBAAA,CAuEhBnZ,WAAW;EAAA;EAAA,QAAA4a,EAAA,G;UAvEVpQ,eAAe;IAAAqQ,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;iBAAfC,GAAA,CAAA/L,cAAA,EAAgB;QAAA,UAAA3O,EAAA,CAAA2a,eAAA;;;;;;;;QC1C7B3a,EAAA,CAAA2B,UAAA,IAAAiZ,uCAAA,4BA8Ke;QAGf5a,EAAA,CAAA2B,UAAA,IAAAkZ,uCAAA,2BAkEe;QACf7a,EAAA,CAAAE,cAAA,kBACiH;QADvGF,EAAA,CAAAgB,UAAA,2BAAA8Z,2DAAAtX,MAAA;UAAA,OAAAkX,GAAA,CAAA7V,UAAA,GAAArB,MAAA;QAAA,EAAwB,oBAAAuX,oDAAA;UAAA,OAAWL,GAAA,CAAA/C,UAAA,EAAY;QAAA,EAAvB;QAEhC3X,EAAA,CAAA2B,UAAA,IAAAqZ,sCAAA,2BA4Bc;QAChBhb,EAAA,CAAAI,YAAA,EAAW;;;QAnRIJ,EAAA,CAAAU,UAAA,SAAAga,GAAA,CAAAnU,WAAA,KAAAmU,GAAA,CAAAzU,gBAAA,CAAsC;QAiLrCjG,EAAA,CAAAK,SAAA,GAAsC;QAAtCL,EAAA,CAAAU,UAAA,UAAAga,GAAA,CAAAnU,WAAA,IAAAmU,GAAA,CAAAzU,gBAAA,CAAsC;QAmE5CjG,EAAA,CAAAK,SAAA,GAAwB;QAAxBL,EAAA,CAAAU,UAAA,YAAAga,GAAA,CAAA7V,UAAA,CAAwB,gBAAA7E,EAAA,CAAA8J,eAAA,IAAAmR,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}