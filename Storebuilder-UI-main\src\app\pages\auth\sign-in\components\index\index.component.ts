import {Component, Inject, OnInit, PLATFORM_ID} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {MessageService} from 'primeng/api';
import {TranslateService} from '@ngx-translate/core';
import jwt_decode from 'jwt-decode';
import {CookieService} from 'ngx-cookie-service';
import * as CryptoJS from 'crypto-js';
import { v4 as uuidv4 } from 'uuid';
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {CountryISO, PhoneNumberFormat, SearchCountryField} from "ngx-intl-tel-input-gg";
import {TenantRecords, UserConsent} from "@core/interface";
import {
  AppDataService,
  LoaderService,
  MainDataService,
  AuthTokenService,
  StoreService,
  AuthService,
  CartService, PermissionService, UserService, CustomGAService
} from "@core/services";
import {GaActionEnum, GoogleAnalyticsService} from "ngx-google-analytics";
import {UserConsentType} from "@core/enums/user";
import {GaLocalActionEnum} from "@core/enums/ga-local-action-enum";
import {isPlatformBrowser} from "@angular/common";
import { GTMService } from '@core/services/gtm.service';


@Component({
  selector: 'app-index',
  templateUrl: './index.component.html',
  styleUrls: ['./index.component.scss'],
})
export class IndexComponent implements OnInit {
  phoneNumber: string = '';
  password: string = '';
  submitted: boolean = false;
  countryPhoneCode: string = '';
  phoneLength: number = 12;
  phoneInputLength: number = 12;
  countryPhoneNumber: string = '';
  cookieValue: any;
  decoded: any;
  redirctURL: any;
  products: Array<any> = [];
  PhoneNumberFormat = PhoneNumberFormat;
  CustomCountryISO: any;
  SearchCountryField = SearchCountryField;
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];
  signInForm: FormGroup;
  cartListCount: any = 0;
  cartListData: any = [];
  customPlaceHolder: any = '';
  isGoogleAnalytics: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth: number;
  constructor(
    private store: StoreService,
    private auth: AuthService,
    private messageService: MessageService,
    private router: Router,
    private cartService: CartService,
    private translate: TranslateService,
    private cookieService: CookieService,
    private authTokenService: AuthTokenService,
    private route: ActivatedRoute, private fb: FormBuilder,
    private mainDataService: MainDataService,
    private permissionService: PermissionService,
    private loaderService: LoaderService,
    private appDataService: AppDataService,
    private $gaService: GoogleAnalyticsService,
    private userService: UserService,
    private customGAService: CustomGAService,
    @Inject(PLATFORM_ID) private platformId: any,
    private $gtmService:GTMService
  ) {

    let tenantId = localStorage.getItem('tenantId');
    if (tenantId && tenantId !== '') {
      if (tenantId == '1') {
        this.customPlaceHolder = 'XXXXXXXXX';
      } else if (tenantId == '2') {
        this.customPlaceHolder = 'XXXXXXXXX';
      } else if (tenantId == '3') {
        this.customPlaceHolder = 'XXXXXXXXX';
      } else if (tenantId == '4') {
        this.customPlaceHolder = 'XXXXXXXXXX';
      }
    }
    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth
    }
  }
  tagName:any=GaActionEnum;
  ngOnInit(): void {
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')
    this.$gtmService.pushPageView('signIn')


    if (!localStorage.getItem("isoCode")) {

      const tenants = this.appDataService.tenants
      if (tenants.records != undefined) {
        let tenantId = localStorage.getItem('tenantId');
        let data = tenants.records;
        let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();
        localStorage.setItem('isoCode', arr?.isoCode??"UG");
        this.store.set('allCountryTenants', tenants.records);
      }

    } else {
      this.CustomCountryISO = localStorage.getItem("isoCode");

    }
    this.route.queryParams.subscribe((params) => {

      this.redirctURL = params.returnUrl;
    });
    this.signInForm = this.fb.group({
      phoneNumber: ['', Validators.required]
    });

    if (this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')
      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value)
    }

  }

  modelChanged(password: any) {
    this.password = password;
  }

  async login() {
    if(this.isGoogleAnalytics){
    this.$gaService.event(GaLocalActionEnum.CLICK_ON_CONTINUE_FOR_SIGN_IN, '', 'SIGN_IN_STEP2', 1, true);
    }
    this.loaderService.show();
    this.submitted = true;
    this.auth
      .login({
        username: this.signInForm.controls.phoneNumber.value.e164Number.slice(1),
        password: this.password,
      })
      .subscribe({
        next: async (res: any) => {
          if (res?.success && res.data.role == 'consumer') {
            if( this.isGoogleAnalytics &&  this.permissionService.getTagFeature('LOGIN')){
              this.$gaService.event(this.tagName.LOGIN, '','',1,true, {"user_ID":res.data.mobileNumber});

            }
            const data: UserConsent = {
              consentType: UserConsentType.Cookie,
              sessionId: localStorage.getItem('consumer-consent-sessionId') || '',
              consent: true,
              userId: res.data.id
            }
            this.userService.updateUserConsent(data).subscribe({
              next: (res: any) => {
              }
            })
            this.mainDataService.setUserData(res.data)
            this.store.set('profile', res.data);
            this.store.set('userPhone', res.data.mobileNumber);
            localStorage.setItem('userId', res.data.id)
            this.store.set('timeInterval', new Date().getTime());
            let token = res.data.authToken.replace('bearer ', '');

            this.decoded = jwt_decode(token);

            let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(
              0
            );
            localStorage.removeItem('visited');
            const dateNow = new Date();
            dateNow.setDate(dateNow.getDate() + parseInt(days));
            let encryptedMessage = CryptoJS.AES.encrypt(
              token,
              'paysky'
            ).toString();
            localStorage.setItem('auth_enc', encryptedMessage);
            this.cookieService.set('authToken', token, {
              expires: dateNow,
              path: '/',
              sameSite: 'Strict',
            });
            this.cookieValue = this.cookieService.get('authToken');
            localStorage.removeItem('isGuest');
            this.authTokenService.authTokenSet(this.cookieValue);


            this.loaderService.hide();
            if (res?.data?.currency)
              this.store.set('currency', res.data.currency);

            localStorage.setItem('refreshToken', res.data.refreshToken);
            this.store.set('refreshToken', res.data.refreshToken);


            const cartData: any = {
              sessionId: localStorage.getItem('sessionId')
            };
            const cartId = localStorage.getItem('cartId');
            await this.checkCart(cartData, cartId);
            if (res.data.isPasswodExpired) {
              this.router.navigateByUrl('/change-password');
              this.messageService.add({
                severity: 'info',
                summary: this.translate.instant(
                  'ResponseMessages.changePassword'
                ),
                detail: this.translate.instant(
                  'ResponseMessages.passwordExpirationChange'
                ),
              });
            } else {
              if (this.redirctURL) {
                this.router.navigate([this.redirctURL]);
                this.redirctURL = null;
              } else {
                this.router.navigate(['/']);
              }
              this.messageService.add({
                severity: 'success',
                summary: this.translate.instant('ResponseMessages.login'),
                detail: this.translate.instant(
                  'ResponseMessages.loggedInSuccessfully'
                ),
              });
            }
          } else {

            this.store.set('profile', '');

            this.loaderService.hide();

            this.messageService.add({
              severity: 'error',
              summary: res?.message
                ? res.message
                : this.translate.instant(
                  'ErrorMessages.invalidUserNameOrPassword'
                ),
            });
            localStorage.setItem('isGuest', 'true');
          }
        },
        error: (err: any) => {
          this.store.set('profile', '');

          this.loaderService.hide();

          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('ErrorMessages.fetchError'),
            detail: err.message,
          });
          localStorage.setItem('isGuest', 'true');
        },
      });
  }

  omit_special_char(event: any) {

    let key;
    key = event.charCode;
    return (key > 47 && key < 58);
  }


  getAllCart(data: any): void {
    this.products = [];
    let cartData: any = {
      sessionId: data.sessionId,
    };
    let applyTo = localStorage.getItem('apply-to');
    if (applyTo && applyTo != '') {
      cartData['applyTo'] = applyTo
    }
    this.cartService.getCart(cartData)
      .subscribe({
        next: (res: any) => {
          this.cartListCount = 0;
          this.cartListData = [];
          if (res.data?.records?.length) {
            this.cartListCount = 0;
            if (res.data.records[0].cartDetails.length) {
              this.cartListCount = res.data.records[0].cartDetails.length;
              this.cartListData = res.data.records[0].cartDetails;

            }
            if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {
              this.cartListCount += res.data.records[0].cartDetailsDPay.length;
              this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay)
            }
            this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData);
            this.mainDataService.setCartLenghtData(this.cartListCount);
            this.mainDataService.setCartItemsData(this.cartListData);

          } else {
            this.mainDataService.setCartLenghtData(0);
            this.mainDataService.setCartItemsData([]);
          }
        }
      });
  }

  public compareCartProducts(products: [], storeProducts: []) {
    if (products.length) {

      products.forEach((item: any) => {
        storeProducts.forEach((data: any) => {
          if (item.specsProductId === data.specsProductId) {
            this.products.push(item);
          }
        });
      });
    } else {
      this.products = storeProducts;
    }
    this.store.set('cartProducts', this.products);
    localStorage.setItem('addedProducts', JSON.stringify(this.products));
  }

  getShipmentMethodByTenantId(data: any) {
    if (this.permissionService.hasPermission('Shipment-Fee')) {
      this.cartService.getShipmentMethodByTenantId().subscribe((res: any) => {
        if (res.success && res.data.length) {
          // this.applyTo = res.data[0].applyTo
          localStorage.setItem('apply-to', res.data[0].applyTo);
          this.getAllCart(data);
        }
      })
    } else {
      localStorage.setItem('apply-to', '2');
      this.getAllCart(data);
    }

  }

  checkCart(cartData: any, cartId: any) {
    return new Promise<void>((resolve, reject) => {
      if (!cartData.sessionId) {
        localStorage.setItem('sessionId', GuidGenerator.newGuid());
        cartData.sessionId = localStorage.getItem('sessionId');
        this.getAllCart(cartData);
        resolve(); // Resolve the promise
      } else {
        if (cartId && cartId != '') {
          cartData.cartId = parseInt(cartId);
        } else {
          cartData.cartId = 0;
        }
        this.cartService.updateCart(cartData)
          .subscribe({
            next: (res: any) => {
              if (res?.data?.cartItems?.length) {
                this.cartListData = res.data.cartItems;
                this.cartListCount = res.data.cartItems.length;
              }
              this.mainDataService.setCartLenghtData(this.cartListCount);
              this.mainDataService.setCartItemsData(this.cartListData);
              this.getShipmentMethodByTenantId(cartData);
              resolve(); // Resolve the promise
            },
            error: (err: any) => {
              this.cartListCount = 0;
              this.cartListData = [];
              this.mainDataService.setCartLenghtData(this.cartListCount);
              this.mainDataService.setCartItemsData(this.cartListData);
              this.getShipmentMethodByTenantId(cartData);
              reject(err); // Reject the promise with the error
            }
          });
      }
    });
  }

  resetPassword() {
    if(this.isGoogleAnalytics){
    this.$gaService.event(GaLocalActionEnum.CLICK_ON_FORGOT_PASSWORD, '', 'FORGOT_PASSWORD', 1, true);
    }
    this.router.navigate(['/reset-password']);
  }


  reloadCurrentPage(pageId: number, title: string) {
    let pageNumber: string | null ;
    if(title == 'Terms and Conditions'){
      pageNumber = localStorage.getItem('TermsAndConditionsId')
    }else{
      pageNumber = localStorage.getItem('PrivacyPolicyId')
    }
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>
      this.router.navigate(['/about-us/'], {
        queryParams: { pageId: pageNumber, title: title },
      })
    );
  }

  // Track signup CTA click
  onSignupCtaClick(ctaLocation: string) {
    if (this.isGoogleAnalytics) {
      this.customGAService.signupCtaClickEvent(ctaLocation);
    }
  }
}
class GuidGenerator {
  static newGuid() {
    return uuidv4()
  }
}
