{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IndexComponent } from './components/index/index.component';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { SharedModule } from \"../../shared/modules/shared.module\";\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nlet OrderRefundModule = class OrderRefundModule {};\nOrderRefundModule = __decorate([NgModule({\n  declarations: [IndexComponent],\n  imports: [CommonModule, SharedModule, DialogModule, ButtonModule, CheckboxModule, InputTextareaModule, RouterModule.forChild(routes)]\n})], OrderRefundModule);\nexport { OrderRefundModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "IndexComponent", "RouterModule", "routes", "SharedModule", "DialogModule", "ButtonModule", "CheckboxModule", "InputTextareaModule", "OrderRefundModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\order-refund\\order-refund.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IndexComponent } from './components/index/index.component';\r\nimport { RouterModule } from \"@angular/router\";\r\nimport { routes } from \"./routes\";\r\nimport { SharedModule } from \"../../shared/modules/shared.module\";\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    DialogModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    InputTextareaModule,\r\n    RouterModule.forChild(routes)\r\n  ]\r\n})\r\nexport class OrderRefundModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,uBAAuB;AAgBpD,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB,GAAI;AAArBA,iBAAiB,GAAAC,UAAA,EAd7BX,QAAQ,CAAC;EACRY,YAAY,EAAE,CACZV,cAAc,CACf;EACDW,OAAO,EAAE,CACPZ,YAAY,EACZI,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBN,YAAY,CAACW,QAAQ,CAACV,MAAM,CAAC;CAEhC,CAAC,C,EACWM,iBAAiB,CAAI;SAArBA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}