{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { FeaturedDataTypeEnum, FeatureType } from \"@core/interface\";\nimport { ShowRoomTypeEnum } from \"@core/enums\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../shared/components/product-slider/product-slider.component\";\nimport * as i8 from \"../../../../shared/components/section/section.component\";\nimport * as i9 from \"@shared/components/landing-templates/template-one/template-one.component\";\nimport * as i10 from \"../main-slider/main-slider.component\";\nimport * as i11 from \"../banner/banner.component\";\nimport * as i12 from \"../category-slider/category-slider.component\";\nimport * as i13 from \"../section-category/section.component\";\nconst _c0 = function (a0) {\n  return {\n    \"mt-3\": a0\n  };\n};\nfunction IndexComponent_section_0_ng_container_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-section-category\", 11);\n    i0.ɵɵelement(2, \"app-category-slider\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r8.index;\n    const feature_r3 = ctx_r8.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, i_r4 === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", feature_r3.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"categories\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelement(2, \"app-banner\", 14);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r9.index;\n    const feature_r3 = ctx_r9.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, i_r4 === 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"banner\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"app-mtn-section\", 16);\n    i0.ɵɵelement(2, \"app-mtn-product-slider\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r10.index;\n    const feature_r3 = ctx_r10.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, i_r4 === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"categoryID\", feature_r3.categoryId)(\"featureProduct\", feature_r3.feature)(\"fetchStatus\", feature_r3.fetchStatus)(\"title\", feature_r3.title)(\"topNumber\", 100);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"products\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵtemplate(2, IndexComponent_section_0_ng_container_4_div_2_Template, 3, 5, \"div\", 7);\n    i0.ɵɵtemplate(3, IndexComponent_section_0_ng_container_4_div_3_Template, 3, 4, \"div\", 8);\n    i0.ɵɵtemplate(4, IndexComponent_section_0_ng_container_4_div_4_Template, 3, 9, \"div\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const feature_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"category\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"banner\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"feature\");\n  }\n}\nfunction IndexComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 2);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelement(2, \"app-main-slider\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵtemplate(4, IndexComponent_section_0_ng_container_4_Template, 5, 3, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r0.mainSlider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredData);\n  }\n}\nfunction IndexComponent_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-landing-template-one\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2, a3) {\n  return {\n    \"navbar-inactive-old-mobile\": a0,\n    \"navbar-active\": a1,\n    \"navbar-inactive\": a2,\n    \"navbar-inactive-mobile\": a3\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nfunction IndexComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 18);\n    i0.ɵɵtemplate(2, IndexComponent_ng_container_1_ng_container_2_Template, 2, 0, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(3, _c1, ctx_r1.isMobileView && !ctx_r1.isMobileTemplate, (ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && !ctx_r1.isMobileTemplate && ctx_r1.isMobileView, !(ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && !ctx_r1.isMobileTemplate, !(ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && ctx_r1.isMobileTemplate))(\"ngStyle\", i0.ɵɵpureFunction1(8, _c2, ctx_r1.isMobileTemplate ? ctx_r1.isMobileView ? \"80px\" : \"15px\" : ctx_r1.isMobileView ? \"230px\" : \"50px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templateId === 1);\n  }\n}\nexport class IndexComponent {\n  onResize(event) {\n    this.updateZoomClass();\n  }\n  updateZoomClass() {\n    if (isPlatformBrowser(this.platformId)) {\n      const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n      if (zoomLevel <= 91) {\n        this.zoomLevelClass = 'zoom-110';\n      }\n      if (zoomLevel <= 112) {\n        this.zoomLevelClass = 'zoom-90';\n      } else if (zoomLevel <= 125) {\n        this.zoomLevelClass = 'zoom-80';\n      } else if (zoomLevel <= 134) {\n        this.zoomLevelClass = 'zoom-75';\n      } else if (zoomLevel <= 150) {\n        this.zoomLevelClass = 'zoom-67';\n      } else if (zoomLevel <= 200) {\n        this.zoomLevelClass = 'zoom-50';\n      } else if (zoomLevel <= 300) {\n        this.zoomLevelClass = 'zoom-33';\n      } else if (zoomLevel <= 400) {\n        this.zoomLevelClass = 'zoom-25';\n      } else {\n        this.zoomLevelClass = 'default-zoom';\n      }\n    }\n  }\n  constructor(store, mainDataService, homeService, productService, messageService, reviewsService, translate, router, cookieService, authTokenService, loaderService, permissionService, appDataService, cd, platformId) {\n    this.store = store;\n    this.mainDataService = mainDataService;\n    this.homeService = homeService;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.reviewsService = reviewsService;\n    this.translate = translate;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.loaderService = loaderService;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.cd = cd;\n    this.platformId = platformId;\n    this.sections = [];\n    this.mainSlider = [];\n    this.categories = [];\n    this.enableFeaturedProducts = false;\n    this.enableNewProducts = false;\n    this.banner = [{\n      src: 'assets/images/banner/banner1.jpg'\n    }, {\n      src: 'assets/images/banner/banner2.jpg'\n    }, {\n      src: 'assets/images/banner/banner3.jpg'\n    }];\n    this.showRoomConfigurationRes = [];\n    this.productOffers = [];\n    this.featuredData = [];\n    this.templateId = 1;\n    this.zoomLevelClass = 'default-zoom';\n    this.isMobileTemplate = false;\n    this.isLayoutTemplate = false;\n    this.isMobileView = window.screen.width < 768;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.appDataService.layoutTemplate) {\n        _this.navbarData = yield _this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n        _this.allBanners = yield _this.appDataService.layoutTemplate.find(section => section.type === 'main_banner');\n        _this.mainDataService.setBannerData({\n          isBannerActive: _this.allBanners?.isActive ?? false,\n          isNavbarDataActive: _this.navbarData?.isActive ?? false\n        });\n      }\n      if (!_this.permissionService.hasPermission('Layout-Template')) {\n        _this.getShowRoom();\n        _this.isLayoutTemplate = false;\n      } else {\n        _this.isLayoutTemplate = true;\n      }\n      _this.cd.detectChanges();\n    })();\n  }\n  findShowRoomTypeId(showRoomId) {\n    return this.showRoomConfigurationRes.find(element => element.showRoomTypeId == showRoomId);\n  }\n  getShowRoom() {\n    this.showRoomConfigurationRes = this.appDataService.showRoomConfiguration?.records;\n    if (this.findShowRoomTypeId(ShowRoomTypeEnum.MainBanner)) {\n      this.getMainSliderData();\n    }\n    this.addFeatureData();\n    const foundFeatureProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && element.featureProduct);\n    const foundCategoryProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && !element.featureProduct && element.categoryId);\n    if (foundFeatureProduct?.length) {\n      this.handleFeature(foundFeatureProduct);\n    } else {\n      this.handleFeatureNolength();\n    }\n    if (foundCategoryProduct.length) {\n      this.handleFoundProduct(foundCategoryProduct);\n    }\n  }\n  addFeatureData() {\n    const foundCategories = this.findShowRoomTypeId(ShowRoomTypeEnum.Category);\n    if (foundCategories?.showRoomTypeId) {\n      const categories = this.appDataService.categories.records;\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Category,\n        data: categories,\n        image: foundCategories.image,\n        color: foundCategories.color,\n        feature: foundCategories.featureProduct,\n        order: foundCategories.order,\n        categoryId: foundCategories.categoryId,\n        fetchStatus: 'completed',\n        isDragged: false,\n        title: 'Categories'\n      });\n    }\n  }\n  signOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/login']);\n    sessionStorage.clear();\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    this.getShowRoom();\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n    }\n  }\n  getMainSliderData() {\n    this.homeService.getMainSliders({}).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        Object.keys(res.data.records).forEach(function (k) {\n          res.data.records[k].imageUrl = UtilityFunctions.verifyImageURL(res.data.records[k].imageUrl, environment.apiEndPoint);\n        });\n        this.mainSlider.push(...res.data.records);\n      },\n      error: err => {\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n  }\n  handleFeature(foundFeatureProduct) {\n    var _this2 = this;\n    foundFeatureProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Feature,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: p.featureProduct,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        title: FeatureType[p.featureProduct],\n        topNumber: p.topNumber\n      });\n    });\n    Promise.all(foundFeatureProduct.map(p => this.productService.GetAllProductsByFeature(p.featureProduct, p.topNumber, true, 1, 20, false, null, true))).then(response => {\n      Promise.all(response.map(respObj => {\n        return new Promise((resolve, reject) => {\n          respObj.subscribe(r => {\n            if (r.data.records) {\n              this.featuredData.forEach(x => {\n                if (x.feature && x.feature === r.data.feature) {\n                  x.data = r.data.records;\n                  x.fetchStatus = 'completed';\n                }\n              });\n              this.featuredData.sort((a, b) => a.order - b.order);\n              this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n            }\n            resolve(true);\n          }, err => {\n            reject(new Error('An error occurred'));\n          });\n        });\n      })).then( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (data) {\n          const foundBanner = _this2.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n          if (foundBanner?.showRoomTypeId) {\n            _this2.homeService.getBanners(foundBanner.bannerId).subscribe({\n              next: res => {\n                _this2.featuredData.push({\n                  type: FeaturedDataTypeEnum.Banner,\n                  data: res.data.imageUrl,\n                  image: foundBanner.imageURL,\n                  order: foundBanner.order\n                });\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              },\n              error: err => {\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              }\n            });\n          }\n          _this2.loaderService.hide();\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).then( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (data) {\n          _this2.loaderService.hide();\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    });\n  }\n  handleFeatureNolength() {\n    const foundBanner = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n    if (foundBanner?.showRoomTypeId) {\n      this.homeService.getBanners(foundBanner.bannerId).subscribe(res => {\n        this.featuredData.push({\n          type: FeaturedDataTypeEnum.Banner,\n          data: res.data,\n          image: foundBanner.imageURL,\n          order: foundBanner.order,\n          categoryId: foundBanner.categoryId\n        });\n        this.featuredData.sort((a, b) => a.order - b.order);\n        this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        this.loaderService.hide();\n      });\n    }\n  }\n  handleFoundProduct(foundCategoryProduct) {\n    foundCategoryProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.CategoryProduct,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: null,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        name: \"Category\"\n      });\n      this.productService.getCategoryProducts(p.categoryId, 15, true).subscribe({\n        next: r => {\n          if (r.data.productsList.records) {\n            this.featuredData.forEach(x => {\n              if (x.categoryId == p.categoryId) {\n                x.data = r.data.productsList.records;\n                x.fetchStatus = 'completed';\n                x.name = r.data.productsList.records[0].categoryName;\n              }\n            });\n            this.loaderService.hide();\n          }\n          this.featuredData.sort((a, b) => a.order - b.order);\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        },\n        error: () => {\n          this.loaderService.hide();\n        }\n      });\n    });\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.HomeService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i1.ReviewsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"home relative mobile-top\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"home\", \"relative\", \"mobile-top\"], [3, \"sliders\"], [1, \"content-container\", \"my-3X\", \"extra-pad\"], [4, \"ngFor\", \"ngForOf\"], [1, \"my-5\", \"ng-star-inserted\", \"mobile-display-category\"], [\"class\", \"category-slider-responsive\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"my-5\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"mb-3 mt-3\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"category-slider-responsive\", 3, \"ngClass\"], [3, \"title\"], [3, \"categories\"], [1, \"my-5\", 3, \"ngClass\"], [1, \"my-3\", 3, \"banner\"], [1, \"mb-3\", \"mt-3\", 3, \"ngClass\"], [3, \"categoryID\", \"featureProduct\", \"fetchStatus\", \"title\", \"topNumber\"], [3, \"products\"], [1, \"landing-template\", 3, \"ngClass\", \"ngStyle\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IndexComponent_section_0_Template, 5, 2, \"section\", 0);\n        i0.ɵɵtemplate(1, IndexComponent_ng_container_1_Template, 3, 10, \"ng-container\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgStyle, i7.ProductSliderComponent, i8.SectionComponent, i9.TemplateOneComponent, i10.MainSliderComponent, i11.BannerComponent, i12.CategorySliderComponent, i13.SectionCategoryComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.landing-template[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .landing-template[_ngcontent-%COMP%] {\\n    padding: 0 15px;\\n    margin-top: 230px;\\n  }\\n}\\n\\n.yellow-bg[_ngcontent-%COMP%] {\\n  background-color: #FFCB05;\\n  position: absolute;\\n  width: 100%;\\n  height: 260px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .yellow-bg[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding-left: 4rem !important;\\n  padding-right: 3rem !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding-left: 1.5rem !important;\\n    padding-right: 1.5rem !important;\\n  }\\n  .extra-pad[_ngcontent-%COMP%]   .my-5.mt-3[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-display-category[_ngcontent-%COMP%] {\\n    margin-top: 30px !important;\\n    margin-bottom: 0px !important;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 130px !important;\\n  }\\n  .navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 74px !important;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 140px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 180px !important;\\n  }\\n  .navbar-inactive-old-mobile[_ngcontent-%COMP%] {\\n    margin-top: 230px !important;\\n  }\\n  .navbar-inactive-mobile[_ngcontent-%COMP%] {\\n    margin-top: 85px !important;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 190px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "environment", "FeaturedDataTypeEnum", "FeatureType", "ShowRoomTypeEnum", "isPlatformBrowser", "UtilityFunctions", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "i_r4", "ɵɵadvance", "feature_r3", "title", "data", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "categoryId", "feature", "fetchStatus", "ɵɵtemplate", "IndexComponent_section_0_ng_container_4_div_2_Template", "IndexComponent_section_0_ng_container_4_div_3_Template", "IndexComponent_section_0_ng_container_4_div_4_Template", "type", "IndexComponent_section_0_ng_container_4_Template", "ctx_r0", "mainSlider", "featuredData", "IndexComponent_ng_container_1_ng_container_2_Template", "ɵɵpureFunction4", "_c1", "ctx_r1", "isMobile<PERSON>iew", "isMobileTemplate", "navbarData", "isActive", "_c2", "templateId", "IndexComponent", "onResize", "event", "updateZoomClass", "platformId", "zoomLevel", "window", "innerWidth", "screen", "availWidth", "zoomLevelClass", "constructor", "store", "mainDataService", "homeService", "productService", "messageService", "reviewsService", "translate", "router", "cookieService", "authTokenService", "loaderService", "permissionService", "appDataService", "cd", "sections", "categories", "enableFeaturedProducts", "enableNewProducts", "banner", "src", "showRoomConfigurationRes", "productOffers", "isLayoutTemplate", "width", "hasPermission", "ngOnInit", "_this", "_asyncToGenerator", "layoutTemplate", "find", "section", "allBanners", "setBannerData", "isBannerActive", "isNavbarDataActive", "getShowRoom", "detectChanges", "findShowRoomTypeId", "showRoomId", "element", "showRoomTypeId", "showRoomConfiguration", "records", "MainBanner", "getMainSliderData", "addFeatureData", "foundFeatureProduct", "filter", "featureProduct", "foundCategoryProduct", "length", "handleFeature", "handleFeatureNolength", "handleFoundProduct", "foundCategories", "Category", "push", "image", "color", "order", "isDragged", "signOut", "setStoreData", "authTokenSet", "delete", "set", "localStorage", "removeItem", "navigate", "sessionStorage", "clear", "setItem", "localStoreNames", "notifications", "unreadNotifications", "shipping", "payment", "promo", "steps", "profile", "orderId", "JSON", "stringify", "getMainSliders", "subscribe", "next", "res", "hide", "Object", "keys", "for<PERSON>ach", "k", "imageUrl", "verifyImageURL", "apiEndPoint", "error", "err", "complete", "_this2", "p", "Feature", "topNumber", "Promise", "all", "map", "GetAllProductsByFeature", "then", "response", "respObj", "resolve", "reject", "r", "x", "sort", "a", "b", "parse", "Error", "_ref", "foundBanner", "Banner", "getBanners", "bannerId", "imageURL", "_x", "apply", "arguments", "_ref2", "_x2", "CategoryProduct", "name", "getCategoryProducts", "productsList", "categoryName", "_", "ɵɵdirectiveInject", "i1", "StoreService", "MainDataService", "HomeService", "ProductService", "i2", "MessageService", "ReviewsService", "i3", "TranslateService", "i4", "Router", "i5", "CookieService", "AuthTokenService", "LoaderService", "PermissionService", "AppDataService", "ChangeDetectorRef", "_2", "selectors", "hostBindings", "IndexComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "IndexComponent_section_0_Template", "IndexComponent_ng_container_1_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\components\\index\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\components\\index\\index.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, HostListener, Inject, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { environment } from '@environments/environment';\r\n\r\nimport {\r\n  BannerResponse,\r\n  Offers,\r\n  Section,\r\n  FeaturedDataTypeEnum,\r\n  FeatureType,\r\n  ProductRate, CategoryRecords\r\n} from \"@core/interface\";\r\n\r\nimport {\r\n  StoreService,\r\n  MainDataService,\r\n  ProductService,\r\n  HomeService,\r\n  LoaderService,\r\n  AuthTokenService,\r\n  ReviewsService, AppDataService, PermissionService\r\n} from '@core/services';\r\nimport { ShowRoomTypeEnum } from \"@core/enums\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\n\r\n@Component({\r\n  selector: 'app-index',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexComponent implements OnInit {\r\n  sections: Array<Section> = [];\r\n  mainSlider: Array<any> = [];\r\n  categories: Array<CategoryRecords> = [];\r\n  enableFeaturedProducts: boolean = false;\r\n  enableNewProducts: boolean = false;\r\n  reviews: ProductRate[] | undefined;\r\n  banner: any = [\r\n    { src: 'assets/images/banner/banner1.jpg' },\r\n    { src: 'assets/images/banner/banner2.jpg' },\r\n    { src: 'assets/images/banner/banner3.jpg' },\r\n  ];\r\n\r\n\r\n  showRoomConfigurationRes: any = [];\r\n  productOffers: Offers[] = [];\r\n  featuredData: any = [];\r\n\r\n  templateId: number = 1;\r\n  zoomLevelClass: string = 'default-zoom';\r\n  navbarData: any;\r\n  allBanners: any;\r\n  isMobileTemplate:boolean=false;\r\n  isLayoutTemplate: boolean = false;\r\n  isMobileView: boolean = window.screen.width < 768;\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: Event) {\r\n    this.updateZoomClass();\r\n  }\r\n\r\n  private updateZoomClass() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const zoomLevel = (window.innerWidth / window.screen.availWidth) * 100;\r\n      if (zoomLevel <= 91) {\r\n        this.zoomLevelClass = 'zoom-110';\r\n      }\r\n      if (zoomLevel <= 112) {\r\n        this.zoomLevelClass = 'zoom-90';\r\n      } else if (zoomLevel <= 125) {\r\n        this.zoomLevelClass = 'zoom-80';\r\n      } else if (zoomLevel <= 134) {\r\n        this.zoomLevelClass = 'zoom-75';\r\n      } else if (zoomLevel <= 150) {\r\n        this.zoomLevelClass = 'zoom-67';\r\n      } else if (zoomLevel <= 200) {\r\n        this.zoomLevelClass = 'zoom-50';\r\n      } else if (zoomLevel <= 300) {\r\n        this.zoomLevelClass = 'zoom-33';\r\n      } else if (zoomLevel <= 400) {\r\n        this.zoomLevelClass = 'zoom-25';\r\n      } else {\r\n        this.zoomLevelClass = 'default-zoom';\r\n      }\r\n    }\r\n  }\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private mainDataService: MainDataService,\r\n    private homeService: HomeService,\r\n    private productService: ProductService,\r\n    private messageService: MessageService,\r\n    private reviewsService: ReviewsService,\r\n    private translate: TranslateService,\r\n    private router: Router,\r\n    private cookieService: CookieService,\r\n    private authTokenService: AuthTokenService,\r\n    private loaderService: LoaderService,\r\n    private permissionService: PermissionService,\r\n    private appDataService: AppDataService,\r\n    private cd: ChangeDetectorRef,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n  ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n  }\r\n\r\n  async ngOnInit() {\r\nif(this.appDataService.layoutTemplate){\r\n  this.navbarData = await this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n  this.allBanners = await this.appDataService.layoutTemplate.find((section: any) => section.type === 'main_banner');\r\n\r\n  this.mainDataService.setBannerData(\r\n    {\r\n      isBannerActive: this.allBanners?.isActive ?? false,\r\n      isNavbarDataActive: this.navbarData?.isActive ?? false\r\n    }\r\n  )\r\n}\r\n\r\n\r\n    if (!this.permissionService.hasPermission('Layout-Template')) {\r\n      this.getShowRoom()\r\n      this.isLayoutTemplate = false\r\n    } else {\r\n      this.isLayoutTemplate = true\r\n    }\r\n    this.cd.detectChanges();\r\n  }\r\n\r\n  findShowRoomTypeId(showRoomId: ShowRoomTypeEnum) {\r\n    return this.showRoomConfigurationRes.find(\r\n      (element: any) => element.showRoomTypeId == showRoomId\r\n    );\r\n  }\r\n\r\n  getShowRoom() {\r\n\r\n    this.showRoomConfigurationRes = this.appDataService.showRoomConfiguration?.records;\r\n\r\n    if (this.findShowRoomTypeId(ShowRoomTypeEnum.MainBanner)) {\r\n      this.getMainSliderData();\r\n    }\r\n\r\n    this.addFeatureData();\r\n\r\n    const foundFeatureProduct = this.showRoomConfigurationRes.filter(\r\n      (element: any) => element.showRoomTypeId == 2 && element.featureProduct\r\n    );\r\n    const foundCategoryProduct = this.showRoomConfigurationRes.filter((element: any) => element.showRoomTypeId == 2 && !element.featureProduct && element.categoryId);\r\n\r\n\r\n    if (foundFeatureProduct?.length) {\r\n    this.handleFeature(foundFeatureProduct)\r\n    } else {\r\n      this.handleFeatureNolength();\r\n    }\r\n\r\n    if (foundCategoryProduct.length) {\r\n      this.handleFoundProduct(foundCategoryProduct);\r\n\r\n    }\r\n  }\r\n\r\n  addFeatureData(){\r\n    const foundCategories = this.findShowRoomTypeId(ShowRoomTypeEnum.Category);\r\n    if (foundCategories?.showRoomTypeId) {\r\n      const categories = this.appDataService.categories.records\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.Category,\r\n        data: categories,\r\n        image: foundCategories.image,\r\n        color: foundCategories.color,\r\n        feature: foundCategories.featureProduct,\r\n        order: foundCategories.order,\r\n        categoryId: foundCategories.categoryId,\r\n        fetchStatus: 'completed',\r\n        isDragged: false,\r\n        title: 'Categories'\r\n      });\r\n    }\r\n  }\r\n  signOut(): void {\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet('');\r\n    this.cookieService.delete('authToken', '/');\r\n\r\n    this.store.set('cartProducts', '');\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    this.router.navigate(['/login']);\r\n\r\n    sessionStorage.clear();\r\n    this.store.set('profile', '');\r\n    this.store.set('cartProducts', '');\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    this.getShowRoom();\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0,\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null,\r\n      });\r\n    } else {\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n      localStorage.removeItem('refreshToken');\r\n      localStorage.removeItem('auth_enc');\r\n    }\r\n  }\r\n\r\n  getMainSliderData(): void {\r\n    this.homeService.getMainSliders({}).subscribe({\r\n      next: (res: any) => {\r\n        this.loaderService.hide();\r\n\r\n        Object.keys(res.data.records).forEach(function (k) {\r\n          res.data.records[k].imageUrl =  UtilityFunctions.verifyImageURL(res.data.records[k].imageUrl, environment.apiEndPoint);\r\n        });\r\n        this.mainSlider.push(...res.data.records);\r\n      },\r\n      error: (err: any) => {\r\n        this.loaderService.hide();\r\n      },\r\n      complete: () => {\r\n        this.loaderService.hide();\r\n\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleFeature(foundFeatureProduct: any) {\r\n    foundFeatureProduct.forEach((p: any) => {\r\n\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.Feature,\r\n        data: [],\r\n        image: p.image,\r\n        color: p.color,\r\n        feature: p.featureProduct,\r\n        order: p.order,\r\n        categoryId: p.categoryId,\r\n        fetchStatus: 'pending',\r\n        isDragged: false,\r\n        title: FeatureType[p.featureProduct],\r\n        topNumber: p.topNumber\r\n      });\r\n    });\r\n    Promise.all(\r\n      foundFeatureProduct.map((p: any) =>\r\n        this.productService.GetAllProductsByFeature(p.featureProduct, p.topNumber, true, 1, 20, false, null, true)\r\n      )\r\n    ).then((response: any) => {\r\n      Promise.all(response.map((respObj: any) => {\r\n        return new Promise((resolve, reject) => {\r\n          respObj.subscribe((r: any) => {\r\n            if (r.data.records) {\r\n              this.featuredData.forEach((x: any) => {\r\n                if (x.feature && x.feature === r.data.feature) {\r\n                  x.data = r.data.records;\r\n                  x.fetchStatus = 'completed';\r\n                }\r\n              });\r\n              this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n              this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n            }\r\n            resolve(true);\r\n          }, (err: any) => {\r\n            reject(new Error('An error occurred'));\r\n          });\r\n        });\r\n\r\n      }))\r\n        .then(async (data: any) => {\r\n          const foundBanner: any = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\r\n          if (foundBanner?.showRoomTypeId) {\r\n            this.homeService\r\n              .getBanners(foundBanner.bannerId)\r\n              .subscribe({\r\n                next: (res: any) => {\r\n                  this.featuredData.push({\r\n                    type: FeaturedDataTypeEnum.Banner,\r\n                    data: res.data.imageUrl,\r\n                    image: foundBanner.imageURL,\r\n                    order: foundBanner.order,\r\n                  });\r\n                  this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n                  this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n                },\r\n                error: (err: any) => {\r\n                  this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n                  this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n                }\r\n              });\r\n          }\r\n          this.loaderService.hide();\r\n        })\r\n        .then(async (data: any) => {\r\n\r\n          this.loaderService.hide();\r\n        });\r\n    });\r\n\r\n  }\r\n\r\n  private handleFeatureNolength() {\r\n    const foundBanner: any = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\r\n    if (foundBanner?.showRoomTypeId) {\r\n      this.homeService.getBanners(foundBanner.bannerId)\r\n        .subscribe((res: BannerResponse) => {\r\n          this.featuredData.push({\r\n            type: FeaturedDataTypeEnum.Banner,\r\n            data: res.data,\r\n            image: foundBanner.imageURL,\r\n            order: foundBanner.order,\r\n            categoryId: foundBanner.categoryId\r\n          });\r\n          this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n          this.loaderService.hide();\r\n        });\r\n    }\r\n  }\r\n\r\n  private handleFoundProduct(foundCategoryProduct: any) {\r\n    foundCategoryProduct.forEach((p: any) => {\r\n\r\n      this.featuredData.push({\r\n        type: FeaturedDataTypeEnum.CategoryProduct,\r\n        data: [],\r\n        image: p.image,\r\n        color: p.color,\r\n        feature: null,\r\n        order: p.order,\r\n        categoryId: p.categoryId,\r\n        fetchStatus: 'pending',\r\n        isDragged: false,\r\n        name: \"Category\"\r\n      });\r\n      this.productService.getCategoryProducts(p.categoryId, 15, true).subscribe({\r\n        next: (r: any) => {\r\n          if (r.data.productsList.records) {\r\n            this.featuredData.forEach((x: any) => {\r\n              if (x.categoryId == p.categoryId) {\r\n                x.data = r.data.productsList.records;\r\n                x.fetchStatus = 'completed';\r\n                x.name = r.data.productsList.records[0].categoryName;\r\n              }\r\n\r\n            });\r\n            this.loaderService.hide();\r\n          }\r\n          this.featuredData.sort((a: any, b: any) => a.order - b.order);\r\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\r\n        },\r\n        error: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n    });\r\n  }\r\n}\r\n", "<section *ngIf=\"!isLayoutTemplate\" class=\"home relative mobile-top\">\r\n  <ng-container>\r\n    <app-main-slider [sliders]=\"mainSlider\"></app-main-slider>\r\n    <div class=\"content-container my-3X extra-pad\">\r\n      <ng-container *ngFor=\"let feature of featuredData; let i = index\">\r\n        <div class=\"my-5 ng-star-inserted mobile-display-category\"></div>\r\n        <div *ngIf=\"feature.type === 'category'\" [ngClass]=\"{ 'mt-3': i === 0 }\" class=\"category-slider-responsive\">\r\n          <app-section-category [title]=\"feature.title\">\r\n            <app-category-slider [categories]=\"feature.data\"></app-category-slider>\r\n          </app-section-category>\r\n        </div>\r\n\r\n        <div *ngIf=\"feature.type === 'banner'\" [ngClass]=\"{ 'mt-3': i === 0 }\" class=\"my-5\">\r\n          <ng-container>\r\n            <app-banner [banner]=\"feature.data\" class=\"my-3\"></app-banner>\r\n          </ng-container>\r\n        </div>\r\n\r\n        <div *ngIf=\"feature.type === 'feature'\" [ngClass]=\"{ 'mt-3': i === 0 }\" class=\"mb-3 mt-3\">\r\n          <!--          {{printFeature(feature)}}-->\r\n          <app-mtn-section [categoryID]=\"feature.categoryId\" [featureProduct]=\"feature.feature\" [fetchStatus]=\"feature.fetchStatus\"\r\n                           [title]=\"feature.title\" [topNumber]=\"100\">\r\n            <app-mtn-product-slider [products]=\"feature.data\"></app-mtn-product-slider>\r\n          </app-mtn-section>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </ng-container>\r\n</section>\r\n\r\n<ng-container *ngIf=\"isLayoutTemplate\">\r\n  <section [ngClass]=\"{\r\n    'navbar-inactive-old-mobile': isMobileView && !isMobileTemplate,\r\n    'navbar-active': navbarData?.isActive && !isMobileTemplate && isMobileView,\r\n    'navbar-inactive': !navbarData?.isActive && !isMobileTemplate,\r\n    'navbar-inactive-mobile': !navbarData?.isActive && isMobileTemplate\r\n    }\"\r\n           class=\"landing-template\" [ngStyle]=\"{\r\n           marginTop: isMobileTemplate ? (isMobileView ? '80px' : '15px') : (isMobileView ? '230px' : '50px')\r\n           }\">\r\n    <ng-container *ngIf=\"templateId === 1\">\r\n      <app-landing-template-one></app-landing-template-one>\r\n    </ng-container>\r\n  </section>\r\n</ng-container>\r\n"], "mappings": ";AAAA,SAAoEA,WAAW,QAAO,eAAe;AAMrG,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,SAIEC,oBAAoB,EACpBC,WAAW,QAEN,iBAAiB;AAWxB,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,OAAOC,gBAAgB,MAAM,2BAA2B;;;;;;;;;;;;;;;;;;;;;;ICtBhDC,EAAA,CAAAC,cAAA,cAA4G;IAExGD,EAAA,CAAAE,SAAA,8BAAuE;IACzEF,EAAA,CAAAG,YAAA,EAAuB;;;;;;IAHgBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,IAAA,QAA+B;IAChDP,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAI,UAAA,UAAAK,UAAA,CAAAC,KAAA,CAAuB;IACtBV,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAI,UAAA,eAAAK,UAAA,CAAAE,IAAA,CAA2B;;;;;IAIpDX,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAY,uBAAA,GAAc;IACZZ,EAAA,CAAAE,SAAA,qBAA8D;IAChEF,EAAA,CAAAa,qBAAA,EAAe;IACjBb,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJiCH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,IAAA,QAA+B;IAEtDP,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAI,UAAA,WAAAK,UAAA,CAAAE,IAAA,CAAuB;;;;;IAIvCX,EAAA,CAAAC,cAAA,cAA0F;IAItFD,EAAA,CAAAE,SAAA,iCAA2E;IAC7EF,EAAA,CAAAG,YAAA,EAAkB;;;;;;IALoBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,IAAA,QAA+B;IAEpDP,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAI,UAAA,eAAAK,UAAA,CAAAK,UAAA,CAAiC,mBAAAL,UAAA,CAAAM,OAAA,iBAAAN,UAAA,CAAAO,WAAA,WAAAP,UAAA,CAAAC,KAAA;IAExBV,EAAA,CAAAQ,SAAA,GAAyB;IAAzBR,EAAA,CAAAI,UAAA,aAAAK,UAAA,CAAAE,IAAA,CAAyB;;;;;IAlBvDX,EAAA,CAAAY,uBAAA,GAAkE;IAChEZ,EAAA,CAAAE,SAAA,aAAiE;IACjEF,EAAA,CAAAiB,UAAA,IAAAC,sDAAA,iBAIM;IAENlB,EAAA,CAAAiB,UAAA,IAAAE,sDAAA,iBAIM;IAENnB,EAAA,CAAAiB,UAAA,IAAAG,sDAAA,iBAMM;IACRpB,EAAA,CAAAa,qBAAA,EAAe;;;;IAnBPb,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAI,UAAA,SAAAK,UAAA,CAAAY,IAAA,gBAAiC;IAMjCrB,EAAA,CAAAQ,SAAA,GAA+B;IAA/BR,EAAA,CAAAI,UAAA,SAAAK,UAAA,CAAAY,IAAA,cAA+B;IAM/BrB,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAI,UAAA,SAAAK,UAAA,CAAAY,IAAA,eAAgC;;;;;IAlB9CrB,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAY,uBAAA,GAAc;IACZZ,EAAA,CAAAE,SAAA,yBAA0D;IAC1DF,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAiB,UAAA,IAAAK,gDAAA,0BAqBe;IACjBtB,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAa,qBAAA,EAAe;IACjBb,EAAA,CAAAG,YAAA,EAAU;;;;IA1BWH,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAC,UAAA,CAAsB;IAEHxB,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAE,YAAA,CAAiB;;;;;IAoCrDzB,EAAA,CAAAY,uBAAA,GAAuC;IACrCZ,EAAA,CAAAE,SAAA,+BAAqD;IACvDF,EAAA,CAAAa,qBAAA,EAAe;;;;;;;;;;;;;;;;;;IAZnBb,EAAA,CAAAY,uBAAA,GAAuC;IACrCZ,EAAA,CAAAC,cAAA,kBAQY;IACVD,EAAA,CAAAiB,UAAA,IAAAS,qDAAA,0BAEe;IACjB1B,EAAA,CAAAG,YAAA,EAAU;IACZH,EAAA,CAAAa,qBAAA,EAAe;;;;IAbJb,EAAA,CAAAQ,SAAA,GAKL;IALKR,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA2B,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,YAAA,KAAAD,MAAA,CAAAE,gBAAA,GAAAF,MAAA,CAAAG,UAAA,kBAAAH,MAAA,CAAAG,UAAA,CAAAC,QAAA,MAAAJ,MAAA,CAAAE,gBAAA,IAAAF,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAG,UAAA,kBAAAH,MAAA,CAAAG,UAAA,CAAAC,QAAA,MAAAJ,MAAA,CAAAE,gBAAA,IAAAF,MAAA,CAAAG,UAAA,kBAAAH,MAAA,CAAAG,UAAA,CAAAC,QAAA,KAAAJ,MAAA,CAAAE,gBAAA,EAKL,YAAA/B,EAAA,CAAAK,eAAA,IAAA6B,GAAA,EAAAL,MAAA,CAAAE,gBAAA,GAAAF,MAAA,CAAAC,YAAA,qBAAAD,MAAA,CAAAC,YAAA;IAIa9B,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAI,UAAA,SAAAyB,MAAA,CAAAM,UAAA,OAAsB;;;ADLzC,OAAM,MAAOC,cAAc;EA2BzBC,QAAQA,CAACC,KAAY;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,IAAIzC,iBAAiB,CAAC,IAAI,CAAC0C,UAAU,CAAC,EAAE;MACtC,MAAMC,SAAS,GAAIC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,MAAM,CAACC,UAAU,GAAI,GAAG;MACtE,IAAIJ,SAAS,IAAI,EAAE,EAAE;QACnB,IAAI,CAACK,cAAc,GAAG,UAAU;;MAElC,IAAIL,SAAS,IAAI,GAAG,EAAE;QACpB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,cAAc;;;EAG1C;EAEAC,YACUC,KAAmB,EACnBC,eAAgC,EAChCC,WAAwB,EACxBC,cAA8B,EAC9BC,cAA8B,EAC9BC,cAA8B,EAC9BC,SAA2B,EAC3BC,MAAc,EACdC,aAA4B,EAC5BC,gBAAkC,EAClCC,aAA4B,EAC5BC,iBAAoC,EACpCC,cAA8B,EAC9BC,EAAqB,EACArB,UAAe;IAdpC,KAAAQ,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACmB,KAAArB,UAAU,GAAVA,UAAU;IAvEzC,KAAAsB,QAAQ,GAAmB,EAAE;IAC7B,KAAAtC,UAAU,GAAe,EAAE;IAC3B,KAAAuC,UAAU,GAA2B,EAAE;IACvC,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,iBAAiB,GAAY,KAAK;IAElC,KAAAC,MAAM,GAAQ,CACZ;MAAEC,GAAG,EAAE;IAAkC,CAAE,EAC3C;MAAEA,GAAG,EAAE;IAAkC,CAAE,EAC3C;MAAEA,GAAG,EAAE;IAAkC,CAAE,CAC5C;IAGD,KAAAC,wBAAwB,GAAQ,EAAE;IAClC,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAA5C,YAAY,GAAQ,EAAE;IAEtB,KAAAU,UAAU,GAAW,CAAC;IACtB,KAAAW,cAAc,GAAW,cAAc;IAGvC,KAAAf,gBAAgB,GAAS,KAAK;IAC9B,KAAAuC,gBAAgB,GAAY,KAAK;IACjC,KAAAxC,YAAY,GAAYY,MAAM,CAACE,MAAM,CAAC2B,KAAK,GAAG,GAAG;IAkD/C,IAAI,CAACxC,gBAAgB,GAAG,IAAI,CAAC4B,iBAAiB,CAACa,aAAa,CAAC,eAAe,CAAC;EAC/E;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAGD,KAAI,CAACd,cAAc,CAACgB,cAAc,EAAC;QACpCF,KAAI,CAAC1C,UAAU,SAAS0C,KAAI,CAACd,cAAc,CAACgB,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACzD,IAAI,KAAK,QAAQ,CAAC;QAC5GqD,KAAI,CAACK,UAAU,SAASL,KAAI,CAACd,cAAc,CAACgB,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACzD,IAAI,KAAK,aAAa,CAAC;QAEjHqD,KAAI,CAACzB,eAAe,CAAC+B,aAAa,CAChC;UACEC,cAAc,EAAEP,KAAI,CAACK,UAAU,EAAE9C,QAAQ,IAAI,KAAK;UAClDiD,kBAAkB,EAAER,KAAI,CAAC1C,UAAU,EAAEC,QAAQ,IAAI;SAClD,CACF;;MAIC,IAAI,CAACyC,KAAI,CAACf,iBAAiB,CAACa,aAAa,CAAC,iBAAiB,CAAC,EAAE;QAC5DE,KAAI,CAACS,WAAW,EAAE;QAClBT,KAAI,CAACJ,gBAAgB,GAAG,KAAK;OAC9B,MAAM;QACLI,KAAI,CAACJ,gBAAgB,GAAG,IAAI;;MAE9BI,KAAI,CAACb,EAAE,CAACuB,aAAa,EAAE;IAAC;EAC1B;EAEAC,kBAAkBA,CAACC,UAA4B;IAC7C,OAAO,IAAI,CAAClB,wBAAwB,CAACS,IAAI,CACtCU,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAIF,UAAU,CACvD;EACH;EAEAH,WAAWA,CAAA;IAET,IAAI,CAACf,wBAAwB,GAAG,IAAI,CAACR,cAAc,CAAC6B,qBAAqB,EAAEC,OAAO;IAElF,IAAI,IAAI,CAACL,kBAAkB,CAACxF,gBAAgB,CAAC8F,UAAU,CAAC,EAAE;MACxD,IAAI,CAACC,iBAAiB,EAAE;;IAG1B,IAAI,CAACC,cAAc,EAAE;IAErB,MAAMC,mBAAmB,GAAG,IAAI,CAAC1B,wBAAwB,CAAC2B,MAAM,CAC7DR,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAI,CAAC,IAAID,OAAO,CAACS,cAAc,CACxE;IACD,MAAMC,oBAAoB,GAAG,IAAI,CAAC7B,wBAAwB,CAAC2B,MAAM,CAAER,OAAY,IAAKA,OAAO,CAACC,cAAc,IAAI,CAAC,IAAI,CAACD,OAAO,CAACS,cAAc,IAAIT,OAAO,CAACzE,UAAU,CAAC;IAGjK,IAAIgF,mBAAmB,EAAEI,MAAM,EAAE;MACjC,IAAI,CAACC,aAAa,CAACL,mBAAmB,CAAC;KACtC,MAAM;MACL,IAAI,CAACM,qBAAqB,EAAE;;IAG9B,IAAIH,oBAAoB,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACG,kBAAkB,CAACJ,oBAAoB,CAAC;;EAGjD;EAEAJ,cAAcA,CAAA;IACZ,MAAMS,eAAe,GAAG,IAAI,CAACjB,kBAAkB,CAACxF,gBAAgB,CAAC0G,QAAQ,CAAC;IAC1E,IAAID,eAAe,EAAEd,cAAc,EAAE;MACnC,MAAMzB,UAAU,GAAG,IAAI,CAACH,cAAc,CAACG,UAAU,CAAC2B,OAAO;MACzD,IAAI,CAACjE,YAAY,CAAC+E,IAAI,CAAC;QACrBnF,IAAI,EAAE1B,oBAAoB,CAAC4G,QAAQ;QACnC5F,IAAI,EAAEoD,UAAU;QAChB0C,KAAK,EAAEH,eAAe,CAACG,KAAK;QAC5BC,KAAK,EAAEJ,eAAe,CAACI,KAAK;QAC5B3F,OAAO,EAAEuF,eAAe,CAACN,cAAc;QACvCW,KAAK,EAAEL,eAAe,CAACK,KAAK;QAC5B7F,UAAU,EAAEwF,eAAe,CAACxF,UAAU;QACtCE,WAAW,EAAE,WAAW;QACxB4F,SAAS,EAAE,KAAK;QAChBlG,KAAK,EAAE;OACR,CAAC;;EAEN;EACAmG,OAAOA,CAAA;IACL,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACrD,gBAAgB,CAACsD,YAAY,CAAC,EAAE,CAAC;IACtC,IAAI,CAACvD,aAAa,CAACwD,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAE3C,IAAI,CAAChE,KAAK,CAACiE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC5D,MAAM,CAAC6D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEhCC,cAAc,CAACC,KAAK,EAAE;IACtB,IAAI,CAACtE,KAAK,CAACiE,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCC,YAAY,CAACK,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCL,YAAY,CAACK,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzC,IAAI,CAACpC,WAAW,EAAE;EACpB;EAEA2B,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC9D,KAAK,CAACwE,eAAe,CAACtB,MAAM,EAAE;MACrC,IAAI,CAAClD,KAAK,CAACiE,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAACjE,KAAK,CAACiE,GAAG,CAAC,eAAe,EAAE;QAC9BQ,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAAC1E,KAAK,CAACiE,GAAG,CAAC,cAAc,EAAE;QAC7BU,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACLd,YAAY,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCL,YAAY,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCL,YAAY,CAACK,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCL,YAAY,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCL,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MACxDhB,YAAY,CAACK,OAAO,CAAC,oBAAoB,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9DhB,YAAY,CAACK,OAAO,CAAC,iBAAiB,EAAEU,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3DhB,YAAY,CAACK,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MACtCL,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;MACvCD,YAAY,CAACC,UAAU,CAAC,UAAU,CAAC;;EAEvC;EAEAvB,iBAAiBA,CAAA;IACf,IAAI,CAAC1C,WAAW,CAACiF,cAAc,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC5E,aAAa,CAAC6E,IAAI,EAAE;QAEzBC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC3H,IAAI,CAAC+E,OAAO,CAAC,CAACgD,OAAO,CAAC,UAAUC,CAAC;UAC/CL,GAAG,CAAC3H,IAAI,CAAC+E,OAAO,CAACiD,CAAC,CAAC,CAACC,QAAQ,GAAI7I,gBAAgB,CAAC8I,cAAc,CAACP,GAAG,CAAC3H,IAAI,CAAC+E,OAAO,CAACiD,CAAC,CAAC,CAACC,QAAQ,EAAElJ,WAAW,CAACoJ,WAAW,CAAC;QACxH,CAAC,CAAC;QACF,IAAI,CAACtH,UAAU,CAACgF,IAAI,CAAC,GAAG8B,GAAG,CAAC3H,IAAI,CAAC+E,OAAO,CAAC;MAC3C,CAAC;MACDqD,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACtF,aAAa,CAAC6E,IAAI,EAAE;MAC3B,CAAC;MACDU,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACvF,aAAa,CAAC6E,IAAI,EAAE;MAE3B;KACD,CAAC;EACJ;EAEQpC,aAAaA,CAACL,mBAAwB;IAAA,IAAAoD,MAAA;IAC5CpD,mBAAmB,CAAC4C,OAAO,CAAES,CAAM,IAAI;MAErC,IAAI,CAAC1H,YAAY,CAAC+E,IAAI,CAAC;QACrBnF,IAAI,EAAE1B,oBAAoB,CAACyJ,OAAO;QAClCzI,IAAI,EAAE,EAAE;QACR8F,KAAK,EAAE0C,CAAC,CAAC1C,KAAK;QACdC,KAAK,EAAEyC,CAAC,CAACzC,KAAK;QACd3F,OAAO,EAAEoI,CAAC,CAACnD,cAAc;QACzBW,KAAK,EAAEwC,CAAC,CAACxC,KAAK;QACd7F,UAAU,EAAEqI,CAAC,CAACrI,UAAU;QACxBE,WAAW,EAAE,SAAS;QACtB4F,SAAS,EAAE,KAAK;QAChBlG,KAAK,EAAEd,WAAW,CAACuJ,CAAC,CAACnD,cAAc,CAAC;QACpCqD,SAAS,EAAEF,CAAC,CAACE;OACd,CAAC;IACJ,CAAC,CAAC;IACFC,OAAO,CAACC,GAAG,CACTzD,mBAAmB,CAAC0D,GAAG,CAAEL,CAAM,IAC7B,IAAI,CAAChG,cAAc,CAACsG,uBAAuB,CAACN,CAAC,CAACnD,cAAc,EAAEmD,CAAC,CAACE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAC3G,CACF,CAACK,IAAI,CAAEC,QAAa,IAAI;MACvBL,OAAO,CAACC,GAAG,CAACI,QAAQ,CAACH,GAAG,CAAEI,OAAY,IAAI;QACxC,OAAO,IAAIN,OAAO,CAAC,CAACO,OAAO,EAAEC,MAAM,KAAI;UACrCF,OAAO,CAACxB,SAAS,CAAE2B,CAAM,IAAI;YAC3B,IAAIA,CAAC,CAACpJ,IAAI,CAAC+E,OAAO,EAAE;cAClB,IAAI,CAACjE,YAAY,CAACiH,OAAO,CAAEsB,CAAM,IAAI;gBACnC,IAAIA,CAAC,CAACjJ,OAAO,IAAIiJ,CAAC,CAACjJ,OAAO,KAAKgJ,CAAC,CAACpJ,IAAI,CAACI,OAAO,EAAE;kBAC7CiJ,CAAC,CAACrJ,IAAI,GAAGoJ,CAAC,CAACpJ,IAAI,CAAC+E,OAAO;kBACvBsE,CAAC,CAAChJ,WAAW,GAAG,WAAW;;cAE/B,CAAC,CAAC;cACF,IAAI,CAACS,YAAY,CAACwI,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACvD,KAAK,GAAGwD,CAAC,CAACxD,KAAK,CAAC;cAC7D,IAAI,CAAClF,YAAY,GAAGwG,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzG,YAAY,CAAC,CAAC;;YAEnEoI,OAAO,CAAC,IAAI,CAAC;UACf,CAAC,EAAGb,GAAQ,IAAI;YACdc,MAAM,CAAC,IAAIO,KAAK,CAAC,mBAAmB,CAAC,CAAC;UACxC,CAAC,CAAC;QACJ,CAAC,CAAC;MAEJ,CAAC,CAAC,CAAC,CACAX,IAAI;QAAA,IAAAY,IAAA,GAAA3F,iBAAA,CAAC,WAAOhE,IAAS,EAAI;UACxB,MAAM4J,WAAW,GAAQrB,MAAI,CAAC7D,kBAAkB,CAACxF,gBAAgB,CAAC2K,MAAM,CAAC;UACzE,IAAID,WAAW,EAAE/E,cAAc,EAAE;YAC/B0D,MAAI,CAAChG,WAAW,CACbuH,UAAU,CAACF,WAAW,CAACG,QAAQ,CAAC,CAChCtC,SAAS,CAAC;cACTC,IAAI,EAAGC,GAAQ,IAAI;gBACjBY,MAAI,CAACzH,YAAY,CAAC+E,IAAI,CAAC;kBACrBnF,IAAI,EAAE1B,oBAAoB,CAAC6K,MAAM;kBACjC7J,IAAI,EAAE2H,GAAG,CAAC3H,IAAI,CAACiI,QAAQ;kBACvBnC,KAAK,EAAE8D,WAAW,CAACI,QAAQ;kBAC3BhE,KAAK,EAAE4D,WAAW,CAAC5D;iBACpB,CAAC;gBACFuC,MAAI,CAACzH,YAAY,CAACwI,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACvD,KAAK,GAAGwD,CAAC,CAACxD,KAAK,CAAC;gBAC7DuC,MAAI,CAACzH,YAAY,GAAGwG,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAACgB,MAAI,CAACzH,YAAY,CAAC,CAAC;cACnE,CAAC;cACDsH,KAAK,EAAGC,GAAQ,IAAI;gBAClBE,MAAI,CAACzH,YAAY,CAACwI,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACvD,KAAK,GAAGwD,CAAC,CAACxD,KAAK,CAAC;gBAC7DuC,MAAI,CAACzH,YAAY,GAAGwG,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAACgB,MAAI,CAACzH,YAAY,CAAC,CAAC;cACnE;aACD,CAAC;;UAENyH,MAAI,CAACxF,aAAa,CAAC6E,IAAI,EAAE;QAC3B,CAAC;QAAA,iBAAAqC,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC,CACDpB,IAAI;QAAA,IAAAqB,KAAA,GAAApG,iBAAA,CAAC,WAAOhE,IAAS,EAAI;UAExBuI,MAAI,CAACxF,aAAa,CAAC6E,IAAI,EAAE;QAC3B,CAAC;QAAA,iBAAAyC,GAAA;UAAA,OAAAD,KAAA,CAAAF,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IACN,CAAC,CAAC;EAEJ;EAEQ1E,qBAAqBA,CAAA;IAC3B,MAAMmE,WAAW,GAAQ,IAAI,CAAClF,kBAAkB,CAACxF,gBAAgB,CAAC2K,MAAM,CAAC;IACzE,IAAID,WAAW,EAAE/E,cAAc,EAAE;MAC/B,IAAI,CAACtC,WAAW,CAACuH,UAAU,CAACF,WAAW,CAACG,QAAQ,CAAC,CAC9CtC,SAAS,CAAEE,GAAmB,IAAI;QACjC,IAAI,CAAC7G,YAAY,CAAC+E,IAAI,CAAC;UACrBnF,IAAI,EAAE1B,oBAAoB,CAAC6K,MAAM;UACjC7J,IAAI,EAAE2H,GAAG,CAAC3H,IAAI;UACd8F,KAAK,EAAE8D,WAAW,CAACI,QAAQ;UAC3BhE,KAAK,EAAE4D,WAAW,CAAC5D,KAAK;UACxB7F,UAAU,EAAEyJ,WAAW,CAACzJ;SACzB,CAAC;QACF,IAAI,CAACW,YAAY,CAACwI,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACvD,KAAK,GAAGwD,CAAC,CAACxD,KAAK,CAAC;QAC7D,IAAI,CAAClF,YAAY,GAAGwG,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzG,YAAY,CAAC,CAAC;QACjE,IAAI,CAACiC,aAAa,CAAC6E,IAAI,EAAE;MAC3B,CAAC,CAAC;;EAER;EAEQlC,kBAAkBA,CAACJ,oBAAyB;IAClDA,oBAAoB,CAACyC,OAAO,CAAES,CAAM,IAAI;MAEtC,IAAI,CAAC1H,YAAY,CAAC+E,IAAI,CAAC;QACrBnF,IAAI,EAAE1B,oBAAoB,CAACsL,eAAe;QAC1CtK,IAAI,EAAE,EAAE;QACR8F,KAAK,EAAE0C,CAAC,CAAC1C,KAAK;QACdC,KAAK,EAAEyC,CAAC,CAACzC,KAAK;QACd3F,OAAO,EAAE,IAAI;QACb4F,KAAK,EAAEwC,CAAC,CAACxC,KAAK;QACd7F,UAAU,EAAEqI,CAAC,CAACrI,UAAU;QACxBE,WAAW,EAAE,SAAS;QACtB4F,SAAS,EAAE,KAAK;QAChBsE,IAAI,EAAE;OACP,CAAC;MACF,IAAI,CAAC/H,cAAc,CAACgI,mBAAmB,CAAChC,CAAC,CAACrI,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,CAACsH,SAAS,CAAC;QACxEC,IAAI,EAAG0B,CAAM,IAAI;UACf,IAAIA,CAAC,CAACpJ,IAAI,CAACyK,YAAY,CAAC1F,OAAO,EAAE;YAC/B,IAAI,CAACjE,YAAY,CAACiH,OAAO,CAAEsB,CAAM,IAAI;cACnC,IAAIA,CAAC,CAAClJ,UAAU,IAAIqI,CAAC,CAACrI,UAAU,EAAE;gBAChCkJ,CAAC,CAACrJ,IAAI,GAAGoJ,CAAC,CAACpJ,IAAI,CAACyK,YAAY,CAAC1F,OAAO;gBACpCsE,CAAC,CAAChJ,WAAW,GAAG,WAAW;gBAC3BgJ,CAAC,CAACkB,IAAI,GAAGnB,CAAC,CAACpJ,IAAI,CAACyK,YAAY,CAAC1F,OAAO,CAAC,CAAC,CAAC,CAAC2F,YAAY;;YAGxD,CAAC,CAAC;YACF,IAAI,CAAC3H,aAAa,CAAC6E,IAAI,EAAE;;UAE3B,IAAI,CAAC9G,YAAY,CAACwI,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACvD,KAAK,GAAGwD,CAAC,CAACxD,KAAK,CAAC;UAC7D,IAAI,CAAClF,YAAY,GAAGwG,IAAI,CAACmC,KAAK,CAACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzG,YAAY,CAAC,CAAC;QACnE,CAAC;QACDsH,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACrF,aAAa,CAAC6E,IAAI,EAAE;QAC3B;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC,QAAA+C,CAAA,G;qBAlWUlJ,cAAc,EAAApC,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAE,eAAA,GAAA1L,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAG,WAAA,GAAA3L,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAA5L,EAAA,CAAAuL,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAO,cAAA,GAAA/L,EAAA,CAAAuL,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAjM,EAAA,CAAAuL,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAnM,EAAA,CAAAuL,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAArM,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAc,gBAAA,GAAAtM,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAe,aAAA,GAAAvM,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAgB,iBAAA,GAAAxM,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAiB,cAAA,GAAAzM,EAAA,CAAAuL,iBAAA,CAAAvL,EAAA,CAAA0M,iBAAA,GAAA1M,EAAA,CAAAuL,iBAAA,CAwEf9L,WAAW;EAAA;EAAA,QAAAkN,EAAA,G;UAxEVvK,cAAc;IAAAwK,SAAA;IAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAdC,GAAA,CAAA3K,QAAA,CAAA4K,MAAA,CAAgB;QAAA,UAAAjN,EAAA,CAAAkN,eAAA;;;;;;;;QCnC7BlN,EAAA,CAAAiB,UAAA,IAAAkM,iCAAA,qBA4BU;QAEVnN,EAAA,CAAAiB,UAAA,IAAAmM,sCAAA,2BAce;;;QA5CLpN,EAAA,CAAAI,UAAA,UAAA4M,GAAA,CAAA1I,gBAAA,CAAuB;QA8BlBtE,EAAA,CAAAQ,SAAA,GAAsB;QAAtBR,EAAA,CAAAI,UAAA,SAAA4M,GAAA,CAAA1I,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}