{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { CustomPageService } from './custom-page.service';\nimport { LoaderService } from '@core/services';\nimport { GTMService } from '@core/services/gtm.service';\nimport { Meta } from \"@angular/platform-browser\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../../shared/components/product-card/product-card.component\";\nimport * as i3 from \"@ngx-translate/core\";\nfunction CustomPageComponent_ng_container_1_section_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function CustomPageComponent_ng_container_1_section_1_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const eachSection_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.redirectFeaturedProducts(eachSection_r1.sectionName, eachSection_r1.sectionId));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"landing.seeMore\"), \" \");\n  }\n}\nfunction CustomPageComponent_ng_container_1_section_1_app_mtn_product_card_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mtn-product-card\", 9);\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"product\", product_r9);\n  }\n}\nfunction CustomPageComponent_ng_container_1_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 3)(1, \"div\", 4)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomPageComponent_ng_container_1_section_1_div_4_Template, 3, 3, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵtemplate(6, CustomPageComponent_ng_container_1_section_1_app_mtn_product_card_6_Template, 1, 1, \"app-mtn-product-card\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const eachSection_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(eachSection_r1.sectionName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r1.productsLimit && eachSection_r1.productsLimit < eachSection_r1.productsTotal);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getLimitedProducts(eachSection_r1));\n  }\n}\nfunction CustomPageComponent_ng_container_1_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 10)(1, \"a\", 11);\n    i0.ɵɵelement(2, \"img\", 12)(3, \"img\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const eachSection_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r3.formatURL(eachSection_r1.redirectionURL), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", eachSection_r1.desktopBanner, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", eachSection_r1.mobileBanner, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomPageComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomPageComponent_ng_container_1_section_1_Template, 7, 3, \"section\", 1);\n    i0.ɵɵtemplate(2, CustomPageComponent_ng_container_1_section_2_Template, 4, 3, \"section\", 2);\n    i0.ɵɵelement(3, \"br\")(4, \"br\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachSection_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r1.products.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r1.redirectionURL);\n  }\n}\nexport class CustomPageComponent {\n  constructor() {\n    this.router = inject(Router);\n    this.customPageService = inject(CustomPageService);\n    this.loaderService = inject(LoaderService);\n    this.$gtmService = inject(GTMService);\n    this.meta = inject(Meta);\n    this.sectionsData = [];\n    this.mobileBackground = '';\n    this.desktopBackground = '';\n  }\n  ngOnInit() {\n    this.fetchCustomPageData();\n  }\n  fetchCustomPageData() {\n    this.loaderService.show();\n    this.customPageService.getCustomPageDetails(this.router.url).subscribe(res => {\n      const {\n        success,\n        data\n      } = res;\n      if (!success || data.status === \"Draft\" || data.hidePage) {\n        this.loaderService.hide();\n        this.router.navigateByUrl(\"page-not-found\");\n      } else {\n        this.desktopBackground = data.desktopBackground;\n        this.mobileBackground = data.mobileBackground;\n        this.customPageService.setBackgroundStyle(this.backgroundStyle);\n        this.customPageService.onChangePageTitle(data.title);\n        this.meta.updateTag({\n          name: 'description',\n          content: data.metaDescription\n        });\n        this.sectionsData = data.sections.map(({\n          sectionName,\n          sectionId,\n          products,\n          desktopBanner,\n          mobileBanner,\n          redirectionURL,\n          productsLimit,\n          productsTotal\n        }) => ({\n          sectionName,\n          sectionId,\n          desktopBanner,\n          mobileBanner,\n          redirectionURL,\n          productsLimit,\n          productsTotal,\n          products: products.map(({\n            productDetails\n          }) => ({\n            ...this.handleProductFromFetchedData(productDetails),\n            sectionName\n          }))\n        }));\n        this.loaderService.hide();\n      }\n      this.$gtmService.pushPageView('Custom page', data.title);\n    });\n  }\n  get backgroundStyle() {\n    const bg = window.innerWidth <= 768 ? this.mobileBackground : this.desktopBackground;\n    if (!bg) {\n      return {};\n    }\n    if (bg.startsWith('http') || bg.startsWith('url(')) {\n      return {\n        'background-image': `url(${bg})`\n      };\n    }\n    return {\n      'background': bg\n    };\n  }\n  handleProductFromFetchedData(record) {\n    let selectedVariance;\n    let features = [];\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      productId: record?.id,\n      productName: record?.name,\n      isLiked: record?.isLiked,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      priceId: selectedVariance?.priceId,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      specProductId: selectedVariance.specProductId,\n      channelId: record.channelId ?? '1',\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      shopId: record.shopId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated,\n      badges: record?.badgesList\n    };\n    return product;\n  }\n  formatURL(url) {\n    if (!url.startsWith('http://') && !url.startsWith('https://')) {\n      return 'https://' + url;\n    }\n    return url;\n  }\n  redirectFeaturedProducts(sectionName, SectionId) {\n    this.router.navigate([`/${this.router.url}/${sectionName}/${SectionId}`]);\n  }\n  getLimitedProducts(section) {\n    const limit = section.productsLimit ?? section.products.length;\n    return section.products.slice(0, limit);\n  }\n}\nCustomPageComponent.ɵfac = function CustomPageComponent_Factory(t) {\n  return new (t || CustomPageComponent)();\n};\nCustomPageComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CustomPageComponent,\n  selectors: [[\"app-custom-page\"]],\n  decls: 2,\n  vars: 1,\n  consts: [[4, \"ngFor\", \"ngForOf\"], [\"class\", \"main-container\", 4, \"ngIf\"], [\"class\", \"main-container banner-container\", 4, \"ngIf\"], [1, \"main-container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"class\", \"see-all\", 3, \"click\", 4, \"ngIf\"], [1, \"each-product\"], [\"class\", \"product-card\", 3, \"product\", 4, \"ngFor\", \"ngForOf\"], [1, \"see-all\", 3, \"click\"], [1, \"product-card\", 3, \"product\"], [1, \"main-container\", \"banner-container\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\"], [\"alt\", \"Desktop Banner\", 1, \"banner-desktop\", 3, \"src\"], [\"alt\", \"Mobile Banner\", 1, \"banner-mobile\", 3, \"src\"]],\n  template: function CustomPageComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"main\");\n      i0.ɵɵtemplate(1, CustomPageComponent_ng_container_1_Template, 5, 2, \"ng-container\", 0);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.sectionsData);\n    }\n  },\n  dependencies: [i1.NgForOf, i1.NgIf, i2.ProductCardComponent, i3.TranslatePipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\nmain[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\nmain[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n  margin: 5px 0 5px 9px;\\n  font-family: \\\"main-medium\\\";\\n  color: rgb(33, 37, 41);\\n  font-weight: 700;\\n  font-size: 22px;\\n  text-transform: capitalize;\\n}\\nmain[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap !important;\\n  padding: 0 16px 0 0;\\n  gap: 7px;\\n}\\nmain[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  width: 220px;\\n  display: flex;\\n  flex-direction: column;\\n}\\nmain[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]:first-child {\\n  margin-top: 26px;\\n}\\nmain[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  min-width: 80%;\\n  margin: auto;\\n}\\n@media (max-width: 768px) {\\n  main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-desktop[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\nmain[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-mobile[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  main[_ngcontent-%COMP%]   .main-container.banner-container[_ngcontent-%COMP%]   .banner-mobile[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.see-all[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--medium-font);\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 20px;\\n}\\n.see-all[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #1a445e;\\n}\\n\\n@media (max-width: 1200px) {\\n  main[_ngcontent-%COMP%] {\\n    margin-top: 120px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  main[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    text-transform: uppercase;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%] {\\n    display: grid;\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 10px;\\n    align-items: start;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    margin: 0;\\n    justify-self: center;\\n  }\\n}\\n@media (max-width: 699px) {\\n  main[_ngcontent-%COMP%] {\\n    margin-top: 35px !important;\\n  }\\n}\\n@media (max-width: 575px) {\\n  main[_ngcontent-%COMP%] {\\n    margin-top: 60px !important;\\n  }\\n}\\n@media (max-width: 467px) {\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%] {\\n    padding: 0 0 0 0;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: 150px !important;\\n  }\\n}\\n@media (max-width: 323px) {\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%] {\\n    padding: 0 0 0 0;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%] {\\n    padding: 0 !important;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: 130px !important;\\n  }\\n}\\n@media (max-width: 280px) {\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n  main[_ngcontent-%COMP%]   .main-container[_ngcontent-%COMP%]   .each-product[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    width: auto !important;\\n  }\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}