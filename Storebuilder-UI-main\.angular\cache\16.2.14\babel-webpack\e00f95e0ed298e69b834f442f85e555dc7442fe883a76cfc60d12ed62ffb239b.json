{"ast": null, "code": "import { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@core/services/gtm.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/card\";\nimport * as i7 from \"../../../../../../shared/pipes/santiaze.pipe\";\nfunction AboutUsComponentComponent_section_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.aboutUsDetails.title, \" \");\n  }\n}\nfunction AboutUsComponentComponent_section_0_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p\", 16);\n    i0.ɵɵpipe(1, \"safeComment\");\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHtml\", i0.ɵɵpipeBind1(1, 1, ctx_r2.aboutUsDetails.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction AboutUsComponentComponent_section_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.aboutUsDetails.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AboutUsComponentComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"p-card\", 6)(7, \"div\", 7)(8, \"div\", 8);\n    i0.ɵɵtemplate(9, AboutUsComponentComponent_section_0_div_9_Template, 3, 1, \"div\", 9);\n    i0.ɵɵelementStart(10, \"div\", 10);\n    i0.ɵɵtemplate(11, AboutUsComponentComponent_section_0_p_11_Template, 2, 3, \"p\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(12, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, AboutUsComponentComponent_section_0_div_13_Template, 3, 1, \"div\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.aboutUsDetails.pageTitle, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.image) && !(ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.visibility) ? \"col-md-10\" : \"col-md-10\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.aboutUsDetails.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.aboutUsDetails.content);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.image) && (ctx_r0.aboutUsDetails == null ? null : ctx_r0.aboutUsDetails.showImage));\n  }\n}\nexport class AboutUsComponentComponent {\n  constructor(aboutUsService, router, _router, domSanitizer, $gtmService) {\n    this.aboutUsService = aboutUsService;\n    this.router = router;\n    this._router = _router;\n    this.domSanitizer = domSanitizer;\n    this.$gtmService = $gtmService;\n    this.aboutUsDetails = {};\n    this.pageId = 0;\n    this.title = '';\n    this.isPageVisible = false;\n    this.environment = environment;\n    let page = router.snapshot.queryParamMap.get('pageId');\n    let pageTitle = router.snapshot.queryParamMap.get('title');\n    if (page) this.pageId = parseInt(page);\n    if (pageTitle) this.title = pageTitle;\n  }\n  ngOnInit() {\n    this.getAboutUsDetails();\n    this.$gtmService.pushPageView('about us', this.title);\n    this.pageId = history.state.id;\n  }\n  getAboutUsDetails() {\n    this.aboutUsService.getFooterPageDetails(this.pageId).subscribe({\n      next: res => {\n        // let page = res.data?.records.filter(\n        //   (x: any) => x.pageId == this.pageId\n        // );\n        let page = res.data;\n        if (!page[0].visibility) {\n          this._router.navigate(['/page-not-found']);\n        }\n        if (page) {\n          this.aboutUsDetails.pageTitle = page[0].pageTitle;\n          this.aboutUsDetails.title = page[0].title;\n          this.aboutUsDetails.showImage = page[0].showImage;\n          this.aboutUsDetails.visibility = page[0].visibility;\n          this.isPageVisible = page[0].visibility;\n          if (page[0].image) {\n            // this.aboutUsDetails.image = page[0].image;\n            this.aboutUsDetails.image = 'data:image/png;base64, ' + page[0].image;\n          }\n          this.aboutUsDetails.content = decodeURIComponent(escape(atob(page[0].content)));\n        }\n      },\n      error: err => {}\n    });\n  }\n  static #_ = this.ɵfac = function AboutUsComponentComponent_Factory(t) {\n    return new (t || AboutUsComponentComponent)(i0.ɵɵdirectiveInject(i1.ContactUsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.GTMService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AboutUsComponentComponent,\n    selectors: [[\"app-AboutUsComponent\"]],\n    inputs: {\n      aboutUsDetails: \"aboutUsDetails\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"contact-us-page pageTop\", 4, \"ngIf\"], [1, \"contact-us-page\", \"pageTop\"], [1, \"about-us-container\", \"mt-7\"], [1, \"font-size-28\", \"bold-font\", \"mb-3\"], [1, \"row\"], [3, \"ngClass\"], [1, \"m-6\"], [1, \"grid\"], [1, \"col-12\", \"detail-container\"], [\"class\", \"row bold-font font-size-24 \", 4, \"ngIf\"], [1, \"row\", \"font-size-18\", \"table-scroll-mobile\"], [\"class\", \"col-md-12 footer-font ql-editor\", 3, \"innerHtml\", 4, \"ngIf\"], [1, \"col-4\"], [\"class\", \"col-md-2 mt-3\", 4, \"ngIf\"], [1, \"row\", \"bold-font\", \"font-size-24\"], [1, \"col-md-12\"], [1, \"col-md-12\", \"footer-font\", \"ql-editor\", 3, \"innerHtml\"], [1, \"col-md-2\", \"mt-3\"], [1, \"image-container\"], [\"alt\", \"No Image\", 1, \"data-image\", 3, \"src\"]],\n    template: function AboutUsComponentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, AboutUsComponentComponent_section_0_Template, 14, 5, \"section\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isPageVisible);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i6.Card, i7.CommentPipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\nh2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%] {\\n  font-size: 16px !important;\\n}\\n\\nul[_ngcontent-%COMP%], li[_ngcontent-%COMP%], ol[_ngcontent-%COMP%], span[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h2[_ngcontent-%COMP%] {\\n  font-weight: 200 !important;\\n  margin: 0 !important;\\n  padding: 0 !important;\\n}\\n\\n.tg[_ngcontent-%COMP%] {\\n  border-collapse: collapse;\\n  border-spacing: 0;\\n}\\n\\n.tg[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: black;\\n  border-style: solid;\\n  border-width: 1px;\\n  font-family: Arial, sans-serif;\\n  font-size: 14px;\\n  overflow: hidden;\\n  padding: 10px 5px;\\n  word-break: normal;\\n}\\n\\n.tg[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-color: black;\\n  border-style: solid;\\n  border-width: 1px;\\n  font-family: Arial, sans-serif;\\n  font-size: 14px;\\n  font-weight: normal;\\n  overflow: hidden;\\n  padding: 10px 5px;\\n  word-break: normal;\\n}\\n\\n.tg[_ngcontent-%COMP%]   .tg-0pky[_ngcontent-%COMP%] {\\n  border-color: inherit;\\n  text-align: left;\\n  vertical-align: top;\\n}\\n\\n.data-image[_ngcontent-%COMP%] {\\n  width: 220px;\\n  height: 220px;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .about-us-container[_ngcontent-%COMP%] {\\n    padding: 2rem 1rem !important;\\n  }\\n}\\n\\n.detail-container[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 0rem 1.5rem !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .table-scroll-mobile[_ngcontent-%COMP%] {\\n    overflow-y: scroll;\\n  }\\n  .pageTop[_ngcontent-%COMP%] {\\n    margin-top: 100px;\\n  }\\n}\\n.footer-font[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  width: 220px;\\n  height: 220px;\\n  box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.12), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 1px 0px rgba(0, 0, 0, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "aboutUsDetails", "title", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r2", "content", "ɵɵsanitizeHtml", "ctx_r3", "image", "ɵɵsanitizeUrl", "ɵɵtemplate", "AboutUsComponentComponent_section_0_div_9_Template", "AboutUsComponentComponent_section_0_p_11_Template", "AboutUsComponentComponent_section_0_div_13_Template", "ctx_r0", "pageTitle", "visibility", "showImage", "AboutUsComponentComponent", "constructor", "aboutUsService", "router", "_router", "domSani<PERSON>zer", "$gtmService", "pageId", "isPageVisible", "page", "snapshot", "queryParamMap", "get", "parseInt", "ngOnInit", "getAboutUsDetails", "pushPageView", "history", "state", "id", "getFooterPageDetails", "subscribe", "next", "res", "data", "navigate", "decodeURIComponent", "escape", "atob", "error", "err", "_", "ɵɵdirectiveInject", "i1", "ContactUsService", "i2", "ActivatedRoute", "Router", "i3", "Dom<PERSON><PERSON><PERSON>zer", "i4", "GTMService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "AboutUsComponentComponent_Template", "rf", "ctx", "AboutUsComponentComponent_section_0_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\footer-pages\\about-us\\components\\about-us\\AboutUsComponent\\AboutUsComponent.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\footer-pages\\about-us\\components\\about-us\\AboutUsComponent\\AboutUsComponent.component.html"], "sourcesContent": ["import {Component, Input, OnInit} from '@angular/core';\r\nimport {DomSanitizer} from '@angular/platform-browser';\r\nimport {ActivatedRoute, Router} from '@angular/router';\r\nimport {GetAllAboutUs} from '@core/interface';\r\nimport {ContactUsService} from '@core/services';\r\nimport { GTMService } from '@core/services/gtm.service';\r\nimport {environment} from \"@environments/environment\";\r\n\r\n@Component({\r\n  selector: 'app-AboutUsComponent',\r\n  templateUrl: './AboutUsComponent.component.html',\r\n  styleUrls: ['./AboutUsComponent.component.scss'],\r\n})\r\nexport class AboutUsComponentComponent implements OnInit {\r\n  @Input() aboutUsDetails: GetAllAboutUs = {} as GetAllAboutUs;\r\n  pageId: number = 0;\r\n  title: string = '';\r\n  isPageVisible: boolean = false;\r\n\r\n  constructor(\r\n    private aboutUsService: ContactUsService,\r\n    private router: ActivatedRoute,\r\n    private _router: Router,\r\n    private domSanitizer: Dom<PERSON>anitizer,\r\n    private $gtmService:GTMService\r\n  ) {\r\n    let page = router.snapshot.queryParamMap.get('pageId');\r\n    let pageTitle = router.snapshot.queryParamMap.get('title');\r\n    if (page) this.pageId = parseInt(page);\r\n    if (pageTitle) this.title = pageTitle;\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.getAboutUsDetails();\r\n    this.$gtmService.pushPageView('about us',this.title)\r\n    this.pageId = history.state.id\r\n  }\r\n\r\n  getAboutUsDetails(): void {\r\n    this.aboutUsService.getFooterPageDetails(this.pageId).subscribe({\r\n      next: (res: any) => {\r\n        // let page = res.data?.records.filter(\r\n          //   (x: any) => x.pageId == this.pageId\r\n          // );\r\n        let page = res.data\r\n\r\n        if(!page[0].visibility) {\r\n          this._router.navigate(['/page-not-found']);\r\n        }\r\n\r\n        if(page){\r\n          this.aboutUsDetails.pageTitle =  page[0].pageTitle\r\n          this.aboutUsDetails.title = page[0].title;\r\n          this.aboutUsDetails.showImage = page[0].showImage;\r\n          this.aboutUsDetails.visibility = page[0].visibility;\r\n          this.isPageVisible = page[0].visibility\r\n          if(page[0].image){\r\n            // this.aboutUsDetails.image = page[0].image;\r\n            this.aboutUsDetails.image = 'data:image/png;base64, ' + page[0].image; \r\n          }\r\n          this.aboutUsDetails.content = decodeURIComponent(escape(atob(page[0].content)));\r\n        }\r\n\r\n      },\r\n      error: (err: any) => {\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  protected readonly environment = environment;\r\n}\r\n", "<section class=\"contact-us-page pageTop\" *ngIf=\"isPageVisible\">\r\n  <div class=\"about-us-container mt-7\">\r\n    <div class=\"font-size-28 bold-font mb-3\">\r\n      {{aboutUsDetails.pageTitle}}\r\n    </div>\r\n    <div class=\"row\">\r\n      <div [ngClass]=\"(aboutUsDetails?.image && !aboutUsDetails?.visibility) ?'col-md-10': 'col-md-10'\">\r\n        <p-card class=\"m-6\">\r\n          <div class=\"grid\">\r\n            <div class=\"col-12 detail-container\">\r\n\r\n              <div *ngIf=\"aboutUsDetails.title\" class=\"row bold-font font-size-24 \">\r\n                <p class=\"col-md-12\">\r\n                  {{ aboutUsDetails.title }}\r\n                </p>\r\n\r\n              </div>\r\n              <div class=\"row font-size-18 table-scroll-mobile\">\r\n                <p *ngIf=\"aboutUsDetails.content\" [innerHtml]=\"aboutUsDetails.content | safeComment\"\r\n                  class=\"col-md-12 footer-font ql-editor\">\r\n                </p>\r\n              </div>\r\n\r\n            </div>\r\n            <div class=\"col-4\"></div>\r\n          </div>\r\n        </p-card>\r\n      </div>\r\n\r\n      <div class=\"col-md-2 mt-3\" *ngIf=\"aboutUsDetails?.image && aboutUsDetails?.showImage\">\r\n\r\n        <div class=\"image-container\">\r\n          <!-- <img alt=\"No Image\" class=\"data-image\" [src]=\"environment.apiEndPoint + '/Images/' + aboutUsDetails?.image\"> -->\r\n          <img alt=\"No Image\" class=\"data-image\" [src]=\"this.aboutUsDetails.image\">\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": "AAMA,SAAQA,WAAW,QAAO,2BAA2B;;;;;;;;;;;ICKvCC,EAAA,CAAAC,cAAA,cAAsE;IAElED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,KAAA,MACF;;;;;IAIAR,EAAA,CAAAS,SAAA,YAEI;;;;;IAF8BT,EAAA,CAAAU,UAAA,cAAAV,EAAA,CAAAW,WAAA,OAAAC,MAAA,CAAAL,cAAA,CAAAM,OAAA,GAAAb,EAAA,CAAAc,cAAA,CAAkD;;;;;IAW9Fd,EAAA,CAAAC,cAAA,cAAsF;IAIlFD,EAAA,CAAAS,SAAA,cAAyE;IAE3ET,EAAA,CAAAG,YAAA,EAAM;;;;IAFmCH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAU,UAAA,QAAAK,MAAA,CAAAR,cAAA,CAAAS,KAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAAiC;;;;;IAjClFjB,EAAA,CAAAC,cAAA,iBAA+D;IAGzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAiB;IAMPD,EAAA,CAAAkB,UAAA,IAAAC,kDAAA,iBAKM;IACNnB,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAkB,UAAA,KAAAE,iDAAA,gBAEI;IACNpB,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAS,SAAA,eAAyB;IAC3BT,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAkB,UAAA,KAAAG,mDAAA,kBAOM;IACRrB,EAAA,CAAAG,YAAA,EAAM;;;;IAlCJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAiB,MAAA,CAAAf,cAAA,CAAAgB,SAAA,MACF;IAEOvB,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAU,UAAA,aAAAY,MAAA,CAAAf,cAAA,kBAAAe,MAAA,CAAAf,cAAA,CAAAS,KAAA,OAAAM,MAAA,CAAAf,cAAA,kBAAAe,MAAA,CAAAf,cAAA,CAAAiB,UAAA,8BAA4F;IAKnFxB,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAU,UAAA,SAAAY,MAAA,CAAAf,cAAA,CAAAC,KAAA,CAA0B;IAO1BR,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAU,UAAA,SAAAY,MAAA,CAAAf,cAAA,CAAAM,OAAA,CAA4B;IAWdb,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAY,MAAA,CAAAf,cAAA,kBAAAe,MAAA,CAAAf,cAAA,CAAAS,KAAA,MAAAM,MAAA,CAAAf,cAAA,kBAAAe,MAAA,CAAAf,cAAA,CAAAkB,SAAA,EAAwD;;;ADhB1F,OAAM,MAAOC,yBAAyB;EAMpCC,YACUC,cAAgC,EAChCC,MAAsB,EACtBC,OAAe,EACfC,YAA0B,EAC1BC,WAAsB;IAJtB,KAAAJ,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IAVZ,KAAAzB,cAAc,GAAkB,EAAmB;IAC5D,KAAA0B,MAAM,GAAW,CAAC;IAClB,KAAAzB,KAAK,GAAW,EAAE;IAClB,KAAA0B,aAAa,GAAY,KAAK;IAqDX,KAAAnC,WAAW,GAAGA,WAAW;IA5C1C,IAAIoC,IAAI,GAAGN,MAAM,CAACO,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,QAAQ,CAAC;IACtD,IAAIf,SAAS,GAAGM,MAAM,CAACO,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC;IAC1D,IAAIH,IAAI,EAAE,IAAI,CAACF,MAAM,GAAGM,QAAQ,CAACJ,IAAI,CAAC;IACtC,IAAIZ,SAAS,EAAE,IAAI,CAACf,KAAK,GAAGe,SAAS;EACvC;EAEAiB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACT,WAAW,CAACU,YAAY,CAAC,UAAU,EAAC,IAAI,CAAClC,KAAK,CAAC;IACpD,IAAI,CAACyB,MAAM,GAAGU,OAAO,CAACC,KAAK,CAACC,EAAE;EAChC;EAEAJ,iBAAiBA,CAAA;IACf,IAAI,CAACb,cAAc,CAACkB,oBAAoB,CAAC,IAAI,CAACb,MAAM,CAAC,CAACc,SAAS,CAAC;MAC9DC,IAAI,EAAGC,GAAQ,IAAI;QACjB;QACE;QACA;QACF,IAAId,IAAI,GAAGc,GAAG,CAACC,IAAI;QAEnB,IAAG,CAACf,IAAI,CAAC,CAAC,CAAC,CAACX,UAAU,EAAE;UACtB,IAAI,CAACM,OAAO,CAACqB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;;QAG5C,IAAGhB,IAAI,EAAC;UACN,IAAI,CAAC5B,cAAc,CAACgB,SAAS,GAAIY,IAAI,CAAC,CAAC,CAAC,CAACZ,SAAS;UAClD,IAAI,CAAChB,cAAc,CAACC,KAAK,GAAG2B,IAAI,CAAC,CAAC,CAAC,CAAC3B,KAAK;UACzC,IAAI,CAACD,cAAc,CAACkB,SAAS,GAAGU,IAAI,CAAC,CAAC,CAAC,CAACV,SAAS;UACjD,IAAI,CAAClB,cAAc,CAACiB,UAAU,GAAGW,IAAI,CAAC,CAAC,CAAC,CAACX,UAAU;UACnD,IAAI,CAACU,aAAa,GAAGC,IAAI,CAAC,CAAC,CAAC,CAACX,UAAU;UACvC,IAAGW,IAAI,CAAC,CAAC,CAAC,CAACnB,KAAK,EAAC;YACf;YACA,IAAI,CAACT,cAAc,CAACS,KAAK,GAAG,yBAAyB,GAAGmB,IAAI,CAAC,CAAC,CAAC,CAACnB,KAAK;;UAEvE,IAAI,CAACT,cAAc,CAACM,OAAO,GAAGuC,kBAAkB,CAACC,MAAM,CAACC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAACtB,OAAO,CAAC,CAAC,CAAC;;MAGnF,CAAC;MACD0C,KAAK,EAAGC,GAAQ,IAAI,CACpB;KACD,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBAtDU/B,yBAAyB,EAAA1B,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA/D,EAAA,CAAA0D,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAjE,EAAA,CAAA0D,iBAAA,CAAAQ,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzB1C,yBAAyB;IAAA2C,SAAA;IAAAC,MAAA;MAAA/D,cAAA;IAAA;IAAAgE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbtC5E,EAAA,CAAAkB,UAAA,IAAA4D,4CAAA,sBAuCU;;;QAvCgC9E,EAAA,CAAAU,UAAA,SAAAmE,GAAA,CAAA3C,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}