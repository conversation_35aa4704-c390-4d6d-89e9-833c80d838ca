{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { routes } from './routes';\nimport { NotFoundComponent } from './components/not-found.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class NotFoundModule {\n  static ɵfac = function NotFoundModule_Factory(t) {\n    return new (t || NotFoundModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NotFoundModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), TranslateModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotFoundModule, {\n    declarations: [NotFoundComponent],\n    imports: [CommonModule, i1.RouterModule, TranslateModule]\n  });\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}