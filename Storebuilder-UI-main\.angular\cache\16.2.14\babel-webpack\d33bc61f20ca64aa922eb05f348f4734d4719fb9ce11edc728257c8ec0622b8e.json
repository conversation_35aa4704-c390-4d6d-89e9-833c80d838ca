{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"@ngx-translate/core\";\nfunction OrderPlacedComponent_ng_container_0_p_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"checkout.orderPlaced.thanksForOrderingYalla\"), \" : \");\n  }\n}\nfunction OrderPlacedComponent_ng_container_0_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"checkout.orderPlaced.thanksForOrderingMomo\"), \" : \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.orderId, \" \");\n  }\n}\nfunction OrderPlacedComponent_ng_container_0_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"checkout.orderPlaced.discount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"- \", ctx_r5.currencyCode, \" \", ctx_r5.disableCent === \"false\" ? i0.ɵɵpipeBind2(6, 5, ctx_r5.orderDiscount, \"1.\" + ctx_r5.decimalValue + \"-\" + ctx_r5.decimalValue) : i0.ɵɵpipeBind1(7, 8, ctx_r5.orderDiscount), \"\");\n  }\n}\nfunction OrderPlacedComponent_ng_container_0_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"checkout.orderPlaced.deliveryoption\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.deliveryOption);\n  }\n}\nfunction OrderPlacedComponent_ng_container_0_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 15)(5, \"span\")(6, \"input\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function OrderPlacedComponent_ng_container_0_div_51_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.addressService.chosenAddress.streetAddress = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"checkout.orderPlaced.shipping\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", ctx_r7.addressService.chosenAddress.streetAddress)(\"ngModel\", ctx_r7.addressService.chosenAddress.streetAddress);\n  }\n}\nconst _c0 = function () {\n  return [\"/orders\"];\n};\nfunction OrderPlacedComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 2);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\");\n    i0.ɵɵelement(9, \"img\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"p\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, OrderPlacedComponent_ng_container_0_p_14_Template, 3, 3, \"p\", 11);\n    i0.ɵɵtemplate(15, OrderPlacedComponent_ng_container_0_p_15_Template, 5, 4, \"p\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13)(18, \"p\", 14);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 15);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 13)(26, \"p\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 15);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"number\");\n    i0.ɵɵpipe(32, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, OrderPlacedComponent_ng_container_0_div_33_Template, 8, 10, \"div\", 16);\n    i0.ɵɵelementStart(34, \"div\", 13)(35, \"p\", 17);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\", 18);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"number\");\n    i0.ɵɵpipe(41, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 13)(44, \"p\", 14);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 15);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 9);\n    i0.ɵɵtemplate(50, OrderPlacedComponent_ng_container_0_div_50_Template, 6, 4, \"div\", 16);\n    i0.ɵɵtemplate(51, OrderPlacedComponent_ng_container_0_div_51_Template, 7, 5, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 9);\n    i0.ɵɵelement(53, \"button\", 20);\n    i0.ɵɵpipe(54, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 20, \"checkout.orderPlaced.orderPlaced\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isConfig);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 22, \"checkout.orderPlaced.totalItemCost\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(23, 24, ctx_r0.orderDetails.orderAmount, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue) : i0.ɵɵpipeBind1(24, 27, ctx_r0.orderDetails.orderAmount), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 29, \"checkout.orderPlaced.shipping\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(31, 31, ctx_r0.shipmentCost, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue) : i0.ɵɵpipeBind1(32, 34, ctx_r0.shipmentCost), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.orderDiscount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 36, \"checkout.orderPlaced.paymentTotal\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(40, 38, ctx_r0.paymentTotal, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue) : i0.ɵɵpipeBind1(41, 41, ctx_r0.paymentTotal), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(46, 43, \"checkout.orderPlaced.paymentMethod\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r0.transactionData == null ? null : ctx_r0.transactionData.PaymentMethod) === 1 ? \"Visa\" : \"MoMo Pay\", \" \", ctx_r0.transactionData.CardNumber, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isShipmentFee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(54, 45, \"checkout.orderPlaced.myOrders\"));\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(47, _c0));\n  }\n}\nfunction OrderPlacedComponent_ng_template_1_p_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"span\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"checkout.orderPlaced.discount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"- \", ctx_r10.currencyCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.orderDiscount);\n  }\n}\nfunction OrderPlacedComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"main\", 26);\n    i0.ɵɵelement(1, \"img\", 8);\n    i0.ɵɵelementStart(2, \"section\", 27)(3, \"h2\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"article\", 30)(12, \"p\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementStart(15, \"span\", 32);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementStart(17, \"span\", 33);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"p\", 31);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementStart(23, \"span\", 32);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementStart(25, \"span\", 33);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, OrderPlacedComponent_ng_template_1_p_28_Template, 7, 5, \"p\", 34);\n    i0.ɵɵelement(29, \"hr\", 35);\n    i0.ɵɵelementStart(30, \"p\", 36);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementStart(33, \"span\", 32);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementStart(35, \"span\", 33);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"currency\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(38, \"p\", 37);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"translate\");\n    i0.ɵɵelementStart(41, \"span\", 33);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"p\", 38);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementStart(46, \"span\", 39);\n    i0.ɵɵtext(47);\n    i0.ɵɵelement(48, \"br\");\n    i0.ɵɵelementStart(49, \"span\", 40);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(51, \"button\", 41);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 20, \"checkout.orderPlaced.mobilHeader\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 22, \"checkout.orderPlaced.mobilP\"), \" : \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.orderDetails.orderId, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 24, \"checkout.orderPlaced.total\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.currencyCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 26, ctx_r2.orderDetails.orderAmount, \" \"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 29, \"checkout.orderPlaced.shippingFee\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.currencyCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(27, 31, ctx_r2.shipmentCost, \" \"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.orderDiscount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 34, \"checkout.orderPlaced.totalMoney\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.currencyCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(37, 36, ctx_r2.paymentTotal, \" \"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 39, \"checkout.orderPlaced.payment\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.transactionData == null ? null : ctx_r2.transactionData.PaymentMethod) === 1 ? \"Visa\" : \"MoMo Pay\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(45, 41, \"checkout.orderPlaced.delivery\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.mobilDeliveryOption ? ctx_r2.mobilDeliveryOption : ctx_r2.deliveryOption, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.mobilDeliveryOptionTime, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(45, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(53, 43, \"checkout.orderPlaced.myOrders\"), \" \");\n  }\n}\nexport class OrderPlacedComponent {\n  constructor(addressService, orderService, store, cartService, permissionService) {\n    this.addressService = addressService;\n    this.orderService = orderService;\n    this.store = store;\n    this.cartService = cartService;\n    this.permissionService = permissionService;\n    this.cartId = 0;\n    this.shipmentCost = 0;\n    this.paymentTotal = 0;\n    this.currencyCode = '';\n    this.deliveryOption = '';\n    this.isConfig = environment.isStoreCloud;\n    this.isShipmentFee = false;\n    this.isMobileLayout = false;\n    this.hasDiscount = false;\n    this.orderDiscount = 0;\n    this.orderId = '';\n    this.decimalValue = 0;\n    this.screenWidth = window.innerWidth;\n    this.orderId = this.orderService.orderId;\n    this.isShipmentFee = this.permissionService.hasPermission('Shipment-Fee');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n  }\n  ngOnInit() {\n    this.disableCent = localStorage.getItem('DisableCents') ?? '';\n    this.decimalValue = JSON.parse(localStorage.getItem('CurrencyDecimal') ?? '');\n    this.getCurrentCartId();\n    this.store.subscription('orderData').subscribe({\n      next: res => {\n        if (res) {\n          this.orderDetails = res;\n          if (this.orderDetails != null) {\n            this.orderDetails.orderAmount = Math.round(this.orderDetails.orderAmount * 100) / 100;\n            if (this.orderDetails.productDetails[0].currencyCode) {\n              this.currencyCode = this.orderDetails.productDetails[0].currencyCode;\n            }\n            this.getOrderDiscount(this.orderDetails.orderId);\n          }\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n    this.store.subscription('shipmentCost').subscribe({\n      next: res => {\n        if (res.deliveryOption) {\n          this.deliveryOption = res.deliveryOption.name;\n          if (res.deliveryOption.name.includes(\"(\")) {\n            let options = res.deliveryOption.name.split(\" \");\n            this.mobilDeliveryOptionTime = options.pop();\n            this.mobilDeliveryOption = res.deliveryOption.name.replace(this.mobilDeliveryOptionTime, \"\");\n          }\n        }\n        if (res.totalDeliveryCost !== null && res.totalDeliveryCost > 0) {\n          this.shipmentCost = Math.round(Number(res.totalDeliveryCost) * 100) / 100;\n          this.paymentTotal = Math.round((this.orderDetails?.orderAmount + this.shipmentCost + Number.EPSILON) * 100) / 100;\n        } else {\n          this.paymentTotal = Math.round((this.orderDetails?.orderAmount + Number.EPSILON) * 100) / 100;\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n    this.store.subscription('transactionData').subscribe({\n      next: res => {\n        if (res) {\n          this.transactionData = res;\n          if (this.transactionData?.CardNumber) {\n            this.transactionData.CardNumber = \"****\" + this.transactionData.CardNumber.slice(-4);\n          }\n          if (this.transactionData?.currencyCode != undefined) {\n            this.currencyCode = this.transactionData?.currencyCode;\n          }\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  getOrderDiscount(id) {\n    this.orderService.getOrderDiscount(id).subscribe({\n      next: res => {\n        if (res.success) {\n          this.orderDiscount = res.data ? res.data : this.hasDiscount;\n          if (this.orderDiscount) {\n            this.paymentTotal = this.paymentTotal - this.orderDiscount;\n          }\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  getCurrentCartId() {\n    this.store.subscription('cartProducts').subscribe({\n      next: res => {\n        this.cartId = res[0]?.cartId;\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n}\nOrderPlacedComponent.ɵfac = function OrderPlacedComponent_Factory(t) {\n  return new (t || OrderPlacedComponent)(i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i1.OrderService), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i1.PermissionService));\n};\nOrderPlacedComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: OrderPlacedComponent,\n  selectors: [[\"app-order-placed\"]],\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"mobilView\", \"\"], [1, \"checkout\"], [1, \"content-container\", \"my-3\"], [1, \"grid\", \"justify-content-center\"], [1, \"col\", \"col-md-6\", \"col-lg-5\", \"bg-white\", \"px-5\", \"pt-6\", \"zoom-design-fix\"], [1, \"grid\"], [1, \"col-12\", \"flex\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"assets/images/my-order-image.svg\", \"alt\", \"\", 1, \"img-header\"], [1, \"col-12\"], [1, \"text-center\", \"order-placed\"], [\"class\", \"text-center thanks-mesg\", 4, \"ngIf\"], [1, \"col-12\", \"border-bottom-2\", \"border-200\"], [1, \"flex\", \"justify-content-between\", \"flex-wrap\", \"card-container\", \"purple-container\"], [1, \"order-placed-list\"], [1, \"order-placed-price\"], [\"class\", \"flex justify-content-between flex-wrap card-container purple-container\", 4, \"ngIf\"], [1, \"payment-total\"], [1, \"payment-order\"], [1, \"col-12\", \"border-bottom-2\", \"border-200\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"col-12\", \"my-2\", \"width-100\", \"second-btn\", 3, \"routerLink\", \"label\"], [1, \"text-center\", \"thanks-mesg\"], [1, \"order-placed-list\", \"discount-style\"], [1, \"order-placed-price\", \"money-style\", \"discount-style\"], [1, \"order-placed-price\", \"standard-delivery-style\"], [\"pInputText\", \"\", \"readonly\", \"\", \"type\", \"text\", 1, \"input-text-mobile\", \"border-none\", \"text-800\", \"font-size-16\", \"medium-font\", \"width-100\", \"pl-0\", \"pt-0\", 3, \"title\", \"ngModel\", \"ngModelChange\"], [1, \"mobil-container\"], [1, \"mobil-main-container\"], [1, \"mobil-header\"], [1, \"mobil-p\"], [1, \"mobil-price-article\"], [1, \"details\"], [1, \"currancy-style\"], [1, \"money-style\"], [\"class\", \"details\", 4, \"ngIf\"], [1, \"hr\"], [1, \"details\", \"total-mobil\"], [1, \"mobil-payment\"], [1, \"mobil-payment\", \"mobil-delivery\"], [1, \"standard-delivery\", \"money-style\"], [1, \"standard-delivery2\"], [\"type\", \"button\", 1, \"mobil-btn\", 3, \"routerLink\"], [1, \"currancy-style\", \"discount-style\"], [1, \"money-style\", \"discount-style\"]],\n  template: function OrderPlacedComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, OrderPlacedComponent_ng_container_0_Template, 55, 48, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, OrderPlacedComponent_ng_template_1_Template, 54, 46, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isMobileLayout && ctx.screenWidth > 768)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i2.NgIf, i3.RouterLink, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.ButtonDirective, i2.DecimalPipe, i2.CurrencyPipe, i6.TranslatePipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.order-placed[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-size: 18px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.thanks-mesg[_ngcontent-%COMP%] {\\n  color: #5F5F5F;\\n  font-size: 15px;\\n  font-weight: 400;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.order-placed-list[_ngcontent-%COMP%] {\\n  color: #5F6C72;\\n  font-family: var(--regular-font) !important;\\n  font-size: 15px;\\n  font-weight: 400;\\n}\\n\\n.order-placed-price[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-weight: 400;\\n  font-size: 15px;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.standard-delivery-style[_ngcontent-%COMP%] {\\n  text-align-last: right;\\n  width: 137px;\\n}\\n\\n.payment-order[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--bold-font) !important;\\n  font-size: 15px;\\n}\\n\\n.payment-total[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font) !important;\\n  color: #191C1F;\\n  font-weight: 16;\\n  font-size: 15px;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-size: 15px;\\n  font-weight: 500;\\n  border-radius: 5px;\\n  padding: 15px 19px;\\n}\\n\\n.discount-style[_ngcontent-%COMP%] {\\n  color: #E21836;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n  .checkout[_ngcontent-%COMP%] {\\n    margin-top: 20px !important;\\n  }\\n  .width-26[_ngcontent-%COMP%] {\\n    margin-bottom: 12px !important;\\n  }\\n}\\n@media only screen and (min-device-width: 1439px) and (max-device-width: 1441px) and (min-resolution: 1dppx) {\\n  .zoom-design-fix[_ngcontent-%COMP%] {\\n    \\n\\n    width: 36.333333%;\\n  }\\n}\\n\\n\\n@media only screen and (min-device-width: 1439px) and (max-device-width: 1441px) and (min-resolution: 1.1dppx) {\\n  .zoom-design-fix[_ngcontent-%COMP%] {\\n    \\n\\n    width: 41.333333%;\\n  }\\n}\\n\\n\\n@media only screen and (min-device-width: 1439px) and (max-device-width: 1441px) and (min-resolution: 1.25dppx) {\\n  .zoom-design-fix[_ngcontent-%COMP%] {\\n    \\n\\n    width: 47.333333%;\\n  }\\n}\\n\\n\\n@media only screen and (min-device-width: 1919px) and (max-device-width: 1921px) and (min-resolution: 1.1dppx) {\\n  .zoom-design-fix[_ngcontent-%COMP%] {\\n    \\n\\n    width: 32.333333%;\\n  }\\n}\\n\\n\\n@media only screen and (min-device-width: 1919px) and (max-device-width: 1921px) and (min-resolution: 1.25dppx) {\\n  .zoom-design-fix[_ngcontent-%COMP%] {\\n    \\n\\n    width: 36.333333%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .mobil-container[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 20px;\\n    padding: 16px 0;\\n    margin: 74px 0 68px;\\n    font-family: \\\"main-regular\\\" !important;\\n  }\\n  .img-header[_ngcontent-%COMP%] {\\n    width: 80px;\\n    margin: 0 auto;\\n  }\\n  .mobil-main-container[_ngcontent-%COMP%] {\\n    display: grid;\\n    gap: 8px;\\n    padding: 12px 16px;\\n    font-family: \\\"main-regular\\\" !important;\\n  }\\n  .mobil-header[_ngcontent-%COMP%] {\\n    text-align: center;\\n    color: #2D2D2D;\\n    font-size: 18px;\\n    font-weight: 500 !important;\\n    margin: 0 auto !important;\\n    max-width: 333px;\\n    font-family: \\\"main-medium\\\" !important;\\n    line-height: normal;\\n  }\\n  .mobil-p[_ngcontent-%COMP%] {\\n    color: #5F5F5F;\\n    text-align: center;\\n    font-size: 14px;\\n    font-weight: 400 !important;\\n    line-height: 150%;\\n    max-width: 343px;\\n    margin: 0 auto !important;\\n  }\\n  .mobil-price-article[_ngcontent-%COMP%] {\\n    display: grid;\\n    gap: 12px;\\n    align-items: center;\\n    background-color: #F8F9FA;\\n    border: 1px solid #E4E7E9;\\n    border-radius: 6px;\\n    opacity: 0.99;\\n    padding: 4px 16px;\\n  }\\n  .mobil-price-article[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    margin: 0 !important;\\n    color: var(--Gray-600, #5F6C72);\\n    font-size: 14px;\\n    font-weight: 400;\\n    line-height: 20px;\\n  }\\n  .mobil-price-article[_ngcontent-%COMP%]   .discount-style[_ngcontent-%COMP%] {\\n    color: #E21836;\\n  }\\n  .mobil-price-article[_ngcontent-%COMP%]   .hr[_ngcontent-%COMP%] {\\n    border-top: #A3A3A3 dashed 2px;\\n    width: 90%;\\n    margin: 0 auto;\\n  }\\n  .mobil-price-article[_ngcontent-%COMP%]   .total-mobil[_ngcontent-%COMP%] {\\n    color: #323232;\\n    font-family: \\\"main-medium\\\" !important;\\n    font-weight: 500 !important;\\n  }\\n  .mobil-payment[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 4px 16px;\\n    border: 1px solid #E4E7E9;\\n    border-radius: 6px;\\n    opacity: 0.99;\\n    background-color: #F8F9FA;\\n    margin: 0 !important;\\n    color: var(--Gray-600, #5F6C72);\\n    font-size: 14px;\\n    font-weight: 400;\\n    line-height: 20px;\\n  }\\n  .mobil-payment.mobil-delivery[_ngcontent-%COMP%] {\\n    align-items: flex-start !important;\\n  }\\n  .mobil-payment[_ngcontent-%COMP%]   .standard-delivery[_ngcontent-%COMP%] {\\n    display: grid;\\n  }\\n  .mobil-payment[_ngcontent-%COMP%]   .standard-delivery2[_ngcontent-%COMP%] {\\n    margin-left: auto;\\n  }\\n  .mobil-btn[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    margin: 0 16px;\\n    height: 48px;\\n    padding: 12px 24px;\\n    border: none;\\n    border-radius: 6px;\\n    background-color: #204E6E;\\n    color: #FFF;\\n    font-size: 14px;\\n    font-family: \\\"main-regular\\\" !important;\\n    font-weight: 700 !important;\\n    line-height: 56px;\\n    letter-spacing: 0.168px;\\n  }\\n  .currancy-style[_ngcontent-%COMP%] {\\n    color: #191C1F;\\n    font-size: 12px;\\n    font-family: \\\"main-regular\\\" !important;\\n    font-weight: 400 !important;\\n    line-height: 20px;\\n  }\\n  .money-style[_ngcontent-%COMP%] {\\n    color: #191C1F;\\n    font-size: 14px;\\n    font-family: \\\"main-medium\\\" !important;\\n    font-weight: 500 !important;\\n    line-height: 20px;\\n  }\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}