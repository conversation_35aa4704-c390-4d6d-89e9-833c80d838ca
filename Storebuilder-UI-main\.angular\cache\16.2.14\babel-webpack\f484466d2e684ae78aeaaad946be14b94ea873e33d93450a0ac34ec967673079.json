{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\n\n/** Default dropdown configuration */\nconst _c0 = function (a0) {\n  return {\n    dropdown: a0\n  };\n};\nconst _c1 = [\"*\"];\nclass BsDropdownConfig {\n  constructor() {\n    /** default dropdown auto closing behavior */\n    this.autoClose = true;\n    /** default dropdown auto closing behavior */\n    this.insideClick = false;\n    /** turn on/off animation */\n    this.isAnimated = false;\n    /** value true of stopOnClickPropagation allows event stopPropagation*/\n    this.stopOnClickPropagation = false;\n  }\n  static #_ = this.ɵfac = function BsDropdownConfig_Factory(t) {\n    return new (t || BsDropdownConfig)();\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BsDropdownConfig,\n    factory: BsDropdownConfig.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BsDropdownState {\n  constructor() {\n    this.direction = 'down';\n    this.autoClose = true;\n    this.insideClick = false;\n    this.isAnimated = false;\n    this.stopOnClickPropagation = false;\n    this.isOpenChange = new EventEmitter();\n    this.isDisabledChange = new EventEmitter();\n    this.toggleClick = new EventEmitter();\n    this.counts = 0;\n    this.dropdownMenu = new Promise(resolve => {\n      this.resolveDropdownMenu = resolve;\n    });\n  }\n  static #_ = this.ɵfac = function BsDropdownState_Factory(t) {\n    return new (t || BsDropdownState)();\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BsDropdownState,\n    factory: BsDropdownState.ɵfac,\n    providedIn: 'platform'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownState, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [style({\n  height: 0,\n  overflow: 'hidden'\n}), animate(DROPDOWN_ANIMATION_TIMING, style({\n  height: '*',\n  overflow: 'hidden'\n}))];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nclass BsDropdownContainerComponent {\n  get direction() {\n    return this._state.direction;\n  }\n  constructor(_state, cd, _renderer, _element, _builder) {\n    this._state = _state;\n    this.cd = cd;\n    this._renderer = _renderer;\n    this._element = _element;\n    this.isOpen = false;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    this._subscription = _state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n      this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n      if (dropdown) {\n        this._renderer.addClass(dropdown, 'show');\n        if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n          this._renderer.setStyle(dropdown, 'left', 'auto');\n          this._renderer.setStyle(dropdown, 'right', '0');\n        }\n        if (this.direction === 'up') {\n          this._renderer.setStyle(dropdown, 'top', 'auto');\n          this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n        }\n      }\n      if (dropdown && this._state.isAnimated) {\n        this._factoryDropDownAnimation.create(dropdown).play();\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n  }\n  /** @internal */\n  _contains(el) {\n    return this._element.nativeElement.contains(el);\n  }\n  ngOnDestroy() {\n    this._subscription.unsubscribe();\n  }\n  static #_ = this.ɵfac = function BsDropdownContainerComponent_Factory(t) {\n    return new (t || BsDropdownContainerComponent)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: BsDropdownContainerComponent,\n    selectors: [[\"bs-dropdown-container\"]],\n    hostAttrs: [2, \"display\", \"block\", \"position\", \"absolute\", \"z-index\", \"1040\"],\n    ngContentSelectors: _c1,\n    decls: 2,\n    vars: 9,\n    consts: [[3, \"ngClass\"]],\n    template: function BsDropdownContainerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"dropup\", ctx.direction === \"up\")(\"show\", ctx.isOpen)(\"open\", ctx.isOpen);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.direction === \"down\"));\n      }\n    },\n    dependencies: [i3.NgClass],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'bs-dropdown-container',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        style: 'display:block;position: absolute;z-index: 1040'\n      },\n      template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: BsDropdownState\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.AnimationBuilder\n    }];\n  }, null);\n})();\nclass BsDropdownDirective {\n  /**\n   * Indicates that dropdown will be closed on item or document click,\n   * and after pressing ESC\n   */\n  set autoClose(value) {\n    this._state.autoClose = value;\n  }\n  get autoClose() {\n    return this._state.autoClose;\n  }\n  /**\n   * Indicates that dropdown will be animated\n   */\n  set isAnimated(value) {\n    this._state.isAnimated = value;\n  }\n  get isAnimated() {\n    return this._state.isAnimated;\n  }\n  /**\n   * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n   */\n  set insideClick(value) {\n    this._state.insideClick = value;\n  }\n  get insideClick() {\n    return this._state.insideClick;\n  }\n  /**\n   * Disables dropdown toggle and hides dropdown menu if opened\n   */\n  set isDisabled(value) {\n    this._isDisabled = value;\n    this._state.isDisabledChange.emit(value);\n    if (value) {\n      this.hide();\n    }\n  }\n  get isDisabled() {\n    return this._isDisabled;\n  }\n  /**\n   * Returns whether or not the popover is currently being shown\n   */\n  get isOpen() {\n    if (this._showInline) {\n      return this._isInlineOpen;\n    }\n    return this._dropdown.isShown;\n  }\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  get _showInline() {\n    return !this.container;\n  }\n  constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._viewContainerRef = _viewContainerRef;\n    this._cis = _cis;\n    this._state = _state;\n    this._config = _config;\n    /**\n     * This attribute indicates that the dropdown should be opened upwards\n     */\n    this.dropup = false;\n    // todo: move to component loader\n    this._isInlineOpen = false;\n    this._isDisabled = false;\n    this._subscriptions = [];\n    this._isInited = false;\n    // set initial dropdown state from config\n    this._state.autoClose = this._config.autoClose;\n    this._state.insideClick = this._config.insideClick;\n    this._state.isAnimated = this._config.isAnimated;\n    this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n    this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n    // create dropdown component loader\n    this._dropdown = this._cis.createLoader(this._elementRef, this._viewContainerRef, this._renderer).provide({\n      provide: BsDropdownState,\n      useValue: this._state\n    });\n    this.onShown = this._dropdown.onShown;\n    this.onHidden = this._dropdown.onHidden;\n    this.isOpenChange = this._state.isOpenChange;\n  }\n  ngOnInit() {\n    // fix: seems there are an issue with `routerLinkActive`\n    // which result in duplicated call ngOnInit without call to ngOnDestroy\n    // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n    if (this._isInited) {\n      return;\n    }\n    this._isInited = true;\n    // attach DOM listeners\n    this._dropdown.listen({\n      // because of dropdown inline mode\n      outsideClick: false,\n      triggers: this.triggers,\n      show: () => this.show()\n    });\n    // toggle visibility on toggle element click\n    this._subscriptions.push(this._state.toggleClick.subscribe(value => this.toggle(value)));\n    // hide dropdown if set disabled while opened\n    this._subscriptions.push(this._state.isDisabledChange.pipe(filter(value => value)).subscribe(( /*value: boolean*/) => this.hide()));\n  }\n  /**\n   * Opens an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  show() {\n    if (this.isOpen || this.isDisabled) {\n      return;\n    }\n    if (this._showInline) {\n      if (!this._inlinedMenu) {\n        this._state.dropdownMenu.then(dropdownMenu => {\n          this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n          this._inlinedMenu = this._dropdown._inlineViewRef;\n          this.addBs4Polyfills();\n          if (this._inlinedMenu) {\n            this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n          }\n          this.playAnimation();\n        })\n        // swallow errors\n        .catch();\n      }\n      this.addBs4Polyfills();\n      this._isInlineOpen = true;\n      this.onShown.emit(true);\n      this._state.isOpenChange.emit(true);\n      this.playAnimation();\n      return;\n    }\n    this._state.dropdownMenu.then(dropdownMenu => {\n      // check direction in which dropdown should be opened\n      const _dropup = this.dropup || typeof this.dropup !== 'undefined' && this.dropup;\n      this._state.direction = _dropup ? 'up' : 'down';\n      const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n      // show dropdown\n      this._dropdown.attach(BsDropdownContainerComponent).to(this.container).position({\n        attachment: _placement\n      }).show({\n        content: dropdownMenu.templateRef,\n        placement: _placement\n      });\n      this._state.isOpenChange.emit(true);\n    })\n    // swallow error\n    .catch();\n  }\n  /**\n   * Closes an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n  hide() {\n    if (!this.isOpen) {\n      return;\n    }\n    if (this._showInline) {\n      this.removeShowClass();\n      this.removeDropupStyles();\n      this._isInlineOpen = false;\n      this.onHidden.emit(true);\n    } else {\n      this._dropdown.hide();\n    }\n    this._state.isOpenChange.emit(false);\n  }\n  /**\n   * Toggles an element’s popover. This is considered a “manual” triggering of\n   * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n   * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n   */\n  toggle(value) {\n    if (this.isOpen || !value) {\n      return this.hide();\n    }\n    return this.show();\n  }\n  /** @internal */\n  _contains(event) {\n    // todo: valorkin fix typings\n    return this._elementRef.nativeElement.contains(event.target) || this._dropdown.instance && this._dropdown.instance._contains(event.target);\n  }\n  navigationClick(event) {\n    const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n    if (!ref) {\n      return;\n    }\n    const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n    const allRef = ref.querySelectorAll('.dropdown-item');\n    switch (event.keyCode) {\n      case 38:\n        if (this._state.counts > 0) {\n          allRef[--this._state.counts].focus();\n        }\n        break;\n      case 40:\n        if (this._state.counts + 1 < allRef.length) {\n          if (firstActive.classList !== allRef[this._state.counts].classList) {\n            allRef[this._state.counts].focus();\n          } else {\n            allRef[++this._state.counts].focus();\n          }\n        }\n        break;\n      default:\n    }\n    event.preventDefault();\n  }\n  ngOnDestroy() {\n    // clean up subscriptions and destroy dropdown\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n    this._dropdown.dispose();\n  }\n  addBs4Polyfills() {\n    this.addShowClass();\n    this.checkRightAlignment();\n    this.addDropupStyles();\n  }\n  playAnimation() {\n    if (this._state.isAnimated && this._inlinedMenu) {\n      setTimeout(() => {\n        if (this._inlinedMenu) {\n          this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n        }\n      });\n    }\n  }\n  addShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  removeShowClass() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n    }\n  }\n  checkRightAlignment() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n    }\n  }\n  addDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      // a little hack to not break support of bootstrap 4 beta\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n      this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n    }\n  }\n  removeDropupStyles() {\n    if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n      this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n    }\n  }\n  static #_ = this.ɵfac = function BsDropdownDirective_Factory(t) {\n    return new (t || BsDropdownDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(BsDropdownConfig), i0.ɵɵdirectiveInject(i2.AnimationBuilder));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BsDropdownDirective,\n    selectors: [[\"\", \"bsDropdown\", \"\"], [\"\", \"dropdown\", \"\"]],\n    hostVars: 6,\n    hostBindings: function BsDropdownDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown.arrowDown\", function BsDropdownDirective_keydown_arrowDown_HostBindingHandler($event) {\n          return ctx.navigationClick($event);\n        })(\"keydown.arrowUp\", function BsDropdownDirective_keydown_arrowUp_HostBindingHandler($event) {\n          return ctx.navigationClick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"dropup\", ctx.dropup)(\"open\", ctx.isOpen)(\"show\", ctx.isOpen);\n      }\n    },\n    inputs: {\n      placement: \"placement\",\n      triggers: \"triggers\",\n      container: \"container\",\n      dropup: \"dropup\",\n      autoClose: \"autoClose\",\n      isAnimated: \"isAnimated\",\n      insideClick: \"insideClick\",\n      isDisabled: \"isDisabled\",\n      isOpen: \"isOpen\"\n    },\n    outputs: {\n      isOpenChange: \"isOpenChange\",\n      onShown: \"onShown\",\n      onHidden: \"onHidden\"\n    },\n    exportAs: [\"bs-dropdown\"],\n    features: [i0.ɵɵProvidersFeature([BsDropdownState])]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdown], [dropdown]',\n      exportAs: 'bs-dropdown',\n      providers: [BsDropdownState],\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[class.dropup]': 'dropup',\n        '[class.open]': 'isOpen',\n        '[class.show]': 'isOpen'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i1.ComponentLoaderFactory\n    }, {\n      type: BsDropdownState\n    }, {\n      type: BsDropdownConfig\n    }, {\n      type: i2.AnimationBuilder\n    }];\n  }, {\n    placement: [{\n      type: Input\n    }],\n    triggers: [{\n      type: Input\n    }],\n    container: [{\n      type: Input\n    }],\n    dropup: [{\n      type: Input\n    }],\n    autoClose: [{\n      type: Input\n    }],\n    isAnimated: [{\n      type: Input\n    }],\n    insideClick: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    isOpenChange: [{\n      type: Output\n    }],\n    onShown: [{\n      type: Output\n    }],\n    onHidden: [{\n      type: Output\n    }],\n    navigationClick: [{\n      type: HostListener,\n      args: ['keydown.arrowDown', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['keydown.arrowUp', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownMenuDirective {\n  constructor(_state, _viewContainer, _templateRef) {\n    _state.resolveDropdownMenu({\n      templateRef: _templateRef,\n      viewContainer: _viewContainer\n    });\n  }\n  static #_ = this.ɵfac = function BsDropdownMenuDirective_Factory(t) {\n    return new (t || BsDropdownMenuDirective)(i0.ɵɵdirectiveInject(BsDropdownState), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BsDropdownMenuDirective,\n    selectors: [[\"\", \"bsDropdownMenu\", \"\"], [\"\", \"dropdownMenu\", \"\"]],\n    exportAs: [\"bs-dropdown-menu\"]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownMenu],[dropdownMenu]',\n      exportAs: 'bs-dropdown-menu'\n    }]\n  }], function () {\n    return [{\n      type: BsDropdownState\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\nclass BsDropdownToggleDirective {\n  constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dropdown = _dropdown;\n    this._element = _element;\n    this._renderer = _renderer;\n    this._state = _state;\n    this.isOpen = false;\n    this._subscriptions = [];\n    // sync is open value with state\n    this._subscriptions.push(this._state.isOpenChange.subscribe(value => {\n      this.isOpen = value;\n      if (value) {\n        this._documentClickListener = this._renderer.listen('document', 'click', event => {\n          if (this._state.autoClose && event.button !== 2 && !this._element.nativeElement.contains(event.target) && !(this._state.insideClick && this._dropdown._contains(event))) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n        this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n          if (this._state.autoClose) {\n            this._state.toggleClick.emit(false);\n            this._changeDetectorRef.detectChanges();\n          }\n        });\n      } else {\n        this._documentClickListener && this._documentClickListener();\n        this._escKeyUpListener && this._escKeyUpListener();\n      }\n    }));\n    // populate disabled state\n    this._subscriptions.push(this._state.isDisabledChange.subscribe(value => this.isDisabled = value || void 0));\n  }\n  onClick(event) {\n    if (this._state.stopOnClickPropagation) {\n      event.stopPropagation();\n    }\n    if (this.isDisabled) {\n      return;\n    }\n    this._state.toggleClick.emit(true);\n  }\n  ngOnDestroy() {\n    if (this._documentClickListener) {\n      this._documentClickListener();\n    }\n    if (this._escKeyUpListener) {\n      this._escKeyUpListener();\n    }\n    for (const sub of this._subscriptions) {\n      sub.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function BsDropdownToggleDirective_Factory(t) {\n    return new (t || BsDropdownToggleDirective)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(BsDropdownDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(BsDropdownState));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BsDropdownToggleDirective,\n    selectors: [[\"\", \"bsDropdownToggle\", \"\"], [\"\", \"dropdownToggle\", \"\"]],\n    hostVars: 3,\n    hostBindings: function BsDropdownToggleDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function BsDropdownToggleDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-haspopup\", true)(\"disabled\", ctx.isDisabled)(\"aria-expanded\", ctx.isOpen);\n      }\n    },\n    exportAs: [\"bs-dropdown-toggle\"]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownToggleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[bsDropdownToggle],[dropdownToggle]',\n      exportAs: 'bs-dropdown-toggle',\n      // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n      host: {\n        '[attr.aria-haspopup]': 'true'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: BsDropdownDirective\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: BsDropdownState\n    }];\n  }, {\n    isDisabled: [{\n      type: HostBinding,\n      args: ['attr.disabled']\n    }],\n    isOpen: [{\n      type: HostBinding,\n      args: ['attr.aria-expanded']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass BsDropdownModule {\n  static forRoot() {\n    return {\n      ngModule: BsDropdownModule,\n      providers: [ComponentLoaderFactory, PositioningService, BsDropdownState]\n    };\n  }\n  static #_ = this.ɵfac = function BsDropdownModule_Factory(t) {\n    return new (t || BsDropdownModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BsDropdownModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BsDropdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownContainerComponent, BsDropdownDirective],\n      exports: [BsDropdownMenuDirective, BsDropdownToggleDirective, BsDropdownDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Component", "ChangeDetectionStrategy", "Directive", "Input", "Output", "HostListener", "HostBinding", "NgModule", "filter", "i1", "ComponentLoaderFactory", "i2", "style", "animate", "i3", "CommonModule", "PositioningService", "_c0", "a0", "dropdown", "_c1", "BsDropdownConfig", "constructor", "autoClose", "insideClick", "isAnimated", "stopOnClickPropagation", "_", "ɵfac", "BsDropdownConfig_Factory", "t", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "BsDropdownState", "direction", "isOpenChange", "isDisabledChange", "toggleClick", "counts", "dropdownMenu", "Promise", "resolve", "resolveDropdownMenu", "BsDropdownState_Factory", "DROPDOWN_ANIMATION_TIMING", "dropdownAnimation", "height", "overflow", "BsDropdownContainerComponent", "_state", "cd", "_renderer", "_element", "_builder", "isOpen", "_factoryDropDownAnimation", "build", "_subscription", "subscribe", "value", "nativeElement", "querySelector", "addClass", "classList", "contains", "setStyle", "create", "play", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_contains", "el", "ngOnDestroy", "unsubscribe", "BsDropdownContainerComponent_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "Renderer2", "ElementRef", "AnimationBuilder", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "ngContentSelectors", "decls", "vars", "consts", "template", "BsDropdownContainerComponent_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵclassProp", "ɵɵproperty", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "encapsulation", "changeDetection", "selector", "OnPush", "host", "BsDropdownDirective", "isDisabled", "_isDisabled", "emit", "hide", "_showInline", "_isInlineOpen", "_dropdown", "isShown", "show", "container", "_elementRef", "_viewContainerRef", "_cis", "_config", "dropup", "_subscriptions", "_isInited", "createLoader", "provide", "useValue", "onShown", "onHidden", "ngOnInit", "listen", "outsideClick", "triggers", "push", "toggle", "pipe", "_inlinedMenu", "then", "attachInline", "viewContainer", "templateRef", "_inlineViewRef", "addBs4Polyfills", "rootNodes", "parentNode", "playAnimation", "catch", "_dropup", "_placement", "placement", "attach", "to", "position", "attachment", "content", "removeShowClass", "removeDropupStyles", "event", "target", "instance", "navigationClick", "ref", "firstActive", "ownerDocument", "activeElement", "allRef", "querySelectorAll", "keyCode", "focus", "length", "preventDefault", "sub", "dispose", "addShowClass", "checkRightAlignment", "addDropupStyles", "setTimeout", "removeClass", "isRightAligned", "removeStyle", "BsDropdownDirective_Factory", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "BsDropdownDirective_HostBindings", "ɵɵlistener", "BsDropdownDirective_keydown_arrowDown_HostBindingHandler", "$event", "BsDropdownDirective_keydown_arrowUp_HostBindingHandler", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "providers", "BsDropdownMenuDirective", "_viewContainer", "_templateRef", "BsDropdownMenuDirective_Factory", "TemplateRef", "BsDropdownToggleDirective", "_changeDetectorRef", "_documentClickListener", "button", "_escKeyUpListener", "onClick", "stopPropagation", "BsDropdownToggleDirective_Factory", "BsDropdownToggleDirective_HostBindings", "BsDropdownToggleDirective_click_HostBindingHandler", "ɵɵattribute", "BsDropdownModule", "forRoot", "ngModule", "BsDropdownModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ngx-bootstrap/dropdown/fesm2022/ngx-bootstrap-dropdown.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ChangeDetectionStrategy, Directive, Input, Output, HostListener, HostBinding, NgModule } from '@angular/core';\nimport { filter } from 'rxjs/operators';\nimport * as i1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\nimport * as i2 from '@angular/animations';\nimport { style, animate } from '@angular/animations';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PositioningService } from 'ngx-bootstrap/positioning';\n\n/** Default dropdown configuration */\nclass BsDropdownConfig {\n    constructor() {\n        /** default dropdown auto closing behavior */\n        this.autoClose = true;\n        /** default dropdown auto closing behavior */\n        this.insideClick = false;\n        /** turn on/off animation */\n        this.isAnimated = false;\n        /** value true of stopOnClickPropagation allows event stopPropagation*/\n        this.stopOnClickPropagation = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownConfig, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass BsDropdownState {\n    constructor() {\n        this.direction = 'down';\n        this.autoClose = true;\n        this.insideClick = false;\n        this.isAnimated = false;\n        this.stopOnClickPropagation = false;\n        this.isOpenChange = new EventEmitter();\n        this.isDisabledChange = new EventEmitter();\n        this.toggleClick = new EventEmitter();\n        this.counts = 0;\n        this.dropdownMenu = new Promise(resolve => {\n            this.resolveDropdownMenu = resolve;\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownState, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownState, providedIn: 'platform' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownState, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform' }]\n        }], ctorParameters: function () { return []; } });\n\nconst DROPDOWN_ANIMATION_TIMING = '220ms cubic-bezier(0, 0, 0.2, 1)';\nconst dropdownAnimation = [\n    style({ height: 0, overflow: 'hidden' }),\n    animate(DROPDOWN_ANIMATION_TIMING, style({ height: '*', overflow: 'hidden' }))\n];\n\n// todo: revert ngClass to [class] when false positive angular-cli issue is fixed\n//          [class.dropdown]=\"direction === 'down'\"-->\nclass BsDropdownContainerComponent {\n    get direction() {\n        return this._state.direction;\n    }\n    constructor(_state, cd, _renderer, _element, _builder) {\n        this._state = _state;\n        this.cd = cd;\n        this._renderer = _renderer;\n        this._element = _element;\n        this.isOpen = false;\n        this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n        this._subscription = _state.isOpenChange.subscribe((value) => {\n            this.isOpen = value;\n            const dropdown = this._element.nativeElement.querySelector('.dropdown-menu');\n            this._renderer.addClass(this._element.nativeElement.querySelector('div'), 'open');\n            if (dropdown) {\n                this._renderer.addClass(dropdown, 'show');\n                if (dropdown.classList.contains('dropdown-menu-right') || dropdown.classList.contains('dropdown-menu-end')) {\n                    this._renderer.setStyle(dropdown, 'left', 'auto');\n                    this._renderer.setStyle(dropdown, 'right', '0');\n                }\n                if (this.direction === 'up') {\n                    this._renderer.setStyle(dropdown, 'top', 'auto');\n                    this._renderer.setStyle(dropdown, 'transform', 'translateY(-101%)');\n                }\n            }\n            if (dropdown && this._state.isAnimated) {\n                this._factoryDropDownAnimation.create(dropdown)\n                    .play();\n            }\n            this.cd.markForCheck();\n            this.cd.detectChanges();\n        });\n    }\n    /** @internal */\n    _contains(el) {\n        return this._element.nativeElement.contains(el);\n    }\n    ngOnDestroy() {\n        this._subscription.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownContainerComponent, deps: [{ token: BsDropdownState }, { token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i2.AnimationBuilder }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.4\", type: BsDropdownContainerComponent, selector: \"bs-dropdown-container\", host: { styleAttribute: \"display:block;position: absolute;z-index: 1040\" }, ngImport: i0, template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: i3.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownContainerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'bs-dropdown-container',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        style: 'display:block;position: absolute;z-index: 1040'\n                    },\n                    template: `\n    <div [class.dropup]=\"direction === 'up'\"\n         [ngClass]=\"{dropdown: direction === 'down'}\"\n         [class.show]=\"isOpen\"\n         [class.open]=\"isOpen\"><ng-content></ng-content>\n    </div>\n  `\n                }]\n        }], ctorParameters: function () { return [{ type: BsDropdownState }, { type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i2.AnimationBuilder }]; } });\n\nclass BsDropdownDirective {\n    /**\n     * Indicates that dropdown will be closed on item or document click,\n     * and after pressing ESC\n     */\n    set autoClose(value) {\n        this._state.autoClose = value;\n    }\n    get autoClose() {\n        return this._state.autoClose;\n    }\n    /**\n     * Indicates that dropdown will be animated\n     */\n    set isAnimated(value) {\n        this._state.isAnimated = value;\n    }\n    get isAnimated() {\n        return this._state.isAnimated;\n    }\n    /**\n     * This attribute indicates that the dropdown shouldn't close on inside click when autoClose is set to true\n     */\n    set insideClick(value) {\n        this._state.insideClick = value;\n    }\n    get insideClick() {\n        return this._state.insideClick;\n    }\n    /**\n     * Disables dropdown toggle and hides dropdown menu if opened\n     */\n    set isDisabled(value) {\n        this._isDisabled = value;\n        this._state.isDisabledChange.emit(value);\n        if (value) {\n            this.hide();\n        }\n    }\n    get isDisabled() {\n        return this._isDisabled;\n    }\n    /**\n     * Returns whether or not the popover is currently being shown\n     */\n    get isOpen() {\n        if (this._showInline) {\n            return this._isInlineOpen;\n        }\n        return this._dropdown.isShown;\n    }\n    set isOpen(value) {\n        if (value) {\n            this.show();\n        }\n        else {\n            this.hide();\n        }\n    }\n    get _showInline() {\n        return !this.container;\n    }\n    constructor(_elementRef, _renderer, _viewContainerRef, _cis, _state, _config, _builder) {\n        this._elementRef = _elementRef;\n        this._renderer = _renderer;\n        this._viewContainerRef = _viewContainerRef;\n        this._cis = _cis;\n        this._state = _state;\n        this._config = _config;\n        /**\n         * This attribute indicates that the dropdown should be opened upwards\n         */\n        this.dropup = false;\n        // todo: move to component loader\n        this._isInlineOpen = false;\n        this._isDisabled = false;\n        this._subscriptions = [];\n        this._isInited = false;\n        // set initial dropdown state from config\n        this._state.autoClose = this._config.autoClose;\n        this._state.insideClick = this._config.insideClick;\n        this._state.isAnimated = this._config.isAnimated;\n        this._state.stopOnClickPropagation = this._config.stopOnClickPropagation;\n        this._factoryDropDownAnimation = _builder.build(dropdownAnimation);\n        // create dropdown component loader\n        this._dropdown = this._cis\n            .createLoader(this._elementRef, this._viewContainerRef, this._renderer)\n            .provide({ provide: BsDropdownState, useValue: this._state });\n        this.onShown = this._dropdown.onShown;\n        this.onHidden = this._dropdown.onHidden;\n        this.isOpenChange = this._state.isOpenChange;\n    }\n    ngOnInit() {\n        // fix: seems there are an issue with `routerLinkActive`\n        // which result in duplicated call ngOnInit without call to ngOnDestroy\n        // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n        if (this._isInited) {\n            return;\n        }\n        this._isInited = true;\n        // attach DOM listeners\n        this._dropdown.listen({\n            // because of dropdown inline mode\n            outsideClick: false,\n            triggers: this.triggers,\n            show: () => this.show()\n        });\n        // toggle visibility on toggle element click\n        this._subscriptions.push(this._state.toggleClick.subscribe((value) => this.toggle(value)));\n        // hide dropdown if set disabled while opened\n        this._subscriptions.push(this._state.isDisabledChange\n            .pipe(filter((value) => value))\n            .subscribe(( /*value: boolean*/) => this.hide()));\n    }\n    /**\n     * Opens an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    show() {\n        if (this.isOpen || this.isDisabled) {\n            return;\n        }\n        if (this._showInline) {\n            if (!this._inlinedMenu) {\n                this._state.dropdownMenu.then((dropdownMenu) => {\n                    this._dropdown.attachInline(dropdownMenu.viewContainer, dropdownMenu.templateRef);\n                    this._inlinedMenu = this._dropdown._inlineViewRef;\n                    this.addBs4Polyfills();\n                    if (this._inlinedMenu) {\n                        this._renderer.addClass(this._inlinedMenu.rootNodes[0].parentNode, 'open');\n                    }\n                    this.playAnimation();\n                })\n                    // swallow errors\n                    .catch();\n            }\n            this.addBs4Polyfills();\n            this._isInlineOpen = true;\n            this.onShown.emit(true);\n            this._state.isOpenChange.emit(true);\n            this.playAnimation();\n            return;\n        }\n        this._state.dropdownMenu.then(dropdownMenu => {\n            // check direction in which dropdown should be opened\n            const _dropup = this.dropup ||\n                (typeof this.dropup !== 'undefined' && this.dropup);\n            this._state.direction = _dropup ? 'up' : 'down';\n            const _placement = this.placement || (_dropup ? 'top start' : 'bottom start');\n            // show dropdown\n            this._dropdown\n                .attach(BsDropdownContainerComponent)\n                .to(this.container)\n                .position({ attachment: _placement })\n                .show({\n                content: dropdownMenu.templateRef,\n                placement: _placement\n            });\n            this._state.isOpenChange.emit(true);\n        })\n            // swallow error\n            .catch();\n    }\n    /**\n     * Closes an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    hide() {\n        if (!this.isOpen) {\n            return;\n        }\n        if (this._showInline) {\n            this.removeShowClass();\n            this.removeDropupStyles();\n            this._isInlineOpen = false;\n            this.onHidden.emit(true);\n        }\n        else {\n            this._dropdown.hide();\n        }\n        this._state.isOpenChange.emit(false);\n    }\n    /**\n     * Toggles an element’s popover. This is considered a “manual” triggering of\n     * the popover. With parameter <code>true</code> allows toggling, with parameter <code>false</code>\n     * only hides opened dropdown. Parameter usage will be removed in ngx-bootstrap v3\n     */\n    toggle(value) {\n        if (this.isOpen || !value) {\n            return this.hide();\n        }\n        return this.show();\n    }\n    /** @internal */\n    _contains(event) {\n        // todo: valorkin fix typings\n        return this._elementRef.nativeElement.contains(event.target) ||\n            (this._dropdown.instance && this._dropdown.instance._contains(event.target));\n    }\n    navigationClick(event) {\n        const ref = this._elementRef.nativeElement.querySelector('.dropdown-menu');\n        if (!ref) {\n            return;\n        }\n        const firstActive = this._elementRef.nativeElement.ownerDocument.activeElement;\n        const allRef = ref.querySelectorAll('.dropdown-item');\n        switch (event.keyCode) {\n            case 38:\n                if (this._state.counts > 0) {\n                    allRef[--this._state.counts].focus();\n                }\n                break;\n            case 40:\n                if (this._state.counts + 1 < allRef.length) {\n                    if (firstActive.classList !== allRef[this._state.counts].classList) {\n                        allRef[this._state.counts].focus();\n                    }\n                    else {\n                        allRef[++this._state.counts].focus();\n                    }\n                }\n                break;\n            default:\n        }\n        event.preventDefault();\n    }\n    ngOnDestroy() {\n        // clean up subscriptions and destroy dropdown\n        for (const sub of this._subscriptions) {\n            sub.unsubscribe();\n        }\n        this._dropdown.dispose();\n    }\n    addBs4Polyfills() {\n        this.addShowClass();\n        this.checkRightAlignment();\n        this.addDropupStyles();\n    }\n    playAnimation() {\n        if (this._state.isAnimated && this._inlinedMenu) {\n            setTimeout(() => {\n                if (this._inlinedMenu) {\n                    this._factoryDropDownAnimation.create(this._inlinedMenu.rootNodes[0]).play();\n                }\n            });\n        }\n    }\n    addShowClass() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.addClass(this._inlinedMenu.rootNodes[0], 'show');\n        }\n    }\n    removeShowClass() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.removeClass(this._inlinedMenu.rootNodes[0], 'show');\n        }\n    }\n    checkRightAlignment() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            const isRightAligned = this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-right') || this._inlinedMenu.rootNodes[0].classList.contains('dropdown-menu-end');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'left', isRightAligned ? 'auto' : '0');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'right', isRightAligned ? '0' : 'auto');\n        }\n    }\n    addDropupStyles() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            // a little hack to not break support of bootstrap 4 beta\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'top', this.dropup ? 'auto' : '100%');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'transform', this.dropup ? 'translateY(-101%)' : 'translateY(0)');\n            this._renderer.setStyle(this._inlinedMenu.rootNodes[0], 'bottom', 'auto');\n        }\n    }\n    removeDropupStyles() {\n        if (this._inlinedMenu && this._inlinedMenu.rootNodes[0]) {\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'top');\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'transform');\n            this._renderer.removeStyle(this._inlinedMenu.rootNodes[0], 'bottom');\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownDirective, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i1.ComponentLoaderFactory }, { token: BsDropdownState }, { token: BsDropdownConfig }, { token: i2.AnimationBuilder }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.4\", type: BsDropdownDirective, selector: \"[bsDropdown], [dropdown]\", inputs: { placement: \"placement\", triggers: \"triggers\", container: \"container\", dropup: \"dropup\", autoClose: \"autoClose\", isAnimated: \"isAnimated\", insideClick: \"insideClick\", isDisabled: \"isDisabled\", isOpen: \"isOpen\" }, outputs: { isOpenChange: \"isOpenChange\", onShown: \"onShown\", onHidden: \"onHidden\" }, host: { listeners: { \"keydown.arrowDown\": \"navigationClick($event)\", \"keydown.arrowUp\": \"navigationClick($event)\" }, properties: { \"class.dropup\": \"dropup\", \"class.open\": \"isOpen\", \"class.show\": \"isOpen\" } }, providers: [BsDropdownState], exportAs: [\"bs-dropdown\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdown], [dropdown]',\n                    exportAs: 'bs-dropdown',\n                    providers: [BsDropdownState],\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        '[class.dropup]': 'dropup',\n                        '[class.open]': 'isOpen',\n                        '[class.show]': 'isOpen'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i1.ComponentLoaderFactory }, { type: BsDropdownState }, { type: BsDropdownConfig }, { type: i2.AnimationBuilder }]; }, propDecorators: { placement: [{\n                type: Input\n            }], triggers: [{\n                type: Input\n            }], container: [{\n                type: Input\n            }], dropup: [{\n                type: Input\n            }], autoClose: [{\n                type: Input\n            }], isAnimated: [{\n                type: Input\n            }], insideClick: [{\n                type: Input\n            }], isDisabled: [{\n                type: Input\n            }], isOpen: [{\n                type: Input\n            }], isOpenChange: [{\n                type: Output\n            }], onShown: [{\n                type: Output\n            }], onHidden: [{\n                type: Output\n            }], navigationClick: [{\n                type: HostListener,\n                args: ['keydown.arrowDown', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['keydown.arrowUp', ['$event']]\n            }] } });\n\nclass BsDropdownMenuDirective {\n    constructor(_state, _viewContainer, _templateRef) {\n        _state.resolveDropdownMenu({\n            templateRef: _templateRef,\n            viewContainer: _viewContainer\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownMenuDirective, deps: [{ token: BsDropdownState }, { token: i0.ViewContainerRef }, { token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.4\", type: BsDropdownMenuDirective, selector: \"[bsDropdownMenu],[dropdownMenu]\", exportAs: [\"bs-dropdown-menu\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownMenuDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdownMenu],[dropdownMenu]',\n                    exportAs: 'bs-dropdown-menu'\n                }]\n        }], ctorParameters: function () { return [{ type: BsDropdownState }, { type: i0.ViewContainerRef }, { type: i0.TemplateRef }]; } });\n\nclass BsDropdownToggleDirective {\n    constructor(_changeDetectorRef, _dropdown, _element, _renderer, _state) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dropdown = _dropdown;\n        this._element = _element;\n        this._renderer = _renderer;\n        this._state = _state;\n        this.isOpen = false;\n        this._subscriptions = [];\n        // sync is open value with state\n        this._subscriptions.push(this._state.isOpenChange.subscribe((value) => {\n            this.isOpen = value;\n            if (value) {\n                this._documentClickListener = this._renderer.listen('document', 'click', (event) => {\n                    if (this._state.autoClose && event.button !== 2 &&\n                        !this._element.nativeElement.contains(event.target) &&\n                        !(this._state.insideClick && this._dropdown._contains(event))) {\n                        this._state.toggleClick.emit(false);\n                        this._changeDetectorRef.detectChanges();\n                    }\n                });\n                this._escKeyUpListener = this._renderer.listen(this._element.nativeElement, 'keyup.esc', () => {\n                    if (this._state.autoClose) {\n                        this._state.toggleClick.emit(false);\n                        this._changeDetectorRef.detectChanges();\n                    }\n                });\n            }\n            else {\n                this._documentClickListener && this._documentClickListener();\n                this._escKeyUpListener && this._escKeyUpListener();\n            }\n        }));\n        // populate disabled state\n        this._subscriptions.push(this._state.isDisabledChange\n            .subscribe((value) => this.isDisabled = value || void 0));\n    }\n    onClick(event) {\n        if (this._state.stopOnClickPropagation) {\n            event.stopPropagation();\n        }\n        if (this.isDisabled) {\n            return;\n        }\n        this._state.toggleClick.emit(true);\n    }\n    ngOnDestroy() {\n        if (this._documentClickListener) {\n            this._documentClickListener();\n        }\n        if (this._escKeyUpListener) {\n            this._escKeyUpListener();\n        }\n        for (const sub of this._subscriptions) {\n            sub.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownToggleDirective, deps: [{ token: i0.ChangeDetectorRef }, { token: BsDropdownDirective }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: BsDropdownState }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.4\", type: BsDropdownToggleDirective, selector: \"[bsDropdownToggle],[dropdownToggle]\", host: { listeners: { \"click\": \"onClick($event)\" }, properties: { \"attr.aria-haspopup\": \"true\", \"attr.disabled\": \"this.isDisabled\", \"attr.aria-expanded\": \"this.isOpen\" } }, exportAs: [\"bs-dropdown-toggle\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownToggleDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[bsDropdownToggle],[dropdownToggle]',\n                    exportAs: 'bs-dropdown-toggle',\n                    // eslint-disable-next-line @angular-eslint/no-host-metadata-property\n                    host: {\n                        '[attr.aria-haspopup]': 'true'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: BsDropdownDirective }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: BsDropdownState }]; }, propDecorators: { isDisabled: [{\n                type: HostBinding,\n                args: ['attr.disabled']\n            }], isOpen: [{\n                type: HostBinding,\n                args: ['attr.aria-expanded']\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\nclass BsDropdownModule {\n    static forRoot() {\n        return {\n            ngModule: BsDropdownModule,\n            providers: [\n                ComponentLoaderFactory,\n                PositioningService,\n                BsDropdownState\n            ]\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownModule, declarations: [BsDropdownMenuDirective,\n            BsDropdownToggleDirective,\n            BsDropdownContainerComponent,\n            BsDropdownDirective], imports: [CommonModule], exports: [BsDropdownMenuDirective,\n            BsDropdownToggleDirective,\n            BsDropdownDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.4\", ngImport: i0, type: BsDropdownModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [\n                        BsDropdownMenuDirective,\n                        BsDropdownToggleDirective,\n                        BsDropdownContainerComponent,\n                        BsDropdownDirective\n                    ],\n                    exports: [\n                        BsDropdownMenuDirective,\n                        BsDropdownToggleDirective,\n                        BsDropdownDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsDropdownConfig, BsDropdownContainerComponent, BsDropdownDirective, BsDropdownMenuDirective, BsDropdownModule, BsDropdownState, BsDropdownToggleDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AAC3J,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAO,KAAKC,EAAE,MAAM,gCAAgC;AACpD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AACpD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,2BAA2B;;AAE9D;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,QAAA,EAAAD;EAAA;AAAA;AAAA,MAAAE,GAAA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;EACvC;EAAC,QAAAC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFT,gBAAgB;EAAA,CAAoD;EAAA,QAAAU,EAAA,GACrK,IAAI,CAACC,KAAK,kBAD6EnC,EAAE,CAAAoC,kBAAA;IAAAC,KAAA,EACYb,gBAAgB;IAAAc,OAAA,EAAhBd,gBAAgB,CAAAO,IAAA;IAAAQ,UAAA,EAAc;EAAM,EAAG;AACzJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxC,EAAE,CAAAyC,iBAAA,CAGXjB,gBAAgB,EAAc,CAAC;IAC9GkB,IAAI,EAAEzC,UAAU;IAChB0C,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,eAAe,CAAC;EAClBnB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoB,SAAS,GAAG,MAAM;IACvB,IAAI,CAACnB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACiB,YAAY,GAAG,IAAI5C,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC6C,gBAAgB,GAAG,IAAI7C,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC8C,WAAW,GAAG,IAAI9C,YAAY,CAAC,CAAC;IACrC,IAAI,CAAC+C,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;MACvC,IAAI,CAACC,mBAAmB,GAAGD,OAAO;IACtC,CAAC,CAAC;EACN;EAAC,QAAAtB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAuB,wBAAArB,CAAA;IAAA,YAAAA,CAAA,IAAwFW,eAAe;EAAA,CAAoD;EAAA,QAAAV,EAAA,GACpK,IAAI,CAACC,KAAK,kBA1B6EnC,EAAE,CAAAoC,kBAAA;IAAAC,KAAA,EA0BYO,eAAe;IAAAN,OAAA,EAAfM,eAAe,CAAAb,IAAA;IAAAQ,UAAA,EAAc;EAAU,EAAG;AAC5J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5BoGxC,EAAE,CAAAyC,iBAAA,CA4BXG,eAAe,EAAc,CAAC;IAC7GF,IAAI,EAAEzC,UAAU;IAChB0C,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAW,CAAC;EACrC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AAEtD,MAAMgB,yBAAyB,GAAG,kCAAkC;AACpE,MAAMC,iBAAiB,GAAG,CACtBzC,KAAK,CAAC;EAAE0C,MAAM,EAAE,CAAC;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,EACxC1C,OAAO,CAACuC,yBAAyB,EAAExC,KAAK,CAAC;EAAE0C,MAAM,EAAE,GAAG;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,CAAC,CACjF;;AAED;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/B,IAAId,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACe,MAAM,CAACf,SAAS;EAChC;EACApB,WAAWA,CAACmC,MAAM,EAAEC,EAAE,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACnD,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,yBAAyB,GAAGF,QAAQ,CAACG,KAAK,CAACX,iBAAiB,CAAC;IAClE,IAAI,CAACY,aAAa,GAAGR,MAAM,CAACd,YAAY,CAACuB,SAAS,CAAEC,KAAK,IAAK;MAC1D,IAAI,CAACL,MAAM,GAAGK,KAAK;MACnB,MAAMhD,QAAQ,GAAG,IAAI,CAACyC,QAAQ,CAACQ,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC5E,IAAI,CAACV,SAAS,CAACW,QAAQ,CAAC,IAAI,CAACV,QAAQ,CAACQ,aAAa,CAACC,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;MACjF,IAAIlD,QAAQ,EAAE;QACV,IAAI,CAACwC,SAAS,CAACW,QAAQ,CAACnD,QAAQ,EAAE,MAAM,CAAC;QACzC,IAAIA,QAAQ,CAACoD,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAIrD,QAAQ,CAACoD,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UACxG,IAAI,CAACb,SAAS,CAACc,QAAQ,CAACtD,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;UACjD,IAAI,CAACwC,SAAS,CAACc,QAAQ,CAACtD,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAC;QACnD;QACA,IAAI,IAAI,CAACuB,SAAS,KAAK,IAAI,EAAE;UACzB,IAAI,CAACiB,SAAS,CAACc,QAAQ,CAACtD,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;UAChD,IAAI,CAACwC,SAAS,CAACc,QAAQ,CAACtD,QAAQ,EAAE,WAAW,EAAE,mBAAmB,CAAC;QACvE;MACJ;MACA,IAAIA,QAAQ,IAAI,IAAI,CAACsC,MAAM,CAAChC,UAAU,EAAE;QACpC,IAAI,CAACsC,yBAAyB,CAACW,MAAM,CAACvD,QAAQ,CAAC,CAC1CwD,IAAI,CAAC,CAAC;MACf;MACA,IAAI,CAACjB,EAAE,CAACkB,YAAY,CAAC,CAAC;MACtB,IAAI,CAAClB,EAAE,CAACmB,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;EACAC,SAASA,CAACC,EAAE,EAAE;IACV,OAAO,IAAI,CAACnB,QAAQ,CAACQ,aAAa,CAACI,QAAQ,CAACO,EAAE,CAAC;EACnD;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,aAAa,CAACgB,WAAW,CAAC,CAAC;EACpC;EAAC,QAAAtD,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAsD,qCAAApD,CAAA;IAAA,YAAAA,CAAA,IAAwF0B,4BAA4B,EAlFtC3D,EAAE,CAAAsF,iBAAA,CAkFsD1C,eAAe,GAlFvE5C,EAAE,CAAAsF,iBAAA,CAkFkFtF,EAAE,CAACuF,iBAAiB,GAlFxGvF,EAAE,CAAAsF,iBAAA,CAkFmHtF,EAAE,CAACwF,SAAS,GAlFjIxF,EAAE,CAAAsF,iBAAA,CAkF4ItF,EAAE,CAACyF,UAAU,GAlF3JzF,EAAE,CAAAsF,iBAAA,CAkFsKxE,EAAE,CAAC4E,gBAAgB;EAAA,CAA4C;EAAA,QAAAxD,EAAA,GAC9T,IAAI,CAACyD,IAAI,kBAnF8E3F,EAAE,CAAA4F,iBAAA;IAAAlD,IAAA,EAmFJiB,4BAA4B;IAAAkC,SAAA;IAAAC,SAAA;IAAAC,kBAAA,EAAAxE,GAAA;IAAAyE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAnF1BrG,EAAE,CAAAuG,eAAA;QAAFvG,EAAE,CAAAwG,cAAA,YAuFxE,CAAC;QAvFqExG,EAAE,CAAAyG,YAAA,EAuF/C,CAAC;QAvF4CzG,EAAE,CAAA0G,YAAA,CAwF7F,CAAC;MAAA;MAAA,IAAAL,EAAA;QAxF0FrG,EAAE,CAAA2G,WAAA,WAAAL,GAAA,CAAAzD,SAAA,SAoF3D,CAAC,SAAAyD,GAAA,CAAArC,MAAD,CAAC,SAAAqC,GAAA,CAAArC,MAAD,CAAC;QApFwDjE,EAAE,CAAA4G,UAAA,YAAF5G,EAAE,CAAA6G,eAAA,IAAAzF,GAAA,EAAAkF,GAAA,CAAAzD,SAAA,YAqFlD,CAAC;MAAA;IAAA;IAAAiE,YAAA,GAIU7F,EAAE,CAAC8F,OAAO;IAAAC,aAAA;IAAAC,eAAA;EAAA,EAA+G;AACxL;AACA;EAAA,QAAAzE,SAAA,oBAAAA,SAAA,KA3FoGxC,EAAE,CAAAyC,iBAAA,CA2FXkB,4BAA4B,EAAc,CAAC;IAC1HjB,IAAI,EAAEvC,SAAS;IACfwC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,uBAAuB;MACjCD,eAAe,EAAE7G,uBAAuB,CAAC+G,MAAM;MAC/C;MACAC,IAAI,EAAE;QACFrG,KAAK,EAAE;MACX,CAAC;MACDoF,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzD,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAE1C,EAAE,CAACuF;IAAkB,CAAC,EAAE;MAAE7C,IAAI,EAAE1C,EAAE,CAACwF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAE1C,EAAE,CAACyF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAE5B,EAAE,CAAC4E;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;AAE/L,MAAM2B,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;EACI,IAAI3F,SAASA,CAAC4C,KAAK,EAAE;IACjB,IAAI,CAACV,MAAM,CAAClC,SAAS,GAAG4C,KAAK;EACjC;EACA,IAAI5C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACkC,MAAM,CAAClC,SAAS;EAChC;EACA;AACJ;AACA;EACI,IAAIE,UAAUA,CAAC0C,KAAK,EAAE;IAClB,IAAI,CAACV,MAAM,CAAChC,UAAU,GAAG0C,KAAK;EAClC;EACA,IAAI1C,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACgC,MAAM,CAAChC,UAAU;EACjC;EACA;AACJ;AACA;EACI,IAAID,WAAWA,CAAC2C,KAAK,EAAE;IACnB,IAAI,CAACV,MAAM,CAACjC,WAAW,GAAG2C,KAAK;EACnC;EACA,IAAI3C,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACiC,MAAM,CAACjC,WAAW;EAClC;EACA;AACJ;AACA;EACI,IAAI2F,UAAUA,CAAChD,KAAK,EAAE;IAClB,IAAI,CAACiD,WAAW,GAAGjD,KAAK;IACxB,IAAI,CAACV,MAAM,CAACb,gBAAgB,CAACyE,IAAI,CAAClD,KAAK,CAAC;IACxC,IAAIA,KAAK,EAAE;MACP,IAAI,CAACmD,IAAI,CAAC,CAAC;IACf;EACJ;EACA,IAAIH,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA;AACJ;AACA;EACI,IAAItD,MAAMA,CAAA,EAAG;IACT,IAAI,IAAI,CAACyD,WAAW,EAAE;MAClB,OAAO,IAAI,CAACC,aAAa;IAC7B;IACA,OAAO,IAAI,CAACC,SAAS,CAACC,OAAO;EACjC;EACA,IAAI5D,MAAMA,CAACK,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACP,IAAI,CAACwD,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAACL,IAAI,CAAC,CAAC;IACf;EACJ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,IAAI,CAACK,SAAS;EAC1B;EACAtG,WAAWA,CAACuG,WAAW,EAAElE,SAAS,EAAEmE,iBAAiB,EAAEC,IAAI,EAAEtE,MAAM,EAAEuE,OAAO,EAAEnE,QAAQ,EAAE;IACpF,IAAI,CAACgE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAClE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACmE,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACtE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuE,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;IACA,IAAI,CAACT,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACJ,WAAW,GAAG,KAAK;IACxB,IAAI,CAACc,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAAC1E,MAAM,CAAClC,SAAS,GAAG,IAAI,CAACyG,OAAO,CAACzG,SAAS;IAC9C,IAAI,CAACkC,MAAM,CAACjC,WAAW,GAAG,IAAI,CAACwG,OAAO,CAACxG,WAAW;IAClD,IAAI,CAACiC,MAAM,CAAChC,UAAU,GAAG,IAAI,CAACuG,OAAO,CAACvG,UAAU;IAChD,IAAI,CAACgC,MAAM,CAAC/B,sBAAsB,GAAG,IAAI,CAACsG,OAAO,CAACtG,sBAAsB;IACxE,IAAI,CAACqC,yBAAyB,GAAGF,QAAQ,CAACG,KAAK,CAACX,iBAAiB,CAAC;IAClE;IACA,IAAI,CAACoE,SAAS,GAAG,IAAI,CAACM,IAAI,CACrBK,YAAY,CAAC,IAAI,CAACP,WAAW,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACnE,SAAS,CAAC,CACtE0E,OAAO,CAAC;MAAEA,OAAO,EAAE5F,eAAe;MAAE6F,QAAQ,EAAE,IAAI,CAAC7E;IAAO,CAAC,CAAC;IACjE,IAAI,CAAC8E,OAAO,GAAG,IAAI,CAACd,SAAS,CAACc,OAAO;IACrC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACf,SAAS,CAACe,QAAQ;IACvC,IAAI,CAAC7F,YAAY,GAAG,IAAI,CAACc,MAAM,CAACd,YAAY;EAChD;EACA8F,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,IAAI,CAACN,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACV,SAAS,CAACiB,MAAM,CAAC;MAClB;MACAC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBjB,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACA,IAAI,CAAC;IAC1B,CAAC,CAAC;IACF;IACA,IAAI,CAACO,cAAc,CAACW,IAAI,CAAC,IAAI,CAACpF,MAAM,CAACZ,WAAW,CAACqB,SAAS,CAAEC,KAAK,IAAK,IAAI,CAAC2E,MAAM,CAAC3E,KAAK,CAAC,CAAC,CAAC;IAC1F;IACA,IAAI,CAAC+D,cAAc,CAACW,IAAI,CAAC,IAAI,CAACpF,MAAM,CAACb,gBAAgB,CAChDmG,IAAI,CAACvI,MAAM,CAAE2D,KAAK,IAAKA,KAAK,CAAC,CAAC,CAC9BD,SAAS,CAAC,EAAE,uBAAuB,IAAI,CAACoD,IAAI,CAAC,CAAC,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACIK,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC7D,MAAM,IAAI,IAAI,CAACqD,UAAU,EAAE;MAChC;IACJ;IACA,IAAI,IAAI,CAACI,WAAW,EAAE;MAClB,IAAI,CAAC,IAAI,CAACyB,YAAY,EAAE;QACpB,IAAI,CAACvF,MAAM,CAACV,YAAY,CAACkG,IAAI,CAAElG,YAAY,IAAK;UAC5C,IAAI,CAAC0E,SAAS,CAACyB,YAAY,CAACnG,YAAY,CAACoG,aAAa,EAAEpG,YAAY,CAACqG,WAAW,CAAC;UACjF,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACvB,SAAS,CAAC4B,cAAc;UACjD,IAAI,CAACC,eAAe,CAAC,CAAC;UACtB,IAAI,IAAI,CAACN,YAAY,EAAE;YACnB,IAAI,CAACrF,SAAS,CAACW,QAAQ,CAAC,IAAI,CAAC0E,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE,MAAM,CAAC;UAC9E;UACA,IAAI,CAACC,aAAa,CAAC,CAAC;QACxB,CAAC;QACG;QAAA,CACCC,KAAK,CAAC,CAAC;MAChB;MACA,IAAI,CAACJ,eAAe,CAAC,CAAC;MACtB,IAAI,CAAC9B,aAAa,GAAG,IAAI;MACzB,IAAI,CAACe,OAAO,CAAClB,IAAI,CAAC,IAAI,CAAC;MACvB,IAAI,CAAC5D,MAAM,CAACd,YAAY,CAAC0E,IAAI,CAAC,IAAI,CAAC;MACnC,IAAI,CAACoC,aAAa,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAAChG,MAAM,CAACV,YAAY,CAACkG,IAAI,CAAClG,YAAY,IAAI;MAC1C;MACA,MAAM4G,OAAO,GAAG,IAAI,CAAC1B,MAAM,IACtB,OAAO,IAAI,CAACA,MAAM,KAAK,WAAW,IAAI,IAAI,CAACA,MAAO;MACvD,IAAI,CAACxE,MAAM,CAACf,SAAS,GAAGiH,OAAO,GAAG,IAAI,GAAG,MAAM;MAC/C,MAAMC,UAAU,GAAG,IAAI,CAACC,SAAS,KAAKF,OAAO,GAAG,WAAW,GAAG,cAAc,CAAC;MAC7E;MACA,IAAI,CAAClC,SAAS,CACTqC,MAAM,CAACtG,4BAA4B,CAAC,CACpCuG,EAAE,CAAC,IAAI,CAACnC,SAAS,CAAC,CAClBoC,QAAQ,CAAC;QAAEC,UAAU,EAAEL;MAAW,CAAC,CAAC,CACpCjC,IAAI,CAAC;QACNuC,OAAO,EAAEnH,YAAY,CAACqG,WAAW;QACjCS,SAAS,EAAED;MACf,CAAC,CAAC;MACF,IAAI,CAACnG,MAAM,CAACd,YAAY,CAAC0E,IAAI,CAAC,IAAI,CAAC;IACvC,CAAC;IACG;IAAA,CACCqC,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;EACIpC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACxD,MAAM,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACyD,WAAW,EAAE;MAClB,IAAI,CAAC4C,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC5C,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACgB,QAAQ,CAACnB,IAAI,CAAC,IAAI,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACI,SAAS,CAACH,IAAI,CAAC,CAAC;IACzB;IACA,IAAI,CAAC7D,MAAM,CAACd,YAAY,CAAC0E,IAAI,CAAC,KAAK,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIyB,MAAMA,CAAC3E,KAAK,EAAE;IACV,IAAI,IAAI,CAACL,MAAM,IAAI,CAACK,KAAK,EAAE;MACvB,OAAO,IAAI,CAACmD,IAAI,CAAC,CAAC;IACtB;IACA,OAAO,IAAI,CAACK,IAAI,CAAC,CAAC;EACtB;EACA;EACA7C,SAASA,CAACuF,KAAK,EAAE;IACb;IACA,OAAO,IAAI,CAACxC,WAAW,CAACzD,aAAa,CAACI,QAAQ,CAAC6F,KAAK,CAACC,MAAM,CAAC,IACvD,IAAI,CAAC7C,SAAS,CAAC8C,QAAQ,IAAI,IAAI,CAAC9C,SAAS,CAAC8C,QAAQ,CAACzF,SAAS,CAACuF,KAAK,CAACC,MAAM,CAAE;EACpF;EACAE,eAAeA,CAACH,KAAK,EAAE;IACnB,MAAMI,GAAG,GAAG,IAAI,CAAC5C,WAAW,CAACzD,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAC1E,IAAI,CAACoG,GAAG,EAAE;MACN;IACJ;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC7C,WAAW,CAACzD,aAAa,CAACuG,aAAa,CAACC,aAAa;IAC9E,MAAMC,MAAM,GAAGJ,GAAG,CAACK,gBAAgB,CAAC,gBAAgB,CAAC;IACrD,QAAQT,KAAK,CAACU,OAAO;MACjB,KAAK,EAAE;QACH,IAAI,IAAI,CAACtH,MAAM,CAACX,MAAM,GAAG,CAAC,EAAE;UACxB+H,MAAM,CAAC,EAAE,IAAI,CAACpH,MAAM,CAACX,MAAM,CAAC,CAACkI,KAAK,CAAC,CAAC;QACxC;QACA;MACJ,KAAK,EAAE;QACH,IAAI,IAAI,CAACvH,MAAM,CAACX,MAAM,GAAG,CAAC,GAAG+H,MAAM,CAACI,MAAM,EAAE;UACxC,IAAIP,WAAW,CAACnG,SAAS,KAAKsG,MAAM,CAAC,IAAI,CAACpH,MAAM,CAACX,MAAM,CAAC,CAACyB,SAAS,EAAE;YAChEsG,MAAM,CAAC,IAAI,CAACpH,MAAM,CAACX,MAAM,CAAC,CAACkI,KAAK,CAAC,CAAC;UACtC,CAAC,MACI;YACDH,MAAM,CAAC,EAAE,IAAI,CAACpH,MAAM,CAACX,MAAM,CAAC,CAACkI,KAAK,CAAC,CAAC;UACxC;QACJ;QACA;MACJ;IACJ;IACAX,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACAlG,WAAWA,CAAA,EAAG;IACV;IACA,KAAK,MAAMmG,GAAG,IAAI,IAAI,CAACjD,cAAc,EAAE;MACnCiD,GAAG,CAAClG,WAAW,CAAC,CAAC;IACrB;IACA,IAAI,CAACwC,SAAS,CAAC2D,OAAO,CAAC,CAAC;EAC5B;EACA9B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC+B,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACA9B,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAChG,MAAM,CAAChC,UAAU,IAAI,IAAI,CAACuH,YAAY,EAAE;MAC7CwC,UAAU,CAAC,MAAM;QACb,IAAI,IAAI,CAACxC,YAAY,EAAE;UACnB,IAAI,CAACjF,yBAAyB,CAACW,MAAM,CAAC,IAAI,CAACsE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC5E,IAAI,CAAC,CAAC;QAChF;MACJ,CAAC,CAAC;IACN;EACJ;EACA0G,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACrC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC5F,SAAS,CAACW,QAAQ,CAAC,IAAI,CAAC0E,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;IACnE;EACJ;EACAY,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC5F,SAAS,CAAC8H,WAAW,CAAC,IAAI,CAACzC,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC;IACtE;EACJ;EACA+B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACtC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,MAAMmC,cAAc,GAAG,IAAI,CAAC1C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAAChF,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAACwE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,CAAChF,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC;MACzK,IAAI,CAACb,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,EAAEmC,cAAc,GAAG,MAAM,GAAG,GAAG,CAAC;MAC9F,IAAI,CAAC/H,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmC,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;IACnG;EACJ;EACAH,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACvC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD;MACA,IAAI,CAAC5F,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAACtB,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;MAC7F,IAAI,CAACtE,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAACtB,MAAM,GAAG,mBAAmB,GAAG,eAAe,CAAC;MACzH,IAAI,CAACtE,SAAS,CAACc,QAAQ,CAAC,IAAI,CAACuE,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC7E;EACJ;EACAa,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACpB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC5F,SAAS,CAACgI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;MACjE,IAAI,CAAC5F,SAAS,CAACgI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC;MACvE,IAAI,CAAC5F,SAAS,CAACgI,WAAW,CAAC,IAAI,CAAC3C,YAAY,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;IACxE;EACJ;EAAC,QAAA5H,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAgK,4BAAA9J,CAAA;IAAA,YAAAA,CAAA,IAAwFoF,mBAAmB,EArY7BrH,EAAE,CAAAsF,iBAAA,CAqY6CtF,EAAE,CAACyF,UAAU,GArY5DzF,EAAE,CAAAsF,iBAAA,CAqYuEtF,EAAE,CAACwF,SAAS,GArYrFxF,EAAE,CAAAsF,iBAAA,CAqYgGtF,EAAE,CAACgM,gBAAgB,GArYrHhM,EAAE,CAAAsF,iBAAA,CAqYgI1E,EAAE,CAACC,sBAAsB,GArY3Jb,EAAE,CAAAsF,iBAAA,CAqYsK1C,eAAe,GArYvL5C,EAAE,CAAAsF,iBAAA,CAqYkM9D,gBAAgB,GArYpNxB,EAAE,CAAAsF,iBAAA,CAqY+NxE,EAAE,CAAC4E,gBAAgB;EAAA,CAA4C;EAAA,QAAAxD,EAAA,GACvX,IAAI,CAAC+J,IAAI,kBAtY8EjM,EAAE,CAAAkM,iBAAA;IAAAxJ,IAAA,EAsYJ2E,mBAAmB;IAAAxB,SAAA;IAAAsG,QAAA;IAAAC,YAAA,WAAAC,iCAAAhG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAtYjBrG,EAAE,CAAAsM,UAAA,+BAAAC,yDAAAC,MAAA;UAAA,OAsYJlG,GAAA,CAAAqE,eAAA,CAAA6B,MAAsB,CAAC;QAAA,+BAAAC,uDAAAD,MAAA;UAAA,OAAvBlG,GAAA,CAAAqE,eAAA,CAAA6B,MAAsB,CAAC;QAAA;MAAA;MAAA,IAAAnG,EAAA;QAtYrBrG,EAAE,CAAA2G,WAAA,WAAAL,GAAA,CAAA8B,MAAA,UAAA9B,GAAA,CAAArC,MAAA,UAAAqC,GAAA,CAAArC,MAAA;MAAA;IAAA;IAAAyI,MAAA;MAAA1C,SAAA;MAAAjB,QAAA;MAAAhB,SAAA;MAAAK,MAAA;MAAA1G,SAAA;MAAAE,UAAA;MAAAD,WAAA;MAAA2F,UAAA;MAAArD,MAAA;IAAA;IAAA0I,OAAA;MAAA7J,YAAA;MAAA4F,OAAA;MAAAC,QAAA;IAAA;IAAAiE,QAAA;IAAAC,QAAA,GAAF7M,EAAE,CAAA8M,kBAAA,CAsYskB,CAAClK,eAAe,CAAC;EAAA,EAA4C;AACzuB;AACA;EAAA,QAAAJ,SAAA,oBAAAA,SAAA,KAxYoGxC,EAAE,CAAAyC,iBAAA,CAwYX4E,mBAAmB,EAAc,CAAC;IACjH3E,IAAI,EAAErC,SAAS;IACfsC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,0BAA0B;MACpC0F,QAAQ,EAAE,aAAa;MACvBG,SAAS,EAAE,CAACnK,eAAe,CAAC;MAC5B;MACAwE,IAAI,EAAE;QACF,gBAAgB,EAAE,QAAQ;QAC1B,cAAc,EAAE,QAAQ;QACxB,cAAc,EAAE;MACpB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1E,IAAI,EAAE1C,EAAE,CAACyF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAE1C,EAAE,CAACwF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAE1C,EAAE,CAACgM;IAAiB,CAAC,EAAE;MAAEtJ,IAAI,EAAE9B,EAAE,CAACC;IAAuB,CAAC,EAAE;MAAE6B,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAElB;IAAiB,CAAC,EAAE;MAAEkB,IAAI,EAAE5B,EAAE,CAAC4E;IAAiB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEsE,SAAS,EAAE,CAAC;MAC/QtH,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEyI,QAAQ,EAAE,CAAC;MACXrG,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEyH,SAAS,EAAE,CAAC;MACZrF,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAE8H,MAAM,EAAE,CAAC;MACT1F,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEoB,SAAS,EAAE,CAAC;MACZgB,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEsB,UAAU,EAAE,CAAC;MACbc,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEqB,WAAW,EAAE,CAAC;MACde,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEgH,UAAU,EAAE,CAAC;MACb5E,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAE2D,MAAM,EAAE,CAAC;MACTvB,IAAI,EAAEpC;IACV,CAAC,CAAC;IAAEwC,YAAY,EAAE,CAAC;MACfJ,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEmI,OAAO,EAAE,CAAC;MACVhG,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEoI,QAAQ,EAAE,CAAC;MACXjG,IAAI,EAAEnC;IACV,CAAC,CAAC;IAAEoK,eAAe,EAAE,CAAC;MAClBjI,IAAI,EAAElC,YAAY;MAClBmC,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACCD,IAAI,EAAElC,YAAY;MAClBmC,IAAI,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC;IACxC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqK,uBAAuB,CAAC;EAC1BvL,WAAWA,CAACmC,MAAM,EAAEqJ,cAAc,EAAEC,YAAY,EAAE;IAC9CtJ,MAAM,CAACP,mBAAmB,CAAC;MACvBkG,WAAW,EAAE2D,YAAY;MACzB5D,aAAa,EAAE2D;IACnB,CAAC,CAAC;EACN;EAAC,QAAAnL,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAoL,gCAAAlL,CAAA;IAAA,YAAAA,CAAA,IAAwF+K,uBAAuB,EA5bjChN,EAAE,CAAAsF,iBAAA,CA4biD1C,eAAe,GA5blE5C,EAAE,CAAAsF,iBAAA,CA4b6EtF,EAAE,CAACgM,gBAAgB,GA5blGhM,EAAE,CAAAsF,iBAAA,CA4b6GtF,EAAE,CAACoN,WAAW;EAAA,CAA4C;EAAA,QAAAlL,EAAA,GAChQ,IAAI,CAAC+J,IAAI,kBA7b8EjM,EAAE,CAAAkM,iBAAA;IAAAxJ,IAAA,EA6bJsK,uBAAuB;IAAAnH,SAAA;IAAA+G,QAAA;EAAA,EAA8F;AACvN;AACA;EAAA,QAAApK,SAAA,oBAAAA,SAAA,KA/boGxC,EAAE,CAAAyC,iBAAA,CA+bXuK,uBAAuB,EAAc,CAAC;IACrHtK,IAAI,EAAErC,SAAS;IACfsC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,iCAAiC;MAC3C0F,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElK,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAE1C,EAAE,CAACgM;IAAiB,CAAC,EAAE;MAAEtJ,IAAI,EAAE1C,EAAE,CAACoN;IAAY,CAAC,CAAC;EAAE,CAAC;AAAA;AAExI,MAAMC,yBAAyB,CAAC;EAC5B5L,WAAWA,CAAC6L,kBAAkB,EAAE1F,SAAS,EAAE7D,QAAQ,EAAED,SAAS,EAAEF,MAAM,EAAE;IACpE,IAAI,CAAC0J,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC1F,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC7D,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACoE,cAAc,GAAG,EAAE;IACxB;IACA,IAAI,CAACA,cAAc,CAACW,IAAI,CAAC,IAAI,CAACpF,MAAM,CAACd,YAAY,CAACuB,SAAS,CAAEC,KAAK,IAAK;MACnE,IAAI,CAACL,MAAM,GAAGK,KAAK;MACnB,IAAIA,KAAK,EAAE;QACP,IAAI,CAACiJ,sBAAsB,GAAG,IAAI,CAACzJ,SAAS,CAAC+E,MAAM,CAAC,UAAU,EAAE,OAAO,EAAG2B,KAAK,IAAK;UAChF,IAAI,IAAI,CAAC5G,MAAM,CAAClC,SAAS,IAAI8I,KAAK,CAACgD,MAAM,KAAK,CAAC,IAC3C,CAAC,IAAI,CAACzJ,QAAQ,CAACQ,aAAa,CAACI,QAAQ,CAAC6F,KAAK,CAACC,MAAM,CAAC,IACnD,EAAE,IAAI,CAAC7G,MAAM,CAACjC,WAAW,IAAI,IAAI,CAACiG,SAAS,CAAC3C,SAAS,CAACuF,KAAK,CAAC,CAAC,EAAE;YAC/D,IAAI,CAAC5G,MAAM,CAACZ,WAAW,CAACwE,IAAI,CAAC,KAAK,CAAC;YACnC,IAAI,CAAC8F,kBAAkB,CAACtI,aAAa,CAAC,CAAC;UAC3C;QACJ,CAAC,CAAC;QACF,IAAI,CAACyI,iBAAiB,GAAG,IAAI,CAAC3J,SAAS,CAAC+E,MAAM,CAAC,IAAI,CAAC9E,QAAQ,CAACQ,aAAa,EAAE,WAAW,EAAE,MAAM;UAC3F,IAAI,IAAI,CAACX,MAAM,CAAClC,SAAS,EAAE;YACvB,IAAI,CAACkC,MAAM,CAACZ,WAAW,CAACwE,IAAI,CAAC,KAAK,CAAC;YACnC,IAAI,CAAC8F,kBAAkB,CAACtI,aAAa,CAAC,CAAC;UAC3C;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACuI,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,CAAC,CAAC;QAC5D,IAAI,CAACE,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC,CAAC;IACH;IACA,IAAI,CAACpF,cAAc,CAACW,IAAI,CAAC,IAAI,CAACpF,MAAM,CAACb,gBAAgB,CAChDsB,SAAS,CAAEC,KAAK,IAAK,IAAI,CAACgD,UAAU,GAAGhD,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;EACjE;EACAoJ,OAAOA,CAAClD,KAAK,EAAE;IACX,IAAI,IAAI,CAAC5G,MAAM,CAAC/B,sBAAsB,EAAE;MACpC2I,KAAK,CAACmD,eAAe,CAAC,CAAC;IAC3B;IACA,IAAI,IAAI,CAACrG,UAAU,EAAE;MACjB;IACJ;IACA,IAAI,CAAC1D,MAAM,CAACZ,WAAW,CAACwE,IAAI,CAAC,IAAI,CAAC;EACtC;EACArC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACoI,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMnC,GAAG,IAAI,IAAI,CAACjD,cAAc,EAAE;MACnCiD,GAAG,CAAClG,WAAW,CAAC,CAAC;IACrB;EACJ;EAAC,QAAAtD,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA6L,kCAAA3L,CAAA;IAAA,YAAAA,CAAA,IAAwFoL,yBAAyB,EAhgBnCrN,EAAE,CAAAsF,iBAAA,CAggBmDtF,EAAE,CAACuF,iBAAiB,GAhgBzEvF,EAAE,CAAAsF,iBAAA,CAggBoF+B,mBAAmB,GAhgBzGrH,EAAE,CAAAsF,iBAAA,CAggBoHtF,EAAE,CAACyF,UAAU,GAhgBnIzF,EAAE,CAAAsF,iBAAA,CAggB8ItF,EAAE,CAACwF,SAAS,GAhgB5JxF,EAAE,CAAAsF,iBAAA,CAggBuK1C,eAAe;EAAA,CAA4C;EAAA,QAAAV,EAAA,GAC3T,IAAI,CAAC+J,IAAI,kBAjgB8EjM,EAAE,CAAAkM,iBAAA;IAAAxJ,IAAA,EAigBJ2K,yBAAyB;IAAAxH,SAAA;IAAAsG,QAAA;IAAAC,YAAA,WAAAyB,uCAAAxH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAjgBvBrG,EAAE,CAAAsM,UAAA,mBAAAwB,mDAAAtB,MAAA;UAAA,OAigBJlG,GAAA,CAAAoH,OAAA,CAAAlB,MAAc,CAAC;QAAA;MAAA;MAAA,IAAAnG,EAAA;QAjgBbrG,EAAE,CAAA+N,WAAA,oCAAAzH,GAAA,CAAAgB,UAAA,mBAAAhB,GAAA,CAAArC,MAAA;MAAA;IAAA;IAAA2I,QAAA;EAAA,EAigBqS;AAC3Y;AACA;EAAA,QAAApK,SAAA,oBAAAA,SAAA,KAngBoGxC,EAAE,CAAAyC,iBAAA,CAmgBX4K,yBAAyB,EAAc,CAAC;IACvH3K,IAAI,EAAErC,SAAS;IACfsC,IAAI,EAAE,CAAC;MACCuE,QAAQ,EAAE,qCAAqC;MAC/C0F,QAAQ,EAAE,oBAAoB;MAC9B;MACAxF,IAAI,EAAE;QACF,sBAAsB,EAAE;MAC5B;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE1E,IAAI,EAAE1C,EAAE,CAACuF;IAAkB,CAAC,EAAE;MAAE7C,IAAI,EAAE2E;IAAoB,CAAC,EAAE;MAAE3E,IAAI,EAAE1C,EAAE,CAACyF;IAAW,CAAC,EAAE;MAAE/C,IAAI,EAAE1C,EAAE,CAACwF;IAAU,CAAC,EAAE;MAAE9C,IAAI,EAAEE;IAAgB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0E,UAAU,EAAE,CAAC;MAChN5E,IAAI,EAAEjC,WAAW;MACjBkC,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEsB,MAAM,EAAE,CAAC;MACTvB,IAAI,EAAEjC,WAAW;MACjBkC,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE+K,OAAO,EAAE,CAAC;MACVhL,IAAI,EAAElC,YAAY;MAClBmC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqL,gBAAgB,CAAC;EACnB,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,gBAAgB;MAC1BjB,SAAS,EAAE,CACPlM,sBAAsB,EACtBM,kBAAkB,EAClByB,eAAe;IAEvB,CAAC;EACL;EAAC,QAAAd,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAoM,yBAAAlM,CAAA;IAAA,YAAAA,CAAA,IAAwF+L,gBAAgB;EAAA,CAAkD;EAAA,QAAA9L,EAAA,GACnK,IAAI,CAACkM,IAAI,kBApiB8EpO,EAAE,CAAAqO,gBAAA;IAAA3L,IAAA,EAoiBSsL;EAAgB,EAK5F;EAAA,QAAAM,EAAA,GACtB,IAAI,CAACC,IAAI,kBA1iB8EvO,EAAE,CAAAwO,gBAAA;IAAAC,OAAA,GA0iBqCvN,YAAY;EAAA,EAAI;AAC3J;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KA5iBoGxC,EAAE,CAAAyC,iBAAA,CA4iBXuL,gBAAgB,EAAc,CAAC;IAC9GtL,IAAI,EAAEhC,QAAQ;IACdiC,IAAI,EAAE,CAAC;MACC8L,OAAO,EAAE,CAACvN,YAAY,CAAC;MACvBwN,YAAY,EAAE,CACV1B,uBAAuB,EACvBK,yBAAyB,EACzB1J,4BAA4B,EAC5B0D,mBAAmB,CACtB;MACDsH,OAAO,EAAE,CACL3B,uBAAuB,EACvBK,yBAAyB,EACzBhG,mBAAmB;IAE3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS7F,gBAAgB,EAAEmC,4BAA4B,EAAE0D,mBAAmB,EAAE2F,uBAAuB,EAAEgB,gBAAgB,EAAEpL,eAAe,EAAEyK,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}