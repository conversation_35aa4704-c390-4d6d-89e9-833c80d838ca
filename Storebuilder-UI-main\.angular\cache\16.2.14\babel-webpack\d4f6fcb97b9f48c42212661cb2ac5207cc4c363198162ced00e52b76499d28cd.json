{"ast": null, "code": "import { environment } from '@environments/environment';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { castToHttpParams } from \"@core/utilities\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@ngx-translate/core\";\nexport let AddressService = /*#__PURE__*/(() => {\n  class AddressService {\n    http;\n    translateService;\n    baseUrl;\n    chosenAddress = null;\n    loadedAddress = false;\n    customAddress = new Subject();\n    customMobile = new Subject();\n    addresses = new Subject();\n    addresseData = this.addresses.asObservable();\n    mobile = new BehaviorSubject(false);\n    mobileData = this.mobile.asObservable();\n    constructor(http, translateService) {\n      this.http = http;\n      this.translateService = translateService;\n      this.baseUrl = `${environment.apiEndPoint}/Tenant/Address`;\n    }\n    setCustomAddress(data) {\n      this.customAddress.next(data);\n    }\n    getCustomAddress() {\n      return this.customAddress.asObservable();\n    }\n    setCustomMobile(data) {\n      this.customMobile.next(data);\n    }\n    getCustomMobile() {\n      return this.customMobile.asObservable();\n    }\n    getAddress() {\n      return this.http.get(`${this.baseUrl}/GetCustomerAddress`);\n    }\n    getAddressById(id) {\n      return this.http.get(`${this.baseUrl}/GetAddressDetails/${id}`);\n    }\n    addAddress(data) {\n      return this.http.post(`${this.baseUrl}/CreateAddress`, data);\n    }\n    deleteAddress(id) {\n      return this.http.post(`${this.baseUrl}/DeleteAddress`, {\n        id: id\n      });\n    }\n    updateAddress(data) {\n      return this.http.post(`${this.baseUrl}/UpdateAddress`, data);\n    }\n    setDefault(id) {\n      return this.http.post(`${this.baseUrl}/SetDefaultToAddress`, {\n        id\n      });\n    }\n    getAddressLabel(data) {\n      for (let i = 1; i <= data.length; i++) {\n        if (!data[i - 1].addressLabel) {\n          let selectedLabel = this.translateService.instant('multipleAddress.address') + i;\n          data[i - 1].addressLabel = selectedLabel;\n        }\n      }\n      return data;\n    }\n    getAllCities(filter) {\n      return this.http.get(`${environment.apiEndPoint}/Shipment/Cities/GetAllCitiesByTenantId?`, {\n        params: castToHttpParams(filter)\n      });\n    }\n    getAllRegions(data) {\n      return this.http.get(`${environment.apiEndPoint}/Tenant/Region/GetAllRegionByTenant?`, {\n        params: castToHttpParams(data)\n      });\n    }\n    static ɵfac = function AddressService_Factory(t) {\n      return new (t || AddressService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.TranslateService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AddressService,\n      factory: AddressService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AddressService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}