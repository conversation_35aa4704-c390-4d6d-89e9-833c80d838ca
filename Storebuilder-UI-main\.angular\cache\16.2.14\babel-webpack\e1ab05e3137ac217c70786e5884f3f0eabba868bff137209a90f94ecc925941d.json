{"ast": null, "code": "export class ProductRate {}\nexport class GetProductReviewRates {}\nexport class ReviewDetailResponse {}\nexport class CreateReviewViewModel {}", "map": {"version": 3, "names": ["ProductRate", "GetProductReviewRates", "ReviewDetailResponse", "CreateReviewViewModel"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\review.ts"], "sourcesContent": ["export class ProductRate {\r\n  public count: number | undefined;\r\n  public rate: number | undefined;\r\n  public productId: number | undefined;\r\n\r\n\r\n}\r\n\r\nexport class GetProductReviewRates {\r\n  public count: number | undefined;\r\n  public rate: number | undefined;\r\n  public rateOne: number | undefined;\r\n  public rateTwo: number | undefined;\r\n  public rateThree: number | undefined;\r\n  public rateFour: number | undefined;\r\n  public rateFive: number | undefined;\r\n\r\n}\r\n\r\nexport class ReviewDetailResponse {\r\n  public id: number | undefined;\r\n  public describtion: string | undefined;\r\n  public rate: number | undefined;\r\n  public creationOn: Date | undefined;\r\n  public customerName: string | undefined;\r\n  public approved: boolean | null | undefined;\r\n\r\n}\r\n\r\nexport class CreateReviewViewModel {\r\n  public SpecProductIds: any[] | undefined;\r\n  public ReviewType: number | undefined;\r\n  public Rate: number | undefined;\r\n  public Description: string | undefined;\r\n  public OrderId: number | undefined;\r\n\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,WAAW;AAQxB,OAAM,MAAOC,qBAAqB;AAWlC,OAAM,MAAOC,oBAAoB;AAUjC,OAAM,MAAOC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}