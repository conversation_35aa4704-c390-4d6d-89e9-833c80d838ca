import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, Inject, PLATFORM_ID } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { OrderService, StoreService } from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-promo-code',
  templateUrl: './promo-code.component.html',
  styleUrls: ['./promo-code.component.scss'],
})
export class PromoCodeComponent implements OnInit, OnChanges {
  @Input() refreshSummary: Subject<void>;
  @Input() isReadOnly: boolean = false;
  @Input() paymentMethodDetails: any;
  @Input() deliveryOptionDetails: any;
  @Input() address: any;

  @Output() applyButtonClicked = new EventEmitter<void>();
  @Output() resetButtonClicked = new EventEmitter<void>();
  @Output() couponApplied = new EventEmitter<string>();
  @Output() couponRemoved = new EventEmitter<void>();
  @Input() regionId:number|null = null;

  promoCodeForm = new FormGroup({
    promoCode: new FormControl(''),
  });
  promoError: string | undefined;
  promoSuccess: string;
  orderDetails: any;
  isButtonDisabled: boolean = false;
  discount: any = '';
  // Tab-specific properties to prevent cross-tab contamination
  private readonly tabId: string;
  private sessionOrderId: string = '';

  constructor(
    public store: StoreService,
    private orderService: OrderService,
    private translate: TranslateService,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    // Generate unique tab identifier
    this.tabId = this.generateTabId();
  }

  ngOnInit(): void {
    this.getOrderData();
    // This resetPromo call is added to make sure that promo is resetted in DB after applying then refresh page
    this.resetPromo();
  }

  ngOnChanges(changes: SimpleChanges) {
    // changes['paymentMethodDetails'] &&
    // changes['paymentMethodDetails'].currentValue?.id ===
    //   changes['paymentMethodDetails'].previousValue?.id

    if(changes['paymentMethodDetails']) {
      this.resetPromo(true);
    }
    
    if (
      changes['deliveryOptionDetails']
    ) {
      this.removePromoOnly(); 
    }

    if(changes['regionId']) {
      this.resetPromo(true);
    }
    
  }

  /**
   * Generate a unique tab identifier
   */
  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Store OrderId in session storage with tab-specific key
   */
  private storeTabSpecificOrderId(orderId: string): void {
    if (isPlatformBrowser(this.platformId)) {
      sessionStorage.setItem(`orderData_${this.tabId}`, JSON.stringify({
        orderId: orderId,
        timestamp: Date.now()
      }));
      this.sessionOrderId = orderId;
    }
  }

  /**
   * Retrieve OrderId from session storage for this specific tab
   */
  private getTabSpecificOrderId(): string | null {
    if (isPlatformBrowser(this.platformId)) {
      const storedData = sessionStorage.getItem(`orderData_${this.tabId}`);
      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          return parsed.orderId;
        } catch (e) {
          console.error('Error parsing tab-specific order data:', e);
        }
      }
    }
    return null;
  }

  /**
   * Get the correct OrderId for this tab, with fallback chain
   */
  private getCorrectOrderId(): string {
    return this.getTabSpecificOrderId() || this.sessionOrderId || this.orderDetails?.orderId;
  }

  async getOrderData() {
    this.store.subscription('orderData').subscribe({
      next: (res: any) => {
        if (res) {
          this.orderDetails = res;
          // Store the OrderId for this specific tab session
          if (res.orderId) {
            this.storeTabSpecificOrderId(res.orderId);
          }
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  resetPromo(paymentMethodChanged?: boolean) {
    this.resetButtonClicked.emit();
    this.isReadOnly = false;
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    const reqBody = {
      OrderId: parseInt(correctOrderId),
      PromoCode: this.promoCodeForm.get('promoCode')?.value || '',
      PaymentType: this.paymentMethodDetails.name
    };

    if(this.discount){
      this.orderService.removePromoCode(reqBody).subscribe({
        next: (res) => {
          this.refreshSummary.next();
          paymentMethodChanged ? this.applyPromo() : this.promoCodeForm.get('promoCode')?.reset();
          this.discount = '';
          this.resetError();
          this.couponRemoved.emit();
        },
      });
    } else if(this.promoCodeForm.get('promoCode')?.value && paymentMethodChanged){
      this.resetError();
      this.applyPromo();
    } else {
      this.promoCodeForm.get('promoCode')?.reset();
      this.resetError();
    }
    this.isButtonDisabled = false;
  }
  removePromoOnly() {
    this.resetButtonClicked.emit();
    this.isReadOnly = false;
  
    const promo = this.promoCodeForm.get('promoCode')?.value;
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    const reqBody = {
      OrderId: parseInt(correctOrderId),
      PromoCode: promo || '',
      PaymentType: this.paymentMethodDetails.name
    };
  
    if (promo) {
  
      this.orderService.removePromoCode(reqBody).subscribe({
        next: () => {
          // this.refreshSummary.next();
          this.promoCodeForm.get('promoCode')?.reset();
          this.discount = '';
          this.resetError();
          this.isButtonDisabled = false;
          this.orderService.resetDiscount()
          this.couponRemoved.emit();
        },
        error: (err) => {
          console.error('Error removing promo code:', err);
        }
      });
    } else {
      this.promoCodeForm.get('promoCode')?.reset();
      this.resetError();
      this.isButtonDisabled = false;
    }
  }
  

  resetError() {
    this.promoError = '';
    this.promoSuccess = '';
  }
  applyPromo() {
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    const reqBody = {
      OrderId: parseInt(correctOrderId),
      PromoCode: this.promoCodeForm.get('promoCode')?.value,
      PaymentType: this.paymentMethodDetails.name,
      regionId :this.regionId
    };
    this.orderService.applyPromoCode(reqBody).subscribe({
      next: (res) => {
        if (res.success) {
          this.promoError='';
          this.applyButtonClicked.emit();
          this.isReadOnly = true;
          this.promoSuccess = this.translate.instant('promo.discountApplied');
          this.refreshSummary.next();
          this.isButtonDisabled = true;
          this.discount = this.promoCodeForm.get('promoCode')?.value;
          this.couponApplied.emit(this.discount);
        } else {
          this.promoSuccess=''
          this.orderService.resetDiscount()
          switch (res.message) {
            case 'Invalid Promo code':
              this.promoError = this.translate.instant(
                'promo.couponCodeInvalid'
              );

              break;
            case 'Coupon already used':
              this.promoError = this.translate.instant(
                'promo.couponAlreadyUsed'
              );
              break;
            default:
              return res.message;
          }
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }
}