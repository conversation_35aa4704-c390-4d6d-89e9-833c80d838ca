{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class FooterService {\n  constructor() {\n    this.http = inject(HttpClient);\n  }\n  getAllFooterSectionsWithPages() {\n    return this.http.get(`${environment.apiEndPoint}/Tenant/ShopPages/GetSectionsWithPages`);\n  }\n  static #_ = this.ɵfac = function FooterService_Factory(t) {\n    return new (t || FooterService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: FooterService,\n    factory: FooterService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpClient", "inject", "environment", "FooterService", "constructor", "http", "getAllFooterSectionsWithPages", "get", "apiEndPoint", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\footer\\footer.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { environment } from '@environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FooterService {\r\n  private http: HttpClient = inject(HttpClient)\r\n  \r\n  getAllFooterSectionsWithPages( ): any {\r\n    return this.http.get(`${environment.apiEndPoint}/Tenant/ShopPages/GetSectionsWithPages`);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAoB,eAAe;AAClD,SAASC,WAAW,QAAQ,2BAA2B;;AAKvD,OAAM,MAAOC,aAAa;EAH1BC,YAAA;IAIU,KAAAC,IAAI,GAAeJ,MAAM,CAACD,UAAU,CAAC;;EAE7CM,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,GAAGL,WAAW,CAACM,WAAW,wCAAwC,CAAC;EAC1F;EAAC,QAAAC,CAAA,G;qBALUN,aAAa;EAAA;EAAA,QAAAO,EAAA,G;WAAbP,aAAa;IAAAQ,OAAA,EAAbR,aAAa,CAAAS,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}