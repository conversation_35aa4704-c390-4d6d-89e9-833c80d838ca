{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AddressService {\n  constructor(http) {\n    this.http = http;\n    this.chosenAddress = null;\n    this.loadedAddress = false;\n    this.baseUrl = `${environment.apiEndPoint}/Tenant/Address`;\n  }\n  getAddress() {\n    return this.http.get(`${this.baseUrl}/GetCustomerAddress`);\n  }\n  addAddress(data) {\n    return this.http.post(`${this.baseUrl}/CreateAddress`, data);\n  }\n  deleteAddress(id) {\n    return this.http.delete(`${this.baseUrl}/addresses/${id}`);\n  }\n  updateAddress(data) {\n    return this.http.post(`${this.baseUrl}/UpdateAddress`, data);\n  }\n  static #_ = this.ɵfac = function AddressService_Factory(t) {\n    return new (t || AddressService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AddressService,\n    factory: AddressService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "AddressService", "constructor", "http", "<PERSON><PERSON><PERSON><PERSON>", "loadedAddress", "baseUrl", "apiEndPoint", "get<PERSON><PERSON><PERSON>", "get", "addAddress", "data", "post", "deleteAddress", "id", "delete", "updateAddress", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\services\\address.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { environment } from 'src/environments/environment';\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { Observable } from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AddressService {\r\n\r\n  baseUrl: string;\r\n  chosenAddress: any = null;\r\n  loadedAddress: boolean = false;\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}/Tenant/Address`;\r\n  }\r\n\r\n  getAddress(): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/GetCustomerAddress`);\r\n  }\r\n\r\n  addAddress(data: any): Observable<object> {\r\n\r\n    return this.http.post(`${this.baseUrl}/CreateAddress`, data);\r\n  }\r\n\r\n  deleteAddress(id: number): Observable<object> {\r\n    return this.http.delete(`${this.baseUrl}/addresses/${id}`);\r\n  }\r\n\r\n  updateAddress(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/UpdateAddress`, data);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,8BAA8B;;;AAO1D,OAAM,MAAOC,cAAc;EAKzBC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHd,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,aAAa,GAAY,KAAK;IAI5B,IAAI,CAACC,OAAO,GAAG,GAAGN,WAAW,CAACO,WAAW,iBAAiB;EAC5D;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,GAAG,IAAI,CAACH,OAAO,qBAAqB,CAAC;EAC5D;EAEAI,UAAUA,CAACC,IAAS;IAElB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACN,OAAO,gBAAgB,EAAEK,IAAI,CAAC;EAC9D;EAEAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACT,OAAO,cAAcQ,EAAE,EAAE,CAAC;EAC5D;EAEAE,aAAaA,CAACL,IAAS;IACrB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACN,OAAO,gBAAgB,EAAEK,IAAI,CAAC;EAC9D;EAAC,QAAAM,CAAA,G;qBA1BUhB,cAAc,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdrB,cAAc;IAAAsB,OAAA,EAAdtB,cAAc,CAAAuB,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}