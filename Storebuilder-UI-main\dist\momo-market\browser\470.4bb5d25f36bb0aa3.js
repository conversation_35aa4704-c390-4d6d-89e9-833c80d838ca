"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[470],{5334:(v,x,o)=>{o.d(x,{q:()=>w});var t=o(5879),u=o(6814),l=o(6223),f=o(6663),c=o(3714);const d=function(n){return{color:n}};function I(n,M){if(1&n&&(t.TgZ(0,"label",7),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&n){const e=t.oxw(2);t.Q6J("ngStyle",t.VKq(4,d,e.firstNameFlag&&0===e.email.length||null!=e.emailForm.controls.emailAddress.errors&&e.emailForm.controls.emailAddress.errors.email?"red":e.labelColor)),t.xp6(1),t.hij(" ",t.lcZ(2,2,"auth.registerPassword.email")," ")}}function b(n,M){if(1&n&&(t.TgZ(0,"label",8),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&n){const e=t.oxw(2);t.Q6J("ngStyle",t.VKq(5,d,e.firstNameFlag&&0===e.email.length||null!=e.emailForm.controls.emailAddress.errors&&e.emailForm.controls.emailAddress.errors.email?"red":e.labelColor))("for",e.floatingLabel?"custom-float-input":"float-input"),t.xp6(1),t.hij(" ",t.lcZ(2,3,"auth.registerPassword.email")," * ")}}function i(n,M){1&n&&(t.TgZ(0,"p",9)(1,"small",10),t._uU(2),t.ALo(3,"translate"),t.qZA()()),2&n&&(t.xp6(2),t.Oqu(t.lcZ(3,1,"auth.registerPassword.validation6")))}const h=function(n,M){return{"p-float-label":n,"mt-3":M}},m=function(){return{standalone:!0}};function a(n,M){if(1&n){const e=t.EpF();t.TgZ(0,"div",1),t.YNc(1,I,3,6,"label",2),t.TgZ(2,"span",3)(3,"input",4),t.NdJ("click",function(){t.CHM(e);const s=t.oxw();return t.KtG(s.onBlur())})("input",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.onInputChange(s))})("ngModelChange",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.email=s)})("ngModelChange",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.onEmailChange(s))}),t.qZA(),t.YNc(4,b,3,7,"label",5),t.qZA(),t.YNc(5,i,4,3,"p",6),t.qZA()}if(2&n){const e=t.oxw();t.xp6(1),t.Q6J("ngIf",!e.floatingLabel),t.xp6(1),t.Q6J("ngClass",t.WLB(7,h,e.floatingLabel,e.floatingLabel)),t.xp6(1),t.Q6J("ngModel",e.email)("ngModelOptions",t.DdM(10,m))("id",e.floatingLabel?"custom-float-input":"float-input"),t.xp6(1),t.Q6J("ngIf",e.floatingLabel),t.xp6(1),t.Q6J("ngIf",null==e.emailForm.controls||null==e.emailForm.controls.emailAddress||null==e.emailForm.controls.emailAddress.errors?null:e.emailForm.controls.emailAddress.errors.email)}}function p(n,M){if(1&n&&(t.TgZ(0,"label",13),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&n){const e=t.oxw(2);t.Udp("color",e.labelColor),t.xp6(1),t.hij(" ",t.lcZ(2,3,"auth.registerPassword.email")," ")}}function g(n,M){if(1&n&&(t.TgZ(0,"label",14),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&n){const e=t.oxw(2);t.Udp("color",e.labelColor),t.Q6J("for",e.floatingLabel?"custom-float-input":"float-input"),t.xp6(1),t.hij(" ",t.lcZ(2,4,"auth.registerPassword.email")," ")}}function P(n,M){1&n&&(t.TgZ(0,"p",9)(1,"small",10),t._uU(2),t.ALo(3,"translate"),t.qZA()()),2&n&&(t.xp6(2),t.Oqu(t.lcZ(3,1,"auth.registerPassword.validation6")))}function _(n,M){if(1&n){const e=t.EpF();t.TgZ(0,"div",1),t.YNc(1,p,3,5,"label",11),t.TgZ(2,"span",3)(3,"input",4),t.NdJ("click",function(){t.CHM(e);const s=t.oxw();return t.KtG(s.onBlur())})("input",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.onInputChange(s))})("ngModelChange",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.email=s)})("ngModelChange",function(s){t.CHM(e);const C=t.oxw();return t.KtG(C.onEmailChange(s))}),t.qZA(),t.YNc(4,g,3,6,"label",12),t.qZA(),t.YNc(5,P,4,3,"p",6),t.qZA()}if(2&n){const e=t.oxw();t.xp6(1),t.Q6J("ngIf",!e.floatingLabel),t.xp6(1),t.Q6J("ngClass",t.WLB(7,h,e.floatingLabel,e.floatingLabel)),t.xp6(1),t.Q6J("ngModel",e.email)("ngModelOptions",t.DdM(10,m))("id",e.floatingLabel?"custom-float-input":"float-input"),t.xp6(1),t.Q6J("ngIf",e.floatingLabel),t.xp6(1),t.Q6J("ngIf",null==e.emailForm.controls||null==e.emailForm.controls.emailAddress||null==e.emailForm.controls.emailAddress.errors?null:e.emailForm.controls.emailAddress.errors.email)}}let w=(()=>{class n{fb;emailRequired="false";floatingLabel=!1;labelColor="white";placeholder="";emailLabel="";emailChange=new t.vpe;validationChange=new t.vpe;blur=new t.vpe;email="";firstNameFlag=!1;emailForm;constructor(e){this.fb=e,this.emailForm=this.fb.group({emailAddress:["",[this.emailValidator]]})}ngOnInit(){this.emailForm.controls.emailAddress.setValidators("true"===this.emailRequired?[l.kI.required,this.emailValidator]:[this.emailValidator]),this.emailForm.controls.emailAddress.updateValueAndValidity(),this.emailForm.valueChanges.subscribe(()=>{this.validationChange.emit(this.isValid())})}onEmailChange(e){this.emailForm.patchValue({emailAddress:e}),this.emailForm.controls.emailAddress.updateValueAndValidity(),e&&""!==e.trim()&&this.isValidEmailAddress(e)?this.emailChange.emit(e.trim()):this.emailChange.emit(null)}onBlur(){this.blur.emit(),"true"===this.emailRequired&&0===this.email.length&&(this.firstNameFlag=!0)}onInputChange(e){if(e.target.value){const r=e.target.value.indexOf(" ");0==r&&(e.target.value=e.target.value.substring(0,r)+e.target.value.substring(r+1))}this.onEmailChange(e.target.value)}emailValidator(e){return e.value?/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]{2,}(\.[a-zA-Z0-9-]{2,})+$/.test(e.value)?null:{email:!0}:null}isValidEmailAddress(e){return/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]{2,}(\.[a-zA-Z0-9-]{2,})+$/.test(e)}isValid(){return this.emailForm.valid&&("false"===this.emailRequired||this.email.length>0)}static \u0275fac=function(r){return new(r||n)(t.Y36(l.qu))};static \u0275cmp=t.Xpm({type:n,selectors:[["app-email-input"]],inputs:{emailRequired:"emailRequired",floatingLabel:"floatingLabel",labelColor:"labelColor",placeholder:"placeholder",emailLabel:"emailLabel"},outputs:{emailChange:"emailChange",validationChange:"validationChange",blur:"blur"},standalone:!0,features:[t.jDz],decls:2,vars:2,consts:[["class","p-field p-col-12",4,"ngIf"],[1,"p-field","p-col-12"],["class","contact-label",3,"ngStyle",4,"ngIf"],[3,"ngClass"],["autocomplete","new-password","name","emailAddress","pInputText","","type","email",3,"ngModel","ngModelOptions","id","click","input","ngModelChange"],[3,"ngStyle","for",4,"ngIf"],["class","field-error",4,"ngIf"],[1,"contact-label",3,"ngStyle"],[3,"ngStyle","for"],[1,"field-error"],[2,"color","red"],["class","contact-label",3,"color",4,"ngIf"],[3,"for","color",4,"ngIf"],[1,"contact-label"],[3,"for"]],template:function(r,s){1&r&&(t.YNc(0,a,6,11,"div",0),t.YNc(1,_,6,11,"div",0)),2&r&&(t.Q6J("ngIf","true"===s.emailRequired),t.xp6(1),t.Q6J("ngIf","false"===s.emailRequired))},dependencies:[u.ez,u.mk,u.O5,u.PC,l.u5,l.Fj,l.JJ,l.On,l.UX,f.aw,f.X$,c.j,c.o],styles:[".p-field[_ngcontent-%COMP%]{margin-bottom:1rem}span[_ngcontent-%COMP%]{display:block}.mt-3[_ngcontent-%COMP%]{margin-top:.25rem}label[_ngcontent-%COMP%]{pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}input#float-input[_ngcontent-%COMP%], input#custom-float-input[_ngcontent-%COMP%]{height:50px!important;width:100%;border-radius:2px;opacity:1;border:none!important;border:1px solid #E4E7E9!important;padding-left:10px;padding-right:10px;font-size:14px;font-weight:400;font-family:var(--regular-font)!important}.field-error[_ngcontent-%COMP%]{margin-top:.25rem;margin-bottom:0}.p-float-label[_ngcontent-%COMP%]{position:relative}.p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{position:absolute;pointer-events:none;top:50%;margin-top:-.5rem;transition:all .2s;color:#6c757d!important}.p-float-label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus ~ label[_ngcontent-%COMP%], .p-float-label[_ngcontent-%COMP%]   input.p-filled[_ngcontent-%COMP%] ~ label[_ngcontent-%COMP%], .p-float-label[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:-webkit-autofill ~ label[_ngcontent-%COMP%]{top:-.5rem;font-size:12px;background-color:transparent;padding:0 .25rem;color:#333!important}.contact-label[_ngcontent-%COMP%]{display:block;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-bottom:.25rem;margin-top:0!important;color:#333!important}"]})}return n})()},1064:(v,x,o)=>{o.d(x,{C:()=>m});var t=o(5879),u=o(6814),l=o(1423),f=o(6223),c=o(6663);function d(a,p){1&a&&(t.TgZ(0,"label",6),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&a&&(t.xp6(1),t.hij("",t.lcZ(2,1,"signIn.password")," *"))}function I(a,p){1&a&&(t.TgZ(0,"label"),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&a&&(t.xp6(1),t.hij("",t.lcZ(2,1,"signIn.password")," *"))}function b(a,p){if(1&a){const g=t.EpF();t.TgZ(0,"div",7)(1,"a",8),t.NdJ("click",function(){t.CHM(g);const _=t.oxw();return t.KtG(_.onForgotPasswordClick())}),t._uU(2),t.ALo(3,"translate"),t.qZA()()}if(2&a){const g=t.oxw();t.xp6(1),t.Q6J("ngClass",g.forgotPasswordClass),t.xp6(1),t.hij(" ",t.lcZ(3,2,"signIn.forgotPassword")," ")}}const i=function(a){return{"mt-3":a}},h=function(){return{standalone:!0}};let m=(()=>{class a{showForgotPassword=!0;forgotPasswordClass="";floatingLabel=!1;feedback=!0;passwordChange=new t.vpe;validationChange=new t.vpe;forgotPasswordClick=new t.vpe;password="";modelChanged(g){this.password=g,this.passwordChange.emit(g),this.validationChange.emit(g&&g.length>0)}onForgotPasswordClick(){this.forgotPasswordClick.emit()}static \u0275fac=function(P){return new(P||a)};static \u0275cmp=t.Xpm({type:a,selectors:[["app-password-input"]],inputs:{showForgotPassword:"showForgotPassword",forgotPasswordClass:"forgotPasswordClass",floatingLabel:"floatingLabel",feedback:"feedback"},outputs:{passwordChange:"passwordChange",validationChange:"validationChange",forgotPasswordClick:"forgotPasswordClick"},standalone:!0,features:[t.jDz],decls:6,vars:11,consts:[[1,"p-field","p-col-12"],["class","contact-label",4,"ngIf"],[1,"p-float-label",3,"ngClass"],["autocomplete","off","id","custom-password-input",1,"customClass",3,"ngModel","feedback","ngModelOptions","toggleMask","ngModelChange"],[4,"ngIf"],["class","flex justify-content-end flex-wrap resetPassword cursor-pointer",4,"ngIf"],[1,"contact-label"],[1,"flex","justify-content-end","flex-wrap","resetPassword","cursor-pointer"],[1,"no-underline",3,"ngClass","click"]],template:function(P,_){1&P&&(t.TgZ(0,"div",0),t.YNc(1,d,3,3,"label",1),t.TgZ(2,"span",2)(3,"p-password",3),t.NdJ("ngModelChange",function(n){return _.modelChanged(n)})("ngModelChange",function(n){return _.password=n}),t.qZA(),t.YNc(4,I,3,3,"label",4),t.qZA(),t.YNc(5,b,4,4,"div",5),t.qZA()),2&P&&(t.xp6(1),t.Q6J("ngIf",!_.floatingLabel),t.xp6(1),t.Q6J("ngClass",t.VKq(8,i,_.floatingLabel)),t.xp6(1),t.Q6J("ngModel",_.password)("feedback",_.feedback)("ngModelOptions",t.DdM(10,h))("toggleMask",!0),t.xp6(1),t.Q6J("ngIf",_.floatingLabel),t.xp6(1),t.Q6J("ngIf",_.showForgotPassword))},dependencies:[u.ez,u.mk,u.O5,f.u5,f.JJ,f.On,l.gz,l.ro,c.aw,c.X$],styles:[".contact-label[_ngcontent-%COMP%]{pointer-events:none;padding:10px;font-size:12px!important;font-weight:400;color:#2d2d2d;line-height:20px}.resetPassword[_ngcontent-%COMP%]{gap:8px;padding:8px 0}.resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#272727;font-size:12px;font-style:normal;font-weight:400;line-height:normal;font-family:var(--regular-font)!important}.p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#323232}  .customClass input{height:60px!important;width:100%;border-radius:4px;opacity:1;border:none!important;border:1px solid #ccc!important;padding-left:10px;padding-right:10px;background-color:#fff!important;font-family:var(--regular-font)!important;font-size:14px}label[_ngcontent-%COMP%]{color:red;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}"]})}return a})()},8145:(v,x,o)=>{o.d(x,{T:()=>b});var t=o(5879),u=o(6814),l=o(6223),f=o(6663),c=o(7152);const d=function(i){return{border:i}},I=function(i,h){return[i,h]};let b=(()=>{class i{fb;maxLength=12;selectedCountryISO;preferredCountries=[];placeholder="";_phoneNumber="";set alreadyAddedPhoneNumber(m){m&&"object"!=typeof m&&(this._phoneNumber=m.slice(3),this.signInForm.controls.phoneNumber.setValue(this._phoneNumber))}get alreadyAddedPhoneNumber(){return this._phoneNumber}phoneNumberChange=new t.vpe;validationChange=new t.vpe;PhoneNumberFormat=c.M9;SearchCountryField=c.wX;signInForm;constructor(m){this.fb=m,this.signInForm=this.fb.group({phoneNumber:["",l.kI.required]})}ngOnInit(){this.signInForm.controls.phoneNumber.valueChanges.subscribe(m=>{this.phoneNumberChange.emit(m),this.validationChange.emit(this.signInForm.controls.phoneNumber.valid)})}static \u0275fac=function(a){return new(a||i)(t.Y36(l.qu))};static \u0275cmp=t.Xpm({type:i,selectors:[["app-phone-input"]],inputs:{maxLength:"maxLength",selectedCountryISO:"selectedCountryISO",preferredCountries:"preferredCountries",placeholder:"placeholder",alreadyAddedPhoneNumber:"alreadyAddedPhoneNumber"},outputs:{phoneNumberChange:"phoneNumberChange",validationChange:"validationChange"},standalone:!0,features:[t._Bn([{provide:l.JU,useExisting:(0,t.Gpc)(()=>i),multi:!0}]),t.jDz],decls:8,vars:23,consts:[[1,"p-field","p-col-12"],[1,"custom-input",3,"ngStyle"],[3,"formGroup"],["f","ngForm"],["for","mobileNumber",1,"contact-label"],["autocomplete","new-phoneNumber","formControlName","phoneNumber","name","phoneNumber",3,"cssClass","enableAutoCountrySelect","enablePlaceholder","maxLength","numberFormat","phoneValidation","preferredCountries","searchCountryField","searchCountryFlag","selectFirstCountry","selectedCountryISO","separateDialCode","customPlaceholder"]],template:function(a,p){1&a&&(t.TgZ(0,"div",0)(1,"div",1)(2,"form",2,3)(4,"label",4),t._uU(5),t.ALo(6,"translate"),t.qZA(),t._UZ(7,"ngx-intl-tel-input",5),t.qZA()()()),2&a&&(t.xp6(1),t.Q6J("ngStyle",t.VKq(18,d,(null==p.signInForm.controls.phoneNumber.value||null==p.signInForm.controls.phoneNumber.value.e164Number?null:p.signInForm.controls.phoneNumber.value.e164Number.length)>0&&!p.signInForm.controls.phoneNumber.valid?"1px solid red":"0px solid transparent")),t.xp6(1),t.Q6J("formGroup",p.signInForm),t.xp6(3),t.hij("",t.lcZ(6,16,"contactUs.mobileNumber"),"*"),t.xp6(2),t.Q6J("cssClass","custom contact-input-phone mobile-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",p.maxLength)("numberFormat",p.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",p.preferredCountries)("searchCountryField",t.WLB(20,I,p.SearchCountryField.Iso2,p.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",p.selectedCountryISO)("separateDialCode",!0)("customPlaceholder",p.placeholder))},dependencies:[u.ez,u.PC,l.UX,l._Y,l.JJ,l.JL,l.sg,l.u,f.aw,f.X$,c.J7,c.FV,c.mh],styles:['.contact-label[_ngcontent-%COMP%]{pointer-events:none;padding:8px 0;font-size:12px!important;font-weight:400;color:#2d2d2d;line-height:20px;font-family:var(--medium-font)!important}[_nghost-%COMP%]     .contact-input-phone{background-color:#fff!important;border:1px solid #ccc!important;border-radius:4px;padding:10px;width:100%;box-sizing:border-box}[_nghost-%COMP%]     .iti__selected-flag{padding:0 6px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{content:"";position:absolute;top:15px;left:93px;width:1px;height:50%;background-color:#9ca69c;transform:translate(-50%);pointer-events:none}@media screen and (max-width: 767px){.custom-input[_ngcontent-%COMP%]{border:none!important;border-radius:0!important}[_nghost-%COMP%]     .contact-input-phone{border:1px solid rgba(32,78,110,.1)!important;border-radius:8px;height:60px!important}.contact-label[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;padding:10px!important}}']})}return i})()},7134:(v,x,o)=>{o.d(x,{R:()=>u});var t=o(4004);class u{static newGuid(){return(0,t.Z)()}}},3714:(v,x,o)=>{o.d(x,{j:()=>c,o:()=>f});var t=o(5879),u=o(6814),l=o(6223);let f=(()=>{class d{el;ngModel;cd;filled;constructor(b,i,h){this.el=b,this.ngModel=i,this.cd=h}ngAfterViewInit(){this.updateFilledState(),this.cd.detectChanges()}ngDoCheck(){this.updateFilledState()}onInput(){this.updateFilledState()}updateFilledState(){this.filled=this.el.nativeElement.value&&this.el.nativeElement.value.length||this.ngModel&&this.ngModel.model}static \u0275fac=function(i){return new(i||d)(t.Y36(t.SBq),t.Y36(l.On,8),t.Y36(t.sBO))};static \u0275dir=t.lG2({type:d,selectors:[["","pInputText",""]],hostAttrs:[1,"p-inputtext","p-component","p-element"],hostVars:2,hostBindings:function(i,h){1&i&&t.NdJ("input",function(a){return h.onInput(a)}),2&i&&t.ekj("p-filled",h.filled)}})}return d})(),c=(()=>{class d{static \u0275fac=function(i){return new(i||d)};static \u0275mod=t.oAB({type:d});static \u0275inj=t.cJS({imports:[u.ez]})}return d})()}}]);