@import '../common';


@include responsive(mobile) {
  #header-zoom-container {
    max-width: 96vw;
    min-width: 96vw;
    margin: 0 auto;
  }
}

@include responsive(desktop) {
  #header-zoom-container {
    display: flex;
    max-width: 1440px;
    min-width: 1440px;
    margin: 0 auto;
  }

  #footer-zoom-container {
    display: flex;
    max-width: 1270px;
    min-width: 1270px;
    margin: 0 auto;
  }

  #zoom-container {
    padding: 0 !important;
    max-width: 1250px !important;
    width: 1250px !important;
    margin: 0 auto;
  }
}

@include responsive(large-desktop) {
  #header-zoom-container {
    display: flex;
    max-width: 1440px;
    min-width: 1440px;
    margin: 0 auto;
  }

  #footer-zoom-container {
    display: flex;
    max-width: 1350px;
    min-width: 1350px;
    margin: 0 auto;
  }

  #zoom-container {
    padding: 0 !important;
    max-width: 1350px !important;
    width: 1350px !important;
    margin: 0 auto;
  }
}


@include responsive(large-desktop) {
  .v3-header header {

    .select-location {
      margin-left: 15px !important;
      margin-right: 30px !important;
    }

    .header-left-section {
      margin-left: 2% !important;
      width: 30% !important;
    }

    .language-dropdown select {
      margin-left: 0px !important;
    }

    .wishlist-icon a {
      margin-right: 0rem !important;
      margin-left: 0rem !important;
    }

    .cart-icon a {
      margin-right: 0rem !important;
      margin-left: 0rem !important;

      span {
        margin-right: 0rem !important;
        margin-left: 0rem !important;
      }
    }

    .header-logo {
      width: 100px !important;
      height: 45px !important;
      margin-top: 15px !important;
    }

  }

  .v3-header-cateogries {

    .header-category {
      // Remove padding override - let the component handle it

      #header-zoom-container {
        max-width: var(--Layout-Width-Screen, 1440px) !important;
        margin: 0 auto !important;
      }
    }
  }

  .yellow-bg-navbar-active {
    top: 122px !important;
  }

  .yellow-bg-navbar-inactive {
    top: 70px !important;
  }

  .feature-products {
    height: auto !important;

    a {
      margin-top: 25px !important;
    }
  }
}

@media (min-width: 1000px) and (max-width: 1400px) {
  .v3-header header {
    .header-left-section {
      width: 35% !important;
    }
  }

}


@include responsive(desktop) {
  .v3-header header {

    .select-location {
      margin-left: 15px !important;
      margin-right: 30px !important;
    }

    .header-left-section {
      margin-left: 2% !important;
      width: 28% !important;
    }

    .language-dropdown select {
      margin-left: 0px !important;
      margin-right: 10px !important;
    }

    .wishlist-icon {
      margin-right: 10px !important;
    }

    .cart-icon {
      margin-right: 10px !important;
    }

    .wishlist-icon a {
      margin-right: 0rem !important;
      margin-left: 0rem !important;
    }

    .cart-icon a {
      margin-right: 0rem !important;
      margin-left: 0rem !important;

      span {
        margin-right: 0rem !important;
        margin-left: 0rem !important;
      }
    }

    .header-logo {
      margin-top: 20px !important;
      width: 100px !important;
    }

  }

  .landing-template {
    padding: 0 50px !important;
  }

  .yellow-bg-navbar-active {
    top: 122px !important;
  }

  .yellow-bg-navbar-inactive {
    top: 70px !important;
  }

  .feature-products {
    height: auto !important;

    a {
      margin-top: 25px !important;
    }
  }
}

@include responsive(tablet) {
  .v3-header header {
    height: 145px !important;
    row-gap: 4px !important;
    padding: 0 25px !important;

    .search-bar {
      width: 65% !important;
    }

    .header-logo {
      margin-top: 25px !important;
      width: 100px !important;
    }

    .language-dropdown select {
      margin-left: 10px !important;
    }
  }

  .v3-header {
    .header-category {
      top: 145px !important;
      padding: 0 25px !important;
    }

    .header-left-section {
      width: 100% !important;
      margin-left: 0% !important;
    }
  }

  .feature-products {
    height: auto !important;

    a {
      margin-top: 25px !important;
    }
  }
}

@include responsive(mobile) {
  .v3-header header {
    padding: 0 8px !important;

    .header-logo {
      width: 160px !important;
      margin-top: 15px;
      height: 50px !important;
      border-radius: 3px;
      object-fit: contain;
      cursor: pointer;
    }

    .header-left-section {
      width: 100% !important;
      margin-left: 0% !important;
    }

    .language-dropdown select {
      margin-left: 0px !important;
    }
  }

  .feature-products {
    height: auto !important;

    a {
      margin-top: 20px !important;
    }
  }
}
