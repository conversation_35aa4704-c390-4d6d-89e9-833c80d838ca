{"ast": null, "code": "import { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-google-analytics\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@core/services/custom-GA.service\";\nexport class GAImpressionDirective {\n  constructor(el, googleAnalytics, permissionService, location, _GACustomEvent) {\n    this.el = el;\n    this.googleAnalytics = googleAnalytics;\n    this.permissionService = permissionService;\n    this.location = location;\n    this._GACustomEvent = _GACustomEvent;\n    this.tagName = GaLocalActionEnum;\n    this.isGoogleAnalytics = false;\n    this.isGoogleAnalytics = this.permissionService.hasPermission(\"Google Analytics\");\n  }\n  ngOnInit() {\n    this.getVisibleElements();\n    let currentPath = this.location.path();\n    if (currentPath.includes('/Portal')) {\n      currentPath.replace(\"/Portal\", \"\");\n    }\n    let expectedPath = currentPath.split(\"/\");\n    this.exactPath = expectedPath[1]?.includes('search') ? \"Search\" : expectedPath[1];\n  }\n  getVisibleElements() {\n    this.observer = new IntersectionObserver((entries, observer) => {\n      entries.forEach(entry => {\n        // Emit true if the element is in the viewport, false otherwise\n        if (entry.isIntersecting) {\n          this.logGoogleImpression();\n          observer.unobserve(entry.target);\n        }\n      });\n    });\n    this.observer.observe(this.el.nativeElement);\n  }\n  ngOnDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n  }\n  logGoogleImpression() {\n    if (this.isGoogleAnalytics) {\n      let pageValue = '';\n      let query = window.location.search.replace('?', '');\n      if (query.includes('categoryName')) {\n        pageValue = query.replace('categoryName=', '');\n      }\n      if (query.includes('q')) {\n        pageValue = query.replace('q=', '');\n      }\n      let itemListName = this.exactPath?.includes('category') ? this.product.categoryName : pageValue;\n      this.googleAnalytics.event(this.tagName.IMPRESSIONS, 'product', 'IMPRESSIONS', 1, true, {\n        \"product_name\": this.product.productName ? this.product?.productName : this.product.name,\n        \"category_name\": this.product.categoryName,\n        \"product_SKU\": this.product?.specProductDetails?.skuAutoGenerated ? this.product?.specProductDetails?.skuAutoGenerated : this.product['skuAutoGenerated'],\n        \"product_ID\": this.product.productId,\n        \"shop_ID\": this.product.shopId,\n        \"product_tags\": this.product?.isBest || this.product.bestSeller ? 'Best Seller' : this.product?.isNew || this.product.newArrival ? 'New Arrival' : this.product?.isHot || this.product.hotDeals ? 'Hot Deals' : 'None',\n        \"promotion\": this.product?.promotionName ? this.product?.promotionName : 'None',\n        \"page_label\": this.exactPath ? this.exactPath : 'Home',\n        \"page_value\": itemListName\n      });\n      this._GACustomEvent.viewItemListEvent(this.product, itemListName);\n    }\n  }\n}\nGAImpressionDirective.ɵfac = function GAImpressionDirective_Factory(t) {\n  return new (t || GAImpressionDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i3.Location), i0.ɵɵdirectiveInject(i4.CustomGAService));\n};\nGAImpressionDirective.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n  type: GAImpressionDirective,\n  selectors: [[\"\", \"appGAImpression\", \"\"]],\n  inputs: {\n    product: \"product\"\n  },\n  standalone: true\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}