import {
  Component,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild
} from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { get } from 'scriptjs';
import { Router } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import { environment } from '@environments/environment';

import { RequestDelivery } from '@core/interface';

import {
  AddressService,
  LoaderService,
  OrderService,
  ShopService,
  ShipmentService,
  TransactionDetailsService,
  TransactionService,
  StoreService,
  PaymentService,
  ProductLogicService, AuthService, MainDataService, PermissionService
} from '@core/services';
import {GoogleAnalyticsService} from "ngx-google-analytics";
import {Subject, Subscription, filter, forkJoin, take} from "rxjs";
import {isPlatformBrowser} from "@angular/common";
import {GaLocalActionEnum} from "@core/enums/ga-local-action-enum";
import { FeatureFlag, PaymentMethodEnum } from '@core/enums/payment-method-enum';
import { CustomGAService } from '@core/services/custom-GA.service';
import { InitiateApmWidgetRequest, InitiateApmWidgetResponse } from '@pages/checkout/modals/APM';
import { LightboxConfig } from '@pages/checkout/modals/lightBox';
import { PaymentStateService } from '@core/services/payment-state.service';


@Component({
  selector: 'app-payment-cart',
  templateUrl: './payment-cart.component.html',
  styleUrls: ['./payment-cart.component.scss'],
  providers: [DialogService],
})
export class PaymentCartComponent implements OnInit, OnChanges {
  
  @Input() deliveryOptionDetails: any;
  @Input() refreshSummary: Subject<void>;
  @Input() cartItems: any[];
  @Input() paymentMethodDetails:any;
  @Input() isFetchOrderPaymentConfig: boolean = false;

  @ViewChild('paymentMethodWrapper') paymentMethodWrapper!: any;
  @HostListener('window:resize', ['$event'])

  refreshSubscription: Subscription;
  ref: DynamicDialogRef | undefined;
  shopsPayments = new Array<any>();
  totalLogisticsFee = 0;
  subOrdersCommission = new Array<any>();
  lightBoxURL: string;
  timeString: string;
  secureHash: string;
  shipmentFee: number = 0;
  totalDiscount: number = 0;
  allowedpaymentMethod: number = 0;
  userPhoneNumber: any;
  showPayButton: boolean = false;
  orderDetails: any;
  orderDetailsWithConfig: any;
  paymentResult: any;
  currency: any;
  countryCode: any;
  countryPhone: any;
  shipmentCost: any = -1;
  AmountTrxn = 0;
  requestData = new RequestDelivery();
  shipmentDetails: any;
  subOrderDetails: any;
  minDate: any = new Date().toDateString();
  maxDate: any = new Date().toDateString();
  multipleTransactionDetails = new Array<any>();
  PaymentCalled: boolean = false;
  shopIds = [];
  merchantId: string = '';
  terminalId: string = '';
  isMobileTemplate: boolean = false;
  OrderId: string = '';
  OrderShopId: number | null = null;
  // Store the original OrderId from this tab's session to prevent cross-tab contamination
  private sessionOrderId: string = '';
  private readonly tabId: string;
  minTimeDelivery: number = Number.MAX_SAFE_INTEGER;
  maxTimeDelivery: number = -1;
  MomoPayCommissionAmount = 0;
  cartId: number = 0;
  arrivalDate: string = '';
  isProceccedCheckOut: boolean = false;
  isLayoutTemplate: boolean = false;
  isShipmentFeeExist : boolean = false;
  isGoogleAnalytics: boolean = false;
  itemsTotalPrices: number = 0;
  currencyCode: string = '';
  decimalValue: number = 0;
  disableCent: any;
  displayLoaderModal= false;
  observer: MutationObserver;
  addressSubscription: Subscription;
  mobileSubscription: Subscription;
  screenWidth:any=window.innerWidth;
  sessionId: string | null;
  userDetails: any;
  orderDiscount: number = 0;

  apmConfig : InitiateApmWidgetResponse ;
  lightboxConfigObj : LightboxConfig ;
  featureFlag: FeatureFlag = FeatureFlag.LIGHTBOX;

  constructor(
    public dialogService: DialogService,
    @Inject(PLATFORM_ID) private platformId: any,
    public store: StoreService,
    public paymentService: PaymentService,
    private router: Router,
    public messageService: MessageService,
    private shopService: ShopService,
    private orderService: OrderService,
    private translate: TranslateService,
    private addressService: AddressService,
    private productLogicService: ProductLogicService,
    private loaderService: LoaderService,
    private authService: AuthService,
    private mainDataService: MainDataService,
    private permissionService: PermissionService,
    private $gaService: GoogleAnalyticsService,
    public shipmentService: ShipmentService,
    private _GACustomEvent:CustomGAService,
    private paymentStateService: PaymentStateService
  ) {
    // Generate unique tab identifier to prevent cross-tab contamination
    this.tabId = this.generateTabId();

    let value = localStorage.getItem('CurrencyDecimal');
    if (value)
      this.decimalValue = parseInt(value);

    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout')
    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')
    this.disableCent = localStorage.getItem('DisableCents');
    this.isFetchOrderPaymentConfig = false;
  }

  get totalOrderAmount(): number {
    const baseAmount = this.orderDetails?.orderAmount;
    const shipmentCost = Number(this.shipmentService.shipmentCost);
    const discount = this.orderDiscount;
    return baseAmount + shipmentCost - discount;
  }

  async ngOnInit() {
    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');
    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');
    this.isShipmentFeeExist = this.permissionService.hasPermission('Shipment-Fee')
    this.getOrderData();
    
    this.addressSubscription = this.addressService.getCustomAddress().subscribe((res: any) => {
      if (res) {
        this.getFullOrderData();
      }
    })
    this.mobileSubscription = this.addressService.getCustomMobile().subscribe((res: any) => {
      if (res) {
        this.UpdateOrder();
      }
    })
    this.showPayButton = false;
    this.getCurrentCartId();
    this.store.subscription('mainData').subscribe({
      next: (res: any) => {

        let data = res.find(
          (obj: any) =>
            obj.key.toLocaleLowerCase() === 'LightBoxURL'.toLocaleLowerCase()
        );
        if (data) this.lightBoxURL = data.lightBoxURL;
        data = res.find(
          (obj: any) =>
            obj.key.toLocaleLowerCase() === 'currency'.toLocaleLowerCase()
        );
        if (data) this.currency = data.displayName;
        data = res.find(
          (obj: any) =>
            obj.key.toLocaleLowerCase() === 'countryCode'.toLocaleLowerCase()
        );
        if (data) this.requestData.countryCode = data.displayName;
        data = res.find(
          (obj: any) =>
            obj.key.toLocaleLowerCase() === 'countryphone'.toLocaleLowerCase()
        );
        if (data) {
          this.requestData.dropOffContactInfo.countryCode = data.displayName;
          this.requestData.pickupContactInfo.countryCode = data.displayName;
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
    this.sessionId = localStorage.getItem('sessionId');
    this.userDetails = this.store.get('profile');

    this.refreshSubscription = this.refreshSummary.subscribe(()=>{
      this.getDiscountValue();
      this.getFullOrderData()
    })

  }

  ngOnChanges() {

 this.deliveryOptionDetails = this.deliveryOptionDetails?.deliveryOption ? this.deliveryOptionDetails?.deliveryOption : this.deliveryOptionDetails

    if (this.deliveryOptionDetails && this.permissionService.hasPermission('Shipment-Fee')) {
      this.getFullOrderData()
    }
  }

  getPaymentConfiguration(): void {
    if (this.hasAPMPermission()) {
      this.featureFlag = FeatureFlag.APM;
    } else {
      this.featureFlag = FeatureFlag.LIGHTBOX;
      this.setLightboxConfiguration();
    }
  }
  
  
  private loadApmConfiguration(): void {
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    const requestPayload: InitiateApmWidgetRequest = {
      Amount: this.totalOrderAmount.toString(),
      Currency: this.currencyCode,
      OrderId: correctOrderId
    };

    this.paymentService.getApmConfig(requestPayload).subscribe({
      next: (res: any) => {
        if (res?.data) {
            this.setApmConfig(res.data);
        }
      },
      error: (err: any) => {
        console.error('Error fetching APM config:', err);
      }
    });
  }
  private setApmConfig(data: any): void {
    this.apmConfig = {
      Token: data.token,
      Amount: data.amount,
      Currency: data.currency,
      Reference: data.reference,
      Signature: data.signature,
      MerchantId: data.merchantId,
      Timestamp: data.timestamp,
      env: data.env,
    };
    this.paymentStateService.setApmConfig(this.apmConfig);
  }
  
  private setLightboxConfiguration(data?: any): void {
    const configData = data || {};
  
    this.lightboxConfigObj = {
      lightBoxURL: configData.lightBoxURL || this.lightBoxURL,
      // trxDateTime: this.timeString,
      // secureHashAnonymous: this.secureHash,
      defaultPaymentMethod: this.paymentMethodDetails?.toLocaleLowerCase()?.includes('card')
        ? PaymentMethodEnum.Card
        : PaymentMethodEnum.Wallet,
      momoPaySubMerchantsDataStr: JSON.stringify(this.shopsPayments),
      MomoPayCommissionAmount: this.MomoPayCommissionAmount,
      MID: configData.merchantId || this.merchantId,
      TID: configData.terminalId || this.terminalId,
      lang: localStorage.getItem('lang'),
      MerchantReference: this.OrderId ?? '',
      AmountTrxn: this.AmountTrxn,
      AdditionalCustomerData: {
        CustomerMobile: this.userPhoneNumber,
      },
      MomoPayLogisticFees: this.totalLogisticsFee,
      MomoPayDiscounts: -this.totalDiscount,
      paymentMethodFromLightBox: this.totalDiscount
        ? this.allowedpaymentMethod
        : PaymentMethodEnum.Both,
      TrxDateTime: this.timeString,
      SecureHash: this.secureHash,
    };
  }
  
  private hasAPMPermission(): boolean {
    return this.permissionService.hasPermission('AllowAPMIntegration');
  }
   
  onResize(event?: any) {
    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth
    }
  }

  /**
   * Generate a unique tab identifier to prevent cross-tab state contamination
   */
  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Store OrderId in session storage with tab-specific key to prevent cross-tab issues
   */
  private storeTabSpecificOrderId(orderId: string): void {
    if (isPlatformBrowser(this.platformId)) {
      sessionStorage.setItem(`orderData_${this.tabId}`, JSON.stringify({
        orderId: orderId,
        timestamp: Date.now()
      }));
      this.sessionOrderId = orderId;
    }
  }

  /**
   * Retrieve OrderId from session storage for this specific tab
   */
  private getTabSpecificOrderId(): string | null {
    if (isPlatformBrowser(this.platformId)) {
      const storedData = sessionStorage.getItem(`orderData_${this.tabId}`);
      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          return parsed.orderId;
        } catch (e) {
          console.error('Error parsing tab-specific order data:', e);
        }
      }
    }
    return null;
  }

  /**
   * Get the correct OrderId for this tab, with fallback chain
   * This ensures all API calls use the tab-specific OrderId
   */
  private getCorrectOrderId(): string {
    return this.getTabSpecificOrderId() || this.sessionOrderId || this.OrderId;
  }

  async getOrderData() {
    this.store.subscription('orderData').subscribe({
      next: (res: any) => {
        if (res) {
          this.orderDetails = res;
          // Store the OrderId for this specific tab session
          if (res.orderId) {
            this.storeTabSpecificOrderId(res.orderId);
          }
          if(this.orderDetails.productDetails.length>0){
            this.currencyCode = this.orderDetails.productDetails[0].currencyCode;
          }
          this.getCustomerAddress();
          this.getCustomerPhone();
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });

  }

getCustomerAddress() {
    this.addressService.getAddress().subscribe({
      next: (res: any) => {

        if (res.data.records.length > 0) {
          this.addressService.chosenAddress = res.data.records[0];
          if((!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === "") || (!this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === "")) {
            this.messageService.add({
              severity: 'info',
              summary: this.translate.instant('ResponseMessages.address'),
              detail: this.translate.instant(
                'ResponseMessages.invalidCityAddress'
              ),
            });

            this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], { queryParams: { returnUrl: '/checkout' } });
            return;
          }
          if (!this.deliveryOptionDetails && !this.permissionService.hasPermission('Shipment-Fee')) {
            this.getFullOrderData();

          }
        } else {
          this.messageService.add({
            severity: 'info',
            summary: this.translate.instant('ResponseMessages.address'),
            detail: this.translate.instant(
              'ResponseMessages.pleaseProvideYourAddress'
            ),
          });

          this.router.navigate(['/account/address'], { queryParams: { returnUrl: '/checkout' } });
          return;
        }

        this.addressService.loadedAddress = true;
      },
      error: (err: any) => {

        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.fetchError'),
          detail: err.message,
        });
      },
    });
  }

getFullOrderData() {
    if(this.deliveryOptionDetails?.id ){
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    let data: any = {
      OrderId: parseInt(correctOrderId),
      AddressId: this.addressService.chosenAddress.id,
    };

    data['deliveryOptionId'] = this.deliveryOptionDetails?.id || data['deliveryOptionId'];

    this.orderService.GetOrderWithPaymentsConfigurations(data).subscribe({
      next: (res: any) => {
        if (res?.success) {
          this.isProceccedCheckOut = true;
          this.isFetchOrderPaymentConfig = true

          this.orderDetailsWithConfig = res.data;
          this.lightBoxURL = res.data.lightBoxURL;
          this.secureHash = res.data.secureHash;
          this.timeString = res.data.timeString;
          console.log('API Response - secureHash:', res.data.secureHash, 'timeString:', res.data.timeString);
          this.merchantId = res.data.merchantId;
          this.terminalId = res.data.terminalId;
          this.subOrderDetails = res.data.shopsDetails;
          this.shopsPayments = res.data.shopsPayments;
          this.totalLogisticsFee = res.data.totalLogisticsFee;
          this.subOrdersCommission = res.data.SubOrdersCommission;
          this.AmountTrxn = res.data.amountTrxn;
          this.OrderId = this.orderDetails.orderId;
          this.OrderShopId = this.orderDetails.shopId;
          this.MomoPayCommissionAmount = res.data.momoPayCommissionAmount;
          this.shipmentService.shipmentCost = res.data.totalDeliveryCost;
          this.shipmentService.actualShipmentFee = res.data.calculateShipmentFeeRes ? res.data.calculateShipmentFeeRes.adjustedShipmentFee : 0
          this.shipmentFee = 0;
          this.totalDiscount = res.data.totalDiscount;
          this.allowedpaymentMethod = res.data?.allowedpaymentMethod;
          if (res.data?.shopsDetails.length) {
            res.data?.shopsDetails.forEach((item: any) => {
              if (item.shipmentFee) {
                this.shipmentFee = this.shipmentFee + item.shipmentFee
              }
            })
          }
          this.maxTimeDelivery = res.data.maxTimeDelivery;
          this.minTimeDelivery = res.data.minTimeDelivery;


          this.shipmentService.currentShipment = {
            totalDeliveryCost: Number(this.shipmentService.shipmentCost),
            shipmentDetails: this.shipmentDetails,
            maxTime: this.maxTimeDelivery,
            minTime: this.minTimeDelivery,
          };
          this.showPayButton = true;
          this.store.set('shipmentCost', {
            totalDeliveryCost: res.data.totalDeliveryCost,
            shipmentDetails: this.shipmentDetails,
            deliveryOption: this.deliveryOptionDetails,
            isApplyShippingFeeDiscount: res.data.isApplyShippingFeeDiscount 
          });

          this.UpdateOrder();
          this.getPaymentConfiguration();
        } else {
          this.isProceccedCheckOut = false;
          if (res?.message != null) {
            if(res.message === 'City is not defined in any region. Please update your address.' ||
              (((!this.addressService.chosenAddress.region || this.addressService.chosenAddress.region === "") ||
                (!this.addressService.chosenAddress.city || this.addressService.chosenAddress.city === "")))) {
              this.messageService.add({
                severity: 'info',
                summary: this.translate.instant('ResponseMessages.address'),
                detail: this.translate.instant(
                  'ResponseMessages.invalidCityAddress'
                ),
              });

              this.router.navigate(['/account/address/' + this.addressService.chosenAddress.id], { queryParams: { returnUrl: '/checkout' } });
              return;
            } else {
              this.messageService.add({
                severity: 'error',
                summary: this.translate.instant('ErrorMessages.fetchError'),
                detail: res.message
              });
            }
          }
        }
      },
      error: (err: any) => {
        this.isProceccedCheckOut = false;
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.fetchError'),
          detail: this.translate.instant(
            'ErrorMessages.pleaseContactCallCenter'
          ),
        });
        this.router.navigate(['/cart']);
      },
    });
  }
  }

 show() {

  if (this.isGoogleAnalytics) {
      this.$gaService.event(
          GaLocalActionEnum.CLICK_ON_PROCEED_TO_PAYMENT, 
          'checkout',                                   
          'PROCEED_TO_PAYMENT',                        
          1,                                           
          true,                                        
          {
              "order_amount": this.shipmentService.currentOrderTotal + this.shipmentService.shipmentCost,
              "order_ID": this.OrderId,
              "user_ID": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',
              "session_ID": this.sessionId,
              "ip_Address": this.store.get('userIP'),
              "device_Type": this.store.get('deviceInfo')?.deviceType,
              "device_Id": this.store.get('deviceInfo')?.deviceId,
              "order_commission": this.MomoPayCommissionAmount,
              "merchant_ID": this.merchantId,
              "shipping_fee": this.shipmentFee,
              "order_totalItems": this.orderDetails?.productDetails?.length,
              "payment_method": this.paymentMethodDetails,
              "suborder_commission": this.subOrdersCommission,
              "delivery_option": this.deliveryOptionDetails.name
          }
      );
  }
   if (this.hasAPMPermission()) {
     this.loadApmConfiguration();
   }
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    forkJoin([
    this.authService.getPhoneNumbers(),
    this.authService.PromotionStockCheck(parseInt(correctOrderId)),
      this.orderService.verifyOrderProductsVisibilityBeforeCheckout(
          {OrderId: parseInt(correctOrderId)}
      )
  ]).subscribe({
    next: ([phoneNumberRes, promotionStockCheck, visibilityCheck]: [any, any, any]) => {
      let promotionStockStatus:any;
      if(promotionStockCheck.data){
       promotionStockStatus = promotionStockCheck.data.promotionalStockAvailable;
      }else{
         this.messageService.add({
          severity: 'error',
          summary: promotionStockCheck.message,
        });
      }
      let isValidOrder = visibilityCheck?.success;

          if(!promotionStockStatus ){
        this.router.navigateByUrl('/cart');
      }

          if (!isValidOrder ) {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ResponseMessages.orderIsInValid'),
        });
        this.router.navigateByUrl('/cart');
      }

          else if (phoneNumberRes.success && promotionStockStatus && isValidOrder) {
        this.displayLoaderModal = true;
        this.showPayButton = false;

        if (this.isProceccedCheckOut) {
          if (this.isProceccedCheckOut) {
            if (this.hasAPMPermission()) {
              this.paymentStateService.apmConfig$
              .pipe(
                filter((config): config is InitiateApmWidgetResponse => !!config), // skip null
                take(1)
              ).subscribe((config) => {
                if (config) {this.paymentMethodWrapper?.startPayment();}
              });
            } else {
              this.paymentMethodWrapper?.startPayment();
            }
          }
        }
      }
    },
    error: (err) => {
      console.error('Error in API calls', err);
      },
      complete: () => {
    }
  });

}

handlePaymentComplete(data: any) {
  this.loaderService.hide();

  // Use the tab-specific OrderId to ensure we're updating the correct order
  const correctOrderId = this.getCorrectOrderId();

  const transactionData = {
    TransactionId: data.SystemReference,
    CardNumber: data.PayerAccount,
    PaymentMethod: data.PaidThrough === 'Card' ? 1 : 2,
    UpdateProductQuantityList: null,
    OrderId: correctOrderId,
    OrderShopId: this.OrderShopId,
  };
  this.PaymentSuccess(transactionData);
  this.onDeleteCart();

  // Use the correct OrderId for status update, not the potentially overwritten one from orderDetails
  this.orderService.updateOrderStatus(parseInt(correctOrderId))
    .pipe(take(1))
    .subscribe(() => this.showPayButton = true);
}

handlePaymentCancel() {
  this.loaderService.hide();
  this.showPayButton = true;
}

handlePaymentError() {
  this.loaderService.hide();
  this.showPayButton = true;
}

PaymentSuccess(transactionData: any) {
    this.store.set('transactionData', transactionData);
    this._GACustomEvent.purchaseEvent(
      this.cartItems,
      transactionData.TransactionId,
      this.shipmentFee,
      this.currencyCode,
      this.deliveryOptionDetails?.name, 
      this.getCurrentCouponCode()
    )
    this.router.navigate(['/checkout/success']);
  }

  private getCurrentCouponCode(): string | undefined {
    const promoCodeComponent = document.querySelector('app-promo-code') as any;
    return promoCodeComponent?.discount || undefined;
  }

getProductShopLatLng(
    transactionId: any,
    shopId: any,
    orderId: any,
    productId: any,
    cost: any
  ) {
    this.shopService.getShopById(shopId).subscribe({
      next: (res: any) => {
        if (res.data === null) {
          return;
        }

        let location = [res.data.lat, res.data.lng];
        this.requestData.pickupContactInfo.addressLatLng =
          '[' + location.toString() + ']';
        this.requestData.dropOffContactInfo.addressLatLng =
          '[' +
          [
            this.addressService.chosenAddress.lat,
            this.addressService.chosenAddress.lng,
          ].toString() +
          ']';

      },
      error: (err: any) => {
      },
    });
  }

  UpdateOrder() {
    let applyTo: any = null
    let deliveryOption: any = null
    if (this.deliveryOptionDetails) {
      if (this.deliveryOptionDetails.applyTo) {
        applyTo = this.deliveryOptionDetails.applyTo
      }
      if (this.deliveryOptionDetails.applyTo) {
        deliveryOption = this.deliveryOptionDetails.id
      }
    }
    this.orderService
      .updateOrder({
        id: this.orderDetails.orderId,
        TransferReferenceId: this.paymentResult?.SystemReference,
        totalDeliveryCost: this.shipmentService.shipmentCost,
        shipmentFee: this.shipmentFee,
        applyTo: applyTo,
        deliveryOption: deliveryOption,
        total: this.totalOrderAmount,
        addressId: this.addressService.chosenAddress.id,
        subOrderDetails: this.subOrderDetails,
        StreetAddress: this.addressService.chosenAddress.streetAddress,
        State: this.addressService.chosenAddress.state,
        Floor: this.addressService.chosenAddress.floor,
        BuldingNumber: this.addressService.chosenAddress.buldingNumber,
        CountryName: this.addressService.chosenAddress.countryName,
        City: this.addressService.chosenAddress.city,
        LandMark: this.addressService.chosenAddress.landMark,
      })
      .subscribe({
        next: (res: any) => {


        },
        error: (err: any) => {
        },
      });
  }

  onDeleteCart() {
    let cartId: any = localStorage.getItem('cartId')
    if (!cartId || cartId == '') {
      cartId = this.cartId
    }
    if (this.isGoogleAnalytics &&  this.permissionService.getTagFeature('delete_cart')) {
      this.$gaService.event('delete_cart', 'cart', cartId);
    }
    this.mainDataService.setCartLenghtData(0);
    this.mainDataService.setCartItemsData([]);
    this.productLogicService.emptyCart(cartId);
  }

  getCurrentCartId() {
    this.store.subscription('cartProducts').subscribe({
      next: (res: any) => {

        this.cartId = res[0]?.cartId;
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  getCustomerPhone() {
    this.authService.getPhoneNumbers().subscribe({
      next: (res: any) => {
        if(res.data && res.data.records && res.data.records.length) {
          this.userPhoneNumber = res.data.records.map((obj: any) => obj.phoneNumber)
          this.requestData.dropOffContactInfo.phoneNumber = res.data.records.map((obj: any) => obj.phoneNumber)
        } else {
          this.userPhoneNumber = localStorage.getItem('phoneNumberArray')
        }
      }
    })
  }

  getDiscountValue() {
    // Use tab-specific OrderId to prevent cross-tab contamination
    const correctOrderId = this.getCorrectOrderId();
    this.orderService.getOrderDiscount(parseInt(correctOrderId)).subscribe({
      next: (res) => {
        if (res.success) {
          this.orderDiscount = res.data >= this.orderDetails.orderAmount ? this.orderDetails.orderAmount : res.data ;
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  /**
   * Clean up tab-specific session storage
   */
  private cleanupTabSpecificData(): void {
    if (isPlatformBrowser(this.platformId)) {
      sessionStorage.removeItem(`orderData_${this.tabId}`);
    }
  }

  ngOnDestroy() {
    this.addressSubscription.unsubscribe()
    this.mobileSubscription.unsubscribe()
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
    this.paymentStateService.clearPaymentState();

    // Clean up tab-specific data
    this.cleanupTabSpecificData();
  }

}
