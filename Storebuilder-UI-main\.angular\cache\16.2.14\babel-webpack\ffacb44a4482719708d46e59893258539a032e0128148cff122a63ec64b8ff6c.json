{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { routes } from \"./routes\";\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { FormsModule } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let OrderRefundModule = /*#__PURE__*/(() => {\n  class OrderRefundModule {\n    static ɵfac = function OrderRefundModule_Factory(t) {\n      return new (t || OrderRefundModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: OrderRefundModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, DialogModule, ButtonModule, CheckboxModule, InputTextareaModule, RouterModule.forChild(routes), BreadcrumbModule, FormsModule]\n    });\n  }\n  return OrderRefundModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}