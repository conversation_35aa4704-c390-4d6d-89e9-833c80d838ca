.category-card {
  width: 120px;
  height: 160px;
  -webkit-box-shadow: 6px 0 4px -4px #dbd5d5a1, -6px 0 4px -4px #dbd5d5a1 !important;
  border-radius: 15px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  max-height: 200px;

  background-color: #ffffff;


  @media screen and (max-width: 768px) {
    width: 82px;
    height: 109px;
    filter: drop-shadow(0px 0px 5px rgba(176, 64, 108, 0.16));
    margin-top: 2px
  }


  img {
    width: 64px;
    @media screen and (max-width: 768px) {
      width: 44px;
      height: 44px;
    }
    height: 64px;
    object-fit: contain;
    padding-top: 12px;
  }

  .title {
    font-size: 16px;
    font-family: var(--medium-font);
    color: black;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 16px;
    word-break: break-word;
    height: 30px;
  }

  .total-items {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 14px;
    color: var(--header_bgcolor);
    font-family: var(--medium-font);
  }
}

@media only screen and (max-width: 768px) {
  .title {
    font-size: 10px !important;
    font-weight: 500;
    font-family: var(--medium-font);
    color: black !important;
    text-align: center !important;
    width: 69px !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    margin-top: 7px !important;


  }
  .total-items {

    font-size: 8px !important;
    color: var(--header_bgcolor);
  }
}

