{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { DialogModule } from 'primeng/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/dialog\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction ConfirmationDeleteDialogComponent_ng_template_1_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"deleteItemPopupComponent.deleteNumber\"), \" \", i0.ɵɵpipeBind1(3, 4, \"deleteItemPopupComponent.?\"), \" \");\n  }\n}\nfunction ConfirmationDeleteDialogComponent_ng_template_1_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(2, 2, \"deleteItemPopupComponent.deleteAddress\"), \" \", i0.ɵɵpipeBind1(3, 4, \"deleteItemPopupComponent.?\"), \" \");\n  }\n}\nfunction ConfirmationDeleteDialogComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 2)(1, \"div\")(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"img\", 5);\n    i0.ɵɵelementStart(5, \"p\", 6);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ConfirmationDeleteDialogComponent_ng_template_1_p_8_Template, 4, 6, \"p\", 7);\n    i0.ɵɵtemplate(9, ConfirmationDeleteDialogComponent_ng_template_1_p_9_Template, 4, 6, \"p\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"button\", 9)(12, \"span\", 10);\n    i0.ɵɵlistener(\"click\", function ConfirmationDeleteDialogComponent_ng_template_1_Template_span_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.OnDelete());\n    });\n    i0.ɵɵtext(13, \"Delete\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 11)(15, \"span\", 10);\n    i0.ɵɵlistener(\"click\", function ConfirmationDeleteDialogComponent_ng_template_1_Template_span_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeModal());\n    });\n    i0.ɵɵtext(16, \"Cancel\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(7, 4, \"deleteItemPopupComponent.delete\"), \" \", ctx_r0.data, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.mobile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.addresses);\n  }\n}\nexport class ConfirmationDeleteDialogComponent {\n  showDialog = false;\n  data;\n  mobile;\n  addresses;\n  update = new EventEmitter();\n  ngOnInit() {\n    /**/\n  }\n  closeModal(modalId) {\n    this.update.emit('cancel');\n  }\n  OnDelete() {\n    this.update.emit('delete');\n  }\n  static ɵfac = function ConfirmationDeleteDialogComponent_Factory(t) {\n    return new (t || ConfirmationDeleteDialogComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ConfirmationDeleteDialogComponent,\n    selectors: [[\"app-confirmation-delete-dialog\"]],\n    inputs: {\n      showDialog: \"showDialog\",\n      data: \"data\",\n      mobile: \"mobile\",\n      addresses: \"addresses\"\n    },\n    outputs: {\n      update: \"update\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[1, \"confirmation-modal\", 3, \"visible\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"onHide\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"id\", \"delete-item-popup\"], [1, \"modal-body\"], [1, \"text-center\"], [\"alt\", \"No Image\", \"src\", \"assets/images/delete.svg\", 1, \"pb-3\", \"img-fluid\", 2, \"width\", \"30px\", \"margin\", \"0 auto\"], [1, \"heading\"], [\"class\", \"desc\", 4, \"ngIf\"], [1, \"mt-2\", \"flex\", \"flex-row\", \"justify-content-between\", \"mt-3\", \"gap-3\"], [\"type\", \"button\", 1, \"p-element\", \"second-btn\", \"p-button\", \"p-component\", \"ng-star-inserted\", \"width-50\"], [1, \"p-button-label\", \"text-btn\", 3, \"click\"], [\"type\", \"button\", 1, \"p-element\", \"ml-1\", \"width-50\", \"main-btn\", \"p-button\", \"p-component\", \"ng-star-inserted\", \"uppercase\"], [1, \"desc\"]],\n    template: function ConfirmationDeleteDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"onHide\", function ConfirmationDeleteDialogComponent_Template_p_dialog_onHide_0_listener() {\n          return ctx.closeModal();\n        })(\"visibleChange\", function ConfirmationDeleteDialogComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.showDialog = $event;\n        });\n        i0.ɵɵtemplate(1, ConfirmationDeleteDialogComponent_ng_template_1_Template, 17, 6, \"ng-template\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.showDialog)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n      }\n    },\n    dependencies: [CommonModule, i1.NgIf, DialogModule, i2.Dialog, i3.PrimeTemplate, TranslateModule, i4.TranslatePipe],\n    styles: [\".confirmation-modal[_ngcontent-%COMP%] {\\n  width: \\\"25vw\\\";\\n}\\n\\n.second-btn[_ngcontent-%COMP%], .main-btn[_ngcontent-%COMP%] {\\n  border-radius: 6px !important;\\n  text-transform: uppercase;\\n}\\n\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\\n  width: auto;\\n  margin-top: 20px;\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%] {\\n  font-style: normal;\\n  font-variant-caps: normal;\\n  font-variant-ligatures: normal;\\n  font-variant-numeric: normal;\\n  font-variant-east-asian: normal;\\n  font-weight: bold;\\n  font-stretch: normal;\\n  font-size: 16px;\\n  line-height: 22px;\\n  letter-spacing: 0px;\\n  color: #2c2738;\\n  font-family: var(--medium-font);\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font);\\n  letter-spacing: 0px;\\n  color: #000;\\n  text-align: center;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  border: 1px solid #e3e6ea;\\n  margin-top: 32px;\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font);\\n  margin: 0px 25px;\\n  justify-content: center;\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  width: 148px !important;\\n  height: 43px;\\n  font-style: normal;\\n  font-variant-caps: normal;\\n  font-variant-ligatures: normal;\\n  font-variant-numeric: normal;\\n  font-variant-east-asian: normal;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-size: 1rem;\\n  line-height: 14px;\\n  letter-spacing: 0px;\\n  outline: none;\\n  margin-right: 10px;\\n  color: #004F71;\\n  background-color: #ffffff !important;\\n  border: 1px solid #004F71 !important;\\n  border-radius: 25px;\\n  font-family: var(--medium-font);\\n  padding: 10px 20px;\\n  margin-left: 15px !important;\\n}\\n#delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%]   .yes-delete[_ngcontent-%COMP%] {\\n  background-color: #1d4c69;\\n  box-shadow: 0px 2px 4px rgba(44, 39, 56, 0.0784313725);\\n  border-radius: 3px;\\n  width: 148px !important;\\n  margin: 0px 10px 0px 10px;\\n  height: 40px;\\n  font-style: normal;\\n  font-variant-caps: normal;\\n  font-variant-ligatures: normal;\\n  font-variant-numeric: normal;\\n  font-variant-east-asian: normal;\\n  font-weight: bold;\\n  font-stretch: normal;\\n  font-size: 12px;\\n  line-height: 18px;\\n  letter-spacing: 0px;\\n  color: #ebf4f8;\\n}\\n\\n@media screen and (max-width: 620px) {\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%] {\\n    font-family: var(--medium-font);\\n    font-style: normal;\\n    font-variant-caps: normal;\\n    font-variant-ligatures: normal;\\n    font-variant-numeric: normal;\\n    font-variant-east-asian: normal;\\n    font-weight: bold;\\n    font-stretch: normal;\\n    font-size: 18px;\\n    line-height: 22px;\\n    letter-spacing: 0px;\\n    color: #2c2738;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%] {\\n    font-family: var(--medium-font);\\n    font-style: normal;\\n    font-variant-caps: normal;\\n    font-variant-ligatures: normal;\\n    font-variant-numeric: normal;\\n    font-variant-east-asian: normal;\\n    font-stretch: normal;\\n    letter-spacing: 0px;\\n    font-weight: 400;\\n    font-size: 15px;\\n    line-height: 19px;\\n    text-align: center;\\n    color: #A3A3A3;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    border: 1px solid #e3e6ea;\\n    margin-top: 32px;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%] {\\n    font-family: var(--medium-font);\\n    margin: 0 auto;\\n    justify-content: center;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 43px;\\n    font-style: normal;\\n    font-variant-caps: normal;\\n    font-variant-ligatures: normal;\\n    font-variant-numeric: normal;\\n    font-variant-east-asian: normal;\\n    font-weight: normal;\\n    font-stretch: normal;\\n    font-size: 1rem;\\n    line-height: 14px;\\n    letter-spacing: 0px;\\n    outline: none;\\n    color: #004F71;\\n    background-color: #ffffff !important;\\n    border: 1px solid #004F71 !important;\\n    border-radius: 25px;\\n    font-family: var(--medium-font);\\n    padding: 10px 20px;\\n    margin-left: 15px !important;\\n  }\\n  #delete-item-popup[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .deleteButtons[_ngcontent-%COMP%]   .yes-delete[_ngcontent-%COMP%] {\\n    background-color: #1d4c69;\\n    box-shadow: 0px 2px 4px rgba(44, 39, 56, 0.0784313725);\\n    border-radius: 3px;\\n    width: 100px;\\n    height: 40px;\\n    font-style: normal;\\n    font-variant-caps: normal;\\n    font-variant-ligatures: normal;\\n    font-variant-numeric: normal;\\n    font-variant-east-asian: normal;\\n    font-weight: bold;\\n    font-stretch: normal;\\n    font-size: 12px;\\n    line-height: 18px;\\n    letter-spacing: 0px;\\n    color: #ebf4f8;\\n  }\\n}\\n.confirm-btn[_ngcontent-%COMP%] {\\n  width: 148px !important;\\n  height: 43px;\\n  color: #ffffff;\\n  background-color: #004F71 !important;\\n  border-radius: 25px;\\n  border: 1px solid #004F71;\\n  font-family: var(--medium-font);\\n  padding: 10px 20px;\\n  outline: 0 none;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: #ffffff !important;\\n  border: 1px solid #004F71 !important;\\n  border-radius: 25px;\\n  font-family: var(--medium-font);\\n  padding: 10px 20px;\\n  outline: 0 none;\\n  height: 43px;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  width: 118px;\\n  height: 35px;\\n}\\n\\n  .p-dialog-content {\\n  border-bottom: none !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .text-btn[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    font-family: var(--regular-font);\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}