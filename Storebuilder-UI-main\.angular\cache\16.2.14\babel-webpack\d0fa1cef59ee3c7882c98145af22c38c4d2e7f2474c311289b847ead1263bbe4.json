{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport UtilityFunctions from '@core/utilities/functions';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/gtm.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../../shared/components/product-card/product-card.component\";\nimport * as i10 from \"primeng/progressspinner\";\nimport * as i11 from \"primeng/breadcrumb\";\nimport * as i12 from \"@pages/category-products/components/category-not-found/category-not-found.component\";\nfunction IndexComponent_ng_container_0_section_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r5.getBannerImages(ctx_r5.categoryBanner == null ? null : ctx_r5.categoryBanner.desktopBanner), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 17);\n    i0.ɵɵelement(1, \"app-mtn-product-card\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"isLayoutTemplate\", !ctx_r9.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"currency\", ctx_r9.currency)(\"product\", product_r10)(\"categoryName\", ctx_r9.categoryName);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_0_section_1_div_4_div_5_a_1_Template, 2, 5, \"a\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.products);\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IndexComponent_ng_container_0_section_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, IndexComponent_ng_container_0_section_1_div_4_div_5_Template, 2, 1, \"div\", 13);\n    i0.ɵɵtemplate(6, IndexComponent_ng_container_0_section_1_div_4_div_6_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.category ? ctx_r6.category.categoryName : ctx_r6.categoryName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.products && ctx_r6.products.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.showProductSpinner);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"category-products-page\": a0,\n    \"hidden-navbar\": a1\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"breadcrumb\": a0,\n    \"hiddenNavbarBreadcrum\": a1\n  };\n};\nfunction IndexComponent_ng_container_0_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 4)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"p-breadcrumb\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, IndexComponent_ng_container_0_section_1_div_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵtemplate(4, IndexComponent_ng_container_0_section_1_div_4_Template, 7, 3, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c0, ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive, !(ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive, !(ctx_r3.navbarData == null ? null : ctx_r3.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"home\", ctx_r3.home)(\"model\", ctx_r3.breadItems);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.categoryBanner == null ? null : ctx_r3.categoryBanner.isBanner) && (ctx_r3.categoryBanner == null ? null : ctx_r3.categoryBanner.desktopBanner));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.products && ctx_r3.products.length);\n  }\n}\nfunction IndexComponent_ng_container_0_app_category_not_found_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-category-not-found\");\n  }\n}\nfunction IndexComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_0_section_1_Template, 5, 12, \"section\", 2);\n    i0.ɵɵtemplate(2, IndexComponent_ng_container_0_app_category_not_found_2_Template, 1, 0, \"app-category-not-found\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isBlank);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isBlank && ctx_r0.products && ctx_r0.products.length === 0);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r13.getBannerImages(ctx_r13.categoryBanner == null ? null : ctx_r13.categoryBanner.mobileBanner), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_9_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 31);\n    i0.ɵɵelement(1, \"app-mtn-product-card\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r18 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"isLayoutTemplate\", !ctx_r17.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"currency\", ctx_r17.currency)(\"product\", product_r18)(\"categoryName\", ctx_r17.categoryName);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_section_0_div_2_div_9_a_1_Template, 2, 5, \"a\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.products);\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IndexComponent_ng_template_1_section_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24)(5, \"div\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 26);\n    i0.ɵɵelement(8, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, IndexComponent_ng_template_1_section_0_div_2_div_9_Template, 2, 1, \"div\", 28);\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_1_section_0_div_2_div_10_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.category ? ctx_r14.category.categoryName : ctx_r14.categoryName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.products && ctx_r14.products.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.showProductSpinner);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"margin-top\": a0\n  };\n};\nfunction IndexComponent_ng_template_1_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 21);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_section_0_div_1_Template, 2, 1, \"div\", 7);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_1_section_0_div_2_Template, 11, 3, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(3, _c2, ctx_r11.isMobileLayout ? \"65px\" : \"210px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.categoryBanner == null ? null : ctx_r11.categoryBanner.isBanner) && (ctx_r11.categoryBanner == null ? null : ctx_r11.categoryBanner.mobileBanner));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.products && ctx_r11.products.length);\n  }\n}\nfunction IndexComponent_ng_template_1_app_category_not_found_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-category-not-found\");\n  }\n}\nfunction IndexComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_1_section_0_Template, 3, 5, \"section\", 20);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_app_category_not_found_1_Template, 1, 0, \"app-category-not-found\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isBlank);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isBlank && ctx_r2.products && ctx_r2.products.length === 0);\n  }\n}\nexport class IndexComponent {\n  activatedRoute;\n  productService;\n  store;\n  reviewsService;\n  messageService;\n  translate;\n  ref;\n  loaderService;\n  tenantService;\n  authTokenService;\n  cookieService;\n  mainDataService;\n  router;\n  appDataService;\n  permissionService;\n  $gaService;\n  platformId;\n  $gtmService;\n  categoryId;\n  topNumber;\n  category;\n  categoryName;\n  items = [];\n  breadItems = [];\n  home = {\n    icon: 'pi pi-home',\n    routerLink: '/'\n  };\n  currency = {};\n  baseUrl = environment.apiEndPoint + '/';\n  emptyMsg = 'Your Category Is Empty';\n  subPath;\n  subId;\n  catIds = '';\n  catPaths = '';\n  products;\n  isError = false;\n  isBlank = true;\n  reviews;\n  badgesList = [];\n  rawCategories = [];\n  newCategory = null;\n  token;\n  pageSize = 50;\n  currentPageSize = 50;\n  triggerProductsCall = false;\n  showProductSpinner = false;\n  loadDataType = '';\n  currentPageNumber = 1;\n  total = 0;\n  ignorePagination = false;\n  shouldCallNextFeatureProduct = true;\n  shouldCallNextCategoryProduct = true;\n  navbarData;\n  isLayoutTemplate = false;\n  promotionId;\n  promotionName;\n  screenWidth = window.innerWidth;\n  isMobileView = this.screenWidth <= 786;\n  isGoogleAnalytics = false;\n  userDetails;\n  tagName = GaActionEnum;\n  isMobileLayout = false;\n  categoryBanner;\n  onScroll(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      const scrollPosition = window.scrollY || document.documentElement.scrollTop;\n      const totalHeight = document.documentElement.scrollHeight;\n      const windowHeight = window.innerHeight;\n      const isAtBottom = scrollPosition + windowHeight >= totalHeight - 1;\n      if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {\n        this.loadOnScrollData();\n      }\n    }\n  }\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n    if (this.screenWidth <= 768) {\n      this.isMobileView = true;\n    } else {\n      this.isMobileView = false;\n    }\n  }\n  loadOnScrollData() {\n    if (this.loadDataType === 'category') {\n      if (this.shouldCallNextCategoryProduct) this.loadPaginatedProducts();\n    } else if (this.loadDataType === 'feature') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedFeatureProducts();\n    } else if (this.loadDataType === 'promotion') {\n      if (this.shouldCallNextFeatureProduct) this.loadPaginatedPromotionProducts();\n    }\n  }\n  constructor(activatedRoute, productService, store, reviewsService, messageService, translate, ref, loaderService, tenantService, authTokenService, cookieService, mainDataService, router, appDataService, permissionService, $gaService, platformId, $gtmService) {\n    this.activatedRoute = activatedRoute;\n    this.productService = productService;\n    this.store = store;\n    this.reviewsService = reviewsService;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.ref = ref;\n    this.loaderService = loaderService;\n    this.tenantService = tenantService;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.mainDataService = mainDataService;\n    this.router = router;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.$gtmService = $gtmService;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.scrollToTop();\n  }\n  ngOnInit() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.paramfunc();\n    this.scrollToTop();\n  }\n  paramfunc() {\n    let param;\n    this.activatedRoute.paramMap.subscribe(params => {\n      this.products = [];\n      param = params.get('id');\n      param = param?.split('&');\n      this.userDetails = this.store.get('profile');\n      if (this.router.url.includes('promotion')) {\n        if (param?.length == 1) {\n          this.promotionId = param[0];\n          this.loadDataType = 'promotion';\n          this.triggerAnalytics('promotion', this.promotionId);\n          this.loadPromotionData();\n          // this.$gtmService.pushPageView('promotion',this.categoryName)\n        }\n      } else {\n        if (param?.length == 1) {\n          this.categoryId = param[0];\n          this.loadDataType = 'category';\n          this.triggerAnalytics('category', this.categoryId);\n          // this.$gtmService.pushPageView('category',this.categoryName)\n          this.loadData();\n        } else if (param?.length == 3) {\n          this.categoryId = param[0];\n          this.topNumber = param[1];\n          this.categoryName = param[2];\n          this.loadDataType = 'feature';\n          this.triggerAnalytics('feature', this.categoryId);\n          this.loadSectionData();\n          this.$gtmService.pushPageView('feature', this.categoryName);\n        }\n      }\n    });\n  }\n  loadData() {\n    this.loaderService.show();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, true, true).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res.success) {\n          this.isError = false;\n          this.total = res.data.productsList.records.length;\n          this.categoryBanner = res.data.categoryBanner;\n          res.data?.productsList?.records.forEach(record => {\n            this.addProductFromLoadData(record);\n            this.badgesList = record.badgesList[record]?.desktopImage || [];\n          });\n          this.getAllCategories();\n        }\n        this.loaderService.hide();\n      },\n      error: err => {\n        this.handleError(err);\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.activatedRoute.queryParams.subscribe(res => {\n      this.subPath = res?.path?.split('//');\n      this.subId = res?.id?.split('//');\n    });\n    if (!this.subPath) {\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          res.forEach(element => {\n            if (element.id == this.categoryId) {\n              this.category = element;\n            }\n          });\n          this.activatedRoute.queryParamMap.subscribe(params => {\n            this.categoryName = params.get('categoryName');\n          });\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.items = [{\n        label: this.category?.categoryName\n      }, {\n        label: label\n      }];\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    } else {\n      let label = 'Products';\n      this.translate.get('categoryCard.products').subscribe(data => {\n        label = data;\n      });\n      this.categoryName = this.subPath[this.subPath.length - 1];\n      this.items = [];\n      this.subPath.forEach((x, index) => {\n        this.items.push({\n          label: x,\n          id: this.subId[index]\n        });\n      });\n      this.items.forEach((val, index) => {\n        this.catPaths = index == 0 ? val.label : this.catPaths + '//' + val.label;\n        this.catIds = index == 0 ? val.id : this.catIds + '//' + String(val.id);\n        val.routerLink = `/category/${val.id}`;\n        val.queryParams = {\n          path: this.catPaths,\n          id: this.catIds\n        };\n      });\n      this.items.push({\n        label: label\n      });\n      this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n    }\n  }\n  loadPromotionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        this.categoryName = res.data.promotionName;\n        this.breadItems = [{\n          label: this.categoryName\n        }];\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  handleError(err) {\n    this.isBlank = false;\n    this.isError = true;\n    this.loaderService.hide();\n  }\n  addProductFromLoadData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      badges: record.badgesList,\n      productId: record?.id,\n      productName: record?.name,\n      isLiked: record?.isLiked,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      priceId: selectedVariance?.priceId,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      specProductId: selectedVariance.specProductId,\n      channelId: record.channelId ?? '1',\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      shopId: record.shopId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated\n    };\n    this.products.push(product);\n  }\n  loadSectionData() {\n    this.loaderService.show();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination).subscribe({\n      next: res => {\n        this.products = [];\n        this.isBlank = false;\n        if (res?.data?.records?.length > 0) {\n          this.total = res?.data?.records?.length;\n          res.data?.records.forEach(record => {\n            this.addProductFromLoadSectionData(record);\n          });\n          this.loaderService.hide();\n        }\n      },\n      error: err => {\n        console.error(err);\n        this.isBlank = false;\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n    this.breadItems = [{\n      label: this.categoryName\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.ref.detectChanges();\n    this.ref.markForCheck();\n  }\n  addProductFromLoadSectionData(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    if (selectedVariance) {\n      let features = [];\n      if (selectedVariance?.productFeaturesList) {\n        features = selectedVariance?.productFeaturesList[0]?.featureList;\n      }\n      let product = {\n        badges: record.badgesList,\n        productId: record?.id,\n        productName: record?.name,\n        isLiked: record?.isLiked,\n        priceValue: selectedVariance?.price,\n        priceId: selectedVariance?.priceId,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: record?.currencyCode,\n        masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: record.shopId,\n        specProductId: selectedVariance.specProductId,\n        channelId: record.channelId ?? '1',\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        stockStatus: selectedVariance.stockStatus,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated\n      };\n      if (product.salePriceValue) {\n        product.salePercent = 100 - product.salePriceValue / product.priceValue * 100;\n      }\n      this.products.push(product);\n    }\n  }\n  fetchCategories(category, cat) {\n    if (category.length == 0) {\n      return;\n    }\n    for (const element of category) {\n      if (element.id == this.categoryId) {\n        this.assignBreadCrumbsData(element);\n      }\n      this.fetchCategories(element.categories, element);\n    }\n  }\n  assignBreadCrumbsData(category) {\n    let idsArray = category?.categoryIds?.split(\"->\")?.map(Number);\n    let nameArray = category?.categoryPath?.split(\"->\")?.map(String);\n    let breadCrumbs = [];\n    if (idsArray.length === nameArray.length) {\n      idsArray?.map((e, i) => {\n        breadCrumbs.push({\n          routerLink: '/category/' + e.toString(),\n          label: nameArray[i]\n        });\n      });\n      this.breadItems = breadCrumbs;\n      this.ref.detectChanges();\n      this.ref.markForCheck();\n    }\n    // this.$gtmService.pushPageView(category?.categoryPath)\n    this.$gtmService.pushPageView('category', category?.categoryPath);\n  }\n  logOut() {\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('profile', '');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    this.store.set('cartProducts', []);\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n  }\n  loadPaginatedProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber).subscribe({\n      next: res => {\n        this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;\n        const lastPageSizeProducts = res.data.productsList.records;\n        this.total = lastPageSizeProducts.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadData(record);\n        });\n        this.triggerProductsCall = !res.data.productsList.records.length;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  addProduct(record) {\n    let selectedVariance;\n    let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n    if (defaultVariant) {\n      selectedVariance = defaultVariant;\n    } else {\n      let approvedVariant = record?.productVariances?.find(variant => variant.soldOut);\n      if (approvedVariant) {\n        selectedVariance = approvedVariant;\n      } else {\n        selectedVariance = record?.productVariances[0];\n      }\n    }\n    let features = [];\n    if (selectedVariance?.productFeaturesList) {\n      features = selectedVariance?.productFeaturesList[0]?.featureList;\n    }\n    let product = {\n      productId: record?.id,\n      productName: record?.name,\n      priceValue: selectedVariance?.price,\n      salePriceValue: selectedVariance?.salePrice,\n      currencyCode: record?.currencyCode,\n      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\n      thumbnailImages: selectedVariance?.thumbnailImages,\n      soldOut: selectedVariance?.soldOut,\n      rate: selectedVariance?.rate,\n      count: selectedVariance?.count ?? 0,\n      salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n      channelId: record?.channelId,\n      isHot: features?.includes(1),\n      isNew: features?.includes(2),\n      isBest: features?.includes(3),\n      quantity: selectedVariance.quantity,\n      proSchedulingId: selectedVariance.proSchedulingId,\n      stockPerSKU: selectedVariance.stockPerSKU,\n      stockStatus: selectedVariance.stockStatus,\n      sku: selectedVariance?.sku,\n      skuAutoGenerated: selectedVariance.skuAutoGenerated\n    };\n    this.products.push(product);\n  }\n  loadPaginatedFeatureProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProductFromLoadSectionData(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  loadPaginatedPromotionProducts() {\n    this.triggerProductsCall = true;\n    this.showProductSpinner = true;\n    this.currentPageSize += this.pageSize;\n    this.currentPageNumber += 1;\n    this.ref.detectChanges();\n    this.productService.GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber).subscribe({\n      next: res => {\n        if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\n        const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\n        this.total = res.data?.records?.length;\n        lastPageSizeProducts.forEach(record => {\n          this.addProduct(record);\n        });\n        if (res.data?.records.length) this.triggerProductsCall = false;else this.triggerProductsCall = true;\n        this.showProductSpinner = false;\n        this.ref.markForCheck();\n      }\n    });\n  }\n  getAllCategories() {\n    let allCategories = localStorage.getItem('allCategories');\n    allCategories = JSON.parse(allCategories);\n    this.rawCategories = allCategories;\n    this.rawCategories?.forEach(cat => {\n      if (cat.id == this.categoryId) {\n        this.assignBreadCrumbsData(cat);\n      }\n      cat['path'] = cat.categoryName;\n      cat['catIds'] = cat.id;\n      this.fetchCategories(cat.categories, cat);\n    });\n  }\n  scrollToTop() {\n    if (isPlatformBrowser(this.platformId)) {\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }\n  }\n  triggerAnalytics(type, id) {\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')) {\n      if (type === 'promotion') {\n        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);\n      } else if (type === 'category') {\n        this.$gaService.pageView('/category', 'Category ID: ' + id);\n      } else if (type === 'feature') {\n        this.$gaService.pageView('/category', 'Feature ID: ' + id);\n      }\n      this.$gaService.event(this.tagName.SEARCH, id, type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST', 1, true, {\n        \"id\": id,\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n  }\n  getBannerImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.ReviewsService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.LoaderService), i0.ɵɵdirectiveInject(i2.TenantService), i0.ɵɵdirectiveInject(i2.AuthTokenService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AppDataService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i7.GTMService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-category-products\"]],\n    hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function IndexComponent_scroll_HostBindingHandler($event) {\n          return ctx.onScroll($event);\n        }, false, i0.ɵɵresolveWindow)(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      products: \"products\"\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"mobileView\", \"\"], [\"class\", \"category-products-page\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"category-products-page\", 3, \"ngClass\"], [1, \"breadcrumb\", \"m-0\", 3, \"ngClass\"], [3, \"home\", \"model\"], [\"class\", \"category-banner\", 4, \"ngIf\"], [1, \"category-banner\"], [1, \"category-banner__img\", 3, \"src\"], [1, \"\"], [1, \"col-12\", \"col-md-6\", \"flex\"], [1, \"font-size-22\", \"bold-font\"], [\"class\", \"title-category flex flex-row flex-wrap \", 4, \"ngIf\"], [\"class\", \"spinner-product\", 4, \"ngIf\"], [1, \"title-category\", \"flex\", \"flex-row\", \"flex-wrap\"], [\"class\", \"slide mt-2 mx-1 md:mx-md-2 lg:mx-lg-2 products-margin\", 3, \"isLayoutTemplate\", 4, \"ngFor\", \"ngForOf\"], [1, \"slide\", \"mt-2\", \"mx-1\", \"md:mx-md-2\", \"lg:mx-lg-2\", \"products-margin\"], [3, \"currency\", \"product\", \"categoryName\"], [1, \"spinner-product\"], [\"class\", \"category-products-page\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"category-products-page\", 3, \"ngStyle\"], [1, \"container\", 2, \"padding-top\", \"16px\"], [1, \"row\", 2, \"padding\", \"0px 15px\"], [1, \"col-9\", 2, \"padding-left\", \"0px !important\"], [1, \"font-size-22\", \"font-bold\"], [1, \"col-3\", 2, \"padding-right\", \"0px !important\"], [2, \"float\", \"inline-end\"], [\"class\", \"row mobile-card-div\", 4, \"ngIf\"], [1, \"row\", \"mobile-card-div\"], [\"class\", \"slide mt-2 col-sm-6\", \"style\", \"padding: 0;\", 3, \"isLayoutTemplate\", 4, \"ngFor\", \"ngForOf\"], [1, \"slide\", \"mt-2\", \"col-sm-6\", 2, \"padding\", \"0\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IndexComponent_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n        i0.ɵɵtemplate(1, IndexComponent_ng_template_1_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isMobileView)(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i9.ProductCardComponent, i10.ProgressSpinner, i11.Breadcrumb, i12.CategoryNotFoundComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  width: var(--product-card-width);\\n}\\n@media only screen and (max-width: 767px) {\\n  a[_ngcontent-%COMP%] {\\n    --product-card-width: 43%;\\n    margin: 8px;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 36% !important;\\n  }\\n}\\n@media only screen and (min-width: 1701px) {\\n  a[_ngcontent-%COMP%] {\\n    --product-card-width: 18%;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 11% !important;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  a[_ngcontent-%COMP%] {\\n    width: 18%;\\n  }\\n  a[class*=isLayoutTemplate][_ngcontent-%COMP%] {\\n    --product-card-width: 11% !important;\\n  }\\n}\\n\\n.showProducts[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n}\\n\\n.font-bold[_ngcontent-%COMP%] {\\n  font-family: \\\"main-regular\\\";\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 24px; \\n\\n  text-transform: uppercase;\\n}\\n\\n.mobile-card-div[_ngcontent-%COMP%] {\\n  justify-content: left;\\n  margin: 0 15px;\\n  width: 100%;\\n}\\n\\n@media screen and (min-width: 769px) {\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    padding-left: 11rem !important;\\n    padding-right: 8rem !important;\\n    margin-top: 0px !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .breadcrumb[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .col-12.md\\\\:col-6.flex.justify-content-center.md\\\\:justify-content-start[_ngcontent-%COMP%] {\\n    justify-content: left !important;\\n  }\\n  .content-container.mt-5[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n}\\n.products-margin[_ngcontent-%COMP%] {\\n  padding: 0px 0px 16px 0px !important;\\n}\\n\\n.title-category[_ngcontent-%COMP%] {\\n  margin-top: 5px !important;\\n  margin-bottom: 1rem !important;\\n}\\n\\n@media screen and (min-width: 768px) {\\n  .category-banner[_ngcontent-%COMP%] {\\n    margin-bottom: 24px;\\n  }\\n}\\n.category-banner__img[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "environment", "isPlatformBrowser", "GaActionEnum", "UtilityFunctions", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "getBannerImages", "categoryBanner", "desktopBanner", "ɵɵsanitizeUrl", "ɵɵclassProp", "ctx_r9", "isLayoutTemplate", "currency", "product_r10", "categoryName", "ɵɵtemplate", "IndexComponent_ng_container_0_section_1_div_4_div_5_a_1_Template", "ctx_r7", "products", "ɵɵtext", "IndexComponent_ng_container_0_section_1_div_4_div_5_Template", "IndexComponent_ng_container_0_section_1_div_4_div_6_Template", "ɵɵtextInterpolate1", "ctx_r6", "category", "length", "showProductSpinner", "IndexComponent_ng_container_0_section_1_div_3_Template", "IndexComponent_ng_container_0_section_1_div_4_Template", "ɵɵpureFunction2", "_c0", "ctx_r3", "navbarData", "isActive", "_c1", "home", "breadItems", "isBanner", "ɵɵelementContainerStart", "IndexComponent_ng_container_0_section_1_Template", "IndexComponent_ng_container_0_app_category_not_found_2_Template", "ɵɵelementContainerEnd", "ctx_r0", "isBlank", "ctx_r13", "mobileBanner", "ctx_r17", "product_r18", "IndexComponent_ng_template_1_section_0_div_2_div_9_a_1_Template", "ctx_r15", "IndexComponent_ng_template_1_section_0_div_2_div_9_Template", "IndexComponent_ng_template_1_section_0_div_2_div_10_Template", "ctx_r14", "IndexComponent_ng_template_1_section_0_div_1_Template", "IndexComponent_ng_template_1_section_0_div_2_Template", "ɵɵpureFunction1", "_c2", "ctx_r11", "isMobileLayout", "IndexComponent_ng_template_1_section_0_Template", "IndexComponent_ng_template_1_app_category_not_found_1_Template", "ctx_r2", "IndexComponent", "activatedRoute", "productService", "store", "reviewsService", "messageService", "translate", "ref", "loaderService", "tenantService", "authTokenService", "cookieService", "mainDataService", "router", "appDataService", "permissionService", "$gaService", "platformId", "$gtmService", "categoryId", "topNumber", "items", "icon", "routerLink", "baseUrl", "apiEndPoint", "emptyMsg", "subPath", "subId", "catIds", "catPaths", "isError", "reviews", "badgesList", "rawCategories", "newCategory", "token", "pageSize", "currentPageSize", "triggerProductsCall", "loadDataType", "currentPageNumber", "total", "ignorePagination", "shouldCallNextFeatureProduct", "shouldCallNextCategoryProduct", "promotionId", "promotionName", "screenWidth", "window", "innerWidth", "isMobile<PERSON>iew", "isGoogleAnalytics", "userDetails", "tagName", "onScroll", "event", "scrollPosition", "scrollY", "document", "documentElement", "scrollTop", "totalHeight", "scrollHeight", "windowHeight", "innerHeight", "isAtBottom", "loadOnScrollData", "onResize", "target", "loadPaginatedProducts", "loadPaginatedFeatureProducts", "loadPaginatedPromotionProducts", "constructor", "hasPermission", "scrollToTop", "ngOnInit", "layoutTemplate", "find", "section", "type", "paramfunc", "param", "paramMap", "subscribe", "params", "get", "split", "url", "includes", "triggerAnalytics", "loadPromotionData", "loadData", "loadSectionData", "pushPageView", "show", "getCategoryProducts", "next", "res", "success", "data", "productsList", "records", "for<PERSON>ach", "record", "addProductFromLoadData", "desktopImage", "getAllCategories", "hide", "error", "err", "handleError", "setTimeout", "subscription", "queryParams", "path", "id", "element", "queryParamMap", "console", "label", "x", "index", "push", "val", "String", "GetAllProductsByPrmotion", "addProductFromLoadSectionData", "complete", "detectChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "defaultVariant", "productVariances", "variant", "isDefault", "approvedVariant", "soldOut", "features", "productFeaturesList", "featureList", "product", "badges", "productId", "productName", "name", "isLiked", "priceValue", "price", "salePriceValue", "salePrice", "priceId", "currencyCode", "masterImageUrl", "images", "thumbnailImages", "rate", "count", "specProductId", "channelId", "salePercent", "shopId", "isHot", "isNew", "isBest", "quantity", "proSchedulingId", "stockPerSKU", "stockStatus", "sku", "skuAutoGenerated", "GetAllProductsByFeature", "fetchCategories", "cat", "assignBreadCrumbsData", "categories", "idsArray", "categoryIds", "map", "Number", "nameArray", "categoryPath", "breadCrumbs", "e", "i", "toString", "logOut", "sessionStorage", "clear", "authTokenSet", "delete", "set", "setCartLenghtData", "setUserData", "localStorage", "setItem", "removeItem", "lastPageSizeProducts", "addProduct", "slice", "allCategories", "getItem", "JSON", "parse", "scrollTo", "top", "behavior", "getTagFeature", "pageView", "SEARCH", "mobileNumber", "verifyImageURL", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ProductService", "StoreService", "ReviewsService", "i3", "MessageService", "i4", "TranslateService", "ChangeDetectorRef", "LoaderService", "TenantService", "AuthTokenService", "i5", "CookieService", "MainDataService", "Router", "AppDataService", "PermissionService", "i6", "GoogleAnalyticsService", "i7", "GTMService", "selectors", "hostBindings", "IndexComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "IndexComponent_resize_HostBindingHandler", "IndexComponent_ng_container_0_Template", "IndexComponent_ng_template_1_Template", "ɵɵtemplateRefExtractor", "_r1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\category-products\\components\\index\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\category-products\\components\\index\\index.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, HostListener, Inject, Input, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport {ActivatedRoute, Router} from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { CookieService } from \"ngx-cookie-service\";\r\nimport { environment } from '@environments/environment';\r\n\r\nimport {\r\n  MainDataService,\r\n  AuthTokenService,\r\n  TenantService,\r\n  LoaderService,\r\n  ReviewsService,\r\n  StoreService,\r\n  ProductService, AppDataService, PermissionService\r\n} from \"@core/services\";\r\n\r\nimport { ProductRate, Category, Product, Currency } from '@core/interface';\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaActionEnum, GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\nimport UtilityFunctions from '@core/utilities/functions';\r\n\r\n@Component({\r\n  selector: 'app-category-products',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss'],\r\n})\r\nexport class IndexComponent implements OnInit {\r\n  categoryId: any;\r\n  topNumber: any;\r\n  category!: Category;\r\n  categoryName: any;\r\n  items: MenuItem[] = [];\r\n  breadItems: MenuItem[] = [];\r\n  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };\r\n  currency: Currency = {} as Currency;\r\n  baseUrl: string = environment.apiEndPoint + '/';\r\n  emptyMsg: string = 'Your Category Is Empty';\r\n  subPath: any;\r\n  subId: any;\r\n  catIds: any = '';\r\n  catPaths: any = '';\r\n  @Input() products: Array<Product>;\r\n  isError : boolean = false;\r\n  isBlank : boolean = true;\r\n  reviews: ProductRate[] | undefined;\r\n  badgesList: any[] = [];\r\n\r\n  rawCategories: any = [];\r\n  newCategory: any = null;\r\n  token: any;\r\n  pageSize: number = 50;\r\n  currentPageSize: number = 50;\r\n  triggerProductsCall: boolean = false;\r\n  showProductSpinner: boolean = false;\r\n\r\n  loadDataType: string = '';\r\n  currentPageNumber: number = 1;\r\n  total: number = 0;\r\n  ignorePagination:boolean = false;\r\n  shouldCallNextFeatureProduct: boolean = true;\r\n  shouldCallNextCategoryProduct: boolean = true;\r\n  navbarData: any;\r\n  isLayoutTemplate: boolean = false;\r\n  promotionId:any;\r\n  promotionName:any;\r\n  screenWidth: number = window.innerWidth;\r\n  isMobileView: boolean =this.screenWidth <= 786;\r\n  isGoogleAnalytics: boolean = false\r\n  userDetails: any;\r\n  tagName:any=GaActionEnum;\r\n  isMobileLayout: boolean = false;\r\n  categoryBanner: any;\r\n\r\n  @HostListener('window:scroll', ['$event'])\r\n  onScroll(event: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const scrollPosition = window.scrollY || document.documentElement.scrollTop;\r\n      const totalHeight = document.documentElement.scrollHeight;\r\n      const windowHeight = window.innerHeight;\r\n      const isAtBottom = scrollPosition + windowHeight >= totalHeight-1;\r\n\r\n      if (isAtBottom && !this.triggerProductsCall && this.total >= this.pageSize) {\r\n          this.loadOnScrollData();\r\n      }\r\n    }\r\n  }\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: any) {\r\n    this.screenWidth = event.target.innerWidth;\r\n    if (this.screenWidth <= 768) {\r\n      this.isMobileView = true;\r\n    } else {\r\n      this.isMobileView = false;\r\n    }\r\n\r\n  }\r\n  loadOnScrollData(){\r\n      if (this.loadDataType === 'category' ) {\r\n          if (this.shouldCallNextCategoryProduct) this.loadPaginatedProducts()\r\n      } else if (this.loadDataType === 'feature') {\r\n          if (this.shouldCallNextFeatureProduct) this.loadPaginatedFeatureProducts()\r\n      }\r\n      else if (this.loadDataType === 'promotion') {\r\n          if (this.shouldCallNextFeatureProduct) this.loadPaginatedPromotionProducts()\r\n      }\r\n  }\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private productService: ProductService,\r\n    private store: StoreService,\r\n    private reviewsService: ReviewsService,\r\n    private messageService: MessageService,\r\n    private translate: TranslateService,\r\n    private ref: ChangeDetectorRef,\r\n    private loaderService: LoaderService,\r\n    private tenantService: TenantService,\r\n    private authTokenService: AuthTokenService,\r\n    private cookieService: CookieService,\r\n    private mainDataService: MainDataService,\r\n    private router: Router,\r\n    private appDataService: AppDataService,\r\n    private permissionService: PermissionService,\r\n    private $gaService: GoogleAnalyticsService,\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n    private $gtmService:GTMService\r\n\r\n  ) {\r\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template')\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.scrollToTop()\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');\r\n    this.paramfunc()\r\n    this.scrollToTop()\r\n  }\r\n\r\n  paramfunc() {\r\n\r\n    let param;\r\n    this.activatedRoute.paramMap.subscribe((params) => {\r\n      this.products = [];\r\n      param = params.get('id');\r\n      param = param?.split('&');\r\n      this.userDetails = this.store.get('profile');\r\n      if(this.router.url.includes('promotion')) {\r\n        if (param?.length == 1) {\r\n          this.promotionId = param[0];\r\n          this.loadDataType = 'promotion'\r\n          this.triggerAnalytics('promotion', this.promotionId)\r\n          this.loadPromotionData();\r\n          // this.$gtmService.pushPageView('promotion',this.categoryName)\r\n        }\r\n      }\r\n      else {\r\n        if (param?.length == 1) {\r\n          this.categoryId = param[0];\r\n          this.loadDataType = 'category'\r\n          this.triggerAnalytics('category', this.categoryId)\r\n          // this.$gtmService.pushPageView('category',this.categoryName)\r\n          this.loadData();\r\n        } else if (param?.length == 3) {\r\n          this.categoryId = param[0];\r\n          this.topNumber = param[1];\r\n          this.categoryName = param[2];\r\n          this.loadDataType = 'feature'\r\n          this.triggerAnalytics('feature', this.categoryId)\r\n          this.loadSectionData();\r\n          this.$gtmService.pushPageView('feature',this.categoryName)\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  loadData(): void {\r\n    this.loaderService.show();\r\n    this.productService\r\n      .getCategoryProducts(this.categoryId, this.currentPageSize, true, true)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          if (res.success) {\r\n            this.isError= false;\r\n            this.total = res.data.productsList.records.length;\r\n            this.categoryBanner = res.data.categoryBanner;\r\n            res.data?.productsList?.records.forEach((record: any) => {\r\n              this.addProductFromLoadData(record)\r\n              this.badgesList = record.badgesList[record]?.desktopImage || [];\r\n            })\r\n           this.getAllCategories();\r\n          }\r\n          this.loaderService.hide();\r\n        },\r\n        error: (err: any) => {\r\n          this.handleError(err);\r\n        },\r\n      });\r\n\r\n\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n    this.activatedRoute.queryParams.subscribe((res) => {\r\n      this.subPath = res?.path?.split('//');\r\n      this.subId = res?.id?.split('//');\r\n    });\r\n\r\n    if (!this.subPath) {\r\n      this.store.subscription('categories').subscribe({\r\n        next: (res: any) => {\r\n\r\n\r\n          res.forEach((element: Category) => {\r\n            if (element.id == this.categoryId) {\r\n              this.category = element;\r\n            }\r\n          });\r\n          this.activatedRoute.queryParamMap.subscribe(params => {\r\n\r\n\r\n          this.categoryName = params.get('categoryName')\r\n          })\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n        },\r\n      });\r\n      let label = 'Products';\r\n\r\n      this.translate.get('categoryCard.products').subscribe((data: any) => {\r\n        label = data;\r\n      });\r\n\r\n      this.items = [{ label: this.category?.categoryName }, { label: label }];\r\n      this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    } else {\r\n      let label = 'Products';\r\n\r\n      this.translate.get('categoryCard.products').subscribe((data: any) => {\r\n        label = data;\r\n      });\r\n      this.categoryName = this.subPath[this.subPath.length - 1];\r\n      this.items = [];\r\n      this.subPath.forEach((x: any, index: any) => {\r\n        this.items.push({\r\n          label: x,\r\n          id: this.subId[index]\r\n        });\r\n      });\r\n\r\n      this.items.forEach((val: any, index: any) => {\r\n        this.catPaths = index == 0 ? val.label : this.catPaths + '//' + val.label;\r\n        this.catIds = index == 0 ? val.id : this.catIds + '//' + String(val.id);\r\n        val.routerLink = `/category/${val.id}`\r\n        val.queryParams = { path: this.catPaths, id: this.catIds }\r\n      });\r\n\r\n      this.items.push({\r\n        label: label,\r\n      });\r\n      this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    }\r\n  }\r\n  loadPromotionData(): void {\r\n\r\n    this.loaderService.show();\r\n    this.productService\r\n      .GetAllProductsByPrmotion(this.promotionId, this.currentPageSize, false, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          this.categoryName=res.data.promotionName;\r\n          this.breadItems = [{ label: this.categoryName }];\r\n          if (res?.data?.records?.length > 0) {\r\n            this.total = res?.data?.records?.length;\r\n            res.data?.records.forEach((record: any) => {\r\n              this.addProductFromLoadSectionData(record);\r\n            })\r\n            this.loaderService.hide();\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n          this.isBlank = false;\r\n          this.loaderService.hide();\r\n        },\r\n        complete: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    this.ref.detectChanges();\r\n    this.ref.markForCheck();\r\n  }\r\n\r\n  private handleError(err: any): void {\r\n    this.isBlank = false;\r\n    this.isError = true;\r\n    this.loaderService.hide();\r\n  }\r\n\r\n  addProductFromLoadData(record:any){\r\n    let selectedVariance;\r\n\r\n    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n    if (defaultVariant) {\r\n      selectedVariance = defaultVariant;\r\n    } else {\r\n      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n      if (approvedVariant) {\r\n        selectedVariance = approvedVariant;\r\n\r\n      } else {\r\n        selectedVariance = record?.productVariances[0];\r\n      }\r\n    }\r\n    let features=[];\r\n    if(selectedVariance?.productFeaturesList){\r\n      features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n    }\r\n\r\n    let product:any = {\r\n      badges:record.badgesList,\r\n      productId: record?.id,\r\n      productName: record?.name,\r\n      isLiked:record?.isLiked,\r\n      priceValue: selectedVariance?.price,\r\n      salePriceValue: selectedVariance?.salePrice,\r\n      priceId: selectedVariance?.priceId,\r\n      currencyCode: record?.currencyCode,\r\n      masterImageUrl: record?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),\r\n      thumbnailImages: selectedVariance?.thumbnailImages,\r\n      soldOut: selectedVariance?.soldOut,\r\n      rate: selectedVariance?.rate,\r\n      count: selectedVariance?.count ?? 0,\r\n      specProductId: selectedVariance.specProductId,\r\n      channelId: record.channelId ?? '1',\r\n      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n      shopId: record.shopId,\r\n      isHot:features?.includes(1),\r\n      isNew:features?.includes(2),\r\n      isBest:features?.includes(3),\r\n      quantity:selectedVariance.quantity,\r\n      proSchedulingId:selectedVariance.proSchedulingId,\r\n      stockPerSKU:selectedVariance.stockPerSKU,\r\n      stockStatus:selectedVariance.stockStatus,\r\n      sku:selectedVariance?.sku,\r\n      skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n    }\r\n    this.products.push(product)\r\n  }\r\n\r\n  loadSectionData(): void {\r\n\r\n    this.loaderService.show();\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, false, this.currentPageNumber, 50, false, null, this.ignorePagination)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.products = [];\r\n          this.isBlank = false;\r\n          if (res?.data?.records?.length > 0) {\r\n            this.total = res?.data?.records?.length;\r\n            res.data?.records.forEach((record: any) => {\r\n             this.addProductFromLoadSectionData(record);\r\n            })\r\n            this.loaderService.hide();\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          console.error(err);\r\n          this.isBlank = false;\r\n          this.loaderService.hide();\r\n        },\r\n        complete: () => {\r\n          this.loaderService.hide();\r\n        }\r\n      });\r\n\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('currency').subscribe({\r\n        next: (res) => (this.currency = res),\r\n      });\r\n    }, 10);\r\n\r\n    this.breadItems = [{ label: this.categoryName }];\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n    this.ref.detectChanges();\r\n    this.ref.markForCheck();\r\n  }\r\n\r\n  addProductFromLoadSectionData(record:any){\r\n    let selectedVariance;\r\n  let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n  if (defaultVariant) {\r\n    selectedVariance = defaultVariant;\r\n  } else {\r\n  let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n  if (approvedVariant) {\r\n    selectedVariance = approvedVariant;\r\n\r\n  } else {\r\n  selectedVariance = record?.productVariances[0];\r\n}\r\n\r\n}\r\nif (selectedVariance) {\r\n  let features=[];\r\n  if(selectedVariance?.productFeaturesList){\r\n    features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n  }\r\n  let product:any = {\r\n    badges:record.badgesList,\r\n    productId: record?.id,\r\n    productName: record?.name,\r\n    isLiked:record?.isLiked,\r\n    priceValue: selectedVariance?.price,\r\n    priceId: selectedVariance?.priceId,\r\n    salePriceValue: selectedVariance?.salePrice,\r\n    currencyCode: record?.currencyCode,\r\n    masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\r\n    thumbnailImages: selectedVariance?.thumbnailImages,\r\n    soldOut: selectedVariance?.soldOut,\r\n    rate: selectedVariance?.rate,\r\n    count: selectedVariance?.count ?? 0,\r\n    salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n    shopId: record.shopId,\r\n    specProductId: selectedVariance.specProductId,\r\n    channelId: record.channelId ?? '1',\r\n    isHot:features?.includes(1),\r\n    isNew:features?.includes(2),\r\n    isBest:features?.includes(3),\r\n    quantity:selectedVariance.quantity,\r\n    proSchedulingId:selectedVariance.proSchedulingId,\r\n    stockPerSKU:selectedVariance.stockPerSKU,\r\n    stockStatus: selectedVariance.stockStatus,\r\n    sku:selectedVariance?.sku,\r\n    skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n\r\n  }\r\n  if (product.salePriceValue) {\r\n    product.salePercent = 100 - (product.salePriceValue / product.priceValue * 100);\r\n  }\r\n  this.products.push(product)\r\n  }\r\n  }\r\n\r\n\r\nfetchCategories(category: any, cat: any) {\r\n\r\n    if (category.length == 0) {\r\n      return;\r\n    }\r\n\r\n    for (const element of category) {\r\n      if (element.id == this.categoryId) {\r\n        this.assignBreadCrumbsData(element);\r\n      }\r\n      this.fetchCategories(element.categories, element);\r\n    }\r\n\r\n  }\r\n\r\n  assignBreadCrumbsData(category: any) {\r\n\r\n    let idsArray: any = category?.categoryIds?.split(\"->\")?.map(Number);\r\n\r\n    let nameArray: any = category?.categoryPath?.split(\"->\")?.map(String);\r\n\r\n    let breadCrumbs: any = [];\r\n    if (idsArray.length === nameArray.length) {\r\n      idsArray?.map((e: any, i: any) => {\r\n        breadCrumbs.push({ routerLink: '/category/' + e.toString(), label: nameArray[i] });\r\n      });\r\n      this.breadItems = breadCrumbs;\r\n      this.ref.detectChanges();\r\n      this.ref.markForCheck();\r\n    }\r\n    // this.$gtmService.pushPageView(category?.categoryPath)\r\n    this.$gtmService.pushPageView('category', category?.categoryPath)\r\n\r\n\r\n\r\n  }\r\n\r\n  logOut() {\r\n    sessionStorage.clear();\r\n    this.authTokenService.authTokenSet('');\r\n\r\n\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('profile', '');\r\n    this.mainDataService.setCartLenghtData(null);\r\n    this.mainDataService.setUserData(null);\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    localStorage.setItem('cartId', '');\r\n    this.store.set('cartProducts', []);\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n  }\r\n\r\n  loadPaginatedProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges();\r\n\r\n    this.productService\r\n      .getCategoryProducts(this.categoryId, this.currentPageSize, false, false, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.shouldCallNextCategoryProduct = res.data?.productsList.records.length > 0;\r\n          const lastPageSizeProducts = res.data.productsList.records;\r\n          this.total = lastPageSizeProducts.length;\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProductFromLoadData(record);\r\n          })\r\n          this.triggerProductsCall = !res.data.productsList.records.length;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n\r\n  addProduct(record:any){\r\n    let selectedVariance;\r\n    let defaultVariant = record?.productVariances?.find((variant: any) => variant.isDefault)\r\n    if (defaultVariant) {\r\n      selectedVariance = defaultVariant;\r\n    } else {\r\n      let approvedVariant = record?.productVariances?.find((variant: any) => variant.soldOut);\r\n      if (approvedVariant) {\r\n        selectedVariance = approvedVariant;\r\n      } else {\r\n        selectedVariance = record?.productVariances[0];\r\n      }\r\n    }\r\n    let features=[];\r\n    if(selectedVariance?.productFeaturesList){\r\n      features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n    }\r\n    let product:any = {\r\n      productId: record?.id,\r\n      productName: record?.name,\r\n      priceValue: selectedVariance?.price,\r\n      salePriceValue: selectedVariance?.salePrice,\r\n      currencyCode: record?.currencyCode,\r\n      masterImageUrl: record?.masterImageUrl ?? selectedVariance.images[0],\r\n      thumbnailImages: selectedVariance?.thumbnailImages,\r\n      soldOut: selectedVariance?.soldOut,\r\n      rate: selectedVariance?.rate,\r\n      count: selectedVariance?.count ?? 0,\r\n      salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n      channelId: record?.channelId,\r\n      isHot:features?.includes(1),\r\n      isNew:features?.includes(2),\r\n      isBest:features?.includes(3),\r\n      quantity:selectedVariance.quantity,\r\n      proSchedulingId:selectedVariance.proSchedulingId,\r\n      stockPerSKU:selectedVariance.stockPerSKU,\r\n      stockStatus: selectedVariance.stockStatus,\r\n      sku:selectedVariance?.sku,\r\n      skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n    }\r\n    this.products.push(product)\r\n  }\r\n\r\n  loadPaginatedFeatureProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges()\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\r\n          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\r\n          this.total = res.data?.records?.length;\r\n\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProductFromLoadSectionData(record);\r\n          })\r\n          if (res.data?.records.length) this.triggerProductsCall = false;\r\n          else this.triggerProductsCall = true;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n  loadPaginatedPromotionProducts() {\r\n    this.triggerProductsCall = true\r\n    this.showProductSpinner = true\r\n    this.currentPageSize += this.pageSize;\r\n    this.currentPageNumber += 1;\r\n    this.ref.detectChanges()\r\n    this.productService\r\n      .GetAllProductsByFeature(this.categoryId, this.currentPageSize, true, this.currentPageNumber)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          if (res.data?.records.length == 0) this.shouldCallNextFeatureProduct = false;\r\n          const lastPageSizeProducts = res.data?.records.slice(-this.pageSize);\r\n          this.total = res.data?.records?.length;\r\n\r\n          lastPageSizeProducts.forEach((record: any) => {\r\n            this.addProduct(record);\r\n          })\r\n          if (res.data?.records.length) this.triggerProductsCall = false;\r\n          else this.triggerProductsCall = true;\r\n          this.showProductSpinner = false\r\n          this.ref.markForCheck()\r\n        }\r\n      })\r\n  }\r\n\r\n\r\n  private getAllCategories() {\r\n    let allCategories: any = localStorage.getItem('allCategories');\r\n    allCategories = JSON.parse(allCategories);\r\n    this.rawCategories = allCategories;\r\n\r\n    this.rawCategories?.forEach((cat: any) => {\r\n      if (cat.id == this.categoryId) {\r\n        this.assignBreadCrumbsData(cat);\r\n      }\r\n      cat['path'] = cat.categoryName;\r\n      cat['catIds'] = cat.id;\r\n      this.fetchCategories(cat.categories, cat);\r\n    });\r\n  }\r\n\r\n  scrollToTop(){\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.scrollTo({top: 0, behavior: 'smooth'});\r\n    }\r\n  }\r\n\r\n  triggerAnalytics(type: string, id: any) {\r\n    if(this.isGoogleAnalytics &&  this.permissionService.getTagFeature(type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST')){\r\n      if(type === 'promotion') {\r\n        this.$gaService.pageView('/promotion', 'Promotion ID: ' + id);\r\n      } else if (type === 'category') {\r\n        this.$gaService.pageView('/category', 'Category ID: ' + id);\r\n      } else if (type === 'feature') {\r\n        this.$gaService.pageView('/category', 'Feature ID: ' + id);\r\n      }\r\n      this.$gaService.event(\r\n        this.tagName.SEARCH,\r\n        id,\r\n        type === 'promotion' ? 'VIEW_PROMOTION' : 'VIEW_ITEM_LIST',\r\n        1,\r\n        true,\r\n        {\r\n          \"id\": id,\r\n          \"user_ID\":this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\r\n        }\r\n      );\r\n    }\r\n  }\r\n  getBannerImages(url: string) {\r\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"!isMobileView; else mobileView\">\r\n<section class=\"category-products-page\"\r\n  [ngClass]=\"{'category-products-page':navbarData?.isActive,'hidden-navbar':!navbarData?.isActive}\"\r\n  *ngIf=\"!isBlank\">\r\n  <div class=\"breadcrumb m-0\"\r\n    [ngClass]=\"{'breadcrumb':navbarData?.isActive,'hiddenNavbarBreadcrum':!navbarData?.isActive}\">\r\n    <p-breadcrumb [home]=\"home\" [model]=\"breadItems\"></p-breadcrumb>\r\n  </div>\r\n\r\n  <!-- Category Banner -->\r\n  <div *ngIf=\"categoryBanner?.isBanner && categoryBanner?.desktopBanner\" class=\"category-banner\">\r\n    <img [src]=\"getBannerImages(categoryBanner?.desktopBanner)\" class=\"category-banner__img\"/>\r\n   </div>\r\n\r\n  <!-- Products list -->\r\n  <div *ngIf=\"products && products.length\">\r\n    <div class=\"\">\r\n      <div class=\"col-12 col-md-6 flex\">\r\n        <div class=\"font-size-22 bold-font\">\r\n          {{ category ? category.categoryName : categoryName }}\r\n        </div>\r\n      </div>\r\n\r\n      <div *ngIf=\"products && products.length > 0\" class=\"title-category flex flex-row flex-wrap \">\r\n        <a *ngFor=\"let product of products\" class=\"slide mt-2 mx-1 md:mx-md-2 lg:mx-lg-2 products-margin\"\r\n           [class.isLayoutTemplate]=\"!isLayoutTemplate\">\r\n          <app-mtn-product-card [currency]=\"currency\" [product]=\"product\" [categoryName]=\"categoryName\"></app-mtn-product-card>\r\n        </a>\r\n      </div>\r\n      <div class=\"spinner-product\" *ngIf=\"showProductSpinner\">\r\n        <p-progressSpinner></p-progressSpinner>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n<app-category-not-found *ngIf=\" !isBlank && products && products.length === 0\"></app-category-not-found>\r\n</ng-container>\r\n\r\n<ng-template #mobileView>\r\n  <section class=\"category-products-page\" [ngStyle]=\"{ 'margin-top': isMobileLayout ? '65px' : '210px' }\"\r\n  *ngIf=\"!isBlank\">\r\n\r\n  <!-- Category Banner -->\r\n  <div *ngIf=\"categoryBanner?.isBanner && categoryBanner?.mobileBanner\" class=\"category-banner\">\r\n    <img [src]=\"getBannerImages(categoryBanner?.mobileBanner)\" class=\"category-banner__img\"/>\r\n   </div>\r\n\r\n   <!-- Products List -->\r\n  <div *ngIf=\"products && products.length\">\r\n    <div class=\"\">\r\n      <div class=\"container\" style=\"padding-top: 16px;\">\r\n        <div class=\"row\" style=\"padding: 0px 15px;\">\r\n          <div class=\"col-9\" style=\"padding-left: 0px !important;\">\r\n            <div class=\"font-size-22 font-bold\" >\r\n            {{ category ? category.categoryName : categoryName }}\r\n            </div>\r\n          </div>\r\n          <div class=\"col-3\" style=\"padding-right: 0px !important\">\r\n            <!-- Content for the second column -->\r\n            <div style=\" float: inline-end;\">\r\n              <!-- <img alt=\"No Image\"style=\"margin-left: 10px;\" src=\"assets/icons/mobile-icons/sort.svg\">\r\n              <img alt=\"No Image\" style=\"margin-left: 10px;\" src=\"assets/icons/mobile-icons/filter.svg\"> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n      <div *ngIf=\"products && products.length > 0\" class=\"row mobile-card-div\" >\r\n        <a *ngFor=\"let product of products\" class=\"slide mt-2 col-sm-6\" style=\"padding: 0;\"\r\n           [class.isLayoutTemplate]=\"!isLayoutTemplate\">\r\n          <app-mtn-product-card [currency]=\"currency\" [product]=\"product\" [categoryName]=\"categoryName\"></app-mtn-product-card>\r\n        </a>\r\n      </div>\r\n      <div class=\"spinner-product\" *ngIf=\"showProductSpinner\">\r\n        <p-progressSpinner></p-progressSpinner>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n  <app-category-not-found *ngIf=\" !isBlank && products && products.length === 0\"></app-category-not-found>\r\n</ng-template>\r\n"], "mappings": "AAAA,SAA2EA,WAAW,QAAO,eAAe;AAK5G,SAASC,WAAW,QAAQ,2BAA2B;AAavD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,YAAY,QAA+B,sBAAsB;AAEzE,OAAOC,gBAAgB,MAAM,2BAA2B;;;;;;;;;;;;;;;;ICXtDC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,aAA0F;IAC3FF,EAAA,CAAAG,YAAA,EAAM;;;;IADAH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,UAAA,QAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,cAAA,kBAAAF,MAAA,CAAAE,cAAA,CAAAC,aAAA,GAAAT,EAAA,CAAAU,aAAA,CAAsD;;;;;IAavDV,EAAA,CAAAC,cAAA,YACgD;IAC9CD,EAAA,CAAAE,SAAA,+BAAqH;IACvHF,EAAA,CAAAG,YAAA,EAAI;;;;;IAFDH,EAAA,CAAAW,WAAA,sBAAAC,MAAA,CAAAC,gBAAA,CAA4C;IACvBb,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAAO,MAAA,CAAAE,QAAA,CAAqB,YAAAC,WAAA,kBAAAH,MAAA,CAAAI,YAAA;;;;;IAH/ChB,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAiB,UAAA,IAAAC,gEAAA,gBAGI;IACNlB,EAAA,CAAAG,YAAA,EAAM;;;;IAJmBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,UAAA,YAAAc,MAAA,CAAAC,QAAA,CAAW;;;;;IAKpCpB,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBVH,EAAA,CAAAC,cAAA,UAAyC;IAIjCD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAiB,UAAA,IAAAK,4DAAA,kBAKM;IACNtB,EAAA,CAAAiB,UAAA,IAAAM,4DAAA,kBAEM;IACRvB,EAAA,CAAAG,YAAA,EAAM;;;;IAbAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAC,MAAA,CAAAC,QAAA,GAAAD,MAAA,CAAAC,QAAA,CAAAV,YAAA,GAAAS,MAAA,CAAAT,YAAA,MACF;IAGIhB,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,UAAA,SAAAoB,MAAA,CAAAL,QAAA,IAAAK,MAAA,CAAAL,QAAA,CAAAO,MAAA,KAAqC;IAMb3B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,UAAA,SAAAoB,MAAA,CAAAG,kBAAA,CAAwB;;;;;;;;;;;;;;;;;IA5B5D5B,EAAA,CAAAC,cAAA,iBAEmB;IAGfD,EAAA,CAAAE,SAAA,sBAAgE;IAClEF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiB,UAAA,IAAAY,sDAAA,iBAEO;IAGP7B,EAAA,CAAAiB,UAAA,IAAAa,sDAAA,iBAkBM;IACR9B,EAAA,CAAAG,YAAA,EAAU;;;;IAhCRH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,IAAAF,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,GAAiG;IAG/FnC,EAAA,CAAAI,SAAA,GAA6F;IAA7FJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA+B,eAAA,IAAAK,GAAA,EAAAH,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,IAAAF,MAAA,CAAAC,UAAA,kBAAAD,MAAA,CAAAC,UAAA,CAAAC,QAAA,GAA6F;IAC/EnC,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAA4B,MAAA,CAAAI,IAAA,CAAa,UAAAJ,MAAA,CAAAK,UAAA;IAIvBtC,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,UAAA,UAAA4B,MAAA,CAAAzB,cAAA,kBAAAyB,MAAA,CAAAzB,cAAA,CAAA+B,QAAA,MAAAN,MAAA,CAAAzB,cAAA,kBAAAyB,MAAA,CAAAzB,cAAA,CAAAC,aAAA,EAA+D;IAK/DT,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,SAAA4B,MAAA,CAAAb,QAAA,IAAAa,MAAA,CAAAb,QAAA,CAAAO,MAAA,CAAiC;;;;;IAoBzC3B,EAAA,CAAAE,SAAA,6BAAwG;;;;;IAnCxGF,EAAA,CAAAwC,uBAAA,GAAqD;IACrDxC,EAAA,CAAAiB,UAAA,IAAAwB,gDAAA,sBAiCU;IACVzC,EAAA,CAAAiB,UAAA,IAAAyB,+DAAA,oCAAwG;IACxG1C,EAAA,CAAA2C,qBAAA,EAAe;;;;IAjCZ3C,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAuC,MAAA,CAAAC,OAAA,CAAc;IAgCQ7C,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,UAAA,UAAAuC,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAAxB,QAAA,IAAAwB,MAAA,CAAAxB,QAAA,CAAAO,MAAA,OAAqD;;;;;IAQ5E3B,EAAA,CAAAC,cAAA,aAA8F;IAC5FD,EAAA,CAAAE,SAAA,aAAyF;IAC1FF,EAAA,CAAAG,YAAA,EAAM;;;;IADAH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,UAAA,QAAAyC,OAAA,CAAAvC,eAAA,CAAAuC,OAAA,CAAAtC,cAAA,kBAAAsC,OAAA,CAAAtC,cAAA,CAAAuC,YAAA,GAAA/C,EAAA,CAAAU,aAAA,CAAqD;;;;;IAyBtDV,EAAA,CAAAC,cAAA,YACgD;IAC9CD,EAAA,CAAAE,SAAA,+BAAqH;IACvHF,EAAA,CAAAG,YAAA,EAAI;;;;;IAFDH,EAAA,CAAAW,WAAA,sBAAAqC,OAAA,CAAAnC,gBAAA,CAA4C;IACvBb,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAA2C,OAAA,CAAAlC,QAAA,CAAqB,YAAAmC,WAAA,kBAAAD,OAAA,CAAAhC,YAAA;;;;;IAH/ChB,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAiB,UAAA,IAAAiC,+DAAA,gBAGI;IACNlD,EAAA,CAAAG,YAAA,EAAM;;;;IAJmBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,UAAA,YAAA8C,OAAA,CAAA/B,QAAA,CAAW;;;;;IAKpCpB,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,wBAAuC;IACzCF,EAAA,CAAAG,YAAA,EAAM;;;;;IA5BVH,EAAA,CAAAC,cAAA,UAAyC;IAM/BD,EAAA,CAAAqB,MAAA,GACA;IAAArB,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAAyD;IAEvDD,EAAA,CAAAE,SAAA,cAGM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAiB,UAAA,IAAAmC,2DAAA,kBAKM;IACNpD,EAAA,CAAAiB,UAAA,KAAAoC,4DAAA,kBAEM;IACRrD,EAAA,CAAAG,YAAA,EAAM;;;;IAvBEH,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAwB,kBAAA,MAAA8B,OAAA,CAAA5B,QAAA,GAAA4B,OAAA,CAAA5B,QAAA,CAAAV,YAAA,GAAAsC,OAAA,CAAAtC,YAAA,MACA;IAaAhB,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,UAAA,SAAAiD,OAAA,CAAAlC,QAAA,IAAAkC,OAAA,CAAAlC,QAAA,CAAAO,MAAA,KAAqC;IAMb3B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,UAAA,SAAAiD,OAAA,CAAA1B,kBAAA,CAAwB;;;;;;;;;;IAnC1D5B,EAAA,CAAAC,cAAA,kBACiB;IAGjBD,EAAA,CAAAiB,UAAA,IAAAsC,qDAAA,iBAEO;IAGPvD,EAAA,CAAAiB,UAAA,IAAAuC,qDAAA,kBA8BM;IACRxD,EAAA,CAAAG,YAAA,EAAU;;;;IAxCgCH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,cAAA,qBAA+D;IAIjG5D,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,UAAA,UAAAsD,OAAA,CAAAnD,cAAA,kBAAAmD,OAAA,CAAAnD,cAAA,CAAA+B,QAAA,MAAAoB,OAAA,CAAAnD,cAAA,kBAAAmD,OAAA,CAAAnD,cAAA,CAAAuC,YAAA,EAA8D;IAK9D/C,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,SAAAsD,OAAA,CAAAvC,QAAA,IAAAuC,OAAA,CAAAvC,QAAA,CAAAO,MAAA,CAAiC;;;;;IAgCvC3B,EAAA,CAAAE,SAAA,6BAAwG;;;;;IAzCxGF,EAAA,CAAAiB,UAAA,IAAA4C,+CAAA,sBAwCQ;IACR7D,EAAA,CAAAiB,UAAA,IAAA6C,8DAAA,oCAAwG;;;;IAxCvG9D,EAAA,CAAAK,UAAA,UAAA0D,MAAA,CAAAlB,OAAA,CAAc;IAwCU7C,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,UAAA,UAAA0D,MAAA,CAAAlB,OAAA,IAAAkB,MAAA,CAAA3C,QAAA,IAAA2C,MAAA,CAAA3C,QAAA,CAAAO,MAAA,OAAqD;;;ADpDhF,OAAM,MAAOqC,cAAc;EAkFfC,cAAA;EACAC,cAAA;EACAC,KAAA;EACAC,cAAA;EACAC,cAAA;EACAC,SAAA;EACAC,GAAA;EACAC,aAAA;EACAC,aAAA;EACAC,gBAAA;EACAC,aAAA;EACAC,eAAA;EACAC,MAAA;EACAC,cAAA;EACAC,iBAAA;EACAC,UAAA;EACqBC,UAAA;EACrBC,WAAA;EAlGVC,UAAU;EACVC,SAAS;EACT1D,QAAQ;EACRV,YAAY;EACZqE,KAAK,GAAe,EAAE;EACtB/C,UAAU,GAAe,EAAE;EAC3BD,IAAI,GAAa;IAAEiD,IAAI,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAG,CAAE;EACxDzE,QAAQ,GAAa,EAAc;EACnC0E,OAAO,GAAW5F,WAAW,CAAC6F,WAAW,GAAG,GAAG;EAC/CC,QAAQ,GAAW,wBAAwB;EAC3CC,OAAO;EACPC,KAAK;EACLC,MAAM,GAAQ,EAAE;EAChBC,QAAQ,GAAQ,EAAE;EACT1E,QAAQ;EACjB2E,OAAO,GAAa,KAAK;EACzBlD,OAAO,GAAa,IAAI;EACxBmD,OAAO;EACPC,UAAU,GAAU,EAAE;EAEtBC,aAAa,GAAQ,EAAE;EACvBC,WAAW,GAAQ,IAAI;EACvBC,KAAK;EACLC,QAAQ,GAAW,EAAE;EACrBC,eAAe,GAAW,EAAE;EAC5BC,mBAAmB,GAAY,KAAK;EACpC3E,kBAAkB,GAAY,KAAK;EAEnC4E,YAAY,GAAW,EAAE;EACzBC,iBAAiB,GAAW,CAAC;EAC7BC,KAAK,GAAW,CAAC;EACjBC,gBAAgB,GAAW,KAAK;EAChCC,4BAA4B,GAAY,IAAI;EAC5CC,6BAA6B,GAAY,IAAI;EAC7C3E,UAAU;EACVrB,gBAAgB,GAAY,KAAK;EACjCiG,WAAW;EACXC,aAAa;EACbC,WAAW,GAAWC,MAAM,CAACC,UAAU;EACvCC,YAAY,GAAW,IAAI,CAACH,WAAW,IAAI,GAAG;EAC9CI,iBAAiB,GAAY,KAAK;EAClCC,WAAW;EACXC,OAAO,GAAKxH,YAAY;EACxB8D,cAAc,GAAY,KAAK;EAC/BpD,cAAc;EAGd+G,QAAQA,CAACC,KAAU;IACjB,IAAI3H,iBAAiB,CAAC,IAAI,CAACoF,UAAU,CAAC,EAAE;MACtC,MAAMwC,cAAc,GAAGR,MAAM,CAACS,OAAO,IAAIC,QAAQ,CAACC,eAAe,CAACC,SAAS;MAC3E,MAAMC,WAAW,GAAGH,QAAQ,CAACC,eAAe,CAACG,YAAY;MACzD,MAAMC,YAAY,GAAGf,MAAM,CAACgB,WAAW;MACvC,MAAMC,UAAU,GAAGT,cAAc,GAAGO,YAAY,IAAIF,WAAW,GAAC,CAAC;MAEjE,IAAII,UAAU,IAAI,CAAC,IAAI,CAAC3B,mBAAmB,IAAI,IAAI,CAACG,KAAK,IAAI,IAAI,CAACL,QAAQ,EAAE;QACxE,IAAI,CAAC8B,gBAAgB,EAAE;;;EAG/B;EAEAC,QAAQA,CAACZ,KAAU;IACjB,IAAI,CAACR,WAAW,GAAGQ,KAAK,CAACa,MAAM,CAACnB,UAAU;IAC1C,IAAI,IAAI,CAACF,WAAW,IAAI,GAAG,EAAE;MAC3B,IAAI,CAACG,YAAY,GAAG,IAAI;KACzB,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,KAAK;;EAG7B;EACAgB,gBAAgBA,CAAA;IACZ,IAAI,IAAI,CAAC3B,YAAY,KAAK,UAAU,EAAG;MACnC,IAAI,IAAI,CAACK,6BAA6B,EAAE,IAAI,CAACyB,qBAAqB,EAAE;KACvE,MAAM,IAAI,IAAI,CAAC9B,YAAY,KAAK,SAAS,EAAE;MACxC,IAAI,IAAI,CAACI,4BAA4B,EAAE,IAAI,CAAC2B,4BAA4B,EAAE;KAC7E,MACI,IAAI,IAAI,CAAC/B,YAAY,KAAK,WAAW,EAAE;MACxC,IAAI,IAAI,CAACI,4BAA4B,EAAE,IAAI,CAAC4B,8BAA8B,EAAE;;EAEpF;EAEAC,YACUxE,cAA8B,EAC9BC,cAA8B,EAC9BC,KAAmB,EACnBC,cAA8B,EAC9BC,cAA8B,EAC9BC,SAA2B,EAC3BC,GAAsB,EACtBC,aAA4B,EAC5BC,aAA4B,EAC5BC,gBAAkC,EAClCC,aAA4B,EAC5BC,eAAgC,EAChCC,MAAc,EACdC,cAA8B,EAC9BC,iBAAoC,EACpCC,UAAkC,EACbC,UAAe,EACpCC,WAAsB;IAjBtB,KAAAjB,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACW,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,WAAW,GAAXA,WAAW;IAGnB,IAAI,CAACrE,gBAAgB,GAAG,IAAI,CAACkE,iBAAiB,CAAC2D,aAAa,CAAC,iBAAiB,CAAC;IAC/E,IAAI,CAACtB,iBAAiB,GAAG,IAAI,CAACrC,iBAAiB,CAAC2D,aAAa,CAAC,kBAAkB,CAAC;IACjF,IAAI,CAAC9E,cAAc,GAAG,IAAI,CAACmB,iBAAiB,CAAC2D,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC1G,UAAU,GAAG,IAAI,CAAC4C,cAAc,CAAC+D,cAAc,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IACtG,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACN,WAAW,EAAE;EACpB;EAEAM,SAASA,CAAA;IAEP,IAAIC,KAAK;IACT,IAAI,CAACjF,cAAc,CAACkF,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MAChD,IAAI,CAACjI,QAAQ,GAAG,EAAE;MAClB8H,KAAK,GAAGG,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MACxBJ,KAAK,GAAGA,KAAK,EAAEK,KAAK,CAAC,GAAG,CAAC;MACzB,IAAI,CAAClC,WAAW,GAAG,IAAI,CAAClD,KAAK,CAACmF,GAAG,CAAC,SAAS,CAAC;MAC5C,IAAG,IAAI,CAACzE,MAAM,CAAC2E,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACxC,IAAIP,KAAK,EAAEvH,MAAM,IAAI,CAAC,EAAE;UACtB,IAAI,CAACmF,WAAW,GAAGoC,KAAK,CAAC,CAAC,CAAC;UAC3B,IAAI,CAAC1C,YAAY,GAAG,WAAW;UAC/B,IAAI,CAACkD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC5C,WAAW,CAAC;UACpD,IAAI,CAAC6C,iBAAiB,EAAE;UACxB;;OAEH,MACI;QACH,IAAIT,KAAK,EAAEvH,MAAM,IAAI,CAAC,EAAE;UACtB,IAAI,CAACwD,UAAU,GAAG+D,KAAK,CAAC,CAAC,CAAC;UAC1B,IAAI,CAAC1C,YAAY,GAAG,UAAU;UAC9B,IAAI,CAACkD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACvE,UAAU,CAAC;UAClD;UACA,IAAI,CAACyE,QAAQ,EAAE;SAChB,MAAM,IAAIV,KAAK,EAAEvH,MAAM,IAAI,CAAC,EAAE;UAC7B,IAAI,CAACwD,UAAU,GAAG+D,KAAK,CAAC,CAAC,CAAC;UAC1B,IAAI,CAAC9D,SAAS,GAAG8D,KAAK,CAAC,CAAC,CAAC;UACzB,IAAI,CAAClI,YAAY,GAAGkI,KAAK,CAAC,CAAC,CAAC;UAC5B,IAAI,CAAC1C,YAAY,GAAG,SAAS;UAC7B,IAAI,CAACkD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACvE,UAAU,CAAC;UACjD,IAAI,CAAC0E,eAAe,EAAE;UACtB,IAAI,CAAC3E,WAAW,CAAC4E,YAAY,CAAC,SAAS,EAAC,IAAI,CAAC9I,YAAY,CAAC;;;IAGhE,CAAC,CAAC;EACJ;EAEA4I,QAAQA,CAAA;IACN,IAAI,CAACpF,aAAa,CAACuF,IAAI,EAAE;IACzB,IAAI,CAAC7F,cAAc,CAChB8F,mBAAmB,CAAC,IAAI,CAAC7E,UAAU,EAAE,IAAI,CAACmB,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,CACtE8C,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC9I,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACyB,OAAO,GAAG,KAAK;QACpB,IAAIqH,GAAG,CAACC,OAAO,EAAE;UACf,IAAI,CAACpE,OAAO,GAAE,KAAK;UACnB,IAAI,CAACW,KAAK,GAAGwD,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO,CAAC3I,MAAM;UACjD,IAAI,CAACnB,cAAc,GAAG0J,GAAG,CAACE,IAAI,CAAC5J,cAAc;UAC7C0J,GAAG,CAACE,IAAI,EAAEC,YAAY,EAAEC,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;YACtD,IAAI,CAACC,sBAAsB,CAACD,MAAM,CAAC;YACnC,IAAI,CAACvE,UAAU,GAAGuE,MAAM,CAACvE,UAAU,CAACuE,MAAM,CAAC,EAAEE,YAAY,IAAI,EAAE;UACjE,CAAC,CAAC;UACH,IAAI,CAACC,gBAAgB,EAAE;;QAExB,IAAI,CAACnG,aAAa,CAACoG,IAAI,EAAE;MAC3B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClB,IAAI,CAACC,WAAW,CAACD,GAAG,CAAC;MACvB;KACD,CAAC;IAIJE,UAAU,CAAC,MAAK;MAEd,IAAI,CAAC7G,KAAK,CAAC8G,YAAY,CAAC,UAAU,CAAC,CAAC7B,SAAS,CAAC;QAC5Ca,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACpJ,QAAQ,GAAGoJ;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI,CAACjG,cAAc,CAACiH,WAAW,CAAC9B,SAAS,CAAEc,GAAG,IAAI;MAChD,IAAI,CAACvE,OAAO,GAAGuE,GAAG,EAAEiB,IAAI,EAAE5B,KAAK,CAAC,IAAI,CAAC;MACrC,IAAI,CAAC3D,KAAK,GAAGsE,GAAG,EAAEkB,EAAE,EAAE7B,KAAK,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC5D,OAAO,EAAE;MACjB,IAAI,CAACxB,KAAK,CAAC8G,YAAY,CAAC,YAAY,CAAC,CAAC7B,SAAS,CAAC;QAC9Ca,IAAI,EAAGC,GAAQ,IAAI;UAGjBA,GAAG,CAACK,OAAO,CAAEc,OAAiB,IAAI;YAChC,IAAIA,OAAO,CAACD,EAAE,IAAI,IAAI,CAACjG,UAAU,EAAE;cACjC,IAAI,CAACzD,QAAQ,GAAG2J,OAAO;;UAE3B,CAAC,CAAC;UACF,IAAI,CAACpH,cAAc,CAACqH,aAAa,CAAClC,SAAS,CAACC,MAAM,IAAG;YAGrD,IAAI,CAACrI,YAAY,GAAGqI,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;UAC9C,CAAC,CAAC;QACJ,CAAC;QACDuB,KAAK,EAAGC,GAAQ,IAAI;UAClBS,OAAO,CAACV,KAAK,CAACC,GAAG,CAAC;QACpB;OACD,CAAC;MACF,IAAIU,KAAK,GAAG,UAAU;MAEtB,IAAI,CAAClH,SAAS,CAACgF,GAAG,CAAC,uBAAuB,CAAC,CAACF,SAAS,CAAEgB,IAAS,IAAI;QAClEoB,KAAK,GAAGpB,IAAI;MACd,CAAC,CAAC;MAEF,IAAI,CAAC/E,KAAK,GAAG,CAAC;QAAEmG,KAAK,EAAE,IAAI,CAAC9J,QAAQ,EAAEV;MAAY,CAAE,EAAE;QAAEwK,KAAK,EAAEA;MAAK,CAAE,CAAC;MACvE,IAAI,CAACnJ,IAAI,GAAG;QAAEiD,IAAI,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAG,CAAE;KACpD,MAAM;MACL,IAAIiG,KAAK,GAAG,UAAU;MAEtB,IAAI,CAAClH,SAAS,CAACgF,GAAG,CAAC,uBAAuB,CAAC,CAACF,SAAS,CAAEgB,IAAS,IAAI;QAClEoB,KAAK,GAAGpB,IAAI;MACd,CAAC,CAAC;MACF,IAAI,CAACpJ,YAAY,GAAG,IAAI,CAAC2E,OAAO,CAAC,IAAI,CAACA,OAAO,CAAChE,MAAM,GAAG,CAAC,CAAC;MACzD,IAAI,CAAC0D,KAAK,GAAG,EAAE;MACf,IAAI,CAACM,OAAO,CAAC4E,OAAO,CAAC,CAACkB,CAAM,EAAEC,KAAU,KAAI;QAC1C,IAAI,CAACrG,KAAK,CAACsG,IAAI,CAAC;UACdH,KAAK,EAAEC,CAAC;UACRL,EAAE,EAAE,IAAI,CAACxF,KAAK,CAAC8F,KAAK;SACrB,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAACrG,KAAK,CAACkF,OAAO,CAAC,CAACqB,GAAQ,EAAEF,KAAU,KAAI;QAC1C,IAAI,CAAC5F,QAAQ,GAAG4F,KAAK,IAAI,CAAC,GAAGE,GAAG,CAACJ,KAAK,GAAG,IAAI,CAAC1F,QAAQ,GAAG,IAAI,GAAG8F,GAAG,CAACJ,KAAK;QACzE,IAAI,CAAC3F,MAAM,GAAG6F,KAAK,IAAI,CAAC,GAAGE,GAAG,CAACR,EAAE,GAAG,IAAI,CAACvF,MAAM,GAAG,IAAI,GAAGgG,MAAM,CAACD,GAAG,CAACR,EAAE,CAAC;QACvEQ,GAAG,CAACrG,UAAU,GAAG,aAAaqG,GAAG,CAACR,EAAE,EAAE;QACtCQ,GAAG,CAACV,WAAW,GAAG;UAAEC,IAAI,EAAE,IAAI,CAACrF,QAAQ;UAAEsF,EAAE,EAAE,IAAI,CAACvF;QAAM,CAAE;MAC5D,CAAC,CAAC;MAEF,IAAI,CAACR,KAAK,CAACsG,IAAI,CAAC;QACdH,KAAK,EAAEA;OACR,CAAC;MACF,IAAI,CAACnJ,IAAI,GAAG;QAAEiD,IAAI,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAG,CAAE;;EAEvD;EACAoE,iBAAiBA,CAAA;IAEf,IAAI,CAACnF,aAAa,CAACuF,IAAI,EAAE;IACzB,IAAI,CAAC7F,cAAc,CAChB4H,wBAAwB,CAAC,IAAI,CAAChF,WAAW,EAAE,IAAI,CAACR,eAAe,EAAE,KAAK,EAAE,IAAI,CAACG,iBAAiB,CAAC,CAC/F2C,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC9I,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACyB,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC7B,YAAY,GAACkJ,GAAG,CAACE,IAAI,CAACrD,aAAa;QACxC,IAAI,CAACzE,UAAU,GAAG,CAAC;UAAEkJ,KAAK,EAAE,IAAI,CAACxK;QAAY,CAAE,CAAC;QAChD,IAAIkJ,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAE3I,MAAM,GAAG,CAAC,EAAE;UAClC,IAAI,CAAC+E,KAAK,GAAGwD,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAE3I,MAAM;UACvCuI,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;YACxC,IAAI,CAACuB,6BAA6B,CAACvB,MAAM,CAAC;UAC5C,CAAC,CAAC;UACF,IAAI,CAAChG,aAAa,CAACoG,IAAI,EAAE;;MAE7B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClBS,OAAO,CAACV,KAAK,CAACC,GAAG,CAAC;QAClB,IAAI,CAACjI,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC2B,aAAa,CAACoG,IAAI,EAAE;MAC3B,CAAC;MACDoB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACxH,aAAa,CAACoG,IAAI,EAAE;MAC3B;KACD,CAAC;IAEJI,UAAU,CAAC,MAAK;MAEd,IAAI,CAAC7G,KAAK,CAAC8G,YAAY,CAAC,UAAU,CAAC,CAAC7B,SAAS,CAAC;QAC5Ca,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACpJ,QAAQ,GAAGoJ;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAGN,IAAI,CAAC7H,IAAI,GAAG;MAAEiD,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAAChB,GAAG,CAAC0H,aAAa,EAAE;IACxB,IAAI,CAAC1H,GAAG,CAAC2H,YAAY,EAAE;EACzB;EAEQnB,WAAWA,CAACD,GAAQ;IAC1B,IAAI,CAACjI,OAAO,GAAG,KAAK;IACpB,IAAI,CAACkD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACvB,aAAa,CAACoG,IAAI,EAAE;EAC3B;EAEAH,sBAAsBA,CAACD,MAAU;IAC/B,IAAI2B,gBAAgB;IAEpB,IAAIC,cAAc,GAAG5B,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACL,IAAII,eAAe,GAAGhC,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OAEnC,MAAM;QACLL,gBAAgB,GAAG3B,MAAM,EAAE6B,gBAAgB,CAAC,CAAC,CAAC;;;IAGlD,IAAIK,QAAQ,GAAC,EAAE;IACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;MACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;IAGhE,IAAIC,OAAO,GAAO;MAChBC,MAAM,EAACtC,MAAM,CAACvE,UAAU;MACxB8G,SAAS,EAAEvC,MAAM,EAAEY,EAAE;MACrB4B,WAAW,EAAExC,MAAM,EAAEyC,IAAI;MACzBC,OAAO,EAAC1C,MAAM,EAAE0C,OAAO;MACvBC,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;MACnCC,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;MAC3CC,OAAO,EAAEpB,gBAAgB,EAAEoB,OAAO;MAClCC,YAAY,EAAEhD,MAAM,EAAEgD,YAAY;MAClCC,cAAc,EAAEjD,MAAM,EAAEiD,cAAc,KAAKtB,gBAAgB,CAACuB,MAAM,GAAGvB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACvGC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;MAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;MAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;MAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;MACnCC,aAAa,EAAE3B,gBAAgB,CAAC2B,aAAa;MAC7CC,SAAS,EAAEvD,MAAM,CAACuD,SAAS,IAAI,GAAG;MAClCC,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;MAC9Ga,MAAM,EAAEzD,MAAM,CAACyD,MAAM;MACrBC,KAAK,EAACxB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC3B0E,KAAK,EAACzB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC3B2E,MAAM,EAAC1B,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC5B4E,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;MAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;MAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;MACxCC,WAAW,EAACrC,gBAAgB,CAACqC,WAAW;MACxCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;MACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;KACrC;IACD,IAAI,CAACtN,QAAQ,CAACuK,IAAI,CAACkB,OAAO,CAAC;EAC7B;EAEAhD,eAAeA,CAAA;IAEb,IAAI,CAACrF,aAAa,CAACuF,IAAI,EAAE;IACzB,IAAI,CAAC7F,cAAc,CAChByK,uBAAuB,CAAC,IAAI,CAACxJ,UAAU,EAAE,IAAI,CAACmB,eAAe,EAAE,KAAK,EAAE,IAAI,CAACG,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAACE,gBAAgB,CAAC,CACrIyC,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC9I,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACyB,OAAO,GAAG,KAAK;QACpB,IAAIqH,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAE3I,MAAM,GAAG,CAAC,EAAE;UAClC,IAAI,CAAC+E,KAAK,GAAGwD,GAAG,EAAEE,IAAI,EAAEE,OAAO,EAAE3I,MAAM;UACvCuI,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;YACzC,IAAI,CAACuB,6BAA6B,CAACvB,MAAM,CAAC;UAC3C,CAAC,CAAC;UACF,IAAI,CAAChG,aAAa,CAACoG,IAAI,EAAE;;MAE7B,CAAC;MACDC,KAAK,EAAGC,GAAQ,IAAI;QAClBS,OAAO,CAACV,KAAK,CAACC,GAAG,CAAC;QAClB,IAAI,CAACjI,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC2B,aAAa,CAACoG,IAAI,EAAE;MAC3B,CAAC;MACDoB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACxH,aAAa,CAACoG,IAAI,EAAE;MAC3B;KACD,CAAC;IAEJI,UAAU,CAAC,MAAK;MAEd,IAAI,CAAC7G,KAAK,CAAC8G,YAAY,CAAC,UAAU,CAAC,CAAC7B,SAAS,CAAC;QAC5Ca,IAAI,EAAGC,GAAG,IAAM,IAAI,CAACpJ,QAAQ,GAAGoJ;OACjC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAEN,IAAI,CAAC5H,UAAU,GAAG,CAAC;MAAEkJ,KAAK,EAAE,IAAI,CAACxK;IAAY,CAAE,CAAC;IAChD,IAAI,CAACqB,IAAI,GAAG;MAAEiD,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAAChB,GAAG,CAAC0H,aAAa,EAAE;IACxB,IAAI,CAAC1H,GAAG,CAAC2H,YAAY,EAAE;EACzB;EAEAH,6BAA6BA,CAACvB,MAAU;IACtC,IAAI2B,gBAAgB;IACtB,IAAIC,cAAc,GAAG5B,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACP,IAAII,eAAe,GAAGhC,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OAEnC,MAAM;QACPL,gBAAgB,GAAG3B,MAAM,EAAE6B,gBAAgB,CAAC,CAAC,CAAC;;;IAIhD,IAAIF,gBAAgB,EAAE;MACpB,IAAIO,QAAQ,GAAC,EAAE;MACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;QACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;MAEhE,IAAIC,OAAO,GAAO;QAChBC,MAAM,EAACtC,MAAM,CAACvE,UAAU;QACxB8G,SAAS,EAAEvC,MAAM,EAAEY,EAAE;QACrB4B,WAAW,EAAExC,MAAM,EAAEyC,IAAI;QACzBC,OAAO,EAAC1C,MAAM,EAAE0C,OAAO;QACvBC,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;QACnCG,OAAO,EAAEpB,gBAAgB,EAAEoB,OAAO;QAClCF,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;QAC3CE,YAAY,EAAEhD,MAAM,EAAEgD,YAAY;QAClCC,cAAc,EAAEjD,MAAM,EAAEiD,cAAc,IAAItB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC;QACpEC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;QAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;QAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;QAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;QACnCG,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;QAC9Ga,MAAM,EAAEzD,MAAM,CAACyD,MAAM;QACrBH,aAAa,EAAE3B,gBAAgB,CAAC2B,aAAa;QAC7CC,SAAS,EAAEvD,MAAM,CAACuD,SAAS,IAAI,GAAG;QAClCG,KAAK,EAACxB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;QAC3B0E,KAAK,EAACzB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;QAC3B2E,MAAM,EAAC1B,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;QAC5B4E,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;QAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;QAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;QACxCC,WAAW,EAAErC,gBAAgB,CAACqC,WAAW;QACzCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;QACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;OAErC;MACD,IAAI7B,OAAO,CAACQ,cAAc,EAAE;QAC1BR,OAAO,CAACmB,WAAW,GAAG,GAAG,GAAInB,OAAO,CAACQ,cAAc,GAAGR,OAAO,CAACM,UAAU,GAAG,GAAI;;MAEjF,IAAI,CAAC/L,QAAQ,CAACuK,IAAI,CAACkB,OAAO,CAAC;;EAE3B;EAGF+B,eAAeA,CAAClN,QAAa,EAAEmN,GAAQ;IAEnC,IAAInN,QAAQ,CAACC,MAAM,IAAI,CAAC,EAAE;MACxB;;IAGF,KAAK,MAAM0J,OAAO,IAAI3J,QAAQ,EAAE;MAC9B,IAAI2J,OAAO,CAACD,EAAE,IAAI,IAAI,CAACjG,UAAU,EAAE;QACjC,IAAI,CAAC2J,qBAAqB,CAACzD,OAAO,CAAC;;MAErC,IAAI,CAACuD,eAAe,CAACvD,OAAO,CAAC0D,UAAU,EAAE1D,OAAO,CAAC;;EAGrD;EAEAyD,qBAAqBA,CAACpN,QAAa;IAEjC,IAAIsN,QAAQ,GAAQtN,QAAQ,EAAEuN,WAAW,EAAE1F,KAAK,CAAC,IAAI,CAAC,EAAE2F,GAAG,CAACC,MAAM,CAAC;IAEnE,IAAIC,SAAS,GAAQ1N,QAAQ,EAAE2N,YAAY,EAAE9F,KAAK,CAAC,IAAI,CAAC,EAAE2F,GAAG,CAACrD,MAAM,CAAC;IAErE,IAAIyD,WAAW,GAAQ,EAAE;IACzB,IAAIN,QAAQ,CAACrN,MAAM,KAAKyN,SAAS,CAACzN,MAAM,EAAE;MACxCqN,QAAQ,EAAEE,GAAG,CAAC,CAACK,CAAM,EAAEC,CAAM,KAAI;QAC/BF,WAAW,CAAC3D,IAAI,CAAC;UAAEpG,UAAU,EAAE,YAAY,GAAGgK,CAAC,CAACE,QAAQ,EAAE;UAAEjE,KAAK,EAAE4D,SAAS,CAACI,CAAC;QAAC,CAAE,CAAC;MACpF,CAAC,CAAC;MACF,IAAI,CAAClN,UAAU,GAAGgN,WAAW;MAC7B,IAAI,CAAC/K,GAAG,CAAC0H,aAAa,EAAE;MACxB,IAAI,CAAC1H,GAAG,CAAC2H,YAAY,EAAE;;IAEzB;IACA,IAAI,CAAChH,WAAW,CAAC4E,YAAY,CAAC,UAAU,EAAEpI,QAAQ,EAAE2N,YAAY,CAAC;EAInE;EAEAK,MAAMA,CAAA;IACJC,cAAc,CAACC,KAAK,EAAE;IACtB,IAAI,CAAClL,gBAAgB,CAACmL,YAAY,CAAC,EAAE,CAAC;IAGtC,IAAI,CAAClL,aAAa,CAACmL,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAC3C,IAAI,CAAC3L,KAAK,CAAC4L,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAACnL,eAAe,CAACoL,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACpL,eAAe,CAACqL,WAAW,CAAC,IAAI,CAAC;IACtCC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCD,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzCD,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAClC,IAAI,CAAChM,KAAK,CAAC4L,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCG,YAAY,CAACE,UAAU,CAAC,cAAc,CAAC;IACvCF,YAAY,CAACE,UAAU,CAAC,UAAU,CAAC;EACrC;EAEA9H,qBAAqBA,CAAA;IACnB,IAAI,CAAC/B,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC3E,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC0E,eAAe,IAAI,IAAI,CAACD,QAAQ;IACrC,IAAI,CAACI,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAClC,GAAG,CAAC0H,aAAa,EAAE;IAExB,IAAI,CAAC/H,cAAc,CAChB8F,mBAAmB,CAAC,IAAI,CAAC7E,UAAU,EAAE,IAAI,CAACmB,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAACG,iBAAiB,CAAC,CACtG2C,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrD,6BAA6B,GAAGqD,GAAG,CAACE,IAAI,EAAEC,YAAY,CAACC,OAAO,CAAC3I,MAAM,GAAG,CAAC;QAC9E,MAAM0O,oBAAoB,GAAGnG,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO;QAC1D,IAAI,CAAC5D,KAAK,GAAG2J,oBAAoB,CAAC1O,MAAM;QACxC0O,oBAAoB,CAAC9F,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAACC,sBAAsB,CAACD,MAAM,CAAC;QACrC,CAAC,CAAC;QACF,IAAI,CAACjE,mBAAmB,GAAG,CAAC2D,GAAG,CAACE,IAAI,CAACC,YAAY,CAACC,OAAO,CAAC3I,MAAM;QAChE,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC2C,GAAG,CAAC2H,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEAoE,UAAUA,CAAC9F,MAAU;IACnB,IAAI2B,gBAAgB;IACpB,IAAIC,cAAc,GAAG5B,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;IACxF,IAAIH,cAAc,EAAE;MAClBD,gBAAgB,GAAGC,cAAc;KAClC,MAAM;MACL,IAAII,eAAe,GAAGhC,MAAM,EAAE6B,gBAAgB,EAAEvD,IAAI,CAAEwD,OAAY,IAAKA,OAAO,CAACG,OAAO,CAAC;MACvF,IAAID,eAAe,EAAE;QACnBL,gBAAgB,GAAGK,eAAe;OACnC,MAAM;QACLL,gBAAgB,GAAG3B,MAAM,EAAE6B,gBAAgB,CAAC,CAAC,CAAC;;;IAGlD,IAAIK,QAAQ,GAAC,EAAE;IACf,IAAGP,gBAAgB,EAAEQ,mBAAmB,EAAC;MACvCD,QAAQ,GAACP,gBAAgB,EAAEQ,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;IAEhE,IAAIC,OAAO,GAAO;MAChBE,SAAS,EAAEvC,MAAM,EAAEY,EAAE;MACrB4B,WAAW,EAAExC,MAAM,EAAEyC,IAAI;MACzBE,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;MACnCC,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;MAC3CE,YAAY,EAAEhD,MAAM,EAAEgD,YAAY;MAClCC,cAAc,EAAEjD,MAAM,EAAEiD,cAAc,IAAItB,gBAAgB,CAACuB,MAAM,CAAC,CAAC,CAAC;MACpEC,eAAe,EAAExB,gBAAgB,EAAEwB,eAAe;MAClDlB,OAAO,EAAEN,gBAAgB,EAAEM,OAAO;MAClCmB,IAAI,EAAEzB,gBAAgB,EAAEyB,IAAI;MAC5BC,KAAK,EAAE1B,gBAAgB,EAAE0B,KAAK,IAAI,CAAC;MACnCG,WAAW,EAAE7B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;MAC9GW,SAAS,EAAEvD,MAAM,EAAEuD,SAAS;MAC5BG,KAAK,EAACxB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC3B0E,KAAK,EAACzB,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC3B2E,MAAM,EAAC1B,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC;MAC5B4E,QAAQ,EAAClC,gBAAgB,CAACkC,QAAQ;MAClCC,eAAe,EAACnC,gBAAgB,CAACmC,eAAe;MAChDC,WAAW,EAACpC,gBAAgB,CAACoC,WAAW;MACxCC,WAAW,EAAErC,gBAAgB,CAACqC,WAAW;MACzCC,GAAG,EAACtC,gBAAgB,EAAEsC,GAAG;MACzBC,gBAAgB,EAAGvC,gBAAgB,CAACuC;KACrC;IACD,IAAI,CAACtN,QAAQ,CAACuK,IAAI,CAACkB,OAAO,CAAC;EAC7B;EAEAtE,4BAA4BA,CAAA;IAC1B,IAAI,CAAChC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC3E,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC0E,eAAe,IAAI,IAAI,CAACD,QAAQ;IACrC,IAAI,CAACI,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAClC,GAAG,CAAC0H,aAAa,EAAE;IACxB,IAAI,CAAC/H,cAAc,CAChByK,uBAAuB,CAAC,IAAI,CAACxJ,UAAU,EAAE,IAAI,CAACmB,eAAe,EAAE,IAAI,EAAE,IAAI,CAACG,iBAAiB,CAAC,CAC5F2C,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACE,IAAI,EAAEE,OAAO,CAAC3I,MAAM,IAAI,CAAC,EAAE,IAAI,CAACiF,4BAA4B,GAAG,KAAK;QAC5E,MAAMyJ,oBAAoB,GAAGnG,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACiG,KAAK,CAAC,CAAC,IAAI,CAAClK,QAAQ,CAAC;QACpE,IAAI,CAACK,KAAK,GAAGwD,GAAG,CAACE,IAAI,EAAEE,OAAO,EAAE3I,MAAM;QAEtC0O,oBAAoB,CAAC9F,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAACuB,6BAA6B,CAACvB,MAAM,CAAC;QAC5C,CAAC,CAAC;QACF,IAAIN,GAAG,CAACE,IAAI,EAAEE,OAAO,CAAC3I,MAAM,EAAE,IAAI,CAAC4E,mBAAmB,GAAG,KAAK,CAAC,KAC1D,IAAI,CAACA,mBAAmB,GAAG,IAAI;QACpC,IAAI,CAAC3E,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC2C,GAAG,CAAC2H,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EACA1D,8BAA8BA,CAAA;IAC5B,IAAI,CAACjC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC3E,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC0E,eAAe,IAAI,IAAI,CAACD,QAAQ;IACrC,IAAI,CAACI,iBAAiB,IAAI,CAAC;IAC3B,IAAI,CAAClC,GAAG,CAAC0H,aAAa,EAAE;IACxB,IAAI,CAAC/H,cAAc,CAChByK,uBAAuB,CAAC,IAAI,CAACxJ,UAAU,EAAE,IAAI,CAACmB,eAAe,EAAE,IAAI,EAAE,IAAI,CAACG,iBAAiB,CAAC,CAC5F2C,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACE,IAAI,EAAEE,OAAO,CAAC3I,MAAM,IAAI,CAAC,EAAE,IAAI,CAACiF,4BAA4B,GAAG,KAAK;QAC5E,MAAMyJ,oBAAoB,GAAGnG,GAAG,CAACE,IAAI,EAAEE,OAAO,CAACiG,KAAK,CAAC,CAAC,IAAI,CAAClK,QAAQ,CAAC;QACpE,IAAI,CAACK,KAAK,GAAGwD,GAAG,CAACE,IAAI,EAAEE,OAAO,EAAE3I,MAAM;QAEtC0O,oBAAoB,CAAC9F,OAAO,CAAEC,MAAW,IAAI;UAC3C,IAAI,CAAC8F,UAAU,CAAC9F,MAAM,CAAC;QACzB,CAAC,CAAC;QACF,IAAIN,GAAG,CAACE,IAAI,EAAEE,OAAO,CAAC3I,MAAM,EAAE,IAAI,CAAC4E,mBAAmB,GAAG,KAAK,CAAC,KAC1D,IAAI,CAACA,mBAAmB,GAAG,IAAI;QACpC,IAAI,CAAC3E,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC2C,GAAG,CAAC2H,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAGQvB,gBAAgBA,CAAA;IACtB,IAAI6F,aAAa,GAAQN,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;IAC9DD,aAAa,GAAGE,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;IACzC,IAAI,CAACtK,aAAa,GAAGsK,aAAa;IAElC,IAAI,CAACtK,aAAa,EAAEqE,OAAO,CAAEsE,GAAQ,IAAI;MACvC,IAAIA,GAAG,CAACzD,EAAE,IAAI,IAAI,CAACjG,UAAU,EAAE;QAC7B,IAAI,CAAC2J,qBAAqB,CAACD,GAAG,CAAC;;MAEjCA,GAAG,CAAC,MAAM,CAAC,GAAGA,GAAG,CAAC7N,YAAY;MAC9B6N,GAAG,CAAC,QAAQ,CAAC,GAAGA,GAAG,CAACzD,EAAE;MACtB,IAAI,CAACwD,eAAe,CAACC,GAAG,CAACE,UAAU,EAAEF,GAAG,CAAC;IAC3C,CAAC,CAAC;EACJ;EAEAlG,WAAWA,CAAA;IACT,IAAI9I,iBAAiB,CAAC,IAAI,CAACoF,UAAU,CAAC,EAAE;MACtCgC,MAAM,CAAC2J,QAAQ,CAAC;QAACC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAC,CAAC;;EAEjD;EAEApH,gBAAgBA,CAACV,IAAY,EAAEoC,EAAO;IACpC,IAAG,IAAI,CAAChE,iBAAiB,IAAK,IAAI,CAACrC,iBAAiB,CAACgM,aAAa,CAAC/H,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,EAAC;MAC7H,IAAGA,IAAI,KAAK,WAAW,EAAE;QACvB,IAAI,CAAChE,UAAU,CAACgM,QAAQ,CAAC,YAAY,EAAE,gBAAgB,GAAG5F,EAAE,CAAC;OAC9D,MAAM,IAAIpC,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAI,CAAChE,UAAU,CAACgM,QAAQ,CAAC,WAAW,EAAE,eAAe,GAAG5F,EAAE,CAAC;OAC5D,MAAM,IAAIpC,IAAI,KAAK,SAAS,EAAE;QAC7B,IAAI,CAAChE,UAAU,CAACgM,QAAQ,CAAC,WAAW,EAAE,cAAc,GAAG5F,EAAE,CAAC;;MAE5D,IAAI,CAACpG,UAAU,CAACwC,KAAK,CACnB,IAAI,CAACF,OAAO,CAAC2J,MAAM,EACnB7F,EAAE,EACFpC,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,EAC1D,CAAC,EACD,IAAI,EACJ;QACE,IAAI,EAAEoC,EAAE;QACR,SAAS,EAAC,IAAI,CAAC/D,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC6J,YAAY,GAAG;OAC9D,CACF;;EAEL;EACA3Q,eAAeA,CAACiJ,GAAW;IACzB,OAAOzJ,gBAAgB,CAACoR,cAAc,CAAC3H,GAAG,EAAE5J,WAAW,CAAC6F,WAAW,CAAC;EACtE;;qBAlpBWzB,cAAc,EAAAhE,EAAA,CAAAoR,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtR,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxR,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAE,YAAA,GAAAzR,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAG,cAAA,GAAA1R,EAAA,CAAAoR,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA5R,EAAA,CAAAoR,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA9R,EAAA,CAAAoR,iBAAA,CAAApR,EAAA,CAAA+R,iBAAA,GAAA/R,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAS,aAAA,GAAAhS,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAU,aAAA,GAAAjS,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAW,gBAAA,GAAAlS,EAAA,CAAAoR,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAApS,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAc,eAAA,GAAArS,EAAA,CAAAoR,iBAAA,CAAAC,EAAA,CAAAiB,MAAA,GAAAtS,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAgB,cAAA,GAAAvS,EAAA,CAAAoR,iBAAA,CAAAG,EAAA,CAAAiB,iBAAA,GAAAxS,EAAA,CAAAoR,iBAAA,CAAAqB,EAAA,CAAAC,sBAAA,GAAA1S,EAAA,CAAAoR,iBAAA,CAkGfzR,WAAW,GAAAK,EAAA,CAAAoR,iBAAA,CAAAuB,EAAA,CAAAC,UAAA;EAAA;;UAlGV5O,cAAc;IAAA6O,SAAA;IAAAC,YAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAdC,GAAA,CAAA1L,QAAA,CAAA2L,MAAA,CAAgB;QAAA,UAAAlT,EAAA,CAAAmT,eAAA,qBAAAC,yCAAAF,MAAA;UAAA,OAAhBD,GAAA,CAAA7K,QAAA,CAAA8K,MAAA,CAAgB;QAAA,UAAAlT,EAAA,CAAAmT,eAAA;;;;;;;;;;;QC5B7BnT,EAAA,CAAAiB,UAAA,IAAAoS,sCAAA,0BAoCe;QAEfrT,EAAA,CAAAiB,UAAA,IAAAqS,qCAAA,gCAAAtT,EAAA,CAAAuT,sBAAA,CA2Cc;;;;QAjFCvT,EAAA,CAAAK,UAAA,UAAA4S,GAAA,CAAA9L,YAAA,CAAqB,aAAAqM,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}