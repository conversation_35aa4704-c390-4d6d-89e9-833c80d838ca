{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { DialogModule } from 'primeng/dialog';\nimport { passwordValidator } from \"@shared/validators/password.validator\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/password\";\nimport * as i9 from \"primeng/dialog\";\nfunction ResetPasswordConfirmationComponent_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28)(1, \"small\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"auth.registerPassword.validation8\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    color: a0\n  };\n};\nfunction ResetPasswordConfirmationComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r5.hasMinimum8Chars === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation1\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    color: \"grey\"\n  };\n};\nfunction ResetPasswordConfirmationComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation1\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r10.hasLowerChar === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation2\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation2\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r15.hasUpperChar === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation3\"), \"\");\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation3\"), \"\");\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r20.hasAtleastOneNumber === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation4\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation4\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 30);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 31);\n  }\n}\nfunction ResetPasswordConfirmationComponent_em_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 32);\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, ctx_r25.hasSpecialChars === true ? \"#01B467\" : \"red\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation5\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, \"auth.registerPassword.validation5\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_p_61_em_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 36);\n  }\n}\nfunction ResetPasswordConfirmationComponent_p_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵtemplate(1, ResetPasswordConfirmationComponent_p_61_em_1_Template, 1, 0, \"em\", 34);\n    i0.ɵɵelementStart(2, \"span\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.hasSpecialChars === false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"auth.registerPassword.validation7\"));\n  }\n}\nfunction ResetPasswordConfirmationComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"img\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 1, \"updatePassword.passwordReset\"), \" \");\n  }\n}\nconst _c2 = function () {\n  return [\"/login\"];\n};\nfunction ResetPasswordConfirmationComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 40);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(1, 2, \"updatePassword.backToLogin\"))(\"routerLink\", i0.ɵɵpureFunction0(4, _c2));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nconst _c4 = function () {\n  return {\n    width: \"22vw\"\n  };\n};\nconst _c5 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"100vw\"\n  };\n};\nexport class ResetPasswordConfirmationComponent {\n  store;\n  fb;\n  permissionService;\n  user;\n  translate;\n  messageService;\n  el;\n  router;\n  newPassword = \"\";\n  confirmPassword = \"\";\n  phoneNumber;\n  verificationCode = \"\";\n  hasUpperChar;\n  hasAtleastOneNumber;\n  hasLowerChar;\n  passwordMatched = false;\n  hasMinimum8Chars = undefined;\n  hasSpecialChars;\n  passwordIsValid = false;\n  password = \"\";\n  forgetPassword;\n  firstNameFlag = false;\n  displayApprovedModal = false;\n  isMobileLayout = false;\n  constructor(store, fb, permissionService, user, translate, messageService, el, router) {\n    this.store = store;\n    this.fb = fb;\n    this.permissionService = permissionService;\n    this.user = user;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.el = el;\n    this.router = router;\n  }\n  get formcontrols() {\n    return this.forgetPassword.controls;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.verificationCode = this.store.get('verificationCode');\n    this.phoneNumber = localStorage.getItem('userPhone');\n    this.formGroup();\n  }\n  formGroup() {\n    this.forgetPassword = this.fb.group({\n      newPassword: ['', [Validators.required, passwordValidator()]],\n      confirmPassword: ['', Validators.required]\n    });\n  }\n  approveModal() {\n    this.user.UpdatePassword({\n      mobileNumber: this.phoneNumber,\n      newPassword: this.forgetPassword?.controls['newPassword']?.value,\n      RequestId: this.verificationCode\n    }).subscribe({\n      next: res => {\n        if (res.success) {\n          this.displayApprovedModal = true;\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant(res.message)\n          });\n        }\n      },\n      error: err => {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: err.message\n        });\n      }\n    });\n  }\n  checkPasswordPattern(password) {\n    this.hasAtleastOneNumber = /\\d+/.test(password);\n    this.hasUpperChar = /[A-Z]+/.test(password);\n    this.hasLowerChar = /[a-z]+/.test(password);\n    this.hasMinimum8Chars = /.{10,}/.test(password);\n    this.hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    if (this.confirmPassword === this.password) this.passwordMatched = true;else this.passwordMatched = false;\n    this.passwordIsValid = this.hasMinimum8Chars && this.hasUpperChar && this.hasLowerChar && this.hasSpecialChars && this.passwordMatched && this.hasAtleastOneNumber;\n    return this.passwordIsValid;\n  }\n  disabled() {\n    if ((this.forgetPassword?.controls['newPassword']?.value === this.forgetPassword?.controls['confirmPassword']?.value || this.forgetPassword?.controls['newPassword']?.value === '') && this.hasSpecialChars && this.hasUpperChar && this.hasLowerChar && this.hasMinimum8Chars && this.hasAtleastOneNumber) {\n      return false;\n    } else {\n      return true;\n    }\n  }\n  onblur(value) {\n    this.isRequired(\"newPassword\");\n    if (value !== 'cp' && !this.hasMinimum8Chars && !this.hasAtleastOneNumber && !this.hasLowerChar && !this.hasSpecialChars && !this.hasUpperChar) {\n      this.hasMinimum8Chars = false;\n      this.hasLowerChar = false;\n      this.hasAtleastOneNumber = false;\n      this.hasSpecialChars = false;\n      this.hasUpperChar = false;\n    }\n  }\n  isRequired(fieldName) {\n    if (this.formcontrols[fieldName]?.errors?.required) {\n      this.firstNameFlag = true;\n    }\n  }\n  isError(fieldName) {\n    return this.formcontrols[fieldName]?.errors;\n  }\n  focusFirstInvalidField() {\n    for (const key of Object.keys(this.formcontrols)) {\n      if (this.formcontrols[key].invalid) {\n        this.focusInputField(key);\n        break;\n      }\n    }\n  }\n  focusInputField(key) {\n    const invalidControl = this.el.nativeElement.querySelector('[formControlName=\"' + key + '\"]');\n    invalidControl.focus();\n  }\n  static ɵfac = function ResetPasswordConfirmationComponent_Factory(t) {\n    return new (t || ResetPasswordConfirmationComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i5.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResetPasswordConfirmationComponent,\n    selectors: [[\"app-reset-password-confirmation\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 67,\n    vars: 66,\n    consts: [[1, \"update-password-page\"], [1, \"content-container\", 3, \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-100\"], [1, \"col-12\", \"py-0\", \"text-center\", \"reset-pass\"], [1, \"col-12\", \"mb-3\", \"text-center\", \"secure-pass\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"shadow-signin\", \"px-5\", \"pt-0\", \"pb-4\"], [1, \"p-fluid\", \"p-grid\"], [3, \"formGroup\"], [\"form\", \"ngForm\"], [1, \"p-field\", \"p-col-12\", \"mt-3\"], [1, \"p-float-label\", \"mt-5\"], [\"aria-autocomplete\", \"false\", \"autocomplete\", \"newPassword\", \"formControlName\", \"newPassword\", \"name\", \"something\", 1, \"customClass\", 3, \"feedback\", \"toggleMask\", \"click\", \"ngModelChange\"], [3, \"ngStyle\"], [\"class\", \"field-error\", 4, \"ngIf\"], [1, \"p-float-label\", \"mt-3\"], [\"aria-autocomplete\", \"false\", \"autocomplete\", \"newPassword\", \"formControlName\", \"confirmPassword\", \"name\", \"something\", 1, \"customClass\", 3, \"ngModel\", \"feedback\", \"toggleMask\", \"click\", \"ngModelChange\"], [1, \"p-field\", \"p-col-12\"], [1, \"list\", \"p-0\"], [1, \"list-none\", \"font-size-13\", \"font-italic\", \"mb-2\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-info-circle\", \"style\", \"color: grey; font-size: 0.8rem\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-check-circle\", \"style\", \"color: #01b467; font-size: 0.8rem\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", \"class\", \"pi pi-times-circle\", \"style\", \"color: red; font-size: 0.8rem\", 4, \"ngIf\"], [\"class\", \"ml-2 instruction\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"list-none\", \"font-size-13\", \"font-italic\", \"mb-5\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-2\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"approvedModal\", 3, \"visible\", \"modal\", \"baseZIndex\", \"breakpoints\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"field-error\"], [2, \"color\", \"red\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-info-circle\", 2, \"color\", \"grey\", \"font-size\", \"0.8rem\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-check-circle\", 2, \"color\", \"#01b467\", \"font-size\", \"0.8rem\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-times-circle\", 2, \"color\", \"red\", \"font-size\", \"0.8rem\"], [1, \"ml-2\", \"instruction\", 3, \"ngStyle\"], [\"aria-hidden\", \"true\", \"class\", \"fa fa-times-circle fa-size\", \"style\", \"color: red\", 4, \"ngIf\"], [1, \"ml-2\", \"instruction\", 2, \"color\", \"red\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-times-circle\", \"fa-size\", 2, \"color\", \"red\"], [1, \"icon\", \"mt-5\", \"mb-5\", \"bg-#01B467-500\", \"text-white\", \"text-center\", \"w-3rem\", \"h-3rem\", \"border-circle\", \"icon\", \"bg-#01B467-500\"], [\"alt\", \"No Image\", \"src\", \"assets/images/Validated.png\"], [1, \"font-bold\", \"text-center\", \"text-black-alpha-90\", \"font-family-password\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-4\", \"width-25\", \"m-auto\", \"font-size-14\", \"second-btn\", 3, \"label\", \"routerLink\"]],\n    template: function ResetPasswordConfirmationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵelementContainerStart(1);\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"p\", 3);\n        i0.ɵɵtext(5);\n        i0.ɵɵpipe(6, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 4);\n        i0.ɵɵtext(8);\n        i0.ɵɵpipe(9, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"form\", 7, 8)(14, \"div\", 9)(15, \"span\", 10)(16, \"p-password\", 11);\n        i0.ɵɵlistener(\"click\", function ResetPasswordConfirmationComponent_Template_p_password_click_16_listener() {\n          return ctx.onblur();\n        })(\"ngModelChange\", function ResetPasswordConfirmationComponent_Template_p_password_ngModelChange_16_listener($event) {\n          return ctx.checkPasswordPattern($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"label\", 12);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(20, ResetPasswordConfirmationComponent_p_20_Template, 4, 3, \"p\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"span\", 14)(23, \"p-password\", 15);\n        i0.ɵɵlistener(\"click\", function ResetPasswordConfirmationComponent_Template_p_password_click_23_listener() {\n          return ctx.onblur(\"cp\");\n        })(\"ngModelChange\", function ResetPasswordConfirmationComponent_Template_p_password_ngModelChange_23_listener($event) {\n          return ctx.confirmPassword = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"label\", 12);\n        i0.ɵɵtext(25);\n        i0.ɵɵpipe(26, \"translate\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(27, \"br\");\n        i0.ɵɵelementStart(28, \"div\", 16)(29, \"ul\", 17)(30, \"li\", 18);\n        i0.ɵɵtemplate(31, ResetPasswordConfirmationComponent_em_31_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(32, ResetPasswordConfirmationComponent_em_32_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(33, ResetPasswordConfirmationComponent_em_33_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(34, ResetPasswordConfirmationComponent_span_34_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(35, ResetPasswordConfirmationComponent_span_35_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"li\", 18);\n        i0.ɵɵtemplate(37, ResetPasswordConfirmationComponent_em_37_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(38, ResetPasswordConfirmationComponent_em_38_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(39, ResetPasswordConfirmationComponent_em_39_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(40, ResetPasswordConfirmationComponent_span_40_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(41, ResetPasswordConfirmationComponent_span_41_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"li\", 18);\n        i0.ɵɵtemplate(43, ResetPasswordConfirmationComponent_em_43_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(44, ResetPasswordConfirmationComponent_em_44_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(45, ResetPasswordConfirmationComponent_em_45_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(46, ResetPasswordConfirmationComponent_span_46_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(47, ResetPasswordConfirmationComponent_span_47_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"li\", 18);\n        i0.ɵɵtemplate(49, ResetPasswordConfirmationComponent_em_49_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(50, ResetPasswordConfirmationComponent_em_50_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(51, ResetPasswordConfirmationComponent_em_51_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(52, ResetPasswordConfirmationComponent_span_52_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(53, ResetPasswordConfirmationComponent_span_53_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"li\", 18);\n        i0.ɵɵtemplate(55, ResetPasswordConfirmationComponent_em_55_Template, 1, 0, \"em\", 19);\n        i0.ɵɵtemplate(56, ResetPasswordConfirmationComponent_em_56_Template, 1, 0, \"em\", 20);\n        i0.ɵɵtemplate(57, ResetPasswordConfirmationComponent_em_57_Template, 1, 0, \"em\", 21);\n        i0.ɵɵtemplate(58, ResetPasswordConfirmationComponent_span_58_Template, 3, 6, \"span\", 22);\n        i0.ɵɵtemplate(59, ResetPasswordConfirmationComponent_span_59_Template, 3, 5, \"span\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"li\", 23);\n        i0.ɵɵtemplate(61, ResetPasswordConfirmationComponent_p_61_Template, 5, 4, \"p\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(62, \"button\", 24);\n        i0.ɵɵlistener(\"click\", function ResetPasswordConfirmationComponent_Template_button_click_62_listener() {\n          return ctx.approveModal();\n        });\n        i0.ɵɵpipe(63, \"translate\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(64, \"p-dialog\", 25);\n        i0.ɵɵlistener(\"visibleChange\", function ResetPasswordConfirmationComponent_Template_p_dialog_visibleChange_64_listener($event) {\n          return ctx.displayApprovedModal = $event;\n        });\n        i0.ɵɵtemplate(65, ResetPasswordConfirmationComponent_ng_template_65_Template, 5, 3, \"ng-template\", 26);\n        i0.ɵɵtemplate(66, ResetPasswordConfirmationComponent_ng_template_66_Template, 2, 5, \"ng-template\", 27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_6_0;\n        let tmp_8_0;\n        let tmp_12_0;\n        let tmp_39_0;\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(58, _c3, ctx.isMobileLayout ? \"1rem\" : \"220px\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 48, \"updatePassword.resetPassword\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 50, \"updatePassword.setUpSecurePassword\"), \". \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.forgetPassword);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"feedback\", true)(\"toggleMask\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(60, _c0, ctx.firstNameFlag && ((tmp_6_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_6_0.value.length) === 0 ? \"red\" : \"grey\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(19, 52, \"updatePassword.newPassword\"), \" *\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.invalidPassword);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.confirmPassword)(\"feedback\", false)(\"toggleMask\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(62, _c0, ctx.firstNameFlag && ((tmp_12_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_12_0.value.length) === 0 || ((tmp_12_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_12_0.value) !== ((tmp_12_0 = ctx.forgetPassword.get(\"confirmPassword\")) == null ? null : tmp_12_0.value) ? \"red\" : \"grey\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(26, 54, \"updatePassword.confirmPassword\"), \" *\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMinimum8Chars === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasLowerChar === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasUpperChar === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAtleastOneNumber === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars !== undefined);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasSpecialChars === undefined);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.forgetPassword == null ? null : (tmp_39_0 = ctx.forgetPassword.get(\"newPassword\")) == null ? null : tmp_39_0.value) !== (ctx.forgetPassword == null ? null : (tmp_39_0 = ctx.forgetPassword.get(\"confirmPassword\")) == null ? null : tmp_39_0.value));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.disabled() === true)(\"label\", i0.ɵɵpipeBind1(63, 56, \"updatePassword.passwordReset\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(64, _c4));\n        i0.ɵɵproperty(\"visible\", ctx.displayApprovedModal)(\"modal\", true)(\"baseZIndex\", 10000)(\"breakpoints\", i0.ɵɵpureFunction0(65, _c5));\n      }\n    },\n    dependencies: [CommonModule, i6.NgIf, i6.NgStyle, ReactiveFormsModule, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, TranslateModule, i3.TranslatePipe, ButtonModule, i7.ButtonDirective, i4.PrimeTemplate, InputTextModule, PasswordModule, i8.Password, DialogModule, i9.Dialog, RouterModule, i5.RouterLink],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog .p-dialog-header {\\n  display: none;\\n}\\n\\ndiv.icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: auto;\\n}\\ndiv.icon[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 31%;\\n  font-weight: bold;\\n  left: 30%;\\n  font-size: 1.2rem;\\n}\\n\\n  .p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n\\n  .p-dialog .p-dialog-footer button {\\n  width: 90% !important;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Helvetica, Arial, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\";\\n  font-size: 1rem;\\n  color: #495057;\\n  background: #ffffff;\\n  padding: 0.5rem 0.5rem;\\n  border: 1px solid #ced4da;\\n  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;\\n  appearance: none;\\n  border-radius: 3px;\\n  width: 100%;\\n}\\n\\n.green[_ngcontent-%COMP%] {\\n  color: forestgreen !important;\\n}\\n\\n.fa-size[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n\\nli[_ngcontent-%COMP%] {\\n  text-align: left;\\n  letter-spacing: 0px;\\n  opacity: 1;\\n  font-family: \\\"main-medium\\\", sans-serif;\\n  font-style: inherit !important;\\n}\\n\\n.reset-pass[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.secure-pass[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: #A3A3A3;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n  .customClass input {\\n  height: 60px !important;\\n  width: 100%;\\n  border-radius: 5px 5px 0px 0px;\\n  opacity: 1;\\n  border: none !important;\\n  border-bottom: 1px solid #b9b9b9 !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  padding-top: 20px;\\n  background-color: #f5f5f5 !important;\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n}\\n\\n  .pi-eye {\\n  position: absolute !important;\\n  right: 9px !important;\\n  top: 30px !important;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  color: #323232 !important;\\n  position: absolute;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\\n\\n.instruction[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 400;\\n  font-family: var(--regular-font) !important;\\n}\\n\\n.shadow-signin[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n}\\n\\n.second-btn[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  text-transform: uppercase;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .update-password-page[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.font-family-password[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog {\\n  top: -74px !important;\\n  box-shadow: none !important;\\n  border: 1px solid rgba(151, 151, 151, 0.17) !important;\\n}\\n\\n  .p-dialog-content {\\n  margin-top: 4rem !important;\\n}\\n\\n  .p-dialog-footer {\\n  padding-bottom: 0rem !important;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .margin-x-100[_ngcontent-%COMP%] {\\n    margin-top: 250px !important;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}