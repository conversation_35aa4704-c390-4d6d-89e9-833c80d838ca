<section class="categories-page">
  <div class="breadcrumb breadcrumb-spacing">
    <p-breadcrumb [home]="home" [model]="items"></p-breadcrumb>
  </div>

  <div class="mt-2 main_font content-container">
    <div class="grid">
      <div class="col-12 col-md-12 flex md:justify-content-start all-categ">
        <div class="all-category bold-font">
          {{ "categories.allCategories" | translate }}
        </div>
      </div>
      <div class="my-4 flex flex-row flex-wrap card-category">
        <a
          *ngFor="let category of categories; let i = index"
          class="mt-2 mx-2 mb-5 card-spaces"
        >
          <app-mtn-category-card
            [category]="category"
          ></app-mtn-category-card>
        </a>
      </div>
    </div>
  </div>

  <!-- Show products when a category is selected -->
  <div *ngIf="selectedCategory && products.length > 0" class="selected-category-products">
    <h3>{{ selectedCategory.categoryName }} Products</h3>
    <div class="products-grid">
      <div *ngFor="let product of products; let i = index" class="product-item">
        {{ product.name }}
      </div>
    </div>
  </div>
</section>
