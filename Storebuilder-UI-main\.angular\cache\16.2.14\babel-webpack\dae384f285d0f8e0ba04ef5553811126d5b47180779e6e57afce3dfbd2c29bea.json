{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ShipmentService {\n  constructor(http) {\n    this.http = http;\n    this.shipmentCost = 0;\n    this.actualShipmentFee = 0;\n    this.latitude = \"\";\n    this.longitude = \"\";\n    this.currentShipment = null;\n    this.currentOrderTotal = 0;\n    this.updateShipMentUrl = '';\n    this.baseUrl = `${environment.apiEndPoint}/Shipment/YellowBirdShipment`;\n    this.updateShipMentUrl = `${environment.apiEndPoint}/Shipment/PAPsShipment`;\n  }\n  createDeliveryRequest(data) {\n    return this.http.post(`${this.baseUrl}/DeliveryRequest`, data);\n  }\n  GetDeliveryPrice(data) {\n    return this.http.post(`${this.baseUrl}/PriceEstimation`, data);\n  }\n  updateShipmentStatus(orderId) {\n    return this.http.get(`${this.updateShipMentUrl}/UpdateShipmentStatus?orderId=${orderId}`);\n  }\n  static #_ = this.ɵfac = function ShipmentService_Factory(t) {\n    return new (t || ShipmentService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ShipmentService,\n    factory: ShipmentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "ShipmentService", "constructor", "http", "shipmentCost", "actualShipmentFee", "latitude", "longitude", "currentShipment", "currentOrderTotal", "updateShipMentUrl", "baseUrl", "apiEndPoint", "createDeliveryRequest", "data", "post", "GetDeliveryPrice", "updateShipmentStatus", "orderId", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\shipment.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from '@environments/environment';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {Observable} from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ShipmentService {\r\n\r\n  shipmentCost: number = 0;\r\n  actualShipmentFee : number = 0;\r\n  baseUrl: string;\r\n  latitude: string = \"\";\r\n  longitude: string = \"\";\r\n  shipmentLoaded: boolean | undefined;\r\n  currentShipment: any = null;\r\n  currentOrderTotal: number = 0;\r\n  updateShipMentUrl: string = '';\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}/Shipment/YellowBirdShipment`;\r\n    this.updateShipMentUrl = `${environment.apiEndPoint}/Shipment/PAPsShipment`;\r\n  }\r\n\r\n\r\n  createDeliveryRequest(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/DeliveryRequest`, data);\r\n  }\r\n\r\n  GetDeliveryPrice(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/PriceEstimation`, data);\r\n  }\r\n\r\n\r\n  updateShipmentStatus(orderId: any): any {\r\n    return this.http.get<any>(`${this.updateShipMentUrl}/UpdateShipmentStatus?orderId=${orderId}`)\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,2BAA2B;;;AAOrD,OAAM,MAAOC,eAAe;EAY1BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAXd,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,iBAAiB,GAAY,CAAC;IAE9B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,eAAe,GAAQ,IAAI;IAC3B,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,iBAAiB,GAAW,EAAE;IAK5B,IAAI,CAACC,OAAO,GAAG,GAAGX,WAAW,CAACY,WAAW,8BAA8B;IACvE,IAAI,CAACF,iBAAiB,GAAG,GAAGV,WAAW,CAACY,WAAW,wBAAwB;EAC7E;EAGAC,qBAAqBA,CAACC,IAAS;IAC7B,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,kBAAkB,EAAEG,IAAI,CAAC;EAChE;EAEAE,gBAAgBA,CAACF,IAAS;IACxB,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,kBAAkB,EAAEG,IAAI,CAAC;EAChE;EAGAG,oBAAoBA,CAACC,OAAY;IAC/B,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAM,GAAG,IAAI,CAACT,iBAAiB,iCAAiCQ,OAAO,EAAE,CAAC;EAChG;EAAC,QAAAE,CAAA,G;qBA/BUnB,eAAe,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfxB,eAAe;IAAAyB,OAAA,EAAfzB,eAAe,CAAA0B,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}