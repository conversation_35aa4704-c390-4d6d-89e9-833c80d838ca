{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TransactionService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Payment`;\n  }\n  createTransaction(data) {\n    return this.http.post(`${this.baseUrl}/Transaction/CreateTransaction`, data);\n  }\n  createMultipleTransactionDetails(data) {\n    return this.http.post(`${this.baseUrl}/TransactionDetails/CreateMultipleTransactionDetails`, data);\n  }\n  GetOrderTransaction(id) {\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionByOrderWithoutFilter/${id}`);\n  }\n  GetTransactionById(id) {\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionById/${id}`);\n  }\n  static #_ = this.ɵfac = function TransactionService_Factory(t) {\n    return new (t || TransactionService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TransactionService,\n    factory: TransactionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "TransactionService", "constructor", "http", "baseUrl", "apiEndPoint", "createTransaction", "data", "post", "createMultipleTransactionDetails", "GetOrderTransaction", "id", "get", "GetTransactionById", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\transaction.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {environment} from 'src/environments/environment';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {Observable} from \"rxjs\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TransactionService {\r\n\r\n  baseUrl: string;\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) {\r\n    this.baseUrl = `${environment.apiEndPoint}/Payment`;\r\n  }\r\n\r\n  createTransaction(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/Transaction/CreateTransaction`, data);\r\n  }\r\n  createMultipleTransactionDetails(data: any): Observable<object> {\r\n    return this.http.post(`${this.baseUrl}/TransactionDetails/CreateMultipleTransactionDetails`, data);\r\n  }\r\n  GetOrderTransaction(id: any): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionByOrderWithoutFilter/${id}`);\r\n  }\r\n  GetTransactionById(id: any): Observable<object> {\r\n    return this.http.get(`${this.baseUrl}/Transaction/GetTransactionById/${id}`);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAQA,WAAW,QAAO,8BAA8B;;;AAOxD,OAAM,MAAOC,kBAAkB;EAI7BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAEZ,IAAI,CAACC,OAAO,GAAG,GAAGJ,WAAW,CAACK,WAAW,UAAU;EACrD;EAEAC,iBAAiBA,CAACC,IAAS;IACzB,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,gCAAgC,EAAEG,IAAI,CAAC;EAC9E;EACAE,gCAAgCA,CAACF,IAAS;IACxC,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,sDAAsD,EAAEG,IAAI,CAAC;EACpG;EACAG,mBAAmBA,CAACC,EAAO;IACzB,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAC,GAAG,IAAI,CAACR,OAAO,mDAAmDO,EAAE,EAAE,CAAC;EAC9F;EACAE,kBAAkBA,CAACF,EAAO;IACxB,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAC,GAAG,IAAI,CAACR,OAAO,mCAAmCO,EAAE,EAAE,CAAC;EAC9E;EAAC,QAAAG,CAAA,G;qBArBUb,kBAAkB,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAlBlB,kBAAkB;IAAAmB,OAAA,EAAlBnB,kBAAkB,CAAAoB,IAAA;IAAAC,UAAA,EAFjB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}