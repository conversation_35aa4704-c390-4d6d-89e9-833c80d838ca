{"ast": null, "code": "import { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"ngx-google-analytics\";\nimport * as i4 from \"./footer.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ngx-translate/core\";\nfunction FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r18.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.contactUs\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r20.navigateToSellerHub());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.sellOnMarketplace\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", \"/orders\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 6, \"footer.myOrders\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 8, \"footer.myAddress\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/details\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 10, \"footer.myDetails\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const eachPage_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r22.reloadCurrentPage(eachPage_r12.id, eachPage_r12.pageTitle));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const eachPage_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", eachPage_r12.pageTitle, \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_1_Template, 3, 3, \"div\", 19);\n    i0.ɵɵtemplate(2, FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_2_Template, 3, 3, \"div\", 19);\n    i0.ɵɵtemplate(3, FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_ng_container_3_Template, 10, 12, \"ng-container\", 20);\n    i0.ɵɵtemplate(4, FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_4_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachPage_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const eachSection_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r13 === 0 && eachSection_r7.sectionName === \"Help\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r13 === 0 && eachSection_r7.sectionName === \"Seller hub\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r13 === 0 && eachSection_r7.sectionName === \"Account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachPage_r12.visibility);\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_Template, 5, 4, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachSection_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", eachSection_r7.pages);\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_template_4_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_div_9_ng_template_4_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r31.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.contactUs\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_div_9_ng_template_4_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r33.navigateToSellerHub());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.sellOnMarketplace\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", \"/orders\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 6, \"footer.myOrders\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 8, \"footer.myAddress\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/details\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 10, \"footer.myDetails\"), \" \");\n  }\n}\nfunction FooterComponent_footer_0_div_9_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FooterComponent_footer_0_div_9_ng_template_4_div_0_Template, 3, 3, \"div\", 19);\n    i0.ɵɵtemplate(1, FooterComponent_footer_0_div_9_ng_template_4_div_1_Template, 3, 3, \"div\", 19);\n    i0.ɵɵtemplate(2, FooterComponent_footer_0_div_9_ng_template_4_ng_container_2_Template, 10, 12, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const eachSection_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", eachSection_r7.sectionName === \"Help\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r7.sectionName === \"Seller hub\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r7.sectionName === \"Account\");\n  }\n}\nfunction FooterComponent_footer_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FooterComponent_footer_0_div_9_ng_container_3_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵtemplate(4, FooterComponent_footer_0_div_9_ng_template_4_Template, 3, 3, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const eachSection_r7 = ctx.$implicit;\n    const _r9 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", eachSection_r7.sectionName, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r7.pages.length > 0)(\"ngIfElse\", _r9);\n  }\n}\nfunction FooterComponent_footer_0_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.footerDetails.mainLogo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_0_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n}\nfunction FooterComponent_footer_0_div_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"a\", 32);\n    i0.ɵɵelement(2, \"em\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const link_r37 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", link_r37.iconLink, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(link_r37.iconClass);\n  }\n}\nfunction FooterComponent_footer_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtemplate(3, FooterComponent_footer_0_div_14_div_3_Template, 3, 4, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.footerLinks);\n  }\n}\nfunction FooterComponent_footer_0_div_15_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"img\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const logo_r39 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r39, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35)(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\");\n    i0.ɵɵelement(7, \"img\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38);\n    i0.ɵɵtemplate(10, FooterComponent_footer_0_div_15_div_10_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 40);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"footer.weAccept\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r6.footerDetails.sideLogo, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.footerDetails.payments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 6, ctx_r6.footerDetails.copyRights), \" \");\n  }\n}\nfunction FooterComponent_footer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 2)(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.backToTop());\n    });\n    i0.ɵɵelement(2, \"em\", 4);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n    i0.ɵɵtemplate(9, FooterComponent_footer_0_div_9_Template, 6, 3, \"div\", 8);\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵtemplate(11, FooterComponent_footer_0_img_11_Template, 1, 1, \"img\", 10);\n    i0.ɵɵtemplate(12, FooterComponent_footer_0_img_12_Template, 1, 0, \"img\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 12);\n    i0.ɵɵtemplate(14, FooterComponent_footer_0_div_14_Template, 4, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, FooterComponent_footer_0_div_15_Template, 14, 8, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, \"footer.backToTop\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.allData);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.scConfig);\n  }\n}\nfunction FooterComponent_footer_1_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r42.footerDetails.mainLogoMobile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r59.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.contactUs\"), \" \");\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const eachPage_r55 = i0.ɵɵnextContext().$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r61.reloadCurrentPage(eachPage_r55.id, eachPage_r55.pageTitle));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const eachPage_r55 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", eachPage_r55.pageTitle, \" \");\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_1_Template, 3, 3, \"div\", 56);\n    i0.ɵɵtemplate(2, FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_2_Template, 2, 1, \"div\", 57);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachPage_r55 = ctx.$implicit;\n    const i_r56 = ctx.index;\n    const eachSection_r49 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r56 === 0 && eachSection_r49.sectionName === \"Help\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachPage_r55.visibility);\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachSection_r49 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", eachSection_r49.pages);\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r69);\n      const ctx_r68 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r68.navigatePage(\"contact-us\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"footer.contactUs\"), \" \");\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_div_0_Template, 3, 3, \"div\", 56);\n  }\n  if (rf & 2) {\n    const eachSection_r49 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngIf\", eachSection_r49.sectionName === \"Help\");\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵtemplate(2, FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r52 = i0.ɵɵreference(3);\n    const eachSection_r49 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r49.pages.length > 0)(\"ngIfElse\", _r52);\n  }\n}\nfunction FooterComponent_footer_1_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_ng_container_15_div_1_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const eachSection_r49 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", eachSection_r49.sectionName !== \"Seller hub\" && eachSection_r49.sectionName !== \"Account\");\n  }\n}\nfunction FooterComponent_footer_1_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"img\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const logo_r73 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r73, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, FooterComponent_footer_1_div_16_div_1_Template, 2, 1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r45.footerDetails.payments);\n  }\n}\nfunction FooterComponent_footer_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 63);\n    i0.ɵɵelement(2, \"img\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const logo_r74 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", logo_r74.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", logo_r74.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx_r47.footerDetails.copyRightsMobile), \" \");\n  }\n}\nfunction FooterComponent_footer_1_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"img\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r48.footerDetails.bottonLogo, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FooterComponent_footer_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 42)(1, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.backToTop());\n    });\n    i0.ɵɵelement(2, \"em\", 4);\n    i0.ɵɵelementStart(3, \"div\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43)(7, \"div\", 44)(8, \"div\", 45);\n    i0.ɵɵtemplate(9, FooterComponent_footer_1_img_9_Template, 1, 1, \"img\", 10);\n    i0.ɵɵtemplate(10, FooterComponent_footer_1_img_10_Template, 1, 0, \"img\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function FooterComponent_footer_1_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.navigateToSellerHub());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 47);\n    i0.ɵɵtemplate(15, FooterComponent_footer_1_ng_container_15_Template, 2, 1, \"ng-container\", 18);\n    i0.ɵɵtemplate(16, FooterComponent_footer_1_div_16_Template, 2, 1, \"div\", 48);\n    i0.ɵɵelementStart(17, \"div\", 49)(18, \"div\", 50);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 51);\n    i0.ɵɵtemplate(22, FooterComponent_footer_1_div_22_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, FooterComponent_footer_1_div_23_Template, 3, 3, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, FooterComponent_footer_1_div_24_Template, 2, 1, \"div\", 53);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 10, \"footer.backToTop\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.scConfig);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 12, \"footer.becomeSeller\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.allData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 14, \"footer.reachOut\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.footerDetails.socials);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.scConfig);\n  }\n}\nexport class FooterComponent {\n  onResize(event) {\n    this.updateZoomClass();\n  }\n  updateZoomClass() {\n    if (isPlatformBrowser(this.platformId)) {\n      const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n      if (zoomLevel <= 91) {\n        this.zoomLevelClass = 'zoom-110';\n      } else if (zoomLevel <= 112) {\n        this.zoomLevelClass = 'zoom-90';\n      } else if (zoomLevel <= 125) {\n        this.zoomLevelClass = 'zoom-80';\n      } else if (zoomLevel <= 134) {\n        this.zoomLevelClass = 'zoom-75';\n      } else if (zoomLevel <= 150) {\n        this.zoomLevelClass = 'zoom-67';\n      } else if (zoomLevel <= 200) {\n        this.zoomLevelClass = 'zoom-50';\n      } else if (zoomLevel <= 300) {\n        this.zoomLevelClass = 'zoom-33';\n      } else if (zoomLevel <= 400) {\n        this.zoomLevelClass = 'zoom-25';\n      } else {\n        this.zoomLevelClass = 'default-zoom';\n      }\n    }\n  }\n  constructor(store, router, platformId, appDataService, permissionService, $gaService, authService, footerService) {\n    this.store = store;\n    this.router = router;\n    this.platformId = platformId;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.authService = authService;\n    this.footerService = footerService;\n    this.issSellMarketplace = environment.isStoreCloud;\n    this.scrollToTop = new EventEmitter();\n    this.scConfig = false;\n    this.isSellerHub = '';\n    this.environment = environment;\n    this.zoomLevelClass = 'default-zoom';\n    this.isMobileTemplate = false;\n    this.isGoogleAnalytics = false;\n    this.footerLinks = [{\n      iconClass: 'fa-brands fa-square-facebook',\n      iconLink: 'https://www.facebook.com/YallaSuperMallEG',\n      iconName: 'facebook'\n    }, {\n      iconClass: 'fa-brands fa-square-twitter',\n      iconLink: 'https://twitter.com/YallaSuperappEG',\n      iconName: 'twitter'\n    }, {\n      iconClass: 'fa-brands fa-youtube',\n      iconLink: 'https://www.youtube.com/@yallasuperapp3030',\n      iconName: 'youtube'\n    }, {\n      iconClass: 'fa-brands fa-instagram',\n      iconLink: 'https://www.instagram.com/yallasupermalleg/',\n      iconName: 'instagram'\n    }, {\n      iconClass: 'fa-brands fa-tiktok',\n      iconLink: 'https://www.tiktok.com/@yallasupermall',\n      iconName: 'tiktok'\n    }];\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.scConfig = environment.isStoreCloud;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.screen.width;\n      if (this.screenWidth > 768) {\n        this.desktopView = true;\n      } else {\n        this.desktopView = false;\n      }\n    }\n    if (!this.authService.isMarketplace()) {\n      const socialLinks = this.appDataService.shopSettingData;\n      if (socialLinks) {\n        for (const link of this.footerLinks) {\n          if (socialLinks.hasOwnProperty(link.iconName) && socialLinks[link.iconName] !== null) {\n            link.iconUrl = socialLinks[link.iconName];\n          }\n        }\n      }\n    }\n  }\n  ngOnInit() {\n    this.footerService.getAllFooterSectionsWithPages().subscribe(res => {\n      this.allData = res.data;\n      let result = this.findPageWithTitle('terms and conditions');\n      if (!result) result = this.findPageWithTitle('termes et conditions');\n      if (result) {\n        localStorage.setItem('TermsAndConditionsId', result?.id);\n      }\n    });\n  }\n  findPageWithTitle(searchText) {\n    for (const section of this.allData) {\n      const page = section.pages.find(page => page.pageTitle?.toLowerCase().includes(searchText.toLowerCase()));\n      if (page) {\n        return page;\n      }\n    }\n    return undefined;\n  }\n  reloadCurrentPage(pageId, title) {\n    if (this.isGoogleAnalytics) {\n      if (title.includes('Terms and Conditions')) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_TERMS_AND_CONDITIONS, '', 'TERMS_AND_CONDITION', 1, true);\n      } else if (title.includes('Privacy policy')) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_PRIVACY_POLICY, '', 'PRIVACY_POLICY', 1, true);\n      }\n    }\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/about-us/'], {\n      queryParams: {\n        pageId: pageId,\n        title: title\n      },\n      state: {\n        id: pageId\n      }\n    }));\n  }\n  navigatePage(url) {\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/' + url + '/']));\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('isShop').subscribe({\n        next: res => this.isShop = res\n      });\n    }, 10);\n    this.store.subscription('allowedFeature').subscribe({\n      next: res => {\n        if (res.length) {\n          this.isSellerHub = localStorage.getItem('isSellerHub');\n        }\n      }\n    });\n  }\n  backToTop() {\n    window.scroll(0, 0);\n    if (isPlatformBrowser(this.platformId)) {\n      window.scroll({\n        top: 0,\n        left: 0,\n        behavior: 'auto'\n      });\n      this.scrollToTop.emit();\n    }\n  }\n  navigateToSellerHub() {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE_FOOTER, '', '', 1, true);\n    }\n    if (isPlatformBrowser(this.platformId)) {\n      window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\n    }\n  }\n  static #_ = this.ɵfac = function FooterComponent_Factory(t) {\n    return new (t || FooterComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i4.FooterService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FooterComponent,\n    selectors: [[\"app-mtn-footer\"]],\n    hostBindings: function FooterComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function FooterComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    inputs: {\n      toHide: \"toHide\",\n      footerDetails: \"footerDetails\"\n    },\n    outputs: {\n      scrollToTop: \"scrollToTop\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"footer\", 4, \"ngIf\"], [\"class\", \"footer-mobile\", 4, \"ngIf\"], [1, \"footer\"], [1, \"footer__to-top\", \"cursor-pointer\", \"pt-4\", \"pb-4\", \"flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [1, \"pi\", \"pi-caret-up\"], [1, \"footer__content\"], [\"id\", \"footer-zoom-container\"], [1, \"footer__content-body\"], [\"class\", \"d-inline-flex flex-column footer__content-list\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-inline-flex\", \"flex-column\", \"footer__content-list\"], [\"alt\", \"No Image\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/white-logo.svg\", 4, \"ngIf\"], [\"id\", \"footer-zoom-container\", 1, \"footer__content-body\", \"justify-content-end\"], [\"class\", \"col-12 col-md-3 flex-row justify-content-center\", 4, \"ngIf\"], [\"class\", \"footer__content-body\", \"id\", \"footer-zoom-container\", 4, \"ngIf\"], [1, \"footer__content-list__heading\"], [4, \"ngIf\", \"ngIfElse\"], [\"showThis\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"footer__content-list__details cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"footer__content-list__details\", 3, \"click\", 4, \"ngIf\"], [1, \"footer__content-list__details\", \"cursor-pointer\", 3, \"click\"], [1, \"footer__content-list__details\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"footer__content-list__details\", 3, \"click\"], [\"alt\", \"No Image\", 3, \"src\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/white-logo.svg\"], [1, \"col-12\", \"col-md-3\", \"flex-row\", \"justify-content-center\"], [1, \"width-icon\"], [1, \"d-flex\"], [\"class\", \"mx-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"mx-3\"], [\"target\", \"_blank\", 1, \"nav-link\", 3, \"href\"], [\"id\", \"footer-zoom-container\", 1, \"footer__content-body\"], [1, \"footer__content-body__divider\", \"footer__content_bottom\"], [1, \"d-flex\", \"flex-row\", \"justify-content-between\", \"mt-3\"], [1, \"footer__content_bottom__payment-header\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"d-inline-flex\", \"flex-row\"], [\"class\", \"footer__content_bottom__payment-logos\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer__content_bottom__copy-rights\"], [1, \"footer__content_bottom__payment-logos\"], [1, \"footer-mobile\"], [1, \"footer-mobile__content\", \"pb-7\"], [1, \"footer-mobile__content__top-section\", \"d-flex\", \"justify-content-between\"], [1, \"footer-mobile__content__top-section__mtn-logo\"], [1, \"footer-mobile__content__top-section__seller_btn\", 3, \"click\"], [1, \"footer-mobile__body-section\"], [\"class\", \"d-inline-flex footer-mobile__body-section__section justify-content-center\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"footer-mobile__body-section__section\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__contact-us\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__social-links\"], [\"class\", \"d-flex justify-content-center footer-mobile__body-section__section__rights-reserved\", 4, \"ngIf\"], [\"class\", \"d-inline-flex footer-mobile__body-section__section justify-content-center footer-bottom\", 4, \"ngIf\"], [\"class\", \"d-inline-flex footer-mobile__body-section__section justify-content-center px-3\", 4, \"ngIf\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\", \"px-3\"], [\"class\", \"footer-mobile__body-section__section__link cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"footer-mobile__body-section__section__link\", 3, \"click\", 4, \"ngIf\"], [1, \"footer-mobile__body-section__section__link\", \"cursor-pointer\", 3, \"click\"], [1, \"footer-mobile__body-section__section__link\", 3, \"click\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\"], [\"class\", \"footer-mobile__body-section__section\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-mobile__body-section__section\"], [\"target\", \"_blank\", 3, \"href\"], [1, \"d-flex\", \"justify-content-center\", \"footer-mobile__body-section__section__rights-reserved\"], [1, \"d-inline-flex\", \"footer-mobile__body-section__section\", \"justify-content-center\", \"footer-bottom\"]],\n    template: function FooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, FooterComponent_footer_0_Template, 16, 8, \"footer\", 0);\n        i0.ɵɵtemplate(1, FooterComponent_footer_1_Template, 25, 16, \"footer\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.desktopView || !ctx.isMobileTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.desktopView && !ctx.toHide && ctx.isMobileTemplate);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i6.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.footer__to-top[_ngcontent-%COMP%] {\\n  min-height: 45px;\\n  background-color: #FFCB05;\\n  color: #191C1F;\\n  font-size: 14px;\\n  font-family: var(--regular-font);\\n  right: 4px;\\n  position: fixed;\\n  z-index: 9999;\\n  width: 60px;\\n  top: 80%;\\n  height: 53px;\\n  border-bottom-left-radius: 13px;\\n  border-top-left-radius: 13px;\\n  text-transform: uppercase;\\n}\\n.footer[_ngcontent-%COMP%]   .top-arrow[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  font-size: 17px;\\n}\\n.footer__content[_ngcontent-%COMP%] {\\n  border-radius: 16px 16px 0px 0px !important;\\n  background-color: var(--footer_content_bgcolor);\\n  color: var(--footer_content_txtcolor) !important;\\n  min-height: 200px;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  padding-top: 26px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n  margin-top: 15px;\\n}\\n.footer__content-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 15px 30px;\\n  width: 100%;\\n  justify-content: space-between;\\n  flex-wrap: wrap;\\n}\\n.footer__content-body__divider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-top: 2px solid #FFF;\\n}\\n@media only screen and (max-width: 767px) {\\n  .footer__content-body[_ngcontent-%COMP%] {\\n    padding: 15px 20px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .footer__content-body[_ngcontent-%COMP%] {\\n    padding: 15px 20px;\\n  }\\n}\\n.footer__content-list[_ngcontent-%COMP%] {\\n  gap: 16px;\\n}\\n.footer__content-list__heading[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 21px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n}\\n.footer__content-list__details[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  cursor: pointer;\\n}\\n.footer__content-list__details[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n@media only screen and (max-width: 767px) {\\n  .footer__content-list[_ngcontent-%COMP%] {\\n    padding: 10px 0px;\\n  }\\n}\\n.footer__content_bottom__payment-header[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  margin: 10px 0px;\\n}\\n.footer__content_bottom__payment-logos[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n}\\n.footer__content_bottom__logo_text[_ngcontent-%COMP%] {\\n  color: #FFCB05;\\n  font-family: var(--regular-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 35px;\\n  margin-right: 10px;\\n}\\n.footer__content_bottom__copy-rights[_ngcontent-%COMP%] {\\n  color: var(--footer_content_txtcolor) !important;\\n  font-family: var(--regular-font);\\n  text-align: right;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 200%;\\n  \\n\\n  text-decoration-line: underline;\\n}\\n.footer[_ngcontent-%COMP%]   .copy-rights[_ngcontent-%COMP%] {\\n  background-color: var(--footer_copy_bgcolor);\\n  color: var(--footer_copy_txtcolor) !important;\\n  font-size: 14px;\\n}\\n.footer[_ngcontent-%COMP%]   .pi-white[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.footer[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-size: 25px;\\n  letter-spacing: 0px;\\n  color: #ffffff;\\n}\\n\\n.footer-mobile[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.footer-mobile__content[_ngcontent-%COMP%] {\\n  border-radius: 16px 16px 0px 0px !important;\\n  background-color: var(--footer_content_bgcolor);\\n  color: var(--footer_content_txtcolor) !important;\\n  min-height: 200px;\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  padding-top: 26px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n}\\n.footer-mobile__content__top-section[_ngcontent-%COMP%] {\\n  padding: 0px 20px;\\n}\\n.footer-mobile__content__top-section__seller_btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 140px;\\n  height: var(--Layout-Padding-Screen, 32px);\\n  padding: 4px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 4px;\\n  border-radius: 4px;\\n  border: 1px solid var(--Main-Colors-Primary, #032D62);\\n  opacity: 0.99;\\n  background: var(--neutral-light-0, #FFF);\\n  box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.2);\\n  color: var(--primary, #204E6E);\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 100%; \\n\\n}\\n.footer-mobile__body-section[_ngcontent-%COMP%] {\\n  padding: 16px 0px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.footer-mobile__body-section__section[_ngcontent-%COMP%] {\\n  gap: 16px;\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.footer-mobile__body-section__section__link[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%; \\n\\n}\\n.footer-mobile__body-section__section__payment-logos[_ngcontent-%COMP%] {\\n  margin-right: 15px;\\n}\\n.footer-mobile__body-section__section__contact-us[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n.footer-mobile__body-section__section__social-links[_ngcontent-%COMP%] {\\n  gap: 19px;\\n}\\n.footer-mobile__body-section__section__social-links[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n.footer-mobile__body-section__section__rights-reserved[_ngcontent-%COMP%] {\\n  color: var(--neutral-light-0, #FFF);\\n  font-family: var(--regular-font);\\n  font-size: 10px;\\n  font-style: normal;\\n  font-weight: 300;\\n  line-height: normal;\\n  max-width: 300px;\\n  margin: 0 auto;\\n  text-align: center;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  a[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .to-top[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .footer-tag[_ngcontent-%COMP%] {\\n    font-weight: 400;\\n    font-size: 12px;\\n    font-family: var(--regular-font) !important;\\n  }\\n  .mobile-social-icon[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .px-7[_ngcontent-%COMP%] {\\n    padding-left: 1rem !important;\\n    padding-right: 1rem !important;\\n  }\\n  .px-6[_ngcontent-%COMP%] {\\n    padding-left: 1rem !important;\\n    padding-right: 1rem !important;\\n  }\\n  .mobile-center-footer[_ngcontent-%COMP%] {\\n    display: flex !important;\\n  }\\n  .mobile-center-footer[_ngcontent-%COMP%]   .width-icon[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    display: flex;\\n  }\\n}\\na[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  cursor: pointer;\\n}\\n\\n.width-icon[_ngcontent-%COMP%] {\\n  width: 220px;\\n}\\n\\n.width-text[_ngcontent-%COMP%] {\\n  width: 213px;\\n  display: block;\\n}\\n\\n.social-text[_ngcontent-%COMP%] {\\n  width: 257px;\\n  display: block;\\n  margin-top: 10px;\\n}\\n\\n.sell-on-market[_ngcontent-%COMP%] {\\n  color: white;\\n  text-decoration: none;\\n}\\n\\n.sell-on-market[_ngcontent-%COMP%]:hover {\\n  color: blue;\\n  text-decoration: underline;\\n}\\n\\n.marketplace-btn[_ngcontent-%COMP%] {\\n  width: 96%;\\n  height: auto;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  text-decoration: underline;\\n  text-align: center;\\n  background: #FFCB05;\\n  padding: 8px;\\n}\\n.marketplace-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  margin-right: 14px;\\n}\\n\\n.marketplace-btn[_ngcontent-%COMP%]:hover {\\n  border: none !important;\\n}\\n\\n.footer-bottom[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "PLATFORM_ID", "environment", "isPlatformBrowser", "GaLocalActionEnum", "i0", "ɵɵelementStart", "ɵɵlistener", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r19", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "navigatePage", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_2_Template_div_click_0_listener", "_r21", "ctx_r20", "navigateToSellerHub", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵproperty", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_4_Template_div_click_0_listener", "_r24", "eachPage_r12", "$implicit", "ctx_r22", "reloadCurrentPage", "id", "pageTitle", "ɵɵtemplate", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_1_Template", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_2_Template", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_ng_container_3_Template", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_div_4_Template", "i_r13", "eachSection_r7", "sectionName", "visibility", "FooterComponent_footer_0_div_9_ng_container_3_ng_container_1_Template", "pages", "FooterComponent_footer_0_div_9_ng_template_4_div_0_Template_div_click_0_listener", "_r32", "ctx_r31", "FooterComponent_footer_0_div_9_ng_template_4_div_1_Template_div_click_0_listener", "_r34", "ctx_r33", "FooterComponent_footer_0_div_9_ng_template_4_div_0_Template", "FooterComponent_footer_0_div_9_ng_template_4_div_1_Template", "FooterComponent_footer_0_div_9_ng_template_4_ng_container_2_Template", "FooterComponent_footer_0_div_9_ng_container_3_Template", "FooterComponent_footer_0_div_9_ng_template_4_Template", "ɵɵtemplateRefExtractor", "length", "_r9", "ɵɵelement", "ctx_r3", "footerDetails", "mainLogo", "ɵɵsanitizeUrl", "link_r37", "iconLink", "ɵɵclassMap", "iconClass", "FooterComponent_footer_0_div_14_div_3_Template", "ctx_r5", "footerLinks", "logo_r39", "FooterComponent_footer_0_div_15_div_10_Template", "ctx_r6", "sideLogo", "payments", "copyRights", "FooterComponent_footer_0_Template_div_click_1_listener", "_r41", "ctx_r40", "backToTop", "FooterComponent_footer_0_div_9_Template", "FooterComponent_footer_0_img_11_Template", "FooterComponent_footer_0_img_12_Template", "FooterComponent_footer_0_div_14_Template", "FooterComponent_footer_0_div_15_Template", "ɵɵtextInterpolate", "ctx_r0", "allData", "scConfig", "ctx_r42", "mainLogoMobile", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_1_Template_div_click_0_listener", "_r60", "ctx_r59", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_2_Template_div_click_0_listener", "_r63", "eachPage_r55", "ctx_r61", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_1_Template", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_div_2_Template", "i_r56", "eachSection_r49", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_ng_container_1_Template", "FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_div_0_Template_div_click_0_listener", "_r69", "ctx_r68", "FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_div_0_Template", "FooterComponent_footer_1_ng_container_15_div_1_ng_container_1_Template", "FooterComponent_footer_1_ng_container_15_div_1_ng_template_2_Template", "_r52", "FooterComponent_footer_1_ng_container_15_div_1_Template", "logo_r73", "FooterComponent_footer_1_div_16_div_1_Template", "ctx_r45", "logo_r74", "url", "icon", "ctx_r47", "copyRightsMobile", "ctx_r48", "bottonLogo", "FooterComponent_footer_1_Template_div_click_1_listener", "_r76", "ctx_r75", "FooterComponent_footer_1_img_9_Template", "FooterComponent_footer_1_img_10_Template", "FooterComponent_footer_1_Template_button_click_11_listener", "ctx_r77", "FooterComponent_footer_1_ng_container_15_Template", "FooterComponent_footer_1_div_16_Template", "FooterComponent_footer_1_div_22_Template", "FooterComponent_footer_1_div_23_Template", "FooterComponent_footer_1_div_24_Template", "ctx_r1", "socials", "FooterComponent", "onResize", "event", "updateZoomClass", "platformId", "zoomLevel", "window", "innerWidth", "screen", "availWidth", "zoomLevelClass", "constructor", "store", "router", "appDataService", "permissionService", "$gaService", "authService", "footerService", "issSellMarketplace", "isStoreCloud", "scrollToTop", "isSellerHub", "isMobileTemplate", "isGoogleAnalytics", "iconName", "hasPermission", "screenWidth", "width", "desktopView", "isMarketplace", "socialLinks", "shopSettingData", "link", "hasOwnProperty", "iconUrl", "ngOnInit", "getAllFooterSectionsWithPages", "subscribe", "res", "data", "result", "findPageWithTitle", "localStorage", "setItem", "searchText", "section", "page", "find", "toLowerCase", "includes", "undefined", "pageId", "title", "CLICK_ON_TERMS_AND_CONDITIONS", "CLICK_ON_PRIVACY_POLICY", "navigateByUrl", "skipLocationChange", "then", "navigate", "queryParams", "state", "ngAfterViewInit", "setTimeout", "subscription", "next", "isShop", "getItem", "scroll", "top", "left", "behavior", "emit", "CLICK_ON_SELL_ON_MARKETPLACE_FOOTER", "open", "merchantURL", "_", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "Router", "AppDataService", "PermissionService", "i3", "GoogleAnalyticsService", "AuthService", "i4", "FooterService", "_2", "selectors", "hostBindings", "FooterComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveWindow", "FooterComponent_footer_0_Template", "FooterComponent_footer_1_Template", "toHide"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\footer\\footer.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\footer\\footer.component.html"], "sourcesContent": ["import {Component, EventEmitter, HostListener, Inject, Input, OnInit, Output, PLATFORM_ID} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport {AppDataService, AuthService, PermissionService, StoreService} from '@core/services';\r\nimport { environment } from '@environments/environment';\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\nimport { FooterService } from './footer.service';\r\n\r\n@Component({\r\n  selector: 'app-mtn-footer',\r\n  templateUrl: './footer.component.html',\r\n  styleUrls: ['./footer.component.scss'],\r\n})\r\nexport class FooterComponent implements OnInit {\r\n  @Input() toHide: any;\r\n  @Input() footerDetails: any;\r\n  issSellMarketplace: boolean = environment.isStoreCloud\r\n  @Output() scrollToTop: EventEmitter<any> = new EventEmitter<any>();\r\n  isShop: any;\r\n  scConfig: boolean = false\r\n  screenWidth?: any;\r\n  desktopView: boolean;\r\n  isSellerHub: any = '';\r\n  protected readonly environment = environment;\r\n  zoomLevelClass: string = 'default-zoom';\r\n  isMobileTemplate:boolean=false;\r\n  isGoogleAnalytics:boolean=false;\r\n  allData: any;\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: Event) {\r\n    this.updateZoomClass();\r\n  }\r\n\r\n  footerLinks: any = [\r\n    {\r\n      iconClass: 'fa-brands fa-square-facebook',\r\n      iconLink: 'https://www.facebook.com/YallaSuperMallEG',\r\n      iconName: 'facebook'\r\n    },{\r\n      iconClass: 'fa-brands fa-square-twitter',\r\n      iconLink: 'https://twitter.com/YallaSuperappEG',\r\n      iconName: 'twitter'\r\n    },{\r\n      iconClass: 'fa-brands fa-youtube',\r\n      iconLink: 'https://www.youtube.com/@yallasuperapp3030',\r\n      iconName: 'youtube'\r\n    },{\r\n      iconClass: 'fa-brands fa-instagram',\r\n      iconLink: 'https://www.instagram.com/yallasupermalleg/',\r\n      iconName: 'instagram'\r\n    },{\r\n      iconClass: 'fa-brands fa-tiktok',\r\n      iconLink: 'https://www.tiktok.com/@yallasupermall',\r\n      iconName: 'tiktok'\r\n    },\r\n  ]\r\n\r\n  private updateZoomClass() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const zoomLevel = (window.innerWidth / window.screen.availWidth) * 100;\r\n      if (zoomLevel <= 91) {\r\n        this.zoomLevelClass = 'zoom-110';\r\n      }\r\n      else if (zoomLevel <= 112) {\r\n        this.zoomLevelClass = 'zoom-90';\r\n      } else if (zoomLevel <= 125) {\r\n        this.zoomLevelClass = 'zoom-80';\r\n      } else if (zoomLevel <= 134) {\r\n        this.zoomLevelClass = 'zoom-75';\r\n      } else if (zoomLevel <= 150) {\r\n        this.zoomLevelClass = 'zoom-67';\r\n      } else if (zoomLevel <= 200) {\r\n        this.zoomLevelClass = 'zoom-50';\r\n      } else if (zoomLevel <= 300) {\r\n        this.zoomLevelClass = 'zoom-33';\r\n      }\r\n      else if (zoomLevel <= 400) {\r\n        this.zoomLevelClass = 'zoom-25';\r\n      } else {\r\n        this.zoomLevelClass = 'default-zoom';\r\n      }\r\n    }\r\n\r\n  }\r\n  constructor(private store: StoreService, public router: Router, @Inject(PLATFORM_ID) private platformId: any,\r\n              private appDataService: AppDataService,\r\n              private permissionService: PermissionService,\r\n              private $gaService: GoogleAnalyticsService,\r\n              private authService: AuthService,\r\n              private footerService: FooterService) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n    this.scConfig = environment.isStoreCloud;\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.screen.width;\r\n      if (this.screenWidth > 768) {\r\n        this.desktopView = true;\r\n      } else {\r\n        this.desktopView = false;\r\n      }\r\n    }\r\n\r\n    if(!this.authService.isMarketplace()){\r\n      const socialLinks = this.appDataService.shopSettingData\r\n      if(socialLinks) {\r\n        for (const link of this.footerLinks) {\r\n          if (socialLinks.hasOwnProperty(link.iconName) && socialLinks[link.iconName] !== null) {\r\n            link.iconUrl = socialLinks[link.iconName];\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.footerService.getAllFooterSectionsWithPages().subscribe((res: any) => {\r\n      this.allData = res.data;\r\n\r\n      let result = this.findPageWithTitle('terms and conditions');\r\n      if (!result)\r\n        result = this.findPageWithTitle('termes et conditions');\r\n\r\n      if (result) {\r\n        localStorage.setItem('TermsAndConditionsId', result?.id);\r\n      }\r\n    });\r\n  }\r\n\r\n  findPageWithTitle(searchText: string): any {\r\n    for (const section of this.allData) {\r\n      const page = section.pages.find((page: any) =>\r\n        page.pageTitle?.toLowerCase().includes(searchText.toLowerCase())\r\n      );\r\n      if (page) {\r\n        return page;\r\n      }\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  reloadCurrentPage(pageId: number, title: string) {\r\n    if(this.isGoogleAnalytics){\r\n    if (title.includes('Terms and Conditions')) {\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_TERMS_AND_CONDITIONS, '', 'TERMS_AND_CONDITION', 1, true);\r\n    } else if (title.includes('Privacy policy')) {\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_PRIVACY_POLICY, '', 'PRIVACY_POLICY', 1, true);\r\n    }\r\n  }\r\n\r\n    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() =>\r\n      this.router.navigate(['/about-us/'], {\r\n        queryParams: { pageId: pageId, title: title }, state: { id: pageId }\r\n      })\r\n    );\r\n  }\r\n\r\n  navigatePage(url: string) {\r\n    this.router\r\n      .navigateByUrl('/', { skipLocationChange: true })\r\n      .then(() => this.router.navigate(['/' + url + '/']));\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n      this.store.subscription('isShop').subscribe({\r\n        next: (res: any) => (this.isShop = res),\r\n      });\r\n    }, 10);\r\n    this.store.subscription('allowedFeature').subscribe({\r\n      next: (res: any) => {\r\n        if (res.length) {\r\n          this.isSellerHub = localStorage.getItem('isSellerHub')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  backToTop() {\r\n    window.scroll(0,0);\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.scroll({\r\n        top: 0,\r\n        left: 0,\r\n        behavior: 'auto',\r\n      });\r\n      this.scrollToTop.emit();\r\n    }\r\n  }\r\n  navigateToSellerHub() {\r\n    if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_SELL_ON_MARKETPLACE_FOOTER, '', '', 1, true);\r\n    }\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      window.open(environment.merchantURL + '?tenantId=' + `${localStorage.getItem('tenantId')}`, '_blank');\r\n    }\r\n  }\r\n}\r\n", "<footer class=\"footer\" *ngIf=\"desktopView || !isMobileTemplate\">\r\n  <div (click)=\"backToTop()\"\r\n    class=\"footer__to-top cursor-pointer pt-4 pb-4 flex flex-column justify-content-center align-items-center\">\r\n    <em class=\"pi pi-caret-up\"></em>\r\n\r\n    <div>{{ \"footer.backToTop\" | translate }}</div>\r\n  </div>\r\n  <div class=\"footer__content\">\r\n    <div id=\"footer-zoom-container\">\r\n      <div class=\"footer__content-body\">\r\n        <div *ngFor=\"let eachSection of allData\" class=\"d-inline-flex flex-column footer__content-list\">\r\n          <div class=\"footer__content-list__heading\"> {{ eachSection.sectionName }} </div>\r\n          <ng-container *ngIf=\"eachSection.pages.length > 0; else showThis\">\r\n            <ng-container *ngFor=\"let eachPage of eachSection.pages; let i = index\">\r\n              \r\n              <div *ngIf=\" i === 0 && eachSection.sectionName === 'Help'\" (click)=\"navigatePage('contact-us')\" class=\"footer__content-list__details cursor-pointer\">\r\n                {{ \"footer.contactUs\" | translate }}\r\n              </div>\r\n              \r\n              <div *ngIf=\" i === 0 && eachSection.sectionName === 'Seller hub'\" (click)=\"navigateToSellerHub()\" class=\"footer__content-list__details cursor-pointer\">\r\n                {{ \"footer.sellOnMarketplace\" | translate }}\r\n              </div>\r\n              \r\n              <ng-container *ngIf=\" i === 0 && eachSection.sectionName === 'Account'\">\r\n                <div [routerLink]=\"'/orders'\" class=\"footer__content-list__details cursor-pointer\">\r\n                  {{ \"footer.myOrders\" | translate }}\r\n                </div>\r\n                <div [routerLink]=\"'/account/address'\" class=\"footer__content-list__details cursor-pointer\">\r\n                  {{ \"footer.myAddress\" | translate }}\r\n                </div>\r\n                <div [routerLink]=\"'/account/details'\" class=\"footer__content-list__details cursor-pointer\">\r\n                  {{ \"footer.myDetails\" | translate }}\r\n                </div>\r\n              </ng-container>\r\n              \r\n              <div *ngIf=\"eachPage.visibility\" (click)=\"reloadCurrentPage(eachPage.id, eachPage.pageTitle)\" class=\"footer__content-list__details\">\r\n                {{ eachPage.pageTitle }}\r\n              </div>\r\n            </ng-container>\r\n          </ng-container>\r\n          \r\n          <ng-template #showThis>\r\n            <div *ngIf=\"eachSection.sectionName === 'Help'\" (click)=\"navigatePage('contact-us')\" class=\"footer__content-list__details cursor-pointer\">\r\n              {{ \"footer.contactUs\" | translate }}\r\n            </div>\r\n            \r\n            <div *ngIf=\"eachSection.sectionName === 'Seller hub'\" (click)=\"navigateToSellerHub()\" class=\"footer__content-list__details cursor-pointer\">\r\n              {{ \"footer.sellOnMarketplace\" | translate }}\r\n            </div>\r\n            \r\n            <ng-container *ngIf=\"eachSection.sectionName === 'Account'\">\r\n              <div [routerLink]=\"'/orders'\" class=\"footer__content-list__details cursor-pointer\">\r\n                {{ \"footer.myOrders\" | translate }}\r\n              </div>\r\n              <div [routerLink]=\"'/account/address'\" class=\"footer__content-list__details cursor-pointer\">\r\n                {{ \"footer.myAddress\" | translate }}\r\n              </div>\r\n              <div [routerLink]=\"'/account/details'\" class=\"footer__content-list__details cursor-pointer\">\r\n                {{ \"footer.myDetails\" | translate }}\r\n              </div>\r\n            </ng-container>\r\n          </ng-template>\r\n        </div>\r\n        \r\n        <div class=\"d-inline-flex flex-column footer__content-list\">\r\n\r\n          <img *ngIf=\"!scConfig\" alt=\"No Image\" [src]=\"footerDetails.mainLogo\">\r\n          <img *ngIf=\"scConfig\" alt=\"No Image\" src=\"assets/icons/white-logo.svg\">\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"footer__content-body justify-content-end\" id=\"footer-zoom-container\">\r\n      <div\r\n        *ngIf=\"scConfig\"\r\n        class=\"col-12 col-md-3 flex-row justify-content-center\"\r\n      >\r\n        <div class=\"width-icon\">\r\n          <div class=\"d-flex\">\r\n            <div class=\"mx-3\" *ngFor=\"let link of footerLinks\">\r\n              <a\r\n                class=\"nav-link\"\r\n                [href]=\"link.iconLink\"\r\n                target=\"_blank\"\r\n              ><em class=\"{{link.iconClass}}\"></em\r\n              ></a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"footer__content-body\" id=\"footer-zoom-container\" *ngIf=\"!scConfig\">\r\n      <div class=\"footer__content-body__divider footer__content_bottom\">\r\n        <div class=\"d-flex flex-row justify-content-between mt-3\">\r\n          <div class=\"footer__content_bottom__payment-header\">\r\n            {{ \"footer.weAccept\" | translate }}\r\n          </div>\r\n          <div>\r\n            <img alt=\"No Image\" [src]=\"footerDetails.sideLogo\">\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"d-flex justify-content-between\">\r\n          <div class=\"d-inline-flex flex-row\">\r\n            <div *ngFor=\"let logo of footerDetails.payments\" class=\"footer__content_bottom__payment-logos\">\r\n              <img alt=\"No Image\" [src]=\"logo\">\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"footer__content_bottom__copy-rights\">\r\n            {{footerDetails.copyRights | translate}}\r\n          </div>\r\n\r\n        </div>\r\n\r\n      </div>\r\n\r\n\r\n    </div>\r\n  </div>\r\n</footer>\r\n<footer class=\"footer-mobile\" *ngIf=\"!desktopView && !toHide && isMobileTemplate\">\r\n  <div (click)=\"backToTop()\"\r\n       class=\"footer__to-top cursor-pointer pt-4 pb-4 flex flex-column justify-content-center align-items-center\">\r\n    <em class=\"pi pi-caret-up\"></em>\r\n\r\n    <div>{{ \"footer.backToTop\" | translate }}</div>\r\n  </div>\r\n  <div class=\"footer-mobile__content pb-7\">\r\n    <div class=\"footer-mobile__content__top-section d-flex justify-content-between\" >\r\n      <div class=\"footer-mobile__content__top-section__mtn-logo\">\r\n        <img *ngIf=\"!scConfig\" alt=\"No Image\" [src]=\"footerDetails.mainLogoMobile\">\r\n        <img *ngIf=\"scConfig\" alt=\"No Image\" src=\"assets/icons/white-logo.svg\">\r\n      </div>\r\n      <button class=\"footer-mobile__content__top-section__seller_btn\" (click)=\"navigateToSellerHub()\">\r\n        {{ \"footer.becomeSeller\" | translate }}\r\n\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"footer-mobile__body-section\">\r\n      <ng-container *ngFor=\"let eachSection of allData\">\r\n        <div *ngIf=\"eachSection.sectionName !== 'Seller hub' && eachSection.sectionName !== 'Account'\" class=\"d-inline-flex footer-mobile__body-section__section justify-content-center px-3\">\r\n          <ng-container *ngIf=\"eachSection.pages.length > 0; else showThis\">\r\n            <ng-container *ngFor=\"let eachPage of eachSection.pages; let i = index\">\r\n              <div *ngIf=\" i === 0 && eachSection.sectionName === 'Help'\" (click)=\"navigatePage('contact-us')\" class=\"footer-mobile__body-section__section__link cursor-pointer\">\r\n                {{ \"footer.contactUs\" | translate }}\r\n              </div>\r\n              \r\n              <div *ngIf=\"eachPage.visibility\" (click)=\"reloadCurrentPage(eachPage.id, eachPage.pageTitle)\" class=\"footer-mobile__body-section__section__link\">\r\n                {{ eachPage.pageTitle }}\r\n              </div>\r\n            </ng-container>\r\n          </ng-container>\r\n          \r\n          <ng-template #showThis>\r\n            <div *ngIf=\"eachSection.sectionName === 'Help'\" (click)=\"navigatePage('contact-us')\" class=\"footer-mobile__body-section__section__link cursor-pointer\">\r\n              {{ \"footer.contactUs\" | translate }}\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <div *ngIf=\"!scConfig\" class=\"d-inline-flex footer-mobile__body-section__section justify-content-center\">\r\n        <div *ngFor=\"let logo of footerDetails.payments\" class=\"footer-mobile__body-section__section\">\r\n          <img alt=\"No Image\" [src]=\"logo\">\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"d-flex flex-column footer-mobile__body-section__section\">\r\n        <div class=\"d-flex justify-content-center footer-mobile__body-section__section__contact-us\">\r\n          {{ \"footer.reachOut\" | translate }}\r\n          \r\n        </div>\r\n        <div class=\"d-flex justify-content-center footer-mobile__body-section__section__social-links\">\r\n          <div *ngFor=\"let logo of footerDetails.socials\">\r\n            <a [href]=\"logo.url\" target=\"_blank\">\r\n              <img alt=\"No Image\" [src]=\"logo.icon\">\r\n            </a>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"!scConfig\" class=\"d-flex justify-content-center footer-mobile__body-section__section__rights-reserved\">\r\n          {{footerDetails.copyRightsMobile | translate}}\r\n        </div>\r\n      </div>\r\n\r\n      <div *ngIf=\"!scConfig\" class=\"d-inline-flex footer-mobile__body-section__section justify-content-center footer-bottom\">\r\n        <img alt=\"No Image\" [src]=\"footerDetails.bottonLogo\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</footer>"], "mappings": "AAAA,SAAmBA,YAAY,EAA+CC,WAAW,QAAO,eAAe;AAG/G,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAAQC,iBAAiB,QAAO,kCAAkC;;;;;;;;;;;ICUpDC,EAAA,CAAAC,cAAA,cAAsJ;IAA1FD,EAAA,CAAAE,UAAA,mBAAAC,iGAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IAC9FT,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;;;;;;IAEAd,EAAA,CAAAC,cAAA,cAAuJ;IAArFD,EAAA,CAAAE,UAAA,mBAAAa,iGAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAC/FlB,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,wCACF;;;;;IAEAd,EAAA,CAAAmB,uBAAA,GAAwE;IACtEnB,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACRX,EAAA,CAAAoB,qBAAA,EAAe;;;IATRpB,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqB,UAAA,yBAAwB;IAC3BrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,+BACF;IACKd,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAqB,UAAA,kCAAiC;IACpCrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;IACKd,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAqB,UAAA,kCAAiC;IACpCrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,iCACF;;;;;;IAGFd,EAAA,CAAAC,cAAA,cAAoI;IAAnGD,EAAA,CAAAE,UAAA,mBAAAoB,iGAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAC,YAAA,GAAAxB,EAAA,CAAAO,aAAA,GAAAkB,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkB,OAAA,CAAAC,iBAAA,CAAAH,YAAA,CAAAI,EAAA,EAAAJ,YAAA,CAAAK,SAAA,CAAkD;IAAA,EAAC;IAC3F7B,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAW,YAAA,CAAAK,SAAA,MACF;;;;;IAxBF7B,EAAA,CAAAmB,uBAAA,GAAwE;IAEtEnB,EAAA,CAAA8B,UAAA,IAAAC,2EAAA,kBAEM;IAEN/B,EAAA,CAAA8B,UAAA,IAAAE,2EAAA,kBAEM;IAENhC,EAAA,CAAA8B,UAAA,IAAAG,oFAAA,6BAUe;IAEfjC,EAAA,CAAA8B,UAAA,IAAAI,2EAAA,kBAEM;IACRlC,EAAA,CAAAoB,qBAAA,EAAe;;;;;;IAvBPpB,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAqB,UAAA,SAAAc,KAAA,UAAAC,cAAA,CAAAC,WAAA,YAAqD;IAIrDrC,EAAA,CAAAY,SAAA,GAA2D;IAA3DZ,EAAA,CAAAqB,UAAA,SAAAc,KAAA,UAAAC,cAAA,CAAAC,WAAA,kBAA2D;IAIlDrC,EAAA,CAAAY,SAAA,GAAwD;IAAxDZ,EAAA,CAAAqB,UAAA,SAAAc,KAAA,UAAAC,cAAA,CAAAC,WAAA,eAAwD;IAYjErC,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqB,UAAA,SAAAG,YAAA,CAAAc,UAAA,CAAyB;;;;;IAvBnCtC,EAAA,CAAAmB,uBAAA,GAAkE;IAChEnB,EAAA,CAAA8B,UAAA,IAAAS,qEAAA,2BAyBe;IACjBvC,EAAA,CAAAoB,qBAAA,EAAe;;;;IA1BsBpB,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAqB,UAAA,YAAAe,cAAA,CAAAI,KAAA,CAAsB;;;;;;IA6BzDxC,EAAA,CAAAC,cAAA,cAA0I;IAA1FD,EAAA,CAAAE,UAAA,mBAAAuC,iFAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmC,OAAA,CAAAlC,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IAClFT,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;;;;;;IAEAd,EAAA,CAAAC,cAAA,cAA2I;IAArFD,EAAA,CAAAE,UAAA,mBAAA0C,iFAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsC,OAAA,CAAA5B,mBAAA,EAAqB;IAAA,EAAC;IACnFlB,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,wCACF;;;;;IAEAd,EAAA,CAAAmB,uBAAA,GAA4D;IAC1DnB,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACRX,EAAA,CAAAoB,qBAAA,EAAe;;;IATRpB,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqB,UAAA,yBAAwB;IAC3BrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,+BACF;IACKd,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAqB,UAAA,kCAAiC;IACpCrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;IACKd,EAAA,CAAAY,SAAA,GAAiC;IAAjCZ,EAAA,CAAAqB,UAAA,kCAAiC;IACpCrB,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,iCACF;;;;;IAjBFd,EAAA,CAAA8B,UAAA,IAAAiB,2DAAA,kBAEM;IAEN/C,EAAA,CAAA8B,UAAA,IAAAkB,2DAAA,kBAEM;IAENhD,EAAA,CAAA8B,UAAA,IAAAmB,oEAAA,6BAUe;;;;IAlBTjD,EAAA,CAAAqB,UAAA,SAAAe,cAAA,CAAAC,WAAA,YAAwC;IAIxCrC,EAAA,CAAAY,SAAA,GAA8C;IAA9CZ,EAAA,CAAAqB,UAAA,SAAAe,cAAA,CAAAC,WAAA,kBAA8C;IAIrCrC,EAAA,CAAAY,SAAA,GAA2C;IAA3CZ,EAAA,CAAAqB,UAAA,SAAAe,cAAA,CAAAC,WAAA,eAA2C;;;;;IAxC9DrC,EAAA,CAAAC,cAAA,aAAgG;IAClDD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChFX,EAAA,CAAA8B,UAAA,IAAAoB,sDAAA,2BA2Be;IAEflD,EAAA,CAAA8B,UAAA,IAAAqB,qDAAA,iCAAAnD,EAAA,CAAAoD,sBAAA,CAoBc;IAChBpD,EAAA,CAAAW,YAAA,EAAM;;;;;IAnDwCX,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAa,kBAAA,MAAAuB,cAAA,CAAAC,WAAA,MAA8B;IAC3DrC,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAqB,UAAA,SAAAe,cAAA,CAAAI,KAAA,CAAAa,MAAA,KAAoC,aAAAC,GAAA;;;;;IAsDnDtD,EAAA,CAAAuD,SAAA,cAAqE;;;;IAA/BvD,EAAA,CAAAqB,UAAA,QAAAmC,MAAA,CAAAC,aAAA,CAAAC,QAAA,EAAA1D,EAAA,CAAA2D,aAAA,CAA8B;;;;;IACpE3D,EAAA,CAAAuD,SAAA,cAAuE;;;;;IAarEvD,EAAA,CAAAC,cAAA,cAAmD;IAKhDD,EAAA,CAAAuD,SAAA,SACA;IAAAvD,EAAA,CAAAW,YAAA,EAAI;;;;IAHHX,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAqB,UAAA,SAAAuC,QAAA,CAAAC,QAAA,EAAA7D,EAAA,CAAA2D,aAAA,CAAsB;IAEnB3D,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAA8D,UAAA,CAAAF,QAAA,CAAAG,SAAA,CAA0B;;;;;IAXvC/D,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAA8B,UAAA,IAAAkC,8CAAA,kBAOM;IACRhE,EAAA,CAAAW,YAAA,EAAM;;;;IAR+BX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAqB,UAAA,YAAA4C,MAAA,CAAAC,WAAA,CAAc;;;;;IA2BjDlE,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAuD,SAAA,cAAiC;IACnCvD,EAAA,CAAAW,YAAA,EAAM;;;;IADgBX,EAAA,CAAAY,SAAA,GAAY;IAAZZ,EAAA,CAAAqB,UAAA,QAAA8C,QAAA,EAAAnE,EAAA,CAAA2D,aAAA,CAAY;;;;;IAd1C3D,EAAA,CAAAC,cAAA,cAA+E;IAIvED,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAuD,SAAA,cAAmD;IACrDvD,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,cAA4C;IAExCD,EAAA,CAAA8B,UAAA,KAAAsC,+CAAA,kBAEM;IACRpE,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAiD;IAC/CD,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IAhBJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,+BACF;IAEsBd,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAqB,UAAA,QAAAgD,MAAA,CAAAZ,aAAA,CAAAa,QAAA,EAAAtE,EAAA,CAAA2D,aAAA,CAA8B;IAM5B3D,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqB,UAAA,YAAAgD,MAAA,CAAAZ,aAAA,CAAAc,QAAA,CAAyB;IAM/CvE,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,QAAAuD,MAAA,CAAAZ,aAAA,CAAAe,UAAA,OACF;;;;;;IAlHVxE,EAAA,CAAAC,cAAA,gBAAgE;IACzDD,EAAA,CAAAE,UAAA,mBAAAuE,uDAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmE,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAExB5E,EAAA,CAAAuD,SAAA,YAAgC;IAEhCvD,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAU,MAAA,GAAoC;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAEjDX,EAAA,CAAAC,cAAA,aAA6B;IAGvBD,EAAA,CAAA8B,UAAA,IAAA+C,uCAAA,iBAoDM;IAEN7E,EAAA,CAAAC,cAAA,cAA4D;IAE1DD,EAAA,CAAA8B,UAAA,KAAAgD,wCAAA,kBAAqE;IACrE9E,EAAA,CAAA8B,UAAA,KAAAiD,wCAAA,kBAAuE;IACzE/E,EAAA,CAAAW,YAAA,EAAM;IAKVX,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAA8B,UAAA,KAAAkD,wCAAA,kBAiBM;IACRhF,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAA8B,UAAA,KAAAmD,wCAAA,mBA2BM;IACRjF,EAAA,CAAAW,YAAA,EAAM;;;;IArHCX,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAAc,WAAA,2BAAoC;IAKRd,EAAA,CAAAY,SAAA,GAAU;IAAVZ,EAAA,CAAAqB,UAAA,YAAA8D,MAAA,CAAAC,OAAA,CAAU;IAwD/BpF,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA8D,MAAA,CAAAE,QAAA,CAAe;IACfrF,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAqB,UAAA,SAAA8D,MAAA,CAAAE,QAAA,CAAc;IAQrBrF,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAqB,UAAA,SAAA8D,MAAA,CAAAE,QAAA,CAAc;IAmB2CrF,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA8D,MAAA,CAAAE,QAAA,CAAe;;;;;IAwCzErF,EAAA,CAAAuD,SAAA,cAA2E;;;;IAArCvD,EAAA,CAAAqB,UAAA,QAAAiE,OAAA,CAAA7B,aAAA,CAAA8B,cAAA,EAAAvF,EAAA,CAAA2D,aAAA,CAAoC;;;;;IAC1E3D,EAAA,CAAAuD,SAAA,cAAuE;;;;;;IAajEvD,EAAA,CAAAC,cAAA,cAAmK;IAAvGD,EAAA,CAAAE,UAAA,mBAAAsF,iHAAA;MAAAxF,EAAA,CAAAI,aAAA,CAAAqF,IAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkF,OAAA,CAAAjF,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IAC9FT,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;;;;;;IAEAd,EAAA,CAAAC,cAAA,cAAiJ;IAAhHD,EAAA,CAAAE,UAAA,mBAAAyF,iHAAA;MAAA3F,EAAA,CAAAI,aAAA,CAAAwF,IAAA;MAAA,MAAAC,YAAA,GAAA7F,EAAA,CAAAO,aAAA,GAAAkB,SAAA;MAAA,MAAAqE,OAAA,GAAA9F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsF,OAAA,CAAAnE,iBAAA,CAAAkE,YAAA,CAAAjE,EAAA,EAAAiE,YAAA,CAAAhE,SAAA,CAAkD;IAAA,EAAC;IAC3F7B,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAgF,YAAA,CAAAhE,SAAA,MACF;;;;;IAPF7B,EAAA,CAAAmB,uBAAA,GAAwE;IACtEnB,EAAA,CAAA8B,UAAA,IAAAiE,2FAAA,kBAEM;IAEN/F,EAAA,CAAA8B,UAAA,IAAAkE,2FAAA,kBAEM;IACRhG,EAAA,CAAAoB,qBAAA,EAAe;;;;;;IAPPpB,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAqB,UAAA,SAAA4E,KAAA,UAAAC,eAAA,CAAA7D,WAAA,YAAqD;IAIrDrC,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqB,UAAA,SAAAwE,YAAA,CAAAvD,UAAA,CAAyB;;;;;IANnCtC,EAAA,CAAAmB,uBAAA,GAAkE;IAChEnB,EAAA,CAAA8B,UAAA,IAAAqE,qFAAA,2BAQe;IACjBnG,EAAA,CAAAoB,qBAAA,EAAe;;;;IATsBpB,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAqB,UAAA,YAAA6E,eAAA,CAAA1D,KAAA,CAAsB;;;;;;IAYzDxC,EAAA,CAAAC,cAAA,cAAuJ;IAAvGD,EAAA,CAAAE,UAAA,mBAAAkG,iGAAA;MAAApG,EAAA,CAAAI,aAAA,CAAAiG,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8F,OAAA,CAAA7F,YAAA,CAAa,YAAY,CAAC;IAAA,EAAC;IAClFT,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,gCACF;;;;;IAFAd,EAAA,CAAA8B,UAAA,IAAAyE,2EAAA,kBAEM;;;;IAFAvG,EAAA,CAAAqB,UAAA,SAAA6E,eAAA,CAAA7D,WAAA,YAAwC;;;;;IAdlDrC,EAAA,CAAAC,cAAA,cAAsL;IACpLD,EAAA,CAAA8B,UAAA,IAAA0E,sEAAA,2BAUe;IAEfxG,EAAA,CAAA8B,UAAA,IAAA2E,qEAAA,iCAAAzG,EAAA,CAAAoD,sBAAA,CAIc;IAChBpD,EAAA,CAAAW,YAAA,EAAM;;;;;IAjBWX,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAqB,UAAA,SAAA6E,eAAA,CAAA1D,KAAA,CAAAa,MAAA,KAAoC,aAAAqD,IAAA;;;;;IAFvD1G,EAAA,CAAAmB,uBAAA,GAAkD;IAChDnB,EAAA,CAAA8B,UAAA,IAAA6E,uDAAA,kBAkBM;IACR3G,EAAA,CAAAoB,qBAAA,EAAe;;;;IAnBPpB,EAAA,CAAAY,SAAA,GAAuF;IAAvFZ,EAAA,CAAAqB,UAAA,SAAA6E,eAAA,CAAA7D,WAAA,qBAAA6D,eAAA,CAAA7D,WAAA,eAAuF;;;;;IAsB7FrC,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAuD,SAAA,cAAiC;IACnCvD,EAAA,CAAAW,YAAA,EAAM;;;;IADgBX,EAAA,CAAAY,SAAA,GAAY;IAAZZ,EAAA,CAAAqB,UAAA,QAAAuF,QAAA,EAAA5G,EAAA,CAAA2D,aAAA,CAAY;;;;;IAFpC3D,EAAA,CAAAC,cAAA,cAAyG;IACvGD,EAAA,CAAA8B,UAAA,IAAA+E,8CAAA,kBAEM;IACR7G,EAAA,CAAAW,YAAA,EAAM;;;;IAHkBX,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAqB,UAAA,YAAAyF,OAAA,CAAArD,aAAA,CAAAc,QAAA,CAAyB;;;;;IAW7CvE,EAAA,CAAAC,cAAA,UAAgD;IAE5CD,EAAA,CAAAuD,SAAA,cAAsC;IACxCvD,EAAA,CAAAW,YAAA,EAAI;;;;IAFDX,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAqB,UAAA,SAAA0F,QAAA,CAAAC,GAAA,EAAAhH,EAAA,CAAA2D,aAAA,CAAiB;IACE3D,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAqB,UAAA,QAAA0F,QAAA,CAAAE,IAAA,EAAAjH,EAAA,CAAA2D,aAAA,CAAiB;;;;;IAI3C3D,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,OAAAoG,OAAA,CAAAzD,aAAA,CAAA0D,gBAAA,OACF;;;;;IAGFnH,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAuD,SAAA,cAAqD;IACvDvD,EAAA,CAAAW,YAAA,EAAM;;;;IADgBX,EAAA,CAAAY,SAAA,GAAgC;IAAhCZ,EAAA,CAAAqB,UAAA,QAAA+F,OAAA,CAAA3D,aAAA,CAAA4D,UAAA,EAAArH,EAAA,CAAA2D,aAAA,CAAgC;;;;;;IAlE5D3D,EAAA,CAAAC,cAAA,iBAAkF;IAC3ED,EAAA,CAAAE,UAAA,mBAAAoH,uDAAA;MAAAtH,EAAA,CAAAI,aAAA,CAAAmH,IAAA;MAAA,MAAAC,OAAA,GAAAxH,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgH,OAAA,CAAA5C,SAAA,EAAW;IAAA,EAAC;IAExB5E,EAAA,CAAAuD,SAAA,YAAgC;IAEhCvD,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAU,MAAA,GAAoC;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAEjDX,EAAA,CAAAC,cAAA,cAAyC;IAGnCD,EAAA,CAAA8B,UAAA,IAAA2F,uCAAA,kBAA2E;IAC3EzH,EAAA,CAAA8B,UAAA,KAAA4F,wCAAA,kBAAuE;IACzE1H,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,kBAAgG;IAAhCD,EAAA,CAAAE,UAAA,mBAAAyH,2DAAA;MAAA3H,EAAA,CAAAI,aAAA,CAAAmH,IAAA;MAAA,MAAAK,OAAA,GAAA5H,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoH,OAAA,CAAA1G,mBAAA,EAAqB;IAAA,EAAC;IAC7FlB,EAAA,CAAAU,MAAA,IAEF;;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGXX,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAA8B,UAAA,KAAA+F,iDAAA,2BAoBe;IAEf7H,EAAA,CAAA8B,UAAA,KAAAgG,wCAAA,kBAIM;IAEN9H,EAAA,CAAAC,cAAA,eAAqE;IAEjED,EAAA,CAAAU,MAAA,IAEF;;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA8F;IAC5FD,EAAA,CAAA8B,UAAA,KAAAiG,wCAAA,kBAIM;IACR/H,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA8B,UAAA,KAAAkG,wCAAA,kBAEM;IACRhI,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAA8B,UAAA,KAAAmG,wCAAA,kBAEM;IACRjI,EAAA,CAAAW,YAAA,EAAM;;;;IA/DDX,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAAc,WAAA,4BAAoC;IAK/Bd,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA6G,MAAA,CAAA7C,QAAA,CAAe;IACfrF,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAqB,UAAA,SAAA6G,MAAA,CAAA7C,QAAA,CAAc;IAGpBrF,EAAA,CAAAY,SAAA,GAEF;IAFEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,qCAEF;IAIsCd,EAAA,CAAAY,SAAA,GAAU;IAAVZ,EAAA,CAAAqB,UAAA,YAAA6G,MAAA,CAAA9C,OAAA,CAAU;IAsB1CpF,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA6G,MAAA,CAAA7C,QAAA,CAAe;IAQjBrF,EAAA,CAAAY,SAAA,GAEF;IAFEZ,EAAA,CAAAa,kBAAA,MAAAb,EAAA,CAAAc,WAAA,iCAEF;IAEwBd,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqB,UAAA,YAAA6G,MAAA,CAAAzE,aAAA,CAAA0E,OAAA,CAAwB;IAM1CnI,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA6G,MAAA,CAAA7C,QAAA,CAAe;IAKjBrF,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAqB,UAAA,UAAA6G,MAAA,CAAA7C,QAAA,CAAe;;;AD/K3B,OAAM,MAAO+C,eAAe;EAgB1BC,QAAQA,CAACC,KAAY;IACnB,IAAI,CAACC,eAAe,EAAE;EACxB;EA0BQA,eAAeA,CAAA;IACrB,IAAIzI,iBAAiB,CAAC,IAAI,CAAC0I,UAAU,CAAC,EAAE;MACtC,MAAMC,SAAS,GAAIC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,MAAM,CAACC,UAAU,GAAI,GAAG;MACtE,IAAIJ,SAAS,IAAI,EAAE,EAAE;QACnB,IAAI,CAACK,cAAc,GAAG,UAAU;OACjC,MACI,IAAIL,SAAS,IAAI,GAAG,EAAE;QACzB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM,IAAIL,SAAS,IAAI,GAAG,EAAE;QAC3B,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MACI,IAAIL,SAAS,IAAI,GAAG,EAAE;QACzB,IAAI,CAACK,cAAc,GAAG,SAAS;OAChC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,cAAc;;;EAI1C;EACAC,YAAoBC,KAAmB,EAASC,MAAc,EAA+BT,UAAe,EACxFU,cAA8B,EAC9BC,iBAAoC,EACpCC,UAAkC,EAClCC,WAAwB,EACxBC,aAA4B;IAL5B,KAAAN,KAAK,GAALA,KAAK;IAAuB,KAAAC,MAAM,GAANA,MAAM;IAAuC,KAAAT,UAAU,GAAVA,UAAU;IACnF,KAAAU,cAAc,GAAdA,cAAc;IACd,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IAzEjC,KAAAC,kBAAkB,GAAY1J,WAAW,CAAC2J,YAAY;IAC5C,KAAAC,WAAW,GAAsB,IAAI9J,YAAY,EAAO;IAElE,KAAA0F,QAAQ,GAAY,KAAK;IAGzB,KAAAqE,WAAW,GAAQ,EAAE;IACF,KAAA7J,WAAW,GAAGA,WAAW;IAC5C,KAAAiJ,cAAc,GAAW,cAAc;IACvC,KAAAa,gBAAgB,GAAS,KAAK;IAC9B,KAAAC,iBAAiB,GAAS,KAAK;IAO/B,KAAA1F,WAAW,GAAQ,CACjB;MACEH,SAAS,EAAE,8BAA8B;MACzCF,QAAQ,EAAE,2CAA2C;MACrDgG,QAAQ,EAAE;KACX,EAAC;MACA9F,SAAS,EAAE,6BAA6B;MACxCF,QAAQ,EAAE,qCAAqC;MAC/CgG,QAAQ,EAAE;KACX,EAAC;MACA9F,SAAS,EAAE,sBAAsB;MACjCF,QAAQ,EAAE,4CAA4C;MACtDgG,QAAQ,EAAE;KACX,EAAC;MACA9F,SAAS,EAAE,wBAAwB;MACnCF,QAAQ,EAAE,6CAA6C;MACvDgG,QAAQ,EAAE;KACX,EAAC;MACA9F,SAAS,EAAE,qBAAqB;MAChCF,QAAQ,EAAE,wCAAwC;MAClDgG,QAAQ,EAAE;KACX,CACF;IAmCC,IAAI,CAACF,gBAAgB,GAAG,IAAI,CAACR,iBAAiB,CAACW,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACF,iBAAiB,GAAG,IAAI,CAACT,iBAAiB,CAACW,aAAa,CAAC,kBAAkB,CAAC;IAEjF,IAAI,CAACzE,QAAQ,GAAGxF,WAAW,CAAC2J,YAAY;IACxC,IAAI1J,iBAAiB,CAAC,IAAI,CAAC0I,UAAU,CAAC,EAAE;MACtC,IAAI,CAACuB,WAAW,GAAGrB,MAAM,CAACE,MAAM,CAACoB,KAAK;MACtC,IAAI,IAAI,CAACD,WAAW,GAAG,GAAG,EAAE;QAC1B,IAAI,CAACE,WAAW,GAAG,IAAI;OACxB,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,KAAK;;;IAI5B,IAAG,CAAC,IAAI,CAACZ,WAAW,CAACa,aAAa,EAAE,EAAC;MACnC,MAAMC,WAAW,GAAG,IAAI,CAACjB,cAAc,CAACkB,eAAe;MACvD,IAAGD,WAAW,EAAE;QACd,KAAK,MAAME,IAAI,IAAI,IAAI,CAACnG,WAAW,EAAE;UACnC,IAAIiG,WAAW,CAACG,cAAc,CAACD,IAAI,CAACR,QAAQ,CAAC,IAAIM,WAAW,CAACE,IAAI,CAACR,QAAQ,CAAC,KAAK,IAAI,EAAE;YACpFQ,IAAI,CAACE,OAAO,GAAGJ,WAAW,CAACE,IAAI,CAACR,QAAQ,CAAC;;;;;EAKnD;EAEAW,QAAQA,CAAA;IACN,IAAI,CAAClB,aAAa,CAACmB,6BAA6B,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;MACxE,IAAI,CAACvF,OAAO,GAAGuF,GAAG,CAACC,IAAI;MAEvB,IAAIC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC,sBAAsB,CAAC;MAC3D,IAAI,CAACD,MAAM,EACTA,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC,sBAAsB,CAAC;MAEzD,IAAID,MAAM,EAAE;QACVE,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEH,MAAM,EAAEjJ,EAAE,CAAC;;IAE5D,CAAC,CAAC;EACJ;EAEAkJ,iBAAiBA,CAACG,UAAkB;IAClC,KAAK,MAAMC,OAAO,IAAI,IAAI,CAAC9F,OAAO,EAAE;MAClC,MAAM+F,IAAI,GAAGD,OAAO,CAAC1I,KAAK,CAAC4I,IAAI,CAAED,IAAS,IACxCA,IAAI,CAACtJ,SAAS,EAAEwJ,WAAW,EAAE,CAACC,QAAQ,CAACL,UAAU,CAACI,WAAW,EAAE,CAAC,CACjE;MACD,IAAIF,IAAI,EAAE;QACR,OAAOA,IAAI;;;IAGf,OAAOI,SAAS;EAClB;EAEA5J,iBAAiBA,CAAC6J,MAAc,EAAEC,KAAa;IAC7C,IAAG,IAAI,CAAC7B,iBAAiB,EAAC;MAC1B,IAAI6B,KAAK,CAACH,QAAQ,CAAC,sBAAsB,CAAC,EAAE;QAC1C,IAAI,CAAClC,UAAU,CAACd,KAAK,CAACvI,iBAAiB,CAAC2L,6BAA6B,EAAE,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE,IAAI,CAAC;OAC3G,MAAM,IAAID,KAAK,CAACH,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,CAAClC,UAAU,CAACd,KAAK,CAACvI,iBAAiB,CAAC4L,uBAAuB,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC;;;IAIjG,IAAI,CAAC1C,MAAM,CAAC2C,aAAa,CAAC,GAAG,EAAE;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MAChE,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;MACnCC,WAAW,EAAE;QAAER,MAAM,EAAEA,MAAM;QAAEC,KAAK,EAAEA;MAAK,CAAE;MAAEQ,KAAK,EAAE;QAAErK,EAAE,EAAE4J;MAAM;KACnE,CAAC,CACH;EACH;EAEA/K,YAAYA,CAACuG,GAAW;IACtB,IAAI,CAACiC,MAAM,CACR2C,aAAa,CAAC,GAAG,EAAE;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC,CAChDC,IAAI,CAAC,MAAM,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,GAAG,GAAG/E,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EACxD;EAEAkF,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAEd,IAAI,CAACnD,KAAK,CAACoD,YAAY,CAAC,QAAQ,CAAC,CAAC1B,SAAS,CAAC;QAC1C2B,IAAI,EAAG1B,GAAQ,IAAM,IAAI,CAAC2B,MAAM,GAAG3B;OACpC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,CAAC3B,KAAK,CAACoD,YAAY,CAAC,gBAAgB,CAAC,CAAC1B,SAAS,CAAC;MAClD2B,IAAI,EAAG1B,GAAQ,IAAI;QACjB,IAAIA,GAAG,CAACtH,MAAM,EAAE;UACd,IAAI,CAACqG,WAAW,GAAGqB,YAAY,CAACwB,OAAO,CAAC,aAAa,CAAC;;MAE1D;KACD,CAAC;EACJ;EAEA3H,SAASA,CAAA;IACP8D,MAAM,CAAC8D,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC;IAClB,IAAI1M,iBAAiB,CAAC,IAAI,CAAC0I,UAAU,CAAC,EAAE;MACtCE,MAAM,CAAC8D,MAAM,CAAC;QACZC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAAClD,WAAW,CAACmD,IAAI,EAAE;;EAE3B;EACA1L,mBAAmBA,CAAA;IACjB,IAAG,IAAI,CAAC0I,iBAAiB,EAAC;MAC1B,IAAI,CAACR,UAAU,CAACd,KAAK,CAACvI,iBAAiB,CAAC8M,mCAAmC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC;;IAE7F,IAAI/M,iBAAiB,CAAC,IAAI,CAAC0I,UAAU,CAAC,EAAE;MACtCE,MAAM,CAACoE,IAAI,CAACjN,WAAW,CAACkN,WAAW,GAAG,YAAY,GAAG,GAAGhC,YAAY,CAACwB,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC;;EAEzG;EAAC,QAAAS,CAAA,G;qBAxLU5E,eAAe,EAAApI,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnN,EAAA,CAAAiN,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArN,EAAA,CAAAiN,iBAAA,CAuE8CrN,WAAW,GAAAI,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAI,cAAA,GAAAtN,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAK,iBAAA,GAAAvN,EAAA,CAAAiN,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAzN,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAQ,WAAA,GAAA1N,EAAA,CAAAiN,iBAAA,CAAAU,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAvExEzF,eAAe;IAAA0F,SAAA;IAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAfC,GAAA,CAAA7F,QAAA,CAAA8F,MAAA,CAAgB;QAAA,UAAAnO,EAAA,CAAAoO,eAAA;;;;;;;;;;;;;;;QCd7BpO,EAAA,CAAA8B,UAAA,IAAAuM,iCAAA,qBA2HS;QACTrO,EAAA,CAAA8B,UAAA,IAAAwM,iCAAA,sBAsES;;;QAlMetO,EAAA,CAAAqB,UAAA,SAAA6M,GAAA,CAAAjE,WAAA,KAAAiE,GAAA,CAAAvE,gBAAA,CAAsC;QA4H/B3J,EAAA,CAAAY,SAAA,GAAiD;QAAjDZ,EAAA,CAAAqB,UAAA,UAAA6M,GAAA,CAAAjE,WAAA,KAAAiE,GAAA,CAAAK,MAAA,IAAAL,GAAA,CAAAvE,gBAAA,CAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}