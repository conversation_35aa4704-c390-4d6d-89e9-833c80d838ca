.header {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  ::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  .p-badge {
    font-size: 0.75rem;
    min-width: 1rem;
    height: 1rem;
    line-height: 1rem;
    background: #ffcc00;
    color: #000000;
  }
}

.v3-header {
  .p-badge {
    font-size: 0.75rem;
    min-width: 1rem;
    height: 1rem;
    line-height: 1rem;
    background: #ffcc00;
    color: #000000;
  }
}

.side-menu {


  .p-sidebar-icon {
    color: white !important;

    &:hover {
      color: white !important;
    }
  }
}

.main-slider {
  .owl-theme {
    .owl-dots {
      position: relative;
      bottom: 35px;

      .owl-dot {
        span {
          width: 30px;
          height: 5px;
          background: white;
        }

        &.active {
          span {
            background: #ffcc00;
          }
        }

        &:hover {
          span {
            background: #ffcc00;
          }
        }
      }
    }
  }
}

.p-rating {
  .p-rating-icon.pi-star-fill {
    color: var(--rating-color) !important;
  }
  // temporary addition
  .p-rating-icon.p-rating-icon-active {
    color: var(--rating-color) !important;
  }
  .p-rating-icon.p-icon {
    width: 1rem !important;
    height: 1rem !important;
  }
}

.p-inputgroup-addon {
  &:first-child {
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
  }

  &:last-child {
    border-top-right-radius: 5px !important;
    border-bottom-right-radius: 5px !important;
  }
}

.p-overlaypanel {
  .profile-menu {
    min-width: 200px !important;
  }
}

@media only screen and (max-width: 768px) {
  .p-sidebar-header {
    padding-top: 17px !important;
  }
}
