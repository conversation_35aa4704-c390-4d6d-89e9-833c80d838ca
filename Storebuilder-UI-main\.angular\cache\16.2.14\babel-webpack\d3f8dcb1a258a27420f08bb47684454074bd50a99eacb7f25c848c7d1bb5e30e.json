{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nconst _c0 = function () {\n  return [\"/categories\"];\n};\nconst _c1 = [\"*\"];\nexport class SectionCategoryComponent {\n  ngOnInit() {}\n  static #_ = this.ɵfac = function SectionCategoryComponent_Factory(t) {\n    return new (t || SectionCategoryComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SectionCategoryComponent,\n    selectors: [[\"app-section-category\"]],\n    inputs: {\n      title: \"title\"\n    },\n    ngContentSelectors: _c1,\n    decls: 10,\n    vars: 8,\n    consts: [[1, \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"category-heading\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"view-all\", \"view-all-btn\", 3, \"label\", \"routerLink\"], [\"alt\", \"No Image\", \"src\", \"assets/images/payment-icons/a-r.svg\", 1, \"arw\"], [1, \"section-block\"], [1, \"mt-3\", \"h-11rem\", \"mobile-swiper\"]],\n    template: function SectionCategoryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelement(6, \"img\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"section\", 4)(8, \"div\", 5);\n        i0.ɵɵprojection(9);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, \"featureType.categories\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(5, 5, \"buttons.viewAll\"))(\"routerLink\", i0.ɵɵpureFunction0(7, _c0));\n      }\n    },\n    dependencies: [i1.ButtonDirective, i2.RouterLink, i3.TranslatePipe],\n    styles: [\".section-block[_ngcontent-%COMP%] {\\n  height: auto;\\n  width: 100%;\\n  position: relative;\\n  max-width: 93vw;\\n  padding: 1.5rem 2.5rem 0rem 3rem;\\n  background: #fafafa 0% 0% no-repeat padding-box;\\n  border-radius: 10px;\\n  z-index: 1;\\n  left: 0px;\\n  padding-top: 4px;\\n  padding-left: 80px;\\n}\\n.section-block[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  z-index: 999;\\n  border: none;\\n  color: #000000;\\n  width: 135px;\\n  font-size: 16px;\\n}\\n.section-block[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #000000;\\n}\\n.section-block[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: flex;\\n  position: absolute;\\n  left: 100px;\\n  width: 17px;\\n  height: 10px;\\n}\\n.section-block[_ngcontent-%COMP%]   .p-button.p-button-outlined[_ngcontent-%COMP%]:enabled:hover {\\n  background: rgba(33, 150, 243, 0.04);\\n  color: white;\\n  border: none;\\n}\\n\\n.arw[_ngcontent-%COMP%] {\\n  display: flex;\\n  position: absolute;\\n  left: 100px;\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  border: none !important;\\n  color: black !important;\\n  width: 135px;\\n  background-color: var(--main_bt_bgcolor) !important;\\n  font-size: 14px;\\n  font-family: var(--medium-font);\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .section-block[_ngcontent-%COMP%] {\\n    height: 150px;\\n    width: 100%;\\n    padding: 20px;\\n    background: #fafafa 0% 0% no-repeat padding-box;\\n    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.16);\\n    border-radius: 10px;\\n    opacity: 1;\\n    z-index: 2;\\n  }\\n  div[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    color: black !important;\\n    padding-left: 0px !important;\\n    font-size: 20px !important;\\n    margin-bottom: 0;\\n  }\\n  div[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    color: #214563 !important;\\n  }\\n  div[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    filter: inherit;\\n  }\\n  .view-all-btn[_ngcontent-%COMP%] {\\n    font-size: 16px !important;\\n  }\\n  .mobile-swiper[_ngcontent-%COMP%] {\\n    margin-top: 0px !important;\\n    height: 0px !important;\\n  }\\n}\\n@media screen and (max-width: 320px) {\\n  .section-block[_ngcontent-%COMP%] {\\n    height: 230px;\\n    margin-top: -150px;\\n    width: 100%;\\n    padding: 20px;\\n    background: #fafafa 0% 0% no-repeat padding-box;\\n    box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.16);\\n    border-radius: 10px;\\n    opacity: 1;\\n    z-index: 9;\\n  }\\n  div[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    color: black !important;\\n  }\\n  div[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    color: #214563 !important;\\n  }\\n  div[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    filter: inherit;\\n  }\\n}\\nh2[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-family: var(--regular-font);\\n  font-size: 28px;\\n  color: #000000;\\n  padding-left: 40px;\\n}\\n\\nbutton[_ngcontent-%COMP%]:focus:not(:focus-visible) {\\n  box-shadow: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["SectionCategoryComponent", "ngOnInit", "_", "_2", "selectors", "inputs", "title", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "SectionCategoryComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵprojection", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\components\\section-category\\section.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\home\\components\\section-category\\section.component.html"], "sourcesContent": ["import {Component, Input, OnInit} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-section-category',\r\n  templateUrl: './section.component.html',\r\n  styleUrls: ['./section.component.scss'],\r\n})\r\nexport class SectionCategoryComponent implements OnInit {\r\n\r\n  @Input() title: string | undefined;\r\n\r\n  ngOnInit(): void {/**/\r\n  }\r\n}\r\n", "<div class=\"flex flex-row justify-content-between\">\r\n  <h2 class=\"category-heading\">{{ \"featureType.categories\" | translate }}</h2>\r\n  <button\r\n    [label]=\"'buttons.viewAll' | translate\"\r\n    [routerLink]=\"['/categories']\"\r\n    class=\"p-button-outlined view-all view-all-btn\"\r\n    pButton\r\n    type=\"button\"\r\n  >\r\n    <img alt=\"No Image\" class=\"arw\" src=\"assets/images/payment-icons/a-r.svg\" />\r\n  </button>\r\n</div>\r\n<section class=\"section-block\">\r\n  <div class=\"mt-3 h-11rem mobile-swiper\">\r\n    <ng-content></ng-content>\r\n  </div>\r\n</section>\r\n"], "mappings": ";;;;;;;;AAOA,OAAM,MAAOA,wBAAwB;EAInCC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUF,wBAAwB;EAAA;EAAA,QAAAG,EAAA,G;UAAxBH,wBAAwB;IAAAI,SAAA;IAAAC,MAAA;MAAAC,KAAA;IAAA;IAAAC,kBAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCPrCE,EAAA,CAAAC,cAAA,aAAmD;QACpBD,EAAA,CAAAE,MAAA,GAA0C;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5EH,EAAA,CAAAC,cAAA,gBAMC;;QACCD,EAAA,CAAAI,SAAA,aAA4E;QAC9EJ,EAAA,CAAAG,YAAA,EAAS;QAEXH,EAAA,CAAAC,cAAA,iBAA+B;QAE3BD,EAAA,CAAAK,YAAA,GAAyB;QAC3BL,EAAA,CAAAG,YAAA,EAAM;;;QAduBH,EAAA,CAAAM,SAAA,GAA0C;QAA1CN,EAAA,CAAAO,iBAAA,CAAAP,EAAA,CAAAQ,WAAA,iCAA0C;QAErER,EAAA,CAAAM,SAAA,GAAuC;QAAvCN,EAAA,CAAAS,UAAA,UAAAT,EAAA,CAAAQ,WAAA,0BAAuC,eAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}