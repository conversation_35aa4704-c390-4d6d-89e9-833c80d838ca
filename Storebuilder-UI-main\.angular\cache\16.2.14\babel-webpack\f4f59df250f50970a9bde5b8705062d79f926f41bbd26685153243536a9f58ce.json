{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * Represents a big integer using a buffer of its individual digits, with the least significant\n * digit stored at the beginning of the array (little endian).\n *\n * For performance reasons, each instance is mutable. The addition operation can be done in-place\n * to reduce memory pressure of allocation for the digits array.\n */\nclass BigInteger {\n  static zero() {\n    return new BigInteger([0]);\n  }\n  static one() {\n    return new BigInteger([1]);\n  }\n  /**\n   * Creates a big integer using its individual digits in little endian storage.\n   */\n  constructor(digits) {\n    this.digits = digits;\n  }\n  /**\n   * Creates a clone of this instance.\n   */\n  clone() {\n    return new BigInteger(this.digits.slice());\n  }\n  /**\n   * Returns a new big integer with the sum of `this` and `other` as its value. This does not mutate\n   * `this` but instead returns a new instance, unlike `addToSelf`.\n   */\n  add(other) {\n    const result = this.clone();\n    result.addToSelf(other);\n    return result;\n  }\n  /**\n   * Adds `other` to the instance itself, thereby mutating its value.\n   */\n  addToSelf(other) {\n    const maxNrOfDigits = Math.max(this.digits.length, other.digits.length);\n    let carry = 0;\n    for (let i = 0; i < maxNrOfDigits; i++) {\n      let digitSum = carry;\n      if (i < this.digits.length) {\n        digitSum += this.digits[i];\n      }\n      if (i < other.digits.length) {\n        digitSum += other.digits[i];\n      }\n      if (digitSum >= 10) {\n        this.digits[i] = digitSum - 10;\n        carry = 1;\n      } else {\n        this.digits[i] = digitSum;\n        carry = 0;\n      }\n    }\n    // Apply a remaining carry if needed.\n    if (carry > 0) {\n      this.digits[maxNrOfDigits] = 1;\n    }\n  }\n  /**\n   * Builds the decimal string representation of the big integer. As this is stored in\n   * little endian, the digits are concatenated in reverse order.\n   */\n  toString() {\n    let res = '';\n    for (let i = this.digits.length - 1; i >= 0; i--) {\n      res += this.digits[i];\n    }\n    return res;\n  }\n}\n/**\n * Represents a big integer which is optimized for multiplication operations, as its power-of-twos\n * are memoized. See `multiplyBy()` for details on the multiplication algorithm.\n */\nclass BigIntForMultiplication {\n  constructor(value) {\n    this.powerOfTwos = [value];\n  }\n  /**\n   * Returns the big integer itself.\n   */\n  getValue() {\n    return this.powerOfTwos[0];\n  }\n  /**\n   * Computes the value for `num * b`, where `num` is a JS number and `b` is a big integer. The\n   * value for `b` is represented by a storage model that is optimized for this computation.\n   *\n   * This operation is implemented in N(log2(num)) by continuous halving of the number, where the\n   * least-significant bit (LSB) is tested in each iteration. If the bit is set, the bit's index is\n   * used as exponent into the power-of-two multiplication of `b`.\n   *\n   * As an example, consider the multiplication num=42, b=1337. In binary 42 is 0b00101010 and the\n   * algorithm unrolls into the following iterations:\n   *\n   *  Iteration | num        | LSB  | b * 2^iter | Add? | product\n   * -----------|------------|------|------------|------|--------\n   *  0         | 0b00101010 | 0    | 1337       | No   | 0\n   *  1         | 0b00010101 | 1    | 2674       | Yes  | 2674\n   *  2         | 0b00001010 | 0    | 5348       | No   | 2674\n   *  3         | 0b00000101 | 1    | 10696      | Yes  | 13370\n   *  4         | 0b00000010 | 0    | 21392      | No   | 13370\n   *  5         | 0b00000001 | 1    | 42784      | Yes  | 56154\n   *  6         | 0b00000000 | 0    | 85568      | No   | 56154\n   *\n   * The computed product of 56154 is indeed the correct result.\n   *\n   * The `BigIntForMultiplication` representation for a big integer provides memoized access to the\n   * power-of-two values to reduce the workload in computing those values.\n   */\n  multiplyBy(num) {\n    const product = BigInteger.zero();\n    this.multiplyByAndAddTo(num, product);\n    return product;\n  }\n  /**\n   * See `multiplyBy()` for details. This function allows for the computed product to be added\n   * directly to the provided result big integer.\n   */\n  multiplyByAndAddTo(num, result) {\n    for (let exponent = 0; num !== 0; num = num >>> 1, exponent++) {\n      if (num & 1) {\n        const value = this.getMultipliedByPowerOfTwo(exponent);\n        result.addToSelf(value);\n      }\n    }\n  }\n  /**\n   * Computes and memoizes the big integer value for `this.number * 2^exponent`.\n   */\n  getMultipliedByPowerOfTwo(exponent) {\n    // Compute the powers up until the requested exponent, where each value is computed from its\n    // predecessor. This is simple as `this.number * 2^(exponent - 1)` only has to be doubled (i.e.\n    // added to itself) to reach `this.number * 2^exponent`.\n    for (let i = this.powerOfTwos.length; i <= exponent; i++) {\n      const previousPower = this.powerOfTwos[i - 1];\n      this.powerOfTwos[i] = previousPower.add(previousPower);\n    }\n    return this.powerOfTwos[exponent];\n  }\n}\n/**\n * Represents an exponentiation operation for the provided base, of which exponents are computed and\n * memoized. The results are represented by a `BigIntForMultiplication` which is tailored for\n * multiplication operations by memoizing the power-of-twos. This effectively results in a matrix\n * representation that is lazily computed upon request.\n */\nclass BigIntExponentiation {\n  constructor(base) {\n    this.base = base;\n    this.exponents = [new BigIntForMultiplication(BigInteger.one())];\n  }\n  /**\n   * Compute the value for `this.base^exponent`, resulting in a big integer that is optimized for\n   * further multiplication operations.\n   */\n  toThePowerOf(exponent) {\n    // Compute the results up until the requested exponent, where every value is computed from its\n    // predecessor. This is because `this.base^(exponent - 1)` only has to be multiplied by `base`\n    // to reach `this.base^exponent`.\n    for (let i = this.exponents.length; i <= exponent; i++) {\n      const value = this.exponents[i - 1].multiplyBy(this.base);\n      this.exponents[i] = new BigIntForMultiplication(value);\n    }\n    return this.exponents[exponent];\n  }\n}\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nfunction digest(message) {\n  return message.id || computeDigest(message);\n}\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nfunction computeDigest(message) {\n  return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nfunction decimalDigest(message) {\n  return message.id || computeDecimalDigest(message);\n}\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nfunction computeDecimalDigest(message) {\n  const visitor = new _SerializerIgnoreIcuExpVisitor();\n  const parts = message.nodes.map(a => a.visit(visitor, null));\n  return computeMsgId(parts.join(''), message.meaning);\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor {\n  visitText(text, context) {\n    return text.value;\n  }\n  visitContainer(container, context) {\n    return `[${container.children.map(child => child.visit(this)).join(', ')}]`;\n  }\n  visitIcu(icu, context) {\n    const strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n  }\n  visitTagPlaceholder(ph, context) {\n    return ph.isVoid ? `<ph tag name=\"${ph.startName}\"/>` : `<ph tag name=\"${ph.startName}\">${ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n  visitPlaceholder(ph, context) {\n    return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n  }\n  visitIcuPlaceholder(ph, context) {\n    return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n  }\n}\nconst serializerVisitor = new _SerializerVisitor();\nfunction serializeNodes(nodes) {\n  return nodes.map(a => a.visit(serializerVisitor, null));\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {\n  visitIcu(icu, context) {\n    let strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    // Do not take the expression into account\n    return `{${icu.type}, ${strCases.join(', ')}}`;\n  }\n}\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nfunction sha1(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = [...textEncoder.encode(str)];\n  const words32 = bytesToWords32(utf8, Endian.Big);\n  const len = utf8.length * 8;\n  const w = new Uint32Array(80);\n  let a = 0x67452301,\n    b = 0xefcdab89,\n    c = 0x98badcfe,\n    d = 0x10325476,\n    e = 0xc3d2e1f0;\n  words32[len >> 5] |= 0x80 << 24 - len % 32;\n  words32[(len + 64 >> 9 << 4) + 15] = len;\n  for (let i = 0; i < words32.length; i += 16) {\n    const h0 = a,\n      h1 = b,\n      h2 = c,\n      h3 = d,\n      h4 = e;\n    for (let j = 0; j < 80; j++) {\n      if (j < 16) {\n        w[j] = words32[i + j];\n      } else {\n        w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      }\n      const fkVal = fk(j, b, c, d);\n      const f = fkVal[0];\n      const k = fkVal[1];\n      const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n      e = d;\n      d = c;\n      c = rol32(b, 30);\n      b = a;\n      a = temp;\n    }\n    a = add32(a, h0);\n    b = add32(b, h1);\n    c = add32(c, h2);\n    d = add32(d, h3);\n    e = add32(e, h4);\n  }\n  // Convert the output parts to a 160-bit hexadecimal string\n  return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value) {\n  // unsigned right shift of zero ensures an unsigned 32-bit number\n  return (value >>> 0).toString(16).padStart(8, '0');\n}\nfunction fk(index, b, c, d) {\n  if (index < 20) {\n    return [b & c | ~b & d, 0x5a827999];\n  }\n  if (index < 40) {\n    return [b ^ c ^ d, 0x6ed9eba1];\n  }\n  if (index < 60) {\n    return [b & c | b & d | c & d, 0x8f1bbcdc];\n  }\n  return [b ^ c ^ d, 0xca62c1d6];\n}\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -0x6b5f56d8;\n  }\n  return [hi, lo];\n}\nfunction computeMsgId(msg, meaning = '') {\n  let msgFingerprint = fingerprint(msg);\n  if (meaning) {\n    const meaningFingerprint = fingerprint(meaning);\n    msgFingerprint = add64(rol64(msgFingerprint, 1), meaningFingerprint);\n  }\n  const hi = msgFingerprint[0];\n  const lo = msgFingerprint[1];\n  return wordsToDecimalString(hi & 0x7fffffff, lo);\n}\nfunction hash32(view, length, c) {\n  let a = 0x9e3779b9,\n    b = 0x9e3779b9;\n  let index = 0;\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    a = res[0], b = res[1], c = res[2];\n  }\n  const remainder = length - index;\n  // the first byte of c is reserved for the length\n  c += length;\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n  return mix(a, b, c)[2];\n}\n// clang-format off\nfunction mix(a, b, c) {\n  a -= b;\n  a -= c;\n  a ^= c >>> 13;\n  b -= c;\n  b -= a;\n  b ^= a << 8;\n  c -= a;\n  c -= b;\n  c ^= b >>> 13;\n  a -= b;\n  a -= c;\n  a ^= c >>> 12;\n  b -= c;\n  b -= a;\n  b ^= a << 16;\n  c -= a;\n  c -= b;\n  c ^= b >>> 5;\n  a -= b;\n  a -= c;\n  a ^= c >>> 3;\n  b -= c;\n  b -= a;\n  b ^= a << 10;\n  c -= a;\n  c -= b;\n  c ^= b >>> 15;\n  return [a, b, c];\n}\n// clang-format on\n// Utils\nvar Endian;\n(function (Endian) {\n  Endian[Endian[\"Little\"] = 0] = \"Little\";\n  Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\nfunction add32(a, b) {\n  return add32to64(a, b)[1];\n}\nfunction add32to64(a, b) {\n  const low = (a & 0xffff) + (b & 0xffff);\n  const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n  return [high >>> 16, high << 16 | low & 0xffff];\n}\nfunction add64(a, b) {\n  const ah = a[0],\n    al = a[1];\n  const bh = b[0],\n    bl = b[1];\n  const result = add32to64(al, bl);\n  const carry = result[0];\n  const l = result[1];\n  const h = add32(add32(ah, bh), carry);\n  return [h, l];\n}\n// Rotate a 32b number left `count` position\nfunction rol32(a, count) {\n  return a << count | a >>> 32 - count;\n}\n// Rotate a 64b number left `count` position\nfunction rol64(num, count) {\n  const hi = num[0],\n    lo = num[1];\n  const h = hi << count | lo >>> 32 - count;\n  const l = lo << count | hi >>> 32 - count;\n  return [h, l];\n}\nfunction bytesToWords32(bytes, endian) {\n  const size = bytes.length + 3 >>> 2;\n  const words32 = [];\n  for (let i = 0; i < size; i++) {\n    words32[i] = wordAt(bytes, i * 4, endian);\n  }\n  return words32;\n}\nfunction byteAt(bytes, index) {\n  return index >= bytes.length ? 0 : bytes[index];\n}\nfunction wordAt(bytes, index, endian) {\n  let word = 0;\n  if (endian === Endian.Big) {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 24 - 8 * i;\n    }\n  } else {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 8 * i;\n    }\n  }\n  return word;\n}\n/**\n * Create a shared exponentiation pool for base-256 computations. This shared pool provides memoized\n * power-of-256 results with memoized power-of-two computations for efficient multiplication.\n *\n * For our purposes, this can be safely stored as a global without memory concerns. The reason is\n * that we encode two words, so only need the 0th (for the low word) and 4th (for the high word)\n * exponent.\n */\nconst base256 = new BigIntExponentiation(256);\n/**\n * Represents two 32-bit words as a single decimal number. This requires a big integer storage\n * model as JS numbers are not accurate enough to represent the 64-bit number.\n *\n * Based on https://www.danvk.org/hex2dec.html\n */\nfunction wordsToDecimalString(hi, lo) {\n  // Encode the four bytes in lo in the lower digits of the decimal number.\n  // Note: the multiplication results in lo itself but represented by a big integer using its\n  // decimal digits.\n  const decimal = base256.toThePowerOf(0).multiplyBy(lo);\n  // Encode the four bytes in hi above the four lo bytes. lo is a maximum of (2^8)^4, which is why\n  // this multiplication factor is applied.\n  base256.toThePowerOf(4).multiplyByAndAddTo(hi, decimal);\n  return decimal.toString();\n}\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n  const substitutions = {};\n  const substitutionLocations = {};\n  const associatedMessageIds = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts = [metadata.text];\n  const placeholderNames = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location\n  };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n  const {\n    text: messageString,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      text: messageString\n    };\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {\n      text: messageString,\n      meaning,\n      description,\n      customId,\n      legacyIds\n    };\n  }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n  const {\n    text: messagePart,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      messagePart\n    };\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {\n      messagePart,\n      placeholderName,\n      associatedMessageId\n    };\n  }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n  if (raw.charAt(0) !== BLOCK_MARKER$1) {\n    return {\n      text: cooked\n    };\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1)\n    };\n  }\n}\nfunction computePlaceholderName(index) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\nclass MissingTranslationError extends Error {\n  constructor(parsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n    this.parsedMessage = parsedMessage;\n    this.type = 'MissingTranslationError';\n  }\n}\nfunction isMissingTranslationError(e) {\n  return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n  const message = parseMessage(messageParts, substitutions);\n  // Look up the translation using the messageId, and then the legacyId if available.\n  let translation = translations[message.id];\n  // If the messageId did not match a translation, try matching the legacy ids instead\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n  return [translation.messageParts, translation.placeholderNames.map(placeholder => {\n    if (message.substitutions.hasOwnProperty(placeholder)) {\n      return message.substitutions[placeholder];\n    } else {\n      throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` + `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n    }\n  })];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames = [];\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n  const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames\n  };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n  let messageString = messageParts[0];\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames\n  };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n  Object.defineProperty(cooked, 'raw', {\n    value: raw\n  });\n  return cooked;\n}\nfunction describeMessage(message) {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy = message.legacyIds && message.legacyIds.length > 0 ? ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` : '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n  Object.keys(translations).forEach(key => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n  try {\n    return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn(e.message);\n    return [messageParts, substitutions];\n  }\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n-common-prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n  if ($localize$1.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize$1.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) : messagePart;\n}\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n\n// This file contains the public API of the `@angular/localize` entry-point\n\n// DO NOT ADD public exports to this file.\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };\n//# sourceMappingURL=localize.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}