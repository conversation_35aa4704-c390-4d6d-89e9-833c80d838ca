{"ast": null, "code": "import { BehaviorSubject, map } from 'rxjs';\nimport { JwtHelperService } from \"@auth0/angular-jwt\";\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-cookie-service\";\nexport class AuthTokenService {\n  http;\n  cookieService;\n  isRefreshTokenCalled = false;\n  helper = new JwtHelperService();\n  baseUrl;\n  userController;\n  otpController;\n  authToken = new BehaviorSubject(null);\n  authTokenData = this.authToken.asObservable();\n  constructor(http, cookieService) {\n    this.http = http;\n    this.cookieService = cookieService;\n    this.baseUrl = `${environment.apiEndPoint}/Auth`;\n    this.userController = '/User';\n    this.otpController = '/OtpUser';\n  }\n  authTokenSet(message) {\n    this.authToken.next(message);\n  }\n  refreshToken(data) {\n    return this.http.post(`${this.baseUrl}${this.userController}/RefreshToken`, data).pipe(map(res => {\n      return res;\n    }));\n  }\n  static ɵfac = function AuthTokenService_Factory(t) {\n    return new (t || AuthTokenService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.CookieService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthTokenService,\n    factory: AuthTokenService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "map", "JwtHelperService", "environment", "AuthTokenService", "http", "cookieService", "isRefreshTokenCalled", "helper", "baseUrl", "userController", "otpController", "authToken", "authTokenData", "asObservable", "constructor", "apiEndPoint", "authTokenSet", "message", "next", "refreshToken", "data", "post", "pipe", "res", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "CookieService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\token.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {BehaviorSubject, map, Observable} from 'rxjs';\r\nimport {JwtHelperService} from \"@auth0/angular-jwt\";\r\nimport {CookieService} from 'ngx-cookie-service';\r\nimport {HttpClient} from '@angular/common/http';\r\n\r\nimport {environment} from 'src/environments/environment';\r\nimport {ResponseModel, RefreshTokenViewModel} from \"../interface\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthTokenService {\r\n\r\n  isRefreshTokenCalled: boolean = false;\r\n  helper = new JwtHelperService();\r\n  baseUrl: string;\r\n  userController: string;\r\n  otpController: string;\r\n  private authToken = new BehaviorSubject<any>(null);\r\n  authTokenData = this.authToken.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private cookieService: CookieService) {\r\n    this.baseUrl = `${environment.apiEndPoint}/Auth`;\r\n    this.userController = '/User';\r\n    this.otpController = '/OtpUser';\r\n  }\r\n\r\n  authTokenSet(message: string) {\r\n\r\n    this.authToken.next(message);\r\n  }\r\n\r\n\r\n  refreshToken(data: RefreshTokenViewModel): Observable<ResponseModel<RefreshTokenViewModel>> {\r\n    return this.http.post(`${this.baseUrl}${this.userController}/RefreshToken`, data)\r\n      .pipe(map(res => {\r\n        return res;\r\n      }));\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAQA,eAAe,EAAEC,GAAG,QAAmB,MAAM;AACrD,SAAQC,gBAAgB,QAAO,oBAAoB;AAInD,SAAQC,WAAW,QAAO,8BAA8B;;;;AAMxD,OAAM,MAAOC,gBAAgB;EAWjBC,IAAA;EACAC,aAAA;EAVVC,oBAAoB,GAAY,KAAK;EACrCC,MAAM,GAAG,IAAIN,gBAAgB,EAAE;EAC/BO,OAAO;EACPC,cAAc;EACdC,aAAa;EACLC,SAAS,GAAG,IAAIZ,eAAe,CAAM,IAAI,CAAC;EAClDa,aAAa,GAAG,IAAI,CAACD,SAAS,CAACE,YAAY,EAAE;EAE7CC,YACUV,IAAgB,EAChBC,aAA4B;IAD5B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,aAAa,GAAbA,aAAa;IACrB,IAAI,CAACG,OAAO,GAAG,GAAGN,WAAW,CAACa,WAAW,OAAO;IAChD,IAAI,CAACN,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,aAAa,GAAG,UAAU;EACjC;EAEAM,YAAYA,CAACC,OAAe;IAE1B,IAAI,CAACN,SAAS,CAACO,IAAI,CAACD,OAAO,CAAC;EAC9B;EAGAE,YAAYA,CAACC,IAA2B;IACtC,OAAO,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,GAAG,IAAI,CAACC,cAAc,eAAe,EAAEW,IAAI,CAAC,CAC9EE,IAAI,CAACtB,GAAG,CAACuB,GAAG,IAAG;MACd,OAAOA,GAAG;IACZ,CAAC,CAAC,CAAC;EACP;;qBA7BWpB,gBAAgB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;;WAAhB1B,gBAAgB;IAAA2B,OAAA,EAAhB3B,gBAAgB,CAAA4B,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}