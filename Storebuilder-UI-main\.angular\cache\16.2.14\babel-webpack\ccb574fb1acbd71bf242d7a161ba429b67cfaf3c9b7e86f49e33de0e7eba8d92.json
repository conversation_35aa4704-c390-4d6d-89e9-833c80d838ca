{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class GTMService {\n  injectGTM(tenants, tenantId) {\n    // get google tag manager id by tenant\n    const gtmId = tenants?.find(tenant => tenant.tenantId == tenantId)?.GTMConatinerId || '';\n    // Inject the <script> tag in the <head> section\n    const scriptTag = document.createElement('script');\n    scriptTag.type = 'text/javascript';\n    scriptTag.async = true;\n    scriptTag.src = `https://www.googletagmanager.com/gtm.js?id=${gtmId}`;\n    // Create the GTM dataLayer and the initialization script\n    const inlineScript = document.createElement('script');\n    inlineScript.text = `\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','${gtmId}');\n        `;\n    // Append the inline script and the external script to the <head>\n    document.head.appendChild(inlineScript);\n    document.head.appendChild(scriptTag);\n    // Inject the <noscript> tag in the <body> section\n    const noScriptTag = document.createElement('noscript');\n    noScriptTag.innerHTML = `<iframe src=\"https://www.googletagmanager.com/ns.html?id=${gtmId}\"\n                                 height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe>`;\n    document.body.appendChild(noScriptTag);\n  }\n  pushDataLayer(obj) {\n    if (obj) {\n      window.dataLayer = window.dataLayer || [];\n      window.dataLayer.push(obj);\n    }\n  }\n  pushPageView(category, label, labelId) {\n    if (category?.includes('->')) {\n      category = category.split('->').join(' > ');\n    }\n    const pageViewURL = label ? category + ' > ' + label + (labelId ? +' > ' + labelId : '') : category;\n    const gtmTag = {\n      event: 'Page View',\n      event_label: pageViewURL\n    };\n    this.pushDataLayer(gtmTag);\n  }\n  pushEvent(event, category, action, label) {\n    const gtmTag = {\n      event: event,\n      category: category,\n      action: action,\n      label: label\n    };\n    this.pushDataLayer(gtmTag);\n  }\n}\nGTMService.ɵfac = function GTMService_Factory(t) {\n  return new (t || GTMService)();\n};\nGTMService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: GTMService,\n  factory: GTMService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}