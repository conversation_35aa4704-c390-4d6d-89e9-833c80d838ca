import {Component, ElementRef, HostListener, Inject, OnInit, PLATFORM_ID, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {MessageService} from 'primeng/api';
import {RoleEnum} from '@core/interface';
import {CountryISO, PhoneNumberFormat, SearchCountryField} from "ngx-intl-tel-input-gg";
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {take} from "rxjs";


import {AppDataService, PermissionService, RegisterService, StoreService} from '@core/services';
import {ConfirmationModalComponent} from "@shared/modals/confirmation-modal/confirmation-modal.component";
import {isPlatformBrowser} from "@angular/common";

@Component({
  selector: 'app-verify-mobile',
  templateUrl: './verify-mobile.component.html',
  styleUrls: ['./verify-mobile.component.scss']
})
export class VerifyMobileComponent implements OnInit {
  @ViewChild('phoneInput3', { read: ElementRef }) phoneInput!: ElementRef;
  toggleValue:boolean=false;
  phoneNumber: string = '';
  countryPhoneNumber: string = "";
  countryPhoneCode: string = "";
  phoneLength: number = 12;
  phoneInputLength: number = 12;
  CustomCountryISO: any;
  PhoneNumberFormat = PhoneNumberFormat;
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];
  SearchCountryField = SearchCountryField;
  verifyForm: FormGroup;
  customPlaceHolder: any = '';
  isPrimary: boolean;
  title: string = ''
  screenWidth:any=window.innerWidth;
  isMobileLayout: boolean = false;
  counter: any = 0;
  nationalNumber: any;
  userPhoneNumber: string;
  @HostListener('window:resize', ['$event'])
  onResize(event?: any) {
    if (isPlatformBrowser(this.platformId)) {
      this.screenWidth = window.innerWidth
    }
  }
  constructor(private otpService: RegisterService, @Inject(PLATFORM_ID) private platformId: any,
     private translate: TranslateService, private messageService: MessageService, private router: Router, private store: StoreService,
              private fb: FormBuilder,private modalService: BsModalService, private appDataService: AppDataService,
              private permissionService: PermissionService) {
    let tenantId = localStorage.getItem('tenantId');
    if(tenantId && tenantId !== '') {
      if(tenantId == '1') {
        this.customPlaceHolder = 'XXXXXXXXXX';
      } else if(tenantId == '2') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '3') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '4') {
        this.customPlaceHolder = 'XXXXXXXXXX';
      }
    }
  }

  ngOnInit(): void {
    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')

    // localStorage.setItem('isPrimary','false')
   this.isPrimary = JSON.parse(localStorage.getItem("isPrimary") ?? '');
   if(this.isPrimary){
      this.title = this.translate.instant('phoneNumber.verifyMobile.changeDefaultNumber')
   }else{
     this.title = this.translate.instant('phoneNumber.verifyMobile.addNew')
   }
    this.CustomCountryISO = localStorage.getItem("isoCode");
    this.verifyForm = this.fb.group({
      phoneNumber: ['', Validators.required]
    });

    if(this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')
      if(phoneLength) this.phoneInputLength = parseInt(phoneLength.value)
    }
      this.verifyForm.controls.phoneNumber.valueChanges.subscribe(value => {
          let tenantId = localStorage.getItem('tenantId');
          let firstChar = value?.number?.charAt(0)
          let countryCode = value?.countryCode
          if( countryCode == 'GH'){
            //  if(firstChar == '0'){
            //     this.counter++
            //   }
            // if(firstChar == '0' || this.counter > 2 || this.counter > 5 || this.counter > 7 && tenantId == '2' ){
            //   this.phoneInputLength=10
            //     if(value?.number.length <2){
            //       this.counter = 0
            //     }
            // }else{
                if (this.appDataService.configuration) {
                  const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
                  if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);
                }
            // }
          }else if(countryCode == 'UG'){
              const inputElement = this.phoneInput.nativeElement.querySelector('input');
              this.nationalNumber = inputElement?.value || '';
             if (this.appDataService.configuration) {
              const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');
              if (phoneLength) {
                this.phoneInputLength = parseInt(phoneLength.value) - (this.nationalNumber[0] === '0' ? 0 : 1);
              }
            }
          }
         
 
      })

  }

  onRawPhoneNumberChange($event: string) {
        this.nationalNumber = $event
     }
  checkMobileExist() {
    this.store.set("loading", true);
    this.otpService.toggleBoolean = this.toggleValue;
    const confirmationModalData: ModalOptions = {
      initialState: {
        confirmationModalDetails: {
          header:"Changing default number",
          message: 'Going forward, this number will be used in:',
          point1:"Sign in activities",
          point2:"Receiving important notifications"
        }
      }
    };
    if (this.verifyForm.valid && this.toggleValue) {

      const bsModalRefConfirmation: BsModalRef = this.modalService.show(ConfirmationModalComponent, confirmationModalData);
      bsModalRefConfirmation.content.submit.pipe(take(1)).subscribe((value: boolean) => {
        if (value) {
          this.primarySecondaryMobileServiceFunction();
        }
      });
    }else if (this.verifyForm.valid && !this.toggleValue){
      if (this.verifyForm.valid) {
        this.primarySecondaryMobileServiceFunction();
      } else {
      this.handleInvalidForm()
      }
    }
  }
  primarySecondaryMobileServiceFunction(){
    this.phoneNumber = this.verifyForm.controls.phoneNumber.value.e164Number.slice(1);
    let tenantISO = localStorage.getItem("isoCode")
    if(this.userPhoneNumber && this.nationalNumber.length == 10 && tenantISO == 'UG'){
        this.phoneNumber = this.userPhoneNumber.slice(1);
    }
    this.otpService.username = this.phoneNumber;
    this.otpService.countryId = "1448983B-0C38-450A-BD71-9204D181B925";
    const isPrimary = localStorage.getItem('isPrimary');
    if (isPrimary && isPrimary != '' && isPrimary === 'true') {
      this.otpService.checkMobileNumber({
        UserName: this.phoneNumber,
        CountryId: "1448983B-0C38-450A-BD71-9204D181B925",
        UserRole: RoleEnum.consumer
      })
        .subscribe({
          next: (res: any) => this.handleResponse(res),
          error: (err: any) => this.handleError(err)
        });
    } else {
      this.otpService.checkSecondaryMobileNumber({userName: this.phoneNumber, userRole: RoleEnum.consumer})
        .subscribe({
          next: (res: any) => this.handleResponse(res),
          error: (err: any) => this.handleError(err)
        });
    }
  }
  handleInvalidForm() {
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: this.translate.instant('ErrorMessages.mobileRequired')
    });
    this.store.set("loading", false);
  }
  handleResponse(res: any) {
    this.store.set("loading", false);
    this.verifyResponse(res);
  }
  handleError(err: any) {
    this.store.set("loading", false);
    this.messageService.add({
      severity: 'error',
      summary: this.translate.instant('ErrorMessages.fetchError'),
      detail: err.message
    });
  }

  verifyResponse(res: any) {
    if (res.success) {
      if (!res.data?.isRegistered) {
        this.store.set('primaryPhone', this.phoneNumber);
        localStorage.setItem('primaryPhone', this.phoneNumber);
        localStorage.setItem("secondaryDefault",'true');
        this.router.navigate(['/account/verify-otp']);
      } else {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.mobileNumberAlreadyRegisteredBefore'),
          detail: res.message
        });
      }
    } else {
      this.messageService.add({
        severity: 'error',
        summary: res.message == 'InvalidMobileNumberFormat' ? this.translate.instant('ErrorMessages.phoneNumberIsUnvalid') : this.translate.instant('ErrorMessages.fetchError'),
        detail: res.Message
      });
      this.store.set("loading", false);
    }
  }

  omit_special_char(event: any) {
    let key;
    key = event.charCode;
    return (key > 47 && key < 58);
  }
  onToggleChange(event:any){
    this.toggleValue = event.target.checked;
    localStorage.setItem('isPrimary',JSON.stringify(this.toggleValue))
  }
  onBack(){
    this.router.navigate(['account/verify-user']);
  }
}
