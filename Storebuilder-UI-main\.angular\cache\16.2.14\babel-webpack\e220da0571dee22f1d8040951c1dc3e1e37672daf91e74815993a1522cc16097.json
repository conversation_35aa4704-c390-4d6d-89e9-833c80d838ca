{"ast": null, "code": "import { g as getDocument, a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren } from '../shared/utils.mjs';\nfunction HashNavigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    on\n  } = _ref;\n  let initialized = false;\n  const document = getDocument();\n  const window = getWindow();\n  extendParams({\n    hashNavigation: {\n      enabled: false,\n      replaceState: false,\n      watchState: false,\n      getSlideIndex(_s, hash) {\n        if (swiper.virtual && swiper.params.virtual.enabled) {\n          const slideWithHash = swiper.slides.filter(slideEl => slideEl.getAttribute('data-hash') === hash)[0];\n          if (!slideWithHash) return 0;\n          const index = parseInt(slideWithHash.getAttribute('data-swiper-slide-index'), 10);\n          return index;\n        }\n        return swiper.getSlideIndex(elementChildren(swiper.slidesEl, `.${swiper.params.slideClass}[data-hash=\"${hash}\"], swiper-slide[data-hash=\"${hash}\"]`)[0]);\n      }\n    }\n  });\n  const onHashChange = () => {\n    emit('hashChange');\n    const newHash = document.location.hash.replace('#', '');\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') : '';\n    if (newHash !== activeSlideHash) {\n      const newIndex = swiper.params.hashNavigation.getSlideIndex(swiper, newHash);\n      if (typeof newIndex === 'undefined' || Number.isNaN(newIndex)) return;\n      swiper.slideTo(newIndex);\n    }\n  };\n  const setHash = () => {\n    if (!initialized || !swiper.params.hashNavigation.enabled) return;\n    const activeSlideEl = swiper.virtual && swiper.params.virtual.enabled ? swiper.slidesEl.querySelector(`[data-swiper-slide-index=\"${swiper.activeIndex}\"]`) : swiper.slides[swiper.activeIndex];\n    const activeSlideHash = activeSlideEl ? activeSlideEl.getAttribute('data-hash') || activeSlideEl.getAttribute('data-history') : '';\n    if (swiper.params.hashNavigation.replaceState && window.history && window.history.replaceState) {\n      window.history.replaceState(null, null, `#${activeSlideHash}` || '');\n      emit('hashSet');\n    } else {\n      document.location.hash = activeSlideHash || '';\n      emit('hashSet');\n    }\n  };\n  const init = () => {\n    if (!swiper.params.hashNavigation.enabled || swiper.params.history && swiper.params.history.enabled) return;\n    initialized = true;\n    const hash = document.location.hash.replace('#', '');\n    if (hash) {\n      const speed = 0;\n      const index = swiper.params.hashNavigation.getSlideIndex(swiper, hash);\n      swiper.slideTo(index || 0, speed, swiper.params.runCallbacksOnInit, true);\n    }\n    if (swiper.params.hashNavigation.watchState) {\n      window.addEventListener('hashchange', onHashChange);\n    }\n  };\n  const destroy = () => {\n    if (swiper.params.hashNavigation.watchState) {\n      window.removeEventListener('hashchange', onHashChange);\n    }\n  };\n  on('init', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      init();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.params.hashNavigation.enabled) {\n      destroy();\n    }\n  });\n  on('transitionEnd _freeModeNoMomentumRelease', () => {\n    if (initialized) {\n      setHash();\n    }\n  });\n  on('slideChange', () => {\n    if (initialized && swiper.params.cssMode) {\n      setHash();\n    }\n  });\n}\nexport { HashNavigation as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}