{"ast": null, "code": "import { TranslateModule } from \"@ngx-translate/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ngx-translate/core\";\nexport class BackButtonComponent {\n  constructor(_location, router) {\n    this._location = _location;\n    this.router = router;\n    this.navigationUrl = '';\n    this.backText = '';\n  }\n  goBack() {\n    this.navigationUrl ? this.router.navigate([this.navigationUrl]) : this._location.back();\n  }\n  static #_ = this.ɵfac = function BackButtonComponent_Factory(t) {\n    return new (t || BackButtonComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BackButtonComponent,\n    selectors: [[\"app-back-button\"]],\n    inputs: {\n      navigationUrl: \"navigationUrl\",\n      backText: \"backText\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 5,\n    vars: 3,\n    consts: [[1, \"back-btn\", 3, \"click\"], [\"alt\", \"back-icon\", \"src\", \"assets/icons/mobile-icons/back-icon.svg\"]],\n    template: function BackButtonComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵlistener(\"click\", function BackButtonComponent_Template_button_click_0_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelement(1, \"img\", 1);\n        i0.ɵɵelementStart(2, \"span\");\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, ctx.backText ? ctx.backText : \"buttons.back\"));\n      }\n    },\n    dependencies: [TranslateModule, i3.TranslatePipe],\n    styles: [\".back-btn[_ngcontent-%COMP%] {\\n  background-color: #F6F6F6;\\n  padding: 16px 25px;\\n  position: absolute;\\n  top: 74px;\\n  width: 100%;\\n  display: flex;\\n  flex-direction: row;\\n  gap: 7px;\\n  outline: none;\\n  border: none;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n.back-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 24px;\\n  font-family: var(--medium-font);\\n  color: #2F3036;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvYmFjay1idXR0b24vYmFjay1idXR0b24uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0o7QUFBSTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQkFBQTtFQUNBLGNBQUE7QUFFTiIsInNvdXJjZXNDb250ZW50IjpbIi5iYWNrLWJ0bntcclxuICAgIGJhY2tncm91bmQtY29sb3I6I0Y2RjZGNjtcclxuICAgIHBhZGRpbmc6IDE2cHggMjVweDtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogNzRweDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XHJcbiAgICBnYXA6N3B4O1xyXG4gICAgb3V0bGluZTogbm9uZTtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHNwYW57XHJcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgICAgZm9udC1zdHlsZTogbm9ybWFsO1xyXG4gICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICBsaW5lLWhlaWdodDogMjRweDtcclxuICAgICAgZm9udC1mYW1pbHk6IHZhcigtLW1lZGl1bS1mb250KTtcclxuICAgICAgY29sb3I6ICMyRjMwMzY7XHJcbiAgICB9XHJcbiAgfVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["TranslateModule", "BackButtonComponent", "constructor", "_location", "router", "navigationUrl", "backText", "goBack", "navigate", "back", "_", "i0", "ɵɵdirectiveInject", "i1", "Location", "i2", "Router", "_2", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BackButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "BackButtonComponent_Template_button_click_0_listener", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "i3", "TranslatePipe", "styles"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\back-button\\back-button.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\back-button\\back-button.component.html"], "sourcesContent": ["import { Component, Input } from \"@angular/core\";\r\nimport { Location } from \"@angular/common\";\r\nimport { Router } from \"@angular/router\";\r\nimport { TranslateModule } from \"@ngx-translate/core\";\r\n@Component({\r\n    selector: 'app-back-button',\r\n    templateUrl: './back-button.component.html',\r\n    styleUrls: ['./back-button.component.scss'],\r\n    standalone: true,\r\n   imports: [\r\n    TranslateModule\r\n  ],\r\n  })\r\n\r\n  export class BackButtonComponent {\r\n    @Input() navigationUrl?:string ='';\r\n    @Input() backText?:string ='';\r\n  constructor( private _location: Location,\r\n    private router:Router ){\r\n\r\n  }\r\n  goBack() {\r\n    this.navigationUrl ? this.router.navigate([this.navigationUrl]):this._location.back()\r\n    }\r\n\r\n  }\r\n", "<button class=\"back-btn\" (click)=\"goBack()\">\r\n    <img alt=\"back-icon\" src=\"assets/icons/mobile-icons/back-icon.svg\">\r\n    <span>{{ (backText ? backText :'buttons.back' )| translate}}</span>\r\n  </button>"], "mappings": "AAGA,SAASA,eAAe,QAAQ,qBAAqB;;;;;AAWnD,OAAM,MAAOC,mBAAmB;EAGhCC,YAAqBC,SAAmB,EAC9BC,MAAa;IADF,KAAAD,SAAS,GAATA,SAAS;IACpB,KAAAC,MAAM,GAANA,MAAM;IAHL,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,QAAQ,GAAU,EAAE;EAI/B;EACAC,MAAMA,CAAA;IACJ,IAAI,CAACF,aAAa,GAAG,IAAI,CAACD,MAAM,CAACI,QAAQ,CAAC,CAAC,IAAI,CAACH,aAAa,CAAC,CAAC,GAAC,IAAI,CAACF,SAAS,CAACM,IAAI,EAAE;EACrF;EAAC,QAAAC,CAAA,G;qBATUT,mBAAmB,EAAAU,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBhB,mBAAmB;IAAAiB,SAAA;IAAAC,MAAA;MAAAd,aAAA;MAAAC,QAAA;IAAA;IAAAc,UAAA;IAAAC,QAAA,GAAAV,EAAA,CAAAW,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdlCjB,EAAA,CAAAmB,cAAA,gBAA4C;QAAnBnB,EAAA,CAAAoB,UAAA,mBAAAC,qDAAA;UAAA,OAASH,GAAA,CAAAtB,MAAA,EAAQ;QAAA,EAAC;QACvCI,EAAA,CAAAsB,SAAA,aAAmE;QACnEtB,EAAA,CAAAmB,cAAA,WAAM;QAAAnB,EAAA,CAAAuB,MAAA,GAAsD;;QAAAvB,EAAA,CAAAwB,YAAA,EAAO;;;QAA7DxB,EAAA,CAAAyB,SAAA,GAAsD;QAAtDzB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA2B,WAAA,OAAAT,GAAA,CAAAvB,QAAA,GAAAuB,GAAA,CAAAvB,QAAA,mBAAsD;;;mBDQ5DN,eAAe,EAAAuC,EAAA,CAAAC,aAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}