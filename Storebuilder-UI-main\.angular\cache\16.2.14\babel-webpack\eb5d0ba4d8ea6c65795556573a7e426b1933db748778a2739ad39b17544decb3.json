{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DetailsService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiEndPoint;\n  }\n  wishlistToggle({\n    specsProductId,\n    productId,\n    channelId,\n    flag\n  }) {\n    if (flag) {\n      this.likeUnlike = 'DeleteCustomerWishList';\n    } else {\n      this.likeUnlike = 'CreateCustomerWishList';\n    }\n    if (channelId == 2) {\n      specsProductId = 0;\n    }\n    return this.http.post(`${this.baseUrl}/Product/CustomerWishList/${this.likeUnlike}`, {\n      specsProductId,\n      productId,\n      channelId\n    });\n  }\n}\nDetailsService.ɵfac = function DetailsService_Factory(t) {\n  return new (t || DetailsService)(i0.ɵɵinject(i1.HttpClient));\n};\nDetailsService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: DetailsService,\n  factory: DetailsService.ɵfac,\n  providedIn: 'root'\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}