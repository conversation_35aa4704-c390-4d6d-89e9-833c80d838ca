{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\nclass TranslateLoader {}\n/**\r\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\r\n */\nclass TranslateFakeLoader extends TranslateLoader {\n  getTranslation(lang) {\n    return of({});\n  }\n}\nTranslateFakeLoader.ɵfac = /* @__PURE__ */function () {\n  let ɵTranslateFakeLoader_BaseFactory;\n  return function TranslateFakeLoader_Factory(t) {\n    return (ɵTranslateFakeLoader_BaseFactory || (ɵTranslateFakeLoader_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeLoader)))(t || TranslateFakeLoader);\n  };\n}();\nTranslateFakeLoader.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TranslateFakeLoader,\n  factory: TranslateFakeLoader.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeLoader, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MissingTranslationHandler {}\n/**\r\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\r\n */\nclass FakeMissingTranslationHandler {\n  handle(params) {\n    return params.key;\n  }\n}\nFakeMissingTranslationHandler.ɵfac = function FakeMissingTranslationHandler_Factory(t) {\n  return new (t || FakeMissingTranslationHandler)();\n};\nFakeMissingTranslationHandler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FakeMissingTranslationHandler,\n  factory: FakeMissingTranslationHandler.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeMissingTranslationHandler, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/* tslint:disable */\n/**\r\n * Determines if two objects or two values are equivalent.\r\n *\r\n * Two objects or values are considered equivalent if at least one of the following is true:\r\n *\r\n * * Both objects or values pass `===` comparison.\r\n * * Both objects or values are of the same type and all of their properties are equal by\r\n *   comparing them with `equals`.\r\n *\r\n * @param o1 Object or value to compare.\r\n * @param o2 Object or value to compare.\r\n * @returns true if arguments are equal.\r\n */\nfunction equals(o1, o2) {\n  if (o1 === o2) return true;\n  if (o1 === null || o2 === null) return false;\n  if (o1 !== o1 && o2 !== o2) return true; // NaN === NaN\n  let t1 = typeof o1,\n    t2 = typeof o2,\n    length,\n    key,\n    keySet;\n  if (t1 == t2 && t1 == 'object') {\n    if (Array.isArray(o1)) {\n      if (!Array.isArray(o2)) return false;\n      if ((length = o1.length) == o2.length) {\n        for (key = 0; key < length; key++) {\n          if (!equals(o1[key], o2[key])) return false;\n        }\n        return true;\n      }\n    } else {\n      if (Array.isArray(o2)) {\n        return false;\n      }\n      keySet = Object.create(null);\n      for (key in o1) {\n        if (!equals(o1[key], o2[key])) {\n          return false;\n        }\n        keySet[key] = true;\n      }\n      for (key in o2) {\n        if (!(key in keySet) && typeof o2[key] !== 'undefined') {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n}\n/* tslint:enable */\nfunction isDefined(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction mergeDeep(target, source) {\n  let output = Object.assign({}, target);\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isObject(source[key])) {\n        if (!(key in target)) {\n          Object.assign(output, {\n            [key]: source[key]\n          });\n        } else {\n          output[key] = mergeDeep(target[key], source[key]);\n        }\n      } else {\n        Object.assign(output, {\n          [key]: source[key]\n        });\n      }\n    });\n  }\n  return output;\n}\nclass TranslateParser {}\nclass TranslateDefaultParser extends TranslateParser {\n  constructor() {\n    super(...arguments);\n    this.templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\n  }\n  interpolate(expr, params) {\n    let result;\n    if (typeof expr === 'string') {\n      result = this.interpolateString(expr, params);\n    } else if (typeof expr === 'function') {\n      result = this.interpolateFunction(expr, params);\n    } else {\n      // this should not happen, but an unrelated TranslateService test depends on it\n      result = expr;\n    }\n    return result;\n  }\n  getValue(target, key) {\n    let keys = typeof key === 'string' ? key.split('.') : [key];\n    key = '';\n    do {\n      key += keys.shift();\n      if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\n        target = target[key];\n        key = '';\n      } else if (!keys.length) {\n        target = undefined;\n      } else {\n        key += '.';\n      }\n    } while (keys.length);\n    return target;\n  }\n  interpolateFunction(fn, params) {\n    return fn(params);\n  }\n  interpolateString(expr, params) {\n    if (!params) {\n      return expr;\n    }\n    return expr.replace(this.templateMatcher, (substring, b) => {\n      let r = this.getValue(params, b);\n      return isDefined(r) ? r : substring;\n    });\n  }\n}\nTranslateDefaultParser.ɵfac = /* @__PURE__ */function () {\n  let ɵTranslateDefaultParser_BaseFactory;\n  return function TranslateDefaultParser_Factory(t) {\n    return (ɵTranslateDefaultParser_BaseFactory || (ɵTranslateDefaultParser_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateDefaultParser)))(t || TranslateDefaultParser);\n  };\n}();\nTranslateDefaultParser.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TranslateDefaultParser,\n  factory: TranslateDefaultParser.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDefaultParser, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateCompiler {}\n/**\r\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\r\n */\nclass TranslateFakeCompiler extends TranslateCompiler {\n  compile(value, lang) {\n    return value;\n  }\n  compileTranslations(translations, lang) {\n    return translations;\n  }\n}\nTranslateFakeCompiler.ɵfac = /* @__PURE__ */function () {\n  let ɵTranslateFakeCompiler_BaseFactory;\n  return function TranslateFakeCompiler_Factory(t) {\n    return (ɵTranslateFakeCompiler_BaseFactory || (ɵTranslateFakeCompiler_BaseFactory = i0.ɵɵgetInheritedFactory(TranslateFakeCompiler)))(t || TranslateFakeCompiler);\n  };\n}();\nTranslateFakeCompiler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TranslateFakeCompiler,\n  factory: TranslateFakeCompiler.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateFakeCompiler, [{\n    type: Injectable\n  }], null, null);\n})();\nclass TranslateStore {\n  constructor() {\n    /**\r\n     * The lang currently used\r\n     */\n    this.currentLang = this.defaultLang;\n    /**\r\n     * a list of translations per lang\r\n     */\n    this.translations = {};\n    /**\r\n     * an array of langs\r\n     */\n    this.langs = [];\n    /**\r\n     * An EventEmitter to listen to translation change events\r\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\n    this.onTranslationChange = new EventEmitter();\n    /**\r\n     * An EventEmitter to listen to lang change events\r\n     * onLangChange.subscribe((params: LangChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\n    this.onLangChange = new EventEmitter();\n    /**\r\n     * An EventEmitter to listen to default lang change events\r\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\n    this.onDefaultLangChange = new EventEmitter();\n  }\n}\nconst USE_STORE = new InjectionToken('USE_STORE');\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\nclass TranslateService {\n  /**\r\n   *\r\n   * @param store an instance of the store (that is supposed to be unique)\r\n   * @param currentLoader An instance of the loader currently used\r\n   * @param compiler An instance of the compiler currently used\r\n   * @param parser An instance of the parser currently used\r\n   * @param missingTranslationHandler A handler for missing translations.\r\n   * @param useDefaultLang whether we should use default language translation when current language translation is missing.\r\n   * @param isolate whether this service should use the store or not\r\n   * @param extend To make a child module extend (and use) translations from parent modules.\r\n   * @param defaultLanguage Set the default language using configuration\r\n   */\n  constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\n    this.store = store;\n    this.currentLoader = currentLoader;\n    this.compiler = compiler;\n    this.parser = parser;\n    this.missingTranslationHandler = missingTranslationHandler;\n    this.useDefaultLang = useDefaultLang;\n    this.isolate = isolate;\n    this.extend = extend;\n    this.pending = false;\n    this._onTranslationChange = new EventEmitter();\n    this._onLangChange = new EventEmitter();\n    this._onDefaultLangChange = new EventEmitter();\n    this._langs = [];\n    this._translations = {};\n    this._translationRequests = {};\n    /** set the default language from configuration */\n    if (defaultLanguage) {\n      this.setDefaultLang(defaultLanguage);\n    }\n  }\n  /**\r\n   * An EventEmitter to listen to translation change events\r\n   * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\r\n     *     // do something\r\n     * });\r\n   */\n  get onTranslationChange() {\n    return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\n  }\n  /**\r\n   * An EventEmitter to listen to lang change events\r\n   * onLangChange.subscribe((params: LangChangeEvent) => {\r\n     *     // do something\r\n     * });\r\n   */\n  get onLangChange() {\n    return this.isolate ? this._onLangChange : this.store.onLangChange;\n  }\n  /**\r\n   * An EventEmitter to listen to default lang change events\r\n   * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\r\n     *     // do something\r\n     * });\r\n   */\n  get onDefaultLangChange() {\n    return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\n  }\n  /**\r\n   * The default lang to fallback when translations are missing on the current lang\r\n   */\n  get defaultLang() {\n    return this.isolate ? this._defaultLang : this.store.defaultLang;\n  }\n  set defaultLang(defaultLang) {\n    if (this.isolate) {\n      this._defaultLang = defaultLang;\n    } else {\n      this.store.defaultLang = defaultLang;\n    }\n  }\n  /**\r\n   * The lang currently used\r\n   */\n  get currentLang() {\n    return this.isolate ? this._currentLang : this.store.currentLang;\n  }\n  set currentLang(currentLang) {\n    if (this.isolate) {\n      this._currentLang = currentLang;\n    } else {\n      this.store.currentLang = currentLang;\n    }\n  }\n  /**\r\n   * an array of langs\r\n   */\n  get langs() {\n    return this.isolate ? this._langs : this.store.langs;\n  }\n  set langs(langs) {\n    if (this.isolate) {\n      this._langs = langs;\n    } else {\n      this.store.langs = langs;\n    }\n  }\n  /**\r\n   * a list of translations per lang\r\n   */\n  get translations() {\n    return this.isolate ? this._translations : this.store.translations;\n  }\n  set translations(translations) {\n    if (this.isolate) {\n      this._translations = translations;\n    } else {\n      this.store.translations = translations;\n    }\n  }\n  /**\r\n   * Sets the default language to use as a fallback\r\n   */\n  setDefaultLang(lang) {\n    if (lang === this.defaultLang) {\n      return;\n    }\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the defaultLang immediately\n      if (this.defaultLang == null) {\n        this.defaultLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(res => {\n        this.changeDefaultLang(lang);\n      });\n    } else {\n      // we already have this language\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\r\n   * Gets the default language used\r\n   */\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  /**\r\n   * Changes the lang currently used\r\n   */\n  use(lang) {\n    // don't change the language if the language given is already selected\n    if (lang === this.currentLang) {\n      return of(this.translations[lang]);\n    }\n    let pending = this.retrieveTranslations(lang);\n    if (typeof pending !== \"undefined\") {\n      // on init set the currentLang immediately\n      if (!this.currentLang) {\n        this.currentLang = lang;\n      }\n      pending.pipe(take(1)).subscribe(res => {\n        this.changeLang(lang);\n      });\n      return pending;\n    } else {\n      // we have this language, return an Observable\n      this.changeLang(lang);\n      return of(this.translations[lang]);\n    }\n  }\n  /**\r\n   * Retrieves the given translations\r\n   */\n  retrieveTranslations(lang) {\n    let pending;\n    // if this language is unavailable or extend is true, ask for it\n    if (typeof this.translations[lang] === \"undefined\" || this.extend) {\n      this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\n      pending = this._translationRequests[lang];\n    }\n    return pending;\n  }\n  /**\r\n   * Gets an object of translations for a given language with the current loader\r\n   * and passes it through the compiler\r\n   */\n  getTranslation(lang) {\n    this.pending = true;\n    const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\n    this.loadingTranslations = loadingTranslations.pipe(map(res => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\n    this.loadingTranslations.subscribe({\n      next: res => {\n        this.translations[lang] = this.extend && this.translations[lang] ? {\n          ...res,\n          ...this.translations[lang]\n        } : res;\n        this.updateLangs();\n        this.pending = false;\n      },\n      error: err => {\n        this.pending = false;\n      }\n    });\n    return loadingTranslations;\n  }\n  /**\r\n   * Manually sets an object of translations for a given language\r\n   * after passing it through the compiler\r\n   */\n  setTranslation(lang, translations, shouldMerge = false) {\n    translations = this.compiler.compileTranslations(translations, lang);\n    if ((shouldMerge || this.extend) && this.translations[lang]) {\n      this.translations[lang] = mergeDeep(this.translations[lang], translations);\n    } else {\n      this.translations[lang] = translations;\n    }\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\r\n   * Returns an array of currently available langs\r\n   */\n  getLangs() {\n    return this.langs;\n  }\n  /**\r\n   * Add available langs\r\n   */\n  addLangs(langs) {\n    langs.forEach(lang => {\n      if (this.langs.indexOf(lang) === -1) {\n        this.langs.push(lang);\n      }\n    });\n  }\n  /**\r\n   * Update the list of available langs\r\n   */\n  updateLangs() {\n    this.addLangs(Object.keys(this.translations));\n  }\n  /**\r\n   * Returns the parsed result of the translations\r\n   */\n  getParsedResult(translations, key, interpolateParams) {\n    let res;\n    if (key instanceof Array) {\n      let result = {},\n        observables = false;\n      for (let k of key) {\n        result[k] = this.getParsedResult(translations, k, interpolateParams);\n        if (isObservable(result[k])) {\n          observables = true;\n        }\n      }\n      if (observables) {\n        const sources = key.map(k => isObservable(result[k]) ? result[k] : of(result[k]));\n        return forkJoin(sources).pipe(map(arr => {\n          let obj = {};\n          arr.forEach((value, index) => {\n            obj[key[index]] = value;\n          });\n          return obj;\n        }));\n      }\n      return result;\n    }\n    if (translations) {\n      res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\n    }\n    if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\n      res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\n    }\n    if (typeof res === \"undefined\") {\n      let params = {\n        key,\n        translateService: this\n      };\n      if (typeof interpolateParams !== 'undefined') {\n        params.interpolateParams = interpolateParams;\n      }\n      res = this.missingTranslationHandler.handle(params);\n    }\n    return typeof res !== \"undefined\" ? res : key;\n  }\n  /**\r\n   * Gets the translated value of a key (or an array of keys)\r\n   * @returns the translated key, or an object of translated keys\r\n   */\n  get(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    // check if we are loading a new translation to use\n    if (this.pending) {\n      return this.loadingTranslations.pipe(concatMap(res => {\n        res = this.getParsedResult(res, key, interpolateParams);\n        return isObservable(res) ? res : of(res);\n      }));\n    } else {\n      let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    }\n  }\n  /**\r\n   * Returns a stream of translated values of a key (or an array of keys) which updates\r\n   * whenever the translation changes.\r\n   * @returns A stream of the translated key, or an object of translated keys\r\n   */\n  getStreamOnTranslationChange(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      if (typeof res.subscribe === 'function') {\n        return res;\n      } else {\n        return of(res);\n      }\n    })));\n  }\n  /**\r\n   * Returns a stream of translated values of a key (or an array of keys) which updates\r\n   * whenever the language changes.\r\n   * @returns A stream of the translated key, or an object of translated keys\r\n   */\n  stream(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap(event => {\n      const res = this.getParsedResult(event.translations, key, interpolateParams);\n      return isObservable(res) ? res : of(res);\n    })));\n  }\n  /**\r\n   * Returns a translation instantly from the internal state of loaded translation.\r\n   * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\r\n   */\n  instant(key, interpolateParams) {\n    if (!isDefined(key) || !key.length) {\n      throw new Error(`Parameter \"key\" required`);\n    }\n    let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\n    if (isObservable(res)) {\n      if (key instanceof Array) {\n        let obj = {};\n        key.forEach((value, index) => {\n          obj[key[index]] = key[index];\n        });\n        return obj;\n      }\n      return key;\n    } else {\n      return res;\n    }\n  }\n  /**\r\n   * Sets the translated value of a key, after compiling it\r\n   */\n  set(key, value, lang = this.currentLang) {\n    this.translations[lang][key] = this.compiler.compile(value, lang);\n    this.updateLangs();\n    this.onTranslationChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\r\n   * Changes the current lang\r\n   */\n  changeLang(lang) {\n    this.currentLang = lang;\n    this.onLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n    // if there is no default lang, use the one that we just set\n    if (this.defaultLang == null) {\n      this.changeDefaultLang(lang);\n    }\n  }\n  /**\r\n   * Changes the default lang\r\n   */\n  changeDefaultLang(lang) {\n    this.defaultLang = lang;\n    this.onDefaultLangChange.emit({\n      lang: lang,\n      translations: this.translations[lang]\n    });\n  }\n  /**\r\n   * Allows to reload the lang file from the file\r\n   */\n  reloadLang(lang) {\n    this.resetLang(lang);\n    return this.getTranslation(lang);\n  }\n  /**\r\n   * Deletes inner translation\r\n   */\n  resetLang(lang) {\n    this._translationRequests[lang] = undefined;\n    this.translations[lang] = undefined;\n  }\n  /**\r\n   * Returns the language code name from the browser, e.g. \"de\"\r\n   */\n  getBrowserLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    if (typeof browserLang === 'undefined') {\n      return undefined;\n    }\n    if (browserLang.indexOf('-') !== -1) {\n      browserLang = browserLang.split('-')[0];\n    }\n    if (browserLang.indexOf('_') !== -1) {\n      browserLang = browserLang.split('_')[0];\n    }\n    return browserLang;\n  }\n  /**\r\n   * Returns the culture language code name from the browser, e.g. \"de-DE\"\r\n   */\n  getBrowserCultureLang() {\n    if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\n      return undefined;\n    }\n    let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\n    browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\n    return browserCultureLang;\n  }\n}\nTranslateService.ɵfac = function TranslateService_Factory(t) {\n  return new (t || TranslateService)(i0.ɵɵinject(TranslateStore), i0.ɵɵinject(TranslateLoader), i0.ɵɵinject(TranslateCompiler), i0.ɵɵinject(TranslateParser), i0.ɵɵinject(MissingTranslationHandler), i0.ɵɵinject(USE_DEFAULT_LANG), i0.ɵɵinject(USE_STORE), i0.ɵɵinject(USE_EXTEND), i0.ɵɵinject(DEFAULT_LANGUAGE));\n};\nTranslateService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TranslateService,\n  factory: TranslateService.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: TranslateStore\n    }, {\n      type: TranslateLoader\n    }, {\n      type: TranslateCompiler\n    }, {\n      type: TranslateParser\n    }, {\n      type: MissingTranslationHandler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_DEFAULT_LANG]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_STORE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [USE_EXTEND]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DEFAULT_LANGUAGE]\n      }]\n    }];\n  }, null);\n})();\nclass TranslateDirective {\n  constructor(translateService, element, _ref) {\n    this.translateService = translateService;\n    this.element = element;\n    this._ref = _ref;\n    // subscribe to onTranslationChange event, in case the translations of the current lang change\n    if (!this.onTranslationChangeSub) {\n      this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe(event => {\n        if (event.lang === this.translateService.currentLang) {\n          this.checkNodes(true, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChangeSub) {\n      this.onLangChangeSub = this.translateService.onLangChange.subscribe(event => {\n        this.checkNodes(true, event.translations);\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe(event => {\n        this.checkNodes(true);\n      });\n    }\n  }\n  set translate(key) {\n    if (key) {\n      this.key = key;\n      this.checkNodes();\n    }\n  }\n  set translateParams(params) {\n    if (!equals(this.currentParams, params)) {\n      this.currentParams = params;\n      this.checkNodes(true);\n    }\n  }\n  ngAfterViewChecked() {\n    this.checkNodes();\n  }\n  checkNodes(forceUpdate = false, translations) {\n    let nodes = this.element.nativeElement.childNodes;\n    // if the element is empty\n    if (!nodes.length) {\n      // we add the key as content\n      this.setContent(this.element.nativeElement, this.key);\n      nodes = this.element.nativeElement.childNodes;\n    }\n    for (let i = 0; i < nodes.length; ++i) {\n      let node = nodes[i];\n      if (node.nodeType === 3) {\n        // node type 3 is a text node\n        let key;\n        if (forceUpdate) {\n          node.lastKey = null;\n        }\n        if (isDefined(node.lookupKey)) {\n          key = node.lookupKey;\n        } else if (this.key) {\n          key = this.key;\n        } else {\n          let content = this.getContent(node);\n          let trimmedContent = content.trim();\n          if (trimmedContent.length) {\n            node.lookupKey = trimmedContent;\n            // we want to use the content as a key, not the translation value\n            if (content !== node.currentValue) {\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            } else if (node.originalContent) {\n              // the content seems ok, but the lang has changed\n              // the current content is the translation, not the key, use the last real content as key\n              key = node.originalContent.trim();\n            } else if (content !== node.currentValue) {\n              // we want to use the content as a key, not the translation value\n              key = trimmedContent;\n              // the content was changed from the user, we'll use it as a reference if needed\n              node.originalContent = content || node.originalContent;\n            }\n          }\n        }\n        this.updateValue(key, node, translations);\n      }\n    }\n  }\n  updateValue(key, node, translations) {\n    if (key) {\n      if (node.lastKey === key && this.lastParams === this.currentParams) {\n        return;\n      }\n      this.lastParams = this.currentParams;\n      let onTranslation = res => {\n        if (res !== key) {\n          node.lastKey = key;\n        }\n        if (!node.originalContent) {\n          node.originalContent = this.getContent(node);\n        }\n        node.currentValue = isDefined(res) ? res : node.originalContent || key;\n        // we replace in the original content to preserve spaces that we might have trimmed\n        this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\n        this._ref.markForCheck();\n      };\n      if (isDefined(translations)) {\n        let res = this.translateService.getParsedResult(translations, key, this.currentParams);\n        if (isObservable(res)) {\n          res.subscribe({\n            next: onTranslation\n          });\n        } else {\n          onTranslation(res);\n        }\n      } else {\n        this.translateService.get(key, this.currentParams).subscribe(onTranslation);\n      }\n    }\n  }\n  getContent(node) {\n    return isDefined(node.textContent) ? node.textContent : node.data;\n  }\n  setContent(node, content) {\n    if (isDefined(node.textContent)) {\n      node.textContent = content;\n    } else {\n      node.data = content;\n    }\n  }\n  ngOnDestroy() {\n    if (this.onLangChangeSub) {\n      this.onLangChangeSub.unsubscribe();\n    }\n    if (this.onDefaultLangChangeSub) {\n      this.onDefaultLangChangeSub.unsubscribe();\n    }\n    if (this.onTranslationChangeSub) {\n      this.onTranslationChangeSub.unsubscribe();\n    }\n  }\n}\nTranslateDirective.ɵfac = function TranslateDirective_Factory(t) {\n  return new (t || TranslateDirective)(i0.ɵɵdirectiveInject(TranslateService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nTranslateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TranslateDirective,\n  selectors: [[\"\", \"translate\", \"\"], [\"\", \"ngx-translate\", \"\"]],\n  inputs: {\n    translate: \"translate\",\n    translateParams: \"translateParams\"\n  }\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[translate],[ngx-translate]'\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    translate: [{\n      type: Input\n    }],\n    translateParams: [{\n      type: Input\n    }]\n  });\n})();\nclass TranslatePipe {\n  constructor(translate, _ref) {\n    this.translate = translate;\n    this._ref = _ref;\n    this.value = '';\n    this.lastKey = null;\n    this.lastParams = [];\n  }\n  updateValue(key, interpolateParams, translations) {\n    let onTranslation = res => {\n      this.value = res !== undefined ? res : key;\n      this.lastKey = key;\n      this._ref.markForCheck();\n    };\n    if (translations) {\n      let res = this.translate.getParsedResult(translations, key, interpolateParams);\n      if (isObservable(res.subscribe)) {\n        res.subscribe(onTranslation);\n      } else {\n        onTranslation(res);\n      }\n    }\n    this.translate.get(key, interpolateParams).subscribe(onTranslation);\n  }\n  transform(query, ...args) {\n    if (!query || !query.length) {\n      return query;\n    }\n    // if we ask another time for the same key, return the last value\n    if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\n      return this.value;\n    }\n    let interpolateParams = undefined;\n    if (isDefined(args[0]) && args.length) {\n      if (typeof args[0] === 'string' && args[0].length) {\n        // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\n        // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\n        let validArgs = args[0].replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":').replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\n        try {\n          interpolateParams = JSON.parse(validArgs);\n        } catch (e) {\n          throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\n        }\n      } else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\n        interpolateParams = args[0];\n      }\n    }\n    // store the query, in case it changes\n    this.lastKey = query;\n    // store the params, in case they change\n    this.lastParams = args;\n    // set the value\n    this.updateValue(query, interpolateParams);\n    // if there is a subscription to onLangChange, clean it\n    this._dispose();\n    // subscribe to onTranslationChange event, in case the translations change\n    if (!this.onTranslationChange) {\n      this.onTranslationChange = this.translate.onTranslationChange.subscribe(event => {\n        if (this.lastKey && event.lang === this.translate.currentLang) {\n          this.lastKey = null;\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onLangChange event, in case the language changes\n    if (!this.onLangChange) {\n      this.onLangChange = this.translate.onLangChange.subscribe(event => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams, event.translations);\n        }\n      });\n    }\n    // subscribe to onDefaultLangChange event, in case the default language changes\n    if (!this.onDefaultLangChange) {\n      this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\n        if (this.lastKey) {\n          this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\n          this.updateValue(query, interpolateParams);\n        }\n      });\n    }\n    return this.value;\n  }\n  /**\r\n   * Clean any existing subscription to change events\r\n   */\n  _dispose() {\n    if (typeof this.onTranslationChange !== 'undefined') {\n      this.onTranslationChange.unsubscribe();\n      this.onTranslationChange = undefined;\n    }\n    if (typeof this.onLangChange !== 'undefined') {\n      this.onLangChange.unsubscribe();\n      this.onLangChange = undefined;\n    }\n    if (typeof this.onDefaultLangChange !== 'undefined') {\n      this.onDefaultLangChange.unsubscribe();\n      this.onDefaultLangChange = undefined;\n    }\n  }\n  ngOnDestroy() {\n    this._dispose();\n  }\n}\nTranslatePipe.ɵfac = function TranslatePipe_Factory(t) {\n  return new (t || TranslatePipe)(i0.ɵɵdirectiveInject(TranslateService, 16), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n};\nTranslatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"translate\",\n  type: TranslatePipe,\n  pure: false\n});\nTranslatePipe.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TranslatePipe,\n  factory: TranslatePipe.ɵfac\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslatePipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'translate',\n      pure: false // required to update the value when the promise is resolved\n    }]\n  }], function () {\n    return [{\n      type: TranslateService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\nclass TranslateModule {\n  /**\r\n   * Use this method in your root module to provide the TranslateService\r\n   */\n  static forRoot(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, TranslateStore, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n  /**\r\n   * Use this method in your other (non root) modules to import the directive/pipe\r\n   */\n  static forChild(config = {}) {\n    return {\n      ngModule: TranslateModule,\n      providers: [config.loader || {\n        provide: TranslateLoader,\n        useClass: TranslateFakeLoader\n      }, config.compiler || {\n        provide: TranslateCompiler,\n        useClass: TranslateFakeCompiler\n      }, config.parser || {\n        provide: TranslateParser,\n        useClass: TranslateDefaultParser\n      }, config.missingTranslationHandler || {\n        provide: MissingTranslationHandler,\n        useClass: FakeMissingTranslationHandler\n      }, {\n        provide: USE_STORE,\n        useValue: config.isolate\n      }, {\n        provide: USE_DEFAULT_LANG,\n        useValue: config.useDefaultLang\n      }, {\n        provide: USE_EXTEND,\n        useValue: config.extend\n      }, {\n        provide: DEFAULT_LANGUAGE,\n        useValue: config.defaultLanguage\n      }, TranslateService]\n    };\n  }\n}\nTranslateModule.ɵfac = function TranslateModule_Factory(t) {\n  return new (t || TranslateModule)();\n};\nTranslateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TranslateModule\n});\nTranslateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslateModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [TranslatePipe, TranslateDirective],\n      exports: [TranslatePipe, TranslateDirective]\n    }]\n  }], null, null);\n})();\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "InjectionToken", "Inject", "Directive", "Input", "<PERSON><PERSON>", "NgModule", "of", "isObservable", "fork<PERSON><PERSON>n", "concat", "defer", "take", "shareReplay", "map", "concatMap", "switchMap", "Translate<PERSON><PERSON><PERSON>", "TranslateFakeLoader", "getTranslation", "lang", "ɵfac", "ɵTranslateFakeLoader_BaseFactory", "TranslateFakeLoader_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "MissingTranslationHandler", "FakeMissingTranslationHandler", "handle", "params", "key", "FakeMissingTranslationHandler_Factory", "equals", "o1", "o2", "t1", "t2", "length", "keySet", "Array", "isArray", "Object", "create", "isDefined", "value", "isObject", "item", "mergeDeep", "target", "source", "output", "assign", "keys", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TranslateDefault<PERSON><PERSON><PERSON>", "constructor", "arguments", "templateMatcher", "interpolate", "expr", "result", "interpolateString", "interpolateFunction", "getValue", "split", "shift", "undefined", "fn", "replace", "substring", "b", "r", "ɵTranslateDefaultParser_BaseFactory", "TranslateDefaultParser_Factory", "TranslateCompiler", "TranslateFakeCompiler", "compile", "compileTranslations", "translations", "ɵTranslateFakeCompiler_BaseFactory", "TranslateFakeCompiler_Factory", "TranslateStore", "currentLang", "defaultLang", "langs", "onTranslationChange", "onLangChange", "onDefaultLangChange", "USE_STORE", "USE_DEFAULT_LANG", "DEFAULT_LANGUAGE", "USE_EXTEND", "TranslateService", "store", "<PERSON><PERSON><PERSON><PERSON>", "compiler", "parser", "missingTranslation<PERSON><PERSON><PERSON>", "useDefaultLang", "isolate", "extend", "defaultLanguage", "pending", "_onTranslationChange", "_onLangChange", "_onDefaultLangChange", "_langs", "_translations", "_translationRequests", "setDefaultLang", "_defaultLang", "_currentLang", "retrieveTranslations", "pipe", "subscribe", "res", "changeDefaultLang", "getDefaultLang", "use", "changeLang", "loadingTranslations", "next", "updateLangs", "error", "err", "setTranslation", "shouldMerge", "emit", "get<PERSON>angs", "addLangs", "indexOf", "push", "getParsedResult", "interpolateParams", "observables", "k", "sources", "arr", "obj", "index", "translateService", "get", "Error", "getStreamOnTranslationChange", "event", "stream", "instant", "set", "reloadLang", "resetLang", "getBrowserLang", "window", "navigator", "browserLang", "languages", "language", "browserLanguage", "userLanguage", "getBrowserCultureLang", "browserCultureLang", "TranslateService_Factory", "ɵɵinject", "decorators", "args", "TranslateDirective", "element", "_ref", "onTranslationChangeSub", "checkNodes", "onLangChangeSub", "onDefaultLangChangeSub", "translate", "translateParams", "currentParams", "ngAfterViewChecked", "forceUpdate", "nodes", "nativeElement", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "i", "node", "nodeType", "last<PERSON>ey", "lookup<PERSON><PERSON>", "content", "get<PERSON>ontent", "<PERSON><PERSON><PERSON>nt", "trim", "currentValue", "originalContent", "updateValue", "lastParams", "onTranslation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "data", "ngOnDestroy", "unsubscribe", "TranslateDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "selector", "TranslatePipe", "transform", "query", "validArgs", "JSON", "parse", "e", "SyntaxError", "_dispose", "TranslatePipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "TranslateModule", "forRoot", "config", "ngModule", "providers", "loader", "provide", "useClass", "useValue", "<PERSON><PERSON><PERSON><PERSON>", "TranslateModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@ngx-translate/core/fesm2020/ngx-translate-core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, InjectionToken, Inject, Directive, Input, Pipe, NgModule } from '@angular/core';\nimport { of, isObservable, forkJoin, concat, defer } from 'rxjs';\nimport { take, shareReplay, map, concatMap, switchMap } from 'rxjs/operators';\n\nclass TranslateLoader {\r\n}\r\n/**\r\n * This loader is just a placeholder that does nothing, in case you don't need a loader at all\r\n */\r\nclass TranslateFakeLoader extends TranslateLoader {\r\n    getTranslation(lang) {\r\n        return of({});\r\n    }\r\n}\r\nTranslateFakeLoader.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeLoader, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\r\nTranslateFakeLoader.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeLoader });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeLoader, decorators: [{\r\n            type: Injectable\r\n        }] });\n\nclass MissingTranslationHandler {\r\n}\r\n/**\r\n * This handler is just a placeholder that does nothing, in case you don't need a missing translation handler at all\r\n */\r\nclass FakeMissingTranslationHandler {\r\n    handle(params) {\r\n        return params.key;\r\n    }\r\n}\r\nFakeMissingTranslationHandler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: FakeMissingTranslationHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\r\nFakeMissingTranslationHandler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: FakeMissingTranslationHandler });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: FakeMissingTranslationHandler, decorators: [{\r\n            type: Injectable\r\n        }] });\n\n/* tslint:disable */\r\n/**\r\n * Determines if two objects or two values are equivalent.\r\n *\r\n * Two objects or values are considered equivalent if at least one of the following is true:\r\n *\r\n * * Both objects or values pass `===` comparison.\r\n * * Both objects or values are of the same type and all of their properties are equal by\r\n *   comparing them with `equals`.\r\n *\r\n * @param o1 Object or value to compare.\r\n * @param o2 Object or value to compare.\r\n * @returns true if arguments are equal.\r\n */\r\nfunction equals(o1, o2) {\r\n    if (o1 === o2)\r\n        return true;\r\n    if (o1 === null || o2 === null)\r\n        return false;\r\n    if (o1 !== o1 && o2 !== o2)\r\n        return true; // NaN === NaN\r\n    let t1 = typeof o1, t2 = typeof o2, length, key, keySet;\r\n    if (t1 == t2 && t1 == 'object') {\r\n        if (Array.isArray(o1)) {\r\n            if (!Array.isArray(o2))\r\n                return false;\r\n            if ((length = o1.length) == o2.length) {\r\n                for (key = 0; key < length; key++) {\r\n                    if (!equals(o1[key], o2[key]))\r\n                        return false;\r\n                }\r\n                return true;\r\n            }\r\n        }\r\n        else {\r\n            if (Array.isArray(o2)) {\r\n                return false;\r\n            }\r\n            keySet = Object.create(null);\r\n            for (key in o1) {\r\n                if (!equals(o1[key], o2[key])) {\r\n                    return false;\r\n                }\r\n                keySet[key] = true;\r\n            }\r\n            for (key in o2) {\r\n                if (!(key in keySet) && typeof o2[key] !== 'undefined') {\r\n                    return false;\r\n                }\r\n            }\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\n/* tslint:enable */\r\nfunction isDefined(value) {\r\n    return typeof value !== 'undefined' && value !== null;\r\n}\r\nfunction isObject(item) {\r\n    return (item && typeof item === 'object' && !Array.isArray(item));\r\n}\r\nfunction mergeDeep(target, source) {\r\n    let output = Object.assign({}, target);\r\n    if (isObject(target) && isObject(source)) {\r\n        Object.keys(source).forEach((key) => {\r\n            if (isObject(source[key])) {\r\n                if (!(key in target)) {\r\n                    Object.assign(output, { [key]: source[key] });\r\n                }\r\n                else {\r\n                    output[key] = mergeDeep(target[key], source[key]);\r\n                }\r\n            }\r\n            else {\r\n                Object.assign(output, { [key]: source[key] });\r\n            }\r\n        });\r\n    }\r\n    return output;\r\n}\n\nclass TranslateParser {\r\n}\r\nclass TranslateDefaultParser extends TranslateParser {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.templateMatcher = /{{\\s?([^{}\\s]*)\\s?}}/g;\r\n    }\r\n    interpolate(expr, params) {\r\n        let result;\r\n        if (typeof expr === 'string') {\r\n            result = this.interpolateString(expr, params);\r\n        }\r\n        else if (typeof expr === 'function') {\r\n            result = this.interpolateFunction(expr, params);\r\n        }\r\n        else {\r\n            // this should not happen, but an unrelated TranslateService test depends on it\r\n            result = expr;\r\n        }\r\n        return result;\r\n    }\r\n    getValue(target, key) {\r\n        let keys = typeof key === 'string' ? key.split('.') : [key];\r\n        key = '';\r\n        do {\r\n            key += keys.shift();\r\n            if (isDefined(target) && isDefined(target[key]) && (typeof target[key] === 'object' || !keys.length)) {\r\n                target = target[key];\r\n                key = '';\r\n            }\r\n            else if (!keys.length) {\r\n                target = undefined;\r\n            }\r\n            else {\r\n                key += '.';\r\n            }\r\n        } while (keys.length);\r\n        return target;\r\n    }\r\n    interpolateFunction(fn, params) {\r\n        return fn(params);\r\n    }\r\n    interpolateString(expr, params) {\r\n        if (!params) {\r\n            return expr;\r\n        }\r\n        return expr.replace(this.templateMatcher, (substring, b) => {\r\n            let r = this.getValue(params, b);\r\n            return isDefined(r) ? r : substring;\r\n        });\r\n    }\r\n}\r\nTranslateDefaultParser.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateDefaultParser, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\r\nTranslateDefaultParser.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateDefaultParser });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateDefaultParser, decorators: [{\r\n            type: Injectable\r\n        }] });\n\nclass TranslateCompiler {\r\n}\r\n/**\r\n * This compiler is just a placeholder that does nothing, in case you don't need a compiler at all\r\n */\r\nclass TranslateFakeCompiler extends TranslateCompiler {\r\n    compile(value, lang) {\r\n        return value;\r\n    }\r\n    compileTranslations(translations, lang) {\r\n        return translations;\r\n    }\r\n}\r\nTranslateFakeCompiler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeCompiler, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\r\nTranslateFakeCompiler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeCompiler });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateFakeCompiler, decorators: [{\r\n            type: Injectable\r\n        }] });\n\nclass TranslateStore {\r\n    constructor() {\r\n        /**\r\n         * The lang currently used\r\n         */\r\n        this.currentLang = this.defaultLang;\r\n        /**\r\n         * a list of translations per lang\r\n         */\r\n        this.translations = {};\r\n        /**\r\n         * an array of langs\r\n         */\r\n        this.langs = [];\r\n        /**\r\n         * An EventEmitter to listen to translation change events\r\n         * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\r\n           *     // do something\r\n           * });\r\n         */\r\n        this.onTranslationChange = new EventEmitter();\r\n        /**\r\n         * An EventEmitter to listen to lang change events\r\n         * onLangChange.subscribe((params: LangChangeEvent) => {\r\n           *     // do something\r\n           * });\r\n         */\r\n        this.onLangChange = new EventEmitter();\r\n        /**\r\n         * An EventEmitter to listen to default lang change events\r\n         * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\r\n           *     // do something\r\n           * });\r\n         */\r\n        this.onDefaultLangChange = new EventEmitter();\r\n    }\r\n}\n\nconst USE_STORE = new InjectionToken('USE_STORE');\r\nconst USE_DEFAULT_LANG = new InjectionToken('USE_DEFAULT_LANG');\r\nconst DEFAULT_LANGUAGE = new InjectionToken('DEFAULT_LANGUAGE');\r\nconst USE_EXTEND = new InjectionToken('USE_EXTEND');\r\nclass TranslateService {\r\n    /**\r\n     *\r\n     * @param store an instance of the store (that is supposed to be unique)\r\n     * @param currentLoader An instance of the loader currently used\r\n     * @param compiler An instance of the compiler currently used\r\n     * @param parser An instance of the parser currently used\r\n     * @param missingTranslationHandler A handler for missing translations.\r\n     * @param useDefaultLang whether we should use default language translation when current language translation is missing.\r\n     * @param isolate whether this service should use the store or not\r\n     * @param extend To make a child module extend (and use) translations from parent modules.\r\n     * @param defaultLanguage Set the default language using configuration\r\n     */\r\n    constructor(store, currentLoader, compiler, parser, missingTranslationHandler, useDefaultLang = true, isolate = false, extend = false, defaultLanguage) {\r\n        this.store = store;\r\n        this.currentLoader = currentLoader;\r\n        this.compiler = compiler;\r\n        this.parser = parser;\r\n        this.missingTranslationHandler = missingTranslationHandler;\r\n        this.useDefaultLang = useDefaultLang;\r\n        this.isolate = isolate;\r\n        this.extend = extend;\r\n        this.pending = false;\r\n        this._onTranslationChange = new EventEmitter();\r\n        this._onLangChange = new EventEmitter();\r\n        this._onDefaultLangChange = new EventEmitter();\r\n        this._langs = [];\r\n        this._translations = {};\r\n        this._translationRequests = {};\r\n        /** set the default language from configuration */\r\n        if (defaultLanguage) {\r\n            this.setDefaultLang(defaultLanguage);\r\n        }\r\n    }\r\n    /**\r\n     * An EventEmitter to listen to translation change events\r\n     * onTranslationChange.subscribe((params: TranslationChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\r\n    get onTranslationChange() {\r\n        return this.isolate ? this._onTranslationChange : this.store.onTranslationChange;\r\n    }\r\n    /**\r\n     * An EventEmitter to listen to lang change events\r\n     * onLangChange.subscribe((params: LangChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\r\n    get onLangChange() {\r\n        return this.isolate ? this._onLangChange : this.store.onLangChange;\r\n    }\r\n    /**\r\n     * An EventEmitter to listen to default lang change events\r\n     * onDefaultLangChange.subscribe((params: DefaultLangChangeEvent) => {\r\n       *     // do something\r\n       * });\r\n     */\r\n    get onDefaultLangChange() {\r\n        return this.isolate ? this._onDefaultLangChange : this.store.onDefaultLangChange;\r\n    }\r\n    /**\r\n     * The default lang to fallback when translations are missing on the current lang\r\n     */\r\n    get defaultLang() {\r\n        return this.isolate ? this._defaultLang : this.store.defaultLang;\r\n    }\r\n    set defaultLang(defaultLang) {\r\n        if (this.isolate) {\r\n            this._defaultLang = defaultLang;\r\n        }\r\n        else {\r\n            this.store.defaultLang = defaultLang;\r\n        }\r\n    }\r\n    /**\r\n     * The lang currently used\r\n     */\r\n    get currentLang() {\r\n        return this.isolate ? this._currentLang : this.store.currentLang;\r\n    }\r\n    set currentLang(currentLang) {\r\n        if (this.isolate) {\r\n            this._currentLang = currentLang;\r\n        }\r\n        else {\r\n            this.store.currentLang = currentLang;\r\n        }\r\n    }\r\n    /**\r\n     * an array of langs\r\n     */\r\n    get langs() {\r\n        return this.isolate ? this._langs : this.store.langs;\r\n    }\r\n    set langs(langs) {\r\n        if (this.isolate) {\r\n            this._langs = langs;\r\n        }\r\n        else {\r\n            this.store.langs = langs;\r\n        }\r\n    }\r\n    /**\r\n     * a list of translations per lang\r\n     */\r\n    get translations() {\r\n        return this.isolate ? this._translations : this.store.translations;\r\n    }\r\n    set translations(translations) {\r\n        if (this.isolate) {\r\n            this._translations = translations;\r\n        }\r\n        else {\r\n            this.store.translations = translations;\r\n        }\r\n    }\r\n    /**\r\n     * Sets the default language to use as a fallback\r\n     */\r\n    setDefaultLang(lang) {\r\n        if (lang === this.defaultLang) {\r\n            return;\r\n        }\r\n        let pending = this.retrieveTranslations(lang);\r\n        if (typeof pending !== \"undefined\") {\r\n            // on init set the defaultLang immediately\r\n            if (this.defaultLang == null) {\r\n                this.defaultLang = lang;\r\n            }\r\n            pending.pipe(take(1))\r\n                .subscribe((res) => {\r\n                this.changeDefaultLang(lang);\r\n            });\r\n        }\r\n        else { // we already have this language\r\n            this.changeDefaultLang(lang);\r\n        }\r\n    }\r\n    /**\r\n     * Gets the default language used\r\n     */\r\n    getDefaultLang() {\r\n        return this.defaultLang;\r\n    }\r\n    /**\r\n     * Changes the lang currently used\r\n     */\r\n    use(lang) {\r\n        // don't change the language if the language given is already selected\r\n        if (lang === this.currentLang) {\r\n            return of(this.translations[lang]);\r\n        }\r\n        let pending = this.retrieveTranslations(lang);\r\n        if (typeof pending !== \"undefined\") {\r\n            // on init set the currentLang immediately\r\n            if (!this.currentLang) {\r\n                this.currentLang = lang;\r\n            }\r\n            pending.pipe(take(1))\r\n                .subscribe((res) => {\r\n                this.changeLang(lang);\r\n            });\r\n            return pending;\r\n        }\r\n        else { // we have this language, return an Observable\r\n            this.changeLang(lang);\r\n            return of(this.translations[lang]);\r\n        }\r\n    }\r\n    /**\r\n     * Retrieves the given translations\r\n     */\r\n    retrieveTranslations(lang) {\r\n        let pending;\r\n        // if this language is unavailable or extend is true, ask for it\r\n        if (typeof this.translations[lang] === \"undefined\" || this.extend) {\r\n            this._translationRequests[lang] = this._translationRequests[lang] || this.getTranslation(lang);\r\n            pending = this._translationRequests[lang];\r\n        }\r\n        return pending;\r\n    }\r\n    /**\r\n     * Gets an object of translations for a given language with the current loader\r\n     * and passes it through the compiler\r\n     */\r\n    getTranslation(lang) {\r\n        this.pending = true;\r\n        const loadingTranslations = this.currentLoader.getTranslation(lang).pipe(shareReplay(1), take(1));\r\n        this.loadingTranslations = loadingTranslations.pipe(map((res) => this.compiler.compileTranslations(res, lang)), shareReplay(1), take(1));\r\n        this.loadingTranslations\r\n            .subscribe({\r\n            next: (res) => {\r\n                this.translations[lang] = this.extend && this.translations[lang] ? { ...res, ...this.translations[lang] } : res;\r\n                this.updateLangs();\r\n                this.pending = false;\r\n            },\r\n            error: (err) => {\r\n                this.pending = false;\r\n            }\r\n        });\r\n        return loadingTranslations;\r\n    }\r\n    /**\r\n     * Manually sets an object of translations for a given language\r\n     * after passing it through the compiler\r\n     */\r\n    setTranslation(lang, translations, shouldMerge = false) {\r\n        translations = this.compiler.compileTranslations(translations, lang);\r\n        if ((shouldMerge || this.extend) && this.translations[lang]) {\r\n            this.translations[lang] = mergeDeep(this.translations[lang], translations);\r\n        }\r\n        else {\r\n            this.translations[lang] = translations;\r\n        }\r\n        this.updateLangs();\r\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\r\n    }\r\n    /**\r\n     * Returns an array of currently available langs\r\n     */\r\n    getLangs() {\r\n        return this.langs;\r\n    }\r\n    /**\r\n     * Add available langs\r\n     */\r\n    addLangs(langs) {\r\n        langs.forEach((lang) => {\r\n            if (this.langs.indexOf(lang) === -1) {\r\n                this.langs.push(lang);\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     * Update the list of available langs\r\n     */\r\n    updateLangs() {\r\n        this.addLangs(Object.keys(this.translations));\r\n    }\r\n    /**\r\n     * Returns the parsed result of the translations\r\n     */\r\n    getParsedResult(translations, key, interpolateParams) {\r\n        let res;\r\n        if (key instanceof Array) {\r\n            let result = {}, observables = false;\r\n            for (let k of key) {\r\n                result[k] = this.getParsedResult(translations, k, interpolateParams);\r\n                if (isObservable(result[k])) {\r\n                    observables = true;\r\n                }\r\n            }\r\n            if (observables) {\r\n                const sources = key.map(k => isObservable(result[k]) ? result[k] : of(result[k]));\r\n                return forkJoin(sources).pipe(map((arr) => {\r\n                    let obj = {};\r\n                    arr.forEach((value, index) => {\r\n                        obj[key[index]] = value;\r\n                    });\r\n                    return obj;\r\n                }));\r\n            }\r\n            return result;\r\n        }\r\n        if (translations) {\r\n            res = this.parser.interpolate(this.parser.getValue(translations, key), interpolateParams);\r\n        }\r\n        if (typeof res === \"undefined\" && this.defaultLang != null && this.defaultLang !== this.currentLang && this.useDefaultLang) {\r\n            res = this.parser.interpolate(this.parser.getValue(this.translations[this.defaultLang], key), interpolateParams);\r\n        }\r\n        if (typeof res === \"undefined\") {\r\n            let params = { key, translateService: this };\r\n            if (typeof interpolateParams !== 'undefined') {\r\n                params.interpolateParams = interpolateParams;\r\n            }\r\n            res = this.missingTranslationHandler.handle(params);\r\n        }\r\n        return typeof res !== \"undefined\" ? res : key;\r\n    }\r\n    /**\r\n     * Gets the translated value of a key (or an array of keys)\r\n     * @returns the translated key, or an object of translated keys\r\n     */\r\n    get(key, interpolateParams) {\r\n        if (!isDefined(key) || !key.length) {\r\n            throw new Error(`Parameter \"key\" required`);\r\n        }\r\n        // check if we are loading a new translation to use\r\n        if (this.pending) {\r\n            return this.loadingTranslations.pipe(concatMap((res) => {\r\n                res = this.getParsedResult(res, key, interpolateParams);\r\n                return isObservable(res) ? res : of(res);\r\n            }));\r\n        }\r\n        else {\r\n            let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\r\n            return isObservable(res) ? res : of(res);\r\n        }\r\n    }\r\n    /**\r\n     * Returns a stream of translated values of a key (or an array of keys) which updates\r\n     * whenever the translation changes.\r\n     * @returns A stream of the translated key, or an object of translated keys\r\n     */\r\n    getStreamOnTranslationChange(key, interpolateParams) {\r\n        if (!isDefined(key) || !key.length) {\r\n            throw new Error(`Parameter \"key\" required`);\r\n        }\r\n        return concat(defer(() => this.get(key, interpolateParams)), this.onTranslationChange.pipe(switchMap((event) => {\r\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\r\n            if (typeof res.subscribe === 'function') {\r\n                return res;\r\n            }\r\n            else {\r\n                return of(res);\r\n            }\r\n        })));\r\n    }\r\n    /**\r\n     * Returns a stream of translated values of a key (or an array of keys) which updates\r\n     * whenever the language changes.\r\n     * @returns A stream of the translated key, or an object of translated keys\r\n     */\r\n    stream(key, interpolateParams) {\r\n        if (!isDefined(key) || !key.length) {\r\n            throw new Error(`Parameter \"key\" required`);\r\n        }\r\n        return concat(defer(() => this.get(key, interpolateParams)), this.onLangChange.pipe(switchMap((event) => {\r\n            const res = this.getParsedResult(event.translations, key, interpolateParams);\r\n            return isObservable(res) ? res : of(res);\r\n        })));\r\n    }\r\n    /**\r\n     * Returns a translation instantly from the internal state of loaded translation.\r\n     * All rules regarding the current language, the preferred language of even fallback languages will be used except any promise handling.\r\n     */\r\n    instant(key, interpolateParams) {\r\n        if (!isDefined(key) || !key.length) {\r\n            throw new Error(`Parameter \"key\" required`);\r\n        }\r\n        let res = this.getParsedResult(this.translations[this.currentLang], key, interpolateParams);\r\n        if (isObservable(res)) {\r\n            if (key instanceof Array) {\r\n                let obj = {};\r\n                key.forEach((value, index) => {\r\n                    obj[key[index]] = key[index];\r\n                });\r\n                return obj;\r\n            }\r\n            return key;\r\n        }\r\n        else {\r\n            return res;\r\n        }\r\n    }\r\n    /**\r\n     * Sets the translated value of a key, after compiling it\r\n     */\r\n    set(key, value, lang = this.currentLang) {\r\n        this.translations[lang][key] = this.compiler.compile(value, lang);\r\n        this.updateLangs();\r\n        this.onTranslationChange.emit({ lang: lang, translations: this.translations[lang] });\r\n    }\r\n    /**\r\n     * Changes the current lang\r\n     */\r\n    changeLang(lang) {\r\n        this.currentLang = lang;\r\n        this.onLangChange.emit({ lang: lang, translations: this.translations[lang] });\r\n        // if there is no default lang, use the one that we just set\r\n        if (this.defaultLang == null) {\r\n            this.changeDefaultLang(lang);\r\n        }\r\n    }\r\n    /**\r\n     * Changes the default lang\r\n     */\r\n    changeDefaultLang(lang) {\r\n        this.defaultLang = lang;\r\n        this.onDefaultLangChange.emit({ lang: lang, translations: this.translations[lang] });\r\n    }\r\n    /**\r\n     * Allows to reload the lang file from the file\r\n     */\r\n    reloadLang(lang) {\r\n        this.resetLang(lang);\r\n        return this.getTranslation(lang);\r\n    }\r\n    /**\r\n     * Deletes inner translation\r\n     */\r\n    resetLang(lang) {\r\n        this._translationRequests[lang] = undefined;\r\n        this.translations[lang] = undefined;\r\n    }\r\n    /**\r\n     * Returns the language code name from the browser, e.g. \"de\"\r\n     */\r\n    getBrowserLang() {\r\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\r\n            return undefined;\r\n        }\r\n        let browserLang = window.navigator.languages ? window.navigator.languages[0] : null;\r\n        browserLang = browserLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\r\n        if (typeof browserLang === 'undefined') {\r\n            return undefined;\r\n        }\r\n        if (browserLang.indexOf('-') !== -1) {\r\n            browserLang = browserLang.split('-')[0];\r\n        }\r\n        if (browserLang.indexOf('_') !== -1) {\r\n            browserLang = browserLang.split('_')[0];\r\n        }\r\n        return browserLang;\r\n    }\r\n    /**\r\n     * Returns the culture language code name from the browser, e.g. \"de-DE\"\r\n     */\r\n    getBrowserCultureLang() {\r\n        if (typeof window === 'undefined' || typeof window.navigator === 'undefined') {\r\n            return undefined;\r\n        }\r\n        let browserCultureLang = window.navigator.languages ? window.navigator.languages[0] : null;\r\n        browserCultureLang = browserCultureLang || window.navigator.language || window.navigator.browserLanguage || window.navigator.userLanguage;\r\n        return browserCultureLang;\r\n    }\r\n}\r\nTranslateService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateService, deps: [{ token: TranslateStore }, { token: TranslateLoader }, { token: TranslateCompiler }, { token: TranslateParser }, { token: MissingTranslationHandler }, { token: USE_DEFAULT_LANG }, { token: USE_STORE }, { token: USE_EXTEND }, { token: DEFAULT_LANGUAGE }], target: i0.ɵɵFactoryTarget.Injectable });\r\nTranslateService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateService });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateService, decorators: [{\r\n            type: Injectable\r\n        }], ctorParameters: function () { return [{ type: TranslateStore }, { type: TranslateLoader }, { type: TranslateCompiler }, { type: TranslateParser }, { type: MissingTranslationHandler }, { type: undefined, decorators: [{\r\n                    type: Inject,\r\n                    args: [USE_DEFAULT_LANG]\r\n                }] }, { type: undefined, decorators: [{\r\n                    type: Inject,\r\n                    args: [USE_STORE]\r\n                }] }, { type: undefined, decorators: [{\r\n                    type: Inject,\r\n                    args: [USE_EXTEND]\r\n                }] }, { type: undefined, decorators: [{\r\n                    type: Inject,\r\n                    args: [DEFAULT_LANGUAGE]\r\n                }] }]; } });\n\nclass TranslateDirective {\r\n    constructor(translateService, element, _ref) {\r\n        this.translateService = translateService;\r\n        this.element = element;\r\n        this._ref = _ref;\r\n        // subscribe to onTranslationChange event, in case the translations of the current lang change\r\n        if (!this.onTranslationChangeSub) {\r\n            this.onTranslationChangeSub = this.translateService.onTranslationChange.subscribe((event) => {\r\n                if (event.lang === this.translateService.currentLang) {\r\n                    this.checkNodes(true, event.translations);\r\n                }\r\n            });\r\n        }\r\n        // subscribe to onLangChange event, in case the language changes\r\n        if (!this.onLangChangeSub) {\r\n            this.onLangChangeSub = this.translateService.onLangChange.subscribe((event) => {\r\n                this.checkNodes(true, event.translations);\r\n            });\r\n        }\r\n        // subscribe to onDefaultLangChange event, in case the default language changes\r\n        if (!this.onDefaultLangChangeSub) {\r\n            this.onDefaultLangChangeSub = this.translateService.onDefaultLangChange.subscribe((event) => {\r\n                this.checkNodes(true);\r\n            });\r\n        }\r\n    }\r\n    set translate(key) {\r\n        if (key) {\r\n            this.key = key;\r\n            this.checkNodes();\r\n        }\r\n    }\r\n    set translateParams(params) {\r\n        if (!equals(this.currentParams, params)) {\r\n            this.currentParams = params;\r\n            this.checkNodes(true);\r\n        }\r\n    }\r\n    ngAfterViewChecked() {\r\n        this.checkNodes();\r\n    }\r\n    checkNodes(forceUpdate = false, translations) {\r\n        let nodes = this.element.nativeElement.childNodes;\r\n        // if the element is empty\r\n        if (!nodes.length) {\r\n            // we add the key as content\r\n            this.setContent(this.element.nativeElement, this.key);\r\n            nodes = this.element.nativeElement.childNodes;\r\n        }\r\n        for (let i = 0; i < nodes.length; ++i) {\r\n            let node = nodes[i];\r\n            if (node.nodeType === 3) { // node type 3 is a text node\r\n                let key;\r\n                if (forceUpdate) {\r\n                    node.lastKey = null;\r\n                }\r\n                if (isDefined(node.lookupKey)) {\r\n                    key = node.lookupKey;\r\n                }\r\n                else if (this.key) {\r\n                    key = this.key;\r\n                }\r\n                else {\r\n                    let content = this.getContent(node);\r\n                    let trimmedContent = content.trim();\r\n                    if (trimmedContent.length) {\r\n                        node.lookupKey = trimmedContent;\r\n                        // we want to use the content as a key, not the translation value\r\n                        if (content !== node.currentValue) {\r\n                            key = trimmedContent;\r\n                            // the content was changed from the user, we'll use it as a reference if needed\r\n                            node.originalContent = content || node.originalContent;\r\n                        }\r\n                        else if (node.originalContent) { // the content seems ok, but the lang has changed\r\n                            // the current content is the translation, not the key, use the last real content as key\r\n                            key = node.originalContent.trim();\r\n                        }\r\n                        else if (content !== node.currentValue) {\r\n                            // we want to use the content as a key, not the translation value\r\n                            key = trimmedContent;\r\n                            // the content was changed from the user, we'll use it as a reference if needed\r\n                            node.originalContent = content || node.originalContent;\r\n                        }\r\n                    }\r\n                }\r\n                this.updateValue(key, node, translations);\r\n            }\r\n        }\r\n    }\r\n    updateValue(key, node, translations) {\r\n        if (key) {\r\n            if (node.lastKey === key && this.lastParams === this.currentParams) {\r\n                return;\r\n            }\r\n            this.lastParams = this.currentParams;\r\n            let onTranslation = (res) => {\r\n                if (res !== key) {\r\n                    node.lastKey = key;\r\n                }\r\n                if (!node.originalContent) {\r\n                    node.originalContent = this.getContent(node);\r\n                }\r\n                node.currentValue = isDefined(res) ? res : (node.originalContent || key);\r\n                // we replace in the original content to preserve spaces that we might have trimmed\r\n                this.setContent(node, this.key ? node.currentValue : node.originalContent.replace(key, node.currentValue));\r\n                this._ref.markForCheck();\r\n            };\r\n            if (isDefined(translations)) {\r\n                let res = this.translateService.getParsedResult(translations, key, this.currentParams);\r\n                if (isObservable(res)) {\r\n                    res.subscribe({ next: onTranslation });\r\n                }\r\n                else {\r\n                    onTranslation(res);\r\n                }\r\n            }\r\n            else {\r\n                this.translateService.get(key, this.currentParams).subscribe(onTranslation);\r\n            }\r\n        }\r\n    }\r\n    getContent(node) {\r\n        return isDefined(node.textContent) ? node.textContent : node.data;\r\n    }\r\n    setContent(node, content) {\r\n        if (isDefined(node.textContent)) {\r\n            node.textContent = content;\r\n        }\r\n        else {\r\n            node.data = content;\r\n        }\r\n    }\r\n    ngOnDestroy() {\r\n        if (this.onLangChangeSub) {\r\n            this.onLangChangeSub.unsubscribe();\r\n        }\r\n        if (this.onDefaultLangChangeSub) {\r\n            this.onDefaultLangChangeSub.unsubscribe();\r\n        }\r\n        if (this.onTranslationChangeSub) {\r\n            this.onTranslationChangeSub.unsubscribe();\r\n        }\r\n    }\r\n}\r\nTranslateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateDirective, deps: [{ token: TranslateService }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\r\nTranslateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.0\", type: TranslateDirective, selector: \"[translate],[ngx-translate]\", inputs: { translate: \"translate\", translateParams: \"translateParams\" }, ngImport: i0 });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateDirective, decorators: [{\r\n            type: Directive,\r\n            args: [{\r\n                    selector: '[translate],[ngx-translate]'\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: TranslateService }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { translate: [{\r\n                type: Input\r\n            }], translateParams: [{\r\n                type: Input\r\n            }] } });\n\nclass TranslatePipe {\r\n    constructor(translate, _ref) {\r\n        this.translate = translate;\r\n        this._ref = _ref;\r\n        this.value = '';\r\n        this.lastKey = null;\r\n        this.lastParams = [];\r\n    }\r\n    updateValue(key, interpolateParams, translations) {\r\n        let onTranslation = (res) => {\r\n            this.value = res !== undefined ? res : key;\r\n            this.lastKey = key;\r\n            this._ref.markForCheck();\r\n        };\r\n        if (translations) {\r\n            let res = this.translate.getParsedResult(translations, key, interpolateParams);\r\n            if (isObservable(res.subscribe)) {\r\n                res.subscribe(onTranslation);\r\n            }\r\n            else {\r\n                onTranslation(res);\r\n            }\r\n        }\r\n        this.translate.get(key, interpolateParams).subscribe(onTranslation);\r\n    }\r\n    transform(query, ...args) {\r\n        if (!query || !query.length) {\r\n            return query;\r\n        }\r\n        // if we ask another time for the same key, return the last value\r\n        if (equals(query, this.lastKey) && equals(args, this.lastParams)) {\r\n            return this.value;\r\n        }\r\n        let interpolateParams = undefined;\r\n        if (isDefined(args[0]) && args.length) {\r\n            if (typeof args[0] === 'string' && args[0].length) {\r\n                // we accept objects written in the template such as {n:1}, {'n':1}, {n:'v'}\r\n                // which is why we might need to change it to real JSON objects such as {\"n\":1} or {\"n\":\"v\"}\r\n                let validArgs = args[0]\r\n                    .replace(/(\\')?([a-zA-Z0-9_]+)(\\')?(\\s)?:/g, '\"$2\":')\r\n                    .replace(/:(\\s)?(\\')(.*?)(\\')/g, ':\"$3\"');\r\n                try {\r\n                    interpolateParams = JSON.parse(validArgs);\r\n                }\r\n                catch (e) {\r\n                    throw new SyntaxError(`Wrong parameter in TranslatePipe. Expected a valid Object, received: ${args[0]}`);\r\n                }\r\n            }\r\n            else if (typeof args[0] === 'object' && !Array.isArray(args[0])) {\r\n                interpolateParams = args[0];\r\n            }\r\n        }\r\n        // store the query, in case it changes\r\n        this.lastKey = query;\r\n        // store the params, in case they change\r\n        this.lastParams = args;\r\n        // set the value\r\n        this.updateValue(query, interpolateParams);\r\n        // if there is a subscription to onLangChange, clean it\r\n        this._dispose();\r\n        // subscribe to onTranslationChange event, in case the translations change\r\n        if (!this.onTranslationChange) {\r\n            this.onTranslationChange = this.translate.onTranslationChange.subscribe((event) => {\r\n                if (this.lastKey && event.lang === this.translate.currentLang) {\r\n                    this.lastKey = null;\r\n                    this.updateValue(query, interpolateParams, event.translations);\r\n                }\r\n            });\r\n        }\r\n        // subscribe to onLangChange event, in case the language changes\r\n        if (!this.onLangChange) {\r\n            this.onLangChange = this.translate.onLangChange.subscribe((event) => {\r\n                if (this.lastKey) {\r\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\r\n                    this.updateValue(query, interpolateParams, event.translations);\r\n                }\r\n            });\r\n        }\r\n        // subscribe to onDefaultLangChange event, in case the default language changes\r\n        if (!this.onDefaultLangChange) {\r\n            this.onDefaultLangChange = this.translate.onDefaultLangChange.subscribe(() => {\r\n                if (this.lastKey) {\r\n                    this.lastKey = null; // we want to make sure it doesn't return the same value until it's been updated\r\n                    this.updateValue(query, interpolateParams);\r\n                }\r\n            });\r\n        }\r\n        return this.value;\r\n    }\r\n    /**\r\n     * Clean any existing subscription to change events\r\n     */\r\n    _dispose() {\r\n        if (typeof this.onTranslationChange !== 'undefined') {\r\n            this.onTranslationChange.unsubscribe();\r\n            this.onTranslationChange = undefined;\r\n        }\r\n        if (typeof this.onLangChange !== 'undefined') {\r\n            this.onLangChange.unsubscribe();\r\n            this.onLangChange = undefined;\r\n        }\r\n        if (typeof this.onDefaultLangChange !== 'undefined') {\r\n            this.onDefaultLangChange.unsubscribe();\r\n            this.onDefaultLangChange = undefined;\r\n        }\r\n    }\r\n    ngOnDestroy() {\r\n        this._dispose();\r\n    }\r\n}\r\nTranslatePipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslatePipe, deps: [{ token: TranslateService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });\r\nTranslatePipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslatePipe, name: \"translate\", pure: false });\r\nTranslatePipe.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslatePipe });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslatePipe, decorators: [{\r\n            type: Injectable\r\n        }, {\r\n            type: Pipe,\r\n            args: [{\r\n                    name: 'translate',\r\n                    pure: false // required to update the value when the promise is resolved\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: TranslateService }, { type: i0.ChangeDetectorRef }]; } });\n\nclass TranslateModule {\r\n    /**\r\n     * Use this method in your root module to provide the TranslateService\r\n     */\r\n    static forRoot(config = {}) {\r\n        return {\r\n            ngModule: TranslateModule,\r\n            providers: [\r\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\r\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\r\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\r\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\r\n                TranslateStore,\r\n                { provide: USE_STORE, useValue: config.isolate },\r\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\r\n                { provide: USE_EXTEND, useValue: config.extend },\r\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\r\n                TranslateService\r\n            ]\r\n        };\r\n    }\r\n    /**\r\n     * Use this method in your other (non root) modules to import the directive/pipe\r\n     */\r\n    static forChild(config = {}) {\r\n        return {\r\n            ngModule: TranslateModule,\r\n            providers: [\r\n                config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },\r\n                config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },\r\n                config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },\r\n                config.missingTranslationHandler || { provide: MissingTranslationHandler, useClass: FakeMissingTranslationHandler },\r\n                { provide: USE_STORE, useValue: config.isolate },\r\n                { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },\r\n                { provide: USE_EXTEND, useValue: config.extend },\r\n                { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },\r\n                TranslateService\r\n            ]\r\n        };\r\n    }\r\n}\r\nTranslateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nTranslateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateModule, declarations: [TranslatePipe,\r\n        TranslateDirective], exports: [TranslatePipe,\r\n        TranslateDirective] });\r\nTranslateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateModule });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.0\", ngImport: i0, type: TranslateModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    declarations: [\r\n                        TranslatePipe,\r\n                        TranslateDirective\r\n                    ],\r\n                    exports: [\r\n                        TranslatePipe,\r\n                        TranslateDirective\r\n                    ]\r\n                }]\r\n        }] });\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { DEFAULT_LANGUAGE, FakeMissingTranslationHandler, MissingTranslationHandler, TranslateCompiler, TranslateDefaultParser, TranslateDirective, TranslateFakeCompiler, TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateParser, TranslatePipe, TranslateService, TranslateStore, USE_DEFAULT_LANG, USE_EXTEND, USE_STORE };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAClH,SAASC,EAAE,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAChE,SAASC,IAAI,EAAEC,WAAW,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAE7E,MAAMC,eAAe,CAAC;AAEtB;AACA;AACA;AACA,MAAMC,mBAAmB,SAASD,eAAe,CAAC;EAC9CE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOb,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;AACJ;AACAW,mBAAmB,CAACG,IAAI;EAAA,IAAAC,gCAAA;EAAA,gBAAAC,4BAAAC,CAAA;IAAA,QAAAF,gCAAA,KAAAA,gCAAA,GAA8ExB,EAAE,CAAA2B,qBAAA,CAAQP,mBAAmB,IAAAM,CAAA,IAAnBN,mBAAmB;EAAA;AAAA,GAAsD;AACzLA,mBAAmB,CAACQ,KAAK,kBAD6E5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EACYV,mBAAmB;EAAAW,OAAA,EAAnBX,mBAAmB,CAAAG;AAAA,EAAG;AAC1I;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAFsGhC,EAAE,CAAAiC,iBAAA,CAEbb,mBAAmB,EAAc,CAAC;IACjHc,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMkC,yBAAyB,CAAC;AAEhC;AACA;AACA;AACA,MAAMC,6BAA6B,CAAC;EAChCC,MAAMA,CAACC,MAAM,EAAE;IACX,OAAOA,MAAM,CAACC,GAAG;EACrB;AACJ;AACAH,6BAA6B,CAACb,IAAI,YAAAiB,sCAAAd,CAAA;EAAA,YAAAA,CAAA,IAAwFU,6BAA6B;AAAA,CAAoD;AAC3MA,6BAA6B,CAACR,KAAK,kBAjBmE5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EAiBsBM,6BAA6B;EAAAL,OAAA,EAA7BK,6BAA6B,CAAAb;AAAA,EAAG;AAC9J;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAlBsGhC,EAAE,CAAAiC,iBAAA,CAkBbG,6BAA6B,EAAc,CAAC;IAC3HF,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACpB,IAAID,EAAE,KAAKC,EAAE,EACT,OAAO,IAAI;EACf,IAAID,EAAE,KAAK,IAAI,IAAIC,EAAE,KAAK,IAAI,EAC1B,OAAO,KAAK;EAChB,IAAID,EAAE,KAAKA,EAAE,IAAIC,EAAE,KAAKA,EAAE,EACtB,OAAO,IAAI,CAAC,CAAC;EACjB,IAAIC,EAAE,GAAG,OAAOF,EAAE;IAAEG,EAAE,GAAG,OAAOF,EAAE;IAAEG,MAAM;IAAEP,GAAG;IAAEQ,MAAM;EACvD,IAAIH,EAAE,IAAIC,EAAE,IAAID,EAAE,IAAI,QAAQ,EAAE;IAC5B,IAAII,KAAK,CAACC,OAAO,CAACP,EAAE,CAAC,EAAE;MACnB,IAAI,CAACM,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAClB,OAAO,KAAK;MAChB,IAAI,CAACG,MAAM,GAAGJ,EAAE,CAACI,MAAM,KAAKH,EAAE,CAACG,MAAM,EAAE;QACnC,KAAKP,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGO,MAAM,EAAEP,GAAG,EAAE,EAAE;UAC/B,IAAI,CAACE,MAAM,CAACC,EAAE,CAACH,GAAG,CAAC,EAAEI,EAAE,CAACJ,GAAG,CAAC,CAAC,EACzB,OAAO,KAAK;QACpB;QACA,OAAO,IAAI;MACf;IACJ,CAAC,MACI;MACD,IAAIS,KAAK,CAACC,OAAO,CAACN,EAAE,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;MACAI,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAC5B,KAAKZ,GAAG,IAAIG,EAAE,EAAE;QACZ,IAAI,CAACD,MAAM,CAACC,EAAE,CAACH,GAAG,CAAC,EAAEI,EAAE,CAACJ,GAAG,CAAC,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACAQ,MAAM,CAACR,GAAG,CAAC,GAAG,IAAI;MACtB;MACA,KAAKA,GAAG,IAAII,EAAE,EAAE;QACZ,IAAI,EAAEJ,GAAG,IAAIQ,MAAM,CAAC,IAAI,OAAOJ,EAAE,CAACJ,GAAG,CAAC,KAAK,WAAW,EAAE;UACpD,OAAO,KAAK;QAChB;MACJ;MACA,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA,SAASa,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACpB,OAAQA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACP,KAAK,CAACC,OAAO,CAACM,IAAI,CAAC;AACpE;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIC,MAAM,GAAGT,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;EACtC,IAAIH,QAAQ,CAACG,MAAM,CAAC,IAAIH,QAAQ,CAACI,MAAM,CAAC,EAAE;IACtCR,MAAM,CAACW,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAEvB,GAAG,IAAK;MACjC,IAAIe,QAAQ,CAACI,MAAM,CAACnB,GAAG,CAAC,CAAC,EAAE;QACvB,IAAI,EAAEA,GAAG,IAAIkB,MAAM,CAAC,EAAE;UAClBP,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;YAAE,CAACpB,GAAG,GAAGmB,MAAM,CAACnB,GAAG;UAAE,CAAC,CAAC;QACjD,CAAC,MACI;UACDoB,MAAM,CAACpB,GAAG,CAAC,GAAGiB,SAAS,CAACC,MAAM,CAAClB,GAAG,CAAC,EAAEmB,MAAM,CAACnB,GAAG,CAAC,CAAC;QACrD;MACJ,CAAC,MACI;QACDW,MAAM,CAACU,MAAM,CAACD,MAAM,EAAE;UAAE,CAACpB,GAAG,GAAGmB,MAAM,CAACnB,GAAG;QAAE,CAAC,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACA,OAAOoB,MAAM;AACjB;AAEA,MAAMI,eAAe,CAAC;AAEtB,MAAMC,sBAAsB,SAASD,eAAe,CAAC;EACjDE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,eAAe,GAAG,uBAAuB;EAClD;EACAC,WAAWA,CAACC,IAAI,EAAE/B,MAAM,EAAE;IACtB,IAAIgC,MAAM;IACV,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC1BC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAE/B,MAAM,CAAC;IACjD,CAAC,MACI,IAAI,OAAO+B,IAAI,KAAK,UAAU,EAAE;MACjCC,MAAM,GAAG,IAAI,CAACE,mBAAmB,CAACH,IAAI,EAAE/B,MAAM,CAAC;IACnD,CAAC,MACI;MACD;MACAgC,MAAM,GAAGD,IAAI;IACjB;IACA,OAAOC,MAAM;EACjB;EACAG,QAAQA,CAAChB,MAAM,EAAElB,GAAG,EAAE;IAClB,IAAIsB,IAAI,GAAG,OAAOtB,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACmC,KAAK,CAAC,GAAG,CAAC,GAAG,CAACnC,GAAG,CAAC;IAC3DA,GAAG,GAAG,EAAE;IACR,GAAG;MACCA,GAAG,IAAIsB,IAAI,CAACc,KAAK,CAAC,CAAC;MACnB,IAAIvB,SAAS,CAACK,MAAM,CAAC,IAAIL,SAAS,CAACK,MAAM,CAAClB,GAAG,CAAC,CAAC,KAAK,OAAOkB,MAAM,CAAClB,GAAG,CAAC,KAAK,QAAQ,IAAI,CAACsB,IAAI,CAACf,MAAM,CAAC,EAAE;QAClGW,MAAM,GAAGA,MAAM,CAAClB,GAAG,CAAC;QACpBA,GAAG,GAAG,EAAE;MACZ,CAAC,MACI,IAAI,CAACsB,IAAI,CAACf,MAAM,EAAE;QACnBW,MAAM,GAAGmB,SAAS;MACtB,CAAC,MACI;QACDrC,GAAG,IAAI,GAAG;MACd;IACJ,CAAC,QAAQsB,IAAI,CAACf,MAAM;IACpB,OAAOW,MAAM;EACjB;EACAe,mBAAmBA,CAACK,EAAE,EAAEvC,MAAM,EAAE;IAC5B,OAAOuC,EAAE,CAACvC,MAAM,CAAC;EACrB;EACAiC,iBAAiBA,CAACF,IAAI,EAAE/B,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,EAAE;MACT,OAAO+B,IAAI;IACf;IACA,OAAOA,IAAI,CAACS,OAAO,CAAC,IAAI,CAACX,eAAe,EAAE,CAACY,SAAS,EAAEC,CAAC,KAAK;MACxD,IAAIC,CAAC,GAAG,IAAI,CAACR,QAAQ,CAACnC,MAAM,EAAE0C,CAAC,CAAC;MAChC,OAAO5B,SAAS,CAAC6B,CAAC,CAAC,GAAGA,CAAC,GAAGF,SAAS;IACvC,CAAC,CAAC;EACN;AACJ;AACAf,sBAAsB,CAACzC,IAAI;EAAA,IAAA2D,mCAAA;EAAA,gBAAAC,+BAAAzD,CAAA;IAAA,QAAAwD,mCAAA,KAAAA,mCAAA,GA5J2ElF,EAAE,CAAA2B,qBAAA,CA4JWqC,sBAAsB,IAAAtC,CAAA,IAAtBsC,sBAAsB;EAAA;AAAA,GAAsD;AAC/LA,sBAAsB,CAACpC,KAAK,kBA7J0E5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EA6JekC,sBAAsB;EAAAjC,OAAA,EAAtBiC,sBAAsB,CAAAzC;AAAA,EAAG;AAChJ;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA9JsGhC,EAAE,CAAAiC,iBAAA,CA8Jb+B,sBAAsB,EAAc,CAAC;IACpH9B,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAMmF,iBAAiB,CAAC;AAExB;AACA;AACA;AACA,MAAMC,qBAAqB,SAASD,iBAAiB,CAAC;EAClDE,OAAOA,CAACjC,KAAK,EAAE/B,IAAI,EAAE;IACjB,OAAO+B,KAAK;EAChB;EACAkC,mBAAmBA,CAACC,YAAY,EAAElE,IAAI,EAAE;IACpC,OAAOkE,YAAY;EACvB;AACJ;AACAH,qBAAqB,CAAC9D,IAAI;EAAA,IAAAkE,kCAAA;EAAA,gBAAAC,8BAAAhE,CAAA;IAAA,QAAA+D,kCAAA,KAAAA,kCAAA,GA/K4EzF,EAAE,CAAA2B,qBAAA,CA+KU0D,qBAAqB,IAAA3D,CAAA,IAArB2D,qBAAqB;EAAA;AAAA,GAAsD;AAC7LA,qBAAqB,CAACzD,KAAK,kBAhL2E5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EAgLcuD,qBAAqB;EAAAtD,OAAA,EAArBsD,qBAAqB,CAAA9D;AAAA,EAAG;AAC9I;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAjLsGhC,EAAE,CAAAiC,iBAAA,CAiLboD,qBAAqB,EAAc,CAAC;IACnHnD,IAAI,EAAEjC;EACV,CAAC,CAAC;AAAA;AAEV,MAAM0F,cAAc,CAAC;EACjB1B,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAAC2B,WAAW,GAAG,IAAI,CAACC,WAAW;IACnC;AACR;AACA;IACQ,IAAI,CAACL,YAAY,GAAG,CAAC,CAAC;IACtB;AACR;AACA;IACQ,IAAI,CAACM,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,IAAI7F,YAAY,CAAC,CAAC;IAC7C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8F,YAAY,GAAG,IAAI9F,YAAY,CAAC,CAAC;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+F,mBAAmB,GAAG,IAAI/F,YAAY,CAAC,CAAC;EACjD;AACJ;AAEA,MAAMgG,SAAS,GAAG,IAAI/F,cAAc,CAAC,WAAW,CAAC;AACjD,MAAMgG,gBAAgB,GAAG,IAAIhG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMiG,gBAAgB,GAAG,IAAIjG,cAAc,CAAC,kBAAkB,CAAC;AAC/D,MAAMkG,UAAU,GAAG,IAAIlG,cAAc,CAAC,YAAY,CAAC;AACnD,MAAMmG,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrC,WAAWA,CAACsC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,yBAAyB,EAAEC,cAAc,GAAG,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,KAAK,EAAEC,eAAe,EAAE;IACpJ,IAAI,CAACR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,oBAAoB,GAAG,IAAI/G,YAAY,CAAC,CAAC;IAC9C,IAAI,CAACgH,aAAa,GAAG,IAAIhH,YAAY,CAAC,CAAC;IACvC,IAAI,CAACiH,oBAAoB,GAAG,IAAIjH,YAAY,CAAC,CAAC;IAC9C,IAAI,CAACkH,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC;IAC9B;IACA,IAAIP,eAAe,EAAE;MACjB,IAAI,CAACQ,cAAc,CAACR,eAAe,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIhB,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACc,OAAO,GAAG,IAAI,CAACI,oBAAoB,GAAG,IAAI,CAACV,KAAK,CAACR,mBAAmB;EACpF;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACa,OAAO,GAAG,IAAI,CAACK,aAAa,GAAG,IAAI,CAACX,KAAK,CAACP,YAAY;EACtE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACY,OAAO,GAAG,IAAI,CAACM,oBAAoB,GAAG,IAAI,CAACZ,KAAK,CAACN,mBAAmB;EACpF;EACA;AACJ;AACA;EACI,IAAIJ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACgB,OAAO,GAAG,IAAI,CAACW,YAAY,GAAG,IAAI,CAACjB,KAAK,CAACV,WAAW;EACpE;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACgB,OAAO,EAAE;MACd,IAAI,CAACW,YAAY,GAAG3B,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACU,KAAK,CAACV,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;EACI,IAAID,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACiB,OAAO,GAAG,IAAI,CAACY,YAAY,GAAG,IAAI,CAAClB,KAAK,CAACX,WAAW;EACpE;EACA,IAAIA,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACiB,OAAO,EAAE;MACd,IAAI,CAACY,YAAY,GAAG7B,WAAW;IACnC,CAAC,MACI;MACD,IAAI,CAACW,KAAK,CAACX,WAAW,GAAGA,WAAW;IACxC;EACJ;EACA;AACJ;AACA;EACI,IAAIE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACe,OAAO,GAAG,IAAI,CAACO,MAAM,GAAG,IAAI,CAACb,KAAK,CAACT,KAAK;EACxD;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACe,OAAO,EAAE;MACd,IAAI,CAACO,MAAM,GAAGtB,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAACS,KAAK,CAACT,KAAK,GAAGA,KAAK;IAC5B;EACJ;EACA;AACJ;AACA;EACI,IAAIN,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACqB,OAAO,GAAG,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACd,KAAK,CAACf,YAAY;EACtE;EACA,IAAIA,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACqB,OAAO,EAAE;MACd,IAAI,CAACQ,aAAa,GAAG7B,YAAY;IACrC,CAAC,MACI;MACD,IAAI,CAACe,KAAK,CAACf,YAAY,GAAGA,YAAY;IAC1C;EACJ;EACA;AACJ;AACA;EACI+B,cAAcA,CAACjG,IAAI,EAAE;IACjB,IAAIA,IAAI,KAAK,IAAI,CAACuE,WAAW,EAAE;MAC3B;IACJ;IACA,IAAImB,OAAO,GAAG,IAAI,CAACU,oBAAoB,CAACpG,IAAI,CAAC;IAC7C,IAAI,OAAO0F,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,IAAI,CAACnB,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACA,WAAW,GAAGvE,IAAI;MAC3B;MACA0F,OAAO,CAACW,IAAI,CAAC7G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB8G,SAAS,CAAEC,GAAG,IAAK;QACpB,IAAI,CAACC,iBAAiB,CAACxG,IAAI,CAAC;MAChC,CAAC,CAAC;IACN,CAAC,MACI;MAAE;MACH,IAAI,CAACwG,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIyG,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClC,WAAW;EAC3B;EACA;AACJ;AACA;EACImC,GAAGA,CAAC1G,IAAI,EAAE;IACN;IACA,IAAIA,IAAI,KAAK,IAAI,CAACsE,WAAW,EAAE;MAC3B,OAAOnF,EAAE,CAAC,IAAI,CAAC+E,YAAY,CAAClE,IAAI,CAAC,CAAC;IACtC;IACA,IAAI0F,OAAO,GAAG,IAAI,CAACU,oBAAoB,CAACpG,IAAI,CAAC;IAC7C,IAAI,OAAO0F,OAAO,KAAK,WAAW,EAAE;MAChC;MACA,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAGtE,IAAI;MAC3B;MACA0F,OAAO,CAACW,IAAI,CAAC7G,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB8G,SAAS,CAAEC,GAAG,IAAK;QACpB,IAAI,CAACI,UAAU,CAAC3G,IAAI,CAAC;MACzB,CAAC,CAAC;MACF,OAAO0F,OAAO;IAClB,CAAC,MACI;MAAE;MACH,IAAI,CAACiB,UAAU,CAAC3G,IAAI,CAAC;MACrB,OAAOb,EAAE,CAAC,IAAI,CAAC+E,YAAY,CAAClE,IAAI,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;EACIoG,oBAAoBA,CAACpG,IAAI,EAAE;IACvB,IAAI0F,OAAO;IACX;IACA,IAAI,OAAO,IAAI,CAACxB,YAAY,CAAClE,IAAI,CAAC,KAAK,WAAW,IAAI,IAAI,CAACwF,MAAM,EAAE;MAC/D,IAAI,CAACQ,oBAAoB,CAAChG,IAAI,CAAC,GAAG,IAAI,CAACgG,oBAAoB,CAAChG,IAAI,CAAC,IAAI,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;MAC9F0F,OAAO,GAAG,IAAI,CAACM,oBAAoB,CAAChG,IAAI,CAAC;IAC7C;IACA,OAAO0F,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACI3F,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAAC0F,OAAO,GAAG,IAAI;IACnB,MAAMkB,mBAAmB,GAAG,IAAI,CAAC1B,aAAa,CAACnF,cAAc,CAACC,IAAI,CAAC,CAACqG,IAAI,CAAC5G,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACjG,IAAI,CAACoH,mBAAmB,GAAGA,mBAAmB,CAACP,IAAI,CAAC3G,GAAG,CAAE6G,GAAG,IAAK,IAAI,CAACpB,QAAQ,CAAClB,mBAAmB,CAACsC,GAAG,EAAEvG,IAAI,CAAC,CAAC,EAAEP,WAAW,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;IACxI,IAAI,CAACoH,mBAAmB,CACnBN,SAAS,CAAC;MACXO,IAAI,EAAGN,GAAG,IAAK;QACX,IAAI,CAACrC,YAAY,CAAClE,IAAI,CAAC,GAAG,IAAI,CAACwF,MAAM,IAAI,IAAI,CAACtB,YAAY,CAAClE,IAAI,CAAC,GAAG;UAAE,GAAGuG,GAAG;UAAE,GAAG,IAAI,CAACrC,YAAY,CAAClE,IAAI;QAAE,CAAC,GAAGuG,GAAG;QAC/G,IAAI,CAACO,WAAW,CAAC,CAAC;QAClB,IAAI,CAACpB,OAAO,GAAG,KAAK;MACxB,CAAC;MACDqB,KAAK,EAAGC,GAAG,IAAK;QACZ,IAAI,CAACtB,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;IACF,OAAOkB,mBAAmB;EAC9B;EACA;AACJ;AACA;AACA;EACIK,cAAcA,CAACjH,IAAI,EAAEkE,YAAY,EAAEgD,WAAW,GAAG,KAAK,EAAE;IACpDhD,YAAY,GAAG,IAAI,CAACiB,QAAQ,CAAClB,mBAAmB,CAACC,YAAY,EAAElE,IAAI,CAAC;IACpE,IAAI,CAACkH,WAAW,IAAI,IAAI,CAAC1B,MAAM,KAAK,IAAI,CAACtB,YAAY,CAAClE,IAAI,CAAC,EAAE;MACzD,IAAI,CAACkE,YAAY,CAAClE,IAAI,CAAC,GAAGkC,SAAS,CAAC,IAAI,CAACgC,YAAY,CAAClE,IAAI,CAAC,EAAEkE,YAAY,CAAC;IAC9E,CAAC,MACI;MACD,IAAI,CAACA,YAAY,CAAClE,IAAI,CAAC,GAAGkE,YAAY;IAC1C;IACA,IAAI,CAAC4C,WAAW,CAAC,CAAC;IAClB,IAAI,CAACrC,mBAAmB,CAAC0C,IAAI,CAAC;MAAEnH,IAAI,EAAEA,IAAI;MAAEkE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAClE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACIoH,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC5C,KAAK;EACrB;EACA;AACJ;AACA;EACI6C,QAAQA,CAAC7C,KAAK,EAAE;IACZA,KAAK,CAAChC,OAAO,CAAExC,IAAI,IAAK;MACpB,IAAI,IAAI,CAACwE,KAAK,CAAC8C,OAAO,CAACtH,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QACjC,IAAI,CAACwE,KAAK,CAAC+C,IAAI,CAACvH,IAAI,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI8G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACO,QAAQ,CAACzF,MAAM,CAACW,IAAI,CAAC,IAAI,CAAC2B,YAAY,CAAC,CAAC;EACjD;EACA;AACJ;AACA;EACIsD,eAAeA,CAACtD,YAAY,EAAEjD,GAAG,EAAEwG,iBAAiB,EAAE;IAClD,IAAIlB,GAAG;IACP,IAAItF,GAAG,YAAYS,KAAK,EAAE;MACtB,IAAIsB,MAAM,GAAG,CAAC,CAAC;QAAE0E,WAAW,GAAG,KAAK;MACpC,KAAK,IAAIC,CAAC,IAAI1G,GAAG,EAAE;QACf+B,MAAM,CAAC2E,CAAC,CAAC,GAAG,IAAI,CAACH,eAAe,CAACtD,YAAY,EAAEyD,CAAC,EAAEF,iBAAiB,CAAC;QACpE,IAAIrI,YAAY,CAAC4D,MAAM,CAAC2E,CAAC,CAAC,CAAC,EAAE;UACzBD,WAAW,GAAG,IAAI;QACtB;MACJ;MACA,IAAIA,WAAW,EAAE;QACb,MAAME,OAAO,GAAG3G,GAAG,CAACvB,GAAG,CAACiI,CAAC,IAAIvI,YAAY,CAAC4D,MAAM,CAAC2E,CAAC,CAAC,CAAC,GAAG3E,MAAM,CAAC2E,CAAC,CAAC,GAAGxI,EAAE,CAAC6D,MAAM,CAAC2E,CAAC,CAAC,CAAC,CAAC;QACjF,OAAOtI,QAAQ,CAACuI,OAAO,CAAC,CAACvB,IAAI,CAAC3G,GAAG,CAAEmI,GAAG,IAAK;UACvC,IAAIC,GAAG,GAAG,CAAC,CAAC;UACZD,GAAG,CAACrF,OAAO,CAAC,CAACT,KAAK,EAAEgG,KAAK,KAAK;YAC1BD,GAAG,CAAC7G,GAAG,CAAC8G,KAAK,CAAC,CAAC,GAAGhG,KAAK;UAC3B,CAAC,CAAC;UACF,OAAO+F,GAAG;QACd,CAAC,CAAC,CAAC;MACP;MACA,OAAO9E,MAAM;IACjB;IACA,IAAIkB,YAAY,EAAE;MACdqC,GAAG,GAAG,IAAI,CAACnB,MAAM,CAACtC,WAAW,CAAC,IAAI,CAACsC,MAAM,CAACjC,QAAQ,CAACe,YAAY,EAAEjD,GAAG,CAAC,EAAEwG,iBAAiB,CAAC;IAC7F;IACA,IAAI,OAAOlB,GAAG,KAAK,WAAW,IAAI,IAAI,CAAChC,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,KAAK,IAAI,CAACD,WAAW,IAAI,IAAI,CAACgB,cAAc,EAAE;MACxHiB,GAAG,GAAG,IAAI,CAACnB,MAAM,CAACtC,WAAW,CAAC,IAAI,CAACsC,MAAM,CAACjC,QAAQ,CAAC,IAAI,CAACe,YAAY,CAAC,IAAI,CAACK,WAAW,CAAC,EAAEtD,GAAG,CAAC,EAAEwG,iBAAiB,CAAC;IACpH;IACA,IAAI,OAAOlB,GAAG,KAAK,WAAW,EAAE;MAC5B,IAAIvF,MAAM,GAAG;QAAEC,GAAG;QAAE+G,gBAAgB,EAAE;MAAK,CAAC;MAC5C,IAAI,OAAOP,iBAAiB,KAAK,WAAW,EAAE;QAC1CzG,MAAM,CAACyG,iBAAiB,GAAGA,iBAAiB;MAChD;MACAlB,GAAG,GAAG,IAAI,CAAClB,yBAAyB,CAACtE,MAAM,CAACC,MAAM,CAAC;IACvD;IACA,OAAO,OAAOuF,GAAG,KAAK,WAAW,GAAGA,GAAG,GAAGtF,GAAG;EACjD;EACA;AACJ;AACA;AACA;EACIgH,GAAGA,CAAChH,GAAG,EAAEwG,iBAAiB,EAAE;IACxB,IAAI,CAAC3F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAI0G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA;IACA,IAAI,IAAI,CAACxC,OAAO,EAAE;MACd,OAAO,IAAI,CAACkB,mBAAmB,CAACP,IAAI,CAAC1G,SAAS,CAAE4G,GAAG,IAAK;QACpDA,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACjB,GAAG,EAAEtF,GAAG,EAAEwG,iBAAiB,CAAC;QACvD,OAAOrI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;MAC5C,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,IAAIA,GAAG,GAAG,IAAI,CAACiB,eAAe,CAAC,IAAI,CAACtD,YAAY,CAAC,IAAI,CAACI,WAAW,CAAC,EAAErD,GAAG,EAAEwG,iBAAiB,CAAC;MAC3F,OAAOrI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4B,4BAA4BA,CAAClH,GAAG,EAAEwG,iBAAiB,EAAE;IACjD,IAAI,CAAC3F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAI0G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA,OAAO5I,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAAC0I,GAAG,CAAChH,GAAG,EAAEwG,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAChD,mBAAmB,CAAC4B,IAAI,CAACzG,SAAS,CAAEwI,KAAK,IAAK;MAC5G,MAAM7B,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACY,KAAK,CAAClE,YAAY,EAAEjD,GAAG,EAAEwG,iBAAiB,CAAC;MAC5E,IAAI,OAAOlB,GAAG,CAACD,SAAS,KAAK,UAAU,EAAE;QACrC,OAAOC,GAAG;MACd,CAAC,MACI;QACD,OAAOpH,EAAE,CAACoH,GAAG,CAAC;MAClB;IACJ,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACI8B,MAAMA,CAACpH,GAAG,EAAEwG,iBAAiB,EAAE;IAC3B,IAAI,CAAC3F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAI0G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA,OAAO5I,MAAM,CAACC,KAAK,CAAC,MAAM,IAAI,CAAC0I,GAAG,CAAChH,GAAG,EAAEwG,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC/C,YAAY,CAAC2B,IAAI,CAACzG,SAAS,CAAEwI,KAAK,IAAK;MACrG,MAAM7B,GAAG,GAAG,IAAI,CAACiB,eAAe,CAACY,KAAK,CAAClE,YAAY,EAAEjD,GAAG,EAAEwG,iBAAiB,CAAC;MAC5E,OAAOrI,YAAY,CAACmH,GAAG,CAAC,GAAGA,GAAG,GAAGpH,EAAE,CAACoH,GAAG,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC;EACR;EACA;AACJ;AACA;AACA;EACI+B,OAAOA,CAACrH,GAAG,EAAEwG,iBAAiB,EAAE;IAC5B,IAAI,CAAC3F,SAAS,CAACb,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,MAAM,EAAE;MAChC,MAAM,IAAI0G,KAAK,CAAE,0BAAyB,CAAC;IAC/C;IACA,IAAI3B,GAAG,GAAG,IAAI,CAACiB,eAAe,CAAC,IAAI,CAACtD,YAAY,CAAC,IAAI,CAACI,WAAW,CAAC,EAAErD,GAAG,EAAEwG,iBAAiB,CAAC;IAC3F,IAAIrI,YAAY,CAACmH,GAAG,CAAC,EAAE;MACnB,IAAItF,GAAG,YAAYS,KAAK,EAAE;QACtB,IAAIoG,GAAG,GAAG,CAAC,CAAC;QACZ7G,GAAG,CAACuB,OAAO,CAAC,CAACT,KAAK,EAAEgG,KAAK,KAAK;UAC1BD,GAAG,CAAC7G,GAAG,CAAC8G,KAAK,CAAC,CAAC,GAAG9G,GAAG,CAAC8G,KAAK,CAAC;QAChC,CAAC,CAAC;QACF,OAAOD,GAAG;MACd;MACA,OAAO7G,GAAG;IACd,CAAC,MACI;MACD,OAAOsF,GAAG;IACd;EACJ;EACA;AACJ;AACA;EACIgC,GAAGA,CAACtH,GAAG,EAAEc,KAAK,EAAE/B,IAAI,GAAG,IAAI,CAACsE,WAAW,EAAE;IACrC,IAAI,CAACJ,YAAY,CAAClE,IAAI,CAAC,CAACiB,GAAG,CAAC,GAAG,IAAI,CAACkE,QAAQ,CAACnB,OAAO,CAACjC,KAAK,EAAE/B,IAAI,CAAC;IACjE,IAAI,CAAC8G,WAAW,CAAC,CAAC;IAClB,IAAI,CAACrC,mBAAmB,CAAC0C,IAAI,CAAC;MAAEnH,IAAI,EAAEA,IAAI;MAAEkE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAClE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACI2G,UAAUA,CAAC3G,IAAI,EAAE;IACb,IAAI,CAACsE,WAAW,GAAGtE,IAAI;IACvB,IAAI,CAAC0E,YAAY,CAACyC,IAAI,CAAC;MAAEnH,IAAI,EAAEA,IAAI;MAAEkE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAClE,IAAI;IAAE,CAAC,CAAC;IAC7E;IACA,IAAI,IAAI,CAACuE,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACiC,iBAAiB,CAACxG,IAAI,CAAC;IAChC;EACJ;EACA;AACJ;AACA;EACIwG,iBAAiBA,CAACxG,IAAI,EAAE;IACpB,IAAI,CAACuE,WAAW,GAAGvE,IAAI;IACvB,IAAI,CAAC2E,mBAAmB,CAACwC,IAAI,CAAC;MAAEnH,IAAI,EAAEA,IAAI;MAAEkE,YAAY,EAAE,IAAI,CAACA,YAAY,CAAClE,IAAI;IAAE,CAAC,CAAC;EACxF;EACA;AACJ;AACA;EACIwI,UAAUA,CAACxI,IAAI,EAAE;IACb,IAAI,CAACyI,SAAS,CAACzI,IAAI,CAAC;IACpB,OAAO,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;EACpC;EACA;AACJ;AACA;EACIyI,SAASA,CAACzI,IAAI,EAAE;IACZ,IAAI,CAACgG,oBAAoB,CAAChG,IAAI,CAAC,GAAGsD,SAAS;IAC3C,IAAI,CAACY,YAAY,CAAClE,IAAI,CAAC,GAAGsD,SAAS;EACvC;EACA;AACJ;AACA;EACIoF,cAAcA,CAAA,EAAG;IACb,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOtF,SAAS;IACpB;IACA,IAAIuF,WAAW,GAAGF,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACnFD,WAAW,GAAGA,WAAW,IAAIF,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IAC3H,IAAI,OAAOJ,WAAW,KAAK,WAAW,EAAE;MACpC,OAAOvF,SAAS;IACpB;IACA,IAAIuF,WAAW,CAACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCuB,WAAW,GAAGA,WAAW,CAACzF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,IAAIyF,WAAW,CAACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCuB,WAAW,GAAGA,WAAW,CAACzF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOyF,WAAW;EACtB;EACA;AACJ;AACA;EACIK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,OAAOP,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,EAAE;MAC1E,OAAOtF,SAAS;IACpB;IACA,IAAI6F,kBAAkB,GAAGR,MAAM,CAACC,SAAS,CAACE,SAAS,GAAGH,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1FK,kBAAkB,GAAGA,kBAAkB,IAAIR,MAAM,CAACC,SAAS,CAACG,QAAQ,IAAIJ,MAAM,CAACC,SAAS,CAACI,eAAe,IAAIL,MAAM,CAACC,SAAS,CAACK,YAAY;IACzI,OAAOE,kBAAkB;EAC7B;AACJ;AACAnE,gBAAgB,CAAC/E,IAAI,YAAAmJ,yBAAAhJ,CAAA;EAAA,YAAAA,CAAA,IAAwF4E,gBAAgB,EA3oBvBtG,EAAE,CAAA2K,QAAA,CA2oBuChF,cAAc,GA3oBvD3F,EAAE,CAAA2K,QAAA,CA2oBkExJ,eAAe,GA3oBnFnB,EAAE,CAAA2K,QAAA,CA2oB8FvF,iBAAiB,GA3oBjHpF,EAAE,CAAA2K,QAAA,CA2oB4H5G,eAAe,GA3oB7I/D,EAAE,CAAA2K,QAAA,CA2oBwJxI,yBAAyB,GA3oBnLnC,EAAE,CAAA2K,QAAA,CA2oB8LxE,gBAAgB,GA3oBhNnG,EAAE,CAAA2K,QAAA,CA2oB2NzE,SAAS,GA3oBtOlG,EAAE,CAAA2K,QAAA,CA2oBiPtE,UAAU,GA3oB7PrG,EAAE,CAAA2K,QAAA,CA2oBwQvE,gBAAgB;AAAA,CAA6C;AAC7aE,gBAAgB,CAAC1E,KAAK,kBA5oBgF5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EA4oBSwE,gBAAgB;EAAAvE,OAAA,EAAhBuE,gBAAgB,CAAA/E;AAAA,EAAG;AACpI;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7oBsGhC,EAAE,CAAAiC,iBAAA,CA6oBbqE,gBAAgB,EAAc,CAAC;IAC9GpE,IAAI,EAAEjC;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEiC,IAAI,EAAEyD;IAAe,CAAC,EAAE;MAAEzD,IAAI,EAAEf;IAAgB,CAAC,EAAE;MAAEe,IAAI,EAAEkD;IAAkB,CAAC,EAAE;MAAElD,IAAI,EAAE6B;IAAgB,CAAC,EAAE;MAAE7B,IAAI,EAAEC;IAA0B,CAAC,EAAE;MAAED,IAAI,EAAE0C,SAAS;MAAEgG,UAAU,EAAE,CAAC;QAChN1I,IAAI,EAAE9B,MAAM;QACZyK,IAAI,EAAE,CAAC1E,gBAAgB;MAC3B,CAAC;IAAE,CAAC,EAAE;MAAEjE,IAAI,EAAE0C,SAAS;MAAEgG,UAAU,EAAE,CAAC;QAClC1I,IAAI,EAAE9B,MAAM;QACZyK,IAAI,EAAE,CAAC3E,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAEhE,IAAI,EAAE0C,SAAS;MAAEgG,UAAU,EAAE,CAAC;QAClC1I,IAAI,EAAE9B,MAAM;QACZyK,IAAI,EAAE,CAACxE,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEnE,IAAI,EAAE0C,SAAS;MAAEgG,UAAU,EAAE,CAAC;QAClC1I,IAAI,EAAE9B,MAAM;QACZyK,IAAI,EAAE,CAACzE,gBAAgB;MAC3B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM0E,kBAAkB,CAAC;EACrB7G,WAAWA,CAACqF,gBAAgB,EAAEyB,OAAO,EAAEC,IAAI,EAAE;IACzC,IAAI,CAAC1B,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACyB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC3B,gBAAgB,CAACvD,mBAAmB,CAAC6B,SAAS,CAAE8B,KAAK,IAAK;QACzF,IAAIA,KAAK,CAACpI,IAAI,KAAK,IAAI,CAACgI,gBAAgB,CAAC1D,WAAW,EAAE;UAClD,IAAI,CAACsF,UAAU,CAAC,IAAI,EAAExB,KAAK,CAAClE,YAAY,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAAC2F,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC7B,gBAAgB,CAACtD,YAAY,CAAC4B,SAAS,CAAE8B,KAAK,IAAK;QAC3E,IAAI,CAACwB,UAAU,CAAC,IAAI,EAAExB,KAAK,CAAClE,YAAY,CAAC;MAC7C,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAAC4F,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC9B,gBAAgB,CAACrD,mBAAmB,CAAC2B,SAAS,CAAE8B,KAAK,IAAK;QACzF,IAAI,CAACwB,UAAU,CAAC,IAAI,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACA,IAAIG,SAASA,CAAC9I,GAAG,EAAE;IACf,IAAIA,GAAG,EAAE;MACL,IAAI,CAACA,GAAG,GAAGA,GAAG;MACd,IAAI,CAAC2I,UAAU,CAAC,CAAC;IACrB;EACJ;EACA,IAAII,eAAeA,CAAChJ,MAAM,EAAE;IACxB,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC8I,aAAa,EAAEjJ,MAAM,CAAC,EAAE;MACrC,IAAI,CAACiJ,aAAa,GAAGjJ,MAAM;MAC3B,IAAI,CAAC4I,UAAU,CAAC,IAAI,CAAC;IACzB;EACJ;EACAM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,UAAU,CAAC,CAAC;EACrB;EACAA,UAAUA,CAACO,WAAW,GAAG,KAAK,EAAEjG,YAAY,EAAE;IAC1C,IAAIkG,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,aAAa,CAACC,UAAU;IACjD;IACA,IAAI,CAACF,KAAK,CAAC5I,MAAM,EAAE;MACf;MACA,IAAI,CAAC+I,UAAU,CAAC,IAAI,CAACd,OAAO,CAACY,aAAa,EAAE,IAAI,CAACpJ,GAAG,CAAC;MACrDmJ,KAAK,GAAG,IAAI,CAACX,OAAO,CAACY,aAAa,CAACC,UAAU;IACjD;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAC5I,MAAM,EAAE,EAAEgJ,CAAC,EAAE;MACnC,IAAIC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;MACnB,IAAIC,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;QAAE;QACvB,IAAIzJ,GAAG;QACP,IAAIkJ,WAAW,EAAE;UACbM,IAAI,CAACE,OAAO,GAAG,IAAI;QACvB;QACA,IAAI7I,SAAS,CAAC2I,IAAI,CAACG,SAAS,CAAC,EAAE;UAC3B3J,GAAG,GAAGwJ,IAAI,CAACG,SAAS;QACxB,CAAC,MACI,IAAI,IAAI,CAAC3J,GAAG,EAAE;UACfA,GAAG,GAAG,IAAI,CAACA,GAAG;QAClB,CAAC,MACI;UACD,IAAI4J,OAAO,GAAG,IAAI,CAACC,UAAU,CAACL,IAAI,CAAC;UACnC,IAAIM,cAAc,GAAGF,OAAO,CAACG,IAAI,CAAC,CAAC;UACnC,IAAID,cAAc,CAACvJ,MAAM,EAAE;YACvBiJ,IAAI,CAACG,SAAS,GAAGG,cAAc;YAC/B;YACA,IAAIF,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cAC/BhK,GAAG,GAAG8J,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D,CAAC,MACI,IAAIT,IAAI,CAACS,eAAe,EAAE;cAAE;cAC7B;cACAjK,GAAG,GAAGwJ,IAAI,CAACS,eAAe,CAACF,IAAI,CAAC,CAAC;YACrC,CAAC,MACI,IAAIH,OAAO,KAAKJ,IAAI,CAACQ,YAAY,EAAE;cACpC;cACAhK,GAAG,GAAG8J,cAAc;cACpB;cACAN,IAAI,CAACS,eAAe,GAAGL,OAAO,IAAIJ,IAAI,CAACS,eAAe;YAC1D;UACJ;QACJ;QACA,IAAI,CAACC,WAAW,CAAClK,GAAG,EAAEwJ,IAAI,EAAEvG,YAAY,CAAC;MAC7C;IACJ;EACJ;EACAiH,WAAWA,CAAClK,GAAG,EAAEwJ,IAAI,EAAEvG,YAAY,EAAE;IACjC,IAAIjD,GAAG,EAAE;MACL,IAAIwJ,IAAI,CAACE,OAAO,KAAK1J,GAAG,IAAI,IAAI,CAACmK,UAAU,KAAK,IAAI,CAACnB,aAAa,EAAE;QAChE;MACJ;MACA,IAAI,CAACmB,UAAU,GAAG,IAAI,CAACnB,aAAa;MACpC,IAAIoB,aAAa,GAAI9E,GAAG,IAAK;QACzB,IAAIA,GAAG,KAAKtF,GAAG,EAAE;UACbwJ,IAAI,CAACE,OAAO,GAAG1J,GAAG;QACtB;QACA,IAAI,CAACwJ,IAAI,CAACS,eAAe,EAAE;UACvBT,IAAI,CAACS,eAAe,GAAG,IAAI,CAACJ,UAAU,CAACL,IAAI,CAAC;QAChD;QACAA,IAAI,CAACQ,YAAY,GAAGnJ,SAAS,CAACyE,GAAG,CAAC,GAAGA,GAAG,GAAIkE,IAAI,CAACS,eAAe,IAAIjK,GAAI;QACxE;QACA,IAAI,CAACsJ,UAAU,CAACE,IAAI,EAAE,IAAI,CAACxJ,GAAG,GAAGwJ,IAAI,CAACQ,YAAY,GAAGR,IAAI,CAACS,eAAe,CAAC1H,OAAO,CAACvC,GAAG,EAAEwJ,IAAI,CAACQ,YAAY,CAAC,CAAC;QAC1G,IAAI,CAACvB,IAAI,CAAC4B,YAAY,CAAC,CAAC;MAC5B,CAAC;MACD,IAAIxJ,SAAS,CAACoC,YAAY,CAAC,EAAE;QACzB,IAAIqC,GAAG,GAAG,IAAI,CAACyB,gBAAgB,CAACR,eAAe,CAACtD,YAAY,EAAEjD,GAAG,EAAE,IAAI,CAACgJ,aAAa,CAAC;QACtF,IAAI7K,YAAY,CAACmH,GAAG,CAAC,EAAE;UACnBA,GAAG,CAACD,SAAS,CAAC;YAAEO,IAAI,EAAEwE;UAAc,CAAC,CAAC;QAC1C,CAAC,MACI;UACDA,aAAa,CAAC9E,GAAG,CAAC;QACtB;MACJ,CAAC,MACI;QACD,IAAI,CAACyB,gBAAgB,CAACC,GAAG,CAAChH,GAAG,EAAE,IAAI,CAACgJ,aAAa,CAAC,CAAC3D,SAAS,CAAC+E,aAAa,CAAC;MAC/E;IACJ;EACJ;EACAP,UAAUA,CAACL,IAAI,EAAE;IACb,OAAO3I,SAAS,CAAC2I,IAAI,CAACc,WAAW,CAAC,GAAGd,IAAI,CAACc,WAAW,GAAGd,IAAI,CAACe,IAAI;EACrE;EACAjB,UAAUA,CAACE,IAAI,EAAEI,OAAO,EAAE;IACtB,IAAI/I,SAAS,CAAC2I,IAAI,CAACc,WAAW,CAAC,EAAE;MAC7Bd,IAAI,CAACc,WAAW,GAAGV,OAAO;IAC9B,CAAC,MACI;MACDJ,IAAI,CAACe,IAAI,GAAGX,OAAO;IACvB;EACJ;EACAY,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC5B,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC6B,WAAW,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAAC5B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC/B,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC+B,WAAW,CAAC,CAAC;IAC7C;EACJ;AACJ;AACAlC,kBAAkB,CAACvJ,IAAI,YAAA0L,2BAAAvL,CAAA;EAAA,YAAAA,CAAA,IAAwFoJ,kBAAkB,EA7yB3B9K,EAAE,CAAAkN,iBAAA,CA6yB2C5G,gBAAgB,GA7yB7DtG,EAAE,CAAAkN,iBAAA,CA6yBwElN,EAAE,CAACmN,UAAU,GA7yBvFnN,EAAE,CAAAkN,iBAAA,CA6yBkGlN,EAAE,CAACoN,iBAAiB;AAAA,CAA4C;AAC1QtC,kBAAkB,CAACuC,IAAI,kBA9yB+ErN,EAAE,CAAAsN,iBAAA;EAAApL,IAAA,EA8yBL4I,kBAAkB;EAAAyC,SAAA;EAAAC,MAAA;IAAAnC,SAAA;IAAAC,eAAA;EAAA;AAAA,EAAkI;AACvP;EAAA,QAAAtJ,SAAA,oBAAAA,SAAA,KA/yBsGhC,EAAE,CAAAiC,iBAAA,CA+yBb6I,kBAAkB,EAAc,CAAC;IAChH5I,IAAI,EAAE7B,SAAS;IACfwK,IAAI,EAAE,CAAC;MACC4C,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEvL,IAAI,EAAEoE;IAAiB,CAAC,EAAE;MAAEpE,IAAI,EAAElC,EAAE,CAACmN;IAAW,CAAC,EAAE;MAAEjL,IAAI,EAAElC,EAAE,CAACoN;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE/B,SAAS,EAAE,CAAC;MACzJnJ,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEgL,eAAe,EAAE,CAAC;MAClBpJ,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoN,aAAa,CAAC;EAChBzJ,WAAWA,CAACoH,SAAS,EAAEL,IAAI,EAAE;IACzB,IAAI,CAACK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3H,KAAK,GAAG,EAAE;IACf,IAAI,CAAC4I,OAAO,GAAG,IAAI;IACnB,IAAI,CAACS,UAAU,GAAG,EAAE;EACxB;EACAD,WAAWA,CAAClK,GAAG,EAAEwG,iBAAiB,EAAEvD,YAAY,EAAE;IAC9C,IAAImH,aAAa,GAAI9E,GAAG,IAAK;MACzB,IAAI,CAACxE,KAAK,GAAGwE,GAAG,KAAKjD,SAAS,GAAGiD,GAAG,GAAGtF,GAAG;MAC1C,IAAI,CAAC0J,OAAO,GAAG1J,GAAG;MAClB,IAAI,CAACyI,IAAI,CAAC4B,YAAY,CAAC,CAAC;IAC5B,CAAC;IACD,IAAIpH,YAAY,EAAE;MACd,IAAIqC,GAAG,GAAG,IAAI,CAACwD,SAAS,CAACvC,eAAe,CAACtD,YAAY,EAAEjD,GAAG,EAAEwG,iBAAiB,CAAC;MAC9E,IAAIrI,YAAY,CAACmH,GAAG,CAACD,SAAS,CAAC,EAAE;QAC7BC,GAAG,CAACD,SAAS,CAAC+E,aAAa,CAAC;MAChC,CAAC,MACI;QACDA,aAAa,CAAC9E,GAAG,CAAC;MACtB;IACJ;IACA,IAAI,CAACwD,SAAS,CAAC9B,GAAG,CAAChH,GAAG,EAAEwG,iBAAiB,CAAC,CAACnB,SAAS,CAAC+E,aAAa,CAAC;EACvE;EACAgB,SAASA,CAACC,KAAK,EAAE,GAAG/C,IAAI,EAAE;IACtB,IAAI,CAAC+C,KAAK,IAAI,CAACA,KAAK,CAAC9K,MAAM,EAAE;MACzB,OAAO8K,KAAK;IAChB;IACA;IACA,IAAInL,MAAM,CAACmL,KAAK,EAAE,IAAI,CAAC3B,OAAO,CAAC,IAAIxJ,MAAM,CAACoI,IAAI,EAAE,IAAI,CAAC6B,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACrJ,KAAK;IACrB;IACA,IAAI0F,iBAAiB,GAAGnE,SAAS;IACjC,IAAIxB,SAAS,CAACyH,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC/H,MAAM,EAAE;MACnC,IAAI,OAAO+H,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC/H,MAAM,EAAE;QAC/C;QACA;QACA,IAAI+K,SAAS,GAAGhD,IAAI,CAAC,CAAC,CAAC,CAClB/F,OAAO,CAAC,kCAAkC,EAAE,OAAO,CAAC,CACpDA,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC;QAC7C,IAAI;UACAiE,iBAAiB,GAAG+E,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC;QAC7C,CAAC,CACD,OAAOG,CAAC,EAAE;UACN,MAAM,IAAIC,WAAW,CAAE,wEAAuEpD,IAAI,CAAC,CAAC,CAAE,EAAC,CAAC;QAC5G;MACJ,CAAC,MACI,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC7H,KAAK,CAACC,OAAO,CAAC4H,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7D9B,iBAAiB,GAAG8B,IAAI,CAAC,CAAC,CAAC;MAC/B;IACJ;IACA;IACA,IAAI,CAACoB,OAAO,GAAG2B,KAAK;IACpB;IACA,IAAI,CAAClB,UAAU,GAAG7B,IAAI;IACtB;IACA,IAAI,CAAC4B,WAAW,CAACmB,KAAK,EAAE7E,iBAAiB,CAAC;IAC1C;IACA,IAAI,CAACmF,QAAQ,CAAC,CAAC;IACf;IACA,IAAI,CAAC,IAAI,CAACnI,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACsF,SAAS,CAACtF,mBAAmB,CAAC6B,SAAS,CAAE8B,KAAK,IAAK;QAC/E,IAAI,IAAI,CAACuC,OAAO,IAAIvC,KAAK,CAACpI,IAAI,KAAK,IAAI,CAAC+J,SAAS,CAACzF,WAAW,EAAE;UAC3D,IAAI,CAACqG,OAAO,GAAG,IAAI;UACnB,IAAI,CAACQ,WAAW,CAACmB,KAAK,EAAE7E,iBAAiB,EAAEW,KAAK,CAAClE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACQ,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACqF,SAAS,CAACrF,YAAY,CAAC4B,SAAS,CAAE8B,KAAK,IAAK;QACjE,IAAI,IAAI,CAACuC,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACmB,KAAK,EAAE7E,iBAAiB,EAAEW,KAAK,CAAClE,YAAY,CAAC;QAClE;MACJ,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAAC,IAAI,CAACS,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACoF,SAAS,CAACpF,mBAAmB,CAAC2B,SAAS,CAAC,MAAM;QAC1E,IAAI,IAAI,CAACqE,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,GAAG,IAAI,CAAC,CAAC;UACrB,IAAI,CAACQ,WAAW,CAACmB,KAAK,EAAE7E,iBAAiB,CAAC;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAC1F,KAAK;EACrB;EACA;AACJ;AACA;EACI6K,QAAQA,CAAA,EAAG;IACP,IAAI,OAAO,IAAI,CAACnI,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAACiH,WAAW,CAAC,CAAC;MACtC,IAAI,CAACjH,mBAAmB,GAAGnB,SAAS;IACxC;IACA,IAAI,OAAO,IAAI,CAACoB,YAAY,KAAK,WAAW,EAAE;MAC1C,IAAI,CAACA,YAAY,CAACgH,WAAW,CAAC,CAAC;MAC/B,IAAI,CAAChH,YAAY,GAAGpB,SAAS;IACjC;IACA,IAAI,OAAO,IAAI,CAACqB,mBAAmB,KAAK,WAAW,EAAE;MACjD,IAAI,CAACA,mBAAmB,CAAC+G,WAAW,CAAC,CAAC;MACtC,IAAI,CAAC/G,mBAAmB,GAAGrB,SAAS;IACxC;EACJ;EACAmI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmB,QAAQ,CAAC,CAAC;EACnB;AACJ;AACAR,aAAa,CAACnM,IAAI,YAAA4M,sBAAAzM,CAAA;EAAA,YAAAA,CAAA,IAAwFgM,aAAa,EAx6BjB1N,EAAE,CAAAkN,iBAAA,CAw6BiC5G,gBAAgB,OAx6BnDtG,EAAE,CAAAkN,iBAAA,CAw6B8DlN,EAAE,CAACoN,iBAAiB;AAAA,CAAuC;AACjOM,aAAa,CAACU,KAAK,kBAz6BmFpO,EAAE,CAAAqO,YAAA;EAAAC,IAAA;EAAApM,IAAA,EAy6BAwL,aAAa;EAAAa,IAAA;AAAA,EAAmC;AACxJb,aAAa,CAAC9L,KAAK,kBA16BmF5B,EAAE,CAAA6B,kBAAA;EAAAC,KAAA,EA06BM4L,aAAa;EAAA3L,OAAA,EAAb2L,aAAa,CAAAnM;AAAA,EAAG;AAC9H;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA36BsGhC,EAAE,CAAAiC,iBAAA,CA26BbyL,aAAa,EAAc,CAAC;IAC3GxL,IAAI,EAAEjC;EACV,CAAC,EAAE;IACCiC,IAAI,EAAE3B,IAAI;IACVsK,IAAI,EAAE,CAAC;MACCyD,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,KAAK,CAAC;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErM,IAAI,EAAEoE;IAAiB,CAAC,EAAE;MAAEpE,IAAI,EAAElC,EAAE,CAACoN;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;AAEhH,MAAMoB,eAAe,CAAC;EAClB;AACJ;AACA;EACI,OAAOC,OAAOA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACG,MAAM,IAAI;QAAEC,OAAO,EAAE3N,eAAe;QAAE4N,QAAQ,EAAE3N;MAAoB,CAAC,EAC5EsN,MAAM,CAACjI,QAAQ,IAAI;QAAEqI,OAAO,EAAE1J,iBAAiB;QAAE2J,QAAQ,EAAE1J;MAAsB,CAAC,EAClFqJ,MAAM,CAAChI,MAAM,IAAI;QAAEoI,OAAO,EAAE/K,eAAe;QAAEgL,QAAQ,EAAE/K;MAAuB,CAAC,EAC/E0K,MAAM,CAAC/H,yBAAyB,IAAI;QAAEmI,OAAO,EAAE3M,yBAAyB;QAAE4M,QAAQ,EAAE3M;MAA8B,CAAC,EACnHuD,cAAc,EACd;QAAEmJ,OAAO,EAAE5I,SAAS;QAAE8I,QAAQ,EAAEN,MAAM,CAAC7H;MAAQ,CAAC,EAChD;QAAEiI,OAAO,EAAE3I,gBAAgB;QAAE6I,QAAQ,EAAEN,MAAM,CAAC9H;MAAe,CAAC,EAC9D;QAAEkI,OAAO,EAAEzI,UAAU;QAAE2I,QAAQ,EAAEN,MAAM,CAAC5H;MAAO,CAAC,EAChD;QAAEgI,OAAO,EAAE1I,gBAAgB;QAAE4I,QAAQ,EAAEN,MAAM,CAAC3H;MAAgB,CAAC,EAC/DT,gBAAgB;IAExB,CAAC;EACL;EACA;AACJ;AACA;EACI,OAAO2I,QAAQA,CAACP,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO;MACHC,QAAQ,EAAEH,eAAe;MACzBI,SAAS,EAAE,CACPF,MAAM,CAACG,MAAM,IAAI;QAAEC,OAAO,EAAE3N,eAAe;QAAE4N,QAAQ,EAAE3N;MAAoB,CAAC,EAC5EsN,MAAM,CAACjI,QAAQ,IAAI;QAAEqI,OAAO,EAAE1J,iBAAiB;QAAE2J,QAAQ,EAAE1J;MAAsB,CAAC,EAClFqJ,MAAM,CAAChI,MAAM,IAAI;QAAEoI,OAAO,EAAE/K,eAAe;QAAEgL,QAAQ,EAAE/K;MAAuB,CAAC,EAC/E0K,MAAM,CAAC/H,yBAAyB,IAAI;QAAEmI,OAAO,EAAE3M,yBAAyB;QAAE4M,QAAQ,EAAE3M;MAA8B,CAAC,EACnH;QAAE0M,OAAO,EAAE5I,SAAS;QAAE8I,QAAQ,EAAEN,MAAM,CAAC7H;MAAQ,CAAC,EAChD;QAAEiI,OAAO,EAAE3I,gBAAgB;QAAE6I,QAAQ,EAAEN,MAAM,CAAC9H;MAAe,CAAC,EAC9D;QAAEkI,OAAO,EAAEzI,UAAU;QAAE2I,QAAQ,EAAEN,MAAM,CAAC5H;MAAO,CAAC,EAChD;QAAEgI,OAAO,EAAE1I,gBAAgB;QAAE4I,QAAQ,EAAEN,MAAM,CAAC3H;MAAgB,CAAC,EAC/DT,gBAAgB;IAExB,CAAC;EACL;AACJ;AACAkI,eAAe,CAACjN,IAAI,YAAA2N,wBAAAxN,CAAA;EAAA,YAAAA,CAAA,IAAwF8M,eAAe;AAAA,CAAkD;AAC7KA,eAAe,CAACW,IAAI,kBA/9BkFnP,EAAE,CAAAoP,gBAAA;EAAAlN,IAAA,EA+9BKsM;AAAe,EAE9F;AAC9BA,eAAe,CAACa,IAAI,kBAl+BkFrP,EAAE,CAAAsP,gBAAA,IAk+BuB;AAC/H;EAAA,QAAAtN,SAAA,oBAAAA,SAAA,KAn+BsGhC,EAAE,CAAAiC,iBAAA,CAm+BbuM,eAAe,EAAc,CAAC;IAC7GtM,IAAI,EAAE1B,QAAQ;IACdqK,IAAI,EAAE,CAAC;MACC0E,YAAY,EAAE,CACV7B,aAAa,EACb5C,kBAAkB,CACrB;MACD0E,OAAO,EAAE,CACL9B,aAAa,EACb5C,kBAAkB;IAE1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS1E,gBAAgB,EAAEhE,6BAA6B,EAAED,yBAAyB,EAAEiD,iBAAiB,EAAEpB,sBAAsB,EAAE8G,kBAAkB,EAAEzF,qBAAqB,EAAEjE,mBAAmB,EAAED,eAAe,EAAEqN,eAAe,EAAEzK,eAAe,EAAE2J,aAAa,EAAEpH,gBAAgB,EAAEX,cAAc,EAAEQ,gBAAgB,EAAEE,UAAU,EAAEH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}