{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"ngx-owl-carousel-o\";\nimport * as i4 from \"../product-card/product-card.component\";\nfunction ProductSliderComponent_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mtn-product-card\", 4);\n  }\n  if (rf & 2) {\n    const newProduct_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currency\", ctx_r2.currency)(\"isDragging\", ctx_r2.isDragging)(\"product\", newProduct_r1);\n  }\n}\nfunction ProductSliderComponent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProductSliderComponent_2_ng_template_0_Template, 1, 3, \"ng-template\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"width\", ctx_r0.products.length < 6 ? 222 : 0);\n  }\n}\nexport class ProductSliderComponent {\n  constructor(store, reviewsService) {\n    this.store = store;\n    this.reviewsService = reviewsService;\n    this.options = {};\n    this.currency = {};\n    this.products = [];\n    this.isDragging = false;\n    this.newProduct = [];\n  }\n  ngOnInit() {\n    this.initData();\n    this.products.forEach(obj => {\n      let selectedVariance;\n      let defaultVariant = obj?.productVariances.find(variant => variant.isDefault);\n      if (defaultVariant) {\n        selectedVariance = defaultVariant;\n      } else {\n        let approvedVariant = obj?.productVariances.find(variant => !variant.soldOut);\n        if (approvedVariant) {\n          selectedVariance = approvedVariant;\n        } else {\n          selectedVariance = obj?.productVariances[0];\n        }\n      }\n      let features = [];\n      if (selectedVariance?.productFeaturesList) {\n        features = selectedVariance?.productFeaturesList[0]?.featureList;\n      }\n      this.newProduct.push({\n        productId: obj?.id,\n        productName: obj?.name,\n        priceValue: selectedVariance?.price,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: obj?.currencyCode,\n        masterImageUrl: selectedVariance?.masterImageUrl,\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        specProductId: selectedVariance.specProductId,\n        channelId: obj.channelId ?? 1,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: obj.shopId,\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated\n      });\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n  }\n  initData() {\n    this.options = {\n      stagePadding: 0,\n      loop: false,\n      autoplay: true,\n      dots: false,\n      autoWidth: true,\n      nav: true,\n      lazyLoad: true,\n      autoplayHoverPause: true,\n      rewind: false,\n      margin: 30,\n      navText: [],\n      responsive: {\n        0: {\n          items: 2,\n          nav: false\n        },\n        300: {\n          items: 2,\n          nav: false\n        },\n        400: {\n          items: 3,\n          nav: false\n        },\n        600: {\n          items: 4\n        },\n        740: {\n          items: 5\n        },\n        800: {\n          items: 6\n        },\n        940: {\n          items: 7\n        },\n        1280: {\n          items: 8\n        },\n        1300: {\n          items: 9\n        },\n        1400: {\n          items: 6\n        },\n        1600: {\n          items: 6\n        }\n      }\n    };\n  }\n  carouselDrag(event) {\n    this.isDragging = event;\n  }\n  static #_ = this.ɵfac = function ProductSliderComponent_Factory(t) {\n    return new (t || ProductSliderComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.ReviewsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProductSliderComponent,\n    selectors: [[\"app-mtn-product-slider\"]],\n    inputs: {\n      products: \"products\"\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[1, \"product-slider\", \"card-slider\"], [3, \"options\", \"dragging\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\", \"class\", \"slide\", 3, \"width\"], [2, \"width\", \"100%\", 3, \"currency\", \"isDragging\", \"product\"]],\n    template: function ProductSliderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"owl-carousel-o\", 1);\n        i0.ɵɵlistener(\"dragging\", function ProductSliderComponent_Template_owl_carousel_o_dragging_1_listener($event) {\n          return ctx.carouselDrag($event.dragging);\n        });\n        i0.ɵɵtemplate(2, ProductSliderComponent_2_Template, 1, 1, null, 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"options\", ctx.options);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.newProduct);\n      }\n    },\n    dependencies: [i2.NgForOf, i3.CarouselComponent, i3.CarouselSlideDirective, i4.ProductCardComponent],\n    styles: [\".product-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvcHJvZHVjdC1zbGlkZXIvcHJvZHVjdC1zbGlkZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvZHVjdC1zbGlkZXJ7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "currency", "isDragging", "newProduct_r1", "ɵɵtemplate", "ProductSliderComponent_2_ng_template_0_Template", "ctx_r0", "products", "length", "ProductSliderComponent", "constructor", "store", "reviewsService", "options", "newProduct", "ngOnInit", "initData", "for<PERSON>ach", "obj", "<PERSON><PERSON><PERSON><PERSON>", "defaultVariant", "productVariances", "find", "variant", "isDefault", "approvedVariant", "soldOut", "features", "productFeaturesList", "featureList", "push", "productId", "id", "productName", "name", "priceValue", "price", "salePriceValue", "salePrice", "currencyCode", "masterImageUrl", "thumbnailImages", "rate", "count", "specProductId", "channelId", "salePercent", "shopId", "isHot", "includes", "isNew", "isBest", "quantity", "proSchedulingId", "stockPerSKU", "sku", "skuAutoGenerated", "ngAfterViewInit", "setTimeout", "subscription", "subscribe", "next", "res", "stagePadding", "loop", "autoplay", "dots", "autoWidth", "nav", "lazyLoad", "autoplayHoverPause", "rewind", "margin", "navText", "responsive", "items", "carouselDrag", "event", "_", "ɵɵdirectiveInject", "i1", "StoreService", "ReviewsService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ProductSliderComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ProductSliderComponent_Template_owl_carousel_o_dragging_1_listener", "$event", "dragging", "ProductSliderComponent_2_Template", "ɵɵelementEnd", "ɵɵadvance"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\product-slider\\product-slider.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\product-slider\\product-slider.component.html"], "sourcesContent": ["import {Component, Input, OnInit} from '@angular/core';\r\nimport {OwlOptions} from \"ngx-owl-carousel-o\";\r\n\r\nimport {ProductRate, Currency} from '@core/interface';\r\n\r\nimport {ReviewsService, StoreService} from '@core/services';\r\n\r\n@Component({\r\n  selector: 'app-mtn-product-slider',\r\n  templateUrl: './product-slider.component.html',\r\n  styleUrls: ['./product-slider.component.scss']\r\n})\r\nexport class ProductSliderComponent implements OnInit {\r\n  options: OwlOptions = {} as OwlOptions;\r\n  currency: Currency = {} as Currency;\r\n  @Input() products: any=[];\r\n  reviews: ProductRate[] | undefined;\r\n\r\n  isDragging: boolean = false;\r\n  newProduct: any = [];\r\n\r\n  constructor(\r\n    private store: StoreService,\r\n    private reviewsService: ReviewsService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initData();\r\n\r\n\r\n    this.products.forEach((obj: any) => {\r\n      let selectedVariance;\r\n      let defaultVariant = obj?.productVariances.find((variant: any) => variant.isDefault)\r\n      if (defaultVariant) {\r\n        selectedVariance = defaultVariant;\r\n      } else {\r\n        let approvedVariant = obj?.productVariances.find((variant: any) => !variant.soldOut);\r\n        if (approvedVariant) {\r\n          selectedVariance = approvedVariant;\r\n\r\n        } else {\r\n          selectedVariance = obj?.productVariances[0];\r\n        }\r\n      }\r\n      let features=[];\r\n      if(selectedVariance?.productFeaturesList){\r\n        features=selectedVariance?.productFeaturesList[0]?.featureList;\r\n      }\r\n\r\n      this.newProduct.push({\r\n        productId: obj?.id,\r\n        productName: obj?.name,\r\n        priceValue: selectedVariance?.price,\r\n        salePriceValue: selectedVariance?.salePrice,\r\n        currencyCode: obj?.currencyCode,\r\n        masterImageUrl: selectedVariance?.masterImageUrl,\r\n        thumbnailImages: selectedVariance?.thumbnailImages,\r\n        soldOut: selectedVariance?.soldOut,\r\n        rate: selectedVariance?.rate,\r\n        count: selectedVariance?.count ?? 0,\r\n        specProductId: selectedVariance.specProductId,\r\n        channelId: obj.channelId ?? 1,\r\n        salePercent: selectedVariance?.salePrice?100 - (selectedVariance?.salePrice / selectedVariance?.price * 100):0,\r\n        shopId: obj.shopId,\r\n        isHot:features?.includes(1),\r\n        isNew:features?.includes(2),\r\n        isBest:features?.includes(3),\r\n        quantity:selectedVariance.quantity,\r\n        proSchedulingId:selectedVariance.proSchedulingId,\r\n        stockPerSKU:selectedVariance.stockPerSKU,\r\n        sku:selectedVariance?.sku,\r\n        skuAutoGenerated : selectedVariance.skuAutoGenerated\r\n\r\n      });\r\n\r\n\r\n    })\r\n\r\n  }\r\n\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n\r\n      this.store.subscription('currency')\r\n        .subscribe({\r\n          next: (res) => this.currency = res\r\n        });\r\n    }, 10);\r\n  }\r\n\r\n  initData(): void {\r\n    this.options = {\r\n      stagePadding: 0,\r\n      loop: false,\r\n      autoplay: true,\r\n      dots: false,\r\n      autoWidth: true,\r\n      nav: true,\r\n      lazyLoad: true,\r\n      autoplayHoverPause: true,\r\n\r\n      rewind: false,\r\n      margin: 30,\r\n      navText: [],\r\n      responsive: {\r\n        0: {\r\n          items: 2,\r\n          nav: false,\r\n        },\r\n        300: {\r\n          items: 2,\r\n          nav: false\r\n        },\r\n        400: {\r\n          items: 3,\r\n          nav: false\r\n        },\r\n        600: {\r\n          items: 4\r\n        },\r\n        740: {\r\n          items: 5\r\n        },\r\n        800: {\r\n          items: 6\r\n        },\r\n        940: {\r\n          items: 7\r\n        },\r\n        1280: {\r\n          items: 8\r\n        },\r\n        1300: {\r\n          items: 9\r\n        },\r\n        1400: {\r\n          items: 6\r\n        },\r\n        1600: {\r\n          items: 6\r\n        },\r\n      }\r\n    };\r\n  }\r\n\r\n  carouselDrag(event: boolean) {\r\n    this.isDragging = event;\r\n  }\r\n}\r\n", "<section class=\"product-slider card-slider\">\r\n  <owl-carousel-o\r\n    (dragging)=\"carouselDrag($event.dragging)\"\r\n    [options]=\"options\"\r\n  >\r\n    <ng-template\r\n      *ngFor=\"let newProduct of newProduct\"\r\n      [width]=\"products.length < 6 ? 222 : 0\"\r\n      carouselSlide\r\n      class=\"slide\"\r\n    >\r\n      <app-mtn-product-card\r\n        style=\"width:100%\"\r\n        [currency]=\"currency\"\r\n        [isDragging]=\"isDragging\"\r\n        [product]=\"newProduct\"\r\n      ></app-mtn-product-card>\r\n    </ng-template>\r\n  </owl-carousel-o>\r\n</section>\r\n"], "mappings": ";;;;;;;ICWMA,EAAA,CAAAC,SAAA,8BAKwB;;;;;IAHtBD,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAC,QAAA,CAAqB,eAAAD,MAAA,CAAAE,UAAA,aAAAC,aAAA;;;;;IARzBN,EAAA,CAAAO,UAAA,IAAAC,+CAAA,yBAYc;;;;IAVZR,EAAA,CAAAE,UAAA,UAAAO,MAAA,CAAAC,QAAA,CAAAC,MAAA,eAAuC;;;ADK7C,OAAM,MAAOC,sBAAsB;EASjCC,YACUC,KAAmB,EACnBC,cAA8B;IAD9B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAVxB,KAAAC,OAAO,GAAe,EAAgB;IACtC,KAAAZ,QAAQ,GAAa,EAAc;IAC1B,KAAAM,QAAQ,GAAM,EAAE;IAGzB,KAAAL,UAAU,GAAY,KAAK;IAC3B,KAAAY,UAAU,GAAQ,EAAE;EAMpB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IAGf,IAAI,CAACT,QAAQ,CAACU,OAAO,CAAEC,GAAQ,IAAI;MACjC,IAAIC,gBAAgB;MACpB,IAAIC,cAAc,GAAGF,GAAG,EAAEG,gBAAgB,CAACC,IAAI,CAAEC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;MACpF,IAAIJ,cAAc,EAAE;QAClBD,gBAAgB,GAAGC,cAAc;OAClC,MAAM;QACL,IAAIK,eAAe,GAAGP,GAAG,EAAEG,gBAAgB,CAACC,IAAI,CAAEC,OAAY,IAAK,CAACA,OAAO,CAACG,OAAO,CAAC;QACpF,IAAID,eAAe,EAAE;UACnBN,gBAAgB,GAAGM,eAAe;SAEnC,MAAM;UACLN,gBAAgB,GAAGD,GAAG,EAAEG,gBAAgB,CAAC,CAAC,CAAC;;;MAG/C,IAAIM,QAAQ,GAAC,EAAE;MACf,IAAGR,gBAAgB,EAAES,mBAAmB,EAAC;QACvCD,QAAQ,GAACR,gBAAgB,EAAES,mBAAmB,CAAC,CAAC,CAAC,EAAEC,WAAW;;MAGhE,IAAI,CAACf,UAAU,CAACgB,IAAI,CAAC;QACnBC,SAAS,EAAEb,GAAG,EAAEc,EAAE;QAClBC,WAAW,EAAEf,GAAG,EAAEgB,IAAI;QACtBC,UAAU,EAAEhB,gBAAgB,EAAEiB,KAAK;QACnCC,cAAc,EAAElB,gBAAgB,EAAEmB,SAAS;QAC3CC,YAAY,EAAErB,GAAG,EAAEqB,YAAY;QAC/BC,cAAc,EAAErB,gBAAgB,EAAEqB,cAAc;QAChDC,eAAe,EAAEtB,gBAAgB,EAAEsB,eAAe;QAClDf,OAAO,EAAEP,gBAAgB,EAAEO,OAAO;QAClCgB,IAAI,EAAEvB,gBAAgB,EAAEuB,IAAI;QAC5BC,KAAK,EAAExB,gBAAgB,EAAEwB,KAAK,IAAI,CAAC;QACnCC,aAAa,EAAEzB,gBAAgB,CAACyB,aAAa;QAC7CC,SAAS,EAAE3B,GAAG,CAAC2B,SAAS,IAAI,CAAC;QAC7BC,WAAW,EAAE3B,gBAAgB,EAAEmB,SAAS,GAAC,GAAG,GAAInB,gBAAgB,EAAEmB,SAAS,GAAGnB,gBAAgB,EAAEiB,KAAK,GAAG,GAAI,GAAC,CAAC;QAC9GW,MAAM,EAAE7B,GAAG,CAAC6B,MAAM;QAClBC,KAAK,EAACrB,QAAQ,EAAEsB,QAAQ,CAAC,CAAC,CAAC;QAC3BC,KAAK,EAACvB,QAAQ,EAAEsB,QAAQ,CAAC,CAAC,CAAC;QAC3BE,MAAM,EAACxB,QAAQ,EAAEsB,QAAQ,CAAC,CAAC,CAAC;QAC5BG,QAAQ,EAACjC,gBAAgB,CAACiC,QAAQ;QAClCC,eAAe,EAAClC,gBAAgB,CAACkC,eAAe;QAChDC,WAAW,EAACnC,gBAAgB,CAACmC,WAAW;QACxCC,GAAG,EAACpC,gBAAgB,EAAEoC,GAAG;QACzBC,gBAAgB,EAAGrC,gBAAgB,CAACqC;OAErC,CAAC;IAGJ,CAAC,CAAC;EAEJ;EAGAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAGd,IAAI,CAAC/C,KAAK,CAACgD,YAAY,CAAC,UAAU,CAAC,CAChCC,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAG,IAAK,IAAI,CAAC7D,QAAQ,GAAG6D;OAChC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC;EACR;EAEA9C,QAAQA,CAAA;IACN,IAAI,CAACH,OAAO,GAAG;MACbkD,YAAY,EAAE,CAAC;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAI;MACfC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,IAAI;MAExBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRP,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRP,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHO,KAAK,EAAE,CAAC;UACRP,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHO,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,IAAI,EAAE;UACJA,KAAK,EAAE;SACR;QACD,IAAI,EAAE;UACJA,KAAK,EAAE;SACR;QACD,IAAI,EAAE;UACJA,KAAK,EAAE;SACR;QACD,IAAI,EAAE;UACJA,KAAK,EAAE;;;KAGZ;EACH;EAEAC,YAAYA,CAACC,KAAc;IACzB,IAAI,CAAC3E,UAAU,GAAG2E,KAAK;EACzB;EAAC,QAAAC,CAAA,G;qBA1IUrE,sBAAsB,EAAAZ,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAE,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtB1E,sBAAsB;IAAA2E,SAAA;IAAAC,MAAA;MAAA9E,QAAA;IAAA;IAAA+E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZnC9F,EAAA,CAAAgG,cAAA,iBAA4C;QAExChG,EAAA,CAAAiG,UAAA,sBAAAC,mEAAAC,MAAA;UAAA,OAAYJ,GAAA,CAAAhB,YAAA,CAAAoB,MAAA,CAAAC,QAAA,CAA6B;QAAA,EAAC;QAG1CpG,EAAA,CAAAO,UAAA,IAAA8F,iCAAA,gBAYc;QAChBrG,EAAA,CAAAsG,YAAA,EAAiB;;;QAfftG,EAAA,CAAAuG,SAAA,GAAmB;QAAnBvG,EAAA,CAAAE,UAAA,YAAA6F,GAAA,CAAA/E,OAAA,CAAmB;QAGMhB,EAAA,CAAAuG,SAAA,GAAa;QAAbvG,EAAA,CAAAE,UAAA,YAAA6F,GAAA,CAAA9E,UAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}