{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() {}\nconst hasXHR2 = function () {\n  const xhr = new XMLHttpRequest({\n    xdomain: false\n  });\n  return null != xhr.responseType;\n}();\nexport class Polling extends Transport {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @package\n   */\n  constructor(opts) {\n    super(opts);\n    this.polling = false;\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? \"443\" : \"80\";\n      }\n      this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n    if (this.opts.withCredentials) {\n      this.cookieJar = createCookieJar();\n    }\n  }\n  get name() {\n    return \"polling\";\n  }\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @protected\n   */\n  doOpen() {\n    this.poll();\n  }\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n   * @package\n   */\n  pause(onPause) {\n    this.readyState = \"pausing\";\n    const pause = () => {\n      this.readyState = \"paused\";\n      onPause();\n    };\n    if (this.polling || !this.writable) {\n      let total = 0;\n      if (this.polling) {\n        total++;\n        this.once(\"pollComplete\", function () {\n          --total || pause();\n        });\n      }\n      if (!this.writable) {\n        total++;\n        this.once(\"drain\", function () {\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n  /**\n   * Starts polling cycle.\n   *\n   * @private\n   */\n  poll() {\n    this.polling = true;\n    this.doPoll();\n    this.emitReserved(\"poll\");\n  }\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @protected\n   */\n  onData(data) {\n    const callback = packet => {\n      // if its the first message we consider the transport open\n      if (\"opening\" === this.readyState && packet.type === \"open\") {\n        this.onOpen();\n      }\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        this.onClose({\n          description: \"transport closed by the server\"\n        });\n        return false;\n      }\n      // otherwise bypass onData and handle the message\n      this.onPacket(packet);\n    };\n    // decode payload\n    decodePayload(data, this.socket.binaryType).forEach(callback);\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emitReserved(\"pollComplete\");\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {}\n    }\n  }\n  /**\n   * For polling, send a close packet.\n   *\n   * @protected\n   */\n  doClose() {\n    const close = () => {\n      this.write([{\n        type: \"close\"\n      }]);\n    };\n    if (\"open\" === this.readyState) {\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      this.once(\"open\", close);\n    }\n  }\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} packets - data packets\n   * @protected\n   */\n  write(packets) {\n    this.writable = false;\n    encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emitReserved(\"drain\");\n      });\n    });\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    const query = this.query || {};\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @private\n   */\n  request(opts = {}) {\n    Object.assign(opts, {\n      xd: this.xd,\n      cookieJar: this.cookieJar\n    }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    req.on(\"success\", fn);\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr post error\", xhrStatus, context);\n    });\n  }\n  /**\n   * Starts a poll cycle.\n   *\n   * @private\n   */\n  doPoll() {\n    const req = this.request();\n    req.on(\"data\", this.onData.bind(this));\n    req.on(\"error\", (xhrStatus, context) => {\n      this.onError(\"xhr poll error\", xhrStatus, context);\n    });\n    this.pollXhr = req;\n  }\n}\nexport class Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @package\n   */\n  constructor(uri, opts) {\n    super();\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.data = undefined !== opts.data ? opts.data : null;\n    this.create();\n  }\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @private\n   */\n  create() {\n    var _a;\n    const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n    opts.xdomain = !!this.opts.xd;\n    const xhr = this.xhr = new XMLHttpRequest(opts);\n    try {\n      xhr.open(this.method, this.uri, true);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n      (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n      xhr.onreadystatechange = () => {\n        var _a;\n        if (xhr.readyState === 3) {\n          (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          this.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          this.setTimeoutFn(() => {\n            this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n          }, 0);\n        }\n      };\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      this.setTimeoutFn(() => {\n        this.onError(e);\n      }, 0);\n      return;\n    }\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n  /**\n   * Called upon error.\n   *\n   * @private\n   */\n  onError(err) {\n    this.emitReserved(\"error\", err, this.xhr);\n    this.cleanup(true);\n  }\n  /**\n   * Cleans up house.\n   *\n   * @private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    this.xhr.onreadystatechange = empty;\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n    this.xhr = null;\n  }\n  /**\n   * Called upon load.\n   *\n   * @private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.emitReserved(\"data\", data);\n      this.emitReserved(\"success\");\n      this.cleanup();\n    }\n  }\n  /**\n   * Aborts the request.\n   *\n   * @package\n   */\n  abort() {\n    this.cleanup();\n  }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n  // @ts-ignore\n  if (typeof attachEvent === \"function\") {\n    // @ts-ignore\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}", "map": {"version": 3, "names": ["Transport", "yeast", "encodePayload", "decodePayload", "createCookieJar", "XHR", "XMLHttpRequest", "Emitter", "installTimerFunctions", "pick", "globalThisShim", "globalThis", "empty", "hasXHR2", "xhr", "xdomain", "responseType", "Polling", "constructor", "opts", "polling", "location", "isSSL", "protocol", "port", "xd", "hostname", "forceBase64", "supportsBinary", "withCredentials", "cookieJar", "name", "doOpen", "poll", "pause", "onPause", "readyState", "writable", "total", "once", "doPoll", "emit<PERSON><PERSON><PERSON><PERSON>", "onData", "data", "callback", "packet", "type", "onOpen", "onClose", "description", "onPacket", "socket", "binaryType", "for<PERSON>ach", "doClose", "close", "write", "packets", "doWrite", "uri", "schema", "secure", "query", "timestampRequests", "timestampParam", "sid", "b64", "createUri", "request", "Object", "assign", "Request", "fn", "req", "method", "on", "xhrStatus", "context", "onError", "bind", "pollXhr", "undefined", "create", "_a", "open", "extraHeaders", "setDisableHeaderCheck", "i", "hasOwnProperty", "setRequestHeader", "e", "addCookies", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "setTimeoutFn", "send", "document", "index", "requestsCount", "requests", "err", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "addEventListener", "terminationEvent"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/engine.io-client/build/esm/transports/polling.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,aAAa,EAAEC,aAAa,QAAQ,kBAAkB;AAC/D,SAASC,eAAe,EAAEC,GAAG,IAAIC,cAAc,QAAS,qBAAqB;AAC7E,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,qBAAqB,EAAEC,IAAI,QAAQ,YAAY;AACxD,SAASC,cAAc,IAAIC,UAAU,QAAQ,kBAAkB;AAC/D,SAASC,KAAKA,CAAA,EAAG,CAAE;AACnB,MAAMC,OAAO,GAAI,YAAY;EACzB,MAAMC,GAAG,GAAG,IAAIR,cAAc,CAAC;IAC3BS,OAAO,EAAE;EACb,CAAC,CAAC;EACF,OAAO,IAAI,IAAID,GAAG,CAACE,YAAY;AACnC,CAAC,CAAE,CAAC;AACJ,OAAO,MAAMC,OAAO,SAASjB,SAAS,CAAC;EACnC;AACJ;AACA;AACA;AACA;AACA;EACIkB,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC,MAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACE,QAAQ;MAC5C,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAAI;MACxB;MACA,IAAI,CAACA,IAAI,EAAE;QACPA,IAAI,GAAGF,KAAK,GAAG,KAAK,GAAG,IAAI;MAC/B;MACA,IAAI,CAACG,EAAE,GACF,OAAOJ,QAAQ,KAAK,WAAW,IAC5BF,IAAI,CAACO,QAAQ,KAAKL,QAAQ,CAACK,QAAQ,IACnCF,IAAI,KAAKL,IAAI,CAACK,IAAI;IAC9B;IACA;AACR;AACA;IACQ,MAAMG,WAAW,GAAGR,IAAI,IAAIA,IAAI,CAACQ,WAAW;IAC5C,IAAI,CAACC,cAAc,GAAGf,OAAO,IAAI,CAACc,WAAW;IAC7C,IAAI,IAAI,CAACR,IAAI,CAACU,eAAe,EAAE;MAC3B,IAAI,CAACC,SAAS,GAAG1B,eAAe,CAAC,CAAC;IACtC;EACJ;EACA,IAAI2B,IAAIA,CAAA,EAAG;IACP,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACC,UAAU,GAAG,SAAS;IAC3B,MAAMF,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACE,UAAU,GAAG,QAAQ;MAC1BD,OAAO,CAAC,CAAC;IACb,CAAC;IACD,IAAI,IAAI,CAACf,OAAO,IAAI,CAAC,IAAI,CAACiB,QAAQ,EAAE;MAChC,IAAIC,KAAK,GAAG,CAAC;MACb,IAAI,IAAI,CAAClB,OAAO,EAAE;QACdkB,KAAK,EAAE;QACP,IAAI,CAACC,IAAI,CAAC,cAAc,EAAE,YAAY;UAClC,EAAED,KAAK,IAAIJ,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;MACA,IAAI,CAAC,IAAI,CAACG,QAAQ,EAAE;QAChBC,KAAK,EAAE;QACP,IAAI,CAACC,IAAI,CAAC,OAAO,EAAE,YAAY;UAC3B,EAAED,KAAK,IAAIJ,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACDA,KAAK,CAAC,CAAC;IACX;EACJ;EACA;AACJ;AACA;AACA;AACA;EACID,IAAIA,CAAA,EAAG;IACH,IAAI,CAACb,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoB,MAAM,CAAC,CAAC;IACb,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACC,IAAI,EAAE;IACT,MAAMC,QAAQ,GAAIC,MAAM,IAAK;MACzB;MACA,IAAI,SAAS,KAAK,IAAI,CAACT,UAAU,IAAIS,MAAM,CAACC,IAAI,KAAK,MAAM,EAAE;QACzD,IAAI,CAACC,MAAM,CAAC,CAAC;MACjB;MACA;MACA,IAAI,OAAO,KAAKF,MAAM,CAACC,IAAI,EAAE;QACzB,IAAI,CAACE,OAAO,CAAC;UAAEC,WAAW,EAAE;QAAiC,CAAC,CAAC;QAC/D,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAACC,QAAQ,CAACL,MAAM,CAAC;IACzB,CAAC;IACD;IACA1C,aAAa,CAACwC,IAAI,EAAE,IAAI,CAACQ,MAAM,CAACC,UAAU,CAAC,CAACC,OAAO,CAACT,QAAQ,CAAC;IAC7D;IACA,IAAI,QAAQ,KAAK,IAAI,CAACR,UAAU,EAAE;MAC9B;MACA,IAAI,CAAChB,OAAO,GAAG,KAAK;MACpB,IAAI,CAACqB,YAAY,CAAC,cAAc,CAAC;MACjC,IAAI,MAAM,KAAK,IAAI,CAACL,UAAU,EAAE;QAC5B,IAAI,CAACH,IAAI,CAAC,CAAC;MACf,CAAC,MACI,CACL;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqB,OAAOA,CAAA,EAAG;IACN,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAChB,IAAI,CAACC,KAAK,CAAC,CAAC;QAAEV,IAAI,EAAE;MAAQ,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,MAAM,KAAK,IAAI,CAACV,UAAU,EAAE;MAC5BmB,KAAK,CAAC,CAAC;IACX,CAAC,MACI;MACD;MACA;MACA,IAAI,CAAChB,IAAI,CAAC,MAAM,EAAEgB,KAAK,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACpB,QAAQ,GAAG,KAAK;IACrBnC,aAAa,CAACuD,OAAO,EAAGd,IAAI,IAAK;MAC7B,IAAI,CAACe,OAAO,CAACf,IAAI,EAAE,MAAM;QACrB,IAAI,CAACN,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACI,YAAY,CAAC,OAAO,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIkB,GAAGA,CAAA,EAAG;IACF,MAAMC,MAAM,GAAG,IAAI,CAACzC,IAAI,CAAC0C,MAAM,GAAG,OAAO,GAAG,MAAM;IAClD,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;IAC9B;IACA,IAAI,KAAK,KAAK,IAAI,CAAC3C,IAAI,CAAC4C,iBAAiB,EAAE;MACvCD,KAAK,CAAC,IAAI,CAAC3C,IAAI,CAAC6C,cAAc,CAAC,GAAG/D,KAAK,CAAC,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAAC2B,cAAc,IAAI,CAACkC,KAAK,CAACG,GAAG,EAAE;MACpCH,KAAK,CAACI,GAAG,GAAG,CAAC;IACjB;IACA,OAAO,IAAI,CAACC,SAAS,CAACP,MAAM,EAAEE,KAAK,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,OAAOA,CAACjD,IAAI,GAAG,CAAC,CAAC,EAAE;IACfkD,MAAM,CAACC,MAAM,CAACnD,IAAI,EAAE;MAAEM,EAAE,EAAE,IAAI,CAACA,EAAE;MAAEK,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,EAAE,IAAI,CAACX,IAAI,CAAC;IAC1E,OAAO,IAAIoD,OAAO,CAAC,IAAI,CAACZ,GAAG,CAAC,CAAC,EAAExC,IAAI,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuC,OAAOA,CAACf,IAAI,EAAE6B,EAAE,EAAE;IACd,MAAMC,GAAG,GAAG,IAAI,CAACL,OAAO,CAAC;MACrBM,MAAM,EAAE,MAAM;MACd/B,IAAI,EAAEA;IACV,CAAC,CAAC;IACF8B,GAAG,CAACE,EAAE,CAAC,SAAS,EAAEH,EAAE,CAAC;IACrBC,GAAG,CAACE,EAAE,CAAC,OAAO,EAAE,CAACC,SAAS,EAAEC,OAAO,KAAK;MACpC,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAEF,SAAS,EAAEC,OAAO,CAAC;IACtD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIrC,MAAMA,CAAA,EAAG;IACL,MAAMiC,GAAG,GAAG,IAAI,CAACL,OAAO,CAAC,CAAC;IAC1BK,GAAG,CAACE,EAAE,CAAC,MAAM,EAAE,IAAI,CAACjC,MAAM,CAACqC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtCN,GAAG,CAACE,EAAE,CAAC,OAAO,EAAE,CAACC,SAAS,EAAEC,OAAO,KAAK;MACpC,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAEF,SAAS,EAAEC,OAAO,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACG,OAAO,GAAGP,GAAG;EACtB;AACJ;AACA,OAAO,MAAMF,OAAO,SAAShE,OAAO,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIW,WAAWA,CAACyC,GAAG,EAAExC,IAAI,EAAE;IACnB,KAAK,CAAC,CAAC;IACPX,qBAAqB,CAAC,IAAI,EAAEW,IAAI,CAAC;IACjC,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACuD,MAAM,GAAGvD,IAAI,CAACuD,MAAM,IAAI,KAAK;IAClC,IAAI,CAACf,GAAG,GAAGA,GAAG;IACd,IAAI,CAAChB,IAAI,GAAGsC,SAAS,KAAK9D,IAAI,CAACwB,IAAI,GAAGxB,IAAI,CAACwB,IAAI,GAAG,IAAI;IACtD,IAAI,CAACuC,MAAM,CAAC,CAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIA,MAAMA,CAAA,EAAG;IACL,IAAIC,EAAE;IACN,MAAMhE,IAAI,GAAGV,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC;IAC7HA,IAAI,CAACJ,OAAO,GAAG,CAAC,CAAC,IAAI,CAACI,IAAI,CAACM,EAAE;IAC7B,MAAMX,GAAG,GAAI,IAAI,CAACA,GAAG,GAAG,IAAIR,cAAc,CAACa,IAAI,CAAE;IACjD,IAAI;MACAL,GAAG,CAACsE,IAAI,CAAC,IAAI,CAACV,MAAM,EAAE,IAAI,CAACf,GAAG,EAAE,IAAI,CAAC;MACrC,IAAI;QACA,IAAI,IAAI,CAACxC,IAAI,CAACkE,YAAY,EAAE;UACxBvE,GAAG,CAACwE,qBAAqB,IAAIxE,GAAG,CAACwE,qBAAqB,CAAC,IAAI,CAAC;UAC5D,KAAK,IAAIC,CAAC,IAAI,IAAI,CAACpE,IAAI,CAACkE,YAAY,EAAE;YAClC,IAAI,IAAI,CAAClE,IAAI,CAACkE,YAAY,CAACG,cAAc,CAACD,CAAC,CAAC,EAAE;cAC1CzE,GAAG,CAAC2E,gBAAgB,CAACF,CAAC,EAAE,IAAI,CAACpE,IAAI,CAACkE,YAAY,CAACE,CAAC,CAAC,CAAC;YACtD;UACJ;QACJ;MACJ,CAAC,CACD,OAAOG,CAAC,EAAE,CAAE;MACZ,IAAI,MAAM,KAAK,IAAI,CAAChB,MAAM,EAAE;QACxB,IAAI;UACA5D,GAAG,CAAC2E,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QACpE,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;MAChB;MACA,IAAI;QACA5E,GAAG,CAAC2E,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;MACzC,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;MACZ,CAACP,EAAE,GAAG,IAAI,CAAChE,IAAI,CAACW,SAAS,MAAM,IAAI,IAAIqD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,UAAU,CAAC7E,GAAG,CAAC;MAClF;MACA,IAAI,iBAAiB,IAAIA,GAAG,EAAE;QAC1BA,GAAG,CAACe,eAAe,GAAG,IAAI,CAACV,IAAI,CAACU,eAAe;MACnD;MACA,IAAI,IAAI,CAACV,IAAI,CAACyE,cAAc,EAAE;QAC1B9E,GAAG,CAAC+E,OAAO,GAAG,IAAI,CAAC1E,IAAI,CAACyE,cAAc;MAC1C;MACA9E,GAAG,CAACgF,kBAAkB,GAAG,MAAM;QAC3B,IAAIX,EAAE;QACN,IAAIrE,GAAG,CAACsB,UAAU,KAAK,CAAC,EAAE;UACtB,CAAC+C,EAAE,GAAG,IAAI,CAAChE,IAAI,CAACW,SAAS,MAAM,IAAI,IAAIqD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,YAAY,CAACjF,GAAG,CAAC;QACxF;QACA,IAAI,CAAC,KAAKA,GAAG,CAACsB,UAAU,EACpB;QACJ,IAAI,GAAG,KAAKtB,GAAG,CAACkF,MAAM,IAAI,IAAI,KAAKlF,GAAG,CAACkF,MAAM,EAAE;UAC3C,IAAI,CAACC,MAAM,CAAC,CAAC;QACjB,CAAC,MACI;UACD;UACA;UACA,IAAI,CAACC,YAAY,CAAC,MAAM;YACpB,IAAI,CAACpB,OAAO,CAAC,OAAOhE,GAAG,CAACkF,MAAM,KAAK,QAAQ,GAAGlF,GAAG,CAACkF,MAAM,GAAG,CAAC,CAAC;UACjE,CAAC,EAAE,CAAC,CAAC;QACT;MACJ,CAAC;MACDlF,GAAG,CAACqF,IAAI,CAAC,IAAI,CAACxD,IAAI,CAAC;IACvB,CAAC,CACD,OAAO+C,CAAC,EAAE;MACN;MACA;MACA;MACA,IAAI,CAACQ,YAAY,CAAC,MAAM;QACpB,IAAI,CAACpB,OAAO,CAACY,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC;MACL;IACJ;IACA,IAAI,OAAOU,QAAQ,KAAK,WAAW,EAAE;MACjC,IAAI,CAACC,KAAK,GAAG9B,OAAO,CAAC+B,aAAa,EAAE;MACpC/B,OAAO,CAACgC,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC,GAAG,IAAI;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIvB,OAAOA,CAAC0B,GAAG,EAAE;IACT,IAAI,CAAC/D,YAAY,CAAC,OAAO,EAAE+D,GAAG,EAAE,IAAI,CAAC1F,GAAG,CAAC;IACzC,IAAI,CAAC2F,OAAO,CAAC,IAAI,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIA,OAAOA,CAACC,SAAS,EAAE;IACf,IAAI,WAAW,KAAK,OAAO,IAAI,CAAC5F,GAAG,IAAI,IAAI,KAAK,IAAI,CAACA,GAAG,EAAE;MACtD;IACJ;IACA,IAAI,CAACA,GAAG,CAACgF,kBAAkB,GAAGlF,KAAK;IACnC,IAAI8F,SAAS,EAAE;MACX,IAAI;QACA,IAAI,CAAC5F,GAAG,CAAC6F,KAAK,CAAC,CAAC;MACpB,CAAC,CACD,OAAOjB,CAAC,EAAE,CAAE;IAChB;IACA,IAAI,OAAOU,QAAQ,KAAK,WAAW,EAAE;MACjC,OAAO7B,OAAO,CAACgC,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC;IACvC;IACA,IAAI,CAACvF,GAAG,GAAG,IAAI;EACnB;EACA;AACJ;AACA;AACA;AACA;EACImF,MAAMA,CAAA,EAAG;IACL,MAAMtD,IAAI,GAAG,IAAI,CAAC7B,GAAG,CAAC8F,YAAY;IAClC,IAAIjE,IAAI,KAAK,IAAI,EAAE;MACf,IAAI,CAACF,YAAY,CAAC,MAAM,EAAEE,IAAI,CAAC;MAC/B,IAAI,CAACF,YAAY,CAAC,SAAS,CAAC;MAC5B,IAAI,CAACgE,OAAO,CAAC,CAAC;IAClB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,OAAO,CAAC,CAAC;EAClB;AACJ;AACAlC,OAAO,CAAC+B,aAAa,GAAG,CAAC;AACzB/B,OAAO,CAACgC,QAAQ,GAAG,CAAC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;EACjC;EACA,IAAI,OAAOS,WAAW,KAAK,UAAU,EAAE;IACnC;IACAA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC;EAC1C,CAAC,MACI,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;IAC7C,MAAMC,gBAAgB,GAAG,YAAY,IAAIrG,UAAU,GAAG,UAAU,GAAG,QAAQ;IAC3EoG,gBAAgB,CAACC,gBAAgB,EAAEF,aAAa,EAAE,KAAK,CAAC;EAC5D;AACJ;AACA,SAASA,aAAaA,CAAA,EAAG;EACrB,KAAK,IAAIvB,CAAC,IAAIhB,OAAO,CAACgC,QAAQ,EAAE;IAC5B,IAAIhC,OAAO,CAACgC,QAAQ,CAACf,cAAc,CAACD,CAAC,CAAC,EAAE;MACpChB,OAAO,CAACgC,QAAQ,CAAChB,CAAC,CAAC,CAACoB,KAAK,CAAC,CAAC;IAC/B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}