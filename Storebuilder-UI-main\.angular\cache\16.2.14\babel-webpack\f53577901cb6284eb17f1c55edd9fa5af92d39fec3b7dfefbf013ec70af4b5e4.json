{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/shared/services/store.service\";\nimport * as i3 from \"../../../../shared/services/product.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nfunction IndexComponent_a_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 9);\n    i0.ɵɵelement(1, \"app-category-card\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵpropertyInterpolate1(\"routerLink\", \"/category/\", category_r1.id, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"category\", category_r1);\n  }\n}\nexport class IndexComponent {\n  //\n  constructor(activatedRoute, store, productService, translateService) {\n    this.activatedRoute = activatedRoute;\n    this.store = store;\n    this.productService = productService;\n    this.translateService = translateService;\n    this.items = [];\n    this.categories = [];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.products = [];\n    this.categoryId = this.activatedRoute.snapshot.params['id'];\n  }\n  //\n  //\n  ngOnInit() {\n    this.items = [{\n      label: this.translateService.instant('categories.allCategories')\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      // Listen To Store Service For any change on categories\n      this.store.subscription('categories').subscribe({\n        next: res => {\n          this.categories = res;\n          // console.log(this.categories, \" 1\",res)\n          // console.log(this.categories);\n        },\n\n        error: err => {\n          console.error(err);\n        }\n      });\n    }, 1);\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i3.ProductService), i0.ɵɵdirectiveInject(i4.TranslateService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    decls: 11,\n    vars: 6,\n    consts: [[1, \"categories-page\"], [1, \"breadcrumb\", 2, \"margin-top\", \"95px\"], [3, \"model\", \"home\"], [1, \"content-container\", \"mt-5\", \"main_font\"], [1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"flex\", \"justify-content-center\", \"md:justify-content-start\"], [1, \"font-size-22\", \"bold-font\"], [1, \"my-5\", \"flex\", \"flex-row\", \"justify-content-center\", \"flex-wrap\"], [\"class\", \"mt-2 mx-2\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"mx-2\", 3, \"routerLink\"], [3, \"category\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 7);\n        i0.ɵɵtemplate(10, IndexComponent_a_10_Template, 2, 2, \"a\", 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"categories.allCategories\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n      }\n    },\n    dependencies: [i5.NgForOf, i1.RouterLink],\n    styles: [\"a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY2F0ZWdvcmllcy9jb21wb25lbnRzL2luZGV4L2luZGV4LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0kscUJBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbImEge1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵpropertyInterpolate1", "category_r1", "id", "ɵɵadvance", "ɵɵproperty", "IndexComponent", "constructor", "activatedRoute", "store", "productService", "translateService", "items", "categories", "home", "icon", "routerLink", "products", "categoryId", "snapshot", "params", "ngOnInit", "label", "instant", "ngAfterViewInit", "setTimeout", "subscription", "subscribe", "next", "res", "error", "err", "console", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "StoreService", "i3", "ProductService", "i4", "TranslateService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵtemplate", "IndexComponent_a_10_Template", "ɵɵtextInterpolate1", "ɵɵpipeBind1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\categories\\components\\index\\index.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\categories\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute } from \"@angular/router\";\r\nimport { ProductService } from \"../../../../shared/services/product.service\";\r\nimport { Category } from 'src/app/interfaces/category';\r\nimport { StoreService } from 'src/app/shared/services/store.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\n\r\n\r\n@Component({\r\n  selector: 'app-index',\r\n  templateUrl: './index.component.html',\r\n  styleUrls: ['./index.component.scss']\r\n})\r\nexport class IndexComponent implements OnInit, AfterViewInit {\r\n  items: MenuItem[] = [];\r\n  categories: Array<Category> = [];\r\n  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };\r\n  categoryId: number;\r\n  products: any[] = [];\r\n  //\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private store: StoreService,\r\n    private productService: ProductService,\r\n    private translateService:TranslateService\r\n  ) { this.categoryId = this.activatedRoute.snapshot.params['id']; }\r\n  //\r\n  //\r\n  ngOnInit(): void {\r\n    this.items = [\r\n      { label: this.translateService.instant('categories.allCategories') }\r\n    ];\r\n\r\n    this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => {\r\n\r\n      // Listen To Store Service For any change on categories\r\n      this.store.subscription('categories')\r\n        .subscribe({\r\n          next: (res: any) => {\r\n            this.categories = res;\r\n            // console.log(this.categories, \" 1\",res)\r\n            // console.log(this.categories);\r\n          },\r\n          error: (err: any) => {\r\n            console.error(err);\r\n\r\n          }\r\n        });\r\n    }, 1);\r\n\r\n  }\r\n\r\n}\r\n", "<section class=\"categories-page\">\r\n  <div class=\"breadcrumb\" style=\"margin-top: 95px\">\r\n    <p-breadcrumb [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n  </div>\r\n  <!--  -->\r\n  <div class=\"content-container mt-5 main_font\">\r\n    <div class=\"grid\">\r\n      <div\r\n        class=\"col-12 md:col-6 flex justify-content-center md:justify-content-start\"\r\n      >\r\n        <div class=\"font-size-22 bold-font\">\r\n          {{ \"categories.allCategories\" | translate }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!--  -->\r\n    <div class=\"my-5 flex flex-row justify-content-center flex-wrap\">\r\n      <a\r\n        *ngFor=\"let category of categories\"\r\n        class=\"mt-2 mx-2\"\r\n        routerLink=\"/category/{{ category.id }}\"\r\n      >\r\n        <app-category-card [category]=\"category\"></app-category-card>\r\n      </a>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": ";;;;;;;;ICiBMA,EAAA,CAAAC,cAAA,WAIC;IACCD,EAAA,CAAAE,SAAA,4BAA6D;IAC/DF,EAAA,CAAAG,YAAA,EAAI;;;;IAHFH,EAAA,CAAAI,sBAAA,6BAAAC,WAAA,CAAAC,EAAA,KAAwC;IAErBN,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,aAAAH,WAAA,CAAqB;;;ADRhD,OAAM,MAAOI,cAAc;EAMzB;EACAC,YACUC,cAA8B,EAC9BC,KAAmB,EACnBC,cAA8B,EAC9BC,gBAAiC;IAHjC,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAV1B,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAoB,EAAE;IAChC,KAAAC,IAAI,GAAa;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAExD,KAAAC,QAAQ,GAAU,EAAE;IAOhB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACV,cAAc,CAACW,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;EAAE;EACjE;EACA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACT,KAAK,GAAG,CACX;MAAEU,KAAK,EAAE,IAAI,CAACX,gBAAgB,CAACY,OAAO,CAAC,0BAA0B;IAAC,CAAE,CACrE;IAED,IAAI,CAACT,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;EACrD;EAEAQ,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MAEd;MACA,IAAI,CAAChB,KAAK,CAACiB,YAAY,CAAC,YAAY,CAAC,CAClCC,SAAS,CAAC;QACTC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAAChB,UAAU,GAAGgB,GAAG;UACrB;UACA;QACF,CAAC;;QACDC,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAACC,GAAG,CAAC;QAEpB;OACD,CAAC;IACN,CAAC,EAAE,CAAC,CAAC;EAEP;EAAC,QAAAE,CAAA,G;qBAzCU3B,cAAc,EAAAT,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAzC,EAAA,CAAAqC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3C,EAAA,CAAAqC,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdrC,cAAc;IAAAsC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCd3BrD,EAAA,CAAAC,cAAA,iBAAiC;QAE7BD,EAAA,CAAAE,SAAA,sBAA2D;QAC7DF,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAA8C;QAMtCD,EAAA,CAAAuD,MAAA,GACF;;QAAAvD,EAAA,CAAAG,YAAA,EAAM;QAIVH,EAAA,CAAAC,cAAA,aAAiE;QAC/DD,EAAA,CAAAwD,UAAA,KAAAC,4BAAA,eAMI;QACNzD,EAAA,CAAAG,YAAA,EAAM;;;QAtBQH,EAAA,CAAAO,SAAA,GAAe;QAAfP,EAAA,CAAAQ,UAAA,UAAA8C,GAAA,CAAAvC,KAAA,CAAe,SAAAuC,GAAA,CAAArC,IAAA;QASvBjB,EAAA,CAAAO,SAAA,GACF;QADEP,EAAA,CAAA0D,kBAAA,MAAA1D,EAAA,CAAA2D,WAAA,wCACF;QAMqB3D,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAtC,UAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}