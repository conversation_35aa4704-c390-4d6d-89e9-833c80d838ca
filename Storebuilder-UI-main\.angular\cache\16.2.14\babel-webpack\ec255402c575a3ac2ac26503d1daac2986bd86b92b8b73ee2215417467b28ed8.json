{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, RendererFactory2, NgZone, ANIMATION_MODULE_TYPE, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵWebAnimationsDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\nlet BrowserAnimationBuilder = /*#__PURE__*/(() => {\n  class BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n      super();\n      this._nextAnimationId = 0;\n      const typeData = {\n        id: '0',\n        encapsulation: ViewEncapsulation.None,\n        styles: [],\n        data: {\n          animation: []\n        }\n      };\n      this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n    build(animation) {\n      const id = this._nextAnimationId.toString();\n      this._nextAnimationId++;\n      const entry = Array.isArray(animation) ? sequence(animation) : animation;\n      issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n      return new BrowserAnimationFactory(id, this._renderer);\n    }\n    static #_ = this.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n      return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserAnimationBuilder,\n      factory: BrowserAnimationBuilder.ɵfac\n    });\n  }\n  return BrowserAnimationBuilder;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n    this._command('create', options);\n  }\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n  init() {\n    this._command('init');\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    this._command('play');\n    this._started = true;\n  }\n  pause() {\n    this._command('pause');\n  }\n  restart() {\n    this._command('restart');\n  }\n  finish() {\n    this._command('finish');\n  }\n  destroy() {\n    this._command('destroy');\n  }\n  reset() {\n    this._command('reset');\n    this._started = false;\n  }\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n  getPosition() {\n    return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n  }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nlet AnimationRendererFactory = /*#__PURE__*/(() => {\n  class AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n      this.delegate = delegate;\n      this.engine = engine;\n      this._zone = _zone;\n      this._currentId = 0;\n      this._microtaskId = 1;\n      this._animationCallbacksBuffer = [];\n      this._rendererCache = new Map();\n      this._cdRecurDepth = 0;\n      engine.onRemovalComplete = (element, delegate) => {\n        // Note: if a component element has a leave animation, and a host leave animation,\n        // the view engine will call `removeChild` for the parent\n        // component renderer as well as for the child component renderer.\n        // Therefore, we need to check if we already removed the element.\n        const parentNode = delegate?.parentNode(element);\n        if (parentNode) {\n          delegate.removeChild(parentNode, element);\n        }\n      };\n    }\n    createRenderer(hostElement, type) {\n      const EMPTY_NAMESPACE_ID = '';\n      // cache the delegates to find out which cached delegate can\n      // be used by which cached renderer\n      const delegate = this.delegate.createRenderer(hostElement, type);\n      if (!hostElement || !type || !type.data || !type.data['animation']) {\n        let renderer = this._rendererCache.get(delegate);\n        if (!renderer) {\n          // Ensure that the renderer is removed from the cache on destroy\n          // since it may contain references to detached DOM nodes.\n          const onRendererDestroy = () => this._rendererCache.delete(delegate);\n          renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n          // only cache this result when the base renderer is used\n          this._rendererCache.set(delegate, renderer);\n        }\n        return renderer;\n      }\n      const componentId = type.id;\n      const namespaceId = type.id + '-' + this._currentId;\n      this._currentId++;\n      this.engine.register(namespaceId, hostElement);\n      const registerTrigger = trigger => {\n        if (Array.isArray(trigger)) {\n          trigger.forEach(registerTrigger);\n        } else {\n          this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n        }\n      };\n      const animationTriggers = type.data['animation'];\n      animationTriggers.forEach(registerTrigger);\n      return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n      this._cdRecurDepth++;\n      if (this.delegate.begin) {\n        this.delegate.begin();\n      }\n    }\n    _scheduleCountTask() {\n      queueMicrotask(() => {\n        this._microtaskId++;\n      });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n      if (count >= 0 && count < this._microtaskId) {\n        this._zone.run(() => fn(data));\n        return;\n      }\n      if (this._animationCallbacksBuffer.length == 0) {\n        queueMicrotask(() => {\n          this._zone.run(() => {\n            this._animationCallbacksBuffer.forEach(tuple => {\n              const [fn, data] = tuple;\n              fn(data);\n            });\n            this._animationCallbacksBuffer = [];\n          });\n        });\n      }\n      this._animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n      this._cdRecurDepth--;\n      // this is to prevent animations from running twice when an inner\n      // component does CD when a parent component instead has inserted it\n      if (this._cdRecurDepth == 0) {\n        this._zone.runOutsideAngular(() => {\n          this._scheduleCountTask();\n          this.engine.flush(this._microtaskId);\n        });\n      }\n      if (this.delegate.end) {\n        this.delegate.end();\n      }\n    }\n    whenRenderingDone() {\n      return this.engine.whenRenderingDone();\n    }\n    static #_ = this.ɵfac = function AnimationRendererFactory_Factory(t) {\n      return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AnimationRendererFactory,\n      factory: AnimationRendererFactory.ɵfac\n    });\n  }\n  return AnimationRendererFactory;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nlet InjectableAnimationEngine = /*#__PURE__*/(() => {\n  class InjectableAnimationEngine extends ɵAnimationEngine {\n    // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n    // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n    // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n    constructor(doc, driver, normalizer, appRef) {\n      super(doc.body, driver, normalizer);\n    }\n    ngOnDestroy() {\n      this.flush();\n    }\n    static #_ = this.ɵfac = function InjectableAnimationEngine_Factory(t) {\n      return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer), i0.ɵɵinject(i0.ApplicationRef));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InjectableAnimationEngine,\n      factory: InjectableAnimationEngine.ɵfac\n    });\n  }\n  return InjectableAnimationEngine;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: () => new ɵWebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nlet BrowserAnimationsModule = /*#__PURE__*/(() => {\n  class BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see {@link BrowserAnimationsModuleConfig}\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n      return {\n        ngModule: BrowserAnimationsModule,\n        providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n      };\n    }\n    static #_ = this.ɵfac = function BrowserAnimationsModule_Factory(t) {\n      return new (t || BrowserAnimationsModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserAnimationsModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: BROWSER_ANIMATIONS_PROVIDERS,\n      imports: [BrowserModule]\n    });\n  }\n  return BrowserAnimationsModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nlet NoopAnimationsModule = /*#__PURE__*/(() => {\n  class NoopAnimationsModule {\n    static #_ = this.ɵfac = function NoopAnimationsModule_Factory(t) {\n      return new (t || NoopAnimationsModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NoopAnimationsModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n      imports: [BrowserModule]\n    });\n  }\n  return NoopAnimationsModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n//# sourceMappingURL=animations.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}