{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { FeaturedDataTypeEnum, FeatureType } from \"@core/interface\";\nimport { ShowRoomTypeEnum } from \"@core/enums\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../shared/components/product-slider/product-slider.component\";\nimport * as i8 from \"../../../../shared/components/section/section.component\";\nimport * as i9 from \"@shared/components/landing-templates/template-one/template-one.component\";\nimport * as i10 from \"../main-slider/main-slider.component\";\nimport * as i11 from \"../banner/banner.component\";\nimport * as i12 from \"../category-slider/category-slider.component\";\nimport * as i13 from \"../section-category/section.component\";\nconst _c0 = function (a0) {\n  return {\n    \"mt-3\": a0\n  };\n};\nfunction IndexComponent_section_0_ng_container_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-section-category\", 11);\n    i0.ɵɵelement(2, \"app-category-slider\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r8.index;\n    const feature_r3 = ctx_r8.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, i_r4 === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", feature_r3.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"categories\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelement(2, \"app-banner\", 14);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r9.index;\n    const feature_r3 = ctx_r9.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, i_r4 === 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"banner\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"app-mtn-section\", 16);\n    i0.ɵɵelement(2, \"app-mtn-product-slider\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    const i_r4 = ctx_r10.index;\n    const feature_r3 = ctx_r10.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, i_r4 === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"categoryID\", feature_r3.categoryId)(\"featureProduct\", feature_r3.feature)(\"fetchStatus\", feature_r3.fetchStatus)(\"title\", feature_r3.title)(\"topNumber\", 100);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"products\", feature_r3.data);\n  }\n}\nfunction IndexComponent_section_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵtemplate(2, IndexComponent_section_0_ng_container_4_div_2_Template, 3, 5, \"div\", 7);\n    i0.ɵɵtemplate(3, IndexComponent_section_0_ng_container_4_div_3_Template, 3, 4, \"div\", 8);\n    i0.ɵɵtemplate(4, IndexComponent_section_0_ng_container_4_div_4_Template, 3, 9, \"div\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const feature_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"category\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"banner\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", feature_r3.type === \"feature\");\n  }\n}\nfunction IndexComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 2);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelement(2, \"app-main-slider\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵtemplate(4, IndexComponent_section_0_ng_container_4_Template, 5, 3, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r0.mainSlider);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.featuredData);\n  }\n}\nfunction IndexComponent_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-landing-template-one\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c1 = function (a0, a1, a2, a3) {\n  return {\n    \"navbar-inactive-old-mobile\": a0,\n    \"navbar-active\": a1,\n    \"navbar-inactive\": a2,\n    \"navbar-inactive-mobile\": a3\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nfunction IndexComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 18);\n    i0.ɵɵtemplate(2, IndexComponent_ng_container_1_ng_container_2_Template, 2, 0, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(3, _c1, ctx_r1.isMobileView && !ctx_r1.isMobileTemplate, (ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && !ctx_r1.isMobileTemplate && ctx_r1.isMobileView, !(ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && !ctx_r1.isMobileTemplate, !(ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive) && ctx_r1.isMobileTemplate))(\"ngStyle\", i0.ɵɵpureFunction1(8, _c2, ctx_r1.isMobileTemplate ? ctx_r1.isMobileView ? \"80px\" : \"15px\" : ctx_r1.isMobileView ? \"230px\" : \"50px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.templateId === 1);\n  }\n}\nexport class IndexComponent {\n  store;\n  mainDataService;\n  homeService;\n  productService;\n  messageService;\n  reviewsService;\n  translate;\n  router;\n  cookieService;\n  authTokenService;\n  loaderService;\n  permissionService;\n  appDataService;\n  cd;\n  platformId;\n  sections = [];\n  mainSlider = [];\n  categories = [];\n  enableFeaturedProducts = false;\n  enableNewProducts = false;\n  reviews;\n  banner = [{\n    src: 'assets/images/banner/banner1.jpg'\n  }, {\n    src: 'assets/images/banner/banner2.jpg'\n  }, {\n    src: 'assets/images/banner/banner3.jpg'\n  }];\n  showRoomConfigurationRes = [];\n  productOffers = [];\n  featuredData = [];\n  templateId = 1;\n  zoomLevelClass = 'default-zoom';\n  navbarData;\n  allBanners;\n  isMobileTemplate = false;\n  isLayoutTemplate = false;\n  isMobileView = window.screen.width < 768;\n  onResize(event) {\n    this.updateZoomClass();\n  }\n  updateZoomClass() {\n    if (isPlatformBrowser(this.platformId)) {\n      const zoomLevel = window.innerWidth / window.screen.availWidth * 100;\n      if (zoomLevel <= 91) {\n        this.zoomLevelClass = 'zoom-110';\n      }\n      if (zoomLevel <= 112) {\n        this.zoomLevelClass = 'zoom-90';\n      } else if (zoomLevel <= 125) {\n        this.zoomLevelClass = 'zoom-80';\n      } else if (zoomLevel <= 134) {\n        this.zoomLevelClass = 'zoom-75';\n      } else if (zoomLevel <= 150) {\n        this.zoomLevelClass = 'zoom-67';\n      } else if (zoomLevel <= 200) {\n        this.zoomLevelClass = 'zoom-50';\n      } else if (zoomLevel <= 300) {\n        this.zoomLevelClass = 'zoom-33';\n      } else if (zoomLevel <= 400) {\n        this.zoomLevelClass = 'zoom-25';\n      } else {\n        this.zoomLevelClass = 'default-zoom';\n      }\n    }\n  }\n  constructor(store, mainDataService, homeService, productService, messageService, reviewsService, translate, router, cookieService, authTokenService, loaderService, permissionService, appDataService, cd, platformId) {\n    this.store = store;\n    this.mainDataService = mainDataService;\n    this.homeService = homeService;\n    this.productService = productService;\n    this.messageService = messageService;\n    this.reviewsService = reviewsService;\n    this.translate = translate;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.loaderService = loaderService;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.cd = cd;\n    this.platformId = platformId;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.appDataService.layoutTemplate) {\n        _this.navbarData = yield _this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n        _this.allBanners = yield _this.appDataService.layoutTemplate.find(section => section.type === 'main_banner');\n        _this.mainDataService.setBannerData({\n          isBannerActive: _this.allBanners?.isActive ?? false,\n          isNavbarDataActive: _this.navbarData?.isActive ?? false\n        });\n      }\n      if (!_this.permissionService.hasPermission('Layout-Template')) {\n        _this.getShowRoom();\n        _this.isLayoutTemplate = false;\n      } else {\n        _this.isLayoutTemplate = true;\n      }\n      _this.cd.detectChanges();\n    })();\n  }\n  findShowRoomTypeId(showRoomId) {\n    return this.showRoomConfigurationRes.find(element => element.showRoomTypeId == showRoomId);\n  }\n  getShowRoom() {\n    this.showRoomConfigurationRes = this.appDataService.showRoomConfiguration?.records;\n    if (this.findShowRoomTypeId(ShowRoomTypeEnum.MainBanner)) {\n      this.getMainSliderData();\n    }\n    this.addFeatureData();\n    const foundFeatureProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && element.featureProduct);\n    const foundCategoryProduct = this.showRoomConfigurationRes.filter(element => element.showRoomTypeId == 2 && !element.featureProduct && element.categoryId);\n    if (foundFeatureProduct?.length) {\n      this.handleFeature(foundFeatureProduct);\n    } else {\n      this.handleFeatureNolength();\n    }\n    if (foundCategoryProduct.length) {\n      this.handleFoundProduct(foundCategoryProduct);\n    }\n  }\n  addFeatureData() {\n    const foundCategories = this.findShowRoomTypeId(ShowRoomTypeEnum.Category);\n    if (foundCategories?.showRoomTypeId) {\n      const categories = this.appDataService.categories.records;\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Category,\n        data: categories,\n        image: foundCategories.image,\n        color: foundCategories.color,\n        feature: foundCategories.featureProduct,\n        order: foundCategories.order,\n        categoryId: foundCategories.categoryId,\n        fetchStatus: 'completed',\n        isDragged: false,\n        title: 'Categories'\n      });\n    }\n  }\n  signOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/login']);\n    sessionStorage.clear();\n    this.store.set('profile', '');\n    this.store.set('cartProducts', '');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    this.getShowRoom();\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n    }\n  }\n  getMainSliderData() {\n    this.homeService.getMainSliders({}).subscribe({\n      next: res => {\n        this.loaderService.hide();\n        Object.keys(res.data.records).forEach(function (k) {\n          res.data.records[k].imageUrl = UtilityFunctions.verifyImageURL(res.data.records[k].imageUrl, environment.apiEndPoint);\n        });\n        this.mainSlider.push(...res.data.records);\n      },\n      error: err => {\n        this.loaderService.hide();\n      },\n      complete: () => {\n        this.loaderService.hide();\n      }\n    });\n  }\n  handleFeature(foundFeatureProduct) {\n    var _this2 = this;\n    foundFeatureProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.Feature,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: p.featureProduct,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        title: FeatureType[p.featureProduct],\n        topNumber: p.topNumber\n      });\n    });\n    Promise.all(foundFeatureProduct.map(p => this.productService.GetAllProductsByFeature(p.featureProduct, p.topNumber, true, 1, 20, false, null, true))).then(response => {\n      Promise.all(response.map(respObj => {\n        return new Promise((resolve, reject) => {\n          respObj.subscribe(r => {\n            if (r.data.records) {\n              this.featuredData.forEach(x => {\n                if (x.feature && x.feature === r.data.feature) {\n                  x.data = r.data.records;\n                  x.fetchStatus = 'completed';\n                }\n              });\n              this.featuredData.sort((a, b) => a.order - b.order);\n              this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n            }\n            resolve(true);\n          }, err => {\n            reject(new Error('An error occurred'));\n          });\n        });\n      })).then( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (data) {\n          const foundBanner = _this2.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n          if (foundBanner?.showRoomTypeId) {\n            _this2.homeService.getBanners(foundBanner.bannerId).subscribe({\n              next: res => {\n                _this2.featuredData.push({\n                  type: FeaturedDataTypeEnum.Banner,\n                  data: res.data.imageUrl,\n                  image: foundBanner.imageURL,\n                  order: foundBanner.order\n                });\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              },\n              error: err => {\n                _this2.featuredData.sort((a, b) => a.order - b.order);\n                _this2.featuredData = JSON.parse(JSON.stringify(_this2.featuredData));\n              }\n            });\n          }\n          _this2.loaderService.hide();\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).then( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (data) {\n          _this2.loaderService.hide();\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    });\n  }\n  handleFeatureNolength() {\n    const foundBanner = this.findShowRoomTypeId(ShowRoomTypeEnum.Banner);\n    if (foundBanner?.showRoomTypeId) {\n      this.homeService.getBanners(foundBanner.bannerId).subscribe(res => {\n        this.featuredData.push({\n          type: FeaturedDataTypeEnum.Banner,\n          data: res.data,\n          image: foundBanner.imageURL,\n          order: foundBanner.order,\n          categoryId: foundBanner.categoryId\n        });\n        this.featuredData.sort((a, b) => a.order - b.order);\n        this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        this.loaderService.hide();\n      });\n    }\n  }\n  handleFoundProduct(foundCategoryProduct) {\n    foundCategoryProduct.forEach(p => {\n      this.featuredData.push({\n        type: FeaturedDataTypeEnum.CategoryProduct,\n        data: [],\n        image: p.image,\n        color: p.color,\n        feature: null,\n        order: p.order,\n        categoryId: p.categoryId,\n        fetchStatus: 'pending',\n        isDragged: false,\n        name: \"Category\"\n      });\n      this.productService.getCategoryProducts(p.categoryId, 15, true).subscribe({\n        next: r => {\n          if (r.data.productsList.records) {\n            this.featuredData.forEach(x => {\n              if (x.categoryId == p.categoryId) {\n                x.data = r.data.productsList.records;\n                x.fetchStatus = 'completed';\n                x.name = r.data.productsList.records[0].categoryName;\n              }\n            });\n            this.loaderService.hide();\n          }\n          this.featuredData.sort((a, b) => a.order - b.order);\n          this.featuredData = JSON.parse(JSON.stringify(this.featuredData));\n        },\n        error: () => {\n          this.loaderService.hide();\n        }\n      });\n    });\n  }\n  static ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.HomeService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i1.ReviewsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(PLATFORM_ID));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"app-index\"]],\n    hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"home relative mobile-top\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"home\", \"relative\", \"mobile-top\"], [3, \"sliders\"], [1, \"content-container\", \"my-3X\", \"extra-pad\"], [4, \"ngFor\", \"ngForOf\"], [1, \"my-5\", \"ng-star-inserted\", \"mobile-display-category\"], [\"class\", \"category-slider-responsive\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"my-5\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"mb-3 mt-3\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"category-slider-responsive\", 3, \"ngClass\"], [3, \"title\"], [3, \"categories\"], [1, \"my-5\", 3, \"ngClass\"], [1, \"my-3\", 3, \"banner\"], [1, \"mb-3\", \"mt-3\", 3, \"ngClass\"], [3, \"categoryID\", \"featureProduct\", \"fetchStatus\", \"title\", \"topNumber\"], [3, \"products\"], [1, \"\", 3, \"ngClass\", \"ngStyle\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, IndexComponent_section_0_Template, 5, 2, \"section\", 0);\n        i0.ɵɵtemplate(1, IndexComponent_ng_container_1_Template, 3, 10, \"ng-container\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLayoutTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLayoutTemplate);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgStyle, i7.ProductSliderComponent, i8.SectionComponent, i9.TemplateOneComponent, i10.MainSliderComponent, i11.BannerComponent, i12.CategorySliderComponent, i13.SectionCategoryComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.landing-template[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .landing-template[_ngcontent-%COMP%] {\\n    padding: 0 15px;\\n    margin-top: 230px;\\n  }\\n}\\n\\n.yellow-bg[_ngcontent-%COMP%] {\\n  background-color: #FFCB05;\\n  position: absolute;\\n  width: 100%;\\n  height: 260px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .yellow-bg[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding-left: 4rem !important;\\n  padding-right: 3rem !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding-left: 1.5rem !important;\\n    padding-right: 1.5rem !important;\\n  }\\n  .extra-pad[_ngcontent-%COMP%]   .my-5.mt-3[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-display-category[_ngcontent-%COMP%] {\\n    margin-top: 30px !important;\\n    margin-bottom: 0px !important;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-width: 1200px) {\\n  .navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 130px !important;\\n  }\\n  .navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 74px !important;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 140px;\\n  }\\n}\\n@media only screen and (max-width: 767px) {\\n  .navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 180px !important;\\n  }\\n  .navbar-inactive-old-mobile[_ngcontent-%COMP%] {\\n    margin-top: 230px !important;\\n  }\\n  .navbar-inactive-mobile[_ngcontent-%COMP%] {\\n    margin-top: 85px !important;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 190px;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}