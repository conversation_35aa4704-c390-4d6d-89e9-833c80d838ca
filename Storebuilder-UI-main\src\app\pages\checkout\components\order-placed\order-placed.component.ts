import {Component, OnInit} from '@angular/core';
import {StoreService, ProductLogicService, CartService, AddressService, PermissionService, OrderService} from '@core/services';
import {environment} from '@environments/environment';

@Component({
  selector: 'app-order-placed',
  templateUrl: './order-placed.component.html',
  styleUrls: ['./order-placed.component.scss']
})
export class OrderPlacedComponent implements OnInit {
  orderDetails: any;
  cartId: number = 0;
  shipmentCost: number = 0;
  transactionData: any;
  paymentTotal: number = 0;
  currencyCode: string = '';
  deliveryOption: string = '';
  isConfig = environment.isStoreCloud
  isShipmentFee: boolean = false;
  isMobileLayout: boolean = false;
  screenWidth: number;
  mobilDeliveryOption:any;
  mobilDeliveryOptionTime:any;
  hasDiscount:boolean=false
  orderDiscount:number=0;
  orderId: string| null = '';
  disableCent : string
  decimalValue = 0;

  constructor(public addressService: AddressService, public orderService:OrderService, private store: StoreService, private cartService: CartService,
    private permissionService: PermissionService) {
      this.screenWidth = window.innerWidth;
      this.isShipmentFee = this.permissionService.hasPermission('Shipment-Fee')
      this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');
    }

    ngOnInit(): void {
    this.disableCent = localStorage.getItem('DisableCents') ?? '';
      this.decimalValue = JSON.parse(localStorage.getItem('CurrencyDecimal') ?? '')
    this.getCurrentCartId();
    this.store.subscription('orderData')
      .subscribe({
        next: (res: any) => {


          if (res) {
            this.orderDetails = res;
            if (this.orderDetails != null) {
              this.orderDetails.orderAmount = Math.round(this.orderDetails.orderAmount * 100) / 100;
              if (this.orderDetails.productDetails[0].currencyCode) {
                this.currencyCode = this.orderDetails.productDetails[0].currencyCode;
              }
              this.orderId = this.orderDetails.orderId;
              this.getOrderDiscount(this.orderDetails.orderId)
            }
          }
        },
        error: (err: any) => {
          console.error(err);
        }
      });
    this.store.subscription('shipmentCost')
      .subscribe({
        next: (res: any) => {
          if(res.deliveryOption){
            this.deliveryOption = res.deliveryOption.name
            if(res.deliveryOption.name.includes("(")){
              let options = res.deliveryOption.name.split(" ")
              this.mobilDeliveryOptionTime = options.pop()
              this.mobilDeliveryOption = res.deliveryOption.name.replace(this.mobilDeliveryOptionTime, "")
            }
          }
          if (res.totalDeliveryCost !== null && res.totalDeliveryCost > 0) {
            this.shipmentCost = Math.round(Number(res.totalDeliveryCost) * 100) / 100;
            this.paymentTotal = Math.round(((this.orderDetails?.orderAmount + this.shipmentCost) + Number.EPSILON) * 100) / 100;
          } else {
            this.paymentTotal = Math.round(((this.orderDetails?.orderAmount) + Number.EPSILON) * 100) / 100;
          }
        },
        error: (err: any) => {
          console.error(err);
        }
      });
    this.store.subscription('transactionData')
      .subscribe({
        next: (res: any) => {

          if (res) {
            this.transactionData = res;
            if (this.transactionData?.CardNumber) {
              this.transactionData.CardNumber = "****" + this.transactionData.CardNumber.slice(-4);
            }

            if (this.transactionData?.currencyCode != undefined) {
              this.currencyCode = this.transactionData?.currencyCode;
            }
          }
        },
        error: (err: any) => {
          console.error(err);
        }
      });
  }


  getOrderDiscount (id: number) {
    this.orderService.getOrderDiscount(id).subscribe({
      next: (res) => {
        if (res.success) {
          this.orderDiscount=res.data ? res.data : this.hasDiscount
          if(this.orderDiscount){
            this.paymentTotal =  this.paymentTotal - this.orderDiscount;

          }
        }
      },
      error: (err: any) => {
        console.error(err);
      },
    });
  }

  getCurrentCartId() {
    this.store.subscription('cartProducts')
      .subscribe({
        next: (res: any) => {

          this.cartId = res[0]?.cartId;
        },
        error: (err: any) => {
          console.error(err);
        }
      });
  }

}
