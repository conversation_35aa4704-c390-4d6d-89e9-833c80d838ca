{"ast": null, "code": "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n  if (typeof uri === \"object\") {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n  const parsed = url(uri, opts.path || \"/socket.io\");\n  const source = parsed.source;\n  const id = parsed.id;\n  const path = parsed.path;\n  const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n  const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n  let io;\n  if (newConnection) {\n    io = new Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      cache[id] = new Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.queryKey;\n  }\n  return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n  Manager,\n  Socket,\n  io: lookup,\n  connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}