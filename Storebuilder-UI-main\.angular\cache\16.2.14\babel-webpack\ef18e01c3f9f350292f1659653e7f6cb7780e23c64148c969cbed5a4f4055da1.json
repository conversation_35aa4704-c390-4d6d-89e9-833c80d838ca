{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"ngx-owl-carousel-o\";\nimport * as i4 from \"../product-card/product-card.component\";\nfunction ProductSliderComponent_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mtn-product-card\", 4);\n  }\n  if (rf & 2) {\n    const newProduct_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"currency\", ctx_r2.currency)(\"isDragging\", ctx_r2.isDragging)(\"product\", newProduct_r1);\n  }\n}\nfunction ProductSliderComponent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProductSliderComponent_2_ng_template_0_Template, 1, 3, \"ng-template\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"width\", ctx_r0.products.length < 6 ? 222 : 0);\n  }\n}\nexport class ProductSliderComponent {\n  constructor(store, reviewsService) {\n    this.store = store;\n    this.reviewsService = reviewsService;\n    this.options = {};\n    this.currency = {};\n    this.products = [];\n    this.isDragging = false;\n    this.newProduct = [];\n  }\n  ngOnInit() {\n    this.initData();\n    this.products.forEach(obj => {\n      let selectedVariance;\n      let defaultVariant = obj?.productVariances.find(variant => variant.isDefault);\n      if (defaultVariant) {\n        selectedVariance = defaultVariant;\n      } else {\n        let approvedVariant = obj?.productVariances.find(variant => !variant.soldOut);\n        if (approvedVariant) {\n          selectedVariance = approvedVariant;\n        } else {\n          selectedVariance = obj?.productVariances[0];\n        }\n      }\n      let features = [];\n      if (selectedVariance?.productFeaturesList) {\n        features = selectedVariance?.productFeaturesList[0]?.featureList;\n      }\n      this.newProduct.push({\n        productId: obj?.id,\n        productName: obj?.name,\n        priceValue: selectedVariance?.price,\n        salePriceValue: selectedVariance?.salePrice,\n        currencyCode: obj?.currencyCode,\n        masterImageUrl: selectedVariance?.masterImageUrl,\n        thumbnailImages: selectedVariance?.thumbnailImages,\n        soldOut: selectedVariance?.soldOut,\n        rate: selectedVariance?.rate,\n        count: selectedVariance?.count ?? 0,\n        specProductId: selectedVariance.specProductId,\n        channelId: obj.channelId ?? 1,\n        salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n        shopId: obj.shopId,\n        isHot: features?.includes(1),\n        isNew: features?.includes(2),\n        isBest: features?.includes(3),\n        quantity: selectedVariance.quantity,\n        proSchedulingId: selectedVariance.proSchedulingId,\n        stockPerSKU: selectedVariance.stockPerSKU,\n        sku: selectedVariance?.sku,\n        skuAutoGenerated: selectedVariance.skuAutoGenerated\n      });\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.store.subscription('currency').subscribe({\n        next: res => this.currency = res\n      });\n    }, 10);\n  }\n  initData() {\n    this.options = {\n      stagePadding: 0,\n      loop: false,\n      autoplay: true,\n      dots: false,\n      autoWidth: true,\n      nav: true,\n      lazyLoad: true,\n      autoplayHoverPause: true,\n      rewind: false,\n      margin: 30,\n      navText: [],\n      responsive: {\n        0: {\n          items: 2,\n          nav: false\n        },\n        300: {\n          items: 2,\n          nav: false\n        },\n        400: {\n          items: 3,\n          nav: false\n        },\n        600: {\n          items: 4\n        },\n        740: {\n          items: 5\n        },\n        800: {\n          items: 6\n        },\n        940: {\n          items: 7\n        },\n        1280: {\n          items: 8\n        },\n        1300: {\n          items: 9\n        },\n        1400: {\n          items: 6\n        },\n        1600: {\n          items: 6\n        }\n      }\n    };\n  }\n  carouselDrag(event) {\n    this.isDragging = event;\n  }\n}\nProductSliderComponent.ɵfac = function ProductSliderComponent_Factory(t) {\n  return new (t || ProductSliderComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.ReviewsService));\n};\nProductSliderComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ProductSliderComponent,\n  selectors: [[\"app-mtn-product-slider\"]],\n  inputs: {\n    products: \"products\"\n  },\n  decls: 3,\n  vars: 2,\n  consts: [[1, \"product-slider\", \"card-slider\"], [3, \"options\", \"dragging\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\", \"class\", \"slide\", 3, \"width\"], [2, \"width\", \"100%\", 3, \"currency\", \"isDragging\", \"product\"]],\n  template: function ProductSliderComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"section\", 0)(1, \"owl-carousel-o\", 1);\n      i0.ɵɵlistener(\"dragging\", function ProductSliderComponent_Template_owl_carousel_o_dragging_1_listener($event) {\n        return ctx.carouselDrag($event.dragging);\n      });\n      i0.ɵɵtemplate(2, ProductSliderComponent_2_Template, 1, 1, null, 2);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"options\", ctx.options);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.newProduct);\n    }\n  },\n  dependencies: [i2.NgForOf, i3.CarouselComponent, i3.CarouselSlideDirective, i4.ProductCardComponent],\n  styles: [\".product-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}