<section class="update-password-page">
  <div *ngIf="loading" class="spinner">
    <p-progressSpinner></p-progressSpinner>
  </div>

  <ng-container *ngIf="!loading">
    <div
      class="content-container"
      [ngClass]="{
        'mobile-content-container': isMobileLayout && screenWidth <= 767
      }"
    >
      <div class="grid justify-content-center shadow-signin">
        <!-- Desktop image -->
        <div class="image col-12 desktop-only">
          <img src="assets/images/registerLogo.svg" alt="" srcset="" />
        </div>

        <div class="col-12 col-md-8 col-lg-6 bg-white content-body">
          <!-- Mobile header -->
          <div class="mobile-header mobile-only">
            <img src="assets/images/signup.svg" alt="Sign Up" />
            <div class="head-desc">
              <h2 class="signup-heading">
                {{ "auth.registerPassword.title" | translate }}
              </h2>
              <span class="signup-desc">{{
                "auth.registerPassword.desc" | translate
              }}</span>
            </div>
          </div>

          <!-- Desktop header -->
          <div class="desktop-only">
            <p class="m-0 py-0 signup-heading">
              {{ "auth.registerPassword.title" | translate }}
            </p>
            <div class="main-color mb-3 please-signin">
              {{ "auth.registerPassword.subTitle" | translate }}
            </div>
          </div>

          <div class="p-fluid">
            <form autocomplete="new-password">
              <!-- Name Input Component -->
              <app-name-input
                [labelColor]="labelColor"
                [floatingLabel]="floatingLabel"
                (firstNameChange)="onFirstNameChange($event)"
                (lastNameChange)="onLastNameChange($event)"
                (validationChange)="onNameValidationChange($event)"
                (blur)="onBlur()"
              >
              </app-name-input>
            <div  *ngIf="selectedRegistrationMethod === 'email'">
              <app-phone-input
                [maxLength]="phoneInputLength"
                [selectedCountryISO]="CustomCountryISO"
                [preferredCountries]="preferredCountries"
                [placeholder]="customPlaceHolder"
                (phoneNumberChange)="onPhoneNumberChange($event)"
                (validationChange)="onPhoneValidationChange($event)"
                (rawPhoneNumberChange)="onRawPhoneNumberChange($event)"

              >
              </app-phone-input>
              </div>
              <!-- Email Input Component -->
              <app-email-input
                *ngIf="selectedRegistrationMethod === 'phone'"
                [emailRequired]="'false'"
                [floatingLabel]="floatingLabel"
                [labelColor]="labelColor"
                (emailChange)="onEmailChange($event)"
                (validationChange)="onEmailValidationChange($event)"
                (blur)="onBlur()"
                [emailLabel]="
                  floatingLabel
                    ? 'auth.registerPassword.email'
                    : ('auth.registerPassword.emailLabel' | translate)
                "
              >
              </app-email-input>

              <!-- Referral By -->
              <div class="p-field p-col-12">
                <span [ngClass]="{ 'p-float-label mt-3': floatingLabel }">
                  <label
                    [ngStyle]="{
                      color: referralBy.length > 50 ? 'red' : labelColor
                    }"
                    [for]="floatingLabel ? 'custom-float-input' : 'float-input'"
                  >
                    {{ "auth.registerPassword.referralBy" | translate }}
                    {{ floatingLabel ? "" : "" }}
                  </label>
                  <input
                    (click)="onBlur()"
                    (input)="onReferralChange($event)"
                    [(ngModel)]="referralBy"
                    [ngModelOptions]="{ standalone: true }"
                    [id]="floatingLabel ? 'custom-float-input' : 'float-input'"
                    name="referralBy"
                    pInputText
                    type="text"
                  />
                </span>
                <p *ngIf="referralBy.length > 50" class="field-error">
                  <small style="color: red">{{
                    "auth.registerPassword.referralByErrors" | translate
                  }}</small>
                </p>
              </div>

              <!-- Password Input Components -->
              <app-password-input
                [showForgotPassword]="false"
                [floatingLabel]="floatingLabel"
                (passwordChange)="onPasswordChange($event)"
                (validationChange)="onPasswordValidationChange($event)"
              >
              </app-password-input>

              <!-- Confirm Password -->
              <div class="p-field p-col-12">
                <span [ngClass]="{ 'p-float-label mt-3': floatingLabel }">
                  <label
                    [ngStyle]="{
                      color:
                        (firstNameFlag && confirmPassword.length === 0) ||
                        (password.length > 0 &&
                          confirmPassword.length > 0 &&
                          password !== confirmPassword)
                          ? 'red'
                          : labelColor
                    }"
                  >
                    {{ "auth.registerPassword.confirmPassword" | translate }}
                    {{ "*" }}
                  </label>
                  <p-password
                    (click)="onBlur()"
                    [(ngModel)]="confirmPassword"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onConfirmPasswordChange($event)"
                    [feedback]="false"
                    [toggleMask]="true"
                    class="customClass"
                    name="confirmPassword"
                    [id]="floatingLabel ? 'custom-password-input' : ''"
                    required="true"
                  >
                  </p-password>
                  <label *ngIf="floatingLabel"
                    >{{
                      "auth.registerPassword.confirmPassword" | translate
                    }}
                    *</label
                  >
                </span>
              </div>

              <!-- Password validation list -->
              <div class="p-field p-col-12">
                <ul class="list p-0 mt-3">
                  <li class="list-none font-size-13 font-italic mb-2">
                    <ng-container *ngIf="!floatingLabel">
                      <em
                        *ngIf="hasMinimum8Chars === undefined"
                        aria-hidden="true"
                        class="pi pi-info-circle"
                        style="color: grey; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasMinimum8Chars === true"
                        aria-hidden="true"
                        class="pi pi-check-circle"
                        style="color: #01b467; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasMinimum8Chars === false"
                        aria-hidden="true"
                        class="pi pi-times-circle"
                        style="color: red; font-size: 0.8rem"
                      ></em>
                    </ng-container>
                    <ng-container *ngIf="floatingLabel">
                      <img
                        src="assets/images/signUp/font-Awsome-info-circle.svg"
                        *ngIf="hasMinimum8Chars === undefined"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-check-circle.svg"
                        *ngIf="hasMinimum8Chars === true"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-times-circle.svg"
                        *ngIf="hasMinimum8Chars === false"
                        aria-hidden="true"
                        class="size-circle"
                      />
                    </ng-container>
                    <span
                      [ngStyle]="{
                        color:
                          hasMinimum8Chars === true
                            ? '#01B467'
                            : hasMinimum8Chars === false
                            ? 'red'
                            : 'grey'
                      }"
                      class="ml-2 instruction"
                    >
                      {{ "auth.registerPassword.validation1" | translate }}
                    </span>
                  </li>

                  <li class="list-none font-size-13 font-italic mb-2">
                    <ng-container *ngIf="!floatingLabel">
                      <em
                        *ngIf="hasLowerChar === undefined"
                        aria-hidden="true"
                        class="pi pi-info-circle"
                        style="color: grey; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasLowerChar === true"
                        aria-hidden="true"
                        class="pi pi-check-circle"
                        style="color: #01b467; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasLowerChar === false"
                        aria-hidden="true"
                        class="pi pi-times-circle"
                        style="color: red; font-size: 0.8rem"
                      ></em>
                    </ng-container>
                    <ng-container *ngIf="floatingLabel">
                      <img
                        src="assets/images/signUp/font-Awsome-info-circle.svg"
                        *ngIf="hasLowerChar === undefined"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-check-circle.svg"
                        *ngIf="hasLowerChar === true"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-times-circle.svg"
                        *ngIf="hasLowerChar === false"
                        aria-hidden="true"
                        class="size-circle"
                      />
                    </ng-container>
                    <span
                      [ngStyle]="{
                        color:
                          hasLowerChar === true
                            ? '#01B467'
                            : hasLowerChar === false
                            ? 'red'
                            : 'grey'
                      }"
                      class="ml-2 instruction"
                    >
                      {{ "auth.registerPassword.validation2" | translate }}
                    </span>
                  </li>

                  <li class="list-none font-size-13 font-italic mb-2">
                    <ng-container *ngIf="!floatingLabel">
                      <em
                        *ngIf="hasUpperChar === undefined"
                        aria-hidden="true"
                        class="pi pi-info-circle"
                        style="color: grey; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasUpperChar === true"
                        aria-hidden="true"
                        class="pi pi-check-circle"
                        style="color: #01b467; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasUpperChar === false"
                        aria-hidden="true"
                        class="pi pi-times-circle"
                        style="color: red; font-size: 0.8rem"
                      ></em>
                    </ng-container>
                    <ng-container *ngIf="floatingLabel">
                      <img
                        src="assets/images/signUp/font-Awsome-info-circle.svg"
                        *ngIf="hasUpperChar === undefined"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-check-circle.svg"
                        *ngIf="hasUpperChar === true"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-times-circle.svg"
                        *ngIf="hasUpperChar === false"
                        aria-hidden="true"
                        class="size-circle"
                      />
                    </ng-container>
                    <span
                      [ngStyle]="{
                        color:
                          hasUpperChar === true
                            ? '#01B467'
                            : hasUpperChar === false
                            ? 'red'
                            : 'grey'
                      }"
                      class="ml-2 instruction"
                    >
                      {{ "auth.registerPassword.validation3" | translate }}
                    </span>
                  </li>

                  <li class="list-none font-size-13 font-italic mb-2">
                    <ng-container *ngIf="!floatingLabel">
                      <em
                        *ngIf="hasAtleastOneNumber === undefined"
                        aria-hidden="true"
                        class="pi pi-info-circle"
                        style="color: grey; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasAtleastOneNumber === true"
                        aria-hidden="true"
                        class="pi pi-check-circle"
                        style="color: #01b467; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasAtleastOneNumber === false"
                        aria-hidden="true"
                        class="pi pi-times-circle"
                        style="color: red; font-size: 0.8rem"
                      ></em>
                    </ng-container>
                    <ng-container *ngIf="floatingLabel">
                      <img
                        src="assets/images/signUp/font-Awsome-info-circle.svg"
                        *ngIf="hasAtleastOneNumber === undefined"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-check-circle.svg"
                        *ngIf="hasAtleastOneNumber === true"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-times-circle.svg"
                        *ngIf="hasAtleastOneNumber === false"
                        aria-hidden="true"
                        class="size-circle"
                      />
                    </ng-container>
                    <span
                      [ngStyle]="{
                        color:
                          hasAtleastOneNumber === true
                            ? '#01B467'
                            : hasAtleastOneNumber === false
                            ? 'red'
                            : 'grey'
                      }"
                      class="ml-2 instruction"
                    >
                      {{ "auth.registerPassword.validation4" | translate }}
                    </span>
                  </li>

                  <li class="list-none font-size-13 font-italic mb-2">
                    <ng-container *ngIf="!floatingLabel">
                      <em
                        *ngIf="hasSpecialChars === undefined"
                        aria-hidden="true"
                        class="pi pi-info-circle"
                        style="color: grey; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasSpecialChars === true"
                        aria-hidden="true"
                        class="pi pi-check-circle"
                        style="color: #01b467; font-size: 0.8rem"
                      ></em>
                      <em
                        *ngIf="hasSpecialChars === false"
                        aria-hidden="true"
                        class="pi pi-times-circle"
                        style="color: red; font-size: 0.8rem"
                      ></em>
                    </ng-container>
                    <ng-container *ngIf="floatingLabel">
                      <img
                        src="assets/images/signUp/font-Awsome-info-circle.svg"
                        *ngIf="hasSpecialChars === undefined"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-check-circle.svg"
                        *ngIf="hasSpecialChars === true"
                        aria-hidden="true"
                        class="size-circle"
                      />
                      <img
                        src="assets/images/signUp/font-Awsome-times-circle.svg"
                        *ngIf="hasSpecialChars === false"
                        aria-hidden="true"
                        class="size-circle"
                      />
                    </ng-container>
                    <span
                      [ngStyle]="{
                        color:
                          hasSpecialChars === true
                            ? '#01B467'
                            : hasSpecialChars === false
                            ? 'red'
                            : 'grey'
                      }"
                      class="ml-2 instruction"
                    >
                      {{ "auth.registerPassword.validation5" | translate }}
                    </span>
                  </li>

                  <li class="list-none font-size-13 font-italic">
                    <p>
                      <ng-container *ngIf="!floatingLabel">
                        <em
                          *ngIf="passwordMatched === undefined"
                          aria-hidden="true"
                          class="pi pi-info-circle"
                          style="color: grey; font-size: 0.8rem"
                        ></em>
                        <em
                          *ngIf="passwordMatched === false"
                          aria-hidden="true"
                          class="pi pi-times-circle"
                          style="color: red; font-size: 0.8rem"
                        ></em>
                        <em
                          *ngIf="passwordMatched === true"
                          aria-hidden="true"
                          class="pi pi-check-circle"
                          style="color: #01b467; font-size: 0.8rem"
                        ></em>
                      </ng-container>
                      <ng-container *ngIf="floatingLabel">
                        <img
                          src="assets/images/signUp/font-Awsome-info-circle.svg"
                          *ngIf="passwordMatched === undefined"
                          aria-hidden="true"
                          class="size-circle"
                        />
                        <img
                          src="assets/images/signUp/font-Awsome-check-circle.svg"
                          *ngIf="passwordMatched === true"
                          aria-hidden="true"
                          class="size-circle"
                        />
                        <img
                          src="assets/images/signUp/font-Awsome-times-circle.svg"
                          *ngIf="passwordMatched === false"
                          aria-hidden="true"
                          class="size-circle"
                        />
                      </ng-container>
                      <span
                        [ngStyle]="{
                          color:
                            passwordMatched === true
                              ? '#01B467'
                              : passwordMatched === false
                              ? 'red'
                              : 'grey'
                        }"
                        class="ml-2 instruction"
                      >
                        {{ "auth.registerPassword.validation7" | translate }}
                      </span>
                    </p>
                  </li>
                </ul>
              </div>
            </form>

            <!-- Submit button -->
            <button
              (click)="checkSubmissionFlow(floatingLabel)"
              [disabled]="checkValidity()"
              class="submit-btn"
              [label]="'auth.registerPassword.signup' | translate"
              pButton
              type="button"
            ></button>

            <!-- Opt In for newsletters -->
            <div class="opt-in" id="opt-in">
              <p-checkbox [(ngModel)]="isChecked" [binary]="true"></p-checkbox>
              <span>{{ "auth.optInCheckBox" | translate }}</span>
            </div>

            <!-- Terms and Conditions - Desktop -->
            <p class="signin-agreement cursor-pointer desktop-only">
              {{ "signIn.AgreeTermsOne" | translate }}
              <a (click)="termsAndConditionsModal()">{{
                "signIn.AgreeTermsTwo" | translate
              }}</a>
              &nbsp;{{ "signIn.AgreeTermsThree" | translate }}
              <a (click)="reloadCurrentPage(3, 'Privacy policy')">{{
                "signIn.AgreeTermsFour" | translate
              }}</a>
              &nbsp;{{ "signIn.AgreeTermsFive" | translate }}.
            </p>

            <!-- Terms and Conditions - Mobile -->
            <div class="p-field p-col-12 mt-3 mb-4 mobile-only">
              <div class="Terms-conditions">
                {{ "auth.registerPassword.agreementText" | translate }}
                <span
                  (click)="termsAndConditionsModal()"
                  class="underline cursor-pointer main-color"
                >
                  {{ "auth.registerPassword.termsAndConds" | translate }}
                </span>
              </div>
            </div>

            <!-- Sign in action - Mobile only -->
            <!-- <div class="signin-action mobile-only">
              <span class="subtitle">{{ 'auth.registerPassword.haveAccount' | translate }}</span>
              <a
                routerLink="/login"
                [label]="'auth.registerPassword.signin' | translate"
                class="signin-btn main-btn"
                pButton
                type="button">
              </a>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Dialogs -->
  <p-dialog
    [(visible)]="displayApprovedModal"
    [breakpoints]="{ '960px': '75vw', '640px': '100vw' }"
    [closable]="true"
    [resizable]="false"
    [style]="{ width: '40vw' }"
  >
    <ng-template pTemplate="content">
      <div
        class="icon mt-5 bg-#01B467-500 text-white text-center w-3rem h-2rem border-circle icon bg-#01B467-500"
      >
        <em class="pi pi-check"></em>
      </div>
      <p class="font-bold text-center text-black-alpha-90">
        {{ "auth.registerPassword.continue" | translate }}
      </p>
    </ng-template>
    <ng-template pTemplate="footer">
      <div class="d-flex jc-center ai-center" style="gap: 1rem">
        <button
          (click)="checkSubmissionFlow(floatingLabel)"
          class="p-field my-4 width-25 font-size-14 second-btn"
          label="Confirm"
          pButton
          type="button"
        ></button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog
    [(visible)]="displayTermsAndConditions"
    [baseZIndex]="10000"
    [breakpoints]="{ '960px': '75vw', '640px': '100vw' }"
    [closable]="true"
    [style]="{ width: '30vw' }"
    class="termsConditionModal"
  >
    <ng-template pTemplate="content">
      <div class="termsandcondition">
        <h1 *ngIf="aboutUsDetails.title" class="term-condition-heading">
          {{ "auth.registerPassword.termsAndConditions" | translate }}
        </h1>
        <div class="h-19rem terms-condition">
          <p
            *ngIf="aboutUsDetails.content"
            [innerHtml]="termsAndConditions"
            class="col-12 font-size-18 m-0 py-0 mx-4"
          >
            {{ termsAndConditions }}
          </p>
        </div>
      </div>
    </ng-template>
    <ng-template pTemplate="footer">
      <button
        (click)="closeTermsModal()"
        [label]="'buttons.close' | translate"
        class="p-field p-col-12 my-4 width-25 m-auto font-size-14 second-btn"
        pButton
        type="button"
      ></button>
    </ng-template>
  </p-dialog>
</section>
