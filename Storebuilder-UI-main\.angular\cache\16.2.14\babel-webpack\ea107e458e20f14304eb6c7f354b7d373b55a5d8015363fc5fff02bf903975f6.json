{"ast": null, "code": "/* @preserve\n * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com\n * (c) 2010-2023 <PERSON>, (c) 2010-2011 CloudMade\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) : typeof define === 'function' && define.amd ? define(['exports'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.leaflet = {}));\n})(this, function (exports) {\n  'use strict';\n\n  var version = \"1.9.4\";\n\n  /*\r\n   * @namespace Util\r\n   *\r\n   * Various utility functions, used by Leaflet internally.\r\n   */\n\n  // @function extend(dest: Object, src?: Object): Object\n  // Merges the properties of the `src` object (or multiple objects) into `dest` object and returns the latter. Has an `L.extend` shortcut.\n  function extend(dest) {\n    var i, j, len, src;\n    for (j = 1, len = arguments.length; j < len; j++) {\n      src = arguments[j];\n      for (i in src) {\n        dest[i] = src[i];\n      }\n    }\n    return dest;\n  }\n\n  // @function create(proto: Object, properties?: Object): Object\n  // Compatibility polyfill for [Object.create](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object/create)\n  var create$2 = Object.create || function () {\n    function F() {}\n    return function (proto) {\n      F.prototype = proto;\n      return new F();\n    };\n  }();\n\n  // @function bind(fn: Function, …): Function\n  // Returns a new function bound to the arguments passed, like [Function.prototype.bind](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Function/bind).\n  // Has a `L.bind()` shortcut.\n  function bind(fn, obj) {\n    var slice = Array.prototype.slice;\n    if (fn.bind) {\n      return fn.bind.apply(fn, slice.call(arguments, 1));\n    }\n    var args = slice.call(arguments, 2);\n    return function () {\n      return fn.apply(obj, args.length ? args.concat(slice.call(arguments)) : arguments);\n    };\n  }\n\n  // @property lastId: Number\n  // Last unique ID used by [`stamp()`](#util-stamp)\n  var lastId = 0;\n\n  // @function stamp(obj: Object): Number\n  // Returns the unique ID of an object, assigning it one if it doesn't have it.\n  function stamp(obj) {\n    if (!('_leaflet_id' in obj)) {\n      obj['_leaflet_id'] = ++lastId;\n    }\n    return obj._leaflet_id;\n  }\n\n  // @function throttle(fn: Function, time: Number, context: Object): Function\n  // Returns a function which executes function `fn` with the given scope `context`\n  // (so that the `this` keyword refers to `context` inside `fn`'s code). The function\n  // `fn` will be called no more than one time per given amount of `time`. The arguments\n  // received by the bound function will be any arguments passed when binding the\n  // function, followed by any arguments passed when invoking the bound function.\n  // Has an `L.throttle` shortcut.\n  function throttle(fn, time, context) {\n    var lock, args, wrapperFn, later;\n    later = function () {\n      // reset lock and call if queued\n      lock = false;\n      if (args) {\n        wrapperFn.apply(context, args);\n        args = false;\n      }\n    };\n    wrapperFn = function () {\n      if (lock) {\n        // called too soon, queue to call later\n        args = arguments;\n      } else {\n        // call and lock until later\n        fn.apply(context, arguments);\n        setTimeout(later, time);\n        lock = true;\n      }\n    };\n    return wrapperFn;\n  }\n\n  // @function wrapNum(num: Number, range: Number[], includeMax?: Boolean): Number\n  // Returns the number `num` modulo `range` in such a way so it lies within\n  // `range[0]` and `range[1]`. The returned value will be always smaller than\n  // `range[1]` unless `includeMax` is set to `true`.\n  function wrapNum(x, range, includeMax) {\n    var max = range[1],\n      min = range[0],\n      d = max - min;\n    return x === max && includeMax ? x : ((x - min) % d + d) % d + min;\n  }\n\n  // @function falseFn(): Function\n  // Returns a function which always returns `false`.\n  function falseFn() {\n    return false;\n  }\n\n  // @function formatNum(num: Number, precision?: Number|false): Number\n  // Returns the number `num` rounded with specified `precision`.\n  // The default `precision` value is 6 decimal places.\n  // `false` can be passed to skip any processing (can be useful to avoid round-off errors).\n  function formatNum(num, precision) {\n    if (precision === false) {\n      return num;\n    }\n    var pow = Math.pow(10, precision === undefined ? 6 : precision);\n    return Math.round(num * pow) / pow;\n  }\n\n  // @function trim(str: String): String\n  // Compatibility polyfill for [String.prototype.trim](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim)\n  function trim(str) {\n    return str.trim ? str.trim() : str.replace(/^\\s+|\\s+$/g, '');\n  }\n\n  // @function splitWords(str: String): String[]\n  // Trims and splits the string on whitespace and returns the array of parts.\n  function splitWords(str) {\n    return trim(str).split(/\\s+/);\n  }\n\n  // @function setOptions(obj: Object, options: Object): Object\n  // Merges the given properties to the `options` of the `obj` object, returning the resulting options. See `Class options`. Has an `L.setOptions` shortcut.\n  function setOptions(obj, options) {\n    if (!Object.prototype.hasOwnProperty.call(obj, 'options')) {\n      obj.options = obj.options ? create$2(obj.options) : {};\n    }\n    for (var i in options) {\n      obj.options[i] = options[i];\n    }\n    return obj.options;\n  }\n\n  // @function getParamString(obj: Object, existingUrl?: String, uppercase?: Boolean): String\n  // Converts an object into a parameter URL string, e.g. `{a: \"foo\", b: \"bar\"}`\n  // translates to `'?a=foo&b=bar'`. If `existingUrl` is set, the parameters will\n  // be appended at the end. If `uppercase` is `true`, the parameter names will\n  // be uppercased (e.g. `'?A=foo&B=bar'`)\n  function getParamString(obj, existingUrl, uppercase) {\n    var params = [];\n    for (var i in obj) {\n      params.push(encodeURIComponent(uppercase ? i.toUpperCase() : i) + '=' + encodeURIComponent(obj[i]));\n    }\n    return (!existingUrl || existingUrl.indexOf('?') === -1 ? '?' : '&') + params.join('&');\n  }\n  var templateRe = /\\{ *([\\w_ -]+) *\\}/g;\n\n  // @function template(str: String, data: Object): String\n  // Simple templating facility, accepts a template string of the form `'Hello {a}, {b}'`\n  // and a data object like `{a: 'foo', b: 'bar'}`, returns evaluated string\n  // `('Hello foo, bar')`. You can also specify functions instead of strings for\n  // data values — they will be evaluated passing `data` as an argument.\n  function template(str, data) {\n    return str.replace(templateRe, function (str, key) {\n      var value = data[key];\n      if (value === undefined) {\n        throw new Error('No value provided for variable ' + str);\n      } else if (typeof value === 'function') {\n        value = value(data);\n      }\n      return value;\n    });\n  }\n\n  // @function isArray(obj): Boolean\n  // Compatibility polyfill for [Array.isArray](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray)\n  var isArray = Array.isArray || function (obj) {\n    return Object.prototype.toString.call(obj) === '[object Array]';\n  };\n\n  // @function indexOf(array: Array, el: Object): Number\n  // Compatibility polyfill for [Array.prototype.indexOf](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/indexOf)\n  function indexOf(array, el) {\n    for (var i = 0; i < array.length; i++) {\n      if (array[i] === el) {\n        return i;\n      }\n    }\n    return -1;\n  }\n\n  // @property emptyImageUrl: String\n  // Data URI string containing a base64-encoded empty GIF image.\n  // Used as a hack to free memory from unused images on WebKit-powered\n  // mobile devices (by setting image `src` to this string).\n  var emptyImageUrl = 'data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=';\n\n  // inspired by https://paulirish.com/2011/requestanimationframe-for-smart-animating/\n\n  function getPrefixed(name) {\n    return window['webkit' + name] || window['moz' + name] || window['ms' + name];\n  }\n  var lastTime = 0;\n\n  // fallback for IE 7-8\n  function timeoutDefer(fn) {\n    var time = +new Date(),\n      timeToCall = Math.max(0, 16 - (time - lastTime));\n    lastTime = time + timeToCall;\n    return window.setTimeout(fn, timeToCall);\n  }\n  var requestFn = window.requestAnimationFrame || getPrefixed('RequestAnimationFrame') || timeoutDefer;\n  var cancelFn = window.cancelAnimationFrame || getPrefixed('CancelAnimationFrame') || getPrefixed('CancelRequestAnimationFrame') || function (id) {\n    window.clearTimeout(id);\n  };\n\n  // @function requestAnimFrame(fn: Function, context?: Object, immediate?: Boolean): Number\n  // Schedules `fn` to be executed when the browser repaints. `fn` is bound to\n  // `context` if given. When `immediate` is set, `fn` is called immediately if\n  // the browser doesn't have native support for\n  // [`window.requestAnimationFrame`](https://developer.mozilla.org/docs/Web/API/window/requestAnimationFrame),\n  // otherwise it's delayed. Returns a request ID that can be used to cancel the request.\n  function requestAnimFrame(fn, context, immediate) {\n    if (immediate && requestFn === timeoutDefer) {\n      fn.call(context);\n    } else {\n      return requestFn.call(window, bind(fn, context));\n    }\n  }\n\n  // @function cancelAnimFrame(id: Number): undefined\n  // Cancels a previous `requestAnimFrame`. See also [window.cancelAnimationFrame](https://developer.mozilla.org/docs/Web/API/window/cancelAnimationFrame).\n  function cancelAnimFrame(id) {\n    if (id) {\n      cancelFn.call(window, id);\n    }\n  }\n  var Util = {\n    __proto__: null,\n    extend: extend,\n    create: create$2,\n    bind: bind,\n    get lastId() {\n      return lastId;\n    },\n    stamp: stamp,\n    throttle: throttle,\n    wrapNum: wrapNum,\n    falseFn: falseFn,\n    formatNum: formatNum,\n    trim: trim,\n    splitWords: splitWords,\n    setOptions: setOptions,\n    getParamString: getParamString,\n    template: template,\n    isArray: isArray,\n    indexOf: indexOf,\n    emptyImageUrl: emptyImageUrl,\n    requestFn: requestFn,\n    cancelFn: cancelFn,\n    requestAnimFrame: requestAnimFrame,\n    cancelAnimFrame: cancelAnimFrame\n  };\n\n  // @class Class\n  // @aka L.Class\n\n  // @section\n  // @uninheritable\n\n  // Thanks to John Resig and Dean Edwards for inspiration!\n\n  function Class() {}\n  Class.extend = function (props) {\n    // @function extend(props: Object): Function\n    // [Extends the current class](#class-inheritance) given the properties to be included.\n    // Returns a Javascript function that is a class constructor (to be called with `new`).\n    var NewClass = function () {\n      setOptions(this);\n\n      // call the constructor\n      if (this.initialize) {\n        this.initialize.apply(this, arguments);\n      }\n\n      // call all constructor hooks\n      this.callInitHooks();\n    };\n    var parentProto = NewClass.__super__ = this.prototype;\n    var proto = create$2(parentProto);\n    proto.constructor = NewClass;\n    NewClass.prototype = proto;\n\n    // inherit parent's statics\n    for (var i in this) {\n      if (Object.prototype.hasOwnProperty.call(this, i) && i !== 'prototype' && i !== '__super__') {\n        NewClass[i] = this[i];\n      }\n    }\n\n    // mix static properties into the class\n    if (props.statics) {\n      extend(NewClass, props.statics);\n    }\n\n    // mix includes into the prototype\n    if (props.includes) {\n      checkDeprecatedMixinEvents(props.includes);\n      extend.apply(null, [proto].concat(props.includes));\n    }\n\n    // mix given properties into the prototype\n    extend(proto, props);\n    delete proto.statics;\n    delete proto.includes;\n\n    // merge options\n    if (proto.options) {\n      proto.options = parentProto.options ? create$2(parentProto.options) : {};\n      extend(proto.options, props.options);\n    }\n    proto._initHooks = [];\n\n    // add method for calling all hooks\n    proto.callInitHooks = function () {\n      if (this._initHooksCalled) {\n        return;\n      }\n      if (parentProto.callInitHooks) {\n        parentProto.callInitHooks.call(this);\n      }\n      this._initHooksCalled = true;\n      for (var i = 0, len = proto._initHooks.length; i < len; i++) {\n        proto._initHooks[i].call(this);\n      }\n    };\n    return NewClass;\n  };\n\n  // @function include(properties: Object): this\n  // [Includes a mixin](#class-includes) into the current class.\n  Class.include = function (props) {\n    var parentOptions = this.prototype.options;\n    extend(this.prototype, props);\n    if (props.options) {\n      this.prototype.options = parentOptions;\n      this.mergeOptions(props.options);\n    }\n    return this;\n  };\n\n  // @function mergeOptions(options: Object): this\n  // [Merges `options`](#class-options) into the defaults of the class.\n  Class.mergeOptions = function (options) {\n    extend(this.prototype.options, options);\n    return this;\n  };\n\n  // @function addInitHook(fn: Function): this\n  // Adds a [constructor hook](#class-constructor-hooks) to the class.\n  Class.addInitHook = function (fn) {\n    // (Function) || (String, args...)\n    var args = Array.prototype.slice.call(arguments, 1);\n    var init = typeof fn === 'function' ? fn : function () {\n      this[fn].apply(this, args);\n    };\n    this.prototype._initHooks = this.prototype._initHooks || [];\n    this.prototype._initHooks.push(init);\n    return this;\n  };\n  function checkDeprecatedMixinEvents(includes) {\n    /* global L: true */\n    if (typeof L === 'undefined' || !L || !L.Mixin) {\n      return;\n    }\n    includes = isArray(includes) ? includes : [includes];\n    for (var i = 0; i < includes.length; i++) {\n      if (includes[i] === L.Mixin.Events) {\n        console.warn('Deprecated include of L.Mixin.Events: ' + 'this property will be removed in future releases, ' + 'please inherit from L.Evented instead.', new Error().stack);\n      }\n    }\n  }\n\n  /*\r\n   * @class Evented\r\n   * @aka L.Evented\r\n   * @inherits Class\r\n   *\r\n   * A set of methods shared between event-powered classes (like `Map` and `Marker`). Generally, events allow you to execute some function when something happens with an object (e.g. the user clicks on the map, causing the map to fire `'click'` event).\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * map.on('click', function(e) {\r\n   * \talert(e.latlng);\r\n   * } );\r\n   * ```\r\n   *\r\n   * Leaflet deals with event listeners by reference, so if you want to add a listener and then remove it, define it as a function:\r\n   *\r\n   * ```js\r\n   * function onClick(e) { ... }\r\n   *\r\n   * map.on('click', onClick);\r\n   * map.off('click', onClick);\r\n   * ```\r\n   */\n\n  var Events = {\n    /* @method on(type: String, fn: Function, context?: Object): this\r\n     * Adds a listener function (`fn`) to a particular event type of the object. You can optionally specify the context of the listener (object the this keyword will point to). You can also pass several space-separated types (e.g. `'click dblclick'`).\r\n     *\r\n     * @alternative\r\n     * @method on(eventMap: Object): this\r\n     * Adds a set of type/listener pairs, e.g. `{click: onClick, mousemove: onMouseMove}`\r\n     */\n    on: function (types, fn, context) {\n      // types can be a map of types/handlers\n      if (typeof types === 'object') {\n        for (var type in types) {\n          // we don't process space-separated events here for performance;\n          // it's a hot path since Layer uses the on(obj) syntax\n          this._on(type, types[type], fn);\n        }\n      } else {\n        // types can be a string of space-separated words\n        types = splitWords(types);\n        for (var i = 0, len = types.length; i < len; i++) {\n          this._on(types[i], fn, context);\n        }\n      }\n      return this;\n    },\n    /* @method off(type: String, fn?: Function, context?: Object): this\r\n     * Removes a previously added listener function. If no function is specified, it will remove all the listeners of that particular event from the object. Note that if you passed a custom context to `on`, you must pass the same context to `off` in order to remove the listener.\r\n     *\r\n     * @alternative\r\n     * @method off(eventMap: Object): this\r\n     * Removes a set of type/listener pairs.\r\n     *\r\n     * @alternative\r\n     * @method off: this\r\n     * Removes all listeners to all events on the object. This includes implicitly attached events.\r\n     */\n    off: function (types, fn, context) {\n      if (!arguments.length) {\n        // clear all listeners if called without arguments\n        delete this._events;\n      } else if (typeof types === 'object') {\n        for (var type in types) {\n          this._off(type, types[type], fn);\n        }\n      } else {\n        types = splitWords(types);\n        var removeAll = arguments.length === 1;\n        for (var i = 0, len = types.length; i < len; i++) {\n          if (removeAll) {\n            this._off(types[i]);\n          } else {\n            this._off(types[i], fn, context);\n          }\n        }\n      }\n      return this;\n    },\n    // attach listener (without syntactic sugar now)\n    _on: function (type, fn, context, _once) {\n      if (typeof fn !== 'function') {\n        console.warn('wrong listener type: ' + typeof fn);\n        return;\n      }\n\n      // check if fn already there\n      if (this._listens(type, fn, context) !== false) {\n        return;\n      }\n      if (context === this) {\n        // Less memory footprint.\n        context = undefined;\n      }\n      var newListener = {\n        fn: fn,\n        ctx: context\n      };\n      if (_once) {\n        newListener.once = true;\n      }\n      this._events = this._events || {};\n      this._events[type] = this._events[type] || [];\n      this._events[type].push(newListener);\n    },\n    _off: function (type, fn, context) {\n      var listeners, i, len;\n      if (!this._events) {\n        return;\n      }\n      listeners = this._events[type];\n      if (!listeners) {\n        return;\n      }\n      if (arguments.length === 1) {\n        // remove all\n        if (this._firingCount) {\n          // Set all removed listeners to noop\n          // so they are not called if remove happens in fire\n          for (i = 0, len = listeners.length; i < len; i++) {\n            listeners[i].fn = falseFn;\n          }\n        }\n        // clear all listeners for a type if function isn't specified\n        delete this._events[type];\n        return;\n      }\n      if (typeof fn !== 'function') {\n        console.warn('wrong listener type: ' + typeof fn);\n        return;\n      }\n\n      // find fn and remove it\n      var index = this._listens(type, fn, context);\n      if (index !== false) {\n        var listener = listeners[index];\n        if (this._firingCount) {\n          // set the removed listener to noop so that's not called if remove happens in fire\n          listener.fn = falseFn;\n\n          /* copy array in case events are being fired */\n          this._events[type] = listeners = listeners.slice();\n        }\n        listeners.splice(index, 1);\n      }\n    },\n    // @method fire(type: String, data?: Object, propagate?: Boolean): this\n    // Fires an event of the specified type. You can optionally provide a data\n    // object — the first argument of the listener function will contain its\n    // properties. The event can optionally be propagated to event parents.\n    fire: function (type, data, propagate) {\n      if (!this.listens(type, propagate)) {\n        return this;\n      }\n      var event = extend({}, data, {\n        type: type,\n        target: this,\n        sourceTarget: data && data.sourceTarget || this\n      });\n      if (this._events) {\n        var listeners = this._events[type];\n        if (listeners) {\n          this._firingCount = this._firingCount + 1 || 1;\n          for (var i = 0, len = listeners.length; i < len; i++) {\n            var l = listeners[i];\n            // off overwrites l.fn, so we need to copy fn to a var\n            var fn = l.fn;\n            if (l.once) {\n              this.off(type, fn, l.ctx);\n            }\n            fn.call(l.ctx || this, event);\n          }\n          this._firingCount--;\n        }\n      }\n      if (propagate) {\n        // propagate the event to parents (set with addEventParent)\n        this._propagateEvent(event);\n      }\n      return this;\n    },\n    // @method listens(type: String, propagate?: Boolean): Boolean\n    // @method listens(type: String, fn: Function, context?: Object, propagate?: Boolean): Boolean\n    // Returns `true` if a particular event type has any listeners attached to it.\n    // The verification can optionally be propagated, it will return `true` if parents have the listener attached to it.\n    listens: function (type, fn, context, propagate) {\n      if (typeof type !== 'string') {\n        console.warn('\"string\" type argument expected');\n      }\n\n      // we don't overwrite the input `fn` value, because we need to use it for propagation\n      var _fn = fn;\n      if (typeof fn !== 'function') {\n        propagate = !!fn;\n        _fn = undefined;\n        context = undefined;\n      }\n      var listeners = this._events && this._events[type];\n      if (listeners && listeners.length) {\n        if (this._listens(type, _fn, context) !== false) {\n          return true;\n        }\n      }\n      if (propagate) {\n        // also check parents for listeners if event propagates\n        for (var id in this._eventParents) {\n          if (this._eventParents[id].listens(type, fn, context, propagate)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    },\n    // returns the index (number) or false\n    _listens: function (type, fn, context) {\n      if (!this._events) {\n        return false;\n      }\n      var listeners = this._events[type] || [];\n      if (!fn) {\n        return !!listeners.length;\n      }\n      if (context === this) {\n        // Less memory footprint.\n        context = undefined;\n      }\n      for (var i = 0, len = listeners.length; i < len; i++) {\n        if (listeners[i].fn === fn && listeners[i].ctx === context) {\n          return i;\n        }\n      }\n      return false;\n    },\n    // @method once(…): this\n    // Behaves as [`on(…)`](#evented-on), except the listener will only get fired once and then removed.\n    once: function (types, fn, context) {\n      // types can be a map of types/handlers\n      if (typeof types === 'object') {\n        for (var type in types) {\n          // we don't process space-separated events here for performance;\n          // it's a hot path since Layer uses the on(obj) syntax\n          this._on(type, types[type], fn, true);\n        }\n      } else {\n        // types can be a string of space-separated words\n        types = splitWords(types);\n        for (var i = 0, len = types.length; i < len; i++) {\n          this._on(types[i], fn, context, true);\n        }\n      }\n      return this;\n    },\n    // @method addEventParent(obj: Evented): this\n    // Adds an event parent - an `Evented` that will receive propagated events\n    addEventParent: function (obj) {\n      this._eventParents = this._eventParents || {};\n      this._eventParents[stamp(obj)] = obj;\n      return this;\n    },\n    // @method removeEventParent(obj: Evented): this\n    // Removes an event parent, so it will stop receiving propagated events\n    removeEventParent: function (obj) {\n      if (this._eventParents) {\n        delete this._eventParents[stamp(obj)];\n      }\n      return this;\n    },\n    _propagateEvent: function (e) {\n      for (var id in this._eventParents) {\n        this._eventParents[id].fire(e.type, extend({\n          layer: e.target,\n          propagatedFrom: e.target\n        }, e), true);\n      }\n    }\n  };\n\n  // aliases; we should ditch those eventually\n\n  // @method addEventListener(…): this\n  // Alias to [`on(…)`](#evented-on)\n  Events.addEventListener = Events.on;\n\n  // @method removeEventListener(…): this\n  // Alias to [`off(…)`](#evented-off)\n\n  // @method clearAllEventListeners(…): this\n  // Alias to [`off()`](#evented-off)\n  Events.removeEventListener = Events.clearAllEventListeners = Events.off;\n\n  // @method addOneTimeEventListener(…): this\n  // Alias to [`once(…)`](#evented-once)\n  Events.addOneTimeEventListener = Events.once;\n\n  // @method fireEvent(…): this\n  // Alias to [`fire(…)`](#evented-fire)\n  Events.fireEvent = Events.fire;\n\n  // @method hasEventListeners(…): Boolean\n  // Alias to [`listens(…)`](#evented-listens)\n  Events.hasEventListeners = Events.listens;\n  var Evented = Class.extend(Events);\n\n  /*\r\n   * @class Point\r\n   * @aka L.Point\r\n   *\r\n   * Represents a point with `x` and `y` coordinates in pixels.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var point = L.point(200, 300);\r\n   * ```\r\n   *\r\n   * All Leaflet methods and options that accept `Point` objects also accept them in a simple Array form (unless noted otherwise), so these lines are equivalent:\r\n   *\r\n   * ```js\r\n   * map.panBy([200, 300]);\r\n   * map.panBy(L.point(200, 300));\r\n   * ```\r\n   *\r\n   * Note that `Point` does not inherit from Leaflet's `Class` object,\r\n   * which means new classes can't inherit from it, and new methods\r\n   * can't be added to it with the `include` function.\r\n   */\n\n  function Point(x, y, round) {\n    // @property x: Number; The `x` coordinate of the point\n    this.x = round ? Math.round(x) : x;\n    // @property y: Number; The `y` coordinate of the point\n    this.y = round ? Math.round(y) : y;\n  }\n  var trunc = Math.trunc || function (v) {\n    return v > 0 ? Math.floor(v) : Math.ceil(v);\n  };\n  Point.prototype = {\n    // @method clone(): Point\n    // Returns a copy of the current point.\n    clone: function () {\n      return new Point(this.x, this.y);\n    },\n    // @method add(otherPoint: Point): Point\n    // Returns the result of addition of the current and the given points.\n    add: function (point) {\n      // non-destructive, returns a new point\n      return this.clone()._add(toPoint(point));\n    },\n    _add: function (point) {\n      // destructive, used directly for performance in situations where it's safe to modify existing point\n      this.x += point.x;\n      this.y += point.y;\n      return this;\n    },\n    // @method subtract(otherPoint: Point): Point\n    // Returns the result of subtraction of the given point from the current.\n    subtract: function (point) {\n      return this.clone()._subtract(toPoint(point));\n    },\n    _subtract: function (point) {\n      this.x -= point.x;\n      this.y -= point.y;\n      return this;\n    },\n    // @method divideBy(num: Number): Point\n    // Returns the result of division of the current point by the given number.\n    divideBy: function (num) {\n      return this.clone()._divideBy(num);\n    },\n    _divideBy: function (num) {\n      this.x /= num;\n      this.y /= num;\n      return this;\n    },\n    // @method multiplyBy(num: Number): Point\n    // Returns the result of multiplication of the current point by the given number.\n    multiplyBy: function (num) {\n      return this.clone()._multiplyBy(num);\n    },\n    _multiplyBy: function (num) {\n      this.x *= num;\n      this.y *= num;\n      return this;\n    },\n    // @method scaleBy(scale: Point): Point\n    // Multiply each coordinate of the current point by each coordinate of\n    // `scale`. In linear algebra terms, multiply the point by the\n    // [scaling matrix](https://en.wikipedia.org/wiki/Scaling_%28geometry%29#Matrix_representation)\n    // defined by `scale`.\n    scaleBy: function (point) {\n      return new Point(this.x * point.x, this.y * point.y);\n    },\n    // @method unscaleBy(scale: Point): Point\n    // Inverse of `scaleBy`. Divide each coordinate of the current point by\n    // each coordinate of `scale`.\n    unscaleBy: function (point) {\n      return new Point(this.x / point.x, this.y / point.y);\n    },\n    // @method round(): Point\n    // Returns a copy of the current point with rounded coordinates.\n    round: function () {\n      return this.clone()._round();\n    },\n    _round: function () {\n      this.x = Math.round(this.x);\n      this.y = Math.round(this.y);\n      return this;\n    },\n    // @method floor(): Point\n    // Returns a copy of the current point with floored coordinates (rounded down).\n    floor: function () {\n      return this.clone()._floor();\n    },\n    _floor: function () {\n      this.x = Math.floor(this.x);\n      this.y = Math.floor(this.y);\n      return this;\n    },\n    // @method ceil(): Point\n    // Returns a copy of the current point with ceiled coordinates (rounded up).\n    ceil: function () {\n      return this.clone()._ceil();\n    },\n    _ceil: function () {\n      this.x = Math.ceil(this.x);\n      this.y = Math.ceil(this.y);\n      return this;\n    },\n    // @method trunc(): Point\n    // Returns a copy of the current point with truncated coordinates (rounded towards zero).\n    trunc: function () {\n      return this.clone()._trunc();\n    },\n    _trunc: function () {\n      this.x = trunc(this.x);\n      this.y = trunc(this.y);\n      return this;\n    },\n    // @method distanceTo(otherPoint: Point): Number\n    // Returns the cartesian distance between the current and the given points.\n    distanceTo: function (point) {\n      point = toPoint(point);\n      var x = point.x - this.x,\n        y = point.y - this.y;\n      return Math.sqrt(x * x + y * y);\n    },\n    // @method equals(otherPoint: Point): Boolean\n    // Returns `true` if the given point has the same coordinates.\n    equals: function (point) {\n      point = toPoint(point);\n      return point.x === this.x && point.y === this.y;\n    },\n    // @method contains(otherPoint: Point): Boolean\n    // Returns `true` if both coordinates of the given point are less than the corresponding current point coordinates (in absolute values).\n    contains: function (point) {\n      point = toPoint(point);\n      return Math.abs(point.x) <= Math.abs(this.x) && Math.abs(point.y) <= Math.abs(this.y);\n    },\n    // @method toString(): String\n    // Returns a string representation of the point for debugging purposes.\n    toString: function () {\n      return 'Point(' + formatNum(this.x) + ', ' + formatNum(this.y) + ')';\n    }\n  };\n\n  // @factory L.point(x: Number, y: Number, round?: Boolean)\n  // Creates a Point object with the given `x` and `y` coordinates. If optional `round` is set to true, rounds the `x` and `y` values.\n\n  // @alternative\n  // @factory L.point(coords: Number[])\n  // Expects an array of the form `[x, y]` instead.\n\n  // @alternative\n  // @factory L.point(coords: Object)\n  // Expects a plain object of the form `{x: Number, y: Number}` instead.\n  function toPoint(x, y, round) {\n    if (x instanceof Point) {\n      return x;\n    }\n    if (isArray(x)) {\n      return new Point(x[0], x[1]);\n    }\n    if (x === undefined || x === null) {\n      return x;\n    }\n    if (typeof x === 'object' && 'x' in x && 'y' in x) {\n      return new Point(x.x, x.y);\n    }\n    return new Point(x, y, round);\n  }\n\n  /*\r\n   * @class Bounds\r\n   * @aka L.Bounds\r\n   *\r\n   * Represents a rectangular area in pixel coordinates.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var p1 = L.point(10, 10),\r\n   * p2 = L.point(40, 60),\r\n   * bounds = L.bounds(p1, p2);\r\n   * ```\r\n   *\r\n   * All Leaflet methods that accept `Bounds` objects also accept them in a simple Array form (unless noted otherwise), so the bounds example above can be passed like this:\r\n   *\r\n   * ```js\r\n   * otherBounds.intersects([[10, 10], [40, 60]]);\r\n   * ```\r\n   *\r\n   * Note that `Bounds` does not inherit from Leaflet's `Class` object,\r\n   * which means new classes can't inherit from it, and new methods\r\n   * can't be added to it with the `include` function.\r\n   */\n\n  function Bounds(a, b) {\n    if (!a) {\n      return;\n    }\n    var points = b ? [a, b] : a;\n    for (var i = 0, len = points.length; i < len; i++) {\n      this.extend(points[i]);\n    }\n  }\n  Bounds.prototype = {\n    // @method extend(point: Point): this\n    // Extends the bounds to contain the given point.\n\n    // @alternative\n    // @method extend(otherBounds: Bounds): this\n    // Extend the bounds to contain the given bounds\n    extend: function (obj) {\n      var min2, max2;\n      if (!obj) {\n        return this;\n      }\n      if (obj instanceof Point || typeof obj[0] === 'number' || 'x' in obj) {\n        min2 = max2 = toPoint(obj);\n      } else {\n        obj = toBounds(obj);\n        min2 = obj.min;\n        max2 = obj.max;\n        if (!min2 || !max2) {\n          return this;\n        }\n      }\n\n      // @property min: Point\n      // The top left corner of the rectangle.\n      // @property max: Point\n      // The bottom right corner of the rectangle.\n      if (!this.min && !this.max) {\n        this.min = min2.clone();\n        this.max = max2.clone();\n      } else {\n        this.min.x = Math.min(min2.x, this.min.x);\n        this.max.x = Math.max(max2.x, this.max.x);\n        this.min.y = Math.min(min2.y, this.min.y);\n        this.max.y = Math.max(max2.y, this.max.y);\n      }\n      return this;\n    },\n    // @method getCenter(round?: Boolean): Point\n    // Returns the center point of the bounds.\n    getCenter: function (round) {\n      return toPoint((this.min.x + this.max.x) / 2, (this.min.y + this.max.y) / 2, round);\n    },\n    // @method getBottomLeft(): Point\n    // Returns the bottom-left point of the bounds.\n    getBottomLeft: function () {\n      return toPoint(this.min.x, this.max.y);\n    },\n    // @method getTopRight(): Point\n    // Returns the top-right point of the bounds.\n    getTopRight: function () {\n      // -> Point\n      return toPoint(this.max.x, this.min.y);\n    },\n    // @method getTopLeft(): Point\n    // Returns the top-left point of the bounds (i.e. [`this.min`](#bounds-min)).\n    getTopLeft: function () {\n      return this.min; // left, top\n    },\n\n    // @method getBottomRight(): Point\n    // Returns the bottom-right point of the bounds (i.e. [`this.max`](#bounds-max)).\n    getBottomRight: function () {\n      return this.max; // right, bottom\n    },\n\n    // @method getSize(): Point\n    // Returns the size of the given bounds\n    getSize: function () {\n      return this.max.subtract(this.min);\n    },\n    // @method contains(otherBounds: Bounds): Boolean\n    // Returns `true` if the rectangle contains the given one.\n    // @alternative\n    // @method contains(point: Point): Boolean\n    // Returns `true` if the rectangle contains the given point.\n    contains: function (obj) {\n      var min, max;\n      if (typeof obj[0] === 'number' || obj instanceof Point) {\n        obj = toPoint(obj);\n      } else {\n        obj = toBounds(obj);\n      }\n      if (obj instanceof Bounds) {\n        min = obj.min;\n        max = obj.max;\n      } else {\n        min = max = obj;\n      }\n      return min.x >= this.min.x && max.x <= this.max.x && min.y >= this.min.y && max.y <= this.max.y;\n    },\n    // @method intersects(otherBounds: Bounds): Boolean\n    // Returns `true` if the rectangle intersects the given bounds. Two bounds\n    // intersect if they have at least one point in common.\n    intersects: function (bounds) {\n      // (Bounds) -> Boolean\n      bounds = toBounds(bounds);\n      var min = this.min,\n        max = this.max,\n        min2 = bounds.min,\n        max2 = bounds.max,\n        xIntersects = max2.x >= min.x && min2.x <= max.x,\n        yIntersects = max2.y >= min.y && min2.y <= max.y;\n      return xIntersects && yIntersects;\n    },\n    // @method overlaps(otherBounds: Bounds): Boolean\n    // Returns `true` if the rectangle overlaps the given bounds. Two bounds\n    // overlap if their intersection is an area.\n    overlaps: function (bounds) {\n      // (Bounds) -> Boolean\n      bounds = toBounds(bounds);\n      var min = this.min,\n        max = this.max,\n        min2 = bounds.min,\n        max2 = bounds.max,\n        xOverlaps = max2.x > min.x && min2.x < max.x,\n        yOverlaps = max2.y > min.y && min2.y < max.y;\n      return xOverlaps && yOverlaps;\n    },\n    // @method isValid(): Boolean\n    // Returns `true` if the bounds are properly initialized.\n    isValid: function () {\n      return !!(this.min && this.max);\n    },\n    // @method pad(bufferRatio: Number): Bounds\n    // Returns bounds created by extending or retracting the current bounds by a given ratio in each direction.\n    // For example, a ratio of 0.5 extends the bounds by 50% in each direction.\n    // Negative values will retract the bounds.\n    pad: function (bufferRatio) {\n      var min = this.min,\n        max = this.max,\n        heightBuffer = Math.abs(min.x - max.x) * bufferRatio,\n        widthBuffer = Math.abs(min.y - max.y) * bufferRatio;\n      return toBounds(toPoint(min.x - heightBuffer, min.y - widthBuffer), toPoint(max.x + heightBuffer, max.y + widthBuffer));\n    },\n    // @method equals(otherBounds: Bounds): Boolean\n    // Returns `true` if the rectangle is equivalent to the given bounds.\n    equals: function (bounds) {\n      if (!bounds) {\n        return false;\n      }\n      bounds = toBounds(bounds);\n      return this.min.equals(bounds.getTopLeft()) && this.max.equals(bounds.getBottomRight());\n    }\n  };\n\n  // @factory L.bounds(corner1: Point, corner2: Point)\n  // Creates a Bounds object from two corners coordinate pairs.\n  // @alternative\n  // @factory L.bounds(points: Point[])\n  // Creates a Bounds object from the given array of points.\n  function toBounds(a, b) {\n    if (!a || a instanceof Bounds) {\n      return a;\n    }\n    return new Bounds(a, b);\n  }\n\n  /*\r\n   * @class LatLngBounds\r\n   * @aka L.LatLngBounds\r\n   *\r\n   * Represents a rectangular geographical area on a map.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var corner1 = L.latLng(40.712, -74.227),\r\n   * corner2 = L.latLng(40.774, -74.125),\r\n   * bounds = L.latLngBounds(corner1, corner2);\r\n   * ```\r\n   *\r\n   * All Leaflet methods that accept LatLngBounds objects also accept them in a simple Array form (unless noted otherwise), so the bounds example above can be passed like this:\r\n   *\r\n   * ```js\r\n   * map.fitBounds([\r\n   * \t[40.712, -74.227],\r\n   * \t[40.774, -74.125]\r\n   * ]);\r\n   * ```\r\n   *\r\n   * Caution: if the area crosses the antimeridian (often confused with the International Date Line), you must specify corners _outside_ the [-180, 180] degrees longitude range.\r\n   *\r\n   * Note that `LatLngBounds` does not inherit from Leaflet's `Class` object,\r\n   * which means new classes can't inherit from it, and new methods\r\n   * can't be added to it with the `include` function.\r\n   */\n\n  function LatLngBounds(corner1, corner2) {\n    // (LatLng, LatLng) or (LatLng[])\n    if (!corner1) {\n      return;\n    }\n    var latlngs = corner2 ? [corner1, corner2] : corner1;\n    for (var i = 0, len = latlngs.length; i < len; i++) {\n      this.extend(latlngs[i]);\n    }\n  }\n  LatLngBounds.prototype = {\n    // @method extend(latlng: LatLng): this\n    // Extend the bounds to contain the given point\n\n    // @alternative\n    // @method extend(otherBounds: LatLngBounds): this\n    // Extend the bounds to contain the given bounds\n    extend: function (obj) {\n      var sw = this._southWest,\n        ne = this._northEast,\n        sw2,\n        ne2;\n      if (obj instanceof LatLng) {\n        sw2 = obj;\n        ne2 = obj;\n      } else if (obj instanceof LatLngBounds) {\n        sw2 = obj._southWest;\n        ne2 = obj._northEast;\n        if (!sw2 || !ne2) {\n          return this;\n        }\n      } else {\n        return obj ? this.extend(toLatLng(obj) || toLatLngBounds(obj)) : this;\n      }\n      if (!sw && !ne) {\n        this._southWest = new LatLng(sw2.lat, sw2.lng);\n        this._northEast = new LatLng(ne2.lat, ne2.lng);\n      } else {\n        sw.lat = Math.min(sw2.lat, sw.lat);\n        sw.lng = Math.min(sw2.lng, sw.lng);\n        ne.lat = Math.max(ne2.lat, ne.lat);\n        ne.lng = Math.max(ne2.lng, ne.lng);\n      }\n      return this;\n    },\n    // @method pad(bufferRatio: Number): LatLngBounds\n    // Returns bounds created by extending or retracting the current bounds by a given ratio in each direction.\n    // For example, a ratio of 0.5 extends the bounds by 50% in each direction.\n    // Negative values will retract the bounds.\n    pad: function (bufferRatio) {\n      var sw = this._southWest,\n        ne = this._northEast,\n        heightBuffer = Math.abs(sw.lat - ne.lat) * bufferRatio,\n        widthBuffer = Math.abs(sw.lng - ne.lng) * bufferRatio;\n      return new LatLngBounds(new LatLng(sw.lat - heightBuffer, sw.lng - widthBuffer), new LatLng(ne.lat + heightBuffer, ne.lng + widthBuffer));\n    },\n    // @method getCenter(): LatLng\n    // Returns the center point of the bounds.\n    getCenter: function () {\n      return new LatLng((this._southWest.lat + this._northEast.lat) / 2, (this._southWest.lng + this._northEast.lng) / 2);\n    },\n    // @method getSouthWest(): LatLng\n    // Returns the south-west point of the bounds.\n    getSouthWest: function () {\n      return this._southWest;\n    },\n    // @method getNorthEast(): LatLng\n    // Returns the north-east point of the bounds.\n    getNorthEast: function () {\n      return this._northEast;\n    },\n    // @method getNorthWest(): LatLng\n    // Returns the north-west point of the bounds.\n    getNorthWest: function () {\n      return new LatLng(this.getNorth(), this.getWest());\n    },\n    // @method getSouthEast(): LatLng\n    // Returns the south-east point of the bounds.\n    getSouthEast: function () {\n      return new LatLng(this.getSouth(), this.getEast());\n    },\n    // @method getWest(): Number\n    // Returns the west longitude of the bounds\n    getWest: function () {\n      return this._southWest.lng;\n    },\n    // @method getSouth(): Number\n    // Returns the south latitude of the bounds\n    getSouth: function () {\n      return this._southWest.lat;\n    },\n    // @method getEast(): Number\n    // Returns the east longitude of the bounds\n    getEast: function () {\n      return this._northEast.lng;\n    },\n    // @method getNorth(): Number\n    // Returns the north latitude of the bounds\n    getNorth: function () {\n      return this._northEast.lat;\n    },\n    // @method contains(otherBounds: LatLngBounds): Boolean\n    // Returns `true` if the rectangle contains the given one.\n\n    // @alternative\n    // @method contains (latlng: LatLng): Boolean\n    // Returns `true` if the rectangle contains the given point.\n    contains: function (obj) {\n      // (LatLngBounds) or (LatLng) -> Boolean\n      if (typeof obj[0] === 'number' || obj instanceof LatLng || 'lat' in obj) {\n        obj = toLatLng(obj);\n      } else {\n        obj = toLatLngBounds(obj);\n      }\n      var sw = this._southWest,\n        ne = this._northEast,\n        sw2,\n        ne2;\n      if (obj instanceof LatLngBounds) {\n        sw2 = obj.getSouthWest();\n        ne2 = obj.getNorthEast();\n      } else {\n        sw2 = ne2 = obj;\n      }\n      return sw2.lat >= sw.lat && ne2.lat <= ne.lat && sw2.lng >= sw.lng && ne2.lng <= ne.lng;\n    },\n    // @method intersects(otherBounds: LatLngBounds): Boolean\n    // Returns `true` if the rectangle intersects the given bounds. Two bounds intersect if they have at least one point in common.\n    intersects: function (bounds) {\n      bounds = toLatLngBounds(bounds);\n      var sw = this._southWest,\n        ne = this._northEast,\n        sw2 = bounds.getSouthWest(),\n        ne2 = bounds.getNorthEast(),\n        latIntersects = ne2.lat >= sw.lat && sw2.lat <= ne.lat,\n        lngIntersects = ne2.lng >= sw.lng && sw2.lng <= ne.lng;\n      return latIntersects && lngIntersects;\n    },\n    // @method overlaps(otherBounds: LatLngBounds): Boolean\n    // Returns `true` if the rectangle overlaps the given bounds. Two bounds overlap if their intersection is an area.\n    overlaps: function (bounds) {\n      bounds = toLatLngBounds(bounds);\n      var sw = this._southWest,\n        ne = this._northEast,\n        sw2 = bounds.getSouthWest(),\n        ne2 = bounds.getNorthEast(),\n        latOverlaps = ne2.lat > sw.lat && sw2.lat < ne.lat,\n        lngOverlaps = ne2.lng > sw.lng && sw2.lng < ne.lng;\n      return latOverlaps && lngOverlaps;\n    },\n    // @method toBBoxString(): String\n    // Returns a string with bounding box coordinates in a 'southwest_lng,southwest_lat,northeast_lng,northeast_lat' format. Useful for sending requests to web services that return geo data.\n    toBBoxString: function () {\n      return [this.getWest(), this.getSouth(), this.getEast(), this.getNorth()].join(',');\n    },\n    // @method equals(otherBounds: LatLngBounds, maxMargin?: Number): Boolean\n    // Returns `true` if the rectangle is equivalent (within a small margin of error) to the given bounds. The margin of error can be overridden by setting `maxMargin` to a small number.\n    equals: function (bounds, maxMargin) {\n      if (!bounds) {\n        return false;\n      }\n      bounds = toLatLngBounds(bounds);\n      return this._southWest.equals(bounds.getSouthWest(), maxMargin) && this._northEast.equals(bounds.getNorthEast(), maxMargin);\n    },\n    // @method isValid(): Boolean\n    // Returns `true` if the bounds are properly initialized.\n    isValid: function () {\n      return !!(this._southWest && this._northEast);\n    }\n  };\n\n  // TODO International date line?\n\n  // @factory L.latLngBounds(corner1: LatLng, corner2: LatLng)\n  // Creates a `LatLngBounds` object by defining two diagonally opposite corners of the rectangle.\n\n  // @alternative\n  // @factory L.latLngBounds(latlngs: LatLng[])\n  // Creates a `LatLngBounds` object defined by the geographical points it contains. Very useful for zooming the map to fit a particular set of locations with [`fitBounds`](#map-fitbounds).\n  function toLatLngBounds(a, b) {\n    if (a instanceof LatLngBounds) {\n      return a;\n    }\n    return new LatLngBounds(a, b);\n  }\n\n  /* @class LatLng\r\n   * @aka L.LatLng\r\n   *\r\n   * Represents a geographical point with a certain latitude and longitude.\r\n   *\r\n   * @example\r\n   *\r\n   * ```\r\n   * var latlng = L.latLng(50.5, 30.5);\r\n   * ```\r\n   *\r\n   * All Leaflet methods that accept LatLng objects also accept them in a simple Array form and simple object form (unless noted otherwise), so these lines are equivalent:\r\n   *\r\n   * ```\r\n   * map.panTo([50, 30]);\r\n   * map.panTo({lon: 30, lat: 50});\r\n   * map.panTo({lat: 50, lng: 30});\r\n   * map.panTo(L.latLng(50, 30));\r\n   * ```\r\n   *\r\n   * Note that `LatLng` does not inherit from Leaflet's `Class` object,\r\n   * which means new classes can't inherit from it, and new methods\r\n   * can't be added to it with the `include` function.\r\n   */\n\n  function LatLng(lat, lng, alt) {\n    if (isNaN(lat) || isNaN(lng)) {\n      throw new Error('Invalid LatLng object: (' + lat + ', ' + lng + ')');\n    }\n\n    // @property lat: Number\n    // Latitude in degrees\n    this.lat = +lat;\n\n    // @property lng: Number\n    // Longitude in degrees\n    this.lng = +lng;\n\n    // @property alt: Number\n    // Altitude in meters (optional)\n    if (alt !== undefined) {\n      this.alt = +alt;\n    }\n  }\n  LatLng.prototype = {\n    // @method equals(otherLatLng: LatLng, maxMargin?: Number): Boolean\n    // Returns `true` if the given `LatLng` point is at the same position (within a small margin of error). The margin of error can be overridden by setting `maxMargin` to a small number.\n    equals: function (obj, maxMargin) {\n      if (!obj) {\n        return false;\n      }\n      obj = toLatLng(obj);\n      var margin = Math.max(Math.abs(this.lat - obj.lat), Math.abs(this.lng - obj.lng));\n      return margin <= (maxMargin === undefined ? 1.0E-9 : maxMargin);\n    },\n    // @method toString(): String\n    // Returns a string representation of the point (for debugging purposes).\n    toString: function (precision) {\n      return 'LatLng(' + formatNum(this.lat, precision) + ', ' + formatNum(this.lng, precision) + ')';\n    },\n    // @method distanceTo(otherLatLng: LatLng): Number\n    // Returns the distance (in meters) to the given `LatLng` calculated using the [Spherical Law of Cosines](https://en.wikipedia.org/wiki/Spherical_law_of_cosines).\n    distanceTo: function (other) {\n      return Earth.distance(this, toLatLng(other));\n    },\n    // @method wrap(): LatLng\n    // Returns a new `LatLng` object with the longitude wrapped so it's always between -180 and +180 degrees.\n    wrap: function () {\n      return Earth.wrapLatLng(this);\n    },\n    // @method toBounds(sizeInMeters: Number): LatLngBounds\n    // Returns a new `LatLngBounds` object in which each boundary is `sizeInMeters/2` meters apart from the `LatLng`.\n    toBounds: function (sizeInMeters) {\n      var latAccuracy = 180 * sizeInMeters / 40075017,\n        lngAccuracy = latAccuracy / Math.cos(Math.PI / 180 * this.lat);\n      return toLatLngBounds([this.lat - latAccuracy, this.lng - lngAccuracy], [this.lat + latAccuracy, this.lng + lngAccuracy]);\n    },\n    clone: function () {\n      return new LatLng(this.lat, this.lng, this.alt);\n    }\n  };\n\n  // @factory L.latLng(latitude: Number, longitude: Number, altitude?: Number): LatLng\n  // Creates an object representing a geographical point with the given latitude and longitude (and optionally altitude).\n\n  // @alternative\n  // @factory L.latLng(coords: Array): LatLng\n  // Expects an array of the form `[Number, Number]` or `[Number, Number, Number]` instead.\n\n  // @alternative\n  // @factory L.latLng(coords: Object): LatLng\n  // Expects an plain object of the form `{lat: Number, lng: Number}` or `{lat: Number, lng: Number, alt: Number}` instead.\n\n  function toLatLng(a, b, c) {\n    if (a instanceof LatLng) {\n      return a;\n    }\n    if (isArray(a) && typeof a[0] !== 'object') {\n      if (a.length === 3) {\n        return new LatLng(a[0], a[1], a[2]);\n      }\n      if (a.length === 2) {\n        return new LatLng(a[0], a[1]);\n      }\n      return null;\n    }\n    if (a === undefined || a === null) {\n      return a;\n    }\n    if (typeof a === 'object' && 'lat' in a) {\n      return new LatLng(a.lat, 'lng' in a ? a.lng : a.lon, a.alt);\n    }\n    if (b === undefined) {\n      return null;\n    }\n    return new LatLng(a, b, c);\n  }\n\n  /*\r\n   * @namespace CRS\r\n   * @crs L.CRS.Base\r\n   * Object that defines coordinate reference systems for projecting\r\n   * geographical points into pixel (screen) coordinates and back (and to\r\n   * coordinates in other units for [WMS](https://en.wikipedia.org/wiki/Web_Map_Service) services). See\r\n   * [spatial reference system](https://en.wikipedia.org/wiki/Spatial_reference_system).\r\n   *\r\n   * Leaflet defines the most usual CRSs by default. If you want to use a\r\n   * CRS not defined by default, take a look at the\r\n   * [Proj4Leaflet](https://github.com/kartena/Proj4Leaflet) plugin.\r\n   *\r\n   * Note that the CRS instances do not inherit from Leaflet's `Class` object,\r\n   * and can't be instantiated. Also, new classes can't inherit from them,\r\n   * and methods can't be added to them with the `include` function.\r\n   */\n\n  var CRS = {\n    // @method latLngToPoint(latlng: LatLng, zoom: Number): Point\n    // Projects geographical coordinates into pixel coordinates for a given zoom.\n    latLngToPoint: function (latlng, zoom) {\n      var projectedPoint = this.projection.project(latlng),\n        scale = this.scale(zoom);\n      return this.transformation._transform(projectedPoint, scale);\n    },\n    // @method pointToLatLng(point: Point, zoom: Number): LatLng\n    // The inverse of `latLngToPoint`. Projects pixel coordinates on a given\n    // zoom into geographical coordinates.\n    pointToLatLng: function (point, zoom) {\n      var scale = this.scale(zoom),\n        untransformedPoint = this.transformation.untransform(point, scale);\n      return this.projection.unproject(untransformedPoint);\n    },\n    // @method project(latlng: LatLng): Point\n    // Projects geographical coordinates into coordinates in units accepted for\n    // this CRS (e.g. meters for EPSG:3857, for passing it to WMS services).\n    project: function (latlng) {\n      return this.projection.project(latlng);\n    },\n    // @method unproject(point: Point): LatLng\n    // Given a projected coordinate returns the corresponding LatLng.\n    // The inverse of `project`.\n    unproject: function (point) {\n      return this.projection.unproject(point);\n    },\n    // @method scale(zoom: Number): Number\n    // Returns the scale used when transforming projected coordinates into\n    // pixel coordinates for a particular zoom. For example, it returns\n    // `256 * 2^zoom` for Mercator-based CRS.\n    scale: function (zoom) {\n      return 256 * Math.pow(2, zoom);\n    },\n    // @method zoom(scale: Number): Number\n    // Inverse of `scale()`, returns the zoom level corresponding to a scale\n    // factor of `scale`.\n    zoom: function (scale) {\n      return Math.log(scale / 256) / Math.LN2;\n    },\n    // @method getProjectedBounds(zoom: Number): Bounds\n    // Returns the projection's bounds scaled and transformed for the provided `zoom`.\n    getProjectedBounds: function (zoom) {\n      if (this.infinite) {\n        return null;\n      }\n      var b = this.projection.bounds,\n        s = this.scale(zoom),\n        min = this.transformation.transform(b.min, s),\n        max = this.transformation.transform(b.max, s);\n      return new Bounds(min, max);\n    },\n    // @method distance(latlng1: LatLng, latlng2: LatLng): Number\n    // Returns the distance between two geographical coordinates.\n\n    // @property code: String\n    // Standard code name of the CRS passed into WMS services (e.g. `'EPSG:3857'`)\n    //\n    // @property wrapLng: Number[]\n    // An array of two numbers defining whether the longitude (horizontal) coordinate\n    // axis wraps around a given range and how. Defaults to `[-180, 180]` in most\n    // geographical CRSs. If `undefined`, the longitude axis does not wrap around.\n    //\n    // @property wrapLat: Number[]\n    // Like `wrapLng`, but for the latitude (vertical) axis.\n\n    // wrapLng: [min, max],\n    // wrapLat: [min, max],\n\n    // @property infinite: Boolean\n    // If true, the coordinate space will be unbounded (infinite in both axes)\n    infinite: false,\n    // @method wrapLatLng(latlng: LatLng): LatLng\n    // Returns a `LatLng` where lat and lng has been wrapped according to the\n    // CRS's `wrapLat` and `wrapLng` properties, if they are outside the CRS's bounds.\n    wrapLatLng: function (latlng) {\n      var lng = this.wrapLng ? wrapNum(latlng.lng, this.wrapLng, true) : latlng.lng,\n        lat = this.wrapLat ? wrapNum(latlng.lat, this.wrapLat, true) : latlng.lat,\n        alt = latlng.alt;\n      return new LatLng(lat, lng, alt);\n    },\n    // @method wrapLatLngBounds(bounds: LatLngBounds): LatLngBounds\n    // Returns a `LatLngBounds` with the same size as the given one, ensuring\n    // that its center is within the CRS's bounds.\n    // Only accepts actual `L.LatLngBounds` instances, not arrays.\n    wrapLatLngBounds: function (bounds) {\n      var center = bounds.getCenter(),\n        newCenter = this.wrapLatLng(center),\n        latShift = center.lat - newCenter.lat,\n        lngShift = center.lng - newCenter.lng;\n      if (latShift === 0 && lngShift === 0) {\n        return bounds;\n      }\n      var sw = bounds.getSouthWest(),\n        ne = bounds.getNorthEast(),\n        newSw = new LatLng(sw.lat - latShift, sw.lng - lngShift),\n        newNe = new LatLng(ne.lat - latShift, ne.lng - lngShift);\n      return new LatLngBounds(newSw, newNe);\n    }\n  };\n\n  /*\n   * @namespace CRS\n   * @crs L.CRS.Earth\n   *\n   * Serves as the base for CRS that are global such that they cover the earth.\n   * Can only be used as the base for other CRS and cannot be used directly,\n   * since it does not have a `code`, `projection` or `transformation`. `distance()` returns\n   * meters.\n   */\n\n  var Earth = extend({}, CRS, {\n    wrapLng: [-180, 180],\n    // Mean Earth Radius, as recommended for use by\n    // the International Union of Geodesy and Geophysics,\n    // see https://rosettacode.org/wiki/Haversine_formula\n    R: 6371000,\n    // distance between two geographical points using spherical law of cosines approximation\n    distance: function (latlng1, latlng2) {\n      var rad = Math.PI / 180,\n        lat1 = latlng1.lat * rad,\n        lat2 = latlng2.lat * rad,\n        sinDLat = Math.sin((latlng2.lat - latlng1.lat) * rad / 2),\n        sinDLon = Math.sin((latlng2.lng - latlng1.lng) * rad / 2),\n        a = sinDLat * sinDLat + Math.cos(lat1) * Math.cos(lat2) * sinDLon * sinDLon,\n        c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n      return this.R * c;\n    }\n  });\n\n  /*\r\n   * @namespace Projection\r\n   * @projection L.Projection.SphericalMercator\r\n   *\r\n   * Spherical Mercator projection — the most common projection for online maps,\r\n   * used by almost all free and commercial tile providers. Assumes that Earth is\r\n   * a sphere. Used by the `EPSG:3857` CRS.\r\n   */\n\n  var earthRadius = 6378137;\n  var SphericalMercator = {\n    R: earthRadius,\n    MAX_LATITUDE: 85.**********,\n    project: function (latlng) {\n      var d = Math.PI / 180,\n        max = this.MAX_LATITUDE,\n        lat = Math.max(Math.min(max, latlng.lat), -max),\n        sin = Math.sin(lat * d);\n      return new Point(this.R * latlng.lng * d, this.R * Math.log((1 + sin) / (1 - sin)) / 2);\n    },\n    unproject: function (point) {\n      var d = 180 / Math.PI;\n      return new LatLng((2 * Math.atan(Math.exp(point.y / this.R)) - Math.PI / 2) * d, point.x * d / this.R);\n    },\n    bounds: function () {\n      var d = earthRadius * Math.PI;\n      return new Bounds([-d, -d], [d, d]);\n    }()\n  };\n\n  /*\r\n   * @class Transformation\r\n   * @aka L.Transformation\r\n   *\r\n   * Represents an affine transformation: a set of coefficients `a`, `b`, `c`, `d`\r\n   * for transforming a point of a form `(x, y)` into `(a*x + b, c*y + d)` and doing\r\n   * the reverse. Used by Leaflet in its projections code.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var transformation = L.transformation(2, 5, -1, 10),\r\n   * \tp = L.point(1, 2),\r\n   * \tp2 = transformation.transform(p), //  L.point(7, 8)\r\n   * \tp3 = transformation.untransform(p2); //  L.point(1, 2)\r\n   * ```\r\n   */\n\n  // factory new L.Transformation(a: Number, b: Number, c: Number, d: Number)\n  // Creates a `Transformation` object with the given coefficients.\n  function Transformation(a, b, c, d) {\n    if (isArray(a)) {\n      // use array properties\n      this._a = a[0];\n      this._b = a[1];\n      this._c = a[2];\n      this._d = a[3];\n      return;\n    }\n    this._a = a;\n    this._b = b;\n    this._c = c;\n    this._d = d;\n  }\n  Transformation.prototype = {\n    // @method transform(point: Point, scale?: Number): Point\n    // Returns a transformed point, optionally multiplied by the given scale.\n    // Only accepts actual `L.Point` instances, not arrays.\n    transform: function (point, scale) {\n      // (Point, Number) -> Point\n      return this._transform(point.clone(), scale);\n    },\n    // destructive transform (faster)\n    _transform: function (point, scale) {\n      scale = scale || 1;\n      point.x = scale * (this._a * point.x + this._b);\n      point.y = scale * (this._c * point.y + this._d);\n      return point;\n    },\n    // @method untransform(point: Point, scale?: Number): Point\n    // Returns the reverse transformation of the given point, optionally divided\n    // by the given scale. Only accepts actual `L.Point` instances, not arrays.\n    untransform: function (point, scale) {\n      scale = scale || 1;\n      return new Point((point.x / scale - this._b) / this._a, (point.y / scale - this._d) / this._c);\n    }\n  };\n\n  // factory L.transformation(a: Number, b: Number, c: Number, d: Number)\n\n  // @factory L.transformation(a: Number, b: Number, c: Number, d: Number)\n  // Instantiates a Transformation object with the given coefficients.\n\n  // @alternative\n  // @factory L.transformation(coefficients: Array): Transformation\n  // Expects an coefficients array of the form\n  // `[a: Number, b: Number, c: Number, d: Number]`.\n\n  function toTransformation(a, b, c, d) {\n    return new Transformation(a, b, c, d);\n  }\n\n  /*\r\n   * @namespace CRS\r\n   * @crs L.CRS.EPSG3857\r\n   *\r\n   * The most common CRS for online maps, used by almost all free and commercial\r\n   * tile providers. Uses Spherical Mercator projection. Set in by default in\r\n   * Map's `crs` option.\r\n   */\n\n  var EPSG3857 = extend({}, Earth, {\n    code: 'EPSG:3857',\n    projection: SphericalMercator,\n    transformation: function () {\n      var scale = 0.5 / (Math.PI * SphericalMercator.R);\n      return toTransformation(scale, 0.5, -scale, 0.5);\n    }()\n  });\n  var EPSG900913 = extend({}, EPSG3857, {\n    code: 'EPSG:900913'\n  });\n\n  // @namespace SVG; @section\n  // There are several static functions which can be called without instantiating L.SVG:\n\n  // @function create(name: String): SVGElement\n  // Returns a instance of [SVGElement](https://developer.mozilla.org/docs/Web/API/SVGElement),\n  // corresponding to the class name passed. For example, using 'line' will return\n  // an instance of [SVGLineElement](https://developer.mozilla.org/docs/Web/API/SVGLineElement).\n  function svgCreate(name) {\n    return document.createElementNS('http://www.w3.org/2000/svg', name);\n  }\n\n  // @function pointsToPath(rings: Point[], closed: Boolean): String\n  // Generates a SVG path string for multiple rings, with each ring turning\n  // into \"M..L..L..\" instructions\n  function pointsToPath(rings, closed) {\n    var str = '',\n      i,\n      j,\n      len,\n      len2,\n      points,\n      p;\n    for (i = 0, len = rings.length; i < len; i++) {\n      points = rings[i];\n      for (j = 0, len2 = points.length; j < len2; j++) {\n        p = points[j];\n        str += (j ? 'L' : 'M') + p.x + ' ' + p.y;\n      }\n\n      // closes the ring for polygons; \"x\" is VML syntax\n      str += closed ? Browser.svg ? 'z' : 'x' : '';\n    }\n\n    // SVG complains about empty path strings\n    return str || 'M0 0';\n  }\n\n  /*\r\n   * @namespace Browser\r\n   * @aka L.Browser\r\n   *\r\n   * A namespace with static properties for browser/feature detection used by Leaflet internally.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * if (L.Browser.ielt9) {\r\n   *   alert('Upgrade your browser, dude!');\r\n   * }\r\n   * ```\r\n   */\n\n  var style = document.documentElement.style;\n\n  // @property ie: Boolean; `true` for all Internet Explorer versions (not Edge).\n  var ie = ('ActiveXObject' in window);\n\n  // @property ielt9: Boolean; `true` for Internet Explorer versions less than 9.\n  var ielt9 = ie && !document.addEventListener;\n\n  // @property edge: Boolean; `true` for the Edge web browser.\n  var edge = 'msLaunchUri' in navigator && !('documentMode' in document);\n\n  // @property webkit: Boolean;\n  // `true` for webkit-based browsers like Chrome and Safari (including mobile versions).\n  var webkit = userAgentContains('webkit');\n\n  // @property android: Boolean\n  // **Deprecated.** `true` for any browser running on an Android platform.\n  var android = userAgentContains('android');\n\n  // @property android23: Boolean; **Deprecated.** `true` for browsers running on Android 2 or Android 3.\n  var android23 = userAgentContains('android 2') || userAgentContains('android 3');\n\n  /* See https://stackoverflow.com/a/17961266 for details on detecting stock Android */\n  var webkitVer = parseInt(/WebKit\\/([0-9]+)|$/.exec(navigator.userAgent)[1], 10); // also matches AppleWebKit\n  // @property androidStock: Boolean; **Deprecated.** `true` for the Android stock browser (i.e. not Chrome)\n  var androidStock = android && userAgentContains('Google') && webkitVer < 537 && !('AudioNode' in window);\n\n  // @property opera: Boolean; `true` for the Opera browser\n  var opera = !!window.opera;\n\n  // @property chrome: Boolean; `true` for the Chrome browser.\n  var chrome = !edge && userAgentContains('chrome');\n\n  // @property gecko: Boolean; `true` for gecko-based browsers like Firefox.\n  var gecko = userAgentContains('gecko') && !webkit && !opera && !ie;\n\n  // @property safari: Boolean; `true` for the Safari browser.\n  var safari = !chrome && userAgentContains('safari');\n  var phantom = userAgentContains('phantom');\n\n  // @property opera12: Boolean\n  // `true` for the Opera browser supporting CSS transforms (version 12 or later).\n  var opera12 = ('OTransition' in style);\n\n  // @property win: Boolean; `true` when the browser is running in a Windows platform\n  var win = navigator.platform.indexOf('Win') === 0;\n\n  // @property ie3d: Boolean; `true` for all Internet Explorer versions supporting CSS transforms.\n  var ie3d = ie && 'transition' in style;\n\n  // @property webkit3d: Boolean; `true` for webkit-based browsers supporting CSS transforms.\n  var webkit3d = 'WebKitCSSMatrix' in window && 'm11' in new window.WebKitCSSMatrix() && !android23;\n\n  // @property gecko3d: Boolean; `true` for gecko-based browsers supporting CSS transforms.\n  var gecko3d = ('MozPerspective' in style);\n\n  // @property any3d: Boolean\n  // `true` for all browsers supporting CSS transforms.\n  var any3d = !window.L_DISABLE_3D && (ie3d || webkit3d || gecko3d) && !opera12 && !phantom;\n\n  // @property mobile: Boolean; `true` for all browsers running in a mobile device.\n  var mobile = typeof orientation !== 'undefined' || userAgentContains('mobile');\n\n  // @property mobileWebkit: Boolean; `true` for all webkit-based browsers in a mobile device.\n  var mobileWebkit = mobile && webkit;\n\n  // @property mobileWebkit3d: Boolean\n  // `true` for all webkit-based browsers in a mobile device supporting CSS transforms.\n  var mobileWebkit3d = mobile && webkit3d;\n\n  // @property msPointer: Boolean\n  // `true` for browsers implementing the Microsoft touch events model (notably IE10).\n  var msPointer = !window.PointerEvent && window.MSPointerEvent;\n\n  // @property pointer: Boolean\n  // `true` for all browsers supporting [pointer events](https://msdn.microsoft.com/en-us/library/dn433244%28v=vs.85%29.aspx).\n  var pointer = !!(window.PointerEvent || msPointer);\n\n  // @property touchNative: Boolean\n  // `true` for all browsers supporting [touch events](https://developer.mozilla.org/docs/Web/API/Touch_events).\n  // **This does not necessarily mean** that the browser is running in a computer with\n  // a touchscreen, it only means that the browser is capable of understanding\n  // touch events.\n  var touchNative = 'ontouchstart' in window || !!window.TouchEvent;\n\n  // @property touch: Boolean\n  // `true` for all browsers supporting either [touch](#browser-touch) or [pointer](#browser-pointer) events.\n  // Note: pointer events will be preferred (if available), and processed for all `touch*` listeners.\n  var touch = !window.L_NO_TOUCH && (touchNative || pointer);\n\n  // @property mobileOpera: Boolean; `true` for the Opera browser in a mobile device.\n  var mobileOpera = mobile && opera;\n\n  // @property mobileGecko: Boolean\n  // `true` for gecko-based browsers running in a mobile device.\n  var mobileGecko = mobile && gecko;\n\n  // @property retina: Boolean\n  // `true` for browsers on a high-resolution \"retina\" screen or on any screen when browser's display zoom is more than 100%.\n  var retina = (window.devicePixelRatio || window.screen.deviceXDPI / window.screen.logicalXDPI) > 1;\n\n  // @property passiveEvents: Boolean\n  // `true` for browsers that support passive events.\n  var passiveEvents = function () {\n    var supportsPassiveOption = false;\n    try {\n      var opts = Object.defineProperty({}, 'passive', {\n        get: function () {\n          // eslint-disable-line getter-return\n          supportsPassiveOption = true;\n        }\n      });\n      window.addEventListener('testPassiveEventSupport', falseFn, opts);\n      window.removeEventListener('testPassiveEventSupport', falseFn, opts);\n    } catch (e) {\n      // Errors can safely be ignored since this is only a browser support test.\n    }\n    return supportsPassiveOption;\n  }();\n\n  // @property canvas: Boolean\n  // `true` when the browser supports [`<canvas>`](https://developer.mozilla.org/docs/Web/API/Canvas_API).\n  var canvas$1 = function () {\n    return !!document.createElement('canvas').getContext;\n  }();\n\n  // @property svg: Boolean\n  // `true` when the browser supports [SVG](https://developer.mozilla.org/docs/Web/SVG).\n  var svg$1 = !!(document.createElementNS && svgCreate('svg').createSVGRect);\n  var inlineSvg = !!svg$1 && function () {\n    var div = document.createElement('div');\n    div.innerHTML = '<svg/>';\n    return (div.firstChild && div.firstChild.namespaceURI) === 'http://www.w3.org/2000/svg';\n  }();\n\n  // @property vml: Boolean\n  // `true` if the browser supports [VML](https://en.wikipedia.org/wiki/Vector_Markup_Language).\n  var vml = !svg$1 && function () {\n    try {\n      var div = document.createElement('div');\n      div.innerHTML = '<v:shape adj=\"1\"/>';\n      var shape = div.firstChild;\n      shape.style.behavior = 'url(#default#VML)';\n      return shape && typeof shape.adj === 'object';\n    } catch (e) {\n      return false;\n    }\n  }();\n\n  // @property mac: Boolean; `true` when the browser is running in a Mac platform\n  var mac = navigator.platform.indexOf('Mac') === 0;\n\n  // @property mac: Boolean; `true` when the browser is running in a Linux platform\n  var linux = navigator.platform.indexOf('Linux') === 0;\n  function userAgentContains(str) {\n    return navigator.userAgent.toLowerCase().indexOf(str) >= 0;\n  }\n  var Browser = {\n    ie: ie,\n    ielt9: ielt9,\n    edge: edge,\n    webkit: webkit,\n    android: android,\n    android23: android23,\n    androidStock: androidStock,\n    opera: opera,\n    chrome: chrome,\n    gecko: gecko,\n    safari: safari,\n    phantom: phantom,\n    opera12: opera12,\n    win: win,\n    ie3d: ie3d,\n    webkit3d: webkit3d,\n    gecko3d: gecko3d,\n    any3d: any3d,\n    mobile: mobile,\n    mobileWebkit: mobileWebkit,\n    mobileWebkit3d: mobileWebkit3d,\n    msPointer: msPointer,\n    pointer: pointer,\n    touch: touch,\n    touchNative: touchNative,\n    mobileOpera: mobileOpera,\n    mobileGecko: mobileGecko,\n    retina: retina,\n    passiveEvents: passiveEvents,\n    canvas: canvas$1,\n    svg: svg$1,\n    vml: vml,\n    inlineSvg: inlineSvg,\n    mac: mac,\n    linux: linux\n  };\n\n  /*\n   * Extends L.DomEvent to provide touch support for Internet Explorer and Windows-based devices.\n   */\n\n  var POINTER_DOWN = Browser.msPointer ? 'MSPointerDown' : 'pointerdown';\n  var POINTER_MOVE = Browser.msPointer ? 'MSPointerMove' : 'pointermove';\n  var POINTER_UP = Browser.msPointer ? 'MSPointerUp' : 'pointerup';\n  var POINTER_CANCEL = Browser.msPointer ? 'MSPointerCancel' : 'pointercancel';\n  var pEvent = {\n    touchstart: POINTER_DOWN,\n    touchmove: POINTER_MOVE,\n    touchend: POINTER_UP,\n    touchcancel: POINTER_CANCEL\n  };\n  var handle = {\n    touchstart: _onPointerStart,\n    touchmove: _handlePointer,\n    touchend: _handlePointer,\n    touchcancel: _handlePointer\n  };\n  var _pointers = {};\n  var _pointerDocListener = false;\n\n  // Provides a touch events wrapper for (ms)pointer events.\n  // ref https://www.w3.org/TR/pointerevents/ https://www.w3.org/Bugs/Public/show_bug.cgi?id=22890\n\n  function addPointerListener(obj, type, handler) {\n    if (type === 'touchstart') {\n      _addPointerDocListener();\n    }\n    if (!handle[type]) {\n      console.warn('wrong event specified:', type);\n      return falseFn;\n    }\n    handler = handle[type].bind(this, handler);\n    obj.addEventListener(pEvent[type], handler, false);\n    return handler;\n  }\n  function removePointerListener(obj, type, handler) {\n    if (!pEvent[type]) {\n      console.warn('wrong event specified:', type);\n      return;\n    }\n    obj.removeEventListener(pEvent[type], handler, false);\n  }\n  function _globalPointerDown(e) {\n    _pointers[e.pointerId] = e;\n  }\n  function _globalPointerMove(e) {\n    if (_pointers[e.pointerId]) {\n      _pointers[e.pointerId] = e;\n    }\n  }\n  function _globalPointerUp(e) {\n    delete _pointers[e.pointerId];\n  }\n  function _addPointerDocListener() {\n    // need to keep track of what pointers and how many are active to provide e.touches emulation\n    if (!_pointerDocListener) {\n      // we listen document as any drags that end by moving the touch off the screen get fired there\n      document.addEventListener(POINTER_DOWN, _globalPointerDown, true);\n      document.addEventListener(POINTER_MOVE, _globalPointerMove, true);\n      document.addEventListener(POINTER_UP, _globalPointerUp, true);\n      document.addEventListener(POINTER_CANCEL, _globalPointerUp, true);\n      _pointerDocListener = true;\n    }\n  }\n  function _handlePointer(handler, e) {\n    if (e.pointerType === (e.MSPOINTER_TYPE_MOUSE || 'mouse')) {\n      return;\n    }\n    e.touches = [];\n    for (var i in _pointers) {\n      e.touches.push(_pointers[i]);\n    }\n    e.changedTouches = [e];\n    handler(e);\n  }\n  function _onPointerStart(handler, e) {\n    // IE10 specific: MsTouch needs preventDefault. See #2000\n    if (e.MSPOINTER_TYPE_TOUCH && e.pointerType === e.MSPOINTER_TYPE_TOUCH) {\n      preventDefault(e);\n    }\n    _handlePointer(handler, e);\n  }\n\n  /*\r\n   * Extends the event handling code with double tap support for mobile browsers.\r\n   *\r\n   * Note: currently most browsers fire native dblclick, with only a few exceptions\r\n   * (see https://github.com/Leaflet/Leaflet/issues/7012#issuecomment-595087386)\r\n   */\n\n  function makeDblclick(event) {\n    // in modern browsers `type` cannot be just overridden:\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only\n    var newEvent = {},\n      prop,\n      i;\n    for (i in event) {\n      prop = event[i];\n      newEvent[i] = prop && prop.bind ? prop.bind(event) : prop;\n    }\n    event = newEvent;\n    newEvent.type = 'dblclick';\n    newEvent.detail = 2;\n    newEvent.isTrusted = false;\n    newEvent._simulated = true; // for debug purposes\n    return newEvent;\n  }\n  var delay = 200;\n  function addDoubleTapListener(obj, handler) {\n    // Most browsers handle double tap natively\n    obj.addEventListener('dblclick', handler);\n\n    // On some platforms the browser doesn't fire native dblclicks for touch events.\n    // It seems that in all such cases `detail` property of `click` event is always `1`.\n    // So here we rely on that fact to avoid excessive 'dblclick' simulation when not needed.\n    var last = 0,\n      detail;\n    function simDblclick(e) {\n      if (e.detail !== 1) {\n        detail = e.detail; // keep in sync to avoid false dblclick in some cases\n        return;\n      }\n      if (e.pointerType === 'mouse' || e.sourceCapabilities && !e.sourceCapabilities.firesTouchEvents) {\n        return;\n      }\n\n      // When clicking on an <input>, the browser generates a click on its\n      // <label> (and vice versa) triggering two clicks in quick succession.\n      // This ignores clicks on elements which are a label with a 'for'\n      // attribute (or children of such a label), but not children of\n      // a <input>.\n      var path = getPropagationPath(e);\n      if (path.some(function (el) {\n        return el instanceof HTMLLabelElement && el.attributes.for;\n      }) && !path.some(function (el) {\n        return el instanceof HTMLInputElement || el instanceof HTMLSelectElement;\n      })) {\n        return;\n      }\n      var now = Date.now();\n      if (now - last <= delay) {\n        detail++;\n        if (detail === 2) {\n          handler(makeDblclick(e));\n        }\n      } else {\n        detail = 1;\n      }\n      last = now;\n    }\n    obj.addEventListener('click', simDblclick);\n    return {\n      dblclick: handler,\n      simDblclick: simDblclick\n    };\n  }\n  function removeDoubleTapListener(obj, handlers) {\n    obj.removeEventListener('dblclick', handlers.dblclick);\n    obj.removeEventListener('click', handlers.simDblclick);\n  }\n\n  /*\r\n   * @namespace DomUtil\r\n   *\r\n   * Utility functions to work with the [DOM](https://developer.mozilla.org/docs/Web/API/Document_Object_Model)\r\n   * tree, used by Leaflet internally.\r\n   *\r\n   * Most functions expecting or returning a `HTMLElement` also work for\r\n   * SVG elements. The only difference is that classes refer to CSS classes\r\n   * in HTML and SVG classes in SVG.\r\n   */\n\n  // @property TRANSFORM: String\n  // Vendor-prefixed transform style name (e.g. `'webkitTransform'` for WebKit).\n  var TRANSFORM = testProp(['transform', 'webkitTransform', 'OTransform', 'MozTransform', 'msTransform']);\n\n  // webkitTransition comes first because some browser versions that drop vendor prefix don't do\n  // the same for the transitionend event, in particular the Android 4.1 stock browser\n\n  // @property TRANSITION: String\n  // Vendor-prefixed transition style name.\n  var TRANSITION = testProp(['webkitTransition', 'transition', 'OTransition', 'MozTransition', 'msTransition']);\n\n  // @property TRANSITION_END: String\n  // Vendor-prefixed transitionend event name.\n  var TRANSITION_END = TRANSITION === 'webkitTransition' || TRANSITION === 'OTransition' ? TRANSITION + 'End' : 'transitionend';\n\n  // @function get(id: String|HTMLElement): HTMLElement\n  // Returns an element given its DOM id, or returns the element itself\n  // if it was passed directly.\n  function get(id) {\n    return typeof id === 'string' ? document.getElementById(id) : id;\n  }\n\n  // @function getStyle(el: HTMLElement, styleAttrib: String): String\n  // Returns the value for a certain style attribute on an element,\n  // including computed values or values set through CSS.\n  function getStyle(el, style) {\n    var value = el.style[style] || el.currentStyle && el.currentStyle[style];\n    if ((!value || value === 'auto') && document.defaultView) {\n      var css = document.defaultView.getComputedStyle(el, null);\n      value = css ? css[style] : null;\n    }\n    return value === 'auto' ? null : value;\n  }\n\n  // @function create(tagName: String, className?: String, container?: HTMLElement): HTMLElement\n  // Creates an HTML element with `tagName`, sets its class to `className`, and optionally appends it to `container` element.\n  function create$1(tagName, className, container) {\n    var el = document.createElement(tagName);\n    el.className = className || '';\n    if (container) {\n      container.appendChild(el);\n    }\n    return el;\n  }\n\n  // @function remove(el: HTMLElement)\n  // Removes `el` from its parent element\n  function remove(el) {\n    var parent = el.parentNode;\n    if (parent) {\n      parent.removeChild(el);\n    }\n  }\n\n  // @function empty(el: HTMLElement)\n  // Removes all of `el`'s children elements from `el`\n  function empty(el) {\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n  }\n\n  // @function toFront(el: HTMLElement)\n  // Makes `el` the last child of its parent, so it renders in front of the other children.\n  function toFront(el) {\n    var parent = el.parentNode;\n    if (parent && parent.lastChild !== el) {\n      parent.appendChild(el);\n    }\n  }\n\n  // @function toBack(el: HTMLElement)\n  // Makes `el` the first child of its parent, so it renders behind the other children.\n  function toBack(el) {\n    var parent = el.parentNode;\n    if (parent && parent.firstChild !== el) {\n      parent.insertBefore(el, parent.firstChild);\n    }\n  }\n\n  // @function hasClass(el: HTMLElement, name: String): Boolean\n  // Returns `true` if the element's class attribute contains `name`.\n  function hasClass(el, name) {\n    if (el.classList !== undefined) {\n      return el.classList.contains(name);\n    }\n    var className = getClass(el);\n    return className.length > 0 && new RegExp('(^|\\\\s)' + name + '(\\\\s|$)').test(className);\n  }\n\n  // @function addClass(el: HTMLElement, name: String)\n  // Adds `name` to the element's class attribute.\n  function addClass(el, name) {\n    if (el.classList !== undefined) {\n      var classes = splitWords(name);\n      for (var i = 0, len = classes.length; i < len; i++) {\n        el.classList.add(classes[i]);\n      }\n    } else if (!hasClass(el, name)) {\n      var className = getClass(el);\n      setClass(el, (className ? className + ' ' : '') + name);\n    }\n  }\n\n  // @function removeClass(el: HTMLElement, name: String)\n  // Removes `name` from the element's class attribute.\n  function removeClass(el, name) {\n    if (el.classList !== undefined) {\n      el.classList.remove(name);\n    } else {\n      setClass(el, trim((' ' + getClass(el) + ' ').replace(' ' + name + ' ', ' ')));\n    }\n  }\n\n  // @function setClass(el: HTMLElement, name: String)\n  // Sets the element's class.\n  function setClass(el, name) {\n    if (el.className.baseVal === undefined) {\n      el.className = name;\n    } else {\n      // in case of SVG element\n      el.className.baseVal = name;\n    }\n  }\n\n  // @function getClass(el: HTMLElement): String\n  // Returns the element's class.\n  function getClass(el) {\n    // Check if the element is an SVGElementInstance and use the correspondingElement instead\n    // (Required for linked SVG elements in IE11.)\n    if (el.correspondingElement) {\n      el = el.correspondingElement;\n    }\n    return el.className.baseVal === undefined ? el.className : el.className.baseVal;\n  }\n\n  // @function setOpacity(el: HTMLElement, opacity: Number)\n  // Set the opacity of an element (including old IE support).\n  // `opacity` must be a number from `0` to `1`.\n  function setOpacity(el, value) {\n    if ('opacity' in el.style) {\n      el.style.opacity = value;\n    } else if ('filter' in el.style) {\n      _setOpacityIE(el, value);\n    }\n  }\n  function _setOpacityIE(el, value) {\n    var filter = false,\n      filterName = 'DXImageTransform.Microsoft.Alpha';\n\n    // filters collection throws an error if we try to retrieve a filter that doesn't exist\n    try {\n      filter = el.filters.item(filterName);\n    } catch (e) {\n      // don't set opacity to 1 if we haven't already set an opacity,\n      // it isn't needed and breaks transparent pngs.\n      if (value === 1) {\n        return;\n      }\n    }\n    value = Math.round(value * 100);\n    if (filter) {\n      filter.Enabled = value !== 100;\n      filter.Opacity = value;\n    } else {\n      el.style.filter += ' progid:' + filterName + '(opacity=' + value + ')';\n    }\n  }\n\n  // @function testProp(props: String[]): String|false\n  // Goes through the array of style names and returns the first name\n  // that is a valid style name for an element. If no such name is found,\n  // it returns false. Useful for vendor-prefixed styles like `transform`.\n  function testProp(props) {\n    var style = document.documentElement.style;\n    for (var i = 0; i < props.length; i++) {\n      if (props[i] in style) {\n        return props[i];\n      }\n    }\n    return false;\n  }\n\n  // @function setTransform(el: HTMLElement, offset: Point, scale?: Number)\n  // Resets the 3D CSS transform of `el` so it is translated by `offset` pixels\n  // and optionally scaled by `scale`. Does not have an effect if the\n  // browser doesn't support 3D CSS transforms.\n  function setTransform(el, offset, scale) {\n    var pos = offset || new Point(0, 0);\n    el.style[TRANSFORM] = (Browser.ie3d ? 'translate(' + pos.x + 'px,' + pos.y + 'px)' : 'translate3d(' + pos.x + 'px,' + pos.y + 'px,0)') + (scale ? ' scale(' + scale + ')' : '');\n  }\n\n  // @function setPosition(el: HTMLElement, position: Point)\n  // Sets the position of `el` to coordinates specified by `position`,\n  // using CSS translate or top/left positioning depending on the browser\n  // (used by Leaflet internally to position its layers).\n  function setPosition(el, point) {\n    /*eslint-disable */\n    el._leaflet_pos = point;\n    /* eslint-enable */\n\n    if (Browser.any3d) {\n      setTransform(el, point);\n    } else {\n      el.style.left = point.x + 'px';\n      el.style.top = point.y + 'px';\n    }\n  }\n\n  // @function getPosition(el: HTMLElement): Point\n  // Returns the coordinates of an element previously positioned with setPosition.\n  function getPosition(el) {\n    // this method is only used for elements previously positioned using setPosition,\n    // so it's safe to cache the position for performance\n\n    return el._leaflet_pos || new Point(0, 0);\n  }\n\n  // @function disableTextSelection()\n  // Prevents the user from generating `selectstart` DOM events, usually generated\n  // when the user drags the mouse through a page with text. Used internally\n  // by Leaflet to override the behaviour of any click-and-drag interaction on\n  // the map. Affects drag interactions on the whole document.\n\n  // @function enableTextSelection()\n  // Cancels the effects of a previous [`L.DomUtil.disableTextSelection`](#domutil-disabletextselection).\n  var disableTextSelection;\n  var enableTextSelection;\n  var _userSelect;\n  if ('onselectstart' in document) {\n    disableTextSelection = function () {\n      on(window, 'selectstart', preventDefault);\n    };\n    enableTextSelection = function () {\n      off(window, 'selectstart', preventDefault);\n    };\n  } else {\n    var userSelectProperty = testProp(['userSelect', 'WebkitUserSelect', 'OUserSelect', 'MozUserSelect', 'msUserSelect']);\n    disableTextSelection = function () {\n      if (userSelectProperty) {\n        var style = document.documentElement.style;\n        _userSelect = style[userSelectProperty];\n        style[userSelectProperty] = 'none';\n      }\n    };\n    enableTextSelection = function () {\n      if (userSelectProperty) {\n        document.documentElement.style[userSelectProperty] = _userSelect;\n        _userSelect = undefined;\n      }\n    };\n  }\n\n  // @function disableImageDrag()\n  // As [`L.DomUtil.disableTextSelection`](#domutil-disabletextselection), but\n  // for `dragstart` DOM events, usually generated when the user drags an image.\n  function disableImageDrag() {\n    on(window, 'dragstart', preventDefault);\n  }\n\n  // @function enableImageDrag()\n  // Cancels the effects of a previous [`L.DomUtil.disableImageDrag`](#domutil-disabletextselection).\n  function enableImageDrag() {\n    off(window, 'dragstart', preventDefault);\n  }\n  var _outlineElement, _outlineStyle;\n  // @function preventOutline(el: HTMLElement)\n  // Makes the [outline](https://developer.mozilla.org/docs/Web/CSS/outline)\n  // of the element `el` invisible. Used internally by Leaflet to prevent\n  // focusable elements from displaying an outline when the user performs a\n  // drag interaction on them.\n  function preventOutline(element) {\n    while (element.tabIndex === -1) {\n      element = element.parentNode;\n    }\n    if (!element.style) {\n      return;\n    }\n    restoreOutline();\n    _outlineElement = element;\n    _outlineStyle = element.style.outlineStyle;\n    element.style.outlineStyle = 'none';\n    on(window, 'keydown', restoreOutline);\n  }\n\n  // @function restoreOutline()\n  // Cancels the effects of a previous [`L.DomUtil.preventOutline`]().\n  function restoreOutline() {\n    if (!_outlineElement) {\n      return;\n    }\n    _outlineElement.style.outlineStyle = _outlineStyle;\n    _outlineElement = undefined;\n    _outlineStyle = undefined;\n    off(window, 'keydown', restoreOutline);\n  }\n\n  // @function getSizedParentNode(el: HTMLElement): HTMLElement\n  // Finds the closest parent node which size (width and height) is not null.\n  function getSizedParentNode(element) {\n    do {\n      element = element.parentNode;\n    } while ((!element.offsetWidth || !element.offsetHeight) && element !== document.body);\n    return element;\n  }\n\n  // @function getScale(el: HTMLElement): Object\n  // Computes the CSS scale currently applied on the element.\n  // Returns an object with `x` and `y` members as horizontal and vertical scales respectively,\n  // and `boundingClientRect` as the result of [`getBoundingClientRect()`](https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect).\n  function getScale(element) {\n    var rect = element.getBoundingClientRect(); // Read-only in old browsers.\n\n    return {\n      x: rect.width / element.offsetWidth || 1,\n      y: rect.height / element.offsetHeight || 1,\n      boundingClientRect: rect\n    };\n  }\n  var DomUtil = {\n    __proto__: null,\n    TRANSFORM: TRANSFORM,\n    TRANSITION: TRANSITION,\n    TRANSITION_END: TRANSITION_END,\n    get: get,\n    getStyle: getStyle,\n    create: create$1,\n    remove: remove,\n    empty: empty,\n    toFront: toFront,\n    toBack: toBack,\n    hasClass: hasClass,\n    addClass: addClass,\n    removeClass: removeClass,\n    setClass: setClass,\n    getClass: getClass,\n    setOpacity: setOpacity,\n    testProp: testProp,\n    setTransform: setTransform,\n    setPosition: setPosition,\n    getPosition: getPosition,\n    get disableTextSelection() {\n      return disableTextSelection;\n    },\n    get enableTextSelection() {\n      return enableTextSelection;\n    },\n    disableImageDrag: disableImageDrag,\n    enableImageDrag: enableImageDrag,\n    preventOutline: preventOutline,\n    restoreOutline: restoreOutline,\n    getSizedParentNode: getSizedParentNode,\n    getScale: getScale\n  };\n\n  /*\r\n   * @namespace DomEvent\r\n   * Utility functions to work with the [DOM events](https://developer.mozilla.org/docs/Web/API/Event), used by Leaflet internally.\r\n   */\n\n  // Inspired by John Resig, Dean Edwards and YUI addEvent implementations.\n\n  // @function on(el: HTMLElement, types: String, fn: Function, context?: Object): this\n  // Adds a listener function (`fn`) to a particular DOM event type of the\n  // element `el`. You can optionally specify the context of the listener\n  // (object the `this` keyword will point to). You can also pass several\n  // space-separated types (e.g. `'click dblclick'`).\n\n  // @alternative\n  // @function on(el: HTMLElement, eventMap: Object, context?: Object): this\n  // Adds a set of type/listener pairs, e.g. `{click: onClick, mousemove: onMouseMove}`\n  function on(obj, types, fn, context) {\n    if (types && typeof types === 'object') {\n      for (var type in types) {\n        addOne(obj, type, types[type], fn);\n      }\n    } else {\n      types = splitWords(types);\n      for (var i = 0, len = types.length; i < len; i++) {\n        addOne(obj, types[i], fn, context);\n      }\n    }\n    return this;\n  }\n  var eventsKey = '_leaflet_events';\n\n  // @function off(el: HTMLElement, types: String, fn: Function, context?: Object): this\n  // Removes a previously added listener function.\n  // Note that if you passed a custom context to on, you must pass the same\n  // context to `off` in order to remove the listener.\n\n  // @alternative\n  // @function off(el: HTMLElement, eventMap: Object, context?: Object): this\n  // Removes a set of type/listener pairs, e.g. `{click: onClick, mousemove: onMouseMove}`\n\n  // @alternative\n  // @function off(el: HTMLElement, types: String): this\n  // Removes all previously added listeners of given types.\n\n  // @alternative\n  // @function off(el: HTMLElement): this\n  // Removes all previously added listeners from given HTMLElement\n  function off(obj, types, fn, context) {\n    if (arguments.length === 1) {\n      batchRemove(obj);\n      delete obj[eventsKey];\n    } else if (types && typeof types === 'object') {\n      for (var type in types) {\n        removeOne(obj, type, types[type], fn);\n      }\n    } else {\n      types = splitWords(types);\n      if (arguments.length === 2) {\n        batchRemove(obj, function (type) {\n          return indexOf(types, type) !== -1;\n        });\n      } else {\n        for (var i = 0, len = types.length; i < len; i++) {\n          removeOne(obj, types[i], fn, context);\n        }\n      }\n    }\n    return this;\n  }\n  function batchRemove(obj, filterFn) {\n    for (var id in obj[eventsKey]) {\n      var type = id.split(/\\d/)[0];\n      if (!filterFn || filterFn(type)) {\n        removeOne(obj, type, null, null, id);\n      }\n    }\n  }\n  var mouseSubst = {\n    mouseenter: 'mouseover',\n    mouseleave: 'mouseout',\n    wheel: !('onwheel' in window) && 'mousewheel'\n  };\n  function addOne(obj, type, fn, context) {\n    var id = type + stamp(fn) + (context ? '_' + stamp(context) : '');\n    if (obj[eventsKey] && obj[eventsKey][id]) {\n      return this;\n    }\n    var handler = function (e) {\n      return fn.call(context || obj, e || window.event);\n    };\n    var originalHandler = handler;\n    if (!Browser.touchNative && Browser.pointer && type.indexOf('touch') === 0) {\n      // Needs DomEvent.Pointer.js\n      handler = addPointerListener(obj, type, handler);\n    } else if (Browser.touch && type === 'dblclick') {\n      handler = addDoubleTapListener(obj, handler);\n    } else if ('addEventListener' in obj) {\n      if (type === 'touchstart' || type === 'touchmove' || type === 'wheel' || type === 'mousewheel') {\n        obj.addEventListener(mouseSubst[type] || type, handler, Browser.passiveEvents ? {\n          passive: false\n        } : false);\n      } else if (type === 'mouseenter' || type === 'mouseleave') {\n        handler = function (e) {\n          e = e || window.event;\n          if (isExternalTarget(obj, e)) {\n            originalHandler(e);\n          }\n        };\n        obj.addEventListener(mouseSubst[type], handler, false);\n      } else {\n        obj.addEventListener(type, originalHandler, false);\n      }\n    } else {\n      obj.attachEvent('on' + type, handler);\n    }\n    obj[eventsKey] = obj[eventsKey] || {};\n    obj[eventsKey][id] = handler;\n  }\n  function removeOne(obj, type, fn, context, id) {\n    id = id || type + stamp(fn) + (context ? '_' + stamp(context) : '');\n    var handler = obj[eventsKey] && obj[eventsKey][id];\n    if (!handler) {\n      return this;\n    }\n    if (!Browser.touchNative && Browser.pointer && type.indexOf('touch') === 0) {\n      removePointerListener(obj, type, handler);\n    } else if (Browser.touch && type === 'dblclick') {\n      removeDoubleTapListener(obj, handler);\n    } else if ('removeEventListener' in obj) {\n      obj.removeEventListener(mouseSubst[type] || type, handler, false);\n    } else {\n      obj.detachEvent('on' + type, handler);\n    }\n    obj[eventsKey][id] = null;\n  }\n\n  // @function stopPropagation(ev: DOMEvent): this\n  // Stop the given event from propagation to parent elements. Used inside the listener functions:\n  // ```js\n  // L.DomEvent.on(div, 'click', function (ev) {\n  // \tL.DomEvent.stopPropagation(ev);\n  // });\n  // ```\n  function stopPropagation(e) {\n    if (e.stopPropagation) {\n      e.stopPropagation();\n    } else if (e.originalEvent) {\n      // In case of Leaflet event.\n      e.originalEvent._stopped = true;\n    } else {\n      e.cancelBubble = true;\n    }\n    return this;\n  }\n\n  // @function disableScrollPropagation(el: HTMLElement): this\n  // Adds `stopPropagation` to the element's `'wheel'` events (plus browser variants).\n  function disableScrollPropagation(el) {\n    addOne(el, 'wheel', stopPropagation);\n    return this;\n  }\n\n  // @function disableClickPropagation(el: HTMLElement): this\n  // Adds `stopPropagation` to the element's `'click'`, `'dblclick'`, `'contextmenu'`,\n  // `'mousedown'` and `'touchstart'` events (plus browser variants).\n  function disableClickPropagation(el) {\n    on(el, 'mousedown touchstart dblclick contextmenu', stopPropagation);\n    el['_leaflet_disable_click'] = true;\n    return this;\n  }\n\n  // @function preventDefault(ev: DOMEvent): this\n  // Prevents the default action of the DOM Event `ev` from happening (such as\n  // following a link in the href of the a element, or doing a POST request\n  // with page reload when a `<form>` is submitted).\n  // Use it inside listener functions.\n  function preventDefault(e) {\n    if (e.preventDefault) {\n      e.preventDefault();\n    } else {\n      e.returnValue = false;\n    }\n    return this;\n  }\n\n  // @function stop(ev: DOMEvent): this\n  // Does `stopPropagation` and `preventDefault` at the same time.\n  function stop(e) {\n    preventDefault(e);\n    stopPropagation(e);\n    return this;\n  }\n\n  // @function getPropagationPath(ev: DOMEvent): Array\n  // Compatibility polyfill for [`Event.composedPath()`](https://developer.mozilla.org/en-US/docs/Web/API/Event/composedPath).\n  // Returns an array containing the `HTMLElement`s that the given DOM event\n  // should propagate to (if not stopped).\n  function getPropagationPath(ev) {\n    if (ev.composedPath) {\n      return ev.composedPath();\n    }\n    var path = [];\n    var el = ev.target;\n    while (el) {\n      path.push(el);\n      el = el.parentNode;\n    }\n    return path;\n  }\n\n  // @function getMousePosition(ev: DOMEvent, container?: HTMLElement): Point\n  // Gets normalized mouse position from a DOM event relative to the\n  // `container` (border excluded) or to the whole page if not specified.\n  function getMousePosition(e, container) {\n    if (!container) {\n      return new Point(e.clientX, e.clientY);\n    }\n    var scale = getScale(container),\n      offset = scale.boundingClientRect; // left and top  values are in page scale (like the event clientX/Y)\n\n    return new Point(\n    // offset.left/top values are in page scale (like clientX/Y),\n    // whereas clientLeft/Top (border width) values are the original values (before CSS scale applies).\n    (e.clientX - offset.left) / scale.x - container.clientLeft, (e.clientY - offset.top) / scale.y - container.clientTop);\n  }\n\n  //  except , Safari and\n  // We need double the scroll pixels (see #7403 and #4538) for all Browsers\n  // except OSX (Mac) -> 3x, Chrome running on Linux 1x\n\n  var wheelPxFactor = Browser.linux && Browser.chrome ? window.devicePixelRatio : Browser.mac ? window.devicePixelRatio * 3 : window.devicePixelRatio > 0 ? 2 * window.devicePixelRatio : 1;\n  // @function getWheelDelta(ev: DOMEvent): Number\n  // Gets normalized wheel delta from a wheel DOM event, in vertical\n  // pixels scrolled (negative if scrolling down).\n  // Events from pointing devices without precise scrolling are mapped to\n  // a best guess of 60 pixels.\n  function getWheelDelta(e) {\n    return Browser.edge ? e.wheelDeltaY / 2 :\n    // Don't trust window-geometry-based delta\n    e.deltaY && e.deltaMode === 0 ? -e.deltaY / wheelPxFactor :\n    // Pixels\n    e.deltaY && e.deltaMode === 1 ? -e.deltaY * 20 :\n    // Lines\n    e.deltaY && e.deltaMode === 2 ? -e.deltaY * 60 :\n    // Pages\n    e.deltaX || e.deltaZ ? 0 :\n    // Skip horizontal/depth wheel events\n    e.wheelDelta ? (e.wheelDeltaY || e.wheelDelta) / 2 :\n    // Legacy IE pixels\n    e.detail && Math.abs(e.detail) < 32765 ? -e.detail * 20 :\n    // Legacy Moz lines\n    e.detail ? e.detail / -32765 * 60 :\n    // Legacy Moz pages\n    0;\n  }\n\n  // check if element really left/entered the event target (for mouseenter/mouseleave)\n  function isExternalTarget(el, e) {\n    var related = e.relatedTarget;\n    if (!related) {\n      return true;\n    }\n    try {\n      while (related && related !== el) {\n        related = related.parentNode;\n      }\n    } catch (err) {\n      return false;\n    }\n    return related !== el;\n  }\n  var DomEvent = {\n    __proto__: null,\n    on: on,\n    off: off,\n    stopPropagation: stopPropagation,\n    disableScrollPropagation: disableScrollPropagation,\n    disableClickPropagation: disableClickPropagation,\n    preventDefault: preventDefault,\n    stop: stop,\n    getPropagationPath: getPropagationPath,\n    getMousePosition: getMousePosition,\n    getWheelDelta: getWheelDelta,\n    isExternalTarget: isExternalTarget,\n    addListener: on,\n    removeListener: off\n  };\n\n  /*\n   * @class PosAnimation\n   * @aka L.PosAnimation\n   * @inherits Evented\n   * Used internally for panning animations, utilizing CSS3 Transitions for modern browsers and a timer fallback for IE6-9.\n   *\n   * @example\n   * ```js\n   * var myPositionMarker = L.marker([48.864716, 2.294694]).addTo(map);\n   *\n   * myPositionMarker.on(\"click\", function() {\n   * \tvar pos = map.latLngToLayerPoint(myPositionMarker.getLatLng());\n   * \tpos.y -= 25;\n   * \tvar fx = new L.PosAnimation();\n   *\n   * \tfx.once('end',function() {\n   * \t\tpos.y += 25;\n   * \t\tfx.run(myPositionMarker._icon, pos, 0.8);\n   * \t});\n   *\n   * \tfx.run(myPositionMarker._icon, pos, 0.3);\n   * });\n   *\n   * ```\n   *\n   * @constructor L.PosAnimation()\n   * Creates a `PosAnimation` object.\n   *\n   */\n\n  var PosAnimation = Evented.extend({\n    // @method run(el: HTMLElement, newPos: Point, duration?: Number, easeLinearity?: Number)\n    // Run an animation of a given element to a new position, optionally setting\n    // duration in seconds (`0.25` by default) and easing linearity factor (3rd\n    // argument of the [cubic bezier curve](https://cubic-bezier.com/#0,0,.5,1),\n    // `0.5` by default).\n    run: function (el, newPos, duration, easeLinearity) {\n      this.stop();\n      this._el = el;\n      this._inProgress = true;\n      this._duration = duration || 0.25;\n      this._easeOutPower = 1 / Math.max(easeLinearity || 0.5, 0.2);\n      this._startPos = getPosition(el);\n      this._offset = newPos.subtract(this._startPos);\n      this._startTime = +new Date();\n\n      // @event start: Event\n      // Fired when the animation starts\n      this.fire('start');\n      this._animate();\n    },\n    // @method stop()\n    // Stops the animation (if currently running).\n    stop: function () {\n      if (!this._inProgress) {\n        return;\n      }\n      this._step(true);\n      this._complete();\n    },\n    _animate: function () {\n      // animation loop\n      this._animId = requestAnimFrame(this._animate, this);\n      this._step();\n    },\n    _step: function (round) {\n      var elapsed = +new Date() - this._startTime,\n        duration = this._duration * 1000;\n      if (elapsed < duration) {\n        this._runFrame(this._easeOut(elapsed / duration), round);\n      } else {\n        this._runFrame(1);\n        this._complete();\n      }\n    },\n    _runFrame: function (progress, round) {\n      var pos = this._startPos.add(this._offset.multiplyBy(progress));\n      if (round) {\n        pos._round();\n      }\n      setPosition(this._el, pos);\n\n      // @event step: Event\n      // Fired continuously during the animation.\n      this.fire('step');\n    },\n    _complete: function () {\n      cancelAnimFrame(this._animId);\n      this._inProgress = false;\n      // @event end: Event\n      // Fired when the animation ends.\n      this.fire('end');\n    },\n    _easeOut: function (t) {\n      return 1 - Math.pow(1 - t, this._easeOutPower);\n    }\n  });\n\n  /*\r\n   * @class Map\r\n   * @aka L.Map\r\n   * @inherits Evented\r\n   *\r\n   * The central class of the API — it is used to create a map on a page and manipulate it.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * // initialize the map on the \"map\" div with a given center and zoom\r\n   * var map = L.map('map', {\r\n   * \tcenter: [51.505, -0.09],\r\n   * \tzoom: 13\r\n   * });\r\n   * ```\r\n   *\r\n   */\n\n  var Map = Evented.extend({\n    options: {\n      // @section Map State Options\n      // @option crs: CRS = L.CRS.EPSG3857\n      // The [Coordinate Reference System](#crs) to use. Don't change this if you're not\n      // sure what it means.\n      crs: EPSG3857,\n      // @option center: LatLng = undefined\n      // Initial geographic center of the map\n      center: undefined,\n      // @option zoom: Number = undefined\n      // Initial map zoom level\n      zoom: undefined,\n      // @option minZoom: Number = *\n      // Minimum zoom level of the map.\n      // If not specified and at least one `GridLayer` or `TileLayer` is in the map,\n      // the lowest of their `minZoom` options will be used instead.\n      minZoom: undefined,\n      // @option maxZoom: Number = *\n      // Maximum zoom level of the map.\n      // If not specified and at least one `GridLayer` or `TileLayer` is in the map,\n      // the highest of their `maxZoom` options will be used instead.\n      maxZoom: undefined,\n      // @option layers: Layer[] = []\n      // Array of layers that will be added to the map initially\n      layers: [],\n      // @option maxBounds: LatLngBounds = null\n      // When this option is set, the map restricts the view to the given\n      // geographical bounds, bouncing the user back if the user tries to pan\n      // outside the view. To set the restriction dynamically, use\n      // [`setMaxBounds`](#map-setmaxbounds) method.\n      maxBounds: undefined,\n      // @option renderer: Renderer = *\n      // The default method for drawing vector layers on the map. `L.SVG`\n      // or `L.Canvas` by default depending on browser support.\n      renderer: undefined,\n      // @section Animation Options\n      // @option zoomAnimation: Boolean = true\n      // Whether the map zoom animation is enabled. By default it's enabled\n      // in all browsers that support CSS3 Transitions except Android.\n      zoomAnimation: true,\n      // @option zoomAnimationThreshold: Number = 4\n      // Won't animate zoom if the zoom difference exceeds this value.\n      zoomAnimationThreshold: 4,\n      // @option fadeAnimation: Boolean = true\n      // Whether the tile fade animation is enabled. By default it's enabled\n      // in all browsers that support CSS3 Transitions except Android.\n      fadeAnimation: true,\n      // @option markerZoomAnimation: Boolean = true\n      // Whether markers animate their zoom with the zoom animation, if disabled\n      // they will disappear for the length of the animation. By default it's\n      // enabled in all browsers that support CSS3 Transitions except Android.\n      markerZoomAnimation: true,\n      // @option transform3DLimit: Number = 2^23\n      // Defines the maximum size of a CSS translation transform. The default\n      // value should not be changed unless a web browser positions layers in\n      // the wrong place after doing a large `panBy`.\n      transform3DLimit: 8388608,\n      // Precision limit of a 32-bit float\n\n      // @section Interaction Options\n      // @option zoomSnap: Number = 1\n      // Forces the map's zoom level to always be a multiple of this, particularly\n      // right after a [`fitBounds()`](#map-fitbounds) or a pinch-zoom.\n      // By default, the zoom level snaps to the nearest integer; lower values\n      // (e.g. `0.5` or `0.1`) allow for greater granularity. A value of `0`\n      // means the zoom level will not be snapped after `fitBounds` or a pinch-zoom.\n      zoomSnap: 1,\n      // @option zoomDelta: Number = 1\n      // Controls how much the map's zoom level will change after a\n      // [`zoomIn()`](#map-zoomin), [`zoomOut()`](#map-zoomout), pressing `+`\n      // or `-` on the keyboard, or using the [zoom controls](#control-zoom).\n      // Values smaller than `1` (e.g. `0.5`) allow for greater granularity.\n      zoomDelta: 1,\n      // @option trackResize: Boolean = true\n      // Whether the map automatically handles browser window resize to update itself.\n      trackResize: true\n    },\n    initialize: function (id, options) {\n      // (HTMLElement or String, Object)\n      options = setOptions(this, options);\n\n      // Make sure to assign internal flags at the beginning,\n      // to avoid inconsistent state in some edge cases.\n      this._handlers = [];\n      this._layers = {};\n      this._zoomBoundLayers = {};\n      this._sizeChanged = true;\n      this._initContainer(id);\n      this._initLayout();\n\n      // hack for https://github.com/Leaflet/Leaflet/issues/1980\n      this._onResize = bind(this._onResize, this);\n      this._initEvents();\n      if (options.maxBounds) {\n        this.setMaxBounds(options.maxBounds);\n      }\n      if (options.zoom !== undefined) {\n        this._zoom = this._limitZoom(options.zoom);\n      }\n      if (options.center && options.zoom !== undefined) {\n        this.setView(toLatLng(options.center), options.zoom, {\n          reset: true\n        });\n      }\n      this.callInitHooks();\n\n      // don't animate on browsers without hardware-accelerated transitions or old Android/Opera\n      this._zoomAnimated = TRANSITION && Browser.any3d && !Browser.mobileOpera && this.options.zoomAnimation;\n\n      // zoom transitions run with the same duration for all layers, so if one of transitionend events\n      // happens after starting zoom animation (propagating to the map pane), we know that it ended globally\n      if (this._zoomAnimated) {\n        this._createAnimProxy();\n        on(this._proxy, TRANSITION_END, this._catchTransitionEnd, this);\n      }\n      this._addLayers(this.options.layers);\n    },\n    // @section Methods for modifying map state\n\n    // @method setView(center: LatLng, zoom: Number, options?: Zoom/pan options): this\n    // Sets the view of the map (geographical center and zoom) with the given\n    // animation options.\n    setView: function (center, zoom, options) {\n      zoom = zoom === undefined ? this._zoom : this._limitZoom(zoom);\n      center = this._limitCenter(toLatLng(center), zoom, this.options.maxBounds);\n      options = options || {};\n      this._stop();\n      if (this._loaded && !options.reset && options !== true) {\n        if (options.animate !== undefined) {\n          options.zoom = extend({\n            animate: options.animate\n          }, options.zoom);\n          options.pan = extend({\n            animate: options.animate,\n            duration: options.duration\n          }, options.pan);\n        }\n\n        // try animating pan or zoom\n        var moved = this._zoom !== zoom ? this._tryAnimatedZoom && this._tryAnimatedZoom(center, zoom, options.zoom) : this._tryAnimatedPan(center, options.pan);\n        if (moved) {\n          // prevent resize handler call, the view will refresh after animation anyway\n          clearTimeout(this._sizeTimer);\n          return this;\n        }\n      }\n\n      // animation didn't start, just reset the map view\n      this._resetView(center, zoom, options.pan && options.pan.noMoveStart);\n      return this;\n    },\n    // @method setZoom(zoom: Number, options?: Zoom/pan options): this\n    // Sets the zoom of the map.\n    setZoom: function (zoom, options) {\n      if (!this._loaded) {\n        this._zoom = zoom;\n        return this;\n      }\n      return this.setView(this.getCenter(), zoom, {\n        zoom: options\n      });\n    },\n    // @method zoomIn(delta?: Number, options?: Zoom options): this\n    // Increases the zoom of the map by `delta` ([`zoomDelta`](#map-zoomdelta) by default).\n    zoomIn: function (delta, options) {\n      delta = delta || (Browser.any3d ? this.options.zoomDelta : 1);\n      return this.setZoom(this._zoom + delta, options);\n    },\n    // @method zoomOut(delta?: Number, options?: Zoom options): this\n    // Decreases the zoom of the map by `delta` ([`zoomDelta`](#map-zoomdelta) by default).\n    zoomOut: function (delta, options) {\n      delta = delta || (Browser.any3d ? this.options.zoomDelta : 1);\n      return this.setZoom(this._zoom - delta, options);\n    },\n    // @method setZoomAround(latlng: LatLng, zoom: Number, options: Zoom options): this\n    // Zooms the map while keeping a specified geographical point on the map\n    // stationary (e.g. used internally for scroll zoom and double-click zoom).\n    // @alternative\n    // @method setZoomAround(offset: Point, zoom: Number, options: Zoom options): this\n    // Zooms the map while keeping a specified pixel on the map (relative to the top-left corner) stationary.\n    setZoomAround: function (latlng, zoom, options) {\n      var scale = this.getZoomScale(zoom),\n        viewHalf = this.getSize().divideBy(2),\n        containerPoint = latlng instanceof Point ? latlng : this.latLngToContainerPoint(latlng),\n        centerOffset = containerPoint.subtract(viewHalf).multiplyBy(1 - 1 / scale),\n        newCenter = this.containerPointToLatLng(viewHalf.add(centerOffset));\n      return this.setView(newCenter, zoom, {\n        zoom: options\n      });\n    },\n    _getBoundsCenterZoom: function (bounds, options) {\n      options = options || {};\n      bounds = bounds.getBounds ? bounds.getBounds() : toLatLngBounds(bounds);\n      var paddingTL = toPoint(options.paddingTopLeft || options.padding || [0, 0]),\n        paddingBR = toPoint(options.paddingBottomRight || options.padding || [0, 0]),\n        zoom = this.getBoundsZoom(bounds, false, paddingTL.add(paddingBR));\n      zoom = typeof options.maxZoom === 'number' ? Math.min(options.maxZoom, zoom) : zoom;\n      if (zoom === Infinity) {\n        return {\n          center: bounds.getCenter(),\n          zoom: zoom\n        };\n      }\n      var paddingOffset = paddingBR.subtract(paddingTL).divideBy(2),\n        swPoint = this.project(bounds.getSouthWest(), zoom),\n        nePoint = this.project(bounds.getNorthEast(), zoom),\n        center = this.unproject(swPoint.add(nePoint).divideBy(2).add(paddingOffset), zoom);\n      return {\n        center: center,\n        zoom: zoom\n      };\n    },\n    // @method fitBounds(bounds: LatLngBounds, options?: fitBounds options): this\n    // Sets a map view that contains the given geographical bounds with the\n    // maximum zoom level possible.\n    fitBounds: function (bounds, options) {\n      bounds = toLatLngBounds(bounds);\n      if (!bounds.isValid()) {\n        throw new Error('Bounds are not valid.');\n      }\n      var target = this._getBoundsCenterZoom(bounds, options);\n      return this.setView(target.center, target.zoom, options);\n    },\n    // @method fitWorld(options?: fitBounds options): this\n    // Sets a map view that mostly contains the whole world with the maximum\n    // zoom level possible.\n    fitWorld: function (options) {\n      return this.fitBounds([[-90, -180], [90, 180]], options);\n    },\n    // @method panTo(latlng: LatLng, options?: Pan options): this\n    // Pans the map to a given center.\n    panTo: function (center, options) {\n      // (LatLng)\n      return this.setView(center, this._zoom, {\n        pan: options\n      });\n    },\n    // @method panBy(offset: Point, options?: Pan options): this\n    // Pans the map by a given number of pixels (animated).\n    panBy: function (offset, options) {\n      offset = toPoint(offset).round();\n      options = options || {};\n      if (!offset.x && !offset.y) {\n        return this.fire('moveend');\n      }\n      // If we pan too far, Chrome gets issues with tiles\n      // and makes them disappear or appear in the wrong place (slightly offset) #2602\n      if (options.animate !== true && !this.getSize().contains(offset)) {\n        this._resetView(this.unproject(this.project(this.getCenter()).add(offset)), this.getZoom());\n        return this;\n      }\n      if (!this._panAnim) {\n        this._panAnim = new PosAnimation();\n        this._panAnim.on({\n          'step': this._onPanTransitionStep,\n          'end': this._onPanTransitionEnd\n        }, this);\n      }\n\n      // don't fire movestart if animating inertia\n      if (!options.noMoveStart) {\n        this.fire('movestart');\n      }\n\n      // animate pan unless animate: false specified\n      if (options.animate !== false) {\n        addClass(this._mapPane, 'leaflet-pan-anim');\n        var newPos = this._getMapPanePos().subtract(offset).round();\n        this._panAnim.run(this._mapPane, newPos, options.duration || 0.25, options.easeLinearity);\n      } else {\n        this._rawPanBy(offset);\n        this.fire('move').fire('moveend');\n      }\n      return this;\n    },\n    // @method flyTo(latlng: LatLng, zoom?: Number, options?: Zoom/pan options): this\n    // Sets the view of the map (geographical center and zoom) performing a smooth\n    // pan-zoom animation.\n    flyTo: function (targetCenter, targetZoom, options) {\n      options = options || {};\n      if (options.animate === false || !Browser.any3d) {\n        return this.setView(targetCenter, targetZoom, options);\n      }\n      this._stop();\n      var from = this.project(this.getCenter()),\n        to = this.project(targetCenter),\n        size = this.getSize(),\n        startZoom = this._zoom;\n      targetCenter = toLatLng(targetCenter);\n      targetZoom = targetZoom === undefined ? startZoom : targetZoom;\n      var w0 = Math.max(size.x, size.y),\n        w1 = w0 * this.getZoomScale(startZoom, targetZoom),\n        u1 = to.distanceTo(from) || 1,\n        rho = 1.42,\n        rho2 = rho * rho;\n      function r(i) {\n        var s1 = i ? -1 : 1,\n          s2 = i ? w1 : w0,\n          t1 = w1 * w1 - w0 * w0 + s1 * rho2 * rho2 * u1 * u1,\n          b1 = 2 * s2 * rho2 * u1,\n          b = t1 / b1,\n          sq = Math.sqrt(b * b + 1) - b;\n\n        // workaround for floating point precision bug when sq = 0, log = -Infinite,\n        // thus triggering an infinite loop in flyTo\n        var log = sq < 0.000000001 ? -18 : Math.log(sq);\n        return log;\n      }\n      function sinh(n) {\n        return (Math.exp(n) - Math.exp(-n)) / 2;\n      }\n      function cosh(n) {\n        return (Math.exp(n) + Math.exp(-n)) / 2;\n      }\n      function tanh(n) {\n        return sinh(n) / cosh(n);\n      }\n      var r0 = r(0);\n      function w(s) {\n        return w0 * (cosh(r0) / cosh(r0 + rho * s));\n      }\n      function u(s) {\n        return w0 * (cosh(r0) * tanh(r0 + rho * s) - sinh(r0)) / rho2;\n      }\n      function easeOut(t) {\n        return 1 - Math.pow(1 - t, 1.5);\n      }\n      var start = Date.now(),\n        S = (r(1) - r0) / rho,\n        duration = options.duration ? 1000 * options.duration : 1000 * S * 0.8;\n      function frame() {\n        var t = (Date.now() - start) / duration,\n          s = easeOut(t) * S;\n        if (t <= 1) {\n          this._flyToFrame = requestAnimFrame(frame, this);\n          this._move(this.unproject(from.add(to.subtract(from).multiplyBy(u(s) / u1)), startZoom), this.getScaleZoom(w0 / w(s), startZoom), {\n            flyTo: true\n          });\n        } else {\n          this._move(targetCenter, targetZoom)._moveEnd(true);\n        }\n      }\n      this._moveStart(true, options.noMoveStart);\n      frame.call(this);\n      return this;\n    },\n    // @method flyToBounds(bounds: LatLngBounds, options?: fitBounds options): this\n    // Sets the view of the map with a smooth animation like [`flyTo`](#map-flyto),\n    // but takes a bounds parameter like [`fitBounds`](#map-fitbounds).\n    flyToBounds: function (bounds, options) {\n      var target = this._getBoundsCenterZoom(bounds, options);\n      return this.flyTo(target.center, target.zoom, options);\n    },\n    // @method setMaxBounds(bounds: LatLngBounds): this\n    // Restricts the map view to the given bounds (see the [maxBounds](#map-maxbounds) option).\n    setMaxBounds: function (bounds) {\n      bounds = toLatLngBounds(bounds);\n      if (this.listens('moveend', this._panInsideMaxBounds)) {\n        this.off('moveend', this._panInsideMaxBounds);\n      }\n      if (!bounds.isValid()) {\n        this.options.maxBounds = null;\n        return this;\n      }\n      this.options.maxBounds = bounds;\n      if (this._loaded) {\n        this._panInsideMaxBounds();\n      }\n      return this.on('moveend', this._panInsideMaxBounds);\n    },\n    // @method setMinZoom(zoom: Number): this\n    // Sets the lower limit for the available zoom levels (see the [minZoom](#map-minzoom) option).\n    setMinZoom: function (zoom) {\n      var oldZoom = this.options.minZoom;\n      this.options.minZoom = zoom;\n      if (this._loaded && oldZoom !== zoom) {\n        this.fire('zoomlevelschange');\n        if (this.getZoom() < this.options.minZoom) {\n          return this.setZoom(zoom);\n        }\n      }\n      return this;\n    },\n    // @method setMaxZoom(zoom: Number): this\n    // Sets the upper limit for the available zoom levels (see the [maxZoom](#map-maxzoom) option).\n    setMaxZoom: function (zoom) {\n      var oldZoom = this.options.maxZoom;\n      this.options.maxZoom = zoom;\n      if (this._loaded && oldZoom !== zoom) {\n        this.fire('zoomlevelschange');\n        if (this.getZoom() > this.options.maxZoom) {\n          return this.setZoom(zoom);\n        }\n      }\n      return this;\n    },\n    // @method panInsideBounds(bounds: LatLngBounds, options?: Pan options): this\n    // Pans the map to the closest view that would lie inside the given bounds (if it's not already), controlling the animation using the options specific, if any.\n    panInsideBounds: function (bounds, options) {\n      this._enforcingBounds = true;\n      var center = this.getCenter(),\n        newCenter = this._limitCenter(center, this._zoom, toLatLngBounds(bounds));\n      if (!center.equals(newCenter)) {\n        this.panTo(newCenter, options);\n      }\n      this._enforcingBounds = false;\n      return this;\n    },\n    // @method panInside(latlng: LatLng, options?: padding options): this\n    // Pans the map the minimum amount to make the `latlng` visible. Use\n    // padding options to fit the display to more restricted bounds.\n    // If `latlng` is already within the (optionally padded) display bounds,\n    // the map will not be panned.\n    panInside: function (latlng, options) {\n      options = options || {};\n      var paddingTL = toPoint(options.paddingTopLeft || options.padding || [0, 0]),\n        paddingBR = toPoint(options.paddingBottomRight || options.padding || [0, 0]),\n        pixelCenter = this.project(this.getCenter()),\n        pixelPoint = this.project(latlng),\n        pixelBounds = this.getPixelBounds(),\n        paddedBounds = toBounds([pixelBounds.min.add(paddingTL), pixelBounds.max.subtract(paddingBR)]),\n        paddedSize = paddedBounds.getSize();\n      if (!paddedBounds.contains(pixelPoint)) {\n        this._enforcingBounds = true;\n        var centerOffset = pixelPoint.subtract(paddedBounds.getCenter());\n        var offset = paddedBounds.extend(pixelPoint).getSize().subtract(paddedSize);\n        pixelCenter.x += centerOffset.x < 0 ? -offset.x : offset.x;\n        pixelCenter.y += centerOffset.y < 0 ? -offset.y : offset.y;\n        this.panTo(this.unproject(pixelCenter), options);\n        this._enforcingBounds = false;\n      }\n      return this;\n    },\n    // @method invalidateSize(options: Zoom/pan options): this\n    // Checks if the map container size changed and updates the map if so —\n    // call it after you've changed the map size dynamically, also animating\n    // pan by default. If `options.pan` is `false`, panning will not occur.\n    // If `options.debounceMoveend` is `true`, it will delay `moveend` event so\n    // that it doesn't happen often even if the method is called many\n    // times in a row.\n\n    // @alternative\n    // @method invalidateSize(animate: Boolean): this\n    // Checks if the map container size changed and updates the map if so —\n    // call it after you've changed the map size dynamically, also animating\n    // pan by default.\n    invalidateSize: function (options) {\n      if (!this._loaded) {\n        return this;\n      }\n      options = extend({\n        animate: false,\n        pan: true\n      }, options === true ? {\n        animate: true\n      } : options);\n      var oldSize = this.getSize();\n      this._sizeChanged = true;\n      this._lastCenter = null;\n      var newSize = this.getSize(),\n        oldCenter = oldSize.divideBy(2).round(),\n        newCenter = newSize.divideBy(2).round(),\n        offset = oldCenter.subtract(newCenter);\n      if (!offset.x && !offset.y) {\n        return this;\n      }\n      if (options.animate && options.pan) {\n        this.panBy(offset);\n      } else {\n        if (options.pan) {\n          this._rawPanBy(offset);\n        }\n        this.fire('move');\n        if (options.debounceMoveend) {\n          clearTimeout(this._sizeTimer);\n          this._sizeTimer = setTimeout(bind(this.fire, this, 'moveend'), 200);\n        } else {\n          this.fire('moveend');\n        }\n      }\n\n      // @section Map state change events\n      // @event resize: ResizeEvent\n      // Fired when the map is resized.\n      return this.fire('resize', {\n        oldSize: oldSize,\n        newSize: newSize\n      });\n    },\n    // @section Methods for modifying map state\n    // @method stop(): this\n    // Stops the currently running `panTo` or `flyTo` animation, if any.\n    stop: function () {\n      this.setZoom(this._limitZoom(this._zoom));\n      if (!this.options.zoomSnap) {\n        this.fire('viewreset');\n      }\n      return this._stop();\n    },\n    // @section Geolocation methods\n    // @method locate(options?: Locate options): this\n    // Tries to locate the user using the Geolocation API, firing a [`locationfound`](#map-locationfound)\n    // event with location data on success or a [`locationerror`](#map-locationerror) event on failure,\n    // and optionally sets the map view to the user's location with respect to\n    // detection accuracy (or to the world view if geolocation failed).\n    // Note that, if your page doesn't use HTTPS, this method will fail in\n    // modern browsers ([Chrome 50 and newer](https://sites.google.com/a/chromium.org/dev/Home/chromium-security/deprecating-powerful-features-on-insecure-origins))\n    // See `Locate options` for more details.\n    locate: function (options) {\n      options = this._locateOptions = extend({\n        timeout: 10000,\n        watch: false\n        // setView: false\n        // maxZoom: <Number>\n        // maximumAge: 0\n        // enableHighAccuracy: false\n      }, options);\n      if (!('geolocation' in navigator)) {\n        this._handleGeolocationError({\n          code: 0,\n          message: 'Geolocation not supported.'\n        });\n        return this;\n      }\n      var onResponse = bind(this._handleGeolocationResponse, this),\n        onError = bind(this._handleGeolocationError, this);\n      if (options.watch) {\n        this._locationWatchId = navigator.geolocation.watchPosition(onResponse, onError, options);\n      } else {\n        navigator.geolocation.getCurrentPosition(onResponse, onError, options);\n      }\n      return this;\n    },\n    // @method stopLocate(): this\n    // Stops watching location previously initiated by `map.locate({watch: true})`\n    // and aborts resetting the map view if map.locate was called with\n    // `{setView: true}`.\n    stopLocate: function () {\n      if (navigator.geolocation && navigator.geolocation.clearWatch) {\n        navigator.geolocation.clearWatch(this._locationWatchId);\n      }\n      if (this._locateOptions) {\n        this._locateOptions.setView = false;\n      }\n      return this;\n    },\n    _handleGeolocationError: function (error) {\n      if (!this._container._leaflet_id) {\n        return;\n      }\n      var c = error.code,\n        message = error.message || (c === 1 ? 'permission denied' : c === 2 ? 'position unavailable' : 'timeout');\n      if (this._locateOptions.setView && !this._loaded) {\n        this.fitWorld();\n      }\n\n      // @section Location events\n      // @event locationerror: ErrorEvent\n      // Fired when geolocation (using the [`locate`](#map-locate) method) failed.\n      this.fire('locationerror', {\n        code: c,\n        message: 'Geolocation error: ' + message + '.'\n      });\n    },\n    _handleGeolocationResponse: function (pos) {\n      if (!this._container._leaflet_id) {\n        return;\n      }\n      var lat = pos.coords.latitude,\n        lng = pos.coords.longitude,\n        latlng = new LatLng(lat, lng),\n        bounds = latlng.toBounds(pos.coords.accuracy * 2),\n        options = this._locateOptions;\n      if (options.setView) {\n        var zoom = this.getBoundsZoom(bounds);\n        this.setView(latlng, options.maxZoom ? Math.min(zoom, options.maxZoom) : zoom);\n      }\n      var data = {\n        latlng: latlng,\n        bounds: bounds,\n        timestamp: pos.timestamp\n      };\n      for (var i in pos.coords) {\n        if (typeof pos.coords[i] === 'number') {\n          data[i] = pos.coords[i];\n        }\n      }\n\n      // @event locationfound: LocationEvent\n      // Fired when geolocation (using the [`locate`](#map-locate) method)\n      // went successfully.\n      this.fire('locationfound', data);\n    },\n    // TODO Appropriate docs section?\n    // @section Other Methods\n    // @method addHandler(name: String, HandlerClass: Function): this\n    // Adds a new `Handler` to the map, given its name and constructor function.\n    addHandler: function (name, HandlerClass) {\n      if (!HandlerClass) {\n        return this;\n      }\n      var handler = this[name] = new HandlerClass(this);\n      this._handlers.push(handler);\n      if (this.options[name]) {\n        handler.enable();\n      }\n      return this;\n    },\n    // @method remove(): this\n    // Destroys the map and clears all related event listeners.\n    remove: function () {\n      this._initEvents(true);\n      if (this.options.maxBounds) {\n        this.off('moveend', this._panInsideMaxBounds);\n      }\n      if (this._containerId !== this._container._leaflet_id) {\n        throw new Error('Map container is being reused by another instance');\n      }\n      try {\n        // throws error in IE6-8\n        delete this._container._leaflet_id;\n        delete this._containerId;\n      } catch (e) {\n        /*eslint-disable */\n        this._container._leaflet_id = undefined;\n        /* eslint-enable */\n        this._containerId = undefined;\n      }\n      if (this._locationWatchId !== undefined) {\n        this.stopLocate();\n      }\n      this._stop();\n      remove(this._mapPane);\n      if (this._clearControlPos) {\n        this._clearControlPos();\n      }\n      if (this._resizeRequest) {\n        cancelAnimFrame(this._resizeRequest);\n        this._resizeRequest = null;\n      }\n      this._clearHandlers();\n      if (this._loaded) {\n        // @section Map state change events\n        // @event unload: Event\n        // Fired when the map is destroyed with [remove](#map-remove) method.\n        this.fire('unload');\n      }\n      var i;\n      for (i in this._layers) {\n        this._layers[i].remove();\n      }\n      for (i in this._panes) {\n        remove(this._panes[i]);\n      }\n      this._layers = [];\n      this._panes = [];\n      delete this._mapPane;\n      delete this._renderer;\n      return this;\n    },\n    // @section Other Methods\n    // @method createPane(name: String, container?: HTMLElement): HTMLElement\n    // Creates a new [map pane](#map-pane) with the given name if it doesn't exist already,\n    // then returns it. The pane is created as a child of `container`, or\n    // as a child of the main map pane if not set.\n    createPane: function (name, container) {\n      var className = 'leaflet-pane' + (name ? ' leaflet-' + name.replace('Pane', '') + '-pane' : ''),\n        pane = create$1('div', className, container || this._mapPane);\n      if (name) {\n        this._panes[name] = pane;\n      }\n      return pane;\n    },\n    // @section Methods for Getting Map State\n\n    // @method getCenter(): LatLng\n    // Returns the geographical center of the map view\n    getCenter: function () {\n      this._checkIfLoaded();\n      if (this._lastCenter && !this._moved()) {\n        return this._lastCenter.clone();\n      }\n      return this.layerPointToLatLng(this._getCenterLayerPoint());\n    },\n    // @method getZoom(): Number\n    // Returns the current zoom level of the map view\n    getZoom: function () {\n      return this._zoom;\n    },\n    // @method getBounds(): LatLngBounds\n    // Returns the geographical bounds visible in the current map view\n    getBounds: function () {\n      var bounds = this.getPixelBounds(),\n        sw = this.unproject(bounds.getBottomLeft()),\n        ne = this.unproject(bounds.getTopRight());\n      return new LatLngBounds(sw, ne);\n    },\n    // @method getMinZoom(): Number\n    // Returns the minimum zoom level of the map (if set in the `minZoom` option of the map or of any layers), or `0` by default.\n    getMinZoom: function () {\n      return this.options.minZoom === undefined ? this._layersMinZoom || 0 : this.options.minZoom;\n    },\n    // @method getMaxZoom(): Number\n    // Returns the maximum zoom level of the map (if set in the `maxZoom` option of the map or of any layers).\n    getMaxZoom: function () {\n      return this.options.maxZoom === undefined ? this._layersMaxZoom === undefined ? Infinity : this._layersMaxZoom : this.options.maxZoom;\n    },\n    // @method getBoundsZoom(bounds: LatLngBounds, inside?: Boolean, padding?: Point): Number\n    // Returns the maximum zoom level on which the given bounds fit to the map\n    // view in its entirety. If `inside` (optional) is set to `true`, the method\n    // instead returns the minimum zoom level on which the map view fits into\n    // the given bounds in its entirety.\n    getBoundsZoom: function (bounds, inside, padding) {\n      // (LatLngBounds[, Boolean, Point]) -> Number\n      bounds = toLatLngBounds(bounds);\n      padding = toPoint(padding || [0, 0]);\n      var zoom = this.getZoom() || 0,\n        min = this.getMinZoom(),\n        max = this.getMaxZoom(),\n        nw = bounds.getNorthWest(),\n        se = bounds.getSouthEast(),\n        size = this.getSize().subtract(padding),\n        boundsSize = toBounds(this.project(se, zoom), this.project(nw, zoom)).getSize(),\n        snap = Browser.any3d ? this.options.zoomSnap : 1,\n        scalex = size.x / boundsSize.x,\n        scaley = size.y / boundsSize.y,\n        scale = inside ? Math.max(scalex, scaley) : Math.min(scalex, scaley);\n      zoom = this.getScaleZoom(scale, zoom);\n      if (snap) {\n        zoom = Math.round(zoom / (snap / 100)) * (snap / 100); // don't jump if within 1% of a snap level\n        zoom = inside ? Math.ceil(zoom / snap) * snap : Math.floor(zoom / snap) * snap;\n      }\n      return Math.max(min, Math.min(max, zoom));\n    },\n    // @method getSize(): Point\n    // Returns the current size of the map container (in pixels).\n    getSize: function () {\n      if (!this._size || this._sizeChanged) {\n        this._size = new Point(this._container.clientWidth || 0, this._container.clientHeight || 0);\n        this._sizeChanged = false;\n      }\n      return this._size.clone();\n    },\n    // @method getPixelBounds(): Bounds\n    // Returns the bounds of the current map view in projected pixel\n    // coordinates (sometimes useful in layer and overlay implementations).\n    getPixelBounds: function (center, zoom) {\n      var topLeftPoint = this._getTopLeftPoint(center, zoom);\n      return new Bounds(topLeftPoint, topLeftPoint.add(this.getSize()));\n    },\n    // TODO: Check semantics - isn't the pixel origin the 0,0 coord relative to\n    // the map pane? \"left point of the map layer\" can be confusing, specially\n    // since there can be negative offsets.\n    // @method getPixelOrigin(): Point\n    // Returns the projected pixel coordinates of the top left point of\n    // the map layer (useful in custom layer and overlay implementations).\n    getPixelOrigin: function () {\n      this._checkIfLoaded();\n      return this._pixelOrigin;\n    },\n    // @method getPixelWorldBounds(zoom?: Number): Bounds\n    // Returns the world's bounds in pixel coordinates for zoom level `zoom`.\n    // If `zoom` is omitted, the map's current zoom level is used.\n    getPixelWorldBounds: function (zoom) {\n      return this.options.crs.getProjectedBounds(zoom === undefined ? this.getZoom() : zoom);\n    },\n    // @section Other Methods\n\n    // @method getPane(pane: String|HTMLElement): HTMLElement\n    // Returns a [map pane](#map-pane), given its name or its HTML element (its identity).\n    getPane: function (pane) {\n      return typeof pane === 'string' ? this._panes[pane] : pane;\n    },\n    // @method getPanes(): Object\n    // Returns a plain object containing the names of all [panes](#map-pane) as keys and\n    // the panes as values.\n    getPanes: function () {\n      return this._panes;\n    },\n    // @method getContainer: HTMLElement\n    // Returns the HTML element that contains the map.\n    getContainer: function () {\n      return this._container;\n    },\n    // @section Conversion Methods\n\n    // @method getZoomScale(toZoom: Number, fromZoom: Number): Number\n    // Returns the scale factor to be applied to a map transition from zoom level\n    // `fromZoom` to `toZoom`. Used internally to help with zoom animations.\n    getZoomScale: function (toZoom, fromZoom) {\n      // TODO replace with universal implementation after refactoring projections\n      var crs = this.options.crs;\n      fromZoom = fromZoom === undefined ? this._zoom : fromZoom;\n      return crs.scale(toZoom) / crs.scale(fromZoom);\n    },\n    // @method getScaleZoom(scale: Number, fromZoom: Number): Number\n    // Returns the zoom level that the map would end up at, if it is at `fromZoom`\n    // level and everything is scaled by a factor of `scale`. Inverse of\n    // [`getZoomScale`](#map-getZoomScale).\n    getScaleZoom: function (scale, fromZoom) {\n      var crs = this.options.crs;\n      fromZoom = fromZoom === undefined ? this._zoom : fromZoom;\n      var zoom = crs.zoom(scale * crs.scale(fromZoom));\n      return isNaN(zoom) ? Infinity : zoom;\n    },\n    // @method project(latlng: LatLng, zoom: Number): Point\n    // Projects a geographical coordinate `LatLng` according to the projection\n    // of the map's CRS, then scales it according to `zoom` and the CRS's\n    // `Transformation`. The result is pixel coordinate relative to\n    // the CRS origin.\n    project: function (latlng, zoom) {\n      zoom = zoom === undefined ? this._zoom : zoom;\n      return this.options.crs.latLngToPoint(toLatLng(latlng), zoom);\n    },\n    // @method unproject(point: Point, zoom: Number): LatLng\n    // Inverse of [`project`](#map-project).\n    unproject: function (point, zoom) {\n      zoom = zoom === undefined ? this._zoom : zoom;\n      return this.options.crs.pointToLatLng(toPoint(point), zoom);\n    },\n    // @method layerPointToLatLng(point: Point): LatLng\n    // Given a pixel coordinate relative to the [origin pixel](#map-getpixelorigin),\n    // returns the corresponding geographical coordinate (for the current zoom level).\n    layerPointToLatLng: function (point) {\n      var projectedPoint = toPoint(point).add(this.getPixelOrigin());\n      return this.unproject(projectedPoint);\n    },\n    // @method latLngToLayerPoint(latlng: LatLng): Point\n    // Given a geographical coordinate, returns the corresponding pixel coordinate\n    // relative to the [origin pixel](#map-getpixelorigin).\n    latLngToLayerPoint: function (latlng) {\n      var projectedPoint = this.project(toLatLng(latlng))._round();\n      return projectedPoint._subtract(this.getPixelOrigin());\n    },\n    // @method wrapLatLng(latlng: LatLng): LatLng\n    // Returns a `LatLng` where `lat` and `lng` has been wrapped according to the\n    // map's CRS's `wrapLat` and `wrapLng` properties, if they are outside the\n    // CRS's bounds.\n    // By default this means longitude is wrapped around the dateline so its\n    // value is between -180 and +180 degrees.\n    wrapLatLng: function (latlng) {\n      return this.options.crs.wrapLatLng(toLatLng(latlng));\n    },\n    // @method wrapLatLngBounds(bounds: LatLngBounds): LatLngBounds\n    // Returns a `LatLngBounds` with the same size as the given one, ensuring that\n    // its center is within the CRS's bounds.\n    // By default this means the center longitude is wrapped around the dateline so its\n    // value is between -180 and +180 degrees, and the majority of the bounds\n    // overlaps the CRS's bounds.\n    wrapLatLngBounds: function (latlng) {\n      return this.options.crs.wrapLatLngBounds(toLatLngBounds(latlng));\n    },\n    // @method distance(latlng1: LatLng, latlng2: LatLng): Number\n    // Returns the distance between two geographical coordinates according to\n    // the map's CRS. By default this measures distance in meters.\n    distance: function (latlng1, latlng2) {\n      return this.options.crs.distance(toLatLng(latlng1), toLatLng(latlng2));\n    },\n    // @method containerPointToLayerPoint(point: Point): Point\n    // Given a pixel coordinate relative to the map container, returns the corresponding\n    // pixel coordinate relative to the [origin pixel](#map-getpixelorigin).\n    containerPointToLayerPoint: function (point) {\n      // (Point)\n      return toPoint(point).subtract(this._getMapPanePos());\n    },\n    // @method layerPointToContainerPoint(point: Point): Point\n    // Given a pixel coordinate relative to the [origin pixel](#map-getpixelorigin),\n    // returns the corresponding pixel coordinate relative to the map container.\n    layerPointToContainerPoint: function (point) {\n      // (Point)\n      return toPoint(point).add(this._getMapPanePos());\n    },\n    // @method containerPointToLatLng(point: Point): LatLng\n    // Given a pixel coordinate relative to the map container, returns\n    // the corresponding geographical coordinate (for the current zoom level).\n    containerPointToLatLng: function (point) {\n      var layerPoint = this.containerPointToLayerPoint(toPoint(point));\n      return this.layerPointToLatLng(layerPoint);\n    },\n    // @method latLngToContainerPoint(latlng: LatLng): Point\n    // Given a geographical coordinate, returns the corresponding pixel coordinate\n    // relative to the map container.\n    latLngToContainerPoint: function (latlng) {\n      return this.layerPointToContainerPoint(this.latLngToLayerPoint(toLatLng(latlng)));\n    },\n    // @method mouseEventToContainerPoint(ev: MouseEvent): Point\n    // Given a MouseEvent object, returns the pixel coordinate relative to the\n    // map container where the event took place.\n    mouseEventToContainerPoint: function (e) {\n      return getMousePosition(e, this._container);\n    },\n    // @method mouseEventToLayerPoint(ev: MouseEvent): Point\n    // Given a MouseEvent object, returns the pixel coordinate relative to\n    // the [origin pixel](#map-getpixelorigin) where the event took place.\n    mouseEventToLayerPoint: function (e) {\n      return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(e));\n    },\n    // @method mouseEventToLatLng(ev: MouseEvent): LatLng\n    // Given a MouseEvent object, returns geographical coordinate where the\n    // event took place.\n    mouseEventToLatLng: function (e) {\n      // (MouseEvent)\n      return this.layerPointToLatLng(this.mouseEventToLayerPoint(e));\n    },\n    // map initialization methods\n\n    _initContainer: function (id) {\n      var container = this._container = get(id);\n      if (!container) {\n        throw new Error('Map container not found.');\n      } else if (container._leaflet_id) {\n        throw new Error('Map container is already initialized.');\n      }\n      on(container, 'scroll', this._onScroll, this);\n      this._containerId = stamp(container);\n    },\n    _initLayout: function () {\n      var container = this._container;\n      this._fadeAnimated = this.options.fadeAnimation && Browser.any3d;\n      addClass(container, 'leaflet-container' + (Browser.touch ? ' leaflet-touch' : '') + (Browser.retina ? ' leaflet-retina' : '') + (Browser.ielt9 ? ' leaflet-oldie' : '') + (Browser.safari ? ' leaflet-safari' : '') + (this._fadeAnimated ? ' leaflet-fade-anim' : ''));\n      var position = getStyle(container, 'position');\n      if (position !== 'absolute' && position !== 'relative' && position !== 'fixed' && position !== 'sticky') {\n        container.style.position = 'relative';\n      }\n      this._initPanes();\n      if (this._initControlPos) {\n        this._initControlPos();\n      }\n    },\n    _initPanes: function () {\n      var panes = this._panes = {};\n      this._paneRenderers = {};\n\n      // @section\n      //\n      // Panes are DOM elements used to control the ordering of layers on the map. You\n      // can access panes with [`map.getPane`](#map-getpane) or\n      // [`map.getPanes`](#map-getpanes) methods. New panes can be created with the\n      // [`map.createPane`](#map-createpane) method.\n      //\n      // Every map has the following default panes that differ only in zIndex.\n      //\n      // @pane mapPane: HTMLElement = 'auto'\n      // Pane that contains all other map panes\n\n      this._mapPane = this.createPane('mapPane', this._container);\n      setPosition(this._mapPane, new Point(0, 0));\n\n      // @pane tilePane: HTMLElement = 200\n      // Pane for `GridLayer`s and `TileLayer`s\n      this.createPane('tilePane');\n      // @pane overlayPane: HTMLElement = 400\n      // Pane for vectors (`Path`s, like `Polyline`s and `Polygon`s), `ImageOverlay`s and `VideoOverlay`s\n      this.createPane('overlayPane');\n      // @pane shadowPane: HTMLElement = 500\n      // Pane for overlay shadows (e.g. `Marker` shadows)\n      this.createPane('shadowPane');\n      // @pane markerPane: HTMLElement = 600\n      // Pane for `Icon`s of `Marker`s\n      this.createPane('markerPane');\n      // @pane tooltipPane: HTMLElement = 650\n      // Pane for `Tooltip`s.\n      this.createPane('tooltipPane');\n      // @pane popupPane: HTMLElement = 700\n      // Pane for `Popup`s.\n      this.createPane('popupPane');\n      if (!this.options.markerZoomAnimation) {\n        addClass(panes.markerPane, 'leaflet-zoom-hide');\n        addClass(panes.shadowPane, 'leaflet-zoom-hide');\n      }\n    },\n    // private methods that modify map state\n\n    // @section Map state change events\n    _resetView: function (center, zoom, noMoveStart) {\n      setPosition(this._mapPane, new Point(0, 0));\n      var loading = !this._loaded;\n      this._loaded = true;\n      zoom = this._limitZoom(zoom);\n      this.fire('viewprereset');\n      var zoomChanged = this._zoom !== zoom;\n      this._moveStart(zoomChanged, noMoveStart)._move(center, zoom)._moveEnd(zoomChanged);\n\n      // @event viewreset: Event\n      // Fired when the map needs to redraw its content (this usually happens\n      // on map zoom or load). Very useful for creating custom overlays.\n      this.fire('viewreset');\n\n      // @event load: Event\n      // Fired when the map is initialized (when its center and zoom are set\n      // for the first time).\n      if (loading) {\n        this.fire('load');\n      }\n    },\n    _moveStart: function (zoomChanged, noMoveStart) {\n      // @event zoomstart: Event\n      // Fired when the map zoom is about to change (e.g. before zoom animation).\n      // @event movestart: Event\n      // Fired when the view of the map starts changing (e.g. user starts dragging the map).\n      if (zoomChanged) {\n        this.fire('zoomstart');\n      }\n      if (!noMoveStart) {\n        this.fire('movestart');\n      }\n      return this;\n    },\n    _move: function (center, zoom, data, supressEvent) {\n      if (zoom === undefined) {\n        zoom = this._zoom;\n      }\n      var zoomChanged = this._zoom !== zoom;\n      this._zoom = zoom;\n      this._lastCenter = center;\n      this._pixelOrigin = this._getNewPixelOrigin(center);\n      if (!supressEvent) {\n        // @event zoom: Event\n        // Fired repeatedly during any change in zoom level,\n        // including zoom and fly animations.\n        if (zoomChanged || data && data.pinch) {\n          // Always fire 'zoom' if pinching because #3530\n          this.fire('zoom', data);\n        }\n\n        // @event move: Event\n        // Fired repeatedly during any movement of the map,\n        // including pan and fly animations.\n        this.fire('move', data);\n      } else if (data && data.pinch) {\n        // Always fire 'zoom' if pinching because #3530\n        this.fire('zoom', data);\n      }\n      return this;\n    },\n    _moveEnd: function (zoomChanged) {\n      // @event zoomend: Event\n      // Fired when the map zoom changed, after any animations.\n      if (zoomChanged) {\n        this.fire('zoomend');\n      }\n\n      // @event moveend: Event\n      // Fired when the center of the map stops changing\n      // (e.g. user stopped dragging the map or after non-centered zoom).\n      return this.fire('moveend');\n    },\n    _stop: function () {\n      cancelAnimFrame(this._flyToFrame);\n      if (this._panAnim) {\n        this._panAnim.stop();\n      }\n      return this;\n    },\n    _rawPanBy: function (offset) {\n      setPosition(this._mapPane, this._getMapPanePos().subtract(offset));\n    },\n    _getZoomSpan: function () {\n      return this.getMaxZoom() - this.getMinZoom();\n    },\n    _panInsideMaxBounds: function () {\n      if (!this._enforcingBounds) {\n        this.panInsideBounds(this.options.maxBounds);\n      }\n    },\n    _checkIfLoaded: function () {\n      if (!this._loaded) {\n        throw new Error('Set map center and zoom first.');\n      }\n    },\n    // DOM event handling\n\n    // @section Interaction events\n    _initEvents: function (remove) {\n      this._targets = {};\n      this._targets[stamp(this._container)] = this;\n      var onOff = remove ? off : on;\n\n      // @event click: MouseEvent\n      // Fired when the user clicks (or taps) the map.\n      // @event dblclick: MouseEvent\n      // Fired when the user double-clicks (or double-taps) the map.\n      // @event mousedown: MouseEvent\n      // Fired when the user pushes the mouse button on the map.\n      // @event mouseup: MouseEvent\n      // Fired when the user releases the mouse button on the map.\n      // @event mouseover: MouseEvent\n      // Fired when the mouse enters the map.\n      // @event mouseout: MouseEvent\n      // Fired when the mouse leaves the map.\n      // @event mousemove: MouseEvent\n      // Fired while the mouse moves over the map.\n      // @event contextmenu: MouseEvent\n      // Fired when the user pushes the right mouse button on the map, prevents\n      // default browser context menu from showing if there are listeners on\n      // this event. Also fired on mobile when the user holds a single touch\n      // for a second (also called long press).\n      // @event keypress: KeyboardEvent\n      // Fired when the user presses a key from the keyboard that produces a character value while the map is focused.\n      // @event keydown: KeyboardEvent\n      // Fired when the user presses a key from the keyboard while the map is focused. Unlike the `keypress` event,\n      // the `keydown` event is fired for keys that produce a character value and for keys\n      // that do not produce a character value.\n      // @event keyup: KeyboardEvent\n      // Fired when the user releases a key from the keyboard while the map is focused.\n      onOff(this._container, 'click dblclick mousedown mouseup ' + 'mouseover mouseout mousemove contextmenu keypress keydown keyup', this._handleDOMEvent, this);\n      if (this.options.trackResize) {\n        onOff(window, 'resize', this._onResize, this);\n      }\n      if (Browser.any3d && this.options.transform3DLimit) {\n        (remove ? this.off : this.on).call(this, 'moveend', this._onMoveEnd);\n      }\n    },\n    _onResize: function () {\n      cancelAnimFrame(this._resizeRequest);\n      this._resizeRequest = requestAnimFrame(function () {\n        this.invalidateSize({\n          debounceMoveend: true\n        });\n      }, this);\n    },\n    _onScroll: function () {\n      this._container.scrollTop = 0;\n      this._container.scrollLeft = 0;\n    },\n    _onMoveEnd: function () {\n      var pos = this._getMapPanePos();\n      if (Math.max(Math.abs(pos.x), Math.abs(pos.y)) >= this.options.transform3DLimit) {\n        // https://bugzilla.mozilla.org/show_bug.cgi?id=1203873 but Webkit also have\n        // a pixel offset on very high values, see: https://jsfiddle.net/dg6r5hhb/\n        this._resetView(this.getCenter(), this.getZoom());\n      }\n    },\n    _findEventTargets: function (e, type) {\n      var targets = [],\n        target,\n        isHover = type === 'mouseout' || type === 'mouseover',\n        src = e.target || e.srcElement,\n        dragging = false;\n      while (src) {\n        target = this._targets[stamp(src)];\n        if (target && (type === 'click' || type === 'preclick') && this._draggableMoved(target)) {\n          // Prevent firing click after you just dragged an object.\n          dragging = true;\n          break;\n        }\n        if (target && target.listens(type, true)) {\n          if (isHover && !isExternalTarget(src, e)) {\n            break;\n          }\n          targets.push(target);\n          if (isHover) {\n            break;\n          }\n        }\n        if (src === this._container) {\n          break;\n        }\n        src = src.parentNode;\n      }\n      if (!targets.length && !dragging && !isHover && this.listens(type, true)) {\n        targets = [this];\n      }\n      return targets;\n    },\n    _isClickDisabled: function (el) {\n      while (el && el !== this._container) {\n        if (el['_leaflet_disable_click']) {\n          return true;\n        }\n        el = el.parentNode;\n      }\n    },\n    _handleDOMEvent: function (e) {\n      var el = e.target || e.srcElement;\n      if (!this._loaded || el['_leaflet_disable_events'] || e.type === 'click' && this._isClickDisabled(el)) {\n        return;\n      }\n      var type = e.type;\n      if (type === 'mousedown') {\n        // prevents outline when clicking on keyboard-focusable element\n        preventOutline(el);\n      }\n      this._fireDOMEvent(e, type);\n    },\n    _mouseEvents: ['click', 'dblclick', 'mouseover', 'mouseout', 'contextmenu'],\n    _fireDOMEvent: function (e, type, canvasTargets) {\n      if (e.type === 'click') {\n        // Fire a synthetic 'preclick' event which propagates up (mainly for closing popups).\n        // @event preclick: MouseEvent\n        // Fired before mouse click on the map (sometimes useful when you\n        // want something to happen on click before any existing click\n        // handlers start running).\n        var synth = extend({}, e);\n        synth.type = 'preclick';\n        this._fireDOMEvent(synth, synth.type, canvasTargets);\n      }\n\n      // Find the layer the event is propagating from and its parents.\n      var targets = this._findEventTargets(e, type);\n      if (canvasTargets) {\n        var filtered = []; // pick only targets with listeners\n        for (var i = 0; i < canvasTargets.length; i++) {\n          if (canvasTargets[i].listens(type, true)) {\n            filtered.push(canvasTargets[i]);\n          }\n        }\n        targets = filtered.concat(targets);\n      }\n      if (!targets.length) {\n        return;\n      }\n      if (type === 'contextmenu') {\n        preventDefault(e);\n      }\n      var target = targets[0];\n      var data = {\n        originalEvent: e\n      };\n      if (e.type !== 'keypress' && e.type !== 'keydown' && e.type !== 'keyup') {\n        var isMarker = target.getLatLng && (!target._radius || target._radius <= 10);\n        data.containerPoint = isMarker ? this.latLngToContainerPoint(target.getLatLng()) : this.mouseEventToContainerPoint(e);\n        data.layerPoint = this.containerPointToLayerPoint(data.containerPoint);\n        data.latlng = isMarker ? target.getLatLng() : this.layerPointToLatLng(data.layerPoint);\n      }\n      for (i = 0; i < targets.length; i++) {\n        targets[i].fire(type, data, true);\n        if (data.originalEvent._stopped || targets[i].options.bubblingMouseEvents === false && indexOf(this._mouseEvents, type) !== -1) {\n          return;\n        }\n      }\n    },\n    _draggableMoved: function (obj) {\n      obj = obj.dragging && obj.dragging.enabled() ? obj : this;\n      return obj.dragging && obj.dragging.moved() || this.boxZoom && this.boxZoom.moved();\n    },\n    _clearHandlers: function () {\n      for (var i = 0, len = this._handlers.length; i < len; i++) {\n        this._handlers[i].disable();\n      }\n    },\n    // @section Other Methods\n\n    // @method whenReady(fn: Function, context?: Object): this\n    // Runs the given function `fn` when the map gets initialized with\n    // a view (center and zoom) and at least one layer, or immediately\n    // if it's already initialized, optionally passing a function context.\n    whenReady: function (callback, context) {\n      if (this._loaded) {\n        callback.call(context || this, {\n          target: this\n        });\n      } else {\n        this.on('load', callback, context);\n      }\n      return this;\n    },\n    // private methods for getting map state\n\n    _getMapPanePos: function () {\n      return getPosition(this._mapPane) || new Point(0, 0);\n    },\n    _moved: function () {\n      var pos = this._getMapPanePos();\n      return pos && !pos.equals([0, 0]);\n    },\n    _getTopLeftPoint: function (center, zoom) {\n      var pixelOrigin = center && zoom !== undefined ? this._getNewPixelOrigin(center, zoom) : this.getPixelOrigin();\n      return pixelOrigin.subtract(this._getMapPanePos());\n    },\n    _getNewPixelOrigin: function (center, zoom) {\n      var viewHalf = this.getSize()._divideBy(2);\n      return this.project(center, zoom)._subtract(viewHalf)._add(this._getMapPanePos())._round();\n    },\n    _latLngToNewLayerPoint: function (latlng, zoom, center) {\n      var topLeft = this._getNewPixelOrigin(center, zoom);\n      return this.project(latlng, zoom)._subtract(topLeft);\n    },\n    _latLngBoundsToNewLayerBounds: function (latLngBounds, zoom, center) {\n      var topLeft = this._getNewPixelOrigin(center, zoom);\n      return toBounds([this.project(latLngBounds.getSouthWest(), zoom)._subtract(topLeft), this.project(latLngBounds.getNorthWest(), zoom)._subtract(topLeft), this.project(latLngBounds.getSouthEast(), zoom)._subtract(topLeft), this.project(latLngBounds.getNorthEast(), zoom)._subtract(topLeft)]);\n    },\n    // layer point of the current center\n    _getCenterLayerPoint: function () {\n      return this.containerPointToLayerPoint(this.getSize()._divideBy(2));\n    },\n    // offset of the specified place to the current center in pixels\n    _getCenterOffset: function (latlng) {\n      return this.latLngToLayerPoint(latlng).subtract(this._getCenterLayerPoint());\n    },\n    // adjust center for view to get inside bounds\n    _limitCenter: function (center, zoom, bounds) {\n      if (!bounds) {\n        return center;\n      }\n      var centerPoint = this.project(center, zoom),\n        viewHalf = this.getSize().divideBy(2),\n        viewBounds = new Bounds(centerPoint.subtract(viewHalf), centerPoint.add(viewHalf)),\n        offset = this._getBoundsOffset(viewBounds, bounds, zoom);\n\n      // If offset is less than a pixel, ignore.\n      // This prevents unstable projections from getting into\n      // an infinite loop of tiny offsets.\n      if (Math.abs(offset.x) <= 1 && Math.abs(offset.y) <= 1) {\n        return center;\n      }\n      return this.unproject(centerPoint.add(offset), zoom);\n    },\n    // adjust offset for view to get inside bounds\n    _limitOffset: function (offset, bounds) {\n      if (!bounds) {\n        return offset;\n      }\n      var viewBounds = this.getPixelBounds(),\n        newBounds = new Bounds(viewBounds.min.add(offset), viewBounds.max.add(offset));\n      return offset.add(this._getBoundsOffset(newBounds, bounds));\n    },\n    // returns offset needed for pxBounds to get inside maxBounds at a specified zoom\n    _getBoundsOffset: function (pxBounds, maxBounds, zoom) {\n      var projectedMaxBounds = toBounds(this.project(maxBounds.getNorthEast(), zoom), this.project(maxBounds.getSouthWest(), zoom)),\n        minOffset = projectedMaxBounds.min.subtract(pxBounds.min),\n        maxOffset = projectedMaxBounds.max.subtract(pxBounds.max),\n        dx = this._rebound(minOffset.x, -maxOffset.x),\n        dy = this._rebound(minOffset.y, -maxOffset.y);\n      return new Point(dx, dy);\n    },\n    _rebound: function (left, right) {\n      return left + right > 0 ? Math.round(left - right) / 2 : Math.max(0, Math.ceil(left)) - Math.max(0, Math.floor(right));\n    },\n    _limitZoom: function (zoom) {\n      var min = this.getMinZoom(),\n        max = this.getMaxZoom(),\n        snap = Browser.any3d ? this.options.zoomSnap : 1;\n      if (snap) {\n        zoom = Math.round(zoom / snap) * snap;\n      }\n      return Math.max(min, Math.min(max, zoom));\n    },\n    _onPanTransitionStep: function () {\n      this.fire('move');\n    },\n    _onPanTransitionEnd: function () {\n      removeClass(this._mapPane, 'leaflet-pan-anim');\n      this.fire('moveend');\n    },\n    _tryAnimatedPan: function (center, options) {\n      // difference between the new and current centers in pixels\n      var offset = this._getCenterOffset(center)._trunc();\n\n      // don't animate too far unless animate: true specified in options\n      if ((options && options.animate) !== true && !this.getSize().contains(offset)) {\n        return false;\n      }\n      this.panBy(offset, options);\n      return true;\n    },\n    _createAnimProxy: function () {\n      var proxy = this._proxy = create$1('div', 'leaflet-proxy leaflet-zoom-animated');\n      this._panes.mapPane.appendChild(proxy);\n      this.on('zoomanim', function (e) {\n        var prop = TRANSFORM,\n          transform = this._proxy.style[prop];\n        setTransform(this._proxy, this.project(e.center, e.zoom), this.getZoomScale(e.zoom, 1));\n\n        // workaround for case when transform is the same and so transitionend event is not fired\n        if (transform === this._proxy.style[prop] && this._animatingZoom) {\n          this._onZoomTransitionEnd();\n        }\n      }, this);\n      this.on('load moveend', this._animMoveEnd, this);\n      this._on('unload', this._destroyAnimProxy, this);\n    },\n    _destroyAnimProxy: function () {\n      remove(this._proxy);\n      this.off('load moveend', this._animMoveEnd, this);\n      delete this._proxy;\n    },\n    _animMoveEnd: function () {\n      var c = this.getCenter(),\n        z = this.getZoom();\n      setTransform(this._proxy, this.project(c, z), this.getZoomScale(z, 1));\n    },\n    _catchTransitionEnd: function (e) {\n      if (this._animatingZoom && e.propertyName.indexOf('transform') >= 0) {\n        this._onZoomTransitionEnd();\n      }\n    },\n    _nothingToAnimate: function () {\n      return !this._container.getElementsByClassName('leaflet-zoom-animated').length;\n    },\n    _tryAnimatedZoom: function (center, zoom, options) {\n      if (this._animatingZoom) {\n        return true;\n      }\n      options = options || {};\n\n      // don't animate if disabled, not supported or zoom difference is too large\n      if (!this._zoomAnimated || options.animate === false || this._nothingToAnimate() || Math.abs(zoom - this._zoom) > this.options.zoomAnimationThreshold) {\n        return false;\n      }\n\n      // offset is the pixel coords of the zoom origin relative to the current center\n      var scale = this.getZoomScale(zoom),\n        offset = this._getCenterOffset(center)._divideBy(1 - 1 / scale);\n\n      // don't animate if the zoom origin isn't within one screen from the current center, unless forced\n      if (options.animate !== true && !this.getSize().contains(offset)) {\n        return false;\n      }\n      requestAnimFrame(function () {\n        this._moveStart(true, options.noMoveStart || false)._animateZoom(center, zoom, true);\n      }, this);\n      return true;\n    },\n    _animateZoom: function (center, zoom, startAnim, noUpdate) {\n      if (!this._mapPane) {\n        return;\n      }\n      if (startAnim) {\n        this._animatingZoom = true;\n\n        // remember what center/zoom to set after animation\n        this._animateToCenter = center;\n        this._animateToZoom = zoom;\n        addClass(this._mapPane, 'leaflet-zoom-anim');\n      }\n\n      // @section Other Events\n      // @event zoomanim: ZoomAnimEvent\n      // Fired at least once per zoom animation. For continuous zoom, like pinch zooming, fired once per frame during zoom.\n      this.fire('zoomanim', {\n        center: center,\n        zoom: zoom,\n        noUpdate: noUpdate\n      });\n      if (!this._tempFireZoomEvent) {\n        this._tempFireZoomEvent = this._zoom !== this._animateToZoom;\n      }\n      this._move(this._animateToCenter, this._animateToZoom, undefined, true);\n\n      // Work around webkit not firing 'transitionend', see https://github.com/Leaflet/Leaflet/issues/3689, 2693\n      setTimeout(bind(this._onZoomTransitionEnd, this), 250);\n    },\n    _onZoomTransitionEnd: function () {\n      if (!this._animatingZoom) {\n        return;\n      }\n      if (this._mapPane) {\n        removeClass(this._mapPane, 'leaflet-zoom-anim');\n      }\n      this._animatingZoom = false;\n      this._move(this._animateToCenter, this._animateToZoom, undefined, true);\n      if (this._tempFireZoomEvent) {\n        this.fire('zoom');\n      }\n      delete this._tempFireZoomEvent;\n      this.fire('move');\n      this._moveEnd(true);\n    }\n  });\n\n  // @section\n\n  // @factory L.map(id: String, options?: Map options)\n  // Instantiates a map object given the DOM ID of a `<div>` element\n  // and optionally an object literal with `Map options`.\n  //\n  // @alternative\n  // @factory L.map(el: HTMLElement, options?: Map options)\n  // Instantiates a map object given an instance of a `<div>` HTML element\n  // and optionally an object literal with `Map options`.\n  function createMap(id, options) {\n    return new Map(id, options);\n  }\n\n  /*\r\n   * @class Control\r\n   * @aka L.Control\r\n   * @inherits Class\r\n   *\r\n   * L.Control is a base class for implementing map controls. Handles positioning.\r\n   * All other controls extend from this class.\r\n   */\n\n  var Control = Class.extend({\n    // @section\n    // @aka Control Options\n    options: {\n      // @option position: String = 'topright'\n      // The position of the control (one of the map corners). Possible values are `'topleft'`,\n      // `'topright'`, `'bottomleft'` or `'bottomright'`\n      position: 'topright'\n    },\n    initialize: function (options) {\n      setOptions(this, options);\n    },\n    /* @section\r\n     * Classes extending L.Control will inherit the following methods:\r\n     *\r\n     * @method getPosition: string\r\n     * Returns the position of the control.\r\n     */\n    getPosition: function () {\n      return this.options.position;\n    },\n    // @method setPosition(position: string): this\n    // Sets the position of the control.\n    setPosition: function (position) {\n      var map = this._map;\n      if (map) {\n        map.removeControl(this);\n      }\n      this.options.position = position;\n      if (map) {\n        map.addControl(this);\n      }\n      return this;\n    },\n    // @method getContainer: HTMLElement\n    // Returns the HTMLElement that contains the control.\n    getContainer: function () {\n      return this._container;\n    },\n    // @method addTo(map: Map): this\n    // Adds the control to the given map.\n    addTo: function (map) {\n      this.remove();\n      this._map = map;\n      var container = this._container = this.onAdd(map),\n        pos = this.getPosition(),\n        corner = map._controlCorners[pos];\n      addClass(container, 'leaflet-control');\n      if (pos.indexOf('bottom') !== -1) {\n        corner.insertBefore(container, corner.firstChild);\n      } else {\n        corner.appendChild(container);\n      }\n      this._map.on('unload', this.remove, this);\n      return this;\n    },\n    // @method remove: this\n    // Removes the control from the map it is currently active on.\n    remove: function () {\n      if (!this._map) {\n        return this;\n      }\n      remove(this._container);\n      if (this.onRemove) {\n        this.onRemove(this._map);\n      }\n      this._map.off('unload', this.remove, this);\n      this._map = null;\n      return this;\n    },\n    _refocusOnMap: function (e) {\n      // if map exists and event is not a keyboard event\n      if (this._map && e && e.screenX > 0 && e.screenY > 0) {\n        this._map.getContainer().focus();\n      }\n    }\n  });\n  var control = function (options) {\n    return new Control(options);\n  };\n\n  /* @section Extension methods\r\n   * @uninheritable\r\n   *\r\n   * Every control should extend from `L.Control` and (re-)implement the following methods.\r\n   *\r\n   * @method onAdd(map: Map): HTMLElement\r\n   * Should return the container DOM element for the control and add listeners on relevant map events. Called on [`control.addTo(map)`](#control-addTo).\r\n   *\r\n   * @method onRemove(map: Map)\r\n   * Optional method. Should contain all clean up code that removes the listeners previously added in [`onAdd`](#control-onadd). Called on [`control.remove()`](#control-remove).\r\n   */\n\n  /* @namespace Map\r\n   * @section Methods for Layers and Controls\r\n   */\n  Map.include({\n    // @method addControl(control: Control): this\n    // Adds the given control to the map\n    addControl: function (control) {\n      control.addTo(this);\n      return this;\n    },\n    // @method removeControl(control: Control): this\n    // Removes the given control from the map\n    removeControl: function (control) {\n      control.remove();\n      return this;\n    },\n    _initControlPos: function () {\n      var corners = this._controlCorners = {},\n        l = 'leaflet-',\n        container = this._controlContainer = create$1('div', l + 'control-container', this._container);\n      function createCorner(vSide, hSide) {\n        var className = l + vSide + ' ' + l + hSide;\n        corners[vSide + hSide] = create$1('div', className, container);\n      }\n      createCorner('top', 'left');\n      createCorner('top', 'right');\n      createCorner('bottom', 'left');\n      createCorner('bottom', 'right');\n    },\n    _clearControlPos: function () {\n      for (var i in this._controlCorners) {\n        remove(this._controlCorners[i]);\n      }\n      remove(this._controlContainer);\n      delete this._controlCorners;\n      delete this._controlContainer;\n    }\n  });\n\n  /*\r\n   * @class Control.Layers\r\n   * @aka L.Control.Layers\r\n   * @inherits Control\r\n   *\r\n   * The layers control gives users the ability to switch between different base layers and switch overlays on/off (check out the [detailed example](https://leafletjs.com/examples/layers-control/)). Extends `Control`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var baseLayers = {\r\n   * \t\"Mapbox\": mapbox,\r\n   * \t\"OpenStreetMap\": osm\r\n   * };\r\n   *\r\n   * var overlays = {\r\n   * \t\"Marker\": marker,\r\n   * \t\"Roads\": roadsLayer\r\n   * };\r\n   *\r\n   * L.control.layers(baseLayers, overlays).addTo(map);\r\n   * ```\r\n   *\r\n   * The `baseLayers` and `overlays` parameters are object literals with layer names as keys and `Layer` objects as values:\r\n   *\r\n   * ```js\r\n   * {\r\n   *     \"<someName1>\": layer1,\r\n   *     \"<someName2>\": layer2\r\n   * }\r\n   * ```\r\n   *\r\n   * The layer names can contain HTML, which allows you to add additional styling to the items:\r\n   *\r\n   * ```js\r\n   * {\"<img src='my-layer-icon' /> <span class='my-layer-item'>My Layer</span>\": myLayer}\r\n   * ```\r\n   */\n\n  var Layers = Control.extend({\n    // @section\n    // @aka Control.Layers options\n    options: {\n      // @option collapsed: Boolean = true\n      // If `true`, the control will be collapsed into an icon and expanded on mouse hover, touch, or keyboard activation.\n      collapsed: true,\n      position: 'topright',\n      // @option autoZIndex: Boolean = true\n      // If `true`, the control will assign zIndexes in increasing order to all of its layers so that the order is preserved when switching them on/off.\n      autoZIndex: true,\n      // @option hideSingleBase: Boolean = false\n      // If `true`, the base layers in the control will be hidden when there is only one.\n      hideSingleBase: false,\n      // @option sortLayers: Boolean = false\n      // Whether to sort the layers. When `false`, layers will keep the order\n      // in which they were added to the control.\n      sortLayers: false,\n      // @option sortFunction: Function = *\n      // A [compare function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/sort)\n      // that will be used for sorting the layers, when `sortLayers` is `true`.\n      // The function receives both the `L.Layer` instances and their names, as in\n      // `sortFunction(layerA, layerB, nameA, nameB)`.\n      // By default, it sorts layers alphabetically by their name.\n      sortFunction: function (layerA, layerB, nameA, nameB) {\n        return nameA < nameB ? -1 : nameB < nameA ? 1 : 0;\n      }\n    },\n    initialize: function (baseLayers, overlays, options) {\n      setOptions(this, options);\n      this._layerControlInputs = [];\n      this._layers = [];\n      this._lastZIndex = 0;\n      this._handlingClick = false;\n      this._preventClick = false;\n      for (var i in baseLayers) {\n        this._addLayer(baseLayers[i], i);\n      }\n      for (i in overlays) {\n        this._addLayer(overlays[i], i, true);\n      }\n    },\n    onAdd: function (map) {\n      this._initLayout();\n      this._update();\n      this._map = map;\n      map.on('zoomend', this._checkDisabledLayers, this);\n      for (var i = 0; i < this._layers.length; i++) {\n        this._layers[i].layer.on('add remove', this._onLayerChange, this);\n      }\n      return this._container;\n    },\n    addTo: function (map) {\n      Control.prototype.addTo.call(this, map);\n      // Trigger expand after Layers Control has been inserted into DOM so that is now has an actual height.\n      return this._expandIfNotCollapsed();\n    },\n    onRemove: function () {\n      this._map.off('zoomend', this._checkDisabledLayers, this);\n      for (var i = 0; i < this._layers.length; i++) {\n        this._layers[i].layer.off('add remove', this._onLayerChange, this);\n      }\n    },\n    // @method addBaseLayer(layer: Layer, name: String): this\n    // Adds a base layer (radio button entry) with the given name to the control.\n    addBaseLayer: function (layer, name) {\n      this._addLayer(layer, name);\n      return this._map ? this._update() : this;\n    },\n    // @method addOverlay(layer: Layer, name: String): this\n    // Adds an overlay (checkbox entry) with the given name to the control.\n    addOverlay: function (layer, name) {\n      this._addLayer(layer, name, true);\n      return this._map ? this._update() : this;\n    },\n    // @method removeLayer(layer: Layer): this\n    // Remove the given layer from the control.\n    removeLayer: function (layer) {\n      layer.off('add remove', this._onLayerChange, this);\n      var obj = this._getLayer(stamp(layer));\n      if (obj) {\n        this._layers.splice(this._layers.indexOf(obj), 1);\n      }\n      return this._map ? this._update() : this;\n    },\n    // @method expand(): this\n    // Expand the control container if collapsed.\n    expand: function () {\n      addClass(this._container, 'leaflet-control-layers-expanded');\n      this._section.style.height = null;\n      var acceptableHeight = this._map.getSize().y - (this._container.offsetTop + 50);\n      if (acceptableHeight < this._section.clientHeight) {\n        addClass(this._section, 'leaflet-control-layers-scrollbar');\n        this._section.style.height = acceptableHeight + 'px';\n      } else {\n        removeClass(this._section, 'leaflet-control-layers-scrollbar');\n      }\n      this._checkDisabledLayers();\n      return this;\n    },\n    // @method collapse(): this\n    // Collapse the control container if expanded.\n    collapse: function () {\n      removeClass(this._container, 'leaflet-control-layers-expanded');\n      return this;\n    },\n    _initLayout: function () {\n      var className = 'leaflet-control-layers',\n        container = this._container = create$1('div', className),\n        collapsed = this.options.collapsed;\n\n      // makes this work on IE touch devices by stopping it from firing a mouseout event when the touch is released\n      container.setAttribute('aria-haspopup', true);\n      disableClickPropagation(container);\n      disableScrollPropagation(container);\n      var section = this._section = create$1('section', className + '-list');\n      if (collapsed) {\n        this._map.on('click', this.collapse, this);\n        on(container, {\n          mouseenter: this._expandSafely,\n          mouseleave: this.collapse\n        }, this);\n      }\n      var link = this._layersLink = create$1('a', className + '-toggle', container);\n      link.href = '#';\n      link.title = 'Layers';\n      link.setAttribute('role', 'button');\n      on(link, {\n        keydown: function (e) {\n          if (e.keyCode === 13) {\n            this._expandSafely();\n          }\n        },\n        // Certain screen readers intercept the key event and instead send a click event\n        click: function (e) {\n          preventDefault(e);\n          this._expandSafely();\n        }\n      }, this);\n      if (!collapsed) {\n        this.expand();\n      }\n      this._baseLayersList = create$1('div', className + '-base', section);\n      this._separator = create$1('div', className + '-separator', section);\n      this._overlaysList = create$1('div', className + '-overlays', section);\n      container.appendChild(section);\n    },\n    _getLayer: function (id) {\n      for (var i = 0; i < this._layers.length; i++) {\n        if (this._layers[i] && stamp(this._layers[i].layer) === id) {\n          return this._layers[i];\n        }\n      }\n    },\n    _addLayer: function (layer, name, overlay) {\n      if (this._map) {\n        layer.on('add remove', this._onLayerChange, this);\n      }\n      this._layers.push({\n        layer: layer,\n        name: name,\n        overlay: overlay\n      });\n      if (this.options.sortLayers) {\n        this._layers.sort(bind(function (a, b) {\n          return this.options.sortFunction(a.layer, b.layer, a.name, b.name);\n        }, this));\n      }\n      if (this.options.autoZIndex && layer.setZIndex) {\n        this._lastZIndex++;\n        layer.setZIndex(this._lastZIndex);\n      }\n      this._expandIfNotCollapsed();\n    },\n    _update: function () {\n      if (!this._container) {\n        return this;\n      }\n      empty(this._baseLayersList);\n      empty(this._overlaysList);\n      this._layerControlInputs = [];\n      var baseLayersPresent,\n        overlaysPresent,\n        i,\n        obj,\n        baseLayersCount = 0;\n      for (i = 0; i < this._layers.length; i++) {\n        obj = this._layers[i];\n        this._addItem(obj);\n        overlaysPresent = overlaysPresent || obj.overlay;\n        baseLayersPresent = baseLayersPresent || !obj.overlay;\n        baseLayersCount += !obj.overlay ? 1 : 0;\n      }\n\n      // Hide base layers section if there's only one layer.\n      if (this.options.hideSingleBase) {\n        baseLayersPresent = baseLayersPresent && baseLayersCount > 1;\n        this._baseLayersList.style.display = baseLayersPresent ? '' : 'none';\n      }\n      this._separator.style.display = overlaysPresent && baseLayersPresent ? '' : 'none';\n      return this;\n    },\n    _onLayerChange: function (e) {\n      if (!this._handlingClick) {\n        this._update();\n      }\n      var obj = this._getLayer(stamp(e.target));\n\n      // @namespace Map\n      // @section Layer events\n      // @event baselayerchange: LayersControlEvent\n      // Fired when the base layer is changed through the [layers control](#control-layers).\n      // @event overlayadd: LayersControlEvent\n      // Fired when an overlay is selected through the [layers control](#control-layers).\n      // @event overlayremove: LayersControlEvent\n      // Fired when an overlay is deselected through the [layers control](#control-layers).\n      // @namespace Control.Layers\n      var type = obj.overlay ? e.type === 'add' ? 'overlayadd' : 'overlayremove' : e.type === 'add' ? 'baselayerchange' : null;\n      if (type) {\n        this._map.fire(type, obj);\n      }\n    },\n    // IE7 bugs out if you create a radio dynamically, so you have to do it this hacky way (see https://stackoverflow.com/a/119079)\n    _createRadioElement: function (name, checked) {\n      var radioHtml = '<input type=\"radio\" class=\"leaflet-control-layers-selector\" name=\"' + name + '\"' + (checked ? ' checked=\"checked\"' : '') + '/>';\n      var radioFragment = document.createElement('div');\n      radioFragment.innerHTML = radioHtml;\n      return radioFragment.firstChild;\n    },\n    _addItem: function (obj) {\n      var label = document.createElement('label'),\n        checked = this._map.hasLayer(obj.layer),\n        input;\n      if (obj.overlay) {\n        input = document.createElement('input');\n        input.type = 'checkbox';\n        input.className = 'leaflet-control-layers-selector';\n        input.defaultChecked = checked;\n      } else {\n        input = this._createRadioElement('leaflet-base-layers_' + stamp(this), checked);\n      }\n      this._layerControlInputs.push(input);\n      input.layerId = stamp(obj.layer);\n      on(input, 'click', this._onInputClick, this);\n      var name = document.createElement('span');\n      name.innerHTML = ' ' + obj.name;\n\n      // Helps from preventing layer control flicker when checkboxes are disabled\n      // https://github.com/Leaflet/Leaflet/issues/2771\n      var holder = document.createElement('span');\n      label.appendChild(holder);\n      holder.appendChild(input);\n      holder.appendChild(name);\n      var container = obj.overlay ? this._overlaysList : this._baseLayersList;\n      container.appendChild(label);\n      this._checkDisabledLayers();\n      return label;\n    },\n    _onInputClick: function () {\n      // expanding the control on mobile with a click can cause adding a layer - we don't want this\n      if (this._preventClick) {\n        return;\n      }\n      var inputs = this._layerControlInputs,\n        input,\n        layer;\n      var addedLayers = [],\n        removedLayers = [];\n      this._handlingClick = true;\n      for (var i = inputs.length - 1; i >= 0; i--) {\n        input = inputs[i];\n        layer = this._getLayer(input.layerId).layer;\n        if (input.checked) {\n          addedLayers.push(layer);\n        } else if (!input.checked) {\n          removedLayers.push(layer);\n        }\n      }\n\n      // Bugfix issue 2318: Should remove all old layers before readding new ones\n      for (i = 0; i < removedLayers.length; i++) {\n        if (this._map.hasLayer(removedLayers[i])) {\n          this._map.removeLayer(removedLayers[i]);\n        }\n      }\n      for (i = 0; i < addedLayers.length; i++) {\n        if (!this._map.hasLayer(addedLayers[i])) {\n          this._map.addLayer(addedLayers[i]);\n        }\n      }\n      this._handlingClick = false;\n      this._refocusOnMap();\n    },\n    _checkDisabledLayers: function () {\n      var inputs = this._layerControlInputs,\n        input,\n        layer,\n        zoom = this._map.getZoom();\n      for (var i = inputs.length - 1; i >= 0; i--) {\n        input = inputs[i];\n        layer = this._getLayer(input.layerId).layer;\n        input.disabled = layer.options.minZoom !== undefined && zoom < layer.options.minZoom || layer.options.maxZoom !== undefined && zoom > layer.options.maxZoom;\n      }\n    },\n    _expandIfNotCollapsed: function () {\n      if (this._map && !this.options.collapsed) {\n        this.expand();\n      }\n      return this;\n    },\n    _expandSafely: function () {\n      var section = this._section;\n      this._preventClick = true;\n      on(section, 'click', preventDefault);\n      this.expand();\n      var that = this;\n      setTimeout(function () {\n        off(section, 'click', preventDefault);\n        that._preventClick = false;\n      });\n    }\n  });\n\n  // @factory L.control.layers(baselayers?: Object, overlays?: Object, options?: Control.Layers options)\n  // Creates a layers control with the given layers. Base layers will be switched with radio buttons, while overlays will be switched with checkboxes. Note that all base layers should be passed in the base layers object, but only one should be added to the map during map instantiation.\n  var layers = function (baseLayers, overlays, options) {\n    return new Layers(baseLayers, overlays, options);\n  };\n\n  /*\r\n   * @class Control.Zoom\r\n   * @aka L.Control.Zoom\r\n   * @inherits Control\r\n   *\r\n   * A basic zoom control with two buttons (zoom in and zoom out). It is put on the map by default unless you set its [`zoomControl` option](#map-zoomcontrol) to `false`. Extends `Control`.\r\n   */\n\n  var Zoom = Control.extend({\n    // @section\n    // @aka Control.Zoom options\n    options: {\n      position: 'topleft',\n      // @option zoomInText: String = '<span aria-hidden=\"true\">+</span>'\n      // The text set on the 'zoom in' button.\n      zoomInText: '<span aria-hidden=\"true\">+</span>',\n      // @option zoomInTitle: String = 'Zoom in'\n      // The title set on the 'zoom in' button.\n      zoomInTitle: 'Zoom in',\n      // @option zoomOutText: String = '<span aria-hidden=\"true\">&#x2212;</span>'\n      // The text set on the 'zoom out' button.\n      zoomOutText: '<span aria-hidden=\"true\">&#x2212;</span>',\n      // @option zoomOutTitle: String = 'Zoom out'\n      // The title set on the 'zoom out' button.\n      zoomOutTitle: 'Zoom out'\n    },\n    onAdd: function (map) {\n      var zoomName = 'leaflet-control-zoom',\n        container = create$1('div', zoomName + ' leaflet-bar'),\n        options = this.options;\n      this._zoomInButton = this._createButton(options.zoomInText, options.zoomInTitle, zoomName + '-in', container, this._zoomIn);\n      this._zoomOutButton = this._createButton(options.zoomOutText, options.zoomOutTitle, zoomName + '-out', container, this._zoomOut);\n      this._updateDisabled();\n      map.on('zoomend zoomlevelschange', this._updateDisabled, this);\n      return container;\n    },\n    onRemove: function (map) {\n      map.off('zoomend zoomlevelschange', this._updateDisabled, this);\n    },\n    disable: function () {\n      this._disabled = true;\n      this._updateDisabled();\n      return this;\n    },\n    enable: function () {\n      this._disabled = false;\n      this._updateDisabled();\n      return this;\n    },\n    _zoomIn: function (e) {\n      if (!this._disabled && this._map._zoom < this._map.getMaxZoom()) {\n        this._map.zoomIn(this._map.options.zoomDelta * (e.shiftKey ? 3 : 1));\n      }\n    },\n    _zoomOut: function (e) {\n      if (!this._disabled && this._map._zoom > this._map.getMinZoom()) {\n        this._map.zoomOut(this._map.options.zoomDelta * (e.shiftKey ? 3 : 1));\n      }\n    },\n    _createButton: function (html, title, className, container, fn) {\n      var link = create$1('a', className, container);\n      link.innerHTML = html;\n      link.href = '#';\n      link.title = title;\n\n      /*\r\n       * Will force screen readers like VoiceOver to read this as \"Zoom in - button\"\r\n       */\n      link.setAttribute('role', 'button');\n      link.setAttribute('aria-label', title);\n      disableClickPropagation(link);\n      on(link, 'click', stop);\n      on(link, 'click', fn, this);\n      on(link, 'click', this._refocusOnMap, this);\n      return link;\n    },\n    _updateDisabled: function () {\n      var map = this._map,\n        className = 'leaflet-disabled';\n      removeClass(this._zoomInButton, className);\n      removeClass(this._zoomOutButton, className);\n      this._zoomInButton.setAttribute('aria-disabled', 'false');\n      this._zoomOutButton.setAttribute('aria-disabled', 'false');\n      if (this._disabled || map._zoom === map.getMinZoom()) {\n        addClass(this._zoomOutButton, className);\n        this._zoomOutButton.setAttribute('aria-disabled', 'true');\n      }\n      if (this._disabled || map._zoom === map.getMaxZoom()) {\n        addClass(this._zoomInButton, className);\n        this._zoomInButton.setAttribute('aria-disabled', 'true');\n      }\n    }\n  });\n\n  // @namespace Map\n  // @section Control options\n  // @option zoomControl: Boolean = true\n  // Whether a [zoom control](#control-zoom) is added to the map by default.\n  Map.mergeOptions({\n    zoomControl: true\n  });\n  Map.addInitHook(function () {\n    if (this.options.zoomControl) {\n      // @section Controls\n      // @property zoomControl: Control.Zoom\n      // The default zoom control (only available if the\n      // [`zoomControl` option](#map-zoomcontrol) was `true` when creating the map).\n      this.zoomControl = new Zoom();\n      this.addControl(this.zoomControl);\n    }\n  });\n\n  // @namespace Control.Zoom\n  // @factory L.control.zoom(options: Control.Zoom options)\n  // Creates a zoom control\n  var zoom = function (options) {\n    return new Zoom(options);\n  };\n\n  /*\n   * @class Control.Scale\n   * @aka L.Control.Scale\n   * @inherits Control\n   *\n   * A simple scale control that shows the scale of the current center of screen in metric (m/km) and imperial (mi/ft) systems. Extends `Control`.\n   *\n   * @example\n   *\n   * ```js\n   * L.control.scale().addTo(map);\n   * ```\n   */\n\n  var Scale = Control.extend({\n    // @section\n    // @aka Control.Scale options\n    options: {\n      position: 'bottomleft',\n      // @option maxWidth: Number = 100\n      // Maximum width of the control in pixels. The width is set dynamically to show round values (e.g. 100, 200, 500).\n      maxWidth: 100,\n      // @option metric: Boolean = True\n      // Whether to show the metric scale line (m/km).\n      metric: true,\n      // @option imperial: Boolean = True\n      // Whether to show the imperial scale line (mi/ft).\n      imperial: true\n\n      // @option updateWhenIdle: Boolean = false\n      // If `true`, the control is updated on [`moveend`](#map-moveend), otherwise it's always up-to-date (updated on [`move`](#map-move)).\n    },\n\n    onAdd: function (map) {\n      var className = 'leaflet-control-scale',\n        container = create$1('div', className),\n        options = this.options;\n      this._addScales(options, className + '-line', container);\n      map.on(options.updateWhenIdle ? 'moveend' : 'move', this._update, this);\n      map.whenReady(this._update, this);\n      return container;\n    },\n    onRemove: function (map) {\n      map.off(this.options.updateWhenIdle ? 'moveend' : 'move', this._update, this);\n    },\n    _addScales: function (options, className, container) {\n      if (options.metric) {\n        this._mScale = create$1('div', className, container);\n      }\n      if (options.imperial) {\n        this._iScale = create$1('div', className, container);\n      }\n    },\n    _update: function () {\n      var map = this._map,\n        y = map.getSize().y / 2;\n      var maxMeters = map.distance(map.containerPointToLatLng([0, y]), map.containerPointToLatLng([this.options.maxWidth, y]));\n      this._updateScales(maxMeters);\n    },\n    _updateScales: function (maxMeters) {\n      if (this.options.metric && maxMeters) {\n        this._updateMetric(maxMeters);\n      }\n      if (this.options.imperial && maxMeters) {\n        this._updateImperial(maxMeters);\n      }\n    },\n    _updateMetric: function (maxMeters) {\n      var meters = this._getRoundNum(maxMeters),\n        label = meters < 1000 ? meters + ' m' : meters / 1000 + ' km';\n      this._updateScale(this._mScale, label, meters / maxMeters);\n    },\n    _updateImperial: function (maxMeters) {\n      var maxFeet = maxMeters * 3.2808399,\n        maxMiles,\n        miles,\n        feet;\n      if (maxFeet > 5280) {\n        maxMiles = maxFeet / 5280;\n        miles = this._getRoundNum(maxMiles);\n        this._updateScale(this._iScale, miles + ' mi', miles / maxMiles);\n      } else {\n        feet = this._getRoundNum(maxFeet);\n        this._updateScale(this._iScale, feet + ' ft', feet / maxFeet);\n      }\n    },\n    _updateScale: function (scale, text, ratio) {\n      scale.style.width = Math.round(this.options.maxWidth * ratio) + 'px';\n      scale.innerHTML = text;\n    },\n    _getRoundNum: function (num) {\n      var pow10 = Math.pow(10, (Math.floor(num) + '').length - 1),\n        d = num / pow10;\n      d = d >= 10 ? 10 : d >= 5 ? 5 : d >= 3 ? 3 : d >= 2 ? 2 : 1;\n      return pow10 * d;\n    }\n  });\n\n  // @factory L.control.scale(options?: Control.Scale options)\n  // Creates an scale control with the given options.\n  var scale = function (options) {\n    return new Scale(options);\n  };\n  var ukrainianFlag = '<svg aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" class=\"leaflet-attribution-flag\"><path fill=\"#4C7BE1\" d=\"M0 0h12v4H0z\"/><path fill=\"#FFD500\" d=\"M0 4h12v3H0z\"/><path fill=\"#E0BC00\" d=\"M0 7h12v1H0z\"/></svg>';\n\n  /*\r\n   * @class Control.Attribution\r\n   * @aka L.Control.Attribution\r\n   * @inherits Control\r\n   *\r\n   * The attribution control allows you to display attribution data in a small text box on a map. It is put on the map by default unless you set its [`attributionControl` option](#map-attributioncontrol) to `false`, and it fetches attribution texts from layers with the [`getAttribution` method](#layer-getattribution) automatically. Extends Control.\r\n   */\n\n  var Attribution = Control.extend({\n    // @section\n    // @aka Control.Attribution options\n    options: {\n      position: 'bottomright',\n      // @option prefix: String|false = 'Leaflet'\n      // The HTML text shown before the attributions. Pass `false` to disable.\n      prefix: '<a href=\"https://leafletjs.com\" title=\"A JavaScript library for interactive maps\">' + (Browser.inlineSvg ? ukrainianFlag + ' ' : '') + 'Leaflet</a>'\n    },\n    initialize: function (options) {\n      setOptions(this, options);\n      this._attributions = {};\n    },\n    onAdd: function (map) {\n      map.attributionControl = this;\n      this._container = create$1('div', 'leaflet-control-attribution');\n      disableClickPropagation(this._container);\n\n      // TODO ugly, refactor\n      for (var i in map._layers) {\n        if (map._layers[i].getAttribution) {\n          this.addAttribution(map._layers[i].getAttribution());\n        }\n      }\n      this._update();\n      map.on('layeradd', this._addAttribution, this);\n      return this._container;\n    },\n    onRemove: function (map) {\n      map.off('layeradd', this._addAttribution, this);\n    },\n    _addAttribution: function (ev) {\n      if (ev.layer.getAttribution) {\n        this.addAttribution(ev.layer.getAttribution());\n        ev.layer.once('remove', function () {\n          this.removeAttribution(ev.layer.getAttribution());\n        }, this);\n      }\n    },\n    // @method setPrefix(prefix: String|false): this\n    // The HTML text shown before the attributions. Pass `false` to disable.\n    setPrefix: function (prefix) {\n      this.options.prefix = prefix;\n      this._update();\n      return this;\n    },\n    // @method addAttribution(text: String): this\n    // Adds an attribution text (e.g. `'&copy; OpenStreetMap contributors'`).\n    addAttribution: function (text) {\n      if (!text) {\n        return this;\n      }\n      if (!this._attributions[text]) {\n        this._attributions[text] = 0;\n      }\n      this._attributions[text]++;\n      this._update();\n      return this;\n    },\n    // @method removeAttribution(text: String): this\n    // Removes an attribution text.\n    removeAttribution: function (text) {\n      if (!text) {\n        return this;\n      }\n      if (this._attributions[text]) {\n        this._attributions[text]--;\n        this._update();\n      }\n      return this;\n    },\n    _update: function () {\n      if (!this._map) {\n        return;\n      }\n      var attribs = [];\n      for (var i in this._attributions) {\n        if (this._attributions[i]) {\n          attribs.push(i);\n        }\n      }\n      var prefixAndAttribs = [];\n      if (this.options.prefix) {\n        prefixAndAttribs.push(this.options.prefix);\n      }\n      if (attribs.length) {\n        prefixAndAttribs.push(attribs.join(', '));\n      }\n      this._container.innerHTML = prefixAndAttribs.join(' <span aria-hidden=\"true\">|</span> ');\n    }\n  });\n\n  // @namespace Map\n  // @section Control options\n  // @option attributionControl: Boolean = true\n  // Whether a [attribution control](#control-attribution) is added to the map by default.\n  Map.mergeOptions({\n    attributionControl: true\n  });\n  Map.addInitHook(function () {\n    if (this.options.attributionControl) {\n      new Attribution().addTo(this);\n    }\n  });\n\n  // @namespace Control.Attribution\n  // @factory L.control.attribution(options: Control.Attribution options)\n  // Creates an attribution control.\n  var attribution = function (options) {\n    return new Attribution(options);\n  };\n  Control.Layers = Layers;\n  Control.Zoom = Zoom;\n  Control.Scale = Scale;\n  Control.Attribution = Attribution;\n  control.layers = layers;\n  control.zoom = zoom;\n  control.scale = scale;\n  control.attribution = attribution;\n\n  /*\n  \tL.Handler is a base class for handler classes that are used internally to inject\n  \tinteraction features like dragging to classes like Map and Marker.\n  */\n\n  // @class Handler\n  // @aka L.Handler\n  // Abstract class for map interaction handlers\n\n  var Handler = Class.extend({\n    initialize: function (map) {\n      this._map = map;\n    },\n    // @method enable(): this\n    // Enables the handler\n    enable: function () {\n      if (this._enabled) {\n        return this;\n      }\n      this._enabled = true;\n      this.addHooks();\n      return this;\n    },\n    // @method disable(): this\n    // Disables the handler\n    disable: function () {\n      if (!this._enabled) {\n        return this;\n      }\n      this._enabled = false;\n      this.removeHooks();\n      return this;\n    },\n    // @method enabled(): Boolean\n    // Returns `true` if the handler is enabled\n    enabled: function () {\n      return !!this._enabled;\n    }\n\n    // @section Extension methods\n    // Classes inheriting from `Handler` must implement the two following methods:\n    // @method addHooks()\n    // Called when the handler is enabled, should add event hooks.\n    // @method removeHooks()\n    // Called when the handler is disabled, should remove the event hooks added previously.\n  });\n\n  // @section There is static function which can be called without instantiating L.Handler:\n  // @function addTo(map: Map, name: String): this\n  // Adds a new Handler to the given map with the given name.\n  Handler.addTo = function (map, name) {\n    map.addHandler(name, this);\n    return this;\n  };\n  var Mixin = {\n    Events: Events\n  };\n\n  /*\r\n   * @class Draggable\r\n   * @aka L.Draggable\r\n   * @inherits Evented\r\n   *\r\n   * A class for making DOM elements draggable (including touch support).\r\n   * Used internally for map and marker dragging. Only works for elements\r\n   * that were positioned with [`L.DomUtil.setPosition`](#domutil-setposition).\r\n   *\r\n   * @example\r\n   * ```js\r\n   * var draggable = new L.Draggable(elementToDrag);\r\n   * draggable.enable();\r\n   * ```\r\n   */\n\n  var START = Browser.touch ? 'touchstart mousedown' : 'mousedown';\n  var Draggable = Evented.extend({\n    options: {\n      // @section\n      // @aka Draggable options\n      // @option clickTolerance: Number = 3\n      // The max number of pixels a user can shift the mouse pointer during a click\n      // for it to be considered a valid click (as opposed to a mouse drag).\n      clickTolerance: 3\n    },\n    // @constructor L.Draggable(el: HTMLElement, dragHandle?: HTMLElement, preventOutline?: Boolean, options?: Draggable options)\n    // Creates a `Draggable` object for moving `el` when you start dragging the `dragHandle` element (equals `el` itself by default).\n    initialize: function (element, dragStartTarget, preventOutline, options) {\n      setOptions(this, options);\n      this._element = element;\n      this._dragStartTarget = dragStartTarget || element;\n      this._preventOutline = preventOutline;\n    },\n    // @method enable()\n    // Enables the dragging ability\n    enable: function () {\n      if (this._enabled) {\n        return;\n      }\n      on(this._dragStartTarget, START, this._onDown, this);\n      this._enabled = true;\n    },\n    // @method disable()\n    // Disables the dragging ability\n    disable: function () {\n      if (!this._enabled) {\n        return;\n      }\n\n      // If we're currently dragging this draggable,\n      // disabling it counts as first ending the drag.\n      if (Draggable._dragging === this) {\n        this.finishDrag(true);\n      }\n      off(this._dragStartTarget, START, this._onDown, this);\n      this._enabled = false;\n      this._moved = false;\n    },\n    _onDown: function (e) {\n      // Ignore the event if disabled; this happens in IE11\n      // under some circumstances, see #3666.\n      if (!this._enabled) {\n        return;\n      }\n      this._moved = false;\n      if (hasClass(this._element, 'leaflet-zoom-anim')) {\n        return;\n      }\n      if (e.touches && e.touches.length !== 1) {\n        // Finish dragging to avoid conflict with touchZoom\n        if (Draggable._dragging === this) {\n          this.finishDrag();\n        }\n        return;\n      }\n      if (Draggable._dragging || e.shiftKey || e.which !== 1 && e.button !== 1 && !e.touches) {\n        return;\n      }\n      Draggable._dragging = this; // Prevent dragging multiple objects at once.\n\n      if (this._preventOutline) {\n        preventOutline(this._element);\n      }\n      disableImageDrag();\n      disableTextSelection();\n      if (this._moving) {\n        return;\n      }\n\n      // @event down: Event\n      // Fired when a drag is about to start.\n      this.fire('down');\n      var first = e.touches ? e.touches[0] : e,\n        sizedParent = getSizedParentNode(this._element);\n      this._startPoint = new Point(first.clientX, first.clientY);\n      this._startPos = getPosition(this._element);\n\n      // Cache the scale, so that we can continuously compensate for it during drag (_onMove).\n      this._parentScale = getScale(sizedParent);\n      var mouseevent = e.type === 'mousedown';\n      on(document, mouseevent ? 'mousemove' : 'touchmove', this._onMove, this);\n      on(document, mouseevent ? 'mouseup' : 'touchend touchcancel', this._onUp, this);\n    },\n    _onMove: function (e) {\n      // Ignore the event if disabled; this happens in IE11\n      // under some circumstances, see #3666.\n      if (!this._enabled) {\n        return;\n      }\n      if (e.touches && e.touches.length > 1) {\n        this._moved = true;\n        return;\n      }\n      var first = e.touches && e.touches.length === 1 ? e.touches[0] : e,\n        offset = new Point(first.clientX, first.clientY)._subtract(this._startPoint);\n      if (!offset.x && !offset.y) {\n        return;\n      }\n      if (Math.abs(offset.x) + Math.abs(offset.y) < this.options.clickTolerance) {\n        return;\n      }\n\n      // We assume that the parent container's position, border and scale do not change for the duration of the drag.\n      // Therefore there is no need to account for the position and border (they are eliminated by the subtraction)\n      // and we can use the cached value for the scale.\n      offset.x /= this._parentScale.x;\n      offset.y /= this._parentScale.y;\n      preventDefault(e);\n      if (!this._moved) {\n        // @event dragstart: Event\n        // Fired when a drag starts\n        this.fire('dragstart');\n        this._moved = true;\n        addClass(document.body, 'leaflet-dragging');\n        this._lastTarget = e.target || e.srcElement;\n        // IE and Edge do not give the <use> element, so fetch it\n        // if necessary\n        if (window.SVGElementInstance && this._lastTarget instanceof window.SVGElementInstance) {\n          this._lastTarget = this._lastTarget.correspondingUseElement;\n        }\n        addClass(this._lastTarget, 'leaflet-drag-target');\n      }\n      this._newPos = this._startPos.add(offset);\n      this._moving = true;\n      this._lastEvent = e;\n      this._updatePosition();\n    },\n    _updatePosition: function () {\n      var e = {\n        originalEvent: this._lastEvent\n      };\n\n      // @event predrag: Event\n      // Fired continuously during dragging *before* each corresponding\n      // update of the element's position.\n      this.fire('predrag', e);\n      setPosition(this._element, this._newPos);\n\n      // @event drag: Event\n      // Fired continuously during dragging.\n      this.fire('drag', e);\n    },\n    _onUp: function () {\n      // Ignore the event if disabled; this happens in IE11\n      // under some circumstances, see #3666.\n      if (!this._enabled) {\n        return;\n      }\n      this.finishDrag();\n    },\n    finishDrag: function (noInertia) {\n      removeClass(document.body, 'leaflet-dragging');\n      if (this._lastTarget) {\n        removeClass(this._lastTarget, 'leaflet-drag-target');\n        this._lastTarget = null;\n      }\n      off(document, 'mousemove touchmove', this._onMove, this);\n      off(document, 'mouseup touchend touchcancel', this._onUp, this);\n      enableImageDrag();\n      enableTextSelection();\n      var fireDragend = this._moved && this._moving;\n      this._moving = false;\n      Draggable._dragging = false;\n      if (fireDragend) {\n        // @event dragend: DragEndEvent\n        // Fired when the drag ends.\n        this.fire('dragend', {\n          noInertia: noInertia,\n          distance: this._newPos.distanceTo(this._startPos)\n        });\n      }\n    }\n  });\n\n  /*\r\n   * @namespace PolyUtil\r\n   * Various utility functions for polygon geometries.\r\n   */\n\n  /* @function clipPolygon(points: Point[], bounds: Bounds, round?: Boolean): Point[]\r\n   * Clips the polygon geometry defined by the given `points` by the given bounds (using the [Sutherland-Hodgman algorithm](https://en.wikipedia.org/wiki/Sutherland%E2%80%93Hodgman_algorithm)).\r\n   * Used by Leaflet to only show polygon points that are on the screen or near, increasing\r\n   * performance. Note that polygon points needs different algorithm for clipping\r\n   * than polyline, so there's a separate method for it.\r\n   */\n  function clipPolygon(points, bounds, round) {\n    var clippedPoints,\n      edges = [1, 4, 2, 8],\n      i,\n      j,\n      k,\n      a,\n      b,\n      len,\n      edge,\n      p;\n    for (i = 0, len = points.length; i < len; i++) {\n      points[i]._code = _getBitCode(points[i], bounds);\n    }\n\n    // for each edge (left, bottom, right, top)\n    for (k = 0; k < 4; k++) {\n      edge = edges[k];\n      clippedPoints = [];\n      for (i = 0, len = points.length, j = len - 1; i < len; j = i++) {\n        a = points[i];\n        b = points[j];\n\n        // if a is inside the clip window\n        if (!(a._code & edge)) {\n          // if b is outside the clip window (a->b goes out of screen)\n          if (b._code & edge) {\n            p = _getEdgeIntersection(b, a, edge, bounds, round);\n            p._code = _getBitCode(p, bounds);\n            clippedPoints.push(p);\n          }\n          clippedPoints.push(a);\n\n          // else if b is inside the clip window (a->b enters the screen)\n        } else if (!(b._code & edge)) {\n          p = _getEdgeIntersection(b, a, edge, bounds, round);\n          p._code = _getBitCode(p, bounds);\n          clippedPoints.push(p);\n        }\n      }\n      points = clippedPoints;\n    }\n    return points;\n  }\n\n  /* @function polygonCenter(latlngs: LatLng[], crs: CRS): LatLng\r\n   * Returns the center ([centroid](http://en.wikipedia.org/wiki/Centroid)) of the passed LatLngs (first ring) from a polygon.\r\n   */\n  function polygonCenter(latlngs, crs) {\n    var i, j, p1, p2, f, area, x, y, center;\n    if (!latlngs || latlngs.length === 0) {\n      throw new Error('latlngs not passed');\n    }\n    if (!isFlat(latlngs)) {\n      console.warn('latlngs are not flat! Only the first ring will be used');\n      latlngs = latlngs[0];\n    }\n    var centroidLatLng = toLatLng([0, 0]);\n    var bounds = toLatLngBounds(latlngs);\n    var areaBounds = bounds.getNorthWest().distanceTo(bounds.getSouthWest()) * bounds.getNorthEast().distanceTo(bounds.getNorthWest());\n    // tests showed that below 1700 rounding errors are happening\n    if (areaBounds < 1700) {\n      // getting a inexact center, to move the latlngs near to [0, 0] to prevent rounding errors\n      centroidLatLng = centroid(latlngs);\n    }\n    var len = latlngs.length;\n    var points = [];\n    for (i = 0; i < len; i++) {\n      var latlng = toLatLng(latlngs[i]);\n      points.push(crs.project(toLatLng([latlng.lat - centroidLatLng.lat, latlng.lng - centroidLatLng.lng])));\n    }\n    area = x = y = 0;\n\n    // polygon centroid algorithm;\n    for (i = 0, j = len - 1; i < len; j = i++) {\n      p1 = points[i];\n      p2 = points[j];\n      f = p1.y * p2.x - p2.y * p1.x;\n      x += (p1.x + p2.x) * f;\n      y += (p1.y + p2.y) * f;\n      area += f * 3;\n    }\n    if (area === 0) {\n      // Polygon is so small that all points are on same pixel.\n      center = points[0];\n    } else {\n      center = [x / area, y / area];\n    }\n    var latlngCenter = crs.unproject(toPoint(center));\n    return toLatLng([latlngCenter.lat + centroidLatLng.lat, latlngCenter.lng + centroidLatLng.lng]);\n  }\n\n  /* @function centroid(latlngs: LatLng[]): LatLng\r\n   * Returns the 'center of mass' of the passed LatLngs.\r\n   */\n  function centroid(coords) {\n    var latSum = 0;\n    var lngSum = 0;\n    var len = 0;\n    for (var i = 0; i < coords.length; i++) {\n      var latlng = toLatLng(coords[i]);\n      latSum += latlng.lat;\n      lngSum += latlng.lng;\n      len++;\n    }\n    return toLatLng([latSum / len, lngSum / len]);\n  }\n  var PolyUtil = {\n    __proto__: null,\n    clipPolygon: clipPolygon,\n    polygonCenter: polygonCenter,\n    centroid: centroid\n  };\n\n  /*\r\n   * @namespace LineUtil\r\n   *\r\n   * Various utility functions for polyline points processing, used by Leaflet internally to make polylines lightning-fast.\r\n   */\n\n  // Simplify polyline with vertex reduction and Douglas-Peucker simplification.\n  // Improves rendering performance dramatically by lessening the number of points to draw.\n\n  // @function simplify(points: Point[], tolerance: Number): Point[]\n  // Dramatically reduces the number of points in a polyline while retaining\n  // its shape and returns a new array of simplified points, using the\n  // [Ramer-Douglas-Peucker algorithm](https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm).\n  // Used for a huge performance boost when processing/displaying Leaflet polylines for\n  // each zoom level and also reducing visual noise. tolerance affects the amount of\n  // simplification (lesser value means higher quality but slower and with more points).\n  // Also released as a separated micro-library [Simplify.js](https://mourner.github.io/simplify-js/).\n  function simplify(points, tolerance) {\n    if (!tolerance || !points.length) {\n      return points.slice();\n    }\n    var sqTolerance = tolerance * tolerance;\n\n    // stage 1: vertex reduction\n    points = _reducePoints(points, sqTolerance);\n\n    // stage 2: Douglas-Peucker simplification\n    points = _simplifyDP(points, sqTolerance);\n    return points;\n  }\n\n  // @function pointToSegmentDistance(p: Point, p1: Point, p2: Point): Number\n  // Returns the distance between point `p` and segment `p1` to `p2`.\n  function pointToSegmentDistance(p, p1, p2) {\n    return Math.sqrt(_sqClosestPointOnSegment(p, p1, p2, true));\n  }\n\n  // @function closestPointOnSegment(p: Point, p1: Point, p2: Point): Number\n  // Returns the closest point from a point `p` on a segment `p1` to `p2`.\n  function closestPointOnSegment(p, p1, p2) {\n    return _sqClosestPointOnSegment(p, p1, p2);\n  }\n\n  // Ramer-Douglas-Peucker simplification, see https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm\n  function _simplifyDP(points, sqTolerance) {\n    var len = points.length,\n      ArrayConstructor = typeof Uint8Array !== undefined + '' ? Uint8Array : Array,\n      markers = new ArrayConstructor(len);\n    markers[0] = markers[len - 1] = 1;\n    _simplifyDPStep(points, markers, sqTolerance, 0, len - 1);\n    var i,\n      newPoints = [];\n    for (i = 0; i < len; i++) {\n      if (markers[i]) {\n        newPoints.push(points[i]);\n      }\n    }\n    return newPoints;\n  }\n  function _simplifyDPStep(points, markers, sqTolerance, first, last) {\n    var maxSqDist = 0,\n      index,\n      i,\n      sqDist;\n    for (i = first + 1; i <= last - 1; i++) {\n      sqDist = _sqClosestPointOnSegment(points[i], points[first], points[last], true);\n      if (sqDist > maxSqDist) {\n        index = i;\n        maxSqDist = sqDist;\n      }\n    }\n    if (maxSqDist > sqTolerance) {\n      markers[index] = 1;\n      _simplifyDPStep(points, markers, sqTolerance, first, index);\n      _simplifyDPStep(points, markers, sqTolerance, index, last);\n    }\n  }\n\n  // reduce points that are too close to each other to a single point\n  function _reducePoints(points, sqTolerance) {\n    var reducedPoints = [points[0]];\n    for (var i = 1, prev = 0, len = points.length; i < len; i++) {\n      if (_sqDist(points[i], points[prev]) > sqTolerance) {\n        reducedPoints.push(points[i]);\n        prev = i;\n      }\n    }\n    if (prev < len - 1) {\n      reducedPoints.push(points[len - 1]);\n    }\n    return reducedPoints;\n  }\n  var _lastCode;\n\n  // @function clipSegment(a: Point, b: Point, bounds: Bounds, useLastCode?: Boolean, round?: Boolean): Point[]|Boolean\n  // Clips the segment a to b by rectangular bounds with the\n  // [Cohen-Sutherland algorithm](https://en.wikipedia.org/wiki/Cohen%E2%80%93Sutherland_algorithm)\n  // (modifying the segment points directly!). Used by Leaflet to only show polyline\n  // points that are on the screen or near, increasing performance.\n  function clipSegment(a, b, bounds, useLastCode, round) {\n    var codeA = useLastCode ? _lastCode : _getBitCode(a, bounds),\n      codeB = _getBitCode(b, bounds),\n      codeOut,\n      p,\n      newCode;\n\n    // save 2nd code to avoid calculating it on the next segment\n    _lastCode = codeB;\n    while (true) {\n      // if a,b is inside the clip window (trivial accept)\n      if (!(codeA | codeB)) {\n        return [a, b];\n      }\n\n      // if a,b is outside the clip window (trivial reject)\n      if (codeA & codeB) {\n        return false;\n      }\n\n      // other cases\n      codeOut = codeA || codeB;\n      p = _getEdgeIntersection(a, b, codeOut, bounds, round);\n      newCode = _getBitCode(p, bounds);\n      if (codeOut === codeA) {\n        a = p;\n        codeA = newCode;\n      } else {\n        b = p;\n        codeB = newCode;\n      }\n    }\n  }\n  function _getEdgeIntersection(a, b, code, bounds, round) {\n    var dx = b.x - a.x,\n      dy = b.y - a.y,\n      min = bounds.min,\n      max = bounds.max,\n      x,\n      y;\n    if (code & 8) {\n      // top\n      x = a.x + dx * (max.y - a.y) / dy;\n      y = max.y;\n    } else if (code & 4) {\n      // bottom\n      x = a.x + dx * (min.y - a.y) / dy;\n      y = min.y;\n    } else if (code & 2) {\n      // right\n      x = max.x;\n      y = a.y + dy * (max.x - a.x) / dx;\n    } else if (code & 1) {\n      // left\n      x = min.x;\n      y = a.y + dy * (min.x - a.x) / dx;\n    }\n    return new Point(x, y, round);\n  }\n  function _getBitCode(p, bounds) {\n    var code = 0;\n    if (p.x < bounds.min.x) {\n      // left\n      code |= 1;\n    } else if (p.x > bounds.max.x) {\n      // right\n      code |= 2;\n    }\n    if (p.y < bounds.min.y) {\n      // bottom\n      code |= 4;\n    } else if (p.y > bounds.max.y) {\n      // top\n      code |= 8;\n    }\n    return code;\n  }\n\n  // square distance (to avoid unnecessary Math.sqrt calls)\n  function _sqDist(p1, p2) {\n    var dx = p2.x - p1.x,\n      dy = p2.y - p1.y;\n    return dx * dx + dy * dy;\n  }\n\n  // return closest point on segment or distance to that point\n  function _sqClosestPointOnSegment(p, p1, p2, sqDist) {\n    var x = p1.x,\n      y = p1.y,\n      dx = p2.x - x,\n      dy = p2.y - y,\n      dot = dx * dx + dy * dy,\n      t;\n    if (dot > 0) {\n      t = ((p.x - x) * dx + (p.y - y) * dy) / dot;\n      if (t > 1) {\n        x = p2.x;\n        y = p2.y;\n      } else if (t > 0) {\n        x += dx * t;\n        y += dy * t;\n      }\n    }\n    dx = p.x - x;\n    dy = p.y - y;\n    return sqDist ? dx * dx + dy * dy : new Point(x, y);\n  }\n\n  // @function isFlat(latlngs: LatLng[]): Boolean\n  // Returns true if `latlngs` is a flat array, false is nested.\n  function isFlat(latlngs) {\n    return !isArray(latlngs[0]) || typeof latlngs[0][0] !== 'object' && typeof latlngs[0][0] !== 'undefined';\n  }\n  function _flat(latlngs) {\n    console.warn('Deprecated use of _flat, please use L.LineUtil.isFlat instead.');\n    return isFlat(latlngs);\n  }\n\n  /* @function polylineCenter(latlngs: LatLng[], crs: CRS): LatLng\r\n   * Returns the center ([centroid](http://en.wikipedia.org/wiki/Centroid)) of the passed LatLngs (first ring) from a polyline.\r\n   */\n  function polylineCenter(latlngs, crs) {\n    var i, halfDist, segDist, dist, p1, p2, ratio, center;\n    if (!latlngs || latlngs.length === 0) {\n      throw new Error('latlngs not passed');\n    }\n    if (!isFlat(latlngs)) {\n      console.warn('latlngs are not flat! Only the first ring will be used');\n      latlngs = latlngs[0];\n    }\n    var centroidLatLng = toLatLng([0, 0]);\n    var bounds = toLatLngBounds(latlngs);\n    var areaBounds = bounds.getNorthWest().distanceTo(bounds.getSouthWest()) * bounds.getNorthEast().distanceTo(bounds.getNorthWest());\n    // tests showed that below 1700 rounding errors are happening\n    if (areaBounds < 1700) {\n      // getting a inexact center, to move the latlngs near to [0, 0] to prevent rounding errors\n      centroidLatLng = centroid(latlngs);\n    }\n    var len = latlngs.length;\n    var points = [];\n    for (i = 0; i < len; i++) {\n      var latlng = toLatLng(latlngs[i]);\n      points.push(crs.project(toLatLng([latlng.lat - centroidLatLng.lat, latlng.lng - centroidLatLng.lng])));\n    }\n    for (i = 0, halfDist = 0; i < len - 1; i++) {\n      halfDist += points[i].distanceTo(points[i + 1]) / 2;\n    }\n\n    // The line is so small in the current view that all points are on the same pixel.\n    if (halfDist === 0) {\n      center = points[0];\n    } else {\n      for (i = 0, dist = 0; i < len - 1; i++) {\n        p1 = points[i];\n        p2 = points[i + 1];\n        segDist = p1.distanceTo(p2);\n        dist += segDist;\n        if (dist > halfDist) {\n          ratio = (dist - halfDist) / segDist;\n          center = [p2.x - ratio * (p2.x - p1.x), p2.y - ratio * (p2.y - p1.y)];\n          break;\n        }\n      }\n    }\n    var latlngCenter = crs.unproject(toPoint(center));\n    return toLatLng([latlngCenter.lat + centroidLatLng.lat, latlngCenter.lng + centroidLatLng.lng]);\n  }\n  var LineUtil = {\n    __proto__: null,\n    simplify: simplify,\n    pointToSegmentDistance: pointToSegmentDistance,\n    closestPointOnSegment: closestPointOnSegment,\n    clipSegment: clipSegment,\n    _getEdgeIntersection: _getEdgeIntersection,\n    _getBitCode: _getBitCode,\n    _sqClosestPointOnSegment: _sqClosestPointOnSegment,\n    isFlat: isFlat,\n    _flat: _flat,\n    polylineCenter: polylineCenter\n  };\n\n  /*\r\n   * @namespace Projection\r\n   * @section\r\n   * Leaflet comes with a set of already defined Projections out of the box:\r\n   *\r\n   * @projection L.Projection.LonLat\r\n   *\r\n   * Equirectangular, or Plate Carree projection — the most simple projection,\r\n   * mostly used by GIS enthusiasts. Directly maps `x` as longitude, and `y` as\r\n   * latitude. Also suitable for flat worlds, e.g. game maps. Used by the\r\n   * `EPSG:4326` and `Simple` CRS.\r\n   */\n\n  var LonLat = {\n    project: function (latlng) {\n      return new Point(latlng.lng, latlng.lat);\n    },\n    unproject: function (point) {\n      return new LatLng(point.y, point.x);\n    },\n    bounds: new Bounds([-180, -90], [180, 90])\n  };\n\n  /*\r\n   * @namespace Projection\r\n   * @projection L.Projection.Mercator\r\n   *\r\n   * Elliptical Mercator projection — more complex than Spherical Mercator. Assumes that Earth is an ellipsoid. Used by the EPSG:3395 CRS.\r\n   */\n\n  var Mercator = {\n    R: 6378137,\n    R_MINOR: 6356752.314245179,\n    bounds: new Bounds([-20037508.34279, -15496570.73972], [20037508.34279, 18764656.23138]),\n    project: function (latlng) {\n      var d = Math.PI / 180,\n        r = this.R,\n        y = latlng.lat * d,\n        tmp = this.R_MINOR / r,\n        e = Math.sqrt(1 - tmp * tmp),\n        con = e * Math.sin(y);\n      var ts = Math.tan(Math.PI / 4 - y / 2) / Math.pow((1 - con) / (1 + con), e / 2);\n      y = -r * Math.log(Math.max(ts, 1E-10));\n      return new Point(latlng.lng * d * r, y);\n    },\n    unproject: function (point) {\n      var d = 180 / Math.PI,\n        r = this.R,\n        tmp = this.R_MINOR / r,\n        e = Math.sqrt(1 - tmp * tmp),\n        ts = Math.exp(-point.y / r),\n        phi = Math.PI / 2 - 2 * Math.atan(ts);\n      for (var i = 0, dphi = 0.1, con; i < 15 && Math.abs(dphi) > 1e-7; i++) {\n        con = e * Math.sin(phi);\n        con = Math.pow((1 - con) / (1 + con), e / 2);\n        dphi = Math.PI / 2 - 2 * Math.atan(ts * con) - phi;\n        phi += dphi;\n      }\n      return new LatLng(phi * d, point.x * d / r);\n    }\n  };\n\n  /*\n   * @class Projection\n    * An object with methods for projecting geographical coordinates of the world onto\n   * a flat surface (and back). See [Map projection](https://en.wikipedia.org/wiki/Map_projection).\n    * @property bounds: Bounds\n   * The bounds (specified in CRS units) where the projection is valid\n    * @method project(latlng: LatLng): Point\n   * Projects geographical coordinates into a 2D point.\n   * Only accepts actual `L.LatLng` instances, not arrays.\n    * @method unproject(point: Point): LatLng\n   * The inverse of `project`. Projects a 2D point into a geographical location.\n   * Only accepts actual `L.Point` instances, not arrays.\n    * Note that the projection instances do not inherit from Leaflet's `Class` object,\n   * and can't be instantiated. Also, new classes can't inherit from them,\n   * and methods can't be added to them with the `include` function.\n    */\n\n  var index = {\n    __proto__: null,\n    LonLat: LonLat,\n    Mercator: Mercator,\n    SphericalMercator: SphericalMercator\n  };\n\n  /*\r\n   * @namespace CRS\r\n   * @crs L.CRS.EPSG3395\r\n   *\r\n   * Rarely used by some commercial tile providers. Uses Elliptical Mercator projection.\r\n   */\n  var EPSG3395 = extend({}, Earth, {\n    code: 'EPSG:3395',\n    projection: Mercator,\n    transformation: function () {\n      var scale = 0.5 / (Math.PI * Mercator.R);\n      return toTransformation(scale, 0.5, -scale, 0.5);\n    }()\n  });\n\n  /*\r\n   * @namespace CRS\r\n   * @crs L.CRS.EPSG4326\r\n   *\r\n   * A common CRS among GIS enthusiasts. Uses simple Equirectangular projection.\r\n   *\r\n   * Leaflet 1.0.x complies with the [TMS coordinate scheme for EPSG:4326](https://wiki.osgeo.org/wiki/Tile_Map_Service_Specification#global-geodetic),\r\n   * which is a breaking change from 0.7.x behaviour.  If you are using a `TileLayer`\r\n   * with this CRS, ensure that there are two 256x256 pixel tiles covering the\r\n   * whole earth at zoom level zero, and that the tile coordinate origin is (-180,+90),\r\n   * or (-180,-90) for `TileLayer`s with [the `tms` option](#tilelayer-tms) set.\r\n   */\n\n  var EPSG4326 = extend({}, Earth, {\n    code: 'EPSG:4326',\n    projection: LonLat,\n    transformation: toTransformation(1 / 180, 1, -1 / 180, 0.5)\n  });\n\n  /*\n   * @namespace CRS\n   * @crs L.CRS.Simple\n   *\n   * A simple CRS that maps longitude and latitude into `x` and `y` directly.\n   * May be used for maps of flat surfaces (e.g. game maps). Note that the `y`\n   * axis should still be inverted (going from bottom to top). `distance()` returns\n   * simple euclidean distance.\n   */\n\n  var Simple = extend({}, CRS, {\n    projection: LonLat,\n    transformation: toTransformation(1, 0, -1, 0),\n    scale: function (zoom) {\n      return Math.pow(2, zoom);\n    },\n    zoom: function (scale) {\n      return Math.log(scale) / Math.LN2;\n    },\n    distance: function (latlng1, latlng2) {\n      var dx = latlng2.lng - latlng1.lng,\n        dy = latlng2.lat - latlng1.lat;\n      return Math.sqrt(dx * dx + dy * dy);\n    },\n    infinite: true\n  });\n  CRS.Earth = Earth;\n  CRS.EPSG3395 = EPSG3395;\n  CRS.EPSG3857 = EPSG3857;\n  CRS.EPSG900913 = EPSG900913;\n  CRS.EPSG4326 = EPSG4326;\n  CRS.Simple = Simple;\n\n  /*\n   * @class Layer\n   * @inherits Evented\n   * @aka L.Layer\n   * @aka ILayer\n   *\n   * A set of methods from the Layer base class that all Leaflet layers use.\n   * Inherits all methods, options and events from `L.Evented`.\n   *\n   * @example\n   *\n   * ```js\n   * var layer = L.marker(latlng).addTo(map);\n   * layer.addTo(map);\n   * layer.remove();\n   * ```\n   *\n   * @event add: Event\n   * Fired after the layer is added to a map\n   *\n   * @event remove: Event\n   * Fired after the layer is removed from a map\n   */\n\n  var Layer = Evented.extend({\n    // Classes extending `L.Layer` will inherit the following options:\n    options: {\n      // @option pane: String = 'overlayPane'\n      // By default the layer will be added to the map's [overlay pane](#map-overlaypane). Overriding this option will cause the layer to be placed on another pane by default.\n      pane: 'overlayPane',\n      // @option attribution: String = null\n      // String to be shown in the attribution control, e.g. \"© OpenStreetMap contributors\". It describes the layer data and is often a legal obligation towards copyright holders and tile providers.\n      attribution: null,\n      bubblingMouseEvents: true\n    },\n    /* @section\n     * Classes extending `L.Layer` will inherit the following methods:\n     *\n     * @method addTo(map: Map|LayerGroup): this\n     * Adds the layer to the given map or layer group.\n     */\n    addTo: function (map) {\n      map.addLayer(this);\n      return this;\n    },\n    // @method remove: this\n    // Removes the layer from the map it is currently active on.\n    remove: function () {\n      return this.removeFrom(this._map || this._mapToAdd);\n    },\n    // @method removeFrom(map: Map): this\n    // Removes the layer from the given map\n    //\n    // @alternative\n    // @method removeFrom(group: LayerGroup): this\n    // Removes the layer from the given `LayerGroup`\n    removeFrom: function (obj) {\n      if (obj) {\n        obj.removeLayer(this);\n      }\n      return this;\n    },\n    // @method getPane(name? : String): HTMLElement\n    // Returns the `HTMLElement` representing the named pane on the map. If `name` is omitted, returns the pane for this layer.\n    getPane: function (name) {\n      return this._map.getPane(name ? this.options[name] || name : this.options.pane);\n    },\n    addInteractiveTarget: function (targetEl) {\n      this._map._targets[stamp(targetEl)] = this;\n      return this;\n    },\n    removeInteractiveTarget: function (targetEl) {\n      delete this._map._targets[stamp(targetEl)];\n      return this;\n    },\n    // @method getAttribution: String\n    // Used by the `attribution control`, returns the [attribution option](#gridlayer-attribution).\n    getAttribution: function () {\n      return this.options.attribution;\n    },\n    _layerAdd: function (e) {\n      var map = e.target;\n\n      // check in case layer gets added and then removed before the map is ready\n      if (!map.hasLayer(this)) {\n        return;\n      }\n      this._map = map;\n      this._zoomAnimated = map._zoomAnimated;\n      if (this.getEvents) {\n        var events = this.getEvents();\n        map.on(events, this);\n        this.once('remove', function () {\n          map.off(events, this);\n        }, this);\n      }\n      this.onAdd(map);\n      this.fire('add');\n      map.fire('layeradd', {\n        layer: this\n      });\n    }\n  });\n\n  /* @section Extension methods\n   * @uninheritable\n   *\n   * Every layer should extend from `L.Layer` and (re-)implement the following methods.\n   *\n   * @method onAdd(map: Map): this\n   * Should contain code that creates DOM elements for the layer, adds them to `map panes` where they should belong and puts listeners on relevant map events. Called on [`map.addLayer(layer)`](#map-addlayer).\n   *\n   * @method onRemove(map: Map): this\n   * Should contain all clean up code that removes the layer's elements from the DOM and removes listeners previously added in [`onAdd`](#layer-onadd). Called on [`map.removeLayer(layer)`](#map-removelayer).\n   *\n   * @method getEvents(): Object\n   * This optional method should return an object like `{ viewreset: this._reset }` for [`addEventListener`](#evented-addeventlistener). The event handlers in this object will be automatically added and removed from the map with your layer.\n   *\n   * @method getAttribution(): String\n   * This optional method should return a string containing HTML to be shown on the `Attribution control` whenever the layer is visible.\n   *\n   * @method beforeAdd(map: Map): this\n   * Optional method. Called on [`map.addLayer(layer)`](#map-addlayer), before the layer is added to the map, before events are initialized, without waiting until the map is in a usable state. Use for early initialization only.\n   */\n\n  /* @namespace Map\n   * @section Layer events\n   *\n   * @event layeradd: LayerEvent\n   * Fired when a new layer is added to the map.\n   *\n   * @event layerremove: LayerEvent\n   * Fired when some layer is removed from the map\n   *\n   * @section Methods for Layers and Controls\n   */\n  Map.include({\n    // @method addLayer(layer: Layer): this\n    // Adds the given layer to the map\n    addLayer: function (layer) {\n      if (!layer._layerAdd) {\n        throw new Error('The provided object is not a Layer.');\n      }\n      var id = stamp(layer);\n      if (this._layers[id]) {\n        return this;\n      }\n      this._layers[id] = layer;\n      layer._mapToAdd = this;\n      if (layer.beforeAdd) {\n        layer.beforeAdd(this);\n      }\n      this.whenReady(layer._layerAdd, layer);\n      return this;\n    },\n    // @method removeLayer(layer: Layer): this\n    // Removes the given layer from the map.\n    removeLayer: function (layer) {\n      var id = stamp(layer);\n      if (!this._layers[id]) {\n        return this;\n      }\n      if (this._loaded) {\n        layer.onRemove(this);\n      }\n      delete this._layers[id];\n      if (this._loaded) {\n        this.fire('layerremove', {\n          layer: layer\n        });\n        layer.fire('remove');\n      }\n      layer._map = layer._mapToAdd = null;\n      return this;\n    },\n    // @method hasLayer(layer: Layer): Boolean\n    // Returns `true` if the given layer is currently added to the map\n    hasLayer: function (layer) {\n      return stamp(layer) in this._layers;\n    },\n    /* @method eachLayer(fn: Function, context?: Object): this\n     * Iterates over the layers of the map, optionally specifying context of the iterator function.\n     * ```\n     * map.eachLayer(function(layer){\n     *     layer.bindPopup('Hello');\n     * });\n     * ```\n     */\n    eachLayer: function (method, context) {\n      for (var i in this._layers) {\n        method.call(context, this._layers[i]);\n      }\n      return this;\n    },\n    _addLayers: function (layers) {\n      layers = layers ? isArray(layers) ? layers : [layers] : [];\n      for (var i = 0, len = layers.length; i < len; i++) {\n        this.addLayer(layers[i]);\n      }\n    },\n    _addZoomLimit: function (layer) {\n      if (!isNaN(layer.options.maxZoom) || !isNaN(layer.options.minZoom)) {\n        this._zoomBoundLayers[stamp(layer)] = layer;\n        this._updateZoomLevels();\n      }\n    },\n    _removeZoomLimit: function (layer) {\n      var id = stamp(layer);\n      if (this._zoomBoundLayers[id]) {\n        delete this._zoomBoundLayers[id];\n        this._updateZoomLevels();\n      }\n    },\n    _updateZoomLevels: function () {\n      var minZoom = Infinity,\n        maxZoom = -Infinity,\n        oldZoomSpan = this._getZoomSpan();\n      for (var i in this._zoomBoundLayers) {\n        var options = this._zoomBoundLayers[i].options;\n        minZoom = options.minZoom === undefined ? minZoom : Math.min(minZoom, options.minZoom);\n        maxZoom = options.maxZoom === undefined ? maxZoom : Math.max(maxZoom, options.maxZoom);\n      }\n      this._layersMaxZoom = maxZoom === -Infinity ? undefined : maxZoom;\n      this._layersMinZoom = minZoom === Infinity ? undefined : minZoom;\n\n      // @section Map state change events\n      // @event zoomlevelschange: Event\n      // Fired when the number of zoomlevels on the map is changed due\n      // to adding or removing a layer.\n      if (oldZoomSpan !== this._getZoomSpan()) {\n        this.fire('zoomlevelschange');\n      }\n      if (this.options.maxZoom === undefined && this._layersMaxZoom && this.getZoom() > this._layersMaxZoom) {\n        this.setZoom(this._layersMaxZoom);\n      }\n      if (this.options.minZoom === undefined && this._layersMinZoom && this.getZoom() < this._layersMinZoom) {\n        this.setZoom(this._layersMinZoom);\n      }\n    }\n  });\n\n  /*\r\n   * @class LayerGroup\r\n   * @aka L.LayerGroup\r\n   * @inherits Interactive layer\r\n   *\r\n   * Used to group several layers and handle them as one. If you add it to the map,\r\n   * any layers added or removed from the group will be added/removed on the map as\r\n   * well. Extends `Layer`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * L.layerGroup([marker1, marker2])\r\n   * \t.addLayer(polyline)\r\n   * \t.addTo(map);\r\n   * ```\r\n   */\n\n  var LayerGroup = Layer.extend({\n    initialize: function (layers, options) {\n      setOptions(this, options);\n      this._layers = {};\n      var i, len;\n      if (layers) {\n        for (i = 0, len = layers.length; i < len; i++) {\n          this.addLayer(layers[i]);\n        }\n      }\n    },\n    // @method addLayer(layer: Layer): this\n    // Adds the given layer to the group.\n    addLayer: function (layer) {\n      var id = this.getLayerId(layer);\n      this._layers[id] = layer;\n      if (this._map) {\n        this._map.addLayer(layer);\n      }\n      return this;\n    },\n    // @method removeLayer(layer: Layer): this\n    // Removes the given layer from the group.\n    // @alternative\n    // @method removeLayer(id: Number): this\n    // Removes the layer with the given internal ID from the group.\n    removeLayer: function (layer) {\n      var id = layer in this._layers ? layer : this.getLayerId(layer);\n      if (this._map && this._layers[id]) {\n        this._map.removeLayer(this._layers[id]);\n      }\n      delete this._layers[id];\n      return this;\n    },\n    // @method hasLayer(layer: Layer): Boolean\n    // Returns `true` if the given layer is currently added to the group.\n    // @alternative\n    // @method hasLayer(id: Number): Boolean\n    // Returns `true` if the given internal ID is currently added to the group.\n    hasLayer: function (layer) {\n      var layerId = typeof layer === 'number' ? layer : this.getLayerId(layer);\n      return layerId in this._layers;\n    },\n    // @method clearLayers(): this\n    // Removes all the layers from the group.\n    clearLayers: function () {\n      return this.eachLayer(this.removeLayer, this);\n    },\n    // @method invoke(methodName: String, …): this\n    // Calls `methodName` on every layer contained in this group, passing any\n    // additional parameters. Has no effect if the layers contained do not\n    // implement `methodName`.\n    invoke: function (methodName) {\n      var args = Array.prototype.slice.call(arguments, 1),\n        i,\n        layer;\n      for (i in this._layers) {\n        layer = this._layers[i];\n        if (layer[methodName]) {\n          layer[methodName].apply(layer, args);\n        }\n      }\n      return this;\n    },\n    onAdd: function (map) {\n      this.eachLayer(map.addLayer, map);\n    },\n    onRemove: function (map) {\n      this.eachLayer(map.removeLayer, map);\n    },\n    // @method eachLayer(fn: Function, context?: Object): this\n    // Iterates over the layers of the group, optionally specifying context of the iterator function.\n    // ```js\n    // group.eachLayer(function (layer) {\n    // \tlayer.bindPopup('Hello');\n    // });\n    // ```\n    eachLayer: function (method, context) {\n      for (var i in this._layers) {\n        method.call(context, this._layers[i]);\n      }\n      return this;\n    },\n    // @method getLayer(id: Number): Layer\n    // Returns the layer with the given internal ID.\n    getLayer: function (id) {\n      return this._layers[id];\n    },\n    // @method getLayers(): Layer[]\n    // Returns an array of all the layers added to the group.\n    getLayers: function () {\n      var layers = [];\n      this.eachLayer(layers.push, layers);\n      return layers;\n    },\n    // @method setZIndex(zIndex: Number): this\n    // Calls `setZIndex` on every layer contained in this group, passing the z-index.\n    setZIndex: function (zIndex) {\n      return this.invoke('setZIndex', zIndex);\n    },\n    // @method getLayerId(layer: Layer): Number\n    // Returns the internal ID for a layer\n    getLayerId: function (layer) {\n      return stamp(layer);\n    }\n  });\n\n  // @factory L.layerGroup(layers?: Layer[], options?: Object)\n  // Create a layer group, optionally given an initial set of layers and an `options` object.\n  var layerGroup = function (layers, options) {\n    return new LayerGroup(layers, options);\n  };\n\n  /*\r\n   * @class FeatureGroup\r\n   * @aka L.FeatureGroup\r\n   * @inherits LayerGroup\r\n   *\r\n   * Extended `LayerGroup` that makes it easier to do the same thing to all its member layers:\r\n   *  * [`bindPopup`](#layer-bindpopup) binds a popup to all of the layers at once (likewise with [`bindTooltip`](#layer-bindtooltip))\r\n   *  * Events are propagated to the `FeatureGroup`, so if the group has an event\r\n   * handler, it will handle events from any of the layers. This includes mouse events\r\n   * and custom events.\r\n   *  * Has `layeradd` and `layerremove` events\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * L.featureGroup([marker1, marker2, polyline])\r\n   * \t.bindPopup('Hello world!')\r\n   * \t.on('click', function() { alert('Clicked on a member of the group!'); })\r\n   * \t.addTo(map);\r\n   * ```\r\n   */\n\n  var FeatureGroup = LayerGroup.extend({\n    addLayer: function (layer) {\n      if (this.hasLayer(layer)) {\n        return this;\n      }\n      layer.addEventParent(this);\n      LayerGroup.prototype.addLayer.call(this, layer);\n\n      // @event layeradd: LayerEvent\n      // Fired when a layer is added to this `FeatureGroup`\n      return this.fire('layeradd', {\n        layer: layer\n      });\n    },\n    removeLayer: function (layer) {\n      if (!this.hasLayer(layer)) {\n        return this;\n      }\n      if (layer in this._layers) {\n        layer = this._layers[layer];\n      }\n      layer.removeEventParent(this);\n      LayerGroup.prototype.removeLayer.call(this, layer);\n\n      // @event layerremove: LayerEvent\n      // Fired when a layer is removed from this `FeatureGroup`\n      return this.fire('layerremove', {\n        layer: layer\n      });\n    },\n    // @method setStyle(style: Path options): this\n    // Sets the given path options to each layer of the group that has a `setStyle` method.\n    setStyle: function (style) {\n      return this.invoke('setStyle', style);\n    },\n    // @method bringToFront(): this\n    // Brings the layer group to the top of all other layers\n    bringToFront: function () {\n      return this.invoke('bringToFront');\n    },\n    // @method bringToBack(): this\n    // Brings the layer group to the back of all other layers\n    bringToBack: function () {\n      return this.invoke('bringToBack');\n    },\n    // @method getBounds(): LatLngBounds\n    // Returns the LatLngBounds of the Feature Group (created from bounds and coordinates of its children).\n    getBounds: function () {\n      var bounds = new LatLngBounds();\n      for (var id in this._layers) {\n        var layer = this._layers[id];\n        bounds.extend(layer.getBounds ? layer.getBounds() : layer.getLatLng());\n      }\n      return bounds;\n    }\n  });\n\n  // @factory L.featureGroup(layers?: Layer[], options?: Object)\n  // Create a feature group, optionally given an initial set of layers and an `options` object.\n  var featureGroup = function (layers, options) {\n    return new FeatureGroup(layers, options);\n  };\n\n  /*\r\n   * @class Icon\r\n   * @aka L.Icon\r\n   *\r\n   * Represents an icon to provide when creating a marker.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var myIcon = L.icon({\r\n   *     iconUrl: 'my-icon.png',\r\n   *     iconRetinaUrl: '<EMAIL>',\r\n   *     iconSize: [38, 95],\r\n   *     iconAnchor: [22, 94],\r\n   *     popupAnchor: [-3, -76],\r\n   *     shadowUrl: 'my-icon-shadow.png',\r\n   *     shadowRetinaUrl: '<EMAIL>',\r\n   *     shadowSize: [68, 95],\r\n   *     shadowAnchor: [22, 94]\r\n   * });\r\n   *\r\n   * L.marker([50.505, 30.57], {icon: myIcon}).addTo(map);\r\n   * ```\r\n   *\r\n   * `L.Icon.Default` extends `L.Icon` and is the blue icon Leaflet uses for markers by default.\r\n   *\r\n   */\n\n  var Icon = Class.extend({\n    /* @section\r\n     * @aka Icon options\r\n     *\r\n     * @option iconUrl: String = null\r\n     * **(required)** The URL to the icon image (absolute or relative to your script path).\r\n     *\r\n     * @option iconRetinaUrl: String = null\r\n     * The URL to a retina sized version of the icon image (absolute or relative to your\r\n     * script path). Used for Retina screen devices.\r\n     *\r\n     * @option iconSize: Point = null\r\n     * Size of the icon image in pixels.\r\n     *\r\n     * @option iconAnchor: Point = null\r\n     * The coordinates of the \"tip\" of the icon (relative to its top left corner). The icon\r\n     * will be aligned so that this point is at the marker's geographical location. Centered\r\n     * by default if size is specified, also can be set in CSS with negative margins.\r\n     *\r\n     * @option popupAnchor: Point = [0, 0]\r\n     * The coordinates of the point from which popups will \"open\", relative to the icon anchor.\r\n     *\r\n     * @option tooltipAnchor: Point = [0, 0]\r\n     * The coordinates of the point from which tooltips will \"open\", relative to the icon anchor.\r\n     *\r\n     * @option shadowUrl: String = null\r\n     * The URL to the icon shadow image. If not specified, no shadow image will be created.\r\n     *\r\n     * @option shadowRetinaUrl: String = null\r\n     *\r\n     * @option shadowSize: Point = null\r\n     * Size of the shadow image in pixels.\r\n     *\r\n     * @option shadowAnchor: Point = null\r\n     * The coordinates of the \"tip\" of the shadow (relative to its top left corner) (the same\r\n     * as iconAnchor if not specified).\r\n     *\r\n     * @option className: String = ''\r\n     * A custom class name to assign to both icon and shadow images. Empty by default.\r\n     */\n\n    options: {\n      popupAnchor: [0, 0],\n      tooltipAnchor: [0, 0],\n      // @option crossOrigin: Boolean|String = false\n      // Whether the crossOrigin attribute will be added to the tiles.\n      // If a String is provided, all tiles will have their crossOrigin attribute set to the String provided. This is needed if you want to access tile pixel data.\n      // Refer to [CORS Settings](https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_settings_attributes) for valid String values.\n      crossOrigin: false\n    },\n    initialize: function (options) {\n      setOptions(this, options);\n    },\n    // @method createIcon(oldIcon?: HTMLElement): HTMLElement\n    // Called internally when the icon has to be shown, returns a `<img>` HTML element\n    // styled according to the options.\n    createIcon: function (oldIcon) {\n      return this._createIcon('icon', oldIcon);\n    },\n    // @method createShadow(oldIcon?: HTMLElement): HTMLElement\n    // As `createIcon`, but for the shadow beneath it.\n    createShadow: function (oldIcon) {\n      return this._createIcon('shadow', oldIcon);\n    },\n    _createIcon: function (name, oldIcon) {\n      var src = this._getIconUrl(name);\n      if (!src) {\n        if (name === 'icon') {\n          throw new Error('iconUrl not set in Icon options (see the docs).');\n        }\n        return null;\n      }\n      var img = this._createImg(src, oldIcon && oldIcon.tagName === 'IMG' ? oldIcon : null);\n      this._setIconStyles(img, name);\n      if (this.options.crossOrigin || this.options.crossOrigin === '') {\n        img.crossOrigin = this.options.crossOrigin === true ? '' : this.options.crossOrigin;\n      }\n      return img;\n    },\n    _setIconStyles: function (img, name) {\n      var options = this.options;\n      var sizeOption = options[name + 'Size'];\n      if (typeof sizeOption === 'number') {\n        sizeOption = [sizeOption, sizeOption];\n      }\n      var size = toPoint(sizeOption),\n        anchor = toPoint(name === 'shadow' && options.shadowAnchor || options.iconAnchor || size && size.divideBy(2, true));\n      img.className = 'leaflet-marker-' + name + ' ' + (options.className || '');\n      if (anchor) {\n        img.style.marginLeft = -anchor.x + 'px';\n        img.style.marginTop = -anchor.y + 'px';\n      }\n      if (size) {\n        img.style.width = size.x + 'px';\n        img.style.height = size.y + 'px';\n      }\n    },\n    _createImg: function (src, el) {\n      el = el || document.createElement('img');\n      el.src = src;\n      return el;\n    },\n    _getIconUrl: function (name) {\n      return Browser.retina && this.options[name + 'RetinaUrl'] || this.options[name + 'Url'];\n    }\n  });\n\n  // @factory L.icon(options: Icon options)\n  // Creates an icon instance with the given options.\n  function icon(options) {\n    return new Icon(options);\n  }\n\n  /*\n   * @miniclass Icon.Default (Icon)\n   * @aka L.Icon.Default\n   * @section\n   *\n   * A trivial subclass of `Icon`, represents the icon to use in `Marker`s when\n   * no icon is specified. Points to the blue marker image distributed with Leaflet\n   * releases.\n   *\n   * In order to customize the default icon, just change the properties of `L.Icon.Default.prototype.options`\n   * (which is a set of `Icon options`).\n   *\n   * If you want to _completely_ replace the default icon, override the\n   * `L.Marker.prototype.options.icon` with your own icon instead.\n   */\n\n  var IconDefault = Icon.extend({\n    options: {\n      iconUrl: 'marker-icon.png',\n      iconRetinaUrl: 'marker-icon-2x.png',\n      shadowUrl: 'marker-shadow.png',\n      iconSize: [25, 41],\n      iconAnchor: [12, 41],\n      popupAnchor: [1, -34],\n      tooltipAnchor: [16, -28],\n      shadowSize: [41, 41]\n    },\n    _getIconUrl: function (name) {\n      if (typeof IconDefault.imagePath !== 'string') {\n        // Deprecated, backwards-compatibility only\n        IconDefault.imagePath = this._detectIconPath();\n      }\n\n      // @option imagePath: String\n      // `Icon.Default` will try to auto-detect the location of the\n      // blue icon images. If you are placing these images in a non-standard\n      // way, set this option to point to the right path.\n      return (this.options.imagePath || IconDefault.imagePath) + Icon.prototype._getIconUrl.call(this, name);\n    },\n    _stripUrl: function (path) {\n      // separate function to use in tests\n      var strip = function (str, re, idx) {\n        var match = re.exec(str);\n        return match && match[idx];\n      };\n      path = strip(path, /^url\\((['\"])?(.+)\\1\\)$/, 2);\n      return path && strip(path, /^(.*)marker-icon\\.png$/, 1);\n    },\n    _detectIconPath: function () {\n      var el = create$1('div', 'leaflet-default-icon-path', document.body);\n      var path = getStyle(el, 'background-image') || getStyle(el, 'backgroundImage'); // IE8\n\n      document.body.removeChild(el);\n      path = this._stripUrl(path);\n      if (path) {\n        return path;\n      }\n      var link = document.querySelector('link[href$=\"leaflet.css\"]');\n      if (!link) {\n        return '';\n      }\n      return link.href.substring(0, link.href.length - 'leaflet.css'.length - 1);\n    }\n  });\n\n  /*\n   * L.Handler.MarkerDrag is used internally by L.Marker to make the markers draggable.\n   */\n\n  /* @namespace Marker\n   * @section Interaction handlers\n   *\n   * Interaction handlers are properties of a marker instance that allow you to control interaction behavior in runtime, enabling or disabling certain features such as dragging (see `Handler` methods). Example:\n   *\n   * ```js\n   * marker.dragging.disable();\n   * ```\n   *\n   * @property dragging: Handler\n   * Marker dragging handler (by both mouse and touch). Only valid when the marker is on the map (Otherwise set [`marker.options.draggable`](#marker-draggable)).\n   */\n\n  var MarkerDrag = Handler.extend({\n    initialize: function (marker) {\n      this._marker = marker;\n    },\n    addHooks: function () {\n      var icon = this._marker._icon;\n      if (!this._draggable) {\n        this._draggable = new Draggable(icon, icon, true);\n      }\n      this._draggable.on({\n        dragstart: this._onDragStart,\n        predrag: this._onPreDrag,\n        drag: this._onDrag,\n        dragend: this._onDragEnd\n      }, this).enable();\n      addClass(icon, 'leaflet-marker-draggable');\n    },\n    removeHooks: function () {\n      this._draggable.off({\n        dragstart: this._onDragStart,\n        predrag: this._onPreDrag,\n        drag: this._onDrag,\n        dragend: this._onDragEnd\n      }, this).disable();\n      if (this._marker._icon) {\n        removeClass(this._marker._icon, 'leaflet-marker-draggable');\n      }\n    },\n    moved: function () {\n      return this._draggable && this._draggable._moved;\n    },\n    _adjustPan: function (e) {\n      var marker = this._marker,\n        map = marker._map,\n        speed = this._marker.options.autoPanSpeed,\n        padding = this._marker.options.autoPanPadding,\n        iconPos = getPosition(marker._icon),\n        bounds = map.getPixelBounds(),\n        origin = map.getPixelOrigin();\n      var panBounds = toBounds(bounds.min._subtract(origin).add(padding), bounds.max._subtract(origin).subtract(padding));\n      if (!panBounds.contains(iconPos)) {\n        // Compute incremental movement\n        var movement = toPoint((Math.max(panBounds.max.x, iconPos.x) - panBounds.max.x) / (bounds.max.x - panBounds.max.x) - (Math.min(panBounds.min.x, iconPos.x) - panBounds.min.x) / (bounds.min.x - panBounds.min.x), (Math.max(panBounds.max.y, iconPos.y) - panBounds.max.y) / (bounds.max.y - panBounds.max.y) - (Math.min(panBounds.min.y, iconPos.y) - panBounds.min.y) / (bounds.min.y - panBounds.min.y)).multiplyBy(speed);\n        map.panBy(movement, {\n          animate: false\n        });\n        this._draggable._newPos._add(movement);\n        this._draggable._startPos._add(movement);\n        setPosition(marker._icon, this._draggable._newPos);\n        this._onDrag(e);\n        this._panRequest = requestAnimFrame(this._adjustPan.bind(this, e));\n      }\n    },\n    _onDragStart: function () {\n      // @section Dragging events\n      // @event dragstart: Event\n      // Fired when the user starts dragging the marker.\n\n      // @event movestart: Event\n      // Fired when the marker starts moving (because of dragging).\n\n      this._oldLatLng = this._marker.getLatLng();\n\n      // When using ES6 imports it could not be set when `Popup` was not imported as well\n      this._marker.closePopup && this._marker.closePopup();\n      this._marker.fire('movestart').fire('dragstart');\n    },\n    _onPreDrag: function (e) {\n      if (this._marker.options.autoPan) {\n        cancelAnimFrame(this._panRequest);\n        this._panRequest = requestAnimFrame(this._adjustPan.bind(this, e));\n      }\n    },\n    _onDrag: function (e) {\n      var marker = this._marker,\n        shadow = marker._shadow,\n        iconPos = getPosition(marker._icon),\n        latlng = marker._map.layerPointToLatLng(iconPos);\n\n      // update shadow position\n      if (shadow) {\n        setPosition(shadow, iconPos);\n      }\n      marker._latlng = latlng;\n      e.latlng = latlng;\n      e.oldLatLng = this._oldLatLng;\n\n      // @event drag: Event\n      // Fired repeatedly while the user drags the marker.\n      marker.fire('move', e).fire('drag', e);\n    },\n    _onDragEnd: function (e) {\n      // @event dragend: DragEndEvent\n      // Fired when the user stops dragging the marker.\n\n      cancelAnimFrame(this._panRequest);\n\n      // @event moveend: Event\n      // Fired when the marker stops moving (because of dragging).\n      delete this._oldLatLng;\n      this._marker.fire('moveend').fire('dragend', e);\n    }\n  });\n\n  /*\r\n   * @class Marker\r\n   * @inherits Interactive layer\r\n   * @aka L.Marker\r\n   * L.Marker is used to display clickable/draggable icons on the map. Extends `Layer`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * L.marker([50.5, 30.5]).addTo(map);\r\n   * ```\r\n   */\n\n  var Marker = Layer.extend({\n    // @section\n    // @aka Marker options\n    options: {\n      // @option icon: Icon = *\n      // Icon instance to use for rendering the marker.\n      // See [Icon documentation](#L.Icon) for details on how to customize the marker icon.\n      // If not specified, a common instance of `L.Icon.Default` is used.\n      icon: new IconDefault(),\n      // Option inherited from \"Interactive layer\" abstract class\n      interactive: true,\n      // @option keyboard: Boolean = true\n      // Whether the marker can be tabbed to with a keyboard and clicked by pressing enter.\n      keyboard: true,\n      // @option title: String = ''\n      // Text for the browser tooltip that appear on marker hover (no tooltip by default).\n      // [Useful for accessibility](https://leafletjs.com/examples/accessibility/#markers-must-be-labelled).\n      title: '',\n      // @option alt: String = 'Marker'\n      // Text for the `alt` attribute of the icon image.\n      // [Useful for accessibility](https://leafletjs.com/examples/accessibility/#markers-must-be-labelled).\n      alt: 'Marker',\n      // @option zIndexOffset: Number = 0\n      // By default, marker images zIndex is set automatically based on its latitude. Use this option if you want to put the marker on top of all others (or below), specifying a high value like `1000` (or high negative value, respectively).\n      zIndexOffset: 0,\n      // @option opacity: Number = 1.0\n      // The opacity of the marker.\n      opacity: 1,\n      // @option riseOnHover: Boolean = false\n      // If `true`, the marker will get on top of others when you hover the mouse over it.\n      riseOnHover: false,\n      // @option riseOffset: Number = 250\n      // The z-index offset used for the `riseOnHover` feature.\n      riseOffset: 250,\n      // @option pane: String = 'markerPane'\n      // `Map pane` where the markers icon will be added.\n      pane: 'markerPane',\n      // @option shadowPane: String = 'shadowPane'\n      // `Map pane` where the markers shadow will be added.\n      shadowPane: 'shadowPane',\n      // @option bubblingMouseEvents: Boolean = false\n      // When `true`, a mouse event on this marker will trigger the same event on the map\n      // (unless [`L.DomEvent.stopPropagation`](#domevent-stoppropagation) is used).\n      bubblingMouseEvents: false,\n      // @option autoPanOnFocus: Boolean = true\n      // When `true`, the map will pan whenever the marker is focused (via\n      // e.g. pressing `tab` on the keyboard) to ensure the marker is\n      // visible within the map's bounds\n      autoPanOnFocus: true,\n      // @section Draggable marker options\n      // @option draggable: Boolean = false\n      // Whether the marker is draggable with mouse/touch or not.\n      draggable: false,\n      // @option autoPan: Boolean = false\n      // Whether to pan the map when dragging this marker near its edge or not.\n      autoPan: false,\n      // @option autoPanPadding: Point = Point(50, 50)\n      // Distance (in pixels to the left/right and to the top/bottom) of the\n      // map edge to start panning the map.\n      autoPanPadding: [50, 50],\n      // @option autoPanSpeed: Number = 10\n      // Number of pixels the map should pan by.\n      autoPanSpeed: 10\n    },\n    /* @section\r\n     *\r\n     * In addition to [shared layer methods](#Layer) like `addTo()` and `remove()` and [popup methods](#Popup) like bindPopup() you can also use the following methods:\r\n     */\n\n    initialize: function (latlng, options) {\n      setOptions(this, options);\n      this._latlng = toLatLng(latlng);\n    },\n    onAdd: function (map) {\n      this._zoomAnimated = this._zoomAnimated && map.options.markerZoomAnimation;\n      if (this._zoomAnimated) {\n        map.on('zoomanim', this._animateZoom, this);\n      }\n      this._initIcon();\n      this.update();\n    },\n    onRemove: function (map) {\n      if (this.dragging && this.dragging.enabled()) {\n        this.options.draggable = true;\n        this.dragging.removeHooks();\n      }\n      delete this.dragging;\n      if (this._zoomAnimated) {\n        map.off('zoomanim', this._animateZoom, this);\n      }\n      this._removeIcon();\n      this._removeShadow();\n    },\n    getEvents: function () {\n      return {\n        zoom: this.update,\n        viewreset: this.update\n      };\n    },\n    // @method getLatLng: LatLng\n    // Returns the current geographical position of the marker.\n    getLatLng: function () {\n      return this._latlng;\n    },\n    // @method setLatLng(latlng: LatLng): this\n    // Changes the marker position to the given point.\n    setLatLng: function (latlng) {\n      var oldLatLng = this._latlng;\n      this._latlng = toLatLng(latlng);\n      this.update();\n\n      // @event move: Event\n      // Fired when the marker is moved via [`setLatLng`](#marker-setlatlng) or by [dragging](#marker-dragging). Old and new coordinates are included in event arguments as `oldLatLng`, `latlng`.\n      return this.fire('move', {\n        oldLatLng: oldLatLng,\n        latlng: this._latlng\n      });\n    },\n    // @method setZIndexOffset(offset: Number): this\n    // Changes the [zIndex offset](#marker-zindexoffset) of the marker.\n    setZIndexOffset: function (offset) {\n      this.options.zIndexOffset = offset;\n      return this.update();\n    },\n    // @method getIcon: Icon\n    // Returns the current icon used by the marker\n    getIcon: function () {\n      return this.options.icon;\n    },\n    // @method setIcon(icon: Icon): this\n    // Changes the marker icon.\n    setIcon: function (icon) {\n      this.options.icon = icon;\n      if (this._map) {\n        this._initIcon();\n        this.update();\n      }\n      if (this._popup) {\n        this.bindPopup(this._popup, this._popup.options);\n      }\n      return this;\n    },\n    getElement: function () {\n      return this._icon;\n    },\n    update: function () {\n      if (this._icon && this._map) {\n        var pos = this._map.latLngToLayerPoint(this._latlng).round();\n        this._setPos(pos);\n      }\n      return this;\n    },\n    _initIcon: function () {\n      var options = this.options,\n        classToAdd = 'leaflet-zoom-' + (this._zoomAnimated ? 'animated' : 'hide');\n      var icon = options.icon.createIcon(this._icon),\n        addIcon = false;\n\n      // if we're not reusing the icon, remove the old one and init new one\n      if (icon !== this._icon) {\n        if (this._icon) {\n          this._removeIcon();\n        }\n        addIcon = true;\n        if (options.title) {\n          icon.title = options.title;\n        }\n        if (icon.tagName === 'IMG') {\n          icon.alt = options.alt || '';\n        }\n      }\n      addClass(icon, classToAdd);\n      if (options.keyboard) {\n        icon.tabIndex = '0';\n        icon.setAttribute('role', 'button');\n      }\n      this._icon = icon;\n      if (options.riseOnHover) {\n        this.on({\n          mouseover: this._bringToFront,\n          mouseout: this._resetZIndex\n        });\n      }\n      if (this.options.autoPanOnFocus) {\n        on(icon, 'focus', this._panOnFocus, this);\n      }\n      var newShadow = options.icon.createShadow(this._shadow),\n        addShadow = false;\n      if (newShadow !== this._shadow) {\n        this._removeShadow();\n        addShadow = true;\n      }\n      if (newShadow) {\n        addClass(newShadow, classToAdd);\n        newShadow.alt = '';\n      }\n      this._shadow = newShadow;\n      if (options.opacity < 1) {\n        this._updateOpacity();\n      }\n      if (addIcon) {\n        this.getPane().appendChild(this._icon);\n      }\n      this._initInteraction();\n      if (newShadow && addShadow) {\n        this.getPane(options.shadowPane).appendChild(this._shadow);\n      }\n    },\n    _removeIcon: function () {\n      if (this.options.riseOnHover) {\n        this.off({\n          mouseover: this._bringToFront,\n          mouseout: this._resetZIndex\n        });\n      }\n      if (this.options.autoPanOnFocus) {\n        off(this._icon, 'focus', this._panOnFocus, this);\n      }\n      remove(this._icon);\n      this.removeInteractiveTarget(this._icon);\n      this._icon = null;\n    },\n    _removeShadow: function () {\n      if (this._shadow) {\n        remove(this._shadow);\n      }\n      this._shadow = null;\n    },\n    _setPos: function (pos) {\n      if (this._icon) {\n        setPosition(this._icon, pos);\n      }\n      if (this._shadow) {\n        setPosition(this._shadow, pos);\n      }\n      this._zIndex = pos.y + this.options.zIndexOffset;\n      this._resetZIndex();\n    },\n    _updateZIndex: function (offset) {\n      if (this._icon) {\n        this._icon.style.zIndex = this._zIndex + offset;\n      }\n    },\n    _animateZoom: function (opt) {\n      var pos = this._map._latLngToNewLayerPoint(this._latlng, opt.zoom, opt.center).round();\n      this._setPos(pos);\n    },\n    _initInteraction: function () {\n      if (!this.options.interactive) {\n        return;\n      }\n      addClass(this._icon, 'leaflet-interactive');\n      this.addInteractiveTarget(this._icon);\n      if (MarkerDrag) {\n        var draggable = this.options.draggable;\n        if (this.dragging) {\n          draggable = this.dragging.enabled();\n          this.dragging.disable();\n        }\n        this.dragging = new MarkerDrag(this);\n        if (draggable) {\n          this.dragging.enable();\n        }\n      }\n    },\n    // @method setOpacity(opacity: Number): this\n    // Changes the opacity of the marker.\n    setOpacity: function (opacity) {\n      this.options.opacity = opacity;\n      if (this._map) {\n        this._updateOpacity();\n      }\n      return this;\n    },\n    _updateOpacity: function () {\n      var opacity = this.options.opacity;\n      if (this._icon) {\n        setOpacity(this._icon, opacity);\n      }\n      if (this._shadow) {\n        setOpacity(this._shadow, opacity);\n      }\n    },\n    _bringToFront: function () {\n      this._updateZIndex(this.options.riseOffset);\n    },\n    _resetZIndex: function () {\n      this._updateZIndex(0);\n    },\n    _panOnFocus: function () {\n      var map = this._map;\n      if (!map) {\n        return;\n      }\n      var iconOpts = this.options.icon.options;\n      var size = iconOpts.iconSize ? toPoint(iconOpts.iconSize) : toPoint(0, 0);\n      var anchor = iconOpts.iconAnchor ? toPoint(iconOpts.iconAnchor) : toPoint(0, 0);\n      map.panInside(this._latlng, {\n        paddingTopLeft: anchor,\n        paddingBottomRight: size.subtract(anchor)\n      });\n    },\n    _getPopupAnchor: function () {\n      return this.options.icon.options.popupAnchor;\n    },\n    _getTooltipAnchor: function () {\n      return this.options.icon.options.tooltipAnchor;\n    }\n  });\n\n  // factory L.marker(latlng: LatLng, options? : Marker options)\n\n  // @factory L.marker(latlng: LatLng, options? : Marker options)\n  // Instantiates a Marker object given a geographical point and optionally an options object.\n  function marker(latlng, options) {\n    return new Marker(latlng, options);\n  }\n\n  /*\n   * @class Path\n   * @aka L.Path\n   * @inherits Interactive layer\n   *\n   * An abstract class that contains options and constants shared between vector\n   * overlays (Polygon, Polyline, Circle). Do not use it directly. Extends `Layer`.\n   */\n\n  var Path = Layer.extend({\n    // @section\n    // @aka Path options\n    options: {\n      // @option stroke: Boolean = true\n      // Whether to draw stroke along the path. Set it to `false` to disable borders on polygons or circles.\n      stroke: true,\n      // @option color: String = '#3388ff'\n      // Stroke color\n      color: '#3388ff',\n      // @option weight: Number = 3\n      // Stroke width in pixels\n      weight: 3,\n      // @option opacity: Number = 1.0\n      // Stroke opacity\n      opacity: 1,\n      // @option lineCap: String= 'round'\n      // A string that defines [shape to be used at the end](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linecap) of the stroke.\n      lineCap: 'round',\n      // @option lineJoin: String = 'round'\n      // A string that defines [shape to be used at the corners](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linejoin) of the stroke.\n      lineJoin: 'round',\n      // @option dashArray: String = null\n      // A string that defines the stroke [dash pattern](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-dasharray). Doesn't work on `Canvas`-powered layers in [some old browsers](https://developer.mozilla.org/docs/Web/API/CanvasRenderingContext2D/setLineDash#Browser_compatibility).\n      dashArray: null,\n      // @option dashOffset: String = null\n      // A string that defines the [distance into the dash pattern to start the dash](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-dashoffset). Doesn't work on `Canvas`-powered layers in [some old browsers](https://developer.mozilla.org/docs/Web/API/CanvasRenderingContext2D/setLineDash#Browser_compatibility).\n      dashOffset: null,\n      // @option fill: Boolean = depends\n      // Whether to fill the path with color. Set it to `false` to disable filling on polygons or circles.\n      fill: false,\n      // @option fillColor: String = *\n      // Fill color. Defaults to the value of the [`color`](#path-color) option\n      fillColor: null,\n      // @option fillOpacity: Number = 0.2\n      // Fill opacity.\n      fillOpacity: 0.2,\n      // @option fillRule: String = 'evenodd'\n      // A string that defines [how the inside of a shape](https://developer.mozilla.org/docs/Web/SVG/Attribute/fill-rule) is determined.\n      fillRule: 'evenodd',\n      // className: '',\n\n      // Option inherited from \"Interactive layer\" abstract class\n      interactive: true,\n      // @option bubblingMouseEvents: Boolean = true\n      // When `true`, a mouse event on this path will trigger the same event on the map\n      // (unless [`L.DomEvent.stopPropagation`](#domevent-stoppropagation) is used).\n      bubblingMouseEvents: true\n    },\n    beforeAdd: function (map) {\n      // Renderer is set here because we need to call renderer.getEvents\n      // before this.getEvents.\n      this._renderer = map.getRenderer(this);\n    },\n    onAdd: function () {\n      this._renderer._initPath(this);\n      this._reset();\n      this._renderer._addPath(this);\n    },\n    onRemove: function () {\n      this._renderer._removePath(this);\n    },\n    // @method redraw(): this\n    // Redraws the layer. Sometimes useful after you changed the coordinates that the path uses.\n    redraw: function () {\n      if (this._map) {\n        this._renderer._updatePath(this);\n      }\n      return this;\n    },\n    // @method setStyle(style: Path options): this\n    // Changes the appearance of a Path based on the options in the `Path options` object.\n    setStyle: function (style) {\n      setOptions(this, style);\n      if (this._renderer) {\n        this._renderer._updateStyle(this);\n        if (this.options.stroke && style && Object.prototype.hasOwnProperty.call(style, 'weight')) {\n          this._updateBounds();\n        }\n      }\n      return this;\n    },\n    // @method bringToFront(): this\n    // Brings the layer to the top of all path layers.\n    bringToFront: function () {\n      if (this._renderer) {\n        this._renderer._bringToFront(this);\n      }\n      return this;\n    },\n    // @method bringToBack(): this\n    // Brings the layer to the bottom of all path layers.\n    bringToBack: function () {\n      if (this._renderer) {\n        this._renderer._bringToBack(this);\n      }\n      return this;\n    },\n    getElement: function () {\n      return this._path;\n    },\n    _reset: function () {\n      // defined in child classes\n      this._project();\n      this._update();\n    },\n    _clickTolerance: function () {\n      // used when doing hit detection for Canvas layers\n      return (this.options.stroke ? this.options.weight / 2 : 0) + (this._renderer.options.tolerance || 0);\n    }\n  });\n\n  /*\n   * @class CircleMarker\n   * @aka L.CircleMarker\n   * @inherits Path\n   *\n   * A circle of a fixed size with radius specified in pixels. Extends `Path`.\n   */\n\n  var CircleMarker = Path.extend({\n    // @section\n    // @aka CircleMarker options\n    options: {\n      fill: true,\n      // @option radius: Number = 10\n      // Radius of the circle marker, in pixels\n      radius: 10\n    },\n    initialize: function (latlng, options) {\n      setOptions(this, options);\n      this._latlng = toLatLng(latlng);\n      this._radius = this.options.radius;\n    },\n    // @method setLatLng(latLng: LatLng): this\n    // Sets the position of a circle marker to a new location.\n    setLatLng: function (latlng) {\n      var oldLatLng = this._latlng;\n      this._latlng = toLatLng(latlng);\n      this.redraw();\n\n      // @event move: Event\n      // Fired when the marker is moved via [`setLatLng`](#circlemarker-setlatlng). Old and new coordinates are included in event arguments as `oldLatLng`, `latlng`.\n      return this.fire('move', {\n        oldLatLng: oldLatLng,\n        latlng: this._latlng\n      });\n    },\n    // @method getLatLng(): LatLng\n    // Returns the current geographical position of the circle marker\n    getLatLng: function () {\n      return this._latlng;\n    },\n    // @method setRadius(radius: Number): this\n    // Sets the radius of a circle marker. Units are in pixels.\n    setRadius: function (radius) {\n      this.options.radius = this._radius = radius;\n      return this.redraw();\n    },\n    // @method getRadius(): Number\n    // Returns the current radius of the circle\n    getRadius: function () {\n      return this._radius;\n    },\n    setStyle: function (options) {\n      var radius = options && options.radius || this._radius;\n      Path.prototype.setStyle.call(this, options);\n      this.setRadius(radius);\n      return this;\n    },\n    _project: function () {\n      this._point = this._map.latLngToLayerPoint(this._latlng);\n      this._updateBounds();\n    },\n    _updateBounds: function () {\n      var r = this._radius,\n        r2 = this._radiusY || r,\n        w = this._clickTolerance(),\n        p = [r + w, r2 + w];\n      this._pxBounds = new Bounds(this._point.subtract(p), this._point.add(p));\n    },\n    _update: function () {\n      if (this._map) {\n        this._updatePath();\n      }\n    },\n    _updatePath: function () {\n      this._renderer._updateCircle(this);\n    },\n    _empty: function () {\n      return this._radius && !this._renderer._bounds.intersects(this._pxBounds);\n    },\n    // Needed by the `Canvas` renderer for interactivity\n    _containsPoint: function (p) {\n      return p.distanceTo(this._point) <= this._radius + this._clickTolerance();\n    }\n  });\n\n  // @factory L.circleMarker(latlng: LatLng, options?: CircleMarker options)\n  // Instantiates a circle marker object given a geographical point, and an optional options object.\n  function circleMarker(latlng, options) {\n    return new CircleMarker(latlng, options);\n  }\n\n  /*\n   * @class Circle\n   * @aka L.Circle\n   * @inherits CircleMarker\n   *\n   * A class for drawing circle overlays on a map. Extends `CircleMarker`.\n   *\n   * It's an approximation and starts to diverge from a real circle closer to poles (due to projection distortion).\n   *\n   * @example\n   *\n   * ```js\n   * L.circle([50.5, 30.5], {radius: 200}).addTo(map);\n   * ```\n   */\n\n  var Circle = CircleMarker.extend({\n    initialize: function (latlng, options, legacyOptions) {\n      if (typeof options === 'number') {\n        // Backwards compatibility with 0.7.x factory (latlng, radius, options?)\n        options = extend({}, legacyOptions, {\n          radius: options\n        });\n      }\n      setOptions(this, options);\n      this._latlng = toLatLng(latlng);\n      if (isNaN(this.options.radius)) {\n        throw new Error('Circle radius cannot be NaN');\n      }\n\n      // @section\n      // @aka Circle options\n      // @option radius: Number; Radius of the circle, in meters.\n      this._mRadius = this.options.radius;\n    },\n    // @method setRadius(radius: Number): this\n    // Sets the radius of a circle. Units are in meters.\n    setRadius: function (radius) {\n      this._mRadius = radius;\n      return this.redraw();\n    },\n    // @method getRadius(): Number\n    // Returns the current radius of a circle. Units are in meters.\n    getRadius: function () {\n      return this._mRadius;\n    },\n    // @method getBounds(): LatLngBounds\n    // Returns the `LatLngBounds` of the path.\n    getBounds: function () {\n      var half = [this._radius, this._radiusY || this._radius];\n      return new LatLngBounds(this._map.layerPointToLatLng(this._point.subtract(half)), this._map.layerPointToLatLng(this._point.add(half)));\n    },\n    setStyle: Path.prototype.setStyle,\n    _project: function () {\n      var lng = this._latlng.lng,\n        lat = this._latlng.lat,\n        map = this._map,\n        crs = map.options.crs;\n      if (crs.distance === Earth.distance) {\n        var d = Math.PI / 180,\n          latR = this._mRadius / Earth.R / d,\n          top = map.project([lat + latR, lng]),\n          bottom = map.project([lat - latR, lng]),\n          p = top.add(bottom).divideBy(2),\n          lat2 = map.unproject(p).lat,\n          lngR = Math.acos((Math.cos(latR * d) - Math.sin(lat * d) * Math.sin(lat2 * d)) / (Math.cos(lat * d) * Math.cos(lat2 * d))) / d;\n        if (isNaN(lngR) || lngR === 0) {\n          lngR = latR / Math.cos(Math.PI / 180 * lat); // Fallback for edge case, #2425\n        }\n\n        this._point = p.subtract(map.getPixelOrigin());\n        this._radius = isNaN(lngR) ? 0 : p.x - map.project([lat2, lng - lngR]).x;\n        this._radiusY = p.y - top.y;\n      } else {\n        var latlng2 = crs.unproject(crs.project(this._latlng).subtract([this._mRadius, 0]));\n        this._point = map.latLngToLayerPoint(this._latlng);\n        this._radius = this._point.x - map.latLngToLayerPoint(latlng2).x;\n      }\n      this._updateBounds();\n    }\n  });\n\n  // @factory L.circle(latlng: LatLng, options?: Circle options)\n  // Instantiates a circle object given a geographical point, and an options object\n  // which contains the circle radius.\n  // @alternative\n  // @factory L.circle(latlng: LatLng, radius: Number, options?: Circle options)\n  // Obsolete way of instantiating a circle, for compatibility with 0.7.x code.\n  // Do not use in new applications or plugins.\n  function circle(latlng, options, legacyOptions) {\n    return new Circle(latlng, options, legacyOptions);\n  }\n\n  /*\n   * @class Polyline\n   * @aka L.Polyline\n   * @inherits Path\n   *\n   * A class for drawing polyline overlays on a map. Extends `Path`.\n   *\n   * @example\n   *\n   * ```js\n   * // create a red polyline from an array of LatLng points\n   * var latlngs = [\n   * \t[45.51, -122.68],\n   * \t[37.77, -122.43],\n   * \t[34.04, -118.2]\n   * ];\n   *\n   * var polyline = L.polyline(latlngs, {color: 'red'}).addTo(map);\n   *\n   * // zoom the map to the polyline\n   * map.fitBounds(polyline.getBounds());\n   * ```\n   *\n   * You can also pass a multi-dimensional array to represent a `MultiPolyline` shape:\n   *\n   * ```js\n   * // create a red polyline from an array of arrays of LatLng points\n   * var latlngs = [\n   * \t[[45.51, -122.68],\n   * \t [37.77, -122.43],\n   * \t [34.04, -118.2]],\n   * \t[[40.78, -73.91],\n   * \t [41.83, -87.62],\n   * \t [32.76, -96.72]]\n   * ];\n   * ```\n   */\n\n  var Polyline = Path.extend({\n    // @section\n    // @aka Polyline options\n    options: {\n      // @option smoothFactor: Number = 1.0\n      // How much to simplify the polyline on each zoom level. More means\n      // better performance and smoother look, and less means more accurate representation.\n      smoothFactor: 1.0,\n      // @option noClip: Boolean = false\n      // Disable polyline clipping.\n      noClip: false\n    },\n    initialize: function (latlngs, options) {\n      setOptions(this, options);\n      this._setLatLngs(latlngs);\n    },\n    // @method getLatLngs(): LatLng[]\n    // Returns an array of the points in the path, or nested arrays of points in case of multi-polyline.\n    getLatLngs: function () {\n      return this._latlngs;\n    },\n    // @method setLatLngs(latlngs: LatLng[]): this\n    // Replaces all the points in the polyline with the given array of geographical points.\n    setLatLngs: function (latlngs) {\n      this._setLatLngs(latlngs);\n      return this.redraw();\n    },\n    // @method isEmpty(): Boolean\n    // Returns `true` if the Polyline has no LatLngs.\n    isEmpty: function () {\n      return !this._latlngs.length;\n    },\n    // @method closestLayerPoint(p: Point): Point\n    // Returns the point closest to `p` on the Polyline.\n    closestLayerPoint: function (p) {\n      var minDistance = Infinity,\n        minPoint = null,\n        closest = _sqClosestPointOnSegment,\n        p1,\n        p2;\n      for (var j = 0, jLen = this._parts.length; j < jLen; j++) {\n        var points = this._parts[j];\n        for (var i = 1, len = points.length; i < len; i++) {\n          p1 = points[i - 1];\n          p2 = points[i];\n          var sqDist = closest(p, p1, p2, true);\n          if (sqDist < minDistance) {\n            minDistance = sqDist;\n            minPoint = closest(p, p1, p2);\n          }\n        }\n      }\n      if (minPoint) {\n        minPoint.distance = Math.sqrt(minDistance);\n      }\n      return minPoint;\n    },\n    // @method getCenter(): LatLng\n    // Returns the center ([centroid](https://en.wikipedia.org/wiki/Centroid)) of the polyline.\n    getCenter: function () {\n      // throws error when not yet added to map as this center calculation requires projected coordinates\n      if (!this._map) {\n        throw new Error('Must add layer to map before using getCenter()');\n      }\n      return polylineCenter(this._defaultShape(), this._map.options.crs);\n    },\n    // @method getBounds(): LatLngBounds\n    // Returns the `LatLngBounds` of the path.\n    getBounds: function () {\n      return this._bounds;\n    },\n    // @method addLatLng(latlng: LatLng, latlngs?: LatLng[]): this\n    // Adds a given point to the polyline. By default, adds to the first ring of\n    // the polyline in case of a multi-polyline, but can be overridden by passing\n    // a specific ring as a LatLng array (that you can earlier access with [`getLatLngs`](#polyline-getlatlngs)).\n    addLatLng: function (latlng, latlngs) {\n      latlngs = latlngs || this._defaultShape();\n      latlng = toLatLng(latlng);\n      latlngs.push(latlng);\n      this._bounds.extend(latlng);\n      return this.redraw();\n    },\n    _setLatLngs: function (latlngs) {\n      this._bounds = new LatLngBounds();\n      this._latlngs = this._convertLatLngs(latlngs);\n    },\n    _defaultShape: function () {\n      return isFlat(this._latlngs) ? this._latlngs : this._latlngs[0];\n    },\n    // recursively convert latlngs input into actual LatLng instances; calculate bounds along the way\n    _convertLatLngs: function (latlngs) {\n      var result = [],\n        flat = isFlat(latlngs);\n      for (var i = 0, len = latlngs.length; i < len; i++) {\n        if (flat) {\n          result[i] = toLatLng(latlngs[i]);\n          this._bounds.extend(result[i]);\n        } else {\n          result[i] = this._convertLatLngs(latlngs[i]);\n        }\n      }\n      return result;\n    },\n    _project: function () {\n      var pxBounds = new Bounds();\n      this._rings = [];\n      this._projectLatlngs(this._latlngs, this._rings, pxBounds);\n      if (this._bounds.isValid() && pxBounds.isValid()) {\n        this._rawPxBounds = pxBounds;\n        this._updateBounds();\n      }\n    },\n    _updateBounds: function () {\n      var w = this._clickTolerance(),\n        p = new Point(w, w);\n      if (!this._rawPxBounds) {\n        return;\n      }\n      this._pxBounds = new Bounds([this._rawPxBounds.min.subtract(p), this._rawPxBounds.max.add(p)]);\n    },\n    // recursively turns latlngs into a set of rings with projected coordinates\n    _projectLatlngs: function (latlngs, result, projectedBounds) {\n      var flat = latlngs[0] instanceof LatLng,\n        len = latlngs.length,\n        i,\n        ring;\n      if (flat) {\n        ring = [];\n        for (i = 0; i < len; i++) {\n          ring[i] = this._map.latLngToLayerPoint(latlngs[i]);\n          projectedBounds.extend(ring[i]);\n        }\n        result.push(ring);\n      } else {\n        for (i = 0; i < len; i++) {\n          this._projectLatlngs(latlngs[i], result, projectedBounds);\n        }\n      }\n    },\n    // clip polyline by renderer bounds so that we have less to render for performance\n    _clipPoints: function () {\n      var bounds = this._renderer._bounds;\n      this._parts = [];\n      if (!this._pxBounds || !this._pxBounds.intersects(bounds)) {\n        return;\n      }\n      if (this.options.noClip) {\n        this._parts = this._rings;\n        return;\n      }\n      var parts = this._parts,\n        i,\n        j,\n        k,\n        len,\n        len2,\n        segment,\n        points;\n      for (i = 0, k = 0, len = this._rings.length; i < len; i++) {\n        points = this._rings[i];\n        for (j = 0, len2 = points.length; j < len2 - 1; j++) {\n          segment = clipSegment(points[j], points[j + 1], bounds, j, true);\n          if (!segment) {\n            continue;\n          }\n          parts[k] = parts[k] || [];\n          parts[k].push(segment[0]);\n\n          // if segment goes out of screen, or it's the last one, it's the end of the line part\n          if (segment[1] !== points[j + 1] || j === len2 - 2) {\n            parts[k].push(segment[1]);\n            k++;\n          }\n        }\n      }\n    },\n    // simplify each clipped part of the polyline for performance\n    _simplifyPoints: function () {\n      var parts = this._parts,\n        tolerance = this.options.smoothFactor;\n      for (var i = 0, len = parts.length; i < len; i++) {\n        parts[i] = simplify(parts[i], tolerance);\n      }\n    },\n    _update: function () {\n      if (!this._map) {\n        return;\n      }\n      this._clipPoints();\n      this._simplifyPoints();\n      this._updatePath();\n    },\n    _updatePath: function () {\n      this._renderer._updatePoly(this);\n    },\n    // Needed by the `Canvas` renderer for interactivity\n    _containsPoint: function (p, closed) {\n      var i,\n        j,\n        k,\n        len,\n        len2,\n        part,\n        w = this._clickTolerance();\n      if (!this._pxBounds || !this._pxBounds.contains(p)) {\n        return false;\n      }\n\n      // hit detection for polylines\n      for (i = 0, len = this._parts.length; i < len; i++) {\n        part = this._parts[i];\n        for (j = 0, len2 = part.length, k = len2 - 1; j < len2; k = j++) {\n          if (!closed && j === 0) {\n            continue;\n          }\n          if (pointToSegmentDistance(p, part[k], part[j]) <= w) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n  });\n\n  // @factory L.polyline(latlngs: LatLng[], options?: Polyline options)\n  // Instantiates a polyline object given an array of geographical points and\n  // optionally an options object. You can create a `Polyline` object with\n  // multiple separate lines (`MultiPolyline`) by passing an array of arrays\n  // of geographic points.\n  function polyline(latlngs, options) {\n    return new Polyline(latlngs, options);\n  }\n\n  // Retrocompat. Allow plugins to support Leaflet versions before and after 1.1.\n  Polyline._flat = _flat;\n\n  /*\n   * @class Polygon\n   * @aka L.Polygon\n   * @inherits Polyline\n   *\n   * A class for drawing polygon overlays on a map. Extends `Polyline`.\n   *\n   * Note that points you pass when creating a polygon shouldn't have an additional last point equal to the first one — it's better to filter out such points.\n   *\n   *\n   * @example\n   *\n   * ```js\n   * // create a red polygon from an array of LatLng points\n   * var latlngs = [[37, -109.05],[41, -109.03],[41, -102.05],[37, -102.04]];\n   *\n   * var polygon = L.polygon(latlngs, {color: 'red'}).addTo(map);\n   *\n   * // zoom the map to the polygon\n   * map.fitBounds(polygon.getBounds());\n   * ```\n   *\n   * You can also pass an array of arrays of latlngs, with the first array representing the outer shape and the other arrays representing holes in the outer shape:\n   *\n   * ```js\n   * var latlngs = [\n   *   [[37, -109.05],[41, -109.03],[41, -102.05],[37, -102.04]], // outer ring\n   *   [[37.29, -108.58],[40.71, -108.58],[40.71, -102.50],[37.29, -102.50]] // hole\n   * ];\n   * ```\n   *\n   * Additionally, you can pass a multi-dimensional array to represent a MultiPolygon shape.\n   *\n   * ```js\n   * var latlngs = [\n   *   [ // first polygon\n   *     [[37, -109.05],[41, -109.03],[41, -102.05],[37, -102.04]], // outer ring\n   *     [[37.29, -108.58],[40.71, -108.58],[40.71, -102.50],[37.29, -102.50]] // hole\n   *   ],\n   *   [ // second polygon\n   *     [[41, -111.03],[45, -111.04],[45, -104.05],[41, -104.05]]\n   *   ]\n   * ];\n   * ```\n   */\n\n  var Polygon = Polyline.extend({\n    options: {\n      fill: true\n    },\n    isEmpty: function () {\n      return !this._latlngs.length || !this._latlngs[0].length;\n    },\n    // @method getCenter(): LatLng\n    // Returns the center ([centroid](http://en.wikipedia.org/wiki/Centroid)) of the Polygon.\n    getCenter: function () {\n      // throws error when not yet added to map as this center calculation requires projected coordinates\n      if (!this._map) {\n        throw new Error('Must add layer to map before using getCenter()');\n      }\n      return polygonCenter(this._defaultShape(), this._map.options.crs);\n    },\n    _convertLatLngs: function (latlngs) {\n      var result = Polyline.prototype._convertLatLngs.call(this, latlngs),\n        len = result.length;\n\n      // remove last point if it equals first one\n      if (len >= 2 && result[0] instanceof LatLng && result[0].equals(result[len - 1])) {\n        result.pop();\n      }\n      return result;\n    },\n    _setLatLngs: function (latlngs) {\n      Polyline.prototype._setLatLngs.call(this, latlngs);\n      if (isFlat(this._latlngs)) {\n        this._latlngs = [this._latlngs];\n      }\n    },\n    _defaultShape: function () {\n      return isFlat(this._latlngs[0]) ? this._latlngs[0] : this._latlngs[0][0];\n    },\n    _clipPoints: function () {\n      // polygons need a different clipping algorithm so we redefine that\n\n      var bounds = this._renderer._bounds,\n        w = this.options.weight,\n        p = new Point(w, w);\n\n      // increase clip padding by stroke width to avoid stroke on clip edges\n      bounds = new Bounds(bounds.min.subtract(p), bounds.max.add(p));\n      this._parts = [];\n      if (!this._pxBounds || !this._pxBounds.intersects(bounds)) {\n        return;\n      }\n      if (this.options.noClip) {\n        this._parts = this._rings;\n        return;\n      }\n      for (var i = 0, len = this._rings.length, clipped; i < len; i++) {\n        clipped = clipPolygon(this._rings[i], bounds, true);\n        if (clipped.length) {\n          this._parts.push(clipped);\n        }\n      }\n    },\n    _updatePath: function () {\n      this._renderer._updatePoly(this, true);\n    },\n    // Needed by the `Canvas` renderer for interactivity\n    _containsPoint: function (p) {\n      var inside = false,\n        part,\n        p1,\n        p2,\n        i,\n        j,\n        k,\n        len,\n        len2;\n      if (!this._pxBounds || !this._pxBounds.contains(p)) {\n        return false;\n      }\n\n      // ray casting algorithm for detecting if point is in polygon\n      for (i = 0, len = this._parts.length; i < len; i++) {\n        part = this._parts[i];\n        for (j = 0, len2 = part.length, k = len2 - 1; j < len2; k = j++) {\n          p1 = part[j];\n          p2 = part[k];\n          if (p1.y > p.y !== p2.y > p.y && p.x < (p2.x - p1.x) * (p.y - p1.y) / (p2.y - p1.y) + p1.x) {\n            inside = !inside;\n          }\n        }\n      }\n\n      // also check if it's on polygon stroke\n      return inside || Polyline.prototype._containsPoint.call(this, p, true);\n    }\n  });\n\n  // @factory L.polygon(latlngs: LatLng[], options?: Polyline options)\n  function polygon(latlngs, options) {\n    return new Polygon(latlngs, options);\n  }\n\n  /*\r\n   * @class GeoJSON\r\n   * @aka L.GeoJSON\r\n   * @inherits FeatureGroup\r\n   *\r\n   * Represents a GeoJSON object or an array of GeoJSON objects. Allows you to parse\r\n   * GeoJSON data and display it on the map. Extends `FeatureGroup`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * L.geoJSON(data, {\r\n   * \tstyle: function (feature) {\r\n   * \t\treturn {color: feature.properties.color};\r\n   * \t}\r\n   * }).bindPopup(function (layer) {\r\n   * \treturn layer.feature.properties.description;\r\n   * }).addTo(map);\r\n   * ```\r\n   */\n\n  var GeoJSON = FeatureGroup.extend({\n    /* @section\r\n     * @aka GeoJSON options\r\n     *\r\n     * @option pointToLayer: Function = *\r\n     * A `Function` defining how GeoJSON points spawn Leaflet layers. It is internally\r\n     * called when data is added, passing the GeoJSON point feature and its `LatLng`.\r\n     * The default is to spawn a default `Marker`:\r\n     * ```js\r\n     * function(geoJsonPoint, latlng) {\r\n     * \treturn L.marker(latlng);\r\n     * }\r\n     * ```\r\n     *\r\n     * @option style: Function = *\r\n     * A `Function` defining the `Path options` for styling GeoJSON lines and polygons,\r\n     * called internally when data is added.\r\n     * The default value is to not override any defaults:\r\n     * ```js\r\n     * function (geoJsonFeature) {\r\n     * \treturn {}\r\n     * }\r\n     * ```\r\n     *\r\n     * @option onEachFeature: Function = *\r\n     * A `Function` that will be called once for each created `Feature`, after it has\r\n     * been created and styled. Useful for attaching events and popups to features.\r\n     * The default is to do nothing with the newly created layers:\r\n     * ```js\r\n     * function (feature, layer) {}\r\n     * ```\r\n     *\r\n     * @option filter: Function = *\r\n     * A `Function` that will be used to decide whether to include a feature or not.\r\n     * The default is to include all features:\r\n     * ```js\r\n     * function (geoJsonFeature) {\r\n     * \treturn true;\r\n     * }\r\n     * ```\r\n     * Note: dynamically changing the `filter` option will have effect only on newly\r\n     * added data. It will _not_ re-evaluate already included features.\r\n     *\r\n     * @option coordsToLatLng: Function = *\r\n     * A `Function` that will be used for converting GeoJSON coordinates to `LatLng`s.\r\n     * The default is the `coordsToLatLng` static method.\r\n     *\r\n     * @option markersInheritOptions: Boolean = false\r\n     * Whether default Markers for \"Point\" type Features inherit from group options.\r\n     */\n\n    initialize: function (geojson, options) {\n      setOptions(this, options);\n      this._layers = {};\n      if (geojson) {\n        this.addData(geojson);\n      }\n    },\n    // @method addData( <GeoJSON> data ): this\n    // Adds a GeoJSON object to the layer.\n    addData: function (geojson) {\n      var features = isArray(geojson) ? geojson : geojson.features,\n        i,\n        len,\n        feature;\n      if (features) {\n        for (i = 0, len = features.length; i < len; i++) {\n          // only add this if geometry or geometries are set and not null\n          feature = features[i];\n          if (feature.geometries || feature.geometry || feature.features || feature.coordinates) {\n            this.addData(feature);\n          }\n        }\n        return this;\n      }\n      var options = this.options;\n      if (options.filter && !options.filter(geojson)) {\n        return this;\n      }\n      var layer = geometryToLayer(geojson, options);\n      if (!layer) {\n        return this;\n      }\n      layer.feature = asFeature(geojson);\n      layer.defaultOptions = layer.options;\n      this.resetStyle(layer);\n      if (options.onEachFeature) {\n        options.onEachFeature(geojson, layer);\n      }\n      return this.addLayer(layer);\n    },\n    // @method resetStyle( <Path> layer? ): this\n    // Resets the given vector layer's style to the original GeoJSON style, useful for resetting style after hover events.\n    // If `layer` is omitted, the style of all features in the current layer is reset.\n    resetStyle: function (layer) {\n      if (layer === undefined) {\n        return this.eachLayer(this.resetStyle, this);\n      }\n      // reset any custom styles\n      layer.options = extend({}, layer.defaultOptions);\n      this._setLayerStyle(layer, this.options.style);\n      return this;\n    },\n    // @method setStyle( <Function> style ): this\n    // Changes styles of GeoJSON vector layers with the given style function.\n    setStyle: function (style) {\n      return this.eachLayer(function (layer) {\n        this._setLayerStyle(layer, style);\n      }, this);\n    },\n    _setLayerStyle: function (layer, style) {\n      if (layer.setStyle) {\n        if (typeof style === 'function') {\n          style = style(layer.feature);\n        }\n        layer.setStyle(style);\n      }\n    }\n  });\n\n  // @section\n  // There are several static functions which can be called without instantiating L.GeoJSON:\n\n  // @function geometryToLayer(featureData: Object, options?: GeoJSON options): Layer\n  // Creates a `Layer` from a given GeoJSON feature. Can use a custom\n  // [`pointToLayer`](#geojson-pointtolayer) and/or [`coordsToLatLng`](#geojson-coordstolatlng)\n  // functions if provided as options.\n  function geometryToLayer(geojson, options) {\n    var geometry = geojson.type === 'Feature' ? geojson.geometry : geojson,\n      coords = geometry ? geometry.coordinates : null,\n      layers = [],\n      pointToLayer = options && options.pointToLayer,\n      _coordsToLatLng = options && options.coordsToLatLng || coordsToLatLng,\n      latlng,\n      latlngs,\n      i,\n      len;\n    if (!coords && !geometry) {\n      return null;\n    }\n    switch (geometry.type) {\n      case 'Point':\n        latlng = _coordsToLatLng(coords);\n        return _pointToLayer(pointToLayer, geojson, latlng, options);\n      case 'MultiPoint':\n        for (i = 0, len = coords.length; i < len; i++) {\n          latlng = _coordsToLatLng(coords[i]);\n          layers.push(_pointToLayer(pointToLayer, geojson, latlng, options));\n        }\n        return new FeatureGroup(layers);\n      case 'LineString':\n      case 'MultiLineString':\n        latlngs = coordsToLatLngs(coords, geometry.type === 'LineString' ? 0 : 1, _coordsToLatLng);\n        return new Polyline(latlngs, options);\n      case 'Polygon':\n      case 'MultiPolygon':\n        latlngs = coordsToLatLngs(coords, geometry.type === 'Polygon' ? 1 : 2, _coordsToLatLng);\n        return new Polygon(latlngs, options);\n      case 'GeometryCollection':\n        for (i = 0, len = geometry.geometries.length; i < len; i++) {\n          var geoLayer = geometryToLayer({\n            geometry: geometry.geometries[i],\n            type: 'Feature',\n            properties: geojson.properties\n          }, options);\n          if (geoLayer) {\n            layers.push(geoLayer);\n          }\n        }\n        return new FeatureGroup(layers);\n      case 'FeatureCollection':\n        for (i = 0, len = geometry.features.length; i < len; i++) {\n          var featureLayer = geometryToLayer(geometry.features[i], options);\n          if (featureLayer) {\n            layers.push(featureLayer);\n          }\n        }\n        return new FeatureGroup(layers);\n      default:\n        throw new Error('Invalid GeoJSON object.');\n    }\n  }\n  function _pointToLayer(pointToLayerFn, geojson, latlng, options) {\n    return pointToLayerFn ? pointToLayerFn(geojson, latlng) : new Marker(latlng, options && options.markersInheritOptions && options);\n  }\n\n  // @function coordsToLatLng(coords: Array): LatLng\n  // Creates a `LatLng` object from an array of 2 numbers (longitude, latitude)\n  // or 3 numbers (longitude, latitude, altitude) used in GeoJSON for points.\n  function coordsToLatLng(coords) {\n    return new LatLng(coords[1], coords[0], coords[2]);\n  }\n\n  // @function coordsToLatLngs(coords: Array, levelsDeep?: Number, coordsToLatLng?: Function): Array\n  // Creates a multidimensional array of `LatLng`s from a GeoJSON coordinates array.\n  // `levelsDeep` specifies the nesting level (0 is for an array of points, 1 for an array of arrays of points, etc., 0 by default).\n  // Can use a custom [`coordsToLatLng`](#geojson-coordstolatlng) function.\n  function coordsToLatLngs(coords, levelsDeep, _coordsToLatLng) {\n    var latlngs = [];\n    for (var i = 0, len = coords.length, latlng; i < len; i++) {\n      latlng = levelsDeep ? coordsToLatLngs(coords[i], levelsDeep - 1, _coordsToLatLng) : (_coordsToLatLng || coordsToLatLng)(coords[i]);\n      latlngs.push(latlng);\n    }\n    return latlngs;\n  }\n\n  // @function latLngToCoords(latlng: LatLng, precision?: Number|false): Array\n  // Reverse of [`coordsToLatLng`](#geojson-coordstolatlng)\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function.\n  function latLngToCoords(latlng, precision) {\n    latlng = toLatLng(latlng);\n    return latlng.alt !== undefined ? [formatNum(latlng.lng, precision), formatNum(latlng.lat, precision), formatNum(latlng.alt, precision)] : [formatNum(latlng.lng, precision), formatNum(latlng.lat, precision)];\n  }\n\n  // @function latLngsToCoords(latlngs: Array, levelsDeep?: Number, closed?: Boolean, precision?: Number|false): Array\n  // Reverse of [`coordsToLatLngs`](#geojson-coordstolatlngs)\n  // `closed` determines whether the first point should be appended to the end of the array to close the feature, only used when `levelsDeep` is 0. False by default.\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function.\n  function latLngsToCoords(latlngs, levelsDeep, closed, precision) {\n    var coords = [];\n    for (var i = 0, len = latlngs.length; i < len; i++) {\n      // Check for flat arrays required to ensure unbalanced arrays are correctly converted in recursion\n      coords.push(levelsDeep ? latLngsToCoords(latlngs[i], isFlat(latlngs[i]) ? 0 : levelsDeep - 1, closed, precision) : latLngToCoords(latlngs[i], precision));\n    }\n    if (!levelsDeep && closed && coords.length > 0) {\n      coords.push(coords[0].slice());\n    }\n    return coords;\n  }\n  function getFeature(layer, newGeometry) {\n    return layer.feature ? extend({}, layer.feature, {\n      geometry: newGeometry\n    }) : asFeature(newGeometry);\n  }\n\n  // @function asFeature(geojson: Object): Object\n  // Normalize GeoJSON geometries/features into GeoJSON features.\n  function asFeature(geojson) {\n    if (geojson.type === 'Feature' || geojson.type === 'FeatureCollection') {\n      return geojson;\n    }\n    return {\n      type: 'Feature',\n      properties: {},\n      geometry: geojson\n    };\n  }\n  var PointToGeoJSON = {\n    toGeoJSON: function (precision) {\n      return getFeature(this, {\n        type: 'Point',\n        coordinates: latLngToCoords(this.getLatLng(), precision)\n      });\n    }\n  };\n\n  // @namespace Marker\n  // @section Other methods\n  // @method toGeoJSON(precision?: Number|false): Object\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function with given `precision`.\n  // Returns a [`GeoJSON`](https://en.wikipedia.org/wiki/GeoJSON) representation of the marker (as a GeoJSON `Point` Feature).\n  Marker.include(PointToGeoJSON);\n\n  // @namespace CircleMarker\n  // @method toGeoJSON(precision?: Number|false): Object\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function with given `precision`.\n  // Returns a [`GeoJSON`](https://en.wikipedia.org/wiki/GeoJSON) representation of the circle marker (as a GeoJSON `Point` Feature).\n  Circle.include(PointToGeoJSON);\n  CircleMarker.include(PointToGeoJSON);\n\n  // @namespace Polyline\n  // @method toGeoJSON(precision?: Number|false): Object\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function with given `precision`.\n  // Returns a [`GeoJSON`](https://en.wikipedia.org/wiki/GeoJSON) representation of the polyline (as a GeoJSON `LineString` or `MultiLineString` Feature).\n  Polyline.include({\n    toGeoJSON: function (precision) {\n      var multi = !isFlat(this._latlngs);\n      var coords = latLngsToCoords(this._latlngs, multi ? 1 : 0, false, precision);\n      return getFeature(this, {\n        type: (multi ? 'Multi' : '') + 'LineString',\n        coordinates: coords\n      });\n    }\n  });\n\n  // @namespace Polygon\n  // @method toGeoJSON(precision?: Number|false): Object\n  // Coordinates values are rounded with [`formatNum`](#util-formatnum) function with given `precision`.\n  // Returns a [`GeoJSON`](https://en.wikipedia.org/wiki/GeoJSON) representation of the polygon (as a GeoJSON `Polygon` or `MultiPolygon` Feature).\n  Polygon.include({\n    toGeoJSON: function (precision) {\n      var holes = !isFlat(this._latlngs),\n        multi = holes && !isFlat(this._latlngs[0]);\n      var coords = latLngsToCoords(this._latlngs, multi ? 2 : holes ? 1 : 0, true, precision);\n      if (!holes) {\n        coords = [coords];\n      }\n      return getFeature(this, {\n        type: (multi ? 'Multi' : '') + 'Polygon',\n        coordinates: coords\n      });\n    }\n  });\n\n  // @namespace LayerGroup\n  LayerGroup.include({\n    toMultiPoint: function (precision) {\n      var coords = [];\n      this.eachLayer(function (layer) {\n        coords.push(layer.toGeoJSON(precision).geometry.coordinates);\n      });\n      return getFeature(this, {\n        type: 'MultiPoint',\n        coordinates: coords\n      });\n    },\n    // @method toGeoJSON(precision?: Number|false): Object\n    // Coordinates values are rounded with [`formatNum`](#util-formatnum) function with given `precision`.\n    // Returns a [`GeoJSON`](https://en.wikipedia.org/wiki/GeoJSON) representation of the layer group (as a GeoJSON `FeatureCollection`, `GeometryCollection`, or `MultiPoint`).\n    toGeoJSON: function (precision) {\n      var type = this.feature && this.feature.geometry && this.feature.geometry.type;\n      if (type === 'MultiPoint') {\n        return this.toMultiPoint(precision);\n      }\n      var isGeometryCollection = type === 'GeometryCollection',\n        jsons = [];\n      this.eachLayer(function (layer) {\n        if (layer.toGeoJSON) {\n          var json = layer.toGeoJSON(precision);\n          if (isGeometryCollection) {\n            jsons.push(json.geometry);\n          } else {\n            var feature = asFeature(json);\n            // Squash nested feature collections\n            if (feature.type === 'FeatureCollection') {\n              jsons.push.apply(jsons, feature.features);\n            } else {\n              jsons.push(feature);\n            }\n          }\n        }\n      });\n      if (isGeometryCollection) {\n        return getFeature(this, {\n          geometries: jsons,\n          type: 'GeometryCollection'\n        });\n      }\n      return {\n        type: 'FeatureCollection',\n        features: jsons\n      };\n    }\n  });\n\n  // @namespace GeoJSON\n  // @factory L.geoJSON(geojson?: Object, options?: GeoJSON options)\n  // Creates a GeoJSON layer. Optionally accepts an object in\n  // [GeoJSON format](https://tools.ietf.org/html/rfc7946) to display on the map\n  // (you can alternatively add it later with `addData` method) and an `options` object.\n  function geoJSON(geojson, options) {\n    return new GeoJSON(geojson, options);\n  }\n\n  // Backward compatibility.\n  var geoJson = geoJSON;\n\n  /*\r\n   * @class ImageOverlay\r\n   * @aka L.ImageOverlay\r\n   * @inherits Interactive layer\r\n   *\r\n   * Used to load and display a single image over specific bounds of the map. Extends `Layer`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var imageUrl = 'https://maps.lib.utexas.edu/maps/historical/newark_nj_1922.jpg',\r\n   * \timageBounds = [[40.712216, -74.22655], [40.773941, -74.12544]];\r\n   * L.imageOverlay(imageUrl, imageBounds).addTo(map);\r\n   * ```\r\n   */\n\n  var ImageOverlay = Layer.extend({\n    // @section\n    // @aka ImageOverlay options\n    options: {\n      // @option opacity: Number = 1.0\n      // The opacity of the image overlay.\n      opacity: 1,\n      // @option alt: String = ''\n      // Text for the `alt` attribute of the image (useful for accessibility).\n      alt: '',\n      // @option interactive: Boolean = false\n      // If `true`, the image overlay will emit [mouse events](#interactive-layer) when clicked or hovered.\n      interactive: false,\n      // @option crossOrigin: Boolean|String = false\n      // Whether the crossOrigin attribute will be added to the image.\n      // If a String is provided, the image will have its crossOrigin attribute set to the String provided. This is needed if you want to access image pixel data.\n      // Refer to [CORS Settings](https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_settings_attributes) for valid String values.\n      crossOrigin: false,\n      // @option errorOverlayUrl: String = ''\n      // URL to the overlay image to show in place of the overlay that failed to load.\n      errorOverlayUrl: '',\n      // @option zIndex: Number = 1\n      // The explicit [zIndex](https://developer.mozilla.org/docs/Web/CSS/CSS_Positioning/Understanding_z_index) of the overlay layer.\n      zIndex: 1,\n      // @option className: String = ''\n      // A custom class name to assign to the image. Empty by default.\n      className: ''\n    },\n    initialize: function (url, bounds, options) {\n      // (String, LatLngBounds, Object)\n      this._url = url;\n      this._bounds = toLatLngBounds(bounds);\n      setOptions(this, options);\n    },\n    onAdd: function () {\n      if (!this._image) {\n        this._initImage();\n        if (this.options.opacity < 1) {\n          this._updateOpacity();\n        }\n      }\n      if (this.options.interactive) {\n        addClass(this._image, 'leaflet-interactive');\n        this.addInteractiveTarget(this._image);\n      }\n      this.getPane().appendChild(this._image);\n      this._reset();\n    },\n    onRemove: function () {\n      remove(this._image);\n      if (this.options.interactive) {\n        this.removeInteractiveTarget(this._image);\n      }\n    },\n    // @method setOpacity(opacity: Number): this\n    // Sets the opacity of the overlay.\n    setOpacity: function (opacity) {\n      this.options.opacity = opacity;\n      if (this._image) {\n        this._updateOpacity();\n      }\n      return this;\n    },\n    setStyle: function (styleOpts) {\n      if (styleOpts.opacity) {\n        this.setOpacity(styleOpts.opacity);\n      }\n      return this;\n    },\n    // @method bringToFront(): this\n    // Brings the layer to the top of all overlays.\n    bringToFront: function () {\n      if (this._map) {\n        toFront(this._image);\n      }\n      return this;\n    },\n    // @method bringToBack(): this\n    // Brings the layer to the bottom of all overlays.\n    bringToBack: function () {\n      if (this._map) {\n        toBack(this._image);\n      }\n      return this;\n    },\n    // @method setUrl(url: String): this\n    // Changes the URL of the image.\n    setUrl: function (url) {\n      this._url = url;\n      if (this._image) {\n        this._image.src = url;\n      }\n      return this;\n    },\n    // @method setBounds(bounds: LatLngBounds): this\n    // Update the bounds that this ImageOverlay covers\n    setBounds: function (bounds) {\n      this._bounds = toLatLngBounds(bounds);\n      if (this._map) {\n        this._reset();\n      }\n      return this;\n    },\n    getEvents: function () {\n      var events = {\n        zoom: this._reset,\n        viewreset: this._reset\n      };\n      if (this._zoomAnimated) {\n        events.zoomanim = this._animateZoom;\n      }\n      return events;\n    },\n    // @method setZIndex(value: Number): this\n    // Changes the [zIndex](#imageoverlay-zindex) of the image overlay.\n    setZIndex: function (value) {\n      this.options.zIndex = value;\n      this._updateZIndex();\n      return this;\n    },\n    // @method getBounds(): LatLngBounds\n    // Get the bounds that this ImageOverlay covers\n    getBounds: function () {\n      return this._bounds;\n    },\n    // @method getElement(): HTMLElement\n    // Returns the instance of [`HTMLImageElement`](https://developer.mozilla.org/docs/Web/API/HTMLImageElement)\n    // used by this overlay.\n    getElement: function () {\n      return this._image;\n    },\n    _initImage: function () {\n      var wasElementSupplied = this._url.tagName === 'IMG';\n      var img = this._image = wasElementSupplied ? this._url : create$1('img');\n      addClass(img, 'leaflet-image-layer');\n      if (this._zoomAnimated) {\n        addClass(img, 'leaflet-zoom-animated');\n      }\n      if (this.options.className) {\n        addClass(img, this.options.className);\n      }\n      img.onselectstart = falseFn;\n      img.onmousemove = falseFn;\n\n      // @event load: Event\n      // Fired when the ImageOverlay layer has loaded its image\n      img.onload = bind(this.fire, this, 'load');\n      img.onerror = bind(this._overlayOnError, this, 'error');\n      if (this.options.crossOrigin || this.options.crossOrigin === '') {\n        img.crossOrigin = this.options.crossOrigin === true ? '' : this.options.crossOrigin;\n      }\n      if (this.options.zIndex) {\n        this._updateZIndex();\n      }\n      if (wasElementSupplied) {\n        this._url = img.src;\n        return;\n      }\n      img.src = this._url;\n      img.alt = this.options.alt;\n    },\n    _animateZoom: function (e) {\n      var scale = this._map.getZoomScale(e.zoom),\n        offset = this._map._latLngBoundsToNewLayerBounds(this._bounds, e.zoom, e.center).min;\n      setTransform(this._image, offset, scale);\n    },\n    _reset: function () {\n      var image = this._image,\n        bounds = new Bounds(this._map.latLngToLayerPoint(this._bounds.getNorthWest()), this._map.latLngToLayerPoint(this._bounds.getSouthEast())),\n        size = bounds.getSize();\n      setPosition(image, bounds.min);\n      image.style.width = size.x + 'px';\n      image.style.height = size.y + 'px';\n    },\n    _updateOpacity: function () {\n      setOpacity(this._image, this.options.opacity);\n    },\n    _updateZIndex: function () {\n      if (this._image && this.options.zIndex !== undefined && this.options.zIndex !== null) {\n        this._image.style.zIndex = this.options.zIndex;\n      }\n    },\n    _overlayOnError: function () {\n      // @event error: Event\n      // Fired when the ImageOverlay layer fails to load its image\n      this.fire('error');\n      var errorUrl = this.options.errorOverlayUrl;\n      if (errorUrl && this._url !== errorUrl) {\n        this._url = errorUrl;\n        this._image.src = errorUrl;\n      }\n    },\n    // @method getCenter(): LatLng\n    // Returns the center of the ImageOverlay.\n    getCenter: function () {\n      return this._bounds.getCenter();\n    }\n  });\n\n  // @factory L.imageOverlay(imageUrl: String, bounds: LatLngBounds, options?: ImageOverlay options)\n  // Instantiates an image overlay object given the URL of the image and the\n  // geographical bounds it is tied to.\n  var imageOverlay = function (url, bounds, options) {\n    return new ImageOverlay(url, bounds, options);\n  };\n\n  /*\r\n   * @class VideoOverlay\r\n   * @aka L.VideoOverlay\r\n   * @inherits ImageOverlay\r\n   *\r\n   * Used to load and display a video player over specific bounds of the map. Extends `ImageOverlay`.\r\n   *\r\n   * A video overlay uses the [`<video>`](https://developer.mozilla.org/docs/Web/HTML/Element/video)\r\n   * HTML5 element.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var videoUrl = 'https://www.mapbox.com/bites/00188/patricia_nasa.webm',\r\n   * \tvideoBounds = [[ 32, -130], [ 13, -100]];\r\n   * L.videoOverlay(videoUrl, videoBounds ).addTo(map);\r\n   * ```\r\n   */\n\n  var VideoOverlay = ImageOverlay.extend({\n    // @section\n    // @aka VideoOverlay options\n    options: {\n      // @option autoplay: Boolean = true\n      // Whether the video starts playing automatically when loaded.\n      // On some browsers autoplay will only work with `muted: true`\n      autoplay: true,\n      // @option loop: Boolean = true\n      // Whether the video will loop back to the beginning when played.\n      loop: true,\n      // @option keepAspectRatio: Boolean = true\n      // Whether the video will save aspect ratio after the projection.\n      // Relevant for supported browsers. See [browser compatibility](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit)\n      keepAspectRatio: true,\n      // @option muted: Boolean = false\n      // Whether the video starts on mute when loaded.\n      muted: false,\n      // @option playsInline: Boolean = true\n      // Mobile browsers will play the video right where it is instead of open it up in fullscreen mode.\n      playsInline: true\n    },\n    _initImage: function () {\n      var wasElementSupplied = this._url.tagName === 'VIDEO';\n      var vid = this._image = wasElementSupplied ? this._url : create$1('video');\n      addClass(vid, 'leaflet-image-layer');\n      if (this._zoomAnimated) {\n        addClass(vid, 'leaflet-zoom-animated');\n      }\n      if (this.options.className) {\n        addClass(vid, this.options.className);\n      }\n      vid.onselectstart = falseFn;\n      vid.onmousemove = falseFn;\n\n      // @event load: Event\n      // Fired when the video has finished loading the first frame\n      vid.onloadeddata = bind(this.fire, this, 'load');\n      if (wasElementSupplied) {\n        var sourceElements = vid.getElementsByTagName('source');\n        var sources = [];\n        for (var j = 0; j < sourceElements.length; j++) {\n          sources.push(sourceElements[j].src);\n        }\n        this._url = sourceElements.length > 0 ? sources : [vid.src];\n        return;\n      }\n      if (!isArray(this._url)) {\n        this._url = [this._url];\n      }\n      if (!this.options.keepAspectRatio && Object.prototype.hasOwnProperty.call(vid.style, 'objectFit')) {\n        vid.style['objectFit'] = 'fill';\n      }\n      vid.autoplay = !!this.options.autoplay;\n      vid.loop = !!this.options.loop;\n      vid.muted = !!this.options.muted;\n      vid.playsInline = !!this.options.playsInline;\n      for (var i = 0; i < this._url.length; i++) {\n        var source = create$1('source');\n        source.src = this._url[i];\n        vid.appendChild(source);\n      }\n    }\n\n    // @method getElement(): HTMLVideoElement\n    // Returns the instance of [`HTMLVideoElement`](https://developer.mozilla.org/docs/Web/API/HTMLVideoElement)\n    // used by this overlay.\n  });\n\n  // @factory L.videoOverlay(video: String|Array|HTMLVideoElement, bounds: LatLngBounds, options?: VideoOverlay options)\n  // Instantiates an image overlay object given the URL of the video (or array of URLs, or even a video element) and the\n  // geographical bounds it is tied to.\n\n  function videoOverlay(video, bounds, options) {\n    return new VideoOverlay(video, bounds, options);\n  }\n\n  /*\n   * @class SVGOverlay\n   * @aka L.SVGOverlay\n   * @inherits ImageOverlay\n   *\n   * Used to load, display and provide DOM access to an SVG file over specific bounds of the map. Extends `ImageOverlay`.\n   *\n   * An SVG overlay uses the [`<svg>`](https://developer.mozilla.org/docs/Web/SVG/Element/svg) element.\n   *\n   * @example\n   *\n   * ```js\n   * var svgElement = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n   * svgElement.setAttribute('xmlns', \"http://www.w3.org/2000/svg\");\n   * svgElement.setAttribute('viewBox', \"0 0 200 200\");\n   * svgElement.innerHTML = '<rect width=\"200\" height=\"200\"/><rect x=\"75\" y=\"23\" width=\"50\" height=\"50\" style=\"fill:red\"/><rect x=\"75\" y=\"123\" width=\"50\" height=\"50\" style=\"fill:#0013ff\"/>';\n   * var svgElementBounds = [ [ 32, -130 ], [ 13, -100 ] ];\n   * L.svgOverlay(svgElement, svgElementBounds).addTo(map);\n   * ```\n   */\n\n  var SVGOverlay = ImageOverlay.extend({\n    _initImage: function () {\n      var el = this._image = this._url;\n      addClass(el, 'leaflet-image-layer');\n      if (this._zoomAnimated) {\n        addClass(el, 'leaflet-zoom-animated');\n      }\n      if (this.options.className) {\n        addClass(el, this.options.className);\n      }\n      el.onselectstart = falseFn;\n      el.onmousemove = falseFn;\n    }\n\n    // @method getElement(): SVGElement\n    // Returns the instance of [`SVGElement`](https://developer.mozilla.org/docs/Web/API/SVGElement)\n    // used by this overlay.\n  });\n\n  // @factory L.svgOverlay(svg: String|SVGElement, bounds: LatLngBounds, options?: SVGOverlay options)\n  // Instantiates an image overlay object given an SVG element and the geographical bounds it is tied to.\n  // A viewBox attribute is required on the SVG element to zoom in and out properly.\n\n  function svgOverlay(el, bounds, options) {\n    return new SVGOverlay(el, bounds, options);\n  }\n\n  /*\r\n   * @class DivOverlay\r\n   * @inherits Interactive layer\r\n   * @aka L.DivOverlay\r\n   * Base model for L.Popup and L.Tooltip. Inherit from it for custom overlays like plugins.\r\n   */\n\n  // @namespace DivOverlay\n  var DivOverlay = Layer.extend({\n    // @section\n    // @aka DivOverlay options\n    options: {\n      // @option interactive: Boolean = false\n      // If true, the popup/tooltip will listen to the mouse events.\n      interactive: false,\n      // @option offset: Point = Point(0, 0)\n      // The offset of the overlay position.\n      offset: [0, 0],\n      // @option className: String = ''\n      // A custom CSS class name to assign to the overlay.\n      className: '',\n      // @option pane: String = undefined\n      // `Map pane` where the overlay will be added.\n      pane: undefined,\n      // @option content: String|HTMLElement|Function = ''\n      // Sets the HTML content of the overlay while initializing. If a function is passed the source layer will be\n      // passed to the function. The function should return a `String` or `HTMLElement` to be used in the overlay.\n      content: ''\n    },\n    initialize: function (options, source) {\n      if (options && (options instanceof LatLng || isArray(options))) {\n        this._latlng = toLatLng(options);\n        setOptions(this, source);\n      } else {\n        setOptions(this, options);\n        this._source = source;\n      }\n      if (this.options.content) {\n        this._content = this.options.content;\n      }\n    },\n    // @method openOn(map: Map): this\n    // Adds the overlay to the map.\n    // Alternative to `map.openPopup(popup)`/`.openTooltip(tooltip)`.\n    openOn: function (map) {\n      map = arguments.length ? map : this._source._map; // experimental, not the part of public api\n      if (!map.hasLayer(this)) {\n        map.addLayer(this);\n      }\n      return this;\n    },\n    // @method close(): this\n    // Closes the overlay.\n    // Alternative to `map.closePopup(popup)`/`.closeTooltip(tooltip)`\n    // and `layer.closePopup()`/`.closeTooltip()`.\n    close: function () {\n      if (this._map) {\n        this._map.removeLayer(this);\n      }\n      return this;\n    },\n    // @method toggle(layer?: Layer): this\n    // Opens or closes the overlay bound to layer depending on its current state.\n    // Argument may be omitted only for overlay bound to layer.\n    // Alternative to `layer.togglePopup()`/`.toggleTooltip()`.\n    toggle: function (layer) {\n      if (this._map) {\n        this.close();\n      } else {\n        if (arguments.length) {\n          this._source = layer;\n        } else {\n          layer = this._source;\n        }\n        this._prepareOpen();\n\n        // open the overlay on the map\n        this.openOn(layer._map);\n      }\n      return this;\n    },\n    onAdd: function (map) {\n      this._zoomAnimated = map._zoomAnimated;\n      if (!this._container) {\n        this._initLayout();\n      }\n      if (map._fadeAnimated) {\n        setOpacity(this._container, 0);\n      }\n      clearTimeout(this._removeTimeout);\n      this.getPane().appendChild(this._container);\n      this.update();\n      if (map._fadeAnimated) {\n        setOpacity(this._container, 1);\n      }\n      this.bringToFront();\n      if (this.options.interactive) {\n        addClass(this._container, 'leaflet-interactive');\n        this.addInteractiveTarget(this._container);\n      }\n    },\n    onRemove: function (map) {\n      if (map._fadeAnimated) {\n        setOpacity(this._container, 0);\n        this._removeTimeout = setTimeout(bind(remove, undefined, this._container), 200);\n      } else {\n        remove(this._container);\n      }\n      if (this.options.interactive) {\n        removeClass(this._container, 'leaflet-interactive');\n        this.removeInteractiveTarget(this._container);\n      }\n    },\n    // @namespace DivOverlay\n    // @method getLatLng: LatLng\n    // Returns the geographical point of the overlay.\n    getLatLng: function () {\n      return this._latlng;\n    },\n    // @method setLatLng(latlng: LatLng): this\n    // Sets the geographical point where the overlay will open.\n    setLatLng: function (latlng) {\n      this._latlng = toLatLng(latlng);\n      if (this._map) {\n        this._updatePosition();\n        this._adjustPan();\n      }\n      return this;\n    },\n    // @method getContent: String|HTMLElement\n    // Returns the content of the overlay.\n    getContent: function () {\n      return this._content;\n    },\n    // @method setContent(htmlContent: String|HTMLElement|Function): this\n    // Sets the HTML content of the overlay. If a function is passed the source layer will be passed to the function.\n    // The function should return a `String` or `HTMLElement` to be used in the overlay.\n    setContent: function (content) {\n      this._content = content;\n      this.update();\n      return this;\n    },\n    // @method getElement: String|HTMLElement\n    // Returns the HTML container of the overlay.\n    getElement: function () {\n      return this._container;\n    },\n    // @method update: null\n    // Updates the overlay content, layout and position. Useful for updating the overlay after something inside changed, e.g. image loaded.\n    update: function () {\n      if (!this._map) {\n        return;\n      }\n      this._container.style.visibility = 'hidden';\n      this._updateContent();\n      this._updateLayout();\n      this._updatePosition();\n      this._container.style.visibility = '';\n      this._adjustPan();\n    },\n    getEvents: function () {\n      var events = {\n        zoom: this._updatePosition,\n        viewreset: this._updatePosition\n      };\n      if (this._zoomAnimated) {\n        events.zoomanim = this._animateZoom;\n      }\n      return events;\n    },\n    // @method isOpen: Boolean\n    // Returns `true` when the overlay is visible on the map.\n    isOpen: function () {\n      return !!this._map && this._map.hasLayer(this);\n    },\n    // @method bringToFront: this\n    // Brings this overlay in front of other overlays (in the same map pane).\n    bringToFront: function () {\n      if (this._map) {\n        toFront(this._container);\n      }\n      return this;\n    },\n    // @method bringToBack: this\n    // Brings this overlay to the back of other overlays (in the same map pane).\n    bringToBack: function () {\n      if (this._map) {\n        toBack(this._container);\n      }\n      return this;\n    },\n    // prepare bound overlay to open: update latlng pos / content source (for FeatureGroup)\n    _prepareOpen: function (latlng) {\n      var source = this._source;\n      if (!source._map) {\n        return false;\n      }\n      if (source instanceof FeatureGroup) {\n        source = null;\n        var layers = this._source._layers;\n        for (var id in layers) {\n          if (layers[id]._map) {\n            source = layers[id];\n            break;\n          }\n        }\n        if (!source) {\n          return false;\n        } // Unable to get source layer.\n\n        // set overlay source to this layer\n        this._source = source;\n      }\n      if (!latlng) {\n        if (source.getCenter) {\n          latlng = source.getCenter();\n        } else if (source.getLatLng) {\n          latlng = source.getLatLng();\n        } else if (source.getBounds) {\n          latlng = source.getBounds().getCenter();\n        } else {\n          throw new Error('Unable to get source layer LatLng.');\n        }\n      }\n      this.setLatLng(latlng);\n      if (this._map) {\n        // update the overlay (content, layout, etc...)\n        this.update();\n      }\n      return true;\n    },\n    _updateContent: function () {\n      if (!this._content) {\n        return;\n      }\n      var node = this._contentNode;\n      var content = typeof this._content === 'function' ? this._content(this._source || this) : this._content;\n      if (typeof content === 'string') {\n        node.innerHTML = content;\n      } else {\n        while (node.hasChildNodes()) {\n          node.removeChild(node.firstChild);\n        }\n        node.appendChild(content);\n      }\n\n      // @namespace DivOverlay\n      // @section DivOverlay events\n      // @event contentupdate: Event\n      // Fired when the content of the overlay is updated\n      this.fire('contentupdate');\n    },\n    _updatePosition: function () {\n      if (!this._map) {\n        return;\n      }\n      var pos = this._map.latLngToLayerPoint(this._latlng),\n        offset = toPoint(this.options.offset),\n        anchor = this._getAnchor();\n      if (this._zoomAnimated) {\n        setPosition(this._container, pos.add(anchor));\n      } else {\n        offset = offset.add(pos).add(anchor);\n      }\n      var bottom = this._containerBottom = -offset.y,\n        left = this._containerLeft = -Math.round(this._containerWidth / 2) + offset.x;\n\n      // bottom position the overlay in case the height of the overlay changes (images loading etc)\n      this._container.style.bottom = bottom + 'px';\n      this._container.style.left = left + 'px';\n    },\n    _getAnchor: function () {\n      return [0, 0];\n    }\n  });\n  Map.include({\n    _initOverlay: function (OverlayClass, content, latlng, options) {\n      var overlay = content;\n      if (!(overlay instanceof OverlayClass)) {\n        overlay = new OverlayClass(options).setContent(content);\n      }\n      if (latlng) {\n        overlay.setLatLng(latlng);\n      }\n      return overlay;\n    }\n  });\n  Layer.include({\n    _initOverlay: function (OverlayClass, old, content, options) {\n      var overlay = content;\n      if (overlay instanceof OverlayClass) {\n        setOptions(overlay, options);\n        overlay._source = this;\n      } else {\n        overlay = old && !options ? old : new OverlayClass(options, this);\n        overlay.setContent(content);\n      }\n      return overlay;\n    }\n  });\n\n  /*\r\n   * @class Popup\r\n   * @inherits DivOverlay\r\n   * @aka L.Popup\r\n   * Used to open popups in certain places of the map. Use [Map.openPopup](#map-openpopup) to\r\n   * open popups while making sure that only one popup is open at one time\r\n   * (recommended for usability), or use [Map.addLayer](#map-addlayer) to open as many as you want.\r\n   *\r\n   * @example\r\n   *\r\n   * If you want to just bind a popup to marker click and then open it, it's really easy:\r\n   *\r\n   * ```js\r\n   * marker.bindPopup(popupContent).openPopup();\r\n   * ```\r\n   * Path overlays like polylines also have a `bindPopup` method.\r\n   *\r\n   * A popup can be also standalone:\r\n   *\r\n   * ```js\r\n   * var popup = L.popup()\r\n   * \t.setLatLng(latlng)\r\n   * \t.setContent('<p>Hello world!<br />This is a nice popup.</p>')\r\n   * \t.openOn(map);\r\n   * ```\r\n   * or\r\n   * ```js\r\n   * var popup = L.popup(latlng, {content: '<p>Hello world!<br />This is a nice popup.</p>')\r\n   * \t.openOn(map);\r\n   * ```\r\n   */\n\n  // @namespace Popup\n  var Popup = DivOverlay.extend({\n    // @section\n    // @aka Popup options\n    options: {\n      // @option pane: String = 'popupPane'\n      // `Map pane` where the popup will be added.\n      pane: 'popupPane',\n      // @option offset: Point = Point(0, 7)\n      // The offset of the popup position.\n      offset: [0, 7],\n      // @option maxWidth: Number = 300\n      // Max width of the popup, in pixels.\n      maxWidth: 300,\n      // @option minWidth: Number = 50\n      // Min width of the popup, in pixels.\n      minWidth: 50,\n      // @option maxHeight: Number = null\n      // If set, creates a scrollable container of the given height\n      // inside a popup if its content exceeds it.\n      // The scrollable container can be styled using the\n      // `leaflet-popup-scrolled` CSS class selector.\n      maxHeight: null,\n      // @option autoPan: Boolean = true\n      // Set it to `false` if you don't want the map to do panning animation\n      // to fit the opened popup.\n      autoPan: true,\n      // @option autoPanPaddingTopLeft: Point = null\n      // The margin between the popup and the top left corner of the map\n      // view after autopanning was performed.\n      autoPanPaddingTopLeft: null,\n      // @option autoPanPaddingBottomRight: Point = null\n      // The margin between the popup and the bottom right corner of the map\n      // view after autopanning was performed.\n      autoPanPaddingBottomRight: null,\n      // @option autoPanPadding: Point = Point(5, 5)\n      // Equivalent of setting both top left and bottom right autopan padding to the same value.\n      autoPanPadding: [5, 5],\n      // @option keepInView: Boolean = false\n      // Set it to `true` if you want to prevent users from panning the popup\n      // off of the screen while it is open.\n      keepInView: false,\n      // @option closeButton: Boolean = true\n      // Controls the presence of a close button in the popup.\n      closeButton: true,\n      // @option autoClose: Boolean = true\n      // Set it to `false` if you want to override the default behavior of\n      // the popup closing when another popup is opened.\n      autoClose: true,\n      // @option closeOnEscapeKey: Boolean = true\n      // Set it to `false` if you want to override the default behavior of\n      // the ESC key for closing of the popup.\n      closeOnEscapeKey: true,\n      // @option closeOnClick: Boolean = *\n      // Set it if you want to override the default behavior of the popup closing when user clicks\n      // on the map. Defaults to the map's [`closePopupOnClick`](#map-closepopuponclick) option.\n\n      // @option className: String = ''\n      // A custom CSS class name to assign to the popup.\n      className: ''\n    },\n    // @namespace Popup\n    // @method openOn(map: Map): this\n    // Alternative to `map.openPopup(popup)`.\n    // Adds the popup to the map and closes the previous one.\n    openOn: function (map) {\n      map = arguments.length ? map : this._source._map; // experimental, not the part of public api\n\n      if (!map.hasLayer(this) && map._popup && map._popup.options.autoClose) {\n        map.removeLayer(map._popup);\n      }\n      map._popup = this;\n      return DivOverlay.prototype.openOn.call(this, map);\n    },\n    onAdd: function (map) {\n      DivOverlay.prototype.onAdd.call(this, map);\n\n      // @namespace Map\n      // @section Popup events\n      // @event popupopen: PopupEvent\n      // Fired when a popup is opened in the map\n      map.fire('popupopen', {\n        popup: this\n      });\n      if (this._source) {\n        // @namespace Layer\n        // @section Popup events\n        // @event popupopen: PopupEvent\n        // Fired when a popup bound to this layer is opened\n        this._source.fire('popupopen', {\n          popup: this\n        }, true);\n        // For non-path layers, we toggle the popup when clicking\n        // again the layer, so prevent the map to reopen it.\n        if (!(this._source instanceof Path)) {\n          this._source.on('preclick', stopPropagation);\n        }\n      }\n    },\n    onRemove: function (map) {\n      DivOverlay.prototype.onRemove.call(this, map);\n\n      // @namespace Map\n      // @section Popup events\n      // @event popupclose: PopupEvent\n      // Fired when a popup in the map is closed\n      map.fire('popupclose', {\n        popup: this\n      });\n      if (this._source) {\n        // @namespace Layer\n        // @section Popup events\n        // @event popupclose: PopupEvent\n        // Fired when a popup bound to this layer is closed\n        this._source.fire('popupclose', {\n          popup: this\n        }, true);\n        if (!(this._source instanceof Path)) {\n          this._source.off('preclick', stopPropagation);\n        }\n      }\n    },\n    getEvents: function () {\n      var events = DivOverlay.prototype.getEvents.call(this);\n      if (this.options.closeOnClick !== undefined ? this.options.closeOnClick : this._map.options.closePopupOnClick) {\n        events.preclick = this.close;\n      }\n      if (this.options.keepInView) {\n        events.moveend = this._adjustPan;\n      }\n      return events;\n    },\n    _initLayout: function () {\n      var prefix = 'leaflet-popup',\n        container = this._container = create$1('div', prefix + ' ' + (this.options.className || '') + ' leaflet-zoom-animated');\n      var wrapper = this._wrapper = create$1('div', prefix + '-content-wrapper', container);\n      this._contentNode = create$1('div', prefix + '-content', wrapper);\n      disableClickPropagation(container);\n      disableScrollPropagation(this._contentNode);\n      on(container, 'contextmenu', stopPropagation);\n      this._tipContainer = create$1('div', prefix + '-tip-container', container);\n      this._tip = create$1('div', prefix + '-tip', this._tipContainer);\n      if (this.options.closeButton) {\n        var closeButton = this._closeButton = create$1('a', prefix + '-close-button', container);\n        closeButton.setAttribute('role', 'button'); // overrides the implicit role=link of <a> elements #7399\n        closeButton.setAttribute('aria-label', 'Close popup');\n        closeButton.href = '#close';\n        closeButton.innerHTML = '<span aria-hidden=\"true\">&#215;</span>';\n        on(closeButton, 'click', function (ev) {\n          preventDefault(ev);\n          this.close();\n        }, this);\n      }\n    },\n    _updateLayout: function () {\n      var container = this._contentNode,\n        style = container.style;\n      style.width = '';\n      style.whiteSpace = 'nowrap';\n      var width = container.offsetWidth;\n      width = Math.min(width, this.options.maxWidth);\n      width = Math.max(width, this.options.minWidth);\n      style.width = width + 1 + 'px';\n      style.whiteSpace = '';\n      style.height = '';\n      var height = container.offsetHeight,\n        maxHeight = this.options.maxHeight,\n        scrolledClass = 'leaflet-popup-scrolled';\n      if (maxHeight && height > maxHeight) {\n        style.height = maxHeight + 'px';\n        addClass(container, scrolledClass);\n      } else {\n        removeClass(container, scrolledClass);\n      }\n      this._containerWidth = this._container.offsetWidth;\n    },\n    _animateZoom: function (e) {\n      var pos = this._map._latLngToNewLayerPoint(this._latlng, e.zoom, e.center),\n        anchor = this._getAnchor();\n      setPosition(this._container, pos.add(anchor));\n    },\n    _adjustPan: function () {\n      if (!this.options.autoPan) {\n        return;\n      }\n      if (this._map._panAnim) {\n        this._map._panAnim.stop();\n      }\n\n      // We can endlessly recurse if keepInView is set and the view resets.\n      // Let's guard against that by exiting early if we're responding to our own autopan.\n      if (this._autopanning) {\n        this._autopanning = false;\n        return;\n      }\n      var map = this._map,\n        marginBottom = parseInt(getStyle(this._container, 'marginBottom'), 10) || 0,\n        containerHeight = this._container.offsetHeight + marginBottom,\n        containerWidth = this._containerWidth,\n        layerPos = new Point(this._containerLeft, -containerHeight - this._containerBottom);\n      layerPos._add(getPosition(this._container));\n      var containerPos = map.layerPointToContainerPoint(layerPos),\n        padding = toPoint(this.options.autoPanPadding),\n        paddingTL = toPoint(this.options.autoPanPaddingTopLeft || padding),\n        paddingBR = toPoint(this.options.autoPanPaddingBottomRight || padding),\n        size = map.getSize(),\n        dx = 0,\n        dy = 0;\n      if (containerPos.x + containerWidth + paddingBR.x > size.x) {\n        // right\n        dx = containerPos.x + containerWidth - size.x + paddingBR.x;\n      }\n      if (containerPos.x - dx - paddingTL.x < 0) {\n        // left\n        dx = containerPos.x - paddingTL.x;\n      }\n      if (containerPos.y + containerHeight + paddingBR.y > size.y) {\n        // bottom\n        dy = containerPos.y + containerHeight - size.y + paddingBR.y;\n      }\n      if (containerPos.y - dy - paddingTL.y < 0) {\n        // top\n        dy = containerPos.y - paddingTL.y;\n      }\n\n      // @namespace Map\n      // @section Popup events\n      // @event autopanstart: Event\n      // Fired when the map starts autopanning when opening a popup.\n      if (dx || dy) {\n        // Track that we're autopanning, as this function will be re-ran on moveend\n        if (this.options.keepInView) {\n          this._autopanning = true;\n        }\n        map.fire('autopanstart').panBy([dx, dy]);\n      }\n    },\n    _getAnchor: function () {\n      // Where should we anchor the popup on the source layer?\n      return toPoint(this._source && this._source._getPopupAnchor ? this._source._getPopupAnchor() : [0, 0]);\n    }\n  });\n\n  // @namespace Popup\n  // @factory L.popup(options?: Popup options, source?: Layer)\n  // Instantiates a `Popup` object given an optional `options` object that describes its appearance and location and an optional `source` object that is used to tag the popup with a reference to the Layer to which it refers.\n  // @alternative\n  // @factory L.popup(latlng: LatLng, options?: Popup options)\n  // Instantiates a `Popup` object given `latlng` where the popup will open and an optional `options` object that describes its appearance and location.\n  var popup = function (options, source) {\n    return new Popup(options, source);\n  };\n\n  /* @namespace Map\r\n   * @section Interaction Options\r\n   * @option closePopupOnClick: Boolean = true\r\n   * Set it to `false` if you don't want popups to close when user clicks the map.\r\n   */\n  Map.mergeOptions({\n    closePopupOnClick: true\n  });\n\n  // @namespace Map\n  // @section Methods for Layers and Controls\n  Map.include({\n    // @method openPopup(popup: Popup): this\n    // Opens the specified popup while closing the previously opened (to make sure only one is opened at one time for usability).\n    // @alternative\n    // @method openPopup(content: String|HTMLElement, latlng: LatLng, options?: Popup options): this\n    // Creates a popup with the specified content and options and opens it in the given point on a map.\n    openPopup: function (popup, latlng, options) {\n      this._initOverlay(Popup, popup, latlng, options).openOn(this);\n      return this;\n    },\n    // @method closePopup(popup?: Popup): this\n    // Closes the popup previously opened with [openPopup](#map-openpopup) (or the given one).\n    closePopup: function (popup) {\n      popup = arguments.length ? popup : this._popup;\n      if (popup) {\n        popup.close();\n      }\n      return this;\n    }\n  });\n\n  /*\r\n   * @namespace Layer\r\n   * @section Popup methods example\r\n   *\r\n   * All layers share a set of methods convenient for binding popups to it.\r\n   *\r\n   * ```js\r\n   * var layer = L.Polygon(latlngs).bindPopup('Hi There!').addTo(map);\r\n   * layer.openPopup();\r\n   * layer.closePopup();\r\n   * ```\r\n   *\r\n   * Popups will also be automatically opened when the layer is clicked on and closed when the layer is removed from the map or another popup is opened.\r\n   */\n\n  // @section Popup methods\n  Layer.include({\n    // @method bindPopup(content: String|HTMLElement|Function|Popup, options?: Popup options): this\n    // Binds a popup to the layer with the passed `content` and sets up the\n    // necessary event listeners. If a `Function` is passed it will receive\n    // the layer as the first argument and should return a `String` or `HTMLElement`.\n    bindPopup: function (content, options) {\n      this._popup = this._initOverlay(Popup, this._popup, content, options);\n      if (!this._popupHandlersAdded) {\n        this.on({\n          click: this._openPopup,\n          keypress: this._onKeyPress,\n          remove: this.closePopup,\n          move: this._movePopup\n        });\n        this._popupHandlersAdded = true;\n      }\n      return this;\n    },\n    // @method unbindPopup(): this\n    // Removes the popup previously bound with `bindPopup`.\n    unbindPopup: function () {\n      if (this._popup) {\n        this.off({\n          click: this._openPopup,\n          keypress: this._onKeyPress,\n          remove: this.closePopup,\n          move: this._movePopup\n        });\n        this._popupHandlersAdded = false;\n        this._popup = null;\n      }\n      return this;\n    },\n    // @method openPopup(latlng?: LatLng): this\n    // Opens the bound popup at the specified `latlng` or at the default popup anchor if no `latlng` is passed.\n    openPopup: function (latlng) {\n      if (this._popup) {\n        if (!(this instanceof FeatureGroup)) {\n          this._popup._source = this;\n        }\n        if (this._popup._prepareOpen(latlng || this._latlng)) {\n          // open the popup on the map\n          this._popup.openOn(this._map);\n        }\n      }\n      return this;\n    },\n    // @method closePopup(): this\n    // Closes the popup bound to this layer if it is open.\n    closePopup: function () {\n      if (this._popup) {\n        this._popup.close();\n      }\n      return this;\n    },\n    // @method togglePopup(): this\n    // Opens or closes the popup bound to this layer depending on its current state.\n    togglePopup: function () {\n      if (this._popup) {\n        this._popup.toggle(this);\n      }\n      return this;\n    },\n    // @method isPopupOpen(): boolean\n    // Returns `true` if the popup bound to this layer is currently open.\n    isPopupOpen: function () {\n      return this._popup ? this._popup.isOpen() : false;\n    },\n    // @method setPopupContent(content: String|HTMLElement|Popup): this\n    // Sets the content of the popup bound to this layer.\n    setPopupContent: function (content) {\n      if (this._popup) {\n        this._popup.setContent(content);\n      }\n      return this;\n    },\n    // @method getPopup(): Popup\n    // Returns the popup bound to this layer.\n    getPopup: function () {\n      return this._popup;\n    },\n    _openPopup: function (e) {\n      if (!this._popup || !this._map) {\n        return;\n      }\n      // prevent map click\n      stop(e);\n      var target = e.layer || e.target;\n      if (this._popup._source === target && !(target instanceof Path)) {\n        // treat it like a marker and figure out\n        // if we should toggle it open/closed\n        if (this._map.hasLayer(this._popup)) {\n          this.closePopup();\n        } else {\n          this.openPopup(e.latlng);\n        }\n        return;\n      }\n      this._popup._source = target;\n      this.openPopup(e.latlng);\n    },\n    _movePopup: function (e) {\n      this._popup.setLatLng(e.latlng);\n    },\n    _onKeyPress: function (e) {\n      if (e.originalEvent.keyCode === 13) {\n        this._openPopup(e);\n      }\n    }\n  });\n\n  /*\n   * @class Tooltip\n   * @inherits DivOverlay\n   * @aka L.Tooltip\n   * Used to display small texts on top of map layers.\n   *\n   * @example\n   * If you want to just bind a tooltip to marker:\n   *\n   * ```js\n   * marker.bindTooltip(\"my tooltip text\").openTooltip();\n   * ```\n   * Path overlays like polylines also have a `bindTooltip` method.\n   *\n   * A tooltip can be also standalone:\n   *\n   * ```js\n   * var tooltip = L.tooltip()\n   * \t.setLatLng(latlng)\n   * \t.setContent('Hello world!<br />This is a nice tooltip.')\n   * \t.addTo(map);\n   * ```\n   * or\n   * ```js\n   * var tooltip = L.tooltip(latlng, {content: 'Hello world!<br />This is a nice tooltip.'})\n   * \t.addTo(map);\n   * ```\n   *\n   *\n   * Note about tooltip offset. Leaflet takes two options in consideration\n   * for computing tooltip offsetting:\n   * - the `offset` Tooltip option: it defaults to [0, 0], and it's specific to one tooltip.\n   *   Add a positive x offset to move the tooltip to the right, and a positive y offset to\n   *   move it to the bottom. Negatives will move to the left and top.\n   * - the `tooltipAnchor` Icon option: this will only be considered for Marker. You\n   *   should adapt this value if you use a custom icon.\n   */\n\n  // @namespace Tooltip\n  var Tooltip = DivOverlay.extend({\n    // @section\n    // @aka Tooltip options\n    options: {\n      // @option pane: String = 'tooltipPane'\n      // `Map pane` where the tooltip will be added.\n      pane: 'tooltipPane',\n      // @option offset: Point = Point(0, 0)\n      // Optional offset of the tooltip position.\n      offset: [0, 0],\n      // @option direction: String = 'auto'\n      // Direction where to open the tooltip. Possible values are: `right`, `left`,\n      // `top`, `bottom`, `center`, `auto`.\n      // `auto` will dynamically switch between `right` and `left` according to the tooltip\n      // position on the map.\n      direction: 'auto',\n      // @option permanent: Boolean = false\n      // Whether to open the tooltip permanently or only on mouseover.\n      permanent: false,\n      // @option sticky: Boolean = false\n      // If true, the tooltip will follow the mouse instead of being fixed at the feature center.\n      sticky: false,\n      // @option opacity: Number = 0.9\n      // Tooltip container opacity.\n      opacity: 0.9\n    },\n    onAdd: function (map) {\n      DivOverlay.prototype.onAdd.call(this, map);\n      this.setOpacity(this.options.opacity);\n\n      // @namespace Map\n      // @section Tooltip events\n      // @event tooltipopen: TooltipEvent\n      // Fired when a tooltip is opened in the map.\n      map.fire('tooltipopen', {\n        tooltip: this\n      });\n      if (this._source) {\n        this.addEventParent(this._source);\n\n        // @namespace Layer\n        // @section Tooltip events\n        // @event tooltipopen: TooltipEvent\n        // Fired when a tooltip bound to this layer is opened.\n        this._source.fire('tooltipopen', {\n          tooltip: this\n        }, true);\n      }\n    },\n    onRemove: function (map) {\n      DivOverlay.prototype.onRemove.call(this, map);\n\n      // @namespace Map\n      // @section Tooltip events\n      // @event tooltipclose: TooltipEvent\n      // Fired when a tooltip in the map is closed.\n      map.fire('tooltipclose', {\n        tooltip: this\n      });\n      if (this._source) {\n        this.removeEventParent(this._source);\n\n        // @namespace Layer\n        // @section Tooltip events\n        // @event tooltipclose: TooltipEvent\n        // Fired when a tooltip bound to this layer is closed.\n        this._source.fire('tooltipclose', {\n          tooltip: this\n        }, true);\n      }\n    },\n    getEvents: function () {\n      var events = DivOverlay.prototype.getEvents.call(this);\n      if (!this.options.permanent) {\n        events.preclick = this.close;\n      }\n      return events;\n    },\n    _initLayout: function () {\n      var prefix = 'leaflet-tooltip',\n        className = prefix + ' ' + (this.options.className || '') + ' leaflet-zoom-' + (this._zoomAnimated ? 'animated' : 'hide');\n      this._contentNode = this._container = create$1('div', className);\n      this._container.setAttribute('role', 'tooltip');\n      this._container.setAttribute('id', 'leaflet-tooltip-' + stamp(this));\n    },\n    _updateLayout: function () {},\n    _adjustPan: function () {},\n    _setPosition: function (pos) {\n      var subX,\n        subY,\n        map = this._map,\n        container = this._container,\n        centerPoint = map.latLngToContainerPoint(map.getCenter()),\n        tooltipPoint = map.layerPointToContainerPoint(pos),\n        direction = this.options.direction,\n        tooltipWidth = container.offsetWidth,\n        tooltipHeight = container.offsetHeight,\n        offset = toPoint(this.options.offset),\n        anchor = this._getAnchor();\n      if (direction === 'top') {\n        subX = tooltipWidth / 2;\n        subY = tooltipHeight;\n      } else if (direction === 'bottom') {\n        subX = tooltipWidth / 2;\n        subY = 0;\n      } else if (direction === 'center') {\n        subX = tooltipWidth / 2;\n        subY = tooltipHeight / 2;\n      } else if (direction === 'right') {\n        subX = 0;\n        subY = tooltipHeight / 2;\n      } else if (direction === 'left') {\n        subX = tooltipWidth;\n        subY = tooltipHeight / 2;\n      } else if (tooltipPoint.x < centerPoint.x) {\n        direction = 'right';\n        subX = 0;\n        subY = tooltipHeight / 2;\n      } else {\n        direction = 'left';\n        subX = tooltipWidth + (offset.x + anchor.x) * 2;\n        subY = tooltipHeight / 2;\n      }\n      pos = pos.subtract(toPoint(subX, subY, true)).add(offset).add(anchor);\n      removeClass(container, 'leaflet-tooltip-right');\n      removeClass(container, 'leaflet-tooltip-left');\n      removeClass(container, 'leaflet-tooltip-top');\n      removeClass(container, 'leaflet-tooltip-bottom');\n      addClass(container, 'leaflet-tooltip-' + direction);\n      setPosition(container, pos);\n    },\n    _updatePosition: function () {\n      var pos = this._map.latLngToLayerPoint(this._latlng);\n      this._setPosition(pos);\n    },\n    setOpacity: function (opacity) {\n      this.options.opacity = opacity;\n      if (this._container) {\n        setOpacity(this._container, opacity);\n      }\n    },\n    _animateZoom: function (e) {\n      var pos = this._map._latLngToNewLayerPoint(this._latlng, e.zoom, e.center);\n      this._setPosition(pos);\n    },\n    _getAnchor: function () {\n      // Where should we anchor the tooltip on the source layer?\n      return toPoint(this._source && this._source._getTooltipAnchor && !this.options.sticky ? this._source._getTooltipAnchor() : [0, 0]);\n    }\n  });\n\n  // @namespace Tooltip\n  // @factory L.tooltip(options?: Tooltip options, source?: Layer)\n  // Instantiates a `Tooltip` object given an optional `options` object that describes its appearance and location and an optional `source` object that is used to tag the tooltip with a reference to the Layer to which it refers.\n  // @alternative\n  // @factory L.tooltip(latlng: LatLng, options?: Tooltip options)\n  // Instantiates a `Tooltip` object given `latlng` where the tooltip will open and an optional `options` object that describes its appearance and location.\n  var tooltip = function (options, source) {\n    return new Tooltip(options, source);\n  };\n\n  // @namespace Map\n  // @section Methods for Layers and Controls\n  Map.include({\n    // @method openTooltip(tooltip: Tooltip): this\n    // Opens the specified tooltip.\n    // @alternative\n    // @method openTooltip(content: String|HTMLElement, latlng: LatLng, options?: Tooltip options): this\n    // Creates a tooltip with the specified content and options and open it.\n    openTooltip: function (tooltip, latlng, options) {\n      this._initOverlay(Tooltip, tooltip, latlng, options).openOn(this);\n      return this;\n    },\n    // @method closeTooltip(tooltip: Tooltip): this\n    // Closes the tooltip given as parameter.\n    closeTooltip: function (tooltip) {\n      tooltip.close();\n      return this;\n    }\n  });\n\n  /*\n   * @namespace Layer\n   * @section Tooltip methods example\n   *\n   * All layers share a set of methods convenient for binding tooltips to it.\n   *\n   * ```js\n   * var layer = L.Polygon(latlngs).bindTooltip('Hi There!').addTo(map);\n   * layer.openTooltip();\n   * layer.closeTooltip();\n   * ```\n   */\n\n  // @section Tooltip methods\n  Layer.include({\n    // @method bindTooltip(content: String|HTMLElement|Function|Tooltip, options?: Tooltip options): this\n    // Binds a tooltip to the layer with the passed `content` and sets up the\n    // necessary event listeners. If a `Function` is passed it will receive\n    // the layer as the first argument and should return a `String` or `HTMLElement`.\n    bindTooltip: function (content, options) {\n      if (this._tooltip && this.isTooltipOpen()) {\n        this.unbindTooltip();\n      }\n      this._tooltip = this._initOverlay(Tooltip, this._tooltip, content, options);\n      this._initTooltipInteractions();\n      if (this._tooltip.options.permanent && this._map && this._map.hasLayer(this)) {\n        this.openTooltip();\n      }\n      return this;\n    },\n    // @method unbindTooltip(): this\n    // Removes the tooltip previously bound with `bindTooltip`.\n    unbindTooltip: function () {\n      if (this._tooltip) {\n        this._initTooltipInteractions(true);\n        this.closeTooltip();\n        this._tooltip = null;\n      }\n      return this;\n    },\n    _initTooltipInteractions: function (remove) {\n      if (!remove && this._tooltipHandlersAdded) {\n        return;\n      }\n      var onOff = remove ? 'off' : 'on',\n        events = {\n          remove: this.closeTooltip,\n          move: this._moveTooltip\n        };\n      if (!this._tooltip.options.permanent) {\n        events.mouseover = this._openTooltip;\n        events.mouseout = this.closeTooltip;\n        events.click = this._openTooltip;\n        if (this._map) {\n          this._addFocusListeners();\n        } else {\n          events.add = this._addFocusListeners;\n        }\n      } else {\n        events.add = this._openTooltip;\n      }\n      if (this._tooltip.options.sticky) {\n        events.mousemove = this._moveTooltip;\n      }\n      this[onOff](events);\n      this._tooltipHandlersAdded = !remove;\n    },\n    // @method openTooltip(latlng?: LatLng): this\n    // Opens the bound tooltip at the specified `latlng` or at the default tooltip anchor if no `latlng` is passed.\n    openTooltip: function (latlng) {\n      if (this._tooltip) {\n        if (!(this instanceof FeatureGroup)) {\n          this._tooltip._source = this;\n        }\n        if (this._tooltip._prepareOpen(latlng)) {\n          // open the tooltip on the map\n          this._tooltip.openOn(this._map);\n          if (this.getElement) {\n            this._setAriaDescribedByOnLayer(this);\n          } else if (this.eachLayer) {\n            this.eachLayer(this._setAriaDescribedByOnLayer, this);\n          }\n        }\n      }\n      return this;\n    },\n    // @method closeTooltip(): this\n    // Closes the tooltip bound to this layer if it is open.\n    closeTooltip: function () {\n      if (this._tooltip) {\n        return this._tooltip.close();\n      }\n    },\n    // @method toggleTooltip(): this\n    // Opens or closes the tooltip bound to this layer depending on its current state.\n    toggleTooltip: function () {\n      if (this._tooltip) {\n        this._tooltip.toggle(this);\n      }\n      return this;\n    },\n    // @method isTooltipOpen(): boolean\n    // Returns `true` if the tooltip bound to this layer is currently open.\n    isTooltipOpen: function () {\n      return this._tooltip.isOpen();\n    },\n    // @method setTooltipContent(content: String|HTMLElement|Tooltip): this\n    // Sets the content of the tooltip bound to this layer.\n    setTooltipContent: function (content) {\n      if (this._tooltip) {\n        this._tooltip.setContent(content);\n      }\n      return this;\n    },\n    // @method getTooltip(): Tooltip\n    // Returns the tooltip bound to this layer.\n    getTooltip: function () {\n      return this._tooltip;\n    },\n    _addFocusListeners: function () {\n      if (this.getElement) {\n        this._addFocusListenersOnLayer(this);\n      } else if (this.eachLayer) {\n        this.eachLayer(this._addFocusListenersOnLayer, this);\n      }\n    },\n    _addFocusListenersOnLayer: function (layer) {\n      var el = typeof layer.getElement === 'function' && layer.getElement();\n      if (el) {\n        on(el, 'focus', function () {\n          this._tooltip._source = layer;\n          this.openTooltip();\n        }, this);\n        on(el, 'blur', this.closeTooltip, this);\n      }\n    },\n    _setAriaDescribedByOnLayer: function (layer) {\n      var el = typeof layer.getElement === 'function' && layer.getElement();\n      if (el) {\n        el.setAttribute('aria-describedby', this._tooltip._container.id);\n      }\n    },\n    _openTooltip: function (e) {\n      if (!this._tooltip || !this._map) {\n        return;\n      }\n\n      // If the map is moving, we will show the tooltip after it's done.\n      if (this._map.dragging && this._map.dragging.moving() && !this._openOnceFlag) {\n        this._openOnceFlag = true;\n        var that = this;\n        this._map.once('moveend', function () {\n          that._openOnceFlag = false;\n          that._openTooltip(e);\n        });\n        return;\n      }\n      this._tooltip._source = e.layer || e.target;\n      this.openTooltip(this._tooltip.options.sticky ? e.latlng : undefined);\n    },\n    _moveTooltip: function (e) {\n      var latlng = e.latlng,\n        containerPoint,\n        layerPoint;\n      if (this._tooltip.options.sticky && e.originalEvent) {\n        containerPoint = this._map.mouseEventToContainerPoint(e.originalEvent);\n        layerPoint = this._map.containerPointToLayerPoint(containerPoint);\n        latlng = this._map.layerPointToLatLng(layerPoint);\n      }\n      this._tooltip.setLatLng(latlng);\n    }\n  });\n\n  /*\n   * @class DivIcon\n   * @aka L.DivIcon\n   * @inherits Icon\n   *\n   * Represents a lightweight icon for markers that uses a simple `<div>`\n   * element instead of an image. Inherits from `Icon` but ignores the `iconUrl` and shadow options.\n   *\n   * @example\n   * ```js\n   * var myIcon = L.divIcon({className: 'my-div-icon'});\n   * // you can set .my-div-icon styles in CSS\n   *\n   * L.marker([50.505, 30.57], {icon: myIcon}).addTo(map);\n   * ```\n   *\n   * By default, it has a 'leaflet-div-icon' CSS class and is styled as a little white square with a shadow.\n   */\n\n  var DivIcon = Icon.extend({\n    options: {\n      // @section\n      // @aka DivIcon options\n      iconSize: [12, 12],\n      // also can be set through CSS\n\n      // iconAnchor: (Point),\n      // popupAnchor: (Point),\n\n      // @option html: String|HTMLElement = ''\n      // Custom HTML code to put inside the div element, empty by default. Alternatively,\n      // an instance of `HTMLElement`.\n      html: false,\n      // @option bgPos: Point = [0, 0]\n      // Optional relative position of the background, in pixels\n      bgPos: null,\n      className: 'leaflet-div-icon'\n    },\n    createIcon: function (oldIcon) {\n      var div = oldIcon && oldIcon.tagName === 'DIV' ? oldIcon : document.createElement('div'),\n        options = this.options;\n      if (options.html instanceof Element) {\n        empty(div);\n        div.appendChild(options.html);\n      } else {\n        div.innerHTML = options.html !== false ? options.html : '';\n      }\n      if (options.bgPos) {\n        var bgPos = toPoint(options.bgPos);\n        div.style.backgroundPosition = -bgPos.x + 'px ' + -bgPos.y + 'px';\n      }\n      this._setIconStyles(div, 'icon');\n      return div;\n    },\n    createShadow: function () {\n      return null;\n    }\n  });\n\n  // @factory L.divIcon(options: DivIcon options)\n  // Creates a `DivIcon` instance with the given options.\n  function divIcon(options) {\n    return new DivIcon(options);\n  }\n  Icon.Default = IconDefault;\n\n  /*\n   * @class GridLayer\n   * @inherits Layer\n   * @aka L.GridLayer\n   *\n   * Generic class for handling a tiled grid of HTML elements. This is the base class for all tile layers and replaces `TileLayer.Canvas`.\n   * GridLayer can be extended to create a tiled grid of HTML elements like `<canvas>`, `<img>` or `<div>`. GridLayer will handle creating and animating these DOM elements for you.\n   *\n   *\n   * @section Synchronous usage\n   * @example\n   *\n   * To create a custom layer, extend GridLayer and implement the `createTile()` method, which will be passed a `Point` object with the `x`, `y`, and `z` (zoom level) coordinates to draw your tile.\n   *\n   * ```js\n   * var CanvasLayer = L.GridLayer.extend({\n   *     createTile: function(coords){\n   *         // create a <canvas> element for drawing\n   *         var tile = L.DomUtil.create('canvas', 'leaflet-tile');\n   *\n   *         // setup tile width and height according to the options\n   *         var size = this.getTileSize();\n   *         tile.width = size.x;\n   *         tile.height = size.y;\n   *\n   *         // get a canvas context and draw something on it using coords.x, coords.y and coords.z\n   *         var ctx = tile.getContext('2d');\n   *\n   *         // return the tile so it can be rendered on screen\n   *         return tile;\n   *     }\n   * });\n   * ```\n   *\n   * @section Asynchronous usage\n   * @example\n   *\n   * Tile creation can also be asynchronous, this is useful when using a third-party drawing library. Once the tile is finished drawing it can be passed to the `done()` callback.\n   *\n   * ```js\n   * var CanvasLayer = L.GridLayer.extend({\n   *     createTile: function(coords, done){\n   *         var error;\n   *\n   *         // create a <canvas> element for drawing\n   *         var tile = L.DomUtil.create('canvas', 'leaflet-tile');\n   *\n   *         // setup tile width and height according to the options\n   *         var size = this.getTileSize();\n   *         tile.width = size.x;\n   *         tile.height = size.y;\n   *\n   *         // draw something asynchronously and pass the tile to the done() callback\n   *         setTimeout(function() {\n   *             done(error, tile);\n   *         }, 1000);\n   *\n   *         return tile;\n   *     }\n   * });\n   * ```\n   *\n   * @section\n   */\n\n  var GridLayer = Layer.extend({\n    // @section\n    // @aka GridLayer options\n    options: {\n      // @option tileSize: Number|Point = 256\n      // Width and height of tiles in the grid. Use a number if width and height are equal, or `L.point(width, height)` otherwise.\n      tileSize: 256,\n      // @option opacity: Number = 1.0\n      // Opacity of the tiles. Can be used in the `createTile()` function.\n      opacity: 1,\n      // @option updateWhenIdle: Boolean = (depends)\n      // Load new tiles only when panning ends.\n      // `true` by default on mobile browsers, in order to avoid too many requests and keep smooth navigation.\n      // `false` otherwise in order to display new tiles _during_ panning, since it is easy to pan outside the\n      // [`keepBuffer`](#gridlayer-keepbuffer) option in desktop browsers.\n      updateWhenIdle: Browser.mobile,\n      // @option updateWhenZooming: Boolean = true\n      // By default, a smooth zoom animation (during a [touch zoom](#map-touchzoom) or a [`flyTo()`](#map-flyto)) will update grid layers every integer zoom level. Setting this option to `false` will update the grid layer only when the smooth animation ends.\n      updateWhenZooming: true,\n      // @option updateInterval: Number = 200\n      // Tiles will not update more than once every `updateInterval` milliseconds when panning.\n      updateInterval: 200,\n      // @option zIndex: Number = 1\n      // The explicit zIndex of the tile layer.\n      zIndex: 1,\n      // @option bounds: LatLngBounds = undefined\n      // If set, tiles will only be loaded inside the set `LatLngBounds`.\n      bounds: null,\n      // @option minZoom: Number = 0\n      // The minimum zoom level down to which this layer will be displayed (inclusive).\n      minZoom: 0,\n      // @option maxZoom: Number = undefined\n      // The maximum zoom level up to which this layer will be displayed (inclusive).\n      maxZoom: undefined,\n      // @option maxNativeZoom: Number = undefined\n      // Maximum zoom number the tile source has available. If it is specified,\n      // the tiles on all zoom levels higher than `maxNativeZoom` will be loaded\n      // from `maxNativeZoom` level and auto-scaled.\n      maxNativeZoom: undefined,\n      // @option minNativeZoom: Number = undefined\n      // Minimum zoom number the tile source has available. If it is specified,\n      // the tiles on all zoom levels lower than `minNativeZoom` will be loaded\n      // from `minNativeZoom` level and auto-scaled.\n      minNativeZoom: undefined,\n      // @option noWrap: Boolean = false\n      // Whether the layer is wrapped around the antimeridian. If `true`, the\n      // GridLayer will only be displayed once at low zoom levels. Has no\n      // effect when the [map CRS](#map-crs) doesn't wrap around. Can be used\n      // in combination with [`bounds`](#gridlayer-bounds) to prevent requesting\n      // tiles outside the CRS limits.\n      noWrap: false,\n      // @option pane: String = 'tilePane'\n      // `Map pane` where the grid layer will be added.\n      pane: 'tilePane',\n      // @option className: String = ''\n      // A custom class name to assign to the tile layer. Empty by default.\n      className: '',\n      // @option keepBuffer: Number = 2\n      // When panning the map, keep this many rows and columns of tiles before unloading them.\n      keepBuffer: 2\n    },\n    initialize: function (options) {\n      setOptions(this, options);\n    },\n    onAdd: function () {\n      this._initContainer();\n      this._levels = {};\n      this._tiles = {};\n      this._resetView(); // implicit _update() call\n    },\n\n    beforeAdd: function (map) {\n      map._addZoomLimit(this);\n    },\n    onRemove: function (map) {\n      this._removeAllTiles();\n      remove(this._container);\n      map._removeZoomLimit(this);\n      this._container = null;\n      this._tileZoom = undefined;\n    },\n    // @method bringToFront: this\n    // Brings the tile layer to the top of all tile layers.\n    bringToFront: function () {\n      if (this._map) {\n        toFront(this._container);\n        this._setAutoZIndex(Math.max);\n      }\n      return this;\n    },\n    // @method bringToBack: this\n    // Brings the tile layer to the bottom of all tile layers.\n    bringToBack: function () {\n      if (this._map) {\n        toBack(this._container);\n        this._setAutoZIndex(Math.min);\n      }\n      return this;\n    },\n    // @method getContainer: HTMLElement\n    // Returns the HTML element that contains the tiles for this layer.\n    getContainer: function () {\n      return this._container;\n    },\n    // @method setOpacity(opacity: Number): this\n    // Changes the [opacity](#gridlayer-opacity) of the grid layer.\n    setOpacity: function (opacity) {\n      this.options.opacity = opacity;\n      this._updateOpacity();\n      return this;\n    },\n    // @method setZIndex(zIndex: Number): this\n    // Changes the [zIndex](#gridlayer-zindex) of the grid layer.\n    setZIndex: function (zIndex) {\n      this.options.zIndex = zIndex;\n      this._updateZIndex();\n      return this;\n    },\n    // @method isLoading: Boolean\n    // Returns `true` if any tile in the grid layer has not finished loading.\n    isLoading: function () {\n      return this._loading;\n    },\n    // @method redraw: this\n    // Causes the layer to clear all the tiles and request them again.\n    redraw: function () {\n      if (this._map) {\n        this._removeAllTiles();\n        var tileZoom = this._clampZoom(this._map.getZoom());\n        if (tileZoom !== this._tileZoom) {\n          this._tileZoom = tileZoom;\n          this._updateLevels();\n        }\n        this._update();\n      }\n      return this;\n    },\n    getEvents: function () {\n      var events = {\n        viewprereset: this._invalidateAll,\n        viewreset: this._resetView,\n        zoom: this._resetView,\n        moveend: this._onMoveEnd\n      };\n      if (!this.options.updateWhenIdle) {\n        // update tiles on move, but not more often than once per given interval\n        if (!this._onMove) {\n          this._onMove = throttle(this._onMoveEnd, this.options.updateInterval, this);\n        }\n        events.move = this._onMove;\n      }\n      if (this._zoomAnimated) {\n        events.zoomanim = this._animateZoom;\n      }\n      return events;\n    },\n    // @section Extension methods\n    // Layers extending `GridLayer` shall reimplement the following method.\n    // @method createTile(coords: Object, done?: Function): HTMLElement\n    // Called only internally, must be overridden by classes extending `GridLayer`.\n    // Returns the `HTMLElement` corresponding to the given `coords`. If the `done` callback\n    // is specified, it must be called when the tile has finished loading and drawing.\n    createTile: function () {\n      return document.createElement('div');\n    },\n    // @section\n    // @method getTileSize: Point\n    // Normalizes the [tileSize option](#gridlayer-tilesize) into a point. Used by the `createTile()` method.\n    getTileSize: function () {\n      var s = this.options.tileSize;\n      return s instanceof Point ? s : new Point(s, s);\n    },\n    _updateZIndex: function () {\n      if (this._container && this.options.zIndex !== undefined && this.options.zIndex !== null) {\n        this._container.style.zIndex = this.options.zIndex;\n      }\n    },\n    _setAutoZIndex: function (compare) {\n      // go through all other layers of the same pane, set zIndex to max + 1 (front) or min - 1 (back)\n\n      var layers = this.getPane().children,\n        edgeZIndex = -compare(-Infinity, Infinity); // -Infinity for max, Infinity for min\n\n      for (var i = 0, len = layers.length, zIndex; i < len; i++) {\n        zIndex = layers[i].style.zIndex;\n        if (layers[i] !== this._container && zIndex) {\n          edgeZIndex = compare(edgeZIndex, +zIndex);\n        }\n      }\n      if (isFinite(edgeZIndex)) {\n        this.options.zIndex = edgeZIndex + compare(-1, 1);\n        this._updateZIndex();\n      }\n    },\n    _updateOpacity: function () {\n      if (!this._map) {\n        return;\n      }\n\n      // IE doesn't inherit filter opacity properly, so we're forced to set it on tiles\n      if (Browser.ielt9) {\n        return;\n      }\n      setOpacity(this._container, this.options.opacity);\n      var now = +new Date(),\n        nextFrame = false,\n        willPrune = false;\n      for (var key in this._tiles) {\n        var tile = this._tiles[key];\n        if (!tile.current || !tile.loaded) {\n          continue;\n        }\n        var fade = Math.min(1, (now - tile.loaded) / 200);\n        setOpacity(tile.el, fade);\n        if (fade < 1) {\n          nextFrame = true;\n        } else {\n          if (tile.active) {\n            willPrune = true;\n          } else {\n            this._onOpaqueTile(tile);\n          }\n          tile.active = true;\n        }\n      }\n      if (willPrune && !this._noPrune) {\n        this._pruneTiles();\n      }\n      if (nextFrame) {\n        cancelAnimFrame(this._fadeFrame);\n        this._fadeFrame = requestAnimFrame(this._updateOpacity, this);\n      }\n    },\n    _onOpaqueTile: falseFn,\n    _initContainer: function () {\n      if (this._container) {\n        return;\n      }\n      this._container = create$1('div', 'leaflet-layer ' + (this.options.className || ''));\n      this._updateZIndex();\n      if (this.options.opacity < 1) {\n        this._updateOpacity();\n      }\n      this.getPane().appendChild(this._container);\n    },\n    _updateLevels: function () {\n      var zoom = this._tileZoom,\n        maxZoom = this.options.maxZoom;\n      if (zoom === undefined) {\n        return undefined;\n      }\n      for (var z in this._levels) {\n        z = Number(z);\n        if (this._levels[z].el.children.length || z === zoom) {\n          this._levels[z].el.style.zIndex = maxZoom - Math.abs(zoom - z);\n          this._onUpdateLevel(z);\n        } else {\n          remove(this._levels[z].el);\n          this._removeTilesAtZoom(z);\n          this._onRemoveLevel(z);\n          delete this._levels[z];\n        }\n      }\n      var level = this._levels[zoom],\n        map = this._map;\n      if (!level) {\n        level = this._levels[zoom] = {};\n        level.el = create$1('div', 'leaflet-tile-container leaflet-zoom-animated', this._container);\n        level.el.style.zIndex = maxZoom;\n        level.origin = map.project(map.unproject(map.getPixelOrigin()), zoom).round();\n        level.zoom = zoom;\n        this._setZoomTransform(level, map.getCenter(), map.getZoom());\n\n        // force the browser to consider the newly added element for transition\n        falseFn(level.el.offsetWidth);\n        this._onCreateLevel(level);\n      }\n      this._level = level;\n      return level;\n    },\n    _onUpdateLevel: falseFn,\n    _onRemoveLevel: falseFn,\n    _onCreateLevel: falseFn,\n    _pruneTiles: function () {\n      if (!this._map) {\n        return;\n      }\n      var key, tile;\n      var zoom = this._map.getZoom();\n      if (zoom > this.options.maxZoom || zoom < this.options.minZoom) {\n        this._removeAllTiles();\n        return;\n      }\n      for (key in this._tiles) {\n        tile = this._tiles[key];\n        tile.retain = tile.current;\n      }\n      for (key in this._tiles) {\n        tile = this._tiles[key];\n        if (tile.current && !tile.active) {\n          var coords = tile.coords;\n          if (!this._retainParent(coords.x, coords.y, coords.z, coords.z - 5)) {\n            this._retainChildren(coords.x, coords.y, coords.z, coords.z + 2);\n          }\n        }\n      }\n      for (key in this._tiles) {\n        if (!this._tiles[key].retain) {\n          this._removeTile(key);\n        }\n      }\n    },\n    _removeTilesAtZoom: function (zoom) {\n      for (var key in this._tiles) {\n        if (this._tiles[key].coords.z !== zoom) {\n          continue;\n        }\n        this._removeTile(key);\n      }\n    },\n    _removeAllTiles: function () {\n      for (var key in this._tiles) {\n        this._removeTile(key);\n      }\n    },\n    _invalidateAll: function () {\n      for (var z in this._levels) {\n        remove(this._levels[z].el);\n        this._onRemoveLevel(Number(z));\n        delete this._levels[z];\n      }\n      this._removeAllTiles();\n      this._tileZoom = undefined;\n    },\n    _retainParent: function (x, y, z, minZoom) {\n      var x2 = Math.floor(x / 2),\n        y2 = Math.floor(y / 2),\n        z2 = z - 1,\n        coords2 = new Point(+x2, +y2);\n      coords2.z = +z2;\n      var key = this._tileCoordsToKey(coords2),\n        tile = this._tiles[key];\n      if (tile && tile.active) {\n        tile.retain = true;\n        return true;\n      } else if (tile && tile.loaded) {\n        tile.retain = true;\n      }\n      if (z2 > minZoom) {\n        return this._retainParent(x2, y2, z2, minZoom);\n      }\n      return false;\n    },\n    _retainChildren: function (x, y, z, maxZoom) {\n      for (var i = 2 * x; i < 2 * x + 2; i++) {\n        for (var j = 2 * y; j < 2 * y + 2; j++) {\n          var coords = new Point(i, j);\n          coords.z = z + 1;\n          var key = this._tileCoordsToKey(coords),\n            tile = this._tiles[key];\n          if (tile && tile.active) {\n            tile.retain = true;\n            continue;\n          } else if (tile && tile.loaded) {\n            tile.retain = true;\n          }\n          if (z + 1 < maxZoom) {\n            this._retainChildren(i, j, z + 1, maxZoom);\n          }\n        }\n      }\n    },\n    _resetView: function (e) {\n      var animating = e && (e.pinch || e.flyTo);\n      this._setView(this._map.getCenter(), this._map.getZoom(), animating, animating);\n    },\n    _animateZoom: function (e) {\n      this._setView(e.center, e.zoom, true, e.noUpdate);\n    },\n    _clampZoom: function (zoom) {\n      var options = this.options;\n      if (undefined !== options.minNativeZoom && zoom < options.minNativeZoom) {\n        return options.minNativeZoom;\n      }\n      if (undefined !== options.maxNativeZoom && options.maxNativeZoom < zoom) {\n        return options.maxNativeZoom;\n      }\n      return zoom;\n    },\n    _setView: function (center, zoom, noPrune, noUpdate) {\n      var tileZoom = Math.round(zoom);\n      if (this.options.maxZoom !== undefined && tileZoom > this.options.maxZoom || this.options.minZoom !== undefined && tileZoom < this.options.minZoom) {\n        tileZoom = undefined;\n      } else {\n        tileZoom = this._clampZoom(tileZoom);\n      }\n      var tileZoomChanged = this.options.updateWhenZooming && tileZoom !== this._tileZoom;\n      if (!noUpdate || tileZoomChanged) {\n        this._tileZoom = tileZoom;\n        if (this._abortLoading) {\n          this._abortLoading();\n        }\n        this._updateLevels();\n        this._resetGrid();\n        if (tileZoom !== undefined) {\n          this._update(center);\n        }\n        if (!noPrune) {\n          this._pruneTiles();\n        }\n\n        // Flag to prevent _updateOpacity from pruning tiles during\n        // a zoom anim or a pinch gesture\n        this._noPrune = !!noPrune;\n      }\n      this._setZoomTransforms(center, zoom);\n    },\n    _setZoomTransforms: function (center, zoom) {\n      for (var i in this._levels) {\n        this._setZoomTransform(this._levels[i], center, zoom);\n      }\n    },\n    _setZoomTransform: function (level, center, zoom) {\n      var scale = this._map.getZoomScale(zoom, level.zoom),\n        translate = level.origin.multiplyBy(scale).subtract(this._map._getNewPixelOrigin(center, zoom)).round();\n      if (Browser.any3d) {\n        setTransform(level.el, translate, scale);\n      } else {\n        setPosition(level.el, translate);\n      }\n    },\n    _resetGrid: function () {\n      var map = this._map,\n        crs = map.options.crs,\n        tileSize = this._tileSize = this.getTileSize(),\n        tileZoom = this._tileZoom;\n      var bounds = this._map.getPixelWorldBounds(this._tileZoom);\n      if (bounds) {\n        this._globalTileRange = this._pxBoundsToTileRange(bounds);\n      }\n      this._wrapX = crs.wrapLng && !this.options.noWrap && [Math.floor(map.project([0, crs.wrapLng[0]], tileZoom).x / tileSize.x), Math.ceil(map.project([0, crs.wrapLng[1]], tileZoom).x / tileSize.y)];\n      this._wrapY = crs.wrapLat && !this.options.noWrap && [Math.floor(map.project([crs.wrapLat[0], 0], tileZoom).y / tileSize.x), Math.ceil(map.project([crs.wrapLat[1], 0], tileZoom).y / tileSize.y)];\n    },\n    _onMoveEnd: function () {\n      if (!this._map || this._map._animatingZoom) {\n        return;\n      }\n      this._update();\n    },\n    _getTiledPixelBounds: function (center) {\n      var map = this._map,\n        mapZoom = map._animatingZoom ? Math.max(map._animateToZoom, map.getZoom()) : map.getZoom(),\n        scale = map.getZoomScale(mapZoom, this._tileZoom),\n        pixelCenter = map.project(center, this._tileZoom).floor(),\n        halfSize = map.getSize().divideBy(scale * 2);\n      return new Bounds(pixelCenter.subtract(halfSize), pixelCenter.add(halfSize));\n    },\n    // Private method to load tiles in the grid's active zoom level according to map bounds\n    _update: function (center) {\n      var map = this._map;\n      if (!map) {\n        return;\n      }\n      var zoom = this._clampZoom(map.getZoom());\n      if (center === undefined) {\n        center = map.getCenter();\n      }\n      if (this._tileZoom === undefined) {\n        return;\n      } // if out of minzoom/maxzoom\n\n      var pixelBounds = this._getTiledPixelBounds(center),\n        tileRange = this._pxBoundsToTileRange(pixelBounds),\n        tileCenter = tileRange.getCenter(),\n        queue = [],\n        margin = this.options.keepBuffer,\n        noPruneRange = new Bounds(tileRange.getBottomLeft().subtract([margin, -margin]), tileRange.getTopRight().add([margin, -margin]));\n\n      // Sanity check: panic if the tile range contains Infinity somewhere.\n      if (!(isFinite(tileRange.min.x) && isFinite(tileRange.min.y) && isFinite(tileRange.max.x) && isFinite(tileRange.max.y))) {\n        throw new Error('Attempted to load an infinite number of tiles');\n      }\n      for (var key in this._tiles) {\n        var c = this._tiles[key].coords;\n        if (c.z !== this._tileZoom || !noPruneRange.contains(new Point(c.x, c.y))) {\n          this._tiles[key].current = false;\n        }\n      }\n\n      // _update just loads more tiles. If the tile zoom level differs too much\n      // from the map's, let _setView reset levels and prune old tiles.\n      if (Math.abs(zoom - this._tileZoom) > 1) {\n        this._setView(center, zoom);\n        return;\n      }\n\n      // create a queue of coordinates to load tiles from\n      for (var j = tileRange.min.y; j <= tileRange.max.y; j++) {\n        for (var i = tileRange.min.x; i <= tileRange.max.x; i++) {\n          var coords = new Point(i, j);\n          coords.z = this._tileZoom;\n          if (!this._isValidTile(coords)) {\n            continue;\n          }\n          var tile = this._tiles[this._tileCoordsToKey(coords)];\n          if (tile) {\n            tile.current = true;\n          } else {\n            queue.push(coords);\n          }\n        }\n      }\n\n      // sort tile queue to load tiles in order of their distance to center\n      queue.sort(function (a, b) {\n        return a.distanceTo(tileCenter) - b.distanceTo(tileCenter);\n      });\n      if (queue.length !== 0) {\n        // if it's the first batch of tiles to load\n        if (!this._loading) {\n          this._loading = true;\n          // @event loading: Event\n          // Fired when the grid layer starts loading tiles.\n          this.fire('loading');\n        }\n\n        // create DOM fragment to append tiles in one batch\n        var fragment = document.createDocumentFragment();\n        for (i = 0; i < queue.length; i++) {\n          this._addTile(queue[i], fragment);\n        }\n        this._level.el.appendChild(fragment);\n      }\n    },\n    _isValidTile: function (coords) {\n      var crs = this._map.options.crs;\n      if (!crs.infinite) {\n        // don't load tile if it's out of bounds and not wrapped\n        var bounds = this._globalTileRange;\n        if (!crs.wrapLng && (coords.x < bounds.min.x || coords.x > bounds.max.x) || !crs.wrapLat && (coords.y < bounds.min.y || coords.y > bounds.max.y)) {\n          return false;\n        }\n      }\n      if (!this.options.bounds) {\n        return true;\n      }\n\n      // don't load tile if it doesn't intersect the bounds in options\n      var tileBounds = this._tileCoordsToBounds(coords);\n      return toLatLngBounds(this.options.bounds).overlaps(tileBounds);\n    },\n    _keyToBounds: function (key) {\n      return this._tileCoordsToBounds(this._keyToTileCoords(key));\n    },\n    _tileCoordsToNwSe: function (coords) {\n      var map = this._map,\n        tileSize = this.getTileSize(),\n        nwPoint = coords.scaleBy(tileSize),\n        sePoint = nwPoint.add(tileSize),\n        nw = map.unproject(nwPoint, coords.z),\n        se = map.unproject(sePoint, coords.z);\n      return [nw, se];\n    },\n    // converts tile coordinates to its geographical bounds\n    _tileCoordsToBounds: function (coords) {\n      var bp = this._tileCoordsToNwSe(coords),\n        bounds = new LatLngBounds(bp[0], bp[1]);\n      if (!this.options.noWrap) {\n        bounds = this._map.wrapLatLngBounds(bounds);\n      }\n      return bounds;\n    },\n    // converts tile coordinates to key for the tile cache\n    _tileCoordsToKey: function (coords) {\n      return coords.x + ':' + coords.y + ':' + coords.z;\n    },\n    // converts tile cache key to coordinates\n    _keyToTileCoords: function (key) {\n      var k = key.split(':'),\n        coords = new Point(+k[0], +k[1]);\n      coords.z = +k[2];\n      return coords;\n    },\n    _removeTile: function (key) {\n      var tile = this._tiles[key];\n      if (!tile) {\n        return;\n      }\n      remove(tile.el);\n      delete this._tiles[key];\n\n      // @event tileunload: TileEvent\n      // Fired when a tile is removed (e.g. when a tile goes off the screen).\n      this.fire('tileunload', {\n        tile: tile.el,\n        coords: this._keyToTileCoords(key)\n      });\n    },\n    _initTile: function (tile) {\n      addClass(tile, 'leaflet-tile');\n      var tileSize = this.getTileSize();\n      tile.style.width = tileSize.x + 'px';\n      tile.style.height = tileSize.y + 'px';\n      tile.onselectstart = falseFn;\n      tile.onmousemove = falseFn;\n\n      // update opacity on tiles in IE7-8 because of filter inheritance problems\n      if (Browser.ielt9 && this.options.opacity < 1) {\n        setOpacity(tile, this.options.opacity);\n      }\n    },\n    _addTile: function (coords, container) {\n      var tilePos = this._getTilePos(coords),\n        key = this._tileCoordsToKey(coords);\n      var tile = this.createTile(this._wrapCoords(coords), bind(this._tileReady, this, coords));\n      this._initTile(tile);\n\n      // if createTile is defined with a second argument (\"done\" callback),\n      // we know that tile is async and will be ready later; otherwise\n      if (this.createTile.length < 2) {\n        // mark tile as ready, but delay one frame for opacity animation to happen\n        requestAnimFrame(bind(this._tileReady, this, coords, null, tile));\n      }\n      setPosition(tile, tilePos);\n\n      // save tile in cache\n      this._tiles[key] = {\n        el: tile,\n        coords: coords,\n        current: true\n      };\n      container.appendChild(tile);\n      // @event tileloadstart: TileEvent\n      // Fired when a tile is requested and starts loading.\n      this.fire('tileloadstart', {\n        tile: tile,\n        coords: coords\n      });\n    },\n    _tileReady: function (coords, err, tile) {\n      if (err) {\n        // @event tileerror: TileErrorEvent\n        // Fired when there is an error loading a tile.\n        this.fire('tileerror', {\n          error: err,\n          tile: tile,\n          coords: coords\n        });\n      }\n      var key = this._tileCoordsToKey(coords);\n      tile = this._tiles[key];\n      if (!tile) {\n        return;\n      }\n      tile.loaded = +new Date();\n      if (this._map._fadeAnimated) {\n        setOpacity(tile.el, 0);\n        cancelAnimFrame(this._fadeFrame);\n        this._fadeFrame = requestAnimFrame(this._updateOpacity, this);\n      } else {\n        tile.active = true;\n        this._pruneTiles();\n      }\n      if (!err) {\n        addClass(tile.el, 'leaflet-tile-loaded');\n\n        // @event tileload: TileEvent\n        // Fired when a tile loads.\n        this.fire('tileload', {\n          tile: tile.el,\n          coords: coords\n        });\n      }\n      if (this._noTilesToLoad()) {\n        this._loading = false;\n        // @event load: Event\n        // Fired when the grid layer loaded all visible tiles.\n        this.fire('load');\n        if (Browser.ielt9 || !this._map._fadeAnimated) {\n          requestAnimFrame(this._pruneTiles, this);\n        } else {\n          // Wait a bit more than 0.2 secs (the duration of the tile fade-in)\n          // to trigger a pruning.\n          setTimeout(bind(this._pruneTiles, this), 250);\n        }\n      }\n    },\n    _getTilePos: function (coords) {\n      return coords.scaleBy(this.getTileSize()).subtract(this._level.origin);\n    },\n    _wrapCoords: function (coords) {\n      var newCoords = new Point(this._wrapX ? wrapNum(coords.x, this._wrapX) : coords.x, this._wrapY ? wrapNum(coords.y, this._wrapY) : coords.y);\n      newCoords.z = coords.z;\n      return newCoords;\n    },\n    _pxBoundsToTileRange: function (bounds) {\n      var tileSize = this.getTileSize();\n      return new Bounds(bounds.min.unscaleBy(tileSize).floor(), bounds.max.unscaleBy(tileSize).ceil().subtract([1, 1]));\n    },\n    _noTilesToLoad: function () {\n      for (var key in this._tiles) {\n        if (!this._tiles[key].loaded) {\n          return false;\n        }\n      }\n      return true;\n    }\n  });\n\n  // @factory L.gridLayer(options?: GridLayer options)\n  // Creates a new instance of GridLayer with the supplied options.\n  function gridLayer(options) {\n    return new GridLayer(options);\n  }\n\n  /*\r\n   * @class TileLayer\r\n   * @inherits GridLayer\r\n   * @aka L.TileLayer\r\n   * Used to load and display tile layers on the map. Note that most tile servers require attribution, which you can set under `Layer`. Extends `GridLayer`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png?{foo}', {foo: 'bar', attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'}).addTo(map);\n   * ```\r\n   *\r\n   * @section URL template\r\n   * @example\r\n   *\r\n   * A string of the following form:\r\n   *\r\n   * ```\r\n   * 'https://{s}.somedomain.com/blabla/{z}/{x}/{y}{r}.png'\r\n   * ```\r\n   *\r\n   * `{s}` means one of the available subdomains (used sequentially to help with browser parallel requests per domain limitation; subdomain values are specified in options; `a`, `b` or `c` by default, can be omitted), `{z}` — zoom level, `{x}` and `{y}` — tile coordinates. `{r}` can be used to add \"&commat;2x\" to the URL to load retina tiles.\r\n   *\r\n   * You can use custom keys in the template, which will be [evaluated](#util-template) from TileLayer options, like this:\r\n   *\r\n   * ```\r\n   * L.tileLayer('https://{s}.somedomain.com/{foo}/{z}/{x}/{y}.png', {foo: 'bar'});\r\n   * ```\r\n   */\n\n  var TileLayer = GridLayer.extend({\n    // @section\n    // @aka TileLayer options\n    options: {\n      // @option minZoom: Number = 0\n      // The minimum zoom level down to which this layer will be displayed (inclusive).\n      minZoom: 0,\n      // @option maxZoom: Number = 18\n      // The maximum zoom level up to which this layer will be displayed (inclusive).\n      maxZoom: 18,\n      // @option subdomains: String|String[] = 'abc'\n      // Subdomains of the tile service. Can be passed in the form of one string (where each letter is a subdomain name) or an array of strings.\n      subdomains: 'abc',\n      // @option errorTileUrl: String = ''\n      // URL to the tile image to show in place of the tile that failed to load.\n      errorTileUrl: '',\n      // @option zoomOffset: Number = 0\n      // The zoom number used in tile URLs will be offset with this value.\n      zoomOffset: 0,\n      // @option tms: Boolean = false\n      // If `true`, inverses Y axis numbering for tiles (turn this on for [TMS](https://en.wikipedia.org/wiki/Tile_Map_Service) services).\n      tms: false,\n      // @option zoomReverse: Boolean = false\n      // If set to true, the zoom number used in tile URLs will be reversed (`maxZoom - zoom` instead of `zoom`)\n      zoomReverse: false,\n      // @option detectRetina: Boolean = false\n      // If `true` and user is on a retina display, it will request four tiles of half the specified size and a bigger zoom level in place of one to utilize the high resolution.\n      detectRetina: false,\n      // @option crossOrigin: Boolean|String = false\n      // Whether the crossOrigin attribute will be added to the tiles.\n      // If a String is provided, all tiles will have their crossOrigin attribute set to the String provided. This is needed if you want to access tile pixel data.\n      // Refer to [CORS Settings](https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_settings_attributes) for valid String values.\n      crossOrigin: false,\n      // @option referrerPolicy: Boolean|String = false\n      // Whether the referrerPolicy attribute will be added to the tiles.\n      // If a String is provided, all tiles will have their referrerPolicy attribute set to the String provided.\n      // This may be needed if your map's rendering context has a strict default but your tile provider expects a valid referrer\n      // (e.g. to validate an API token).\n      // Refer to [HTMLImageElement.referrerPolicy](https://developer.mozilla.org/en-US/docs/Web/API/HTMLImageElement/referrerPolicy) for valid String values.\n      referrerPolicy: false\n    },\n    initialize: function (url, options) {\n      this._url = url;\n      options = setOptions(this, options);\n\n      // detecting retina displays, adjusting tileSize and zoom levels\n      if (options.detectRetina && Browser.retina && options.maxZoom > 0) {\n        options.tileSize = Math.floor(options.tileSize / 2);\n        if (!options.zoomReverse) {\n          options.zoomOffset++;\n          options.maxZoom = Math.max(options.minZoom, options.maxZoom - 1);\n        } else {\n          options.zoomOffset--;\n          options.minZoom = Math.min(options.maxZoom, options.minZoom + 1);\n        }\n        options.minZoom = Math.max(0, options.minZoom);\n      } else if (!options.zoomReverse) {\n        // make sure maxZoom is gte minZoom\n        options.maxZoom = Math.max(options.minZoom, options.maxZoom);\n      } else {\n        // make sure minZoom is lte maxZoom\n        options.minZoom = Math.min(options.maxZoom, options.minZoom);\n      }\n      if (typeof options.subdomains === 'string') {\n        options.subdomains = options.subdomains.split('');\n      }\n      this.on('tileunload', this._onTileRemove);\n    },\n    // @method setUrl(url: String, noRedraw?: Boolean): this\n    // Updates the layer's URL template and redraws it (unless `noRedraw` is set to `true`).\n    // If the URL does not change, the layer will not be redrawn unless\n    // the noRedraw parameter is set to false.\n    setUrl: function (url, noRedraw) {\n      if (this._url === url && noRedraw === undefined) {\n        noRedraw = true;\n      }\n      this._url = url;\n      if (!noRedraw) {\n        this.redraw();\n      }\n      return this;\n    },\n    // @method createTile(coords: Object, done?: Function): HTMLElement\n    // Called only internally, overrides GridLayer's [`createTile()`](#gridlayer-createtile)\n    // to return an `<img>` HTML element with the appropriate image URL given `coords`. The `done`\n    // callback is called when the tile has been loaded.\n    createTile: function (coords, done) {\n      var tile = document.createElement('img');\n      on(tile, 'load', bind(this._tileOnLoad, this, done, tile));\n      on(tile, 'error', bind(this._tileOnError, this, done, tile));\n      if (this.options.crossOrigin || this.options.crossOrigin === '') {\n        tile.crossOrigin = this.options.crossOrigin === true ? '' : this.options.crossOrigin;\n      }\n\n      // for this new option we follow the documented behavior\n      // more closely by only setting the property when string\n      if (typeof this.options.referrerPolicy === 'string') {\n        tile.referrerPolicy = this.options.referrerPolicy;\n      }\n\n      // The alt attribute is set to the empty string,\n      // allowing screen readers to ignore the decorative image tiles.\n      // https://www.w3.org/WAI/tutorials/images/decorative/\n      // https://www.w3.org/TR/html-aria/#el-img-empty-alt\n      tile.alt = '';\n      tile.src = this.getTileUrl(coords);\n      return tile;\n    },\n    // @section Extension methods\n    // @uninheritable\n    // Layers extending `TileLayer` might reimplement the following method.\n    // @method getTileUrl(coords: Object): String\n    // Called only internally, returns the URL for a tile given its coordinates.\n    // Classes extending `TileLayer` can override this function to provide custom tile URL naming schemes.\n    getTileUrl: function (coords) {\n      var data = {\n        r: Browser.retina ? '@2x' : '',\n        s: this._getSubdomain(coords),\n        x: coords.x,\n        y: coords.y,\n        z: this._getZoomForUrl()\n      };\n      if (this._map && !this._map.options.crs.infinite) {\n        var invertedY = this._globalTileRange.max.y - coords.y;\n        if (this.options.tms) {\n          data['y'] = invertedY;\n        }\n        data['-y'] = invertedY;\n      }\n      return template(this._url, extend(data, this.options));\n    },\n    _tileOnLoad: function (done, tile) {\n      // For https://github.com/Leaflet/Leaflet/issues/3332\n      if (Browser.ielt9) {\n        setTimeout(bind(done, this, null, tile), 0);\n      } else {\n        done(null, tile);\n      }\n    },\n    _tileOnError: function (done, tile, e) {\n      var errorUrl = this.options.errorTileUrl;\n      if (errorUrl && tile.getAttribute('src') !== errorUrl) {\n        tile.src = errorUrl;\n      }\n      done(e, tile);\n    },\n    _onTileRemove: function (e) {\n      e.tile.onload = null;\n    },\n    _getZoomForUrl: function () {\n      var zoom = this._tileZoom,\n        maxZoom = this.options.maxZoom,\n        zoomReverse = this.options.zoomReverse,\n        zoomOffset = this.options.zoomOffset;\n      if (zoomReverse) {\n        zoom = maxZoom - zoom;\n      }\n      return zoom + zoomOffset;\n    },\n    _getSubdomain: function (tilePoint) {\n      var index = Math.abs(tilePoint.x + tilePoint.y) % this.options.subdomains.length;\n      return this.options.subdomains[index];\n    },\n    // stops loading all tiles in the background layer\n    _abortLoading: function () {\n      var i, tile;\n      for (i in this._tiles) {\n        if (this._tiles[i].coords.z !== this._tileZoom) {\n          tile = this._tiles[i].el;\n          tile.onload = falseFn;\n          tile.onerror = falseFn;\n          if (!tile.complete) {\n            tile.src = emptyImageUrl;\n            var coords = this._tiles[i].coords;\n            remove(tile);\n            delete this._tiles[i];\n            // @event tileabort: TileEvent\n            // Fired when a tile was loading but is now not wanted.\n            this.fire('tileabort', {\n              tile: tile,\n              coords: coords\n            });\n          }\n        }\n      }\n    },\n    _removeTile: function (key) {\n      var tile = this._tiles[key];\n      if (!tile) {\n        return;\n      }\n\n      // Cancels any pending http requests associated with the tile\n      tile.el.setAttribute('src', emptyImageUrl);\n      return GridLayer.prototype._removeTile.call(this, key);\n    },\n    _tileReady: function (coords, err, tile) {\n      if (!this._map || tile && tile.getAttribute('src') === emptyImageUrl) {\n        return;\n      }\n      return GridLayer.prototype._tileReady.call(this, coords, err, tile);\n    }\n  });\n\n  // @factory L.tilelayer(urlTemplate: String, options?: TileLayer options)\n  // Instantiates a tile layer object given a `URL template` and optionally an options object.\n\n  function tileLayer(url, options) {\n    return new TileLayer(url, options);\n  }\n\n  /*\r\n   * @class TileLayer.WMS\r\n   * @inherits TileLayer\r\n   * @aka L.TileLayer.WMS\r\n   * Used to display [WMS](https://en.wikipedia.org/wiki/Web_Map_Service) services as tile layers on the map. Extends `TileLayer`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```js\r\n   * var nexrad = L.tileLayer.wms(\"http://mesonet.agron.iastate.edu/cgi-bin/wms/nexrad/n0r.cgi\", {\r\n   * \tlayers: 'nexrad-n0r-900913',\r\n   * \tformat: 'image/png',\r\n   * \ttransparent: true,\r\n   * \tattribution: \"Weather data © 2012 IEM Nexrad\"\r\n   * });\r\n   * ```\r\n   */\n\n  var TileLayerWMS = TileLayer.extend({\n    // @section\n    // @aka TileLayer.WMS options\n    // If any custom options not documented here are used, they will be sent to the\n    // WMS server as extra parameters in each request URL. This can be useful for\n    // [non-standard vendor WMS parameters](https://docs.geoserver.org/stable/en/user/services/wms/vendor.html).\n    defaultWmsParams: {\n      service: 'WMS',\n      request: 'GetMap',\n      // @option layers: String = ''\n      // **(required)** Comma-separated list of WMS layers to show.\n      layers: '',\n      // @option styles: String = ''\n      // Comma-separated list of WMS styles.\n      styles: '',\n      // @option format: String = 'image/jpeg'\n      // WMS image format (use `'image/png'` for layers with transparency).\n      format: 'image/jpeg',\n      // @option transparent: Boolean = false\n      // If `true`, the WMS service will return images with transparency.\n      transparent: false,\n      // @option version: String = '1.1.1'\n      // Version of the WMS service to use\n      version: '1.1.1'\n    },\n    options: {\n      // @option crs: CRS = null\n      // Coordinate Reference System to use for the WMS requests, defaults to\n      // map CRS. Don't change this if you're not sure what it means.\n      crs: null,\n      // @option uppercase: Boolean = false\n      // If `true`, WMS request parameter keys will be uppercase.\n      uppercase: false\n    },\n    initialize: function (url, options) {\n      this._url = url;\n      var wmsParams = extend({}, this.defaultWmsParams);\n\n      // all keys that are not TileLayer options go to WMS params\n      for (var i in options) {\n        if (!(i in this.options)) {\n          wmsParams[i] = options[i];\n        }\n      }\n      options = setOptions(this, options);\n      var realRetina = options.detectRetina && Browser.retina ? 2 : 1;\n      var tileSize = this.getTileSize();\n      wmsParams.width = tileSize.x * realRetina;\n      wmsParams.height = tileSize.y * realRetina;\n      this.wmsParams = wmsParams;\n    },\n    onAdd: function (map) {\n      this._crs = this.options.crs || map.options.crs;\n      this._wmsVersion = parseFloat(this.wmsParams.version);\n      var projectionKey = this._wmsVersion >= 1.3 ? 'crs' : 'srs';\n      this.wmsParams[projectionKey] = this._crs.code;\n      TileLayer.prototype.onAdd.call(this, map);\n    },\n    getTileUrl: function (coords) {\n      var tileBounds = this._tileCoordsToNwSe(coords),\n        crs = this._crs,\n        bounds = toBounds(crs.project(tileBounds[0]), crs.project(tileBounds[1])),\n        min = bounds.min,\n        max = bounds.max,\n        bbox = (this._wmsVersion >= 1.3 && this._crs === EPSG4326 ? [min.y, min.x, max.y, max.x] : [min.x, min.y, max.x, max.y]).join(','),\n        url = TileLayer.prototype.getTileUrl.call(this, coords);\n      return url + getParamString(this.wmsParams, url, this.options.uppercase) + (this.options.uppercase ? '&BBOX=' : '&bbox=') + bbox;\n    },\n    // @method setParams(params: Object, noRedraw?: Boolean): this\n    // Merges an object with the new parameters and re-requests tiles on the current screen (unless `noRedraw` was set to true).\n    setParams: function (params, noRedraw) {\n      extend(this.wmsParams, params);\n      if (!noRedraw) {\n        this.redraw();\n      }\n      return this;\n    }\n  });\n\n  // @factory L.tileLayer.wms(baseUrl: String, options: TileLayer.WMS options)\n  // Instantiates a WMS tile layer object given a base URL of the WMS service and a WMS parameters/options object.\n  function tileLayerWMS(url, options) {\n    return new TileLayerWMS(url, options);\n  }\n  TileLayer.WMS = TileLayerWMS;\n  tileLayer.wms = tileLayerWMS;\n\n  /*\n   * @class Renderer\n   * @inherits Layer\n   * @aka L.Renderer\n   *\n   * Base class for vector renderer implementations (`SVG`, `Canvas`). Handles the\n   * DOM container of the renderer, its bounds, and its zoom animation.\n   *\n   * A `Renderer` works as an implicit layer group for all `Path`s - the renderer\n   * itself can be added or removed to the map. All paths use a renderer, which can\n   * be implicit (the map will decide the type of renderer and use it automatically)\n   * or explicit (using the [`renderer`](#path-renderer) option of the path).\n   *\n   * Do not use this class directly, use `SVG` and `Canvas` instead.\n   *\n   * @event update: Event\n   * Fired when the renderer updates its bounds, center and zoom, for example when\n   * its map has moved\n   */\n\n  var Renderer = Layer.extend({\n    // @section\n    // @aka Renderer options\n    options: {\n      // @option padding: Number = 0.1\n      // How much to extend the clip area around the map view (relative to its size)\n      // e.g. 0.1 would be 10% of map view in each direction\n      padding: 0.1\n    },\n    initialize: function (options) {\n      setOptions(this, options);\n      stamp(this);\n      this._layers = this._layers || {};\n    },\n    onAdd: function () {\n      if (!this._container) {\n        this._initContainer(); // defined by renderer implementations\n\n        // always keep transform-origin as 0 0\n        addClass(this._container, 'leaflet-zoom-animated');\n      }\n      this.getPane().appendChild(this._container);\n      this._update();\n      this.on('update', this._updatePaths, this);\n    },\n    onRemove: function () {\n      this.off('update', this._updatePaths, this);\n      this._destroyContainer();\n    },\n    getEvents: function () {\n      var events = {\n        viewreset: this._reset,\n        zoom: this._onZoom,\n        moveend: this._update,\n        zoomend: this._onZoomEnd\n      };\n      if (this._zoomAnimated) {\n        events.zoomanim = this._onAnimZoom;\n      }\n      return events;\n    },\n    _onAnimZoom: function (ev) {\n      this._updateTransform(ev.center, ev.zoom);\n    },\n    _onZoom: function () {\n      this._updateTransform(this._map.getCenter(), this._map.getZoom());\n    },\n    _updateTransform: function (center, zoom) {\n      var scale = this._map.getZoomScale(zoom, this._zoom),\n        viewHalf = this._map.getSize().multiplyBy(0.5 + this.options.padding),\n        currentCenterPoint = this._map.project(this._center, zoom),\n        topLeftOffset = viewHalf.multiplyBy(-scale).add(currentCenterPoint).subtract(this._map._getNewPixelOrigin(center, zoom));\n      if (Browser.any3d) {\n        setTransform(this._container, topLeftOffset, scale);\n      } else {\n        setPosition(this._container, topLeftOffset);\n      }\n    },\n    _reset: function () {\n      this._update();\n      this._updateTransform(this._center, this._zoom);\n      for (var id in this._layers) {\n        this._layers[id]._reset();\n      }\n    },\n    _onZoomEnd: function () {\n      for (var id in this._layers) {\n        this._layers[id]._project();\n      }\n    },\n    _updatePaths: function () {\n      for (var id in this._layers) {\n        this._layers[id]._update();\n      }\n    },\n    _update: function () {\n      // Update pixel bounds of renderer container (for positioning/sizing/clipping later)\n      // Subclasses are responsible of firing the 'update' event.\n      var p = this.options.padding,\n        size = this._map.getSize(),\n        min = this._map.containerPointToLayerPoint(size.multiplyBy(-p)).round();\n      this._bounds = new Bounds(min, min.add(size.multiplyBy(1 + p * 2)).round());\n      this._center = this._map.getCenter();\n      this._zoom = this._map.getZoom();\n    }\n  });\n\n  /*\n   * @class Canvas\n   * @inherits Renderer\n   * @aka L.Canvas\n   *\n   * Allows vector layers to be displayed with [`<canvas>`](https://developer.mozilla.org/docs/Web/API/Canvas_API).\n   * Inherits `Renderer`.\n   *\n   * Due to [technical limitations](https://caniuse.com/canvas), Canvas is not\n   * available in all web browsers, notably IE8, and overlapping geometries might\n   * not display properly in some edge cases.\n   *\n   * @example\n   *\n   * Use Canvas by default for all paths in the map:\n   *\n   * ```js\n   * var map = L.map('map', {\n   * \trenderer: L.canvas()\n   * });\n   * ```\n   *\n   * Use a Canvas renderer with extra padding for specific vector geometries:\n   *\n   * ```js\n   * var map = L.map('map');\n   * var myRenderer = L.canvas({ padding: 0.5 });\n   * var line = L.polyline( coordinates, { renderer: myRenderer } );\n   * var circle = L.circle( center, { renderer: myRenderer } );\n   * ```\n   */\n\n  var Canvas = Renderer.extend({\n    // @section\n    // @aka Canvas options\n    options: {\n      // @option tolerance: Number = 0\n      // How much to extend the click tolerance around a path/object on the map.\n      tolerance: 0\n    },\n    getEvents: function () {\n      var events = Renderer.prototype.getEvents.call(this);\n      events.viewprereset = this._onViewPreReset;\n      return events;\n    },\n    _onViewPreReset: function () {\n      // Set a flag so that a viewprereset+moveend+viewreset only updates&redraws once\n      this._postponeUpdatePaths = true;\n    },\n    onAdd: function () {\n      Renderer.prototype.onAdd.call(this);\n\n      // Redraw vectors since canvas is cleared upon removal,\n      // in case of removing the renderer itself from the map.\n      this._draw();\n    },\n    _initContainer: function () {\n      var container = this._container = document.createElement('canvas');\n      on(container, 'mousemove', this._onMouseMove, this);\n      on(container, 'click dblclick mousedown mouseup contextmenu', this._onClick, this);\n      on(container, 'mouseout', this._handleMouseOut, this);\n      container['_leaflet_disable_events'] = true;\n      this._ctx = container.getContext('2d');\n    },\n    _destroyContainer: function () {\n      cancelAnimFrame(this._redrawRequest);\n      delete this._ctx;\n      remove(this._container);\n      off(this._container);\n      delete this._container;\n    },\n    _updatePaths: function () {\n      if (this._postponeUpdatePaths) {\n        return;\n      }\n      var layer;\n      this._redrawBounds = null;\n      for (var id in this._layers) {\n        layer = this._layers[id];\n        layer._update();\n      }\n      this._redraw();\n    },\n    _update: function () {\n      if (this._map._animatingZoom && this._bounds) {\n        return;\n      }\n      Renderer.prototype._update.call(this);\n      var b = this._bounds,\n        container = this._container,\n        size = b.getSize(),\n        m = Browser.retina ? 2 : 1;\n      setPosition(container, b.min);\n\n      // set canvas size (also clearing it); use double size on retina\n      container.width = m * size.x;\n      container.height = m * size.y;\n      container.style.width = size.x + 'px';\n      container.style.height = size.y + 'px';\n      if (Browser.retina) {\n        this._ctx.scale(2, 2);\n      }\n\n      // translate so we use the same path coordinates after canvas element moves\n      this._ctx.translate(-b.min.x, -b.min.y);\n\n      // Tell paths to redraw themselves\n      this.fire('update');\n    },\n    _reset: function () {\n      Renderer.prototype._reset.call(this);\n      if (this._postponeUpdatePaths) {\n        this._postponeUpdatePaths = false;\n        this._updatePaths();\n      }\n    },\n    _initPath: function (layer) {\n      this._updateDashArray(layer);\n      this._layers[stamp(layer)] = layer;\n      var order = layer._order = {\n        layer: layer,\n        prev: this._drawLast,\n        next: null\n      };\n      if (this._drawLast) {\n        this._drawLast.next = order;\n      }\n      this._drawLast = order;\n      this._drawFirst = this._drawFirst || this._drawLast;\n    },\n    _addPath: function (layer) {\n      this._requestRedraw(layer);\n    },\n    _removePath: function (layer) {\n      var order = layer._order;\n      var next = order.next;\n      var prev = order.prev;\n      if (next) {\n        next.prev = prev;\n      } else {\n        this._drawLast = prev;\n      }\n      if (prev) {\n        prev.next = next;\n      } else {\n        this._drawFirst = next;\n      }\n      delete layer._order;\n      delete this._layers[stamp(layer)];\n      this._requestRedraw(layer);\n    },\n    _updatePath: function (layer) {\n      // Redraw the union of the layer's old pixel\n      // bounds and the new pixel bounds.\n      this._extendRedrawBounds(layer);\n      layer._project();\n      layer._update();\n      // The redraw will extend the redraw bounds\n      // with the new pixel bounds.\n      this._requestRedraw(layer);\n    },\n    _updateStyle: function (layer) {\n      this._updateDashArray(layer);\n      this._requestRedraw(layer);\n    },\n    _updateDashArray: function (layer) {\n      if (typeof layer.options.dashArray === 'string') {\n        var parts = layer.options.dashArray.split(/[, ]+/),\n          dashArray = [],\n          dashValue,\n          i;\n        for (i = 0; i < parts.length; i++) {\n          dashValue = Number(parts[i]);\n          // Ignore dash array containing invalid lengths\n          if (isNaN(dashValue)) {\n            return;\n          }\n          dashArray.push(dashValue);\n        }\n        layer.options._dashArray = dashArray;\n      } else {\n        layer.options._dashArray = layer.options.dashArray;\n      }\n    },\n    _requestRedraw: function (layer) {\n      if (!this._map) {\n        return;\n      }\n      this._extendRedrawBounds(layer);\n      this._redrawRequest = this._redrawRequest || requestAnimFrame(this._redraw, this);\n    },\n    _extendRedrawBounds: function (layer) {\n      if (layer._pxBounds) {\n        var padding = (layer.options.weight || 0) + 1;\n        this._redrawBounds = this._redrawBounds || new Bounds();\n        this._redrawBounds.extend(layer._pxBounds.min.subtract([padding, padding]));\n        this._redrawBounds.extend(layer._pxBounds.max.add([padding, padding]));\n      }\n    },\n    _redraw: function () {\n      this._redrawRequest = null;\n      if (this._redrawBounds) {\n        this._redrawBounds.min._floor();\n        this._redrawBounds.max._ceil();\n      }\n      this._clear(); // clear layers in redraw bounds\n      this._draw(); // draw layers\n\n      this._redrawBounds = null;\n    },\n    _clear: function () {\n      var bounds = this._redrawBounds;\n      if (bounds) {\n        var size = bounds.getSize();\n        this._ctx.clearRect(bounds.min.x, bounds.min.y, size.x, size.y);\n      } else {\n        this._ctx.save();\n        this._ctx.setTransform(1, 0, 0, 1, 0, 0);\n        this._ctx.clearRect(0, 0, this._container.width, this._container.height);\n        this._ctx.restore();\n      }\n    },\n    _draw: function () {\n      var layer,\n        bounds = this._redrawBounds;\n      this._ctx.save();\n      if (bounds) {\n        var size = bounds.getSize();\n        this._ctx.beginPath();\n        this._ctx.rect(bounds.min.x, bounds.min.y, size.x, size.y);\n        this._ctx.clip();\n      }\n      this._drawing = true;\n      for (var order = this._drawFirst; order; order = order.next) {\n        layer = order.layer;\n        if (!bounds || layer._pxBounds && layer._pxBounds.intersects(bounds)) {\n          layer._updatePath();\n        }\n      }\n      this._drawing = false;\n      this._ctx.restore(); // Restore state before clipping.\n    },\n\n    _updatePoly: function (layer, closed) {\n      if (!this._drawing) {\n        return;\n      }\n      var i,\n        j,\n        len2,\n        p,\n        parts = layer._parts,\n        len = parts.length,\n        ctx = this._ctx;\n      if (!len) {\n        return;\n      }\n      ctx.beginPath();\n      for (i = 0; i < len; i++) {\n        for (j = 0, len2 = parts[i].length; j < len2; j++) {\n          p = parts[i][j];\n          ctx[j ? 'lineTo' : 'moveTo'](p.x, p.y);\n        }\n        if (closed) {\n          ctx.closePath();\n        }\n      }\n      this._fillStroke(ctx, layer);\n\n      // TODO optimization: 1 fill/stroke for all features with equal style instead of 1 for each feature\n    },\n\n    _updateCircle: function (layer) {\n      if (!this._drawing || layer._empty()) {\n        return;\n      }\n      var p = layer._point,\n        ctx = this._ctx,\n        r = Math.max(Math.round(layer._radius), 1),\n        s = (Math.max(Math.round(layer._radiusY), 1) || r) / r;\n      if (s !== 1) {\n        ctx.save();\n        ctx.scale(1, s);\n      }\n      ctx.beginPath();\n      ctx.arc(p.x, p.y / s, r, 0, Math.PI * 2, false);\n      if (s !== 1) {\n        ctx.restore();\n      }\n      this._fillStroke(ctx, layer);\n    },\n    _fillStroke: function (ctx, layer) {\n      var options = layer.options;\n      if (options.fill) {\n        ctx.globalAlpha = options.fillOpacity;\n        ctx.fillStyle = options.fillColor || options.color;\n        ctx.fill(options.fillRule || 'evenodd');\n      }\n      if (options.stroke && options.weight !== 0) {\n        if (ctx.setLineDash) {\n          ctx.setLineDash(layer.options && layer.options._dashArray || []);\n        }\n        ctx.globalAlpha = options.opacity;\n        ctx.lineWidth = options.weight;\n        ctx.strokeStyle = options.color;\n        ctx.lineCap = options.lineCap;\n        ctx.lineJoin = options.lineJoin;\n        ctx.stroke();\n      }\n    },\n    // Canvas obviously doesn't have mouse events for individual drawn objects,\n    // so we emulate that by calculating what's under the mouse on mousemove/click manually\n\n    _onClick: function (e) {\n      var point = this._map.mouseEventToLayerPoint(e),\n        layer,\n        clickedLayer;\n      for (var order = this._drawFirst; order; order = order.next) {\n        layer = order.layer;\n        if (layer.options.interactive && layer._containsPoint(point)) {\n          if (!(e.type === 'click' || e.type === 'preclick') || !this._map._draggableMoved(layer)) {\n            clickedLayer = layer;\n          }\n        }\n      }\n      this._fireEvent(clickedLayer ? [clickedLayer] : false, e);\n    },\n    _onMouseMove: function (e) {\n      if (!this._map || this._map.dragging.moving() || this._map._animatingZoom) {\n        return;\n      }\n      var point = this._map.mouseEventToLayerPoint(e);\n      this._handleMouseHover(e, point);\n    },\n    _handleMouseOut: function (e) {\n      var layer = this._hoveredLayer;\n      if (layer) {\n        // if we're leaving the layer, fire mouseout\n        removeClass(this._container, 'leaflet-interactive');\n        this._fireEvent([layer], e, 'mouseout');\n        this._hoveredLayer = null;\n        this._mouseHoverThrottled = false;\n      }\n    },\n    _handleMouseHover: function (e, point) {\n      if (this._mouseHoverThrottled) {\n        return;\n      }\n      var layer, candidateHoveredLayer;\n      for (var order = this._drawFirst; order; order = order.next) {\n        layer = order.layer;\n        if (layer.options.interactive && layer._containsPoint(point)) {\n          candidateHoveredLayer = layer;\n        }\n      }\n      if (candidateHoveredLayer !== this._hoveredLayer) {\n        this._handleMouseOut(e);\n        if (candidateHoveredLayer) {\n          addClass(this._container, 'leaflet-interactive'); // change cursor\n          this._fireEvent([candidateHoveredLayer], e, 'mouseover');\n          this._hoveredLayer = candidateHoveredLayer;\n        }\n      }\n      this._fireEvent(this._hoveredLayer ? [this._hoveredLayer] : false, e);\n      this._mouseHoverThrottled = true;\n      setTimeout(bind(function () {\n        this._mouseHoverThrottled = false;\n      }, this), 32);\n    },\n    _fireEvent: function (layers, e, type) {\n      this._map._fireDOMEvent(e, type || e.type, layers);\n    },\n    _bringToFront: function (layer) {\n      var order = layer._order;\n      if (!order) {\n        return;\n      }\n      var next = order.next;\n      var prev = order.prev;\n      if (next) {\n        next.prev = prev;\n      } else {\n        // Already last\n        return;\n      }\n      if (prev) {\n        prev.next = next;\n      } else if (next) {\n        // Update first entry unless this is the\n        // single entry\n        this._drawFirst = next;\n      }\n      order.prev = this._drawLast;\n      this._drawLast.next = order;\n      order.next = null;\n      this._drawLast = order;\n      this._requestRedraw(layer);\n    },\n    _bringToBack: function (layer) {\n      var order = layer._order;\n      if (!order) {\n        return;\n      }\n      var next = order.next;\n      var prev = order.prev;\n      if (prev) {\n        prev.next = next;\n      } else {\n        // Already first\n        return;\n      }\n      if (next) {\n        next.prev = prev;\n      } else if (prev) {\n        // Update last entry unless this is the\n        // single entry\n        this._drawLast = prev;\n      }\n      order.prev = null;\n      order.next = this._drawFirst;\n      this._drawFirst.prev = order;\n      this._drawFirst = order;\n      this._requestRedraw(layer);\n    }\n  });\n\n  // @factory L.canvas(options?: Renderer options)\n  // Creates a Canvas renderer with the given options.\n  function canvas(options) {\n    return Browser.canvas ? new Canvas(options) : null;\n  }\n\n  /*\n   * Thanks to Dmitry Baranovsky and his Raphael library for inspiration!\n   */\n\n  var vmlCreate = function () {\n    try {\n      document.namespaces.add('lvml', 'urn:schemas-microsoft-com:vml');\n      return function (name) {\n        return document.createElement('<lvml:' + name + ' class=\"lvml\">');\n      };\n    } catch (e) {\n      // Do not return fn from catch block so `e` can be garbage collected\n      // See https://github.com/Leaflet/Leaflet/pull/7279\n    }\n    return function (name) {\n      return document.createElement('<' + name + ' xmlns=\"urn:schemas-microsoft.com:vml\" class=\"lvml\">');\n    };\n  }();\n\n  /*\n   * @class SVG\n   *\n   *\n   * VML was deprecated in 2012, which means VML functionality exists only for backwards compatibility\n   * with old versions of Internet Explorer.\n   */\n\n  // mixin to redefine some SVG methods to handle VML syntax which is similar but with some differences\n  var vmlMixin = {\n    _initContainer: function () {\n      this._container = create$1('div', 'leaflet-vml-container');\n    },\n    _update: function () {\n      if (this._map._animatingZoom) {\n        return;\n      }\n      Renderer.prototype._update.call(this);\n      this.fire('update');\n    },\n    _initPath: function (layer) {\n      var container = layer._container = vmlCreate('shape');\n      addClass(container, 'leaflet-vml-shape ' + (this.options.className || ''));\n      container.coordsize = '1 1';\n      layer._path = vmlCreate('path');\n      container.appendChild(layer._path);\n      this._updateStyle(layer);\n      this._layers[stamp(layer)] = layer;\n    },\n    _addPath: function (layer) {\n      var container = layer._container;\n      this._container.appendChild(container);\n      if (layer.options.interactive) {\n        layer.addInteractiveTarget(container);\n      }\n    },\n    _removePath: function (layer) {\n      var container = layer._container;\n      remove(container);\n      layer.removeInteractiveTarget(container);\n      delete this._layers[stamp(layer)];\n    },\n    _updateStyle: function (layer) {\n      var stroke = layer._stroke,\n        fill = layer._fill,\n        options = layer.options,\n        container = layer._container;\n      container.stroked = !!options.stroke;\n      container.filled = !!options.fill;\n      if (options.stroke) {\n        if (!stroke) {\n          stroke = layer._stroke = vmlCreate('stroke');\n        }\n        container.appendChild(stroke);\n        stroke.weight = options.weight + 'px';\n        stroke.color = options.color;\n        stroke.opacity = options.opacity;\n        if (options.dashArray) {\n          stroke.dashStyle = isArray(options.dashArray) ? options.dashArray.join(' ') : options.dashArray.replace(/( *, *)/g, ' ');\n        } else {\n          stroke.dashStyle = '';\n        }\n        stroke.endcap = options.lineCap.replace('butt', 'flat');\n        stroke.joinstyle = options.lineJoin;\n      } else if (stroke) {\n        container.removeChild(stroke);\n        layer._stroke = null;\n      }\n      if (options.fill) {\n        if (!fill) {\n          fill = layer._fill = vmlCreate('fill');\n        }\n        container.appendChild(fill);\n        fill.color = options.fillColor || options.color;\n        fill.opacity = options.fillOpacity;\n      } else if (fill) {\n        container.removeChild(fill);\n        layer._fill = null;\n      }\n    },\n    _updateCircle: function (layer) {\n      var p = layer._point.round(),\n        r = Math.round(layer._radius),\n        r2 = Math.round(layer._radiusY || r);\n      this._setPath(layer, layer._empty() ? 'M0 0' : 'AL ' + p.x + ',' + p.y + ' ' + r + ',' + r2 + ' 0,' + 65535 * 360);\n    },\n    _setPath: function (layer, path) {\n      layer._path.v = path;\n    },\n    _bringToFront: function (layer) {\n      toFront(layer._container);\n    },\n    _bringToBack: function (layer) {\n      toBack(layer._container);\n    }\n  };\n  var create = Browser.vml ? vmlCreate : svgCreate;\n\n  /*\n   * @class SVG\n   * @inherits Renderer\n   * @aka L.SVG\n   *\n   * Allows vector layers to be displayed with [SVG](https://developer.mozilla.org/docs/Web/SVG).\n   * Inherits `Renderer`.\n   *\n   * Due to [technical limitations](https://caniuse.com/svg), SVG is not\n   * available in all web browsers, notably Android 2.x and 3.x.\n   *\n   * Although SVG is not available on IE7 and IE8, these browsers support\n   * [VML](https://en.wikipedia.org/wiki/Vector_Markup_Language)\n   * (a now deprecated technology), and the SVG renderer will fall back to VML in\n   * this case.\n   *\n   * @example\n   *\n   * Use SVG by default for all paths in the map:\n   *\n   * ```js\n   * var map = L.map('map', {\n   * \trenderer: L.svg()\n   * });\n   * ```\n   *\n   * Use a SVG renderer with extra padding for specific vector geometries:\n   *\n   * ```js\n   * var map = L.map('map');\n   * var myRenderer = L.svg({ padding: 0.5 });\n   * var line = L.polyline( coordinates, { renderer: myRenderer } );\n   * var circle = L.circle( center, { renderer: myRenderer } );\n   * ```\n   */\n\n  var SVG = Renderer.extend({\n    _initContainer: function () {\n      this._container = create('svg');\n\n      // makes it possible to click through svg root; we'll reset it back in individual paths\n      this._container.setAttribute('pointer-events', 'none');\n      this._rootGroup = create('g');\n      this._container.appendChild(this._rootGroup);\n    },\n    _destroyContainer: function () {\n      remove(this._container);\n      off(this._container);\n      delete this._container;\n      delete this._rootGroup;\n      delete this._svgSize;\n    },\n    _update: function () {\n      if (this._map._animatingZoom && this._bounds) {\n        return;\n      }\n      Renderer.prototype._update.call(this);\n      var b = this._bounds,\n        size = b.getSize(),\n        container = this._container;\n\n      // set size of svg-container if changed\n      if (!this._svgSize || !this._svgSize.equals(size)) {\n        this._svgSize = size;\n        container.setAttribute('width', size.x);\n        container.setAttribute('height', size.y);\n      }\n\n      // movement: update container viewBox so that we don't have to change coordinates of individual layers\n      setPosition(container, b.min);\n      container.setAttribute('viewBox', [b.min.x, b.min.y, size.x, size.y].join(' '));\n      this.fire('update');\n    },\n    // methods below are called by vector layers implementations\n\n    _initPath: function (layer) {\n      var path = layer._path = create('path');\n\n      // @namespace Path\n      // @option className: String = null\n      // Custom class name set on an element. Only for SVG renderer.\n      if (layer.options.className) {\n        addClass(path, layer.options.className);\n      }\n      if (layer.options.interactive) {\n        addClass(path, 'leaflet-interactive');\n      }\n      this._updateStyle(layer);\n      this._layers[stamp(layer)] = layer;\n    },\n    _addPath: function (layer) {\n      if (!this._rootGroup) {\n        this._initContainer();\n      }\n      this._rootGroup.appendChild(layer._path);\n      layer.addInteractiveTarget(layer._path);\n    },\n    _removePath: function (layer) {\n      remove(layer._path);\n      layer.removeInteractiveTarget(layer._path);\n      delete this._layers[stamp(layer)];\n    },\n    _updatePath: function (layer) {\n      layer._project();\n      layer._update();\n    },\n    _updateStyle: function (layer) {\n      var path = layer._path,\n        options = layer.options;\n      if (!path) {\n        return;\n      }\n      if (options.stroke) {\n        path.setAttribute('stroke', options.color);\n        path.setAttribute('stroke-opacity', options.opacity);\n        path.setAttribute('stroke-width', options.weight);\n        path.setAttribute('stroke-linecap', options.lineCap);\n        path.setAttribute('stroke-linejoin', options.lineJoin);\n        if (options.dashArray) {\n          path.setAttribute('stroke-dasharray', options.dashArray);\n        } else {\n          path.removeAttribute('stroke-dasharray');\n        }\n        if (options.dashOffset) {\n          path.setAttribute('stroke-dashoffset', options.dashOffset);\n        } else {\n          path.removeAttribute('stroke-dashoffset');\n        }\n      } else {\n        path.setAttribute('stroke', 'none');\n      }\n      if (options.fill) {\n        path.setAttribute('fill', options.fillColor || options.color);\n        path.setAttribute('fill-opacity', options.fillOpacity);\n        path.setAttribute('fill-rule', options.fillRule || 'evenodd');\n      } else {\n        path.setAttribute('fill', 'none');\n      }\n    },\n    _updatePoly: function (layer, closed) {\n      this._setPath(layer, pointsToPath(layer._parts, closed));\n    },\n    _updateCircle: function (layer) {\n      var p = layer._point,\n        r = Math.max(Math.round(layer._radius), 1),\n        r2 = Math.max(Math.round(layer._radiusY), 1) || r,\n        arc = 'a' + r + ',' + r2 + ' 0 1,0 ';\n\n      // drawing a circle with two half-arcs\n      var d = layer._empty() ? 'M0 0' : 'M' + (p.x - r) + ',' + p.y + arc + r * 2 + ',0 ' + arc + -r * 2 + ',0 ';\n      this._setPath(layer, d);\n    },\n    _setPath: function (layer, path) {\n      layer._path.setAttribute('d', path);\n    },\n    // SVG does not have the concept of zIndex so we resort to changing the DOM order of elements\n    _bringToFront: function (layer) {\n      toFront(layer._path);\n    },\n    _bringToBack: function (layer) {\n      toBack(layer._path);\n    }\n  });\n  if (Browser.vml) {\n    SVG.include(vmlMixin);\n  }\n\n  // @namespace SVG\n  // @factory L.svg(options?: Renderer options)\n  // Creates a SVG renderer with the given options.\n  function svg(options) {\n    return Browser.svg || Browser.vml ? new SVG(options) : null;\n  }\n  Map.include({\n    // @namespace Map; @method getRenderer(layer: Path): Renderer\n    // Returns the instance of `Renderer` that should be used to render the given\n    // `Path`. It will ensure that the `renderer` options of the map and paths\n    // are respected, and that the renderers do exist on the map.\n    getRenderer: function (layer) {\n      // @namespace Path; @option renderer: Renderer\n      // Use this specific instance of `Renderer` for this path. Takes\n      // precedence over the map's [default renderer](#map-renderer).\n      var renderer = layer.options.renderer || this._getPaneRenderer(layer.options.pane) || this.options.renderer || this._renderer;\n      if (!renderer) {\n        renderer = this._renderer = this._createRenderer();\n      }\n      if (!this.hasLayer(renderer)) {\n        this.addLayer(renderer);\n      }\n      return renderer;\n    },\n    _getPaneRenderer: function (name) {\n      if (name === 'overlayPane' || name === undefined) {\n        return false;\n      }\n      var renderer = this._paneRenderers[name];\n      if (renderer === undefined) {\n        renderer = this._createRenderer({\n          pane: name\n        });\n        this._paneRenderers[name] = renderer;\n      }\n      return renderer;\n    },\n    _createRenderer: function (options) {\n      // @namespace Map; @option preferCanvas: Boolean = false\n      // Whether `Path`s should be rendered on a `Canvas` renderer.\n      // By default, all `Path`s are rendered in a `SVG` renderer.\n      return this.options.preferCanvas && canvas(options) || svg(options);\n    }\n  });\n\n  /*\n   * L.Rectangle extends Polygon and creates a rectangle when passed a LatLngBounds object.\n   */\n\n  /*\n   * @class Rectangle\n   * @aka L.Rectangle\n   * @inherits Polygon\n   *\n   * A class for drawing rectangle overlays on a map. Extends `Polygon`.\n   *\n   * @example\n   *\n   * ```js\n   * // define rectangle geographical bounds\n   * var bounds = [[54.559322, -5.767822], [56.1210604, -3.021240]];\n   *\n   * // create an orange rectangle\n   * L.rectangle(bounds, {color: \"#ff7800\", weight: 1}).addTo(map);\n   *\n   * // zoom the map to the rectangle bounds\n   * map.fitBounds(bounds);\n   * ```\n   *\n   */\n\n  var Rectangle = Polygon.extend({\n    initialize: function (latLngBounds, options) {\n      Polygon.prototype.initialize.call(this, this._boundsToLatLngs(latLngBounds), options);\n    },\n    // @method setBounds(latLngBounds: LatLngBounds): this\n    // Redraws the rectangle with the passed bounds.\n    setBounds: function (latLngBounds) {\n      return this.setLatLngs(this._boundsToLatLngs(latLngBounds));\n    },\n    _boundsToLatLngs: function (latLngBounds) {\n      latLngBounds = toLatLngBounds(latLngBounds);\n      return [latLngBounds.getSouthWest(), latLngBounds.getNorthWest(), latLngBounds.getNorthEast(), latLngBounds.getSouthEast()];\n    }\n  });\n\n  // @factory L.rectangle(latLngBounds: LatLngBounds, options?: Polyline options)\n  function rectangle(latLngBounds, options) {\n    return new Rectangle(latLngBounds, options);\n  }\n  SVG.create = create;\n  SVG.pointsToPath = pointsToPath;\n  GeoJSON.geometryToLayer = geometryToLayer;\n  GeoJSON.coordsToLatLng = coordsToLatLng;\n  GeoJSON.coordsToLatLngs = coordsToLatLngs;\n  GeoJSON.latLngToCoords = latLngToCoords;\n  GeoJSON.latLngsToCoords = latLngsToCoords;\n  GeoJSON.getFeature = getFeature;\n  GeoJSON.asFeature = asFeature;\n\n  /*\n   * L.Handler.BoxZoom is used to add shift-drag zoom interaction to the map\n   * (zoom to a selected bounding box), enabled by default.\n   */\n\n  // @namespace Map\n  // @section Interaction Options\n  Map.mergeOptions({\n    // @option boxZoom: Boolean = true\n    // Whether the map can be zoomed to a rectangular area specified by\n    // dragging the mouse while pressing the shift key.\n    boxZoom: true\n  });\n  var BoxZoom = Handler.extend({\n    initialize: function (map) {\n      this._map = map;\n      this._container = map._container;\n      this._pane = map._panes.overlayPane;\n      this._resetStateTimeout = 0;\n      map.on('unload', this._destroy, this);\n    },\n    addHooks: function () {\n      on(this._container, 'mousedown', this._onMouseDown, this);\n    },\n    removeHooks: function () {\n      off(this._container, 'mousedown', this._onMouseDown, this);\n    },\n    moved: function () {\n      return this._moved;\n    },\n    _destroy: function () {\n      remove(this._pane);\n      delete this._pane;\n    },\n    _resetState: function () {\n      this._resetStateTimeout = 0;\n      this._moved = false;\n    },\n    _clearDeferredResetState: function () {\n      if (this._resetStateTimeout !== 0) {\n        clearTimeout(this._resetStateTimeout);\n        this._resetStateTimeout = 0;\n      }\n    },\n    _onMouseDown: function (e) {\n      if (!e.shiftKey || e.which !== 1 && e.button !== 1) {\n        return false;\n      }\n\n      // Clear the deferred resetState if it hasn't executed yet, otherwise it\n      // will interrupt the interaction and orphan a box element in the container.\n      this._clearDeferredResetState();\n      this._resetState();\n      disableTextSelection();\n      disableImageDrag();\n      this._startPoint = this._map.mouseEventToContainerPoint(e);\n      on(document, {\n        contextmenu: stop,\n        mousemove: this._onMouseMove,\n        mouseup: this._onMouseUp,\n        keydown: this._onKeyDown\n      }, this);\n    },\n    _onMouseMove: function (e) {\n      if (!this._moved) {\n        this._moved = true;\n        this._box = create$1('div', 'leaflet-zoom-box', this._container);\n        addClass(this._container, 'leaflet-crosshair');\n        this._map.fire('boxzoomstart');\n      }\n      this._point = this._map.mouseEventToContainerPoint(e);\n      var bounds = new Bounds(this._point, this._startPoint),\n        size = bounds.getSize();\n      setPosition(this._box, bounds.min);\n      this._box.style.width = size.x + 'px';\n      this._box.style.height = size.y + 'px';\n    },\n    _finish: function () {\n      if (this._moved) {\n        remove(this._box);\n        removeClass(this._container, 'leaflet-crosshair');\n      }\n      enableTextSelection();\n      enableImageDrag();\n      off(document, {\n        contextmenu: stop,\n        mousemove: this._onMouseMove,\n        mouseup: this._onMouseUp,\n        keydown: this._onKeyDown\n      }, this);\n    },\n    _onMouseUp: function (e) {\n      if (e.which !== 1 && e.button !== 1) {\n        return;\n      }\n      this._finish();\n      if (!this._moved) {\n        return;\n      }\n      // Postpone to next JS tick so internal click event handling\n      // still see it as \"moved\".\n      this._clearDeferredResetState();\n      this._resetStateTimeout = setTimeout(bind(this._resetState, this), 0);\n      var bounds = new LatLngBounds(this._map.containerPointToLatLng(this._startPoint), this._map.containerPointToLatLng(this._point));\n      this._map.fitBounds(bounds).fire('boxzoomend', {\n        boxZoomBounds: bounds\n      });\n    },\n    _onKeyDown: function (e) {\n      if (e.keyCode === 27) {\n        this._finish();\n        this._clearDeferredResetState();\n        this._resetState();\n      }\n    }\n  });\n\n  // @section Handlers\n  // @property boxZoom: Handler\n  // Box (shift-drag with mouse) zoom handler.\n  Map.addInitHook('addHandler', 'boxZoom', BoxZoom);\n\n  /*\n   * L.Handler.DoubleClickZoom is used to handle double-click zoom on the map, enabled by default.\n   */\n\n  // @namespace Map\n  // @section Interaction Options\n\n  Map.mergeOptions({\n    // @option doubleClickZoom: Boolean|String = true\n    // Whether the map can be zoomed in by double clicking on it and\n    // zoomed out by double clicking while holding shift. If passed\n    // `'center'`, double-click zoom will zoom to the center of the\n    //  view regardless of where the mouse was.\n    doubleClickZoom: true\n  });\n  var DoubleClickZoom = Handler.extend({\n    addHooks: function () {\n      this._map.on('dblclick', this._onDoubleClick, this);\n    },\n    removeHooks: function () {\n      this._map.off('dblclick', this._onDoubleClick, this);\n    },\n    _onDoubleClick: function (e) {\n      var map = this._map,\n        oldZoom = map.getZoom(),\n        delta = map.options.zoomDelta,\n        zoom = e.originalEvent.shiftKey ? oldZoom - delta : oldZoom + delta;\n      if (map.options.doubleClickZoom === 'center') {\n        map.setZoom(zoom);\n      } else {\n        map.setZoomAround(e.containerPoint, zoom);\n      }\n    }\n  });\n\n  // @section Handlers\n  //\n  // Map properties include interaction handlers that allow you to control\n  // interaction behavior in runtime, enabling or disabling certain features such\n  // as dragging or touch zoom (see `Handler` methods). For example:\n  //\n  // ```js\n  // map.doubleClickZoom.disable();\n  // ```\n  //\n  // @property doubleClickZoom: Handler\n  // Double click zoom handler.\n  Map.addInitHook('addHandler', 'doubleClickZoom', DoubleClickZoom);\n\n  /*\n   * L.Handler.MapDrag is used to make the map draggable (with panning inertia), enabled by default.\n   */\n\n  // @namespace Map\n  // @section Interaction Options\n  Map.mergeOptions({\n    // @option dragging: Boolean = true\n    // Whether the map is draggable with mouse/touch or not.\n    dragging: true,\n    // @section Panning Inertia Options\n    // @option inertia: Boolean = *\n    // If enabled, panning of the map will have an inertia effect where\n    // the map builds momentum while dragging and continues moving in\n    // the same direction for some time. Feels especially nice on touch\n    // devices. Enabled by default.\n    inertia: true,\n    // @option inertiaDeceleration: Number = 3000\n    // The rate with which the inertial movement slows down, in pixels/second².\n    inertiaDeceleration: 3400,\n    // px/s^2\n\n    // @option inertiaMaxSpeed: Number = Infinity\n    // Max speed of the inertial movement, in pixels/second.\n    inertiaMaxSpeed: Infinity,\n    // px/s\n\n    // @option easeLinearity: Number = 0.2\n    easeLinearity: 0.2,\n    // TODO refactor, move to CRS\n    // @option worldCopyJump: Boolean = false\n    // With this option enabled, the map tracks when you pan to another \"copy\"\n    // of the world and seamlessly jumps to the original one so that all overlays\n    // like markers and vector layers are still visible.\n    worldCopyJump: false,\n    // @option maxBoundsViscosity: Number = 0.0\n    // If `maxBounds` is set, this option will control how solid the bounds\n    // are when dragging the map around. The default value of `0.0` allows the\n    // user to drag outside the bounds at normal speed, higher values will\n    // slow down map dragging outside bounds, and `1.0` makes the bounds fully\n    // solid, preventing the user from dragging outside the bounds.\n    maxBoundsViscosity: 0.0\n  });\n  var Drag = Handler.extend({\n    addHooks: function () {\n      if (!this._draggable) {\n        var map = this._map;\n        this._draggable = new Draggable(map._mapPane, map._container);\n        this._draggable.on({\n          dragstart: this._onDragStart,\n          drag: this._onDrag,\n          dragend: this._onDragEnd\n        }, this);\n        this._draggable.on('predrag', this._onPreDragLimit, this);\n        if (map.options.worldCopyJump) {\n          this._draggable.on('predrag', this._onPreDragWrap, this);\n          map.on('zoomend', this._onZoomEnd, this);\n          map.whenReady(this._onZoomEnd, this);\n        }\n      }\n      addClass(this._map._container, 'leaflet-grab leaflet-touch-drag');\n      this._draggable.enable();\n      this._positions = [];\n      this._times = [];\n    },\n    removeHooks: function () {\n      removeClass(this._map._container, 'leaflet-grab');\n      removeClass(this._map._container, 'leaflet-touch-drag');\n      this._draggable.disable();\n    },\n    moved: function () {\n      return this._draggable && this._draggable._moved;\n    },\n    moving: function () {\n      return this._draggable && this._draggable._moving;\n    },\n    _onDragStart: function () {\n      var map = this._map;\n      map._stop();\n      if (this._map.options.maxBounds && this._map.options.maxBoundsViscosity) {\n        var bounds = toLatLngBounds(this._map.options.maxBounds);\n        this._offsetLimit = toBounds(this._map.latLngToContainerPoint(bounds.getNorthWest()).multiplyBy(-1), this._map.latLngToContainerPoint(bounds.getSouthEast()).multiplyBy(-1).add(this._map.getSize()));\n        this._viscosity = Math.min(1.0, Math.max(0.0, this._map.options.maxBoundsViscosity));\n      } else {\n        this._offsetLimit = null;\n      }\n      map.fire('movestart').fire('dragstart');\n      if (map.options.inertia) {\n        this._positions = [];\n        this._times = [];\n      }\n    },\n    _onDrag: function (e) {\n      if (this._map.options.inertia) {\n        var time = this._lastTime = +new Date(),\n          pos = this._lastPos = this._draggable._absPos || this._draggable._newPos;\n        this._positions.push(pos);\n        this._times.push(time);\n        this._prunePositions(time);\n      }\n      this._map.fire('move', e).fire('drag', e);\n    },\n    _prunePositions: function (time) {\n      while (this._positions.length > 1 && time - this._times[0] > 50) {\n        this._positions.shift();\n        this._times.shift();\n      }\n    },\n    _onZoomEnd: function () {\n      var pxCenter = this._map.getSize().divideBy(2),\n        pxWorldCenter = this._map.latLngToLayerPoint([0, 0]);\n      this._initialWorldOffset = pxWorldCenter.subtract(pxCenter).x;\n      this._worldWidth = this._map.getPixelWorldBounds().getSize().x;\n    },\n    _viscousLimit: function (value, threshold) {\n      return value - (value - threshold) * this._viscosity;\n    },\n    _onPreDragLimit: function () {\n      if (!this._viscosity || !this._offsetLimit) {\n        return;\n      }\n      var offset = this._draggable._newPos.subtract(this._draggable._startPos);\n      var limit = this._offsetLimit;\n      if (offset.x < limit.min.x) {\n        offset.x = this._viscousLimit(offset.x, limit.min.x);\n      }\n      if (offset.y < limit.min.y) {\n        offset.y = this._viscousLimit(offset.y, limit.min.y);\n      }\n      if (offset.x > limit.max.x) {\n        offset.x = this._viscousLimit(offset.x, limit.max.x);\n      }\n      if (offset.y > limit.max.y) {\n        offset.y = this._viscousLimit(offset.y, limit.max.y);\n      }\n      this._draggable._newPos = this._draggable._startPos.add(offset);\n    },\n    _onPreDragWrap: function () {\n      // TODO refactor to be able to adjust map pane position after zoom\n      var worldWidth = this._worldWidth,\n        halfWidth = Math.round(worldWidth / 2),\n        dx = this._initialWorldOffset,\n        x = this._draggable._newPos.x,\n        newX1 = (x - halfWidth + dx) % worldWidth + halfWidth - dx,\n        newX2 = (x + halfWidth + dx) % worldWidth - halfWidth - dx,\n        newX = Math.abs(newX1 + dx) < Math.abs(newX2 + dx) ? newX1 : newX2;\n      this._draggable._absPos = this._draggable._newPos.clone();\n      this._draggable._newPos.x = newX;\n    },\n    _onDragEnd: function (e) {\n      var map = this._map,\n        options = map.options,\n        noInertia = !options.inertia || e.noInertia || this._times.length < 2;\n      map.fire('dragend', e);\n      if (noInertia) {\n        map.fire('moveend');\n      } else {\n        this._prunePositions(+new Date());\n        var direction = this._lastPos.subtract(this._positions[0]),\n          duration = (this._lastTime - this._times[0]) / 1000,\n          ease = options.easeLinearity,\n          speedVector = direction.multiplyBy(ease / duration),\n          speed = speedVector.distanceTo([0, 0]),\n          limitedSpeed = Math.min(options.inertiaMaxSpeed, speed),\n          limitedSpeedVector = speedVector.multiplyBy(limitedSpeed / speed),\n          decelerationDuration = limitedSpeed / (options.inertiaDeceleration * ease),\n          offset = limitedSpeedVector.multiplyBy(-decelerationDuration / 2).round();\n        if (!offset.x && !offset.y) {\n          map.fire('moveend');\n        } else {\n          offset = map._limitOffset(offset, map.options.maxBounds);\n          requestAnimFrame(function () {\n            map.panBy(offset, {\n              duration: decelerationDuration,\n              easeLinearity: ease,\n              noMoveStart: true,\n              animate: true\n            });\n          });\n        }\n      }\n    }\n  });\n\n  // @section Handlers\n  // @property dragging: Handler\n  // Map dragging handler (by both mouse and touch).\n  Map.addInitHook('addHandler', 'dragging', Drag);\n\n  /*\n   * L.Map.Keyboard is handling keyboard interaction with the map, enabled by default.\n   */\n\n  // @namespace Map\n  // @section Keyboard Navigation Options\n  Map.mergeOptions({\n    // @option keyboard: Boolean = true\n    // Makes the map focusable and allows users to navigate the map with keyboard\n    // arrows and `+`/`-` keys.\n    keyboard: true,\n    // @option keyboardPanDelta: Number = 80\n    // Amount of pixels to pan when pressing an arrow key.\n    keyboardPanDelta: 80\n  });\n  var Keyboard = Handler.extend({\n    keyCodes: {\n      left: [37],\n      right: [39],\n      down: [40],\n      up: [38],\n      zoomIn: [187, 107, 61, 171],\n      zoomOut: [189, 109, 54, 173]\n    },\n    initialize: function (map) {\n      this._map = map;\n      this._setPanDelta(map.options.keyboardPanDelta);\n      this._setZoomDelta(map.options.zoomDelta);\n    },\n    addHooks: function () {\n      var container = this._map._container;\n\n      // make the container focusable by tabbing\n      if (container.tabIndex <= 0) {\n        container.tabIndex = '0';\n      }\n      on(container, {\n        focus: this._onFocus,\n        blur: this._onBlur,\n        mousedown: this._onMouseDown\n      }, this);\n      this._map.on({\n        focus: this._addHooks,\n        blur: this._removeHooks\n      }, this);\n    },\n    removeHooks: function () {\n      this._removeHooks();\n      off(this._map._container, {\n        focus: this._onFocus,\n        blur: this._onBlur,\n        mousedown: this._onMouseDown\n      }, this);\n      this._map.off({\n        focus: this._addHooks,\n        blur: this._removeHooks\n      }, this);\n    },\n    _onMouseDown: function () {\n      if (this._focused) {\n        return;\n      }\n      var body = document.body,\n        docEl = document.documentElement,\n        top = body.scrollTop || docEl.scrollTop,\n        left = body.scrollLeft || docEl.scrollLeft;\n      this._map._container.focus();\n      window.scrollTo(left, top);\n    },\n    _onFocus: function () {\n      this._focused = true;\n      this._map.fire('focus');\n    },\n    _onBlur: function () {\n      this._focused = false;\n      this._map.fire('blur');\n    },\n    _setPanDelta: function (panDelta) {\n      var keys = this._panKeys = {},\n        codes = this.keyCodes,\n        i,\n        len;\n      for (i = 0, len = codes.left.length; i < len; i++) {\n        keys[codes.left[i]] = [-1 * panDelta, 0];\n      }\n      for (i = 0, len = codes.right.length; i < len; i++) {\n        keys[codes.right[i]] = [panDelta, 0];\n      }\n      for (i = 0, len = codes.down.length; i < len; i++) {\n        keys[codes.down[i]] = [0, panDelta];\n      }\n      for (i = 0, len = codes.up.length; i < len; i++) {\n        keys[codes.up[i]] = [0, -1 * panDelta];\n      }\n    },\n    _setZoomDelta: function (zoomDelta) {\n      var keys = this._zoomKeys = {},\n        codes = this.keyCodes,\n        i,\n        len;\n      for (i = 0, len = codes.zoomIn.length; i < len; i++) {\n        keys[codes.zoomIn[i]] = zoomDelta;\n      }\n      for (i = 0, len = codes.zoomOut.length; i < len; i++) {\n        keys[codes.zoomOut[i]] = -zoomDelta;\n      }\n    },\n    _addHooks: function () {\n      on(document, 'keydown', this._onKeyDown, this);\n    },\n    _removeHooks: function () {\n      off(document, 'keydown', this._onKeyDown, this);\n    },\n    _onKeyDown: function (e) {\n      if (e.altKey || e.ctrlKey || e.metaKey) {\n        return;\n      }\n      var key = e.keyCode,\n        map = this._map,\n        offset;\n      if (key in this._panKeys) {\n        if (!map._panAnim || !map._panAnim._inProgress) {\n          offset = this._panKeys[key];\n          if (e.shiftKey) {\n            offset = toPoint(offset).multiplyBy(3);\n          }\n          if (map.options.maxBounds) {\n            offset = map._limitOffset(toPoint(offset), map.options.maxBounds);\n          }\n          if (map.options.worldCopyJump) {\n            var newLatLng = map.wrapLatLng(map.unproject(map.project(map.getCenter()).add(offset)));\n            map.panTo(newLatLng);\n          } else {\n            map.panBy(offset);\n          }\n        }\n      } else if (key in this._zoomKeys) {\n        map.setZoom(map.getZoom() + (e.shiftKey ? 3 : 1) * this._zoomKeys[key]);\n      } else if (key === 27 && map._popup && map._popup.options.closeOnEscapeKey) {\n        map.closePopup();\n      } else {\n        return;\n      }\n      stop(e);\n    }\n  });\n\n  // @section Handlers\n  // @section Handlers\n  // @property keyboard: Handler\n  // Keyboard navigation handler.\n  Map.addInitHook('addHandler', 'keyboard', Keyboard);\n\n  /*\n   * L.Handler.ScrollWheelZoom is used by L.Map to enable mouse scroll wheel zoom on the map.\n   */\n\n  // @namespace Map\n  // @section Interaction Options\n  Map.mergeOptions({\n    // @section Mouse wheel options\n    // @option scrollWheelZoom: Boolean|String = true\n    // Whether the map can be zoomed by using the mouse wheel. If passed `'center'`,\n    // it will zoom to the center of the view regardless of where the mouse was.\n    scrollWheelZoom: true,\n    // @option wheelDebounceTime: Number = 40\n    // Limits the rate at which a wheel can fire (in milliseconds). By default\n    // user can't zoom via wheel more often than once per 40 ms.\n    wheelDebounceTime: 40,\n    // @option wheelPxPerZoomLevel: Number = 60\n    // How many scroll pixels (as reported by [L.DomEvent.getWheelDelta](#domevent-getwheeldelta))\n    // mean a change of one full zoom level. Smaller values will make wheel-zooming\n    // faster (and vice versa).\n    wheelPxPerZoomLevel: 60\n  });\n  var ScrollWheelZoom = Handler.extend({\n    addHooks: function () {\n      on(this._map._container, 'wheel', this._onWheelScroll, this);\n      this._delta = 0;\n    },\n    removeHooks: function () {\n      off(this._map._container, 'wheel', this._onWheelScroll, this);\n    },\n    _onWheelScroll: function (e) {\n      var delta = getWheelDelta(e);\n      var debounce = this._map.options.wheelDebounceTime;\n      this._delta += delta;\n      this._lastMousePos = this._map.mouseEventToContainerPoint(e);\n      if (!this._startTime) {\n        this._startTime = +new Date();\n      }\n      var left = Math.max(debounce - (+new Date() - this._startTime), 0);\n      clearTimeout(this._timer);\n      this._timer = setTimeout(bind(this._performZoom, this), left);\n      stop(e);\n    },\n    _performZoom: function () {\n      var map = this._map,\n        zoom = map.getZoom(),\n        snap = this._map.options.zoomSnap || 0;\n      map._stop(); // stop panning and fly animations if any\n\n      // map the delta with a sigmoid function to -4..4 range leaning on -1..1\n      var d2 = this._delta / (this._map.options.wheelPxPerZoomLevel * 4),\n        d3 = 4 * Math.log(2 / (1 + Math.exp(-Math.abs(d2)))) / Math.LN2,\n        d4 = snap ? Math.ceil(d3 / snap) * snap : d3,\n        delta = map._limitZoom(zoom + (this._delta > 0 ? d4 : -d4)) - zoom;\n      this._delta = 0;\n      this._startTime = null;\n      if (!delta) {\n        return;\n      }\n      if (map.options.scrollWheelZoom === 'center') {\n        map.setZoom(zoom + delta);\n      } else {\n        map.setZoomAround(this._lastMousePos, zoom + delta);\n      }\n    }\n  });\n\n  // @section Handlers\n  // @property scrollWheelZoom: Handler\n  // Scroll wheel zoom handler.\n  Map.addInitHook('addHandler', 'scrollWheelZoom', ScrollWheelZoom);\n\n  /*\n   * L.Map.TapHold is used to simulate `contextmenu` event on long hold,\n   * which otherwise is not fired by mobile Safari.\n   */\n\n  var tapHoldDelay = 600;\n\n  // @namespace Map\n  // @section Interaction Options\n  Map.mergeOptions({\n    // @section Touch interaction options\n    // @option tapHold: Boolean\n    // Enables simulation of `contextmenu` event, default is `true` for mobile Safari.\n    tapHold: Browser.touchNative && Browser.safari && Browser.mobile,\n    // @option tapTolerance: Number = 15\n    // The max number of pixels a user can shift his finger during touch\n    // for it to be considered a valid tap.\n    tapTolerance: 15\n  });\n  var TapHold = Handler.extend({\n    addHooks: function () {\n      on(this._map._container, 'touchstart', this._onDown, this);\n    },\n    removeHooks: function () {\n      off(this._map._container, 'touchstart', this._onDown, this);\n    },\n    _onDown: function (e) {\n      clearTimeout(this._holdTimeout);\n      if (e.touches.length !== 1) {\n        return;\n      }\n      var first = e.touches[0];\n      this._startPos = this._newPos = new Point(first.clientX, first.clientY);\n      this._holdTimeout = setTimeout(bind(function () {\n        this._cancel();\n        if (!this._isTapValid()) {\n          return;\n        }\n\n        // prevent simulated mouse events https://w3c.github.io/touch-events/#mouse-events\n        on(document, 'touchend', preventDefault);\n        on(document, 'touchend touchcancel', this._cancelClickPrevent);\n        this._simulateEvent('contextmenu', first);\n      }, this), tapHoldDelay);\n      on(document, 'touchend touchcancel contextmenu', this._cancel, this);\n      on(document, 'touchmove', this._onMove, this);\n    },\n    _cancelClickPrevent: function cancelClickPrevent() {\n      off(document, 'touchend', preventDefault);\n      off(document, 'touchend touchcancel', cancelClickPrevent);\n    },\n    _cancel: function () {\n      clearTimeout(this._holdTimeout);\n      off(document, 'touchend touchcancel contextmenu', this._cancel, this);\n      off(document, 'touchmove', this._onMove, this);\n    },\n    _onMove: function (e) {\n      var first = e.touches[0];\n      this._newPos = new Point(first.clientX, first.clientY);\n    },\n    _isTapValid: function () {\n      return this._newPos.distanceTo(this._startPos) <= this._map.options.tapTolerance;\n    },\n    _simulateEvent: function (type, e) {\n      var simulatedEvent = new MouseEvent(type, {\n        bubbles: true,\n        cancelable: true,\n        view: window,\n        // detail: 1,\n        screenX: e.screenX,\n        screenY: e.screenY,\n        clientX: e.clientX,\n        clientY: e.clientY\n        // button: 2,\n        // buttons: 2\n      });\n\n      simulatedEvent._simulated = true;\n      e.target.dispatchEvent(simulatedEvent);\n    }\n  });\n\n  // @section Handlers\n  // @property tapHold: Handler\n  // Long tap handler to simulate `contextmenu` event (useful in mobile Safari).\n  Map.addInitHook('addHandler', 'tapHold', TapHold);\n\n  /*\n   * L.Handler.TouchZoom is used by L.Map to add pinch zoom on supported mobile browsers.\n   */\n\n  // @namespace Map\n  // @section Interaction Options\n  Map.mergeOptions({\n    // @section Touch interaction options\n    // @option touchZoom: Boolean|String = *\n    // Whether the map can be zoomed by touch-dragging with two fingers. If\n    // passed `'center'`, it will zoom to the center of the view regardless of\n    // where the touch events (fingers) were. Enabled for touch-capable web\n    // browsers.\n    touchZoom: Browser.touch,\n    // @option bounceAtZoomLimits: Boolean = true\n    // Set it to false if you don't want the map to zoom beyond min/max zoom\n    // and then bounce back when pinch-zooming.\n    bounceAtZoomLimits: true\n  });\n  var TouchZoom = Handler.extend({\n    addHooks: function () {\n      addClass(this._map._container, 'leaflet-touch-zoom');\n      on(this._map._container, 'touchstart', this._onTouchStart, this);\n    },\n    removeHooks: function () {\n      removeClass(this._map._container, 'leaflet-touch-zoom');\n      off(this._map._container, 'touchstart', this._onTouchStart, this);\n    },\n    _onTouchStart: function (e) {\n      var map = this._map;\n      if (!e.touches || e.touches.length !== 2 || map._animatingZoom || this._zooming) {\n        return;\n      }\n      var p1 = map.mouseEventToContainerPoint(e.touches[0]),\n        p2 = map.mouseEventToContainerPoint(e.touches[1]);\n      this._centerPoint = map.getSize()._divideBy(2);\n      this._startLatLng = map.containerPointToLatLng(this._centerPoint);\n      if (map.options.touchZoom !== 'center') {\n        this._pinchStartLatLng = map.containerPointToLatLng(p1.add(p2)._divideBy(2));\n      }\n      this._startDist = p1.distanceTo(p2);\n      this._startZoom = map.getZoom();\n      this._moved = false;\n      this._zooming = true;\n      map._stop();\n      on(document, 'touchmove', this._onTouchMove, this);\n      on(document, 'touchend touchcancel', this._onTouchEnd, this);\n      preventDefault(e);\n    },\n    _onTouchMove: function (e) {\n      if (!e.touches || e.touches.length !== 2 || !this._zooming) {\n        return;\n      }\n      var map = this._map,\n        p1 = map.mouseEventToContainerPoint(e.touches[0]),\n        p2 = map.mouseEventToContainerPoint(e.touches[1]),\n        scale = p1.distanceTo(p2) / this._startDist;\n      this._zoom = map.getScaleZoom(scale, this._startZoom);\n      if (!map.options.bounceAtZoomLimits && (this._zoom < map.getMinZoom() && scale < 1 || this._zoom > map.getMaxZoom() && scale > 1)) {\n        this._zoom = map._limitZoom(this._zoom);\n      }\n      if (map.options.touchZoom === 'center') {\n        this._center = this._startLatLng;\n        if (scale === 1) {\n          return;\n        }\n      } else {\n        // Get delta from pinch to center, so centerLatLng is delta applied to initial pinchLatLng\n        var delta = p1._add(p2)._divideBy(2)._subtract(this._centerPoint);\n        if (scale === 1 && delta.x === 0 && delta.y === 0) {\n          return;\n        }\n        this._center = map.unproject(map.project(this._pinchStartLatLng, this._zoom).subtract(delta), this._zoom);\n      }\n      if (!this._moved) {\n        map._moveStart(true, false);\n        this._moved = true;\n      }\n      cancelAnimFrame(this._animRequest);\n      var moveFn = bind(map._move, map, this._center, this._zoom, {\n        pinch: true,\n        round: false\n      }, undefined);\n      this._animRequest = requestAnimFrame(moveFn, this, true);\n      preventDefault(e);\n    },\n    _onTouchEnd: function () {\n      if (!this._moved || !this._zooming) {\n        this._zooming = false;\n        return;\n      }\n      this._zooming = false;\n      cancelAnimFrame(this._animRequest);\n      off(document, 'touchmove', this._onTouchMove, this);\n      off(document, 'touchend touchcancel', this._onTouchEnd, this);\n\n      // Pinch updates GridLayers' levels only when zoomSnap is off, so zoomSnap becomes noUpdate.\n      if (this._map.options.zoomAnimation) {\n        this._map._animateZoom(this._center, this._map._limitZoom(this._zoom), true, this._map.options.zoomSnap);\n      } else {\n        this._map._resetView(this._center, this._map._limitZoom(this._zoom));\n      }\n    }\n  });\n\n  // @section Handlers\n  // @property touchZoom: Handler\n  // Touch zoom handler.\n  Map.addInitHook('addHandler', 'touchZoom', TouchZoom);\n  Map.BoxZoom = BoxZoom;\n  Map.DoubleClickZoom = DoubleClickZoom;\n  Map.Drag = Drag;\n  Map.Keyboard = Keyboard;\n  Map.ScrollWheelZoom = ScrollWheelZoom;\n  Map.TapHold = TapHold;\n  Map.TouchZoom = TouchZoom;\n  exports.Bounds = Bounds;\n  exports.Browser = Browser;\n  exports.CRS = CRS;\n  exports.Canvas = Canvas;\n  exports.Circle = Circle;\n  exports.CircleMarker = CircleMarker;\n  exports.Class = Class;\n  exports.Control = Control;\n  exports.DivIcon = DivIcon;\n  exports.DivOverlay = DivOverlay;\n  exports.DomEvent = DomEvent;\n  exports.DomUtil = DomUtil;\n  exports.Draggable = Draggable;\n  exports.Evented = Evented;\n  exports.FeatureGroup = FeatureGroup;\n  exports.GeoJSON = GeoJSON;\n  exports.GridLayer = GridLayer;\n  exports.Handler = Handler;\n  exports.Icon = Icon;\n  exports.ImageOverlay = ImageOverlay;\n  exports.LatLng = LatLng;\n  exports.LatLngBounds = LatLngBounds;\n  exports.Layer = Layer;\n  exports.LayerGroup = LayerGroup;\n  exports.LineUtil = LineUtil;\n  exports.Map = Map;\n  exports.Marker = Marker;\n  exports.Mixin = Mixin;\n  exports.Path = Path;\n  exports.Point = Point;\n  exports.PolyUtil = PolyUtil;\n  exports.Polygon = Polygon;\n  exports.Polyline = Polyline;\n  exports.Popup = Popup;\n  exports.PosAnimation = PosAnimation;\n  exports.Projection = index;\n  exports.Rectangle = Rectangle;\n  exports.Renderer = Renderer;\n  exports.SVG = SVG;\n  exports.SVGOverlay = SVGOverlay;\n  exports.TileLayer = TileLayer;\n  exports.Tooltip = Tooltip;\n  exports.Transformation = Transformation;\n  exports.Util = Util;\n  exports.VideoOverlay = VideoOverlay;\n  exports.bind = bind;\n  exports.bounds = toBounds;\n  exports.canvas = canvas;\n  exports.circle = circle;\n  exports.circleMarker = circleMarker;\n  exports.control = control;\n  exports.divIcon = divIcon;\n  exports.extend = extend;\n  exports.featureGroup = featureGroup;\n  exports.geoJSON = geoJSON;\n  exports.geoJson = geoJson;\n  exports.gridLayer = gridLayer;\n  exports.icon = icon;\n  exports.imageOverlay = imageOverlay;\n  exports.latLng = toLatLng;\n  exports.latLngBounds = toLatLngBounds;\n  exports.layerGroup = layerGroup;\n  exports.map = createMap;\n  exports.marker = marker;\n  exports.point = toPoint;\n  exports.polygon = polygon;\n  exports.polyline = polyline;\n  exports.popup = popup;\n  exports.rectangle = rectangle;\n  exports.setOptions = setOptions;\n  exports.stamp = stamp;\n  exports.svg = svg;\n  exports.svgOverlay = svgOverlay;\n  exports.tileLayer = tileLayer;\n  exports.tooltip = tooltip;\n  exports.transformation = toTransformation;\n  exports.version = version;\n  exports.videoOverlay = videoOverlay;\n  var oldL = window.L;\n  exports.noConflict = function () {\n    window.L = oldL;\n    return this;\n  };\n  // Always export us to window global (see #2364)\n  window.L = exports;\n});\n//# sourceMappingURL=leaflet-src.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}