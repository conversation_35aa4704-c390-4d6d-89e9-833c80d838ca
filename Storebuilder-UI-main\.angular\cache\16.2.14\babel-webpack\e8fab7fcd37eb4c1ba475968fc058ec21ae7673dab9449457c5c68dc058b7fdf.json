{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { catchError, Observable, Subject, tap, throwError } from 'rxjs';\nimport { JwtHelperService } from \"@auth0/angular-jwt\";\nimport { environment } from \"src/environments/environment\";\nimport jwt_decode from \"jwt-decode\";\nimport { RefreshTokenViewModel } from '../interface';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"ngx-cookie-service\";\nexport let AppInterceptor = /*#__PURE__*/(() => {\n  class AppInterceptor {\n    platformId;\n    store;\n    authService;\n    router;\n    messageService;\n    cookieService;\n    authTokenService;\n    headers;\n    userToken = '';\n    tenantId;\n    guestToken;\n    clonedRequest;\n    timeInterval;\n    XXSRFTOKEN = '';\n    decoded;\n    helper = new JwtHelperService();\n    isRefreshTokenCalled = false;\n    isExpired = false;\n    called = false;\n    refreshTokenInProgress = false;\n    tokenRefreshedSource = new Subject();\n    tokenRefreshed$ = this.tokenRefreshedSource.asObservable();\n    constructor(platformId, store, authService, router, messageService, cookieService, authTokenService) {\n      this.platformId = platformId;\n      this.store = store;\n      this.authService = authService;\n      this.router = router;\n      this.messageService = messageService;\n      this.cookieService = cookieService;\n      this.authTokenService = authTokenService;\n      this.guestToken = '';\n      this.headers = {\n        authorization: ' ',\n        token: '',\n        lang: '',\n        tenantId: '',\n        currency: '',\n        accept: 'application/json',\n        contentType: 'application/json',\n        allowOrigin: '*',\n        allowHeaders: 'Cookie, Cache-Control, Host, User-Agent, Accept, token, Authorization, currency, lang, origin, x-requested-with, content-type, Accept-Encoding',\n        allowMethods: 'PUT, GET, POST, DELETE, PATCH, OPTIONS',\n        contentPolicy: \"script-src https: 'unsafe-inline' 'unsafe-eval';style-src https: 'unsafe-inline' 'unsafe-eval';img-src https: data:;font-src https: data:;\"\n      };\n      this.getTimeInterval();\n    }\n    addAHeaders(request) {\n      this.userToken = this.cookieService.get('authToken');\n      if (!this.userToken) {\n        let profile = localStorage.getItem('profile');\n        if (profile && profile !== '') {\n          profile = JSON.parse(profile);\n          this.userToken = profile.authToken.replace('bearer ', '');\n          const decoded = jwt_decode(this.userToken);\n          let days = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n          const dateNow = new Date();\n          dateNow.setDate(dateNow.getDate() + parseInt(days));\n          this.cookieService.set('authToken', this.userToken, {\n            expires: dateNow,\n            path: '/',\n            sameSite: 'Strict'\n          });\n        }\n      }\n      if (request.url.includes(\"RefreshToken\") || request.url.includes(\"Login\") || request.url.includes(\"ForgotPassword\")) {\n        this.headers.Authorization = '';\n      } else {\n        this.headers.Authorization = this.userToken == '' ? '' : `Bearer ${this.userToken}`;\n      }\n      this.tenantId = localStorage.getItem('tenantId');\n      if (this.tenantId) {\n        this.headers.tenantId = this.tenantId;\n      }\n      this.headers.lang = localStorage.getItem('lang') ?? 'en';\n      let currency = localStorage.getItem('currency')?.toString();\n      if (currency) {\n        this.headers.currency = currency;\n      } else {\n        this.headers.currency = 'UGX';\n      }\n      this.headers.XXSRFTOKEN = this.store.get('XXSRFTOKEN') || localStorage.getItem('XXSRFTOKEN');\n      if (this.headers.XXSRFTOKEN == undefined) {\n        this.headers.XXSRFTOKEN = '';\n      }\n      const mappedContentType = request.url.includes('connect/token') || request.url.includes('ContactForm/SubmitContact') ? 'application/x-www-form-urlencoded' : this.headers.contentType;\n      if (environment.isMarketPlace) {\n        this.clonedRequest = request.clone({\n          headers: request.headers.set('Authorization', this.headers.Authorization).set('token', this.headers.token).set('tenantId', this.headers.tenantId).set('lang', this.headers.lang).set('currency', this.headers.currency).set('Accept', this.headers.accept).set('Content-Type', mappedContentType).set('X-XSRF-TOKEN', this.headers.XXSRFTOKEN).set('Content-Security-Policy', this.headers.contentPolicy),\n          withCredentials: true\n        });\n      } else {\n        this.clonedRequest = request.clone({\n          headers: request.headers.set('Authorization', this.headers.Authorization).set('token', this.headers.token).set('lang', this.headers.lang).set('currency', this.headers.currency).set('Accept', this.headers.accept).set('Content-Type', this.headers.contentType).set('X-XSRF-TOKEN', this.headers.XXSRFTOKEN).set('Content-Security-Policy', this.headers.contentPolicy),\n          withCredentials: true\n        });\n      }\n      return this.clonedRequest;\n    }\n    refreshToken() {\n      if (this.refreshTokenInProgress) {\n        return new Observable(observer => {\n          this.tokenRefreshed$.subscribe(() => {\n            observer.next(undefined);\n            observer.complete();\n          });\n        });\n      } else {\n        this.refreshTokenInProgress = true;\n        this.userToken = this.cookieService.get('authToken');\n        let refreshToken = localStorage.getItem('refreshToken');\n        let model = new RefreshTokenViewModel();\n        model.AuthToken = this.userToken;\n        model.RefreshToken = refreshToken;\n        return this.authService.refreshToken(model).pipe(tap(res => {\n          this.refreshTokenInProgress = false;\n          let token = res.data.authToken;\n          this.decoded = jwt_decode(token);\n          let days = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n          const dateNow = new Date();\n          dateNow.setDate(dateNow.getDate() + parseInt(days));\n          this.cookieService.set('authToken', token, {\n            expires: dateNow,\n            path: '/',\n            sameSite: 'Strict'\n          });\n          localStorage.setItem('refreshToken', res.data.refreshToken);\n          this.tokenRefreshedSource.next();\n        }), catchError(error => {\n          this.refreshTokenInProgress = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: \"Oops\",\n            detail: 'Something went wrong,please login again'\n          });\n          this.signOut();\n        }));\n      }\n    }\n    handleResponseError(error, request, next) {\n      if (isPlatformBrowser(this.platformId)) {\n        if (error.status == 401 || error.status == 0) {\n          if ((this.router.url.includes('login') || this.router.url.includes('register')) && error.status != 401 && !error.url.includes('market.momo.africa')) {\n            this.messageService.add({\n              severity: 'error',\n              detail: error?.error?.message ? error.error.message : 'Something went wrong'\n            });\n          } else {\n            this.signOut();\n          }\n        } else if (!error.url.includes('/GetCategoriesParentAndChildProductsNew/') && !error.message.includes('Invalid Data')) {\n          this.messageService.add({\n            severity: 'error',\n            detail: error?.error.message ? error.error.message : 'Something went wrong'\n          });\n        }\n        return throwError(error);\n      }\n    }\n    intercept(request, next) {\n      request = this.addAHeaders(request);\n      return next.handle(request).pipe(catchError(error => {\n        return this.handleResponseError(error, request, next);\n      }));\n    }\n    signOut() {\n      const tenantId = localStorage.getItem('tenantId') ?? environment.defaultTenant;\n      const maintenanceMode = localStorage.getItem('maintenanceMode') ?? 'false';\n      const saveCookie = localStorage.getItem('save_cookie') ?? '';\n      const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant');\n      localStorage.clear();\n      localStorage.setItem('maintenanceModeTenant', localMaintenanceTenant || '');\n      this.setStoreData();\n      this.authTokenService.authTokenSet(\"\");\n      this.cookieService.delete('authToken', '/');\n      this.store.set('cartProducts', []);\n      localStorage.setItem('secondaryDefault', 'false');\n      localStorage.setItem('sessionId', '');\n      localStorage.setItem('addedProducts', '');\n      localStorage.setItem('cartId', '');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('auth_enc');\n      localStorage.setItem('tenantId', tenantId);\n      localStorage.setItem('maintenanceMode', maintenanceMode);\n      localStorage.setItem('save_cookie', saveCookie);\n      if (isPlatformBrowser(this.platformId)) {\n        window.location.href = '/';\n      }\n    }\n    NotFound() {\n      this.router.navigate(['/not-found']);\n    }\n    getTimeInterval() {\n      if (this.store.get('timeInterval')) {\n        this.store.subscription('timeInterval').subscribe({\n          next: res => {\n            if (res) {\n              this.timeInterval = res;\n            }\n          },\n          error: err => {\n            console.error(err);\n          }\n        });\n      }\n    }\n    setStoreData() {\n      if (this.store.localStoreNames.length) {\n        this.store.set('refreshToken', null);\n        this.store.set('profile', null);\n        this.store.set('cartProducts', []);\n        this.store.set('favouritesProducts', []);\n        this.store.set('compareProducts', []);\n        this.store.set('socialAccount', null);\n        this.store.set('XXSRFTOKEN', null);\n        this.store.set('notifications', {\n          notifications: [],\n          unreadNotifications: 0\n        });\n        this.store.set('checkoutData', {\n          shipping: null,\n          payment: null,\n          promo: null,\n          steps: null,\n          profile: null,\n          orderId: null\n        });\n      } else {\n        localStorage.setItem('refreshToken', '');\n        localStorage.setItem('timeInterval', '');\n        localStorage.setItem('TenantId', '');\n        localStorage.setItem('userPhone', '');\n        localStorage.setItem('profile', '');\n        localStorage.setItem('cartProducts', JSON.stringify([]));\n        localStorage.setItem('favouritesProducts', JSON.stringify([]));\n        localStorage.setItem('compareProducts', JSON.stringify([]));\n        localStorage.setItem('XXSRFTOKEN', '');\n      }\n    }\n    static ɵfac = function AppInterceptor_Factory(t) {\n      return new (t || AppInterceptor)(i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.MessageService), i0.ɵɵinject(i4.CookieService), i0.ɵɵinject(i1.AuthTokenService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AppInterceptor,\n      factory: AppInterceptor.ɵfac\n    });\n  }\n  return AppInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}