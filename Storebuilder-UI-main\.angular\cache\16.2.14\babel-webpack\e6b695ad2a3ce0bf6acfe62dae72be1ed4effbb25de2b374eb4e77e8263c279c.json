{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let RegisterService = /*#__PURE__*/(() => {\n  class RegisterService {\n    http;\n    toggleBoolean;\n    otpBaseUrl;\n    userBaseUrl;\n    userPassword;\n    username = '';\n    countryId = '';\n    constructor(http) {\n      this.http = http;\n      this.otpBaseUrl = `${environment.apiEndPoint}/Auth/OtpUser`;\n      this.userBaseUrl = `${environment.apiEndPoint}/Auth/User`;\n    }\n    checkMobileNumber(data) {\n      return this.http.post(`${this.otpBaseUrl}/CheckUserNameAvailability`, data);\n    }\n    checkEmailAddress(data) {\n      return this.http.post(`${this.otpBaseUrl}/CheckEmailAvailability`, data);\n    }\n    checkSecondaryMobileNumber(data) {\n      return this.http.post(`${this.userBaseUrl}/CheckUserNumberAvailabilityForSecondaryandPrimary`, data);\n    }\n    checkOTP(data) {\n      return this.http.post(`${this.userBaseUrl}/VerifyMobileNumber`, data);\n    }\n    verifyEmail(data) {\n      return this.http.post(`${this.userBaseUrl}/VerifyEmail`, data);\n    }\n    registerUser(data) {\n      return this.http.post(`${this.userBaseUrl}/RegisterPortal`, data);\n    }\n    registerUserByEmail(data) {\n      return this.http.post(`${this.userBaseUrl}/RegisterPortalByEmailConsumer`, data);\n    }\n    VerifyForgotPassword(data) {\n      return this.http.post(`${this.userBaseUrl}/VerifyForgotPassword`, data);\n    }\n    static ɵfac = function RegisterService_Factory(t) {\n      return new (t || RegisterService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RegisterService,\n      factory: RegisterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RegisterService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}