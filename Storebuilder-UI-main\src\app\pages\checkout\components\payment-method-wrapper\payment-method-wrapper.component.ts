import { Component, EventEmitter, Input, Output, ViewChild, OnInit } from '@angular/core';
import { LightboxPaymentComponent } from '../lightbox-payment/lightbox-payment.component';
import { FeatureFlag } from '@core/enums/payment-method-enum';
import { ApmPaymentComponent } from '../apm-payment/apm-payment.component';
import { InitiateApmWidgetResponse } from '@pages/checkout/modals/APM';
import { LightboxConfig } from '@pages/checkout/modals/lightBox';
import { PaymentStateService } from '@core/services/payment-state.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-payment-method-wrapper',
  templateUrl: './payment-method-wrapper.component.html',
  styleUrls: ['./payment-method-wrapper.component.scss']
})
export class PaymentMethodWrapperComponent implements OnInit {
  @Input() lightboxConfig: LightboxConfig;
  @Input() apmConfig: InitiateApmWidgetResponse;
  @Input() featureFlag: string;
  @Output() onComplete = new EventEmitter<any>();
  @Output() onCancel = new EventEmitter<void>();
  @Output() onError = new EventEmitter<void>();

  @ViewChild(LightboxPaymentComponent) lightboxPayment!: LightboxPaymentComponent;
  @ViewChild(ApmPaymentComponent) ApmPayment!: ApmPaymentComponent;


  constructor(private paymentStateService: PaymentStateService) {}


  ngOnInit() { }

  startPayment(): void {
    if (this.featureFlag === FeatureFlag.LIGHTBOX) {
      this.lightboxPayment?.startPayment();
    } else if (this.featureFlag === FeatureFlag.APM) {
      const currentConfig = this.paymentStateService.getApmConfig();
      if (currentConfig) {
        this.apmConfig = currentConfig;
        this.ApmPayment?.startPayment();
      }
    }
  }


}

