{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nfunction AccordionTab_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r8.accordion.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.iconClass);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.iconClass);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_span_1_Template, 1, 3, \"span\", 9);\n    i0.ɵɵtemplate(2, AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.accordion.collapseIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.accordion.collapseIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r10.accordion.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.iconClass);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r11.iconClass);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_2_span_1_Template, 1, 3, \"span\", 9);\n    i0.ɵɵtemplate(2, AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.accordion.expandIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.accordion.expandIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵtemplate(2, AccordionTab_ng_container_3_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selected);\n  }\n}\nfunction AccordionTab_4_ng_template_0_Template(rf, ctx) {}\nfunction AccordionTab_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccordionTab_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AccordionTab_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.header, \" \");\n  }\n}\nfunction AccordionTab_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_content_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\nfunction AccordionTab_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_11_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.contentTemplate);\n  }\n}\nconst _c0 = [\"*\", [[\"p-header\"]]];\nconst _c1 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nconst _c2 = function (a0) {\n  return {\n    transitionParams: a0\n  };\n};\nconst _c3 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\nconst _c4 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\nconst _c5 = [\"*\", \"p-header\"];\nconst _c6 = [\"*\"];\nlet idx = 0;\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nclass AccordionTab {\n  changeDetector;\n  /**\n   * Used to define the header of the tab.\n   * @group Props\n   */\n  header;\n  /**\n   * Inline style of the tab header.\n   * @group Props\n   */\n  headerStyle;\n  /**\n   * Inline style of the tab.\n   * @group Props\n   */\n  tabStyle;\n  /**\n   * Inline style of the tab content.\n   * @group Props\n   */\n  contentStyle;\n  /**\n   * Style class of the tab.\n   * @group Props\n   */\n  tabStyleClass;\n  /**\n   * Style class of the tab header.\n   * @group Props\n   */\n  headerStyleClass;\n  /**\n   * Style class of the tab content.\n   * @group Props\n   */\n  contentStyleClass;\n  /**\n   * Whether the tab is disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n   * @group Props\n   */\n  cache = true;\n  /**\n   * Transition options of the animation.\n   * @group Props\n   */\n  transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  /**\n   * Position of the icon.\n   * @group Props\n   */\n  iconPos = 'start';\n  /**\n   * Event triggered by changing the choice.\n   * @param {boolean} value - Boolean value indicates that the option is changed.\n   * @group Emits\n   */\n  selectedChange = new EventEmitter();\n  headerFacet;\n  templates;\n  _selected = false;\n  /**\n   * The value that returns the selection.\n   * @group Props\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(val) {\n    this._selected = val;\n    if (!this.loaded) {\n      if (this._selected && this.cache) {\n        this.loaded = true;\n      }\n      this.changeDetector.detectChanges();\n    }\n  }\n  get iconClass() {\n    if (this.iconPos === 'end') {\n      return 'p-accordion-toggle-icon-end';\n    } else {\n      return 'p-accordion-toggle-icon';\n    }\n  }\n  contentTemplate;\n  headerTemplate;\n  iconTemplate;\n  id = `p-accordiontab-${idx++}`;\n  loaded = false;\n  accordion;\n  constructor(accordion, changeDetector) {\n    this.changeDetector = changeDetector;\n    this.accordion = accordion;\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n        case 'icon':\n          this.iconTemplate = item.template;\n          break;\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  toggle(event) {\n    if (this.disabled) {\n      return false;\n    }\n    let index = this.findTabIndex();\n    if (this.selected) {\n      this.selected = false;\n      this.accordion.onClose.emit({\n        originalEvent: event,\n        index: index\n      });\n    } else {\n      if (!this.accordion.multiple) {\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n          if (this.accordion.tabs[i].selected) {\n            this.accordion.tabs[i].selected = false;\n            this.accordion.tabs[i].selectedChange.emit(false);\n            this.accordion.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n      this.selected = true;\n      this.loaded = true;\n      this.accordion.onOpen.emit({\n        originalEvent: event,\n        index: index\n      });\n    }\n    this.selectedChange.emit(this.selected);\n    this.accordion.updateActiveIndex();\n    this.changeDetector.markForCheck();\n    event.preventDefault();\n  }\n  findTabIndex() {\n    let index = -1;\n    for (var i = 0; i < this.accordion.tabs.length; i++) {\n      if (this.accordion.tabs[i] == this) {\n        index = i;\n        break;\n      }\n    }\n    return index;\n  }\n  get hasHeaderFacet() {\n    return this.headerFacet && this.headerFacet.length > 0;\n  }\n  onKeydown(event) {\n    if (event.which === 32 || event.which === 13) {\n      this.toggle(event);\n      event.preventDefault();\n    }\n  }\n  ngOnDestroy() {\n    this.accordion.tabs.splice(this.findTabIndex(), 1);\n  }\n  static ɵfac = function AccordionTab_Factory(t) {\n    return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AccordionTab,\n    selectors: [[\"p-accordionTab\"]],\n    contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      header: \"header\",\n      headerStyle: \"headerStyle\",\n      tabStyle: \"tabStyle\",\n      contentStyle: \"contentStyle\",\n      tabStyleClass: \"tabStyleClass\",\n      headerStyleClass: \"headerStyleClass\",\n      contentStyleClass: \"contentStyleClass\",\n      disabled: \"disabled\",\n      cache: \"cache\",\n      transitionOptions: \"transitionOptions\",\n      iconPos: \"iconPos\",\n      selected: \"selected\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\"\n    },\n    ngContentSelectors: _c5,\n    decls: 12,\n    vars: 38,\n    consts: [[1, \"p-accordion-tab\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-accordion-header\"], [\"role\", \"tab\", 1, \"p-accordion-header-link\", 3, \"ngClass\", \"click\", \"keydown\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\", 3, \"ngClass\", \"ngStyle\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-accordion-header-text\"]],\n    template: function AccordionTab_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n        i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n          return ctx.toggle($event);\n        })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n          return ctx.onKeydown($event);\n        });\n        i0.ɵɵtemplate(3, AccordionTab_ng_container_3_Template, 3, 2, \"ng-container\", 3);\n        i0.ɵɵtemplate(4, AccordionTab_4_Template, 1, 0, null, 4);\n        i0.ɵɵtemplate(5, AccordionTab_span_5_Template, 2, 1, \"span\", 5);\n        i0.ɵɵtemplate(6, AccordionTab_ng_container_6_Template, 1, 0, \"ng-container\", 6);\n        i0.ɵɵtemplate(7, AccordionTab_ng_content_7_Template, 1, 0, \"ng-content\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n        i0.ɵɵprojection(10);\n        i0.ɵɵtemplate(11, AccordionTab_ng_container_11_Template, 2, 1, \"ng-container\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-accordion-tab-active\", ctx.selected);\n        i0.ɵɵproperty(\"ngClass\", ctx.tabStyleClass)(\"ngStyle\", ctx.tabStyle);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"p-highlight\", ctx.selected)(\"p-disabled\", ctx.disabled);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleMap(ctx.headerStyle);\n        i0.ɵɵproperty(\"ngClass\", ctx.headerStyleClass);\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.id)(\"aria-controls\", ctx.id + \"-content\")(\"aria-expanded\", ctx.selected);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.iconTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(28, _c1, ctx.selected));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(32, _c3, i0.ɵɵpureFunction1(30, _c2, ctx.transitionOptions)) : i0.ɵɵpureFunction1(36, _c4, i0.ɵɵpureFunction1(34, _c2, ctx.transitionOptions)));\n        i0.ɵɵattribute(\"id\", ctx.id + \"-content\")(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.id);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.contentStyleClass)(\"ngStyle\", ctx.contentStyle);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n      }\n    },\n    dependencies: function () {\n      return [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, ChevronRightIcon, ChevronDownIcon];\n    },\n    styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('tabContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n    },\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionTab, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordionTab',\n      template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\">\n            <div class=\"p-accordion-header\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"tab\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\"\n                    [attr.aria-controls]=\"id + '-content'\"\n                    [attr.aria-expanded]=\"selected\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"id + '-content'\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"id\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('tabContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: Accordion,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => Accordion)]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    header: [{\n      type: Input\n    }],\n    headerStyle: [{\n      type: Input\n    }],\n    tabStyle: [{\n      type: Input\n    }],\n    contentStyle: [{\n      type: Input\n    }],\n    tabStyleClass: [{\n      type: Input\n    }],\n    headerStyleClass: [{\n      type: Input\n    }],\n    contentStyleClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    cache: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    iconPos: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    headerFacet: [{\n      type: ContentChildren,\n      args: [Header]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nclass Accordion {\n  el;\n  changeDetector;\n  /**\n   * When enabled, multiple tabs can be activated at the same time.\n   * @group Props\n   */\n  multiple = false;\n  /**\n   * Inline style of the tab header and content.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Icon of a collapsed tab.\n   * @group Props\n   */\n  expandIcon;\n  /**\n   * Icon of an expanded tab.\n   * @group Props\n   */\n  collapseIcon;\n  /**\n   * Index of the active tab or an array of indexes in multiple mode.\n   * @group Props\n   */\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(val) {\n    this._activeIndex = val;\n    if (this.preventActiveIndexPropagation) {\n      this.preventActiveIndexPropagation = false;\n      return;\n    }\n    this.updateSelectionState();\n  }\n  /**\n   * Callback to invoke when an active tab is collapsed by clicking on the header.\n   * @param {AccordionTabCloseEvent} event - Custom tab close event.\n   * @group Emits\n   */\n  onClose = new EventEmitter();\n  /**\n   * Callback to invoke when a tab gets expanded.\n   * @param {AccordionTabOpenEvent} event - Custom tab open event.\n   * @group Emits\n   */\n  onOpen = new EventEmitter();\n  /**\n   * Returns the active index.\n   * @param {number | number[]} value - New index.\n   * @group Emits\n   */\n  activeIndexChange = new EventEmitter();\n  tabList;\n  tabListSubscription = null;\n  _activeIndex;\n  preventActiveIndexPropagation = false;\n  tabs = [];\n  constructor(el, changeDetector) {\n    this.el = el;\n    this.changeDetector = changeDetector;\n  }\n  ngAfterContentInit() {\n    this.initTabs();\n    this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n      this.initTabs();\n    });\n  }\n  initTabs() {\n    this.tabs = this.tabList.toArray();\n    this.updateSelectionState();\n    this.changeDetector.markForCheck();\n  }\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n  updateSelectionState() {\n    if (this.tabs && this.tabs.length && this._activeIndex != null) {\n      for (let i = 0; i < this.tabs.length; i++) {\n        let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n        let changed = selected !== this.tabs[i].selected;\n        if (changed) {\n          this.tabs[i].selected = selected;\n          this.tabs[i].selectedChange.emit(selected);\n          this.tabs[i].changeDetector.markForCheck();\n        }\n      }\n    }\n  }\n  updateActiveIndex() {\n    let index = this.multiple ? [] : null;\n    this.tabs.forEach((tab, i) => {\n      if (tab.selected) {\n        if (this.multiple) {\n          index.push(i);\n        } else {\n          index = i;\n          return;\n        }\n      }\n    });\n    this.preventActiveIndexPropagation = true;\n    this.activeIndexChange.emit(index);\n  }\n  ngOnDestroy() {\n    if (this.tabListSubscription) {\n      this.tabListSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function Accordion_Factory(t) {\n    return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Accordion,\n    selectors: [[\"p-accordion\"]],\n    contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, AccordionTab, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      multiple: \"multiple\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      expandIcon: \"expandIcon\",\n      collapseIcon: \"collapseIcon\",\n      activeIndex: \"activeIndex\"\n    },\n    outputs: {\n      onClose: \"onClose\",\n      onOpen: \"onOpen\",\n      activeIndexChange: \"activeIndexChange\"\n    },\n    ngContentSelectors: _c6,\n    decls: 2,\n    vars: 4,\n    consts: [[\"role\", \"tablist\", 3, \"ngClass\", \"ngStyle\"]],\n    template: function Accordion_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Accordion, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordion',\n      template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    multiple: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    expandIcon: [{\n      type: Input\n    }],\n    collapseIcon: [{\n      type: Input\n    }],\n    activeIndex: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    onOpen: [{\n      type: Output\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    tabList: [{\n      type: ContentChildren,\n      args: [AccordionTab]\n    }]\n  });\n})();\nclass AccordionModule {\n  static ɵfac = function AccordionModule_Factory(t) {\n    return new (t || AccordionModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AccordionModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n      exports: [Accordion, AccordionTab, SharedModule],\n      declarations: [Accordion, AccordionTab]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };", "map": {"version": 3, "names": ["trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "i0", "EventEmitter", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "NgModule", "Header", "PrimeTemplate", "SharedModule", "ChevronDownIcon", "ChevronRightIcon", "AccordionTab_ng_container_3_ng_container_1_span_1_Template", "rf", "ctx", "ɵɵelement", "ctx_r8", "ɵɵnextContext", "ɵɵclassMap", "accordion", "collapseIcon", "ɵɵproperty", "iconClass", "AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template", "ctx_r9", "AccordionTab_ng_container_3_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r6", "ɵɵadvance", "AccordionTab_ng_container_3_ng_container_2_span_1_Template", "ctx_r10", "expandIcon", "AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template", "ctx_r11", "AccordionTab_ng_container_3_ng_container_2_Template", "ctx_r7", "AccordionTab_ng_container_3_Template", "ctx_r0", "selected", "AccordionTab_4_ng_template_0_Template", "AccordionTab_4_Template", "AccordionTab_span_5_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r2", "ɵɵtextInterpolate1", "header", "AccordionTab_ng_container_6_Template", "ɵɵelementContainer", "AccordionTab_ng_content_7_Template", "ɵɵprojection", "AccordionTab_ng_container_11_ng_container_1_Template", "AccordionTab_ng_container_11_Template", "ctx_r5", "contentTemplate", "_c0", "_c1", "a0", "$implicit", "_c2", "transitionParams", "_c3", "a1", "value", "params", "_c4", "_c5", "_c6", "idx", "AccordionTab", "changeDetector", "headerStyle", "tabStyle", "contentStyle", "tabStyleClass", "headerStyleClass", "contentStyleClass", "disabled", "cache", "transitionOptions", "iconPos", "<PERSON><PERSON><PERSON><PERSON>", "headerFacet", "templates", "_selected", "val", "loaded", "detectChanges", "headerTemplate", "iconTemplate", "id", "constructor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "toggle", "event", "index", "findTabIndex", "onClose", "emit", "originalEvent", "multiple", "i", "tabs", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOpen", "updateActiveIndex", "preventDefault", "hasHeaderFacet", "onKeydown", "which", "ngOnDestroy", "splice", "ɵfac", "AccordionTab_Factory", "t", "ɵɵdirectiveInject", "Accordion", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "AccordionTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "outputs", "ngContentSelectors", "decls", "vars", "consts", "AccordionTab_Template", "ɵɵprojectionDef", "ɵɵlistener", "AccordionTab_Template_a_click_2_listener", "$event", "AccordionTab_Template_a_keydown_2_listener", "ɵɵclassProp", "ɵɵstyleMap", "ɵɵattribute", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "data", "animation", "height", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "host", "class", "decorators", "el", "styleClass", "activeIndex", "_activeIndex", "preventActiveIndexPropagation", "updateSelectionState", "activeIndexChange", "tabList", "tabListSubscription", "initTabs", "changes", "subscribe", "_", "toArray", "getBlockableElement", "nativeElement", "children", "includes", "changed", "tab", "push", "unsubscribe", "Accordion_Factory", "ElementRef", "Accordion_ContentQueries", "Accordion_Template", "AccordionModule", "AccordionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/primeng/fesm2022/primeng-accordion.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\n\nlet idx = 0;\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nclass AccordionTab {\n    changeDetector;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    header;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    headerStyle;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    tabStyle;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    tabStyleClass;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    headerStyleClass;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'start';\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    selectedChange = new EventEmitter();\n    headerFacet;\n    templates;\n    _selected = false;\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(val) {\n        this._selected = val;\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n            this.changeDetector.detectChanges();\n        }\n    }\n    get iconClass() {\n        if (this.iconPos === 'end') {\n            return 'p-accordion-toggle-icon-end';\n        }\n        else {\n            return 'p-accordion-toggle-icon';\n        }\n    }\n    contentTemplate;\n    headerTemplate;\n    iconTemplate;\n    id = `p-accordiontab-${idx++}`;\n    loaded = false;\n    accordion;\n    constructor(accordion, changeDetector) {\n        this.changeDetector = changeDetector;\n        this.accordion = accordion;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.disabled) {\n            return false;\n        }\n        let index = this.findTabIndex();\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        }\n        else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n        event.preventDefault();\n    }\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n    get hasHeaderFacet() {\n        return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n        if (event.which === 32 || event.which === 13) {\n            this.toggle(event);\n            event.preventDefault();\n        }\n    }\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionTab, deps: [{ token: forwardRef(() => Accordion) }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.2\", type: AccordionTab, selector: \"p-accordionTab\", inputs: { header: \"header\", headerStyle: \"headerStyle\", tabStyle: \"tabStyle\", contentStyle: \"contentStyle\", tabStyleClass: \"tabStyleClass\", headerStyleClass: \"headerStyleClass\", contentStyleClass: \"contentStyleClass\", disabled: \"disabled\", cache: \"cache\", transitionOptions: \"transitionOptions\", iconPos: \"iconPos\", selected: \"selected\" }, outputs: { selectedChange: \"selectedChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", predicate: Header }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\">\n            <div class=\"p-accordion-header\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"tab\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\"\n                    [attr.aria-controls]=\"id + '-content'\"\n                    [attr.aria-expanded]=\"selected\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"id + '-content'\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"id\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i1.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgTemplateOutlet; }), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i1.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(function () { return ChevronRightIcon; }), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(function () { return ChevronDownIcon; }), selector: \"ChevronDownIcon\" }], animations: [\n            trigger('tabContent', [\n                state('hidden', style({\n                    height: '0'\n                })),\n                state('visible', style({\n                    height: '*'\n                })),\n                transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                transition('void => *', animate(0))\n            ])\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-accordionTab', template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\">\n            <div class=\"p-accordion-header\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [style]=\"headerStyle\"\n                    role=\"tab\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\"\n                    [attr.aria-controls]=\"id + '-content'\"\n                    [attr.aria-expanded]=\"selected\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"id + '-content'\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"id\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('tabContent', [\n                            state('hidden', style({\n                                height: '0'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: Accordion, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => Accordion)]\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { header: [{\n                type: Input\n            }], headerStyle: [{\n                type: Input\n            }], tabStyle: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], tabStyleClass: [{\n                type: Input\n            }], headerStyleClass: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], cache: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], iconPos: [{\n                type: Input\n            }], selectedChange: [{\n                type: Output\n            }], headerFacet: [{\n                type: ContentChildren,\n                args: [Header]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], selected: [{\n                type: Input\n            }] } });\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nclass Accordion {\n    el;\n    changeDetector;\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    expandIcon;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    collapseIcon;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(val) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n        this.updateSelectionState();\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    onOpen = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    tabList;\n    tabListSubscription = null;\n    _activeIndex;\n    preventActiveIndexPropagation = false;\n    tabs = [];\n    constructor(el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n    }\n    ngAfterContentInit() {\n        this.initTabs();\n        this.tabListSubscription = this.tabList.changes.subscribe((_) => {\n            this.initTabs();\n        });\n    }\n    initTabs() {\n        this.tabs = this.tabList.toArray();\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                let changed = selected !== this.tabs[i].selected;\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n    updateActiveIndex() {\n        let index = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    index.push(i);\n                }\n                else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: Accordion, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.0.2\", type: Accordion, selector: \"p-accordion\", inputs: { multiple: \"multiple\", style: \"style\", styleClass: \"styleClass\", expandIcon: \"expandIcon\", collapseIcon: \"collapseIcon\", activeIndex: \"activeIndex\" }, outputs: { onClose: \"onClose\", onOpen: \"onOpen\", activeIndexChange: \"activeIndexChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"tabList\", predicate: AccordionTab }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: Accordion, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordion',\n                    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { multiple: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], expandIcon: [{\n                type: Input\n            }], collapseIcon: [{\n                type: Input\n            }], activeIndex: [{\n                type: Input\n            }], onClose: [{\n                type: Output\n            }], onOpen: [{\n                type: Output\n            }], activeIndexChange: [{\n                type: Output\n            }], tabList: [{\n                type: ContentChildren,\n                args: [AccordionTab]\n            }] } });\nclass AccordionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionModule, declarations: [Accordion, AccordionTab], imports: [CommonModule, ChevronRightIcon, ChevronDownIcon], exports: [Accordion, AccordionTab, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionModule, imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.0.2\", ngImport: i0, type: AccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n                    exports: [Accordion, AccordionTab, SharedModule],\n                    declarations: [Accordion, AccordionTab]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACjK,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACjE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAAC,SAAAC,2DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+K+BjB,EAAE,CAAAmB,SAAA,cAkB+B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAlBlCpB,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAsB,UAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAC,YAkBC,CAAC;IAlBJxB,EAAE,CAAAyB,UAAA,YAAAL,MAAA,CAAAM,SAkBuB,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlB1BjB,EAAE,CAAAmB,SAAA,yBAmBK,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAW,MAAA,GAnBR5B,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAyB,UAAA,YAAAG,MAAA,CAAAF,SAmBE,CAAC;EAAA;AAAA;AAAA,SAAAG,oDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBLjB,EAAE,CAAA8B,uBAAA,EAiBzC,CAAC;IAjBsC9B,EAAE,CAAA+B,UAAA,IAAAf,0DAAA,iBAkB+B,CAAC;IAlBlChB,EAAE,CAAA+B,UAAA,IAAAJ,qEAAA,6BAmBK,CAAC;IAnBR3B,EAAE,CAAAgC,qBAAA,CAoBzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,MAAA,GApBsDjC,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAkC,SAAA,EAkBjC,CAAC;IAlB8BlC,EAAE,CAAAyB,UAAA,SAAAQ,MAAA,CAAAV,SAAA,CAAAC,YAkBjC,CAAC;IAlB8BxB,EAAE,CAAAkC,SAAA,EAmBrB,CAAC;IAnBkBlC,EAAE,CAAAyB,UAAA,UAAAQ,MAAA,CAAAV,SAAA,CAAAC,YAmBrB,CAAC;EAAA;AAAA;AAAA,SAAAW,2DAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBkBjB,EAAE,CAAAmB,SAAA,cAsB2B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAmB,OAAA,GAtB9BpC,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAsB,UAAA,CAAAc,OAAA,CAAAb,SAAA,CAAAc,UAsBH,CAAC;IAtBArC,EAAE,CAAAyB,UAAA,YAAAW,OAAA,CAAAV,SAsBmB,CAAC;EAAA;AAAA;AAAA,SAAAY,uEAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtBtBjB,EAAE,CAAAmB,SAAA,0BAuBI,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAsB,OAAA,GAvBPvC,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAyB,UAAA,YAAAc,OAAA,CAAAb,SAuBC,CAAC;EAAA;AAAA;AAAA,SAAAc,oDAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBJjB,EAAE,CAAA8B,uBAAA,EAqBxC,CAAC;IArBqC9B,EAAE,CAAA+B,UAAA,IAAAI,0DAAA,iBAsB2B,CAAC;IAtB9BnC,EAAE,CAAA+B,UAAA,IAAAO,sEAAA,8BAuBI,CAAC;IAvBPtC,EAAE,CAAAgC,qBAAA,CAwBzD,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAwB,MAAA,GAxBsDzC,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAkC,SAAA,EAsBnC,CAAC;IAtBgClC,EAAE,CAAAyB,UAAA,SAAAgB,MAAA,CAAAlB,SAAA,CAAAc,UAsBnC,CAAC;IAtBgCrC,EAAE,CAAAkC,SAAA,EAuBtB,CAAC;IAvBmBlC,EAAE,CAAAyB,UAAA,UAAAgB,MAAA,CAAAlB,SAAA,CAAAc,UAuBtB,CAAC;EAAA;AAAA;AAAA,SAAAK,qCAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBmBjB,EAAE,CAAA8B,uBAAA,EAgBxC,CAAC;IAhBqC9B,EAAE,CAAA+B,UAAA,IAAAF,mDAAA,yBAoBzD,CAAC;IApBsD7B,EAAE,CAAA+B,UAAA,IAAAS,mDAAA,yBAwBzD,CAAC;IAxBsDxC,EAAE,CAAAgC,qBAAA,CAyB7D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA0B,MAAA,GAzB0D3C,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAkC,SAAA,EAiB3C,CAAC;IAjBwClC,EAAE,CAAAyB,UAAA,SAAAkB,MAAA,CAAAC,QAiB3C,CAAC;IAjBwC5C,EAAE,CAAAkC,SAAA,EAqB1C,CAAC;IArBuClC,EAAE,CAAAyB,UAAA,UAAAkB,MAAA,CAAAC,QAqB1C,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAA5B,EAAA,EAAAC,GAAA;AAAA,SAAA4B,wBAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArBuCjB,EAAE,CAAA+B,UAAA,IAAAc,qCAAA,qBA0BkB,CAAC;EAAA;AAAA;AAAA,SAAAE,6BAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1BrBjB,EAAE,CAAAgD,cAAA,cA2Bd,CAAC;IA3BWhD,EAAE,CAAAiD,MAAA,EA6B5E,CAAC;IA7ByEjD,EAAE,CAAAkD,YAAA,CA6BrE,CAAC;EAAA;EAAA,IAAAjC,EAAA;IAAA,MAAAkC,MAAA,GA7BkEnD,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAkC,SAAA,EA6B5E,CAAC;IA7ByElC,EAAE,CAAAoD,kBAAA,MAAAD,MAAA,CAAAE,MAAA,KA6B5E,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7ByEjB,EAAE,CAAAuD,kBAAA,EA8BZ,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAvC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9BSjB,EAAE,CAAAyD,YAAA,kCA+BV,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/BOjB,EAAE,CAAAuD,kBAAA,EA6CP,CAAC;EAAA;AAAA;AAAA,SAAAI,sCAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CIjB,EAAE,CAAA8B,uBAAA,EA4CP,CAAC;IA5CI9B,EAAE,CAAA+B,UAAA,IAAA2B,oDAAA,yBA6CP,CAAC;IA7CI1D,EAAE,CAAAgC,qBAAA,CA8C7D,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA2C,MAAA,GA9C0D5D,EAAE,CAAAqB,aAAA;IAAFrB,EAAE,CAAAkC,SAAA,EA6CxB,CAAC;IA7CqBlC,EAAE,CAAAyB,UAAA,qBAAAmC,MAAA,CAAAC,eA6CxB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,SAAA,EAAAD;EAAA;AAAA;AAAA,MAAAE,GAAA,YAAAA,CAAAF,EAAA;EAAA;IAAAG,gBAAA,EAAAH;EAAA;AAAA;AAAA,MAAAI,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,KAAA;IAAAC,MAAA,EAAAF;EAAA;AAAA;AAAA,MAAAG,GAAA,YAAAA,CAAAH,EAAA;EAAA;IAAAC,KAAA;IAAAC,MAAA,EAAAF;EAAA;AAAA;AAAA,MAAAI,GAAA;AAAA,MAAAC,GAAA;AA1NxE,IAAIC,GAAG,GAAG,CAAC;AACX;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,cAAc;EACd;AACJ;AACA;AACA;EACIxB,MAAM;EACN;AACJ;AACA;AACA;EACIyB,WAAW;EACX;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,YAAY;EACZ;AACJ;AACA;AACA;EACIC,aAAa;EACb;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,KAAK,GAAG,IAAI;EACZ;AACJ;AACA;AACA;EACIC,iBAAiB,GAAG,sCAAsC;EAC1D;AACJ;AACA;AACA;EACIC,OAAO,GAAG,OAAO;EACjB;AACJ;AACA;AACA;AACA;EACIC,cAAc,GAAG,IAAIvF,YAAY,CAAC,CAAC;EACnCwF,WAAW;EACXC,SAAS;EACTC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACI,IAAI/C,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC+C,SAAS;EACzB;EACA,IAAI/C,QAAQA,CAACgD,GAAG,EAAE;IACd,IAAI,CAACD,SAAS,GAAGC,GAAG;IACpB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd,IAAI,IAAI,CAACF,SAAS,IAAI,IAAI,CAACN,KAAK,EAAE;QAC9B,IAAI,CAACQ,MAAM,GAAG,IAAI;MACtB;MACA,IAAI,CAAChB,cAAc,CAACiB,aAAa,CAAC,CAAC;IACvC;EACJ;EACA,IAAIpE,SAASA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC6D,OAAO,KAAK,KAAK,EAAE;MACxB,OAAO,6BAA6B;IACxC,CAAC,MACI;MACD,OAAO,yBAAyB;IACpC;EACJ;EACA1B,eAAe;EACfkC,cAAc;EACdC,YAAY;EACZC,EAAE,GAAI,kBAAiBtB,GAAG,EAAG,EAAC;EAC9BkB,MAAM,GAAG,KAAK;EACdtE,SAAS;EACT2E,WAAWA,CAAC3E,SAAS,EAAEsD,cAAc,EAAE;IACnC,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACtD,SAAS,GAAGA,SAAS;EAC9B;EACA4E,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACT,SAAS,CAACU,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAACzC,eAAe,GAAGwC,IAAI,CAACE,QAAQ;UACpC;QACJ,KAAK,QAAQ;UACT,IAAI,CAACR,cAAc,GAAGM,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,MAAM;UACP,IAAI,CAACP,YAAY,GAAGK,IAAI,CAACE,QAAQ;UACjC;QACJ;UACI,IAAI,CAAC1C,eAAe,GAAGwC,IAAI,CAACE,QAAQ;UACpC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,MAAMA,CAACC,KAAK,EAAE;IACV,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAIsB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,IAAI,CAAC/D,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACrB,SAAS,CAACqF,OAAO,CAACC,IAAI,CAAC;QAAEC,aAAa,EAAEL,KAAK;QAAEC,KAAK,EAAEA;MAAM,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAACnF,SAAS,CAACwF,QAAQ,EAAE;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzF,SAAS,CAAC0F,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UACjD,IAAI,IAAI,CAACzF,SAAS,CAAC0F,IAAI,CAACD,CAAC,CAAC,CAACpE,QAAQ,EAAE;YACjC,IAAI,CAACrB,SAAS,CAAC0F,IAAI,CAACD,CAAC,CAAC,CAACpE,QAAQ,GAAG,KAAK;YACvC,IAAI,CAACrB,SAAS,CAAC0F,IAAI,CAACD,CAAC,CAAC,CAACxB,cAAc,CAACqB,IAAI,CAAC,KAAK,CAAC;YACjD,IAAI,CAACtF,SAAS,CAAC0F,IAAI,CAACD,CAAC,CAAC,CAACnC,cAAc,CAACsC,YAAY,CAAC,CAAC;UACxD;QACJ;MACJ;MACA,IAAI,CAACvE,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACiD,MAAM,GAAG,IAAI;MAClB,IAAI,CAACtE,SAAS,CAAC6F,MAAM,CAACP,IAAI,CAAC;QAAEC,aAAa,EAAEL,KAAK;QAAEC,KAAK,EAAEA;MAAM,CAAC,CAAC;IACtE;IACA,IAAI,CAAClB,cAAc,CAACqB,IAAI,CAAC,IAAI,CAACjE,QAAQ,CAAC;IACvC,IAAI,CAACrB,SAAS,CAAC8F,iBAAiB,CAAC,CAAC;IAClC,IAAI,CAACxC,cAAc,CAACsC,YAAY,CAAC,CAAC;IAClCV,KAAK,CAACa,cAAc,CAAC,CAAC;EAC1B;EACAX,YAAYA,CAAA,EAAG;IACX,IAAID,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzF,SAAS,CAAC0F,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,IAAI,CAACzF,SAAS,CAAC0F,IAAI,CAACD,CAAC,CAAC,IAAI,IAAI,EAAE;QAChCN,KAAK,GAAGM,CAAC;QACT;MACJ;IACJ;IACA,OAAON,KAAK;EAChB;EACA,IAAIa,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC9B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACyB,MAAM,GAAG,CAAC;EAC1D;EACAM,SAASA,CAACf,KAAK,EAAE;IACb,IAAIA,KAAK,CAACgB,KAAK,KAAK,EAAE,IAAIhB,KAAK,CAACgB,KAAK,KAAK,EAAE,EAAE;MAC1C,IAAI,CAACjB,MAAM,CAACC,KAAK,CAAC;MAClBA,KAAK,CAACa,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnG,SAAS,CAAC0F,IAAI,CAACU,MAAM,CAAC,IAAI,CAAChB,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;EACtD;EACA,OAAOiB,IAAI,YAAAC,qBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFlD,YAAY,EAAtB5E,EAAE,CAAA+H,iBAAA,CAAsC7H,UAAU,CAAC,MAAM8H,SAAS,CAAC,GAAnEhI,EAAE,CAAA+H,iBAAA,CAA8E/H,EAAE,CAACiI,iBAAiB;EAAA;EAC7L,OAAOC,IAAI,kBAD8ElI,EAAE,CAAAmI,iBAAA;IAAAC,IAAA,EACJxD,YAAY;IAAAyD,SAAA;IAAAC,cAAA,WAAAC,4BAAAtH,EAAA,EAAAC,GAAA,EAAAsH,QAAA;MAAA,IAAAvH,EAAA;QADVjB,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EACogB7H,MAAM;QAD5gBX,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EACsjB5H,aAAa;MAAA;MAAA,IAAAK,EAAA;QAAA,IAAAyH,EAAA;QADrkB1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA1H,GAAA,CAAAuE,WAAA,GAAAiD,EAAA;QAAF1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA1H,GAAA,CAAAwE,SAAA,GAAAgD,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAC,MAAA;MAAAzF,MAAA;MAAAyB,WAAA;MAAAC,QAAA;MAAAC,YAAA;MAAAC,aAAA;MAAAC,gBAAA;MAAAC,iBAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,iBAAA;MAAAC,OAAA;MAAA3C,QAAA;IAAA;IAAAmG,OAAA;MAAAvD,cAAA;IAAA;IAAAwD,kBAAA,EAAAvE,GAAA;IAAAwE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA5C,QAAA,WAAA6C,sBAAAnI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjB,EAAE,CAAAqJ,eAAA,CAAAvF,GAAA;QAAF9D,EAAE,CAAAgD,cAAA,YAE8B,CAAC,YAAD,CAAC,UAAD,CAAC;QAFjChD,EAAE,CAAAsJ,UAAA,mBAAAC,yCAAAC,MAAA;UAAA,OASlEtI,GAAA,CAAAsF,MAAA,CAAAgD,MAAa,CAAC;QAAA,EAAC,qBAAAC,2CAAAD,MAAA;UAAA,OACbtI,GAAA,CAAAsG,SAAA,CAAAgC,MAAgB,CAAC;QAAA,CADL,CAAC;QATiDxJ,EAAE,CAAA+B,UAAA,IAAAW,oCAAA,yBAyB7D,CAAC;QAzB0D1C,EAAE,CAAA+B,UAAA,IAAAe,uBAAA,eA0BkB,CAAC;QA1BrB9C,EAAE,CAAA+B,UAAA,IAAAgB,4BAAA,iBA6BrE,CAAC;QA7BkE/C,EAAE,CAAA+B,UAAA,IAAAuB,oCAAA,yBA8BZ,CAAC;QA9BStD,EAAE,CAAA+B,UAAA,IAAAyB,kCAAA,uBA+BV,CAAC;QA/BOxD,EAAE,CAAAkD,YAAA,CAgC5E,CAAC,CAAD,CAAC;QAhCyElD,EAAE,CAAAgD,cAAA,YAyCnF,CAAC,YAAD,CAAC;QAzCgFhD,EAAE,CAAAyD,YAAA,GA2CnD,CAAC;QA3CgDzD,EAAE,CAAA+B,UAAA,KAAA4B,qCAAA,yBA8C7D,CAAC;QA9C0D3D,EAAE,CAAAkD,YAAA,CA+C1E,CAAC,CAAD,CAAC,CAAD,CAAC;MAAA;MAAA,IAAAjC,EAAA;QA/CuEjB,EAAE,CAAA0J,WAAA,2BAAAxI,GAAA,CAAA0B,QAElB,CAAC;QAFe5C,EAAE,CAAAyB,UAAA,YAAAP,GAAA,CAAA+D,aAEQ,CAAC,YAAA/D,GAAA,CAAA6D,QAAD,CAAC;QAFX/E,EAAE,CAAAkC,SAAA,EAGtB,CAAC;QAHmBlC,EAAE,CAAA0J,WAAA,gBAAAxI,GAAA,CAAA0B,QAGtB,CAAC,eAAA1B,GAAA,CAAAkE,QAAD,CAAC;QAHmBpF,EAAE,CAAAkC,SAAA,EAMvD,CAAC;QANoDlC,EAAE,CAAA2J,UAAA,CAAAzI,GAAA,CAAA4D,WAMvD,CAAC;QANoD9E,EAAE,CAAAyB,UAAA,YAAAP,GAAA,CAAAgE,gBAKhD,CAAC;QAL6ClF,EAAE,CAAA4J,WAAA,aAAA1I,GAAA,CAAAkE,QAAA,WAWvC,CAAC,OAAAlE,GAAA,CAAA+E,EAAD,CAAC,kBAAA/E,GAAA,CAAA+E,EAAA,aAAD,CAAC,kBAAA/E,GAAA,CAAA0B,QAAD,CAAC;QAXoC5C,EAAE,CAAAkC,SAAA,EAgB1C,CAAC;QAhBuClC,EAAE,CAAAyB,UAAA,UAAAP,GAAA,CAAA8E,YAgB1C,CAAC;QAhBuChG,EAAE,CAAAkC,SAAA,EA0B9B,CAAC;QA1B2BlC,EAAE,CAAAyB,UAAA,qBAAAP,GAAA,CAAA8E,YA0B9B,CAAC,4BA1B2BhG,EAAE,CAAA6J,eAAA,KAAA9F,GAAA,EAAA7C,GAAA,CAAA0B,QAAA,CA0B9B,CAAC;QA1B2B5C,EAAE,CAAAkC,SAAA,EA2BhB,CAAC;QA3BalC,EAAE,CAAAyB,UAAA,UAAAP,GAAA,CAAAqG,cA2BhB,CAAC;QA3BavH,EAAE,CAAAkC,SAAA,EA8B7B,CAAC;QA9B0BlC,EAAE,CAAAyB,UAAA,qBAAAP,GAAA,CAAA6E,cA8B7B,CAAC;QA9B0B/F,EAAE,CAAAkC,SAAA,EA+BzB,CAAC;QA/BsBlC,EAAE,CAAAyB,UAAA,SAAAP,GAAA,CAAAqG,cA+BzB,CAAC;QA/BsBvH,EAAE,CAAAkC,SAAA,EAqCuF,CAAC;QArC1FlC,EAAE,CAAAyB,UAAA,gBAAAP,GAAA,CAAA0B,QAAA,GAAF5C,EAAE,CAAA6J,eAAA,KAAAzF,GAAA,EAAFpE,EAAE,CAAA6J,eAAA,KAAA3F,GAAA,EAAAhD,GAAA,CAAAoE,iBAAA,KAAFtF,EAAE,CAAA6J,eAAA,KAAArF,GAAA,EAAFxE,EAAE,CAAA6J,eAAA,KAAA3F,GAAA,EAAAhD,GAAA,CAAAoE,iBAAA,EAqCuF,CAAC;QArC1FtF,EAAE,CAAA4J,WAAA,OAAA1I,GAAA,CAAA+E,EAAA,aAmCrD,CAAC,iBAAA/E,GAAA,CAAA0B,QAAD,CAAC,oBAAA1B,GAAA,CAAA+E,EAAD,CAAC;QAnCkDjG,EAAE,CAAAkC,SAAA,EA0ClB,CAAC;QA1CelC,EAAE,CAAAyB,UAAA,YAAAP,GAAA,CAAAiE,iBA0ClB,CAAC,YAAAjE,GAAA,CAAA8D,YAAD,CAAC;QA1CehF,EAAE,CAAAkC,SAAA,EA4CT,CAAC;QA5CMlC,EAAE,CAAAyB,UAAA,SAAAP,GAAA,CAAA2C,eAAA,KAAA3C,GAAA,CAAAmE,KAAA,GAAAnE,GAAA,CAAA2E,MAAA,GAAA3E,GAAA,CAAA0B,QAAA,CA4CT,CAAC;MAAA;IAAA;IAAAkH,YAAA,WAAAA,CAAA;MAAA,QAM+ehK,EAAE,CAACiK,OAAO,EAA2HjK,EAAE,CAACkK,IAAI,EAAoIlK,EAAE,CAACmK,gBAAgB,EAA2LnK,EAAE,CAACoK,OAAO,EAAkHnJ,gBAAgB,EAAoGD,eAAe;IAAA;IAAAqJ,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAAkD,CACz0C7K,OAAO,CAAC,YAAY,EAAE,CAClBC,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;QAClB4K,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC,EACH7K,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;QACnB4K,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC,EACH3K,UAAU,CAAC,oBAAoB,EAAE,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,EACnED,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;IACL;IAAA2K,eAAA;EAAA;AACT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/D6FzK,EAAE,CAAA0K,iBAAA,CA+DJ9F,YAAY,EAAc,CAAC;IAC1GwD,IAAI,EAAEjI,SAAS;IACfwK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAgB;MAAErE,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEsE,UAAU,EAAE,CACKpL,OAAO,CAAC,YAAY,EAAE,CAClBC,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;QAClB4K,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC,EACH7K,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;QACnB4K,MAAM,EAAE;MACZ,CAAC,CAAC,CAAC,EACH3K,UAAU,CAAC,oBAAoB,EAAE,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,EACnED,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC,CACL;MAAE2K,eAAe,EAAEpK,uBAAuB,CAAC0K,MAAM;MAAEV,aAAa,EAAE/J,iBAAiB,CAAC0K,IAAI;MAAEC,IAAI,EAAE;QAC7FC,KAAK,EAAE;MACX,CAAC;MAAEd,MAAM,EAAE,CAAC,sdAAsd;IAAE,CAAC;EACjf,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/B,IAAI,EAAEJ,SAAS;MAAEkD,UAAU,EAAE,CAAC;QAC9D9C,IAAI,EAAE9H,MAAM;QACZqK,IAAI,EAAE,CAACzK,UAAU,CAAC,MAAM8H,SAAS,CAAC;MACtC,CAAC;IAAE,CAAC,EAAE;MAAEI,IAAI,EAAEpI,EAAE,CAACiI;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE5E,MAAM,EAAE,CAAC;MACrE+E,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEuE,WAAW,EAAE,CAAC;MACdsD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEwE,QAAQ,EAAE,CAAC;MACXqD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEyE,YAAY,EAAE,CAAC;MACfoD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE0E,aAAa,EAAE,CAAC;MAChBmD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE2E,gBAAgB,EAAE,CAAC;MACnBkD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE4E,iBAAiB,EAAE,CAAC;MACpBiD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE6E,QAAQ,EAAE,CAAC;MACXgD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE8E,KAAK,EAAE,CAAC;MACR+C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE+E,iBAAiB,EAAE,CAAC;MACpB8C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEgF,OAAO,EAAE,CAAC;MACV6C,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiF,cAAc,EAAE,CAAC;MACjB4C,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEiF,WAAW,EAAE,CAAC;MACd2C,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAAChK,MAAM;IACjB,CAAC,CAAC;IAAE+E,SAAS,EAAE,CAAC;MACZ0C,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAAC/J,aAAa;IACxB,CAAC,CAAC;IAAEgC,QAAQ,EAAE,CAAC;MACXwF,IAAI,EAAE7H;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMyH,SAAS,CAAC;EACZmD,EAAE;EACFtG,cAAc;EACd;AACJ;AACA;AACA;EACIkC,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIpH,KAAK;EACL;AACJ;AACA;AACA;EACIyL,UAAU;EACV;AACJ;AACA;AACA;EACI/I,UAAU;EACV;AACJ;AACA;AACA;EACIb,YAAY;EACZ;AACJ;AACA;AACA;EACI,IAAI6J,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACzF,GAAG,EAAE;IACjB,IAAI,CAAC0F,YAAY,GAAG1F,GAAG;IACvB,IAAI,IAAI,CAAC2F,6BAA6B,EAAE;MACpC,IAAI,CAACA,6BAA6B,GAAG,KAAK;MAC1C;IACJ;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI5E,OAAO,GAAG,IAAI3G,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACImH,MAAM,GAAG,IAAInH,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACIwL,iBAAiB,GAAG,IAAIxL,YAAY,CAAC,CAAC;EACtCyL,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BL,YAAY;EACZC,6BAA6B,GAAG,KAAK;EACrCtE,IAAI,GAAG,EAAE;EACTf,WAAWA,CAACiF,EAAE,EAAEtG,cAAc,EAAE;IAC5B,IAAI,CAACsG,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACtG,cAAc,GAAGA,cAAc;EACxC;EACAsB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyF,QAAQ,CAAC,CAAC;IACf,IAAI,CAACD,mBAAmB,GAAG,IAAI,CAACD,OAAO,CAACG,OAAO,CAACC,SAAS,CAAEC,CAAC,IAAK;MAC7D,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAA,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3E,IAAI,GAAG,IAAI,CAACyE,OAAO,CAACM,OAAO,CAAC,CAAC;IAClC,IAAI,CAACR,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC3G,cAAc,CAACsC,YAAY,CAAC,CAAC;EACtC;EACA8E,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACd,EAAE,CAACe,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC5C;EACAX,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,IAAI,IAAI,CAACoE,YAAY,IAAI,IAAI,EAAE;MAC5D,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACvC,IAAIpE,QAAQ,GAAG,IAAI,CAACmE,QAAQ,GAAG,IAAI,CAACuE,YAAY,CAACc,QAAQ,CAACpF,CAAC,CAAC,GAAGA,CAAC,KAAK,IAAI,CAACsE,YAAY;QACtF,IAAIe,OAAO,GAAGzJ,QAAQ,KAAK,IAAI,CAACqE,IAAI,CAACD,CAAC,CAAC,CAACpE,QAAQ;QAChD,IAAIyJ,OAAO,EAAE;UACT,IAAI,CAACpF,IAAI,CAACD,CAAC,CAAC,CAACpE,QAAQ,GAAGA,QAAQ;UAChC,IAAI,CAACqE,IAAI,CAACD,CAAC,CAAC,CAACxB,cAAc,CAACqB,IAAI,CAACjE,QAAQ,CAAC;UAC1C,IAAI,CAACqE,IAAI,CAACD,CAAC,CAAC,CAACnC,cAAc,CAACsC,YAAY,CAAC,CAAC;QAC9C;MACJ;IACJ;EACJ;EACAE,iBAAiBA,CAAA,EAAG;IAChB,IAAIX,KAAK,GAAG,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,IAAI;IACrC,IAAI,CAACE,IAAI,CAACb,OAAO,CAAC,CAACkG,GAAG,EAAEtF,CAAC,KAAK;MAC1B,IAAIsF,GAAG,CAAC1J,QAAQ,EAAE;QACd,IAAI,IAAI,CAACmE,QAAQ,EAAE;UACfL,KAAK,CAAC6F,IAAI,CAACvF,CAAC,CAAC;QACjB,CAAC,MACI;UACDN,KAAK,GAAGM,CAAC;UACT;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACuE,6BAA6B,GAAG,IAAI;IACzC,IAAI,CAACE,iBAAiB,CAAC5E,IAAI,CAACH,KAAK,CAAC;EACtC;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACiE,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACa,WAAW,CAAC,CAAC;IAC1C;EACJ;EACA,OAAO5E,IAAI,YAAA6E,kBAAA3E,CAAA;IAAA,YAAAA,CAAA,IAAwFE,SAAS,EA9RnBhI,EAAE,CAAA+H,iBAAA,CA8RmC/H,EAAE,CAAC0M,UAAU,GA9RlD1M,EAAE,CAAA+H,iBAAA,CA8R6D/H,EAAE,CAACiI,iBAAiB;EAAA;EAC5K,OAAOC,IAAI,kBA/R8ElI,EAAE,CAAAmI,iBAAA;IAAAC,IAAA,EA+RJJ,SAAS;IAAAK,SAAA;IAAAC,cAAA,WAAAqE,yBAAA1L,EAAA,EAAAC,GAAA,EAAAsH,QAAA;MAAA,IAAAvH,EAAA;QA/RPjB,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EA+RkX5D,YAAY;MAAA;MAAA,IAAA3D,EAAA;QAAA,IAAAyH,EAAA;QA/RhY1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAA1H,GAAA,CAAAwK,OAAA,GAAAhD,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAC,MAAA;MAAA/B,QAAA;MAAApH,KAAA;MAAAyL,UAAA;MAAA/I,UAAA;MAAAb,YAAA;MAAA6J,WAAA;IAAA;IAAAtC,OAAA;MAAAnC,OAAA;MAAAQ,MAAA;MAAAqE,iBAAA;IAAA;IAAAzC,kBAAA,EAAAtE,GAAA;IAAAuE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA5C,QAAA,WAAAqG,mBAAA3L,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjB,EAAE,CAAAqJ,eAAA;QAAFrJ,EAAE,CAAAgD,cAAA,YAgSS,CAAC;QAhSZhD,EAAE,CAAAyD,YAAA,EAiS3D,CAAC;QAjSwDzD,EAAE,CAAAkD,YAAA,CAkSlF,CAAC;MAAA;MAAA,IAAAjC,EAAA;QAlS+EjB,EAAE,CAAAsB,UAAA,CAAAJ,GAAA,CAAAkK,UAgSP,CAAC;QAhSIpL,EAAE,CAAAyB,UAAA,qCAgS9C,CAAC,YAAAP,GAAA,CAAAvB,KAAD,CAAC;MAAA;IAAA;IAAAmK,YAAA,GAGehK,EAAE,CAACiK,OAAO,EAAoFjK,EAAE,CAACoK,OAAO;IAAAE,aAAA;IAAAI,eAAA;EAAA;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArS6FzK,EAAE,CAAA0K,iBAAA,CAqSJ1C,SAAS,EAAc,CAAC;IACvGI,IAAI,EAAEjI,SAAS;IACfwK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBrE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KAAK;MACeiE,eAAe,EAAEpK,uBAAuB,CAAC0K,MAAM;MAC/CE,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7C,IAAI,EAAEpI,EAAE,CAAC0M;IAAW,CAAC,EAAE;MAAEtE,IAAI,EAAEpI,EAAE,CAACiI;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAElB,QAAQ,EAAE,CAAC;MAC5HqB,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEZ,KAAK,EAAE,CAAC;MACRyI,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE6K,UAAU,EAAE,CAAC;MACbhD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE8B,UAAU,EAAE,CAAC;MACb+F,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEiB,YAAY,EAAE,CAAC;MACf4G,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAE8K,WAAW,EAAE,CAAC;MACdjD,IAAI,EAAE7H;IACV,CAAC,CAAC;IAAEqG,OAAO,EAAE,CAAC;MACVwB,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAE4G,MAAM,EAAE,CAAC;MACTgB,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEiL,iBAAiB,EAAE,CAAC;MACpBrD,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEkL,OAAO,EAAE,CAAC;MACVtD,IAAI,EAAE3H,eAAe;MACrBkK,IAAI,EAAE,CAAC/F,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiI,eAAe,CAAC;EAClB,OAAOjF,IAAI,YAAAkF,wBAAAhF,CAAA;IAAA,YAAAA,CAAA,IAAwF+E,eAAe;EAAA;EAClH,OAAOE,IAAI,kBA3U8E/M,EAAE,CAAAgN,gBAAA;IAAA5E,IAAA,EA2USyE;EAAe;EACnH,OAAOI,IAAI,kBA5U8EjN,EAAE,CAAAkN,gBAAA;IAAAC,OAAA,GA4UoCpN,YAAY,EAAEgB,gBAAgB,EAAED,eAAe,EAAED,YAAY;EAAA;AAChM;AACA;EAAA,QAAA4J,SAAA,oBAAAA,SAAA,KA9U6FzK,EAAE,CAAA0K,iBAAA,CA8UJmC,eAAe,EAAc,CAAC;IAC7GzE,IAAI,EAAE1H,QAAQ;IACdiK,IAAI,EAAE,CAAC;MACCwC,OAAO,EAAE,CAACpN,YAAY,EAAEgB,gBAAgB,EAAED,eAAe,CAAC;MAC1DsM,OAAO,EAAE,CAACpF,SAAS,EAAEpD,YAAY,EAAE/D,YAAY,CAAC;MAChDwM,YAAY,EAAE,CAACrF,SAAS,EAAEpD,YAAY;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASoD,SAAS,EAAE6E,eAAe,EAAEjI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}