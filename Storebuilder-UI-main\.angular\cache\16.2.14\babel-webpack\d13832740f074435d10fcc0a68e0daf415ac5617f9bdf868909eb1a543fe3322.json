{"ast": null, "code": "import { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"ngx-cookie-service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-google-analytics\";\nimport * as i5 from \"primeng/badge\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ngx-translate/core\";\nfunction MobileNavbarComponent_div_0_div_1_p_badge_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵpropertyInterpolate(\"value\", ctx_r3.cartListCount);\n  }\n}\nfunction MobileNavbarComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function MobileNavbarComponent_div_0_div_1_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const menu_r2 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateFloating(menu_r2));\n    });\n    i0.ɵɵelement(1, \"img\", 4);\n    i0.ɵɵtemplate(2, MobileNavbarComponent_div_0_div_1_p_badge_2_Template, 1, 1, \"p-badge\", 5);\n    i0.ɵɵelementStart(3, \"div\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const menu_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r1.renderMenuFilledIcon(menu_r2.name) ? menu_r2.filledIcon : menu_r2.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", menu_r2.name === \"navbar.Cart\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.renderMenuFilledIcon(menu_r2.name) ? \"mobile-navbar__navs__name-filled\" : \"mobile-navbar__navs__name\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 4, menu_r2.name));\n  }\n}\nfunction MobileNavbarComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, MobileNavbarComponent_div_0_div_1_Template, 6, 6, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.floatingMenu);\n  }\n}\nexport class MobileNavbarComponent {\n  authTokenService;\n  cookieService;\n  router;\n  mainDataService;\n  cd;\n  commonService;\n  $gaService;\n  permissionService;\n  store;\n  authToken = '';\n  floatingMenu;\n  cartListCount = 0;\n  isMobileTemplate = false;\n  isGoogleAnalytics = false;\n  selectedMenu;\n  userDetails;\n  constructor(authTokenService, cookieService, router, mainDataService, cd, commonService, $gaService, permissionService, store) {\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.router = router;\n    this.mainDataService = mainDataService;\n    this.cd = cd;\n    this.commonService = commonService;\n    this.$gaService = $gaService;\n    this.permissionService = permissionService;\n    this.store = store;\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n  }\n  ngOnInit() {\n    this.userDetails = this.store.get('profile');\n    this.mainDataService.getCartLengthData().subscribe(data => {\n      this.cartListCount = data ?? 0;\n      if (this.cartListCount > 0) {\n        if (this.cartListCount > 9) {\n          this.cartListCount = '9+';\n        }\n      }\n      this.cd.markForCheck();\n      this.cd.detectChanges();\n    });\n    this.floatingMenu = [{\n      name: 'navbar.home',\n      icon: 'assets/icons/mobile-home.svg',\n      filledIcon: 'assets/icons/mobile-home-filled.svg'\n    }, {\n      name: 'navbar.Wishlist',\n      icon: 'assets/icons/mobile-wishlist.svg',\n      filledIcon: 'assets/icons/mobile-wishlist-filled.svg'\n    }, {\n      name: 'navbar.Categories',\n      icon: 'assets/icons/mobile-categories.svg',\n      filledIcon: 'assets/icons/mobile-categories-filled.svg'\n    }, {\n      name: 'navbar.Cart',\n      icon: 'assets/icons/mobile-cart.svg',\n      filledIcon: 'assets/icons/mobile-cart-flled.svg'\n    }, {\n      name: 'navbar.Profile',\n      icon: 'assets/icons/mobile-profile.svg',\n      filledIcon: 'assets/icons/mobile-profile-filled.svg'\n    }];\n    this.selectedMenu = this.floatingMenu[0];\n    this.commonService.isShowSearchBox.next(true);\n    this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n    if (!this.authToken) {\n      this.authToken = this.cookieService.get('authToken');\n    }\n  }\n  navigateFloating(menu) {\n    const tempSelectedMenu = this.floatingMenu.find(m => m.name === menu.name) ?? null;\n    if (menu.name === 'navbar.Profile') {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_IN_UP, '', 'SIGNIN/SIGNUP', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails?.mobileNumber : 'Un_Authenticated',\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId\n        });\n      }\n      if (this.authToken) this.router.navigate(['/account']);else this.router.navigate(['/login']);\n    } else if (menu.name === 'navbar.home') {\n      this.router.navigate(['/']);\n    } else if (menu.name === 'navbar.Wishlist') {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_WISHLIST_FOOTER, '', '', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId\n        });\n      }\n      if (this.authToken) this.router.navigate(['/wishlist']);else this.router.navigate(['/login']);\n    } else if (menu.name === 'navbar.Cart') {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_CART_ICON, 'navigation', 'CART_ON_TOP_BANNER', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n          \"session_ID\": localStorage.getItem('sessionId')\n        });\n      }\n      this.router.navigate(['/cart']);\n    } else if (menu.name === 'navbar.Categories') {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true, {\n          \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n          \"ip_Address\": this.store.get('userIP'),\n          \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n          \"device_Id\": this.store.get('deviceInfo')?.deviceId\n        });\n      }\n      this.router.navigate(['/categories-list']);\n    }\n    if (tempSelectedMenu?.name === this.selectedMenu?.name) {\n      this.commonService.isShowSearchBox.next(true);\n    } else {\n      this.commonService.isShowSearchBox.next(false);\n    }\n    this.selectedMenu = tempSelectedMenu;\n  }\n  renderMenuFilledIcon(menu) {\n    const tempSelectedMenu = this.floatingMenu.find(m => m.name === menu) ?? null;\n    if (tempSelectedMenu) {\n      if (tempSelectedMenu.name === 'navbar.Profile') {\n        return this.router.url.includes('login') || this.router.url.includes('account');\n      } else if (tempSelectedMenu.name === 'navbar.home') {\n        return this.router.url === '/';\n      } else if (tempSelectedMenu.name === 'navbar.Wishlist') {\n        return this.router.url.includes('/wishlist');\n      } else if (tempSelectedMenu.name === 'navbar.Cart') {\n        return this.router.url === '/cart';\n      } else if (tempSelectedMenu.name === 'navbar.Categories') {\n        return this.router.url === '/categories-list';\n      }\n    }\n  }\n  static ɵfac = function MobileNavbarComponent_Factory(t) {\n    return new (t || MobileNavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i2.CookieService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i4.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.StoreService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MobileNavbarComponent,\n    selectors: [[\"app-mobile-navbar\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"d-flex mobile-navbar\", 4, \"ngIf\"], [1, \"d-flex\", \"mobile-navbar\"], [\"class\", \"mobile-navbar__navs\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"mobile-navbar__navs\", 3, \"click\"], [\"alt\", \"No Image\", 3, \"src\"], [\"class\", \"mobile-navbar__navs__badge\", 3, \"value\", 4, \"ngIf\"], [1, \"text-capitalize\", 3, \"ngClass\"], [1, \"mobile-navbar__navs__badge\", 3, \"value\"]],\n    template: function MobileNavbarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MobileNavbarComponent_div_0_Template, 2, 1, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate);\n      }\n    },\n    dependencies: [i5.Badge, i6.NgClass, i6.NgForOf, i6.NgIf, i7.TranslatePipe],\n    styles: [\".mobile-navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  width: 100%;\\n  display: flex;\\n  padding: 8px 25px;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  background: #204E6E;\\n  box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.25);\\n}\\n.mobile-navbar__navs[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 8px 0px;\\n}\\n.mobile-navbar__navs[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.mobile-navbar__navs__name[_ngcontent-%COMP%] {\\n  color: #FFF;\\n  font-family: var(--regular-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%; \\n\\n}\\n.mobile-navbar__navs__badge[_ngcontent-%COMP%] {\\n  right: 23%;\\n  top: 1px;\\n  position: absolute;\\n}\\n.mobile-navbar__navs__name-filled[_ngcontent-%COMP%] {\\n  color: #FFCB05;\\n  font-family: var(--regular-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 100%; \\n\\n}\\n\\n  .p-badge {\\n  font-size: 0.75rem;\\n  min-width: 1rem;\\n  height: 1rem;\\n  line-height: 1rem;\\n  background: #ffcc00;\\n  color: #000;\\n  padding: 0 0.2rem !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["GaLocalActionEnum", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r3", "cartListCount", "ɵɵelementStart", "ɵɵlistener", "MobileNavbarComponent_div_0_div_1_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "menu_r2", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "navigateFloating", "ɵɵtemplate", "MobileNavbarComponent_div_0_div_1_p_badge_2_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "renderMenuFilledIcon", "name", "filledIcon", "icon", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵpipeBind1", "MobileNavbarComponent_div_0_div_1_Template", "ctx_r0", "floatingMenu", "MobileNavbarComponent", "authTokenService", "cookieService", "router", "mainDataService", "cd", "commonService", "$gaService", "permissionService", "store", "authToken", "isMobileTemplate", "isGoogleAnalytics", "selected<PERSON><PERSON>u", "userDetails", "constructor", "hasPermission", "ngOnInit", "get", "getCartLengthData", "subscribe", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "isShowSearchBox", "next", "authTokenData", "message", "menu", "tempSelectedMenu", "find", "m", "event", "CLICK_ON_SIGN_IN_UP", "mobileNumber", "deviceType", "deviceId", "navigate", "CLICK_ON_WISHLIST_FOOTER", "CLICK_ON_CART_ICON", "localStorage", "getItem", "CLICK_ON_CATEGORY", "url", "includes", "ɵɵdirectiveInject", "i1", "AuthTokenService", "i2", "CookieService", "i3", "Router", "MainDataService", "ChangeDetectorRef", "CommonService", "i4", "GoogleAnalyticsService", "PermissionService", "StoreService", "selectors", "decls", "vars", "consts", "template", "MobileNavbarComponent_Template", "rf", "ctx", "MobileNavbarComponent_div_0_Template"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\mobile-navbar\\mobile-navbar.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\components\\mobile-navbar\\mobile-navbar.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component} from '@angular/core';\r\nimport {AuthTokenService, CommonService, MainDataService, PermissionService, StoreService} from \"@core/services\";\r\nimport {CookieService} from \"ngx-cookie-service\";\r\nimport {ActivatedRoute, Router} from \"@angular/router\";\r\nimport {GaLocalActionEnum} from \"@core/enums/ga-local-action-enum\";\r\nimport {GoogleAnalyticsService} from \"ngx-google-analytics\";\r\n\r\ninterface Menu {\r\n  name: string,\r\n  icon: string,\r\n  filledIcon: string\r\n}\r\n\r\n@Component({\r\n  selector: 'app-mobile-navbar',\r\n  templateUrl: './mobile-navbar.component.html',\r\n  styleUrls: ['./mobile-navbar.component.scss']\r\n})\r\nexport class MobileNavbarComponent {\r\n  authToken: any = '';\r\n  floatingMenu: Menu[];\r\n  cartListCount: any = 0;\r\n  isMobileTemplate: boolean = false;\r\n  isGoogleAnalytics: boolean = false;\r\n  selectedMenu : Menu | null;\r\n  userDetails: any;\r\n  constructor(private authTokenService: AuthTokenService,\r\n              private cookieService: CookieService,\r\n              protected router: Router,\r\n              private mainDataService: MainDataService,\r\n              private cd: ChangeDetectorRef,\r\n              private commonService : CommonService,\r\n              private $gaService: GoogleAnalyticsService,\r\n              private permissionService: PermissionService ,\r\n              private store: StoreService ) {\r\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.userDetails = this.store.get('profile');\r\n    this.mainDataService.getCartLengthData().subscribe((data: any) => {\r\n\r\n      this.cartListCount = data ?? 0;\r\n      if (this.cartListCount > 0) {\r\n\r\n        if (this.cartListCount > 9) {\r\n          this.cartListCount = '9+';\r\n        }\r\n      }\r\n      this.cd.markForCheck();\r\n      this.cd.detectChanges();\r\n    });\r\n    this.floatingMenu = [\r\n      {\r\n        name: 'navbar.home',\r\n        icon: 'assets/icons/mobile-home.svg',\r\n        filledIcon: 'assets/icons/mobile-home-filled.svg'\r\n      },\r\n      {\r\n        name: 'navbar.Wishlist',\r\n        icon: 'assets/icons/mobile-wishlist.svg',\r\n        filledIcon: 'assets/icons/mobile-wishlist-filled.svg'\r\n      },\r\n      {\r\n        name: 'navbar.Categories',\r\n        icon: 'assets/icons/mobile-categories.svg',\r\n        filledIcon: 'assets/icons/mobile-categories-filled.svg'\r\n      },\r\n      {\r\n        name: 'navbar.Cart',\r\n        icon: 'assets/icons/mobile-cart.svg',\r\n\r\n        filledIcon: 'assets/icons/mobile-cart-flled.svg'\r\n      },\r\n      {\r\n        name: 'navbar.Profile',\r\n        icon: 'assets/icons/mobile-profile.svg',\r\n        filledIcon: 'assets/icons/mobile-profile-filled.svg'\r\n      }\r\n    ]\r\n    this.selectedMenu = this.floatingMenu[0];\r\n    this.commonService.isShowSearchBox.next(true);\r\n    this.authTokenService.authTokenData.subscribe(\r\n      (message) => (this.authToken = message)\r\n    );\r\n    if (!this.authToken) {\r\n      this.authToken = this.cookieService.get('authToken');\r\n    }\r\n  }\r\n\r\n  navigateFloating(menu: Menu) {\r\n\r\n    const tempSelectedMenu: Menu | null = this.floatingMenu.find((m: Menu) => m.name === menu.name) ?? null\r\n\r\n    if (menu.name === 'navbar.Profile') {\r\n      if(this.isGoogleAnalytics){\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_IN_UP, '', 'SIGNIN/SIGNUP', 1, true , {\r\n        \"user_ID\":this.userDetails?this.userDetails?.mobileNumber:'Un_Authenticated',\r\n        \"ip_Address\": this.store.get('userIP'),\r\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n       \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n      });\r\n    }\r\n      if (this.authToken) this.router.navigate(['/account'])\r\n      else this.router.navigate(['/login'])\r\n    } else if(menu.name === 'navbar.home') {\r\n      this.router.navigate(['/'])\r\n    } else if(menu.name === 'navbar.Wishlist') {\r\n      if(this.isGoogleAnalytics){\r\n\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_WISHLIST_FOOTER, '', '', 1, true,{\r\n        \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated',\r\n        \"ip_Address\": this.store.get('userIP'),\r\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n      \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n\r\n      });\r\n    }\r\n      if (this.authToken) this.router.navigate(['/wishlist'])\r\n      else this.router.navigate(['/login'])\r\n    }\r\n    else if(menu.name === 'navbar.Cart') {\r\n      if(this.isGoogleAnalytics){\r\n    this.$gaService.event(GaLocalActionEnum.CLICK_ON_CART_ICON, 'navigation', 'CART_ON_TOP_BANNER', 1, true , {\r\n      \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated',\r\n      \"ip_Address\": this.store.get('userIP'),\r\n      \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n      \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n      \"session_ID\": localStorage.getItem('sessionId')\r\n    });\r\n  }\r\n     this.router.navigate(['/cart'])\r\n    }\r\n    else if(menu.name === 'navbar.Categories') {\r\n      if(this.isGoogleAnalytics){\r\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_CATEGORY, '', 'CATEGORY_PAGE', 1, true , {\r\n        \"user_ID\":this.userDetails?this.userDetails.mobileNumber:'Un_Authenticated',\r\n        \"ip_Address\": this.store.get('userIP'),\r\n        \"device_Type\": this.store.get('deviceInfo')?.deviceType,\r\n      \"device_Id\": this.store.get('deviceInfo')?.deviceId,\r\n      });\r\n    }\r\n      this.router.navigate(['/categories-list'])\r\n    }\r\n    if(tempSelectedMenu?.name === this.selectedMenu?.name){\r\n      this.commonService.isShowSearchBox.next(true);\r\n    }else{\r\n      this.commonService.isShowSearchBox.next(false);\r\n\r\n    }\r\n    this.selectedMenu = tempSelectedMenu;\r\n  }\r\n\r\n  renderMenuFilledIcon(menu: string) {\r\n    const tempSelectedMenu : Menu | null  = this.floatingMenu.find((m: Menu) => m.name === menu) ?? null\r\n    if(tempSelectedMenu) {\r\n      if(tempSelectedMenu.name === 'navbar.Profile') {\r\n        return this.router.url.includes('login') || this.router.url.includes('account');\r\n      } else if(tempSelectedMenu.name === 'navbar.home') {\r\n        return this.router.url === '/';\r\n      }\r\n      else if(tempSelectedMenu.name === 'navbar.Wishlist') {\r\n        return this.router.url.includes('/wishlist');\r\n      }\r\n      else if(tempSelectedMenu.name === 'navbar.Cart') {\r\n        return this.router.url === '/cart';\r\n      }\r\n      else if(tempSelectedMenu.name === 'navbar.Categories') {\r\n        return this.router.url === '/categories-list';\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<div class=\"d-flex mobile-navbar\" *ngIf=\"isMobileTemplate\">\r\n\r\n  <div *ngFor=\"let menu of floatingMenu\" class=\"mobile-navbar__navs\" (click)=\"navigateFloating(menu)\">\r\n    <img alt=\"No Image\" [src]=\"renderMenuFilledIcon(menu.name) ? menu.filledIcon : menu.icon\">\r\n    <p-badge *ngIf=\"menu.name === 'navbar.Cart'\" class=\"mobile-navbar__navs__badge\" value=\"{{ cartListCount }}\">\r\n\r\n    </p-badge>\r\n\r\n    <div class=\"text-capitalize\"\r\n    [ngClass]=\"renderMenuFilledIcon(menu.name) ? 'mobile-navbar__navs__name-filled' : 'mobile-navbar__navs__name'\">{{menu.name | translate}}</div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAIA,SAAQA,iBAAiB,QAAO,kCAAkC;;;;;;;;;;;ICA9DC,EAAA,CAAAC,SAAA,iBAEU;;;;IAFsED,EAAA,CAAAE,qBAAA,UAAAC,MAAA,CAAAC,aAAA,CAA2B;;;;;;IAF7GJ,EAAA,CAAAK,cAAA,aAAoG;IAAjCL,EAAA,CAAAM,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,WAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAL,OAAA,CAAsB;IAAA,EAAC;IACjGX,EAAA,CAAAC,SAAA,aAA0F;IAC1FD,EAAA,CAAAiB,UAAA,IAAAC,oDAAA,qBAEU;IAEVlB,EAAA,CAAAK,cAAA,aAC+G;IAAAL,EAAA,CAAAmB,MAAA,GAAyB;;IAAAnB,EAAA,CAAAoB,YAAA,EAAM;;;;;IAN1HpB,EAAA,CAAAqB,SAAA,GAAqE;IAArErB,EAAA,CAAAsB,UAAA,QAAAC,MAAA,CAAAC,oBAAA,CAAAb,OAAA,CAAAc,IAAA,IAAAd,OAAA,CAAAe,UAAA,GAAAf,OAAA,CAAAgB,IAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAAqE;IAC/E5B,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,UAAA,SAAAX,OAAA,CAAAc,IAAA,mBAAiC;IAK3CzB,EAAA,CAAAqB,SAAA,GAA8G;IAA9GrB,EAAA,CAAAsB,UAAA,YAAAC,MAAA,CAAAC,oBAAA,CAAAb,OAAA,CAAAc,IAAA,qEAA8G;IAACzB,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,OAAAnB,OAAA,CAAAc,IAAA,EAAyB;;;;;IAT5IzB,EAAA,CAAAK,cAAA,aAA2D;IAEzDL,EAAA,CAAAiB,UAAA,IAAAc,0CAAA,iBAQM;IACR/B,EAAA,CAAAoB,YAAA,EAAM;;;;IATkBpB,EAAA,CAAAqB,SAAA,GAAe;IAAfrB,EAAA,CAAAsB,UAAA,YAAAU,MAAA,CAAAC,YAAA,CAAe;;;ADgBvC,OAAM,MAAOC,qBAAqB;EAQZC,gBAAA;EACAC,aAAA;EACEC,MAAA;EACFC,eAAA;EACAC,EAAA;EACAC,aAAA;EACAC,UAAA;EACAC,iBAAA;EACAC,KAAA;EAfpBC,SAAS,GAAQ,EAAE;EACnBX,YAAY;EACZ7B,aAAa,GAAQ,CAAC;EACtByC,gBAAgB,GAAY,KAAK;EACjCC,iBAAiB,GAAY,KAAK;EAClCC,YAAY;EACZC,WAAW;EACXC,YAAoBd,gBAAkC,EAClCC,aAA4B,EAC1BC,MAAc,EAChBC,eAAgC,EAChCC,EAAqB,EACrBC,aAA6B,EAC7BC,UAAkC,EAClCC,iBAAoC,EACpCC,KAAmB;IARnB,KAAAR,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACX,KAAAC,MAAM,GAANA,MAAM;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IACvB,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACH,iBAAiB,CAACQ,aAAa,CAAC,eAAe,CAAC;IAC7E,IAAI,CAACJ,iBAAiB,GAAG,IAAI,CAACJ,iBAAiB,CAACQ,aAAa,CAAC,kBAAkB,CAAC;EAEnF;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,GAAG,IAAI,CAACL,KAAK,CAACS,GAAG,CAAC,SAAS,CAAC;IAC5C,IAAI,CAACd,eAAe,CAACe,iBAAiB,EAAE,CAACC,SAAS,CAAEC,IAAS,IAAI;MAE/D,IAAI,CAACnD,aAAa,GAAGmD,IAAI,IAAI,CAAC;MAC9B,IAAI,IAAI,CAACnD,aAAa,GAAG,CAAC,EAAE;QAE1B,IAAI,IAAI,CAACA,aAAa,GAAG,CAAC,EAAE;UAC1B,IAAI,CAACA,aAAa,GAAG,IAAI;;;MAG7B,IAAI,CAACmC,EAAE,CAACiB,YAAY,EAAE;MACtB,IAAI,CAACjB,EAAE,CAACkB,aAAa,EAAE;IACzB,CAAC,CAAC;IACF,IAAI,CAACxB,YAAY,GAAG,CAClB;MACER,IAAI,EAAE,aAAa;MACnBE,IAAI,EAAE,8BAA8B;MACpCD,UAAU,EAAE;KACb,EACD;MACED,IAAI,EAAE,iBAAiB;MACvBE,IAAI,EAAE,kCAAkC;MACxCD,UAAU,EAAE;KACb,EACD;MACED,IAAI,EAAE,mBAAmB;MACzBE,IAAI,EAAE,oCAAoC;MAC1CD,UAAU,EAAE;KACb,EACD;MACED,IAAI,EAAE,aAAa;MACnBE,IAAI,EAAE,8BAA8B;MAEpCD,UAAU,EAAE;KACb,EACD;MACED,IAAI,EAAE,gBAAgB;MACtBE,IAAI,EAAE,iCAAiC;MACvCD,UAAU,EAAE;KACb,CACF;IACD,IAAI,CAACqB,YAAY,GAAG,IAAI,CAACd,YAAY,CAAC,CAAC,CAAC;IACxC,IAAI,CAACO,aAAa,CAACkB,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;IAC7C,IAAI,CAACxB,gBAAgB,CAACyB,aAAa,CAACN,SAAS,CAC1CO,OAAO,IAAM,IAAI,CAACjB,SAAS,GAAGiB,OAAQ,CACxC;IACD,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAG,IAAI,CAACR,aAAa,CAACgB,GAAG,CAAC,WAAW,CAAC;;EAExD;EAEApC,gBAAgBA,CAAC8C,IAAU;IAEzB,MAAMC,gBAAgB,GAAgB,IAAI,CAAC9B,YAAY,CAAC+B,IAAI,CAAEC,CAAO,IAAKA,CAAC,CAACxC,IAAI,KAAKqC,IAAI,CAACrC,IAAI,CAAC,IAAI,IAAI;IAEvG,IAAIqC,IAAI,CAACrC,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAG,IAAI,CAACqB,iBAAiB,EAAC;QAC1B,IAAI,CAACL,UAAU,CAACyB,KAAK,CAACnE,iBAAiB,CAACoE,mBAAmB,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE,IAAI,EAAG;UAC1F,SAAS,EAAC,IAAI,CAACnB,WAAW,GAAC,IAAI,CAACA,WAAW,EAAEoB,YAAY,GAAC,kBAAkB;UAC5E,YAAY,EAAE,IAAI,CAACzB,KAAK,CAACS,GAAG,CAAC,QAAQ,CAAC;UACtC,aAAa,EAAE,IAAI,CAACT,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEiB,UAAU;UACxD,WAAW,EAAE,IAAI,CAAC1B,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEkB;SAC3C,CAAC;;MAEF,IAAI,IAAI,CAAC1B,SAAS,EAAE,IAAI,CAACP,MAAM,CAACkC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,MACjD,IAAI,CAAClC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACtC,MAAM,IAAGT,IAAI,CAACrC,IAAI,KAAK,aAAa,EAAE;MACrC,IAAI,CAACY,MAAM,CAACkC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;KAC5B,MAAM,IAAGT,IAAI,CAACrC,IAAI,KAAK,iBAAiB,EAAE;MACzC,IAAG,IAAI,CAACqB,iBAAiB,EAAC;QAE1B,IAAI,CAACL,UAAU,CAACyB,KAAK,CAACnE,iBAAiB,CAACyE,wBAAwB,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAC;UAChF,SAAS,EAAC,IAAI,CAACxB,WAAW,GAAC,IAAI,CAACA,WAAW,CAACoB,YAAY,GAAC,kBAAkB;UAC3E,YAAY,EAAE,IAAI,CAACzB,KAAK,CAACS,GAAG,CAAC,QAAQ,CAAC;UACtC,aAAa,EAAE,IAAI,CAACT,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEiB,UAAU;UACzD,WAAW,EAAE,IAAI,CAAC1B,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEkB;SAE1C,CAAC;;MAEF,IAAI,IAAI,CAAC1B,SAAS,EAAE,IAAI,CAACP,MAAM,CAACkC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,MAClD,IAAI,CAAClC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACtC,MACI,IAAGT,IAAI,CAACrC,IAAI,KAAK,aAAa,EAAE;MACnC,IAAG,IAAI,CAACqB,iBAAiB,EAAC;QAC5B,IAAI,CAACL,UAAU,CAACyB,KAAK,CAACnE,iBAAiB,CAAC0E,kBAAkB,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,EAAE,IAAI,EAAG;UACxG,SAAS,EAAC,IAAI,CAACzB,WAAW,GAAC,IAAI,CAACA,WAAW,CAACoB,YAAY,GAAC,kBAAkB;UAC3E,YAAY,EAAE,IAAI,CAACzB,KAAK,CAACS,GAAG,CAAC,QAAQ,CAAC;UACtC,aAAa,EAAE,IAAI,CAACT,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEiB,UAAU;UACvD,WAAW,EAAE,IAAI,CAAC1B,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEkB,QAAQ;UACnD,YAAY,EAAEI,YAAY,CAACC,OAAO,CAAC,WAAW;SAC/C,CAAC;;MAED,IAAI,CAACtC,MAAM,CAACkC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;KAC/B,MACI,IAAGT,IAAI,CAACrC,IAAI,KAAK,mBAAmB,EAAE;MACzC,IAAG,IAAI,CAACqB,iBAAiB,EAAC;QAC1B,IAAI,CAACL,UAAU,CAACyB,KAAK,CAACnE,iBAAiB,CAAC6E,iBAAiB,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE,IAAI,EAAG;UACxF,SAAS,EAAC,IAAI,CAAC5B,WAAW,GAAC,IAAI,CAACA,WAAW,CAACoB,YAAY,GAAC,kBAAkB;UAC3E,YAAY,EAAE,IAAI,CAACzB,KAAK,CAACS,GAAG,CAAC,QAAQ,CAAC;UACtC,aAAa,EAAE,IAAI,CAACT,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEiB,UAAU;UACzD,WAAW,EAAE,IAAI,CAAC1B,KAAK,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEkB;SAC1C,CAAC;;MAEF,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;IAE5C,IAAGR,gBAAgB,EAAEtC,IAAI,KAAK,IAAI,CAACsB,YAAY,EAAEtB,IAAI,EAAC;MACpD,IAAI,CAACe,aAAa,CAACkB,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;KAC9C,MAAI;MACH,IAAI,CAACnB,aAAa,CAACkB,eAAe,CAACC,IAAI,CAAC,KAAK,CAAC;;IAGhD,IAAI,CAACZ,YAAY,GAAGgB,gBAAgB;EACtC;EAEAvC,oBAAoBA,CAACsC,IAAY;IAC/B,MAAMC,gBAAgB,GAAkB,IAAI,CAAC9B,YAAY,CAAC+B,IAAI,CAAEC,CAAO,IAAKA,CAAC,CAACxC,IAAI,KAAKqC,IAAI,CAAC,IAAI,IAAI;IACpG,IAAGC,gBAAgB,EAAE;MACnB,IAAGA,gBAAgB,CAACtC,IAAI,KAAK,gBAAgB,EAAE;QAC7C,OAAO,IAAI,CAACY,MAAM,CAACwC,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAACzC,MAAM,CAACwC,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC;OAChF,MAAM,IAAGf,gBAAgB,CAACtC,IAAI,KAAK,aAAa,EAAE;QACjD,OAAO,IAAI,CAACY,MAAM,CAACwC,GAAG,KAAK,GAAG;OAC/B,MACI,IAAGd,gBAAgB,CAACtC,IAAI,KAAK,iBAAiB,EAAE;QACnD,OAAO,IAAI,CAACY,MAAM,CAACwC,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC;OAC7C,MACI,IAAGf,gBAAgB,CAACtC,IAAI,KAAK,aAAa,EAAE;QAC/C,OAAO,IAAI,CAACY,MAAM,CAACwC,GAAG,KAAK,OAAO;OACnC,MACI,IAAGd,gBAAgB,CAACtC,IAAI,KAAK,mBAAmB,EAAE;QACrD,OAAO,IAAI,CAACY,MAAM,CAACwC,GAAG,KAAK,kBAAkB;;;EAGnD;;qBA3JW3C,qBAAqB,EAAAlC,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnF,EAAA,CAAA+E,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAArF,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAM,eAAA,GAAAtF,EAAA,CAAA+E,iBAAA,CAAA/E,EAAA,CAAAuF,iBAAA,GAAAvF,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAQ,aAAA,GAAAxF,EAAA,CAAA+E,iBAAA,CAAAU,EAAA,CAAAC,sBAAA,GAAA1F,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAW,iBAAA,GAAA3F,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAY,YAAA;EAAA;;UAArB1D,qBAAqB;IAAA2D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClBlCnG,EAAA,CAAAiB,UAAA,IAAAoF,oCAAA,iBAWM;;;QAX6BrG,EAAA,CAAAsB,UAAA,SAAA8E,GAAA,CAAAvD,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}