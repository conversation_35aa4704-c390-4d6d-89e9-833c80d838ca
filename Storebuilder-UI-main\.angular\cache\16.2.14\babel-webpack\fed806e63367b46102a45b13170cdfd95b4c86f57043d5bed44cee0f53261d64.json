{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport { Loader } from '@googlemaps/js-api-loader';\nclass NgxGpAutocompleteService {\n  constructor() {\n    this.defaultOptions = {};\n  }\n  setOptions(options) {\n    this.defaultOptions = {\n      ...this.defaultOptions,\n      ...options\n    };\n  }\n  getOptions() {\n    return this.defaultOptions;\n  }\n}\nNgxGpAutocompleteService.ɵfac = function NgxGpAutocompleteService_Factory(t) {\n  return new (t || NgxGpAutocompleteService)();\n};\nNgxGpAutocompleteService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxGpAutocompleteService,\n  factory: NgxGpAutocompleteService.ɵfac,\n  providedIn: 'root'\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGpAutocompleteService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass NgxGpConfig {}\nclass NgxGpAutocompleteDirective {\n  constructor(el, ngxGpAutocompleteService, config, ngZone) {\n    this.el = el;\n    this.ngxGpAutocompleteService = ngxGpAutocompleteService;\n    this.config = config;\n    this.ngZone = ngZone;\n    this.onAddressChange = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    if (!this.options) this.options = this.ngxGpAutocompleteService.getOptions();\n    new Loader(this.config.loaderOptions).load().then(() => {\n      this.initialize();\n    });\n  }\n  isGoogleLibExists() {\n    return !(!google || !google.maps || !google.maps.places);\n  }\n  initialize() {\n    if (!this.isGoogleLibExists()) throw new Error(\"Google maps library can not be found\");\n    this.autocomplete = new google.maps.places.Autocomplete(this.el.nativeElement, this.options);\n    if (!this.autocomplete) throw new Error(\"Autocomplete is not initialized\");\n    if (!this.autocomplete.addListener != null) {\n      // Check to bypass https://github.com/angular-ui/angular-google-maps/issues/270\n      this.eventListener = this.autocomplete.addListener('place_changed', () => {\n        this.handleChangeEvent();\n      });\n    }\n    this.el.nativeElement.addEventListener('keydown', event => {\n      if (!event.key) {\n        return;\n      }\n      let key = event.key.toLowerCase();\n      if (key == 'enter' && event.target === this.el.nativeElement) {\n        event.preventDefault();\n        event.stopPropagation();\n      }\n    });\n    // according to https://gist.github.com/schoenobates/ef578a02ac8ab6726487\n    if (window && window.navigator && window.navigator.userAgent && navigator.userAgent.match(/(iPad|iPhone|iPod)/g)) {\n      setTimeout(() => {\n        let containers = document.getElementsByClassName('pac-container');\n        if (containers) {\n          let arr = Array.from(containers);\n          if (arr) {\n            for (let container of arr) {\n              if (!container) continue;\n              container.addEventListener('touchend', e => {\n                e.stopImmediatePropagation();\n              });\n            }\n          }\n        }\n      }, 500);\n    }\n  }\n  reset() {\n    this.autocomplete.setComponentRestrictions(this.options.componentRestrictions);\n    this.autocomplete.setTypes(this.options.types);\n  }\n  handleChangeEvent() {\n    this.ngZone.run(() => {\n      this.place = this.autocomplete.getPlace();\n      if (this.place) {\n        this.onAddressChange.emit(this.place);\n      }\n    });\n  }\n}\nNgxGpAutocompleteDirective.ɵfac = function NgxGpAutocompleteDirective_Factory(t) {\n  return new (t || NgxGpAutocompleteDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgxGpAutocompleteService), i0.ɵɵdirectiveInject(NgxGpConfig), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nNgxGpAutocompleteDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgxGpAutocompleteDirective,\n  selectors: [[\"\", \"ngx-gp-autocomplete\", \"\"]],\n  inputs: {\n    options: \"options\"\n  },\n  outputs: {\n    onAddressChange: \"onAddressChange\"\n  },\n  exportAs: [\"ngx-places\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGpAutocompleteDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngx-gp-autocomplete]',\n      exportAs: 'ngx-places'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: NgxGpAutocompleteService\n    }, {\n      type: NgxGpConfig\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    options: [{\n      type: Input\n    }],\n    onAddressChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxGpAutocompleteModule {\n  static forRoot(config) {\n    return {\n      ngModule: NgxGpAutocompleteModule,\n      providers: [{\n        provide: NgxGpConfig,\n        useValue: config\n      }]\n    };\n  }\n}\nNgxGpAutocompleteModule.ɵfac = function NgxGpAutocompleteModule_Factory(t) {\n  return new (t || NgxGpAutocompleteModule)();\n};\nNgxGpAutocompleteModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxGpAutocompleteModule\n});\nNgxGpAutocompleteModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxGpAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgxGpAutocompleteDirective],\n      exports: [NgxGpAutocompleteDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ngx-gp-autocomplete\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgxGpAutocompleteDirective, NgxGpAutocompleteModule, NgxGpAutocompleteService, NgxGpConfig };\n//# sourceMappingURL=angular-magic-ngx-gp-autocomplete.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}