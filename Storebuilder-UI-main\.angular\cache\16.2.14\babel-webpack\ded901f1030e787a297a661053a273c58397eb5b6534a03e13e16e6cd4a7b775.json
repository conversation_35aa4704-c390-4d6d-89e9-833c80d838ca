{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap((a, i) => map((b, ii) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n  } else if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "mergeInternals", "isFunction", "mergeMap", "project", "resultSelector", "concurrent", "Infinity", "a", "i", "b", "ii", "source", "subscriber"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/rxjs/dist/esm/internal/operators/mergeMap.js"], "sourcesContent": ["import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMap(project, resultSelector, concurrent = Infinity) {\n    if (isFunction(resultSelector)) {\n        return mergeMap((a, i) => map((b, ii) => resultSelector(a, b, i, ii))(innerFrom(project(a, i))), concurrent);\n    }\n    else if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent));\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,QAAQA,CAACC,OAAO,EAAEC,cAAc,EAAEC,UAAU,GAAGC,QAAQ,EAAE;EACrE,IAAIL,UAAU,CAACG,cAAc,CAAC,EAAE;IAC5B,OAAOF,QAAQ,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAKX,GAAG,CAAC,CAACY,CAAC,EAAEC,EAAE,KAAKN,cAAc,CAACG,CAAC,EAAEE,CAAC,EAAED,CAAC,EAAEE,EAAE,CAAC,CAAC,CAACZ,SAAS,CAACK,OAAO,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC;EAChH,CAAC,MACI,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACzCC,UAAU,GAAGD,cAAc;EAC/B;EACA,OAAOL,OAAO,CAAC,CAACY,MAAM,EAAEC,UAAU,KAAKZ,cAAc,CAACW,MAAM,EAAEC,UAAU,EAAET,OAAO,EAAEE,UAAU,CAAC,CAAC;AACnG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}