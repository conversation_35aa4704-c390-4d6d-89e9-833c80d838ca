<p-dialog [(visible)]="displayModal"
          [breakpoints]="{ '960px': '75vw', '640px': '90vw' }" [dismissableMask]="true" [draggable]="false" [showHeader]="false"
          [modal]="true" [resizable]="false" >
  <ng-template pTemplate="content">
    <section class="login">
      <div *ngIf="displayModal" class="login-content-container mt-3">
        <div class="grid justify-content-between mobile-top">
          <div class="shadow-signin">
            <div class="col-12 image">
              <img src="assets/images/new-signin.svg" alt="" srcset="">
            </div>
            <div class="col-12 col-md-8 col-lg-6 bg-white header-body" style="line-height: 1.5">
              <!-- Show sign-in-up-header only on mobile -->
                           <sign-in-up-header
                             class="mobile-only"
                             [title]="'signIn.signIn'"
                             [img]="'assets/images/new-signin.svg'">
                           </sign-in-up-header>

              <!-- Desktop header content -->
              <div class="desktop-only">
                <p class="signin-heading">{{ "signIn.signIn" | translate }}</p>
                <p class="signIn-content">{{ "signIn.content" | translate }}</p>
              </div>

              <form autocomplete="new-password">
                <div class="p-fluid p-grid">
                  <!-- Phone Input Component -->
                  <app-phone-input
                    [alreadyAddedPhoneNumber]="phoneNumber"
                    [maxLength]="phoneInputLength"
                    [selectedCountryISO]="customCountryISO"
                    [preferredCountries]="preferredCountries"
                    [placeholder]="customPlaceHolder"
                    (phoneNumberChange)="onPhoneNumberChange($event)"
                    (validationChange)="onPhoneValidationChange($event)"
                    (rawPhoneNumberChange)="onRawPhoneNumberChange($event)">
                  </app-phone-input>

                  <!-- Password Input Component -->
                  <app-password-input
                    [showForgotPassword]="true"
                    [forgotPasswordClass]="forgotPasswordClass"
                    [floatingLabel]="floatingLabelEnabled"
                    (passwordChange)="onPasswordChange($event)"
                    (validationChange)="onPasswordValidationChange($event)"
                    (forgotPasswordClick)="resetPassword()"
                    [feedback]="false"
                  >
                  </app-password-input>

                  <button
                    (click)="login()"
                    [disabled]="!isFormValid"
                    [label]="buttonLabel | translate"
                    class="sign-in-btn"
                    pButton
                    type="button">
                  </button>

                  <p class="signin-agreement">
                    {{ "signIn.AgreeTermsOne" | translate }}
                    <a (click)="reloadCurrentPage(171, 'Terms and Conditions')">
                      {{ "signIn.AgreeTermsTwo" | translate }}
                    </a>&nbsp;{{ "signIn.AgreeTermsThree" | translate }}
                    <a (click)="reloadCurrentPage(170, 'Privacy policy')">
                      {{ "signIn.AgreeTermsFour" | translate }}
                    </a>&nbsp;{{ "signIn.AgreeTermsFive" | translate }}.
                  </p>

<!--                  <div class="new-customer-container">-->
<!--                    <p>{{ "signIn.newCustomer" | translate }}</p>-->
<!--                    <a class="register-now" [routerLink]="['/register']">-->
<!--                      {{ "signIn.Register" | translate }}-->
<!--                    </a>-->
<!--                  </div>-->
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

  </ng-template>
</p-dialog>
