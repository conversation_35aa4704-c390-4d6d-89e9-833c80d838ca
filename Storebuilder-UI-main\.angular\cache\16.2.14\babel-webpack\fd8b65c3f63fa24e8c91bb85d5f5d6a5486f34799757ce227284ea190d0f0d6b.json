{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewChild, Output, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"zoomContainer\"];\nconst _c1 = [\"imageThumbnail\"];\nconst _c2 = [\"fullSizeImage\"];\nconst _c3 = function (a1) {\n  return {\n    ngxImageZoomFullContainer: true,\n    ngxImageZoomLensEnabled: a1\n  };\n};\nclass NgxImageZoomComponent {\n  constructor(renderer, changeDetectorRef) {\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this.zoomScroll = new EventEmitter();\n    this.zoomPosition = new EventEmitter();\n    this.imagesLoaded = new EventEmitter();\n    this.enableLens = false;\n    this.lensBorderRadius = 0;\n    this.thumbWidth = 0;\n    this.thumbHeight = 0;\n    this.fullWidth = 0;\n    this.fullHeight = 0;\n    this.lensWidth = 100;\n    this.lensHeight = 100;\n    this.zoomMode = 'hover';\n    this.magnification = 1;\n    this.enableScrollZoom = false;\n    this.scrollStepSize = 0.1;\n    this.circularLens = false;\n    this.minZoomRatio = 1;\n    this.maxZoomRatio = 2;\n    this.xRatio = 0;\n    this.yRatio = 0;\n    this.zoomingEnabled = false;\n    this.zoomFrozen = false;\n    this.isReady = false;\n    this.thumbImageLoaded = false;\n    this.fullImageLoaded = false;\n    this.latestMouseLeft = -1;\n    this.latestMouseTop = -1;\n    this.eventListeners = [];\n    this.altText = '';\n    this.titleText = '';\n  }\n  set setThumbImage(thumbImage) {\n    this.thumbImageLoaded = false;\n    this.setIsReady(false);\n    this.thumbImage = thumbImage;\n  }\n  set setFullImage(fullImage) {\n    this.fullImageLoaded = false;\n    this.setIsReady(false);\n    this.fullImage = fullImage;\n  }\n  set setZoomMode(zoomMode) {\n    if (NgxImageZoomComponent.validZoomModes.some(m => m === zoomMode)) {\n      this.zoomMode = zoomMode;\n    }\n  }\n  set setMagnification(magnification) {\n    this.magnification = Number(magnification) || this.magnification;\n    this.zoomScroll.emit(this.magnification);\n  }\n  set setMinZoomRatio(minZoomRatio) {\n    const ratio = Number(minZoomRatio) || this.minZoomRatio || this.baseRatio || 0;\n    this.minZoomRatio = Math.max(ratio, this.baseRatio || 0);\n  }\n  set setMaxZoomRatio(maxZoomRatio) {\n    this.maxZoomRatio = Number(maxZoomRatio) || this.maxZoomRatio;\n  }\n  set setScrollStepSize(stepSize) {\n    this.scrollStepSize = Number(stepSize) || this.scrollStepSize;\n  }\n  set setEnableLens(enable) {\n    this.enableLens = Boolean(enable);\n  }\n  set setLensWidth(width) {\n    this.lensWidth = Number(width) || this.lensWidth;\n  }\n  set setLensHeight(height) {\n    this.lensHeight = Number(height) || this.lensHeight;\n  }\n  set setCircularLens(enable) {\n    this.circularLens = Boolean(enable);\n  }\n  set setEnableScrollZoom(enable) {\n    this.enableScrollZoom = Boolean(enable);\n  }\n  ngOnInit() {\n    this.setUpEventListeners();\n  }\n  ngOnChanges() {\n    if (this.enableLens) {\n      if (this.circularLens) {\n        this.lensBorderRadius = this.lensWidth / 2;\n      } else {\n        this.lensBorderRadius = 0;\n      }\n    }\n    this.calculateRatioAndOffset();\n    this.calculateImageAndLensPosition();\n  }\n  ngOnDestroy() {\n    this.eventListeners.forEach(destroyFn => destroyFn());\n  }\n  /**\r\n   * Template helper methods\r\n   */\n  onThumbImageLoaded() {\n    this.thumbImageLoaded = true;\n    this.checkImagesLoaded();\n  }\n  onFullImageLoaded() {\n    this.fullImageLoaded = true;\n    this.checkImagesLoaded();\n  }\n  setUpEventListeners() {\n    const nativeElement = this.zoomContainer.nativeElement;\n    switch (this.zoomMode) {\n      case 'hover':\n        this.eventListeners.push(this.renderer.listen(nativeElement, 'mouseenter', event => this.hoverMouseEnter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.hoverMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', event => this.hoverMouseMove(event)));\n        break;\n      case 'toggle':\n        this.eventListeners.push(this.renderer.listen(nativeElement, 'click', event => this.toggleClick(event)));\n        break;\n      case 'toggle-click':\n        this.eventListeners.push(this.renderer.listen(nativeElement, 'click', event => this.toggleClick(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.clickMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', event => this.clickMouseMove(event)));\n        break;\n      case 'click':\n        this.eventListeners.push(this.renderer.listen(nativeElement, 'click', event => this.clickStarter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.clickMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', event => this.clickMouseMove(event)));\n        break;\n      case 'hover-freeze':\n        this.eventListeners.push(this.renderer.listen(nativeElement, 'mouseenter', event => this.hoverFreezeMouseEnter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.hoverFreezeMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', event => this.hoverFreezeMouseMove(event)), this.renderer.listen(nativeElement, 'click', event => this.hoverFreezeClick(event)));\n    }\n    if (this.enableScrollZoom) {\n      // Chrome: 'mousewheel', Firefox: 'DOMMouseScroll', IE: 'onmousewheel'\n      this.eventListeners.push(this.renderer.listen(nativeElement, 'mousewheel', event => this.onMouseWheel(event)), this.renderer.listen(nativeElement, 'DOMMouseScroll', event => this.onMouseWheel(event)), this.renderer.listen(nativeElement, 'onmousewheel', event => this.onMouseWheel(event)));\n    }\n    if (this.enableLens && this.circularLens) {\n      this.lensBorderRadius = this.lensWidth / 2;\n    }\n  }\n  checkImagesLoaded() {\n    this.calculateRatioAndOffset();\n    if (this.thumbImageLoaded && this.fullImageLoaded) {\n      this.calculateImageAndLensPosition();\n      this.setIsReady(true);\n    }\n  }\n  setIsReady(value) {\n    this.isReady = value;\n    this.imagesLoaded.emit(value);\n  }\n  /**\r\n   * Zoom position setters\r\n   */\n  setZoomPosition(left, top) {\n    this.latestMouseLeft = Number(left) || this.latestMouseLeft;\n    this.latestMouseTop = Number(top) || this.latestMouseTop;\n    const c = {\n      x: this.latestMouseLeft,\n      y: this.latestMouseTop\n    };\n    this.zoomPosition.emit(c);\n  }\n  /**\r\n   * Mouse wheel event\r\n   */\n  onMouseWheel(event) {\n    // Don't eat events if zooming isn't active\n    if (!this.zoomingEnabled || this.zoomFrozen) {\n      return;\n    }\n    event = window.event || event; // old IE\n    const direction = Math.max(Math.min(event.wheelDelta || -event.detail, 1), -1);\n    if (direction > 0) {\n      // up\n      this.setMagnification = Math.min(this.magnification + this.scrollStepSize, this.maxZoomRatio);\n    } else {\n      // down\n      this.setMagnification = Math.max(this.magnification - this.scrollStepSize, this.minZoomRatio);\n    }\n    this.calculateRatio();\n    this.calculateZoomPosition(event);\n    // Prevent scrolling on page.\n    event.returnValue = false; // IE\n    if (event.preventDefault) {\n      event.preventDefault(); // Chrome & FF\n    }\n  }\n  /**\r\n   * Hover mode\r\n   */\n  hoverMouseEnter(event) {\n    this.zoomOn(event);\n  }\n  hoverMouseLeave() {\n    this.zoomOff();\n  }\n  hoverMouseMove(event) {\n    this.calculateZoomPosition(event);\n  }\n  /**\r\n   * Toggle mode\r\n   */\n  toggleClick(event) {\n    if (this.zoomingEnabled) {\n      this.zoomOff();\n    } else {\n      this.zoomOn(event);\n    }\n  }\n  /**\r\n   * Click mode\r\n   */\n  clickStarter(event) {\n    if (this.zoomingEnabled === false) {\n      this.zoomOn(event);\n    }\n  }\n  clickMouseLeave() {\n    this.zoomOff();\n  }\n  clickMouseMove(event) {\n    if (this.zoomingEnabled) {\n      this.calculateZoomPosition(event);\n    }\n  }\n  /**\r\n   * Hover freeze mode\r\n   */\n  hoverFreezeMouseEnter(event) {\n    if (this.zoomingEnabled && !this.zoomFrozen) {\n      this.zoomOn(event);\n    }\n  }\n  hoverFreezeMouseLeave() {\n    if (this.zoomingEnabled && !this.zoomFrozen) {\n      this.zoomOff();\n    }\n  }\n  hoverFreezeMouseMove(event) {\n    if (this.zoomingEnabled && !this.zoomFrozen) {\n      this.calculateZoomPosition(event);\n    }\n  }\n  hoverFreezeClick(event) {\n    if (this.zoomingEnabled && this.zoomFrozen) {\n      this.zoomFrozen = false;\n      this.zoomOff();\n    } else if (this.zoomingEnabled) {\n      this.zoomFrozen = true;\n      this.changeDetectorRef.markForCheck();\n    } else {\n      this.zoomOn(event);\n    }\n  }\n  /**\r\n   * Private helper methods\r\n   */\n  zoomOn(event) {\n    if (this.isReady) {\n      this.zoomingEnabled = true;\n      this.calculateRatioAndOffset();\n      this.display = 'block';\n      this.calculateZoomPosition(event);\n      this.changeDetectorRef.markForCheck();\n    }\n  }\n  zoomOff() {\n    this.zoomingEnabled = false;\n    this.display = 'none';\n    this.changeDetectorRef.markForCheck();\n  }\n  calculateZoomPosition(event) {\n    const newLeft = Math.max(Math.min(event.offsetX, this.thumbWidth), 0);\n    const newTop = Math.max(Math.min(event.offsetY, this.thumbHeight), 0);\n    this.setZoomPosition(newLeft, newTop);\n    this.calculateImageAndLensPosition();\n    this.changeDetectorRef.markForCheck();\n  }\n  calculateImageAndLensPosition() {\n    let lensLeftMod = 0;\n    let lensTopMod = 0;\n    if (this.enableLens && this.latestMouseLeft > 0) {\n      lensLeftMod = this.lensLeft = this.latestMouseLeft - this.lensWidth / 2;\n      lensTopMod = this.lensTop = this.latestMouseTop - this.lensHeight / 2;\n    }\n    this.fullImageLeft = this.latestMouseLeft * -this.xRatio - lensLeftMod;\n    this.fullImageTop = this.latestMouseTop * -this.yRatio - lensTopMod;\n  }\n  calculateRatioAndOffset() {\n    this.thumbWidth = this.imageThumbnail.nativeElement.width;\n    this.thumbHeight = this.imageThumbnail.nativeElement.height;\n    // If lens is disabled, set lens size to equal thumb size and position it on top of the thumb\n    if (!this.enableLens) {\n      this.lensWidth = this.thumbWidth;\n      this.lensHeight = this.thumbHeight;\n      this.lensLeft = 0;\n      this.lensTop = 0;\n    }\n    // getBoundingClientRect() ? https://stackoverflow.com/a/44008873\n    this.offsetTop = this.imageThumbnail.nativeElement.getBoundingClientRect().top;\n    this.offsetLeft = this.imageThumbnail.nativeElement.getBoundingClientRect().left;\n    if (this.fullImage === undefined) {\n      this.fullImage = this.thumbImage;\n    }\n    if (this.fullImageLoaded) {\n      this.fullWidth = this.fullSizeImage.nativeElement.naturalWidth;\n      this.fullHeight = this.fullSizeImage.nativeElement.naturalHeight;\n      this.baseRatio = Math.max(this.thumbWidth / this.fullWidth, this.thumbHeight / this.fullHeight);\n      // Don't allow zooming to smaller than thumbnail size\n      this.minZoomRatio = Math.max(this.minZoomRatio || 0, this.baseRatio || 0);\n      this.calculateRatio();\n    }\n  }\n  calculateRatio() {\n    this.magnifiedWidth = this.fullWidth * this.magnification;\n    this.magnifiedHeight = this.fullHeight * this.magnification;\n    this.xRatio = (this.magnifiedWidth - this.thumbWidth) / this.thumbWidth;\n    this.yRatio = (this.magnifiedHeight - this.thumbHeight) / this.thumbHeight;\n  }\n}\nNgxImageZoomComponent.validZoomModes = ['hover', 'toggle', 'click', 'toggle-click', 'hover-freeze'];\nNgxImageZoomComponent.ɵfac = function NgxImageZoomComponent_Factory(t) {\n  return new (t || NgxImageZoomComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nNgxImageZoomComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxImageZoomComponent,\n  selectors: [[\"lib-ngx-image-zoom\"]],\n  viewQuery: function NgxImageZoomComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 7);\n      i0.ɵɵviewQuery(_c2, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.zoomContainer = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.imageThumbnail = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fullSizeImage = _t.first);\n    }\n  },\n  inputs: {\n    setThumbImage: [\"thumbImage\", \"setThumbImage\"],\n    setFullImage: [\"fullImage\", \"setFullImage\"],\n    setZoomMode: [\"zoomMode\", \"setZoomMode\"],\n    setMagnification: [\"magnification\", \"setMagnification\"],\n    setMinZoomRatio: [\"minZoomRatio\", \"setMinZoomRatio\"],\n    setMaxZoomRatio: [\"maxZoomRatio\", \"setMaxZoomRatio\"],\n    setScrollStepSize: [\"scrollStepSize\", \"setScrollStepSize\"],\n    setEnableLens: [\"enableLens\", \"setEnableLens\"],\n    setLensWidth: [\"lensWidth\", \"setLensWidth\"],\n    setLensHeight: [\"lensHeight\", \"setLensHeight\"],\n    setCircularLens: [\"circularLens\", \"setCircularLens\"],\n    setEnableScrollZoom: [\"enableScrollZoom\", \"setEnableScrollZoom\"],\n    altText: \"altText\",\n    titleText: \"titleText\"\n  },\n  outputs: {\n    zoomScroll: \"zoomScroll\",\n    zoomPosition: \"zoomPosition\",\n    imagesLoaded: \"imagesLoaded\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 7,\n  vars: 35,\n  consts: [[1, \"ngxImageZoomContainer\"], [\"zoomContainer\", \"\"], [1, \"ngxImageZoomThumbnail\", 3, \"alt\", \"title\", \"src\", \"load\"], [\"imageThumbnail\", \"\"], [3, \"ngClass\"], [1, \"ngxImageZoomFull\", 3, \"alt\", \"title\", \"src\", \"load\"], [\"fullSizeImage\", \"\"]],\n  template: function NgxImageZoomComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1)(2, \"img\", 2, 3);\n      i0.ɵɵlistener(\"load\", function NgxImageZoomComponent_Template_img_load_2_listener() {\n        return ctx.onThumbImageLoaded();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"div\", 4)(5, \"img\", 5, 6);\n      i0.ɵɵlistener(\"load\", function NgxImageZoomComponent_Template_img_load_5_listener() {\n        return ctx.onFullImageLoaded();\n      });\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.thumbWidth, \"px\")(\"height\", ctx.thumbHeight, \"px\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"alt\", ctx.altText)(\"title\", ctx.titleText)(\"src\", ctx.thumbImage, i0.ɵɵsanitizeUrl);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleProp(\"display\", ctx.display)(\"top\", ctx.lensTop, \"px\")(\"left\", ctx.lensLeft, \"px\")(\"width\", ctx.lensWidth, \"px\")(\"height\", ctx.lensHeight, \"px\")(\"border-radius\", ctx.lensBorderRadius, \"px\");\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c3, ctx.enableLens));\n      i0.ɵɵadvance(1);\n      i0.ɵɵstyleProp(\"display\", ctx.display)(\"top\", ctx.fullImageTop, \"px\")(\"left\", ctx.fullImageLeft, \"px\")(\"width\", ctx.magnifiedWidth, \"px\")(\"height\", ctx.magnifiedHeight, \"px\");\n      i0.ɵɵproperty(\"alt\", ctx.altText)(\"title\", ctx.titleText)(\"src\", ctx.fullImage, i0.ɵɵsanitizeUrl);\n    }\n  },\n  dependencies: [i1.NgClass],\n  styles: [\".ngxImageZoomContainer[_ngcontent-%COMP%]{position:relative;margin:auto;overflow:hidden;pointer-events:none}.ngxImageZoomThumbnail[_ngcontent-%COMP%]{pointer-events:all}.ngxImageZoomFull[_ngcontent-%COMP%]{position:absolute;max-width:none;max-height:none;display:none;pointer-events:none}.ngxImageZoomFullContainer[_ngcontent-%COMP%]{position:absolute;overflow:hidden;pointer-events:none}.ngxImageZoomFullContainer.ngxImageZoomLensEnabled[_ngcontent-%COMP%]{border:2px solid red;cursor:crosshair;pointer-events:none}\"]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxImageZoomComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lib-ngx-image-zoom',\n      template: \"<div\\n    #zoomContainer\\n    class=\\\"ngxImageZoomContainer\\\"\\n    [style.width.px]=\\\"this.thumbWidth\\\"\\n    [style.height.px]=\\\"this.thumbHeight\\\"\\n>\\n    <img\\n        #imageThumbnail\\n        class=\\\"ngxImageZoomThumbnail\\\"\\n        [alt]=\\\"altText\\\"\\n        [title]=\\\"titleText\\\"\\n        [src]=\\\"thumbImage\\\"\\n        (load)=\\\"onThumbImageLoaded()\\\"\\n    />\\n\\n    <div\\n        [ngClass]=\\\"{\\n            ngxImageZoomFullContainer: true,\\n            ngxImageZoomLensEnabled: this.enableLens\\n        }\\\"\\n        [style.display]=\\\"this.display\\\"\\n        [style.top.px]=\\\"this.lensTop\\\"\\n        [style.left.px]=\\\"this.lensLeft\\\"\\n        [style.width.px]=\\\"this.lensWidth\\\"\\n        [style.height.px]=\\\"this.lensHeight\\\"\\n        [style.border-radius.px]=\\\"this.lensBorderRadius\\\"\\n    >\\n        <img\\n            #fullSizeImage\\n            class=\\\"ngxImageZoomFull\\\"\\n            [alt]=\\\"altText\\\"\\n            [title]=\\\"titleText\\\"\\n            [src]=\\\"fullImage\\\"\\n            (load)=\\\"onFullImageLoaded()\\\"\\n            [style.display]=\\\"this.display\\\"\\n            [style.top.px]=\\\"this.fullImageTop\\\"\\n            [style.left.px]=\\\"this.fullImageLeft\\\"\\n            [style.width.px]=\\\"this.magnifiedWidth\\\"\\n            [style.height.px]=\\\"this.magnifiedHeight\\\"\\n        />\\n    </div>\\n</div>\\n\",\n      styles: [\".ngxImageZoomContainer{position:relative;margin:auto;overflow:hidden;pointer-events:none}.ngxImageZoomThumbnail{pointer-events:all}.ngxImageZoomFull{position:absolute;max-width:none;max-height:none;display:none;pointer-events:none}.ngxImageZoomFullContainer{position:absolute;overflow:hidden;pointer-events:none}.ngxImageZoomFullContainer.ngxImageZoomLensEnabled{border:2px solid red;cursor:crosshair;pointer-events:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    zoomContainer: [{\n      type: ViewChild,\n      args: ['zoomContainer', {\n        static: true\n      }]\n    }],\n    imageThumbnail: [{\n      type: ViewChild,\n      args: ['imageThumbnail', {\n        static: true\n      }]\n    }],\n    fullSizeImage: [{\n      type: ViewChild,\n      args: ['fullSizeImage', {\n        static: true\n      }]\n    }],\n    zoomScroll: [{\n      type: Output\n    }],\n    zoomPosition: [{\n      type: Output\n    }],\n    imagesLoaded: [{\n      type: Output\n    }],\n    setThumbImage: [{\n      type: Input,\n      args: ['thumbImage']\n    }],\n    setFullImage: [{\n      type: Input,\n      args: ['fullImage']\n    }],\n    setZoomMode: [{\n      type: Input,\n      args: ['zoomMode']\n    }],\n    setMagnification: [{\n      type: Input,\n      args: ['magnification']\n    }],\n    setMinZoomRatio: [{\n      type: Input,\n      args: ['minZoomRatio']\n    }],\n    setMaxZoomRatio: [{\n      type: Input,\n      args: ['maxZoomRatio']\n    }],\n    setScrollStepSize: [{\n      type: Input,\n      args: ['scrollStepSize']\n    }],\n    setEnableLens: [{\n      type: Input,\n      args: ['enableLens']\n    }],\n    setLensWidth: [{\n      type: Input,\n      args: ['lensWidth']\n    }],\n    setLensHeight: [{\n      type: Input,\n      args: ['lensHeight']\n    }],\n    setCircularLens: [{\n      type: Input,\n      args: ['circularLens']\n    }],\n    setEnableScrollZoom: [{\n      type: Input,\n      args: ['enableScrollZoom']\n    }],\n    altText: [{\n      type: Input\n    }],\n    titleText: [{\n      type: Input\n    }]\n  });\n})();\nclass NgxImageZoomModule {}\nNgxImageZoomModule.ɵfac = function NgxImageZoomModule_Factory(t) {\n  return new (t || NgxImageZoomModule)();\n};\nNgxImageZoomModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxImageZoomModule\n});\nNgxImageZoomModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxImageZoomModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgxImageZoomComponent],\n      imports: [CommonModule],\n      exports: [NgxImageZoomComponent]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-image-zoom\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxImageZoomComponent, NgxImageZoomModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ViewChild", "Output", "Input", "NgModule", "i1", "CommonModule", "_c0", "_c1", "_c2", "_c3", "a1", "ngxImageZoomFullContainer", "ngxImageZoomLensEnabled", "NgxImageZoomComponent", "constructor", "renderer", "changeDetectorRef", "zoomScroll", "zoomPosition", "imagesLoaded", "enableLens", "lensBorderRadius", "thumbWidth", "thumbHeight", "fullWidth", "fullHeight", "lens<PERSON>id<PERSON>", "lensHeight", "zoomMode", "magnification", "enableScrollZoom", "scrollStepSize", "circularLens", "minZoomRatio", "maxZoomRatio", "xRatio", "yRatio", "zoomingEnabled", "zoomFrozen", "isReady", "thumbImageLoaded", "fullImageLoaded", "latestMouseLeft", "latestMouseTop", "eventListeners", "altText", "titleText", "setThumbImage", "thumbImage", "setIsReady", "setFullImage", "fullImage", "setZoomMode", "validZoomModes", "some", "m", "setMagnification", "Number", "emit", "setMinZoomRatio", "ratio", "baseRatio", "Math", "max", "setMaxZoomRatio", "setScrollStepSize", "stepSize", "setEnableLens", "enable", "Boolean", "set<PERSON><PERSON><PERSON><PERSON>th", "width", "setLensHeight", "height", "setCircularLens", "setEnableScrollZoom", "ngOnInit", "setUpEventListeners", "ngOnChanges", "calculateRatioAndOffset", "calculateImageAndLensPosition", "ngOnDestroy", "for<PERSON>ach", "destroyFn", "onThumbImageLoaded", "checkImagesLoaded", "onFullImageLoaded", "nativeElement", "zoomContainer", "push", "listen", "event", "hoverMouseEnter", "hoverMouseLeave", "hoverMouseMove", "toggleClick", "clickMouseLeave", "clickMouseMove", "clickStarter", "hoverFreezeMouseEnter", "hoverFreezeMouseLeave", "hoverFreezeMouseMove", "hoverFreezeClick", "onMouseWheel", "value", "setZoomPosition", "left", "top", "c", "x", "y", "window", "direction", "min", "wheelDelta", "detail", "calculateRatio", "calculateZoomPosition", "returnValue", "preventDefault", "zoomOn", "zoomOff", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "display", "newLeft", "offsetX", "newTop", "offsetY", "lensLeftMod", "lensTopMod", "lensLeft", "lensTop", "fullImageLeft", "fullImageTop", "imageThumbnail", "offsetTop", "getBoundingClientRect", "offsetLeft", "undefined", "fullSizeImage", "naturalWidth", "naturalHeight", "magnifiedWidth", "magnifiedHeight", "ɵfac", "NgxImageZoomComponent_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "NgxImageZoomComponent_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NgxImageZoomComponent_Template", "ɵɵelementStart", "ɵɵlistener", "NgxImageZoomComponent_Template_img_load_2_listener", "ɵɵelementEnd", "NgxImageZoomComponent_Template_img_load_5_listener", "ɵɵstyleProp", "ɵɵadvance", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "styles", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "static", "NgxImageZoomModule", "NgxImageZoomModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ngx-image-zoom/fesm2020/ngx-image-zoom.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewChild, Output, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass NgxImageZoomComponent {\r\n    constructor(renderer, changeDetectorRef) {\r\n        this.renderer = renderer;\r\n        this.changeDetectorRef = changeDetectorRef;\r\n        this.zoomScroll = new EventEmitter();\r\n        this.zoomPosition = new EventEmitter();\r\n        this.imagesLoaded = new EventEmitter();\r\n        this.enableLens = false;\r\n        this.lensBorderRadius = 0;\r\n        this.thumbWidth = 0;\r\n        this.thumbHeight = 0;\r\n        this.fullWidth = 0;\r\n        this.fullHeight = 0;\r\n        this.lensWidth = 100;\r\n        this.lensHeight = 100;\r\n        this.zoomMode = 'hover';\r\n        this.magnification = 1;\r\n        this.enableScrollZoom = false;\r\n        this.scrollStepSize = 0.1;\r\n        this.circularLens = false;\r\n        this.minZoomRatio = 1;\r\n        this.maxZoomRatio = 2;\r\n        this.xRatio = 0;\r\n        this.yRatio = 0;\r\n        this.zoomingEnabled = false;\r\n        this.zoomFrozen = false;\r\n        this.isReady = false;\r\n        this.thumbImageLoaded = false;\r\n        this.fullImageLoaded = false;\r\n        this.latestMouseLeft = -1;\r\n        this.latestMouseTop = -1;\r\n        this.eventListeners = [];\r\n        this.altText = '';\r\n        this.titleText = '';\r\n    }\r\n    set setThumbImage(thumbImage) {\r\n        this.thumbImageLoaded = false;\r\n        this.setIsReady(false);\r\n        this.thumbImage = thumbImage;\r\n    }\r\n    set setFullImage(fullImage) {\r\n        this.fullImageLoaded = false;\r\n        this.setIsReady(false);\r\n        this.fullImage = fullImage;\r\n    }\r\n    set setZoomMode(zoomMode) {\r\n        if (NgxImageZoomComponent.validZoomModes.some(m => m === zoomMode)) {\r\n            this.zoomMode = zoomMode;\r\n        }\r\n    }\r\n    set setMagnification(magnification) {\r\n        this.magnification = Number(magnification) || this.magnification;\r\n        this.zoomScroll.emit(this.magnification);\r\n    }\r\n    set setMinZoomRatio(minZoomRatio) {\r\n        const ratio = Number(minZoomRatio) || this.minZoomRatio || this.baseRatio || 0;\r\n        this.minZoomRatio = Math.max(ratio, this.baseRatio || 0);\r\n    }\r\n    set setMaxZoomRatio(maxZoomRatio) {\r\n        this.maxZoomRatio = Number(maxZoomRatio) || this.maxZoomRatio;\r\n    }\r\n    set setScrollStepSize(stepSize) {\r\n        this.scrollStepSize = Number(stepSize) || this.scrollStepSize;\r\n    }\r\n    set setEnableLens(enable) {\r\n        this.enableLens = Boolean(enable);\r\n    }\r\n    set setLensWidth(width) {\r\n        this.lensWidth = Number(width) || this.lensWidth;\r\n    }\r\n    set setLensHeight(height) {\r\n        this.lensHeight = Number(height) || this.lensHeight;\r\n    }\r\n    set setCircularLens(enable) {\r\n        this.circularLens = Boolean(enable);\r\n    }\r\n    set setEnableScrollZoom(enable) {\r\n        this.enableScrollZoom = Boolean(enable);\r\n    }\r\n    ngOnInit() {\r\n        this.setUpEventListeners();\r\n    }\r\n    ngOnChanges() {\r\n        if (this.enableLens) {\r\n            if (this.circularLens) {\r\n                this.lensBorderRadius = this.lensWidth / 2;\r\n            }\r\n            else {\r\n                this.lensBorderRadius = 0;\r\n            }\r\n        }\r\n        this.calculateRatioAndOffset();\r\n        this.calculateImageAndLensPosition();\r\n    }\r\n    ngOnDestroy() {\r\n        this.eventListeners.forEach((destroyFn) => destroyFn());\r\n    }\r\n    /**\r\n     * Template helper methods\r\n     */\r\n    onThumbImageLoaded() {\r\n        this.thumbImageLoaded = true;\r\n        this.checkImagesLoaded();\r\n    }\r\n    onFullImageLoaded() {\r\n        this.fullImageLoaded = true;\r\n        this.checkImagesLoaded();\r\n    }\r\n    setUpEventListeners() {\r\n        const nativeElement = this.zoomContainer.nativeElement;\r\n        switch (this.zoomMode) {\r\n            case 'hover':\r\n                this.eventListeners.push(this.renderer.listen(nativeElement, 'mouseenter', (event) => this.hoverMouseEnter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.hoverMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', (event) => this.hoverMouseMove(event)));\r\n                break;\r\n            case 'toggle':\r\n                this.eventListeners.push(this.renderer.listen(nativeElement, 'click', (event) => this.toggleClick(event)));\r\n                break;\r\n            case 'toggle-click':\r\n                this.eventListeners.push(this.renderer.listen(nativeElement, 'click', (event) => this.toggleClick(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.clickMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', (event) => this.clickMouseMove(event)));\r\n                break;\r\n            case 'click':\r\n                this.eventListeners.push(this.renderer.listen(nativeElement, 'click', (event) => this.clickStarter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.clickMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', (event) => this.clickMouseMove(event)));\r\n                break;\r\n            case 'hover-freeze':\r\n                this.eventListeners.push(this.renderer.listen(nativeElement, 'mouseenter', (event) => this.hoverFreezeMouseEnter(event)), this.renderer.listen(nativeElement, 'mouseleave', () => this.hoverFreezeMouseLeave()), this.renderer.listen(nativeElement, 'mousemove', (event) => this.hoverFreezeMouseMove(event)), this.renderer.listen(nativeElement, 'click', (event) => this.hoverFreezeClick(event)));\r\n        }\r\n        if (this.enableScrollZoom) {\r\n            // Chrome: 'mousewheel', Firefox: 'DOMMouseScroll', IE: 'onmousewheel'\r\n            this.eventListeners.push(this.renderer.listen(nativeElement, 'mousewheel', (event) => this.onMouseWheel(event)), this.renderer.listen(nativeElement, 'DOMMouseScroll', (event) => this.onMouseWheel(event)), this.renderer.listen(nativeElement, 'onmousewheel', (event) => this.onMouseWheel(event)));\r\n        }\r\n        if (this.enableLens && this.circularLens) {\r\n            this.lensBorderRadius = this.lensWidth / 2;\r\n        }\r\n    }\r\n    checkImagesLoaded() {\r\n        this.calculateRatioAndOffset();\r\n        if (this.thumbImageLoaded && this.fullImageLoaded) {\r\n            this.calculateImageAndLensPosition();\r\n            this.setIsReady(true);\r\n        }\r\n    }\r\n    setIsReady(value) {\r\n        this.isReady = value;\r\n        this.imagesLoaded.emit(value);\r\n    }\r\n    /**\r\n     * Zoom position setters\r\n     */\r\n    setZoomPosition(left, top) {\r\n        this.latestMouseLeft = Number(left) || this.latestMouseLeft;\r\n        this.latestMouseTop = Number(top) || this.latestMouseTop;\r\n        const c = {\r\n            x: this.latestMouseLeft,\r\n            y: this.latestMouseTop\r\n        };\r\n        this.zoomPosition.emit(c);\r\n    }\r\n    /**\r\n     * Mouse wheel event\r\n     */\r\n    onMouseWheel(event) {\r\n        // Don't eat events if zooming isn't active\r\n        if (!this.zoomingEnabled || this.zoomFrozen) {\r\n            return;\r\n        }\r\n        event = window.event || event; // old IE\r\n        const direction = Math.max(Math.min((event.wheelDelta || -event.detail), 1), -1);\r\n        if (direction > 0) {\r\n            // up\r\n            this.setMagnification = Math.min(this.magnification + this.scrollStepSize, this.maxZoomRatio);\r\n        }\r\n        else {\r\n            // down\r\n            this.setMagnification = Math.max(this.magnification - this.scrollStepSize, this.minZoomRatio);\r\n        }\r\n        this.calculateRatio();\r\n        this.calculateZoomPosition(event);\r\n        // Prevent scrolling on page.\r\n        event.returnValue = false; // IE\r\n        if (event.preventDefault) {\r\n            event.preventDefault(); // Chrome & FF\r\n        }\r\n    }\r\n    /**\r\n     * Hover mode\r\n     */\r\n    hoverMouseEnter(event) {\r\n        this.zoomOn(event);\r\n    }\r\n    hoverMouseLeave() {\r\n        this.zoomOff();\r\n    }\r\n    hoverMouseMove(event) {\r\n        this.calculateZoomPosition(event);\r\n    }\r\n    /**\r\n     * Toggle mode\r\n     */\r\n    toggleClick(event) {\r\n        if (this.zoomingEnabled) {\r\n            this.zoomOff();\r\n        }\r\n        else {\r\n            this.zoomOn(event);\r\n        }\r\n    }\r\n    /**\r\n     * Click mode\r\n     */\r\n    clickStarter(event) {\r\n        if (this.zoomingEnabled === false) {\r\n            this.zoomOn(event);\r\n        }\r\n    }\r\n    clickMouseLeave() {\r\n        this.zoomOff();\r\n    }\r\n    clickMouseMove(event) {\r\n        if (this.zoomingEnabled) {\r\n            this.calculateZoomPosition(event);\r\n        }\r\n    }\r\n    /**\r\n     * Hover freeze mode\r\n     */\r\n    hoverFreezeMouseEnter(event) {\r\n        if (this.zoomingEnabled && !this.zoomFrozen) {\r\n            this.zoomOn(event);\r\n        }\r\n    }\r\n    hoverFreezeMouseLeave() {\r\n        if (this.zoomingEnabled && !this.zoomFrozen) {\r\n            this.zoomOff();\r\n        }\r\n    }\r\n    hoverFreezeMouseMove(event) {\r\n        if (this.zoomingEnabled && !this.zoomFrozen) {\r\n            this.calculateZoomPosition(event);\r\n        }\r\n    }\r\n    hoverFreezeClick(event) {\r\n        if (this.zoomingEnabled && this.zoomFrozen) {\r\n            this.zoomFrozen = false;\r\n            this.zoomOff();\r\n        }\r\n        else if (this.zoomingEnabled) {\r\n            this.zoomFrozen = true;\r\n            this.changeDetectorRef.markForCheck();\r\n        }\r\n        else {\r\n            this.zoomOn(event);\r\n        }\r\n    }\r\n    /**\r\n     * Private helper methods\r\n     */\r\n    zoomOn(event) {\r\n        if (this.isReady) {\r\n            this.zoomingEnabled = true;\r\n            this.calculateRatioAndOffset();\r\n            this.display = 'block';\r\n            this.calculateZoomPosition(event);\r\n            this.changeDetectorRef.markForCheck();\r\n        }\r\n    }\r\n    zoomOff() {\r\n        this.zoomingEnabled = false;\r\n        this.display = 'none';\r\n        this.changeDetectorRef.markForCheck();\r\n    }\r\n    calculateZoomPosition(event) {\r\n        const newLeft = Math.max(Math.min(event.offsetX, this.thumbWidth), 0);\r\n        const newTop = Math.max(Math.min(event.offsetY, this.thumbHeight), 0);\r\n        this.setZoomPosition(newLeft, newTop);\r\n        this.calculateImageAndLensPosition();\r\n        this.changeDetectorRef.markForCheck();\r\n    }\r\n    calculateImageAndLensPosition() {\r\n        let lensLeftMod = 0;\r\n        let lensTopMod = 0;\r\n        if (this.enableLens && this.latestMouseLeft > 0) {\r\n            lensLeftMod = this.lensLeft = this.latestMouseLeft - this.lensWidth / 2;\r\n            lensTopMod = this.lensTop = this.latestMouseTop - this.lensHeight / 2;\r\n        }\r\n        this.fullImageLeft = (this.latestMouseLeft * -this.xRatio) - lensLeftMod;\r\n        this.fullImageTop = (this.latestMouseTop * -this.yRatio) - lensTopMod;\r\n    }\r\n    calculateRatioAndOffset() {\r\n        this.thumbWidth = this.imageThumbnail.nativeElement.width;\r\n        this.thumbHeight = this.imageThumbnail.nativeElement.height;\r\n        // If lens is disabled, set lens size to equal thumb size and position it on top of the thumb\r\n        if (!this.enableLens) {\r\n            this.lensWidth = this.thumbWidth;\r\n            this.lensHeight = this.thumbHeight;\r\n            this.lensLeft = 0;\r\n            this.lensTop = 0;\r\n        }\r\n        // getBoundingClientRect() ? https://stackoverflow.com/a/44008873\r\n        this.offsetTop = this.imageThumbnail.nativeElement.getBoundingClientRect().top;\r\n        this.offsetLeft = this.imageThumbnail.nativeElement.getBoundingClientRect().left;\r\n        if (this.fullImage === undefined) {\r\n            this.fullImage = this.thumbImage;\r\n        }\r\n        if (this.fullImageLoaded) {\r\n            this.fullWidth = this.fullSizeImage.nativeElement.naturalWidth;\r\n            this.fullHeight = this.fullSizeImage.nativeElement.naturalHeight;\r\n            this.baseRatio = Math.max((this.thumbWidth / this.fullWidth), (this.thumbHeight / this.fullHeight));\r\n            // Don't allow zooming to smaller than thumbnail size\r\n            this.minZoomRatio = Math.max(this.minZoomRatio || 0, this.baseRatio || 0);\r\n            this.calculateRatio();\r\n        }\r\n    }\r\n    calculateRatio() {\r\n        this.magnifiedWidth = (this.fullWidth * this.magnification);\r\n        this.magnifiedHeight = (this.fullHeight * this.magnification);\r\n        this.xRatio = (this.magnifiedWidth - this.thumbWidth) / this.thumbWidth;\r\n        this.yRatio = (this.magnifiedHeight - this.thumbHeight) / this.thumbHeight;\r\n    }\r\n}\r\nNgxImageZoomComponent.validZoomModes = ['hover', 'toggle', 'click', 'toggle-click', 'hover-freeze'];\r\nNgxImageZoomComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomComponent, deps: [{ token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\r\nNgxImageZoomComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.5\", type: NgxImageZoomComponent, selector: \"lib-ngx-image-zoom\", inputs: { setThumbImage: [\"thumbImage\", \"setThumbImage\"], setFullImage: [\"fullImage\", \"setFullImage\"], setZoomMode: [\"zoomMode\", \"setZoomMode\"], setMagnification: [\"magnification\", \"setMagnification\"], setMinZoomRatio: [\"minZoomRatio\", \"setMinZoomRatio\"], setMaxZoomRatio: [\"maxZoomRatio\", \"setMaxZoomRatio\"], setScrollStepSize: [\"scrollStepSize\", \"setScrollStepSize\"], setEnableLens: [\"enableLens\", \"setEnableLens\"], setLensWidth: [\"lensWidth\", \"setLensWidth\"], setLensHeight: [\"lensHeight\", \"setLensHeight\"], setCircularLens: [\"circularLens\", \"setCircularLens\"], setEnableScrollZoom: [\"enableScrollZoom\", \"setEnableScrollZoom\"], altText: \"altText\", titleText: \"titleText\" }, outputs: { zoomScroll: \"zoomScroll\", zoomPosition: \"zoomPosition\", imagesLoaded: \"imagesLoaded\" }, viewQueries: [{ propertyName: \"zoomContainer\", first: true, predicate: [\"zoomContainer\"], descendants: true, static: true }, { propertyName: \"imageThumbnail\", first: true, predicate: [\"imageThumbnail\"], descendants: true, static: true }, { propertyName: \"fullSizeImage\", first: true, predicate: [\"fullSizeImage\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n    #zoomContainer\\n    class=\\\"ngxImageZoomContainer\\\"\\n    [style.width.px]=\\\"this.thumbWidth\\\"\\n    [style.height.px]=\\\"this.thumbHeight\\\"\\n>\\n    <img\\n        #imageThumbnail\\n        class=\\\"ngxImageZoomThumbnail\\\"\\n        [alt]=\\\"altText\\\"\\n        [title]=\\\"titleText\\\"\\n        [src]=\\\"thumbImage\\\"\\n        (load)=\\\"onThumbImageLoaded()\\\"\\n    />\\n\\n    <div\\n        [ngClass]=\\\"{\\n            ngxImageZoomFullContainer: true,\\n            ngxImageZoomLensEnabled: this.enableLens\\n        }\\\"\\n        [style.display]=\\\"this.display\\\"\\n        [style.top.px]=\\\"this.lensTop\\\"\\n        [style.left.px]=\\\"this.lensLeft\\\"\\n        [style.width.px]=\\\"this.lensWidth\\\"\\n        [style.height.px]=\\\"this.lensHeight\\\"\\n        [style.border-radius.px]=\\\"this.lensBorderRadius\\\"\\n    >\\n        <img\\n            #fullSizeImage\\n            class=\\\"ngxImageZoomFull\\\"\\n            [alt]=\\\"altText\\\"\\n            [title]=\\\"titleText\\\"\\n            [src]=\\\"fullImage\\\"\\n            (load)=\\\"onFullImageLoaded()\\\"\\n            [style.display]=\\\"this.display\\\"\\n            [style.top.px]=\\\"this.fullImageTop\\\"\\n            [style.left.px]=\\\"this.fullImageLeft\\\"\\n            [style.width.px]=\\\"this.magnifiedWidth\\\"\\n            [style.height.px]=\\\"this.magnifiedHeight\\\"\\n        />\\n    </div>\\n</div>\\n\", styles: [\".ngxImageZoomContainer{position:relative;margin:auto;overflow:hidden;pointer-events:none}.ngxImageZoomThumbnail{pointer-events:all}.ngxImageZoomFull{position:absolute;max-width:none;max-height:none;display:none;pointer-events:none}.ngxImageZoomFullContainer{position:absolute;overflow:hidden;pointer-events:none}.ngxImageZoomFullContainer.ngxImageZoomLensEnabled{border:2px solid red;cursor:crosshair;pointer-events:none}\\n\"], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomComponent, decorators: [{\r\n            type: Component,\r\n            args: [{ selector: 'lib-ngx-image-zoom', template: \"<div\\n    #zoomContainer\\n    class=\\\"ngxImageZoomContainer\\\"\\n    [style.width.px]=\\\"this.thumbWidth\\\"\\n    [style.height.px]=\\\"this.thumbHeight\\\"\\n>\\n    <img\\n        #imageThumbnail\\n        class=\\\"ngxImageZoomThumbnail\\\"\\n        [alt]=\\\"altText\\\"\\n        [title]=\\\"titleText\\\"\\n        [src]=\\\"thumbImage\\\"\\n        (load)=\\\"onThumbImageLoaded()\\\"\\n    />\\n\\n    <div\\n        [ngClass]=\\\"{\\n            ngxImageZoomFullContainer: true,\\n            ngxImageZoomLensEnabled: this.enableLens\\n        }\\\"\\n        [style.display]=\\\"this.display\\\"\\n        [style.top.px]=\\\"this.lensTop\\\"\\n        [style.left.px]=\\\"this.lensLeft\\\"\\n        [style.width.px]=\\\"this.lensWidth\\\"\\n        [style.height.px]=\\\"this.lensHeight\\\"\\n        [style.border-radius.px]=\\\"this.lensBorderRadius\\\"\\n    >\\n        <img\\n            #fullSizeImage\\n            class=\\\"ngxImageZoomFull\\\"\\n            [alt]=\\\"altText\\\"\\n            [title]=\\\"titleText\\\"\\n            [src]=\\\"fullImage\\\"\\n            (load)=\\\"onFullImageLoaded()\\\"\\n            [style.display]=\\\"this.display\\\"\\n            [style.top.px]=\\\"this.fullImageTop\\\"\\n            [style.left.px]=\\\"this.fullImageLeft\\\"\\n            [style.width.px]=\\\"this.magnifiedWidth\\\"\\n            [style.height.px]=\\\"this.magnifiedHeight\\\"\\n        />\\n    </div>\\n</div>\\n\", styles: [\".ngxImageZoomContainer{position:relative;margin:auto;overflow:hidden;pointer-events:none}.ngxImageZoomThumbnail{pointer-events:all}.ngxImageZoomFull{position:absolute;max-width:none;max-height:none;display:none;pointer-events:none}.ngxImageZoomFullContainer{position:absolute;overflow:hidden;pointer-events:none}.ngxImageZoomFullContainer.ngxImageZoomLensEnabled{border:2px solid red;cursor:crosshair;pointer-events:none}\\n\"] }]\r\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { zoomContainer: [{\r\n                type: ViewChild,\r\n                args: ['zoomContainer', { static: true }]\r\n            }], imageThumbnail: [{\r\n                type: ViewChild,\r\n                args: ['imageThumbnail', { static: true }]\r\n            }], fullSizeImage: [{\r\n                type: ViewChild,\r\n                args: ['fullSizeImage', { static: true }]\r\n            }], zoomScroll: [{\r\n                type: Output\r\n            }], zoomPosition: [{\r\n                type: Output\r\n            }], imagesLoaded: [{\r\n                type: Output\r\n            }], setThumbImage: [{\r\n                type: Input,\r\n                args: ['thumbImage']\r\n            }], setFullImage: [{\r\n                type: Input,\r\n                args: ['fullImage']\r\n            }], setZoomMode: [{\r\n                type: Input,\r\n                args: ['zoomMode']\r\n            }], setMagnification: [{\r\n                type: Input,\r\n                args: ['magnification']\r\n            }], setMinZoomRatio: [{\r\n                type: Input,\r\n                args: ['minZoomRatio']\r\n            }], setMaxZoomRatio: [{\r\n                type: Input,\r\n                args: ['maxZoomRatio']\r\n            }], setScrollStepSize: [{\r\n                type: Input,\r\n                args: ['scrollStepSize']\r\n            }], setEnableLens: [{\r\n                type: Input,\r\n                args: ['enableLens']\r\n            }], setLensWidth: [{\r\n                type: Input,\r\n                args: ['lensWidth']\r\n            }], setLensHeight: [{\r\n                type: Input,\r\n                args: ['lensHeight']\r\n            }], setCircularLens: [{\r\n                type: Input,\r\n                args: ['circularLens']\r\n            }], setEnableScrollZoom: [{\r\n                type: Input,\r\n                args: ['enableScrollZoom']\r\n            }], altText: [{\r\n                type: Input\r\n            }], titleText: [{\r\n                type: Input\r\n            }] } });\n\nclass NgxImageZoomModule {\r\n}\r\nNgxImageZoomModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nNgxImageZoomModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomModule, declarations: [NgxImageZoomComponent], imports: [CommonModule], exports: [NgxImageZoomComponent] });\r\nNgxImageZoomModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomModule, imports: [[\r\n            CommonModule,\r\n        ]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.5\", ngImport: i0, type: NgxImageZoomModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    declarations: [\r\n                        NgxImageZoomComponent,\r\n                    ],\r\n                    imports: [\r\n                        CommonModule,\r\n                    ],\r\n                    exports: [\r\n                        NgxImageZoomComponent\r\n                    ]\r\n                }]\r\n        }] });\n\n/*\r\n * Public API Surface of ngx-image-zoom\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxImageZoomComponent, NgxImageZoomModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC3F,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,YAAAA,CAAAC,EAAA;EAAA;IAAAC,yBAAA;IAAAC,uBAAA,EAAAF;EAAA;AAAA;AAE/C,MAAMG,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAEC,iBAAiB,EAAE;IACrC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,UAAU,GAAG,IAAInB,YAAY,CAAC,CAAC;IACpC,IAAI,CAACoB,YAAY,GAAG,IAAIpB,YAAY,CAAC,CAAC;IACtC,IAAI,CAACqB,YAAY,GAAG,IAAIrB,YAAY,CAAC,CAAC;IACtC,IAAI,CAACsB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,GAAG;IACpB,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB,IAAI,CAACC,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,cAAc,GAAG,GAAG;IACzB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;EACvB;EACA,IAAIC,aAAaA,CAACC,UAAU,EAAE;IAC1B,IAAI,CAACR,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACS,UAAU,CAAC,KAAK,CAAC;IACtB,IAAI,CAACD,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIE,YAAYA,CAACC,SAAS,EAAE;IACxB,IAAI,CAACV,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,UAAU,CAAC,KAAK,CAAC;IACtB,IAAI,CAACE,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIC,WAAWA,CAACxB,QAAQ,EAAE;IACtB,IAAIf,qBAAqB,CAACwC,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,KAAK3B,QAAQ,CAAC,EAAE;MAChE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;EACJ;EACA,IAAI4B,gBAAgBA,CAAC3B,aAAa,EAAE;IAChC,IAAI,CAACA,aAAa,GAAG4B,MAAM,CAAC5B,aAAa,CAAC,IAAI,IAAI,CAACA,aAAa;IAChE,IAAI,CAACZ,UAAU,CAACyC,IAAI,CAAC,IAAI,CAAC7B,aAAa,CAAC;EAC5C;EACA,IAAI8B,eAAeA,CAAC1B,YAAY,EAAE;IAC9B,MAAM2B,KAAK,GAAGH,MAAM,CAACxB,YAAY,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC4B,SAAS,IAAI,CAAC;IAC9E,IAAI,CAAC5B,YAAY,GAAG6B,IAAI,CAACC,GAAG,CAACH,KAAK,EAAE,IAAI,CAACC,SAAS,IAAI,CAAC,CAAC;EAC5D;EACA,IAAIG,eAAeA,CAAC9B,YAAY,EAAE;IAC9B,IAAI,CAACA,YAAY,GAAGuB,MAAM,CAACvB,YAAY,CAAC,IAAI,IAAI,CAACA,YAAY;EACjE;EACA,IAAI+B,iBAAiBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACnC,cAAc,GAAG0B,MAAM,CAACS,QAAQ,CAAC,IAAI,IAAI,CAACnC,cAAc;EACjE;EACA,IAAIoC,aAAaA,CAACC,MAAM,EAAE;IACtB,IAAI,CAAChD,UAAU,GAAGiD,OAAO,CAACD,MAAM,CAAC;EACrC;EACA,IAAIE,YAAYA,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC7C,SAAS,GAAG+B,MAAM,CAACc,KAAK,CAAC,IAAI,IAAI,CAAC7C,SAAS;EACpD;EACA,IAAI8C,aAAaA,CAACC,MAAM,EAAE;IACtB,IAAI,CAAC9C,UAAU,GAAG8B,MAAM,CAACgB,MAAM,CAAC,IAAI,IAAI,CAAC9C,UAAU;EACvD;EACA,IAAI+C,eAAeA,CAACN,MAAM,EAAE;IACxB,IAAI,CAACpC,YAAY,GAAGqC,OAAO,CAACD,MAAM,CAAC;EACvC;EACA,IAAIO,mBAAmBA,CAACP,MAAM,EAAE;IAC5B,IAAI,CAACtC,gBAAgB,GAAGuC,OAAO,CAACD,MAAM,CAAC;EAC3C;EACAQ,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1D,UAAU,EAAE;MACjB,IAAI,IAAI,CAACY,YAAY,EAAE;QACnB,IAAI,CAACX,gBAAgB,GAAG,IAAI,CAACK,SAAS,GAAG,CAAC;MAC9C,CAAC,MACI;QACD,IAAI,CAACL,gBAAgB,GAAG,CAAC;MAC7B;IACJ;IACA,IAAI,CAAC0D,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,6BAA6B,CAAC,CAAC;EACxC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrC,cAAc,CAACsC,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAAC,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC5C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC6C,iBAAiB,CAAC,CAAC;EAC5B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7C,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC4C,iBAAiB,CAAC,CAAC;EAC5B;EACAR,mBAAmBA,CAAA,EAAG;IAClB,MAAMU,aAAa,GAAG,IAAI,CAACC,aAAa,CAACD,aAAa;IACtD,QAAQ,IAAI,CAAC3D,QAAQ;MACjB,KAAK,OAAO;QACR,IAAI,CAACgB,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAGI,KAAK,IAAK,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC9E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,WAAW,EAAGI,KAAK,IAAK,IAAI,CAACG,cAAc,CAACH,KAAK,CAAC,CAAC,CAAC;QAC7R;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC/C,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,OAAO,EAAGI,KAAK,IAAK,IAAI,CAACI,WAAW,CAACJ,KAAK,CAAC,CAAC,CAAC;QAC1G;MACJ,KAAK,cAAc;QACf,IAAI,CAAC/C,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,OAAO,EAAGI,KAAK,IAAK,IAAI,CAACI,WAAW,CAACJ,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACS,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjF,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,WAAW,EAAGI,KAAK,IAAK,IAAI,CAACM,cAAc,CAACN,KAAK,CAAC,CAAC,CAAC;QACpR;MACJ,KAAK,OAAO;QACR,IAAI,CAAC/C,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,OAAO,EAAGI,KAAK,IAAK,IAAI,CAACO,YAAY,CAACP,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACS,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjF,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,WAAW,EAAGI,KAAK,IAAK,IAAI,CAACM,cAAc,CAACN,KAAK,CAAC,CAAC,CAAC;QACrR;MACJ,KAAK,cAAc;QACf,IAAI,CAAC/C,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAGI,KAAK,IAAK,IAAI,CAACQ,qBAAqB,CAACR,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAE,MAAM,IAAI,CAACa,qBAAqB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrF,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,WAAW,EAAGI,KAAK,IAAK,IAAI,CAACU,oBAAoB,CAACV,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,OAAO,EAAGI,KAAK,IAAK,IAAI,CAACW,gBAAgB,CAACX,KAAK,CAAC,CAAC,CAAC;IAC9Y;IACA,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;MACvB;MACA,IAAI,CAACc,cAAc,CAAC6C,IAAI,CAAC,IAAI,CAAC1E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,YAAY,EAAGI,KAAK,IAAK,IAAI,CAACY,YAAY,CAACZ,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,gBAAgB,EAAGI,KAAK,IAAK,IAAI,CAACY,YAAY,CAACZ,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5E,QAAQ,CAAC2E,MAAM,CAACH,aAAa,EAAE,cAAc,EAAGI,KAAK,IAAK,IAAI,CAACY,YAAY,CAACZ,KAAK,CAAC,CAAC,CAAC;IAC1S;IACA,IAAI,IAAI,CAACvE,UAAU,IAAI,IAAI,CAACY,YAAY,EAAE;MACtC,IAAI,CAACX,gBAAgB,GAAG,IAAI,CAACK,SAAS,GAAG,CAAC;IAC9C;EACJ;EACA2D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACvC,gBAAgB,IAAI,IAAI,CAACC,eAAe,EAAE;MAC/C,IAAI,CAACuC,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAAC/B,UAAU,CAAC,IAAI,CAAC;IACzB;EACJ;EACAA,UAAUA,CAACuD,KAAK,EAAE;IACd,IAAI,CAACjE,OAAO,GAAGiE,KAAK;IACpB,IAAI,CAACrF,YAAY,CAACuC,IAAI,CAAC8C,KAAK,CAAC;EACjC;EACA;AACJ;AACA;EACIC,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAE;IACvB,IAAI,CAACjE,eAAe,GAAGe,MAAM,CAACiD,IAAI,CAAC,IAAI,IAAI,CAAChE,eAAe;IAC3D,IAAI,CAACC,cAAc,GAAGc,MAAM,CAACkD,GAAG,CAAC,IAAI,IAAI,CAAChE,cAAc;IACxD,MAAMiE,CAAC,GAAG;MACNC,CAAC,EAAE,IAAI,CAACnE,eAAe;MACvBoE,CAAC,EAAE,IAAI,CAACnE;IACZ,CAAC;IACD,IAAI,CAACzB,YAAY,CAACwC,IAAI,CAACkD,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIL,YAAYA,CAACZ,KAAK,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAACtD,cAAc,IAAI,IAAI,CAACC,UAAU,EAAE;MACzC;IACJ;IACAqD,KAAK,GAAGoB,MAAM,CAACpB,KAAK,IAAIA,KAAK,CAAC,CAAC;IAC/B,MAAMqB,SAAS,GAAGlD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACmD,GAAG,CAAEtB,KAAK,CAACuB,UAAU,IAAI,CAACvB,KAAK,CAACwB,MAAM,EAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChF,IAAIH,SAAS,GAAG,CAAC,EAAE;MACf;MACA,IAAI,CAACxD,gBAAgB,GAAGM,IAAI,CAACmD,GAAG,CAAC,IAAI,CAACpF,aAAa,GAAG,IAAI,CAACE,cAAc,EAAE,IAAI,CAACG,YAAY,CAAC;IACjG,CAAC,MACI;MACD;MACA,IAAI,CAACsB,gBAAgB,GAAGM,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClC,aAAa,GAAG,IAAI,CAACE,cAAc,EAAE,IAAI,CAACE,YAAY,CAAC;IACjG;IACA,IAAI,CAACmF,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,qBAAqB,CAAC1B,KAAK,CAAC;IACjC;IACAA,KAAK,CAAC2B,WAAW,GAAG,KAAK,CAAC,CAAC;IAC3B,IAAI3B,KAAK,CAAC4B,cAAc,EAAE;MACtB5B,KAAK,CAAC4B,cAAc,CAAC,CAAC,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;EACI3B,eAAeA,CAACD,KAAK,EAAE;IACnB,IAAI,CAAC6B,MAAM,CAAC7B,KAAK,CAAC;EACtB;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC4B,OAAO,CAAC,CAAC;EAClB;EACA3B,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,CAAC0B,qBAAqB,CAAC1B,KAAK,CAAC;EACrC;EACA;AACJ;AACA;EACII,WAAWA,CAACJ,KAAK,EAAE;IACf,IAAI,IAAI,CAACtD,cAAc,EAAE;MACrB,IAAI,CAACoF,OAAO,CAAC,CAAC;IAClB,CAAC,MACI;MACD,IAAI,CAACD,MAAM,CAAC7B,KAAK,CAAC;IACtB;EACJ;EACA;AACJ;AACA;EACIO,YAAYA,CAACP,KAAK,EAAE;IAChB,IAAI,IAAI,CAACtD,cAAc,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACmF,MAAM,CAAC7B,KAAK,CAAC;IACtB;EACJ;EACAK,eAAeA,CAAA,EAAG;IACd,IAAI,CAACyB,OAAO,CAAC,CAAC;EAClB;EACAxB,cAAcA,CAACN,KAAK,EAAE;IAClB,IAAI,IAAI,CAACtD,cAAc,EAAE;MACrB,IAAI,CAACgF,qBAAqB,CAAC1B,KAAK,CAAC;IACrC;EACJ;EACA;AACJ;AACA;EACIQ,qBAAqBA,CAACR,KAAK,EAAE;IACzB,IAAI,IAAI,CAACtD,cAAc,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC,IAAI,CAACkF,MAAM,CAAC7B,KAAK,CAAC;IACtB;EACJ;EACAS,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC/D,cAAc,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC,IAAI,CAACmF,OAAO,CAAC,CAAC;IAClB;EACJ;EACApB,oBAAoBA,CAACV,KAAK,EAAE;IACxB,IAAI,IAAI,CAACtD,cAAc,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACzC,IAAI,CAAC+E,qBAAqB,CAAC1B,KAAK,CAAC;IACrC;EACJ;EACAW,gBAAgBA,CAACX,KAAK,EAAE;IACpB,IAAI,IAAI,CAACtD,cAAc,IAAI,IAAI,CAACC,UAAU,EAAE;MACxC,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACmF,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAI,IAAI,CAACpF,cAAc,EAAE;MAC1B,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACtB,iBAAiB,CAAC0G,YAAY,CAAC,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACF,MAAM,CAAC7B,KAAK,CAAC;IACtB;EACJ;EACA;AACJ;AACA;EACI6B,MAAMA,CAAC7B,KAAK,EAAE;IACV,IAAI,IAAI,CAACpD,OAAO,EAAE;MACd,IAAI,CAACF,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAC0C,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAC4C,OAAO,GAAG,OAAO;MACtB,IAAI,CAACN,qBAAqB,CAAC1B,KAAK,CAAC;MACjC,IAAI,CAAC3E,iBAAiB,CAAC0G,YAAY,CAAC,CAAC;IACzC;EACJ;EACAD,OAAOA,CAAA,EAAG;IACN,IAAI,CAACpF,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACsF,OAAO,GAAG,MAAM;IACrB,IAAI,CAAC3G,iBAAiB,CAAC0G,YAAY,CAAC,CAAC;EACzC;EACAL,qBAAqBA,CAAC1B,KAAK,EAAE;IACzB,MAAMiC,OAAO,GAAG9D,IAAI,CAACC,GAAG,CAACD,IAAI,CAACmD,GAAG,CAACtB,KAAK,CAACkC,OAAO,EAAE,IAAI,CAACvG,UAAU,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMwG,MAAM,GAAGhE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACmD,GAAG,CAACtB,KAAK,CAACoC,OAAO,EAAE,IAAI,CAACxG,WAAW,CAAC,EAAE,CAAC,CAAC;IACrE,IAAI,CAACkF,eAAe,CAACmB,OAAO,EAAEE,MAAM,CAAC;IACrC,IAAI,CAAC9C,6BAA6B,CAAC,CAAC;IACpC,IAAI,CAAChE,iBAAiB,CAAC0G,YAAY,CAAC,CAAC;EACzC;EACA1C,6BAA6BA,CAAA,EAAG;IAC5B,IAAIgD,WAAW,GAAG,CAAC;IACnB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAI,IAAI,CAAC7G,UAAU,IAAI,IAAI,CAACsB,eAAe,GAAG,CAAC,EAAE;MAC7CsF,WAAW,GAAG,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACxF,eAAe,GAAG,IAAI,CAAChB,SAAS,GAAG,CAAC;MACvEuG,UAAU,GAAG,IAAI,CAACE,OAAO,GAAG,IAAI,CAACxF,cAAc,GAAG,IAAI,CAAChB,UAAU,GAAG,CAAC;IACzE;IACA,IAAI,CAACyG,aAAa,GAAI,IAAI,CAAC1F,eAAe,GAAG,CAAC,IAAI,CAACP,MAAM,GAAI6F,WAAW;IACxE,IAAI,CAACK,YAAY,GAAI,IAAI,CAAC1F,cAAc,GAAG,CAAC,IAAI,CAACP,MAAM,GAAI6F,UAAU;EACzE;EACAlD,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACzD,UAAU,GAAG,IAAI,CAACgH,cAAc,CAAC/C,aAAa,CAAChB,KAAK;IACzD,IAAI,CAAChD,WAAW,GAAG,IAAI,CAAC+G,cAAc,CAAC/C,aAAa,CAACd,MAAM;IAC3D;IACA,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;MAClB,IAAI,CAACM,SAAS,GAAG,IAAI,CAACJ,UAAU;MAChC,IAAI,CAACK,UAAU,GAAG,IAAI,CAACJ,WAAW;MAClC,IAAI,CAAC2G,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACC,OAAO,GAAG,CAAC;IACpB;IACA;IACA,IAAI,CAACI,SAAS,GAAG,IAAI,CAACD,cAAc,CAAC/C,aAAa,CAACiD,qBAAqB,CAAC,CAAC,CAAC7B,GAAG;IAC9E,IAAI,CAAC8B,UAAU,GAAG,IAAI,CAACH,cAAc,CAAC/C,aAAa,CAACiD,qBAAqB,CAAC,CAAC,CAAC9B,IAAI;IAChF,IAAI,IAAI,CAACvD,SAAS,KAAKuF,SAAS,EAAE;MAC9B,IAAI,CAACvF,SAAS,GAAG,IAAI,CAACH,UAAU;IACpC;IACA,IAAI,IAAI,CAACP,eAAe,EAAE;MACtB,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACmH,aAAa,CAACpD,aAAa,CAACqD,YAAY;MAC9D,IAAI,CAACnH,UAAU,GAAG,IAAI,CAACkH,aAAa,CAACpD,aAAa,CAACsD,aAAa;MAChE,IAAI,CAAChF,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAE,IAAI,CAACzC,UAAU,GAAG,IAAI,CAACE,SAAS,EAAI,IAAI,CAACD,WAAW,GAAG,IAAI,CAACE,UAAW,CAAC;MACnG;MACA,IAAI,CAACQ,YAAY,GAAG6B,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9B,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC4B,SAAS,IAAI,CAAC,CAAC;MACzE,IAAI,CAACuD,cAAc,CAAC,CAAC;IACzB;EACJ;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC0B,cAAc,GAAI,IAAI,CAACtH,SAAS,GAAG,IAAI,CAACK,aAAc;IAC3D,IAAI,CAACkH,eAAe,GAAI,IAAI,CAACtH,UAAU,GAAG,IAAI,CAACI,aAAc;IAC7D,IAAI,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC2G,cAAc,GAAG,IAAI,CAACxH,UAAU,IAAI,IAAI,CAACA,UAAU;IACvE,IAAI,CAACc,MAAM,GAAG,CAAC,IAAI,CAAC2G,eAAe,GAAG,IAAI,CAACxH,WAAW,IAAI,IAAI,CAACA,WAAW;EAC9E;AACJ;AACAV,qBAAqB,CAACwC,cAAc,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,CAAC;AACnGxC,qBAAqB,CAACmI,IAAI,YAAAC,8BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFrI,qBAAqB,EAA/BhB,EAAE,CAAAsJ,iBAAA,CAA+CtJ,EAAE,CAACuJ,SAAS,GAA7DvJ,EAAE,CAAAsJ,iBAAA,CAAwEtJ,EAAE,CAACwJ,iBAAiB;AAAA,CAA4C;AAClPxI,qBAAqB,CAACyI,IAAI,kBAD8EzJ,EAAE,CAAA0J,iBAAA;EAAAC,IAAA,EACJ3I,qBAAqB;EAAA4I,SAAA;EAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADnB/J,EAAE,CAAAiK,WAAA,CAAAxJ,GAAA;MAAFT,EAAE,CAAAiK,WAAA,CAAAvJ,GAAA;MAAFV,EAAE,CAAAiK,WAAA,CAAAtJ,GAAA;IAAA;IAAA,IAAAoJ,EAAA;MAAA,IAAAG,EAAA;MAAFlK,EAAE,CAAAmK,cAAA,CAAAD,EAAA,GAAFlK,EAAE,CAAAoK,WAAA,QAAAJ,GAAA,CAAArE,aAAA,GAAAuE,EAAA,CAAAG,KAAA;MAAFrK,EAAE,CAAAmK,cAAA,CAAAD,EAAA,GAAFlK,EAAE,CAAAoK,WAAA,QAAAJ,GAAA,CAAAvB,cAAA,GAAAyB,EAAA,CAAAG,KAAA;MAAFrK,EAAE,CAAAmK,cAAA,CAAAD,EAAA,GAAFlK,EAAE,CAAAoK,WAAA,QAAAJ,GAAA,CAAAlB,aAAA,GAAAoB,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAApH,aAAA;IAAAG,YAAA;IAAAE,WAAA;IAAAI,gBAAA;IAAAG,eAAA;IAAAK,eAAA;IAAAC,iBAAA;IAAAE,aAAA;IAAAG,YAAA;IAAAE,aAAA;IAAAE,eAAA;IAAAC,mBAAA;IAAA9B,OAAA;IAAAC,SAAA;EAAA;EAAAsH,OAAA;IAAAnJ,UAAA;IAAAC,YAAA;IAAAC,YAAA;EAAA;EAAAkJ,QAAA,GAAFxK,EAAE,CAAAyK,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+BAAAf,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF/J,EAAE,CAAA+K,cAAA,eAC41C,CAAC,eAAD,CAAC;MAD/1C/K,EAAE,CAAAgL,UAAA,kBAAAC,mDAAA;QAAA,OACohDjB,GAAA,CAAAzE,kBAAA,CAAmB,CAAC;MAAA,CAAC,CAAC;MAD5iDvF,EAAE,CAAAkL,YAAA,CACijD,CAAC;MADpjDlL,EAAE,CAAA+K,cAAA,YACk+D,CAAC,eAAD,CAAC;MADr+D/K,EAAE,CAAAgL,UAAA,kBAAAG,mDAAA;QAAA,OAC+qEnB,GAAA,CAAAvE,iBAAA,CAAkB,CAAC;MAAA,CAAC,CAAC;MADtsEzF,EAAE,CAAAkL,YAAA,CACi9E,CAAC,CAAD,CAAC,CAAD,CAAC;IAAA;IAAA,IAAAnB,EAAA;MADp9E/J,EAAE,CAAAoL,WAAA,UAAApB,GAAA,CAAAvI,UAAA,MAC6yC,CAAC,WAAAuI,GAAA,CAAAtI,WAAA,MAAD,CAAC;MADhzC1B,EAAE,CAAAqL,SAAA,EACm8C,CAAC;MADt8CrL,EAAE,CAAAsL,UAAA,QAAAtB,GAAA,CAAAhH,OACm8C,CAAC,UAAAgH,GAAA,CAAA/G,SAAD,CAAC,QAAA+G,GAAA,CAAA7G,UAAA,EADt8CnD,EAAE,CAAAuL,aACm8C,CAAC;MADt8CvL,EAAE,CAAAqL,SAAA,EAC+uD,CAAC;MADlvDrL,EAAE,CAAAoL,WAAA,YAAApB,GAAA,CAAAlC,OAC+uD,CAAC,QAAAkC,GAAA,CAAA1B,OAAA,MAAD,CAAC,SAAA0B,GAAA,CAAA3B,QAAA,MAAD,CAAC,UAAA2B,GAAA,CAAAnI,SAAA,MAAD,CAAC,WAAAmI,GAAA,CAAAlI,UAAA,MAAD,CAAC,kBAAAkI,GAAA,CAAAxI,gBAAA,MAAD,CAAC;MADlvDxB,EAAE,CAAAsL,UAAA,YAAFtL,EAAE,CAAAwL,eAAA,KAAA5K,GAAA,EAAAoJ,GAAA,CAAAzI,UAAA,CACqsD,CAAC;MADxsDvB,EAAE,CAAAqL,SAAA,EACivE,CAAC;MADpvErL,EAAE,CAAAoL,WAAA,YAAApB,GAAA,CAAAlC,OACivE,CAAC,QAAAkC,GAAA,CAAAxB,YAAA,MAAD,CAAC,SAAAwB,GAAA,CAAAzB,aAAA,MAAD,CAAC,UAAAyB,GAAA,CAAAf,cAAA,MAAD,CAAC,WAAAe,GAAA,CAAAd,eAAA,MAAD,CAAC;MADpvElJ,EAAE,CAAAsL,UAAA,QAAAtB,GAAA,CAAAhH,OACmlE,CAAC,UAAAgH,GAAA,CAAA/G,SAAD,CAAC,QAAA+G,GAAA,CAAA1G,SAAA,EADtlEtD,EAAE,CAAAuL,aACmlE,CAAC;IAAA;EAAA;EAAAE,YAAA,GAAi2BlL,EAAE,CAACmL,OAAO;EAAAC,MAAA;AAAA,EAA2D;AACpmG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFwG5L,EAAE,CAAA6L,iBAAA,CAEf7K,qBAAqB,EAAc,CAAC;IACnH2I,IAAI,EAAEzJ,SAAS;IACf4L,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAElB,QAAQ,EAAE,myCAAmyC;MAAEc,MAAM,EAAE,CAAC,yaAAya;IAAE,CAAC;EACjxD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhC,IAAI,EAAE3J,EAAE,CAACuJ;IAAU,CAAC,EAAE;MAAEI,IAAI,EAAE3J,EAAE,CAACwJ;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE7D,aAAa,EAAE,CAAC;MAChIgE,IAAI,EAAExJ,SAAS;MACf2L,IAAI,EAAE,CAAC,eAAe,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEvD,cAAc,EAAE,CAAC;MACjBkB,IAAI,EAAExJ,SAAS;MACf2L,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAElD,aAAa,EAAE,CAAC;MAChBa,IAAI,EAAExJ,SAAS;MACf2L,IAAI,EAAE,CAAC,eAAe,EAAE;QAAEE,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE5K,UAAU,EAAE,CAAC;MACbuI,IAAI,EAAEvJ;IACV,CAAC,CAAC;IAAEiB,YAAY,EAAE,CAAC;MACfsI,IAAI,EAAEvJ;IACV,CAAC,CAAC;IAAEkB,YAAY,EAAE,CAAC;MACfqI,IAAI,EAAEvJ;IACV,CAAC,CAAC;IAAE8C,aAAa,EAAE,CAAC;MAChByG,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzI,YAAY,EAAE,CAAC;MACfsG,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEvI,WAAW,EAAE,CAAC;MACdoG,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEnI,gBAAgB,EAAE,CAAC;MACnBgG,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEhI,eAAe,EAAE,CAAC;MAClB6F,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE3H,eAAe,EAAE,CAAC;MAClBwF,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE1H,iBAAiB,EAAE,CAAC;MACpBuF,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAExH,aAAa,EAAE,CAAC;MAChBqF,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAErH,YAAY,EAAE,CAAC;MACfkF,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEnH,aAAa,EAAE,CAAC;MAChBgF,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEjH,eAAe,EAAE,CAAC;MAClB8E,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEhH,mBAAmB,EAAE,CAAC;MACtB6E,IAAI,EAAEtJ,KAAK;MACXyL,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE9I,OAAO,EAAE,CAAC;MACV2G,IAAI,EAAEtJ;IACV,CAAC,CAAC;IAAE4C,SAAS,EAAE,CAAC;MACZ0G,IAAI,EAAEtJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4L,kBAAkB,CAAC;AAEzBA,kBAAkB,CAAC9C,IAAI,YAAA+C,2BAAA7C,CAAA;EAAA,YAAAA,CAAA,IAAwF4C,kBAAkB;AAAA,CAAkD;AACnLA,kBAAkB,CAACE,IAAI,kBAjEiFnM,EAAE,CAAAoM,gBAAA;EAAAzC,IAAA,EAiEMsC;AAAkB,EAAqG;AACvOA,kBAAkB,CAACI,IAAI,kBAlEiFrM,EAAE,CAAAsM,gBAAA;EAAAC,OAAA,GAkEoC,CAClI/L,YAAY,CACf;AAAA,EAAI;AACb;EAAA,QAAAoL,SAAA,oBAAAA,SAAA,KArEwG5L,EAAE,CAAA6L,iBAAA,CAqEfI,kBAAkB,EAAc,CAAC;IAChHtC,IAAI,EAAErJ,QAAQ;IACdwL,IAAI,EAAE,CAAC;MACCU,YAAY,EAAE,CACVxL,qBAAqB,CACxB;MACDuL,OAAO,EAAE,CACL/L,YAAY,CACf;MACDiM,OAAO,EAAE,CACLzL,qBAAqB;IAE7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,qBAAqB,EAAEiL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}