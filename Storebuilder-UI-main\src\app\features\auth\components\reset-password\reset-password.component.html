<section class="reset-password">
  <img class="image-desktop" src="assets/images/resetPassword.svg"/>
  <div class="tab-container">
    <div>
      <p class=" text-center reset-heading">
        {{ "resetPassword.resetPassword" | translate }}
      </p>
      <p class="text-center send-otp">
        {{ "resetPassword.authenticateYourself" | translate }}
      </p>
    </div>
    <div class="tab-navigation">
      <button
        (click)="switchTab('mobileNumber')"
        [class.active]="activeTab === 'mobileNumber'"
        class="tab-button"
        type="button">
        <div class="content">
          <span class="text">
             {{ "contactUs.mobileNumber" | translate }}
          </span>
        </div>
      </button>
      <button
        (click)="switchTab('email')"
        [class.active]="activeTab === 'email'"
        class="tab-button"
        type="button">
        <div class="content">
          <span class="text">
             {{ "contactUs.email" | translate }}
          </span>
        </div>
      </button>
    </div>
    <div class="tab-content p-fluid p-grid">
      <!-- Phone Input Component -->
      <div *ngIf="activeTab === 'mobileNumber'" class="tab-pane">
        <form [formGroup]="resetPasswordForm" autocomplete="new-password">
          <div class="p-fluid p-grid">
            <div class="mt-3">
              <div [class.error]="resetPasswordForm.controls.phoneNumber.touched &&
                                resetPasswordForm.controls.phoneNumber.invalid"
                   class="p-float-label w-full">
                <form #f="ngForm" [formGroup]="resetPasswordForm">
                  <ngx-intl-tel-input
                    #phoneInput2
                    [cssClass]="'custom contact-input-phone'"
                    [customPlaceholder]="customPlaceHolder"
                    [enableAutoCountrySelect]="true"
                    [enablePlaceholder]="true"
                    [maxLength]="phoneInputLength"
                    [numberFormat]="PhoneNumberFormat.National"
                    [phoneValidation]="false"
                    [preferredCountries]="preferredCountries"
                    [searchCountryField]="[SearchCountryField.Iso2, SearchCountryField.Name]"
                    [searchCountryFlag]="true"
                    [selectFirstCountry]="false"
                    [selectedCountryISO]="CustomCountryISO"
                    [separateDialCode]="true"
                    formControlName="phoneNumber"
                    name="phone">
                  </ngx-intl-tel-input>
                </form>
                <label class="contact-label" for="mobileNumber">
                  {{ "contactUs.mobileNumber" | translate }}*
                </label>
              </div>
            </div>
            <button (click)="resetPassword()"
                    [disabled]="!resetPasswordForm.controls.phoneNumber.value || !resetPasswordForm.controls.phoneNumber.valid"
                    [label]="'resetPassword.next' | translate"
                    class="p-field p-col-12 mb-5 mt-7 width-100 font-size-14 second-btn"
                    pButton
                    type="button">
            </button>
          </div>
        </form>

      </div>
      <div *ngIf="activeTab === 'email'" class="tab-pane">

        <!-- Email Input Component -->
        <app-email-input
          (emailChange)="onEmailChange($event)"
          (validationChange)="onEmailValidationChange($event)"
          [emailLabel]="floatingLabel ? 'auth.registerPassword.email' : 'auth.registerPassword.emailLabel' | translate"
          [emailRequired]="'false'"
          [floatingLabel]="floatingLabel"
          [labelColor]="labelColor"
          class="customClass"
        >
        </app-email-input>

        <button (click)="resetPassword()"
                [disabled]="!email || !isEmailValid"
                [label]="'resetPassword.next' | translate"
                class="p-field p-col-12 mb-5 mt-7 width-100 font-size-14 second-btn"
                pButton
                type="button">
        </button>
      </div>
    </div>

  </div>
  <img class="image-mobile" src="assets/images/resetPassword.svg"/>
</section>
