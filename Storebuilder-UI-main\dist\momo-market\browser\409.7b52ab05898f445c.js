"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[409],{7409:(b,f,c)=>{c.r(f),c.d(f,{MerchantsModule:()=>I});var u=c(6075),d=c(6814),t=c(5879),p=c(864),h=c(6663);const g=function(i){return{"shadow-4 active":i}};function x(i,a){if(1&i){const o=t.EpF();t.ynx(0),t.TgZ(1,"a",7),t.NdJ("click",function(){const r=t.CHM(o).$implicit,s=t.oxw();return t.KtG(s.onScrollHref(r.key))}),t._uU(2),t.qZA(),t.BQk()}if(2&i){const o=a.$implicit,n=t.oxw();t.xp6(1),t.Q6J("ngClass",t.VKq(2,g,n.search===o)),t.xp6(1),t.hij(" ",o.key," ")}}function v(i,a){if(1&i){const o=t.EpF();t.TgZ(0,"div",12)(1,"a",13),t.NdJ("click",function(){const r=t.CHM(o).$implicit,s=t.oxw(2);return t.KtG(s.ShopProduct(r.shopId,r.shopName))}),t._uU(2),t.qZA()()}if(2&i){const o=a.$implicit;t.xp6(2),t.Oqu(o.shopName)}}function y(i,a){if(1&i&&(t.ynx(0),t.TgZ(1,"div",8)(2,"div",9)(3,"div",10),t._uU(4),t.qZA()(),t.TgZ(5,"div",9)(6,"div",1),t.YNc(7,v,3,1,"div",11),t.qZA()()(),t.BQk()),2&i){const o=a.$implicit;t.xp6(1),t.Q6J("id",o.key),t.xp6(3),t.hij(" ",o.key," "),t.xp6(3),t.Q6J("ngForOf",o.shopList)}}let C=(()=>{class i{shopService;viewportScroller;route;search="";criteria=[];merchants=[];shopList=[];newArr;screenWidth;isMobileView=!1;onResize(o){this.screenWidth=o.target.innerWidth,this.isMobileView=this.screenWidth<=768}constructor(o,n,e){this.shopService=o,this.viewportScroller=n,this.route=e}ngOnInit(){this.getMerchants()}getMerchants(){this.shopService.getAllShopSorted().subscribe(o=>{o.success&&o.data&&(this.shopList=o.data),this.criteria=this.newArr})}onScrollHref(o){this.viewportScroller.setOffset(this.isMobileView?[0,195]:[0,170]),this.viewportScroller.scrollToAnchor(o)}ShopProduct(o,n){this.route.navigate(["/merchants/merchant-product",o,n])}static \u0275fac=function(n){return new(n||i)(t.Y36(p.dD),t.Y36(d.EM),t.Y36(u.F0))};static \u0275cmp=t.Xpm({type:i,selectors:[["app-index"]],hostBindings:function(n,e){1&n&&t.NdJ("resize",function(s){return e.onResize(s)},!1,t.Jf7)},decls:10,vars:5,consts:[[1,"merchants","mt-5","px-0"],[1,"grid"],[1,"col-12","col-md-6","flex","md:justify-content-start"],[1,"font-size-28","bold-font"],[1,"mt-5","flex","flex-row","justify-content-start","lg:justify-content-evenly","flex-wrap"],[4,"ngFor","ngForOf"],[1,"mt-8","mobile-all-merchant"],["fragment","letter.key",1,"main-color","p-2","cursor-pointer","font-size-20","bold-font","uppercase",3,"ngClass","click"],[1,"grid","mt-5",3,"id"],[1,"col-12"],[1,"bold-font","font-size-20"],["class","col-12 col-md-4",4,"ngFor","ngForOf"],[1,"col-12","col-md-4"],[1,"font-size-15","main-color","medium-font","my-1","no-underline",2,"cursor","pointer",3,"click"]],template:function(n,e){1&n&&(t.TgZ(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3),t._uU(4),t.ALo(5,"translate"),t.qZA()()(),t.TgZ(6,"div",4),t.YNc(7,x,3,4,"ng-container",5),t.qZA(),t.TgZ(8,"div",6),t.YNc(9,y,8,3,"ng-container",5),t.qZA()()),2&n&&(t.xp6(4),t.hij(" ",t.lcZ(5,3,"merchant.merchants")," "),t.xp6(3),t.Q6J("ngForOf",e.shopList),t.xp6(2),t.Q6J("ngForOf",e.shopList))},dependencies:[d.mk,d.sg,h.X$],styles:[".merchants[_ngcontent-%COMP%]{min-height:100%}.merchants[_ngcontent-%COMP%]   .search[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:5px;outline:none;box-shadow:0 2px 4px #00000003;color:#a3a3a3;font-size:12px;font-family:var(--regular-font)}.m-top[_ngcontent-%COMP%]{margin-top:10rem!important}@media screen and (max-width: 768px){.m-top[_ngcontent-%COMP%]{margin-top:17rem!important}.mobile-all-merchant[_ngcontent-%COMP%]{padding:0 50px;margin-top:0!important}}"]})}return i})();var P=c(9147),w=c(7875);function _(i,a){if(1&i&&(t.TgZ(0,"a",7),t._UZ(1,"app-mtn-product-card",8),t.qZA()),2&i){const o=a.$implicit,n=t.oxw();t.xp6(1),t.Q6J("currency",n.currency)("product",o)}}function M(i,a){1&i&&(t.TgZ(0,"div",9),t._uU(1),t.ALo(2,"translate"),t.qZA()),2&i&&(t.xp6(1),t.hij(" ",t.lcZ(2,1,"ErrorMessages.noProducts")," "))}const O=[{path:"",component:C},{path:"merchant-product/:id/:name",component:(()=>{class i{productService;store;reviewsService;activatedRoute;$gtmService;products=[];reviews;shopName;currency={};completed=!1;constructor(o,n,e,r,s){this.productService=o,this.store=n,this.reviewsService=e,this.activatedRoute=r,this.$gtmService=s}ngOnInit(){let o;this.activatedRoute.paramMap.subscribe(e=>{o=e.get("id"),this.shopName=e.get("name"),this.$gtmService.pushPageView("merchant",this.shopName),this.getShopProducts(o)})}getShopProducts(o){this.productService.GetProductsByShop(o).subscribe({next:n=>{this.completed=!0,this.products=[],n.success?this.handleData(n.data):this.products=[],this.store.set("loading",!1)},error:n=>{this.store.set("loading",!1)}})}handleData(o){o?.forEach(n=>{let e,r=n?.productVariances?.find(m=>m.isDefault);if(r)e=r;else{let m=n?.productVariances?.find(Z=>!Z.soldOut);e=m||n?.productVariances[0]}let s=[];e?.productFeaturesList&&(s=e?.productFeaturesList[0]?.featureList);let l={productId:n?.id,productName:n?.name,isLiked:n?.isLiked,priceValue:e?.price,priceId:e?.priceId,salePriceValue:e?.salePrice,currencyCode:n?.currencyCode,masterImageUrl:n?.masterImageUrl??e.images[0],thumbnailImages:e?.thumbnailImages,soldOut:e?.soldOut,rate:e?.rate,count:e?.count??0,salePercent:e?.salePrice?100-e?.salePrice/e?.price*100:0,shopId:n.shopId,specProductId:e.specProductId,channelId:n.channelId??"1",isHot:s?.includes(1),isNew:s?.includes(2),isBest:s?.includes(3),quantity:e.quantity,proSchedulingId:e.proSchedulingId,stockPerSKU:e.stockPerSKU,stockStatus:e.stockStatus,sku:e?.sku,skuAutoGenerated:e.skuAutoGenerated,badgesList:n?.badgesList};l.salePriceValue&&(l.salePercent=100-l.salePriceValue/l.priceValue*100),this.products.push(l)})}static \u0275fac=function(n){return new(n||i)(t.Y36(p.M5),t.Y36(p.d6),t.Y36(p.Y0),t.Y36(u.gz),t.Y36(P.J))};static \u0275cmp=t.Xpm({type:i,selectors:[["app-merchant-products"]],decls:9,vars:3,consts:[[1,"category-products-page"],[1,""],[1,"col-12","col-md-6","flex"],[1,"font-size-22","bold-font"],[1,"my-3","flex","flex-row","justify-content-start","flex-wrap"],["class","slide","class","mt-2 mx-2 mb-5 merchant-sub-products",4,"ngFor","ngForOf"],["class","productEmpty my-5 flex flex-row justify-content-center",4,"ngIf"],[1,"mt-2","mx-2","mb-5","merchant-sub-products"],[2,"width","100%",3,"currency","product"],[1,"productEmpty","my-5","flex","flex-row","justify-content-center"]],template:function(n,e){1&n&&(t.TgZ(0,"section",0)(1,"div")(2,"div",1)(3,"div",2)(4,"div",3),t._uU(5),t.qZA()(),t.TgZ(6,"div",4),t.YNc(7,_,2,2,"a",5),t.qZA(),t.YNc(8,M,3,3,"div",6),t.qZA()()()),2&n&&(t.xp6(5),t.hij(" ",e.shopName," "),t.xp6(2),t.Q6J("ngForOf",e.products),t.xp6(1),t.Q6J("ngIf",0===(null==e.products?null:e.products.length)&&1==e.completed))},dependencies:[d.sg,d.O5,w.Y,h.X$],styles:[".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.productEmpty[_ngcontent-%COMP%]{font-family:var(--medium-font)!important}.mtn-card-all[_ngcontent-%COMP%]{flex:0 0 18%;width:18%}@media only screen and (max-width: 767px){.mtn-card-all[_ngcontent-%COMP%]{flex:0 0 45%;width:45%}.category-products-page[_ngcontent-%COMP%]{margin-top:110px!important}}@media only screen and (min-width: 768px) and (max-width: 1200px){a[_ngcontent-%COMP%]{width:25%;flex:0 0 25%}}.merchant-sub-products[_ngcontent-%COMP%]{max-width:243px}@media only screen and (max-width: 767px){.merchant-sub-products[_ngcontent-%COMP%]{max-width:180px}}@media only screen and (min-width: 768px) and (max-width: 1200px){.merchant-sub-products[_ngcontent-%COMP%]{max-width:288px}}"]})}return i})()}];var S=c(6574);let I=(()=>{class i{static \u0275fac=function(n){return new(n||i)};static \u0275mod=t.oAB({type:i});static \u0275inj=t.cJS({imports:[d.ez,u.Bz.forChild(O),S.p,h.aw]})}return i})()}}]);