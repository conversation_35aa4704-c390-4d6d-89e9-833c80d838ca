import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class TabSessionService {
  private readonly tabId: string;

  constructor(@Inject(PLATFORM_ID) private platformId: any) {
    this.tabId = this.generateTabId();
  }

  /**
   * Generate a unique tab identifier
   */
  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Store data with tab-specific key in sessionStorage
   */
  setTabData<T>(key: string, data: T): void {
    if (isPlatformBrowser(this.platformId)) {
      const tabKey = `${key}_${this.tabId}`;
      sessionStorage.setItem(tabKey, JSON.stringify({
        data,
        timestamp: Date.now(),
        tabId: this.tabId
      }));
    }
  }

  /**
   * Retrieve data from tab-specific sessionStorage
   */
  getTabData<T>(key: string): T | null {
    if (isPlatformBrowser(this.platformId)) {
      const tabKey = `${key}_${this.tabId}`;
      const storedData = sessionStorage.getItem(tabKey);
      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          return parsed.data;
        } catch (e) {
          console.error('Error parsing tab-specific data:', e);
        }
      }
    }
    return null;
  }

  /**
   * Remove tab-specific data
   */
  removeTabData(key: string): void {
    if (isPlatformBrowser(this.platformId)) {
      const tabKey = `${key}_${this.tabId}`;
      sessionStorage.removeItem(tabKey);
    }
  }

  /**
   * Get current tab ID
   */
  getTabId(): string {
    return this.tabId;
  }

  /**
   * Clean up all tab-specific data
   */
  cleanupTabData(): void {
    if (isPlatformBrowser(this.platformId)) {
      const keys = Object.keys(sessionStorage);
      keys.forEach(key => {
        if (key.endsWith(`_${this.tabId}`)) {
          sessionStorage.removeItem(key);
        }
      });
    }
  }

  /**
   * Store order data specifically for this tab
   */
  setOrderData(orderData: any): void {
    this.setTabData('orderData', orderData);
  }

  /**
   * Get order data specifically for this tab
   */
  getOrderData(): any {
    return this.getTabData('orderData');
  }

  /**
   * Store OrderId specifically for this tab
   */
  setOrderId(orderId: string): void {
    this.setTabData('orderId', orderId);
  }

  /**
   * Get OrderId specifically for this tab
   */
  getOrderId(): string | null {
    return this.getTabData<string>('orderId');
  }
}
