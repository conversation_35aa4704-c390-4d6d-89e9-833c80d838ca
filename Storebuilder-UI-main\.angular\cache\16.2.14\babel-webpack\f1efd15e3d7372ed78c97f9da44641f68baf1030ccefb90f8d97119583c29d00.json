{"ast": null, "code": "import { ElementRef, PLATFORM_ID } from '@angular/core';\nimport { NavigationEnd } from \"@angular/router\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { ConfigurationKeys } from \"@core/enums\";\nimport { TenantRecords } from \"@core/interface\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-cookie-service\";\nimport * as i7 from \"@core/services/device-detection.service\";\nimport * as i8 from \"primeng/toast\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"../../../../shared/components/loader/loader.component\";\nconst _c0 = [\"scroll\"];\nfunction MainLandingComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass GuidGenerator {\n  static newGuid() {\n    return uuidv4();\n  }\n}\nexport class MainLandingComponent {\n  constructor(store, cartService, meta, messageService, mainDataService, translate, router, cookieService, authService, authTokenService, cd, tenantService, appDataService, languageService, platformId, commonService, deviceDetectionService) {\n    this.store = store;\n    this.cartService = cartService;\n    this.meta = meta;\n    this.messageService = messageService;\n    this.mainDataService = mainDataService;\n    this.translate = translate;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authService = authService;\n    this.authTokenService = authTokenService;\n    this.cd = cd;\n    this.tenantService = tenantService;\n    this.appDataService = appDataService;\n    this.languageService = languageService;\n    this.platformId = platformId;\n    this.commonService = commonService;\n    this.deviceDetectionService = deviceDetectionService;\n    this.lang = 'en';\n    this.storeNames = [];\n    this.display = true;\n    this.loading = false;\n    this.seoTitle = '';\n    this.seoDesc = '';\n    this.isShop = false;\n    this.isRefreshTokenCalled = false;\n    this.isRenderApplication = false;\n    this.cartListCount = 0;\n    this.cartListData = [];\n    this.country = '';\n    translate.addLangs(['en', 'ar']);\n    this.themeApplied = false;\n    translate.setDefaultLang('en');\n  }\n  ngOnInit() {\n    const sessionId = window.localStorage.getItem('sessionId');\n    if (!sessionId) {\n      localStorage.setItem('sessionId', GuidGenerator.newGuid());\n    }\n    this.triggerInitialCalls();\n    this.getUserIpAddress();\n    this.getDeviceInfo();\n  }\n  getUserIpAddress() {\n    this.authService.getUserIPAddress().subscribe({\n      next: res => {\n        if (res?.data?.ipAddress) {\n          const userIp = res?.data?.ipAddress.replace('::ffff:', '') || '';\n          this.store.set('userIP', userIp);\n        }\n      },\n      error: err => {\n        console.warn('Error in Fetching User IP Address', err);\n      }\n    });\n  }\n  getDeviceInfo() {\n    const device_info = {\n      deviceType: this.deviceDetectionService.getDeviceType(),\n      deviceId: this.deviceDetectionService.getDeviceId()\n    };\n    this.store.set('deviceInfo', device_info);\n  }\n  triggerInitialCalls() {\n    this.routeToTop();\n    this.createStores();\n    // this.getCategories()\n    // this.lang = this.store.get('lang') || 'en';\n    // remove extra language call\n    // this.setLangSettings();\n    this.getMainData();\n  }\n  routeToTop() {\n    this.router.events.subscribe(event => {\n      if (!(event instanceof NavigationEnd)) {\n        return;\n      }\n      window.scrollTo(0, 0);\n    });\n  }\n  createStores() {\n    this.storeNames = [{\n      name: 'timeInterval',\n      data: null,\n      localStore: true\n    }, {\n      name: 'orderData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'transactionData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'userPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'mainData',\n      data: null,\n      localStore: true\n    }, {\n      name: 'allCountryTenants',\n      data: null,\n      localStore: true\n    }, {\n      name: 'tenantId',\n      data: null,\n      localStore: true\n    }, {\n      name: 'shipmentCost',\n      data: null,\n      localStore: true\n    }, {\n      name: 'isShop',\n      data: null,\n      localStorage: true\n    }, {\n      name: 'currency',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryCode',\n      data: null,\n      localStore: true\n    }, {\n      name: 'countryPhone',\n      data: null,\n      localStore: true\n    }, {\n      name: 'profile',\n      data: null,\n      localStore: true\n    }, {\n      name: 'categories',\n      data: [],\n      localStore: true\n    }, {\n      name: 'notifications',\n      data: null,\n      localStore: false\n    }, {\n      name: 'cartProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'favouritesProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'compareProducts',\n      data: [],\n      localStore: true\n    }, {\n      name: 'cartProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'favouritesProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'compareProductSuccess',\n      data: null,\n      localStore: false\n    }, {\n      name: 'allowedFeature',\n      data: null,\n      localStore: false\n    }, {\n      name: 'userIP',\n      data: null,\n      localStore: false\n    }, {\n      name: 'deviceInfo',\n      data: null,\n      localStore: false\n    }, {\n      name: 'checkoutData',\n      data: {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      },\n      localStore: true\n    }, {\n      name: 'search',\n      data: '',\n      localStore: false\n    }, {\n      name: 'loading',\n      data: false,\n      localStore: true\n    }, {\n      name: 'verificationCode',\n      data: '',\n      localStore: true\n    }, {\n      name: 'sessionId',\n      data: '',\n      localStore: true\n    }];\n    /*Create Dynamic BehaviorSubject at Store*/\n    this.storeNames.forEach(item => {\n      this.store.createNewStore(item.name, item.data, item.localStore);\n    });\n  }\n  getConfigurationDecimal() {\n    const configuration = this.appDataService.configuration;\n    this.isRenderApplication = true;\n    const findAndSetLocalStorage = key => {\n      const record = configuration.records?.find(x => x.key === key);\n      if (record) localStorage.setItem(key, record.value);\n    };\n    findAndSetLocalStorage(ConfigurationKeys.CurrencyDecimal);\n    findAndSetLocalStorage(ConfigurationKeys.CountryPhone);\n    findAndSetLocalStorage(ConfigurationKeys.PhoneLength);\n    findAndSetLocalStorage(ConfigurationKeys.PhoneNumberMask);\n    findAndSetLocalStorage(ConfigurationKeys.Currency);\n    findAndSetLocalStorage(ConfigurationKeys.EmailRequired);\n    findAndSetLocalStorage(ConfigurationKeys.DisableCents);\n    findAndSetLocalStorage(ConfigurationKeys.CustomerAddressLandmarkRequired);\n    localStorage.setItem('emailRequired', 'false');\n    // localStorage.setItem('disableCent', 'false');\n    // Commented out extra API calls\n    // this.getCart();\n    this.getAllCountryTenants();\n    this.getCategories();\n    this.isRenderApplication = true;\n  }\n  getCart() {\n    let cartData = {\n      sessionId: localStorage.getItem('sessionId') ?? ''\n    };\n    let applyTo = localStorage.getItem('apply-to');\n    if (applyTo && applyTo != '') {\n      cartData['applyTo'] = applyTo;\n    }\n    this.cartService.getCart(cartData).subscribe({\n      next: res => {\n        this.cartListCount = 0;\n        this.cartListData = [];\n        if (res?.data?.records?.length) {\n          this.cartListCount = 0;\n          if (res.data.records[0].cartDetails.length) {\n            this.cartListCount = res.data.records[0].cartDetails.length;\n            this.cartListData = res.data.records[0].cartDetails;\n          }\n          if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n            this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n            this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n          }\n          this.mainDataService.setCartLenghtData(this.cartListCount);\n          this.mainDataService.setCartItemsData(this.cartListData);\n        } else {\n          this.mainDataService.setCartLenghtData(0);\n          this.mainDataService.setCartItemsData([]);\n        }\n      },\n      error: err => {},\n      complete: () => {}\n    });\n  }\n  getMainData() {\n    this.getInitialDataConfig();\n  }\n  getInitialDataConfig() {\n    const initialData = this.appDataService.initialData;\n    this.getConfigurationDecimal();\n    this.handleFeatureByTenant(initialData.featureByTenantRes);\n    this.setMainData(initialData.result.records);\n    this.setShopDetails(initialData.isShop, initialData.shopProductSetting);\n    this.tenantService.setHeader(true);\n  }\n  handleFeatureByTenant(featureByTenantRes) {\n    if (featureByTenantRes.length) {\n      localStorage.setItem('Allowed-feature', JSON.stringify(featureByTenantRes));\n      const featureList = JSON.parse(localStorage.getItem('Allowed-feature') ?? '[]');\n      const isSellerHub = featureList.find(item => item.portalName === 'Seller-Hub');\n      localStorage.setItem('isSellerHub', isSellerHub ? 'seller-hub' : 'not-seller-hub');\n    } else {\n      localStorage.setItem('isSellerHub', 'not-seller-hub');\n    }\n    this.store.set('allowedFeature', featureByTenantRes);\n  }\n  setMainData(resultRecords) {\n    if (resultRecords !== undefined) {\n      this.store.set('mainData', resultRecords);\n      Object.entries(resultRecords).forEach(([subkey, value]) => {\n        const key = parseInt(subkey);\n        const record = resultRecords[key];\n        if (record.key === 'AppTheme') {\n          const dynamicStyle = record.displayName;\n          if (dynamicStyle != null && dynamicStyle !== '') {\n            const dynamicStyleObj = JSON.parse(dynamicStyle);\n            this.applyStyle(dynamicStyleObj);\n          }\n        } else {\n          this.themeApplied = true;\n          this.cd.detectChanges();\n        }\n      });\n    }\n  }\n  setShopDetails(isShop, shopProductSetting) {\n    this.isShop = isShop;\n    if (this.isShop) {\n      this.seoTitle = shopProductSetting.seoTitle;\n      this.seoDesc = shopProductSetting.seoDescription;\n      this.meta.updateTag({\n        name: this.seoTitle,\n        content: this.seoDesc\n      });\n    } else {\n      this.meta.updateTag({\n        name: 'Description',\n        content: 'MarketPlace, Buy, products'\n      });\n    }\n  }\n  getCategories() {\n    const categories = this.appDataService.categories;\n    if (categories.records != undefined) {\n      this.store.set('categories', categories.records);\n      localStorage.setItem('allCategories', JSON.stringify(categories.records));\n    }\n  }\n  getAllCountryTenants() {\n    const tenants = this.appDataService.tenants;\n    if (tenants.records != undefined) {\n      let tenantId = localStorage.getItem('tenantId');\n      let data = tenants.records;\n      let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n      localStorage.setItem('isoCode', arr?.isoCode);\n      this.store.set('allCountryTenants', tenants.records);\n    }\n  }\n  appendCssPropertyValue(key, value, dynamicStyleObj, sub) {\n    if (value) {\n      Object.entries(value).forEach(([subKey, subSubValue]) => {\n        if (value[subKey]) {\n          if (isPlatformBrowser(this.platformId)) {\n            document.documentElement.style.setProperty('--' + subKey, value[subKey]);\n          }\n        } else if (subKey && dynamicStyleObj && dynamicStyleObj[key]) {\n          this.appendCssPropertyValue(subKey, dynamicStyleObj[key][subKey], dynamicStyleObj, true);\n        }\n      });\n    }\n  }\n  applyStyle(dynamicStyleObj) {\n    Object.entries(dynamicStyleObj).forEach(([key, value]) => {\n      if (value && dynamicStyleObj[key] && (dynamicStyleObj[key]?.length > 2 || Object.entries(dynamicStyleObj[key])?.length >= 1)) {\n        if (dynamicStyleObj[key] != null && (dynamicStyleObj[key][0]?.length == 1 || Number(dynamicStyleObj[key][0]))) {\n          if (isPlatformBrowser(this.platformId)) {\n            document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n          }\n        } else {\n          this.appendCssPropertyValue(key, dynamicStyleObj[key], dynamicStyleObj, false);\n        }\n      } else {\n        if (isPlatformBrowser(this.platformId)) {\n          document.documentElement.style.setProperty('--' + key, dynamicStyleObj[key]);\n        }\n      }\n    });\n    this.themeApplied = true;\n    this.cd.detectChanges();\n  }\n  scrollToTop() {\n    this.scroll.nativeElement.scrollTop = 0;\n  }\n  signOut() {\n    this.commonService.logOut();\n    this.router.navigate(['/login']);\n    this.getMainData();\n  }\n}\nMainLandingComponent.ɵfac = function MainLandingComponent_Factory(t) {\n  return new (t || MainLandingComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.Meta), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.CookieService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.TenantService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.LanguageService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i7.DeviceDetectionService));\n};\nMainLandingComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: MainLandingComponent,\n  selectors: [[\"app-main-landing\"]],\n  viewQuery: function MainLandingComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5, ElementRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroll = _t.first);\n    }\n  },\n  decls: 4,\n  vars: 2,\n  consts: [[1, \"main-content\"], [\"position\", \"top-right\", \"sticky\", \"true\", 3, \"autoZIndex\"], [4, \"ngIf\"]],\n  template: function MainLandingComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"app-mtn-loader\")(2, \"p-toast\", 1);\n      i0.ɵɵtemplate(3, MainLandingComponent_ng_container_3_Template, 2, 0, \"ng-container\", 2);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"autoZIndex\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isRenderApplication);\n    }\n  },\n  dependencies: [i8.Toast, i9.NgIf, i5.RouterOutlet, i10.LoaderComponent],\n  styles: [\".container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n}\\n\\n.page[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  min-height: 100vh;\\n}\\n\\n  .p-toast-top-right {\\n  top: 7rem !important;\\n  z-index: 10000 !important;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n    .p-toast-top-right {\\n    top: 0.8rem !important;\\n    right: 0px !important;\\n    left: 0px !important;\\n    z-index: 10000 !important;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    min-height: 55vh;\\n  }\\n}\\n.main-content[_ngcontent-%COMP%]:has(div.not-found) {\\n  min-height: 40vh !important;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}