{"ast": null, "code": "export var PaymentMethodEnum;\n(function (PaymentMethodEnum) {\n  PaymentMethodEnum[PaymentMethodEnum[\"Card\"] = 0] = \"Card\";\n  PaymentMethodEnum[PaymentMethodEnum[\"Wallet\"] = 1] = \"Wallet\";\n  PaymentMethodEnum[PaymentMethodEnum[\"Both\"] = 2] = \"Both\";\n  PaymentMethodEnum[PaymentMethodEnum[\"NoChannelAllowed\"] = 4] = \"NoChannelAllowed\";\n})(PaymentMethodEnum || (PaymentMethodEnum = {}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}