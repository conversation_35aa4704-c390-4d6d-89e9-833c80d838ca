{"ast": null, "code": "import { environment } from '@environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TransactionDetailsService {\n  http;\n  baseUrl;\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = `${environment.apiEndPoint}/Payment/TransactionDetails`;\n  }\n  createTransactionDetail(data) {\n    return this.http.post(`${this.baseUrl}/CreateTransactionDetails`, data);\n  }\n  static ɵfac = function TransactionDetailsService_Factory(t) {\n    return new (t || TransactionDetailsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TransactionDetailsService,\n    factory: TransactionDetailsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}