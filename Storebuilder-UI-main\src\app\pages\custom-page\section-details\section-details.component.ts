import { Component, inject } from '@angular/core';
import { MenuItem } from 'primeng/api/menuitem';
import { Router, ActivatedRoute } from '@angular/router';
import { AppDataService } from '@core/services';
import { CustomPageService } from '../custom-page.service';

@Component({
  selector: 'app-section-details',
  templateUrl: './section-details.component.html',
  styleUrls: ['./section-details.component.scss']
})
export class SectionDetailsComponent {
  router: Router = inject(Router);
  route: ActivatedRoute = inject(ActivatedRoute);
  appDataService: AppDataService = inject(AppDataService);
  customPageService: CustomPageService = inject(CustomPageService);

  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  breadItems: MenuItem[] = [];
  navbarData: any;
  sectionName: string = '';
  sectionId: string = '0';
  title: string = '';
  products: any[] = [];
  currentPage: number = 1;
  pageSize: number = 10;
  isLoading: boolean = false;
  isLastPage: boolean = false;
  hasMoreProducts = true;

  ngOnInit(): void {
    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');
    this.routeData();
    this.loadProducts();
  }

  routeData() {
    this.route.paramMap.subscribe(params => {
      const tenant = params.get('tenant');
      this.title = params.get('title') || '';
      this.sectionName = params.get('sectionName') || '';
      this.sectionId = params.get('sectionId') || '';
      this.breadItems = [
        { label: this.title, routerLink: [`/${tenant}/${this.title}`] },
        { label: this.sectionName, routerLink: [`/${tenant}/${this.title}/${this.sectionName}`] }
      ];
    });
  }

  loadProducts(): void {

    this.isLoading = true;

    this.customPageService.getProducts(this.currentPage, this.pageSize, this.sectionId).subscribe(
      (response) => {
        const newProducts = response.data.records[0].products;
        if (newProducts.length < this.pageSize || !response.data.hasNext) {
          this.isLastPage = true; 
          this.hasMoreProducts = false; 
        }
        newProducts.forEach((record: any) => {
          this.addProductFromLoadData(record);
        })
        this.currentPage++; 
        this.isLoading = false; 

        // this.cdRef.detectChanges();
      },
      (error) => {
        console.error('Error fetching products:', error);
        this.isLoading = false;
        this.currentPage--;
      }
    );
  }

  addProductFromLoadData(record: any) {
    let selectedVariance;
    const productDetails = record.productDetails;

    let defaultVariant = record?.productDetails?.productVariances?.find((variant: any) => variant.isDefault)
    if (defaultVariant) {
      selectedVariance = defaultVariant;
    } else {
      let approvedVariant = record?.productDetails?.productVariances?.find((variant: any) => variant.soldOut);
      if (approvedVariant) {
        selectedVariance = approvedVariant;

      } else {
        selectedVariance = record?.productDetails?.productVariances[0];
      }
    }
    let features = [];
    if (selectedVariance?.productFeaturesList) {
      features = selectedVariance?.productFeaturesList[0]?.featureList;
    }

    let product: any = {
      badges: productDetails.badgesList,
      productId: productDetails?.id,
      productName: productDetails?.name,
      isLiked: productDetails?.isLiked,
      priceValue: selectedVariance?.price,
      salePriceValue: selectedVariance?.salePrice,
      priceId: selectedVariance?.priceId,
      currencyCode: productDetails?.currencyCode,
      masterImageUrl: productDetails?.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : null),
      thumbnailImages: selectedVariance?.thumbnailImages,
      soldOut: selectedVariance?.soldOut,
      rate: selectedVariance?.rate,
      count: selectedVariance?.count ?? 0,
      specProductId: selectedVariance.specProductId,
      channelId: productDetails.channelId ?? '1',
      salePercent: selectedVariance?.salePrice ? 100 - (selectedVariance?.salePrice / selectedVariance?.price * 100) : 0,
      shopId: productDetails.shopId,
      isHot: features?.includes(1),
      isNew: features?.includes(2),
      isBest: features?.includes(3),
      quantity: selectedVariance.quantity,
      proSchedulingId: selectedVariance.proSchedulingId,
      stockPerSKU: selectedVariance.stockPerSKU,
      stockStatus: selectedVariance.stockStatus,
      sku: selectedVariance?.sku,
      skuAutoGenerated: selectedVariance.skuAutoGenerated
    }
    this.products.push(product)
  }



  onScrolledToEnd(): void {
    if (this.isLoading || !this.hasMoreProducts) {
      return;
    }

    this.isLoading = true;
    this.loadProducts();
  }

}