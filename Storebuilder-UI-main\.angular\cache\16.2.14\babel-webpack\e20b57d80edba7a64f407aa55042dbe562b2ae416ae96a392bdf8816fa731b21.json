{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr, faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faLine } from '@fortawesome/free-brands-svg-icons';\nimport { faSms, faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus } from '@fortawesome/free-solid-svg-icons';\nimport * as i1 from '@fortawesome/angular-fontawesome';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nconst shareIcons = [faFacebookF, faTwitter, faLinkedinIn, faPinterestP, faRedditAlien, faTumblr, faWhatsapp, faViber, faVk, faFacebookMessenger, faTelegramPlane, faMix, faXing, faSms, faEnvelope, faCheck, faPrint, faExclamation, faLink, faEllipsisH, faMinus, faLine];\nclass ShareIconsModule {\n  constructor(iconLibrary) {\n    iconLibrary.addIcons(...shareIcons);\n  }\n  static #_ = this.ɵfac = function ShareIconsModule_Factory(t) {\n    return new (t || ShareIconsModule)(i0.ɵɵinject(i1.FaIconLibrary));\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ShareIconsModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [FontAwesomeModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShareIconsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FontAwesomeModule]\n    }]\n  }], function () {\n    return [{\n      type: i1.FaIconLibrary\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ShareIconsModule };\n//# sourceMappingURL=ngx-sharebuttons-icons.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}