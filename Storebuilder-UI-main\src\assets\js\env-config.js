window.APP_SETTINGS = {
  production: "true",
  apiEndPoint: "'https://auth-api.mtn.yetanotherdomain.xyz'",
  securityApiUrl: "'https://uat-apps.paysky.io/sso-authenticator/connect/token'",
  featureProductsLimit: "100",
  platform: "'storecloud'",
  isStoreCloud: "false",
  isMarketPlace: "true",
  testing: "false",
  excludingTenants: "[0]",
  defaultTenant: "'1'",
  referer: "1",
  merchantURL: "'https://merchant-admin.mtn.yetanotherdomain.xyz'",
  marketPlaceHostName: ""store.mtn.yetanotherdomain.xyz"",
  client_id: "''",
  client_secret: "'82C0CFC2-F45A-469A-B124-DDBE23A8C13B'",
  scope: "'cc.access'",
}
