{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport jwt_decode from \"jwt-decode\";\nimport * as CryptoJS from 'crypto-js';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"ngx-cookie-service\";\nimport * as i7 from \"primeng/password\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"ngx-intl-tel-input-gg\";\nconst _c0 = function (a0) {\n  return {\n    marginTop: a0\n  };\n};\nfunction VerifyUserComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"div\", 4)(3, \"p\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"span\", 10)(14, \"label\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-password\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyUserComponent_ng_container_1_Template_p_password_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.password = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_container_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.checkMobileExist());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_container_1_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onBack());\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(23, _c0, ctx_r0.screenWidth <= 768 ? ctx_r0.isMobileLayout ? \"5rem\" : \"220px\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 11, \"newUser.verifyUser.password\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(8, 13, \"newUser.verifyUser.passwordConfirm\"), \"\\u00A0\", i0.ɵɵpipeBind1(9, 15, ctx_r0.title), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(16, 17, \"signIn.password\"), \" *\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false)(\"ngModel\", ctx_r0.password);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.password.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 19, \"newUser.verifyUser.continue\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 21, \"newUser.verifyUser.cancel\"), \" \");\n  }\n}\nfunction VerifyUserComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 4)(2, \"p\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 10)(13, \"label\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p-password\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function VerifyUserComponent_ng_template_2_Template_p_password_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.password = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function VerifyUserComponent_ng_template_2_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.checkMobileExist());\n    });\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 9, \"newUser.verifyUser.password\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(7, 11, \"newUser.verifyUser.passwordConfirm\"), \"\\u00A0\", i0.ɵɵpipeBind1(8, 13, ctx_r2.title), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 15, \"signIn.password\"), \" *\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false)(\"ngModel\", ctx_r2.password);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(18, 17, \"newUser.verifyUser.continue\"))(\"disabled\", ctx_r2.password.length === 0);\n  }\n}\nexport class VerifyUserComponent {\n  otpService;\n  translate;\n  messageService;\n  router;\n  store;\n  auth;\n  authTokenService;\n  permissionService;\n  platformId;\n  $gaService;\n  cookieService;\n  loaderService;\n  phoneNumber;\n  countryPhoneNumber = \"\";\n  countryPhoneCode = \"\";\n  phoneLength = 12;\n  phoneInputLength = 12;\n  tagName = GaLocalActionEnum;\n  isGoogleAnalytics = false;\n  password = '';\n  cookieValue;\n  decoded;\n  isPrimary;\n  title = '';\n  screenWidth = window.innerWidth;\n  isMobileLayout = false;\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(otpService, translate, messageService, router, store, auth, authTokenService, permissionService, platformId, $gaService, cookieService, loaderService) {\n    this.otpService = otpService;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.auth = auth;\n    this.authTokenService = authTokenService;\n    this.permissionService = permissionService;\n    this.platformId = platformId;\n    this.$gaService = $gaService;\n    this.cookieService = cookieService;\n    this.loaderService = loaderService;\n    let userData = localStorage.getItem('profile') ?? '{}';\n    userData = JSON.parse(userData);\n    if (userData?.mobileNumber) this.phoneNumber = userData.mobileNumber;\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.isPrimary = JSON.parse(localStorage.getItem(\"isPrimary\") ?? '');\n    if (this.isPrimary) {\n      this.title = this.translate.instant('newUser.verifyUser.confirmChangeDefaultNumber');\n    } else {\n      this.title = this.translate.instant('newUser.verifyUser.addNewNumber');\n    }\n  }\n  onBack() {\n    this.router.navigate(['account/details']);\n  }\n  triggerEnterPasswordAnalytics(isPasswordCorrect = '') {\n    this.$gaService.event(this.tagName.CLICK_ON_ENTER_PASSWORD, '', 'CONFIRM_PASSWORD_TO_ADD_NEW_NUMBER', 1, true, {\n      \"submission_outcome\": isPasswordCorrect\n    });\n  }\n  checkMobileExist() {\n    this.store.set(\"loading\", true);\n    this.otpService.userPassword = this.password;\n    if (this.phoneNumber != null && this.phoneNumber != \"\" && this.password && this.password !== '') {\n      this.auth.login({\n        username: this.phoneNumber.replace('-', ''),\n        password: this.password\n      }).subscribe({\n        next: res => {\n          if (res?.success) {\n            this.store.set('profile', res.data);\n            this.store.set('userPhone', res.data.mobileNumber);\n            this.store.set('timeInterval', new Date().getTime());\n            let token = res.data.authToken.replace('bearer ', '');\n            this.triggerEnterPasswordAnalytics('Pass');\n            this.decoded = jwt_decode(token);\n            let days = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n            const dateNow = new Date();\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\n            let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();\n            localStorage.setItem('auth_enc', encryptedMessage);\n            this.cookieService.set('authToken', token, {\n              expires: dateNow,\n              path: '/',\n              sameSite: 'Strict'\n            });\n            this.cookieValue = this.cookieService.get('authToken');\n            this.authTokenService.authTokenSet(this.cookieValue);\n            localStorage.removeItem('isGuest');\n            this.loaderService.hide();\n            if (res?.data?.currency) this.store.set('currency', res.data.currency);\n            localStorage.setItem('refreshToken', res.data.refreshToken);\n            this.store.set('refreshToken', res.data.refreshToken);\n            this.router.navigate(['/account/verify-mobile']);\n          } else {\n            this.triggerEnterPasswordAnalytics('failed');\n            this.messageService.add({\n              severity: 'error',\n              summary: '',\n              detail: res.message\n            });\n            localStorage.setItem('isGuest', 'true');\n          }\n        }\n      });\n    } else {\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\n      });\n      this.store.set(\"loading\", false);\n    }\n  }\n  omit_special_char(event) {\n    let key;\n    key = event.charCode;\n    return key > 47 && key < 58;\n  }\n  static ɵfac = function VerifyUserComponent_Factory(t) {\n    return new (t || VerifyUserComponent)(i0.ɵɵdirectiveInject(i1.RegisterService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.CookieService), i0.ɵɵdirectiveInject(i1.LoaderService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: VerifyUserComponent,\n    selectors: [[\"app-verify-user\"]],\n    hostBindings: function VerifyUserComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function VerifyUserComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"register\"], [4, \"ngIf\", \"ngIfElse\"], [\"desktopView\", \"\"], [1, \"content-container\", 3, \"ngStyle\"], [1, \"grid\", \"justify-content-center\", \"margin-x-100\"], [1, \"col-12\", \"content-container__heading-title\", \"text-center\", \"m-0\", \"py-0\", \"mx-4\"], [1, \"col-12\", \"content-container__heading-sub-title\", \"mb-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\"], [1, \"p-fluid\", \"p-grid\"], [1, \"p-field\", \"p-col-12\"], [1, \"\"], [1, \"pass-label\"], [\"autocomplete\", \"off\", 1, \"customClass\", 3, \"toggleMask\", \"feedback\", \"ngModel\", \"ngModelChange\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"content-container__primary-btn\", \"mt-4\", \"gap-2\", 3, \"disabled\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"second-btn\", \"content-container__secondary-btn\", \"mt-3\", \"gap-2\", 3, \"click\"], [1, \"content-container\", \"my-3\"], [1, \"col-12\", \"bold-font\", \"text-center\", \"font-size-28\", \"m-0\", \"py-0\", \"mx-4\", 2, \"line-height\", \"1.5\"], [1, \"col-12\", \"text-center\", \"no-underline\", \"font-size-16\", \"mb-3\", \"default-number\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"border-round\", \"bg-white\", \"shadow-1\", \"px-5\", \"pt-6\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"mb-5\", \"mt-7\", \"width-100\", \"font-size-14\", \"second-btn\", 3, \"label\", \"disabled\", \"click\"]],\n    template: function VerifyUserComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0);\n        i0.ɵɵtemplate(1, VerifyUserComponent_ng_container_1_Template, 24, 25, \"ng-container\", 1);\n        i0.ɵɵtemplate(2, VerifyUserComponent_ng_template_2_Template, 19, 19, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(3);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.screenWidth < 768)(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: [i7.Password, i8.NgControlStatus, i9.NgIf, i9.NgStyle, i10.ButtonDirective, i8.NgModel, i11.NativeElementInjectorDirective, i2.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n#phone-number[_ngcontent-%COMP%] {\\n  padding: 0.9rem;\\n  width: 100%;\\n}\\n\\n.register[_ngcontent-%COMP%] {\\n  margin-top: 90px;\\n}\\n\\n.pass-label[_ngcontent-%COMP%] {\\n  color: #323232;\\n  position: absolute;\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  z-index: 3;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n}\\n\\n  .p-password-input {\\n  height: 60px !important;\\n  width: 100%;\\n  border-radius: 5px 5px 0px 0px;\\n  opacity: 1;\\n  border: none !important;\\n  border-bottom: 1px solid #b9b9b9 !important;\\n  padding-left: 10px;\\n  padding-right: 10px;\\n  padding-top: 20px;\\n  background-color: #f5f5f5 !important;\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n}\\n\\n.default-number[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font) !important;\\n  color: #a3a3a3;\\n}\\n\\n@media only screen and (max-width: 767px) {\\n  .register[_ngcontent-%COMP%] {\\n    margin-top: 122px !important;\\n  }\\n  .content-container__heading-title[_ngcontent-%COMP%] {\\n    color: #000;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 20px;\\n    font-style: normal;\\n    font-weight: 700;\\n    line-height: normal;\\n  }\\n  .content-container__heading-sub-title[_ngcontent-%COMP%] {\\n    color: #6E6E6E;\\n    text-align: center;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 400;\\n    line-height: normal;\\n  }\\n  .content-container[_ngcontent-%COMP%]   .pass-label[_ngcontent-%COMP%] {\\n    color: #323232;\\n    font-family: \\\"main-medium\\\";\\n    font-size: 12px;\\n    font-style: normal;\\n    font-weight: 500;\\n    line-height: normal;\\n  }\\n  .content-container__primary-btn[_ngcontent-%COMP%] {\\n    background: var(--primary, #204E6E);\\n    border-radius: 8px !important;\\n    color: var(--Gray-00, #FFF);\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    letter-spacing: 0.168px;\\n    place-content: center;\\n    padding: 12px 24px;\\n    height: 48px;\\n  }\\n  .content-container__secondary-btn[_ngcontent-%COMP%] {\\n    background: transparent !important;\\n    border-radius: 8px !important;\\n    color: var(--primary, #204E6E);\\n    font-family: \\\"main-medium\\\";\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    letter-spacing: 0.168px;\\n    place-content: center;\\n    padding: 12px 24px;\\n    height: 48px;\\n  }\\n    .p-password-input {\\n    border-bottom: none !important;\\n    border-radius: 8px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "jwt_decode", "CryptoJS", "isPlatformBrowser", "GaLocalActionEnum", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "VerifyUserComponent_ng_container_1_Template_p_password_ngModelChange_17_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "password", "VerifyUserComponent_ng_container_1_Template_button_click_18_listener", "ctx_r5", "checkMobileExist", "VerifyUserComponent_ng_container_1_Template_button_click_21_listener", "ctx_r6", "onBack", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "screenWidth", "isMobileLayout", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "title", "length", "VerifyUserComponent_ng_template_2_Template_p_password_ngModelChange_16_listener", "_r8", "ctx_r7", "VerifyUserComponent_ng_template_2_Template_button_click_17_listener", "ctx_r9", "ctx_r2", "VerifyUserComponent", "otpService", "translate", "messageService", "router", "store", "auth", "authTokenService", "permissionService", "platformId", "$gaService", "cookieService", "loaderService", "phoneNumber", "countryPhoneNumber", "countryPhoneCode", "phoneLength", "phoneInputLength", "tagName", "isGoogleAnalytics", "cookieValue", "decoded", "isPrimary", "window", "innerWidth", "onResize", "event", "constructor", "userData", "localStorage", "getItem", "JSON", "parse", "mobileNumber", "ngOnInit", "hasPermission", "instant", "navigate", "triggerEnterPasswordAnalytics", "isPasswordCorrect", "CLICK_ON_ENTER_PASSWORD", "set", "userPassword", "login", "username", "replace", "subscribe", "next", "res", "success", "data", "Date", "getTime", "token", "authToken", "days", "exp", "toFixed", "dateNow", "setDate", "getDate", "parseInt", "encryptedMessage", "AES", "encrypt", "toString", "setItem", "expires", "path", "sameSite", "get", "authTokenSet", "removeItem", "hide", "currency", "refreshToken", "add", "severity", "summary", "detail", "message", "omit_special_char", "key", "charCode", "ɵɵdirectiveInject", "i1", "RegisterService", "i2", "TranslateService", "i3", "MessageService", "i4", "Router", "StoreService", "AuthService", "AuthTokenService", "PermissionService", "i5", "GoogleAnalyticsService", "i6", "CookieService", "LoaderService", "selectors", "hostBindings", "VerifyUserComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵtemplate", "VerifyUserComponent_ng_container_1_Template", "VerifyUserComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\verify-user\\verify-user.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\account\\components\\verify-user\\verify-user.component.html"], "sourcesContent": ["import {Component, HostListener, Inject, OnInit, PLATFORM_ID} from '@angular/core';\r\nimport {Router} from '@angular/router';\r\nimport {TranslateService} from '@ngx-translate/core';\r\nimport {MessageService} from 'primeng/api';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport {CookieService} from \"ngx-cookie-service\";\r\nimport * as CryptoJS from 'crypto-js';\r\nimport {\r\n  LoaderService,\r\n  AuthService,\r\n  RegisterService,\r\n  AuthTokenService,\r\n  StoreService,\r\n  PermissionService\r\n} from \"@core/services\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\r\nimport {GoogleAnalyticsService } from 'ngx-google-analytics';\r\n\r\n\r\n@Component({\r\n  selector: 'app-verify-user',\r\n  templateUrl: './verify-user.component.html',\r\n  styleUrls: ['./verify-user.component.scss']\r\n})\r\nexport class VerifyUserComponent implements OnInit {\r\n\r\n  phoneNumber: string | undefined;\r\n  countryPhoneNumber: string = \"\";\r\n  countryPhoneCode: string = \"\";\r\n  phoneLength: number = 12;\r\n  phoneInputLength: number = 12;\r\n  tagName: any = GaLocalActionEnum;\r\n  isGoogleAnalytics:boolean= false\r\n  password: any = '';\r\n\r\n  cookieValue: any;\r\n  decoded: any;\r\n  isPrimary: boolean;\r\n  title: string = ''\r\n  screenWidth:any=window.innerWidth;\r\n  isMobileLayout: boolean = false;\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event?: any) {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.screenWidth = window.innerWidth\r\n    }\r\n  }\r\n  constructor(private otpService: RegisterService,\r\n              private translate: TranslateService,\r\n              private messageService: MessageService,\r\n              private router: Router,\r\n              private store: StoreService,\r\n              private auth: AuthService,\r\n              private authTokenService: AuthTokenService,\r\n              private permissionService: PermissionService,\r\n              @Inject(PLATFORM_ID) private platformId: any,\r\n              private $gaService: GoogleAnalyticsService,\r\n              private cookieService: CookieService, private loaderService: LoaderService) {\r\n    let userData: any = localStorage.getItem('profile') ?? '{}';\r\n    userData = JSON.parse(userData);\r\n    if (userData?.mobileNumber) this.phoneNumber = userData.mobileNumber;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout')\r\n    this.isPrimary = JSON.parse(localStorage.getItem(\"isPrimary\") ?? '');\r\n    if(this.isPrimary){\r\n      this.title = this.translate.instant('newUser.verifyUser.confirmChangeDefaultNumber');\r\n    }else{\r\n      this.title = this.translate.instant('newUser.verifyUser.addNewNumber');\r\n    }\r\n  }\r\n  onBack(){\r\n    this.router.navigate(['account/details']);\r\n  }\r\n\r\n  triggerEnterPasswordAnalytics(isPasswordCorrect:string ='') {\r\n      this.$gaService.event(\r\n        this.tagName.CLICK_ON_ENTER_PASSWORD,\r\n        '',\r\n        'CONFIRM_PASSWORD_TO_ADD_NEW_NUMBER',\r\n        1,\r\n        true,\r\n        {\r\n          \"submission_outcome\": isPasswordCorrect\r\n        }\r\n      );\r\n  }\r\n  \r\n  checkMobileExist() {\r\n    this.store.set(\"loading\", true);\r\n    this.otpService.userPassword = this.password;\r\n\r\n    if (this.phoneNumber != null && this.phoneNumber != \"\" && this.password && this.password !== '') {\r\n      this.auth\r\n        .login({\r\n          username: this.phoneNumber.replace('-', ''),\r\n          password: this.password,\r\n        }).subscribe({\r\n        next: (res: any) => {\r\n          if (res?.success) {\r\n            this.store.set('profile', res.data);\r\n            this.store.set('userPhone', res.data.mobileNumber);\r\n            this.store.set('timeInterval', new Date().getTime());\r\n            let token = res.data.authToken.replace('bearer ', '');\r\n            this.triggerEnterPasswordAnalytics('Pass');\r\n            this.decoded = jwt_decode(token);\r\n\r\n            let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(\r\n              0\r\n            );\r\n\r\n            const dateNow = new Date();\r\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n            let encryptedMessage = CryptoJS.AES.encrypt(\r\n              token,\r\n              'paysky'\r\n            ).toString();\r\n            localStorage.setItem('auth_enc', encryptedMessage);\r\n            this.cookieService.set('authToken', token, {\r\n              expires: dateNow,\r\n              path: '/',\r\n              sameSite: 'Strict',\r\n            });\r\n            this.cookieValue = this.cookieService.get('authToken');\r\n\r\n            this.authTokenService.authTokenSet(this.cookieValue);\r\n            localStorage.removeItem('isGuest');\r\n\r\n            this.loaderService.hide();\r\n            if (res?.data?.currency)\r\n              this.store.set('currency', res.data.currency);\r\n\r\n            localStorage.setItem('refreshToken', res.data.refreshToken);\r\n            this.store.set('refreshToken', res.data.refreshToken);\r\n            this.router.navigate(['/account/verify-mobile']);\r\n          } else {\r\n            this.triggerEnterPasswordAnalytics('failed');\r\n            this.messageService.add({\r\n              severity: 'error',\r\n              summary: '',\r\n              detail: res.message,\r\n            });\r\n            localStorage.setItem('isGuest', 'true');\r\n          }\r\n        }\r\n      });\r\n\r\n\r\n    } else {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        summary: this.translate.instant('ErrorMessages.fetchError'),\r\n        detail: this.translate.instant('ErrorMessages.mobileRequired')\r\n      });\r\n      this.store.set(\"loading\", false);\r\n    }\r\n  }\r\n\r\n  omit_special_char(event: any) {\r\n    let key;\r\n    key = event.charCode;\r\n    return (key > 47 && key < 58);\r\n  }\r\n\r\n}\r\n", "<section class=\"register\">\r\n  <ng-container *ngIf=\"screenWidth <768 else desktopView\">\r\n    <div class=\"content-container\" [ngStyle]=\"{marginTop: (screenWidth <= 768 ? (isMobileLayout ? '5rem' : '220px') : '') }\">\r\n      <div class=\"grid justify-content-center margin-x-100 \">\r\n        <p class=\"col-12 content-container__heading-title text-center m-0 py-0 mx-4\" >\r\n          {{ \"newUser.verifyUser.password\" | translate }}\r\n        </p>\r\n        <div class=\"col-12 content-container__heading-sub-title mb-3\">\r\n          {{ \"newUser.verifyUser.passwordConfirm\" | translate }}&nbsp;{{this.title | translate}}\r\n        </div>\r\n        <div class=\"col-12 col-md-8 col-lg-6\">\r\n          <div class=\"p-fluid p-grid\">\r\n            <div class=\"p-field p-col-12\">\r\n              <span class=\"\">\r\n                <label class=\"pass-label\">{{ \"signIn.password\" | translate }} *</label>\r\n                <p-password [toggleMask]=\"true\" [feedback]=\"false\" autocomplete=\"off\" [(ngModel)]=\"password\"\r\n                            class=\"customClass\"></p-password>\r\n              </span>\r\n            </div>\r\n            <button (click)=\"checkMobileExist()\" [disabled]=\"password.length === 0\"\r\n                    class=\"w-full second-btn content-container__primary-btn mt-4 gap-2\" pButton type=\"button\">\r\n              {{\"newUser.verifyUser.continue\" | translate}}\r\n            </button>\r\n\r\n            <button (click)=\"onBack()\"\r\n                    class=\"w-full second-btn content-container__secondary-btn mt-3 gap-2\" pButton type=\"button\">\r\n              {{\"newUser.verifyUser.cancel\" | translate}}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n  <ng-template #desktopView >\r\n    <div class=\"content-container my-3\">\r\n      <div class=\"grid justify-content-center margin-x-100 \">\r\n        <p class=\"col-12 bold-font text-center font-size-28 m-0 py-0 mx-4\" style=\"line-height: 1.5\">\r\n          {{ \"newUser.verifyUser.password\" | translate }}\r\n        </p>\r\n        <div class=\"col-12 text-center no-underline font-size-16 mb-3 default-number\">\r\n          {{ \"newUser.verifyUser.passwordConfirm\" | translate }}&nbsp;{{this.title | translate}}\r\n        </div>\r\n        <div class=\"col-12 col-md-8 col-lg-6 border-round bg-white shadow-1 px-5 pt-6\">\r\n          <div class=\"p-fluid p-grid\">\r\n            <div class=\"p-field p-col-12\">\r\n              <span class=\"\">\r\n                <label class=\"pass-label\">{{ \"signIn.password\" | translate }} *</label>\r\n                <p-password [toggleMask]=\"true\" [feedback]=\"false\" autocomplete=\"off\" [(ngModel)]=\"password\"\r\n                  class=\"customClass\"></p-password>\r\n              </span>\r\n            </div>\r\n\r\n            <button [label]=\"'newUser.verifyUser.continue' | translate\" [disabled]=\"password.length === 0\"\r\n              class=\"p-field p-col-12 mb-5 mt-7 width-100 font-size-14 second-btn\" pButton (click)=\"checkMobileExist()\"\r\n              type=\"button\"></button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-template>\r\n</section>\r\n"], "mappings": "AAAA,SAAiDA,WAAW,QAAO,eAAe;AAIlF,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,KAAKC,QAAQ,MAAM,WAAW;AASrC,SAAQC,iBAAiB,QAAO,iBAAiB;AACjD,SAASC,iBAAiB,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;ICflEC,EAAA,CAAAC,uBAAA,GAAwD;IACtDD,EAAA,CAAAE,cAAA,aAAyH;IAGnHF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,aAA8D;IAC5DF,EAAA,CAAAG,MAAA,GACF;;;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAsC;IAIJF,EAAA,CAAAG,MAAA,IAAqC;;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvEJ,EAAA,CAAAE,cAAA,sBACgC;IADsCF,EAAA,CAAAK,UAAA,2BAAAC,iFAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,QAAA,GAAAN,MAAA;IAAA,EAAsB;IAC5DP,EAAA,CAAAI,YAAA,EAAa;IAGjDJ,EAAA,CAAAE,cAAA,kBACkG;IAD1FF,EAAA,CAAAK,UAAA,mBAAAS,qEAAA;MAAAd,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAf,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAG,MAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAElChB,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBACoG;IAD5FF,EAAA,CAAAK,UAAA,mBAAAY,qEAAA;MAAAjB,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAS,MAAA,GAAAlB,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAM,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAExBnB,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAKnBJ,EAAA,CAAAoB,qBAAA,EAAe;;;;IA9BkBpB,EAAA,CAAAqB,SAAA,GAAyF;IAAzFrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAuB,eAAA,KAAAC,GAAA,EAAAC,MAAA,CAAAC,WAAA,UAAAD,MAAA,CAAAE,cAAA,0BAAyF;IAGlH3B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,4CACF;IAEE7B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA6B,WAAA,yDAAA7B,EAAA,CAAA6B,WAAA,QAAAJ,MAAA,CAAAM,KAAA,OACF;IAKkC/B,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAA4B,kBAAA,KAAA5B,EAAA,CAAA6B,WAAA,kCAAqC;IACnD7B,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,oBAAmB,+BAAAG,MAAA,CAAAZ,QAAA;IAIEb,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAsB,UAAA,aAAAG,MAAA,CAAAZ,QAAA,CAAAmB,MAAA,OAAkC;IAErEhC,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,6CACF;IAIE7B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,2CACF;;;;;;IAOR7B,EAAA,CAAAE,cAAA,cAAoC;IAG9BF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,cAA8E;IAC5EF,EAAA,CAAAG,MAAA,GACF;;;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAA+E;IAI7CF,EAAA,CAAAG,MAAA,IAAqC;;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvEJ,EAAA,CAAAE,cAAA,sBACsB;IADgDF,EAAA,CAAAK,UAAA,2BAAA4B,gFAAA1B,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAA0B,GAAA;MAAA,MAAAC,MAAA,GAAAnC,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAuB,MAAA,CAAAtB,QAAA,GAAAN,MAAA;IAAA,EAAsB;IACtEP,EAAA,CAAAI,YAAA,EAAa;IAIvCJ,EAAA,CAAAE,cAAA,kBAEgB;IAD+DF,EAAA,CAAAK,UAAA,mBAAA+B,oEAAA;MAAApC,EAAA,CAAAQ,aAAA,CAAA0B,GAAA;MAAA,MAAAG,MAAA,GAAArC,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAyB,MAAA,CAAArB,gBAAA,EAAkB;IAAA,EAAC;;IAC3FhB,EAAA,CAAAI,YAAA,EAAS;;;;IAjB3BJ,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4B,kBAAA,MAAA5B,EAAA,CAAA6B,WAAA,2CACF;IAEE7B,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA6B,WAAA,yDAAA7B,EAAA,CAAA6B,WAAA,QAAAS,MAAA,CAAAP,KAAA,OACF;IAKkC/B,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAA4B,kBAAA,KAAA5B,EAAA,CAAA6B,WAAA,kCAAqC;IACnD7B,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,oBAAmB,+BAAAgB,MAAA,CAAAzB,QAAA;IAK3Bb,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAsB,UAAA,UAAAtB,EAAA,CAAA6B,WAAA,wCAAmD,aAAAS,MAAA,CAAAzB,QAAA,CAAAmB,MAAA;;;AD3BvE,OAAM,MAAOO,mBAAmB;EAuBVC,UAAA;EACAC,SAAA;EACAC,cAAA;EACAC,MAAA;EACAC,KAAA;EACAC,IAAA;EACAC,gBAAA;EACAC,iBAAA;EACqBC,UAAA;EACrBC,UAAA;EACAC,aAAA;EAAsCC,aAAA;EA/B1DC,WAAW;EACXC,kBAAkB,GAAW,EAAE;EAC/BC,gBAAgB,GAAW,EAAE;EAC7BC,WAAW,GAAW,EAAE;EACxBC,gBAAgB,GAAW,EAAE;EAC7BC,OAAO,GAAQ1D,iBAAiB;EAChC2D,iBAAiB,GAAU,KAAK;EAChC7C,QAAQ,GAAQ,EAAE;EAElB8C,WAAW;EACXC,OAAO;EACPC,SAAS;EACT9B,KAAK,GAAW,EAAE;EAClBL,WAAW,GAAKoC,MAAM,CAACC,UAAU;EACjCpC,cAAc,GAAY,KAAK;EAE/BqC,QAAQA,CAACC,KAAW;IAClB,IAAInE,iBAAiB,CAAC,IAAI,CAACkD,UAAU,CAAC,EAAE;MACtC,IAAI,CAACtB,WAAW,GAAGoC,MAAM,CAACC,UAAU;;EAExC;EACAG,YAAoB1B,UAA2B,EAC3BC,SAA2B,EAC3BC,cAA8B,EAC9BC,MAAc,EACdC,KAAmB,EACnBC,IAAiB,EACjBC,gBAAkC,EAClCC,iBAAoC,EACfC,UAAe,EACpCC,UAAkC,EAClCC,aAA4B,EAAUC,aAA4B;IAVlE,KAAAX,UAAU,GAAVA,UAAU;IACV,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACI,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IAAyB,KAAAC,aAAa,GAAbA,aAAa;IACrE,IAAIgB,QAAQ,GAAQC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI;IAC3DF,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;IAC/B,IAAIA,QAAQ,EAAEK,YAAY,EAAE,IAAI,CAACpB,WAAW,GAAGe,QAAQ,CAACK,YAAY;EACtE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC9C,cAAc,GAAG,IAAI,CAACoB,iBAAiB,CAAC2B,aAAa,CAAC,eAAe,CAAC;IAC3E,IAAI,CAACb,SAAS,GAAGS,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACpE,IAAG,IAAI,CAACR,SAAS,EAAC;MAChB,IAAI,CAAC9B,KAAK,GAAG,IAAI,CAACU,SAAS,CAACkC,OAAO,CAAC,+CAA+C,CAAC;KACrF,MAAI;MACH,IAAI,CAAC5C,KAAK,GAAG,IAAI,CAACU,SAAS,CAACkC,OAAO,CAAC,iCAAiC,CAAC;;EAE1E;EACAxD,MAAMA,CAAA;IACJ,IAAI,CAACwB,MAAM,CAACiC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAC,6BAA6BA,CAACC,iBAAA,GAA0B,EAAE;IACtD,IAAI,CAAC7B,UAAU,CAACgB,KAAK,CACnB,IAAI,CAACR,OAAO,CAACsB,uBAAuB,EACpC,EAAE,EACF,oCAAoC,EACpC,CAAC,EACD,IAAI,EACJ;MACE,oBAAoB,EAAED;KACvB,CACF;EACL;EAEA9D,gBAAgBA,CAAA;IACd,IAAI,CAAC4B,KAAK,CAACoC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;IAC/B,IAAI,CAACxC,UAAU,CAACyC,YAAY,GAAG,IAAI,CAACpE,QAAQ;IAE5C,IAAI,IAAI,CAACuC,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,IAAI,EAAE,IAAI,IAAI,CAACvC,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,EAAE,EAAE;MAC/F,IAAI,CAACgC,IAAI,CACNqC,KAAK,CAAC;QACLC,QAAQ,EAAE,IAAI,CAAC/B,WAAW,CAACgC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QAC3CvE,QAAQ,EAAE,IAAI,CAACA;OAChB,CAAC,CAACwE,SAAS,CAAC;QACbC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,EAAEC,OAAO,EAAE;YAChB,IAAI,CAAC5C,KAAK,CAACoC,GAAG,CAAC,SAAS,EAAEO,GAAG,CAACE,IAAI,CAAC;YACnC,IAAI,CAAC7C,KAAK,CAACoC,GAAG,CAAC,WAAW,EAAEO,GAAG,CAACE,IAAI,CAACjB,YAAY,CAAC;YAClD,IAAI,CAAC5B,KAAK,CAACoC,GAAG,CAAC,cAAc,EAAE,IAAIU,IAAI,EAAE,CAACC,OAAO,EAAE,CAAC;YACpD,IAAIC,KAAK,GAAGL,GAAG,CAACE,IAAI,CAACI,SAAS,CAACT,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;YACrD,IAAI,CAACP,6BAA6B,CAAC,MAAM,CAAC;YAC1C,IAAI,CAACjB,OAAO,GAAGhE,UAAU,CAACgG,KAAK,CAAC;YAEhC,IAAIE,IAAI,GAAQ,CAAC,IAAI,CAAClC,OAAO,CAACmC,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAEC,OAAO,CAChE,CAAC,CACF;YAED,MAAMC,OAAO,GAAG,IAAIP,IAAI,EAAE;YAC1BO,OAAO,CAACC,OAAO,CAACD,OAAO,CAACE,OAAO,EAAE,GAAGC,QAAQ,CAACN,IAAI,CAAC,CAAC;YACnD,IAAIO,gBAAgB,GAAGxG,QAAQ,CAACyG,GAAG,CAACC,OAAO,CACzCX,KAAK,EACL,QAAQ,CACT,CAACY,QAAQ,EAAE;YACZpC,YAAY,CAACqC,OAAO,CAAC,UAAU,EAAEJ,gBAAgB,CAAC;YAClD,IAAI,CAACnD,aAAa,CAAC8B,GAAG,CAAC,WAAW,EAAEY,KAAK,EAAE;cACzCc,OAAO,EAAET,OAAO;cAChBU,IAAI,EAAE,GAAG;cACTC,QAAQ,EAAE;aACX,CAAC;YACF,IAAI,CAACjD,WAAW,GAAG,IAAI,CAACT,aAAa,CAAC2D,GAAG,CAAC,WAAW,CAAC;YAEtD,IAAI,CAAC/D,gBAAgB,CAACgE,YAAY,CAAC,IAAI,CAACnD,WAAW,CAAC;YACpDS,YAAY,CAAC2C,UAAU,CAAC,SAAS,CAAC;YAElC,IAAI,CAAC5D,aAAa,CAAC6D,IAAI,EAAE;YACzB,IAAIzB,GAAG,EAAEE,IAAI,EAAEwB,QAAQ,EACrB,IAAI,CAACrE,KAAK,CAACoC,GAAG,CAAC,UAAU,EAAEO,GAAG,CAACE,IAAI,CAACwB,QAAQ,CAAC;YAE/C7C,YAAY,CAACqC,OAAO,CAAC,cAAc,EAAElB,GAAG,CAACE,IAAI,CAACyB,YAAY,CAAC;YAC3D,IAAI,CAACtE,KAAK,CAACoC,GAAG,CAAC,cAAc,EAAEO,GAAG,CAACE,IAAI,CAACyB,YAAY,CAAC;YACrD,IAAI,CAACvE,MAAM,CAACiC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;WACjD,MAAM;YACL,IAAI,CAACC,6BAA6B,CAAC,QAAQ,CAAC;YAC5C,IAAI,CAACnC,cAAc,CAACyE,GAAG,CAAC;cACtBC,QAAQ,EAAE,OAAO;cACjBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE/B,GAAG,CAACgC;aACb,CAAC;YACFnD,YAAY,CAACqC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;QAE3C;OACD,CAAC;KAGH,MAAM;MACL,IAAI,CAAC/D,cAAc,CAACyE,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,IAAI,CAAC5E,SAAS,CAACkC,OAAO,CAAC,0BAA0B,CAAC;QAC3D2C,MAAM,EAAE,IAAI,CAAC7E,SAAS,CAACkC,OAAO,CAAC,8BAA8B;OAC9D,CAAC;MACF,IAAI,CAAC/B,KAAK,CAACoC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;;EAEpC;EAEAwC,iBAAiBA,CAACvD,KAAU;IAC1B,IAAIwD,GAAG;IACPA,GAAG,GAAGxD,KAAK,CAACyD,QAAQ;IACpB,OAAQD,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAG,EAAE;EAC9B;;qBA3IWlF,mBAAmB,EAAAvC,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA/H,EAAA,CAAA2H,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjI,EAAA,CAAA2H,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAnI,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAQ,YAAA,GAAApI,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAS,WAAA,GAAArI,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAU,gBAAA,GAAAtI,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAW,iBAAA,GAAAvI,EAAA,CAAA2H,iBAAA,CA+BVhI,WAAW,GAAAK,EAAA,CAAA2H,iBAAA,CAAAa,EAAA,CAAAC,sBAAA,GAAAzI,EAAA,CAAA2H,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAA3I,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAgB,aAAA;EAAA;;UA/BpBrG,mBAAmB;IAAAsG,SAAA;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;iBAAnBC,GAAA,CAAAjF,QAAA,CAAAzD,MAAA,CAAgB;QAAA,UAAAP,EAAA,CAAAkJ,eAAA;;;;;;;;QCzB7BlJ,EAAA,CAAAE,cAAA,iBAA0B;QACxBF,EAAA,CAAAmJ,UAAA,IAAAC,2CAAA,4BA+Be;QACfpJ,EAAA,CAAAmJ,UAAA,IAAAE,0CAAA,kCAAArJ,EAAA,CAAAsJ,sBAAA,CA0Bc;QAChBtJ,EAAA,CAAAI,YAAA,EAAU;;;;QA3DOJ,EAAA,CAAAqB,SAAA,GAAuB;QAAvBrB,EAAA,CAAAsB,UAAA,SAAA2H,GAAA,CAAAvH,WAAA,OAAuB,aAAA6H,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}