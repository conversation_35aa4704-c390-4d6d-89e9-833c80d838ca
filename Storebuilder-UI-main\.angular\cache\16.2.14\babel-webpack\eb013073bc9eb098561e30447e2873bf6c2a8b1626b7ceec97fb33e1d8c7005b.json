{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core/services/gtm.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/carousel\";\nimport * as i7 from \"@shared/components/landing-templates/promotion-banner/promotion-banner.component\";\nimport * as i8 from \"@shared/components/landing-templates/promotion-vertical/promotion-vertical.component\";\nimport * as i9 from \"@shared/components/landing-templates/feature-products/feature-products.component\";\nimport * as i10 from \"@shared/components/mtn-main-slider/mtn-main-slider.component\";\nimport * as i11 from \"@shared/modals/flash-sale-modal/flash-sale-modal.component\";\nfunction TemplateOneComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 15)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"app-mtn-main-slider\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"sliders\", ctx_r0.mainBanner);\n  }\n}\nfunction TemplateOneComponent_section_2_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-promotion-banner\", 23);\n  }\n  if (rf & 2) {\n    const banner_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"banner\", banner_r14);\n  }\n}\nfunction TemplateOneComponent_section_2_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 21);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_2_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"responsiveOptions\", ctx_r11.rowOnePromotionResponsiveConfig)(\"value\", ctx_r11.rowOnePromotions)(\"circular\", false)(\"autoplayInterval\", 0)(\"showIndicators\", false)(\"showNavigators\", false);\n  }\n}\nfunction TemplateOneComponent_section_2_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"app-promotion-banner\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const banner_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banner\", banner_r16);\n  }\n}\nfunction TemplateOneComponent_section_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_2_ng_container_3_div_1_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.rowOnePromotions);\n  }\n}\nfunction TemplateOneComponent_section_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 17)(1, \"div\", 18);\n    i0.ɵɵtemplate(2, TemplateOneComponent_section_2_p_carousel_2_Template, 2, 6, \"p-carousel\", 19);\n    i0.ɵɵtemplate(3, TemplateOneComponent_section_2_ng_container_3_Template, 2, 1, \"ng-container\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth <= 767);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth > 767);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    minHeight: a0\n  };\n};\nfunction TemplateOneComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-promotion-vertical\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c0, ctx_r2.isMobileLayout && ctx_r2.isMobileView ? \"600px\" : \"inherit\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banners\", ctx_r2.rowTwoPromotions);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"template-one__feature__feature-products-max\": a0\n  };\n};\nfunction TemplateOneComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"app-feature-products\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx_r3.rowTwoPromotions.length == 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"featureId\", ctx_r3.firstFeaturedProducts.id)(\"isLeftVerticalBanner\", ctx_r3.rowTwoPromotions.length == 0 ? true : false)(\"products\", ctx_r3.firstFeaturedProducts.data)(\"showLoader\", ctx_r3.firstFeaturedProducts.showLoader)(\"title\", ctx_r3.firstFeaturedProducts.title);\n  }\n}\nfunction TemplateOneComponent_section_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 30)(1, \"div\", 4)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r4.bestSellerMobileBanner);\n  }\n}\nfunction TemplateOneComponent_section_8_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-promotion-banner\", 23);\n  }\n  if (rf & 2) {\n    const banner_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"banner\", banner_r20);\n  }\n}\nfunction TemplateOneComponent_section_8_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 21);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_8_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"responsiveOptions\", ctx_r17.rowThreePromotionResponsiveConfig)(\"value\", ctx_r17.rowThreePromotions)(\"circular\", false)(\"autoplayInterval\", 0)(\"showIndicators\", false)(\"showNavigators\", false);\n  }\n}\nfunction TemplateOneComponent_section_8_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"app-promotion-banner\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const banner_r22 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banner\", banner_r22);\n  }\n}\nfunction TemplateOneComponent_section_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TemplateOneComponent_section_8_ng_container_3_div_1_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r18.rowThreePromotions);\n  }\n}\nfunction TemplateOneComponent_section_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 33)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, TemplateOneComponent_section_8_p_carousel_2_Template, 2, 6, \"p-carousel\", 19);\n    i0.ɵɵtemplate(3, TemplateOneComponent_section_8_ng_container_3_Template, 2, 1, \"ng-container\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.screenWidth <= 767);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.screenWidth > 767);\n  }\n}\nfunction TemplateOneComponent_app_feature_products_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-feature-products\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"featureId\", ctx_r6.secondFeaturedProducts.id)(\"isRightVerticalBanner\", ctx_r6.rowFourPromotions.length == 0 ? true : false)(\"products\", ctx_r6.secondFeaturedProducts.data)(\"showLoader\", ctx_r6.firstFeaturedProducts.showLoader)(\"title\", ctx_r6.secondFeaturedProducts.title);\n  }\n}\nfunction TemplateOneComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"app-promotion-vertical\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"banners\", ctx_r7.rowFourPromotions);\n  }\n}\nfunction TemplateOneComponent_section_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 30)(1, \"div\", 4)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r8.bestOfferMobileBanner);\n  }\n}\nfunction TemplateOneComponent_section_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 30)(1, \"div\", 4)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"app-promotion-banner\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"banner\", ctx_r9.rowFivePromotions);\n  }\n}\nfunction TemplateOneComponent_section_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 38);\n    i0.ɵɵelement(1, \"app-feature-products\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"featureId\", ctx_r10.thirdFeaturedProducts.id)(\"fullRowProducts\", true)(\"isNewArrival\", true)(\"products\", ctx_r10.thirdFeaturedProducts.data)(\"showLoader\", ctx_r10.firstFeaturedProducts.showLoader)(\"title\", ctx_r10.thirdFeaturedProducts.title);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"template-one__feature-2__feature-products-max\": a0\n  };\n};\nexport let TemplateOneComponent = /*#__PURE__*/(() => {\n  class TemplateOneComponent {\n    appDataService;\n    productService;\n    tenantService;\n    router;\n    permissionService;\n    $gtmService;\n    platformId;\n    screenWidth;\n    topNumber = 8;\n    // Main Banner\n    mainBanner = [];\n    // Featured Products\n    firstFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    secondFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    thirdFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    fourthFeaturedProducts = {\n      id: 0,\n      data: [],\n      title: '',\n      isActive: false,\n      showLoader: true\n    };\n    // Promotions\n    rowOnePromotions = [];\n    rowTwoPromotions = [];\n    rowThreePromotions = [];\n    rowFourPromotions = [];\n    rowFivePromotions;\n    bestSellerMobileBanner;\n    bestOfferMobileBanner;\n    rowOnePromotionResponsiveConfig;\n    rowThreePromotionResponsiveConfig;\n    displayFlashSaleModal = false;\n    flashSaleData;\n    flashSale;\n    isMobileView = window.innerWidth <= 768;\n    isMobileLayout = false;\n    onResize(event) {\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n        this.isMobileView = this.screenWidth <= 768;\n      }\n    }\n    constructor(appDataService, productService, tenantService, router, permissionService, $gtmService, platformId) {\n      this.appDataService = appDataService;\n      this.productService = productService;\n      this.tenantService = tenantService;\n      this.router = router;\n      this.permissionService = permissionService;\n      this.$gtmService = $gtmService;\n      this.platformId = platformId;\n      this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n      this.rowOnePromotionResponsiveConfig = [{\n        breakpoint: '2400px',\n        numVisible: 3,\n        numScroll: 0\n      }, {\n        breakpoint: '991px',\n        numVisible: 2,\n        numScroll: 1\n      }, {\n        breakpoint: '767px',\n        numVisible: 2,\n        numScroll: 1\n      }];\n      this.rowThreePromotionResponsiveConfig = [{\n        breakpoint: '1199px',\n        numVisible: 3,\n        numScroll: 0\n      }, {\n        breakpoint: '991px',\n        numVisible: 2,\n        numScroll: 1\n      }, {\n        breakpoint: '767px',\n        numVisible: 1.5,\n        numScroll: 1\n      }];\n    }\n    ngOnInit() {\n      this.$gtmService.pushPageView('Home');\n      this.fetchFeaturedProducts();\n      this.fetchPromotions();\n      this.fetchMainBanner();\n      this.fetchFlashSale();\n    }\n    fetchFeaturedProducts() {\n      const allFeatureProducts = this.appDataService.layoutTemplate.filter(section => section.type === 'feature_product');\n      if (allFeatureProducts.length) {\n        allFeatureProducts.forEach(item => {\n          if (item.isActive) {\n            switch (item.data) {\n              // Hot Deals Section\n              case '1':\n                if (this.tenantService.isAllowedCached.isHotDeals) {\n                  this.getFeatureProducts(parseInt(item.data), \"first\");\n                } else {\n                  this.firstFeaturedProducts = localStorage.getItem('firstFeaturedProducts') ? JSON.parse(localStorage.getItem('firstFeaturedProducts') ?? '') : [];\n                }\n                break;\n              // New Arrivals Section\n              case '2':\n                if (this.tenantService.isAllowedCached.isNewArrivals) {\n                  this.getFeatureProducts(parseInt(item.data), \"second\");\n                } else {\n                  this.secondFeaturedProducts = localStorage.getItem('secondFeaturedProducts') ? JSON.parse(localStorage.getItem('secondFeaturedProducts') ?? '') : [];\n                }\n                break;\n              // Bestsellers Section\n              case '3':\n                if (this.tenantService.isAllowedCached.isBestSeller) {\n                  this.getFeatureProducts(parseInt(item.data), \"third\");\n                } else {\n                  this.thirdFeaturedProducts = localStorage.getItem('thirdFeaturedProducts') ? JSON.parse(localStorage.getItem('thirdFeaturedProducts') ?? '') : [];\n                }\n                break;\n            }\n          } else {\n            this.firstFeaturedProducts.showLoader = false;\n            this.secondFeaturedProducts.showLoader = false;\n            this.thirdFeaturedProducts.showLoader = false;\n          }\n        });\n      }\n    }\n    // fetchPromotions() {\n    //   const allPromotions = this.appDataService.layoutTemplate.filter((section: any) => section.type === 'promotion')\n    //   allPromotions.forEach((promotion: any) => {\n    //     if (promotion.name === 'Promotion Card Left' || promotion.name === 'Promotion Card Middle' || promotion.name === 'Promotion Card Right') {\n    //       const data: any = JSON.parse(promotion.data)\n    //       data.ctaLink = data.CTALink\n    //       data.isActive = promotion.isActive\n    //       if (data.isActive) {\n    //         this.rowOnePromotions.push(data)\n    //       }\n    //     }\n    //     if (promotion.name === 'Discount Banner Left') {\n    //       const data: any = JSON.parse(promotion.data)\n    //       data.ctaLink = data.CTALink\n    //       data.isActive = promotion.isActive\n    //       if (data.isActive) {\n    //         this.rowTwoPromotions.push(data)\n    //       }\n    //\n    //     }\n    //     if (promotion.name === 'Discount Banner Middle Left' || promotion.name === 'Discount Banner Middle Right') {\n    //       const data: any = JSON.parse(promotion.data)\n    //       data.ctaLink = data.CTALink\n    //       data.isActive = promotion.isActive\n    //       if (data.isActive) {\n    //         this.rowThreePromotions.push(data)\n    //       }\n    //\n    //     }\n    //     if (promotion.name === 'Discount Banner Right Top' || promotion.name === 'Discount Banner Right Bottom') {\n    //       const data: any = JSON.parse(promotion.data)\n    //       data.ctaLink = data.CTALink\n    //       data.isActive = promotion.isActive\n    //       if (data.isActive) {\n    //         this.rowFourPromotions.push(data)\n    //       }\n    //\n    //     }\n    //     if (promotion.name === 'Discount Banner Bottom') {\n    //       const data: any = JSON.parse(promotion.data)\n    //       data.ctaLink = data.CTALink\n    //       data.isActive = promotion.isActive\n    //       if (data.isActive) {\n    //         this.rowFivePromotions = data;\n    //       }\n    //\n    //     }\n    //   })\n    //\n    // }\n    fetchPromotions() {\n      const allPromotions = this.appDataService.layoutTemplate.filter(section => section.type === 'promotion');\n      allPromotions.forEach(promotion => {\n        const data = JSON.parse(promotion.data);\n        data.ctaLink = data.CTALink;\n        data.isActive = promotion.isActive;\n        if (data.isActive) {\n          data['promotionId'] = promotion.promotionId;\n          switch (promotion.name) {\n            case 'Promotion Card Left':\n            case 'Promotion Card Middle':\n            case 'Promotion Card Right':\n              this.rowOnePromotions.push(data);\n              break;\n            case 'Discount Banner Left':\n              this.rowTwoPromotions.push(data);\n              break;\n            case 'Discount Banner Middle Left':\n            case 'Discount Banner Middle Right':\n              this.rowThreePromotions.push(data);\n              break;\n            case 'Discount Banner Right Top':\n            case 'Discount Banner Right Bottom':\n              this.rowFourPromotions.push(data);\n              break;\n            case 'Discount Banner Bottom':\n              this.rowFivePromotions = data;\n              break;\n          }\n        }\n      });\n      const bestSellerMobileBanner = this.appDataService.layoutTemplate.find(section => section.type === 'best_seller_banner');\n      if (bestSellerMobileBanner) {\n        const data = JSON.parse(bestSellerMobileBanner.data);\n        data.ctaLink = data.CTALink;\n        data.isActive = bestSellerMobileBanner.isActive;\n        this.bestSellerMobileBanner = data;\n      }\n      const bestOfferMobileBanner = this.appDataService.layoutTemplate.find(section => section.type === 'best_offers_banner');\n      if (bestOfferMobileBanner) {\n        const data = JSON.parse(bestOfferMobileBanner.data);\n        data.ctaLink = data.CTALink;\n        data.isActive = bestOfferMobileBanner.isActive;\n        this.bestOfferMobileBanner = data;\n      }\n    }\n    fetchMainBanner() {\n      const allBanners = this.appDataService.layoutTemplate.find(section => section.type === 'main_banner');\n      if (allBanners) {\n        if (allBanners?.isActive) this.mainBanner = JSON.parse(allBanners.data);\n        if (this.mainBanner.length > 0) {\n          this.mainBanner = this.mainBanner.filter(item => !item.isMerchantbanner);\n        }\n        this.mainBanner = this.filterInactivebanners(this.mainBanner);\n      }\n    }\n    filterInactivebanners(tempData) {\n      let currentDate = Date.now();\n      let data = tempData.filter(banner => {\n        let endDate = new Date(banner.endDate).getTime();\n        let startDate = new Date(banner.startDate).getTime();\n        if (!banner.promotionId) {\n          if (!endDate) {\n            return banner;\n          } else {\n            if (endDate && endDate >= currentDate && startDate && startDate <= currentDate) {\n              return banner;\n            }\n          }\n        }\n        if (banner.promotionId && endDate && endDate >= currentDate && startDate && startDate <= currentDate) {\n          return banner;\n        }\n      });\n      return data;\n    }\n    getFeatureProducts(featureId, featureSequence) {\n      let featureTopNumber = 8;\n      let featureShowOutOfStock = false;\n      const showRoomRecords = this.appDataService.showRoomConfiguration.records;\n      if (showRoomRecords.length) {\n        const featureRecord = showRoomRecords.find(res => res.showRoomTypeId == 2 && res.featureProduct == featureId);\n        if (featureRecord && featureRecord?.topNumber) {\n          featureTopNumber = featureRecord.topNumber;\n        }\n        if (featureRecord && featureRecord?.showOutOfStock) {\n          featureShowOutOfStock = featureRecord.showOutOfStock;\n        }\n      }\n      let pageSize = featureTopNumber;\n      this.productService.GetAllProductsByFeature(featureId, featureTopNumber, false, 1, pageSize, false, featureShowOutOfStock, true).subscribe({\n        next: res => {\n          if (res?.data?.records?.length) {\n            const tempProducts = [];\n            res.data?.records.forEach(record => {\n              this.addTempProduct(res, tempProducts, record, featureSequence, featureId, featureTopNumber);\n            });\n          } else {\n            this.setFeatureProducts(featureSequence, featureId, [], res?.data?.featureName, featureTopNumber);\n          }\n        }\n      });\n    }\n    fetchFlashSale() {\n      this.flashSale = this.appDataService.layoutTemplate.find(section => section.description === 'Flash Sale');\n      const visited = localStorage.getItem('visited');\n      if (this.flashSale?.isActive && this.flashSale?.promotionId && !visited && this.flashSale?.data) {\n        this.flashSaleData = JSON.parse(this.flashSale.data);\n        this.displayFlashSaleModal = true;\n      }\n    }\n    addTempProduct(res, tempProducts, record, featureSequence, featureId, featureTopNumber) {\n      let selectedVariance;\n      let defaultVariant = record?.productVariances?.find(variant => variant.isDefault);\n      if (defaultVariant) {\n        selectedVariance = defaultVariant;\n      } else {\n        let approvedVariant = record?.productVariances?.find(variant => !variant.soldOut);\n        if (approvedVariant) {\n          selectedVariance = approvedVariant;\n        } else {\n          selectedVariance = record?.productVariances[0];\n        }\n      }\n      if (selectedVariance) {\n        let features = selectedVariance?.productFeaturesList[0]?.featureList;\n        let product = {\n          productId: record?.id,\n          productName: record?.name,\n          isLiked: record?.isLiked,\n          priceValue: selectedVariance?.price,\n          priceId: selectedVariance?.priceId,\n          salePriceValue: selectedVariance?.salePrice,\n          currencyCode: record?.currencyCode,\n          masterImageUrl: selectedVariance.masterImageUrl ?? (selectedVariance.images ? selectedVariance.images[0] : ''),\n          thumbnailImages: selectedVariance?.thumbnailImages,\n          soldOut: selectedVariance?.soldOut,\n          rate: selectedVariance?.rate,\n          count: selectedVariance?.count ?? 0,\n          salePercent: selectedVariance?.salePrice ? 100 - selectedVariance?.salePrice / selectedVariance?.price * 100 : 0,\n          shopId: record.shopId,\n          specProductId: selectedVariance.specProductId,\n          channelId: record.channelId ?? '1',\n          isHot: !!features?.includes(1),\n          isNew: !!features?.includes(2),\n          isBest: !!features?.includes(3),\n          quantity: selectedVariance.quantity,\n          proSchedulingId: selectedVariance.proSchedulingId,\n          stockPerSKU: selectedVariance.stockPerSKU,\n          stockStatus: selectedVariance.stockStatus,\n          sku: selectedVariance?.sku,\n          skuAutoGenerated: selectedVariance.skuAutoGenerated,\n          badgesList: record?.badgesList\n        };\n        if (product.salePriceValue) {\n          product.salePercent = 100 - product.salePriceValue / product.priceValue * 100;\n        }\n        tempProducts.push(product);\n      }\n      this.setFeatureProducts(featureSequence, featureId, tempProducts, res?.data?.featureName, featureTopNumber);\n    }\n    setFeatureProducts(featureSequence, featureId, tempProducts, featureName, topNumberLimit = 8) {\n      // todo: this slicing needs to be discussed may be this one is may be handled from styling\n      if (featureSequence === 'first') {\n        let firstProductsCount = this.rowTwoPromotions.length > 0 ? topNumberLimit : 10;\n        this.firstFeaturedProducts = {\n          id: featureId,\n          data: tempProducts.slice(0, firstProductsCount),\n          title: featureName,\n          showLoader: false,\n          isActive: true\n        };\n        localStorage.setItem('firstFeaturedProducts', JSON.stringify(this.firstFeaturedProducts));\n      } else if (featureSequence === 'second') {\n        let secondProductsCount = this.rowFourPromotions.length > 0 ? topNumberLimit : 10;\n        this.secondFeaturedProducts = {\n          id: featureId,\n          data: tempProducts.slice(0, secondProductsCount),\n          title: featureName,\n          showLoader: false,\n          isActive: true\n        };\n        localStorage.setItem('secondFeaturedProducts', JSON.stringify(this.secondFeaturedProducts));\n      } else if (featureSequence === 'third') {\n        this.thirdFeaturedProducts = {\n          id: featureId,\n          data: tempProducts.slice(0, topNumberLimit),\n          title: featureName,\n          showLoader: false,\n          isActive: true\n        };\n        localStorage.setItem('thirdFeaturedProducts', JSON.stringify(this.thirdFeaturedProducts));\n      } else if (featureSequence === 'fourth') {\n        this.fourthFeaturedProducts = {\n          id: featureId,\n          data: tempProducts.slice(0, topNumberLimit),\n          title: featureName,\n          showLoader: false,\n          isActive: true\n        };\n        localStorage.setItem('fourthFeaturedProducts', JSON.stringify(this.fourthFeaturedProducts));\n      }\n    }\n    onFlashCancel() {\n      this.displayFlashSaleModal = false;\n    }\n    routeToCTA() {\n      this.displayFlashSaleModal = false;\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.flashSale.promotionId) {\n          const data = JSON.parse(this.flashSale.data);\n          let tempurl;\n          // if(data.promotionName) {\n          //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + data.promotionName;\n          // } else{\n          //   tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + this.flashSale.promotionId;\n          // }\n          // window.open(tempurl, '_blank');\n          this.router.navigate([`/promotion/${data.promotionName}`]);\n        } else {\n          window.open(this.flashSaleData.CTALink, '_blank');\n        }\n      }\n    }\n    static ɵfac = function TemplateOneComponent_Factory(t) {\n      return new (t || TemplateOneComponent)(i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i1.TenantService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i3.GTMService), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateOneComponent,\n      selectors: [[\"app-landing-template-one\"]],\n      hostBindings: function TemplateOneComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function TemplateOneComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 19,\n      vars: 19,\n      consts: [[1, \"template-one\"], [\"class\", \"template-one__slider\", 4, \"ngIf\"], [\"class\", \"template-one__promotion\", 4, \"ngIf\"], [1, \"template-one__feature\", 3, \"ngStyle\"], [1, \"d-flex\", \"justify-content-center\"], [\"class\", \"template-one__feature__promotion-vertical d-inline-flex\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"template-one__feature__feature-products d-inline-flex pl-3\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"template-one__promotion-large\", 4, \"ngIf\"], [\"class\", \"template-one__promotion-medium\", 4, \"ngIf\"], [1, \"template-one__feature-2\"], [1, \"template-one__feature-2__feature-products\", \"d-inline-flex\", \"pr-3\", 3, \"ngClass\"], [3, \"featureId\", \"isRightVerticalBanner\", \"products\", \"showLoader\", \"title\", 4, \"ngIf\"], [\"class\", \"template-one__feature-2__promotion-vertical d-inline-flex\", 4, \"ngIf\"], [\"class\", \"template-one__feature-single\", 4, \"ngIf\"], [3, \"data\", \"displayModal\", \"submit\", \"cancel\"], [1, \"template-one__slider\"], [3, \"sliders\"], [1, \"template-one__promotion\"], [1, \"d-flex\", \"justify-content-flex-start\", \"mobile-second-banner\"], [3, \"responsiveOptions\", \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"showNavigators\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"responsiveOptions\", \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"showNavigators\"], [\"pTemplate\", \"item\"], [1, \"w-100\", \"promotion-banner-small\", 3, \"banner\"], [\"class\", \"template-one__promotion__promotion-content d-inline-flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-one__promotion__promotion-content\", \"d-inline-flex\"], [1, \"template-one__feature__promotion-vertical\", \"d-inline-flex\", 3, \"ngStyle\"], [3, \"banners\"], [1, \"template-one__feature__feature-products\", \"d-inline-flex\", \"pl-3\", 3, \"ngClass\"], [3, \"featureId\", \"isLeftVerticalBanner\", \"products\", \"showLoader\", \"title\"], [1, \"template-one__promotion-large\"], [1, \"template-one__promotion-large__promotion-content\", \"d-inline-flex\"], [1, \"w-100\", 3, \"banner\"], [1, \"template-one__promotion-medium\"], [\"class\", \"template-one__promotion-medium__promotion-content d-inline-flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"template-one__promotion-medium__promotion-content\", \"d-inline-flex\"], [3, \"featureId\", \"isRightVerticalBanner\", \"products\", \"showLoader\", \"title\"], [1, \"template-one__feature-2__promotion-vertical\", \"d-inline-flex\"], [1, \"template-one__feature-single\"], [3, \"featureId\", \"fullRowProducts\", \"isNewArrival\", \"products\", \"showLoader\", \"title\"]],\n      template: function TemplateOneComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, TemplateOneComponent_section_1_Template, 3, 1, \"section\", 1);\n          i0.ɵɵtemplate(2, TemplateOneComponent_section_2_Template, 4, 2, \"section\", 2);\n          i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4);\n          i0.ɵɵtemplate(5, TemplateOneComponent_div_5_Template, 2, 4, \"div\", 5);\n          i0.ɵɵtemplate(6, TemplateOneComponent_div_6_Template, 2, 8, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, TemplateOneComponent_section_7_Template, 4, 1, \"section\", 7);\n          i0.ɵɵtemplate(8, TemplateOneComponent_section_8_Template, 4, 2, \"section\", 8);\n          i0.ɵɵelementStart(9, \"section\", 9)(10, \"div\", 4)(11, \"div\", 10);\n          i0.ɵɵtemplate(12, TemplateOneComponent_app_feature_products_12_Template, 1, 5, \"app-feature-products\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, TemplateOneComponent_div_13_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, TemplateOneComponent_section_14_Template, 4, 1, \"section\", 7);\n          i0.ɵɵtemplate(15, TemplateOneComponent_section_15_Template, 4, 1, \"section\", 7);\n          i0.ɵɵtemplate(16, TemplateOneComponent_section_16_Template, 2, 6, \"section\", 13);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(17);\n          i0.ɵɵelementStart(18, \"app-flash-sale-modal\", 14);\n          i0.ɵɵlistener(\"submit\", function TemplateOneComponent_Template_app_flash_sale_modal_submit_18_listener() {\n            return ctx.routeToCTA();\n          })(\"cancel\", function TemplateOneComponent_Template_app_flash_sale_modal_cancel_18_listener() {\n            return ctx.onFlashCancel();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.mainBanner.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rowOnePromotions.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(15, _c0, ctx.isMobileLayout && ctx.isMobileView ? \"600px\" : \"inherit\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.screenWidth > 1200 && ctx.rowTwoPromotions.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.firstFeaturedProducts.isActive);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.bestSellerMobileBanner == null ? null : ctx.bestSellerMobileBanner.isActive);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rowThreePromotions.length > 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c2, ctx.rowFourPromotions.length == 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.secondFeaturedProducts.isActive);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.screenWidth > 1200 && ctx.rowFourPromotions.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.bestOfferMobileBanner == null ? null : ctx.bestOfferMobileBanner.isActive);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rowFivePromotions);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.thirdFeaturedProducts.isActive);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.flashSaleData)(\"displayModal\", ctx.displayFlashSaleModal);\n        }\n      },\n      dependencies: [i4.PrimeTemplate, i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i6.Carousel, i7.PromotionBannerComponent, i8.PromotionVerticalComponent, i9.FeatureProductsComponent, i10.MtnMainSliderComponent, i11.FlashSaleModalComponent],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.template-one[_ngcontent-%COMP%]{width:100%;display:block;margin:0 auto;padding:0!important}.template-one__slider[_ngcontent-%COMP%]{height:330px;width:1300px;margin-left:auto;margin-right:auto}.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{height:inherit}.template-one__slider[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:auto}@media only screen and (max-width: 767px){.template-one__slider[_ngcontent-%COMP%]{height:220px;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__slider[_ngcontent-%COMP%]{width:90vw}}.template-one__promotion[_ngcontent-%COMP%]{height:250px;margin-top:30px;width:1300px;margin-left:auto;margin-right:auto;padding:0}.template-one__promotion[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{width:100%;padding:0;margin:0;gap:0}.template-one__promotion__promotion-content[_ngcontent-%COMP%]{width:422.6666666667px;margin-right:16px;padding:0;margin-left:0}.template-one__promotion__promotion-content[_ngcontent-%COMP%]:first-child{margin-left:0}.template-one__promotion__promotion-content[_ngcontent-%COMP%]:last-child{margin-right:0}.template-one__promotion__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:250px;width:100%;padding:0;margin:0}@media only screen and (max-width: 767px){.template-one__promotion__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:120px}}.template-one__promotion__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]   .promotion-banner[_ngcontent-%COMP%]{padding:0;margin:0;width:100%;height:100%}@media only screen and (max-width: 767px){.template-one__promotion[_ngcontent-%COMP%]{height:120px;margin-top:37px;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__promotion[_ngcontent-%COMP%]{width:90vw}}.template-one__promotion-medium[_ngcontent-%COMP%]{height:350px;margin-top:30px;width:1300px;margin-left:auto;margin-right:auto}.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]{width:688px;margin-right:15px}.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]:last-child{margin-right:0}.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:350px}@media only screen and (max-width: 767px){.template-one__promotion-medium__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:115px}}@media only screen and (max-width: 767px){.template-one__promotion-medium[_ngcontent-%COMP%]{height:115px;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__promotion-medium[_ngcontent-%COMP%]{width:90vw}}.template-one__promotion-large[_ngcontent-%COMP%]{height:400px;margin-top:30px;width:1300px;margin-left:auto;margin-right:auto}.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]{width:100%;margin-right:15px}.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]:last-child{margin-right:0}.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:400px}@media only screen and (max-width: 767px){.template-one__promotion-large__promotion-content[_ngcontent-%COMP%]   app-promotion-banner[_ngcontent-%COMP%]{height:165px}}@media only screen and (max-width: 767px){.template-one__promotion-large[_ngcontent-%COMP%]{height:165px;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__promotion-large[_ngcontent-%COMP%]{width:90vw}}.template-one__feature[_ngcontent-%COMP%]{margin-top:30px;height:auto;width:1300px;margin-left:auto;margin-right:auto}.template-one__feature__promotion-vertical[_ngcontent-%COMP%]{width:15%;height:inherit}@media only screen and (max-width: 767px){.template-one__feature__promotion-vertical[_ngcontent-%COMP%]{height:auto}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature__promotion-vertical[_ngcontent-%COMP%]{height:auto}}.template-one__feature__promotion-vertical[_ngcontent-%COMP%]   app-promotion-vertical[_ngcontent-%COMP%]{width:100%}.template-one__feature__feature-products[_ngcontent-%COMP%]{margin-left:15px;width:85%}.template-one__feature__feature-products[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%]{width:100%}@media only screen and (max-width: 767px){.template-one__feature__feature-products[_ngcontent-%COMP%]{width:100%}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature__feature-products[_ngcontent-%COMP%]{width:100%}}.template-one__feature__feature-products-max[_ngcontent-%COMP%]{width:100%!important}@media only screen and (max-width: 767px){.template-one__feature[_ngcontent-%COMP%]{height:auto;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature[_ngcontent-%COMP%]{height:auto;width:90vw}}.template-one__feature-2[_ngcontent-%COMP%]{min-height:600px;height:auto;margin-top:30px;width:1300px;margin-left:auto;margin-right:auto}@media only screen and (max-width: 767px){.template-one__feature-2[_ngcontent-%COMP%]{min-height:300px;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature-2[_ngcontent-%COMP%]{min-height:300px;width:90vw}}.template-one__feature-2__promotion-vertical[_ngcontent-%COMP%]{width:15%;height:600px}.template-one__feature-2__promotion-vertical[_ngcontent-%COMP%]   app-promotion-vertical[_ngcontent-%COMP%]{width:100%}.template-one__feature-2__feature-products[_ngcontent-%COMP%]{margin-right:15px;width:85%}.template-one__feature-2__feature-products[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%]{width:100%}@media only screen and (max-width: 767px){.template-one__feature-2__feature-products[_ngcontent-%COMP%]{width:100%}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature-2__feature-products[_ngcontent-%COMP%]{width:100%}}.template-one__feature-2__feature-products-max[_ngcontent-%COMP%]{width:100%!important}@media only screen and (max-width: 767px){.template-one__feature-2[_ngcontent-%COMP%]{height:auto;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature-2[_ngcontent-%COMP%]{height:auto;width:90vw}}.template-one__feature-single[_ngcontent-%COMP%]{min-height:530px;height:auto;margin-top:30px;width:1300px;margin-left:auto;margin-right:auto}.template-one__feature-single[_ngcontent-%COMP%]   app-feature-products[_ngcontent-%COMP%]{width:100%}@media only screen and (max-width: 767px){.template-one__feature-single[_ngcontent-%COMP%]{height:auto;width:90vw}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature-single[_ngcontent-%COMP%]{height:auto;width:90vw}}@media only screen and (max-width: 767px){.template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%]{margin-left:0!important;padding-left:0!important}.template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%]{margin-left:0!important;padding-left:0!important;margin-right:0!important;padding-right:0!important}.template-one__feature[_ngcontent-%COMP%], .template-one__feature-2[_ngcontent-%COMP%], .template-one__feature-single[_ngcontent-%COMP%]{margin-top:13px!important}.mobile-second-banner[_ngcontent-%COMP%]{display:block!important}}@media only screen and (min-width: 768px) and (max-width: 1200px){.template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%], .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%]{margin-left:0!important;padding-left:0!important;margin-right:0!important;padding-right:0!important}}@media only screen and (min-width: 1201px) and (max-width: 1700px){.template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%], .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%]{margin-left:0!important;padding-left:0!important;margin-right:0!important;padding-right:0!important}}@media only screen and (min-width: 1701px){.template-one__feature__feature-products.d-inline-flex.pl-3[_ngcontent-%COMP%], .template-one__feature-2__feature-products.d-inline-flex.pr-3[_ngcontent-%COMP%]{margin-left:0!important;padding-left:0!important;margin-right:0!important;padding-right:0!important}}[_nghost-%COMP%]{display:block;width:100%;padding:0!important}  .landing-template app-landing-template-one{padding:0!important}  .landing-template app-landing-template-one .template-one{padding:0!important}\"]\n    });\n  }\n  return TemplateOneComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}