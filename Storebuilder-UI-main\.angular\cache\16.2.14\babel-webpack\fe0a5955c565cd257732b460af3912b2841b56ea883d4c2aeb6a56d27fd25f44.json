{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nconst _c0 = function (a0) {\n  return {\n    \"shadow-4 active\": a0\n  };\n};\nfunction IndexComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_7_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const letter_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onScrollHref(letter_r2.key));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const letter_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.search === letter_r2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", letter_r2.key, \" \");\n  }\n}\nfunction IndexComponent_ng_container_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_9_div_7_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const shop_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.ShopProduct(shop_r7.shopId, shop_r7.shopName));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const shop_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(shop_r7.shopName);\n  }\n}\nfunction IndexComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\", 1);\n    i0.ɵɵtemplate(7, IndexComponent_ng_container_9_div_7_Template, 3, 1, \"div\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const data_r5 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", data_r5.key);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", data_r5.key, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", data_r5.shopList);\n  }\n}\nexport class IndexComponent {\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n    if (this.screenWidth <= 768) {\n      this.isMobileView = true;\n    } else {\n      this.isMobileView = false;\n    }\n  }\n  constructor(shopService, viewportScroller, route) {\n    this.shopService = shopService;\n    this.viewportScroller = viewportScroller;\n    this.route = route;\n    this.search = '';\n    this.criteria = [];\n    this.merchants = [];\n    this.shopList = [];\n    this.isMobileView = false;\n  }\n  ngOnInit() {\n    this.getMerchants();\n  }\n  getMerchants() {\n    this.shopService.getAllShopSorted().subscribe(res => {\n      if (res.success && res.data) this.shopList = res.data;\n      this.criteria = this.newArr;\n    });\n  }\n  onScrollHref(key) {\n    if (!this.isMobileView) {\n      this.viewportScroller.setOffset([0, 170]);\n    } else {\n      this.viewportScroller.setOffset([0, 195]);\n    }\n    this.viewportScroller.scrollToAnchor(key);\n  }\n  ShopProduct(value, Name) {\n    this.route.navigate([`/merchants/merchant-product`, value, Name]);\n  }\n}\nIndexComponent.ɵfac = function IndexComponent_Factory(t) {\n  return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ShopService), i0.ɵɵdirectiveInject(i2.ViewportScroller), i0.ɵɵdirectiveInject(i3.Router));\n};\nIndexComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: IndexComponent,\n  selectors: [[\"app-index\"]],\n  hostBindings: function IndexComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"resize\", function IndexComponent_resize_HostBindingHandler($event) {\n        return ctx.onResize($event);\n      }, false, i0.ɵɵresolveWindow);\n    }\n  },\n  decls: 10,\n  vars: 5,\n  consts: [[1, \"merchants\", \"mt-5\", \"px-0\"], [1, \"grid\"], [1, \"col-12\", \"col-md-6\", \"flex\", \"md:justify-content-start\"], [1, \"font-size-28\", \"bold-font\"], [1, \"mt-5\", \"flex\", \"flex-row\", \"justify-content-start\", \"lg:justify-content-evenly\", \"flex-wrap\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mt-8\", \"mobile-all-merchant\"], [\"fragment\", \"letter.key\", 1, \"main-color\", \"p-2\", \"cursor-pointer\", \"font-size-20\", \"bold-font\", \"uppercase\", 3, \"ngClass\", \"click\"], [1, \"grid\", \"mt-5\", 3, \"id\"], [1, \"col-12\"], [1, \"bold-font\", \"font-size-20\"], [\"class\", \"col-12 col-md-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-md-4\"], [1, \"font-size-15\", \"main-color\", \"medium-font\", \"my-1\", \"no-underline\", 2, \"cursor\", \"pointer\", 3, \"click\"]],\n  template: function IndexComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵtext(4);\n      i0.ɵɵpipe(5, \"translate\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"div\", 4);\n      i0.ɵɵtemplate(7, IndexComponent_ng_container_7_Template, 3, 4, \"ng-container\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵtemplate(9, IndexComponent_ng_container_9_Template, 8, 3, \"ng-container\", 5);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 3, \"merchant.merchants\"), \" \");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.shopList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.shopList);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i4.TranslatePipe],\n  styles: [\".merchants[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n}\\n.merchants[_ngcontent-%COMP%]   .search[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  border-radius: 5px;\\n  outline: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.0117647059);\\n  color: #a3a3a3;\\n  font-size: 12px;\\n  font-family: var(--regular-font);\\n}\\n\\n.m-top[_ngcontent-%COMP%] {\\n  margin-top: 10rem !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .m-top[_ngcontent-%COMP%] {\\n    margin-top: 17rem !important;\\n  }\\n  .mobile-all-merchant[_ngcontent-%COMP%] {\\n    padding: 0px 50px 0px 50px;\\n    margin-top: 0px !important;\\n  }\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}