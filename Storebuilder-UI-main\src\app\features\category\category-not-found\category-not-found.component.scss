@import '../../../../assets/scss/common.scss';

// Base styles
.empty-cart {
  height: 640px;
  position: relative;
}

.center {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: 0;
  transform: translate(-50%, -25%);
}

.cartText {
  width: 302px;
  font: 700 28px var(--medium-font);
  margin: 25px 0 15px !important;
}

.second-btn {
  width: 293px !important;
  font: 500 14px var(--medium-font);
  text-transform: uppercase;
}

.cart-wait {
  font: 300 15px var(--regular-font);
  color: #A3A3A3 !important;
}

// ========================================
// MEDIA QUERIES
// ========================================

@media (max-width: 768px) {
  .empty-cart {
    height: 300px;
  }
}
