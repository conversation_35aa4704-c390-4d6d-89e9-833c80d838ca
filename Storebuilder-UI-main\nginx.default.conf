server {
    listen       443 ssl;
    listen  [::]:443 ssl;
    server_name  store.mtn.yetanotherdomain.xyz;

    ssl_certificate    /shared/certs/tls.crt;
    ssl_certificate_key /shared/certs/tls.key;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
	    try_files $uri $uri/ /index.html;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

