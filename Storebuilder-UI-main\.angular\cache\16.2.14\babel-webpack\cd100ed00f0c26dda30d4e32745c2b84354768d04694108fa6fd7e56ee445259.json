{"ast": null, "code": "import { k as getSlideTransformEl, c as createElement } from './utils.mjs';\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\nexport { createShadow as c };", "map": {"version": 3, "names": ["k", "getSlideTransformEl", "c", "createElement", "createShadow", "suffix", "slideEl", "side", "shadowClass", "shadow<PERSON><PERSON><PERSON>", "shadowEl", "querySelector", "split", "join", "append"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/swiper/shared/create-shadow.mjs"], "sourcesContent": ["import { k as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,aAAa;AAE1E,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC3C,MAAMC,WAAW,GAAI,sBAAqBD,IAAI,GAAI,IAAGA,IAAK,EAAC,GAAG,EAAG,GAAEF,MAAM,GAAI,wBAAuBA,MAAO,EAAC,GAAG,EAAG,EAAC;EACnH,MAAMI,eAAe,GAAGR,mBAAmB,CAACK,OAAO,CAAC;EACpD,IAAII,QAAQ,GAAGD,eAAe,CAACE,aAAa,CAAE,IAAGH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAE,EAAC,CAAC;EACpF,IAAI,CAACH,QAAQ,EAAE;IACbA,QAAQ,GAAGP,aAAa,CAAC,KAAK,EAAEK,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC;IACvDH,eAAe,CAACK,MAAM,CAACJ,QAAQ,CAAC;EAClC;EACA,OAAOA,QAAQ;AACjB;AAEA,SAASN,YAAY,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}