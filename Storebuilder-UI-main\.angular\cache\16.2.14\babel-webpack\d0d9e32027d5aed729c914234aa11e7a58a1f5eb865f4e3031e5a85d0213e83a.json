{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@shared/components/loader-dots/loader-dots.component\";\nimport * as i3 from \"primeng/dialog\";\nimport * as i4 from \"@ngx-translate/core\";\nfunction LightboxLoaderModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"img\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 3);\n    i0.ɵɵelement(4, \"app-loader-dots\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelement(6, \"img\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 3)(9, \"span\", 7);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 1, \"checkout.paymentCart.loadingMessage\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class LightboxLoaderModalComponent {\n  displayModal = false;\n  type = '';\n  ngOnInit() {}\n  ngOnChanges(changes) {}\n  static ɵfac = function LightboxLoaderModalComponent_Factory(t) {\n    return new (t || LightboxLoaderModalComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LightboxLoaderModalComponent,\n    selectors: [[\"app-lightbox-loader-modal\"]],\n    inputs: {\n      displayModal: \"displayModal\",\n      type: \"type\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 2,\n    vars: 8,\n    consts: [[1, \"loaderModal\", 3, \"visible\", \"breakpoints\", \"closable\", \"blockScroll\", \"showHeader\", \"draggable\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-space-between\", \"mb-4\"], [1, \"d-inline-flex\"], [\"src\", \"assets/icons/Artwork.png\", \"alt\", \"No Image\"], [\"src\", \"assets/icons/Frame%20(2).svg\", \"alt\", \"No Image\"], [1, \"d-flex\", \"align-items-center\"], [1, \"loaderModal__label\"]],\n    template: function LightboxLoaderModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function LightboxLoaderModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵtemplate(1, LightboxLoaderModalComponent_ng_template_1_Template, 12, 3, \"ng-template\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(7, _c0))(\"closable\", false)(\"blockScroll\", true)(\"showHeader\", true)(\"draggable\", false)(\"modal\", true);\n      }\n    },\n    dependencies: [i1.PrimeTemplate, i2.LoaderDotsComponent, i3.Dialog, i4.TranslatePipe],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n  .p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n\\n  .p-dialog .p-dialog-header {\\n  padding: 0.5rem;\\n}\\n\\n  .p-dialog .p-dialog-footer button {\\n  width: 100%;\\n  margin: 0 !important;\\n}\\n\\n.loaderModal__label[_ngcontent-%COMP%] {\\n  color: #323232;\\n  font-family: \\\"main-medium\\\";\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n.loaderModal[_ngcontent-%COMP%]     .p-dialog {\\n  width: 375px;\\n  height: 290px;\\n  padding: 70px 32px;\\n  border-radius: var(--Border-Radius-borderRadiusLG, 8px);\\n  background: #FFF;\\n  place-content: center;\\n}\\n.loaderModal[_ngcontent-%COMP%]     .p-dialog-content {\\n  padding: 0 !important;\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}