"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[442],{8442:(V,m,a)=>{a.r(m),a.d(m,{SignInComponent:()=>A});var g=a(5861),t=a(5879),l=a(6814),d=a(3023),u=a(5662),p=a(6075),h=a(6663),C=a(9566),O=a(2284),M=a(6776),b=a(6726),v=a(7134),x=a(8145),I=a(8893),w=a(1064),P=a(707),f=a(7874),y=a(5334),r=a(864),S=a(5219),T=a(459),L=a(9147);function D(c,U){if(1&c){const n=t.EpF();t.TgZ(0,"div",26)(1,"app-phone-input",27),t.NdJ("phoneNumberChange",function(i){t.CHM(n);const o=t.oxw();return t.KtG(o.onPhoneNumberChange(i))})("validationChange",function(i){t.CHM(n);const o=t.oxw();return t.KtG(o.onPhoneValidationChange(i))}),t.qZA()()}if(2&c){const n=t.oxw();t.xp6(1),t.Q6J("maxLength",n.phoneInputLength)("selectedCountryISO",n.customCountryISO)("preferredCountries",n.preferredCountries)("placeholder",n.customPlaceHolder)}}function E(c,U){if(1&c){const n=t.EpF();t.TgZ(0,"div",26)(1,"app-email-input",28),t.NdJ("emailChange",function(i){t.CHM(n);const o=t.oxw();return t.KtG(o.onEmailChange(i))})("validationChange",function(i){t.CHM(n);const o=t.oxw();return t.KtG(o.onEmailValidationChange(i))}),t.ALo(2,"translate"),t.qZA()()}if(2&c){const n=t.oxw();t.xp6(1),t.Q6J("emailRequired","false")("labelColor",n.labelColor)("emailLabel",t.lcZ(2,3,"auth.registerPassword.email"))}}let A=(()=>{class c{store;auth;messageService;router;cartService;translate;cookieService;authTokenService;route;mainDataService;permissionService;loaderService;appDataService;$gaService;userService;platformId;$gtmService;customGAService;phoneNumber=null;password="";isPhoneValid=!1;isPasswordValid=!1;phoneInputLength=12;customCountryISO;customPlaceHolder="";preferredCountries=[d.H.Uganda,d.H.Ghana,d.H.C\u00f4teDIvoire];submitted=!1;redirctURL;cartListCount=0;cartListData=[];isGoogleAnalytics=!1;isMobileLayout=!1;screenWidth;tagName=u.Ir;products=[];activeTab="mobileNumber";email=null;isEmailValid=!1;constructor(n,e,i,o,s,_,k,Z,N,R,X,F,B,K,W,z,G,j){this.store=n,this.auth=e,this.messageService=i,this.router=o,this.cartService=s,this.translate=_,this.cookieService=k,this.authTokenService=Z,this.route=N,this.mainDataService=R,this.permissionService=X,this.loaderService=F,this.appDataService=B,this.$gaService=K,this.userService=W,this.platformId=z,this.$gtmService=G,this.customGAService=j,this.initializeComponent()}ngOnInit(){this.setupPermissions(),this.setupCountryISO(),this.setupRouteParams(),this.setupPhoneInputLength(),this.$gtmService.pushPageView("signIn")}initializeComponent(){this.setupCustomPlaceholder(),(0,l.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}setupPermissions(){this.isGoogleAnalytics=this.permissionService.hasPermission("Google Analytics"),this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout")}setupCountryISO(){if(localStorage.getItem("isoCode"))this.customCountryISO=localStorage.getItem("isoCode");else{const n=this.appDataService.tenants;if(null!=n.records){let e=localStorage.getItem("tenantId"),o=n.records.find(s=>s.tenantId==e)??new O.Sb;localStorage.setItem("isoCode",o?.isoCode),this.store.set("allCountryTenants",n.records)}}}setupRouteParams(){this.route.queryParams.subscribe(n=>{this.redirctURL=n.returnUrl})}setupPhoneInputLength(){if(this.appDataService.configuration){const n=this.appDataService.configuration.records.find(e=>"PhoneLength"===e.key);n&&(this.phoneInputLength=parseInt(n.value))}}onRegisterClick(){this.isGoogleAnalytics&&this.customGAService.signupCtaClickEvent("signin_page"),this.router.navigate(["/register"])}setupCustomPlaceholder(){let n=localStorage.getItem("tenantId");n&&""!==n&&(this.customPlaceHolder={1:"XXXXXXXXX",2:"XXXXXXXXX",3:"XXXXXXXXX",4:"XXXXXXXXXX"}[n]||"")}get isFormValid(){return(this.isPhoneValid||this.isEmailValid)&&this.isPasswordValid&&this.password.length>0}onPhoneNumberChange(n){this.phoneNumber=n}onPhoneValidationChange(n){this.isPhoneValid=n}onPasswordChange(n){this.password=n}onPasswordValidationChange(n){this.isPasswordValid=n}login(){var n=this;return(0,g.Z)(function*(){var e;n.isGoogleAnalytics&&n.$gaService.event(C.s.CLICK_ON_CONTINUE_FOR_SIGN_IN,"","SIGN_IN_STEP2",1,!0),n.loaderService.show(),n.submitted=!0,n.auth.login({username:n.phoneNumber?n.phoneNumber.e164Number.slice(1):n.email,password:n.password}).subscribe({next:(e=(0,g.Z)(function*(i){yield n.handleLoginSuccess(i)}),function(o){return e.apply(this,arguments)}),error:e=>{n.handleLoginError(e)}})})()}handleLoginSuccess(n){var e=this;return(0,g.Z)(function*(){if(n?.success&&"consumer"==n.data.role){e.isGoogleAnalytics&&e.permissionService.getTagFeature("LOGIN")&&e.$gaService.event(e.tagName.LOGIN,"","",1,!0,{user_ID:n.data.mobileNumber}),yield e.updateUserConsent(n.data.id),e.setUserData(n.data),e.setAuthToken(n.data.authToken),e.loaderService.hide(),n?.data?.currency&&e.store.set("currency",n.data.currency),localStorage.setItem("refreshToken",n.data.refreshToken),e.store.set("refreshToken",n.data.refreshToken);const i={sessionId:localStorage.getItem("sessionId")},o=localStorage.getItem("cartId");yield e.checkCart(i,o),e.handlePostLoginNavigation(n.data)}else e.handleLoginFailure(n?.message)})()}updateUserConsent(n){var e=this;return(0,g.Z)(function*(){const i={consentType:M.h.Cookie,sessionId:localStorage.getItem("consumer-consent-sessionId")||"",consent:!0,userId:n};e.userService.updateUserConsent(i).subscribe({next:o=>{}})})()}setUserData(n){this.mainDataService.setUserData(n),this.store.set("profile",n),this.store.set("userPhone",n.mobileNumber),localStorage.setItem("userId",n.id),this.store.set("timeInterval",(new Date).getTime())}setAuthToken(n){let e=n.replace("bearer ",""),o=((0,b.Z)(e).exp/864e5).toFixed(0);localStorage.removeItem("visited");const s=new Date;s.setDate(s.getDate()+parseInt(o));let _=f.AES.encrypt(e,"paysky").toString();localStorage.setItem("auth_enc",_),this.cookieService.set("authToken",e,{expires:s,path:"/",sameSite:"Strict"}),localStorage.removeItem("isGuest"),this.authTokenService.authTokenSet(e)}handlePostLoginNavigation(n){n.isPasswodExpired?(this.router.navigateByUrl("/change-password"),this.messageService.add({severity:"info",summary:this.translate.instant("ResponseMessages.changePassword"),detail:this.translate.instant("ResponseMessages.passwordExpirationChange")})):(this.redirctURL?(this.router.navigate([this.redirctURL]),this.redirctURL=null):this.router.navigate(["/"]),this.messageService.add({severity:"success",summary:this.translate.instant("ResponseMessages.login"),detail:this.translate.instant("ResponseMessages.loggedInSuccessfully")}))}handleLoginError(n){this.store.set("profile",""),this.loaderService.hide(),this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n.message}),localStorage.setItem("isGuest","true")}handleLoginFailure(n){this.store.set("profile",""),this.loaderService.hide(),this.messageService.add({severity:"error",summary:n||this.translate.instant("ErrorMessages.invalidUserNameOrPassword")}),localStorage.setItem("isGuest","true")}resetPassword(){this.isGoogleAnalytics&&this.$gaService.event(C.s.CLICK_ON_FORGOT_PASSWORD,"","FORGOT_PASSWORD",1,!0),this.router.navigate(["/reset-password"])}reloadCurrentPage(n,e){this.router.navigateByUrl("/",{skipLocationChange:!0}).then(()=>this.router.navigate(["/about-us/"],{queryParams:{pageId:n,title:e}}))}getAllCart(n){this.products=[];let e={sessionId:n.sessionId},i=localStorage.getItem("apply-to");i&&""!=i&&(e.applyTo=i),this.cartService.getCart(e).subscribe({next:o=>{this.cartListCount=0,this.cartListData=[],o.data?.records?.length?(this.cartListCount=0,o.data.records[0].cartDetails.length&&(this.cartListCount=o.data.records[0].cartDetails.length,this.cartListData=o.data.records[0].cartDetails),o.data.records[0].cartDetailsDPay&&o.data.records[0].cartDetailsDPay.length&&(this.cartListCount+=o.data.records[0].cartDetailsDPay.length,this.cartListData=this.cartListData.concat(o.data.records[0].cartDetailsDPay)),this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData)):(this.mainDataService.setCartLenghtData(0),this.mainDataService.setCartItemsData([]))}})}compareCartProducts(n,e){n.length?n.forEach(i=>{e.forEach(o=>{i.specsProductId===o.specsProductId&&this.products.push(i)})}):this.products=e,this.store.set("cartProducts",this.products),localStorage.setItem("addedProducts",JSON.stringify(this.products))}getShipmentMethodByTenantId(n){this.permissionService.hasPermission("Shipment-Fee")?this.cartService.getShipmentMethodByTenantId().subscribe(e=>{e.success&&e.data.length&&(localStorage.setItem("apply-to",e.data[0].applyTo),this.getAllCart(n))}):(localStorage.setItem("apply-to","2"),this.getAllCart(n))}checkCart(n,e){return new Promise((i,o)=>{n.sessionId?(n.cartId=e&&""!=e?parseInt(e):0,this.cartService.updateCart(n).subscribe({next:s=>{s?.data?.cartItems?.length&&(this.cartListData=s.data.cartItems,this.cartListCount=s.data.cartItems.length),this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(n),i()},error:s=>{this.cartListCount=0,this.cartListData=[],this.mainDataService.setCartLenghtData(this.cartListCount),this.mainDataService.setCartItemsData(this.cartListData),this.getShipmentMethodByTenantId(n),o(s)}})):(localStorage.setItem("sessionId",v.R.newGuid()),n.sessionId=localStorage.getItem("sessionId"),this.getAllCart(n),i())})}get forgotPasswordClass(){return"font-size-12"}get floatingLabelEnabled(){return!1}get buttonLabel(){return"signIn.continue"}switchTab(n){this.activeTab=n,"mobileNumber"===n&&(this.email=null,this.isEmailValid=!1),"email"===n&&(this.phoneNumber=null,this.isPhoneValid=!1)}get labelColor(){return this.screenWidth<=767?"grey":"white"}onEmailChange(n){this.email=n}onEmailValidationChange(n){this.isEmailValid=n}static \u0275fac=function(e){return new(e||c)(t.Y36(r.d6),t.Y36(r.e8),t.Y36(S.ez),t.Y36(p.F0),t.Y36(r.Ni),t.Y36(h.sK),t.Y36(T.N),t.Y36(r.Lz),t.Y36(p.gz),t.Y36(r.iI),t.Y36(r.$A),t.Y36(r.D1),t.Y36(r.UW),t.Y36(u.$r),t.Y36(r.KD),t.Y36(t.Lbi),t.Y36(L.J),t.Y36(r.$V))};static \u0275cmp=t.Xpm({type:c,selectors:[["app-sign-in"]],standalone:!0,features:[t.jDz],decls:55,vars:49,consts:[[1,"login"],[1,"content-container","my-3"],[1,"grid","justify-content-between","mobile-top"],[1,"shadow-signin"],[1,"col-12","image"],["src","assets/images/new-signin.svg","alt","","srcset",""],[1,"col-12","col-md-8","col-lg-6","bg-white","header-body",2,"line-height","1.5"],[1,"mobile-only",3,"title","img"],[1,"desktop-only"],[1,"signin-heading"],[1,"signIn-content"],["autocomplete","new-password"],[1,"tab-container"],[1,"tab-navigation"],["type","button",1,"tab-button",3,"click"],[1,"content"],[1,"text"],[1,"tab-content","p-fluid","p-grid"],["class","tab-pane",4,"ngIf"],[1,"p-fluid","p-grid"],[3,"showForgotPassword","forgotPasswordClass","floatingLabel","feedback","passwordChange","validationChange","forgotPasswordClick"],["pButton","","type","button",1,"sign-in-btn",3,"disabled","label","click"],[1,"signin-agreement"],[3,"click"],[1,"new-customer-container"],[1,"register-now",3,"click"],[1,"tab-pane"],[3,"maxLength","selectedCountryISO","preferredCountries","placeholder","phoneNumberChange","validationChange"],[1,"customClass",3,"emailRequired","labelColor","emailLabel","emailChange","validationChange"]],template:function(e,i){1&e&&(t.TgZ(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),t._UZ(5,"img",5),t.qZA(),t.TgZ(6,"div",6),t._UZ(7,"sign-in-up-header",7),t.TgZ(8,"div",8)(9,"p",9),t._uU(10),t.ALo(11,"translate"),t.qZA(),t.TgZ(12,"p",10),t._uU(13),t.ALo(14,"translate"),t.qZA()(),t.TgZ(15,"form",11)(16,"div",12)(17,"div",13)(18,"button",14),t.NdJ("click",function(){return i.switchTab("mobileNumber")}),t.TgZ(19,"div",15)(20,"span",16),t._uU(21),t.ALo(22,"translate"),t.qZA()()(),t.TgZ(23,"button",14),t.NdJ("click",function(){return i.switchTab("email")}),t.TgZ(24,"div",15)(25,"span",16),t._uU(26),t.ALo(27,"translate"),t.qZA()()()(),t.TgZ(28,"div",17),t.YNc(29,D,2,4,"div",18),t.YNc(30,E,3,5,"div",18),t.qZA()(),t.TgZ(31,"div",19)(32,"app-password-input",20),t.NdJ("passwordChange",function(s){return i.onPasswordChange(s)})("validationChange",function(s){return i.onPasswordValidationChange(s)})("forgotPasswordClick",function(){return i.resetPassword()}),t.qZA(),t.TgZ(33,"button",21),t.NdJ("click",function(){return i.login()}),t.ALo(34,"translate"),t.qZA(),t.TgZ(35,"p",22),t._uU(36),t.ALo(37,"translate"),t.TgZ(38,"a",23),t.NdJ("click",function(){return i.reloadCurrentPage(171,"Terms and Conditions")}),t._uU(39),t.ALo(40,"translate"),t.qZA(),t._uU(41),t.ALo(42,"translate"),t.TgZ(43,"a",23),t.NdJ("click",function(){return i.reloadCurrentPage(170,"Privacy policy")}),t._uU(44),t.ALo(45,"translate"),t.qZA(),t._uU(46),t.ALo(47,"translate"),t.qZA(),t.TgZ(48,"div",24)(49,"p"),t._uU(50),t.ALo(51,"translate"),t.qZA(),t.TgZ(52,"a",25),t.NdJ("click",function(){return i.onRegisterClick()}),t._uU(53),t.ALo(54,"translate"),t.qZA()()()()()()()()()),2&e&&(t.xp6(7),t.Q6J("title","signIn.signIn")("img","assets/images/new-signin.svg"),t.xp6(3),t.Oqu(t.lcZ(11,25,"signIn.signIn")),t.xp6(3),t.Oqu(t.lcZ(14,27,"signIn.content")),t.xp6(5),t.ekj("active","mobileNumber"===i.activeTab),t.xp6(3),t.hij(" ",t.lcZ(22,29,"contactUs.mobileNumber")," "),t.xp6(2),t.ekj("active","email"===i.activeTab),t.xp6(3),t.hij(" ",t.lcZ(27,31,"contactUs.email")," "),t.xp6(3),t.Q6J("ngIf","mobileNumber"===i.activeTab),t.xp6(1),t.Q6J("ngIf","email"===i.activeTab),t.xp6(2),t.Q6J("showForgotPassword",!0)("forgotPasswordClass",i.forgotPasswordClass)("floatingLabel",i.floatingLabelEnabled)("feedback",!1),t.xp6(1),t.Q6J("disabled",!i.isFormValid)("label",t.lcZ(34,33,i.buttonLabel)),t.xp6(3),t.hij(" ",t.lcZ(37,35,"signIn.AgreeTermsOne")," "),t.xp6(3),t.hij(" ",t.lcZ(40,37,"signIn.AgreeTermsTwo")," "),t.xp6(2),t.hij("\xa0",t.lcZ(42,39,"signIn.AgreeTermsThree")," "),t.xp6(3),t.hij(" ",t.lcZ(45,41,"signIn.AgreeTermsFour")," "),t.xp6(2),t.hij("\xa0",t.lcZ(47,43,"signIn.AgreeTermsFive"),". "),t.xp6(4),t.Oqu(t.lcZ(51,45,"signIn.newCustomer")),t.xp6(3),t.hij(" ",t.lcZ(54,47,"signIn.Register")," "))},dependencies:[l.ez,l.O5,p.Bz,P.hJ,P.Hq,x.T,I.j,w.C,h.aw,h.X$,y.q],styles:['.header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.login[_ngcontent-%COMP%]     button.back-btn{position:relative!important}.login   [_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}@media (min-width: 768px){.login   [_nghost-%COMP%]     button.back-btn{top:122px!important}}.login[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{padding:24px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{border:1px solid rgba(32,78,110,.1019607843);border-radius:8px;background-color:#fff;display:flex;width:100%}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{flex-direction:column}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .mobile-btn[_ngcontent-%COMP%]{border-radius:8px;padding:12px 24px;height:48px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{flex:1;text-align:end}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{flex:1;max-width:600px;padding:20px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding-left:3rem;padding-right:3rem;padding-top:1.5rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding:0;max-width:100%}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{color:#212121;font-size:20px;font-weight:500;margin-bottom:16px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{font-size:26px;font-weight:700;font-family:var(--medium-font)!important}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signIn-content[_ngcontent-%COMP%]{color:#443f3f;font-size:14px;font-style:normal;font-weight:400;line-height:130%;font-family:var(--regular-font)!important}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{pointer-events:none;padding:8px 0;font-size:12px!important;font-weight:400;color:#2d2d2d;line-height:20px}.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:block}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:none}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:block}}.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{height:56px;border-radius:8px;background-color:#204e6e;color:#fff;font-size:14px;font-weight:500;padding:12px 24px;display:flex;justify-content:center;align-items:center;width:100%}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin:.5rem 0}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin-bottom:.75rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]{justify-content:center!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-top[_ngcontent-%COMP%]{margin-top:26px!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-container[_ngcontent-%COMP%]{overflow:hidden;margin-top:0}}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]{gap:8px;padding:8px 0}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#272727;font-size:12px;font-style:normal;font-weight:400;line-height:normal;font-family:var(--regular-font)!important}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--main-color);font-weight:700;margin-top:1rem;margin-bottom:1rem}}.login[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:#f5f5f5;color:#323232;font-weight:700;border-radius:5px}.login[_ngcontent-%COMP%]   .p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#323232}.login[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;background-color:#e8effd;color:#204e6e;font-weight:500;font-size:14px;text-align:center;padding:12px 24px;border-radius:8px;display:block;margin:16px auto}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-size:12px;color:#272727;margin-top:16px}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;text-decoration:underline}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]{margin-top:0}}  .customClass input{height:50px!important;width:100%;border-radius:8px;opacity:1;border:1px solid #ccc!important;padding-left:10px;padding-right:10px;background-color:#fff!important;font-family:var(--medium-font)!important;font-size:16px}label[_ngcontent-%COMP%]{color:red;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.signin-agreement[_ngcontent-%COMP%]{color:#272727;font-family:var(--regular-font)!important}.signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;font-weight:400;font-size:12px;text-decoration:underline!important}.second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important}[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}a[_ngcontent-%COMP%]:hover{color:var(--header_bgcolor)}.new-customer-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#373636;font-size:14px;font-weight:500;text-align:center;display:flex;align-items:center;gap:6px;margin:0 6px;white-space:nowrap;font-family:var(--regular-font)!important}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{content:"";flex-grow:1;display:inline-block;width:90px;height:1px;background-color:#204e6e;opacity:.1}.new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;text-align:center;background-color:#e8effd;color:#204e6e;font-weight:500;padding:12px 24px;border-radius:8px;border:none;margin:16px 0;font-family:var(--regular-font)!important}[_nghost-%COMP%]     .contact-input-phone{background-color:#fff!important;border:1px solid #ccc!important;border-radius:4px;padding:10px;width:100%;box-sizing:border-box}[_nghost-%COMP%]     .iti__selected-flag{padding:0 6px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{content:"";position:absolute;top:15px;left:93px;width:1px;height:50%;background-color:#9ca69c;transform:translate(-50%);pointer-events:none}.tab-container[_ngcontent-%COMP%]{width:100%}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;padding:12px;min-width:100%;height:76px;background:#E8EFFD;border-radius:8px;margin-bottom:30px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 16px;gap:16px;width:50%;height:52px;border-radius:8px;border:none;background:transparent;cursor:pointer;transition:all .3s ease;font-family:var(--regular-font);font-style:normal;font-weight:500;font-size:14px;line-height:36px;letter-spacing:1.25px;color:var(--bottom_Strock, #A3A3A3);position:relative}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:11px 0;gap:8px;width:100%;height:52px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-right[_ngcontent-%COMP%]{display:none;width:16px;height:16px;font-family:Material Icons;font-style:normal;font-weight:400;font-size:16px;line-height:100%;display:flex;align-items:center;text-align:center;color:#fff}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{width:100%;height:36px;font-family:var(--regular-font);font-style:normal;font-weight:500;font-size:14px;line-height:36px;display:flex;align-items:center;justify-content:center;text-align:center;letter-spacing:1.25px;color:var(--bottom_Strock, #A3A3A3)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-left[_ngcontent-%COMP%]{display:none;width:12px;height:16px;font-family:Material Icons;font-style:normal;font-weight:400;font-size:16px;line-height:100%;display:flex;align-items:center;text-align:center;color:#fff}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover{color:#333}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]{background:#204E6E;color:var(--shades-white, #FFF)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--shades-white, #FFF);text-align:center;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:500;line-height:36px;letter-spacing:1.25px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-right[_ngcontent-%COMP%], .tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-left[_ngcontent-%COMP%]{color:var(--shades-white, #FFF)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:focus{outline:none}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[_ngcontent-%COMP%]{display:block;opacity:1;animation:fadeIn .3s ease-in-out;visibility:visible;height:auto}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[_ngcontent-%COMP%]   .uploaded-content[_ngcontent-%COMP%]{padding:40px;text-align:center;background:#f9f9f9;border-radius:8px;color:#666;font-family:var(--regular-font)}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[hidden][_ngcontent-%COMP%]{display:none!important}']})}return c})()}}]);