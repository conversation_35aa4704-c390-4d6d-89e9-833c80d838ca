"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[165],{6165:(b,u,n)=>{n.r(u),n.d(u,{ChangePasswordModule:()=>L});var a=n(6814),e=n(5879),g=n(864),m=n(1312),f=n(5219),i=n(707),M=n(6075),d=n(1423),o=n(6223),r=n(6663);function w(t,N){1&t&&(e.TgZ(0,"div",18),e._UZ(1,"em",19),e.qZA(),e.TgZ(2,"p",20),e._uU(3),e.<PERSON>o(4,"translate"),e.qZA()),2&t&&(e.xp6(3),e.hij(" ",e.lcZ(4,1,"changePassword.changePassword")," "))}const v=function(){return["/login"]};function P(t,N){1&t&&e._UZ(0,"button",21),2&t&&e.Q6J("routerLink",e.DdM(1,v))}const x=function(t){return{marginTop:t}},C=function(){return{width:"50vw"}},y=function(){return{"960px":"75vw","640px":"100vw"}},T=[{path:"",component:(()=>{class t{store;user;permissionService;phoneNumber;oldPassword="";newPassword="";countryPhoneNumber="";phoneLength=12;displayApprovedModal=!1;isMobileLayout=!1;constructor(p,l,s){this.store=p,this.user=l,this.permissionService=s}ngOnInit(){this.isMobileLayout=this.permissionService.hasPermission("Mobile-Layout"),this.phoneNumber=localStorage.getItem("userPhone")}approveModal(){this.user.ChangePassword({mobileNumber:this.phoneNumber,oldPassword:this.oldPassword,newPassword:this.newPassword}).subscribe({next:p=>{p.success&&(this.displayApprovedModal=!0)},error:p=>{}})}static \u0275fac=function(l){return new(l||t)(e.Y36(g.d6),e.Y36(g.KD),e.Y36(g.$A))};static \u0275cmp=e.Xpm({type:t,selectors:[["app-index"]],decls:34,vars:34,consts:[[1,"update-password-page"],[1,"content-container",3,"ngStyle"],[1,"grid","justify-content-center","margin-x-100"],[1,"col-12","bold-font","text-center","font-size-28","m-0","py-0","mx-4"],[1,"col-12","main-color","text-center","no-underline","font-size-16","pt-0","m-0","mb-3"],[1,"col-12","col-md-8","col-lg-6","border-round","bg-white","shadow-1","px-5","pt-6"],[1,"p-fluid","p-grid"],[1,"p-field","p-col-12"],[1,"mb-3"],["autocomplete","off","disabled","true","id","phone-number","maxlength","15","minlength","15","type","text",3,"ngModel","placeholder","ngModelChange"],[1,"p-field","p-col-12","mt-3"],[1,""],[3,"ngModel","feedback","toggleMask","ngModelChange"],["aria-autocomplete","false","autocomplete","newPassword",3,"ngModel","feedback","toggleMask","ngModelChange"],["label","Reset Password","pButton","","type","button",1,"p-field","p-col-12","my-2","width-100","font-size-14","second-btn",3,"disabled","click"],[1,"approvedModal",3,"visible","breakpoints","resizable","visibleChange"],["pTemplate","content"],["pTemplate","footer"],[1,"icon","mt-5","bg-green-500","text-white","text-center","w-3rem","h-3rem","border-circle","icon","bg-green-500"],[1,"pi","pi-check"],[1,"font-bold","text-center","text-black-alpha-90"],["label","Back To Login","pButton","","type","button",1,"p-field","p-col-12","my-4","width-25","m-auto","font-size-14","second-btn",3,"routerLink"]],template:function(l,s){1&l&&(e.TgZ(0,"section",0),e.ynx(1),e.TgZ(2,"div",1)(3,"div",2)(4,"p",3),e._uU(5),e.ALo(6,"translate"),e.qZA(),e.TgZ(7,"div",4),e._uU(8),e.ALo(9,"translate"),e.qZA(),e.TgZ(10,"div",5)(11,"div",6)(12,"div",7)(13,"span",8)(14,"label"),e._uU(15),e.ALo(16,"translate"),e.qZA(),e.TgZ(17,"input",9),e.NdJ("ngModelChange",function(c){return s.phoneNumber=c}),e.qZA()()(),e.TgZ(18,"div",10)(19,"span",11)(20,"label"),e._uU(21),e.ALo(22,"translate"),e.qZA(),e.TgZ(23,"p-password",12),e.NdJ("ngModelChange",function(c){return s.oldPassword=c}),e.qZA()()(),e.TgZ(24,"div",10)(25,"span",11)(26,"label"),e._uU(27),e.ALo(28,"translate"),e.qZA(),e.TgZ(29,"p-password",13),e.NdJ("ngModelChange",function(c){return s.newPassword=c}),e.qZA()()(),e.TgZ(30,"button",14),e.NdJ("click",function(){return s.approveModal()}),e.qZA(),e.TgZ(31,"p-dialog",15),e.NdJ("visibleChange",function(c){return s.displayApprovedModal=c}),e.YNc(32,w,5,3,"ng-template",16),e.YNc(33,P,1,2,"ng-template",17),e.qZA()()()()(),e.BQk(),e.qZA()),2&l&&(e.xp6(2),e.Q6J("ngStyle",e.VKq(30,x,s.isMobileLayout?"1rem":"220px")),e.xp6(3),e.hij(" ",e.lcZ(6,20,"changePassword.changePassword")," "),e.xp6(3),e.hij(" ",e.lcZ(9,22,"changePassword.securityPolicy")," "),e.xp6(7),e.Oqu(e.lcZ(16,24,"changePassword.phoneNumber")),e.xp6(2),e.s9C("placeholder",s.countryPhoneNumber),e.Q6J("ngModel",s.phoneNumber),e.xp6(4),e.hij("",e.lcZ(22,26,"changePassword.oldPassword")," *"),e.xp6(2),e.Q6J("ngModel",s.oldPassword)("feedback",!1)("toggleMask",!0),e.xp6(4),e.hij("",e.lcZ(28,28,"changePassword.newPassword")," *"),e.xp6(2),e.Q6J("ngModel",s.newPassword)("feedback",!1)("toggleMask",!0),e.xp6(1),e.Q6J("disabled",!s.newPassword),e.xp6(1),e.Akn(e.DdM(32,C)),e.Q6J("visible",s.displayApprovedModal)("breakpoints",e.DdM(33,y))("resizable",!1))},dependencies:[a.PC,m.V,f.jx,i.Hq,M.rH,d.ro,o.Fj,o.JJ,o.wO,o.nD,o.On,r.X$],styles:[".p-dialog .p-dialog-header{display:none}div.icon[_ngcontent-%COMP%]{position:relative;margin:auto}div.icon[_ngcontent-%COMP%]   .pi-check[_ngcontent-%COMP%]{position:absolute;top:31%;font-weight:700;left:30%;font-size:1.2rem}@media screen and (max-width: 768px){.update-password-page[_ngcontent-%COMP%]{margin-top:160px}}"]})}return t})(),canActivate:[n(8036).a]}];var I=n(3714),J=n(9663);let L=(()=>{class t{static \u0275fac=function(l){return new(l||t)};static \u0275mod=e.oAB({type:t});static \u0275inj=e.cJS({imports:[a.ez,m.S,i.hJ,M.Bz.forChild(T),d.gz,I.j,J.zz,i.hJ,r.aw,o.u5]})}return t})()},3714:(b,u,n)=>{n.d(u,{j:()=>f,o:()=>m});var a=n(5879),e=n(6814),g=n(6223);let m=(()=>{class i{el;ngModel;cd;filled;constructor(d,o,r){this.el=d,this.ngModel=o,this.cd=r}ngAfterViewInit(){this.updateFilledState(),this.cd.detectChanges()}ngDoCheck(){this.updateFilledState()}onInput(){this.updateFilledState()}updateFilledState(){this.filled=this.el.nativeElement.value&&this.el.nativeElement.value.length||this.ngModel&&this.ngModel.model}static \u0275fac=function(o){return new(o||i)(a.Y36(a.SBq),a.Y36(g.On,8),a.Y36(a.sBO))};static \u0275dir=a.lG2({type:i,selectors:[["","pInputText",""]],hostAttrs:[1,"p-inputtext","p-component","p-element"],hostVars:2,hostBindings:function(o,r){1&o&&a.NdJ("input",function(v){return r.onInput(v)}),2&o&&a.ekj("p-filled",r.filled)}})}return i})(),f=(()=>{class i{static \u0275fac=function(o){return new(o||i)};static \u0275mod=a.oAB({type:i});static \u0275inj=a.cJS({imports:[e.ez]})}return i})()}}]);