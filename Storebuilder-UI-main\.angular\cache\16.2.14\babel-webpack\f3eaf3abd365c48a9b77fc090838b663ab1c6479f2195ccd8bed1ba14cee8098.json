{"ast": null, "code": "import { IndexComponent } from \"./components/index/index.component\";\nimport { MerchantProductsComponent } from \"./components/merchant-products/merchant-products.component\";\nexport const routes = [{\n  path: '',\n  component: IndexComponent\n}, {\n  path: 'merchant-product/:id/:name',\n  component: MerchantProductsComponent\n}];", "map": {"version": 3, "names": ["IndexComponent", "MerchantProductsComponent", "routes", "path", "component"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchants\\routes.ts"], "sourcesContent": ["import {Routes} from \"@angular/router\";\r\nimport {IndexComponent} from \"./components/index/index.component\";\r\nimport { MerchantProductsComponent } from \"./components/merchant-products/merchant-products.component\";\r\n\r\nexport const routes: Routes = [\r\n  {path: '', component: IndexComponent},\r\n  {path: 'merchant-product/:id/:name', component: MerchantProductsComponent},\r\n];\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,oCAAoC;AACjE,SAASC,yBAAyB,QAAQ,4DAA4D;AAEtG,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAACC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAc,CAAC,EACrC;EAACG,IAAI,EAAE,4BAA4B;EAAEC,SAAS,EAAEH;AAAyB,CAAC,CAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}