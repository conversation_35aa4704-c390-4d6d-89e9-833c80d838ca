{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ElementRef, PLATFORM_ID } from '@angular/core';\nimport { take } from \"rxjs\";\nimport { environment } from 'src/environments/environment';\nimport { CookieModalComponent } from \"@shared/modals/cookie-modal/cookie-modal.component\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport jwt_decode from \"jwt-decode\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@core/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"ngx-cookie-service\";\nimport * as i5 from \"ngx-bootstrap/modal\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"@core/services/gtm.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"./shared/components/header/header.component\";\nimport * as i10 from \"./shared/components/footer/footer.component\";\nimport * as i11 from \"./shared/components/loader/loader.component\";\nimport * as i12 from \"@pages/landing/components/tenant-configuration/tenant-configuration.component\";\nimport * as i13 from \"./shared/components/mobile-navbar/mobile-navbar.component\";\nimport * as i14 from \"primeng/toast\";\nimport * as i15 from \"@pages/maintenance/maintenance.component\";\nconst _c0 = [\"scroll\"];\nfunction AppComponent_app_mtn_tenant_configuration_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mtn-tenant-configuration\");\n  }\n}\nfunction AppComponent_ng_template_2_ng_container_0_section_1_app_mtn_header_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mtn-header\");\n  }\n}\nfunction AppComponent_ng_template_2_ng_container_0_section_1_app_mobile_navbar_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-mobile-navbar\");\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"page-navbar-active\": a0,\n    \"page-navbar-inactive\": a1\n  };\n};\nfunction AppComponent_ng_template_2_ng_container_0_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 4, 5);\n    i0.ɵɵtemplate(2, AppComponent_ng_template_2_ng_container_0_section_1_app_mtn_header_2_Template, 1, 0, \"app-mtn-header\", 2);\n    i0.ɵɵelementStart(3, \"div\", 6);\n    i0.ɵɵelement(4, \"p-toast\", 7);\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"div\", 9);\n    i0.ɵɵelement(7, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"app-mtn-footer\", 10);\n    i0.ɵɵlistener(\"scrollToTop\", function AppComponent_ng_template_2_ng_container_0_section_1_Template_app_mtn_footer_scrollToTop_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.scrollToTop());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AppComponent_ng_template_2_ng_container_0_section_1_app_mobile_navbar_9_Template, 1, 0, \"app-mobile-navbar\", 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showHeader);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, ctx_r5.sliderConfig.isNavbarDataActive, !ctx_r5.sliderConfig.isNavbarDataActive));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"autoZIndex\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"footerDetails\", ctx_r5.footerDetails)(\"toHide\", ctx_r5.isSpecificRoute());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.desktopView);\n  }\n}\nfunction AppComponent_ng_template_2_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-mtn-loader\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AppComponent_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppComponent_ng_template_2_ng_container_0_section_1_Template, 10, 9, \"section\", 3);\n    i0.ɵɵtemplate(2, AppComponent_ng_template_2_ng_container_0_ng_container_2_Template, 2, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.initializeApp);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.initializeApp && !(ctx_r3.showTenantSelection.includes(ctx_r3.title) && (ctx_r3.tenantId === null || ctx_r3.tenantId === \"\")));\n  }\n}\nfunction AppComponent_ng_template_2_app_maintenance_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-maintenance\");\n  }\n}\nfunction AppComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AppComponent_ng_template_2_ng_container_0_Template, 3, 2, \"ng-container\", 2);\n    i0.ɵɵtemplate(1, AppComponent_ng_template_2_app_maintenance_1_Template, 1, 0, \"app-maintenance\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isShowMaintenance());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isShowMaintenance());\n  }\n}\nexport class AppComponent {\n  onResize(event) {\n    this.screenWidth = event.target.innerWidth;\n    this.isMobileView = this.screenWidth <= 768;\n  }\n  constructor(appTitle, tenantService, cd, activatedRoute, router, cookieService, authTokenService, store, mainDataService, modalService, permissionService, authService, platformId, meta, loaderService, sitemapService, appDataService, renderer, http, $gtmService) {\n    this.appTitle = appTitle;\n    this.tenantService = tenantService;\n    this.cd = cd;\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.store = store;\n    this.mainDataService = mainDataService;\n    this.modalService = modalService;\n    this.permissionService = permissionService;\n    this.authService = authService;\n    this.platformId = platformId;\n    this.meta = meta;\n    this.loaderService = loaderService;\n    this.sitemapService = sitemapService;\n    this.appDataService = appDataService;\n    this.renderer = renderer;\n    this.http = http;\n    this.$gtmService = $gtmService;\n    this.showTenantSelection = [];\n    this.showHeader = false;\n    this.initializeApp = false;\n    this.waitForTenantSubscription = false;\n    this.footerDetails = {\n      mainLogo: 'assets/icons/momo-marketplace-footer-logo.png',\n      mainLogoMobile: 'assets/icons/new-logo-marketplace-mobile.svg',\n      copyRights: 'footer.copyRightsMobile',\n      copyRightsMobile: \"footer.copyRightsMobile\",\n      sideLogo: 'assets/icons/footer-momo-logo.svg',\n      bottonLogo: 'assets/icons/footer-momo-logo-mobile.svg',\n      payments: ['assets/icons/Momo-pay-logo.svg', 'assets/icons/Mastercard-logo.svg', 'assets/icons/Visa-logo.svg'],\n      socials: [{\n        icon: 'assets/icons/facebook-mb-icon.svg',\n        url: 'https://www.facebook.com/'\n      }\n      // {\n      //   icon:'assets/icons/insta-mb-icon.svg',\n      //   url: 'https://www.instagram.com/'\n      // },\n      // {\n      //   icon:'assets/icons/linkedin-mb-icon.svg',\n      //   url: 'https://www.linkedin.com/'\n      // }\n      ]\n    };\n\n    this.sliderConfig = {\n      isBannerActive: false,\n      isNavbarDataActive: false\n    };\n    this.maintenanceMode = [];\n    this.isMobileView = window.innerWidth <= 768;\n    this.isMobileTemplate = false;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.screen.width;\n      if (this.screenWidth >= 768) {\n        this.desktopView = true;\n      } else {\n        this.desktopView = false;\n      }\n    }\n    this.triggerMaintenanceMode();\n    if (environment.isStoreCloud) {\n      this.title = 'Yalla Mall';\n      $('body').addClass('storecloud');\n      localStorage.setItem('tenantId', environment.defaultTenant);\n    } else {\n      this.title = 'Market by MoMo';\n      this.showTenantSelection.push(this.title);\n    }\n    appTitle.setTitle(this.title);\n    this.tenantId = localStorage.getItem('tenantId');\n  }\n  ngOnInit() {\n    this.meta.addTag({\n      name: 'title',\n      content: 'Market by MoMo'\n    });\n    this.meta.addTag({\n      name: 'description',\n      content: 'E-commerce platform'\n    });\n    let authToken = this.cookieService.get('authToken');\n    if (!authToken) {\n      localStorage.setItem('isGuest', 'true');\n    } else {\n      localStorage.removeItem('isGuest');\n    }\n  }\n  initializeApplication() {\n    this.loaderService.show();\n    this.updateApiExpiry();\n    // TODO: Generate updated sitemaps from the following method\n    // this.generateSitemap()\n    const showCookiePopup = localStorage.getItem('save_cookie');\n    if (!showCookiePopup) {\n      this.triggerCookiePopup();\n    }\n    this.activatedRoute.queryParams.subscribe(queryParams => {\n      if (queryParams.tenantId) {\n        this.tenantId = queryParams.tenantId || environment.defaultTenant;\n        localStorage.setItem('tenantId', this.tenantId);\n        this.waitForTenantSubscription = true;\n        let authToken = this.cookieService.get('authToken');\n        if (!authToken) {\n          let profile = localStorage.getItem('profile');\n          if (profile && profile !== '') {\n            profile = JSON.parse(profile);\n            authToken = profile.authToken.replace('bearer ', '');\n            const decoded = jwt_decode(authToken);\n            let days = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n            const dateNow = new Date();\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\n            this.cookieService.set('authToken', authToken, {\n              expires: dateNow,\n              path: '/',\n              sameSite: 'Strict'\n            });\n          }\n        }\n        if (!authToken) {\n          let isGuest = localStorage.getItem('isGuest');\n          if (!isGuest) {\n            this.logOut();\n          }\n          this.loadAppData();\n        } else {\n          this.loadAppData();\n        }\n      } else {\n        setTimeout(() => {\n          if (!this.waitForTenantSubscription) {\n            this.loadAppData();\n          }\n        }, 500);\n      }\n    });\n    if (environment.isStoreCloud) {\n      this.title = 'Yalla Mall';\n      $('body').addClass('storecloud');\n      let tenant = localStorage.getItem('tenantId');\n      if (!tenant) {\n        localStorage.setItem('tenantId', environment.defaultTenant);\n      }\n    } else {\n      this.title = 'Market by MoMo';\n      this.showTenantSelection.push(this.title);\n    }\n    this.mainDataService.getBannerData().subscribe(data => {\n      if (data?.isBannerActive) {\n        this.sliderConfig.isBannerActive = data.isBannerActive;\n      }\n      if (data?.isNavbarDataActive) {\n        this.sliderConfig.isNavbarDataActive = data.isNavbarDataActive;\n      }\n      this.cd.detectChanges();\n    });\n    // this.tenantSubscription = this.tenantService.getTenant()\n    //   .subscribe(response => {\n    //     this.tenantId = response\n    //     localStorage.setItem('tenantId', this.tenantId)\n    //     this.loadAppData()\n    //   })\n    this.tenantId = localStorage.getItem('tenantId');\n    if (this.tenantId) {\n      this.loadAppData();\n    }\n    this.tenantService.getHeader().subscribe(resp => {\n      this.showHeader = resp;\n      this.cd.detectChanges();\n    });\n    // this.loadAppData()\n    UtilityFunctions.assignSessionId();\n    UtilityFunctions.assignConsentSessionId();\n  }\n  scrollToTop() {\n    this.scroll.nativeElement.scrollTop = 0;\n  }\n  loadAppData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.authService.isMarketplace()) {\n        yield _this.authService.getShopSettingsData().subscribe({\n          next: res => {\n            if (res.data) {\n              _this.appDataService.shopSettingData = res.data;\n              _this.cd.detectChanges();\n            }\n          }\n        });\n      }\n      if (_this.tenantId !== null && _this.tenantId !== '' && _this.tenantId != undefined) {\n        yield _this.tenantService.getAppConfigurationData(_this.isMobileView);\n        _this.authService.getAllFeatureByTenant().subscribe({\n          next: function () {\n            var _ref = _asyncToGenerator(function* (res) {\n              if (res.success) {\n                const permissions = res.data.map(perm => perm.portalName);\n                _this.permissionService.setPermissions(permissions);\n                _this.isMobileTemplate = _this.permissionService.hasPermission('Mobile-Layout');\n                yield _this.tenantService.getConfigurationWithoutCache(_this.isMobileView && _this.permissionService.hasPermission('Mobile-Layout'));\n                if (_this.permissionService.hasPermission('Google Analytics')) _this.getTagsData();\n                _this.initializeApp = true;\n                _this.loaderService.hide();\n                _this.cd.detectChanges();\n              }\n            });\n            return function next(_x) {\n              return _ref.apply(this, arguments);\n            };\n          }(),\n          error: err => {\n            _this.initializeApp = true;\n          }\n        });\n        // await this.getAllowedFeatures();\n      }\n    })();\n  }\n\n  getTagsData() {\n    this.mainDataService.getAllGoogleTagConfigurations().subscribe({\n      next: res => {\n        const permissions = res.data.map(perm => {\n          if (perm.isActive) {\n            return perm.tag;\n          }\n        });\n        this.permissionService.setTagFeature(permissions);\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  triggerCookiePopup() {\n    if (isPlatformBrowser(this.platformId)) {\n      const cookieModalData = {\n        initialState: {\n          cookieModalDetails: {\n            header: \"We Value your privacy\",\n            message: 'We use cookies to ensure you get the best experience on our website. By using our website you agree to your cookie.'\n          }\n        },\n        backdrop: 'static',\n        ignoreBackdropClick: true,\n        class: \"cookie-popup-width\"\n      };\n      const bsModalRefCookie = this.modalService.show(CookieModalComponent, cookieModalData);\n      bsModalRefCookie.content.submit.pipe(take(1)).subscribe(value => {});\n    }\n  }\n  ngOnDestroy() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.tenantSubscription.unsubscribe();\n    }\n  }\n  logOut() {\n    sessionStorage.clear();\n    this.authTokenService.authTokenSet('');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('profile', '');\n    this.mainDataService.setCartLenghtData(null);\n    this.mainDataService.setUserData(null);\n    localStorage.setItem('secondaryDefault', 'false');\n    localStorage.setItem('sessionId', '');\n    localStorage.setItem('addedProducts', '');\n    localStorage.setItem('cartId', '');\n    this.store.set('cartProducts', []);\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    localStorage.setItem('isGuest', 'true');\n  }\n  getAllowedFeatures() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.authService.getAllFeatureByTenant().subscribe({\n        next: res => {\n          if (res.success) {\n            const permissions = res.data.map(perm => perm.portalName);\n            _this2.permissionService.setPermissions(permissions);\n            _this2.isMobileTemplate = _this2.permissionService.hasPermission('Mobile-Layout');\n          }\n        },\n        error: err => {\n          _this2.initializeApp = true;\n        }\n      });\n    })();\n  }\n  updateApiExpiry() {\n    const apiExpiry = localStorage.getItem('apiExpiry');\n    if (!apiExpiry && apiExpiry === '') {\n      localStorage.setItem('apiExpiry', new Date().getTime().toString());\n    }\n  }\n  generateSitemap() {\n    const allRoutes = this.router.config;\n    const relevantRoutes = this.filterRelevantRoutes(allRoutes); // Optionally filter out irrelevant routes\n    const sitemapContent = this.sitemapService.generateSitemap(relevantRoutes);\n  }\n  filterRelevantRoutes(routes) {\n    if (routes && routes.length > 0 && routes[0].children) return routes[0].children.filter(route => {\n      return !route.path.startsWith('category/') && !route.path.startsWith('product/') && !route.path.startsWith('order/') && !route.path.startsWith('**');\n    });\n  }\n  isSpecificRoute() {\n    const specificRoutePath = ['/categories-list', '/cart', '/account', '/wishlist', '/checkout', '/account/address', '/account/verify-address', '/account/address/add-address', '/account/help'];\n    const currentRoute = this.router.url;\n    // console.log(currentRoute , specificRoutePath);\n    return specificRoutePath.some(route => route == currentRoute);\n  }\n  isShowMaintenance() {\n    let tenantId = Number(localStorage.getItem('tenantId'));\n    return this.maintenanceMode.some(item => {\n      if (item.tenantId === tenantId) {\n        return item.maintenanceMode;\n      }\n    });\n  }\n  triggerMaintenanceMode() {\n    const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant');\n    if (localMaintenanceTenant && localMaintenanceTenant !== '') {\n      this.maintenanceMode = JSON.parse(localMaintenanceTenant);\n      setTimeout(() => {\n        if (!this.isShowMaintenance()) {\n          this.initializeApplication();\n        }\n        this.cd.detectChanges();\n      }, 500);\n    } else {\n      this.http.get('assets/data.json').subscribe(data => {\n        this.$gtmService.injectGTM(data, this.tenantId);\n        this.maintenanceMode = data;\n        if (!this.isShowMaintenance()) {\n          this.initializeApplication();\n        }\n        this.cd.detectChanges();\n      });\n    }\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.TenantService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.CookieService), i0.ɵɵdirectiveInject(i2.AuthTokenService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i2.MainDataService), i0.ɵɵdirectiveInject(i5.BsModalService), i0.ɵɵdirectiveInject(i2.PermissionService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.Meta), i0.ɵɵdirectiveInject(i2.LoaderService), i0.ɵɵdirectiveInject(i2.SitemapService), i0.ɵɵdirectiveInject(i2.AppDataService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i6.HttpClient), i0.ɵɵdirectiveInject(i7.GTMService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    viewQuery: function AppComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ElementRef);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroll = _t.first);\n      }\n    },\n    hostBindings: function AppComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function AppComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 4,\n    vars: 2,\n    consts: [[4, \"ngIf\", \"ngIfElse\"], [\"showLandingPage\", \"\"], [4, \"ngIf\"], [\"class\", \"container mw-100 px-0\", 4, \"ngIf\"], [1, \"container\", \"mw-100\", \"px-0\"], [\"scroll\", \"\"], [1, \"page\", 3, \"ngClass\"], [\"position\", \"top-right\", \"sticky\", \"true\", 3, \"autoZIndex\"], [\"id\", \"zoom-container\"], [1, \"mobile-bg-border\"], [3, \"footerDetails\", \"toHide\", \"scrollToTop\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0);\n        i0.ɵɵtemplate(1, AppComponent_app_mtn_tenant_configuration_1_Template, 1, 0, \"app-mtn-tenant-configuration\", 0);\n        i0.ɵɵtemplate(2, AppComponent_ng_template_2_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementContainerEnd();\n      }\n      if (rf & 2) {\n        const _r1 = i0.ɵɵreference(3);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showTenantSelection.includes(ctx.title) && (ctx.tenantId === null || ctx.tenantId === \"\"))(\"ngIfElse\", _r1);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i3.RouterOutlet, i9.HeaderComponent, i10.FooterComponent, i11.LoaderComponent, i12.TenantConfigurationComponent, i13.MobileNavbarComponent, i14.Toast, i15.MaintenanceComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: auto;\\n}\\n\\n.page[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  min-height: 100vh;\\n  background-color: #ffffff;\\n}\\n\\n  .p-toast-top-right {\\n  top: 7rem !important;\\n  z-index: 10000 !important;\\n}\\n\\n.attribute-dropdown[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n@media only screen and (max-width: 786px) {\\n    .p-toast-top-right {\\n    top: 7rem !important;\\n    right: 0px !important;\\n    left: 0px !important;\\n    z-index: 10000 !important;\\n  }\\n  .page[_ngcontent-%COMP%] {\\n    min-height: 2px !important;\\n  }\\n}\\n@media only screen and (min-width: 701px) and (max-width: 1200px) {\\n  .page[_ngcontent-%COMP%] {\\n    margin-top: 4rem !important;\\n  }\\n}\\n.yellow-bg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 260px;\\n}\\n@media only screen and (max-width: 767px) {\\n  .yellow-bg[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n\\n@media only screen and (min-width: 1701px) {\\n  yellow-bg-navbar-active[_ngcontent-%COMP%] {\\n    top: 122px !important;\\n  }\\n  .yellow-bg-navbar-inactive[_ngcontent-%COMP%] {\\n    top: 70px !important;\\n  }\\n  .page-navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 122px;\\n  }\\n  .page-navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 73px;\\n  }\\n}\\n@media only screen and (min-width: 1201px) and (max-width: 1700px) {\\n  yellow-bg-navbar-active[_ngcontent-%COMP%] {\\n    top: 122px !important;\\n  }\\n  .yellow-bg-navbar-inactive[_ngcontent-%COMP%] {\\n    top: 70px !important;\\n  }\\n  .page-navbar-active[_ngcontent-%COMP%] {\\n    margin-top: 122px;\\n  }\\n  .page-navbar-inactive[_ngcontent-%COMP%] {\\n    margin-top: 73px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["ElementRef", "PLATFORM_ID", "take", "environment", "CookieModalComponent", "UtilityFunctions", "isPlatformBrowser", "jwt_decode", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtemplate", "AppComponent_ng_template_2_ng_container_0_section_1_app_mtn_header_2_Template", "ɵɵelementEnd", "ɵɵlistener", "AppComponent_ng_template_2_ng_container_0_section_1_Template_app_mtn_footer_scrollToTop_8_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "scrollToTop", "AppComponent_ng_template_2_ng_container_0_section_1_app_mobile_navbar_9_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "showHeader", "ɵɵpureFunction2", "_c1", "sliderConfig", "isNavbarDataActive", "footerDetails", "isSpecificRoute", "desktopView", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "AppComponent_ng_template_2_ng_container_0_section_1_Template", "AppComponent_ng_template_2_ng_container_0_ng_container_2_Template", "ctx_r3", "initializeApp", "showTenantSelection", "includes", "title", "tenantId", "AppComponent_ng_template_2_ng_container_0_Template", "AppComponent_ng_template_2_app_maintenance_1_Template", "ctx_r2", "isShowMaintenance", "AppComponent", "onResize", "event", "screenWidth", "target", "innerWidth", "isMobile<PERSON>iew", "constructor", "appTitle", "tenantService", "cd", "activatedRoute", "router", "cookieService", "authTokenService", "store", "mainDataService", "modalService", "permissionService", "authService", "platformId", "meta", "loaderService", "sitemapService", "appDataService", "renderer", "http", "$gtmService", "waitForTenantSubscription", "mainLogo", "mainLogoMobile", "copyRights", "copyRightsMobile", "sideLogo", "bottonLogo", "payments", "socials", "icon", "url", "isBannerActive", "maintenanceMode", "window", "isMobileTemplate", "screen", "width", "triggerMaintenanceMode", "isStoreCloud", "$", "addClass", "localStorage", "setItem", "defaultTenant", "push", "setTitle", "getItem", "ngOnInit", "addTag", "name", "content", "authToken", "get", "removeItem", "initializeApplication", "show", "updateApiExpiry", "showCookiePopup", "trigger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryParams", "subscribe", "profile", "JSON", "parse", "replace", "decoded", "days", "exp", "toFixed", "dateNow", "Date", "setDate", "getDate", "parseInt", "set", "expires", "path", "sameSite", "isGuest", "logOut", "loadAppData", "setTimeout", "tenant", "getBannerData", "data", "detectChanges", "<PERSON><PERSON><PERSON><PERSON>", "resp", "assignSessionId", "assignConsentSessionId", "scroll", "nativeElement", "scrollTop", "_this", "_asyncToGenerator", "isMarketplace", "getShopSettingsData", "next", "res", "shopSettingData", "undefined", "getAppConfigurationData", "getAllFeatureByTenant", "_ref", "success", "permissions", "map", "perm", "portalName", "setPermissions", "hasPermission", "getConfigurationWithoutCache", "getTagsData", "hide", "_x", "apply", "arguments", "error", "err", "getAllGoogleTagConfigurations", "isActive", "tag", "setTagFeature", "console", "cookieModalData", "initialState", "cookieModalDetails", "header", "message", "backdrop", "ignoreBackdropClick", "class", "bsModalRefCookie", "submit", "pipe", "value", "ngOnDestroy", "tenantSubscription", "unsubscribe", "sessionStorage", "clear", "authTokenSet", "delete", "setCartLenghtData", "setUserData", "getAllowedFeatures", "_this2", "apiExpiry", "getTime", "toString", "generateSitemap", "allRoutes", "config", "relevantRoutes", "filterRelevantRoutes", "sitemapContent", "routes", "length", "children", "filter", "route", "startsWith", "specificRoutePath", "currentRoute", "some", "Number", "item", "localMaintenanceTenant", "injectGTM", "_", "ɵɵdirectiveInject", "i1", "Title", "i2", "TenantService", "ChangeDetectorRef", "i3", "ActivatedRoute", "Router", "i4", "CookieService", "AuthTokenService", "StoreService", "MainDataService", "i5", "BsModalService", "PermissionService", "AuthService", "Meta", "LoaderService", "SitemapService", "AppDataService", "Renderer2", "i6", "HttpClient", "i7", "GTMService", "_2", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "$event", "ɵɵresolveWindow", "AppComponent_app_mtn_tenant_configuration_1_Template", "AppComponent_ng_template_2_Template", "ɵɵtemplateRefExtractor", "_r1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\app.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\app.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectionStrategy,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef, HostListener,\r\n  Inject,\r\n  PLATFORM_ID, Renderer2,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport {Title} from '@angular/platform-browser';\r\nimport {firstValueFrom, Subscription, take} from \"rxjs\";\r\nimport {ActivatedRoute, NavigationEnd, NavigationStart, Router, Routes} from \"@angular/router\";\r\nimport {CookieService} from \"ngx-cookie-service\";\r\n\r\nimport {environment} from 'src/environments/environment';\r\n\r\nimport {\r\n  AuthService,\r\n  AuthTokenService, LoaderService,\r\n  MainDataService,\r\n  StoreService,\r\n  TenantService,\r\n  SitemapService,\r\n  PermissionService, AppDataService\r\n} from \"@core/services\";\r\nimport {BsModalRef, BsModalService, ModalOptions} from \"ngx-bootstrap/modal\";\r\nimport {CookieModalComponent} from \"@shared/modals/cookie-modal/cookie-modal.component\";\r\nimport UtilityFunctions from \"@core/utilities/functions\";\r\nimport {isPlatformBrowser} from \"@angular/common\";\r\nimport {Meta} from '@angular/platform-browser';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { GTMService } from '@core/services/gtm.service';\r\n\r\ndeclare let $: any;\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class AppComponent {\r\n  @ViewChild('scroll', {read: ElementRef}) public scroll!: ElementRef<any>;\r\n  screenWidth?: any;\r\n  desktopView: boolean;\r\n  showTenantSelection: any = [];\r\n  tenantId: any;\r\n  title: string;\r\n  tenantSubscription: Subscription;\r\n  showHeader: boolean = false;\r\n  initializeApp: boolean = false;\r\n  waitForTenantSubscription: boolean = false;\r\n  token: any;\r\n\r\n  footerDetails: any = {\r\n    mainLogo: 'assets/icons/momo-marketplace-footer-logo.png',\r\n    mainLogoMobile:'assets/icons/new-logo-marketplace-mobile.svg',\r\n    copyRights: 'footer.copyRightsMobile',\r\n    copyRightsMobile:\"footer.copyRightsMobile\",\r\n    sideLogo: 'assets/icons/footer-momo-logo.svg',\r\n    bottonLogo:'assets/icons/footer-momo-logo-mobile.svg',\r\n    payments: ['assets/icons/Momo-pay-logo.svg',\r\n      'assets/icons/Mastercard-logo.svg',\r\n      'assets/icons/Visa-logo.svg'],\r\n    socials:[\r\n      {\r\n        icon: 'assets/icons/facebook-mb-icon.svg',\r\n        url: 'https://www.facebook.com/'\r\n      },\r\n      // {\r\n      //   icon:'assets/icons/insta-mb-icon.svg',\r\n      //   url: 'https://www.instagram.com/'\r\n      // },\r\n      // {\r\n      //   icon:'assets/icons/linkedin-mb-icon.svg',\r\n      //   url: 'https://www.linkedin.com/'\r\n      // }\r\n    ]\r\n\r\n  }\r\n  sliderConfig: any = {\r\n    isBannerActive: false,\r\n    isNavbarDataActive: false\r\n  }\r\n  maintenanceMode: any[] = [];\r\n  isMobileView = window.innerWidth <= 768;\r\n  isMobileTemplate: boolean = false;\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: any) {\r\n    this.screenWidth = event.target.innerWidth;\r\n    this.isMobileView = this.screenWidth <= 768;\r\n  }\r\n  constructor(private appTitle: Title,\r\n              private tenantService: TenantService,\r\n              private cd: ChangeDetectorRef,\r\n              private activatedRoute: ActivatedRoute,\r\n              public router: Router,\r\n              private cookieService: CookieService,\r\n              private authTokenService: AuthTokenService,\r\n              private store: StoreService,\r\n              private mainDataService: MainDataService,\r\n              private modalService: BsModalService,\r\n              private permissionService: PermissionService,\r\n              private authService: AuthService,\r\n              @Inject(PLATFORM_ID) private platformId: any,\r\n              private meta: Meta,\r\n              private loaderService: LoaderService,\r\n              private sitemapService: SitemapService,\r\n              private appDataService: AppDataService,\r\n              private renderer: Renderer2,\r\n              private http: HttpClient,\r\n              private $gtmService:GTMService) {\r\n      if (isPlatformBrowser(this.platformId)) {\r\n        this.screenWidth = window.screen.width;\r\n        if (this.screenWidth >= 768) {\r\n          this.desktopView = true;\r\n        } else {\r\n          this.desktopView = false;\r\n        }\r\n    }\r\n    this.triggerMaintenanceMode()\r\n\r\n    if (environment.isStoreCloud) {\r\n      this.title = 'Yalla Mall';\r\n      $('body').addClass('storecloud');\r\n      localStorage.setItem('tenantId', environment.defaultTenant)\r\n    } else {\r\n      this.title = 'Market by MoMo';\r\n      this.showTenantSelection.push(this.title);\r\n    }\r\n\r\n    appTitle.setTitle(this.title);\r\n    this.tenantId = localStorage.getItem('tenantId')\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.meta.addTag({ name: 'title', content: 'Market by MoMo' });\r\n    this.meta.addTag({ name: 'description', content: 'E-commerce platform' });\r\n    \r\n    let authToken: any = this.cookieService.get('authToken');\r\n    if(!authToken){\r\n      localStorage.setItem('isGuest', 'true');\r\n    }\r\n    else{\r\n      localStorage.removeItem('isGuest');\r\n    }\r\n  }\r\n\r\n  initializeApplication() {\r\n    this.loaderService.show()\r\n    this.updateApiExpiry();\r\n\r\n    // TODO: Generate updated sitemaps from the following method\r\n    // this.generateSitemap()\r\n\r\n    const showCookiePopup = localStorage.getItem('save_cookie')\r\n    if (!showCookiePopup) {\r\n      this.triggerCookiePopup()\r\n    }\r\n    this.activatedRoute.queryParams.subscribe(queryParams => {\r\n\r\n      if (queryParams.tenantId) {\r\n        this.tenantId = queryParams.tenantId || environment.defaultTenant\r\n        localStorage.setItem('tenantId', this.tenantId)\r\n        this.waitForTenantSubscription = true\r\n        let authToken: any = this.cookieService.get('authToken');\r\n        if(!authToken) {\r\n          let profile: any = localStorage.getItem('profile')\r\n          if(profile && profile !== '') {\r\n            profile = JSON.parse(profile)\r\n            authToken = profile.authToken.replace('bearer ', '')\r\n            const decoded: any = jwt_decode(authToken);\r\n\r\n            let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(\r\n              0\r\n            );\r\n            const dateNow = new Date();\r\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n            this.cookieService.set('authToken', authToken, {\r\n              expires: dateNow,\r\n              path: '/',\r\n              sameSite: 'Strict',\r\n            });\r\n          }\r\n        }\r\n        if (!authToken) {\r\n          let isGuest: any = localStorage.getItem('isGuest');\r\n          if(!isGuest){\r\n            this.logOut()\r\n          }          \r\n          this.loadAppData()\r\n        } else {\r\n          this.loadAppData()\r\n        }\r\n      } else {\r\n        setTimeout(() => {\r\n          if (!this.waitForTenantSubscription) {\r\n            this.loadAppData()\r\n          }\r\n        }, 500)\r\n      }\r\n\r\n    });\r\n    if (environment.isStoreCloud) {\r\n      this.title = 'Yalla Mall';\r\n      $('body').addClass('storecloud');\r\n      let tenant = localStorage.getItem('tenantId');\r\n      if(!tenant){\r\n         localStorage.setItem('tenantId', environment.defaultTenant)\r\n      }\r\n    } else {\r\n      this.title = 'Market by MoMo';\r\n      this.showTenantSelection.push(this.title);\r\n    }\r\n\r\n    this.mainDataService.getBannerData().subscribe((data: any) => {\r\n      if (data?.isBannerActive) {\r\n        this.sliderConfig.isBannerActive = data.isBannerActive\r\n      }\r\n      if (data?.isNavbarDataActive) {\r\n        this.sliderConfig.isNavbarDataActive = data.isNavbarDataActive\r\n      }\r\n      this.cd.detectChanges();\r\n    })\r\n\r\n    // this.tenantSubscription = this.tenantService.getTenant()\r\n    //   .subscribe(response => {\r\n\r\n    //     this.tenantId = response\r\n    //     localStorage.setItem('tenantId', this.tenantId)\r\n    //     this.loadAppData()\r\n    //   })\r\n      this.tenantId = localStorage.getItem('tenantId')\r\n      if(this.tenantId){\r\n        this.loadAppData()\r\n      }\r\n\r\n    this.tenantService.getHeader()\r\n      .subscribe((resp: any) => {\r\n        this.showHeader = resp\r\n        this.cd.detectChanges()\r\n      })\r\n\r\n    // this.loadAppData()\r\n\r\n    UtilityFunctions.assignSessionId();\r\n    UtilityFunctions.assignConsentSessionId();\r\n  }\r\n\r\n  scrollToTop() {\r\n    this.scroll.nativeElement.scrollTop = 0;\r\n  }\r\n\r\n  async loadAppData() {\r\n    if (!this.authService.isMarketplace()) {\r\n      await this.authService.getShopSettingsData().subscribe({\r\n        next: (res: any) => {\r\n          if(res.data) {\r\n            this.appDataService.shopSettingData = res.data\r\n            this.cd.detectChanges()\r\n          }\r\n        }\r\n      })\r\n    }\r\n    if (this.tenantId !== null && this.tenantId !== '' && this.tenantId != undefined) {\r\n      await this.tenantService.getAppConfigurationData(this.isMobileView);\r\n      this.authService.getAllFeatureByTenant().subscribe({\r\n        next: async (res: any) => {\r\n          if (res.success) {\r\n            const permissions = res.data.map((perm: any) => perm.portalName);\r\n            this.permissionService.setPermissions(permissions);\r\n            this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n\r\n            await this.tenantService.getConfigurationWithoutCache(this.isMobileView && this.permissionService.hasPermission('Mobile-Layout'))\r\n\r\n            if (this.permissionService.hasPermission('Google Analytics')) this.getTagsData()\r\n            this.initializeApp = true\r\n\r\n            this.loaderService.hide()\r\n\r\n            this.cd.detectChanges()\r\n          }\r\n        },\r\n        error: (err: any) => {\r\n          this.initializeApp = true;\r\n        }\r\n      });\r\n      // await this.getAllowedFeatures();\r\n\r\n    }\r\n  }\r\ngetTagsData(){\r\n    this.mainDataService.getAllGoogleTagConfigurations().subscribe({\r\n      next: (res: any) => {\r\n        const permissions = res.data.map((perm: any) => {\r\n          if (perm.isActive) {\r\n            return perm.tag;\r\n          }\r\n        });\r\n        this.permissionService.setTagFeature(permissions);\r\n      }, error: (err: any) => {\r\n        console.error(err)\r\n      }\r\n    })\r\n}\r\n  triggerCookiePopup() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      const cookieModalData: ModalOptions = {\r\n        initialState: {\r\n          cookieModalDetails: {\r\n            header: \"We Value your privacy\",\r\n            message: 'We use cookies to ensure you get the best experience on our website. By using our website you agree to your cookie.'\r\n          }\r\n        },\r\n        backdrop: 'static',\r\n        ignoreBackdropClick: true,\r\n        class: \"cookie-popup-width\"\r\n      };\r\n      const bsModalRefCookie: BsModalRef = this.modalService.show(CookieModalComponent, cookieModalData);\r\n      bsModalRefCookie.content.submit.pipe(take(1)).subscribe((value: boolean) => {\r\n      });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      this.tenantSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  logOut() {\r\n    sessionStorage.clear();\r\n    this.authTokenService.authTokenSet('');\r\n\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('profile', '');\r\n    this.mainDataService.setCartLenghtData(null);\r\n    this.mainDataService.setUserData(null);\r\n    localStorage.setItem('secondaryDefault', 'false')\r\n    localStorage.setItem('sessionId', '');\r\n    localStorage.setItem('addedProducts', '');\r\n    localStorage.setItem('cartId', '');\r\n    this.store.set('cartProducts', []);\r\n    localStorage.removeItem('refreshToken');\r\n    localStorage.removeItem('auth_enc');\r\n    localStorage.setItem('isGuest', 'true');\r\n  }\r\n\r\n  async getAllowedFeatures(): Promise<any> {\r\n    this.authService.getAllFeatureByTenant().subscribe({\r\n      next: (res: any) => {\r\n        if (res.success) {\r\n          const permissions = res.data.map((perm: any) => perm.portalName);\r\n          this.permissionService.setPermissions(permissions);\r\n          this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.initializeApp = true;\r\n      }\r\n    });\r\n  }\r\n\r\n  updateApiExpiry() {\r\n    const apiExpiry = localStorage.getItem('apiExpiry')\r\n    if (!apiExpiry && apiExpiry === '') {\r\n      localStorage.setItem('apiExpiry', new Date().getTime().toString())\r\n    }\r\n  }\r\n\r\n  generateSitemap() {\r\n    const allRoutes: Routes = this.router.config;\r\n    const relevantRoutes = this.filterRelevantRoutes(allRoutes); // Optionally filter out irrelevant routes\r\n    const sitemapContent = this.sitemapService.generateSitemap(relevantRoutes);\r\n  }\r\n\r\n  filterRelevantRoutes(routes: any) {\r\n    if (routes && routes.length > 0 && routes[0].children)\r\n      return routes[0].children.filter((route: any) => {\r\n        return !route.path.startsWith('category/') &&\r\n          !route.path.startsWith('product/') &&\r\n          !route.path.startsWith('order/') &&\r\n          !route.path.startsWith('**');\r\n      });\r\n  }\r\n  isSpecificRoute(): boolean {\r\n    const specificRoutePath = ['/categories-list','/cart','/account','/wishlist','/checkout','/account/address','/account/verify-address','/account/address/add-address','/account/help'];\r\n    const currentRoute = this.router.url;\r\n    // console.log(currentRoute , specificRoutePath);\r\n    return specificRoutePath.some((route:string)=>route == currentRoute);\r\n  }\r\n\r\n  isShowMaintenance(): boolean {\r\n    let tenantId = Number(localStorage.getItem('tenantId'));\r\n    return this.maintenanceMode.some((item: any) => {\r\n      if (item.tenantId === tenantId) {\r\n        return item.maintenanceMode\r\n      }\r\n    })\r\n  }\r\n\r\n  triggerMaintenanceMode() {\r\n      const localMaintenanceTenant = localStorage.getItem('maintenanceModeTenant')\r\n    if (localMaintenanceTenant && localMaintenanceTenant !== '') {\r\n      this.maintenanceMode = JSON.parse(localMaintenanceTenant)\r\n      setTimeout(() => {\r\n        if (!this.isShowMaintenance()) {\r\n          this.initializeApplication()\r\n        }\r\n        this.cd.detectChanges()\r\n      }, 500)\r\n    } else {\r\n      this.http.get<any>('assets/data.json').subscribe((data: any) => {\r\n        this.$gtmService.injectGTM(data,this.tenantId)\r\n        this.maintenanceMode = data;\r\n        if (!this.isShowMaintenance()) {\r\n          this.initializeApplication()\r\n        }\r\n        this.cd.detectChanges()\r\n      });\r\n    }\r\n  }\r\n\r\n}\r\n", "<ng-container>\r\n  <app-mtn-tenant-configuration *ngIf=\"\r\n    showTenantSelection.includes(title) &&\r\n      (tenantId === null || tenantId === '');\r\n    else showLandingPage\r\n  \"></app-mtn-tenant-configuration>\r\n\r\n  <ng-template #showLandingPage>\r\n    <ng-container *ngIf=\"!isShowMaintenance()\">\r\n      <section #scroll *ngIf=\"initializeApp\" class=\"container mw-100 px-0\">\r\n        <app-mtn-header *ngIf=\"showHeader\"></app-mtn-header>\r\n        <div [ngClass]=\"{'page-navbar-active': sliderConfig.isNavbarDataActive, 'page-navbar-inactive': !sliderConfig.isNavbarDataActive}\"\r\n             class=\"page\">\r\n          <p-toast [autoZIndex]=\"true\" position=\"top-right\" sticky=\"true\"></p-toast>\r\n          <div id=\"zoom-container\">\r\n            <div class=\"mobile-bg-border\">\r\n              <router-outlet></router-outlet>\r\n            </div>\r\n          </div>\r\n\r\n          <app-mtn-footer (scrollToTop)=\"scrollToTop()\" [footerDetails]=\"footerDetails\"\r\n                          [toHide]=\"isSpecificRoute()\"></app-mtn-footer>\r\n          <app-mobile-navbar *ngIf=\"!desktopView\"></app-mobile-navbar>\r\n        </div>\r\n      </section>\r\n      <ng-container\r\n        *ngIf=\"!initializeApp && !(showTenantSelection.includes(title) && (tenantId === null || tenantId === ''))\">\r\n        <app-mtn-loader></app-mtn-loader>\r\n      </ng-container>\r\n    </ng-container>\r\n    <app-maintenance *ngIf=\"isShowMaintenance()\"></app-maintenance>\r\n  </ng-template>\r\n\r\n</ng-container>\r\n\r\n"], "mappings": ";AAAA,SAIEA,UAAU,EAEVC,WAAW,QAEN,eAAe;AAEtB,SAAsCC,IAAI,QAAO,MAAM;AAIvD,SAAQC,WAAW,QAAO,8BAA8B;AAYxD,SAAQC,oBAAoB,QAAO,oDAAoD;AACvF,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,SAAQC,iBAAiB,QAAO,iBAAiB;AAGjD,OAAOC,UAAU,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;IC9BjCC,EAAA,CAAAC,SAAA,mCAIiC;;;;;IAK3BD,EAAA,CAAAC,SAAA,qBAAoD;;;;;IAYlDD,EAAA,CAAAC,SAAA,wBAA4D;;;;;;;;;;;;IAbhED,EAAA,CAAAE,cAAA,oBAAqE;IACnEF,EAAA,CAAAG,UAAA,IAAAC,6EAAA,4BAAoD;IACpDJ,EAAA,CAAAE,cAAA,aACkB;IAChBF,EAAA,CAAAC,SAAA,iBAA0E;IAC1ED,EAAA,CAAAE,cAAA,aAAyB;IAErBF,EAAA,CAAAC,SAAA,oBAA+B;IACjCD,EAAA,CAAAK,YAAA,EAAM;IAGRL,EAAA,CAAAE,cAAA,yBAC6C;IAD7BF,EAAA,CAAAM,UAAA,yBAAAC,mGAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAeX,EAAA,CAAAY,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACAb,EAAA,CAAAK,YAAA,EAAiB;IAC9DL,EAAA,CAAAG,UAAA,IAAAW,gFAAA,+BAA4D;IAC9Dd,EAAA,CAAAK,YAAA,EAAM;;;;IAbWL,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAgB,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAgB;IAC5BlB,EAAA,CAAAe,SAAA,GAA6H;IAA7Hf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAAH,MAAA,CAAAI,YAAA,CAAAC,kBAAA,GAAAL,MAAA,CAAAI,YAAA,CAAAC,kBAAA,EAA6H;IAEvHtB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,UAAA,oBAAmB;IAOkBhB,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAgB,UAAA,kBAAAC,MAAA,CAAAM,aAAA,CAA+B,WAAAN,MAAA,CAAAO,eAAA;IAEzDxB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,UAAA,UAAAC,MAAA,CAAAQ,WAAA,CAAkB;;;;;IAG1CzB,EAAA,CAAA0B,uBAAA,GAC6G;IAC3G1B,EAAA,CAAAC,SAAA,qBAAiC;IACnCD,EAAA,CAAA2B,qBAAA,EAAe;;;;;IApBjB3B,EAAA,CAAA0B,uBAAA,GAA2C;IACzC1B,EAAA,CAAAG,UAAA,IAAAyB,4DAAA,sBAeU;IACV5B,EAAA,CAAAG,UAAA,IAAA0B,iEAAA,0BAGe;IACjB7B,EAAA,CAAA2B,qBAAA,EAAe;;;;IApBK3B,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,UAAA,SAAAc,MAAA,CAAAC,aAAA,CAAmB;IAiBlC/B,EAAA,CAAAe,SAAA,GAAwG;IAAxGf,EAAA,CAAAgB,UAAA,UAAAc,MAAA,CAAAC,aAAA,MAAAD,MAAA,CAAAE,mBAAA,CAAAC,QAAA,CAAAH,MAAA,CAAAI,KAAA,MAAAJ,MAAA,CAAAK,QAAA,aAAAL,MAAA,CAAAK,QAAA,UAAwG;;;;;IAI7GnC,EAAA,CAAAC,SAAA,sBAA+D;;;;;IAtB/DD,EAAA,CAAAG,UAAA,IAAAiC,kDAAA,0BAqBe;IACfpC,EAAA,CAAAG,UAAA,IAAAkC,qDAAA,6BAA+D;;;;IAtBhDrC,EAAA,CAAAgB,UAAA,UAAAsB,MAAA,CAAAC,iBAAA,GAA0B;IAsBvBvC,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,UAAA,SAAAsB,MAAA,CAAAC,iBAAA,GAAyB;;;ADY/C,OAAM,MAAOC,YAAY;EA+CvBC,QAAQA,CAACC,KAAU;IACjB,IAAI,CAACC,WAAW,GAAGD,KAAK,CAACE,MAAM,CAACC,UAAU;IAC1C,IAAI,CAACC,YAAY,GAAG,IAAI,CAACH,WAAW,IAAI,GAAG;EAC7C;EACAI,YAAoBC,QAAe,EACfC,aAA4B,EAC5BC,EAAqB,EACrBC,cAA8B,EAC/BC,MAAc,EACbC,aAA4B,EAC5BC,gBAAkC,EAClCC,KAAmB,EACnBC,eAAgC,EAChCC,YAA4B,EAC5BC,iBAAoC,EACpCC,WAAwB,EACHC,UAAe,EACpCC,IAAU,EACVC,aAA4B,EAC5BC,cAA8B,EAC9BC,cAA8B,EAC9BC,QAAmB,EACnBC,IAAgB,EAChBC,WAAsB;IAnBtB,KAAAnB,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACU,KAAAC,UAAU,GAAVA,UAAU;IAC/B,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAlE/B,KAAAnC,mBAAmB,GAAQ,EAAE;IAI7B,KAAAd,UAAU,GAAY,KAAK;IAC3B,KAAAa,aAAa,GAAY,KAAK;IAC9B,KAAAqC,yBAAyB,GAAY,KAAK;IAG1C,KAAA7C,aAAa,GAAQ;MACnB8C,QAAQ,EAAE,+CAA+C;MACzDC,cAAc,EAAC,8CAA8C;MAC7DC,UAAU,EAAE,yBAAyB;MACrCC,gBAAgB,EAAC,yBAAyB;MAC1CC,QAAQ,EAAE,mCAAmC;MAC7CC,UAAU,EAAC,0CAA0C;MACrDC,QAAQ,EAAE,CAAC,gCAAgC,EACzC,kCAAkC,EAClC,4BAA4B,CAAC;MAC/BC,OAAO,EAAC,CACN;QACEC,IAAI,EAAE,mCAAmC;QACzCC,GAAG,EAAE;;MAEP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA;KAGH;;IACD,KAAAzD,YAAY,GAAQ;MAClB0D,cAAc,EAAE,KAAK;MACrBzD,kBAAkB,EAAE;KACrB;IACD,KAAA0D,eAAe,GAAU,EAAE;IAC3B,KAAAlC,YAAY,GAAGmC,MAAM,CAACpC,UAAU,IAAI,GAAG;IACvC,KAAAqC,gBAAgB,GAAY,KAAK;IA0B7B,IAAIpF,iBAAiB,CAAC,IAAI,CAAC8D,UAAU,CAAC,EAAE;MACtC,IAAI,CAACjB,WAAW,GAAGsC,MAAM,CAACE,MAAM,CAACC,KAAK;MACtC,IAAI,IAAI,CAACzC,WAAW,IAAI,GAAG,EAAE;QAC3B,IAAI,CAAClB,WAAW,GAAG,IAAI;OACxB,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,KAAK;;;IAG9B,IAAI,CAAC4D,sBAAsB,EAAE;IAE7B,IAAI1F,WAAW,CAAC2F,YAAY,EAAE;MAC5B,IAAI,CAACpD,KAAK,GAAG,YAAY;MACzBqD,CAAC,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC;MAChCC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE/F,WAAW,CAACgG,aAAa,CAAC;KAC5D,MAAM;MACL,IAAI,CAACzD,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACF,mBAAmB,CAAC4D,IAAI,CAAC,IAAI,CAAC1D,KAAK,CAAC;;IAG3Cc,QAAQ,CAAC6C,QAAQ,CAAC,IAAI,CAAC3D,KAAK,CAAC;IAC7B,IAAI,CAACC,QAAQ,GAAGsD,YAAY,CAACK,OAAO,CAAC,UAAU,CAAC;EAElD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAClC,IAAI,CAACmC,MAAM,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAgB,CAAE,CAAC;IAC9D,IAAI,CAACrC,IAAI,CAACmC,MAAM,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAqB,CAAE,CAAC;IAEzE,IAAIC,SAAS,GAAQ,IAAI,CAAC9C,aAAa,CAAC+C,GAAG,CAAC,WAAW,CAAC;IACxD,IAAG,CAACD,SAAS,EAAC;MACZV,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;KACxC,MACG;MACFD,YAAY,CAACY,UAAU,CAAC,SAAS,CAAC;;EAEtC;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAACxC,aAAa,CAACyC,IAAI,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA;IAEA,MAAMC,eAAe,GAAGhB,YAAY,CAACK,OAAO,CAAC,aAAa,CAAC;IAC3D,IAAI,CAACW,eAAe,EAAE;MACpB,IAAI,CAACC,kBAAkB,EAAE;;IAE3B,IAAI,CAACvD,cAAc,CAACwD,WAAW,CAACC,SAAS,CAACD,WAAW,IAAG;MAEtD,IAAIA,WAAW,CAACxE,QAAQ,EAAE;QACxB,IAAI,CAACA,QAAQ,GAAGwE,WAAW,CAACxE,QAAQ,IAAIxC,WAAW,CAACgG,aAAa;QACjEF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,IAAI,CAACvD,QAAQ,CAAC;QAC/C,IAAI,CAACiC,yBAAyB,GAAG,IAAI;QACrC,IAAI+B,SAAS,GAAQ,IAAI,CAAC9C,aAAa,CAAC+C,GAAG,CAAC,WAAW,CAAC;QACxD,IAAG,CAACD,SAAS,EAAE;UACb,IAAIU,OAAO,GAAQpB,YAAY,CAACK,OAAO,CAAC,SAAS,CAAC;UAClD,IAAGe,OAAO,IAAIA,OAAO,KAAK,EAAE,EAAE;YAC5BA,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;YAC7BV,SAAS,GAAGU,OAAO,CAACV,SAAS,CAACa,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;YACpD,MAAMC,OAAO,GAAQlH,UAAU,CAACoG,SAAS,CAAC;YAE1C,IAAIe,IAAI,GAAQ,CAACD,OAAO,CAACE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAEC,OAAO,CAC3D,CAAC,CACF;YACD,MAAMC,OAAO,GAAG,IAAIC,IAAI,EAAE;YAC1BD,OAAO,CAACE,OAAO,CAACF,OAAO,CAACG,OAAO,EAAE,GAAGC,QAAQ,CAACP,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC7D,aAAa,CAACqE,GAAG,CAAC,WAAW,EAAEvB,SAAS,EAAE;cAC7CwB,OAAO,EAAEN,OAAO;cAChBO,IAAI,EAAE,GAAG;cACTC,QAAQ,EAAE;aACX,CAAC;;;QAGN,IAAI,CAAC1B,SAAS,EAAE;UACd,IAAI2B,OAAO,GAAQrC,YAAY,CAACK,OAAO,CAAC,SAAS,CAAC;UAClD,IAAG,CAACgC,OAAO,EAAC;YACV,IAAI,CAACC,MAAM,EAAE;;UAEf,IAAI,CAACC,WAAW,EAAE;SACnB,MAAM;UACL,IAAI,CAACA,WAAW,EAAE;;OAErB,MAAM;QACLC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC,IAAI,CAAC7D,yBAAyB,EAAE;YACnC,IAAI,CAAC4D,WAAW,EAAE;;QAEtB,CAAC,EAAE,GAAG,CAAC;;IAGX,CAAC,CAAC;IACF,IAAIrI,WAAW,CAAC2F,YAAY,EAAE;MAC5B,IAAI,CAACpD,KAAK,GAAG,YAAY;MACzBqD,CAAC,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC;MAChC,IAAI0C,MAAM,GAAGzC,YAAY,CAACK,OAAO,CAAC,UAAU,CAAC;MAC7C,IAAG,CAACoC,MAAM,EAAC;QACRzC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE/F,WAAW,CAACgG,aAAa,CAAC;;KAE/D,MAAM;MACL,IAAI,CAACzD,KAAK,GAAG,gBAAgB;MAC7B,IAAI,CAACF,mBAAmB,CAAC4D,IAAI,CAAC,IAAI,CAAC1D,KAAK,CAAC;;IAG3C,IAAI,CAACsB,eAAe,CAAC2E,aAAa,EAAE,CAACvB,SAAS,CAAEwB,IAAS,IAAI;MAC3D,IAAIA,IAAI,EAAErD,cAAc,EAAE;QACxB,IAAI,CAAC1D,YAAY,CAAC0D,cAAc,GAAGqD,IAAI,CAACrD,cAAc;;MAExD,IAAIqD,IAAI,EAAE9G,kBAAkB,EAAE;QAC5B,IAAI,CAACD,YAAY,CAACC,kBAAkB,GAAG8G,IAAI,CAAC9G,kBAAkB;;MAEhE,IAAI,CAAC4B,EAAE,CAACmF,aAAa,EAAE;IACzB,CAAC,CAAC;IAEF;IACA;IAEA;IACA;IACA;IACA;IACE,IAAI,CAAClG,QAAQ,GAAGsD,YAAY,CAACK,OAAO,CAAC,UAAU,CAAC;IAChD,IAAG,IAAI,CAAC3D,QAAQ,EAAC;MACf,IAAI,CAAC6F,WAAW,EAAE;;IAGtB,IAAI,CAAC/E,aAAa,CAACqF,SAAS,EAAE,CAC3B1B,SAAS,CAAE2B,IAAS,IAAI;MACvB,IAAI,CAACrH,UAAU,GAAGqH,IAAI;MACtB,IAAI,CAACrF,EAAE,CAACmF,aAAa,EAAE;IACzB,CAAC,CAAC;IAEJ;IAEAxI,gBAAgB,CAAC2I,eAAe,EAAE;IAClC3I,gBAAgB,CAAC4I,sBAAsB,EAAE;EAC3C;EAEA5H,WAAWA,CAAA;IACT,IAAI,CAAC6H,MAAM,CAACC,aAAa,CAACC,SAAS,GAAG,CAAC;EACzC;EAEMZ,WAAWA,CAAA;IAAA,IAAAa,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI,CAACD,KAAI,CAAClF,WAAW,CAACoF,aAAa,EAAE,EAAE;QACrC,MAAMF,KAAI,CAAClF,WAAW,CAACqF,mBAAmB,EAAE,CAACpC,SAAS,CAAC;UACrDqC,IAAI,EAAGC,GAAQ,IAAI;YACjB,IAAGA,GAAG,CAACd,IAAI,EAAE;cACXS,KAAI,CAAC7E,cAAc,CAACmF,eAAe,GAAGD,GAAG,CAACd,IAAI;cAC9CS,KAAI,CAAC3F,EAAE,CAACmF,aAAa,EAAE;;UAE3B;SACD,CAAC;;MAEJ,IAAIQ,KAAI,CAAC1G,QAAQ,KAAK,IAAI,IAAI0G,KAAI,CAAC1G,QAAQ,KAAK,EAAE,IAAI0G,KAAI,CAAC1G,QAAQ,IAAIiH,SAAS,EAAE;QAChF,MAAMP,KAAI,CAAC5F,aAAa,CAACoG,uBAAuB,CAACR,KAAI,CAAC/F,YAAY,CAAC;QACnE+F,KAAI,CAAClF,WAAW,CAAC2F,qBAAqB,EAAE,CAAC1C,SAAS,CAAC;UACjDqC,IAAI;YAAA,IAAAM,IAAA,GAAAT,iBAAA,CAAE,WAAOI,GAAQ,EAAI;cACvB,IAAIA,GAAG,CAACM,OAAO,EAAE;gBACf,MAAMC,WAAW,GAAGP,GAAG,CAACd,IAAI,CAACsB,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,UAAU,CAAC;gBAChEf,KAAI,CAACnF,iBAAiB,CAACmG,cAAc,CAACJ,WAAW,CAAC;gBAClDZ,KAAI,CAAC3D,gBAAgB,GAAG2D,KAAI,CAACnF,iBAAiB,CAACoG,aAAa,CAAC,eAAe,CAAC;gBAE7E,MAAMjB,KAAI,CAAC5F,aAAa,CAAC8G,4BAA4B,CAAClB,KAAI,CAAC/F,YAAY,IAAI+F,KAAI,CAACnF,iBAAiB,CAACoG,aAAa,CAAC,eAAe,CAAC,CAAC;gBAEjI,IAAIjB,KAAI,CAACnF,iBAAiB,CAACoG,aAAa,CAAC,kBAAkB,CAAC,EAAEjB,KAAI,CAACmB,WAAW,EAAE;gBAChFnB,KAAI,CAAC9G,aAAa,GAAG,IAAI;gBAEzB8G,KAAI,CAAC/E,aAAa,CAACmG,IAAI,EAAE;gBAEzBpB,KAAI,CAAC3F,EAAE,CAACmF,aAAa,EAAE;;YAE3B,CAAC;YAAA,gBAfDY,IAAIA,CAAAiB,EAAA;cAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;YAAA;UAAA,GAeH;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClBzB,KAAI,CAAC9G,aAAa,GAAG,IAAI;UAC3B;SACD,CAAC;QACF;;IAED;EACH;;EACFiI,WAAWA,CAAA;IACP,IAAI,CAACxG,eAAe,CAAC+G,6BAA6B,EAAE,CAAC3D,SAAS,CAAC;MAC7DqC,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMO,WAAW,GAAGP,GAAG,CAACd,IAAI,CAACsB,GAAG,CAAEC,IAAS,IAAI;UAC7C,IAAIA,IAAI,CAACa,QAAQ,EAAE;YACjB,OAAOb,IAAI,CAACc,GAAG;;QAEnB,CAAC,CAAC;QACF,IAAI,CAAC/G,iBAAiB,CAACgH,aAAa,CAACjB,WAAW,CAAC;MACnD,CAAC;MAAEY,KAAK,EAAGC,GAAQ,IAAI;QACrBK,OAAO,CAACN,KAAK,CAACC,GAAG,CAAC;MACpB;KACD,CAAC;EACN;EACE5D,kBAAkBA,CAAA;IAChB,IAAI5G,iBAAiB,CAAC,IAAI,CAAC8D,UAAU,CAAC,EAAE;MACtC,MAAMgH,eAAe,GAAiB;QACpCC,YAAY,EAAE;UACZC,kBAAkB,EAAE;YAClBC,MAAM,EAAE,uBAAuB;YAC/BC,OAAO,EAAE;;SAEZ;QACDC,QAAQ,EAAE,QAAQ;QAClBC,mBAAmB,EAAE,IAAI;QACzBC,KAAK,EAAE;OACR;MACD,MAAMC,gBAAgB,GAAe,IAAI,CAAC3H,YAAY,CAAC8C,IAAI,CAAC3G,oBAAoB,EAAEgL,eAAe,CAAC;MAClGQ,gBAAgB,CAAClF,OAAO,CAACmF,MAAM,CAACC,IAAI,CAAC5L,IAAI,CAAC,CAAC,CAAC,CAAC,CAACkH,SAAS,CAAE2E,KAAc,IAAI,CAC3E,CAAC,CAAC;;EAEN;EAEAC,WAAWA,CAAA;IACT,IAAI1L,iBAAiB,CAAC,IAAI,CAAC8D,UAAU,CAAC,EAAE;MACtC,IAAI,CAAC6H,kBAAkB,CAACC,WAAW,EAAE;;EAEzC;EAEA3D,MAAMA,CAAA;IACJ4D,cAAc,CAACC,KAAK,EAAE;IACtB,IAAI,CAACtI,gBAAgB,CAACuI,YAAY,CAAC,EAAE,CAAC;IAEtC,IAAI,CAACxI,aAAa,CAACyI,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAC3C,IAAI,CAACvI,KAAK,CAACmE,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7B,IAAI,CAAClE,eAAe,CAACuI,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACvI,eAAe,CAACwI,WAAW,CAAC,IAAI,CAAC;IACtCvG,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC;IACjDD,YAAY,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACrCD,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;IACzCD,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAClC,IAAI,CAACnC,KAAK,CAACmE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCjC,YAAY,CAACY,UAAU,CAAC,cAAc,CAAC;IACvCZ,YAAY,CAACY,UAAU,CAAC,UAAU,CAAC;IACnCZ,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;EACzC;EAEMuG,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApD,iBAAA;MACtBoD,MAAI,CAACvI,WAAW,CAAC2F,qBAAqB,EAAE,CAAC1C,SAAS,CAAC;QACjDqC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAIA,GAAG,CAACM,OAAO,EAAE;YACf,MAAMC,WAAW,GAAGP,GAAG,CAACd,IAAI,CAACsB,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,UAAU,CAAC;YAChEsC,MAAI,CAACxI,iBAAiB,CAACmG,cAAc,CAACJ,WAAW,CAAC;YAClDyC,MAAI,CAAChH,gBAAgB,GAAGgH,MAAI,CAACxI,iBAAiB,CAACoG,aAAa,CAAC,eAAe,CAAC;;QAEjF,CAAC;QACDO,KAAK,EAAGC,GAAQ,IAAI;UAClB4B,MAAI,CAACnK,aAAa,GAAG,IAAI;QAC3B;OACD,CAAC;IAAC;EACL;EAEAyE,eAAeA,CAAA;IACb,MAAM2F,SAAS,GAAG1G,YAAY,CAACK,OAAO,CAAC,WAAW,CAAC;IACnD,IAAI,CAACqG,SAAS,IAAIA,SAAS,KAAK,EAAE,EAAE;MAClC1G,YAAY,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI4B,IAAI,EAAE,CAAC8E,OAAO,EAAE,CAACC,QAAQ,EAAE,CAAC;;EAEtE;EAEAC,eAAeA,CAAA;IACb,MAAMC,SAAS,GAAW,IAAI,CAACnJ,MAAM,CAACoJ,MAAM;IAC5C,MAAMC,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAACH,SAAS,CAAC,CAAC,CAAC;IAC7D,MAAMI,cAAc,GAAG,IAAI,CAAC5I,cAAc,CAACuI,eAAe,CAACG,cAAc,CAAC;EAC5E;EAEAC,oBAAoBA,CAACE,MAAW;IAC9B,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC,CAACE,QAAQ,EACnD,OAAOF,MAAM,CAAC,CAAC,CAAC,CAACE,QAAQ,CAACC,MAAM,CAAEC,KAAU,IAAI;MAC9C,OAAO,CAACA,KAAK,CAACpF,IAAI,CAACqF,UAAU,CAAC,WAAW,CAAC,IACxC,CAACD,KAAK,CAACpF,IAAI,CAACqF,UAAU,CAAC,UAAU,CAAC,IAClC,CAACD,KAAK,CAACpF,IAAI,CAACqF,UAAU,CAAC,QAAQ,CAAC,IAChC,CAACD,KAAK,CAACpF,IAAI,CAACqF,UAAU,CAAC,IAAI,CAAC;IAChC,CAAC,CAAC;EACN;EACAzL,eAAeA,CAAA;IACb,MAAM0L,iBAAiB,GAAG,CAAC,kBAAkB,EAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,EAAC,kBAAkB,EAAC,yBAAyB,EAAC,8BAA8B,EAAC,eAAe,CAAC;IACrL,MAAMC,YAAY,GAAG,IAAI,CAAC/J,MAAM,CAAC0B,GAAG;IACpC;IACA,OAAOoI,iBAAiB,CAACE,IAAI,CAAEJ,KAAY,IAAGA,KAAK,IAAIG,YAAY,CAAC;EACtE;EAEA5K,iBAAiBA,CAAA;IACf,IAAIJ,QAAQ,GAAGkL,MAAM,CAAC5H,YAAY,CAACK,OAAO,CAAC,UAAU,CAAC,CAAC;IACvD,OAAO,IAAI,CAACd,eAAe,CAACoI,IAAI,CAAEE,IAAS,IAAI;MAC7C,IAAIA,IAAI,CAACnL,QAAQ,KAAKA,QAAQ,EAAE;QAC9B,OAAOmL,IAAI,CAACtI,eAAe;;IAE/B,CAAC,CAAC;EACJ;EAEAK,sBAAsBA,CAAA;IAClB,MAAMkI,sBAAsB,GAAG9H,YAAY,CAACK,OAAO,CAAC,uBAAuB,CAAC;IAC9E,IAAIyH,sBAAsB,IAAIA,sBAAsB,KAAK,EAAE,EAAE;MAC3D,IAAI,CAACvI,eAAe,GAAG8B,IAAI,CAACC,KAAK,CAACwG,sBAAsB,CAAC;MACzDtF,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAAC1F,iBAAiB,EAAE,EAAE;UAC7B,IAAI,CAAC+D,qBAAqB,EAAE;;QAE9B,IAAI,CAACpD,EAAE,CAACmF,aAAa,EAAE;MACzB,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL,IAAI,CAACnE,IAAI,CAACkC,GAAG,CAAM,kBAAkB,CAAC,CAACQ,SAAS,CAAEwB,IAAS,IAAI;QAC7D,IAAI,CAACjE,WAAW,CAACqJ,SAAS,CAACpF,IAAI,EAAC,IAAI,CAACjG,QAAQ,CAAC;QAC9C,IAAI,CAAC6C,eAAe,GAAGoD,IAAI;QAC3B,IAAI,CAAC,IAAI,CAAC7F,iBAAiB,EAAE,EAAE;UAC7B,IAAI,CAAC+D,qBAAqB,EAAE;;QAE9B,IAAI,CAACpD,EAAE,CAACmF,aAAa,EAAE;MACzB,CAAC,CAAC;;EAEN;EAAC,QAAAoF,CAAA,G;qBA7XUjL,YAAY,EAAAxC,EAAA,CAAA0N,iBAAA,CAAAC,EAAA,CAAAC,KAAA,GAAA5N,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA9N,EAAA,CAAA0N,iBAAA,CAAA1N,EAAA,CAAA+N,iBAAA,GAAA/N,EAAA,CAAA0N,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAjO,EAAA,CAAA0N,iBAAA,CAAAM,EAAA,CAAAE,MAAA,GAAAlO,EAAA,CAAA0N,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAApO,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAQ,gBAAA,GAAArO,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAS,YAAA,GAAAtO,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAU,eAAA,GAAAvO,EAAA,CAAA0N,iBAAA,CAAAc,EAAA,CAAAC,cAAA,GAAAzO,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAa,iBAAA,GAAA1O,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAc,WAAA,GAAA3O,EAAA,CAAA0N,iBAAA,CA+DHjO,WAAW,GAAAO,EAAA,CAAA0N,iBAAA,CAAAC,EAAA,CAAAiB,IAAA,GAAA5O,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAgB,aAAA,GAAA7O,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAiB,cAAA,GAAA9O,EAAA,CAAA0N,iBAAA,CAAAG,EAAA,CAAAkB,cAAA,GAAA/O,EAAA,CAAA0N,iBAAA,CAAA1N,EAAA,CAAAgP,SAAA,GAAAhP,EAAA,CAAA0N,iBAAA,CAAAuB,EAAA,CAAAC,UAAA,GAAAlP,EAAA,CAAA0N,iBAAA,CAAAyB,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UA/DpB7M,YAAY;IAAA8M,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BACKjQ,UAAU;;;;;;;;;;iBAD3BkQ,GAAA,CAAAjN,QAAA,CAAAkN,MAAA,CACb;QAAA,UAAA3P,EAAA,CAAA4P,eAAA;;;;;;;;QC3CA5P,EAAA,CAAA0B,uBAAA,GAAc;QACZ1B,EAAA,CAAAG,UAAA,IAAA0P,oDAAA,0CAIiC;QAEjC7P,EAAA,CAAAG,UAAA,IAAA2P,mCAAA,gCAAA9P,EAAA,CAAA+P,sBAAA,CAwBc;QAEhB/P,EAAA,CAAA2B,qBAAA,EAAe;;;;QAhCkB3B,EAAA,CAAAe,SAAA,GAG1B;QAH0Bf,EAAA,CAAAgB,UAAA,SAAA0O,GAAA,CAAA1N,mBAAA,CAAAC,QAAA,CAAAyN,GAAA,CAAAxN,KAAA,MAAAwN,GAAA,CAAAvN,QAAA,aAAAuN,GAAA,CAAAvN,QAAA,SAG1B,aAAA6N,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}