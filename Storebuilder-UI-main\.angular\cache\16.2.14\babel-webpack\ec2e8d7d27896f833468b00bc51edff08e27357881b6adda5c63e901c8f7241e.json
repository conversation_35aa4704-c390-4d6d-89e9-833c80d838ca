{"ast": null, "code": "import { environment } from \"@environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class WishlistService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiEndPoint;\n  }\n  deleteCustomerWishList(data) {\n    return this.http.post(`${this.baseUrl}/product/CustomerWishList/DeleteCustomerWishList`, data);\n  }\n  getAllCustomerWishList() {\n    return this.http.get(`${this.baseUrl}/Product/CustomerWishList/GetAllCustomerWishList?TopNumber=100`);\n  }\n  static #_ = this.ɵfac = function WishlistService_Factory(t) {\n    return new (t || WishlistService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: WishlistService,\n    factory: WishlistService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "WishlistService", "constructor", "http", "baseUrl", "apiEndPoint", "deleteCustomerWishList", "data", "post", "getAllCustomerWishList", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\services\\wishlist.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {HttpClient} from \"@angular/common/http\";\r\nimport {environment} from \"@environments/environment\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WishlistService {\r\n  private baseUrl = environment.apiEndPoint;\r\n\r\n  constructor(private http: HttpClient) {\r\n  }\r\n\r\n  deleteCustomerWishList(data: any) {\r\n    return this.http.post(`${this.baseUrl}/product/CustomerWishList/DeleteCustomerWishList`, data);\r\n  }\r\n\r\n  getAllCustomerWishList() {\r\n    return this.http.get(`${this.baseUrl}/Product/CustomerWishList/GetAllCustomerWishList?TopNumber=100`);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAEA,SAAQA,WAAW,QAAO,2BAA2B;;;AAKrD,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,OAAO,GAAGJ,WAAW,CAACK,WAAW;EAGzC;EAEAC,sBAAsBA,CAACC,IAAS;IAC9B,OAAO,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,kDAAkD,EAAEG,IAAI,CAAC;EAChG;EAEAE,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,GAAG,IAAI,CAACN,OAAO,gEAAgE,CAAC;EACvG;EAAC,QAAAO,CAAA,G;qBAZUV,eAAe,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAff,eAAe;IAAAgB,OAAA,EAAfhB,eAAe,CAAAiB,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}