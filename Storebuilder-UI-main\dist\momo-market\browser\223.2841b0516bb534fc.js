"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[223],{1223:(de,M,r)=>{r.r(M),r.d(M,{MerchantLivestreamModule:()=>le});var _=r(6814),g=r(6075),C=r(3326),l=r(5879),A=r(553),E=r(3214),s=r(6223),h=r(2495),x=r(2831),R=r(6232),b=r(8645);const L=(0,x.i$)({passive:!0});let S=(()=>{class t{constructor(e,i){this._platform=e,this._ngZone=i,this._monitoredElements=new Map}monitor(e){if(!this._platform.isBrowser)return R.E;const i=(0,h.fI)(e),d=this._monitoredElements.get(i);if(d)return d.subject;const o=new b.x,a="cdk-text-field-autofilled",c=m=>{"cdk-text-field-autofill-start"!==m.animationName||i.classList.contains(a)?"cdk-text-field-autofill-end"===m.animationName&&i.classList.contains(a)&&(i.classList.remove(a),this._ngZone.run(()=>o.next({target:m.target,isAutofilled:!1}))):(i.classList.add(a),this._ngZone.run(()=>o.next({target:m.target,isAutofilled:!0})))};return this._ngZone.runOutsideAngular(()=>{i.addEventListener("animationstart",c,L),i.classList.add("cdk-text-field-autofill-monitored")}),this._monitoredElements.set(i,{subject:o,unlisten:()=>{i.removeEventListener("animationstart",c,L)}}),o}stopMonitoring(e){const i=(0,h.fI)(e),d=this._monitoredElements.get(i);d&&(d.unlisten(),d.subject.complete(),i.classList.remove("cdk-text-field-autofill-monitored"),i.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(i))}ngOnDestroy(){this._monitoredElements.forEach((e,i)=>this.stopMonitoring(i))}static#e=this.\u0275fac=function(i){return new(i||t)(l.LFG(x.t4),l.LFG(l.R0b))};static#t=this.\u0275prov=l.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),k=(()=>{class t{static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275mod=l.oAB({type:t});static#i=this.\u0275inj=l.cJS({})}return t})();var f=r(5407);r(6825);let z=(()=>{class t{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275prov=l.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),D=(()=>{class t{static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275mod=l.oAB({type:t});static#i=this.\u0275inj=l.cJS({providers:[z]})}return t})(),U=(()=>{class t{static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275dir=l.lG2({type:t})}return t})();const q=new l.OlP("MatFormField");let v=(()=>{class t{static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275mod=l.oAB({type:t});static#i=this.\u0275inj=l.cJS({imports:[f.BQ,_.ez,D,f.BQ]})}return t})();const Z=new l.OlP("MAT_INPUT_VALUE_ACCESSOR"),X=["button","checkbox","file","hidden","image","radio","range","reset","submit"];let Q=0;const V=(0,f.FD)(class{constructor(t,n,e,i){this._defaultErrorStateMatcher=t,this._parentForm=n,this._parentFormGroup=e,this.ngControl=i,this.stateChanges=new b.x}});let $=(()=>{class t extends V{get disabled(){return this._disabled}set disabled(e){this._disabled=(0,h.Ig)(e),this.focused&&(this.focused=!1,this.stateChanges.next())}get id(){return this._id}set id(e){this._id=e||this._uid}get required(){return this._required??this.ngControl?.control?.hasValidator(s.kI.required)??!1}set required(e){this._required=(0,h.Ig)(e)}get type(){return this._type}set type(e){this._type=e||"text",this._validateType(),!this._isTextarea&&(0,x.qK)().has(this._type)&&(this._elementRef.nativeElement.type=this._type)}get value(){return this._inputValueAccessor.value}set value(e){e!==this.value&&(this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=(0,h.Ig)(e)}constructor(e,i,d,o,a,c,m,y,O,T){super(c,o,a,d),this._elementRef=e,this._platform=i,this._autofillMonitor=y,this._formField=T,this._uid="mat-input-"+Q++,this.focused=!1,this.stateChanges=new b.x,this.controlType="mat-input",this.autofilled=!1,this._disabled=!1,this._type="text",this._readonly=!1,this._neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(w=>(0,x.qK)().has(w)),this._iOSKeyupListener=w=>{const p=w.target;!p.value&&0===p.selectionStart&&0===p.selectionEnd&&(p.setSelectionRange(1,1),p.setSelectionRange(0,0))};const F=this._elementRef.nativeElement,I=F.nodeName.toLowerCase();this._inputValueAccessor=m||F,this._previousNativeValue=this.value,this.id=this.id,i.IOS&&O.runOutsideAngular(()=>{e.nativeElement.addEventListener("keyup",this._iOSKeyupListener)}),this._isServer=!this._platform.isBrowser,this._isNativeSelect="select"===I,this._isTextarea="textarea"===I,this._isInFormField=!!T,this._isNativeSelect&&(this.controlType=F.multiple?"mat-native-select-multiple":"mat-native-select")}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._platform.IOS&&this._elementRef.nativeElement.removeEventListener("keyup",this._iOSKeyupListener)}ngDoCheck(){this.ngControl&&(this.updateErrorState(),null!==this.ngControl.disabled&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}_focusChanged(e){e!==this.focused&&(this.focused=e,this.stateChanges.next())}_onInput(){}_dirtyCheckNativeValue(){const e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){const e=this._getPlaceholder();if(e!==this._previousPlaceholder){const i=this._elementRef.nativeElement;this._previousPlaceholder=e,e?i.setAttribute("placeholder",e):i.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){X.indexOf(this._type)}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!(this._isNeverEmpty()||this._elementRef.nativeElement.value||this._isBadInput()||this.autofilled)}get shouldLabelFloat(){if(this._isNativeSelect){const e=this._elementRef.nativeElement,i=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&i&&i.label)}return this.focused||!this.empty}setDescribedByIds(e){e.length?this._elementRef.nativeElement.setAttribute("aria-describedby",e.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){const e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}static#e=this.\u0275fac=function(i){return new(i||t)(l.Y36(l.SBq),l.Y36(x.t4),l.Y36(s.a5,10),l.Y36(s.F,8),l.Y36(s.sg,8),l.Y36(f.rD),l.Y36(Z,10),l.Y36(S),l.Y36(l.R0b),l.Y36(q,8))};static#t=this.\u0275dir=l.lG2({type:t,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:18,hostBindings:function(i,d){1&i&&l.NdJ("focus",function(){return d._focusChanged(!0)})("blur",function(){return d._focusChanged(!1)})("input",function(){return d._onInput()}),2&i&&(l.Ikx("id",d.id)("disabled",d.disabled)("required",d.required),l.uIk("name",d.name||null)("readonly",d.readonly&&!d._isNativeSelect||null)("aria-invalid",d.empty&&d.required?null:d.errorState)("aria-required",d.required)("id",d.id),l.ekj("mat-input-server",d._isServer)("mat-mdc-form-field-textarea-control",d._isInFormField&&d._isTextarea)("mat-mdc-form-field-input-control",d._isInFormField)("mdc-text-field__input",d._isInFormField)("mat-mdc-native-select-inline",d._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:["aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly"},exportAs:["matInput"],features:[l._Bn([{provide:U,useExisting:t}]),l.qOj,l.TTD]})}return t})(),W=(()=>{class t{static#e=this.\u0275fac=function(i){return new(i||t)};static#t=this.\u0275mod=l.oAB({type:t});static#i=this.\u0275inj=l.cJS({imports:[f.BQ,v,v,k,f.BQ]})}return t})();function G(t,n){if(1&t){const e=l.EpF();l.TgZ(0,"div",10)(1,"div",11),l.NdJ("click",function(){const o=l.CHM(e).$implicit,a=l.oxw();return l.KtG(a.cardClick(o.id,o.roomId))}),l.TgZ(2,"span",12)(3,"a",13)(4,"strong",14),l._uU(5,"LIVE"),l.qZA(),l._UZ(6,"span",15),l.qZA()(),l.TgZ(7,"img",16),l.NdJ("error",function(d){l.CHM(e);const o=l.oxw();return l.KtG(o.errorHandler(d))}),l.qZA(),l.TgZ(8,"div",17)(9,"div",18)(10,"h1",19),l._uU(11),l.qZA()(),l.TgZ(12,"p",20),l._uU(13),l.qZA()()()()}if(2&t){const e=n.$implicit,i=l.oxw();l.xp6(7),l.Q6J("src",i.cartListImage(e.Thumbnail),l.LSH),l.xp6(4),l.Oqu(e.title),l.xp6(2),l.hij(" ",e.description," ")}}const J=[{path:"",component:(()=>{class t{livestream;router;platformId;liveMerchants=[];baseUrl=A.N.apiEndPoint;searchValue;constructor(e,i,d){this.livestream=e,this.router=i,this.platformId=d}ngOnInit(){this.getLivestreamData()}getLivestreamData(){this.livestream.getLiveMerchantData().subscribe({next:e=>{this.liveMerchants=e?.data.liveMerchants},error:e=>{console.error(e)}})}onKeyUp(e){let i=e.target.value;""==i?this.getLivestreamData():this.liveMerchants=this.liveMerchants.filter(d=>d.title===i)}cardClick(e,i){const o=this.router.createUrlTree(["/storecloud/merchants-livestream/merchant-liveStream-details"],{queryParams:{streamId:e,roomId:i}}).toString();if((0,_.NF)(this.platformId)){const c=`${window.location.origin}${o}`;window.open(c,"_blank")}}errorHandler(e){e.target.src="./assets/images/banner/livestream-banner.png"}cartListImage(e){return(0,C.B)(e,this.baseUrl)}static \u0275fac=function(i){return new(i||t)(l.Y36(E._),l.Y36(g.F0),l.Y36(l.Lbi))};static \u0275cmp=l.Xpm({type:t,selectors:[["app-merchant-livestream"]],decls:14,vars:2,consts:[[1,"cart","mt-8","cart-top"],[1,"content-container","my-3",2,"margin-top","180px !important"],[1,"my-3"],[1,"col-12","flex"],[1,"font-size-22","bold-font"],[1,"search-bar"],["matInput","","placeholder","Search live Merchant",3,"ngModel","ngModelChange","keyup.enter"],[1,"pi","pi-search"],[1,"row"],["class","col col-xl-4 col-lg-6 col-bigger col-xxxl","style","display: flex;justify-content: center;",4,"ngFor","ngForOf"],[1,"col","col-xl-4","col-lg-6","col-bigger","col-xxxl",2,"display","flex","justify-content","center"],[1,"card",3,"click"],[1,"live-status-holder"],["routerLink","merchants-livestream",1,"btn","btn_live","streamstatus"],[1,"statustext"],[1,"live-icon"],["alt","...",1,"card__img",3,"src","error"],[1,"card__content"],[2,"display","inline-flex"],[1,"card__header"],[1,"card__text","description-holder"]],template:function(i,d){1&i&&(l.TgZ(0,"section",0),l.ynx(1),l.TgZ(2,"div",1),l.ynx(3),l.TgZ(4,"div",2)(5,"div",3)(6,"div",4),l._uU(7,"LiveStream By Merchants"),l.qZA()(),l.TgZ(8,"div",3)(9,"span",5)(10,"input",6),l.NdJ("ngModelChange",function(a){return d.searchValue=a})("keyup.enter",function(a){return d.onKeyUp(a)}),l.qZA(),l._UZ(11,"em",7),l.qZA()(),l.TgZ(12,"div",8),l.YNc(13,G,14,3,"div",9),l.qZA()(),l.BQk(),l.qZA(),l.BQk(),l.qZA()),2&i&&(l.xp6(10),l.Q6J("ngModel",d.searchValue),l.xp6(3),l.Q6J("ngForOf",d.liveMerchants))},dependencies:[_.sg,g.rH,s.Fj,s.JJ,s.On,$],styles:['.card[_ngcontent-%COMP%]{width:350px;height:300px}.card[_ngcontent-%COMP%]:hover{transform:translateY(-.5%);box-shadow:0 4rem 8rem #0003}.card__img[_ngcontent-%COMP%]{display:block;width:100%;height:200px;object-fit:cover}.card__content[_ngcontent-%COMP%]{padding:1rem}.card__header[_ngcontent-%COMP%]{font-size:1rem;font-weight:500;color:#0d0d0d;line-height:unset!important;margin-bottom:0!important}.card__text[_ngcontent-%COMP%]{font-size:12px;letter-spacing:.1rem;line-height:1.7;color:#3d3d3d}.live-icon[_ngcontent-%COMP%]{display:inline-block;position:relative;top:calc(50% - 5px);background-color:#fff;width:10px;height:10px;border:1px solid rgba(0,0,0,.1);border-radius:50%;z-index:1}.live-icon[_ngcontent-%COMP%]:before{content:"";display:block;position:absolute;background-color:#fff;width:100%;height:100%;border-radius:50%;animation:_ngcontent-%COMP%_live 2s ease-in-out infinite;z-index:-1}@keyframes _ngcontent-%COMP%_live{0%{transform:scale(1)}to{transform:scale(3.5);background-color:#fff0}}.btn[_ngcontent-%COMP%]{display:block;font-weight:700;margin-top:2px;position:relative;background-color:red;text-decoration:none;color:#333;border-radius:5px;border:1px solid white;transition:all .2s;z-index:1;outline:none;-webkit-tap-highlight-color:rgba(255,255,255,0)}.streamstatus[_ngcontent-%COMP%]{display:inline-flex;flex-direction:row-reverse;align-items:center;text-transform:uppercase}.statustext[_ngcontent-%COMP%]{padding-left:5px;color:#fff;font-family:var(--bold-font)}.description-holder[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:172px}.live-status-holder[_ngcontent-%COMP%]{position:absolute;margin:10px}.search-bar[_ngcontent-%COMP%]{box-shadow:1px 1px 14px -4px #9e9e9ec2;border-radius:10px;position:relative}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:385px;height:35px;padding:0 10px;border-radius:10px}.search-bar[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{position:absolute;margin-left:-38px;top:9px}@media only screen and (max-width: 500px){.statustext[_ngcontent-%COMP%]{margin-right:8px}span.live-icon[_ngcontent-%COMP%]{position:absolute;right:3px;margin:0 3px}}@media only screen and (max-width: 350px){.btn[_ngcontent-%COMP%]{width:100%}}@media (min-width: 1300px) and (max-width: 1610px){.col-bigger[_ngcontent-%COMP%]{flex:0 0 32%;max-width:35%}}@media (min-width: 1610px) and (max-width: 1921px){.col-xxxl[_ngcontent-%COMP%]{flex:0 0 32%;max-width:25%}}']})}return t})()},{path:"merchant-liveStream-details",component:C.x}];let K=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=l.oAB({type:t});static \u0275inj=l.cJS({imports:[g.Bz.forChild(J),g.Bz]})}return t})();var ee=r(381),te=r(258),ie=r(617);let le=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=l.oAB({type:t});static \u0275inj=l.cJS({providers:[E._],imports:[_.ez,te.m,K,s.u5,ee.l1,ie.Ps,v,W]})}return t})()}}]);