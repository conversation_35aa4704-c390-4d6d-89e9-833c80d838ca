{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { environment } from '@environments/environment';\nimport { OrderService } from \"@core/services\";\nimport { OrderStatus, CreateReviewViewModel, UserTypeOfCancellation, SubOrderDetailStatus, OrderItemStatus } from '@core/interface';\nimport { take } from \"rxjs\";\nimport { isPlatformBrowser } from '@angular/common';\nimport { ConfirmationModalComponent } from '@shared/modals/confirmation-modal/confirmation-modal.component';\nimport { ReturnReason } from \"@core/services/return-reason.service\";\nimport { CancelReason } from \"@core/services/cancel-reason.service\";\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core/services\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"ngx-bootstrap/modal\";\nimport * as i8 from \"@core/services/gtm.service\";\nimport * as i9 from \"@core/services/custom-GA.service\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/breadcrumb\";\nimport * as i14 from \"primeng/inputnumber\";\nimport * as i15 from \"@core/services/cancel-reason.service\";\nimport * as i16 from \"@core/services/return-reason.service\";\nfunction IndexComponent_ng_container_0_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.cancelAllOrderModel());\n    });\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.cancelOrder\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.requestReturn());\n    });\n    i0.ɵɵelement(1, \"img\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.orderRequestReturn\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 60);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_container_0_div_35_img_2_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r33.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r28.orderListImage(product_r26 == null ? null : product_r26.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 60);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_container_0_div_35_img_3_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r29.orderListImage(product_r26 == null ? null : product_r26.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_div_35_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const product_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.cancelSingleOrderModel(product_r26));\n    });\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.cancelThisItem\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_div_35_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const product_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.returnSingleItem(product_r26));\n    });\n    i0.ɵɵelement(1, \"img\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.requestReturn\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_p_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"orderDetails.refundSuccessfullyProcessed\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtemplate(2, IndexComponent_ng_container_0_div_35_img_2_Template, 1, 1, \"img\", 48);\n    i0.ɵɵtemplate(3, IndexComponent_ng_container_0_div_35_img_3_Template, 1, 1, \"img\", 48);\n    i0.ɵɵelementStart(4, \"div\", 49)(5, \"p\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 51)(8, \"p\", 52);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 54)(14, \"section\", 55)(15, \"article\", 10);\n    i0.ɵɵelement(16, \"em\", 56);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"img\", 57);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_div_35_Template_img_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const product_r26 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.onOpenLogs(product_r26));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"section\");\n    i0.ɵɵtemplate(21, IndexComponent_ng_container_0_div_35_button_21_Template, 4, 3, \"button\", 58);\n    i0.ɵɵtemplate(22, IndexComponent_ng_container_0_div_35_button_22_Template, 4, 3, \"button\", 58);\n    i0.ɵɵtemplate(23, IndexComponent_ng_container_0_div_35_p_23_Template, 3, 3, \"p\", 59);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r26 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r26 == null ? null : product_r26.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r26.thumbnailImages && product_r26.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r26.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r18.currencyCode, \"\", i0.ɵɵpipeBind1(10, 11, product_r26.price / product_r26.qtyOrdered), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Qty: \", product_r26.qtyOrdered, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.getClasses(product_r26.orderItemStatusId));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.orderStatus(product_r26.orderItemStatus), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (product_r26.orderItemStatusId === ctx_r18.OrderItemStatusEnum.Placed || product_r26.orderItemStatusId === ctx_r18.OrderItemStatusEnum.Pending) && ctx_r18.allowCancelItems && !product_r26.isDispatched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r26.orderItemStatusId === ctx_r18.OrderItemStatusEnum.Delivered && ctx_r18.allowRefundItems);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r26.orderItemStatusId === ctx_r18.OrderItemStatusEnum.Returned || product_r26.orderItemStatusId === ctx_r18.OrderItemStatusEnum.Cancelled);\n  }\n}\nfunction IndexComponent_ng_container_0_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"account.details.Deliveryoption\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r19.order.deliveryOption);\n  }\n}\nfunction IndexComponent_ng_container_0_p_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r20.transactionData == null ? null : ctx_r20.transactionData.paymentMethod) === 1 ? i0.ɵɵpipeBind1(2, 1, \"orderDetails.typeCard\") : i0.ɵɵpipeBind1(3, 3, \"MoMo Pay\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_0_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, \"orderDetails.discount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" - \", ctx_r21.currencyCode, \" \", i0.ɵɵpipeBind1(6, 5, ctx_r21.orderDiscount), \"\");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"color\": a0\n  };\n};\nfunction IndexComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 15)(3, \"span\", 16);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_container_0_Template_span_click_3_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.backButton());\n    });\n    i0.ɵɵelement(4, \"img\", 17);\n    i0.ɵɵelementStart(5, \"span\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"section\", 19)(9, \"div\", 20)(10, \"div\", 21)(11, \"div\", 22)(12, \"span\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"span\", 23);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 24);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 26);\n    i0.ɵɵelement(25, \"em\", 27);\n    i0.ɵɵelementStart(26, \"span\", 28);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"span\", 30);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 31);\n    i0.ɵɵtemplate(33, IndexComponent_ng_container_0_button_33_Template, 4, 3, \"button\", 32);\n    i0.ɵɵtemplate(34, IndexComponent_ng_container_0_button_34_Template, 4, 3, \"button\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, IndexComponent_ng_container_0_div_35_Template, 24, 13, \"div\", 34);\n    i0.ɵɵelementStart(36, \"div\", 35)(37, \"div\", 36)(38, \"p\", 23);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, IndexComponent_ng_container_0_div_41_Template, 6, 4, \"div\", 37);\n    i0.ɵɵelementStart(42, \"div\", 38)(43, \"p\");\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"p\", 39);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 40)(49, \"p\", 23);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 38)(53, \"p\");\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"p\");\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 38)(59, \"p\");\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(62, IndexComponent_ng_container_0_p_62_Template, 4, 5, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 38)(64, \"p\");\n    i0.ɵɵtext(65);\n    i0.ɵɵpipe(66, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"p\");\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 38)(70, \"p\");\n    i0.ɵɵtext(71);\n    i0.ɵɵpipe(72, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"p\");\n    i0.ɵɵtext(74);\n    i0.ɵɵpipe(75, \"number\");\n    i0.ɵɵpipe(76, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 38)(78, \"p\");\n    i0.ɵɵtext(79);\n    i0.ɵɵpipe(80, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"p\");\n    i0.ɵɵtext(82);\n    i0.ɵɵpipe(83, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(84, IndexComponent_ng_container_0_div_84_Template, 7, 7, \"div\", 37);\n    i0.ɵɵelementStart(85, \"div\", 41)(86, \"p\");\n    i0.ɵɵtext(87);\n    i0.ɵɵpipe(88, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"p\");\n    i0.ɵɵtext(90);\n    i0.ɵɵpipe(91, \"number\");\n    i0.ɵɵpipe(92, \"number\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_5_0;\n    let tmp_6_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 33, \"order.orderDetails\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 35, \"order.orderNo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.order.orderId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 37, \"order.orderDate\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 39, ctx_r0.order.createdOn, \"dd/MM/YYYY\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(75, _c0, ctx_r0.utility.orderStatus((tmp_5_0 = ctx_r0.order.orderStatus) !== null && tmp_5_0 !== undefined ? tmp_5_0 : ctx_r0.OrderStatusEnum.Cancelled, ctx_r0.OrderStatusEnum)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(77, _c0, ctx_r0.utility.orderStatus((tmp_6_0 = ctx_r0.order.orderStatus) !== null && tmp_6_0 !== undefined ? tmp_6_0 : ctx_r0.OrderStatusEnum.Cancelled, ctx_r0.OrderStatusEnum)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.orderStatus(ctx_r0.order.status), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(31, 42, \"order.yourOrders\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fullOrderCancelAvailability() && ctx_r0.isAllowCancelOrders && !ctx_r0.order.isDispatched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fullOrderReturnAvailability() && ctx_r0.isAllowRefundOrders);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.orderItems);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 44, \"account.details.shippingDetails\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.order.deliveryOption);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 46, \"ResponseMessages.address\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.order.shippingAddress == null ? null : ctx_r0.order.shippingAddress.streetAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(51, 48, \"orderDetails.paymentDetails\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 50, \"orderDetails.transactionID\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.order.transactionId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(61, 52, \"orderDetails.type\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.transactionData == null ? null : ctx_r0.transactionData.paymentMethod);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(66, 54, \"orderDetails.transactionPhone\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.order.customerPhone);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(72, 56, \"orderDetails.itemsAmount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(75, 58, ctx_r0.order.orderItemsAmount, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue) : i0.ɵɵpipeBind1(76, 61, ctx_r0.order.orderItemsAmount), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(80, 63, \"orderDetails.shipping\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(83, 65, ctx_r0.order.shippingAmount, \"1.0-0\") : ctx_r0.order.shippingAmount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.orderDiscount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(88, 68, \"orderDetails.paymentTotal\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", ctx_r0.disableCent === \"false\" ? i0.ɵɵpipeBind2(91, 70, ctx_r0.totalCost - ctx_r0.orderDiscount, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue) : i0.ɵɵpipeBind1(92, 73, ctx_r0.totalCost - ctx_r0.orderDiscount), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_10_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_10_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r56.cancelAllOrderModel());\n    });\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.cancelOrder\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_10_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_10_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r58.requestReturn());\n    });\n    i0.ɵɵelement(1, \"img\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.orderRequestReturn\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"p\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 81)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 82);\n    i0.ɵɵelement(9, \"em\", 83);\n    i0.ɵɵelementStart(10, \"span\", 84);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, IndexComponent_ng_template_1_div_10_button_12_Template, 4, 3, \"button\", 85);\n    i0.ɵɵtemplate(13, IndexComponent_ng_template_1_div_10_button_13_Template, 4, 3, \"button\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 7, \"orderDetails.orderNo\"), \". \", ctx_r49.order.orderId, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(7, 9, ctx_r49.order.createdOn, \"dd/MM/YYYY\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c0, ctx_r49.utility.orderStatus((tmp_2_0 = ctx_r49.order.orderStatus) !== null && tmp_2_0 !== undefined ? tmp_2_0 : ctx_r49.OrderStatusEnum.Cancelled, ctx_r49.OrderStatusEnum)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r49.orderStatus(ctx_r49.order.status), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.fullOrderCancelAvailability() && ctx_r49.isAllowCancelOrders && !ctx_r49.order.isDispatched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.fullOrderReturnAvailability() && ctx_r49.isAllowRefundOrders);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 104);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_1_div_12_img_6_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r68);\n      const ctx_r67 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r67.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r60 = i0.ɵɵnextContext().$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r61.orderListImage(product_r60 == null ? null : product_r60.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 104);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_1_div_12_img_7_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r70.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r60 = i0.ɵɵnextContext().$implicit;\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r62.orderListImage(product_r60 == null ? null : product_r60.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_em_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 111);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_em_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 112);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_em_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 113);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_em_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 114);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_em_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"em\", 115);\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_1_div_12_div_15_em_1_Template, 1, 0, \"em\", 106);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_1_div_12_div_15_em_2_Template, 1, 0, \"em\", 107);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_1_div_12_div_15_em_3_Template, 1, 0, \"em\", 108);\n    i0.ɵɵtemplate(4, IndexComponent_ng_template_1_div_12_div_15_em_4_Template, 1, 0, \"em\", 109);\n    i0.ɵɵtemplate(5, IndexComponent_ng_template_1_div_12_div_15_em_5_Template, 1, 0, \"em\", 110);\n    i0.ɵɵelementStart(6, \"span\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r60 = i0.ɵɵnextContext().$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Placed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Processing || product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.ReturnInProgress || product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Pending);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Delivered);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.RequestReturn);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Cancelled || product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.Returned || product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.ReturnRejected || product_r60.orderItemStatusId === ctx_r63.OrderItemStatusEnum.CancelledByUser);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r63.orderStatus(product_r60.orderItemStatus), \"\");\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_12_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r81);\n      const product_r60 = i0.ɵɵnextContext().$implicit;\n      const ctx_r79 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r79.cancelSingleOrderModel(product_r60));\n    });\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.cancelThisItem\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_12_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const product_r60 = i0.ɵɵnextContext().$implicit;\n      const ctx_r82 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r82.returnSingleItem(product_r60));\n    });\n    i0.ɵɵelement(1, \"img\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"orderDetails.requestReturn\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_p_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"orderDetails.refundSuccessfullyProcessed\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_12_Template_div_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r86);\n      const product_r60 = restoredCtx.$implicit;\n      const ctx_r85 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r85.productDetails(product_r60));\n    });\n    i0.ɵɵelementStart(3, \"div\", 90)(4, \"span\", 91);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, IndexComponent_ng_template_1_div_12_img_6_Template, 1, 1, \"img\", 92);\n    i0.ɵɵtemplate(7, IndexComponent_ng_template_1_div_12_img_7_Template, 1, 1, \"img\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 93)(9, \"p\", 94);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_1_div_12_Template_p_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r86);\n      const product_r60 = restoredCtx.$implicit;\n      const ctx_r87 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r87.productDetails(product_r60));\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 95);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 96);\n    i0.ɵɵtemplate(15, IndexComponent_ng_template_1_div_12_div_15_Template, 8, 6, \"div\", 97);\n    i0.ɵɵelementStart(16, \"div\")(17, \"div\", 98)(18, \"p\", 99);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 100);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵpipe(25, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 101);\n    i0.ɵɵtemplate(27, IndexComponent_ng_template_1_div_12_button_27_Template, 4, 3, \"button\", 102);\n    i0.ɵɵtemplate(28, IndexComponent_ng_template_1_div_12_button_28_Template, 4, 3, \"button\", 85);\n    i0.ɵɵtemplate(29, IndexComponent_ng_template_1_div_12_p_29_Template, 3, 3, \"p\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"hr\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r60 = ctx.$implicit;\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r60.qtyOrdered);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60 == null ? null : product_r60.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r60.thumbnailImages && product_r60.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", product_r60.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(13, 15, \"orderDetails.sku\"), \" \", product_r60.sku, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatus);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r50.currencyCode, \" \", ctx_r50.disableCent === \"false\" ? i0.ɵɵpipeBind2(20, 17, product_r60.price, \"1.\" + ctx_r50.decimalValue + \"-\" + ctx_r50.decimalValue) : i0.ɵɵpipeBind1(21, 20, product_r60.price), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", (product_r60 == null ? null : product_r60.itemStatusDetails == null ? null : product_r60.itemStatusDetails.subOrderItemStatus) === \"Pending\" ? \"Placement\" : ctx_r50.orderStatus(product_r60 == null ? null : product_r60.itemStatusDetails == null ? null : product_r60.itemStatusDetails.subOrderItemStatus), \" \", i0.ɵɵpipeBind1(24, 22, \"order.At\"), \": \", i0.ɵɵpipeBind2(25, 24, product_r60 == null ? null : product_r60.itemStatusDetails == null ? null : product_r60.itemStatusDetails.subOrderItemStatusUpdatedAt, \"dd/MM/yyyy hh:mm a\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (product_r60.orderItemStatusId === ctx_r50.OrderItemStatusEnum.Placed || product_r60.orderItemStatusId === ctx_r50.OrderItemStatusEnum.Pending) && ctx_r50.allowCancelItems && !product_r60.isDispatched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r50.OrderItemStatusEnum.Delivered && ctx_r50.allowRefundItems && product_r60.isRefundable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", product_r60.orderItemStatusId === ctx_r50.OrderItemStatusEnum.Returned || product_r60.orderItemStatusId === ctx_r50.OrderItemStatusEnum.Cancelled);\n  }\n}\nfunction IndexComponent_ng_template_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"account.details.Deliveryoption\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r51.order.deliveryOption);\n  }\n}\nfunction IndexComponent_ng_template_1_p_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r52.transactionData == null ? null : ctx_r52.transactionData.paymentMethod) === 1 ? i0.ɵɵpipeBind1(2, 1, \"orderDetails.typeCard\") : i0.ɵɵpipeBind1(3, 3, \"MoMo Pay\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_1_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, \"orderDetails.discount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" - \", ctx_r53.currencyCode, \" \", i0.ɵɵpipeBind1(6, 5, ctx_r53.orderDiscount), \"\");\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"order-details-page\": a0,\n    \"hidden-navbar\": a1\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    breadcrumb: a0,\n    hiddenNavbarBreadcrum: a1\n  };\n};\nfunction IndexComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 64)(1, \"div\", 65);\n    i0.ɵɵelement(2, \"p-breadcrumb\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"div\", 68)(5, \"div\", 69)(6, \"div\", 70)(7, \"p\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_1_div_10_Template, 14, 14, \"div\", 72);\n    i0.ɵɵelementStart(11, \"div\", 73);\n    i0.ɵɵtemplate(12, IndexComponent_ng_template_1_div_12_Template, 31, 27, \"div\", 74);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 75)(14, \"div\", 76)(15, \"p\", 23);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, IndexComponent_ng_template_1_div_18_Template, 6, 4, \"div\", 37);\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\", 39);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 77)(26, \"p\", 23);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 38)(30, \"p\");\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 38)(36, \"p\");\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, IndexComponent_ng_template_1_p_39_Template, 4, 5, \"p\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 38)(41, \"p\");\n    i0.ɵɵtext(42);\n    i0.ɵɵpipe(43, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\");\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 38)(47, \"p\");\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"p\");\n    i0.ɵɵtext(51);\n    i0.ɵɵpipe(52, \"number\");\n    i0.ɵɵpipe(53, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 38)(55, \"p\");\n    i0.ɵɵtext(56);\n    i0.ɵɵpipe(57, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"p\");\n    i0.ɵɵtext(59);\n    i0.ɵɵpipe(60, \"number\");\n    i0.ɵɵpipe(61, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(62, IndexComponent_ng_template_1_div_62_Template, 7, 7, \"div\", 37);\n    i0.ɵɵelement(63, \"hr\", 78);\n    i0.ɵɵelementStart(64, \"div\", 41)(65, \"p\");\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p\");\n    i0.ɵɵtext(69);\n    i0.ɵɵpipe(70, \"number\");\n    i0.ɵɵpipe(71, \"number\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(63, _c1, ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(66, _c2, ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"home\", ctx_r2.home)(\"model\", ctx_r2.items);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 28, \"order.yourOrders\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showActionOrderButton);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.orderItems);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 30, \"account.details.shippingDetails\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.order.deliveryOption);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 32, \"ResponseMessages.address\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.order.shippingAddress == null ? null : ctx_r2.order.shippingAddress.streetAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 34, \"orderDetails.paymentDetails\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(32, 36, \"orderDetails.transactionID\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.order.transactionId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(38, 38, \"orderDetails.type\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.transactionData == null ? null : ctx_r2.transactionData.paymentMethod);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(43, 40, \"orderDetails.transactionPhone\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.order.customerPhone);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(49, 42, \"orderDetails.itemsAmount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.currencyCode, \" \", ctx_r2.disableCent === \"false\" ? i0.ɵɵpipeBind2(52, 44, ctx_r2.order.orderItemsAmount, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue) : i0.ɵɵpipeBind1(53, 47, ctx_r2.order.orderItemsAmount), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(57, 49, \"orderDetails.shipping\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.currencyCode, \" \", ctx_r2.disableCent === \"false\" ? i0.ɵɵpipeBind2(60, 51, ctx_r2.order.shippingAmount, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue) : i0.ɵɵpipeBind1(61, 54, ctx_r2.order.shippingAmount), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.orderDiscount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(67, 56, \"orderDetails.paymentTotal\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.currencyCode, \" \", ctx_r2.disableCent === \"false\" ? i0.ɵɵpipeBind2(70, 58, ctx_r2.totalCost - ctx_r2.orderDiscount, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue) : i0.ɵɵpipeBind1(71, 61, ctx_r2.totalCost - ctx_r2.orderDiscount), \" \");\n  }\n}\nfunction IndexComponent_ng_template_4_div_0_div_4_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 104);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_4_div_0_div_4_img_3_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r95.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r90 = i0.ɵɵnextContext().$implicit;\n    const ctx_r92 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r92.orderListImage(product_r90 == null ? null : product_r90.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_4_div_0_div_4_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 104);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_4_div_0_div_4_img_4_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r98.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r90 = i0.ɵɵnextContext().$implicit;\n    const ctx_r93 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r93.orderListImage(product_r90 == null ? null : product_r90.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_4_div_0_div_4_hr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 127);\n  }\n}\nconst _c3 = function () {\n  return {\n    minWidth: \"100%\"\n  };\n};\nfunction IndexComponent_ng_template_4_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 118)(2, \"div\", 119);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_4_div_0_div_4_img_3_Template, 1, 1, \"img\", 92);\n    i0.ɵɵtemplate(4, IndexComponent_ng_template_4_div_0_div_4_img_4_Template, 1, 1, \"img\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 120)(6, \"p\", 121);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 95);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 95);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 122)(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-dropdown\", 123);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_4_div_0_div_4_Template_p_dropdown_ngModelChange_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r102);\n      const i_r91 = restoredCtx.index;\n      const ctx_r101 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r101.formData.CancelOrderItem[i_r91].CancelledReasonId = $event);\n    })(\"onChange\", function IndexComponent_ng_template_4_div_0_div_4_Template_p_dropdown_onChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r103 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r103.onReasonSelected($event));\n    });\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"textarea\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_4_div_0_div_4_Template_textarea_ngModelChange_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r102);\n      const i_r91 = restoredCtx.index;\n      const ctx_r104 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r104.formData.CancelOrderItem[i_r91].CancelReasonDescription = $event);\n    });\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 125)(22, \"p\");\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\");\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, IndexComponent_ng_template_4_div_0_div_4_hr_28_Template, 1, 0, \"hr\", 126);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r90 = ctx.$implicit;\n    const i_r91 = ctx.index;\n    const ctx_r89 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r90 == null ? null : product_r90.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r90.thumbnailImages && product_r90.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r90.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(10, 18, \"orderDetails.sku\"), \": \", product_r90.skuAutoGenerated ? product_r90.skuAutoGenerated : product_r90.sku, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"QTY: \", product_r90.qtyOrdered, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 20, \"orderDetails.cancelOrderReason\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(31, _c3));\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(18, 22, \"orderDetails.reasonForCancellation\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r89.formData.CancelOrderItem[i_r91].CancelledReasonId)(\"options\", ctx_r89.cancelledReasons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(20, 24, \"orderDetails.explainMore\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r89.formData.CancelOrderItem[i_r91].CancelReasonDescription);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 26, \"orderDetails.amount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r89.currencyCode, \" \", i0.ɵɵpipeBind2(27, 28, product_r90.price, \"1.\" + ctx_r89.decimalValue + \"-\" + ctx_r89.decimalValue), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r91 < ctx_r89.orderItems.length - 1);\n  }\n}\nfunction IndexComponent_ng_template_4_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 117);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, IndexComponent_ng_template_4_div_0_div_4_Template, 29, 32, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"orderDetails.cancelOrder\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r88.orderItems);\n  }\n}\nfunction IndexComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_4_div_0_Template, 5, 4, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.cancelOrderDetails);\n  }\n}\nfunction IndexComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r110 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 130)(2, \"div\", 131)(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_5_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r109 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r109.cancelAllApprovedModal());\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r105 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 5, \"orderDetails.totalAmount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r105.currencyCode, \" \", i0.ɵɵpipeBind2(8, 7, ctx_r105.totalCost, \"1.\" + ctx_r105.decimalValue + \"-\" + ctx_r105.decimalValue), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r105.isFormValid())(\"label\", i0.ɵɵpipeBind1(10, 10, \"orderDetails.cancelOrder\"));\n  }\n}\nfunction IndexComponent_ng_template_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 133)(1, \"p\", 134);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 135);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 136)(8, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_5_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r112);\n      const ctx_r111 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r111.closeCancellingOrder());\n    });\n    i0.ɵɵelementStart(9, \"div\", 138);\n    i0.ɵɵelement(10, \"img\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 140);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_5_div_1_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r112);\n      const ctx_r113 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r113.onCancelAllOrderApproval());\n    });\n    i0.ɵɵelementStart(15, \"div\", 142);\n    i0.ɵɵelement(16, \"img\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 140);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"orderDetails.cancelOrder\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 6, \"orderDetails.cancelOrderConfirmQuestions\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"deleteItemPopupComponent.cancel\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 10, \"orderDetails.proceed\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵelement(1, \"img\", 145);\n    i0.ɵɵelementStart(2, \"p\", 146);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"orderDetails.orderCancelled\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"orderDetails.weAreProcessingTheRefund\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_5_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_5_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r115);\n      const ctx_r114 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r114.closeCancellingOrder());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(2, 1, \"orderDetails.ok\"));\n  }\n}\nfunction IndexComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_5_div_0_Template, 11, 12, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_5_div_1_Template, 20, 12, \"div\", 128);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_5_div_2_Template, 8, 6, \"div\", 129);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_5_div_3_Template, 3, 3, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.cancelOrderDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.confirmCancelAllOrder);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.proceedCancellingOrder);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.proceedCancellingOrder);\n  }\n}\nfunction IndexComponent_ng_template_7_div_0_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r122 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 154);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_7_div_0_img_7_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r122);\n      const ctx_r121 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r121.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r119 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r119.orderListImage(ctx_r119.selectedProduct == null ? null : ctx_r119.selectedProduct.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_7_div_0_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 154);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_7_div_0_img_8_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r124);\n      const ctx_r123 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r123.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r120 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r120.orderListImage(ctx_r120.selectedProduct == null ? null : ctx_r120.selectedProduct.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 87)(5, \"div\", 118)(6, \"div\", 119);\n    i0.ɵɵtemplate(7, IndexComponent_ng_template_7_div_0_img_7_Template, 1, 1, \"img\", 150);\n    i0.ɵɵtemplate(8, IndexComponent_ng_template_7_div_0_img_8_Template, 1, 1, \"img\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 120)(10, \"p\", 121);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 95);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 95);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 151)(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p-inputNumber\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_7_div_0_Template_p_inputNumber_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r125.selectedProduct.qtyOrdered = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 122)(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p-dropdown\", 153);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_7_div_0_Template_p_dropdown_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r127 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r127.subOrderSelectedCanceledReason = $event);\n    })(\"onChange\", function IndexComponent_ng_template_7_div_0_Template_p_dropdown_onChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r128 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r128.onReasonSelected($event));\n    });\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"textarea\", 124);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_7_div_0_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r129 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r129.subOrderCancelReasonDescription = $event);\n    });\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r116 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 22, \"orderDetails.cancelItem\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r116.selectedProduct == null ? null : ctx_r116.selectedProduct.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r116.selectedProduct.thumbnailImages && ctx_r116.selectedProduct.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r116.selectedProduct.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(14, 24, \"orderDetails.sku\"), \": \", ctx_r116.selectedProduct.skuAutoGenerated ? ctx_r116.selectedProduct.skuAutoGenerated : ctx_r116.selectedProduct.sku, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" QTY: \", ctx_r116.selectedProduct.qtyOrdered, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 26, \"orderDetails.numberOfCancelledItem\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true)(\"ngModel\", ctx_r116.selectedProduct.qtyOrdered)(\"showButtons\", true)(\"min\", 1)(\"max\", ctx_r116.selectedProduct.qtyOrdered);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(25, 28, \"orderDetails.cancelOrderReason\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(34, _c3));\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(27, 30, \"orderDetails.reasonForCancellation\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r116.subOrderSelectedCanceledReason)(\"options\", ctx_r116.cancelledReasons)(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(29, 32, \"orderDetails.explainMore\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r116.subOrderCancelReasonDescription);\n  }\n}\nfunction IndexComponent_ng_template_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r131 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 155);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 70)(8, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_7_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r130 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r130.closeCancellingSingleOrder());\n    });\n    i0.ɵɵelement(9, \"img\", 157);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_7_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r131);\n      const ctx_r132 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r132.confirmToCancelSingleApprovedModal());\n    });\n    i0.ɵɵelement(14, \"img\", 159);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"orderDetails.cancelItem\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 6, \"orderDetails.cancelSingleOrderConfirmQuestions\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 8, \"deleteItemPopupComponent.cancel\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 10, \"orderDetails.proceed\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵelement(1, \"img\", 145);\n    i0.ɵɵelementStart(2, \"p\", 146);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"orderDetails.itemCancelled\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"orderDetails.weAreProcessingTheRefund\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_7_div_0_Template, 30, 35, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_7_div_1_Template, 18, 12, \"div\", 9);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_7_div_2_Template, 8, 6, \"div\", 129);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.cancelSingleOrderDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.confirmCancelSingleOrder);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.proceedCancellingSingleOrder);\n  }\n}\nfunction IndexComponent_ng_template_8_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r136 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 131)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_8_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r136);\n      const ctx_r135 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r135.cancelSingleApprovedModal());\n    });\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r133 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"orderDetails.amount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r133.currencyCode, \" \", ctx_r133.singleOrderTotal, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r133.subOrderSelectedCanceledReason)(\"label\", i0.ɵɵpipeBind1(8, 7, \"orderDetails.cancelItem\"));\n  }\n}\nfunction IndexComponent_ng_template_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r138 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_8_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r138);\n      const ctx_r137 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r137.closeCancellingSingleOrder());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(2, 1, \"orderDetails.ok\"));\n  }\n}\nfunction IndexComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_8_div_0_Template, 9, 9, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_8_div_1_Template, 3, 3, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.cancelSingleOrderDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.proceedCancellingSingleOrder);\n  }\n}\nfunction IndexComponent_ng_template_10_div_0_div_18_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r150 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 154);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_10_div_0_div_18_img_3_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r150);\n      const ctx_r149 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r149.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r144 = i0.ɵɵnextContext().$implicit;\n    const ctx_r146 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r146.orderListImage(product_r144 == null ? null : product_r144.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_10_div_0_div_18_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r153 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 179);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_10_div_0_div_18_img_4_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r153);\n      const ctx_r152 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r152.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r144 = i0.ɵɵnextContext().$implicit;\n    const ctx_r147 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r147.orderListImage(product_r144 == null ? null : product_r144.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_10_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r156 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 118)(2, \"div\", 119);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_10_div_0_div_18_img_3_Template, 1, 1, \"img\", 150);\n    i0.ɵɵtemplate(4, IndexComponent_ng_template_10_div_0_div_18_img_4_Template, 1, 1, \"img\", 170);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 120)(6, \"p\", 121);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 95);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 122)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p-dropdown\", 171);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_div_0_div_18_Template_p_dropdown_ngModelChange_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r156);\n      const product_r144 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(product_r144.selectedReturnReason = $event);\n    });\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"textarea\", 172);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_div_0_div_18_Template_textarea_ngModelChange_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r156);\n      const product_r144 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(product_r144.returnReasonDescription = $event);\n    });\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 173)(23, \"span\", 174);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_0_div_18_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r156);\n      const _r148 = i0.ɵɵreference(30);\n      return i0.ɵɵresetView(_r148.click());\n    });\n    i0.ɵɵelement(26, \"img\", 176);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 177, 178);\n    i0.ɵɵlistener(\"change\", function IndexComponent_ng_template_10_div_0_div_18_Template_input_change_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r156);\n      const product_r144 = restoredCtx.$implicit;\n      const i_r145 = restoredCtx.index;\n      const ctx_r159 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r159.onFileSelected($event, product_r144, i_r145));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r144 = ctx.$implicit;\n    const ctx_r143 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r144 == null ? null : product_r144.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !product_r144.thumbnailImages && product_r144.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", product_r144.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(10, 18, \"orderDetails.sku\"), \" \", product_r144.sku, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(14, 20, \"orderDetails.returnReason\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(30, _c3));\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(16, 22, \"orderDetails.selectReturnReason\"));\n    i0.ɵɵproperty(\"ngModel\", product_r144.selectedReturnReason)(\"options\", ctx_r143.returnReasons)(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(18, 24, \"orderDetails.explainMore\"));\n    i0.ɵɵproperty(\"ngModel\", product_r144.returnReasonDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(21, 26, \"orderDetails.uploadImageVideo\"), \"*\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", product_r144.selectedFileName ? product_r144.selectedFileName : \"PNG,JPG,MP4\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 28, \"orderDetails.upload\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"maxFileSize\", 2 * 1024 * 1024);\n  }\n}\nfunction IndexComponent_ng_template_10_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 161)(5, \"div\", 162)(6, \"span\", 163);\n    i0.ɵɵelement(7, \"img\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 165);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 166);\n    i0.ɵɵelement(12, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 167);\n    i0.ɵɵelement(14, \"img\", 168);\n    i0.ɵɵelementStart(15, \"p\", 169);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, IndexComponent_ng_template_10_div_0_div_18_Template, 31, 31, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r139 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 7, \"orderDetails.returnReason\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(11, _c0, ctx_r139.requestReturnDetails ? \"#A3A3A3\" : \"#204E6E\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 9, \"orderDetails.pickUpPlace\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r139.orderItems);\n  }\n}\nfunction IndexComponent_ng_template_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 161)(5, \"div\", 162)(6, \"span\", 163);\n    i0.ɵɵelement(7, \"img\", 180);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 165);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 166);\n    i0.ɵɵelement(12, \"hr\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 167);\n    i0.ɵɵelement(14, \"img\", 182);\n    i0.ɵɵelementStart(15, \"p\", 169);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 183);\n    i0.ɵɵelement(19, \"img\", 184);\n    i0.ɵɵelementStart(20, \"p\", 185);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r140 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 7, \"orderDetails.returnReason\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(11, _c0, ctx_r140.requestReturnItemDetails ? \"#A3A3A3\" : \"#204E6E\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 9, \"orderDetails.pickUpPlace\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r140.order.shippingAddress == null ? null : ctx_r140.order.shippingAddress.streetAddress, \" \");\n  }\n}\nfunction IndexComponent_ng_template_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r161 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 186)(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 155);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 187)(8, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r160 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r160.cancelReturnOrder());\n    });\n    i0.ɵɵelement(9, \"img\", 157);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r161);\n      const ctx_r162 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r162.confirmToReturnModal());\n    });\n    i0.ɵɵelement(14, \"img\", 159);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"p\", 188);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 7, \"orderDetails.areYouSureWantReturnOrder\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 9, \"deleteItemPopupComponent.cancel\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 11, \"orderDetails.proceed\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 13, \"orderDetails.returnFromPickedUpDeliveredAddress\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵelement(1, \"img\", 189);\n    i0.ɵɵelementStart(2, \"p\", 146);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"orderDetails.returnRequestSent\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"orderDetails.notifiedMerchantReturnedRequest\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_10_div_0_Template, 19, 13, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_10_div_1_Template, 22, 13, \"div\", 9);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_10_div_2_Template, 21, 15, \"div\", 160);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_10_div_3_Template, 8, 6, \"div\", 129);\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.requestReturnDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.orderPickUpLocation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.confirmReturnOrder);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.returnedOrderConifermed);\n  }\n}\nfunction IndexComponent_ng_template_11_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r167 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 131)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 190)(8, \"div\", 191)(9, \"button\", 192);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_11_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r167);\n      const ctx_r166 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r166.cancelReturnOrder());\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 191)(12, \"button\", 193);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_11_div_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r167);\n      const ctx_r168 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r168.proceedReturnOrder());\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r163 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"orderDetails.amount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r163.currencyCode, \" \", ctx_r163.totalCost - ctx_r163.orderDiscount, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(10, 7, \"orderDetails.back\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(13, 9, \"orderDetails.confirmReason\"));\n  }\n}\nfunction IndexComponent_ng_template_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r170 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 131)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 190)(8, \"div\", 191)(9, \"button\", 192);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_11_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r170);\n      const ctx_r169 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r169.backConfirmPicUpLocationOrder());\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 191)(12, \"button\", 194);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_11_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r170);\n      const ctx_r171 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r171.confirmPicUpLocationOrder());\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r164 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"orderDetails.amount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r164.currencyCode, \" \", ctx_r164.totalCost - ctx_r164.orderDiscount, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(10, 7, \"orderDetails.back\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(13, 9, \"orderDetails.return\"));\n  }\n}\nfunction IndexComponent_ng_template_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r173 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_11_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r173);\n      const ctx_r172 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r172.cancelReturnOrder());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(2, 1, \"orderDetails.ok\"));\n  }\n}\nfunction IndexComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_11_div_0_Template, 14, 11, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_11_div_1_Template, 14, 11, \"div\", 9);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_11_div_2_Template, 3, 3, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.requestReturnDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.orderPickUpLocation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.returnedOrderConifermed);\n  }\n}\nfunction IndexComponent_ng_template_13_div_0_img_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r182 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 154);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_13_div_0_img_21_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r182);\n      const ctx_r181 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r181.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r178 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r178.orderListImage(ctx_r178.selectedReturnItem == null ? null : ctx_r178.selectedReturnItem.thumbnailImages[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_ng_template_13_div_0_img_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r184 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 154);\n    i0.ɵɵlistener(\"error\", function IndexComponent_ng_template_13_div_0_img_22_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r184);\n      const ctx_r183 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r183.errorHandler($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r179 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r179.orderListImage(ctx_r179.selectedReturnItem == null ? null : ctx_r179.selectedReturnItem.productImage), i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c4 = function (a0) {\n  return {\n    \"background\": a0\n  };\n};\nfunction IndexComponent_ng_template_13_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r186 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 161)(5, \"div\", 162)(6, \"span\", 163);\n    i0.ɵɵelement(7, \"img\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 165);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 166);\n    i0.ɵɵelement(12, \"hr\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 167);\n    i0.ɵɵelement(14, \"img\", 168);\n    i0.ɵɵelementStart(15, \"p\", 169);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 87)(19, \"div\", 118)(20, \"div\", 119);\n    i0.ɵɵtemplate(21, IndexComponent_ng_template_13_div_0_img_21_Template, 1, 1, \"img\", 150);\n    i0.ɵɵtemplate(22, IndexComponent_ng_template_13_div_0_img_22_Template, 1, 1, \"img\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 120)(24, \"p\", 121);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\", 95);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 151)(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p-inputNumber\", 152);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_13_div_0_Template_p_inputNumber_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const ctx_r185 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r185.selectedReturnItem.qtyOrdered = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 122)(35, \"span\");\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p-dropdown\", 171);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_13_div_0_Template_p_dropdown_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const ctx_r187 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r187.selectedReturnReason = $event);\n    });\n    i0.ɵɵpipe(39, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"textarea\", 172);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_13_div_0_Template_textarea_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const ctx_r188 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r188.returnItemReasonDescription = $event);\n    });\n    i0.ɵɵpipe(41, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 173)(46, \"span\", 174);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_13_div_0_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r186);\n      const _r180 = i0.ɵɵreference(53);\n      return i0.ɵɵresetView(_r180.click());\n    });\n    i0.ɵɵelement(49, \"img\", 176);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"input\", 177, 178);\n    i0.ɵɵlistener(\"change\", function IndexComponent_ng_template_13_div_0_Template_input_change_52_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const ctx_r190 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r190.onFileItemSelected($event, ctx_r190.selectedReturnItem));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r174 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 29, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 31, \"orderDetails.returnReason\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c4, ctx_r174.requestReturnItemDetails ? \"#A3A3A3\" : \"#204E6E\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c0, ctx_r174.requestReturnItemDetails ? \"#A3A3A3\" : \"#204E6E\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 33, \"orderDetails.pickUpPlace\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r174.selectedReturnItem == null ? null : ctx_r174.selectedReturnItem.thumbnailImages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r174.selectedReturnItem.thumbnailImages && ctx_r174.selectedReturnItem.productImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r174.selectedReturnItem.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(28, 35, \"orderDetails.sku\"), \" \", ctx_r174.selectedReturnItem.sku, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 37, \"orderDetails.numberOfReturnedItem\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", true)(\"ngModel\", ctx_r174.selectedReturnItem.qtyOrdered)(\"showButtons\", true)(\"min\", 1)(\"max\", ctx_r174.selectedReturnItem.qtyOrdered);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 39, \"orderDetails.returnReason\"), \"* \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(53, _c3));\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(39, 41, \"orderDetails.selectReturnReason\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r174.selectedReturnReason)(\"options\", ctx_r174.returnReasons)(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(41, 43, \"orderDetails.explainMore\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r174.returnItemReasonDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(44, 45, \"orderDetails.uploadImageVideo\"), \"*\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r174.selectedReturnItem.selectedItemFileName ? ctx_r174.selectedReturnItem.selectedItemFileName : \"PNG,JPG,MP4\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(51, 47, \"orderDetails.upload\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"maxFileSize\", 2 * 1024 * 1024);\n  }\n}\nfunction IndexComponent_ng_template_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 161)(5, \"div\", 162)(6, \"span\", 163);\n    i0.ɵɵelement(7, \"img\", 180);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 165);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 166);\n    i0.ɵɵelement(12, \"hr\", 181);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 167);\n    i0.ɵɵelement(14, \"img\", 195);\n    i0.ɵɵelementStart(15, \"p\", 169);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 183);\n    i0.ɵɵelement(19, \"img\", 196);\n    i0.ɵɵelementStart(20, \"p\", 185);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r175 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 5, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 7, \"orderDetails.returnReason\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(11, _c0, ctx_r175.requestReturnItemDetails ? \"#A3A3A3\" : \"#204E6E\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 9, \"orderDetails.pickUpPlace\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r175.order.shippingAddress == null ? null : ctx_r175.order.shippingAddress.streetAddress, \" \");\n  }\n}\nfunction IndexComponent_ng_template_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r192 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 186)(1, \"p\", 149);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 155);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 197)(8, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_13_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r192);\n      const ctx_r191 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r191.closeReturnItemModel());\n    });\n    i0.ɵɵelement(9, \"img\", 157);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_13_div_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r192);\n      const ctx_r193 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r193.confirmToReturnSingleItem());\n    });\n    i0.ɵɵelement(14, \"img\", 159);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 4, \"orderDetails.requestReturn\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 6, \"orderDetails.areYouSureWantReturnItem\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 8, \"deleteItemPopupComponent.cancel\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 10, \"orderDetails.proceed\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵelement(1, \"img\", 145);\n    i0.ɵɵelementStart(2, \"p\", 146);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 147);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"orderDetails.returnRequestSent\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 4, \"orderDetails.notifiedMerchantReturnedRequest\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_13_div_0_Template, 54, 54, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_13_div_1_Template, 22, 13, \"div\", 9);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_13_div_2_Template, 18, 12, \"div\", 160);\n    i0.ɵɵtemplate(3, IndexComponent_ng_template_13_div_3_Template, 8, 6, \"div\", 129);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.requestReturnItemDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.pickUpLocation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.confirmReturnItem);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.returnedItemConifermed);\n  }\n}\nfunction IndexComponent_ng_template_14_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r198 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 131)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 190)(8, \"div\", 191)(9, \"button\", 192);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_14_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r198);\n      const ctx_r197 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r197.closeReturnItemModel());\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 191)(12, \"button\", 198);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_14_div_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r198);\n      const ctx_r199 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r199.proceedReturnSingleItem());\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r194 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, \"orderDetails.amount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r194.currencyCode, \" \", ctx_r194.singleOrderTotal, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(10, 8, \"orderDetails.back\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r194.selectedReturnReason || !ctx_r194.selectedReturnItem.selectedItemFileName)(\"label\", i0.ɵɵpipeBind1(13, 10, \"orderDetails.returnItem\"));\n  }\n}\nfunction IndexComponent_ng_template_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r201 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 131)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 190)(8, \"div\", 191)(9, \"button\", 192);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_14_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r201);\n      const ctx_r200 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r200.backConfirmPicUpLocation());\n    });\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 191)(12, \"button\", 194);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_14_div_1_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r201);\n      const ctx_r202 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r202.confirmPicUpLocation());\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r195 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"orderDetails.amount\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r195.currencyCode, \" \", ctx_r195.singleOrderTotal, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(10, 7, \"orderDetails.back\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(13, 9, \"orderDetails.return\"));\n  }\n}\nfunction IndexComponent_ng_template_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r204 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_14_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r204);\n      const ctx_r203 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r203.closeReturnItemModel());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(2, 1, \"orderDetails.ok\"));\n  }\n}\nfunction IndexComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_14_div_0_Template, 14, 12, \"div\", 9);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_14_div_1_Template, 14, 11, \"div\", 9);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_14_div_2_Template, 3, 3, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.requestReturnItemDetails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.pickUpLocation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.returnedItemConifermed);\n  }\n}\nfunction IndexComponent_ng_container_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"section\", 200);\n    i0.ɵɵelement(2, \"img\", 201)(3, \"img\", 202);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"article\")(5, \"p\", 203);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 12);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const previousStatus_r206 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", previousStatus_r206 == null ? null : previousStatus_r206.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 2, previousStatus_r206 == null ? null : previousStatus_r206.date, \"dd/MM/yyyy hh:mm a\"), \" \");\n  }\n}\nfunction IndexComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, IndexComponent_ng_container_20_div_1_Template, 10, 5, \"div\", 199);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.selectedProductLogs == null ? null : ctx_r11.selectedProductLogs.previousStatus);\n  }\n}\nfunction IndexComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 204);\n    i0.ɵɵelement(2, \"span\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 205);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction IndexComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 206);\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", (ctx_r14.selectedProductLogs == null ? null : ctx_r14.selectedProductLogs.currentStatus == null ? null : ctx_r14.selectedProductLogs.currentStatus.status) === \"ReturnRejected\" || (ctx_r14.selectedProductLogs == null ? null : ctx_r14.selectedProductLogs.currentStatus == null ? null : ctx_r14.selectedProductLogs.currentStatus.status) === \"Returned\" ? \"assets/images/returnLogStatus.svg\" : \"assets/images/success-log.svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IndexComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"img\", 207);\n    i0.ɵɵelementStart(2, \"p\", 208);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.selectedProductLogs == null ? null : ctx_r15.selectedProductLogs.futureStatus == null ? null : ctx_r15.selectedProductLogs.futureStatus.status, \" \");\n  }\n}\nconst _c5 = function () {\n  return {\n    width: \"30vw\"\n  };\n};\nconst _c6 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"90vw\"\n  };\n};\nconst _c7 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"640px\": \"80vw\"\n  };\n};\nexport let IndexComponent = /*#__PURE__*/(() => {\n  class IndexComponent {\n    primengConfig;\n    route;\n    transactionService;\n    orderService;\n    store;\n    messageService;\n    translate;\n    reviewService;\n    loaderService;\n    appDataService;\n    permissionService;\n    platformId;\n    _location;\n    fb;\n    router;\n    modalService;\n    cancelReason;\n    returnReason;\n    $gtmService;\n    _GACustomEvents;\n    items = [];\n    disableCent;\n    decimalValue = 0;\n    reasons = [];\n    selectedReason;\n    displayModal = false;\n    approvedModal = false;\n    displayRateModal = false;\n    displaySubmitModal = false;\n    home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    cancelAllOrder = false;\n    confirmCancelAllOrder = false;\n    proceedCancellingOrder = false;\n    cancelSingleOrder = false;\n    cancelOrderDetails = false;\n    cancelSingleOrderDetails = false;\n    confirmCancelSingleOrder = false;\n    proceedCancellingSingleOrder = false;\n    requestReturnModel = false;\n    requestReturnDetails = false;\n    confirmReturnOrder = false;\n    returnedOrderConifermed = false;\n    selectedProduct;\n    selectedFileName = '';\n    selectedItemFileName = '';\n    selectedReturnItem;\n    requestReturnItemDetails = false;\n    confirmReturnItem = false;\n    returnItemModel = false;\n    returnedItemConifermed = false;\n    logsDialog = false;\n    selectedProductLogs;\n    UserTypeOfCancellationEnum = UserTypeOfCancellation;\n    OrderStatusEnum = OrderStatus;\n    subOrderDetailStatusEnum = SubOrderDetailStatus;\n    OrderItemStatusEnum = OrderItemStatus;\n    displayRefundModal = false;\n    approvedRefundModal = false;\n    displayReviewModal = false;\n    approvedReviewModel = false;\n    orderItems = [];\n    order = {\n      items: []\n    };\n    order_id;\n    transactionData;\n    totalCost = 0;\n    baseUrl = environment.apiEndPoint;\n    refundReasons = [];\n    disableBtn = false;\n    refundReason = '';\n    isRequiredError = false;\n    reviewrating;\n    rating;\n    RefundReason;\n    ReviewDescribtion;\n    displayReview = false;\n    password;\n    click = false;\n    btnLabel = 'Refresh';\n    btnIcon = 'pi pi-refresh';\n    currencyCode = '';\n    tenantId = '';\n    showSecret = false;\n    navbarData;\n    isShipmentFeeExist = false;\n    _BaseURL = environment.apiEndPoint;\n    cancelledReasons = [];\n    selectedCanceledReason;\n    subOrderSelectedCanceledReason;\n    canceledOrder;\n    returnedOrder;\n    cancelReasonDescriptionView;\n    subOrderCancelReasonDescription;\n    returnItemReasonDescription;\n    returnItemReason;\n    returnReasons = [];\n    selectedReturnReason;\n    showActionOrderButton = true;\n    screenWidth;\n    isAllowCancelOrders = false;\n    isAllowRefundOrders = false;\n    allowCancelItems = false;\n    allowRefundItems = false;\n    allowCancelSpecificQuantityItems = false;\n    allowRefundSpecificQuantityItems = false;\n    display = false;\n    activeIndex = 0;\n    activeOrderItemIndex = 0;\n    products = [{\n      name: 'LG C2 42 (106cm) 4K Smart OLED evo TV',\n      sku: '1234568'\n    }\n    // Add more products as needed\n    ];\n\n    refundForm;\n    uploadedFileName;\n    refundProdList = [];\n    refundModalOrderlist = [];\n    Address;\n    finalModal = false;\n    model;\n    langChangeSubscription;\n    isMobileLayout = false;\n    pickUpLocation = false;\n    orderPickUpLocation = false;\n    orderDiscount = 0;\n    formData;\n    uniqueSubOrderIDs = [];\n    fullUniqueSubOrder;\n    priceWithShipmentFee = false;\n    singleOrderTotal = 0;\n    utility = UtilityFunctions;\n    constructor(primengConfig, route, transactionService, orderService, store, messageService, translate, reviewService, loaderService, appDataService, permissionService, platformId, _location, fb, router, modalService, cancelReason, returnReason, $gtmService, _GACustomEvents) {\n      this.primengConfig = primengConfig;\n      this.route = route;\n      this.transactionService = transactionService;\n      this.orderService = orderService;\n      this.store = store;\n      this.messageService = messageService;\n      this.translate = translate;\n      this.reviewService = reviewService;\n      this.loaderService = loaderService;\n      this.appDataService = appDataService;\n      this.permissionService = permissionService;\n      this.platformId = platformId;\n      this._location = _location;\n      this.fb = fb;\n      this.router = router;\n      this.modalService = modalService;\n      this.cancelReason = cancelReason;\n      this.returnReason = returnReason;\n      this.$gtmService = $gtmService;\n      this._GACustomEvents = _GACustomEvents;\n      this.disableCent = localStorage.getItem('DisableCents');\n      let value = localStorage.getItem('CurrencyDecimal');\n      if (value) this.decimalValue = parseInt(value);\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n      this.refundForm = this.fb.group({\n        refunds: this.fb.array([])\n      });\n      this.initForm();\n    }\n    ngOnInit() {\n      this.isShipmentFeeExist = this.permissionService.hasPermission('Shipment-Fee');\n      this.isAllowCancelOrders = this.permissionService.hasPermission('IsAllowCancelOrders');\n      this.isAllowRefundOrders = this.permissionService.hasPermission('IsAllowRefundOrders');\n      this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n      this.allowCancelItems = this.permissionService.hasPermission('AllowCancelItems');\n      this.allowRefundItems = this.permissionService.hasPermission('AllowRefundItems');\n      this.allowCancelSpecificQuantityItems = this.permissionService.hasPermission('AllowCancelSpecificQuantityItems');\n      this.allowRefundSpecificQuantityItems = this.permissionService.hasPermission('AllowRefundSpecificQuantityItems');\n      this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n      this.order_id = this.route.snapshot.params['id'];\n      this.formData = {\n        OrderId: this.order_id,\n        PIN: null,\n        CancelAllOrder: false,\n        CancelOrderItem: [],\n        CancelledReasonId: null,\n        CancelReasonDescription: null\n      };\n      this.translate.get('order').subscribe(res => {\n        this.items = [{\n          icon: '',\n          label: res.yourAccount,\n          routerLink: '/account'\n        }, {\n          icon: '',\n          label: res.yourOrders,\n          routerLink: '/orders'\n        }, {\n          icon: '',\n          label: res.orderDetails,\n          routerLink: `/order/${this.order_id}`\n        }];\n        this.$gtmService.pushPageView('account', 'your orders', this.order_id);\n      });\n      this.canceledOrder = {\n        cancelledItems: [],\n        cancelReasonDescription: '',\n        cancelAllOrder: false,\n        cancelledReasonId: 0,\n        orderId: 0\n      };\n      this.primengConfig.ripple = true;\n      this.order_id = this.route.snapshot.params['id'];\n      this.loadData();\n      this.tenantId = localStorage.getItem('tenantId');\n      this.setModel();\n      this.langChangeSubscription = this.translate.onLangChange.subscribe(event => {\n        this.setModel();\n      });\n    }\n    cancelOrderModal() {\n      this.click = true;\n      this.loadCancelReasons();\n      this.displayModal = true;\n      this.disableBtn = true;\n    }\n    reviewOrderModal() {\n      this.click = true;\n      this.loadRefundReasons();\n      this.displayRateModal = true;\n      this.disableBtn = true;\n    }\n    reviewSubmitModal() {\n      this.click = true;\n      this.loadRefundReasons();\n      this.displayRateModal = false;\n      this.displaySubmitModal = true;\n      this.disableBtn = true;\n    }\n    reviewOkButton() {\n      this.displaySubmitModal = false;\n    }\n    modelChanged(password) {\n      this.password = password;\n    }\n    openRefundOrderModal() {\n      this.click = true;\n      this.displayRefundModal = true;\n    }\n    refundApprovalModal() {\n      if (!this.refundReason) {\n        this.isRequiredError = true;\n        return;\n      }\n      const requestRefund = {\n        orderId: Number(this.order_id),\n        refundReasonDescription: this.refundReason\n      };\n      this.orderService.requestOrderRefund(requestRefund).subscribe(res => {\n        if (res.success) {\n          this.displayRefundModal = false;\n          this.approvedRefundModal = true;\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.orderRefundRequestFailed'),\n            detail: this.translate.instant('ErrorMessages.errorWhileTheRefundRequestProcess')\n          });\n        }\n      });\n    }\n    ApprovelReviewModal() {\n      this.AddReviews();\n    }\n    loadData() {\n      this.loaderService.show();\n      this.showActionOrderButton = false;\n      this.orderService.getAllOrderDetail(this.order_id).subscribe({\n        next: res => {\n          this.order = res?.data[0];\n          this.orderDiscount = this.order?.discount ? this.order?.discount : 0;\n          if (this.order?.logisticFee) {\n            this.order.shippingAmount = parseInt(this.order.shippingAmount, 0) + parseInt(this.order.logisticFee, 0);\n          }\n          this.currencyCode = this.order.currencyCode ? this.order.currencyCode : this.order.orderCurrencyCode ?? localStorage.getItem('currency')?.toString();\n          this.totalCost = this.order.grandTotal ? this.order.grandTotal : 0;\n          if (this.order?.items && this.order.items.length > 0) {\n            this.orderItems = this.order.items;\n            this.initializeFormData();\n          }\n          this.loaderService.hide();\n          this.showActionOrderButton = true;\n          this.getOrderTransactions();\n          if (this.order?.status == 'Delivered' && !this.order.isReviewed) this.displayReviewModal = true;\n          this.getLastShipmentFees();\n        },\n        error: err => {\n          this.loaderService.hide();\n        }\n      });\n      this.initializeFormData();\n    }\n    getLastShipmentFees() {\n      const allSubOrderIdFromItems = this.order.items.filter(eachItem => {\n        return eachItem.orderItemStatus !== \"Cancelled\" && eachItem.orderItemStatus !== \"Returned\" && eachItem.orderItemStatus !== \"ReturnInProgress\";\n      }).map(eachItem => {\n        return eachItem.subOrderId;\n      });\n      this.uniqueSubOrderIDs = allSubOrderIdFromItems.filter((item, index, arr) => {\n        return arr.indexOf(item) === arr.lastIndexOf(item);\n      });\n    }\n    loadRefundReasons() {\n      this.orderService.getRefundReasons().subscribe(res => {\n        if (res.reasons.length > 0) {\n          this.refundReasons = res.reasons;\n        }\n      });\n    }\n    loadCancelReasons() {\n      this.cancelReason.getAllCancelReason(this.UserTypeOfCancellationEnum.Consumer).subscribe(res => {\n        if (res.reasons.length > 0) {\n          this.cancelledReasons = res.reasons;\n        }\n      });\n    }\n    AddReviews() {\n      let model = new CreateReviewViewModel();\n      if (this.reviewrating == 0 || this.reviewrating == undefined) {\n        return this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: this.translate.instant('ErrorMessages.pleaseEnterValidReview')\n        });\n      } else {\n        model.Rate = this.reviewrating;\n        model.ReviewType = this.reviewrating;\n        model.Description = this.ReviewDescribtion;\n        model.OrderId = this.order_id;\n        model.SpecProductIds = this.orderItems.map(a => a.specsProductId);\n        this.reviewService.addProductReviews(model).subscribe(res => {\n          if (res) this.displayReviewModal = false;\n          this.displaySubmitModal = true;\n        });\n      }\n    }\n    orderStatus(status) {\n      let statusName = '';\n      this.translate.get('order').subscribe(res => {\n        statusName = res[status];\n      });\n      return statusName ? statusName : status;\n    }\n    toggleSecret() {\n      this.showSecret = !this.showSecret;\n    }\n    orderListImage(img) {\n      return UtilityFunctions.verifyImageURL(img, this._BaseURL);\n    }\n    errorHandler(event) {\n      if (environment.isStoreCloud) {\n        event.target.src = \"assets/images/placeholder.png\";\n      } else {\n        event.target.src = \"assets/images/mtn-alt.png\";\n      }\n    }\n    cancelAllOrderModel() {\n      this._GACustomEvents.orderCancelInitiatedEvent(this.order_id);\n      this.loadCancelReasons();\n      this.cancelAllOrder = true;\n      this.cancelOrderDetails = true;\n    }\n    cancelAllApprovedModal() {\n      if (this.selectedCanceledReason !== undefined) return this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mustSelectCancelationReason')\n      });\n      this.cancelOrderDetails = false;\n      this.confirmCancelAllOrder = true;\n    }\n    cancelApprovedModal() {\n      this.loaderService.show();\n      if (this.selectedCanceledReason == undefined) {\n        this.cancelOrderDetails = true;\n        this.confirmCancelAllOrder = false;\n        this.loaderService.hide();\n      }\n      this.canceledOrder = {\n        orderId: this.order_id,\n        cancelledReasonId: this.selectedCanceledReason?.id,\n        cancelAllOrder: true,\n        cancelReasonDescription: this.cancelReasonDescriptionView,\n        cancelledItems: []\n      };\n      this.orderService.cancelOrder(this.canceledOrder).subscribe(res => {\n        if (res?.success) {\n          this.cancelOrderDetails = false;\n          this.confirmCancelAllOrder = false;\n          this.proceedCancellingOrder = true;\n          this.loaderService.hide();\n        } else {\n          this.closeCancellingOrder();\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.orderCancellationFailed'),\n            detail: res?.message ?? this.translate.instant('ErrorMessages.errorWhileCancelYourOrder')\n          });\n        }\n      });\n    }\n    closeCancellingOrder() {\n      this.cancelAllOrder = false;\n      this.confirmCancelAllOrder = false;\n      this.proceedCancellingOrder = false;\n      this.selectedCanceledReason.id = 0;\n      this.cancelReasonDescriptionView = '';\n      this.loaderService.hide();\n      this.loadData();\n    }\n    onCancelAllOrderApproval() {\n      if (this.isFormValid()) {\n        const payload = {\n          OrderId: +this.formData.OrderId,\n          PIN: null,\n          CancelAllOrder: false,\n          CancelledItems: this.formData.CancelOrderItem.map(item => ({\n            CancelledReasonId: item.CancelledReasonId?.id,\n            CancelReasonDescription: item.CancelReasonDescription,\n            ItemId: item.ItemId\n          })),\n          CancelledReasonId: null,\n          CancelReasonDescription: null\n        };\n        this.orderService.rejectAllOrder(payload).subscribe(res => {\n          if (res?.success) {\n            this._GACustomEvents.orderCancelSubmittedEvent(this.order_id);\n            this.cancelOrderDetails = false;\n            this.confirmCancelAllOrder = false;\n            this.proceedCancellingOrder = true;\n            this.messageService.add({\n              severity: 'success',\n              summary: this.translate.instant('orderDetails.orderCancelled'),\n              detail: this.translate.instant('orderDetails.yourOrderHasBeenCancelled')\n            });\n            this.loaderService.hide();\n            this.loadData();\n          } else {\n            this.closeCancellingOrder();\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.orderCancellationFailed'),\n              detail: res.message\n            });\n          }\n        });\n      } else {\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: this.translate.instant('ErrorMessages.mustSelectCancelationReason')\n        });\n      }\n    }\n    filterUniqueSubOrders(key) {\n      this.uniqueSubOrderIDs.map(eachEl => {\n        if (eachEl === key) {\n          this.priceWithShipmentFee = true;\n          this.fullUniqueSubOrder = this.order.subOrder.find(eachSubOrder => eachSubOrder.id === key);\n        }\n      });\n    }\n    cancelSingleOrderModel(product) {\n      this._GACustomEvents.orderCancelInitiatedEvent(this.order_id);\n      this.filterUniqueSubOrders(product.subOrderId);\n      this.loadCancelReasons();\n      this.selectedProduct = product;\n      this.getSingleItemPrice(product.subOrderId, product.subOrderDetailsId);\n      this.cancelSingleOrder = true;\n      this.cancelSingleOrderDetails = true;\n    }\n    cancelSingleApprovedModal() {\n      if (this.subOrderSelectedCanceledReason == undefined) return this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: this.translate.instant('ErrorMessages.mustSelectCancelationReason')\n      });\n      this.cancelSingleOrderDetails = false;\n      this.confirmCancelSingleOrder = true;\n    }\n    confirmToCancelSingleApprovedModal() {\n      this.loaderService.show();\n      if (this.subOrderSelectedCanceledReason == undefined) {\n        this.cancelSingleOrderDetails = true;\n        this.confirmCancelSingleOrder = false;\n      }\n      this.canceledOrder = {\n        orderId: this.order_id,\n        cancelledReasonId: null,\n        cancelAllOrder: false,\n        cancelReasonDescription: null,\n        cancelledItems: [{\n          ItemId: this.selectedProduct.subOrderDetailsId,\n          cancelledReasonId: this.subOrderSelectedCanceledReason.id,\n          CancelReasonDescription: this.subOrderCancelReasonDescription\n        }]\n      };\n      this.orderService.cancelOrder(this.canceledOrder).subscribe(res => {\n        if (res?.success) {\n          this._GACustomEvents.orderCancelSubmittedEvent(this.order_id);\n          this.cancelSingleOrderDetails = false;\n          this.confirmCancelSingleOrder = false;\n          this.proceedCancellingSingleOrder = true;\n          this.loaderService.hide();\n          this.loadData();\n        } else {\n          this.closeCancellingSingleOrder();\n          this.disableBtn = false;\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.orderCancellationFailed'),\n            detail: res?.message ?? this.translate.instant('ErrorMessages.errorWhileCancelYourOrder')\n          });\n        }\n      });\n    }\n    closeCancellingSingleOrder() {\n      this.cancelSingleOrder = false;\n      this.confirmCancelSingleOrder = false;\n      this.proceedCancellingSingleOrder = false;\n      this.singleOrderTotal = 0;\n      // this.subOrderSelectedCanceledReason.id = 0;\n      this.subOrderCancelReasonDescription = '';\n      this.priceWithShipmentFee = false;\n      this.loaderService.hide();\n      this.loadData();\n    }\n    loadReturnReasons() {\n      this.returnReason.getAllReturnReason(this.UserTypeOfCancellationEnum.Consumer).subscribe(res => {\n        if (res.reasons.length > 0) {\n          this.returnReasons = res.reasons;\n        }\n      });\n    }\n    requestReturn() {\n      const productIds = this.orderItems && this.orderItems.length > 0 ? this.orderItems.map(product => product.productId ?? product.subOrderDetailsId ?? 0) : [0];\n      this._GACustomEvents.returnInitiatedEvent(this.order_id, productIds);\n      this.loadReturnReasons();\n      this.requestReturnDetails = true;\n      this.requestReturnModel = true;\n    }\n    proceedReturnOrder() {\n      for (const product of this.orderItems) {\n        if (product.selectedReturnReason == undefined) {\n          return this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: this.translate.instant('ErrorMessages.mustSelectReturnReason')\n          });\n        }\n        if (!product.selectedImageFile) {\n          return this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: this.translate.instant('ErrorMessages.mustUploadItemImage')\n          });\n        }\n        const maxSizeInBytes = 2 * 1024 * 1024; // 2 megabytes\n        if (product.selectedImageFile.size > maxSizeInBytes) {\n          return this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.fetchError'),\n            detail: this.translate.instant('ErrorMessages.mustUploadItemImageSize')\n          });\n        }\n      }\n      this.requestReturnDetails = false;\n      this.orderPickUpLocation = true;\n    }\n    confirmPicUpLocationOrder() {\n      this.orderPickUpLocation = false;\n      this.confirmReturnOrder = true;\n    }\n    backConfirmPicUpLocationOrder() {\n      this.orderPickUpLocation = false;\n      this.confirmReturnOrder = false;\n      this.requestReturnDetails = true;\n    }\n    readImageFile(file) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        return new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onloadend = () => {\n            const base64Data = reader.result;\n            resolve(base64Data);\n          };\n          reader.onerror = () => {\n            reject(new Error(_this.translate.instant('ErrorMessages.errorReadingImage')));\n          };\n          reader.readAsDataURL(file);\n        });\n      })();\n    }\n    confirmToReturnModal() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const itemsToReturn = [];\n        for (const product of _this2.orderItems) {\n          if (product.selectedImageFile) {\n            try {\n              const evidenceFile = yield _this2.readImageFile(product.selectedImageFile);\n              itemsToReturn.push({\n                ItemId: product.subOrderDetailsId,\n                RefundReasonId: product.selectedReturnReason.id,\n                RefundReasonDescription: product.returnReasonDescription,\n                evidenceFile: evidenceFile.split(',')[1]\n              });\n            } catch (error) {\n              _this2.messageService.add({\n                severity: 'error',\n                summary: _this2.translate.instant('ErrorMessages.errorReadingImage')\n              });\n              return;\n            }\n          }\n        }\n        if (itemsToReturn.length === 0) {\n          return;\n        }\n        const requestData = {\n          orderId: _this2.order_id,\n          refundAllOrder: true,\n          refundReasonDescription: '',\n          refundOrderItems: itemsToReturn\n        };\n        _this2.orderService.returnOrder(requestData).subscribe(res => {\n          if (res?.success) {\n            _this2._GACustomEvents.returnSubmittedEvent(_this2.order_id);\n            _this2.requestReturnDetails = false;\n            _this2.confirmReturnOrder = false;\n            _this2.returnedOrderConifermed = true;\n          } else {\n            _this2.messageService.add({\n              severity: 'error',\n              summary: _this2.translate.instant('ErrorMessages.orderReturnedFailed'),\n              detail: res?.message ?? _this2.translate.instant('ErrorMessages.errorWhileReturnYourOrder')\n            });\n          }\n        });\n      })();\n    }\n    onFileSelected(event, product, index) {\n      const fileInput = event.target;\n      if (fileInput.files && fileInput.files[0]) {\n        const file = fileInput.files[0];\n        // Validate file type\n        const validFileTypes = ['image/png', 'image/jpeg', 'video/mp4'];\n        if (!validFileTypes.includes(file.type)) {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.uploadItemFailed'),\n            detail: this.translate.instant('ErrorMessages.itemUploadAllowed')\n          });\n          return;\n        }\n        product.selectedFileName = fileInput.files[0].name;\n        product.image = URL.createObjectURL(file);\n        product.selectedImageFile = file;\n        const productId = product.productId ?? product.subOrderDetailsId ?? 0;\n        this._GACustomEvents.returnEvidenceUploadedEvent(file.type, file.size, 'success', productId);\n      }\n    }\n    cancelReturnOrder() {\n      this.priceWithShipmentFee = false;\n      this.requestReturnDetails = false;\n      this.requestReturnModel = false;\n      this.confirmReturnOrder = false;\n      this.orderPickUpLocation = false;\n      this.returnedOrderConifermed = false;\n      this.orderItems.forEach(product => {\n        product.selectedReturnReason = null;\n        product.returnReasonDescription = '';\n      });\n      this.returnedOrder = {\n        orderId: this.order_id,\n        refundAllOrder: false,\n        refundReasonDescription: '',\n        refundOrderItems: []\n      };\n      this.loadData();\n    }\n    returnSingleItem(product) {\n      const productId = product.productId ?? product.subOrderDetailsId ?? 0;\n      this._GACustomEvents.returnInitiatedEvent(this.order_id, [productId]);\n      this.filterUniqueSubOrders(product.subOrderId);\n      this.loadReturnReasons();\n      this.selectedReturnItem = product;\n      this.getSingleItemPrice(product.subOrderId, product.subOrderDetailsId);\n      this.returnItemModel = true;\n      this.requestReturnItemDetails = true;\n    }\n    proceedReturnSingleItem() {\n      if (this.selectedReturnReason == undefined) {\n        return this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: this.translate.instant('ErrorMessages.mustSelectReturnReason')\n        });\n      }\n      if (!this.selectedReturnItem.selectedImageFile) {\n        return this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: this.translate.instant('ErrorMessages.mustUploadItemImage')\n        });\n      }\n      const maxSizeInBytes = 2 * 1024 * 1024; // 2 megabytes\n      if (this.selectedReturnItem.selectedImageFile.size > maxSizeInBytes) {\n        return this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.fetchError'),\n          detail: this.translate.instant('ErrorMessages.mustUploadItemImageSize')\n        });\n      }\n      this.requestReturnItemDetails = false;\n      this.pickUpLocation = true;\n    }\n    confirmPicUpLocation() {\n      this.pickUpLocation = false;\n      this.confirmReturnItem = true;\n    }\n    backConfirmPicUpLocation() {\n      this.pickUpLocation = false;\n      this.confirmReturnItem = false;\n      this.requestReturnItemDetails = true;\n    }\n    confirmToReturnSingleItem() {\n      if (!this.returnedOrder || this.returnedOrder.refundOrderItems && this.returnedOrder.refundOrderItems.length === 0) {\n        this.returnedOrder = {\n          orderId: this.order_id,\n          refundAllOrder: false,\n          refundReasonDescription: '',\n          refundOrderItems: [{\n            ItemId: this.selectedReturnItem.subOrderDetailsId,\n            RefundReasonId: this.selectedReturnReason.id,\n            RefundReasonDescription: this.returnItemReasonDescription,\n            evidenceFile: this.selectedReturnItem.selectedImageFile\n          }]\n        };\n      }\n      this.orderService.returnOrder(this.returnedOrder).subscribe(res => {\n        if (res?.success) {\n          this._GACustomEvents.returnSubmittedEvent(this.order_id);\n          this.requestReturnItemDetails = false;\n          this.confirmReturnItem = false;\n          this.returnedItemConifermed = true;\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.orderReturnedFailed'),\n            detail: res?.message ?? this.translate.instant('ErrorMessages.errorWhileReturnYourOrder')\n          });\n        }\n      });\n    }\n    onFileItemSelected(event, product) {\n      const fileInput = event.target;\n      if (fileInput.files && fileInput.files[0]) {\n        const file = fileInput.files[0];\n        // Validate file type\n        const validFileTypes = ['image/png', 'image/jpeg', 'video/mp4'];\n        if (!validFileTypes.includes(file.type)) {\n          this.messageService.add({\n            severity: 'error',\n            summary: this.translate.instant('ErrorMessages.uploadItemFailed'),\n            detail: this.translate.instant('ErrorMessages.itemUploadAllowed')\n          });\n          return;\n        }\n        this.selectedReturnItem.selectedItemFileName = file.name;\n        this.convertToBase64(file).then(base64 => {\n          const base64WithoutPrefix = base64.split(',')[1];\n          this.selectedReturnItem.selectedImageFile = base64WithoutPrefix;\n          const productId = product.productId ?? product.subOrderDetailsId ?? 0;\n          this._GACustomEvents.returnEvidenceUploadedEvent(file.type, file.size, 'success', productId);\n        });\n      }\n    }\n    convertToBase64(file) {\n      return _asyncToGenerator(function* () {\n        return new Promise(resolve => {\n          const reader = new FileReader();\n          reader.onloadend = () => {\n            resolve(reader.result);\n          };\n          reader.readAsDataURL(file);\n        });\n      })();\n    }\n    closeReturnItemModel() {\n      this.priceWithShipmentFee = false;\n      this.requestReturnItemDetails = false;\n      this.confirmReturnItem = false;\n      this.returnItemModel = false;\n      this.singleOrderTotal = 0;\n      this.returnedItemConifermed = false;\n      this.pickUpLocation = false;\n      this.selectedReturnItem.selectedImageFile = null;\n      this.selectedReturnItem.selectedItemFileName = '';\n      this.returnedOrder = {\n        orderId: this.order_id,\n        refundAllOrder: false,\n        refundReasonDescription: '',\n        refundOrderItems: []\n      };\n      this.selectedReturnItem = [];\n      this.returnItemReasonDescription = '';\n      this.selectedReturnReason.id = undefined;\n      this.loadData();\n    }\n    getOrderTransactions() {\n      this.loaderService.show();\n      this.transactionService.GetOrderTransaction(this.order_id).subscribe({\n        next: res => {\n          this.transactionData = res.data;\n          this.loaderService.hide();\n        },\n        error: err => {}\n      });\n    }\n    fullOrderCancelAvailability() {\n      const statusIds = [];\n      let isAnyOrderDispatched = false;\n      this.orderItems.forEach(orderProduct => {\n        statusIds.push(orderProduct.orderItemStatusId);\n        if (orderProduct.isDispatched) {\n          isAnyOrderDispatched = true;\n        }\n      });\n      if (!statusIds?.length || isAnyOrderDispatched) {\n        return false;\n      }\n      if (statusIds.includes(this.OrderItemStatusEnum.Processing) || statusIds.includes(this.OrderItemStatusEnum.Delivered) || statusIds.includes(this.OrderItemStatusEnum.Cancelled) || statusIds.includes(this.OrderItemStatusEnum.CancelledByUser) || statusIds.includes(this.OrderItemStatusEnum.Returned) || statusIds.includes(this.OrderItemStatusEnum.RequestReturn) || statusIds.includes(this.OrderItemStatusEnum.ReturnInProgress) || statusIds.includes(this.OrderItemStatusEnum.ReturnRejected)) {\n        return false;\n      }\n      return true;\n    }\n    fullOrderReturnAvailability() {\n      const returnStatusIds = [];\n      let isOrderRefundable = false;\n      this.orderItems.forEach(orderProduct => {\n        returnStatusIds.push(orderProduct.orderItemStatusId);\n        if (orderProduct.isRefundable == false) {\n          isOrderRefundable = true;\n        }\n      });\n      if (!returnStatusIds?.length || isOrderRefundable) {\n        return false;\n      }\n      if (returnStatusIds.includes(this.OrderItemStatusEnum.Pending) || returnStatusIds.includes(this.OrderItemStatusEnum.Processing) || returnStatusIds.includes(this.OrderItemStatusEnum.Cancelled) || returnStatusIds.includes(this.OrderItemStatusEnum.CancelledByUser) || returnStatusIds.includes(this.OrderItemStatusEnum.Returned) || returnStatusIds.includes(this.OrderItemStatusEnum.RequestReturn) || returnStatusIds.includes(this.OrderItemStatusEnum.Placed) || returnStatusIds.includes(this.OrderItemStatusEnum.ReturnInProgress) || returnStatusIds.includes(this.OrderItemStatusEnum.ReturnRejected)) {\n        return false;\n      }\n      return true;\n    }\n    backButton() {\n      this._location.back();\n    }\n    orderstatus(status) {\n      if (status === OrderItemStatus.Cancelled || status === OrderItemStatus.Returned || status === OrderItemStatus.CancelledByUser || status === OrderItemStatus.ReturnRejected) {\n        return {\n          color: 'red',\n          background: 'rgba(255, 0, 0, 0.1)'\n        };\n      } else if (status === OrderItemStatus.Placed) {\n        return {\n          color: '#d8d8d8',\n          background: 'rgba(1, 180, 103, 0.10)'\n        };\n      } else if (status === OrderItemStatus.Pending || status === OrderItemStatus.Processing || status === OrderItemStatus.ReturnInProgress) {\n        return {\n          color: 'orange',\n          background: 'rgba(255, 165, 0, 0.1)'\n        };\n      } else if (status === OrderItemStatus.Delivered) {\n        return {\n          color: '#01B467',\n          background: 'rgba(1, 180, 103, 0.10)'\n        };\n      } else if (status === OrderItemStatus.RequestReturn) {\n        return {\n          color: '#ff6c00',\n          background: 'rgba(255, 165, 0, 0.1)'\n        };\n      } else {\n        return {\n          color: '#01B467',\n          background: 'rgba(1, 180, 103, 0.10)'\n        };\n      }\n    }\n    get refunds() {\n      return this.refundForm.get('refunds');\n    }\n    initForm() {\n      this.refundForm = this.fb.group({\n        reason: [''],\n        details: [''],\n        upload: ['']\n      });\n    }\n    next() {\n      if (!this.refundForm.valid) return;\n      this.refundProdList.push(this.refundForm.value);\n      this.refundForm.reset();\n      this.uploadedFileName = '';\n      if (this.activeOrderItemIndex < this.refundModalOrderlist.length - 1) {\n        this.activeOrderItemIndex++;\n      } else {\n        this.activeIndex++;\n        // this.display = false; // Uncomment if needed\n      }\n    }\n\n    back() {\n      if (this.activeIndex > 0) {\n        this.activeIndex = this.activeIndex - 1;\n        this.refundProdList = [];\n      } else if (this.activeIndex == 0 && this.activeOrderItemIndex == 0) {\n        this.close();\n      }\n      if (this.activeOrderItemIndex > 0 && this.activeOrderItemIndex < this.refundModalOrderlist.length && this.activeIndex == 0) {\n        this.refundProdList.splice(this.activeOrderItemIndex, 1);\n        this.activeOrderItemIndex--;\n      }\n    }\n    return() {\n      this.close();\n      const confirmationModalData = {\n        initialState: {\n          confirmationModalDetails: {\n            header: this.translate.instant('orderDetails.requestRefundOrder'),\n            message: this.translate.instant('orderDetails.requestRefundMessage'),\n            returnModal: true\n          }\n        }\n      };\n      const bsModalRefConfirmation = this.modalService.show(ConfirmationModalComponent, confirmationModalData);\n      bsModalRefConfirmation.content.submit.pipe(take(1)).subscribe(value => {\n        if (value) {\n          const newconfirmationModalData = {\n            initialState: {\n              confirmationModalDetails: {\n                header: this.translate.instant('orderDetails.refundRequestSent'),\n                message: this.translate.instant('orderDetails.notifiedMerchantReturnedRequest'),\n                returnModalConfirmation: true\n              }\n            }\n          };\n          const bsModalRefConfirmation = this.modalService.show(ConfirmationModalComponent, newconfirmationModalData);\n          bsModalRefConfirmation.content.submit.pipe(take(1)).subscribe(value => {\n            if (value) {}\n          });\n        }\n      });\n    }\n    showDialog(orderItems) {\n      if (Array.isArray(orderItems)) {\n        this.refundModalOrderlist = JSON.parse(JSON.stringify(orderItems));\n      } else {\n        this.refundModalOrderlist.push(orderItems);\n      }\n      this.display = true;\n    }\n    onUpload(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.uploadedFileName = input.files[0].name;\n        this.refundForm.controls.upload.setValue(this.uploadedFileName);\n      }\n    }\n    removeFile() {\n      this.uploadedFileName = null;\n    }\n    close() {\n      this.display = false;\n      this.activeIndex = 0;\n      this.refundProdList = [];\n      this.uploadedFileName = '';\n      this.activeOrderItemIndex = 0;\n      this.refundModalOrderlist = [];\n    }\n    productDetails(product) {\n      this.router.navigate(['product', product.productId, 1], {\n        queryParams: {\n          tenantId: this.tenantId,\n          lang: localStorage.getItem(\"lang\")\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    setModel() {\n      this.translate.get(['orderDetails.returnReason', 'orderDetails.pickUpPlace']).subscribe(translations => {\n        this.model = [{\n          label: translations['orderDetails.returnReason']\n        }, {\n          label: translations['orderDetails.pickUpPlace']\n        }];\n      });\n    }\n    initializeFormData() {\n      this.formData.CancelOrderItem = this.orderItems.map(item => {\n        return {\n          CancelledReasonId: '',\n          CancelReasonDescription: '',\n          ItemId: item.subOrderDetailsId\n        };\n      });\n    }\n    isFormValid() {\n      return this.formData.CancelOrderItem.every(item => item?.CancelledReasonId !== '');\n    }\n    onReasonSelected(event) {\n      const selectedReason = event.value;\n      if (selectedReason) {\n        this._GACustomEvents.orderCancelReasonSelectedEvent(this.order_id, selectedReason.id, selectedReason.reason);\n      }\n    }\n    ngOnDestroy() {\n      if (this.langChangeSubscription) {\n        this.langChangeSubscription.unsubscribe();\n      }\n    }\n    onOpenLogs(eachProduct) {\n      this.logsDialog = !this.logsDialog;\n      this.selectedProductLogs = eachProduct.itemHistoryLog;\n    }\n    onOpenAndCloseLogs() {\n      this.logsDialog = !this.logsDialog;\n    }\n    getClasses(orderStatusEnum) {\n      return {\n        'log-status': true,\n        'pending': orderStatusEnum === this.OrderStatusEnum.OrderPlaced,\n        'processing': orderStatusEnum === this.OrderStatusEnum.Processing,\n        'canceled': orderStatusEnum === this.OrderStatusEnum.Cancelled,\n        'delivered': orderStatusEnum === this.OrderStatusEnum.Delivered,\n        'return-requested': orderStatusEnum === this.OrderStatusEnum.RequestReturn,\n        'return-in-progress': orderStatusEnum === this.OrderStatusEnum.ReturnInProgress,\n        'returned': orderStatusEnum === this.OrderItemStatusEnum.Returned,\n        'return-rejected': orderStatusEnum === this.OrderStatusEnum.ReturnRejected\n      };\n    }\n    getLogClasses() {\n      return {\n        'current-p': true,\n        'rejected-p': this.selectedProductLogs?.currentStatus?.status === \"Cancelled\" || this.selectedProductLogs?.currentStatus?.status === \"Return Requested\" || this.selectedProductLogs?.currentStatus?.status === \"Return In Progress\" || this.selectedProductLogs?.currentStatus?.status === \"Returned\" || this.selectedProductLogs?.currentStatus?.status === \"Return Rejected\"\n      };\n    }\n    // Get calculated price from BE api for cancellation and return single orders items\n    getSingleItemPrice(subOrderId, subOrderDetailsId) {\n      const req = {\n        OrderId: parseInt(this.order_id),\n        SubOrderId: subOrderId,\n        SubOrderDetailsId: subOrderDetailsId\n      };\n      this.orderService.ReturnLastItemAmountWithShipmentFees(req).subscribe({\n        next: res => {\n          this.singleOrderTotal = res.data?.totalAmount;\n        }\n      });\n    }\n    static ɵfac = function IndexComponent_Factory(t) {\n      return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.TransactionService), i0.ɵɵdirectiveInject(OrderService), i0.ɵɵdirectiveInject(i3.StoreService), i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i3.ReviewsService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i3.AppDataService), i0.ɵɵdirectiveInject(i3.PermissionService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i7.BsModalService), i0.ɵɵdirectiveInject(CancelReason), i0.ɵɵdirectiveInject(ReturnReason), i0.ɵɵdirectiveInject(i8.GTMService), i0.ɵɵdirectiveInject(i9.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndexComponent,\n      selectors: [[\"app-index\"]],\n      decls: 35,\n      vars: 68,\n      consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [1, \"cancel-all-order\", \"rounded\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"onHide\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\", \"style\", \"z-index: 100\"], [1, \"logs-modal\", 3, \"visible\", \"breakpoints\", \"dismissableMask\", \"draggable\", \"modal\", \"resizable\", \"showHeader\", \"visibleChange\"], [1, \"logs-header\"], [\"src\", \"assets/images/logs-close.svg\", 1, \"logs-close-btn\", 3, \"click\"], [1, \"logs-section\"], [4, \"ngIf\"], [3, \"ngClass\"], [\"endImg\", \"\"], [1, \"logs-date\"], [1, \"logs-btn\", 3, \"click\"], [1, \"order-detail-mobile\"], [1, \"order-list-heading\"], [1, \"image-span\", 3, \"click\"], [\"src\", \"assets/icons/mobile-icons/back-icon.svg\", \"width\", \"21\", \"height\", \"21\", \"alt\", \"\", \"title\", \"\", 2, \"margin-right\", \"8px\"], [1, \"text-span\"], [1, \"orders\", \"orders-container\"], [1, \"order-info-header\"], [1, \"order-info-header__orderDetails\"], [1, \"order-info-header__orderDetails__orderNum\"], [1, \"title\"], [1, \"value\"], [1, \"order-info-header__orderDetails__orderDate\"], [1, \"order-info-header__orderStatus\"], [1, \"pi\", \"pi-circle-fill\", 3, \"ngStyle\"], [3, \"ngStyle\"], [1, \"myorderwrapper\", 2, \"padding\", \"10px\"], [1, \"yourOrder\", 2, \"width\", \"40%\"], [2, \"width\", \"60%\", \"display\", \"inline-flex\", \"justify-content\", \"end\"], [\"class\", \"btn return-order-btn\", \"style\", \"padding-right: 6px\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn return-order-btn p-0\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"order-info-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-details-box-container\", \"mt-2\"], [1, \"shipping-details\", \"p-4\", \"mb-3\", \"rounded\"], [\"class\", \"details-box d-flex justify-content-space-between\", 4, \"ngIf\"], [1, \"details-box\", \"d-flex\", \"justify-content-space-between\"], [1, \"text-right\"], [1, \"payment-details\", \"p-4\", \"rounded\"], [1, \"details-box\", \"total\", \"d-flex\", \"justify-content-space-between\"], [1, \"btn\", \"return-order-btn\", 2, \"padding-right\", \"6px\", 3, \"click\"], [\"alt\", \"X\", \"src\", \"assets/icons/X.svg\", 1, \"mr-1\"], [1, \"btn\", \"return-order-btn\", \"p-0\", 3, \"click\"], [\"alt\", \"Arrow Up Right\", \"src\", \"assets/icons/ArrowUpRight.svg\", 1, \"mr-1\"], [1, \"order-info-card\"], [1, \"card-content\"], [\"class\", \"product-image\", \"alt\", \"Product Image\", 3, \"src\", \"error\", 4, \"ngIf\"], [1, \"product-info\"], [1, \"product-title\"], [1, \"pricequantitywrapper\", 2, \"display\", \"inline-flex\"], [1, \"product-price\"], [1, \"product-qty\"], [1, \"log-refund-section\"], [1, \"log-section\"], [1, \"pi\", \"pi-circle-fill\"], [\"src\", \"assets/images/history.jpg\", 3, \"click\"], [\"class\", \"btn return-order-btn text-uppercase\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mt-3 mt-sm-0 float-item-end refund-successfully\", 4, \"ngIf\"], [\"alt\", \"Product Image\", 1, \"product-image\", 3, \"src\", \"error\"], [1, \"btn\", \"return-order-btn\", \"text-uppercase\", 3, \"click\"], [1, \"mt-3\", \"mt-sm-0\", \"float-item-end\", \"refund-successfully\"], [1, \"discount-price\"], [1, \"order-details-page\", \"bg-white\", 3, \"ngClass\"], [1, \"breadcrumb\", 3, \"ngClass\"], [3, \"home\", \"model\"], [1, \"row\", \"gx-3\", \"py-4\", \"px-5\", \"order-details-container\"], [1, \"col\", \"col-12\", \"col-md-8\", \"p-0\", \"mb-3\", \"mb-md-0\"], [1, \"border\", \"border-secondary-subtle\", \"rounded\", \"m-end-3\", \"del-m-end-xs\", \"order-details-data\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"py-3\", \"px-4\", \"m-0\", \"your-orders-title\"], [\"class\", \"py-3 px-4 d-flex flex-wrap justify-content-between flex-lg-nowrap border-secondary-subtle align-items-center order-details-header justify-content-sm-start\", 4, \"ngIf\"], [1, \"p-4\", \"single-order-details-data-container\"], [\"class\", \"single-order-details-data mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-details-box-container\", \"col\", \"col-12\", \"col-md-4\", \"p-0\"], [1, \"order-details-box\", \"p-4\", \"mb-3\", \"border\", \"border-secondary-subtle\", \"rounded\"], [1, \"order-details-box\", \"p-4\", \"border\", \"border-secondary-subtle\", \"rounded\"], [1, \"hr-order-style\", \"my-3\"], [1, \"py-3\", \"px-4\", \"d-flex\", \"flex-wrap\", \"justify-content-between\", \"flex-lg-nowrap\", \"border-secondary-subtle\", \"align-items-center\", \"order-details-header\", \"justify-content-sm-start\"], [1, \"m-end-2\", \"m-0\", \"text-uppercase\"], [1, \"m-end-2\", \"m-0\"], [1, \"status-badge\", \"px-2\", \"m-0\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", 3, \"ngStyle\"], [1, \"order-status\"], [\"class\", \"btn cancel-order-btn mt-3 mt-sm-0 float-item-end text-uppercase\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"cancel-order-btn\", \"mt-3\", \"mt-sm-0\", \"float-item-end\", \"text-uppercase\", 3, \"click\"], [1, \"single-order-details-data\", \"mb-3\"], [1, \"d-flex\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"single-order-details-data\"], [1, \"image\", \"m-end-2\", \"image-badge\", \"cursor-pointer\", 3, \"click\"], [1, \"p-element\", \"badge-mobile\"], [1, \"p-badge\", \"p-component\", \"p-badge-no-gutter\"], [\"alt\", \"Product Image\", 3, \"src\", \"error\", 4, \"ngIf\"], [1, \"single-order-title-container\", \"mr-2\"], [1, \"single-order-title\", \"m-0\", \"mb-1\", \"cursor-pointer\", 3, \"click\"], [1, \"single-order-sku\", \"m-0\"], [1, \"d-flex\", \"align-items-center\", \"m-end-3\", \"del-m-end-xs\", \"float-item-end\", \"del-float-end-xs\", \"mt-3\", \"mt-md-0\"], [\"class\", \"status-badge single-order-bade px-2 m-end-2\", 4, \"ngIf\"], [1, \"single-order-price\", \"text-uppercase\", \"mr-sm-6\"], [1, \"m-0\", \"price-border\"], [1, \"status-time\"], [1, \"d-flex\", \"mb-3\"], [\"class\", \"btn cancel-order-btn float-item-end text-uppercase\", 3, \"click\", 4, \"ngIf\"], [1, \"hr-order-style\"], [\"alt\", \"Product Image\", 3, \"src\", \"error\"], [1, \"status-badge\", \"single-order-bade\", \"px-2\", \"m-end-2\"], [\"class\", \"pi pi-circle-fill mr-1 order-placed\", 4, \"ngIf\"], [\"class\", \"pi pi-circle-fill mr-1 processing\", 4, \"ngIf\"], [\"class\", \"pi pi-circle-fill mr-1 completed\", 4, \"ngIf\"], [\"class\", \"pi pi-circle-fill mr-1 refund\", 4, \"ngIf\"], [\"class\", \"pi pi-circle-fill mr-1 canceled\", 4, \"ngIf\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", \"order-placed\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", \"processing\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", \"completed\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", \"refund\"], [1, \"pi\", \"pi-circle-fill\", \"mr-1\", \"canceled\"], [1, \"btn\", \"cancel-order-btn\", \"float-item-end\", \"text-uppercase\", 3, \"click\"], [1, \"cancel-order-heading\", \"cancel_order_details\"], [1, \"d-flex\", \"align-items-center\", \"single-order-details-data\"], [1, \"image\", \"m-end-2\"], [1, \"single-order-title-container\", \"text-start\"], [1, \"single-order-title\", \"m-0\", \"mb-1\"], [1, \"cancel-order-reason\", \"mt-3\"], [\"optionLabel\", \"reason\", \"scrollHeight\", \"180px\", 1, \"d-block\", \"mt-2\", \"border\", \"rounded\", \"p-2\", 3, \"ngModel\", \"options\", \"placeholder\", \"ngModelChange\", \"onChange\"], [\"cols\", \"30\", \"rows\", \"5\", 1, \"border\", \"rounded\", \"bg-white\", \"my-3\", \"p-3\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [1, \"d-flex\", \"justify-content-space-between\", \"mt-2\"], [\"class\", \"divider mt-3\", 4, \"ngIf\"], [1, \"divider\", \"mt-3\"], [\"class\", \" rounded-lg p-2 max-w-sm mx-auto text-center\", 4, \"ngIf\"], [\"class\", \"d-flex flex-column align-items-center\", 4, \"ngIf\"], [1, \"total-amount-container\", 2, \"border-top\", \"1px solid #ddd\", \"padding-top\", \"1rem\", \"margin-top\", \"1rem\"], [1, \"total-amount\", \"d-flex\", \"justify-content-space-between\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"w-100\", \"rounded\", \"mt-3\", \"text-uppercase\", 3, \"disabled\", \"label\", \"click\"], [1, \"rounded-lg\", \"p-2\", \"max-w-sm\", \"mx-auto\", \"text-center\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\", \"cancel-order-heading\"], [1, \"text-gray-600\", \"mb-6\", \"cancel-order-confirm-questions\"], [1, \"d-flex\", \"justify-content-center\", \"gap-5\"], [1, \"action-btn\", \"cancel-btn\", \"d-flex\", \"flex-column\", \"align-items-center\", 3, \"click\"], [1, \"icon-circle\", \"bg-danger\"], [\"alt\", \"Cancel Icon\", \"src\", \"assets/icons/quit-cancel.svg\"], [1, \"action-label\", \"mt-2\"], [1, \"action-btn\", \"proceed-btn\", \"d-flex\", \"flex-column\", \"align-items-center\", 3, \"click\"], [1, \"icon-circle\", \"bg-success\"], [\"alt\", \"Proceed Icon\", \"src\", \"assets/icons/proceed-cancel.svg\"], [1, \"d-flex\", \"flex-column\", \"align-items-center\"], [\"alt\", \"Logo\", \"height\", \"80\", \"ngSrc\", \"assets/icons/proceed-cancel.svg\", \"width\", \"80\"], [1, \"cancel-order-heading\", \"my-4\"], [1, \"cancel-order-confirm-questions\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"w-100\", \"rounded\", \"text-uppercase\", 3, \"label\", \"click\"], [1, \"cancel-order-heading\"], [\"width\", \"60\", \"height\", \"60\", \"alt\", \"Product Image\", 3, \"src\", \"error\", 4, \"ngIf\"], [1, \"quantity-input-container\"], [\"mode\", \"decimal\", \"inputId\", \"minmax-buttons\", 1, \"quantity-input\", \"mt-2\", 3, \"disabled\", \"ngModel\", \"showButtons\", \"min\", \"max\", \"ngModelChange\"], [\"optionLabel\", \"reason\", \"scrollHeight\", \"180px\", 1, \"d-block\", \"mt-2\", \"border\", \"rounded\", \"p-2\", 3, \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\", \"onChange\"], [\"width\", \"60\", \"height\", \"60\", \"alt\", \"Product Image\", 3, \"src\", \"error\"], [1, \"cancel-order-confirm-questions\", \"my-4\"], [1, \"cancel-proceed-btns\", \"d-flex\", \"flex-column\", \"align-items-center\", \"m-end-3\", 3, \"click\"], [\"alt\", \"Logo\", \"src\", \"assets/icons/quit-cancel.svg\", 1, \"mb-2\"], [1, \"cancel-proceed-btns\", \"d-flex\", \"flex-column\", \"align-items-center\", 3, \"click\"], [\"alt\", \"Logo\", \"src\", \"assets/icons/proceed-cancel.svg\", 1, \"mb-2\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"row\", \"address-step\"], [1, \"col-4\", \"text-center\"], [1, \"number-one\"], [\"alt\", \"number 1\", \"src\", \"assets/icons/num-1.svg\", 1, \"position-relative\"], [1, \"return-reason\"], [1, \"col\", \"position-relative\", \"address-step-hr\"], [1, \"col-4\", \"text-center\", \"position-relative\"], [\"alt\", \"number 2 gray\", \"src\", \"assets/icons/num-2-gray.svg\"], [1, \"return-reason\", 3, \"ngStyle\"], [\"alt\", \"Product Image\", \"width\", \"60\", \"height\", \"60\", 3, \"src\", \"error\", 4, \"ngIf\"], [\"optionLabel\", \"reason\", \"scrollHeight\", \"180px\", 1, \"d-block\", \"mt-2\", \"border\", \"rounded\", \"p-2\", 3, \"ngModel\", \"options\", \"required\", \"placeholder\", \"ngModelChange\"], [\"cols\", \"30\", \"rows\", \"3\", 1, \"border\", \"rounded\", \"bg-white\", \"my-3\", \"p-3\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [1, \"custom-file-input\"], [1, \"file-name\"], [1, \"upload-btn\", 3, \"click\"], [\"alt\", \"Arrow Up Right\", \"src\", \"assets/icons/upload-simple.svg\", 1, \"mr-1\"], [\"accept\", \".mp4, .png, .jpg\", \"type\", \"file\", 3, \"change\"], [\"fileInput\", \"\"], [\"alt\", \"Product Image\", \"width\", \"60\", \"height\", \"60\", 3, \"src\", \"error\"], [\"alt\", \"number 1\", \"src\", \"assets/icons/primary-right.svg\", 1, \"position-relative\"], [1, \"select-address\"], [\"alt\", \"number 2\", \"src\", \"assets/icons/num-2.svg\"], [1, \"d-flex\", \"align-items-start\"], [\"alt\", \"radio-icon\", \"src\", \"assets/icons/radio-icon.svg\"], [1, \"ml-2\"], [1, \"text-center\"], [1, \"d-flex\", \"justify-content-around\", \"px-4\"], [1, \"mt-3\"], [\"alt\", \"Logo\", \"height\", \"60\", \"ngSrc\", \"assets/icons/proceed-cancel.svg\", \"width\", \"60\"], [1, \"row\"], [1, \"col-6\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"return-outline\", \"rounded\", \"w-100\", 3, \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"w-100\", \"rounded\", 3, \"label\", \"click\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"rounded\", \"w-100\", 3, \"label\", \"click\"], [\"alt\", \"number 2 gray\", \"src\", \"assets/icons/num-2.svg\"], [\"alt\", \"number 2 gray\", \"src\", \"assets/icons/radio-icon.svg\"], [1, \"d-flex\", \"justify-content-around\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"cancel-order-btn\", \"rounded\", \"w-100\", 3, \"disabled\", \"label\", \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"log-icons\"], [\"src\", \"assets/images/success-log.svg\"], [\"src\", \"assets/images/line-green.svg\"], [1, \"prev-p\"], [1, \"logs-gif\"], [\"src\", \"assets/images/line-grey.svg\"], [3, \"src\"], [\"src\", \"assets/images/future.svg\", 1, \"future-icon\"], [1, \"future-p\"]],\n      template: function IndexComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, IndexComponent_ng_container_0_Template, 93, 79, \"ng-container\", 0);\n          i0.ɵɵtemplate(1, IndexComponent_ng_template_1_Template, 72, 69, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(3, \"p-dialog\", 2);\n          i0.ɵɵlistener(\"onHide\", function IndexComponent_Template_p_dialog_onHide_3_listener() {\n            return ctx.closeCancellingOrder();\n          })(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_3_listener($event) {\n            return ctx.cancelAllOrder = $event;\n          });\n          i0.ɵɵtemplate(4, IndexComponent_ng_template_4_Template, 1, 1, \"ng-template\", 3);\n          i0.ɵɵtemplate(5, IndexComponent_ng_template_5_Template, 4, 4, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p-dialog\", 2);\n          i0.ɵɵlistener(\"onHide\", function IndexComponent_Template_p_dialog_onHide_6_listener() {\n            return ctx.closeCancellingSingleOrder();\n          })(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_6_listener($event) {\n            return ctx.cancelSingleOrder = $event;\n          });\n          i0.ɵɵtemplate(7, IndexComponent_ng_template_7_Template, 3, 3, \"ng-template\", 3);\n          i0.ɵɵtemplate(8, IndexComponent_ng_template_8_Template, 2, 2, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p-dialog\", 2);\n          i0.ɵɵlistener(\"onHide\", function IndexComponent_Template_p_dialog_onHide_9_listener() {\n            return ctx.cancelReturnOrder();\n          })(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_9_listener($event) {\n            return ctx.requestReturnModel = $event;\n          });\n          i0.ɵɵtemplate(10, IndexComponent_ng_template_10_Template, 4, 4, \"ng-template\", 3);\n          i0.ɵɵtemplate(11, IndexComponent_ng_template_11_Template, 3, 3, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p-dialog\", 2);\n          i0.ɵɵlistener(\"onHide\", function IndexComponent_Template_p_dialog_onHide_12_listener() {\n            return ctx.closeReturnItemModel();\n          })(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_12_listener($event) {\n            return ctx.returnItemModel = $event;\n          });\n          i0.ɵɵtemplate(13, IndexComponent_ng_template_13_Template, 4, 4, \"ng-template\", 3);\n          i0.ɵɵtemplate(14, IndexComponent_ng_template_14_Template, 3, 3, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-dialog\", 5);\n          i0.ɵɵlistener(\"visibleChange\", function IndexComponent_Template_p_dialog_visibleChange_15_listener($event) {\n            return ctx.logsDialog = $event;\n          });\n          i0.ɵɵelementStart(16, \"h2\", 6);\n          i0.ɵɵtext(17, \" Item history \");\n          i0.ɵɵelementStart(18, \"img\", 7);\n          i0.ɵɵlistener(\"click\", function IndexComponent_Template_img_click_18_listener() {\n            return ctx.onOpenAndCloseLogs();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"section\", 8);\n          i0.ɵɵtemplate(20, IndexComponent_ng_container_20_Template, 2, 1, \"ng-container\", 9);\n          i0.ɵɵelementStart(21, \"div\")(22, \"section\", 10);\n          i0.ɵɵtemplate(23, IndexComponent_ng_container_23_Template, 4, 0, \"ng-container\", 0);\n          i0.ɵɵtemplate(24, IndexComponent_ng_template_24_Template, 1, 1, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"article\")(27, \"p\", 10);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\", 12);\n          i0.ɵɵtext(30);\n          i0.ɵɵpipe(31, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(32, IndexComponent_div_32_Template, 4, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function IndexComponent_Template_button_click_33_listener() {\n            return ctx.onOpenAndCloseLogs();\n          });\n          i0.ɵɵtext(34, \"Close\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(2);\n          const _r13 = i0.ɵɵreference(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobileLayout && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(58, _c5));\n          i0.ɵɵproperty(\"visible\", ctx.cancelAllOrder)(\"breakpoints\", i0.ɵɵpureFunction0(59, _c6))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(60, _c5));\n          i0.ɵɵproperty(\"visible\", ctx.cancelSingleOrder)(\"breakpoints\", i0.ɵɵpureFunction0(61, _c6))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(62, _c5));\n          i0.ɵɵproperty(\"visible\", ctx.requestReturnModel)(\"breakpoints\", i0.ɵɵpureFunction0(63, _c6))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(64, _c5));\n          i0.ɵɵproperty(\"visible\", ctx.returnItemModel)(\"breakpoints\", i0.ɵɵpureFunction0(65, _c6))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(66, _c5));\n          i0.ɵɵproperty(\"visible\", ctx.logsDialog)(\"breakpoints\", i0.ɵɵpureFunction0(67, _c7))(\"dismissableMask\", true)(\"draggable\", false)(\"modal\", true)(\"resizable\", false)(\"showHeader\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", (ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.previousStatus.length) > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", (ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.futureStatus) ? \"log-icons\" : \"future-icon\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.futureStatus)(\"ngIfElse\", _r13);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", ctx.getLogClasses());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.currentStatus == null ? null : ctx.selectedProductLogs.currentStatus.status, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(31, 55, ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.currentStatus == null ? null : ctx.selectedProductLogs.currentStatus.date, \"dd/MM/yyyy hh:mm a\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProductLogs == null ? null : ctx.selectedProductLogs.futureStatus);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i10.Dialog, i1.PrimeTemplate, i11.ButtonDirective, i12.Dropdown, i6.DefaultValueAccessor, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, i13.Breadcrumb, i5.NgOptimizedImage, i14.InputNumber, i5.DecimalPipe, i5.DatePipe, i4.TranslatePipe],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.order-detail-mobile[_ngcontent-%COMP%]{margin-top:72px;padding:8px 16px;flex-direction:column;background:#F6F6F6}.order-detail-mobile[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]{padding:8px 0 16px;display:flex}.order-detail-mobile[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]   .text-span[_ngcontent-%COMP%]{font-family:main-medium;font-size:16px;align-self:center}.order-detail-mobile[_ngcontent-%COMP%]   .order-list-heading[_ngcontent-%COMP%]   .image-span[_ngcontent-%COMP%]{display:flex}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header[_ngcontent-%COMP%]{padding:16px 8px;border-radius:8px;border-bottom:1px solid #E4E7E9;background:#FFF}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails[_ngcontent-%COMP%]{display:flex}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderNum[_ngcontent-%COMP%]{display:grid;width:50%;justify-content:start}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderNum[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderNum[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-regular;font-size:12px;font-weight:600;line-height:150%;text-transform:uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderDate[_ngcontent-%COMP%]{display:grid;width:50%;justify-content:flex-end;text-align:end}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderDate[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderDetails__orderDate[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-regular;font-size:12px;font-weight:600;line-height:150%;text-transform:uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderStatus[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:main-regular;line-height:normal;font-size:12px;gap:4px;padding:4px 0;font-weight:700}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-header__orderStatus[_ngcontent-%COMP%]   em[_ngcontent-%COMP%]{place-content:center;font-size:.5rem}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .myorderwrapper[_ngcontent-%COMP%]{padding:8px 0;margin-top:8px}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .myorderwrapper[_ngcontent-%COMP%]   .yourOrder[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:600;line-height:24px;text-transform:capitalize;width:50%;display:inline-block}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .myorderwrapper[_ngcontent-%COMP%]   .return-order-btn[_ngcontent-%COMP%]{color:var(--delete, #EE5858);font-family:main-medium;font-size:12px;font-style:normal;font-weight:500;letter-spacing:.144px;text-transform:uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]{padding:12px 8px;border-radius:8px;border-bottom:1px solid #E4E7E9;background:#FFF}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .status-wrapper[_ngcontent-%COMP%]   .order-text[_ngcontent-%COMP%]{padding:2px 6px;border-radius:4px;display:inline-flex;color:\\\"\\\";font-family:main-medium;font-size:14px;font-weight:500;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .status-wrapper[_ngcontent-%COMP%]   .return-order-btn[_ngcontent-%COMP%]{color:var(--delete, #EE5858);font-family:main-medium;font-size:12px;font-style:normal;font-weight:500;letter-spacing:.144px;text-transform:uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{display:flex;flex-direction:row;margin-top:8px}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:73px;height:73px;border-radius:8px;margin-right:16px}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-title[_ngcontent-%COMP%]{color:#204e6e;font-family:main-regular;font-size:16px;font-weight:500;line-height:20px;text-transform:capitalize;margin-bottom:6px;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]{display:inline-flex;justify-content:space-between;margin-right:5px;align-content:flex-end;align-items:baseline}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{color:#333;margin-bottom:4px;color:#204e6e;font-family:main-medium;font-size:16px;font-weight:600;line-height:20px;text-transform:Uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .product-qty[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:500;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .refund-successfully[_ngcontent-%COMP%]{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#01b467}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]{padding:4px 8px;display:flex;align-items:center;gap:4px;font-family:main-regular}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-family:main-regular;line-height:normal;font-size:12px;font-weight:700}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.pending[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.processing[_ngcontent-%COMP%]{color:#9e9e9e}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.canceled[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.returned[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.return-rejected[_ngcontent-%COMP%]{color:#ff5252}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.delivered[_ngcontent-%COMP%]{color:#01b467}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.return-requested[_ngcontent-%COMP%], .order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status.return-in-progress[_ngcontent-%COMP%]{color:#fb8c00}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-info-card[_ngcontent-%COMP%]   .log-refund-section[_ngcontent-%COMP%]   .log-section[_ngcontent-%COMP%]   .log-status[_ngcontent-%COMP%]   .pi.pi-circle-fill[_ngcontent-%COMP%]{font-size:.5rem;color:inherit}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .shipping-details[_ngcontent-%COMP%]{background:white}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .shipping-details[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:600;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .shipping-details[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:500;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .shipping-details[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:first-child{color:#204e6e}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]{background:white}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:600;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:16px;font-weight:500;line-height:24px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .orders-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:first-child{color:#204e6e}.order-detail-mobile[_ngcontent-%COMP%]     .zIndex .p-dialog-mask{z-index:10000!important}.order-detail-mobile[_ngcontent-%COMP%]     p-dialog .p-dialog-content{border-radius:8px!important;padding:0!important;overflow:auto!important}.order-detail-mobile[_ngcontent-%COMP%]   .myp-steps[_ngcontent-%COMP%]   .p-steps[_ngcontent-%COMP%]   .p-steps-item.p-highlight[_ngcontent-%COMP%]   .p-steps-title[_ngcontent-%COMP%]{color:#204e6e;font-family:main-medium;font-size:14px;font-weight:400;line-height:22px;text-transform:capitalize;border-color:#204e6e}.order-detail-mobile[_ngcontent-%COMP%]   .myp-steps[_ngcontent-%COMP%]   .p-steps[_ngcontent-%COMP%]   .p-steps-item[_ngcontent-%COMP%]   .p-menuitem-link[_ngcontent-%COMP%]   .p-steps-title[_ngcontent-%COMP%]{color:#a3a3a3;font-family:main-regular;font-size:13px;font-weight:400;line-height:22px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]     .myp-steps .p-steps .p-steps-item .p-menuitem-link .p-steps-number{font-size:16px!important;font-family:main-medium!important}.order-detail-mobile[_ngcontent-%COMP%]     .refund-mobile-p-dialog .p-dialog{width:332px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]{padding:24px 24px 18px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-top:10px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#204e6e;font-family:main-medium;font-size:18px;font-weight:700;line-height:normal;text-transform:capitalize;text-align:center;display:block;padding:8px 0}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{display:flex;flex-direction:row;margin-top:16px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:248px;height:100px;border-radius:8px;margin-right:16px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-title[_ngcontent-%COMP%]{color:#204e6e;font-family:main-regular;font-size:16px;font-weight:500;line-height:20px;text-transform:capitalize;margin-bottom:6px;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]{display:inline-flex;margin-right:5px;align-content:flex-end;align-items:baseline}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{color:#333;margin-bottom:4px;color:#191c1f;font-family:main-medium;font-size:12px;font-weight:400;line-height:20px;text-transform:Uppercase}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]{display:grid;margin-top:14px;margin-bottom:5px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:12px;font-weight:400;line-height:20px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]     p-dropdown .p-dropdown{height:44px;width:100%;border-radius:2px;border:1px solid var(--Gray-100, #E4E7E9);background:var(--Gray-00, #FFF)}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]     p-dropdown .p-dropdown .p-dropdown-label{color:#2d2d2d;font-family:main-regular;font-size:14px;font-weight:400;line-height:20px;text-transform:capitalize;align-self:center}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{border-radius:2px;border:1px solid var(--Gray-100, #E4E7E9);background:var(--Gray-00, #FFF);display:flex;padding:12px 10px 16px 16px;min-height:127px;align-items:center;align-self:stretch}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]{display:flex;align-items:center}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]   input[type=file][_ngcontent-%COMP%]{display:none}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-radius:2px;border:1px solid var(--custom-primary, #204E6E);padding:0 8px;justify-content:center;align-items:center;height:44px;background:white;color:#204e6e;font-family:main-medium;font-size:14px;font-weight:700;line-height:40px;text-transform:Uppercase;width:103px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]{display:flex;height:44px;padding:12px 35px 12px 16px;align-items:center;flex:1 0 0;border-radius:2px;border:1px solid var(--Gray-100, #E4E7E9);background:var(--Gray-00, #FFF);margin-right:5px;max-width:180px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;min-width:100%}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .custom-file-upload[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   i.pi-times[_ngcontent-%COMP%]{cursor:pointer;margin-left:.5em}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]{display:inline-flex;justify-content:space-between;margin-right:5px;align-content:flex-end;align-items:baseline}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{color:#333;margin-bottom:4px;color:#204e6e;font-family:main-regular;font-size:15px;font-weight:400;line-height:20px;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .product-qty[_ngcontent-%COMP%]{color:#2d2d2d;font-family:main-medium;font-size:14px;font-weight:500;line-height:24px;text-transform:capitalize;margin-bottom:0}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .pricequantitywrapper[_ngcontent-%COMP%]   .totalAmount[_ngcontent-%COMP%]{color:#000;font-family:main-medium;font-size:16px;font-weight:700;line-height:normal;text-transform:capitalize}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]{text-align:center;width:100%}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-wrapper[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#204e6e;font-family:main-medium;font-size:15px;font-weight:400;line-height:normal;text-transform:capitalize;margin-bottom:0}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-buttons[_ngcontent-%COMP%]{display:flex}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:50%}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-buttons[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{display:flex;height:40px;padding:12px 24px;border-radius:8px;border:2px solid var(--primary, #204E6E);justify-content:center;align-items:center;background-color:#fff;color:#204e6e;margin-right:16px;font-weight:700;text-transform:uppercase;font-size:14px;font-family:main-regular}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .form-buttons[_ngcontent-%COMP%]   .next[_ngcontent-%COMP%]{display:flex;height:40px;padding:12px 24px;border-radius:8px;justify-content:center;align-items:center;background-color:#204e6e;color:#fff;border:none!important;font-weight:700;text-transform:uppercase;font-size:14px;font-family:main-regular}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .steptwo[_ngcontent-%COMP%]{padding:10px}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .steptwo[_ngcontent-%COMP%]     .p-radioButton .p-radiobutton .p-radiobutton-box .p-radiobutton-icon{width:8px!important;height:8px!important}.order-detail-mobile[_ngcontent-%COMP%]   .refund-modal-content[_ngcontent-%COMP%]   .card-form[_ngcontent-%COMP%]   .steptwo[_ngcontent-%COMP%]     .p-radiobutton .p-radiobutton-box.p-highlight{border-color:#495057;background:#495057}  .myp-steps .p-steps .p-steps-item:first-child:before{width:calc(50% + 1rem);transform:translate(100%)}  .myp-steps .p-steps .p-steps-item:before{border-top-width:2px;margin-top:calc(-1rem + 1px)}  .myp-steps .p-steps .p-steps-item:last-child:before{width:50%}  .p-steps .p-steps-item.p-highlight .p-steps-title{font-weight:400!important}.order-details-page[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{min-height:24px;display:flex;align-items:center}.order-details-page[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   .order-status[_ngcontent-%COMP%]{font-size:12px;font-family:var(--regular-font);font-weight:500;color:#fff}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .m-end-3[_ngcontent-%COMP%]{margin-inline-end:1.5rem!important}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .m-end-2[_ngcontent-%COMP%]{margin-inline-end:1rem!important}@media screen and (max-width: 767px){.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .del-m-end-xs[_ngcontent-%COMP%]{margin-inline-end:unset!important}}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .float-item-end[_ngcontent-%COMP%]{margin-inline-start:auto}@media screen and (max-width: 767px){.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .del-float-end-xs[_ngcontent-%COMP%]{margin-inline-start:unset!important}}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .hr-order-style[_ngcontent-%COMP%]{background-color:var(--stroke-color, #E4E7E9);height:1px;margin:unset}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .your-orders-title[_ngcontent-%COMP%]{font-size:18px;font-family:var(--medium-font);font-weight:500;color:#2d2d2d}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .order-details-header[_ngcontent-%COMP%]{background-color:var(--stroke-color, #E4E7E9)}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .order-details-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;font-family:var(--medium-font);font-weight:500;color:#2d2d2d}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .cancel-order-btn[_ngcontent-%COMP%]{font-size:14px;font-family:var(--medium-font);font-weight:700;color:#ee5858}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .refund-successfully[_ngcontent-%COMP%]{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#01b467}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .single-order-details-data-container[_ngcontent-%COMP%]   .single-order-details-data[_ngcontent-%COMP%]   .single-order-title-container[_ngcontent-%COMP%]{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#191c1f}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .single-order-details-data-container[_ngcontent-%COMP%]   .single-order-details-data[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-height:100px;max-width:100px;border-radius:50px}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .single-order-details-data-container[_ngcontent-%COMP%]   .single-order-details-data[_ngcontent-%COMP%]   .single-order-price[_ngcontent-%COMP%]{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#2d2d2d}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-data[_ngcontent-%COMP%]   .single-order-details-data-container[_ngcontent-%COMP%]   .single-order-details-data[_ngcontent-%COMP%]   .single-order-price[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:unset}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .order-details-box[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:20px;font-size:18px;font-family:var(--medium-font);font-weight:500;color:#191c1f}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .order-details-box[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:first-child{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#204e6e}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .order-details-box[_ngcontent-%COMP%]   .details-box[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{font-size:14px;font-family:var(--medium-font);font-weight:500;color:#2d2d2d}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .order-details-box[_ngcontent-%COMP%]   .details-box.total[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:first-child{font-size:16px;font-family:var(--medium-font);font-weight:500;color:#204e6e}.order-details-page[_ngcontent-%COMP%]   .order-details-container[_ngcontent-%COMP%]   .order-details-box-container[_ngcontent-%COMP%]   .order-details-box[_ngcontent-%COMP%]   .details-box.total[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{font-size:16px;font-family:var(--medium-font);font-weight:700;color:#2d2d2d}.order-details-page[_ngcontent-%COMP%]     .m-end-2{margin-inline-end:1rem!important}.order-details-page[_ngcontent-%COMP%]     .m-end-3{margin-inline-end:1.5rem!important}.item-details[_ngcontent-%COMP%]   .first-txt[_ngcontent-%COMP%]{position:absolute;top:0;left:20px;background:black;color:#fff;border-radius:66%;width:20px;height:20px;font-size:10px;text-align:center;font-family:main-regular!important}.product-name[_ngcontent-%COMP%]{font-size:15px;font-weight:500;font-family:main-medium!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:225px}.order-deatil-currency[_ngcontent-%COMP%]{font-size:11.34px;font-weight:700;font-family:main-medium!important}.order-deatil-price[_ngcontent-%COMP%]{font-size:17.82px;font-weight:700;font-family:main-medium!important}.order-list[_ngcontent-%COMP%]{font-size:15px;font-weight:500;font-family:main-medium!important}.order-value[_ngcontent-%COMP%]{font-size:15px;font-weight:400;font-family:main-regular!important;color:#a3a3a3}.second-btn[_ngcontent-%COMP%]{width:330px;height:45px;font-size:15px;font-weight:500;font-family:main-medium!important}  .p-dialog-content{border-bottom:none!important;border-radius:0!important}@media screen and (max-width: 320px){.order-details-page[_ngcontent-%COMP%]{margin-top:140px}}  p-dropdown .pi{color:#6c757d!important}.eye-img[_ngcontent-%COMP%]{height:20px!important}.refresh-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:-20px}@media screen and (max-width: 768px){.order-number[_ngcontent-%COMP%]{text-align:left}.width-26[_ngcontent-%COMP%]{width:70%}.mobile-none[_ngcontent-%COMP%]{display:none!important}.price.desktop-none[_ngcontent-%COMP%]{top:0!important}.mt-5[_ngcontent-%COMP%]{margin-top:0!important}  p-breadcrumb.p-element{margin-bottom:15px!important}.item-details[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{margin-right:15px!important}.second-btn[_ngcontent-%COMP%]{text-transform:uppercase}.mobile-text[_ngcontent-%COMP%]{font-weight:400!important;font-size:15px!important;font-family:main-regular!important;color:#a3a3a3}.item-details[_ngcontent-%COMP%]{width:100%}}@media only screen and (max-width: 768px) and (max-width: 767px){.order-details-page[_ngcontent-%COMP%]{margin-top:200px}}@media screen and (max-width: 768px){p.text-left.itemreceived-text.ng-star-inserted[_ngcontent-%COMP%]{font-family:main-regular!important;font-size:15px}.btn-width[_ngcontent-%COMP%]{width:100%!important;height:52px!important;justify-content:center!important;align-items:center!important;padding:0 16px!important;border-radius:52px!important}.cancel-order-heading[_ngcontent-%COMP%]{font-family:main-medium!important}  .p-dropdown .p-dropdown-label.p-placeholder{font-family:main-regular!important}  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:not(.p-highlight):not(.p-disabled):hover{font-family:main-regular!important}}.badge-mobile[_ngcontent-%COMP%]{position:absolute;right:0}.image-badge[_ngcontent-%COMP%]{position:relative}.p-badge[_ngcontent-%COMP%]{background:#ffcc00!important;color:#000!important}.return-order-btn[_ngcontent-%COMP%]{color:var(--delete, #EE5858);font-family:main-medium;font-size:12px;font-style:normal;font-weight:500;letter-spacing:.144px;text-transform:uppercase}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content{padding:24px 16px}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-header{margin-bottom:18px;font-family:main-regular;font-size:14px;font-weight:700;line-height:normal;color:#000;position:relative}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-header .logs-close-btn{position:absolute;top:-11px;right:-5px}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section{padding-top:16px;display:flex;flex-direction:column;gap:4px}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div{display:flex;gap:10px;font-size:14px;font-family:main-regular;font-weight:500;line-height:15px;min-height:50px}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .log-icons{display:flex;flex-direction:column;gap:4px;align-items:center;justify-content:center}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .log-icons .logs-gif{display:flex;align-items:center;justify-content:center;width:20px;height:20px;border-radius:100%;overflow:hidden}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .log-icons .logs-gif span{width:8px;height:8px;border-radius:100%;background-color:#000;animation:_ngcontent-%COMP%_pulse .7s linear alternate infinite}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #2c2a2a}50%{box-shadow:0 0 0 3px #4b4747}to{box-shadow:0 0 0 7px #464343}}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .future-icon{align-self:start}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .prev-p, [_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .current-p, [_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .future-p{font-family:main-regular;font-size:14px;font-weight:500;line-height:15px;margin:0!important}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .prev-p{color:#0d0b26}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .current-p{color:#01b467}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .rejected-p{color:#ee5858}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .future-p{color:#c5c6cc}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-section div .logs-date{font-size:12px;font-weight:400;line-height:20px;color:#a0a0a0;margin:0!important}[_nghost-%COMP%]     .logs-modal .p-dialog .p-dialog-content .logs-btn{margin-top:8px;padding:12px 24px;width:100%;border:2px solid #204E6E;border-radius:6px;font-family:main-regular;color:#204e6e;font-weight:700;font-size:14px;text-transform:capitalize;background-color:#fff}[_nghost-%COMP%]     .cancel-all-order, [_nghost-%COMP%]     .confirm-cancel-all-order{position:relative}[_nghost-%COMP%]     .cancel-all-order .m-end-3, [_nghost-%COMP%]     .confirm-cancel-all-order .m-end-3{margin-inline-end:1.5rem!important}[_nghost-%COMP%]     .cancel-all-order .m-end-2, [_nghost-%COMP%]     .confirm-cancel-all-order .m-end-2{margin-inline-end:1rem!important}[_nghost-%COMP%]     .cancel-all-order .p-dialog-content, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-content{border:unset;border-radius:4px 4px 0 0;padding-top:24px}[_nghost-%COMP%]     .cancel-all-order .cancel-order-heading, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-heading{font-size:18px;font-family:var(--medium-font);font-weight:700;color:#204e6e;text-align:center}[_nghost-%COMP%]     .cancel-all-order .address-step img, [_nghost-%COMP%]     .confirm-cancel-all-order .address-step img{background-color:#fff;z-index:5}[_nghost-%COMP%]     .cancel-all-order .address-step .address-step-hr hr, [_nghost-%COMP%]     .confirm-cancel-all-order .address-step .address-step-hr hr{width:200%;position:absolute;left:-50%;top:6%;background-color:#a3a3a3;color:#a3a3a3;opacity:1}[_nghost-%COMP%]     .cancel-all-order .address-step .address-step-hr hr.select-address, [_nghost-%COMP%]     .confirm-cancel-all-order .address-step .address-step-hr hr.select-address{background-color:#204e6e}[_nghost-%COMP%]     .cancel-all-order .address-step .return-reason, [_nghost-%COMP%]     .confirm-cancel-all-order .address-step .return-reason{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#204e6e}[_nghost-%COMP%]     .cancel-all-order .single-order-details-data .single-order-title-container, [_nghost-%COMP%]     .confirm-cancel-all-order .single-order-details-data .single-order-title-container{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#191c1f}[_nghost-%COMP%]     .cancel-all-order .single-order-details-data .image img, [_nghost-%COMP%]     .confirm-cancel-all-order .single-order-details-data .image img{max-height:100px;max-width:100px}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason{text-align:start}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason span, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason span{font-size:12px;font-family:var(--medium-font);font-weight:400;color:#2d2d2d}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason p-dropdown, [_nghost-%COMP%]     .cancel-all-order .cancel-order-reason textarea, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason p-dropdown, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason textarea{border-color:#e4e7e9!important}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason p-dropdown .p-dropdown, [_nghost-%COMP%]     .cancel-all-order .cancel-order-reason textarea .p-dropdown, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason p-dropdown .p-dropdown, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason textarea .p-dropdown{border:unset!important}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason textarea, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason textarea{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#77878f;min-width:100%;max-width:100%;resize:none}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason .custom-file-input, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason .custom-file-input{position:relative;overflow:hidden;display:flex}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason .custom-file-input input, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason .custom-file-input input{position:absolute;top:0;right:0;margin:0;padding:0;font-size:20px;cursor:pointer;opacity:0;filter:alpha(opacity=0);visibility:hidden}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason .custom-file-input .file-name, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason .custom-file-input .file-name{margin-inline-end:10px;font-size:16px;color:#333;width:100%;border-radius:2px;border:1px solid var(--Gray-100, #E4E7E9);background:var(--Gray-00, #FFF);padding:12px 8px;display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;-webkit-line-clamp:2;line-height:1.5;max-height:50px}[_nghost-%COMP%]     .cancel-all-order .cancel-order-reason .custom-file-input .upload-btn, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-reason .custom-file-input .upload-btn{text-transform:uppercase;font-size:14px;font-family:var(--medium-font);font-weight:700;color:#204e6e;border:1px solid #204E6E;min-width:120px;padding:12px 8px;background-color:#fff;border-radius:2px}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer{padding:16px}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer .total-amount p:first-child, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer .total-amount p:first-child{font-size:16px;font-family:var(--medium-font);font-weight:500;color:#204e6e}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer .total-amount p:last-child, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer .total-amount p:last-child{font-size:16px;font-family:var(--medium-font);font-weight:700;color:#2d2d2d}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer .cancel-order-btn, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer .cancel-order-btn{margin:unset;background-color:#204e6e;min-height:56px}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer .cancel-order-btn:disabled, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer .cancel-order-btn:disabled{background:#f5f5f5;color:#9c9b9b;cursor:not-allowed;pointer-events:unset;border:unset}[_nghost-%COMP%]     .cancel-all-order .p-dialog-footer .cancel-order-btn.return-outline, [_nghost-%COMP%]     .confirm-cancel-all-order .p-dialog-footer .cancel-order-btn.return-outline{background-color:#fff;color:#204e6e;border:#204E6E solid 2px}[_nghost-%COMP%]     .cancel-all-order .cancel-order-confirm-questions, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-order-confirm-questions{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#000}[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-proceed-btns{background-color:unset;border:unset!important}[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns span, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-proceed-btns span{font-size:14px;font-family:var(--medium-font);font-weight:400;color:#4a4a4a}[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns:disabled, [_nghost-%COMP%]     .confirm-cancel-all-order .cancel-proceed-btns:disabled{background:#f5f5f5;color:#9c9b9b;cursor:not-allowed}[_nghost-%COMP%]     .cancel-all-order .quantity-input-container, [_nghost-%COMP%]     .confirm-cancel-all-order .quantity-input-container{text-align:start}[_nghost-%COMP%]     .cancel-all-order .quantity-input-container span, [_nghost-%COMP%]     .confirm-cancel-all-order .quantity-input-container span{font-size:12px;font-family:var(--medium-font);font-weight:400;color:#2d2d2d}[_nghost-%COMP%]     .cancel-all-order .quantity-input-container .quantity-input, [_nghost-%COMP%]     .confirm-cancel-all-order .quantity-input-container .quantity-input{width:100%}[_nghost-%COMP%]     .cancel-all-order .quantity-input-container .quantity-input .p-inputnumber-buttons-stacked, [_nghost-%COMP%]     .confirm-cancel-all-order .quantity-input-container .quantity-input .p-inputnumber-buttons-stacked{width:100%}[_nghost-%COMP%]     .cancel-all-order .quantity-input-container .quantity-input .p-inputnumber-button-group .p-button-icon-only, [_nghost-%COMP%]     .confirm-cancel-all-order .quantity-input-container .quantity-input .p-inputnumber-button-group .p-button-icon-only{background:unset;border:unset;color:#000}[_nghost-%COMP%]     .cancel-all-order .p-dialog-mask{top:110px;height:calc(100% - 110px)}@media screen and (max-width: 1200px){[_nghost-%COMP%]     .cancel-all-order .p-dialog-mask{top:75px;height:calc(100% - 75px)}}@media screen and (max-width: 1200px){[_nghost-%COMP%]     .cancel-all-order .single-order-details-data .image img{max-width:60;max-height:60}}[_nghost-%COMP%]     .cancel-all-order .p-dialog-content{padding:1.5rem;border-bottom:none!important;border-radius:inherit!important;border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns{background-color:unset;border:unset!important}[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns img{width:80px;height:80px}@media screen and (max-width: 1200px){[_nghost-%COMP%]     .cancel-all-order .cancel-proceed-btns img{width:60px;height:60px}}.status-time[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;margin:0 0 13px auto;font-family:main-regular;font-size:12px;font-weight:400;line-height:20px;color:#2d2d2d}.confirm-cancel-order[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;max-width:300px;margin:auto;box-shadow:0 4px 12px #0000001a}.cancel-order-heading[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:700;color:#2f4f6f}.cancel-order-confirm-questions[_ngcontent-%COMP%]{font-size:1rem;color:#6c757d}.d-flex.gap-5[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{margin-right:1.5rem}.action-btn[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;color:#495057;font-size:.875rem;text-align:center}.icon-circle[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:60px;height:60px;border-radius:50%}.bg-danger[_ngcontent-%COMP%]{background-color:#e74c3c}.bg-success[_ngcontent-%COMP%]{background-color:#2ecc71}.icon-circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:24px;height:24px}.action-label[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:#495057}.cancel_order_details[_ngcontent-%COMP%]{border-bottom:1px solid #eae7e7;padding-bottom:1rem;margin-top:1rem}\"]\n    });\n  }\n  return IndexComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}