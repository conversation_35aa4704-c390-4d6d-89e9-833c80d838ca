import {ChangeDetectorRef, Component, ElementRef, Inject, <PERSON><PERSON><PERSON>, PLATFORM_ID, ViewChild} from '@angular/core';
import {NgxGpAutocompleteDirective} from "@angular-magic/ngx-gp-autocomplete";
import {UntypedFormControl, UntypedFormGroup, Validators} from "@angular/forms";
import {CountryISO} from "ngx-intl-tel-input-gg";
import {AddressService, AppDataService, LoaderService, MainDataService, StoreService} from "@core/services";
import {MessageService} from "primeng/api";
import {ActivatedRoute, Router} from "@angular/router";
import {TranslateService} from "@ngx-translate/core";
import {isPlatformBrowser, Location} from "@angular/common";
import {TenantRecords} from "@core/interface";

@Component({
  selector: 'app-mobile-address-map',
  templateUrl: './mobile-address-map.component.html',
  styleUrls: ['./mobile-address-map.component.scss']
})
export class MobileAddressMapComponent {
  routeToCheckOut:any;
  markerOptions: any = { draggable: true,icon:'assets/images/map-pin.svg' };
  latitude?: number;
  longitude?: number;
  phoneLength: number = 13;
  position: any
  landMarkAddressRequired: boolean = false;
  isDisplaySuccessModal: boolean = false;
  message: string ='';
  addressDetailCity : string = '';
  @ViewChild("placesRef") placesRef: NgxGpAutocompleteDirective;

  addressForm = new UntypedFormGroup({
    addressLabel: new UntypedFormControl('Home'),
    receiverFirstName: new UntypedFormControl(''),
    receiverLastName: new UntypedFormControl(''),
    streetAddress: new UntypedFormControl('', Validators.required),
    country: new UntypedFormControl('', Validators.required),
    city: new UntypedFormControl('', Validators.required),
    state: new UntypedFormControl(''),
    landMark: new UntypedFormControl('', Validators.required ),
    deliveryInstructions: new UntypedFormControl(''),
    buldingNumber: new UntypedFormControl(''),
    postcode: new UntypedFormControl(''),
    receiverPhoneNumber: new UntypedFormControl('', Validators.required),
    geo_location: new UntypedFormControl(''),
    Lat: new UntypedFormControl(''),
    Lng: new UntypedFormControl(''),
    Id: new UntypedFormControl(''),
    additionalAddress:new UntypedFormControl(''),
    region: new UntypedFormGroup({
      regionId: new UntypedFormControl('', Validators.required),
      regionName: new UntypedFormControl('', Validators.required)
    })
  });
  search: string = '';
  Lat!: number;
  Lng!: number;
  zoom!: number;
  address!: string;
  myplaceHolder: string | undefined;
  source: any;
  isDefault = false;
  navbarData:any;
  center: google.maps.LatLngLiteral = {
    lat: 0.3,
    lng: 32.5
  };
  mapOptions = {
    fullscreenControl: false,
    disableDefaultUI: true,
    componentRestrictions: { country: 'UG' }
  };
  allCities : any = [];
  isDifferentCity: boolean = false;
  public addressLabelList = [
    {'name': 'Home', 'id': 1},{'name': 'Work', 'id': 2},{'name': 'Other', 'id': 3}

  ]
  selectedAddressType: any;
  @ViewChild('search')
  public searchElementRef!: ElementRef;
  id: string = '';
  routeSub: any;
  private geoCoder: any = new google.maps.Geocoder();
  private redirectUrl: string;
  map: google.maps.Map;
  borderBottomStyle = '2px solid red !important';
  phoneInputLength: number = 12;
  preferredCountries: CountryISO[] = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];
  CustomCountryISO: any;
  customPlaceHolder: any = '';
  allRegionList : any = [];
  filteredCities: any[] = [];
  // Country coordinates mapping
  private countryCoordinates: { [key: string]: { lat: number; lng: number; country: string } } = {
    'UG': { lat: 0.3136, lng: 32.5811, country: 'UG' }, // Uganda
    'GH': { lat: 5.595465, lng: -0.242603, country: 'GH' }, // Ghana
    'CI': { lat: 7.5399, lng: -5.5471, country: 'CI' }  // Côte d'Ivoire
  };
  constructor(
    private ngZone: NgZone,
    public addressService: AddressService,
    private messageService: MessageService,
    private router: Router,
    private store: StoreService,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private mainDataService: MainDataService,
    private loaderService: LoaderService,
    private _location: Location,
    private cd : ChangeDetectorRef,
    private appDataService: AppDataService,
    @Inject(PLATFORM_ID) private platformId: any,
  ) {
    this.source = this.router.getCurrentNavigation()?.extras?.state;

    let tenantId = localStorage.getItem('tenantId');
    if(tenantId && tenantId !== '') {
      if(tenantId == '1') {
        this.customPlaceHolder = 'XXXXXXXXX';
      } else if(tenantId == '2') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '3') {
        this.customPlaceHolder = 'XXXXXXXXX';
      }else if(tenantId == '4') {
        this.customPlaceHolder = 'XXXXXXXXXX';
      }
    }
     // Set initial map center based on isoCode
    this.setMapCenterFromIsoCode();
  }

 /**
   * Set map center and component restrictions based on isoCode from localStorage
   */
  private setMapCenterFromIsoCode(): void {
    const isoCode = localStorage.getItem('isoCode');
    
    if (isoCode && this.countryCoordinates[isoCode]) {
      const countryData = this.countryCoordinates[isoCode];
      this.center = {
        lat: countryData.lat,
        lng: countryData.lng
      };
      
      // Update map options with the correct country restriction
      this.mapOptions = {
        ...this.mapOptions,
        componentRestrictions: { country: countryData.country }
      };
    } else {
      // Default to Uganda if isoCode is not found or not supported
      const defaultCountry = this.countryCoordinates['UG'];
      this.center = {
        lat: defaultCountry.lat,
        lng: defaultCountry.lng
      };
      
      this.mapOptions = {
        ...this.mapOptions,
        componentRestrictions: { country: defaultCountry.country }
      };
    }
  }
  onBack(){
    this.routeToCheckOut ? this.router.navigateByUrl("/checkout/selectAddress") : this.router.navigateByUrl("/account/address");
  }

  ngOnInit() {
    this.route.queryParamMap.subscribe((queryParams) => {
      this.routeToCheckOut = queryParams.get("checkout");
    });

    this.navbarData = this.appDataService.layoutTemplate.find((section: any) => section.type === 'navbar');
    this.route.queryParams.subscribe((params: any) => {
      this.redirectUrl = params.returnUrl;
    });

    this.routeSub = this.route.params.subscribe((params) => {


        this.getCities();
        this.setCurrentLocation();
        this.getAllRegion();
    });

    let phoneLength = localStorage.getItem('PhoneLength')?.toString();
    let landMarkAddress = localStorage
      .getItem('customerAddressLandmarkRequired')
      ?.toString();
    if (landMarkAddress && landMarkAddress == 'True') {
      this.landMarkAddressRequired = true;
      this.addressForm.controls['landMark'].setValidators([
        Validators.required,
      ]);
      this.addressForm.controls['landMark'].updateValueAndValidity();
    }
    if (phoneLength) {
      this.phoneLength = parseInt(phoneLength) - 2;
    }
    let userDetails = this.store.get('profile');
    this.mainDataService.setUserData(userDetails);
    let name = userDetails?.name?.split(' ');
    this.addressForm.patchValue({
      receiverFirstName: name[0] ? name[0] : '',
      receiverLastName: name[1] ? name[1] : '',
      // receiverPhoneNumber: userDetails.mobileNumber,
    });


    if (!localStorage.getItem("isoCode")) {

      const tenants = this.appDataService.tenants
      if (tenants.records != undefined) {
        let tenantId = localStorage.getItem('tenantId');
        let data = tenants.records;
        let arr: TenantRecords = data.find((element: any) => element.tenantId == tenantId) ?? new TenantRecords();
        localStorage.setItem('isoCode', arr?.isoCode??"UG");
        this.store.set('allCountryTenants', tenants.records);
      }

    } else {
      this.CustomCountryISO = localStorage.getItem("isoCode");
    }
    if(this.appDataService.configuration) {
      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength')
      if(phoneLength) this.phoneInputLength = parseInt(phoneLength.value)
    }
    this.filteredCities = this.allCities;
  }
  mapInitialize(map: any) {
    this.map = map;
  }
  public handleAddressChange(place: any) {
    this.Lat = place.geometry.location.lat();
    this.Lng = place.geometry.location.lng();
    this.position = [{
      position: {lat: this.Lat, lng: this.Lng}
    }]
    this.zoom = 12;
    this.center = this.position[0].position
    this.getAddress(this.Lat, this.Lng)
  }

  ngOnDestroy() {
    this.routeSub.unsubscribe();
  }

  getAddress(latitude: number, longitude: number) {

    this.geoCoder.geocode(
      {location: {lat: latitude, lng: longitude}},
      (results: { formatted_address: string, address_components: any }[], status: string) => {
        if (status === 'OK') {
          if (results[0]) {
            const countryComponent = results[0].address_components.find((component: { types: string | string[]; }) =>
                        component.types.includes('country')
                      );
            console.log(countryComponent);
            const isoCode = localStorage.getItem('isoCode');
            if (countryComponent && countryComponent.short_name === isoCode) {    
                this.position = [{
                  position: {lat: latitude, lng: longitude}
                }]
                this.center = this.position[0].position;
                this.zoom = 12;
                this.address = results[0].formatted_address;
                if(results[0]?.address_components.length){
                  const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))
                  this.addressDetailCity = city.long_name;

                }
                this.addressForm.patchValue({
                  streetAddress: this.address,
                  country: results[results.length - 1].formatted_address,
                  // city: results[results.length - 3].formatted_address,
                });
                this.validate();
                this.getCoordinates();
                this.cd.detectChanges();
              }else{
                this.setMapCenterFromIsoCode()
                this.position = [{}]
                this.cd.detectChanges();
              }
          } else {
            if (isPlatformBrowser(this.platformId)) {
              window.alert('No results found');
            }
          }
        } else {
          if (isPlatformBrowser(this.platformId)) {
            window.alert('Geocoder failed due to: ' + status);

          }
        }
      }
    );
  }

  clear(): void {
    this.searchElementRef.nativeElement.value = '';

  }

  onSubmit() {

    this.addressForm.patchValue({
      Lat: this.Lat ? this.Lat.toString() : '',
      Lng: this.Lng ? this.Lng.toString() : '',
      // receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),
    });


    this.loaderService.show();
    if (this.addressForm.value.postcode == '')
      this.addressForm.value.postcode = 0;

    if (this.addressForm.valid) {
      const formValue = {
        ...this.addressForm.value,
        region: this.addressForm.value.region.regionName, // Send regionName only
        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1),
      };
      if (formValue.postcode === "" || formValue.postcode === null) delete formValue.postcode;
      this.addressService.addAddress(formValue).subscribe({
        next: (res: any) => {
          if(res?.success){
          this.loaderService.hide();
          this.isDisplaySuccessModal = true;
          this.message = this.translate.instant('ResponseMessages.addressAddedSuccessfully')
          } else {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: res?.message,
            });
          }
        },
        error: (err: any) => {

          this.loaderService.hide();
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('ErrorMessages.fetchError'),
            detail: err,
          });
        },
      });
    }
  }
  onConfrim(){
    this.isDisplaySuccessModal = false;
    if(this.redirectUrl && this.redirectUrl !== '') {
      this.router.navigate([this.redirectUrl])
    } else {
      this._location.back();
    }
  }
  Update() {
    if (this.Lat.toString() === "" || this.Lng.toString() === "" || this.addressForm.controls['streetAddress'].value === "") {
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('ErrorMessages.addressIsRequired'),
      });
      return;
    }

    this.addressForm.patchValue({
      Lat: this.Lat.toString(),
      Lng: this.Lng.toString(),
      Id: this.addressService.chosenAddress.id,
    });

    this.loaderService.show();

    if (this.addressForm.valid) {
      const formValue = {
        ...this.addressForm.value,
        region: this.addressForm.value.region.regionName,
        receiverPhoneNumber: this.addressForm.controls.receiverPhoneNumber.value.e164Number.slice(1)
      };
      if (formValue.postcode === "" || formValue.postcode === null) delete formValue.postcode;
      this.addressService.updateAddress(formValue).subscribe({
        next: (res: any) => {
          this.loaderService.hide();
          if (!res.success) {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('ResponseMessages.address'),
              detail: this.translate.instant(res.message),
            });
          } else {
            this.isDisplaySuccessModal = true;
            this.message = this.translate.instant('ResponseMessages.addressUpdatedSuccessfully');
          }
        },
        error: (err: any) => {
          this.loaderService.hide();
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('ErrorMessages.fetchError'),
            detail: err,
          });
        },
      });
    }
  }


  OnlyNumeric(val: any): boolean {
    if (!Number(val.value)) {
      this.addressForm.value.postcode = '';
      this.addressForm.value.phone = '';
    }
    return false;
  }

  checkPlaceHolder() {
    if (this.myplaceHolder) {
      this.myplaceHolder = '';
    } else {
      this.myplaceHolder = localStorage.getItem('countryPhone')?.toString();
      if (this.myplaceHolder)
        this.myplaceHolder = this.myplaceHolder + ' 000 000 000';
      else this.myplaceHolder = '256 000 000 000';
    }
  }

  validate() {
    if (!this.addressForm.valid) return true;
  }

  setAsDefault() {
    this.addressService.setDefault(this.id).subscribe({
      next: (res: any) => {
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('ResponseMessages.address'),
          detail: this.translate.instant(
            'ResponseMessages.defaultAddressSuccessfully'
          ),
        });
        if(this.redirectUrl && this.redirectUrl !== '') {
          this.router.navigate([this.redirectUrl])
        } else {
          this.router.navigate(['/account/address']);
        }
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('ErrorMessages.fetchError'),
          detail: err,
        });
      },
    });
  }


  private setCurrentLocation() {
    this.addressService.chosenAddress = null;
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition((position) => {
        this.Lat = position.coords.latitude;
        this.Lng = position.coords.longitude;
        this.position = [{
          position: {lat: this.Lat, lng: this.Lng}
        }]
        this.center = this.position[0].position
        this.zoom = 12;

        this.getAddress(this.Lat, this.Lng);
        this.createLocationButton();
      });
    }

  }
  mapClicked(event : any){
    let latLng = JSON.parse(JSON.stringify(event.latLng));
    this.Lat = latLng.lat;
    this.Lng =latLng.lng
    this.position = [{
      position: {lat: this.Lat, lng: this.Lng}
    }]
    this.center = this.position[0].position
    this.zoom = 12;
    this.getAddress(this.Lat, this.Lng);
  }
  createLocationButton() {
    if (isPlatformBrowser(this.platformId)) {
      const controlDiv : any = document.createElement('div');
      controlDiv.index = 100;
      this.map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
    }
  }
  markerDragEnd(event: any) {
    if (event.latLng != null) {
      const latLng = event.latLng.toJSON();
      ;
      this.getAddress(latLng.lat, latLng.lng);
    }
  }
  getCities(){
    const reqObj : any = {
      currentPage : 1,
      pageSize:5,
      ignorePagination : true
    }
    this.addressService.getAllCities(reqObj).subscribe((res: any) => {
      if(res.success){
        this.allCities = res.data.records;
        this.allCities.unshift({
          "id": -1,
          "cityName": null,
          "regionId": -1,
          "isActive": true
        })
      }
    })
  }
  getCoordinates() {
    var geocoder = new google.maps.Geocoder();
    this.isDifferentCity = true;
    geocoder.geocode({ 'address': this.addressForm.controls['streetAddress'].value },  (results: any, status: any) => {
      if (status == google.maps.GeocoderStatus.OK) {
        if(results[0].address_components.length){
          const city  =  results[0].address_components.find((item: any) => item.types.includes('locality'))
          if(city?.long_name === this.addressDetailCity){
            this.isDifferentCity = false;
            this.cd.detectChanges();
          }
        }
      }
    });
  }

  getAllRegion(){
    let reqObj = {
      currentPage: 1,
      pageSize:  5,
      ignorePagination: true
    }
    this.addressService.getAllRegions(reqObj).subscribe((res : any) => {
      if(res.success){
        this.allRegionList = res.data.records;
        this.allRegionList.unshift({
          "id": -1,
          "regionName": null,
          "regionId": -1,
          "isActive": true
        })
      }
    });
  }

  filterCitiesByRegion(regionId: any): void {

    const selectedRegion = this.allRegionList.find((region: { id: any; }) => region.id === regionId);
    this.addressForm.patchValue({
      region: {
        regionId: selectedRegion.id,
        regionName: selectedRegion.regionName
      }
    });
    this.filteredCities = this.allCities.filter((city: { regionId: any; }) => city.regionId === regionId);
  }
  addMoreAddress() {
    if(this.routeToCheckOut){
      if (this.redirectUrl && this.redirectUrl !== '') {
        this.router.navigate(['/account/address/add-address'], {state: {returnUrl: this.redirectUrl,lat:this.Lat,lng:this.Lng },
          queryParams: { checkout: 'checkout' }
        })
        this.redirectUrl = '';
      } else {
        this.router.navigate(['/account/address/add-address'],{state: {returnUrl: this.redirectUrl,lat:this.Lat,lng:this.Lng},
          queryParams: { checkout: 'checkout' }})
      }
    } else {
      if (this.redirectUrl && this.redirectUrl !== '') {
        this.router.navigate(['/account/address/add-address'], {state: {returnUrl: this.redirectUrl,lat:this.Lat,lng:this.Lng}})
        this.redirectUrl = '';
      } else {
        this.router.navigate(['/account/address/add-address'],{state: {returnUrl: this.redirectUrl,lat:this.Lat,lng:this.Lng}})
      }
    }
  }
}
