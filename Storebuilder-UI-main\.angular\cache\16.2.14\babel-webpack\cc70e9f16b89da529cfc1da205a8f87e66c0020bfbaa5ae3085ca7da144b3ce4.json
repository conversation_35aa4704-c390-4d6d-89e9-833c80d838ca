{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CommentPipe } from \"../pipes/santiaze.pipe\";\nimport { DeliveryDateFormatPipe } from \"../pipes/delivery-date-format.pipe\";\n// Modules\nimport { FormsModule, ReactiveFormsModule } from \"@angular/forms\";\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { RouterModule } from \"@angular/router\";\nimport { ButtonModule } from \"primeng/button\";\nimport { BreadcrumbModule } from \"primeng/breadcrumb\";\nimport { DialogModule } from \"primeng/dialog\";\n// Components\nimport { SizeGuideModalComponent } from \"@shared/components/size-guide-modal/size-guide-modal.component\";\nimport { DeleteCartModalComponent } from \"@shared/modals/delete-cart-modal/delete-cart-modal.component\";\nimport { ConfirmationModalComponent } from \"@shared/modals/confirmation-modal/confirmation-modal.component\";\nimport { MessageModalComponent } from \"@shared/modals/message-modal/message-modal.component\";\nimport { SuccessModalComponent } from \"@shared/modals/success-modal/success-modal.component\";\nimport { PaymentFailedModalComponent } from \"@shared/modals/payment-failed-modal/payment-failed-modal.component\";\nimport { MobileModalComponent } from \"@shared/modals/mobile-modal/mobile-modal.component\";\nimport { AddressModalComponent } from \"@shared/modals/address-modal/address-modal.component\";\nimport { StrictNumberOnlyDirective } from \"@core/directives\";\nimport { PaginatorModule } from \"primeng/paginator\";\nimport { CheckboxModule } from \"primeng/checkbox\";\nimport { MobileCartModalComponent } from '@shared/modals/mobile-cart-modal/mobile-cart-modal.component';\nimport { LoaderDotsComponent } from \"@shared/components/loader-dots/loader-dots.component\";\nimport { ConfirmationDeleteDialogComponent } from \"../modals/confirmation-delete-dialog/confirmation-delete-dialog.component\";\nimport { AgeRestrictionComponent } from \"@shared/components/age-restriction/age-restriction.component\";\nimport * as i0 from \"@angular/core\";\nconst APP_COMPONENTS = [SizeGuideModalComponent, DeleteCartModalComponent, ConfirmationModalComponent, MessageModalComponent, SuccessModalComponent, PaymentFailedModalComponent, MobileModalComponent, AddressModalComponent, StrictNumberOnlyDirective, CommentPipe, DeliveryDateFormatPipe, MobileCartModalComponent, LoaderDotsComponent, AgeRestrictionComponent];\nconst APP_MODULES = [ButtonModule, BreadcrumbModule, DialogModule, PaginatorModule, CommonModule, TranslateModule, RouterModule, FormsModule, ReactiveFormsModule];\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n    static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n    static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [CommentPipe, DeliveryDateFormatPipe],\n      imports: [APP_MODULES, ConfirmationDeleteDialogComponent, ButtonModule, RouterModule, BreadcrumbModule, FormsModule, ReactiveFormsModule, CheckboxModule]\n    });\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}