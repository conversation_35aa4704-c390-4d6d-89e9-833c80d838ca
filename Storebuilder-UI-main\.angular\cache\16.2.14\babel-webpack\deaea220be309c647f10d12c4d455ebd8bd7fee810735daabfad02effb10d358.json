{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\modal.model.ts"], "sourcesContent": ["\r\nexport interface ConfirmationModal extends ModalConfig{\r\n  point1:string;\r\n  point2:string;\r\n}\r\n\r\nexport interface ModalConfig {\r\n  header:string;\r\n  message: string;\r\n  returnModal?:boolean;\r\n  returnModalConfirmation?:boolean;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}