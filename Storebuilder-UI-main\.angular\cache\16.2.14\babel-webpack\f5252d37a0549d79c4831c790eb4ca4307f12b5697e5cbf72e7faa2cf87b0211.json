{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\main-slider.ts"], "sourcesContent": ["export interface MainSlider {\r\n  creationDate?: Date;\r\n  id?: number;\r\n  imageUrl?: string;\r\n  title?: string;\r\n  isDelete?: boolean;\r\n  redirectTypeId?: number;\r\n  redirectValue?: number;\r\n  shopId?: number;\r\n  tenantId?: number;\r\n  updateOn?: Date;\r\n}\r\n\r\n\r\nexport interface Configuration {\r\n  id: number;\r\n  bannerId:number;\r\n  categoryId:number;\r\n  searchData:string;\r\n  order:number;\r\n  shopId:number;\r\n  showRoomTypeId:number;\r\n  topNumber:number;\r\n}"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}