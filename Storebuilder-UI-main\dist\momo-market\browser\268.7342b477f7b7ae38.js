"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[268],{2268:(O,d,r)=>{r.r(d),r.d(d,{CategoriesModule:()=>y});var a=r(6814),s=r(6075),t=r(5879),l=r(864),c=r(6663),h=r(2960),g=r(2655);function f(e,M){if(1&e&&(t.TgZ(0,"a",9),t._UZ(1,"app-mtn-category-card",10),t.qZA()),2&e){const n=M.$implicit,o=t.oxw();t.MGl("routerLink","/category/",n.id,""),t.xp6(1),t.Q6J("category",n)("showAllMobile",o.screenWidth<768)}}const u=function(e){return{"content-container":e}},C=[{path:"",component:(()=>{class e{activatedRoute;store;productService;translateService;cdr;platformId;items=[];categories=[];home={icon:"pi pi-home",routerLink:"/"};categoryId;products=[];screenWidth;constructor(n,o,i,p,m,I){this.activatedRoute=n,this.store=o,this.productService=i,this.translateService=p,this.cdr=m,this.platformId=I,this.categoryId=this.activatedRoute.snapshot.params.id,(0,a.NF)(this.platformId)&&(this.screenWidth=window.innerWidth)}onResize(n){this.screenWidth=n.target.innerWidth}ngOnInit(){this.items=[{label:this.translateService.instant("categories.allCategories")}],this.home={icon:"pi pi-home",routerLink:"/"}}ngAfterViewInit(){setTimeout(()=>{let n=localStorage.getItem("allCategories");n&&""!==n&&(this.categories=JSON.parse(n),this.cdr.detectChanges())},100)}static \u0275fac=function(o){return new(o||e)(t.Y36(s.gz),t.Y36(l.d6),t.Y36(l.M5),t.Y36(c.sK),t.Y36(t.sBO),t.Y36(t.Lbi))};static \u0275cmp=t.Xpm({type:e,selectors:[["app-index"]],hostBindings:function(o,i){1&o&&t.NdJ("resize",function(m){return i.onResize(m)},!1,t.Jf7)},decls:11,vars:9,consts:[[1,"categories-page"],[1,"breadcrumb",2,"margin-top","93px"],[3,"home","model"],[1,"mt-2","main_font",3,"ngClass"],[1,"grid"],[1,"col-12","col-md-12","flex","md:justify-content-start","all-categ"],[1,"all-category","bold-font"],[1,"my-4","flex","flex-row","flex-wrap","card-category"],["class","mt-2 mx-2 mb-5 card-spaces",3,"routerLink",4,"ngFor","ngForOf"],[1,"mt-2","mx-2","mb-5","card-spaces",3,"routerLink"],[3,"category","showAllMobile"]],template:function(o,i){1&o&&(t.TgZ(0,"section",0)(1,"div",1),t._UZ(2,"p-breadcrumb",2),t.qZA(),t.TgZ(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6),t._uU(7),t.ALo(8,"translate"),t.qZA()(),t.TgZ(9,"div",7),t.YNc(10,f,2,3,"a",8),t.qZA()()()()),2&o&&(t.xp6(2),t.Q6J("home",i.home)("model",i.items),t.xp6(1),t.Q6J("ngClass",t.VKq(7,u,i.screenWidth>=768)),t.xp6(4),t.hij(" ",t.lcZ(8,5,"categories.allCategories")," "),t.xp6(3),t.Q6J("ngForOf",i.categories))},dependencies:[a.mk,a.sg,h.$,s.rH,g.a,c.X$],styles:["a[_ngcontent-%COMP%]{text-decoration:none}.all-category[_ngcontent-%COMP%]{font-size:24px}@media screen and (max-width: 768px){.breadcrumb[_ngcontent-%COMP%]{margin-top:0!important}.all-categ[_ngcontent-%COMP%]{justify-content:left!important;margin-left:17px}.main_font[_ngcontent-%COMP%]{margin-top:0!important}.breadcrumb[_ngcontent-%COMP%]   p-breadcrumb.p-element[_ngcontent-%COMP%]{margin-bottom:15px!important}.card-category[_ngcontent-%COMP%]{margin-top:1rem!important}.card-spaces[_ngcontent-%COMP%]{margin-left:1.4rem!important;margin-right:0rem!important}}"]})}return e})()}];var v=r(4480),x=r(6574);let y=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=t.oAB({type:e});static \u0275inj=t.cJS({imports:[a.ez,x.p,s.Bz.forChild(C),v.T,c.aw,g.w]})}return e})()}}]);