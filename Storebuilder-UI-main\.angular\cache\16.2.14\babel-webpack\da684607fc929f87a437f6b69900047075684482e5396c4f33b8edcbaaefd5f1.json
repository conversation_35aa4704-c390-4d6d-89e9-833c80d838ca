{"ast": null, "code": "import { HttpParams } from \"@angular/common/http\";\nimport { CountryISO, PhoneNumberFormat, SearchCountryField } from \"ngx-intl-tel-input-gg\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"./contact-us-form.service\";\nimport * as i5 from \"@core/services/gtm.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"primeng/card\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"ngx-intl-tel-input-gg\";\nfunction ContactUsComponentComponent_section_0_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactUsComponentComponent_section_0_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.contactForm.value.Message.length, \" \");\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.contactUsDetails.title, \" \");\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r8.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r8.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"https://maps.google.com/?q=\", contact_r8.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r8.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate(\"href\", contact_r8.value, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"img\", 41);\n    i0.ɵɵtemplate(2, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_2_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(3, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_3_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(4, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_4_Template, 2, 2, \"a\", 43);\n    i0.ɵɵtemplate(5, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_5_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(6, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_a_6_Template, 2, 2, \"a\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", \"data:image/png;base64, \" + contact_r8.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r8.key === \"MobileNumber\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r8.key === \"Email\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r8.key === \"Location\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r8.key === \"WhatsAppNumber\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r8.key === \"ChattingLink\");\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ContactUsComponentComponent_section_0_p_card_67_div_3_div_1_Template, 7, 6, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r8.visible && contact_r8.value);\n  }\n}\nfunction ContactUsComponentComponent_section_0_p_card_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 34);\n    i0.ɵɵtemplate(1, ContactUsComponentComponent_section_0_p_card_67_p_1_Template, 2, 1, \"p\", 35);\n    i0.ɵɵelementStart(2, \"div\", 36);\n    i0.ɵɵtemplate(3, ContactUsComponentComponent_section_0_p_card_67_div_3_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.screenWidth >= 768);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.contactUsDetails == null ? null : ctx_r5.contactUsDetails.contactUs);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"hidden-navbar\": a0\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    border: a0\n  };\n};\nconst _c2 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction ContactUsComponentComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"em\", 3)(3, \"em\", 4);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"p-card\", 9)(14, \"form\", 10)(15, \"div\", 11)(16, \"div\", 12);\n    i0.ɵɵelement(17, \"input\", 13);\n    i0.ɵɵelementStart(18, \"label\", 14);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementStart(21, \"span\", 15);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 11)(24, \"div\", 12);\n    i0.ɵɵelement(25, \"input\", 16);\n    i0.ɵɵelementStart(26, \"label\", 17);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementStart(29, \"span\", 15);\n    i0.ɵɵtext(30, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 11)(32, \"div\", 12)(33, \"form\", 18, 19);\n    i0.ɵɵelement(35, \"ngx-intl-tel-input\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"label\", 21);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementStart(39, \"span\", 15);\n    i0.ɵɵtext(40, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(41, \"div\", 11)(42, \"div\", 12);\n    i0.ɵɵelement(43, \"input\", 22);\n    i0.ɵɵelementStart(44, \"label\", 23);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementStart(47, \"span\", 15);\n    i0.ɵɵtext(48, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 11)(50, \"span\", 24);\n    i0.ɵɵelement(51, \"textarea\", 25);\n    i0.ɵɵelementStart(52, \"div\", 26);\n    i0.ɵɵtemplate(53, ContactUsComponentComponent_section_0_span_53_Template, 2, 0, \"span\", 27);\n    i0.ɵɵtemplate(54, ContactUsComponentComponent_section_0_span_54_Template, 2, 1, \"span\", 28);\n    i0.ɵɵelementStart(55, \"span\", 29);\n    i0.ɵɵtext(56, \" /500\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"label\", 30);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"translate\");\n    i0.ɵɵelementStart(60, \"span\", 15);\n    i0.ɵɵtext(61, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(62, \"div\", 11)(63, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ContactUsComponentComponent_section_0_Template_button_click_63_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.submit());\n    });\n    i0.ɵɵtext(64);\n    i0.ɵɵpipe(65, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(66, \"hr\");\n    i0.ɵɵtemplate(67, ContactUsComponentComponent_section_0_p_card_67_Template, 4, 2, \"p-card\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(49, _c0, !(ctx_r0.navbarData == null ? null : ctx_r0.navbarData.isActive)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 33, \"contactUs.contactUs\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 35, \"contactUs.contactUsTitle\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.contactForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c1, ctx_r0.contactForm.controls.FirstName.touched && !ctx_r0.contactForm.controls.FirstName.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 37, \"contactUs.firstName\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(53, _c1, ctx_r0.contactForm.controls.LastName.touched && !ctx_r0.contactForm.controls.LastName.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 39, \"contactUs.lastName\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(55, _c1, ctx_r0.contactForm.controls.MobileNumber.touched && !ctx_r0.contactForm.controls.MobileNumber.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.contactForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r0.phoneInputLength)(\"numberFormat\", ctx_r0.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r0.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(57, _c2, ctx_r0.SearchCountryField.Iso2, ctx_r0.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r0.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r0.customPlaceHolder);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(38, 41, \"contactUs.mobileNumber\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(60, _c1, ctx_r0.contactForm.controls.Email.touched && !ctx_r0.contactForm.controls.Email.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(46, 43, \"contactUs.emailAddress\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(62, _c1, ctx_r0.contactForm.controls.Message.touched && !ctx_r0.contactForm.controls.Message.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r0.contactForm == null ? null : ctx_r0.contactForm.value == null ? null : ctx_r0.contactForm.value.Message == null ? null : ctx_r0.contactForm.value.Message.length));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.contactForm == null ? null : ctx_r0.contactForm.value == null ? null : ctx_r0.contactForm.value.Message == null ? null : ctx_r0.contactForm.value.Message.length) > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(59, 45, \"contactUs.message\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(65, 47, \"contactUs.sendMessage\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.contactUsDetails == null ? null : ctx_r0.contactUsDetails.contactUs == null ? null : ctx_r0.contactUsDetails.contactUs.length);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.contactUsDetails.title, \" \");\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r27.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", contact_r27.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"https://maps.google.com/?q=\", contact_r27.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate1(\"href\", \"tel:\", contact_r27.value, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵpropertyInterpolate(\"href\", contact_r27.value, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"img\", 41);\n    i0.ɵɵtemplate(2, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_2_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(3, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_3_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(4, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_4_Template, 2, 2, \"a\", 43);\n    i0.ɵɵtemplate(5, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_5_Template, 2, 2, \"a\", 42);\n    i0.ɵɵtemplate(6, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_a_6_Template, 2, 2, \"a\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", \"data:image/png;base64, \" + contact_r27.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r27.key === \"MobileNumber\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r27.key === \"Email\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r27.key === \"Location\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r27.key === \"WhatsAppNumber\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", contact_r27.key === \"ChattingLink\");\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, ContactUsComponentComponent_section_1_p_card_9_div_3_div_1_Template, 7, 6, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contact_r27 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !contact_r27.visible && contact_r27.value);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 49);\n    i0.ɵɵtemplate(1, ContactUsComponentComponent_section_1_p_card_9_p_1_Template, 2, 1, \"p\", 35);\n    i0.ɵɵelementStart(2, \"div\", 36);\n    i0.ɵɵtemplate(3, ContactUsComponentComponent_section_1_p_card_9_div_3_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.screenWidth >= 768);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.contactUsDetails == null ? null : ctx_r23.contactUsDetails.contactUs);\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_14_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_14_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r42.contactForm.value.Message.length, \" \");\n  }\n}\nfunction ContactUsComponentComponent_section_1_p_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-card\", 9)(1, \"form\", 10)(2, \"div\", 11)(3, \"div\", 12);\n    i0.ɵɵelement(4, \"input\", 13);\n    i0.ɵɵelementStart(5, \"label\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementStart(8, \"span\", 15);\n    i0.ɵɵtext(9, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12);\n    i0.ɵɵelement(12, \"input\", 16);\n    i0.ɵɵelementStart(13, \"label\", 17);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementStart(16, \"span\", 15);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 11)(19, \"div\", 50)(20, \"form\", 18, 19);\n    i0.ɵɵelement(22, \"ngx-intl-tel-input\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"label\", 21);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementStart(26, \"span\", 15);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 11)(29, \"div\", 12);\n    i0.ɵɵelement(30, \"input\", 22);\n    i0.ɵɵelementStart(31, \"label\", 23);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"translate\");\n    i0.ɵɵelementStart(34, \"span\", 15);\n    i0.ɵɵtext(35, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 11)(37, \"span\", 24);\n    i0.ɵɵelement(38, \"textarea\", 25);\n    i0.ɵɵelementStart(39, \"div\", 26);\n    i0.ɵɵtemplate(40, ContactUsComponentComponent_section_1_p_card_14_span_40_Template, 2, 0, \"span\", 27);\n    i0.ɵɵtemplate(41, ContactUsComponentComponent_section_1_p_card_14_span_41_Template, 2, 1, \"span\", 28);\n    i0.ɵɵelementStart(42, \"span\", 29);\n    i0.ɵɵtext(43, \" /500\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"label\", 30);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵelementStart(47, \"span\", 15);\n    i0.ɵɵtext(48, \"*\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 11)(50, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ContactUsComponentComponent_section_1_p_card_14_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r43.submit());\n    });\n    i0.ɵɵtext(51);\n    i0.ɵɵpipe(52, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r24.contactForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(40, _c1, ctx_r24.contactForm.controls.FirstName.touched && !ctx_r24.contactForm.controls.FirstName.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(7, 28, \"contactUs.firstName\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(42, _c1, ctx_r24.contactForm.controls.LastName.touched && !ctx_r24.contactForm.controls.LastName.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 30, \"contactUs.lastName\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(44, _c1, ctx_r24.contactForm.controls.MobileNumber.touched && !ctx_r24.contactForm.controls.MobileNumber.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r24.contactForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cssClass\", \"custom contact-input-phone\")(\"enableAutoCountrySelect\", true)(\"enablePlaceholder\", true)(\"maxLength\", ctx_r24.phoneInputLength)(\"numberFormat\", ctx_r24.PhoneNumberFormat.National)(\"phoneValidation\", false)(\"preferredCountries\", ctx_r24.preferredCountries)(\"searchCountryField\", i0.ɵɵpureFunction2(46, _c2, ctx_r24.SearchCountryField.Iso2, ctx_r24.SearchCountryField.Name))(\"searchCountryFlag\", true)(\"selectFirstCountry\", false)(\"selectedCountryISO\", ctx_r24.CustomCountryISO)(\"separateDialCode\", true)(\"customPlaceholder\", ctx_r24.customPlaceHolder);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 32, \"contactUs.mobileNumber\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(49, _c1, ctx_r24.contactForm.controls.Email.touched && !ctx_r24.contactForm.controls.Email.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 34, \"contactUs.emailAddress\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(51, _c1, ctx_r24.contactForm.controls.Message.touched && !ctx_r24.contactForm.controls.Message.valid ? \"1px solid red\" : \"0px solid transparent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r24.contactForm == null ? null : ctx_r24.contactForm.value == null ? null : ctx_r24.contactForm.value.Message == null ? null : ctx_r24.contactForm.value.Message.length));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r24.contactForm == null ? null : ctx_r24.contactForm.value == null ? null : ctx_r24.contactForm.value.Message == null ? null : ctx_r24.contactForm.value.Message.length) > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(46, 36, \"contactUs.message\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(52, 38, \"contactUs.sendMessage\"), \" \");\n  }\n}\nfunction ContactUsComponentComponent_section_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"em\", 3)(3, \"em\", 4);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 8);\n    i0.ɵɵtemplate(9, ContactUsComponentComponent_section_1_p_card_9_Template, 4, 2, \"p-card\", 46);\n    i0.ɵɵelement(10, \"hr\");\n    i0.ɵɵelementStart(11, \"div\", 47);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ContactUsComponentComponent_section_1_p_card_14_Template, 53, 53, \"p-card\", 48);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r1.navbarData == null ? null : ctx_r1.navbarData.isActive));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 6, \"contactUs.contactUsMobileTemplate\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth < 768 && (ctx_r1.contactUsDetails == null ? null : ctx_r1.contactUsDetails.contactUs == null ? null : ctx_r1.contactUsDetails.contactUs.length));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 8, \"contactUs.contactUsTitle\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.screenWidth < 768);\n  }\n}\nexport let ContactUsComponentComponent = /*#__PURE__*/(() => {\n  class ContactUsComponentComponent {\n    contactUsService;\n    translate;\n    messageService;\n    formService;\n    userService;\n    appDataService;\n    $gtmService;\n    contactUsDetails = {};\n    contactForm;\n    screenWidth = window.innerWidth;\n    userLoggedIn = false;\n    SearchCountryField = SearchCountryField;\n    CustomCountryISO;\n    PhoneNumberFormat = PhoneNumberFormat;\n    preferredCountries = [CountryISO.UnitedStates, CountryISO.UnitedKingdom];\n    separateDialCode = false;\n    userData;\n    customPlaceHolder = '';\n    phoneInputLength = 12;\n    navbarData;\n    constructor(contactUsService, translate, messageService, formService, userService, appDataService, $gtmService) {\n      this.contactUsService = contactUsService;\n      this.translate = translate;\n      this.messageService = messageService;\n      this.formService = formService;\n      this.userService = userService;\n      this.appDataService = appDataService;\n      this.$gtmService = $gtmService;\n      this.contactForm = this.formService.form;\n      let tenantId = localStorage.getItem('tenantId');\n      if (tenantId && tenantId !== '') {\n        if (tenantId == '1') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '2') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '3') {\n          this.customPlaceHolder = 'XXXXXXXXX';\n        } else if (tenantId == '4') {\n          this.customPlaceHolder = 'XXXXXXXXXX';\n        }\n      }\n    }\n    ngOnInit() {\n      this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n      this.$gtmService.pushPageView('contact us');\n      this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n      this.userService.updateScrollTop(true);\n      this.userData = localStorage.getItem('profile');\n      this.userAssign();\n      this.getContactUsDetails();\n      if (this.appDataService.configuration) {\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n        if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n      }\n    }\n    userAssign() {\n      if (this.userData) {\n        this.userLoggedIn = true;\n        const patchData = {};\n        this.userData = JSON.parse(this.userData);\n        let names = this.userData.name.split(' ');\n        if (names.length) {\n          if (names[0]) patchData.FirstName = names[0];\n          if (names[1]) patchData.LastName = names[1];\n        }\n        if (this.userData.mobileNumber) patchData.MobileNumber = this.userData.mobileNumber.substring(3);\n        if (this.userData.email) patchData.Email = this.userData.email;\n        this.contactForm.patchValue(patchData);\n      }\n    }\n    onResize(event) {\n      this.screenWidth = event.target.innerWidth;\n    }\n    getContactUsDetails() {\n      this.contactUsService.getShopContactUs().subscribe({\n        next: res => {\n          this.contactUsDetails = res.data;\n          if (!res.success) {\n            this.messageService.add({\n              severity: 'error',\n              summary: this.translate.instant('ErrorMessages.fetchError'),\n              detail: res.message\n            });\n          }\n        },\n        error: err => {}\n      });\n    }\n    submit() {\n      this.formService.markTouched(this.contactForm);\n      if (this.contactForm.valid) {\n        this.contactForm.controls['MobileNumber'].setValue(this.contactForm.controls['MobileNumber'].value?.e164Number);\n        const httpParams = new HttpParams({\n          fromObject: this.formService.Dto()\n        });\n        this.contactUsService.submitContactForm(httpParams).subscribe({\n          next: res => {\n            if (res.success) {\n              if (this.userLoggedIn) {\n                this.contactForm.controls['Message'].reset();\n              } else {\n                this.contactForm.controls['FirstName'].reset();\n                this.contactForm.controls['LastName'].reset();\n                this.contactForm.controls['MobileNumber'].reset();\n                this.contactForm.controls['Email'].reset();\n                this.contactForm.controls['Message'].reset();\n              }\n              this.messageService.add({\n                severity: 'success',\n                detail: this.translate.instant('ResponseMessages.contactUsSuccessMessage')\n              });\n            } else {\n              this.messageService.add({\n                severity: 'error',\n                detail: this.translate.instant('ErrorMessages.contactUsErrorMessage')\n              });\n            }\n          },\n          error: err => {\n            this.messageService.add({\n              severity: 'error',\n              detail: this.translate.instant('ErrorMessages.contactUsErrorMessage')\n            });\n          }\n        });\n      }\n    }\n    static ɵfac = function ContactUsComponentComponent_Factory(t) {\n      return new (t || ContactUsComponentComponent)(i0.ɵɵdirectiveInject(i1.ContactUsService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ContactUsFormService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i5.GTMService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactUsComponentComponent,\n      selectors: [[\"app-ContactUsComponent\"]],\n      hostBindings: function ContactUsComponentComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ContactUsComponentComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        contactUsDetails: \"contactUsDetails\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"contact-us-page\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"contact-us-page\", 3, \"ngClass\"], [1, \"breadcrumb-address\", \"d-flex\"], [\"aria-hidden\", \"true\", \"tabindex\", \"0\", 1, \"pi\", \"pi-home\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"pi\", \"pi-angle-right\"], [1, \"content-container\", \"contact-btn\"], [1, \"row\", \"mt-1\"], [1, \"font-size-24\", \"bold-font\", \"contact-us-title\"], [1, \"d-flex\", \"contact-card\"], [1, \"mt-3\", \"mb-5\"], [1, \"grid\", 3, \"formGroup\"], [1, \"col-12\"], [1, \"p-float-label\", \"w-full\", 3, \"ngStyle\"], [\"formControlName\", \"FirstName\", \"id\", \"fname\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", \"p-float-label\", \"w-full\", \"contact-input\"], [\"for\", \"fname\", 1, \"contact-label\"], [1, \"red-asterisk\"], [\"formControlName\", \"LastName\", \"id\", \"lname\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", \"p-float-label\", \"w-full\", \"contact-input\"], [\"for\", \"lname\", 1, \"contact-label\"], [3, \"formGroup\"], [\"f\", \"ngForm\"], [\"formControlName\", \"MobileNumber\", \"name\", \"phone\", 3, \"cssClass\", \"enableAutoCountrySelect\", \"enablePlaceholder\", \"maxLength\", \"numberFormat\", \"phoneValidation\", \"preferredCountries\", \"searchCountryField\", \"searchCountryFlag\", \"selectFirstCountry\", \"selectedCountryISO\", \"separateDialCode\", \"customPlaceholder\"], [\"for\", \"mobileNumber\", 1, \"contact-label\"], [\"formControlName\", \"Email\", \"id\", \"emailAddress\", \"pInputText\", \"\", \"type\", \"text\", 1, \"form-input\", \"p-float-label\", \"w-full\", \"contact-input\"], [\"for\", \"emailAddress\", 1, \"contact-label\", \"email-label\"], [1, \"p-float-label\", 2, \"border\", \"0px solid transparent\", \"background-color\", \"rgb(245, 245, 245) !important\", \"height\", \"136px !important\", 3, \"ngStyle\"], [\"formControlName\", \"Message\", \"id\", \"message\", \"maxlength\", \"500\", \"pInputTextarea\", \"\", \"rows\", \"8\", \"type\", \"text\", 1, \"form-input\", \"p-float-label\", \"w-full\", \"contact-text-area\", \"contact-textarea\"], [1, \"the-count\"], [4, \"ngIf\"], [\"id\", \"current\", 4, \"ngIf\"], [\"id\", \"maximum\"], [\"for\", \"message\", 1, \"contact-label\", 2, \"top\", \"0 !important\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"my-2\", \"font-size-14\", \"second-btn\", \"d-block\", \"text-center\", \"col-3\", \"contactUs-btn\", 3, \"click\"], [\"class\", \"desktop mt-3\", 4, \"ngIf\"], [\"id\", \"current\"], [1, \"desktop\", \"mt-3\"], [\"class\", \"col-12 contact-detail-2\", 4, \"ngIf\"], [1, \"p-field\", \"p-col-12\", \"contact-details-form\"], [\"class\", \"p-field p-col-12 contact-details-form\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"contact-detail-2\"], [\"class\", \"col-12 col-md-4 w-full address-text\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-4\", \"w-full\", \"address-text\"], [\"alt\", \"No Image\", \"width\", \"16\", \"height\", \"16\", 1, \"phone-img\", \"ng-star-inserted\", 3, \"src\"], [3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", 3, \"href\", 4, \"ngIf\"], [3, \"href\"], [\"target\", \"_blank\", 3, \"href\"], [\"class\", \"desktop pad mt-3\", 4, \"ngIf\"], [1, \"bold-font\", \"font-size-24\", \"contact-us-title\"], [\"class\", \"mt-3 mb-5\", 4, \"ngIf\"], [1, \"desktop\", \"pad\", \"mt-3\"], [1, \"p-float-label\", \"w-full\", \"z-0\", 3, \"ngStyle\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-field\", \"p-col-12\", \"font-size-14\", \"second-btn\", \"d-block\", \"text-center\", \"col-3\", \"contactUs-btn\", 3, \"click\"]],\n      template: function ContactUsComponentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ContactUsComponentComponent_section_0_Template, 68, 64, \"section\", 0);\n          i0.ɵɵtemplate(1, ContactUsComponentComponent_section_1_Template, 15, 12, \"section\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.contactUsDetails && ctx.screenWidth > 767);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.contactUsDetails && ctx.screenWidth < 768);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgStyle, i7.RouterLink, i8.Card, i9.InputText, i10.ɵNgNoValidate, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgControlStatusGroup, i10.MaxLengthValidator, i10.FormGroupDirective, i10.FormControlName, i11.NgxIntlTelInputComponent, i11.NativeElementInjectorDirective, i2.TranslatePipe],\n      styles: [\"textarea[_ngcontent-%COMP%]{box-sizing:border-box;resize:none;width:100%}.desktop[_ngcontent-%COMP%]{margin-left:12px;width:44%}.content-container[_ngcontent-%COMP%]{background:#f5f5f5;justify-content:center;display:grid}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{margin-right:40px}@media (max-width: 1366px){.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]{flex-direction:column!important}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{margin-right:0}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child   .contact-text-area[_ngcontent-%COMP%]{margin:1px}}@media (max-width: 420px){.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .mb-5[_ngcontent-%COMP%]{margin-bottom:50px}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child{width:100%}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .desktop[_ngcontent-%COMP%]{margin-top:20px;margin-left:0;width:100%;margin-bottom:10px}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .mbl-btn[_ngcontent-%COMP%]{display:flex;justify-content:center}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .contact-details-form[_ngcontent-%COMP%]{margin-bottom:0!important}}.contact-input[_ngcontent-%COMP%]{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;padding-top:20px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px}.contact-label[_ngcontent-%COMP%]{position:absolute;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:16px!important;font-weight:500;color:#323232;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.contact-textarea[_ngcontent-%COMP%]{height:109px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px;position:absolute;top:25px;padding-top:0}textarea#message[_ngcontent-%COMP%]{outline:none!important}.p-card.p-component[_ngcontent-%COMP%]{border-radius:5px;padding:15px;background:#ffffff;border:1px solid rgba(229,229,229,.9019607843);padding:0!important}.phone-img[_ngcontent-%COMP%]{margin-left:4px;margin-right:7px;float:left;width:20px;height:20px;margin-bottom:10px}.address-text[_ngcontent-%COMP%]{font-size:16px;color:#383843!important;font-weight:400;font-family:var(--medium-font)!important;margin-bottom:0}.breadcrumb-address.d-flex[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;background-color:#f5f5f5;padding:1rem 2rem;font-size:15px;gap:10px}i.fa.fa-angle-left[_ngcontent-%COMP%]{padding:0 10px;margin:auto 0}.contact-detail-2[_ngcontent-%COMP%]{color:#3d3d48;font-family:var(--medium-font)!important;font-weight:700;font-size:24px}.contactUs-btn[_ngcontent-%COMP%]{padding:7px;margin-top:10px}@media screen and (max-width: 768px){.contactUs-btn[_ngcontent-%COMP%]{width:100%;border-radius:3px}}.email-label[_ngcontent-%COMP%]{text-transform:capitalize}@media screen and (max-width: 768px){.contact-us-page[_ngcontent-%COMP%]{margin:25% 20px 20px}button.p-element.p-field.p-col-12.my-2.font-size-14.second-btn.d-block.text-center.col-3.p-button.p-component[_ngcontent-%COMP%]{width:100%}.content-container[_ngcontent-%COMP%]{padding-left:0!important;padding-right:0!important}.mb-5.mt-3.p-element[_ngcontent-%COMP%]{margin-bottom:0!important}.red-asterisk[_ngcontent-%COMP%]{color:#ee5858}.desktop[_ngcontent-%COMP%]{margin-left:12px;width:98%;margin-bottom:30px}.content-container.contact-btn[_ngcontent-%COMP%], .breadcrumb-address.d-flex[_ngcontent-%COMP%]{background-color:#fff}.contact-us-title[_ngcontent-%COMP%]{font-size:20px!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:35px!important}}@media screen and (max-width: 768px) and (max-width: 700px){.hidden-navbar[_ngcontent-%COMP%]{margin-top:65px!important}}@media screen and (max-width: 768px) and (max-width: 575px){.hidden-navbar[_ngcontent-%COMP%]{margin-top:93px!important}}textarea[_ngcontent-%COMP%]:focus ~ label[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%] ~ label[_ngcontent-%COMP%]{top:-.75em!important}.the-count[_ngcontent-%COMP%]{padding:.1rem 0 0;font-size:.875rem;display:flex;position:absolute;right:20px;top:135px}a[_ngcontent-%COMP%]:hover{color:#00f!important}  .contact-card .iti__selected-flag{pointer-events:none!important}.address-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#000!important;white-space:normal;word-break:break-word;max-width:80%}\"]\n    });\n  }\n  return ContactUsComponentComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}