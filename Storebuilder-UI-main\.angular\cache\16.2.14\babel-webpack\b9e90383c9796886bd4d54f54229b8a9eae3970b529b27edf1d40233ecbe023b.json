{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from \"@angular/router\";\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputMaskModule } from \"primeng/inputmask\";\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { DialogModule } from 'primeng/dialog';\nimport { routes } from \"./routes\";\n// Components\nimport { IndexComponent } from './components/index/index.component';\nimport { RegisterOtpComponent } from './components/register-otp/register-otp.component';\nimport { RegisterResetPasswordComponent } from './components/register-reset-password/register-reset-password.component';\nimport { TranslateModule } from \"@ngx-translate/core\";\nimport { ButtonModule } from \"primeng/button\";\nimport { NgxIntlTelInputModule } from \"ngx-intl-tel-input-gg\";\nimport { ProgressSpinnerModule } from \"primeng/progressspinner\";\nimport { SharedModule } from \"@shared/modules/shared.module\";\nimport { InitialModule } from \"../../../shared/modules/initial.module\";\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { BackButtonComponent } from '@shared/components/back-button/back-button.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class RegisterModule {\n  static #_ = this.ɵfac = function RegisterModule_Factory(t) {\n    return new (t || RegisterModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: RegisterModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [DialogService],\n    imports: [CommonModule, PasswordModule, InputTextModule, InputMaskModule, FormsModule, DialogModule, ReactiveFormsModule, RouterModule.forChild(routes), TranslateModule, ButtonModule, NgxIntlTelInputModule, ProgressSpinnerModule, SharedModule, InitialModule, BackButtonComponent]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RegisterModule, {\n    declarations: [IndexComponent, RegisterOtpComponent, RegisterResetPasswordComponent],\n    imports: [CommonModule, PasswordModule, InputTextModule, InputMaskModule, FormsModule, DialogModule, ReactiveFormsModule, i1.RouterModule, TranslateModule, ButtonModule, NgxIntlTelInputModule, ProgressSpinnerModule, SharedModule, InitialModule, BackButtonComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "InputTextModule", "InputMaskModule", "FormsModule", "ReactiveFormsModule", "PasswordModule", "DialogModule", "routes", "IndexComponent", "RegisterOtpComponent", "RegisterResetPasswordComponent", "TranslateModule", "ButtonModule", "NgxIntlTelInputModule", "ProgressSpinnerModule", "SharedModule", "InitialModule", "DialogService", "BackButtonComponent", "RegisterModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\auth\\register\\register.module.ts"], "sourcesContent": ["import {NgModule} from '@angular/core';\r\nimport {CommonModule} from '@angular/common';\r\nimport {RouterModule} from \"@angular/router\";\r\nimport {InputTextModule} from 'primeng/inputtext';\r\nimport {InputMaskModule} from \"primeng/inputmask\";\r\nimport {FormsModule, ReactiveFormsModule} from '@angular/forms';\r\nimport {PasswordModule} from 'primeng/password';\r\nimport {DialogModule} from 'primeng/dialog';\r\n\r\nimport {routes} from \"./routes\";\r\n\r\n// Components\r\nimport {IndexComponent} from './components/index/index.component';\r\nimport {RegisterOtpComponent} from './components/register-otp/register-otp.component';\r\nimport {RegisterResetPasswordComponent} from './components/register-reset-password/register-reset-password.component';\r\n\r\nimport {TranslateModule} from \"@ngx-translate/core\";\r\nimport {ButtonModule} from \"primeng/button\";\r\nimport {NgxIntlTelInputModule} from \"ngx-intl-tel-input-gg\";\r\nimport {ProgressSpinnerModule} from \"primeng/progressspinner\";\r\nimport {SharedModule} from \"@shared/modules/shared.module\";\r\nimport { InitialModule } from \"../../../shared/modules/initial.module\";\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { BackButtonComponent } from '@shared/components/back-button/back-button.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IndexComponent,\r\n    RegisterOtpComponent,\r\n    RegisterResetPasswordComponent\r\n    ],\r\n  imports: [\r\n    CommonModule,\r\n    PasswordModule,\r\n    InputTextModule,\r\n    InputMaskModule,\r\n    FormsModule,\r\n    DialogModule,\r\n    ReactiveFormsModule,\r\n    RouterModule.forChild(routes),\r\n    TranslateModule,\r\n    ButtonModule,\r\n    NgxIntlTelInputModule,\r\n    ProgressSpinnerModule,\r\n    SharedModule,\r\n    InitialModule,\r\n    BackButtonComponent\r\n\r\n],\r\nproviders:[DialogService]\r\n})\r\nexport class RegisterModule {\r\n}\r\n"], "mappings": "AACA,SAAQA,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,YAAY,QAAO,iBAAiB;AAC5C,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,eAAe,QAAO,mBAAmB;AACjD,SAAQC,WAAW,EAAEC,mBAAmB,QAAO,gBAAgB;AAC/D,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,YAAY,QAAO,gBAAgB;AAE3C,SAAQC,MAAM,QAAO,UAAU;AAE/B;AACA,SAAQC,cAAc,QAAO,oCAAoC;AACjE,SAAQC,oBAAoB,QAAO,kDAAkD;AACrF,SAAQC,8BAA8B,QAAO,wEAAwE;AAErH,SAAQC,eAAe,QAAO,qBAAqB;AACnD,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,qBAAqB,QAAO,uBAAuB;AAC3D,SAAQC,qBAAqB,QAAO,yBAAyB;AAC7D,SAAQC,YAAY,QAAO,+BAA+B;AAC1D,SAASC,aAAa,QAAQ,wCAAwC;AACtE,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,mBAAmB,QAAQ,sDAAsD;;;AA6B1F,OAAM,MAAOC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;eAFjB,CAACL,aAAa,CAAC;IAAAM,OAAA,GAjBrBxB,YAAY,EACZM,cAAc,EACdJ,eAAe,EACfC,eAAe,EACfC,WAAW,EACXG,YAAY,EACZF,mBAAmB,EACnBJ,YAAY,CAACwB,QAAQ,CAACjB,MAAM,CAAC,EAC7BI,eAAe,EACfC,YAAY,EACZC,qBAAqB,EACrBC,qBAAqB,EACrBC,YAAY,EACZC,aAAa,EACbE,mBAAmB;EAAA;;;2EAKVC,cAAc;IAAAM,YAAA,GAxBvBjB,cAAc,EACdC,oBAAoB,EACpBC,8BAA8B;IAAAa,OAAA,GAG9BxB,YAAY,EACZM,cAAc,EACdJ,eAAe,EACfC,eAAe,EACfC,WAAW,EACXG,YAAY,EACZF,mBAAmB,EAAAsB,EAAA,CAAA1B,YAAA,EAEnBW,eAAe,EACfC,YAAY,EACZC,qBAAqB,EACrBC,qBAAqB,EACrBC,YAAY,EACZC,aAAa,EACbE,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}