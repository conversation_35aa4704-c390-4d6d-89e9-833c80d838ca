"use strict";(self.webpackChunkmomo_market=self.webpackChunkmomo_market||[]).push([[367],{7367:(F,g,c)=>{c.r(g),c.d(g,{ContactUsModule:()=>ot});var s=c(6814),_=c(6075),f=c(4480),h=c(9862),m=c(7152),t=c(5879),x=c(864),d=c(6663),l=c(5219),i=c(6223),b=c(553);let C=(()=>{class e{fb;form;constructor(n){this.fb=n,this.form=this.fb.group({FirstName:["",[i.kI.required]],LastName:["",[]],Email:["",[i.kI.required,i.kI.email]],MobileNumber:["",[i.kI.required]],Message:["",[i.kI.required]]})}get formControls(){return this.form.controls}patchForm(n){this.form.patchValue(n)}isInvalid(n){return this.form.get(n)?.invalid&&(this.form.get(n)?.touched||this.form.get(n)?.dirty)}markTouched(n){n.hasOwnProperty("controls")?Object.keys(n.controls).forEach(o=>{n.controls[o].markAsTouched(),n.controls[o].statusChanges.emit("TOUCHED"),n.controls[o].hasOwnProperty("controls")&&this.markTouched(n[o])}):(n.markAsTouched(),n.statusChanges.emit("TOUCHED"))}Dto(){const n=this.form.value;return n.TenantId=localStorage.getItem("tenantId")??b.N.defaultTenant,n}static \u0275fac=function(o){return new(o||e)(t.LFG(i.qu))};static \u0275prov=t.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var y=c(9147),U=c(4532),Z=c(3714);function P(e,a){1&e&&(t.TgZ(0,"span"),t._uU(1,"0 "),t.qZA())}function N(e,a){if(1&e&&(t.TgZ(0,"span",33),t._uU(1),t.qZA()),2&e){const n=t.oxw(2);t.xp6(1),t.hij("",n.contactForm.value.Message.length," ")}}function S(e,a){if(1&e&&(t.TgZ(0,"p",38),t._uU(1),t.qZA()),2&e){const n=t.oxw(3);t.xp6(1),t.hij(" ",n.contactUsDetails.title," ")}}function A(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","tel:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function I(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","mailto:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function D(e,a){if(1&e&&(t.TgZ(0,"a",45),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","https://maps.google.com/?q=",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function L(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","tel:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function q(e,a){if(1&e&&(t.TgZ(0,"a",45),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.s9C("href",n.value,t.LSH),t.xp6(1),t.Oqu(n.value)}}function J(e,a){if(1&e&&(t.TgZ(0,"div",40),t._UZ(1,"img",41),t.YNc(2,A,2,2,"a",42),t.YNc(3,I,2,2,"a",42),t.YNc(4,D,2,2,"a",43),t.YNc(5,L,2,2,"a",42),t.YNc(6,q,2,2,"a",43),t.qZA()),2&e){const n=t.oxw().$implicit;t.xp6(1),t.Q6J("src","data:image/png;base64, "+n.image,t.LSH),t.xp6(1),t.Q6J("ngIf","MobileNumber"===n.key),t.xp6(1),t.Q6J("ngIf","Email"===n.key),t.xp6(1),t.Q6J("ngIf","Location"===n.key),t.xp6(1),t.Q6J("ngIf","WhatsAppNumber"===n.key),t.xp6(1),t.Q6J("ngIf","ChattingLink"===n.key)}}function w(e,a){if(1&e&&(t.TgZ(0,"div",36),t.YNc(1,J,7,6,"div",39),t.qZA()),2&e){const n=a.$implicit;t.xp6(1),t.Q6J("ngIf",!n.visible&&n.value)}}function k(e,a){if(1&e&&(t.TgZ(0,"p-card",34),t.YNc(1,S,2,1,"p",35),t.TgZ(2,"div",36),t.YNc(3,w,2,1,"div",37),t.qZA()()),2&e){const n=t.oxw(2);t.xp6(1),t.Q6J("ngIf",n.screenWidth>=768),t.xp6(2),t.Q6J("ngForOf",null==n.contactUsDetails?null:n.contactUsDetails.contactUs)}}const M=function(e){return{"hidden-navbar":e}},p=function(e){return{border:e}},T=function(e,a){return[e,a]};function Q(e,a){if(1&e){const n=t.EpF();t.TgZ(0,"section",1)(1,"div",2),t._UZ(2,"em",3)(3,"em",4),t.TgZ(4,"span"),t._uU(5),t.ALo(6,"translate"),t.qZA()(),t.TgZ(7,"div",5)(8,"div",6)(9,"div",7),t._uU(10),t.ALo(11,"translate"),t.qZA()(),t.TgZ(12,"div",8)(13,"p-card",9)(14,"form",10)(15,"div",11)(16,"div",12),t._UZ(17,"input",13),t.TgZ(18,"label",14),t._uU(19),t.ALo(20,"translate"),t.TgZ(21,"span",15),t._uU(22,"*"),t.qZA()()()(),t.TgZ(23,"div",11)(24,"div",12),t._UZ(25,"input",16),t.TgZ(26,"label",17),t._uU(27),t.ALo(28,"translate"),t.TgZ(29,"span",15),t._uU(30,"*"),t.qZA()()()(),t.TgZ(31,"div",11)(32,"div",12)(33,"form",18,19),t._UZ(35,"ngx-intl-tel-input",20),t.qZA(),t.TgZ(36,"label",21),t._uU(37),t.ALo(38,"translate"),t.TgZ(39,"span",15),t._uU(40,"*"),t.qZA()()()(),t.TgZ(41,"div",11)(42,"div",12),t._UZ(43,"input",22),t.TgZ(44,"label",23),t._uU(45),t.ALo(46,"translate"),t.TgZ(47,"span",15),t._uU(48,"*"),t.qZA()()()(),t.TgZ(49,"div",11)(50,"span",24),t._UZ(51,"textarea",25),t.TgZ(52,"div",26),t.YNc(53,P,2,0,"span",27),t.YNc(54,N,2,1,"span",28),t.TgZ(55,"span",29),t._uU(56," /500"),t.qZA()(),t.TgZ(57,"label",30),t._uU(58),t.ALo(59,"translate"),t.TgZ(60,"span",15),t._uU(61,"*"),t.qZA()()()(),t.TgZ(62,"div",11)(63,"button",31),t.NdJ("click",function(){t.CHM(n);const r=t.oxw();return t.KtG(r.submit())}),t._uU(64),t.ALo(65,"translate"),t.qZA()()()(),t._UZ(66,"hr"),t.YNc(67,k,4,2,"p-card",32),t.qZA()()()}if(2&e){const n=t.oxw();t.Q6J("ngClass",t.VKq(49,M,!(null!=n.navbarData&&n.navbarData.isActive))),t.xp6(2),t.Q6J("routerLink","/"),t.xp6(3),t.hij(" ",t.lcZ(6,33,"contactUs.contactUs"),""),t.xp6(5),t.hij(" ",t.lcZ(11,35,"contactUs.contactUsTitle")," "),t.xp6(4),t.Q6J("formGroup",n.contactForm),t.xp6(2),t.Q6J("ngStyle",t.VKq(51,p,n.contactForm.controls.FirstName.touched&&!n.contactForm.controls.FirstName.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.hij("",t.lcZ(20,37,"contactUs.firstName")," "),t.xp6(5),t.Q6J("ngStyle",t.VKq(53,p,n.contactForm.controls.LastName.touched&&!n.contactForm.controls.LastName.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Oqu(t.lcZ(28,39,"contactUs.lastName")),t.xp6(5),t.Q6J("ngStyle",t.VKq(55,p,n.contactForm.controls.MobileNumber.touched&&!n.contactForm.controls.MobileNumber.valid?"1px solid red":"0px solid transparent")),t.xp6(1),t.Q6J("formGroup",n.contactForm),t.xp6(2),t.Q6J("cssClass","custom contact-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",n.phoneInputLength)("numberFormat",n.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",n.preferredCountries)("searchCountryField",t.WLB(57,T,n.SearchCountryField.Iso2,n.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",n.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",n.customPlaceHolder),t.xp6(2),t.Oqu(t.lcZ(38,41,"contactUs.mobileNumber")),t.xp6(5),t.Q6J("ngStyle",t.VKq(60,p,n.contactForm.controls.Email.touched&&!n.contactForm.controls.Email.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Oqu(t.lcZ(46,43,"contactUs.emailAddress")),t.xp6(5),t.Q6J("ngStyle",t.VKq(62,p,n.contactForm.controls.Message.touched&&!n.contactForm.controls.Message.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Q6J("ngIf",!(null!=n.contactForm&&null!=n.contactForm.value&&null!=n.contactForm.value.Message&&n.contactForm.value.Message.length)),t.xp6(1),t.Q6J("ngIf",(null==n.contactForm||null==n.contactForm.value||null==n.contactForm.value.Message?null:n.contactForm.value.Message.length)>0),t.xp6(4),t.Oqu(t.lcZ(59,45,"contactUs.message")),t.xp6(6),t.hij(" ",t.lcZ(65,47,"contactUs.sendMessage")," "),t.xp6(3),t.Q6J("ngIf",null==n.contactUsDetails||null==n.contactUsDetails.contactUs?null:n.contactUsDetails.contactUs.length)}}function X(e,a){if(1&e&&(t.TgZ(0,"p",38),t._uU(1),t.qZA()),2&e){const n=t.oxw(3);t.xp6(1),t.hij(" ",n.contactUsDetails.title," ")}}function Y(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","tel:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function E(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","mailto:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function H(e,a){if(1&e&&(t.TgZ(0,"a",45),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","https://maps.google.com/?q=",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function z(e,a){if(1&e&&(t.TgZ(0,"a",44),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.MGl("href","tel:",n.value,"",t.LSH),t.xp6(1),t.Oqu(n.value)}}function j(e,a){if(1&e&&(t.TgZ(0,"a",45),t._uU(1),t.qZA()),2&e){const n=t.oxw(2).$implicit;t.s9C("href",n.value,t.LSH),t.xp6(1),t.Oqu(n.value)}}function V(e,a){if(1&e&&(t.TgZ(0,"div",40),t._UZ(1,"img",41),t.YNc(2,Y,2,2,"a",42),t.YNc(3,E,2,2,"a",42),t.YNc(4,H,2,2,"a",43),t.YNc(5,z,2,2,"a",42),t.YNc(6,j,2,2,"a",43),t.qZA()),2&e){const n=t.oxw().$implicit;t.xp6(1),t.Q6J("src","data:image/png;base64, "+n.image,t.LSH),t.xp6(1),t.Q6J("ngIf","MobileNumber"===n.key),t.xp6(1),t.Q6J("ngIf","Email"===n.key),t.xp6(1),t.Q6J("ngIf","Location"===n.key),t.xp6(1),t.Q6J("ngIf","WhatsAppNumber"===n.key),t.xp6(1),t.Q6J("ngIf","ChattingLink"===n.key)}}function K(e,a){if(1&e&&(t.TgZ(0,"div",36),t.YNc(1,V,7,6,"div",39),t.qZA()),2&e){const n=a.$implicit;t.xp6(1),t.Q6J("ngIf",!n.visible&&n.value)}}function B(e,a){if(1&e&&(t.TgZ(0,"p-card",49),t.YNc(1,X,2,1,"p",35),t.TgZ(2,"div",36),t.YNc(3,K,2,1,"div",37),t.qZA()()),2&e){const n=t.oxw(2);t.xp6(1),t.Q6J("ngIf",n.screenWidth>=768),t.xp6(2),t.Q6J("ngForOf",null==n.contactUsDetails?null:n.contactUsDetails.contactUs)}}function G(e,a){1&e&&(t.TgZ(0,"span"),t._uU(1,"0 "),t.qZA())}function W(e,a){if(1&e&&(t.TgZ(0,"span",33),t._uU(1),t.qZA()),2&e){const n=t.oxw(3);t.xp6(1),t.hij("",n.contactForm.value.Message.length," ")}}function $(e,a){if(1&e){const n=t.EpF();t.TgZ(0,"p-card",9)(1,"form",10)(2,"div",11)(3,"div",12),t._UZ(4,"input",13),t.TgZ(5,"label",14),t._uU(6),t.ALo(7,"translate"),t.TgZ(8,"span",15),t._uU(9,"*"),t.qZA()()()(),t.TgZ(10,"div",11)(11,"div",12),t._UZ(12,"input",16),t.TgZ(13,"label",17),t._uU(14),t.ALo(15,"translate"),t.TgZ(16,"span",15),t._uU(17,"*"),t.qZA()()()(),t.TgZ(18,"div",11)(19,"div",50)(20,"form",18,19),t._UZ(22,"ngx-intl-tel-input",20),t.qZA(),t.TgZ(23,"label",21),t._uU(24),t.ALo(25,"translate"),t.TgZ(26,"span",15),t._uU(27,"*"),t.qZA()()()(),t.TgZ(28,"div",11)(29,"div",12),t._UZ(30,"input",22),t.TgZ(31,"label",23),t._uU(32),t.ALo(33,"translate"),t.TgZ(34,"span",15),t._uU(35,"*"),t.qZA()()()(),t.TgZ(36,"div",11)(37,"span",24),t._UZ(38,"textarea",25),t.TgZ(39,"div",26),t.YNc(40,G,2,0,"span",27),t.YNc(41,W,2,1,"span",28),t.TgZ(42,"span",29),t._uU(43," /500"),t.qZA()(),t.TgZ(44,"label",30),t._uU(45),t.ALo(46,"translate"),t.TgZ(47,"span",15),t._uU(48,"*"),t.qZA()()()(),t.TgZ(49,"div",11)(50,"button",51),t.NdJ("click",function(){t.CHM(n);const r=t.oxw(2);return t.KtG(r.submit())}),t._uU(51),t.ALo(52,"translate"),t.qZA()()()()}if(2&e){const n=t.oxw(2);t.xp6(1),t.Q6J("formGroup",n.contactForm),t.xp6(2),t.Q6J("ngStyle",t.VKq(40,p,n.contactForm.controls.FirstName.touched&&!n.contactForm.controls.FirstName.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.hij("",t.lcZ(7,28,"contactUs.firstName")," "),t.xp6(5),t.Q6J("ngStyle",t.VKq(42,p,n.contactForm.controls.LastName.touched&&!n.contactForm.controls.LastName.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Oqu(t.lcZ(15,30,"contactUs.lastName")),t.xp6(5),t.Q6J("ngStyle",t.VKq(44,p,n.contactForm.controls.MobileNumber.touched&&!n.contactForm.controls.MobileNumber.valid?"1px solid red":"0px solid transparent")),t.xp6(1),t.Q6J("formGroup",n.contactForm),t.xp6(2),t.Q6J("cssClass","custom contact-input-phone")("enableAutoCountrySelect",!0)("enablePlaceholder",!0)("maxLength",n.phoneInputLength)("numberFormat",n.PhoneNumberFormat.National)("phoneValidation",!1)("preferredCountries",n.preferredCountries)("searchCountryField",t.WLB(46,T,n.SearchCountryField.Iso2,n.SearchCountryField.Name))("searchCountryFlag",!0)("selectFirstCountry",!1)("selectedCountryISO",n.CustomCountryISO)("separateDialCode",!0)("customPlaceholder",n.customPlaceHolder),t.xp6(2),t.Oqu(t.lcZ(25,32,"contactUs.mobileNumber")),t.xp6(5),t.Q6J("ngStyle",t.VKq(49,p,n.contactForm.controls.Email.touched&&!n.contactForm.controls.Email.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Oqu(t.lcZ(33,34,"contactUs.emailAddress")),t.xp6(5),t.Q6J("ngStyle",t.VKq(51,p,n.contactForm.controls.Message.touched&&!n.contactForm.controls.Message.valid?"1px solid red":"0px solid transparent")),t.xp6(3),t.Q6J("ngIf",!(null!=n.contactForm&&null!=n.contactForm.value&&null!=n.contactForm.value.Message&&n.contactForm.value.Message.length)),t.xp6(1),t.Q6J("ngIf",(null==n.contactForm||null==n.contactForm.value||null==n.contactForm.value.Message?null:n.contactForm.value.Message.length)>0),t.xp6(4),t.Oqu(t.lcZ(46,36,"contactUs.message")),t.xp6(6),t.hij(" ",t.lcZ(52,38,"contactUs.sendMessage")," ")}}function R(e,a){if(1&e&&(t.TgZ(0,"section",1)(1,"div",2),t._UZ(2,"em",3)(3,"em",4),t.TgZ(4,"span"),t._uU(5),t.ALo(6,"translate"),t.qZA()(),t.TgZ(7,"div",5)(8,"div",8),t.YNc(9,B,4,2,"p-card",46),t._UZ(10,"hr"),t.TgZ(11,"div",47),t._uU(12),t.ALo(13,"translate"),t.qZA(),t.YNc(14,$,53,53,"p-card",48),t.qZA()()()),2&e){const n=t.oxw();t.Q6J("ngClass",t.VKq(10,M,null==n.navbarData?null:n.navbarData.isActive)),t.xp6(2),t.Q6J("routerLink","/"),t.xp6(3),t.hij(" ",t.lcZ(6,6,"contactUs.contactUsMobileTemplate"),""),t.xp6(4),t.Q6J("ngIf",n.screenWidth<768&&(null==n.contactUsDetails||null==n.contactUsDetails.contactUs?null:n.contactUsDetails.contactUs.length)),t.xp6(3),t.hij(" ",t.lcZ(13,8,"contactUs.contactUsTitle")," "),t.xp6(2),t.Q6J("ngIf",n.screenWidth<768)}}const tt=[{path:"",component:(()=>{class e{contactUsService;translate;messageService;formService;userService;appDataService;$gtmService;contactUsDetails={};contactForm;screenWidth=window.innerWidth;userLoggedIn=!1;SearchCountryField=m.wX;CustomCountryISO;PhoneNumberFormat=m.M9;preferredCountries=[m.HT.UnitedStates,m.HT.UnitedKingdom];separateDialCode=!1;userData;customPlaceHolder="";phoneInputLength=12;navbarData;constructor(n,o,r,O,v,at,ct){this.contactUsService=n,this.translate=o,this.messageService=r,this.formService=O,this.userService=v,this.appDataService=at,this.$gtmService=ct,this.contactForm=this.formService.form;let u=localStorage.getItem("tenantId");u&&""!==u&&("1"==u||"2"==u||"3"==u?this.customPlaceHolder="XXXXXXXXX":"4"==u&&(this.customPlaceHolder="XXXXXXXXXX"))}ngOnInit(){if(this.navbarData=this.appDataService.layoutTemplate.find(n=>"navbar"===n.type),this.$gtmService.pushPageView("contact us"),this.CustomCountryISO=localStorage.getItem("isoCode"),this.userService.updateScrollTop(!0),this.userData=localStorage.getItem("profile"),this.userAssign(),this.getContactUsDetails(),this.appDataService.configuration){const n=this.appDataService.configuration.records.find(o=>"PhoneLength"===o.key);n&&(this.phoneInputLength=parseInt(n.value))}}userAssign(){if(this.userData){this.userLoggedIn=!0;const n={};this.userData=JSON.parse(this.userData);let o=this.userData.name.split(" ");o.length&&(o[0]&&(n.FirstName=o[0]),o[1]&&(n.LastName=o[1])),this.userData.mobileNumber&&(n.MobileNumber=this.userData.mobileNumber.substring(3)),this.userData.email&&(n.Email=this.userData.email),this.contactForm.patchValue(n)}}onResize(n){this.screenWidth=n.target.innerWidth}getContactUsDetails(){this.contactUsService.getShopContactUs().subscribe({next:n=>{this.contactUsDetails=n.data,n.success||this.messageService.add({severity:"error",summary:this.translate.instant("ErrorMessages.fetchError"),detail:n.message})},error:n=>{}})}submit(){if(this.formService.markTouched(this.contactForm),this.contactForm.valid){this.contactForm.controls.MobileNumber.setValue(this.contactForm.controls.MobileNumber.value?.e164Number);const n=new h.LE({fromObject:this.formService.Dto()});this.contactUsService.submitContactForm(n).subscribe({next:o=>{o.success?(this.userLoggedIn||(this.contactForm.controls.FirstName.reset(),this.contactForm.controls.LastName.reset(),this.contactForm.controls.MobileNumber.reset(),this.contactForm.controls.Email.reset()),this.contactForm.controls.Message.reset(),this.messageService.add({severity:"success",detail:this.translate.instant("ResponseMessages.contactUsSuccessMessage")})):this.messageService.add({severity:"error",detail:this.translate.instant("ErrorMessages.contactUsErrorMessage")})},error:o=>{this.messageService.add({severity:"error",detail:this.translate.instant("ErrorMessages.contactUsErrorMessage")})}})}}static \u0275fac=function(o){return new(o||e)(t.Y36(x.Dr),t.Y36(d.sK),t.Y36(l.ez),t.Y36(C),t.Y36(x.KD),t.Y36(x.UW),t.Y36(y.J))};static \u0275cmp=t.Xpm({type:e,selectors:[["app-ContactUsComponent"]],hostBindings:function(o,r){1&o&&t.NdJ("resize",function(v){return r.onResize(v)},!1,t.Jf7)},inputs:{contactUsDetails:"contactUsDetails"},decls:2,vars:2,consts:[["class","contact-us-page",3,"ngClass",4,"ngIf"],[1,"contact-us-page",3,"ngClass"],[1,"breadcrumb-address","d-flex"],["aria-hidden","true","tabindex","0",1,"pi","pi-home","cursor-pointer",3,"routerLink"],[1,"pi","pi-angle-right"],[1,"content-container","contact-btn"],[1,"row","mt-1"],[1,"font-size-24","bold-font","contact-us-title"],[1,"d-flex","contact-card"],[1,"mt-3","mb-5"],[1,"grid",3,"formGroup"],[1,"col-12"],[1,"p-float-label","w-full",3,"ngStyle"],["formControlName","FirstName","id","fname","pInputText","","type","text",1,"form-input","p-float-label","w-full","contact-input"],["for","fname",1,"contact-label"],[1,"red-asterisk"],["formControlName","LastName","id","lname","pInputText","","type","text",1,"form-input","p-float-label","w-full","contact-input"],["for","lname",1,"contact-label"],[3,"formGroup"],["f","ngForm"],["formControlName","MobileNumber","name","phone",3,"cssClass","enableAutoCountrySelect","enablePlaceholder","maxLength","numberFormat","phoneValidation","preferredCountries","searchCountryField","searchCountryFlag","selectFirstCountry","selectedCountryISO","separateDialCode","customPlaceholder"],["for","mobileNumber",1,"contact-label"],["formControlName","Email","id","emailAddress","pInputText","","type","text",1,"form-input","p-float-label","w-full","contact-input"],["for","emailAddress",1,"contact-label","email-label"],[1,"p-float-label",2,"border","0px solid transparent","background-color","rgb(245, 245, 245) !important","height","136px !important",3,"ngStyle"],["formControlName","Message","id","message","maxlength","500","pInputTextarea","","rows","8","type","text",1,"form-input","p-float-label","w-full","contact-text-area","contact-textarea"],[1,"the-count"],[4,"ngIf"],["id","current",4,"ngIf"],["id","maximum"],["for","message",1,"contact-label",2,"top","0 !important"],["pButton","","type","button",1,"p-field","p-col-12","my-2","font-size-14","second-btn","d-block","text-center","col-3","contactUs-btn",3,"click"],["class","desktop mt-3",4,"ngIf"],["id","current"],[1,"desktop","mt-3"],["class","col-12 contact-detail-2",4,"ngIf"],[1,"p-field","p-col-12","contact-details-form"],["class","p-field p-col-12 contact-details-form",4,"ngFor","ngForOf"],[1,"col-12","contact-detail-2"],["class","col-12 col-md-4 w-full address-text",4,"ngIf"],[1,"col-12","col-md-4","w-full","address-text"],["alt","No Image","width","16","height","16",1,"phone-img","ng-star-inserted",3,"src"],[3,"href",4,"ngIf"],["target","_blank",3,"href",4,"ngIf"],[3,"href"],["target","_blank",3,"href"],["class","desktop pad mt-3",4,"ngIf"],[1,"bold-font","font-size-24","contact-us-title"],["class","mt-3 mb-5",4,"ngIf"],[1,"desktop","pad","mt-3"],[1,"p-float-label","w-full","z-0",3,"ngStyle"],["pButton","","type","button",1,"p-field","p-col-12","font-size-14","second-btn","d-block","text-center","col-3","contactUs-btn",3,"click"]],template:function(o,r){1&o&&(t.YNc(0,Q,68,64,"section",0),t.YNc(1,R,15,12,"section",0)),2&o&&(t.Q6J("ngIf",r.contactUsDetails&&r.screenWidth>767),t.xp6(1),t.Q6J("ngIf",r.contactUsDetails&&r.screenWidth<768))},dependencies:[s.mk,s.sg,s.O5,s.PC,_.rH,U.Z,Z.o,i._Y,i.Fj,i.JJ,i.JL,i.nD,i.sg,i.u,m.FV,m.mh,d.X$],styles:["textarea[_ngcontent-%COMP%]{box-sizing:border-box;resize:none;width:100%}.desktop[_ngcontent-%COMP%]{margin-left:12px;width:44%}.content-container[_ngcontent-%COMP%]{background:#f5f5f5;justify-content:center;display:grid}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{margin-right:40px}@media (max-width: 1366px){.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]{flex-direction:column!important}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{margin-right:0}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child   .contact-text-area[_ngcontent-%COMP%]{margin:1px}}@media (max-width: 420px){.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .mb-5[_ngcontent-%COMP%]{margin-bottom:50px}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:last-child{width:100%}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .desktop[_ngcontent-%COMP%]{margin-top:20px;margin-left:0;width:100%;margin-bottom:10px}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{padding-left:1rem!important;padding-right:1rem!important}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .mbl-btn[_ngcontent-%COMP%]{display:flex;justify-content:center}.content-container[_ngcontent-%COMP%]   .contact-card[_ngcontent-%COMP%]   .contact-details-form[_ngcontent-%COMP%]{margin-bottom:0!important}}.contact-input[_ngcontent-%COMP%]{height:60px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;padding-top:20px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px}.contact-label[_ngcontent-%COMP%]{position:absolute;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:16px!important;font-weight:500;color:#323232;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.contact-textarea[_ngcontent-%COMP%]{height:109px!important;width:100%;border-radius:5px 5px 0 0;opacity:1;border:none!important;border-bottom:1px solid #b9b9b9!important;padding-left:10px;padding-right:10px;background-color:#f5f5f5!important;font-family:var(--medium-font)!important;font-size:16px;position:absolute;top:25px;padding-top:0}textarea#message[_ngcontent-%COMP%]{outline:none!important}.p-card.p-component[_ngcontent-%COMP%]{border-radius:5px;padding:15px;background:#ffffff;border:1px solid rgba(229,229,229,.9019607843);padding:0!important}.phone-img[_ngcontent-%COMP%]{margin-left:4px;margin-right:7px;float:left;width:20px;height:20px;margin-bottom:10px}.address-text[_ngcontent-%COMP%]{font-size:16px;color:#383843!important;font-weight:400;font-family:var(--medium-font)!important;margin-bottom:0}.breadcrumb-address.d-flex[_ngcontent-%COMP%]{font-family:var(--medium-font)!important;font-weight:500;cursor:pointer;background-color:#f5f5f5;padding:1rem 2rem;font-size:15px;gap:10px}i.fa.fa-angle-left[_ngcontent-%COMP%]{padding:0 10px;margin:auto 0}.contact-detail-2[_ngcontent-%COMP%]{color:#3d3d48;font-family:var(--medium-font)!important;font-weight:700;font-size:24px}.contactUs-btn[_ngcontent-%COMP%]{padding:7px;margin-top:10px}@media screen and (max-width: 768px){.contactUs-btn[_ngcontent-%COMP%]{width:100%;border-radius:3px}}.email-label[_ngcontent-%COMP%]{text-transform:capitalize}@media screen and (max-width: 768px){.contact-us-page[_ngcontent-%COMP%]{margin:25% 20px 20px}button.p-element.p-field.p-col-12.my-2.font-size-14.second-btn.d-block.text-center.col-3.p-button.p-component[_ngcontent-%COMP%]{width:100%}.content-container[_ngcontent-%COMP%]{padding-left:0!important;padding-right:0!important}.mb-5.mt-3.p-element[_ngcontent-%COMP%]{margin-bottom:0!important}.red-asterisk[_ngcontent-%COMP%]{color:#ee5858}.desktop[_ngcontent-%COMP%]{margin-left:12px;width:98%;margin-bottom:30px}.content-container.contact-btn[_ngcontent-%COMP%], .breadcrumb-address.d-flex[_ngcontent-%COMP%]{background-color:#fff}.contact-us-title[_ngcontent-%COMP%]{font-size:20px!important}.hidden-navbar[_ngcontent-%COMP%]{margin-top:35px!important}}@media screen and (max-width: 768px) and (max-width: 700px){.hidden-navbar[_ngcontent-%COMP%]{margin-top:65px!important}}@media screen and (max-width: 768px) and (max-width: 575px){.hidden-navbar[_ngcontent-%COMP%]{margin-top:93px!important}}textarea[_ngcontent-%COMP%]:focus ~ label[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%] ~ label[_ngcontent-%COMP%]{top:-.75em!important}.the-count[_ngcontent-%COMP%]{padding:.1rem 0 0;font-size:.875rem;display:flex;position:absolute;right:20px;top:135px}a[_ngcontent-%COMP%]:hover{color:#00f!important}  .contact-card .iti__selected-flag{pointer-events:none!important}.address-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#000!important;white-space:normal;word-break:break-word;max-width:80%}"]})}return e})()}];var nt=c(9663),et=c(1312);let ot=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=t.oAB({type:e});static \u0275inj=t.cJS({imports:[s.ez,_.Bz.forChild(tt),f.T,U.d,Z.j,nt.zz,i.u5,et.S,i.UX,d.aw,m.J7]})}return e})()},3714:(F,g,c)=>{c.d(g,{j:()=>m,o:()=>h});var s=c(5879),_=c(6814),f=c(6223);let h=(()=>{class t{el;ngModel;cd;filled;constructor(d,l,i){this.el=d,this.ngModel=l,this.cd=i}ngAfterViewInit(){this.updateFilledState(),this.cd.detectChanges()}ngDoCheck(){this.updateFilledState()}onInput(){this.updateFilledState()}updateFilledState(){this.filled=this.el.nativeElement.value&&this.el.nativeElement.value.length||this.ngModel&&this.ngModel.model}static \u0275fac=function(l){return new(l||t)(s.Y36(s.SBq),s.Y36(f.On,8),s.Y36(s.sBO))};static \u0275dir=s.lG2({type:t,selectors:[["","pInputText",""]],hostAttrs:[1,"p-inputtext","p-component","p-element"],hostVars:2,hostBindings:function(l,i){1&l&&s.NdJ("input",function(C){return i.onInput(C)}),2&l&&s.ekj("p-filled",i.filled)}})}return t})(),m=(()=>{class t{static \u0275fac=function(l){return new(l||t)};static \u0275mod=s.oAB({type:t});static \u0275inj=s.cJS({imports:[_.ez]})}return t})()}}]);