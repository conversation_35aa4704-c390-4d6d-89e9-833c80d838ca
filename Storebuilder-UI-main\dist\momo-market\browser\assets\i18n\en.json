{"account": {"address": {"addAddress": "Add Address", "addAddressMessage": "Address has been successfully added", "building": "Apt, Suite, Unit, building (optional)", "city": "City", "country": "Country", "defaultAddress": "Set as <PERSON><PERSON><PERSON>", "first-name": "First name", "instructions": "Delivery instructions (optional)", "landMark": "Nearest Landmark", "last-name": "Last name", "optional": "(optional)", "phone": "Phone number", "post-code": "PostCode", "search": "Type your address", "state": "State", "street": "Street address or P.O. Box", "updateAddress": "CONFIRM ADDRESS"}, "details": {"change": "Change", "default": "<PERSON><PERSON><PERSON>", "Deliveryoption": "Delivery Option", "email": "Email Address", "firstName": "First Name", "lastName": "Last Name", "newSecondaryPhone": "New mobile number", "phoneNumber": "Mobile Number", "secondaryPhoneNumber": "Other Phone Number", "secretCode": "SecretCode", "shippingDetails": "Shipping details", "yourDetails": "My Profile", "requestReturn": "Request return", "addPhoneNumber": "Add mobile number"}, "index": {"logout": "Logout", "yourAccount": "My Account", "yourAddresses": "My Addresses", "yourDetails": "Profile Settings", "yourOrders": "My Orders", "profile": "My Profile", "lang": "Language", "country": "Country", "language": "English", "myAccount": "My Account", "myAddresses": "My Addresses"}, "optInMessage": "You have subscribed our newsletter.", "optOutMessage": "You have unsubscribed from our newsletter."}, "addingAddress": {"RecipientContactNumber": "Recipient Contact Number", "phoneNote": "This is the number the delivery person will use to contact the recipient at the time of delivery.", "addAddress": "Add Address", "addNewAddress": "Add new address", "confirmAddress": "Confirm Address", "addressLabel": "Address label", "building": "Apt, Suite, Unit, building (optional)", "city": "City", "country": "Country", "defaultAddress": "Set as <PERSON><PERSON><PERSON>", "first-name": "First name", "instructions": "Delivery Instructions", "landMark": "Nearest Landmark", "last-name": "Last name", "optional": "(optional)", "otherAddress": "Address line 1", "otherAddressSecond": "Address line 2", "phone": "Phone number", "post-code": "PostCode", "search": "Type your address", "state": "State", "street": "Street address or P.O. Box", "updateAddress": "CONFIRM ADDRESS", "updateAddressTitle": "Update Address", "region": "Region", "addressDetails": "Address details"}, "addressModal": {"addAddress": "ADD NEW ADDRESS", "default": "<PERSON><PERSON><PERSON>", "yourAddresses": "Your addresses", "updateAddresses": "Update address", "shippingAddress": "Your Shipping Address", "confirmAddress": "Confirm Address"}, "addressOtherModal": {"addressLabel": "Address label", "labelName": "Label name", "add": "Add", "cancel": "Cancel"}, "auth": {"otp": {"next": "Next", "resend": "Resend", "resendIn": "Resend in", "subTitle": "We just sent the verification code", "title": "Enter OTP"}, "registerPassword": {"referralBy": "Referred By", "referralByErrors": "Maximum Value should Less Than 50 chars", "agreeText": "I am agreeing to the", "confirmPassword": "Confirm Password", "continue": "Continue", "signUp": "Sign-up", "email": "Email", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "phoneNumber": "Phone number", "subTitle": "Find everything you love on Market by MoMo", "termsAndConditions": "Terms and conditions", "title": "Sign-up", "validation1": "At least 10 characters in length", "validation2": "Lower case letter (a-z)", "validation3": "Upper case letter (A-Z)", "validation4": "At least one number (0-9)", "validation5": "At least one special character (!@#$%^&*()-_=+[]{};:,./?)", "validation6": "Email must be a valid email address", "validation7": "The password mismatched", "validation8": "Invalid password! Please choose a stronger password", "signup": "Sign up", "signin": "Sign-in", "desc": "Find everything you love on Market by MoMo", "haveAccount": "Already have an account?", "registerSuccessMsg": "Your account has been successfully created", "registerSuccessMsgUghanda": "Welcome aboard! Use code WELCOMEUG to get 50% off up to UGX 5,000 on your first order.", "RegistrationSuccessful": "🎉 Registration Successful! ", "okButton": "Start Shopping!", "termsAndConds": "MoMo’s Conditions of Use and Privacy Notice.", "agreementText": "By continuing, you agree to", "backToLogin": "Back to login"}, "optInCheckBox": "Get exclusive deals, new arrivals, and insider tips straight to your inbox! Subscribe to our newsletter and never miss a great offer."}, "buttons": {"addreview": "Add", "addToCart": "Add to cart", "close": "Close", "readLess": "Show Less", "readMore": "Show more", "shopNow": "Buy Now", "viewAll": "View all", "back": "Back"}, "cart": {"cartDetail": {"cantDeliverLocationMessage": "*Can't deliver to your location", "cantDeliverMessage": "*Can't deliver this product to your address", "delete": "Delete", "moveWishList": "Move to WishList", "notAvailable": "NOT AVAILABLE", "outOfStock": "This product is out of stock", "Qty": "Qty", "color": "Color", "size": "Size", "size2": "Size2"}, "emptyCart": {"cartEmpty": "Your cart is empty", "cartEmptyMessage": "Add items to get started", "startShopping": "Let's start shopping", "checkSpelling": "- Check your spelling for typing errors", "emptyCategories": "Your category is empty", "noResultFound": "No results found for", "searchGeneralTerms": "- Try searching more general terms - you can then filter the search results", "shortWords": "- Try searching with short and simple keywords", "whatAreYouWaitingFor": "What are you waiting for", "goToHome": " Go to Homepage", "addItems": "Add items to get started"}, "index": {"cartWarning": "Placing a product on the cart does not reserve that product or price. We only reserve the product once payment is received", "continueShopping": "Continue shopping", "price": "PRICE", "products": "PRODUCTS", "quantity": "QUANTITY", "v2": {"cart": "<PERSON><PERSON>"}, "yourCart": "Your cart"}}, "categories": {"allCategories": "All Categories", "categoryNotFound": "Category is not found", "productNotFound": "No product found", "goToHomePage": "Go to Home Page"}, "categoryCard": {"items": "items", "products": "Products"}, "changePassword": {"changePassword": "Change Password", "confirmPassword": "Confirm Password", "newPassword": "New Password", "oldPassword": "Old Password", "phoneNumber": "Phone number", "securityPolicy": "For security policy , You don't change your password from 90 days ,please update it"}, "checkout": {"noItemsInCart": "Some Products Aren`t Availble", "checkoutLabel": "Checkout", "ShippingAddress": "Shipping Address", "DeliveryOption": "Delivery Option", "deliveryMethod": {"area": "Area", "change": "Change", "checkout": "Checkout", "city": "City", "default": "<PERSON><PERSON><PERSON>", "deliverOption": "Delivery option", "deliveryMethod": "Please select your preferred delivery method", "fromTo": " From 2 to 3 days", "location": "Location", "mobileNumber": "Mobile number", "nextDay": "Next day", "sameDay": "Same day", "shipping": "Shipping", "shippingAddress": "Shipping address", "contactNo": "Contact Number", "infoMessage": "This number will be used by the shipper to contact you", "paymentMethod": "Payment Option", "paymentOption": "Payment Option", "paymentOptionDesktop": "Payment Option", "momoPayLabel": "MoMo Pay", "cardLabel": "Debit or Credit Cards"}, "index": {"deliveryMethod": "Delivery Method", "orderSummary": "Order Summary"}, "item": "item", "items": "items", "orderPlaced": {"deliveryoption": "Delivery option", "item": "<PERSON><PERSON>", "myOrders": "My orders", "orderPlaced": "Your order has been placed successfully", "paymentMethod": "Payment Method", "paymentTotal": "Total", "shipping": "Shipping", "thanksForOrdering": "Thanks for shopping on MoMoMarket, here is the summary of your order", "thanksForOrderingMomo": "Thanks for shopping on MoMoMarket, here is the summary of your order number", "thanksForOrderingYalla": "Thanks for shopping on Yalla Super Mall, here is the summary of your order", "totalItemCost": "Total Item Cost", "vat": "VAT", "mobilHeader": "Your order has been placed successfully", "mobilP": "Thank you for Shopping on Market by MoMo , here is the summary of your order number", "total": "Sub total", "shippingFee": "Shipping fees", "discount": "Discount", "totalMoney": "Total", "payment": "Payment Method", "delivery": "Delivery option"}, "orderSummary": {"delivery": "Delivery", "deliveryOption": "Delivery option", "item": "Total Items price", "subTotal": "Sub total", "shippingFees": "Shipping fees", "orderPlaced": "Order placed", "paymentMethod": "Payment Method", "paymentTotal": "Payment Total", "shipping": "Shipping", "thanksForOrdering": "Thanks for shopping on MoMoMarket, here is the summary of your order", "total": "Total", "totalItemCost": "Total Item Cost", "usuallyDeliveredWithin": "Usually delivered within 2 working days", "vat": "VAT", "discount": "Discount"}, "paymentCart": {"arrives": " Estimated Delivery Date", "proceed": "Proceed to Payment", "PayNow": "Pay Now", "loadingMessage": "Please wait your transaction is processing"}, "paymentDialog": {"123": "123", "12345678": "12345678", "afriElec": "AfriElec", "allInformationsSafe": "All your informations is safe and secured", "cardholderName": "Cardholder name", "cardNumber": "Card Number", "dontHaveMomo": "Don't have <PERSON><PERSON><PERSON> yet", "howItWorks": "How it works", "merchantName": "Merchant name", "merchantRef": "Merchant ref", "mm/yy": "MM/YY", "paymentTotal": "Payment Total", "payWith": "Pay with", "phoneNumber": "Phone number", "saveCardForLater": "Save card for later", "sign-upNow": "Sign-up now", "youNeedToHaveMomo": "You need to have a MoMo account in order to proceed. We'll send you a request via the mobile app and USSD."}, "proceedToCheckout": {"itemsCost": "Items price", "multipleItems": "Items", "orderSummary": "Order summary", "proceedTo": "Proceed to checkout", "singleItem": "<PERSON><PERSON>", "vat": "VAT"}}, "contactUs": {"contactDetails": "Contact details", "contactUs": "Contact Us", "contactUsTitle": "Contact Us", "contactUsMobileTemplate": "Contact our team", "emailAddress": "Email address", "email": "Email", "firstName": "First name", "getInTouchWith": "Get in touch with", "lastName": "Last name", "message": "Message", "mobileNumber": "Mobile Number", "sendMessage": "Send message", "getInTouchWithUs": "Get in touch with us"}, "deleteCart": {"cancel": "Cancel", "delete": "Delete", "sureMesg": "Are you sure you want to delete this item?"}, "optOutModal": {"close": "Close", "message": "Some items in your cart have exited the promotion prices", "cartMessage": "the items in your cart have exited the promotion prices"}, "cpl": {"first": "You can buy up to", "second": "items at the discounted price.", "third": "Extra items will be at the regular price.", "fourth": "Please review your cart before proceeding."}, "mobileCartModal": {"cancel": "Cancel", "move": "Move To Wishlist", "removeFromCart": "Remove from cart", "delete": "delete", "suremovewishlist": "Are you sure you want to move this item to wishlist?", "sureRemoveCart": "Are you sure you want to remove this item from the cart?"}, "deleteItemPopupComponent": {"?": "?", "cancel": "Cancel", "delete": "Delete", "deleteAddress": "Are you sure you want to delete this address", "deleteNumber": "Are you sure you want to delete this mobile number", "deleteSelected": "Do you want to delete selected", "yesDelete": "Yes, delete"}, "ErrorMessages": {"contactUsErrorMessage": "Veuillez réessayer plus tard.", "errorWhileCancelYourOrder": "error while cancel your order, please try again.", "errorWhileTheRefundRequestProcess": "error while the refund request process, please try again.", "exceedOperations": "Exceed number of operation per hour", "fetchError": "<PERSON><PERSON>r", "InsuficientQuantity": "There is no more items for this product.", "invalidOtp": "Invalid OTP", "invalidUserNameOrPassword": "Invalid User name or password", "loginExpired": "Login Expired", "mobileNumberAlreadyRegisteredBefore": "Mobile number is already registered", "mobileRequired": "Mobile Required", "emailRequired": "Email Required", "mustEnterPassword": "Must Enter password", "mustSelectCancelationReason": "Must Select Cancelation Reason", "noProducts": "There is no product", "orderCancellationFailed": "Order Cancellation Failed", "orderRefundRequestFailed": "Order refund request Failed", "outOfDeliveryArea": "Out of delivery area", "phoneNumberIsUnvalid": "Phone Number Is Invalid", "pleaseContactCallCenter": "Your order cannot be submitted please contact call center", "pleaseEnterValidEmailAddress": "Please Enter <PERSON><PERSON> Email Address", "pleaseEnterValidReview": "Please Enter A valid Review", "pleaseLoginAgain": "Login Expired,Please login again", "pleaseLoginFirst.": "Unauthrized Action, please login first.", "unauthrizedAction": "Unauthrized Action", "addressIsRequired": "Street Address is required", "errorReadingImage": "Error reading image file", "mustSelectReturnReason": "Must Select Return Reason", "mustUploadItemImage": "Must upload item image", "mustUploadItemImageSize": "Image/Video retched max size ", "errorWhileReturnYourOrder": "Error while Return your order, please try again.", "orderReturnedFailed": "Order Returned Failed", "itemPerCustomerError": "Cannot add products more than ", "itemPerCustomerErrorNext": " products", "uploadItemFailed": "Upload item failed", "itemUploadAllowed": "Only PNG, JPG, or MP4 files are allowed."}, "featureType": {"bestseller": "Best Seller", "categories": "Categories", "hotdeals": "Hot Deals", "promochezmoiestzo": "Global Products", "newarrival": "New Arrival"}, "floationPanel": {"addToCart": "Add to cart", "buyNow": "Buy Now", "notifyMe": "NOTIFY ME"}, "footer": {"copyRightsMobile": "All rights reserved for Group FinCommerce Inc. in the Republic of South Africa", "aboutPaysky": "About Yalla", "aboutUs": "About us", "account": "Account", "backToTop": "Top", "career": "Careers", "company": "Company", "conditionsOfUse": "Conditions of use", "contactUs": "Contact us", "getSellerMobileApp": "Get seller mobile app", "help": "Help", "myAddress": "My Address", "myDetails": "My Profile", "myOrders": "My Orders", "privacyPolicy": "Privacy policy", "rightsReserved": "© 2023 MoMoMarket. All rights reserved.", "scRightsReserved": "© 2023,Yalla.online", "sellerHub": "Seller hub", "sellOnMarketplace": "Sell on Marketplace", "sellOnMoMoButton": "Sell on Marketplace", "sellOnMomoMarket": "Sell On MomoMarket", "shippingAndReturn": "Shipping & returns", "termsAndConditions": "Terms & conditions", "weAccept": " We Accept", "careers": "Careers", "becomeSeller": " Become a Seller", "contactWithUs": "Contact With Us", "reachOut": "Reach out", "returnAndRefund": "Return & Refund", "disputeResolution": "Dispute Resolution", "warrantyPolicy": "Warranty Policy", "faq": "FAQ", "NewsletterPreferences": "Newsletter Preferences"}, "header": {"cart": "<PERSON><PERSON>", "deliverTo": "Deliver to", "hi": "Hi", "login": "Login / Signup", "logout": "Log Out", "signIn": "Sign-In", "signup": "Sign Up", "wishList": "WishList", "yourAddresses": "My Addresses", "yourDetails": "My Account", "yourOrders": "My Orders", "myAddresses": "My Addresses"}, "landing": {"BestSeller": "Best Seller", "browseAllProduct": "Browse All Product", "HotDeals": "Hot Deals", "PromoChezMoiEstZO": "Global Products", "NewArrival": "New Arrival", "seeMore": "See more", "seeAll": "See all"}, "landingNavbar": {"liveMerchants": "Live Merchants", "sellOnMarketplace": "Sell on marketplace", "sellOnYallaMall": "Sell on Yalla Mall"}, "landingPage": {"clickOnCountry": "Click on your country name to enter Momo Marketplace", "searchCountry": "Search country", "selectCountry": "Select country", "noCountryFound": "No country found"}, "landingPageSearch": {"brands": "Brands", "clear": "Clear", "colors": "Colors", "price": "Price", "refineBy": "Refine by", "searchFilter": "Search", "searchFor": "Search for ", "shortBy": "Sort by :"}, "language": {"English": "English", "French": "French"}, "merchant": {"merchants": "Merchants", "searchMerchant": "Search merchant"}, "messageModal": {"pleaseSignInAgain": " please, sign in again"}, "mobileModal": {"addNumber": "Add number", "default": "<PERSON><PERSON><PERSON>", "yourMobileNumber": "Your Mobile numbers"}, "multipleAddress": {"address": "Address", "confirmDelete": "Are you want to delete this addrees", "default": "<PERSON><PERSON><PERSON>", "other": "Other", "yourAddresses": "Your addresses", "myAddresses": "My Addresses", "Addresses": "Addresses", "selectAddress": "Select address", "confirmAddress": "Confirm Address", "completeAddress": "Complete address details"}, "navbar": {"all": "All", "home": "home", "Wishlist": "Wishlist", "Categories": "Categories", "Cart": "<PERSON><PERSON>", "Profile": "Profile"}, "newUser": {"verifyUser": {"addNewNumber": "add new number", "confirmChangeDefaultNumber": "confirm change default number", "continue": "Continue", "cancel": "Cancel", "proceed": "Proceed", "next": "Next", "enterPassword": "Please enter your account password", "password": "Password", "passwordConfirm": "Enter your password to", "OK": "OK"}}, "notFound": {"goBack": "Go Back"}, "order": {"At": "At", "allOrders": "All Orders", "Cancelled": "Cancelled", "Delivered": "Delivered", "InTransit": "In Transit", "orderItems": "items", "orderItemsCount": "Order has", "orderNo": "Order No.", "OrderPlaced": "Order Placed", "orderStatus": "Order Status", "Pending": "Pending", "Processing": "Processing", "refresh": "Refresh", "Refunded": "Refunded", "RefundRequested": "Refund Requested", "Succeeded": "Successful", "viewDetails": "view details", "yourOrders": "My Orders", "orderDetails": "Order Details", "yourAccount": "My Account", "orderDate": "Order Date", "RequestReturn": "Return Requested", "Returned": "Returned", "noOrder": "No order found", "noOrderMessage": "Add Items To Get Started", "UnderProcessing": "Under Processing", "ReturnRejected": "Return Rejected", "ReturnInProgress": "Return In Progress"}, "orderDetails": {"addReview": "Add Review", "amount": "Amount", "amountDetails": "Amount details", "cancelOrder": "Cancel order", "cancelReason": "Please indicate the reason for cancellation", "itemCancelled": "Item cancelled", "itemsAmount": "Items Amount", "notificationSent": " We've notified the merchant and will revert back to you once they accept or decline the request", "ok": "OK", "orderCancelled": "Order cancelled", "yourOrderHasBeenCancelled": "Your order has been cancelled", "orderDetails": "Order details", "orderNo": "Order no", "password": "Password", "paymentDetails": "Payment details", "orderDetailsBox": "Download Your Order Receipt", "invalidOrderReceipt": "Invalid Order Receipt", "cannotDownloadReceipt": "Unable to download receipt. The order receipt is invalid or not available.", "paymentTotal": "Payment total", "proceed": "Proceed", "rateAndReview": "Rate and Review", "reasonForCancellation": "Reason for cancellation", "reasonForRefund": "Reason for refund", "refundAmount": "Refund amount", "refundOrder": "Request Refund", "refundReasonIsRequired": "Refund reason is required", "refundRequestSent": "Refund request sent", "requestRefund": "Request Refund", "reviewRequestSent": "Review request sent", "selectTheProduct": "Select the product(s) you want to request refund", "shipmentStatus": "Shipment Status", "shipmentTracking": "Shipment tracking", "shipping": "Shipping", "shippingDetails": "Shipping details", "sku": "SKU", "takeTheTime": "Take your time to tell us what do you think about the product you purchase", "transactionID": "Transaction ID", "transactionPhone": "Mobile Number", "transactionStatus": "Transaction Status", "type": "Type", "typeCard": "Card", "typeMomo": "MoMo Pay", "vat": "VAT", "weAreProcessingTheRefund": "We are processing the refund, it might take up to 72 hours", "yourReview": "Your Review", "rateReview": "Rate and Review", "itemYouReceived": "Take the time to tell us what do you think about the item you received", "submitreview": "SUBMIT REVIEW", "reviewSubmitted": " Review submitted", "reviewPublish": "Your review will be published once our team has reviewed and approved it", "cancelOrderReason": "Cancel reason", "explainMore": "Explain more", "cancelOrderConfirmQuestions": "Are you sure you want to cancel this Order?", "cancelThisItem": "Cancel this item", "cancelItem": "Cancel item", "cancelSingleOrderConfirmQuestions": "Are you sure you want to cancel this item?", "requestReturn": "Request return", "returnReason": "Return reason", "confirmReason": "Confirm return ", "orderRequestReturn": "Request return Order", "selectReturnReason": "Select return reason", "upload": "Upload", "uploadImageVideo": "Upload image or video", "areYouSureWantReturnOrder": "Are you sure you want to return this order?", "areYouSureWantReturnItem": "Are you sure you want to Return this Item?", "returnRequestSent": "Return request sent", "notifiedMerchantReturnedRequest": "We've notified the merchant and will revert back to you once they accept or decline the request.", "returnItem": "Return Item", "refundSuccessfullyProcessed": "Refunded", "pickUpPlace": "Pick up place", "totalAmount": "Total Amount", "uploadImage": "Upload image or video", "requestRefundOrder": "Request refund Order", "requestRefundMessage": "Are you sure you want to Refund this Order?", "Back": "back", "next": "Next", "return": "Return", "numberOfCancelledItem": "Number of cancelled item", "numberOfReturnedItem": "Number of returned item", "returnFromPickedUpDeliveredAddress": "Return will be picked up from the delivered address", "back": "Back", "discount": "Discount"}, "otp": {"enterOTP": "Enter OTP", "resend": "Resend in 00", "verificationCodeSent": "We just sent the verification code"}, "phoneNumber": {"verifyMobile": {"addNew": "Add New Mobile Number", "changeDefaultNumber": "Change Default Number", "mobileNumber": "Mobile number"}}, "primeng": {"medium": "Medium", "passwordPrompt": "Enter a password", "strong": "Strong", "weak": "Weak"}, "productCard": {"instock": "In stock", "soldOut": "Out of stock"}, "productDetails": {"details": {"aboutThisProduct": "About this product", "addToCart": "Add to cart", "basedOn": "Based on", "bestseller": "Best Seller", "bestSeller": "BEST SELLER", "buyNow": "Buy Now", "colors": "Colors", "customerRating": "Customer Rating", "customerReviews": "Customer reviews", "description": "Description", "dimension": "Dimension", "dx": "D x", "female": "Female", "gender": "Gender", "h": "H", "height": "Height", "hot": "HOT", "inStock": "in stock", "length": "Length", "linkIsCopiedSuccessfully": "Link is copied successfully", "male": "Male", "merchantName": "Merchant name", "multiColor": "Multi Color", "newArrivals": "New Arrivals", "off": "off", "orCopyLink": "Or copy link", "outOfStock": "Out of stock", "overallRating": "Overall rating", "productDetails": "Product details", "ProductShareWithYourFriend": "If you like this product share it with your friends.", "ratings": "ratings", "review": "Review", "sale": "SALE", "shareThisProduct": "Share this product", "sizeGuide": "Size Guide", "sku": "Merchant SKU", "specification": "Specification", "uniSex": "Unisex", "verifiedPurchase": "Verified purchase", "weight": "Weight", "width": "<PERSON><PERSON><PERSON>", "wx": "W x", "notifyMe": "NOTIFY ME", "only": "Only", "leftInStock": "left in stock", "leftInStockMultiple": "left in stock", "brand": "Brand", "itemsLeft": "items left", "deliverTo": "Deliver to", "homeLocation": "Home location", "skuAutoGenerated": "SKU"}}, "register": {"authenticateYourself": "We'll send you an OTP to authenticate yourself", "next": "Next", "phoneNumber": "Phone number", "register": "Register", "login": "Sign-in", "mobileNumber": "Mobile number", "continue": "Continue", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign-Up", "content": "Find everything you love on Market by MoMo"}, "resetPassword": {"authenticateYourself": "We'll send you an OTP to authenticate yourself", "next": "Next", "phoneNumber": "Phone number", "resetPassword": "Reset password"}, "signIn": {"signIn": "Sign-in", "noAccountYet": "No account yet? ", "createone": "Create one", "phoneNumber": "Phone number", "password": "Password", "forgotPassword": "Forgot Your password?", "continue": "Sign-in", "tooltip": "Please lengthen this text to 12 characters or more", "signInText": "Find everything you love on Market by MoMo", "AgreeTermsOne": "By continuing, you agree to Market by MoMo's", "AgreeTermsTwo": "Conditions of Use", "AgreeTermsThree": "and", "AgreeTermsFour": "Privacy Notice", "AgreeTermsFive": " ", "newCustomer": "New Customer?", "Register": "Register now", "content": "Find everything you love on Market by MoMo"}, "updatePassword": {"resetPassword": "Reset Password", "setUpSecurePassword": "Please set up a secure password following the criteria below", "phoneNumber": "Phone number", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordReset": "Password reset", "backToLogin": "Back To Login"}, "ResponseMessages": {"orderIsInValid": "Order Contains Invalid Products.", "address": "Address", "addressAddedSuccessfully": "Address has been successfully added", "addressUpdatedSuccessfully": "Address has been successfully edited", "cart": "<PERSON><PERSON>", "changePassword": "change password", "contactUsSuccessMessage": "Thanks for contacting us! We will be in touch with you shortly.", "defaultAddressSuccessfully": "Default address added", "invalidCityAddress": "City is not defined in any region. Please update your address.", "loggedInSuccessfully": "Logged In Successfully", "login": "<PERSON><PERSON>", "newPrimarySuccessMessage": "Default number changed Successfully.", "newSecondaryDeleteSuccessMessage": "Secondary phone number deleted.", "newSecondarySuccessMessage": "New Mobile number added Successfully", "okButtonText": "OK", "passwordExpirationChange": "Password Expiration change", "phoneNumberIsValid": "Phone Number Is Valid", "pleaseProvideYourAddress": "Please provide your address", "register": "Registeration", "registerSuccess": "User Created Successfully", "successfullyAddedToCart": "Successfully added to cart", "successfullyAddedToWishList": "Successfully added to the wishlist", "successfullyDeletedFromCart": "Wishlist successfully updated", "successfullyRemovedToWishList": "Removed from the wishlist", "successfullyUpdatedFromCart": "<PERSON><PERSON> successfully updated", "successfullyRemovedFromCart": "<PERSON><PERSON> successfully updated", "wishList": "WishList"}, "search": {"in": "in", "scSearchOnMoMoMarket": "Search on yalla super mall", "searchOnMoMoMarket": "Search on Market by MoMo", "recentSearches": "Recent Searches"}, "searchNotFound": "No results found!", "sellerInfo": {"delivery": "Delivery", "details": "Details", "moreFromThisSeller": " More from this Seller", "returnPolicy": "Return Policy", "daysReturnable": "This item is eligible for free returns within", "days": "days", "sellerInformation": "Seller Information", "totalReviews": "Total reviews", "viewAll": "View All", "warranty": "Warranty", "moreSeller": "See more from the seller"}, "sideMenu": {"allMerchants": "All Merchants", "browseAllCategories": "Browse all categories", "categories": "Categories", "customerService": "Customer service", "help": "Help", "home": "Home", "merchants": "Merchants", "signIn": "Sign-In", "signOut": "Sign-Out", "yourAccount": "My Account", "yourAddresses": "My Account"}, "wishlist": {"addToCart": "Add to cart", "continueShopping": "Continue shopping", "delete": "Delete", "description": "Placing a product on the Wishlist dos not reserve that product or price. We only reserve the product once payment is received", "MoveCart": "Move to cart", "notAvailable": "NOT AVAILABLE", "price": "PRICE", "products": "PRODUCTS", "title": "Wishlist", "was": "Was", "wishListEmpty": "Your wishlist is empty", "wishListEmptyMessage": "Your wishlist is empty", "wishListShopping": "Start Shopping", "wishListWaiting": "What are you waiting for?", "items": "items", "movetoCart": "Move to cart", "notifyMe": " Notify me", "yourWishlist": "Your Wishlist"}, "yourDetailsbread": {"details": "details", "yourDetails": "My Profile"}, "notifyMeDetails": {"notifyMeVia": "Notify me via:", "byNumber": "By Mobile number", "byEmail": "By email address", "notifyAvaliable": "Notify me when available", "close": "CLOSE", "thanksForInterest": "Thanks for your interest!", "notifyProductIsAvaialble": "Well, you will receive a notification if the product is available"}, "flashSaleModal": {"hours": " Hours", "min": "Min", "sec": "Sec", "visit": "Visit Now", "close": "Close"}, "promo": {"header": "Add promo code", "placeholder": "Enter coupon code", "apply": "Apply", "discountApplied": "Discount applied", "couponCodeInvalid": "Invalid Promo code", "couponAlreadyUsed": "Coupon already used"}, "countries": {"Uganda": "Uganda", "Ghana": "Ghana", "CotedIvoire": "Côte d’Ivoire"}, "ageConsentModal": {"ageVerification": "Age verification", "PleaseVerify": "Please verify your age to enter.", "Day": "Day", "Month": "Month", "Year": "Year", "IAgreeAll": "I agree all", "btnCancel": "Cancel", "btnProceed": "Proceed", "validationMsg": "Please enter all data required", "errorLabel": "The date of birth you entered indicates that you are under the age of *AGE*. We are not permitted to sell liquor and/or selected vaping products to you. Please remove all liquor and/or selected vaping products from your cart to proceed."}}