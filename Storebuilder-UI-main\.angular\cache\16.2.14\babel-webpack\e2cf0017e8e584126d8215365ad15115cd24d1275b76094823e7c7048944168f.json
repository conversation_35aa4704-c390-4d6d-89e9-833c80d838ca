{"ast": null, "code": "export const environment = {\n  production: true,\n  apiEndPoint: 'https://market.momo.africa:1010',\n  securityApiUrl: 'https://uat-apps.paysky.io/sso-authenticator/connect/token',\n  featureProductsLimit: 100,\n  platform: 'storecloud',\n  isStoreCloud: false,\n  isMarketPlace: true,\n  testing: false,\n  excludingTenants: [0],\n  defaultTenant: '1',\n  referer: 1,\n  merchantURL: \"https://marketuat.momo.africa:2024\",\n  maintenanceMode: false,\n  marketPlaceHostName: \"marketuat.momo.africa\",\n  client_id: '',\n  client_secret: '82C0CFC2-F45A-469A-B124-DDBE23A8C13B',\n  scope: 'cc.access'\n};", "map": {"version": 3, "names": ["environment", "production", "apiEndPoint", "securityApiUrl", "featureProductsLimit", "platform", "isStoreCloud", "isMarketPlace", "testing", "excludingTenants", "defaultTenant", "referer", "merchantURL", "maintenanceMode", "marketPlaceHostName", "client_id", "client_secret", "scope"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\r\n  production: true,\r\n  apiEndPoint: 'https://market.momo.africa:1010',\r\n  securityApiUrl:'https://uat-apps.paysky.io/sso-authenticator/connect/token',\r\n  featureProductsLimit: 100,\r\n  platform: 'storecloud',\r\n  isStoreCloud: false,\r\n  isMarketPlace: true,\r\n  testing: false,\r\n  excludingTenants: [0],\r\n  defaultTenant: '1',\r\n  referer:1,\r\n  merchantURL: \"https://marketuat.momo.africa:2024\",\r\n  maintenanceMode: false,\r\n  marketPlaceHostName: \"marketuat.momo.africa\",\r\n  client_id:'',\r\n  client_secret:'82C0CFC2-F45A-469A-B124-DDBE23A8C13B',\r\n  scope:'cc.access'\r\n};"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,iCAAiC;EAC9CC,cAAc,EAAC,4DAA4D;EAC3EC,oBAAoB,EAAE,GAAG;EACzBC,QAAQ,EAAE,YAAY;EACtBC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,IAAI;EACnBC,OAAO,EAAE,KAAK;EACdC,gBAAgB,EAAE,CAAC,CAAC,CAAC;EACrBC,aAAa,EAAE,GAAG;EAClBC,OAAO,EAAC,CAAC;EACTC,WAAW,EAAE,oCAAoC;EACjDC,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE,uBAAuB;EAC5CC,SAAS,EAAC,EAAE;EACZC,aAAa,EAAC,sCAAsC;EACpDC,KAAK,EAAC;CACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}