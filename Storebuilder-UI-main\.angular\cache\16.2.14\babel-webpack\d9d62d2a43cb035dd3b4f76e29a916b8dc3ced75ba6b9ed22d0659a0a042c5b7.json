{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { FormGroup, FormControl } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction PromoCodeComponent_i_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 10);\n    i0.ɵɵlistener(\"click\", function PromoCodeComponent_i_9_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.resetPromo());\n    });\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PromoCodeComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"img\", 13);\n    i0.ɵɵelementStart(2, \"span\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.promoError, \" \");\n  }\n}\nfunction PromoCodeComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"img\", 16);\n    i0.ɵɵelementStart(2, \"span\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.promoSuccess, \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"promo-code__input-error\": a0\n  };\n};\nexport class PromoCodeComponent {\n  constructor(store, orderService, translate) {\n    this.store = store;\n    this.orderService = orderService;\n    this.translate = translate;\n    this.isReadOnly = false;\n    this.applyButtonClicked = new EventEmitter();\n    this.resetButtonClicked = new EventEmitter();\n    this.regionId = null;\n    this.promoCodeForm = new FormGroup({\n      promoCode: new FormControl('')\n    });\n    this.isButtonDisabled = false;\n    this.discount = '';\n  }\n  ngOnInit() {\n    this.getOrderData();\n    // This resetPromo call is added to make sure that promo is resetted in DB after applying then refresh page\n    this.resetPromo();\n  }\n  ngOnChanges(changes) {\n    // changes['paymentMethodDetails'] &&\n    // changes['paymentMethodDetails'].currentValue?.id ===\n    //   changes['paymentMethodDetails'].previousValue?.id\n    if (changes['paymentMethodDetails']) {\n      this.resetPromo(true);\n    }\n    if (changes['deliveryOptionDetails']) {\n      this.removePromoOnly();\n    }\n    if (changes['regionId']) {\n      this.resetPromo(true);\n    }\n  }\n  getOrderData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.store.subscription('orderData').subscribe({\n        next: res => {\n          if (res) {\n            _this.orderDetails = res;\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    })();\n  }\n  resetPromo(paymentMethodChanged) {\n    this.resetButtonClicked.emit();\n    this.isReadOnly = false;\n    const reqBody = {\n      OrderId: this.orderDetails?.orderId,\n      PromoCode: this.promoCodeForm.get('promoCode')?.value || '',\n      PaymentType: this.paymentMethodDetails.name\n    };\n    if (this.discount) {\n      this.orderService.removePromoCode(reqBody).subscribe({\n        next: res => {\n          this.refreshSummary.next();\n          paymentMethodChanged ? this.applyPromo() : this.promoCodeForm.get('promoCode')?.reset();\n          this.discount = '';\n          this.resetError();\n        }\n      });\n    } else if (this.promoCodeForm.get('promoCode')?.value && paymentMethodChanged) {\n      this.resetError();\n      this.applyPromo();\n    } else {\n      this.promoCodeForm.get('promoCode')?.reset();\n      this.resetError();\n    }\n    this.isButtonDisabled = false;\n  }\n  removePromoOnly() {\n    this.resetButtonClicked.emit();\n    this.isReadOnly = false;\n    const promo = this.promoCodeForm.get('promoCode')?.value;\n    const reqBody = {\n      OrderId: this.orderDetails?.orderId,\n      PromoCode: promo || '',\n      PaymentType: this.paymentMethodDetails.name\n    };\n    if (promo) {\n      this.orderService.removePromoCode(reqBody).subscribe({\n        next: () => {\n          // this.refreshSummary.next();\n          this.promoCodeForm.get('promoCode')?.reset();\n          this.discount = '';\n          this.resetError();\n          this.isButtonDisabled = false;\n          this.orderService.resetDiscount();\n        },\n        error: err => {\n          console.error('Error removing promo code:', err);\n        }\n      });\n    } else {\n      this.promoCodeForm.get('promoCode')?.reset();\n      this.resetError();\n      this.isButtonDisabled = false;\n    }\n  }\n  resetError() {\n    this.promoError = '';\n    this.promoSuccess = '';\n  }\n  applyPromo() {\n    const reqBody = {\n      OrderId: this.orderDetails?.orderId,\n      PromoCode: this.promoCodeForm.get('promoCode')?.value,\n      PaymentType: this.paymentMethodDetails.name,\n      regionId: this.regionId\n    };\n    this.orderService.applyPromoCode(reqBody).subscribe({\n      next: res => {\n        if (res.success) {\n          this.promoError = '';\n          this.applyButtonClicked.emit();\n          this.isReadOnly = true;\n          this.promoSuccess = this.translate.instant('promo.discountApplied');\n          this.refreshSummary.next();\n          this.isButtonDisabled = true;\n          this.discount = this.promoCodeForm.get('promoCode')?.value;\n        } else {\n          this.promoSuccess = '';\n          this.orderService.resetDiscount();\n          switch (res.message) {\n            case 'Invalid Promo code':\n              this.promoError = this.translate.instant('promo.couponCodeInvalid');\n              break;\n            case 'Coupon already used':\n              this.promoError = this.translate.instant('promo.couponAlreadyUsed');\n              break;\n            default:\n              return res.message;\n          }\n        }\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n}\nPromoCodeComponent.ɵfac = function PromoCodeComponent_Factory(t) {\n  return new (t || PromoCodeComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.OrderService), i0.ɵɵdirectiveInject(i2.TranslateService));\n};\nPromoCodeComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: PromoCodeComponent,\n  selectors: [[\"app-promo-code\"]],\n  inputs: {\n    refreshSummary: \"refreshSummary\",\n    isReadOnly: \"isReadOnly\",\n    paymentMethodDetails: \"paymentMethodDetails\",\n    deliveryOptionDetails: \"deliveryOptionDetails\",\n    address: \"address\",\n    regionId: \"regionId\"\n  },\n  outputs: {\n    applyButtonClicked: \"applyButtonClicked\",\n    resetButtonClicked: \"resetButtonClicked\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 15,\n  vars: 18,\n  consts: [[1, \"promo-code\"], [1, \"promo-code__header\"], [1, \"promo-code__form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"d-flex\"], [1, \"promo-code__input-wrapper\"], [\"formControlName\", \"promoCode\", \"id\", \"promoCode\", \"pInputText\", \"\", \"type\", \"text\", 1, \"promo-code__input\", 3, \"ngClass\", \"placeholder\", \"readonly\", \"input\"], [\"class\", \"promo-code__input-reset\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"promo-code__action\", 3, \"disabled\"], [\"class\", \"promo-code__error-wrapper\", 4, \"ngIf\"], [\"class\", \"promo-code__success-wrapper\", 4, \"ngIf\"], [1, \"promo-code__input-reset\", 3, \"click\"], [\"src\", \"assets/icons/circle-close.svg\", \"alt\", \"reset icon\"], [1, \"promo-code__error-wrapper\"], [\"src\", \"assets/icons/error.svg\", \"alt\", \"error icon\", 1, \"promo-code__error-icon\"], [1, \"promo-code__error-msg\"], [1, \"promo-code__success-wrapper\"], [\"src\", \"assets/icons/success-icon.svg\", \"alt\", \"success icon\", 1, \"promo-code__success-icon\"], [1, \"promo-code__success-msg\"]],\n  template: function PromoCodeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtext(2);\n      i0.ɵɵpipe(3, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"form\", 2);\n      i0.ɵɵlistener(\"ngSubmit\", function PromoCodeComponent_Template_form_ngSubmit_4_listener() {\n        return ctx.applyPromo();\n      });\n      i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"input\", 5);\n      i0.ɵɵlistener(\"input\", function PromoCodeComponent_Template_input_input_7_listener() {\n        return ctx.resetError();\n      });\n      i0.ɵɵpipe(8, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(9, PromoCodeComponent_i_9_Template, 2, 0, \"i\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"button\", 7);\n      i0.ɵɵtext(11);\n      i0.ɵɵpipe(12, \"translate\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(13, PromoCodeComponent_div_13_Template, 4, 1, \"div\", 8);\n      i0.ɵɵtemplate(14, PromoCodeComponent_div_14_Template, 4, 1, \"div\", 9);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      let tmp_5_0;\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 10, \"promo.header\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.promoCodeForm);\n      i0.ɵɵadvance(3);\n      i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(8, 12, \"promo.header\"));\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ctx.promoError))(\"readonly\", ctx.isReadOnly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.promoCodeForm.get(\"promoCode\")) == null ? null : tmp_5_0.value) && !ctx.promoError);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"disabled\", ctx.isButtonDisabled);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 14, \"promo.apply\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.promoError);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.promoSuccess);\n    }\n  },\n  dependencies: [i3.NgClass, i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i2.TranslatePipe],\n  styles: [\".promo-code[_ngcontent-%COMP%] {\\n  margin-bottom: 14px;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__form[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding: 24px;\\n  }\\n}\\n.promo-code__header[_ngcontent-%COMP%] {\\n  padding: 24px 0px 16px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  line-height: 20.8px;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__header[_ngcontent-%COMP%] {\\n    padding: 10px 24px;\\n    background: #F2F4F5;\\n    color: var(--gray-700, #475156);\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    line-height: normal;\\n    text-transform: capitalize;\\n  }\\n}\\n.promo-code__input[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  border: 1px solid rgba(0, 0, 0, 0.1) !important;\\n  border-radius: 8px;\\n  padding: 18px 8px;\\n  height: 50px;\\n  width: 100%;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__input[_ngcontent-%COMP%] {\\n    height: 38px;\\n    background-color: #F5F5F5 !important;\\n    border: none !important;\\n    border-radius: 6px;\\n    padding: 11px 20px;\\n  }\\n}\\n.promo-code__input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  margin-inline-end: 8px;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__input-wrapper[_ngcontent-%COMP%] {\\n    margin-inline-end: 10px;\\n  }\\n}\\n.promo-code__input[_ngcontent-%COMP%]::placeholder {\\n  color: #C5C6CC;\\n  font-size: 14px;\\n  font-weight: 400;\\n  line-height: 14px;\\n  letter-spacing: 0.5px;\\n}\\n.promo-code__input-error[_ngcontent-%COMP%] {\\n  border-color: #EE5858 !important;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__input-error[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 38, 6, 0.06) !important;\\n    border: 1px solid #EE5858 !important;\\n    color: #FF5252 !important;\\n  }\\n}\\n.promo-code__input-reset[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 8px;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__input-reset[_ngcontent-%COMP%] {\\n    right: 22px;\\n  }\\n}\\n.promo-code__error-wrapper[_ngcontent-%COMP%], .promo-code__success-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 5px;\\n}\\n.promo-code__error-icon[_ngcontent-%COMP%], .promo-code__success-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  margin-inline-end: 4px;\\n}\\n.promo-code__error-msg[_ngcontent-%COMP%], .promo-code__success-msg[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 400;\\n  line-height: 14px;\\n}\\n.promo-code__error-msg[_ngcontent-%COMP%] {\\n  color: #FF5252;\\n}\\n.promo-code__success-msg[_ngcontent-%COMP%] {\\n  color: #01B467;\\n}\\n.promo-code__action[_ngcontent-%COMP%] {\\n  height: 50px;\\n  background-color: white;\\n  border: 1px solid #204E6E;\\n  border-radius: 6px;\\n  color: #204E6E;\\n  padding: 12px 24px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  line-height: 14px;\\n  letter-spacing: 0.012em;\\n  text-align: left;\\n}\\n@media screen and (min-width: 768px) {\\n  .promo-code__action[_ngcontent-%COMP%] {\\n    display: flex;\\n    height: 38px;\\n    padding: 0px 20px;\\n    justify-content: center;\\n    align-items: center;\\n    border-radius: 6px;\\n    border: 2px solid #204E6E;\\n    color: var(--colors-main-color, #204E6E);\\n    font-size: 14px;\\n    font-style: normal;\\n    font-weight: 700;\\n    line-height: 40px;\\n    letter-spacing: 0.168px;\\n    text-transform: uppercase;\\n  }\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}