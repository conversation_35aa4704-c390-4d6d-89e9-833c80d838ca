body {
  font-family: var(--regular-font);
}

body.overlay {
  overflow: hidden;
}

.pi {
  * {
    font-family: var(--regular-font);
  }
}

.content-container {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}
.mobile-content-container {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.main-slider {
  position: relative;

  .owl-carousel {
    width: 100%;


    .owl-item {
      width: 100%;
      height: 100%;

      img {
        height: auto;
        width: 95%;
        object-fit: contain;
        border-radius: 15px;
        transform: translate(3rem, 0%);
      }
    }

    .owl-nav {
      .owl-next {
        position: absolute;
        right: 15px;
        top: 0;
        bottom: 0;
        margin: auto !important;
        background: transparent !important;
        height: 30px !important;
        font-size: 10px;

        span {
          font-size: 25px;
          color: #000;
        }
      }


      .owl-prev {
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        margin: auto !important;
        background: transparent !important;
        height: 30px !important;
        font-size: 10px;

        span {
          font-size: 25px;
          color: #000;
        }
      }
    }
  }
}

.card-slider {
  position: relative;

  .owl-carousel .owl-stage-outer {
    padding: 10px 0;
  }

  .owl-carousel {
    width: 100%;

    .owl-nav {
      padding: 0 !important;

      .owl-next {
        position: absolute;
        right: -35px;
        top: 0;
        bottom: 0;
        margin: auto !important;
        background: transparent !important;
        height: 30px !important;
        font-size: 10px;

        span {
          font-size: 25px;
          color: #000;
        }
      }

      .owl-prev {
        position: absolute;
        left: -35px;
        top: 0;
        bottom: 0;
        margin: auto !important;
        background: transparent !important;
        height: 30px !important;
        font-size: 10px;

        span {
          font-size: 25px;
          color: #000;
        }
      }
    }
  }
}

.in-stock {
  color: #01b467;
  font-size: 12px;
  font-family: var(--medium-font);
}

.product-flag {
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px 10px 10px 0;
  padding: 2px 5px;
  background-color: var(--flag-color);
  color: var(--flag-font-color);
  font-family: var(--medium-font) !important;
  font-size: 10.4px;

  .pi {
    font-size: 13px;
  }
}

.rating {
  display: flex;
  flex-direction: row;

  .star {
    color: var(--fourth-color);
    font-size: 12px;
    margin-top: 5px;
  }

  .rate {
    position: relative;
    top: 2px;
    color: var(--fourth-color);
    font-size: 14px;
    font-family: var(--medium-font);
  }

  .rating-number {
    color: #a3a3a3;
    font-size: 12px;
    font-family: var(--light-font);
  }
}

.progress-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: absolute;
}

.spinner {
  position: fixed;
  z-index: 9999;
  height: 2em;
  width: 6em;
  overflow: show;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* Transparent Overlay */
.spinner:before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.53);
}


.spinner-product {
  z-index: 10000;
  height: 2em;
  width: 6em;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin-bottom: 100px;
}

/* Transparent Overlay */
.spinner-product:before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

::ng-deep .p-breadcrumb {
  background: #f5f5f5;
}

::ng-deep .p-breadcrumb ul li:last-child .p-menuitem-text {
  color: #000;
}

::ng-deep .p-breadcrumb ul li:last-child .p-menuitem-icon {
  color: #000;
  position: relative;
  top: 0.1rem;
  margin-right: 0.5rem;
}

.breadcrumb p-breadcrumb.col-12.p-element {
  padding-left: 0;
  padding-right: 0;
  padding-top: 0px;
}

.p-breadcrumb.p-component {
  background: #F5F5F5;
  font-family: var(--regular-font);
  padding:1rem 2rem;
}

.pi-chevron-right:before {
  content: "\e931";
  font-weight: bold;
  color: #323232;
}

p-breadcrumb.p-element {
  width: 100%;
  margin-bottom: 30px;
}

.pi-home:before {
  content: "\f878";
  background-image: url('../../assets/icons/home.svg') !important;
  padding: 5px 3px 0px 3px;
  background-repeat: no-repeat;
}

.p-breadcrumb ul li .p-menuitem-link .p-menuitem-text {
  color: #323232;
  font-family: var(--medium-font);
  font-size: 15px;
}

.product-nums {
  color: #a3a3a3;
  font-size: 15px;
  font-weight: normal;
}

.status-badge {
  background: var(--header_bgcolor);
  border-radius: 25px;
  width: max-content;
  margin: auto;

  .pi-circle-fill {
    font-size: 0.5rem;
  }

  .pi {
    &.pi-circle-fill {
      &.pending {
        color: #d8d8d8;
      }

      &.processing {
        color: orange;
      }

      &.canceled {
        color: red;
      }

      &.refund {
        color: #ff6c00;
      }

      &.completed {
        color: #00c853;
      }

      &.InTransit {
        color: orange;
      }
    }
  }
}

.item-details {
  width: 51%;
  margin: auto;

  img {
    width: 37px;
    height: 48px;
  }

  .price {
    position: relative;
    top: 1rem;
  }

  .image {
    margin: 3%;
    position: relative;
  }

  .first-txt {
    position: absolute;
    top: -27px;
    left: 20px;
  }
}


@media only screen and (max-width: 786px) {
  .main-slider {
    position: relative;

    .owl-carousel {
      .owl-item {
        width: 100%;
        height: 100%;

        img {
          height: auto;
          width: 100%;
          transform: none;
          object-fit: fill;
        }
      }
    }
  }

  .item-details {
    width: 100%;
    margin: auto;

    img {
      width: 37px;
      height: 48px;
    }

    .price {
      position: relative;
      top: 1rem;
    }

    .image {
      margin: 3%;
      position: relative;
    }

    .first-txt {
      position: absolute;
      top: -27px;
      left: 20px;
    }
  }

  .rate {
    font-size: 15px !important;
    font-family: var(--medium-font);
    font-weight: 500;
  }

  .rating-number {
    color: #a3a3a3;
    font-size: 14px !important;
    font-weight: 300;
    font-family: var(--light-font);
    margin-top: 3px;
  }
}
@media screen and (max-width: 768px) {
  .p-breadcrumb ul li .p-menuitem-link .p-menuitem-text{
    color: #646464;
    font-size: 10px;
    font-weight: 400;
    line-height: 100%;
  }
  .p-breadcrumb .pi-home {
    display: none; /* Hide the home icon */
  }
  .p-breadcrumb.p-component{
    background: transparent;
    padding: 1rem
  }
  .p-breadcrumb ul li:nth-child(2) {
    display: none !important;
  }
  .p-menu-item-icon{
    display: flex;
    height: 24px;

  }
  .pi-home:before{
    background-size: 22px 19px;
    height: 16px;
    margin: auto 0;
    padding: 0 3px;
  }
  .p-breadcrumb {
    ul {
      li {
        .p-menuitem-link {
          display: flex;
        }
        &:nth-child(1) {
          .p-menuitem-link {
            display: unset
          }
        }
      }
    }
  }
}
.mobile-container{
  border: 1px solid rgba(32, 78, 110, 0.10);
  border-radius: 8px;
  margin: 25px;
  padding: 0 1rem !important;
  .signup-heading{
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%;
    font-family: var(--medium-font);


  }
  .signup-desc{
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%;
    font-family: var(--regular-font);
  }

}
.dialog-wrapper .p-dialog-content{
  border-radius: 8px;
  padding: 0;
  max-height: none !important;
  overflow: visible !important;
}

.dialog-wrapper .p-dialog {
  max-height: none !important;
  overflow: visible !important;
}
