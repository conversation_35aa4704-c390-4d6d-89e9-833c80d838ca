{"ast": null, "code": "export var UserConsentType;\n(function (UserConsentType) {\n  UserConsentType[UserConsentType[\"Cookie\"] = 1] = \"Cookie\";\n  UserConsentType[UserConsentType[\"Register\"] = 2] = \"Register\";\n  UserConsentType[UserConsentType[\"Promotion\"] = 3] = \"Promotion\";\n})(UserConsentType || (UserConsentType = {}));", "map": {"version": 3, "names": ["UserConsentType"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\enums\\user.ts"], "sourcesContent": ["export enum UserConsentType {\r\n  Cookie = 1,\r\n  Register = 2,\r\n  Promotion = 3\r\n}\r\n"], "mappings": "AAAA,WAAYA,eAIX;AAJD,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,0BAAU;EACVA,eAAA,CAAAA,eAAA,8BAAY;EACZA,eAAA,CAAAA,eAAA,gCAAa;AACf,CAAC,EAJWA,eAAe,KAAfA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}