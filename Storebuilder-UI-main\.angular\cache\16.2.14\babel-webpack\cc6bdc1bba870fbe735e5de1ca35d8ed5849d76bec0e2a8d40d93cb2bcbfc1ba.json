{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport UtilityFunctions from \"@core/utilities/functions\";\nimport { environment } from \"@environments/environment\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { Carousel } from 'primeng/carousel';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-google-analytics\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/carousel\";\nconst _c0 = function (a0) {\n  return {\n    \"background-image\": a0\n  };\n};\nfunction MtnMainSliderComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function MtnMainSliderComponent_ng_template_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const mainSlider_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.routeToCTA(mainSlider_r2, mainSlider_r2.CTALink));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const mainSlider_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(1, _c0, \"url(\" + ctx_r0.getBannerImages(mainSlider_r2.imageUrl) + \")\"));\n  }\n}\nfunction MtnMainSliderComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const dot_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", dot_r5.selected ? \"dot-color\" : \"dot-simple\");\n  }\n}\nexport class MtnMainSliderComponent {\n  router;\n  platformId;\n  $gaService;\n  sliderOptions;\n  innerWidth;\n  mobileScreen = false;\n  sliders = [];\n  sliderDots = [];\n  constructor(router, platformId, $gaService) {\n    this.router = router;\n    this.platformId = platformId;\n    this.$gaService = $gaService;\n    Carousel.prototype.onTouchMove = () => {};\n    this.sliderOptions = {\n      loop: true,\n      autoplay: true,\n      center: true,\n      dots: true,\n      autoplayTimeout: 3000,\n      autoHeight: false,\n      autoWidth: true,\n      lazyLoad: true,\n      autoplayHoverPause: true,\n      navText: ['<em class=\"pi pi-angle-left white-color font-size-30\"></em>', '<em class=\"pi pi-angle-right white-color font-size-30\"></em>'],\n      responsive: {\n        0: {\n          items: 1,\n          dots: true,\n          nav: false\n        },\n        600: {\n          items: 1,\n          dots: true,\n          nav: false\n        },\n        1000: {\n          items: 1\n        }\n      }\n    };\n  }\n  ngOnInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.innerWidth = window.innerWidth;\n      if (this.innerWidth < 768) {\n        this.mobileScreen = true;\n      } else {\n        this.mobileScreen = false;\n      }\n    }\n    this.sliders.map((x, index) => {\n      this.sliderDots.push({\n        index,\n        selected: index === 0\n      });\n    });\n  }\n  routeToCTA(banner, url) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_BANNERS, '', 'BANNERS_ON_HOMEPAGE', 1, true, {\n        bannerId: banner.promotionId || url.split('/').pop(),\n        redirectPage: url\n      });\n      if (banner.promotionId) {\n        if (banner.CTALink) {\n          const cta = banner.CTALink.replace(/promotions\\//g, \"promotion/\");\n          if (!banner.CTALink.includes('http://') && !banner.CTALink.includes('https://')) {\n            window.location.href = 'https://' + cta;\n          } else {\n            window.location.href = cta;\n          }\n        } else {\n          let tempurl = ' https://' + environment.marketPlaceHostName + '/promotion/' + banner.promotionId;\n          window.location.href = tempurl;\n        }\n      } else {\n        window.location.href = url;\n      }\n    }\n  }\n  getBannerImages(url) {\n    return UtilityFunctions.verifyImageURL(url, environment.apiEndPoint);\n  }\n  changeSliderDot(event) {\n    this.sliderDots.forEach(obj => {\n      obj.selected = obj.index == event.page;\n    });\n  }\n  static ɵfac = function MtnMainSliderComponent_Factory(t) {\n    return new (t || MtnMainSliderComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i2.GoogleAnalyticsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MtnMainSliderComponent,\n    selectors: [[\"app-mtn-main-slider\"]],\n    inputs: {\n      sliders: \"sliders\"\n    },\n    decls: 6,\n    vars: 5,\n    consts: [[1, \"mtn-main-slider\"], [3, \"value\", \"circular\", \"autoplayInterval\", \"showIndicators\", \"onPage\"], [\"pTemplate\", \"item\"], [1, \"row\"], [1, \"center-dot\", \"mt-2\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"banner-image\", 3, \"ngStyle\", \"click\"], [3, \"ngClass\"]],\n    template: function MtnMainSliderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"p-carousel\", 1);\n        i0.ɵɵlistener(\"onPage\", function MtnMainSliderComponent_Template_p_carousel_onPage_1_listener($event) {\n          return ctx.changeSliderDot($event);\n        });\n        i0.ɵɵtemplate(2, MtnMainSliderComponent_ng_template_2_Template, 1, 3, \"ng-template\", 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, MtnMainSliderComponent_div_5_Template, 1, 1, \"div\", 5);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"value\", ctx.sliders)(\"circular\", true)(\"autoplayInterval\", 3000)(\"showIndicators\", false);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.sliderDots);\n      }\n    },\n    dependencies: [i3.PrimeTemplate, i4.NgClass, i4.NgForOf, i4.NgStyle, i5.Carousel],\n    styles: [\".mtn-main-slider .owl-theme .owl-dots {\\n  position: relative !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .slider-height[_ngcontent-%COMP%] {\\n    margin-top: 0rem !important;\\n  }\\n  .slider[_ngcontent-%COMP%] {\\n    margin-top: 18px !important;\\n  }\\n  .mbl-des[_ngcontent-%COMP%] {\\n    height: 170px !important;\\n  }\\n}\\n@media screen and (min-width: 769px) {\\n  .slider-height[_ngcontent-%COMP%] {\\n    margin-top: 8rem !important;\\n  }\\n}\\n\\n\\n.animated[_ngcontent-%COMP%] {\\n  animation-duration: 3000ms;\\n  animation-fill-mode: both;\\n}\\n\\n\\n\\n\\n\\n.owl-animated-out[_ngcontent-%COMP%] {\\n  z-index: 1;\\n}\\n\\n\\n\\n\\n.owl-animated-in[_ngcontent-%COMP%] {\\n  z-index: 0;\\n}\\n\\n\\n\\n.fadeOut[_ngcontent-%COMP%] {\\n  animation-name: _ngcontent-%COMP%_fadeOut;\\n}\\n@keyframes _ngcontent-%COMP%_fadeOut {\\n  0% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0;\\n  }\\n}\\ndiv[_ngcontent-%COMP%] {\\n  height: inherit;\\n}\\ndiv[_ngcontent-%COMP%]   .p-element[_ngcontent-%COMP%] {\\n  height: inherit;\\n}\\ndiv[_ngcontent-%COMP%]   .p-element[_ngcontent-%COMP%]   .p-carousel-items-content[_ngcontent-%COMP%] {\\n  height: inherit !important;\\n}\\n\\n.dot-color[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n  background-color: #FFCB05;\\n  border-radius: 50%;\\n}\\n\\n.center-dot[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.dot-simple[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n  background: rgba(32, 78, 110, 0.5);\\n  border-radius: 50%;\\n}\\n\\n.mtn-main-slider[_ngcontent-%COMP%] {\\n  width: 1300px;\\n}\\n@media screen and (max-width: 768px) {\\n  .mtn-main-slider[_ngcontent-%COMP%] {\\n    width: 90vw;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}