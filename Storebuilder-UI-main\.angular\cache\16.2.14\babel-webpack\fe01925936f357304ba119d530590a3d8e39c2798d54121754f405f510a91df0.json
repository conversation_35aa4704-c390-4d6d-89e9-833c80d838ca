{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/button\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/dialog\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ngx-translate/core\";\nfunction SuccessModalComponent_ng_template_1_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.caution);\n  }\n}\nfunction SuccessModalComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"a\");\n    i0.ɵɵelement(2, \"img\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, SuccessModalComponent_ng_template_1_p_5_Template, 2, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.message, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.caution);\n  }\n}\nfunction SuccessModalComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function SuccessModalComponent_ng_template_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSumbit());\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(2, 1, \"ResponseMessages.okButtonText\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    \"960px\": \"75vw\",\n    \"768px\": \"75vw\"\n  };\n};\nexport class SuccessModalComponent {\n  constructor() {\n    this.displayModal = false;\n    this.message = '';\n    this.caution = '';\n    this.submit = new EventEmitter();\n    this.cancel = new EventEmitter();\n  }\n  ngOnInit() {}\n  onSumbit() {\n    this.submit.emit(true);\n  }\n  static #_ = this.ɵfac = function SuccessModalComponent_Factory(t) {\n    return new (t || SuccessModalComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SuccessModalComponent,\n    selectors: [[\"app-mtn-success-modal\"]],\n    inputs: {\n      displayModal: \"displayModal\",\n      message: \"message\",\n      caution: \"caution\"\n    },\n    outputs: {\n      submit: \"submit\",\n      cancel: \"cancel\"\n    },\n    decls: 3,\n    vars: 6,\n    consts: [[1, \"success-confirmation\", 3, \"visible\", \"breakpoints\", \"resizable\", \"closable\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [1, \"body-container\"], [\"src\", \"assets/images/successfully.svg\", \"alt\", \"No Image\", 1, \"btn-width\"], [1, \"body-content\", 2, \"margin-top\", \"15px\"], [\"class\", \"d-flex justify-content-center sign-caution\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"sign-caution\"], [1, \"d-flex\", \"align-items-center\", \"action-footer\", \"mb-4\"], [\"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-rounded\", \"d-inline-flex\", \"btn-width\", \"confirm-btn\", 3, \"label\", \"click\"]],\n    template: function SuccessModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-dialog\", 0);\n        i0.ɵɵlistener(\"visibleChange\", function SuccessModalComponent_Template_p_dialog_visibleChange_0_listener($event) {\n          return ctx.displayModal = $event;\n        });\n        i0.ɵɵtemplate(1, SuccessModalComponent_ng_template_1_Template, 6, 2, \"ng-template\", 1);\n        i0.ɵɵtemplate(2, SuccessModalComponent_ng_template_2_Template, 3, 3, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"visible\", ctx.displayModal)(\"breakpoints\", i0.ɵɵpureFunction0(5, _c0))(\"resizable\", false)(\"closable\", false)(\"modal\", true);\n      }\n    },\n    dependencies: [i1.ButtonDirective, i2.PrimeTemplate, i3.Dialog, i4.NgIf, i5.TranslatePipe],\n    styles: [\".p-dialog .p-dialog-footer {\\n  text-align: center;\\n}\\n\\n  .p-dialog .p-dialog-header {\\n  padding: 0.5rem;\\n}\\n\\n  .p-dialog .p-dialog-footer button {\\n  width: 100%;\\n  margin: 0 !important;\\n}\\n\\n.success-confirmation[_ngcontent-%COMP%]     .p-dialog {\\n  max-width: 390px;\\n  background: white;\\n  height: 428px;\\n}\\n\\n  .p-dialog .p-dialog-header .p-dialog-title {\\n  font-family: var(--bold-font) !important;\\n  font-style: normal;\\n  font-weight: 700;\\n  font-size: 18px;\\n  line-height: 23px;\\n  color: #000000;\\n  margin-top: 15px;\\n}\\n\\n  .p-dialog-draggable .p-dialog-header {\\n  justify-content: center;\\n}\\n\\n.body-container[_ngcontent-%COMP%] {\\n  padding: 45px 30px 45px 30px !important;\\n}\\n\\n.body-content[_ngcontent-%COMP%] {\\n  color: #000000;\\n  font-family: var(--bold-font) !important;\\n  font-style: normal;\\n  font-weight: 700;\\n  font-size: 18px;\\n  line-height: 23px;\\n  text-align: center;\\n}\\n\\n.attributes-name[_ngcontent-%COMP%] {\\n  flex-direction: row;\\n  align-items: flex-start;\\n  padding: 8px 16px;\\n  width: 100%;\\n  height: 60px;\\n  background: #F5F5F5;\\n  border-bottom: 1px solid #A3A3A3;\\n  margin: 20px 0px;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  font-family: var(--bold-font) !important;\\n  display: flex;\\n  align-items: center;\\n  letter-spacing: 0.5px;\\n  background: #F5F5F5;\\n  border: none;\\n  width: 100%;\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 12px;\\n  line-height: 20px;\\n  \\n\\n  color: #323232;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n  .model img {\\n  width: 50px;\\n  height: 50px;\\n  margin-bottom: 30px;\\n  margin-top: 70px;\\n}\\n  .model p {\\n  color: #000;\\n  font-size: 18px;\\n  font-family: var(--medium-font) !important;\\n  margin-bottom: 114px;\\n  text-align: center;\\n  line-height: 25px;\\n  padding-right: 28px;\\n  padding-left: 28px;\\n}\\n\\n.confirm-btn[_ngcontent-%COMP%] {\\n  background: var(--header_bgcolor) !important;\\n  border-color: var(--header_bgcolor) !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #F5F5F5 !important;\\n  border-color: #F5F5F5 !important;\\n  color: #000 !important;\\n}\\n\\n.btn-width[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  height: 52px !important;\\n  justify-content: center !important;\\n  align-items: center !important;\\n  padding: 0px 16px !important;\\n  border-radius: 52px !important;\\n}\\n\\n.action-footer[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\n\\n.success-confirmation[_ngcontent-%COMP%]     .p-dialog-content {\\n  border: none;\\n}\\n\\n.sign-caution[_ngcontent-%COMP%] {\\n  color: #000;\\n  text-align: center;\\n  font-family: var(--regular-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n\\n@media screen and (max-width: 768px) {\\n    .p-dialog .p-dialog-footer {\\n    padding: 0 2rem 2rem 2rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "caution", "ɵɵelement", "ɵɵtemplate", "SuccessModalComponent_ng_template_1_p_5_Template", "ɵɵtextInterpolate1", "ctx_r0", "message", "ɵɵproperty", "ɵɵlistener", "SuccessModalComponent_ng_template_2_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onSumbit", "ɵɵpropertyInterpolate", "ɵɵpipeBind1", "SuccessModalComponent", "constructor", "displayModal", "submit", "cancel", "ngOnInit", "emit", "_", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SuccessModalComponent_Template", "rf", "ctx", "SuccessModalComponent_Template_p_dialog_visibleChange_0_listener", "$event", "SuccessModalComponent_ng_template_1_Template", "SuccessModalComponent_ng_template_2_Template", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\success-modal\\success-modal.component.ts", "C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\shared\\modals\\success-modal\\success-modal.component.html"], "sourcesContent": ["import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-mtn-success-modal',\r\n  templateUrl: './success-modal.component.html',\r\n  styleUrls: ['./success-modal.component.scss'],\r\n\r\n})\r\nexport class SuccessModalComponent implements OnInit {\r\n  @Input() displayModal: boolean = false;\r\n  @Input() message: string = '';\r\n  @Input() caution: string = '';\r\n\r\n  @Output() submit = new EventEmitter<boolean>();\r\n  @Output() cancel = new EventEmitter<boolean>();\r\n\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  onSumbit() {\r\n    this.submit.emit(true);\r\n  }\r\n\r\n}\r\n", "<p-dialog class=\"success-confirmation\" [(visible)]=\"displayModal\" [breakpoints]=\"{ '960px': '75vw', '768px': '75vw' }\"\r\n  [resizable]=\"false\" [closable]=\"false\" [modal]=\"true\">\r\n  <ng-template pTemplate=\"content\">\r\n    <div class=\" flex flex-column align-items-center gap-3 \" class=\"body-container\">\r\n      <a>\r\n        <img class=\"btn-width\" src=\"assets/images/successfully.svg\" alt=\"No Image\" />\r\n      </a>\r\n      <p class=\"body-content\" style=\"margin-top: 15px\">\r\n        {{message}}\r\n      </p>\r\n      <p class=\"d-flex justify-content-center sign-caution\" *ngIf=\"caution\">{{caution}}</p>\r\n    </div>\r\n  </ng-template>\r\n  <ng-template pTemplate=\"footer\">\r\n    <div class=\" d-flex align-items-center action-footer mb-4\">\r\n      <button pButton pRipple label=\"{{'ResponseMessages.okButtonText' | translate}}\"\r\n        class=\"p-button-rounded d-inline-flex btn-width confirm-btn\" (click)=\"onSumbit()\">\r\n      </button>\r\n\r\n    </div>\r\n  </ng-template>\r\n</p-dialog>"], "mappings": "AAAA,SAAmBA,YAAY,QAA8B,eAAe;;;;;;;;;ICUtEC,EAAA,CAAAC,cAAA,WAAsE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAW;;;;;IAPnFP,EAAA,CAAAC,cAAA,aAAgF;IAE5ED,EAAA,CAAAQ,SAAA,aAA6E;IAC/ER,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,WAAiD;IAC/CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAS,UAAA,IAAAC,gDAAA,eAAqF;IACvFV,EAAA,CAAAG,YAAA,EAAM;;;;IAHFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;IACuDb,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAc,UAAA,SAAAF,MAAA,CAAAL,OAAA,CAAa;;;;;;IAItEP,EAAA,CAAAC,cAAA,aAA2D;IAEMD,EAAA,CAAAe,UAAA,mBAAAC,qEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;;IACnFtB,EAAA,CAAAG,YAAA,EAAS;;;IAFeH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAuB,qBAAA,UAAAvB,EAAA,CAAAwB,WAAA,wCAAuD;;;;;;;;;ADPrF,OAAM,MAAOC,qBAAqB;EANlCC,YAAA;IAOW,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAd,OAAO,GAAW,EAAE;IACpB,KAAAN,OAAO,GAAW,EAAE;IAEnB,KAAAqB,MAAM,GAAG,IAAI7B,YAAY,EAAW;IACpC,KAAA8B,MAAM,GAAG,IAAI9B,YAAY,EAAW;;EAG9C+B,QAAQA,CAAA,GACR;EAEAR,QAAQA,CAAA;IACN,IAAI,CAACM,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;EACxB;EAAC,QAAAC,CAAA,G;qBAdUP,qBAAqB;EAAA;EAAA,QAAAQ,EAAA,G;UAArBR,qBAAqB;IAAAS,SAAA;IAAAC,MAAA;MAAAR,YAAA;MAAAd,OAAA;MAAAN,OAAA;IAAA;IAAA6B,OAAA;MAAAR,MAAA;MAAAC,MAAA;IAAA;IAAAQ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRlC1C,EAAA,CAAAC,cAAA,kBACwD;QADjBD,EAAA,CAAAe,UAAA,2BAAA6B,iEAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAhB,YAAA,GAAAkB,MAAA;QAAA,EAA0B;QAE/D7C,EAAA,CAAAS,UAAA,IAAAqC,4CAAA,yBAUc;QACd9C,EAAA,CAAAS,UAAA,IAAAsC,4CAAA,yBAOc;QAChB/C,EAAA,CAAAG,YAAA,EAAW;;;QArB4BH,EAAA,CAAAc,UAAA,YAAA6B,GAAA,CAAAhB,YAAA,CAA0B,gBAAA3B,EAAA,CAAAgD,eAAA,IAAAC,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}