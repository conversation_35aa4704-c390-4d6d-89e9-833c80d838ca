{"ast": null, "code": "import { i as elementTransitionEnd } from './utils.mjs';\nfunction effectVirtualTransitionEnd(_ref) {\n  let {\n    swiper,\n    duration,\n    transformElements,\n    allSlides\n  } = _ref;\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.filter(slideEl => slideEl.shadowRoot && slideEl.shadowRoot === el.parentNode)[0];\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\nexport { effectVirtualTransitionEnd as e };", "map": {"version": 3, "names": ["i", "elementTransitionEnd", "effectVirtualTransitionEnd", "_ref", "swiper", "duration", "transformElements", "allSlides", "activeIndex", "getSlide", "el", "parentElement", "slide", "slides", "filter", "slideEl", "shadowRoot", "parentNode", "params", "virtualTranslate", "eventTriggered", "transitionEndTarget", "transformEl", "classList", "contains", "getSlideIndex", "for<PERSON>ach", "destroyed", "animating", "evt", "window", "CustomEvent", "bubbles", "cancelable", "wrapperEl", "dispatchEvent", "e"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/swiper/shared/effect-virtual-transition-end.mjs"], "sourcesContent": ["import { i as elementTransitionEnd } from './utils.mjs';\n\nfunction effectVirtualTransitionEnd(_ref) {\n  let {\n    swiper,\n    duration,\n    transformElements,\n    allSlides\n  } = _ref;\n  const {\n    activeIndex\n  } = swiper;\n  const getSlide = el => {\n    if (!el.parentElement) {\n      // assume shadow root\n      const slide = swiper.slides.filter(slideEl => slideEl.shadowRoot && slideEl.shadowRoot === el.parentNode)[0];\n      return slide;\n    }\n    return el.parentElement;\n  };\n  if (swiper.params.virtualTranslate && duration !== 0) {\n    let eventTriggered = false;\n    let transitionEndTarget;\n    if (allSlides) {\n      transitionEndTarget = transformElements;\n    } else {\n      transitionEndTarget = transformElements.filter(transformEl => {\n        const el = transformEl.classList.contains('swiper-slide-transform') ? getSlide(transformEl) : transformEl;\n        return swiper.getSlideIndex(el) === activeIndex;\n      });\n    }\n    transitionEndTarget.forEach(el => {\n      elementTransitionEnd(el, () => {\n        if (eventTriggered) return;\n        if (!swiper || swiper.destroyed) return;\n        eventTriggered = true;\n        swiper.animating = false;\n        const evt = new window.CustomEvent('transitionend', {\n          bubbles: true,\n          cancelable: true\n        });\n        swiper.wrapperEl.dispatchEvent(evt);\n      });\n    });\n  }\n}\n\nexport { effectVirtualTransitionEnd as e };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,oBAAoB,QAAQ,aAAa;AAEvD,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EACxC,IAAI;IACFC,MAAM;IACNC,QAAQ;IACRC,iBAAiB;IACjBC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAM;IACJK;EACF,CAAC,GAAGJ,MAAM;EACV,MAAMK,QAAQ,GAAGC,EAAE,IAAI;IACrB,IAAI,CAACA,EAAE,CAACC,aAAa,EAAE;MACrB;MACA,MAAMC,KAAK,GAAGR,MAAM,CAACS,MAAM,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACC,UAAU,IAAID,OAAO,CAACC,UAAU,KAAKN,EAAE,CAACO,UAAU,CAAC,CAAC,CAAC,CAAC;MAC5G,OAAOL,KAAK;IACd;IACA,OAAOF,EAAE,CAACC,aAAa;EACzB,CAAC;EACD,IAAIP,MAAM,CAACc,MAAM,CAACC,gBAAgB,IAAId,QAAQ,KAAK,CAAC,EAAE;IACpD,IAAIe,cAAc,GAAG,KAAK;IAC1B,IAAIC,mBAAmB;IACvB,IAAId,SAAS,EAAE;MACbc,mBAAmB,GAAGf,iBAAiB;IACzC,CAAC,MAAM;MACLe,mBAAmB,GAAGf,iBAAiB,CAACQ,MAAM,CAACQ,WAAW,IAAI;QAC5D,MAAMZ,EAAE,GAAGY,WAAW,CAACC,SAAS,CAACC,QAAQ,CAAC,wBAAwB,CAAC,GAAGf,QAAQ,CAACa,WAAW,CAAC,GAAGA,WAAW;QACzG,OAAOlB,MAAM,CAACqB,aAAa,CAACf,EAAE,CAAC,KAAKF,WAAW;MACjD,CAAC,CAAC;IACJ;IACAa,mBAAmB,CAACK,OAAO,CAAChB,EAAE,IAAI;MAChCT,oBAAoB,CAACS,EAAE,EAAE,MAAM;QAC7B,IAAIU,cAAc,EAAE;QACpB,IAAI,CAAChB,MAAM,IAAIA,MAAM,CAACuB,SAAS,EAAE;QACjCP,cAAc,GAAG,IAAI;QACrBhB,MAAM,CAACwB,SAAS,GAAG,KAAK;QACxB,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAACC,WAAW,CAAC,eAAe,EAAE;UAClDC,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE;QACd,CAAC,CAAC;QACF7B,MAAM,CAAC8B,SAAS,CAACC,aAAa,CAACN,GAAG,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF;AAEA,SAAS3B,0BAA0B,IAAIkC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}