import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Category } from '@core/interface';

@Component({
  selector: 'app-category-list',
  templateUrl: './category-list.component.html',
  standalone: true,
  imports: [CommonModule]
})
export class CategoryListComponent {
  @Input() categories: Category[] = [];
  @Output() selectCategory = new EventEmitter<Category>();
}
