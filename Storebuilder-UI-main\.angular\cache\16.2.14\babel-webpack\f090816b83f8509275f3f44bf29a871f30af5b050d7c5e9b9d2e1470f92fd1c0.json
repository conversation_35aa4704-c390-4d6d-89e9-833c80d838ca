{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { catchError, Observable, Subject, tap, throwError } from 'rxjs';\nimport { JwtHelperService } from \"@auth0/angular-jwt\";\nimport { RefreshTokenViewModel } from '../interfaces/auth';\nimport jwt_decode from \"jwt-decode\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../shared/services/store.service\";\nimport * as i2 from \"../shared/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"../shared/services/token.service\";\nexport class AppInterceptor {\n  constructor(platformId, store, authService, router, messageService, cookieService, authTokenService) {\n    this.platformId = platformId;\n    this.store = store;\n    this.authService = authService;\n    this.router = router;\n    this.messageService = messageService;\n    this.cookieService = cookieService;\n    this.authTokenService = authTokenService;\n    this.userToken = '';\n    this.XXSRFTOKEN = '';\n    this.helper = new JwtHelperService();\n    this.isRefreshTokenCalled = false;\n    this.isExpired = false;\n    this.called = false;\n    ///////\n    this.refreshTokenInProgress = false;\n    this.tokenRefreshedSource = new Subject();\n    this.tokenRefreshed$ = this.tokenRefreshedSource.asObservable();\n    this.guestToken = '';\n    this.headers = {\n      authorization: ' ',\n      token: '',\n      lang: '',\n      tenantId: '',\n      currency: '',\n      accept: 'application/json',\n      contentType: 'application/json',\n      allowOrigin: '*',\n      allowHeaders: 'Cookie, Cache-Control, Host, User-Agent, Accept, token, Authorization, currency, lang, origin, x-requested-with, content-type, Accept-Encoding',\n      allowMethods: 'PUT, GET, POST, DELETE, PATCH, OPTIONS'\n    };\n    this.getTimeInterval();\n  }\n  addAHeaders(request) {\n    this.userToken = this.cookieService.get('authToken');\n    if (request.url.includes(\"RefreshToken\") || request.url.includes(\"Login\") || request.url.includes(\"ForgotPassword\")) {\n      this.headers.Authorization = '';\n    } else {\n      this.headers.Authorization = this.userToken == '' ? '' : `Bearer ${this.userToken}`;\n    }\n    this.tenantId = localStorage.getItem('tenantId');\n    if (this.tenantId) {\n      this.headers.tenantId = this.tenantId;\n    }\n    this.headers.lang = localStorage.getItem('lang') || 'en';\n    var currency = localStorage.getItem('currency')?.toString();\n    this.headers.currency = currency ? currency : 'UGX';\n    this.headers.XXSRFTOKEN = this.store.get('XXSRFTOKEN') || localStorage.getItem('XXSRFTOKEN');\n    if (this.headers.XXSRFTOKEN == undefined) {\n      this.headers.XXSRFTOKEN = '';\n    }\n    let tenantId = window.localStorage.getItem('testingTenantId') || '';\n    this.clonedRequest = request.clone({\n      // withCredentials: true,\n      headers: request.headers.set('Authorization', this.headers.Authorization).set('token', this.headers.token).set('tenantId', this.headers.tenantId).set('lang', this.headers.lang).set('currency', this.headers.currency).set('Accept', this.headers.accept).set('Content-Type', this.headers.contentType).set('X-XSRF-TOKEN', this.headers.XXSRFTOKEN)\n      // .set('TenantId', tenantId)\n      // .set('Access-Control-Allow-Origin', this.headers.allowOrigin)\n      // .set('Access-Control-Allow-Headers', this.headers.allowHeaders)\n      // .set('Access-Control-Allow-Methods', this.headers.allowMethods)\n      // .set(\"Access-Control-Allow-Credentials\", 'true')\n      ,\n\n      withCredentials: true\n    });\n    return this.clonedRequest;\n  }\n  refreshToken() {\n    if (this.refreshTokenInProgress) {\n      return new Observable(observer => {\n        this.tokenRefreshed$.subscribe(() => {\n          observer.next();\n          observer.complete();\n        });\n      });\n    } else {\n      this.refreshTokenInProgress = true;\n      this.userToken = this.cookieService.get('authToken');\n      let refreshToken = localStorage.getItem('refreshToken');\n      var model = new RefreshTokenViewModel();\n      model.AuthToken = this.userToken;\n      model.RefreshToken = refreshToken;\n      return this.authService.refreshToken(model).pipe(tap(res => {\n        this.refreshTokenInProgress = false;\n        let token = res.data.authToken;\n        this.decoded = jwt_decode(token);\n        // const date = new Date(this.decoded.exp);\n        let days = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n        const dateNow = new Date();\n        dateNow.setDate(dateNow.getDate() + parseInt(days));\n        this.cookieService.set('authToken', token, {\n          expires: dateNow,\n          path: '/',\n          sameSite: 'Strict'\n        });\n        localStorage.setItem('refreshToken', res.data.refreshToken);\n        this.tokenRefreshedSource.next();\n      }), catchError(error => {\n        this.refreshTokenInProgress = false;\n        this.messageService.add({\n          severity: 'error',\n          summary: \"Oops\",\n          detail: 'Something went wrong,please login again'\n        });\n        this.signOut();\n      }));\n    }\n  }\n  handleResponseError(error, request, next) {\n    //orgional code\n    // // Business error\n    // if (error.status === 400) {\n    //     // Show message\n    // }\n    // Invalid token error\n    if (error.status == 401 || error.status == 0) {\n      if ((this.router.url.includes('login') || this.router.url.includes('register')) && error.status != 401) {\n        this.messageService.add({\n          severity: 'error',\n          detail: error.error && error.error.message ? error.error.message : 'Something went wrong'\n        });\n      } else {\n        this.signOut();\n      }\n      // this.userToken = this.cookieService.get('authToken');\n      // const isExpired = this.helper.isTokenExpired(this.userToken);\n      // if( isExpired==false || this.userToken==''){\n      //   this.messageService.add({\n      //     severity: 'error',\n      //     summary: \"Oops\",\n      //     detail:'Something went wrong,please login again'\n      // });\n      // this.userToken = this.cookieService.get('authToken');\n      // const isExpired = this.helper.isTokenExpired(this.userToken);\n      // refresh token code commented\n      //   if( isExpired==false ){\n      //   }\n      // this.userToken = this.cookieService.get('authToken');\n      // let refreshToken:any= localStorage.getItem('refreshToken')\n      // refresh token code commented\n      // if(this.userToken && refreshToken){\n      // return this.refreshToken().pipe(\n      //     switchMap(() => {\n      //         request = this.addAHeaders(request);\n      //         return next.handle(request);\n      //     }),\n      //     catchError((e):any => {\n      //         if (e.status !== 401) {\n      //             return this.handleResponseError(e);\n      //         } else {\n      //           this.messageService.add({\n      //             severity: 'error',\n      //       summary: \"Oops\",\n      //       detail:'Something went wrong,please login again'\n      //       });\n      //             this.signOut();\n      //         }\n      //     }));\n      // }\n      // else{\n      //   this.signOut();\n      // }\n    }\n    // Access denied error\n    // else if (error.status === 403) {\n    //   this.messageService.add({\n    //     severity: 'error',\n    //     summary: \"Access Denied\",\n    //   });\n    //     // Show message\n    //     // Logout\n    //     this.signOut();\n    // }\n    // // Server error\n    // else if (error.status === 500) {\n    //     // Show message\n    //     this.messageService.add({\n    //       severity: 'error',\n    //       summary: \"Internal Server Error\",\n    //     });\n    // }\n    // // Maintenance error\n    // else if (error.status === 503) {\n    //   this.messageService.add({\n    //     severity: 'error',\n    //     summary: \"Server Down Due to Maintainance\",\n    //   });\n    //     // Show message\n    //     // Redirect to the maintenance page\n    // }\n    // else if (error == 'Not Found') {\n    //             this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {\n    //               this.router.navigate(['not-found']);\n    //             });\n    // }\n    else {\n      this.messageService.add({\n        severity: 'error',\n        detail: error.error && error.error.message ? error.error.message : 'Something went wrong'\n      });\n      // this.signOut();\n    }\n    //  else if (error == 'Unknown Error') {\n    //   console.log('unknown Error')\n    //   // this.signOut();\n    //   // this.handleAuthorizationError();\n    // }\n    // else if(error.status ===0)\n    // {\n    //   this.messageService.add({\n    //     severity: 'error',\n    //     summary: \"Unknown Error\",\n    //   });\n    //   this.signOut();\n    // }\n    // removed working code\n    // else if(\n    //   error == 'Unauthorized' ||\n    //   error == 'Unknown Error' ||\n    //   error?.statusText == 'Unauthorized' ||\n    //   error?.statusText == 'Unknown Error'\n    // )\n    // {\n    //   this.userToken = this.cookieService.get('authToken');\n    //   const isExpired = this.helper.isTokenExpired(this.userToken);\n    //   if(request.url.includes(\"RefreshToken\") || isExpired==false){\n    //     this.signOut();\n    //     return null;\n    //   }\n    //   this.userToken = this.cookieService.get('authToken');\n    //   let refreshToken:any= localStorage.getItem('refreshToken')\n    //   if(this.userToken && refreshToken){\n    //   return this.refreshToken().pipe(\n    //     switchMap(() => {\n    //         request = this.addAHeaders(request);\n    //         return next.handle(request);\n    //     }),\n    //     catchError((e):any => {\n    //         if (e.status !== 401) {\n    //             return this.handleResponseError(e);\n    //         } else {\n    //             this.signOut();\n    //         }\n    //     }));\n    //   }else{\n    //     this.signOut();\n    //   }\n    // }\n    return throwError(error);\n    // our code\n  }\n\n  intercept(request, next) {\n    // this.authService = this.injector.get(AuthService);\n    // Handle request\n    request = this.addAHeaders(request);\n    console.log('request', request);\n    // Handle response\n    return next.handle(request).pipe(catchError(error => {\n      return this.handleResponseError(error, request, next);\n    }));\n  }\n  //   intercept(request: HttpRequest<unknown>, next: HttpHandler): any {\n  //     if(request.url.includes(\"RefreshToken\")){\n  //       this.clonedRequest = request.clone({\n  //         // withCredentials: true,\n  //         headers: undefined\n  //       });\n  //     }\n  //     else{\n  //       this.userToken = this.cookieService.get('authToken');\n  //       console.log('user token',this.userToken);\n  //       this.headers.Authorization = (this.userToken == '') ? '' : `Bearer ${this.userToken}`;\n  //       this.isExpired = false\n  //     return next.handle(this.clonedRequest).pipe(\n  //       tap((event: HttpEvent<any>) => {\n  //         if (event instanceof HttpResponse) {\n  //           if (event.status === 401) {\n  //             console.log('sadsadsaddsdsad2');\n  //             this.signOut();\n  //           }\n  //           if (event.status == 404) {\n  //             this.NotFound();\n  //           }\n  //           var token = event.headers.get('XSRF-TOKEN');\n  //           if (token != null && token != '') {\n  //             localStorage.setItem('XXSRFTOKEN', token);\n  //           }\n  //         }\n  //         return event;\n  //       }), catchError((error) => {\n  //         if (error == 'Not Found') {\n  //           this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {\n  //             this.router.navigate(['not-found']);\n  //           });\n  //         } else if (error == 'Unknown Error') {\n  //           console.log('unknown Error')\n  //           // this.signOut();\n  //           // this.handleAuthorizationError();\n  //         }\n  //         else if(error=='Unauthorized')\n  //         {\n  //           let refreshToken:any= localStorage.getItem('refreshToken')\n  //           // if (!this.isRefreshTokenCalled)\n  //           // {\n  //           //   this.isRefreshTokenCalled = true\n  //           //   var model = new RefreshTokenViewModel();\n  //           //   model.AuthToken = this.userToken\n  //           //   model.RefreshToken = refreshToken\n  //           //   if (model.AuthToken && model.RefreshToken) {\n  //           //     this.authService.refreshToken(model).subscribe({\n  //           //       next: (res: any) => {\n  //           //         if (res.data != null) {\n  //           //           let token = res.data.authToken;\n  //           //           let decoded: any = jwt_decode(token);\n  //           //           let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n  //           //           const dateNow = new Date();\n  //           //           dateNow.setDate(dateNow.getDate() + parseInt(days));\n  //           //           this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\n  //           //           localStorage.setItem('refreshToken', res.data.refreshToken);\n  //           //           this.authService.refreshToken(model).subscribe();\n  //           //           } else {\n  //           //           model.AuthToken = '';\n  //           //           model.RefreshToken = '';\n  //           //         }\n  //           //         this.isRefreshTokenCalled = false\n  //           //         // this.triggerInitialCalls()\n  //           //       },\n  //           //       error: (err: any) => {\n  //           //         console.error(err);\n  //           //         model.AuthToken = '';\n  //           //         model.RefreshToken = '';\n  //           //         this.signOut();\n  //           //         this.isRefreshTokenCalled = false\n  //           //       }\n  //           //     });\n  //           //   } else {\n  //           //     model.AuthToken = '';\n  //           //     model.RefreshToken = '';\n  //           //     this.signOut();\n  //           //   }\n  //           // }\n  //           let promise=new Promise((resolve,reject)=>{\n  //             resolve('resolved')\n  //           })\n  //           promise.then(res=>{\n  //             next.handle(this.clonedRequest)\n  //           })\n  //         }\n  //         return throwError(error);\n  //       })\n  //     );\n  //   }\n  // }\n  handleAuthorizationError() {\n    let promise = new Promise((resolve, reject) => {});\n    ///////////////////////\n    // var model = new RefreshTokenViewModel();\n    // this.authTokenService.authTokenData.subscribe(message => model.AuthToken = message);\n    // model.AuthToken = this.cookieService.get('authToken');\n    // // model.authToken = this.authTokenService.authTokenData.subscribe(message => message);\n    // model.RefreshToken = localStorage.getItem('refreshToken')?.toString();\n    // if (model.AuthToken && model.RefreshToken) {\n    //   this.authService.refreshToken(model).subscribe({\n    //     next: (res: any) => {\n    //       if (res.data != null) {\n    //         let token = res.data.authToken;\n    //         this.decoded = jwt_decode(token);\n    //         // const date = new Date(this.decoded.exp);\n    //         let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n    //         const dateNow = new Date();\n    //         dateNow.setDate(dateNow.getDate() + parseInt(days));\n    //         this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\n    //         localStorage.setItem('refreshToken', res.data.refreshToken);\n    //       } else {\n    //         model.AuthToken = '';\n    //         model.RefreshToken = '';\n    //         this.signOut();\n    //       }\n    //       this.isRefreshTokenCalled = false\n    //     },\n    //     error: (err: any) => {\n    //       console.error(err);\n    //       model.AuthToken = '';\n    //       model.RefreshToken = '';\n    //       this.signOut();\n    //     }\n    //   });\n    // } else {\n    //   model.AuthToken = '';\n    //   model.RefreshToken = '';\n    //   this.signOut();\n    // }\n  }\n\n  signOut() {\n    this.setStoreData();\n    this.authTokenService.authTokenSet(\"\");\n    // this.cookieService.delete('authToken');\n    this.cookieService.delete('authToken', '/');\n    this.store.set('cartProducts', '');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('auth_enc');\n    this.router.navigate(['/login']);\n  }\n  NotFound() {\n    this.router.navigate(['/not-found']);\n  }\n  getTimeInterval() {\n    if (this.store.get('timeInterval')) {\n      this.store.subscription('timeInterval').subscribe({\n        next: res => {\n          if (res) {\n            this.timeInterval = res;\n          }\n        },\n        error: err => {\n          console.error(err);\n        }\n      });\n    }\n  }\n  setStoreData() {\n    if (this.store.localStoreNames.length) {\n      // this.store.set('authToken', null);\n      this.store.set('refreshToken', null);\n      this.store.set('profile', null);\n      this.store.set('cartProducts', []);\n      this.store.set('favouritesProducts', []);\n      this.store.set('compareProducts', []);\n      this.store.set('socialAccount', null);\n      this.store.set('XXSRFTOKEN', null);\n      this.store.set('notifications', {\n        notifications: [],\n        unreadNotifications: 0\n      });\n      this.store.set('checkoutData', {\n        shipping: null,\n        payment: null,\n        promo: null,\n        steps: null,\n        profile: null,\n        orderId: null\n      });\n    } else {\n      // localStorage.setItem('authToken', '');\n      localStorage.setItem('refreshToken', '');\n      localStorage.setItem('timeInterval', '');\n      localStorage.setItem('TenantId', '');\n      localStorage.setItem('userPhone', '');\n      localStorage.setItem('profile', '');\n      localStorage.setItem('cartProducts', JSON.stringify([]));\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\n      localStorage.setItem('compareProducts', JSON.stringify([]));\n      localStorage.setItem('XXSRFTOKEN', '');\n    }\n  }\n  static #_ = this.ɵfac = function AppInterceptor_Factory(t) {\n    return new (t || AppInterceptor)(i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i1.StoreService), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.Router), i0.ɵɵinject(i4.MessageService), i0.ɵɵinject(i5.CookieService), i0.ɵɵinject(i6.AuthTokenService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AppInterceptor,\n    factory: AppInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "catchError", "Observable", "Subject", "tap", "throwError", "JwtHelperService", "RefreshTokenViewModel", "jwt_decode", "AppInterceptor", "constructor", "platformId", "store", "authService", "router", "messageService", "cookieService", "authTokenService", "userToken", "XXSRFTOKEN", "helper", "isRefreshTokenCalled", "isExpired", "called", "refreshTokenInProgress", "tokenRefreshedSource", "tokenRefreshed$", "asObservable", "guestToken", "headers", "authorization", "token", "lang", "tenantId", "currency", "accept", "contentType", "<PERSON><PERSON><PERSON><PERSON>", "allowHeaders", "allowMethods", "getTimeInterval", "addAHeaders", "request", "get", "url", "includes", "Authorization", "localStorage", "getItem", "toString", "undefined", "window", "clonedRequest", "clone", "set", "withCredentials", "refreshToken", "observer", "subscribe", "next", "complete", "model", "AuthToken", "RefreshToken", "pipe", "res", "data", "authToken", "decoded", "days", "exp", "toFixed", "dateNow", "Date", "setDate", "getDate", "parseInt", "expires", "path", "sameSite", "setItem", "error", "add", "severity", "summary", "detail", "signOut", "handleResponseError", "status", "message", "intercept", "console", "log", "handle", "handleAuthorizationError", "promise", "Promise", "resolve", "reject", "setStoreData", "authTokenSet", "delete", "removeItem", "navigate", "NotFound", "subscription", "timeInterval", "err", "localStoreNames", "length", "notifications", "unreadNotifications", "shipping", "payment", "promo", "steps", "profile", "orderId", "JSON", "stringify", "_", "i0", "ɵɵinject", "i1", "StoreService", "i2", "AuthService", "i3", "Router", "i4", "MessageService", "i5", "CookieService", "i6", "AuthTokenService", "_2", "factory", "ɵfac"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\config\\app.interceptor.ts"], "sourcesContent": ["import { Inject, Injectable, PLATFORM_ID } from '@angular/core';\r\nimport {\r\n  HttpRequest,\r\n  HttpHandler,\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n  HttpResponse,\r\n  HttpErrorResponse\r\n} from '@angular/common/http';\r\nimport { catchError, delay, Observable, Subject, switchMap, tap, throwError } from 'rxjs';\r\nimport { StoreService } from \"../shared/services/store.service\";\r\nimport { isPlatformBrowser } from \"@angular/common\";\r\nimport { Router } from \"@angular/router\";\r\nimport { JwtHelperService } from \"@auth0/angular-jwt\";\r\nimport { MessageService } from 'primeng/api';\r\nimport { AuthService } from '../shared/services/auth.service';\r\nimport { RefreshTokenViewModel } from '../interfaces/auth';\r\nimport { stringify } from 'querystring';\r\nimport { CookieService } from 'ngx-cookie-service';\r\nimport { AuthTokenService } from '../shared/services/token.service';\r\nimport { threadId } from 'worker_threads';\r\nimport { environment } from \"../../environments/environment\";\r\nimport jwt_decode from \"jwt-decode\";\r\n\r\n\r\ndeclare var CryptoJS: any;\r\n\r\n@Injectable()\r\nexport class AppInterceptor implements HttpInterceptor {\r\n  headers: any;\r\n  userToken: any = '';\r\n  tenantId: any;\r\n  guestToken: string;\r\n  clonedRequest: any;\r\n  timeInterval: any;\r\n  XXSRFTOKEN: string = '';\r\n  decoded: any;\r\n  helper = new JwtHelperService();\r\n  isRefreshTokenCalled: boolean = false;\r\n  isExpired=false;\r\n  called=false;\r\n\r\n\r\n  ///////\r\n\r\n\r\n  refreshTokenInProgress = false;\r\n  tokenRefreshedSource = new Subject<void>();\r\n  tokenRefreshed$ = this.tokenRefreshedSource.asObservable();\r\n\r\n  constructor(\r\n    @Inject(PLATFORM_ID) private platformId: any,\r\n    private store: StoreService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private messageService: MessageService,\r\n    private cookieService: CookieService,\r\n    private authTokenService: AuthTokenService) {\r\n    this.guestToken = '';\r\n    this.headers = {\r\n      authorization: ' ',\r\n      token: '',\r\n      lang: '',\r\n      tenantId: '',\r\n      currency: '',\r\n      accept: 'application/json',\r\n      contentType: 'application/json',\r\n      allowOrigin: '*',\r\n      allowHeaders: 'Cookie, Cache-Control, Host, User-Agent, Accept, token, Authorization, currency, lang, origin, x-requested-with, content-type, Accept-Encoding',\r\n      allowMethods: 'PUT, GET, POST, DELETE, PATCH, OPTIONS'\r\n    };\r\n    this.getTimeInterval();\r\n\r\n  }\r\n  addAHeaders(request:any) {\r\n\r\n\r\n    this.userToken = this.cookieService.get('authToken');\r\n    if(request.url.includes(\"RefreshToken\") ||\r\n      request.url.includes(\"Login\") ||\r\n      request.url.includes(\"ForgotPassword\")){\r\n\r\n    this.headers.Authorization = '';\r\n    }\r\n    else{\r\n      this.headers.Authorization = (this.userToken == '') ? '' : `Bearer ${this.userToken}`;\r\n    }\r\n\r\n\r\n    this.tenantId = localStorage.getItem('tenantId');\r\n    if (this.tenantId) {\r\n      this.headers.tenantId = this.tenantId;\r\n    }\r\n\r\n    this.headers.lang = localStorage.getItem('lang') || 'en';\r\n    var currency = localStorage.getItem('currency')?.toString();\r\n    this.headers.currency = currency ? currency : 'UGX';\r\n\r\n    this.headers.XXSRFTOKEN = this.store.get('XXSRFTOKEN') || localStorage.getItem('XXSRFTOKEN');\r\n    if (this.headers.XXSRFTOKEN == undefined) {\r\n      this.headers.XXSRFTOKEN = '';\r\n    }\r\n  let tenantId = window.localStorage.getItem('testingTenantId') || '';\r\n\r\n\r\n    this.clonedRequest = request.clone({\r\n      // withCredentials: true,\r\n      headers: request.headers\r\n        .set('Authorization', this.headers.Authorization)\r\n        .set('token', this.headers.token)\r\n        .set('tenantId', this.headers.tenantId)\r\n        .set('lang', this.headers.lang)\r\n        .set('currency', this.headers.currency)\r\n        .set('Accept', this.headers.accept)\r\n        .set('Content-Type', this.headers.contentType)\r\n        .set('X-XSRF-TOKEN', this.headers.XXSRFTOKEN)\r\n      // .set('TenantId', tenantId)\r\n      // .set('Access-Control-Allow-Origin', this.headers.allowOrigin)\r\n      // .set('Access-Control-Allow-Headers', this.headers.allowHeaders)\r\n      // .set('Access-Control-Allow-Methods', this.headers.allowMethods)\r\n      // .set(\"Access-Control-Allow-Credentials\", 'true')\r\n      ,\r\n      withCredentials: true\r\n    });\r\n\r\n    return this.clonedRequest\r\n  }\r\n\r\n  refreshToken(): Observable<any> {\r\n    if (this.refreshTokenInProgress) {\r\n        return new Observable(observer => {\r\n            this.tokenRefreshed$.subscribe(() => {\r\n                observer.next();\r\n                observer.complete();\r\n            });\r\n        });\r\n    } else {\r\n        this.refreshTokenInProgress = true;\r\n        this.userToken = this.cookieService.get('authToken');\r\n        let refreshToken:any= localStorage.getItem('refreshToken')\r\n\r\n        var model = new RefreshTokenViewModel();\r\n            model.AuthToken = this.userToken\r\n            model.RefreshToken = refreshToken\r\n\r\n        return this.authService.refreshToken(model).pipe(\r\n            tap((res:any) => {\r\n                this.refreshTokenInProgress = false;\r\n\r\n            let token = res.data.authToken;\r\n            this.decoded = jwt_decode(token);\r\n            // const date = new Date(this.decoded.exp);\r\n            let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\r\n\r\n            const dateNow = new Date();\r\n            dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n            this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\r\n            localStorage.setItem('refreshToken', res.data.refreshToken);\r\n\r\n                this.tokenRefreshedSource.next();\r\n\r\n            }),\r\n            catchError((error):any => {\r\n\r\n              this.refreshTokenInProgress = false;\r\n              this.messageService.add({\r\n                severity: 'error',\r\n          summary: \"Oops\",\r\n          detail:'Something went wrong,please login again'\r\n          });\r\n              this.signOut();\r\n          }));\r\n\r\n          }\r\n  }\r\n\r\n\r\n  handleResponseError(error:any, request?:any, next?:any) {\r\n    //orgional code\r\n    // // Business error\r\n    // if (error.status === 400) {\r\n    //     // Show message\r\n    // }\r\n\r\n    // Invalid token error\r\n    if (error.status == 401 || error.status ==0){\r\n\r\n      if((this.router.url.includes('login') || this.router.url.includes('register')) && error.status!=401 )\r\n      {\r\n        this.messageService.add({\r\n          severity: 'error',\r\n          detail: error.error && error.error.message?error.error.message:'Something went wrong',\r\n        });\r\n      }\r\n      else{\r\n        this.signOut();\r\n      }\r\n      // this.userToken = this.cookieService.get('authToken');\r\n      // const isExpired = this.helper.isTokenExpired(this.userToken);\r\n      // if( isExpired==false || this.userToken==''){\r\n      //   this.messageService.add({\r\n      //     severity: 'error',\r\n      //     summary: \"Oops\",\r\n      //     detail:'Something went wrong,please login again'\r\n      // });\r\n      // this.userToken = this.cookieService.get('authToken');\r\n      // const isExpired = this.helper.isTokenExpired(this.userToken);\r\n    // refresh token code commented\r\n      //   if( isExpired==false ){\r\n\r\n\r\n    //   }\r\n      // this.userToken = this.cookieService.get('authToken');\r\n      // let refreshToken:any= localStorage.getItem('refreshToken')\r\n      // refresh token code commented\r\n      // if(this.userToken && refreshToken){\r\n        // return this.refreshToken().pipe(\r\n        //     switchMap(() => {\r\n        //         request = this.addAHeaders(request);\r\n        //         return next.handle(request);\r\n        //     }),\r\n        //     catchError((e):any => {\r\n        //         if (e.status !== 401) {\r\n        //             return this.handleResponseError(e);\r\n        //         } else {\r\n        //           this.messageService.add({\r\n        //             severity: 'error',\r\n        //       summary: \"Oops\",\r\n        //       detail:'Something went wrong,please login again'\r\n        //       });\r\n        //             this.signOut();\r\n        //         }\r\n        //     }));\r\n          // }\r\n          // else{\r\n          //   this.signOut();\r\n          // }\r\n      }\r\n    // Access denied error\r\n    // else if (error.status === 403) {\r\n    //   this.messageService.add({\r\n    //     severity: 'error',\r\n    //     summary: \"Access Denied\",\r\n    //   });\r\n    //     // Show message\r\n    //     // Logout\r\n    //     this.signOut();\r\n    // }\r\n\r\n    // // Server error\r\n    // else if (error.status === 500) {\r\n    //     // Show message\r\n    //     this.messageService.add({\r\n    //       severity: 'error',\r\n    //       summary: \"Internal Server Error\",\r\n    //     });\r\n    // }\r\n\r\n    // // Maintenance error\r\n    // else if (error.status === 503) {\r\n    //   this.messageService.add({\r\n    //     severity: 'error',\r\n    //     summary: \"Server Down Due to Maintainance\",\r\n    //   });\r\n    //     // Show message\r\n    //     // Redirect to the maintenance page\r\n    // }\r\n\r\n    // else if (error == 'Not Found') {\r\n    //             this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {\r\n    //               this.router.navigate(['not-found']);\r\n    //             });\r\n    // }\r\n\r\n    else {\r\n      this.messageService.add({\r\n        severity: 'error',\r\n        detail:error.error && error.error.message?error.error.message:'Something went wrong',\r\n      });\r\n      // this.signOut();\r\n    }\r\n\r\n\r\n              //  else if (error == 'Unknown Error') {\r\n              //   console.log('unknown Error')\r\n              //   // this.signOut();\r\n              //   // this.handleAuthorizationError();\r\n              // }\r\n\r\n              // else if(error.status ===0)\r\n              // {\r\n\r\n              //   this.messageService.add({\r\n              //     severity: 'error',\r\n              //     summary: \"Unknown Error\",\r\n              //   });\r\n              //   this.signOut();\r\n              // }\r\n\r\n              // removed working code\r\n              // else if(\r\n              //   error == 'Unauthorized' ||\r\n              //   error == 'Unknown Error' ||\r\n              //   error?.statusText == 'Unauthorized' ||\r\n              //   error?.statusText == 'Unknown Error'\r\n              // )\r\n              // {\r\n              //   this.userToken = this.cookieService.get('authToken');\r\n              //   const isExpired = this.helper.isTokenExpired(this.userToken);\r\n              //   if(request.url.includes(\"RefreshToken\") || isExpired==false){\r\n              //     this.signOut();\r\n              //     return null;\r\n              //   }\r\n              //   this.userToken = this.cookieService.get('authToken');\r\n              //   let refreshToken:any= localStorage.getItem('refreshToken')\r\n              //   if(this.userToken && refreshToken){\r\n              //   return this.refreshToken().pipe(\r\n              //     switchMap(() => {\r\n              //         request = this.addAHeaders(request);\r\n              //         return next.handle(request);\r\n              //     }),\r\n              //     catchError((e):any => {\r\n              //         if (e.status !== 401) {\r\n              //             return this.handleResponseError(e);\r\n              //         } else {\r\n              //             this.signOut();\r\n              //         }\r\n              //     }));\r\n              //   }else{\r\n              //     this.signOut();\r\n              //   }\r\n              // }\r\n\r\n    return throwError(error);\r\n\r\n    // our code\r\n\r\n\r\n\r\n\r\n}\r\n\r\nintercept(request: HttpRequest<any>, next: HttpHandler): Observable<any> {\r\n  // this.authService = this.injector.get(AuthService);\r\n\r\n  // Handle request\r\n  request = this.addAHeaders(request);\r\n  console.log('request',request)\r\n  // Handle response\r\n  return next.handle(request).pipe(catchError((error):any => {\r\n      return this.handleResponseError(error, request, next);\r\n  }));\r\n}\r\n\r\n\r\n//   intercept(request: HttpRequest<unknown>, next: HttpHandler): any {\r\n\r\n//     if(request.url.includes(\"RefreshToken\")){\r\n\r\n//       this.clonedRequest = request.clone({\r\n//         // withCredentials: true,\r\n//         headers: undefined\r\n//       });\r\n//     }\r\n//     else{\r\n\r\n//       this.userToken = this.cookieService.get('authToken');\r\n//       console.log('user token',this.userToken);\r\n//       this.headers.Authorization = (this.userToken == '') ? '' : `Bearer ${this.userToken}`;\r\n//       this.isExpired = false\r\n\r\n\r\n\r\n\r\n//     return next.handle(this.clonedRequest).pipe(\r\n//       tap((event: HttpEvent<any>) => {\r\n\r\n//         if (event instanceof HttpResponse) {\r\n\r\n\r\n\r\n//           if (event.status === 401) {\r\n\r\n//             console.log('sadsadsaddsdsad2');\r\n\r\n//             this.signOut();\r\n//           }\r\n//           if (event.status == 404) {\r\n//             this.NotFound();\r\n//           }\r\n//           var token = event.headers.get('XSRF-TOKEN');\r\n//           if (token != null && token != '') {\r\n//             localStorage.setItem('XXSRFTOKEN', token);\r\n\r\n//           }\r\n\r\n//         }\r\n//         return event;\r\n//       }), catchError((error) => {\r\n//         if (error == 'Not Found') {\r\n//           this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {\r\n//             this.router.navigate(['not-found']);\r\n//           });\r\n//         } else if (error == 'Unknown Error') {\r\n//           console.log('unknown Error')\r\n//           // this.signOut();\r\n//           // this.handleAuthorizationError();\r\n//         }\r\n//         else if(error=='Unauthorized')\r\n//         {\r\n\r\n//           let refreshToken:any= localStorage.getItem('refreshToken')\r\n//           // if (!this.isRefreshTokenCalled)\r\n//           // {\r\n//           //   this.isRefreshTokenCalled = true\r\n\r\n//           //   var model = new RefreshTokenViewModel();\r\n//           //   model.AuthToken = this.userToken\r\n//           //   model.RefreshToken = refreshToken\r\n//           //   if (model.AuthToken && model.RefreshToken) {\r\n//           //     this.authService.refreshToken(model).subscribe({\r\n//           //       next: (res: any) => {\r\n//           //         if (res.data != null) {\r\n//           //           let token = res.data.authToken;\r\n\r\n//           //           let decoded: any = jwt_decode(token);\r\n//           //           let days: any = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\r\n\r\n//           //           const dateNow = new Date();\r\n//           //           dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n//           //           this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\r\n//           //           localStorage.setItem('refreshToken', res.data.refreshToken);\r\n//           //           this.authService.refreshToken(model).subscribe();\r\n\r\n//           //           } else {\r\n//           //           model.AuthToken = '';\r\n//           //           model.RefreshToken = '';\r\n//           //         }\r\n//           //         this.isRefreshTokenCalled = false\r\n\r\n//           //         // this.triggerInitialCalls()\r\n//           //       },\r\n//           //       error: (err: any) => {\r\n//           //         console.error(err);\r\n//           //         model.AuthToken = '';\r\n//           //         model.RefreshToken = '';\r\n//           //         this.signOut();\r\n//           //         this.isRefreshTokenCalled = false\r\n//           //       }\r\n//           //     });\r\n//           //   } else {\r\n//           //     model.AuthToken = '';\r\n//           //     model.RefreshToken = '';\r\n//           //     this.signOut();\r\n//           //   }\r\n//           // }\r\n\r\n//           let promise=new Promise((resolve,reject)=>{\r\n//             resolve('resolved')\r\n//           })\r\n//           promise.then(res=>{\r\n//             next.handle(this.clonedRequest)\r\n//           })\r\n//         }\r\n\r\n\r\n//         return throwError(error);\r\n//       })\r\n//     );\r\n//   }\r\n// }\r\n\r\n\r\n  private handleAuthorizationError() {\r\n\r\n    let promise = new Promise((resolve, reject) => {\r\n    })\r\n\r\n    ///////////////////////\r\n    // var model = new RefreshTokenViewModel();\r\n    // this.authTokenService.authTokenData.subscribe(message => model.AuthToken = message);\r\n    // model.AuthToken = this.cookieService.get('authToken');\r\n    // // model.authToken = this.authTokenService.authTokenData.subscribe(message => message);\r\n    // model.RefreshToken = localStorage.getItem('refreshToken')?.toString();\r\n    // if (model.AuthToken && model.RefreshToken) {\r\n    //   this.authService.refreshToken(model).subscribe({\r\n    //     next: (res: any) => {\r\n    //       if (res.data != null) {\r\n    //         let token = res.data.authToken;\r\n\r\n\r\n    //         this.decoded = jwt_decode(token);\r\n    //         // const date = new Date(this.decoded.exp);\r\n    //         let days: any = (this.decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\r\n\r\n    //         const dateNow = new Date();\r\n    //         dateNow.setDate(dateNow.getDate() + parseInt(days));\r\n    //         this.cookieService.set('authToken', token, {expires: dateNow, path: '/', sameSite: 'Strict'});\r\n    //         localStorage.setItem('refreshToken', res.data.refreshToken);\r\n    //       } else {\r\n    //         model.AuthToken = '';\r\n    //         model.RefreshToken = '';\r\n    //         this.signOut();\r\n    //       }\r\n    //       this.isRefreshTokenCalled = false\r\n\r\n    //     },\r\n    //     error: (err: any) => {\r\n    //       console.error(err);\r\n    //       model.AuthToken = '';\r\n    //       model.RefreshToken = '';\r\n    //       this.signOut();\r\n    //     }\r\n    //   });\r\n    // } else {\r\n    //   model.AuthToken = '';\r\n    //   model.RefreshToken = '';\r\n    //   this.signOut();\r\n    // }\r\n\r\n\r\n  }\r\n\r\n  signOut(): void {\r\n    this.setStoreData();\r\n    this.authTokenService.authTokenSet(\"\");\r\n    // this.cookieService.delete('authToken');\r\n    this.cookieService.delete('authToken', '/');\r\n    this.store.set('cartProducts', '');\r\n    localStorage.removeItem('refreshToken')\r\n    localStorage.removeItem('auth_enc')\r\n    this.router.navigate(['/login']);\r\n  }\r\n\r\n  NotFound(): void {\r\n    this.router.navigate(['/not-found']);\r\n  }\r\n\r\n  getTimeInterval(): void {\r\n    if (this.store.get('timeInterval')) {\r\n      this.store.subscription('timeInterval')\r\n        .subscribe({\r\n          next: (res: Date) => {\r\n            if (res) {\r\n              this.timeInterval = res;\r\n            }\r\n          },\r\n          error: (err: any) => {\r\n            console.error(err);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  setStoreData(): void {\r\n    if (this.store.localStoreNames.length) {\r\n      // this.store.set('authToken', null);\r\n      this.store.set('refreshToken', null);\r\n      this.store.set('profile', null);\r\n      this.store.set('cartProducts', []);\r\n      this.store.set('favouritesProducts', []);\r\n      this.store.set('compareProducts', []);\r\n      this.store.set('socialAccount', null);\r\n      this.store.set('XXSRFTOKEN', null);\r\n      this.store.set('notifications', {\r\n        notifications: [],\r\n        unreadNotifications: 0\r\n      });\r\n      this.store.set('checkoutData', {\r\n        shipping: null,\r\n        payment: null,\r\n        promo: null,\r\n        steps: null,\r\n        profile: null,\r\n        orderId: null\r\n      });\r\n    } else {\r\n      // localStorage.setItem('authToken', '');\r\n      localStorage.setItem('refreshToken', '');\r\n      localStorage.setItem('timeInterval', '');\r\n      localStorage.setItem('TenantId', '');\r\n      localStorage.setItem('userPhone', '');\r\n      localStorage.setItem('profile', '');\r\n      localStorage.setItem('cartProducts', JSON.stringify([]));\r\n      localStorage.setItem('favouritesProducts', JSON.stringify([]));\r\n      localStorage.setItem('compareProducts', JSON.stringify([]));\r\n      localStorage.setItem('XXSRFTOKEN', '');\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAA6BA,WAAW,QAAQ,eAAe;AAS/D,SAASC,UAAU,EAASC,UAAU,EAAEC,OAAO,EAAaC,GAAG,EAAEC,UAAU,QAAQ,MAAM;AAIzF,SAASC,gBAAgB,QAAQ,oBAAoB;AAGrD,SAASC,qBAAqB,QAAQ,oBAAoB;AAM1D,OAAOC,UAAU,MAAM,YAAY;;;;;;;;AAMnC,OAAM,MAAOC,cAAc;EAsBzBC,YAC+BC,UAAe,EACpCC,KAAmB,EACnBC,WAAwB,EACxBC,MAAc,EACdC,cAA8B,EAC9BC,aAA4B,EAC5BC,gBAAkC;IANb,KAAAN,UAAU,GAAVA,UAAU;IAC/B,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA3B1B,KAAAC,SAAS,GAAQ,EAAE;IAKnB,KAAAC,UAAU,GAAW,EAAE;IAEvB,KAAAC,MAAM,GAAG,IAAId,gBAAgB,EAAE;IAC/B,KAAAe,oBAAoB,GAAY,KAAK;IACrC,KAAAC,SAAS,GAAC,KAAK;IACf,KAAAC,MAAM,GAAC,KAAK;IAGZ;IAGA,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,oBAAoB,GAAG,IAAItB,OAAO,EAAQ;IAC1C,KAAAuB,eAAe,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IAUxD,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG;MACbC,aAAa,EAAE,GAAG;MAClBC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,kBAAkB;MAC1BC,WAAW,EAAE,kBAAkB;MAC/BC,WAAW,EAAE,GAAG;MAChBC,YAAY,EAAE,gJAAgJ;MAC9JC,YAAY,EAAE;KACf;IACD,IAAI,CAACC,eAAe,EAAE;EAExB;EACAC,WAAWA,CAACC,OAAW;IAGrB,IAAI,CAACxB,SAAS,GAAG,IAAI,CAACF,aAAa,CAAC2B,GAAG,CAAC,WAAW,CAAC;IACpD,IAAGD,OAAO,CAACE,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,IACrCH,OAAO,CAACE,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IAC7BH,OAAO,CAACE,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAC;MAEzC,IAAI,CAAChB,OAAO,CAACiB,aAAa,GAAG,EAAE;KAC9B,MACG;MACF,IAAI,CAACjB,OAAO,CAACiB,aAAa,GAAI,IAAI,CAAC5B,SAAS,IAAI,EAAE,GAAI,EAAE,GAAG,UAAU,IAAI,CAACA,SAAS,EAAE;;IAIvF,IAAI,CAACe,QAAQ,GAAGc,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAChD,IAAI,IAAI,CAACf,QAAQ,EAAE;MACjB,IAAI,CAACJ,OAAO,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ;;IAGvC,IAAI,CAACJ,OAAO,CAACG,IAAI,GAAGe,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;IACxD,IAAId,QAAQ,GAAGa,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAEC,QAAQ,EAAE;IAC3D,IAAI,CAACpB,OAAO,CAACK,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,GAAG,KAAK;IAEnD,IAAI,CAACL,OAAO,CAACV,UAAU,GAAG,IAAI,CAACP,KAAK,CAAC+B,GAAG,CAAC,YAAY,CAAC,IAAII,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAC5F,IAAI,IAAI,CAACnB,OAAO,CAACV,UAAU,IAAI+B,SAAS,EAAE;MACxC,IAAI,CAACrB,OAAO,CAACV,UAAU,GAAG,EAAE;;IAEhC,IAAIc,QAAQ,GAAGkB,MAAM,CAACJ,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE;IAGjE,IAAI,CAACI,aAAa,GAAGV,OAAO,CAACW,KAAK,CAAC;MACjC;MACAxB,OAAO,EAAEa,OAAO,CAACb,OAAO,CACrByB,GAAG,CAAC,eAAe,EAAE,IAAI,CAACzB,OAAO,CAACiB,aAAa,CAAC,CAChDQ,GAAG,CAAC,OAAO,EAAE,IAAI,CAACzB,OAAO,CAACE,KAAK,CAAC,CAChCuB,GAAG,CAAC,UAAU,EAAE,IAAI,CAACzB,OAAO,CAACI,QAAQ,CAAC,CACtCqB,GAAG,CAAC,MAAM,EAAE,IAAI,CAACzB,OAAO,CAACG,IAAI,CAAC,CAC9BsB,GAAG,CAAC,UAAU,EAAE,IAAI,CAACzB,OAAO,CAACK,QAAQ,CAAC,CACtCoB,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACzB,OAAO,CAACM,MAAM,CAAC,CAClCmB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACzB,OAAO,CAACO,WAAW,CAAC,CAC7CkB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACzB,OAAO,CAACV,UAAU;MAC9C;MACA;MACA;MACA;MACA;MAAA;;MAEAoC,eAAe,EAAE;KAClB,CAAC;IAEF,OAAO,IAAI,CAACH,aAAa;EAC3B;EAEAI,YAAYA,CAAA;IACV,IAAI,IAAI,CAAChC,sBAAsB,EAAE;MAC7B,OAAO,IAAItB,UAAU,CAACuD,QAAQ,IAAG;QAC7B,IAAI,CAAC/B,eAAe,CAACgC,SAAS,CAAC,MAAK;UAChCD,QAAQ,CAACE,IAAI,EAAE;UACfF,QAAQ,CAACG,QAAQ,EAAE;QACvB,CAAC,CAAC;MACN,CAAC,CAAC;KACL,MAAM;MACH,IAAI,CAACpC,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAACN,SAAS,GAAG,IAAI,CAACF,aAAa,CAAC2B,GAAG,CAAC,WAAW,CAAC;MACpD,IAAIa,YAAY,GAAMT,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAE1D,IAAIa,KAAK,GAAG,IAAItD,qBAAqB,EAAE;MACnCsD,KAAK,CAACC,SAAS,GAAG,IAAI,CAAC5C,SAAS;MAChC2C,KAAK,CAACE,YAAY,GAAGP,YAAY;MAErC,OAAO,IAAI,CAAC3C,WAAW,CAAC2C,YAAY,CAACK,KAAK,CAAC,CAACG,IAAI,CAC5C5D,GAAG,CAAE6D,GAAO,IAAI;QACZ,IAAI,CAACzC,sBAAsB,GAAG,KAAK;QAEvC,IAAIO,KAAK,GAAGkC,GAAG,CAACC,IAAI,CAACC,SAAS;QAC9B,IAAI,CAACC,OAAO,GAAG5D,UAAU,CAACuB,KAAK,CAAC;QAChC;QACA,IAAIsC,IAAI,GAAQ,CAAC,IAAI,CAACD,OAAO,CAACE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;QAErE,MAAMC,OAAO,GAAG,IAAIC,IAAI,EAAE;QAC1BD,OAAO,CAACE,OAAO,CAACF,OAAO,CAACG,OAAO,EAAE,GAAGC,QAAQ,CAACP,IAAI,CAAC,CAAC;QACnD,IAAI,CAACrD,aAAa,CAACsC,GAAG,CAAC,WAAW,EAAEvB,KAAK,EAAE;UAAC8C,OAAO,EAAEL,OAAO;UAAEM,IAAI,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAQ,CAAC,CAAC;QAC7FhC,YAAY,CAACiC,OAAO,CAAC,cAAc,EAAEf,GAAG,CAACC,IAAI,CAACV,YAAY,CAAC;QAEvD,IAAI,CAAC/B,oBAAoB,CAACkC,IAAI,EAAE;MAEpC,CAAC,CAAC,EACF1D,UAAU,CAAEgF,KAAK,IAAQ;QAEvB,IAAI,CAACzD,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACT,cAAc,CAACmE,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACvBC,OAAO,EAAE,MAAM;UACfC,MAAM,EAAC;SACN,CAAC;QACE,IAAI,CAACC,OAAO,EAAE;MAClB,CAAC,CAAC,CAAC;;EAGX;EAGAC,mBAAmBA,CAACN,KAAS,EAAEvC,OAAY,EAAEiB,IAAS;IACpD;IACA;IACA;IACA;IACA;IAEA;IACA,IAAIsB,KAAK,CAACO,MAAM,IAAI,GAAG,IAAIP,KAAK,CAACO,MAAM,IAAG,CAAC,EAAC;MAE1C,IAAG,CAAC,IAAI,CAAC1E,MAAM,CAAC8B,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC/B,MAAM,CAAC8B,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,KAAKoC,KAAK,CAACO,MAAM,IAAE,GAAG,EACnG;QACE,IAAI,CAACzE,cAAc,CAACmE,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBE,MAAM,EAAEJ,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACQ,OAAO,GAACR,KAAK,CAACA,KAAK,CAACQ,OAAO,GAAC;SAChE,CAAC;OACH,MACG;QACF,IAAI,CAACH,OAAO,EAAE;;MAEhB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACF;MACE;MAGF;MACE;MACA;MACA;MACA;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACE;MACA;MACA;MACA;;IAEN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAAA,KAEK;MACH,IAAI,CAACvE,cAAc,CAACmE,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBE,MAAM,EAACJ,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACQ,OAAO,GAACR,KAAK,CAACA,KAAK,CAACQ,OAAO,GAAC;OAC/D,CAAC;MACF;;IAIQ;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEV,OAAOpF,UAAU,CAAC4E,KAAK,CAAC;IAExB;EAKJ;;EAEAS,SAASA,CAAChD,OAAyB,EAAEiB,IAAiB;IACpD;IAEA;IACAjB,OAAO,GAAG,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC;IACnCiD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAClD,OAAO,CAAC;IAC9B;IACA,OAAOiB,IAAI,CAACkC,MAAM,CAACnD,OAAO,CAAC,CAACsB,IAAI,CAAC/D,UAAU,CAAEgF,KAAK,IAAQ;MACtD,OAAO,IAAI,CAACM,mBAAmB,CAACN,KAAK,EAAEvC,OAAO,EAAEiB,IAAI,CAAC;IACzD,CAAC,CAAC,CAAC;EACL;EAGA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAKA;EACA;EAEA;EAIA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EAGUmC,wBAAwBA,CAAA;IAE9B,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI,CAC9C,CAAC,CAAC;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAGF;;EAEAZ,OAAOA,CAAA;IACL,IAAI,CAACa,YAAY,EAAE;IACnB,IAAI,CAAClF,gBAAgB,CAACmF,YAAY,CAAC,EAAE,CAAC;IACtC;IACA,IAAI,CAACpF,aAAa,CAACqF,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC;IAC3C,IAAI,CAACzF,KAAK,CAAC0C,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;IAClCP,YAAY,CAACuD,UAAU,CAAC,cAAc,CAAC;IACvCvD,YAAY,CAACuD,UAAU,CAAC,UAAU,CAAC;IACnC,IAAI,CAACxF,MAAM,CAACyF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC1F,MAAM,CAACyF,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA/D,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5B,KAAK,CAAC+B,GAAG,CAAC,cAAc,CAAC,EAAE;MAClC,IAAI,CAAC/B,KAAK,CAAC6F,YAAY,CAAC,cAAc,CAAC,CACpC/C,SAAS,CAAC;QACTC,IAAI,EAAGM,GAAS,IAAI;UAClB,IAAIA,GAAG,EAAE;YACP,IAAI,CAACyC,YAAY,GAAGzC,GAAG;;QAE3B,CAAC;QACDgB,KAAK,EAAG0B,GAAQ,IAAI;UAClBhB,OAAO,CAACV,KAAK,CAAC0B,GAAG,CAAC;QACpB;OACD,CAAC;;EAER;EAEAR,YAAYA,CAAA;IACV,IAAI,IAAI,CAACvF,KAAK,CAACgG,eAAe,CAACC,MAAM,EAAE;MACrC;MACA,IAAI,CAACjG,KAAK,CAAC0C,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MACpC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC/B,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;MAClC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC;MACxC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,iBAAiB,EAAE,EAAE,CAAC;MACrC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;MACrC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;MAClC,IAAI,CAAC1C,KAAK,CAAC0C,GAAG,CAAC,eAAe,EAAE;QAC9BwD,aAAa,EAAE,EAAE;QACjBC,mBAAmB,EAAE;OACtB,CAAC;MACF,IAAI,CAACnG,KAAK,CAAC0C,GAAG,CAAC,cAAc,EAAE;QAC7B0D,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;OACV,CAAC;KACH,MAAM;MACL;MACAtE,YAAY,CAACiC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCjC,YAAY,CAACiC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;MACxCjC,YAAY,CAACiC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACpCjC,YAAY,CAACiC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;MACrCjC,YAAY,CAACiC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MACnCjC,YAAY,CAACiC,OAAO,CAAC,cAAc,EAAEsC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MACxDxE,YAAY,CAACiC,OAAO,CAAC,oBAAoB,EAAEsC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC9DxE,YAAY,CAACiC,OAAO,CAAC,iBAAiB,EAAEsC,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;MAC3DxE,YAAY,CAACiC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;;EAE1C;EAAC,QAAAwC,CAAA,G;qBAhjBU/G,cAAc,EAAAgH,EAAA,CAAAC,QAAA,CAuBf1H,WAAW,GAAAyH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAvBV9H,cAAc;IAAA+H,OAAA,EAAd/H,cAAc,CAAAgI;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}