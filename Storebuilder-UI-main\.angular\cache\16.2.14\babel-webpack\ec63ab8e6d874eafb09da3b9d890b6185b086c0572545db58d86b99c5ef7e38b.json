{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LoaderService {\n  isLoading = new Subject();\n  show() {\n    this.isLoading.next(true);\n  }\n  hide() {\n    this.isLoading.next(false);\n  }\n  static ɵfac = function LoaderService_Factory(t) {\n    return new (t || LoaderService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoaderService,\n    factory: LoaderService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}