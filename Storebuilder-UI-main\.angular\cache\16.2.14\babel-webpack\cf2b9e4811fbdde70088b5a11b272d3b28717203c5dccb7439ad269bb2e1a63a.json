{"ast": null, "code": "import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, notification => observeNotification(notification, subscriber)));\n  });\n}\n//# sourceMappingURL=dematerialize.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}