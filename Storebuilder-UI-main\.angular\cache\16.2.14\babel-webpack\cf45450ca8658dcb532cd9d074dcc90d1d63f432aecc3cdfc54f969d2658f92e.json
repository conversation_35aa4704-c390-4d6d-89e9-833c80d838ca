{"ast": null, "code": "export class ApiResponse {}\nexport class InitialData {}\nexport class InitialDataResult {}\nexport class InitialDataResultRecords {}\nexport class ConfigurationResponse {}\nexport class ConfigurationRecords {}\nexport class Tenant {}\nexport class TenantRecords {}\nexport class Categories {}\nexport class CategoryRecords {}", "map": {"version": 3, "names": ["ApiResponse", "InitialData", "InitialDataResult", "InitialDataResultRecords", "ConfigurationResponse", "ConfigurationRecords", "Tenant", "TenantRecords", "Categories", "CategoryRecords"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\core\\interface\\app.ts"], "sourcesContent": ["import {Language} from \"./language\";\r\nimport {UserConsentType} from \"@core/enums/user\";\r\n\r\nexport class ApiResponse {\r\n  data: any;\r\n  success: boolean;\r\n  message?: string;\r\n}\r\n\r\nexport class InitialData {\r\n  isShop: boolean;\r\n  languages: Language[];\r\n  result: InitialDataResult;\r\n  shopProductSetting: any;\r\n  featureByTenantRes :[];\r\n}\r\n\r\nexport class InitialDataResult {\r\n  hasNext: boolean;\r\n  records: InitialDataResultRecords[];\r\n  total: number;\r\n}\r\n\r\nexport class InitialDataResultRecords {\r\n  displayName: string;\r\n  id: number;\r\n  isDelete: boolean;\r\n  key: string;\r\n  keyTypeId: number;\r\n  value: any;\r\n}\r\n\r\nexport class ConfigurationResponse {\r\n  hasNext: false;\r\n  records: ConfigurationRecords[];\r\n  total: number\r\n}\r\n\r\nexport class ConfigurationRecords {\r\n  id: number;\r\n  key: string;\r\n  value: string;\r\n}\r\n\r\nexport class Tenant {\r\n  hasNext: boolean;\r\n  records: TenantRecords[];\r\n  total: number;\r\n}\r\n\r\nexport class TenantRecords {\r\n  id: number;\r\n  code: string;\r\n  countryId: string;\r\n  isoCode: string;\r\n  name: string;\r\n  nameAr: string;\r\n  tenantId: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport class Categories {\r\n  hasNext: boolean;\r\n  records: CategoryRecords[];\r\n  total: number;\r\n}\r\n\r\nexport class CategoryRecords {\r\n  id: number;\r\n  categories: CategoryRecords[];\r\n  categoryIds: number[];\r\n  categoryName: string;\r\n  categoryNameAr: string;\r\n  categoryPath: string;\r\n  categoryStatus: boolean;\r\n  image: string;\r\n  isRefundable: boolean;\r\n  isVirtual: boolean;\r\n  order: number;\r\n  parentId: number;\r\n  shopId: number;\r\n  templateId: number;\r\n  totalProductCount: number;\r\n}\r\n\r\n\r\nexport interface ShowroomConf {\r\n  bannerId: number;\r\n  bannerSize: any;\r\n  categoryId: any;\r\n  color: any;\r\n  featureProduct: any;\r\n  id: number;\r\n  image: string;\r\n  includeSideBanner: boolean;\r\n  order: number;\r\n  searchData: any;\r\n  shopId: any;\r\n  showRoomTypeId: number;\r\n  topNumber: number;\r\n}\r\n\r\nexport interface AllShowroomConf {\r\n  data: {\r\n    showRoomConfigurationRes: {\r\n      records: ShowroomConf[];\r\n      total: number;\r\n      hasNext: boolean;\r\n    };\r\n  };\r\n  success: true;\r\n}\r\n\r\n\r\nexport interface Offers {\r\n  creationDate: string;\r\n  id: number;\r\n  imageUrl: string;\r\n  isDelete: boolean;\r\n  offerPercent: number;\r\n  shopId: any;\r\n  tenantId: number;\r\n  updateOn: any;\r\n}\r\n\r\nexport interface AllOffers {\r\n  data: { records: Offers[]; total: number; hasNext: boolean };\r\n  success: true;\r\n}\r\n\r\nexport interface BannerResponse {\r\n  data: Banner;\r\n  success: true;\r\n}\r\nexport interface Banner {\r\n  categoryId: number;\r\n  creationDate: string;\r\n  description: string;\r\n  id: number;\r\n  imageUrl: string;\r\n  isDelete: boolean;\r\n  redirectTypeId: number;\r\n  redirectValue: number;\r\n  shopId: number;\r\n  tenantId: number;\r\n  title: string;\r\n  updateOn: string;\r\n}\r\n\r\n\r\nexport interface UserConsent {\r\n  sessionId: string;\r\n  userId?: string;\r\n  consentType: UserConsentType,\r\n  consent: boolean,\r\n  recordId?: number\r\n}\r\n\r\nexport interface AgeConsent {\r\n  sessionId: string;\r\n  userId?: string;\r\n  MinimumAgeForProductEligibility: number;\r\n  recordId?: number;\r\n}\r\n"], "mappings": "AAGA,OAAM,MAAOA,WAAW;AAMxB,OAAM,MAAOC,WAAW;AAQxB,OAAM,MAAOC,iBAAiB;AAM9B,OAAM,MAAOC,wBAAwB;AASrC,OAAM,MAAOC,qBAAqB;AAMlC,OAAM,MAAOC,oBAAoB;AAMjC,OAAM,MAAOC,MAAM;AAMnB,OAAM,MAAOC,aAAa;AAW1B,OAAM,MAAOC,UAAU;AAMvB,OAAM,MAAOC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}