{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { ButtonModule } from 'primeng/button';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { RoleEnum } from '@core/interface';\nimport { CountryISO } from 'ngx-intl-tel-input-gg';\nimport { PhoneInputComponent } from '../phone-input/phone-input.component';\nimport { EmailInputComponent } from '../email-input/email-input.component';\nimport { InitialModule } from \"@shared/modules/initial.module\";\nimport { RegisterUserModalComponent } from \"../../modals/resigter-user-modal/register-user-modal.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"@angular/forms\";\nfunction RegisterComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-phone-input\", 27);\n    i0.ɵɵlistener(\"phoneNumberChange\", function RegisterComponent_div_32_Template_app_phone_input_phoneNumberChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onPhoneNumberChange($event));\n    })(\"validationChange\", function RegisterComponent_div_32_Template_app_phone_input_validationChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onPhoneValidationChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"maxLength\", ctx_r0.phoneInputLength)(\"selectedCountryISO\", ctx_r0.CustomCountryISO)(\"preferredCountries\", ctx_r0.preferredCountries)(\"placeholder\", ctx_r0.customPlaceHolder);\n  }\n}\nfunction RegisterComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-email-input\", 28);\n    i0.ɵɵlistener(\"emailChange\", function RegisterComponent_div_33_Template_app_email_input_emailChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onEmailChange($event));\n    })(\"validationChange\", function RegisterComponent_div_33_Template_app_email_input_validationChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onEmailValidationChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"emailRequired\", \"true\")(\"floatingLabel\", false)(\"labelColor\", \"#333\");\n  }\n}\nfunction RegisterComponent_ng_container_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-register-user-modal\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"displayModal\", ctx_r2.isRegisterModal)(\"phoneNumber\", ctx_r2.phoneNumber);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"register-mobile\": a0\n  };\n};\nexport class RegisterComponent {\n  otpService;\n  translate;\n  messageService;\n  router;\n  store;\n  permissionService;\n  appDataService;\n  $gaService;\n  $gtmService;\n  customGAService;\n  phoneNumber;\n  email = '';\n  isPhoneValid = false;\n  isEmailValid = false;\n  selectedRegistrationMethod = 'phone';\n  countryPhoneNumber = \"\";\n  countryPhoneCode = \"\";\n  phoneLength = 12;\n  phoneInputLength = 12;\n  CustomCountryISO;\n  preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n  customPlaceHolder = '';\n  isMobileLayout = false;\n  screenWidth = window.innerWidth;\n  isGoogleAnalytics = false;\n  isRegisterModal = false;\n  hasTrackedStartSignup = false;\n  get buttonLabel() {\n    return this.screenWidth <= 767 ? 'register.next' : 'register.continue';\n  }\n  get isFormValid() {\n    return this.selectedRegistrationMethod === 'phone' ? this.isPhoneValid : this.isEmailValid;\n  }\n  constructor(otpService, translate, messageService, router, store, permissionService, appDataService, $gaService, $gtmService, customGAService) {\n    this.otpService = otpService;\n    this.translate = translate;\n    this.messageService = messageService;\n    this.router = router;\n    this.store = store;\n    this.permissionService = permissionService;\n    this.appDataService = appDataService;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.customGAService = customGAService;\n    this.setupCustomPlaceholder();\n  }\n  ngOnInit() {\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    this.$gtmService.pushPageView('signUp');\n    // Track view_signup_form event\n    if (this.isGoogleAnalytics) {\n      console.log('viewSignupFormEvent : ', this.isGoogleAnalytics);\n      this.customGAService.viewSignupFormEvent('register_page');\n    }\n    this.CustomCountryISO = localStorage.getItem(\"isoCode\");\n    if (this.appDataService.configuration) {\n      const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n      if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n    }\n  }\n  setupCustomPlaceholder() {\n    let tenantId = localStorage.getItem('tenantId');\n    if (tenantId && tenantId !== '') {\n      const placeholders = {\n        '1': 'XXXXXXXXX',\n        '2': 'XXXXXXXXX',\n        '3': 'XXXXXXXXX',\n        '4': 'XXXXXXXXXX'\n      };\n      this.customPlaceHolder = placeholders[tenantId] || '';\n    }\n  }\n  onPhoneNumberChange(phoneNumber) {\n    this.phoneNumber = phoneNumber;\n  }\n  onPhoneValidationChange(isValid) {\n    this.isPhoneValid = isValid;\n    // Track start_signup event when user starts interacting with the form\n    if (isValid && this.isGoogleAnalytics && !this.hasTrackedStartSignup) {\n      this.customGAService.startSignupEvent('Phone');\n      this.hasTrackedStartSignup = true;\n    }\n  }\n  onEmailChange(email) {\n    this.email = email || '';\n  }\n  onEmailValidationChange(isValid) {\n    this.isEmailValid = isValid;\n    // Track start_signup event when user starts interacting with the form\n    if (isValid && this.isGoogleAnalytics && !this.hasTrackedStartSignup) {\n      this.customGAService.startSignupEvent('Email');\n      this.hasTrackedStartSignup = true;\n    }\n  }\n  selectRegistrationMethod(method) {\n    this.selectedRegistrationMethod = method;\n    localStorage.setItem('registrationMethod', method);\n    this.isPhoneValid = false;\n    this.isEmailValid = false;\n    this.hasTrackedStartSignup = false;\n  }\n  checkMobileExist() {\n    this.isRegisterModal = false;\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(GaLocalActionEnum.CLICK_ON_SIGN_UP, '', 'SIGN_UP', 1, true);\n    }\n    this.store.set(\"loading\", true);\n    if (this.selectedRegistrationMethod === 'phone') {\n      this.handlePhoneRegistration();\n    } else {\n      this.handleEmailRegistration();\n    }\n  }\n  handlePhoneRegistration() {\n    if (this.phoneNumber?.e164Number) {\n      this.phoneNumber = this.phoneNumber.e164Number.slice(1);\n    }\n    if (this.isPhoneValid) {\n      this.processRegistration(this.phoneNumber);\n    } else {\n      this.showValidationError('ErrorMessages.mobileRequired');\n    }\n  }\n  handleEmailRegistration() {\n    if (this.isEmailValid && this.email) {\n      this.processRegistration(this.email);\n    } else {\n      this.showValidationError('ErrorMessages.emailRequired');\n    }\n  }\n  processRegistration(username) {\n    this.otpService.username = username;\n    this.otpService.countryId = \"1448983B-0C38-450A-BD71-9204D181B925\";\n    const requestData = {\n      UserName: username,\n      CountryId: \"1448983B-0C38-450A-BD71-9204D181B925\",\n      UserRole: RoleEnum.consumer\n    };\n    const apiCall = this.selectedRegistrationMethod === 'phone' ? this.otpService.checkMobileNumber(requestData) : this.otpService.checkEmailAddress(requestData);\n    apiCall.subscribe({\n      next: res => {\n        this.store.set(\"loading\", false);\n        if (res.success) {\n          if (res.data.isRegistered) {\n            this.isRegisterModal = true;\n          } else {\n            localStorage.setItem('registrationMethod', this.selectedRegistrationMethod);\n            this.router.navigateByUrl('/register/register-otp', {\n              state: {\n                mobile: username\n              }\n            });\n          }\n        } else {\n          this.router.navigateByUrl('/login');\n        }\n      },\n      error: err => {\n        this.store.set(\"loading\", false);\n        this.showError(err.message);\n      }\n    });\n  }\n  showValidationError(errorKey) {\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: this.translate.instant(errorKey)\n    });\n    this.store.set(\"loading\", false);\n  }\n  showError(message) {\n    this.messageService.add({\n      severity: 'error',\n      summary: this.translate.instant('ErrorMessages.fetchError'),\n      detail: message\n    });\n    this.store.set(\"loading\", false);\n  }\n  omit_special_char(event) {\n    let key;\n    key = event.charCode;\n    return key > 47 && key < 58;\n  }\n  reloadCurrentPage(pageId, title) {\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => this.router.navigate(['/about-us/'], {\n      queryParams: {\n        pageId: pageId,\n        title: title\n      }\n    }));\n  }\n  static ɵfac = function RegisterComponent_Factory(t) {\n    return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.RegisterService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(i1.CustomGAService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RegisterComponent,\n    selectors: [[\"app-register\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 57,\n    vars: 53,\n    consts: [[1, \"register\", 3, \"ngClass\"], [1, \"content-container\"], [1, \"grid\", \"justify-content-between\", \"shadow-signin\"], [1, \"image\", \"col-12\", \"desktop-only\"], [\"src\", \"assets/images/registerLogo.svg\", \"alt\", \"\", \"srcset\", \"\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"pt-6\", \"header-body\"], [1, \"mobile-header\", \"mobile-only\"], [\"src\", \"assets/images/signup.svg\"], [1, \"head-desc\"], [1, \"signup-heading\"], [1, \"signup-desc\"], [1, \"desktop-only\"], [1, \"signUp-heading\"], [1, \"signUp-content\"], [\"autocomplete\", \"new-password\"], [1, \"p-fluid\", \"p-grid\"], [1, \"mt-3\"], [1, \"registration-tabs\"], [\"type\", \"button\", 1, \"tab-button\", 3, \"click\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-column\", \"mb-5\", \"btn-container\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"sign-up-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"signin-agreement\"], [3, \"click\"], [1, \"new-customer-container\"], [\"routerLink\", \"/login\", \"pButton\", \"\", \"type\", \"button\", 1, \"register-now\", \"p-field\", \"p-col-12\", 3, \"label\"], [4, \"ngIf\"], [3, \"maxLength\", \"selectedCountryISO\", \"preferredCountries\", \"placeholder\", \"phoneNumberChange\", \"validationChange\"], [3, \"emailRequired\", \"floatingLabel\", \"labelColor\", \"emailChange\", \"validationChange\"], [3, \"displayModal\", \"phoneNumber\"]],\n    template: function RegisterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵelement(7, \"img\", 7);\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"h2\", 9);\n        i0.ɵɵtext(10);\n        i0.ɵɵpipe(11, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"span\", 10);\n        i0.ɵɵtext(13);\n        i0.ɵɵpipe(14, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 11)(16, \"p\", 12);\n        i0.ɵɵtext(17);\n        i0.ɵɵpipe(18, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"p\", 13);\n        i0.ɵɵtext(20);\n        i0.ɵɵpipe(21, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"form\", 14)(23, \"div\", 15)(24, \"div\", 16)(25, \"div\", 17)(26, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_26_listener() {\n          return ctx.selectRegistrationMethod(\"phone\");\n        });\n        i0.ɵɵtext(27);\n        i0.ɵɵpipe(28, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_29_listener() {\n          return ctx.selectRegistrationMethod(\"email\");\n        });\n        i0.ɵɵtext(30);\n        i0.ɵɵpipe(31, \"translate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(32, RegisterComponent_div_32_Template, 2, 4, \"div\", 19);\n        i0.ɵɵtemplate(33, RegisterComponent_div_33_Template, 2, 3, \"div\", 19);\n        i0.ɵɵelementStart(34, \"div\", 20)(35, \"button\", 21);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_35_listener() {\n          return ctx.checkMobileExist();\n        });\n        i0.ɵɵpipe(36, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"p\", 22);\n        i0.ɵɵtext(38);\n        i0.ɵɵpipe(39, \"translate\");\n        i0.ɵɵelementStart(40, \"a\", 23);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_40_listener() {\n          return ctx.reloadCurrentPage(171, \"Terms and Conditions\");\n        });\n        i0.ɵɵtext(41);\n        i0.ɵɵpipe(42, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(43);\n        i0.ɵɵpipe(44, \"translate\");\n        i0.ɵɵelementStart(45, \"a\", 23);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_45_listener() {\n          return ctx.reloadCurrentPage(170, \"Privacy policy\");\n        });\n        i0.ɵɵtext(46);\n        i0.ɵɵpipe(47, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(48);\n        i0.ɵɵpipe(49, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"div\", 24)(51, \"p\");\n        i0.ɵɵtext(52);\n        i0.ɵɵpipe(53, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(54, \"a\", 25);\n        i0.ɵɵpipe(55, \"translate\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵtemplate(56, RegisterComponent_ng_container_56_Template, 2, 2, \"ng-container\", 26);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c0, ctx.isMobileLayout && ctx.screenWidth <= 767));\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 23, \"auth.registerPassword.title\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 25, \"auth.registerPassword.desc\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 27, \"register.signUp\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 29, \"register.content\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"active\", ctx.selectedRegistrationMethod === \"phone\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 31, \"register.mobileNumber\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"active\", ctx.selectedRegistrationMethod === \"email\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 33, \"auth.registerPassword.email\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedRegistrationMethod === \"phone\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedRegistrationMethod === \"email\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.isFormValid)(\"label\", i0.ɵɵpipeBind1(36, 35, ctx.buttonLabel));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(39, 37, \"signIn.AgreeTermsOne\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(42, 39, \"signIn.AgreeTermsTwo\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(44, 41, \"signIn.AgreeTermsThree\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(47, 43, \"signIn.AgreeTermsFour\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(49, 45, \"signIn.AgreeTermsFive\"), \". \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(53, 47, \"register.alreadyHaveAccount\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(55, 49, \"register.login\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isRegisterModal);\n      }\n    },\n    dependencies: [CommonModule, i7.NgClass, i7.NgIf, RouterModule, i4.RouterLink, TranslateModule, i2.TranslatePipe, ButtonModule, i8.ButtonDirective, PhoneInputComponent, EmailInputComponent, InitialModule, i9.ɵNgNoValidate, i9.NgControlStatusGroup, i9.NgForm, RegisterUserModalComponent],\n    styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container {\\n  pointer-events: none !important;\\n}\\n\\n.register   section   [_nghost-%COMP%]     button.back-btn {\\n  position: relative !important;\\n}\\n\\n.content-container[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n.shadow-signin[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  border: 1px solid rgba(151, 151, 151, 0.17);\\n  border-radius: 7px;\\n  background-color: #ffffff;\\n  display: flex;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: end;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  width: 300px;\\n  border-radius: 281.739px;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  padding: 20px;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-heading[_ngcontent-%COMP%] {\\n  color: #212121;\\n  font-size: 26px;\\n  margin-bottom: 16px;\\n  font-weight: 700;\\n  font-family: var(--medium-font) !important;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-content[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font) !important;\\n  color: #443F3F;\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 130%;\\n}\\n.shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  transition-property: all;\\n  transition-timing-function: ease;\\n  line-height: 1;\\n  left: 0;\\n  padding: 10px;\\n  font-size: 11px !important;\\n  font-weight: 500;\\n  color: #323232;\\n  font-family: var(--medium-font) !important;\\n  margin-top: 0 !important;\\n  top: 0 !important;\\n}\\n\\n.registration-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  background-color: #E8EFFD;\\n  border-radius: 8px;\\n  padding: 4px;\\n  margin-bottom: 16px;\\n}\\n.registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: none;\\n  background: transparent;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #666;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-family: var(--medium-font) !important;\\n}\\n.registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(32, 78, 110, 0.1);\\n  color: #204e6e;\\n}\\n.registration-tabs[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%] {\\n  background-color: #204e6e;\\n  color: white;\\n  box-shadow: 0 2px 4px rgba(32, 78, 110, 0.2);\\n}\\n\\n@media screen and (max-width: 767px) {\\n  .registration-tabs[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .registration-tabs[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n    font-size: 13px;\\n  }\\n}\\n.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #373636;\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 15px;\\n  white-space: nowrap;\\n}\\n.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  flex-grow: 1;\\n  display: inline-block;\\n  width: 90px;\\n  height: 1px;\\n  background-color: #204E6E;\\n  opacity: 0.1;\\n}\\n.btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%] {\\n  background-color: #E8EFFD;\\n  color: #204E6E;\\n  padding: 16px;\\n  border-radius: 4px;\\n  border: none;\\n  margin: 16px 0;\\n  font-weight: 500;\\n  font-size: 14px;\\n  font-family: var(--regular-font) !important;\\n}\\n.btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%] {\\n  height: 48px;\\n  border-radius: 4px;\\n  background-color: #204e6e;\\n  color: white;\\n  font-weight: 500;\\n  margin-top: 24px;\\n  padding: 12px 24px;\\n  width: 100%;\\n}\\n.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%] {\\n  font-family: var(--regular-font) !important;\\n  font-size: 12px;\\n  color: #272727;\\n  margin-top: 16px;\\n}\\n.btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #204e6e;\\n  text-decoration: underline;\\n}\\n\\n.mobile-only[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media screen and (max-width: 767px) {\\n  .mobile-only[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.desktop-only[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n@media screen and (max-width: 767px) {\\n  .desktop-only[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media screen and (min-width: 768px) and (max-width: 1024px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%] {\\n    padding: 20px;\\n    flex-direction: column;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 20px;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 200px;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: 16px;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-heading[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signUp-content[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after {\\n    width: 60px;\\n  }\\n}\\n@media screen and (max-width: 767px) {\\n  .register[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n  .register-mobile[_ngcontent-%COMP%] {\\n    height: 100%;\\n    margin-top: 0;\\n    display: flex;\\n    border: 1px solid rgba(151, 151, 151, 0.17);\\n    border-radius: 7px;\\n  }\\n  .register-mobile[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%] {\\n    margin-top: 50px;\\n    flex: 1;\\n    display: flex;\\n    flex-direction: column;\\n    padding: 16px;\\n  }\\n  .register-mobile[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding: 0;\\n    margin: 0;\\n    flex-direction: column;\\n    border: 1px solid rgba(32, 78, 110, 0.1019607843);\\n    border-radius: 8px;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: row;\\n    padding: 1rem 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin-right: 1rem;\\n    flex-shrink: 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .head-desc[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 8px;\\n    justify-content: center;\\n    flex: 1;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%] {\\n    margin: 0 !important;\\n    font-size: 18px;\\n    font-weight: 500;\\n    color: #212121;\\n    font-family: var(--medium-font) !important;\\n    line-height: 1.2;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n    font-weight: 400;\\n    color: #443F3F;\\n    font-family: var(--regular-font) !important;\\n    line-height: 1.3;\\n  }\\n  .header-body[_ngcontent-%COMP%] {\\n    padding: 0 !important;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%] {\\n    background-color: var(--primary);\\n    color: white;\\n    border: none;\\n    margin-bottom: 1rem;\\n    height: 44px;\\n    font-size: 14px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    line-height: 1.4;\\n    text-align: center;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n    margin: 12px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after {\\n    width: 40px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    font-size: 13px;\\n    margin: 12px 0;\\n  }\\n}\\n@media screen and (max-width: 480px) {\\n  .content-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .register-mobile[_ngcontent-%COMP%] {\\n    border: 1px solid rgba(151, 151, 151, 0.17);\\n    border-radius: 7px;\\n  }\\n  .register-mobile[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%] {\\n    margin-top: 50px;\\n    padding: 12px;\\n  }\\n  .mobile-header[_ngcontent-%COMP%] {\\n    padding: 0.8rem 0;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .signup-heading[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .mobile-header[_ngcontent-%COMP%]   .signup-desc[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .sign-up-btn[_ngcontent-%COMP%] {\\n    height: 42px;\\n    font-size: 13px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    margin: 10px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before, .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after {\\n    width: 20px;\\n  }\\n  .btn-container[_ngcontent-%COMP%]   .new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    font-size: 12px;\\n  }\\n}\\n[_nghost-%COMP%]     .contact-input-phone {\\n  background-color: white !important;\\n  border: 1px solid #ccc !important;\\n  border-radius: 4px;\\n  padding: 10px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n[_nghost-%COMP%]     .iti__selected-flag {\\n  padding: 0px 6px 0px 6px;\\n}\\n\\n[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 15px;\\n  left: 93px;\\n  width: 1px;\\n  height: 50%;\\n  background-color: #9CA69C;\\n  transform: translateX(-50%);\\n  pointer-events: none;\\n}\\n\\n[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container {\\n  pointer-events: none !important;\\n}\\n\\n@media screen and (max-width: 767px) {\\n  [_nghost-%COMP%]     .contact-input-phone {\\n    padding: 12px 10px;\\n    font-size: 14px;\\n  }\\n  [_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle::before {\\n    top: 13px;\\n    left: 85px;\\n  }\\n}\\n@media screen and (max-width: 480px) {\\n  [_nghost-%COMP%]     .contact-input-phone {\\n    padding: 10px 8px;\\n    font-size: 13px;\\n  }\\n  [_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle::before {\\n    left: 91px;\\n  }\\n}\"]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}