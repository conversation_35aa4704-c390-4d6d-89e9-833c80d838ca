{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PLATFORM_ID } from '@angular/core';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport { CountryISO } from '../../models/CountryISO.enum';\nimport { GaActionEnum } from 'ngx-google-analytics';\nimport { RouterModule } from '@angular/router';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport { TenantRecords } from '@core/interface';\nimport { UserConsentType } from '../../models/UserConsentType.enum';\nimport jwt_decode from 'jwt-decode';\nimport { GuidGenerator } from '../../utils/guid-generator';\nimport { PhoneInputComponent } from \"../phone-input/phone-input.component\";\nimport { SignInUpHeaderComponent } from \"../../../../shared/components/sign-in-up-header/sign-in-up-header.component\";\nimport { PasswordInputComponent } from \"../password-input/password-input.component\";\nimport { ButtonModule } from 'primeng/button';\nimport * as CryptoJS from 'crypto-js';\nimport { EmailInputComponent } from \"../email-input/email-input.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"ngx-cookie-service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"@core/services/gtm.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nfunction SignInComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"app-phone-input\", 27);\n    i0.ɵɵlistener(\"phoneNumberChange\", function SignInComponent_div_29_Template_app_phone_input_phoneNumberChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPhoneNumberChange($event));\n    })(\"validationChange\", function SignInComponent_div_29_Template_app_phone_input_validationChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPhoneValidationChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"maxLength\", ctx_r0.phoneInputLength)(\"selectedCountryISO\", ctx_r0.customCountryISO)(\"preferredCountries\", ctx_r0.preferredCountries)(\"placeholder\", ctx_r0.customPlaceHolder);\n  }\n}\nfunction SignInComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"app-email-input\", 28);\n    i0.ɵɵlistener(\"emailChange\", function SignInComponent_div_30_Template_app_email_input_emailChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onEmailChange($event));\n    })(\"validationChange\", function SignInComponent_div_30_Template_app_email_input_validationChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onEmailValidationChange($event));\n    });\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"emailRequired\", \"false\")(\"labelColor\", ctx_r1.labelColor)(\"emailLabel\", i0.ɵɵpipeBind1(2, 3, \"auth.registerPassword.email\"));\n  }\n}\nexport let SignInComponent = /*#__PURE__*/(() => {\n  class SignInComponent {\n    store;\n    auth;\n    messageService;\n    router;\n    cartService;\n    translate;\n    cookieService;\n    authTokenService;\n    route;\n    mainDataService;\n    permissionService;\n    loaderService;\n    appDataService;\n    $gaService;\n    userService;\n    platformId;\n    $gtmService;\n    customGAService;\n    phoneNumber = null;\n    password = '';\n    isPhoneValid = false;\n    isPasswordValid = false;\n    phoneInputLength = 12;\n    customCountryISO;\n    customPlaceHolder = '';\n    preferredCountries = [CountryISO.Uganda, CountryISO.Ghana, CountryISO.CôteDIvoire];\n    submitted = false;\n    redirctURL;\n    cartListCount = 0;\n    cartListData = [];\n    isGoogleAnalytics = false;\n    isMobileLayout = false;\n    screenWidth;\n    tagName = GaActionEnum;\n    products = [];\n    activeTab = 'mobileNumber';\n    email = null;\n    isEmailValid = false;\n    constructor(store, auth, messageService, router, cartService, translate, cookieService, authTokenService, route, mainDataService, permissionService, loaderService, appDataService, $gaService, userService, platformId, $gtmService, customGAService) {\n      this.store = store;\n      this.auth = auth;\n      this.messageService = messageService;\n      this.router = router;\n      this.cartService = cartService;\n      this.translate = translate;\n      this.cookieService = cookieService;\n      this.authTokenService = authTokenService;\n      this.route = route;\n      this.mainDataService = mainDataService;\n      this.permissionService = permissionService;\n      this.loaderService = loaderService;\n      this.appDataService = appDataService;\n      this.$gaService = $gaService;\n      this.userService = userService;\n      this.platformId = platformId;\n      this.$gtmService = $gtmService;\n      this.customGAService = customGAService;\n      this.initializeComponent();\n    }\n    ngOnInit() {\n      this.setupPermissions();\n      this.setupCountryISO();\n      this.setupRouteParams();\n      this.setupPhoneInputLength();\n      this.$gtmService.pushPageView('signIn');\n    }\n    initializeComponent() {\n      this.setupCustomPlaceholder();\n      if (isPlatformBrowser(this.platformId)) {\n        this.screenWidth = window.innerWidth;\n      }\n    }\n    setupPermissions() {\n      this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n      this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    }\n    setupCountryISO() {\n      if (!localStorage.getItem(\"isoCode\")) {\n        const tenants = this.appDataService.tenants;\n        if (tenants.records != undefined) {\n          let tenantId = localStorage.getItem('tenantId');\n          let data = tenants.records;\n          let arr = data.find(element => element.tenantId == tenantId) ?? new TenantRecords();\n          localStorage.setItem('isoCode', arr?.isoCode);\n          this.store.set('allCountryTenants', tenants.records);\n        }\n      } else {\n        this.customCountryISO = localStorage.getItem(\"isoCode\");\n      }\n    }\n    setupRouteParams() {\n      this.route.queryParams.subscribe(params => {\n        this.redirctURL = params.returnUrl;\n      });\n    }\n    setupPhoneInputLength() {\n      if (this.appDataService.configuration) {\n        const phoneLength = this.appDataService.configuration.records.find(item => item.key === 'PhoneLength');\n        if (phoneLength) this.phoneInputLength = parseInt(phoneLength.value);\n      }\n    }\n    onRegisterClick() {\n      if (this.isGoogleAnalytics) {\n        this.customGAService.signupCtaClickEvent('signin_page');\n      }\n      this.router.navigate(['/register']);\n    }\n    setupCustomPlaceholder() {\n      let tenantId = localStorage.getItem('tenantId');\n      if (tenantId && tenantId !== '') {\n        const placeholders = {\n          '1': 'XXXXXXXXX',\n          '2': 'XXXXXXXXX',\n          '3': 'XXXXXXXXX',\n          '4': 'XXXXXXXXXX'\n        };\n        this.customPlaceHolder = placeholders[tenantId] || '';\n      }\n    }\n    get isFormValid() {\n      return (this.isPhoneValid || this.isEmailValid) && this.isPasswordValid && this.password.length > 0;\n    }\n    onPhoneNumberChange(phoneNumber) {\n      this.phoneNumber = phoneNumber;\n    }\n    onPhoneValidationChange(isValid) {\n      this.isPhoneValid = isValid;\n    }\n    onPasswordChange(password) {\n      this.password = password;\n    }\n    onPasswordValidationChange(isValid) {\n      this.isPasswordValid = isValid;\n    }\n    login() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (_this.isGoogleAnalytics) {\n          _this.$gaService.event(GaLocalActionEnum.CLICK_ON_CONTINUE_FOR_SIGN_IN, '', 'SIGN_IN_STEP2', 1, true);\n        }\n        _this.loaderService.show();\n        _this.submitted = true;\n        _this.auth.login({\n          username: _this.phoneNumber ? _this.phoneNumber.e164Number.slice(1) : _this.email,\n          password: _this.password\n        }).subscribe({\n          next: function () {\n            var _ref = _asyncToGenerator(function* (res) {\n              yield _this.handleLoginSuccess(res);\n            });\n            return function next(_x) {\n              return _ref.apply(this, arguments);\n            };\n          }(),\n          error: err => {\n            _this.handleLoginError(err);\n          }\n        });\n      })();\n    }\n    handleLoginSuccess(res) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (res?.success && res.data.role == 'consumer') {\n          if (_this2.isGoogleAnalytics && _this2.permissionService.getTagFeature('LOGIN')) {\n            _this2.$gaService.event(_this2.tagName.LOGIN, '', '', 1, true, {\n              \"user_ID\": res.data.mobileNumber\n            });\n          }\n          yield _this2.updateUserConsent(res.data.id);\n          _this2.setUserData(res.data);\n          _this2.setAuthToken(res.data.authToken);\n          _this2.loaderService.hide();\n          if (res?.data?.currency) {\n            _this2.store.set('currency', res.data.currency);\n          }\n          localStorage.setItem('refreshToken', res.data.refreshToken);\n          _this2.store.set('refreshToken', res.data.refreshToken);\n          const cartData = {\n            sessionId: localStorage.getItem('sessionId')\n          };\n          const cartId = localStorage.getItem('cartId');\n          yield _this2.checkCart(cartData, cartId);\n          _this2.handlePostLoginNavigation(res.data);\n        } else {\n          _this2.handleLoginFailure(res?.message);\n        }\n      })();\n    }\n    updateUserConsent(userId) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const data = {\n          consentType: UserConsentType.Cookie,\n          sessionId: localStorage.getItem('consumer-consent-sessionId') || '',\n          consent: true,\n          userId: userId\n        };\n        _this3.userService.updateUserConsent(data).subscribe({\n          next: res => {}\n        });\n      })();\n    }\n    setUserData(userData) {\n      this.mainDataService.setUserData(userData);\n      this.store.set('profile', userData);\n      this.store.set('userPhone', userData.mobileNumber);\n      localStorage.setItem('userId', userData.id);\n      this.store.set('timeInterval', new Date().getTime());\n    }\n    setAuthToken(authToken) {\n      let token = authToken.replace('bearer ', '');\n      const decoded = jwt_decode(token);\n      let days = (decoded.exp / (60 * 60 * 24 * 1000)).toFixed(0);\n      localStorage.removeItem('visited');\n      const dateNow = new Date();\n      dateNow.setDate(dateNow.getDate() + parseInt(days));\n      let encryptedMessage = CryptoJS.AES.encrypt(token, 'paysky').toString();\n      localStorage.setItem('auth_enc', encryptedMessage);\n      this.cookieService.set('authToken', token, {\n        expires: dateNow,\n        path: '/',\n        sameSite: 'Strict'\n      });\n      localStorage.removeItem('isGuest');\n      this.authTokenService.authTokenSet(token);\n    }\n    handlePostLoginNavigation(userData) {\n      if (userData.isPasswodExpired) {\n        this.router.navigateByUrl('/change-password');\n        this.messageService.add({\n          severity: 'info',\n          summary: this.translate.instant('ResponseMessages.changePassword'),\n          detail: this.translate.instant('ResponseMessages.passwordExpirationChange')\n        });\n      } else {\n        if (this.redirctURL) {\n          this.router.navigate([this.redirctURL]);\n          this.redirctURL = null;\n        } else {\n          this.router.navigate(['/']);\n        }\n        this.messageService.add({\n          severity: 'success',\n          summary: this.translate.instant('ResponseMessages.login'),\n          detail: this.translate.instant('ResponseMessages.loggedInSuccessfully')\n        });\n      }\n    }\n    handleLoginError(err) {\n      this.store.set('profile', '');\n      this.loaderService.hide();\n      this.messageService.add({\n        severity: 'error',\n        summary: this.translate.instant('ErrorMessages.fetchError'),\n        detail: err.message\n      });\n      localStorage.setItem('isGuest', 'true');\n    }\n    handleLoginFailure(message) {\n      this.store.set('profile', '');\n      this.loaderService.hide();\n      this.messageService.add({\n        severity: 'error',\n        summary: message || this.translate.instant('ErrorMessages.invalidUserNameOrPassword')\n      });\n      localStorage.setItem('isGuest', 'true');\n    }\n    resetPassword() {\n      if (this.isGoogleAnalytics) {\n        this.$gaService.event(GaLocalActionEnum.CLICK_ON_FORGOT_PASSWORD, '', 'FORGOT_PASSWORD', 1, true);\n      }\n      this.router.navigate(['/reset-password']);\n    }\n    reloadCurrentPage(pageId, title) {\n      this.router.navigateByUrl('/', {\n        skipLocationChange: true\n      }).then(() => this.router.navigate(['/about-us/'], {\n        queryParams: {\n          pageId: pageId,\n          title: title\n        }\n      }));\n    }\n    getAllCart(data) {\n      this.products = [];\n      let cartData = {\n        sessionId: data.sessionId\n      };\n      let applyTo = localStorage.getItem('apply-to');\n      if (applyTo && applyTo != '') {\n        cartData['applyTo'] = applyTo;\n      }\n      this.cartService.getCart(cartData).subscribe({\n        next: res => {\n          this.cartListCount = 0;\n          this.cartListData = [];\n          if (res.data?.records?.length) {\n            this.cartListCount = 0;\n            if (res.data.records[0].cartDetails.length) {\n              this.cartListCount = res.data.records[0].cartDetails.length;\n              this.cartListData = res.data.records[0].cartDetails;\n            }\n            if (res.data.records[0].cartDetailsDPay && res.data.records[0].cartDetailsDPay.length) {\n              this.cartListCount += res.data.records[0].cartDetailsDPay.length;\n              this.cartListData = this.cartListData.concat(res.data.records[0].cartDetailsDPay);\n            }\n            this.mainDataService._cartItemshDataAfterLoginIn.next(this.cartListData);\n            this.mainDataService.setCartLenghtData(this.cartListCount);\n            this.mainDataService.setCartItemsData(this.cartListData);\n          } else {\n            this.mainDataService.setCartLenghtData(0);\n            this.mainDataService.setCartItemsData([]);\n          }\n        }\n      });\n    }\n    compareCartProducts(products, storeProducts) {\n      if (products.length) {\n        products.forEach(item => {\n          storeProducts.forEach(data => {\n            if (item.specsProductId === data.specsProductId) {\n              this.products.push(item);\n            }\n          });\n        });\n      } else {\n        this.products = storeProducts;\n      }\n      this.store.set('cartProducts', this.products);\n      localStorage.setItem('addedProducts', JSON.stringify(this.products));\n    }\n    getShipmentMethodByTenantId(data) {\n      if (this.permissionService.hasPermission('Shipment-Fee')) {\n        this.cartService.getShipmentMethodByTenantId().subscribe(res => {\n          if (res.success && res.data.length) {\n            // this.applyTo = res.data[0].applyTo\n            localStorage.setItem('apply-to', res.data[0].applyTo);\n            this.getAllCart(data);\n          }\n        });\n      } else {\n        localStorage.setItem('apply-to', '2');\n        this.getAllCart(data);\n      }\n    }\n    checkCart(cartData, cartId) {\n      return new Promise((resolve, reject) => {\n        if (!cartData.sessionId) {\n          localStorage.setItem('sessionId', GuidGenerator.newGuid());\n          cartData.sessionId = localStorage.getItem('sessionId');\n          this.getAllCart(cartData);\n          resolve(); // Resolve the promise\n        } else {\n          if (cartId && cartId != '') {\n            cartData.cartId = parseInt(cartId);\n          } else {\n            cartData.cartId = 0;\n          }\n          this.cartService.updateCart(cartData).subscribe({\n            next: res => {\n              if (res?.data?.cartItems?.length) {\n                this.cartListData = res.data.cartItems;\n                this.cartListCount = res.data.cartItems.length;\n              }\n              this.mainDataService.setCartLenghtData(this.cartListCount);\n              this.mainDataService.setCartItemsData(this.cartListData);\n              this.getShipmentMethodByTenantId(cartData);\n              resolve(); // Resolve the promise\n            },\n\n            error: err => {\n              this.cartListCount = 0;\n              this.cartListData = [];\n              this.mainDataService.setCartLenghtData(this.cartListCount);\n              this.mainDataService.setCartItemsData(this.cartListData);\n              this.getShipmentMethodByTenantId(cartData);\n              reject(err);\n            }\n          });\n        }\n      });\n    }\n    get forgotPasswordClass() {\n      return 'font-size-12';\n    }\n    get floatingLabelEnabled() {\n      return false;\n    }\n    get buttonLabel() {\n      return 'signIn.continue';\n    }\n    switchTab(tab) {\n      this.activeTab = tab;\n      if (tab === 'mobileNumber') {\n        this.email = null;\n        this.isEmailValid = false;\n      }\n      if (tab === 'email') {\n        this.phoneNumber = null;\n        this.isPhoneValid = false;\n      }\n    }\n    get labelColor() {\n      return this.screenWidth <= 767 ? 'grey' : 'white';\n    }\n    onEmailChange(value) {\n      this.email = value;\n    }\n    onEmailValidationChange(isValid) {\n      this.isEmailValid = isValid;\n    }\n    static ɵfac = function SignInComponent_Factory(t) {\n      return new (t || SignInComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.CookieService), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i1.LoaderService), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i7.GTMService), i0.ɵɵdirectiveInject(i1.CustomGAService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignInComponent,\n      selectors: [[\"app-sign-in\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 55,\n      vars: 49,\n      consts: [[1, \"login\"], [1, \"content-container\", \"my-3\"], [1, \"grid\", \"justify-content-between\", \"mobile-top\"], [1, \"shadow-signin\"], [1, \"col-12\", \"image\"], [\"src\", \"assets/images/new-signin.svg\", \"alt\", \"\", \"srcset\", \"\"], [1, \"col-12\", \"col-md-8\", \"col-lg-6\", \"bg-white\", \"header-body\", 2, \"line-height\", \"1.5\"], [1, \"mobile-only\", 3, \"title\", \"img\"], [1, \"desktop-only\"], [1, \"signin-heading\"], [1, \"signIn-content\"], [\"autocomplete\", \"new-password\"], [1, \"tab-container\"], [1, \"tab-navigation\"], [\"type\", \"button\", 1, \"tab-button\", 3, \"click\"], [1, \"content\"], [1, \"text\"], [1, \"tab-content\", \"p-fluid\", \"p-grid\"], [\"class\", \"tab-pane\", 4, \"ngIf\"], [1, \"p-fluid\", \"p-grid\"], [3, \"showForgotPassword\", \"forgotPasswordClass\", \"floatingLabel\", \"feedback\", \"passwordChange\", \"validationChange\", \"forgotPasswordClick\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"sign-in-btn\", 3, \"disabled\", \"label\", \"click\"], [1, \"signin-agreement\"], [3, \"click\"], [1, \"new-customer-container\"], [1, \"register-now\", 3, \"click\"], [1, \"tab-pane\"], [3, \"maxLength\", \"selectedCountryISO\", \"preferredCountries\", \"placeholder\", \"phoneNumberChange\", \"validationChange\"], [1, \"customClass\", 3, \"emailRequired\", \"labelColor\", \"emailLabel\", \"emailChange\", \"validationChange\"]],\n      template: function SignInComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"sign-in-up-header\", 7);\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"p\", 9);\n          i0.ɵɵtext(10);\n          i0.ɵɵpipe(11, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 10);\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"form\", 11)(16, \"div\", 12)(17, \"div\", 13)(18, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_button_click_18_listener() {\n            return ctx.switchTab(\"mobileNumber\");\n          });\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"span\", 16);\n          i0.ɵɵtext(21);\n          i0.ɵɵpipe(22, \"translate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_button_click_23_listener() {\n            return ctx.switchTab(\"email\");\n          });\n          i0.ɵɵelementStart(24, \"div\", 15)(25, \"span\", 16);\n          i0.ɵɵtext(26);\n          i0.ɵɵpipe(27, \"translate\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 17);\n          i0.ɵɵtemplate(29, SignInComponent_div_29_Template, 2, 4, \"div\", 18);\n          i0.ɵɵtemplate(30, SignInComponent_div_30_Template, 3, 5, \"div\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 19)(32, \"app-password-input\", 20);\n          i0.ɵɵlistener(\"passwordChange\", function SignInComponent_Template_app_password_input_passwordChange_32_listener($event) {\n            return ctx.onPasswordChange($event);\n          })(\"validationChange\", function SignInComponent_Template_app_password_input_validationChange_32_listener($event) {\n            return ctx.onPasswordValidationChange($event);\n          })(\"forgotPasswordClick\", function SignInComponent_Template_app_password_input_forgotPasswordClick_32_listener() {\n            return ctx.resetPassword();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_button_click_33_listener() {\n            return ctx.login();\n          });\n          i0.ɵɵpipe(34, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\", 22);\n          i0.ɵɵtext(36);\n          i0.ɵɵpipe(37, \"translate\");\n          i0.ɵɵelementStart(38, \"a\", 23);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_a_click_38_listener() {\n            return ctx.reloadCurrentPage(171, \"Terms and Conditions\");\n          });\n          i0.ɵɵtext(39);\n          i0.ɵɵpipe(40, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41);\n          i0.ɵɵpipe(42, \"translate\");\n          i0.ɵɵelementStart(43, \"a\", 23);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_a_click_43_listener() {\n            return ctx.reloadCurrentPage(170, \"Privacy policy\");\n          });\n          i0.ɵɵtext(44);\n          i0.ɵɵpipe(45, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46);\n          i0.ɵɵpipe(47, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 24)(49, \"p\");\n          i0.ɵɵtext(50);\n          i0.ɵɵpipe(51, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"a\", 25);\n          i0.ɵɵlistener(\"click\", function SignInComponent_Template_a_click_52_listener() {\n            return ctx.onRegisterClick();\n          });\n          i0.ɵɵtext(53);\n          i0.ɵɵpipe(54, \"translate\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"title\", \"signIn.signIn\")(\"img\", \"assets/images/new-signin.svg\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 25, \"signIn.signIn\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 27, \"signIn.content\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"mobileNumber\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 29, \"contactUs.mobileNumber\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"email\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 31, \"contactUs.email\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"mobileNumber\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"email\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showForgotPassword\", true)(\"forgotPasswordClass\", ctx.forgotPasswordClass)(\"floatingLabel\", ctx.floatingLabelEnabled)(\"feedback\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFormValid)(\"label\", i0.ɵɵpipeBind1(34, 33, ctx.buttonLabel));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 35, \"signIn.AgreeTermsOne\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 37, \"signIn.AgreeTermsTwo\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(42, 39, \"signIn.AgreeTermsThree\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(45, 41, \"signIn.AgreeTermsFour\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\\u00A0\", i0.ɵɵpipeBind1(47, 43, \"signIn.AgreeTermsFive\"), \". \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(51, 45, \"signIn.newCustomer\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(54, 47, \"signIn.Register\"), \" \");\n        }\n      },\n      dependencies: [CommonModule, i8.NgIf, RouterModule, ButtonModule, i9.ButtonDirective, PhoneInputComponent, SignInUpHeaderComponent, PasswordInputComponent, TranslateModule, i4.TranslatePipe, EmailInputComponent],\n      styles: [\".header-spacing[_ngcontent-%COMP%], .header-spacing-not-navbar[_ngcontent-%COMP%]{margin-top:122px}.discount-price[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#e21836!important;font-family:var(--medium-font)!important}.login[_ngcontent-%COMP%]     button.back-btn{position:relative!important}.login   [_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}@media (min-width: 768px){.login   [_nghost-%COMP%]     button.back-btn{top:122px!important}}.login[_ngcontent-%COMP%]   .content-container[_ngcontent-%COMP%]{padding:24px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{border:1px solid rgba(32,78,110,.1019607843);border-radius:8px;background-color:#fff;display:flex;width:100%}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]{flex-direction:column}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .mobile-btn[_ngcontent-%COMP%]{border-radius:8px;padding:12px 24px;height:48px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{flex:1;text-align:end}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;width:300px}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{flex:1;max-width:600px;padding:20px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding-left:3rem;padding-right:3rem;padding-top:1.5rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]{padding:0;max-width:100%}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{color:#212121;font-size:20px;font-weight:500;margin-bottom:16px}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signin-heading[_ngcontent-%COMP%]{font-size:26px;font-weight:700;font-family:var(--medium-font)!important}}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .signIn-content[_ngcontent-%COMP%]{color:#443f3f;font-size:14px;font-style:normal;font-weight:400;line-height:130%;font-family:var(--regular-font)!important}.login[_ngcontent-%COMP%]   .shadow-signin[_ngcontent-%COMP%]   .header-body[_ngcontent-%COMP%]   .contact-label[_ngcontent-%COMP%]{pointer-events:none;padding:8px 0;font-size:12px!important;font-weight:400;color:#2d2d2d;line-height:20px}.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:block}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .mobile-only[_ngcontent-%COMP%]{display:none}}.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:none}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .desktop-only[_ngcontent-%COMP%]{display:block}}.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{height:56px;border-radius:8px;background-color:#204e6e;color:#fff;font-size:14px;font-weight:500;padding:12px 24px;display:flex;justify-content:center;align-items:center;width:100%}@media screen and (min-width: 768px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin:.5rem 0}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .sign-in-btn[_ngcontent-%COMP%]{margin-bottom:.75rem}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]{justify-content:center!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-top[_ngcontent-%COMP%]{margin-top:26px!important}}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .mobile-container[_ngcontent-%COMP%]{overflow:hidden;margin-top:0}}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]{gap:8px;padding:8px 0}.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#272727;font-size:12px;font-style:normal;font-weight:400;line-height:normal;font-family:var(--regular-font)!important}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]   .resetPassword[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--main-color);font-weight:700;margin-top:1rem;margin-bottom:1rem}}.login[_ngcontent-%COMP%]   .p-inputtext[_ngcontent-%COMP%]{background:#f5f5f5;color:#323232;font-weight:700;border-radius:5px}.login[_ngcontent-%COMP%]   .p-float-label[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#323232}.login[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;background-color:#e8effd;color:#204e6e;font-weight:500;font-size:14px;text-align:center;padding:12px 24px;border-radius:8px;display:block;margin:16px auto}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]{font-size:12px;color:#272727;margin-top:16px}.login[_ngcontent-%COMP%]   .signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;text-decoration:underline}@media screen and (max-width: 767px){.login[_ngcontent-%COMP%]{margin-top:0}}  .customClass input{height:50px!important;width:100%;border-radius:8px;opacity:1;border:1px solid #ccc!important;padding-left:10px;padding-right:10px;background-color:#fff!important;font-family:var(--medium-font)!important;font-size:16px}label[_ngcontent-%COMP%]{color:red;pointer-events:none;transition-property:all;transition-timing-function:ease;line-height:1;left:0;padding:10px;font-size:11px!important;font-weight:500;font-family:var(--medium-font)!important;margin-top:0!important;top:0!important}.signin-agreement[_ngcontent-%COMP%]{color:#272727;font-family:var(--regular-font)!important}.signin-agreement[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#204e6e;font-weight:400;font-size:12px;text-decoration:underline!important}.second-btn[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--medium-font)!important}[_nghost-%COMP%]     .iti--allow-dropdown .iti__flag-container{pointer-events:none!important}a[_ngcontent-%COMP%]:hover{color:var(--header_bgcolor)}.new-customer-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#373636;font-size:14px;font-weight:500;text-align:center;display:flex;align-items:center;gap:6px;margin:0 6px;white-space:nowrap;font-family:var(--regular-font)!important}.new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:before, .new-customer-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:after{content:\\\"\\\";flex-grow:1;display:inline-block;width:90px;height:1px;background-color:#204e6e;opacity:.1}.new-customer-container[_ngcontent-%COMP%]   .register-now[_ngcontent-%COMP%]{width:100%;text-align:center;background-color:#e8effd;color:#204e6e;font-weight:500;padding:12px 24px;border-radius:8px;border:none;margin:16px 0;font-family:var(--regular-font)!important}[_nghost-%COMP%]     .contact-input-phone{background-color:#fff!important;border:1px solid #ccc!important;border-radius:4px;padding:10px;width:100%;box-sizing:border-box}[_nghost-%COMP%]     .iti__selected-flag{padding:0 6px}[_nghost-%COMP%]     .iti__selected-flag.dropdown-toggle:before{content:\\\"\\\";position:absolute;top:15px;left:93px;width:1px;height:50%;background-color:#9ca69c;transform:translate(-50%);pointer-events:none}.tab-container[_ngcontent-%COMP%]{width:100%}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;padding:12px;min-width:100%;height:76px;background:#E8EFFD;border-radius:8px;margin-bottom:30px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 16px;gap:16px;width:50%;height:52px;border-radius:8px;border:none;background:transparent;cursor:pointer;transition:all .3s ease;font-family:var(--regular-font);font-style:normal;font-weight:500;font-size:14px;line-height:36px;letter-spacing:1.25px;color:var(--bottom_Strock, #A3A3A3);position:relative}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:11px 0;gap:8px;width:100%;height:52px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-right[_ngcontent-%COMP%]{display:none;width:16px;height:16px;font-family:Material Icons;font-style:normal;font-weight:400;font-size:16px;line-height:100%;display:flex;align-items:center;text-align:center;color:#fff}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{width:100%;height:36px;font-family:var(--regular-font);font-style:normal;font-weight:500;font-size:14px;line-height:36px;display:flex;align-items:center;justify-content:center;text-align:center;letter-spacing:1.25px;color:var(--bottom_Strock, #A3A3A3)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-left[_ngcontent-%COMP%]{display:none;width:12px;height:16px;font-family:Material Icons;font-style:normal;font-weight:400;font-size:16px;line-height:100%;display:flex;align-items:center;text-align:center;color:#fff}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover{color:#333}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]{background:#204E6E;color:var(--shades-white, #FFF)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--shades-white, #FFF);text-align:center;font-family:var(--regular-font);font-size:14px;font-style:normal;font-weight:500;line-height:36px;letter-spacing:1.25px}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-right[_ngcontent-%COMP%], .tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .v-icon-left[_ngcontent-%COMP%]{color:var(--shades-white, #FFF)}.tab-container[_ngcontent-%COMP%]   .tab-navigation[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:focus{outline:none}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[_ngcontent-%COMP%]{display:block;opacity:1;animation:fadeIn .3s ease-in-out;visibility:visible;height:auto}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[_ngcontent-%COMP%]   .uploaded-content[_ngcontent-%COMP%]{padding:40px;text-align:center;background:#f9f9f9;border-radius:8px;color:#666;font-family:var(--regular-font)}.tab-container[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]   .tab-pane[hidden][_ngcontent-%COMP%]{display:none!important}\"]\n    });\n  }\n  return SignInComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}