{"ast": null, "code": "import { EventEmitter, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from \"@core/enums/ga-local-action-enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@core/services/permission.service\";\nimport * as i6 from \"ngx-google-analytics\";\nimport * as i7 from \"ngx-cookie-service\";\nimport * as i8 from \"@pages/cart/components/services/is-opt-out.service\";\nimport * as i9 from \"@core/services/custom-GA.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"@shared/modals/age-consent-modal/age-consent-modal.component\";\nconst _c0 = function (a0) {\n  return {\n    \"opacity\": a0\n  };\n};\nfunction CheckoutCardComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 3)(2, \"section\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"p\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function CheckoutCardComponent_ng_container_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.checkAddressCityRegion());\n    });\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"span\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 11);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 12);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 13);\n    i0.ɵɵelement(19, \"path\", 14)(20, \"path\", 15);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isDisabledSubmit)(\"ngStyle\", i0.ɵɵpureFunction1(15, _c0, ctx_r0.isDisabledSubmit ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.products.length, \" \", i0.ɵɵpipeBind1(11, 8, \"checkout.items\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currencyCode, \" \", i0.ɵɵpipeBind2(14, 10, ctx_r0.grandtotal, \"1.\" + ctx_r0.decimalValue + \"-\" + ctx_r0.decimalValue), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 13, \"checkout.deliveryMethod.checkout\"), \" \");\n  }\n}\nfunction CheckoutCardComponent_ng_template_1_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"section\", 4)(2, \"h2\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"div\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 26);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 17)(13, \"div\", 18);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 26);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 27)(20, \"div\", 28);\n    i0.ɵɵtext(21, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 28);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 29)(26, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function CheckoutCardComponent_ng_template_1_div_27_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.CreateOrder());\n    });\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 12, \"checkout.proceedToCheckout.orderSummary\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(8, 14, \"checkout.proceedToCheckout.itemsCost\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.currencyCode, \" \", i0.ɵɵpipeBind2(11, 16, ctx_r5.ItemsCost, \"1.\" + ctx_r5.decimalValue + \"-\" + ctx_r5.decimalValue), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 19, \"checkout.proceedToCheckout.vat\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.currencyCode, \" \", i0.ɵɵpipeBind2(18, 21, 0, \"1.\" + ctx_r5.decimalValue + \"-\" + ctx_r5.decimalValue), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.currencyCode, \" \", i0.ɵɵpipeBind2(24, 24, ctx_r5.grandtotal, \"1.\" + ctx_r5.decimalValue + \"-\" + ctx_r5.decimalValue), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate3(\"label\", \"\", i0.ɵɵpipeBind1(27, 27, \"checkout.proceedToCheckout.proceedTo\"), \" (\", ctx_r5.products.length, \" \", ctx_r5.products.length > 1 ? i0.ɵɵpipeBind1(28, 29, \"checkout.proceedToCheckout.multipleItems\") : i0.ɵɵpipeBind1(29, 31, \"checkout.proceedToCheckout.singleItem\"), \")\");\n  }\n}\nfunction CheckoutCardComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"section\", 4)(2, \"h2\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 17)(6, \"div\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 19);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"div\", 21);\n    i0.ɵɵtext(14, \" Total \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 22);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 6)(20, \"p\", 7);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function CheckoutCardComponent_ng_template_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.checkAddressCityRegion());\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(27, CheckoutCardComponent_ng_template_1_div_27_Template, 30, 33, \"div\", 24);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 13, \"checkout.proceedToCheckout.orderSummary\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(8, 15, \"checkout.proceedToCheckout.itemsCost\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.currencyCode, \" \", i0.ɵɵpipeBind2(11, 17, ctx_r2.ItemsCost, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.currencyCode, \" \", i0.ɵɵpipeBind2(17, 20, ctx_r2.grandtotal, \"1.\" + ctx_r2.decimalValue + \"-\" + ctx_r2.decimalValue), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isDisabledSubmit)(\"ngStyle\", i0.ɵɵpureFunction1(29, _c0, ctx_r2.isDisabledSubmit ? \"0.5\" : \"\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind1(24, 23, \"checkout.proceedToCheckout.proceedTo\"), \" (\", ctx_r2.products.length, \" \", ctx_r2.products.length > 1 ? i0.ɵɵpipeBind1(25, 25, \"checkout.proceedToCheckout.multipleItems\") : i0.ɵɵpipeBind1(26, 27, \"checkout.proceedToCheckout.singleItem\"), \") \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLayoutTemplate && ctx_r2.screenWidth > 768);\n  }\n}\nexport class CheckoutCardComponent {\n  constructor(orderService, mainDataService, router, store, messageService, translate, permissionService, productLogicService, $gaService, platformId, authTokenService, cookieService, commonService, isOptOutService, authService, _GACustomEvents, cdr) {\n    this.orderService = orderService;\n    this.mainDataService = mainDataService;\n    this.router = router;\n    this.store = store;\n    this.messageService = messageService;\n    this.translate = translate;\n    this.permissionService = permissionService;\n    this.productLogicService = productLogicService;\n    this.$gaService = $gaService;\n    this.platformId = platformId;\n    this.authTokenService = authTokenService;\n    this.cookieService = cookieService;\n    this.commonService = commonService;\n    this.isOptOutService = isOptOutService;\n    this.authService = authService;\n    this._GACustomEvents = _GACustomEvents;\n    this.cdr = cdr;\n    this.products = new Array();\n    this.updateCart = new EventEmitter();\n    this.orderDetailsList = new Array();\n    this.token = '';\n    this.decimalValue = 0;\n    this.currencyCode = '';\n    this.ItemsCost = 0;\n    this.grandtotal = 0;\n    this.isShipmentFeePermission = false;\n    this.isDisabledSubmit = false;\n    this.isLayoutTemplate = false;\n    this.isGoogleAnalytics = false;\n    this.isMobileLayout = false;\n    this.isOptOutCheck = false;\n    this.onCartOptOutFlag = false;\n    this.googleAnalyticsArr = [];\n    this.event = event;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n    let value = localStorage.getItem('CurrencyDecimal');\n    if (value) this.decimalValue = parseInt(value);\n    let currency = localStorage.getItem('currency');\n    if (!currency || currency == '') {\n      currency = localStorage.getItem('Currency');\n    }\n    if (currency) this.currencyCode = currency;\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  ngOnInit() {\n    this.isShipmentFeePermission = this.permissionService.hasPermission('Shipment-Fee');\n    this.isMobileLayout = this.permissionService.hasPermission('Mobile-Layout');\n    this.sessionId = localStorage.getItem('sessionId');\n    this.userDetails = this.store.get('profile');\n    this.tenantId = localStorage.getItem(\"tenantId\");\n  }\n  ngOnChanges() {\n    this.ItemsCost = 0;\n    this.grandtotal = 0;\n    this.products.forEach(product => {\n      if (product?.salePriceValue) {\n        this.ItemsCost += product.salePriceValue * product.quantity;\n      } else {\n        this.ItemsCost += product.price * product.quantity;\n      }\n    });\n    this.grandtotal = this.ItemsCost;\n    this.checkValidation();\n  }\n  CreateOrder() {\n    this.authTokenService.authTokenData.subscribe(message => this.token = message);\n    if (!this.token && !this.cookieService.get('authToken')) {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: '/cart'\n        }\n      });\n    } else {\n      const product = this.products[0];\n      const callBackMethod = this.proceedToCheckout.bind(this);\n      this.updateCart.emit({\n        product,\n        callBackMethod,\n        isBeingProceesed: true\n      });\n    }\n  }\n  proceedToCheckout() {\n    this.productIntialize();\n    this.hasEligiblePerson = this.products.some(product => product.isAgeEligible);\n    this.maxAge = this.products.reduce((max, product) => Math.max(max, product.productEligibilityAge), 0);\n    if (this.hasEligiblePerson) this.eligibilityWarningLabel = this.products.filter(product => product.productEligibilityMessage && product.productEligibilityAge == this.maxAge).map(product => product.productEligibilityMessage)[0];\n    this.eligibilityErrorLabel = this.products.filter(product => product.productEligibilityErrorMessage && product.productEligibilityAge == this.maxAge).map(product => product.productEligibilityErrorMessage)[0];\n    if (!this.hasEligiblePerson) this.assignOrder();\n    this.cdr.detectChanges();\n  }\n  onSubmitConsent(dateOfBirth) {\n    this.order.CustomerDateOfBirth = dateOfBirth;\n    this.hasEligiblePerson = false;\n    this.assignOrder();\n  }\n  closeConsentModal() {\n    this.hasEligiblePerson = false;\n  }\n  productIntialize() {\n    let userDetails = localStorage.getItem('profile');\n    if (!userDetails || userDetails == '') {\n      this.signOut();\n      return;\n    }\n    userDetails = JSON.parse(userDetails);\n    this.mainDataService.setUserData(userDetails);\n    let name = userDetails?.name?.split(' ');\n    let cartProducts = null;\n    this.mainDataService.getCartItemsData().subscribe(res => {\n      cartProducts = res;\n    });\n    if (isPlatformBrowser(this.platformId)) {\n      let pathUrl = window.location.host.split('.');\n      let shopName = '';\n      if (pathUrl.length > 2) shopName = pathUrl[0];\n      this.totalPrice = 0.0;\n      for (let product of this.products) {\n        let item = cartProducts.find(x => x.specsProductId == product.specsProductId);\n        if (item.specProductDetails.status !== 'Rejected' && item.specProductDetails.quantity !== 0) {\n          let productDetails = {\n            productId: product.productId,\n            PriceId: product.priceId,\n            ShopId: product.shopId,\n            SpecsProductId: product.specsProductId,\n            price: product.quantity * (product.salePriceValue ? product.salePriceValue : product.price),\n            quantity: product.quantity,\n            shopName: item?.shopName,\n            categoryName: item?.categoryName,\n            shopCategoryName: item?.shopCategoryName,\n            sku: item?.sku,\n            currencyCode: product.currencyCode,\n            channelId: product.channelId,\n            productName: item?.productName,\n            imageUrl: product.imageUrl,\n            proSchedulingId: product.proSchedulingId,\n            promotionalStock: product.promotionalStock,\n            subsidized: product.isOptOut ? false : product.subsidized ? product.subsidized : false\n          };\n          const GABody = {\n            product_ID: product?.productId,\n            shop_ID: product.shopId,\n            price: (product.salePriceValue ?? product.quantity) * product.price,\n            quantity: product?.quantity,\n            shop_name: item?.shopName,\n            category_name: item?.categoryName || '',\n            shop_categoryName: item?.shopCategoryName || '',\n            product_SKU: item?.specProductDetails?.skuAutoGenerated || '',\n            product_name: item?.productName,\n            seller_name: item?.sellerName,\n            product_tags: item?.specProductDetails?.bestSeller ? 'Best Seller' : item?.specProductDetails?.newArrival ? 'New Arrival' : item?.specProductDetails?.hotDeals ? 'Hot Deals' : '',\n            promotion: product?.promotionName ? product?.promotionName : 'None'\n          };\n          if (product.salePriceValue && product.salePriceValue > 0) {\n            this.totalPrice += product.quantity * product.salePriceValue;\n          } else {\n            this.totalPrice += product.quantity * product.price;\n          }\n          this.orderDetailsList.push(productDetails);\n          this.googleAnalyticsArr.push(GABody);\n        }\n      }\n      this.order = {\n        ItemCount: this.products?.length,\n        Total: this.totalPrice,\n        CustomerFirstName: name ? name[0] ? name[0] : '' : '',\n        CustomerLastName: name ? name[1] ? name[1] : '' : '',\n        CustomerEmail: userDetails.email ?? '',\n        CustomerPhone: userDetails?.mobileNumber,\n        ShopName: shopName,\n        OrderDetailList: this.orderDetailsList\n      };\n    }\n  }\n  assignOrder() {\n    this.orderService.createOrder(this.order).subscribe({\n      next: res => {\n        if (this.isGoogleAnalytics) {\n          this.$gaService.event(GaLocalActionEnum.CLICK_ON_PROCEED_TO_CHECKOUT, 'checkout', 'PROCEED_TO_CHECKOUT', 1, true, {\n            'order_products': this.googleAnalyticsArr,\n            \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated',\n            \"session_ID\": this.sessionId,\n            \"ip_Address\": this.store.get('userIP'),\n            \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n            \"device_Id\": this.store.get('deviceInfo')?.deviceId,\n            'order_amount': this.order?.Total,\n            'order_totalItems': this.order?.ItemCount,\n            'order_ID': res?.data\n          });\n        }\n        this.store.set(\"loading\", false);\n        this.userDetails = this.store.get('profile');\n        if (res.success) {\n          if (this.isGoogleAnalytics && this.permissionService.getTagFeature('checkout')) {\n            this.$gaService.event('checkout', '', '', 1, true, {\n              \"order_ID\": res.data,\n              \"user_ID\": this.userDetails.mobileNumber,\n              \"session_ID\": this.sessionId,\n              \"ip_Address\": this.store.get('userIP'),\n              \"device_Type\": this.store.get('deviceInfo')?.deviceType,\n              \"device_Id\": this.store.get('deviceInfo')?.deviceId\n            });\n          }\n          this.store.set('orderData', {\n            orderId: res.data,\n            productDetails: this.orderDetailsList,\n            orderAmount: this.totalPrice,\n            orderDiscount: this.orderDiscountReceipt\n          });\n          const promotionProductInOrder = this.order.OrderDetailList.find(item => item.proSchedulingId);\n          if (promotionProductInOrder) {\n            this.authService.PromotionStockCheck(res?.data).subscribe({\n              next: res => {\n                if (res.data.promotionalStockAvailable) {\n                  this.router.navigate(['/checkout']);\n                } else {\n                  this.isOptOutService.updateIsOptOutCheck(true);\n                }\n              },\n              error: err => {}\n            });\n          } else {\n            this.router.navigate(['/checkout']);\n          }\n        } else {\n          this.store.set('orderData', '');\n          this.messageService.add({\n            severity: 'error',\n            summary: res.message ?? this.translate.instant('ErrorMessages.fetchError')\n          });\n        }\n      },\n      error: err => {\n        this.store.set(\"loading\", false);\n        this.store.set('orderData', '');\n        this.messageService.add({\n          severity: 'error',\n          summary: this.translate.instant('ErrorMessages.sessionTimeOut'),\n          detail: err\n        });\n      }\n    });\n  }\n  checkAddressCityRegion() {\n    this._GACustomEvents.beginPurchaseEvent(this.products);\n    this.authTokenService.authTokenData.subscribe(message => this.authToken = message);\n    if (!this.authToken) {\n      this.authToken = this.cookieService.get('authToken');\n    }\n    // if (!this.authToken) {\n    //   this.router.navigate(['login']);\n    //   return;\n    // }\n    const cartData = this.productLogicService.cartProductList;\n    let isError = false;\n    if (cartData) {\n      for (let cart of cartData) {\n        if (!cart.shipmentFeeExists) {\n          isError = true;\n          break;\n        }\n      }\n      if (isError && this.isShipmentFeePermission) {\n        this.errorMessage = this.translate.instant('cart.cartDetail.cantDeliverLocationMessage');\n      } else {\n        this.CreateOrder();\n      }\n    } else {\n      this.CreateOrder();\n    }\n  }\n  checkValidation() {\n    this.isDisabledSubmit = false;\n    let isDisabled = this.products.find(product => product.specProductDetails?.status === 'Rejected' || product.specProductDetails?.quantity === 0);\n    if (isDisabled) {\n      this.isDisabledSubmit = true;\n    }\n  }\n  signOut() {\n    this.commonService.logOut();\n    this.router.navigate(['/login']);\n  }\n}\nCheckoutCardComponent.ɵfac = function CheckoutCardComponent_Factory(t) {\n  return new (t || CheckoutCardComponent)(i0.ɵɵdirectiveInject(i1.OrderService), i0.ɵɵdirectiveInject(i1.MainDataService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.PermissionService), i0.ɵɵdirectiveInject(i1.ProductLogicService), i0.ɵɵdirectiveInject(i6.GoogleAnalyticsService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i7.CookieService), i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i8.IsOptOutService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i9.CustomGAService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nCheckoutCardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CheckoutCardComponent,\n  selectors: [[\"app-checkout-card\"]],\n  inputs: {\n    products: \"products\",\n    orderDiscountReceipt: \"orderDiscountReceipt\"\n  },\n  outputs: {\n    updateCart: \"updateCart\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 4,\n  vars: 6,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [3, \"age\", \"displayModal\", \"eligibilityWarningLabel\", \"eligibilityErrorLabel\", \"submit\", \"cancel\"], [1, \"new-checkout-card\"], [1, \"checkout-card\"], [1, \"row\"], [1, \"col-md-12\", \"error-container\"], [1, \"error-msg\"], [1, \"button-container-mobile\", 3, \"disabled\", \"ngStyle\", \"click\"], [1, \"button-content\"], [1, \"items\"], [1, \"price\"], [1, \"checkout-button\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 20 20\", \"fill\", \"none\"], [\"d\", \"M3.125 10L16.875 10\", \"stroke\", \"#F5F7FC\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M11.25 15.625L16.875 10L11.25 4.375\", \"stroke\", \"#F5F7FC\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"order-sumary\", \"mb-0\"], [1, \"col-12\", \"flex\", \"flex-row\", \"align-items-start\", \"justify-content-between\", \"my-2\", \"p-0\"], [1, \"mr-2\", \"order-sumary-heading\"], [1, \"order-cost\"], [1, \"d-flex\", \"justify-content-space-between\", \"checkout-card__total\"], [1, \"checkout-card__total__title\"], [1, \"checkout-card__total__value\"], [1, \"checkout-card__checkout\", 3, \"disabled\", \"ngStyle\", \"click\"], [\"class\", \"old-checkout-card\", 4, \"ngIf\"], [1, \"old-checkout-card\"], [1, \"mr-2\", \"order-cost\"], [1, \"col-12\", \"flex\", \"flex-row\", \"align-items-start\", \"justify-content-between\", \"my-2\", \"p-0\", \"border-bottom-line\"], [1, \"mr-2\", \"order-sumary-total\"], [1, \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"col-12\", \"my-2\", \"width-100\", \"second-btn\", 3, \"label\", \"click\"]],\n  template: function CheckoutCardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CheckoutCardComponent_ng_container_0_Template, 21, 17, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, CheckoutCardComponent_ng_template_1_Template, 28, 31, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(3, \"app-age-consent-modal\", 2);\n      i0.ɵɵlistener(\"submit\", function CheckoutCardComponent_Template_app_age_consent_modal_submit_3_listener($event) {\n        return ctx.onSubmitConsent($event);\n      })(\"cancel\", function CheckoutCardComponent_Template_app_age_consent_modal_cancel_3_listener() {\n        return ctx.closeConsentModal();\n      });\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isMobileLayout && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"age\", ctx.maxAge)(\"displayModal\", ctx.hasEligiblePerson)(\"eligibilityWarningLabel\", ctx.eligibilityWarningLabel)(\"eligibilityErrorLabel\", ctx.eligibilityErrorLabel);\n    }\n  },\n  dependencies: [i10.NgIf, i10.NgStyle, i11.ButtonDirective, i12.AgeConsentModalComponent, i10.DecimalPipe, i4.TranslatePipe],\n  styles: [\".new-checkout-card[_ngcontent-%COMP%]   .checkout-card__heading[_ngcontent-%COMP%] {\\n  color: #191C1F;\\n  font-family: var(--medium-font);\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 24px;\\n  \\n\\n  padding: 20px 0px;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #E4E7E9;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__summary__title[_ngcontent-%COMP%] {\\n  color: var(--gray-600, #5F6C72);\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 20px;\\n  \\n\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__summary__value[_ngcontent-%COMP%] {\\n  color: var(--gray-900, #191C1F);\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 20px;\\n  \\n\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__total[_ngcontent-%COMP%] {\\n  padding: 16px 0px 24px 0px;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__total__title[_ngcontent-%COMP%] {\\n  color: var(--gray-600, #5F6C72);\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 20px;\\n  \\n\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__total__value[_ngcontent-%COMP%] {\\n  color: var(--gray-900, #191C1F);\\n  font-family: var(--regular-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 20px;\\n  \\n\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .checkout-card__checkout[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 56px;\\n  padding: 0px 0px;\\n  justify-content: center;\\n  align-self: stretch;\\n  border-radius: 6px;\\n  background: var(--main_bt_txtcolor);\\n  color: #FFF;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 56px;\\n  \\n\\n  letter-spacing: 0.168px;\\n  text-transform: uppercase;\\n  border: none;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n  width: auto;\\n  height: 45px;\\n  border-radius: 20px;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]     .p-button-label {\\n  text-transform: uppercase !important;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]:focus {\\n  outline: 0 none;\\n  outline-offset: 0;\\n  box-shadow: 0 0 0 0.2rem #ffcc00;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .order-sumary-heading[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 15px;\\n  font-family: var(--regular-font) !important;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .order-sumary[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 20px;\\n  font-family: var(--medium-font) !important;\\n  margin-bottom: 25px !important;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .order-sumary-total[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 15px;\\n  font-family: var(--medium-font) !important;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .order-cost[_ngcontent-%COMP%] {\\n  color: #A3A3A3;\\n  font-weight: 400;\\n  font-size: 15px;\\n  font-family: var(--regular-font) !important;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .border-bottom-line[_ngcontent-%COMP%] {\\n  line-height: 44px;\\n  border-bottom: 2px solid #F1F2F3;\\n  margin-bottom: 16px !important;\\n}\\n.new-checkout-card[_ngcontent-%COMP%]   .error-container[_ngcontent-%COMP%]   .error-msg[_ngcontent-%COMP%] {\\n  color: var(--custom-error, #FF5252);\\n  font-family: var(--medium-font);\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n}\\n@media screen and (max-width: 768px) {\\n  .new-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n    width: 296px !important;\\n    height: 43px !important;\\n    font-size: 14px;\\n    font-weight: 500;\\n  }\\n  .new-checkout-card[_ngcontent-%COMP%]   .order-sumary[_ngcontent-%COMP%] {\\n    font-size: 16px !important;\\n  }\\n}\\n@media only screen and (min-width: 900px) and (max-width: 1366px) {\\n  .new-checkout-card[_ngcontent-%COMP%]   .checkout-card__checkout[_ngcontent-%COMP%] {\\n    padding: 0px 0px;\\n    font-size: 11px;\\n  }\\n}\\n\\n.old-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n  width: auto;\\n  height: 45px;\\n  border-radius: 20px;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]     .p-button-label {\\n  text-transform: uppercase !important;\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%]:focus {\\n  outline: 0 none;\\n  outline-offset: 0;\\n  box-shadow: 0 0 0 0.2rem #ffcc00;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .order-sumary-heading[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 15px;\\n  font-family: var(--regular-font) !important;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .order-sumary[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 20px;\\n  font-family: var(--medium-font) !important;\\n  margin-bottom: 25px !important;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .order-sumary-total[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 15px;\\n  font-family: var(--medium-font) !important;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .order-cost[_ngcontent-%COMP%] {\\n  color: #A3A3A3;\\n  font-weight: 400;\\n  font-size: 15px;\\n  font-family: var(--regular-font) !important;\\n}\\n.old-checkout-card[_ngcontent-%COMP%]   .border-bottom-line[_ngcontent-%COMP%] {\\n  line-height: 44px;\\n  border-bottom: 2px solid #F1F2F3;\\n  margin-bottom: 16px !important;\\n}\\n@media screen and (max-width: 768px) {\\n  .old-checkout-card[_ngcontent-%COMP%]   .second-btn[_ngcontent-%COMP%] {\\n    width: 296px !important;\\n    height: 43px !important;\\n    font-size: 14px;\\n    font-weight: 500;\\n  }\\n  .old-checkout-card[_ngcontent-%COMP%]   .order-sumary[_ngcontent-%COMP%] {\\n    font-size: 16px !important;\\n  }\\n}\\n\\n.button-container-mobile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 56px;\\n  width: 100%;\\n  padding: 0px 12px;\\n  border-radius: 6px;\\n  background: var(--main_bt_txtcolor);\\n  color: #FFF;\\n  font-family: var(--medium-font);\\n  font-size: 14px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 56px;\\n  letter-spacing: 0.168px;\\n  border: none;\\n  justify-content: space-between;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  line-height: 1.7;\\n  text-align: start;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-family: \\\"main-regular\\\";\\n  text-transform: lowercase;\\n  font-weight: 400;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-family: \\\"main-regular\\\";\\n  font-weight: 700;\\n}\\n.button-container-mobile[_ngcontent-%COMP%]   .checkout-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: none;\\n  color: #fff;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 700;\\n  font-family: \\\"main-regular\\\";\\n  display: inline-flex;\\n  width: 60%;\\n  justify-content: space-between;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n\\n.arrow[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  font-size: 24px;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}