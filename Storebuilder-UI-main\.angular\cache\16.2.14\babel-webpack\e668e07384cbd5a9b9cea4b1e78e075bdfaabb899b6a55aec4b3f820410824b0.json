{"ast": null, "code": "import { IndexComponent } from \"./components/index/index.component\";\nimport { OrderPlacedComponent } from \"./components/order-placed/order-placed.component\";\nimport { SelectAddressMobiluiComponent } from \"./components/select-address-mobilui/select-address-mobilui.component\";\nexport const routes = [{\n  path: '',\n  component: IndexComponent\n}, {\n  path: 'success',\n  component: OrderPlacedComponent\n}, {\n  path: 'selectAddress',\n  component: SelectAddressMobiluiComponent\n}];", "map": {"version": 3, "names": ["IndexComponent", "OrderPlacedComponent", "SelectAddressMobiluiComponent", "routes", "path", "component"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\checkout\\routes.ts"], "sourcesContent": ["import {Routes} from \"@angular/router\";\r\nimport {IndexComponent} from \"./components/index/index.component\";\r\nimport {OrderPlacedComponent} from \"./components/order-placed/order-placed.component\";\r\nimport { SelectAddressMobiluiComponent } from \"./components/select-address-mobilui/select-address-mobilui.component\";\r\n\r\nexport const routes: Routes = [\r\n  { path: '', component: IndexComponent },\r\n  { path: 'success', component: OrderPlacedComponent },\r\n  { path: 'selectAddress', component: SelectAddressMobiluiComponent },\r\n];\r\n"], "mappings": "AACA,SAAQA,cAAc,QAAO,oCAAoC;AACjE,SAAQC,oBAAoB,QAAO,kDAAkD;AACrF,SAASC,6BAA6B,QAAQ,sEAAsE;AAEpH,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEL;AAAc,CAAE,EACvC;EAAEI,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEJ;AAAoB,CAAE,EACpD;EAAEG,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEH;AAA6B,CAAE,CACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}