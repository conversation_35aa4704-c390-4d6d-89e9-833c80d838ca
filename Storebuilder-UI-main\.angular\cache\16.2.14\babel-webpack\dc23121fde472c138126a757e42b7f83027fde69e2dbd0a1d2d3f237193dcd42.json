{"ast": null, "code": "import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n  constructor(open, close) {\n    this.open = open;\n    this.close = close || open;\n  }\n  isManual() {\n    return this.open === 'manual' || this.close === 'manual';\n  }\n}\nconst DEFAULT_ALIASES = {\n  hover: ['mouseover', 'mouseout'],\n  focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n  const trimmedTriggers = (triggers || '').trim();\n  if (trimmedTriggers.length === 0) {\n    return [];\n  }\n  const parsedTriggers = trimmedTriggers.split(/\\s+/).map(trigger => trigger.split(':')).map(triggerPair => {\n    const alias = aliases[triggerPair[0]] || triggerPair;\n    return new Trigger(alias[0], alias[1]);\n  });\n  const manualTriggers = parsedTriggers.filter(triggerPair => triggerPair.isManual());\n  if (manualTriggers.length > 1) {\n    throw new Error('Triggers parse error: only one manual trigger is allowed');\n  }\n  if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n    throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n  }\n  return parsedTriggers;\n}\nfunction listenToTriggers(renderer,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n  const parsedTriggers = parseTriggers(triggers);\n  const listeners = [];\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  parsedTriggers.forEach(trigger => {\n    if (trigger.open === trigger.close) {\n      listeners.push(renderer.listen(target, trigger.open, toggleFn));\n      return;\n    }\n    listeners.push(renderer.listen(target, trigger.open, showFn));\n    if (trigger.close) {\n      listeners.push(renderer.listen(target, trigger.close, hideFn));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction listenToTriggersV2(renderer, options) {\n  const parsedTriggers = parseTriggers(options.triggers);\n  const target = options.target;\n  // do nothing\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  // all listeners\n  const listeners = [];\n  // lazy listeners registration\n  const _registerHide = [];\n  const registerHide = () => {\n    // add hide listeners to unregister array\n    _registerHide.forEach(fn => listeners.push(fn()));\n    // register hide events only once\n    _registerHide.length = 0;\n  };\n  // register open\\close\\toggle listeners\n  parsedTriggers.forEach(trigger => {\n    const useToggle = trigger.open === trigger.close;\n    const showFn = useToggle ? options.toggle : options.show;\n    if (!useToggle && trigger.close && options.hide) {\n      const triggerClose = trigger.close;\n      const optionsHide = options.hide;\n      const _hide = () => renderer.listen(target, triggerClose, optionsHide);\n      _registerHide.push(_hide);\n    }\n    if (showFn) {\n      listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction registerOutsideClick(renderer, options) {\n  if (!options.outsideClick) {\n    return Function.prototype;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return renderer.listen('document', 'click', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\nfunction registerEscClick(renderer, options) {\n  if (!options.outsideEsc) {\n    return Function.prototype;\n  }\n  return renderer.listen('document', 'keyup.esc', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = typeof window !== 'undefined' && window || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\nvar BsVerions;\n(function (BsVerions) {\n  BsVerions[\"isBs4\"] = \"bs4\";\n  BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n  const spanEl = win.document.createElement('span');\n  spanEl.innerText = 'testing bs version';\n  spanEl.classList.add('d-none');\n  spanEl.classList.add('pl-1');\n  win.document.head.appendChild(spanEl);\n  const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n  if (checkPadding && parseFloat(checkPadding)) {\n    win.document.head.removeChild(spanEl);\n    return 'bs4';\n  }\n  win.document.head.removeChild(spanEl);\n  return 'bs5';\n}\nfunction setTheme(theme) {\n  guessedVersion = theme;\n}\nfunction isBs4() {\n  if (guessedVersion) return guessedVersion === 'bs4';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n  if (guessedVersion) return guessedVersion === 'bs5';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n  return {\n    isBs4: isBs4(),\n    isBs5: isBs5()\n  };\n}\nfunction currentBsVersion() {\n  const bsVer = getBsVer();\n  const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n  return BsVerions[resVersion];\n}\nclass LinkedList {\n  constructor() {\n    this.length = 0;\n    this.asArray = [];\n    // Array methods overriding END\n  }\n\n  get(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      return void 0;\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current?.value;\n  }\n  add(value, position = this.length) {\n    if (position < 0 || position > this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = {\n      value,\n      next: undefined,\n      previous: undefined\n    };\n    if (this.length === 0) {\n      this.head = node;\n      this.tail = node;\n      this.current = node;\n    } else {\n      if (position === 0 && this.head) {\n        // first node\n        node.next = this.head;\n        this.head.previous = node;\n        this.head = node;\n      } else if (position === this.length && this.tail) {\n        // last node\n        this.tail.next = node;\n        node.previous = this.tail;\n        this.tail = node;\n      } else {\n        // node in middle\n        const currentPreviousNode = this.getNode(position - 1);\n        const currentNextNode = currentPreviousNode?.next;\n        if (currentPreviousNode && currentNextNode) {\n          currentPreviousNode.next = node;\n          currentNextNode.previous = node;\n          node.previous = currentPreviousNode;\n          node.next = currentNextNode;\n        }\n      }\n    }\n    this.length++;\n    this.createInternalArrayRepresentation();\n  }\n  remove(position = 0) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    if (position === 0 && this.head) {\n      // first node\n      this.head = this.head.next;\n      if (this.head) {\n        // there is no second node\n        this.head.previous = undefined;\n      } else {\n        // there is no second node\n        this.tail = undefined;\n      }\n    } else if (position === this.length - 1 && this.tail?.previous) {\n      // last node\n      this.tail = this.tail.previous;\n      this.tail.next = undefined;\n    } else {\n      // middle node\n      const removedNode = this.getNode(position);\n      if (removedNode?.next && removedNode.previous) {\n        removedNode.next.previous = removedNode.previous;\n        removedNode.previous.next = removedNode.next;\n      }\n    }\n    this.length--;\n    this.createInternalArrayRepresentation();\n  }\n  set(position, value) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = this.getNode(position);\n    if (node) {\n      node.value = value;\n      this.createInternalArrayRepresentation();\n    }\n  }\n  toArray() {\n    return this.asArray;\n  }\n  findAll(fn) {\n    let current = this.head;\n    const result = [];\n    if (!current) {\n      return result;\n    }\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return result;\n      }\n      if (fn(current.value, index)) {\n        result.push({\n          index,\n          value: current.value\n        });\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  // Array methods overriding start\n  push(...args) {\n    args.forEach(arg => {\n      this.add(arg);\n    });\n    return this.length;\n  }\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const last = this.tail;\n    this.remove(this.length - 1);\n    return last?.value;\n  }\n  unshift(...args) {\n    args.reverse();\n    args.forEach(arg => {\n      this.add(arg, 0);\n    });\n    return this.length;\n  }\n  shift() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const lastItem = this.head?.value;\n    this.remove();\n    return lastItem;\n  }\n  forEach(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      fn(current.value, index);\n      current = current.next;\n    }\n  }\n  indexOf(value) {\n    let current = this.head;\n    let position = -1;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return position;\n      }\n      if (current.value === value) {\n        position = index;\n        break;\n      }\n      current = current.next;\n    }\n    return position;\n  }\n  some(fn) {\n    let current = this.head;\n    let result = false;\n    while (current && !result) {\n      if (fn(current.value)) {\n        result = true;\n        break;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  every(fn) {\n    let current = this.head;\n    let result = true;\n    while (current && result) {\n      if (!fn(current.value)) {\n        result = false;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  toString() {\n    return '[Linked List]';\n  }\n  find(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      if (fn(current.value, index)) {\n        return current.value;\n      }\n      current = current.next;\n    }\n  }\n  findIndex(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return -1;\n      }\n      if (fn(current.value, index)) {\n        return index;\n      }\n      current = current.next;\n    }\n    return -1;\n  }\n  getNode(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current;\n  }\n  createInternalArrayRepresentation() {\n    const outArray = [];\n    let current = this.head;\n    while (current) {\n      outArray.push(current.value);\n      current = current.next;\n    }\n    this.asArray = outArray;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n  const sufix = 'Change';\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return function OnChangeHandler(target, propertyKey) {\n    const _key = ` __${propertyKey}Value`;\n    Object.defineProperty(target, propertyKey, {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get() {\n        return this[_key];\n      },\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      set(value) {\n        const prevValue = this[_key];\n        this[_key] = value;\n        if (prevValue !== value && this[propertyKey + sufix]) {\n          this[propertyKey + sufix].emit(value);\n        }\n      }\n    });\n  };\n}\nclass Utils {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static reflow(element) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (bs => bs)(element.offsetHeight);\n  }\n  // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static getStyles(elem) {\n    // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n    // IE throws on elements created in popups\n    // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n    let view = elem.ownerDocument.defaultView;\n    if (!view || !view.opener) {\n      view = win;\n    }\n    return view.getComputedStyle(elem);\n  }\n  static stackOverflowConfig() {\n    const bsVer = currentBsVersion();\n    return {\n      crossorigin: \"anonymous\",\n      integrity: bsVer === 'bs5' ? 'sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65' : 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2',\n      cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css' : 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css'\n    };\n  }\n}\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n  if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n    return;\n  }\n  _messagesHash[msg] = true;\n  console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };", "map": {"version": 3, "names": ["isDevMode", "<PERSON><PERSON>", "constructor", "open", "close", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ALIASES", "hover", "focus", "parseTriggers", "triggers", "aliases", "trimmedTriggers", "trim", "length", "parsedTriggers", "split", "map", "trigger", "triggerPair", "alias", "manualTriggers", "filter", "Error", "listenToTriggers", "renderer", "target", "showFn", "hideFn", "toggleFn", "listeners", "Function", "prototype", "for<PERSON>ach", "push", "listen", "unsubscribeFn", "listenToTriggersV2", "options", "_registerHide", "registerHide", "fn", "useToggle", "toggle", "show", "hide", "triggerClose", "optionsHide", "_hide", "registerOutsideClick", "outsideClick", "event", "contains", "targets", "some", "registerEscClick", "outsideEsc", "win", "window", "document", "location", "gc", "performance", "Event", "MouseEvent", "KeyboardEvent", "EventTarget", "History", "Location", "EventListener", "BsVerions", "guessedVersion", "_guessBsVersion", "spanEl", "createElement", "innerText", "classList", "add", "head", "append<PERSON><PERSON><PERSON>", "checkPadding", "getComputedStyle", "paddingLeft", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "setTheme", "theme", "isBs4", "isBs5", "getBsVer", "currentBsVersion", "bsVer", "resVersion", "Object", "keys", "find", "key", "LinkedList", "asArray", "get", "position", "current", "index", "next", "value", "node", "undefined", "previous", "tail", "currentPreviousNode", "getNode", "currentNextNode", "createInternalArrayRepresentation", "remove", "removedNode", "set", "toArray", "findAll", "result", "args", "arg", "pop", "last", "unshift", "reverse", "shift", "lastItem", "indexOf", "every", "toString", "findIndex", "outArray", "OnChange", "sufix", "OnChangeHandler", "propertyKey", "_key", "defineProperty", "prevValue", "emit", "Utils", "reflow", "element", "bs", "offsetHeight", "getStyles", "elem", "view", "ownerDocument", "defaultView", "opener", "stackOverflowConfig", "crossorigin", "integrity", "cdnLink", "_messagesHash", "_hideMsg", "console", "warnOnce", "msg", "warn"], "sources": ["C:/projects/MTN/FrontEnd/Frontend/Storebuilder-UI-main/node_modules/ngx-bootstrap/utils/fesm2022/ngx-bootstrap-utils.mjs"], "sourcesContent": ["import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n    constructor(open, close) {\n        this.open = open;\n        this.close = close || open;\n    }\n    isManual() {\n        return this.open === 'manual' || this.close === 'manual';\n    }\n}\n\nconst DEFAULT_ALIASES = {\n    hover: ['mouseover', 'mouseout'],\n    focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n    const trimmedTriggers = (triggers || '').trim();\n    if (trimmedTriggers.length === 0) {\n        return [];\n    }\n    const parsedTriggers = trimmedTriggers\n        .split(/\\s+/)\n        .map((trigger) => trigger.split(':'))\n        .map((triggerPair) => {\n        const alias = aliases[triggerPair[0]] || triggerPair;\n        return new Trigger(alias[0], alias[1]);\n    });\n    const manualTriggers = parsedTriggers.filter((triggerPair) => triggerPair.isManual());\n    if (manualTriggers.length > 1) {\n        throw new Error('Triggers parse error: only one manual trigger is allowed');\n    }\n    if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n        throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n    }\n    return parsedTriggers;\n}\nfunction listenToTriggers(renderer, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n    const parsedTriggers = parseTriggers(triggers);\n    const listeners = [];\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    parsedTriggers.forEach((trigger) => {\n        if (trigger.open === trigger.close) {\n            listeners.push(renderer.listen(target, trigger.open, toggleFn));\n            return;\n        }\n        listeners.push(renderer.listen(target, trigger.open, showFn));\n        if (trigger.close) {\n            listeners.push(renderer.listen(target, trigger.close, hideFn));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction listenToTriggersV2(renderer, options) {\n    const parsedTriggers = parseTriggers(options.triggers);\n    const target = options.target;\n    // do nothing\n    if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n        return Function.prototype;\n    }\n    // all listeners\n    const listeners = [];\n    // lazy listeners registration\n    const _registerHide = [];\n    const registerHide = () => {\n        // add hide listeners to unregister array\n        _registerHide.forEach((fn) => listeners.push(fn()));\n        // register hide events only once\n        _registerHide.length = 0;\n    };\n    // register open\\close\\toggle listeners\n    parsedTriggers.forEach((trigger) => {\n        const useToggle = trigger.open === trigger.close;\n        const showFn = useToggle ? options.toggle : options.show;\n        if (!useToggle && trigger.close && options.hide) {\n            const triggerClose = trigger.close;\n            const optionsHide = options.hide;\n            const _hide = () => renderer.listen(target, triggerClose, optionsHide);\n            _registerHide.push(_hide);\n        }\n        if (showFn) {\n            listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n        }\n    });\n    return () => {\n        listeners.forEach((unsubscribeFn) => unsubscribeFn());\n    };\n}\nfunction registerOutsideClick(renderer, options) {\n    if (!options.outsideClick) {\n        return Function.prototype;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return renderer.listen('document', 'click', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\nfunction registerEscClick(renderer, options) {\n    if (!options.outsideEsc) {\n        return Function.prototype;\n    }\n    return renderer.listen('document', 'keyup.esc', (event) => {\n        if (options.target && options.target.contains(event.target)) {\n            return;\n        }\n        if (options.targets &&\n            options.targets.some(target => target.contains(event.target))) {\n            return;\n        }\n        if (options.hide) {\n            options.hide();\n        }\n    });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = (typeof window !== 'undefined' && window) || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\n\nvar BsVerions;\n(function (BsVerions) {\n    BsVerions[\"isBs4\"] = \"bs4\";\n    BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n    const spanEl = win.document.createElement('span');\n    spanEl.innerText = 'testing bs version';\n    spanEl.classList.add('d-none');\n    spanEl.classList.add('pl-1');\n    win.document.head.appendChild(spanEl);\n    const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n    if (checkPadding && parseFloat(checkPadding)) {\n        win.document.head.removeChild(spanEl);\n        return 'bs4';\n    }\n    win.document.head.removeChild(spanEl);\n    return 'bs5';\n}\nfunction setTheme(theme) {\n    guessedVersion = theme;\n}\nfunction isBs4() {\n    if (guessedVersion)\n        return guessedVersion === 'bs4';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n    if (guessedVersion)\n        return guessedVersion === 'bs5';\n    guessedVersion = _guessBsVersion();\n    return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n    return {\n        isBs4: isBs4(),\n        isBs5: isBs5()\n    };\n}\nfunction currentBsVersion() {\n    const bsVer = getBsVer();\n    const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n    return BsVerions[resVersion];\n}\n\nclass LinkedList {\n    constructor() {\n        this.length = 0;\n        this.asArray = [];\n        // Array methods overriding END\n    }\n    get(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            return void 0;\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current?.value;\n    }\n    add(value, position = this.length) {\n        if (position < 0 || position > this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = {\n            value,\n            next: undefined,\n            previous: undefined\n        };\n        if (this.length === 0) {\n            this.head = node;\n            this.tail = node;\n            this.current = node;\n        }\n        else {\n            if (position === 0 && this.head) {\n                // first node\n                node.next = this.head;\n                this.head.previous = node;\n                this.head = node;\n            }\n            else if (position === this.length && this.tail) {\n                // last node\n                this.tail.next = node;\n                node.previous = this.tail;\n                this.tail = node;\n            }\n            else {\n                // node in middle\n                const currentPreviousNode = this.getNode(position - 1);\n                const currentNextNode = currentPreviousNode?.next;\n                if (currentPreviousNode && currentNextNode) {\n                    currentPreviousNode.next = node;\n                    currentNextNode.previous = node;\n                    node.previous = currentPreviousNode;\n                    node.next = currentNextNode;\n                }\n            }\n        }\n        this.length++;\n        this.createInternalArrayRepresentation();\n    }\n    remove(position = 0) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        if (position === 0 && this.head) {\n            // first node\n            this.head = this.head.next;\n            if (this.head) {\n                // there is no second node\n                this.head.previous = undefined;\n            }\n            else {\n                // there is no second node\n                this.tail = undefined;\n            }\n        }\n        else if (position === this.length - 1 && this.tail?.previous) {\n            // last node\n            this.tail = this.tail.previous;\n            this.tail.next = undefined;\n        }\n        else {\n            // middle node\n            const removedNode = this.getNode(position);\n            if (removedNode?.next && removedNode.previous) {\n                removedNode.next.previous = removedNode.previous;\n                removedNode.previous.next = removedNode.next;\n            }\n        }\n        this.length--;\n        this.createInternalArrayRepresentation();\n    }\n    set(position, value) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        const node = this.getNode(position);\n        if (node) {\n            node.value = value;\n            this.createInternalArrayRepresentation();\n        }\n    }\n    toArray() {\n        return this.asArray;\n    }\n    findAll(fn) {\n        let current = this.head;\n        const result = [];\n        if (!current) {\n            return result;\n        }\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return result;\n            }\n            if (fn(current.value, index)) {\n                result.push({ index, value: current.value });\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    // Array methods overriding start\n    push(...args) {\n        args.forEach((arg) => {\n            this.add(arg);\n        });\n        return this.length;\n    }\n    pop() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const last = this.tail;\n        this.remove(this.length - 1);\n        return last?.value;\n    }\n    unshift(...args) {\n        args.reverse();\n        args.forEach((arg) => {\n            this.add(arg, 0);\n        });\n        return this.length;\n    }\n    shift() {\n        if (this.length === 0) {\n            return undefined;\n        }\n        const lastItem = this.head?.value;\n        this.remove();\n        return lastItem;\n    }\n    forEach(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            fn(current.value, index);\n            current = current.next;\n        }\n    }\n    indexOf(value) {\n        let current = this.head;\n        let position = -1;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return position;\n            }\n            if (current.value === value) {\n                position = index;\n                break;\n            }\n            current = current.next;\n        }\n        return position;\n    }\n    some(fn) {\n        let current = this.head;\n        let result = false;\n        while (current && !result) {\n            if (fn(current.value)) {\n                result = true;\n                break;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    every(fn) {\n        let current = this.head;\n        let result = true;\n        while (current && result) {\n            if (!fn(current.value)) {\n                result = false;\n            }\n            current = current.next;\n        }\n        return result;\n    }\n    toString() {\n        return '[Linked List]';\n    }\n    find(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return;\n            }\n            if (fn(current.value, index)) {\n                return current.value;\n            }\n            current = current.next;\n        }\n    }\n    findIndex(fn) {\n        let current = this.head;\n        for (let index = 0; index < this.length; index++) {\n            if (!current) {\n                return -1;\n            }\n            if (fn(current.value, index)) {\n                return index;\n            }\n            current = current.next;\n        }\n        return -1;\n    }\n    getNode(position) {\n        if (this.length === 0 || position < 0 || position >= this.length) {\n            throw new Error('Position is out of the list');\n        }\n        let current = this.head;\n        for (let index = 0; index < position; index++) {\n            current = current?.next;\n        }\n        return current;\n    }\n    createInternalArrayRepresentation() {\n        const outArray = [];\n        let current = this.head;\n        while (current) {\n            outArray.push(current.value);\n            current = current.next;\n        }\n        this.asArray = outArray;\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n    const sufix = 'Change';\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function OnChangeHandler(target, propertyKey) {\n        const _key = ` __${propertyKey}Value`;\n        Object.defineProperty(target, propertyKey, {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            get() {\n                return this[_key];\n            },\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            set(value) {\n                const prevValue = this[_key];\n                this[_key] = value;\n                if (prevValue !== value && this[propertyKey + sufix]) {\n                    this[propertyKey + sufix].emit(value);\n                }\n            }\n        });\n    };\n}\n\nclass Utils {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static reflow(element) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ((bs) => bs)(element.offsetHeight);\n    }\n    // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    static getStyles(elem) {\n        // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n        // IE throws on elements created in popups\n        // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n        let view = elem.ownerDocument.defaultView;\n        if (!view || !view.opener) {\n            view = win;\n        }\n        return view.getComputedStyle(elem);\n    }\n    static stackOverflowConfig() {\n        const bsVer = currentBsVersion();\n        return {\n            crossorigin: \"anonymous\",\n            integrity: bsVer === 'bs5' ? 'sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65' : 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2',\n            cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css' : 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css',\n        };\n    }\n}\n\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n    if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n        return;\n    }\n    _messagesHash[msg] = true;\n    console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;;AAEzC;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK,IAAID,IAAI;EAC9B;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACC,KAAK,KAAK,QAAQ;EAC5D;AACJ;AAEA,MAAME,eAAe,GAAG;EACpBC,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC;EAChCC,KAAK,EAAE,CAAC,SAAS,EAAE,UAAU;AACjC,CAAC;AACD;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAEC,OAAO,GAAGL,eAAe,EAAE;EACxD,MAAMM,eAAe,GAAG,CAACF,QAAQ,IAAI,EAAE,EAAEG,IAAI,CAAC,CAAC;EAC/C,IAAID,eAAe,CAACE,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,EAAE;EACb;EACA,MAAMC,cAAc,GAAGH,eAAe,CACjCI,KAAK,CAAC,KAAK,CAAC,CACZC,GAAG,CAAEC,OAAO,IAAKA,OAAO,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CACpCC,GAAG,CAAEE,WAAW,IAAK;IACtB,MAAMC,KAAK,GAAGT,OAAO,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC,IAAIA,WAAW;IACpD,OAAO,IAAIlB,OAAO,CAACmB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGN,cAAc,CAACO,MAAM,CAAEH,WAAW,IAAKA,WAAW,CAACd,QAAQ,CAAC,CAAC,CAAC;EACrF,IAAIgB,cAAc,CAACP,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,IAAIS,KAAK,CAAC,0DAA0D,CAAC;EAC/E;EACA,IAAIF,cAAc,CAACP,MAAM,KAAK,CAAC,IAAIC,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;IAC1D,MAAM,IAAIS,KAAK,CAAC,0EAA0E,CAAC;EAC/F;EACA,OAAOR,cAAc;AACzB;AACA,SAASS,gBAAgBA,CAACC,QAAQ;AAClC;AACAC,MAAM,EAAEhB,QAAQ,EAAEiB,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACxC,MAAMd,cAAc,GAAGN,aAAa,CAACC,QAAQ,CAAC;EAC9C,MAAMoB,SAAS,GAAG,EAAE;EACpB,IAAIf,cAAc,CAACD,MAAM,KAAK,CAAC,IAAIC,cAAc,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE;IAC7D,OAAO0B,QAAQ,CAACC,SAAS;EAC7B;EACAjB,cAAc,CAACkB,OAAO,CAAEf,OAAO,IAAK;IAChC,IAAIA,OAAO,CAACf,IAAI,KAAKe,OAAO,CAACd,KAAK,EAAE;MAChC0B,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAE0B,QAAQ,CAAC,CAAC;MAC/D;IACJ;IACAC,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAEwB,MAAM,CAAC,CAAC;IAC7D,IAAIT,OAAO,CAACd,KAAK,EAAE;MACf0B,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACd,KAAK,EAAEwB,MAAM,CAAC,CAAC;IAClE;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTE,SAAS,CAACG,OAAO,CAAEG,aAAa,IAAKA,aAAa,CAAC,CAAC,CAAC;EACzD,CAAC;AACL;AACA,SAASC,kBAAkBA,CAACZ,QAAQ,EAAEa,OAAO,EAAE;EAC3C,MAAMvB,cAAc,GAAGN,aAAa,CAAC6B,OAAO,CAAC5B,QAAQ,CAAC;EACtD,MAAMgB,MAAM,GAAGY,OAAO,CAACZ,MAAM;EAC7B;EACA,IAAIX,cAAc,CAACD,MAAM,KAAK,CAAC,IAAIC,cAAc,CAAC,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE;IAC7D,OAAO0B,QAAQ,CAACC,SAAS;EAC7B;EACA;EACA,MAAMF,SAAS,GAAG,EAAE;EACpB;EACA,MAAMS,aAAa,GAAG,EAAE;EACxB,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB;IACAD,aAAa,CAACN,OAAO,CAAEQ,EAAE,IAAKX,SAAS,CAACI,IAAI,CAACO,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD;IACAF,aAAa,CAACzB,MAAM,GAAG,CAAC;EAC5B,CAAC;EACD;EACAC,cAAc,CAACkB,OAAO,CAAEf,OAAO,IAAK;IAChC,MAAMwB,SAAS,GAAGxB,OAAO,CAACf,IAAI,KAAKe,OAAO,CAACd,KAAK;IAChD,MAAMuB,MAAM,GAAGe,SAAS,GAAGJ,OAAO,CAACK,MAAM,GAAGL,OAAO,CAACM,IAAI;IACxD,IAAI,CAACF,SAAS,IAAIxB,OAAO,CAACd,KAAK,IAAIkC,OAAO,CAACO,IAAI,EAAE;MAC7C,MAAMC,YAAY,GAAG5B,OAAO,CAACd,KAAK;MAClC,MAAM2C,WAAW,GAAGT,OAAO,CAACO,IAAI;MAChC,MAAMG,KAAK,GAAGA,CAAA,KAAMvB,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAEoB,YAAY,EAAEC,WAAW,CAAC;MACtER,aAAa,CAACL,IAAI,CAACc,KAAK,CAAC;IAC7B;IACA,IAAIrB,MAAM,EAAE;MACRG,SAAS,CAACI,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACT,MAAM,EAAER,OAAO,CAACf,IAAI,EAAE,MAAMwB,MAAM,CAACa,YAAY,CAAC,CAAC,CAAC;IACrF;EACJ,CAAC,CAAC;EACF,OAAO,MAAM;IACTV,SAAS,CAACG,OAAO,CAAEG,aAAa,IAAKA,aAAa,CAAC,CAAC,CAAC;EACzD,CAAC;AACL;AACA,SAASa,oBAAoBA,CAACxB,QAAQ,EAAEa,OAAO,EAAE;EAC7C,IAAI,CAACA,OAAO,CAACY,YAAY,EAAE;IACvB,OAAOnB,QAAQ,CAACC,SAAS;EAC7B;EACA;EACA,OAAOP,QAAQ,CAACU,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGgB,KAAK,IAAK;IACnD,IAAIb,OAAO,CAACZ,MAAM,IAAIY,OAAO,CAACZ,MAAM,CAAC0B,QAAQ,CAACD,KAAK,CAACzB,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIY,OAAO,CAACe,OAAO,IACff,OAAO,CAACe,OAAO,CAACC,IAAI,CAAC5B,MAAM,IAAIA,MAAM,CAAC0B,QAAQ,CAACD,KAAK,CAACzB,MAAM,CAAC,CAAC,EAAE;MAC/D;IACJ;IACA,IAAIY,OAAO,CAACO,IAAI,EAAE;MACdP,OAAO,CAACO,IAAI,CAAC,CAAC;IAClB;EACJ,CAAC,CAAC;AACN;AACA,SAASU,gBAAgBA,CAAC9B,QAAQ,EAAEa,OAAO,EAAE;EACzC,IAAI,CAACA,OAAO,CAACkB,UAAU,EAAE;IACrB,OAAOzB,QAAQ,CAACC,SAAS;EAC7B;EACA,OAAOP,QAAQ,CAACU,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGgB,KAAK,IAAK;IACvD,IAAIb,OAAO,CAACZ,MAAM,IAAIY,OAAO,CAACZ,MAAM,CAAC0B,QAAQ,CAACD,KAAK,CAACzB,MAAM,CAAC,EAAE;MACzD;IACJ;IACA,IAAIY,OAAO,CAACe,OAAO,IACff,OAAO,CAACe,OAAO,CAACC,IAAI,CAAC5B,MAAM,IAAIA,MAAM,CAAC0B,QAAQ,CAACD,KAAK,CAACzB,MAAM,CAAC,CAAC,EAAE;MAC/D;IACJ;IACA,IAAIY,OAAO,CAACO,IAAI,EAAE;MACdP,OAAO,CAACO,IAAI,CAAC,CAAC;IAClB;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,GAAG,GAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAK,CAAC,CAAC;AAC3D,MAAMC,QAAQ,GAAGF,GAAG,CAACE,QAAQ;AAC7B,MAAMC,QAAQ,GAAGH,GAAG,CAACG,QAAQ;AAC7B;AACA,MAAMC,EAAE,GAAGJ,GAAG,CAACI,EAAE,GAAG,MAAMJ,GAAG,CAACI,EAAE,CAAC,CAAC,GAAG,MAAM,IAAI;AAC/C,MAAMC,WAAW,GAAGL,GAAG,CAACK,WAAW,GAAGL,GAAG,CAACK,WAAW,GAAG,IAAI;AAC5D,MAAMC,KAAK,GAAGN,GAAG,CAACM,KAAK;AACvB,MAAMC,UAAU,GAAGP,GAAG,CAACO,UAAU;AACjC,MAAMC,aAAa,GAAGR,GAAG,CAACQ,aAAa;AACvC,MAAMC,WAAW,GAAGT,GAAG,CAACS,WAAW;AACnC,MAAMC,OAAO,GAAGV,GAAG,CAACU,OAAO;AAC3B,MAAMC,QAAQ,GAAGX,GAAG,CAACW,QAAQ;AAC7B,MAAMC,aAAa,GAAGZ,GAAG,CAACY,aAAa;AAEvC,IAAIC,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK;EAC1BA,SAAS,CAAC,OAAO,CAAC,GAAG,KAAK;AAC9B,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIC,cAAc;AAClB,SAASC,eAAeA,CAAA,EAAG;EACvB,MAAMC,MAAM,GAAGhB,GAAG,CAACE,QAAQ,CAACe,aAAa,CAAC,MAAM,CAAC;EACjDD,MAAM,CAACE,SAAS,GAAG,oBAAoB;EACvCF,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;EAC9BJ,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;EAC5BpB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;EACrC,MAAMO,YAAY,GAAGvB,GAAG,CAACwB,gBAAgB,CAACR,MAAM,CAAC,CAACS,WAAW;EAC7D,IAAIF,YAAY,IAAIG,UAAU,CAACH,YAAY,CAAC,EAAE;IAC1CvB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACM,WAAW,CAACX,MAAM,CAAC;IACrC,OAAO,KAAK;EAChB;EACAhB,GAAG,CAACE,QAAQ,CAACmB,IAAI,CAACM,WAAW,CAACX,MAAM,CAAC;EACrC,OAAO,KAAK;AAChB;AACA,SAASY,QAAQA,CAACC,KAAK,EAAE;EACrBf,cAAc,GAAGe,KAAK;AAC1B;AACA,SAASC,KAAKA,CAAA,EAAG;EACb,IAAIhB,cAAc,EACd,OAAOA,cAAc,KAAK,KAAK;EACnCA,cAAc,GAAGC,eAAe,CAAC,CAAC;EAClC,OAAOD,cAAc,KAAK,KAAK;AACnC;AACA,SAASiB,KAAKA,CAAA,EAAG;EACb,IAAIjB,cAAc,EACd,OAAOA,cAAc,KAAK,KAAK;EACnCA,cAAc,GAAGC,eAAe,CAAC,CAAC;EAClC,OAAOD,cAAc,KAAK,KAAK;AACnC;AACA,SAASkB,QAAQA,CAAA,EAAG;EAChB,OAAO;IACHF,KAAK,EAAEA,KAAK,CAAC,CAAC;IACdC,KAAK,EAAEA,KAAK,CAAC;EACjB,CAAC;AACL;AACA,SAASE,gBAAgBA,CAAA,EAAG;EACxB,MAAMC,KAAK,GAAGF,QAAQ,CAAC,CAAC;EACxB,MAAMG,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIL,KAAK,CAACK,GAAG,CAAC,CAAC;EAC7D,OAAO1B,SAAS,CAACsB,UAAU,CAAC;AAChC;AAEA,MAAMK,UAAU,CAAC;EACb/F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,MAAM,GAAG,CAAC;IACf,IAAI,CAACoF,OAAO,GAAG,EAAE;IACjB;EACJ;;EACAC,GAAGA,CAACC,QAAQ,EAAE;IACV,IAAI,IAAI,CAACtF,MAAM,KAAK,CAAC,IAAIsF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACtF,MAAM,EAAE;MAC9D,OAAO,KAAK,CAAC;IACjB;IACA,IAAIuF,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,KAAK,IAAIwB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,QAAQ,EAAEE,KAAK,EAAE,EAAE;MAC3CD,OAAO,GAAGA,OAAO,EAAEE,IAAI;IAC3B;IACA,OAAOF,OAAO,EAAEG,KAAK;EACzB;EACA3B,GAAGA,CAAC2B,KAAK,EAAEJ,QAAQ,GAAG,IAAI,CAACtF,MAAM,EAAE;IAC/B,IAAIsF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,IAAI,CAACtF,MAAM,EAAE;MACxC,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,MAAMkF,IAAI,GAAG;MACTD,KAAK;MACLD,IAAI,EAAEG,SAAS;MACfC,QAAQ,EAAED;IACd,CAAC;IACD,IAAI,IAAI,CAAC5F,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI,CAACgE,IAAI,GAAG2B,IAAI;MAChB,IAAI,CAACG,IAAI,GAAGH,IAAI;MAChB,IAAI,CAACJ,OAAO,GAAGI,IAAI;IACvB,CAAC,MACI;MACD,IAAIL,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACtB,IAAI,EAAE;QAC7B;QACA2B,IAAI,CAACF,IAAI,GAAG,IAAI,CAACzB,IAAI;QACrB,IAAI,CAACA,IAAI,CAAC6B,QAAQ,GAAGF,IAAI;QACzB,IAAI,CAAC3B,IAAI,GAAG2B,IAAI;MACpB,CAAC,MACI,IAAIL,QAAQ,KAAK,IAAI,CAACtF,MAAM,IAAI,IAAI,CAAC8F,IAAI,EAAE;QAC5C;QACA,IAAI,CAACA,IAAI,CAACL,IAAI,GAAGE,IAAI;QACrBA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACC,IAAI;QACzB,IAAI,CAACA,IAAI,GAAGH,IAAI;MACpB,CAAC,MACI;QACD;QACA,MAAMI,mBAAmB,GAAG,IAAI,CAACC,OAAO,CAACV,QAAQ,GAAG,CAAC,CAAC;QACtD,MAAMW,eAAe,GAAGF,mBAAmB,EAAEN,IAAI;QACjD,IAAIM,mBAAmB,IAAIE,eAAe,EAAE;UACxCF,mBAAmB,CAACN,IAAI,GAAGE,IAAI;UAC/BM,eAAe,CAACJ,QAAQ,GAAGF,IAAI;UAC/BA,IAAI,CAACE,QAAQ,GAAGE,mBAAmB;UACnCJ,IAAI,CAACF,IAAI,GAAGQ,eAAe;QAC/B;MACJ;IACJ;IACA,IAAI,CAACjG,MAAM,EAAE;IACb,IAAI,CAACkG,iCAAiC,CAAC,CAAC;EAC5C;EACAC,MAAMA,CAACb,QAAQ,GAAG,CAAC,EAAE;IACjB,IAAI,IAAI,CAACtF,MAAM,KAAK,CAAC,IAAIsF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACtF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,IAAI6E,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACtB,IAAI,EAAE;MAC7B;MACA,IAAI,CAACA,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyB,IAAI;MAC1B,IAAI,IAAI,CAACzB,IAAI,EAAE;QACX;QACA,IAAI,CAACA,IAAI,CAAC6B,QAAQ,GAAGD,SAAS;MAClC,CAAC,MACI;QACD;QACA,IAAI,CAACE,IAAI,GAAGF,SAAS;MACzB;IACJ,CAAC,MACI,IAAIN,QAAQ,KAAK,IAAI,CAACtF,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC8F,IAAI,EAAED,QAAQ,EAAE;MAC1D;MACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACD,QAAQ;MAC9B,IAAI,CAACC,IAAI,CAACL,IAAI,GAAGG,SAAS;IAC9B,CAAC,MACI;MACD;MACA,MAAMQ,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACV,QAAQ,CAAC;MAC1C,IAAIc,WAAW,EAAEX,IAAI,IAAIW,WAAW,CAACP,QAAQ,EAAE;QAC3CO,WAAW,CAACX,IAAI,CAACI,QAAQ,GAAGO,WAAW,CAACP,QAAQ;QAChDO,WAAW,CAACP,QAAQ,CAACJ,IAAI,GAAGW,WAAW,CAACX,IAAI;MAChD;IACJ;IACA,IAAI,CAACzF,MAAM,EAAE;IACb,IAAI,CAACkG,iCAAiC,CAAC,CAAC;EAC5C;EACAG,GAAGA,CAACf,QAAQ,EAAEI,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC1F,MAAM,KAAK,CAAC,IAAIsF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACtF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,MAAMkF,IAAI,GAAG,IAAI,CAACK,OAAO,CAACV,QAAQ,CAAC;IACnC,IAAIK,IAAI,EAAE;MACNA,IAAI,CAACD,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACQ,iCAAiC,CAAC,CAAC;IAC5C;EACJ;EACAI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClB,OAAO;EACvB;EACAmB,OAAOA,CAAC5E,EAAE,EAAE;IACR,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,MAAMwC,MAAM,GAAG,EAAE;IACjB,IAAI,CAACjB,OAAO,EAAE;MACV,OAAOiB,MAAM;IACjB;IACA,KAAK,IAAIhB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxF,MAAM,EAAEwF,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAOiB,MAAM;MACjB;MACA,IAAI7E,EAAE,CAAC4D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1BgB,MAAM,CAACpF,IAAI,CAAC;UAAEoE,KAAK;UAAEE,KAAK,EAAEH,OAAO,CAACG;QAAM,CAAC,CAAC;MAChD;MACAH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACA;EACApF,IAAIA,CAAC,GAAGqF,IAAI,EAAE;IACVA,IAAI,CAACtF,OAAO,CAAEuF,GAAG,IAAK;MAClB,IAAI,CAAC3C,GAAG,CAAC2C,GAAG,CAAC;IACjB,CAAC,CAAC;IACF,OAAO,IAAI,CAAC1G,MAAM;EACtB;EACA2G,GAAGA,CAAA,EAAG;IACF,IAAI,IAAI,CAAC3G,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO4F,SAAS;IACpB;IACA,MAAMgB,IAAI,GAAG,IAAI,CAACd,IAAI;IACtB,IAAI,CAACK,MAAM,CAAC,IAAI,CAACnG,MAAM,GAAG,CAAC,CAAC;IAC5B,OAAO4G,IAAI,EAAElB,KAAK;EACtB;EACAmB,OAAOA,CAAC,GAAGJ,IAAI,EAAE;IACbA,IAAI,CAACK,OAAO,CAAC,CAAC;IACdL,IAAI,CAACtF,OAAO,CAAEuF,GAAG,IAAK;MAClB,IAAI,CAAC3C,GAAG,CAAC2C,GAAG,EAAE,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,OAAO,IAAI,CAAC1G,MAAM;EACtB;EACA+G,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC/G,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO4F,SAAS;IACpB;IACA,MAAMoB,QAAQ,GAAG,IAAI,CAAChD,IAAI,EAAE0B,KAAK;IACjC,IAAI,CAACS,MAAM,CAAC,CAAC;IACb,OAAOa,QAAQ;EACnB;EACA7F,OAAOA,CAACQ,EAAE,EAAE;IACR,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,KAAK,IAAIwB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxF,MAAM,EAAEwF,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA5D,EAAE,CAAC4D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC;MACxBD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;EACJ;EACAwB,OAAOA,CAACvB,KAAK,EAAE;IACX,IAAIH,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,IAAIsB,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxF,MAAM,EAAEwF,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAOD,QAAQ;MACnB;MACA,IAAIC,OAAO,CAACG,KAAK,KAAKA,KAAK,EAAE;QACzBJ,QAAQ,GAAGE,KAAK;QAChB;MACJ;MACAD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOH,QAAQ;EACnB;EACA9C,IAAIA,CAACb,EAAE,EAAE;IACL,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,IAAIwC,MAAM,GAAG,KAAK;IAClB,OAAOjB,OAAO,IAAI,CAACiB,MAAM,EAAE;MACvB,IAAI7E,EAAE,CAAC4D,OAAO,CAACG,KAAK,CAAC,EAAE;QACnBc,MAAM,GAAG,IAAI;QACb;MACJ;MACAjB,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACAU,KAAKA,CAACvF,EAAE,EAAE;IACN,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,IAAIwC,MAAM,GAAG,IAAI;IACjB,OAAOjB,OAAO,IAAIiB,MAAM,EAAE;MACtB,IAAI,CAAC7E,EAAE,CAAC4D,OAAO,CAACG,KAAK,CAAC,EAAE;QACpBc,MAAM,GAAG,KAAK;MAClB;MACAjB,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAOe,MAAM;EACjB;EACAW,QAAQA,CAAA,EAAG;IACP,OAAO,eAAe;EAC1B;EACAlC,IAAIA,CAACtD,EAAE,EAAE;IACL,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,KAAK,IAAIwB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxF,MAAM,EAAEwF,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA,IAAI5D,EAAE,CAAC4D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1B,OAAOD,OAAO,CAACG,KAAK;MACxB;MACAH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;EACJ;EACA2B,SAASA,CAACzF,EAAE,EAAE;IACV,IAAI4D,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,KAAK,IAAIwB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACxF,MAAM,EAAEwF,KAAK,EAAE,EAAE;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV,OAAO,CAAC,CAAC;MACb;MACA,IAAI5D,EAAE,CAAC4D,OAAO,CAACG,KAAK,EAAEF,KAAK,CAAC,EAAE;QAC1B,OAAOA,KAAK;MAChB;MACAD,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,OAAO,CAAC,CAAC;EACb;EACAO,OAAOA,CAACV,QAAQ,EAAE;IACd,IAAI,IAAI,CAACtF,MAAM,KAAK,CAAC,IAAIsF,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,IAAI,CAACtF,MAAM,EAAE;MAC9D,MAAM,IAAIS,KAAK,CAAC,6BAA6B,CAAC;IAClD;IACA,IAAI8E,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,KAAK,IAAIwB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,QAAQ,EAAEE,KAAK,EAAE,EAAE;MAC3CD,OAAO,GAAGA,OAAO,EAAEE,IAAI;IAC3B;IACA,OAAOF,OAAO;EAClB;EACAW,iCAAiCA,CAAA,EAAG;IAChC,MAAMmB,QAAQ,GAAG,EAAE;IACnB,IAAI9B,OAAO,GAAG,IAAI,CAACvB,IAAI;IACvB,OAAOuB,OAAO,EAAE;MACZ8B,QAAQ,CAACjG,IAAI,CAACmE,OAAO,CAACG,KAAK,CAAC;MAC5BH,OAAO,GAAGA,OAAO,CAACE,IAAI;IAC1B;IACA,IAAI,CAACL,OAAO,GAAGiC,QAAQ;EAC3B;AACJ;;AAEA;AACA,SAASC,QAAQA,CAAA,EAAG;EAChB,MAAMC,KAAK,GAAG,QAAQ;EACtB;EACA,OAAO,SAASC,eAAeA,CAAC5G,MAAM,EAAE6G,WAAW,EAAE;IACjD,MAAMC,IAAI,GAAI,MAAKD,WAAY,OAAM;IACrC1C,MAAM,CAAC4C,cAAc,CAAC/G,MAAM,EAAE6G,WAAW,EAAE;MACvC;MACApC,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACqC,IAAI,CAAC;MACrB,CAAC;MACD;MACArB,GAAGA,CAACX,KAAK,EAAE;QACP,MAAMkC,SAAS,GAAG,IAAI,CAACF,IAAI,CAAC;QAC5B,IAAI,CAACA,IAAI,CAAC,GAAGhC,KAAK;QAClB,IAAIkC,SAAS,KAAKlC,KAAK,IAAI,IAAI,CAAC+B,WAAW,GAAGF,KAAK,CAAC,EAAE;UAClD,IAAI,CAACE,WAAW,GAAGF,KAAK,CAAC,CAACM,IAAI,CAACnC,KAAK,CAAC;QACzC;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,MAAMoC,KAAK,CAAC;EACR;EACA,OAAOC,MAAMA,CAACC,OAAO,EAAE;IACnB;IACA,CAAEC,EAAE,IAAKA,EAAE,EAAED,OAAO,CAACE,YAAY,CAAC;EACtC;EACA;EACA;EACA,OAAOC,SAASA,CAACC,IAAI,EAAE;IACnB;IACA;IACA;IACA,IAAIC,IAAI,GAAGD,IAAI,CAACE,aAAa,CAACC,WAAW;IACzC,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACG,MAAM,EAAE;MACvBH,IAAI,GAAG1F,GAAG;IACd;IACA,OAAO0F,IAAI,CAAClE,gBAAgB,CAACiE,IAAI,CAAC;EACtC;EACA,OAAOK,mBAAmBA,CAAA,EAAG;IACzB,MAAM5D,KAAK,GAAGD,gBAAgB,CAAC,CAAC;IAChC,OAAO;MACH8D,WAAW,EAAE,WAAW;MACxBC,SAAS,EAAE9D,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAG,yEAAyE;MAClL+D,OAAO,EAAE/D,KAAK,KAAK,KAAK,GAAG,yEAAyE,GAAG;IAC3G,CAAC;EACL;AACJ;AAEA,MAAMgE,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,QAAQ,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,EAAE,MAAM,IAAIA,OAAO,CAAC;AACvE,SAASC,QAAQA,CAACC,GAAG,EAAE;EACnB,IAAI,CAAC/J,SAAS,CAAC,CAAC,IAAI4J,QAAQ,IAAIG,GAAG,IAAIJ,aAAa,EAAE;IAClD;EACJ;EACAA,aAAa,CAACI,GAAG,CAAC,GAAG,IAAI;EACzBF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;AACrB;;AAEA;AACA;AACA;;AAEA,SAASzF,SAAS,EAAE2B,UAAU,EAAEmC,QAAQ,EAAEnI,OAAO,EAAE2I,KAAK,EAAElD,gBAAgB,EAAE/B,QAAQ,EAAE8B,QAAQ,EAAEjE,gBAAgB,EAAEa,kBAAkB,EAAE5B,aAAa,EAAE8C,gBAAgB,EAAEN,oBAAoB,EAAEoC,QAAQ,EAAEyE,QAAQ,EAAErG,GAAG,IAAIC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}