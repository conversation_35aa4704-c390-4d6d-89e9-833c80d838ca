{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { GaActionEnum } from \"ngx-google-analytics\";\nimport { isPlatformBrowser } from \"@angular/common\";\nimport { GaLocalActionEnum } from '@core/enums/ga-local-action-enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-google-analytics\";\nimport * as i6 from \"@core/services/gtm.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"ngx-intl-tel-input-gg\";\nimport * as i11 from \"../../../../shared/modals/confirmation-delete-dialog/confirmation-delete-dialog.component\";\nfunction AddressListComponent_ng_container_0_ng_container_10_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"multipleAddress.default\"), \" \");\n  }\n}\nfunction AddressListComponent_ng_container_0_ng_container_10_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"multipleAddress.other\"), \" \");\n  }\n}\nfunction AddressListComponent_ng_container_0_ng_container_10_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 31);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_container_0_ng_container_10_img_12_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.showConfirmationModal(item_r4.id));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddressListComponent_ng_container_0_ng_container_10_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r4.addressLabel, \" \");\n  }\n}\nfunction AddressListComponent_ng_container_0_ng_container_10_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(2, 2, \"multipleAddress.address\"), \" \", i_r5 + 1, \"\");\n  }\n}\nfunction AddressListComponent_ng_container_0_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"a\", 15)(4, \"div\", 16)(5, \"div\", 17)(6, \"div\", 18)(7, \"div\", 19);\n    i0.ɵɵtemplate(8, AddressListComponent_ng_container_0_ng_container_10_button_8_Template, 3, 3, \"button\", 20);\n    i0.ɵɵtemplate(9, AddressListComponent_ng_container_0_ng_container_10_button_9_Template, 3, 3, \"button\", 21);\n    i0.ɵɵelementStart(10, \"div\", 22)(11, \"img\", 23);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_container_0_ng_container_10_Template_img_click_11_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.triggerAddressAnalytics(\"CLICK_ON_EDIT_ADDRESS\", \"EDIT_ADDRESS\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AddressListComponent_ng_container_0_ng_container_10_img_12_Template, 1, 0, \"img\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 25);\n    i0.ɵɵtemplate(14, AddressListComponent_ng_container_0_ng_container_10_span_14_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(15, AddressListComponent_ng_container_0_ng_container_10_span_15_Template, 3, 4, \"span\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 28);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", item_r4.isDefault);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r4.isDefault && item_r4.addressLabel !== \"Home\" && item_r4.addressLabel !== \"Work\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address/\" + item_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r4.isDefault);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r4 == null ? null : item_r4.addressLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(item_r4 == null ? null : item_r4.addressLabel));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", item_r4.streetAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r4.streetAddress);\n  }\n}\nfunction AddressListComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"section\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n    i0.ɵɵelement(5, \"img\", 6);\n    i0.ɵɵelementStart(6, \"span\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 8);\n    i0.ɵɵtemplate(10, AddressListComponent_ng_container_0_ng_container_10_Template, 18, 8, \"ng-container\", 9);\n    i0.ɵɵelementStart(11, \"div\", 10)(12, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_container_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.addAddressMobile());\n    });\n    i0.ɵɵpipe(13, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-confirmation-delete-dialog\", 12);\n    i0.ɵɵlistener(\"update\", function AddressListComponent_ng_container_0_Template_app_confirmation_delete_dialog_update_14_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onDelete($event));\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", \"/account\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 6, \"multipleAddress.myAddresses\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(13, 8, \"addingAddress.addNewAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"addresses\", 1)(\"showDialog\", ctx_r0.displayModal);\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"multipleAddress.default\"), \" \");\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"multipleAddress.other\"), \" \");\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_img_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 31);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_template_1_div_11_ng_container_8_img_13_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const item_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r31.showConfirmationModal(item_r24.id));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r24.addressLabel, \" \");\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r25 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(2, 2, \"multipleAddress.address\"), \" \", i_r25 + 1, \"\");\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\")(2, \"div\", 14)(3, \"input\", 47);\n    i0.ɵɵlistener(\"change\", function AddressListComponent_ng_template_1_div_11_ng_container_8_Template_input_change_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const item_r24 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.changeDefaultAddress(item_r24));\n    })(\"ngModelChange\", function AddressListComponent_ng_template_1_div_11_ng_container_8_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.selectedAddress = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 48)(5, \"div\", 16)(6, \"div\", 17)(7, \"div\", 18)(8, \"div\", 19);\n    i0.ɵɵtemplate(9, AddressListComponent_ng_template_1_div_11_ng_container_8_button_9_Template, 3, 3, \"button\", 20);\n    i0.ɵɵtemplate(10, AddressListComponent_ng_template_1_div_11_ng_container_8_button_10_Template, 3, 3, \"button\", 21);\n    i0.ɵɵelementStart(11, \"div\", 22)(12, \"img\", 23);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_template_1_div_11_ng_container_8_Template_img_click_12_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r39 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r39.triggerAddressAnalytics(\"CLICK_ON_EDIT_ADDRESS\", \"EDIT_ADDRESS\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AddressListComponent_ng_template_1_div_11_ng_container_8_img_13_Template, 1, 0, \"img\", 49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 25);\n    i0.ɵɵtemplate(15, AddressListComponent_ng_template_1_div_11_ng_container_8_span_15_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(16, AddressListComponent_ng_template_1_div_11_ng_container_8_span_16_Template, 3, 4, \"span\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 28);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.selectedAddress)(\"disabled\", ctx_r23.address.length === 1)(\"value\", item_r24);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", item_r24.isDefault);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r24.isDefault && item_r24.addressLabel !== \"Home\" && item_r24.addressLabel !== \"Work\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address/\" + item_r24.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r24.isDefault);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r24 == null ? null : item_r24.addressLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(item_r24 == null ? null : item_r24.addressLabel));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", item_r24.streetAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r24.streetAddress);\n  }\n}\nfunction AddressListComponent_ng_template_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 40)(2, \"div\", 41)(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 44);\n    i0.ɵɵtemplate(8, AddressListComponent_ng_template_1_div_11_ng_container_8_Template, 19, 11, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 45)(10, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_template_1_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.addAddress());\n    });\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-confirmation-delete-dialog\", 12);\n    i0.ɵɵlistener(\"update\", function AddressListComponent_ng_template_1_div_11_Template_app_confirmation_delete_dialog_update_12_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.onDelete($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 5, \"multipleAddress.myAddresses\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(11, 7, \"addingAddress.addAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"addresses\", 1)(\"showDialog\", ctx_r21.displayModal);\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_ng_container_8_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r44 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r44 == null ? null : item_r44.addressLabel);\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_ng_container_8_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r45 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(2, 2, \"multipleAddress.address\"), \" \", i_r45 + 1, \"\");\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_ng_container_8_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_ng_container_8_em_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"em\", 62);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_template_1_div_12_ng_container_8_em_16_Template_em_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const item_r44 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r52.showConfirmationModal(item_r44.id));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 51)(2, \"div\")(3, \"a\", 52)(4, \"div\", 53);\n    i0.ɵɵelement(5, \"img\", 54);\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"div\", 25);\n    i0.ɵɵtemplate(8, AddressListComponent_ng_template_1_div_12_ng_container_8_span_8_Template, 2, 1, \"span\", 26);\n    i0.ɵɵtemplate(9, AddressListComponent_ng_template_1_div_12_ng_container_8_span_9_Template, 3, 4, \"span\", 27);\n    i0.ɵɵtemplate(10, AddressListComponent_ng_template_1_div_12_ng_container_8_button_10_Template, 2, 0, \"button\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 28);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"a\", 57);\n    i0.ɵɵelement(14, \"em\", 58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 59);\n    i0.ɵɵtemplate(16, AddressListComponent_ng_template_1_div_12_ng_container_8_em_16_Template, 1, 0, \"em\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r44 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address/\" + item_r44.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", item_r44 == null ? null : item_r44.addressLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(item_r44 == null ? null : item_r44.addressLabel));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r44.isDefault);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"title\", item_r44.streetAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r44.streetAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address/\" + item_r44.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !item_r44.isDefault);\n  }\n}\nfunction AddressListComponent_ng_template_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 40)(2, \"div\", 41)(3, \"div\", 42)(4, \"div\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 50);\n    i0.ɵɵtemplate(8, AddressListComponent_ng_template_1_div_12_ng_container_8_Template, 17, 8, \"ng-container\", 9);\n    i0.ɵɵelementStart(9, \"div\", 45)(10, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function AddressListComponent_ng_template_1_div_12_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.addAddress());\n    });\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-confirmation-delete-dialog\", 12);\n    i0.ɵɵlistener(\"update\", function AddressListComponent_ng_template_1_div_12_Template_app_confirmation_delete_dialog_update_12_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.onDelete($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 5, \"multipleAddress.myAddresses\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", i0.ɵɵpipeBind1(11, 7, \"addingAddress.addAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"addresses\", 1)(\"showDialog\", ctx_r22.displayModal);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"hidden-navbar\": a0\n  };\n};\nfunction AddressListComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"em\", 36)(3, \"em\", 37);\n    i0.ɵɵelementStart(4, \"span\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"em\", 37);\n    i0.ɵɵelementStart(8, \"span\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, AddressListComponent_ng_template_1_div_11_Template, 13, 9, \"div\", 39);\n    i0.ɵɵtemplate(12, AddressListComponent_ng_template_1_div_12_Template, 13, 9, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, !(ctx_r2.navbarData == null ? null : ctx_r2.navbarData.isActive)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", \"/account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 8, \"sideMenu.yourAccount\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", \"/account/address\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 10, \"multipleAddress.myAddresses\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLayoutTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLayoutTemplate);\n  }\n}\nexport class AddressListComponent {\n  onResize(event) {\n    if (isPlatformBrowser(this.platformId)) {\n      this.screenWidth = window.innerWidth;\n    }\n  }\n  constructor(addressService, translate, platformId, messageService, router, route, appDataService, permissionService, $gaService, $gtmService, store) {\n    this.addressService = addressService;\n    this.translate = translate;\n    this.platformId = platformId;\n    this.messageService = messageService;\n    this.router = router;\n    this.route = route;\n    this.appDataService = appDataService;\n    this.permissionService = permissionService;\n    this.$gaService = $gaService;\n    this.$gtmService = $gtmService;\n    this.store = store;\n    this.address = [];\n    this.displayModal = false;\n    this.selectedId = '';\n    this.isMobileTemplate = false;\n    this.isLayoutTemplate = false;\n    this.tagName = GaActionEnum;\n    this.tagNameLocal = GaLocalActionEnum;\n    this.isGoogleAnalytics = false;\n    this.screenWidth = window.innerWidth;\n    this.isLayoutTemplate = this.permissionService.hasPermission('Layout-Template');\n    this.isMobileTemplate = this.permissionService.hasPermission('Mobile-Layout');\n    this.isGoogleAnalytics = this.permissionService.hasPermission('Google Analytics');\n  }\n  ngOnInit() {\n    this.navbarData = this.appDataService.layoutTemplate.find(section => section.type === 'navbar');\n    this.$gtmService.pushPageView('account', 'addresses');\n    this.route.queryParams.subscribe(params => {\n      this.redirectUrl = params.returnUrl;\n    });\n    this.getCustomerAddress();\n  }\n  triggerAddressAnalytics(tagNameKey, label) {\n    if (this.isGoogleAnalytics) {\n      this.$gaService.event(this.tagNameLocal[tagNameKey], '', label, 1, true);\n    }\n  }\n  getCustomerAddress() {\n    this.addressService.getAddress().subscribe({\n      next: res => {\n        this.userDetails = this.store.get('profile');\n        this.triggerAnalytics();\n        this.address = res.data.records;\n        this.selectedAddress = this.address[0];\n      }\n    });\n  }\n  onDelete(event) {\n    this.displayModal = false;\n    if (event == 'delete') {\n      this.triggerAddressAnalytics('CLICK_ON_DELETE_ADDRESS', 'DELETE_ADDRESS');\n      this.addressService.deleteAddress(this.selectedId).subscribe({\n        next: res => {\n          this.messageService.add({\n            severity: 'success',\n            summary: '',\n            detail: 'Address deleted successfully'\n          });\n          this.getCustomerAddress();\n        }\n      });\n    }\n  }\n  showConfirmationModal(id) {\n    this.selectedId = id;\n    this.displayModal = true;\n  }\n  addAddressMobile() {\n    // if (this.redirectUrl && this.redirectUrl !== '') {\n    //   this.router.navigate(['/account/address/verify-address'], {queryParams: {returnUrl: this.redirectUrl}})\n    //   this.redirectUrl = '';\n    // } else {\n    this.triggerAddressAnalytics('CLICK_ON_ADD_ADDRESS', 'ADD_ADDRESS');\n    this.router.navigate(['/account/verify-address']);\n    // }\n  }\n\n  addAddress() {\n    this.triggerAddressAnalytics('CLICK_ON_ADD_ADDRESS', 'ADD_ADDRESS');\n    if (this.redirectUrl && this.redirectUrl !== '') {\n      this.router.navigate(['/account/address/add-address'], {\n        queryParams: {\n          returnUrl: this.redirectUrl\n        }\n      });\n      this.redirectUrl = '';\n    } else {\n      this.router.navigate(['/account/address/add-address']);\n    }\n  }\n  addressDetails(id) {\n    this.router.navigate([`/account/address/${id}`]);\n  }\n  changeDefaultAddress(event) {\n    this.addressService.setDefault(event.id).subscribe({\n      next: res => {\n        this.getCustomerAddress();\n      },\n      error: err => {\n        console.error(err);\n      }\n    });\n  }\n  triggerAnalytics() {\n    if (this.isGoogleAnalytics && this.permissionService.getTagFeature('VIEW_ITEM_LIST')) {\n      this.$gaService.pageView('/account/address', 'All Addresses');\n      this.$gaService.event(this.tagName.SEARCH, '', 'VIEW_ITEM_LIST', 1, true, {\n        \"user_ID\": this.userDetails ? this.userDetails.mobileNumber : 'Un_Authenticated'\n      });\n    }\n  }\n}\nAddressListComponent.ɵfac = function AddressListComponent_Factory(t) {\n  return new (t || AddressListComponent)(i0.ɵɵdirectiveInject(i1.AddressService), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i1.AppDataService), i0.ɵɵdirectiveInject(i1.PermissionService), i0.ɵɵdirectiveInject(i5.GoogleAnalyticsService), i0.ɵɵdirectiveInject(i6.GTMService), i0.ɵɵdirectiveInject(i1.StoreService));\n};\nAddressListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddressListComponent,\n  selectors: [[\"app-address-list\"]],\n  hostBindings: function AddressListComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"resize\", function AddressListComponent_resize_HostBindingHandler($event) {\n        return ctx.onResize($event);\n      }, false, i0.ɵɵresolveWindow);\n    }\n  },\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"oldContainer\", \"\"], [1, \"account-page-mobile\"], [1, \"d-flex\", \"cart-mobile-new__address-layout\", \"flex-row\"], [1, \"d-inline-flex\", \"cart-mobile-new__address-layout__address-items-section\"], [1, \"header-container\"], [\"alt\", \"back-icon\", \"src\", \"assets/icons/mobile-icons/back-icon.svg\", 3, \"routerLink\"], [1, \"header-container__header-detail\"], [1, \"mt-5\", \"flex\", \"flex-column\", \"justify-content-start\", \"flex-wrap\", \"account-links\", \"mobile-address\", \"mobile-addressbar\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mt-3\", \"add-new-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"add-new-address-btn\", 3, \"label\", \"click\"], [3, \"addresses\", \"showDialog\", \"update\"], [1, \"address-bg\"], [1, \"radio-button-container\"], [1, \"justify-content-between\", \"px-3\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\", \"align-items-center\"], [1, \"align-items-center\"], [1, \"address-item-list\"], [1, \"address-container\"], [1, \"address-actions\"], [\"class\", \"default-btn\", 4, \"ngIf\"], [\"class\", \"other-btn\", 4, \"ngIf\"], [1, \"icons-container\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/edit-address.svg\", 1, \"edit-icon\", 3, \"routerLink\", \"click\"], [\"alt\", \"No Image\", \"class\", \"delete-icon\", \"src\", \"assets/icons/delete-address.svg\", 3, \"click\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [\"class\", \"address-tag\", \"style\", \"font-weight: bolder; text-transform: capitalize\", 4, \"ngIf\"], [\"class\", \"address-tag\", \"style\", \"font-weight: bolder\", 4, \"ngIf\"], [1, \"street-address\", 3, \"title\"], [1, \"default-btn\"], [1, \"other-btn\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/delete-address.svg\", 1, \"delete-icon\", 3, \"click\"], [1, \"address-tag\", 2, \"font-weight\", \"bolder\", \"text-transform\", \"capitalize\"], [1, \"address-tag\", 2, \"font-weight\", \"bolder\"], [1, \"account-page\", 3, \"ngClass\"], [1, \"breadcrumb-address\", \"d-flex\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-home\", \"cursor-pointer\", 3, \"routerLink\"], [\"aria-hidden\", \"true\", 1, \"pi\", \"pi-angle-left\"], [3, \"routerLink\"], [4, \"ngIf\"], [1, \"content-container\", \"mobile-top\", 2, \"margin-top\", \"40px\"], [1, \"grid\"], [1, \"col-12\", \"col-md-6\", \"flex\", \"md:justify-content-start\"], [1, \"font-size-28\", \"bold-font\", \"your-addres\"], [1, \"mt-3\", \"flex\", \"flex-column\", \"justify-content-start\", \"flex-wrap\", \"account-links\", \"mobile-address\", \"mobile-addressbar\"], [1, \"mt-3\", \"button-address\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"w-full\", \"confirmBtn\", 3, \"label\", \"click\"], [\"type\", \"radio\", 1, \"radio-default\", 3, \"ngModel\", \"disabled\", \"value\", \"change\", \"ngModelChange\"], [1, \"justify-content-between\", \"p-3\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\", \"align-items-center\", \"surface-100\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/delete-address.svg\", \"class\", \"delete-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"mt-5\", \"flex\", \"flex-column\", \"justify-content-start\", \"flex-wrap\", \"account-links\", \"mobile-address\"], [1, \"d-inline-flex\"], [1, \"flex\", \"justify-content-between\", \"py-3\", \"px-3\", \"surface-100\", \"mb-2\", \"no-underline\", \"text-black-alpha-90\", \"border-round\", \"align-items-center\", 3, \"routerLink\"], [1, \"align-items-center\", \"d-flex\"], [\"alt\", \"No Image\", \"src\", \"assets/icons/pin.svg\", 1, \"img\"], [1, \"address-item\"], [\"class\", \"default\", 4, \"ngIf\"], [1, \"d-flex\", \"my-auto\", 3, \"routerLink\"], [1, \"pi\", \"pi-angle-right\", 2, \"cursor\", \"pointer\"], [1, \"surface-100\", 2, \"align-items\", \"center\", \"display\", \"flex\", \"height\", \"64px\", \"width\", \"22px\"], [\"class\", \"fas fa-trash delete-color cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"default\"], [1, \"fas\", \"fa-trash\", \"delete-color\", \"cursor-pointer\", 3, \"click\"]],\n  template: function AddressListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, AddressListComponent_ng_container_0_Template, 15, 10, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, AddressListComponent_ng_template_1_Template, 13, 14, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isMobileTemplate && ctx.screenWidth <= 768)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i7.DefaultValueAccessor, i7.RadioControlValueAccessor, i7.NgControlStatus, i8.NgClass, i8.NgForOf, i8.NgIf, i9.ButtonDirective, i4.RouterLink, i7.NgModel, i10.NativeElementInjectorDirective, i11.ConfirmationDeleteDialogComponent, i2.TranslatePipe],\n  styles: [\".header-spacing[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.header-spacing-not-navbar[_ngcontent-%COMP%] {\\n  margin-top: 122px;\\n}\\n\\n.discount-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #E21836 !important;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.account-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  max-width: 366px;\\n  width: 100%;\\n}\\n\\n.account-page[_ngcontent-%COMP%] {\\n  margin-bottom: 250px !important;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .account-page[_ngcontent-%COMP%] {\\n    margin-top: 200px !important;\\n  }\\n  .mobile-address[_ngcontent-%COMP%] {\\n    margin-top: 10px !important;\\n  }\\n  .your-addres[_ngcontent-%COMP%] {\\n    font-size: 20px !important;\\n  }\\n  .street-address[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    font-weight: 500;\\n    color: #323232;\\n    font-family: var(--medium-font) !important;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n    -webkit-line-clamp: 2;\\n    -webkit-box-orient: vertical;\\n    display: -webkit-box;\\n  }\\n  .mobile-top[_ngcontent-%COMP%] {\\n    margin-top: 20px !important;\\n  }\\n  .radio-default[_ngcontent-%COMP%] {\\n    left: 8px !important;\\n  }\\n  .hidden-navbar[_ngcontent-%COMP%] {\\n    margin-top: 150px !important;\\n  }\\n}\\n.pi[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n\\n.img[_ngcontent-%COMP%] {\\n  margin-left: -5px;\\n  margin-right: 10px;\\n}\\n\\n.delete-color[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor);\\n  font-size: 16px;\\n}\\n\\n.pi-angle-right[_ngcontent-%COMP%] {\\n  color: var(--main_bt_txtcolor);\\n}\\n\\n.address-tag[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-size: 16px;\\n  font-weight: 500 !important;\\n}\\n\\n.street-address[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500 !important;\\n}\\n\\n.address-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 266px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.button-address[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  max-width: 355px;\\n}\\n.button-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: var(--header_bgcolor);\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  border: 1px solid var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n  outline: 0 none;\\n  margin-bottom: 20px;\\n  text-transform: uppercase;\\n}\\n\\n.default[_ngcontent-%COMP%] {\\n  width: 73px;\\n  height: 19px;\\n  background: #FFCB05 0% 0% no-repeat padding-box;\\n  border-radius: 50px;\\n  border: none;\\n  letter-spacing: -0.15px;\\n  color: #323232;\\n  font-size: 11px;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.breadcrumb-address[_ngcontent-%COMP%] {\\n  background-color: #efeded;\\n  padding: 1rem 2rem;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 6px;\\n  margin: auto 0;\\n}\\n.breadcrumb-address[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-family: var(--medium-font) !important;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 15px;\\n}\\n\\n.radio-default[_ngcontent-%COMP%] {\\n  align-self: center;\\n  display: inline-flex;\\n  margin-right: 10px;\\n}\\n\\n.radio-button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.account-page-mobile[_ngcontent-%COMP%] {\\n  margin-top: 75px !important;\\n}\\n\\n.cart-mobile-new__address-layout__address-items-section[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  width: 100%;\\n  border: 1px solid #E4E7E9;\\n  border-radius: 4px;\\n  flex-direction: column;\\n  background: #F6F6F6;\\n  margin-bottom: 69px;\\n}\\n\\n.add-new-address[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.add-new-address[_ngcontent-%COMP%]   .confirmBtn[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: var(--header_bgcolor);\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  border: 1px solid var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n  outline: 0 none;\\n  margin-bottom: 20px;\\n  text-transform: uppercase;\\n}\\n\\n.address-bg[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 16px;\\n  border-bottom: 1px solid #E4E7E9;\\n  padding: 16px 8px 16px 16px;\\n  margin-bottom: 10px;\\n}\\n\\n.mobile-addressbar[_ngcontent-%COMP%] {\\n  padding: 13px;\\n  padding-top: 0px;\\n}\\n\\n.address-item-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.header-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #2D2D2D;\\n}\\n\\n.address-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.address-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n\\n.default-btn[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  background: #DCE6FD;\\n  color: #022C61;\\n  border: none;\\n  font-weight: 400;\\n  font-size: 10px;\\n  font-family: var(--medium-font) !important;\\n}\\n\\n.icons-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 16px;\\n  margin-left: auto; \\n\\n}\\n.icons-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%], .delete-icon[_ngcontent-%COMP%] {\\n  margin-left: 10px; \\n\\n}\\n\\n.right-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.right-section[_ngcontent-%COMP%]   .default[_ngcontent-%COMP%] {\\n  margin-right: 10px; \\n\\n}\\n\\n.add-new-address-btn[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: var(--header_bgcolor);\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  border: 1px solid var(--header_bgcolor);\\n  font-family: var(--medium-font);\\n  outline: 0 none;\\n  margin-bottom: 20px;\\n}\\n\\n.add-new-address-btn[_ngcontent-%COMP%]:active {\\n  border: 1px solid var(--header_bgcolor) !important;\\n  background-color: #ffffff !important;\\n}\\n\\n.other-btn[_ngcontent-%COMP%] {\\n  color: #856600;\\n  font-weight: 400;\\n  font-size: 10px;\\n  border-radius: 4px;\\n  background: #FFE992;\\n  border: none;\\n  font-family: var(--medium-font) !important;\\n  width: 50px;\\n}\"]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}