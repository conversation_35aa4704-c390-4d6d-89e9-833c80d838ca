{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MerchantLivestreamRoutingModule } from './merchant-livestream-routing.module';\nimport { MerchantLivestreamComponent } from './merchant-livestream.component';\nimport { MerchantLivestreamDetailsComponent } from './merchant-livestream-details/merchant-livestream-details.component';\nimport { LiveStreamService } from '@core/services/livestream.service';\nimport { DragScrollModule } from 'ngx-drag-scroll';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { FormsModule } from '@angular/forms';\nimport { SharedModule } from '@shared/modules/shared.module';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport * as i0 from \"@angular/core\";\nexport class MerchantLivestreamModule {\n  static #_ = this.ɵfac = function MerchantLivestreamModule_Factory(t) {\n    return new (t || MerchantLivestreamModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MerchantLivestreamModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [LiveStreamService],\n    imports: [CommonModule, SharedModule, MerchantLivestreamRoutingModule, FormsModule, DragScrollModule, MatIconModule, MatFormFieldModule, MatInputModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MerchantLivestreamModule, {\n    declarations: [MerchantLivestreamComponent, MerchantLivestreamDetailsComponent],\n    imports: [CommonModule, SharedModule, MerchantLivestreamRoutingModule, FormsModule, DragScrollModule, MatIconModule, MatFormFieldModule, MatInputModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MerchantLivestreamRoutingModule", "MerchantLivestreamComponent", "MerchantLivestreamDetailsComponent", "LiveStreamService", "DragScrollModule", "MatFormFieldModule", "FormsModule", "SharedModule", "MatIconModule", "MatInputModule", "MerchantLivestreamModule", "_", "_2", "_3", "imports", "declarations"], "sources": ["C:\\projects\\MTN\\FrontEnd\\Frontend\\Storebuilder-UI-main\\src\\app\\pages\\merchant-livestream\\merchant-livestream.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { MerchantLivestreamRoutingModule } from './merchant-livestream-routing.module';\r\nimport { MerchantLivestreamComponent } from './merchant-livestream.component';\r\nimport { MerchantLivestreamDetailsComponent } from './merchant-livestream-details/merchant-livestream-details.component';\r\nimport { LiveStreamService } from '@core/services/livestream.service';\r\nimport { DragScrollModule } from 'ngx-drag-scroll';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { SharedModule } from '@shared/modules/shared.module';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule } from '@angular/material/input';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    MerchantLivestreamComponent,\r\n    MerchantLivestreamDetailsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    MerchantLivestreamRoutingModule,\r\n    FormsModule,\r\n    DragScrollModule,\r\n    MatIconModule,\r\n    MatFormFieldModule,\r\n    MatInputModule \r\n  ],\r\n  providers:[LiveStreamService]\r\n})\r\nexport class MerchantLivestreamModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,+BAA+B,QAAQ,sCAAsC;AACtF,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,kCAAkC,QAAQ,qEAAqE;AACxH,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;;AAoBxD,OAAM,MAAOC,wBAAwB;EAAA,QAAAC,CAAA,G;qBAAxBD,wBAAwB;EAAA;EAAA,QAAAE,EAAA,G;UAAxBF;EAAwB;EAAA,QAAAG,EAAA,G;eAFzB,CAACV,iBAAiB,CAAC;IAAAW,OAAA,GAT3Bf,YAAY,EACZQ,YAAY,EACZP,+BAA+B,EAC/BM,WAAW,EACXF,gBAAgB,EAChBI,aAAa,EACbH,kBAAkB,EAClBI,cAAc;EAAA;;;2EAILC,wBAAwB;IAAAK,YAAA,GAfjCd,2BAA2B,EAC3BC,kCAAkC;IAAAY,OAAA,GAGlCf,YAAY,EACZQ,YAAY,EACZP,+BAA+B,EAC/BM,WAAW,EACXF,gBAAgB,EAChBI,aAAa,EACbH,kBAAkB,EAClBI,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}